package cn.sphd.miners.modules.sales.service.impl;


import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.dao.FinanceInvoiceDetailDao;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceDetail;
import cn.sphd.miners.modules.finance.service.InvoiceService;
import cn.sphd.miners.modules.sales.dao.*;
import cn.sphd.miners.modules.sales.entity.*;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.sales.service.SlInvoiceService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.hibernate.criterion.CriteriaSpecification;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@org.springframework.transaction.annotation.Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class SlInvoiceServiceImpl implements SlInvoiceService {
    @Autowired
    SlInvoiceApplicationDao slInvoiceApplicationDao;
    @Autowired
    SlInvoiceCheckDao slInvoiceCheckDao;
    @Autowired
    SlInvoiceCheckItemDao slInvoiceCheckItemDao;
    @Autowired
    SlInvoiceApplicationItemDao slInvoiceApplicationItemDao;
    @Autowired
    SlInvoiceDeliveryDao slInvoiceDeliveryDao;
    @Autowired
    SlInvoiceDeliveryItemDao slInvoiceDeliveryItemDao;
    @Autowired
    SlInvoiceDeliveryHistoryDao slInvoiceDeliveryHistoryDao;
    @Autowired
    SlInvoiceDeliveryItemHistoryDao slInvoiceDeliveryItemHistoryDao;
    @Autowired
    PdCustomerService pdCustomerService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    FinanceInvoiceDetailDao financeInvoiceDetailDao;
    @Autowired
    UserService userService;
    @Autowired
    UserSuspendMsgService suspendMsgService;
    @Autowired
    InvoiceService financeInvoiceService;
    @Autowired
    SWMessageService swMessageService;

    @Autowired
    UserPopedomService popedomService;

    @Override
    public Map<String, Object> getInvoiceDeliveryList(Integer oid, Integer userId, String state, Integer currPage, Integer pageSize) {
        String sql = "select a.id, a.applicant, a.applicant_name as applicantName, a.creator, a.create_name as createName, a.create_date as createDate, a.update_date as applyDate, c.full_name as customerName, c.`code` as customerCode, o.sn, case when a.division_way = 2 then ( select COUNT(distinct iad.invoice) from t_sl_invoice_application_item i left join t_sl_invoice_delivery_item di on di.invoice = i.invoice left join t_sl_invoice_application_detail iad on iad.application_item = i.id where i.application = a.id and di.id is null) else ( select COUNT(distinct i.invoice) from t_sl_invoice_application_item i left join t_sl_invoice_delivery_item di on di.invoice = i.invoice where i.application = a.id and di.id is null) end as invoiceCount, IFNULL((select SUM(i.amount) from t_sl_invoice_application_item i left join t_sl_invoice_delivery_item di on di.invoice = i.invoice where i.application = a.id and di.id is null), 0) amount, a.state from t_sl_invoice_application as a left join t_sl_orders as o on o.id = a.orders left join t_sl_customer as c on c.id = o.customer " +
                "where a.org=" + oid + " and a.state='" + state + "' and (a.teminate_state is null or a.teminate_state=0)";

        if (userId != null)
            sql += " and o.creator = " + userId;

        System.out.println(sql);
        return slInvoiceApplicationDao.findMapByConditionByPage(sql, "", new Integer[]{}, currPage, pageSize);
    }

    @Override
    public List<Map<String, Object>> checkInvoiceList(Integer oid, Integer id) {
        String sql = "select distinct i.id as applicationItemId,ia.division_way, i.invoice_category invoiceCategory, i.old_invoice oldInvoice, d.invoice_no as invoiceNo, d.losted, case when ia.division_way = 2 then sum(iad.amount * oii.item_price + iad.amount * oii.item_price * oii.invoice_rate / 100) else i.amount end as amount, ifnull(i.invoice, iad.invoice) as invoice, ic.result_type as resultType, ic.check_result as checkResult, di.id as deliveryId from t_sl_invoice_application_item as i left join t_sl_orders_item_invoice oii on oii.application_item = i.id left join t_sl_invoice_application ia on i.application = ia.id left join t_sl_invoice_application_detail iad on iad.application_item = i.id and iad.old_invoice = oii.id left join t_finance_invoice_detail as d on i.invoice = d.id or d.id = iad.invoice left join t_sl_invoice_check as ic on ic.invoice = i.invoice or ic.invoice = iad.invoice left join t_sl_invoice_delivery_item as di on i.invoice = di.invoice or di.invoice = iad.invoice where i.application='" + id + "' and i.org='" + oid + "'";
        SlInvoiceApplication application = slInvoiceApplicationDao.get(id);
        if ("2".equals(application.getDivisionWay())) {
            sql += " group by iad.invoice";
        }
        return slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> getInvoiceRegistrationList(Integer oid, Integer id) {
        String sql = "select distinct i.id as applicationItemId, i.invoice_category invoiceCategory, i.old_invoice oldInvoice, d.invoice_no as invoiceNo, case when ia.division_way =2 then sum(iad.amount * oii.item_price+iad.amount * oii.item_price*oii.invoice_rate/100) else i.amount end as amount, ifnull(i.invoice, iad.invoice) as invoice, ic.result_type as resultType, ic.check_result as checkResult from t_sl_invoice_application_item as i left join t_sl_orders_item_invoice oii on oii.application_item = i.id left join t_sl_invoice_application ia on i.application = ia.id left join t_sl_invoice_application_detail iad on iad.application_item = i.id and iad.old_invoice = oii.id left join t_finance_invoice_detail as d on i.invoice = d.id or d.id = iad.invoice left join t_sl_invoice_check as ic on ic.invoice = i.invoice or ic.invoice = iad.invoice left join t_sl_invoice_delivery_item as di on i.invoice = di.invoice or di.invoice = iad.invoice where i.application='" + id + "' and i.org='" + oid + "' and di.id is null";
        SlInvoiceApplication application = slInvoiceApplicationDao.get(id);
        if ("2".equals(application.getDivisionWay())) {
            sql += " group by iad.invoice";
        }
        return slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> orderInvoiceListByAuditNo(Integer oid, Integer id) {
        String sql = "SELECT si.result_type as resultType,si.result_reasion as resultReasion \n" +
                "from t_sl_orders_item_invoice as o\n" +
                "LEFT JOIN t_sl_invoice_check as s on o.id=s.invoice\n" + "LEFT JOIN t_sl_invoice_check_item  as si on s.id=si.invoice_check\n" +
                "where o.application_item='" + id + "'  " +
                "and o.check_result='0'\n" +
                "order by o.finance_time desc";

        return slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> orderInvoiceList(Integer applicationItemId, Integer fid) {
        SlInvoiceApplicationItem item = slInvoiceApplicationItemDao.get(applicationItemId);
        SlInvoiceApplication application = null;
        if (item != null) {
            application = slInvoiceApplicationDao.get(item.getApplication());
        }

        String sql = "select distinct i.id orderInvoiceId, i.item_name itemName, case when ia.division_way = 2 then iad.amount else i.item_quantity end as itemQuantity, i.item_price itemPrice, i.invoice_rate invoiceRate, case when ia.division_way = 2 then fid.amount else i.invoice_amount end as invoiceAmount, i.check_result checkResult,ia.id from t_sl_orders_item_invoice as i left join t_sl_invoice_application_item iai on i.application_item = iai.id left join t_sl_invoice_application_detail iad on iad.application_item = iai.id  and iad.old_invoice = i.id left join t_sl_invoice_application ia on iad.application = ia.id left join t_finance_invoice_detail fid on iad.invoice = fid.id";

        if (application != null && "1".equals(application.getDivisionWay()))
            sql += " or i.invoice = fid.id";

        sql += " where i.application_item=" + applicationItemId;
        if (fid != null)
            sql += " and fid.id=" + fid;
        if (application != null && "2".equals(application.getDivisionWay()))
            sql += " group by iad.id";


        return slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public Map<String, Object> getInvoiceItemByApplicationItem(Integer applicationItemId, Integer fid) {
        String sql = "select distinct ifnull(i.invoice, iad.invoice) as invoice, i.id as applicationItemId, d.invoice_no as invoiceNo, case when ia.division_way =2 then d.amount else i.amount end as amount , d.type, d.losted, o.customer_name as customerName from t_sl_invoice_application_item as i left join t_sl_invoice_application ia on i.application = ia.id left join t_sl_orders_item_invoice oii on oii.application_item = i.id left join t_sl_invoice_application_detail iad on iad.application_item = i.id left join t_finance_invoice_detail as d on i.invoice = d.id or iad.invoice = d.id left join t_sl_invoice_application as a on a.id = i.application left join t_sl_orders as o on o.id = a.orders where i.id = " + applicationItemId;
        if (fid != null)
            sql += " and d.id = " + fid;
        return (Map<String, Object>) slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).uniqueResult();
    }

    @Override
    public SlInvoiceCheck getSlInvoiceCheck(Integer invoice) {
        String sql = "from SlInvoiceCheck where invoice=" + invoice;
        return slInvoiceCheckDao.getByHQL(sql);
    }

    @Override
    public SlInvoiceApplicationItem getApplicationItemById(Integer oid, Integer applicationItemId) {
        return slInvoiceApplicationItemDao.getByHQL("from SlInvoiceApplicationItem where id=" + applicationItemId + " and org=" + oid);
    }

    @Override
    public Map<String, Object> invoiceLoss(Integer oid, Integer applicationItemId, User user, Integer fid) {
        Map map = new HashMap();
        if (applicationItemId == null) {
            map.put("status", 0);
            return map;
        }
        SlInvoiceApplicationItem item = this.getApplicationItemById(oid, applicationItemId);


        if (item == null) {
            map.put("status", 0);
            return map;
        } else {
            FinanceInvoiceDetail invoiceDetail = financeInvoiceService.getFinanceInvoiceDetailById(item.getInvoice() == null ? fid : item.getInvoice());
            invoiceDetail.setLosted("1");
            SlInvoiceCheck check = this.getSlInvoiceCheck(item.getInvoice());
            if (check != null) {
                slInvoiceCheckDao.delete(check);
            }
            financeInvoiceService.updateInvoiceDetail(invoiceDetail, "2", map, user);
        }

        return map;
    }

    @Override
    public void addOrUpdateSlInvoiceCheck(SlInvoiceCheck slInvoiceCheck, String resultReasion, User user) {
        if ("0".equals(slInvoiceCheck.getCheckResult())) {
            Integer type = slInvoiceCheck.getResultType() == null ? 0 : slInvoiceCheck.getResultType();
            if (slInvoiceCheck.getId() != null) {
                List<SlInvoiceCheckItem> l = slInvoiceCheckItemDao.getListByHQL("from SlInvoiceCheckItem where invoiceCheck=" + slInvoiceCheck.getId());
                slInvoiceCheckItemDao.deleteAll(l);
            }

            slInvoiceCheckDao.saveOrUpdate(slInvoiceCheck);

            if ("1".equals(type.toString()) || "2".equals(type.toString()) || "4".equals(type.toString())) {
                SlInvoiceCheckItem slInvoiceCheckItem = new SlInvoiceCheckItem();
                slInvoiceCheckItem.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem.setResultType(type);
                slInvoiceCheckItem.setResultReasion(resultReasion);
                slInvoiceCheckItem.setCreator(user.getUserID());
                slInvoiceCheckItem.setCreateName(user.getUserName());
                slInvoiceCheckItem.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem);
            }

            if ("3".equals(type.toString())) {
                SlInvoiceCheckItem slInvoiceCheckItem1 = new SlInvoiceCheckItem();
                slInvoiceCheckItem1.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem1.setResultType(1);
                slInvoiceCheckItem1.setResultReasion(resultReasion);
                slInvoiceCheckItem1.setCreator(user.getUserID());
                slInvoiceCheckItem1.setCreateName(user.getUserName());
                slInvoiceCheckItem1.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem1);

                SlInvoiceCheckItem slInvoiceCheckItem2 = new SlInvoiceCheckItem();
                slInvoiceCheckItem2.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem2.setResultType(2);
                slInvoiceCheckItem2.setResultReasion(resultReasion);
                slInvoiceCheckItem2.setCreator(user.getUserID());
                slInvoiceCheckItem2.setCreateName(user.getUserName());
                slInvoiceCheckItem2.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem2);
            }
            if ("5".equals(type.toString())) {
                SlInvoiceCheckItem slInvoiceCheckItem1 = new SlInvoiceCheckItem();
                slInvoiceCheckItem1.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem1.setResultType(1);
                slInvoiceCheckItem1.setResultReasion(resultReasion);
                slInvoiceCheckItem1.setCreator(user.getUserID());
                slInvoiceCheckItem1.setCreateName(user.getUserName());
                slInvoiceCheckItem1.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem1);

                SlInvoiceCheckItem slInvoiceCheckItem4 = new SlInvoiceCheckItem();
                slInvoiceCheckItem4.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem4.setResultType(4);
                slInvoiceCheckItem4.setResultReasion(resultReasion);
                slInvoiceCheckItem4.setCreator(user.getUserID());
                slInvoiceCheckItem4.setCreateName(user.getUserName());
                slInvoiceCheckItem4.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem4);
            }
            if ("6".equals(type.toString())) {
                SlInvoiceCheckItem slInvoiceCheckItem2 = new SlInvoiceCheckItem();
                slInvoiceCheckItem2.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem2.setResultType(2);
                slInvoiceCheckItem2.setResultReasion(resultReasion);
                slInvoiceCheckItem2.setCreator(user.getUserID());
                slInvoiceCheckItem2.setCreateName(user.getUserName());
                slInvoiceCheckItem2.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem2);

                SlInvoiceCheckItem slInvoiceCheckItem4 = new SlInvoiceCheckItem();
                slInvoiceCheckItem4.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem4.setResultType(4);
                slInvoiceCheckItem4.setResultReasion(resultReasion);
                slInvoiceCheckItem4.setCreator(user.getUserID());
                slInvoiceCheckItem4.setCreateName(user.getUserName());
                slInvoiceCheckItem4.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem4);
            }
            if ("7".equals(type.toString())) {

                SlInvoiceCheckItem slInvoiceCheckItem1 = new SlInvoiceCheckItem();
                slInvoiceCheckItem1.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem1.setResultType(1);
                slInvoiceCheckItem1.setResultReasion(resultReasion);
                slInvoiceCheckItem1.setCreator(user.getUserID());
                slInvoiceCheckItem1.setCreateName(user.getUserName());
                slInvoiceCheckItem1.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem1);

                SlInvoiceCheckItem slInvoiceCheckItem2 = new SlInvoiceCheckItem();
                slInvoiceCheckItem2.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem2.setResultType(2);
                slInvoiceCheckItem2.setResultReasion(resultReasion);
                slInvoiceCheckItem2.setCreator(user.getUserID());
                slInvoiceCheckItem2.setCreateName(user.getUserName());
                slInvoiceCheckItem2.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem2);

                SlInvoiceCheckItem slInvoiceCheckItem4 = new SlInvoiceCheckItem();
                slInvoiceCheckItem4.setInvoiceCheck(slInvoiceCheck.getId());
                slInvoiceCheckItem4.setResultType(4);
                slInvoiceCheckItem4.setResultReasion(resultReasion);
                slInvoiceCheckItem4.setCreator(user.getUserID());
                slInvoiceCheckItem4.setCreateName(user.getUserName());
                slInvoiceCheckItem4.setCreateDate(new Date());
                slInvoiceCheckItemDao.save(slInvoiceCheckItem4);
            }


        } else {
            slInvoiceCheckDao.saveOrUpdate(slInvoiceCheck);
        }

    }

    @Override
    @Transactional
    public String registration(HttpServletRequest request, User user, Integer oid) {

        //申请单id
        Integer id = Integer.valueOf(request.getParameter("id"));
        SlInvoiceApplication invoiceApplication = slInvoiceApplicationDao.get(id);


        if (invoiceApplication == null) {
            return "申请单id，传入错误";
        }

        //发票号
        String invoices = request.getParameter("invoices");
        //发票接收人
        String receiver = request.getParameter("receiver");
        //发票接收人电话
        String receiverPhone = request.getParameter("receiverPhone");
        //发运方式
        String deliveryWay = request.getParameter("deliveryWay");
        //快递发运方式
        String deliverySubWay = request.getParameter("deliverySubWay");
        //快递公司
        String deliveryCompany = request.getParameter("deliveryCompany");
        //单号
        String deliverySn = request.getParameter("deliverySn");
        //邮寄日期
        String deliveryTime = request.getParameter("deliveryTime");
        //备注
        String memo = request.getParameter("memo");
        //经办人
        String operator = request.getParameter("operator");
        //经办人联系电话
        String operatorPhone = request.getParameter("operatorPhone");
        SlInvoiceDelivery delivery = new SlInvoiceDelivery();
        delivery.setOrg(oid);
        delivery.setReceiver(receiver);//接收人
        delivery.setDeliveryWay(deliveryWay);
        delivery.setDeliverySubWay(deliverySubWay);
        delivery.setDeliveryCompany(deliveryCompany);
        delivery.setDeliverySn(deliverySn);
        delivery.setOperator(operator);
        delivery.setOperatorPhone(operatorPhone);
        delivery.setApplication(id);
        delivery.setDeliveryOperation("1");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        try {
            if (deliveryTime != null) ;
            delivery.setDeliveryTime(dateFormat.parse(deliveryTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        delivery.setMemo(memo);
        delivery.setVersionNo(0);
        delivery.setCreator(user.getUserID());
        delivery.setCreateName(user.getUserName());
        delivery.setCreateDate(new Date());
        delivery.setReceiverPhone(receiverPhone);


        JSONObject json = null;
        JSONArray array = JSON.parseArray(invoices);
        int flag = 0;
        List<SlInvoiceDeliveryItem> items = new ArrayList<>();
        for (Object obj : array) {
            json = (JSONObject) obj;
            Integer in = Integer.valueOf(json.getString("invoice"));
            boolean selected = json.getBooleanValue("selected");
            if (selected) {

                SlInvoiceDeliveryItem item = slInvoiceDeliveryItemDao.getByHQL("from SlInvoiceDeliveryItem where invoice=" + in);
                if (item != null) {
                    return "该发票已经被登记，重新刷新界面";
                }
                flag++;
                item = new SlInvoiceDeliveryItem();
                //item.setDelivery(delivery.getId());
                item.setApplication(id);
                item.setInvoice(in);
                item.setOperation("1");
                item.setCreator(user.getUserID());
                item.setCreateName(user.getUserName());
                item.setCreateDate(new Date());
                //   slInvoiceDeliveryItemDao.save(item);
                items.add(item);
            }

        }
        User manager = userService.getUserByRoleCode(user.getOid(), "super");
        User account = userService.getUserByRoleCode(user.getOid(), "finance");
        List<UserPopedom> accounts = popedomService.getAllUserPopedomByMid(oid, "lp");

        User creator = userService.getUserByID(invoiceApplication.getCreator());
        if (flag > 0) {
            slInvoiceDeliveryDao.save(delivery);
            for (SlInvoiceDeliveryItem item : items) {
                item.setDelivery(delivery.getId());
                FinanceInvoiceDetail invoiceDetail = financeInvoiceDetailDao.get(item.getInvoice());
                HashMap msg = new HashMap();
                msg.put("message", "号码为" + invoiceDetail.getInvoiceNo() + "发票已发出登记");
                msg.put("applicationId", item.getApplication());
                msg.put("time", new Date());
                msg.put("user", user.getUserName());
                msg.put("application", invoiceApplication);

                clusterMessageSendingOperations.convertAndSendToUser(creator.getUserID() + "", "/saleRegistration", null, null, null, null, JSON.toJSONString(msg));//推送
                clusterMessageSendingOperations.convertAndSendToUser(manager.getUserID() + "", "/saleRegistration", null, null, null, null, JSON.toJSONString(msg));//推送
                clusterMessageSendingOperations.convertAndSendToUser(account.getUserID() + "", "/saleRegistration", null, null, null, null, JSON.toJSONString(msg));//推送
                slInvoiceDeliveryItemDao.save(item);


                for (UserPopedom userPopedom : accounts) {
                    swMessageService.rejectSend(0, -1, msg, userPopedom.getUserId() + "", "invoiceRegister", null, null, userPopedom.getUser(), "invoiceRegister");
                }


            }
            //角标变化-zy
            swMessageService.rejectSend(-1, -1, new HashMap<>(), creator.getUserID() + "" + "", "invoiceRegister", null, null, creator, "invoiceApply");
            swMessageService.rejectSend(0, -1, new HashMap<>(), creator.getUserID() + "" + "", "invoiceRegister", null, null, creator, "invoiceRegister");
            swMessageService.rejectSend(-1, -1, new HashMap<>(), manager.getUserID() + "" + "", "invoiceRegister", null, null, manager, "invoiceApproval");
            swMessageService.rejectSend(0, -1, new HashMap<>(), manager.getUserID() + "", "invoiceRegister", null, null, manager, "invoiceRegister");

            //添加后刷新
            slInvoiceDeliveryDao.getSession().flush();

            List<Map<String, Object>> list = getInvoiceRegistrationList(oid, id);
            if (list.size() == 0) {
                SlInvoiceApplication sa = slInvoiceApplicationDao.get(id);
                sa.setState("7");
                slInvoiceApplicationDao.update(sa);
            }
        } else {
            return "请选择发票";
        }


        return "1";
    }

    @Override
    public String registrationUpdate(HttpServletRequest request, User user, Integer oid) {
        //申请单id
        Integer id = request.getParameter("id") == null ? 0 : Integer.valueOf(request.getParameter("id"));
        //发运信息id
        Integer deliveryId = Integer.valueOf(request.getParameter("deliveryId"));
        //发票号
        String invoices = request.getParameter("invoices");
        //发票接收人
        String receiver = request.getParameter("receiver");
        //发票接收人电话
        String receiverPhone = request.getParameter("receiverPhone");
        //发运方式
        String deliveryWay = request.getParameter("deliveryWay");
        //快递发运方式
        String deliverySubWay = request.getParameter("deliverySubWay");
        //快递公司
        String deliveryCompany = request.getParameter("deliveryCompany");
        //单号
        String deliverySn = request.getParameter("deliverySn");
        //邮寄日期
        String deliveryTime = request.getParameter("deliveryTime");
        //备注
        String memo = request.getParameter("memo");
        //货运修改操作方式
        String deliveryOperation = request.getParameter("deliveryOperation");
        //经办人
        String operator = request.getParameter("operator");
        //经办人联系电话
        String operatorPhone = request.getParameter("operatorPhone");
        SlInvoiceDelivery delivery = slInvoiceDeliveryDao.get(deliveryId);
        if (delivery == null) {
            return "发运信息传入错误";
        }
        JSONArray array = JSON.parseArray(invoices);
        if (array == null || array.size() == 0) {
            return "请选择发票";
        }

        if (deliveryTime == null) {
            return "请填写邮寄日期";
        }
        //添加发运信息修改记录
        SlInvoiceDeliveryHistory history = new SlInvoiceDeliveryHistory();
        history.setInvoiceDelivery(delivery.getId());
        history.setOrg(delivery.getOrg());
        history.setApplication(delivery.getApplication());
        history.setApplicationNo(delivery.getApplicationNo());
        history.setReceiver(delivery.getReceiver());
        history.setReceiverPhone(delivery.getReceiverPhone());
        history.setDeliveryWay(delivery.getDeliveryWay());
        history.setDeliverySubWay(delivery.getDeliverySubWay());
        history.setDeliveryTime(delivery.getDeliveryTime());
        history.setDeliverySn(delivery.getDeliverySn());
        history.setDeliveryCompany(delivery.getDeliveryCompany());
        history.setOperator(delivery.getOperator());
        history.setOperatorPhone(delivery.getOperatorPhone());
        history.setLicenseTag(delivery.getLicenseTag());
        history.setMemo(delivery.getMemo());
        history.setSginer(delivery.getSginer());
        history.setSignRecord(delivery.getSignRecord());
        history.setSignTime(delivery.getSignTime());
        history.setSignResult(delivery.getSignResult());
        history.setOperator(delivery.getOperator());
        history.setOperatorPhone(delivery.getOperatorPhone());
        history.setDeliveryOperation(delivery.getDeliveryOperation());
        history.setSignOperation(delivery.getSignOperation());
        history.setPreviousId(delivery.getPreviousId());
        history.setVersionNo(delivery.getVersionNo());
        history.setCreator(user.getUserID());
        history.setCreateName(user.getUserName());
        history.setCreateDate(new Date());
        slInvoiceDeliveryHistoryDao.save(history);


        //修改发运信息
        delivery.setReceiver(receiver);//接收人
        delivery.setReceiverPhone(receiverPhone);//接收人号码
        delivery.setDeliveryWay(deliveryWay);
        delivery.setDeliverySubWay(deliverySubWay);
        delivery.setDeliveryCompany(deliveryCompany);
        delivery.setDeliverySn(deliverySn);
        delivery.setDeliveryOperation("3");
        delivery.setPreviousId(history.getId());
        delivery.setOperator(operator);
        delivery.setOperatorPhone(operatorPhone);
        delivery.setVersionNo(delivery.getVersionNo() + 1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        try {
            delivery.setDeliveryTime(dateFormat.parse(deliveryTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        delivery.setMemo(memo);
        delivery.setUpdator(user.getUserID());
        delivery.setUpdateName(user.getUserName());
        delivery.setUpdateDate(new Date());
        slInvoiceDeliveryDao.update(delivery);


        List<SlInvoiceDeliveryItem> list = slInvoiceDeliveryItemDao.getListByDeliveryId(delivery.getId());


        for (SlInvoiceDeliveryItem i : list) {
            SlInvoiceDeliveryItemHistory itemHistory = new SlInvoiceDeliveryItemHistory();
            itemHistory.setInvoiceDelivery(i.getId());
            itemHistory.setDelivery(history.getId());
            itemHistory.setInvoice(i.getInvoice());
            itemHistory.setApplication(i.getApplication());
            itemHistory.setMemo(i.getMemo());
            itemHistory.setCreator(user.getUserID());
            itemHistory.setOperation(i.getOperation());
            itemHistory.setCreateName(user.getUserName());
            itemHistory.setCreateDate(new Date());

            int res = 0;

            JSONObject json = null;

            for (Object obj : array) {
                json = (JSONObject) obj;
                boolean selected = json.getBooleanValue("selected");
                String s = json.getString("invoice");
                if (selected == true) {
                    if (i.getId().toString().equals(s)) {
                        res++;
                    }
                }

            }
            //说明这张发票没勾选，删除
            if (res == 0) {
                slInvoiceDeliveryItemDao.delete(i);
            }

            slInvoiceDeliveryItemHistoryDao.save(itemHistory);
        }

        JSONObject json = null;
        for (Object obj : array) {
            json = (JSONObject) obj;
            if (obj != null) {
                boolean selected = json.getBooleanValue("selected");
                String s = json.getString("invoice");
                if (selected) {
                    SlInvoiceDeliveryItem item = slInvoiceDeliveryItemDao.get(Integer.valueOf(s));
                    if (item == null) {
                        item = new SlInvoiceDeliveryItem();
                        item.setDelivery(delivery.getId());
                        item.setApplication(id);
                        item.setInvoice(Integer.valueOf(String.valueOf(s)));
                        item.setCreator(user.getUserID());
                        item.setCreateName(user.getUserName());
                        item.setOperation("1");
                        item.setCreateDate(new Date());
                        slInvoiceDeliveryItemDao.save(item);
                    }
                }

            }


        }


        return "1";
    }

    @Override
    public List<Map<String, Object>> registrationRecordList(Integer id, Integer oid) {
        String sql = "select id recordId,invoice_delivery as deliveryId , create_name as updateName,create_date as updateDate,previous_id as previousId from t_sl_invoice_delivery_history where invoice_delivery=" + id + "  and org=" + oid + " and (sign_result = ''||sign_result is null) order by create_date desc";
        return slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public Map<String, Object> registrationRecord(Integer recordId, String isNew, Integer fid) {
        Map map = new HashMap();
        String customerName = null;
        Map before = new HashMap();
        Map after = new HashMap();
        //说明是最新的修改记录
        if ("1".equals(isNew)) {
            //修改前
            SlInvoiceDeliveryHistory history = slInvoiceDeliveryHistoryDao.get(recordId);

            if (history == null) {
                map.put("status", 0);
                map.put("msg", "参数传递错误");
                return map;
            }

            Map<String, Object> customer = getCustomerNameByApplicationId(history.getApplication(), history.getOrg());
            if (customer != null) {
                customerName = String.valueOf(customer.get("customerName"));
            }

            SlInvoiceDelivery delivery = slInvoiceDeliveryDao.get(history.getInvoiceDelivery());
            if (delivery == null) {
                if (history == null) {
                    map.put("status", 0);
                    map.put("msg", "数据有误");
                    return map;
                }
            }
            before.put("receiver", history.getReceiver());
            before.put("deliverySubWay", history.getDeliverySubWay());
            before.put("deliveryWay", history.getDeliveryWay());
            before.put("deliveryCompany", history.getDeliveryCompany());
            before.put("deliverySn", history.getDeliverySn());
            before.put("deliveryTime", history.getDeliveryTime());
            before.put("memo", history.getMemo());
            before.put("customerName", customerName);
            before.put("operator", history.getOperator());
            before.put("operatorPhone", history.getOperatorPhone());
            List<Map<String, Object>> itemHistoryList = slInvoiceDeliveryItemHistoryDao.getListByDeliveryId(history.getId(), fid);
            before.put("items", itemHistoryList);


            after.put("receiver", delivery.getReceiver());
            after.put("deliverySubWay", delivery.getDeliverySubWay());
            after.put("deliveryWay", delivery.getDeliveryWay());
            after.put("deliveryCompany", delivery.getDeliveryCompany());
            after.put("deliverySn", delivery.getDeliverySn());
            after.put("deliveryTime", delivery.getDeliveryTime());
            after.put("memo", delivery.getMemo());
            after.put("operator", delivery.getOperator());
            after.put("operatorPhone", delivery.getOperatorPhone());
            after.put("customerName", customerName);
            List<Map<String, Object>> itemList = slInvoiceDeliveryItemDao.getListByDeliveryIdForMap2(delivery.getId());
            after.put("items", itemList);
        } else {
            //修改前
            SlInvoiceDeliveryHistory history = slInvoiceDeliveryHistoryDao.get(recordId);
            Map<String, Object> customer = getCustomerNameByApplicationId(history.getApplication(), history.getOrg());
            if (customer != null) {
                customerName = String.valueOf(customer.get("customerName"));
            }


            before.put("receiver", history.getReceiver());
            before.put("deliverySubWay", history.getDeliverySubWay());
            before.put("deliveryWay", history.getDeliveryWay());
            before.put("deliveryCompany", history.getDeliveryCompany());
            before.put("deliverySn", history.getDeliverySn());
            before.put("deliveryTime", history.getDeliveryTime());
            before.put("memo", history.getMemo());
            before.put("customerName", customerName);
            before.put("operator", history.getOperator());
            before.put("operatorPhone", history.getOperatorPhone());
            List<Map<String, Object>> beforeHistoryList = slInvoiceDeliveryItemHistoryDao.getListByDeliveryId(history.getId(), fid);
            before.put("items", beforeHistoryList);

            //修改后
            SlInvoiceDeliveryHistory afterHistory = slInvoiceDeliveryHistoryDao.getByPreviousId(history.getId());
            if (afterHistory == null) {
                after.put("receiver", null);
                after.put("deliverySubWay", null);
                after.put("deliveryWay", null);
                after.put("deliveryCompany", null);
                after.put("deliverySn", null);
                after.put("deliveryTime", null);
                after.put("memo", null);
                after.put("customerName", customerName);
                after.put("operator", null);
                after.put("operatorPhone", null);
                after.put("items", new ArrayList<>());

            } else {
                after.put("receiver", afterHistory.getReceiver());
                after.put("deliverySubWay", afterHistory.getDeliverySubWay());
                after.put("deliveryCompany", afterHistory.getDeliveryCompany());
                after.put("deliveryWay", afterHistory.getDeliveryWay());
                after.put("deliverySn", afterHistory.getDeliverySn());
                after.put("deliveryTime", afterHistory.getDeliveryTime());
                after.put("memo", afterHistory.getMemo());
                after.put("customerName", customerName);
                after.put("operator", afterHistory.getOperator());
                after.put("operatorPhone", afterHistory.getOperatorPhone());
                List<Map<String, Object>> afterHistoryList = slInvoiceDeliveryItemHistoryDao.getListByDeliveryId(afterHistory.getId(), fid);
                after.put("items", afterHistoryList);

            }
        }
        map.put("status", 1);
        map.put("before", before);
        map.put("after", after);

        return map;
    }

    @Override
    public Map<String, Object> registrationLook(Integer id, Integer oid) {
        Map map = new HashMap();
        Map data = new HashMap();
        SlInvoiceDelivery delivery = slInvoiceDeliveryDao.get(id);
        if (delivery == null) {
            map.put("status", 0);
            return map;
        }

        String sql = "SELECT\n" +
                "i.id as applicationItemId,\n" +
                "i.invoice_category invoiceCategory,\n" +
                "i.old_invoice oldInvoice,\n" +
                "d.invoice_no as invoiceNo,\n" +
                "CASE WHEN ia.division_way='2' THEN d.amount ELSE i.amount END as amount,\n" +
                "CASE WHEN ia.division_way='2' THEN iad.invoice ELSE i.invoice END as invoice,\n" +
                "ic.result_type as resultType,\n" +
                "ic.check_result as checkResult\n" +
                "FROM\n" +
                "t_sl_invoice_application_item as i\n" +
                "LEFT JOIN t_sl_invoice_application ia on i.application = ia.id\n" +
                "LEFT JOIN t_sl_invoice_application_detail iad on iad.application_item = i.id\n" +
                "LEFT JOIN t_finance_invoice_detail as d on \n" +
                "  (CASE WHEN ia.division_way='2' THEN iad.invoice ELSE i.invoice END = d.id)\n" +
                "LEFT JOIN t_sl_invoice_check as ic on \n" +
                "  (CASE WHEN ia.division_way='2' THEN iad.invoice ELSE i.invoice END = ic.invoice)\n" +
                "LEFT JOIN t_sl_invoice_delivery_item as di on \n" +
                "  (CASE WHEN ia.division_way='2' THEN iad.invoice ELSE i.invoice END = di.invoice)\n" +
                "WHERE i.application='" + delivery.getApplication() + "' AND (di.delivery=" + id + " OR di.id IS NULL)\n" +
                "GROUP BY i.id, i.invoice_category, i.old_invoice, d.invoice_no, d.amount, i.amount, ia.division_way, iad.invoice, i.invoice, ic.result_type, ic.check_result";


        //获取审核一致的发票详情列表
        List<Map<String, Object>> list = slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();

        List<SlInvoiceDeliveryItem> items = slInvoiceDeliveryItemDao.getListByDeliveryId(delivery.getId());
        //查询该张发票有没有被登记
        List<Map<String, Object>> list2 = new ArrayList<>();
        for (Map<String, Object> m : list) {


            if ("1".equals(String.valueOf(m.get("checkResult")))) {
                list2.add(m);
                m.put("selected", false);
                for (SlInvoiceDeliveryItem item : items) {
                    String invoice = item.getInvoice() == null ? "null" : String.valueOf(item.getInvoice());
                    if (invoice.equals(String.valueOf(m.get("invoice")))) {
                        m.put("selected", true);
                        break;
                    }

                }
            }
        }
        map.put("list", list2);


        String customerName = null;
        Map<String, Object> customer = getCustomerNameByApplicationId(delivery.getApplication(), delivery.getOrg());
        if (customer != null) {
            customerName = String.valueOf(customer.get("customerName"));
        }
        data.put("receiver", delivery.getReceiver());
        data.put("deliverySubWay", delivery.getDeliverySubWay());
        data.put("deliveryCompany", delivery.getDeliveryCompany());
        data.put("deliverySn", delivery.getDeliverySn());
        data.put("deliveryTime", delivery.getDeliveryTime());
        data.put("deliveryWay", delivery.getDeliveryWay());
        data.put("memo", delivery.getMemo());
        data.put("operatorPhone", delivery.getOperatorPhone());
        data.put("operator", delivery.getOperator());
        data.put("customerName", customerName);
        if (delivery.getUpdateName() == null || "".equals(delivery.getUpdateName())) {
            data.put("operationName", delivery.getCreateName());
            data.put("operationDate", delivery.getCreateDate());
            data.put("operation", delivery.getCreator());
        } else {
            data.put("operationName", delivery.getUpdateName());
            data.put("operationDate", delivery.getUpdateDate());
            data.put("operation", delivery.getUpdator());
        }


        //发票地址
        List<Map<String, Object>> invoiceAddressList = pdCustomerService.getAllAddress((Integer) customer.get("customerId"), "2", "1");

        //其他订单
        List<Map<String, Object>> deliveryList = getDeliveryList(delivery.getOrg(), delivery.getId());


        map.put("customerInvoice", invoiceAddressList);
        map.put("deliveryList", deliveryList);

        map.put("status", 1);
        map.put("data", data);


        return map;
    }

    @Override
    public Map<String, Object> signForLook(Integer id, Integer oid) {
        Map map = new HashMap();
        Map data = new HashMap();
        SlInvoiceDelivery delivery = slInvoiceDeliveryDao.get(id);
        if (delivery == null) {
            map.put("status", 0);
            return map;
        }
        List<Map<String, Object>> list = slInvoiceDeliveryItemDao.getListByDeliveryIdForMap(id);


        String customerName = null;
        Map<String, Object> customer = getCustomerNameByApplicationId(delivery.getApplication(), delivery.getOrg());
        if (customer != null) {
            customerName = String.valueOf(customer.get("customerName"));
        }
        data.put("receiver", delivery.getReceiver());
        data.put("deliverySubWay", delivery.getDeliverySubWay());
        data.put("deliveryCompany", delivery.getDeliveryCompany());
        data.put("deliverySn", delivery.getDeliverySn());
        data.put("deliveryTime", delivery.getDeliveryTime());
        data.put("deliveryWay", delivery.getDeliveryWay());
        data.put("operator", delivery.getOperator());
        data.put("operatorPhone", delivery.getOperatorPhone());

        data.put("sginer", delivery.getSginer());
        data.put("signTime", delivery.getSignTime());
        data.put("signResult", delivery.getSignResult());
        data.put("signRecord", delivery.getSignRecord());

        data.put("memo", delivery.getMemo());
        data.put("customerName", customerName);
        data.put("list", list);
        map.put("status", 1);
        map.put("data", data);


        return map;
    }

    @Override
    @Transactional
    public Map<String, Object> signFor(HttpServletRequest request, User user) {
        Map map = new HashMap();
        String deliveryId = request.getParameter("deliveryId");
        String data = request.getParameter("data");
        String result = request.getParameter("result");
        String sginer = request.getParameter("sginer");
        String signTime = request.getParameter("signTime");
        String signRecord = request.getParameter("signRecord");
        JSONArray parseArray = JSON.parseArray(data);
        JSONObject json = null;
        SlInvoiceDelivery delivery = slInvoiceDeliveryDao.get(Integer.valueOf(deliveryId));
        if (delivery == null) {
            map.put("status", 0);
            map.put("msg", "传入申请单id错误");
            return map;
        }

        if (delivery.getSignResult() != null && !"".equals(delivery.getSignResult()) && !"3".equals(delivery.getSignResult())) {
            map.put("status", 0);
            map.put("msg", "已签收，无需重复操作");
            return map;
        }
        delivery.setSginer(sginer);

        delivery.setSignResult(result);
        delivery.setSignRecord(signRecord);
        delivery.setUpdateDate(new Date());
        delivery.setUpdateName(user.getUserName());
        delivery.setUpdator(user.getUserID());
        delivery.setSignOperation("3");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        try {
            delivery.setSignTime(dateFormat.parse(signTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        slInvoiceDeliveryDao.update(delivery);


        for (Object obj : parseArray) {
            json = (JSONObject) obj;
            String itemId = json.getString("itemId");
            String signResult = json.getString("signResult");
            String signError = json.getString("signError");
            String signHandle = json.getString("signHandle");

            if (itemId != null && !"".equals(itemId)) {
                SlInvoiceDeliveryItem item = slInvoiceDeliveryItemDao.get(Integer.valueOf(itemId));
                if (item == null) {
                    map.put("status", 0);
                    map.put("msg", "传入发票详情id错误");
                    return map;
                }
                item.setSignResult(signResult);
                item.setSignError(signError);
                item.setSignHandle(signHandle);
                item.setUpdateDate(new Date());
                item.setUpdateName(user.getUserName());
                item.setUpdator(user.getUserID());
                item.setOperation("5");
                if (item.getVersionNo() == null) {
                    item.setVersionNo(0);
                } else {
                    item.setVersionNo(item.getVersionNo() + 1);
                }

                slInvoiceDeliveryItemDao.update(item);
                User manager = userService.getUserByRoleCode(user.getOid(), "super");
                User account = userService.getUserByRoleCode(user.getOid(), "finance");
                SlInvoiceApplication invoiceApplication = slInvoiceApplicationDao.get(delivery.getApplication());
                if ("2".equals(signResult)) {
                    FinanceInvoiceDetail invoiceDetail = financeInvoiceDetailDao.get(item.getInvoice());
                    if (invoiceDetail != null) {
                        String fp = "";
                        String yy = "";
                        if ("1".equals(invoiceDetail.getType())) {
                            fp = "专用票";
                        } else if ("2".equals(invoiceDetail.getType())) {
                            fp = "普通票";
                        } else if ("3".equals(invoiceDetail.getType())) {
                            fp = "其他发票";
                        }

                        if ("1".equals(signError)) {
                            yy = "单价不对";
                        } else if ("2".equals(signError)) {
                            yy = "数量不对";
                        } else if ("3".equals(signError)) {
                            yy = "发票破损";
                        } else if ("4".equals(signError)) {
                            yy = "发票里多商品";
                        } else if ("5".equals(signError)) {
                            yy = "发票里少商品";
                        } else if ("6".equals(signError)) {
                            yy = "发票抬头不对";
                        } else if ("9".equals(signError)) {
                            yy = "其他原因";
                        } else if ("7".equals(signError)) {
                            yy = "发票被客户弄丢";
                        } else if ("8".equals(signError)) {
                            yy = "发票由于其他原因丢失";
                        }

                        if ("7".equals(signError) || "8".equals(signError)) {

                            Map msg = new HashMap();
                            msg.put("message", "号码为" + invoiceDetail.getInvoiceNo() + "的" + fp + "（" + yy + "），需确定应对方案。请重点关注销售近期提交的开票申请。");
                            msg.put("applicationId", item.getApplication());
                            msg.put("time", new Date());
                            msg.put("user", user.getUserName());

                            clusterMessageSendingOperations.convertAndSendToUser(invoiceApplication.getCreator() + "", "/signFor", null, null, null, null, JSON.toJSONString(msg));//推送
                            clusterMessageSendingOperations.convertAndSendToUser(manager.getUserID() + "", "/signFor", null, null, null, null, JSON.toJSONString(msg));//推送
                            clusterMessageSendingOperations.convertAndSendToUser(account.getUserID() + "", "/signFor", null, null, null, null, JSON.toJSONString(msg));//推送
                            List<User> userList = userService.getUsersByOidAndRoleCode(delivery.getOrg(), "('super','finance')");
                            for (User u : userList) {
                                suspendMsgService.saveUserSuspendMsg(1, "号码为" + invoiceDetail.getInvoiceNo() + "的" + fp + "（" + yy + "），需确定应对方案。请重点关注销售近期提交的开票申请。", "终止时间:" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), u.getUserID(), "invoiceSignFor", delivery.getApplication());
                            }

                        } else {
                            //作废
                            invoiceDetail.setState("3");
                            invoiceDetail.setOperation("4");
                            invoiceDetail.setUpdator(user.getUserID());
                            invoiceDetail.setUpdateName(user.getUserName());
                            invoiceDetail.setUpdateDate(new Date());
                            financeInvoiceDetailDao.update(invoiceDetail);

                            String message = "由于号码为" + invoiceDetail.getInvoiceNo() + "的" + fp + "（" + yy + "），导致客户未接收，故系统已将其作废。\n" +
                                    "请向销售要回该发票后，在开票系统中将其及时正式作废，以避免税费损失，如需向客户重新开票，销售会重新提出开票申请。";
                            Map msg = new HashMap();
                            msg.put("message", message);
                            msg.put("applicationId", item.getApplication());
                            msg.put("time", new Date());
                            msg.put("user", user.getUserName());
                            clusterMessageSendingOperations.convertAndSendToUser(invoiceApplication.getCreator() + "", "/signFor", null, null, null, null, JSON.toJSONString(map));//推送未签收
                            clusterMessageSendingOperations.convertAndSendToUser(manager.getUserID() + "", "/signFor", null, null, null, null, JSON.toJSONString(msg));//推送
                            clusterMessageSendingOperations.convertAndSendToUser(account.getUserID() + "", "/signFor", null, null, null, null, JSON.toJSONString(msg));//推送
                            List<User> userList = userService.getUsersByOidAndRoleCode(delivery.getOrg(), "('finance')");
                            for (User u : userList) {
                                suspendMsgService.saveUserSuspendMsg(1, message, "终止时间:" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), u.getUserID(), "invoiceSignFor", delivery.getApplication());
                            }
                        }

                    }

                } else {

                    Map msg = new HashMap();
                    msg.put("message", "正常签收");
                    msg.put("applicationId", item.getApplication());
                    msg.put("time", new Date());
                    msg.put("user", user.getUserName());
                    clusterMessageSendingOperations.convertAndSendToUser(invoiceApplication.getCreator() + "", "/signFor", null, null, null, null, JSON.toJSONString(map));//推送未签收
                    clusterMessageSendingOperations.convertAndSendToUser(manager.getUserID() + "", "/signFor", null, null, null, null, JSON.toJSONString(msg));//推送
                    clusterMessageSendingOperations.convertAndSendToUser(account.getUserID() + "", "/signFor", null, null, null, null, JSON.toJSONString(msg));//推送
                }

            }
        }
        //申请单id
        Integer id = delivery.getApplication();
        List<Map<String, Object>> list = getInvoiceRegistrationList(delivery.getOrg(), id);
        if (list.size() == 0) {
            SlInvoiceApplication sa = slInvoiceApplicationDao.get(id);
            sa.setState("9");
            slInvoiceApplicationDao.update(sa);
        }

        map.put("status", 1);
        return map;
    }

    @Override
    public Map<String, Object> signForUpdate(HttpServletRequest request, User user) {
        Map map = new HashMap();
        String deliveryId = request.getParameter("deliveryId");
        String data = request.getParameter("data");
        String result = request.getParameter("result");
        String sginer = request.getParameter("sginer");
        String signTime = request.getParameter("signTime");
        String signRecord = request.getParameter("signRecord");
        JSONArray parseArray = JSON.parseArray(data);
        JSONObject json = null;
        SlInvoiceDelivery delivery = slInvoiceDeliveryDao.get(Integer.valueOf(deliveryId));
        if (delivery == null) {
            map.put("status", 0);
            map.put("msg", "传入申请单id错误");
            return map;
        }

        //添加发运信息修改记录
        SlInvoiceDeliveryHistory history = new SlInvoiceDeliveryHistory();
        history.setInvoiceDelivery(delivery.getId());
        history.setOrg(delivery.getOrg());
        history.setApplication(delivery.getApplication());
        history.setApplicationNo(delivery.getApplicationNo());
        history.setReceiver(delivery.getReceiver());
        history.setReceiverPhone(delivery.getReceiverPhone());
        history.setDeliveryWay(delivery.getDeliveryWay());
        history.setDeliverySubWay(delivery.getDeliverySubWay());
        history.setDeliveryTime(delivery.getDeliveryTime());
        history.setDeliverySn(delivery.getDeliverySn());
        history.setDeliveryCompany(delivery.getDeliveryCompany());
        history.setOperator(delivery.getOperator());
        history.setOperatorPhone(delivery.getOperatorPhone());
        history.setLicenseTag(delivery.getLicenseTag());
        history.setMemo(delivery.getMemo());
        history.setSginer(delivery.getSginer());
        history.setSignRecord(delivery.getSignRecord());
        history.setSignTime(delivery.getSignTime());
        history.setSignResult(delivery.getSignResult());
        history.setOperator(delivery.getOperator());
        history.setOperatorPhone(delivery.getOperatorPhone());
        history.setDeliveryOperation(delivery.getDeliveryOperation());
        history.setSignOperation(delivery.getSignOperation());
        history.setPreviousId(delivery.getPreviousId());
        history.setVersionNo(delivery.getVersionNo());
        history.setCreator(user.getUserID());
        history.setCreateName(user.getUserName());
        history.setCreateDate(new Date());
        slInvoiceDeliveryHistoryDao.save(history);


        delivery.setSginer(sginer);

        delivery.setSignResult(result);
        delivery.setSignRecord(signRecord);
        delivery.setUpdateDate(new Date());
        delivery.setUpdateName(user.getUserName());
        delivery.setUpdator(user.getUserID());
        delivery.setSignOperation("3");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        try {
            delivery.setSignTime(dateFormat.parse(signTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        slInvoiceDeliveryDao.update(delivery);


        for (Object obj : parseArray) {
            json = (JSONObject) obj;
            String itemId = json.getString("itemId");
            String signResult = json.getString("signResult");
            String signError = json.getString("signError");
            String signHandle = json.getString("signHandle");

            if (itemId != null && !"".equals(itemId)) {
                SlInvoiceDeliveryItem item = slInvoiceDeliveryItemDao.get(Integer.valueOf(itemId));
                if (item == null) {
                    map.put("status", 0);
                    map.put("msg", "传入发票详情id错误");
                    return map;
                }
                SlInvoiceDeliveryItemHistory itemHistory = new SlInvoiceDeliveryItemHistory();
                itemHistory.setInvoiceDelivery(item.getId());
                itemHistory.setDelivery(history.getId());
                itemHistory.setInvoice(item.getInvoice());
                itemHistory.setApplication(item.getApplication());
                itemHistory.setMemo(item.getMemo());
                itemHistory.setCreator(user.getUserID());
                itemHistory.setOperation(item.getOperation());
                itemHistory.setCreateName(user.getUserName());
                itemHistory.setCreateDate(new Date());
                itemHistory.setSignError(item.getSignError());
                itemHistory.setSignHandle(item.getSignHandle());
                itemHistory.setSignResult(item.getSignResult());
                slInvoiceDeliveryItemHistoryDao.save(itemHistory);


                item.setSignResult(signResult);
                item.setSignError(signError);
                item.setSignHandle(signHandle);
                item.setUpdateDate(new Date());
                item.setUpdateName(user.getUserName());
                item.setUpdator(user.getUserID());
                item.setOperation("5");
                if (item.getVersionNo() == null) {
                    item.setVersionNo(0);
                } else {
                    item.setVersionNo(item.getVersionNo() + 1);
                }

                slInvoiceDeliveryItemDao.update(item);


            }
        }

        map.put("status", 1);
        return map;
    }

    @Override
    @Transactional
    public Map<String, Object> updateAllNotSignFor(HttpServletRequest request, User user) {
        Map map = new HashMap();
        String deliveryId = request.getParameter("deliveryId");
        if (deliveryId == null || "".equals(deliveryId)) {
            map.put("status", 0);
            map.put("msg", "传入发票id错误");
            return map;

        }
        SlInvoiceDelivery delivery = slInvoiceDeliveryDao.get(Integer.valueOf(deliveryId));
        delivery.setSignResult(null);
        delivery.setSignRecord(null);
        delivery.setSignTime(null);
        delivery.setUpdateDate(new Date());
        delivery.setUpdateName(user.getUserName());
        delivery.setUpdator(user.getUserID());
        delivery.setSignOperation("3");
        slInvoiceDeliveryDao.update(delivery);
        List<SlInvoiceDeliveryItem> list = slInvoiceDeliveryItemDao.getListByDeliveryId(Integer.valueOf(deliveryId));
        for (SlInvoiceDeliveryItem item : list) {
            item.setSignResult(null);
            item.setSignError(null);
            item.setSignHandle(null);
            item.setUpdateDate(new Date());
            item.setUpdateName(user.getUserName());
            item.setUpdator(user.getUserID());
            item.setOperation("5");
            item.setVersionNo(item.getVersionNo() + 1);
            slInvoiceDeliveryItemDao.update(item);
        }

        //申请单id
        Integer id = delivery.getApplication();
        List<Map<String, Object>> l = getInvoiceRegistrationList(delivery.getOrg(), id);
        if (l.size() == 0) {
            SlInvoiceApplication sa = slInvoiceApplicationDao.get(id);
            sa.setState("7");
            slInvoiceApplicationDao.update(sa);
        }
        map.put("status", 1);
        return map;
    }

    @Override
    public Map<String, Object> signForList(Integer oid, Integer currPage, Integer pageSize) {
        //获取未签收完成发票申请单id
        Map<String, Object> res = slInvoiceApplicationDao.getApplicationBySignFor(oid, currPage, pageSize, 0);
        List<Map<String, Object>> idsList = (List<Map<String, Object>>) res.get("data");
        String ids = "";
        if (idsList == null) {
            res.put("data", new ArrayList<>());
            res.put("status", 1);
            return res;
        }
        for (Map<String, Object> m : idsList) {
            ids = ids + "," + m.get("application");
        }
        ids = ids.substring(1, ids.length());
        String sql = "select distinct a.id, a.applicant, a.applicant_name as applicantName, a.creator, a.create_name as createName, a.create_date as createDate, a.apply_date as applyDate, c.full_name as customerName, c.`code` as customerCode, o.sn, ( select COUNT(di.id) from t_sl_invoice_delivery_item as di where di.application = a.id and (di.sign_result = '' or di.sign_result is null )) as invoiceCount, case when a.division_way != 2 then ( select SUM(i.amount) from t_sl_invoice_delivery_item as di left join t_sl_invoice_application_item as i on di.invoice = i.invoice where i.application = a.id and (di.sign_result = '' or di.sign_result is null )) else ( select ifnull(SUM(fid.amount),0) from t_sl_invoice_delivery_item as di left join t_sl_invoice_application_detail iad on di.invoice = iad.invoice left join t_sl_invoice_application_item i on iad.application_item = i.id left join t_finance_invoice_detail fid on iad.invoice = fid.id where i.application = a.id and (di.sign_result = '' or di.sign_result is null )) end as amount from t_sl_invoice_application as a left join t_sl_orders as o on o.id = a.orders left join t_sl_invoice_application_item tsiai on tsiai .application = a.id left join t_sl_customer as c on c.id = o.customer " +
                "where a.id in (" + ids + ")";

        List<Map<String, Object>> list = slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();

        List<Map<String, Object>> deliveryList = slInvoiceDeliveryDao.getListInApplicationId(ids);

        for (Map<String, Object> m : list) {
            List<Map<String, Object>> r = new ArrayList<>();
            for (Map<String, Object> d : deliveryList) {
                if (m.get("id").equals(d.get("application"))) {
                    if ("0".equals(String.valueOf(d.get("recordNum")))) {
                        d.put("recordBut", false);
                    } else {
                        d.put("recordBut", true);
                    }
                    if (d.get("signResult") == null || "".equals(d.get("signResult").toString())) {
                        r.add(d);
                    }

                }
            }
            m.put("delivery", r);
        }
        res.put("data", list);
        res.put("status", 1);
        return res;
    }

    @Override
    public List<Map<String, Object>> getDeliveryList(Integer org, Integer deliveryId) {
        String sql = "select \n" +
                "delivery_sn deliverySn,\n" +
                "delivery_company deliveryCompany,\n" +
                "create_name operator,\n" +
                "delivery_time deliveryTime\n" +
                "from t_sl_invoice_delivery\n" +
                "where delivery_sub_way = 1 and org=" + org + " \n" +
                "and (sign_result = 2 or sign_result=3 or sign_result is null or sign_result = '')   ";
        if (deliveryId != null) {
            sql += " and id !=" + deliveryId;
        }

        return slInvoiceDeliveryDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public Map<String, Object> signForRecordList(Integer oid, Integer currPage, Integer pageSize) {
        //获取未签收完成发票申请单id
        Map<String, Object> res = slInvoiceApplicationDao.getApplicationBySignFor(oid, currPage, pageSize, 1);
        List<Map<String, Object>> idsList = (List<Map<String, Object>>) res.get("data");
        String ids = "";
        if (idsList == null) {
            res.put("data", new ArrayList<>());
            res.put("status", 1);
            return res;
        }
        for (Map<String, Object> m : idsList) {
            ids = ids + "," + m.get("application");
        }

        ids = ids.substring(1, ids.length());

        String sql = "select a.id, a.applicant, a.applicant_name as applicantName, a.creator, a.create_name as createName, a.create_date as createDate, a.apply_date as applyDate, c.full_name as customerName, c.`code` as customerCode, o.sn, ( select COUNT(di.id) from t_sl_invoice_delivery_item as di where di.application = a.id ) as invoiceCount, ifnull(case when a.division_way != 2 then ( select SUM(i.amount) from t_sl_invoice_delivery_item as di left join t_sl_invoice_application_item as i on di.invoice = i.invoice where i.application = a.id ) else ( select SUM(i.amount) from t_sl_invoice_delivery_item as di left join t_sl_orders_item_invoice oii on oii.invoice = di.invoice left join t_sl_invoice_application_item i on oii.application_item = i.id where i.application = a.id ) end,0) as amount from\n" +
                "t_sl_invoice_application as a\n" +
                "LEFT JOIN t_sl_orders as o on o.id=a.orders\n" +
                "left join t_sl_invoice_application_item tsiai on tsiai .application =a.id \n" +
                "LEFT JOIN t_sl_customer as c on c.id=o.customer\n" +
                "where a.id in (" + ids + ")";
        List<Map<String, Object>> list = slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();

        List<Map<String, Object>> deliveryList = slInvoiceDeliveryDao.getListInApplicationId(ids);

        for (Map<String, Object> m : list) {
            List<Map<String, Object>> r = new ArrayList<>();
            for (Map<String, Object> d : deliveryList) {
                if (m.get("id").equals(d.get("application"))) {
                    if (d.get("signResult") != null && !"".equals(d.get("signResult").toString())) {
                        if ("0".equals(String.valueOf(d.get("signNum")))) {
                            d.put("recordBut", false);
                        } else {
                            d.put("recordBut", true);
                        }
                        r.add(d);
                    }
                }
            }
            m.put("delivery", r);
        }
        res.put("data", list);
        res.put("status", 1);
        return res;
    }

    @Override
    public List<Map<String, Object>> signForUpdateRecordList(Integer id, Integer oid) {
        String sql = "select id recordId,invoice_delivery as deliveryId , create_name as updateName,create_date as updateDate,previous_id as previousId from t_sl_invoice_delivery_history where invoice_delivery=" + id + "  and org=" + oid + " and (sign_result != '' or sign_result is not null) order by create_date desc";
        return slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public Map<String, Object> signForUpdateRecordLook(Integer recordId, String isNew) {
        Map map = new HashMap();
        String customerName = null;
        Map before = new HashMap();
        Map after = new HashMap();
        //说明是最新的修改记录
        if ("1".equals(isNew)) {
            //修改前
            SlInvoiceDeliveryHistory history = slInvoiceDeliveryHistoryDao.get(recordId);

            if (history == null) {
                map.put("status", 0);
                map.put("msg", "参数传递错误");
                return map;
            }

            Map<String, Object> customer = getCustomerNameByApplicationId(history.getApplication(), history.getOrg());
            if (customer != null) {
                customerName = String.valueOf(customer.get("customerName"));
            }

            SlInvoiceDelivery delivery = slInvoiceDeliveryDao.get(history.getInvoiceDelivery());
            if (delivery == null) {
                if (history == null) {
                    map.put("status", 0);
                    map.put("msg", "数据有误");
                    return map;
                }
            }
            before.put("receiver", history.getReceiver());
            before.put("deliverySubWay", history.getDeliverySubWay());
            before.put("deliveryWay", history.getDeliveryWay());
            before.put("deliveryCompany", history.getDeliveryCompany());
            before.put("deliverySn", history.getDeliverySn());
            before.put("deliveryTime", history.getDeliveryTime());
            before.put("memo", history.getMemo());
            before.put("customerName", customerName);
            before.put("operator", history.getOperator());
            before.put("operatorPhone", history.getOperatorPhone());

            before.put("sginer", history.getSginer());
            before.put("signTime", history.getSignTime());
            before.put("signResult", history.getSignResult());
            before.put("signRecord", history.getSignRecord());
            List<Map<String, Object>> itemHistoryList = slInvoiceDeliveryItemHistoryDao.getListByDeliveryIdForSign(history.getId());
            before.put("items", itemHistoryList);


            after.put("receiver", delivery.getReceiver());
            after.put("deliverySubWay", delivery.getDeliverySubWay());
            after.put("deliveryWay", delivery.getDeliveryWay());
            after.put("deliveryCompany", delivery.getDeliveryCompany());
            after.put("deliverySn", delivery.getDeliverySn());
            after.put("deliveryTime", delivery.getDeliveryTime());
            after.put("memo", delivery.getMemo());
            after.put("operator", delivery.getOperator());
            after.put("operatorPhone", delivery.getOperatorPhone());
            after.put("customerName", customerName);

            after.put("sginer", delivery.getSginer());
            after.put("signTime", delivery.getSignTime());
            after.put("signResult", delivery.getSignResult());
            after.put("signRecord", delivery.getSignRecord());
            List<Map<String, Object>> itemList = slInvoiceDeliveryItemDao.getListByDeliveryIdForSignMap(delivery.getId());
            after.put("items", itemList);
        } else {
            //修改前
            SlInvoiceDeliveryHistory history = slInvoiceDeliveryHistoryDao.get(recordId);
            Map<String, Object> customer = getCustomerNameByApplicationId(history.getApplication(), history.getOrg());
            if (customer != null) {
                customerName = String.valueOf(customer.get("customerName"));
            }


            before.put("receiver", history.getReceiver());
            before.put("deliverySubWay", history.getDeliverySubWay());
            before.put("deliveryWay", history.getDeliveryWay());
            before.put("deliveryCompany", history.getDeliveryCompany());
            before.put("deliverySn", history.getDeliverySn());
            before.put("deliveryTime", history.getDeliveryTime());
            before.put("memo", history.getMemo());
            before.put("customerName", customerName);
            before.put("operator", history.getOperator());
            before.put("operatorPhone", history.getOperatorPhone());

            before.put("sginer", history.getSginer());
            before.put("signTime", history.getSignTime());
            before.put("signResult", history.getSignResult());
            before.put("signRecord", history.getSignRecord());
            List<Map<String, Object>> beforeHistoryList = slInvoiceDeliveryItemHistoryDao.getListByDeliveryIdForSign(history.getId());
            before.put("items", beforeHistoryList);

            //修改后
            SlInvoiceDeliveryHistory afterHistory = slInvoiceDeliveryHistoryDao.getByPreviousId(history.getId());
            if (afterHistory == null) {
                after.put("receiver", null);
                after.put("deliverySubWay", null);
                after.put("deliveryWay", null);
                after.put("deliveryCompany", null);
                after.put("deliverySn", null);
                after.put("deliveryTime", null);
                after.put("memo", null);
                after.put("customerName", customerName);
                after.put("operator", null);
                after.put("operatorPhone", null);

                after.put("sginer", null);
                after.put("signTime", null);
                after.put("signResult", null);
                after.put("signRecord", null);
                after.put("items", new ArrayList<>());

            } else {
                after.put("receiver", afterHistory.getReceiver());
                after.put("deliverySubWay", afterHistory.getDeliverySubWay());
                after.put("deliveryCompany", afterHistory.getDeliveryCompany());
                after.put("deliveryWay", afterHistory.getDeliveryWay());
                after.put("deliverySn", afterHistory.getDeliverySn());
                after.put("deliveryTime", afterHistory.getDeliveryTime());
                after.put("memo", afterHistory.getMemo());
                after.put("customerName", customerName);
                after.put("operator", afterHistory.getOperator());
                after.put("operatorPhone", afterHistory.getOperatorPhone());

                after.put("sginer", afterHistory.getSginer());
                after.put("signTime", afterHistory.getSignTime());
                after.put("signResult", afterHistory.getSignResult());
                after.put("signRecord", afterHistory.getSignRecord());
                List<Map<String, Object>> afterHistoryList = slInvoiceDeliveryItemHistoryDao.getListByDeliveryIdForSign(afterHistory.getId());
                after.put("items", afterHistoryList);

            }
        }
        map.put("status", 1);
        map.put("before", before);
        map.put("after", after);

        return map;
    }


    public Map<String, Object> getCustomerNameByApplicationId(Integer id, Integer org) {

        String sql = "SELECT\n" +
                "a.id,\n" +
                "a.applicant,\n" +
                "a.applicant_name as applicantName,\n" +
                "a.creator,\n" +
                "a.create_name as createName, \n" +
                "a.create_date as createDate,\n" +
                "a.apply_date as applyDate,\n" +
                "c.id customerId,\n" +
                "c.full_name as customerName,\n" +
                "c.`code` as customerCode,\n" +
                "o.sn,\n" +
                "(select COUNT(i.id) from t_sl_invoice_application_item as i where i.application=a.id) as invoiceCount,\n" +
                "(select SUM(i.amount) from t_sl_invoice_application_item as i where i.application=a.id) as amount\n" +
                "FROM\n" +
                "t_sl_invoice_application as a\n" +
                "LEFT JOIN t_sl_orders as o on o.id=a.orders\n" +
                "LEFT JOIN t_sl_customer as c on c.id=o.customer\n" +
                "where a.id=" + id + " and a.org=" + org;

        Query result = slInvoiceApplicationDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP);
        if (result.getResultList().size() == 0) {
            return null;
        }
        return (Map<String, Object>) result.getSingleResult();

    }


    public static void main(String[] args) {

        String sql = "SELECT\n" +
                "a.id,\n" +
                "a.applicant,\n" +
                "a.applicant_name as applicantName,\n" +
                "a.creator,\n" +
                "a.create_name as createName, \n" +
                "a.create_date as createDate,\n" +
                "a.apply_date as applyDate,\n" +
                "c.name as customerName,\n" +
                "c.`code` as customerCode,\n" +
                "o.sn,\n" +
                "(select COUNT(di.id) from t_sl_invoice_delivery_item as di where di.application=a.id and (di.sign_result = '' or di.sign_result is  null)) as invoiceCount,\n" +
                "(select SUM(i.amount) from t_sl_invoice_application_item as i where i.application=a.id) as amount\n" +
                "FROM\n" +
                "t_sl_invoice_application as a\n" +
                "LEFT JOIN t_sl_orders as o on o.id=a.orders\n" +
                "LEFT JOIN t_sl_customer as c on c.id=o.customer\n";


        System.out.println(sql);


    }
}
