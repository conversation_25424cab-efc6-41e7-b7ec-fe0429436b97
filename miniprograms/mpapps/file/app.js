// app.js
import localStorage from 'localStorage'
const miniProgram = wx?.getAccountInfoSync ? wx.getAccountInfoSync().miniProgram : tt?.getEnvInfoSync().microapp
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = localStorage.getItem('logs') || []
    logs.unshift(Date.now())
    localStorage.setItem('logs', logs)
  },
  globalData: {
    // webViewRoot: 'https://wuyu-n.frp.btransmission.com',
    webViewRoot: 'https://dvm01.btransmission.com/vue/file/dist/index.html',
    tgCode:'weChat',
    appId: miniProgram.appId,
    navigationBarTitleText: '贝塔模板',
    mpVersion: 'SVN_REVISION',
    envType: miniProgram.envVersion ?? miniProgram.envType
  }
})
