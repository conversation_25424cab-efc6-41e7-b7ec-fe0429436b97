const localStorage = {
  getItem(key) {
    let result = false
    try {
      result = wx.getStorageSync(key)
      if (result === '') {  
        const res = wx.getStorageInfoSync();
        if(res.keys.indexOf(key)<0) {
          result = null
        }
      }
    } catch (e) {
      console.log('wx.getStorageSync error', e)
    }
    return result
  },
  setItem(key, value) {
    try {
      wx.setStorageSync(key, value)
    } catch (e) {
      console.log('wx.setStorageSync error', e)
    }
  },
  removeItem(key) {
    try {
      wx.removeStorageSync(key)
    } catch (e) {
      console.log('wx.removeStorageSync error', e)
    }
  },
  clear() {
    try {
      wx.clearStorageSync()
    } catch (e) {
      console.log('wx.clearStorageSync error', e)
    }
  }
}
export default localStorage