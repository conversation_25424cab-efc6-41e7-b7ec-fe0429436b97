// logs.js
import localStorage from '../../localStorage'
const util = require('../../utils/util.js')
// import 'https://cdn.bootcdn.net/ajax/libs/hls.js/1.2.7/hls.min.js'

Page({
  data: {
    logs: []
  },
  onLoad() {
    // if(Hls.isSupported()) {
    //   let video = document.getElementById('video')
    //   let track = document.createElement('track')
    //   track.src =  'vod.vtt'
    //   track.srclang='zh-cn'
    //   track.label='简体中文'
    //   track.kind='subtitles'
    //   track.default = 'default'
    //   let hls = new Hls()
    //   hls.loadSource('/vod/test2.mp4/index.m3u8')
    //   hls.attachMedia(video)
    //   hls.on(Hls.Events.MANIFEST_PARSED,function() {
    //     video.appendChild(track)
    //     video.play()
    //   })
    // }
    this.setData({
      logs: (localStorage.getItem('logs') || []).map(log => {
        return {
          date: util.formatTime(new Date(log)),
          timeStamp: log
        }
      })
    })
  }
})
