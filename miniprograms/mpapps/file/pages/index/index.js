// index.js
// 获取应用实例
import localStorage from '../../localStorage'
Page({
  data: {
    uri: '',
    motto: '欢迎使用',
    isControl: '',
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    canIUseGetUserProfile: false,
    canIUseOpenData: false
    // canIUseOpenData: wx.canIUse('open-data.type.userAvatarUrl') && wx.canIUse('open-data.type.userNickName')
    //  如需尝试获取用户信息可改为 false
  },
  // 事件处理函数
  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },
  onLoad() {
    
    // if (wx.getUserProfile) {
    //   this.setData({
    //     canIUseGetUserProfile: true
    //   })
    // }
    // wx.chooseMessageFile({
    //   count: 10,
    //   type: 'all',
    //   success (res) {
    //     // tempFilePath可以作为img标签的src属性显示图片
    //     const tempFilePaths = res.tempFiles
    //   }
    // })
    let that = this
    let globalData = getApp().globalData
    let secretKey = that.generateMixed(92)
    let url = `https://dvm05.btransmission.com/tp/tpAppGetToken.do`
    wx.request({
      'url': url,
      'data':{
        'appId':  globalData.appId,
        'secretKey': secretKey,
        'appVersion': globalData.mpVersion
      },
      success: function(res){
        console.log('验证返回值：', res.data)
        let data = String(res.data)
        that.setData({  isControl: data })
        that.goNext()
      },
      fail:function(err){
        console.log('验证返回值 fail：', err)
      }

    })
    // this.bindViewTap()
  },
  goNext: function () {
    let data = this.data.isControl
    let len = (data && data.length) || 0
    console.log('返回值长度=', len, 'data=', data)
    if(len === 16){ // yeah  shenheqi
      // this.goNext2()
    }else{
      this.login()
    }
  },
  login: function () {
    let that = this
    console.log('Welcome to Mini Code', that.uri)
    let globalData = getApp().globalData
    that.uri = 'appId=' + globalData.appId + '&mpVersion=' + globalData.mpVersion + '&envType' + globalData.envType + '&code='

    wx.getSetting({
      success(res) {
        console.log(" wx.getSetting:", res );

        if (!res.authSetting['scope.userInfo']) {
          wx.authorize({
            scope: 'scope.userInfo',
            success (res) {
              console.log(" wx.authorize:", res );

              // 用户已经同意小程序使用录音功能，后续调用 wx.startRecord 接口不会弹窗询问
            }
          })
        }
      }
    })

    wx.checkSession({
      success(res) {
        let code = localStorage.getItem('sessionCode')
        if (code != null) {
          that.uri += code
          that.wxUserInfo()
        } else {
          console.log('  wx.checkSession success  else', )
          that.wxLogin()
        }
      },
      fail() {
        console.log('  wx.checkSession fail', )

        that.wxLogin()
      }
    })
  },
  wxLogin : function() {
    console.log('  wx.wxLogin ', )
    let that = this
    console.log('wxLogin', that.uri)
    wx.login({
      force: true,
      success(res) {
        that.uri += res.code
        console.log(`login 调用成功${res.code} ${res.anonymousCode}`);
        localStorage.setItem('sessionCode', res.code)
        that.wxUserInfo()
      },
      fail(res) {
        console.log(`login 调用失败`);
        that.setData({msg : '您拒绝了授权申请'})
      },
    });
  },
  wxUserInfo: function(){
    console.log('wxUserInfo ' )

    let that = this
    console.log('wxUserInfo', that.uri)
    wx.getUserProfile({
      desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        console.log(res)
        console.log(`getUserProfile 调用成功`, res)
        that.uri += '&userInfo=' + encodeURIComponent(JSON.stringify(res.userInfo))
        that.navigate()
        
      },
      fail(res){
        console.log(`getUserInfo 调用失败`, res)
        that.navigate()
      }
    })
  },
  navigate: function () {
    let that = this
    console.log('navigate', that.uri)
    wx.navigateTo({
      url: '/pages/detail/index?' + that.uri,
      success: (res) => {
      },
      fail: (res) => {
      },
    });
  },
  generateMixed(n) {
    var str = ['0','1','2','3','4','5','6','7','8','9',
      'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',
      'a','b','c','f','g','h','i','j','k','l','m','p','q','t','u','v','','x','y','z',
      // '@','#','$','¥','%','^','&','*','<','>','?','{','+','=','_','-'
    ];
    var res = "";
    let length = str.length - 1
    for(var i = 0; i < n+1 ; i ++) {
      var id = Math.ceil(Math.random()*length);
      res += str[id];
    }
    let insertStr = 'wonderss';
    let insertStrLen = insertStr.length;
    for(let j=0; j < insertStrLen; j++ ){
      let len =  parseInt(res.length / insertStrLen)
      let startIndex = j * len;
      // let endIndex = (j+1) * len;
      let selectIndex = Math.round(Math.random()* len +startIndex);
      let insertA = insertStr[j];
      res = `${ res.substring(0,selectIndex) }${ insertA }${ res.substring(selectIndex) }`;
    }

    return res;
  }
})
