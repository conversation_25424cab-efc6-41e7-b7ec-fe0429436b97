Page({
  data: {
    // 'userImg':'',
    // 'title':''
    'userInfo':{}
  },
  onLoad: function (options) {
    let that = this
    tt.getStorage({
      key: "userInfo",
      success(res) {
        console.log('缓存取出 userInfo =', res)
        that.setData({ userInfo : res.data })

      },
      fail(res) {
        console.log('缓存取出 userInfo失败 = ', res);
      },
    });
  },
  goPage : function (env) {
    let address = env.currentTarget.dataset.address
    console.log('address=', address)
    switch (address){
      case 'mine':
        tt.navigateTo({ 'url': '/pages/mine/privateSpace/my/my' })
        break;
      case 'about':
        tt.navigateTo({ 'url': '/pages/mine/privateSpace/about/about' })
        break;
      case 'useFee':
        tt.navigateTo({ 'url': '/pages/mine/privateSpace/useFee/useFee' })
      break;

    }

  },
})