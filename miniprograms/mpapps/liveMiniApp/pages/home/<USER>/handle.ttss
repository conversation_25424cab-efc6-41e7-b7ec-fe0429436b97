.mar30{ margin-top: 30px; }
 .handleC{
    display: flex;
    padding-top: 10px;
    background-color: #fff;
    width: 100%;
    
  }
  .handleC>view{
    flex: 1;
    line-height: 30px;
    border-bottom: 2px solid #fff;
    text-align: center;
    padding:10px 0;
  }
  .handleC>view.activeH{
    border-bottom-color: #53b5a8;
  }
  .nullKlass{
    line-height: 200px;
    color: #ccc;
    font-size: 26px;
    text-align: center;
  }
  .icon{
    width: 20px;
    height: 19px;
    display: inline-block;
    background-size: 20px;
    margin-right: 10px;
    position: relative;
    top:5px;
  }
 

