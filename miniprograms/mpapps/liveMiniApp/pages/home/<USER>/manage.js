// /Users/<USER>/Documents/ideaProject/VSCproject/java/miners_trunk/miniprograms/mpapps/live/pages/home/<USER>/manage.js
Page({
  data: {
    curDate:'',
    weekDay:''
  },
  onLoad: function (options) {
    
    this.setData({ curDate :  (new Date()).format("yyyy年M月d日")  })
    this.setData({ weekDay :  "日一二三四五六".charAt(new Date().getDay()) })
    console.log('errr')
  },
  gopage : function (env) {
    console.log(env)
    let address = env.currentTarget.dataset.address
    console.log('address=', address)
    switch (address){
      case 'lyricMsg':
      case 'xiMsg':
      case 'ziMsg':
        tt.navigateTo({ 'url': `/pages/home/<USER>/${ address }`})
        break;

    }

  },
})