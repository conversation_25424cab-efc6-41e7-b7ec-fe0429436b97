<!-- 数据 -->
<view class="mainContainer">

    <page-head></page-head>

    <view class="handleC">
        <view data-type="1" class="{{activeH === 1 ? 'activeH':''}}" bindtap="tabNum" >任务/申请（X）</view>
        <view data-type="2" class="{{activeH === 2 ? 'activeH':''}}" bindtap="tabNum" >处理/审批（X）</view>
        <view data-type="3" class="{{activeH === 3 ? 'activeH':''}}" bindtap="tabNum" >消息（X）</view>
    </view>
    <view class="mar30">
        <view tt:if="{{ activeH === 1 }}">
          <view v-if="managerCode === 'super'" class="live-panel live-tr">
            <view data-address="geTask" bind:tap="gopage">
                <image class="icon" src="/assets//manage/yinyueshezhi.png"></image>
              歌词任务
            </view>
          </view>
        </view>
        <view tt:if="{{ activeH === 2 }}">
          <view class="live-panel live-pad">
            <view class="live-tr live-tr-line" data-address="geTaskhadle" bind:tap="gopage">
                <image class="icon" src="/assets//manage/yinyueshezhi.png"></image>
              歌词任务处理
            </view>
            <view class="live-tr" data-address="geTaskCharge" bind:tap="gopage">
                <image class="icon" src="/assets//manage/yinyueshezhi.png"></image>
              歌词任务审批
            </view>
          </view>
        </view>
        <view tt:if="{{ activeH === 3 }}">
          <view class="nullKlass">
            暂无消息数据
          </view>
        </view>
  
      </view>
      
    

</view>
