// /Users/<USER>/Documents/ideaProject/VSCproject/java/miners_trunk/miniprograms/mpapps/live/pages/home/<USER>/handle.js
Page({
  data: {
    activeH:2
  },
  onLoad: function (options) {

  },
  tabNum(env){
    let type = Number(env.currentTarget.dataset.type);
    console.log('type=', type)
    this.setData({ activeH : type })
  },
  gopage : function (env) {
    console.log(env)
    let address = env.currentTarget.dataset.address
    console.log('address=', address)
    switch (address){
      case 'geTask':
      case 'geTaskhadle':
      case 'geTaskCharge':
        tt.navigateTo({ 'url': `/pages/home/<USER>/${ address }`})
        break;

    }

  },
})