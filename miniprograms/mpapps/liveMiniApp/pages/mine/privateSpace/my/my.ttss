/* $imgWid: 80px; */
/* $imgWid2: 40px; */


.houseSty8{
    position: relative;
    top:5px;
  }
  .my-flex-panel{
    display: flex; padding:20px; background-color: #fff; margin-bottom: 20px;
  }
  .img{
    flex: 1; display: inline-block; overflow: hidden; background-color: #fff;
  }
  .img image{ background-color: #eee; border-radius: 80px; width:80px; height:80px; position: relative; top: 20px; }

  .user{
    flex: 3;
    padding-left: 15px;
  }
  .officeName{ font-size: 1.1em; line-height: 50px; color: #53b5a8; font-weight:bold;  }
  .userInfo{ line-height:30px; color: #555; font-size: 0.9em; }
 
  .icon{ width: 20px; height:20px; background-color: #fff; background-repeat: no-repeat; background-size: 100%; display: inline-block; position: relative; top:6px; margin-right:14px; }

 

