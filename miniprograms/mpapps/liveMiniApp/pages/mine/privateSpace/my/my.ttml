<view>
    <view class="mine">
        <view class="my-flex-panel my-flex-item">
            <view class="img">
                <image class="image" src="{{ userInfo.avatarUrl }}" />
            </view>
            <view class="user">
                <view class="officeName">{{ userInfo.nickName }}</view>
                <view class="userInfo">
                    <view bindtap="goPage('userMsg')">
                        <text class="txt">
                            {{ userInfo.country ||'地域未知' }} {{userInfo.province }} {{userInfo.city }}
                        </text>
                    </view>
                </view>
            </view>
        </view>
        <view>
            <view class="live-panel live-pad">
                <view class="live-tr live-tr-line">
                    <image class="icon" src="/assets/my/changeAcc.png"></image>
                    修改账号

                </view>
                <view class="live-tr">
                    <image class="icon" src="/assets/my/about.png"></image>
                    关于

                </view>
            </view>

            <!-- <view class="live-panel live-tr">
                <view @click="goPage('register1')">
                    <image class="icon" src="/assets/my/changeOffice.png"></image>
                    注册工作室
                </view>
            </view> -->
            <view class="live-panel live-tr">
                <view>
                    <image class="icon" src="/assets/my/myIncome.png"></image>
                    我的收益

                </view>
            </view>
            <view class="live-panel live-pad live-tr" data-address="backOffice"  bindtap="goPage">
                <view>
                    <image class="icon" src="/assets/my/return.png"></image>
                    返回机构
                </view>
              
            </view>


        </view>
        <tab-bar></tab-bar>

    </view>
</view>