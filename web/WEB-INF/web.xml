<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance
           http://www.springmodules.org/schema/cache/springmodules-cache.xsd
           http://www.springmodules.org/schema/cache/springmodules-ehcache.xsd"
           xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
		   http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
           version="3.0">



    <filter>
        <filter-name>SpringOpenSessionInViewFilter</filter-name>
        <filter-class>org.springframework.orm.hibernate5.support.OpenSessionInViewFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>
    <filter-mapping>
        <filter-name>SpringOpenSessionInViewFilter</filter-name>
        <url-pattern>*.do</url-pattern>
        <url-pattern>/socket/*</url-pattern>
    </filter-mapping>
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:applicationContext.xml</param-value>
    </context-param>

    <filter>
        <filter-name>CORS</filter-name>
        <filter-class>com.thetransactioncompany.cors.CORSFilter</filter-class>
        <init-param>
            <param-name>cors.allowOrigin</param-name>
            <param-value>*</param-value>
        </init-param>
        <init-param>
            <param-name>cors.supportedMethods</param-name>
            <param-value>GET, POST, HEAD, PUT, DELETE, OPTIONS</param-value>
        </init-param>
        <init-param>
            <param-name>cors.supportedHeaders</param-name>
            <param-value>Authorization, Accept, Origin, X-Requested-With, Content-Type, Last-Modified, Access-Control-Allow-Origin, Wndrss-Prj, token</param-value>
        </init-param>
        <init-param>
            <param-name>cors.exposedHeaders</param-name>
            <param-value>Set-Cookie, Wndrss-Prj, token</param-value>
        </init-param>
        <init-param>
            <param-name>cors.supportsCredentials</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CORS</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
<!--    删除SessionListener -->
<!--    <listener>-->
<!--        <listener-class>cn.sphd.miners.common.initializer.SessionListener</listener-class>-->
<!--    </listener>-->

    <servlet>
        <servlet-name>dispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:dispatcher-servlet.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
        <async-supported>true</async-supported>
    </servlet>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/assets/*</url-pattern>
        <url-pattern>/css/*</url-pattern>
        <url-pattern>/jqGrid/*</url-pattern>
        <url-pattern>/My97DatePicker/*</url-pattern>
        <url-pattern>/script/*</url-pattern>
        <url-pattern>/upload/*</url-pattern>
        <url-pattern>/vue/file/dist/*</url-pattern>
        <url-pattern>/vue/investigation/dist/*</url-pattern>
        <url-pattern>/vue/liveMiniApp/dist/*</url-pattern>
        <url-pattern>/vue/message/dist/*</url-pattern>
        <url-pattern>/vue/recruit/dist/*</url-pattern>
        <url-pattern>/vue/survey/dist/*</url-pattern>
        <url-pattern>/vue/liveAssistant/dist/*</url-pattern>
        <url-pattern>/vue/steamer/dist/*</url-pattern>
        <url-pattern>/vue/minersFrontEnd/dist/*</url-pattern>
        <url-pattern>/vue/minersActiveAcc/dist/*</url-pattern>
        <url-pattern>/vue/wonderssManager/dist/*</url-pattern>
        <url-pattern>/favicon.ico</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>dispatcher</servlet-name>
        <url-pattern>*.do</url-pattern>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <filter>
        <filter-name>SpringCharacterEncodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <async-supported>true</async-supported>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>SpringCharacterEncodingFilter</filter-name>
        <url-pattern>*.do</url-pattern>
        <url-pattern>/socket/*</url-pattern>
    </filter-mapping>
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>
    
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>
    <!--<error-page>-->
        <!--<error-code>500</error-code>-->
        <!--<location>/WEB-INF/pages/modules/error/500.jsp</location>-->
    <!--</error-page>-->
    <!--<error-page>-->
        <!--<error-code>404</error-code>-->
        <!--<location>/WEB-INF/pages/modules/error/404.jsp</location>-->
    <!--</error-page>-->
    <distributable />
</web-app>
