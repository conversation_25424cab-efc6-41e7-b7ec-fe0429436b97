<%@ page import="org.apache.commons.lang3.StringUtils" %>
<%@ page import="cn.sphd.miners.common.utils.CookieUtils" %>
<%@ page import="cn.sphd.miners.common.utils.GetLocalIPUtils" %>
<%--
  Created by IntelliJ IDEA.
  User: rose
  Date: 2016/12/16
  Time: 11:36
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%--<%= System.getProperty("BaseUrl") %>--%>

<%--兼容IE9--%>
<%--<!--[if lt IE 9]>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/respond.min.js"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/excanvas.min.js"></script>
<![endif]-->--%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/jquery.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/menu/jquery.cookie.min.js?v=SVN_REVISION" type="text/javascript"></script>
<%-- kendo插件 --%>
<% if("true".equalsIgnoreCase(CookieUtils.getCookie(request,"kendoCDN"))){ %>
<script defer src='https://kendo.cdn.telerik.com/2019.2.619/js/kendo.all.min.js'></script>
<% } else { %>
<script defer src='<%=System.getProperty("BaseUrl")%>/assets/kendo/js/kendo.all.min.js'></script>
<% }%>
<%-- 验证kendo插件 --%>
<script type="text/javascript">
$(function () {
    if (typeof kendo == 'undefined') {
        $('<link>').attr({ rel: 'stylesheet',type: 'text/css',href: '<%=System.getProperty("BaseUrl")%>/assets/kendo/styles/kendo.common-material.min.css'}).appendTo("head")
        $('<link>').attr({ rel: 'stylesheet',type: 'text/css',href: '<%=System.getProperty("BaseUrl")%>/assets/kendo/styles/kendo.material.min.css'}).appendTo("head")
        $('<link>').attr({ rel: 'stylesheet',type: 'text/css',href: '<%=System.getProperty("BaseUrl")%>/assets/kendo/styles/kendo.material.mobile.min.css'}).appendTo("head")
        <%--$.getScript('<%=System.getProperty("BaseUrl")%>/assets/kendo/js/kendo.all.min.js', function(){ console.log('download local！') })--%>
        $.ajax({dataType:"script",url:'<%=System.getProperty("BaseUrl")%>/assets/kendo/js/kendo.all.min.js',success:function(){console.log('Local kendo downloaded！')},beforeSend:function(){},error:function(){}})
    }
})
</script>
<script src="<%=System.getProperty("BaseUrl")%>/script/base64/base64.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/auth.js?v=SVN_REVISION" type="text/javascript"></script>
<%-- 加载系统及用户数据 --%>
<script>
    <%=StringUtils.isNotEmpty(response.getHeader("token"))?"auth.saveToken('"+response.getHeader("token")+"')":""%>
    <%=StringUtils.isNotEmpty(response.getHeader("user"))?"localStorage.setItem('user', decodeURIComponent('"+response.getHeader("user")+"'))":""%>
    if(localStorage.getItem('token') === null) {
        location.href = '<%=System.getProperty("BaseUrl")%>/sys/logout.do';
    } else if(localStorage.getItem('user') === null) {
        location.href = '<%=System.getProperty("BaseUrl")%>/sys/goBack.do';
    }
    <%=StringUtils.isNotEmpty(response.getHeader("org"))?"localStorage.setItem('org', decodeURIComponent('"+response.getHeader("org")+"'))":""%>
    $.webRoot = '<%= System.getProperty("BaseUrl") %>';//站点跟目录前缀
    $.ow365url = '<%= request.getAttribute("ow365url") %>';//ow365预览前缀
    $.uploadUrl = '<%= request.getAttribute("uploadUrl") %>';//同域下载前缀
    $.fileUrl = '<%= request.getAttribute("fileUrl") %>';//文件服务器下载前缀
    console.log('$.webRoot = ', $.webRoot);
    console.log('$.ow365url = ', $.ow365url);
    console.log('$.uploadUrl = ', $.uploadUrl);
    console.log('$.fileUrl = ', $.fileUrl);
    flashMenuBadge();
    function flashMenuBadge() {
        if(location.pathname==='/sys/sureLogin.do' || (localStorage.getItem('mainMenu') && localStorage.getItem('messageMenu') && localStorage.getItem('userBadgeNumbers') && localStorage.getItem('rolePrincipals')|| false ) === false ) {
            $.ajax({
                "url":$.webRoot+"/sys/getAllMenu.do",
                type:"post",
                dataType:"json",
                async:false,
                success:function(res){
                    var data = res.data;
                    localStorage.setItem('mainMenu', data.mainMenu);
                    localStorage.setItem('messageMenu', data.messageMenu);
                    localStorage.setItem('userBadgeNumbers', data.userBadgeNumbers);
                    localStorage.setItem('rolePrincipals', JSON.stringify(data.rolePrincipals));
                }
            });
        } else {
            $.ajax({
                "url": $.webRoot + "/sys/getUserBadgeNumbers.do",
                type: "post",
                dataType: "json",
                async: false,
                success: function (res) {
                    var data = res.data;
                    localStorage.setItem('userBadgeNumbers', data);
                }
            });
        }
    }
</script>
<%--websockes推送方法--%>
<script src="<%=System.getProperty("BaseUrl")%>/script/websockes/sockjs.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/websockes/stomp.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/websockes/sphd.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/jquery.timers-1.2.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/bootstrap/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js?v=SVN_REVISION" type="text/javascript"></script>

<script src="<%=System.getProperty("BaseUrl")%>/assets/global/scripts/app.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/layouts/layout/scripts/layout.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/select2-master/dist/js/select2.full.js?v=SVN_REVISION"></script>
<%--<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/cropper-master/dist/cropper.js"></script>--%>
<%--弹框能够自动消失的提示插件（用在成功操作时）--%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/layer/layer.js?v=SVN_REVISION"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/labelauty/jquery-labelauty.js?v=SVN_REVISION"></script>

<%--分页插件--%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/setPage/setPage.js?v=SVN_REVISION" type="text/javascript"></script>
<%--异步图片--%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/ajaxPicture/ajaxPic.js?v=SVN_REVISION" type="text/javascript"></script>
<%--日历插件--%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/laydate/laydate/laydate.js?v=SVN_REVISION" type="text/javascript"></script>
<%--弹框--%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/bounce/bounce.js?v=SVN_REVISION" type="text/javascript"></script>
<%--表单验证--%>
<script src="<%=System.getProperty("BaseUrl")%>/script/testForm.js?v=SVN_REVISION" type="text/javascript"></script>


<%--加载导航树，包含浮窗控件--%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/menu/pageMenus.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/menu/md_main.js?v=SVN_REVISION" type="text/javascript"></script>

<%-- 公用的方法 --%>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/common.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/downLoadFile/downLoadFile.js?v=SVN_REVISION" type="text/javascript"></script>



<%-- 赵应的window.open --%>
<script src="<%=System.getProperty("BaseUrl")%>/script/function.js?v=SVN_REVISION"></script>
<%--框架样式的控件 --%>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/green.js?v=SVN_REVISION"></script>
<%--以前用的时间插件--%>
<%--<script type="text/javascript" src="/My97DatePicker/WdatePicker.js"></script>--%>
<%-- 上传文件的插件 --%>
<script src="<%=System.getProperty("BaseUrl")%>/script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>
<%-- 时间格式化的插件 --%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/moment/moment.js?v=SVN_REVISION" type="text/javascript"></script>

<%-- md5 加密 --%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/md5/md5.min.js?v=SVN_REVISION"></script>
<%--修改账号-lyt--%>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/contentHeader.js?v=SVN_REVISION"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/seleniumUpdateId.js?v=SVN_REVISION"></script>
<%
    String hello = "Title";
    String ip= GetLocalIPUtils.getServerIp();
    int index=ip.lastIndexOf(".");
    if(index>=0) {
        hello=ip.substring(index+1);
    }
%>
<!-- <%=hello%> -->