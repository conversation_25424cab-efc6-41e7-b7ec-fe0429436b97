<%--
  Created by IntelliJ IDEA.
  User: rose
  Date: 2016/12/20
  Time: 16:21
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>

<!--[if lt IE 9]>
<script src="../assets/global/plugins/respond.min.js?v=SVN_REVISION"></script>
<script src="../../assets/global/plugins/excanvas.min.js?v=SVN_REVISION"></script>
<![endif]-->
<script src="../assets/global/plugins/jquery.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/global/plugins/bootstrap/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/global/plugins/jquery.cokie.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/global/scripts/metronic.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/admin/layout/scripts/layout.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/admin/layout/scripts/quick-sidebar.js?v=SVN_REVISION" type="text/javascript"></script>

<!-- <script src="../../assets/global/plugins/jquery-migrate.min.js" type="text/javascript"></script> -->
<!-- <script src="../../assets/global/plugins/jquery-ui/jquery-ui.min.js" type="text/javascript"></script> -->
<!-- <script src="../../assets/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js" type="text/javascript"></script> -->
<!-- <script src="../../assets/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script> -->
<!-- <script src="../../assets/global/plugins/jquery.blockui.min.js" type="text/javascript"></script> -->
<!-- <script src="../../assets/global/plugins/uniform/jquery.uniform.min.js" type="text/javascript"></script> -->
<!-- <script src="../../assets/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script> -->

<!-- <script src="../../assets/admin/layout/scripts/demo.js" type="text/javascript"></script> -->

<script>
    jQuery(document).ready(function() {
        Metronic.init(); // init metronic core components
        Layout.init(); // init current layout
        QuickSidebar.init(); // init quick sidebar
        // Demo.init(); // init demo features
    });
</script>
<script>
    // (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    // (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
    // m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    // })(window,document,'script','../www.google-analytics.com/analytics.js','ga');
    // ga('create', 'UA-37564768-1', 'keenthemes.com');
    // ga('send', 'pageview');
</script>