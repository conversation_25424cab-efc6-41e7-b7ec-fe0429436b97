<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%--<form autocomplete="off" onsubmit="return false">--%>
<%--超时重新登录--%>
<div class="common_pop" style="z-index: 99998">
    <div class="bonceContainer bounce-green" id="reLogin">
        <div class="bonceHead">
            <span class="pastTip_msg">请输入密码</span>
            <a class="bounce_close" onclick="$('.accountPsd').val('');location.href='../sys/logout.do'"></a>
        </div>
        <div class="bonceCon">
            <div class="pass-main">
                <div class="pass-w">
                    <span class="pass-label">账号</span>
                    <span class="accMobile pass-con"></span>
                </div>
                <div class="pass-w">
                    <span class="pass-label">密码</span>
                    <input class="accountPsd pass-con" type="password" placeholder="请输入密码" value="" onkeydown="passwordKeydown(event);" autocomplete="new-password" style="width:148px;"/>
                    <p class="errorTip hd">登录密码错误！</p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="$('.accountPsd').val('');location.href='../sys/logout.do'">取消</span>
            <button class="ty-btn ty-circle-5 ty-btn-big ty-btn-green" onclick="checkPassword($(this));return false;">确定</button>
        </div>
    </div>
</div>
<div class="common_bounce">
    <div class="bonceContainer bounce-blue" id="receiveTip">
        <div class="bonceHead">
            <span> ！！提示</span>
            <a class="bounce_close" onclick="common_bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>您能否收到手机号<span class="orgAccount"></span>的短信验证码？</p>
            </div>
            <div class="text-center">
                <div class="ty-radio">
                    <input type="radio" name="state" value="1" id="state1">
                    <label for="state1"></label> 能
                </div>
                <div class="ty-radio" style="margin-left: 32px">
                    <input type="radio" name="state" value="0" id="state2">
                    <label for="state2"></label> 不能
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big" onclick="common_bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="receiveTipNextStep()">下一步</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="changeUser">
        <div class="bonceHead">
            <span class="bounce_title">修改登录账号</span>
            <a class="bounce_close" onclick="common_bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="SMSVerification form">
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">账号</span>
                        <input class="SMS_phone" name="user" type="text" disabled style="flex: auto">
                    </div>
                </div>
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">验证码</span>
                        <input type="text" name="code" placeholder="请输入验证码" style="flex: auto">
                        <button class="text-btn text-btn-green getCodeBtn" onclick="sendCode_oldPhone($(this))">获取验证码</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big" onclick="common_bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="checkCode_oldPhone()">下一步</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="editNewAccount">
        <div class="bonceHead">
            <span>修改登录账号</span>
            <a class="bounce_close" onclick="common_bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="SMSVerification form">
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">新账号</span>
                        <input class="SMS_phone" name="user" type="text" placeholder="请输入新账号" style="flex: auto" onchange="newMobileCheck($(this))">
                    </div>
                </div>
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">验证码</span>
                        <input type="text" name="code" placeholder="请输入验证码" style="flex: auto">
                        <button class="text-btn text-btn-green getCodeBtn" onclick="sendCode_newPhone($(this))">获取验证码</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="common_bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="editNewAccount_submit()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="editNewPassword">
        <div class="bonceHead">
            <span>修改登录账号</span>
            <a class="bounce_close" onclick="common_bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="SMSVerification form">
                <div class="item">
                    请您设置密码，以便登录更快捷。
                </div>
                <div class="item ty-color-orange" style="margin-top: 4px">
                    忘记密码时，您可使用验证码登录。
                </div>
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">密码</span>
                        <input name="passW" type="text" placeholder="8-16位字符" style="flex: auto">
                    </div>
                </div>
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">确认密码</span>
                        <input name="passW2" type="text" placeholder="8-16位字符" style="flex: auto">
                    </div>
                </div>
                <div class="item">
                    <small class="ty-color-blue">注：密码需为8-16位数字与字符的组合，不能含空格。</small>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="common_bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="editNewPassword_submit()">设置完毕，重新登陆</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="changePassword">
        <div class="bonceHead">
            <span class="bounce_title">修改密码</span>
            <a class="bounce_close" onclick="common_bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="SMSVerification form">
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">账号</span>
                        <input class="SMS_phone" name="user" type="text" disabled style="flex: auto">
                    </div>
                </div>
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">验证码</span>
                        <input type="text" name="code" placeholder="请输入验证码" style="flex: auto">
                        <button class="text-btn text-btn-green getCodeBtn" onclick="sendCode_changePassword($(this))">获取验证码</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big" onclick="common_bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="checkCode_phone()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="changePassword_set">
        <div class="bonceHead">
            <span>修改密码</span>
            <a class="bounce_close" onclick="common_bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="SMSVerification form">
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">密码</span>
                        <input name="passW" type="text" placeholder="8-16位字符" style="flex: auto">
                    </div>
                </div>
                <div class="item">
                    <div class="input_avatar">
                        <span class="input_title">确认密码</span>
                        <input name="passW2" type="text" placeholder="8-16位字符" style="flex: auto">
                    </div>
                </div>
                <div class="item">
                    <small class="ty-color-blue">注：密码需为8-16位，必须包括数宇和英文宇母，英文字母分大小写。</small>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="common_bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="changePassword_set_submit()">设置完毕，重新登陆</span>
        </div>
    </div>
    <%--查重提示--%>
    <div class="bonceContainer bounce-red" id="checkTip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="common_bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p>修改成功</p>
            <p>再登录系统，请使用 <span class="phone"></span> 账号！</p>
            <p>退出中……</p>
        </div>
        <div class="bonceFoot">
            <div class="canAllow">
                <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" id="tjSure" onclick="common_bounce.cancel()">确定</span>
            </div>
        </div>
    </div>
</div>
<div class="common_bounce1">
    <div class="bonceContainer bounce-blue" id="tips">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="common_bounce1.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg" style="text-align: center; margin-top: 16px;"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="common_bounce1.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="tipIKnow">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="common_bounce1.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="common_bounce1.cancel()">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="unableTip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="common_bounce1.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="unableTip_ms">
                <p style="text-indent: 2em;">如想修改登录账号又无法获取相应手机号的短信验证码，可请AAABBB为您修改。</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="common_bounce1.cancel();common_bounce.cancel()">我知道了</span>
        </div>
    </div>

</div>
<%-- 复杂的详情页 内嵌iframe --%>
<div id="somethingItemDetailsPage" onclick="$('#somethingItemDetailsPage').css('display','none');event.stopPropagation();">
    <iframe src="" onclick="event.stopPropagation();"></iframe>
</div>

<%--侧面导航树--%>
<div class="page-header navbar navbar-fixed-top">
    <div class="page-header-inner ">
        <%--logo--%>
        <div class="page-logo">
            <a class="logo-default minersLogo"><span class="green_logo">W</span>onderss</a>
            <div class="menu-toggler sidebar-toggler"></div>
        </div>
        <%--左侧菜单--%>
        <div class="left-menu">
            <ul class="nav navbar-nav">
                <li class="navbar-navItem navbar-nav-home" onclick="location.href='../sys/index.do'">
                    <i class="fa fa-home" title="我的桌面"></i>
                    <%--<a class="fa fa-home goHome" title="我的桌面" href="<%=System.getProperty("BaseUrl")%>/sys/index.do"></a>--%>
                </li>
            </ul>
        </div>
        <%--中间多重身份--%>
        <div class="middle-menu">
            <div class="principal_avatar">
                <span class="principal_recent_tip">您的当前身份为<span class="principal_recent"></span></span>
                <span class="principal_tip">其他身份</span>
                <ul class="principal_list"></ul>
            </div>
        </div>
        <%--右侧菜单--%>
        <div class="top-menu">
            <ul class="nav navbar-nav pull-right">
                <%--讨论区通知--%>
                <li class="navbar-navItem navbar-nav-blog" id="discussion" onclick="location.href='../forum/discussionIndex.do'" style="display: none">
                    <i class="fa fa-commenting navbar-navItem-title"></i>
                    <span class="badge badge-default" id="discussNum"></span>
                </li>
                <%-- 原来的 消息通知--%>
                <li class="navbar-navItem navbar-nav-msg" onclick="goMessage()">
                    <i class="fa fa-envelope navbar-navItem-title"></i>
                    <span class="badge badge-default" id="msgNum2"></span>
                </li>
                <%--消息通知--%>
                <li class="navbar-navItem navbar-nav-msg" id="faEnvelope">
                    <i class="fa fa-pencil navbar-navItem-title"></i>
                    <span class="badge badge-default" id="msgNum"></span>
                    <%-- vue浮窗 --%>
                    <div class="bounceFloating">
                        <div class="floatingContainer">
                            <iframe></iframe>
                        </div>
                    </div>
                </li>
                <%--用户信息--%>
                <li class="navbar-navItem" id="peUserControl">
                    <div data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                        <img id="loginUserIMG" class="img-circle" src="" onerror="this.src='../assets/images/avatar/avatar_default_small.png'" />
                        <span class="username username-hide-on-mobile navbar-navItem-title" id="loginUserNAME" ></span>
                        <span style="display:none; " id="loginUserType" ></span>
                        <span class="username username-hide-on-mobile" id="orgName" style="width:150px; overflow: hidden;"></span>
                        <i class="fa fa-angle-down" style="color:#ccc;"></i>
                        <div class="hd" id="loginUser"></div>
                    </div>
                    <div style="display: none" id="peImgFile" onclick="event.stopPropagation();">

                        <%-- <input type="hidden" name="num" class="editFilePe" value="0"/>
                        <form action="../general/updateUserImage.do" id="updateUserImageForm" enctype="multipart/form-data">--%>
                            <%--<input name="imgFile" id="peImgFile" type="file" onchange="setImagePreviewPe();" style="display:none;" />--%>
                            <%--<input type="hidden" name="userId" value="" id="peImgFileUID"/>--%>
                        <%--</form>--%>
                    </div>
                    <ul class="dropdown-menu dropdown-menu-right dropdown-menu-default userControl" >
                        <div class="control1">
                            <li class="user_info">
                                <div class="logo-avatar">
                                    <img onmouseenter="changeImgBtn(1)" class="img-circle" src="" id="loginUserIMG2" onerror="this.src='../assets/images/avatar/avatar_default.png'" />
                                    <span class="changeImgBtn">更换头像</span>
                                    <div onmouseout="changeImgBtn()" onclick="upPeImg(event)" class="changeImgBtn"></div>
                                </div>
                                <div class="u1">
                                    <div id="loginUserNAME2"></div>
                                    <div><span id="loginUserPhone"></span><i class="fa fa-edit" onclick="updateUserAccount()"></i></div>
                                </div>
                            </li>
                            <li class="litLi">
                                <p onclick="changePassword()"><i class="fa fa-edit"></i>修改密码 <i class="fa fa-angle-right ty-right"></i></p>
                            </li>
<%--                            <li class="litLi" style="display: none;">--%>
<%--                                <p onclick="showBindWx()"><i class="fa fa-edit"></i>绑定微信<i class="fa fa-angle-right ty-right"></i></p>--%>
<%--                            </li>--%>
<%--                            <li class="litLi">--%>
<%--                                <div>--%>
<%--                                    <a href="<%=System.getProperty("BaseUrl")%>/sys/weChatBind.do"><i class="fa fa-flag"></i>微信跳页面</a>--%>
<%--                                </div>--%>
<%--                            </li>--%>
                            <li class="litLi" >
                                <p onclick="g2(event , 2)"><i class="fa fa-exclamation-circle"></i>关于 <i class="fa fa-angle-right ty-right"></i></p>
                            </li>
                            <li class="bigLi">
                                <div>
                                    <a data-info="weixinBind" onclick="aHrefNoMenu($(this))"><i class="fa fa-weixin"></i>微信绑定 <span id="bdWx"  class="ty-right "></span></a>
                                    <a href="<%=System.getProperty("BaseUrl")%>/sys/goBack.do"><i class="fa fa-flag"></i>切换机构</a>
                                    <a href="<%=System.getProperty("BaseUrl")%>/sys/logout.do"><i class="fa fa-mail-reply"></i>退出 </a>
                                </div>
                            </li>
                        </div>
                        <div class="control2">
                            <li onclick="g2(event , 1)" class="ttlLi">关于 <i class="fa fa-angle-left ty-left"></i></li>
                            <li class="bigLi">
                                <div>
                                    <%--<a href="<%=System.getProperty("BaseUrl")%>/about/scanDownload.do"><i class="fa fa-download"></i>扫描下载</a>--%>
                                </div>
                            </li>
                        </div>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<%-- 修改密码弹窗 --%>
<div id="dialog"></div>
<input type="hidden" id="peRes">
<%-- ajax Loading --%>
<div class="zhe_AjaxPic">
    <div class="zhe_circleCon">
        <h3 class="zhe_loadingTip">正在加载 . . . </h3>
    </div>
</div>