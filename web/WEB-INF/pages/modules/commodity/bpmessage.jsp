<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/commodity/goods.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/commodity/basic.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>
<style>
</style>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">

    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align:center"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue " onclick="bounce.cancel();">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="bpcheck" style="width: 800px">
        <div class="bonceHead">
            <span id="bpmc"></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-left:20px;margin-top:10px;">商品基本信息</div>
            <table class="ty-table ty-table-control">
                <tr>
                    <td width="13%">商品代号</td>
                    <td width="13%">商品名称</td>
                    <td width="13%">产品图号</td>
                    <td width="13%">产品名称</td>
                    <td width="12%">产品净重</td>
                    <td width="8%">单位</td>
                    <td width="8%">型号</td>
                    <td width="8%">规格</td>
                    <td width="12%">最低库存</td>
                </tr>
                <tr>
                    <td id="outerth" width="13%"></td>
                    <td id="outermc" width="13%"></td>
                    <td id="innerth" width="13%"></td>
                    <td id="innermc" width="13%"></td>
                    <td id="weight" width="12%"></td>
                    <td id="unit" width="8%"></td>
                    <td id="xh" width="8%"></td>
                    <td id="specifications" width="8%"></td>
                    <td id="stock" width="12%"></td>
                </tr>
            </table>
            <div class="bzBigCon">
                <%--最小包装信息--%>
                <div class="rank_hide1" onclick="stopIntervalbp(1)">
                    <div style="margin-left:20px;margin-top:10px;">
                        <span class="levelname">最小包装信息</span>
                    </div>
                    <div style="margin-left:40px;margin-top:20px;">
                        <div class="bzInput">
                            <span>每个包装内的商品数量 </span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c nonull dweigth" id="bp_amount1" placeholder="请录入数量" onKeyup = "clearNoNum( this )" onchange="changeColor(1)" onfocus="stopIntervalbp(1)">
                            <span id="check_amount1"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装方式</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c nonull" id="bp_method1"  placeholder="请录入包装方式" onchange="changeColor(1)" onfocus="stopIntervalbp(1)">
                            <span id="check_method1"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装材料名称<span class="bp_hide" style="color:red">*</span></span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c" id="bp_cname1" placeholder="请录入包装材料名称" onchange="changeColor(1)" onfocus="stopIntervalbp(1)">
                            <span id="check_cname1"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装材料代号</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c cateid" id="bp_id1" placeholder="请录入包装材料代号"  onchange="changeColor(1)" onclick="startBpmat($(this),event,1)">
                            <input type="text" name="" id="add_cusId1" style="display:none;"  >
                            <span id="check_id1"></span>
                            <div id="bpCon1" class="customerCon" >
                            </div>
                        </div>
                        <div class="bzInput">
                            <span>包装物重量</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c backWeight" id="bp_weight1" placeholder="请录入包装物重量" onKeyup = "clearNoNum( this )" onfocus="stopIntervalbp(1)">
                            <span id="check_netweight1"></span>
                            <span class="sameunit"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装后每包总重量</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c txt-totalweight" id="bp_toweight1" readonly onfocus="stopIntervalbp(1)">
                            <span id="check_taweight1"></span>
                            <span class="sameunit"></span>
                        </div>
                    </div>
                    <div style="clear:both;"></div>
                    <%--点击编辑时出现的按钮--%>
                    <div style="float:right;">
                        <span class="panel-4 ty-btn ty-btn-blue ty-btn-big ty-circle-5 bp_hide" id="addbp1" onclick="addrank(this,1)" disabled="">增加</span>
                    </div>
                    <div style="clear:both;"></div>
                </div>
                <%--二级包装信息--%>
                <div class="rank_hide2 level_hide" onclick="stopIntervalbp(2)">
                    <div style="margin-left:20px;margin-top:10px;">
                        <span class="levelname">二级包装信息</span>
                    </div>
                    <div style="margin-left:40px;margin-top:20px;">
                        <div class="bzInput">
                            <span>每个包装内的商品数量 </span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c nonull dweigth" id="bp_amount2" placeholder="请录入数量" onKeyup = "clearNoNum( this )" onchange="changeColor(2)" onfocus="stopIntervalbp(2)">
                            <span id="check_amount2"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装方式</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c nonull" id="bp_method2"  placeholder="请录入包装方式" onchange="changeColor(2)" onfocus="stopIntervalbp(2)">
                            <span id="check_method2"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装材料名称</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c" id="bp_cname2" placeholder="请录入包装材料名称" onchange="changeColor(2)" onfocus="stopIntervalbp(2)">
                            <span id="check_cname2"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装材料代号</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c cateid" id="bp_id2" placeholder="请录入包装材料代号"  onchange="changeColor(2)"  onclick="startBpmat($(this),event,2)">
                            <span id="check_id2"></span>
                            <div id="bpCon2" class="customerCon"  ></div>
                        </div>
                        <div class="bzInput">
                            <span>包装物重量</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c backWeight" id="bp_weight2" placeholder="请录入包装物重量" onKeyup = "clearNoNum( this )" onfocus="stopIntervalbp(2)">
                            <span id="check_netweight2"></span>
                            <span class="sameunit"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装后每包总重量</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c" id="bp_toweight2" readonly onfocus="stopIntervalbp(2)">
                            <span id="check_taweight2"></span>
                            <span class="sameunit"></span>
                        </div>
                    </div>
                    <div style="clear:both;"></div>
                    <%--点击编辑时出现的按钮--%>
                    <div style="float:right;">
                        <span class="panel-4 ty-btn ty-btn-blue ty-btn-big ty-circle-5 bp_hide" id="addbp2" onclick="addrank(this,2)">增加</span>
                        <span class="panel-4 ty-btn ty-btn-big ty-circle-5 bp_hide" id="delbp2" onclick="dele(2)">取消此级包装方式</span>
                    </div>
                    <div style="clear:both;"></div>
                </div>
                <%--三级包装信息--%>
                <div class="rank_hide3 level_hide" onclick="stopIntervalbp(3)">
                    <div style="margin-left:20px;margin-top:10px;">
                        <span class="levelname">三级包装信息</span>
                    </div>
                    <div style="margin-left:40px;margin-top:20px;">
                        <div class="bzInput">
                            <span>每个包装内的商品数量 </span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c nonull dweigth" id="bp_amount3" placeholder="请录入数量" onKeyup = "clearNoNum( this )" onchange="changeColor(3)" onfocus="stopIntervalbp(3)">
                            <span id="check_amount3"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装方式</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c nonull" id="bp_method3"  placeholder="请录入包装方式" onchange="changeColor(3)" onfocus="stopIntervalbp(3)">
                            <span id="check_method3"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装材料名称</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c" id="bp_cname3" placeholder="请录入包装材料名称" onchange="changeColor(3)" onfocus="stopIntervalbp(3)">
                            <span id="check_cname3"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装材料代号</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c cateid" onchange="changeColor(3)"  id="bp_id3" placeholder="请录入包装材料代号" onclick="startBpmat($(this),event,3)">
                            <span id="check_id3"></span>
                            <div id="bpCon3" class="customerCon" ></div>
                        </div>
                        <div class="bzInput">
                            <span>包装物重量</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c backWeight" id="bp_weight3" placeholder="请录入包装物重量" onKeyup = "clearNoNum( this )" onfocus="stopIntervalbp(3)">
                            <span id="check_netweight3"></span>
                            <span class="sameunit"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装后每包总重量</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c" id="bp_toweight3" readonly onfocus="stopIntervalbp(3)">
                            <span id="check_taweight3"></span>
                            <span class="sameunit"></span>
                        </div>
                    </div>
                    <div style="clear:both;"></div>
                    <%--点击编辑时出现的按钮--%>
                    <div style="float:right;">
                        <span class="panel-4 ty-btn ty-btn-blue ty-btn-big ty-circle-5 bp_hide" id="addbp3" onclick="addrank(this,3)">增加</span>
                        <span class="panel-4 ty-btn ty-btn-big ty-circle-5 bp_hide" id="delbp3" onclick="dele(3)">取消此级包装方式</span>
                    </div>
                    <div style="clear:both;"></div>
                </div>
                <%--四级包装信息--%>
                <div class="rank_hide4 level_hide" onclick="stopIntervalbp(4)">
                    <div style="margin-left:20px;margin-top:10px;">
                        <span class="levelname">四级包装信息</span>
                    </div>
                    <div style="margin-left:40px;margin-top:20px;">
                        <div class="bzInput">
                            <span>每个包装内的商品数量 </span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c nonull dweigth" id="bp_amount4" placeholder="请录入数量" onKeyup = "clearNoNum( this )" onchange="changeColor(4)" onfocus="stopIntervalbp(4)">
                            <span id="check_amount4"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装方式</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c nonull" id="bp_method4"  placeholder="请录入包装方式" onchange="changeColor(4)" onfocus="stopIntervalbp(4)">
                            <span id="check_method4"></span>

                        </div>
                        <div class="bzInput">
                            <span>包装材料名称</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c" id="bp_cname4" onchange="changeColor(4)" placeholder="请录入包装材料名称" onfocus="stopIntervalbp(4)">
                            <span id="check_cname4"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装材料代号</span>
                            <span class="bp_hide" style="color:red">*</span>
                            <br>
                            <input type="text"class=" ty-inputSelect bp_hide Col_c cateid" onchange="changeColor(4)" id="bp_id4" placeholder="请录入包装材料代号"  onclick="startBpmat($(this),event,4)">
                            <span id="check_id4"></span>
                            <div id="bpCon4" class="customerCon"  ></div>
                        </div>
                        <div class="bzInput">
                            <span>包装物重量</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c backWeight" id="bp_weight4" placeholder="请录入包装物重量" onKeyup = "clearNoNum( this )" onfocus="stopIntervalbp(4)">
                            <span id="check_netweight4"></span>
                            <span class="sameunit"></span>
                        </div>
                        <div class="bzInput">
                            <span>包装后每包总重量</span>
                            <br>
                            <input type="text" class=" ty-inputSelect bp_hide Col_c" id="bp_toweight4" readonly onfocus="stopIntervalbp(4)">
                            <span id="check_taweight4"></span>
                            <span class="sameunit"></span>
                        </div>
                    </div>
                    <div style="clear:both;"></div>
                    <%--点击编辑时出现的按钮--%>
                    <div style="float:right;">
                        <span class="panel-4 ty-btn ty-btn-big ty-circle-5 bp_hide" id="delbp4" onclick="dele(4)">取消此级包装方式</span>
                    </div>
                    <div style="clear:both;"></div>
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <%--点击查看时的页面按钮--%>
            <span class="panel-4 ty-btn ty-btn-big ty-circle-5 check_hide" id="del" onclick="bounce.cancel()">取消</span>
            <%--点击编辑时出现的按钮--%>
            <span class="panel-4 ty-btn ty-btn-blue ty-btn-big ty-circle-5 bp_hide" id="addsub" onclick="bounce_OK(this)" >提交</span>
            <span class="panel-4 ty-btn ty-btn-big ty-circle-5 bp_hide" id="bounce_over1" onclick="bounce.cancel   ()">取消</span>
        </div>
    </div>

</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>包装信息</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="Border-big">
                    <div class="opinionCon">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td width="8%" rowspan="2">序号</td>
                                <td width="8%" rowspan="2">商品代号</td>
                                <td width="8%" rowspan="2">商品名称</td>
                                <td width="20%" colspan="2">最小包装信息</td>
                                <td width="20%" colspan="2">二级包装信息</td>
                                <td width="20%" colspan="2">三级包装信息</td>
                                <td width="16%" rowspan="2">操作</td>
                            </tr>
                            <tr>
                                <td width="8%">包装方式</td>
                                <td width="12%">每个包装内的数量</td>
                                <td width="8%">包装方式</td>
                                <td width="12%">每个包装内的数量</td>
                                <td width="8%">包装方式</td>
                                <td width="12%">每个包装内的数量</td>
                                <%--<td></td>--%>
                            </tr>
                            </thead>
                            <%--放包装信息的主列表--%>
                            <tbody id="pack_tbl">

                            </tbody>
                        </table>
                        <div id="ye_goodsprocess"></div>
                    </div>
                    <div class="clr"></div>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/commodity/bpmessage.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>
