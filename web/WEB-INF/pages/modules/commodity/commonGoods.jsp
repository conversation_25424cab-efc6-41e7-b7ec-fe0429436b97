<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/commodity/commodityCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/commodity/basic.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%--商品录入--%>
    <div class="bonceContainer bounce-blue" id="addCommodity" style="width: 980px;">
        <div class="bonceHead bounce-blue">
            <span>录入已销售过的商品</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon">
            <div class="modInput">
                <div class="clear">
                    <div class="modTip ty-left">
                        <p>您正在向“<span class="ty-color-red">待分类</span>”下录入商品！</p>
                        <p class="ty-color-blue sold" style="font-size: 12px;">注：已销售过的商品录入后，系统将提示成品库的库管员<span class="ty-color-red">填写初始库存。</span></p>
                        <p class="ty-color-blue noSold" style="font-size: 12px;">注：未销售过的商品录入后，其<span class="ty-color-red">初始库存默认为零！</span></p>
                    </div>
                    <div class="ty-right importSect">
                        <span class="nodeBtn" id="importBtn" data-fun="importOpen">批量导入</span>
                    </div>
                </div>
                <div class="line"></div>
                <table class="pannelTab">
                    <tr>
                        <td>
                            <div>
                                <i class="red">*</i>商品代号
                            </div>

                            <input class="ty-inputText" value="" placeholder="请录入" name="outerSn" require/>
                            <i class="fa fa-times clearInputVal"></i>
                        </td>
                        <td>
                            <div>
                                <i class="red">*</i>商品名称
                            </div>
                            <input class="ty-inputText" value="" placeholder="请录入"name="outerName" require/>
                            <i class="fa fa-times clearInputVal"></i>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>规格</div>
                            <input class="ty-inputText" value="" placeholder="请录入" name="specifications"/>
                        </td>
                        <td>
                            <div>型号</div>
                            <input class="ty-inputText" value="" placeholder="请录入" name="model"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" class="firstTimeCCon">
                            <span>成为公司通用型商品的时间</span>
                            <span class="marL30 " id="firstTimeCC" data-type="0">
                              <span class="radiop cc1" onclick="toggefirstTime(1,'')"><i class="fa fa-circle-o" ></i>今年</span>
                              <span class="firMonthcon">
                                 <input type="text" id="firMonth" onchange="nullNextN()" class="ty-inputText" style="width: 100px;" />
                                  <span class="radiop ccN" onclick="toggefirstTimeN('')"><i class="fa fa-circle-o"></i>不确定月份</span>
                              </span>
                              <span class="radiop cc2" onclick="toggefirstTime(2,'')"><i class="fa fa-circle-o"></i>去年</span>
                              <span class="radiop cc3" onclick="toggefirstTime(3,'')"><i class="fa fa-circle-o"></i>更久之前</span>
                            </span>
                        </td>
                    </tr>
                </table>
                <div class="line"></div>
                <div class="ty-color-blue" style="margin:20px 0 0 32px;font-size: 12px;">注：录入订单时，需确认开票情况，并将引用此处单价。在此也可只录入参考单价。</div>
                <table class="pannelTab">
                    <tr>
                        <td class="invoice1">
                            <div>开增值税专用发票时的单价</div>
                            <div class="modItem-ss ty-left">
                                <p class="modItemTtl">
                                    税率
                                </p>
                                <select class="ty-inputSelect" id="addInvoiceSpecial" name="taxRate" onchange="reSetPrice()">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="modItem-s ty-left">
                                <p class="modItemTtl">
                                    不含税单价
                                </p>
                                <input class="ty-inputText" value="" placeholder="请录入" id="unitPriceNotax_add" name="unitPriceNotax" onkeyup="clearNoNumN(this,10);autoPrice(1);" />
                            </div>
                            <div class="modItem-s ty-right">
                                <p class="modItemTtl">
                                    含税单价
                                </p>
                                <input class="ty-inputText" value="" name="unitPrice" id="unitPrice_add" placeholder="请录入" onkeyup="clearNoNumN(this,10);autoPrice(2);" />
                            </div>
                        </td>
                        <td>
                            <div>开普通发票时的开票单价</div>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceInvoice" onkeyup="clearNoNumN(this,10)"/>
                        </td>

                    </tr>
                    <tr>
                        <td>
                            <div>不开发票时的单价</div>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNoinvoice" onkeyup="clearNoNumN(this,10)"/>
                        </td>
                        <td>
                            <div>参考单价</div>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceReference" onkeyup="clearNoNumN(this,10)"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <p class="modItemTtl">
                                <i class="red">*</i>计量单位
                                <span class="nodeBtn ty-right" data-fun="addUnit">新增</span>
                            </p>
                            <select class="ty-inputSelect" type="text" id="unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                            <input type="hidden" name="unit" id="add_unitName">
                        </td>
                        <td>
                            <p class="modItemTtl">
                                价格说明<span class="lenTip ty-right">0/100</span>
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="priceDesc" onkeyup="limitWord($(this), 100)"/>
                            <i class="fa fa-times clearInputVal"></i>
                        </td>
                    </tr>
                </table>
                <div class="line"></div>
                <table class="pannelTab">
                    <tr>
                        <td>
                            <p class="modItemTtl">
                                商品照片(最多可上传9张)
                                <span class="nodeBtn ty-right" id="filePic"></span>
                            </p>
                            <div class="modPics">
                                <div class="file-box filePicBox clear"><span class="inTip">请上传</span></div>
                                <div class="clearPicsBtn" onclick="clearPicsBtn($(this))">清除</div>
                            </div>
                        </td>
                        <td>
                            <p class="modItemTtl">
                                商品视频(可上传一个，且不可超过15秒)
                                <span class="nodeBtn ty-right" id="fileVedio"></span>
                            </p>
                            <div class="file-box fileVedioBox clear"><span class="inTip">请上传</span></div>

                        </td>
                    </tr>
                    <tr>
                        <td>
                            <p class="modItemTtl">
                                <i class="red">*</i>最低库存
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="minimumStock" onkeyup="testNumSize3(this)" require/>

                        </td>
                        <td>
                            <p class="modItemTtl">
                                商品说明<span class="lenTip ty-right">0/100</span>
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="memo" onkeyup="limitWord($(this), 100)"/>
                            <i class="fa fa-times clearInputVal"></i>

                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="chargeXhr($(this))">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addCommoditySure()">确定</span>
        </div>
    </div>
    <%--商品查看--%>
    <div class="bonceContainer bounce-blue" id="seeCommodity" style="width: 900px;">
        <div class="bonceHead bounce-blue">
            <span>商品查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="add-block ty-color-orange" id="stopDetails">
                <span id="stopName">XXX</span>已于<span id="stopDate">XXXX-XX-XX XX:XX:XX</span>将本商品“暂停销售”！</span>
            </div>
            <div class="main-nav">
                <span>基本信息</span>
                <div class="ty-right">
                    <span class="nodeBtn editBtn" onclick="updateCommodityBase(1)">修改</span>
                    <span class="nodeBtn" data-fun="baseRecord" data-cat="1">修改记录</span>
                </div>
            </div>
            <div>
                <table  class="ty-table" id="commodityBase">
                    <tbody>
                    <tr>
                        <td>商品代号</td>
                        <td colspan="3" need data-name="outerSn"></td>
                    </tr>
                    <tr>
                        <td width="30%">商品名称</td>
                        <td colspan="3" need data-name="outerName"></td>
                    </tr>
                    <tr>
                        <td width="12%">规格</td>
                        <td need data-name="specifications"></td>
                        <td width="12%">型号</td>
                        <td need data-name="model"></td>
                    </tr>
                    <tr>
                        <td colspan="4"> 成为公司通用型的时间：<span id="exclusiveTimeC"></span> </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="main-nav">
                <span>价格信息（以下价格有效期为 <span class="jiaBetween"></span>）</span>
                <div class="ty-right">
                    <span class="nodeBtn editBtn" onclick="showOtherPrice(1)" >其他价格</span>
                    <span class="nodeBtn editBtn" onclick="updatePrice(1)">修改</span>
                    <span class="nodeBtn" onclick="epLog()">修改记录</span>

                </div>
            </div>
            <div>
                <table  class="ty-table" >
                    <tbody>
                        <tr>
                           <td width="30%">开增值税专用发票时</td>
                           <td data-name="specialDes">
                           </td>
                       </tr>
                       <tr>
                           <td>开普通发票时</td>
                           <td data-name="generalDes">

                           </td>
                       </tr>
                       <tr>
                           <td>不开发票时</td>
                           <td data-name="noDes">
                           </td>
                       </tr>
                       <tr>
                           <td>参考单价</td>
                           <td data-name="referenceDes">
                           </td>
                       </tr>
                    </tbody>
                </table>
            </div>
            <div class="elemFlex">
                <span class="ttl">其他信息</span>
                <div>
                    <span class="nodeBtn editBtn" onclick="updateOtherData()">修改</span>
                    <span class="nodeBtn" data-fun="baseRecord" data-cat="2">修改记录</span>
                </div>
            </div>
            <div>
                <table  class="ty-table" id="seeInvoice1">
                    <tbody>
                    <tr>
                        <td>关联的产品</td>
                        <td>
                            <div class="ty-left"><span need data-name="innerSn"></span>&nbsp;&nbsp;<span need data-name="productName"></span></div>
                            <div class="ty-right nodeBtn" data-fun="relatedRecord">关联记录</div>
                        </td>
                    </tr>

                    <tr>
                        <td>计量单位</td>
                        <td need data-name="unit"></td>
                    </tr>
                    <tr>
                        <td>最低库存</td>
                        <td need data-name="minimumStock"></td>
                    </tr>
                    <tr>
                        <td>商品照片</td>
                        <td colspan="3" class="comPics"></td>
                    </tr>
                    <tr>
                        <td>商品视频</td>
                        <td colspan="3" class="comVideo"></td>
                    </tr>
                    <tr>
                        <td>商品说明<span class="lenTip"></span></td>
                        <td colspan="3" need data-name="memo">
                        </td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td need data-name="priceDesc"></td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="elemFlex">
                <span class="ttl">商品的两项开票资料</span>
            </div>
            <div>
                <table class="ty-table">
                    <tbody>
                    <tr>
                        <td>货物或应税劳务、服务名称</td>
                        <td>
                            <span need data-name="huoTwo"  id="nameInvoice"></span>
                            <div class="ty-right">
                                <span class="nodeBtn" style="margin-right: 20px;" data-fun="twoEdit" data-type="huo">编辑</span>
                                <span class="nodeBtn" data-fun="twoEditLog" data-type="huo">编辑记录</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>规格型号</td>
                        <td>
                            <span need data-name="modelTwo" id="glInvoice"></span>
                            <div class="ty-right">
                                <span class="nodeBtn" style="margin-right: 20px;" data-fun="twoEdit" data-type="model">编辑</span>
                                <span class="nodeBtn" data-fun="twoEditLog" data-type="model">编辑记录</span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="clear">
                <div class="operations">
                    <div><span class="oper">创建</span><span id="seeCreator"></span></div>
                    <div class="reLog">
                    </div>
                </div>
            </div>
            <div class="hd" id="inforStorage"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>


    <%-- 商品的两项开票资料 编辑 --%>
    <div class="bonceContainer bounce-blue" id="twoEdit">
        <div class="bonceHead">
            <span>编辑商品的开票资料</span>
            <a class="bounce_close" onclick="twoEditCancel()"></a>
        </div>
        <div class="bonceCon">
            <%--   货物或应税劳务、服务名称--%>
            <div class="huo typeCC">
                <div class="noEdit">
                    <p>目前，本商品开票资料“货物或应税劳务、服务名称”尚无数据，需采用哪项数据？</p>
                    <div class="blueTip">
                        <span>注1 财务人员对本商品开发票时，需使用到此数据，建议进行设置！</span><br>
                        <span>注2 此数据设置前，往往需先与客户沟通，并达成一致。</span>
                    </div>
                </div>
                <div class="yesEdit">
                    <p>目前，本商品开票资料“货物或应税劳务、服务名称”的数据为：</p>
                    <p class="huoVal">XXXXXXXXXXXXXXX</p>
                    <hr>
                    <p>需将此数据修改为哪一项？请选择。</p>
                    <div class="blueTip">
                        <span>注1 此数据修改前，往往需先与客户沟通，并达成一致。</span>
                    </div>
                </div>
                <div>
                    <div class="selectItems">
                        <div class="selectItem">
                            <span class="funBtn" data-fun="toggleTwo"><span data-val="1" class="fa fa-circle-o"></span>采用商品代号的当前数据</span>
                        </div>
                        <div class="selectItem">
                            <span class="funBtn" data-fun="toggleTwo"><span data-val="2" class="fa fa-circle-o"></span>采用商品名称的当前数据</span>
                        </div>
                        <div class="selectItem">
                            <span class="funBtn" data-fun="toggleTwo"><span data-val="3" class="fa fa-circle-o"></span>上述代号与名称都使用，代号在前</span>
                        </div>
                        <div class="selectItem">
                            <span class="funBtn" data-fun="toggleTwo"><span data-val="4" class="fa fa-circle-o"></span>上述代号与名称都使用，名称在前</span>
                        </div>
                        <div class="selectItem">
                            <span class="funBtn" data-fun="toggleTwo" data-type="zi"><span data-val="5" class="fa fa-circle-o"></span>自定义</span>
                            <input type="text" class="form-control" placeholder="请录入" disabled>
                        </div>
                    </div>

                    <hr>
                    <p>当前，本商品代号与名称数据如下：</p>
                    <div class="padC">
                        <div>
                            <span>商品代号：<span class="codeTxt" id="foo1">XXXXXXXXXXXXXXXXX</span></span> <span class="linkBtn copyFun" data-fun="copyFun" data-clipboard-target="#foo1" data-txt="1">复制</span>
                        </div>
                        <div>
                            <span>商品名称：<span class="codeTxt" id="foo2">XXXXXXXXXXXXXXXXX</span></span> <span class="linkBtn copyFun" data-fun="copyFun" data-clipboard-target="#foo2">复制</span>
                        </div>
                    </div>

                    <hr>
                </div>
            </div>
            <%--  规格型号--%>
            <div class="model typeCC">
                <div class="noEdit">
                    <p>目前，本商品开票资料“规格型号”尚无数据，需采用哪项数据？</p>
                    <div class="blueTip">
                        <span>注1 财务人员对本商品开发票时，如使用此数据，请设置！</span><br>
                        <span>注2 此数据设置前，往往需先与客户沟通，并达成一致。</span>
                    </div>
                </div>
                <div class="yesEdit">
                    <p>目前，本商品开票资料“规格型号”的数据为：</p>
                    <p class="modelVal">XXXXXXXXXXXXXXX</p>
                    <hr>
                    <p>需将此数据修改为哪一项？请选择。</p>
                    <div class="blueTip">
                        <span>注1 此数据修改前，往往需先与客户沟通，并达成一致。</span>
                    </div>
                </div>
                <div>
                    <div class="selectItems">
                        <div class="selectItem">
                            <span class=" funBtn" data-fun="toggleTwo"><span data-val="1" class="fa fa-circle-o"></span>发票上的“规格型号”为空，无需填写</span>
                        </div>
                        <div class="selectItem">
                            <span class=" funBtn" data-fun="toggleTwo"><span data-val="2" class="fa fa-circle-o"></span>采用规格的当前数据</span>
                        </div>
                        <div class="selectItem">
                            <span class=" funBtn" data-fun="toggleTwo"><span data-val="3" class="fa fa-circle-o"></span>采用型号的当前数据</span>
                        </div>
                        <div class="selectItem">
                            <span class=" funBtn" data-fun="toggleTwo"><span data-val="4" class="fa fa-circle-o"></span>上述规格与型号都使用，规格在前</span>
                        </div>
                        <div class="selectItem">
                            <span class=" funBtn" data-fun="toggleTwo"><span data-val="5" class="fa fa-circle-o"></span>上述规格与型号都使用，型号在前</span>
                        </div>
                        <div class="selectItem">
                            <span class=" funBtn" data-fun="toggleTwo" data-type="zi"><span data-val="6" class="fa fa-circle-o"></span>自定义</span>
                            <input type="text" class="form-control" placeholder="请录入">
                        </div>
                    </div>

                    <hr>
                    <p>当前，本商品规格与型号数据如下：</p>
                    <div class="padC">
                        <div>
                            <span>规格：<span class="codeTxt" id="foo3">XXXXXXXXXXXXXXXXX</span></span> <span class="linkBtn copyFun" data-fun="copyFun" data-clipboard-target="#foo3">复制</span>
                        </div>
                        <div>
                            <span>型号：<span class="codeTxt" id="foo4">XXXXXXXXXXXXXXXXX</span></span> <span class="linkBtn copyFun" data-fun="copyFun" data-clipboard-target="#foo4">复制</span>
                        </div>
                    </div>

                    <hr>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="twoEditCancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="twoEditOk()">确定</span>
        </div>
    </div>


    <%-- 商品的两项开票资料 编辑记录 --%>
    <div class="bonceContainer bounce-blue" id="twoEditLog">
        <div class="bonceHead">
            <span>开票资料“<span class="ttlType">货物或应税劳务、服务名称</span>”的编辑记录</span>
            <a class="bounce_close" onclick="bounce.show($('#seeCommodity')); "></a>
        </div>
        <div class="bonceCon noEdit">
            <h3 style="color:#aaa; text-align: center;">尚无数据</h3>
        </div>
        <div class="bonceCon havEdit">
            <p class="lastMsg">当前资料为第n次编辑后的结果，操作者：XXX XXXX-XX-XX XX：XX：XX</p>
            <table class="ty-table">
                <thead>
                <td>资料状态</td>
                <td>编辑后的数据</td>
                <td>操作者</td>
                </thead>
                <tbody id="logTab">

                </tbody>
            </table>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.show($('#seeCommodity')); ">取消</span>
        </div>
    </div>



    <%-- 当前订购信息 --%>
    <div class="bonceContainer bounce-red" id="curOrders">
        <div class="bonceHead">
            <span id="curTtl">！ 重要提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <tr>
                    <td width="10%">序号</td>
                    <td width="15%">订单号</td>
                    <td width="15%">负责人</td>
                    <td width="15%">未发货数量</td>
                    <td width="15%">要求到货日期</td>
                    <td width="30%">客户名称</td>
                </tr>
                <tr>
                    <td>序号</td>
                    <td>订单号</td>
                    <td>负责人</td>
                    <td>未发货数量</td>
                    <td>要求到货日期</td>
                    <td>客户名称</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%-- ！提示 -暂停销售 --%>
    <div class="bonceContainer bounce-red" id="pauseSales">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="pauseSalesTip"></div>

        </div>
        <div class="bonceFoot elemFlexSpace">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="suspendSaleSure()">确定</span>
        </div>
    </div>
    <%-- ！提示 -删除 --%>
    <div class="bonceContainer bounce-red" id="deleteCommodity">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="deleteTip" class="ty-center">
                系统不支持“删除”有关联、包装或订购信息的商
                品，而此商品在系统中有关联、包装或订购信息！</div>
        </div>
        <div class="bonceFoot">
            <span class="delRefuse ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">我知道了</span>
            <span class="deleteCan ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="deleteCan ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="deleteCommoditySure()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--  商品价格信息的修改记录  --%>
    <div class="bonceContainer bounce-blue" id="epPriceLog" style="width: 1040px;">
        <div class="bonceHead">
            <span>商品价格信息的修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="epPriceLogInfo">当前资料为第n次修改后的结果，修改人：XXX XXXX-XX-XX XX：XX：XX</p>
            <table class="ty-table" id="epPriceLogTab">
                <tr>
                    <td>数据</td>
                    <td>有效期</td>
                    <td>创建人/修改人</td>
                    <td>修改原因</td>
                    <td>财务的确认记录</td>
                </tr>

            </table>
        </div>
        <div class="bonceFoot main80">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关 闭</span>
        </div>
    </div>

    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="otherPrice" style="width: 600px;">
        <div class="bonceHead">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>价格信息（以下价格有效期为 <span class="validateInfo"></span> ）</p>
            <table class="ty-table">
                <tr>
                    <td width="30%">开增值税专用发票时</td>
                    <td class="priceCat1"></td>
                </tr>
                <tr>
                    <td width="30%">开普通发票时</td>
                    <td class="priceCat2"></td>
                </tr>
                <tr>
                    <td width="30%">不开发票时</td>
                    <td class="priceCat3"></td>
                </tr>
                <tr>
                    <td width="30%">参考单价</td>
                    <td class="priceCat4"></td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3"  onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="editPrice" style="width: 800px;">
        <div class="bonceHead">
            <span>修改商品的价格信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>商品代号/名称/规格/型号 <br>
                    <span class="gsInfo"></span>
                </p>
                <div class="lineC">
                    <div class="eerrw">
                        <div>开增值税专用发票时的单价</div>
                        <div class="inv1 flexdc">
                            <div>
                                税率 <br>
                                <select class="form-control" id="addInv" need name="taxRate" require onchange="clearPrice2()">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div>
                                不含税单价 <br>
                                <input id="price11" class="form-control" need  placeholder="请录入" name="unitPriceNotax" onkeyup="clearNoNumN(this,10);autoPrice2(1);" />
                            </div>
                            <div>
                                含税单价 <br>
                                <input id="price21" class="form-control" need placeholder="请录入" name="unitPriceNotax" onkeyup="clearNoNumN(this,10);autoPrice2(2);" />
                            </div>
                        </div>
                        <div class="inv2">
                            开普通发票时的开票单价 <br>
                            <input class="form-control" id="price3" need  placeholder="请录入" name="unitPriceInvoice" onkeyup="clearNoNumN(this,10)" />
                        </div>
                        <div class="inv4">
                            不开发票时的单价 <br>
                            <input class="form-control" need id="price4" placeholder="请录入" name="unitPriceNoinvoice" onkeyup="clearNoNumN(this,10)" />
                        </div>
                        <div class="inv5">
                            参考单价 <br>
                            <input class="form-control" need id="price5" placeholder="请录入" name="unitPriceReference" onkeyup="clearNoNumN(this,10)" />
                        </div>
                    </div>

                </div>
                <div>
                    <p><i class="red">*</i>本次修改的生效日期</p>
                    <p>
                        <span class="faI"><i data-type="1" class="fa fa-circle-o"></i>立即生效</span>
                        <span class="faI"><i data-type="2" class="fa fa-circle-o"></i>选择其他日期</span>
                        <input type="text" class="form-control" id="date1" placeholder="请选择日期" >
                    </p>
                </div>
                <div class="inputOk">
                    <div>
                        <p>修改原因 <span class="ty-right">0/100</span></p>
                        <textarea placeholder="如必要，请录入" onkeyup="limitWord3($(this), 100)" id="ep_reason" class="form-control"></textarea>
                    </div>
                    <div class="lineC">
                        <p>
                            本修改生效后，将生成修改记录，以及自<span class="useDate"></span>起创建新业务时将采用改后的数据，已完结业务及进行中的订单则不会跟随改变。
                        </p>
                        <p>
                            <span class="useDate"></span>之前再创建新订单时，如包含本商品，则申请开票时，系统可能询问本商品执行哪个价格。
                        </p>
                        <p>
                            进行中的订单中，包含本商品的共<span class="orderUnNum22"></span>个。申请开票时，系统将询问本商品执行哪个价格。
                            <span class="ty-right nodeBtn" data-fun="getOccOrder" data-type="price">查看</span>
                        </p>
                    </div>
                    <p>
                        点击“确定”后，本次修改将生效！
                    </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" id="editPriceOk" onclick="editPriceOk()">确定</span>
        </div>
    </div>
    <%--订单查看--%>
    <div class="bonceContainer bounce-blue" id="ep_orderList" style="width: 920px;">
        <div class="bonceHead">
            <span>订单查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                以下为包含所修改商品的未完结订单。对这些订单开票与收款时，系统将询问执行本商品哪个价格。
            </p>
            <table class="ty-table">
                <tr>
                    <td>订单号</td>
                    <td>订单收到日期</td>
                    <td>录入时间</td>
                    <td>计量单位</td>
                    <td>订购数量</td>
                    <td>要求到货日期</td>
                    <td>已发货</td>
                    <td>已签收</td>
                    <td>已开票</td>
                </tr>
                <tr data-info="12">
                    <td>订单号</td>
                    <td>订单收到日期</td>
                    <td>录入时间</td>
                    <td>计量单位</td>
                    <td>订购数量</td>
                    <td>要求到货日期</td>
                    <td class="ty-table-control"><span class="ty-color-blue" onclick="dataLog(1)">已发货</span></td>
                    <td class="ty-table-control"><span class="ty-color-blue" onclick="dataLog(2)">已签收</span></td>
                    <td class="ty-table-control"><span class="ty-color-blue" onclick="dataLog(3)">已开票</span></td>
                </tr>
            </table>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="seeOrder" style="width: 980px;">
        <div class="bonceHead bounce-blue">
            <span>查看要货计划</span>
            <a class="bounce_close" onclick="seeOrderCancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                以下<span class="num"></span>条要货计划所属的订单尚未完成，现商品基本信息已修改，需确定应对方案！
            </p>
            <div class="blueTip">
                注：应对方案的操作，需到“处理”中或“订单管理—订单管理”下进行，此处只可查看。
            </div>
            <table class="ty-table">
                <tr>
                    <td>要求到货日期</td>
                    <td>计量单位</td>
                    <td>订购数量</td>
                    <td>已发货数量</td>
                    <td>所属订单的订单号</td>
                    <td>订单收到日期</td>
                    <td>客户</td>
                </tr>
                <tr></tr>
            </table>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="seeOrderCancel()">关闭</span>
        </div>
    </div>

    <%--修改商品基本信息--%>
    <div class="bonceContainer bounce-blue" id="editCommodityBase" style="width: 970px;">
        <div class="bonceHead">
            <span>修改商品的基本信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <table class="tyable" id="updateCommodityBase2">
                    <tr>
                        <td colspan="2">
                            <span><i class="red">*</i>商品代号</span><br/>
                            <span class="inputC">
                                <i class="fa fa-close"></i>
                                <input type="text" class="form-control" need require name="outerSn">
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <span><i class="red">*</i>商品名称</span><br/>
                            <span class="inputC">
                                <i class="fa fa-close"></i>
                                <input type="text" class="form-control" need require name="outerName">
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="pad2">
                            <span>规格</span><br/>
                            <span class="inputC">
                                <i class="fa fa-close"></i>
                                <input type="text" class="form-control" need name="specifications">
                            </span>
                        </td>
                        <td class="pad2">
                            <span>型号</span><br/>
                            <span class="inputC">
                                <i class="fa fa-close"></i>
                                <input type="text" class="form-control" need name="model">
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" class="firstTimeCCon">
                            <span>成为公司通用型商品的时间</span>
                            <span class="marL30 " id="firstTimeCC2" data-type="0">
                              <span class="radiop cc1" onclick="toggefirstTime(1,2)"><i class="fa fa-circle-o" ></i>今年</span>
                              <span class="firMonthcon">
                                 <input type="text" id="firMonth2" onchange="nullNextN()" class="ty-inputText" style="width: 100px;" />
                                  <span class="radiop ccN" onclick="toggefirstTimeN(2)"><i class="fa fa-circle-o"></i>不确定月份</span>
                              </span>
                              <span class="radiop cc2" onclick="toggefirstTime(2,2)"><i class="fa fa-circle-o"></i>去年</span>
                              <span class="radiop cc3" onclick="toggefirstTime(3,2)"><i class="fa fa-circle-o"></i>更久之前</span>
                            </span>
                        </td>
                    </tr>
                    <tr><td colspan="2" class="hrLine"></td></tr>
                    <tr><td colspan="2" class="pad">
                        <p>开发票时，本商品在发票内的信息 <span class="ty-right nodeBtn">功能说明</span></p>
                        <p><i class="red">*</i>修改生效后，商品的“货物或应税劳务、服务名称”是否需修改？</p>
                        <p class="nameLinkable">
                            <span class="faGroup1"><i data-sta="0" class="fa fa-circle-o"></i>无需修改</span>
                            <span class="faGroup1" id="eName"><i data-sta="1" class="fa fa-circle-o"></i>需修改，且已修改</span>
                            <span class="ty-right nodeBtn" data-fun="twoEdit" data-type="huo" data-sta="editInfo">编辑</span>
                        </p>
                        <p>修改生效后，本商品的“规格型号”是否需修改？</p>
                        <p class="modelLinkable">
                            <span class="faGroup1"><i data-sta="0" class="fa fa-circle-o"></i>无需修改</span>
                            <span class="faGroup1" id="eModel"><i data-sta="1" class="fa fa-circle-o"></i>需修改，且已修改</span>
                            <span class="ty-right nodeBtn" data-fun="twoEdit" data-type="model" data-sta="editInfo">编辑</span>
                        </p>
                    </td></tr>
                    <tr><td colspan="2" class="hrLine"></td></tr>
                    <tr><td colspan="2" class="pad">
                        <span>修改原因</span>
                        <span class="ty-right lenTip">0/100</span><br>
                        <span class="inputC ">
                            <i class="fa fa-close"></i>
                            <input type="text" class="form-control" name="basicReason" onkeyup="limitWord2($(this), 100)" need placeholder=" 如必要，请录入">
                        </span>
                    </td></tr>
                    <tr><td colspan="2" class="hrLine"></td></tr>
                    <tr><td colspan="2" class="pad">
                        <p>本修改生效后，将生成修改记录，以及创建新业务时将采用改后的数据，已完结业务及进行中的订单则不会跟随改变。</p>
                        <p>
                            <span class="ty-right nodeBtn" data-fun="seeOrder" data-type="base">查看</span>
                            以下<span id="orderUnNum2"></span>条数据所属的订单尚未完成，现商品基本信息已修改，需确定应对方案！<br>
                            <span class="litBlue">注：应对方案的操作，需到“处理”中或“订单管理—订单管理”下进行，此处只可查看。</span>
                        </p>
                    </td></tr>
                    <tr><td colspan="2" class="hrLine"></td></tr>
                    <tr><td colspan="2" class="pad">
                        点击“确定”后，本次修改将生效！
                    </td></tr>
                </table>
            </div>
        </div>

        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateCommodityBaseSure()">确定</span>
        </div>
    </div>
    <%--修改商品其他信息--%>
    <div class="bonceContainer bounce-blue" id="editCommodityOther" style="width: 980px;">
        <div class="bonceHead bounce-blue">
            <span>修改商品的其他信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <div id="editOtherInvoice">
                <div class="modInfo clear">
                    <div class="modItem-m">
                        <p class="modItemTtl">商品代号</p>
                        <div name="outerSn" need class="gray"></div>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">商品名称</p>
                        <div name="outerName" need class="gray"></div>
                    </div>
                </div>
                <div class="modInfo clear">
                    <div class="modItem-m">
                        <p class="modItemTtl">规格</p>
                        <div name="specifications" need class="gray"></div>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">型号</p>
                        <div name="model" need class="gray"></div>
                    </div>
                </div>
                <hr/>
                <div class="modInfo clear">
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            <i class="red">*</i>计量单位
                            <span class="nodeBtn ty-right" data-fun="addUnit">新增</span>
                        </p>
                        <select class="ty-inputSelect" type="text" id="update_unitSelect" name="unitId" need require onchange="unitAssign($(this))"></select>
                        <input type="hidden" name="unit" id="update_unitName" need>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            <i class="red">*</i>最低库存
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="minimumStock" onkeyup="testNumSize3(this)" need require/>
                    </div>

                    <div class="modItem-m mediaBody">
                        <p class="modItemTtl">
                            商品照片(最多可上传9张)
                            <span class="nodeBtn ty-right" id="filePic_edit"></span>
                        </p>
                        <div class="modPics">
                            <div class="file-box filePicBox clear"><span class="inTip">请上传</span></div>
                            <div class="clearPicsBtn" onclick="clearPicsBtn($(this))">清除</div>
                        </div>
                    </div>
                    <div class="modItem-m mediaBody">
                        <p class="modItemTtl">
                            商品视频(可上传一个，且不可超过15秒)
                            <span class="nodeBtn ty-right" id="fileVedio_edit"></span>
                        </p>
                        <div class="file-box fileVedioBox clear"><span class="inTip">请上传</span></div>
                    </div>
                </div>
                <div class="modInfo clear">
                    <div class="modItem-m">
                        <p class="modItemTtl gsDesc ">
                            商品说明<span class="lenTip ty-right">0/100</span>
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="memo" need onkeyup="limitWord($(this), 100)"/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl priceDesc">
                            价格说明<span class="lenTip ty-right">0/100</span>
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="priceDesc" onkeyup="limitWord($(this), 100)" need/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                </div>
                <hr/>
                <div class="modInfo clear">
                    <div class="modItem-l">
                        <p class="modItemTtl reason">
                            修改原因<span class="lenTip ty-right">0/100</span>
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="basicReason" onkeyup="limitWord($(this), 100)" need/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                </div>

            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateOtherSure()">确定</span>
        </div>
    </div>
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue" id="commEditLog" style="width: 900px;">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="padding:20px;">
                <p class="curSta"></p>
                <table class="ty-table">
                    <tr>
                        <td>资料状态</td>
                        <td>创建人/修改人</td>
                        <td>修改原因</td>
                        <td class="cai">财务的确认记录</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--导入前--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leadingBefore">
        <div class="bonceHead">
            <span>批量导入通用型商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #ddebf7;">
            <form>
                <div style="padding: 15px 20px;">
                    <div class="stepItem">
                        <p>要导入的通用型商品中，如含有需要开增值税专用发票的，完</p>
                        <p>成以下两项设置，可降低导入后的工作量！</p>
                        <p class="fap"><i data-val="1" class="fa fa-circle-o"></i> 导入全部通用型商品</p>
                        <hr>
                        <p class="fap"><i data-val="2" class="fa fa-circle-o"></i> 要导入的通用型商品中，含有需要开增值税专用发票的，并进行如下设置</p>
                        <div class="tipBlue">注：每次只能导入一种税率且价格型式相同的商品！</div>
                    </div>
                    <div class="stepItem" style="padding-left:50px;">
                        <div>请选择要导入商品的税率</div>
                        <select class="form-control" id="imRate" style="width:240px;"></select>
                        <p>&nbsp;</p>
                        <div>请选择本次要导入商品价格的型式</div>
                        <div>
                            <p class="fap2"><i data-val="1" class="fa fa-circle-o"></i> 导入商品的价格均为含税价</p>
                            <p class="fap2"><i data-val="0" class="fa fa-circle-o"></i> 导入商品的价格均为不含税价</p>
                        </div>
                    </div>
                    <hr>
                    <div>
                        <p class="fap"><i data-val="5" class="fa fa-circle-o"></i> 要导入的通用型商品中，不含有需要开增值税专用发票的</p>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot" style="background: #ddebf7;">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="leadingShowNext()">确定</span>
        </div>
    </div>
    <%--导入--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #ddebf7;">
            <form action="../export/ImportMaterial.do" id="materielImport" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：下载空白的“商品清单”。</p>
                        <div class="flexRow">
                            <span>商品清单</span>
                            <a href="../assets/oralResource/template/material_blank_sheet.xls"
                               id="mould1" download="商品清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“商品清单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="importUploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="matImportOk('cancel');">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="matImportOk()">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加、删除或修改所下载“商品清单”空白表的“列”，否则上传会失败。</span></br>
                            <span>2、在电脑上保存“商品清单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot" style="background: #ddebf7;">
        </div>
    </div>
    <%-- 下一步确定  --%>
    <div class="bonceContainer bounce-red" id="importListTj">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>还有<span id="errNum"></span>种商品无法保存至系统。</p>
            <p>进入下一步，这些商品将被舍弃。</p>
            <p>确定进入下一步吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="importListTjSure()">确定</span>
        </div>
    </div>
    <%-- 放弃  --%>
    <div class="bonceContainer bounce-red" id="importCancel">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>放弃后，本次批量导入的数据将消失不见。</p>
            <p>确定放弃吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-name="importCancelSure">确定</span>
        </div>
    </div>
    <%-- 保存  --%>
    <div class="bonceContainer bounce-red" id="importListSave">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <p>确定后：</p>
                <p class="saveTip"></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-name="lastSaveSure">确定</span>
        </div>
    </div>
    <%-- 上次的批量导入尚未完成。  --%>
    <div class="bonceContainer bounce-red" id="importNotCompleted">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody unfinishedForm">
                <p>上次的批量导入尚未完成。</p>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="1"></span>继续上次的操作
                </div>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="0"></span>放弃上次的操作，重新批量导入
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-name="judgeImport">确定</span>
        </div>
    </div>
    <%-- 修改导入的材料 --%>
    <div class="bonceContainer bounce-green" id="updateImportMt" style="width: 980px">
        <div class="bonceHead">
            <span>商品修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modInfo clear" id="updateImportCommodity">
                <div class="modItem-m">
                    <p class="modItemTtl">
                        <i class="red">*</i>商品代号
                    </p>
                    <input class="ty-inputText" value="" placeholder="请录入" name="code" need require/>
                    <i class="fa fa-times clearInputVal"></i>
                </div>
                <div class="modItem-m">
                    <p class="modItemTtl">
                        <i class="red">*</i>商品名称
                    </p>
                    <input class="ty-inputText" value="" placeholder="请录入"name="name" need require/>
                    <i class="fa fa-times clearInputVal"></i>
                </div>
                <div class="modItem-m">
                    <div class="ty-left">
                        <p class="modItemTtl">
                            规格
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="specifications" need/>
                    </div>
                    <div class="ty-right">
                        <p class="modItemTtl">
                            型号
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="model" need/>
                    </div>
                </div>
                <div class="modItem-m">
                    <div class="ty-left">
                        <p class="modItemTtl">
                            <i class="red">*</i>计量单位
                            <span class="nodeBtn ty-right" data-fun="addUnitImport">新增</span>
                        </p>
                        <select class="ty-inputSelect" type="text" id="import_update_unitSelect" name="unitId" need require onchange="unitAssign($(this))"></select>
                        <input type="hidden" name="unit" id="import_update_unitName" need>
                    </div>
                    <div class="ty-right">
                        <p class="modItemTtl">
                            <i class="red">*</i>最低库存
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="miniStock" onkeyup="testNumSize3(this)" need require/>
                    </div>
                </div>
                <div class="modItem-m">
                    <p class="modItemTtl">
                        商品照片(最多可上传9张)
                        <span class="nodeBtn ty-right" id="filePic2"></span>
                    </p>
                    <div class="modPics">
                        <div class="file-box filePicBox clear"><span class="inTip">请上传</span></div>
                        <div class="clearPicsBtn" onclick="clearPicsBtn($(this))">清除</div>
                    </div>
                </div>
                <div class="modItem-m mediaBody">
                    <p class="modItemTtl">
                        商品视频(可上传一个，且不可超过15秒)
                        <span class="nodeBtn ty-right" id="fileVedio2"></span>
                    </p>
                    <div class="file-box fileVedioBox clear"><span class="inTip">请上传</span></div>
                </div>
                <div class="modItem-l">
                    <p class="modItemTtl">
                        商品说明<span class="lenTip ty-right">0/100</span>
                    </p>
                    <input class="ty-inputText" value="" placeholder="请录入" name="desc" need onkeyup="limitWord($(this), 100)"/>
                    <i class="fa fa-times clearInputVal"></i>
                </div>

            </div>
            <hr style="border-top:1px solid #ccc;" />
            <div class="ty-color-blue" style="margin-left: 32px;font-size: 12px;">注：录入订单时，需确认开票情况，并将引用此处单价。在此也可只录入参考单价。</div>
            <div class="modInfo clear">
                <div class="modItem-m invoice1">
                    <p class="clear">请录入开增值税专用发票时的价格</p>
                    <div class="modItem-ss ty-left">
                        <p class="modItemTtl">
                            税率
                        </p>
                        <select class="ty-inputSelect" id="addInvoiceSpecial2" need name="taxRate" onchange="clearPrice()">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="modItem-s ty-left">
                        <p class="modItemTtl">
                            不含税单价
                        </p>
                        <input class="ty-inputText" id="price1" need placeholder="请录入" name="unitPriceNotax" onkeyup="clearNoNum(this)"/>
                    </div>
                    <div class="modItem-s ty-right">
                        <p class="modItemTtl">
                            含税单价
                        </p>
                        <input class="ty-inputText" id="price2" need name="unitPrice" placeholder="请录入" onkeyup="clearNoNum(this)"/>
                    </div>
                </div>
                <div class="modItem-m invoice2">
                    <p style="height: 20px;"></p>
                    <p class="modItemTtl">
                        开普通发票时的开票单价
                    </p>
                    <input class="ty-inputText" need placeholder="请录入" name="unitPriceInvoice" onkeyup="clearNoNum(this)"/>
                </div>
            </div>
            <div class="modInfo clear">
                <div class="modItem-m invoice4">
                    <p class="modItemTtl">
                        不开发票时的单价
                    </p>
                    <input class="ty-inputText" need placeholder="请录入" name="unitPriceNoinvoice" onkeyup="clearNoNum(this)"/>
                </div>
                <div class="modItem-m invoice0">
                    <p class="modItemTtl">
                        参考单价
                    </p>
                    <input class="ty-inputText" need placeholder="请录入" name="unitPriceReference" onkeyup="clearNoNum(this)"/>
                </div>
                <div class="modItem-l">
                    <p class="modItemTtl">
                        价格说明<span class="lenTip ty-right">0/100</span>
                    </p>
                    <input class="ty-inputText" need placeholder="请录入" name="priceDesc" onkeyup="limitWord($(this), 100)"/>
                    <i class="fa fa-times clearInputVal"></i>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-name="updateImportMtBtn">确定</span>
        </div>
    </div>
    <%-- 删除导入的材料  --%>
    <div class="bonceContainer bounce-red" id="importMtDel">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">确定删除所导入的这个商品吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="importMtDel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed2.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-red" id="iknowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="iknowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(1)">确定</span>
        </div>
    </div>
    <%-- 提示 --%>
    <div class="bonceContainer bounce-red" id="unfilledTip">
        <div class="bonceHead">
            <span>!提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p id="unfilledTip_ms">还有必填项尚未填写！ </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 导入的重要提示 --%>
    <div class="bonceContainer bounce-red" id="importantTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="gap-lt">
                <h4>导入失败！</h4>
                <div>原因可能为：</div>
                <div>1、修改了所下载表格中的“列”。</div>
                <div>2、选错了文件。</div>
                <div>3、文件太大，或里面含有图片等。</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">知道了</span>
        </div>
    </div>

    <%--商品修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="commEditLogScan" style="width: 900px;">
        <div class="bonceHead bounce-blue">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon align-left">
            <div class="logScan1">
                <div class="main-nav">
                    <span>基本信息</span>
                </div>
                <table  class="ty-table">
                    <tbody>
                    <tr>
                        <td>商品代号</td>
                        <td colspan="3" need data-name="outerSn"></td>
                    </tr>
                    <tr>
                        <td width="30%">商品名称</td>
                        <td colspan="3" need data-name="outerName"></td>
                    </tr>
                    <tr>
                        <td width="12%">规格</td>
                        <td need data-name="specifications"></td>
                        <td width="12%">型号</td>
                        <td need data-name="model"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="logScan2">
                <div class="elemFlex">
                    <span class="ttl">其他信息</span>
                </div>
                <table  class="ty-table seeInvoice1">
                    <tbody>
                    <tr>
                        <td>关联的产品</td>
                        <td>
                            <div class="ty-left"><span need data-name="innerSn"></span>&nbsp;&nbsp;<span need data-name="productName"></span></div>
                            <div class="ty-right nodeBtn" data-fun="relatedRecord">关联记录</div>
                        </td>
                    </tr>
                    <tr>
                        <td>计量单位</td>
                        <td class="unit" need data-name="unit"></td>
                    </tr>
                    <tr>
                        <td>最低库存</td>
                        <td class="minimumStock" need data-name="minimumStock"></td>
                    </tr>
                    <tr>
                        <td>商品照片</td>
                        <td class="comPics" need data-name="comPics"></td>
                    </tr>
                    <tr>
                        <td>商品视频</td>
                        <td class="comVideo" need data-name="comVideo"></td>
                    </tr>
                    <tr>
                        <td>商品说明</td>
                        <td need data-name="memo">
                        </td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td need data-name="priceDesc"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="clear">
                <div class="operations">
                    <div><span class="oper">创建</span><span id="seeCreator_log"></span></div>
                    <div class="reLog">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%--关联记录--%>
    <div class="bonceContainer bounce-blue" id="relateRecord" style="width: 676px; max-height:400px; ">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="md-out">
                <div>
                    <span class="mTtl">当前关联的产品</span>
                    <span class="modPlace"></span>
                </div>
                <div>
                    <span class="mTtl">关联的操作者</span>
                    <span class="operationPlace"></span>
                </div>
            </div>
            <table class="ty-table">
                <tr>
                    <td width="20%" class="controlTd">记 录</td>
                    <td width="40%">所关联的产品</td>
                    <td width="40%">关联的操作者</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>通用型商品</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainData">
                    <div id="picShow" style="display: none;">
                        <img src=""/>
                    </div>
                    <div id="video-box" style="display: none;">
                        <video id="Video1" controls height="320" width="240" title="video element">
                            HTML5 Video is required for this example
                        </video>
                    </div><%--material massage--%>
                    <div>
                        <div id="mtInfo" class="mainCon1 Con">
                            <div class="bigContainer">
                                <div class="left_container">
                                    <div class="indexInput">
                                        <div class="Btop"><span>录入商品</span></div>
                                        <ul class="faceul Left-label">
                                            <li class="faceul1">
                                                <a>
                                                    <button onclick="newSalesCommodity(1,1)">录入已销售过的商品</button>
                                                </a>
                                            </li>
                                            <li class="faceul1">
                                                <a>
                                                    <button onclick="newSalesCommodity(0,1)">录入未销售过的商品</button>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div>
                                        <div class="Btop" id="firstLevel"><span id="firstLevelName">全部</span>(<span id="firstLevelAmount">0</span>种)</div>
                                        <form>
                                            <ul class="faceul bottomTree" id="kindsTree"></ul>
                                            <div class="faceul1 suspendBtn">
                                                <a>
                                                    <span><span onclick="suspendCommodyList($(this))">暂停销售的商品</span>（<span id="suspendCommodyNum">0</span>种)</span>
                                                </a>
                                            </div>
                                            <div class="left-bottom clear" style="display: none;">
                                                <div class="add-b" onclick="gobackLstLevel(1)"> <a> <span>返回上一级</span> </a>  </div>
                                                <div class="add-b" onclick="gobackLstLevel(2)" > <a> <span >返回全部</span>  </a> </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div class="between"><div class="between2"></div></div>
                                <div class="right_container">
                                    <div id="com_invoiceCategory" style="display: none;"></div>
                                    <div class="Right-label" id="right_container">
                                        <div class="container_nav">
                                            <div class="conon">
                                                <div class="dq">
                                                    <span>当前分类</span> <span>：</span>
                                                    <span id="curID">
                                                        <span onclick="showkindNav($(this))" data-id="" data-name="">全部</span>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ty-right searchSect">
                                                <div class="ty-left keywordSearch">
                                                    查找商品
                                                    <input placeholder="请输入商品代号或名称" id="searchKeyBase" />
                                                </div>
                                                <span class="ty-left ty-btn ty-btn-blue" onclick="searchKeyBase()">确定</span>
                                            </div>
                                        </div>
                                        <div class="">
                                            <div class="inSales inSalesList">
                                                <table class="ty-table ty-table-none bg-yellow" id="normalMaterial">
                                                    <thead>
                                                    <td width="10%">商品代号</td>
                                                    <td width="10%">商品名称</td>
                                                    <td width="8%">型号</td>
                                                    <td width="8%">规格</td>
                                                    <td width="8%">计量单位</td>
                                                    <td width="10%">最低库存</td>
                                                    <td width="10%">当前库存</td>
                                                    <td width="200">创建人</td>
                                                    <td width="20%">操作</td>
                                                    </thead>
                                                    <tbody id="materialList"></tbody>
                                                </table>
                                            </div>
                                            <div class="inSuspend inSuspendList" style="display: none;">
                                                <table class="ty-table ty-table-none bg-yellow" id="suspendTYList">
                                                    <thead>
                                                    <td width="10%">商品代号</td>
                                                    <td width="10%">商品名称</td>
                                                    <td width="8%">型号</td>
                                                    <td width="8%">规格</td>
                                                    <td width="8%">计量单位</td>
                                                    <td width="10%">最低库存</td>
                                                    <td width="10%">当前库存</td>
                                                    <td width="16%">暂停销售的操作</td>
                                                    <td width="20%">操作</td>
                                                    </thead>
                                                    <tbody></tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="ye"></div>
                                </div>
                                <div class="clr"></div>
                            </div>
                        </div>
                        <div class="mainCon3 narrowLamp bg-yellow" id="importEnteryType">
                            <div class="importCon1">
                                <div class="importNoSave stepItem">
                                    <span class="ty-btn ty-btn-yellow ty-btn-big btn" data-name="clearNoSave" style="margin-right: 250px;">放 弃</span>
                                    <span class="ty-btn ty-btn-blue ty-btn-big btn" data-name="stepNext">下一步</span>
                                </div>
                                <p>您共导入通用商品<span class="initAll"></span>条，其中以下<span class="initWrong"></span>条存在问题，<span class="ty-color-red">无法保存至系统</span>。</p>
                                <p>名称或代号未录入，本次导入商品的代号互相重复或与系统中已有代号相同等情况，计量单位未填写或计量单位被停用。均算作问题。</p>
                                <div class="gap-Tp">
                                    <div id="tureMtList" style="display: none;"></div>
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <td>商品名称</td>
                                        <td>商品代号</td>
                                        <td>型号</td>
                                        <td>规格</td>
                                        <td>计量单位</td>
                                        <td>最低库存</td>
                                        <td>商品说明</td>
                                        <td>操作</td>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="importCon2">
                                <div class="importing stepItem">
                                    <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 btn" data-name="cancelSave" style="margin-right: 250px;">放 弃</span>
                                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 btn" id="save">保 存</span>
                                </div>
                                <p>您共导入商品<span class="initAll"></span>条，可保存至系统的共<span class="inabledSum"></span>条。</p>
                                <table class="ty-table ty-table-control normal1">
                                    <thead>
                                    <tr>
                                        <td width="30%">商品</td>
                                        <td>最低库存</td>
                                        <%--<td class="hasRatePrice">含税单价</td>--%>
                                        <td>商品说明</td>
                                        <td width="10%" rowspan="2">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                                <table class="ty-table ty-table-control normal2">
                                    <thead>
                                    <tr>
                                        <td rowspan="2">商品</td>
                                        <td colspan="3" class="tdSpecial po-invoice1">增值税专用发票</td>
                                        <td rowspan="2" class="po-invoice2">开普通发票时的开票单价</td>
                                        <td rowspan="2">不开发票时的单价</td>
                                        <td rowspan="2">参考单价</td>
                                        <td rowspan="2">操作</td>
                                    </tr>
                                    <tr class="po-invoice1">
                                        <td>税率</td>
                                        <td>含税价</td>
                                        <td>不含税价</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.4/clipboard.min.js"></script>

<script src="../script/commodity/generalGoods.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/commodity/TyZsCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>
