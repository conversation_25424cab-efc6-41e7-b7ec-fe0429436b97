<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/commodity/goods.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/commodity/basic.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
 <div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue " onclick="processcancel()">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="mtConfirm">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_confirm_ms"> </p>
                    <p id="mt_confirm_type" class="hd"></p>
                    <p id="mt_confirm_id" class="hd"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue" onclick="okConfirm()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_cancel()">取消</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="BJAccount" style="width: 800px">
        <div class="bonceHead">
            <span>查看工序</span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <div class="Bounce_boder">
                <div class="bonce_left" >
                    <div class="BDIV">
                        <div class="GDS_left"><span>内部图号</span></div>
                        <div class="GDS_right">
                            <span id="sh_proinnertu"></span>
                            <input type="text" class="pro_hide Col_c" id="pro_innertu"  readonly="true">
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>构成</span></div>
                        <div class="GDS_right">
                            <span id="sh_pro"></span>
                            <input type="text" class="pro_hide Col_c" id="pro_pro" readonly="true">
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>工序名称</span></div>
                        <div class="GDS_right">
                            <span id="sh_proname"></span>
                            <input type="text" class="pro_hide" id="pro_proname" >
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>单耗</span></div>
                        <div class="GDS_right">
                            <span id="sh_take"></span>
                            <input type="text" class="pro_hide" id="pro_take" onKeyup = "clearNoNum( this )"
                            >
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>损耗定额</span></div>
                        <div class="GDS_right">
                            <span id="sh_sunhao"></span>
                            <input type="text" class="pro_hide" id="pro_sunhao" onKeyup = "clearNoNum( this )"
                            >
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>工艺参数指导书编号</span></div>
                        <div class="GDS_right">
                            <span id="sh_arts"></span>
                            <input type="text" class="pro_hide" id="pro_arts">
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>包装指导书编号</span></div>
                        <div class="GDS_right">
                            <span id="sh_pack"></span>
                            <input type="text" class="pro_hide" id="pro_pack" >
                        </div>
                    </div>
                </div>
                <div class="bonce_left">
                    <div class="BDIV">
                        <div class="GDS_left"><span>内部名称</span></div>
                        <div class="GDS_right">
                            <span id="sh_proinnername"></span>
                            <input type="text" class="pro_hide Col_c" id="pro_innername" readonly="true">
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>产品净重</span></div>
                        <div class="GDS_right">
                            <span id="sh_weight"></span>
                            <input type="text" class="pro_hide Col_c" id="pro_weight" readonly="true">
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>每模腔数</span></div>
                        <div class="GDS_right">
                            <span id="sh_procount"></span>
                            <input type="text" class="pro_hide" id="pro_count" onKeyup = "clearNoNum( this )">
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>材料利用率</span></div>
                        <div class="GDS_right">
                            <span id="sh_clmake"></span>
                            <input type="text" class="pro_hide" id="pro_clmake" onKeyup = "clearNoNum( this )"
                            >
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>废品率定额</span></div>
                        <div class="GDS_right">
                            <span id="sh_rubbish"></span>
                            <input type="text" class="pro_hide" id="pro_rubbish" onKeyup = "clearNoNum( this )" >
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>加工作业指导书编号</span></div>
                        <div class="GDS_right">
                            <span id="sh_work"></span>
                            <input type="text" class="pro_hide" id="pro_work" >
                        </div>
                    </div>
                    <div class="BDIV">
                        <div class="GDS_left"><span>备注</span></div>
                        <div class="GDS_right">
                            <span id="sh_remark"></span>
                            <input type="text" class="pro_hide" id="pro_remark">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="panel-4 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_BJ()" id="bounce_BJ">编辑</span>
            <span class="panel-4 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_OK()" class="pro_hide" id="bounce_OK">保存</span>
            <span class="panel-4 ty-btn ty-btn-big ty-circle-5" onclick="bounce_over()" id="bounce_over">关闭</span>
            <span class="panel-4 ty-btn ty-btn-big ty-circle-5" onclick="bounce_over1()" class="pro_hide" id="bounce_over1">取消</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>我的桌面</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="pull-right">
                    <div class="ty-search">
                        <span class="ty-search-ttl"></span>内部图号：<input id="search_innerSn" type="text" class="ty-searchInput"/>
                        <input type="button" value=""  class="ty-searchBtn" onclick="searchProcess()"/>
                    </div>
                </div>
                <div class="Border-big">
                    <div class="opinionCon">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td width="8%">内部图号</td>
                            <td width="8%">内部名称</td>
                            <td width="8%">构成</td>
                            <td width="8%">工序名称</td>
                            <td width="8%">产品净重</td>
                            <td width="8%">每模腔数</td>
                            <td width="8%">单耗</td>
                            <td width="8%">材料利用率</td>
                            <td width="8%">损耗定额</td>
                            <td width="8%">废品率定额</td>
                            <td width="8%">备注</td>
                            <td width="12%">操作</td>
                            </thead>
                            <tbody id="process_tbl">

                            </tbody>
                        </table>
                        <div id="ye_goodsprocess"></div>
                    </div>
                    <div class="clr"></div>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/commodity/process.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>
