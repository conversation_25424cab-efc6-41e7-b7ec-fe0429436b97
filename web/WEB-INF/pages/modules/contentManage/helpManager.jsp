<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

	<link href="../css/content/about/about.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<%@ include  file="../../common/contentHeader.jsp"%>

	<div class="page-container">
		<%@ include  file="../../common/contentSliderNav.jsp"%>

		<div class="page-content-wrapper" >
			<div class="page-content" id="paContainer" styly="min-height:800px;">

				<div class="contn" id="privacyCon" >
					<div class="manageNav ">使用帮助</div>
					<div class="Con">
						<div class="aboutbtn azury addOpinion" onclick="addScan()">我要上传</div>
						<div class="opinionCon">
							<table class="table table-hover table-bordered " id="helpTbl" >
								<thead>
									<th width="15%">上传日期</th>
									<th width="10%">上传人</th>
									<th width="15%">上传理由</th>
									<th width="15%">审批时间</th>
									<th width="10%">审批人</th>
									<th width="15%">审批意见</th>
									<th width="10%">状态</th>
									<th width="10%">更多</th>
								</thead>
								<tbody>
									 
								</tbody>
							</table>
							<div id="ye"></div>
						</div>
					</div>
				</div>
				<!-- 详情视图 -->
				<div class="Con hd" id="privacyDetails">
					 <div class="resultDiv">
					 	<span class="goback" onclick="ConToggle()"></span>
					 	<span class="manageNav "> 使用帮助 -- 查看 </span><span class="vrIcon " id="status_1">已批准</span>
					 	<span id="updateRight" onclick="turnEdit()" class="helpBtn_update orange bigBtn hand" >我要修改</span>
					 </div>
					 <div class="resultCon">
					 	<div class="resultKey">
					 	 	<span>上传理由：</span><label id="pri_reson">是大法官的</label><br>
					 	 	<span>内容列表：</span> 
					 	</div>
					 	<div class="answer_content">
					 		<table class="table table-hover table-bordered " id="helpDetails_tbl">
					 			<thead>
					 				<th width="10%">模块名称</th>
					 				<th width="20%">标题</th>
					 				<th width="60%">内容</th>
					 				<th width="10%" class="upcon_help">操作</th>
					 			</thead>
					 			<tbody>
					 				 
					 			</tbody>
					 		</table> 	
					 		<div id="ye1"> </div>
					 		 
					 	</div>
					 </div>
				</div>
				<!-- 上传视图 -->
				<div class="Con hd" id="privacyAdd">
					<div class="resultDiv">
					 	<span class="goback" onclick="ConToggle()"></span>
					 	<span class="manageNav "> 使用帮助 -- 上传 </span>
					 	<span onclick="submitHelp()" class="helpBtn_update orange bigBtn hand">确认提交</span>
					</div>
					<div class="resultCon">
					 	<div class="resultKey">
					 		<div class="hd">
					 			<span id="editHelpID"></span>
					 			<span id="editHelpVersion"></span>
					 		</div>
					 		<span>上传理由：</span><input type="text" class="itemCon" id="addpri_reason" /><br>
					 	 	<span>上传列表：</span> 
					 	</div>
					 	<div class="answer_content">
					 	 	<table class="table table-hover table-bordered " id="helpUpdate_tbl">
					 			<thead>
					 				<th width="10%">模块名称</th>
					 				<th width="20%">标题</th>
					 				<th width="60%">内容</th>
					 				<th width="10%" class=" ">操作</th>
					 			</thead>
					 			<tbody>
					 				 
					 			</tbody>
					 		</table> 	
					 		<div id="ye2"></div>

					 		<div class="editDiv">
					 			<div class="modelSelect">
								 	<span class="itemTtl">模块：</span>
								 	<select  class="itemCon" id="helpModel">
								 		<option value="0">请选择</option>
								 		<option value="1">薪资宝</option>
								 		<option value="2">销售</option>
								 		<option value="3">物料</option>
								 		<option value="4">商品</option>
								 		<option value="5">资源中心</option>
								 	</select> 
								</div>
								<div class=" ">
								 	<span class="itemTtl">标题：</span>
								 	<input type="text" id="helpTtl" class="itemCon" />
								</div>
								<div class=" ">
								 	<span class="itemTtl">内容：</span><br>
								 	<textarea class="itemCon_textarea" id="helpContent"></textarea>
								</div>
								<div class="opItem">
								 	<div class=" opbtn azury saveHelpBtn" onclick="saveHelpItem()">保存</div>
								 	 
								</div>
					 		</div>
					 	</div>
					</div>
				</div>

			</div>
		</div>

		<%@ include  file="../../common/contentSliderLitbar.jsp"%>
	</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
	<script src="../css/content/about/about.js?v=SVN_REVISION" type="text/javascript"></script>
	<script src="../css/content/js/setPage.js?v=SVN_REVISION" type="text/javascript"></script>
 	<script src="../css/content/js/helpManage.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>