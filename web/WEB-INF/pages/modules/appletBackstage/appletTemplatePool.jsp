<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/applet/miniAppsManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%--提示--%>
    <div class="bonceContainer bounce-red" id="bindTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <p>微信小程序的注册申请已提交！</p>
                <p>微信官方将在3-10个工作日向您反馈审</p>
                <p>核结果，届时您将收到系统消息，请耐心等待！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="bounce.cancel()">知道了</span>
        </div>
    </div>
    <%--与Wonderss绑定小程序--%>
    <div class="bonceContainer bounce-blue" id="checkApp">
        <div class="bonceHead">
            <span>与Wonderss绑定小程序</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <p>请根据公司实际情况，在以下选项中选择</p>
                <div class="item">
                    <div class="ty-radio">
                        <input type="radio" name="changeImportManager" value="1" id="weDreact">
                        <label for="weDreact"></label> 注册一个新的微信小程序，直接注册，不使用已有的微信公众号
                    </div>
                </div>
                <div class="item">
                    <div class="ty-radio">
                        <input type="radio" name="changeImportManager" value="1" id="weSigned">
                        <label for="weSigned"></label> 注册一个新的微信小程序，使用已有、已完成认证的微信公众号
                    </div>
                </div>
                <div class="item">
                    <div class="ty-radio">
                        <input type="radio" name="changeImportManager" value="1" id="weExisting">
                        <label for="weDreact"></label> 使用已有的微信小程序
                    </div>
                </div>
                <div class="item">
                    <div class="ty-radio">
                        <input type="radio" name="changeImportManager" value="1" id="tikExisting">
                        <label for="weDreact"></label> 使用已有的字节小程序（抖音小程序）
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="checkApp()">下一步</span>
        </div>
    </div>
    <%--注册新的微信小程序--%>
    <div class="bonceContainer bounce-blue" id="addService" style="width:840px;">
        <div class="bonceHead">
            <span>注册新的微信小程序</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <div class="gatBt">注册新的微信小程序，需按真实情况填写或选择以下各项，提交后，还需微信公众平台审核通过</div>
            <div>
                <div class="ap-group">
                    <label><span class="xing"></span>企业名称（需与工商部门登记信息一致）</label>
                    <input type="text" class="form-control" />
                    <span class="ty-color-blue">是“无主体名称的个体工商户”的，填“个体户+法人姓名”，如“个体户张三”</span>
                </div>
                <div class="ap-group">
                    <label>企业代码类型</label>
                    <div>
                        <label class="radio-inline">
                            <input type="radio" value="option1"> 统一社会信用代码（18 位）
                        </label>
                        <label class="radio-inline">
                            <input type="radio" value="option1"> 组织机构代码（9位xxxxxxxx-x）
                        </label>
                        <label class="radio-inline">
                            <input type="radio" value="option1"> 营业执照注册号(15 位)
                        </label>
                    </div>
                </div>
                <div class="ap-group companyCodeArea">
                    <label><span class="xing"></span>企业代码</label>
                    <div class="notes">本框内仅可输入数字或英文字母，英文字母均按大写字母展示。选择企业代码类型后，本框方可填写！</div>
                    <div class="companyCodeData">
                        <div class="code code1">
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                        </div>
                        <div class="code code2">
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" /> -
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                        </div>
                        <div class="code code3">
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                        </div>
                    </div>
                </div>
                <div class="ap-group">
                    <label><span class="xing"></span>法人微信号</label>
                    <input type="text" class="form-control" />
                </div>
                <div class="ap-group">
                    <label><span class="xing"></span>法人真实姓名</label>
                    <input type="text" class="form-control" />
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addServiceSure()">确 定</span>
        </div>
    </div>
    <%--查看服务项目-周期--%>
    <div class="bonceContainer bounce-blue" id="seeServiceByCycle" style="width:800px;">
        <div class="bonceHead">
            <span>查看服务项目</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="add-block ty-color-orange" id="stopDetails">
                <span id="stopName">XXX</span>已于<span id="stopDate">XXXX-XX-XX XX:XX:XX</span>将本产品“停止生产”！</span>
            </div>
            <div class="hd serviceData"></div>
            <div class="scan-wrap">
                <div class="headTip"></div>
                <div class="clear">
                    <div class="ty-right">创建：<span class="seeCreater"></span></div>
                </div>
                <div class="elemFlex">
                    <div>项目的基础信息</div>
                    <div>
                        <span class="def-btn fun-btn" data-icon="1" data-type="updateService">修改</span>
                        <span class="def-btn fun-btn" data-icon="1" data-type="updateServiceRecords">修改记录</span>
                    </div>
                </div>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="12%">代号</td>
                        <td width="48%" colspan="3">名称</td>
                        <td width="40%" colspan="2">说明</td>
                    </tr>
                    <tr>
                        <td need data-name="code"></td>
                        <td need data-name="name" colspan="3"></td>
                        <td need data-name="memo" colspan="2"></td>
                    </tr>
                    <tr>
                        <td colspan="3">开增值税专用发票时</td>
                        <td rowspan="2">开普票时的开票单价</td>
                        <td rowspan="2">不开发票时的单价</td>
                        <td rowspan="2">参考单价</td>
                    </tr>
                    <tr>
                        <td>税率</td>
                        <td>不含税单价</td>
                        <td>含税单价</td>
                    </tr>
                    <tr>
                        <td need data-name="taxRate"></td>
                        <td need data-name="unitPrice"></td>
                        <td need data-name="unitPriceNotax"></td>
                        <td need data-name="unitPriceInvoice"></td>
                        <td need data-name="unitPriceNoinvoice"></td>
                        <td need data-name="unitPriceReference"></td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td colspan="5" need data-name="priceDesc"></td>
                    </tr>
                    </tbody>
                </table>
                <hr/>
                <div class="elemFlex">
                    <span>收费周期</span>
                    <span>每<span class="feeCycle"></span>收取一次</span>
                </div>
                <div class="elemFlex">
                    <span>收费时间</span>
                    <span><span class="timeCycle"></span>天内</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--查看服务项目-不按周期--%>
    <div class="bonceContainer bounce-blue" id="seeServiceNoCycle" style="width:900px;">
        <div class="bonceHead">
            <span>查看服务项目</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="add-block ty-color-orange stopDetails">
                <span class="stopName">XXX</span>已于<span class="stopDate">XXXX-XX-XX XX:XX:XX</span>将本产品“停止生产”！</span>
            </div>
            <div class="hd serviceData"></div>
            <div class="scan-wrap">
                <div class="copyArea">
                    <div class="headTip"></div>
                    <div class="clear">
                        <div class="ty-right">创建：<span class="seeCreater"></span></div>
                    </div>
                    <div class="elemFlex">
                        <div>项目的基础信息</div>
                        <div class="funBtns">
                            <span class="def-btn fun-btn" data-icon="0" data-type="updateService">修改</span>
                            <span class="def-btn fun-btn" data-icon="0" data-type="updateServiceRecords">修改记录</span>
                        </div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="12%">代号</td>
                            <td width="64%" colspan="4">名称</td>
                            <td width="24%">单位</td>
                        </tr>
                        <tr>
                            <td need data-name="code"></td>
                            <td need data-name="name" colspan="4"></td>
                            <td need data-name="unit"></td>
                        </tr>
                        <tr>
                            <td>说明</td>
                            <td need data-name="memo" colspan="5"></td>
                        </tr>
                        <tr>
                            <td colspan="3">开增值税专用发票时</td>
                            <td rowspan="2">开普票时的开票单价</td>
                            <td rowspan="2">不开发票时的单价</td>
                            <td rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr>
                            <td need data-name="taxRate"></td>
                            <td need data-name="unitPrice"></td>
                            <td need data-name="unitPriceNotax"></td>
                            <td need data-name="unitPriceInvoice"></td>
                            <td need data-name="unitPriceNoinvoice"></td>
                            <td need data-name="unitPriceReference"></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="5" need data-name="priceDesc"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <hr/>
                <div>
                    <div class="elemFlex modeNoSetted">
                        <div>
                            <div>服务费用的收取，有时与交付/验收有关，还可能分多期收取。</div>
                            <div>“模式设置”，相当于建立对服务费用收取的系统性管控。如需要，请设置！</div>
                            <div class="ty-color-blue">注：模式设置后，如需要还可重新设置。</div>
                        </div>
                        <div><span class="def-btn fun-btn withBold" data-type="edit_modeSetting">模式设置</span></div>
                    </div>
                    <div class="modeSettings">
                        <div>服务费用的收取模式已进行了设置，且该模式下已有数据。</div>
                        <div>该模式下的已有数据可修改，此外如需要，模式也可“重新设置”。</div>
                        <div class="ty-color-blue">“重新设置模式”时，已有数据将被清空。</div>
                        <div class="ty-right">
                            <span class="def-btn fun-btn withBold" data-type="editModeService" id="editModeService">修改当前数据</span>
                            <span class="def-btn fun-btn withBold" data-type="editModeLog">数据的修改记录</span>
                            <span class="def-btn fun-btn withBold" data-type="reModeSet">重新设置模式</span>
                        </div>
                        <div class="gapTp">
                            <div class="oneTime">
                                <div class="elemFlex">
                                    <span>收费时间</span>
                                    <div>
                                        <span class="timeCycle">XXXXXXX  XX</span>天内
                                    </div>
                                </div>
                            </div>
                            <div class="manyTime">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>模板库</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content">
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <input type="hidden" id="showMainConNum">
                    <div class="mainCon mainCon1">
                        <p>小程序模板库</p>
                        <div class="">
                            <ul class="tab_header clear">
                                <li class="ty-active">草稿箱</li>
                                <li>普通模板库</li>
                                <li>标准模板库</li>
                                <li>回收站</li>
                            </ul>
                            <div class="tbControl">
                                <table class="tbArea1">
                                    <thead>
                                    <tr>
                                        <td>模板代号</td>
                                        <td>模板名称</td>
                                        <td>版本号</td>
                                        <td>所属平台</td>
                                        <td>描述</td>
                                        <td>来源</td>
                                        <td>最近上传</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td></td>
                                    </tr>
                                    </tbody>
                                </table>
                                <table class="tbArea2">
                                    <thead>
                                    <tr>
                                        <td>模板代号</td>
                                        <td>模板名称</td>
                                        <td>版本号</td>
                                        <td>所属平台</td>
                                        <td>机构客户的数量</td>
                                        <td>用户总数</td>
                                        <td>在普通模板库的创建</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td></td>
                                    </tr>
                                    </tbody>
                                </table>
                                <table class="tbArea3">
                                    <thead>
                                    <tr>
                                        <td>版本号</td>
                                        <td>描述</td>
                                        <td>templateID</td>
                                        <td>添加到本模板库</td>
                                        <td>审核状态</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td></td>
                                    </tr>
                                    </tbody>
                                </table>
                                <table class="tbArea4">
                                    <thead>
                                    <tr>
                                        <td>模板代号</td>
                                        <td>模板名称</td>
                                        <td>版本号</td>
                                        <td>所属平台</td>
                                        <td>模板库</td>
                                        <td>创建</td>
                                        <td>停用</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/sales/serviceProjects.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
