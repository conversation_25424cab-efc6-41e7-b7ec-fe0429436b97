<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/accountant/voucherManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    td>div{ height:35px; margin:0 -15px; line-height:35px; border-bottom:1px solid #d7d7d7 ;  }
    td>div:last-child{ border-bottom:none;       }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_cancel()">确定</span>
        </div>
    </div>
    <%--  <div class="bonceContainer" id="subject_addAccount">
          <div class="bonceHead">
              <span>新增下级科目</span>
              <a class="bounce_close" onclick="subject_close()"></a>
          </div>
          <div class="bonceCon">
              <div class="addpayDetails">
                  <div>
                      <p class="subject_Input subject_bg">
                          <span>科目编号</span>
                          <input type="text" name="" value="1001001" disabled>
                      </p>
                      <p class="subject_Input">
                          <span>科目名称</span>
                          <input type="text" name="">
                      </p>
                      <p class="subject_Input subject_bg">
                          <span>上级科目</span>
                          <input type="text" name="" value="1001    库存现金" disabled>
                      </p>
                      <p class="subject_Input subject_bg">
                          <span>科目类别</span>
                          <input type="text" name="" value="流动资产" disabled>
                      </p>
                      <p class="subject_Input subject_bor">
                          <span>余额方向</span>
                          <input type="text" name="" value="借">
                      </p>
                      <p class="subject_Input">
                          <span>计量单位</span>
                          <input type="text" name="">
                      </p>

                  </div>
              </div>
          </div>
          <div class="bonceFoot">
              <span class="btn blue" onclick="subject_addsure()">确定</span>
              <span class="btn " onclick="subject_cancel()">取消</span>
          </div>
      </div>
      <div class="bonceContainer" id="subject_upAccount">
          <div class="bonceHead">
              <span>编辑科目</span>
              <a class="bounce_close" onclick="subject_close()"></a>
          </div>
          <div class="bonceCon">
              <div class="addpayDetails">
                  <div>
                      <p class="subject_Input subject_bg">
                          <span>科目编号</span>
                          <input type="text" name="" value="1001001" disabled>
                      </p>
                      <p class="subject_Input">
                          <span>科目名称</span>
                          <input type="text" name="">
                      </p>
                      <p class="subject_Input subject_bg">
                          <span>上级科目</span>
                          <input type="text" name="" value="1001    库存现金" disabled>
                      </p>
                      <p class="subject_Input subject_bg">
                          <span>科目类别</span>
                          <input type="text" name="" value="流动资产" disabled>
                      </p>
                      <p class="subject_Input subject_bor">
                          <span>余额方向</span>
                          <input type="text" name="" value="借">
                      </p>
                      <p class="subject_Input">
                          <span>计量单位</span>
                          <input type="text" name="">
                      </p>

                  </div>
              </div>
          </div>
          <div class="bonceFoot">
              <span class="btn blue" onclick="subjectup_sure()">确定</span>
              <span class="btn " onclick="subjectup_cancel()">取消</span>
          </div>
      </div>--%>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>凭证管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="Manager_search">
                    <span>查凭证</span>
                    <div class="ty-search">
                        <input class="ty-searchInput" autocomplete="off" id="searchDate" type="text">
                        <input class="ty-searchBtn" type="text">
                    </div>
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3">导出</button>
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="printVouncher()">打印</button>
                </div>
                <div class="ty-mainData">

                    <div class="opinionCon">

                        <table class="ty-table">
                            <thead>
                            <td><span type="checkbox" name="" class="check_all_false"  onclick="voucherBtn($(this))" id="checkList"></span></td>
                            <td>凭证日期</td>
                            <td>摘要</td>
                            <td>科目</td>
                            <td>借方金额</td>
                            <td>贷方金额</td>
                            <td>经手人</td>
                            <td>制单人</td>
                            <td>审核人</td>
                            </thead>
                            <tbody id="set_body">
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>

                            </tbody>
                        </table>

                    </div>
                </div>
                <div class="clr"></div>
            </div>

        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<div class="hd"  style="background: #fff!important;">
    <!--startprint1-->
    <div class="printCon">
        <div class="printItem">
            <div class="printRight">
                <p>[1/1]</p>
                <p>附件</p>
                <p></p>
                <p>张</p>
            </div>
            <div class="head">
                <p>现金付款凭证</p>
                <p>
                    <span>公司名称 <span>天津贝塔科技有限公司</span></span>
                    <span>日期 <span>2017年01月31日</span></span>
                    <span>凭证号 <span>（现付） 00003</span></span>
                </p>
            </div>
            <table>
                <thead>
                <td width="20%">摘要</td>
                <td width="35%">科目编号及名称</td>
                <td width="20%">借方金额</td>
                <td width="25%">贷方金额</td>
                </thead>
                <tbody border="1" cellspacing="0">
                <tr>
                    <td>付工资</td>
                    <td>2241006 其他应付款-薪资宝</td>
                    <td>91858.20</td>
                    <td></td>
                </tr>
                <tr>
                    <td>付工资</td>
                    <td>21001 库存现金</td>
                    <td></td>
                    <td>91858.20</td>
                </tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td>合计</td><td>玖万壹仟捌佰伍拾八元贰角整</td><td>91858.20</td><td>91858.20</td></tr>
                </tbody>
            </table>
            <div class="foot">
                <span>会计主管 <span>郭玉俊</span></span>
                <span>记账 <span>宋红艳</span></span>
                <span>复核 <span>宋红艳</span></span>
                <span>出纳 <span>郭玉俊</span></span>
                <span>制单 <span>郭玉俊</span></span>
            </div>
        </div>
        <div class="printItem">
            <div class="printRight">
                <p>[1/1]</p>
                <p>附件</p>
                <p></p>
                <p>张</p>
            </div>
            <div class="head">
                <p>现金付款凭证</p>
                <p>
                    <span>公司名称 <span>天津贝塔科技有限公司</span></span>
                    <span>日期 <span>2017年01月31日</span></span>
                    <span>凭证号 <span>（现付） 00003</span></span>
                </p>
            </div>
            <table>
                <thead>
                <td width="20%">摘要</td>
                <td width="35%">科目编号及名称</td>
                <td width="20%">借方金额</td>
                <td width="25%">贷方金额</td>
                </thead>
                <tbody border="1" cellspacing="0">
                <tr>
                    <td>付工资</td>
                    <td>2241006 其他应付款-薪资宝</td>
                    <td>91858.20</td>
                    <td></td>
                </tr>
                <tr>
                    <td>付工资</td>
                    <td>21001 库存现金</td>
                    <td></td>
                    <td>91858.20</td>
                </tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td>合计</td><td>玖万壹仟捌佰伍拾八元贰角整</td><td>91858.20</td><td>91858.20</td></tr>
                </tbody>
            </table>
            <div class="foot">
                <span>会计主管 <span>郭玉俊</span></span>
                <span>记账 <span>宋红艳</span></span>
                <span>复核 <span>宋红艳</span></span>
                <span>出纳 <span>郭玉俊</span></span>
                <span>制单 <span>郭玉俊</span></span>
            </div>
        </div>
        <div class="printItem">
            <div class="printRight">
                <p>[1/1]</p>
                <p>附件</p>
                <p></p>
                <p>张</p>
            </div>
            <div class="head">
                <p>现金付款凭证</p>
                <p>
                    <span>公司名称 <span>天津贝塔科技有限公司</span></span>
                    <span>日期 <span>2017年01月31日</span></span>
                    <span>凭证号 <span>（现付） 00003</span></span>
                </p>
            </div>
            <table>
                <thead>
                <td width="20%">摘要</td>
                <td width="35%">科目编号及名称</td>
                <td width="20%">借方金额</td>
                <td width="25%">贷方金额</td>
                </thead>
                <tbody border="1" cellspacing="0">
                <tr>
                    <td>付工资</td>
                    <td>2241006 其他应付款-薪资宝</td>
                    <td>91858.20</td>
                    <td></td>
                </tr>
                <tr>
                    <td>付工资</td>
                    <td>21001 库存现金</td>
                    <td></td>
                    <td>91858.20</td>
                </tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td>合计</td><td>玖万壹仟捌佰伍拾八元贰角整</td><td>91858.20</td><td>91858.20</td></tr>
                </tbody>
            </table>
            <div class="foot">
                <span>会计主管 <span>郭玉俊</span></span>
                <span>记账 <span>宋红艳</span></span>
                <span>复核 <span>宋红艳</span></span>
                <span>出纳 <span>郭玉俊</span></span>
                <span>制单 <span>郭玉俊</span></span>
            </div>
        </div>
        <div class="printItem">
            <div class="printRight">
                <p>[1/1]</p>
                <p>附件</p>
                <p></p>
                <p>张</p>
            </div>
            <div class="head">
                <p>现金付款凭证</p>
                <p>
                    <span>公司名称 <span>天津贝塔科技有限公司</span></span>
                    <span>日期 <span>2017年01月31日</span></span>
                    <span>凭证号 <span>（现付） 00003</span></span>
                </p>
            </div>
            <table>
                <thead>
                <td width="20%">摘要</td>
                <td width="35%">科目编号及名称</td>
                <td width="20%">借方金额</td>
                <td width="25%">贷方金额</td>
                </thead>
                <tbody border="1" cellspacing="0">
                <tr>
                    <td>付工资</td>
                    <td>2241006 其他应付款-薪资宝</td>
                    <td>91858.20</td>
                    <td></td>
                </tr>
                <tr>
                    <td>付工资</td>
                    <td>21001 库存现金</td>
                    <td></td>
                    <td>91858.20</td>
                </tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td></td><td></td><td></td><td></td></tr>
                <tr><td>合计</td><td>玖万壹仟捌佰伍拾八元贰角整</td><td>91858.20</td><td>91858.20</td></tr>
                </tbody>
            </table>
            <div class="foot">
                <span>会计主管 <span>郭玉俊</span></span>
                <span>记账 <span>宋红艳</span></span>
                <span>复核 <span>宋红艳</span></span>
                <span>出纳 <span>郭玉俊</span></span>
                <span>制单 <span>郭玉俊</span></span>
            </div>
        </div>
    </div>
    <!--endprint1-->
</div>

<script src="../script/accountant/voucherManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>

