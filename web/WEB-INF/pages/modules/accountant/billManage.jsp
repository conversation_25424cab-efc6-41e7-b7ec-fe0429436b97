<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/accountant/billManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/subjectChoose.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/subjectsTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce">
    <div class="bonceContainer bounce-red" id="errorTip" style="width: 450px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--不予下账”的票据--%>
    <div class="bonceContainer bounce-blue" id="noaccount">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>票据日期在<span class="bothPriodInfo"></span>之间、已选为“不予下账”的票据共<span class="bothPriodCount"></span>条，其中</p>
            <div class="bothPriod">
                <ul>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关 闭</span>
        </div>
    </div>
    <%--不予下账”的票据-按月份展示--%>
    <div class="bonceContainer bounce-blue" id="noaccountByMonth">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>票据日期为<span class="bothPriodInfo"></span>、已选为“不予下账”的票据有<span class="bothPriodCount"></span>条，其中</p>
            <div class="bothPriodByMonth">
                <ul class="clear">
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关 闭</span>
        </div>
    </div>

    <%--选择科目--%>
    <div class="bonceContainer bounce-blue" id="choose_addAccount" style="width: 1000px;">
        <div class="bonceHead source">
            <span>选择科目</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>

        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con clearfix">
                <div class="ty-right">
                    <span class="ty-btn ty-btn-blue ty-circle-3 ty-btn-big" onclick="accountingDetailBtn()">数据详情</span>
                </div>
                <div class="inputPart">
                    <%--<div class="headInfo">
                        <div class="normalItem">
                            <span>凭证日期：</span>
                            <select class="subjectInput voucher" name="bill_period">
                                <option value="2">上月凭证</option>
                                <option value="1">本月凭证</option>
                            </select>
                        </div>
                    </div>--%>
                    <div class="borrowMoreLoans" style="max-height: 319px;overflow-y: auto;overflow-x: hidden"></div>
                    <div class="special"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--  <div class="bonceContainer" id="subject_addAccount">
          <div class="bonceHead">
              <span>新增下级科目</span>
              <a class="bounce_close" onclick="subject_close()"></a>
          </div>
          <div class="bonceCon">
              <div class="addpayDetails">
                  <div>
                      <p class="subject_Input subject_bg">
                          <span>科目编号</span>
                          <input type="text" name="" value="1001001" disabled>
                      </p>
                      <p class="subject_Input">
                          <span>科目名称</span>
                          <input type="text" name="">
                      </p>
                      <p class="subject_Input subject_bg">
                          <span>上级科目</span>
                          <input type="text" name="" value="1001    库存现金" disabled>
                      </p>
                      <p class="subject_Input subject_bg">
                          <span>科目类别</span>
                          <input type="text" name="" value="流动资产" disabled>
                      </p>
                      <p class="subject_Input subject_bor">
                          <span>余额方向</span>
                          <input type="text" name="" value="借">
                      </p>
                      <p class="subject_Input">
                          <span>计量单位</span>
                          <input type="text" name="">
                      </p>

                  </div>
              </div>
          </div>
          <div class="bonceFoot">
              <span class="btn blue" onclick="subject_addsure()">确定</span>
              <span class="btn " onclick="subject_cancel()">取消</span>
          </div>
      </div>
      <div class="bonceContainer" id="subject_upAccount">
          <div class="bonceHead">
              <span>编辑科目</span>
              <a class="bounce_close" onclick="subject_close()"></a>
          </div>
          <div class="bonceCon">
              <div class="addpayDetails">
                  <div>
                      <p class="subject_Input subject_bg">
                          <span>科目编号</span>
                          <input type="text" name="" value="1001001" disabled>
                      </p>
                      <p class="subject_Input">
                          <span>科目名称</span>
                          <input type="text" name="">
                      </p>
                      <p class="subject_Input subject_bg">
                          <span>上级科目</span>
                          <input type="text" name="" value="1001    库存现金" disabled>
                      </p>
                      <p class="subject_Input subject_bg">
                          <span>科目类别</span>
                          <input type="text" name="" value="流动资产" disabled>
                      </p>
                      <p class="subject_Input subject_bor">
                          <span>余额方向</span>
                          <input type="text" name="" value="借">
                      </p>
                      <p class="subject_Input">
                          <span>计量单位</span>
                          <input type="text" name="">
                      </p>

                  </div>
              </div>
          </div>
          <div class="bonceFoot">
              <span class="btn blue" onclick="subjectup_sure()">确定</span>
              <span class="btn " onclick="subjectup_cancel()">取消</span>
          </div>
      </div>--%>

</div>
<div class="bounce_Fixed">
    <%@ include  file="subjectTree.jsp"%>
    <div class="bonceContainer bounce-blue" id="tips"  style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="content" style="text-align: center">
                <div class="tip"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="accountingDetail" style="width: 600px;">
        <div class="bonceHead">
            <span>数据详情</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="detailed"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--报销数据查看--%>
    <div class="bonceContainer bounce-blue" id="reimburse" style="width: 900px">
        <div class="bonceHead">
            <span class="bonceHeadName">报销查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-top: 12px">
                <div><div class="item-title"><b>摘要</b></div> <div class="item-content-big summary"></div></div>
                <div><div class="item-title"><b>用途</b></div> <div class="item-content-big purpose"></div>
                </div>
            </div>
            <div class="billDetail" style="margin-top: 12px">
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td>票据种类</td>
                        <td>单张票据金额</td>
                        <td>票据数量</td>
                        <td>票据金额合计</td>
                        <td>申请报销的金额</td>
                        <td>查看</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 报销 - 票据内容 --%>
    <div class="bonceContainer bounce-blue" id="billInfo" style="width: 1200px">
        <div class="bonceHead">
            <span class="bonceHeadName">票据内容</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <%--<div class="item">--%>
            <%--<div class="item-title">票据所属月份</div>--%>
            <%--<div class="item-content">2019-5</div>--%>
            <%--</div>--%>
            <div>
                <%--增值税专用发票，增值税普通发票货物--%>
                <table class="ty-table ty-table-control VATGood">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>税率</td>
                        <td>税额</td>
                        <td>含税合计</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--定额普通发票--%>
                <div class="quotaGood" style="display: none">
                    <div class="item">
                        <div class="item-title">票据种类</div>
                        <div class="item-content billCatName">定额普通发票</div>
                    </div>
                    <div class="item">
                        <div class="item-title">费用类别</div>
                        <div class="item-content feeCatName">交通费-其他</div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td>单张票据金额</td>
                            <td>数量</td>
                            <td>合计</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <%--其他普通发票货物--%>
                <table class="ty-table ty-table-control otherGood" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--收据货物--%>
                <table class="ty-table ty-table-control receipt" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>物品数量</td>
                        <td>单价</td>
                        <td>金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="item text-right singleAmount">
                （单张票据金额）价税合计 <span class="amount ty-color-blue"></span> 元
            </div>
            <div class="item billQuantity text-right">
            </div>
            <div class="item">
                <div class="item-title">备注</div>
                <div class="item-content memo"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed2.cancel()">确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>凭证管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainData">
                    <div class="mainCon mainCon1" style="width: 900px;">
                        <div class="settle-date">做凭证需按月进行。目前，需结账的月份为<span id="needMonth"></span></div>
                        <div>
                            <div class="vcher-mod">
                                <div class="row-con">
                                    <div class="ty-left">
                                        <div>
                                            <span class="con-txt">目前系统中，可供制作本月凭证的票据</span>
                                            共<span class="countNum" data-type="billQuantity"></span>条
                                        </div>
                                        <div class="careful">注：“查看/选择科目”页上，可对各票据选择科目。之后，这些票据将进入“手动选择了科目的凭证”</div>
                                    </div>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 fun-btn" data-type="subjectOperate">查看/选择科目</span>
                                </div>
                            </div>
                            <div class="vcher-mod">
                                <div class="row-con">
                                    <div class="ty-left">
                                        <div>
                                            <span class="con-txt">本月已有科目的凭证中，手动选择的</span>
                                            共<span class="countNum" data-type="manualSelection"></span>条
                                        </div>
                                        <div class="careful">注：结账结束前，这些凭证的科目均可修改</div>
                                    </div>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 scanBtn fun-btn" data-type="manualsScan">查 看</span>
                                </div>
                                <div class="row-con">
                                    <div class="ty-left">
                                        <div>
                                            <span class="con-txt">本月已有科目的凭证中，由系统选择的</span>
                                            共<span class="countNum" data-type="osSelection"></span>条
                                        </div>
                                        <div class="careful">注：结账结束前，这些凭证的科目均可修改。修改时，既可逐条进行，也可通过设置进行整体性修改</div>
                                    </div>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 scanBtn fun-btn" data-type="osScan">查 看</span>
                                </div>
                                <div class="row-con">
                                    <div class="ty-left">
                                        <div>
                                            <span class="con-txt">本月已有科目的凭证中，为会计录入的</span>
                                            共<span class="countNum" data-type="accountantSelection"></span>条
                                        </div>
                                        <div class="careful">注：结账结束前，会计手动录入的凭证，既可录入更多，也可修改</div>
                                    </div>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 scanBtn fun-btn" data-type="accountantlsScan">查 看</span>
                                </div>
                            </div>
                            <div class="vcher-mod-no">
                                <div class="row-con">
                                    <div class="ty-left sysHd">此外，系统中</div>
                                    <div class="ty-left">
                                        <div>
                                            <span class="con-txt-w">尚未入会计帐的票据</span>
                                            共<span class="countNum" data-type="billsNotRecorded"></span>条
                                        </div>
                                    </div>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 scanBtn fun-btn" data-type="noAccountingScan" data-icon="1">查 看</span>
                                </div>
                                <div class="row-con-no">
                                    <div class="ty-left gapHd">
                                        <div>
                                            <span class="con-txt-w">已制作过凭证但选为“不予做账”的</span>
                                            共<span class="countNum" data-type="billsNotAccount"></span>条
                                        </div>
                                    </div>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 scanBtn fun-btn" data-type="markNoAccounting" data-icon="1">查 看</span>
                                </div>
                                <div class="careful">注：该数据为开始使用系统以来的全部数据，选择时请避免选择不宜入账的陈年数据！</div>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon mainCon2">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="reback()">返 回</span>
                        </div>
                        <div class="count-con">
                            <p class="tickDetails">目前，系统内尚未入会计帐的票据共条，其中</p>
                            <ul class="notRecordedlist">
                                <li>
                                    <span class="count-it">票据日期为XXXX年XX月的共XXX条</span>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3 fun-btn" data-type="getList">查 看</span>
                                </li>
                                <li>
                                    <span class="count-it">票据日期为XXXX年XX月的共XXX条</span>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3">查 看</span>
                                </li>
                                <li>
                                    <span class="count-it">票据日期为XXXX年XX月的共XXX条</span>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3">查 看</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="mainCon mainCon3">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="reback(2)">返 回</span>
                        </div>
                        <div class="count-con">
                            <p class="tickDetails">目前，系统内已选为“不予做账”的票据共条，其中</p>
                            <ul>
                                <li>
                                    <span class="count-it">票据日期为XXXX年XX月的共XXX条</span>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3">查 看</span>
                                </li>
                                <li>
                                    <span class="count-it">票据日期为XXXX年XX月的共XXX条</span>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3">查 看</span>
                                </li>
                                <li>
                                    <span class="count-it">票据日期为XXXX年XX月的共XXX条</span>
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3">查 看</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="mainCon mainCon4">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="reback(3)">返 回</span>
                        </div>
                        <div class="count-con">
                            <p class="tickDetails">目前，系统内尚未入会计帐、票据日期为XXXX年XX月的票据共如下XXX条</p>
                            <table class="ty-table">
                                <thead>
                                <td width="12%">摘要</td>
                                <td width="10%">收入</td>
                                <td width="10%">支出</td>
                                <td width="7%">票据数量</td>
                                <td width="10%">票据日期</td>
                                <td width="12%">用途</td>
                                <td width="18%">创建</td>
                                <td width="13%">备注</td>
                                <td width="8%">操作</td>
                                </thead>
                                <tbody>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mainCon mainCon5">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="reback()">返 回</span>
                            <div class="ty-right">
                                <span class="search">查凭证
                                    <input type="text" placeholder="请输入凭证金额、经手人或摘要内可能包含的内容"><span onclick="voucherSearch($(this))">确定</span>
                                </span>
                            </div>
                        </div>
                        <div>
                            <div class="voucherParam hd"></div>
                            <div class="tblVoucher2">
                                <p class="voucherDetails"></p>
                                <table class="ty-table">
                                    <thead>
                                    <td width="8%">凭证日期</td>
                                    <td width="8%">票据日期</td>
                                    <td width="12%">摘要</td>
                                    <td width="12%">科目</td>
                                    <td width="10%">借方金额</td>
                                    <td width="10%">贷方金额</td>
                                    <td width="16%">创建</td>
                                    <td width="16%">制单</td>
                                    <td width="8%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon mainCon6">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 fun-btn" data-type="chooseBack">返 回</span>
                        </div>
                        <div class="cashFlowOption hd"></div>
                        <div class="ty-secondTab" id="secondNav">
                            <ul id="tab1">
                                <li class="ty-active">正常待选择（<span class="count1"></span>条）</li><%--与修改待选择长得一样--%>
                                <li>修改待选择（<span class="count2"></span>条）</li><%--与修改待选择长得一样--%>
                                <li>去年与今年的“不予下账”（<span class="count3"></span>条）</li><%--与正常待选择长得一样--%>
                            </ul>
                        </div>
                        <div id="tblCon" class="subjectChoose">
                            <div class="tblCon0">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td width="12%">摘要</td>
                                        <td width="10%">收入</td>
                                        <td width="10%">支出</td>
                                        <td width="10%">票据数量</td>
                                        <td width="10%">票据日期</td>
                                        <td width="12%">用途</td>
                                        <td width="15%">创建</td>
                                        <td width="13%">备注</td>
                                        <td width="8%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <div class="tblCon1">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td width="12%">摘要</td>
                                        <td width="10%">收入</td>
                                        <td width="10%">支出</td>
                                        <td width="10%">票据数量</td>
                                        <td width="10%">票据日期</td>
                                        <td width="12%">用途</td>
                                        <td width="15%">创建</td>
                                        <td width="13%">备注</td>
                                        <td width="8%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <div class="tblCon2">
                                <div class="regulation">
                                    <p>去年与今年“不予下账”的票据中</p>
                                    <p>
                                        <span>票据日期为<span class="settleMonth"></span>的票据共<span class="settleMonthcount"></span>条，如下所示</span>
                                        <span class="ct-essay">票据日期在<span class="lastToNow"></span>之间的票据</span>
                                        <span class="ty-color-blue fun-btn" data-type="noVoucherCount">查看</span>
                                        <span class="hd nearlyTwoYears"></span>
                                    </p>
                                </div>
                                <div>认为可入<span class="settleMonth"></span>会计账的数据，请点击该条操作下的“修改”</div>
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td width="12%">摘要</td>
                                        <td width="10%">收入</td>
                                        <td width="10%">支出</td>
                                        <td width="10%">票据数量</td>
                                        <td width="10%">票据日期</td>
                                        <td width="12%">用途</td>
                                        <td width="15%">创建</td>
                                        <td width="13%">备注</td>
                                        <td width="8%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon mainCon7">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="reback(6)">返 回</span>
                        </div>
                        <div class="subjectChoose">
                            <p class="tickDetails"></p>
                            <p>您如认为那笔还可入<span class="settleMonth"></span>的会计账，请点击“修改”</p>
                            <div class="noVoucherCount">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td width="12%">摘要</td>
                                        <td width="10%">收入</td>
                                        <td width="10%">支出</td>
                                        <td width="10%">票据数量</td>
                                        <td width="10%">票据日期</td>
                                        <td width="12%">用途</td>
                                        <td width="15%">创建</td>
                                        <td width="13%">备注</td>
                                        <td width="8%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon mainCon8">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 fun-btn" onclick="reback(5)">返 回</span>
                        </div>
                        <div>
                            <table class="ty-table scTab2">
                                <thead>
                                <td width="10%">凭证日期</td>
                                <td width="10%">票据日期</td>
                                <td width="10%">摘要</td>
                                <td width="10%">科目</td>
                                <td width="10%">借方金额</td>
                                <td width="10%">贷方金额</td>
                                <td width="16%">创建</td>
                                <td width="16%">制单</td>
                                <td width="8%">操作</td>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="clr"></div>
            </div>

        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/accountant/billManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/subjectChooseCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/subjectsTree1.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/voucherCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>

