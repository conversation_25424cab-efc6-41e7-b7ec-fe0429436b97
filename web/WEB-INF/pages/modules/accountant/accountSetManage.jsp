<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/authority.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/subjectSet.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    #prom{ display: inline-block; margin-left: 50px; color:#f58410 ;  }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce">
    <div class="bonceContainer bounce-green" id="checkout" style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p style="text-align: center; padding:10px 0">您确定要结账吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="sureCheckout()">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="tip" style="width:500px; ">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="tipMs" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--重新建账提示--%>
    <div class="bonceContainer bounce-blue" id="rebuild_tip" style="width:500px; ">
        <div class="bonceHead">
            <span>！提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p>重新建账后，您将无法在系统中看到之前的会计数据。 </p>
                    <p>您确定重新建账吗？ </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="rebuildAccount()">确定，重新登录</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--科目变动记录--%>
    <div class="bonceContainer bounce-blue" id="accountChangeRecord">
        <div class="bonceHead">
            <span>变动内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="hd" id="recordInfo"></div>
            <p><span id="re-changeDate"></span>的科目变动记录</p>
            <table class="ty-table ty-table-control">
                <tbody>
                <tr>
                    <td>操作内容简述</td>
                    <td>操作者</td>
                    <td>操作内容详述</td>
                </tr>
                <tr>
                    <td></td>
                    <td></td>
                    <td><span class="funbtn ty-color-blue" data-fun="afterCreation">建账后的科目</span></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel();">关 闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--修改前  修改后--%>
    <div class="bonceContainer bounce-green" id="subject_updateScan">
        <div class="bonceHead">
            <span>修改前</span>
            <a class="bounce_close hand" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con su_pack">
                <div>
                    <p class="subject_Input subject_bg">
                        <span>科目编号</span>
                        <span class="con_scan" data-name="subject">科目编号</span>
                    </p>
                    <p class="subject_Input">
                        <span>科目名称</span>
                        <span class="con_scan" data-name="name">科目编号</span>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>上级科目</span>
                        <span class="con_scan" data-name="parent">科目编号</span>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>科目类别</span>
                        <span class="con_scan" data-name="categoryName">科目编号</span>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>余额方向</span>
                        <span class="con_scan" data-name="balanceDirection">科目编号科目编号</span>
                    </p>
                    <div class="subject_Input">
                        <span>数量核算</span>
                        <div class='ty-form-checkbox' skin="green" style="margin-left: 25px;"><i
                                class="fa fa-check"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>建账管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <input type="hidden" id="showMainConNum">
                <div class="mainCon mainCon1">
                    <div>
                        <span>以下为本公司的建账记录。需要时，可“重新建账”。</span>
                        <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5" id="rebuildAccount">重新建账</span>
                    </div>
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td>事件</td>
                            <td>建账/重新建帐的操作者</td>
                            <td>建账/重新建帐完成的时间</td>
                            <td>科目变动记录</td>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <div class="mainCon mainCon2">
                    <div class="back-btn">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback()">返回</span>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td rowspan="2">变动日期</td>
                            <td colspan="4">操作后各级科目的数量</td>
                            <td rowspan="2">变动内容</td>
                            <td rowspan="2">变动后的科目</td>
                        </tr>
                        <tr>
                            <td>一级科目</td>
                            <td>二级科目</td>
                            <td>三级科目</td>
                            <td>四级科目</td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="changeContent_ye"></div>
                </div>
                <div class="mainCon mainCon3">
                    <div class="back-btn">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback(2)">返回</span>
                    </div>
                    <ul class="ty-secondTab" id="subjectScan">
                        <li class="ty-active" value="1">
                            资产类
                        </li>
                        <li value="2">
                            负债类
                        </li>
                        <li value="3">
                            所有者权益类
                        </li>
                        <li value="4">
                            成本类
                        </li>
                        <li value="5">
                            损益类
                        </li>
                    </ul>
                    <div class="tbScroll">
                        <div id="subChangeParam" class="hd"></div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td width="25%">编码</td>
                                <td width="25%">名称</td>
                                <td width="20%">类别</td>
                                <td width="15%">余额方向</td>
                                <td width="15%">状态</td>
                            </tr>
                            </thead>
                            <tbody id="subChange">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/accountant/accountSetManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/subjectJournal.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

