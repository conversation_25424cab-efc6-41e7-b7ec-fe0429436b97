<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/subjectsTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/subjectChoose.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce_Fixed2">
    <%-- 报销 - 票据内容 --%>
    <div class="bonceContainer bounce-blue" id="billInfo" style="width: 1200px">
        <div class="bonceHead">
            <span class="bonceHeadName">票据内容</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <%--<div class="item">--%>
            <%--<div class="item-title">票据所属月份</div>--%>
            <%--<div class="item-content">2019-5</div>--%>
            <%--</div>--%>
            <div>
                <%--增值税专用发票，增值税普通发票货物--%>
                <table class="ty-table ty-table-control VATGood">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>税率</td>
                        <td>税额</td>
                        <td>含税合计</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--定额普通发票--%>
                <div class="quotaGood" style="display: none">
                    <div class="item">
                        <div class="item-title">票据种类</div>
                        <div class="item-content billCatName">定额普通发票</div>
                    </div>
                    <div class="item">
                        <div class="item-title">费用类别</div>
                        <div class="item-content feeCatName">交通费-其他</div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td>单张票据金额</td>
                            <td>数量</td>
                            <td>合计</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <%--其他普通发票货物--%>
                <table class="ty-table ty-table-control otherGood" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--收据货物--%>
                <table class="ty-table ty-table-control receipt" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>物品数量</td>
                        <td>单价</td>
                        <td>金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="item text-right singleAmount">
                （单张票据金额）价税合计 <span class="amount ty-color-blue"></span> 元
            </div>
            <div class="item billQuantity text-right">
            </div>
            <div class="item">
                <div class="item-title">备注</div>
                <div class="item-content memo"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed2.cancel()">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%@ include  file="subjectTree.jsp"%>
    <div class="bonceContainer bounce-blue" id="tips"  style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="content" style="text-align: center">
                <div class="tip"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="mtTip1"  style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1"  style="text-align: center"></div>
            </div>
        </div>
        <div class="bonceFoot">

        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="confirm" style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="confirmWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">

        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="accountingDetail" style="width: 600px;">
        <div class="bonceHead">
            <span>数据详情</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="detailed"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--报销数据查看--%>
    <div class="bonceContainer bounce-blue" id="reimburse" style="width: 900px">
        <div class="bonceHead">
            <span class="bonceHeadName">报销查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-top: 12px">
                <div><div class="item-title"><b>摘要</b></div> <div class="item-content-big summary"></div></div>
                <div><div class="item-title"><b>用途</b></div> <div class="item-content-big purpose"></div>
                </div>
            </div>
            <div class="billDetail" style="margin-top: 12px">
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td>票据种类</td>
                        <td>单张票据金额</td>
                        <td>票据数量</td>
                        <td>票据金额合计</td>
                        <td>申请报销的金额</td>
                        <td>查看</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip" style="width: 450px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--选择科目--%>
    <div class="bonceContainer bounce-blue" id="choose_addAccount" style="width: 1000px;">
        <div class="bonceHead source">
            <span>选择科目</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>

        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con clearfix">
                <div class="ty-right">
                    <span class="ty-btn ty-btn-blue ty-circle-3 ty-btn-big" onclick="accountingDetailBtn()">数据详情</span>
                </div>
                <div>
                    <div class="headInfo">
                        <div class="normalItem">
                            <span>凭证日期：</span>
                            <select class="subjectInput voucher" name="bill_period">
                                <option value="2">上月凭证</option>
                                <option value="1">本月凭证</option>
                            </select>
                        </div>
                    </div>
                    <div class="borrowMoreLoans" style="max-height: 319px;overflow-y: auto;overflow-x: hidden"></div>
                    <div class="special"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--不予下账待审批的数据明细--%>
    <div class="bonceContainer bounce-blue" id="pend_addAccount" style="width: 1200px;">
        <div class="bonceHead">
            <span>数据明细</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con clearfix">
                <div class="ty-right">
                    <span class="ty-btn ty-btn-blue ty-circle-3 ty-btn-big" onclick="accountingDetailBtn()">数据详情</span>
                </div>
                <div>
                    <div class="headInfo">
                        <div class="normalItem">
                            <span>凭证日期：</span>
                            <select class="subjectInput voucher" name="bill_period">
                                <option value="2">上月凭证</option>
                                <option value="1">本月凭证</option>
                            </select>
                        </div>
                    </div>
                    <div class="borrowMoreLoans" style="max-height: 319px;overflow-y: auto;overflow-x: hidden"></div>
                    <div class="special"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>科目选择</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div class="cashFlowOption hd"></div>
            <div class="ty-container" id="Con_continer">
                <span class="hd navTxt" id="firstTag"><i class=""></i>待选择</span>
                <span class="hd nav_"> / </span>
                <span class="hd navTxt" id="secondTag"><i class=""></i>正常待选择</span>

                <div class="content"></div>
                <ul class="ty-firstTab">
                    <li to="tab1" id="tabChoose" class="ty-active"> 待选择</li>
                    <li to="tab2" id="tabApprove">待审批</li>
                    <li to="tab3" id="tabAgree">  已批准</li>
                    <li to="tab4" id="tabReject"> 已驳回</li>
                </ul>

                <div class="main">
                    <div class="ty-secondTab" id="secondNav">
                        <ul id="tab1">
                            <li type="1" class="ty-active">正常待选择</li>
                            <li type="2">修改待选择</li>
                        </ul>
                        <ul id="tab2" style="display: none">
                            <li state="1">正常待审批</li><%--与修改待选择长得一样--%>
                            <li state="4">修改待审批</li><%--与修改待选择长得一样--%>
                            <li state="5">不予下账待审批</li><%--与正常待选择长得一样--%>
                        </ul>
                        <ul id="tab3" style="display: none">
                            <li state="2">正常已批准</li><%--与修改待选择长得一样--%>
                            <li state="7">修改已批准</li><%--与修改待选择长得一样--%>
                            <li state="9">不予下账已批准</li><%--与正常待选择长得一样--%>
                        </ul>
                        <ul id="tab4" style="display: none">
                            <li state="3">正常已驳回</li><%--与修改待选择长得一样--%>
                            <li state="8">修改已驳回</li><%--与修改待选择长得一样--%>
                            <li state="6">不予下账已驳回</li><%--与正常待选择长得一样--%>
                        </ul>
                    </div>
                    <div class="ty-mainData" id="tblCon">
                        <table class="ty-table ty-table-control">
                            <thead></thead>
                            <tbody></tbody>
                        </table>
                        <div></div>
                    </div>
                </div>
                <div class="clr"></div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<%--<script src="${pageContext.request.contextPath }/script/accountant/subjectsTree.js" type="text/javascript"></script>--%>
<script src="../script/accountant/subjectsTree1.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/subjectChoose.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/subjectChooseCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>

