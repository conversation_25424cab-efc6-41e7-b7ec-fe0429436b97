<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    #laydate_today{ display: none!important; }
    .ty-table td {
        text-align: left;
    }
    .ty-table thead td {
        border-color: #d7d7d7;
        background: #f2f2f2;
        text-align: center;
    }
    .ty-table td>span[level='0']{
        font-weight: bold;
    }
    .ty-table td>span[level='1']{
        margin-left: 30px;
    }
    .ty-table td>span[level='2']{
        margin-left: 50px;
    }
    .ty-table td>span[level='3']{
        margin-left: 90px;
    }
    .laydate_table thead,.laydate_table tbody{
        display: none;
    }
    .carryover{
        background-color: #dff0ff;
        border: 1px solid #e3e3e3;
        display: inline-block;
        height: 30px;
        line-height: 30px;
        min-width: 182px;
    }
    .carryover>.carryoverType{
        padding: 0 8px;
        font-size: 12px;
        color: #999;
    }
    .carryover>.carryoverType i{
        font-size: 14px;
        margin-right: 5px;
    }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="F_tip" style="width: 450px">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"> </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="F_errorTip" style="width: 450px">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"> </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="tallage" style="width: 450px">
        <div class="bonceHead">
            <span>请选择报税日期</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
             <p>报税日期 <input type="text" id="tallageDate" onclick="showTB()" /></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="tallage()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="tip" style="width: 450px">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p id="tipMs" style="text-align: center; padding:10px 0"> </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip" style="width: 450px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="lossAndGainBroughtForward" style="width: 500px;">
        <div class="bonceHead">
            <span>结转损益</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <table class="ty-table ty-table-none">
                <tr>
                    <td>凭证字</td>
                    <td><input type="text" class="ty-inputText" disabled placeholder="转"></td>
                </tr>
                <tr>
                    <td>凭证摘要</td>
                    <td><input type="text" class="ty-inputText summary"></td>
                </tr>
                <tr>
                    <td>结转类型</td>
                    <td>
                        <div class="carryover">
                            <span class="carryoverType"><i class="fa fa-circle-o"></i>收入</span>
                            <span class="carryoverType"><i class="fa fa-circle-o"></i>支出</span>
                            <span class="carryoverType"><i class="fa fa-dot-circle-o"></i>损益</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>本年利润科目</td>
                    <td><input type="text" class="ty-inputText" disabled placeholder="3103"></td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureLossAndGainBroughtForward()">生成凭证</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="checkout" style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p style="text-align: center; padding:10px 0">您确定要结账吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="sureCheckout()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="counterCheck" style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p style="text-align: center; padding:10px 0">您确定要反结账吗</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="sureCounterCheck()">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>对公报表</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
                <div class="ty-container">
                    <div class="ty-search">
                        <input type="text" class="ty-searchInput" autocomplete="off" id="searchDate">
                        <input type="text" class="ty-searchBtn">
                    </div>

                    <div class="ty-right" id="handleBtn">
                        <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="trialBtn" onclick="trial()">试算</button>
                        <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="lossAndGainBroughtForwardBtn" onclick="lossAndGainBroughtForwardBtn()">结转损益</button>
                        <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="checkOutBtn" onclick="checkoutBtn()">记账</button>
                        <button class="ty-btn ty-btn-red ty-btn-big ty-circle-5" id="counterCheckBtn" onclick="counterCheckBtn()" style="display: none">反记账</button>
                        <button class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" id="tallageBtn"  >报税完毕</button>
                        <%--<span class="ty-btn ty-btn-green ty-btn-big ty-circle-5">打印</span>--%>
                        <%--<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5">导出</span>--%>
                    </div>

                    <ul class="ty-secondTab" >
                        <li class="ty-active">试算平衡表</li>
                        <li>科目余额表</li>
                        <li>利润表</li>
                        <li>资产负债表</li>
                        <li>现金流量表</li>
                    </ul>
                    <div class="ty-mainData">
                        <%--试算平衡表--%>
                        <div class="tblContainer hd">
                            <table class="ty-table">
                                <thead>
                                <tr>
                                    <td>科目编号</td>
                                    <td>科目名称</td>
                                    <td colspan="2">期初余额</td>
                                    <td>借方发生</td>
                                    <td>贷方发生</td>
                                    <td colspan="2">期末余额</td>
                                </tr>
                                </thead>
                                <tbody>
                                <%--<tr>--%>
                                <%--<td colspan="2" rowspan="2">合计</td>--%>
                                <%--<td>借</td>--%>
                                <%--<td>formatMoney(25619844)</td>--%>
                                <%--<td rowspan="2">无</td>--%>
                                <%--<td rowspan="2">无</td>--%>
                                <%--<td>借</td>--%>
                                <%--<td>formatMoney(25619844)</td>--%>
                                <%--</tr>--%>
                                <%--<tr>--%>
                                <%--<td>贷</td>--%>
                                <%--<td>formatMoney(25619844)</td>--%>
                                <%--<td>贷</td>--%>
                                <%--<td>formatMoney(25619844)</td>--%>
                                <%--</tr>--%>
                                </tbody>
                            </table>
                        </div>
                        <%--科目余额表--%>
                        <div class="tblContainer hd">
                            <table class="ty-table">
                                <thead>
                                <tr>
                                    <td rowspan="2">科目编号</td>
                                    <td rowspan="2">科目名称</td>
                                    <td colspan="2">年初余额</td>
                                    <td colspan="2">期初余额</td>
                                    <td colspan="2">本期发生</td>
                                    <td colspan="2">本年累计</td>
                                    <td colspan="2">期末余额</td>
                                </tr>
                                <tr>
                                    <td>方向</td>
                                    <td>金额</td>
                                    <td>方向</td>
                                    <td>金额</td>
                                    <td>借方</td>
                                    <td>贷方</td>
                                    <td>借方</td>
                                    <td>贷方</td>
                                    <td>方向</td>
                                    <td>金额</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <%--利润表--%>
                        <div class="tblContainer hd">
                            <table class="ty-table">
                                <thead>
                                <tr>
                                    <td>项目</td>
                                    <td>行次</td>
                                    <td>本年累计金额</td>
                                    <td>本月金额</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <%--资产负债表--%>
                        <div class="tblContainer">
                            <table class="ty-table">
                                <thead>
                                <tr>
                                    <td>资产</td>
                                    <td>行次</td>
                                    <td>期末余额</td>
                                    <td>年初余额</td>
                                    <td>负债和所有者权益</td>
                                    <td>行次</td>
                                    <td>期末余额</td>
                                    <td>年初余额</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <%--现金流量表--%>
                        <div class="tblContainer hd">
                            <table class="ty-table">
                                <thead>
                                <tr>
                                    <td>项目</td>
                                    <td>行次</td>
                                    <td>本年累计金额</td>
                                    <td>本期金额</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>

                    </div>
                    <div class="clr"></div>
                </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/contraryReports.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>

