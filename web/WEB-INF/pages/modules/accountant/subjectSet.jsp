<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include file="../../common/headerTop.jsp" %>
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/accountant/subjectSet.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

<%@ include file="../../common/headerBottom.jsp" %>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce_Fixed">
    <%--余额不一致提示--%>
    <div class="bonceContainer bounce-red" id="balanceTip">
        <div class="bonceHead">
            <span>!重要提示</span>
            <a class="bounce_close hand" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="kt_tip_ms"><span class="ty-red">下列账户<span class="period"></span>的期末余额与财务模块的当前余额不一致。</span>如有未录入至系统财务模块的单据，请财务人员及时录入；如该不一致是由于已录入的<span class="sameMonth"></span>月单据尚未做账导致，则请您及时做账！</p>
            <table class="ty-table ty-table-control balanceDetailList">
                <thead>
                <tr>
                    <td>序号</td>
                    <td>账号名称</td>
                    <td>账号</td>
                    <td class="tb-period">A年B-1余额</td>
                    <td>财务模块的当前余额</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--采购关联功能说明--%>
    <div class="bonceContainer bounce-blue illustrate" id="purchaseFunction">
        <div class="bonceHead">
            <span>采购关联功能说明</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="gapB">
                    <div>采购关联设置后，采购的物、票、款均将由系统给予自动记账，会计不必再手工记账。</div>
                    <div>系统记账后、票据所属月份结账前，如需要，会计还可手动修改系统记账的结果。</div>
                </div>
                <p>会计帐中已有会计科目全部录至本系统前，不可开启采购关联设置！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce_Fixed.cancel();">关闭</span>
        </div>
    </div>
    <%--采购关联开关记录--%>
    <div class="bonceContainer bounce-blue illustrate" id="purchaseRecord">
        <div class="bonceHead">
            <span>采购关联开关记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <tbody>
                <tr>
                    <td width="70%">操作者</td>
                    <td width="30%">操作内容</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce_Fixed.cancel();">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="purchuseAssTip" style="width: 500px;">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="set-item">
                    <div>会计帐中已有会计科目全部录至本系统前，不可开启采购关联设置，请确认！</div>
                </div>
                <div class="changeDot">
                    <i class="fa fa-dot-circle-o"></i>会计帐中已有会计科目已全部录至本系统
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确 定</span>
        </div>
    </div>
        <%--修改前  修改后--%>
    <div class="bonceContainer bounce-green" id="subject_updateScan">
        <div class="bonceHead">
            <span>修改前</span>
            <a class="bounce_close hand" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con su_pack">
                <div>
                    <p class="subject_Input subject_bg">
                        <span>科目编号</span>
                        <span class="con_scan" data-name="subject">科目编号</span>
                    </p>
                    <p class="subject_Input">
                        <span>科目名称</span>
                        <span class="con_scan" data-name="name">科目编号</span>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>上级科目</span>
                        <span class="con_scan" data-name="parent">科目编号</span>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>科目类别</span>
                        <span class="con_scan" data-name="categoryName">科目编号</span>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>余额方向</span>
                        <span class="con_scan" data-name="balanceDirection">科目编号科目编号</span>
                    </p>
                    <div class="subject_Input">
                        <span>数量核算</span>
                        <div class='ty-form-checkbox' skin="green" style="margin-left: 25px;"><i
                                class="fa fa-check"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="mtTip" style="width: 450px">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip" style="width: 450px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="mtTip2" value="">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms2" style="text-align: center; padding:10px 0" value=""></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="changeSure()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--新增下级科目--%>
    <div class="bonceContainer bounce-green" id="subject_addAccount">
        <div class="bonceHead">
            <span>新增科目</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con">
                <div>
                    <p class="subject_Input subject_bg">
                        <span>科目编号</span> <input type="text" name="" value="1001001" disabled>
                    </p>
                    <p class="subject_Input">
                        <span>科目名称</span> <input type="text" name="">
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>上级科目</span> <input type="text" name="" value="1001   库存现金" disabled>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>科目类别</span>  <input type="text" name="" value="流动资产" disabled>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>余额方向</span> <input type="text" value="借" disabled>
                    </p>
                    <div class="subject_Input">
                        <span>数量核算</span>
                        <div class='ty-form-checkbox checkNum' id="checkNum" skin="green" style="margin-left: 25px;"><i
                                class="fa fa-check"></i></div>
                    </div>
                    <p class="subject_Input unit" style="display: none">
                        <span>计量单位</span>  <input type="text" name="" class="quantity">
                    </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="subject_addsure()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--编辑科目--%>
    <div class="bonceContainer bounce-blue" id="subject_upAccount">
        <div class="bonceHead">
            <span>编辑科目</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con">
                <div>
                    <p class="subject_Input subject_bg">
                        <span>科目编号</span>
                        <input type="text" name="" value="1001001" disabled>
                    </p>
                    <p class="subject_Input">
                        <span>科目名称</span>
                        <input type="text" name="">
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>上级科目</span>
                        <input type="text" name="" value="1001    库存现金" disabled>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>科目类别</span>
                        <input type="text" name="" value="流动资产" disabled>
                    </p>
                    <p class="subject_Input subject_bor">
                        <span>余额方向</span>
                        <input type="text" name="" value="借" disabled>
                    </p>
                    <div class="subject_Input">
                        <span>数量核算</span>
                        <div class='ty-form-checkbox checkNum' skin="green" style="margin-left: 25px;">
                            <i class="fa fa-check"></i>
                        </div>
                    </div>
                    <p class="subject_Input unit" style="display: none">
                        <span>计量单位</span>
                        <input type="text" name="">
                    </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="subjectup_sure()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5 " onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 启用科目名称关联功能 --%>
    <div class="bonceContainer bounce-blue" id="open"  >
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>使用科目关联功能可大幅降低会计的工作量。请确认您当前的情况：</p> <p></p>
            <p class="mar" id="fa1" onclick="faChange(1)"><i class="fa fa-square-o"></i>  使用本系统前已设有会计科目，且所有科目已全部录至本系统</p>
            <p class="mar" id="fa2" onclick="faChange(2)"><i class="fa fa-check-square-o"></i>  本公司为新公司，之前未设立会计科目</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="linkBtn()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--新增物料科目--%>
    <div class="bonceContainer bounce-green" id="mtSubject_addAccount">
        <div class="bonceHead">
            <span>新增科目</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con">
                <div>
                    <p class="subject_Input subject_bg">
                        <span>科目编号</span> <input type="text" id="mtSubjectNo"  disabled>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>科目名称</span> <input type="text" id="mtSubjectName" disabled>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>上级科目</span> <select onchange="setSelectVal($(this))" id="pSubjects"></select>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>科目类别</span>  <input type="text" id="mtSubjectCat" value="流动资产" disabled>
                    </p>
                    <p class="subject_Input subject_bg">
                        <span>余额方向</span> <input type="text" value="借" id="mtSubjectYu" disabled>
                    </p>
                    <div class="subject_Input">
                        <span>数量核算</span>
                        <div class='ty-form-checkbox ty-form-checked' skin="green" style="margin-left: 25px;"><i
                                class="fa fa-check"></i></div>
                    </div>
                    <p class="subject_Input subject_bg">
                        <span>计量单位</span>  <input type="text" id="mtSubjectUnit">
                    </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="subject_mtAddSure()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--公司建账提示--%>
    <div class="bonceContainer bounce-blue" id="buildBill" style="width:400px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
        </div>
        <div class="bonceCon">
            <p id="bill_tip" style="text-align: center; padding:10px 0">公司是否已建账？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="setBuildAccountState(1)">是</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="setBuildAccountState(2)">否</span>
        </div>
    </div>
    <%--科目设置完毕提示--%>
    <div class="bonceContainer bounce-blue" id="subjectSetOver" style="width:400px;">
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="over_tip" style="text-align: center; padding:10px 0">科目已设置完毕？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 completAccount" onclick="setBuildAccountState(3)">是</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">否</span>
        </div>
    </div>
    <%--建账完毕提示--%>
    <div class="bonceContainer bounce-red" id="finishTip" style="width: 450px">
        <div class="bonceHead">
            <span>!重要提示</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>只要您正常使用本系统，<span class="generateBeginTime"></span>1日起，会计报表、账簿与凭证将由本系统自动生成！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span><%--)setUpComplete(--%>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="buildFinish()">确定，重新登录</span>
        </div>
    </div>
    <%--金额核查--%>
    <div class="bonceContainer bounce-green" id="amountCheck" style="width:1300px;">
        <div class="bonceHead">
            <span>金额核查</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="amount-body">
                <div class="topTip">
                    <p class="ty-left">下表中使用红字标识的<span class="needChangeNum"></span>个科目数据互相矛盾。</p>
                    <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 balanceTip hd" onclick="">余额不一致！</span>
                </div>
                <ul class="ty-secondTab">
                    <li class="ty-active" onclick="amountCheckTab($(this))" value="1">
                        资产类
                    </li>
                    <li onclick="amountCheckTab($(this))" value="2">
                        负债类
                    </li>
                    <li onclick="amountCheckTab($(this))" value="3">所有者权益类</li>
                    <li onclick="amountCheckTab($(this))" value="4">成本类</li>
                    <li onclick="amountCheckTab($(this))" value="5">损益类</li>
                    <li onclick="amountCheckTab($(this))" value="6" class="cashSheetCheck">现金流量表</li>
                </ul>
                <div>
                    <table class="ty-table ty-table-control alignCenter" id="checkTab1">
                        <thead style="display:block">
                        <tr style="display:block">
                            <td style="display:inline-block;width: 9%" rowspan="2">编码</td><td style="display:inline-block;width: 8%" rowspan="2">名称</td><td style="display:inline-block;width: 8%" rowspan="2">类别</td><td style="display:inline-block;width: 6%" rowspan="2">余额方向</td><td style="display:inline-block;width: 17%" colspan="2">年初余额</td><td style="display:inline-block;width: 17%" colspan="2">本年累计（借方）</td><td style="display:inline-block;width: 17%" colspan="2">本年累计（贷方）</td><td style="display:inline-block;width: 17%" colspan="2">期末余额</td>
                        </tr>
                        <tr style="display:block">
                            <td style="display:inline-block;width: 31%"></td><td style="display:inline-block;width: 8%">数量</td><td style="display:inline-block;width: 9%">余额</td><td style="display:inline-block;width: 8%">数量</td><td style="display:inline-block;width: 9%">余额</td><td style="display:inline-block;width: 8%">数量</td><td style="display:inline-block;width: 9%">余额</td><td style="display:inline-block;width: 8%">数量</td><td style="display:inline-block;width: 9%">余额</td>
                        </tr>
                        </thead>
                        <tbody style="display:block;max-height:350px;overflow-y:auto;overflow-x:hidden;" id="amountCheckTab"></tbody>
                    </table>
                    <table class="ty-table ty-table-control hd alignCenter" id="checkTab2">
                        <thead style="display:block">
                        <tr style="display:block">
                            <td style="display:inline-block;width: 40%">项目</td><td style="display:inline-block;width: 20%">行次</td><td style="display:inline-block;width: 20%">本年累计金额</td><td style="display:inline-block;width: 20%">本期余额</td>
                        </tr>
                        </thead>
                        <tbody style="display:block;max-height:350px;overflow-y:auto;overflow-x:hidden;" id="caseListCheck"></tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
    <%--报表核查--%>
    <div class="bonceContainer bounce-green" id="reportCheck" style="width:1200px;">
        <div class="bonceHead">
            <span>报表核查</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="dateSet"></p>
            <div class="amount-body">
                <ul class="ty-secondTab">
                    <li class="ty-active" onclick="getTrialBalanceSheet($(this))" value="1">
                        试算平衡表
                    </li>
                    <li onclick="getAccountBalanceSheet($(this))" value="2">
                        科目余额表
                    </li>
                    <li onclick="getIncomeSheet($(this))">利润表</li>
                    <li onclick="getBalanceSheet($(this))">资产负债表</li>
                    <li onclick="getCashFlowSheet($(this))">现金流量表</li>
                </ul>
                <div>
                    <%--试算平衡表--%>
                    <div class="tblContainer hd" style="max-height:400px;overflow-y:auto;overflow-x:hidden;">
                        <table class="ty-table">
                            <thead>
                            <tr>
                                <td>科目编号</td>
                                <td>科目名称</td>
                                <td colspan="2">期初余额</td>
                                <td>借方发生</td>
                                <td>贷方发生</td>
                                <td colspan="2">期末余额</td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <%--科目余额表--%>
                    <div class="tblContainer hd" style="max-height:400px;overflow-y:auto;overflow-x:hidden;">
                        <table class="ty-table">
                            <thead>
                            <tr>
                                <td rowspan="2">科目编号</td>
                                <td rowspan="2">科目名称</td>
                                <td colspan="2">年初余额</td>
                                <td colspan="2">期初余额</td>
                                <td colspan="2">本期发生</td>
                                <td colspan="2">本年累计</td>
                                <td colspan="2">期末余额</td>
                            </tr>
                            <tr>
                                <td>方向</td>
                                <td>金额</td>
                                <td>方向</td>
                                <td>金额</td>
                                <td>借方</td>
                                <td>贷方</td>
                                <td>借方</td>
                                <td>贷方</td>
                                <td>方向</td>
                                <td>金额</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--利润表--%>
                    <div class="tblContainer hd">
                        <table class="ty-table alignCenter colLeft">
                            <thead style="display:block">
                            <tr style="display:block">
                                <td style="display:inline-block; width:40%">项目</td><td style="display:inline-block; width:20%">行次</td><td style="display:inline-block; width:20%">本年累计金额</td><td style="display:inline-block; width:20%">本月金额</td>
                            </tr>
                            </thead>
                            <tbody style="display:block;height:400px;overflow-y:auto;overflow-x:hidden;"></tbody>
                        </table>
                    </div>
                    <%--资产负债表--%>
                    <div class="tblContainer">
                        <table class="ty-table alignCenter colLeft">
                            <thead style="display:block">
                            <tr style="display:block">
                                <td style="display:inline-block; width:20%">资产</td><td style="display:inline-block; width:10%">行次</td><td style="display:inline-block; width:10%">期末余额</td><td style="display:inline-block; width:10%">年初余额</td><td style="display:inline-block; width:20%">负债和所有者权益</td><td style="display:inline-block; width:10%">行次</td><td style="display:inline-block; width:10%">期末余额</td><td style="display:inline-block; width:10%">年初余额</td>
                            </tr>
                            </thead>
                            <tbody style="display:block;height:400px;overflow-y:auto;overflow-x:hidden;"></tbody>
                        </table>
                    </div>
                    <%--现金流量表--%>
                    <div class="tblContainer hd">
                        <table class="ty-table alignCenter colLeft">
                            <thead class="ty-table" style="display:block">
                            <tr style="display:block">
                                <td style="display:inline-block; width:30%">项目</td><td style="display:inline-block; width:20%">行次</td><td style="display:inline-block; width:25%">本年累计金额</td><td style="display:inline-block; width:25%">本期金额</td>
                            </tr>
                            </thead>
                            <tbody id="reportListCheck" style="display:block;height:400px;overflow-y:auto;overflow-x:hidden;"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--银行存款提示--%>
    <div class="bonceContainer bounce-red" id="oprationTip" style="width: 450px">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close hand" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>请先到财务模块中新操作！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--管理费用操作记录--%>
    <div class="bonceContainer bounce-blue" id="manageBillRecord">
        <div class="bonceHead">
            <span>管理费用操作记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="formItem">
                <div class="item-title"></div>
            </div>
            <div class="ty-alert ty-alert-info">一级费用类别名称&nbsp;<b class="feeCatName ty-color-blue"></b></div>
            <div>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td>原始状态</td>
                        <td>与  <span class="ty-color-green">5602管理费用</span> 科目不关联</td>
                        <td>--</td>
                    </tr>
                    <tr>
                        <td>第1次修改后</td>
                        <td>修改为与  <span class="ty-color-green">56020005办公费用</span> 科目相关联</td>
                        <td>修改者 甘宝宝 2018/1/1 10:22:57</td>
                    </tr>
                    <tr>
                        <td>第2次修改后</td>
                        <td>修改为与  <span class="ty-color-green">管理费用5602</span> 科目不关联</td>
                        <td>修改者 甘宝宝 2018/1/1 10:22:57</td>
                    </tr>
                    <tr>
                        <td>第3次修改后</td>
                        <td>修改为与  <span class="ty-color-green">56020005办公费用</span> 科目相关联</td>
                        <td>修改者 甘宝宝 2018/1/1 10:22:57</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</button>
        </div>
    </div>
    <%--重要说明--%>
    <div class="bonceContainer bounce-blue illustrate" id="importantNote">
        <div class="bonceHead">
            <span>重要说明</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="noteCon">
                <p><span>1</span> 关于科目的状态</p>
                <p><span>1.1</span> 每个科目都有“已启用”与“已禁用”两种状态</p>
                <p><span>1.2</span> 点击“已启用”，并在之后的提示页上点击确定，即可将状态变为“已禁用”。反之亦然</p>
                <p><span>1.3</span> 状态变更的如为高级科目，则其全部下级科目的状态将跟随变更，而无需单独操作</p>
                <p><span>1.4</span> 凭证制单、需为单据选择科目时，“已禁用”的科目不会出现在选项里</p>
                <p class="topMag"><span>2</span> 科目的新增、修改或状态变更，均将在“科目变动记录”中生成记录</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--采购关联设置--%>
    <div class="bonceContainer bounce-blue" id="purchaseAssociationSet" style="width: 500px;">
        <div class="bonceHead">
            <span>采购关联设置</span>
            <a class="bounce_close" onclick="purchaseCancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="set-step1">
                    <div class="set-item clear">
                        <div class="ty-left">本功能当前状态：<span id="recentStatus"></span></div>
                        <div class="ty-right set-operation">
                            <span class="fun-btn ty-color-blue" data-type="functionIll">功能说明</span>
                            <span class="fun-btn ty-color-blue" data-type="purchaseRecord">开关记录</span>
                        </div>
                    </div>
                    <div class="set-item">该状态如需修改，请在本页面进行</div>
                    <div class="checkSection">
                        <div class="changeDot select-active">
                            <i class="fa fa-circle-o" data-type="Y"></i>开启本功能
                        </div>
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="N"></i>暂时关闭本功能
                        </div>
                    </div>
                </div>
                <div class="set-step2">
                    <div class="set-item">
                        <div>会计帐中已有会计科目全部录至本系统前，不可开启采购关联设置，请确认！</div>
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="Y"></i>会计帐中已有会计科目已全部录至本系统
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="purchaseCancel();">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-type="purchaseAssocial">确 定</span>
        </div>
    </div>
    <%--科目变动记录--%>
    <div class="bonceContainer bounce-blue" id="accountChangeRecord">
        <div class="bonceHead">
            <span>变动内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="hd" id="recordInfo"></div>
            <p><span id="re-changeDate"></span>的科目变动记录</p>
            <table class="ty-table ty-table-control">
                <tbody>
                <tr>
                    <td>操作内容简述</td>
                    <td>操作者</td>
                    <td>操作内容详述</td>
                </tr>
                <tr>
                    <td></td>
                    <td></td>
                    <td><span class="funbtn ty-color-blue" data-fun="afterCreation">建账后的科目</span></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel();">关 闭</span>
        </div>
    </div>
</div>
<%@ include file="../../common/contentHeader.jsp" %>
<div class="page-container">
    <%@ include file="../../common/contentSliderNav.jsp" %>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>科目管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
        <div class="page-content-wrapper">
            <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <%--<div class="ty-right">
                    <div class="blackTip">
                        <div class="blackTip-arrow"></div>
                        <div class="blackTip-inner">
                            个人报销与科目关联后，可降低您50%以上的工作量!
                        </div>
                    </div>
                </div>--%>
                <input type="hidden" id="showMainConNum">
                <div class="mainCon mainCon1">
                    <div class="main_sub">
                        <div class="settingTip hd">
                            <p class="ty-color-green">今天是<span class="today"></span>，请进行科目设置</p>
                            <p class="ty-color-green">您可在某科目下新增下一级科目，也可将不需要科目的状态修改为“禁用”。</p>
                        </div>
                        <div class="ty-right">
                            <span class="funBtn ty-btn-blue fun-btn" data-type="note">重要说明</span>
                            <span class="funBtn ty-btn-blue fun-btn" data-type="changeRecord">科目变动记录</span>
                            <span class="funBtn ty-btn-blue fun-btn" onclick="associateBtn()">个人报销关联设置</span>
                            <span class="funBtn ty-btn-blue fun-btn" data-type="purchaseAssociationSet">采购关联设置</span>
                        </div>
                        <ul class="ty-secondTab" id="mainTab">
                            <li id="bar_asset" onclick="category($(this))" value="1">
                                资产类
                            </li>
                            <li onclick="category($(this))" value="2">
                                负债类
                            </li>
                            <li onclick="category($(this))" value="3">
                                所有者权益类
                            </li>
                            <li onclick="category($(this))" value="4">
                                成本类
                            </li>
                            <li onclick="category($(this))" value="5">
                                损益类
                            </li>
                        </ul>
                    </div>
                    <div class="main_input hd">
                        <div class="entryTip">
                            <p>今天是<span class="timeLine"></span></p>
                            <p>请录入下表中各科目<span class="lastMonth"></span>份的年初余额、本年累计（借方）、本年累计（贷方）及期末余额；并请录入<span class="lastMonth"></span>份现金流量表中各项的本年累计金额。</p>
                            <p>录入完毕后，您可点击<span class="ty-color-green">录入金额核查</span>按钮与<span class="ty-color-green">报表核查</span>按钮，以检查已录入数据与实际是否一致。</p>
                            <p>如核对无误，则可点击<span class="ty-color-green">建账完毕</span>按钮，以进行下一步操作。</p>
                        </div>
                        <div class="remain">
                            <p class="ty-left">年初余额所属年份<span class="balanceYear"></span></p>
                            <p class="ty-left">期末余额所属月份<span class="balanceMonth"></span></p>
                            <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="setBuildAccountState(1)">返回</span>
                        </div>
                        <ul class="ty-secondTab" id="amountTab">
                            <li class="ty-active" onclick="amountTab($(this))" value="1">
                                资产类
                            </li>
                            <li onclick="amountTab($(this))" value="2">
                                负债类
                            </li>
                            <li onclick="amountTab($(this))" value="3">
                                所有者权益类
                            </li>
                            <li onclick="amountTab($(this))" value="4">
                                成本类
                            </li>
                            <li onclick="amountTab($(this))" value="5">
                                损益类
                            </li>
                            <li onclick="amountTab($(this))" value="6">
                                现金流量表
                            </li>
                        </ul>
                    </div>
                    <div class="ty-mainData main_sub tbScroll" id="subjectSet">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td width="180">编码</td>
                                <td>名称</td>
                                <td width="180">类别</td>
                                <td width="180">余额方向</td>
                                <td width="180">状态</td>
                                <td width="180">操作</td>
                            </tr>
                            </thead>
                            <tbody id="set_body"></tbody>
                        </table>
                        <div class="subject-save">
                            <span class="ty-left ty-btn ty-btn-green ty-btn-big ty-circle-5 setOverBtn hd" onclick="bounce.show($('#finishTip'));">科目设置完毕,重新登录</span>
                            <span class="ty-left ty-btn ty-btn-green ty-btn-big ty-circle-5 setSubOverBtn hd" onclick="bounce.show($('#subjectSetOver'))">科目设置完毕</span>
                        </div>
                    </div>
                    <div class="ty-mainData main_input hd">
                        <div class="tbScroll">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td rowspan="2" width="10%">编码</td>
                                    <td rowspan="2" width="10%">名称</td>
                                    <td rowspan="2" width="8%">类别</td>
                                    <td rowspan="2" width="6%">余额方向</td>
                                    <td colspan="2" width="16%">年初余额</td>
                                    <td colspan="2" width="16%">本年累计（借方）</td>
                                    <td colspan="2" width="16%">本年累计（贷方）</td>
                                    <td colspan="2" width="16%">期末余额</td>
                                </tr>
                                <tr>
                                    <td>数量</td>
                                    <td>余额</td>
                                    <td>数量</td>
                                    <td>余额</td>
                                    <td>数量</td>
                                    <td>余额</td>
                                    <td>数量</td>
                                    <td>余额</td>
                                </tr>
                                </thead>
                                <tbody class="setAlign" id="enterInitFinance" catoType="1"></tbody>
                            </table>
                            <table class="ty-table ty-table-control hd" id="caseStatetable">
                                <thead>
                                <tr>
                                    <td>项目</td>
                                    <td>行次</td>
                                    <td>本年累计金额</td>
                                    <td>本期余额</td>
                                </tr>
                                </thead>
                                <tbody id="caseTable">
                                </tbody>
                            </table>
                        </div>
                        <div class="editBtnGroup">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="temporarySave()">暂存，稍后再编辑</span>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="amountCheck()">录入金额核查</span>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="reportCheck()">报表核查</span>
                            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" id="buildOverBtn" can="off">建账完毕,重新登录</span>
                        </div>
                    </div>
                    <div class="clr"></div>
                </div>
                <%--科目关联--%>
                <div class="mainCon mainCon2 associate">
                    <div class="back-btn">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback()">返回</span>
                    </div>
                    <div class="associate_tips">
                        <div>经审批的职工个人报销申请将进入会计模块。</div>
                        <div>下表中，各一级费用类别与销售费用或管理费用均被默认为“不关联”状态。</div>
                        <div>处于<span class="ty-color-orange">“不关联”状态下的报销需由您手动选择</span>会计科目。</div>
                        <div>您可随时修改其状态。</div>
                        <div>处于<span class="ty-color-orange">“关联”状态下的报销将由系统代选</span>为已关联的科目。</div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>一级费用类别</td>
                            <td>操作</td>
                            <td>销售费用5601</td>
                            <td>管理费用5602</td>
                            <td>操作记录</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="ty-right">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="setReimburseRelevance()">确定</span>
                    </div>
                </div>
                <%--科目变动记录--%>
                <div class="mainCon mainCon3">
                    <div class="back-btn">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback()">返回</span>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td rowspan="2">变动日期</td>
                            <td colspan="4">操作后各级科目的数量</td>
                            <td rowspan="2">变动内容</td>
                            <td rowspan="2">变动后的科目</td>
                        </tr>
                        <tr>
                            <td>一级科目</td>
                            <td>二级科目</td>
                            <td>三级科目</td>
                            <td>四级科目</td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="changeContent_ye"></div>
                </div>
                <div class="mainCon mainCon4">
                    <div class="back-btn">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback(3)">返回</span>
                    </div>
                    <ul class="ty-secondTab" id="subjectScan">
                        <li class="ty-active" value="1">
                            资产类
                        </li>
                        <li value="2">
                            负债类
                        </li>
                        <li value="3">
                            所有者权益类
                        </li>
                        <li value="4">
                            成本类
                        </li>
                        <li value="5">
                            损益类
                        </li>
                    </ul>
                    <div class="tbScroll">
                        <div id="subChangeParam" class="hd"></div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td width="25%">编码</td>
                                <td width="25%">名称</td>
                                <td width="20%">类别</td>
                                <td width="15%">余额方向</td>
                                <td width="15%">状态</td>
                            </tr>
                            </thead>
                            <tbody id="subChange">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../../common/contentSliderLitbar.jsp" %>
</div>
<%@ include file="../../common/footerTop.jsp" %>
<%@ include file="../../common/footerScript.jsp" %>

<script src="../script/accountant/subjectSet.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/subjectJournal.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include file="../../common/footerBottom.jsp" %>