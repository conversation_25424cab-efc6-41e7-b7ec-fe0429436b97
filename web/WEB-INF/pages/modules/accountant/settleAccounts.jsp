<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/accountant/settleAccount.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<style>
    #prom{ display: inline-block; margin-left: 50px; color:#f58410 ;  }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce">
    <div class="bonceContainer bounce-blue" id="tip" style="width: 450px">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p id="tipMs" style="text-align: center; padding:10px 0"> </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip" style="width: 450px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="tallage" style="width: 450px">
        <div class="bonceHead">
            <span>请选择报税日期</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>报税日期 <input type="text" id="tallageDate" onclick="showTB()" /></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="tallage()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="checkout" style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p style="text-align: center; padding:10px 0">您确定要结账吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="sureCheckout()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="counterCheck" style="width: 500px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p style="text-align: center; padding:10px 0">您确定要反结账吗</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="sureCounterCheck()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="lossAndGainBroughtForward" style="width: 500px;">
        <div class="bonceHead">
            <span>结转损益</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon turnLeft">
            <table class="ty-table ty-table-none">
                <tr>
                    <td>凭证字</td>
                    <td><input type="text" class="ty-inputText" disabled placeholder="转"></td>
                </tr>
                <tr>
                    <td>凭证摘要</td>
                    <td><input type="text" class="ty-inputText summary"></td>
                </tr>
                <tr>
                    <td>结转类型</td>
                    <td>
                        <div class="carryover">
                            <span class="carryoverType"><i class="fa fa-circle-o"></i>收入</span>
                            <span class="carryoverType"><i class="fa fa-circle-o"></i>支出</span>
                            <span class="carryoverType"><i class="fa fa-dot-circle-o"></i>损益</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>本年利润科目</td>
                    <td><input type="text" class="ty-inputText" disabled placeholder="3103"></td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureLossAndGainBroughtForward()">生成凭证</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="accountingDetail" style="width: 600px;">
        <div class="bonceHead">
            <span>数据详情</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="detailed"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--报销数据查看--%>
    <div class="bonceContainer bounce-blue" id="reimburse" style="width: 900px">
        <div class="bonceHead">
            <span class="bonceHeadName">报销查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-top: 12px">
                <div><div class="item-title"><b>摘要</b></div> <div class="item-content-big summary"></div></div>
                <div><div class="item-title"><b>用途</b></div> <div class="item-content-big purpose"></div>
                </div>
            </div>
            <div class="billDetail" style="margin-top: 12px">
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td>票据种类</td>
                        <td>单张票据金额</td>
                        <td>票据数量</td>
                        <td>票据金额合计</td>
                        <td>申请报销的金额</td>
                        <td>查看</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 报销 - 票据内容 --%>
    <div class="bonceContainer bounce-blue" id="billInfo" style="width: 1200px">
        <div class="bonceHead">
            <span class="bonceHeadName">票据内容</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <%--<div class="item">--%>
            <%--<div class="item-title">票据所属月份</div>--%>
            <%--<div class="item-content">2019-5</div>--%>
            <%--</div>--%>
            <div>
                <%--增值税专用发票，增值税普通发票货物--%>
                <table class="ty-table ty-table-control VATGood">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>税率</td>
                        <td>税额</td>
                        <td>含税合计</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--定额普通发票--%>
                <div class="quotaGood" style="display: none">
                    <div class="item">
                        <div class="item-title">票据种类</div>
                        <div class="item-content billCatName">定额普通发票</div>
                    </div>
                    <div class="item">
                        <div class="item-title">费用类别</div>
                        <div class="item-content feeCatName">交通费-其他</div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td>单张票据金额</td>
                            <td>数量</td>
                            <td>合计</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <%--其他普通发票货物--%>
                <table class="ty-table ty-table-control otherGood" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--收据货物--%>
                <table class="ty-table ty-table-control receipt" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>物品数量</td>
                        <td>单价</td>
                        <td>金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="item text-right singleAmount">
                （单张票据金额）价税合计 <span class="amount ty-color-blue"></span> 元
            </div>
            <div class="item billQuantity text-right">
            </div>
            <div class="item">
                <div class="item-title">备注</div>
                <div class="item-content memo"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed2.cancel()">确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>结账管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
                <div class="ty-container">
                    <%-- 首页 --%>
                        <div class="mainCon mainCon1">
                            <div class="vcher-mod">
                                <div class="row-con">
                                    <div class="settle-date ty-left">做凭证需按月进行。目前，需结账的月份为<span id="settleMonth"></span></div>
                                    <span class="ty-right funBtn ty-btn-green fun-btn" data-type="settleRecord" id="record">结账记录</span>
                                </div>
                            </div>
                            <div class="vcher-mod">
                                <div class="row-con">
                                    <div class="left-count">
                                        <div class="ty-left">本月已有凭证</div>
                                        <div class="ty-right">合计<span class="countNum" data-type="monthVoucher"></span>张</div>
                                    </div>
                                </div>
                                <div class="row-con">
                                    <div class="left-count">
                                        <div class="overSect">其中</div>
                                        <div class="ty-left">手动选择科目的凭证</div>
                                        <div class="ty-right"><span class="countNum" data-type="manualSelection"></span>张</div>
                                    </div>
                                    <span class="ty-right funBtn ty-btn-green scanBtn fun-btn" data-type="manualsScan">查 看</span>
                                </div>
                                <div class="row-con">
                                    <div class="left-count">
                                        <div class="ty-left gapL">由系统选择科目的凭证</div>
                                        <div class="ty-right"><span class="countNum" data-type="osSelection"></span>张</div>
                                    </div>
                                    <span class="ty-right funBtn ty-btn-green scanBtn fun-btn" data-type="osScan">查 看</span>
                                </div>
                                <div class="row-con">
                                    <div class="left-count">
                                        <div class="ty-left gapL">会计录入的凭证</div>
                                        <div class="ty-right"><span class="countNum" data-type="accountantSelection"></span>张</div>
                                    </div>
                                    <span class="ty-right funBtn ty-btn-green scanBtn fun-btn" data-type="accountantlsScan">查 看</span>
                                    <div class="careful">操作说明：凭证的增减或更换科目需在“凭证管理”中进行，本模块仅可查看</div>
                                </div>
                            </div>
                            <div class="vcher-mod-no">
                                <div id="settle">
                                    <button class="funBtn ty-btn-green scanBtn" id="trialBtn" onclick="trial()">试 算</button>
                                    <button class="funBtn ty-btn-green scanBtn" id="lossAndGainBroughtForwardBtn" onclick="lossAndGainBroughtForwardBtn()">结转损益</button>
                                    <button class="funBtn ty-btn-green" id="partCheckOutBtn" onclick="partCheckoutBtn()">记账</button>
                                    <button class="funBtn ty-btn-red scanBtn" id="counterCheckBtn" style="display: none" onclick="counterCheckBtn()">反记账</button>
                                    <button class="funBtn ty-btn-green scanBtn" id="checkOutPreview" onclick="checkOutPreview()">结账预览</button>
                                    <button class="funBtn ty-btn-green scanBtn" id="checkOutBtn" onclick="checkoutBtn()">结 账</button>
                                    <button class="funBtn ty-btn-green scanBtn" disabled="disabled" id="tallageBtn" onclick="tallageBtn()">报税完毕</button>
                                    <%--<span style="margin-left:10px;"class="ty-btn ty-btn-blue ty-circle-5 ty-btn-big" onclick="settleAccounts2()">撤销结账</span>--%>
                                </div>
                                <p class="careful">操作说明：结账需从试算开始。结账前，可多次试算。</p>
                            </div>
                        </div>
                        <div class="mainCon mainCon2 specialTab">
                            <div class="back-btn">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 fun-btn" data-type="reback6">返回</span>
                                <div class="ty-right">
                                    其他年份的结账记录
                                    <input id="otherYear" class="ty-searchInput" />
                                </div>
                            </div>
                            <div>以下为<span class="annual"></span>年度各月的结账记录。查看其他年份的数据，请在右上角切换年份</div>
                            <table class="ty-table ty-table-control" id="settleRecordList">
                                <thead>
                                <tr>
                                    <td width="25%">月份</td>
                                    <td width="25%">结账时间</td>
                                    <td width="50%">可查看的资料</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <div class="mainCon mainCon4">
                            <div class="back-btn">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 fun-btn" data-type="reback5">返回</span>
                            </div>
                            <p class="reportDetail">XXXX年XX月的财务报表</p>
                            <input type="hidden" id="reportDateSelect" />
                            <%--原contraryReports.jsp--%>
                            <ul class="ty-secondTab" >
                                <li class="ty-active">试算平衡表</li>
                                <li>科目余额表</li>
                                <li>利润表</li>
                                <li>资产负债表</li>
                                <li>现金流量表</li>
                            </ul>
                            <div class="turnLeft">
                                <%--试算平衡表--%>
                                <div class="tblContainer hd">
                                    <table class="ty-table">
                                        <thead>
                                        <tr>
                                            <td>科目编号</td>
                                            <td>科目名称</td>
                                            <td colspan="2">期初余额</td>
                                            <td>借方发生</td>
                                            <td>贷方发生</td>
                                            <td colspan="2">期末余额</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <%--<tr>--%>
                                        <%--<td colspan="2" rowspan="2">合计</td>--%>
                                        <%--<td>借</td>--%>
                                        <%--<td>formatMoney(25619844)</td>--%>
                                        <%--<td rowspan="2">无</td>--%>
                                        <%--<td rowspan="2">无</td>--%>
                                        <%--<td>借</td>--%>
                                        <%--<td>formatMoney(25619844)</td>--%>
                                        <%--</tr>--%>
                                        <%--<tr>--%>
                                        <%--<td>贷</td>--%>
                                        <%--<td>formatMoney(25619844)</td>--%>
                                        <%--<td>贷</td>--%>
                                        <%--<td>formatMoney(25619844)</td>--%>
                                        <%--</tr>--%>
                                        </tbody>
                                    </table>
                                </div>
                                <%--科目余额表--%>
                                <div class="tblContainer hd">
                                    <table class="ty-table">
                                        <thead>
                                        <tr>
                                            <td rowspan="2">科目编号</td>
                                            <td rowspan="2">科目名称</td>
                                            <td colspan="2">年初余额</td>
                                            <td colspan="2">期初余额</td>
                                            <td colspan="2">本期发生</td>
                                            <td colspan="2">本年累计</td>
                                            <td colspan="2">期末余额</td>
                                        </tr>
                                        <tr>
                                            <td>方向</td>
                                            <td>金额</td>
                                            <td>方向</td>
                                            <td>金额</td>
                                            <td>借方</td>
                                            <td>贷方</td>
                                            <td>借方</td>
                                            <td>贷方</td>
                                            <td>方向</td>
                                            <td>金额</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <%--利润表--%>
                                <div class="tblContainer hd">
                                    <table class="ty-table">
                                        <thead>
                                        <tr>
                                            <td>项目</td>
                                            <td>行次</td>
                                            <td>本年累计金额</td>
                                            <td>本月金额</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <%--资产负债表--%>
                                <div class="tblContainer">
                                    <table class="ty-table">
                                        <thead>
                                        <tr>
                                            <td>资产</td>
                                            <td>行次</td>
                                            <td>期末余额</td>
                                            <td>年初余额</td>
                                            <td>负债和所有者权益</td>
                                            <td>行次</td>
                                            <td>期末余额</td>
                                            <td>年初余额</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <%--现金流量表--%>
                                <div class="tblContainer hd">
                                    <table class="ty-table">
                                        <thead>
                                        <tr>
                                            <td>项目</td>
                                            <td>行次</td>
                                            <td>本年累计金额</td>
                                            <td>本期金额</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="mainCon mainCon5 specialTab">
                            <div class="back-btn">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 fun-btn" data-type="reback5">返回</span>
                                <div class="ty-right">
                                    <span class="search">查凭证
                                        <input type="text" placeholder="请输入凭证金额、经手人或摘要内可能包含的内容"><span onclick="voucherSearch($(this))">确定</span>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <div class="voucherParam hd"></div>
                                <div class="tblVoucher1">
                                    <p class="voucherDetails">XXXX年XX月的凭证  共XXX张</p>
                                    <table class="ty-table">
                                        <thead>
                                        <td width="10%">凭证日期</td>
                                        <td width="12%">摘要</td>
                                        <td width="13%">科目</td>
                                        <td width="10%">借方金额</td>
                                        <td width="10%">贷方金额</td>
                                        <td width="15%">创建人</td>
                                        <td width="15%">制单人</td>
                                        <td width="15%">审核人</td>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="tblVoucher2">
                                    <p class="voucherDetails">XXXX年XX月XXXXXXX的凭证  共XXX张</p>
                                    <table class="ty-table">
                                        <thead>
                                        <td width="8%">凭证日期</td>
                                        <td width="8%">票据日期</td>
                                        <td width="14%">摘要</td>
                                        <td width="14%">借方科目</td>
                                        <td width="10%">借方金额</td>
                                        <td width="10%">贷方金额</td>
                                        <td width="15%">制单</td>
                                        <td width="16%">审核</td>
                                        <td width="5%">操作</td>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="mainCon mainCon6">
                            <div class="back-btn">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 fun-btn" data-type="reback6">返回</span>
                            </div>
                            <input type="hidden" id="previewing" value="0"/>
                            <div class="vcher-mod">
                                <div class="row-con">
                                    <p class="settle-date ty-left">需结账的月份为<span class="settleDate"></span>    本页数据均模拟数据，正式结账完成后，才会生成实际数据！</p>
                                    <div class="careful">注：正式结账前，数据还可调整。调整后需重新“试算”！</div>
                                </div>
                            </div>
                            <div class="vcher-mod">
                                <div class="row-con">
                                    <div class="ty-left">结账后本月的财务报表</div>
                                    <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 fun-btn" data-type="fcReportPreview">预 览</span>
                                </div>
                                <div class="row-con">
                                    本月凭证共<span class="countNum" data-type="monthVoucher"></span>张，其中
                                </div>
                                <div class="intend2">
                                    <div class="row-con">
                                        <div class="ty-left">系统生成的凭证共<span class="countNum" data-type="osSelection"></span>张</div>
                                        <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 fun-btn" data-type="osScan">预 览</span>
                                    </div>
                                    <div class="row-con">
                                        <div class="ty-left">会计录入的凭证共<span class="countNum" data-type="accountantSelection"></span>张</div>
                                        <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 fun-btn" data-type="accountantlsScan">预 览</span>
                                    </div>
                                    <div class="row-con">
                                        <div class="ty-left">手动选择科目的凭证共<span class="countNum" data-type="manualSelection"></span>张</div>
                                        <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 fun-btn" data-type="manualsScan">预 览</span>
                                    </div>
                                </div>
                                <div class="row-con">
                                    <div class="ty-left">本月结账后的账簿</div>
                                    <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 fun-btn" data-type="bookPreview">预 览</span>
                                </div>
                            </div>
                        </div>
                        <div class="mainCon mainCon7">
                            <div class="back-btn">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 fun-btn" data-type="reback5">返回</span>
                            </div>
                            <ul class="ty-firstTab">
                                <li flag="0" class="ty-active">总账</li>
                                <li flag="1">明细账</li>
                                <div class="bookDetail">
                                    <span class="bookDate"></span>结账后的账簿
                                </div>
                            </ul>
                            <div class="bookMainCon">
                                <div class="ty-colFileTree">
                                    <div class="treeName">快速查询</div>
                                    <div class="treeBox"></div>
                                </div>
                                <div class="ty-fileContent" style="position: relative;">
                                    <div class="subjectInfo"></div>
                                    <table class="ty-table ty-table-control subjectDetail">
                                        <thead>
                                        <tr>
                                            <td>凭证日期</td>
                                            <td>摘要</td>
                                            <td>借方金额</td>
                                            <td>贷方金额</td>
                                            <td>方向</td>
                                            <td>余额</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>

                        </div>
                        <div class="mainCon mainCon8 specialTab">
                            <div class="back-btn">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 fun-btn" onclick="reback(5)">返 回</span>
                            </div>
                            <div>
                                <table class="ty-table scTab1">
                                    <thead>
                                    <td width="10%">凭证日期</td>
                                    <td width="12%">摘要</td>
                                    <td width="13%">科目</td>
                                    <td width="10%">借方金额</td>
                                    <td width="10%">贷方金额</td>
                                    <td width="15%">创建人</td>
                                    <td width="15%">制单人</td>
                                    <td width="15%">审核人</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                                <table class="ty-table scTab2">
                                    <thead>
                                    <td width="10%">凭证日期</td>
                                    <td width="10%">票据日期</td>
                                    <td width="10%">摘要</td>
                                    <td width="12%">科目</td>
                                    <td width="10%">借方金额</td>
                                    <td width="10%">贷方金额</td>
                                    <td width="15%">创建</td>
                                    <td width="15%">制单</td>
                                    <td width="8%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/contraryReports.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/settleAccounts.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/voucherCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/subjectsTree1.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/accountBook1.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

