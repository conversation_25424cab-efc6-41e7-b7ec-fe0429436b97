<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include file="../../common/headerTop.jsp" %>
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/accountant/subjectsTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/accountant/accountantImport.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/accountant/accountBook.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<style>
    #laydate_today{ display:none!important;   }
    #actsTree2 .dropDown > ul {
        padding: 0 0 0 30px;
    }
    .ty-container{
        position: relative;
        min-height: 702px;
    }
    .ty-colFileTree{
        width: 300px;
    }
    .ty-colFileTree .level1{
        height: 580px;
        overflow: auto;
    }
    .ty-colFileTree .ty-treeItem{
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .ty-fileContent{
        width:100%;
        padding-left:20px;
    }
    .treeName{
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-weight: bold;
        text-align: center;
        background-color: #e9e9e9;
        color: #444;
    }
    .treeSearch{
        width: 100%;
        height: 40px;
        text-align: center;
        padding-top:4px;
    }
    .subjectInfo{
        font-weight: bold;
        border-left: 3px solid #5d9ded;
        line-height: 26px;
        text-indent: 20px;
        color: #666;
        margin-bottom:20px;
    }
    .ty-search{
        float: right;
        position: relative;
        line-height: 1;
    }
    .ty-searchInput {
        background-color: #fff;
    }
    input.ty-searchInput{
        border: 1px solid #48cfad;
    }
    .laydate_table thead,.laydate_table tbody{
        display: none;
    }
    .ty-table td {
        text-align: left;
    }
    .ty-table thead td {
        border-color: #d7d7d7;
        background: #f2f2f2;
        text-align: center;
    }
    .ty-mainData{
        display: flex;
    }
</style>
<%@ include file="../../common/headerBottom.jsp" %>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce_Fixed">
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_close()"></a>
        </div>
        <div class="bonceCon ">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_cancel()">确定</span>
        </div>
    </div>

</div>
<%@ include file="../../common/contentHeader.jsp" %>

<div class="page-container">
    <%@ include file="../../common/contentSliderNav.jsp" %>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>账簿管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" id="paContainer">
            <!--放内容的地方-->
            <div class="ty-container">
                <ul class="ty-firstTab">
                    <li flag="0" class="ty-active">总账</li>
                    <li flag="1">明细账</li>
                    <div class="ty-search">
                        <input type="text" class="ty-searchInput" autocomplete="off" id="searchDate">
                        <span type="text" class="ty-searchBtn" onclick="searchB();"></span>
                    </div>
                </ul>
                <div class="ty-mainData">
                    <div class="ty-colFileTree">
                        <div class="treeName">快速查询</div>
                        <%--<div class="treeSearch">--%>
                            <%--<div class="ty-search">--%>
                                <%--<input type="text" class="ty-searchInput" autocomplete="off" id="searchDate">--%>
                                <%--<input type="text" class="ty-searchBtn">--%>
                            <%--</div>--%>
                        <%--</div>--%>
                        <div class="treeBox"></div>
                    </div>
                    <div class="ty-fileContent" style="position: relative;">
                        <div class="subjectInfo"></div>
                        <table class="ty-table ty-table-control subjectDetail">
                            <thead>
                            <tr>
                                <td>凭证日期</td>
                                <td>摘要</td>
                                <td>借方金额</td>
                                <td>贷方金额</td>
                                <td>方向</td>
                                <td>余额</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                    <%--<div class="ty-mainData">--%>

                        <%--&lt;%&ndash;左侧科目树&ndash;%&gt;--%>
                            <%--<div class="tb_left fast_change">--%>
                                <%--<div class="head">--%>
                                    <%--<span>快速切换</span>--%>
                                <%--</div>--%>

                                <%--<div class="cont">--%>
                                    <%--<div>--%>
                                        <%--<input type="text">--%>
                                        <%--<div class="search" id="search"></div>--%>
                                    <%--</div>--%>


                                    <%--<div id="son_cont">--%>
                                        <%--<ul id="actsTree2">--%>

                                        <%--</ul>--%>
                                    <%--</div>--%>
                                <%--</div>--%>
                            <%--</div>--%>


                            <%--&lt;%&ndash;右侧详细信息&ndash;%&gt;--%>
                            <%--<div class="tb_right detailed">--%>
                                <%--<div class="tit_msg">--%>
                                    <%--<span>科目名称：</span><span id="tit_msg"></span>--%>
                                <%--</div>--%>
                                <%--<table class="ty-table ty-table-control subjectDetail">--%>
                                    <%--&lt;%&ndash;表单头部&ndash;%&gt;--%>
                                    <%--<thead style="display:block" id="theader">--%>
                                    <%--<tr  style="display:block;padding-right:5px;">--%>
                                        <%--<td style="display:inline-block;width:16.5%">凭证日期--%>
                                        <%--</td><td style="display:inline-block;width:16.5%">摘要--%>
                                    <%--</td><td style="display:inline-block;width:16.5%">借方金额--%>
                                    <%--</td><td style="display:inline-block;width:16.5%">贷方金额--%>
                                    <%--</td><td style="display:inline-block;width:16.5%">方向--%>
                                    <%--</td><td style="display:inline-block;width:17.5%">余额</td>--%>
                                    <%--</tr>--%>

                                    <%--</thead>--%>

                                    <%--&lt;%&ndash;表单内容&ndash;%&gt;--%>
                                    <%--<tbody id="set_body"  style="display:block;height:500px;overflow-y:auto;overflow-x:hidden;">--%>
                                        <%--<tr style="display:block">--%>
                                            <%--<td style="width:16.5%" class="margin_r fontSize">凭证日期--%>
                                            <%--</td><td style="width:16.5%" class="margin_r fontSize">摘要--%>
                                        <%--</td><td style="width:16.5%" class="margin_r fontSize">借方金额--%>
                                        <%--</td><td style="width:16.5%" class="margin_r fontSize">贷方金额--%>
                                        <%--</td><td style="width:16.5%" class="margin_r fontSize">方向--%>
                                        <%--</td><td style="width:17.5%" class="fontSize">余额</td>--%>
                                        <%--</tr>--%>
                                    <%--</tbody>--%>
                                <%--</table>--%>

                            <%--</div>--%>

                        <%--<div class="clear"></div>--%>


                    <%--</div>--%>

                <%--<div class="clr"></div>--%>
            </div>


        </div>
    </div>

    <%@ include file="../../common/contentSliderLitbar.jsp" %>
</div>
<%@ include file="../../common/footerTop.jsp" %>
<%@ include file="../../common/footerScript.jsp" %>

<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/subjectsTree1.js?v=SVN_REVISION" type="text/javascript"></script>
<%--<script src="${pageContext.request.contextPath }/script/accountant/accountBook.js" type="text/javascript"></script>--%>
<script src="../script/accountant/accountBook1.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include file="../../common/footerBottom.jsp" %>

