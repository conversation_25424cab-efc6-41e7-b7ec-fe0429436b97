<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include file="../../common/headerTop.jsp" %>
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/accountant/authority.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<style>
    .i-pentagon{  }
</style>
<%@ include file="../../common/headerBottom.jsp" %>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce">
    <%--提示--%>
    <div class="bonceContainer bounce-blue" id="mtTip" style="width:500px; ">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--新增小会计--%>
    <div class="bonceContainer bounce-green" id="addStaff">
        <div class="bonceHead">
            <span>新增会计人员：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="inpitItem">
                <span class="inputTtl">手机号码：</span>
                <input type="text" id="Phone" class="inputCon">
            </div>
            <p class="tip"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="addStaffOk()">确定</span>
        </div>
    </div>
    <%-- 调整权限 --%>
    <div class="bonceContainer bounce-green" id="adjustAuthority" style="width: 1200px">
        <div class="bonceHead">
            <span>权限分配</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <table class="ty-table ty-table-left">
                <tbody>
                <%--科目设置--%>
                <tr class="subjectSet">
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sb_all">
                            <span>科目设置</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sb">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sj">
                            <span>新增</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sk">
                            <span>修改</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sz">
                            <span>状态设置</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <%--科目选择--%>
                <tr class="subjectChoose">
                    <td rowspan="4">
                        <div class='ty-form-checkbox choose' skin="green" id="auth_sc_all">
                            <span>科目选择</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>待选择</td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sp">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sq">
                            <span>科目选择</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_so">
                            <span>不予下账</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>打印</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>导出</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                </tr>
                <tr class="subjectChoose">
                    <td>待审批</td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sr">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_ss">
                            <span>审批</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>打印</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>导出</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                </tr>
                <tr class="subjectChoose">
                    <td>已审批</td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_st">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_su">
                            <span>不予下账</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sv">
                            <span>修改</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>打印</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>导出</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                </tr>
                <tr class="subjectChoose">
                    <td>已驳回</td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sx">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sw">
                            <span>不予下账</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sy">
                            <span>修改</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>打印</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>导出</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                </tr>
                <%--凭证管理--%>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sd_all">
                            <span>凭证管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sd">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>打印</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox ty-checkbox-disabled' skin="green">
                            <span>导出</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                </tr>
                <%--账簿管理--%>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_se_all">
                            <span>账簿管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_se">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <%--对公报表--%>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sf_all">
                            <span>对公报表</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sf">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sza">
                            <span>试算</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_szb">
                            <span>结转损益</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_szc">
                            <span>小结账</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_szd">
                            <span>反结账</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
                <%--会计录入--%>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sg_all">
                            <span>会计录入</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sg">
                            <span>查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sl">
                            <span>新增</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sm">
                            <span>修改</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" id="auth_sn">
                            <span>删除</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="authority_surebtn()" id="editRoleSubmit">确认</span>
        </div>
    </div>
</div>
<%@ include file="../../common/contentHeader.jsp" %>
<div class="page-container">
    <%@ include file="../../common/contentSliderNav.jsp" %>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>会计权限管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="getPassword">
                    <div class="Con_bor">
                        <div class="opinionCon authority_con">
                            <div class="authNav">
                                <%--<c:if test="${agentType==1} ">--%>
                                <a id="addStaffBtn" class="ty-btn-green ty-circle-5 ty-btn ty-btn-big" onclick="addStaff()">新增会计</a>
                                <%--</c:if>--%>
                            </div>
                            <table class="ty-table ty-table-control panel_1">
                                <thead>
                                <td width="50%">电话</td>
                                <td width="50%">操作</td>
                                </thead>
                                <tbody id="staffCon"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="clr"></div>
            </div>
        </div>
    </div>
    <%@ include file="../../common/contentSliderLitbar.jsp" %>
</div>
<%@ include file="../../common/footerTop.jsp" %>
<%@ include file="../../common/footerScript.jsp" %>

<script src="../script/accountant/authority.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    var modelAgentType = ${agentType} ;
    if(modelAgentType != 1){
        $("#addStaffBtn").remove() ;
    }
</script>
<%@ include file="../../common/footerBottom.jsp" %>

