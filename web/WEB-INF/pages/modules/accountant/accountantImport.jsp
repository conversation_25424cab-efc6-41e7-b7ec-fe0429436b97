<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include file="../../common/headerTop.jsp" %>
<link href="../css/accountant/common.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/accountant/subjectsTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/accountant/accountantImport.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<style>
    .bonceCon input,.bonceCon select,.bonceCon textarea{
        border:1px solid #dee8f0;
        background-color: #fff;
        line-height: 36px;
        text-align: left;
        padding: 0 8px;
        color: #666;
    }
    .bonceCon input:focus,.bonceCon select:focus,.bonceCon textarea:focus{
        border:1px solid #5d9cec;
        color: #555;
    }
    .bonceCon input:disabled,.bonceCon select:disabled,.bonceCon textarea:disabled{
        background-color: #eee;
        cursor: not-allowed;
        color: #888;
    }
    table.ty-table td{ padding:0 5px;  }
    .headInfo,.borrowMoreLoans,.special,.chooseBtn{
        position: relative;
        margin-top: 15px;
    }
    .headInfo .normalItem,.cashFlowGroup .normalItem{
        position: relative;
        padding:0 20px;
        margin-bottom: 5px;
    }
    .borrowMoreLoans .borrowItem,.borrowMoreLoans .loanItem{
        margin-top: -1px;
        position: relative;
        padding:10px;
    }
    .borrowMoreLoans .borrowItem{
        border-left: 10px solid #7fdd9e;
        background: #e1f3e9;
    }
    .borrowMoreLoans .borrowItem:nth-child(odd){
        border-left: 10px solid #a1e0b1;
        background: #eaf5f2;
    }
    .borrowMoreLoans .mPrice{
        display: inline
    }
    .borrowMoreLoans .mPrice span{
        font-weight: bold;
    }
    .borrowMoreLoans .loanItem{
        border-left: 10px solid #97c2f2;
        background: #ebf0f3;
    }
    .borrowMoreLoans .loanItem:nth-child(odd){
        border-left: 10px solid #7fb2ec;
        background: #dee8ed;
    }
    .borrowMoreLoans .mNumber span,.borrowMoreLoans .mPrice span,.normalItem span{
        margin: 0 5px 0 10px;
        display: inline-block;
    }
    .borrowMoreLoans .mNumber span:nth-child(1),.normalItem span:nth-child(1){
        width: 78px;
    }
    .borrowMoreLoans .handle{
        float: right;
        height: 30px;
        line-height: 30px;
    }
    .borrowMoreLoans span.subjectTitle{
        font-weight: bold;
        margin: 0 5px 0 10px;
        display: inline-block;
        width: 78px;
    }
    .borrowMoreLoans .m5{
        margin-top: 5px;
    }
    .borrowMoreLoans .subjectBorrow,.borrowMoreLoans .subjectLoan,.special .cash,.normalItem input,.normalItem select{
        width: 324px;
    }
    .borrowMoreLoans .price,.special .amount{
        width: 65px;
    }
    .borrowItem.fixed{
        margin-top: 0;
    }
    .loanItem.fixed{
        margin-top:20px;
    }
    .bookIcon:before{
        content: "\f02d";
        font-family: 'FontAwesome';
        font-weight: normal;
        font-style: normal;
        font-size: 18px;
        color: #bed3de;
        position: absolute;
        top: 12px;
        left: 400px;
        z-index: 10
    }
    .special{
        max-height: 160px;
        overflow-y: auto;
        overflow-x: hidden;
        margin-top: 20px;
    }
    .special .cashFlowGroup{
        margin-bottom: 5px;
    }
    .subjectNo{
        position: absolute;
        width: 30px;
        height: 54px;
        line-height:54px;
        top: 0;
        left: 0;
        color: #fff;
        text-align: center;
        font-weight:bold;
    }
    .borrowItem .subjectNo{
        background-color: #a1e0cf;
    }
    .loanItem .subjectNo{
        background-color: #9bcbe6;
    }
    input,select{
        padding:0 10px;
    }
    td>div{ height:40px; margin:0 -15px; padding: 0 15px; border-bottom:1px solid #e9e9e9 ;overflow: hidden;line-height: 1  }
    td>div:after {
        display:inline-block;
        width:0;
        height:100%;
        vertical-align:middle;
        content:"";
    }
    td>div:last-child{ border-bottom:none;       }
    .w65{
        width: 65px;
    }
    .chooseBtn{
        width: 100%;
    }
    .trGray>td{ color: #bbb; }
</style>
<%@ include file="../../common/headerBottom.jsp" %>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce_Fixed">
    <%@ include file="subjectTree.jsp" %>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="tip" style="width: 450px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-circle-5 ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip" style="width: 450px;">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--选择科目--%>
    <div class="bonceContainer bounce-blue" id="choose_addAccount" style="width: 790px;">
        <div class="bonceHead source">
            <span>选择科目</span>
            <a class="bounce_close" onclick="bounce_close()"></a>

        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con clearfix">
                <div class="col-md-4">
                    <div class="detailed">
                        <table class="ty-table">

                        </table>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="headInfo"></div>
                    <div class="borrowMoreLoans" style="margin-bottom: 15px;height: 400px;overflow-y: auto;overflow-x: hidden"></div>
                    <div class="special"></div>
                    <div class="chooseBtn kind"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="enter_addAccount" style="width: 800px;">
        <div class="bonceHead">
            <span>会计录入</span>
            <a class="bounce_close" onclick="entering_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con">
                <div class="addAccount">
                    <div class="headInfo"></div>
                    <div class="borrowMoreLoans" style="max-height: 319px;overflow-y: auto;overflow-x: hidden"></div>
                    <div class="special"></div>
                    <div class="chooseBtn kind"></div>
                </div>
                <%--<div class="enter_contwo">--%>
                    <%--<div class="choose_Input   enter_last">--%>
                        <%--<span>摘要  ：</span>--%>
                        <%--<input type="text" name="" id="addsumary">--%>
                    <%--</div>--%>
                <%--</div>--%>
                <div class="    ">
                    <p class="mescon" id="mesTip"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="update_addAccount" style="width: 790px;">
        <div class="bonceHead">
            <span>会计录入修改</span>
            <a class="bounce_close" onclick="entering_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails choose_con">
                <div class="addAccount">
                    <div class="headInfo"></div>
                    <div class="borrowMoreLoans" style="max-height: 319px;overflow-y: auto;overflow-x: hidden"></div>
                    <div class="special"></div>
                    <div class="chooseBtn kind"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-5" onclick="entering_cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-circle-5 ty-btn-big" id="entering_updatasure" onclick="entering_updatasure()">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="enter_deletAccount" style="width: 500px">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="entering_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="sale_delete">
                    <p style="text-align:center;padding:10px 0;">
                        <span class="pic"></span>
                        <span>是否确认删除？</span>
                    </p>
                    <p id="del_billID" class="hd"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" onclick="entering_deletsure()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="entering_cancel()">取消</span>
        </div>
    </div>
</div>
<%@ include file="../../common/contentHeader.jsp" %>
<div class="page-container">
    <%@ include file="../../common/contentSliderNav.jsp" %>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>会计录入</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="cashFlowOption hd"></div>
            <div class="ty-container">
                <div class="ty-right">
                    <a class="ty-btn-green ty-btn ty-btn-big ty-circle-5" onclick="enter_add($(this))" id="enter_addbtn">新增</a>
                </div>
                <div class="ty-mainData">
                    <div class="opinionCon th_tevslt">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>凭证日期</td>
                                <td>摘要</td>
                                <td>借方科目</td>
                                <td>借方金额</td>
                                <td>贷方科目</td>
                                <td>贷方金额</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody id="set_body">  </tbody>
                        </table>
                        <div id=""></div>
                    </div>
                    <div class="clr"></div>
                </div>
            </div>


        </div>
    </div>

    <%@ include file="../../common/contentSliderLitbar.jsp" %>
</div>
<%@ include file="../../common/footerTop.jsp" %>
<%@ include file="../../common/footerScript.jsp" %>
<script src="../script/accountant/subjectsTree1.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/accountantImport.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/accountant/common.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include file="../../common/footerBottom.jsp" %>

