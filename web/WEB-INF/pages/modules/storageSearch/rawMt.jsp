<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%--<link href="../css/production/storage.css" rel="stylesheet" type="text/css" />--%>
<link href="../css/storage/rawMt.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/storageSearch/rawMtSearch.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%--占用库位查看--%>
    <div class="bonceContainer bounce-blue " id="holdStockSee" style="width:1000px;display: none;
        position: fixed;left: 451.5px;top: -47px;z-index: 1;">
        <div class="bonceHead">
            <span>占用库位查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-none bg-yellow" id="holdStockInfo">
                    <thead>
                    <tr>
                        <td width="15%">材料名称</td>
                        <td width="15%">材料代号</td>
                        <td width="10%">型号</td>
                        <td width="10%">规格</td>
                        <td width="20%">创建人</td>
                        <td width="10%">计量单位</td>
                        <td width="10%">当前库存</td>
                        <td width="10%">占用库位</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div id="onlyNumInfo" >该材料未选择库位</div>
                <div id="currentStation" class="resetCurrentStation"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce.cancel();">取消</span>
<%--            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="holdStockSeeReset" onclick="reSelectStock($(this));">去选择库位</span>--%>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container" onclick="scanCtrlHide()">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>原辅材料查询</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer"  >
            <!--放内容的地方-->
            <div class="ty-container" id="one">
                <div class="ty-page" >要查找的资料</div>
                <div class="ty-catch">
                    <input class="ask" id="search_text"
<%--                           onkeypress="getMainList(1)" onclick="AutoComplete()"--%>
                           placeholder="请输入材料代号/材料名称">
                    <button class="next" onclick="getMainList(1)">确定</button>
                </div>
<%--                <div class="ty-out" id="bocked" onclick="rendertest()"> </div>--%>
            </div>
            <div class="ty-container ty-otdd" id="two">
                <button type="button" class="btn btn-default" id="back" onclick="stockJump(0)">返回原辅材料主页</button>
                <div class="pageStyle container_item">
                    <%--首页--%>
                    <div class="initBody">
                        <!--写东西的地方-->
                        <div class="mdi">符合查询条件的材料如下</div>
                        <table class="ty-table ty-table-none bg-yellow" id="stork">
                            <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>创建人</td>
                                    <td>计量单位</td>
                                    <td>最低库存</td>
                                    <td>当前库存</td>
                                    <td>初始库存</td>
                                    <td>占用库位</td>
                                </tr>
                            </thead>
                            <tbody id="boder">
                            </tbody>
                        </table>
                    </div>
                </div>
                <div id="ye1"></div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/storageSearch/rawMtSearch.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>

