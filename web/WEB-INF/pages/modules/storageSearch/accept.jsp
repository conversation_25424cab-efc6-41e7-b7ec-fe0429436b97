<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
 <link href="../css/storageSearch/accept.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .page>div{
        display: none;
    }
    .page1{ height:200px; }
    .searchCon{
        width: 250px;
        border:1px solid #cdcdcd;
        border-radius: 4px;
        margin:200px auto;
    }
    .searchCon>input{
        border:none;
    }
</style>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
<div class="bounce">
    <%--占用库位查看--%>
    <div class="bonceContainer bounce-blue" id="holdStockSee" style="width:1000px;">
        <div class="bonceHead">
            <span>占用库位查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-none bg-yellow" id="holdStockInfo">
                    <thead>
                    <tr>
                        <td width="15%">商品代号</td>
                        <td width="15%">商品名称</td>
                        <td width="10%">型号</td>
                        <td width="10%">规格</td>
                        <td width="20%">创建人</td>
                        <td width="10%">计量单位</td>
                        <td width="10%">当前库存</td>
                        <td width="10%">占用库位</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div id="onlyNumInfo">该材料尚未选择库位</div>
                <div id="currentStation" class="resetCurrentStation"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce.cancel();">取消</span>
<%--            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="holdStockSeeReset" onclick="reSelectStock(null);">去选择库位</span>--%>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>成品查询</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="page">
                    <div class=" page1">
                        <div class="ty-con" id="one">
                            <div class="ty-page" >要查找的商品</div>
                            <div class="ty-catch">
                                <input class="ask" id="search_text" onkeypress="getMainList(1)" onclick="AutoComplete()"
                                       placeholder="请录入商品代号/商品名称">
                                <button class="next funBtn" data-fun="searchBtn">确定</button>
                            </div>
                        </div>
                    </div>
                    <div class="page2">
                        <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue" data-fun="goMain">返回成品主页</span>
                        <p style="margin-top:20px; ">
                            <span>符合查询条件的商品如下</span>
                        </p>
                        <table class="ty-table ty-table-control" id="gsTb">
                            <tr>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建人</td>
                                <td>计量单位</td>
                                <td>最低库存</td>
                                <td>当前库存</td>
                                <td>初始库存</td>
                                <td>占用库位</td>
                            </tr>
                            <tr>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建人</td>
                                <td>计量单位</td>
                                <td>最低库存</td>
                                <td>当前库存</td>
                                <td>初始库存</td>
                                <td><span class="ty-color-blue"></span></td>
                            </tr>
                        </table>
                        <div id="ye"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/storageSearch/accept.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>
</body>
</html>
