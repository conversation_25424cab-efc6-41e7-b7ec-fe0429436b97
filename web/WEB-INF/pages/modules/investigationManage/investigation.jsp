<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/investigationManage/questionBank.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .mainCon {
        max-width: 1200px;
    }
    .ty-linkBtn:hover{
        text-decoration: underline;
    }
    .ty-linkBtn{
        cursor: pointer;
        color: #0b94ea; display: inline-block; padding: 3px 10px;
    }
    .oneLine{
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        word-break: break-all !important;
        text-align: left !important;
    }
    .lineDot{
        width: 100px;
        display: inline-block;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        word-break: break-all !important;
    }
    .qAnsList .ty-color-blue:hover{ background: #0b94ea; color: #fff; cursor: pointer;  }
    .qAnsList .ty-color-blue{
        border-radius: 3px;
        display: inline-block; padding: 2px 10px; margin-left: 40px;
    }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed">
    <%-- 截止日期履历--%>
    <div class="bonceContainer bounce-green" id="endtimLog">
        <div class="bonceHead">
            <span>截止日期履历</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>本次调查问卷提交的截止日期：<span class="endTime"></span></p>
            <p>之前设置过的截止日期如下： </p>
            <table class="ty-table" id="endtimLogTab">
                <tr><td>截止日期</td><td>创建/修改的操作</td></tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 封面图片--%>
    <div class="bonceContainer bounce-green" id="UploadFImg">
        <div class="bonceHead">
            <span>封面图片</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <p>微信发布的链接封面上将展示此图片。如需要，可更换。</p>
            <div class="imgCC">
                <div class="fImg">
                    <img src="${ path }" alt="封面图片">
                    <span class="hd">${ JSON.stringify({ 'path': path  }) }</span>
                </div>
            </div>
            <p style="margin-top: 20px">
                &nbsp;
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="uploadFImg">更换</span>
            </p>
        </div>
        <div class="bonceFoot">
        </div>
    </div>

    <%-- 分享到微信后的效果 --%>
    <div class="bonceContainer bounce-green" id="shareView" style="width: 300px; ">
        <div class="shareViewbg" >
            <div class="back" onclick="bounce_Fixed.cancel()"><i class="backBtn  fa fa-angle-left"></i>微信好友或群名</div>
            <div class="txtCon">
                <div class="ttl"></div>
                <div style="display: flex;">
                    <div class="txt">
                        问卷提交截止日期: 2022-22-22 <br> 机构简称
                    </div>
                    <div class="fim">
                    </div>
                </div>
            </div>
        </div>
    </div>


    <%-- 预览效果 --%>
    <div class="bonceContainer bounce-green" id="preView" style="width: 350px; ">
        <div class="preView" >
            <div class="preTtl">
                <i class="fa fa-times preViewD" onclick="bounce_Fixed.cancel()"></i>
                问卷调查
            </div>
            <div id="preViewC" style="height:550px; overflow:auto;  ">
                <p class="memo"></p>
                <h3 class="ttl"></h3>
                <div class="preface"></div>
                <div class="main">
                    <div class="cat">
                        <h4>问卷类别描述的数据</h4>
                        <div class="ques">
                            <p>1、问题1的问题内容（字号同问卷类别描述，但字体不加粗）</p>
                            <div>选项</div>
                            <div>选项</div>
                            <div>选项</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>

<div class="bounce">
    <%-- 新建调查--%>
    <div class="bonceContainer bounce-green" id="createInv">
        <div class="bonceHead">
            <span>调查管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon manageInvBC" >
            <div>
                <p>请选择本次要调查的问卷 <i class="ty-color-red">*</i>
                    <span class="linkBtn ty-right" data-fun="prevScan">预 览</span>
                </p>
                <select style="width:400px; " id="preID" class="bankList form-control" onchange="showNext()"></select>
            </div>
            <div class="marTp16px">
                <div class="flexcc">
                    <div>
                        <p>查看或更换封面图片，请点击“封面图片”	</p>
                        <p class="blueTip">注：封面图片指分享至微信的链接封面上的图片。</p>
                    </div>
                    <div class="fengm" onclick="openUploadFImg()">
                        封面图片
                    </div>
                </div>
                <div style="display: none;">
                    <div id="uploadCC"></div>
                </div>
                <div>
                    <p> 问卷提交后，多长时间内可修改？请设置 </p>
                    <select id="add_min">
                        <option value=""></option>
                        <option value="1">1分钟</option>
                        <option value="10">10分钟</option>
                        <option value="20">20分钟</option>
                        <option value="30">30分钟</option>

                    </select>
                </div>
                <div>
                    <p>请选择问卷提交的截至日期<i class="ty-color-red">*</i></p>
                    <input type="text" id="endDate" />
                    <p class="blueTip">注：所选日期24：00后，问卷将无法再提交。</p>
                </div>
                <div>
                    <p>使用Wonderss的手机端，即可将所创建的调查问卷分享到微信！<br>
                        <span class="blueTip">注：Wonderss手机端里，该功能在“工作”下的“调查管理”中</span>
                    </p>
                </div>

            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="createInvOk">确定</span>
        </div>
    </div>
    <%-- 调查管理--%>
    <div class="bonceContainer bounce-blue" id="manageInv">
        <div class="bonceHead">
            <span>调查管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon manageInvBC" >
            <div>
                使用Wonderss的手机端，即可将所发起的调查问卷分享到微信！<br>
                <span class="blueTip">注：Wonderss手机端里，该功能在“工作”下的“调查管理”中</span>
                <div style="text-align: right">
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="shareView">分享到微信后的效果</span>
                </div>
            </div>
            <div>
                问卷提交后，多长时间内可修改？请设置 <br>
                <select id="manage_upTim" class="form-control">
                    <option value="">请选择</option>
                    <option value="1">1分钟</option>
                    <option value="10">10分钟</option>
                    <option value="20">20分钟</option>
                    <option value="30">30分钟</option>
                </select>
            </div>
            <div class="endDateFm">
                <i class="fa fa-circle-o"></i> 修改问卷提交的截止日期
                <span class="ty-btn ty-btn-blue ty-right ty-btn-big ty-circle-5" data-fun="endtimLog">截止日期履历</span>
            </div>
            <div class="timCC">
                <input type="text" id="upEndDate" class="form-control" placeholder="请选择 截止日期"/>

            </div>
            <div class="endTestCC">
                <i class="fa fa-circle-o"></i> 立即终止本次调查
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="manageInvOk">确定</span>
        </div>
    </div>
    <%-- 调查管理2--%>
    <div class="bonceContainer bounce-blue" id="manageInv2" style="width: 600px;">
        <div class="bonceHead">
            <span>调查管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon manageInvBC" >
            <div class="flexcc">
                <div>
                    <p>本次调查问卷提交的截止日期已过！</p>
                    <p>该截止日期：<span class="endtim"></span></p>
                </div>
                <div>
                    <span class="ty-btn ty-btn-blue ty-right ty-btn-big ty-circle-5" data-fun="endtimLog">截止日期履历</span>
                </div>
            </div>
            <div>
                使用Wonderss的手机端，即可将所发起的调查问卷分享到微信！<br>
                <span class="blueTip">注：Wonderss手机端里，该功能在“工作”下的“调查管理”中</span>
                <div style="text-align: right">
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="shareView">分享到微信后的效果</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5"  onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--  重新生成二维码--%>
    <div class="bonceContainer bounce-green" id="reSaveQRcode">
        <div class="bonceHead">
            <span>重新生成二维码</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <form autocomplete="off">
                <span id="createTip"></span><br/>
                <p>请选择将要生成二维码的到期日</p>
                <input type="text" class="form-control" id="endDate2" style="width:200px; ">
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="reSaveQROk">确定</span>
        </div>
    </div>
    <%-- 二维码生成记录--%>
    <div class="bonceContainer bounce-blue" id="scanQRLog" style="width: 800px;">
        <div class="bonceHead">
            <span>二维码生成记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <tr>
                    <td>生成日期</td>
                    <td>设定的到期日</td>
                    <td>实际的作废日期</td>
                    <td>作废的操作</td>
                </tr>
                <tr>
                    <td>生成日期</td>
                    <td>设定的到期日</td>
                    <td>实际的作废日期</td>
                    <td>作废的操作</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 数据展示设置--%>
    <div class="bonceContainer bounce-blue" id="showSet" style="width: 800px;">
        <div class="bonceHead">
            <span>展示设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <span class="ty-right">
                <span class="ty-linkBtn funBtn" data-fun="allSelectBtn">全选</span>
                <span class="ty-linkBtn funBtn" data-fun="allNoSelectBtn">全不选</span>
            </span>
            <p>页面上要展示哪些问题？请进行设置。</p>
            <table class="ty-table ty-table-control">
                <tr>
                    <td width="10%">题号</td>
                    <td width="60%">问题</td>
                    <td width="10%">状态</td>
                    <td width="20%">选为展示对象</td>
                </tr>

            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" data-fun="updateShowQuestion">确定</span>
        </div>
    </div>
    <%-- 导出设置--%>
    <div class="bonceContainer bounce-blue" id="exportSet" style="width: 800px;">
        <div class="bonceHead">
            <span>原始数据导出</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
             <span class="ty-right">
                <span class="ty-linkBtn funBtn" data-fun="allSelectBtn2">全选</span>
                <span class="ty-linkBtn funBtn" data-fun="allNoSelectBtn2">全不选</span>
            </span>
            <p>请选择要导出哪些问题的原始数据。</p>
            <table class="ty-table ty-table-control">
                <tr>
                    <td width="10%">题号</td>
                    <td width="60%">问题</td>
                    <td width="10%">状态</td>
                    <td width="20%">选为展示对象</td>
                </tr>

            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" data-fun="exportData">导出</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>调查管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content"   >
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="mainCon mainCon11">
                    <div class="clear line" style="text-align: right">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="goStop" >已终止的调查</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="createInv" >发起新的调查</span>
                        <br>
                        <div class="ty-right searchSect searchSect2">
                            <div class="ty-left keywordSearch"><span class="ty-left">查找</span>
                                <div class="inputBox ty-right">
                                    <i></i>
                                    <input type="text" id="searchKey" placeholder=" 请输入关键字或素材代号">
                                </div>
                            </div>
                            <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchBtn1">确定</button>
                        </div>
                    </div>
                    <div>
                        <p class="marTop20">进行中的调查共以下 <span class="num1"></span> 个</p>
                        <table class="ty-table ty-table-control widthTab">
                            <tr>
                                <td>所用问卷</td>
                                <td>调查发起时间</td>
                                <td>问卷提交截止日期</td>
                                <td>已回收的问卷</td>
                                <td>其他操作</td>
                            </tr>
                        </table>
                        <div id="page11"></div>
                    </div>
                </div>
                <%-- 搜索 1--%>
                <div class="mainCon mainCon22">
                    <div>
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" data-fun="back22">返回</span>
                    </div>
                    <div>
                        <p class="marTop20">进行中的调查共以下 <span class="num1"></span> 个</p>
                        <table class="ty-table ty-table-control widthTab">
                            <tr>
                                <td>所用问卷</td>
                                <td>调查发起时间</td>
                                <td>问卷提交截止日期</td>
                                <td>已回收的问卷</td>
                                <td>其他操作</td>
                            </tr>
                        </table>
                        <div id="page1Search"></div>

                    </div>

                </div>
                <%-- 已终止的调查 --%>
                <div class="mainCon mainCon55">
                    <div class="ty-right searchSect">
                        <div class="ty-left keywordSearch"><span class="ty-left">其他年份</span>
                            <div class="inputBox ty-right">
                                <i></i>
                                <input type="text" id="endSearchYear" placeholder="请选择">
                            </div>
                        </div>
                        <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchBtn2">确定</button>
                    </div>
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back55">返回</span>

                    </p>
                    <p class="marTop20"><span class="year">XXXX</span>年已终止的调查共以下<span class="num">XX</span>份 </p>
                    <table class="ty-table ty-table-control" >
                        <tr>
                            <td>月份</td>
                            <td>已终止的调查</td>
                        </tr>

                    </table>
                </div>
                <%-- 已终止的调查 列表 --%>
                <div class="mainCon mainCon66">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back66">返回</span>
                    </div>
                    <div>
                        <p class="marTop20"><span class="month"></span>已终止的调查共以下<span class="num6"></span>个。</p>
                        <table class="ty-table ty-table-control widthTab">
                            <tr>
                                <td>所用问卷</td>
                                <td>调查发起时间</td>
                                <td>问卷提交截止日期</td>
                                <td>已回收的问卷</td>
                                <td>其他操作</td>
                            </tr>
                        </table>
                        <div id="page66"></div>
                    </div>
                </div>
                <%-- 查看问卷 --%>
                <div class="mainCon mainCon77">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back77" data-fun="back77">返回</span>
                    </div>
                    <div class="marTop20">
                        <div class="investigation">
                            <p class="memo"></p>
                            <h3 class="ttl"></h3>
                            <div class="preface"></div>
                            <div class="main">
                                <div class="cat">
                                    <h4>问卷类别描述的数据</h4>
                                    <div class="ques">
                                        <p>1、问题1的问题内容（字号同问卷类别描述，但字体不加粗）</p>
                                        <div>选项</div>
                                        <div>选项</div>
                                        <div>选项</div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
                <%-- 查看答卷列表 --%>
                <div class="mainCon mainCon88">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back88" data-fun="back88">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back881" data-fun="back88">返回主页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back881" data-fun="back881">返回上一页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right marLeft100 exportDataSet" data-fun="exportDataSet">原始数据导出</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right marLeft100" data-fun="dataShowSet">本页展示设置</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right notJiData" data-fun="notJiData">未纳入统计的数据</span>
                    </div>
                    <div class="marTop20">
                        <p>截至 <span class="time"></span>，问卷已回收<span class="num"></span>份，具体如下：</p>
                        <div style="width: 100%; overflow: auto; padding:0 0 20px 0;">
                            <table class="ty-table ty-table-control" id="tab88"  >
                                <tr>
                                    <td><span class="lineDot" >提交时间</span></td>
                                    <td>调查发起时间</td>
                                    <td>已回收的问卷</td>
                                    <td>其他操作</td>
                                </tr>
                            </table>
                        </div>
                        <div id="page88"></div>
                    </div>
                </div>
                <%-- 调查分析 --%>
                <div class="mainCon mainCon99">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " data-fun="back99">返回</span>
                    </div>
                    <div class="marTop20">
                        <p class="df12">  本次调查共有<span class="qMun"></span>道题，
                            截至<span class="endTime"></span> ，
                            共回收<span class="ansNum"></span>份</p>
                        <p>  点击查看，可查看相应题型的数据</p>
                        <table class="ty-table ty-table-control" id="analysisAns">
                            <tr>
                                <td>题型</td>
                                <td>数量</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td>问答题（含常规问答题与特殊型式的问答题）<br>
                                    年、月或日期的题<br>
                                    国内省、市、地区或地址的题</td>
                                <td class="q1Num"></td>
                                <td><span class="ty-color-blue funBtn" data-fun="getQAnsysList" data-type="1">查看</span></td>
                            </tr>
                            <tr>
                                <td>正误判断题、常规单选题</td>
                                <td class="q2Num"></td>
                                <td><span class="ty-color-blue funBtn" data-fun="getQAnsysList" data-type="2">查看</span></td>
                            </tr>
                            <tr>
                                <td>常规多选题</td>
                                <td class="q3Num"></td>
                                <td><span class="ty-color-blue funBtn" data-fun="getQAnsysList" data-type="3">查看</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <%-- 各题型 问题查看 --%>
                <div class="mainCon mainCon1010">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="back1010">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="exportBtn" data-place="1">导出</span>
                    </div>
                    <div class="marTop20">
                        <p class="df1"></p>
                        <div>
                            <div class="type1">
                                常规问答题与系统中特殊型式的问答题共<span class="q1Num2"></span>道，情况如下
                                <div class="type1List" style="padding-left: 32px;"></div>
                                <div class="pagetype1"></div>
                            </div>
                            <div class="type2">
                                常规单选题与正误判断题共<span class="q2Num2"></span>道，情况如下
                                <div class="type2List" style="padding-left: 32px;"></div>
                                <div class="pagetype2"></div>
                            </div>
                            <div class="type3">
                                <span class="df123">常规多选题共<span class="q3Num2"></span>道，各题各个选项被选择的情况统计如下</span>
                                <div class="type3List" style="padding-left: 32px;"></div>
                                <div class="pagetype3"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <%-- 问答题的 答案 详情 --%>
                <div class="mainCon mainCon1111">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right allDataBtn" data-fun="allDataBtn">全部数据</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="back1111">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="exportBtn" data-place="2">导出</span>
                    </div>
                    <div class="marTop20">
                        <p class="qInfo"></p>
                        <p><span class="qwe"></span></p>
                        <div class="qAnsList"></div>
                        <div class="page11"></div>
                    </div>
                </div>
                <%-- 多选题的 切换至答卷者选择结果的统计报表 --%>
                <div class="mainCon mainCon1212">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="back1111">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="exportBtn" data-place="3">导出</span>
                    </div>
                    <div class="marTop20">
                        <div>
                            <div class="type3" style="padding-left: 32px; ">
                                <span class="chang1"></span>
                                <p class="questionInfo"></p>
                                <table class="ty-table optionTab">
                                    <tr><td>选项</td></tr>
                                    <tr><td>选项</td></tr>
                                </table>
                                <div class="marTop20">问卷共回收 <span class="pall"></span>份，其中 <span class="pans"></span>份回答了本题，回答者按选择结果统计如下：</div>
                                <table class="ty-table ansTab">
                                    <tr><td>选择结果</td><td>选择该项的数量</td><td>占比</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <%-- 年月日 省市区的 详情 和  全部数据 --%>
                <div class="mainCon mainCon1313">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="back1313">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="exportBtn" data-place="4">导出</span>
                    </div>
                    <div class="marTop20">
                        <p class="qInfo13"></p>
                        <p><span class="qwe13"></span></p>
                        <div class="qAnsList13"></div>
                        <div class="page13"></div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<%--<script src="../assets/global/plugins/jquery.qrcode.min.js?v=SVN_REVISION" type="text/javascript"></script>--%>
<script src="../script/investigationManage/investigation.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
