<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/investigationManage/questionBank.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="alertTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>

</div>
<div class="bounce">
    <%-- 删除 --%>
    <div class="bonceContainer bounce-green" id="tipConImg" >
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <img src="" id="tipImg" style="width:100%; ">
        </div>
    </div>
    <%-- 删除 --%>
    <div class="bonceContainer bounce-green" id="bankDel" >
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="bankDelOk">确定</span>
        </div>
    </div>
    <%-- 删除 --%>
    <div class="bonceContainer bounce-green" id="quesDel" >
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" class="tip">确定删除本道问题吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="qDelOk">确定</span>
        </div>
    </div>
    <%-- 退出新增问卷 --%>
    <div class="bonceContainer bounce-green" id="backAddBank" >
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" class="tip">退出后，系统将不保留您本次编辑的问卷。<br>确定退出？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="back">确定</span>
        </div>
    </div>

    <%-- 停启用 --%>
    <div class="bonceContainer bounce-green" id="bankStartOrStop" >
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="bankStartOrStopOk">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-green" id="editQuestion1" >
        <div class="bonceHead">
            <span class="editQuestion1Ttl"></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form autocomplete="off">
                <div class="pannel">
                    <div>
                        <span> 问题内容</span>
                        <span class="lenTip qContentLen ty-right">0/80</span>
                    </div>
                    <div class="clearInput">
                        <textarea id="qContent" class="form-control" placeholder="" onchange="setWordsNum($(this),80)"></textarea>
                        <i class="fa fa-times clearTextAreaVal" onmousedown="clearPrevVal($(this))"></i>
                    </div>
                </div>
                <div>
                    <p>请选择题型</p>
                    <div class="radioArea">
                        <div class="type1and2">
                            <span data-val="1"><i class="fa fa-circle-o"></i> 常规问答题<span class="bluetip">注：录入框为一个且不可新增、仅可输入文字</span></span>
                            <span data-val="2"><i class="fa fa-circle-o"></i> 正误判断题</span>
                        </div>
                        <div class="type3and4">
                            <span data-val="3"><i class="fa fa-circle-o"></i> 常规单选题</span>
                            <span data-val="4"><i class="fa fa-circle-o"></i> 常规多选题</span>
                        </div>
                        <div class="type5and6">
                            <span data-val="5"><i class="fa fa-circle-o"></i> 几种特殊型式的问答题</span>
                            <span data-val="6" style="color:#aaa; "><i class="fa fa-circle-o" style="color:#aaa; "></i> 需上传附件的题</span>
                        </div>
                        <div class="typenum typenum5">
                            <span data-val="51"><i class="fa fa-circle-o"></i> 录入必须为电子邮箱格式的问答题</span>
                            <span data-val="52"><i class="fa fa-circle-o"></i> 录入必须为11位的手机号的问答题</span>
                            <span data-val="53"><i class="fa fa-circle-o"></i> 可新增录入框的问答题</span>
                        </div>
                        <div class="type7and8">
                            <span data-val="7"><i class="fa fa-circle-o"></i> 国内省、市、地区或地址的题</span>
                            <span data-val="8"><i class="fa fa-circle-o"></i> 年、月或日期的题</span>
                        </div>
                        <div class="typenum typenum7">
                            <p class="bluetip">注：选项中的国内省份均含国内各省、自治区、直辖市及港澳台</p>
                            <span data-val="71"><i class="fa fa-circle-o"></i> 仅可单选，且选项为国内省份</span>
                            <span data-val="72"><i class="fa fa-circle-o"></i> 仅可单选，且选项为国内省份与城市</span>
                            <span data-val="73"><i class="fa fa-circle-o"></i> 仅可单选，且选项为国内省份、城市与地区</span>
                            <span data-val="74"><i class="fa fa-circle-o"></i> 需单选，且选项为国内省份、城市与地区，此外还可录入具体地址</span>
                        </div>
                        <div class="typenum typenum8">
                            <%--<p class="bluetip">注：附件需为图片、word、excel、ppt或pdf型式。且单个不得大于20M，否则不得上传</p>--%>
                            <span data-val="81"><i class="fa fa-circle-o"></i> 选项为年历，答题者需选择为具体哪年的题</span>
                            <span data-val="82"><i class="fa fa-circle-o"></i> 选项为月历，答题者需选择为具体哪年哪月的题</span>
                            <span data-val="83"><i class="fa fa-circle-o"></i> 选项为日历，答题者需选择具体日期的题</span>
                        </div>
                    </div>
                </div>
                <div class="qType">
                    <div class="qType1"></div>
                    <div class="qType2 marTop20">
                        <p>选项默认为“是”、“否”。如需要，可修改。</p>
                        <div class="pannel">
                            <div>
                                <span>选项1</span>
                            </div>
                            <div class="clearInput">
                                <input type="text" class="form-control" placeholder="" />
                                <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                            </div>

                        </div>
                        <div class="pannel">
                            <div>
                                <span>选项2</span>
                            </div>
                            <div class="clearInput">
                                <input type="text" class="form-control" placeholder="" />
                                <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                            </div>
                        </div>
                    </div>
                    <div class="qType3 qType4 marTop20">
                        <p>
                            <span>请录入各选项的内容。如需要，可“增加选项”。</span>
                            <span class="linkBtn ty-right" data-fun="addOption">新增选项</span></p>
                        <div class="optionPannel">
                            <div class="pannel ">
                                <div>
                                    <span>选项</span>
                                    <span class="lenTip ty-right">0/18</span>
                                </div>
                                <div class="clearInput">
                                    <textarea class="form-control" placeholder="请录入，字数上限为80个。" onchange="setWordsNum($(this),80)"></textarea>
                                    <i class="fa fa-times clearTextAreaVal" onmousedown="clearPrevVal($(this))"></i>
                                </div>
                            </div>
                            <div class="pannel ">
                                <div>
                                    <span>选项</span>
                                    <span class="lenTip ty-right">0/18</span>
                                </div>
                                <div class="clearInput">
                                    <textarea class="form-control" placeholder="请录入，字数上限为80个。" onchange="setWordsNum($(this),80)"></textarea>
                                    <i class="fa fa-times clearTextAreaVal" onmousedown="clearPrevVal($(this))"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="saveQuestion">保存</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="moveQuestion" >
        <div class="bonceHead">
            <span>移动</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                要将本问题移到第
                <select id="qNoList" onchange="changeOprion(1)"><option value=""></option> </select>
                题之前。
            </p>
            <p>
                <span id="qNoFa" class="fa fa-circle-o" onclick="changeOprion(2)"></span>要将本问题移到最后。
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="moveQuestionOK">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>问卷库</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div class="ty-container">
                <%-- 主页面 --%>
                <div class="mainCon mainCon1">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-fun="addBank">新增问卷</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="goStop" >已停用的问卷</span>
                        <div class="ty-right searchSect">
                            <div class="ty-left keywordSearch"><span class="ty-left">查找</span>
                                <div class="inputBox ty-right">
                                    <i></i>
                                    <input type="text" id="searchKey" placeholder=" 请输入关键字或素材代号">
                                </div>
                            </div>
                            <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchBtn1">确定</button>
                        </div>
                    </div>
                    <div>
                        <p>发起调查时，可供选用的问卷共以下 <span class="num1"></span> 个</p>
                        <table class="ty-table ty-table-control widthTab">
                            <tr>
                                <td width="60%">问卷标题</td>
                                <td width="20%">创建</td>
                                <td width="20%">操作</td>
                            </tr>

                        </table>
                        <div id="page1"></div>
                        <div id="page1Search"></div>
                    </div>
                </div>
                <%-- 新增问卷， 编辑问卷1 --%>
                <div class="mainCon mainCon2">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 back" data-fun="back">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="addQuesNext">下一步</span>
                    </p>
                    <form autocomplete="off">
                        <div class="pannel">
                            <div>
                                <span><i class="red">*</i> 问卷标题</span>
                                <span class="ty-right">
                                    <span class="lenTip lenTip50">0/50</span>
                                    <span>什么是问卷标题？ <span class="linkBtn" data-fun="tipCon" data-val="1">查看</span></span>
                                </span>
                            </div>
                            <input type="text" id="bankName" class="form-control" placeholder="请录入问卷的标题，字数上限为50个。" onchange="setWordsNum($(this),50)"/>
                        </div>
                        <div class="pannel">
                            <div>
                                <span>问卷的页眉</span>
                                <span class="ty-right">
                                    <span class="lenTip">0/18</span>
                                    <span>什么是问卷页眉？ <span class="linkBtn" data-fun="tipCon" data-val="2">查看</span></span>
                                </span>
                            </div>
                            <input type="text" id="bankTip" class="form-control" placeholder="请录入问卷的页眉，字数上限为18个。" onchange="setWordsNum($(this),18)"/>
                        </div>
                        <hr>
                        <div class="addAttachCon">
                            <div>
                                <span>问卷标题与问题编辑完后，请点击“下一步”。</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="addNewQustion">新增问题</span>
                            </div>
                            <table class="ty-table ty-table-control" id="qusList">
                                <tr>
                                    <td>题号</td>
                                    <td>问题</td>
                                    <td>问题类型</td>
                                    <td>操作</td>
                                </tr>
                                <tr>
                                    <td>1</td>
                                    <td>填表人姓名</td>
                                    <td>问答题</td>
                                    <td>
                                        <span class="ty-color-blue" data-fun="qEidt">修改</span>
                                        <span class="ty-color-blue" data-fun="qAddPrev">在上方增加一道题</span>
                                    </td>
                                </tr>
                            </table>
                        </div>

                    </form>

                </div>
                <%-- 新增问卷， 编辑问卷2  --%>
                <div class="mainCon mainCon3">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backAddBank">退出</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="prevStep">上一步</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="saveBank">保存</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" style="margin-right:20px; " data-fun="scan">预览</span>
                    </p>
                    <form autocomplete="off">
                        <p>
                            <span>问卷共有问题 <span class="qNum"></span> 道，其中必答题 <span class="qMustNum"></span>  道</span>
                            <span class="linkBtn ty-right" data-fun="mustSet">必答题设置</span>
                        </p>
                        <hr>
                        <div class="pannel">
                            <div>
                                <span class="ty-right">
                                    <span class="lenTip prefaceTip">0/18</span>
                                    <%--<span>什么是问题前言？<span class="linkBtn" data-fun="tipCon" data-val="3">查看</span></span>--%>
                                </span>
                                <p>前言</p>
                            </div>
                            <textarea type="text" id="preface" class="form-control" style="height:304px;" placeholder="请录入前言，字数上限为1000个。" onchange="setWordsNum($(this),1000)"></textarea>
                        </div>
                        <hr>
                        <div class="pannel">
                            <div>
                                <span class="ty-right">
                                    <span class="lenTip lenTip50">0/18</span>
                                    <span>什么是问题类别描述？<span class="linkBtn" data-fun="tipCon" data-val="3">查看</span></span>
                                </span>
                                <p>问卷中，连续的问题属于一个类别时，您还可录入一个或多个“问题类别描述”。</p>
                            </div>
                            <input type="text" class="form-control desc1" placeholder="请录入问题的类别描述，字数上限为50个。" onchange="setWordsNum($(this),50)"/>
                            <p>
                                <span>请确定问题类别描述的位置：</span>
                                <span class="marLeft100">问题类别描述放到第<select class="desc1 form-control" ></select>题之前</span>
                                <span style="    position: relative; top: 12px;" class="linkBtn ty-right" data-fun="bankDescAdd">增加问题类别描述</span>
                            </p>
                        </div>
                        <div class="descriptionArea">
                        </div>
                    </form>
                </div>
                <%-- 新增问卷， 编辑问卷3 必答题设置 --%>
                <div class="mainCon mainCon4">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back4">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right saveMust" data-fun="saveMust">完成</span>
                    </p>
                    <p>在扫码者所见页上，您所设置的“必答题”将标以红色符号“<span class="red">*</span>”。 </p>
                    <p>不回答“必答题”，扫码者无法提交问卷。 </p>
                    <p><span class="red">重要提示：</span>“必答题”过多，与总题量过多类似，扫码者可能嫌麻烦不愿答题。 </p>
                    <hr>
                    <p>问卷共 <span class="allNum"></span> 道题，已选必答题 <span class="mustNum"></span> 道</p>
                    <table class="ty-table ty-table-control" id="mustTab">
                        <tr>
                            <td width="20%">题号</td>
                            <td width="20%">问题</td>
                            <td width="20%">问题类型</td>
                            <td width="20%">状态</td>
                            <td width="20%">选为必答题</td>
                        </tr>
                        <tr>
                            <td>题号</td>
                            <td>问题</td>
                            <td>问题类型</td>
                            <td>状态</td>
                            <td>
                                <span class="ty-color-blue funBtn" data-fun="qMustToggle">选择</span>
                            </td>
                        </tr>
                    </table>

                </div>
                <%-- 已停用的问卷 --%>
                <div class="mainCon mainCon5">
                    <div class="ty-right searchSect">
                        <div class="ty-left keywordSearch"><span class="ty-left">其他年份</span>
                            <div class="inputBox ty-right">
                                <i></i>
                                <input type="text" id="endSearchYear" placeholder="请选择">
                            </div>
                        </div>
                        <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchBtn2">确定</button>
                    </div>
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back51">返回</span>

                    </p>
                    <p class="marTop20"><span class="year">XXXX</span>年已停用的问卷共以下<span class="num">XX</span>份 </p>
                    <table class="ty-table ty-table-control" id="stopBankMonth">
                        <tr>
                            <td>月份</td>
                            <td>已停用的问卷</td>
                        </tr>
                        <tr>
                            <td>月份</td>
                            <td>
                                <span class="ty-color-blue" data-fun="bankScan">查看</span>
                            </td>
                        </tr>
                    </table>
                </div>
                <%-- 已停用的问卷列表 --%>
                <div class="mainCon mainCon6">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back5">返回</span>

                    </div>
                    <div>
                        <p class="marTop20">以下<span class="num6"></span>份问卷已被停用，除非被“启用”，否则发起调查时无法选用。 </p>
                        <table class="ty-table ty-table-control widthTab">
                            <tr>
                                <td>问卷标题</td>
                                <td>创建</td>
                                <td>停用</td>
                                <td>操作</td>
                            </tr>

                        </table>
                        <div id="page6"></div>
                    </div>
                </div>
                <%-- 查看问卷 --%>
                <div class="mainCon mainCon7">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back7" data-fun="back7">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-fun="copyToNew">复制，生成新问卷</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right noStop marRight20 update" data-fun="update">修改问卷</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right noStop marRight20 updateNormal" data-fun="updateNormal">修改问题以外的内容</span>
                    </div>
                    <div class="marTop20">
                        <p class="stopC">您可“复制，生成新问卷”。问卷中的问题无法直接修改。 </p>
                        <p class="startC">您可修改问卷中“问题”以外的内容，可“复制，生成新问卷”，但问卷中的问题无法直接修改。 </p>

                        <div class="investigation">
                            <p class="memo"></p>
                            <h3 class="ttl"></h3>
                            <div class="preface"></div>
                            <div class="main">
                                <div class="cat">
                                    <h4>问卷类别描述的数据</h4>
                                    <div class="ques">
                                        <p>1、问题1的问题内容（字号同问卷类别描述，但字体不加粗）</p>
                                        <div>选项</div>
                                        <div>选项</div>
                                        <div>选项</div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
                <%-- 查看问卷 --%>
                <div class="mainCon mainCon71">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back7" data-fun="back71">返回</span>
                    </div>
                    <div class="marTop20">
                        <div class="investigation">
                            <p class="memo"></p>
                            <h3 class="ttl"></h3>
                            <div class="tip"></div>
                            <div class="main">
                                <div class="cat">
                                    <h4>问卷类别描述的数据</h4>
                                    <div class="ques">
                                        <p>1、问题1的问题内容（字号同问卷类别描述，但字体不加粗）</p>
                                        <div>选项</div>
                                        <div>选项</div>
                                        <div>选项</div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
                <%-- 修改问卷-问题以外的内容--%>
                <div class="mainCon mainCon8" style="margin-bottom: 100px; ">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back8">返回</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="editBankNext">修改完毕，下一步</span>
                    </p>
                    <form autocomplete="off">
                        <div>
                            <p>问卷中下列内容修改后即生效，系统对修改不予记录。</p>
                            <div class="pannel">
                                <div>
                                    <span><i class="red">*</i> 问卷标题</span>
                                    <span class="ty-right">
                                        <span class="lenTip">0/50</span>
                                        <span>什么是问卷标题？ <span class="linkBtn" data-fun="tipCon" data-val="1">查看</span></span>
                                    </span>
                                </div>
                                <input type="text" id="bankName2" class="form-control" placeholder="请录入问卷的标题，字数上限为50个。" onchange="setWordsNum($(this),50)"/>
                            </div>
                            <div class="pannel">
                                <div>
                                    <span>问卷的页眉</span>
                                    <span class="ty-right">
                                        <span class="lenTip">0/18</span>
                                        <span>什么是问卷页眉？ <span class="linkBtn" data-fun="tipCon" data-val="2">查看</span></span>
                                    </span>
                                </div>
                                <input type="text" id="bankTip2" class="form-control" placeholder="请录入问卷的页眉，字数上限为18个。" onchange="setWordsNum($(this),18)"/>
                            </div>
                            <div class="pannel">
                                <div>
                                    <span>前言</span>
                                    <span class="ty-right">
                                        <span class="lenTip">0/1000</span>
                                        <%--<span>什么是问卷标题？ <span class="linkBtn">查看</span></span>--%>
                                    </span>
                                </div>
                                <textarea type="text" id="preface2" class="form-control" style="height:304px; " placeholder="请录入问卷的前言，字数上限为1000个。" onchange="setWordsNum($(this),1000)"></textarea>
                            </div>
                        </div>
                        <div>
                            <div class="pannel">
                                <div>
                                    <span>问卷中，连续的问题属于一个类别时，您还可录入一个或多个“问题类别描述”。</span>
                                    <span class="ty-right">
                                        <span class="lenTip">0/18</span>
                                        <span>什么是问题类别描述？<span class="linkBtn">查看</span></span>
                                    </span>
                                </div>
                                <input type="text" class="form-control desc1" placeholder="请录入问题的类别描述，字数上限为50个。" onchange="setWordsNum($(this),50)"/>
                                <p>
                                    <span>请确定问题类别描述的位置：</span>
                                    <span class="marLeft100">问题类别描述放到第<select class="desc1 form-control"></select>题之前</span>
                                    <span class="linkBtn ty-right" data-fun="bankDescAdd2">增加问题类别描述</span>
                                </p>
                            </div>
                            <div class="descriptionArea"></div>
                        </div>
                       <div>
                           <p>
                               <span>问卷中的必答题 </span>
                               <span class="linkBtn ty-right mustReSet" data-fun="mustReSet">重新设置</span>
                           </p>
                           <div style="background: #eee;" class="form-control">现有<span class="qMustNum"></span>道</div>
                       </div>
                    </form>
                </div>


            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/investigationManage/questionBank.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
