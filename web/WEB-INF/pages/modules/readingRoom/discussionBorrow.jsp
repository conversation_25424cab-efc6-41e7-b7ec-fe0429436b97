<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/discussion/discussion.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
    .item-title .ty-radio input + label{
        top: 8px;
    }
    .themeList{
        width: 360px;
        background-color: #f6f6f6;
        padding:  0 0 8px 0;
    }
    .discussionList{
        min-height: 400px;
        height: calc(100vh - 250px);
    }
    .yeCon{
        font-size: 12px;
        color: #333;
    }
    .themeItem:hover{
        background-color: #f2f2f2;
    }
    .contentNull{
        text-align: center;
        padding: 30px 0;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <%-- scanSetTip --%>
    <div class="bonceContainer bounce-blue " id="scanSetTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeRight(1)">我知道了</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 提示 --%>
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align:center; font-size:16px; margin-top: 20px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--文件借阅申请弹窗--%>
    <div class="bonceContainer bounce-blue" id="borrowApply" style="width: 500px">
        <div class="bonceHead">
            <span class="bounce_title">阅览申请</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="item-row">讨论组的阅览申请，最终取决于文管的审批。</div>
            <div class="item-row">先获得上级批准的阅览申请，更可能通过文管的审批。</div>
            <div class="item-row">您也可直接提交给文管，而无需他人审批。</div>
            <div class="hr"></div>
            <div class="item-row">
                <div class="item-title item-title-long">
                    <div class="ty-radio">
                        <input type="radio" name="isNeedOther" id="needOther" value="1" checked>
                        <label for="needOther"></label> 阅览申请需审批
                    </div>
                </div>
                <div class="item-content">
                    <select class="ty-inputSelect chooseApprover">
                        <option value="选择审批人" selected></option>
                    </select>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title item-title-long" style="width: 285px;">
                    <div class="ty-radio">
                        <input type="radio" name="isNeedOther" value="0" id="noNeedOther">
                        <label for="noNeedOther"></label> <span id="uploadDirect">直接向文管提交阅览申请，无需他人审批</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureBorrowApply" id="sureBorrowApplyBtn">确定</button>
        </div>
    </div>
    <%-- 文件查看基本信息 --%>
    <div class="bonceContainer bounce-blue " id="docInfoScan" style="width:800px">
        <div class="bonceHead">
            <span>基本信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <div class="infoCon clearfix">
                <h4 class="info_title" id="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" id="info_sn"></span></div>
                    <div class="trItem"><span class="ttl">保存位置：</span><span class="con" id="info_category"></span></div>
                    <div class="trItem"><span class="ttl">文件大小：</span><span class="con" id="info_size"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con" id="info_gn"></span></div>
                    <div class="trItem"><span class="ttl2">创建人：</span><span class="con"><span id="info_createName" class="info_name"></span><span id="info_createDate"></span></span></div>
                    <div class="trItem"><span class="ttl2">文件类型：</span><span class="con" id="info_version"></span></div>
                </div>
            </div>
            <div>
                <div><div class="trItem info_content"><span class="ttl">说明：</span><span class="con" id="info_content" style="max-width: 670px"></span></div></div>
            </div>
            <div class="infoCon clearfix">
                <div class="ty-left processHistory">
                    <div class="item-header" style="margin-left: 10px">
                        审批记录
                    </div>
                    <div class="processList">
                    </div>
                </div>
                <div class="ty-left censusInfo">
                    <div class="item-header">
                        统计信息
                    </div>
                    <div class="trItem">
                        <span class="ttl3">浏览次数：</span>
                        <span class="con times view_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="viewNumBtn" onclick="seeHandelRecordBtn(1)">浏览记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">下载次数：</span>
                        <span class="con times download_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="downloadNumBtn" onclick="seeHandelRecordBtn(2)">下载记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">移动次数：</span>
                        <span class="con times move_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="moveNumBtn" onclick="seeHandelRecordBtn(5)">移动记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件名称修改次数：</span>
                        <span class="con times name_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="changeNameNumBtn" onclick="seeHandelRecordBtn(6)">修改记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件编号修改次数：</span>
                        <span class="con times no_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="changeNoNumBtn" onclick="seeHandelRecordBtn(7)">修改记录</button>
                    </div>
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 高级查询弹窗 --%>
    <div class="bonceContainer bounce-blue " id="advancedSearch" data-type="0" style="width: 600px">
        <div class="bonceHead">
            <span>高级搜索</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <ul class="ty-secondTab">
                <li class="ty-active">文件搜索</li>
                <li>文件类别搜索</li>
            </ul>
            <div class="searchCon">
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件名称
                    </div>
                    <div class="eq_r">
                        <input type="text" class="name">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件编号
                    </div>
                    <div class="eq_r">
                        <input type="text" class="fileSn">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件类型
                    </div>
                    <div class="eq_r">
                        <select class="version">
                            <option value="">--请选择文件类型--</option>
                            <option value="doc">*.doc</option>
                            <option value="docx">*.docx</option>
                            <option value="xls">*.xls</option>
                            <option value="xlsx">*.xlsx</option>
                            <option value="zip">*.zip</option>
                            <option value="rar">*.rar</option>
                            <option value="apk">*.apk</option>
                            <option value="ipa">*.ipa</option>
                            <option value="ppt">*.ppt</option>
                            <option value="txt">*.txt</option>
                            <option value="pdf">*.pdf</option>
                            <option value="png">*.png</option>
                            <option value="jpg">*.jpg</option>
                            <option value="wps">*.wps</option>
                            <option value="et">*.et</option>
                        </select>
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        上传人
                    </div>
                    <div class="eq_r">
                        <input type="text" class="createName" />
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        上传时间
                    </div>
                    <div class="eq_r">
                        <input type="text" class="createDateBegin" id="uploadDateStart"> ~
                        <input type="text" class="createDateEnd" id="uploadDateEnd">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        换版人
                    </div>
                    <div class="eq_r">
                        <input type="text" class="updateName">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        换版时间
                    </div>
                    <div class="eq_r" id="postDate">
                        <input type="text" class="updateDateBegin" id="ChangeDateStart"> ~
                        <input type="text" class="updateDateEnd" id="ChangeDateEnd">
                    </div>
                </div>
            </div>
            <div class="searchCon" style="display: none">
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件类别
                    </div>
                    <div class="eq_r">
                        <input type="text" class="folderName">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="advancedSearchBtn">确定</button>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>讨论组</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container" style="position:relative; ">
                <div class="themeList">
                    <div class="searchBar">
                        <div class="searchInput" style="width: 80%">
                            <input type="text" class="ty-search" placeholder="搜索">
                            <i class="fa fa-times-circle clearInput" style="right: 5px; top: 8px;"></i>
                        </div>
                        <i class="fa fa-search" type="btn" data-name="searchTheme"></i>
                    </div>
                    <div class="discussionList">

                    </div>
                    <div id="ye_borrow"></div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    var isGeneral = generalType === '1' || generalType === '2'
    var isSuper = generalType === '0'
</script>
<script src="../script/readingRoom/discussionBorrow.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
