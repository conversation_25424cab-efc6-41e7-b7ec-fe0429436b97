<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/formulaManage/materialsInFm.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(6)">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 材料修改 --%>
    <div class="bonceContainer bounce-green" id="mtEntry">
        <div class="bonceHead">
            <span>材料修改</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="case3" style="margin:0 50px;">
                <p>本材料用于产品配方</p>
                <div class="orangeTip border">
                    修改说明：此处的修改仅能对以后所发的采购订单生效，对已存在于系统中的采购订单无法生效！
                </div>
            </div>
            <div>
                <div class="item" style="margin-top:20px; ">
                    <div style="position: relative;" class="col-md-4"><span class="item-title">材料名称<span class="red">*</span></span>
                        <input need name="name" type="text" require/>
                        <input need name="id" type="hidden" />
                    </div>
                    <div class="col-md-4"><span class="item-title">材料代号<span class="red">*</span></span><input need name="code" type="text" require/></div>
                    <div class="col-md-4">
                        <span class="item-title">计量单位<span class="red">*</span></span>
                        <select need type="text" id="unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                        <%--<input need type="hidden" name="unitId" require id="add_unitName">--%>
                        <span onclick="addUnit()" class="btnCat"> 新增</span>
                    </div>
                </div>
                <div class="item">
                    <div class="col-md-4"><span class="item-title">规格</span><input need name="specifications" type="text" /></div>
                    <div class="col-md-4"><span class="item-title">型号</span><input need name="model" type="text" /></div>
                </div>
                <div class="item">
                    <div class="col-md-12"><span class="item-title">备注</span><input need name="memo" type="text" style="width:760px;" onkeyup="countWords($(this), 100)"/><span class="textMax">0/100</span></div>
                </div>
            </div>

            <div class="clr"></div>
            <div>
                <div class="case3" style="margin:20px 50px;">
                    <p>系统中包含本材料的未完结采购订单共 <span class="orderNum"></span>个。</p>
                    <div>采购人员需要终止未完结的采购订单吗？
                        <input need type="hidden" name="terminateOrders">
                        <span class="terminateOrders">
                            <i class="fa fa-circle-o" data-num="1"></i>需要
                            <i class="fa fa-circle-o" data-num="0"></i>不需要
                        </span>
                    </div>
                    <div class="orangeTip" style="margin-top:20px; ">注：选择“需要”时，系统将向采购人员发送系统消息，但您仍需自行确认采购人员是否采取了实际行动。</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="mtEditOk() ">确定</span>
        </div>
    </div>
    <%-- 材料查看 --%>
    <div class="bonceContainer bounce-blue" id="mtScan">
        <div class="bonceHead">
            <span>材料查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <div class="col-md-6">
                    <span class="scanMt_state">本材料用于产品的配方。</span>
                </div>
                <div class="col-md-3"><span class="btnCat" id="editMtBtn" >修改材料的基本信息</span></div>
                <div class="col-md-3"><span class="disabled btnCat">材料基本信息的修改记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-2">&nbsp;</div>
                <div class="col-md-6"><span class="item-title">创建人</span><span class="scanMt_create">张三丰给 2015-15-15 14：12：12</span></div>
                <div class="col-md-4">&nbsp;</div>
            </div>
            <div class="item" style="margin-top: 20px;">
                <div class="col-md-4"><span class="item-title">材料名称</span><span class="scanMt_name"></span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span class="scanMt_code"></span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span class="scanMt_unit"></span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">规格</span><span class="scanMt_specifications"></span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span class="scanMt_model"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">备注</span><span class="scanMt_memo"></span></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关 闭</span>
        </div>
    </div>
    <%-- 停用材料查看 --%>
    <div class="bonceContainer bounce-blue" id="mtScanStop" style="width: 900px">
        <div class="bonceHead">
            <span>材料查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <div class="col-md-8 ty-color-blue stopElem" id="stopIntro"></div>
                <div class="col-md-3"><span class="disabled btnCat">材料基本信息的修改记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-3">
                    <span class="scanMt_state">本材料用于产品的配方。</span>
                </div>
                <div class="col-md-5"><span class="item-title">创建人</span><span class="scanMt_create">张三丰给 2015-15-15 14：12：12</span></div>
                <div class="col-md-4"><span class="disabled btnCat">材料停用/恢复使用的操作记录</span></div>
            </div>
            <div class="item" style="margin-top: 20px;">
                <div class="col-md-4"><span class="item-title">材料名称</span><span class="scanMt_name"></span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span class="scanMt_code"></span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span class="scanMt_unit"></span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">规格</span><span class="scanMt_specifications"></span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span class="scanMt_model"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">备注</span><span class="scanMt_memo"></span></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关 闭</span>
        </div>
    </div>
    <%-- 停用提示 --%>
    <div class="bonceContainer bounce-red " id="stopTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <div class="msg"></div>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="mtStopOk()">确定</span>
        </div>
    </div>
    <%-- 我知道了 系列提示 --%>
    <div class="bonceContainer bounce-red " id="iknow">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p class="msg"></p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>

</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>材料</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container">
                <input type="hidden" name="mainConShow">
                <%-- 主页面 --%>
                <div class="mainCon1">
                    <div class="flexCon">
                        <div>
                            <span>如下<span class="totalRows"></span>种材料正被用于配方中</span>
                        </div>
                        <div>
                            <div class="search">
                                <span>查找</span>
                                <input type="text" placeholder="请输入材料的代号或名称">
                                <span data-type="searchBtn" class="btnCat"><i></i></span>
                            </div>
                        </div>
                        <div>
                            <span>停止用于配方中的材料有<span class="stopNum"></span>种</span><span data-type="goStopMt" class="btnCat"> 去查看</span>
                        </div>
                    </div>
                    <table style="margin-top:10px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>使用该材料的配方</td>
                            <td>操作</td>
                        </tr>

                        </tbody>
                    </table>
                    <div id="yeCon1"></div>
                </div>
                <%-- 使用该材料的配方 --%>
                <div class="mainCon2">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回材料首页</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goback2" id="goback2">返回上一页</span>
                    </div>
                    <table style="margin-top:30px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                        </tr>
                        </tbody>
                    </table>
                    <p style="margin:20px auto 10px; ">
                        <span>使用过该材料的配方有如下<span class="usedNum"></span>个</span>
                    </p>
                    <table class="ty-table ty-table-control" >
                        <tbody>
                        <tr>
                            <td>配方代号</td>
                            <td>配方/材料名称</td>
                            <td>主料</td>
                            <td>辅料</td>
                            <td>创建人</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>操作人</td>
                            <td>
                                <span data-type="changeMt" class="btn ty-color-blue">更换材料</span>
                                <span data-type="changeMtLog" class="btn ty-color-blue">材料更换记录</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon2"></div>
                </div>
                <%-- 停用材料列表 --%>
                <div class="mainCon3">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回</span>
                    </div>
                    <div class="flexCon">
                        <div>曾用于配方的材料有如下<span class="totalRows"></span> 种</div>
                        <div>
                            <div class="search">
                                <span>查找</span>
                                <input type="text" placeholder="请输入材料的代号或名称">
                                <span data-type="searchBtnStop" class="btnCat"><i></i></span>
                            </div>
                        </div>
                    </div>
                    <table style="margin-top:15px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>停用时间</td>
                            <td>曾使用过该材料的配方</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>停用时间</td>
                            <td class="ty-td-control"><span data-type="mtUesdGs" class="btn ty-color-blue ">X种</span></td>
                            <td>
                                <span data-type="mtScan" class="btn ty-color-blue ">查看</span>
                                <span data-type="mtStart" class="btn ty-color-blue ">恢复使用</span>
                                <span data-type="mtDel" class="btn ty-color-red ">删除</span>
                                <span class="hd">{}</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon3"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/formulaManage/materialsInFm.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
