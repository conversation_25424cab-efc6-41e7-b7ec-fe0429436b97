<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/formulaManage/formula.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">

<div class="bounce">

    <%-- 配方查看 --%>
    <div class="bonceContainer bounce-blue" id="mtScan">
        <div class="bonceHead">
            <span>配方查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="gapIn ty-color-blue" id="stopIntro">
            </div>
            <div class="item clear">
                <span data-name="id" class="scanMt hd"></span>
                <div class="col-md-6"><span class="item-title">配方代号</span><span data-name="code" class="scanMt"></span></div>
                <div class="col-md-6"><span class="item-title ty-color-blue" onclick="updateFormula()" id="editMtBtn">修改</span><span class="ty-color-gray">修改记录</span></div>
            </div>
            <div class="item clear">
                <div class="col-md-6"><span class="item-title">配方名称</span><span data-name="name" class="scanMt"></span></div>
                <div class="col-md-6"><span class="item-title">材料名称</span><span data-name="materialName" class="scanMt"></span></div>
            </div>
            <div class="gapT">
                <p>配方中主料有如下<span id="scanZlNum"></span>种</p>
                <table class="ty-table ty-table-control" id="scanMainMat">
                    <thead>
                    <tr>
                        <td>原料代号</td>
                        <td>原料名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>份数</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
            <div class="gapT">
                <p>配方中辅料有如下<span id="scanFlNum"></span>种</p>
                <table class="ty-table ty-table-control" id="scanSubMat">
                    <thead>
                    <tr>
                        <td>原料代号</td>
                        <td>原料名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>份数</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="innerSn">产品图号</td>
                        <td class="name">产品名称</td>
                        <td class="specifications">规格</td>
                        <td class="model">型号</td>
                        <td class="unit">计量单位</td>
                        <td class="phrase">所处阶段</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--  产品查看  --%>
    <div class="bonceContainer bounce-blue" id="scanGS" style="width: 700px;">
        <div class="bonceHead">
            <span>产品查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>基本信息</p>
                <table class="ty-table">
                    <tbody>
                    <tr>
                        <td>产品图号</td>
                        <td>产品名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>所处阶段</td>
                        <td>加工部门</td>
                    </tr>
                    <tr>
                        <td class="innerSn">产品图号</td>
                        <td class="name">产品名称</td>
                        <td class="specifications">规格</td>
                        <td class="model">型号</td>
                        <td class="unit">计量单位</td>
                        <td class="phrase">所处阶段</td>
                        <td class="processDeptName">加工部门</td>
                    </tr>
                    <tr>
                        <td>产品说明</td>
                        <td colspan="6" class="memo" style="text-align:left ;"></td>
                    </tr>
                    </tbody>
                </table>
                <p></p><p>创建人：<span class="create">张三 2020-02-02 12：12：12</span></p>
            </div>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%-- 选择完确定的提示 --%>
    <div class="bonceContainer bounce-blue " id="selectTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>您已选择<span id="selectMtNum"></span>项！确认所选无误吗？</p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="selectMt()">确定</span>
        </div>
    </div>
    <%-- 停用提示 --%>
    <div class="bonceContainer bounce-red " id="stopTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <div class="msg"></div>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="mtStopOk()">确定</span>
        </div>
    </div>
    <%-- 删除提示 --%>
    <div class="bonceContainer bounce-red " id="delTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>确定删除此配方？</p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="mtDelOk()">确定</span>
        </div>
    </div>
    <%-- 我知道了 系列提示 --%>
    <div class="bonceContainer bounce-red " id="iknow">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p class="msg"></p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>

</div>
<div class="bounce_Fixed">
    <%-- 新增配方 --%>
    <div class="bonceContainer bounce-green" id="mtEntry">
        <div class="bonceHead">
            <span class="formulaTtl">新增配方</span>
            <a class="bounce_close" onclick="bounce_Fixed3.show($('#cancelFormulaTip'))"></a>
        </div>
        <div class="bonceCon">
            <div>
                <input type="hidden" class="entry" name="id" value="null"/>
                <input type="hidden" class="entry" name="product" id="productId" value="null"/>
                <div class="item clear">
                    <div class="col-md-6">
                        <div class="butCol col-formulaSn">
                            <span class="item-title">配方代号<span class="red">*</span></span><input id="formulaSn" class="entry" name="code" data-name="formulaSn" type="text" />
                        </div>
                    </div>
                    <div class="col-md-6"><span class="ty-color-blue ty-right btnDo" data-name="functionSee">功能说明</span></div>
                </div>
                <div class="item clear">
                    <div class="col-md-6">
                        <span class="item-title">配方名称</span><input id="formulaName" class="entry" name="name" type="text" />
                        <div class="blueTip">注：配方无名称时，本栏可不填写。</div>
                    </div>
                    <div class="col-md-6">
                        <span class="item-title">材料名称</span><input class="entry" name="materialName" type="text" />
                        <div class="blueTip">注：配方无对应的材料名称时，本栏可不填写。</div>
                    </div>
                </div>
                <div class="gapT">
                    <div class="gapIn clear">
                        <span>配方中主料有如下<span id="mainMatNum">0</span>种</span>
                        <button class="ty-right btnDo" id="entryMainMatBtn" data-type="1" data-name="entryFormulaMat">录入主料</button>
                    </div>
                    <table class="ty-table ty-table-control" id="mainMaterial">
                        <thead>
                        <tr>
                            <td>原料代号</td>
                            <td>原料名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>份数</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <div class="gapT">
                    <div class="gapIn clear">
                        <span>配方中辅料有如下<span id="subMatNum">0</span>种</span>
                        <button class="ty-right btnDo" id="entryFormulaMatBtn" data-type="2" data-name="entryFormulaMat">录入辅料</button>
                    </div>
                    <table class="ty-table ty-table-control" id="subMaterial">
                        <thead>
                        <tr>
                            <td>原料代号</td>
                            <td>原料名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>份数</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed3.show($('#cancelFormulaTip')); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addOrUpdateFormula() ">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--功能说明--%>
    <div class="bonceContainer bounce-blue" id="functionSee" style="width: 760px;">
        <div class="bonceHead">
            <span>功能说明</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>1 &nbsp;配方名称在其他模块均不展示，“主料”、“辅料”在其他模块不做区分。</p>
            <p>2 &nbsp;本模块内“原料”在其他模块均展示为“材料”。</p>
            <p>3 &nbsp;本模块仅管理配方所需的原料，“混料”需在“工序管理”模块内操作。</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">关闭</span>
        </div>
    </div>
    <%--录入主料/辅料--%>
    <div class="bonceContainer bounce-blue" id="entryFormulaMat" style="width: 700px;">
        <div class="bonceHead">
            <span>录入配方里的原料</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>您正在录入配方"<span id="currentName"></span>"所需的原料。</p>
            <p>您可选择已录入至系统的材料，也可录入新材料。</p>
            <input class="entry" type="hidden" name='id' value=""/>
            <input class="entry" type="hidden" name='csmId' value=""/>
            <input class="entry" type="hidden" id="currentStock" name='isCurrent' value="0"/>
            <table class="ty-table bd-none">
                <tbody>
                <tr>
                    <td>原料代号<i class="red">*</i></td>
                    <td>原料名称<i class="red">*</i></td>
                    <td width="14%">计量单位<span class="red">*</span>
                        <span onclick="addUnit()" id="addUnitBtn" class="addUnitBtn">新增</span></td>
                </tr>
                <tr>
                    <td class="innerSn-col">
                        <input type="text" id="innerSn" class="entry" name="code" data-name="code" data-old="" require/>
                        <div id="selecGS">
                            <option value=""></option>
                        </div>
                    </td>
                    <td class="innerName-col">
                        <input id="innerName" name="name" type="text" class="entry" data-name="name" data-old="" require/>
                        <div id="selecGN">
                            <option value=""></option>
                        </div>
                    </td>
                    <td>
                        <select type="text" class="entry" id="unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                        <input class="entry" type="hidden" name="unit" id="add_unitName">
                    </td>
                </tr>
                <tr>
                    <td>规格</td>
                    <td>型号</td>
                    <td>份数<i class="red">*</i></td>
                </tr>
                <tr>
                    <td><input class="entry" name="specifications" type="text"/></td>
                    <td><input class="entry" name="model" type="text"/></td>
                    <td><input class="entry" name="amount" id="amount" type="text" onkeyup="clearNum(this)" require/></td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td colspan="2"><input name="memo" class="entry long" type="text" placeholder="此处最多可录入100字" onkeyup="countWords($(this), 100)"/></td>
                </tr>
                </tbody>
            </table>
            <p class="textMax">0/100</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="partEntryOK" onclick="finishEnter(); ">录入完毕</button>
        </div>
    </div>
    <%--修改份数--%>
    <div class="bonceContainer bounce-blue" id="editNum" style="width: 550px;">
        <div class="bonceHead">
            <span>修改份数</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <tbody>
                <tr>
                    <td width="25%">原料代号</td>
                    <td width="25%">原料名称</td>
                    <td width="50%">计量单位</td>
                </tr>
                <tr>
                    <td class="code"></td>
                    <td class="name"></td>
                    <td class="unit"></td>
                </tr>
                <tr>
                    <td>规格</td>
                    <td>型号</td>
                    <td>份数</td>
                </tr>
                <tr>
                    <td class="specifications"></td>
                    <td class="model"></td>
                    <td>
                        <div class="amountPos">
                            <input name="amount" type="text" onkeyup="clearNum(this)"/><i class="clearStr" onclick="clearPre($(this))">X</i>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td colspan="2" class="memo"></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="finishNumEdit" onclick="finishNumEdit(); ">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(5)">确定</span>
        </div>
    </div>
    <%--  配方录入提示  --%>
    <div class="bonceContainer bounce-blue" id="addFormulaTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>配方中的原料已存入系统。</p>
            <p>确定保存该配方吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="addOrUpdateFormulaSure()">确定</span>
        </div>
    </div>
    <%--  配方关闭提示  --%>
    <div class="bonceContainer bounce-blue" id="cancelFormulaTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>确定放弃已录入的数据吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="mtEntryCancel()">确定</span>
        </div>
    </div>
    <%--  提示  --%>
    <div class="bonceContainer bounce-blue" id="giveTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-center">还有必填项尚未填写！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce_Fixed3.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 删除提示 --%>
    <div class="bonceContainer bounce-red " id="delObjTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p id="msgTip"></p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce_Fixed3.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="commonDelSure()">确定</span>
        </div>
    </div>
    <%--  新材料提示  --%>
    <div class="bonceContainer bounce-red" id="entryTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>您录入了系统中还没有的新材料。</p>
            <p>请确认是否需要库管员填写本材料的当前库存数量。</p>
            <p class="ty-color-blue">注：曾购买过的材料可能有库存，请选择“需要”！</p>
            <div class="space isCurrentStock clear">
                <div class="ty-left dots">
                    <span class="fa fa-circle-o" data-num="1"></span> 需要
                </div>
                <div class="ty-left dots">
                    <span class="fa fa-circle-o" data-num="0"></span> 不需要
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="finishEnterBtn" onclick="finishEnterSure()">确定</button>
        </div>
    </div>
    <%--  更换配方提示  --%>
    <div class="bonceContainer bounce-blue" id="changeFormula">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-center">确定更换该产品或零件的配方吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addOrUpdateFormulaSure('change')">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>配方</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content"  >
            <!--放内容的地方-->
            <div class="ty-container">
                <input type="hidden" name="mainConShow">
                <%-- 首页 --%>
                <div class="mainCon mainCon1">
                    <div class="singepannel">
                        <div class="areaOne clear">
                            <div class="ty-left">
                                <p>现有<span id="confirmNum">X</span>种“材料需由多种原料先混合”的产品或零件有待选择配方</p>
                                <p>现有配方有如下<span id="currentForula"></span>个</p>
                            </div>
                            <div class="col-b ty-left">
                                <p><span class="ty-color-blue btnDo" data-name="goSelect">去处理</span></p>
                                <p><span class="ty-color-blue btnDo" data-name="addMtBtn">新增配方</span></p>
                            </div>
                            <div class="col-c ty-right">
                                <p>已停用的配方有<span id="currentStopForula"></span>种<span class="col-btn ty-color-blue btnDo" data-name="goStopMt">去查看</span></p>
                                <div class="searchSect">
                                    <div class="ty-left keywordSearch">
                                        查找
                                        <input placeholder="请输入配方的代号或名称" id="searchKeyBase" />
                                    </div>
                                    <span class="ty-left ty-color-blue btnDo" data-name="searchBtn">确 定</span>
                                </div>
                            </div>
                        </div>
                        <table class="ty-table ty-table-control" id="forulaList">
                            <thead>
                            <tr>
                                <td class="10%">配方代号</td>
                                <td class="15%">配方/材料名称</td>
                                <td class="10%">主料</td>
                                <td class="10%">辅料</td>
                                <td class="15%">创建人</td>
                                <td class="15%">使用该配方的产品及零件</td>
                                <td class="25%">操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div id="yeCon1"></div>
                </div>
                <%-- 产品或零件待选择配方 --%>
                <div class="mainCon mainCon2">
                    <div class="singepannel">
                        <div class="backPre">
                            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回配方首页</span>
                        </div>
                        <div class="narrowWrap">
                            <div class="areaOne">
                                <p>如下<span id="unChooseNum"></span>种“材料需由多种原料先混合”的产品或零件有待选择配方</p>
                                <p>从配方角度选产品或零件更为便捷，强烈推荐！<span class="ty-color-blue tryBtn btnDo" data-name="chooseByMt">去试试</span></p>
                            </div>
                            <table class="ty-table ty-table-control" id="unChooseMat">
                                <thead>
                                <tr>
                                    <td class="10%">图号</td>
                                    <td class="15%">名称</td>
                                    <td class="10%">型号</td>
                                    <td class="10%">规格</td>
                                    <td class="15%">计量单位</td>
                                    <td class="15%">创建人</td>
                                    <td class="25%">操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>规格</td>
                                    <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                    <td>
                                        <span class="ty-color-blue ">查看</span>
                                        <span class="ty-color-blue ">去处理</span>
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <div id="yeCon2"></div>
                    </div>
                </div>
                <%--去试试--%>
                <div class="mainCon3">
                    <p style="margin-top:30px; ">
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回配方首页</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="gopre2">返回上一页</span>
                    </p>
                    <p>请选择一个配方！</p>
                    <table style="margin-top:20px; " class="ty-table ty-table-control" id="selectFormulaList" >
                        <thead>
                        <tr>
                            <td></td>
                            <td>配方代号</td>
                            <td>配方/材料名称</td>
                            <td>主料</td>
                            <td>辅料</td>
                            <td>创建人</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                    <div id="yeCon3"></div>
                </div>
                <%--去试试> 下一步--%>
                <div class="mainCon4">
                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回配方首页</span>
                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goPre3">返回上一页</span>
                    <span style="margin-top:50px; " class="ty-right">
                        <span class="select0 ty-btn ty-btn-blue" data-type="goPre3">返回上一步</span>
                        <span class="select1 ty-color-blue btnDo" data-name="selectMtOkBtn">挑选完毕，确定</span>
                    </span>
                    <ul class="ty-secondTab">
                        <li class="ty-active">待选择(<span>X</span>)</li>
                        <li>已选中(<span>X</span>)</li>
                    </ul>
                    <div>
                        <p style="margin-top:20px; ">您所选中的配方如下  </p>
                        <table class="ty-table ty-table-control"  >
                            <tbody>
                            <tr>
                                <td>配方代号</td>
                                <td>配方/材料名称</td>
                                <td>主料</td>
                                <td>辅料</td>
                                <td>创建人</td>
                                <td>操作</td>
                            </tr>
                            </tbody>
                        </table>

                        <div style="margin-top:50px; ">
                            <p class="select0">请选择使用该配方加工的的产品或零件</p>
                            <p class="select1">已选中的产品或零件如下。您可放弃已选中的数据。被放弃的数据将回到“待选择”，在本页则不再显示。</p>
                        </div>
                        <table class="ty-table ty-table-control">
                            <tbody>
                            <tr>
                                <td></td>
                                <td>图号</td>
                                <td>名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>创建人</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td><i class="fa fa-circle-o"></i></td>
                                <td>图号</td>
                                <td>名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>创建人</td>
                                <td>
                                    <span class="btn ty-color-blue" data-type="gsScan">查看</span>
                                    <span class="hd"></span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div id="yeCon4"></div>
                    </div>
                </div>
                <%-- 停用配方列表 --%>
                <div class="mainCon5">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回配方首页</span>
                    </div>
                    <p style="margin-top:30px; " >已停用的配方有如下 <span class="totalRows"></span> 种</p>
                    <table style="margin-top:15px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>配方代号</td>
                            <td>配方/材料名称</td>
                            <td>主料</td>
                            <td>辅料</td>
                            <td>停用时间</td>
                            <td>曾使用过该配方的产品及零件</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>停用时间</td>
                            <td class="ty-td-control"><span data-type="mtUesdGs" class="btn ty-color-blue ">X种</span></td>
                            <td>
                                <span data-type="fmScan" class="btn ty-color-blue ">查看</span>
                                <span data-type="fmStart" class="btn ty-color-blue ">恢复使用</span>
                                <span data-type="fmDel" class="btn ty-color-red ">删除</span>
                                <span class="hd">{}</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon5"></div>
                </div>
                <%-- 使用该材料的产品和零件 --%>
                <div class="mainCon6">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回配方首页</span>
                    </div>
                    <div class="narrowWrap">
                        <table style="margin-top:30px; " class="ty-table ty-table-control"  >
                            <tbody>
                            <tr>
                                <td>配方代号</td>
                                <td>配方/材料名称</td>
                                <td>主料</td>
                                <td>辅料</td>
                                <td>创建人</td>
                            </tr>
                            <tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>创建人</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <p style="margin:20px auto 10px; ">
                        <span>使用该配方的产品及零件有如下 <span class="totalRows"></span> 种</span>
                        <span class="ty-right">曾使用过该配方的产品及零件（<span class="usedNum"></span>种） <span data-name="fmUesdGs" class="ty-color-blue btnDo">去查看</span></span>
                    </p>
                    <table class="ty-table ty-table-control" >
                        <tbody>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>
                                <span data-type="changeMt" class="btn ty-color-blue">更换材料</span>
                                <span data-type="changeMtLog" class="btn ty-color-blue">材料更换记录</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon6"></div>
                </div>
                <%-- 材料更换记录 --%>
                <div class="mainCon7">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回配方首页</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="goPre69" data-type="goPre69">返回上一页</span>
                    </div>

                    <table style="margin-top:30px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td><span data-type="gsScan" class="ty-color-blue">查看</span></td>
                        </tr>
                        </tbody>
                    </table>
                    <p style="margin:20px auto 10px; ">
                        <span>该产品或零件配方的更换记录如下</span>
                    </p>
                    <table class="ty-table ty-table-control" >
                        <tbody>
                        <tr>
                            <td>配方代号</td>
                            <td>配方/材料名称</td>
                            <td>主料</td>
                            <td>辅料</td>
                            <td>操作人</td>
                            <td>操作属性</td>
                        </tr>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>操作人</td>
                            <td>操作属性</td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon7"></div>
                </div>
                <%-- 曾使用过该材料的产品和零件 --%>
                <div class="mainCon8">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回配方首页</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="goback2" data-type="goback2" data-num="0">返回上一页</span>
                    </div>

                    <table style="margin-top:30px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>配方代号</td>
                            <td>配方/材料名称</td>
                            <td>主料</td>
                            <td>辅料</td>
                            <td>创建人</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>创建人</td>
                        </tr>
                        </tbody>
                    </table>
                    <p style="margin:20px auto 10px; ">
                        <span>曾使用过该配方的产品及零件有如下 <span class="totalRows"></span> 种</span>
                    </p>
                    <table class="ty-table ty-table-control" >
                        <tbody>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>
                                <span data-type="changeMtLog" class="btn ty-color-blue">配方更换记录</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon8"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/formulaManage/formula.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
