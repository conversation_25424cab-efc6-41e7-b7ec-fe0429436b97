
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/wonderssProduct/sysCommon.css?v=SVN_REVISION" />
<link rel="stylesheet" type="text/css" href="../css/wonderssProduct/sysSet.css?v=SVN_REVISION" />
<link rel="stylesheet" type="text/css" href="../css/event/icon/iconfont.css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">

<div class="bounce">
    <div class="bonceContainer bounce-red" id="bounce_tip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-red sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="editProduct" style="width: 700px">
        <div class="bonceHead">
            <span class="bounce_title">新增产品</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="modelChoose">
                    <input type="hidden" id="editType">
                    <div class="ty-alert updateShow">您可修改本产品的名称，或更换模板</div>
                    <div class="item-flex">
                        <div class="item-title">产品名称：</div>
                        <div class="item-content">
                            <input class="kj-input" name="name" type="text" style="width: 100%">
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="item-title">请选择模板：</div>
                        <div class="item-content">
                            <select class="kj-select chooseModel" onchange="changeModelInfo($(this))" style="width: 100%"></select>
                        </div>
                    </div>
                </div>
                <div class="modelInfo">
                    <hr>
                    <div class="ty-alert">
                        已重命名模块的数量 <span id="renameNumber"></span>个
                    </div>
                    <div style="max-height:150px; overflow-y:auto; ">
                        <table class="kj-table kj-table-striped tbl_see_rename" style="width: 70%">
                            <thead>
                            <tr>
                                <td>原名称</td>
                                <td>新名称</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="ty-alert" style="margin-top: 16px">
                        主套餐内的模块
                    </div>
                    <table class="kj-table kj-table-striped tbl_see_mainModule">
                        <thead>
                        <tr>
                            <td>模块名称</td>
                            <td>下辖的一级菜单数量</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="ty-alert" style="margin-top: 16px">
                        使用本模板的用户增值功能的模块
                    </div>
                    <table class="kj-table kj-table-striped tbl_see_valueAddedModule">
                        <thead>
                        <tr>
                            <td>模块名称</td>
                            <td>下辖的一级菜单数量</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="ty-alert" style="margin-top: 16px">
                        与本模板增值功能模块对应的已有套餐
                        <div class="btn-group">
                            <span class="link-blue" onclick="seeUsableSetMenuBtn('seeModel')">查看</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="sureEditProduct()">提交</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="seeProduct" style="width: 700px">
        <div class="bonceHead">
            <span>产品查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <h4><span class="pdName"></span></h4>
                <div class="item-flex">
                    <div class="item-title">所选模板：</div>
                    <div class="item-content"><span class="pdModel"></span><div class="ty-right">操作人 <span class="pdTime"></span></div></div>
                </div>
                <div class="ty-alert">
                    已重命名模块的数量 <b class="renameNumber"></b> 个
                </div>
                <table class="kj-table tbl_see_rename" style="width: 70%">
                    <thead>
                    <tr>
                        <td>原名称</td>
                        <td>新名称</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    主套餐内的模块
                </div>
                <table class="kj-table kj-table-striped tbl_see_mainModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    使用本模板的用户增值功能的模块
                </div>
                <table class="kj-table kj-table-striped tbl_see_valueAddedModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    与本模板增值功能模块对应的已有套餐
                    <div class="btn-group">
                        <span class="link-blue" onclick="seeUsableSetMenuBtn('seeModel')">查看</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--编辑产品的提示--%>
    <div class="bonceContainer bounce-red" id="bounceFixed_tip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-red sureBtn">确定</span>
        </div>
    </div>
    <%-- 我知道了 提示 --%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon text-center">

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%--套餐清单查看--%>
    <div class="bonceContainer bounce-blue" id="seeUsableSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐清单查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="ty-alert">
                    本模板的用户不可选择的模块数为A，设系统内已有套餐数为B
                    需根据A、B，算出系统内不包含A的已有套餐数C，并需列出C的清单
                    机构可选择的套餐，即为上述C的清单，具体如下：
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>套餐名称</td>
                        <td>创建</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--套餐查看--%>
    <div class="bonceContainer bounce-blue" id="seeSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <h4><span class="setMenuName"></span></h4>
                <div class="ty-alert">
                    组成本套餐的模块
                    <div class="btn-group">
                        创建 <span class="create"></span>
                    </div>
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <div class="bonceContainer bounce-blue" id="seeModule" style="width: 700px">
        <div class="bonceHead">
            <span>模块查看</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <h4><b class="moduleName"></b></h4>
                <table class="kj-table moduleTable">
                    <thead>
                    <tr>
                        <td class="thisLevel">一级菜单</td>
                        <td class="nextLevel">
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td class="thisLevel">二级菜单</td>
                                    <td class="nextLevel">
                                        <table class="kj-table">
                                            <thead>
                                            <tr>
                                                <td class="thisLevel">三级菜单</td>
                                            </tr>
                                            </thead>
                                        </table>
                                    </td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel();">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>产品设置</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content"  >
            <!--放内容的地方-->
            <div class="ty-container" id="home">
                <input type="hidden" id="listType">
                <div class="ty-page-header" style="display: none">
                    <div class="page-header__left" onclick="back()">
                        <i class="iconfont icon-houtui icon-back"></i>
                        <span>返回</span>
                    </div>
                    <div class="page-header__content" onclick="backToMain()">
                        <i class="iconfont icon-zhuye icon-back"></i>
                        <span>主页</span>
                    </div>
                </div>
                <div class="page" page="main">
                    <div class="ty-alert">
                        下列产品供特殊机构新增机构时选用
                        <div class="btn-group">
                            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="newProduct()">新增产品</span>
                            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="seeStopProduct()">已停用的产品</span>
                        </div>
                    </div>
                    <table class="kj-table tbl_main">
                        <thead>
                        <tr>
                            <td>产品名称</td>
                            <td>所选模板</td>
                            <td>创建人</td>
                            <td>使用该产品的机构</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                    <div id="ye1"></div>
                </div>
                <%-- 停用的 --%>
                <div class="page" page="product_stop">
                    <div class="ty-alert">以下为曾用作新增机构，但已停用的产品</div>
                    <table class="kj-table kj-table-striped tbl_product_stop">
                        <thead>
                        <tr>
                            <td>产品名称</td>
                            <td>所选模板</td>
                            <td>创建人</td>
                            <td>曾使用该产品的机构</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr></tbody>
                    </table>
                    <div id="ye2"></div>
                </div>
                <%-- 使用该产品的机构 --%>
                <div class="page" page="product_useOrg" style="width: 700px">
                    <h4><span class="pdName"></span></h4>
                    <div class="item-flex">
                        <div class="item-title">所选模板：</div>
                        <div class="item-content"><span class="pdModel"></span><span class="ty-right">创建人 <span class="pdTime"></span></span></div>
                    </div>
                    <div class="ty-alert">
                        现有如下 <span class="orgNum"></span> 个机构在使用本产品
                        <div class="btn-group">
                            <span style="margin-right: 16px">曾使用过的机构</span><span class="link-blue seeOldOrg" data-id="0" onclick="seeOldOrg()">去看看</span>
                        </div>
                    </div>
                    <table class="kj-table kj-table-striped tbl_useOrg">
                        <thead>
                        <tr>
                            <td>机构名称</td>
                            <td>创建人</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye3"></div>
                </div>
                <%-- 曾使用该产品的机构 --%>
                <div class="page" page="product_usedOrg" style="width: 700px">
                    <h4><span class="pdName"></span></h4>
                    <div class="item-flex">
                        <div class="item-title">所选模板：</div>
                        <div class="item-content"><span class="pdModel"></span><span class="ty-right">创建人 <span class="pdTime"></span></span></div>
                    </div>
                    <div class="ty-alert">
                        曾有如下 <span class="orgNum"></span> 个机构使用过本产品
                    </div>
                    <table class="kj-table kj-table-striped tbl_usedOrg">
                        <thead>
                        <tr>
                            <td>机构名称</td>
                            <td>创建人</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye4"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/wonderssProduct/sysCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/wonderssProduct/sysSet.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>
</body>
</html>