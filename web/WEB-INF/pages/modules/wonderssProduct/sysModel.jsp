<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/wonderssProduct/sysCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/wonderssProduct/sysModel.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">

<div class="bounce_Fixed3">
    <div class="bonceContainer bounce-blue" id="seeModule" style="width: 700px">
        <div class="bonceHead">
            <span>模块查看</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <h4><b class="moduleName"></b></h4>
                <table class="kj-table moduleTable">
                    <thead>
                    <tr>
                        <td class="thisLevel">一级菜单</td>
                        <td class="nextLevel">
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td class="thisLevel">二级菜单</td>
                                    <td class="nextLevel">
                                        <table class="kj-table">
                                            <thead>
                                            <tr>
                                                <td class="thisLevel">三级菜单</td>
                                            </tr>
                                            </thead>
                                        </table>
                                    </td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--选择模块--%>
    <div class="bonceContainer bounce-blue" id="chooseModule" style="width: 700px">
        <div class="bonceHead">
            <span>选择模块</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="ty-alert">请选择模块！</div>
                <table class="kj-table kj-table-striped tbl_chooseModule">
                    <thead>
                    <tr>
                        <td></td>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="sureChooseModule()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="renameEdit"  style="width: 400px">
        <div class="bonceHead">
            <span>模块重命名</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="item-flex">
                    <span class="item-title">原名称</span> <input class="kj-input oldName" type="text" disabled>
                </div>
                <div class="item-flex">
                    <span class="item-title">新名称</span> <input class="kj-input newName" type="text">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="renameEditOk()">确定</span>
        </div>
    </div>

    <%--套餐查看--%>
    <div class="bonceContainer bounce-blue" id="seeSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <h4><span class="setMenuName"></span></h4>
                <div class="ty-alert">
                    组成本套餐的模块
                    <div class="btn-group">
                        创建 <span class="create"></span>
                    </div>
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 菜单重命名记录 --%>
    <div class="bonceContainer bounce-blue" id="reNameLog2" style="width: 612px">
        <div class="bonceHead">
            <span>菜单重命名记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="chuTip">初始名称为XXX，当前名称为XXXX菜单的重命名记录</p>
            <table class="ty-table ty-table-control " id="logList2">
                <tr>
                    <td>重命名的操作</td>
                    <td>重命名后菜单的名称</td>
                    <td>重命名的类别</td>
                </tr>
                <tr></tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>

    <%--关于模板--%>
    <div class="bonceContainer bounce-blue" id="aboutModel" style="width: 700px">
        <div class="bonceHead">
            <span>关于模板</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <p>1 特殊机构/管理平台上，操作者需在“模板管理”中设置“模板”，之后需在“产品设置”中将“模板”再定义为“产品”</p>
                <p>2 设置模板时，需先设置主套餐，之后设置“使用本模板的用户不可选择的模块”。系统将依据“使用本模板的用户不可选择的模块”清单，计算出选择该模板的用户还可选用哪些已有套餐</p>
                <p>3 设置套餐，意义在于向客户提供高性价比的功能组合</p>
                <p>4 用户选择模板（产品）时，模板（产品）选定后主套餐即以确定，系统的基本功能即以提供，用户同时可见到的主套餐以外的可选模块列表与可选套餐列表，并可在其中进行选择</p>
                <p>5 “主套餐”、“可选套餐”与特殊机构下“服务套餐”菜单的关系</p>
                <p>5.1 特殊机构/管理平台下有“服务套餐”菜单</p>
                <p>5.2 前述设置的“主套餐”与“可选套餐”均需“查重”且需通过，通过后即需在“服务套餐”菜单中产生相应的数据，“服务套餐”菜单下主要功能为定价</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">关闭</span>
        </div>
    </div>
    <%--创建新套餐--%>
    <div class="bonceContainer bounce-green" id="editSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>创建新套餐</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="ty-alert">
                    <span>套餐名称 <span class="ty-color-red">*</span></span>
                    <input type="text" class="kj-input" name="name" style="margin-left: 16px; flex: auto">
                </div>
                <div class="ty-alert">
                    <div><div>组成本套餐的模块</div><small class="ty-color-blue">注：以下选项中不含构成了本模板主套餐的模块</small></div>
                    <div class="btn-group">
                        <span class="link-blue" onclick="chooseModuleBtn('menu')">去选择模块</span>
                    </div>
                </div>
                <table class="kj-table kj-table-striped tbl_menuModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="sureEditSetMenu()">确定</span>
        </div>
    </div>
    <%--套餐清单查看--%>
    <div class="bonceContainer bounce-blue" id="seeUsableSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐清单查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="ty-alert">
                    本模板的用户不可选择的模块数为A，设系统内已有套餐数为B，
                    需根据A、B，算出系统内不包含A的已有套餐数C，并需列出C的清单，
                    机构可选择的套餐，即为上述C的清单，具体如下：
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>套餐名称</td>
                        <td>创建</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">关闭</span>
        </div>
    </div>
    <%--重命名管理--%>
    <div class="bonceContainer bounce-blue" id="renameManage" style="width: 800px">
        <div class="bonceHead">
            <span>重命名管理</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>已重命名模块的数量 <b class="renameNumber"></b>个</p>
            <table class="kj-table tbl_renameManage" style="width: 70%">
                <thead>
                <tr>
                    <td>原名称</td>
                    <td>新名称</td>
                    <td>操作</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <hr>
            <table class="kj-table moduleTable">
                <thead>
                <tr>
                    <td class="thisLevel" level="1">一级菜单</td>
                    <td class="nextLevel">
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td class="thisLevel" level="2">二级菜单</td>
                                <td class="nextLevel">
                                    <table class="kj-table">
                                        <thead>
                                        <tr>
                                            <td class="thisLevel" level="2">三级菜单</td>
                                        </tr>
                                        </thead>
                                    </table>
                                </td>
                            </tr>
                            </thead>
                        </table>
                    </td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green update" onclick="renameOkBtn()">确定</span>
        </div>
    </div>
    <%--新增模板的提示--%>
    <div class="bonceContainer bounce-blue" id="addModelTip">
        <div class="bonceHead">
            <span>模块重命名选项</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width:80%; margin:20px auto; cursor: default; " >
                <div class="ty-radio">
                    <input type="radio" name="isNeedRename" id="noNeedRename" value="1">
                    <label for="noNeedRename"></label> 没有需要重命名的模块
                </div>
                <div class="ty-radio">
                    <input type="radio" name="isNeedRename" id="needRename" value="2">
                    <label for="needRename"></label> 有需要重命名的模块
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="addModelTipOkBtn()">确定</span>
        </div>
    </div>
    <%--创建修改模板的提示--%>
    <div class="bonceContainer bounce-red" id="editModelTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon text-center">

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-red" onclick="editModelOk()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">

    <%-- 菜单重命名记录 --%>
    <div class="bonceContainer bounce-blue" id="reNameLog" style="width: 600px">
        <div class="bonceHead">
            <span>菜单重命名记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>重命名过的菜单如下：</p>
            <table class="ty-table ty-table-control " id="logList">
                <tr>
                    <td>菜单初始名称</td>
                    <td>菜单当前名称</td>
                    <td>最后操作人</td>
                    <td>操作记录</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 删除模板 --%>
    <div class="bonceContainer bounce-red" id="delModel" >
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="line-height: 50px; text-align: center;">
            确定删除该模板吗？

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="delModelOkBtn()">确定</span>
        </div>
    </div>


<%-- 编辑模板 --%>
    <div class="bonceContainer bounce-green" id="editModel" style="width: 800px">
        <div class="bonceHead">
            <span>创建模板</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="ty-alert">
                    <span>模板名称/主套餐名称 <span class="ty-color-red">*</span></span>
                    <input type="text" class="kj-input" name="name" style="margin-left: 16px">
                    <div class="btn-group">
                        <span class="link-blue" onclick="aboutModelBtn()">关于模板</span>
                    </div>
                </div>
                <div class="part" part="step1">
                    <div class="ty-alert">
                        <div><div>主套餐内的模块</div><small class="ty-color-blue">注：权限等必要的模块一般需选入所创建模板的主套餐，虽然系统对此并无功能上的限制</small></div>
                        <div class="btn-group">
                            <span class="link-blue" onclick="chooseModuleBtn('main')">去选择模块</span>
                        </div>
                    </div>
                    <table class="kj-table kj-table-striped tbl_mainModule">
                        <thead>
                        <tr>
                            <td>模块名称</td>
                            <td>下辖的一级菜单数量</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="part" part="step2">
                    <div class="ty-alert">主套餐内的模块</div>
                    <table class="kj-table kj-table-striped tbl_mainModuleSee">
                        <thead>
                        <tr>
                            <td>模块名称</td>
                            <td>下辖的一级菜单数量</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="ty-alert" style="margin-top: 16px">
                        使用本模板的用户增值功能的模块
                        <div class="btn-group">
                            <span class="link-blue" onclick="chooseModuleBtn('valueAdded')">去选择模块</span>
                        </div>
                    </div>
                    <table class="kj-table kj-table-striped tbl_valueAddedModule">
                        <thead>
                        <tr>
                            <td>模块名称</td>
                            <td>下辖的一级菜单数量</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="ty-alert" style="margin-top: 16px">
                        与本模板增值功能模块对应的已有套餐
                        <div class="btn-group">
                            <span class="link-blue" onclick="seeUsableSetMenuBtn()">查看</span>
                            <span class="link-blue" onclick="editSetMenuBtn()">创建新套餐</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="part" part="step1">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
                <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="createNextBtn()">下一步</span>
            </div>
            <div class="part" part="step2">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">全部放弃</span>
                <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="createComplete()">创建完成</span>
            </div>
        </div>
    </div>
    <%-- 模板查看 --%>
    <div class="bonceContainer bounce-green" id="seeModel" style="width: 800px">
        <div class="bonceHead">
            <span>模板查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <h4 class="modelName"></h4>
                <div class="ty-alert">已重命名模块的数量 <b class="renameNumber"></b> 个</div>
                <table class="kj-table tbl_see_rename" style="width: 70%">
                    <thead>
                    <tr>
                        <td>原名称</td>
                        <td>新名称</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    主套餐内的模块
                </div>
                <table class="kj-table kj-table-striped tbl_see_mainModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    使用本模板的用户增值功能的模块
                </div>
                <table class="kj-table kj-table-striped tbl_see_valueAddedModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    与本模板增值功能模块对应的已有套餐
                    <div class="btn-group">
                        <span class="link-blue" onclick="seeUsableSetMenuBtn('seeModel')">查看</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">关闭</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>模板管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content"  >
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="page" page="main">
                    <div class="ty-alert">
                        本页列表中的各模板用于在特殊机构/管理平台上新增机构时选用。
                        <div class="btn-group text-right">
                            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="createModel()">创建模板</span>
                        </div>
                    </div>
                    <table class="kj-table tbl_main">
                        <thead>
                        <tr>
                            <td>模板名称</td>
                            <td>主套餐名称</td>
                            <td>创建</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye1"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/wonderssProduct/sysCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/wonderssProduct/sysModel.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

</body>
</html>
