<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/wonderssProduct/sysCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/wonderssProduct/sysModule.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">

<div class="bounce">
    <%-- 删除模块 --%>
    <div class="bonceContainer bounce-red" id="delModule" >
        <div class="bonceHead">
            <span>删除模块</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="line-height: 50px; text-align: center;">
            确定删除该模块吗？

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="delModelOkBtn()">确定</span>
        </div>
    </div>


    <%-- 编辑模块 --%>
    <div class="bonceContainer bounce-green" id="editModule" style="width: 800px">
        <div class="bonceHead">
            <span>创建模块</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="item-flex">
                    <div class="item-title">模块名称 <span class="ty-color-red">*</span></div>
                    <div class="item-content">
                        <input type="text" class="kj-input" name="name" style="width: 100%">
                    </div>
                </div>
                <div class="ty-alert">
                    <div>
                        <div>本模块由哪些菜单组成？请选择！</div>
                        <div class="ty-color-blue">
                            <small>
                                注1 模块用以构成模板，创建后还将作为一条“项目”的数据进入“服务项目”，并可定价 <br>
                                注2 本页面上的“模块名称”为必做项，此外点击“确定”时还需勾选不少于一个菜单 <br>
                                注3 创建模块时，系统需对所勾选的菜单组合给予“查重”；此外，系统不限制某菜单是否曾参与其他模块的构成，但操作时需注意的是，绝大多数菜单都不宜被多个模块引用 <br>
                                注4 所创建模块的功能需尽量有“独立性”、“难以再分割”，且宜尽量维持稳定，少“修改”
                            </small>
                        </div>
                    </div>
                </div>
                <table class="kj-table moduleTable">
                    <thead>
                    <tr>
                        <td class="thisLevel" level="1">
                            <div class="ty-checkbox">
                                <input type="checkbox" name="all" id="module_all">
                                <label for="module_all"></label> 一级菜单
                            </div>
                        </td>
                        <td class="nextLevel">
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td class="thisLevel" level="2">二级菜单</td>
                                    <td class="nextLevel">
                                        <table class="kj-table">
                                            <thead>
                                            <tr>
                                                <td class="thisLevel" level="2">三级菜单</td>
                                            </tr>
                                            </thead>
                                        </table>
                                    </td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="editModelOkBtn()">确定</span>
        </div>
    </div>



    <%-- 查看模块 --%>
    <div class="bonceContainer bounce-blue" id="seeModule" style="width: 800px">
        <div class="bonceHead">
            <span>模块查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <h4><b class="moduleName"></b></h4>
                <table class="kj-table moduleTable">
                    <thead>
                    <tr>
                        <td class="thisLevel">一级菜单</td>
                        <td class="nextLevel">
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td class="thisLevel">二级菜单</td>
                                    <td class="nextLevel">
                                        <table class="kj-table">
                                            <thead>
                                            <tr>
                                                <td class="thisLevel">三级菜单</td>
                                            </tr>
                                            </thead>
                                        </table>
                                    </td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>




</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>模块管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content"  >
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="page" page="main">
                    <div class="ty-alert">
                        本页列表中的各模块用于在特殊机构/管理平台上创建模块时选用。
                        <div class="btn-group text-right">
                            <span class="ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="addNewModuleBtn()">创建模块</span>
                        </div>
                    </div>
                    <table class="kj-table tbl_main">
                        <thead>
                        <tr>
                            <td>模块名称</td>
                            <td>创建</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/wonderssProduct/sysCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/wonderssProduct/sysModule.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

</body>
</html>
