<%--
  Created by IntelliJ IDEA.
  User: 张旭博
  Date: 2017/10/20
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/production/storage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/logistic/signature.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
<div class="bounce_Fixed">
    <%--签收录入-确认无误弹窗--%>
    <div class="bonceContainer bounce-blue" id="signSure" style="min-width:630px;">
        <div class="bonceHead">
            <span>签收确认无误</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon linkUploadify">
            <div class="inputSize">
                <div class="form-group">
                    <label for="arriveTime">实际到达日期</label>
                    <input type="text" id="arriveTime" class="arriveTimeFact form-control" placeholder="请选择"/>
                </div>
                <div class="form-group">
                    <label for="signTime">客户签收日期</label>
                    <input type="text" class="signTime form-control" id="signTime" placeholder="请选择"/>
                </div>
                <div class="form-group">
                    <label for="signerName">签收人</label>
                    <input type="text" class="signerName form-control" id="signerName" placeholder="请录入"/>
                </div>
                <div class="form-group">
                    <label for="record">情况记录</label>
                    <input type="text" class="signRecordInfo form-control" id="record">
                </div>
            </div>
            <%--
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td width="30%">实际到达日期</td>
                    <td width="70%"><input type="text" class="signTime" id="arriveTime"></td>
                </tr>
                <tr>
                    <td>客户签收日期</td>
                    <td><input type="text" class="signTime" id="signTime"></td>
                </tr>
                <tr>
                    <td>签收人</td>
                    <td><input type="text" class="signerName"></td>
                </tr>
                <tr>
                    <td>情况记录</td>
                    <td colspan="2"><textarea cols="30" rows="1" class="signRecordInfo"></textarea></td>
                </tr>
                </tbody>
            </table>--%>
            <div>
                <p>附件（可上传签收单据照片，最多可上传3张）<span class="linkBtn ty-right" id="signRecordUplaod">上传</span></p>
                <div class="signRecordImg">

                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="signSureBtn" onclick="signSure()" disabled>确定</button>
        </div>
    </div>
    <%--签收录入-修改弹窗--%>
    <div class="bonceContainer bounce-blue" id="signDetailChange" style="min-width:630px;">
        <div class="bonceHead">
            <span>修改签收数量</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td style="width: 40%">请录入收货方实际的收货数量：</td>
                    <td><input type="text" class="actualDeliveryNum" style="width: 100%" onkeyUp = 'clearNoNum(this)'></td>
                </tr>
                <tr>
                    <td>情况记录</td>
                    <td><textarea cols="30" rows="5" class="memo" style="width: 100%"></textarea></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="signDetailChangeBtn" onclick="signChange()" disabled>确定</button>
        </div>
    </div>
    <%--签收查看-详情查看--%>
    <div class="bonceContainer bounce-blue" id="signDetailCheck" style="min-width:630px;">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td style="width: 40%">收货方实际的收货数量：</td>
                    <td><input type="text" class="actualDeliveryNum" style="width: 100%" disabled></td>
                </tr>
                <tr>
                    <td>情况记录</td>
                    <td><textarea cols="30" rows="5" class="memo" style="width: 100%" disabled></textarea></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel();">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 警告提示框 --%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--签收记录--%>
    <div class="bonceContainer bounce-blue" id="signRecord" style="min-width:1400px;">
        <div class="bonceHead">
            <span>签收记录</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="signRecordBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td>订单号</td>
                    <td class="sn">1</td>
                    <td>原始录入时间</td>
                    <td colspan="2" class="create_date"></td>
                    <td>订单修改时间</td>
                    <td class="update_date"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="4" class="deliveryDate"></td>
                    <td>搬运负责人</td>
                    <td class="carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="4" class="arriveDate"></td>
                    <td>计划的发货方式</td>
                    <td class="deliveryWay"></td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>要求到货日期</td>
                    <td>货物件数</td>
                    <td>出库-仓库处理</td>
                    <td>出库-物流复核</td>
                    <td>计量单位</td>
                    <td>应签收数量</td>
                    <td>实际签收数量</td>
                </tr>
                <tbody></tbody>
            </table>
            <%--<div class="applyAll"><span class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right" id="signSureBaseBtn" data-state="1" onclick="signSureBtn()">数量无误，收货方已全部签收</span></div>--%>
            <div class="applyAll">
                <span class="agreenTip" data-checked="0" onclick="checkSignFinish($(this))"><i class="fa fa-circle-o"></i>经确认，以上商品的<span class="ty-color-red">实际签收数量</span>均无误！</span>
                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right" id="signSureBaseBtn" data-state="1" onclick="signSureBtn()">确定</span></div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--签收查看--%>
    <div class="bonceContainer bounce-blue" id="signCheck" style="min-width:1400px;">
        <div class="bonceHead">
            <span>签收查看</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="signCheckBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td width="18%">订单号</td>
                    <td width="18%" class="sn">1</td>
                    <td width="10%">原始录入时间</td>
                    <td width="18%" colspan="2" class="create_date"></td>
                    <td width="10%">订单修改时间</td>
                    <td width="18%" class="update_date"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="2" class="deliveryDate"></td>
                    <td colspan="2">搬运负责人</td>
                    <td colspan="2" class="carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="2" class="arriveDate"></td>
                    <td colspan="2">计划的发货方式</td>
                    <td colspan="2" class="deliveryWay"></td>
                </tr>
                </tbody>
            </table>
            <p class="countAll" style="margin-top: 8px"></p>
            <div id="signInfoRecord">
                <div>
                    <div class="recordCon clear">
                        <div class="col-md-6 col-lg-6">
                            <div class="clear">
                                <span class="ty-left">情况记录</span>
                                <div class="imgScanWall">
                                    <span>照片1</span>
                                    <span>照片2</span>
                                    <span>照片1</span>
                                </div>
                            </div>
                            <div class="signDetail"></div>
                        </div>
                        <div class="recordHeader ty-right">
                            <b>实际到达日期：</b><span class="signerFact">XXXX-XX-XX</span><b>客户签收：</b><span class="signer"></span><span class="signTime"></span><b>录入者：</b><span class="recorder"></span><span class="recordTime"></span>
                        </div>
                    </div>
                </div>
            </div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>要求到货日期</td>
                    <td>货物件数</td>
                    <td>出库-仓库处理</td>
                    <td>出库-物流复核</td>
                    <td>计量单位</td>
                    <td>应签收数量</td>
                    <td>实际签收数量</td>
                </tr>
                <tbody></tbody>
            </table>
            <p class="applyAll"></p>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--签收管理-已签收-出库单--%>
    <div class="bonceContainer bounce-green" id="outStorageOrder" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库单</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageOrderBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="4" class="deliveryDate"></td>
                    <td>订单号</td>
                    <td class="sn"></td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>出库数量</td>
                    <td>货物件数</td>
                    <td>仓库审批时间</td>
                    <td>复核时间</td>
                </tr>
                <tbody></tbody>
            </table>
            <p class="applyAll"></p>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--出库申请-已出库-发运信息--%>
    <div class="bonceContainer bounce-blue" id="logisticInfo" style="min-width:1200px">
        <div class="bonceHead">
            <span>发运信息</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="transInfo" style="display: none;text-align: center">未获取到发运信息</div>
            <div class="transDetail">
                <table class="ty-table ty-table-none">
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>签收管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div id="picShow" style="display: none;">
                <img src=""/>
            </div>
            <div class="ty-container">
                <ul class="ty-secondTab">
                    <li class="ty-active">待签收</li>
                    <li>已签收</li>
                </ul>
                <div class="ty-mainData">
                    <%--待签收--%>
                    <div class="tblContainer">
                        <table class="ty-table ty-table-control panel_4" id="unsigned">
                            <thead>
                            <tr>
                                <td>出库日期</td>
                                <td>客户名称</td>
                                <td>客户代号</td>
                                <td>订单号</td>
                                <td>商品类别总数</td>
                                <td>货物总件数</td>
                                <td>计划到达日期</td>
                                <td>申请提交时间</td>
                                <td>仓库审批时间</td>
                                <td>复核时间</td>
                                <td>提交者</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--已签收--%>
                    <div class="tblContainer" style="display: none;">
                        <table class="ty-table ty-table-control" id="signed">
                            <thead>
                            <tr>
                                <td>出库日期</td>
                                <td>客户名称</td>
                                <td>客户代号</td>
                                <td>订单号</td>
                                <td>商品类别总数</td>
                                <td>货物总件数</td>
                                <td>实际到达日期</td>
                                <td>签收人</td>
                                <td>客户签收日期</td>
                                <td>数据录入时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div id="ye_signature"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/logistic/signature.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
