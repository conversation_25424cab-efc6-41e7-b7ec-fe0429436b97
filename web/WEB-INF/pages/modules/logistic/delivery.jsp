<%--
  Created by IntelliJ IDEA.
  User: 张旭博
  Date: 2017/10/20
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/production/storage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
<%--出库申请--新增商品提示框--%>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="F_errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="outStorageApply" style="min-width:1200px;">
        <div class="bonceHead">
            <span>出库申请</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageApplyBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td>订单号</td>
                    <td class="sn">1</td>
                    <td>原始录入时间</td>
                    <td colspan="2" class="create_date"></td>
                    <td>订单修改时间</td>
                    <td class="update_date"></td>
                </tr>
                <tr>
                    <td>计划出库日期 <span class="ty-color-red">*</span></td>
                    <td colspan="3"><input type="text" id="deliveryDate"></td>
                    <td>搬运负责人</td>
                    <td colspan="2"><input type="text" id="carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="3"><input type="text" id="arriveDate"></td>
                    <td>计划的发货方式</td>
                    <td colspan="2">
                        <select id="deliveryWay">
                            <option value="">请选择发货方式</option>
                            <option value="快递">快递</option>
                            <option value="随身携带">随身携带</option>
                            <option value="货运">货运</option>
                        </select>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList" id="apply-sure">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>外部名称</td>
                    <td>产品图号</td>
                    <td>内部名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>要求到货日期</td>
                    <td>要货数量</td>
                    <td>可选数量</td>
                    <td>已出库总数</td>
                    <td>计划出库数量 <span class="ty-color-red">*</span></td>
                    <td>货物件数</td>
                    <td>操作</td>
                </tr>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce_Fixed.cancel() ">继续选择商品</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="addGoodsBtn" onclick="outStorageApply(0)">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="outStorageCheckOk">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord">您确定复核无误吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureOutStorageCheck($(this),1)">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="outStorageCheckNo">
        <div class="bonceHead">
            <span>更改数量</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            计划出库数量 <input type="text" class="amount" onkeyup="clearNoNum(this)">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureOutStorageCheck($(this),2)">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
        </div>
    </div>

</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--已作废的发货通知--%>
    <div class="bonceContainer bounce-green" id="invalidStorageApply" style="min-width:1400px;">
        <div class="bonceHead">
            <span>已作废的发货通知</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 450px;overflow-y: auto">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>外部名称</td>
                    <td>产品图号</td>
                    <td>内部名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>要求到货日期</td>
                    <td>要货数量</td>
                    <td>已出库总数</td>
                    <td>订单号</td>
                    <td>原始录入时间</td>
                    <td>客户名称</td>
                    <td>收货地点</td>
                    <td>订单修改时间</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--出库申请-待处理-选择商品--%>
    <div class="bonceContainer bounce-blue" id="outStorageApply_chooseGood" style="min-width:1500px;">
        <div class="bonceHead">
            <span>出库申请-选择商品</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 450px;overflow-y: auto">
            <table class="ty-table ty-table-control" id="apply_chooseGood">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>外部名称</td>
                    <td>产品图号</td>
                    <td>内部名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>要求到货日期</td>
                    <td>要货数量</td>
                    <td>可选数量</td>
                    <td>已出库总数</td>
                    <td>订单号</td>
                    <td>原始录入时间</td>
                    <td>客户名称</td>
                    <td>收货地点</td>
                    <td>订单修改时间</td>
                    <td></td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">

        </div>
    </div>

    <%--出库申请-待处理-筛选--%>
    <div class="bonceContainer bounce-blue" id="outStorageApply_query" style="min-width:1400px;height: 550px;overflow: auto">
        <div class="bonceHead">
            <span>出库申请-筛选</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control tblList" id="apply_query">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>外部名称</td>
                    <td>产品图号</td>
                    <td>内部名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>要求到货日期</td>
                    <td>要货数量</td>
                    <td>可选数量</td>
                    <td>已出库总数</td>
                    <td>订单号</td>
                    <td>原始录入时间</td>
                    <td>客户名称</td>
                    <td>收货地点</td>
                    <td>订单修改时间</td>
                    <td></td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="outStorageApplyBtn()">确定</span>
        </div>
    </div>

    <%--出库申请-待出库-修改--%>
    <div class="bonceContainer bounce-blue" id="outStorageChange" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库申请-修改</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageChangeBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td>订单号</td>
                    <td class="sn">1</td>
                    <td>原始录入时间</td>
                    <td colspan="2" class="create_date"></td>
                    <td>订单修改时间</td>
                    <td class="update_date"></td>
                </tr>
                <tr>
                    <td>计划出库日期 <span class="ty-color-red">*</span></td>
                    <td colspan="3"><input type="text" id="change_deliveryDate"></td>
                    <td>搬运负责人</td>
                    <td colspan="2"><input type="text" id="change_carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="3"><input type="text" id="change_arriveDate"></td>
                    <td>计划的发货方式</td>
                    <td colspan="2">
                        <select id="change_deliveryWay">
                            <option value="">请选择发货方式</option>
                            <option value="快递">快递</option>
                            <option value="随身携带">随身携带</option>
                            <option value="货运">货运</option>
                        </select>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>外部名称</td>
                    <td>产品图号</td>
                    <td>内部名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>要求到货日期</td>
                    <td>要货数量</td>
                    <td>可选数量</td>
                    <td>已出库总数</td>
                    <td>计划出库数量 <span class="ty-color-red">*</span></td>
                    <td>货物件数</td>
                </tr>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="changeGoodsBtn" onclick="outStorageApply(1)">确定</button>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
        </div>
    </div>

    <%--出库申请-待出库-查看(出库申请单)--%>
    <div class="bonceContainer bounce-blue" id="outStorageOrder" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库申请单</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageOrderBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="4" class="deliveryDate"></td>
                    <td>订单号</td>
                    <td class="sn"></td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>外部名称</td>
                    <td>产品图号</td>
                    <td>内部名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>计划出库数量</td>
                    <td>货物件数</td>
                </tr>
                <tbody></tbody>
            </table>
            <div class="applyAll">申请人：王建 2017-20-23</div>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--出库申请-待复核-复核--%>
    <div class="bonceContainer bounce-blue" id="outStorageCheck" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库申请-复核</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageCheckBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td>订单号</td>
                    <td class="sn">1</td>
                    <td>原始录入时间</td>
                    <td colspan="2" class="create_date"></td>
                    <td>订单修改时间</td>
                    <td class="update_date"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="3" class="deliveryDate"></td>
                    <td>搬运负责人</td>
                    <td colspan="2" class="carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="3" class="arriveDate"></td>
                    <td>计划的发货方式</td>
                    <td colspan="2" class="deliveryWay"></td>
                </tr>
                </tbody>
            </table>
            <p class="countAll" style="margin-top: 8px"></p>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>外部名称</td>
                    <td>产品图号</td>
                    <td>内部名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>要求到货日期</td>
                    <td>要货数量</td>
                    <td>可选数量</td>
                    <td>已出库总数</td>
                    <td>计划出库数量</td>
                    <td>货物件数</td>
                    <td>仓库审批结果</td>
                    <td>仓库审批时间</td>
                    <td>操作</td>
                </tr>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--出库申请-已出库-查看--%>
    <div class="bonceContainer bounce-blue" id="outStorageOk" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库申请-已出库-查看</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageOkBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td>订单号</td>
                    <td class="sn"></td>
                    <td>原始录入时间</td>
                    <td colspan="2" class="create_date"></td>
                    <td>订单修改时间</td>
                    <td class="update_date"></td>
                </tr>
                <tr>
                    <td>出库日期</td>
                    <td colspan="3" class="approveTime"></td>
                    <td>搬运负责人</td>
                    <td colspan="2" class="carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="3" class="arriveDate"></td>
                    <td>计划的发货方式</td>
                    <td colspan="2" class="deliveryWay"></td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px">本次计划出库共？种商品，共？件</div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>外部名称</td>
                    <td>产品图号</td>
                    <td>内部名称</td>
                    <td>单位</td>
                    <td>要求到货日期</td>
                    <td>出库数量</td>
                    <td>货物件数</td>
                    <td>仓库审批时间</td>
                    <td>复核时间</td>
                </tr>
                <tbody></tbody>
            </table>
            <div class="applyAll"></div>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--出库申请-已出库-发运信息--%>
    <div class="bonceContainer bounce-blue" id="logisticInfo" style="min-width:1200px">
        <div class="bonceHead">
            <span>发运信息</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="chooseWay">
                <span>请选择货物的发运方式:</span>
                <span class="ty-btn ty-btn-green ty-circle-3 transport">快递</span>
                <span class="ty-btn ty-btn-green ty-circle-3 transport">随身携带</span>
                <span class="ty-btn ty-btn-green ty-circle-3 transport">货运</span>
            </div>
            <div class="transInfo" style="display: none">
                <table class="ty-table ty-table-none">
                    <tbody>
                    <tr>
                        <td>发运方式：</td>
                        <td>快递</td>
                        <td>快递单号：</td>
                        <td><input type="text"></td>
                        <td>快递公司：</td>
                        <td><input type="text"></td>
                        <td>交寄日期：</td>
                        <td><input type="text"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="transDetail" style="display: none">
                <table class="ty-table ty-table-none">
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="handle">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="sureLogistic()">确定</span>
            </div>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>发货管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div style="margin: 10px 0">
                    <span class="ty-btn ty-btn-big ty-btn-green ty-circle-5" onclick="outStorageApply_chooseGoodBtn()">出库申请</span>
                    <span class="ty-btn ty-btn-big ty-btn-red ty-circle-5" onclick="invalidStorageApplyBtn()">已作废的发货通知</span>
                </div>

                <ul class="ty-secondTab">
                    <li class="ty-active">待处理</li>
                    <li>待出库</li>
                    <li>待复核</li>
                    <li>已出库</li>
                </ul>
                <div class="ty-mainData">
                    <%--待处理--%>
                    <div class="tblContainer">
                        <table class="ty-table ty-table-control" id="waitHandle">
                            <thead>
                            <tr>
                                <td>商品代号</td>
                                <td>外部名称</td>
                                <td>产品图号</td>
                                <td>内部名称</td>
                                <td>单位</td>
                                <td>当前库存</td>
                                <td>要求到货日期</td>
                                <td>要货数量</td>
                                <td>可选数量</td>
                                <td>已出库总数</td>
                                <td>订单号</td>
                                <td>原始录入时间</td>
                                <td>客户名称</td>
                                <td>收货地点</td>
                                <td>订单修改时间</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--待出库--%>
                    <div class="tblContainer" style="display: none;">
                        <table class="ty-table ty-table-control" id="waitOutStorage">
                            <thead>
                            <tr>
                                <td>计划出库日期</td>
                                <td>客户名称</td>
                                <td>收货地址</td>
                                <td>计划的发货方式</td>
                                <td>订单号</td>
                                <td>商品类别总数</td>
                                <td>货物总件数</td>
                                <td>搬运负责人</td>
                                <td>计划到达日期</td>
                                <td>申请提交时间</td>
                                <td>申请最后修改时间</td>
                                <td>提交者</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--待复核--%>
                    <div class="tblContainer" style="display: none;">
                        <table class="ty-table ty-table-control panel_3" id="waitCheck">
                            <thead>
                            <tr>
                                <td>计划出库日期</td>
                                <td>客户名称</td>
                                <td>收货地址</td>
                                <td>计划的发货方式</td>
                                <td>订单号</td>
                                <td>商品类别总数</td>
                                <td>货物总件数</td>
                                <td>搬运负责人</td>
                                <td>计划到达日期</td>
                                <td>申请提交时间</td>
                                <td>申请最后修改时间</td>
                                <td>提交者</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--已出库--%>
                    <div class="tblContainer" style="display: none;">
                        <table class="ty-table ty-table-control panel_4" id="alreadyOutStorage">
                            <thead>
                            <tr>
                                <td>出库日期</td>
                                <td>客户名称</td>
                                <td>客户代号</td>
                                <td>订单号</td>
                                <td>商品类别总数</td>
                                <td>货物总件数</td>
                                <td>计划到达日期</td>
                                <td>申请提交时间</td>
                                <td>仓库审批时间</td>
                                <td>复核时间</td>
                                <td>提交者</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div id="ye_outStorage"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/logistic/delivery.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
