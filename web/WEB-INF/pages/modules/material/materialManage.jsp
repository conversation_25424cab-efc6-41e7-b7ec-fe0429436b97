<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/material/wuliao.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%-- 新增分类 --%>
    <div class="bonceContainer bounce-blue" id="addAccount">
        <div class="bonceHead">
            <span id="addkindTtl">新增分类</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu">
                    <div class="hd">
                        <span id="addkind_pid"></span>
                        <span id="addkind_type"></span>
                    </div>
                     请输入分类名称 : <input type="text" style="width:300px;" id="addkind_name" />
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addkind()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 编辑分类名称 --%>
    <div class="bonceContainer bounce-blue" id="updateAccount">
        <div class="bonceHead">
            <span>编辑分类名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu">
                    <p>请输入修改后的分类名称</p>
                    <input type="text"  id="kindEditName" >
                    <input type="text"  class="hd" id="kindEditID" style="display: none; ">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="saveKindEdit()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 删除分类 --%>
    <div class="bonceContainer bounce-red" id="deleteAccount">
        <div class="bonceHead">
            <span>删除分类</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p style="vertical-align:top; ">确定 要删除名称为<span id="delKindname"></span>吗？</p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="okDeleteKind()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5"  onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 温馨提示 --%>
    <div class="bonceContainer bounce-orange" id="mtTip">
    <div class="bonceHead bounce-blue">
        <span>温馨提示：</span>
        <a class="bounce_close" onclick="bounce.cancel()"></a>
    </div>
    <div class="bonceCon">
        <div class="addpayDetails">
            <div class="shu1">
                <p id="mt_tip_ms"> </p>
            </div>
        </div>
    </div>
    <div class="bonceFoot">
        <span class="ty-btn ty-btn-orange ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
    </div>
</div>
    <div class="bonceContainer bounce-red" id="mtConfirm">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_confirm_ms"> </p>
                    <p id="mt_confirm_type" class="hd"></p>
                    <p id="mt_confirm_id" class="hd"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="okConfirm()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 新增采购信息 --%>
    <div class="bonceContainer bounce-green" id="addPurchase">
        <div class="bonceHead bounce-blue">
            <span>新增采购信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-left threeCon">
                <p> <input type="hidden" id="editType">
                    <span>材料名称</span><input type="text" disabled id="e_pMtName"/>
                    <span>材料代号</span><input type="text" disabled id="e_pMtCode"/>
                    <span>计量单位</span><input type="text" disabled id="e_pMtUnit"/>
                </p>
                <p class="">
                    <span>供应商名称</span><select id="supplier" onchange="matchSupper($(this))"></select>
                    <span class="purchase-link" onclick="addBtn_supply()">新增供应商</span>
                </p>
                <p>
                    <span> 供应商简称</span>
                    <span class="e_supContainer">
                        <input type="text" id="e_gName1" disabled/>
                        <span class="supCon" id="supCon1"></span>
                    </span><input type="hidden" id="selectSupInfo" />
                    <span>供应商代号</span><input type="text" id="e_gCode1" disabled />
                </p>
                <p class="supInfo supInfoTip leMar" id="sup1" > </p>
                <p class="supInfo leMar containThis">
                    采购合同中包含本材料吗？ <input type="hidden" id="containThis">
                    <span onclick="containThis(1 , $(this))" class="radioCon">是 <i id="containThis1" class="fa fa-circle-o"></i></span>
                    <span onclick="containThis(0 , $(this))" class="radioCon">否 <i id="containThis0" class="fa fa-circle-o"></i></span>
                </p>
                <p class="supInfo leMar">
                    该供应商供应的本材料价格是否稳定？ <input type="hidden" id="isStable">
                    <span onclick="isStable(1 , $(this))" class="radioCon">相对稳定 <i id="isStable1" class="fa fa-circle-o"></i></span>
                    <span onclick="isStable(2 , $(this))" class="radioCon">变动较频繁 <i id="isStable2" class="fa fa-circle-o"></i></span>
                </p>
                <p class="leMar stable canInvoice">
                    购买本材料是否能开发票？ <input type="hidden" id="canInvoice">
                    <span onclick="canInvoice(1 , $(this))" class="radioCon">是 <i id="canInvoice1" class="fa fa-circle-o"></i></span>
                    <span onclick="canInvoice(0 , $(this))" class="radioCon">否 <i id="canInvoice0" class="fa fa-circle-o"></i></span>
                    <span class="canInvoice2 radioCon" onclick="canInvoice(2 , $(this))">不确定 <i id="canInvoice2" class="fa fa-circle-o"></i></span>
                </p>
                <p class="leMar stable incoiceType">
                    购买本材料给开何种发票？ <input type="hidden" id="incoiceType">
                    <span onclick="incoiceType(1 , $(this))" class="radioCon" id="incoiceType1Con">增值税专用发票 <i id="incoiceType1" class="fa fa-circle-o"></i></span>
                    <span onclick="incoiceType(2 , $(this))" class="radioCon">其他发票 <i id="incoiceType2" class="fa fa-circle-o"></i></span>
                    <span class="incoiceType4" onclick="incoiceType(4 , $(this))" class="radioCon">不给开票 <i id="incoiceType4" class="fa fa-circle-o"></i></span>
                </p>
                <p class="leMar stable priceInfo" ><input type="hidden" id="isParValue">
                    <span class="type1">已约定的不开票单价</span>
                    <span class="type2" style="width: auto">
                        <span class="type3">参考单价</span><input type="hidden" id="referPrice">
                        <span onclick="referPrice(1 , $(this))" class="radioCon">含税 <i id="referPrice1" class="fa fa-circle-o"></i></span>
                        <span onclick="referPrice(0 , $(this))" class="radioCon">不含税 <i id="referPrice0" class="fa fa-circle-o"></i></span>
                    </span>
                    <input type="text" id="price"/>元/<span style="width: auto;" class="purUnit"></span>
                </p>
                <p class="leMar stable priceInfo">
                    <span>该价格是否含运费？</span> <input type="hidden" id="containYunFee">
                    <span onclick="containYunFee(1 , $(this))" class="radioCon"><i id="containYunFee1" class="fa fa-circle-o"></i> 为送货上门价格，含所有运费</span>
                </p>
                <p class="leMar stable priceInfo">
                    <span></span>
                    <span onclick="containYunFee(2 , $(this))" class="radioCon"><i id="containYunFee2" class="fa fa-circle-o"></i> 含长途运输费用，到本市后需我司自提</span>
                </p>
                <p class="leMar stable priceInfo">
                    <span></span>
                    <span onclick="containYunFee(3 , $(this))" class="radioCon"><i id="containYunFee3" class="fa fa-circle-o"></i> 为离厂价格，不包含任何运费</span>
                </p>
                <p class="leMar supInfo ">
                    所供应材料的包装方式 <input type="hidden" id="packgeType">
                    <span onclick="packgeType(1 , $(this))" class="radioCon">基本固定 <i id="packgeType1" class="fa fa-circle-o"></i></span>
                    <span onclick="packgeType(2 , $(this))" class="radioCon">型式不定 <i id="packgeType2" class="fa fa-circle-o"></i></span>
                </p>
                <p class="supInfo ">
                    <span>采购周期</span><input type="text" id="purTurn"/>
                    <span>最低采购量</span><input type="text" id="minPur"/><span class="purUnit"></span>
                    <span>最低库存</span><input type="text" id="minStorage"/><span class="purUnit"></span>
                    <br><span class="tipBlue">注：下单后需多少天可入库</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="purchaseOK()">确定</span>
        </div>
    </div>

    <%-- 新增、编辑供应商 --%>
    <div class="bonceContainer bounce-green" id="addSupply"  onclick="$('#supCon').hide()">
        <div class="bonceHead">
            <span id="supConTtl">新增供应商</span>
            <a class="bounce_close" onclick="bounce.show($('#addPurchase'))"></a>
        </div>
        <div class="bonceCon"  >
            <input type="hidden" id="sup_type" />
            <input type="hidden" id="e_gId"  /> <%-- 供应商ID--%>
            <input type="hidden" id="e_gSbid"  /> <%-- 供应关系id --%>
            <div class="ty-left threeCon">
                <p> <%--第一行--%>
                    <span>材料名称</span><input type="text" disabled id="e_gMtName"/>
                    <span>材料代号</span><input type="text" disabled id="e_gMtCode"/>
                    <span>计量单位</span><input type="text" disabled id="e_gMtUnit"/>
                </p>
                <p>
                    <span> 供应商名称<i class="xing">*</i></span>
                    <span class="e_supContainer">
                        <input type="text" id="e_gName" style="width: 550px;" onchange="setGname2($(this))"  />
                    </span>

                </p>
                <p>
                    <span> 供应商简称<i class="xing">*</i></span>
                    <span class="e_supContainer">
                        <input type="text" id="e_gName2"/>
                    </span>
                    <span>供应商代号<i class="xing">*</i></span><input type="text" id="e_gCode"  />
                </p>
                <%--第3行--%>
                <p class="leMar">
                    是否已与其签订采购合同？ <input type="hidden" id="haveContract">
                    <span onclick="haveContract(1 , $(this))" class="radioCon">是 <i id="haveContract1" class="fa fa-circle-o"></i></span>
                    <span onclick="haveContract(0 , $(this))" class="radioCon">否 <i id="haveContract0" class="fa fa-circle-o"></i></span>
                </p>
                <%--第4行 ， 全部非必填项--%>
                <p class="hang4">
                    <span>合同编号</span><input type="text" id="e_gCompactNo" />
                    <span>有效期至</span><input type="text" id="e_gCompactExpire" />
                    <span>签署日期</span><input type="text" id="e_gCompactSignDay" />
                </p>
                <%--第5行--%>
                <p class="leMar noNext">
                    该供应商是否能开发票？ <input type="hidden" id="haveInvoice">
                    <span onclick="haveInvoice(1 , $(this))" class="radioCon">是 <i id="haveInvoice1" class="fa fa-circle-o"></i></span>
                    <span onclick="haveInvoice(0 , $(this))" class="radioCon">否 <i id="haveInvoice0" class="fa fa-circle-o"></i></span>
                </p>
                <%--第6行--%>
                <p class="leMar noNext hang6">
                    是否能开增值税专用发票？ <input type="hidden" id="vatInvoice">
                    <span onclick="vatInvoice(1, $(this))" class="radioCon">是 <i id="vatInvoice1" class="fa fa-circle-o"></i></span>
                    <span onclick="vatInvoice(2, $(this))" class="radioCon">否 <i id="vatInvoice2" class="fa fa-circle-o"></i></span>
                    <span class="hang_6" style="width: auto;">
                        <span class="litT">请录入税率</span> <input type="text" id="e_gRate0" onkeyup="clearNoNum(this)">%
                    </span>
                </p>
                <%--第7行--%>
                <p class="leMar">
                    是否接受挂账？ <input type="hidden" id="setHangAccount">
                    <span onclick="setHangAccount(1 , $(this))" class="radioCon">可接受 <i id="setHangAccount1" class="fa fa-circle-o"></i></span>
                    <span onclick="setHangAccount(0 , $(this))" class="radioCon">不接受 <i id="setHangAccount0" class="fa fa-circle-o"></i></span>
                    <span class="hang_7" style="width: auto;" >
                        <span>请录入已约定的账期</span> <input type="text" id="hangDays" onkeyup="clearNoNum(this)" />天

                    </span>
                </p>
                <%--第7-1行--%>
                <p class="leMar hang7_1">
                    请选择从何时开始计算账期？  <input type="hidden" id="setStartDate">
                    <span onclick="setStartDate(1 , $(this))" class="radioCon" id="setStartDate1Con">自货入库之日起 <i id="setStartDate1" class="fa fa-circle-o"></i></span>
                    <span onclick="setStartDate(2 , $(this))" class="radioCon" id="setStartDate2Con">自发票入账之日起 <i id="setStartDate2" class="fa fa-circle-o"></i></span>
                </p>
                <%--第8行--%>
                <p class="leMar hang8">
                    是否可接受汇票？ <input type="hidden" id="hui">
                    <span onclick="hui(1 , $(this))" class="radioCon">可接受 <i id="hui1" class="fa fa-circle-o"></i></span>
                    <span onclick="hui(2 , $(this))" class="radioCon">不确定 <i id="hui2" class="fa fa-circle-o"></i></span>
                </p>
                <%--第16行--%>
                <p class="leMar hang16">
                    是否需要预付款？ <input type="hidden" id="setAdvanceFee">
                    <span onclick="setAdvanceFee(1, $(this))" class="radioCon">需要 <i id="setAdvanceFee1" class="fa fa-circle-o"></i></span>
                    <span onclick="setAdvanceFee(2, $(this))" class="radioCon">不需要 <i id="setAdvanceFee2" class="fa fa-circle-o"></i></span>
                    <span id="relative0_2" onclick="setAdvanceFee(0, $(this))" class="radioCon">不确定 <i id="setAdvanceFee0" class="fa fa-circle-o"></i></span>
                </p>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-left" style="color:red;" id="supTip"></span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="saveNewSupply()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.show($('#addPurchase'))">取消</span>
        </div>
    </div>
    <%-- 盘点提示 --%>
    <div class="bonceContainer bounce-blue" id="inventory">
        <div class="bonceHead bounce-blue">
            <span>提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p>请在手机端进行盘点</p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 原始库存 、 初始库存 记录表格 --%>
    <div class="bonceContainer bounce-blue" id="storageHis">
        <div class="bonceHead bounce-blue">
            <span id="StorageHistory"> 初始库存修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="hisHeadc">
                <table  class="ty-table hisHead " >
                    <thead id="headStorage">
                    <td width="20%">修改人</td>
                    <td width="20%">修改时间</td>
                    <td width="20%">供应商名称</td>
                    <td width="20%">修改前的初始库存</td>
                    <td width="20%">修改后的初始库存</td>
                    </thead>
                </table>
            </div>
            <div class="hisCon">
                <table class="ty-table" >
                    <tbody id="oralStorageHis">  </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 查看供应商 --%>
    <div class="bonceContainer bounce-blue" id="gScanInfo">
        <div class="bonceHead bounce-blue">
            <span>查看供应商</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class=" threeCon">
                <p>
                    <span>材料名称</span><input type="text" disabled id="s_gMtName"/>
                    <span>材料代号</span><input type="text" disabled id="s_gMtCode"/>
                    <span>计量单位</span><input type="text" disabled id="s_gMtUnit"/>
                </p>
                <p>
                    <span> 供应商名称 </span>
                    <span class="e_supContainer">
                        <input type="text" id="s_gName" style="width: 550px;" disabled />
                    </span>
                </p>
                <p>
                    <span> 供应商简称 </span>
                    <span class="e_supContainer">
                        <input type="text" id="s_gName2" disabled/>
                    </span>
                    <span>供应商代号 </span><input type="text" id="s_gCode" disabled />
                </p>
                <p class="supInfoTip leMar" id="sup2">
                    采购合同已签订，合同编号X，有效期至X，签署日期为X <br>
                    该供应商不能开发票，可接受挂账，账期XX天，自货物入库之日起计算，不确定能接受承兑汇票。
                </p>
                <p>
                    <span id="priceTtl"> </span>
                    <span class="widthAuto" id="priceCon">
                    </span>
                </p>
                <p class="supInfoTip leMar" id="mtTip_s">
                    本材料与采购合同的关系元素 <br>
                    包装方式元素，最低采购量元素，采购周期元素。 <br>
                    价格稳定元素，开票元素，预付款元素。
                </p>
                <p>
                    <span id="memoTtl">其他信息</span>
                    <span class="widthAuto" id="minStorageInfo">
                    </span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>物料信息</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="pull-right" id="addBtn">
                    <a class="panel_4 ty-btn ty-btn-big ty-btn-green ty-circle-5"  onclick="addMtBtn()" >新增物料</a>
                </div>
                <div class="clr"></div>
                <br>
                <div class="ty-mainData">
                    <%--material massage--%>
                    <div id="mtInfo" class="Con">
                        <div class="bigContainer">
                            <div class="left_container">
                                <div class="Btop"><span>物料类型</span></div>
                                <div >
                                    <form>
                                        <ul class="faceul Left-label" id="kindsTree"></ul>

                                        <div class="left-bottom">
                                            <div class="add-b" onclick="gobackLstLevel()"> <a> <span class="back-btn">返回上一级</span> </a>  </div>
                                            <div class="add-b" onclick="addBtnAct(1)" > <a> <span >新增分类</span>  </a> </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="between"><div class="between2"></div></div>
                            <div class="right_container">
                                <div class="Right-label" id="right_container">
                                    <div class="container_nav">
                                        <div class="conon">
                                            <div class="dq">
                                                <span>当前分类</span>  <span>：</span>  <span id="curID">  </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="opinionCon">
                                        <table class="ty-table ty-table-control" id="normalMaterial">
                                            <thead>
                                            <td width="13%">物料名称</td>
                                            <td width="7%">代号</td>
                                            <td width="10%">型号</td>
                                            <td width="5%">规格</td>
                                            <td width="10%">物料类型</td>
                                            <td width="5%">来源</td>
                                            <td width="5%">计量单位</td>
                                            <td width="10%">当前库存数量</td>
                                            <td width="10%">最低库存</td>
                                            <td width="5%">库位</td>
                                            <td width="15%">操作</td>
                                            </thead>
                                            <tbody id="materialList">  </tbody>
                                        </table>
                                        <table class="ty-table ty-table-control" id="goodslMaterial">
                                            <thead>
                                            <td width="13%">内部名称</td>
                                            <td width="7%">内部图号</td>
                                            <td width="10%">型号</td>
                                            <td width="5%">规格</td>
                                            <td width="5%">物料类型</td>
                                            <td width="5%">来源</td>
                                            <td width="5%">计量单位</td>
                                            <%--<td width="5%">单位</td>--%>
                                            <td width="10%">当前库存数量</td>
                                            <td width="10%">最低库存</td>
                                            <td width="5%">库位</td>
                                            <td width="15%">操作</td>
                                            </thead>
                                            <tbody id="goodsmaterialList">

                                            </tbody>
                                        </table>
                                        <div id="ye"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="clr"></div>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                    <%--add material --%>
                    <div id="mtAdd" class="Con-bt1">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="goback()">返回</span>
                        <br><br>
                        <div class="TCh">
                            <div class="TCh1">
                                <%-- 物料的基本信息 --%>
                                <div id="editNormalMaterialMess">
                                    <div class="TCh1-container">基本信息</div>
                                    <div class="TCh1-content">
                                        <input type="text" id="mt_id" class="hd" >
                                        <input type="text" id="mt_type" class="hd" >
                                        <input type="text" id="mt_isControl" class="hd" >
                                        <div class="ty-left doubleCon">
                                            <p><span>物料名称<i class="xing">*</i></span><input type="text" id="mt_name" /></p>
                                            <p><span>计量单位</span><input type="text" id="mt_unit"/></p>
                                            <p><span>规格</span><input type="text" id="mt_specifications" /></p>
                                            <p>
                                                <span>最低库存</span><input type="text"  readonly   id="mt_minStorage" />
                                            </p>
                                            <p><span> </span> <a class="link" onclick="showStorageHis(1)">最低库存修改记录</a></p>
                                            <p><span>备注</span><input type="text" id="mt_memo" /></p>
                                        </div>
                                        <div class="ty-left doubleCon">
                                            <p><span>代号<i class="xing">*</i></span><input type="text" id="mt_code" /></p>
                                            <p><span>净重</span><input type="text" id="mt_net_weight"  onkeyup="clearNoNum(this)"/></p>
                                            <p><span>型号</span><input type="text" id="mt_model" /></p>
                                            <p>
                                                <span>初始库存</span><input type="text"  readonly id="mt_oralStorage"  />
                                            </p>
                                            <p><span> </span> <a class="link" onclick="showStorageHis(2)">初始库存修改记录</a></p>
                                        </div>
                                        <div class="clr"></div>
                                    </div>
                                </div>
                                <%-- 商品的基本信息 --%>
                                <div id="editGoodsMaterialMess" class="hd">
                                    <div class="TCh1-container">基本信息</div>
                                    <div class="TCh1-content">
                                        <input type="text" id="goods_id" class="hd" >
                                        <input type="text" id="goods_type" class="hd" >
                                        <input type="text" id="goods_isControl" class="hd" >
                                        <div class="ty-left doubleCon">
                                            <p><span>内部名称</span><input type="text" id="goods_name" readonly /></p>
                                            <p><span>计量单位</span><input type="text" id="goods_unit" readonly /></p>
                                            <p><span>规格</span><input type="text" id="goods_specifications" readonly /></p>
                                            <p><span>最低库存</span><input type="text" id="goods_minStorage" readonly /></p>
                                            <p class="notCaiGou"><span> </span> <a class="link" onclick="showStorageHis(1)">最低库存修改记录</a></p>
                                            <p><span>备注</span><input type="text" id="goods_memo" readonly /></p>
                                        </div>
                                        <div class="ty-left doubleCon">
                                            <p><span>内部图号</span><input type="text" id="goods_code" readonly /></p>
                                            <p><span>净重</span><input type="text" id="goods_net_weight" readonly /></p>
                                            <p><span>型号</span><input type="text" id="goods_model" readonly /></p>
                                            <p><span>初始库存</span><input type="text" id="goods_oralStorage" readonly /></p>
                                            <p><span> </span> <a class="link" onclick="showStorageHis(2)">初始库存修改记录</a></p>
                                        </div>
                                        <div class="clr"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="TCh1-container">分类信息</div>
                                    <div class="TCh1-content_sele  ">
                                        <div class="mt_kind">
                                            <span class="mt_ttl">物料类型</span>
                                            <div class="biaoInfo1">
                                                <div>
                                                    <span>已选分类：</span>
                                                <span class="mt_hasSelect">
                                                </span>
                                                </div>
                                                <div id="mtKindContainer">  </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="notCaiGou">
                                    <div class="TCh1-container">采购信息</div>
                                    <div class="opinionCon T">
                                        <div class="selectContainer ">
                                            <span class="purchase-link" onclick="addBtn_purchase()"> 新增采购信息 </span>
                                            <span class="addsupTip"></span>
                                        </div>
                                        <table class="ty-table ty-table-control">
                                            <thead>
                                                <td width="13%">供应商名称</td>
                                                <td width="12%">采购合同</td>
                                                <td width="7%">发票种类</td>
                                                <td width="10%">已约定单价</td>
                                                <td width="7%">参考单价</td>
                                                <td width="7%">是否开票</td>
                                                <td width="7%">采购周期</td>
                                                <td width="10%">最低库存</td>
                                                <td width="20%">操作</td>
                                            </thead>
                                            <tbody id="suppierList"> </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div>
                                    <div class="TCh1-container">库房信息</div>
                                    <div class="TCh1-content">
                                        <div class="ty-left doubleCon">
                                            <p><span>库位</span><input type="text" id="mt_stock_position" ></p>
                                            <p><span>储存要求</span><input type="text" id="mt_stock_requirements" ></p>
                                        </div>
                                        <div class="clr"></div>
                                    </div>
                                </div>
                                <div id="qualityCon">
                                    <div class="TCh1-container">质量信息</div>
                                    <div class="TCh1-content">
                                        <div class="ty-left doubleCon">
                                            <p><span>试验方法</span><input type="text" id="mt_experimental_method" ></p>
                                            <p><span>检验标准</span><input type="text" id="mt_inspection_standard" ></p>
                                            <p><span>指导书</span><input type="text" id="mt_inspection_instructions" ></p>
                                            <p><span>外委实验记录</span><input type="text" id="mt_outer_record" ></p>
                                            <p><span>备注</span><input type="text" id="mt_qulity_memo" ></p>
                                        </div>
                                        <div class="ty-left doubleCon">
                                            <p><span>保质期</span><input type="text" id="mt_expiration_date" ></p>
                                            <p><span>检验作业</span><input type="text" id="mt_inspection_operation" ></p>
                                            <p><span>质量协议编号</span><input type="text" id="mt_protocol_sn" ></p>
                                            <p><span>封样编号</span><input type="text" id="mt_sealed_samples_sn" ></p>
                                        </div>
                                        <div class="clr"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="TCh">
                            <div class="TCh2">
                                <div class="controlBtn">
                                    <a class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 " onclick="saveMtEdit()" >保存</a>
                                    <a class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="cancelMtEdit()" >取消</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/material/material.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
