<%--
  Created by IntelliJ IDEA.
  User: Sara
  Date: 2016/9/20
  Time: 14:20
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>

<%@ include  file="../../common/headerTop.jsp"%>
    <link href="../css/material/wuliao.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <div class="bonceContainer bounce-blue" id="addContact">
        <div class="bonceHead">
            <span id="contactTtl"></span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <table class="addpayDetails ty-table">
                <tr>
                    <td>联系人</td>
                    <td><input type="text" id="editcontact_name"  ></td>
                    <input type="text" id="editcontact_type" style="display: none; ">
                    <input type="text" id="editcontact_id"  style="display: none; ">
                </tr>
                <tr>
                    <td>电话</td>
                    <td><input type="text" id="editcontact_mobile"></td>
                </tr>
                <tr>
                    <td>部门</td>
                    <td><input type="text" id="editcontact_depart"></td>
                </tr>
                <tr>
                    <td>职位</td>
                    <td><input type="text" id="editcontact_zhi"></td>
                </tr>
                <tr>
                    <td>邮箱</td>
                    <td><input type="text" id="editcontact_email"></td>
                </tr>
                <tr>
                    <td>传真</td>
                    <td><input type="text" id="editcontact_fax"></td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td><input type="text" id="editcontact_memo"></td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <p style="text-align: center ; color:red;   " id="msTip"></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="saveContact()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_cancel()">取消</span>
        </div>
    </div>

    <div class="bonceContainer bounce-red" id="deleteContact">
        <div class="bonceHead">
            <span>删除账户</span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p style="vertical-align:top; ">确定 要删除该分类名称吗？</p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_cancel()">取消</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_cancel()">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-red" id="mtConfirm">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_confirm_ms"> </p>
                    <p id="mt_confirm_type" class="hd"></p>
                    <p id="mt_confirm_id" class="hd"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="okConfirm()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_cancel()">取消</span>
        </div>
    </div>

</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>供应商名录</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainData">
                    <div id="supplierList">
                        <div class="opinionCon T1">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="9%">名称</td>
                                <td width="9%">联系人</td>
                                <td width="9%">手机</td>
                                <td width="9%">电话</td>
                                <td width="12%">邮箱</td>
                                <td width="10%">传真</td>
                                <td width="12%">地址</td>
                                <td width="12%">付款方式</td>
                                <td width="18%">操作</td>

                                </thead>
                                <tbody id="supplyList">
                                <%--<tr>
                                    <td>利达一厂</td>
                                    <td>王鸥</td>
                                    <td>13512521311</td>
                                    <td>86512132</td>
                                    <td><EMAIL></td>
                                    <td>58156132</td>
                                    <td>合肥路315号</td>
                                    <td>现金</td>
                                    <td> <span onclick='delBtnSup($(this))'>[ 查看 ]</span>
                                          <span onclick='delBtnSup($(this))'>[ 删除 ]</span></td>
                                </tr>--%>

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <%--supplyInfo --%>
                    <div id="scanInfo" class="hd">
                        <ul class="ty-secondTab">
                            <li class="ty-active">供应商详细信息</li>
                        </ul>
                        <div class="opinionCon" style="margin-top: 15px">
                            <table class="ty-table">
                                <tbody>
                                <tr>
                                    <td width="12%"><span class="jiacu" >名称</span></td>
                                    <td width="38%" class="tz"><span id="supCompant_span"  class="editSup_0" >利达一厂</span>
                                        <input  class="editSup_1"  disabled="true" type="text" id="supCompant_input">
                                        <span class="editSup_1 cantEdit" >( 不可修改 )</span></td>
                                    <input class="hd" readonly type="text" id="supId_input">
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td class="jiacu"><span>联系人</span></td>
                                    <td class="tz"><span id="supContact_span" class="editSup_0"></span>
                                        <input type="text"  id="supContact_input"  class="editSup_1"  /> </td>
                                    <td class="jiacu"><span>手机</span></td>
                                    <td class="tz"><span id="supMobile_span" class="editSup_0"> </span>
                                        <input type="text" id="supMobile_input"  class="editSup_1" > </td>
                                </tr>
                                <tr>
                                    <td class="jiacu"><span>电话</span></td>
                                    <td class="tz">
                                        <span id="supTel_span" class="editSup_0"> </span>
                                        <input type="text"  id="supTel_input"  class="editSup_1" > </td>
                                    <td class="jiacu"><span>邮箱</span></td>
                                    <td class="tz">
                                        <span id="supMail_span"  class="editSup_0"> </span>
                                        <input type="text"  id="supMail_input"  class="editSup_1" >  </td>
                                </tr>
                                <tr>
                                    <td class="jiacu"><span>传真</span></td>
                                    <td class="tz">
                                        <span id="supFax_span"  class="editSup_0"> </span>
                                        <input type="text"  id="supFax_input" class="editSup_1"> </td>
                                    <td class="jiacu"><span>地址</span></td>
                                    <td class="tz">
                                        <span id="supAddr_span" class="editSup_0"></span>
                                        <input type="text"  id="supAddr_input" class="editSup_1">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="jiacu"><span>付款方式</span></td>
                                    <td class="tz">
                                        <span id="supPayMethod_span" class="editSup_0"> </span>
                                        <select id="supPayMethod_input" class="editSup_1">
                                            <option value="1">现金</option>
                                            <option value="2">转账</option>
                                            <option value="3">汇款</option>
                                        </select>  </td>
                                    <td class="jiacu"><span>开户行</span></td>
                                    <td class="tz">
                                        <span id="supBank_span" class="editSup_0"> </span>
                                        <input type="text"  id="supBank_input" class="editSup_1"> </td>
                                </tr>
                                <tr>
                                    <td class="jiacu"><span>账号</span></td>
                                    <td class="tz">
                                        <span id="supBankNo_span" class="editSup_0"> </span>
                                        <input type="text"  id="supBankNo_input"class="editSup_1"> </td>
                                    <td class="jiacu"><span>付款类型</span></td>
                                    <td class="tz">
                                        <span id="supPayType_span" class="editSup_0"> </span>
                                        <select  id="supPayType_input" class="editSup_1">
                                            <option value="1">预付</option>
                                            <option value="2">货到付款</option>
                                        </select> </td>
                                </tr>

                                </tbody>
                            </table>
                            <table class="ty-table" >
                                <tr>
                                    <td>
                                        <span class="ty-btn ty-btn-blue ty-btn-big editSup_0 ty-right" onclick="editBtn_supInfo()">编辑</span>
                                        <span class="ty-btn ty-btn-blue ty-btn-big editSup_1 ty-right" onclick="saveBtn_supInfo()" >保存</span>
                                        <span class="ty-btn ty-btn-big editSup_1 ty-right" onclick="cancelBtn_supInfo()" >取消</span>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <ul class="ty-secondTab">
                            <li class="ty-active">联系人一览</li>
                            <span class="panel_4 ty-btn ty-btn-big ty-btn-green ty-circle-5 ty-right" id="addBtnAct" onclick="addBtnContact()" style="margin-top: 8px">新增联系人</span>
                        </ul>

                        <div class="opinionCon" style="margin-top: 15px">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="8%">联系人</td>
                                <td width="8%">部门</td>
                                <td width="8%">职位</td>
                                <td width="12%">手机</td>
                                <td width="12%">邮箱</td>
                                <td width="17%">传真</td>
                                <td width="17%">备注</td>
                                <td width="18%">操作</td>
                                </thead>
                                <tbody id="supContact">
                                <%-- <tr>
                                     <td>张已辣</td>
                                     <td>销售部</td>
                                     <td>经理</td>
                                     <td>121561511</td>
                                     <td>2352435</td>
                                     <td>352435</td>
                                     <td>0</td>
                                     <td class='bj'><span onclick='upBtnContact($(this))'>[ 编辑 ]</span><span onclick='delBtnContact($(this))'>[ 删除 ]</span></td>
                                 </tr>--%>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="clearfix"></div>



        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/material/supply.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
