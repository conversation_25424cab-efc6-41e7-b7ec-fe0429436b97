<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/trainManage/questionBank.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="alertTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
             <div class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="confirmTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
             <div class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="confirmTipOk">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="cancelTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip">确定放弃您刚编辑的内容吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="backSourceInfoSure">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="saveTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip">您根据该素材制作的单选题已保存至系统！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 单选/判断 题查看 --%>
    <div class="bonceContainer bounce-blue" id="questionScan"  >
        <div class="bonceHead">
            <span>单选题查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div>
            <div class="bonceCon">
                <form autocomplete="off" onkeydown="if(event.keyCode==13)return false;" style="margin:0 50px;">
                    <div class="pannel">
                        <div class="form-group">
                            <label>题目</label>
                            <span class="codeQ"></span>
                        </div>
                        <div class="contentQ" style="margin: -12px auto 10px;"></div>
                        <div class="form-group qt1Right">
                            <label>正确项</label>
                            <div class="curRight" ></div>
                        </div>
                        <hr>
                        <div>
                            <div class="qt1">
                                <div style="font-size: 0.8em;" class="onOption">
                                    干扰项不足5个时，可新增干扰项；已有的干扰项则可删除或停用。<br> 已停用的干扰项可查看，并可进一步管理。<br>
                                </div>
                                <div class="onOption" style="text-align: right;">
                                    <span class="linkBtn addOption" style="margin-right: 20px;" data-fun="addOption">新增干扰项</span>
                                    <span class="linkBtn" data-fun="stopOption">查看已停用的干扰项</span>
                                </div>
                                <table class="ty-table ty-table-control" id="qtTb1">
                                </table>
                            </div>
                            <div class="qt2">
                            </div>
                            <%-- 新增干扰项 --%>
                            <div class="option clearArea">
                                <label>干扰项</label>
                                <textarea style="width:100%;" class="form-control" placeholder="请录入干扰项内容，字数上限为80个。" onkeyup="setWordsNum($(this),80)"></textarea>
                                <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="bonceFoot">
                <span class="option">
                    <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5 " data-fun="backOn">关闭本页</span>
                    <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="saveOption">保 存</span>
                </span>
                 <span class="ty-btn bounce-ok ty-btn-big ty-circle-5 offOption" data-fun="backOn">关闭本页</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5 onOption" onclick="bounce_Fixed.cancel()">关闭本页</span>
            </div>
        </div>
    </div>
    <%-- 制作单选题/判断题 --%>
    <div class="bonceContainer bounce-blue" id="editQuestion"  >
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" data-fun="backSourceInfo"></a>
        </div>
        <div>
            <div class="qt1">
                <div class="bonceCon">
                    <form autocomplete="off" onkeydown="if(event.keyCode==13)return false;" style="margin:0 50px;">
                        <div class="pannel">
                            单选题备选答案中的正确项无需录入，而需在素材原文中选择。<br>
                            先点击之处为正确项的起点，后点击之处为终点。
                            <div class="editS form-control" onmousedown="setNormal($(this))" onmouseup="getHightLight($(this))">日照香炉生紫烟,遥看瀑布挂前川的意思。日照香炉生紫烟,遥看瀑布挂前川的意思。日照香炉生紫烟,遥看瀑布挂前川的意思</div>
                            <div class="form-group">
                                <label>正确项</label>
                                <div class="curRight form-control" style="height: auto; min-height:34px; "></div>
                            </div>
                            <hr>
                            <div style="font-size: 0.8em;">
                                单选题备选答案中的干扰项需手动录入。干扰项最多可录入五个。<br>
                                系统中单选题需要四个备选项，所以您录入干扰项不能少于三个。<br>
                                干扰项如多于三个，出题时，系统将在其中随机抽取三个。
                            </div>
                            <div style="margin-top:15px; " class="interfereOptionList">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="bonceFoot">
                    <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" data-fun="backSourceInfo" >取消</span>
                    <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="saveQuestion1" >保存</span>
                </div>
            </div>
            <div class="qt2">
                <div class="bonceCon">
                    <form class="isFirst" autocomplete="off" onkeydown="if(event.keyCode==13)return false;" style="margin:0 50px;">
                        <div class="pannel">
                            素材的原文将生成一道试题，其标准答案为“正确”。<br>
                            素材内容的任何修改，均将生成标准答案为“错误”的备选题。
                            <div class="form-group">
                                <label>标准答案为“正确”的试题</label>
                                <div class="curRight form-control" style="height: auto;"></div>
                            </div>
                            <div style="margin-top:15px; " class="interfereOptionList">
                            </div>
                        </div>
                    </form>
                    <form class="notFirst" autocomplete="off" onkeydown="if(event.keyCode==13)return false;" style="margin:0 50px;">
                        <div class="pannel ">
                            <p><span style="margin-right:20px; ">素材</span><span class="souceCode">SC*********-0001。</span></p>
                            <div class="souceContent"></div>
                            <hr>
                            <div class="clearArea">
                                <p><span>新增判断</span>
                                    <span class="tfType" data-val="1"><i class="fa fa-circle-o"></i>正确项</span>
                                    <span class="tfType" data-val="0"><i class="fa fa-circle-o"></i>错误项</span>
                                </p>
                                <textarea class="form-control" style="width:100%; " id="newTF" placeholder="" onkeyup="setWordsNum($(this),80)"></textarea>
                                <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="bonceFoot">
                    <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" data-fun="backSourceInfo">取消</span>
                    <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="saveQuestion2" >下一步</span>
                </div>
            </div>
        </div>

    </div>
   <%-- 增加与题库有关的文件或资料 --%>
    <div class="bonceContainer bounce-blue" id="addAttach" >
        <div class="bonceHead">
            <span>增加与题库有关的文件或资料</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div id="uploadFile" class="hd"></div>
        <div class="bonceCon">
            <div style="padding:20px;">
                <p data-val="1" style="color:#ccc;">
                    <i class="fa fa-circle-o"></i> <span>选择Wonderss系统内的文件或资料</span>
                </p>
                <p data-val="2" onclick="selectSouce($(this))">
                    <i class="fa fa-circle-o"></i> <span>选择本地文件</span><br>
                    <small class="ty-color-blue">注：本地文件系指您自己的电脑或其他设备中的文件</small>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="openFileSouce()">下一步</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-green" id="editQuestionBank" >
        <div class="bonceHead">
            <span>新增题库</span>
            <a class="bounce_close" onclick="closeQuestionBank();"></a>
        </div>
        <div class="bonceCon">
            <form autocomplete="off" onkeydown="if(event.keyCode==13)return false;">
                <div class="pannel clearInput">
                    <div>题库名称（请选取与题库主题有关的名字）<span class="lenTip ty-right">0/15</span></div>
                    <input type="text" id="bankName" class="form-control" placeholder="请录入" onkeyup="setWordsNum($(this),15)"/>
                    <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                </div>
               <div class="pannel">
                   <div>题库代号（此代号由系统生成，不可修改）</div>
                   <span class="bankCode bankCodeCss">*********</span>
               </div>
                <div class="addAttachCon">
                    <div>
                        将与题库有关的附件增加于此，管理更便捷！<br>
                        如有多个附件，请逐一增加。
                    </div>
                    <div>
                        <span class="linkBtn" data-fun="newAttachBtn">增加附件</span>
                    </div>
                </div>
                <div class="attachList" data-del="">
                    <div class="attachItem">
                        <div class="fa fa-file-excel-o"></div>
                        <div>
                            <p title="" class="fileName">asd尽快发布山卡拉地方开始打击报复扣税的</p>
                            <div class="fileSize">1616113MB</div>
                        </div>
                        <div>
                            <span class="redLink">删除</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="closeQuestionBank();">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="save">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="editSource" >
        <div class="bonceHead">
            <span>新增素材/素材查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div>
            <div class="step0">
                <div class="bonceCon">
                    <form autocomplete="off" onkeydown="if(event.keyCode==13)return false;">
                        <div class="pannel">
                            <p>素材的内容需与试题相关。系统支持使用素材生成某些类型的试题。</p>
                            <p>某素材下可有多道不同类型的试题，但某次考核只会选取某素材下的一道题。</p>
                        </div>
                        <div class="pannel clearArea" style="margin-top: 30px;">
                            <div>请将素材录入或复制到下面的空格里。<span class="lenTip ty-right">0/80</span></div>
                            <textarea class="form-control" id="sourceTxt" onkeyup="setWordsNum($(this),80)"></textarea>
                            <i class="fa fa-times clearAreaVal" onmousedown="clearPrevVal($(this))"style="right: 10px;"></i>
                        </div>
                    </form>
                </div>
                <div class="bonceFoot">
                    <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
                    <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="saveSource">保存</span>
                </div>
            </div>
            <div class="step1">
                <div class="bonceCon">
                    <form autocomplete="off" onkeydown="if(event.keyCode==13)return false;" style="margin:0 50px;">
                        <div class="pannel firstTime">
                            <p>素材已保存至系统，代号为 <span class="souceCode">SC*********-0001</span>。</p>
                        </div>
                        <div class="pannel moreTime ">
                            <p><span style="margin-right:20px; ">素材</span><span class="souceCode">SC*********-0001。</span></p>
                            <div class="souceContent"></div>
                        </div>
                        <div>
                            <div class="moreTime">
                                <hr>
                                <p>本素材下已有单选题 <span class="chosNum"></span> 道，判断题  <span class="chargNum"></span> 道，具体如下：</p>
                                <div><span class="linkBtn" style="float: right;margin-top:-20px; " data-fun="showStopQuetion">查看已停用的试题</span></div>
                                <table class="ty-table ty-table-control" id="questionList1">
                                    <tr>
                                        <td>题型</td>
                                        <td>创建</td>
                                        <td>操作</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="enabledType">
                                <hr>
                                <p class="firstTime">该素材下尚无试题。请选择试题的生成方式，并点击下一步。</p>
                                <p class="moreTime">如需增加新的试题，请选择试题的生成方式，并点击下一步</p>
                                <div>
                                    <p data-val="2" onclick="selectFromSouce($(this))">
                                        <i class="fa fa-circle-o"></i> <span>在素材基础上编辑试题</span><br>
                                    </p>
                                    <p id="questionTypeCon">
                                        <span data-val="1" onclick="selectQuestionType($(this))" class="questionType"><i class="fa fa-circle-o"></i> <span>制作单选题</span></span>
                                        <span data-val="2" onclick="selectQuestionType($(this))" class="questionType"><i class="fa fa-circle-o"></i> <span>制作判断题</span></span>
                                    </p>
                                    <p data-val="1" style="color:#ccc;">
                                        <i style="color:#ccc;" class="fa fa-circle-o"></i> <span>自行编辑试题，而不使用素材</span>
                                    </p>
                                </div>
                                <hr class="firstTime">
                                <p class="firstTime">您也可关闭本页，另择时间生成试题。</p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="bonceFoot">
                    <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭本页</span>
                    <span class="ty-btn bounce-ok ty-btn-big ty-circle-5 enabledType" data-fun="nextStep">下一步</span>
                </div>
            </div>
        </div>
    </div>
    <%-- 已停用的试题 --%>
    <div class="bonceContainer bounce-green" id="stopQuestion" >
        <div class="bonceHead">
            <span>已停用的试题</span>
            <a class="bounce_close btnCat" data-fun="goSourceInfo"></a>
        </div>
        <div>
            <div class="step1">
                <div class="bonceCon">
                    <form autocomplete="off" onkeydown="if(event.keyCode==13)return false;" style="margin:0 50px;">
                        <div class="pannel">
                            <p><span>素材</span><span style="margin-left:20px; " class="souceCode">SC*********-0001。</span></p>
                            <div class="souceContent" ></div>
                        </div>
                        <div>
                            <div>
                                <hr>
                                <table class="ty-table ty-table-control" id="questionList0">
                                    <tr>
                                        <td>题型</td>
                                        <td>创建</td>
                                        <td>操作</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="bonceFoot">
                    <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" data-fun="goSourceInfo">关闭本页</span>
                </div>
            </div>
        </div>
    </div>
    <%-- 题库基本信息 --%>
    <div class="bonceContainer bounce-blue" id="bankInfo" style="width: 560px;">
        <div class="bonceHead">
            <span>题库基本信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table style="width:90%;margin:0 auto;line-height: 30px; ">
                <tr>
                    <td>题库名称</td>
                    <td class="bankNm"></td>
                    <td><span class="linkBtn base" data-fun="bankEditLog">修改记录</span></td>
                </tr>
                <tr>
                    <td>题库代号</td>
                    <td class="bankCode"></td>
                    <td></td>
                </tr>
                <tr>
                    <td>创建人</td>
                    <td class="bankCreat"></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="3"></td>
                </tr>
                <tr>
                    <td colspan="3">
                        与题库有关的附件 <br>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="scanFileList">

                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5 hisBase" data-fun="backBankEditLogList">关闭</span>
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5 base" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5 scanAbel" data-fun="bankEdit">修改</span>
        </div>
    </div>
    <%-- 题库修改记录 --%>
    <div class="bonceContainer bounce-blue" id="bankEditLog" style="width: 820px;">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" data-fun="backBankScan"></a>
        </div>
        <div class="bonceCon">
            <div style="padding:20px;">
                <p class="curSta"></p>
                <table class="ty-table">
                    <tr>
                        <td>资料状态</td>
                        <td>操 作</td>
                        <td>创建人/修改人</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="backBankScan">关闭</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>试题库</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div class="ty-container">
                <input type="hidden" id="searchKey">
                <input type="hidden" id="showMainConNum">
                <%-- 主页面 --%>
                <div class="mianCon mainCon1">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-fun="add">新增题库</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="goStop" >已停用的题库</span>
                        <div class="ty-right searchSect">
                            <div class="ty-left keywordSearch"><span class="ty-left">查找</span>
                                <div class="inputBox ty-right">
                                    <input type="text" placeholder=" 请输入关键字或素材代号">
                                </div>
                            </div>
                            <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchBtn1">确定</button>
                        </div>
                    </div>
                    <div>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowBtn" data-type="code">题库代号<i class="fa fa-long-arrow-down"></i></td>
                                <td>题库名称 </td>
                                <td class="arrowBtn" data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowBtn" data-type="sourceNum">素材数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>在用试题数量</td>
                                <td>操作</td>
                            </tr>

                        </table>
                        <div id="page1"></div>
                    </div>
                </div>
               <%-- 搜索页 --%>
                <div class="mianCon mainCon2">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back1">返回</span>
                    </p>
                    <div>
                        <p>以下是符合您查询条件的数据</p>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowBtn" data-type="code">题库代号<i class="fa fa-long-arrow-down"></i></td>
                                <td>题库名称 </td>
                                <td class="arrowBtn" data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowBtn" data-type="sourceNum">素材数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>在用试题数量</td>
                                <td>操作</td>
                            </tr>
                        </table>
                        <div id="page2"></div>

                    </div>
                </div>
                <%-- 已停用题库页面 --%>
                <div class="mianCon mainCon3">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back1">返回</span>
                    </p>
                    <div>
                        以下是已停用的题库。
                        <div class="ty-right searchSect">
                            <div class="ty-left keywordSearch"><span class="ty-left">查找</span>
                                <div class="inputBox ty-right">
                                    <input type="text" placeholder=" 请输入关键字或素材代号">
                                </div>
                            </div>
                            <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchBtn2">确定</button>
                        </div>
                    </div>
                    <div>
                        <table class="ty-table ty-table-control" style="margin-top:20px;">
                            <tr>
                                <td class="arrowBtn" data-type="code">题库代号<i class="fa fa-long-arrow-down"></i></td>
                                <td>题库名称 </td>
                                <td class="arrowBtn" data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowBtn" data-type="sourceNum">素材数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>在用试题数量</td>
                                <td>操作</td>
                            </tr>
                        </table>
                        <div id="page3"></div>

                    </div>
                </div>
               <%-- 已停用题库搜索页 --%>
                <div class="mianCon mainCon4">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back3">返回</span>
                    </p>
                    <div>
                        <p>以下是符合您查询条件的数据</p>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowBtn" data-type="code">题库代号<i class="fa fa-long-arrow-down"></i></td>
                                <td>题库名称 </td>
                                <td class="arrowBtn" data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowBtn" data-type="sourceNum">素材数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>在用试题数量</td>
                                <td>操作</td>
                            </tr>
                        </table>
                        <div id="page4"></div>
                    </div>
                </div>
                <%-- 题库查看 --%>
                <div class="mianCon mainCon5">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 back5" data-fun="back5">返回</span>
                    </p>
                    <div>
                        <div>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="bankInfo">题库基本信息</span>
                            <span class="bankName">题库名字</span><br>
                            创建 <span class="bankCreate">哈哈哈 2020-20-20 20：20：20</span>
                        </div>
                        <p style="margin-top:20px;">
                            <span style="margin-top:-20px; " class="ty-btn ty-btn-gray ty-btn-big ty-circle-5 ty-right" >操作指南</span>
                            <span>素材的内容应与试题相关。同一素材下最多可有四道单选题与四道判断题。更多功能请查阅操作指南。</span>
                        </p>
                        <div class="line clear">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 showAble" data-fun="addSource">新增素材</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="stopSourceList" >已停用的素材</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right showAble" style="margin-right: 20px;" data-fun="allUsingSource" >全部在用素材</span>
                            <div class="ty-right searchSect">
                                <div class="ty-left keywordSearch"><span class="ty-left">查找素材</span>
                                    <div class="inputBox ty-right">
                                        <input type="text" placeholder=" 请输入材料的代号或名称">
                                    </div>
                                </div>
                                <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchSource">确定</button>
                            </div>
                        </div>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowSource" data-type="code">素材代号<i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource"  data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource" data-type="questionNum">在用试题数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>已停用的试题</td>
                                <td>操作</td>
                            </tr>

                        </table>
                        <div id="page5"></div>
                    </div>
                </div>
                <%-- 在用素材查找 --%>
                <div class="mianCon mainCon8">
                    <div class="line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 back6" data-fun="back6">返回</span>
                    </div>
                    <div>
                        <p>以下是符合您查找条件的数据。 </p>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowSource" data-type="code">素材代号<i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource"  data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource" data-type="questionNum">在用试题数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>已停用的试题</td>
                                <td>操作</td>
                            </tr>
                        </table>
                        <div id="page8"></div>
                    </div>
                </div>
                <%-- 已停用的素材。 --%>
                <div class="mianCon mainCon6">
                    <div class="line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 back6" data-fun="back6">返回</span>
                        <div class="ty-right searchSect">
                            <div class="ty-left keywordSearch"><span class="ty-left">查找</span>
                                <div class="inputBox ty-right">
                                    <input type="text" placeholder="请输入关键字或素材代号">
                                </div>
                            </div>
                            <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchSource2">确定</button>
                        </div>
                    </div>
                    <div>
                        <p>以下是已停用的素材。 </p>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowSource" data-type="code">素材代号<i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource"  data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource" data-type="questionNum">在用试题数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>已停用的试题</td>
                                <td>操作</td>
                            </tr>
                        </table>
                        <div id="page6"></div>
                    </div>
                </div>
                <%-- 已停用素材搜索页 --%>
                <div class="mianCon mainCon9">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="back4">返回</span>
                    </p>
                    <div>
                        <p>以下是符合您查询条件的数据</p>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowSource" data-type="code">素材代号<i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource"  data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource" data-type="questionNum">在用试题数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>已停用的试题</td>
                                <td>操作</td>
                            </tr>
                        </table>
                        <div id="page9"></div>

                    </div>
                </div>
                <%-- 全部在用素材 --%>
                <div class="mianCon mainCon7">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 back6" data-fun="back6">返回</span>
                    </p>
                    <div>
                        <p>以下是本题库的全部在用素材。 </p>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowSource" data-type="code">素材代号<i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource" data-type="create">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowSource">素材内容</td>
                            </tr>
                        </table>
                        <div id="page7"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/trainManage/questionBank.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
