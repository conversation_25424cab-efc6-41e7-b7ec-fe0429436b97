<%--
  Created by IntelliJ IDEA.
  User: hxz
  Date: 2021/1/11
  Time: 12:04
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/trainManage/assessManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="alertTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="confirmTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="confirmTipOk">确定</span>
        </div>
    </div>

</div>
<div class="bounce_Fixed">
    <%-- 选题题库 / 选题试题 --%>
    <div class="bonceContainer bounce-blue" id="selectQustionBank" >
        <div class="bonceHead">
            <span>单选题库</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <%--选题题库--%>
        <div class="step0">
            <div class="bonceCon">
                <form autocomplete="off">
                    <table class="ty-table ty-table-control" id="selectBankList">
                        <tr>
                            <td class="toggle">选择</td>
                            <td>题库代号</td>
                            <td>题库名称</td>
                        </tr>
                        <tr>
                            <td><i class="fa fa-square-o fa-check-square-o"></i></td>
                            <td>题库代号</td>
                            <td>题库名称</td>
                        </tr>
                    </table>
                </form>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="selectQustionBankOk">确定</span>
            </div>
        </div>
        <%--选题试题--%>
        <div class="step1">
            <div class="bonceCon">
                <form autocomplete="off">
                    <h3 class="name">题库名称</h3>
                    <span class="code">题库代号</span>
                    <p>创建 <span class="create">2020-20-20 14:90:90</span></p>
                    <p>本题库可提供单选题<span class="cNum"></span>道，判断题<span class="tNum"></span>道，合计则不超过<span class="sNum"></span>道。</p>
                    <hr>
                    <p>请输入需要本题库提供的试题数量。</p>
                    <p><span>单选题</span> <input type="text" id="cNum" data-type="1" onkeyup="controlNum(this)" class="wid100 form-control">道</p>
                    <p><span>判断题</span> <input type="text" id="tNum" data-type="2" onkeyup="controlNum(this)" class="wid100 form-control">道</p>
                </form>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="bankSelectQOk">确定</span>
            </div>
        </div>
    </div>
    <%-- 考核对象 / 相关题库 --%>
    <div class="bonceContainer bounce-blue" id="usesBanksScan" >
        <div class="bonceHead">
            <span>相关题库</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table usersCon" >
                <thead>
                <tr>
                    <td id="commScan" colspan="2" style="text-align:left;">考核对象 共<span class="listLen"></span>人</td>
                    <td colspan="2" style="text-align:left; display: none;">本次考核中，尚有如下<span class="listLen"></span>人需补考。</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>职工</td>
                    <td>部门与岗位</td>
                </tr>
                </tbody>
            </table>
            <table class="ty-table banksCon">
                <thead>
                <tr>
                    <td colspan="2" style="text-align:left;">相关题库 共<span class="listLen"></span>个</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>题库代号</td>
                    <td>题库名称</td>
                    <td>已选试题</td>
                </tr>
                <tr>
                    <td>题库代号</td>
                    <td>题库名称</td>
                    <td>已选试题</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
        </div>
    </div>
        <%--试卷查看--%>
    <div class="bonceContainer bounce-blue" id="testPaperScan" style="width: 800px;">
        <div class="bonceHead">
            <p class="topBtn">
                <span class="ty-btn ty-btn-blue ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
            </p>
        </div>
        <div class="bonceCon">
            <table width="100%" class="desc">
                <tr>
                    <td>考核目的</td>
                    <td class="target"></td>
                </tr>
                <tr>
                    <td>交卷截止时间</td>
                    <td class="endTime">202-21-21 12：12：12</td>
                    <td>参考人数</td>
                    <td><span class="usersLen">112</span>人</td>
                </tr>
                <tr>
                    <td>试题数量</td>
                    <td class="testInfo">共XX道，其中单选题XX道，判断题XX道</td>
                </tr>
            </table>
            <hr>
            <div>
                <p class="cInfo"></p>
                <div class="questionInfo type1">
                    <div class="questionItem">
                        <span>1、（本题XX分)玄武门之变，是唐高祖武德九年六月初四，由当时唐高祖李渊次子（&nbsp;&nbsp;&nbsp;）在唐王朝的首都长安城的玄武门附近发动的一次政变。</span>
                        <p><span>A</span>李自成</p>
                        <p><span>B</span>李隆基</p>
                        <p><span>C</span>李清照</p>
                        <p><span>D</span>李鸿章</p>
                    </div>
                </div>
                <p class="tfInfo"></p>
                <div class="questionInfo type2">
                    <div class="questionItem">
                        <span>1、（本题XX分)玄武门之变，是唐高祖武德九年六月初四，由当时唐高祖李渊次子（&nbsp;&nbsp;&nbsp;）在唐王朝的首都长安城的玄武门附近发动的一次政变。</span>
                        <p>
                            <span><i class="fa fa-circle-o"></i><i class="fa fa-check"></i></span>
                            <span><i class="fa fa-circle-o"></i><i class="fa fa-times"></i></span>
                        </p>
                    </div>
                    <div class="questionItem">
                        <span>1、（本题XX分)玄武门之变，是唐高祖武德九年六月初四，由当时唐高祖李渊次子（&nbsp;&nbsp;&nbsp;）在唐王朝的首都长安城的玄武门附近发动的一次政变。</span>
                        <p>
                            <span><i class="fa fa-circle-o"></i><i class="fa fa-check"></i></span>
                            <span><i class="fa fa-circle-o"></i><i class="fa fa-times"></i></span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="testEnd ty-center">— —以下无内容— —</div>
        </div>
    </div>
    <%-- 修改考核公示的设置 --%>
    <div class="bonceContainer bounce-blue" id="publicitySetting" >
        <div class="bonceHead">
            <span>修改考核公示的设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="settingMain">
                <div>职工可在手机端“考核成绩公示板”看到历次考核的排名。</div>
                <div>某次考核交卷截止时间到后，成绩即公示于考核成绩公示板。</div>
                <hr/>
                <div>考核成绩的排名规则：</div>
                <div>分数相同者，答题所用时间少的排前面；</div>
                <div>答题时间也相同的，交卷早的排前面；</div>
                <div>交卷时间也相同的，名次并列。</div>
                <hr/>
                <div>考核默认公示成绩及格者中的前三名，且无奖励。</div>
                <div>本次考核如需修改公示的设置，请给予修改。</div>
                <div>
                    <div class="pubSet">
                        <i class="fa fa-circle-o" data-type="1"></i>
                        <span>本次考核的名次不公示</span>
                    </div>
                    <div class="pubSet">
                        <i class="fa fa-circle-o" data-type="2"></i>
                        <span>本次考核取成绩前
                            <select class="topFew">
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                        名给予公示
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="publicitySettingSure()">确 定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 发起考核/各道试题的分数 --%>
    <div class="bonceContainer bounce-blue" id="editTest" style="width: 560px;">
        <div class="bonceHead">
            <span>发起考核</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form autocomplete="off">
                <div class="pannel">
                    <div>考核对象</div>
                    <select class="form-control" id="testUserList"></select>
                </div>
                <div class="pannel testers">
                    <span class="tester"></span>
                </div>
                <div class="pannel">
                    <div>考核目的</div>
                    <input type="text"  class="target form-control" placeholder="请录入" onkeyup="setWordsNum($(this),30)"/>
                </div>
                <div class="addAttachCon">
                    <div>
                        试题将在哪个或哪些题库中选取？<br>
                        请先单选题库，之后在题库内选题。
                    </div>
                    <div>
                        <span class="linkBtn" data-fun="selectQuestionBank">单选题库</span>
                    </div>
                </div>
                <table class="ty-table ty-table-control" id="bankList">
                    <tr>
                        <td>题库代号</td>
                        <td>题库名称</td>
                        <td>已选试题</td>
                        <td>操作</td>
                    </tr>
                    <tr>
                        <td>题库代号</td>
                        <td>题库名称</td>
                        <td>已选试题</td>
                        <td>
                            <span class="funbtn ty-color-blue" data-fun="bankSelectQ">选题</span>
                            <span class="funbtn ty-color-red" data-fun="bankDel">删除</span>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="selectQuesOk">题已选完，下一步</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="editTest2" >
        <div class="bonceHead">
            <span>各道试题的分数</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form autocomplete="off">
                <div class="pannel">
                    本套试卷现有单选题<span id="cAllnum"></span>道，
                    判断题<span id="tAllnum"></span>道。请确定各道试题的分数。<br>
                    页面下方的“当前试卷总分”为100分时，可进入“下一步”。
                </div>
                <div class="pannel">
                    有时，每道题分数都相同，“当前试卷总分”无法为100分。<br>
                    这时就需要调整下表中两种分值题目的数量与分值。<br>
                    所谓普通分值或特殊分值除此用途外，并无更特殊的用途。
                </div>
                <hr>
                <table id="fenList">
                    <tr class="c" data-type="1" data-q="1">
                        <td width="30%">普通分值的单选题</td>
                        <td width="25%"><input name="c_default" type="text" onkeyup="clearNum(this)" class="num form-control"/>道</td>
                        <td width="15%">每道题</td>
                        <td width="30%"><input type="text" onkeyup="clearNum(this)" class="s form-control"/>分</td>
                    </tr>
                    <tr class="c" data-type="2" data-q="1">
                        <td>特殊分值的单选题</td>
                        <td><input type="text" class="num form-control"onkeyup="clearNum(this)" />道</td>
                        <td>每道题</td>
                        <td><input type="text" class="s form-control"onkeyup="clearNum(this)" />分</td>
                    </tr>
                    <tr style="height:15px;"></tr>
                    <tr class="c" data-type="1" data-q="2">
                        <td>普通分值的判断题</td>
                        <td><input name="t_default" type="text" class="num form-control"/>道</td>
                        <td>每道题</td>
                        <td><input type="text" class="s form-control"/>分</td>
                    </tr>
                    <tr class="c" data-type="2" data-q="2">
                        <td>特殊分值的判断题</td>
                        <td><input type="text" class="num form-control"/>道</td>
                        <td>每道题</td>
                        <td><input type="text" class="s form-control"/>分</td>
                    </tr>
                    <tr style="border-bottom:1px solid #ccc; "></tr>
                    <tr style="border-top:1px solid #ccc ;">
                        <td></td>
                        <td></td>
                        <td>当前试卷总分</td>
                        <td><input type="text" readonly class="form-control d"/>分</td>
                    </tr>
                </table>
                <hr>

            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 editTestOk bounce-cancel" data-fun="editTestOk">下一步</span>
        </div>
    </div>
    <%--  考核的其他事项/ 考核查看 --%>
    <div class="bonceContainer bounce-blue" id="testScan" >
        <div class="bonceHead">
            <span>考核的其他事项</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form autocomplete="off">
                <p class="ty-color-red scan stop"></p>
                <table style="width:100% ">
                    <tr>
                        <td class="right">考核对象</td>
                        <td><span class="usersLen">20</span>人</td>
                        <td class="ty-td-control"><span class="ty-color-blue funbtn human" data-fun="testUserScan">查看</span><span class="hd"></span></td>
                        <td class="right">相关题库</td>
                        <td><span class="banksLen">20</span>个</td>
                        <td class="ty-td-control"><span class="ty-color-blue funbtn" data-fun="questionBankScan">查看</span></td>
                    </tr>
                    <tr>
                        <td class="right">考核目的</td>
                        <td colspan="5" class="target"></td>
                    </tr>
                    <tr class="edit scan">
                        <td class="right">考核公示</td>
                        <td colspan="4" class="publicity">成绩前三名将给予公示，但无奖励。</td>
                        <td class="ty-td-control"><span class="ty-color-blue funbtn" data-fun="editPubSetting">修改</span></td>
                    </tr>
                    <tr>
                        <td class="right">试题数量</td>
                        <td colspan="4" class="testInfo">共XX道，其中单选题XX道，判断题XX道</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funbtn scan scanTestPaper" data-fun="scanTestPaper">查看</span>
                            <span class="hd"></span>
                        </td>
                    </tr>
                    <tr class="makeup">
                        <td class="right">答卷时间</td>
                        <td colspan="5" class="answerDuration"></td>
                    </tr>
                    <tr class="makeup">
                        <td class="right">及格分数</td>
                        <td colspan="5" class="passingScore"></td>
                    </tr>
                </table>
                <hr>
                <div class="pannel">
                    <div>交卷截至时间（此时间前未能交卷者，以零分计）</div>
                    <input type="text" class="edit makeup" id="date1" placeholder="请选择日期"/>
                    <input type="text" class="edit makeup" id="dateTime" placeholder="请选择时间"/>
                    <span class="scan endTime"></span>
                </div>
                <div class="pannel edit scan clearValBody">
                    <div>答卷时间（开始答题后，答卷时间内未答的题以零分计）</div>
                    <input type="text" id="answerDuration" onkeyup="clearNoNum(this)" class="form-control edit" placeholder="请输入数字" />
                    <i class="fa fa-times clearInputVal"></i>
                    <span class="edit durUnit">分钟</span>
                    <span class="scan answerDuration"></span>
                </div>
                <div class="pannel edit scan clearValBody">
                    <div>及格分数（达不到此分数者需补考）</div>
                    <input type="text" class="form-control edit" onkeyup="clearNoNum(this)" id="passingScore" placeholder="请输入分数" />
                    <i class="fa fa-times clearInputVal"></i>
                    <span class="scan passingScore"></span>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5 edit" data-fun="cancelTest">取消本次考核</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 edit" data-fun="sureTest">完成，发出考核通知</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 scan" onclick="bounce.cancel()" >关闭</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 makeup" onclick="bounce.cancel()" >取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 makeup" data-fun="makeupTest">完成，发出考核通知</span>
        </div>
    </div>
    <%--  职工补考记录 --%>
    <div class="bonceContainer bounce-blue" id="staffTestScan" style="width: 700px;">
        <div class="bonceHead">
            <span>职工补考记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form autocomplete="off">
                <p class="ty-color-red scan stop"></p>
                <table width="100%" class="test-tab">
                    <tr>
                        <td width="20%" >职工</td>
                        <td width="30%" class="userName"></td>
                        <td width="15%">部门</td>
                        <td class="departName" colspan="3"></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>岗位</td>
                        <td class="postName" colspan="3"></td>
                    </tr>
                    <tr><td colspan="6"><hr></td></tr>
                    <tr>
                        <td>正式考核</td>
                        <td></td>
                        <td>及格分数</td>
                        <td class="passingScore" colspan="3"></td>
                    </tr>
                    <tr>
                        <td>交卷截止时间</td>
                        <td class="endTime"></td>
                        <td>考核得分</td>
                        <td class="score"></td>
                        <td width="80px">名次</td>
                        <td class="rank"></td>
                    </tr>
                    <tr>
                        <td>考核目的</td>
                        <td colspan="5" class="goal"></td>
                    </tr>
                    <tr><td colspan="6"><hr></td></tr>
                    <tr>
                        <td>补考</td>
                        <td class="ty-color-red" colspan="5">当前状态<span class="ty-color-red passingState" style="margin-left: 20px;"></span></td>

                    </tr>
                </table>
                <table class="ty-table" id="tbTestList">
                    <tbody>
                    <tr>
                        <td>交卷截止时间</td>
                        <td>实际交卷时间</td>
                        <td>答题用时</td>
                        <td>分数</td>
                    </tr>
                    </tbody>
                </table>
            </form>
        </div>
        <div class="bonceFoot">  </div>
    </div>
        <%--职工考核记录--%>
    <div class="bonceContainer bounce-blue" id="assessmentRecordScan" style="width: 800px;">
        <div class="bonceHead">
        </div>
        <div class="bonceCon">
            <div class="recordttl">
                <span class="ty-left ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
                <h3>职工考核记录</h3>
            </div>
            <div class="areaA clear">
                <div class="ty-left"> <span class="cells">职工</span><span id="record_staff"></span></div>
                <div class="ty-right">
                    <div>
                        <span class="cells">部门</span><span id="record_depart"></span>
                    </div>
                    <div>
                        <span class="cells">岗位</span><span id="record_post"></span>
                    </div>
                </div>
            </div>
            <div class="cellRow"><span id="record_peroid"></span>期间</div>
            <table class="ty-table ty-table-control">

            </table>
            <div class="areaA"></div>
            <div class="clear cellRow">
                <div class="ty-left">以下为历次正式考核的分数，其中红色的为需补考的。</div>
                <div class="ty-right grayLink">
                    查看补考情况
                </div>
            </div>
            <table class="ty-table ty-table-control">
                <tr>
                    <td>交卷截止时间</td>
                    <td>实际交卷时间</td>
                    <td>答题用时</td>
                    <td>名次</td>
                    <td>分数</td>
                </tr>
            </table>
            <div class="testEnd ty-center">— —以下无内容— —</div>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>考核管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div class="ty-container">
                <%-- 主页面 --%>
                <input type="hidden" id="showMainConNum">
                <div class="mianCon mainCon1">
                    <p>
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-fun="startTest">发起考核</span>
                        <span style="margin-left:20px; " class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="goFinish" >已结束的考核</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="goStop" >被终止的考核</span>
                    </p>
                    <div>
                        <div class="ttl"><span>以下是尚未结束的考核。</span></div>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowBtn" data-type="endTime">考试截止时间 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowBtn" data-type="createTime">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td>考核目的</td>
                                <td class="arrowBtn" data-type="bankNum">相关题库数量 <i class="fa fa-long-arrow-down"></i></td>
                                <td>试题数量</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td>考试截止时间 </td>
                                <td>创建 </td>
                                <td>考核目的</td>
                                <td>相关题库数量  </td>
                                <td>试题数量</td>
                                <td>
                                    <span class="funbtn ty-color-blue" data-fun="testScan">查看</span>
                                    <span class="funbtn ty-color-red" data-fun="testDel">终止本次考核</span>
                                </td>
                            </tr>
                        </table>
                        <div id="page1"></div>
                    </div>
                </div>
                <%-- 被终止的考核 --%>
                <div class="mianCon mainCon3">
                    <p>
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-fun="backMain">返 回</span>
                    </p>
                    <div>
                        <div class="ttl"><span>以下是已被终止的考核。</span></div>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowBtn" data-type="endTime">考试截止时间 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowBtn" data-type="createTime">创建 <i class="fa fa-long-arrow-down"></i></td>
                                <td class="arrowBtn" data-type="stopTime">终止时间 <i class="fa fa-long-arrow-down"></i></td>
                                <td>考核目的</td>
                                <td>试题数量</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td>考试截止时间 </td>
                                <td>创建 </td>
                                <td>终止时间</td>
                                <td>考核目的</td>
                                <td>试题数量</td>
                                <td>
                                    <span class="funbtn ty-color-blue" data-fun="testScan">查看</span>
                                </td>
                            </tr>
                        </table>
                        <div id="page3"></div>
                    </div>
                </div>
                <%-- 已结束的考核 --%>
                <div class="mianCon mainCon4">
                    <div class="clear">
                        <div class="ty-left">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 byMonth1" data-fun="backMain">返回</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 byMonth2" data-fun="backMain">返回考核管理主页</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 byMonth2" data-fun="goFinish" >返回上一页</span>
                        </div>
                        <div class="queryByTime ty-right">
                            <div class="querySect ty-right">
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="yearQuery" >查看年报表</button>
                                <div id="yearQueryTime"></div>
                            </div>
                            <div class="querySect ty-right">
                                <button style="margin-right:20px; " class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="monthQuery" data-fun="monthQuery" >查看其他月份统计</button>
                                <div class="layui-inline" id="monthQueryTime"></div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="ttl1">
                            <span class="infottl"><%--以下是已结束的考核--%></span>
                            <span class="blueLink linkBtn ty-color-blue" onclick="byEmployeeScan()">切换为按职工查看</span>
                        </div>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowBtn" data-type="endTime">交卷截止时间 <i class="fa fa-long-arrow-down"></i></td>
                                <td>考核目的</td>
                                <td>参考人数</td>
                                <td>一次通过</td>
                                <td>尚未通过</td>
                                <td>及格分数</td>
                                <td>平均分</td>
                                <td>最高分</td>
                                <td>最低分</td>
                                <td>操作</td>
                            </tr>

                        </table>
                        <div id="page4"></div>
                    </div>
                </div>
                <%-- 考试排名 --%>
                <div class="mianCon mainCon5">
                    <div class="ranking">
                        <div class="clear ranking_head">
                            <span class="ty-btn ty-left ty-btn-blue ty-btn-big ty-circle-5" data-fun="backCon4">关 闭</span>
                            <h4 class="ty-center">考试排名</h4>
                        </div>
                        <table width="100%">
                            <tbody>
                            <tr>
                                <td width="130">考核目的</td>
                                <td colspan="3" class="target"></td>
                            </tr>
                            <tr>
                                <td>交卷截止时间</td>
                                <td class="endTime">202-21-21 12：12：12</td>
                                <td class="ty-center">参考人数</td>
                                <td><span class="usersLen">112</span>人</td>
                            </tr>
                            <tr>
                                <td>试题数量</td>
                                <td colspan="3" class="testInfo">共XX道，其中单选题XX道，判断题XX道</td>
                            </tr>
                            </tbody>
                        </table>
                        <hr>
                        <p>表中被标识为了深颜色的，为分数低于 <span class="passingScore"></span>、需补考的。</p>
                        <div class="clear">
                            <p class="ty-left">当前尚有 <span class="failNum"></span>人未最终通过考试，还需补考。</p>
                            <span class="linkBtn makeupScan ty-right" data-fun="makeupScan">查看补考</span>
                        </div>
                        <table id="tb5" class="ty-table ty-table-control">
                            <tr>
                                <td>名次</td>
                                <td>职工</td>
                                <td>部门与岗位</td>
                                <td>答题用时</td>
                                <td>分数</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <%-- 补考情况 --%>
                <div class="mianCon mainCon6">
                    <div class="makeAgain">
                        <div class="clear">
                            <span class="ty-btn ty-left ty-btn-green ty-btn-big ty-circle-5" onclick="showMainCon(5)">关 闭</span>
                            <h3 class="ty-center">补考情况</h3>
                        </div>
                        <table width="100%" class="againning">
                            <tr>
                                <td>考核目的</td>
                                <td colspan="3" class="target"></td>
                            </tr>
                            <tr>
                                <td>交卷截止时间</td>
                                <td class="endTime">202-21-21 12：12：12</td>
                                <td>参考人数</td>
                                <td><span class="usersLen">112</span>人</td>
                            </tr>
                        </table>
                        <hr>
                        <table id="tb6" class="ty-table ty-table-control">
                            <tr>
                                <td>职工</td>
                                <td>部门与岗位</td>
                                <td>已补考次数</td>
                                <td>当前状态</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td>职工 15202255153</td>
                                <td>部门与岗位</td>
                                <td>已补考次数</td>
                                <td>当前状态</td>
                                <td><span class="ty-color-blue" data-fun="makeupLog">查看</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <%-- 切换为按职工查看 --%>
                <div class="mianCon mainCon7">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backMain">返回考核管理主页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backPre" >返回上一页</span>
                    </p>
                    <div>
                        <div class="ttl1">
                            <span class="infottl"></span>
                        </div>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td>职工</td>
                                <td>部门与岗位</td>
                                <td>参考次数</td>
                                <td>一次通过的次数</td>
                                <td>补考次数</td>
                                <td>平均分</td>
                                <td>最高分</td>
                                <td>最低分</td>
                                <td>最高名次</td>
                                <td>最低名次</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                                <td>
                                    <span class="funbtn ty-color-blue" data-fun="assessmentRecordScan">查看详情</span>
                                </td>
                            </tr>
                        </table>
                        <div id="page7"></div>
                    </div>
                </div>
                <%-- 查看年统计 --%>
                <div class="mianCon mainCon8">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backMain">返回考核管理主页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backPre" >返回上一页</span>
                    </p>
                    <div>
                        <div class="ttl1">
                            <span class="infottl"><%--以下是已结束的考核--%></span>
                            <span class="blueLink linkBtn ty-color-blue" data-fun="monthScan">查看月统计</span>
                        </div>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td class="arrowBtn" data-type="endTime">交卷截止时间 <i class="fa fa-long-arrow-down"></i></td>
                                <td>考核目的</td>
                                <td>参考人数</td>
                                <td>一次通过</td>
                                <td>及格分数</td>
                                <td>平均分</td>
                                <td>最高分</td>
                                <td>最低分</td>
                                <td>操作</td>
                            </tr>

                        </table>
                        <div id="page8"></div>
                    </div>
                </div>
                <%-- 查看月统计 --%>
                <div class="mianCon mainCon9">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backMain">返回考核管理主页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backPre" >返回上一页</span>
                    </p>
                    <div class="limitPy">
                        <div class="tbTtl ty-center">查看月统计</div>
                        <table class="ty-table ty-table-control">
                            <tr>
                                <td>月份</td>
                                <td>应结束的考核</td>
                                <td>实际结束的考核</td>
                            </tr>
                            <tr>
                                <td>职工</td>
                                <td>职工</td>
                                <td>职工</td>
                            </tr>
                        </table>
                        <div id="page9"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/trainManage/assessManage.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>

