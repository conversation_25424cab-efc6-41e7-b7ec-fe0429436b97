
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/unit/unitManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">

<div class="bounce">
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk()">确定</span>
        </div>
    </div>
    <%-- 失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 停启用tip  --%>
    <div class="bonceContainer bounce-red" id="tip2">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="editUnitOk()">确定</span>
        </div>
    </div>


</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>计量单位</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content"  >
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="mainCon mainCon1">
                    <div class="flexCon">
                        <div> <span>现有计量单位有如下 <span class="totalNum1"></span> 个</span> </div>
                        <div> <span class="linkBtn" data-type="addNew">新增计量单位</span> </div>
                        <div> <span>已停用的计量单位有 <span class="sumHistory"></span> 种 <span class="linkBtn" data-type="seeStopList">去查看</span></span> </div>
                    </div>
                    <table class="ty-table ty-table-control" style="margin-top: 20px;">
                        <thead>
                        <tr>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>恢复使用的操作者</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <div id="ye1"></div>
                </div>
                <div class="mainCon mainCon2">
                    <p>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goPre">返回上一页</span>
                    </p>
                    <div>
                        <span>曾使用过的计量单位有如下 <span class="totalNum2"></span> 个</span>
                    </div>
                    <table class="ty-table ty-table-control"style="margin-top: 20px;">
                        <thead>
                        <tr>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>停用人</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="ye2"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/unit/unitManage.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
