<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/purchase/orderInfo.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/purchase/orderManage.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<%@ include  file="../../common/headerBottom.jsp"%>
<style>
    body,.ty-mainData{background: #fff; }
    .orderInfo .ty-color-red{width: 400px;  line-height: 20px;}
    .orderInfo p{ margin-bottom:0; line-height:30px;   }
    .orderInfo span{display:inline-block;  }
    .orderInfo .t1{ width: 350px; }
    .gapR{margin-right: 15px;}
</style>
</head>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="zhe_AjaxPic">
    <div class="zhe_circleCon">
        <h3 class="zhe_loadingTip">正在加载 . . . </h3>
    </div>
</div>
<div class="bounce">
    <%-- 信息对比  --%>
    <div class="bonceContainer bounce-blue" id="detailsCompare" style="width: 1100px;">
        <div class="bonceHead">
            <span>信息对比</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control bg-yellow gapBt" id="mtDetailsCompare">
                <thead>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>创建人</td>
                    <td>计量单位</td>
                    <td>当前库存</td>
                    <td>最低库存</td>
                    <td>占用库位</td>
                    <td>供应商</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div id="compareSupplier" class="supplierList">
                <div class="tabs__header clear">
                    <div class="tabs__nav">
                    </div>
                </div>
                <div class="tabs__content">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>
    <%-- 驳回理由 --%>
    <div class="bonceContainer bounce-red" id="approveOrd3" style="width:500px;">
        <div class="bonceHead">
            <span>驳回理由</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="reasonCon" >
                <p>请选择或录入驳回理由：</p>
                <p><i data-type="1" class="fa fa-circle-o"></i> <span>该供应商价格贵</span></p>
                <p><i data-type="2" class="fa fa-circle-o"></i> <span>该供应商质量不稳定</span></p>
                <p><i data-type="3" class="fa fa-circle-o"></i> <span>该供应商服务态度不好</span></p>
                <p><i data-type="4" class="fa fa-circle-o"></i> <span>该供应商无法确保准时交货</span></p>
                <p><i data-type="5" class="fa fa-circle-o"></i> <span>付款方式不合理</span></p>
                <p><i data-type="6" class="fa fa-circle-o"></i> <span>暂无需采购</span></p>
                <p><i data-type="7" class="fa fa-circle-o"></i> <span>发票原因</span></p>
                <p><i data-type="8" class="fa fa-circle-o"></i> <span>其他原因</span></p>
                <p><textarea onkeyup="wordsMaxTip()" id="rejectReasionDesc" placeholder="请录入原因" style="width: 300px; height:100px;"></textarea><span class="txtMax"></span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="approveOrd(3)">确定</span>
        </div>
    </div>
    <%--在途数量--%>
    <div class="bonceContainer bounce-blue" id="inTransit" style="width:918px; ">
        <div class="bonceHead">
            <span>在途数量</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon otherControl">
            <div class="conTtl">
                <span need data-name="code">XX材XX料XX代XX号XX</span>
                <span need data-name="name">X材X料XX名X称X</span>
                <span>在途共<span need data-name="way_num"></span></span>
            </div>
            <div>规格：<span need data-name="specifications"></span></div>
            <div class="clear"><span class="ty-left">型号：<span need data-name="model"></span></span><span class="ty-right">计量单位：<span need data-name="unit"></span></span></div>
            <hr/>
            <div>
                <table class="ty-table ty-table-control yellowTb">
                    <thead>
                    <tr>
                        <td>购买理由</td>
                        <td>采购已订购数量/在途数</td>
                        <td>要求到货日期</td>
                        <td>订单进度</td>
                        <td>订单号</td>
                        <td>供应商</td>
                        <td>请购时间</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>购买理由</td>
                        <td>采购已订购数量/在途数</td>
                        <td>要求到货日期</td>
                        <td>订单进度</td>
                        <td>订单号</td>
                        <td>供应商</td>
                        <td>请购时间</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 购买理由  --%>
    <div class="bonceContainer bounce-blue" id="buyReason">
        <div class="bonceHead">
            <span>购买理由</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>
</div>
<%-- 主页面 --%>
<div class="ty-mainData" style="display: none; " >
    <div class="centerCon" id="mainshow" style="display: none;">
        <div class="mainHead clear">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-left" onclick="cancelIframe()">取消</span>
            <h2 id="title">采购订单</h2>
            <div class="controlArr ty-right">
                <div class="ctrl_t1">
                    <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="approveOrdBtn(3)">审批驳回</span>
                    <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="approveOrd(2)">审批通过</span>
                </div>
                <div class="ctrl_t0">
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="approveOrd(4)">撤回采购申请</span>
                </div>
                <div class="ctrl_t2"></div>
                <div class="ctrl_t3"></div>
            </div>
        </div>
        <div>
            <table class="orderInfo">
                <tr>
                    <td>
                        <p>
                            <span class="t1"><span>供应商名称：</span><span class="show_supplierName"></span></span>
                            <span>订单总额：</span><span class="show_amount"></span>
                        </p>
                    </td>
                    <td><span>订单号：</span><span class="show_sn"></span></td>
                </tr>
            </table>
        </div>
        <div style="width: 100%; overflow: auto">
            <p>系统内数据为实时更新，故与采购申请时相比，您在购买理由中看到的数据可能已发生了变化！</p>
            <table style="width: 1200px" class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>要求到货日期</td>
                    <td>购买数量</td>
                    <td>单价</td>
                    <td>总价</td>
                    <td>备注</td>
                    <td>运费</td>
                    <td>购买理由</td>
                    <td>在途数量</td>
                    <td>信息对比</td>
                </tr>
                </thead>
                <tbody class="ordersItem">
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>购买数量</td>
                    <td>要求到货日期</td>
                    <td>单价</td>
                    <td>总价</td>
                    <td>备注</td>
                    <td>运费</td>
                    <td><span onclick="detailsCompare($(this))">查看</span></td>
                </tr>
                </tbody>
            </table>
        </div>

    </div>
</div>

<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/purchase/orderInfo.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>
</body>
</html>
