<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/purchase/purchaseSalesPlan.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%--未完成的销售订单--%>
    <div class="bonceContainer bounce-blue" id="outstandingOrders" style="width:918px; ">
        <div class="bonceHead">
            <span>未完成的销售订单</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon otherControl">
            <div class="conTtl">
                <span class="fontH5" need data-name="inner_sn">XX材XX料XX代XX号XX</span>
                <span class="fontH5" need data-name="name">X材X料XX名X称X</span>
            </div>
            <div>规格：<span need data-name="specifications"></span></div>
            <div class="clear"><span class="ty-left">型号：<span need data-name="model"></span></span><span class="ty-right">计量单位：<span need data-name="unit"></span></span></div>
            <hr/>
            <div>
                <table class="ty-table ty-table-control yellowTb">
                    <thead>
                    <tr>
                        <td>订单号</td>
                        <td>订单收到日期</td>
                        <td>客户名称</td>
                        <td>客户订购数/未发货数</td>
                        <td>统筹订购数</td>
                        <td>采购订购数/在途数</td>
                        <td>相应的采购订单号</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--在途数量--%>
    <div class="bonceContainer bounce-green" id="inTransit" style="width:918px; ">
        <div class="bonceHead">
            <span>在途数量</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon otherControl">
            <div class="conTtl">
                <span need data-name="code">XX材XX料XX代XX号XX</span>
                <span need data-name="name">X材X料XX名X称X</span>
                <span>在途共 <span need data-name="way_num"></span></span>
            </div>
            <div>规格：<span need data-name="specifications"></span></div>
            <div class="clear"><span class="ty-left">型号：<span need data-name="model"></span></span><span class="ty-right">计量单位：<span need data-name="unit"></span></span></div>
            <hr/>
            <div>
                <table class="ty-table ty-table-control yellowTb">
                    <thead>
                    <tr>
                        <td>购买理由</td>
                        <td>采购已订购数量/在途数</td>
                        <td>要求到货日期</td>
                        <td>订单进度</td>
                        <td>订单号</td>
                        <td>供应商</td>
                        <td>请购时间</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>购买理由</td>
                        <td>采购已订购数量/在途数</td>
                        <td>要求到货日期</td>
                        <td>订单进度</td>
                        <td>订单号</td>
                        <td>供应商</td>
                        <td>请购时间</td>
                    </tr><tr>
                        <td>购买理由</td>
                        <td>采购已订购数量/在途数</td>
                        <td>要求到货日期</td>
                        <td>订单进度</td>
                        <td>订单号</td>
                        <td>供应商</td>
                        <td>请购时间</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>购销统筹</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container">
                <%-- 主页面 --%>
                <div class="hd" id="showMainConNum"></div>
                <div class="hd" id="planConNum"></div>
                    <div class="mainCon1">
                        <div class="clear" style="margin-bottom: 20px;">
                            <ul class="ty-secondTab">
                                <li class="ty-active">待统筹</li>
                                <li>已统筹,但采购未完成</li>
                            </ul>
                            <div class="ty-right taskFinished"><span id="purchaseCompleted" data-type="orderCompleted" class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">已统筹,且采购已完成</span></div>
                        </div>
                        <div class="narrowSize">
                            <table class="ty-table ty-table-control bg-yellow">
                                <thead>
                                <td>订单号</td>
                                <td>订单收到日期</td>
                                <td>客户名称</td>
                                <td>订单录入</td>
                                <td class="ttlStr">统筹处理的时间</td>
                                <td>操作</td>
                                </thead>
                                <tbody>
                                </tbody>

                            </table>
                        </div>
                        <div id="yeCon"></div>
                    </div>
                    <div class="mainCon2">
                        <div class="clear headCon">
                            <div class="ty-left">
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返 回</span>
                                <%--<span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回上一页</span>--%>
                            </div>
                            <div class="ty-right">
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="allFinished">均已处理，下一步</span>
                            </div>
                        </div>
                        <div class="clear gapB">
                            <div class="ty-left">
                                <input type="hidden" id="orderId"/>
                                <input type="hidden" id="untreatedSize"/>
                                <p class="mdFont" need data-name="customer_name"></p>
                                <div class="modBill">
                                    <span need data-name="CODE"></span>
                                    <span>订单收到日期：<span need data-name="sign_date"></span></span>
                                    <span>订单号：<span need data-name="sn"></span></span>
                                </div>
                            </div>
                            <div class="ty-right">
                                <p class="reviewLogBtn"><span class="ty-color-blue">订单评审日志</span></p>
                                <div class="smFont">
                                    <div>订单录入：<span need data-name="orderCreate"></span></div>
                                </div>
                            </div>
                        </div>
                        <table class="ty-table ty-table-control bg-yellow">
                            <thead>
                            <td>商品代号</td>
                            <td>商品名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>客户订购数</td>
                            <td class="arrowBtn">要求到货日期<i class="fa fa-long-arrow-down"></i></td>
                            <td>状态</td>
                            <td>操作</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="mainCon3">
                        <span class="hd" id="handleLog"></span>
                        <div class="clear headCon">
                            <div class="ty-left">
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回购销统筹首页</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goPrev">返回上一页</span>
                            </div>
                            <div class="ty-right">
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="matPurchaseSure" data-type="matPurchaseSure">确 定</span>
                            </div>
                        </div>
                        <div class="middleGap">
                            <table class="ty-table ty-table-control bg-yellow" id="goodsDetail">
                                <thead>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>客户订购数</td>
                                <td class="arrowBtn">要求到货日期<i class="fa fa-long-arrow-down"></i></td>
                                <td>本商品其他未完成的销售订单</td>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                    <td>创建人</td>
                                    <td>创建人</td>
                                    <td>创建人</td>
                                    <td><span class="btn ty-color-blue" type="btn" data-type="handle">查看</span>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <p>以下为与<span id="modName"></span>商品相关联的产品，如需订购，请确定到货时间与数量的最低要求后点击确定。如无需订购，请选择无需订购的选项，并点击确定</p>
                            <table class="ty-table ty-table-control bg-yellow" id="matsDetail">
                                <thead>
                                <td>材料代号</td>
                                <td>材料名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>理论需要数量</td>
                                <td>当前库存</td>
                                <td>在途数量</td>
                                <td>到货数量不可少于</td>
                                <td>到货时间不可晚于</td>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                    <td>创建人</td>
                                    <td>创建人</td>
                                    <td>创建人</td>
                                    <td>创建人</td>
                                    <td><input type="text" value="" /></td>
                                    <td><input type="text" value="" id="lastArrivalTime" /></td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="nextStep">
                                <div class="nextBtn"><span class="checkLable" data-state=""><span class="fa fa-circle-o"></span> 经确认，本材料无需订购</span></div>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon4">
                        <div class="clear headCon">
                            <div class="ty-left">
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMainRush">返 回</span>
                            </div>
                            <div class="ty-right">
                                <div class="timeBtn">
                                    <span class="btnCat ssMonth" data-type="ssMonth">月统计表<i class="fa fa-sort-desc"></i></span>
                                    <div class="monthTb" data-type="month">
                                    </div>
                                </div>
                                <div class="timeBtn">
                                    <span class="btnCat ssYear" data-type="ssYear">年统计表<i class="fa fa-sort-desc"></i></span>
                                    <div class="yearTb" data-type="year">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p>
                            <span class="duringTime"></span>期间，已统筹、且采购已完成的销售订单共<span class="saleOrderCount">0</span>个
                        </p>
                        <table class="ty-table ty-table-control bg-yellow">
                            <thead>
                            <td>订单号</td>
                            <td>订单收到日期</td>
                            <td>客户名称</td>
                            <td>订单录入</td>
                            <td>统筹处理的时间</td>
                            <td>操作</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <div id="yeCon1"></div>
                    </div>
                    <div class="mainCon5">
                        <div class="clear headCon">
                            <div class="ty-left">
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回购销统筹首页</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goPrev">返回上一页</span>
                            </div>
                        </div>
                        <div class="clear gapB">
                            <div class="ty-left">
                                <input type="hidden" id="up_orderId"/>
                                <input type="hidden" id="up_untreatedSize"/>
                                <p class="mdFont" need data-name="customer_name"></p>
                                <div class="modBill">
                                    <span need data-name="CODE"></span>
                                    <span>订单收到日期：<span need data-name="sign_date"></span></span>
                                    <span>订单号：<span need data-name="sn"></span></span>
                                </div>
                            </div>
                            <div class="ty-right">
                                <p class="reviewLogBtn"><span class="ty-color-blue">订单评审日志</span></p>
                                <div class="smFont">
                                    <div>订单录入：<span need data-name="orderCreate"></span></div>
                                    <div id="signCreate">统筹处理：<span></span></div>
                                </div>
                            </div>
                        </div>
                        <table class="ty-table ty-table-control bg-yellow">
                            <thead>
                            <td>商品代号</td>
                            <td>商品名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>客户订购数/未发货数</td>
                            <td>统筹订购数</td>
                            <td>采购订购数/在途数</td>
                            <td>相应的采购订单号</td>
                            <td>操作</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
            </div>
        </div>

        <%@ include  file="../../common/contentSliderLitbar.jsp"%>
    </div>
    <%@ include  file="../../common/footerTop.jsp"%>
    <%@ include  file="../../common/footerScript.jsp"%>

    <script src="../script/purchase/purchaseSalesPlan.js?v=SVN_REVISION" type="text/javascript"></script>

    <%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
