
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/purchase/materialEntry.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">

<div class="bounce">
    <%-- 编辑分类名称 --%>
    <div class="bonceContainer bounce-blue" id="editCat">
        <div class="bonceHead">
            <span>编辑分类名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu">
                    <p>请输入修改后的分类名称</p>
                    <input type="text"  id="kindEditName" placeholder="请输入修改后的分类名称" >
                    <input type="hidden"  id="kindEditID">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="saveKindEdit()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="delCatTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align:center;">
            确定删除该分类？
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="okDeleteKind()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5 bounce-cancel" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 删除材料  --%>
    <div class="bonceContainer bounce-red" id="mtDel">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">确定删除此材料？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="mtScanDel('mtDel', 1) ">确定</span>
        </div>
    </div>
    <%-- 新增材料类别  --%>
    <div class="bonceContainer bounce-green" id="addCat">
        <div class="bonceHead">
            <span>新增材料类别</span>
            <a class="bounce_close" onclick="addCatCacel()"></a>
        </div>
        <div class="bonceCon"  >
            <p>新增的类别将隶属于：</p>
            <input type="hidden" id="addkind_pid">
            <p class="ty-color-blue" id="catParents">全部 > 怨妇才来哦 > 待分类</p>
            <hr style="border-bottom:1px solid #ccc; ">
            <p>材料类别的名称</p>
            <p class="ty-center">
                <input type="text" placeholder=" 请录入" id="newCatName">
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="addCatCacel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addCatOk()">确定</span>
        </div>
    </div>
    <%-- 材料录入 --%>
        <div class="bonceContainer bounce-green" id="mtEntryImportBefore" style="width:352px; ">
            <div class="bonceHead">
                <span>批量导入</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <p>每次只能导入一个类别的材料，请先选择一个类别！</p>
                <div>
                    材料类别 <i class="red">*</i>
                    <span class="ty-right btnCat" onclick="addCatBtn(3)">新增</span>
                </div>
                <div class="categorySelectList" style="line-height:40px;">
                    <select style="width: 292px;" class="form-control category" id="catImport" onchange="setCat($(this).val(), $(this))" ></select>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="mtEntryImportBeforeOk() ">确定</span>
            </div>
        </div>
    <div class="bonceContainer bounce-green" id="mtEntry">
        <div class="bonceHead">
            <span>材料录入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear">
                <div class="ty-left">
                    <div class="say1">您正在录入 <span class="red">曾采购过的材料</span>。<br>录入完毕后，库管员将收到提示，且必须填写当前库存数量。</div>
                    <div class="say0">您正在录入 <span class="red">未采购过的材料</span>。<br>录入完毕后，库存数量无法填写。</div>
                    <div class="say"></div>
                </div>
                <div class="editTip">
                    <p class="ty-color-orange">修改说明：</p>
                    <div>
                        <div class="ty-color-orange">此处的修改仅能对以后所发的采购订单生效，对已存在于系统中的采购订单无法生效！</div>
                        <div class="orderCount0">系统中包含本材料的未完结采购订单共<span class="order_count"></span>个。</div>
                        <div class="ty-color-orange orderCount0">如需修改未完结采购订单中的材料信息，可通过修改完材料信息后“终止”相应的订单，之后再录入新订单的方式。</div>
                    </div>
                </div>
                <div class="ty-right">
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="importBtn" onclick="leadingShow()">批量导入</span>
                </div>
            </div>
            <input name="operation" type="hidden"  require/>
            <input name="userId" type="hidden" require/>
            <input name="isPurchased" type="hidden" require/>
            <input name="enabled" type="hidden" require/>
            <input name="id" type="hidden" require/>
            <input name="origin" type="hidden" require/>
            <div class="item">
                <div class="col-md-6"><span class="item-title">材料名称<span class="red">*</span></span>
                    <br><input name="name" type="text" require need/></div>
                <div class="col-md-6"><span class="item-title">材料代号<span class="red">*</span></span>
                    <br><input name="code" type="text" require need/></div>

                <div class="col-md-6"><span class="item-title">规格</span>
                    <br><input name="specifications" require  type="text" /></div>
                <div class="col-md-6"><span class="item-title">型号</span>
                    <br><input name="model" require  type="text" /></div>

                <div class="col-md-6">
                    <span class="item-title">计量单位<span class="red">*</span></span>
                    <span onclick="addUnit()" class="btnCat ty-right" id="unitSelectBtn"> 新增</span>
                    <br><select type="text" id="unitSelect" name="unitId" require need></select>
                </div>
                <div class="col-md-6 categorySelectList" id="cats">
                    <span class="item-title">材料类别<span class="red">*</span></span>
                    <span class="btnCat ty-right" id="catSelectBtn" onclick="addCatBtn2()"> 新增</span>
                    <br><select id="category" class="category" onchange="setCat($(this).val(), $(this))" require> </select>
                </div>
                <div class="col-md-12 intelligentWh" style="padding: 5px 0;">
                    <p class="bz">
                        <span style="margin-right: 25px;">本材料是否有保质期方面的要求？<i class="ty-color-red">*</i></span>
                        <span class="faG1" id="baozhi1" onclick="baozhi($(this))"><i data-type="1" class="fa fa-circle-o"></i>有</span>
                        <span class="faG1" id="baozhi0" onclick="baozhi($(this))"><i data-type="0" class="fa fa-circle-o"></i>没有</span>
                    </p>
                    <p class="baozhi1">
                        <span>（开封）开瓶后可使用几日？ </span>
                        <span>
                            <input style="width:460px;" placeholder="请录入" id="openDuration" type="text" class="form-control" onkeyup="clearNum(this); countWords($(this),13)" />
                            <span class="textMax2 ty-right" style="position: relative; left: -20px; " id="len1">0/13</span>
                        </span>

                    </p>
                    <p class="baozhi1">
                        <span>入库时，与保质期有关的数据需录入哪项？<i class="ty-color-red">*</i></span>
                        <span class="faG1" style="width: 140px;" id="baozhiInput1" onclick="baozhiInput($(this))"><i data-type="1" class="fa fa-circle-o"></i>可使用的截至日期</span>
                        <span class="faG1" id="baozhiInput2" onclick="baozhiInput($(this))"><i data-type="2" class="fa fa-circle-o"></i>生产日期</span>
                    </p>
                    <p class="baozhiInput2">
                        <span>不同供应商供应的本材料保质期是否相同？<i class="ty-color-red">*</i></span>
                        <span class="faG1" id="baozhiSame1" onclick="baozhiSame($(this))"><i data-type="1" class="fa fa-circle-o"></i>相同</span>
                        <span class="faG1" id="baozhiSame0" onclick="baozhiSame($(this))"><i data-type="0" class="fa fa-circle-o"></i>不同</span>
                    </p>
                    <p class="baozhiTime">
                        <span style="margin-right: 30px;">本材料的保质期为自生产日期之后多久？<i class="ty-color-red">*</i></span>
                        <span>
                            <input style="width:180px;" placeholder="请录入数字" type="text" class="form-control" id="expirationDays" onkeyup="clearNum(this);" /> 天
                        </span>
                    </p>
                </div>

                <div class="col-md-12">
                    <span class="item-title">备注</span>
                    <input name="memo" type="text" require class="form-control" onkeyup="countWords($(this),100)" />
                    <span class="textMax ty-right" id="len2">0/100</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="mtEditOk() ">确定</span>
        </div>
    </div>
    <%-- 材料查看 --%>
    <div class="bonceContainer bounce-blue" id="mtScan">
        <div class="bonceHead">
            <span>材料查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <div class="col-md-6">
                    <span class="scanMt_state">本材料录入时为“曾采购过的材料”。</span>
                </div>
                <div class="col-md-3"><span class="btnCat" id="editMtBtn" onclick="editMtBtn()">修改材料的基本信息</span></div>
                <div class="col-md-3" style="text-align: right"><span class="disabled btnCat">材料基本信息的修改记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-2">&nbsp; </div>
                <div class="col-md-6"><span class="item-title">创建人</span><span class="scanMt_create">张三丰给 2015-15-15 14：12：12</span></div>
                <div class="col-md-4" style="text-align: right"><span class="disabled btnCat ">材料暂停/恢复采购的操作记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">材料名称</span><span class="scanMt_name">sdsdsfsdsdf</span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span class="scanMt_code">sdsdsfsdsdf</span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span class="scanMt_unit">sdsdsfsdsdf</span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">规格</span><span class="scanMt_specifications">sdskdsds</span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span class="scanMt_model">sdskdsds</span></div>
            </div>
            <div class="item scanExpShow">
                <div class="col-md-12"><span class="scanMt_expStr"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">备注</span><span class="scanMt_memo">sdfsadsa</span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">材料类别</span>
                    <span class="scanMt_cat">sdsds>sdsds>sdsds</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(7)">确定</span>
        </div>
    </div>
    <%--导入--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #ddebf7;">
            <form action="../export/ImportMaterial.do" id="materielImport" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：下载空白的“材料清单”。</p>
                        <div class="flexRow">
                            <span>材料清单</span>
                            <a href="../assets/oralResource/template/material_blank_sheet.xls"
                               id="mould1" download="材料清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“材料清单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="uploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="matImportOk('cancel');">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="matImportOk()">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加、删除或修改所下载“材料清单”空白表的“列”，否则上传会失败。</span></br>
                            <span>2、在电脑上保存“材料清单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot" style="background: #ddebf7;">
        </div>
    </div>
    <%-- 下一步确定  --%>
    <div class="bonceContainer bounce-red" id="importListTj">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>还有<span id="errNum"></span>种材料无法保存至系统。</p>
            <p>进入下一步，这些材料将被舍弃。</p>
            <p>确定进入下一步吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="importListTjSure()">确定</span>
        </div>
    </div>
    <%-- 放弃  --%>
    <div class="bonceContainer bounce-red" id="importCancel">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>放弃后，本次批量导入的数据将消失不见。</p>
            <p>确定放弃吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-name="importCancelSure">确定</span>
        </div>
    </div>
    <%-- 保存  --%>
    <div class="bonceContainer bounce-red" id="importListSave">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <p>确定后：</p>
                <p class="saveTip"></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-name="lastSaveSure">确定</span>
        </div>
    </div>
    <%-- 上次的批量导入尚未完成。  --%>
    <div class="bonceContainer bounce-red" id="importNotCompleted">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody unfinishedForm">
                <p>上次的批量导入尚未完成。</p>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="1"></span>继续上次的操作
                </div>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="0"></span>放弃上次的操作，重新批量导入
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-name="judgeImport">确定</span>
        </div>
    </div>
    <%-- 修改导入的材料 --%>
    <div class="bonceContainer bounce-green" id="updateImportMt" style="width:702px">
        <div class="bonceHead">
            <span>材料修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item clear editInput">
                <div class="col-md-6"><span class="item-title">材料名称<span class="red">*</span></span><br><input name="name" type="text" require need/></div>
                <div class="col-md-6"><span class="item-title">材料代号<span class="red">*</span></span><br><input name="code" type="text" require need/></div>
            </div>
            <div class="item clear editInput">
                <div class="col-md-6"><span class="item-title">规格</span><br><input name="specifications" require  type="text" /></div>
                <div class="col-md-6"><span class="item-title">型号</span><br><input name="model" require  type="text" /></div>
            </div>

            <div class="col-md-6" style="margin-top:10px; ">
                <span class="item-title">计量单位<span class="red">*</span></span>
                <br><input type="text" id="unitSelect2" name="unitId" disabled require />
            </div>
            <div class="col-md-6 categorySelectList" id="cats_import" style="margin-top:10px; ">
                <span class="item-title">材料类别<span class="red">*</span></span>
                <br><input id="category2" disabled name="category_" />
            </div>
            <div id="expCont" class="col-md-12" style=" position: relative; padding: 10px; margin:10px 0 0; ">
                <div class="maskImport"></div>
                <p class="bz">
                    <span style="margin-right: 25px;">本材料是否有保质期方面的要求？<i class="ty-color-red">*</i></span>
                    <span class="faG1" id="baozhi1_import" onclick="baozhi($(this))"><i data-type="1" class="fa fa-circle-o"></i>有</span>
                    <span class="faG1" id="baozhi0_import" onclick="baozhi($(this))"><i data-type="0" class="fa fa-circle-o"></i>没有</span>
                </p>
                <p class="baozhi1">
                    <span>开瓶（开封）后可使用几日？ </span>
                    <span>
                            <input style="width:460px;" placeholder="请录入" id="openDuration_import" type="text" class="form-control" onkeyup="clearNum(this); countWords($(this),13)" />
                            <span class="textMax ty-right" style="position: relative; left: -20px; " id="len1_import">0/13</span>
                        </span>

                </p>
                <p class="baozhi1">
                    <span>入库时，与保质期有关的数据需录入哪项？<i class="ty-color-red">*</i></span>
                    <span class="faG1" style="width: 140px;" id="baozhiInput1_import" onclick="baozhiInput($(this))"><i data-type="1" class="fa fa-circle-o"></i>可使用的截至日期</span>
                    <span class="faG1" id="baozhiInput2_import" onclick="baozhiInput($(this))"><i data-type="2" class="fa fa-circle-o"></i>生产日期</span>
                </p>
                <p class="baozhiInput2">
                    <span>不同供应商供应的本材料保质期是否相同？<i class="ty-color-red">*</i></span>
                    <span class="faG1" id="baozhiSame1_import" onclick="baozhiSame($(this))"><i data-type="1" class="fa fa-circle-o"></i>相同</span>
                    <span class="faG1" id="baozhiSame0_import" onclick="baozhiSame($(this))"><i data-type="0" class="fa fa-circle-o"></i>不同</span>
                </p>
                <p class="baozhiTime">
                    <span style="margin-right: 30px;">本材料的保质期为自生产日期之后多久？<i class="ty-color-red">*</i></span>
                    <span>
                        <input style="width:180px;" placeholder="请录入数字" type="text" class="form-control" id="expirationDays_import" onkeyup="clearNum(this);" /> 天
                    </span>
                </p>
            </div>

            <div class="item clear editInput" style="margin-top:10px; ">
                <div class="col-md-12"><span class="item-title">备注</span>
                    <input name="memo" type="text" require class="form-control" onkeyup="countWords($(this),100)" />
                    <span class="textMax memoLen ty-right">0/100</span></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-name="updateImportMt">确定</span>
        </div>
    </div>
    <%-- 删除导入的材料  --%>
    <div class="bonceContainer bounce-red" id="importMtDel">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">确定删除所导入的这个材料吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="importMtDel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 导入的重要提示 --%>
    <div class="bonceContainer bounce-red" id="importantTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="gap-lt">
                <h4>导入失败！</h4>
                <div>原因可能为：</div>
                <div>1、修改了所下载表格中的“列”。</div>
                <div>2、选错了文件。</div>
                <div>3、文件太大，或里面含有图片等。</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed2.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-red" id="iknowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="iknowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>材料录入</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content"  >
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="mainCon mainCon1">
                    <div class="leCat">
                        <div>录入需采购的材料</div>
                        <ul class="addMt">
                            <li data-type="1" onclick="addMtBtn(1)">录入曾采购过的材料</li>
                            <li data-type="0" onclick="addMtBtn(0)">录入未曾采购过的材料</li>
                        </ul>
                        <div>全部（<span class="mtNum"></span>种）<span class="ty-right btnCat addCat" onclick="addCatBtn()">新增分类</span></div>
                        <ul id="catAll">
                        </ul>
                        <ul class="bottom" id="bottom1" onclick="goPause();">
                            <li>暂停采购的材料  <span id="enabledCount" class="catNum" style="top: 0px; float: right;"></span></li>
                        </ul>
                        <div class="bottom" id="bottom2">
                            <span class="btnCat" onclick="backPreCat(); ">返回上一级</span>
                            <span class="btnCat" onclick="backPreCat(1); ">返回全部材料</span>
                        </div>
                    </div>
                    <div class="hd" id="catTree"></div>
                    <div class="riMt">
                        <div>
                            <span>当前分类： <span class="curCatory curCat"></span></span>
                            <span class="search">查找：
                                <input type="text" placeholder="请输入原辅材料的代号或名称"><span onclick="search($(this))">确定</span>
                            </span>
                        </div>
                        <div class="lePad">
                            <table class="ty-table ty-table-control"  >
                                <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                    <td>
                                        <span data-type="mtScan" class="ty-color-blue ">查看</span>
                                        <span data-type="mtDel" class="ty-color-red ">删除</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div id="ye1"></div>
                        </div>

                    </div>
                </div>
                <div class="mainCon mainCon2">
                    <div class="leCat">
                        <div>暂停采购的材料
                            <span class="catNumPause"></span>
                        </div>
                        <div class="bottom">
                            <span class="btnCat" onclick="toggleSuspend(1);backPreCat(0)">返回上一级</span>
                            <span class="btnCat" onclick="toggleSuspend(1); backPreCat(1)">返回全部材料</span>
                        </div>
                    </div>
                    <div class="riMt">
                        <div>
                            <span>当前分类： <span class="curCatory curCat2"> 全部 > 暂停采购的材料</span></span>
                            <span class="search">查找：<input type="text" placeholder="请输入原辅材料的代号或名称"><span onclick="search($(this))">确定</span></span>
                        </div>
                        <div class="lePad">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                    <td>材料被暂停采购的时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                    <td>材料被暂停采购的时间</td>
                                    <td>
                                        <span class="ty-color-blue">查看</span>
                                        <span class="ty-color-red">删除</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div id="ye2"></div>
                        </div>

                    </div>
                </div>
                <div class="mainCon3 narrowLamp bg-yellow" id="importEnteryType">
                    <div class="importCon1">
                        <div class="importNoSave stepItem">
                            <span class="ty-btn ty-btn-yellow ty-btn-big btn" data-name="clearNoSave" style="margin-right: 250px;">放 弃</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big btn" data-name="stepNext">下一步</span>
                        </div>
                        <p>您共导入材料<span class="initAll"></span>条，其中以下<span class="initWrong"></span>条存在问题，<span class="red">无法保存至系统</span>。</p>
                        <p>名称或代号未录入，本次导入材料的代号互相重复或与系统中已有代号相同等情况，计量单位未填写或计量单位被停用。均算作问题。</p>
                        <div class="gap-Tp">
                            <div id="tureMtList" style="display: none;"></div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位<span onclick="addUnitCommom(3)" class="btnCat"> 新增</span></td>
                                <td>备注</td>
                                <td>操作</td>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="importCon2">
                        <div class="importing stepItem">
                            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 btn" data-name="cancelSave" style="margin-right: 250px;">放 弃</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 btn" id="save">保 存</span>
                        </div>
                        <div>
                            <div class="line matCategory categorySelectList" id="changeCat1">
                                <span>更换材料的类别<span class="red">*</span></span>
                                <select class="category" id="matCategorySelect" onchange="setCat($(this).val(), $(this))">
                                    <option value="">   请选择</option>
                                </select>
                            </div>
                            <span>您正在向<span id="curCatexp"></span>下导入材料。</span>
                        </div>
                        <hr style="border-bottom: 1px solid #ddd; clear: both;">
                        <p>您共导入材料<span class="initAll"></span>条，其中可保存至系统的共<span class="inabledSum"></span>条。</p>
                        <p>材料“保质期方面的规定”与 “计量单位”编辑后，才可保存至系统！</p>

                        <table class="ty-table ty-table-control">
                            <thead>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>保质期方面的要求<i class="red">*</i></td>
                            <td><span class="red">*</span>计量单位<span onclick="addImportUnit()" class="btnCat"> 新增</span></td>
                            <td>备注</td>
                            <td>操作</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <%@ include  file="../../common/contentSliderLitbar.jsp"%>
    </div>
    <%@ include  file="../../common/footerTop.jsp"%>
    <%@ include  file="../../common/footerScript.jsp"%>

    <%@ include  file="../../common/footerBottom.jsp"%>
    <script src="../script/purchase/materialEntry.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
