<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/purchase/materialManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/region.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">

<div class="bounce_Fixed4">
    <div class="bonceContainer bounce-blue" id="includeGoodContract" style="width: 800px;">
        <div class="bonceHead">
            <span>已包含某材料的销售合同</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="row-flex cusInfo">
            </div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>合同编号</td>
                    <td>所涉材料</td>
                    <td>有效期</td>
                    <td>签署日期</td>
                    <td>本版本合同的创建</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel()">关闭</span>
        </div>
    </div>
    <%--联系方式查看--%>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签：</span>
                    <span class="sale-con" id="see_contactTag"></span>
                </div>
            </div>
            <%--<p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>--%>
            <table class="see_otherContact ty-table" style="width:442px;">
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed4.cancel()">关闭</span>
        </div>
    </div>
    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" onkeyup="countWords($(this),20)"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed4.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%-- 向本合同添加材料 从本合同移出材料 本合同下的材料 --%>
    <div class="bonceContainer bounce-blue" id="tipcontractGoods" style="width: 800px; max-height:400px; ">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="row-flex" id="tips">
                <div><span class="supplerName"></span>的材料共有以下<span class="tip"></span>种</div>
                <div class="btn-group countStr">
                    已选 <span class="count">0</span> 种
                </div>
            </div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td class="selectTd"></td>
                    <td>材料代号</td>
                    <td>材料名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>已包含的合同</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 addOrCancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn bounce-ok addOrCancel ty-btn-big ty-circle-3" onclick="addOrCancelOk()">确定</span>
            <span class="ty-btn bounce-ok cScanc ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%-- 选择联系人--%>
    <div class="bonceContainer bounce-blue" id="chooseCusContact" style="width: 450px">
        <div class="bonceHead">
            <span>选择客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" style="background:#f0f8ff ">
            <input type="hidden" id="target">
            <div class="p0">
                <p style='text-align:center;'>暂无客户数据，请通过新增添加</p>
            </div>
            <div class="p1">
                <p>以下为可供选择的客户联系人</p>
                <ul class="cusList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="lineBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">取消</span>
            <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseCusContactOk()">确定</button>
        </div>
    </div>
    <%--新增联系人--%>
    <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
        <div class="bonceHead">
            <span>新增客户联系人</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon linkUploadify" style="max-height:500px;overflow-y: auto;">
            <form id="newContectData">
                <p>
                    <span class="sale_ttl1">联系人标签：</span>
                    <span class="sale_gap" id="contactFlag">收货人</span>
                </p>
                <p>
                    <span class="sale_ttl1">姓名：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                </p>
                <p>
                    <span class="sale_ttl1">手机：</span>
                    <span><input type="text" placeholder="请录入" id="contactNumber" require/></span>
                    <a class="sale_ttl1 addMore" onclick="addMore($(this))">添加更多联系方式</a>
                    <select style="display: none;width: 186px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                        <option value="0"></option>
                        <option value="1">手机</option>
                        <option value="2">QQ</option>
                        <option value="3">Email</option>
                        <option value="4">微信</option>
                        <option value="5">微博</option>
                        <option value="9">自定义</option>
                    </select>
                </p>
                <ul class="otherContact">
                </ul>
                <div id="contactsCard">
                    <span class="sale_ttl1">名片：</span>
                    <div id="uploadCard" class="cardUploadBtn"></div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big funBtn" id="addContact" data-fun="addContactOk">提交</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--新增收货信息--%>
    <div class="bonceContainer bounce-blue" id="newReceiveInfo" style="width: 450px">
        <div class="bonceHead">
            <span>收货信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1"><i class="xing"></i>收货地址</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="ReceiveAddress" require/></span>
            </p>
            <p>
                <span class="sale_ttl1"><i class="xing"></i>联系人</span>
                <span class="sale_con1 chooseCusCon" data-target="#ReceiveName">
                    <input type="text" readonly placeholder="请选择" id="ReceiveName" require/>
                    <span class="hd"></span>
                    <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                </span>
                <span class="linkBtn" onclick="addContactInfo(2)">新增</span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big funBtn"  data-source="1" data-fun="addressAddSure">提交</button>
        </div>
    </div>
    <%--新增到货区域--%>
    <div class="bonceContainer bounce-green" id="newReceiveAreaInfo" style="width: 750px">
        <div class="bonceHead">
            <span>新增到货区域</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 180px;">
            <div class="areaForm">
                <div class="regionMode">
                    <span class="sale_ttl1"><span class="ty-color-red">*</span>货物需到达的城市或地区</span>
                    <div class="sale_con1 regionText" onclick="regionCheck()">
                        <input type="text" placeholder="请选择" id="regionCon" require />
                        <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                        <span class="hd"></span>
                    </div>
                    <div class="regionBox">
                        <ul class="region-tab"></ul>
                        <div class="region-content">
                            <ul data-level="1"></ul>
                        </div>
                    </div>
                </div>
                <p>
                    <span class="sale_ttl1">对需到达地点的特殊要求</span>
                    <span class="sale_con1 clearVal">
                    <input type="text" placeholder="请录入" id="requirements"/>
                    <i class="clearInputVal">X</i>
                </span>
                </p>
                <p>
                    <span class="sale_ttl1"><span class="ty-color-red">*</span>联系人</span>
                    <span class="sale_con1 chooseCusCon" data-target="#areaName">
                <input type="text" readonly placeholder="请选择" id="areaName" require/>
                <span class="hd"></span>
                <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
            </span>
                    <span class="linkBtn" onclick="addContactInfo(4)">新增</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big funBtn" data-source="2" data-fun="addressAddSure">提交</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--新增、修改、续约 合同--%>
    <div class="bonceContainer bounce-green" id="newContractInfo" style="width: 500px">
        <div class="bonceHead">
            <span>新增合同</span>
            <a class="bounce_close" onclick="editContractOk(0)"></a>
        </div>
        <div class="bonceCon linkUploadify">
            <div class="bounceMainCon">
                <div class="flex-box cusItem">
                    <div class="citem" style="flex: auto">
                        <p>供应商名称</p>
                        <input type="text" name="customerName" disabled style="width: 100%">
                    </div>
                </div>
                <div class="flex-box">
                    <div class="citem">
                        <p><i class="red">*</i> 合同编号</p>
                        <input type="text" placeholder="请录入" class="cNo">
                    </div>
                    <div class="citem">
                        <p>签署日期</p>
                        <input type="text" placeholder="请选择" readonly class="cSignDate">
                    </div>
                </div>
                <div class="flex-box">
                    <div class="citem">
                        <p><i class="red">*</i> 合同的有效期</p>
                        <input type="text" placeholder="请选择" readonly class="cStartDate">
                    </div>
                    <div class="citem">
                        <p>&nbsp;</p>
                        <span>至</span>
                    </div>
                    <div class="citem">
                        <p>&nbsp;</p>
                        <input type="text" placeholder="请选择" readonly class="cEndDate">
                    </div>
                </div>
                <div class="citem2">
                    <p>合同的扫描件或照片(共可上传9张) <span class="linkBtn ty-right" id="cUpload1"></span></p>
                    <div class="fileCon">
                        <div class="fileCon1"></div>
                        <div class="hd deleteFile"></div>
                        <div style="clear: both"></div>
                    </div>
                </div>
                <div class="citem2">
                    <p>合同的可编辑版 <span class="linkBtn ty-right" id="cUpload2"></span></p>
                    <div class="fileCon ">
                        <div class="fileCon2"></div>
                        <div style="clear: both"></div>
                    </div>
                </div>
                <div class="citem2 cGST">
                    <div class="row-flex">
                        本合同下的材料 <span data-fun="scanTyGs" type="btn" class="link-blue tyGoodNumber">0</span> 种
                        <div class="btn-group">
                            <span class="link-blue" type="btn" data-fun="removeTyGs">移出材料</span>
                            <span class="link-blue" type="btn" data-fun="addTyGs">添加材料</span>
                        </div>
                    </div>
                    <div class="fileCon" style=" background: #f0f0f0;">
                        <div class="tyGoodList" style="font-size: 13px"></div>
                    </div>
                </div>
                <div class="citem2">
                    <p>备注</p>
                    <input class="cMemo" type="text" style="width: 100%" placeholder="请录入">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="editContractOk(0)">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="editContractOk" data-name="editContractOk">提交</button>
        </div>
    </div>
    <%-- 新增税率  --%>
    <div class="bonceContainer bounce-green" id="addTaxRate">
        <div class="bonceHead">
            <span>新增税率</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center"><input type="text" placeholder=" 请输入税率" id="taxRateVal" onkeyup="clearNoNum(this)" /></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addTaxRatetOk()">确定</span>
        </div>
    </div>
        <%-- 管理交货地点--%>
        <div class="bonceContainer bounce-blue" id="chooseDelivery" style="width: 800px">
            <div class="bonceHead">
                <span>管理交货地点</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon" style="background:#f0f8ff ">
                <div class="wideBody">
                    <div class="flexBox padd">
                        <span>系统内可供选择的收货地址如下，可多选</span>
                        <div class="linkBtn funBtn funTtl" data-fun="addAddress" data-type="new">新增收货地址</div>
                    </div>
                    <div class="blueCare">
                        <div class="care1">注：向未勾选的收获地址发货，录入采购订单时需录入新的价格，系统将给予提示。</div>
                        <div class="care2">注：向未勾选的到货区域发货，录入采购订单时需录入新的价格，系统将给予提示。</div>
                    </div>
                    <div>
                        <div class="hd receiveAddress"></div>
                        <ul class="placeList">
                            <li>
                                <i class="fa fa-circle-o"></i>
                                <span>姓名</span>
                                <span class="hd"></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseDeliveryOk()">确定</button>
            </div>
        </div>
</div>
<div class="bounce">
    <input type="hidden" id="isEdit">
    <%-- 新增合同 --%>
    <%--<div class="bonceContainer bounce-green" id="newcontract" style="margin-top: 20px;">
        <div class="bonceHead">
            <span>新增合同</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon linkUploadify" id="newconbox" style="max-height: 445px;">
            <div class="flex-box" style="display: flex;justify-content: space-between;flex-wrap: wrap;">
                <div class="citem" style="margin-bottom: 12px;">
                    <p><i class="red">*</i>合同编号</p>
                    <input type="text" placeholder="请录入" class="cNo" autocomplete="off" id="connum">
                </div>
                <div class="citem" style="margin-bottom: 12px;">
                    <p>签署日期</p>
                    <input type="text" placeholder="请选择" readonly class="cSignDate"
                           onclick="signingtime()" autocomplete="off" id="wrtdate">
                </div>
            </div>
            <div class="flex-box" style="display: flex;justify-content: space-between;flex-wrap: wrap;">
                <div class="citem" style="margin-bottom: 12px;">
                    <p>合同的有效期</p>
                    <input type="text" placeholder="请选择" readonly class="cStartDate" autocomplete="off">
                </div>
                <div class="citem">
                    <p>&nbsp;</p>
                    <span>至</span>
                </div>
                <div class="citem">
                    <p>&nbsp;</p>
                    <input type="text" placeholder="请选择" readonly class="cEndDate" autocomplete="off">
                </div>
            </div>
            <div class="citem2">
                <p>合同的扫描件或照片(共可上传9张)
                    <span class="linkBtn ty-right" id="cUpload1">
&lt;%&ndash;                        <a id="file_upload_1-button" class="uploadify-button"&ndash;%&gt;
&lt;%&ndash;                           target="_self" href="javascript:void(0)">上传</a>&ndash;%&gt;
                    </span>
                </p>
                <div class="fileCon">
                    <div class="fileCon1" id="fileCon1-1"></div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div class="citem2">
                <p>合同的可编辑版
                    <span class="linkBtn ty-right" id="cUpload2">
&lt;%&ndash;                        <a id="file_upload_2-button" class="uploadify-button"&ndash;%&gt;
&lt;%&ndash;                           target="_self" href="javascript:void(0)">上传</a>&ndash;%&gt;
                    </span>
                </p>
                <div class="fileCon">
                    <div class="fileCon2" id="fileCon2-1"></div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div class="citem2 cGS">
                <p>本合同下的材料 <span data-fun="scanGs" class="linkBtn scanGs btnCat" id="contdown" style="padding: 0 10px;" onclick="contentall()">XXX</span> 种
                    <span class="linkBtn ty-right " data-fun="addGs" id="addmpst1" onclick="addmaterial()">添加材料</span>
                    <span class="linkBtn ty-right " style="position: relative;left: -5px;"  id="movepost1" data-fun="removeGs" onclick="rmveconter()">移出材料</span>
                </p>
                <div class="fileCon" onclick="contentall()" style=" background: #eee;margin-bottom: 10px;border: 1px solid #ddd;
                    height: 30px;border-radius: 2px;line-height: 30px;padding: 0 9px;">
                    <div class="goodList" id="goodLust" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        &lt;%&ndash;                        <span class="gsIm"></span>&ndash;%&gt;
                    </div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div class="citem2">
                <p>备注</p>
                <input class="cMemo" type="text" style="width:420px; " placeholder="请录入" autocomplete="off">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="cgaddctact(0)" id="q2">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="cgaddctact3(0)" id="q3">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="cgaddcontact" onclick="cgaddctact(1)">提交</button>
            <button class="ty-btn ty-btn-green ty-btn-big" id="cgaddcontact3" onclick="cgaddctact3(1)">提交</button>
        </div>
    </div>--%>
    <%--  查看定点信息 --%>
    <div id="fixedScan" class="bonceContainer bounce-blue" style="width: 980px;">
        <div class="bonceHead">
            <span>查看定点信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <div class="col-md-8"> <span class="say0"></span></div>
                <div class="col-md-4"> <span class="btnCat">材料基本信息的修改记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-2">&nbsp; </div>
                <div class="col-md-6"><span class="item-title">创建人</span><span id="s_create">张三丰给 2015-15-15 14：12：12</span></div>
                <div class="col-md-4"><span class="btnCat">材料暂停/恢复采购的操作记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">材料名称</span><span id="s_name"></span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span id="s_code"></span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span id="s_unit"></span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">规格</span><span id="s_specifications">sdskdsds</span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span id="s_model">sdskdsds</span></div>
            </div>
            <div class="item scanExpShow">
                <div class="col-md-12"><span class="scanMt_expStr"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">备注</span><span id="s_memo">sdfsadsa</span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">材料类别</span>
                    <span id="s_cat"></span>
                </div>
            </div>
            <div class="clr"></div>
            <div class="nextCon threeCon">
                <div class="item">
                    <div class="col-md-12">
                        <span class="item-title">供应商名称<span class="red">*</span></span>
                        <span id="supplier2" >供应商名称</span>
                    </div>
                </div>
                <div class="item">
                    <div class="col-md-6"><span class="item-title">供应商简称</span> <span id="supplier3">供应商简称</span></div>
                    <div class="col-md-6"><span class="item-title">供应商代号</span><span id="g_code">供应商代号</span></div>
                </div>
                <div class="clr"></div>
                <div class="scan " style="padding:20px 0; line-height:30px; border-bottom:1px solid #ddd; ">
                    <div class="ctrlGp">
                        <span id="ceateInfo"></span>
                        <span data-type="stopPurchase" class="start btnCat ty-right">暂停从该供应商处采购本材料</span>
                        <span data-type="startPurchase" class="stop btnCat ty-right">恢复从该供应商处采购本材料</span>
                    </div>
                    <div class="ctrlGp">
                        <span>如需修改从该供应商处采购本材料的有关信息，可点击“修改”。</span>
                        <span class="btnCat ty-color-gray ty-right" style="color:#ccc;">修改记录</span>
                        <span data-type="editSupMt" data-tbid = "42" class="start btnCat ty-right" id="updunter">修改</span>
                    </div>
                </div>
                <%--<div class="supInfo supInfoTip leMar" id="sup2" >--%>
                <%--</div>--%>
                <div>
                    <div id="supInfo2" class="supInfoTip" style="margin: 5px 10px; ">
                        <p>采购合同元素</p>
                        <p>开票能力元素，挂账元素，汇票元素。</p>
                    </div>
                    <div class="scan">
                        <span id="priceTtl"> </span>
                        <span class="widthAuto" id="priceCon"></span>
                    </div>
                    <div class="supInfoTip scan" id="mtTip_s" style="margin: 5px 10px; ">
                        <p id="contractun"></p>
                        <p>本材料与采购合同的关系元素</p>
                        <p> 包装方式元素，最低采购量元素，采购周期元素。</p>
                        <p>价格稳定元素，开票元素，预付款元素。</p>
                    </div>
                    <div class="edit editTip">
                        <p class="ty-color-orange">修改说明：</p>
                        <div>
                            <span class="ty-color-orange">此处的修改仅能对以后所发的采购订单生效，对已存在于系统中的采购订单无法生效！</span><br>
                            系统中包含本材料本供应商的未完结采购订单共 <span class="ordersNum"></span> 个。<br>
                            <span class="ty-color-orange">如需修改未完结采购订单中的材料信息，可通过修改完材料信息后“终止”相应的订单，之后再录入新订单的方式。</span>
                        </div>
                    </div>


                    <div class="edit">
                        <input type="hidden" id="edit_supplier">
                        <div class="bor3">
                            <p class="supInfo leMar containThis" id="contants">
                                采购合同中包含本材料吗？ <input type="hidden" id="edit_containThis">
                                <span onclick="containThis(1 , $(this))" class="radioCon">是 <i id="edit_containThis1" class="fa fa-circle-o"></i></span>
                                <span onclick="containThis(0 , $(this))" class="radioCon">否 <i id="edit_containThis0" class="fa fa-circle-o"></i></span>
                            </p>
                            <p class="supInfo leMar containThis" id="contread">
                                此材料是否包含于与供应商已签订的合同中？ <input type="hidden" id="edit_urgnistad">
                                <span onclick="urgnistad(1 , $(this))" class="radioCon">是 <i id="urgnistad1" class="fa fa-circle-o"></i></span>
                                <span onclick="urgnistad(0 , $(this))" class="radioCon">否 <i id="urgnistad0" class="fa fa-circle-o"></i></span>
                            </p>
                            <%--手动新加的--%>
                            <p class="leMar stable contrInfo">
                                <span class="btnCat" style="display: block;margin-left: 208px;" onclick="newContract($(this))" id="addcontrot1">新增合同</span><input type="hidden" id="materialcontract1">
                                <span class="wenzi">本材料在哪个合同中</span>
                                <select id="contract1"></select>
                            </p>
                            <p class="supInfo leMar">
                                该供应商供应的本材料价格是否稳定？ <input type="hidden" id="edit_isStable">
                                <span onclick="isStable(1 , $(this))" class="radioCon">相对稳定 <i id="edit_isStable1" class="fa fa-circle-o"></i></span>
                                <span onclick="isStable(2 , $(this))" class="radioCon">变动较频繁 <i id="edit_isStable2" class="fa fa-circle-o"></i></span>
                            </p>
                            <p class="leMar stable canInvoice">
                                购买本材料是否给开发票？ <input type="hidden" id="edit_canInvoice">
                                <span onclick="canInvoice(1 , $(this))" class="radioCon">是 <i id="edit_canInvoice1" class="fa fa-circle-o"></i></span>
                                <span onclick="canInvoice(0 , $(this))" class="radioCon">否 <i id="edit_canInvoice0" class="fa fa-circle-o"></i></span>
                                <span class="canInvoice2 radioCon" onclick="canInvoice(2 , $(this))">不确定 <i id="edit_canInvoice2" class="fa fa-circle-o"></i></span>
                            </p>
                            <p class="leMar stable incoiceType">
                                购买本材料给开何种发票？ <input type="hidden" id="edit_incoiceType">
                                <span onclick="incoiceType(1 , $(this))" class="radioCon" id="edit_incoiceType1Con">增值税专用发票 <i id="edit_incoiceType1" class="fa fa-circle-o"></i></span>
                                <span onclick="incoiceType(2 , $(this))" class="radioCon">其他发票 <i id="edit_incoiceType2" class="fa fa-circle-o"></i></span>
                                <span class="incoiceType4" onclick="incoiceType(4 , $(this))" class="radioCon">不给开票 <i id="edit_incoiceType4" class="fa fa-circle-o"></i></span>
                            </p>
                            <p class="leMar stable priceInfo" ><input type="hidden" id="edit_isParValue">
                                <span class="type1">已约定的不开票单价</span>
                                <span class="type2" style="width: auto">
                                    <span class="type3">参考单价</span><input type="hidden" id="edit_referPrice">
                                    <span onclick="referPrice(1 , $(this))" class="radioCon">含税 <i id="edit_referPrice1" class="fa fa-circle-o"></i></span>
                                    <span onclick="referPrice(0 , $(this))" class="radioCon">不含税 <i id="edit_referPrice0" class="fa fa-circle-o"></i></span>
                                </span>
                                <input type="text" id="edit_price"/>元/<span style="width: auto;" class="purUnit"></span>
                                <i class="taxWrap type2"><span>税率</span>
                                    <span class="taxRateBody">
                              <input type="hidden" id="edit_taxRateId" />
                              <input type="text" id="edit_taxRate" readonly onclick="taxRateFun($(this))">
                            <span class="taxRateList">
                               <span><span>10%</span><span>删除</span></span>
                            </span>
                            </span>
                                    <span class="linkBtn funBtn" data-fun="addTaxRate">新增</span></i>
                            </p>
                            <p class="leMar stable edit_acceptBills">
                                供应商是否可接受汇票？ <input type="hidden" id="edit_acceptBills">
                                <span onclick="acceptBills(1 , $(this))" class="radioCon">可接受 <i id="edit_acceptBills1" class="fa fa-circle-o"></i></span>
                                <span onclick="acceptBills(2 , $(this))" class="radioCon">不确定 <i id="edit_acceptBills2" class="fa fa-circle-o"></i></span>
                            </p>
                            <div class="stable priceInfo"><p class="leMar">
                                <span>该价格是否含运费？<span class="red">*</span></span> <input type="hidden" id="edit_containYunFee">
                                <span onclick="containYunFee(1 , $(this))" class="radioCon"><i id="edit_containYunFee1" class="fa fa-circle-o"></i> 为送货上门价格，含所有运费</span></p>
                                <div class="manageAddress">
                                    <div class="flexBox">送货至以下地点时，运费需由供应商承担<span class="linkBtn funBtn" data-fun="manageAddress">管理交货地点</span></div>
                                    <div class="manageAddressList"></div>
                                    <div class="hd"></div>
                                </div>
                            </div>
                            <div class="stable priceInfo"><p class="leMar">
                                <span></span>
                                <span onclick="containYunFee(2 , $(this))" class="radioCon"><i id="edit_containYunFee2" class="fa fa-circle-o"></i> 含长途运输费用，配送至某城市或地区</span></p>
                                <div class="manageAddress">
                                    <div class="flexBox">配送至以下区域时，运费需由供应商承担<span class="linkBtn funBtn" data-fun="manageAddress">管理交货地点</span></div>
                                    <div class="manageAreaList"></div>
                                    <div class="hd"></div>
                                </div>
                            </div>
                            <p class="leMar stable priceInfo">
                                <span></span>
                                <span onclick="containYunFee(3 , $(this))" class="radioCon"><i id="edit_containYunFee3" class="fa fa-circle-o"></i> 为离厂价格，不包含任何运费</span>
                            </p>
                            <p class="leMar supInfo ">
                                所供应材料的包装方式 <input type="hidden" id="edit_packgeType">
                                <span onclick="packgeType(1 , $(this))" class="radioCon">基本固定 <i id="edit_packgeType1" class="fa fa-circle-o"></i></span>
                                <span onclick="packgeType(2 , $(this))" class="radioCon">型式不定 <i id="edit_packgeType2" class="fa fa-circle-o"></i></span>
                            </p>
                            <p class="supInfo ">
                                <span style="width:100px; ">采购周期</span><input style="width:150px;" type="text" id="edit_purTurn"/><span style="width: 50px;">天</span>
                                <span style="width:96px; ">最低采购量</span><input style="width:150px;" type="text" id="edit_minPur"/><span style="margin-left:-25px; " class="purUnit"></span>
                                <span style="width:77px; ">最低库存</span><input style="width:150px;" type="text" id="edit_minStorage"/><span style="margin-left:-25px; " class="purUnit"></span>
                                <br><span class="tipBlue">注：下单后需多少天可入库</span>
                            </p>
                        </div>

                    </div>

                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="edit ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="edit ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="editMtSupOk() ">确定</span>
            <span class="scan ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>
    <%--  添加材料的定点供应商 --%>
    <div id="addMtSup" class="bonceContainer bounce-green">
        <div class="bonceHead">
            <span>添加材料的定点供应商</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <input type="hidden" id="tdID">
            <div class="item">
                <div class="col-md-8">
                    <span class="say1">本材料录入时为“曾采购过的材料”。</span>
                    <span class="say0">本材料录入时为“未采购过的材料”</span> </div>
                <div class="col-md-4"> <span class="btnCat">材料基本信息的修改记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-2">&nbsp; </div>
                <div class="col-md-6"><span class="item-title">创建人</span><span class="addMtSup_create"></span></div>
                <div class="col-md-4"><span class="btnCat">材料暂停/恢复采购的操作记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">材料名称</span><span class="addMtSup_name"></span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span class="addMtSup_code"></span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span class="addMtSup_unit"></span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">规格</span><span class="addMtSup_specifications"></span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span class="addMtSup_model"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">备注</span><span class="addMtSup_memo"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">材料类别</span>
                    <p class="addMtSup_cat"></p>
                </div>
            </div>
            <div class="clr">

            </div>
            <div class="nextCon threeCon">
                <div class="item">
                    <div class="col-md-12">
                        <span class="item-title">供应商名称<span class="red">*</span></span>
                        <select id="supplier" onchange="matchSupper($(this))"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="col-md-6"><span class="item-title">供应商简称</span> <input type="text" id="e_gName1" disabled/></div>
                    <div class="col-md-6"><span class="item-title">供应商代号</span><input type="text" id="e_gCode1" disabled /></div>
                </div>
                <div class="clr"></div>
                <p class="supInfo supInfoTip leMar" id="sup1" > </p>
                <div>
                    <%--                    <p class="supInfo leMar containThis">--%>
                    <%--                        采购合同中包含本材料吗？ <input type="hidden" id="containThis">--%>
                    <%--                        <span onclick="containThis(1 , $(this))" class="radioCon">是 <i id="containThis1" class="fa fa-circle-o"></i></span>--%>
                    <%--                        <span onclick="containThis(0 , $(this))" class="radioCon">否 <i id="containThis0" class="fa fa-circle-o"></i></span>--%>
                    <%--                    </p>--%>
                    <p class="supInfo leMar containThis" id="contread">
                        此材料是否包含于与供应商已签订的合同中？ <input type="hidden" id="ontractsigned">
                        <span onclick="ontractsigned(1 , $(this))" class="radioCon">是 <i id="ontractsigned1" class="fa fa-circle-o"></i></span>
                        <span onclick="ontractsigned(0 , $(this))" class="radioCon">否 <i id="ontractsigned0" class="fa fa-circle-o"></i></span>
                    </p>
                    <%--手动新加的--%>
                    <p class="leMar stable contrInfo">
                        <span class="btnCat" style="display: block;margin-left: 208px;" onclick="newContract($(this))" id="addcontrot">新增合同</span><input type="hidden" id="materialcontract">
                        <span class="wenzi">本材料在哪个合同中</span>
                        <select id="contract"></select>
                    </p>
                    <div id="edgbox"></div>

                    <p class="supInfo leMar">
                        该供应商供应的本材料价格是否稳定？ <input type="hidden" id="isStable">
                        <span onclick="isStable(1 , $(this))" class="radioCon">相对稳定 <i id="isStable1" class="fa fa-circle-o"></i></span>
                        <span onclick="isStable(2 , $(this))" class="radioCon">变动较频繁 <i id="isStable2" class="fa fa-circle-o"></i></span>
                    </p>
                    <p class="leMar stable canInvoice">
                        购买本材料是否给开发票？ <input type="hidden" id="canInvoice">
                        <span onclick="canInvoice(1 , $(this))" class="radioCon">是 <i id="canInvoice1" class="fa fa-circle-o"></i></span>
                        <span onclick="canInvoice(0 , $(this))" class="radioCon">否 <i id="canInvoice0" class="fa fa-circle-o"></i></span>
                        <span class="canInvoice2 radioCon" onclick="canInvoice(2 , $(this))">不确定 <i id="canInvoice2" class="fa fa-circle-o"></i></span>
                    </p>
                    <p class="leMar stable incoiceType">
                        购买本材料给开何种发票？ <input type="hidden" id="incoiceType">
                        <span onclick="incoiceType(1 , $(this))" class="radioCon" id="incoiceType1Con">增值税专用发票 <i id="incoiceType1" class="fa fa-circle-o"></i></span>
                        <span onclick="incoiceType(2 , $(this))" class="radioCon">其他发票 <i id="incoiceType2" class="fa fa-circle-o"></i></span>
                        <span class="incoiceType4" onclick="incoiceType(4 , $(this))" class="radioCon">不给开票 <i id="incoiceType4" class="fa fa-circle-o"></i></span>
                    </p>
                    <p class="leMar stable priceInfo" ><input type="hidden" id="isParValue">
                        <span class="type1">已约定的不开票单价</span>
                        <span class="type2" style="width: auto">
                        <span class="type3">参考单价</span><input type="hidden" id="referPrice">
                        <span onclick="referPrice(1 , $(this))" class="radioCon">含税 <i id="referPrice1" class="fa fa-circle-o"></i></span>
                        <span onclick="referPrice(0 , $(this))" class="radioCon">不含税 <i id="referPrice0" class="fa fa-circle-o"></i></span>
                    </span>
                        <input type="text" id="price"/>元/<span style="width: auto;" class="purUnit"></span>
                        <i class="taxWrap type2">
                            <span>税率</span>
                            <span class="taxRateBody">
                              <input type="hidden" id="taxRateId" />
                              <input type="text" id="taxRate" readonly onclick="taxRateFun($(this))">
                            <span class="taxRateList">
                               <span><span>10%</span><span>删除</span></span>
                            </span>
                            </span>
                        <span class="linkBtn funBtn" data-fun="addTaxRate">新增</span></i>
                    </p>
                        <p class="leMar stable acceptBills">
                            供应商是否可接受汇票？ <input type="hidden" id="acceptBills">
                            <span onclick="acceptBills(1 , $(this))" class="radioCon">可接受 <i id="acceptBills1" class="fa fa-circle-o"></i></span>
                            <span onclick="acceptBills(2 , $(this))" class="radioCon">不确定 <i id="acceptBills2" class="fa fa-circle-o"></i></span>
                        </p>
                    <div class="priceInfo stable">
                        <p class="leMar">
                            <span>该价格是否含运费？<span class="red">*</span></span> <input type="hidden" id="containYunFee">
                            <span onclick="containYunFee(1 , $(this))" class="radioCon"><i id="containYunFee1" class="fa fa-circle-o"></i> 为送货上门价格，含所有运费</span>
                        </p>
                        <div class="manageAddress">
                            <div class="flexBox">送货至以下地点时，运费需由供应商承担<span class="linkBtn funBtn" data-fun="manageAddress">管理交货地点</span></div>
                            <div class="manageAddressList"></div>
                            <div class="hd"></div>
                        </div>
                    </div>
                    <div class="priceInfo stable">
                        <p class="leMar"><span></span>
                            <span onclick="containYunFee(2 , $(this))" class="radioCon"><i id="containYunFee2" class="fa fa-circle-o"></i> 含长途运输费用，配送至某城市或地区</span></p>
                        <div class="manageAddress">
                            <div class="flexBox">配送至以下区域时，运费需由供应商承担<span class="linkBtn funBtn" data-fun="manageAddress">管理交货地点</span></div>
                            <div class="manageAreaList"></div>
                            <div class="hd"></div>
                        </div>
                    </div>
                    <p class="leMar stable priceInfo">
                        <span></span>
                        <span onclick="containYunFee(3 , $(this))" class="radioCon"><i id="containYunFee3" class="fa fa-circle-o"></i> 为离厂价格，不包含任何运费</span>
                    </p>
                    <p class="leMar supInfo ">
                        所供应材料的包装方式 <input type="hidden" id="packgeType">
                        <span onclick="packgeType(1 , $(this))" class="radioCon">基本固定 <i id="packgeType1" class="fa fa-circle-o"></i></span>
                        <span onclick="packgeType(2 , $(this))" class="radioCon">型式不定 <i id="packgeType2" class="fa fa-circle-o"></i></span>
                    </p>
                    <p class="expInfo" style="padding-left: 50px;">
                        该供应商供应本材料的保质期，为自生产日期之后多久？
                         <input id="expDate" class="form-control" style="width: 160px;margin-left: 50px; margin-right: 10px; " type="text" placeholder="请录入数字"/>天
                    </p>
                    <p class="supInfo ">
                        <span style="width:100px; ">采购周期</span><input type="text" id="purTurn" onkeyup="clearNoNum1(this)"/><span style="margin-left:-20px; width: 55px;" >天</span>
                        <span>最低采购量</span><input type="text" id="minPur" onkeyup="clearNoNum1(this)"/><span style="margin-left:-20px; " class="purUnit"></span>
                        <span>最低库存</span><input type="text" id="minStorage" onkeyup="clearNoNum1(this)"/><span style="margin-left:-20px; " class="purUnit"></span>
                        <br><span class="tipBlue">注：下单后需多少天可入库</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addMtSupOk(); ">确定</span>
        </div>
    </div>
    <%-- 向系统添加新供应商 --%>
    <div class="bonceContainer bounce-green" id="addSupply" >
        <div class="bonceHead">
            <span id="supConTtl">新增供应商</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <input type="hidden" id="sup_type" />
            <input type="hidden" id="e_gId"  /> <%-- 供应商ID--%>
            <input type="hidden" id="e_gSbid"  /> <%-- 供应关系id --%>
            <div class="ty-left threeCon">
                <%-- <p> &lt;%&ndash;第一行&ndash;%&gt;
                     <span>材料名称</span><input type="text" disabled id="e_gMtName"/>
                     <span>材料代号</span><input type="text" disabled id="e_gMtCode"/>
                     <span>计量单位</span><input type="text" disabled id="e_gMtUnit"/>
                 </p>--%>
                <p>
                    <span> 供应商名称<i class="xing">*</i></span>
                    <span class="e_supContainer">
                        <input type="text" id="e_gName" style="width: 550px;" onchange="setGname2($(this))"  />
                    </span>

                </p>
                <p>
                    <span> 供应商简称<i class="xing">*</i></span>
                    <span class="e_supContainer">
                    <input type="text" id="e_gName2"/>
                </span>
                    <span>供应商代号<i class="xing">*</i></span><input type="text" id="e_gCode"  />
                </p>
                <%--第3行--%>
                <%--                <p class="leMar">--%>
                <%--                    是否已与其签订采购合同？ <input type="hidden" id="haveContract">--%>
                <%--                    <span onclick="haveContract(1 , $(this))" class="radioCon">是 <i id="haveContract1" class="fa fa-circle-o"></i></span>--%>
                <%--                    <span onclick="haveContract(0 , $(this))" class="radioCon">否 <i id="haveContract0" class="fa fa-circle-o"></i></span>--%>
                <%--                </p>--%>
                <%--第4行 ， 全部非必填项--%>
                <p class="hang4">
                    <span>合同编号</span><input type="text" id="e_gCompactNo" />
                    <span>有效期至</span><input type="text" id="e_gCompactExpire" />
                    <span>签署日期</span><input type="text" id="e_gCompactSignDay" />
                </p>
                <%--第5行--%>
               <%-- <p class="leMar noNext">
                    该供应商是否能开发票？ <input type="hidden" id="haveInvoice">
                    <span onclick="haveInvoice(1 , $(this))" class="radioCon">是 <i id="haveInvoice1" class="fa fa-circle-o"></i></span>
                    <span onclick="haveInvoice(0 , $(this))" class="radioCon">否 <i id="haveInvoice0" class="fa fa-circle-o"></i></span>
                </p>
                &lt;%&ndash;第6行&ndash;%&gt;
                <p class="leMar noNext hang6">
                    是否能开增值税专用发票？ <input type="hidden" id="vatInvoice">
                    <span onclick="vatInvoice(1, $(this))" class="radioCon">是 <i id="vatInvoice1" class="fa fa-circle-o"></i></span>
                    <span onclick="vatInvoice(2, $(this))" class="radioCon">否 <i id="vatInvoice2" class="fa fa-circle-o"></i></span>
                    <span class="hang_6" style="width: auto;">
                    <span class="litT">请录入税率</span> <input type="text" id="e_gRate0" onkeyup="clearNoNum(this)">%
                </span>
                </p>--%>
                <%--第7行--%>
                <p class="leMar">
                    是否接受挂账？ <input type="hidden" id="setHangAccount">
                    <span onclick="setHangAccount(1 , $(this))" class="radioCon">可接受 <i id="setHangAccount1" class="fa fa-circle-o"></i></span>
                    <span onclick="setHangAccount(0 , $(this))" class="radioCon">不接受 <i id="setHangAccount0" class="fa fa-circle-o"></i></span>
                    <span class="hang_7" style="width: auto;" >
                    <span>请录入已约定的账期</span> <input type="text" id="hangDays" onkeyup="clearNoNum(this)" />天

                </span>
                </p>
                <%--第7-1行--%>
                <p class="leMar hang7_1">
                    请选择从何时开始计算账期？  <input type="hidden" id="setStartDate">
                    <span onclick="setStartDate(1 , $(this))" class="radioCon" id="setStartDate1Con">自货入库之日起 <i id="setStartDate1" class="fa fa-circle-o"></i></span>
                    <span onclick="setStartDate(2 , $(this))" class="radioCon" id="setStartDate2Con">自发票入账之日起 <i id="setStartDate2" class="fa fa-circle-o"></i></span>
                </p>
                <%--第8行--%>
                <%--<p class="leMar hang8">
                    是否可接受汇票？ <input type="hidden" id="hui">
                    <span onclick="hui(1 , $(this))" class="radioCon">可接受 <i id="hui1" class="fa fa-circle-o"></i></span>
                    <span onclick="hui(2 , $(this))" class="radioCon">不确定 <i id="hui2" class="fa fa-circle-o"></i></span>
                </p>--%>
                <%--第16行--%>
                <p class="leMar hang16">
                    是否需要预付款？ <input type="hidden" id="setAdvanceFee">
                    <span onclick="setAdvanceFee(1, $(this))" class="radioCon">需要 <i id="setAdvanceFee1" class="fa fa-circle-o"></i></span>
                    <span onclick="setAdvanceFee(2, $(this))" class="radioCon">不需要 <i id="setAdvanceFee2" class="fa fa-circle-o"></i></span>
                    <span id="relative0_2" onclick="setAdvanceFee(0, $(this))" class="radioCon">不确定 <i id="setAdvanceFee0" class="fa fa-circle-o"></i></span>
                </p>
                <!--手动添加的-->
                <p class="leMar" id="paymore">
                    <span class="hang_17" style="width: 367px;text-align: left;">
                        <span>请录入需预付的比例</span>
                        <!--onclick="uncertainty(0,$(this))"-->
                        <input type="text" id="e_gRate1" onkeyup="clearNoNum2(this)" onblur="clear0($(this))" onfocus="outover($(this))" style="margin-left: 10px;">%
                    </span>
                    <input type="hidden" id="uncertainty">
                    <span id="chooseunde" onclick="uncertainty(1,$(this))" class="radioCon" style="display: inline-block">
                        比例不确定 <i id="uncertainty1" class="fa fa-circle-o"></i>
                    </span>
                    </input>
                </p>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-left" style="color:red;" id="supTip"></span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="saveNewSupply()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--  初始库存 --%>
    <div class="bonceContainer bounce-blue" id="initStock">
        <div class="bonceHead">
            <span>初始库存</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p><span>当前数据</span><span id="curNum"></span></p>
            <table class="ty-table ty-table-control"  >
                <thead>
                <tr>
                    <td>修改人</td>
                    <td>修改时间</td>
                    <td>供应商名称</td>
                    <td>修改前的初始库存</td>
                    <td>修改后的初始库存</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>修改人</td>
                    <td>修改时间</td>
                    <td>供应商名称</td>
                    <td>修改前的初始库存</td>
                    <td>修改后的初始库存</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>
    <%--占用库位查看--%>
    <div class="bonceContainer bounce-blue" id="holdStockSee" style="width:1000px;">
        <div class="bonceHead">
            <span>占用库位查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-none bg-yellow" id="holdStockInfo">
                    <thead>
                    <tr>
                        <td width="15%">材料名称</td>
                        <td width="15%">材料代号</td>
                        <td width="10%">型号</td>
                        <td width="10%">规格</td>
                        <td width="20%">创建人</td>
                        <td width="10%">计量单位</td>
                        <td width="10%">当前库存</td>
                        <td width="10%">占用库位</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div id="onlyNumInfo" >该材料未选择库位</div>
                <div id="currentStation" class="resetCurrentStation"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--  本材料无需定点采购 --%>
    <div id="noFixedPurchase" class="bonceContainer bounce-red">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-center">确定本材料无需定点采购？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="noFixedPurchase()">确定</span>
        </div>
    </div>
    <%--  暂停采购 --%>
    <div id="stopPurchase" class="bonceContainer bounce-blue">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-center">操作成功后，本材料再下单采购时将无法选择此供应商。</p>
            <p class="ty-center"> 确定暂停向该供应商采购该材料？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="editPurchase(0)">确定</span>
        </div>
    </div>
    <%--  恢复采购 --%>
    <div id="startPurchase" class="bonceContainer bounce-red">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-center">操作成功后，本材料再下单采购时将可再次选择此供应商。</p>
            <p class="ty-center"> 确定恢复向该供应商采购该材料？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="editPurchase(1)">确定</span>
        </div>
    </div>
    <%--  全部恢复采购 --%>
    <div id="startPurchase2" class="bonceContainer bounce-red">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-center">操作成功后，再下单采购时将可选择该材料。</p>
            <p class="ty-center"> 确定恢复采购该材料？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="editPurchase(1,1)">确定</span>
        </div>
    </div>
    <%--  暂停采购 2 全部的 --%>
    <div id="stopPurchase2" class="bonceContainer bounce-blue">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-center">操作成功后，再下采购订单时将无法选择该材料。</p>
            <p class="ty-center">确定暂停采购该材料？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="editPurchase(0 , 1)">确定</span>
        </div>
    </div>
    <%--  恢复采购 --%>
    <div id="startPurchase" class="bonceContainer bounce-red">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-center">操作成功后，本材料再下单采购时将可再次选择此供应商。</p>
            <p class="ty-center"> 确定恢复向该供应商采购该材料？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="editPurchase(1)">确定</span>
        </div>
    </div>
    <%-- 材料查看 --%>
    <div class="bonceContainer bounce-blue" id="mtScan">
        <div class="bonceHead">
            <span>材料查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <div class="col-md-12">
                    <span class="scanMt_state">本材料录入时为“曾采购过的材料”。</span>
                    <span class="hd"> 本材料来自于产品的拆分（包装）。</span>
                    <span class="hd"> XX已于XXXX-XX-XX XX:XX:XX将本材料“暂停采购”！</span>
                </div>
                <%--<div class="col-md-3"><span class="btnCat" id="editMtBtn" onclick="editMtBtn()">修改材料的基本信息</span></div>--%>
                <%--<div class="col-md-3"><span class="disabled btnCat">材料基本信息的修改记录</span></div>--%>
            </div>
            <div class="item">
                <div class="col-md-2">&nbsp; </div>
                <div class="col-md-6"><span class="item-title">创建人</span><span class="scanMt_create">张三丰给 2015-15-15 14：12：12</span></div>
                <div class="col-md-4">&nbsp; </div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">材料名称</span><span class="scanMt_name">sdsdsfsdsdf</span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span class="scanMt_code">sdsdsfsdsdf</span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span class="scanMt_unit">sdsdsfsdsdf</span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">规格</span><span class="scanMt_specifications">sdskdsds</span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span class="scanMt_model">sdskdsds</span></div>
            </div>
            <div class="item scanExpShow">
                <div class="col-md-12"><span class="scanMt_expStr"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">备注</span><span class="scanMt_memo">sdfsadsa</span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">材料类别</span>
                    <span class="scanMt_cat">sdsds>sdsds>sdsds</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%-- 本合同下的材料 --%>
    <div class="bonceContainer bounce-blue" id="contactbox"
         style="width: 676px;max-height: 400px;display: block;position: fixed;left: 596.5px;top: 84px;z-index: 1;">
        <div class="bonceHead">
            <span>本合同下的材料</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: none;">
            <div>
                <p id="contben">本合同下的材料共有以下<span>XX</span>种</p>
                <div>
                    <table class="ty-table ty-table-control" style="width: 592px; margin-left: 50px;" id="removematerial">
                        <tr style="background-color:#fff;">
                            <td width="25%">材料代号</td>
                            <td width="30%">材料名称</td>
                            <td width="15%">型号</td>
                            <td width="15%">规格</td>
                            <td width="15%">计量单位</td>
                        </tr>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big"
                  onclick="closedown()">关闭</span>
        </div>
    </div><%--
    &lt;%&ndash; 向本合同添加材料 &ndash;%&gt;
    <div class="bonceContainer bounce-blue" id="addcontract"
         style="width: 676px;max-height: 400px;display: block;position: fixed;left: 596.5px;top: 84px;z-index: 1;">
        <div class="bonceHead">
            <span>向本合同添加材料</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 384px;">
            <div>
                <p id="cGTp">可供选择的材料共有以下<span>XX</span>种</p>
                <div>
                    <table class="ty-table" style="width: 592px; margin-left: 50px;">
                        <tr style="background-color:#fff;">
                            <td class="before"></td>
                            <td width="25%">材料代号</td>
                            <td width="30%">材料名称</td>
                            <td width="15%">型号</td>
                            <td width="15%">规格</td>
                            <td width="15%">计量单位</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big"
                  onclick="backcontant($(this))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="Submitup" onclick="submget($(this))">确定</button>
        </div>
    </div>
    &lt;%&ndash; 从本合同移出材料 &ndash;%&gt;
    <div class="bonceContainer bounce-blue" id="remoecontact"
         style="width: 676px;max-height: 400px;display: block;position: fixed;left: 596.5px;top: 84px;z-index: 1;">
        <div class="bonceHead">
            <span>从本合同移出材料</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: none;">
            <div>
                <p id="cGTpc">可供选择的材料共有以下<span>XX</span>种</p>
                <div>
                    <table class="ty-table ty-table-control" style="width: 592px; margin-left: 50px;">
                        <tr style="background-color:#fff;">
                            <td></td>
                            <td width="25%">材料代号</td>
                            <td width="30%">材料名称</td>
                            <td width="15%">型号</td>
                            <td width="15%">规格</td>
                            <td width="15%">计量单位</td>
                        </tr>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big"
                  onclick="bounce.show($('#newcontract'))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="redelete" onclick="updunteContent($(this))">确定</button>
        </div>
    </div>--%>
    <%-- 未勾选完整提示弹窗 --%>
    <div class="bonceContainer bounc_blue" id="toolTipbox">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >
            <p>还有必填项尚未填写！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >
            <div id="contracto">
                <p>本材料已暂停从该供应商处采购。</p>
                <p>如需继续向其采购，请为其“恢复采购”！</p>
            </div>
            <div id="mustchoose">
                <p>还有必填项尚未填写！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); bounce.show($('#addSupply'))">我知道了</span>
        </div>
    </div>
</div>
<div class="bonceContainer bounce-red" id="choseall">
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>材料管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content"  >
            <!--放内容的地方-->
            <div class="ty-container">
                <%-- 首页 --%>
                <div class="mainCon mainCon1">
                    <div class="leCat">
                        <div>全部（<span class="mtNum"></span>种）</div>
                        <ul id="catAll">

                        </ul>
                        <ul id="bottom1" class="bottom" onclick="goPause()">
                            <li>暂停采购的材料  <span class="catNum stopNum"></span></li>
                        </ul>
                        <div id="bottom2" class="bottom">
                            <span class="btnCat" onclick="backPreCat(); ">返回上一级</span>
                            <span class="btnCat" onclick="backPreCat(1); ">返回全部材料</span>
                        </div>
                    </div>
                    <div class="hd" id="catTree"></div>
                    <div class="riMt">
                        <div>
                            <span>当前分类： <span class="curCatory curCat"></span></span>
                            <div class="lePad">
                                <p>
                                 <span>
                                现有<span id="count"></span>种需采购的材料需确认定点信息！
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="goConfirm(); ">去处理</span>
                                </span>
                                    <span class="search">查找：
                                <input type="text" placeholder="请输入原辅材料的代号或名称"><span onclick="search($(this),1)">确定</span>
                                </span>
                                </p>
                                <table class="ty-table ty-table-control"  >
                                    <thead>
                                    <tr>
                                        <td>材料名称</td>
                                        <td>材料代号</td>
                                        <td>型号</td>
                                        <td>规格</td>
                                        <td>创建人</td>
                                        <td>计量单位</td>
                                        <td>当前库存</td>
                                        <td>最低库存</td>
                                        <td>占用库位</td>
                                        <td>供应商</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>材料名称</td>
                                        <td>材料代号</td>
                                        <td>型号</td>
                                        <td>规格</td>
                                        <td>创建人</td>
                                        <td>计量单位</td>
                                        <td>当前库存</td>
                                        <td>最低库存</td>
                                        <td>占用库位</td>
                                        <td class="ty-td-control"><span data-type="sup" class="ty-color-blue">12个</span></td>
                                        <td>
                                            <span data-type="mtScan" class="ty-color-blue ">暂停采购</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <div id="ye1"></div>
                            </div>

                        </div>

                    </div>
                </div>
                <%-- 待确认定点信息列表 --%>
                <div class="mainCon mainCon2">
                    <div class="singepannel">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="goMainCon1()">返回材料管理主页 </span>
                        <div class="lePad">
                            <p>如下<span class="confirmNum"></span>种需采购的材料有待确认定点信息!</p>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>创建人</td>
                                    <td>计量单位</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>创建人</td>
                                    <td>计量单位</td>
                                    <td>
                                        <span data-type="fixedMassage" class="ty-color-blue ">确定定点信息</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div id="ye2"></div>
                        </div>

                    </div>
                </div>
                <%-- 确定定点信息 --%>
                <div class="mainCon mainCon3">
                    <div class="singepannel">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="goMainCon1(1)">返回材料管理主页 </span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="goConfirm(2)">返回上一页 </span>
                        <div>
                            <table class="ty-table ty-table-control mt31" style="width:70%; ">
                                <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>创建人</td>
                                    <td>计量单位</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td class="mt31_name">材料名称</td>
                                    <td class="mt31_code">材料代号</td>
                                    <td class="mt31_model">型号</td>
                                    <td class="mt31_specifications">规格</td>
                                    <td class="mt31_create">创建人</td>
                                    <td class="mt31_unit">计量单位</td>
                                    <td>
                                        <span class="hd"></span>
                                        <span class="ty-color-blue" onclick="mtScan()">查看</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="brCon">
                                <p class="ctrlGp">
                                    <span class="noPositionCreat">本材料现有如下0个定点供应商</span>
                                    <span class="ty-right">
                                        <span class="btnCat" data-tbid="32" data-type="addMtSup">添加本材料的定点供应商</span>
                                        <span class="btnCat" data-type="addNewSup">向系统添加新供应商</span>
                                        <span class="btnCat noFixedPurchaseBtn" data-type="noFixedPurchase">本材料无需定点采购</span>
                                    </span>
                                </p>
                                <table class="ty-table ty-table-control mt32">
                                    <thead>
                                    <tr>
                                        <td>供应商</td>
                                        <td>采购合同</td>
                                        <td>单价</td>
                                        <td>是否开票</td>
                                        <td>创建人</td>
                                        <td>采购周期</td>
                                        <td>当前库存</td>
                                        <td>最低库存</td>
                                        <td>初始库存</td>
                                        <td>占用库位</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>供应商</td>
                                        <td>采购合同</td>
                                        <td>单价</td>
                                        <td>是否开票</td>
                                        <td>创建人</td>
                                        <td>采购周期</td>
                                        <td>当前库存</td>
                                        <td>最低库存</td>
                                        <td>初始库存</td>
                                        <td>占用库位</td>
                                        <td>
                                            <span class="ty-color-blue">查看</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>
                </div>
                <%-- 供应商 --%>
                <div class="mainCon mainCon4">
                    <div class="singepannel">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="goMainCon1(1)">返回材料管理主页 </span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="hidePause4" onclick="hidePause4()">返回上一页 </span>
                        <div>
                            <table class="ty-table ty-table-control mt41"  style="width:85%; ">
                                <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>创建人</td>
                                    <td>计量单位</td>
                                    <td>当前库存</td>
                                    <td>最低库存</td>
                                    <td>初始库存</td>
                                    <td>占用库位</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td class="mt41_name">材料名称</td>
                                    <td class="mt41_code">材料代号</td>
                                    <td class="mt41_specifications">型号</td>
                                    <td class="mt41_model">规格</td>
                                    <td class="mt41_create">创建人</td>
                                    <td class="mt41_unit">计量单位</td>
                                    <td class="mt41_current_stock">当前库存</td>
                                    <td class="mt41_minimum_stock">最低库存</td>
                                    <td class="ty-td-control mt41_initial_stock"><span data-type="initStock" class="ty-color-blue">初始库存</span></td>
                                    <td class="ty-td-control mt41_location_number"><span data-type="storeHouse" class="ty-color-blue">占用库位</span></td>
                                    <td>
                                        <span class="ty-color-blue" onclick="mtScan()">查看</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div>
                                <%-- 没暂停的 --%>
                                <div class="brCon">
                                    <p class="topPad fixDis ctrlGp">
                                        <span class="stopInfo"></span>
                                        <span><span class="btnCat " data-type="startPurchase2">恢复采购</span></span>
                                        <span></span>
                                    </p>
                                    <p class="fixDis ctrlGp">
                                        <span class="noPositionCreat"></span>
                                        <span class="stopSupListBtn">被暂停采购本材料的定点供应商 <span class="btnCat showPauseSup" data-type="showPauseSup">XX 个</span></span>
                                        <span></span>
                                    </p>
                                    <p class="txt-right ctrlGp">
                                        <span class="btnCat addMtSupBtn" data-tbid="42" data-type="addMtSup">添加本材料的定点供应商</span>
                                        <span class="btnCat addNewSupBtn" data-type="addNewSup">向系统添加新供应商</span>
                                        <span class="btnCat noFixedPurchaseBtn" data-type="noFixedPurchase">本材料无需定点采购</span>
                                        <span class="btnCat noFixedPurchaseLogBtn ty-color-gray" data-type="noFixedPurchaseLog">本材料定点采购的操作记录</span>
                                    </p>
                                    <table class="ty-table ty-table-control mt42">
                                        <thead>
                                        <tr>
                                            <td>供应商</td>
                                            <td>采购合同</td>
                                            <td>单价</td>
                                            <td>是否开票</td>
                                            <td>创建人</td>
                                            <td>采购周期</td>
                                            <td>当前库存</td>
                                            <td>最低库存</td>
                                            <td>初始库存</td>
                                            <td>占用库位</td>
                                            <td>操作</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td>供应商</td>
                                            <td>采购合同</td>
                                            <td>单价</td>
                                            <td>是否开票</td>
                                            <td>创建人</td>
                                            <td>采购周期</td>
                                            <td>当前库存</td>
                                            <td>最低库存</td>
                                            <td class="ty-td-control"><span data-type="initStock" class="ty-color-blue">初始库存</span></td>
                                            <td class="ty-td-control"><span data-type="storeHouse" class="ty-color-blue">占用库位</span></td>
                                            <td>
                                                <span class="ty-color-blue" data-type="fixedScan" id="looker">查看</span>
                                                <span class="ty-color-blue" data-type="stopPurchase">暂停采购</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <%--暂停的--%>
                                <div class="brCon pauseSup">
                                    <p class="topPad">
                                        <span>被暂停采购本材料的定点供应商有如下 <span class="pauseSup1">XX</span>个</span>
                                    </p>
                                    <table class="ty-table ty-table-control mt43">
                                        <thead>
                                        <tr>
                                            <td>供应商</td>
                                            <td>采购合同</td>
                                            <td>单价</td>
                                            <td>是否开票</td>
                                            <td>被暂停的时间</td>
                                            <td>采购周期</td>
                                            <td>当前库存</td>
                                            <td>最低库存</td>
                                            <td>初始库存</td>
                                            <td>占用库位</td>
                                            <td>操作</td>
                                        </tr>
                                        </thead>
                                        <tbody>

                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
                <%-- 暂停采购的材料列表 --%>
                <div class="mainCon mainCon5">
                    <div class="leCat">
                        <div>暂停采购的材料
                            <span class="stopNum2">22</span>
                        </div>
                        <div class="bottom">
                            <span class="btnCat" onclick="goMainCon1(1)">返回上一级</span>
                            <span class="btnCat" onclick="toggleSuspend(1); backPreCat(1)">返回全部材料</span>
                        </div>
                    </div>
                    <div class="riMt">
                        <div>
                            <span>当前分类： <span class="curCatory curCat2"> 全部 > 暂停采购的材料</span></span>
                            <span class="search">查找：<input type="text" placeholder="请输入原辅材料的代号或名称"><span onclick="search($(this), 2)">确定</span></span>
                        </div>
                        <div class="lePad">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>被暂停采购的时间</td>
                                    <td>计量单位</td>
                                    <td>当前库存</td>
                                    <td>最低库存</td>
                                    <td>占用库位</td>
                                    <td>供应商</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div id="ye5"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/purchase/materialManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/sales/region.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
