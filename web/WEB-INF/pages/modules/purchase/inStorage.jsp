<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="${pageContext.request.contextPath }/css/production/review.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style type="text/css">
    .selecGS{ max-height:126px;   }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="giveUpApply()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="chooseMateriel_baoZhiQi" style="width:950px;">
        <div class="bonceHead">
            <span class="chooseTtl">选择要入库的材料</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form class="materielInputForm" autocomplete="off" style="width: 860px; margin: 8px auto; min-height: 300px">
                <div class="mtBase">
                    <div class="baoZhiQiPart tip">
                        <div class="el-row">
                            <div class="el-24">
                                <p class="editTip">本材料设置了保质期要求。请选择要入库材料的“<span class="name_deadline">--</span>”、录入相应数量，并编辑该组材料包装情况。</p>
                                <p><small class="ty-color-blue">注 要入库这批材料的保质期如有多个，则录完一组数据后，点击“增加一组数据”！</small></p>
                            </div>
                        </div>
                    </div>
                    <div class="defaultPart tip">
                        <div class="el-row">
                            <div class="el-24">请选择材料，并请录入数量。</div>
                        </div>
                    </div>

                    <div class="el-row">
                        <div class="el-12">
                            <div class="form-item label-top">
                                <div class="form-item-label">材料代号<span class="ty-color-red">*</span></div>
                                <div class="form-item-content">
                                    <input class="ty-inputText searchInputText" name="code" placeholder="请选择" style="width: 100%"/>
                                    <input class="hd" name="id" style="display:none;"/>
                                    <div class="search_choose_list"><div class="search_choose_list_avatar"></div></div>
                                </div>
                            </div>
                        </div>
                        <div class="el-12">
                            <div class="form-item label-top">
                                <div class="form-item-label">材料名称<span class="ty-color-red">*</span></div>
                                <div class="form-item-content">
                                    <input class="ty-inputText searchInputText" name="name" placeholder="请选择" style="width: 100%"/>
                                    <div class="search_choose_list"><div class="search_choose_list_avatar"></div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="otherInput">
                        <div class="el-row">
                            <div class="el-6">
                                <div class="form-item label-top">
                                    <div class="form-item-label">规格</div>
                                    <div class="form-item-content">
                                        <input class="ty-inputText" name="specifications" readonly style="width: 100%"/>
                                    </div>
                                </div>
                            </div>
                            <div class="el-6">
                                <div class="form-item label-top">
                                    <div class="form-item-label">型号</div>
                                    <div class="form-item-content">
                                        <input class="ty-inputText" name="model" readonly style="width: 100%"/>
                                    </div>
                                </div>
                            </div>
                            <div class="el-6">
                                <div class="form-item label-top">
                                    <div class="form-item-label">计量单位</div>
                                    <div class="form-item-content">
                                        <input class="ty-inputText" name="unit" readonly style="width: 100%"/>
                                    </div>
                                </div>
                            </div>
                            <div class="el-6">
                                <div class="form-item label-top">
                                    <div class="form-item-label">单价</div>
                                    <div class="form-item-content">
                                        <select class="ty-inputSelect" name="unit_price" readonly onchange="setOtherVal($(this))" style="width: 100%"></select>
                                        <input class="ty-inputText" name="unit_price" onchange="setMemo()" onkeyup="clearNoNum(this)" style="width: 100%"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="el-row">
                            <div class="el-12">
                                <div class="form-item label-top">
                                    <div class="form-item-label">备注</div>
                                    <div class="form-item-content">
                                        <select class="ty-inputSelect" name="memo" readonly onchange="setOtherVal($(this))" style="width: 100%"></select>
                                        <input class="ty-inputText" name="memo" onclick="setInvoice()" readonly style="width: 100%"/>
                                    </div>
                                </div>
                            </div>
                            <div class="el-12">
                                <div class="defaultPart">
                                    <div class="form-item label-top">
                                        <div class="form-item-label">申请入库<span class="ty-color-red">*</span></div>
                                        <div class="form-item-content">
                                            <input onkeyup="limitSize3(this)" name="quantity" class="quantity" placeholder="请录入数量" style="width: 100%"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="baoZhiQiPart">
                                    <div class="form-item label-top">
                                        <div class="form-item-label">申请入库合计</div>
                                        <div class="form-item-content">
                                            <input class="ty-inputText" name="count" disabled style="width: 100%"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="baoZhiQiPart">

                    <div class="hidePart">
                        <div class="el-row groupItem">
                            <div class="el-6">
                                <div class="form-item label-top">
                                    <div class="form-item-label"><span class="name_deadline">--</span><span class="ty-color-red">*</span></div>
                                    <div class="form-item-content">
                                        <input class="ty-inputText" name="deadline" placeholder="请选择日期" style="width: 100%"/>
                                    </div>
                                </div>
                            </div>
                            <div class="el-6">
                                <div class="form-item label-top">
                                    <div class="form-item-label">申请入库<span class="ty-color-red">*</span></div>
                                    <div class="form-item-content">
                                        <input class="ty-inputText quantity" onkeyup="limitSize3(this)" name="quantity" placeholder="请录入数量" style="width: 100%" onchange="judgeHasInputPack($(this))"/><span class="unit"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="el-12">
                                <div class="form-item label-top">
                                    <div class="form-item-label">本组材料的包装情况<span class="ty-color-red">*</span> <span class="ty-right link-blue" onclick="editBaoZhuangInfo($(this))">去编辑</span></div>
                                    <div class="form-item-content">
                                        <input class="ty-inputText" name="editstate" disabled value="尚未编辑" style="width: 100%"/>
                                        <span class="hd"></span>
                                        <span class="link-red delBtn" onclick="delOneGroup($(this))">删除</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mtRowInputGroup">
                        <div class="el-row groupItem fixed">
                            <div class="el-6">
                                <div class="form-item label-top">
                                    <div class="form-item-label"><span class="name_deadline">--</span><span class="ty-color-red">*</span> <span class="ty-right link-blue" onclick="addOneGroup()">增加一组数据</span></div>
                                    <div class="form-item-content dateInput">
                                        <input class="ty-inputText" placeholder="请选择日期" name="deadline" style="width: 100%"/>
                                    </div>
                                </div>
                            </div>
                            <div class="el-6">
                                <div class="form-item label-top">
                                    <div class="form-item-label">申请入库<span class="ty-color-red">*</span></div>
                                    <div class="form-item-content">
                                        <input class="ty-inputText quantity" onkeyup="limitSize3(this)" name="quantity" placeholder="请录入数量" style="width: 100%" onchange="judgeHasInputPack($(this))"/><span class="unit"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="el-12">
                                <div class="form-item label-top">
                                    <div class="form-item-label">本组材料的包装情况<span class="ty-color-red">*</span> <span class="ty-right link-blue" onclick="editBaoZhuangInfo($(this))">去编辑</span></div>
                                    <div class="form-item-content">
                                        <input class="ty-inputText" name="editstate" disabled value="尚未编辑" style="width: 100%"/>
                                        <span class="hd"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="chooseInMaterielBaoZhiQiBtn" onclick="chooseMateriel_baoZhiQi_submit()">确定</button>
        </div>
    </div>
    <%-- 入库关闭提示  --%>
    <div class="bonceContainer bounce-red" id="closeTip">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>确定放弃所录入的内容吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="closeUpApply()">确定</span>
        </div>
    </div>
    <%--提交入库申请前提示--%>
    <div class="bonceContainer bounce-blue" id="inStorageApplyTip" style="min-width:700px;">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="conWrap">
                <p class="sect">提交入库申请前，以下尚需确认：</p>
                <div class="sect line2">
                    <div class="clear">
                        <p class="ty-left">待入库材料已由系统分配至已有的采购订单中。</p>
                        <span class="ty-right ty-color-blue btnDo" type="btn" data-name="allocatedMat">去查看</span>
                    </div>
                    <div class="indent">
                        <span class="fa fa-square-o" data-val="1" onclick="toggleFa($(this))" id="val1"></span>
                        对于待入库材料在采购订单中的分配情况，无异议。
                    </div>
                </div>
                <div class="sect line3">
                    <p>待入库材料中，要入库数量大于可入库数量的有<span class="ty-color-blue" id="amountOut"></span>种。</p>
                    <p>多出来的材料是否需付款？需选出需付款的，并确定其订单处理方式。</p>
                    <p> *  此处无法修改“要入库数量”！</p>
                </div>
                <div class="sect indent">
                    <div class="clear paySumTr">
                        <p class="ty-left">该 <span class="ty-color-blue" id="amountOut2"></span> 种材料中，多出来材料需付款的有<span id="paySum"></span>种</p>
                        <span class="ty-right ty-color-blue btnDo" type="btn" data-name="adjustingMat">去调整</span><span class="hd" id="adjustingMatOralList"></span>
                    </div>
                    <div class="noPaySumTr">
                        <span class="fa fa-square-o" data-val="2" onclick="toggleFa($(this))" id="val2"></span>
                        经确认，无需付款的<span class="ty-color-blue" id="noPaySum"></span>种材料将由系统补发订单。
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-big bounce-ok ty-circle-3" type="btn" data-name="inStorageApplyTipOk" id="chooseMaterielBtn">确定</button>
        </div>
    </div>
    <%-- 删除 提示框  --%>
    <div class="bonceContainer bounce-red" id="delTip">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>确定删除本行内容？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type='btn' data-name="delSure">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="changeMtTip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="padding: 16px 36px">
            <div class="msg text-center">
                更换材料后，已录入数据将被清空。<br>
                确定继续操作吗？
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="changeQuantityTip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close cancelBtn"></a>
        </div>
        <div class="bonceCon" style="padding: 16px 36px">
            <div class="msg text-center">
                修改此项数据后，已编辑的包装情况将被清空。<br>
                确定继续操作吗？
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3 cancelBtn" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="editBaoZhuangTip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="padding: 16px 36px">
            <div class="msg text-left">对于没有包装的材料，您需确保入库时的入库标识能正确粘贴，另外，点击“确定”后，这些材料入库时将按数量整笔入库，系统内不再区分包装。</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">知道了，继续</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="bounce_Fixed2Tip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="msg text-center"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="ordSelect">
        <div class="bonceHead">
            <span>选择订单号</span>
            <a class="bounce_close" onclick="ordSelectOk('cancel')"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p><span>订单号：</span><select></select></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="ordSelectOk('cancel')">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="ordSelectOk()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="adjustTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p>还有未确定处理方式的材料</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 备注选择 --%>
    <div class="bonceContainer bounce-blue" id="memoSelect">
        <div class="bonceHead">
            <span>请选择</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div>
                <p class="leMar stable canInvoice">
                    购买本材料是否能开发票？ <input type="hidden" id="edit_canInvoice">
                    <span onclick="canInvoice(1 , $(this))" class="radioCon">是 <i id="edit_canInvoice1" class="fa fa-circle-o"></i></span>
                    <span onclick="canInvoice(0 , $(this))" class="radioCon">否 <i id="edit_canInvoice0" class="fa fa-circle-o"></i></span>
                </p>
                <p class="leMar stable incoiceType">
                    给开何种发票？ <input type="hidden" id="edit_incoiceType">
                    <span onclick="incoiceType(1 , $(this))" class="radioCon" id="edit_incoiceType1Con">增值税专用发票 <i id="edit_incoiceType1" class="fa fa-circle-o"></i></span>
                    <span onclick="incoiceType(2 , $(this))" class="radioCon">其他发票 <i id="edit_incoiceType2" class="fa fa-circle-o"></i></span>
                </p>
                <p class="leMar stable hasTax">
                    该单价是否含税？ <input type="hidden" id="edit_hasTax">
                    <span onclick="hasTax(1 , $(this))" class="radioCon">是 <i id="edit_hasTax1" class="fa fa-circle-o"></i></span>
                    <span onclick="hasTax(0 , $(this))" class="radioCon">否 <i id="edit_hasTax0" class="fa fa-circle-o"></i></span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3"onclick="memoSelectOk()">确定</span>

        </div>
    </div>

    <%--选择物料--%>
    <div class="bonceContainer bounce-blue" id="matDistribution" style="min-width:1000px;">
        <div class="bonceHead">
            <span>材料分配</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 450px;overflow-y: auto">
            <p>所有要入库的材料均需有订单。系统已将本次要入库材料全部分配给了未完结的订单。</p>
            <p>您可“自行调整分配数量”。如无需调整，可关闭本页面。</p>
            <div class="supplerInfo clear">
                <div class="ty-left">
                    <span class="ttl">供应商名称</span>
                    <span class="supFullname"></span>
                </div>
                <div class="ty-left">
                    <span class="ttl">简称</span>
                    <span class="supname"></span>
                </div>
                <div class="ty-left">
                    <span class="ttl">代号</span>
                    <span class="supcode"></span>
                </div>
                <div class="ty-right">
                    <span class="ty-color-blue btnDo" id="canAdjust" onclick="canAdjust()">调整分配数量</span>
                </div>
            </div>
            <table class="ty-table ty-table-control bg-yellow"  id="matDistribut">
                <thead>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号 </td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>购买数量</td>
                    <td>所属订单</td>
                    <td>可分配数量</td>
                    <td>系统分配的数量</td>
                    <td>调整后的数量</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号 </td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>购买数量</td>
                    <td>所属订单</td>
                    <td>可分配数量</td>
                    <td>系统分配的数量</td>
                    <td><input value="" disabled="disabled" /></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="hd" id="matDistributList"></span>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="matDistributCancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="matDistribut" onclick="matDistributOk()">确定</button>
        </div>
    </div>
    <%--调整材料--%>
    <div class="bonceContainer bounce-blue" id="adjustMat" style="min-width:1000px;">
        <div class="bonceHead">
            <span>调整材料</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 450px;overflow-y: auto">
            <ul class="ty-secondTab">
                <li class="ty-active">待选择(<span>X</span>)</li>
                <li>已选中(<span>X</span>)</li>
            </ul>
            <div style="padding:20px;">
                <div class="select0">
                    <p>待入库材料中，要入库数量大于可入库数量的有 <span id="amountOut3"></span> 种。</p>
                    <p>多出来的材料是否需付款？请选出需付款的。</p>
                    <table class="ty-table bg-yellow">
                        <thead>
                        <tr>
                            <td></td>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>申请入库的数量</td>
                            <td>多出来的数量</td>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <div class="select1">
                    <p>请确认已选中材料的处理方式。</p>
                    <p>您可放弃已选中的数据。被放弃的数据将回到“待选择”，在本页则不再显示。<span class="ty-right ty-color-blue btnDo" onclick="adjustMatOk()">调整完毕，返回</span></p>
                    <table class="ty-table ty-table-control bg-yellow" id="select2">
                        <thead>
                        <tr>
                            <td></td>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>申请入库的数量</td>
                            <td>多出来的数量</td>
                            <td>处理方式</td>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span id="adjustList" class="hd"></span>
        </div>
    </div>
    <%--去编辑 编辑本组材料的包装情况--%>
    <div class="bonceContainer bounce-blue" id="editBaoZhuangInfo" style="width:1000px;">
        <div class="bonceHead">
            <span>编辑本组材料的包装情况</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 450px;overflow-y: auto">
            <div>本组材料共 <span class="mtNum"></span><span class="unit"></span>，每种包装各有多少？请录入！</div>
            <div><small class="ty-color-blue">注：格内如不填写数据，则按零计算！</small></div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>最外层使用的主要包装物</td>
                    <td>单个包装内材料的数量</td>
                    <td>此种包装的个数</td>
                    <td>材料数量小计</td>
                </tr>
                </thead>
                <tbody></tbody>
                <tfoot>
                <tr class="fixed">
                    <td>没有包装</td>
                    <td>--</td>
                    <td><input class="ty-inputText" type="text" placeholder="此处需录入为没有包装的材料的数量" style="width: 300px"></td>
                    <td><span class="smallCount"></span></td>
                </tr>
                <tr>
                    <td colspan="3">各行材料数量相加</td>
                    <td><span class="allCount"></span></td>
                </tr>
                </tfoot>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="editBaoZhuangInfoBtn" onclick="editBaoZhuangInfo_submit()">确定</button>
        </div>
    </div>

</div>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>入库</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:80px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="guide_avatar">
                    <div class="ty-center">
                        要入库的物料来自于哪个供应商？
                    </div>
                    <div id="supplier">
                        <div class="searchArea">
                            <input type="text" class="searchInput ty-inputText">
                            <div class="down-arrow"><span class="fa fa-chevron-down"></span></div>
                        </div>
                        <div class="input_choose_list"></div>
                    </div>
                </div>
                <div class="main" style="display: none">
                    <div class="clear">
                        <div class="ty-left">
                            <p>正在办理来自于 <span class="ty-color-blue btnDo supplierName"></span> <span class="hd"></span> 所材料的入库申请。</p>
                            <p>请选择到货日期及要入库的物料后，提交入库申请。</p>
                        </div>
                        <div class="ty-right">
                            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="cancelApply">返回</span>
                            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" type="btn" data-name="inStorageApply" id="inStorageApplyBtn">提交入库申请</button>
                            <%--<span class="ty-color-blue supplierName"></span> 向本公司提供物料，请<span class="ty-color-red">先选择</span>要入库的物料。
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="chooseMateriel">选择物料</button>
                            <button class="ty-btn ty-btn-big ty-btn-orange ty-circle-3" type="btn" data-name="cancelApply">放弃本次申请</button>--%>
                        </div>
                    </div>
                    <div class="applyForm">
                        <div class="inStoItem">
                            <div class="clear matItem">
                                <div class="ty-left">
                                    <span>
                                        <span>到货日期</span>
                                        <input class="kj-input" type="text" id="arriveDate" placeholder="请选择"/>
                                    </span>
                                    <span class="link-blue btnDo linkBtn chooseInSto" onclick="chooseMateriel($(this))">选择要入库的材料</span>
                                </div>
                                <div class="ty-right">
                                    <span class="link-blue btnDo" id="addAGroup" onclick="newChooseMateriel($(this))">增加一组到货日期</span>
                                </div>
                            </div>
                            <table class="kj-table" id="materielTab">
                                <thead>
                                <tr>
                                    <td width="15%">材料代号</td>
                                    <td width="18%">材料名称</td>
                                    <td width="10%">型号</td>
                                    <td width="10%">规格</td>
                                    <td width="6%">计量单位</td>
                                    <td width="8%">申请入库的数量</td>
                                    <td width="10%">保质期至</td>
                                    <td width="23%">操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="${pageContext.request.contextPath }/script/purchase/inStorage.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
