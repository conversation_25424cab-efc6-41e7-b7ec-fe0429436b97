<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/purchase/receiveAddress.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/region.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <%--新增联系人--%>
    <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
        <div class="bonceHead">
            <span>新增联系人</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon linkUploadify" style="max-height:500px;overflow-y: auto;">
            <form id="newContectData">
                <p>
                    <span class="sale_ttl1">姓名：</span><span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span><span class="sale_ttl1">职位：</span><span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                </p>
                <p>
                    <span class="sale_ttl1">手机：</span><span><input type="text" placeholder="请录入" id="contactNumber" require/></span><a class="sale_ttl1 addMore" onclick="addMore($(this))">添加更多联系方式</a>
                    <select style="display: none;width: 191px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                        <option value="0"></option>
                        <option value="1">手机</option>
                        <option value="2">QQ</option>
                        <option value="3">Email</option>
                        <option value="4">微信</option>
                        <option value="5">微博</option>
                        <option value="9">自定义</option>
                    </select>
                </p>
                <ul class="otherContact">
                </ul>
                <div id="contactsCard">
                    <span class="sale_ttl1">名片：</span>
                    <div id="uploadCard" class="cardUploadBtn"></div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big funBtn" id="addContact" data-fun="addContact">提交</button>
        </div>
    </div>
    <%-- 选择联系人--%>
    <div class="bonceContainer bounce-blue" id="chooseCusContact" style="width: 450px">
        <div class="bonceHead">
            <span>选择联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="background:#f0f8ff ">
            <input type="hidden" id="target">
            <div class="p0">
                <p style='text-align:center;'>暂无数据，请通过新增添加</p>
            </div>
            <div class="p1">
                <p>以下为可供选择的联系人</p>
                <ul class="cusList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="linkBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseCusContactOk()">确定</button>
        </div>
    </div>
        <%-- 修改记录 --%>
        <div class="bonceContainer bounce-blue" id="receiveUpdateRecords" style="width: 590px;">
            <div class="bonceHead">
                <span>修改记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div style="padding:20px;">
                    <p class="curSta"></p>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td>记录</td>
                            <td>操 作</td>
                            <td>创建者/修改者</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
            </div>
        </div>
</div>
<div class="bounce">
    <%--！提示--%>
    <div class="bonceContainer bounce-blue" id="updateTip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon areaA">
            <p>此处的修改，适用于该地址中错别字之类的修改。</p>
            <p>修改为另一个地点，请勿使用本“修改”！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取 消</span>
            <button class="ty-btn ty-btn-blue ty-circle-5 ty-btn-big funBtn" data-fun="updateNext">继续操作</button>
        </div>
    </div>
    <%--新增收货信息--%>
    <div class="bonceContainer bounce-blue" id="newReceiveInfo" style="width: 450px">
        <div class="bonceHead">
            <span>新增收货地址</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1"><i class="xing"></i>收货地址</span>
                <span class="sale_con1 clearVal">
                    <input type="text" placeholder="请录入" id="ReceiveAddress" require/>
                        <i class="clearInputVal">X</i>
                </span>
            </p>
            <p>
                <span class="sale_ttl1"><i class="xing"></i>联系人</span>
                <span class="sale_con1 chooseCusCon" data-target="#ReceiveName">
                    <input type="text" readonly placeholder="请选择" id="ReceiveName" require/>
                    <span class="hd"></span>
                    <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                </span>
                <span class="linkBtn" onclick="addContactInfo(2)">新增</span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取 消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 funBtn"  data-source="1" data-fun="addressAddSure">提 交</button>
        </div>
    </div>
        <%--新增到货区域--%>
        <div class="bonceContainer bounce-green" id="newReceiveAreaInfo" style="width: 750px">
            <div class="bonceHead">
                <span>新增到货区域</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon" style="height: 180px;">
                <div class="areaForm">
                    <div class="regionMode">
                        <span class="sale_ttl1"><span class="ty-color-red">*</span>货物需到达的城市或地区</span>
                        <div class="sale_con1 regionText" onclick="regionCheck()">
                            <input type="text" placeholder="请选择" id="regionCon" require />
                            <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                            <span class="hd"></span>
                        </div>
                        <div class="regionBox">
                            <ul class="region-tab"></ul>
                            <div class="region-content">
                                <ul data-level="1"></ul>
                            </div>
                        </div>
                    </div>
                    <p>
                        <span class="sale_ttl1">对需到达地点的特殊要求</span>
                        <span class="sale_con1 clearVal">
                        <input type="text" placeholder="请录入" id="requirements"/>
                        <i class="clearInputVal">X</i>
                    </span>
                    </p>
                    <p>
                        <span class="sale_ttl1"><span class="ty-color-red">*</span>联系人</span>
                        <span class="sale_con1 chooseCusCon" data-target="#areaName">
                    <input type="text" readonly placeholder="请选择" id="areaName" require/>
                    <span class="hd"></span>
                    <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                </span>
                        <span class="linkBtn" onclick="addContactInfo(4)">新增</span>
                    </p>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel()">取消</span>
                <button class="ty-btn ty-btn-green ty-btn-big funBtn"  data-source="2" data-fun="addressAddSure">提交</button>
            </div>
        </div>
        <%--已被停用的数据--%>
        <div class="bonceContainer bounce-blue" id="receiveStopped" style="width: 650px">
            <div class="bonceHead">
                <span>已被停用的数据</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon" style="min-height: 100px;">
                <table class="ty-table ty-table-control blueBg receiveStoppedList">
                    <tbody>
                    <tr>
                        <td>名称</td>
                        <td>被停用的时间</td>
                        <td>操作</td>
                    </tr>
                    <tr>
                    </tr>
                    </tbody>
                </table>

            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关 闭</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
    <%--联系方式查看--%>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <table class="see_otherContact ty-table" style="width:442px;">
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关 闭</span>
        </div>
    </div>
        <%--修改记录-查 看--%>
        <div class="bonceContainer bounce-blue" id="recordScan" style="width: 550px">
            <div class="bonceHead">
                <span></span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="areaA areaForm">
                    <p>
                        <span class="sale_ttl1 record_name">收货地址</span>
                        <span class="sale_con1 record_address"></span>
                    </p>
                    <p>
                        <span class="sale_ttl1">收货人</span>
                        <span class="sale_con1 record_contact"></span>
                    </p>
                    <p>
                        <span class="sale_ttl1">收货电话</span>
                        <span class="sale_con1 record_telephone" ></span>
                    </p>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            </div>
        </div>
        <%--启用 --%>
        <div class="bonceContainer bounce-blue" id="restartTip">
            <div class="bonceHead">
                <span></span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="ty-center">确定后，该条数据将可重新被选用！</div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="restartOk()">确 定</span>
            </div>
        </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>收货地点</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <input type="hidden" id="showMainConNum">
                    <div class="mainCon mainCon1">
                        <p>本模块用于管理本公司订货时的收货地点。该数据可供添加供应商或下订单时选用。</p>
                        <div class="panel-box">
                            <div class="flexBox">
                                <div>需送货上门情况下的收货信息</div>
                                <div>
                                    <span class="linkBtn gapR" data-fun="addAddress">新增</span>
                                    <span class="linkBtn" data-fun="stoppedAddress" data-type="1">已被停用的数据</span>
                                </div>
                            </div>
                            <table class="ty-table ty-table-control blueBg receiveAddressList">
                                <thead>
                                <tr>
                                    <td>收货地址</td>
                                    <td>收货人</td>
                                    <td>收货电话</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="panel-box">
                            <div class="flexBox">
                                <div>需配送至某城市情况下的到货区域</div>
                                <div>
                                    <span class="linkBtn gapR" data-fun="addArea">新增</span>
                                    <span class="linkBtn" data-fun="stoppedAddress" data-type="2">已被停用的数据</span>
                                </div>
                            </div>
                            <table class="ty-table ty-table-control blueBg receiveAreaList">
                                <thead>
                                <tr>
                                    <td>到货区域</td>
                                    <td>收货人</td>
                                    <td>收货电话</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/purchase/receiveAddress.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/sales/region.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
