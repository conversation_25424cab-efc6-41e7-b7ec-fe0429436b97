<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/purchase/orderManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/dailyAffairs/dailyAffairs.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">

<div class="bounce">
    <%--选择票款处理方式--%>
    <div class="bonceContainer bounce-green" id="invoiceHandleChoice" >
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="padding-left:118px; ">
            <p>请选择您要办理的事务</p>
            <p><i class="fa fa-circle-o" data-val="1"></i>仅提交票据，不提交付款申请</p>
            <p><i class="fa fa-circle-o" data-val="2"></i>录入票据，并提交付款申请</p>
            <p><i class="fa fa-circle-o" data-val="3"></i>仅提交付款申请，不录入票据</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5"  onclick="invoiceHandleChoiceOk()">确定</span>
        </div>
    </div>
    <%--票款处理-仅提交票据--%>
    <div class="bonceContainer bounce-green" id="invoiceHandle" style="width:820px;">
        <div class="bonceHead">
            <span>票款处理-仅提交票据</span>
            <a class="bounce_close" onclick="cancelReimburseBtn()"></a>
        </div>
        <div class="bonceCon" >
            <table width="100%;" class="fds">
                <tr>
                    <td width="10%">订单号</td><td width="20%" class="orderSn"></td>
                    <td width="10%">订单总额</td><td width="20%" class="orderAmount"></td>
                    <td width="20%" style="text-align: right;" >订单日期</td><td width="20%" style="text-align: right;" class="orderDate"></td>
                </tr>
                <tr>
                    <td>供应商名称</td><td colspan="3" class="supName"></td>
                    <td style="text-align: right;" >供应商代号</td><td style="text-align: right;" class="supCode"></td>
                </tr>
            </table>
            <table class="ty-table">
                <tr>
                    <td colspan="2">检验已合格</td>
                    <td colspan="2">已入库</td>
                    <td colspan="2">已提交票据</td>
                    <td colspan="2">已付款</td>
                </tr>
                <tr>
                    <td>金额</td><td>比例</td>
                    <td>金额</td><td>比例</td>
                    <td>金额</td><td>比例</td>
                    <td>金额</td><td>比例</td>
                </tr>
                <tr class="rateCon">
                    <td>100.00元</td><td>12%</td>
                    <td>100.00元</td><td>12%</td>
                    <td>100.00元</td><td>12%</td>
                    <td>100.00元</td><td>12%</td>
                </tr>
            </table>
            <div class="inv inv1 inv2" id="reimburse">
                <hr class="hr">
                <p>您已录入票据共 <b class="ty-color-blue billCountNumber"></b> 张，
                    票据金额共<b class="ty-color-blue billCountAmount"></b>元，
                    需支付的金额为<b class="ty-color-blue countAmount"></b>元
                    <span class="ty-btn ty-circle-3 ty-btn-green ty-right" type="btn" data-name="goodEntry">票据录入</span>
                </p>
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td>票据种类</td>
                        <td>单张票据金额</td>
                        <td>票据数量</td>
                        <td>票据金额合计</td>
                        <td>申请报销的金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="invoiceList">
                    </tbody>
                </table>
            </div>
            <div class="inv inv2 inv3" id="reasonCon">
                <hr class="hr">
                <p>此次申请付款的金额 <input type="text" id="amount2" onkeyup="tofixed4(this,2)" onchange="turnUpperKey($(this))">元 <span class="upperKey" style="margin-left: 20px;">大写 一二三四元</span></p>
                <p>提交此次付款申请的原因</p>
                <p><i class="fa fa-circle-o" data-val="1"></i>符合合同约定</p>
                <p>
                    <i class="fa fa-circle-o" data-val="2"></i>不符合合同约定，录入具体原因
                    <span class="reasonTip">
                         <input id="reason" placeholder="此处可录入不超过30字" style="width: 100%;" onkeyup="setNumberTip($(this))"/>
                        <span class="ty-right numTip">0/30</span>
                    </span>

                </p>

            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelReimburseBtn()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="invoiceHandleOk()">提交审批</span>
        </div>
    </div>

    <%-- 删除材料  --%>
    <div class="bonceContainer bounce-red" id="mtDel">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">确定从本采购订单中删除该材料吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="deleteOtherSure()">确定</span>
        </div>
    </div>
    <%-- 删除材料  --%>
    <div class="bonceContainer bounce-red" id="backTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="ty-center">
                <p>返回后，已编辑内容将被放弃。</p>
                <p>确定返回吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="turnPage() ">确定</span>
        </div>
    </div>
    <%-- 最低采购量提示  --%>
    <div class="bonceContainer bounce-red" id="purchaseTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="ty-center">
                <p>采购该供应商该材料的最低采购量为<span id="minimumPurchase"></span>。</p>
                <p>确定所录入的购买数量无误吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%-- 提示  --%>
    <div class="bonceContainer bounce-red" id="planTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="ty-center">
                <div class="hd info"></div>
                <p>购买数量不能多于统筹订购数</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="planTipSure(); ">确定</span>
        </div>
    </div>
    <%-- 采购周期提示  --%>
    <div class="bonceContainer bounce-red" id="deliveryDateLimit">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="ty-center">
                <p>该供应商供应该材料的采购周期为<span id="perchaseCycle"></span>天。</p>
                <p>确定所选的到货日期无误吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel();">确定</span>
        </div>
    </div>
    <%-- 信息对比  --%>
    <div class="bonceContainer bounce-blue" id="detailsCompare" style="width: 1200px;">
        <div class="bonceHead">
            <span>信息对比</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control bg-yellow gapBt" id="mtDetailsCompare">
                <thead>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>创建人</td>
                    <td>计量单位</td>
                    <td>当前库存</td>
                    <td>最低库存</td>
                    <td>占用库位</td>
                    <td>供应商</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div id="compareSupplier" class="supplierList">
                <div class="tabs__header clear">
                    <div class="tabs__nav">
                    </div>
                </div>
                <div class="tabs__content">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>
    <%-- 预付款  --%>
    <div class="bonceContainer bounce-blue" id="advanceCharge">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p>系统中，该供应商状态为
                <span id="messgble">""。</span>
                <br />
                <span id="annotation">如必要，可在本订单上修改状态，否则点击“确定”继续流程。</span>
            </p>
            <p>本次采购是否需要预付款？</p>
            <div class="clear setAdvance">
                <div class="ty-left"><span class="fa fa-circle-o" data-type="2" id="needget"></span>需要</div>
<%--                <div class="ty-left"><span class="fa fa-circle-o" data-type="2" id="needget" disabled data-ron="ynk" data-yon="bili"></span>需要</div><!--需要关闭的按钮-->--%>
                <div class="ty-left"><span class="fa fa-circle-o" data-type="1" id="noneed"></span>不需要</div>
            </div>
            <div class="advanceMain">
                <div>
                    <span class="gapRL">请确认以下各项付款要素：</span>
                    <span class="ty-color-blue" onclick="addMoreAdvance()">需多次预付款</span>
                </div>
                <div class="advanceForm">
                    <div class="advanceItem">
                        <div>
                            <span class="xing">计划付款时间</span>
                            <input id="timege" class="planTime" type="text" name="planDate" value="" />
                        </div>
                        <div style="display: flex;align-items: center;">
                            <span class="xing" style="margin-right: 24px;">计划付款金额</span>
                            <div style="height: 39px;">
                                <input type="text" value="" id="math" name="planAmount" oninput="testNum(this)"/>
                                <div style="font-size: 14px;margin-top: -25px;margin-left: 121px;display: block;width: 20px;" id="pofont">元</div>
                                <div style="top: -22px;cursor: pointer;display: block;right: 13px;position: relative;" class="input_clear" id="cloone">
                                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">x</button>
                                </div>
                            </div>
                        </div>
                        <div>
<%--                            <p class="ty-color-blue">注：默认的--%>
                            <p class="ty-color-blue" id="messge">注：默认的
                                <span id="mon1">XXXX.XX</span>
                                元为订单金额
                                <span id="mon2">XXXX.XX</span>
                                元的<span id="mon3">XX.XX</span>%，但可修改。
                            </p>
                        </div>
                        <div>
                            <span class="xing">计划付款方式</span>
                            <select id="picege" name="planMethod"></select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">返回</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="advanceTj();">确定</span>
        </div>
    </div>
    <%-- 修改备注内容  --%>
    <div class="bonceContainer bounce-blue" id="editUncertainMemo" style="width: 620px;">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="uncertainMemo leaveLft">
                <div class="memoItem clear">
                    <p>购买本材料是否给开发票</p>
                    <div data-open="1" onclick="openNext($(this))"><span data-name="invoicable" data-type="1" class="fa fa-circle-o"></span>是</div>
                    <div data-open="0" onclick="openNext($(this))"><span data-name="invoicable" data-type="0" class="fa fa-circle-o"></span>否</div>
                </div>
                <div class="memoItem clear" style="display: none;">
                    <p>给开何种发票</p>
                    <div data-open="1" onclick="openNext($(this))"><span data-name="invoiceCategory" data-type="1" class="fa fa-circle-o"></span>增值税专用发票</div>
                    <div data-open="0" onclick="openNext($(this))"><span data-name="invoiceCategory" data-type="2" class="fa fa-circle-o"></span>其他发票</div>
                </div>
                <div class="memoItem clear" style="display: none;">
                    <p>该单价是否含税</p>
                    <div data-open="0" onclick="openNext($(this))"><span data-name="taxInclusive" data-type="1" class="fa fa-circle-o"></span>含税</div>
                    <div data-open="0" onclick="openNext($(this))"><span data-name="taxInclusive" data-type="0" class="fa fa-circle-o"></span>不含税</div>
                    <div class="taxWrap">
                        <span>税率</span>
                        <span class="taxRateBody">
                              <input type="hidden" id="taxRateId" />
                              <input type="text" id="order_taxRate" readonly onclick="taxRateFun($(this))">
                            <span class="taxRateList"></span>
                            </span>
                        <span class="linkBtn" onclick="addTaxRate($(this))">新增</span></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">返回</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="memoTurn();">确定</span>
        </div>
    </div>
    <%--在途数量--%>
    <div class="bonceContainer bounce-green" id="inTransit" style="width:918px; ">
        <div class="bonceHead">
            <span>在途数量</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon otherControl">
            <div class="conTtl">
                <span need data-name="code">XX材XX料XX代XX号XX</span>
                <span need data-name="name">X材X料XX名X称X</span>
                <span>在途共 <span need data-name="way_num"></span></span>
            </div>
            <div>规格：<span need data-name="specifications"></span></div>
            <div class="clear"><span class="ty-left">型号：<span need data-name="model"></span></span><span class="ty-right">计量单位：<span need data-name="unit"></span></span></div>
            <hr/>
            <div>
                <table class="ty-table ty-table-control yellowTb">
                    <thead>
                    <tr>
                        <td>购买理由</td>
                        <td>采购已订购数量/在途数</td>
                        <td>要求到货日期</td>
                        <td>订单进度</td>
                        <td>订单号</td>
                        <td>供应商</td>
                        <td>请购时间</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>购买理由</td>
                        <td>采购已订购数量/在途数</td>
                        <td>要求到货日期</td>
                        <td>订单进度</td>
                        <td>订单号</td>
                        <td>供应商</td>
                        <td>请购时间</td>
                    </tr><tr>
                        <td>购买理由</td>
                        <td>采购已订购数量/在途数</td>
                        <td>要求到货日期</td>
                        <td>订单进度</td>
                        <td>订单号</td>
                        <td>供应商</td>
                        <td>请购时间</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
        <div class="bonceContainer bounce-blue" id="addressChangeTip" style="min-width:400px;">
            <div class="bonceHead">
                <span>！提示</span>
                <a class="bounce_close" onclick="addressChangeOk(1) "></a>
            </div>
            <div class="bonceCon clearfix">
                <div class="narrowBody">
                    <div class="tips">
                        <span class="tip1">本材料在系统内没有该交货地点的价格。</span>
                        <span class="tip2">刚才所选材料中，有<span class="outNum"></span>种在系统内没有已选交货地点的价格。</span>
                        <span class="tip3">重新选择收货地点后，有<span class="outNum"></span>种已选材料在系统内没有新交货地点的价格。</span>
                    </div>
                    <ul class="conditional">
                        <li>
                            <i class="fa fa-square-o" data-val="1"></i>
                            <span> 重新选择交货地点</span>
                        </li>
                        <li>
                            <i class="fa fa-square-o" data-val="2"></i>
                            <span> 录入在该交货地点的价格</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="addressChangeOk(1) ">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="addressChangeOk(2) ">确 定</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-green" id="billEntry" style="min-width:1200px;">
        <div class="bonceHead">
            <span>票据信息</span>
            <a class="bounce_close" onclick="cancelfile() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="ty-alert ty-alert-info"> 开票日期 <span class="issueDate"></span></div>
            <div>
                <%--增值税专用发票，增值税普通发票货物--%>
                <table class="ty-table ty-table-control VATGood">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td id="m2">货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>税率</td>
                        <td>税额</td>
                        <td>含税合计</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--其他普通发票货物--%>
                <table class="ty-table ty-table-control otherGood" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--收据货物--%>
                <table class="ty-table ty-table-control receipt" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>物品数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="handleBtn">
                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="goodEntryNext">录入本张票据上的下一种货物</button>
                <div class='ty-form-checkbox' skin="green" level="1" style="margin-top: 8px">
                    <i class="fa fa-check"></i>
                    <span>本张票据录入完毕</span>
                </div>
            </div>
            <form id="form_billEntry" style="display: none">
                <div class="formItem formItem_auto">
                    <div class="formTitle t1">为尽量避免录入错误，请再次录入您手中这张票据的总金额 <span class="ty-color-red">*</span></div>
                    <div class="formTitle t2">请录入本票据的价税合计 <span style="width:200px; "></span> 价税合计 <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input type="text" id="billAmount" name="billAmount" require onkeyUp = 'testNum(this)' onchange="setToFixed2($(this));setAmount()"> 元<div class="isBillAmountTip ty-color-red">价税合计与本票据其他数据有冲突，请检查并修正！</div></div>
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">内容与本票据完全相同，且开票月份也为<span class="issueMonth"></span>的票据总数量 <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input data-type="num" id="number" name="number" require onchange="setAmount(); setInput()" onkeyUp = 'clearNum(this)' placeholder="1"> 张</div>
                </div>
                <div class="formItem formItem_auto repeatBill" style="display: none">
                    <div class="formTitle">本张发票号码为 <span class="thisBillNo"></span>，<span class="ty-color-orange">请输入其他 <span class="thisBillNum"></span>张发票的发票号码 <span class="ty-color-red">*</span></span> </div>
                    <div class="formCon"><div class="repeatCon"></div></div>
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">如您想要申请报销的金额不是 <span class="ty-color-blue countAllAmount"></span>元，则请修改申请报销的金额  <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input class="inputAmount" name="amount" data-type="num" require onkeyUp = 'testNum(this)' onchange="setToFixed2($(this));"> 元</div>
                </div>
                <%-- <div class="formItem formItem_auto">
                     <div class="formTitle">备注</div>
                     <div class="formCon"><textarea name="memo"  cols="30" rows="2" style="width: 500px" onkeyup="countWords($(this),60)"></textarea><div class="textMax text-right">255/255</div></div>
                 </div>--%>
                <div class="ty-alert ty-alert-info">
                    您可点击浏览以上传票据图片。票据是否需上传、如何拍照、是否粘贴等，请按财务的要求。
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">
                        <span>票据图片</span>
                    </div>
                    <div class="formCon">
                        <div class="formConBg">
                            <div class="cp_imgShow clearfix"></div>
                            <div class="cp_imgUpload"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelfile()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="billEntryBtn" type="btn" data-name="billEntry">确定</button>
        </div>
    </div>

    <%-- 还有必填项尚未填写  --%>
    <div class="bonceContainer bounce-blue" id="filledTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">还有必填项尚未填写!</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="adPlanTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计划付款金额不可超过采购订单的金额！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增税率  --%>
    <div class="bonceContainer bounce-green" id="addTaxRate">
        <div class="bonceHead">
            <span>新增税率</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center"><input type="text" placeholder=" 请输入税率" id="taxRateVal" onkeyup="clearNoNum(this)" /></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addTaxRatetOk()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-green" id="goodEntry" style="min-width:600px;">
        <div class="bonceHead">
            <span>票据录入</span>
            <a class="bounce_close" onclick="cancelGoodsEntery()  "></a>
        </div>
        <div class="bonceCon clearfix">
            <form action="" id="form_goodEntry">
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 票据种类</span>
                    </div>
                    <div class="formCon">
                        <select name="billCat" id="billCat" require></select>
                    </div>
                </div>
                <div class="ty-nextTip">想要录入的票据上只有一行内容吗？<br>
                    <span type="btn" data-name="iconOneRow" data-val="1"><i class="fa fa-circle-o"></i>是</span>
                    <input type="hidden" id="onlyOneRow"><span class="wid"></span>
                    <span type="btn" data-name="iconOneRow" data-val="0"><i class="fa fa-circle-o"></i>不，内容超过一行</span>
                </div>
                <div>
                    <div class="kindInvoice">
                        <%--增值税发票 普通发票 和 收据 --%>
                        <div class="VATInvoice" style="display: none">
                            <div class="formItem">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> 开票日期</span></div>
                                <div class="formCon"><input type="text" name="issueDate" class="issueDate" id="issueDate" require></div>
                            </div>
                            <div class="formItem formItem_billNo">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> 发票号码</span></div>
                                <div class="formCon"><input type="text" class="billNo" onchange='countWords($(this),8)' require></div>
                            </div>
                            <div class="ty-alert ty-alert-info">！请录入您手中这张票据的内容。<span class="ty-color-orange">如该票据中有多行内容</span>，则先录第一行。</div>
                            <div class="formItem firstFeeCat">
                                <div class="formTitle">
                                    <span><span class="ty-color-red">*</span> 费用类别</span>
                                </div>
                                <div class="formCon">
                                    <select class="feeCat" id="feeCat" require style="width: 188px;">
                                        <option value="0">---请选择费用类别---</option>
                                    </select>
                                </div>
                            </div>
                            <div class="formItem secondFeeCat" style="display: none">
                                <div class="formTitle">
                                    <span><span class="ty-color-red">*</span></span>
                                </div>
                                <div class="formCon">
                                    <select class="secondFeeCat" require>
                                        <option value="0">---请选择二级费用类别---</option>
                                    </select>
                                </div>
                            </div>
                            <div class="formItem formItem_itemName">
                                <div class="formTitle"><span class="ty-color-red">* </span><span class="titleName" id="m1">货物或应税劳务、服务名称</span></div>
                                <div class="formCon"><select name="ordersItem" onchange="setMtPrice($(this))" id="itemName" style="width: 188px;" require></select></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>规格型号</span></div>
                                <div class="formCon"><input type="text" name="model" id="model" onchange='countWords($(this),10)'></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>单位</span></div>
                                <div class="formCon"><input type="text" name="unit" id="unit" onchange='countWords($(this),6)'></div>
                            </div>
                            <div class="formItem formItem_itemQuantity">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> <span class="titleName">数量</span></span></div>
                                <div class="formCon"><input type="text" id="itemQuantity" name="itemQuantity" onkeyUp = 'tofixed4(this);setMtPrice($(this))' require></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>单价</span></div>
                                <div class="formCon"><input type="text" name="uniPrice" id="uniPrice" disabled></div>
                            </div>
                            <div class="formItem formItem_price">
                                <div class="formTitle"><span><span class="ty-color-red">*</span><span class="titleName">金额</span></span></div>
                                <div class="formCon"><input type="text" name="price" id="price" onkeyUp = 'testNum(this)' onchange="countWords($(this),8);setToFixed2($(this))" require></div>
                            </div>
                            <div class="taxInput">
                                <div class="formItem">
                                    <div class="formTitle"><span>税率</span></div>
                                    <div class="formCon"><input type="text" name="taxRate" id="taxRate" disabled style="width: 158px"> %<div class="tipTaxRate ty-color-red text-right">税率超过100%</div></div>
                                </div>
                                <div class="formItem">
                                    <div class="formTitle"><span><span class="ty-color-red">*</span> 税额</span></div>
                                    <div class="formCon"><input type="text" name="taxAmount" id="taxAmount" onkeyUp = 'testNum(this)' onchange="countWords($(this),8);setToFixed2($(this))" require></div>
                                </div>
                                <div class="formItem">
                                    <div class="formTitle"><span>含税合计</span></div>
                                    <div class="formCon"><input type="text" name="amount" id="amount" onchange="countWords($(this),8);setToFixed2($(this))" disabled></div>
                                </div>
                            </div>
                            <div class="formItem formItem_itemMemo">
                                <div class="formTitle"><span>发票上的备注</span></div>
                                <div class="formCon"><input type="text" class="billMemo" name="memo" onchange="countWords($(this), 30)" ><span class="textMax"></span></div>
                            </div>
                        </div>
                        <%--定额发票内容--%>
                        <div class="quotaInvoice" style="display: none">
                            <div class="ty-alert ty-alert-info">！ 您在此可录入多种定额发票。请按财务要求上传票据照片。系统支持每行均上传一张。</div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td><span class="ty-color-blue" data-name="newRow" type="btn" id="newRow">增加行</span></td>
                                </tr>
                                <tr>
                                    <td><span class="ty-color-red">*</span>费用类别</td>
                                    <td><span class="ty-color-red">*</span>单张发票金额</td>
                                    <td><span class="ty-color-red">*</span>数量</td>
                                    <td>发票金额合计</td>
                                    <td><span class="ty-color-red">*</span>实际支出金额</td>
                                </tr>
                                </thead>
                                <tbody> </tbody>
                            </table>
                            <div class="summaryTip" id="summary2">您已录入定额发票共 0 张，发票金额总计 0 元，实际支出（即您将报销）总计 0 元。</div>
                            <div>
                                <%--<div class="formItem">--%>
                                <%--<span>备注</span> <input type="text" id="memo" style="display: inline-block; width:400px; ">--%>
                                <%--</div>--%>
                            </div>

                        </div>
                        <%--单行录入增普/其他发票/收据--%>
                        <div class="oneRowInvoice">
                            <div class="ty-alert ty-alert-info">！ 您在此可录入多张只有一行内容的 <span class="in4"></span>，请按财务要求上传票据照片。系统支持每行均上传一张。</div>
                            <div>
                                <span type="btn" data-name="inputNextBtn" id="inputNextBtn" class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-3">录入下一张只有一行内容的增值税普通发票</span>
                                <div class="ty-alert ty-alert-info ty-clear"> </div>
                                <table class="ty-table ty-table-control oneRowTab" style="min-width:1500px;">
                                    <thead>
                                    <tr>
                                        <td><span class="ty-color-red">*</span>费用类别</td>
                                        <td><span class="ty-color-red in2">*</span>发票号码</td>
                                        <td><span class="ty-color-red">*</span>开票日期</td>
                                        <td><span class="ty-color-red">*</span><span class="in3">货物或应税劳务、服务名称</span><span class="in1">票据内容</span></td>
                                        <td>规格型号</td>
                                        <td>单位</td>
                                        <td>数量</td>
                                        <td><span class="ty-color-red">*</span><span id="in4">发票金额</span></td>
                                        <td><span class="ty-color-red">*</span>实际支出金额</td>
                                        <td>发票上的备注</td>

                                    </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                            <div class="summaryTip" id="summary"></div>
                            <div>
                                <%--<div class="formItem">--%>
                                <%--<span>备注</span> <input type="text" id="memo2" name="memo" style="display: inline-block; width:400px; ">--%>
                                <%--</div>--%>
                            </div>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelGoodsEntery() ">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="goodEntryBtn" type="btn" data-name="sureGoodEntry">录入完毕，下一步</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <div class="bonceContainer bounce-red" id="tip" style="min-width:600px;">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center">
                财务规定：<span class="ty-color-red">一次报销中不可既有发票又有收据</span>。
                <div class="tip"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="tipDelTr">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center" class="deltip">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="tipDelTrOk() ">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addMemo">
        <div class="bonceHead">
            <span>发票上的备注</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center">
                <textarea placeholder="您手中发票上的备注如有内容，您可在此录入。如觉不必要录入，或无备注，则请忽略。"
                          id="memo3" onkeyup="countWords($(this),60)"></textarea>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="addMemoOk();  ">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>订单管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <input type="hidden" value="" id="isEdit" />
                <div class="backGroup clear">
                    <div class="stockJump">
                        <button type="button" class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="backInit" onclick="backJump(2)">返回订单管理</button>
                        <button type="button" class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="backPre" onclick="backJump(1)">返回上一页</button>
                    </div>
                </div>
                <div class="ty-mainData" id="paContainer">
                    <%-- 订单管理主页面 --%>
                    <div class="mainArea leftSpace">
                        <div class="flexCon">
                            <div class="kindChoose">
                                <span class="conWt">新增订单</span>
                                <select id="addOrderKind" onchange="orderTypeChoose($(this))">

                                </select>
                            </div>
                            <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-5">待补发的订单</span>
                            <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-5">被终止的订单</span>
                            <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-5">已完结的订单</span>
                        </div>
                        <div class="flexCon">
                            <p>处于进程中的订单（共<span id="ordNum"></span>个）如下表，其中百分比均为金额的百分比。</p>
                            <div class="searchSect">
                                <div class="ty-left keywordSearch">
                                    查找
                                    <input placeholder="请输入订单编号或供应商简称" id="orderSearchKey" />
                                </div>
                                <span class="ty-left ty-btn ty-btn-blue ssEnsure" onclick="orderSearchKey()">确定</span>
                            </div>
                        </div>
                        <div class="tpl">
                            <table class="ty-table ty-table-control" id="phAllOrders">
                                <thead>
                                <tr>
                                    <td width="16%">订单编号</td>
                                    <td width="16%">供应商</td>
                                    <td width="18%">创建时间</td>
                                    <td width="26%">订单综合管理</td>
                                    <td width="8%">检验已合格</td>
                                    <td width="8%">已入库</td>
                                    <td width="8%">票据已提交</td>
                                    <td width="8%">已付款</td>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                        <%--分页容器--%>
                        <div id="ye_orderManage"></div>
                    </div>
                    <%-- 补发订单 --%>
                    <div class="replenish leftSpace" style="display: none">
                        <div class="repStepOne">
                            <div class="module1">
                                <p>以下为销售来了新订单后，统筹人员确定的需采购材料的最低需求。请在此基础上制作并下发订单。</p>
                                <table class="ty-table ty-table-control specialTb">
                                    <thead>
                                    <tr>
                                        <td width="18%">材料名称</td>
                                        <td width="18%">材料代号</td>
                                        <td width="8%">型号</td>
                                        <td width="8%">规格</td>
                                        <td width="8%">计量单位</td>
                                        <td width="10%">到货日期不可晚于</td>
                                        <td width="10%">到货数量不可少于</td>
                                        <td width="12%">采购已订购总数/在途数量</td>
                                        <td width="8%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>材料代号</td>
                                        <td>型号</td>
                                        <td>规格</td>
                                        <td>计量单位</td>
                                        <td>计量单位</td>
                                        <td>创建人</td>
                                        <td>gdgs</td>
                                        <td>gdgs</td>
                                        <td>gdgs</td>
                                        <td>gdgs</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="supplyAgain specialCol">
                                <div class="leCat">
                                    <div>全部（<span id="catTotal"></span>种)</div>
                                    <ul id="catAll">
                                        <li>构成商品的原辅材料 <span class="catNum">22</span></li>
                                        <li>外购成品 <span class="catNum">22</span></li>
                                        <li>商品的包装物 <span class="catNum">22</span></li>
                                        <li>其他原辅材料 <span class="catNum">22</span></li>
                                    </ul>
                                </div>
                                <div class="hd" id="catTree"></div>
                                <div class="riMt" style="margin-left: 255px;">
                                    <div class="quickLine">
                                        <span>当前分类： <span class="curCatory curCat"></span></span>
                                        <div class="ty-right searchSect">
                                            <div class="ty-left keywordSearch">
                                                查找
                                                <input id="mtSearch" placeholder="请输入原辅材料的代号或名称" />
                                            </div>
                                            <span class="ty-left ty-btn ty-btn-blue ssEnsure" onclick="mtSearchSure()">确定</span>
                                        </div>
                                    </div>
                                    <p>请选择要补货的材料。</p>
                                    <table class="ty-table ty-table-control specialTb" id="replenishList">
                                        <thead>
                                        <tr>
                                            <td width="7%" class="ty-empty"></td>
                                            <td width="14%">材料名称</td>
                                            <td width="14%">材料代号</td>
                                            <td width="10%">型号</td>
                                            <td width="10%">规格</td>
                                            <td width="7.5%">计量单位</td>
                                            <td width="7.5%">当前库存</td>
                                            <td width="7.5%">最低库存</td>
                                            <td width="7.5%">在途数量</td>
                                            <td width="7.5%">占用库位</td>
                                            <td width="7.5%">供应商</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td class="br-empty"><span class="fa fa-circle-o"></span></td>
                                            <td>材料代号</td>
                                            <td>型号</td>
                                            <td>规格</td>
                                            <td>计量单位</td>
                                            <td>计量单位</td>
                                            <td>创建人</td>
                                            <td>gdgs</td>
                                            <td>gdgs</td>
                                            <td>gdgs</td>
                                            <td>gdgs</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div id="replenish_ye1"></div>
                                </div>
                            </div>
                            <div class="warningOrder">
                                <p>下列材料的库存已低于最低库存，请查看在途数量，如在途货物入库后库存依然不足，可下单采购</p>
                                <table class="ty-table ty-table-control specialTb" id="warningList">
                                    <thead>
                                    <tr>
                                        <td width="18%">材料名称</td>
                                        <td width="18%">材料代号</td>
                                        <td width="8%">型号</td>
                                        <td width="8%">规格</td>
                                        <td width="8%">计量单位</td>
                                        <td width="8%">当前库存</td>
                                        <td width="8%">最低库存</td>
                                        <td width="8%">不足数量</td>
                                        <td width="8%">在途数量</td>
                                        <td width="8%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>材料代号</td>
                                        <td>型号</td>
                                        <td>规格</td>
                                        <td>计量单位</td>
                                        <td>计量单位</td>
                                        <td>创建人</td>
                                        <td>gdgs</td>
                                        <td>gdgs</td>
                                        <td>gdgs</td>
                                        <td>gdgs</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="repStepTwo" id="matMatchSupplier">
                            <span class="hd" id="rderSource"></span>
                            <span class="hd" id="matDetails"></span>
                            <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5 freeBuy">自由购买</span>
                            <div class="riMt">
                                <div class="supplierList">
                                    <div>
                                        <div class="tabs__header clear">
                                            <div class="tabs__nav">
                                            </div>
                                        </div>
                                        <div class="tabs__content">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="repStepThr specialCol">
                            <div class="riMt">
                                <h4 class="ty-center">采购订单</h4>
                                <div class="hd" id="matchSupperInfo"></div>
                                <div class="clear">
                                    <div class="ty-left orderAddress">
                                        <span class="gapR"><span class="xing"></span>收货地点</span>
                                        <select id="deliveryAddress" onchange="isIncludeAddress($(this))" require>
                                        </select>
                                    </div>
                                    <div class="ty-right">
                                        <span class="gapR"><span class="xing"></span>订单号</span>
                                        <input type="text" value="" id="reissueOrderSn" require/>
                                    </div>
                                </div>
                                <div class="supplierInfo clear" id="matchSupper">
                                    <div class="spNm">
                                        <span class="gapR">供应商名称</span>
                                        <span class="spCd" need data-name="full_name"></span>
                                    </div>
                                    <div>
                                        <span class="spTl">简称</span>
                                        <span class="spCd" need data-name="supplier_name"></span>
                                    </div>
                                    <div>
                                        <span class="spTl">代号</span>
                                        <span class="spCd" need data-name="code_name"></span>
                                    </div>
                                    <div class="ty-right">
                                        <span class="spTl">订单总额</span>
                                        <span class="spInt"><span id="orderTotleAmount"></span> 元</span>
                                    </div>
                                </div>
                                <table class="ty-table ty-table-control" id="matBuyInfo">
                                    <thead>
                                    <tr>
                                        <td width="10%">材料名称</td>
                                        <td width="10%">材料代号</td>
                                        <td width="7%">型号</td>
                                        <td width="7%">规格</td>
                                        <td width="8%">计量单位</td>
                                        <td width="10%"><span class="xing"></span>购买数量</td>
                                        <td width="10%"><span class="xing"></span>要求到货日期</td>
                                        <td width="8%">单价</td>
                                        <td width="8%">总价</td>
                                        <td width="10%">备注</td>
                                        <td width="12%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                                <div class="nextStep">
                                    <p>您还可选择<span class="ty-color-red">该供应商供应的</span>其他材料。所选材料的采购信息填完后，可进行下一步。</p>
                                    <div class="nextBtn hd" id="formSaleMat"><span><span class="fa fa-circle-o" data-type="6"></span> 选购新销售订单所需的其他材料</span></div>
                                    <div class="nextBtn hd" id="otherWarningMat"><span><span class="fa fa-circle-o" data-type="0"></span> 选购库存已预警的材料</span></div>
                                    <div class="nextBtn"><span><span class="fa fa-circle-o" data-type="1"></span> 选购其他材料</span></div>
                                    <div class="nextBtn"><span><span class="fa fa-circle-o" data-type="2"></span> 不选购，进行下一步</span></div>
                                </div>
                                <div class="ty-center">
                                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="nextStep()" id="makesure">确定</span>
                                </div>
                            </div>
                        </div>
                        <div class="repStepFor specialCol">
                            <div class="riMt">
                                <div class="hd" id="otherMaterialType"></div>
                                <div class="elemFlex">
                                    <div class="supplierInfo" id="forSupplierInfo">
                                        <div class="spNm">
                                            <span class="spTl">供应商名称</span>
                                            <span class="spCd" need data-name="full_name"></span>
                                        </div>
                                        <div>
                                            <span class="spTl">简称</span>
                                            <span class="spCd" need data-name="supplier_name">旺旺食品有限公司</span>
                                        </div>
                                        <div>
                                            <span class="spTl">代号</span>
                                            <span class="spCd" need data-name="code_name"></span>
                                        </div>
                                    </div>
                                    <div class="supplierInfo">
                                        <div class="spTl">已选 <span id="checkedNum">0</span> 种</div>
                                        <div class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseOtherMat()">选择完毕</div>
                                    </div>
                                </div>
                                <table class="ty-table ty-table-control specialTb" id="otherMaterial">
                                    <thead>
                                    <tr>
                                        <td class="ty-empty"></td>
                                        <td>材料名称</td>
                                        <td>材料代号</td>
                                        <td>型号</td>
                                        <td>规格</td>
                                        <td>计量单位</td>
                                        <td>当前库存</td>
                                        <td>最低库存</td>
                                        <td class="warningThd">不足数量</td>
                                        <td>在途数量</td>
                                        <td>供应商</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <%-- 订单详情 --%>
                    <div class="orderDetails specialCol leftSpace">
                        <div class="riMt">
                            <h4 class="ty-center">采购订单</h4>
                            <div class="text-right">
                                <span class="gapR">订单号</span>
                                <span require data-name="sn">wwdjoadh00001</span>
                            </div>
                            <div class="supplierInfo">
                                <div class="spNm">
                                    <span class="spTl">供应商名称</span>
                                    <span class="spCd" require data-name="full_name"></span>
                                </div>
                                <div>
                                    <span class="spTl">订单总额</span>
                                    <span class="spCd"><span id="orderTlAmount"></span>元</span>
                                </div>
                            </div>
                            <div id="advancePayList"></div>
                            <table class="ty-table ty-table-control" id="orderDetailsList">
                                <thead>
                                <tr>
                                    <td width="10%">材料名称</td>
                                    <td width="8%">材料代号</td>
                                    <td width="8%">型号</td>
                                    <td width="8%">规格</td>
                                    <td width="8%">计量单位</td>
                                    <td width="10%">购买数量</td>
                                    <td width="10%">要求到货日期</td>
                                    <td width="10%">单价</td>
                                    <td width="8%">总价</td>
                                    <td width="12%">备注</td>
                                    <td width="6%">运费</td>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <%-- 订单修改 --%>
                    <div class="orderUpdate specialCol">
                        <div class="riMt">
                            <h4 class="ty-center">采购订单</h4>
                            <div class="ty-left orderAddress">
                                <span class="gapR"><span class="xing"></span>收货地点</span>
                                <select id="edit_deliveryAddress" onchange="isIncludeAddress($(this))" require>
                                </select>
                            </div>
                            <div class="text-right">
                                <span class="gapR"><span class="xing"></span>订单号</span>
                                <input type="text" value="" id="up_orderSn" require />
                            </div>
                            <div class="supplierInfo" id="up_supplierInfo">
                                <span id="up_supplier" class="hd"></span>
                                <div class="spNm">
                                    <span class="spTl">供应商名称</span>
                                    <span class="spCd" require data-name="supplier_name">旺旺食品有限公司</span>
                                </div>
                                <div>
                                    <span class="spTl">简称</span>
                                    <span class="spCd" require data-name="short_name">旺旺食品</span>
                                </div>
                                <div>
                                    <span class="spTl">代号</span>
                                    <span class="spCd" require data-name="code_name">ww</span>
                                </div>
                                <div>
                                    <span class="spTl">订单总额</span>
                                    <span class="spCd"><span id="up_orderTlAmount"></span>元</span>
                                </div>
                            </div>
                            <div id="up_advancePayList" style="display: none"></div>
                            <table class="ty-table ty-table-control" id="updateOrderList">
                                <thead>
                                <tr>
                                    <td width="10%">材料名称</td>
                                    <td width="8%">材料代号</td>
                                    <td width="6%">型号</td>
                                    <td width="6%">规格</td>
                                    <td width="8%">计量单位</td>
                                    <td width="10%"><span class="xing"></span>购买数量</td>
                                    <td width="10%"><span class="xing"></span>要求到货日期</td>
                                    <td width="8%">单价</td>
                                    <td width="8%">总价</td>
                                    <td width="8%">备注</td>
                                    <td width="12%">操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            <div class="ty-center ftGapT">
                                <span class="ty-btn ty-btn-blue ty-btn-big" onclick="orderUpdateSure()">确定</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/purchase/orderManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/purchase/dailyAffairsCommon.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
