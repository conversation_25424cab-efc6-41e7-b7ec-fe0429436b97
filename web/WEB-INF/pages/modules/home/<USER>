<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    .mar{ padding-left:30px;  }
    .staffContainer{ margin-right:-10px;   }
    .stf_Img{ width:50px;   }
    .stf_Img img{ display:inline-block; width:40px; height:40px; border-radius:20px; margin:0 auto;      }
    .staff{ overflow:hidden; padding:10px; margin:0 8px 8px 0; width:288px; height:144px; display: inline-block             }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>通讯录</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <form action="../user/toAddressList.do" method="post" class="ty-search">
                    <span class="ty-search-ttl"></span>按姓名查找：<input name="userName" type="text" class="ty-searchInput"/><input type="submit" value=""  class="ty-searchBtn" />
                </form>
                <ul class="ty-secondTab">
                    <li class="ty-active" >全部</li>
                </ul>
                <div class="ty-mainData">
                    <div class="staffContainer">
                        <c:forEach items="${users}" var="u">
                        <div class=" staff ty-circle-5 ty-panel">
                            <div class="stf_Img ty-left"><img src="<%= request.getAttribute("fileUrl") %>${u.imgPath}" onerror="this.src='../assets/oralResource/user.png'" alt="Uder"></div>
                            <div class="stf_Info ty-left">
                                <p>${u.userName} <span class="mar color6">${u.mobile}</span></p>
                                <p>${u.departName} <span class="mar">${u.postName}</span></p>
                                <p class="color6">QQ：${u.qq}</p>
                                <p class="color6">E-mail：${u.email}</p>
                            </div>
                            <div class="clr"></div>
                        </div>
                        </c:forEach>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%--<%@ include  file="../../common/footerTop.jsp"%>--%>
<%@ include  file="../../common/footerScript.jsp"%>
</body>
</html>
