<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%--<link rel="stylesheet" type="text/css" href="../css/attendance/attendance.css?v=SVN_REVISION" />--%>
<link rel="stylesheet" type="text/css" href="../css/home/<USER>" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed3">
    <%-- creater: 张旭博，2020-12-08 13:59:31，修改考勤 查看请假 查看按钮弹框 --%>
    <div class="bonceContainer bounce-blue" id="leaveRecord">
        <div class="bonceHead">
            <span class="bounce_title">请假记录</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="detail"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed3.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="confirm">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="content text-center"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue confirmBtn">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="tipError">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="content text-center">
                操作失败！
                <br>
                还有计划内工作的结果尚未编辑。
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="bounce_Fixed2.cancel()">我知道了</button>
        </div>
    </div>
    <%--其他日期的计划内工作--%>
    <div class="bonceContainer bounce-green" id="chooseOtherInnerWork" style="width: 900px">
        <div class="bonceHead">
            <span>其他日期的计划内工作</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table text-left">
                <thead>
                <tr>
                    <td>操作</td>
                    <td>工作内容</td>
                    <td>所属日期</td>
                    <td>计划开始时间</td>
                    <td>计划耗时</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green offSureBtn" onclick="sureChooseOtherInnerWork()">确定</button>
        </div>
    </div>
    <%--编辑日常工作--%>
    <div class="bonceContainer bounce-blue" id="editRoutineWork" style="width: 600px">
        <div class="bonceHead">
            <span>编辑日常工作</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceItem">
                <div class="bounceItem_title">计划内容</div>
                <div class="bounceItem_content">
                    <span class="routine_contentPlan"></span>
                </div>
            </div>
            <div class="bounceItem">
                <div class="bounceItem_title">计划开始时间</div>
                <div class="bounceItem_content">
                    <span class="routine_timePlanTime"></span>
                </div>
            </div>
            <div class="bounceItem">
                <div class="bounceItem_title">完成情况</div>
                <div class="bounceItem_content">
                    <select class="kj-input routineFulfillment" name="routineFulfillment" require style="width: 100%">
                        <option value="0">请在以下选项中选择</option>
                        <option value="1">已完成</option>
                        <option value="2">当日未做，或未做完</option>
                    </select>
                </div>
            </div>
            <div class="kj-panel panel_0"></div>
            <div class="kj-panel panel_1">
                <%--当日完成了该项工作--%>
                <div class="repeatPart">
                    <div class="kj-hr"></div>
                    <div class="text-right">
                        <span class="ty-btn ty-btn-blue ty-circle-2 controlBtn" onclick="addFactWorkForm($(this))">增加</span>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">开始时间</div>
                        <div class="bounceItem_content">
                            <select class="kj-input halfTime" name="timeFactTime" placeholder="请选择开始时间" require style="width: 100%"></select>
                        </div>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">工作内容</div>
                        <div class="bounceItem_content">
                            <textarea cols="30" rows="2" data-type="textarea" name="content" placeholder="请在此录入内容" class="kj-textarea autoFill" max="230"></textarea>
                            <div class="textMax text-right">0/230</div>
                        </div>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">实际耗时</div>
                        <div class="bounceItem_content">
                            <input class="kj-input short _hour" name="durationFactHour" placeholder="请输入" onkeyup="clearNum0(this)"/>小时
                            <input class="kj-input short _minute" name="durationFactMinute" placeholder="请输入" require onkeyup="clearNum0(this)" />分钟
                        </div>
                    </div>
                </div>
                <div class="repeatList"></div>
            </div>
            <div class="kj-panel panel_2"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue sureBtn" id="sureEditRoutineWorkBtn">确定</button>
        </div>
    </div>
    <%--增加当日计划内工作--%>
    <div class="bonceContainer bounce-blue" id="editInnerWork" style="width: 600px">
        <div class="bonceHead">
            <span>编辑计划内的工作</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">此项计划内的工作当日应该有进展。请按实际填写下表。</div>
            <div class="bounceItem">
                <div class="bounceItem_title">计划内容</div>
                <div class="bounceItem_content">
                    <span class="inner_contentPlan"></span>
                </div>
            </div>
            <div class="bounceItem">
                <div class="bounceItem_title">计划开始日期</div>
                <div class="bounceItem_content">
                    <span class="inner_timePlan"></span>
                </div>
                <div class="bounceItem_title">计划耗时</div>
                <div class="bounceItem_content">
                    <span class="inner_durationPlan"></span>小时
                </div>
            </div>
            <div class="bounceItem">
                <div class="bounceItem_title">完成情况</div>
                <div class="bounceItem_content">
                    <select class="kj-input fulfillment" name="fulfillment" require style="width: 100%">
                        <option value="0">请在以下选项中选择</option>
                        <option value="1">当日完成了该项工作</option>
                        <option value="2">该项工作当日做了一部分，剩余的改天再做</option>
                        <option value="3">该项工作当日做了一部分，剩余的不做了。关闭该项任务</option>
                        <option value="4">该项工作当日没做，改天再做</option>
                        <option value="5">该项工作当日没做，并且不做了。关闭该项任务</option>
                    </select>
                </div>
            </div>
            <div class="kj-panel panel_0"></div>
            <div class="kj-panel panel_1">
                <%--当日完成了该项工作--%>
                <div class="repeatPart">
                    <div class="kj-hr"></div>
                    <div class="text-right">
                        <span class="ty-btn ty-btn-blue ty-circle-2 controlBtn" onclick="addFactWorkForm($(this))">增加</span>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">当日开始时间</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanTime" id="radio_startInTime" value="1">
                                    <label for="radio_startInTime"></label> 于计划时间准时开始
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanTime" id="radio_startOtherTime" value="0">
                                    <label for="radio_startOtherTime"></label> 其他时间
                                </div>
                            </div>
                            <div class="extraInput">
                                <select class="kj-input halfTime" name="timeFactTime" placeholder="请选择开始时间" require style="width: 100%"></select>
                            </div>
                        </div>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">当日工作内容</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanContent" id="radio_allPlanContent" value="1">
                                    <label for="radio_allPlanContent"></label> 全部为计划的内容
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanContent" id="radio_otherContent" value="0">
                                    <label for="radio_otherContent"></label> 还需另行录入内容
                                </div>
                            </div>
                            <div class="extraInput">
                                <textarea cols="30" rows="2" data-type="textarea" name="content" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                                <div class="textMax text-right">0/230</div>
                            </div>
                        </div>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">实际耗时</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanHour" id="radio_allPlanHour" value="1">
                                    <label for="radio_allPlanHour"></label> 为计划耗时
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanHour" id="radio_otherHour" value="0">
                                    <label for="radio_otherHour"></label> 另行录入
                                </div>
                            </div>
                            <div class="extraInput">
                                <input class="kj-input short _hour" name="durationFactHour" placeholder="请输入" onkeyup="clearNum0(this)"/>小时
                                <input class="kj-input short _minute" name="durationFactMinute" placeholder="请输入" require onkeyup="clearNum0(this)" />分钟
                            </div>
                        </div>
                    </div>
                </div>
                <div class="repeatList"></div>
            </div>
            <div class="kj-panel panel_2">
                <%--该项工作当日做了一部分，剩余的改天再做--%>
                <div class="bounceItem">
                    <div class="bounceItem_title">未完成原因</div>
                    <div class="bounceItem_content">
                        <textarea cols="30" rows="2" data-type="textarea" name="reason" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                        <div class="textMax text-right">0/230</div>
                    </div>
                </div>

                <div class="repeatPart">
                    <div class="kj-hr"></div>
                    <div class="text-right">
                        <span class="ty-btn ty-btn-blue ty-circle-2 controlBtn" onclick="addFactWorkForm($(this))">增加</span>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">当日开始时间</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanTime" id="radio_startInTime_2" value="1">
                                    <label for="radio_startInTime_2"></label> 于计划时间准时开始
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanTime" id="radio_startOtherTime_2" value="0">
                                    <label for="radio_startOtherTime_2"></label> 其他时间
                                </div>
                            </div>
                            <div class="extraInput">
                                <select class="kj-input halfTime" name="timeFactTime" placeholder="请选择开始时间" re quire style="width: 100%"></select>
                            </div>
                        </div>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">当日工作内容</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanContent" id="radio_allPlanContent_2" value="1">
                                    <label for="radio_allPlanContent_2"></label> 全部为计划的内容
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanContent" id="radio_otherContent_2" value="0">
                                    <label for="radio_otherContent_2"></label> 还需另行录入内容
                                </div>
                            </div>
                            <div class="extraInput">
                                <textarea cols="30" rows="2" data-type="textarea" name="content" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                                <div class="textMax text-right">0/230</div>
                            </div>
                        </div>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">实际耗时</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanHour" id="radio_allPlanHour_2" value="1">
                                    <label for="radio_allPlanHour_2"></label> 为计划耗时
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanHour" id="radio_otherHour_2" value="0">
                                    <label for="radio_otherHour_2"></label> 另行录入
                                </div>
                            </div>
                            <div class="extraInput">
                                <input class="kj-input short _hour" name="durationFactHour" placeholder="请输入" onkeyup="clearNum0(this)" />小时
                                <input class="kj-input short _minute" name="durationFactMinute" placeholder="请输入" onkeyup="clearNum0(this)" require />分钟
                            </div>
                        </div>
                    </div>
                </div>
                <div class="repeatList"></div>
                <div class="kj-hr"></div>
                <div class="bounceItem">
                    请填写剩余部分的完成计划
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">预计开始日期</div>
                    <div class="bounceItem_content">
                        <input class="kj-input" id="inner2_timeContinueDate" name="timeContinueDate" require />
                    </div>
                    <div class="bounceItem_title">预计开始时间</div>
                    <div class="bounceItem_content">
                        <select class="kj-input halfTime" name="timeContinueTime" require></select>
                    </div>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">计划耗时</div>
                    <div class="bounceItem_content">
                        <input class="kj-input short _hour" name="continuePlanHour" onkeyup="clearNum0(this)" />小时
                        <input class="kj-input short _minute" name="continuePlanMinute" require onkeyup="clearNum0(this)" />分钟
                    </div>
                </div>
            </div>
            <div class="kj-panel panel_3">
                <%--该项工作当日做了一部分，剩余的不做了。关闭该项任务--%>
                <div class="bounceItem">
                    <div class="bounceItem_title">关闭原因</div>
                    <div class="bounceItem_content">
                        <textarea cols="30" rows="2" data-type="textarea" name="reason" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                        <div class="textMax text-right">0/230</div>
                    </div>
                </div>
                <div class="repeatPart">
                    <div class="kj-hr"></div>
                    <div class="text-right">
                        <span class="ty-btn ty-btn-blue ty-circle-2 controlBtn" onclick="addFactWorkForm($(this))">增加</span>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">当日开始时间</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanTime" id="radio_startInTime_3" value="1">
                                    <label for="radio_startInTime_3"></label> 于计划时间准时开始
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanTime" id="radio_startOtherTime_3" value="0">
                                    <label for="radio_startOtherTime_3"></label> 其他时间
                                </div>
                            </div>
                            <div class="extraInput">
                                <select class="kj-input halfTime" name="timeFactTime" placeholder="请选择开始时间" require style="width: 100%"></select>
                            </div>
                        </div>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">当日工作内容</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanContent" id="radio_allPlanContent_3" value="1">
                                    <label for="radio_allPlanContent_3"></label> 全部为计划的内容
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanContent" id="radio_otherContent_3" value="0">
                                    <label for="radio_otherContent_3"></label> 还需另行录入内容
                                </div>
                            </div>
                            <div class="extraInput">
                                <textarea cols="30" rows="2" data-type="textarea" name="content" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                                <div class="textMax text-right">0/230</div>
                            </div>
                        </div>
                    </div>
                    <div class="bounceItem">
                        <div class="bounceItem_title">实际耗时</div>
                        <div class="bounceItem_content">
                            <div class="chooseDo">
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanHour" id="radio_allPlanHour_3" value="1">
                                    <label for="radio_allPlanHour_3"></label> 为计划耗时
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" name="isPlanHour" id="radio_otherHour_3" value="0">
                                    <label for="radio_otherHour_3"></label> 另行录入
                                </div>
                            </div>
                            <div class="extraInput">
                                <input class="kj-input short _hour" name="durationFactHour" placeholder="请输入" onkeyup="clearNum0(this)" />小时
                                <input class="kj-input short _minute" name="durationFactMinute" placeholder="请输入" require onkeyup="clearNum0(this)" />分钟
                            </div>
                        </div>
                    </div>
                </div>
                <div class="repeatList"></div>
            </div>
            <div class="kj-panel panel_4">
                <%--该项工作当日没做，改天再做--%>
                <div class="bounceItem">
                    <div class="bounceItem_title">未完成原因</div>
                    <div class="bounceItem_content">
                        <textarea cols="30" rows="2" data-type="textarea" name="reason" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                        <div class="textMax text-right">0/230</div>
                    </div>
                </div>
                <div class="kj-hr"></div>
                <div class="bounceItem">
                    该项工作计划将于哪天、何时继续？请确定！
                </div>
                <div class="bounceItem">
                    <div class="bounceItem">
                        <div class="bounceItem_title">预计开始日期</div>
                        <div class="bounceItem_content">
                            <input class="kj-input" id="inner4_timeContinueDate" name="timeContinueDate" require />
                        </div>
                        <div class="bounceItem_title">预计开始时间</div>
                        <div class="bounceItem_content">
                            <select class="kj-input halfTime" name="timeContinueTime" require></select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="kj-panel panel_5">
                <%--该项工作当日没做，并且不做了。关闭该项任务--%>
                <div class="bounceItem">
                    <div class="bounceItem_title">关闭原因</div>
                    <div class="bounceItem_content">
                        <textarea cols="30" rows="2" data-type="textarea" name="reason" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                        <div class="textMax text-right">0/230</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue sureBtn" id="sureEditInnerWorkBtn">确定</button>
        </div>
    </div>
    <%--增加当日所做的计划外工作--%>
    <div class="bonceContainer bounce-green" id="editOutterWork" style="width: 600px">
        <div class="bonceHead">
            <span>当日所做的计划外工作</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">请录入当日所做的计划外工作，其开始时间或耗时是否填写，请按公司规定。</div>
            <div class="bounceItem">
                <div class="bounceItem_title">工作内容</div>
                <div class="bounceItem_content">
                    <textarea cols="30" rows="2" data-type="textarea" name="content" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                    <div class="textMax text-right">0/230</div>
                </div>
            </div>
            <div class="bounceItem">
                <div class="bounceItem_title">所属日期</div>
                <div class="bounceItem_content">
                    <span class="thisDayWeek"></span>
                </div>
            </div>
            <div class="repeatPart">
                <div class="text-right">
                    <span class="ty-btn ty-btn-blue ty-circle-2 controlBtn" onclick="addFactWorkForm($(this))">增加</span>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">开始时间</div>
                    <div class="bounceItem_content">
                        <select class="kj-input halfTime" name="timeFactTime" placeholder="请选择开始时间" require style="width: 100%"></select>
                    </div>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">耗时</div>
                    <div class="bounceItem_content">
                        <input class="kj-input short _hour" name="durationFactHour" placeholder="请输入" onkeyup="clearNum0(this)" />小时
                        <input class="kj-input short _minute" name="durationFactMinute" placeholder="请输入" require onkeyup="clearNum0(this)" />分钟
                    </div>
                </div>
            </div>
            <div class="repeatList"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green sureBtn" id="sureEditOutterWorkBtn">确定</button>
        </div>
    </div>
    <%--增加近日的工作计划--%>
    <div class="bonceContainer bounce-green" id="editRecentWork" style="width: 600px">
        <div class="bonceHead">
            <span>近日的工作计划</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">请录入您下个工作日或近日要做的工作。本页面上各项均需填写内容后方可点击“确定”。</div>
            <div class="bounceItem">
                <div class="bounceItem_title">工作内容</div>
                <div class="bounceItem_content">
                    <textarea cols="30" rows="2" data-type="contentPlan" name="contentPlan" placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                    <div class="textMax text-right">0/230</div>
                </div>
            </div>
            <div class="bounceItem">
                <div class="bounceItem_title">预计开始日期</div>
                <div class="bounceItem_content">
                    <input class="kj-input" id="recent_timePlanDate" name="timePlanDate" require />
                </div>
                <div class="bounceItem_title">预计开始时间</div>
                <div class="bounceItem_content">
                    <select class="kj-input halfTime" name="timePlanTime" require></select>
                </div>
            </div>
            <div class="bounceItem">
                <div class="bounceItem_title">计划耗时</div>
                <div class="bounceItem_content">
                    <input class="kj-input short _hour" name="durationPlanHour" onkeyup="clearNum0(this)" />小时
                    <input class="kj-input short _minute" name="durationPlanMinute" require onkeyup="clearNum0(this)" />分钟
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green sureBtn" id="sureEditRecentWorkBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2020-12-08 14:35:11，修改记录 - 查看工作记录  --%>
    <div class="bonceContainer bounce-blue" id="workRecordSee" style="width: 1100px">
        <div class="bonceHead">
            <span class="name">工作记录</span>
            <a class="bounce_close cancelBtn" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-panel">
                <div class="item">
                    <div class="item_content">
                        <span class="currentDayWeek"></span>
                    </div>
                    <div class="item_content" style="width: 400px">
                        <span class="workReason"></span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_content leave">
                        当日有请假：
                        <div class="leaveList"></div>
                    </div>
                    <div class="item_content timeSpentTotalVisible">
                        当日已录入内容合计耗时：<span class="timeSpentTotal"></span>小时
                    </div>
                </div>
            </div>
            <div class="ty-panel routineWork">
                <div class="ty-panel-title"><span class="title">日常工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>结果</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel innerWork">
                <div class="ty-panel-title"><span class="title">计划内的工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>计划耗时</td>
                            <td>结果</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short outterWork">
                <div class="ty-panel-title"><span class="title">当日所做的计划外工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td style="width: 500px">工作内容</td>
                            <td>所属日期</td>
                            <td>开始时间</td>
                            <td>耗时</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short recentWork">
                <div class="ty-panel-title"><span class="title">近日的工作计划</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>预计开始时间</td>
                            <td>预计耗时</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed2.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2020-12-08 14:35:11，工作记录 - 查看未来  --%>
    <div class="bonceContainer bounce-blue" id="workFutureSee" style="width: 1100px">
        <div class="bonceHead">
            <span class="name">工作记录</span>
            <a class="bounce_close cancelBtn" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-panel">
                <div class="item">
                    <div class="item_content">
                        <span class="currentDayWeek"></span>
                    </div>
                    <div class="item_content" style="width: 400px">
                        <span class="workReason"></span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_content leave">
                        当日有请假：
                        <div class="leaveList"></div>
                    </div>
                </div>
            </div>
            <div class="ty-panel routineWork">
                <div class="ty-panel-title"><span class="title">日常工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel innerWork">
                <div class="ty-panel-title"><span class="title">计划内的工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>计划耗时</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed2.cancel()">关闭</button>
        </div>
    </div>
    <%--新增日常工作--%>
    <div class="bonceContainer bounce-green" id="editDailyWork" style="width:550px;">
        <div class="bonceHead">
            <span class="bounce_title">新增日常工作</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form class="ty-form" id="form_editDailyWork" style="width: 400px; margin: 0 auto">
                <div class="formItem">
                    <span><span class="ty-color-red">*</span> 工作内容</span>
                    <div>
                        <textarea cols="30" rows="2" data-type="textarea" name="content" require placeholder="请输入内容，最多可输入230字。" class="kj-textarea autoFill" max="230"></textarea>
                        <div class="textMax text-right">0/230</div>
                    </div>

                </div>
                <div class="formItem">
                    <span><span class="ty-color-red">*</span> 频次</span>
                    <select data-type="select" name="frequency" require style="width: 100%">
                        <option value="">请选择</option>
                        <option value="1">每日一次</option>
                        <option value="2">每周一次</option>
                    </select>
                </div>
                <div class="kj-panel panel_"></div>
                <div class="kj-panel panel_1">
                    <div class="formItem">
                        <span>计划开始的时间（如不好确定，可不设置）</span>
                        <select class="kj-input halfTime" name="planTimeStr" style="width: 100%"></select>
                    </div>
                    <div class="formItem">
                        <div>
                            <span>从几点开始提醒（如为其他时间，可予修改）</span>
                            <select class="kj-input halfTime" name="remindTimeStr" style="width: 100%"></select>
                        </div>
                    </div>
                </div>
                <div class="kj-panel panel_2">
                    <div class="formItem">
                        <span><span class="ty-color-red">*</span> 具体为星期几</span>
                        <select class="kj-input weekTime" name="dayWeek" require style="width: 100%">
                            <option value="">请选择</option>
                            <option value="1">每周星期一</option>
                            <option value="2">每周星期二</option>
                            <option value="3">每周星期三</option>
                            <option value="4">每周星期四</option>
                            <option value="5">每周星期五</option>
                            <option value="6">每周星期六</option>
                            <option value="0">每周星期日</option>
                        </select>
                    </div>
                    <div class="formItem">
                        <span>计划开始的时间（如不好确定，可不设置）</span>
                        <select class="kj-input halfTime" name="planTimeStr" style="width: 100%"></select>
                    </div>
                    <div class="formItem">
                        <div>
                            <span>从几点开始提醒（如为其他时间，可予修改）</span>
                            <select class="kj-input halfTime" name="remindTimeStr" style="width: 100%"></select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2 sureBtn" id="sureEditDailyWorkBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- creator: 张旭博，2020-12-08 13:55:31，提示  --%>
    <div class="bonceContainer bounce-blue" id="bounceFixed_tip">
        <div class="bonceHead">
            <span class="name">！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tips text-center"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue sureBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2021-11-24 13:55:31， 日常工作设置 --%>
    <div class="bonceContainer bounce-blue" id="dailyWorkSetting" style="width: 600px">
        <div class="bonceHead">
            <span class="name">日常工作设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="min-height: 260px">
            <div class="ty-alert">
                您的日常工作安排如下
                <div class="btn-group text-right">
                    <button class="ty-btn ty-btn-green ty-btn-big ty-circle-2" onclick="editDailyWork()">新增日常工作</button>
                </div>
            </div>
            <table class="kj-table table-dailyWork">
                <thead>
                <td>工作内容</td>
                <td>频次</td>
                <td>计划开始的时间</td>
                <td>操作</td>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2020-12-08 13:55:31，修改工作记录  --%>
    <div class="bonceContainer bounce-blue" id="workChange" style="width: 1100px">
        <div class="bonceHead">
            <span class="name">修改工作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-panel">
                <div class="item">
                    <div class="item_content">
                        <span class="currentDayWeek"></span>
                    </div>
                    <div class="item_content" style="width: 400px">
                        <span class="workReason"></span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_content leave">
                        当日有请假：
                        <div class="leaveList"></div>
                    </div>
                    <div class="item_content timeSpentTotalVisible">
                        当日已录入内容合计耗时：<span class="timeSpentTotal"></span>小时
                    </div>
                </div>
                <div class="item lastOperateTimeVisible">
                    <div class="item_content">
                        最后的操作时间 <span class="lastOperateTime"></span>
                    </div>
                </div>
            </div>
            <div class="ty-alert">您仅可修改当日所做的日常工作和计划外工作。</div>
            <div class="ty-panel routineWork">
                <div class="ty-panel-title"><span class="title">日常工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>结果</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short outterWork">
                <div class="ty-panel-title"><span class="title">当日所做的计划外工作</span> <div class="btn-group text-right"><button class="ty-btn ty-btn-green ty-circle-2" type="add" name="editOutterWork">增加</button></div></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td style="width: 400px">工作内容</td>
                            <td>所属日期</td>
                            <td>开始时间</td>
                            <td>耗时</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="changeWorkConfirm(1)">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2020-12-08 13:55:31，工作记录的修改记录  --%>
    <div class="bonceContainer bounce-blue" id="workRecord" style="width: 700px">
        <div class="bonceHead">
            <span class="name">工作记录的修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">当前资料为第 <span class="changeNum"></span> 次修改后的结果。</div>
            <div class="ty-panel-content">
                <table class="kj-table text-left">
                    <thead>
                    <tr>
                        <td>资料状态</td>
                        <td>操作</td>
                        <td>创建人/修改人</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-06-28 14:35:11，录入回复  --%>
    <div class="bonceContainer bounce-blue" id="inputReply">
        <div class="bonceHead">
            <span class="name">录入回复</span>
            <a class="bounce_close cancelBtn" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div  style="width: 420px; margin: auto">
                <textarea cols="30" rows="1" data-type="textarea" name="content" placeholder="请在此录入内容" class="kj-textarea autoFill" max="50"></textarea>
                <div class="textMax text-right">0/50</div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 cancelBtn" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue sureBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-green" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-green" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--温馨提示--%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">很遗憾，上月已经结账了！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-red" onclick="bounce.cancel()">我知道了</span>
        </div>-
    </div>
    <%-- creator: 张旭博，2020-12-08 13:55:31，录入当日的工作内容  --%>
    <div class="bonceContainer bounce-blue" id="workInput" style="width: 1100px">
        <div class="bonceHead">
            <span class="name">录入当日的工作内容</span>
            <a class="bounce_close" onclick="closeAndFreshWork()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-panel pastTip">
                <div class="ty-alert tip0">您尚未提交这天的工作记录！</div>
                <div class="ty-alert tip1">请尽快录入，并尽快正式提交。</div>
                <div class="ty-alert tip2">
                    <div>
                        <div>页面上是您所保存的草稿，您可将其正式提交至系统，也可修改或增加内容后再正式提交。</div>
                        <div>注意：修改后，之前的内容将被覆盖！！</div>
                    </div>
                </div>
            </div>
            <div class="ty-panel">
                <div class="item">
                    <div class="item_content">
                        <span class="currentDayWeek"></span>
                    </div>
                    <div class="item_content" style="width: 400px">
                        <span class="workReason"></span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_content leave">
                        当日有请假：
                        <div class="leaveList"></div>
                    </div>
                    <div class="item_content timeSpentTotalVisible">
                        当日已录入内容合计耗时：<span class="timeSpentTotal"></span>小时
                    </div>
                </div>
                <div class="item lastOperateTimeVisible">
                    <div class="item_content">
                        最后的操作时间 <span class="lastOperateTime"></span>
                    </div>
                </div>
            </div>
            <div class="kj-hr"></div>
            <div class="reviewPart panel_review"></div>
            <div class="kj-hr panel_review"></div>
            <div class="ty-panel routineWork">
                <div class="ty-panel-title"><span class="title">日常工作</span></div>
                <div class="ty-panel-content">
                    <div class="ty-alert">
                        点击“编辑”，以填写以下各项工作的进度
                        <div class="btn-group text-right"><button class="ty-btn ty-btn-green ty-circle-2" onclick="dailyWorkBtn()">设置</button></div>
                    </div>
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>结果</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel innerWork">
                <div class="ty-panel-title"><span class="title">计划内的工作</span></div>
                <div class="ty-panel-content">
                    <div class="ty-alert">
                        请根据实际填写以下各项工作的进度。如还提前完成了其他日期的计划内工作，请点击“其他日期的计划内工作。”
                        <div class="btn-group text-right"><button class="ty-btn ty-btn-green ty-circle-2" type="btn" name="otherInnerWork">其他日期的计划内工作</button></div>
                    </div>
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>计划耗时</td>
                            <td>结果</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short outterWork">
                <div class="ty-panel-title"><span class="title">当日所做的计划外工作</span> <div class="btn-group text-right"><button class="ty-btn ty-btn-green ty-circle-2" type="add" name="editOutterWork">增加</button></div></div>
                <div class="ty-panel-content">
                    <div class="ty-alert">
                        当日如做了计划以外的工作，可点击“增加”按钮，以录入至系统。
                    </div>
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td style="width: 400px">工作内容</td>
                            <td>所属日期</td>
                            <td>开始时间</td>
                            <td>耗时</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short recentWork">
                <div class="ty-panel-title"><span class="title">近日的工作计划</span> <div class="btn-group text-right"><button class="ty-btn ty-btn-green ty-circle-2" type="add" name="editRecentWork">增加</button></div></div>
                <div class="ty-panel-content">
                    <div class="ty-alert">
                        请点击“增加”按钮，以增加下个工作日或近日要做的工作。
                    </div>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>预计开始时间</td>
                            <td>预计耗时</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="closeAndFreshWork()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" id="sureSaveWorkBtn"  onclick="saveWorkConfirm(2)">正式提交</button>
        </div>
    </div>
    <%-- creator: 张旭博，2020-12-08 14:35:11，工作记录  --%>
    <div class="bonceContainer bounce-blue" id="workSee" style="width: 1100px">
        <div class="bonceHead">
            <span class="name">工作记录</span>
            <a class="bounce_close cancelBtn" onclick="closeAndFreshWork()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-panel">
                <div class="item">
                    <div class="item_content">
                        <span class="currentDayWeek"></span>
                    </div>
                    <div class="item_content" style="width: 400px">
                        <span class="workReason"></span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_content leave">
                        当日有请假：
                        <div class="leaveList"></div>
                    </div>
                    <div class="item_content timeSpentTotalVisible">
                        当日已录入内容合计耗时：<span class="timeSpentTotal"></span>小时
                    </div>
                </div>
                <div class="item lastOperateTimeVisible">
                    <div class="item_content">
                        最后的操作时间 <span class="lastOperateTime"></span>
                    </div>
                    <div class="item_content workRecordBtn">
                        <button class="ty-btn ty-btn-blue ty-circle-2" onclick="workRecordBtn()">修改记录</button>
                    </div>
                </div>
            </div>
            <div class="kj-hr"></div>
            <div class="reviewPart panel_review">
                <div class="review-item-avatar">
                    <div class="review-avatar">
                        <div class="review-item">
                            <div class="review-title">
                                XXX XXXX-XX-XX XX:XX:XX点评
                                <div class="btn-group text-right">
                                    <button class="ty-btn ty-btn-blue ty-circle-2" onclick="replyReview()">回复</button>
                                    <button class="ty-btn ty-btn-red ty-circle-2" onclick="deleteReview()">删除</button>
                                </div>
                            </div>
                            <div class="review-content">负栋之柱，多于南亩之农夫；架梁之椽，多于机上之工女；钉头磷磷，多于在庾之粟粒；瓦缝参差，多于周身之帛缕；</div>
                        </div>
                    </div>
                    <div class="reply-avatar">
                        <div class="review-item">
                            <div class="review-title">
                                XXX XXXX-XX-XX XX:XX:XX点评
                            </div>
                            <div class="review-content">负栋之柱，多于南亩之农夫；架梁之椽，多于机上之工女；钉头磷磷，多于在庾之粟粒；瓦缝参差，多于周身之帛缕；</div>
                        </div>
                        <div class="review-item">
                            <div class="review-title">
                                XXX XXXX-XX-XX XX:XX:XX点评
                            </div>
                            <div class="review-content">负栋之柱，多于南亩之农夫；架梁之椽，多于机上之工女；钉头磷磷，多于在庾之粟粒；瓦缝参差，多于周身之帛缕；</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="kj-hr panel_review"></div>
            <div class="ty-panel routineWork">
                <div class="ty-panel-title"><span class="title">日常工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>结果</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel innerWork">
                <div class="ty-panel-title"><span class="title">计划内的工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>计划耗时</td>
                            <td>结果</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short outterWork">
                <div class="ty-panel-title"><span class="title">当日所做的计划外工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td style="width: 500px">工作内容</td>
                            <td>所属日期</td>
                            <td>开始时间</td>
                            <td>耗时</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short recentWork">
                <div class="ty-panel-title"><span class="title">近日的工作计划</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>预计开始时间</td>
                            <td>预计耗时</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 cancelBtn" onclick="closeAndFreshWork()">取消</button>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="changeAttendanceBtn()">修改</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>我的工作记录</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <input id="pageNum" type="hidden" value="1" />
            <div class="ty-container" id="home">
                <div class="backBtn" style="display: none;">
                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="backToMainBtn" onclick="backToMain()">返回工作记录主页</button>
                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="back()">返回上一页</button>
                </div>
                <%--主页面--%>
                <div class="page main" page="main">
                    <%-- //  考勤管理初始页 顶部操作--%>
                    <div class="workQuery">
                        <div class="ty-alert btn-input">
                            今天是 <span class="ty-color-blue nowDayWeek"><span class="nowDay_date"></span> <span class="workOrRest"></span></span>
                            <div class="btn-group text-right">
                                <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2" onclick="dailyWorkBtn()">日常工作设置</button>
                                <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2" onclick="workInputBtn()">录入今天的工作内容</button>
                            </div>
                        </div>
                        <div class="ty-alert btn-back" style="display: none">
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 back" data-name="back">返回工作记录主页面</button>
                        </div>
                        <div class="ty-alert btn-input">
                            <div class="queryItem" style="width: 500px">
                                查看其他月份的工作记录
                                <input class="kj-input kj-input-blue" id="otherMonthWork" placeholder="请选择月份"/>
                            </div>
                            <div class="btn-group text-right">
                                <div class="queryItem" style="width: 300px">
                                    查看工作年报
                                    <input class="kj-input kj-input-blue" id="yearReport" placeholder="请选择年份"/>
                                </div>
                                <div class="queryItem" style="width: 300px">
                                    查看工作月报
                                    <input class="kj-input kj-input-blue" id="monthReport" placeholder="请选择月份"/>
                                </div>
                            </div>
                        </div>
                        <div class="ty-alert thisMonth"></div>
                        <table class="kj-table tbl_workCount">
                            <thead>
                            <tr>
                                <td>共有工作日</td>
                                <td>工作日之外的加班天数</td>
                                <td>截至目前应交天数</td>
                                <td>截至目前未交天数</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div class="ty-alert">
                            ※ <span id="attendanceTip">点击某天，即可查看这天的工作记录。</span>
                        </div>
                        <%--<div class="ty-alert">--%>
                        <%--※ 日历中某日期如带有<span class="ty-color-red">！！</span>标识，表示这天应该有工作记录，但尚未提交。--%>
                        <%--</div>--%>
                    </div>

                    <%-- //  考勤管理初始页--%>

                    <div class="ty-tblContainer tbl_month"></div>
                </div>
                <div class="page" page="monthReport" style="display: none; width: 1000px">
                    <div class="ty-alert">
                        <span class="tips"></span>
                        <div class="btn-group text-right">
                            <span class="link-blue seeAllWorkBtn" onclick="seeMonthReportDetail()">查看本月全部工作</span>
                        </div>
                    </div>
                    <table class="kj-table">
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="monthReportDetail" style="display: none">
                    <div class="ty-alert"><span class="tips"></span></div>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td rowspan="2">类别</td>
                            <td rowspan="2">创建时间</td>
                            <td colspan="2">计划</td>
                            <td rowspan="2">结果</td>
                            <td colspan="2">工作记录</td>
                        </tr>
                        <tr>
                            <td>内容</td>
                            <td>时间</td>
                            <td>时间</td>
                            <td>内容</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>

    var setStatus = "${status}" ; // status 1-已设置 0-未设置
    // 获取开始日期
    var startUp = "${startUsingSystemTime}";
    // 获取时间误差
    var hostTime = <%= System.currentTimeMillis() %>;

    var diff = hostTime - new Date().getTime();
    if(diff < 30000){
        diff = 0;
    }
    var startMonth = startUp.substr(0,7) + '-02' ;
    var ssEnd = new Date(hostTime).format('yyyy-MM') + '-20';
</script>
<script src="../script/home/<USER>"></script>
<script src="../script/home/<USER>"></script>
</body>
</html>
