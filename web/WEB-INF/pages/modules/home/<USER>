<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2021/2/24
  Time: 10:38
  To change this template use File | Settings | File Templates.
--%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/attendance/attendance.css?v=SVN_REVISION" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <%--confirm --%>
    <div class="bonceContainer bounce-blue" id="tipConfirm">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.show($('#updateAttend'))"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">您的修改需经核实，并经审批后生效。<br/>确定提交修改申请吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big bounce-cancel" onclick="bounce_Fixed.show($('#updateAttend'))">取消</span>
            <span class="ty-btn ty-circle-2 ty-btn-big bounce-ok" onclick="sureChangeAttendance()">确定</span>
        </div>
    </div>
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="tipRepeat">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">您提交的修改还未审批，请审批后再操作</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%-- updator: 侯杏哲 2018-4-28  考勤设置 选择部门 --%>
    <div class="bonceContainer bounce-green" id="scanSet">
        <div class="bonceHead">
            <span>请选择使用部门</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel() "></a>
        </div>
        <div class="bonceCon">
            <div class="departTree">
                <ul class="ty-left" id="all" >
                    <li>
                        <div lass="departid" info="all">
                            <i class="fa fa-angle-right"></i> <span>全部</span>
                            <i class="fa fa-plus-square plusAll" onclick="plus($(this), event)"></i>
                        </div>
                        <ul id="allRight"></ul>
                    </li>
                </ul>
                <div class="ty-left arrow"><i class="fa fa-arrows-h"></i></div>
                <form class="ty-left" id="deparCon">
                    <input type="hidden" name="categoryId" id="categoryId">
                    <ul id="nowRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel() ">取消</span>
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-2" id="selectDepartOk" >确定</span>
        </div>
    </div>
    <%-- updater : hxz 2018-04-27  提示 其他部门未设置， 是否设置 --%>
    <div class="bonceContainer bounce-green" id="setAnotherTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="AnotherTip">还有部门尚未设置考勤时间</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="setAnother()">去设置</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="setAttenceDate()">暂不设置</span>
        </div>
    </div>
    <%-- updater : hxz 2018-04-27  作息时间 设置 --%>
    <div class="bonceContainer bounce-green" id="attenceOtherSetting" style="width:600px;">
        <div class="bonceHead">
            <span>其他考勤设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="firstShow">请确定开始使用本系统记录考勤的日期<input type="text" class="" id="roleStartDate" /></p>
            <p class="firstShow">请确每日考勤录入时间
                <select id="attendanceTime">
                    <option value="07:30">07:30</option>
                    <option selected value="08:00">08:00</option>
                    <option value="08:30">08:30</option>
                    <option value="09:00">09:00</option>
                    <option value="09:30">09:30</option>
                    <option value="10:00">10:00</option>
                    <option value="10:30">10:30</option>
                    <option value="11:00">11:00</option>
                </select>
            </p>
            <p class="atTip firstShow">如考勤在此之前未录入，则所有职工将被记为<span>旷工</span>。您需要逐条去修改。</p>
            <p>请根据您的实际情况，重新勾选确定本月及下月的作息时间。</p>
            <div class="">
                <ul class="ty-secondTab monthsNav" id="months">
                    <li>2018年11月</li>
                    <li>2018年12月</li>
                </ul>
                <div id="clendars" class="clendars"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-2" onclick="dateSetOk()">确定</span>
        </div>
    </div>
    <%--  creater 姚宗涛 2018/2/9  08:58:00  修改考勤  查看加班记录  详情 查看按钮 弹框--%>
    <div class="bonceContainer bounce-green" id="fix_overtimeDetails">
        <div class="bonceHead">
            <span>加班记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
        </div>
    </div>
    <%-- creater 姚宗涛 2018/2/8 16:35:09 修改考勤 查看请假 查看按钮弹框 --%>
    <div class="bonceContainer bounce-green" id="details">
        <div class="bonceHead">
            <span>请假记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clearFlt">
                <dl>
                    <dd><span class="leaveName">王金锁</span> <span class="leaveTime">2017年10月10日</span>请假情况 </dd>
                </dl>
            </div>
            <div id="seeleaveDetail">
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%-- creater 姚宗涛 2018/2/8 16:35:09 修改考勤 查看请假 查看按钮弹框 --%>
    <div class="bonceContainer bounce-blue" id="leaveRecord">
        <div class="bonceHead">
            <span class="bounce_title">请假记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="detail"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- creater 张旭博 2020/11/13 16:35:09 修改考勤 弹窗--%>
    <div class="bonceContainer bounce-blue" id="updateAttend" style="width: 600px">
        <div class="bonceHead">
            <span class="bounce_title">修改考勤</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">
                下表中带 <span class="ty-color-red">*</span> 的必须填写，且考勤修改后需经审批后方才生效。
            </div>
            <div class="mainChange">
                <div class="bounceItem">
                    <div class="bounceItem_title">修改理由 <span class="ty-color-red">*</span></div>
                    <div class="bounceItem_content">
                        <input class="kj-input" type="text" name="updateDesc" placeholder="所填写的理由最多不得超过30字" require onkeyup="countWords($(this),30)" style="width: 100%">
                        <div class="textMax text-right" max="30"></div>
                    </div>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">上班考勤 <span class="ty-color-red">*</span></div>
                    <div class="bounceItem_content">
                        <div class="ty-radio">
                            <input type="radio" name="upIsNormal" id="up_normal" value="1">
                            <label for="up_normal"></label> 正常
                        </div>
                        <div class="ty-radio">
                            <input type="radio" name="upIsNormal" id="up_late" value="0">
                            <label for="up_late"></label> 迟到
                        </div>
                    </div>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">下班考勤 <span class="ty-color-red">*</span></div>
                    <div class="bounceItem_content">
                        <div class="ty-radio">
                            <input type="radio" name="downIsNormal" id="down_normal" value="1">
                            <label for="down_normal"></label> 正常
                        </div>
                        <div class="ty-radio">
                            <input type="radio" name="downIsNormal" id="down_early" value="0">
                            <label for="down_early"></label> 早退
                        </div>
                    </div>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">其他</div>
                    <div class="bounceItem_content otherInput">
                        <div class="ty-checkbox">
                            <input type="checkbox" name="isLeave" id="has_leave" value="0">
                            <label for="has_leave"></label> 有请假
                        </div>
                        <div class="ty-checkbox">
                            <input type="checkbox" name="isOverTime" id="has_overtime" value="1">
                            <label for="has_overtime"></label> 有加班
                        </div>
                        <div class="ty-checkbox">
                            <input type="checkbox" name="isAbsenteeism" id="has_out" value="2">
                            <label for="has_out"></label> 有旷工
                        </div>
                    </div>
                </div>
            </div>
            <div class="otherChange">
                <div class="kj-panel leavePart" style="display:none;">
                    <div class="ty-alert">
                        请录入请假信息
                        <div class="btn-group text-right">
                            <span class="ty-btn ty-btn-green ty-circle-2" onclick="addNewInput($(this))">增加新的请假信息</span>
                        </div>
                    </div>
                    <div class="input_model" >
                        <div class="inputInfo_item">
                            <div class="text-right inputInfo_item_del">
                                <span class="ty-btn ty-btn-red ty-circle-2" title="删除此条请假信息" onclick="delInput($(this))">删除此条请假信息</span>
                            </div>
                            <div class="inputInfo_item_content">
                                <div class="bounceItem">
                                    <div class="bounceItem_title">请假类型 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select" name="leaveType" require>
                                            <option value="">---请选择---</option>
                                            <option value="1">事假</option>
                                            <option value="2">病假</option>
                                            <option value="3">年假</option>
                                            <option value="4">调休</option>
                                            <option value="5">婚假</option>
                                            <option value="6">产假</option>
                                            <option value="7">陪产假</option>
                                            <option value="8">路途假</option>
                                            <option value="9">其他</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="bounceItem">
                                    <div class="bounceItem_title">开始时间 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select halfTime" name="leaveBeginDate" require></select>
                                    </div>
                                    <div class="bounceItem_title">结束时间 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select halfTime" name="leaveEndDate" require></select>
                                    </div>
                                </div>
                                <div class="bounceItem">
                                    <div class="bounceItem_title">请假说明</div>
                                    <div class="bounceItem_content">
                                        <input class="kj-input" type="text" placeholder="如无可忽略。录入内容最多不得超过30字" name="leaveReason" onkeyup="countWords($(this),30)" style="width: 100%">
                                        <div class="textMax text-right" max="30"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inputInfo_avatar"></div>
                </div>
                <div class="kj-panel overTimePart" style="display: none;">
                    <div class="ty-alert">
                        请录入加班信息
                        <div class="btn-group text-right">
                            <span class="ty-btn ty-btn-green ty-circle-2" onclick="addNewInput($(this))">增加新的加班信息</span>
                        </div>
                    </div>
                    <div class="input_model">
                        <div class="inputInfo_item">
                            <div class="text-right inputInfo_item_del">
                                <span class="ty-btn ty-btn-red ty-circle-2" title="删除此条加班信息" onclick="delInput($(this))">删除此条加班信息</span>
                            </div>
                            <div class="inputInfo_item_content">
                                <div class="bounceItem">
                                    <div class="bounceItem_title">加班时长 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <input class="kj-input" name="overDuration" onkeyup="clearNum(this)" require/> h
                                    </div>
                                </div>
                                <div class="bounceItem">
                                    <div class="bounceItem_title">加班事由</div>
                                    <div class="bounceItem_content">
                                        <input class="kj-input" type="text"  name="overReason" placeholder="如无可忽略。录入内容最多不得超过30字" onkeyup="countWords($(this),30)" style="width: 100%"/>
                                        <div class="textMax text-right" max="30"></div>
                                    </div>
                                </div>
                                <div class="bounceItem">
                                    <div class="bounceItem_title">备注</div>
                                    <div class="bounceItem_content">
                                        <input class="kj-input" type="text"  name="overMemo" placeholder="如无可忽略。录入内容最多不得超过30字" onkeyup="countWords($(this),30)" style="width: 100%">
                                        <div class="textMax text-right" max="30"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inputInfo_avatar"></div>
                </div>
                <div class="kj-panel outPart" style="display: none">
                    <div class="ty-alert">
                        请录入旷工信息
                        <div class="btn-group text-right">
                            <span class="ty-btn ty-btn-green ty-circle-2" onclick="addNewInput($(this))">增加新的旷工信息</span>
                        </div>
                    </div>
                    <div class="input_model">
                        <div class="inputInfo_item">
                            <div class="text-right inputInfo_item_del">
                                <span class="ty-btn ty-btn-red ty-circle-2" title="删除此条旷工信息" onclick="delInput($(this))">删除此条旷工信息</span>
                            </div>
                            <div class="inputInfo_item_content">
                                <div class="bounceItem">
                                    <div class="bounceItem_title">开始时间 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select halfTime" name="aBeginDate" require></select>
                                    </div>
                                    <div class="bounceItem_title">结束时间 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select halfTime" name="aEndDate" require></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inputInfo_avatar"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot cent">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="sureChangeAttendanceBtn()">提交</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%--打卡记录--%>
    <div class="bonceContainer bounce-blue" id="clockRecord" style="width:1110px;">
        <div class="bonceHead">
            <span id="recordTime"></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p><span id="recordName"></span>打卡记录</p>
            <table class="ty-table ty-table-control" id="clockRecordList">
                <tbody>
                <tr>
                    <td>考勤日期</td>
                    <td>打卡时间</td>
                    <td>打卡类型</td>
                    <td>打卡用手机的品牌</td>
                    <td>打卡用手机的型号</td>
                    <td>打卡用手机的唯一标识</td>
                    <td>考勤设备的编号</td>
                </tr>
                </tbody>
            </table>
            <div id="clockRecordPage"></div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%--提示--%>
    <div class="bonceContainer bounce-green" id="mtTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-green" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">很遗憾，上月已经结账了！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
    <%-- creater 姚宗涛 2018/2/8  09:38:00  考勤录入  下班考勤弹框  --%>
    <div class="bonceContainer bounce-green" id="attendance">
        <div class="bonceHead">
            <span class="name">贾宝玉10月26号星期四</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="attDetails hd">11111</div>
            <div>
                <div class="updaterInfo">
                    <p>上班考勤</p>
                    <div class="yestDayMorn">正常</div>
                </div>
                <div class="updaterInfo">
                    <p class="isAbsent">旷工 <span class="fa fa-circle-o" spot="0" outRadio="0" onclick="isSure($(this))"></span> <button class="hd ty-btn ty-circle-2 ty-btn-green ty-right outOfworkAdd" disabled="disabled" onclick="outOfworkAdd($(this))">新增</button></p>
                    <div class="outOfwork" id="offDuty_absent"></div>
                </div>
                <div class="updaterInfo" id="offWorkSet">
                    <p>下班考勤</p>
                    <div>
                        <dl>
                            <dd>正常 <span class="fa fa-circle-o" onclick="yesOrNot(1,$(this))"></span> </dd>
                            <dd>早退 <span class="fa fa-circle-o" onclick="yesOrNot(3,$(this))"></span> </dd>
                            <input type="hidden" value="0" id="endWorkAttend">
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green offSureBtn" onclick="endAttendSure()">确定</button>
        </div>
    </div>
    <%-- creator 李玉婷 2018/3/26  考勤录入  录入上班考勤弹窗  --%>
    <div class="bonceContainer bounce-green" id="first_attendance">
        <div class="bonceHead">
            <span class="name">贾宝玉</span><span>10月26号星期四</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="hd worker"></div>
            <div>
                <div class="updaterInfo">
                    <p>上班考勤</p>
                    <div id="attendanceState">
                        <dl id="isoutWork">
                            <dd>正常 <span class="fa fa-circle-o" att="1" onclick="yesOrNot(1,$(this))"></span> </dd>
                            <dd>迟到 <span class="fa fa-circle-o" onclick="yesOrNot(2,$(this))"></span> </dd>
                            <input type="hidden" type="text" value="7" id="workAttend">
                            <dd class="hd outWork">旷工 </dd>
                        </dl>
                    </div>
                </div>
                <div class="clr"></div>
                <div class="updaterInfo">
                    <p>旷工 <span id="outofworkBtn" class="fa fa-circle-o" spot="1" outRadio="0" onclick="isSure($(this))"></span></p>
                    <div class="outOfwork">
                        <dl class="hd">
                            <dd class="xing">开始时间</dd>
                            <dd id="notInTime">
                                <select id="outOfworkStrTime" name=""></select>
                            </dd>
                            <dd>结束时间</dd>
                            <dd>
                                <select id="outOfworkEndTime" name=""></select>
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green mornAttendSure" disabled="disabled" onclick="startAttendSure()">确定</button>
        </div>
    </div>
    <%-- creater 姚宗涛 2018/2/8  15:20:00  修改考勤  查看请假，早退，旷工，迟到。。。。弹框   --%>
    <div class="bonceContainer bounce-blue" id="log" style="min-width:610px;">
        <div class="bonceHead">
            <span class="personName">贾宝玉</span>的<span class="type">请假</span>记录
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table"></table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--  creater 姚宗涛 2018/2/9  08:58:00  修改考勤  查看加班记录  详情 查看按钮 弹框--%>
    <div class="bonceContainer bounce-green" id="overtimeDetails">
        <div class="bonceHead">
            <span>加班记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="">
                <dl>
                    <dd><span>王金锁</span><span>2017年10月26日</span>加班情况 </dd>
                </dl>
            </div>
            <div class="">
                <p>加班</p>
                <div>
                    <dl>
                        <dd>批准时长</dd>
                        <dd>日期</dd>
                        <dd>说明</dd>
                        <dd>批准时间</dd>
                    </dl>
                    <dl>
                        <dd class="">2h</dd>
                        <dd>2017年10月26日</dd>
                        <dd>我就批准你2小时</dd>
                        <dd>2017/10/27 11:00</dd>
                    </dl>
                    <dl>
                        <dd style="margin-top:90px;">批准人</dd>
                    </dl>
                    <dl>
                        <dd style="margin-top:90px;">包莹莹</dd>
                    </dl>
                </div>
                <div class="clr"></div>
            </div>
            <div class="clr"></div>
            <div class="">
                <div>
                    <dl>
                        <dd>申报时长</dd>
                        <dd>日期</dd>
                        <dd>时间</dd>
                        <dd>加班事由</dd>
                        <dd>申请时间</dd>
                    </dl>
                    <dl>
                        <dd>3h</dd>
                        <dd>2017年10月26日</dd>
                        <dd>17:00-20:00</dd>
                        <dd>作图</dd>
                        <dd>2017/10/27 09:00</dd>
                    </dl>
                </div>
                <div class="clr"></div>
            </div>
            <div class="clr"></div>
            <div class="">
                <div>
                    <dl>
                        <dd>计划时长</dd>
                        <dd>日期</dd>
                        <dd>时间</dd>
                        <dd>加班事由</dd>
                        <dd>批准时间</dd>
                        <dd>申请时间</dd>
                    </dl>
                    <dl>
                        <dd>3h</dd>
                        <dd>2017年10月26日</dd>
                        <dd>17:00-20:00</dd>
                        <dd>作图</dd>
                        <dd>2017/10/27 16:00</dd>
                        <dd>2017/10/27 14:00</dd>
                    </dl>
                    <dl>
                        <dd style="margin-top:120px;">批准人</dd>
                    </dl>
                    <dl>
                        <dd style="margin-top:120px;">包莹莹</dd>
                    </dl>
                </div>
                <div class="clr"></div>
            </div>
            <div class="clr"></div>
        </div>
    </div>
    <%--  creater 姚宗涛 2018/2/11 14:41:08  考勤管理页面数据（应出勤人数，迟到人数，早退人数，请假人数，旷工人数，加班人数。。。）查看弹框 --%>
    <div class="bonceContainer bounce-blue" id="seeDetails" style="width: 700px">
        <div class="bonceHead">
            <span class="bounce_title">2017年10月26日应出勤人员</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <table class="kj-table">
                    <thead></thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--creator:lyt 2018/3/21 考勤设置 考勤时间设置--%>
    <div class="bonceContainer bounce-green" id="initeSetting" style="width:600px;">
        <div class="bonceHead">
            <span>考勤时间设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" id="roles">
            <ul class="layer busyTimeSetting" >
                <li class="clockTime overflow">
                    <div class="ty-left"><span class="smallTitle">应上班时间</span><select class="workBeginTime"></select>
                    </div>
                    <div class="ty-left"><span class="smallTitle">应下班时间</span><select class="workEndTime"></select>
                    </div>
                </li>
                <li class="noonType">
                    <span class="smallTitle">午休时间</span>
                    <i class="fa fa-dot-circle-o radioSelect" lang="1" id="isSetNoon" onclick="setNoonToggle($(this))"></i>
                    <span id="setNoonSpan">
                        <select class="noonBeginTime" id="noonBeginTime"></select>
                        <span class="bridge">一</span>
                        <select class="noonEndTime" id="noonEndTime"></select>
                    </span>
                </li>
                <li class="singleChoice">
                    <span class="smallTitle">中午是否考勤</span>
                    <input type="hidden" id="isBreak">
                    <span class="fa ty-gray fa-circle-o radioSelect"></span><span class="mar60">是</span>
                    <span class="fa ty-gray fa-dot-circle-o radioSelect"></span><span>否</span>
                </li>
                <li>
                    <span class="smallTitle">适用部门</span>
                    <div class="department overflow">
                        <div class="departSelected" id="departSelected"> </div>
                        <div class="addDepart" onclick="addDepart($(this))"><i class="fa fa-bars"></i> </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="bonceFoot">
            <p id="updateAT">请选定本次对考勤设置的修改自何时生效 <input type="text" id="editOprate"/></p>
            <p>
                <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-2" id="attendanceSure" >确定</span>
            </p>

        </div>
    </div>
    <%--creator:lyt 2018/3/28 考勤设置 作息时间查看--%>
    <div class="bonceContainer bounce-green" id="timetableSetting" style="width:500px;">
        <div class="bonceHead">
            <span>作息时间设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="monthsTip">2018年8月份与9月份的作息计划如下，您可根据实际情况勾选修改。</p>
            <ul class="ty-secondTab monthsNav" id="monthsScan">
                <li>2018年11月</li>
                <li>2018年12月</li>
            </ul>
            <div class="clendars" id="clendarsScan" ></div>
            <div class="mask"></div>
        </div>
        <div class="bonceFoot">
            <p class="scan">
                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-2 restSet" onclick="beforeScanAttcs();scanAttc(1);">编辑</span>
                <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-2" onclick="bounce.cancel()">取消</span>
            </p>
            <p class="edit">
                <input type="hidden" id="today">
                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-2 restSet"
                      onclick="editAttendanceOK($(this))">确定</span>
                <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-2" onclick="beforeScanAttcs();scanAttc(0);">取消</span>
            </p>

        </div>
    </div>
    <%--creator:lyt 2018/4/13 考勤设置 修改考勤录入时间弹窗--%>
    <div class="bonceContainer bounce-blue" id="editTime">
        <div class="bonceHead">
            <span>修改考勤录入时间</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <span>考勤录入时间</span>
                <select id="endInputTime" class="ty-inputSelect">
                    <option value="07:30">7:30</option>
                    <option value="08:00">8:00</option>
                    <option value="08:30">8:30</option>
                    <option value="09:00">9:00</option>
                    <option value="09:30">9:30</option>
                    <option value="10:00">10:00</option>
                    <option value="10:30">10:30</option>
                    <option value="11:00">11:00</option>
                </select>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="updateSetTimeBtn()">确定</span>
        </div>
    </div>
    <%--creator:lyt 2018/4/13 考勤设置 修改详情--%>
    <div class="bonceContainer bounce-blue" id="editRecordDetails" style="width:800px;">
        <div class="bonceHead">
            <span>修改详情：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="recordDetailsCon">
                <div class="detailsHead">
                    <span class="title_dl">修改时间</span><span class="infoEditDate title_dl" id="infoEditDate">2018/1/20 8:36:35</span>
                    <span class="title_dl">修改人</span><span class="infoEditDate title_dl" id="infoEditor">露水</span>
                    <span class="takeEffectTime" id="takeEffectTime">本修改自XX年X月X日起生效</span>
                </div>
                <div class="compareSect overflow">
                    <div class="ty-left detCon">
                        <ul>
                            <li>
                                <span class="sect-title">应上班时间</span><span>8:00</span>
                                <span class="sect-title">应下班时间</span><span>17:00</span>
                            </li>
                            <li>
                                <span class="sect-title">午休时间</span><span>12:00一13:00</span>
                            </li>
                            <li>
                                <span class="sect-title">中午是否考勤</span><span>否</span>
                            </li>
                            <li>
                                <span class="sect-title">适用部门</span><span>财务一部、财务二部</span>
                            </li>
                        </ul>
                    </div>
                    <div class="ty-right detCon">
                        <ul>
                            <li>
                                <span class="sect-title">应上班时间</span><span>8:00</span>
                                <span class="sect-title">应下班时间</span><span>17:00</span>
                            </li>
                            <li>
                                <span class="sect-title">午休时间</span><span>12:00一13:00</span>
                            </li>
                            <li>
                                <span class="sect-title">中午是否考勤</span><span>否</span>
                            </li>
                            <li class="editAfter">
                                <i class="flag_arrow fa fa-long-arrow-right"></i>
                                <span class="sect-title">适用部门</span><span>财务一部、财务二部</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="compareSect overflow">
                    <div class="ty-left detCon">
                        <ul>
                            <li>
                                <span class="sect-title">应上班时间</span><span>8:00</span>
                                <span class="sect-title">应下班时间</span><span>17:00</span>
                            </li>
                            <li>
                                <span class="sect-title">午休时间</span><span>12:00一13:00</span>
                            </li>
                            <li>
                                <span class="sect-title">中午是否考勤</span><span>否</span>
                            </li>
                            <li>
                                <span class="sect-title">适用部门</span><span>财务一部、财务二部</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--creator:lyt 2018/3/27 考勤修改 修改提交后的查看 --%>
    <div class="bonceContainer bounce-blue" id="attendanceChangeDetail" style="width:560px;">
        <div class="bonceHead">
            <span class="bounce_title">考勤详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">
                应出勤时间&nbsp;<span class="shouldTime"></span>
            </div>
            <div class="ty-alert">
                <div class="btn-group text-center">
                    <span class="title-big">实际考勤</span>
                    <button class="ty-btn ty-btn-blue ty-circle-2 changeBtn" id="attendUpdate" onclick="attendUpdate()">修改</button>
                </div>
            </div>
            <table class="kj-table">
                <tbody></tbody>
            </table>
            <div class="process text-right creator"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 multi-details-ok" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="attendanceSetRecord" style="width:560px;">
        <div class="bonceHead">
            <span>考勤设置修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table editRecord">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>修改时间</th>
                    <th>修改人</th>
                    <th>生效日期</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody id="updateLog"></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>我的考勤</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <input id="pageNum" type="hidden" value="1" />
            <div class="ty-container">
                <%--主页面--%>
                <div class="page main">
                    <%-- //  考勤管理初始页 顶部操作--%>
                    <div class="collectionBtn">
                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 back hd" onclick="back($(this))">返回</button>
                    </div>
                    <div class="attendanceQuery">
                        <div class="ty-alert ty-alert-info">
                            今天是 <span class="ty-color-blue nowDay"><span class="nowDay_date"></span> <span class="workOrRest"></span></span>
                            <div class="btn-group text-right">
                                <div class="queryItem">
                                    <span class="ssTtl">查看考勤统计</span>
                                    <input class="kj-input kj-input-blue" id="countKey" placeholder="请选择月份" /><!--
                                --><button class="ty-btn ty-btn-big ty-btn-blue" data-name="countScreen" >确定</button>
                                </div>
                                <div class="queryItem">
                                    <span class="ssTtl">查看考勤明细</span>
                                    <input class="kj-input kj-input-blue" id="detailedKey" placeholder="请选择月份" /><!--
                                --><button class="ty-btn ty-btn-big ty-btn-blue" data-name="detailScreen" >确定</button>
                                </div>
                                <div style="display: none;" id="byLastMonth">如上月尚未结账，您还可修改上月的职工考勤。<button class="ty-btn ty-btn-big ty-btn-blue" onclick="changeLastMonthAttendance(1)">修改上月的考勤</button></div>
                                <div style="display: none;" id="byNowMonth"><button class="ty-btn ty-btn-big ty-btn-blue" onclick="changeLastMonthAttendance(0)">修改本月的考勤</button></div>
                            </div>
                        </div>
                        <div class="ty-alert ty-alert-info">
                            <div style="display: inline-block">
                                <span id="attendanceTip"></span>
                                <small id="updateAttendanceTip" class="ty-color-red" style="display: none;">如需修改某人考勤，点击其考勤即可进行。</small>
                            </div>
                        </div>
                    </div>
                    <div>
                        <%--   // 考勤统计 --%>
                        <div class="ty-tblContainer count">
                            <table class="kj-table hover" id="attendCountList">
                                <thead>
                                <tr>
                                    <th rowspan="2">姓名</th>
                                    <th rowspan="2">部门</th>
                                    <th>请假</th>
                                    <th>出差</th>
                                    <th>外出</th>
                                    <th>迟到</th>
                                    <th>早退</th>
                                    <th>旷工</th>
                                    <th>加班</th>
                                </tr>
                                <tr>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div id="count_page"></div>
                        </div>
                        <%--   // 考勤明细 --%>
                        <div class="ty-tblContainer detail hd">
                            <div class="ty-alert ty-alert-warning">

                                考勤说明：
                                <div class="icon_mark">
                                    <span class="fa fa-check"></span>
                                    <span>正常</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-level-down"></span>
                                    <span>迟到</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-times"></span>
                                    <span>旷工</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-bicycle"></span>
                                    <span>外出</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-plane"></span>
                                    <span>出差</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-level-up"></span>
                                    <span>早退</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-clock-o"></span>
                                    <span>请假</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-minus"></span>
                                    <span>无需考勤</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-star-o"></span>
                                    <span>复杂</span>
                                </div>
                            </div>
                            <div class="table-fixed-avatar">
                                <table id="attendDetList" class="kj-table attendDetList fixed hover">
                                    <thead></thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <div id="eidt_page"></div>
                        </div>
                    </div>
                </div>
                <%--考勤录入--%>
                <div class="page attendanceInput" style="display: none">
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="back($(this))">返回</button>
                    <div class="ty-tblContainer">
                        <p class="hd" id="workTime"></p>
                        <table class="kj-table" id="attendInput" open="0">
                            <tbody></tbody>
                        </table>
                    </div>
                    <div id="log_page"></div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var setStatus = "${status}" ; // status 1-已设置 0-未设置
    // 获取开始日期
    var startUp = "${startUsingSystemTime}";
    // 获取时间误差
    var hostTime = <%= System.currentTimeMillis() %>;

    var diff = hostTime - new Date().getTime();
    if(diff < 30000){
        diff = 0;
    }
    var startMonth = startUp.substr(0,7) + '-02' ;
    var ssEnd = new Date(hostTime).format('yyyy-MM') + '-20';
</script>
<script src="../script/home/<USER>"></script>

<%--<script src="../script/general/attendance/attendance.js?v=SVN_REVISION"></script>--%>
<%--<script src="../script/general/attendance/myCanlendar.js?v=SVN_REVISION"></script>--%>
<%--<script src="../script/general/attendance/setAttendanceRole.js?v=SVN_REVISION"></script>--%>
</body>
</html>

