<%--
  Created by IntelliJ IDEA.
  User: 侯杏哲
  Date: 2017/8/11
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/production/review.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .hoverDetail{
        cursor: default;
    }
    .hoverDetailCon{
        position: absolute;
        top: 0;
        right: 50%;
        width: 300px;
        min-height: 120px;
        background-color: #fff;
        padding: 10px 20px 20px 20px;
        border:1px solid #ccc;
        box-shadow: 0 0 5px #ccc;
        display: none;
        text-align: left;
        z-index: 999;
    }
    .hoverDetail:hover .hoverDetailCon{
        display: block;
    }
</style>

<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
        <%--入库检验-待处理-查看弹窗--%>
        <div class="bonceContainer bounce-blue" id="seeStorageCheck" style="min-width:1200px;">
            <div class="bonceHead">
                <span>待处理入库详情</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon" style="height: 450px;overflow-y: auto">
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td>序号      </td>
                        <td>商品代号 </td>
                        <td>商品名称 </td>
                        <td>产品图号 </td>
                        <td>产品名称 </td>
                        <td>单位     </td>
                        <td>数量     </td>
                        <td>生产日期  </td>
                        <td>产品到期日</td>
                        <td>入库流程</td>
                        <td>检验</td>
                        <td>驳回理由</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" id="storageCheckBtn" onclick="sureStorageCheck()">提交</button>
            </div>
        </div>

</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>入库检验</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <span class="hd" id="curN">待处理</span>
                <ul class="ty-secondTab">
                    <li class="ty-active">待处理</li>
                    <li>已合格</li>
                    <li>已驳回</li>
                </ul>
                <div class="ty-mainData">
                    <%-- 待处理 --%>
                    <div class="tblContainer">
                        <table class="ty-table ty-table-control panel_1" id="tab1">
                            <thead>
                            <tr>
                                <td>序号</td>
                                <td>申请人</td>
                                <td>申请时间</td>
                                <td>查看详情</td>
                            </tr>
                            </thead>
                            <tbody id="tab1Bdy"> </tbody>
                        </table>
                    </div>
                    <%-- 已合格 --%>
                    <div class="tblContainer" style="display: none">
                        <table class="ty-table ty-table-control panel_2" id="tab2">
                            <thead>
                            <tr>
                                <td>序号</td>
                                <td>申请时间</td>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>产品图号</td>
                                <td>产品名称</td>
                                <td>单位</td>
                                <td>数量</td>
                                <td>生产日期</td>
                                <td>产品到期日</td>
                                <td>入库流程</td>
                            </tr>
                            </thead>
                            <tbody id="tab2Bdy"> </tbody>
                        </table>
                    </div>
                    <%-- 已驳回--%>
                    <div class="tblContainer" style="display: none">
                        <table class="ty-table ty-table-control panel_3" id="tab3">
                            <thead>
                            <tr>
                                <td>序号</td>
                                <td>申请时间</td>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>产品图号</td>
                                <td>产品名称</td>
                                <td>单位</td>
                                <td>数量</td>
                                <td>生产日期</td>
                                <td>产品到期日</td>
                                <td>入库流程</td>
                            </tr>
                            </thead>
                            <tbody id="tab3Bdy"> </tbody>
                        </table>
                    </div>

                    <div id="ye_storageTest"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/production/storageTest.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
