<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/technology/productArchives.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/common/theme/fileTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/wenjian.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="compotionBounce" id="compotionBounce">
</div>
<div class="bounce">
    <%-- 文件查看基本信息 --%>
    <div class="bonceContainer bounce-blue " id="docInfoScan" style="width:800px">
        <div class="bonceHead">
            <span>基本信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <div class="infoCon clearfix">
                <h4 class="info_title" id="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" id="info_sn"></span></div>
                    <div class="trItem"><span class="ttl">保存位置：</span><span class="con" id="info_category"></span></div>
                    <div class="trItem"><span class="ttl">文件大小：</span><span class="con" id="info_size"></span></div>
                    <div class="trItem"><span class="ttl">文件类型：</span><span class="con" id="info_version"></span></div>
                    <div class="trItem"><span class="ttl">说明：</span><span class="con" id="info_content"></span></div>

                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con" id="info_gn"></span></div>
                    <div class="trItem"><span class="ttl2">创建人：</span><span class="con" id="info_creator"></span></div>
                    <div class="trItem"><span class="ttl2">换版人：</span><span class="con" id="info_updater"><span class="info_createName info_name"></span> <span class="info_createDate"></span></span></div>
                    <div class="trItem generalPart">
                        <span class="ttl3">本版本的签收记录：</span>
                        <span class="con times sign_num" style="width: 60px"></span>
                        <span class="link-blue" id="signRecordBtn" onclick="seeSignRecordBtn()" style="margin-left: 12px">查看</span>
                    </div>
                </div>
            </div>
            <div class="infoCon clearfix">
                <div class="ty-left processHistory">
                    <div class="item-header">
                        审批记录
                    </div>
                    <div class="processList">
                    </div>
                </div>
                <div class="ty-left censusInfo">
                    <div class="item-header">
                        统计信息
                    </div>
                    <div class="trItem">
                        <span class="ttl3">浏览次数</span>
                        <span class="con times view_num"></span>
                        <span class="link-blue" id="viewNumBtn" onclick="seeHandelRecordBtn(1)">浏览记录</span>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">下载次数</span>
                        <span class="con times download_num"></span>
                        <span class="link-blue" id="downloadNumBtn" onclick="seeHandelRecordBtn(2)">下载记录</span>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">移动次数</span>
                        <span class="con times move_num"></span>
                        <span class="link-blue" id="moveNumBtn" onclick="seeHandelRecordBtn(5)">移动记录</span>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件名称修改次数</span>
                        <span class="con times name_num"></span>
                        <span class="link-blue" id="changeNameNumBtn" onclick="seeHandelRecordBtn(6)">修改记录</span>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件编号修改次数</span>
                        <span class="con times no_num"></span>
                        <span class="link-blue" id="changeNoNumBtn" onclick="seeHandelRecordBtn(7)">修改记录</span>
                    </div>
                    <div class="trItem generalPart">
                        <span class="ttl3">文件废止/复用记录</span>
                        <span class="link-blue" id="terminateBtn" onclick="seeTerminateRecordBtn(4)" style="margin-left: 78px">查看</span>
                    </div>
                    <div class="trItem generalPart">
                        <span class="ttl3">文件停用/复用记录</span>
                        <span class="link-gray" id="stopReuseBtn" style="margin-left: 78px">查看</span>
                    </div>
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel();bounce_Fixed.show($('#addfilelook'));">关闭</span>
        </div>
    </div>
    <%-- 提示 --%>
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="ty-center">
                    <p id="mt_tip_ms"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue " onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 产品录入 --%>
    <div class="bonceContainer bounce-blue" id="addProduct" style="width:1100px;">
        <div class="bonceHead">
            <span>产品录入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <div class="addProductForm">
                <p>您正在向“<span class="ty-color-red">待分类</span>”中录入产品！</p>
                <div class="elemFlex" style="padding: 0;">
                    <%--<p class="ty-color-redOrange">产品录入后请“拆分”，以便更充分发挥系统功能。</p>--%>
                    <div>
                        <p class="ty-color-green" style="margin-bottom: 0px;">为充分属于系统功能，推荐：</p>
                        <p class="ty-color-green" style="margin-bottom: 0px;">1、产品录入时，本页面的几项关联尽量完成；</p>
                        <p class="ty-color-green">2、产品录入后，及时进行“拆分”等操作。</p>
                    </div>
                    <span class="ty-btn ty-btn-gray" style="margin-top: 38px;">批量导入</span>
                </div>
                <table class="ty-table ty-table-control" id="productionFrom">
                    <tbody>
                    <tr>
                        <td width="18%">产品图号<i class="xing"></i></td>
                        <td width="18%">产品名称<i class="xing"></i></td>
                        <td width="10%">规格</td>
                        <td width="10%">型号</td>
                        <td width="18%">计量单位<span class="xing"></span>
                            <span onclick="addUnit($(this))" style="font-weight: bold; color:#0b94ea; padding:0 5px; ">新增</span></td>
                        <td width="14%">所处阶段<i class="xing"></i></td>
                        <td width="14%">加工部门<i class="xing"></i></td>
                    </tr>
                    <tr>
                        <td><input need name="innerSn" type="text" require /></td>
                        <td><input need name="name" type="text" require /></td>
                        <td><input need name="specifications" type="text" /></td>
                        <td><input need name="model" type="text" /></td>
                        <td>
                            <select need type="text" id="unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                            <input need type="hidden" name="unit" id="add_unitName">
                        </td>
                        <td>
                            <select need name="phrase" require>
                                <option value="1">开发中</option>
                                <option value="2">开发完成</option>
                            </select>
                        </td>
                        <td>
                            <input need name="processDeptName" type="text" require id="createOrg" readonly="readonly" onclick="selectDepart('create')">
                            <input need name="processDept" type="hidden" id="createOrgID">
                        </td>
                    </tr>
                    <tr>
                        <td>产品说明</td>
                        <td colspan="8"><input need name="memo" type="text" placeholder="此处最多可录入100字" maxlength="100" onkeyup="limitWord($(this))"/></td>
                    </tr>
                    </tbody>
                </table>
                <p class="wordCount" style="text-align: right;">0/100</p>
                <div class="relatedGoods">
                    <hr />
                    <div style="display: flex;" class="podutsition">
                        <div class="plence" style="width: 435px;">产品与商品关联后，可使用到系统更多功能！</div>
                        <div class="plence" style="width: 180px;">通用型商品<span class="merchandise">X</span>关联</div>
                        <div class="plence" style="width: 210px;">专属商品已关联<span class="exclsieoucts">XX</span>种</div>
                        <div>
<%--                            <span style="font-weight: bold;color: #0b94ea;  margin-right: 15px;" onclick="lookcatbook()" id="lookcatbook">查看</span>--%>
                            <span style="font-weight: bold;color: #0b94ea;" onclick="openasoiat()" id="association">关联</span>
                        </div>
                    </div>
                    <hr />
                    <div>
                        <div style="display: flex;">
                            <div class="plence" style="width: 644px;">点击“增加文件”后选取产品图纸或其他内部文件，可供技术、工艺、检验等查看！</div>
                            <div class="plence" style="width: 158px;">已关联<span class="pronumb">XX</span>个文件</div>
                            <div style="float: right;">
                                <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;" onclick="vieilrcrds()" class="haveloonk">查看</span>
                                <span style="font-weight: bold;color: #0b94ea;" onclick="addproduct(1)" class="addprodun">增加文件</span>
                            </div>
                        </div>
                        <div class="ty-color-blue">
                            <div>注1 内部文件系指“文件与资料”中已按规则上传的文件；</div>
                            <div>注2 对于没有使用权限的文件，您无法选择，如需选择，请自行向管理员提出；</div>
                            <div>注3 工艺类或检验类的文件，建议在其他模块中由相关人员操作.</div>
                        </div>
                    </div>
                    <hr />
                    <div style="display: flex;">
                        <div class="plence" style="width: 656px;">
                            <div>图片—图片格式的产品图纸</div>
                            <div class="ty-color-blue">注：上传后可供快速查看！</div>
                        </div>
                        <div class="plence unupent" style="width: 184px;">尚未上传</div>
                        <div class="plence hd upent" style="width: 100px;">已上传</div>
                        <div class="plence hd detent" style="width: 100px;">已移除</div>
                        <div >
                            <span style="font-weight: bold;color: #0b94ea;" onclick="" id="cpUplond-1" class="send1">
                                <%--上传--%>
                            </span>
                        </div>
                        <div class="hd oveadd" style="display: flex;">
                            <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;" onclick="tacklook(1)" id="tacklook">查看</span>
                            <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;" onclick="" id="cpUplond-2" class="send2">
                                <%--更换--%>
                            </span>
                            <span style="font-weight: bold;color: #0b94ea;" onclick="detented(1)">移除</span>
                        </div>
                    </div>
                    <hr style="margin-top: 40px;"/>
                    <div style="display: flex;">
                        <div class="plence bigen">
                            <div>图片—标记有控制点顺序号的图片</div>
                            <div class="ty-color-blue">注：上传后，可供工艺及检验模块调用！</div>
                        </div>
                        <div class="plence unupent2" style="width: 184px;">尚未上传</div>
                        <div class="plence hd upent2" style="width: 100px;">已上传</div>
                        <div class="plence hd detent2" style="width: 100px;">已移除</div>
                        <div style="float: right;">
                            <span style="font-weight: bold;color: #0b94ea;" onclick="" id="cpUplond-5" class="send5">
                                <%--上传--%>
                            </span>
                        </div>
                        <div class="hd oveadd2" style="display: flex;">
                            <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;" onclick="tacklook(2)" id="tacklook2">查看</span>
                            <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;" onclick="" id="cpUplond-6">
                                <%--更换--%>
                            </span>
                            <span style="font-weight: bold;color: #0b94ea;" onclick="detented(2)">移除</span>
                        </div>
                    </div>
                    <%--下面注释掉的部分一会儿移动到另外一个新的弹窗中--%>
                    <%--<div>以下内容为产品与商品关联的操作，“关联管理”模块也可进行同等效果的操作。</div>
                    <div>关联关系的查看与修改需到“关联管理”模块中进行。</div>
                    <div>此外，信息不足时，以下内容可暂不操作。</div>
                    <div class="clear cmChoose">
                        <div class="cmTip">本产品作为通用型商品销售时，如使用另外的商品代号与名称，请在右侧选框中选择具体为哪个通用型商品。</div>
                        <div class="ty-left">
                            <select id="cmGoods">
                                <option value="">请选择通用型商品</option>
                            </select>
                        </div>
                    </div>
                    <div>本产品作为某客户的专属商品销售时</div>
                    <div>1、如使用另外的商品代号与名称，请先选择客户，之后在该客户的专属商品下选择；</div>
                    <div>2、不同客户下的商品代号如不同，请点击“新增”，之后可选择另一组客户与商品代号。</div>
                    <div class="cmChoose" id="zsGoods">
                        <div class="zsCase">
                            <select class="customerList" onchange="getZsProductList($(this))">
                                <option value="">请选择客户</option>
                            </select>
                            <select class="zsGoodsList">
                                <option value="">请选择专属商品</option>
                            </select>
                            <span class="blueLinkBtn" onclick="addMore()">新增</span>
                        </div>
                    </div>--%>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addProductionSure()" id="addPurover">录入完毕</span>
        </div>
    </div>
    <%--   产品查看 --%>
    <div class="bonceContainer bounce-blue" id="seeProduct" style="width:1000px;">
        <div class="bonceHead">
            <span>产品查看</span>
            <a class="bounce_close" onclick="closePartScan()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-color-orange" id="stopDetails">
                <span id="stopName">XXX</span>已于<span id="stopDate">XXXX-XX-XX XX:XX:XX</span>将本产品“停止生产”！</span>
            </div>
            <div class="hd" id="productInfo"></div>
            <div class="scan-wrap" style="margin: 0 64px;">
                <div class="elemFlex">
                    <div>
                        <span>基本信息</span>
                        <div>创建：<span id="seeCreater"></span></div>
                    </div>
                    <div>
                        <span class="ty-btn ty-btn-blue partsScan2" onclick="updateProduction()">修改基本信息</span>
                        <span class="ty-btn ty-btn-blue" data-type="baseRecords">基本信息的修改记录</span>
                        <span class="ty-btn ty-btn-blue" data-type="ptSuspendRecord">停止生产的操作记录</span>
                    </div>
                </div>
                <table class="ty-table ty-table-control" id="seeProductInit">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">单重</td>
                        <td width="12%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="netWeight"></td>
                        <td need data-name="processDeptName"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <hr/>
                <div class="middleword1" style="display: flex;">
                    <div style="flex: 0 1 200px;padding: 10px 0;">
                        <div>图片—图片格式的产品图纸</div>
                    </div>
                    <div class="pic1" style="display: flex;margin-left: 149px;">
                        <div style="padding: 10px 0;" class="swsc1">尚未上传</div>
                        <div style="padding: 10px 0;" class="yyc1">已移除</div>
                        <div style="display:flex;margin-left: 377px;">
                            <span style="font-weight: bold;color: #0b94ea;" onclick="changeup7()" id="cpUplond-7" class="send7">
                                <%--上传--%>
                            </span>
                        </div>
                    </div>
                    <div class="uppic1" style="display: flex;margin-left: 148px;">
                        <div style="flex: 0 1 100px;padding: 10px 0;margin-right: 191px;padding-left: 11px;">已上传</div>
                        <div style="display: flex;margin-left: 50px;width: 236px;">
                            <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;padding: 10px 0;" onclick="propiclook(1)" class="propiclook" id="propiclook">查看</span>
                            <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;padding: 10px 0;" onclick="propiclook(1,'has')" class="propiclook" id="propiclookhas">查看</span>
                            <span class="cpUplond-3" style="height: 19px;margin-top: 10px;font-weight: bold;color: #0b94ea;" onclick="changedown3()" id="cpUplond-3">
                                <%--更换--%>
                            </span>
                            <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;padding: 10px 0;" onclick="removedettle(0)" id="retuem3">移除</span>
                            <span style="font-weight: bold;color: #0b94ea;padding: 10px 0;padding-left: 4px;" onclick="catlistollk(0)" id="catlistollk0">操作记录</span>
                        </div>
                    </div>
                </div>
                <div class="middleword2" style="display: flex;">
                    <div style="flex: 0 1 210px;padding: 10px 0;">
                        <div>图片—标记有控制点顺序号的图片</div>
                    </div>
                    <div class="pic2" style="display: flex;margin-left: 141px;">
                        <div style="padding: 10px 0;" class="swsc2">尚未上传</div>
                        <div style="padding: 10px 0;" class="yyc2">已移除</div>
                        <div style="display: flex;margin-left: 377px;">
                            <span style="font-weight: bold;color: #0b94ea;" onclick="changeup8()" id="cpUplond-8" class="send8">
                                <%--上传--%>
                            </span>
                        </div>
                    </div>
                    <div class="uppic2" style="display: flex;">
                        <div style="flex: 0 1 100px;padding: 10px 0;margin-right: 162px;padding-left: 11px;margin-left: 128px;">已上传</div>
                        <div style="display: flex;margin-left: 53px;width: 236px;padding-left: 20px;">
                            <span style="font-weight: bold;color:#0b94ea;margin-right: 10px;padding: 10px 0;" onclick="propiclook(2)" class="propiclook2" id="propiclook2">查看</span>
                            <span style="font-weight: bold;color:#0b94ea;margin-right: 10px;padding: 10px 0;" onclick="propiclook(2,'has')" class="propiclook2" id="propiclookhas2">查看</span>
                            <span class="cpUplond-4" style="height: 19px;margin-top: 10px;font-weight: bold;color: #0b94ea;" onclick="changedown4()" id="cpUplond-4">
                                <%--更换--%>
                            </span>
                            <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;padding: 10px 0;" onclick="removedettle(1)" id="retuem4">移除</span>
                            <span style="font-weight: bold;color:#0b94ea;padding: 10px 0;padding-left: 4px;" onclick="catlistollk(1)" id="catlistollk1">操作记录</span>
                        </div>
                    </div>
                </div>
                <div class="middleword3" style="display: flex;margin-top: 10px;">
                    <div style="margin-right: 149px;flex:0 1 200px;">
                        <div>图纸/其他技术文件</div>
                    </div>
                    <div style="flex: 1;">共<span class="protest">XXX</span>个</div>
                    <div style="flex: 0 1 200px;padding-left: 13px;display: flex;">
                        <span style="font-weight: bold;color: #0b94ea;margin-right: 10px;margin-left: 27px;" onclick="haveloonk2()" class="haveloonk2">查看</span>
                        <span style="font-weight: bold;color:#0b94ea;margin-right: 10px;" onclick="addproct(1)" class="addproct">增加文件</span>
                        <span style="font-weight: bold;color:#0b94ea;" onclick="catlistollk(2)" id="catlistollk2">操作记录</span>
                    </div>
                </div>
                <hr />
                <div class="middleScan">
                    <div class="clear">
                        <div class="double-sample ty-left">
                            <p>当前含有本件的产品/组件中</p>
                            <div class="cell-sample">
                                <div class="row-sample clear">
                                    <span class="ty-left">直接含有本件的产品</span>
                                    <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="1">查看</span>
                                    <span class="ty-right"><span class="several" data-name="directPartProduct"></span>种</span>
                                </div>
                                <div class="row-sample clear">
                                    <span class="ty-left">间接含有本件的产品</span>
                                    <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="2">查看</span>
                                    <span class="ty-right"><span class="several" data-name="indirectPartProduct"></span>种</span>
                                </div>
                                <div class="row-sample clear">
                                    <span class="ty-left">直接含有本件的组件</span>
                                    <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="3">查看</span>
                                    <span class="ty-right"><span class="several" data-name="directPartComposition"></span>种</span>
                                </div>
                                <p class="ty-color-blue">此处的组件仅包含组成产品的装配件，而不包含产品</p>
                            </div>
                        </div>
                        <div class="double-sample ty-right">
                            <p>曾含有、但已不再含有本件的产品/组件中</p>
                            <div class="cell-sample">
                                <div class="row-sample clear">
                                    <span class="ty-left">曾直接含有本件的产品</span>
                                    <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="4">查看</span>
                                    <span class="ty-right"><span class="several" data-name="formerDirectPartProduct"></span>种</span>
                                </div>
                                <div class="row-sample clear">
                                    <span class="ty-left">曾间接含有本件的产品</span>
                                    <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="5">查看</span>
                                    <span class="ty-right"><span class="several" data-name="formerIndirectPartProduct"></span>种</span>
                                </div>
                                <div class="row-sample clear">
                                    <span class="ty-left">曾直接含有本件的组件</span>
                                    <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="6">查看</span>
                                    <span class="ty-right"><span class="several" data-name="formerdirectPartComposition"></span>种</span>
                                </div>
                                <p class="ty-color-blue">此处的组件仅包含组成产品的装配件，而不包含产品</p>
                            </div>
                        </div>
                    </div>
                    <hr/>
                </div>
                <div class="afterConstitute">
                    <div class="elemFlex">
                        <div>
                            <span id="compositionDetails">本件委托外部加工</span>
                            <div>最新编辑：<span id="updateLast"></span></div>
                        </div>
                        <div>
                            <span class="ty-btn ty-btn-blue partsScan2" data-type="editSource">修改来源/构成</span>
                            <span class="ty-btn ty-btn-blue" data-type="compositionRecords">来源/构成修改记录</span>
                        </div>
                    </div>
                    <%--<div class="only hd">
                         <div class="matInfo1">
                             <table class="ty-table ty-table-control">
                                 <thead>
                                 <td width="30%">材料代号</td>
                                 <td width="40%">材料名称/规格/型号</td>
                                 <td width="30%">计量单位</td>
                                 </thead>
                                 <tbody>
                                 </tbody>
                             </table>
                         </div>
                         <div class="matInfo2">
                             <table class="ty-table ty-table-control">
                                 <thead>
                                 <td>配方代号</td>
                                 <td>配方/材料名称</td>
                                 <td>主料</td>
                                 <td>辅料</td>
                                 </thead>
                                 <tbody>
                                 </tbody>
                             </table>
                         </div>
                         <%--<div class="matInfo3">
                             <div class="elemFlex">
                                 <span>以下为当前状态下本件直接包含的零组件</span>
                                 <div>
                                     <span class="ty-btn ty-btn-blue" data-type="allParts">当前全部的零组件</span>
                                     <span class="ty-btn ty-btn-blue" data-type="viewSettings">全部零组件查看设置</span>
                                     <span class="ty-btn ty-btn-blue" data-type="allOnceParts">曾经包含的零组件</span>
                                     <span class="hd" id="viewSettingsState"></span>
                                 </div>
                             </div>
                             <div class="scropWrap">
                                 <div class="innerElem">
                                     <table class="ty-table ty-table-control">
                                         <thead>
                                         <td width="12%">图号</td>
                                         <td width="12%">名称/规格/型号</td>
                                         <td width="8%">计量单位</td>
                                         <td width="8%">装配数量</td>
                                         <td width="8%">来源</td>
                                         <td width="8%">构成</td>
                                         <td width="8%">单重</td>
                                         <td width="14%">参与构成的最新操作</td>
                                         <td width="8%">操作</td>
                                         </thead>
                                         <tbody>
                                         </tbody>
                                     </table>
                                 </div>
                             </div>
                         </div>
                     </div>--%>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closePartScan()">关闭</span>
        </div>
    </div>
    <%-- ！提示 -删除 --%>
    <div class="bonceContainer bounce-red" id="deleteProduct">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="deleteTip"></div>
        </div>
        <div class="bonceFoot">
            <span class="delRefuse ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">我知道了</span>
            <span class="deleteCan ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="deleteCan ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="deleteProductSure()">确定</span>
        </div>
    </div>
    <%-- ！提示 -暂停销售 --%>
    <div class="bonceContainer bounce-red" id="pauseProduct">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="pauseProductTip" class="ty-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="pauseRefuse ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="pauseCan ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="suspendSaleSure()">确定</span>
        </div>
    </div>
    <%--  产品拆分  --%>
    <div class="bonceContainer bounce-blue" id="splitGS">
        <div class="bonceHead">
            <span>产品拆分</span>
            <a class="bounce_close" onclick="goBack(2)"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>待拆分的产品</p>
                <table class="ty-table" style="width: 75%;">
                    <tbody>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                    </tr>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                    </tr>
                    </tbody>
                </table>
                <p></p><p>该产品由以下零组件组合而成 <span class="ty-btn linkBtn" data-type="partEntry">录入直接组成该产品的零组件</span>(一般为装配图明细表中内容)</p>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                        <td>装配数量</td>
                        <td>来源</td>
                        <td>构成</td>
                        <td>拆分情况</td>
                        <td>操作</td>
                    </tr>

                    </tbody>
                </table>
            </div>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="goBack(2)">返回</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="splitOKBtn(0)">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--各种操作记录--%>
    <div class="bonceContainer bounce-blue" id="fileHandleRecord" style="width: 650px">
        <div class="bonceHead">
            <span class="recordTitleName">修改文件编号</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:300px; overflow-y: auto; ">
            <table class="ty-table recordTable"></table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%-- 可供选择的文件列表 List of files for selection--%>
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width:1050px;">
        <div class="bonceHead">
            <span class="bounce_title">请选择内部文件</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-fileContent">
                <%--文件夹列表 --%>
                <div class="ty-colFileTree folder_list" data-name="main"></div>
                <%-- 文件列表 --%>
                <div class="mar">
                    <ul class="ty-secondTab" id="fileSort" style="display: none">
                        <li class="ty-active">发布时间<span class="sort sortName"><i class="fa fa-long-arrow-down"></i></span></li>
                        <li type="1">文件编号<span class="sort sortName" style="display: none"><i class="fa fa-long-arrow-down"></i></span></li>
                    </ul>
                    <div class="ty-fileList mainFileList" >
                        <%--文件列表--%>
                    </div>
                    <div id="ye_con"></div>
                </div>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="closewindw()" id="deteeten">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="sureChooseFolder" id="sureChooseFolderBtn">确定</button>
        </div>
    </div>
    <%-- 图片格式的产品图纸操作记录--%>
    <div class="bonceContainer bounce-blue" id="procatlist" style="width: 1277px;">
        <div class="bonceHead">
            <span>操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="hd" id="productbox"></div>
        <div class="bonceCon specialForm">
            <div class="addProductForm">
                <p>基本信息</p>
                <p>创建：<span id="seeCreater2">XXX XXXX-XX XX XX:XX:XX</span></p>
                <table class="ty-table ty-table-control" id="prolistbook">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="18%">名称/规格/型号</td>
                        <td width="10%">计量单位</td>
                        <td width="10%">所处阶段</td>
                        <td width="10%">单重</td>
                        <td width="10%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn">XXXXX</td>
                        <td need data-name="name">XXXXXXXX/XXXX/XXX</td>
                        <td need data-name="unit">XXX</td>
                        <td need data-name="phrase">XXX</td>
                        <td need data-name="netWeight">XXX</td>
                        <td need data-name="processDeptName">XXX</td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td colspan="8" need data-name="memo" class="algnL">XXXXXXXX</td>
                    </tr>
                    </tbody>
                </table>
                <hr />
                <p class="titme">图片—图片格式的产品图纸</p>
                <table class="ty-table ty-table-control" id="procatpicok">
                    <tbody>
                    <tr>
                        <td width="18%">操作的名称</td>
                        <td width="18%">操作者</td>
                        <td width="18%">操作后的图片</td>
                    </tr>
                    <tr class="upsent">
                        <td class="upsend">上传</td>
                        <td class="upsendpeo">XXX XXXX-XX-XX XX:XX:XX</td>
                        <td>
                            <span class="ty-color-blue">查看</span>
                            <span class="hd"></span>
                        </td>
                    </tr>
                    <tr class="upnewble">
                        <td class="upnewblce">更换</td>
                        <td class="upnewblceo">XXX XXXX-XX-XX XX:XX:XX</td>
                        <td>
                            <span class="ty-color-blue">查看</span>
                            <span class="hd"></span>
                        </td>
                    </tr>
                    <tr class="dettal">
                        <td class="dettleal">移除</td>
                        <td class="dettlealall">XXX XXXX-XX-XX XX:XX:XX</td>
                        <td>
                            <span class="ty-color-blue" onclick="delooker()">查看</span>
                            <span class="hd"></span>
                        </td>
                    </tr>
                    <%--                    <tr>--%>
                    <%--                        <td>上传</td>--%>
                    <%--                        <td>XXX XXXX-XX-XX XX:XX:XX</td>--%>
                    <%--                        <td>--%>
                    <%--                            <span class="ty-color-blue">查看</span>--%>
                    <%--                        </td>--%>
                    <%--                    </tr>--%>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closenwind(6)">关闭</span>
        </div>
    </div>
    <%-- 移除数据提示--%>
    <div class="bonceContainer bounce-blue" id="deteation">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>确定后，此处将不再展示内容！</p>
            </div>
        </div>
        <div class="bonceFoot qspent">
            <%--            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="closenwind(5)">取消</span>--%>
            <%--            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="keepsure()">确定</span>--%>
        </div>
    </div>
    <%-- 与商品关联 --%>
    <div class="bonceContainer bounce-blue" id="catasoiation" style="width: 1100px;">
        <div class="bonceHead">
            <span>与商品关联</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <div class="addProductForm">
                <table class="ty-table ty-table-control" id="productionlook">
                    <tbody>
                    <tr>
                        <td width="18%">产品图号</td>
                        <td width="18%">产品名称</td>
                        <td width="10%">计量单位</td>
                        <td width="10%">所处阶段</td>
                        <td width="10%">单重</td>
                        <td width="10%">加工部门</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>产品说明</td>
                        <td colspan="8"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="relatedGoods">
                    <hr />
                    <div>本页面进行为产品与商品关联的操作</div>
                    <div>——信息不足时，可暂不操作；</div>
                    <div>——“关联管理”模块可进行同等效果的操作；</div>
                    <div>——关联关系的查看与修改需到“关联管理”模块中进行。</div>
                    <div class="clear cmChoose" id="generlist">
                        <div class="cmTip">本产品作为通用型商品销售时，如使用另外的商品代号与名称，请在右侧选框中选择具体为哪个通用型商品。</div>
                        <div class="ty-left">
                            <select id="cmGoods">
                                <option value="">请选择通用型商品</option>
                            </select>
                        </div>
                    </div>
                    <div>本产品作为某客户的专属商品销售时</div>
                    <div>1、如使用另外的商品代号与名称，请先选择客户，之后在该客户的专属商品下选择；</div>
                    <div>2、不同客户下的商品代号如不同，请点击“新增”，之后可选择另一组客户与商品代号。</div>
                    <div class="cmChoose" id="zsGoods">
                        <div class="zsCase">
                            <select class="customerList" onchange="getZsProductList($(this))">
                                <option value="">请选择客户</option>
                            </select>
                            <select class="zsGoodsList" onchange="getzslist($(this))">
                                <option value="">请选择专属商品</option>
                            </select>
                            <span class="blueLinkBtn" onclick="addMore()">新增</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="closenwind(1)" id="closewinden">取消</span>
<%--            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="closenwind(2)" id="closewinden2">取消</span>--%>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="entrycomple()" id="entrover">确定</span>
        </div>
    </div>
    <%-- 查看产品和产品关联情况--%>
    <div class="bonceContainer bounce-blue" id="viewrecords" style="width: 1277px;">
        <div class="bonceHead">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <div class="addProductForm" style="width: 1000px;">
                <p>产品：</p>
                <table class="ty-table ty-table-control" id="procatbook">
                    <tbody>
                    <tr>
                        <td style="width: 100px;">产品图号</td>
                        <td style="width: 170px;">产品名称/规格/型号</td>
                        <td style="width: 100px;">计量单位</td>
                        <td style="width: 100px;">产品单重</td>
                        <td style="width: 100px;">重量单位</td>
                        <td style="width: 105px;">来源</td>
                        <td style="width: 105px;">构成</td>
                        <td style="width: 194px;">创建人</td>
                    </tr>
                    <tr>
                        <td>XXXXXXX</td>
                        <td>XXXXXXXX/XXXX/XXX</td>
                        <td>XX</td>
                        <td>XX</td>
                        <td>XX</td>
                        <td>XXXXXXXXX</td>
                        <td>XXXXXXXXX</td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    </tbody>
                </table>
                <p style="margin-top: 20px;">本产品已与如下<span class="pronumber">XX</span>种商品关联：</p>
                <table class="ty-table ty-table-control" id="prosoiaion">
                    <tbody>
                    <tr>
                        <td>商品图号</td>
                        <td>商品名称/规格/型号</td>
                        <td>计量单位</td>
                        <td>所属客户</td>
                        <td>创建人</td>
                        <td>关联操作人</td>
                        <td>操作</td>
                    </tr>
                    </tbody>
                </table>
                <p style="margin-top:20px;">本产品已与如下通用商品关联:</p>
                <table class="ty-table ty-table-control" id="tytblen">
                    <tbody>
                    <tr>
                        <td style="width: 13%;">商品图号</td>
                        <td style="width: 22.4%;">商品名称/规格/型号</td>
                        <td style="width: 25.8%;">计量单位</td>
                        <td>创建人</td>
                        <td>关联操作人</td>
                        <td>操作</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closenwind(2)" id="coseendwn">关闭</span>
        </div>
    </div>
    <%-- 点击“增加文件”后的查看按钮 --%>
    <div class="bonceContainer bounce-blue" id="addfilelook" style="width: 1277px;">
        <div class="bonceHead">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <div class="addProductForm" style="width: 1000px;">
                <p>产品：</p>
                <table class="ty-table ty-table-control" id="procatloken">
                    <tbody>
                    <tr>
                        <td style="width: 100px;">产品图号</td>
                        <td style="width: 170px;">产品名称/规格/型号</td>
                        <td style="width: 100px;">计量单位</td>
                        <td style="width: 100px;">产品单重</td>
                        <td style="width: 100px;">重量单位</td>
                        <td style="width: 105px;">来源</td>
                        <td style="width: 105px;">构成</td>
                        <td style="width: 194px;">创建人</td>
                    </tr>
                    <tr>
                        <td>XXXXXXX</td>
                        <td>XXXXXXXX/XXXX/XXX</td>
                        <td>XX</td>
                        <td>XX</td>
                        <td>XX</td>
                        <td>XXXXXXXXX</td>
                        <td>XXXXXXXXX</td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    </tbody>
                </table>
                <p style="margin-top: 20px;">本产品已与如下<span class="pronemd">XX</span>个文件关联：</p>
                <div class="messgnend"></div>
                <%--                <div class="messgnen" style="display: block;width: 1032px;">--%>
                <%--                    <div style="display: flex;">--%>
                <%--                        <div class="plence" style="display: flex;">--%>
                <%--                            <div style="display: inline-block;margin: 37px;margin-top: 10px;margin: 20px 20px;">--%>
                <%--                                <div style="width: 30px;height: 20px;">图标</div>--%>
                <%--                            </div>--%>
                <%--                            <div style="display: inline-block;margin-right: 202px;">--%>
                <%--                                <p style="margin: 0px;margin-top: 10px;">XXXXXXXXXXXXXXXXXXXX</p>--%>
                <%--                                <p>XXX XXXX.XXMB  XXXX-XX-XX XX:XX:XX</p>--%>
                <%--                            </div>--%>
                <%--                        </div>--%>
                <%--                        <div class="plence" style="margin-right: 38px;">--%>
                <%--                            <div style="display: flex;margin-top: 10px;">--%>
                <%--                                <div style="float: left;margin-left: 76px;margin-right: -4px;">编号：XXXXXX</div>--%>
                <%--                                <div style="float: right;margin-left: 39px;">GXXX</div>--%>
                <%--                            </div>--%>
                <%--                            <div>--%>
                <%--                                <span style="font-weight: bold;color:#0b94ea;margin-right: 10px;" type="btn" name="seeOnline">在线预览</span>--%>
                <%--                                <span style="font-weight: bold;color:#0b94ea;margin-right: 10px;" onclick="" type="btn" name="download">下载</span>--%>
                <%--                                <span style="font-weight: bold;color:#0b94ea;margin-right: 10px;" onclick="" type="btn" name="basicMsg">基本信息</span>--%>
                <%--                                <span style="font-weight: bold;color:#0b94ea;margin-right: 10px;" onclick="" type="btn" name="changeVersionRecord">换版记录</span>--%>
                <%--                            </div>--%>
                <%--                        </div>--%>
                <%--                        <div>--%>
                <%--                            <div style="margin-top: 10px;">--%>
                <%--                                <p style="margin-bottom: 0px;">选择：XXX XXXX-XX-XX XX:XX:XX</p>--%>
                <%--                            </div>--%>
                <%--                            <div>--%>
                <%--                                <span style="font-weight: bold;color: #0b94ea;margin-left: 148px;" onclick="overconact()">解除关联</span>--%>
                <%--                            </div>--%>
                <%--                        </div>--%>
                <%--                    </div>--%>
                <%--                </div>--%>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closenwind(3)" id="closeoff">关闭</span>
        </div>
    </div>

    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%-- 产品修改 --%>
    <div class="bonceContainer bounce-blue" id="updateProduct" style="width:1000px;">
        <div class="bonceHead">
            <span>修改基本信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <p>基本信息</p>
            <div>
                <table class="ty-table ty-table-control" id="updateProduction">
                    <tbody>
                    <tr>
                        <td width="25%">图号<i class="xing"></i></td>
                        <td width="25%">名称<i class="xing"></i></td>
                        <td width="25%">规格</td>
                        <td width="25%" colspan="2">型号</td>
                    </tr>
                    <tr>
                        <td><input need name="innerSn" require type="text" /></td>
                        <td><input need name="name" require type="text" /></td>
                        <td><input need name="specifications" type="text" /></td>
                        <td colspan="2"><input need name="model" type="text" /></td>
                    </tr>
                    <tr>
                        <td>所处阶段<i class="xing"></i></td>
                        <td>加工部门<i class="xing"></i></td>
                        <td>计量单位<span class="xing"></span>
                            <span onclick="addUnit($(this))"  style="padding:0 5px; color:#0b94ea;font-weight:bold; ">新增</span></td>
                        <td>单重<i class="xing"></i></td>
                        <td>重量单位<i class="xing"></i></td>
                    </tr>
                    <tr>
                        <td>
                            <select need name="phrase" require>
                                <option value="1">开发中</option>
                                <option value="2">开发完成</option>
                            </select>
                        </td>
                        <td>
                            <input need name="processDeptName" type="text" require id="updateOrg" readonly="readonly" onclick="selectDepart('update')">
                            <input need name="processDept" type="hidden" id="updateOrgID">
                        </td>
                        <td>
                            <select need type="text" id="unitSelect2" name="unitId" require onchange="unitAssign($(this))"></select>
                            <input need type="hidden" name="unit" id="update_unitName">
                        </td>
                        <td><input type="text" id="update_netWeight" name="netWeight" need onkeyup="clearNoNum(this)"></td>
                        <td>
                            <select id="update_weightUnit" name="weightUnit" need>
                                <option value="1">毫克（mg）</option>
                                <option value="2">克（g）</option>
                                <option value="3">千克（kg）</option>
                                <option value="4">吨（T）</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td colspan="8"><input need name="memo" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                    </tr>
                    </tbody>
                </table>
                <p class="wordCount" id="updateMemo" style="text-align: right;">0/100</p>
            </div>
            <div class="add-block ty-color-orange">
                <span class="gapSm">注1</span>此修改对已存在于系统中的订单无法生效！
            </div>
            <div class="add-block">
                <span class="gapLong">系统中包含本产品的未完结订单共<span id="orderUnNum"></span>个</span>
                <span class="ty-btn ty-btn-gray">去查看</span>
            </div>
            <p class="ty-color-orange"><span class="gapSm">注</span>此修改对已导出系统的数据、会计模块中已报税的数据、已完结订单、以及未完结订单中的已开票数据均无法生效！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateProductSure()">确定</span>
        </div>
    </div>
    <%--  中部查看  --%>
    <div class="bonceContainer bounce-blue" id="seePartsDetail" style="width:1200px;">
        <div class="bonceHead">
            <span>组件当前全部的零组件</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">加工部门</td>
                        <td width="12%">单重</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="processDeptName"></td>
                        <td need data-name="netWeight"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="combin">
                    <div>
                        <div class="gspT">
                            <div class="introTtl">当前直接含有本件的产品有XXXX种，具体如下：</div>
                            <div class="ty-color-blue">注：本件如直接组成该件，则装配层级为1，如直接组成该件的1级组件，则装配层级为2，并以此类推</div>
                        </div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="18%">图号</td>
                            <td width="30%">名称/规格/型号</td>
                            <td width="15%">本件的装配层级</td>
                            <td width="15%">本件的装配数量</td>
                            <td width="22%">参与构成的时间</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--   产品当前全部的零组件 --%>
    <div class="bonceContainer bounce-blue" id="seePartsGroup" style="width:1200px;">
        <div class="bonceHead">
            <span>产品当前全部的零组件</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%" class="pro-name">名称/规格/型号</td>
                        <td class="pro-specifications">规格</td>
                        <td class="pro-model">型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">加工部门</td>
                        <td width="12%">单重</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="processDeptName"></td>
                        <td need data-name="netWeight"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="tbMain">
                    <div>
                        <div class="gspT">
                            <div class="ty-color-blue">注1：参与构成本件的零组件中，直接参与的层级为1，直接构成1级组件的零组件层级为2，并以此类推</div>
                            <div class="ty-color-blue">注2：为节约资源，系统展出为1-4级的零组件。如需查看某个4级零组件的组成，请点击该件的“层级”</div>
                        </div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="6%">层级</td>
                            <td width="10%">图号</td>
                            <td class="trends-name">名称/规格/型号</td>
                            <td class="trends-specifications">规格</td>
                            <td class="trends-model">型号</td>
                            <td width="10%">计量单位</td>
                            <td width="10%">装配数量</td>
                            <td width="10%">来源</td>
                            <td width="12%">构成</td>
                            <td width="12%">单重</td>
                            <td width="12%">参与构成的时间</td>
                        </tr>
                        <tr>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="hd" id="allPartsGroup"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--全部零组件查看设置--%>
    <div class="bonceContainer bounce-blue" id="viewSettings" style="width:800px;">
        <div class="bonceHead">
            <span id="supConTtl">全部零组件查看设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="settingsCon">
                <p>全部零组件展示页上的当前设置如下</p>
                <div class="leMar">
                    <p>产品规格、型号的展示</p>
                    <p>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>都展示 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>都不展示 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>只展示规格 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>只展示型号 </span>
                    </p>
                </div>
                <div class="leMar">
                    <p>零组件规格、型号的展示</p>
                    <p>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>都展示 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>都不展示 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>只展示规格 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>只展示型号 </span>
                    </p>
                </div>
                <div class="leMar">
                    <p>名称、规格、型号是否展示于同一个框内？</p>
                    <p>
                        <span class="radioCon"> <i class="fa fa-circle-o"></i>是</span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>否</span>
                    </p>
                </div>
                <div class="leMar">
                    <p>以后查看某个产品的的全部零组件时，都按上述设置展示吗？</p>
                    <p>
                        <span class="radioCon"> <i class="fa fa-circle-o"></i>是</span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>不，上述设置仅对此此次查看有效</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="viewSettingsOk()">确定</span>
        </div>
    </div>
    <%--   曾经包含的零组件 --%>
    <div class="bonceContainer bounce-blue" id="seeOncePartsGroup" style="width:1200px;">
        <div class="bonceHead">
            <span>曾经包含的零组件</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">加工部门</td>
                        <td width="12%">单重</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="processDeptName"></td>
                        <td need data-name="netWeight"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="tbMain tbMain2">
                    <div>
                        <div class="gspT">
                            <div class="ty-color-blue">注1：以下层级为1的为曾直接构成本件的零组件，层级不为1的是构成这些1级组件的零组件</div>
                            <div class="ty-color-blue">注2：曾直接构成本件的某件被删除后，可能再次参与构成，之后又被删除，且此过程可能多次反复。故下表中同一个件可能出现多次</div>
                        </div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="6%">层级</td>
                            <td width="10%">图号</td>
                            <td width="18%">名称/规格/型号</td>
                            <td width="10%">计量单位</td>
                            <td width="10%">装配数量</td>
                            <td width="8%">来源</td>
                            <td width="8%">构成</td>
                            <td width="8%">单重</td>
                            <td width="12%">参与构成的时间</td>
                            <td width="10%">在构成中被删除的时间</td>
                        </tr>
                        <tr>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--修改来源/构成--%>
    <div class="bonceContainer bounce-blue" id="editPartsSource" style="width:1000px;">
        <div class="bonceHead">
            <span>修改来源/构成</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <div>
                    基本信息
                    <p>创建：<span class="creatInfo"></span></p>
                </div>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">单重</td>
                        <td width="12%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="netWeight"></td>
                        <td need data-name="processDeptName"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="editSourceSect">
                    <input id="actual_process" type="hidden"/>
                    <input id="actual_composition" type="hidden"/>
                    <div class="origin">
                        <span class="gapRt">加工方情况<span class="ty-color-red">*</span></span>
                        <select id="edit_process" class="entry">
                            <option value="">--- 请选择 ---</option>
                            <option value="1">委托外部加工</option>
                            <option value="2">加工方为本公司</option>
                            <option value="3">本公司自制，但有时外包</option>
                        </select>
                    </div>
                    <div class="origin">
                        <div>
                            <span class="gapRt">构成情况<span class="ty-color-red">*</span></span>
                            <select class="entry" id="edit_composition"><%-- readonly="readonly" onfocus="this.defOpt=this.selectedIndex" onchange="this.selectedIndex=this.defOpt;"--%>
                                <option value="">--- 请选择 ---</option>
                                <option value="2" onclick="giveTip()">为装配件</option>
                                <option value="3" onclick="giveTip()">材料需由多种原料先混合</option>
                                <option value="4" onclick="giveTip()">由所购买的单一材料直接加工而成</option>
                            </select>
                        </div>
                        <div class="ty-right linkBtn" data-type="editInsidePart" id="editInsidePart">修改组成本组件的零组件</div>
                    </div>
                    <p class="ty-color-blue assemblingTip">注：装配件需拆分产品，请继续操作！</p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="editPartsSourceSure()">确定</span>
        </div>
    </div>
    <%--停止生产的操作记录--%>
    <div class="bonceContainer bounce-blue" id="partSuspendRecord" style="width:600px;">
        <div class="bonceHead">
            <span>停止生产的操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="60%">操作</td>
                        <td width="40%">操作内容</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 基本信息的修改记录 --%>
    <div class="bonceContainer bounce-blue " id="baseRecords" style="width: 550px;">
        <div class="bonceHead">
            <span id="logType">基本信息的修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="curSta"></p>
                <div id="assemblyTip">
                    <div>注：直接组成本件零组件本身的修改不计入本件的修改记录</div>
                    <div>所谓本身的修改，系指其基本信息、构成信息或其他实质性的修改，或其各级零组件的修改</div>
                </div>
                <table class="ty-table ty-table-control">
                    <tr>
                        <td>资料状态</td>
                        <td>操 作</td>
                        <td>创建人/修改人</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  零组件拆分  --%>
    <div class="bonceContainer bounce-blue" id="splitGS2">
        <div class="bonceHead">
            <span>零组件拆分</span>
            <a class="bounce_close" onclick="goBack(0)"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>待拆分的产品</p>
                <table class="ty-table" style="width: 75%;">
                    <tbody>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                    </tr>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                    </tr>
                    </tbody>
                </table>
                <p></p><p>该产品由以下零组件组合而成 <span class="ty-btn linkBtn" data-type="partEntry">录入直接组成该产品的零组件</span>(一般为装配图明细表中内容)</p>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                        <td>装配数量</td>
                        <td>来源</td>
                        <td>构成</td>
                        <td>拆分情况</td>
                        <td>操作</td>
                    </tr>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                        <td>装配数量</td>
                        <td>来源</td>
                        <td>构成</td>
                        <td>拆分情况</td>
                        <td>
                            <span type="btn" data-type="splitBtn2" class="ty-color-blue">编辑</span>
                            <span type="btn" data-type="splitBtn2" class="ty-color-blue">修改</span>
                            <span type="btn" data-type="splitBtn2" class="ty-color-blue">删除</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-left ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="goBack(0)">返回</span>
            <span style="margin-left:40px; " class="ty-left ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="goBack(1)">返回产品拆分</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="splitOKBtn(2)">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2" style="display: none;">
    <%-- 文件查看 --%>
    <div class="bonceContainer bounce-blue" id="docShow">
        <div class="bonceHead">
            <span>文件内容查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon ty-searchContent">
            <div class="ty-center fileList">
                <div></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel();">关闭</span>
        </div>
    </div>
    <%--  换版记录 --%>
    <div class="bonceContainer bounce-blue" id="resHistory" style="width: 1050px;">
        <div class="bonceHead">
            <span>换版记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-searchContent">
                <div class="mar">
                    <%--                    <div class="text-right" style="margin-bottom: 8px;">--%>
                    <%--                        <button class="ty-btn ty-btn-blue ty-circle-3" onclick="goBack('record')">返回</button>--%>
                    <%--                    </div>--%>
                    <div class="fileList">
                        <%--文件列表--%>
                    </div>
                    <div  id="ye_record"></div>
                </div>
            </div>
            <div></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel();">关闭</span>
        </div>
    </div>
    <%-- 解除关联提示弹窗--%>
    <div class="bonceContainer bounce-blue" id="relasecntact">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>确定后，本产品与本文件将解除关联！ </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="closenwind(4)">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="keepsure" onclick="madesure()">确定</span>
        </div>
    </div>
    <%--零组件录入--%>
    <div class="bonceContainer bounce-blue" id="partEntry" onclick="stopMtch(event)" style="width:1000px;">
        <div class="bonceHead">
            <span>零组件录入</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>您正在录入直接组成“<span class="red" id="gsName">XXX商品名</span>”的零组件！请根据实际情况操作。</p>
            <p>您可选择已录入至系统的产品或零组件，也可录入新的零组件。</p>
            <table class="ty-table">
                <tbody>
                <tr>
                    <td>图号<i class="xing"></i></td>
                    <td>名称<i class="xing"></i></td>
                    <td colspan="2">规格</td>
                    <td colspan="2">型号</td>
                </tr>
                <tr>
                    <input type="hidden" id="id" class="entry">
                    <input type="hidden" id="oldId" class="entry">
                    <td>

                        <input type="text" id="innerSn" class="entry" onclick="startMatch(event)">
                        <div id="selecGS">
                            <option value=""></option>
                        </div>
                    </td>
                    <td><input type="text" id="name" class="entry" onfocus="$(this).click()"></td>
                    <td colspan="2"><input type="text" id="specifications" class="entry"></td>
                    <td colspan="2"><input type="text" id="model" class="entry"></td>
                </tr>
                <tr>
                    <td>本零组件的加工方情况<i class="xing"></i></td>
                    <td>本零组件的构成情况<i class="xing"></i></td>
                    <td>计量单位<i class="xing"></i>
                        <span id="addUnitBtn" onclick="addUnit($(this))"  style="padding:0 5px; color:#0b94ea;font-weight:bold; ">新增</span>
                    </td>
                    <td>装配数量<i class="xing"></i></td>
                    <td>单重<i class="xing"></i></td>
                    <td>重量单位<i class="xing"></i></td>
                </tr>
                <tr>
                    <td><select id="process" class="entry" style="width: 170px;">
                        <option value="">--- 请选择 ---</option>
                        <option value="2">加工方为本公司</option>
                        <option value="1">委托外部加工</option>
                        <option value="3">本公司自制，但有时外包</option>
                    </select></td>
                    <td><select class="entry" id="composition" style="width: 230px;">
                        <option value="">--- 请选择 ---</option>
                        <option value="2">也是装配件</option>
                        <option value="3">材料需由多种原料先混合</option>
                        <option value="4">由所购买的单一材料直接加工而成</option>
                    </select></td>
                    <td><select class="entry" id="unitId" name="unitId"  onchange="unitAssign($(this))" style="width: 110px;"></select>
                        <input type="hidden" id="unit" class="entry"></td>
                    <td><input type="text" id="amount" class="entry" onkeyup="clearNoNum(this)"></td>
                    <td><input type="text" id="netWeight" class="entry" onkeyup="clearNoNum(this)"></td>
                    <td>
                        <select id="weightUnit" class="entry" style="width: 110px;">
                            <option value="1">毫克（mg）</option>
                            <option value="2">克（g）</option>
                            <option value="3">千克（kg）</option>
                            <option value="4">吨（T）</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>说明</td>
                    <td colspan="5"><textarea onkeyup="countWords($(this),100)" class="entry" id="memo" placeholder="此处最多可录入100字"></textarea><span class="ty-right textMax">0/100</span></td>

                </tr>
                </tbody>
            </table>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel();">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="partEntryOK();" id="partEntryOK">录入完毕</span>
        </div>
    </div>

    <%-- 提示 --%>
    <div class="bonceContainer bounce-red" id="unfilledTip">
        <div class="bonceHead">
            <span>!提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p id="unfilledTip_ms">还有必填项尚未填写！ </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 请选择加工单位 --%>
    <div class="bonceContainer bounce-blue" id="selectDeapar" style="width: 500px">
        <div class="bonceHead">
            <span>请选择 <span id="selectTtl">加工单位</span></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon clearfix" style="height: 300px;overflow: auto">
            <div class="ty-colFileTree"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="sureNewDivisionBtn"
                    onclick="selectDepartOk()">确定
            </button>
        </div>
    </div>
    <%--基本信息的修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="scanBaseRecords" style="width:800px;">
        <div class="bonceHead">
            <span>基本信息的修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <p>基本信息</p>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">单重</td>
                        <td width="12%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="netWeight"></td>
                        <td need data-name="processDeptName"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%--来源/构成修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="scancompositionRecords" style="width:800px;">
        <div class="bonceHead">
            <span>来源/构成修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <p>基本信息</p>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">单重</td>
                        <td width="12%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="netWeight"></td>
                        <td need data-name="processDeptName"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="elemFlex" id="cDescript"></div>
                <div class="otherOnly">
                    <div class="compRecord1">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td width="30%">材料代号</td>
                            <td width="40%">材料名称/规格/型号</td>
                            <td width="30%">计量单位</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="compRecord2">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td>配方代号</td>
                            <td>配方/材料名称</td>
                            <td>主料</td>
                            <td>辅料</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="compRecord3">
                        <p class="ty-color-blue">注：下表中的红字为本次修改内容，其中整行为红、不带红色横线的，为新增加的零组件，带有红线的为本次删除的零组件</p>
                        <div class="scropWrap">
                            <div class="innerElem">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="12%">图号</td>
                                    <td width="12%">名称/规格/型号</td>
                                    <td width="8%">计量单位</td>
                                    <td width="8%">装配数量</td>
                                    <td width="8%">来源</td>
                                    <td width="8%">构成</td>
                                    <td width="8%">单重</td>
                                    <td width="8%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%--  提示  --%>
    <div class="bonceContainer bounce-blue giveTip " id="giveTip7">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon ty-center" id="msg">
            <p>确定删除该行零组件吗？ </p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce_Fixed2.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="delPartOk()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue giveTip" id="giveTip8">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="msg"></p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce_Fixed2.cancel();">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue giveTip" id="giveTip9">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="type0">该产品的零组件尚未拆分完。</p>
                <p class="type2">该零件的零组件尚未拆分完。</p>
            </div>
            <p>您操作的结果已保存，但该产品将依旧在“待处理”中。</p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="splitOK()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <div class="bonceContainer bounce-blue giveTip" id="giveTip6">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipC">还有必填项尚未填写！</p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce_Fixed3.cancel()">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue giveTip" id="giveTip9">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="type0">该产品的零组件尚未拆分完。</p>
                <p class="type2">该零件的零组件尚未拆分完。</p>
            </div>
            <p>您操作的结果已保存，但该产品将依旧在“待处理”中。</p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="splitOK()">确定</span>
        </div>
    </div>
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType" />
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(3)">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>产品档案</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainData">
                    <%--material massage--%>
                    <div id="picShow" style="display: none;">
                        <img src=""/>
                    </div>
                    <%--                    <div id="docShow" style="display: none;">--%>
                    <%--                        <div class="ty-searchContent">--%>
                    <%--                            <div class="fileList">--%>
                    <%--                                <div href=""></div>--%>
                    <%--                            </div>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <input type="hidden" id="showMainConNum" />
                    <div id="mtInfo" class="Con mainCon mainCon1">
                        <div class="bigContainer clear">
                            <div class="left_container">
                                <div class="Btop" id="firstLevel"><span id="firstLevelName">全部</span>(<span id="firstLevelAmount">0</span>种)</div>
                                <div >
                                    <form>
                                        <ul class="faceul" id="kindsTree"></ul>
                                        <div class="faceul1 bottom suspendBtn">
                                            <a>
                                                <span><span onclick="suspendPdSet($(this))">停止生产的产品</span>（<span id="suspendPdNum">0</span>种)</span>
                                            </a>
                                        </div>
                                        <div class="left-bottom bottom clear" style="display: none;">
                                            <div class="add-b" onclick="gobackLstLevel(1)"> <a> <span>返回上一级</span> </a>  </div>
                                            <div class="add-b" onclick="gobackLstLevel(2)" > <a> <span >返回全部</span>  </a> </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="right_container">
                                <div class="Right-label" id="right_container">
                                    <div class="container_nav">
                                        <div class="lineQuery">
                                            <div class="ty-left">系统中的产品如下</div>
                                            <div class="queryItem">
                                                <span class="ssTtl">来源</span>
                                                <select class="kj-select kj-select-blue" data-fun="1" onchange="searchKeyBase(3,$(this))">
                                                    <option value=""> 全部 </option>
                                                    <option value="2">加工方为本公司</option>
                                                    <option value="1">委托外部加工</option>
                                                    <option value="3">本公司自制，但有时外包</option>
                                                </select>
                                            </div>
                                            <div class="queryItem">
                                                <span class="ssTtl">构成</span>
                                                <select class="kj-select kj-select-blue" data-fun="1" onchange="searchKeyBase(4,$(this))">
                                                    <option value=""> 全部 </option>
                                                    <option value="2">也是装配件</option>
                                                    <option value="3">材料需由多种原料先混合</option>
                                                    <option value="4">由所购买的单一材料直接加工而成</option>
                                                </select>
                                            </div>
                                            <div class="ty-right searchSect">
                                                <div class="ty-left keywordSearch">
                                                    查找
                                                    <input placeholder="请输入图号或名称内的关键字" id="searchKeyBase" />
                                                </div>
                                                <span class="ty-left ty-btn ty-btn-blue" onclick="searchKeyBase(2)">确定</span>
                                            </div>
                                        </div>
                                        <div class="conon1">
                                            <div class="dq">
                                                <span>当前分类</span>  <span>：</span>
                                                <span id="curID">
                                                        <span onclick="showkindNav($(this))" data-id="" data-name="">全部</span>
                                                    </span>
                                            </div>
                                        </div>
                                        <div class="lineInput">
                                            <p class="ty-left">以下共<span id="xRow"></span>条数据</p>
                                            <div class="ty-right">
                                                <span id="addProductionBtn" class="ty-btn ty-btn-blue" onclick="addProduction()">录入产品</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="inProduct">
                                            <table class="ty-table ty-table-none bg-yellow" id="productionList">
                                                <thead>
                                                <td width="20%">产品图号</td>
                                                <td width="20%">产品名称/型号/规格</td>
                                                <td width="10%">计量单位</td>
                                                <td width="10%">产品单重</td>
                                                <td width="20%">创建人</td>
                                                <td width="20%">操作</td>
                                                </thead>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                        <div class="inSuspend" style="display: none;">
                                            <table class="ty-table ty-table-none bg-yellow" id="suspendList">
                                                <thead>
                                                <td width="20%">产品图号</td>
                                                <td width="20%">产品名称/型号/规格</td>
                                                <td width="10%">计量单位</td>
                                                <td width="10%">产品单重</td>
                                                <td width="20%">停止生产的操作</td>
                                                <td width="20%">操作</td>
                                                </thead>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div id="ye"></div>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon mainCon2" style="display: none">
                        <p>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-fun="backMain" onclick="showMainCon(1)">返 回</span>
                        </p>
                        <p>符合查询条件的数据共以下<span id="ssNum"></span>条</p>
                        <table class="ty-table ty-table-none bg-yellow" id="productionListQ">
                            <thead>
                            <td width="20%">产品图号</td>
                            <td width="20%">产品名称/型号/规格</td>
                            <td width="10%">计量单位</td>
                            <td width="10%">产品单重</td>
                            <td width="20%" class="con3Ttl">创建人</td>
                            <td width="20%">操作</td>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/technology/productArchives.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

