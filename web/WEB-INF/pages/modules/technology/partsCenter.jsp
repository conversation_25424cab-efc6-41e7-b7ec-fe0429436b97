<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/technology/partsCenter.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<<div class="compotionBounce" id="compotionBounce">
</div>
<div class="bounce">

    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="ty-center">
                    <p id="mt_tip_ms"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue " onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 产品录入 --%>
    <div class="bonceContainer bounce-blue" id="addProduct" style="width:900px;">
        <div class="bonceHead">
            <span>产品录入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <p>您正在向“<span class="ty-color-red">待分类</span>”中录入产品！</p>
            <div class="elemFlex">
                <p class="ty-color-redOrange">产品录入后请“拆分”，以便更充分发挥系统功能。</p>
                <span class="ty-btn ty-btn-gray">批量导入</span>
            </div>
            <table class="ty-table ty-table-control" id="productionFrom">
                <tbody>
                <tr>
                    <td width="18%">产品图号<i class="xing">*</i></td>
                    <td width="18%">产品名称<i class="xing">*</i></td>
                    <td width="10%">规格</td>
                    <td width="10%">型号</td>
                    <td width="18%">计量单位<span class="xing">*</span>
                        <span onclick="addUnit($(this))" style="font-weight: bold; color:#0b94ea; padding:0 5px; ">新增</span></td>
                    <td width="14%">所处阶段<i class="xing">*</i></td>
                    <td width="14%">加工部门<i class="xing">*</i></td>
                </tr>
                <tr>
                    <td><input need name="innerSn" type="text" require /></td>
                    <td><input need name="name" type="text" require /></td>
                    <td><input need name="specifications" type="text" /></td>
                    <td><input need name="model" type="text" /></td>
                    <td>
                        <select need type="text" id="unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                        <input need type="hidden" name="unit" id="add_unitName">
                    </td>
                    <td>
                        <select need name="phrase" require>
                            <option value="1">开发中</option>
                            <option value="2">开发完成</option>
                        </select>
                    </td>
                    <td>
                        <input need name="processDeptName" type="text" require id="createOrg" readonly="readonly" onclick="selectDepart('create')">
                        <input need name="processDept" type="hidden" id="createOrgID">
                    </td>
                </tr>
                <tr>
                    <td>产品说明</td>
                    <td colspan="8"><input need name="memo" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                </tr>
                </tbody>
            </table>
            <p class="wordCount" style="text-align: right;">0/100</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addProductionSure()">录入完毕</span>

        </div>
    </div>
    <%--   零组件查看 --%>
    <div class="bonceContainer bounce-blue" id="seeParts" style="width:1000px;">
        <div class="bonceHead">
            <span>零组件查看</span>
            <a class="bounce_close" onclick="closePartScan()"></a>
        </div>
        <div class="bonceCon">
            <div class="add-block ty-color-orange" id="stopDetails">
                <span id="stopName">XXX</span>已于<span id="stopDate">XXXX-XX-XX XX:XX:XX</span>将本产品“停止生产”！</span>
            </div>
            <div class="hd" id="partData"></div>
            <div class="scan-wrap">
                <div class="elemFlex">
                    <div>
                        <span>基本信息</span>
                        <div>创建：<span id="seeCreater"></span></div>
                    </div>
                    <div class="partsScan1">
                        <span class="ty-btn ty-btn-blue partsScan2" data-type="updateParts">修改基本信息</span>
                        <span class="ty-btn ty-btn-blue" data-type="baseRecords">基本信息的修改记录</span>
                        <span class="ty-btn ty-btn-blue" data-type="ptSuspendRecord">停止生产的操作记录</span>
                    </div>
                </div>
                <table class="ty-table ty-table-control" id="seePartsInit">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">单重</td>
                        <td width="12%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="netWeight"></td>
                        <td need data-name="processDeptName"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <hr/>
                <div class="clear ">
                    <div class="double-sample ty-left">
                        <p>当前含有本件的产品/组件中</p>
                        <div class="cell-sample">
                            <div class="row-sample clear">
                                <span class="ty-left">直接含有本件的产品</span>
                                <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="1">查看</span>
                                <span class="ty-right"><span class="several" data-name="directPartProduct"></span>种</span>
                            </div>
                            <div class="row-sample clear">
                                <span class="ty-left">间接含有本件的产品</span>
                                <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="2">查看</span>
                                <span class="ty-right"><span class="several" data-name="indirectPartProduct"></span>种</span>
                            </div>
                            <div class="row-sample clear">
                                <span class="ty-left">直接含有本件的组件</span>
                                <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="3">查看</span>
                                <span class="ty-right"><span class="several" data-name="directPartComposition"></span>种</span>
                            </div>
                            <p class="ty-color-blue">此处的组件仅包含组成产品的装配件，而不包含产品</p>
                        </div>
                    </div>
                    <div class="double-sample ty-right partsScan1">
                        <p>曾含有、但已不再含有本件的产品/组件中</p>
                        <div class="cell-sample">
                            <div class="row-sample clear">
                                <span class="ty-left">曾直接含有本件的产品</span>
                                <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="4">查看</span>
                                <span class="ty-right"><span class="several" data-name="formerDirectPartProduct"></span>种</span>
                            </div>
                            <div class="row-sample clear">
                                <span class="ty-left">曾间接含有本件的产品</span>
                                <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="5">查看</span>
                                <span class="ty-right"><span class="several" data-name="formerIndirectPartProduct"></span>种</span>
                            </div>
                            <div class="row-sample clear">
                                <span class="ty-left">曾直接含有本件的组件</span>
                                <span class="ty-right linkBtn" data-type="directlyProducts" data-lang="6">查看</span>
                                <span class="ty-right"><span class="several" data-name="formerdirectPartComposition"></span>种</span>
                            </div>
                            <p class="ty-color-blue">此处的组件仅包含组成产品的装配件，而不包含产品</p>
                        </div>
                    </div>
                </div>
                <hr/>
                <div class="elemFlex">
                    <div>
                        <span id="compositionDetails">本件委托外部加工</span>
                        <div>最新编辑：<span id="updateLast"></span></div>
                    </div>
                    <div class="partsScan1">
                        <span class="ty-btn ty-btn-blue partsScan2" data-type="editSource">修改来源/构成</span>
                        <span class="ty-btn ty-btn-blue" data-type="compositionRecords">来源/构成修改记录</span>
                    </div>
                </div>
                <div class="only">
                    <div class="matInfo1">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td width="30%">材料代号</td>
                            <td width="40%">材料名称/规格/型号</td>
                            <td width="30%">计量单位</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="matInfo2">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td>配方代号</td>
                            <td>配方/材料名称</td>
                            <td>主料</td>
                            <td>辅料</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="matInfo3">
                        <div class="elemFlex">
                            <span>以下为当前状态下本件直接包含的零组件</span>
                            <div>
                                <span class="ty-btn ty-btn-blue" data-type="allParts">当前全部的零组件</span>
                                <div class="partsScan1" style="display: inline">
                                    <span class="ty-btn ty-btn-blue" data-type="viewSettings">全部零组件查看设置</span>
                                    <span class="ty-btn ty-btn-blue" data-type="allOnceParts">曾经包含的零组件</span>
                                </div>
                                <span class="hd" id="viewSettingsState"></span>
                            </div>
                        </div>
                        <div class="scropWrap">
                            <div class="innerElem">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="12%">图号</td>
                                    <td width="12%">名称/规格/型号</td>
                                    <td width="8%">计量单位</td>
                                    <td width="8%">装配数量</td>
                                    <td width="8%">来源</td>
                                    <td width="8%">构成</td>
                                    <td width="8%">单重</td>
                                    <td width="14%">参与构成的最新操作</td>
                                    <td width="8%">操作</td>
                                    </thead>
                                    <tbody>
                                    <td>图号</td>
                                    <td>名称/型号/规格</td>
                                    <td>计量单位</td>
                                    <td>计量单位</td>
                                    <td>单重</td>
                                    <td>直接参与组装的产品</td>
                                    <td>直接参与组装的组件</td>
                                    <td>创建人</td>
                                    <td>
                                        <span class="ty-color-blue linkBtn" data-type="partScan">查看</span>
                                        <span class="hd"></span>
                                    </td>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closePartScan()">关闭</span>
        </div>
    </div>
    <%-- ！提示 -删除 --%>
    <div class="bonceContainer bounce-red" id="deleteProduct">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="deleteTip"></div>
        </div>
        <div class="bonceFoot">
            <span class="delRefuse ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">我知道了</span>
            <span class="deleteCan ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="deleteCan ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="deleteProductSure()">确定</span>
        </div>
    </div>
    <%-- ！提示 -暂停销售 --%>
    <div class="bonceContainer bounce-red" id="pauseProduct">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="pauseProductTip" class="ty-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="pauseRefuse ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="pauseCan ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="suspendSaleSure()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--  中部查看  --%>
    <div class="bonceContainer bounce-blue" id="seePartsDetail" style="width:1200px;">
        <div class="bonceHead">
            <span>组件当前全部的零组件</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">加工部门</td>
                        <td width="12%">单重</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="processDeptName"></td>
                        <td need data-name="netWeight"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="combin">
                    <div>
                        <div class="gspT">
                            <div class="introTtl">当前直接含有本件的产品有XXXX种，具体如下：</div>
                            <div class="ty-color-blue">注：本件如直接组成该件，则装配层级为1，如直接组成该件的1级组件，则装配层级为2，并以此类推</div>
                        </div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="18%">图号</td>
                            <td width="30%">名称/规格/型号</td>
                            <td width="15%">本件的装配层级</td>
                            <td width="15%">本件的装配数量</td>
                            <td width="22%">参与构成的时间</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--   组件当前全部的零组件 --%>
    <div class="bonceContainer bounce-blue" id="seePartsGroup" style="width:1200px;">
        <div class="bonceHead">
            <span>组件当前全部的零组件</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">加工部门</td>
                        <td width="12%">单重</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="processDeptName"></td>
                        <td need data-name="netWeight"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="tbMain">
                    <div>
                        <div class="gspT">
                            <div class="ty-color-blue">注1：参与构成本件的零组件中，直接参与的层级为1，直接构成1级组件的零组件层级为2，并以此类推</div>
                            <div class="ty-color-blue">注2：为节约资源，系统展出为1-4级的零组件。如需查看某个4级零组件的组成，请点击该件的“层级”</div>
                        </div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="6%">层级</td>
                            <td width="10%">图号</td>
                            <td class="trends-name">名称/规格/型号</td>
                            <td class="trends-specifications">规格</td>
                            <td class="trends-model">型号</td>
                            <td width="10%">计量单位</td>
                            <td width="10%">装配数量</td>
                            <td width="10%">来源</td>
                            <td width="12%">构成</td>
                            <td width="12%">单重</td>
                            <td width="12%">参与构成的时间</td>
                        </tr>
                        <tr>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="hd" id="allPartsGroup"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--全部零组件查看设置--%>
    <div class="bonceContainer bounce-blue" id="viewSettings" style="width:800px;">
        <div class="bonceHead">
            <span id="supConTtl">全部零组件查看设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="settingsCon">
                <p>全部零组件展示页上的当前设置如下</p>
                <div class="leMar">
                    <p>规格、型号的展示</p>
                    <p>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>都展示 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>都不展示 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>只展示规格 </span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>只展示型号 </span>
                    </p>
                </div>
                <div class="leMar">
                    <p>名称、规格、型号是否展示于同一个框内？</p>
                    <p>
                        <span class="radioCon"> <i class="fa fa-circle-o"></i>是</span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>否</span>
                    </p>
                </div>
                <div class="leMar">
                    <p>以后查看某组件的全部零组件时，都按上述设置展示吗？</p>
                    <p>
                        <span class="radioCon"> <i class="fa fa-circle-o"></i>是</span>
                        <span class="radioCon"><i class="fa fa-circle-o"></i>不，上述设置仅对此此次查看有效</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="viewSettingsOk()">确定</span>
        </div>
    </div>
    <%--   曾经包含的零组件 --%>
    <div class="bonceContainer bounce-blue" id="seeOncePartsGroup" style="width:1200px;">
        <div class="bonceHead">
            <span>曾经包含的零组件</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">加工部门</td>
                        <td width="12%">单重</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="processDeptName"></td>
                        <td need data-name="netWeight"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="tbMain tbMain2">
                    <div>
                        <div class="gspT">
                            <div class="ty-color-blue">注1：以下层级为1的为曾直接构成本件的零组件，层级不为1的是构成这些1级组件的零组件</div>
                            <div class="ty-color-blue">注2：曾直接构成本件的某件被删除后，可能再次参与构成，之后又被删除，且此过程可能多次反复。故下表中同一个件可能出现多次</div>
                        </div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="6%">层级</td>
                            <td width="10%">图号</td>
                            <td width="18%">名称/规格/型号</td>
                            <td width="10%">计量单位</td>
                            <td width="10%">装配数量</td>
                            <td width="8%">来源</td>
                            <td width="8%">构成</td>
                            <td width="8%">单重</td>
                            <td width="12%">参与构成的时间</td>
                            <td width="10%">在构成中被删除的时间</td>
                        </tr>
                        <tr>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--修改来源/构成--%>
    <div class="bonceContainer bounce-blue" id="editPartsSource" style="width:1000px;">
        <div class="bonceHead">
            <span>修改来源/构成</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <div>
                    基本信息
                    <p>创建：<span class="creatInfo"></span></p>
                </div>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">单重</td>
                        <td width="12%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="netWeight"></td>
                        <td need data-name="processDeptName"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="editSourceSect">
                    <input id="actual_process" type="hidden"/>
                    <input id="actual_composition" type="hidden"/>
                    <div class="origin">
                        <span class="gapRt">加工方情况<span class="ty-color-red">*</span></span>
                        <select id="edit_process" class="entry">
                            <option value="">--- 请选择 ---</option>
                            <option value="1">委托外部加工</option>
                            <option value="2">加工方为本公司</option>
                            <option value="3">本公司自制，但有时外包</option>
                        </select>
                    </div>
                    <div class="origin">
                        <div>
                            <span class="gapRt">构成情况<span class="ty-color-red">*</span></span>
                            <select class="entry" id="edit_composition"><%-- readonly="readonly" onfocus="this.defOpt=this.selectedIndex" onchange="this.selectedIndex=this.defOpt;"--%>
                                <option value="">--- 请选择 ---</option>
                                <option value="2" onclick="giveTip()">为装配件</option>
                                <option value="3" onclick="giveTip()">材料需由多种原料先混合</option>
                                <option value="4" onclick="giveTip()">由所购买的单一材料直接加工而成</option>
                            </select>
                        </div>
                        <div class="ty-right linkBtn" data-type="editInsidePart" id="editInsidePart">修改组成本组件的零组件</div>
                    </div>
                    <p class="ty-color-blue assemblingTip">注：装配件需拆分产品，请继续操作！</p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="editPartsSourceSure()">确定</span>
        </div>
    </div>
    <%--停止生产的操作记录--%>
    <div class="bonceContainer bounce-blue" id="partSuspendRecord" style="width:600px;">
        <div class="bonceHead">
            <span>停止生产的操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="30%">操作</td>
                        <td width="20%">操作内容</td>
                        <td width="50%">原因</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--修改基本信息--%>
    <div class="bonceContainer bounce-blue" id="updateParts" style="width:1000px;">
        <div class="bonceHead">
            <span>修改基本信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon specialForm">
            <p>基本信息</p>
            <div>
                <table class="ty-table ty-table-control" id="updateProduction">
                    <tbody>
                    <tr>
                        <td width="25%">图号<i class="xing"></i></td>
                        <td width="25%">名称<i class="xing"></i></td>
                        <td width="25%">规格</td>
                        <td width="25%" colspan="2">型号</td>
                    </tr>
                    <tr>
                        <td><input need name="innerSn" require type="text" /></td>
                        <td><input need name="name" require type="text" /></td>
                        <td><input need name="specifications" type="text" /></td>
                        <td colspan="2"><input need name="model" type="text" /></td>
                    </tr>
                    <tr>
                        <td>所处阶段<i class="xing"></i></td>
                        <td>加工部门<i class="xing"></i></td>
                        <td>计量单位<span class="xing"></span>
                            <span onclick="addUnit($(this))"  style="padding:0 5px; color:#0b94ea;font-weight:bold; ">新增</span></td>
                        <td>单重<i class="xing"></i></td>
                        <td>重量单位<i class="xing"></i></td>
                    </tr>
                    <tr>
                        <td>
                            <select need name="phrase" require>
                                <option value="1">开发中</option>
                                <option value="2">开发完成</option>
                            </select>
                        </td>
                        <td>
                            <input need name="processDeptName" type="text" require id="updateOrg" readonly="readonly" onclick="selectDepart('update')">
                            <input need name="processDept" type="hidden" id="updateOrgID">
                        </td>
                        <td>
                            <select need type="text" id="unitSelect2" name="unitId" require onchange="unitAssign($(this))"></select>
                            <input need type="hidden" name="unit" id="update_unitName">
                        </td>
                        <td><input type="text" id="update_netWeight" name="netWeight" need onkeyup="clearNoNum(this)"></td>
                        <td>
                            <select id="update_weightUnit" name="weightUnit" need>
                                <option value="1">毫克（mg）</option>
                                <option value="2">克（g）</option>
                                <option value="3">千克（kg）</option>
                                <option value="4">吨（T）</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td colspan="8"><input need name="memo" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="add-block ty-color-orange gspT">
                <div class="gapSm">注:</div>
                <div>1、修改后，本件在以下场景将展示为新数据</div>
                <div>1） 系统内，所参与产品或零组件的构成清单中，及零组件档案的列表中</div>
                <div>2） 在系统内各处被选用时</div>
                <div>3） 选用将要产生的记录中</div>
                <div>2、系统内已有记录中，本件依旧展示为修改前的数据</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updatePartSure()">确定</span>
        </div>
    </div>
    <%-- 基本信息的修改记录 --%>
    <div class="bonceContainer bounce-blue " id="baseRecords" style="width: 550px;">
        <div class="bonceHead">
            <span id="logType">基本信息的修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="curSta"></p>
                <table class="ty-table ty-table-control">
                    <tr>
                        <td>资料状态</td>
                        <td>操 作</td>
                        <td>创建人/修改人</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  零组件拆分  --%>
    <div class="bonceContainer bounce-blue" id="splitGS2">
        <div class="bonceHead">
            <span>零组件拆分</span>
            <a class="bounce_close" onclick="goBack(0)"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>待拆分的产品</p>
                <table class="ty-table" style="width: 75%;">
                    <tbody>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                    </tr>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                    </tr>
                    </tbody>
                </table>
                <p></p><p>该产品由以下零组件组合而成 <span class="ty-btn linkBtn" data-type="partEntry">录入直接组成该产品的零组件</span>(一般为装配图明细表中内容)</p>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                        <td>装配数量</td>
                        <td>来源</td>
                        <td>构成</td>
                        <td>拆分情况</td>
                        <td>操作</td>
                    </tr>
                    <tr>
                        <td>图号</td>
                        <td>名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>单重</td>
                        <td>装配数量</td>
                        <td>来源</td>
                        <td>构成</td>
                        <td>拆分情况</td>
                        <td>
                            <span type="btn" data-type="splitBtn2" class="ty-color-blue">编辑</span>
                            <span type="btn" data-type="splitBtn2" class="ty-color-blue">修改</span>
                            <span type="btn" data-type="splitBtn2" class="ty-color-blue">删除</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="goBack(0)">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="splitOKBtn(2)">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--零组件录入--%>
    <div class="bonceContainer bounce-blue" id="partEntry" onclick="stopMtch(event)" style="width:1000px;">
        <div class="bonceHead">
            <span>零组件录入</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>您正在录入直接组成“<span class="red" id="gsName">XXX商品名</span>”的零组件！请根据实际情况操作。</p>
            <p>您可选择已录入至系统的产品或零组件，也可录入新的零组件。</p>
            <table class="ty-table">
                <tbody>
                <tr>
                    <td>图号<i class="xing"></i></td>
                    <td>名称<i class="xing"></i></td>
                    <td colspan="2">规格</td>
                    <td colspan="2">型号</td>
                </tr>
                <tr>
                    <input type="hidden" id="id" class="entry">
                    <input type="hidden" id="oldId" class="entry">
                    <td>

                        <input type="text" id="innerSn" class="entry" onclick="startMatch(event)">
                        <div id="selecGS">
                            <option value=""></option>
                        </div>
                    </td>
                    <td><input type="text" id="name" class="entry" onfocus="$(this).click()"></td>
                    <td colspan="2"><input type="text" id="specifications" class="entry"></td>
                    <td colspan="2"><input type="text" id="model" class="entry"></td>
                </tr>
                <tr>
                    <td>本零组件的加工方情况<i class="xing"></i></td>
                    <td>本零组件的构成情况<i class="xing"></i></td>
                    <td>计量单位<i class="xing"></i>
                        <span id="addUnitBtn" onclick="addUnit($(this))"  style="padding:0 5px; color:#0b94ea;font-weight:bold; ">新增</span>
                    </td>
                    <td>装配数量<i class="xing"></i></td>
                    <td>单重<i class="xing"></i></td>
                    <td>重量单位<i class="xing"></i></td>
                </tr>
                <tr>
                    <td><select id="process" class="entry" style="width: 170px;">
                        <option value="">--- 请选择 ---</option>
                        <option value="2">加工方为本公司</option>
                        <option value="1">委托外部加工</option>
                        <option value="3">本公司自制，但有时外包</option>
                    </select></td>
                    <td><select class="entry" id="composition" style="width: 230px;">
                        <option value="">--- 请选择 ---</option>
                        <option value="2">也是装配件</option>
                        <option value="3">材料需由多种原料先混合</option>
                        <option value="4">由所购买的单一材料直接加工而成</option>
                    </select></td>
                    <td><select class="entry" id="unitId" name="unitId"  onchange="unitAssign($(this))" style="width: 110px;"></select>
                        <input type="hidden" id="unit" class="entry"></td>
                    <td><input type="text" id="amount" class="entry" onkeyup="clearNoNum(this)"></td>
                    <td><input type="text" id="netWeight" class="entry" onkeyup="clearNoNum(this)"></td>
                    <td>
                        <select id="weightUnit" class="entry" style="width: 110px;">
                            <option value="1">毫克（mg）</option>
                            <option value="2">克（g）</option>
                            <option value="3">千克（kg）</option>
                            <option value="4">吨（T）</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>说明</td>
                    <td colspan="5"><textarea onkeyup="countWords($(this),100)" class="entry" id="memo" placeholder="此处最多可录入100字"></textarea><span class="ty-right textMax">0/100</span></td>

                </tr>
                </tbody>
            </table>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel();">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="partEntryOK();" id="partEntryOK">录入完毕</span>
        </div>
    </div>
    <%--基本信息的修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="scanBaseRecords" style="width:800px;">
        <div class="bonceHead">
            <span>基本信息的修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <p>基本信息</p>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">单重</td>
                        <td width="12%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="netWeight"></td>
                        <td need data-name="processDeptName"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- 请选择加工单位 --%>
    <div class="bonceContainer bounce-blue" id="selectDeapar" style="width: 500px">
        <div class="bonceHead">
            <span>请选择 <span id="selectTtl">加工单位</span></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon clearfix" style="height: 300px;overflow: auto">
            <div class="ty-colFileTree"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="sureNewDivisionBtn"
                    onclick="selectDepartOk()">确定
            </button>
        </div>
    </div>
    <%-- 提示 --%>
    <div class="bonceContainer bounce-red" id="unfilledTip">
        <div class="bonceHead">
            <span>!提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p id="unfilledTip_ms">还有必填项尚未填写！ </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <%--  提示  --%>
    <div class="bonceContainer bounce-blue giveTip " id="giveTip7">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon ty-center" id="msg">
            <p>确定删除该行零组件吗？ </p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce_Fixed2.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="delPartOk()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue giveTip" id="giveTip8">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="msg"></p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce_Fixed2.cancel();">我知道了</span>
        </div>
    </div>
    <%--来源/构成修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="scancompositionRecords" style="width:800px;">
        <div class="bonceHead">
            <span>来源/构成修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <p>基本信息</p>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="18%">图号</td>
                        <td width="22%">名称/规格/型号</td>
                        <td width="12%">计量单位</td>
                        <td width="12%">所处阶段</td>
                        <td width="12%">单重</td>
                        <td width="12%">加工部门</td>
                    </tr>
                    <tr>
                        <td need data-name="innerSn"></td>
                        <td need data-name="name"></td>
                        <td need data-name="unit"></td>
                        <td need data-name="phrase"></td>
                        <td need data-name="netWeight"></td>
                        <td need data-name="processDeptName"></td>
                    </tr>
                    <tr>
                        <td>说明</td>
                        <td need data-name="memo" colspan="8" class="algnL"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="elemFlex" id="cDescript"></div>
                <div class="otherOnly">
                    <div class="compRecord1">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td width="30%">材料代号</td>
                            <td width="40%">材料名称/规格/型号</td>
                            <td width="30%">计量单位</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="compRecord2">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td>配方代号</td>
                            <td>配方/材料名称</td>
                            <td>主料</td>
                            <td>辅料</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="compRecord3">
                        <p class="ty-color-blue">注：下表中的红字为本次修改内容，其中整行为红、不带红色横线的，为新增加的零组件，带有红线的为本次删除的零组件</p>
                        <div class="scropWrap">
                            <div class="innerElem">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="12%">图号</td>
                                    <td width="12%">名称/规格/型号</td>
                                    <td width="8%">计量单位</td>
                                    <td width="8%">装配数量</td>
                                    <td width="8%">来源</td>
                                    <td width="8%">构成</td>
                                    <td width="8%">单重</td>
                                    <td width="8%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(4)">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>产品档案</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainData">
                    <%--material massage--%>
                    <input type="hidden" id="showMainConNum" />
                    <div id="mtInfo" class="Con mainCon mainCon1">
                        <div class="bigContainer clear">
                            <div class="left_container">
                                <div class="Btop" id="firstLevel"><span id="firstLevelName">全部</span>(<span id="firstLevelAmount">0</span>种)</div>
                                <div >
                                    <form>
                                        <ul class="faceul" id="kindsTree"></ul>
                                        <div class="faceul1 bottom suspendBtn">
                                            <a>
                                                <span><span onclick="suspendPartSet($(this))">停止生产的零组件</span>（<span id="suspendPdNum">0</span>种)</span>
                                            </a>
                                        </div>
                                        <div class="left-bottom bottom clear" style="display: none;">
                                            <div class="add-b" onclick="gobackLstLevel(1)"> <a> <span>返回上一级</span> </a>  </div>
                                            <div class="add-b" onclick="gobackLstLevel(2)" > <a> <span >返回全部</span>  </a> </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="right_container">
                                <div class="Right-label" id="right_container">
                                    <div class="container_nav">
                                        <div class="lineQuery">
                                            <div class="ty-left">系统中的零组件如下（不含产品）</div>
                                            <div class="queryItem">
                                                <span class="ssTtl">来源</span>
                                                <select class="kj-select kj-select-blue" onchange="searchKeyPartsBase(3,$(this))" style="width: 240px" >
                                                    <option value=""> 全部 </option>
                                                    <option value="2">加工方为本公司</option>
                                                    <option value="1">委托外部加工</option>
                                                    <option value="3">本公司自制，但有时外包</option>
                                                </select>
                                            </div>
                                            <div class="queryItem">
                                                <span class="ssTtl">构成</span>
                                                <select class="kj-select kj-select-blue" onchange="searchKeyPartsBase(4,$(this))" style="width: 240px">
                                                    <option value=""> 全部 </option>
                                                    <option value="2">也是装配件</option>
                                                    <option value="3">材料需由多种原料先混合</option>
                                                    <option value="4">由所购买的单一材料直接加工而成</option>
                                                </select>
                                            </div>
                                            <div class="ty-right searchSect">
                                                <div class="ty-left keywordSearch">
                                                    查找
                                                    <input placeholder="请输入图号或名称内的关键字" id="searchKeyBase" />
                                                </div>
                                                <span class="ty-left ty-btn ty-btn-blue" onclick="searchKeyPartsBase(2,$(this))">确定</span>
                                            </div>
                                        </div>
                                        <div class="conon hd">
                                            <div class="dq">
                                                <span>当前分类</span>  <span>：</span>
                                                <span id="curID">
                                                        <span onclick="showkindNav($(this))" data-id="" data-name="">全部</span>
                                                    </span>
                                            </div>
                                        </div>
                                        <div class="lineInput">
                                            <p class="ty-left">以下共<span id="xRow"></span>条数据</p>
                                            <div class="ty-right">
                                                <span id="addProductionBtn" class="ty-btn ty-btn-blue" onclick="tempStorage()">暂存于系统的零组件</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="inProduct">
                                            <table class="ty-table ty-table-control ty-table-none bg-yellow" id="productionList">
                                                <thead>
                                                <td width="14%">图号</td>
                                                <td width="18%">名称/型号/规格</td>
                                                <td width="8%">计量单位</td>
                                                <td width="8%">单重</td>
                                                <td width="12%">直接参与组装的产品</td>
                                                <td width="12%">直接参与组装的组件</td>
                                                <td width="18">创建人</td>
                                                <td width="10%">操作</td>
                                                </thead>
                                                <tbody>
                                                <td>图号</td>
                                                <td>名称/型号/规格</td>
                                                <td>计量单位</td>
                                                <td>单重</td>
                                                <td>直接参与组装的产品</td>
                                                <td>直接参与组装的组件</td>
                                                <td>创建人</td>
                                                <td>
                                                    <span class="ty-color-blue linkBtn" data-type="partScan">查看</span>
                                                    <span class="hd"></span>
                                                </td>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="inSuspend" style="display: none;">
                                            <table class="ty-table ty-table-none bg-yellow" id="suspendList">
                                                <thead>
                                                <td width="14%">图号</td>
                                                <td width="15%">名称/型号/规格</td>
                                                <td width="8%">计量单位</td>
                                                <td width="8%">单重</td>
                                                <td width="15%">曾直接参与组装的产品</td>
                                                <td width="15%">曾直接参与组装的组件</td>
                                                <td width="15%">停止生产的操作</td>
                                                <td width="10%">操作</td>
                                                </thead>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div id="ye"></div>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon mainCon2">
                        <p>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-type="backMain">返 回</span>
                        </p>
                        <div class="lineQuery">
                            <div class="ty-left">暂存于系统中的零部件如下</div>
                            <div class="queryItem">
                                <span class="ssTtl">来源</span>
                                <select class="kj-select kj-select-blue" onchange="searchTempParts(3,$(this))">
                                    <option value=""> 全部 </option>
                                    <option value="2">加工方为本公司</option>
                                    <option value="1">委托外部加工</option>
                                    <option value="3">本公司自制，但有时外包</option>
                                </select>
                            </div>
                            <div class="queryItem">
                                <span class="ssTtl">构成</span>
                                <select class="kj-select kj-select-blue" onchange="searchTempParts(4,$(this))" style="width: 230px;">
                                    <option value=""> 全部 </option>
                                    <option value="2">也是装配件</option>
                                    <option value="3">材料需由多种原料先混合</option>
                                    <option value="4">由所购买的单一材料直接加工而成</option>
                                </select>
                            </div>
                            <div class="ty-right searchSect">
                                <div class="ty-left keywordSearch">
                                    查找
                                    <input placeholder="请输入图号或名称内的关键字" id="searchKeyBase1" />
                                </div>
                                <span class="ty-left ty-btn ty-btn-blue" onclick="searchTempParts(2,$(this))">确定</span>
                            </div>
                        </div>
                        <div class="lineInput">
                            <p class="ty-left">以下共<span id="xRow_temp"></span>条数据</p>
                        </div>
                        <div>
                            <table class="ty-table ty-table-none bg-yellow" id="partsTemporaryList">
                                <thead>
                                <td width="15%">图号</td>
                                <td width="15%">名称/型号/规格</td>
                                <td width="8%">计量单位</td>
                                <td width="8%" style="max-width: 139px;">单重</td>
                                <td width="15%">直接参与组装的产品</td>
                                <td width="15%">直接参与组装的组件</td>
                                <td width="14">创建人</td>
                                <td width="10%">操作</td>
                                </thead>
                                <tbody>
                                <td>图号</td>
                                <td>名称/型号/规格</td>
                                <td>计量单位</td>
                                <td>单重</td>
                                <td>直接参与组装的产品</td>
                                <td>直接参与组装的组件</td>
                                <td>创建人</td>
                                <td>
                                    <span class="ty-color-blue linkBtn" data-type="partScan">查看</span>
                                    <span class="hd"></span>
                                </td>
                                </tbody>
                            </table>
                            <div id="ye_temp"></div>
                        </div>
                    </div>
                    <div class="mainCon mainCon3">
                        <p>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-type="backMain">返 回</span>
                        </p>
                        <p>符合查询条件的数据共以下<span id="ssNum"></span>条</p>
                        <table class="ty-table ty-table-none bg-yellow" id="productionListQ">
                            <thead>
                            <td width="14%">图号</td>
                            <td width="18%">名称/型号/规格</td>
                            <td width="8%">计量单位</td>
                            <td width="8%">单重</td>
                            <td width="12%" class="con3Ttl">直接参与组装的产品</td>
                            <td width="12%" class="con3Ttl">直接参与组装的组件</td>
                            <td width="18">创建人</td>
                            <td width="10%">操作</td>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/technology/partsCenter.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

