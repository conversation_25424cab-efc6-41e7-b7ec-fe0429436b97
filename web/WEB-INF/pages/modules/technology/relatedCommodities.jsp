<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/commodity/commodityCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/technology/relation.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
</div>
<<div class="compotionBounce" id="compotionBounce">
</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--去选择产品--%>
    <div class="bonceContainer bounce-blue" id="commodityProductrelate" style="width:1200px;">
        <div class="bonceHead">
            <span>商品与产品关联</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <div class="elemFlex">
                    <span class="ty-color-redOrange">待关联的商品</span>
                    <span class="grayLink">关联记录</span>
                </div>
                <table class="ty-table ty-table-control" id="productDetails">
                    <tbody>
                    <tr>
                        <td>商品代号</td>
                        <td>商品名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>所属客户</td>
                        <td>创建人</td>
                    </tr></tbody>
                </table>
                <div class="elemFlex">
                    <span class="ty-color-redOrange">请选择或查找应与该商品关联的产品。</span>
                    <div class="searchSect">
                        <div class="ty-left keywordSearch">
                            查找产品
                            <input placeholder="请输入产品图号或名称" id="searchPdKey" />
                        </div>
                        <span class="ty-left ty-btn ty-btn-blue" onclick="searchPdKey()">确定</span>
                    </div>
                </div>
            </div>
            <table class="ty-table ty-table-none" id="relationList">
                <tbody>
                <tr>
                    <td class="bg-none"></td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>来源</td>
                    <td>构成</td>
                    <td>产品单重</td>
                    <td>重量单位</td>
                    <td>创建人</td>
                </tr>
                </tbody>
            </table>
            <div id="waiteRl"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关  闭</span>
        </div>
    </div>
    <%--商品与产品关联查看--%>
    <div class="bonceContainer bounce-blue" id="productRelatedSee" style="width:1000px;">
        <div class="bonceHead">
            <span>查看与商品关联的产品</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="elemFlex">
                <span class="ty-color-redOrange">下面的商品与产品由<span id="currName"></span>于<span id="currDate"></span>进行了关联。</span>
                <div>
                    <span class="linkBtn" data-fun="rechoosePoduct">重新关联</span>
                    <span class="grayLink">关联记录</span>
                </div>
            </div>
            <div>
                <div>商品：</div>
                <table class="ty-table ty-table-control" id="relatedGoodsSee">
                    <tbody>
                    <tr>
                        <td>商品代号</td>
                        <td>商品名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>所属客户</td>
                        <td>创建人</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="elemFlex">
                <span>产品:</span>
                <div class="gapM">
                    <span class="grayLink">基本信息修改记录</span>
                    <span class="grayLink">产品构成</span>
                    <span class="grayLink">产品构成修改记录</span>
                    <span class="grayLink">停止生产的操作记录</span>
                </div>
            </div>
            <table class="ty-table ty-table-control" id="relatedProductSee">
                <tbody>
                <tr>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>来源</td>
                    <td>构成</td>
                    <td>产品单重</td>
                    <td>重量单位</td>
                    <td>创建人</td>
                </tr>
                </tbody>
            </table>
            <div class="elemFlex">
                <div>
                    与产品有关联关系的商品共<span class="relatedNumSee"></span>种
                    <span class="linkBtn gapL" data-fun="relateGoodsScan">查看</span>
                </div>
                <span class="grayLink">本产品的关联记录</span>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关  闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--商品与产品关联确定--%>
    <div class="bonceContainer bounce-blue" id="productRelated" style="min-width:1000px;">
        <div class="bonceHead">
            <span>商品与产品关联</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="elemFlex">
                <span class="ty-color-redOrange">待关联的商品</span>
                <span class="grayLink">关联记录</span>
            </div>
            <div>
                <table class="ty-table ty-table-control" id="relatedGoods">
                    <tbody>
                    <tr>
                        <td>商品代号</td>
                        <td>商品名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>所属客户</td>
                        <td>创建人</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="ty-color-redOrange gapT">您所选产品如下：</div>
            <div class="elemFlex">
                <span>产品基本信息</span>
                <div class="gapM">
                    <span class="grayLink">基本信息修改记录</span>
                    <span class="grayLink" onclick="pdComposition(1)">产品构成</span>
                    <span class="grayLink">产品构成修改记录</span>
                    <span class="grayLink">停止生产的操作记录</span>
                </div>
            </div>
            <table class="ty-table ty-table-control" id="relatedProduct">
                <tbody>
                <tr>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>来源</td>
                    <td>构成</td>
                    <td>产品单重</td>
                    <td>重量单位</td>
                    <td>创建人</td>
                </tr>
                </tbody>
            </table>
            <div class="elemFlex">
                <div>
                    与产品有关联关系的商品共<span class="relatedNumSee"></span>种
                    <span class="linkBtn gapL relateGoods" data-fun="relateGoodsScan">查看</span>
                </div>
                <span class="grayLink">本产品的关联记录</span>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce_Fixed.cancel();">返回重选</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="commodyRelatedSure();">确定关联</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--与产品有关联关系的商品--%>
    <div class="bonceContainer bounce-blue" id="productRelatedCm" style="width:1000px;">
        <div class="bonceHead">
            <span>查看与商品关联的产品</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="elemFlex">
                <span>产品:</span>
            </div>
            <table class="ty-table ty-table-control" id="productSee">
                <tbody>
                <tr>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>来源</td>
                    <td>构成</td>
                    <td>产品单重</td>
                    <td>重量单位</td>
                    <td>创建人</td>
                </tr></tbody>
            </table>
            <div class="gapT">本产品已与如下<span id="relatedNum"></span>种商品关联：</div>
            <table class="ty-table ty-table-control" id="relatedRecord">
                <tbody>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>所属客户</td>
                    <td>创建人</td>
                    <td>关联操作人</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed2.cancel();">关  闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>关联管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="wrapBody">
                    <div class="mainCon mainCon1">
                        <ul class="ty-secondTab">
                            <li class="ty-active">有待关联的商品</li>
                            <li>已关联的商品</li>
                        </ul>
                        <%--待入库--%>
                        <div class="ty-mainData">
                            <div id="waitRelativeCommody">
                                <p>商品需与产品关联后，方能更充分使用系统的功能，如销售所下订单方能与生产等部门的数据相连通。</p>
                                <p class="ty-color-redOrange">如下<span id="waitNum"></span>种商品有待与产品关联。</p>
                                <table class="ty-table ty-table-none bg-yellow" id="waitRelativeList">
                                    <thead>
                                    <tr>
                                        <td>商品代号</td>
                                        <td>商品名称</td>
                                        <td>型号</td>
                                        <td>规格</td>
                                        <td>计量单位</td>
                                        <td>所属客户</td>
                                        <td>创建人</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <div id="relativedCommody">
                                <div class="elemFlex">
                                    <p class="ty-color-redOrange">如下<span id="relatedReady"></span>种商品已与产品关联完毕。</p>
                                    <span class="ty-btn ty-btn-blue ty-btn-big" onclick="getListByProduct(1, 1)">按产品展示关联情况</span>
                                    <div class="searchSect">
                                        <div class="ty-left keywordSearch">
                                            查找产品
                                            <input placeholder="请输入产品图号或名称" id="searchReadyKey" />
                                        </div>
                                        <span class="ty-left ty-btn ty-btn-blue" onclick="searchReadyCommody()">确定</span>
                                    </div>
                                </div>
                                <table class="ty-table ty-table-none bg-yellow" id="relativedList">
                                    <thead>
                                    <tr>
                                        <td>商品代号</td>
                                        <td>商品名称</td>
                                        <td>型号</td>
                                        <td>规格</td>
                                        <td>计量单位</td>
                                        <td>所属客户</td>
                                        <td>创建人</td>
                                        <td>关联操作人</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                        <div id="ye_accept"></div>
                    </div>
                    <div class="mainCon mainCon2">
                        <div class="lineQuery backBtn">
                            <div class="ty-left">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(1)">返回</span>
                            </div>
                            <div class="queryItem">
                                <span class="ssTtl">数据筛选</span>
                                <select id="bySource" class="kj-select kj-select-blue" onchange="getListByProduct(3, 1)">
                                    <option value=""> 全部来源 </option>
                                    <option value="2">加工方为本公司</option>
                                    <option value="1">委托外部加工</option>
                                    <option value="3">本公司自制，但有时外包</option>
                                </select>
                            </div>
                            <div class="queryItem">
                                <select id="byCompose" class="kj-select kj-select-blue" onchange="getListByProduct(4, 1)">
                                    <option value=""> 全部构成 </option>
                                    <option value="2">也是装配件</option>
                                    <option value="3">材料需由多种原料先混合</option>
                                    <option value="4">由所购买的单一材料直接加工而成</option>
                                </select>
                            </div>
                            <div class="ty-right searchSect">
                                <div class="ty-left keywordSearch">
                                    数据查找
                                    <input placeholder="请输入产品图号或名称中的关键字" id="searchKeyBase" />
                                </div>
                                <span class="ty-left ty-btn ty-btn-blue" onclick="getListByProduct(2, 1)">确定</span>
                            </div>
                        </div>
                        <p>以下为各产品所关联商品的情况</p>
                        <table class="ty-table ty-table-none bg-yellow" id="productList">
                            <thead>
                            <td width="20%">产品图号</td>
                            <td width="30%">产品名称/型号/规格</td>
                            <td width="50%">所关联的商品</td>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_2"></div>
                    </div>
                    <div class="mainCon mainCon3">
                        <div class="backBtn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(2)">返回</span>
                        </div>
                        <p>符合条件的数据共以下<span id="countNum"></span>条</p>
                        <table class="ty-table ty-table-none bg-yellow" id="searchProductList">
                            <thead>
                            <td width="20%">产品图号</td>
                            <td width="30%">产品名称/型号/规格</td>
                            <td width="50%">所关联的商品</td>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_3"></div>
                    </div>
                    <div class="mainCon mainCon4">
                        <div class="backBtn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 relationSee funBtn" data-fun="backPrePage">返回</span>
                        </div>
                        <p>产品基本信息</p>
                        <table class="ty-table ty-table-control" id="byProductSee">
                            <tbody>
                            <tr>
                                <td>产品图号</td>
                                <td>产品名称/型号/规格</td>
                                <td>计量单位</td>
                                <td>来源</td>
                                <td>构成</td>
                                <td>产品单重</td>
                                <td>重量单位</td>
                                <td>创建人</td>
                            </tr></tbody>
                        </table>
                        <p class="gapTp">本产品已与如下<span id="relatedCountNum"></span>种商品关联</p>
                        <table class="ty-table ty-table-control" id="byProductRelated">
                            <tbody>
                            <tr>
                                <td>商品代号</td>
                                <td>商品名称/型号/规格</td>
                                <td>计量单位</td>
                                <td>所属客户</td>
                                <td>创建人</td>
                                <td>关联操作人</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/technology/related.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>

</body>
</html>
