<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/commodity/goods.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/commodity/composition.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
 <%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<<div class="compotionBounce" id="compotionBounce">
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="rupdateGoods()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="rupdateGoods()">确定</span>
        </div>
    </div>

    <%-- 构成 编辑/查看 弹框--%>
    <%--<div class="goods_update bonceContainer bounce-green" style="width:800px;">--%>
    <%--</div>--%>

    <div class="bonceContainer bounce-red" id="mtConfirm">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_confirm_ms"> </p>
                    <div class="hd">
                        <p id="mt_confirm_type" ></p>
                        <p id="mt_confirm_id"  ></p>

                        <p id="mt_confirm_source" ></p>
                        <p id="mt_confirm_compotion" ></p>
                        <p id="mt_confirm_stuff" ></p>
                        <p id="mt_confirm_memo" ></p>
                        <p id="mt_confirm_cateroy" ></p>
                        <p id="_goodsID" ></p>
                        <p id="_goodsType" ></p>
                        <p id="_parentCompotion" ></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-5" onclick="okConfirm()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelConfirm()">取消</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="updateMaterial" onclick="stopIntervalMt()">
        <div class="bonceHead">
            <span id="typettl"></span><span id="pVal"></span>
            <a class="bounce_close" onclick="rupdateGoods()"></a>
        </div>
        <div class="bonceCon ">
            <div class="bonceCon-center"  >
                <input type="hidden" id="upMt_type">
                <input type="hidden" id="upMt_mtID">
                <input type="hidden" id="upMt_compotionID">
                <input type="hidden" id="upMt_goodsID">
                <div class="chargeSomething">
                    <span class="bounce_ttl">代号</span>
                    <div class="bounce_con">
                        <input type="text" id="upMt_no"  onclick="searchCode($(this) , event )" >
                        <div class="mt_conainer" id="mt_conainer" onfocus=" stopIntervalMt(); $('#upMt_name').focus();" ></div>
                    </div>
                </div>
                <div><span class="bounce_ttl">物料名称</span><input type="text" id="upMt_name" onfocus="stopIntervalMt()"></div>
                <div><span class="bounce_ttl">规格</span><input type="text" id="upMt_gui" onfocus="stopIntervalMt()"></div>
                <div><span class="bounce_ttl">型号</span><input type="text" id="upMt_size" onfocus="stopIntervalMt()"></div>
                <div><span class="bounce_ttl">配比比例</span><input type="text" id="upMt_bi" onkeyup="clearNum(this)" onfocus="stopIntervalMt()">%</div>
                <div><span class="bounce_ttl">物料类型</span><input type="text" id="upMt_kind" disabled="disabled" value="原辅材料"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="saveUpMtBtn()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelUpMtBtn()">取消</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="updateGoods" onclick="stopIntervalGs()">
        <div class="bonceHead">
            <span id="typettl_gs"></span><span id="pVal_g"></span>
            <a class="bounce_close" onclick="rupdateGoods()"></a>
        </div>
        <div class="bonceCon">
            <div class="bonceCon-center" >
                <input type="hidden" id="upGs_id" />
                <input type="hidden" id="upGs_goodsType" />
                <input type="hidden" id="upGs_type" />
                <input type="hidden" id="upGs_compotionID" />
                <input type="hidden" id="upGs_BigGsId" />
                <div class="chargeSomething">
                    <span class="bounce_ttl add_code">代号</span>
                    <div class="bounce_con">
                        <input type="text" id="upGs_no"  onclick="searchBoth($(this) , event )" onfocus="searchBoth($(this) , event )"  >
                        <div class="mt_conainerGoods" id="gs_conainer" >
                        </div>
                    </div>
                </div>
                <div><span class="bounce_ttl">名称</span><input type="text" id="upGs_name"  ></div>
                <div><span class="bounce_ttl">规格</span><input type="text" id="upGs_gui" ></div>
                <div><span class="bounce_ttl">型号</span><input type="text" id="upGs_size"></div>
                <div><span class="bounce_ttl">单位</span><input type="text" id="upGs_unit"></div>
                <div><span class="bounce_ttl">装配数量</span><input type="text" id="upGs_num" onkeyup="clearNum(this)" ></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="saveUpGsBtn()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelUpGsBtn()">取消</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>商品构成</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="pull-right">
                    <div class="ty-search">
                        <span class="ty-search-ttl"></span>产品图号：<input id="searchInnerSn" type="text" class="ty-searchInput"/>
                        <input type="button" value=""  class="ty-searchBtn" onclick="searchBtn($(this))"/>
                    </div>
                </div>
                <div>
                    <%--构成主页面--%>
                    <div class="goods_composition Border-big">
                        <div class="opinionCon">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="5%">序号</td>
                                <td width="10%">产品图号</td>
                                <td width="12%">产品名称</td>
                                <td width="6%">单位</td>
                                <td width="8%">来源</td>
                                <td width="8%">构成</td>
                                <td width="8%">产品净重</td>
                                <td width="17%">材料</td>
                                <td width="10%">备注</td>
                                <td width="16%">构成操作</td>
                                </thead>
                                <tbody id="compotionList"></tbody>
                            </table>
                            <div id="compositionYe"></div>
                        </div>
                        <div class="clr"></div>
                    </div>

                    <%-- 构成编辑/查看 --%>
                    <%--<div class="goods_update"  ></div>--%>


                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/commodity/composition.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>