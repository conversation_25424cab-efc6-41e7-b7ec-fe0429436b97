<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/technology/packManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%--包装设置--%>
    <div class="bonceContainer bounce-blue" id="commodityPacking" style="width:1060px;">
        <div class="bonceHead">
            <span class="bounce_title">包装设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="wrapLab">
                <div class="sectCon1">
                    <div class="modeSelect">
                        <span class="spTtl">本商品是否需要包装？<span class="red">*</span></span>
                        <span class="modelPattern"><span class="fa fa-circle-o" data-icon="1"></span>需要</span>
                        <span class="modelPattern"><span class="fa fa-circle-o" data-icon="2"></span>不需要</span>
                    </div>
                    <div class="hd" id="commodityInfo"></div>
                </div>
                <div class="sectCon sectCon2 hrLine">
                    <div class="clear">
                        <span>请从最小包装开始录入，录入完成后，如必要可“增加一层包装”，直至最外层的包装。</span>
                        <span class="linkBtn ty-right" data-fun="addMoreLayer">增加一层包装</span>
                    </div>
                </div>
                <div class="sectCon sectCon3">
                    <table class="ty-table ty-table-none" id="packageAll">
                        <tbody>
                        <tr class="packageItem" data-lev="1">
                            <td><span class="spTtl">本层包装的主要包装物<span class="red">*</span></span></td>
                            <td class="majorName">尚未选择</td>
                            <td colspan="2">
                                <span class="linkBtn ty-right majorPackBtn" data-fun="selectPackage" data-source="1" data-type="add">选择主要包装物</span>
                                <div class="hd majorPack"></div>
                            </td>
                        </tr>
                        <tr>
                            <td><span class="spTtl">本层包装的辅助包装物</span></td>
                            <td>
                                <span class="subName">尚未选择</span>
                                <span class="linkBtn ty-right" data-fun="scanSubPackList">查看/管理</span>
                                <div class="hd subPack"></div>
                            </td>
                            <td colspan="2">
                                <span class="linkBtn ty-right" data-fun="selectPackage" data-source="2">增加辅助包装物</span>
                            </td>
                        </tr>
                        <tr>
                            <td><span class="spTtl">单个包装内商品的数量<span class="red">*</span></span></td>
                            <td>
                                <input type="text" name="productCount" placeholder="请录入数字" oninput="clearNoNumN(this, 2)" onkeyup="productNumCount(this)" require/>
                                <span class="matUnit"></span>
                            </td>
                            <td>本层包装内商品的净重</td>
                            <td>
                                <input class="modNetWeight" type="text" disabled />
                                <span class="modWeightUnit"></span>
                            </td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>
                                <input class="modNum" type="hidden" disabled/>
                            </td>
                            <td>本层包装的毛重（含包装）</td>
                            <td>
                                <input class="modGrossWeight" type="text" disabled/>
                                <span class="modWeightUnit"></span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="commodityPackingOk()">确定</span>
        </div>
    </div>
        <%--商品最外层包装的信息--%>
        <div class="bonceContainer bounce-blue" id="outerBase" style="width: 680px;">
            <div class="bonceHead bounce-blue">
                <span>商品最外层包装的信息</span>
                <a class="bounce_close" onclick="bounce.show($('#commodityPacking'))"></a>
            </div>
            <div class="bonceCon">
                <div class="fmWrapper">
                    <div>请录入本商品最外层包装的外廓信息</div>
                    <div class="bTip">注：各尺寸的单位均默认为m，如不适用，请更换！</div>
                    <ul class="outer_form">
                        <li>
                            <div>摆放于货架之后长度方向的尺寸
                                <span class="ty-color-gray ty-right">查看图例</span>
                            </div>
                            <input class="ty-inputText" value="" placeholder="请录入数字" name="outerLength" require />
                            <select name="lengthUnitId" require>
                                <option value="3">m</option>
                                <option value="2">cm</option>
                                <option value="1">mm</option>
                            </select>
                        </li>
                        <li>
                            <div>
                                摆放于货架之后宽度方向的尺寸
                                <span class="ty-color-gray ty-right">查看图例</span>
                            </div>
                            <input class="ty-inputText" value="" placeholder="请录入数字" name="outerWidth" require/>
                            <select name="widthUnitId" require>
                                <option value="3">m</option>
                                <option value="2">cm</option>
                                <option value="1">mm</option>
                            </select>
                        </li>
                        <li>
                            <div>
                                摆放于货架之后高度方向的尺寸
                                <span class="ty-color-gray ty-right">查看图例</span>
                            </div>
                            <input class="ty-inputText" value="" placeholder="请录入数字" name="outerHeight" require />
                            <select name="heightUnitId" require>
                                <option value="3">m</option>
                                <option value="2">cm</option>
                                <option value="1">mm</option>
                            </select>
                        </li>
                    </ul>
                    <div class="shapeCon">
                        <p>本商品最外层包装是什么形状？请选择：</p>
                        <div class="outerShape">
                            <div><input type="radio" name="outerShape" value="1" />长方体</div>
                            <div><input type="radio" name="outerShape" value="2" />圆柱体</div>
                            <div><input type="radio" name="outerShape" value="3" />其他形状</div>
                        </div>
                    </div>
                    <div class="effectTimeCon">
                        <p>本此次修改从何时开始生效？</p>
                        <input class="form-control" value="" placeholder="请选择日期" name="effectTime" id="outerEffect" />
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.show($('#commodityPacking'))">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="outerBaseOk()">确定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-blue" id="withoutPackageTip" style="width: 580px;">
            <div class="bonceHead bounce-blue">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="msgCon ty-center">确定后，在用的包装方式均将停用！</div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="withoutPackageOk()">确定</span>
            </div>
        </div>
        <%--查看本层包装的包装物--%>
        <div class="bonceContainer bounce-blue" id="scanItemPack" style="width: 1000px;">
            <div class="bonceHead bounce-blue">
                <span>查看本层包装的包装物</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <p>本层包装为<span id="packName"></span>，包装内商品净重<span id="packItemWeight"></span>，包装后的毛重<span id="packItemGrossWeight">XX重量单位</span>。</p>
                <p>本层包装的单个包装内装有<span id="packItemCount">商品XX商品的计量单位</span>。</p>
                <hr/>
                <table class="ty-table ty-table-control" id="scanItemPackList">
                    <tbody>
                    <tr>
                        <td width="15%">包装物类别</td>
                        <td width="30%">包装物信息</td>
                        <td width="15%">使用时的理论用量</td>
                        <td width="20%">本层所用本包装物总重</td>
                        <td width="20%">本包装物的出材量</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关  闭</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed">
    <%--选择/更换本层包装的主要包装物--%>
    <div class="bonceContainer bounce-blue" id="selectPackage" style="width: 980px;">
        <div class="bonceHead bounce-blue">
            <span>选择/更换本层包装的主要包装物</span>
            <a class="bounce_close" onclick="backCancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modInput">
                <table class="pannelTab" id="packageSelect">
                    <tbody>
                    <tr>
                        <td>
                            <div>
                                包装物代号<i class="red">*</i>
                                <span class="linkBtn ty-right" data-fun="addPackage">增加包装物</span>
                            </div>
                            <select name="material" id="packageName" onchange="changePackageName($(this))">
                                <option value="">请选择</option>
                            </select>
                        </td>
                        <td>
                            <div>
                                包装物名称<i class="red">*</i>
                            </div>
                            <input class="ty-inputText" value="" name="name" disabled require/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>规格</div>
                            <input class="ty-inputText" value="" name="specifications" disabled/>
                        </td>
                        <td>
                            <div>型号</div>
                            <input class="ty-inputText" value="" name="model" disabled/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>计量单位1（进货时）</div>
                            <input class="ty-inputText" value="" name="unit" disabled/>
                        </td>
                        <td>
                            <div>备注</div>
                            <input class="ty-inputText" value="" name="memo" disabled/>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="line tipCon">
                    <div class="ty-left">
                        <div>以下各数据均特指进行本层包装且使用本包装物时的情景，请逐项填写。如遇难理解之处，可参看“操作指南”。</div>
                        <div class="ty-color-blue">注 以下有些数据可用于计算包装物的利用率，有些数据用于系统测算出发货时的货物总重，故各非必填项也建议填写！</div>
                    </div>
                    <span class="ty-right linkBtn" onclick="bounce_Fixed2.show($('#manualBook'))">操作指南</span>
                </div>
                 <table class="pannelTab" id="otherBase">
                    <tr>
                        <td>
                            <div>
                                计量单位2<i class="red">*</i>（使用时)
                                <span class="linkBtn ty-right" data-fun="addUnit" data-targ="slct_unit">新增</span>
                            </div>
                            <select class="ty-inputSelect" name="usageUnitId" id="slct_unit" onchange="changeUnitName($(this))">
                                <option value="">请选择</option>
                            </select>
                        </td>
                        <td>
                            <div class="areaCon">
                                <div>理论用量<i class="red">*</i></div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="ratedAmout" oninput="clearNoNumN(this,10)" onchange="weightCount($(this), 1)"/>
                                <span class="usageUnitName"></span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>单重1</div>
                            <input class="weightText" value="" placeholder="请录入，如需要，可更换重量单位" name="stockWeight" oninput="clearNoNumN(this,10)" onchange="weightCount($(this), 2)"/>
                            <select class="weightSelect" type="text" id="weightUnit1" name="stockWeightUnitId" require onchange='stockWeightUnitChange($("#weight2")[0], 1)'>
                                <option value="1">毫克（mg）</option>
                                <option value="2">克（g）</option>
                                <option value="3">千克（kg）</option>
                                <option value="4">吨（T）</option>
                            </select>
                        </td>
                        <td>
                            <div class="areaCon">
                                <div>单重1合计</div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="weightReference" disabled id="weightCount"/>
                                <span class="weightReferenceUnit unitVal"></span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>单重2</div>
                            <input class="weightText" type="text" name="usageWeight" id="weight2" placeholder="请录入，如需要，可更换重量单位" oninput="volumCount(this, 1)">
                            <select class="weightSelect" id="weightUnit2" type="text" name="usageWeightUnitId" onchange="volumCount(this,2)">
                                <option value="1">毫克（mg）</option>
                                <option value="2">克（g）</option>
                                <option value="3">千克（kg）</option>
                                <option value="4">吨（T）</option>
                            </select>
                        </td>
                        <td>
                            <div>出材量</div>
                            <input class="ty-inputText" value="20" placeholder="请录入" id="volumCon" name="volum" disabled/>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="backCancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="selectPackageSure()">确定</span>
        </div>
    </div>
        <%--查看/管理本层包装的辅助包装物--%>
        <div class="bonceContainer bounce-blue" id="scanSubPack" style="width: 1000px;">
            <div class="bonceHead bounce-blue">
                <span>查看/管理本层包装的辅助包装物</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="clear">
                    本层包装的辅助包装物已有如下<span class="subPackLen"></span>种
                    <span class="linkBtn ty-right" data-fun="selectPackage" data-source="3" data-type="add">增加辅助包装物</span>
                </div>
                <table class="ty-table ty-table-control" id="scanSubPackList">
                    <tbody>
                    <tr>
                        <td width="30%">包装物</td>
                        <td width="15%">使用时的理论用量</td>
                        <td width="20%">本层所用本包装物总重</td>
                        <td width="15%">出材量</td>
                        <td width="20%">操作</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关  闭</span>
            </div>
        </div>
        <%--删除本层提示--%>
        <div class="bonceContainer bounce-blue" id="delThisLayer" style="width: 580px;">
            <div class="bonceHead bounce-blue">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="ty-center">
                    <p>删除本层包装后，商品的最外层包装需重新编辑！</p>
                    <p>确定删除本层包装吗？</p>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="delThisLayerOk()">确定</span>
            </div>
        </div>
        <%--删除提示--%>
        <div class="bonceContainer bounce-blue" id="delTip" style="width: 580px;">
            <div class="bonceHead bounce-blue">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce_Fixed.show($('#scanSubPack'))"></a>
            </div>
            <div class="bonceCon">
                <div class="ty-center">
                    <p>确定删除本材料吗？</p>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.show($('#scanSubPack'))">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="delScanPackage()">确定</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
    <%--增加包装物--%>
    <div class="bonceContainer bounce-blue" id="addPackage" style="width: 680px;">
        <div class="bonceHead bounce-blue">
            <span>增加包装物</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="fmWrapper">
                <p>所增加的包装物将进入材料—“商品的包装物”清单</p>
                <ul class="pack_form">
                    <li>
                        <div>
                            包装物代号<i class="red">*</i>
                        </div>
                        <input class="ty-inputText" value="" placeholder="请录入" name="code" require/>
                        <i class="fa fa-times clearInputVal"></i>
                    </li>
                    <li>
                        <div>
                            包装物名称<i class="red">*</i>
                        </div>
                        <input class="ty-inputText" value="" placeholder="请录入" name="name" require/>
                        <i class="fa fa-times clearInputVal"></i>
                    </li>
                    <li>
                        <div>
                            规格
                        </div>
                        <input class="ty-inputText" value="" placeholder="请录入" name="specifications" />
                        <i class="fa fa-times clearInputVal"></i>
                    </li>
                    <li>
                        <div>
                            型号
                        </div>
                        <input class="ty-inputText" value="" placeholder="请录入" name="model" />
                        <i class="fa fa-times clearInputVal"></i>
                    </li>
                    <li>
                        <div>
                            进货时的计量单位<i class="red">*</i>
                            <span class="linkBtn ty-right" data-fun="addUnit" data-targ="buyingUnit">新增</span>
                        </div>
                        <select class="ty-inputSelect" name="unitId" id="buyingUnit" require>
                            <option value="">请选择</option>
                        </select>
                    </li>
                    <li>
                        <div>
                            备注（录入内容可包含但不限于包装物的材质）
                        </div>
                        <input class="ty-inputText" value="" placeholder="如需要，请录入" name="memo" />
                        <i class="fa fa-times clearInputVal"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addPackageOk()">确定</span>
        </div>
    </div>
    <%--操作指南--%>
    <div class="bonceContainer bounce-blue" id="manualBook" style="width: 800px;">
        <div class="bonceHead bounce-blue">
            <span>操作指南</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="manualEct">
                <p>1 名词解释</p>
                <p>
                    <span>1.1 计量单位2</span>
                    系指使用本包装物进行本层包装时，本包装物的计量单位。
                </p>
                <p>
                    <span>1.2 理论用量</span>
                    系指使用本包装物进行本层包装时，本包装物的理论用量。
                </p>
                <p>
                    <span>1.3 单重1</span>
                    系指每计量单位2本包装物的重量。
                </p>
                <p>
                    <span>1.4 单重1合计</span>
                    系指本层包装所使用本包装物的总重量，算法为理论用量乘以单重1。
                </p>
                <p>
                    <span>1.5 单重2</span>
                    系指每计量单位1本包装物的重量。
                </p>
                <p>
                    <span>1.6 出材量</span>
                    系指每计量单位1的本包装物能出来多少计量单位2的本包装物。
                </p>
                <p>2 页面上的操作</p>
                <p>2.1 总皮重与出材量系由系统算出，无需录入，其他各项则需录入。</p>
                <p>2.2 单重1不是必填项，但如不录入，系统无法算出“单重1合计”与“出材量”的值。</p>
                <p>2.3 单重2不是必填项，但如不录入，系统无法算出“出材量”的值。</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关  闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(2)">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>包装管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainData">
                    <%--material massage--%>
                    <input type="hidden" id="showMainConNum" />
                    <div class="mainCon mainCon1">
                        <div class="unSection">
                            <div class="ty-left">待设置包装信息的商品</div>
                            <span>共<span id="wszNum">0</span>种</span>
                            <span class="linkBtn" data-fun="getUnpackList">去设置</span>
                        </div>
                        <div class="lineQuery clear">
                            <div class="ty-left">以下为包装信息已设置的商品</div>
                            <div class="ty-right searchSect">
                                <div class="ty-left keywordSearch">
                                    <span class="searchIcon"></span>
                                    <input placeholder="请输入要查找商品的代号或名称" id="searchKeyBase1" />
                                </div>
                                <span class="ty-left ty-btn ty-btn-blue searchBtn" onclick="getPackList()">确 定</span>
                            </div>
                        </div>
                        <div>
                            <table class="ty-table ty-table-control bg-yellow" id="packList">
                                <tbody>
                                <tr>
                                    <td width="15%">商品图号</td>
                                    <td width="15%">商品名称/规格/型号</td>
                                    <td width="8%">计量单位</td>
                                    <td width="8%" style="max-width: 139px;">所属客户</td>
                                    <td width="15%">包装概述</td>
                                    <td width="10%">操作</td>
                                </tr>
                                </tbody>
                            </table>
                            <div id="ye_temp"></div>
                        </div>
                    </div>
                    <div class="mainCon mainCon2">
                        <div class="backPage">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(1);">返回首页</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(1);">返回上一页</span>
                        </div>
                        <div class="lineQuery clear">
                            <div class="ty-left">以下为包装信息有待设置的商品</div>
                            <div class="ty-right searchSect">
                                <div class="ty-left keywordSearch">
                                    <span class="searchIcon"></span>
                                    <input placeholder="请输入要查找商品的代号或名称" id="searchKeyBase2" />
                                </div>
                                <span class="ty-left ty-btn ty-btn-blue" onclick="getUnpackList()">确定</span>
                            </div>
                        </div>
                        <table class="ty-table ty-table-control bg-yellow" id="commodityListN">
                            <tbody>
                            <tr>
                                <td width="14%">商品图号</td>
                                <td width="18%">商品名称/规格/型号</td>
                                <td width="8%">计量单位</td>
                                <td width="8%">所属客户</td>
                                <td width="18">创建人</td>
                                <td width="10%">操作</td>
                            </tr>
                            </tbody>
                        </table>
                        <div id="ye2"></div>
                    </div>
                    <div class="mainCon mainCon3">
                        <div class="backPage clear">
                            <input type="hidden" id="backPreNum" />
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(1);">返回首页</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(9);">返回上一页</span>
                            <div class="ty-right tjAll">
                                <i class="fa fa-circle-o faBtn" data-fun="toogeCircle" id="sflag"></i>
                                本商品的包装已设置完毕！
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapLt" onclick="packingFinishSure()">确  定</span>
                            </div>
                        </div>
                        <div class="topName sc_commodityInfo">XXXXXXXX/XXXXX/XXXXXX/XXXXXXXXXXXXXXXXX/XX</div>
                        <div class="topName clear">
                            <span class="sc_mode1">本商品已创建了<span class="packageKinds">0</span>种包装方式。</span>
                            <span class="sc_mode2">本商品包装方式中，在用的有<span class="packageKinds">0</span>种，已停用的有<span class="susKinds">0</span>种。</span>
                            <div class="ty-right">
                                <div class="hd" id="susPacking"></div>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 susPackingList" onclick="susPackingList($(this))">已停用的数据</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updatePackingTip($(this))" data-source="1">改为无需包装</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addPacking()">新增包装方式</span>
                            </div>
                        </div>
                        <div class="mainOnlyRow" data-id="">
                            <div class="linkWrap">
                                以下为<span class="createInfo"></span>创建的包装方式。
                                <span class="linkBtn gap" data-fun="updatePackage">编辑</span>
                                <span class="redLinkBtn susPackage" data-fun="updatePackingTip" data-type="0" data-source="3">停用</span>
                                <span class="redLinkBtn delPackage" data-fun="updatePackingTip" data-source="2">删除</span>
                                <span class="linkBtn updateRecord gapLt" data-fun="getUpdateRecord">操作记录</span>
                                <span class="ty-right linkBtn" data-fun="scanMorePacking" id="scanMore">查看更多的包装方式</span>
                                <span class="hd packItemInfo"></span>
                            </div>
                            <div class="pckRow packageOuter">本包装方式最外层主要使用XXXX包装，长宽高：XXX*XXXX*XXXX，形状：XXX</div>
                            <div class="pckRow packageOuterOther">本包装方式共X层，装有XXX.XX计量单位本商品，商品净重为XXXX.XX重量单位，含包装的毛重为XXXX.XX重量单位</div>
                            <div id="pckLevels"></div>
                        </div>
                        <div class="hd" id="detailCon3"></div>
                    </div>
                    <div class="mainCon mainCon4">
                        <div class="backPage">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(1);">返回首页</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(3);">返回上一页</span>
                        </div>
                        <div class="topName sc_commodityInfo">XXXXXXXX/XXXXX/XXXXXX/XXXXXXXXXXXXXXXXX/XX</div>
                        <div class="topName clear">
                            <span class="sc_mode1">本商品已创建了<span class="packageKinds">0</span>种包装方式，您可对各种包装方式进行编辑或删除。</span>
                            <span class="sc_mode2">本商品包装方式中，<span class="usingCon">在用的有<span class="packageKinds">0</span>种，</span>已停用的有<span class="susKinds">0</span>种。</span>
                        </div>
                        <div class="mainRows" id="scanMoreCon">
                            <div class="packageScanItem">
                                <div class="linkWrap">
                                    以下为<span class="createInfo"></span>创建的包装方式。
                                    <span class="linkBtn gap" data-fun="updatePackage">编辑</span>
                                    <span class="redLinkBtn susPackage" data-fun="updatePackingTip" data-type="0" data-source="3">停用</span>
                                    <span class="redLinkBtn delPackage" data-fun="updatePackingTip" data-source="2">删除</span>
                                    <span class="linkBtn updateRecord gapLt" data-fun="getUpdateRecord">操作记录</span>
                                    <span class="hd packItemInfo"></span>
                                </div>
                                <div class="pckRow">本包装方式最外层主要使用XXXX包装，长宽高：XXX*XXXX*XXXX，形状：XXX</div>
                                <div class="pckRow">本包装方式共X层，装有XXX.XX计量单位本商品，商品净重为XXXX.XX重量单位，含包装的毛重为XXXX.XX重量单位</div>
                                <div class="pckRow">
                                    <span class="gapTtl">最小包装</span>
                                    主要使用XXX包装，辅助包装物共XX种
                                    <span class="ty-right linkBtn">查看</span>
                                </div>
                                <div class="pckRow">
                                    <span class="gapTtl">最小包装</span>
                                    主要使用XXX包装，辅助包装物共XX种
                                    <span class="ty-right linkBtn">查看</span>
                                </div>
                                <div class="pckRow">
                                    <span class="gapTtl">最小包装</span>
                                    主要使用XXX包装，辅助包装物共XX种
                                    <span class="ty-right linkBtn">查看</span>
                                </div>
                                <div class="pckRow">
                                    <span class="gapTtl">最小包装</span>
                                    主要使用XXX包装，辅助包装物共XX种
                                    <span class="ty-right linkBtn">查看</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon mainCon5">
                        <div class="backPage">
                            <input type="hidden" id="backNum" />
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(1);">返回首页</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(8);">返回上一页</span>
                        </div>
                        <div class="limitWid"><p>包装方式的操作记录</p></div>
                        <div class="limitWid">
                            <span>商品：</span><span class="sc_commodityInfo"></span>
                        </div>
                        <div class="limitWid">
                            <span>客户：</span><span class="sc_commodityCus"></span>
                        </div>
                        <div>
                            <table class="ty-table ty-table-control" id="recordList">
                                <tbody>
                                <tr>
                                <td>属性</td>
                                <td>操作者及时间</td>
                                <td>生效日期</td>
                                <td>详情查看</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mainCon mainCon6">
                        <div class="backPage">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(1);">返回首页</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="backPre(5);">返回上一页</span>
                        </div>
                        <div class="mainRows recordScan">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/technology/packManage.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

