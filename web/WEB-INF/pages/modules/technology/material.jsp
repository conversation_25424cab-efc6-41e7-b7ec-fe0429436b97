<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/technology/material.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%-- 材料录入 --%>
    <div class="bonceContainer bounce-green" id="mtEntry" onclick="stopMtch(event)">
        <div class="bonceHead">
            <span>材料录入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <div class="case1">
                    <div>您正在为“<span class="red">XXXX20CrMnTi</span>”选择或录入<span class="changeMtTtl">更换后的</span>材料。</div>
                    <div class="tip">注：录入材料的名称或代号后，系统内如有相关材料将弹出。您可在其中选择，也可自行继续录入新材料！</div>
                </div>
                <div class="case2" style="margin:30px 50px 0;">
                    <div>请确认是否需要库管员填写本材料的当前库存数量。
                        <input need type="hidden" name="isCurrent">
                        <span class="isCurrent">
                            <i class="fa fa-circle-o" data-num="1"></i>需要
                            <i class="fa fa-circle-o" data-num="0"></i>不需要
                        </span>
                    </div>
                    <div class="tip">注：曾购买过的材料可能有库存，请选择“需要”！</div>
                </div>
                <div class="case3" style="margin:0 50px;">
                    <p>本材料用于产品</p>
                    <div class="orangeTip border">
                        修改说明：此处的修改仅能对以后所发的采购订单生效，对已存在于系统中的采购订单无法生效！
                    </div>
                </div>
            </div>
            <div>
                <div class="item" style="margin-top:20px; ">
                    <div style="position: relative;" class="col-md-4"><span class="item-title">材料名称<span class="red">*</span></span>
                        <input need name="name" type="text" require/>
                        <input need name="id" type="hidden" />
                        <input need name="product_" type="hidden" />
                        <div id="selectMt"></div>
                    </div>
                    <div class="col-md-4"><span class="item-title">材料代号<span class="red">*</span></span><input need name="code" type="text" require/></div>
                    <div class="col-md-4">
                        <span class="item-title">计量单位<span class="red">*</span></span>
                        <select style="width:150px; " need type="text" id="unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                        <input need type="hidden" name="unit" require id="add_unitName">
                        <span data-type="addUnit" class="btnCat" id="addUnitBtn"> 新增</span>
                    </div>
                </div>
                <div class="item">
                    <div class="col-md-4"><span class="item-title">规格</span><input need name="specifications" type="text" /></div>
                    <div class="col-md-4"><span class="item-title">型号</span><input need name="model" type="text" /></div>
                </div>
                <div class="item">
                    <div class="col-md-12"><span class="item-title">备注</span><input need name="memo" type="text" style="width:760px;" onkeyup="countWords($(this), 100)"/><span class="textMax">0/100</span></div>
                </div>
            </div>

            <div class="clr"></div>
            <div>
                <div class="case1"></div><div class="case2"></div>
                <div class="case3" style="margin:20px 50px;">
                    <p>系统中包含本材料的未完结采购订单共 <span class="orderNum"></span>个。</p>
                    <div>采购人员需要终止未完结的采购订单吗？
                        <input need type="hidden" name="terminateOrders">
                        <span class="terminateOrders">
                            <i class="fa fa-circle-o" data-num="1"></i>需要
                            <i class="fa fa-circle-o" data-num="0"></i>不需要
                        </span>
                    </div>
                    <div class="orangeTip" style="margin-top:20px; ">注：选择“需要”时，系统将向采购人员发送系统消息，但您仍需自行确认采购人员是否采取了实际行动。</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="mtEditOk() ">确定</span>
        </div>
    </div>
    <%-- 材料查看 --%>
    <div class="bonceContainer bounce-blue" id="mtScan">
        <div class="bonceHead">
            <span>材料查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <span style="color:#0da3e2 " id="stopTime"></span>
            </div>
            <div class="item row">
                <div class="col-md-6">
                    <span class="scanMt_state">本材料用于产品。</span>
                </div>
                <div class="col-md-3"><span class="btnCat" id="editMtBtn" >修改材料的基本信息</span></div>
                <div class="col-md-3" style="　text-align: right"><span class="disabled btnCat">材料基本信息的修改记录</span></div>
            </div>
            <div class="item row">
                <div class="col-md-3">&nbsp;</div>
                <div class="col-md-4"><span class="item-title">创建人</span><span class="scanMt_create">张三丰给 2015-15-15 14：12：12</span></div>
                <div class="col-md-4"><span class="disabled btnCat" id="startEndLog" style="　text-align: right">材料停用/恢复使用的操作记录</span></div>
            </div>
            <div class="item row" style="margin-top: 20px;">
                <div class="col-md-4"><span class="item-title">材料名称</span><span class="scanMt_name"></span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span class="scanMt_code"></span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span class="scanMt_unit"></span></div>
            </div>
            <div class="item row">
                <div class="col-md-4"><span class="item-title">规格</span><span class="scanMt_specifications"></span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span class="scanMt_model"></span></div>
            </div>
            <div class="item row">
                <div class="col-md-12"><span class="item-title">备注</span><span class="scanMt_memo"></span></div>
            </div>
            <div class="item row catCon">
                <div class="col-md-12"><span class="item-title">材料类别</span><span class="scanMt_cat"></span></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--  产品查看  --%>
    <div class="bonceContainer bounce-blue" id="scanGS">
        <div class="bonceHead">
            <span>产品查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>基本信息</p>
                <table class="ty-table">
                    <tbody>
                    <tr>
                        <td>产品图号</td>
                        <td>产品名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>所处阶段</td>
                        <td>加工部门</td>
                    </tr>
                    <tr>
                        <td class="innerSn">产品图号</td>
                        <td class="name">产品名称</td>
                        <td class="specifications">规格</td>
                        <td class="model">型号</td>
                        <td class="unit">计量单位</td>
                        <td class="phrase">所处阶段</td>
                        <td class="processDeptName">加工部门</td>
                    </tr>
                    <tr>
                        <td>产品说明</td>
                        <td colspan="6" class="memo" style="text-align:left ;"></td>
                    </tr>
                    </tbody>
                </table>
                <p></p><p>创建人：<span class="create">张三 2020-02-02 12：12：12</span></p>
            </div>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%-- 选择完确定的提示 --%>
    <div class="bonceContainer bounce-blue " id="selectTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon centerCon">
            <p>确认所选无误吗？</p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="selectMt()">确定</span>
        </div>
    </div>
    <%-- 停用提示 --%>
    <div class="bonceContainer bounce-red " id="stopTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon centerCon">
            <div class="msg"></div>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="mtStopOk()">确定</span>
        </div>
    </div>
    <%-- 删除提示 --%>
    <div class="bonceContainer bounce-red " id="delTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon centerCon">
            <p>确定删除该材料？</p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="mtDelOk()">确定</span>
        </div>
    </div>
    <%-- 我知道了 系列提示 --%>
    <div class="bonceContainer bounce-red " id="iknow">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon centerCon">
            <p class="msg"></p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>

</div>
<div class="bounce_Fixed">
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(8)">确定</span>
        </div>
    </div>

    <%-- 材料新增的提示 --%>
    <div class="bonceContainer bounce-red" id="mtAddTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="" style="margin:30px 50px 0;">
                <div>
                    <p>系统中还没有该材料</p>
                    <p>请确认是否需要库管员填写本材料的当前库存数量。</p>
                    <div class="tip">注：曾购买过的材料可能有库存，请选择“需要”！</div>
                    <span class="isCurrent">
                        <i class="fa fa-circle-o" data-num="1"></i>需要
                        <i class="fa fa-circle-o" data-num="0"></i>不需要
                    </span>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="changeMtTipCancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="mtEditOk('changeOk')">确定</span>
        </div>
    </div>
    <%-- 更换材料提示 --%>
    <div class="bonceContainer bounce-blue " id="changeMtTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon centerCon">
            <p>确定更换该产品或零件的材料吗？</p>
            <p></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-cancel ty-circle-3" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="mtEditOk('changeOk')">确定</span>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>材料</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container">
                <input type="hidden" name="mainConShow">
                <%-- 主页面 --%>
                <div class="mainCon1">
                    <div>
                        <div class="flexCon">
                            <div>
                                <span>现有<span class="pdNum">X</span>种“由所购买的单一材料直接加工而成”的产品或零件有待选择材料</span> <span data-type="goSelect" class="btnCat">去处理</span>
                            </div>
                             <div>
                                 <span>已停用的材料有<span class="stopNum"></span>种</span><span data-type="goStopMt" class="btnCat"> 去查看</span>
                             </div>
                        </div>
                        <div class="flexCon">
                            <div>
                                <span>如下<span class="totalRows"></span>种材料与产品或零件有关</span> <span data-type="addMtBtn" class="btnCat" style="margin-left:254px; ">新增材料</span>
                            </div>
                            <div>
                                <div class="search">
                                    <span>查找</span>
                                    <input type="text" placeholder="请输入材料的代号或名称">
                                    <span data-type="searchBtn" class="btnCat"><i></i></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <table style="margin-top:10px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>使用该材料的产品及零件</td>
                            <td>操作</td>
                        </tr>

                        </tbody>
                    </table>
                    <div id="yeCon1"></div>
                </div>
                    <%--去处理--%>
                <div class="mainCon2">
                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回材料首页</span>
                    <p style="margin-top:20px; ">如下<span class="totalRows"></span>种“由所购买的单一材料直接加工而成”的产品或零件有待选择材料</p>
                    <p>从材料角度选产品或零件更为便捷，强烈推荐！ <span data-type="chooseByMt" class="btnCat">去试试</span></p>
                    <table class="ty-table ty-table-control" style="margin-top:20px; width: 75%;">
                        <tbody>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>
                                <span data-type="gsScan" class="btn ty-color-blue">查看</span>
                                <span data-type="gsSelect" class="btn ty-color-blue">去处理</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon2"></div>
                </div>
                    <%--去试试--%>
                <div class="mainCon3">
                    <p style="margin-top:30px; ">
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回材料首页</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="gopre2">返回上一页</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 ty-right" id="goNext" data-type="goNext" data-selectMt="">下一步</span>
                    </p>

                    <p>请先选择材料。选择后将跳转至选择产品或零件的页面。</p>
                    <table style="margin-top:20px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td></td>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>操作</td>
                        </tr>

                        </tbody>
                    </table>
                    <div id="yeCon3"></div>
                </div>
                    <%--去试试> 下一步--%>
                <div class="mainCon4">
                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回材料首页</span>
                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goPre3">返回上一页</span>
                    <span style="margin-top:50px; " class="ty-right">
                        <span class="select0 " ></span>
                        <span class="select1 btnCat" data-type="selectMtOkBtn">挑选完毕，确定</span>
                    </span>
                    <ul class="ty-secondTab">
                        <li class="ty-active">待选择(<span>X</span>)</li>
                        <li>已选中(<span>X</span>)</li>
                    </ul>
                    <div>
                        <p style="margin-top:20px; ">您所选中的材料如下  </p>
                        <table class="ty-table ty-table-control"  >
                            <tbody>
                            <tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>创建人</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>创建人</td>
                                <td>
                                    <span data-type="mtScan" class="btn ty-color-blue ">查看</span>
                                    <span class="hd"></span>
                                </td>
                            </tr>
                            </tbody>
                        </table>

                        <div style="margin-top:50px; ">
                            <p class="select0">请选择使用该材料加工的的产品或零件</p>
                            <p class="select1">已选中的产品或零件如下。您可放弃已选中的数据。被放弃的数据将回到“待选择”，在本页则不再显示。</p>
                        </div>
                        <table class="ty-table ty-table-control">
                            <tbody>
                            <tr>
                                <td></td>
                                <td>图号</td>
                                <td>名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>创建人</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td><i class="fa fa-circle-o"></i></td>
                                <td>图号</td>
                                <td>名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>创建人</td>
                                <td>
                                    <span class="btn ty-color-blue" data-type="gsScan">查看</span>
                                    <span class="hd"></span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div id="yeCon4"></div>
                    </div>
                </div>
                <%-- 停用材料列表 --%>
                <div class="mainCon5">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回</span>
                    </div>
                    <p style="margin-top:30px; " >已停用的材料有如下 <span class="totalRows"></span> 种</p>
                    <table style="margin-top:15px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>停用时间</td>
                            <td>曾使用过该材料的产品及零件</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>停用时间</td>
                            <td class="ty-td-control"><span data-type="mtUesdGs" class="btn ty-color-blue ">X种</span></td>
                            <td>
                                <span data-type="mtScan" class="btn ty-color-blue ">查看</span>
                                <span data-type="mtStart" class="btn ty-color-blue ">恢复使用</span>
                                <span data-type="mtDel" class="btn ty-color-red ">删除</span>
                                <span class="hd">{}</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon5"></div>
                </div>
                <%-- 使用该材料的产品和零件 --%>
                <div class="mainCon6">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回材料首页</span>
                    </div>

                    <table style="margin-top:30px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                        </tr>
                        </tbody>
                    </table>
                    <p style="margin:20px auto 10px; ">
                        <span>使用该材料的产品及零件有如下 <span class="totalRows"></span> 种</span>
                        <span class="ty-right">曾使用过该材料的产品及零件（<span class="usedNum"></span>种） <span data-type="mtUesdGs" class="btnCat">去看看</span></span>
                    </p>
                    <table class="ty-table ty-table-control" >
                        <tbody>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>
                                <span data-type="changeMt" class="btn ty-color-blue">更换材料</span>
                                <span data-type="changeMtLog" class="btn ty-color-blue">材料更换记录</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon6"></div>
                </div>
                <%-- 材料更换记录 --%>
                <div class="mainCon7">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回材料首页</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="goPre69" data-type="goPre69">返回上一页</span>
                    </div>

                    <table style="margin-top:30px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                            <td><span data-type="gsScan" class="ty-color-blue">查看</span></td>
                        </tr>
                        </tbody>
                    </table>
                    <p style="margin:20px auto 10px; ">
                        <span>该产品或零件材料的更换记录如下</span>
                    </p>
                    <table class="ty-table ty-table-control" >
                        <tbody>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>操作属性</td>
                        </tr>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>操作属性</td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon7"></div>
                </div>
                <%-- 曾使用过该材料的产品和零件 --%>
                <div class="mainCon8">
                    <div>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" data-type="goMain">返回材料首页</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="goback2" data-type="goback2" data-num="0">返回上一页</span>
                    </div>

                    <table style="margin-top:30px; " class="ty-table ty-table-control"  >
                        <tbody>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                        </tr>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建人</td>
                        </tr>
                        </tbody>
                    </table>
                    <p style="margin:20px auto 10px; ">
                        <span>曾使用过该材料的产品及零件有如下 <span class="totalRows"></span> 种</span>
                    </p>
                    <table class="ty-table ty-table-control" >
                        <tbody>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>图号</td>
                            <td>名称</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>操作人</td>
                            <td>
                                <span data-type="changeMtLog" class="btn ty-color-blue">材料更换记录</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="yeCon8"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/technology/material.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
