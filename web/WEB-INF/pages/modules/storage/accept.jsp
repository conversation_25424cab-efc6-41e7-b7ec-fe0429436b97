<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
 <link href="../css/production/storage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .bonceCon input,.bonceCon select,.bonceCon textarea {
        border:1px solid #dee8f0;
        background-color: #fff;
        line-height: 36px;
        text-align: left;
        padding: 0 8px;
        color: #3f3f3f;
        width: 180px;
    }
    .bonceCon input:disabled{
        background-color: #dff0ff;
    }
    .hoverDetail{
        cursor: default;
    }
    .hoverDetailCon{
        position: absolute;
        top: 0;
        right: 50%;
        width: 300px;
        min-height: 120px;
        background-color: #fff;
        padding: 10px 20px 20px 20px;
        border:1px solid #ccc;
        box-shadow: 0 0 5px #ccc;
        display: none;
        text-align: left;
        z-index: 999;
    }
    .hoverDetail:hover .hoverDetailCon{
        display: block;
    }
    .applyAll{
        width: 100%;
        height: 35px;
        line-height: 35px;
        background-color: #f3f3f3;
        padding: 0 15px;
        color: #666;;
        margin-top: 8px;
        text-align: right;
    }
</style>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
<div class="bounce_Fixed">
    <%--签收查看-详情查看--%>
    <div class="bonceContainer bounce-blue" id="signDetailCheck" style="min-width:630px;">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td style="width: 40%">收货方实际的收货数量：</td>
                    <td><input type="text" class="actualDeliveryNum" style="width: 100%" disabled></td>
                </tr>
                <tr>
                    <td>情况记录</td>
                    <td><textarea cols="30" rows="5" class="memo" style="width: 100%" disabled></textarea></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel();">确定</span>
        </div>
    </div>
    <%--录入初始库存数量/库位--%>
    <div class="bonceContainer bounce-blue" id="enterInitialStock" style="min-width:900px;">
        <div class="bonceHead">
            <span>录入初始库存数量/库位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none bg-yellow" id="initialStockList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>创建人</td>
                    <td>计量单位</td>
                    <td>初始库存</td>
                    <td>占用库位</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <p class="gap" style="text-indent: 2em;">您需先选择库位，之后录入该库位上所放置该材料的数量。</p>
            <div class="selectSect">
                <div class="clear checkCondition">
                    <div class="ty-left">
                        <span class="fa fa-square-o" onclick="turnCheck($(this))"></span>
                        直接录入初始库存数量，暂不选择库位
                    </div>
                    <div class="ty-right checkCtrl storesAble">
                        <span class="addOneStore" onclick="newStore()">增加新库位</span>
                    </div>
                </div>
                <div class="notSelectStores">
                    <p>请录入该材料的初始库存数量
                        <input class="onlyNumber" type="text" onkeyup="limitSize(this)" /><span class="goodsUnit"></span>
                    </p>
                </div>
                <ul class="storesFill clear storesAble">
                    <li>
                        <div>
                            <span class="gapRt">库位</span>
                            <select onchange="fillStore($(this))" id="addInitStore">
                            </select>
                            <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                        </div>
                        <div>
                            <span class="gapRt">数量</span>
                            <input type="text" onkeyup="limitSize(this)"/><span class="goodsUnit"></span>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addInitialStock()">确定</span>
        </div>
    </div>
    <%--重新选择库位--%>
    <div class="bonceContainer bounce-blue" id="reselectLocation" style="min-width:900px;">
        <div class="bonceHead">
            <span id="reselectTtl">重新选择库位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none bg-yellow" id="reselectList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>创建人</td>
                    <td>计量单位</td>
                    <td>当前库存</td>
                    <td>占用库位</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div id="resetCurrentStation" class="resetCurrentStation"></div>
            <p class="gap noStation" style="text-indent: 2em;">您需先选择库位，之后录入该库位上所放置该材料的数量。</p>
            <p class="gap hasStation" style="text-indent: 2em;">您重新选择库位后，还需录入该库位上所放置该材料的数量。</p>
            <div class="selectSect">
                <div class="clear checkCondition">
                    <div class="ty-left hasStation">
                        <span class="fa fa-square-o" onclick="turnCheck($(this))"></span>
                        放弃本材料所占库位，且本材料暂不再选择库位
                    </div>
                    <div class="ty-right checkCtrl storesAble">
                        <span class="addOneStore" onclick="newStore($(this))">增加新库位</span>
                    </div>
                </div>
                <div class="notSelectStores">
                    <p>请录入该材料的初始库存数量
                        <input class="onlyNumber" type="text" onkeyup="limitSize(this)"/>
                        <span class="reUnit"></span>
                    </p>
                </div>
                <ul class="storesFill clear storesAble">
                    <li>
                        <div>
                            <span class="gapRt">库位</span>
                            <select onchange="fillStore($(this))" id="resetInitStore">
                            </select>
                            <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                        </div>
                        <div>
                            <span class="gapRt">数量</span>
                            <input type="text" onkeyup="limitSize(this)"/>
                            <span class="reUnit"></span>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="updateLocation(2)">确定</span>
        </div>
    </div>
    <%-- 修改初始库存--%>
    <div class="bonceContainer bounce-blue" id="updateStores" style="min-width:400px;">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>请输入新的初始库存</p>
            <div>
                <input id="newStore" type="text" onkeyup="limitSize(this)" />
                <i></i>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateStoresSure()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--操作--%>
    <div class="bonceContainer bounce-blue" id="storageAcceptHandle" style="width: 450px">
        <div class="bonceHead">
            <span>操作</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="radioBox" id="operationRadio">
                <div class="ty-radio numOk" value="0">
                    <i class="fa fa-circle-o"></i>
                    <span>数量无误，入库</span>
                </div>
                <div class="ty-radio numNo" value="1">
                    <i class="fa fa-circle-o"></i>
                    <span>数量不对，修改数量</span>
                </div>
                <input type="text" name="" class="judgmentQuantity" style="display: none">
                <div id="productNum" style="display: none">
                    <span>请录入您清点的商品数量：</span>
                    <input type="text" class="productNum" style="width: 200px;" onkeyup="limitSize(this)">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureStorageAcceptHandle()" id="storageAcceptHandleBtn" disabled>提交</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="outStorageApply" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库申请</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageApplyBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="4" class="deliveryDate"></td>
                    <td>订单号</td>
                    <td class="sn">1</td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList" id="goodList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>计划出库数量 <span class="ty-color-red">*</span></td>
                    <td>货物件数</td>
                    <td>操作</td>
                </tr>
                <tbody></tbody>
            </table>
            <div class="applyAll"></div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--出库申请-待出库-查看(出库申请单)--%>
    <div class="bonceContainer bounce-blue" id="outStorageOrder" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库申请单</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageOrderBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="4" class="deliveryDate"></td>
                    <td>订单号</td>
                    <td class="sn"></td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>计划出库数量</td>
                    <td>货物件数</td>
                </tr>
                <tbody></tbody>
            </table>
            <div class="applyAll">申请人：王建 2017-20-23</div>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--签收查看--%>
    <div class="bonceContainer bounce-blue" id="signCheck" style="min-width:1400px;">
        <div class="bonceHead">
            <span>成品出库查看</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-none" id="signCheckBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td width="18%">订单号</td>
                    <td width="18%" class="sn">1</td>
                    <td width="10%">原始录入时间</td>
                    <td width="18%" colspan="2" class="create_date"></td>
                    <td width="10%">订单修改时间</td>
                    <td width="18%" class="update_date"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="2" class="deliveryDate"></td>
                    <td colspan="2">搬运负责人</td>
                    <td colspan="2" class="carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="2" class="arriveDate"></td>
                    <td colspan="2">计划的发货方式</td>
                    <td colspan="2" class="deliveryWay"></td>
                </tr>
                </tbody>
            </table>
            <p class="countAll" style="margin-top: 8px"></p>
            <div id="signInfoRecord">
                <div class="recordTitle">
                    情况记录
                    <div class="recordHeader ty-right">
                        <b>签收人：</b><span class="signer"></span><b>签收时间：</b><span class="signTime"></span><b>录入者：</b><span class="recorder"></span><b>录入时间：</b><span class="recordTime"></span>
                    </div>
                </div>

                <div class="recordCon">
                    <div class="signDetail"></div>
                </div>
            </div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>要求到货日期</td>
                    <td>出库数量</td>
                    <td>货物件数</td>
                    <td>仓库审批时间</td>
                    <td>复核时间</td>
                    <td>实际签收数量</td>
                    <td>录入时间</td>
                    <td>情况记录</td>
                </tr>
                <tbody></tbody>
            </table>
            <p class="applyAll"></p>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--出库申请-已出库-发运信息--%>
    <div class="bonceContainer bounce-blue" id="logisticInfo" style="min-width:1200px">
        <div class="bonceHead">
            <span>发运信息</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="transInfo" style="display: none;text-align: center">未获取到发运信息</div>
            <div class="transDetail">
                <table class="ty-table ty-table-none">
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">确定</span>
        </div>
    </div>
    <%--按类别查看成品 - 初始库存--%>
    <div class="bonceContainer bounce-blue" id="initialStockSee" style="min-width:900px;">
        <div class="bonceHead">
            <span>初始库存</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <span class="noteTtl">当前初始库存</span>
                <span class="noteTtl" id="stockAmount">DWF-1001</span>
                <span class="ty-btn ty-btn-blue ty-btn-big" id="updateStoresBtn" onclick="updateStores()">修改</span>
                <table class="ty-table ty-table-control" id="initialStockRecord">
                    <thead>
                    <tr>
                        <td>修改人</td>
                        <td>修改时间</td>
                        <td>客户名称</td>
                        <td>修改前的初始库存</td>
                        <td>修改后的初始库存</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--占用库位查看--%>
    <div class="bonceContainer bounce-blue" id="holdStockSee" style="width:1000px;">
        <div class="bonceHead">
            <span>占用库位查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-none bg-yellow" id="holdStockInfo">
                    <thead>
                    <tr>
                        <td width="15%">商品代号</td>
                        <td width="15%">商品名称</td>
                        <td width="10%">型号</td>
                        <td width="10%">规格</td>
                        <td width="20%">创建人</td>
                        <td width="10%">计量单位</td>
                        <td width="10%">当前库存</td>
                        <td width="10%">占用库位</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div id="onlyNumInfo">该材料尚未选择库位</div>
                <div id="currentStation" class="resetCurrentStation"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="holdStockSeeReset" onclick="reSelectStock(null);">去选择库位</span>
        </div>
    </div>
    <%--初始库存-修改记录--%>
    <div class="bonceContainer bounce-blue" id="initStockUpdateRecord" style="min-width:900px;">
        <div class="bonceHead">
            <span>初始库存-修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-control" id="initUpdateRecord">
                    <thead>
                    <tr>
                        <td>修改人</td>
                        <td>修改时间</td>
                        <td>客户名称</td>
                        <td>修改前的初始库存</td>
                        <td>修改后的初始库存</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--最低库存-修改记录--%>
    <div class="bonceContainer bounce-blue" id="safeStockUpdateRecord" style="min-width:900px;">
        <div class="bonceHead">
            <span>最低库存-修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-control" id="safeRecord">
                    <thead>
                    <tr>
                        <td>修改人</td>
                        <td>修改时间</td>
                        <td>客户名称</td>
                        <td>修改前的最低库存</td>
                        <td>修改后的最低库存</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">确定</span>
        </div>
    </div>
    <%-- 我知道了  --%>
    <div class="bonceContainer bounce-red" id="knowTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <div id="knowTipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 还有必填项尚未填写！  --%>
    <div class="bonceContainer bounce-red" id="nullTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <div id="nullTipMs">还有必填项尚未填写！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 库位情况查看--%>
    <div class="bonceContainer bounce-blue" id="seeStores" style="min-width:830px;">
        <div class="bonceHead">
            <span>库位情况查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="seeStoresInfo">
                <span>库位</span>
                <span id="seeStoresName"></span>
            </div>
            <table class="ty-table ty-table-none  bg-yellow storeTb storesCreateInfo">
                <thead>
                <tr>
                    <td width="20%">所属库房</td>
                    <td width="20%">所属区域</td>
                    <td width="15%">所属货架</td>
                    <td width="15%">所属层数</td>
                    <td width="10%" class="hdNot"></td>
                    <td width="20%">创建人</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <p>该库位已放置的材料</p>
            <table class="ty-table ty-table-none  bg-yellow storeTb scienceCreateInfo">
                <thead>
                <tr>
                    <td width="20%">商品代号</td>
                    <td width="20%">商品名称</td>
                    <td width="15%">型号</td>
                    <td width="15%">规格</td>
                    <td width="10%">计量单位</td>
                    <td width="20%">数量</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel();">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>成品库</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <button type="button" class="btn btn-default stockJump" onclick="stockJump(0)">返回成品库主页</button>
                <div class="pageStyle container_item">
                    <%--首页--%>
                    <div id="productMain" class="stockHandle" data-part="0">
                        <div class="fragment">
                            <div class="ty-left">
                                <span class="frCon">待录入初始库存数量的成品共<span id="waitingFor"></span>种</span>
                                <span class="ty-btn ty-btn-blue" onclick="stockJump(1)">去处理</span>
                            </div>
                            <div class="ty-right partRt">
                                <span class="ty-btn ty-btn-blue frCheck" onclick="takeInventory()">去盘点</span>
                                <span class="ty-btn ty-btn-blue" onclick="stockJump(2)">重新选择库位</span>
                            </div>
                        </div>
                        <div class="fragment">
                            <div class="ty-left">
                                <span class="frCon">待处理的成品入库申请共<span id="dairuku"></span>笔</span>
                                <span class="ty-btn ty-btn-blue" onclick="stockJump(4)">去处理</span>
                            </div>
                            <div class="ty-right partRt">
                                <span class="frCheck">出入库记录</span>
                                <span class="ty-btn ty-btn-blue" onclick="stockJump(6)">查看</span>
                            </div>
                        </div>
                        <div class="fragment">
                            <div class="ty-left">
                                <span class="frCon">待处理的成品出库申请共<span id="daichuku"></span>笔</span>
                                <span class="ty-btn ty-btn-blue" onclick="stockJump(5)">去处理</span>
                            </div>
                            <div class="ty-right partRt">
                                <span class="frCheck">盘点记录</span>
                                <span class="ty-btn ty-btn-gray">查看</span>
                            </div>
                        </div>
                        <div class="warehouse">
                            <table class="pt-table" id="finishedWarehouse">
                                <tbody>
                                <tr>
                                    <td class="td-orange">
                                        <p>已设置成品库</p>
                                        <p><span houseReq data-name="alreadySet"></span>个</p>
                                    </td>
                                    <td class="td-lightOrange">库内资源</td>
                                    <td>
                                        <p>区域数量</p>
                                        <p>货架数量</p>
                                        <p>库位总数/空库位数</p>
                                    </td>
                                    <td>
                                        <p houseReq data-name="quyu"></p>
                                        <p houseReq data-name="huojia"></p>
                                        <p houseReq data-name="kuwei"></p>
                                    </td>
                                    <td class="td-lightOrange">库内货物概览</td>
                                    <td>
                                        <p>种类总数</p>
                                        <p>净重合计</p>
                                        <p>总重合计</p></td>
                                    <td>
                                        <p houseReq data-name="zl"></p>
                                        <p>未知</p>
                                        <p>未知</p>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="clear">
                            <h5 class="ty-left">您除可点击某成品库以查看成品外，还可按类别查看成品，或直接查找某成品。</h5>
                            <span class="ty-right ty-btn ty-btn-blue" onclick="stockJump(3)">按类别查看成品</span>
                        </div>
                        <div class="clear searchSect">
                            <div class="ty-left keywordSearch">
                                <input placeholder="请输入您要查找成品的图号或名称" id="searchKey" />
                                <i></i>
                            </div>
                            <span class="ty-left ty-btn ty-btn-blue" onclick="stockJump(7)">确定</span>
                        </div>
                        <div class="warehouse">
                            <div class="clear">
                                <table class="pt-table single" id="singleHouse">
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                        <%--录入初始库存数量/库位--%>
                    <div class="stockHandle" data-part="1" style="display: none">
                        <p class="ty-color-orange">如下<span id="waitingTotle"></span>种成品的初始库存数量/库位有待您录入。</p>
                        <table class="ty-table ty-table-none bg-yellow" id="waitingForEntry">
                            <thead>
                            <tr>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建人</td>
                                <td>计量单位</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                        <%-- 重新选择库位--%>
                    <div class="stockHandle" data-part="2" style="display: none">
                        <p class="ty-color-orange">请选择您要重新选择库位的成品。</p>
                        <table class="ty-table ty-table-none bg-yellow" id="resetStock">
                            <thead>
                            <tr>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建人</td>
                                <td>计量单位</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div id="byCategory" data-part="3" class="seemore" style="display: none"><%--按类别查看成品--%>
                        <div id="mtInfo" class="Con">
                            <div class="bigContainer">
                                <div class="left_container">
                                    <div class="Btop"><span><span id="categoryName" data-id="">全部成品</span>（<span id="allCount"></span>种）</span></div>
                                    <div>
                                        <form>
                                            <ul class="faceul Left-label" id="kindsTree"></ul>
                                            <div class="faceul1 suspendBtn">
                                                <a data-stop="1" onclick="showkindBtn($(this))">
                                                    <span>暂停销售的商品</span><span>（<span id="suspendCommodyNum">0</span>种)</span>
                                                    <div class='hd'>
                                                        <span class='kindId' data-type=''></span>
                                                    </div>
                                                </a>
                                            </div>
                                            <div class="left-bottom clear" style="display: none;">
                                                <div class="add-b" onclick="gobackLstLevel()"> <a> <span>返回上一级</span> </a>  </div>
                                                <div class="add-b" onclick="getPdByCategory(0,'','',1,20)" > <a> <span >返回全部</span>  </a> </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div class="between"><div class="between2"></div></div>
                                <div class="right_container">
                                    <div class="Right-label" id="right_container">
                                        <div class="container_nav">
                                            <div class="conon">
                                                <div class="dq">
                                                    <span>当前分类</span>  <span>：</span>  <span id="curID">  </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="opinionCon">
                                            <table class="ty-table ty-table-none bg-yellow" id="goodsByCategory">
                                                <thead>
                                                <td width="10%">商品代号</td>
                                                <td width="8%">商品名称</td>
                                                <td width="8%">型号</td>
                                                <td width="8%">规格</td>
                                                <td width="16%">创建人</td>
                                                <td width="8%">计量单位</td>
                                                <td width="8%">最低库存</td>
                                                <td width="10%">当前库存</td>
                                                <td width="8%">初始库存</td>
                                                <td width="8%">占用库位</td>
                                                <td width="8%">操作</td>
                                                </thead>
                                                <tbody id="byCategoryCon">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="clr"></div>
                            </div>
                        </div>
                    </div>
                    <div data-part="4" style="display: none">
                        <%--成品入库申请--%>
                        <table class="ty-table ty-table-none bg-yellow" id="inStorage">
                            <thead>
                            <tr>
                                <td>序号</td>
                                <td>申请时间</td>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>产品代号</td>
                                <td>产品名称</td>
                                <td>单位</td>
                                <td>数量</td>
                                <td>生产日期</td>
                                <td>产品到期日</td>
                                <td>入库流程</td>
                                <td>审批</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div data-part="5" style="display: none">
                        <%--成品出库申请--%>
                        <table class="ty-table ty-table-none bg-yellow" id="outStorage">
                            <thead>
                            <tr>
                                <td>计划出库日期</td>
                                <td>客户名称</td>
                                <td>收货地址</td>
                                <td>计划的发货方式</td>
                                <td>订单号</td>
                                <td>商品类别总数</td>
                                <td>货物总件数</td>
                                <td>搬运负责人</td>
                                <td>计划到达日期</td>
                                <td>申请提交时间</td>
                                <td>申请最后修改时间</td>
                                <td>提交者</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div data-part="6" style="display: none">
                        <div id="checkInOutInfo">
                            <ul class="ty-secondTab">
                                <li class="ty-active">成品入库</li>
                                <li>成品出库</li>
                            </ul>
                            <%--待入库--%>
                            <div class="ty-mainData">
                                <%--成品入库申请--%>
                                <table class="ty-table ty-table-none bg-yellow" id="inStorageInfo">
                                    <thead>
                                    <tr>
                                        <td>序号</td>
                                        <td>申请时间</td>
                                        <td>商品代号</td>
                                        <td>商品名称</td>
                                        <td>产品代号</td>
                                        <td>产品名称</td>
                                        <td>单位</td>
                                        <td>申请入库数量</td>
                                        <td>实际入库数量</td>
                                        <td>生产日期</td>
                                        <td>产品到期日</td>
                                        <td>入库流程</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <%--成品出库申请--%>
                                <table class="ty-table ty-table-none bg-yellow" id="outStorageInfo" style="display: none">
                                    <thead>
                                    <tr>
                                        <td>出库日期</td>
                                        <td>客户名称</td>
                                        <td>客户代号</td>
                                        <td>订单号</td>
                                        <td>商品类别总数</td>
                                        <td>货物总件数</td>
                                        <td>申请提交时间</td>
                                        <td>仓库审批时间</td>
                                        <td>复核时间</td>
                                        <td>提交者</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="stockScreen seemore" data-part="7" style="display: none"><%--要查找成品的图号或名称--%>
                        <p>符合查找条件的结果如下：</p>
                        <table class="ty-table ty-table-none bg-yellow" id="resultList">
                            <thead>
                            <td width="10%">商品代号</td>
                            <td width="10%">商品名称</td>
                            <td width="8%">型号</td>
                            <td width="8%">规格</td>
                            <td width="16%">创建人</td>
                            <td width="8%">计量单位</td>
                            <td width="8%">最低库存</td>
                            <td width="8%">当前库存</td>
                            <td width="8%">初始库存</td>
                            <td width="8%">占用库位</td>
                            <td width="8%">操作</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div id="ye_accept"></div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/production/accept.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>
</body>
</html>
