<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

<link href="../css/event/icon/iconfont.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<style>
    .page{
        width: 1000px; margin-left: 32px; margin-top: 8px;
        display: none;
    }
    .page:first-child{
        display: block;
    }
    section.description{
        width: 100%;
        padding: 4px 0;
        box-sizing: border-box;
        position: relative;
        font-size: 13px;
        color: #909399;
        overflow: hidden;
        opacity: 1;
        margin-bottom: 4px;
    }
    .inputNum{
        width: 60px;
    }
    .item_left_title{
        display: inline-block;
    }
    .minNum, .maxNum{
        color: #445366;
        font-weight: bold;
        background: #eee;
        padding: 2px 6px;
        margin: 0 4px;
    }
    .ty-page-header{
        display: flex;
        line-height: 29px;
        color: #666;
        padding: 0 20px;
        background: #fcfcfc;
        margin-right: 1px;
        border-bottom: 1px solid #e6e6e6;
    }
    .ty-page-header[disabled]{
        color: #b5b5b5;
    }
    .ty-page-header[disabled] .page-header__left{
        cursor: default;
    }
    .page-header__left{
        display: flex;
        cursor: pointer;
        margin-right: 40px;
        position: relative;
    }
    .page-header__left::after {
        content: "";
        position: absolute;
        width: 1px;
        height: 16px;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        background-color: #dcdfe6;
    }
    .page-header__left .icon-back {
        font-size: 18px;
        margin-right: 6px;
        align-self: center;
        position: relative;
        top: 1px;
    }
    .ty-header{
        display: flex;
    }
    .btn-group{
        display: block;
    }
</style>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed2">
    <%-- 自定义参数 --%>
    <div class="bonceContainer bounce-blue" id="customParameters" style="width: 500px">
        <div class="bonceHead">
            <span class="title">自定义参数</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="description">
                请在空格内录入参数，如需要，还可“增加行”或“删除行”。
            </section>
            <table class="kj-table table_customParam">
                <thead>
                <tr>
                    <td>实际情况</td>
                    <td>本项指标实际得分</td>
                    <td><button class="link-blue" name="addOneRow">增加</button></td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td><div class="item_left_title">低于</div> <input type="text" placeholder="请录入" class="inputNum"> 天</td>
                    <td><input type="text" placeholder="请录入" class="inputNum"></td>
                    <td></td>
                </tr>
                <tr>
                    <td><div class="item_left_title"><span class="minNum"></span> 至</div> <input type="text" placeholder="请录入" class="inputNum"> 天</td>
                    <td><input type="text" placeholder="请录入" class="inputNum"></td>
                    <td><button class="link-red" name="delRow">删除</button></td>
                </tr>
                <tr>
                    <td><div class="item_left_title"><span class="minNum"></span>  至</div> <input type="text" placeholder="请录入" class="inputNum"> 天</td>
                    <td><input type="text" placeholder="请录入" class="inputNum"></td>
                    <td><button class="link-red" name="delRow">删除</button></td>
                </tr>
                <tr>
                    <td><div class="item_left_title">多于</div> <span class="minNum"></span> 天</td>
                    <td>100</td>
                    <td></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="customParameters_step" style="width: 500px">
        <div class="bonceHead">
            <span class="title">自定义参数</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="description" style="text-align: center">
                步长S单位为“天”，可设置为1至5的自然数。如需要，可修改。
            </section>
            <div style="width: 150px; margin: 0 auto">
                步长S <input type="text" class="step" onkeyup="clearNum()" style="width: 60px; margin-left: 8px"> 天
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <%-- 修改价格补偿系数 --%>
    <div class="bonceContainer bounce-blue" id="changePriceCoefficient" style="width: 500px">
        <div class="bonceHead">
            <span class="title">修改价格补偿系数</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="description">
                请在空格内录入价格补偿系数。
            </section>
            <table class="kj-table table_customParam">
                <thead>
                <tr>
                    <td>场景</td>
                    <td>系数</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>开增值税专用发票价格的系数</td>
                    <td>1</td>
                </tr>
                <tr>
                    <td>开增值税普通发票价格的系数</td>
                    <td><input type="text" placeholder="请录入" class="inputFloat"></td>
                </tr>
                <tr>
                    <td>不开发票价格的系数</td>
                    <td><input type="text" placeholder="请录入" class="inputFloat"></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <%-- 修改记录 - 查看 --%>
    <div class="bonceContainer bounce-blue" id="changeRecordSee" style="width: 500px">
        <div class="bonceHead">
            <span class="title">算法管理</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="algorithm1-5 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <div>
                    <div class="section1-5">
                        <section class="description">
                            采用为分段式计值法。
                        </section>
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>实际情况</td>
                                <td>本项指标实际得分</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>挂账天数低于10天</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>挂账天数为11天至30天之间</td>
                                <td>40</td>
                            </tr>
                            <tr>
                                <td>挂账天数为31天至60天之间</td>
                                <td>70</td>
                            </tr>
                            <tr>
                                <td>挂账天数多于60天</td>
                                <td>100</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="stepwise1-5">
                        <section class="description">
                            采用为步进式算法。
                        </section>
                        <section class="description">
                            设系统中某供应商挂账天数的数据为A，步长为S，本项指标实际得分为K，则
                        </section>
                        <div><h4 class="text-center">K=A/S</h4></div>
                        <section class="description">
                            其中：<br>
                            S单位为天，已设置为 <span class="step mark"></span><br>
                            K最大值限制为100。
                        </section>
                    </div>
                </div>
            </div>
            <div class="algorithm2-4 hd"> <%--2-4 2-6 2-8 公用--%>
                <h5><span class="indexName"></span>的算法</h5>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>实际情况</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="algorithm2-9 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <table class="kj-table enumerate">
                    <thead>
                    <tr>
                        <td>实际情况</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="algorithm2-9Price hd">
                <h5>价格补偿系数</h5>
                <table class="kj-table enumerate">
                    <thead>
                    <tr>
                        <td>场景</td>
                        <td>系数当前的值</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="algorithm2-14 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>采购周期的天数</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="algorithm2-15 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>最低采购量的数值<br>(计量单位无法示出)</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue" id="changeRecord" style="width: 800px">
        <div class="bonceHead">
            <span class="title">修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <h5><span class="indexName"></span>算法的修改记录</h5>
            <div class="ty-alert">
                当前数据系第 <span class="ty-color-blue changeNum"></span> 次修改后的结果
            </div>
            <table class="kj-table">
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- 更换算法 --%>
    <div class="bonceContainer bounce-blue" id="changeAlgorithm" style="width: 500px">
        <div class="bonceHead">
            <span class="title">更换算法</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="sectionChange">
                <h5>挂账指标2算法之分段式计值法</h5>
                <section class="description">
                    带有默认值的分段式计值法算法如下：
                </section>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>实际情况</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>挂账天数低于10天</td>
                        <td>10</td>
                    </tr>
                    <tr>
                        <td>挂账天数为11天至30天之间</td>
                        <td>40</td>
                    </tr>
                    <tr>
                        <td>挂账天数为31天至60天之间</td>
                        <td>70</td>
                    </tr>
                    <tr>
                        <td>挂账天数多于60天</td>
                        <td>100</td>
                    </tr>
                    </tbody>
                </table>
                <div class="ty-alert">
                    <div>
                        您可直接采用上述算法。<br>
                        如需要，也可“自定义参数”。
                    </div>
                    <div class="ty-btn-group text-right" style="flex: auto">
                        <button class="link-blue" name="customParamFake">自定义参数</button>
                    </div>
                </div>
            </div>
            <div class="stepwiseChange" style="display: none">
                <h5>挂账指标2算法之步进式算法</h5>
                <section class="description">
                    带有默认值的步进式算法如下：<br>
                    设系统中某供应商挂账天数的数据为A，步长为S，本项指标实际得分为K，则
                </section>
                <div><h4 class="text-center">K=A/S</h4></div>
                <section class="description">
                    其中：<br>
                    S单位为天，系统的默认值为1，可修改至2至5的自然数；<br>
                    K最大值限制为100。
                </section>
                <div class="ty-alert">
                    <div>
                        您可直接采用上述算法。<br>
                        如需要，也可“自定义参数”。
                    </div>
                    <div class="ty-btn-group text-right" style="flex: auto">
                        <button class="link-blue" name="customParamFake">自定义参数</button>
                    </div>
                </div>
            </div>
            <div class="ty-radio">
                <input type="radio" id="changeThis">
                <label for="changeThis"></label>
                挂账指标2更换为本算法
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 算法管理 --%>
    <div class="bonceContainer bounce-blue" id="algorithmManage">
        <div class="bonceHead">
            <span class="title">算法及管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="algorithm1-1 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <div class="ty-alert">某材料本指标的算法按下表。本指标的算法不可修改。</div>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>实际情况</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <section class="description text-right">当前算法的生效时间：<span class="algBeginTime"></span></section>
            </div>
            <div class="algorithm1-5 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <div>
                    <div class="section1-5">
                        <section class="description">
                            本指标现采用如下的分段式计值法。<br>
                            您可“自定义参数”，也可“更换为其他算法”。<br>
                            两种操作均将生成修改记录。"
                        </section>
                        <div class="btn-group text-right">
                            <button class="link-blue" name="customParam">自定义参数</button>
                            <button class="link-blue" name="changeAlgorithm">更换为其他算法</button>
                            <button class="link-blue" name="changeRecord">修改记录</button>
                        </div>
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>实际情况</td>
                                <td>本项指标实际得分</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>挂账天数低于10天</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>挂账天数为11天至30天之间</td>
                                <td>40</td>
                            </tr>
                            <tr>
                                <td>挂账天数为31天至60天之间</td>
                                <td>70</td>
                            </tr>
                            <tr>
                                <td>挂账天数多于60天</td>
                                <td>100</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="stepwise1-5">
                        <section class="description">
                            本指标现采用如下的步进式算法。<br>
                            您可“更换为其他算法”，也可“修改当前算法中的参数”。<br>
                            两种操作均将生成修改记录。"
                        </section>
                        <div class="btn-group text-right">
                            <button class="link-blue" name="customParam">自定义参数</button>
                            <button class="link-blue" name="changeAlgorithm">更换为其他算法</button>
                            <button class="link-blue" name="changeRecord">修改记录</button>
                        </div>
                        <section class="description">
                            设系统中某供应商挂账天数的数据为A，步长为S，本项指标实际得分为K，则
                        </section>
                        <div><h4 class="text-center">K=A/S</h4></div>
                        <section class="description">
                            其中：<br>
                            S单位为天，当前设置为 <span class="step mark"></span>。<br>
                            K最大值限制为100，即达到100后不再随A增加而变化。
                        </section>
                    </div>
                </div>

                <section class="description text-right">当前算法的生效时间：<span class="algBeginTime"></span></section>
            </div>
            <div class="algorithm2-4 hd"> <%--2-4 2-6 2-8 公用--%>
                <h5><span class="indexName"></span>的算法</h5>
                <section class="description">
                    本指标算法如下。<br>
                    您可“自定义参数”，操作后将生成修改记录。
                </section>
                <div class="btn-group text-right">
                    <button class="link-blue" name="customParam">自定义参数</button>
                    <button class="link-blue" name="changeRecord">修改记录</button>
                </div>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>实际情况</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>跌幅不足1</td>
                        <td>5</td>
                    </tr>
                    <tr>
                        <td>跌幅达到1%不足2%</td>
                        <td>10</td>
                    </tr>
                    <tr>
                        <td>跌幅达到2%不足3%</td>
                        <td>20</td>
                    </tr>
                    <tr>
                        <td>跌幅达到3%不足4%</td>
                        <td>40</td>
                    </tr>
                    <tr>
                        <td>跌幅达到4%不足5%</td>
                        <td>70</td>
                    </tr>
                    <tr>
                        <td>跌幅达到5或更多</td>
                        <td>100</td>
                    </tr>
                    </tbody>
                </table>
                <section class="description text-right">当前算法的生效时间：<span class="algBeginTime"></span></section>
            </div>
            <div class="algorithm2-9 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <section class="description">
                    本指标算法如下。<br>
                    您可“自定义参数”，操作后将生成修改记录。
                </section>
                <div class="btn-group text-right">
                    <button class="link-blue" name="customParam">自定义参数</button>
                    <button class="link-blue" name="changeRecord">修改记录</button>
                </div>
                <table class="kj-table enumerate">
                    <thead>
                    <tr>
                        <td>实际情况</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <section class="description text-right">当前算法的生效时间：<span class="algBeginTime"></span></section>
                <section class="description">
                    开票属性相同的材料价格可比较，不同的则不好比较。<br>
                    价格补偿系数，以下简称系数，用以不同开票属性价格比较时，修正价格间差异。<br>
                    下表中为系统当前的值。<br>
                    您可“修改价格补偿系数”，操作后将生成修改记录。<br>
                </section>
                <div class="btn-group text-right">
                    <button class="link-blue" name="changePriceCoefficient">修改价格补偿系数</button>
                    <button class="link-blue" name="priceChangeRecord">修改记录</button>
                </div>
                <table class="kj-table param">
                    <thead>
                    <tr>
                        <td>场景</td>
                        <td>系数当前的值</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>开增值税专用发票价格的系数</td>
                        <td>1</td>
                    </tr>
                    <tr>
                        <td>开增值税普通发票价格的系数</td>
                        <td>1.06</td>
                    </tr>
                    <tr>
                        <td>不开发票价格的系数</td>
                        <td>1.05</td>
                    </tr>
                    </tbody>
                </table>
                <section class="description text-right">当前数据的生效时间：<span class="algBeginTime"></span></section>
            </div>
            <div class="algorithm2-14 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <section class="description">
                    本指标算法如下。<br>
                    您可“自定义参数”，操作后将生成修改记录。
                </section>
                <div class="btn-group text-right">
                    <button class="link-blue" name="customParam">自定义参数</button>
                    <button class="link-blue" name="changeRecord">修改记录</button>
                </div>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>采购周期的天数</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>0天</td>
                        <td>100</td>
                    </tr>
                    <tr>
                        <td>1天-10天</td>
                        <td>70</td>
                    </tr>
                    <tr>
                        <td>11天-30天</td>
                        <td>40</td>
                    </tr><tr>
                        <td>31天-60天</td>
                        <td>10</td>
                    </tr><tr>
                        <td>60天以上</td>
                        <td>0</td>
                    </tr>
                    </tbody>
                </table>
                <section class="description text-right">当前算法的生效时间：<span class="algBeginTime"></span></section>
            </div>
            <div class="algorithm2-15 hd">
                <h5><span class="indexName"></span>的算法</h5>
                <section class="description">
                    本指标算法如下。<br>
                    您可“自定义参数”，操作后将生成修改记录。<br>
                    <small class="ty-color-blue">注：材料情况差异很大，本指标仅能对部分材料起到作用！</small>
                </section>
                <div class="btn-group text-right">
                    <button class="link-blue" name="customParam">自定义参数</button>
                    <button class="link-blue" name="changeRecord">修改记录</button>
                </div>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>最低采购量的数值<br>(计量单位无法示出)</td>
                        <td>本项指标实际得分</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>0</td>
                        <td>100</td>
                    </tr>
                    <tr>
                        <td>1-100</td>
                        <td>70</td>
                    </tr>
                    <tr>
                        <td>101-1000</td>
                        <td>40</td>
                    </tr><tr>
                        <td>1001-5000</td>
                        <td>10</td>
                    </tr><tr>
                        <td>5000以上</td>
                        <td>0</td>
                    </tr>
                    </tbody>
                </table>
                <section class="description text-right">当前算法的生效时间：<span class="algBeginTime"></span></section>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
<%--        <div class="ty-page-header">--%>
<%--            <div class="page-header__left" disabled="disabled" onclick="back()">--%>
<%--                <i class="iconfont icon-houtui icon-back"></i>--%>
<%--                <span>返回</span>--%>
<%--            </div>--%>
<%--            <div class="page-header__content" disabled="disabled" onclick="backToMain()">--%>
<%--                <i class="iconfont icon-zhuye icon-back"></i>--%>
<%--                <span>主页</span>--%>
<%--            </div>--%>
<%--        </div>--%>
        <ul>
            <li class="active">
                <span>MCI</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer">
            <!--放内容的地方-->
            <div class="ty-container" id="home">
                <div class="ty-page-container">
                    <div class="backBtn" style="display: none;margin-left: 32px;">
                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="back()">返回</button>
                    </div>
                    <div class="page page_main" page="main">
                        <div class="ty-alert">
                            <div>
                                材料采购指数（MCI）为系统对某供应商供应某种材料的评估分值，可用以解决材料下采购订单时，哪个供应商排在上方的问题。<br>
                                系统有默认的MCI算法，如觉必要可查看算法管理并重新设置，或调整权重。
                            </div>
                            <div class="btn-group text-right" style="flex: auto">
                                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="weightAdjustmentBtn()">调整权重</button>
                                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="weightAdjustmentRecordBtn()">权重调整记录</button>
                            </div>
                        </div>
                        <table class="kj-table" id="firstIndex">
                            <thead>
                            <tr>
                                <td>指标</td>
                                <td>满分</td>
                                <td>权重</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="page page_2-16" page="2-16">
                        <%--指标2-16专属--%>
                        <div class="ty-alert">
                            <div>
                                供应商常规指标由以下指标组成。系统有默认算法，如觉必要可查看算法管理并重新设置，或调整权重。
                            </div>
                            <div class="btn-group text-right" style="flex: auto">
                                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="weightAdjustmentBtn()">调整权重</button>
                                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="weightAdjustmentRecordBtn()">权重调整记录</button>
                            </div>
                        </div>
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>指标</td>
                                <td>满分</td>
                                <td>权重</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="page page_weightAdjustment" page="weightAdjustment">
                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 ty-right" name="sureAdjustWeight" style="margin-bottom: 8px">确定</button>
                        <table class="kj-table" id="tbl_weight">
                            <thead>
                            <tr>
                                <td>指标的类别</td>
                                <td>原权重</td>
                                <td>新权重</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>材料采购合同指标</td>
                                <td>8%</td>
                                <td>
                                    <input type="text" placeholder="请录入" class="inputNum">%
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="page page_weightAdjustmentRecord" page="weightAdjustmentRecord">
                        <div class="text-center">
                            <h4>权重调整记录</h4>
                        </div>
                        <div class="ty-alert">
                            <div class="tips">
                                当前数据系第 <span class="ty-color-blue changeNum"></span> 次修改后的结果
                            </div>
                        </div>
                        <table class="kj-table">
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="page page_record_weightAdjustmentRecordSee" page="weightAdjustmentRecordSee">
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>指标的类别</td>
                                <td>权重</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>

<%@ include  file="../../common/footerTop.jsp"%>

<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/supplierproduct/MCI.js?v=SVN_REVISION" type="text/javascript"></script>
</div>
</body>
</html>