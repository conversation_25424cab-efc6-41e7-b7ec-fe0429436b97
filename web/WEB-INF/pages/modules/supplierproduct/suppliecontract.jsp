<%--
  Created by IntelliJ IDEA.
  User: sy
  Date: 2022/4/30
  Time: 15:53
  供应商 - 常规信息
  To change this template use File | Settings | File Templates.
--%>
<%--<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/purchase/materialManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/supplierproduct/suppliecontract.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/sale.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/wonderssProduct/sysCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed4">
        <div class="bonceContainer bounce-blue" id="includeGoodContract" style="width: 800px;">
            <div class="bonceHead">
                <span>已包含某材料的销售合同</span>
                <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="row-flex cusInfo">
                </div>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>合同编号</td>
                        <td>所涉材料</td>
                        <td>有效期</td>
                        <td>签署日期</td>
                        <td>本版本合同的创建</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel()">关闭</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed3">
    <%-- 选择联系人 --%>
    <div class="bonceContainer bounce-blue" id="chooseCusContact" style="width: 450px">
        <div class="bonceHead">
            <span>选择联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #f0f8ff;">
            <input type="hidden" id="target">
            <div class="p0">
                <p style='text-align:center;'>暂无客户数据，请通过新增添加</p>
            </div>
            <div class="p1">
                <p>以下为可供选择的客户联系人</p>
                <ul class="cusList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="linkBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5"
                  onclick="bounce_Fixed3.cancel($('#chooseCusContact'))">取消</span>
            <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseCusContactOk();
                bounce_Fixed2.show($('#newMailInfo'))">确定</button>
        </div>
    </div>
        <%-- 修改记录 - 合同查看 --%>
        <div class="bonceContainer bounce-blue" id="cScanHis" style="width: 800px; ">
            <div class="bonceHead">
                <span>查看合同</span>
                <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="bounceMainCon">
                    <table class="kj-table kj-table-control leftTab">
                        <tr><td>合同编号</td><td class="contract_see_sn"></td></tr>
                        <tr><td>签署日期</td><td class="contract_see_signTime"></td></tr>
                        <tr><td>合同的有效期</td><td class="contract_see_validTime"></td></tr>
                        <tr><td>合同的扫描件或照片</td><td class="contract_see_image"></td></tr>
                        <tr><td>合同的可编辑版</td><td class="contract_see_file"></tr>
                        <tr>
                            <td>本合同下的材料（共<span class="contract_see_tyNum"></span>种）</td>
                            <td class="cGoodss">
                                <span class="link-blue node" data-fun="tyNum">查看</span>
                            </td>
                        </tr>
                        <tr><td>备注</td><td class="contract_see_memo"></td></tr>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed3.cancel()">关闭</span>
            </div>
        </div>
        <%-- 向本合同添加材料 从本合同移出材料 本合同下的材料 --%>
        <div class="bonceContainer bounce-blue" id="tipcontractGoods" style="width: 800px; max-height:400px; ">
            <div class="bonceHead">
                <span></span>
                <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="row-flex" id="tips">
                    <div><span class="supplerName"></span>的材料共有以下<span class="tip"></span>种</div>
                    <div class="btn-group countStr">
                        已选 <span class="count">0</span> 种
                    </div>
                </div>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td class="selectTd"></td>
                        <td>材料代号</td>
                        <td>材料名称</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>计量单位</td>
                        <td>已包含的合同</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-circle-3 addOrCancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
                <span class="ty-btn bounce-ok addOrCancel ty-btn-big ty-circle-3" onclick="addOrCancelOk()">确定</span>
                <span class="ty-btn bounce-ok cScanc ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
            </div>
        </div>
        <%-- 暂停履约/终止合同 、 恢复履约/重启合作 提示 --%>
        <div class="bonceContainer bounce-red" id="cEndTip" >
            <div class="bonceHead">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div style="text-align: center;" id="tipsc">

                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-circle-3 ty-btn-big" onclick="bounce_Fixed3.cancel()">取消</span>
                <span class="ty-btn bounce-ok ty-btn-big"  data-name="cEndOk">确定</span>
            </div>
        </div>
        <%-- 恢复履约/重启合作（已过期提示) --%>
        <div class="bonceContainer bounce-red" id="cEndTip2" >
            <div class="bonceHead">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="bounceMainCon">
                    本合同已于 <span class="vaildEnd"></span> 到期。请确定具体要进行哪项操作：
                    <div class="kj-radio-group" style="margin-top: 16px; padding-left: 80px;">
                        <div class="kj-radio">
                            <input type="radio" name="expire" id="radio_c_no" value="1">
                            <label for="radio_c_no">不再执行，转入“已到期的合同”</label>
                        </div>
                        <div class="kj-radio">
                            <input type="radio" name="expire" id="radio_c_next" value="2">
                            <label for="radio_c_next">续约</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-circle-3 ty-btn-big" onclick="bounce_Fixed3.cancel()">取消</span>
                <span class="ty-btn bounce-ok ty-btn-big"  data-name="cEndOk2">确定</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
    <%-- 新增邮寄信息 --%>
    <div class="bonceContainer bounce-green" id="newMailInfo" style="width: 460px;z-index: 1;position: absolute;z-index: 200">
        <div class="bonceHead">
            <span>新增邮寄信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">邮寄地址：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="mailAddress" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">邮寄编码：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="mailNumber" require/></span>
            <p id="mailNumError">请输入正确的邮寄编码！</p>
            </p>
            <p>
                <span class="sale_ttl1">联系人：</span>
                <span class="sale_con1 chooseCusCon" data-target="#mailName">
                    <input type="text" readonly placeholder="请选择" id="mailName" require/>
                    <span class="hd"></span>
                    <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                </span>
                <span class="linkBtn" onclick="addContactInfo(3)">新增</span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" id="before"
                  onclick="getback()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big" id="after"
                  onclick="bounce_Fixed2.cancel($('#newMailInfo'))">取消</span>
            <%-- 新增供应商弹窗中新增邮寄地址--%>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addMail" data-name="addMail">确定</button>
            <%-- 常规修改弹窗中新增邮寄地址--%>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addMailml" data-name="addMailml">确定</button>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addMailmn" onclick="setAddress(2,$(this))" data-name="addMailmla">确定</button>
        </div>
    </div>
    <%-- 修改基本信息 --%>
    <div class="bonceContainer bounce-blue" id="chagebasm" style="display: block;position: fixed;left: 222.5px;
        top: -192px;width: 854px;">
        <div class="bonceHead">
            <span>修改基本信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel($('#chagebasm'))"></a>
        </div>
        <div class="bonceCon">
            <form class="sale_updata" id="splibox">
                <div class="sale_c" style="margin-bottom: 20px;">
                    <div class="sale_ttl1"><span class="ty-color-red">*</span>供应商名称:</div>
                    <div class="sale_con1">
                        <input type="text" id="spconname" class="add_splName" name="fullName"
                               placeholder="请录入" require style="width: 640px;">
                    </div>
                </div>
                <div class="sale_c" style="margin-bottom: 20px;">
                    <div class="sale_ttl1"><span class="ty-color-red">*</span>供应商简称:</div>
                    <div class="sale_con1">
                        <input type="tename" id="spcnlast" class="add_spluname"
                               name="spluName" placeholder="请录入" require style="width: 279px;">
                    </div>
                    <div class="sale_ttl1"><span class="ty-color-red">*</span>供应商代号:</div>
                    <div class="sale_con1"><input type="nobe" id="spfont" class="add_splnumb"
                                                  name="splnmb" placeholder="请录入" require style="width: 251px;"></div>
                </div>
                <div class="sale_c" style="margin-bottom: 20px;">
                    <div class="sale_ttl1">全景照片:</div>
                    <div id="qImages">
                        <div id="panoramaBtn" class="saleUploadBtn ty-left"></div>
                        <div class="sale_ttl1">（共可上传9张）</div>
                        <div class="imgWall"></div>
                    </div>
                </div>
                <p class="leMar" style="margin-bottom: 20px;">
                    是否接受挂账？ <input type="hidden" id="spcont">
                    <span onclick="setHangAccount(1 , $(this))" class="radioCon" type="radio" style="margin-left: 85px;"
                          name="getbont">
                             <i id="upsetHangAccount1" class="fa fa-circle-o"></i>可接受
                        </span>
                    <span onclick="setHangAccount(0 , $(this))" class="radioCon" type="radio" style="margin-left: 33px;"
                          name="getbont">
                             <i id="upsetHangAccount0" class="fa fa-circle-o"></i>不接受
                        </span>
                    <span class="hang_7" style="width: auto;margin-left: 95px;" >
                        <span>请录入已约定的账期</span> <input type="day" id="hangDays" name="initialbont"
                                                      onkeyup="clearNoNum(this)" />天
                        </span>
                </p>
                <p class="leMar hang7_1" id="spcontdent">
                    请选择从何时开始计算账期？  <input type="hidden" id="spsetStartDate">
                    <span onclick="setStartDate(1 , $(this))" class="radioCon" id="spsetStartDate1Con">
                            <i id="upsetStartDate1" class="fa fa-circle-o"></i>自货入库之日起
                        </span>
                    <span onclick="setStartDate(2 , $(this))" class="radioCon" id="spsetStartDate2Con">
                            <i id="upsetStartDate2" class="fa fa-circle-o"></i>自发票入账之日起
                        </span>
                </p>
                <p class="leMar hang16" style="margin-bottom: 20px;">
                    是否需要预付款？ <input type="hidden" id="sppri">
                    <span onclick="setAdvanceFee(1, $(this))" class="radioCon" style="margin-left: 71px;">
                            <i id="upsetAdvanceFee1" class="fa fa-circle-o"></i>需要
                        </span>
                    <span onclick="setAdvanceFee(2, $(this))" class="radioCon" style="margin-left: 47px;">
                            <i id="upsetAdvanceFee2" class="fa fa-circle-o"></i>不需要
                        </span>
                    <span id="spother" onclick="setAdvanceFee(0, $(this))" class="radioCon" style="margin-left: 33px;">
                            <i id="upsetAdvanceFee0" class="fa fa-circle-o"></i>不确定
                        </span>
                </p>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel($('#chagebasm'))">取消</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="spmdsure"
                        onclick="sale_updatesure()" style="margin-right: 48px;">提交</button>
            </span>
        </div>
    </div>
    <%-- 删除、停用、启用提示 --%>
    <div class="bonceContainer bounce-red" id="changepont" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="shu1">
                <p id="turnTipMs" style="text-align: center; padding:10px 0 8px"> </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" id="oprationSure">确定</span>
        </div>
    </div>
    <%-- 已被停用的邮寄信息 --%>
    <div class="bonceContainer bounce-red" id="stopsendms">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <tr>
                    <td>名称</td>
                    <td>操作</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- 已被删除的联系人 --%>
    <div class="bonceContainer bounce-red" id="deletepeo">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <tr>
                    <td>名称</td>
                    <td>操作</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- 基本信息查看 --%>
    <div class="bonceContainer bounce-blue" id="jbxilook" style="min-width: 1200px;position: absolute; z-index: 10">
        <div class="bonceHead">
            <span class="detailTal">基本信息查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 600px;overflow: auto">
            <div class="lina">
                <div class="part">
                    <div class="item">
                        <div class="item">
                            <span class="item_title" style="text-align: left;">供应商名称:</span>
                            <span class="item_content" style="width: 600px" id="jbsun_fullname"></span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item"><span class="item_title" style="text-align: left;">供应商简称:</span>
                            <span class="item_content" id="jbsun_name" style="width: 300px"></span></div>
                        <div class="item" style="margin-left: 437px;">
                            <span class="item_title" style="margin-right: 0;">
                                供应商代号:
                            </span>
                            <span class="item_content" id="jbsun_node" style="width: 200px"></span>
                        </div>
                    </div>
                </div>
                <div class="part">
                    <div class="item">
                        <div class="item_title" style="text-align: left;">全景照片：</div>
                        <div class="item_content" id="jbsun_pitc"></div>
                    </div>
                    <div id="jbsun_zweek">
                        <p>可接受挂账，账期<span>XX</span>天，则货物入库之日起计算，不需要预付款</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5"
                  onclick="bounce_Fixed2.cancel($('#jbxilook'));bounce_Fixed2.show($('#upbookmess'))">关闭</span>
        </div>
    </div>
    <%-- 开票信息查看 --%>
    <div class="bonceContainer bounce-blue" id="kpxilook" style="min-width: 1200px;position: absolute; z-index: 10">
        <div class="bonceHead">
            <span class="detailTal">开票信息查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 600px;overflow: auto">
            <div class="lina">
                <div class="part">
                    <div class="item">
                        <div class="item">
                            <span class="item_title" style="text-align: left;">供应商名称:</span>
                            <span class="item_content" style="width: 600px" id="kpsun_fullname"></span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item"><span class="item_title" style="text-align: left;">供应商简称:</span>
                            <span class="item_content" id="kpsun_name" style="width: 300px"></span></div>
                        <div class="item" style="margin-left: 437px;">
                            <span class="item_title" style="margin-right: 0;">
                                供应商代号:
                            </span>
                            <span class="item_content" id="kpsun_node" style="width: 200px"></span>
                        </div>
                    </div>
                </div>
                <div class="part">
                    <div class="kpsun_creat">
                        <p>该供应商能开税率为<span class="uabe"></span>%的增值税专用发票，可接收承兑汇票</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce_Fixed2.cancel($('#kpxilook'));bounce_Fixed2.show($('#upbookmess'))">关闭</span>
        </div>
    </div>
    <%-- 邮寄地址查看 --%>
    <div class="bonceContainer bounce-blue" id="yjdzlook" style="min-width: 1200px;position: absolute; z-index: 10">
        <div class="bonceHead">
            <span class="detailTal">邮寄信息查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="fpDisUse ty-color-red"></p>
            <p>
                <span class="sale_ttl1">邮寄地址：</span>
                <span class="sale-con" id="fpAddress"></span>
            </p>
            <p>
                <span class="sale_ttl1">发票接收人：</span>
                <span class="sale-con" id="fpName"></span>
            </p>
            <p>
                <span class="sale_ttl1">联系电话：</span>
                <span class="sale-con" id="fpMobile"></span>
            </p>
            <p>
                <span class="sale_ttl1">邮寄编码：</span>
                <span class="sale-con" id="fpNumber"></span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- 联系人信息查看 --%>
    <div class="bonceContainer bounce-blue" id="lixilook" style="min-width: 987px;position: fixed;z-index: 10;
        display: block;left: 526.5px;top: -27.5px;">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">姓名：</span>
                <span class="sale-con" id="record_contactName"></span>
                <span class="sale_ttl1">职位：</span>
                <span class="" id="record_position"></span>
            </p>
            <table class="record_otherContact ty-table">
            </table>
            <span class="sale_ttl1">名片：</span>
            <div id="record_contactsCard">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- 修改记录列表弹窗(基本信息） --%>
    <div class="bonceContainer bounce-blue" id="upbookmess" style="">
        <div class="bonceHead">
            <span class="recordTtl">基本信息修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="createRecord clear">
                <p class="ty-left recordTip">当前资料尚未经修改。</p>
                <p class="ty-right recordEditer"></p>
            </div>
            <table class="ty-table ty-table-control changeRecord">
                <thead>
                <tr>
                    <td>记录</td>
                    <td>操作</td>
                    <td>创建者/修改者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <input type="hidden" id="fore">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="upcane"
                  onclick="bounce_Fixed2.cancel();">关闭</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="pacelook"g
                  onclick="bounce_Fixed2.cancel($('#upbookmess'));">关闭</span>
        </div>
    </div>
    <%-- 暂停采购/恢复采购的操作记录 --%>
    <div class="bonceContainer bounce-blue" id="operation" style="z-index: 100">
        <div class="bonceHead">
            <span>暂停采购/恢复采购的操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control" id="suspendRecord">
                <thead style="background: none">
                <tr>
                    <td>操作性质</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3"
                  onclick="bounce_Fixed2.cancel();">关闭</span>
        </div>
    </div>
    <%-- 新增联系人 --%>
    <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
        <div class="bonceHead">
            <span>新增联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel();"></a>
        </div>
        <div class="bonceCon linkUploadify" style="max-height:500px;overflow-y: auto;">
            <form id="newContectData">
                <p>
                    <span class="sale_ttl1">联系人标签：</span>
                    <span class="sale_gap" id="contactFlag"></span>
                </p>
                <p>
                    <span class="sale_ttl1">姓名：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                </p>
                <p>
                    <span class="sale_ttl1">手机：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactNumber" require/></span>
                    <a class="sale_ttl1 addMore" onclick="addMore($(this))">添加更多联系方式</a>
                    <select style="display: none;width: 167px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                        <option value="0"></option>
                        <option value="1">手机</option>
                        <option value="2">QQ</option>
                        <option value="3">Email</option>
                        <option value="4">微信</option>
                        <option value="5">微博</option>
                        <option value="9">自定义</option>
                    </select>
                </p>
                <ul class="otherContact">
                    <li id="addone" style="margin-left: 56px;width: 287px;display: list-item;">
                        <span class="sale_titl1">手机:</span>
                        <span class="gap" style="margin-left:22px;">
                            <input id="contractphone" type="text" placeholder="请录入" data-type="1" data-name="手机"  data-org="" require/>
                        </span>
                        <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>
                    </li>
                    <li id="addtwo" style="width: 347px;display: list-item;margin-left: -11px;">
                        <span class="sale_ttl1">QQ</span>
                        <span class="gap">
                            <input id="contractqq" type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/>
                        </span>
                        <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>
                    </li>
                    <li id="addthree" style="width: 347px;display: list-item;">
                        <span class="sale_ttl1">Email</span>
                        <span class="gap">
                            <input id="contractemal" type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/>
                        </span>
                        <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>
                    </li>
                    <li id="addfour" style="width: 356px;display: list-item;margin-left: -25px;">
                        <span class="sale_ttl1">微信</span>
                        <span class="gap">
                            <input id="contractweixi" type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/>
                        </span>
                        <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>
                    </li>
                    <li id="addfive" style="width: 347px;display: list-item;">
                        <span class="sale_ttl1">微博</span>
                        <span class="gap">
                            <input id="contractwebo" type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/>
                        </span>
                        <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>
                    </li>
                </ul>
                <div id="contactsCard">
                    <span class="sale_ttl1">名片：</span>
                    <div id="uploadCard" class="cardUploadBtn">
                        <div class="bussnessCard"></div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big qu1"
                  onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big qu2" id="yjtc" onclick="getallbc(1)">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big qu2" id="lxtc" onclick="getallbc(0)">取消</span>
            <%-- 新增供应商弹窗中的新增联系人 --%>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addContact" data-name="addContact">确定</button>
            <%-- 新增联系人确定 （常规修改弹窗）（新增邮寄信息中的联系人增加）--%>
            <button class="ty-btn ty-btn-green ty-btn-big hd" id="addContact2" data-name="addContactm">确定</button>
            <%-- 新增联系人确定（常规修改弹窗）(联系人单独增加） --%>
            <button class="ty-btn ty-btn-green ty-btn-big hd" id="addContact1" data-name="addContactmw">确定</button>
            <%-- 修改联系人（常规修改弹窗）（联系人单独修改）--%>
            <button class="ty-btn ty-btn-green ty-btn-big hd" id="addContact3" onclick="updatelix($(this))">确定</button>
        </div>
    </div>
    <%-- 名片 --%>
    <div class="bonceContainer bounce-blue" id="phunte" >
        <div class="bonceHead">
            <span>名片</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel();"></a>
        </div>
        <div class="bonceCon" id="poncune"></div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel($('#phunte'))">关闭</span>
        </div>
    </div>
        <%-- 合同查看 --%>
        <div class="bonceContainer bounce-blue" id="cScan" style="width: 800px; ">
            <div class="bonceHead">
                <span>查看合同</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="bounceMainCon">
                    <p>本版本合同的创建 <span class="contract_see_create"></span></p>
                    <table class="kj-table kj-table-control leftTab">
                        <tr><td>合同编号</td><td class="contract_see_sn"></td></tr>
                        <tr><td>签署日期</td><td class="contract_see_signTime"></td></tr>
                        <tr><td>合同的有效期</td><td class="contract_see_validTime"></td></tr>
                        <tr><td>合同的扫描件或照片</td><td class="contract_see_image"></td></tr>
                        <tr><td>合同的可编辑版</td><td class="contract_see_file"></tr>
                        <tr>
                            <td>本合同下的材料（共<span class="contract_see_tyNum"></span>种）</td>
                            <td class="cGoodss">
                                <span class="link-blue node" data-fun="tyNum">查看</span>
                            </td>
                        </tr>
                        <tr><td>备注</td><td class="contract_see_memo"></td></tr>
                        <tr>
                            <td>本版本合同的修改记录</td>
                            <td class="cEditLog">
                                <span class="link-blue node" data-fun="cEditLog">查看</span>
                            </td>
                        </tr>
                        <tr>
                            <td>本合同的续约记录</td>
                            <td class="cRenewalLog">
                                <span class="link-blue node" data-fun="cRenewalLog">查看</span>
                            </td>
                        </tr>
                    </table>
                    <div class="enabledList"></div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed2.cancel()">关闭</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed">
    <%-- ‘导入失败’提示弹窗 --%>
    <div class="bonceContainer bounce-red" id="Importfailed">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="margin: 20px 36px;">
            <div class="nameing">
                <p class="detel">
                    <span>导入失败！</span>
                </p>
                <p class="detel">
                    <span>原因可能为：</span>
                    <br />
                    <span>1、修改了所下载表格中的“列”；</span>
                    <br />
                    <span>2、选错了文件；</span>
                    <br />
                    <span>3、文件太大，或里面含有图片等。</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel($('#Importfailed'))" style="margin-right: 49px;">我知道了</span>
        </div>
    </div>
    <%-- 材料清单 --%>
    <div class="bonceContainer bounce-blue" id="cmaterials">
        <div class="bonceHead">
            <span class="recordTtl">材料清单</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="createRecord clear" id="contactlist">
                <p class="ty-left recordTip">
                    <span></span>
                    <%--                    <span>XX</span>供应商简称<span>XX</span>供应的材料中，给开增值税专用发票的共<span>XXX</span>种--%>
                </p>
            </div>
            <table class="ty-table ty-table-control material" id="coactbank">
                <thead>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                </tr>
                </thead>
                <tbody id="bothon"></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="cloose($(this))">关闭</span>
        </div>
    </div>
    <%-- 供应商联系人 --%>
    <div class="bonceContainer bounce-blue" id="contacts" style="width:600px;">
        <div class="bonceHead">
            <span>供应商联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactFlex">
                <div style="display: inline-block;margin-left: 29px;">
                    <span>姓名:</span>
                    <span class="sale-con" id="con-contactName"></span>
                </div>
                <div style="display:inline-block;">
                    <span class="sale_ttl1">职位:</span>
                    <span class="sale-con" id="con-contactpost"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签:</span>
                    <span class="sale-con" id="con-contacttag"></span>
                </div>
            </div>
            <table class="see-otherContact ty-table" style="width: 442px;margin: 20px auto;">
                <tbody>
                <tr></tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="look1"
                  onclick="bounce_Fixed.cancel($('#contacts'));">关闭</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addmore"
                  onclick="bounce_Fixed.cancel($('#contacts'));bounce_Fixed3.show($('#chooseCusContact'))">关闭</span>
        </div>
    </div>
    <%-- 温馨提示 --%>
    <div class="bonceContainer bounce-red" id="mtTip" style="width: 400px;">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center;font-size: 14px;"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--新增、修改、续约 合同--%>
    <div class="bonceContainer bounce-green" id="newContractInfo" style="width: 500px">
        <div class="bonceHead">
            <span>新增合同</span>
            <a class="bounce_close" onclick="editContractOk(0)"></a>
        </div>
        <div class="bonceCon linkUploadify">
            <div class="bounceMainCon">
                <p class="cRenewalTip">请录入续约后的合同信息。</p>
                <div class="flex-box cusItem">
                    <div class="citem" style="flex: auto">
                        <p>供应商名称</p>
                        <input type="text" name="customerName" disabled style="width: 100%">
                    </div>
                </div>
                <div class="flex-box">
                    <div class="citem">
                        <p><i class="red">*</i> 合同编号</p>
                        <input type="text" placeholder="请录入" class="cNo">
                    </div>
                    <div class="citem">
                        <p>签署日期</p>
                        <input type="text" placeholder="请选择" readonly class="cSignDate">
                    </div>
                </div>
                <div class="flex-box">
                    <div class="citem">
                        <p><i class="red">*</i> 合同的有效期</p>
                        <input type="text" placeholder="请选择" readonly class="cStartDate">
                    </div>
                    <div class="citem">
                        <p>&nbsp;</p>
                        <span>至</span>
                    </div>
                    <div class="citem">
                        <p>&nbsp;</p>
                        <input type="text" placeholder="请选择" readonly class="cEndDate">
                    </div>
                </div>
                <div class="citem2">
                    <p>合同的扫描件或照片(共可上传9张) <span class="linkBtn ty-right" id="cUpload1"></span></p>
                    <div class="fileCon">
                        <div class="fileCon1"></div>
                        <div class="hd deleteFile"></div>
                        <div style="clear: both"></div>
                    </div>
                </div>
                <div class="citem2">
                    <p>合同的可编辑版 <span class="linkBtn ty-right" id="cUpload2"></span></p>
                    <div class="fileCon ">
                        <div class="fileCon2"></div>
                        <div style="clear: both"></div>
                    </div>
                </div>
                <div class="citem2 cGST">
                    <div class="row-flex">
                        本合同下的材料 <span data-fun="scanTyGs" type="btn" class="link-blue tyGoodNumber">0</span> 种
                        <div class="btn-group">
                            <span class="link-blue" type="btn" data-fun="removeTyGs">移出材料</span>
                            <span class="link-blue" type="btn" data-fun="addTyGs">添加材料</span>
                        </div>
                    </div>
                    <div class="fileCon" style=" background: #f0f0f0;">
                        <div class="tyGoodList" style="font-size: 13px"></div>
                    </div>
                </div>
                <div class="citem2">
                    <p>备注</p>
                    <input class="cMemo" type="text" style="width: 100%" placeholder="请录入">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="editContractOk(0)">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="editContractOk" data-name="editContractOk">提交</button>
        </div>
    </div>
    <%-- 联系人自定义弹窗 --%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" onkeyup="countWords($(this),20)"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel();bounce_Fixed2.show($('#newContectInfo'))">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
    <%-- 删除提示弹窗 --%>
    <div class="bonceContainer bounce-red" id="deletesure" style="width:450px;">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="name">
                <p style="text-align:center;padding:10px 0 8px;">
                    <span>确定删除吗?</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="deletylst()">确定</span>
        </div>
    </div>
    <%-- 确定删除本合同么 --%>
    <div class="bonceContainer bounce-red" id="contractDelTip" style="width:450px;">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;">确定删除本合同么?</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="contractDelTipOk()">确定</span>
        </div>
    </div>
    <%-- 确定暂停该供应商的供应资格吗——点击暂停采购按钮后显示的弹窗--%>
    <div class="bonceContainer bounce-red" id="suspend" style="width:702px;">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p style="text-align: left;padding: 12px 57px;">
                    <span>
                        对某供应商“暂停采购”意味着暂停其供应资格。点击本页面上的“确定”后：
                    </span>
                    <br />
                    <span>
                            1、为材料选择定点供应商时，该供应商不再出现于选项中。
                    </span>
                    <br />
                    <span>
                            2、再下订单时，为要采购的材料选择供应商时，该供应商不再出现于选项中。
                    </span>
                    <br />
                    <br />
                    <span>
                        需要时，可在系统中对其“恢复采购”，但可购买其什么材料，需另行设置。
                    </span>
                    <br />
                    <br />
                    <span>
                        确定暂停该供应商的供应资格吗？
                    </span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="beginstop()">确定</span>
        </div>
    </div>
    <%-- 确定恢复该供应商的供应资格吗？ --%>
    <div class="bonceContainer bounce-red" id="qualification" style="width:702px;">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p style="text-align:left;padding:10px 0 8px;">
                    <span>
                        对某供应商“恢复采购”意味着恢复其供应资格。点击本页面上的“确定”后：
                    </span>
                    <br />
                    <span>
                        为材料选择定点供应商时，该供应商将出现于选项中，但并无可采购的材料。
                    </span>
                    <br />
                    <br />
                    <span>
                        具体可采购该供应商什么材料，请另行设置。
                    </span>
                    <br />
                    <br />
                    <span>
                        确定恢复该供应商的供应资格吗？
                    </span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5"  onclick="becameback($(this))">确定</span>
        </div>
    </div>
        <%--修改记录--%>
        <div class="bonceContainer bounce-blue" id="contractChangeLog" style="width: 700px">
            <div class="bonceHead">
                <span>修改记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="bounceMainCon">
                    <p class="tips">
                        当前数据为本版本合同第 <span class="changeNum"></span> 次修改后的结果。
                        <span class="ty-right">
                        修改时间：<span class="updateTime"></span>
                    </span>
                    </p>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td>记录</td>
                            <td>操作</td>
                            <td>创建者/修改者</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
            </div>
        </div>
        <%--续约记录--%>
        <div class="bonceContainer bounce-blue" id="contractRenewLog" style="width: 700px">
            <div class="bonceHead">
                <span>续约记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>版本</td>
                        <td>操作</td>
                        <td>创建（时间为各版本合同的续约时间）</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
            </div>
        </div>
        <%-- 已到期的合同 --%>
        <div class="bonceContainer bounce-blue" id="contractEndData" style="width:600px; ">
            <div class="bonceHead">
                <span>已到期的合同</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>合同编号</td>
                        <td>到期日</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
            </div>
        </div>
        <%-- 已暂停/终止的合同 --%>
        <div class="bonceContainer bounce-blue" id="contractStopData" style="width:600px; ">
            <div class="bonceHead">
                <span>已暂停履约/终止的合同</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>合同编号</td>
                        <td>暂停履约/终止的时间</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
            </div>
        </div>
</div>
<div class="bounce">
    <%-- 查看合同(续约记录后的查看) --%>
   <%-- <div class="bonceContainer bounce-orange" id="viecontract" style="width: 450px;">
        <div class="bonceHead">
            <span>查看合同</span>
            <a class="bounce_close" onclick="editContractOk(0)"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control indexUpdateList">
                <tbody>
                <tr>
                    <td>名称</td>
                    <td>操作</td>
                </tr>
                <tr>
                    <td>合同编号</td>
                    <td>XXXXXXXX</td>
                </tr>
                <tr>
                    <td>签署日期</td>
                    <td>XXXX-XX-XX</td>
                </tr>
                <tr>
                    <td>合同的有效期</td>
                    <td>XXXX-XX-XX</td>至<td>XXXX-XX-XX</td>
                </tr>
                <tr>
                    <td>合同的扫描件或照片</td>
                    <td>
                           <span class="ty-color-blue node" data-name="" date-type=""
                                 data-source="">1</span>
                        <span class="ty-color-blue node" data-name="" date-type=""
                              data-source="">2</span>
                        <span class="ty-color-blue node" data-name="" date-type=""
                              data-source="">3</span>
                    </td>
                </tr>
                <tr>
                    <td>合同的可编辑版</td>
                    <td>
                            <span class="ty-color-blue node" data-name="" date-type=""
                                  data-source="">查看</span>
                    </td>
                </tr>
                <tr>
                    <td>本合同下的材料（共<span>XX种</span>）</td>
                    <td>
                            <span class="ty-color-blue node" data-name="" date-type=""
                                  data-source="">查看</span>
                    </td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td>XXXXXXXXXXXXXXX</td>
                </tr>
                <tr>
                    <td>本版本合同的修改记录</td>
                    <td>
                            <span class="ty-color-blue node" data-name="" date-type=""
                                  data-source="">查看</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>--%>

        <%-- 供应商信息查看 --%>
        <div class="bonceContainer bounce-blue" id="havelook" style="min-width: 1200px;position: absolute; z-index: 10">
            <div class="bonceHead">
                <span>供应商信息查看</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon" style="height: 600px;overflow: auto">
                <div class="lina" style="margin: 43px;">
                    <div class="part">
                        <div class="item" style="display: flex">
                            <div class="item" style="display: flex">
                                <span class="item_title" style="text-align: left;">供应商名称:</span>
                                <span class="item_content" style="width: 600px" id="see_name"></span>
                            </div>
                        </div>
                        <div class="item" style="display: flex">
                            <div class="item" style="display: flex">
                                <span class="item_title" style="text-align: left;">供应商简称:</span>
                                <span class="item_content" id="see_fullname" style="width: 300px"></span></div>
                            <div class="item" style="margin-left: 348px;">
                            <span class="item_title" style="margin-right: 0;">
                                供应商代号:
                            </span>
                                <span class="item_content" id="see_codde" style="width: 200px"></span>
                            </div>
                        </div>
                        <div class="item" style="display: flex">
                            <div class="item" style="display: flex">
                                <span class="item_title" style="text-align: left;">创建者:</span>
                                <span class="item_content">
                                <span id="see_createName"></span>
                                <span id="see_createDate"></span>
                            </span>
                            </div>
                            <div class="right_btn">
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="getSuspendRecordList($(this))">
                                    暂停采购/恢复采购的操作记录
                                </button>
                            </div>
                            <div class="right_btn">
                                <button class="ty-btn ty-btn-blue ty-circle-3" data-type="spbaserecord"
                                        data-obj="see" onclick="getlookup($(this))">修改记录
                                </button>
                            </div>
                        </div>
                        <div class="item">
                            <div class="item" id="prient">
                                <span class="item_title" style="text-align: left;">暂停采购:</span>
                                <span class="item_content" style="width: 300px"></span>
                            </div>
                        </div>
                    </div>
                    <div class="part">
                        <div class="item" style="display:flex;">
                            <div class="item_title" style="text-align: left;">全景照片：</div>
                            <div class="item_content" id="overallImgUpload"></div>
                        </div>
                        <div id="gotpose" style="display: flex;align-items: center">
                            <p style="margin: 0 0 0;">可接受挂账，账期<span>XX</span>天，则货物入库之日起计算，不需要预付款</p>
                        </div>
                    </div>
                    <div class="part">
                        <div class="item" style="align-items: center;">
                            <span class="item_title" style="text-align: left;">开票信息</span>
                            <button class="ty-btn ty-btn-blue ty-circle-3" data-type="spinvoiceRecords"
                                    data-obj="see" onclick="getmiddcord($(this))" style="margin-left: 862px;">
                                修改记录
                            </button>
                        </div>
                        <div id="shuil" style="display: flex">
                            <p style="margin: 0 0 0;">该供应商能开税率为<span>X</span>%的增值税专用发票，可接收承兑汇票</p>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl" style="margin-left: 8px;">合同信息</span>
                        <span style="color: #5399c2;font-size: 0.8em;margin-left: 50px;"></span>
                        <div class="dataList contractList">
                            <table class="ty-table ty-table-control contractPlaceList">
                                <thead style="background: none">
                                <td>合同编号</td>
                                <td>签署日期</td>
                                <td>合同的有效期</td>
                                <td>本合同下的材料</td>
                                <td>操作</td>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl" style="margin-left: 8px;">邮寄信息</span>
                        <div class="dataList receiveList">
                            <table class="ty-table ty-table-control mailPlaceList">
                                <thead style="background: none">
                                <td>邮寄地址</td>
                                <td>联系人</td>
                                <td>邮政编码</td>
                                <td>手机</td>
                                <td>操作</td>
                                </thead>
                                <tbody id="clist21"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl" style="margin-left: 8px;margin-right: -38px;">联系人</span>
                        该供应商现共有如下<span class="contactNum"></span>位联系人
                        <div class="dataList receiveListo">
                            <table class="ty-table ty-table-control">
                                <thead style="background: none">
                                <td>姓名</td>
                                <td>职位</td>
                                <td>手机</td>
                                <td>操作</td>
                                </thead>
                                <tbody id="clist31"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">关闭</span>
            </div>
        </div>
    <%-- 点击‘批量导入’按钮后展示的提示弹窗--%>
    <div class="bonceContainer bounce-red" id="batchimport">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="nameing">
                <p class="detel" style="margin-left: 50px;">
                    <span>上次的批量导入尚未完成。</span>
                </p>
                <p class="leMar gap">
                    <input type="hidden" id="operateport">
                    <span onclick="operateport(1,$(this))" class="radioCon" style="display: block;margin-bottom: 15px;">
                        <i id="operateport1" class="fa fa-circle-o"></i>继续上次的操作
                    </span>
                    <span onclick="operateport(2,$(this))" class="radioCon" style="display: block;">
                        <i id="operateport2" class="fa fa-circle-o"></i>放弃上次的操作，重新批量导入
                    </span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="keepsure()" id="keepstre">确定</span>
        </div>
    </div>
    <%-- 批量导入弹窗--%>
    <div class="bonceContainer bounce-green" id="batimptble">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form action="../equipment/model/import" id="leading" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：点击“下载”，以下载空白的“供应商清单”。</p>
                        <div class="flexRow">
                            <span>供应商清单</span>
                            <a href="<%=System.getProperty("BaseUrl")%>/assets/template/supList.xls"
                               id="mould1" download="供应商清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“供应商清单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”，之后选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="importUploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="importOk('cancel');">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="importOk(0)">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加或删除“供应商清单”空白表的“列”，也不要修改修改各列的名字，否则上传会失败。</span></br>
                            <span>2、“供应商清单”“另存为”至电脑上时，可使用新的文件名，但点击本页面的“浏览”时如选错文件，上传可能失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <%-- 放弃提示弹窗 --%>
    <div class="bonceContainer bounce-red" id="giveupend">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="nameing">
                <p class="detel" style="text-align: center;margin-bottom: 0px;">放弃后，本次批量导入的数据将消失不见。</p>
                <p class="detel" style="text-align: center;margin-bottom: 0px;">确定放弃吗？</p>
            </div>
        </div>
        <div class="bonceFoot getoffer">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="giveupsure(3)">确定</span>
        </div>
    </div>
    <%-- 下一步提示弹窗--%>
    <div class="bonceContainer bounce-red" id="nextpont">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="nameing">
                <p class="detel" style="text-align: center;margin-bottom: 0px;">还有<span class="numer">XX</span>种供应商无法保存至系统。</p>
                <p class="detel" style="text-align: center;margin-bottom: 0px;">进入下一步，这些数据将被舍弃。</p>
                <p class="detel" style="text-align: center;margin-bottom: 0px;">确定进入下一步吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pacesure()">确定</span>
        </div>
    </div>
    <%-- 删除提示弹窗--%>
    <div class="bonceContainer bounce-red" id="dettlepont">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="nameing">
                <p class="detel" style="text-align: center;margin-bottom: 0px;">确定删除这条数据吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="dettend()" id="dettend">确定</span>
        </div>
    </div>
    <%-- 修改供应商数据 --%>
    <div class="bonceContainer bounce-blue" id="updatesuplist" style="width: 500px;">
        <div class="bonceHead">
            <span>修改供应商数据</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="margin: 20px;margin-left: 44px;margin-right: 53px;">
            <div class="cita1" style="padding: 0 46px;">
                <div class="citem gap">
                    <span class="ty-color-red">*</span>供应商名称
                </div>
                <div class="chose1">
                    <input type="text" class="ibige login-enter-main-content-User" style="width: 287px;" id="userName">
                    <div style="top:-27px;cursor:pointer;display: block;right: 13px;position: relative;" class="input_clear" id="cloone">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">x</button>
                    </div>
                </div>
            </div>
            <div class="cita2" style="padding: 0 46px;">
                <div class="citem gap">
                    <span class="ty-color-red">*</span>简称
                </div>
                <div class="chose2">
                    <input type="text" class="ibige" style="width: 287px;" id="name">
                    <div style="top:-27px;cursor:pointer;display: block;right: 13px;position: relative;" class="input_clear" id="cloone1">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">x</button>
                    </div>
                </div>
            </div>
            <div class="cita3" style="padding: 0 46px;">
                <div class="citem gap">
                    <span class="ty-color-red">*</span>代号
                </div>
                <div class="chose3">
                    <input type="text" class="ibige" style="width: 287px;" id="coden">
                    <div style="top:-27px;cursor:pointer;display: block;right: 13px;position: relative;" class="input_clear" id="cloone2">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">x</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updatensure()" id="updatensure">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="confirm_tip" style="width: 550px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p>当前为供应商创建阶段，还没到创建供应关系的时候，所以新增的合同里没法添加材料。</p>
                <p>您可“继续操作”，待有了关联的数据后再完善相关信息。</p>
            </div>
            <div>
                <span class="fa fa-circle-o gapFar" onclick="noMoreTip($(this))"></span>知道了，不再提示！
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="confirm_tipOk()">继续操作</span>
        </div>
    </div>
    <%-- 新增供应商 --%>
    <div class="bonceContainer bounce-green window" id="addAccount" style="min-width: 1200px">  <%--大神改的位置--%>
        <div class="bonceHead">
            <span>新增供应商</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 600px;overflow: auto;margin-left: 0;">
            <form class="sale_updata threeCon" id="addsupplier" style="margin: 43px;">
                <div class="">
                    <p class="one">基本信息</p>
                    <div class="sale_c upsth leMar" style="font-size: 14px;display: flex">
                        <div class="sale_ttl1">
                            <span> <i class="xing">*</i>供应商名称:</span>
                        </div>
                        <div class="sale_con1" style="width: 100%">
                            <input type="text" id="suppliername" class="add_splName" name="fullName" placeholder="请录入" require style="width: 100%;">
                        </div>
                    </div>
                    <div class="sale_c upsth leMar" style="font-size: 14px;display: flex">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>供应商简称:</div>
                        <div class="sale_con1">
                            <input type="tename" id="supplieruname" class="add_spluname" name="spluName" placeholder="请录入" require style="width:407px">
                        </div>
                        <div class="sale_ttl1">
                            <span class="ty-color-red">*</span>
                            供应商代号:
                        </div>
                        <div class="sale_con1" style="width: 100%;">
                            <input type="nobe" id="suppliernumber" class="add_splnumb" name="splnmb" placeholder="请录入" require style="width: 100%;">
                        </div>
                    </div>
                    <div class="sale_c upsth leMar" style="margin: 0 0 10px;margin-left: 0px;margin-left: 20px;font-size: 14px;display: flex">
                        <div class="sale_ttl1" style="text-align: center;margin-left: 10px;margin-right: 0;">
                            全景照片:
                        </div>
                        <div id="edit_qImages">
                            <div id="edit_panoramaBtn" class="saleUploadBtn ty-left"></div>
                            <div class="sale_ttl1">（共可上传9张）</div>
                            <div class="imgWall"></div>
                        </div>
                    </div>
                    <p class="leMar" id="getCredit" style="padding-bottom: 9px;display: flex;align-items: center;">
                        是否接受挂账？
                        <input type="hidden" id="setHangAccount">
                        <span onclick="setHangAccount(1 , $(this))" class="radioCon" type="radio" name="getbont" style="margin-left: 84px;">
                        <i id="setHangAccount1" class="fa fa-circle-o"></i>接受
                    </span>
                        <span onclick="setHangAccount(0 , $(this))" class="radioCon" type="radio" name="getbont" style="margin-left: 70px;">
                         <i id="setHangAccount0" class="fa fa-circle-o"></i>不接受
                    </span>
                        <span class="hang_7" style="width: auto;display: inline-block;margin-left: 80px;" >
                        <span>请录入已约定的账期</span>
                        <input type="textdata" id="spcontwek" name="initialPeriod" onkeyup="clearNoNum(this)" />天
                    </span>
                    </p>
                    <p class="leMar hang7_1" id="contdent" style="padding-bottom: 12px;display: flex">
                        请选择从何时开始计算账期？  <input type="hidden" id="setStartDate">
                        <span onclick="setStartDate(1 , $(this))" class="radioCon" id="setStartDate1Con">
                        <i id="setStartDate1" class="fa fa-circle-o"></i>自货入库之日起
                    </span>
                        <span onclick="setStartDate(2 , $(this))" class="radioCon" id="setStartDate2Con">
                        <i id="setStartDate2" class="fa fa-circle-o"></i>自发票入账之日起
                    </span>
                    </p>
                    <p class="leMar hang16" style="padding-bottom: 9px;display: flex">
                        是否需要预付款？ <input type="hidden" id="setAdvanceFee">
                        <span onclick="setAdvanceFee(1, $(this))" class="radioCon" style="margin-left: 70px;">
                        <i id="setAdvanceFee1" class="fa fa-circle-o"></i>需要
                    </span>
                        <span onclick="setAdvanceFee(2, $(this))" class="radioCon" style="margin-left: 70px;">
                        <i id="setAdvanceFee2" class="fa fa-circle-o"></i>不需要
                    </span>
                        <span id="relative0_2" onclick="setAdvanceFee(0, $(this))" class="radioCon" style="margin-left: 79px;">
                        <i id="setAdvanceFee0" class="fa fa-circle-o"></i>不确定
                    </span>
                    </p>
                    <!--手动添加的-->
                    <p class="leMar" id="paymore" style="display: flex;align-items: center;">
                    <span class="hang_17" style="width: 494px;text-align: left;">
                        <span>请录入需预付的比例</span>
                        <input type="text" id="e_gRate1" style="margin-left: 67px;" onclick="uncertainty(0,$(this))" onblur="clear0($(this))"
                               onkeyup="clearNoNum(this)">%
                    </span>
                        <input type="hidden" id="uncertainty">
                        <span id="chooseunde" onclick="uncertainty(1,$(this))" class="radioCon" style="display: inline-block;
                        margin-left: 1px;">
                        <i id="uncertainty1" class="fa fa-circle-o"></i>比例不确定
                    </span>
                    </p>
                    <%-- <p class="two" style="margin-bottom: 18px;">开票信息</p>
                     <p class="leMar" id="havenoce" style="margin-bottom: 17px;display: flex"><span class="ty-color-red" style="text-align: left;margin-left: -10px;
                                 width: auto;display: block;padding-right: 0px;">*</span>
                         该供应商是否能开发票？<input type="hidden" id="haveInvoice">
                         <span onclick="haveInvoice(1 , $(this))" class="radioCon" style="margin-left: 27px;">
                                 <i id="haveInvoice1" class="fa fa-circle-o"></i>是
                              </span>
                         <span onclick="haveInvoice(0 , $(this))" class="radioCon" style="margin-left: 84px;">
                                  <i id="haveInvoice0" class="fa fa-circle-o"></i>否
                             </span>
                     </p>
                     <p class="leMar noNext godemo" id="contemo" style="display: flex;align-items: center;">
                         是否能开增值税专用发票？ <input type="hidden" id="vatInvoice">
                         <span onclick="vatInvoice(1, $(this))" class="radioCon" style="margin-left: 13px;">
                                 <i id="vatInvoice1" class="fa fa-circle-o"></i>是
                             </span>
                         <span onclick="vatInvoice(2, $(this))" class="radioCon" style="margin-left: 84px;">
                                 <i id="vatInvoice2" class="fa fa-circle-o"></i>否
                              </span>
                         <span class="godemo_1" style="width: auto;margin-left: 122px;display: inline-block;">
                                 <span class="litT">请录入税率</span> <input type="text" id="e_gRate0" onkeyup="clearNoNum(this)"
                                                                        data-options="min:0,max:100">%
                             </span>
                     </p>
                     <p class="leMar order hd part hp acceptBills" style="margin-margin-top: 17px;margin-bottom: 10px;display: block;margin-left: 40px;
                         display: flex;">
                         是否可接受汇票？ <input type="hidden" id="huip">
                         <span onclick="huip(1 , $(this))" class="radioCon" style="margin-left: 70px;"><i id="huip1" class="fa fa-circle-o"></i>可接受</span>
                         <span onclick="huip(0 , $(this))" class="radioCon" style="margin-left: 56px;"><i id="huip0" class="fa fa-circle-o"></i>不确定</span>
                     </p>--%>
                    <div style="margin-top: 22px;margin-left: -9px;display: block;" class="hd offerb">
                        <span style="margin-left: 56px;">该供应商供应材料</span>
                        <span class="ty-color-blue btnCat linkBtn conone"  style="margin-left: 142px;" onclick="allcountent($(this))">XX</span>种
                        <div style="padding-left: 56px;margin-top: 20px;">
                            <span>其中</span>
                            <span style="margin-left: 23px;">给开增值税专用发票的</span>
                            <span style="margin-left: 59px;">
                            <span class="ty-color-blue btnCat linkBtn onez" onclick="getMaterial($(this))">XX</span>
                        </span>种
                            <div style="padding-left: 55px;margin-top: 20px;">
                                <span>给开其他发票的</span><span style="margin-left: 101px;">
                            <span class="ty-color-blue btnCat linkBtn twoz" onclick="getoterinvoices($(this))">XX</span>
                        </span>种
                            </div>
                            <div style="padding-left: 54px;margin-top: 20px;">
                                <span>不给开票或不确定是否开票的</span><span style="margin-left: 18px;">
                            <span class="ty-color-blue btnCat linkBtn threez" onclick="noinvoicingend($(this))">XX</span>
                        </span>种
                            </div>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">合同信息</span>
                        <span id="newContract" class="ty-btn ty-btn-blue ty-btn-big node" data-type="new" data-name="contractInfo" data-source="addCustomer">新 增</span>
                        <span style="color: #5399c2;  font-size: 0.8em; margin-left: 50px;"></span>
                        <div class="dataList contractList">
                            <table class="kj-table kj-table-control">
                                <thead>
                                <td>合同编号</td>
                                <td>签署日期</td>
                                <td>合同的有效期</td>
                                <td>操作</td>
                                </thead>
                                <tbody id="clist"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">邮寄信息</span>
                        <span id="newAddress" class="ty-btn ty-btn-blue ty-btn-big node" data-type="new"
                              data-name="receiveInfo" data-source="addCustomer" onclick="uiaddmail()">新 增</span>
                        <div class="dataList receiveList">
                            <table class="kj-table kj-table-control mailPlaceList">
                                <thead style="background: none;">
                                <td>邮寄地址</td>
                                <td>联系人</td>
                                <td>邮政编码</td>
                                <td>手机</td>
                                <td>操作</td>
                                </thead>
                                <tbody id="clist2"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">联系人</span>
                        <span id="newcontacts" class="ty-btn ty-btn-blue ty-btn-big" data-type="new"
                              data-name="conreceiveInfo" data-source="addCustomer" onclick="addcustom()">新 增</span>
                        <%--onclick="addContactInfo(2)"--%>
                        <div class="dataList contectList">
                            <table class="ty-table ty-table-control">
                                <thead style="background: none;">
                                <td>姓名</td>
                                <td>职位</td>
                                <td>操作</td>
                                <td>姓名</td>
                                <td>职位</td>
                                <td>操作</td>
                                </thead>
                                <tbody id="clist3"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cloosne()" id="ansow">取消</span>
            <span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="addSuppliBtn"
                    onclick="sale_addsure()" disabled="">提交</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 hd" id="upSuppliBtn"
                    onclick="sale_updatesure()">确定</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 hd" id="upinvoBtn"
                    onclick="sale_upinvosure()">确定</button>
        </span>
        </div>
    </div>
    <%-- 供应商常规信息的修改 --%>
    <div class="bonceContainer bounce-blue" id="updateSupPanel" style="min-width: 750px;">
        <div class="bonceHead">
            <span>供应商常规信息管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y:auto;">
            <p id="supplie_main"></p>
            <table class="ty-table ty-table-control indexUpdateList">
                <tbody class="box1">
                <tr>
                    <td>名称</td>
                    <td>操作</td>
                </tr>
                <tr>
                    <td>基本信息</td>
                    <td>
                    <span class="ty-color-blue jiben" data-name="spupdate" onclick="openbasic($(this))" style="font-size: 12px;
                        font-family: 宋体;padding: 5px 15px 5px;border-radius: 3px;font-weight: bold;
                        cursor: pointer;">修改
                    </span>
                        <span class="ty-color-blue" data-type="spbaserecord" data-obj="update"
                              onclick="getjbxxme($(this))" style="font-size: 12px;font-family: 宋体;padding: 5px 15px 5px;
                        border-radius: 3px;font-weight: bold;cursor: pointer;">修改记录
                    </span>
                    </td>
                </tr>
                <tr id="contractPanel">
                    <td>合同信息</td>
                    <td>
                        <span class="link-blue node" data-name="contractInfo" data-type="new" data-source="updateCustomer">新增</span>
                        <span class="link-blue node" data-name="contractEndData" data-type="1" data-source="updateCustomer">已到期的合同</span>
                        <span class="link-blue node" data-name="contractStopData" data-type="2" data-source="updateCustomer">已暂停/终止的合同</span>
                    </td>
                </tr>
                <tr class="contractItem hd">
                </tr>
                <tr id="ujnation">
                    <td>邮寄信息</td>
                    <td>
                    <span class="ty-color-blue edit" data-name="mailInfom" data-type="new"
                          data-source="updateSupplie" onclick="ujmess()" style="font-size: 12px;font-family: 宋体;padding: 5px 15px 5px;
                        border-radius: 3px;font-weight: bold;cursor: pointer;">新增
                    </span>
                        <span class="ty-color-blue node" data-name="contractyEndData" onclick="deactivatemess()" data-type="1"
                              data-source="updateSupplie" style="font-size: 12px;font-family: 宋体;padding: 5px 15px 5px;
                        border-radius: 3px;font-weight: bold;cursor: pointer;">已被停用的数据
                    </span>
                    </td>
                </tr>
                <tr id="ujnrion" class="hd">
                </tr>
                <tr id="family">
                    <td>联系人</td>
                    <td>
                    <span class="ty-color-blue edit" data-name="mailInfom" data-type="new"
                          data-source="updateSupplie" onclick="famnew()" style="font-size: 12px;font-family: 宋体;padding: 5px 15px 5px;
                        border-radius: 3px;font-weight: bold;cursor: pointer;">新增
                    </span>
                        <span class="ty-color-blue node" data-name="deleteData" data-type="1"
                              data-source="deldateSupplie" onclick="overdelete()" style="font-size: 12px;font-family: 宋体;padding: 5px 15px 5px;
                        border-radius: 3px;font-weight: bold;cursor: pointer;">已被删除的数据
                    </span>
                    </td>
                </tr>
                <tr id="famier" class="hd">
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container" onclick="scanCtrlHide()">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>常规信息</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer">
            <!--放内容的地方-->
            <div style="min-height: 750px;">
                <div class="ty-container" id="home">
                    <input type="hidden" id="isEdit" />
                    <div id="picShow" style="display: none;">
                        <img src=""/>
                    </div>
                    <div>
                        <div class="main" style="margin: 0 96px;">
                            <span class="Search">
                                <input id="se0" type="text" placeholder="供应商名称/供应商代号" />
                                <span class="se" onclick="searchcontract()">搜索</span>
                            </span>
                            <%--                            <button onclick="getContractMes(1,20)">供应商列表查看</button>--%>
                            <div class="pull-right">
                                <span class="panel_4 ty-btn ty-btn-big ty-btn-cyan ty-circle-3" id="contractImport"
                                      onclick="contractShow()">批量导入</span>
                                <span class="panel_4 ty-btn ty-btn-big ty-btn-green ty-circle-3" id="addCot"
                                      onclick="addSupplierBtn($(this))">新增供应商</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="suspendedContract()"
                                      id="suspendedContractBtn">已暂停采购的供应商</span>
                            </div><br>
                            <div class="opinionCon">
                                <%--                                <button class="ty-color-blue" id="one" onclick="goone()">XX种供应商材料</button>--%>
                                <%--                                <button class="ty-color-blue" id="two" onclick="gotwo()">XX种独家材料</button>--%>
                                <%--                                <button class="ty-color-blue" id="three" onclick="gothree()">XX种不提供材料</button>--%>
                                <div style="margin-top: 42px;" class="word">本公司处于正常状态的供应商共<span>X</span>个，具体如下：</div>
                                <table class="ty-table ty-table-control" id="boxs">
                                    <thead style="background: none;">
                                    <td>供应商</td>
                                    <td>创建人</td>
                                    <td>供应中的材料</td>
                                    <td>独家供应的材料</td>
                                    <td>不再供应的材料</td>
                                    <td>操作</td>
                                    </thead>
                                    <tbody class="sale_Tbody" id="cotManage_body"></tbody>
                                </table>
                                <div id="ye_suppliecontract"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                    </div>
                </div>
                <div class="ty-container" id="materials" style="display: none; margin: 35px 20px 20px 20px">
                    <button type="button" class="btn btn-default" id="back1" onclick="comebck1()" style="margin: 0 96px;">返回常规信息主页</button>
                    <div class="pageStyle container_item">
                        <div class="initBody" style="margin: 0 96px;">
                            <div class="title" style="margin-top: 42px;" id="titale1">
                                <span>供应商简称</span>供应中的材料共<span>XX</span>种，具体如下：
                            </div>
                            <table class="ty-table bg-yellow">
                                <thead style="background: none;" id="tatle">
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                </tr>
                                </thead>
                                <tbody id="boder1" class="ty-table">
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="ye1"></div>
                </div>
                <div class="ty-container" id="exclusive" style="display: none; margin: 35px 20px 20px 20px">
                    <button type="button" class="btn btn-default" id="back2" onclick="comebck1()" style="margin: 0 96px;">返回常规信息主页</button>
                    <div class="pageStyle container_item">
                        <div class="initBody"  style="margin: 0 96px;">
                            <div class="title" style="margin-top: 42px;" id="titale2">
                                <span>XX</span>供应商简称<span>XX</span>独家供应的材料共<span>XX</span>种，具体如下：
                            </div>
                            <table class="ty-table bg-yellow">
                                <thead style="background: none;">
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                </tr>
                                </thead>
                                <tbody id="boder2" class="ty-table"></tbody>
                            </table>
                        </div>
                    </div>
                    <div id="ye2"></div>
                </div>
                <div class="ty-container" id="nolonger" style="display: none; margin: 35px 20px 20px 20px;">
                    <button type="button" class="btn btn-default" id="back3" onclick="comebck1()" style="margin: 0 96px;">返回常规信息主页</button>
                    <div class="pageStyle container_item">
                        <div class="initBody" style="margin: 0 96px;">
                            <div class="title" style="margin-top: 42px;" id="titale3">
                                <span>XX</span>供应商简称<span>XX</span>供应过但不再供应的材料共<span>XX</span>种，具体如下：
                            </div>
                            <table class="ty-table bg-yellow">
                                <thead style="background: none;">
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                </tr>
                                </thead>
                                <tbody id="boder3" class="ty-table"></tbody>
                            </table>
                        </div>
                    </div>
                    <div id="ye3"></div>
                </div>
                <%-- 已暂停采购的供应商 --%>
                <div class="ty-container" style="display: none;margin: 35px 20px 20px 20px" id="procurement">
                    <button type="button" class="btn btn-default" onclick="comebck1()" style="margin: 0 96px;">返回上一页</button>
                    <div class="pageStyle container_item">
                        <div class="initBody" style="margin: 0 96px;">
                            <div class="title" style="margin-top: 42px;">
                                本公司处于暂停采购状态的供应商共<span>X</span>个，具体如下：
                            </div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr style="background-color:white;">
                                    <td>供应商</td>
                                    <td>代号</td>
                                    <td>创建人</td>
                                    <td>“暂停采购”的操作者</td>
                                    <td>供应过的材料</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody id="ye4"></tbody>
                            </table>
                            <div id="ye_stopen"></div>
                        </div>
                    </div>
                    <%--                    <button class="ty-btn-blue" id="recovery" onclick="printen()">恢复采购</button>--%>
                    <%--                    <button class="ty-btn-blue" onclick="prentmy()">XX查看</button>--%>
                </div>
                <%-- 搜索后显示的 --%>
                <div class="ty-container" style="display: none;margin-left:60px" id="searchdi">
                    <button type="button" class="btn btn-default" onclick="comebck1()" style="margin: 0 96px;">返回上一页</button>
                    <div class="pageStyle container_item">
                        <div class="initBody" id="chose" style="margin: 0 96px;">
                            <div class="title" style="margin-top: 42px;">
                                符合条件的数据共<span>XX</span>条，具体如下：
                            </div>
                            <table class="ty-table ty-table-control" id="message">
                                <thead style="background: none;">
                                <td>供应商</td>
                                <td>创建人</td>
                                <td>供应中的材料</td>
                                <td>独家供应的材料</td>
                                <td>不再供应的材料</td>
                                <td>操作</td>
                                </thead>
                                <tbody id="ye5" class="sale_Tbody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <%-- 错误数据页面 --%>
                <div class="ty-container" style="display: none;margin: 35px 20px 20px 20px" id="rongmess">
                    <button type="button" class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="abandonn()" style="margin: 0 96px;">放弃</button>
                    <button type="button" class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="nextstart" onclick="nextstart()" style="margin: 0 96px;">下一步</button>
                    <div class="pageStyle container_item">
                        <div class="initBody" style="margin: 0 96px;">
                            <div class="title" style="margin-top: 42px;">
                                您共导入供应商<span class="allate">XX</span>条，其中以下<span class="wrong">XX</span>条存在问题，<span style="color: red;">无法保存至系统</span>。
                                <br />
                                名称或代号未录入，本次导入供应商的代号互相重复或与系统中已有代号相同等情况，均算作问题。
                            </div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr style="background-color:white;">
                                    <td>供应商名称</td>
                                    <td>简称</td>
                                    <td>代号</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody id="roye1"></tbody>
                            </table>
                            <div id="ye_stopen1"></div>
                        </div>
                    </div>
                </div>
                <%-- 正确数据的页面 --%>
                <div class="ty-container" style="display: none;margin: 35px 20px 20px 20px" id="truemess">
                    <button type="button" class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="abandonn(2)" style="margin: 0 96px;">放弃</button>
                    <button type="button" class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="nextstart2" onclick="nextstart(2)" style="margin: 0 96px;">保存</button>
                    <div class="initBody" style="margin: 0 96px;">
                        <div class="title" style="margin-top: 42px;">
                            导入供应商数据共<span class="allmes">XX</span>条，已舍弃<span class="dittem">XX</span>条，以下<span class="lastem">XX</span>条可保存至系统。
                            <span>  </span>
                            完成本页面的操作后，请点击“保存”！
                        </div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr style="background-color:white;">
                                <td>供应商名称</td>
                                <td>简称</td>
                                <td>代号</td>
                                <td>是否接受挂账</td>
                                <td>是否需要预付款</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody id="roye2"></tbody>
                        </table>
                        <div id="ye_stopen2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>

<%@ include  file="../../common/footerTop.jsp"%>

<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/supplierproduct/suppliecontract.js?v=SVN_REVISION" type="text/javascript"></script>
</div>

<script>

</script>
</body>
</html>