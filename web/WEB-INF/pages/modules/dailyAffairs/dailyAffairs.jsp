<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/dailyAffairs/dailyAffairs.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
/*.hd{ display: none!important; }*/
</style>
<%@ include  file="../../common/headerBottom.jsp"%>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%--<div id="auth" style="display:none;  ">${rolePopedom}</div>--%>
<div class="bounce_Fixed3">
    <div class="bonceContainer bounce-red" id="tip" style="min-width:600px;">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center">
                财务规定：<span class="ty-color-red">一次报销中不可既有发票又有收据</span>。
                <div class="tip"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="selectFee">
        <div class="bonceHead">
            <span>选择费用类别</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center">
                <p>费用类别：<select onchange='setNextCat($(this))' class="feeCat_1"></select></p>
                <p style="display: none;" id="pFeeCat2">二级类别：<select class="feeCat_2" ></select></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="selectFeeOk();  ">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addMemo">
        <div class="bonceHead">
            <span>发票上的备注</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center">
                <textarea placeholder="您手中发票上的备注如有内容，您可在此录入。如觉不必要录入，或无备注，则请忽略。"
                          id="memo3" onkeyup="countWords($(this),60)"></textarea>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="addMemoOk();  ">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="tipDelTr">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center" class="deltip">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="tipDelTrOk() ">确定</span>
        </div>
    </div>
    <a href="" target="_blank" download="" id="bigScan"></a>
</div>
<div class="bounce_Fixed2">

    <div class="bonceContainer bounce-green" id="goodEntry" style="min-width:600px;">
        <div class="bonceHead">
            <span>票据录入</span>
            <a class="bounce_close" onclick="cancelGoodsEntery()  "></a>
        </div>
        <div class="bonceCon clearfix">
            <form action="" id="form_goodEntry">
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 票据种类</span>
                    </div>
                    <div class="formCon">
                        <select name="billCat" id="billCat" require></select>
                    </div>
                </div>
                <div class="ty-nextTip">想要录入的票据上只有一行内容吗？<br>
                    <span type="btn" data-name="iconOneRow" data-val="1"><i class="fa fa-circle-o"></i>是</span>
                    <input type="hidden" id="onlyOneRow"><span class="wid"></span>
                    <span type="btn" data-name="iconOneRow" data-val="0"><i class="fa fa-circle-o"></i>不，内容超过一行</span>
                </div>
                <div>
                    <div class="kindInvoice">
                        <%--增值税发票 普通发票 和 收据 --%>
                        <div class="VATInvoice" style="display: none">
                            <div class="formItem">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> 开票日期</span></div>
                                <div class="formCon"><input type="text" name="issueDate" class="issueDate" id="issueDate" require></div>
                            </div>
                            <div class="formItem formItem_billNo">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> 发票号码</span></div>
                                <div class="formCon"><input type="text" class="billNo" onchange='countWords($(this),8)' require></div>
                            </div>
                            <div class="ty-alert ty-alert-info">！请录入您手中这张票据的内容。<span class="ty-color-orange">如该票据中有多行内容</span>，则先录第一行。</div>
                            <div class="formItem firstFeeCat">
                                <div class="formTitle">
                                    <span><span class="ty-color-red">*</span> 费用类别</span>
                                </div>
                                <div class="formCon">
                                    <select class="feeCat" id="feeCat" require>
                                        <option value="0">---请选择费用类别---</option>
                                    </select>
                                </div>
                            </div>
                            <div class="formItem secondFeeCat" style="display: none">
                                <div class="formTitle">
                                    <span><span class="ty-color-red">*</span></span>
                                </div>
                                <div class="formCon">
                                    <select class="secondFeeCat" require>
                                        <option value="0">---请选择二级费用类别---</option>
                                    </select>
                                </div>
                            </div>
                            <div class="formItem formItem_itemName">
                                <div class="formTitle"><span class="ty-color-red">* </span><span class="titleName" id="m1">货物或应税劳务、服务名称</span></div>
                                <div class="formCon"><input type="text" name="itemName" onchange='countWords($(this),14)' require></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>规格型号</span></div>
                                <div class="formCon"><input type="text" name="model" onchange='countWords($(this),10)'></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>单位</span></div>
                                <div class="formCon"><input type="text" name="unit" onchange='countWords($(this),6)'></div>
                            </div>
                            <div class="formItem formItem_itemQuantity">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> <span class="titleName">数量</span></span></div>
                                <div class="formCon"><input type="text" name="itemQuantity" onkeyUp = 'clearNum(this)' onchange='countWords($(this),10)' require></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>单价</span></div>
                                <div class="formCon"><input type="text" name="uniPrice" disabled></div>
                            </div>
                            <div class="formItem formItem_price">
                                <div class="formTitle"><span><span class="ty-color-red">*</span><span class="titleName">金额</span></span></div>
                                <div class="formCon"><input type="text" name="price" onkeyUp = 'testNum(this)' onchange="countWords($(this),8);setToFixed2($(this))" require></div>
                            </div>
                            <div class="taxInput">
                                <div class="formItem">
                                    <div class="formTitle"><span>税率</span></div>
                                    <div class="formCon"><input type="text" name="taxRate" disabled style="width: 158px"> %<div class="tipTaxRate ty-color-red text-right">税率超过100%</div></div>
                                </div>
                                <div class="formItem">
                                    <div class="formTitle"><span><span class="ty-color-red">*</span> 税额</span></div>
                                    <div class="formCon"><input type="text" name="taxAmount" onkeyUp = 'testNum(this)' onchange="countWords($(this),8);setToFixed2($(this))" require></div>
                                </div>
                                <div class="formItem">
                                    <div class="formTitle"><span>含税合计</span></div>
                                    <div class="formCon"><input type="text" name="amount" onchange="countWords($(this),8);setToFixed2($(this))" disabled></div>
                                </div>
                            </div>
                            <div class="formItem formItem_itemMemo">
                                <div class="formTitle"><span>发票上的备注</span></div>
                                <div class="formCon"><input type="text" class="billMemo" name="memo" onchange="countWords($(this), 30)" ><span class="textMax"></span></div>
                            </div>
                        </div>
                        <%--定额发票内容--%>
                        <div class="quotaInvoice" style="display: none">
                            <div class="ty-alert ty-alert-info">！ 您在此可录入多种定额发票。请按财务要求上传票据照片。系统支持每行均上传一张。</div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td><span class="ty-color-blue" data-name="newRow" type="btn" id="newRow">增加行</span></td>
                                </tr>
                                <tr>
                                    <td><span class="ty-color-red">*</span>费用类别</td>
                                    <td><span class="ty-color-red">*</span>单张发票金额</td>
                                    <td><span class="ty-color-red">*</span>数量</td>
                                    <td>发票金额合计</td>
                                    <td><span class="ty-color-red">*</span>实际支出金额</td>
                                </tr>
                                </thead>
                                <tbody> </tbody>
                            </table>
                            <div class="summaryTip" id="summary2">您已录入定额发票共 0 张，发票金额总计 0 元，实际支出（即您将报销）总计 0 元。</div>
                            <div>
                                <%--<div class="formItem">--%>
                                    <%--<span>备注</span> <input type="text" id="memo" style="display: inline-block; width:400px; ">--%>
                                <%--</div>--%>
                            </div>

                        </div>
                        <%--单行录入增普/其他发票/收据--%>
                        <div class="oneRowInvoice">
                            <div class="ty-alert ty-alert-info">！ 您在此可录入多张只有一行内容的 <span class="in4"></span>，请按财务要求上传票据照片。系统支持每行均上传一张。</div>
                            <div>
                                <span type="btn" data-name="inputNextBtn" id="inputNextBtn" class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-3">录入下一张只有一行内容的增值税普通发票</span>
                                <div class="ty-alert ty-alert-info ty-clear"> </div>
                                <table class="ty-table ty-table-control oneRowTab" style="min-width:1500px;">
                                    <thead>
                                    <tr>
                                        <td><span class="ty-color-red">*</span>费用类别</td>
                                        <td><span class="ty-color-red in2">*</span>发票号码</td>
                                        <td><span class="ty-color-red">*</span>开票日期</td>
                                        <td><span class="ty-color-red">*</span><span class="in3">货物或应税劳务、服务名称</span><span class="in1">票据内容</span></td>
                                        <td>规格型号</td>
                                        <td>单位</td>
                                        <td>数量</td>
                                        <td><span class="ty-color-red">*</span><span id="in4">发票金额</span></td>
                                        <td><span class="ty-color-red">*</span>实际支出金额</td>
                                        <td>发票上的备注</td>

                                    </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                            <div class="summaryTip" id="summary"></div>
                            <div>
                                <%--<div class="formItem">--%>
                                    <%--<span>备注</span> <input type="text" id="memo2" name="memo" style="display: inline-block; width:400px; ">--%>
                                <%--</div>--%>
                            </div>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelGoodsEntery() ">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="goodEntryBtn" type="btn" data-name="sureGoodEntry">录入完毕，下一步</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">

    <div class="bonceContainer bounce-green" id="bounceFixed_tip" >
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
          <div class="text-center">需加班时请尽量提前申请，多谢配合！</div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 iknow">我知道了</button>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="invoiceUpload1" >
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel() "></a>
        </div>
        <div class="hide" style="display: block; ">
            <div id="invoiceUp"></div>
        </div>
        <div class="bonceCon clearfix">
            <div>
                <p>这张发票的识别需录入验证码！</p>
                <p>
                    <span>* 验证码</span>
                    <input type="text" id="inputCode" placeholder="请录入验证码"> <span id="color_hint">请输入验证码图片中的文字</span>
                </p>
                <p>
                    <span id="code" onclick="reFreashQR()"></span> <span class="linkBtn">点击图片刷新</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="invoiceUpload1OK" onclick="validate()">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="billEntry" style="min-width:1200px;">
        <div class="bonceHead">
            <span>票据信息</span>
            <a class="bounce_close" onclick="cancelfile() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="ty-alert ty-alert-info"> 开票日期 <span class="issueDate"></span></div>
            <div>
                <%--增值税专用发票，增值税普通发票货物--%>
                <table class="ty-table ty-table-control VATGood">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td id="m2">货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>税率</td>
                        <td>税额</td>
                        <td>含税合计</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--其他普通发票货物--%>
                <table class="ty-table ty-table-control otherGood" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--收据货物--%>
                <table class="ty-table ty-table-control receipt" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>物品数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="handleBtn">
                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="goodEntryNext">录入本张票据上的下一种货物</button>
                <div class='ty-form-checkbox' skin="green" level="1" style="margin-top: 8px">
                    <i class="fa fa-check"></i>
                    <span>本张票据录入完毕</span>
                </div>
            </div>
            <form id="form_billEntry" style="display: none">
                <div class="formItem formItem_auto">
                    <div class="formTitle t1">为尽量避免录入错误，请再次录入您手中这张票据的总金额 <span class="ty-color-red">*</span></div>
                    <div class="formTitle t2">请录入本票据的价税合计 <span style="width:200px; "></span> 价税合计 <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input type="text" name="billAmount" require onkeyUp = 'testNum(this)' onchange="setToFixed2($(this));setAmount()"> 元<div class="isBillAmountTip ty-color-red">价税合计与本票据其他数据有冲突，请检查并修正！</div></div>
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">内容与本票据完全相同，且开票月份也为<span class="issueMonth"></span>的票据总数量 <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input data-type="num" id="billnum" name="number" require onchange="setAmount()" onkeyUp = 'clearNum(this)' placeholder="1"> 张</div>
                </div>
                <div class="formItem formItem_auto repeatBill" style="display: none">
                    <div class="formTitle">本张发票号码为 <span class="thisBillNo"></span>，<span class="ty-color-orange">请输入其他 <span class="thisBillNum"></span>张发票的发票号码 <span class="ty-color-red">*</span></span> </div>
                    <div class="formCon"><div class="repeatCon"></div></div>
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">如您想要申请报销的金额不是 <span class="ty-color-blue countAllAmount"></span>元，则请修改申请报销的金额  <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input class="inputAmount" name="amount" data-type="num" require onkeyUp = 'testNum(this)' onchange="setToFixed2($(this));"> 元</div>
                </div>
               <%-- <div class="formItem formItem_auto">
                    <div class="formTitle">备注</div>
                    <div class="formCon"><textarea name="memo"  cols="30" rows="2" style="width: 500px" onkeyup="countWords($(this),60)"></textarea><div class="textMax text-right">255/255</div></div>
                </div>--%>
                <div class="ty-alert ty-alert-info">
                    您可点击浏览以上传票据图片。票据是否需上传、如何拍照、是否粘贴等，请按财务的要求。
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">
                        <span>票据图片</span>
                    </div>
                    <div class="formCon">
                        <div class="formConBg">
                            <div class="cp_imgShow clearfix"></div>
                            <div class="cp_imgUpload"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelfile()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="billEntryBtn" type="btn" data-name="billEntry">确定</button>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-red" id="tips">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel(); clearInterval(timer);"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 80%;margin: 0 auto">
                财务规定：<span class="ty-color-red">一次报销中不可既有发票又有收据。</span>您刚才已录入收据，发票需另外提出申请！
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); clearInterval(timer);">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureOvertimeApply()">确定</span>
        </div>
    </div>
    <%--加班 申请--%>
    <div class="bonceContainer bounce-blue overtimeApply">
        <div class="bonceHead">
            <span>我要申请加班</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: 0 auto">
                <div class="item">
                    <div class="item_title">
                        加班日期 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <input type="text" class="beginDate1 ty-input" id="overtimeBeginDate" style="width: 100%" placeholder="请选择日期">
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        加班起止时间 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <select id="overtimeBeginTime" class="ty-input" style="width: 164px"></select>
                        <i class="fa fa-long-arrow-right" style="color: #5d9cec;"></i>
                        <select id="overtimeEndTime" class="ty-input" style="width: 164px"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        计划时长
                    </div>
                    <div class="item_content">
                        <input type="text" class="overtimePlanHours ty-input" id="hours" style="width: 100%" readonly>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        加班事由 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <textarea id="overtimeReason" cols="30" rows="2" class="ty-textarea" style="width:100%"></textarea>
                    </div>
                </div>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureOvertimeApply()">确定</span>
        </div>
    </div>
    <%--我要补报加班--%>
    <div class="bonceContainer bounce-blue overtimeSupplementary">
        <div class="bonceHead">
            <span>补报加班</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: 0 auto">
                <div class="ty-color-gray">
                    请您在此补报加班。
                </div>
                <div class="item">
                    <div class="item_title">
                        加班日期 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <input type="text" class="beginDate1 ty-input" name="beginDate" id="overtimeSupplementary_beginDate" style="width: 100%" placeholder="请选择日期">
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        加班起止时间 <span class="ty-color-red">*</span>
                    </div>
                    <select name="beginTime" class="ty-input" style="width: 164px"></select>
                    <i class="fa fa-long-arrow-right" style="color: #5d9cec;"></i>
                    <select name="endTime" class="ty-input" style="width: 164px"></select>
                </div>
                <div class="item">
                    <div class="item_title">
                        申报时长
                    </div>
                    <div class="item_content">
                        <input type="text" class="overtimePlanHours ty-input hours" style="width: 100%" readonly>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        加班事由 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <textarea name="reason" cols="30" rows="2" class="ty-textarea" style="width:100%"></textarea>
                    </div>
                </div>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureOvertimeSupplementary()">确定</span>
        </div>
    </div>
    <%--指派下属加班--%>
    <div class="bonceContainer bounce-blue overtimeAssign">
        <div class="bonceHead">
            <span>指派下属加班</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: 0 auto">
                <div class="ty-color-gray">
                    在此可指派下属加班。
                </div>
                <div class="item">
                    <div class="item_title">
                        指派原因 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <textarea name="reason" cols="30" rows="2" class="ty-textarea" placeholder="请录入" style="width:100%"></textarea>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        需加班者 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <div class="input_choose">
                            <div class="input_show">
                                <input type="text" class="search">
                            </div>
                        </div>
                        <div class="input_choose_list"></div>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        需加班的日期 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <input type="text" class="beginDate1 ty-input" name="beginDate" id="overtimeAssign_beginDate" style="width: 100%" placeholder="请选择日期">
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        加班起止时间 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <select name="beginTime" class="ty-input" style="width: 164px"></select>
                        <i class="fa fa-long-arrow-right" style="color: #5d9cec;"></i>
                        <select name="endTime" class="ty-input" style="width: 164px"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        加班时长
                    </div>
                    <div class="item_content">
                        <input type="text" class="overtimePlanHours ty-input hours" style="width: 100%" readonly>
                    </div>
                </div>

            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureOvertimeAssign()">确定</span>
        </div>
    </div>
    <%--请假 申请--%>
    <div class="bonceContainer bounce-blue leaveApply">
        <div class="bonceHead">
            <span>请假申请</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: 0 auto">
                <div class="item">
                    <div class="item_title">
                        请假类型 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <select id="leaveType" class="ty-input" style="width: 100%"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        请假开始时间 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <input type="text" id="leaveBeginDate" class="ty-input" style="width:200px"> <select id="leaveBeginTime" class="ty-input" style="width:146px"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        请假结束时间 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <input type="text" id="leaveEndDate" class="ty-input" style="width:200px" autocomplete="new-password"> <select id="leaveEndTime" class="ty-input" style="width:146px"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        请假事由 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <textarea cols="30" rows="2" id="leaveReason" class="ty-textarea" style="width:100%"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureLeaveApply()">确定</span>
        </div>
    </div>
    <%--事后补假--%>
    <div class="bonceContainer bounce-green" id="leaveSupplementaryTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="text-center">此申请通过审批后，相应时间的薪资将按公司规定扣除。确定提交吗</div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="leaveSupplementaryTipOK()" >确定</button>
        </div>
    </div>

    <div class="bonceContainer bounce-blue leaveSupplementary">
        <div class="bonceHead">
            <span>事后补假</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: 0 auto">
                <div class="ty-color-gray">请您在此事后补假。</div>
                <div class="item">
                    <div class="item_title">
                        请假类型 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <select name="leaveType" class="ty-input" style="width: 100%"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        请假开始时间 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <input type="text" name="beginDate" id="leaveSupplementary_beginDate" class="ty-input" style="width:200px"> <select name="beginTime" class="ty-input" style="width:146px"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        请假结束时间 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <input type="text" name="endDate" class="ty-input" id="leaveSupplementary_endDate" style="width:200px"> <select name="endTime" class="ty-input" style="width:146px"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">
                        请假事由 <span class="ty-color-red">*</span>
                    </div>
                    <div class="item_content">
                        <textarea cols="30" rows="2" name="reason" class="ty-textarea" style="width:100%"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureLeaveSupplementary()">确定</span>
        </div>
    </div>
    <%--报销申请页面(新)--%>
    <div class="bonceContainer bounce-green" id="reimburse" style="width: 1200px;" >
        <div class="bonceHead">
            <span>报销申请</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:650px;overflow: auto ">
            <p class="ty-alert ty-alert-info"><span class="ty-color-red">！！需入库</span>的物品<span class="ty-color-red">请勿</span>在此报销！</p>
            <div class="main">
                <form id="form_reimburse">
                    <div class="formItem">
                        <div class="formTitle">
                            <span><span class="ty-color-red">*</span> 事件的发生日期</span>
                        </div>
                        <div class="formCon">
                            <input type="text" name="beginDate" id="reimburseBeginDate" require> - <input type="text" name="endDate" id="reimburseEndDate" require>
                        </div>
                        <div class="formTitle">
                            <span><span class="ty-color-red">*</span> 是否属于销售事务？</span>
                        </div>
                        <div class="formCon transactionType">
                            <input type="radio" name="transactionType" value="1">是
                            <input type="radio" name="transactionType" value="2">否
                        </div>
                    </div>
                    <div class="formItem">
                        <div class="formTitle">
                            <span><span class="ty-color-red">*</span> 报销事由</span>
                        </div>
                        <div class="formCon">
                            <textarea name="purpose" cols="30" rows="3" style="width: 500px" require onkeyup="countWords($(this),255)"></textarea>
                            <div class="textMax text-right">255/255</div>
                        </div>
                    </div>
                </form>
                <div class="ty-alert ty-alert-info" style=" width: 1170px; display: block;  line-height: 31px;">
                    您已录入票据共 <b class="ty-color-blue billCountNumber"> 0 </b> 张，票据金额共 <b class="ty-color-blue billCountAmount"> 0 </b> 元，需报销的金额为 <b class="ty-color-blue countAmount"> 0 </b> 元。
                    <button type="btn" class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-name="goodEntry">票据录入</button>
                    <button type="btn" style="margin-right: 20px;" class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-name="invoiceUpload">票据上传</button>
                </div>
                <div class="isMonthTip ty-color-red" style="display: none">
                    本公司财务规定：一次报销中不可既含有本月的发票，又含有非本月的发票。
                    <div>您所录入的发票中既有本月又有非本月的票据。您要么删除所有非本月票据，要么删除所有本月票据，之后方可提交审批</div>
                </div>
                <div>
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td>票据种类</td>
                            <td>单张票据金额</td>
                            <td>票据数量</td>
                            <td>票据金额合计</td>
                            <td>申请报销的金额</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <%--<p class="tip" id="tip"></p>--%>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelReimburseBtn() ">取消</span>
            <button class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" type="btn" id="reimburseBtn" data-name="reimburse">提交审批</button>
        </div>
    </div>

</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>日常事务</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->

            <div class="ty-container mainPage">
                <ul class="affair">
<%--                    <li name="overTime">--%>
<%--                        <i class="icon icon_overTime"></i>--%>
<%--                        <span class="icon-name" title="我要申请加班">--%>
<%--                            <span>--%>
<%--                                我要申请加班--%>
<%--                            </span>--%>
<%--	                    </span>--%>
<%--                    </li>--%>
<%--                    <li name="overTime-makeUp">--%>
<%--                        <i class="icon icon_overTime-makeUp"></i>--%>
<%--                        <span class="icon-name" title="我要补报加班">--%>
<%--                            <span>--%>
<%--                                我要补报加班--%>
<%--                            </span>--%>
<%--	                    </span>--%>
<%--                    </li>--%>
<%--                    <li name="overTime-assign">--%>
<%--                        <i class="icon icon_overTime-assign"></i>--%>
<%--                        <span class="icon-name" title="指派下属加班">--%>
<%--                            <span>--%>
<%--                                指派下属加班--%>
<%--                            </span>--%>
<%--	                    </span>--%>
<%--                    </li>--%>
<%--                    <li name="leave">--%>
<%--                        <i class="icon icon_leave"></i>--%>
<%--                        <span class="icon-name" title="我要请假">--%>
<%--                            <span>--%>
<%--                                我要请假--%>
<%--                            </span>--%>
<%--	                    </span>--%>
<%--                    </li>--%>
<%--                    <li name="leave-makeUp">--%>
<%--                        <i class="icon icon_leave-makeUp"></i>--%>
<%--                        <span class="icon-name" title="我要事后补假">--%>
<%--                            <span>--%>
<%--                                我要事后补假--%>
<%--                            </span>--%>
<%--	                    </span>--%>
<%--                    </li>--%>
                    <li name="reimburse">
                        <i class="icon icon_application"></i>
                        <span class="icon-name" title="我要报销">
                            <span>
                                我要报销
                            </span>
	                    </span>
                    </li>
                    <li name="shopping" class="disabled">
                        <i class="icon icon_shopping"></i>
                        <span class="icon-name" title="我要购物">
                            <span>
                                我要购物
                            </span>
	                    </span>
                    </li>
                    <li name="car" class="disabled">
                        <i class="icon icon_car"></i>
                        <span class="icon-name" title="我要加班">
                            <span>
                                我要用车
                            </span>
	                    </span>
                    </li>
                    <li name="out" class="disabled">
                        <i class="icon icon_trip"></i>
                        <span class="icon-name" title="我要请假">
                            <span>
                                我要出差
                            </span>
	                    </span>
                    </li>
                    <li name="businessTravel" class="disabled">
                        <i class="icon icon_outgoing"></i>
                        <span class="icon-name" title="我要外出">
                            <span>
                                我要外出
                            </span>
	                    </span>
                    </li>
                    <li name="receive" class="disabled">
                        <i class="icon icon_collar"></i>
                        <span class="icon-name" title="我要购物">
                            <span>
                                我要领用
                            </span>
	                    </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/dailyAffairs/dailyAffairs.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/downLoadFile/downLoadFile.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>
