<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/r_wenjian.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/fileTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
    .colTab{
        float: left;
        min-width: 270px;
        max-width: 500px;
        background-color: #f4f5f9;
        color: #192f4a;
    }
    .colTab_item{
        height: 40px;
        line-height: 40px;
        padding: 0 16px;
        cursor: default;
        font-size: 13px;
    }
    .colTab_item.active{
        background-color: #5d9ded;
        color: #fff;
    }
    .uploadify-button,.uploadDeleteBtn {
        display: inline-block;
        border: none;
        background-color: #e7e7e7;
        height: 24px;
        border-radius: 3px;
        padding: 0 12px;
        font-size: 12px;
        font-weight: 400;
        color: #535353;
        cursor: pointer;
        text-decoration: none;
        flex-shrink: 0;
        line-height: 24px;
        margin: 4px;
    }
    .uploadify-button:hover{
        background-color: #5d9cec;
        color: #fff;
    }
    .progressnum, .uploadify_bottom{
        display: none;
    }
    .uploadify-progress{
        width: 150px;
        height: 6px;
        background-color: #ddd;
        margin: 3px auto;
        border-radius: 2px;
    }
    .file_item{
        width: 80px;
        position: relative;
    }
    .file_item .fileType{
        width: 36px;
        height: 40px;
        margin: auto;
        background-size:36px;
        background-repeat: no-repeat;
    }
    .file_item  .file_name {
        font-size: 12px;
        display:inline-block;
        width:100%;
        overflow:hidden;
        height: 50px;
        margin-top: 4px;
        text-align: center;
    }
    .fileShowList{
        display: flex;
    }
    .delRole, .delFile{
        transform: rotate(45deg);
        -moz-transform: rotate(45deg);
        display: inline-block;
        font-size: 18px;
        width: 18px;
        height: 18px;
        text-align: center;
        vertical-align: middle;
        line-height: 18px;
        cursor: pointer;
    }
    .delFile{
        position: absolute;
        right: 5px;
        top: -5px;
    }

    .field{
        display: flex;
        flex-direction: row;
        margin-top: 6px;
        padding: 4px 0;
    }
    .field label{
        width: 80px;
        flex-grow: 0;
        min-height: 5px;
        flex-shrink: 0;
        font-weight: bold;
        line-height: 1.5;
    }
    .uploadBg{
        background: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        flex: auto;
    }
    .text_disabled {
        width: 100%;
        border: 1px solid #dcdfe6;
        background-color: #efefef;
        border-radius: 2px;
        display: inline-block;
        line-height: 1.5;
        box-sizing: border-box;
        color: #606266;
        font-size: inherit;
        outline: none;
        padding: 4px 8px;
    }
    .item-content .clearInput {
        position: absolute;
        top: 1px;
        right: 0;
        color: #ccc;
        line-height: 30px;
        font-size: 16px;
    }
    .trItem{
        padding:5px 8px;
    }
    .trItem .ttl, .trItem .ttl2, .trItem .ttl3{
        display: inline-block;
        color: #6e7c8a;
        vertical-align: top;
    }
    .trItem .ttl{
        width:80px;
        text-align: right;
    }
    .trItem .ttl2{
        width:70px;
        text-align: right;
    }
    .trItem .ttl3{
        width:150px;
        text-align: left;
    }
    .trItem .con{
        display: inline-block;
        word-wrap: break-word;
        word-spacing:normal;
        max-width: 280px;
    }
    .ty-search .clearInput{
        position: absolute;
        top: 1px;
        right: 35px;
        color: #ccc;
        line-height: 30px;
        font-size: 16px;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-red" id="bounceFixed2_errorTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="bounceFixed_errorTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="handleRecordDetail" style="width: 800px">
        <div class="bonceHead">
            <span>操作记录查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="infoCon clearfix">
                <div>
                    <span class="info_title" id="info_title"></span>
                    <span class="info_handle"></span>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con info_sn"></span></div>
                    <div class="trItem"><span class="ttl">文件大小：</span><span class="con info_size"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con info_gn"></span></div>
                    <div class="trItem"><span class="ttl2">创建人：</span><span class="con info_createName"></span> <span class="info_createDate"></span></div>
                    <div class="trItem"><span class="ttl2">文件类型：</span><span class="con info_version"></span></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-red" id="bounce_errorTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="bounce_confirm">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 knowBtn" type="btn">我知道了</span>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 上传表格 --%>
    <div class="bonceContainer bounce-green" id="newSheet" style="width: 600px">
        <div class="bonceHead">
            <span class="name">上传表格</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 500px; margin: 0 auto">
                请在本地文件中选择
                <div class="field">
                    <div class="uploadBg">
                        <div class="fileUpload"></div>
                        <div class="fileShowList"></div>
                    </div>
                </div>
                <div class="field">
                    <label>表格编号 <span class="ty-color-red">*</span></label>
                    <div class="item-content">
                        <input type="text" name="sn" require style="width: 420px">
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="field">
                    <label>表格名称 <span class="ty-color-red">*</span></label>
                    <div class="item-content">
                        <input type="text" name="name" require style="width: 420px">
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <span class="ty-color-blue">注：您可根据公司实际要求，修改系统给予的表格编号与表格名称。</span>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3 sureBtn" type="btn" data-name="sureNewSheet" id="sureNewSheetBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 修改表格信息 --%>
    <div class="bonceContainer bounce-blue" id="changeSheetMsg" style="width: 600px">
        <div class="bonceHead">
            <span class="name">修改表格信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 500px; margin: 0 auto">
                <span class="ty-color-gray">表格信息的修改无需审批，但将生成不可删除的修改记录，请谨慎操作！</span>
                <div class="field">
                    <label>表格编号 <span class="ty-color-red">*</span></label>
                    <div>
                        <input type="text" name="sn" require style="width: 420px">
                    </div>
                </div>
                <div class="field">
                    <label>表格名称 <span class="ty-color-red">*</span></label>
                    <div>
                        <input type="text" name="name" require style="width: 420px">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn" id="sureChangeSheetMsgBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 表格换版 --%>
    <div class="bonceContainer bounce-blue" id="changeSheetVersion" style="width: 600px">
        <div class="bonceHead">
            <span class="name">表格换版</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 500px; margin: 0 auto">
                请在本地文件中选择
                <div class="field">
                    <div class="uploadBg">
                        <div class="fileUpload"></div>
                        <div class="fileShowList"></div>
                    </div>
                </div>
                <div class="field">
                    <label>表格编号</label>
                    <div class="text_disabled seeSn"></div>
                </div>
                <div class="field">
                    <label>表格名称</label>
                    <div class="text_disabled seeName"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn" data-name="sureChangeSheet" id="sureChangeSheetVersionBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 操作记录 --%>
    <div class="bonceContainer bounce-blue" id="sheetHandleRecord" style="width: 600px">
        <div class="bonceHead">
            <span class="name">操作记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <thead>
                <tr>
                    <td>操作</td>
                    <td>操作者</td>
                    <td>操作后的表格</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_sheetHandleRecord"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 关联记录 --%>
    <div class="bonceContainer bounce-blue" id="relateRecord" style="width: 800px">
        <div class="bonceHead">
            <span class="name">关联记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <span class="fileSn"></span> <span class="fileName ty-color-blue"></span>的关联记录如下
            <table class="ty-table">
                <thead>
                <tr>
                    <td>操作</td>
                    <td>文件编号</td>
                    <td>文件名称</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_relateRecord"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 所关联的文件 --%>
    <div class="bonceContainer bounce-blue" id="relate" style="width: 700px">
        <div class="bonceHead">
            <span class="name">所关联的文件</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            与<span class="fileSn"></span> <span class="fileName ty-color-blue"></span>相关联的文件如下
            <table class="ty-table">
                <thead>
                <tr>
                    <td>文件编号</td>
                    <td>文件名称</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_relate"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 曾关联的文件 --%>
    <div class="bonceContainer bounce-blue" id="related" style="width: 900px">
        <div class="bonceHead">
            <span class="name">曾关联的文件</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            与<span class="fileSn"></span> <span class="fileName ty-color-blue"></span>曾关联过的文件如下
            <table class="ty-table">
                <thead>
                <tr>
                    <td>文件编号</td>
                    <td>文件名称</td>
                    <td>关联的操作</td>
                    <td>解除关联的操作</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_related"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>表格管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container" style="position:relative; ">
                <div id="btn-group" style="width: 1280px;text-align: right">
                    <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 customQuery" id="customQueryBtn" type="btn" data-name="newSheet">上传表格</button>
                    <span class="ty-search">
                        <%--<span class="ty-search-ttl"></span>--%>
                        <input class="ty-searchInput" id="fileNameOrSn" type="text" placeholder="文件名称/文件编号">
                        <i class="fa fa-times-circle clearInput"></i>
                        <div class="ty-searchBtn" onclick="searchBtn()"></div>
                    </span>
                </div>
                <div>
                    <div id="main" class="ty-mainData">
                        <div class="ty-fileContent">
                            <%-- 文件夹列表 --%>
                            <div class="colTab" id="listDoTc" data-name="main">
                                <div class="level1" level="1">
                                    <li class="colTab_item active">
                                        <div title="待关联的表格" data-id="" data-child="false">
                                            <i class="fa fa-chain-broken"></i>
                                            <span>待关联的表格</span>
                                        </div>
                                    </li>
                                    <li class="colTab_item">
                                        <div title="已关联的表格" data-id="" data-child="false">
                                            <i class="fa fa-chain"></i>
                                            <span>已关联的表格</span>
                                        </div>
                                    </li>
                                    <li class="colTab_item">
                                        <div title="被废止的表格" data-id="" data-child="false">
                                            <i class="fa fa-trash"></i>
                                            <span>被废止的表格</span>
                                        </div>
                                    </li>
                                </div>
                            </div>
                            <%-- 文件列表 --%>
                            <div class="mar">
                                <div class="searchTopBar" style="display: none">
                                    <button class="ty-btn ty-btn-blue" style="margin-bottom: 8px" onclick="backToMain()">返回</button>
                                    <div class="ty-alert ty-alert-info">
                                        <i class="fa fa-check-circle"></i> 共有<span class="ty-color-blue searchSheetNum"></span>条数据符合搜索条件
                                    </div>
                                </div>
                                <div class="ty-fileList mainFileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_sheetList"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                        <div class="ty-searchContent" style="display: none">
                            <div class="ty-right">
                                <button class="ty-btn ty-btn-blue ty-circle-3" id="rebackBtn" onclick="goBack('query')">返回</button>
                            </div>
                            <div class="mar">
                                <ul class="ty-secondTab" id="searchSort" style="display: none; margin-bottom:8px">
                                    <li>发布人 <select name="" id="search_applier"></select></li>
                                    <li>发布时间<span class="sort sortName" style="display: none"><i class="fa fa-long-arrow-down"></i></span></li>
                                </ul>
                                <div class="searchInfo">
                                    <div class="folderContent">
                                        <div class="searchFolder">
                                            <div class="searchFolderContent"></div>
                                            <div id="ye-search-folder"></div>
                                        </div>
                                        <div class="childFolder"></div>
                                    </div>
                                    <div class="fileContent">
                                        <div class="searchFile"></div>
                                        <div id="ye-search-file"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="resHistory" style="display: none">
                        <div class="ty-searchContent">
                            <div class="ty-right">
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="goBack('record')">返回</button>
                            </div>
                            <div class="mar">
                                <div class="fileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_record"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    var isGeneral = generalType === '1' || generalType === '2'
    var isSuper = generalType === '0'
    if(isGeneral){
        $("#fileUploadBtn").html("上传文件");
        $("#uploadDirect").html("本文件直接发布，无需他人审批");

    }else {
        $("#fileUploadBtn").html("文件发布申请");
        $("#folderUploadBtn").remove()
        $("#uploadDirect").html("本文件直接提交给文管，无需他人审批");
    }
</script>
<script src="../script/resourseCenter/sheet.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
