<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/r_wenjian.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/fileTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed3">
    <%--移动弹窗的确定弹窗--%>
    <div class="bonceContainer bounce-blue" id="bounceFixed3_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="seeBorrowPerson" style="width: 600px">
        <div class="bonceHead">
            <span>借阅人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clearfix">
                <h4 class="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件名称：</span><span class="con" name="name"></span></div>
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" name="fileSn"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con" name="changeNum"></span></div>
                    <div class="trItem"><span class="ttl2">借阅人数：</span><span class="con" name="borrowNum"></span></div>
                </div>
            </div>
            <table class="ty-table">
                <thead>
                <tr>
                    <td>姓名</td>
                    <td>性别</td>
                    <td>手机号</td>
                    <td>部门</td>
                    <td>职位</td>
                </tr>
                </thead>
                <tbody class="table_borrowPerson"></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</button>
        </div>
    </div>
    <%-- 文件移动-权限调整页面-查看更多 --%>
    <div class="bonceContainer bounce-blue" id="moreMoveAcl" style="width: 600px">
        <div class="bonceHead">
            <span>查看更多数据</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
           <div class="moreMoveAclCon">
               <div class="item">
                   有本文件使用权限者，原有如下 <b class="ty-color-blue oldAclNum"></b> 人
               </div>
               <div class="roleList oldAcl"></div>
               <div class="item">
                   点击“移动后的使用权限”页上的“确定”后，将失去本文件使用权限者共如下 <b class="ty-color-blue loseAclNum"></b> 人
               </div>
               <div class="roleList loseAcl"></div>
               <div class="item">
                   点击“移动后的使用权限”页上的“确定”后，将新得到本文件使用权限者共如下 <b class="ty-color-blue getAclNum"></b> 人
               </div>
               <div class="roleList getAcl"></div>
           </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--移动弹窗的确定弹窗--%>
    <div class="bonceContainer bounce-blue" id="bounceFixed2_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="movedScanSet" style="width:700px; ">
        <div class="bonceHead">
            <span>移动后的使用权限</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="moveSetCon">
                <div class="item">
                    本文件将移至 <b class="ty-color-blue moveToFolderName"></b> 文件夹下。移动可能导致文件使用权限发生变化。</br>
                    点击确定后，将拥有本文件使用权限的人员如下表。
                </div>
                <div class="item">
                    您可对下表人员“减员调整”，并可将所减人员再“增员调整”回来。<br>
                    想让下表以外人员拥有使用权限，需到“文件夹管理”中修改上级文件夹的使用权限。
                </div>
                <div class="ty-hr"></div>
                <div class="item">
                    点击本页面上的“确定”后，有本文件使用权限者共如下 <b class="ty-color-blue scanUseNum"></b> 人。
                    <span class="ty-color-blue ty-right">减员调整</span>
                </div>
                <div class="roleList addList"></div>
                <div class="ty-hr"></div>
                <div class="item">
                    被“减员调整”的共如下 <b class="ty-color-blue decreaseNum"></b> 人。您还可将某人或全部“增员调整”回去。
                    <span class="ty-color-blue ty-right">增员调整</span>
                </div>
                <div class="roleList delList"></div>
                <div class="ty-hr"></div>
                <div class="item">
                    点击“查看更多”，可查看本文件移动前后与使用权限有关的更多数据！
                    <button class="ty-btn ty-btn-blue ty-right" onclick="seeMoreMoveAcl()">查看更多</button>
                </div>
                <div class="ty-hr"></div>
                <div class="ty-checkbox">
                    <input type="checkbox" id="iReadMove">
                    <label for="iReadMove"></label>
                    权限已调整完成，或虽未调整但无需调整！
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureMove">确定</span>
        </div>
    </div>
    <%--本版本的签收记录--%>
    <div class="bonceContainer bounce-blue" id="signRecord">
        <div class="bonceHead">
            <span>本版本的签收记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="infoCon">
                <h4 class="info_fileName"></h4>
                <div>文件编号：<span class="info_fileSn"></span></div>
                <div class="ty-alert creator">
                    版本号：G <span class="info_changeNum"></span>
                    <div class="text-right" style="flex: auto"><span class="info_creator"></span></div>
                </div>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>职工</td>
                        <td>签收时间</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="bounceFixed_errorTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-circle-3"  onclick="bounce_Fixed.cancel()">取消</span>
        </div>
    </div>
    <%-- scanSetTip --%>
    <div class="bonceContainer bounce-blue " id="scanSetTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeRight(1)">我知道了</span>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip2">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess2" style="text-align: center;"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 签收提示 --%>
    <div class="bonceContainer bounce-blue" id="confirmSignIn">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: 0 auto">
                <div style="margin: 16px 0">
                    您本次上传了多个文件。请确定签收者签收时的模式。
                </div>
                <div style="margin: 16px 0">
                    <div class="ty-radio">
                        <input type="radio" name="wayOfSignIn" id="filesOnceSignIn" value="1">
                        <label for="filesOnceSignIn"></label>
                        多个文件一次签收
                    </div>
                    <div class="ty-color-blue">
                        签收者省事，但更可能忽略文件本身内容
                    </div>
                </div>
                <div class="ty-radio">
                    <input type="radio" name="wayOfSignIn" id="filesMultipleSignIn" value="2">
                    <label for="filesMultipleSignIn"></label>
                    各文件需分别签收
                </div>
                <div class="ty-color-blue">
                    签收者麻烦，但会提高对文件内容关注度的可能性
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 sureBtn"  type="btn" data-name="sureConfirmSignIn" id="sureConfirmSignInBtn">确定</button>
        </div>
    </div>
    <%--各种操作记录--%>
    <div class="bonceContainer bounce-blue" id="fileHandleRecord" style="width: 650px">
        <div class="bonceHead">
            <span class="recordTitleName">修改文件编号</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:300px; overflow-y: auto; ">
            <table class="ty-table recordTable"></table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="F_errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">确定</span>
        </div>
    </div>
    <%--选择保存位置--%>
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 650px">
        <div class="bonceHead">
            <span class="bounce_title">选择保存位置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="folder_list ty-colFileTree" data-name="chooseSaveFolder"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn" data-name="sureChooseFolder" id="sureChooseFolderBtn">确定</button>
        </div>
    </div>
    <%--移动弹窗--%>
    <div class="bonceContainer bounce-blue" id="moveFile">
        <div class="bonceHead">
            <span>移动到</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <%-- 移动到的文件夹列表 --%>
        <div class="bonceCon">
            <div class="ty-colFileTree" data-name="moveTo"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn"  type="btn" data-name="sureMoveFile" id="sureMoveFileBtn">下一步</button>
        </div>
    </div>
    <%--在关联中选择（待关联，已关联，被废止）--%>
    <div class="bonceContainer bounce-blue" id="chooseInFolder" style="width: 900px">
        <div class="bonceHead">
            <span class="bounce_title">在待关联的表格中选择</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <%-- 移动到的文件夹列表 --%>
        <div class="bonceCon">
            <div class="avatar" style="width: 800px ; margin: 0 auto">
                <div class="text-right">
                    <span class="ty-search">
                       <input class="ty-searchInput" id="fileNameOrSn2" type="text" placeholder="文件名称/文件编号">
                       <i class="fa fa-times-circle clearInput"></i>
                       <div class="ty-searchBtn" onclick="searchBtn2()"></div>
                   </span>
                </div>
                <div class="ty-hr"></div>
                <div class="ty-fileList"></div>
                <div id="ye_chooseRelate"></div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <%--操作记录查看--%>
    <div class="bonceContainer bounce-blue" id="handleRecordDetail" style="width: 800px">
        <div class="bonceHead">
            <span>操作记录查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="infoCon clearfix">
                <h4 class="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con info_sn"></span></div>
                    <div class="trItem"><span class="ttl">文件大小：</span><span class="con info_size"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con info_gn"></span></div>
                    <div class="trItem"><span class="ttl2">创建人：</span><span class="con info_createName"></span> <span class="info_createDate"></span></div>
                    <div class="trItem"><span class="ttl2">文件类型：</span><span class="con info_version"></span></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>

    <%--文件废止/复用记录--%>
    <div class="bonceContainer bounce-blue" id="terminateRecord" style="width: 800px">
        <div class="bonceHead">
            <span class="bounce_title">文件废止/复用记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="infoCon">
                <h4 class="info_fileName"></h4>
                <div>文件编号：<span class="info_fileSn"></span></div>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>事项</td>
                        <td>操作时间</td>
                        <td>操作者</td>
                        <td>操作时的版本号</td>
                        <td>已签收/应签收</td>
                        <td>签收记录</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn">确定</span>
        </div>
    </div>
    <%-- 使用权限设置 --%>
    <div class="bonceContainer bounce-blue" id="scanSet">
        <div class="bonceHead">
            <span>使用权限设置</span>
            <a class="bounce_close" onclick="cancelChangeRight()"></a>
        </div>
        <div class="bonceCon ">
            <input type="hidden" id="isNew">
            <p><span class="packType">文件夹</span>名称：<span class="packName"></span> </p>
            <p class="">
                请选择需使用本<span class="packType">文件夹</span>的职工 <span style="margin-left:100px; " class="btnLink" data-type="allSelect">全选</span>
                <span class="ty-right txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    已选职工：<span class="selectedNum">20</span>位
                    <span class="btnLink" data-type="allClear">全部清空</span>
                </span>
            </p>
            <div class="departTree">
                <ul class="ty-left">
                    <p class="txtR"><span class="btnLink changeTypeLeft" id="changeTypeLeft" onclick="changeType($(this), 'left')">直接展示全部职工</span></p>
                    <div id="allRight"></div>
                </ul>
                <div class="ty-left arrow"><i class="fa fa-exchange"></i></div>
                <form class="ty-left" id="deparCon">
                    <input type="hidden" name="categoryId" id="categoryId">
                    <p class="txtR"><span class="btnLink" id="changeTypeRight" onclick="changeType($(this), 'right')">切换为按部门展示</span></p>
                    <ul id="nowRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
            <p>&nbsp;
                <span class="cancelCon">以下<span id="cancelNum">22</span>位职工刚被取消了本<span class="packType">文件夹</span>的使用权限。</span>
                <span class="ty-right getCon txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    以下<span id="getNum"></span>位职工刚获得了本<span class="packType">文件夹</span>的使用权限。
                </span>
            </p>
            <div class="departTree changeCon">
                <ul class="ty-left cancelCon" style="height:150px;">
                    <div id="cancelRight"></div>
                </ul>
                <form class="ty-right getCon" style="margin-right:15px;height:150px;">
                    <ul id="getRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelChangeRight()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeRight()" id="sureChangeRight">确定</button>
        </div>
    </div>
    <%-- 新增子级类别 --%>
    <div class="bonceContainer bounce-green bounce-newClass">
        <div class="bonceHead">
            <span>新建子文件夹</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>在【<span class="recentParentFolder"></span>】下新建子文件夹</p>
            <p>子文件夹名称</p>
            <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
            <p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。
                <span class="ty-right btnLink" id="scanSetFolerBtn" onclick="scanSetBtn()">去设置使用权限</span>
            </p>
        </div>
        <span class="hd" id="havRightUserCon"></span>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewClass()">确定</span>
        </div>
    </div>
    <%-- 提示 --%>
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align:center; font-size:16px; margin-top: 20px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--上传文件弹窗--%>
    <div class="bonceContainer bounce-blue" id="fileUpload" style="width: 500px">
        <div class="bonceHead">
            <span class="bounce_title">文件发布申请</span>
            <a class="bounce_close" onclick="chargeXhr()"></a>
        </div>
        <div class="bonceCon clearfix">
            <p>请选择要上传的文件</p>
            <div class="upload_avatar">
                <input name="file" type="file"  id="upload-file-01">
            </div>
            <div class="inputPart">
                <div class="show_fileOrFiles">
                    <div class="item-row">
                        <div class="item-title">文件编号 <span class="ty-color-red">*</span></div>
                        <div class="item-content">
                            <input type="text" name="fileNo" class="ty-inputText" require>
                            <i class="fa fa-times-circle clearInput"></i>
                        </div>
                    </div>
                    <div class="item-row">
                        <div class="item-title">文件名称 <span class="ty-color-red">*</span></div>
                        <div class="item-content">
                            <input type="text" name="name" class="ty-inputText" require>
                            <i class="fa fa-times-circle clearInput"></i>
                        </div>
                    </div>
                    <div class="item-row">
                        <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注：您可根据公司实际要求，修改系统给予的文件编号与文件名称</div>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title item-title-long">请选择保存位置</div>
                    <div class="item-content">
                        <button class="ty-btn ty-btn-blue ty-circle-3" type="btn" data-name="chooseSaveFolder">选择</button>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title"></div>
                    <div class="item-content">
                        <div class="savePlace"></div>
                    </div>
                </div>
            </div>
            <div class="seePart" style="display: none">
                <div class="item-row">
                    <div class="item-title">文件编号</div>
                    <div class="item-content">
                        <div class="text_disabled see_fileNo"></div>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title">文件名称</div>
                    <div class="item-content">
                        <div class="text_disabled see_fileName"></div>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title">保存位置</div>
                    <div class="item-content">
                        <div class="text_disabled see_savePlace"></div>
                    </div>
                </div>
            </div>
            <div class="hr"></div>
            <div class="item-row">
                <div class="item-title item-title-long">
                    <div class="ty-radio">
                        <input type="radio" name="isNeedOther" value="0" id="noNeedOther">
                        <label for="noNeedOther"></label> <span id="uploadDirect">本文件直接提交给文管，无需他人审批</span>
                    </div>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title item-title-long">
                    <div class="ty-radio">
                        <input type="radio" name="isNeedOther" id="needOther" value="1" checked>
                        <label for="needOther"></label> 本文件需审批
                    </div>
                </div>
                <div class="item-content">
                    <select class="ty-inputSelect chooseApprover">
                        <option value="选择审批人" selected></option>
                    </select>
                </div>
            </div>
            <div class="item-column">
                <p class="inputPart">说明</p>
                <p class="seePart">换版原因</p>
                <input type="text" name="content" class="ty-inputText inputPart" placeholder="您可在此录入必要的说明。如无说明，可忽略。">
                <input type="text" name="content" class="ty-inputText seePart" placeholder="您可在此阐述换版原因。如无，可忽略。" style="display: none">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="chargeXhr()">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureUploadNewFile" id="sureUploadNewFileBtn">确定</button>
        </div>
    </div>
    <%--文件夹上传弹窗--%>
    <div class="bonceContainer bounce-blue" id="folderUpload" style="width: 500px">
        <div class="bonceHead">
            <span>上传文件夹</span>
            <a class="bounce_close" onclick="chargeXhr()"></a>
        </div>
        <div class="bonceCon clearfix">
            <p>请选择要上传的文件夹<span id="folderStatus" style="padding-left: 20px;"></span></p>
            <div class="upload_avatar"><input name="file" type="file"  id="upload-folder-01"></div>
            <div class="item-row">
                <div class="item-title item-title-long">请选择保存位置</div>
                <div class="item-content">
                    <button class="ty-btn ty-btn-blue ty-circle-3" type="btn" id="chooseSaveFolderBtn" data-name="chooseSaveFolder">选择</button>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title">文件夹名称 <span class="ty-color-red">*</span> </div>
                <div class="item-content">
                    <input type="text" id="folder_name" class="ty-inputText" require>
                    <i class="fa fa-times-circle clearInput"></i>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title"></div>
                <div class="item-content">
                    <div class="savePlace"></div>
                </div>
            </div>
            <div class="item-row">
                <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注1  上传成功后请检查各文件的名称或编号。不符合期望的，您可自行修改</div>
            </div>
            <div class="item-row">
                <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i><div>注2  本系统不支持部分格式文件的上传，也不支持超过1.5G的单个文件的上传。系统将提供<span class="ty-color-red">未能成功上传的文件清单</span>，以便您妥善安排</div></div>
            </div>
            <div class="item-row">
                <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注3  对于某次上传的持续时间，本系统本身并不限制，但断网、断电，及浏览
                    器或本页面被关闭等情况将导致已上传的文件全部丢失。</div>
            </div>
            <div class="item-row">
                <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注4  对于一次上传的文件总数，本系统本身虽不限制，但建议不要超过1000个。</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="chargeXhr()">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3"  type="btn" data-name="sureUploadNewFolder" id="sureUploadNewFolderBtn">确定</button>
        </div>
    </div>

    <%-- 文件查看基本信息 --%>
    <div class="bonceContainer bounce-blue " id="docInfoScan" style="width:800px">
        <div class="bonceHead">
            <span>基本信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <div class="infoCon clearfix">
                <h4 class="info_title" id="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" id="info_sn"></span></div>
                    <div class="trItem"><span class="ttl">保存位置：</span><span class="con" id="info_category"></span></div>
                    <div class="trItem"><span class="ttl">文件大小：</span><span class="con" id="info_size"></span></div>
                    <div class="trItem"><span class="ttl">文件类型：</span><span class="con" id="info_version"></span></div>
                    <div class="trItem"><span class="ttl">说明：</span><span class="con" id="info_content"></span></div>

                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con" id="info_gn"></span></div>
                    <div class="trItem"><span class="ttl2">创建人：</span><span class="con" id="info_creator"></span></div>
                    <div class="trItem"><span class="ttl2">换版人：</span><span class="con" id="info_updater"><span class="info_createName info_name"></span> <span class="info_createDate"></span></span></div>
                    <div class="trItem generalPart">
                        <span class="ttl3">本版本的签收记录：</span>
                        <span class="con times sign_num" style="width: 60px"></span>
                        <span class="link-blue" id="signRecordBtn" onclick="seeSignRecordBtn()" style="margin-left: 12px">查看</span>
                    </div>
                </div>
            </div>
            <div class="infoCon clearfix">
                <div class="ty-left processHistory">
                    <div class="item-header">
                        审批记录
                    </div>
                    <div class="processList">
                    </div>
                </div>
                <div class="ty-left censusInfo">
                    <div class="item-header">
                        统计信息
                    </div>
                    <div class="trItem">
                        <span class="ttl3">浏览次数</span>
                        <span class="con times view_num"></span>
                        <span class="link-blue" id="viewNumBtn" onclick="seeHandelRecordBtn(1)">浏览记录</span>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">下载次数</span>
                        <span class="con times download_num"></span>
                        <span class="link-blue" id="downloadNumBtn" onclick="seeHandelRecordBtn(2)">下载记录</span>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">移动次数</span>
                        <span class="con times move_num"></span>
                        <span class="link-blue" id="moveNumBtn" onclick="seeHandelRecordBtn(5)">移动记录</span>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件名称修改次数</span>
                        <span class="con times name_num"></span>
                        <span class="link-blue" id="changeNameNumBtn" onclick="seeHandelRecordBtn(6)">修改记录</span>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件编号修改次数</span>
                        <span class="con times no_num"></span>
                        <span class="link-blue" id="changeNoNumBtn" onclick="seeHandelRecordBtn(7)">修改记录</span>
                    </div>
                    <div class="trItem generalPart">
                        <span class="ttl3">文件废止/复用记录</span>
                        <span class="link-blue" id="terminateBtn" onclick="seeTerminateRecordBtn(4)" style="margin-left: 78px">查看</span>
                    </div>
                    <div class="trItem generalPart">
                        <span class="ttl3">文件停用/复用记录</span>
                        <span class="link-gray" id="stopReuseBtn" style="margin-left: 78px">查看</span>
                    </div>
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 高级查询弹窗 --%>
    <div class="bonceContainer bounce-blue " id="advancedSearch" data-type="0" style="width: 600px">
        <div class="bonceHead">
            <span>高级搜索</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <ul class="ty-secondTab">
                <li class="ty-active">文件搜索</li>
                <li>文件类别搜索</li>
            </ul>
            <div class="searchCon">
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件名称
                    </div>
                    <div class="eq_r">
                        <input type="text" class="name">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件编号
                    </div>
                    <div class="eq_r">
                        <input type="text" class="fileSn">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件类型
                    </div>
                    <div class="eq_r">
                        <select class="version">
                            <option value="">--请选择文件类型--</option>
                            <option value="doc">*.doc</option>
                            <option value="docx">*.docx</option>
                            <option value="xls">*.xls</option>
                            <option value="xlsx">*.xlsx</option>
                            <option value="zip">*.zip</option>
                            <option value="rar">*.rar</option>
                            <option value="apk">*.apk</option>
                            <option value="ipa">*.ipa</option>
                            <option value="ppt">*.ppt</option>
                            <option value="txt">*.txt</option>
                            <option value="pdf">*.pdf</option>
                            <option value="png">*.png</option>
                            <option value="jpg">*.jpg</option>
                            <option value="wps">*.wps</option>
                            <option value="et">*.et</option>
                        </select>
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        上传人
                    </div>
                    <div class="eq_r">
                        <input type="text" class="createName" />
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        上传时间
                    </div>
                    <div class="eq_r">
                        <input type="text" class="createDateBegin" id="uploadDateStart"> ~
                        <input type="text" class="createDateEnd" id="uploadDateEnd">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        换版人
                    </div>
                    <div class="eq_r">
                        <input type="text" class="updateName">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        换版时间
                    </div>
                    <div class="eq_r" id="postDate">
                        <input type="text" class="updateDateBegin" id="ChangeDateStart"> ~
                        <input type="text" class="updateDateEnd" id="ChangeDateEnd">
                    </div>
                </div>
            </div>
            <div class="searchCon" style="display: none">
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件类别
                    </div>
                    <div class="eq_r">
                        <input type="text" class="folderName">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="advancedSearchBtn">确定</button>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
    <%--更改文件名称--%>
    <div class="bonceContainer bounce-blue" id="changeFileName">
        <div class="bonceHead">
            <span>修改文件名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>当前文件名称：</div>
            <b class="currentFileName"></b>
            <div>请录入新的文件名称：</div>
            <input type="text" class="changeFileName" style="width: 100%">
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="changeFileNameBtn"  onclick="sureChangeFileName()">确定</button>
        </div>
    </div>
    <%--更改文件编号--%>
    <div class="bonceContainer bounce-blue" id="changeFileNo">
        <div class="bonceHead">
            <span>修改文件编号</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>当前文件编号：</div>
            <b class="currentFileNo"></b>
            <div>请录入新的文件编号</div>
            <input type="text" class="changeFileNo" style="width: 100%">
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="changeFileNoBtn" onclick="sureChangeFileNo()">确定</button>
        </div>
    </div>
    <%--文件功能 - 文件废止弹窗--%>
    <div class="bonceContainer bounce-blue" id="fileAbolish" style="width: 700px">
        <div class="bonceHead">
            <span class="bounce_title">文件废止</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="part_relateOther">
                <div>本文件所关联的表格中，还与其他文件关联的如下</div>
                <small class="ty-color-blue">注：本文件的废止，不影响这些表格的使用，也与这些表格与本文件外的关联关系无关</small>
                <table class="ty-table" id="tbl_relateOther">
                    <thead>
                    <tr>
                        <td>表格编号/表格名称</td>
                        <td>引用了该表格的文件</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="hr"></div>
            </div>
            <div class="part_relateSelf">
                <div>本文件所关联的表格中，仅与本文件关联的如下</div>
                <small class="ty-color-blue">注：本文件正式废止前，这些表格可正常使用，正式废止后，这些表格也将被废止</small>
                <div class="item-row">
                    <table class="ty-table" id="tbl_relateSelf">
                        <thead>
                        <tr>
                            <td>表格编号/表格名称</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="hr"></div>
            </div>
            <div class="item-row">
                <div class="tip">确定废止本文件吗？</div>
            </div>
            <div class="item-row">
                <div class="item-title item-title-long">
                    <div class="ty-radio">
                        <input type="radio" name="isNeedOther" value="0" id="fileAbolish_noNeedOther">
                        <label for="fileAbolish_noNeedOther"></label> <span id="fileAbolish_uploadDirect">本文件直接提交给文管，无需他人审批</span>
                    </div>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title item-title-long">
                    <div class="ty-radio">
                        <input type="radio" name="isNeedOther" id="fileAbolish_needOther" value="1" checked>
                        <label for="fileAbolish_needOther"></label> 本文件需审批
                    </div>
                </div>
                <div class="item-content">
                    <select class="ty-inputSelect chooseApprover">
                        <option value="选择审批人" selected></option>
                    </select>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="sureFileAbolishBtn" onclick="sureFileAbolish()">确定</button>
        </div>
    </div>
    <%--  增加需与本文件关联的表格 --%>
    <div class="bonceContainer bounce-green" id="newSheetRelateFile" style="width: 600px">
        <div class="bonceHead">
            <span class="name">增加需与本文件关联的表格</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 500px; margin: 0 auto">
                <div class="ty-alert">您既可在本地文件中选择，也可在Wonderss系统内文件中选择</div>
                <div class="ty-hr"></div>
                如需要，请在本地文件中选择
                <div class="field">
                    <div class="uploadBg">
                        <div class="fileUpload"></div>
                        <div class="fileShowList"></div>
                    </div>
                </div>
                <div class="field">
                    <label>表格编号 <span class="ty-color-red">*</span></label>
                    <div class="item-content">
                        <input type="text" name="sn" require style="width: 420px">
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="field">
                    <label>表格名称 <span class="ty-color-red">*</span></label>
                    <div class="item-content">
                        <input type="text" name="name" require style="width: 420px">
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <span class="ty-color-blue">注：您可根据公司实际要求，修改系统给予的表格编号与表格名称。</span>
                <div class="ty-hr"></div>
                <p><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="chooseInWaitRelate">在待关联的表格中选择</button></p>
                <p><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="chooseInRelated">在已关联的表格中选择</button></p>
                <p><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="chooseInStoped">在被废止的表格中选择</button></p>
<%--                <div class="ty-radio">--%>
<%--                    <input type="radio" id="a" name="">--%>
<%--                    <label for="a"></label>在待关联的表格中选择--%>
<%--                </div>--%>
<%--                <div class="ty-radio">--%>
<%--                    <input type="radio" id="b">--%>
<%--                    <label for="b"></label>在已关联的表格中选择--%>
<%--                </div>--%>
<%--                <div class="ty-radio">--%>
<%--                    <input type="radio" id="c">--%>
<%--                    <label for="c"></label>在被废止的表格中选择--%>
<%--                </div>--%>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3 sureBtn" type="btn" data-name="sureNewSheet" id="newSheetRelateFileBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 修改表格信息 --%>
    <div class="bonceContainer bounce-blue" id="changeSheetMsg" style="width: 600px">
        <div class="bonceHead">
            <span class="name">修改表格信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 500px; margin: 0 auto">
                <span class="ty-color-gray">表格信息的修改无需审批，但将生成不可删除的修改记录，请谨慎操作！</span>
                <div class="field">
                    <label>表格编号 <span class="ty-color-red">*</span></label>
                    <div>
                        <input type="text" name="sn" require style="width: 420px">
                    </div>
                </div>
                <div class="field">
                    <label>表格名称 <span class="ty-color-red">*</span></label>
                    <div>
                        <input type="text" name="name" require style="width: 420px">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn" data-name="sureChangeSheetMsg" id="sureChangeSheetMsgBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 表格换版 --%>
    <div class="bonceContainer bounce-blue" id="changeSheetVersion" style="width: 600px">
        <div class="bonceHead">
            <span class="name">表格换版</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 500px; margin: 0 auto">
                请在本地文件中选择
                <div class="field">
                    <div class="uploadBg">
                        <div class="fileUpload"></div>
                        <div class="fileShowList"></div>
                    </div>
                </div>
                <div class="field">
                    <label>表格编号</label>
                    <div class="text_disabled seeSn"></div>
                </div>
                <div class="field">
                    <label>表格名称</label>
                    <div class="text_disabled seeName"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn" data-name="sureChangeSheet" id="sureChangeSheetVersionBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 操作记录 --%>
    <div class="bonceContainer bounce-blue" id="sheetHandleRecord" style="width: 600px">
        <div class="bonceHead">
            <span class="name">操作记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <thead>
                <tr>
                    <td>操作</td>
                    <td>操作者</td>
                    <td>操作后的表格</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_sheetHandleRecord"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 表格关联的操作记录 --%>
    <div class="bonceContainer bounce-blue" id="sheetRelateHandleRecord" style="width: 900px">
        <div class="bonceHead">
            <span class="name">表格关联的操作记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            本文件表格关联的操作记录如下
            <table class="ty-table">
                <thead>
                <tr>
                    <td>操作</td>
                    <td>表格编号</td>
                    <td>表格名称</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_sheetRelateHandleRecord"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 关联记录 --%>
    <div class="bonceContainer bounce-blue" id="relateRecord" style="width: 900px">
        <div class="bonceHead">
            <span class="name">关联记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <span class="fileSn"></span> <span class="fileName ty-color-blue"></span>的关联记录如下
            <table class="ty-table">
                <thead>
                <tr>
                    <td>操作</td>
                    <td>文件编号</td>
                    <td>文件名称</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_relateRecord"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 所关联的文件 --%>
    <div class="bonceContainer bounce-blue" id="relate" style="width: 700px">
        <div class="bonceHead">
            <span class="name">所关联的文件</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            与<span class="fileSn"></span> <span class="fileName ty-color-blue"></span>相关联的文件如下
            <table class="ty-table">
                <thead>
                <tr>
                    <td>文件编号</td>
                    <td>文件名称</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_relate"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- creator: 张旭博，2022-3-2 8:55:31， 曾关联的文件 --%>
    <div class="bonceContainer bounce-blue" id="related" style="width: 900px">
        <div class="bonceHead">
            <span class="name">曾关联的文件</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            与<span class="fileSn"></span> <span class="fileName ty-color-blue"></span>曾关联过的文件如下
            <table class="ty-table">
                <thead>
                <tr>
                    <td>文件编号</td>
                    <td>文件名称</td>
                    <td>关联的操作</td>
                    <td>解除关联的操作</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_related"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>文件管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container" style="position:relative; ">
                <div id="btn-group" style="width: 1280px;text-align: right">
                    <span class="ty-search">
                        <input class="ty-searchInput" id="fileNameOrSn" type="text" placeholder="文件名称/文件编号">
                        <div class="ty-searchBtn" onclick="searchBtn()"></div>
                    </span>
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 customQuery" id="customQueryBtn" onclick="advanceSearchBtn()">高级搜索</button>
                    <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" type="btn" id="newFoler" onclick="newClass()">新建文件夹</button>
                    <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" type="btn" data-name="fileUpload" id="fileUploadBtn">上传文件</button>
                    <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" type="btn" data-name="folderUpload" id="folderUploadBtn">上传文件夹</button>
                </div>
                <div>
                    <div id="main" class="ty-mainData">
                        <div class="ty-fileContent">
                            <%-- 文件夹列表 --%>
                            <div class="ty-colFileTree setBgHeight" id="listDoc" data-name="main"></div>
                            <%-- 文件列表 --%>
                            <div class="mar">
                                <ul class="ty-secondTab" id="fileSort" style="display: none">
                                    <li class="ty-active">发布时间<span class="sort sortName"><i class="fa fa-long-arrow-down"></i></span></li>
                                    <li type="1">文件编号<span class="sort sortName" style="display: none"><i class="fa fa-long-arrow-down"></i></span></li>
                                    <div role="switch" class="ty-switch" id="switchShowAbolishFile" style="float: right;margin-top: 15px;">
                                        <input type="checkbox" class="ty-switch__input">
                                        <span class="ty-switch__label ty-switch__label--left">
                                            <span aria-hidden="true">隐藏已废止的文件</span>
                                        </span>
                                        <span class="ty-switch__core"></span>
                                        <span class="ty-switch__label ty-switch__label--right">
                                            <span>显示已废止的文件</span>
                                        </span>
                                    </div>
                                </ul>
                                <div class="ty-fileList mainFileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_con"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                        <div class="ty-searchContent" style="display: none">
                            <div class="mar">
                                <div class="text-right" style="margin-bottom: 8px">
                                    <button class="ty-btn ty-btn-blue ty-circle-3" id="rebackBtn" onclick="goBack('query')">返回</button>
                                </div>
                                <ul class="ty-secondTab" id="searchSort" style="display: none; margin-bottom:8px">
                                    <li>发布人 <select name="" id="search_applier"></select></li>
                                    <li>发布时间<span class="sort sortName" style="display: none"><i class="fa fa-long-arrow-down"></i></span></li>
                                </ul>
                                <div class="searchInfo">
                                    <div class="folderContent">
                                        <div class="searchFolder">
                                            <div class="searchFolderContent"></div>
                                            <div id="ye-search-folder"></div>
                                        </div>
                                        <div class="childFolder"></div>
                                    </div>
                                    <div class="fileContent">
                                        <div class="searchFile"></div>
                                        <div id="ye-search-file"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="resHistory" style="display: none">
                        <div class="ty-searchContent">
                            <div class="mar">
                                <div class="text-right" style="margin-bottom: 8px">
                                    <button class="ty-btn ty-btn-blue ty-circle-3" onclick="goBack('record')">返回</button>
                                </div>
                                <div class="fileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_record"></div>
                            </div>
                        </div>
                    </div>
                    <div id="page_sheetRelate" style="display: none">
                        <div class="ty-searchContent">
                            <div class="mar">
                                <div class="ty-alert">
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="goBack('record')">返回</button>
                                    <div class="btn-group text-right" style="flex: auto">
                                        <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="newSheetRelateFile()" id="addNeedRelateSheetBtn">增加需与本文件关联的表格</button>
                                    </div>
                                </div>
                                <div class="tips ty-alert ty-alert-info">系统中，已与本文件关联的表格共<span class="relateNum"></span>个。关联表格的管理，如增加、解除关联及换版，均需您操作。</div>
                                <div class="fileList" >
                                    <%--文件列表--%>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    var isGeneral = generalType === '1' || generalType === '2'
    var isSuper = generalType === '0'
    if(isGeneral){
        $("#fileUploadBtn").html("上传文件");
        $("#fileAbolish .bounce_title").html("文件废止");
        $("#uploadDirect").html("本文件直接发布，无需他人审批");
        $("#fileAbolish_uploadDirect").html("本文件直接废止，无需他人审批");

    }else {
        $("#fileUploadBtn").html("文件发布申请");
        $("#fileAbolish .bounce_title").html("文件废止申请");
        $("#folderUploadBtn").remove()
        $("#uploadDirect").html("本文件直接提交给文管，无需他人审批");
        $("#fileAbolish_uploadDirect").html("本文件直接提交给文管，无需他人审批");
    }
</script>
<script src="../script/resourseCenter/wenjian.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
