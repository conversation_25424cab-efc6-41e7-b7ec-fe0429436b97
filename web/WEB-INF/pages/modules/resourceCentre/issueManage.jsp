<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    #scanInfo{ width:690px; }
    #infoCon>div{ width:315px;margin-left: 10px; }
    .trItem{ padding:5px 15px;   }
    .trItem .ttl{  display: inline-block;  color: #6e7c8a;  }
    .trItem .con{  word-wrap: break-word;  word-spacing:normal;  }
    .trItem>span:nth-child(1){ width:100px;     }
    .link{ color: #0b94ea;  }
    #reason{ display: block; height:50px; border:1px solid #ddd; width: 100%;  }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%-- 查看详情 --%>
    <div class="bonceContainer bounce-blue " id="scanInfo">
        <div class="bonceHead">
            <span id="infoTTL">待审批详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <div id="infoCon">
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件类别：</span><span class="con" id="info_category"></span></div>
                    <div class="trItem"><span class="ttl">文件名称：</span><span class="con" id="info_name"></span></div>
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" id="info_sn"></span></div>
                    <div class="trItem"><span class="ttl">文件类型：</span><span class="con" id="info_vertion"></span></div>
                    <div class="trItem"><span class="ttl">文件大小：</span><span class="con" id="info_size"></span></div>
                    <div class="trItem"><span class="ttl">说明：</span><span class="con" id="info_content"></span></div>
                    <div class="trItem alreadyNo"><span class="ttl">发布驳回时间：</span><span class="con" id="info_noTime"></span></div>
                    <div class="trItem alreadyNo"><span class="ttl">驳回理由：</span><span class="con" id="info_noReason"></span></div>
                    <div class="trItem alreadyOk"><span class="ttl">发布批准时间：</span><span class="con" id="info_okTime"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">发布申请人：</span><span class="con" id="info_creator"></span></div>
                    <div class="trItem"><span class="ttl">发布申请时间：</span><span class="con" id="info_creatTime"></span></div>
                    <div class="trItem"><span class="ttl">制作人：</span><span class="con" id="info_audo"></span></div>
                    <div class="trItem"><span class="ttl">制作时间：</span><span class="con" id="info_audoTime"></span></div>
                    <div class="trItem"><span class="ttl">审核人：</span><span class="con" id="info_charger"></span></div>
                    <div class="trItem"><span class="ttl">审核时间：</span><span class="con" id="info_chargeTime"></span></div>
                    <div class="trItem"><span class="ttl">审批人：</span><span class="con" id="info_char"></span></div>
                    <div class="trItem"><span class="ttl">审批时间：</span><span class="con" id="info_charTime"></span></div>
                </div>
                <div class="clr"></div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-left handle"></span>
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-5 info_charge" onclick="bounce.show($('#noReason'))">驳回</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 info_charge" onclick="bounce.show($('#confirm'))">批准</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 info_scan" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 批准提示 confirm --%>
    <div class="bonceContainer bounce-blue " id="confirm">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
             <p id="confirmTip" style="text-align: center;">您确定批准上传该文件吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="charge(2)">确定</span>
        </div>
    </div>
    <%-- 驳回确认 --%>
    <div class="bonceContainer bounce-red " id="noReason">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
             <p>请填写驳回理由</p>
            <textarea id="reason"></textarea>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="charge(3)">确定</span>
        </div>
    </div>
    <%-- 温馨提示 --%>
    <div class="bonceContainer bounce-blue " id="tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p id="TipCon" style="text-align: center;"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>发布管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <ul class="ty-secondTab">
                    <li class="ty-active" onclick="showSecTab($(this) , 1)">待审批</li>
                    <li class="" onclick="showSecTab($(this) , 2)">已批准</li>
                    <li class="" onclick="showSecTab($(this) , 3)">已驳回</li>
                </ul>
                <div class="ty-mainData">
                    <%--待审批的列表--%>
                    <table class="ty-table ty-table-control" id="tab1">
                        <thead>
                        <td width="15%">发布申请时间</td>
                        <td width="15%">发布申请人</td>
                        <td width="15%">文件编号</td>
                        <td width="15%">文件名称</td>
                        <td width="10%">文件类型</td>
                        <td width="15%">文件大小</td>
                        <td width="15%">详情</td>
                        </thead>
                        <tbody id="tab1Con"></tbody>
                    </table>
                    <%--审批完的列表--%>
                    <table class="ty-table ty-table-control hd" id="tab2">
                        <thead>
                        <td width="15%">发布申请时间</td>
                        <td width="15%">发布申请人</td>
                        <td width="10%">文件编号</td>
                        <td width="15%">文件名称</td>
                        <td width="10%">文件类型</td>
                        <td width="10%">文件大小</td>
                        <td width="15%" id="type">发布批准时间</td>
                        <td width="10%">详情</td>
                        </thead>
                        <tbody id="tab2Con"></tbody>
                    </table>
                    <div id="ye_con"></div>
                </div>
                <div id="ye"></div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/issueManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/downLoadFile/downLoadFile.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工

</script>
</body>
</html>
