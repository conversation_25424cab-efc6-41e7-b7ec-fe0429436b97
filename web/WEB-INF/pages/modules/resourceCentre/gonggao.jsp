<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/docManage.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
     <%--  update--%>
         <%--移动弹窗--%>
     <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 650px">
         <div class="bonceHead">
             <span class="bounce_title">移动到</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <div class="folder_list ty-colFileTree" name="chooseFolder"></div>
         </div>
         <div class="bonceFoot">
             <button class="ty-btn ty-btn-big ty-circle-3" type="btn" onclick="bounce.cancel()">取消</button>
             <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" id="sureChooseFolderBtn" onclick="sureMoverFolder()">下一步</button>
         </div>
     </div>
     <div class="bonceContainer bounce-blue bounce-changeClass">
         <div class="bonceHead">
             <span>修改文件夹名称</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <p>将文件夹【<span class="folderNameChange"></span>】的名称修改为</p>
             <p class="text-center"><input type="text" class="ty-inputText categoryName" placeholder="vbxfg" style="width: 374px;"></p>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeClass()">确定</span>
         </div>
     </div>
     <%-- delete --%>
     <div class="bonceContainer bounce-red bounce-deleteClass">
         <div class="bonceHead">
             <span>删除文件夹</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon text-center">
             <p>确定删除该文件夹？</p>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-red ty-btn-big" onclick="sureDeleteClass()">确定</span>
         </div>
     </div>
     <%-- 新增子级类别 --%>
     <div class="bonceContainer bounce-green bounce-newClass">
         <div class="bonceHead">
             <span>新建子文件夹</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <p>在【<span class="recentParentFolder"></span>】下新建子文件夹</p>
             <p>子文件夹名称</p>
             <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
             <p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。
                <span class="ty-right btnLink" onclick="scanSetBtn('son')">去设置使用权限</span>
             </p>
             <span class="hd" id="havRightUserCon"></span>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewClass()">确定</span>
         </div>
     </div>
     <%-- 全部文件下新增文件夹 --%>
     <div class="bonceContainer bounce-green bounce-newSameClass">
         <div class="bonceHead">
             <span>新建子文件夹</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <p>在【全部文件】下新建子文件夹</p>
             <p>子文件夹名称</p>
             <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
             <p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。
                 <span class="ty-right btnLink" onclick="scanSetBtn('same')">去设置使用权限</span>
             </p>
             <span class="hd" id="havRightUserCon_same"></span>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewSameClass()">确定</span>
         </div>
     </div>
     <%-- 使用权限设置 --%>
     <div class="bonceContainer bounce-blue" id="scanSet">
         <div class="bonceHead">
             <span>使用权限设置</span>
             <a class="bounce_close" onclick="cancelChangeRight()"></a>
         </div>
         <div class="bonceCon ">
             <input type="hidden" id="isNew">
             <p>文件夹名称：<span class="packName">一级文件夹</span></p>
             <p class="">
                 请选择需使用本文件夹的职工<span style="margin-left:100px; " class="btnLink" data-type="allSelect">全选</span>
                <span class="ty-right txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    已选职工：<span class="selectedNum">20</span>位
                    <span class="btnLink" data-type="allClear">全部清空</span>
                </span>
             </p>
             <div class="departTree ">
                 <ul class="ty-left">
                     <p class="txtR"><span class="btnLink" id="changeTypeLeft" onclick="changeType($(this), 'left')">直接展示全部职工</span></p>
                     <div id="allRight"></div>
                 </ul>
                 <div class="ty-left arrow"><i class="fa fa-exchange"></i></div>
                 <form class="ty-left" id="deparCon">
                     <input type="hidden" name="categoryId" id="categoryId">
                     <p class="txtR"><span class="btnLink" id="changeTypeRight" onclick="changeType($(this), 'right')">切换为按部门展示</span></p>
                     <ul id="nowRight"></ul>
                 </form>
                 <div class="clr"></div>
             </div>
             <p>&nbsp;
                 <span class="cancelCon">以下<span id="cancelNum">22</span>位职工刚被取消了本文件夹的使用权限。</span>
                 <span class="ty-right getCon txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    以下<span id="getNum"></span>位职工刚获得了本文件夹的使用权限。
                </span>
             </p>
             <div class="departTree changeCon">
                 <ul class="ty-left cancelCon" style="height:150px;">
                     <div id="cancelRight"></div>
                 </ul>
                 <form class="ty-right getCon" style="margin-right:15px;height:150px;">
                     <ul id="getRight"></ul>
                 </form>
                 <div class="clr"></div>
             </div>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelChangeRight()">取消</span>
             <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeRight()" id="sureChangeRight">确定</button>
         </div>
     </div>
     <%-- 使用权限设置 --%>
     <div class="bonceContainer bounce-blue" id="scanSetLog" style="width: 1100px">
         <div class="bonceHead">
             <span>使用权限操作记录</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon ">
             <div class="searchBar">
                 <div class="searchByTime">
                     当前数据所属时间
                     <select class="ty-inputText" name="scanSetLog_year" id="scanSetLog_year"></select>
                     <select class="ty-inputSelect" name="scanSetLog_month" id="scanSetLog_month">
                         <option value="">---月份---</option>
                         <option value="1">1月</option>
                         <option value="2">2月</option>
                         <option value="3">3月</option>
                         <option value="4">4月</option>
                         <option value="5">5月</option>
                         <option value="6">6月</option>
                         <option value="7">7月</option>
                         <option value="8">8月</option>
                         <option value="9">9月</option>
                         <option value="10">10月</option>
                         <option value="11">11月</option>
                         <option value="12">12月</option>
                     </select>
                 </div>
                 <div class="searchByNameOrSn">
                     精准查找
                     <input class="ty-inputText inputNameOrSn" type="text" placeholder="请输入文件编号、文件名称或文件夹名称" id="scanSetLog_input"><button class="ty-btn ty-btn-big ty-btn-blue" id="searchScanSetLogBtn" onclick="scanSetLogSearch()">确定</button>
                 </div>
             </div>
             <table class="kj-table kj-table-striped">
                 <thead>
                 <tr>
                     <td width="20%">操作对象</td>
                     <td width="8%">对象性质</td>
                     <td width="10%">操作性质</td>
                     <td width="20%">操作结果</td>
                     <td width="20%">操作者</td>
                     <td>当时路径</td>
                 </tr>
                 </thead>
                 <tbody></tbody>
             </table>
             <div id="logYe"></div>
         </div>
         <div class="bonceFoot">
             <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
         </div>
     </div>
     <%-- 文件夹名称修改记录 --%>
     <div class="bonceContainer bounce-green" id="nameEditRecord" style="width: 660px;">
         <div class="bonceHead">
             <span>文件夹名称修改记录</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <h4 class="folderNameSee">作业指导书</h4>
             <div class="clear">
                 <p class="ty-left recordTip"></p>
                 <p class="ty-right recordEditer"></p>
             </div>
             <table class="ty-table ty-table-control historyCon">
                 <thead>
                 <tr>
                     <td>状态</td>
                     <td>文件夹名称</td>
                     <td>创建人/修改人</td>
                 </tr>
                 </thead>
                 <tbody></tbody>
             </table>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
         </div>
     </div>
     <%-- tip --%>
     <div class="bonceContainer bounce-blue " id="tip">
         <div class="bonceHead">
             <span>温馨提示</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <div id="tipMess" style="text-align: center"></div>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">我知道了</span>
         </div>
     </div>
     <div class="bonceContainer bounce-blue" id="bounce_tip">
         <div class="bonceHead">
             <span>提示</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <div class="text-center tipMsg"></div>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn">确定</span>
         </div>
     </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="bounceFixed_tip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg" style="width: 380px;margin: 0 auto;"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="movedScanSet" style="width:700px; ">
        <div class="bonceHead">
            <span>移动后的使用权限</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="moveSetCon">
                <div class="item">
                    本文件夹将移至 <b class="ty-color-blue moveToFolderName"></b> 文件夹下。移动可能导致文件夹使用权限发生变化。</br>
                    点击确定后，将拥有本文件夹使用权限的人员如下表。
                </div>
                <div class="item">
                    您可对下表人员“减员调整”，并可将所减人员再“增员调整”回来。</br>
                    想让下表以外人员拥有使用权限，需到“文件夹管理”中修改上级文件夹的使用权限。
                </div>
                <div class="ty-hr"></div>
                <div class="item">
                    点击本页面上的“确定”后，有本文件夹使用权限者共如下 <b class="ty-color-blue scanUseNum"></b> 人。
                    <span class="ty-color-blue ty-right">减员调整</span>
                </div>
                <div class="roleList addList"></div>
                <div class="ty-hr"></div>
                <div class="item">
                    被“减员调整”的共如下 <b class="ty-color-blue decreaseNum"></b> 人。您还可将某人或全部“增员调整”回去。
                    <span class="ty-color-blue ty-right">增员调整</span>
                </div>
                <div class="roleList delList"></div>
                <div class="ty-hr"></div>
                <div class="item">
                    <b class="ty-color-red">重要提示！</b> 本文件夹下各文件夹与文件移动前的使用权限，本页面上没有表达，移动完成后，更将无法再查看到。故如需要，请在移动前查看清楚或自行保存。
                </div>
                <div class="ty-checkbox">
                    <input type="checkbox" id="iReadMove">
                    <label for="iReadMove"></label>
                    重要提示我已阅读且理解，且权限的调整也已完成！
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureMove">确定</span>
        </div>
    </div>
    <%-- 使用权限设置 - 精确查找 --%>
    <div class="bonceContainer bounce-blue" id="scanSetLog_accurate" style="width: 1100px">
        <div class="bonceHead">
            <span>使用权限操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <div class="searchBar">
                符合查询条件的数据如下：
                <div class="searchByNameOrSn">
                    筛选
                    <select name="" class="showFileOrFolder">
                        <option value="">文件夹与文件都显示</option>
                        <option value="1">只显示文件夹</option>
                        <option value="2">只显示文件</option>
                    </select>
                </div>
            </div>
            <table class="kj-table kj-table-striped">
                <thead>
                <tr>
                    <td width="20%">操作对象</td>
                    <td width="8%">对象性质</td>
                    <td width="10%">操作性质</td>
                    <td width="20%">操作结果</td>
                    <td width="20%">操作者</td>
                    <td>当时路径</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_searchAccurate"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>

    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip2">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess2" style="text-align: center;"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%-- scanSetTip --%>
    <div class="bonceContainer bounce-blue " id="scanSetTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeRight(1)">我知道了</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- scanSetLog --%>
    <div class="bonceContainer bounce-blue " id="scanSetLogScan" style="width:600px; ">
        <div class="bonceHead">
            <span>使用权限操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 550px; margin: 8px auto">
                <div class="log_row">
                    <div class="log_item">
                        <div class="log_item_title">
                            <span class="logCat">文件夹</span>名称：
                        </div>
                        <div class="log_item_content name"></div>
                    </div>
                    <div class="log_item">
                        <div class="log_item_title">操作者：</div>
                        <div class="log_item_content handler"></div>
                    </div>
                </div>
                <div class="log_row">
                    <div class="log_item">
                        <div class="log_item_title">操作性质：</div>
                        <div class="log_item_content operateType"></div>
                    </div>
                    <div class="log_item">
                        <div class="log_item_title">当时路径：</div>
                        <div class="log_item_content path"></div>
                    </div>
                </div>
                <div class="log_row">
                    <div class="log_item_long">
                        <div class="log_item_title">操作结果：</div>
                        <div class="log_item_content result"></div>
                    </div>
                </div>
                <div class="log_row">
                    <table class="kj-table kj-table-striped log_roleList">
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>文件夹管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" style="min-width: 1206px">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container clearfix">
                <div class="ty-fileContent">
                    <%--竖型文件树容器--%>
                    <div class="ty-colFileTree setBgHeight" name="main"></div>
                    <div class="ty-mainData mar">
                        <%--此类别信息--%>
                        <div class="ty-panel">
                            <div class="nowFolder">
                                <div class="headPanel">
                                    <h3>全部文件</h3>
                                    <div class="operableBtn"></div>
                                </div>
                                <div class="docLastIntrol">
                                    <table class="def-table CategoryMessage">
                                        <thead>
                                        <tr>
                                            <td width="25%">包含</td>
                                            <td width="25%">大小</td>
                                            <td width="25%">创建人</td>
                                            <td width="25%">创建时间</td>
                                        </tr>
                                        </thead>
                                        <%--此类别容器--%>
                                        <tbody>
                                        <tr class="generalFolder">
                                            <td></td>
                                            <td>
                                                <button class="link-blue" type="btn" fileType="1" name="fileSize">有效文件</button>
                                                <button class="link-blue" type="btn" fileType="2" name="fileSize">历史文件</button>
                                            </td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right hd" id="" style="margin-top:9px" onclick="newSameClass()">新增同级类别</div>
                            </div>
                            <div class="cCategoryMessage">
                                <p class="generalTip">一级子文件夹共有如下<span></span>个</p>
                                <p class="nomalTip hd">直属于 <span class="normalName"></span> 的子文件夹共有如下 <span></span> 个</p>
                                <table class="def-table addBold">
                                    <thead>
                                    <tr>
                                        <td width="20%">文件夹名称</td>
                                        <td width="20%">包含</td>
                                        <td width="20%">大小</td>
                                        <td width="20%">创建人</td>
                                        <td width="20%">创建时间</td>
                                    </tr>
                                    </thead>
                                    <%--此类别容器--%>
                                    <tbody class="childlFolderInfo"></tbody>
                                </table>
                            </div>
                        </div>
                        <%--子类别信息--%>
                        <div class="ty-panel hd" style="margin-top:130px">
                            <div class="ty-panelHeader childFolder">
                                <h3>子类别</h3>
                            </div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                    <tr>
                                        <td width="60%">类别名称</td>
                                        <td width="20%">创建日期</td>
                                        <td width="20%">修改日期</td>
                                    </tr>
                                </thead>
                                <%--子类别容器--%>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/gonggao.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    if(generalType != 1 && generalType != 2){
        $("#addBtn").hide(); $("#sureChangeRight").hide();
    }
</script>
</body>
</html>
