<%--
  Created by IntelliJ IDEA.
  User: WuYu
  Date: 2021/11/17
  Time: 11:50
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<title>Hello, World! example for mxGraph</title>
<style type="text/css" media="screen">
    div.base {
        position: absolute;
        overflow: hidden;
        font-family: Arial;
        font-size: 8pt;
    }
    div.base#graph {
        border-style: solid;
        border-color: #F2F2F2;
        border-width: 1px;
        background: url('<%=System.getProperty("BaseUrl")%>/assets/mxgraph/images/grid.gif');
    }
</style>

<!-- Sets the basepath for the library if not in same directory -->
<script type='text/javascript'>
    mxBasePath = '<%=System.getProperty("BaseUrl")%>/assets/mxgraph'
    mxImageBasePath = '<%=System.getProperty("BaseUrl")%>/assets/mxgraph/images'
    mxLanguage = 'zh'
    mxDefaultLanguage = 'zh'
    mxLanguages = [ 'zh' , 'en' ]
</script>

<!-- Loads and initializes the library -->
<script type='text/javascript' src='<%=System.getProperty("BaseUrl")%>/assets/mxgraph/js/mxClient.js'></script>
<script type='text/javascript' src='<%=System.getProperty("BaseUrl")%>/assets/mxgraph/js/app.js'></script>

<!-- Example code -->
<script type='text/javascript'>
    mxGraph.prototype.htmlLabels = true
    mxGraph.prototype.isWrapping = function(cell) {
        return true
    }
    mxConstants.DEFAULT_HOTSPOT = 1
    // Enables guides
    mxGraphHandler.prototype.guidesEnabled = true
    // Alt disables guides
    mxGuide.prototype.isEnabledForEvent = function(evt) {
        return !mxEvent.isAltDown(evt)
    }
    // Enables snapping waypoints to terminals
    mxEdgeHandler.prototype.snapToTerminals = true

    window.onbeforeunload = function() { return mxResources.get('changesLost') }
</script>
</head>
<body class='page-header-fixed page-sidebar-closed-hide-logo page-content-white' onload="createEditor(mxBasePath+'config/workfloweditor.xml');">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>mxGraph Workflow Example</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container" onload="$(this).height($(window).height()-80)" style="width: 100%; height: 100%; min-height: 1000px" >
                <table id="splash" width="100%" height="100%"
                       style="background:white;position:absolute;top:0px;left:0px;z-index:4;">
                    <tr>
                        <td align="center" valign="middle">
                            <img src="images/loading.gif">
                        </td>
                    </tr>
                </table>
                <div id="graph" class="base">
                    <!-- Graph Here -->
                </div>
                <div id="status" class="base" align="right" style="white-space:nowrap;">
                    <!-- Status Here -->
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
</body>
</html>