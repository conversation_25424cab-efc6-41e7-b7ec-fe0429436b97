<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2017/2/22
  Time: 11:28
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
    <title>Title</title>
</head>
<body>
<input type="text" name="testValue" value="<%= request.getAttribute("testValue") %>">
<button onclick="ajaxSubmit();" >ajaxSubmit</button>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/jquery.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script type="text/javascript">
function ajaxSubmit() {
    $.ajax({
        url: "<%=System.getProperty("BaseUrl")%>/test/testJspOrJson",
        type: "post",
        dataType: "json",
        success: function (data) {
            console.log('data.testValue', data.testValue)
            $("input").val(data.testValue)
        }
    })
}
</script>
</body>
</html>
