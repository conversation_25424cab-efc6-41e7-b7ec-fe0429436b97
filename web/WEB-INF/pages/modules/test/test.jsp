<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2017/2/22
  Time: 11:28
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
    <title>Title</title>
</head>
<body>
<a href="${pageContext.request.contextPath}/upload/template/客户模板.xls?v=SVN_REVISION" value="">下载客户导入模板</a>
<a href="${pageContext.request.contextPath}/export/exportAllCustomer.do" value="">导出客户</a>
<form action="${pageContext.request.contextPath}/export/importCustomer.do" method="post" enctype="multipart/form-data">
    <input type="file" name="file">
    <input type="submit" value="导入客户信息">
</form>


<a href="${pageContext.request.contextPath}/upload/template/商品模板.xls?v=SVN_REVISION" value="">下载商品导入模板</a>
<a href="${pageContext.request.contextPath}/export/expertAllProduct.do" value="">导出商品</a>
<form action="${pageContext.request.contextPath}/export/importProduct.do" method="post" enctype="multipart/form-data">
    <input type="file" name="file">
    <input type="submit" value="导入商品信息">
</form>


<a href="${pageContext.request.contextPath}/upload/template/用户模板.xls?v=SVN_REVISION" value="">下载用户导入模板</a>
<a href="${pageContext.request.contextPath}/export/exportAllUser.do" value="">导出用户</a>
<form action="${pageContext.request.contextPath}/export/importUser.do" method="post" enctype="multipart/form-data">
    <input type="file" name="file">
    <input type="submit" value="导入用户信息">
</form>

</body>
</html>
