<%--
  Created by IntelliJ IDEA.
  User: heb20
  Date: 2023/5/10
  Time: 8:26
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/processmanagement/processSettings.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed3">
    <%-- 复用提示弹窗 --%>
    <div class="bonceContainer bounce-blue bigun" id="aganstart">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon answers">
            <div class="link-on">确定恢复使用此类别吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="madehesure()" id="hepuped">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 被停用的类别弹窗--%>
    <div class="bonceContainer bounce-blue bigun tbonal" id="deatatcares">
        <div class="bonceHead">
            <span>被停用的类别</span>
            <a class="bounce_close coseheen" onclick="closhepe()"></a>
        </div>
        <div class="bonceCon">
            <p class="plances">“工序”的类别中，已被停用的如下：</p>
            <div class="pageStyle container_item">
                <div class="initBody bnck">
                    <table class="coldor ty-table ty-table-control">
                        <thead class="coldor">
                        <tr>
                            <td>类别名称</td>
                            <td>本次停用</td>
                            <td>创建记录</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody class="ty-table" id="stopthinuse">
                        <tr>
                            <td>XXXXXXX</td>
                            <td>XXX XXXX-XX-XX XX：XX：XX</td>
                            <td>XXX XXXX-XX-XX XX：XX：XX</td>
                            <td>
                                <span class="thin-btn promk" data-name="againtry" onclick="againtry()">复用</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5 coseheen" onclick="closhepe()" id="coseheen">关闭</span>
        </div>
    </div>
    <%-- 停用提示弹窗 --%>
    <div class="bonceContainer bounce-blue bigun" id="stopstart">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon answers">
            <div class="link-on">确定停用此类别吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="stopsure()" id="letmakesure">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--类别管理弹窗--%>
    <div class="bonceContainer bounce-blue bigun tbonal" id="caeoagntopup">
        <div class="bonceHead">
            <span>类别管理</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="companyDetails opacel">
                <div class="panel-box">
                    <div>选择工序、子工序或操作的选项时，通过类别进行筛选可提高效率！</div>
                    <div class="ty-alert nuame">
                        <div class="com_address">“工序”的类别中，在用的如下：</div>
                        <div>
                            <span class="thin-btn stopcat"  onclick="stopcaten()">被停用的类别</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="pageStyle container_item">
                <div class="initBody bnck">
                    <table class="coldor ty-table ty-table-control">
                        <thead class="coldor">
                        <tr>
                            <td>类别名称</td>
                            <td>创建</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody class="ty-table" id="catlanks">
                        <tr>
                            <td>XXXXXXX</td>
                            <td>XXX XXXX-XX-XX XX：XX：XX</td>
                            <td>
                                <span class="thin-btn promk" data-name="stoptry" onclick="stoptry()">停用</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cloosecat()" id="cloosecat">关闭</span>
        </div>
    </div>
    <%-- 新增类别--%>
    <div class="bonceContainer bounce-blue bigun tbonal" id="addcaten" style="width: 600px;">
        <div class="bonceHead">
            <span>新增类别</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="margin: 49px 82px 20px;">
            <div class="flex-box">
                <div class="citem">
                    <p>类别名称<i class="red">*</i> </p>
                    <input type="text" placeholder="请录入" class="cNum puchnub" id="catnamebe">
                </div>
                <div class="ty-color-blue" style="margin-top: 20px;">注：选择选项时，先按类别筛选，查找更容易！</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="clonsen()" id="detenq">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addcatsune()" id="takesure">确定</span>
        </div>
    </div>
    <%-- 周期设置--%>
    <div class="bonceContainer bounce-blue bigun tbonal" id="weekprosene" style="width: 1036px;">
        <div class="bonceHead">
            <span>周期设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="margin: 40px 74px;">
            <div class="flex-box answers">
                <div class="citem">
                    <p>请确定周期的型式<i class="red">*</i></p>
                    <select id="chosetimeen" value="0" onchange="chnencont()" class="puchnub" style="width: 872px;" autocomplete="off">
                        <option value="0" selected="selected">请选择</option>
                        <option value="1">一定时间内需进行一次或数次</option>
                        <option value="2" disabled style="background-color: #C0C0C0;">一定次数的操作后需进行一次或数次</option>
                        <option value="3" disabled style="background-color: #C0C0C0;">一个或几个班次后需进行一次或数次</option>
                    </select>
                </div>
            </div>
            <div class="flex-box answers dyhide" id="upchose">
                <div class="citem">
                    <p>请确定多长时间需进行多少次本操作<i class="red">*</i></p>
                    <div>
                        <input type="text" placeholder="请录入数字" class="cNum" id="numtime">
                        <select value="0" onchange="" class="puchnub" id="timeplay" style="width: 364px;">
                            <option value="0" disabled selected style="display:none;">请选择时间单位</option>
                            <option value="1">日</option>
                            <option value="2">时</option>
                            <option value="3">分</option>
                        </select>
                        <span>需进行</span>
                        <input type="text" placeholder="请录入数字" class="cNum" id="pontnumb">
                        <span>次本操作</span>
                    </div>
                </div>
            </div>
            <div class="flex-box answers dyhide" id="midchose">
                <div class="citem">
                    <p>请确定多少次什么操作后需进行多少次本操作</p>
                    <div>
                        <input type="text" placeholder="请录入次数" class="cNum">
                        <select value="0" onchange="" class="puchnub" placeholder="请选择操作">
                            <option value="0"></option>
                        </select>
                        <span>需进行</span>
                        <input type="text" placeholder="请录入数字" class="cNum">
                        <span>次本操作</span>
                    </div>
                </div>
            </div>
            <div class="flex-box answers dyhide" id="thirchose">
                <div class="citem">
                    <p>请确定多少个班次后需进行多少次本操作</p>
                    <div>
                        <input type="text" placeholder="请录入数字" class="cNum">
                        <span>个班次后需进行</span>
                        <input type="text" placeholder="请录入数字" class="cNum">
                        <span>次本操作</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cloaddcat(4)">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="madentence" onclick="madensure()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--删除确认提示 --%>
    <div class="bonceContainer bounce-red bigun" id="detenalsten">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="answers" class="answers"></div>
        </div>
        <div class="bonceFoot" id="buntflower">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" id="detalenel" onclick="detenter(1)">确定</span>
        </div>
    </div>
    <%-- 创建“工序”的选项--%>
    <div class="bonceContainer bounce-blue bigun" id="craercopions" style="width: 617px;">
        <div class="bonceHead">
            <span>创建“工序”的选项</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="margin: 40px 74px;">
            <div class="flex-box">
                <div class="citem">
                    <p>代号<i class="red">*</i> </p>
                    <input type="text" placeholder="请录入" class="cNum puchnub" id="cnnumb">
                </div>
            </div>
            <div class="flex-box answers">
                <div class="citem">
                    <p>名称<i class="red">*</i> </p>
                    <input type="text" placeholder="请录入" class="cNa puchnub" id="cnname" >
                </div>
            </div>
            <div class="flex-box answers">
                <div class="citem">
                    <p>
                        类别
                        <span class="linkBtn ty-right addcat" onclick="addcaten(1)">新增类别</span>
                        <span class="linkBtn ty-right addcat" onclick="caegryamnt(1)">类别管理</span>
                    </p>
                    <select value="0" class="puchnub" id="canental">
                        <option value="0">待分类</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cloaddcat(1)">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addpresure(1)" id="addcatsune">确定</span>
        </div>
    </div>
    <%-- 创建“子工序”的选项--%>
    <div class="bonceContainer bounce-blue bigun" id="craeropions2" style="width: 617px;">
        <div class="bonceHead">
            <span>创建“子工序”的选项</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="margin: 40px 74px;">
            <div class="flex-box">
                <div class="citem">
                    <p>代号<i class="red">*</i> </p>
                    <input type="text" placeholder="请录入" class="cNum puchnub" id="cnnumb2">
                </div>
            </div>
            <div class="flex-box answers">
                <div class="citem">
                    <p>名称<i class="red">*</i> </p>
                    <input type="text" placeholder="请录入" class="cNa puchnub" id="cnname2">
                </div>
            </div>
            <div class="flex-box answers">
                <div class="citem">
                    <p>
                        类别
                        <span class="linkBtn ty-right addcat" onclick="addcaten(2)">新增类别</span>
                        <span class="linkBtn ty-right addcat" onclick="caegryamnt(2)">类别管理</span>
                    </p>
                    <select value="0" onchange="" class="puchnub" id="canental2">
                        <option value="0">待分类</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cloaddcat(2)">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addpresure(2)" id="addchidcatsune">确定</span>
        </div>
    </div>
    <%-- 创建“操作”的选项 --%>
    <div class="bonceContainer bounce-blue bigun" id="craeropions3" style="width: 617px;">
        <div class="bonceHead">
            <span>创建“操作”的选项</span>
            <a class="bounce_close" onclick="cloaddcat(3)"></a>
        </div>
        <div class="bonceCon" style="margin: 40px 74px;">
            <div class="flex-box">
                <div class="citem">
                    <p>代号<i class="red">*</i> </p>
                    <input type="text" placeholder="请录入" class="cNum puchnub" id="cnnumb3">
                </div>
            </div>
            <div class="flex-box answers">
                <div class="citem">
                    <p>名称<i class="red">*</i> </p>
                    <input type="text" placeholder="请录入" class="cNa puchnub" id="cnname3">
                </div>
            </div>
            <div class="flex-box answers">
                <div class="citem">
                    <p>操作的时间点<i class="red">*</i> </p>
                    <select value="0" onchange="" class="puchnub" id="catenthin">
                        <option value="0">请选择</option>
                        <option value="1">正式操作之前的准备工作</option>
                        <option value="2">正式操作</option>
                        <option value="3">正式操作之后的收尾工作</option>
                    </select>
                </div>
            </div>
            <div class="flex-box answers">
                <div class="citem">
                    <p>
                        类别
                        <span class="linkBtn ty-right addcat" onclick="addcaten(3)">新增类别</span>
                        <span class="linkBtn ty-right addcat" onclick="caegryamnt(3)">类别管理</span>
                    </p>
                    <select value="0" onchange="" class="puchnub" id="canental3">
                        <option value="0">待分类</option>
                    </select>
                </div>
            </div>
            <div class="flex-box answers">
                <div class="citem">
                    <p>是否为周期性的“操作”？<i class="red">*</i> </p>
                    <input type="hidden" id="weektry">
                    <span onclick="weekty(1,$(this))" class="radioCon" type="radio" >
                        <i id="weekund1" class="fa fa-circle-o"></i>不是
                    </span>
                    <span onclick="weekty(2,$(this))" class="radioCon" type="radio" style="margin-left: 198px;">
                        <i id="weekund2" class="fa fa-circle-o"></i>是
                    </span>
                    <span class="linkBtn ty-right addcat addount" id="weekermake" onclick="makeweekpus()">编辑周期</span>
                </div>
            </div>
            <p style="margin-top: 10px;" class="dyhide" id="onea">每<span class="timedy">XX</span>时间单位需进行<span class="pontnumb">XX</span>次本操作</p>
            <p id="twob" class="dyhide">每<span>XX</span>次<span>XX</span>操作后需进行<span>XX</span>次本操作</p>
            <p id="threec" class="dyhide">每<span>XX</span>个班次后需进行<span>XX</span>次本操作</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cloaddcat(3)">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addpresure(3)" id="addpresure">确定</span>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>工序设置</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <%--首页(a页)--%>
                <div class="companyDetails" id="homeset">
                    <h1 class="hd"></h1>
                    <div class="panel-box">
                        <p>工序的材料配置、控制点管理、文件配置等各项，均需“工序”设置完成后方可进行！</p>
                    </div>
                    <div class="panel-box">
                        <p>各道“工序”设置时，需在已有选项中选择。建议提前设置选项。</p>
                        <div class="ty-alert">
                            <div class="com_address">点击“选项管理”，即可对选项进行设置与管理！</div>
                            <div>
                                <span class="thin-btn" onclick="gotenchose()">选项管理</span>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="ty-alert">
                            <div class="com_address">
                                <span>产品的制造/装配工序</span>
                                <span class="wrtdboad">应设置<span class="yses1">XX</span>条，已设置<span class="edsess1">XX</span>条</span>
                            </div>
                            <div>
                                <span class="thin-btn promk" data-name="promdprce" onclick="goensusses(1,$(this))">去管理</span>
                            </div>
                        </div>
                        <div class="ty-alert">
                            <div class="com_address">
                                <span>产品的包装工序</span>
                                <span class="wrtdboad2">应设置<span class="yses2">XX</span>条，已设置<span class="edsess2">XX</span>条</span>
                            </div>
                            <div>
                                <span class="thin-btn-hid">去管理</span>
                            </div>
                        </div>
                        <div class="ty-alert">
                            <div class="com_address">
                                <span>零组件的制造/装配工序</span>
                                <span class="wrtdboad3">应设置<span class="yses3">XX</span>条，已设置<span class="edsess3">XX</span>条</span>
                            </div>
                            <div>
                                <span class="thin-btn promk" data-name="spareparts" onclick="gospartsess(2,$(this))">去管理</span>
                            </div>
                        </div>
                        <div class="ty-alert">
                            <div class="com_address">
                                <span>零组件的包装工序</span>
                                <span class="wrtdboad4">应设置<span class="yses4">XX</span>条，已设置<span class="edsess4">XX</span>条</span>
                            </div>
                            <div>
                                <span class="thin-btn-hid">去管理</span>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="ty-alert">
                            <div class="com_address">
                                <div>
                                    <span>设置工序时，是否需设置“子工序”？</span>
                                    <span class="wrtbdoad5">XXX</span>
                                </div>
                                <div class="ty-color-blue">注：上述状态如与贵公司需求不符，请点击“改变状态”。</div>
                            </div>
                            <div>
                                <span class="thin-btn-hid">改变状态</span>
                            </div>
                        </div>
                    </div>
                </div>

                <%--点击‘去管理’跳转页面(b页)1--%>
                <div class="ty-container glmang" id="tomanage">
                    <button type="button" class="btn btn-default bnck bnck-bon" data-name="tenbck" id="backhome">返回</button>
                    <div class="pageStyle container_item" style="margin-top: 20px;">
                        <div class="initBody bnck">
                            <table class="coldor ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>图号（代号）/名称/规格/型号/计量单位</td>
                                    <td>单重</td>
                                    <td>工序</td>
                                    <td>子工序</td>
                                    <td>操作</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody class="ty-table" id="tdonalty">
                                <tr>
                                    <td>XXXXXXXX/XXXXX/XXXXXX/XXXXXX/XX</td>
                                    <td>XXXX</td>
                                    <td>已设置XX个</td>
                                    <td>已设置XX个</td>
                                    <td>已设置XX项</td>
                                    <td>
                                        <span class="ty-color-blue" onclick="">查看</span>
                                        <span class="ty-color-blue" onclick="getsetunup()">设置</span>
                                        <span class="ty-color-blue" onclick="">操作日志</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div id="yetoman"></div>
                        </div>
                    </div>
                </div>


                <%--点击‘设置’展示‘查看设置’页面(c页)2--%>
                <div class="ty-container glmang" id="tolooksetup" style="margin: 0px 96px;">
                    <div class="bnck" style="margin-left: 39px;margin-top: 33px;margin-right: 0px;">
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more bnck-more1" id="backstrthme" onclick="comesthom(1)">返回首页</button>
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more2 gbcklast" id="backlastply" onclick="comelastpy(1)">返回上一页</button>
                        <div class="pull-right">
                            <input type="hidden" class="setchoeprocs">
                            <span onclick="setchoeprocs(1,$(this))" class="radioCon circlee needSelect" type="radio"
                                  name="getbont">
                                    <i id="upsetHangAccount1" class="fa fa-circle-o"></i> 本产品已设置工序<span class="onderal">XX</span>个，设置完成！
                                </span>
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="makdsure()" id="madesure">确定</button>
                        </div>
                    </div>
                    <div class="companyDetails opacel" style="margin-left: 0px;width: 100%;">
                        <div class="panel-box messg1" style="margin-left: 39px;">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <div>
                                        <span id="alone">XXXXXXXX/XXXXX/XXXXXX/XXXXXXXXXXXXXXXXX/XX/单重XXX</span>
                                        <span id="claenr">XXXXXXXX/XXXXX/XXXXXX/XXXXXXXXXXXXXXXXX/XX/净重XXX</span>
                                    </div>
                                    <div class="ty-color-blue">注：请根据实际情况，选择本产品的各道工序！</div>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">选择“工序套餐”</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="choneproec" onclick="choneprod()">选择一道“工序”</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box messg2" style="margin-left: 39px;">
                            <%--                            <div class="ty-alert toptble"></div>--%>
                            <div class="ty-alert chiden">
                                <div class="ty-alert chidtble">
                                    <div class="com_address childcaten">
                                        <div>
                                            <span id="chiden1">XX工序XXXXX代号XXXXXXXX/XXX工序名称XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</span>
                                        </div>
                                        <div class="ty-color-blue">注：请根据实际情况，选择本工序下的各项操作！</div>
                                    </div>
                                    <div class="link-ed">
                                        <span class="thin-btn promk" onclick="getprolink(4)">排序</span>
                                        <span class="promk thin-btn-tne2" onclick="">删除</span>
                                    </div>
                                    <div class="pull-right">
                                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">选择“操作套餐“</button>
                                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 chonechen" onclick="chonechen($(this))">选择一项”操作“</button>
                                    </div>
                                </div>
                                <%--<div class="pageStyle container_item" id="tboneable"></div>--%>
                            </div>
                        </div>
                    </div>
                </div>
                <%--展示‘选择一道工序'列表(d页)3--%>
                <div class="ty-container glmang" id="choseseten">
                    <div class="bnck">
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more bnck-more1" onclick="comesthom(2)">返回首页</button>
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more2 gbcklast" onclick="comelastpy(2)">返回上一页</button>
                        <div class="pull-right">
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addchones(1,2)">创建选项</button>
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="choseover()" id="choseover">选择完毕</button>
                        </div>
                    </div>
                    <div class="companyDetailsote opacel">
                        <div class="panel-box">
                            <div class="lank-up" style="align-items: center;display: flex;flex-direction: row;justify-content: space-between;">
                                <div class="com_address">
                                    <span>选择一个选项，并确认该工序是否带有子工序后，点击右上角的“选择完毕”。</span>
                                </div>
                                <div class="pull-right">
                                    <span class="plancode dgrap">筛选</span>
                                    <span class="second dgrap">
<%--                                        <input type="text" name="makeupCo2" id="makeupCo2" class="makeinp dyshow" onfocus="setfocus2(this)"--%>
<%--                                               oninput="setinput2(this)" placeholder="请选择或输入" style="width: 420px;"  list="typenum2" onchange="chonsepund($(this))"/>--%>
<%--                                        <datalist name="makeupCoSe2" id="typenum2" onchange="changeF2(this)" size="10" value="0"--%>
<%--                                                  class="sel-rund sental" style="-moz-appearance:button;width: 420px;">--%>
<%--                                            <option>选项1</option>--%>
<%--                                            <option>选项2</option>--%>
<%--                                            <option>选项3</option>--%>
<%--                                            <option>选项4</option>--%>
<%--                                            <option>选项5</option>--%>
<%--                                            <option>选项6</option>--%>
<%--                                             <option>4356</option>--%>
<%--                                        </datalist>--%>
                                        <select id="typenum2" onchange="chonsepund($(this))" class="sel-rund">
                                        </select>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pageStyle container_item">
                        <div class="initBody bnck">
                            <table class="coldor ty-table ty-table-control">
                                <thead class="coldor">
                                <tr>
                                    <td>选择</td>
                                    <td>工序代号</td>
                                    <td>工序名称</td>
                                    <td>本工序下是否有子工序</td>
                                    <td>创建</td>
                                </tr>
                                </thead>
                                <tbody class="ty-table" id="chonsepen">
                                <tr>
                                    <td class="chonice_item"><input id='allSelected' type="checkbox"></td>
                                    <td>XXXXXXXXXXXXX</td>
                                    <td>XXXXXXXXXXXXX</td>
                                    <td>
                                        <input type="hidden" id="hadyno">
                                        <span onclick="hadyno(1,$(this))" class="radioCon" type="radio" id="yechosen">
                                            <i id="yechidset1" class="fa fa-circle-o"></i>有
                                        </span>
                                        <span onclick="hadyno(2,$(this))" class="radioCon" type="radio" id="nochosen">
                                            <i id="yechidset2" class="fa fa-circle-o"></i>没有
                                        </span>
                                    </td>
                                    <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <%-- 选择工序排序列表(f页)4 --%>
                <div class="ty-container glmang" id="presslisttune">
                    <div class="dnck">
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more bnck-more1" onclick="comesthom(3)">返回首页</button>
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more2 gbcklast" onclick="comelastpy(3)">返回上一页</button>
                        <div class="pull-right pxlistoa">
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="sortsure(1,$(this))" id="sortsure">确定</button>
                        </div>
                    </div>
                    <div class="companyDetails opacel">
                        <div class="panel-box paenbox">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span class="nameer">工序代号XXXXXXXX/工序名称XXXXXXXXXXXXXXXXXXX</span>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box paenbox">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <div class="dgrap">要把本工序排到哪个工序后面？请选择</div>
                                    <div class="dgrap bemar">
                                        <input  type="hidden" id="checkenpace">
                                        <span onclick="tblechocpen(2,$(this))" class="radioCon" type="radio" name="chose">
                                            <i id="setbefencefnt1" class="fa fa-circle-o"></i>排到最前面
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pageStyle container_item">
                        <div class="initBody bnck">
                            <table class="coldor ty-table ty-table-control">
                                <thead class="coldor">
                                <tr>
                                    <td>选择</td>
                                    <td class="gxcheng1">工序代号</td>
                                    <td class="gxcheng2">工序名称</td>
                                </tr>
                                </thead>
                                <tbody class="ty-table" id="newsortlnk">
                                <%--                                <tr>--%>
                                <%--                                    <td>--%>
                                <%--                                        <input  type="hidden" id="tblechocpen">--%>
                                <%--                                        <span onclick="tblechocpen(1,$(this))" class="radioCon" type="radio">--%>
                                <%--                                            <i id="uptbelcopenq1" class="fa fa-circle-o"></i>--%>
                                <%--                                        </span>--%>
                                <%--                                    </td>--%>
                                <%--                                    <td>XXXXXXXXXXXXXXXX</td>--%>
                                <%--                                    <td>XXXXXXXXXXXXXXXX</td>--%>
                                <%--                                </tr>--%>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <%-- 选择操作排序列表(m页)5--%>
                <div class="ty-container glmang" id="ctenlistlink">
                    <div class="dnck">
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more bnck-more1" onclick="comesthom(9)">返回首页</button>
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more2 gbcklast" onclick="comelastpy(7)">返回上一页</button>
                        <div class="pull-right" id="madesune">
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-btn-blue-circle-3" onclick="operatesort(1)" id="operatesort">确定</button>
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-btn-blue-circle-3" onclick="operatesort(2)" id="operatesort2">确定</button>
                        </div>
                    </div>
                    <div class="companyDetails opacel">
                        <div class="panel-box paenbox">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span class="nameer">工序代号XXXXXXXX/工序名称XXXXXXXXXXXXXXXXXXX</span>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box paenbox">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <div class="dgrap">要把本操作排到哪项操作后面？请选择</div>
                                    <div class="dgrap bemar">
                                        <input type="hidden" id="checkupone">
                                        <%--                                        <span onclick="setbeforecz(1,$(this))" class="radioCon" type="radio">--%>
                                        <%--                                            <i id="setbeforecz1" class="fa fa-circle-o"></i>排到最前面--%>
                                        <%--                                        </span>--%>
                                        <span onclick="setonble(2,$(this))" class="radioCon" type="radio">
                                            <i id="setbeforecz1" class="fa fa-circle-o"></i>排到最前面
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pageStyle container_item">
                        <div class="initBody bnck">
                            <table class="coldor ty-table ty-table-control">
                                <thead class="coldor">
                                <tr>
                                    <td>选择</td>
                                    <td>操作人员需进行的操作</td>
                                    <td>所属类别</td>
                                    <td>是否为周期性操作</td>
                                </tr>
                                </thead>
                                <tbody class="ty-table" id="catsortlist">
                                <tr>
                                    <td>
                                        <input type="hidden" id="tbalecatch">
                                        <span onclick="" class="radioCon" type="radio">
                                            <i id="setconfents1" class="fa fa-circle-o"></i>
                                        </span>
                                    </td>
                                    <td>XXXXXXXXXXXXXXXX</td>
                                    <td>XXX</td>
                                    <td>XXX</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <%-- 展示‘准备工作’页面列表(e页)6--%>
                <div class="ty-container glmang" id="wekupworker" style="margin: 20px 20px 20px 20px;">
                    <div class="bnck" style="margin-left: 67px;margin-right: 67px;">
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more bnck-more1" onclick="comesthom(4)">返回首页</button>
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more2 gbcklast" onclick="comelastpy(4)">返回上一页</button>
                        <div class="pull-right">
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="addchose" onclick="addchones(3,2)">创建选项</button>
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 choseoxer" onclick="chonceover(1)">选择完毕</button>
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 choseoxer3" onclick="chonceover(3)">选择完毕</button>
                        </div>
                    </div>
                    <div class="companyDetails opacel" style="margin-top: 20px;margin-left: 67px;">
                        <div class="panel-box paenbox">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span>请选择一个选项，之后点击右上角的“选择完毕”。</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="ty-secondTab bnck" style="margin-left: 67px;margin-right: 67px;" id="clenket">
                        <li class="ty-active" data-state="0">准备工作</li>
                        <li data-state="1">正式操作</li>
                        <li data-state="2">收尾工作</li>
                    </ul>
                    <div class="clr"></div>
                    <br>
                    <div class="ty-mainData preparation" style="margin-top: 0px;">
                        <div class="mainCon">
                            <div>
                                <div></div>
                                <div style="float: right;margin-right: 67px;margin-bottom: 24px;">
                                    <span class="plancode dgrap">筛选</span>
                                    <span class="second dgrap upbox">
                                        <select id="typenum3" onchange="chosepunchid($(this))" class="sel-rund"></select>
<%--                                        <input type="text" name="makeupCo3" id="makeupCo3" class="makeinp dyshow" onfocus="setfocus3(this)"--%>
<%--                                               oninput="setinput3(this)" placeholder="请选择或输入" style="width: 420px;"/>--%>
<%--                                        <select name="makeupCoSe3" id="typenum3" onchange="changeF3(this)" size="10" value="0"--%>
<%--                                                class="sel-rund sental" style="-moz-appearance:button;width: 420px;">--%>
<%--                                            <option value="0">准备工作类别1</option>--%>
<%--                                            <option value="1">准备工作类别2</option>--%>
<%--                                            <option value="2">准备工作类别3</option>--%>
<%--                                            <option value="3">准备工作类别4</option>--%>
<%--                                            <option value="4">准备工作类别5</option>--%>
<%--                                            <option value="5">准备工作类别6</option>--%>
<%--                                            <option value="6">准备工作类别7</option>--%>
<%--                                        </select>--%>
                                    </span>
                                </div>
                            </div>
                            <div class="pageStyle container_item">
                                <div class="initBody bnck" style="margin: 0 67px;">
                                    <table class="coldor ty-table ty-table-control">
                                        <thead class="coldor">
                                        <tr>
                                            <td>选择</td>
                                            <td>操作内容</td>
                                            <td>所属类别</td>
                                            <td>是否为周期性操作</td>
                                            <td>创建</td>
                                        </tr>
                                        </thead>
                                        <tbody class="ty-table" id="laiskstart">
                                        <tr>
                                            <td><input id='allSelected2' type="checkbox"></td>
                                            <td>XXXXXXXXXXXX</td>
                                            <td>XXXXXXXXXXXXX</td>
                                            <td>XX</td>
                                            <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div id="ye_alllank"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ty-mainData formalwork" style="margin-top: 0px;">
                        <div class="mainCon">
                            <div>
                                <div></div>
                                <div style="float: right;margin-right: 67px;margin-bottom: 24px;">
                                    <span class="plancode dgrap">筛选</span>
                                    <span class="second dgrap upbox">
                                        <select id="typenum4" onchange="chosepunem($(this))" class="sel-rund"></select>
<%--                                       <input type="text" name="makeupCo4" id="makeupCo4" class="makeinp dyshow" onfocus="setfocus4(this)"--%>
<%--                                              oninput="setinput4(this)" placeholder="请录入或输入" style="width: 420px;"/>--%>
<%--                                        <select name="makeupCoSe4" id="typenum4" onchange="changeF4(this)" size="10" value="0"--%>
<%--                                                class="sel-rund sental" style="-moz-appearance:button;width: 420px;">--%>
<%--                                            <option value="0">正在工作类别1</option>--%>
<%--                                            <option value="1">正在工作类别2</option>--%>
<%--                                            <option value="2">正在工作类别3</option>--%>
<%--                                            <option value="3">正在工作类别4</option>--%>
<%--                                            <option value="4">正在工作类别5</option>--%>
<%--                                        </select>--%>
                                    </span>
                                </div>
                            </div>
                            <div class="pageStyle container_item">
                                <div class="initBody bnck" style="margin: 0 67px;">
                                    <table class="coldor ty-table ty-table-control">
                                        <thead class="coldor">
                                        <tr>
                                            <td>选择</td>
                                            <td>操作内容</td>
                                            <td>所属类别</td>
                                            <td>是否为周期性操作</td>
                                            <td>创建</td>
                                        </tr>
                                        </thead>
                                        <tbody class="ty-table" id="laiskstart2">
                                        <tr>
                                            <td><input id="allSelected4" type="checkbox"> </td>
                                            <td>XXXXXXXXXXXX</td>
                                            <td>XXXXXXXXXXXXX</td>
                                            <td>XX</td>
                                            <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div id="ye_allank2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ty-mainData closingwork" style="margin-top: 0px;">
                        <div class="mainCon">
                            <div>
                                <div></div>
                                <div style="float: right;margin-right: 67px;margin-bottom: 24px;">
                                    <span class="plancode dgrap">筛选</span>
                                    <span class="second dgrap upbox">
                                        <select id="typenum5" onchange="chosepufive($(this))" class="sel-rund"></select>
<%--                                        <input type="text" name="makeupCo5" id="makeupCo5" class="makeinp dyshow" onfocus="setfocus5(this)"--%>
<%--                                               oninput="setinput5(this)" placeholder="请选择或输入" style="width: 420px;"/>--%>
<%--                                        <select name="makeupCoSe5" id="typenum5" onchange="changeF5(this)" size="10" value="0"--%>
<%--                                                class="sel-rund sental" style="-moz-appearance:button;width: 420px;">--%>
<%--                                            <option value="0">收尾工作类别1</option>--%>
<%--                                            <option value="1">收尾工作类别2</option>--%>
<%--                                            <option value="2">收尾工作类别3</option>--%>
<%--                                            <option value="3">收尾工作类别4</option>--%>
<%--                                            <option value="4">收尾工作类别5</option>--%>
<%--                                        </select>--%>
                                    </span>
                                </div>
                            </div>
                            <div class="pageStyle container_item">
                                <div class="initBody bnck" style="margin: 0 67px;">
                                    <table class="coldor ty-table ty-table-control">
                                        <thead class="coldor">
                                        <tr>
                                            <td>选择</td>
                                            <td>操作内容</td>
                                            <td>所属类别</td>
                                            <td>是否为周期性操作</td>
                                            <td>创建</td>
                                        </tr>
                                        </thead>
                                        <tbody class="ty-table" id="laiskstart3">
                                        <tr>
                                            <td><input id="allSelected5" type="checkbox"> </td>
                                            <td>XXXXXXXXXXXX</td>
                                            <td>XXXXXXXXXXXXX</td>
                                            <td>XX</td>
                                            <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div id="ye_alllank2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <%-- 点击‘有’跳转页面(j页) 7--%>
                <div class="ty-container glmang" id="hadplcelist">
                    <div class="bnck">
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more bnck-more1" onclick="comesthom(5)">返回首页</button>
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more2 gbcklast" onclick="comelastpy(5)">返回上一页</button>
                        <div class="pull-right">
                            <input type="hidden" class="setchoeprocse">
                            <span onclick="setchoeprocse(1,$(this))" class="radioCon circlee" type="radio" name="getbont">
                                <i id="updsetHangAccount1" class="fa fa-circle-o"></i> 本产品已设置工序<span class="onderal2">XX</span>个，设置完成！
                            </span>
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="makdsure()" id="madesure3">确定</button>
                        </div>
                    </div>
                    <div class="companyDetails opacel">
                        <div class="panel-box paenbox">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <div>
                                        <span id="alone2">XXXXXXXX/XXXXX/XXXXXX/XXXXXXXXXXXXXXXXX/XX/单重XXX</span>
                                        <span id="claenr2">XXXXXXXX/XXXXX/XXXXXX/XXXXXXXXXXXXXXXXX/XX/净重XXX</span>
                                    </div>
                                    <div class="ty-color-blue">注：请根据实际情况，选择本产品的各道工序！</div>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">选择“工序套餐”</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="choneprod()">选择一道“工序”</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box paenbox">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <div>
                                        <span id="nodoen2">XX工序XXXXX代号XXXXXXXX/XXX工序名称XXXXXXXXXXXXXXXXXXX</span>
                                    </div>
                                    <div class="ty-color-blue">注：请根据实际情况，选择本工序下的各道子工序！</div>
                                </div>
                                <div class="link-ed">
                                    <span class="thin-btn promk" onclick="getprolink(2)" id="startmonk">排序</span>
                                    <span class="promk thin-btn-tne2" onclick="detenddown(1)">删除</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">选择“子工序套餐”</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 chosechid" onclick="chenpchid($(this))">选择一道“子工序”</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <%-- -点击‘选择一道“子工序”’跳转页面(g页)8--%>
                <div class="ty-container glmang" id="chidlistplc">
                    <div class="bnck">
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more bnck-more1" onclick="comesthom(6)">返回首页</button>
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more2 gbcklast" onclick="comelastpy(6)">返回上一页</button>
                        <div class="pull-right">
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addchones(2,2)">创建选项</button>
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 choseover" onclick="chonceover(2)">选择完毕</button>
                        </div>
                    </div>
                    <div class="companyDetailsote opacel">
                        <div class="panel-box">
                            <div class="lank-up" style="align-items: center;display: flex;flex-direction: row;justify-content: space-between;">
                                <div class="com_address">
                                    <span>请选择一个选项，之后点击右上角的“选择完毕”。</span>
                                </div>
                                <div class="pull-right">
                                    <span class="plancode dgrap">筛选</span>
                                    <span class="second dgrap">
                                        <select id="typenum" onchange="chosechident($(this))" class="sel-rund"></select>
<%--                                        <input type="text" list="typenum" name="makeupCo" id="makeupCo" class="makeinp dyshow" onfocus="setfocus(this)"--%>
<%--                                               oninput="setinput(this)" placeholder="请选择或输入" style="width: 420px;"/>--%>
<%--                                        <div class="figger"></div>--%>
<%--                                        <datalist name="makeupCoSe" id="typenum" onchange="changeF(this)" size="10" value="0"--%>
<%--                                                  class="sel-rund sental" style="-moz-appearance:button;width: 420px;">--%>
<%--                                            <option>类别1</option>--%>
<%--                                            <option>类别2</option>--%>
<%--                                            <option>2</option>--%>
<%--                                            <option>3</option>--%>
<%--                                            <option>选项1</option>--%>
<%--                                            <option>六</option>--%>
<%--                                        </datalist>--%>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pageStyle container_item" id="tbend">
                        <div class="initBody bnck">
                            <table class="coldor ty-table ty-table-control">
                                <thead class="coldor">
                                <tr>
                                    <td>选择</td>
                                    <td>子工序代号</td>
                                    <td>子工序名称</td>
                                    <td>创建</td>
                                </tr>
                                </thead>
                                <tbody class="ty-table" id="chidlistall">
                                <tr>
                                    <td><input id='allSelected3' type="checkbox"></td>
                                    <td>XXXXXXXXXXXXX</td>
                                    <td>XXXXXXXXXXXXX</td>
                                    <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <%--点击‘选择完毕’跳转页面(h页) 9--%>
                <!--<div class="ty-container glmang" id="choneovald">
                    <div class="bnck">
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more bnck-more1" id="backfirstlist" onclick="comesthom(10)">返回首页</button>
                        <button type="button" class="btn btn-default bnck bnck-bon bnck-more2" id="backlastlist" onclick="comelastpy(9)">返回上一页</button>
                        <div class="pull-right">
                            <input type="hidden" class="setchoeprocs">
                            <span onclick="setchopependse(1,$(this))" class="radioCon circlee" type="radio" name="getbont">
                                 <i id="setchoeprocs1" class="fa fa-circle-o"></i> 本产品已设置工序<span class="onderal3">XX</span>个，设置完成！
                            </span>
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="madesure2" onclick="makdsure()">确定</button>
                        </div>
                    </div>
                    <div class="companyDetails opacel">
                        <div class="panel-box paenbox">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <div>
                                        <span id="claener">XXXXXXXX/XXXXX/XXXXXX/XXXXXXXXXXXXXXXXX/XX/净重XXX</span>
                                    </div>
                                    <div class="ty-color-blue">注：请根据实际情况，选择本产品的各道工序！</div>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">选择“工序套餐”</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="">选择一道“工序”</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box paenbox messg2">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <div>
                                        <span id="nodoen3">XX工序XXXXX代号XXXXXXXX/XXX工序名称XXXXXXXXXXXXXXXXXXX</span>
                                    </div>
                                    <div class="ty-color-blue">注：请根据实际情况，选择本工序下的各道子工序！</div>
                                </div>
                                <div class="link-ed">
                                    <span class="thin-btn promk" onclick="getprolink(3)">排序</span>
                                    <span class="promk thin-btn-tne2" onclick="">删除</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">选择“子工序套餐”</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="">选择一道“子工序”</button>
                                </div>
                            </div>
                            <div class="ty-alert">
                                <div class="com_address childcaten">
                                    <div>
                                        <span>XX工序XXXXX代号XXXXXXXX/XXX工序名称XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</span>
                                    </div>
                                    <div class="ty-color-blue">注：请根据实际情况，选择本工序下的各项操作！</div>
                                </div>
                                <div class="link-ed">
                                    <span class="thin-btn promk" onclick="getprolink(4)">排序</span>
                                    <span class="promk thin-btn-tne2" onclick="">删除</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">选择“操作套餐“</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 chonechen" onclick="chonechen($(this))">选择一项”操作“</button>
                                </div>
                            </div>
                            <div class="pageStyle container_item nona catmore1">
                                <div class="initBody bnck">
                                    <table class="coldor ty-table ty-table-control">
                                        <thead class="coldor">
                                        <tr>
                                            <td>操作人员需进行的操作</td>
                                            <td>所属类别</td>
                                            <td>是否为周期性操作</td>
                                            <td>操作</td>
                                        </tr>
                                        </thead>
                                        <tbody class="ty-table" id="chidsortlink">
                                        <tr>
                                            <td>XXXXXXXXXXXXXXXX</td>
                                            <td>XXX</td>
                                            <td>XXX</td>
                                            <td>
                                                <span class="ty-color-blue" onclick="getprolink(5)">排序</span>
                                                <span class="ty-color-red" onclick="">删除</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="ty-alert catmore2">
                                <div class="com_address childcaten">
                                    <div>
                                        <span class="name2">XX工序XXXXX代号XXXXXXXX/XXX工序名称XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</span>
                                    </div>
                                    <div class="ty-color-blue">注：请逐一选择本工序下的各项操作，方式为点击“选择一项操作”，之后根据实际情况录入内容。</div>
                                </div>
                                <div class="link-ed">
                                    <span class="thin-btn promk" onclick="getprolink(6)">排序</span>
                                    <span class="promk thin-btn-tne2" onclick="">删除</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">选择“操作套餐“</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="">选择一项”操作“</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>-->
                <%-- 点击‘选项管理’按钮跳转到新的页面 --%>
                <div class="ty-container glmang companyDetails dnck" id="procssletion" style="margin: 0px 96px;">
                    <button type="button" class="btn btn-defpuchnubault bnck-bon" onclick="comesthom(7)">返回</button>
                    <div class="newend">
                        <div class="panel-box bordenor">
                            <p>在“工序设置”下设置工序、子工序或操作时，都需要在已有选项中选择。</p>
                            <p>工序、子工序或操作选项的设置与管理需在本页面进行，</p>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span>“工序”</span>
                                    <span class="placne">已有选项<span class="nubener1">XXX</span>种</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addchones(1,1)">创建选项</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">管理已有选项</button>
                                </div>
                            </div>
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span class="placne2">已有选项的“套餐”<span class="chosnub1">XX</span>种</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">组建选项的“套餐”</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">管理已有“套餐”</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span>“子工序”</span>
                                    <span class="placne3">已有选项<span class="nubener2">XXX</span>种</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addchones(2,1)">创建选项</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">管理已有选项</button>
                                </div>
                            </div>
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span class="placne2">已有选项的“套餐”<span class="chosnub2">XX</span>种</span>
                                </div>
                                <div  class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">组建选项的“套餐”</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">管理已有“套餐”</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span>“操作”</span>
                                    <span class="placne">已有选项<span class="nubener3">XXX</span>种</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addchones(3,1)">创建选项</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">管理已有选项</button>
                                </div>
                            </div>
                            <div class="ty-alert">
                                <div class="com_address">
                                    <span class="placne2">已有选项的“套餐”<span class="chosnub3">XX</span>种</span>
                                </div>
                                <div class="pull-right">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">组建选项的“套餐”</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3">管理已有“套餐”</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/processmanagement/processSettings.js?v=SVN_REVISION" type="text/javascript"></script>

<script>

</script>
</body>


