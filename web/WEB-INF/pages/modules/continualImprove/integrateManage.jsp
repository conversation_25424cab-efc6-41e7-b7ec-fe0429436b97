<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/complaint/continualImprove.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<%@ include  file="../../common/headerBottom.jsp"%>
</head>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce_Fixed">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%--结案--%>
    <div class="bonceContainer bounce-blue" id="closeCase" style="width: 450px">
        <div class="bonceHead">
            <span>结案</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width:300px; margin: auto">
                <div class="ty-radio" value="4">
                    <p>实际结案日期 <span class="ty-color-red">*</span></p>
                    <input type="text" id="factTime"  style="width: 300px">
                </div>
                <div>
                    <p>请录入结案意见：<span class="ty-color-red">*</span></p>
                    <textarea id="settleOpinion" cols="30" rows="3" style="width: 300px"></textarea>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="approveImprovement(4)" id="sureCloseCaseBtn" disabled>提交</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--新增持续改进立案者--%>
    <div class="bonceContainer bounce-blue" id="newDivision" style="width: 760px">
        <div class="bonceHead">
            <span>请确定何人可拥有持续改进立案的权限</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="departTree">
                <ul class="ty-left" id="allRight">  </ul>
                <div class="ty-left arrow"><i class="fa fa-exchange"></i></div>
                <form class="ty-left" id="deparCon">
                    <input type="hidden" name="fileId" id="fileId">
                    <ul id="nowRight"> </ul>
                </form>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="sureNewDivisionBtn" onclick="sureNewDivision()">确定</button>
        </div>
    </div>
    <%--新增持续改进--%>
    <div class="bonceContainer bounce-green" id="newImprovement" style="width:880px;">
        <div class="bonceHead">
            <span>新增持续改进项目</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="cp_add_box">
                <div class="formItem">
                    <div class="formTitle"><span><span class="ty-color-red">*</span> 项目名称</span></div>
                    <div class="formCon"><input type="text" class="projectName required" max = "20" placeholder="20字"></div>
                    <div class="formTitle"><span>类别</span></div>
                    <div class="formCon"><input type="text" class="category"  max = "15" placeholder="15字"></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>标签</span></div>
                    <div class="formCon"><input type="text" class="tag"  max = "15" placeholder="15字"></div>
                    <div class="formTitle"><span>项目编号</span></div>
                    <div class="formCon"><input type="text" class="code"  max = "18" placeholder="18字"></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>立项依据</span></div>
                    <div class="formCon"><input type="text" class="foundation" max = "30" placeholder="30字"></div>
                    <div class="formTitle"><span>项目负责人</span></div>
                    <div class="formCon"><input type="text" class="principalName" max = "8" placeholder="8字"></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>小组成员</span></div>
                    <div class="formCon"></textarea><textarea class="member" rows="2" max = "60" placeholder="60字"></textarea></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>现况</span></div>
                    <div class="formCon"><textarea type="text" class="currentSituation" rows="2" max = "100" placeholder="100字"></textarea></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>立项目的</span></div>
                    <div class="formCon"><textarea type="text" class="proposal" rows="2" max = "200" placeholder="200字"></textarea></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>项目描述</span></div>
                    <div class="formCon"><textarea type="text" class="description" rows="2" max = "300" placeholder="300字"></textarea></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>进度计划</span></div>
                    <div class="formCon">
                        <div style="float: left;">
                            <small>预计开始日期</small><input class="date startTimeFind" id="startTimeFind" autocomplete="off" type="num">
                        </div>
                        <div style="float:left;">
                            <small>预计完成日期</small><input class="date endTimeFind" id="endTimeFind" autocomplete="off" type="num">
                        </div>
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>收益测算</span></div>
                    <div class="formCon"><textarea class="profitEstimate" rows="2" max = "300" placeholder="300字"></textarea></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>备注</span></div>
                    <div class="formCon"><textarea class="memo" rows="2" max = "200" placeholder="200字"></textarea></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="sureNewImprovementBtn" onclick="sureNewImprovement()" disabled>提交</button>
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
        </div>
    </div>
    <%--持续改进查看--%>
    <div class="bonceContainer bounce-blue" id="seeImprovement" style="min-width:880px;">
        <div class="bonceHead">
            <span>持续改进查看</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix" style="max-height: 500px;overflow: auto">
            <div class="cp_scan_box">
                <div class="formItem">
                    <div class="formTitle"><span><span class="ty-color-red">*</span> 项目名称</span></div>
                    <div class="formCon"><div class="cp_input cp_projectName"></div></div>
                    <div class="formTitle"><span>类别</span></div>
                    <div class="formCon"><div class="cp_input cp_category"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>标签</span></div>
                    <div class="formCon"><div class="cp_input cp_tag"></div></div>
                    <div class="formTitle"><span>项目编号</span></div>
                    <div class="formCon"><div class="cp_input cp_code"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>立项依据</span></div>
                    <div class="formCon"><div class="cp_input cp_foundation"></div></div>
                    <div class="formTitle"><span>项目负责人</span></div>
                    <div class="formCon"><div class="cp_input cp_principalName"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>小组成员</span></div>
                    <div class="formCon"><div class="cp_textarea cp_member"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>现况</span></div>
                    <div class="formCon"><div class="cp_textarea cp_currentSituation"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>立项目的</span></div>
                    <div class="formCon"><div class="cp_textarea cp_proposal"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>项目描述</span></div>
                    <div class="formCon"><div class="cp_textarea cp_description"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>进度计划</span></div>
                    <div class="formCon"><small>预计开始日期</small><span class="cp_date cp_startTimeFind"></span><small>预计完成日期</small><span class="cp_date cp_endTimeFind"></span></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>收益测算</span></div>
                    <div class="formCon"><div class="cp_textarea cp_profitEstimate"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>备注</span></div>
                    <div class="formCon"><div class="cp_textarea cp_memo"></div></div>
                </div>
            </div>
            <%--结案记录--%>
            <div class="cp_endCaseRecord"></div>

            <%--审批记录--%>
            <div class="cp_approveHistory"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeCaseBtn()">结案</button>
        </div>
    </div>
    <%--更换立案者--%>
    <div class="bonceContainer bounce-blue" id="changeFiling" style="width: 450px">
        <div class="bonceHead">
            <span>更换立案者</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <select id="change_filing"></select>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureChangeFiling()" id="changeFilingBtn" disabled>确定</button>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>综合管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="buttonGroup">
                    <%--主页按钮--%>
                    <div class="mainButton">
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" id="divisionSettingBtn" onclick="divisionSettingBtn()">分工设置</button>
                        <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right" id="newImprovementBtn" onclick="newImprovementBtn()" style="margin-right: 5px">新增持续改进项目</button>
                    </div>
                    <%--分工设置按钮--%>
                    <div class="divisionButton" style="display: none">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-left"  onclick="goBack()">返回</span>
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right" onclick="newDivisionBtn()">新增</span>
                    </div>
                    <%--结案通过时间切换按钮--%>
                    <div class="passedButton" style="display: none">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-left"  onclick="goBack()">返回</span>
                        <div class="ty-btn-group ty-right" id="changeState" style="margin-left: 8px;">
                            <a class="ty-btn ty-btn-big ty-circle-5 ty-btn-active-blue">本年</a>
                            <a class="ty-btn ty-btn-big ty-circle-5">去年</a>
                            <a class="ty-btn ty-btn-big ty-circle-5">前年</a>
                            <a class="ty-btn ty-btn-big ty-circle-5" data-to="custom" id="customTimeBtn">
                                <span>自定义时间</span>
                                <div class="hel" style="display: none" id="custom" onclick="event.stopPropagation()">
                                    <div class="hel_con">
                                        <table class="ty-table ty-table-none">
                                            <tr>
                                                <td>起时间</td>
                                                <td><input type="text" class="ty-inputText" id="queryBeginTime"></td>
                                            </tr>
                                            <tr>
                                                <td>止时间</td>
                                                <td><input type="text" class="ty-inputText" id="queryEndTime"></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="hel_foot">
                                        <span class="ty-btn ty-btn-big ty-circle-3"
                                              onclick="$('#custom').hide();">取消</span>
                                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="queryBtn">查询</span>
                                    </div>
                                    <div class="popper__arrow" style="right: 51px"></div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="clr"></div>
                <br>
                <div>
                    <div class=" mainPage">
                        <ul class="ty-secondTab">
                            <li class="ty-active">立案待审批</li>
                            <li>立案驳回</li>
                            <li>立案通过</li>
                            <li>结案待审批</li>
                            <li>结案驳回</li>
                            <li>结案通过</li>
                        </ul>

                        <%--主页--%>
                        <div class="ty-mainData">
                            <%--立案待审批--%>
                            <div class="tblContainer"  style="display: none;">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td>项目名称</td>
                                        <td>项目编号</td>
                                        <td>类别</td>
                                        <td>标签</td>
                                        <td>项目负责人</td>
                                        <td>预计开始日期</td>
                                        <td>预计完成日期</td>
                                        <td>立案申请者</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <%--立案驳回--%>
                            <div class="tblContainer" style="display: none;">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td>项目名称</td>
                                        <td>项目编号</td>
                                        <td>类别</td>
                                        <td>标签</td>
                                        <td>项目负责人</td>
                                        <td>预计开始日期</td>
                                        <td>预计完成日期</td>
                                        <td>立案申请者</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <%--立案通过--%>
                            <div class="tblContainer">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td>项目名称</td>
                                        <td>项目编号</td>
                                        <td>类别</td>
                                        <td>标签</td>
                                        <td>项目负责人</td>
                                        <td>预计开始日期</td>
                                        <td>预计完成日期</td>
                                        <td>立案申请者</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <%--结案待审批--%>
                            <div class="tblContainer" style="display: none;">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td>项目名称</td>
                                        <td>项目编号</td>
                                        <td>类别</td>
                                        <td>标签</td>
                                        <td>项目负责人</td>
                                        <td>实际结案日期</td>
                                        <td>结案者</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <%--结案驳回--%>
                            <div class="tblContainer" style="display: none;">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td>项目名称</td>
                                        <td>项目编号</td>
                                        <td>类别</td>
                                        <td>标签</td>
                                        <td>项目负责人</td>
                                        <td>实际结案日期</td>
                                        <td>结案者</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <%--分工设置--%>
                    <div class="divisionPage" style="display: none">
                        <div>
                            <%--立案者列表--%>
                            <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>已有如下<span class="ty-color-blue userFigure"></span>人拥有持续改进立案的权限：</div>
                            <div class="tblContainer">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td>姓名</td>
                                        <td>性别</td>
                                        <td>手机号</td>
                                        <td>部门</td>
                                        <td>职位</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <%--结案通过--%>
                    <div class="passedPage" style="display: none">
                        <%--结案通过--%>
                        <div class="tblContainer">

                            <div class="tplContainer">
                                <div class="tip"></div>
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <tr>
                                        <td>项目名称</td>
                                        <td>项目编号</td>
                                        <td>类别</td>
                                        <td>标签</td>
                                        <td>项目负责人</td>
                                        <td>实际结案日期</td>
                                        <td>结案者</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                        <div id="ye_Improvement_integrate"></div>
                    </div>
                </div>


            </div>


        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>
<%--<script src="${pageContext.request.contextPath }/script/complaint/complaintCommon.js" type="text/javascript"></script>--%>
<script src="../script/continualImprove/integrateManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/downLoadFile/downLoadFile.js?v=SVN_REVISION" type="text/javascript"></script>


<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
