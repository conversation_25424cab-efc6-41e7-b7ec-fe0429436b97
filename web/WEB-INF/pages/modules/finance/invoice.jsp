<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    .reasonHover{
        cursor: default;
    }
    .reasonHoverCon{
        position: absolute;
        top: 0;
        left: 50%;
        width: 200px;
        min-height: 120px;
        background-color: #fff;
        padding: 10px 20px 20px 20px;
        border:1px solid #ccc;
        box-shadow: 0 0 5px #ccc;
        display: none;
        text-align: left;
        text-indent: 24px;
        word-wrap: break-word;
    }
    .reasonHover:hover .reasonHoverCon{
        display: block;
    }

    .receiveCorpItems{
        position: absolute;
        display: none;
        background: #fff;
        width: 181px;
        left: 5px;
        border: 1px solid #ddd;
        max-height: 130px;
        overflow-y: auto;
        margin-left: 538px;
    }

    .receiveCorpItems > option {
        padding: 0 15px;
    }

    .receiveCorpItems > option:hover{
        color:#34495e;
        background: #b9d2df;
    }

    .bonceCon input:not([type="radio"]):not([type="checkbox"]), .bonceCon select {
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        background-color: #fff;
        display: inline-block;
        height: 30px;
        line-height: 30px;
        background-image: none;
        box-sizing: border-box;
        color: #606266;
        font-size: inherit;
        outline: none;
        padding: 0 8px;
        transition: border-color .2s cubic-bezier(.645,.045,.355,1);
    }

    .iCon {
        padding: 50px;
    }

    .iCon > p {
        cursor: default;
    }

    .iCon i {
        color: #0b94ea;
    }

    .bounce_Fixed2 {
        position: fixed;
        z-index: 10002;
        background: rgba(100, 100, 100, 0.5);
    }

    .textLeft {
        text-align: left !important;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed2">
    <%-- 查看操作记录详情 --%>
    <div class="bonceContainer bounce-green" id="invoiceLogInfo" style="width: 800px">
        <div class="bonceHead">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td>开票申请日期</td>
                    <td class="textLeft" id="applicationTime_log"></td>
                    <td>申请人</td>
                    <td class="textLeft" id="applicantName_log"></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 开票日期</td>
                    <td class="textLeft" id="operateDate_log"></td>
                    <td><span class="ty-color-red">*</span> 客户名称</td>
                    <td class="textLeft" id="receiveCorp_log"></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 发票金额/元</td>
                    <td class="textLeft" id="amount_log"></td>
                    <td><span class="ty-color-red">*</span> 是否含税</td>
                    <td class="textLeft" id="taxInclusive_log"></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-green" id="F_tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="F_errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="invoiceEntry" style="width: 800px">
        <div class="bonceHead">
            <span>开票录入</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td>开票申请日期</td>
                    <td>
                        <input type="text" class="ty-inputText applicationTime" id="applicationTime">
                    </td>
                    <td>申请人</td>
                    <td style="text-align: left">
                        <input type="text" class="ty-inputText applicantName" id="applicantName">
                    </td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 开票日期</td>
                    <td>
                        <input type="text" class="ty-inputText operateDate" id="operateDate">
                    </td>
                    <td><span class="ty-color-red">*</span> 客户名称</td>
                    <td style="text-align: left">
                        <div class="receiveCorpSearch">
                            <span class="recivefent" onclick="showReceiveCorpItems()">
                                <input type="text" class="ty-inputText receiveCorp" require id="receiveCorp"
                                       onchange="" autocomplete="off" readonly/>
                            </span>
                            <div class="box"></div>
                            <div class="receiveCorpItems">
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 发票金额/元</td>
                    <td>
                        <input type="text" class="ty-inputText amount" id="amount" onkeyup="validateAmount($(this))" autocomplete="off">
                    </td>
                    <td><span class="ty-color-red">*</span> 是否含税</td>
                    <td style="text-align: left">
                        <select type="text" class="ty-inputText taxInclusive" id="taxInclusive">
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </td>
                </tr>
                </tbody>
            </table>
            <p class="errorTip ty-color-red"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="sureInvoiceEntryBtn" onclick="sureInvoiceEntry()" disabled>确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="voidInvoice">
        <div class="bonceHead">
            <span>作废发票</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
           <h5 style="text-align: center">您确定该发票实际已经被作废了吗？</h5>
            <textarea class="ty-textarea reason" id="reason" rows="3" style="width: 100%" placeholder="请填写作废理由"></textarea>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="sureVoidInvoice()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="openInvoice">
        <div class="bonceHead">
            <span>启用发票</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <h5 style="text-align: center">确定重新启用该张发票？</h5>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="sureOpenInvoice()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="updateInvoice">
        <div class="bonceHead">
            <span>修改已开发票的信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="iCon">
                <p onclick="togleIcon($(this) , 1)"><i class="fa fa-circle-o"></i> 之前操作失误，该发票实际尚未开具</p>
                <p onclick="togleIcon($(this) , 2)"><i class="fa fa-circle-o"></i> 修改已开发票里面的内容</p>
                <p onclick="togleIcon($(this) , 3)"><i class="fa fa-circle-o"></i> 作废这张发票</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="sureupdateInvoiceType()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="invoiceLog" style="width: 800px">
        <div class="bonceHead">
            <span>操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <tbody>
                <tr>
                    <td>操作时间</td>
                    <td>操作内容</td>
                    <td>操作者</td>
                    <td>操作前</td>
                    <td>操作后</td>
                </tr>
                </tbody>
            </table>
            <p class="errorTip ty-color-red"></p>
        </div>
        <div class="bonceFoot"></div>
    </div>

</div>
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-green" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="newInvoice" style="width:1025px;">
        <div class="bonceHead">
            <span>新增发票</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td><span class="ty-color-red">*</span> 请选择发票种类</td>
                    <td>
                        <select class="ty-inputText buyKind">
                            <option value=""> 请选择</option>
                            <option value="1">增值税专用票</option>
                            <option value="2">增值税普通票</option>
                            <option value="3">其他普通票</option>
                        </select>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 购买日期</td>
                    <td>
                        <input type="text" class="ty-inputText buyDate" id="buyDate">
                    </td>
                    <td><span class="ty-color-red">*</span> 购买者</td>
                    <td>
                        <select class="ty-inputText buyerName" id="buyerName"></select>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 发票代码</td>
                    <td>
                        <input type="text" class="ty-inputText buyNo" id="buyNo">
                    </td>
                    <td><span class="ty-color-red">*</span> 起号码</td>
                    <td>
                        <input type="text" class="ty-inputText beginNo" id="beginNo" onkeyup="validateNo($(this))"
                               autocomplete="off">
                    </td>
                    <td><span class="ty-color-red">*</span> 止号码</td>
                    <td>
                        <input type="text" class="ty-inputText endNo" id="endNo" onkeyup="validateNo($(this))" autocomplete="off">
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="sureNewInvoiceBtn" onclick="sureNewInvoice()" disabled>提交</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="invoiceList" style="width: 1080px;">
        <div class="bonceHead">
            <span>发票列表</span>
            <a class="bounce_close" onclick="bounce.cancel();refreashList()"></a>
        </div>
        <div class="bonceCon" style="height: 600px;overflow-y: auto;">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>发票号码</td>
                    <td>开票日期</td>
                    <td>开票金额</td>
                    <td>客户名称</td>
                    <td>开票申请日期</td>
                    <td>申请人</td>
                    <td>状态</td>
                    <td>作废理由</td>
                    <td>操作</td>
                </tr>
                </thead>
                <tbody id="financeInvoiceDetails"></tbody>
            </table>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>发票管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container" id="infoCon">
                <p>
                    <span id="navControl"></span>
                </p>
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-left" id="newInvoiceBtn"
                        onclick="newInvoiceBtn()">新增
                </button>
                <div class="clr"></div>
                <br>
                <div class="ty-mainData">
                    <div class="tplContainer">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>起止号码</td>
                                <td>发票代号</td>
                                <td>发票种类</td>
                                <td>状态为空白的发票</td>
                                <td>购买者</td>
                                <td>购买日期</td>
                                <td>录入</td>
                            </tr>
                            </thead>
                            <tbody  id="invoicePurchaseList"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/finance/invoice.js?v=SVN_REVISION"></script>

</body>
</html>
