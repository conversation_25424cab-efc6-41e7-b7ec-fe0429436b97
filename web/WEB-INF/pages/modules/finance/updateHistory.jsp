<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    .ty-left{ width:50%;   }
    #approveChange{ width:800px; }
    .infoCon{
        max-height: 400px;
        overflow-y: auto;
    }
    .ty-process#approveChangeDetail{
        padding:30px 15px 20px ;
    }
    .handleBtn{
        padding:8px;
    }
    .bounceConMain{
        width: 300px;
        margin: auto;
    }
    .bounceConMain textarea{
        width: 100%;
    }
    .ty-process-item p{
        word-break: break-all;
    }

    .cp_img_box{
        display: inline-block;
        width: 100px;
        position: relative;
        vertical-align: top;
    }
    .cp_img_box .fileType{
        padding-top: 20px;
    }
    .cp_img_box .fileType a{
        display: none;
        background-color: rgba(93, 156, 236,.9);
        color: #fff;
        height: 18px;
        line-height:18px;
        width:36px;
        margin: 0 auto 5px;
        font-size: 12px;
        text-align: center;
        border-radius: 2px;
        cursor: pointer;
    }
    .cp_img_box .fa-times {
        position: absolute;
        right: 6px;
        top: 0px;
        color: #d64835;
    }
    .cp_img_box:hover .fileType a{
        display: block;
    }
    .cp_img_box .fileType span.fa{
        display: none;
    }
    .cp_img_box:hover .fileType span.fa{
        display: block;
    }
    .cp_img_name{
        font-size: 12px;
        color: #666;
        text-align: center;
        word-break:break-all;
        max-height: 70px;
    }
    .fixedBox{
        display: flex;
    }
    .fileType {
        width: 60px;
        height: 80px;
        margin-left: 20px;
        margin-bottom: 2px;
        background-position: center;
        background-color: #eee;
        background-size: 60px 80px;
    }


</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-green" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p class="tipWord" style="text-align: center; padding:10px 0"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="confirm">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceConMain">
                <div class="agree">
                    <p class="tip-word">确定批准此条财务修改申请吗？</p>
                </div>
                <div class="reject">
                    <p class="tip-word">请输入驳回理由</p>
                    <textarea name="reason" cols="30" rows="3" placeholder="最多可录入50字" onkeyup="countWords($(this),50)"></textarea>
                    <div class="textMax text-right" max="50"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="approvalChange()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>处理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container">
                <div id="approveChange">
                    <div class="ty-alert ty-alert-info description"></div>
                    <div class="clearfix">
                        <div class="ty-left ty-procon">
                            <div class="ty-panel ty-process" >
                                <div class="infoHead ty-process-ttl"><span class="ty-btn ty-btn-green">审批流程</span></div>
                                <div class="conInfo ty-process-container" id="process">
                                    <div class="infoList ty-process-item">
                                        <p><span class="dot dot-wait"></span>处理人：超管 </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="ty-left ty-procon">
                            <div class="ty-panel ty-process" id="approveChangeDetail">
                                <div class="infoHead ty-process-ttl "><span class="ty-btn ty-btn-green">详情信息</span></div>
                                <div class="infoCon"></div>
                            </div>
                        </div>
                    </div>
                    <div class="handleBtn text-right"></div>
                </div>

            </div>

        </div>
    </div>

</div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/finance/updateHistory.js?v=SVN_REVISION"></script>

</body>
</html>
