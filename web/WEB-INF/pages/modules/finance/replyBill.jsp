<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/finance/replyBill.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_cancel()">确定</span>
        </div>
    </div>
    <!-- 转账支票台账查看 -->
    <div class="bonceContainer bounce-blue" id="bill_seeAccount">
        <div class="bonceHead">
            <span>转账支票详情</span>
            <a class="bounce_close" onclick="bill_seeClose()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails" style="max-height: 500px;overflow-y:auto;">
                <div>
                    <div class="check_See">
                        <span>项目</span>
                        <input type="text" name="" disabled id="checkSee_item" value="收入">
                    </div>
                    <div class="check_See">
                        <span>类别</span>
                        <input type="text" name="" id="checkSee_catagory" disabled value="借款">
                    </div>
                    <div class="check_bg">
                        <div class="dataMark"></div>
                        <div class="check_Seebg">
                            <span>票据种类</span>
                            <input type="text" id="checkSee_billType" name="">
                        </div>
                    </div>
                    <div class="check_bg">
                        <div class="dataMark"></div>
                        <div class="check_Seebg">
                            <span>票据所属月份</span>
                            <input type="text" id="checkSee_month" name="">
                        </div>
                    </div>
                    <div class="check_See">
                        <span>摘要</span>
                        <input type="text" name="" id="checkSee_abstract" disabled value="收到的转账支票">
                    </div>
                    <div class="check_bg">
                        <div class="dataMark"></div>
                        <div class="check_Seebg">
                            <span>票据数量</span>
                            <input type="text" id="checkSee_num" name="">
                        </div>
                    </div>
                    <div class="check_See">
                        <span>实际金额</span>
                        <input type="text" name="" id="checkSee_money" disabled>
                    </div>
                    <div class="check_See">
                        <span>发票金额</span>
                        <input type="text" name="" id="checkSee_billMoney" disabled>
                    </div>
                    <div class="check_See">
                        <span>用途</span>
                        <input type="text" id="checkSee_use" name="" disabled>
                    </div>
                    <div class="check_See">
                        <span>经手人</span>
                        <input type="text" id="checkSee_guy" name="" disabled value="白杨">
                    </div>
                    <div class="check_See">
                        <span>收入方式</span>
                        <input type="text" name="" id="checkSee_income" disabled value="转账支票">
                    </div>
                    <div class="check_See">
                        <span>收到支票日期</span>
                        <input type="text" name="" id="checkSee_getbilldate" disabled value="2016年7月5日">
                    </div>
                    <div class="check_See">
                        <span>付款单位</span>
                        <input type="text" name="" id="checkSee_payer" disabled value="阿里巴巴">
                    </div>
                    <div class="check_See">
                        <span>支票号</span>
                        <input type="text" name="" id="checkSee_billnumber" disabled>
                    </div>
                    <div class="check_See">
                        <span>支票到期日</span>
                        <input type="text" name="" id="checkSee_billoutdata" disabled>
                    </div>
                    <div class="check_See">
                        <span>出具支票单位</span>
                        <input type="text" name="" id="checkSee_checkunit" disabled value="阿里巴巴">
                    </div>
                    <div class="check_See">
                        <span>出具支票银行</span>
                        <input type="text" name="" id="checkSee_checkbank" disabled value="建设银行">
                    </div>
                    <div class="check_See">
                        <span>财务经手人</span>
                        <input type="text" name="" id="checkSee_financeguy" disabled>
                    </div>
                    <div class="check_See">
                        <span>录入时间</span>
                        <input type="text" name="" id="checkSee_inputtime" disabled>
                    </div>
                    <div class="check_See">
                        <span>备注</span>
                        <input type="text" id="checkSee_remark" name="" disabled>
                    </div>

                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="billSee_cancel()">取消</span>
        </div>
    </div>
    <!-- 转账支票台账修改 -->
    <%--<div class="bonceContainer bounce-blue" id="bill_updataAccount">
        <div>
            <div class="bonceHead">
                <span>转账支票修改</span>
                <a class="bounce_close" onclick="bill_updataClose()"></a>
            </div>
            <div class="bonceCon">
                <div class="addpayDetails">
                    <div>
                        <div class="check_See">
                            <span>项目</span>
                            <input type="text" name="" id="billUp_item" disabled value="收入">
                        </div>
                        <div class="check_See" id="">
                            <span>类别</span>
                            <select id="billUp_category" onclick="bill_others($(this))">
                                <option value="1">货款</option>
                                <option value="2">借款</option>
                                <option value="3">投资款</option>
                                <option value="4">废品</option>
                                <option value="5" >其他</option>
                            </select>
                        </div>
                        <div class="check_See check_SeeHide">
                            <span> </span>
                            <input type="text" name="" disabled>
                        </div>
                        <div class="check_bg">
                            <div class="dataMark"></div>
                            <div class="check_SeeType">
                                <span>票据种类</span>
                                <span class="radioBtn "  onclick="dataImportNav($(this))">
                                    <span class="val">{"value":0 , "name":"dataNav"}</span>
                                    <span class="radioShow"></span>
                                </span>
                                <span class="valShow">增值票</span>

                                <span class="radioBtn "  onclick="dataImportNav($(this))">
                                    <span class="val">{"value":0 , "name":"dataNav"}</span>
                                    <span class="radioShow"></span>
                                </span>
                                <span class="valShow">普通票</span>

                                <span class="radioBtn " onclick="dataImportNav($(this))">
                                    <span class="val">{"value":0 , "name":"dataNav"}</span>
                                    <span class="radioShow"></span>
                                </span>
                                <span class="valShow">收据</span>

                            </div>
                        </div>
                        <div class="check_bg">
                            <div class="dataMark"></div>
                            <div class="check_SeeType">
                                <span>票据所属月份</span>
                                <span class="radioBtn " id="billdateCur" onclick="dataImportNav($(this))">
                                    <span class="val">{"value":0 , "name":"dataNav"}</span>
                                    <span class="radioShow"></span>
                                </span>
                                <span class="valShow">本月票据</span>

                                <span class="radioBtn " id="billdateLast" onclick="dataImportNav($(this))">
                                    <span class="val">{"value":0 , "name":"dataNav"}</span>
                                    <span class="radioShow"></span>
                                </span>
                                <span class="valShow">非本月票据</span>

                            </div>
                        </div>
                        <div class="check_See">
                            <span>摘要</span>
                            <input type="text" name="" id="billUp_abstract" value="收到的转账支票">
                        </div>
                        <div class="check_bg">
                            <div class="dataMark"></div>
                            <div class="check_Seebg">
                                <span>票据数量</span>
                                <input type="text" name="" id="billUp_num">
                            </div>
                        </div>
                        <div class="check_See">
                            <span>实际金额</span>
                            <input type="text" name="" id="billUp_money"  >
                        </div>
                        <div class="check_See">
                            <span>发票金额</span>
                            <input type="text" name="" id="billUp_billMoney"  >
                        </div>
                        <div class="check_See">
                            <span>用途</span>
                            <input type="text" name="" id="billUp_use">
                        </div>
                        <div class="check_See">
                            <span>经手人</span>
                            <input type="text" name="" id="billUp_guy"  >
                        </div>
                        <div class="check_See">
                            <span>收入方式</span>
                            <input type="text" name="" id="billUp_income" disabled >
                        </div>
                        <div class="check_See">
                            <span>收到支票日期</span>
                            <input type="text" name="" id="billUp_getbilldata"  >
                        </div>
                        <div class="check_See">
                            <span>付款单位</span>
                            <input type="text" name="" id="billUp_payer"  >
                        </div>
                        <div class="check_See">
                            <span>支票号</span>
                            <input type="text" name="" id="billUp_billnumber" value="30501232  00090991">
                        </div>
                        <div class="check_See">
                            <span>支票到期日</span>
                            <input type="text" name="" id="billUp_billoutdata" value="2017年10月1日">
                        </div>
                        <div class="check_See">
                            <span>出具支票单位</span>
                            <input type="text" name="" id="billUp_checkunit" >
                        </div>
                        <div class="check_See">
                            <span>出具支票银行</span>
                            <input type="text" name="" id="billUp_checkbank"  >
                        </div>
                        <div class="check_See">
                            <span>财务经手人</span>
                            <input type="text" disabled id="billUp_financeguy"  >
                        </div>
                        <div class="check_See">
                            <span>录入时间</span>
                            <input type="text" disabled id="billUp_inputtime"  >
                        </div>
                        <div class="check_See">
                            <span>备注</span>
                            <input type="text" name="" id="billUp_remark">
                        </div>

                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="billUpdata_sure($(this))">提交</span>
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="billUpdata_cancel()">取消</span>
            </div>
        </div>
    </div>--%>
    <!-- 转账支票台账转入银行     -->
    <div class="bonceContainer bounce-blue" id="bill_bankAccount">
        <div class="bonceHead">
            <span>存入银行</span>
            <a class="bounce_close" onclick="bill_bankClose()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div>
                    <div class="check_See bank_Con">
                        <span>存入银行账户</span>
                        <span>*</span>
                        <select id="turnToBank_1"> </select>
                    </div>
                    <div class="check_See bank_Con">
                        <span>存入时间</span>
                        <span>*</span>
                        <input type="text" class="laydate-icon" id="bill_depositDate">
                    </div>
                    <div class="check_See bank_Con">
                        <span>存入经手人</span>
                        <span> </span>
                        <input type="text" name="" id="bill_depositorName">
                    </div>
                    <div class="check_See bank_Con">
                        <span>到账时间</span>
                        <span>*</span>
                        <input type="text"  class="laydate-icon" id="billReceiveAccountDate">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <p id="turnToTip_1"></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="billBank_sure()">提交</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="billBank_cancel()">取消</span>
        </div>
    </div>
    <!-- 承兑汇票台账转入银行 -->
    <div class="bonceContainer bounce-blue" id="accept_bankAccount">
        <div class="bonceHead">
            <span>存入银行</span>
            <a class="bounce_close" onclick="accept_bankClose()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div>
                    <div class="check_See bank_Con">
                        <span>存入银行账户</span>
                        <span>*</span>
                        <select id="acceptBankList" >
                            <option>请选择银行账户</option>
                            <option value="1"><span id="accept_ICBC">工商银行西青支行</span><span id="accept_cardNumber1">8999 8660 8984 8849 890</span></option>
                            <option value="2"><span id="accept_long">建设银行河北支行</span><span id="accept_cardNumber2">6224 6782 0723 800</span></option>
                            <option value="3"><span id="accept_cb">合作社</span><span id="accept_cardNumber3">6208 3927 3293 979</span></option>
                        </select>
                    </div>
                    <div class="check_See bank_Con">
                        <span>存入时间</span>
                        <span>*</span>
                        <input type="text" class="laydate-icon" id="accept_depositDate">
                    </div>
                    <div class="check_See bank_Con">
                        <span>存入经手人</span>
                        <span> </span>
                        <input type="text" name="" id="accept_depositorName">
                    </div>
                    <div class="check_See bank_Con">
                        <span>到账时间</span>
                        <span>*</span>
                        <input type="text" id="acceptReceiveAccountDate" class="laydate-icon" >
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <p id="turnToTip_2"></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="acceptBank_sure()">提交</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="acceptBank_cancel()">取消</span>
        </div>
    </div>
    <!-- 转账支票查看-来源于回款录入 -->
    <div class="bonceContainer bounce-blue" id="collectChequeSee" style="width:800px;">
        <div class="bonceHead">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <ul class="chequeDetail">
                <li>
                    <span class="subTitle">客户名称</span>
                    <span class="subCon" id="collectCustomer"></span>
                </li>
                <li>
                    <span class="subTitle">回款金额</span>
                    <span class="subCon" id="collectAmount"></span>
                </li>
                <li>
                    <span class="subTitle">收入方式</span>
                    <span class="subCon" id="collectIncome"></span>
                </li>
                <li>
                    <span class="subTitle">收到支票日期</span>
                    <span class="subCon" id="collectReceive"></span>
                </li>
                <li>
                    <span class="subTitle">支票号</span>
                    <span class="subCon" id="collectSn"></span>
                </li>
                <li>
                    <span class="subTitle">支票到期日</span>
                    <span class="subCon" id="collectDueDate"></span>
                </li>
                <li>
                    <span class="subTitle">出具支票单位</span>
                    <span class="subCon" id="collectUnit"></span>
                </li>
                <li>
                    <span class="subTitle">出具支票银行</span>
                    <span class="subCon" id="collectBank"></span>
                </li>
            </ul>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <!-- 承兑汇票查看-来源于回款录入 -->
    <div class="bonceContainer bounce-blue" id="collectBillSee" style="width:800px;">
        <div class="bonceHead">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <ul class="collectBillDetail">
                <li>
                    <span class="subTitle">客户名称</span>
                    <span class="subCon" id="billCustomer"></span>
                </li>
                <li>
                    <span class="subTitle">回款金额</span>
                    <span class="subCon"></span>
                </li>
                <li>
                    <span class="subTitle">收入方式</span>
                    <span class="subCon" id="billIncome"></span>
                </li>
                <li>
                    <span class="subTitle">收到汇票日期</span>
                    <span class="subCon"></span>
                </li>
                <li>
                    <span class="subTitle">汇票号</span>
                    <span class="subCon"></span>
                </li>
                <li>
                    <span class="subTitle">汇票到期日</span>
                    <span class="subCon"></span>
                </li>
                <li>
                    <span class="subTitle">原始出具汇票单位</span>
                    <span class="subCon"></span>
                </li>
                <li>
                    <span class="subTitle">出具汇票银行</span>
                    <span class="subCon"></span>
                </li>
            </ul>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <!-- 承兑汇票台账查看/转账支票台账查看 -->
    <div class="bonceContainer bounce-blue" id="acceptCheckSee" style="width:800px;">
        <div class="bonceHead">
            <span>承兑汇票查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon align-left">
            <div class="smallSize">
                <div>
                    <span class="ty-btn ty-btn-blue ty-btn-size ty-circle-5" onclick="updateAcceptBill()">修改</span>
                    <span class="ty-btn ty-btn-blue ty-btn-size ty-circle-5" onclick="baseRecord()">修改记录</span>
                </div>
                <div class="remarryTeam">
                    <div class="paddT clear">
                        <p class="ty-left tTtl operateCon">存入银行的信息</p>
                        <p class="ty-right">操作 <span class="operateDate"></span></p>
                    </div>
                    <div>
                        <table  class="ty-table saveStore2">
                            <tbody>
                            <tr>
                                <td width="40%">存入银行账户</td>
                                <td id="saveBank"></td>
                            </tr>
                            <tr>
                                <td>存入时间</td>
                                <td id="saveTime"></td>
                            </tr>
                            <tr>
                                <td>存入经手人</td>
                                <td id="saveHandler"></td>
                            </tr>
                            <tr>
                                <td>到账时间</td>
                                <td id="saveArriveDate"></td>
                            </tr>
                            </tbody>
                        </table>
                        <table  class="ty-table saveStore4">
                            <tbody>
                            <tr>
                                <td width="40%">收款单位</td>
                                <td id="payeeName"></td>
                            </tr>
                            <tr>
                                <td>实际付款日期</td>
                                <td id="payDate"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="paddT clear">
                    <p class="ty-left tTtl">票面信息</p>
                    <p class="ty-right">创建 <span class="createInfo"></span></p>
                </div>
                <table  class="ty-table">
                    <tbody>
                    <tr>
                        <td width="40%" class="snTtl">汇票号</td>
                        <td colspan="3" id="billSn"></td>
                    </tr>
                    <tr>
                        <td>金额</td>
                        <td id="billAmount"></td>
                    </tr>
                    <tr>
                        <td>到期日</td>
                        <td id="billDueDate"></td>
                    </tr>
                    <tr>
                        <td class="unitTtl">最初出具的单位</td>
                        <td id="billUnit"></td>
                    </tr>
                    <tr>
                        <td>出具的银行</td>
                        <td id="billBank"></td>
                    </tr>
                    <tr>
                        <td>付款单位</td>
                        <td id="acceptSee_payer"></td>
                    </tr>
                    </tbody>
                </table>
                <p class="paddT tTtl">其他信息</p>
                <table  class="ty-table" >
                    <tbody>
                    <tr>
                        <td width="40%">数据来源</td>
                        <td id="acceptSee_item">收入</td>
                    </tr>
                    <tr>
                        <td>用途</td>
                        <td id="acceptSee_use"></td>
                    </tr>
                    <tr>
                        <td>摘要</td>
                        <td id="acceptSee_abstract"></td>
                    </tr>
                    <tr>
                        <td>收到日期</td>
                        <td id="acceptSee_getbilldata"></td>
                    </tr>
                    <tr class="sourceIncome">
                        <td>所开具发票或收据的金额</td>
                        <td id="acceptSee_billMoney"></td>
                    </tr>
                    <tr class="sourceIncome">
                        <td>经手人</td>
                        <td id="acceptSee_guy"></td>
                    </tr>
                    <tr class="sourceIncome">
                        <td>备注</td>
                        <td id="acceptSee_remark"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="hd seeAccountInfo"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel()">关  闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <!-- 承兑汇票台账修改 -->
    <div class="bonceContainer bounce-blue" id="accept_updataAccount" style="width: 800px;">
        <div>
            <div class="bonceHead">
                <span>修改<span class="billCat">承兑汇票</span>信息</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="upForm">
                    <input class="form-control" type="hidden" name="id" id="updateId" />
                    <ul id="accept_income" class="clear bgGray">
                        <li>
                            <p>数据类别</p>
                            <input class="form-control" disabled id="acceptUp_item" value="收入" />
                        </li>
                        <li>
                            <p>数据类别</p>
                            <select class="form-control" id="acceptUp_category" onchange="bill_others($(this))" name="category">
                                <option value="1">货款</option>
                                <option value="2">借款</option>
                                <option value="3">投资款</option>
                                <option value="4">废品</option>
                                <option value="5" >其他</option>
                            </select>
                        </li>
                        <li>
                            <p>用途</p>
                            <input class="form-control" value="" placeholder="请录入" name="purpose"/>
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                        <li>
                            <p>摘要</p>
                            <input class="form-control" value="" placeholder="请录入" name="summary"/>
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                        <li>
                            <p>所开具发票或收据的金额</p>
                            <input class="form-control" value="" placeholder="请录入" name="billAmount" oninput="clearNoNumN(this, 2)" />
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                        <li class="clear">
                            <div class="modItem-s ty-left">
                                <p>收到日期</p>
                                <input class="form-control" name="receiveDate" id="income_receiveDate"/>
                            </div>
                            <div class="modItem-s ty-right">
                                <p>经手人</p>
                                <input type="text" class="form-control" name="operatorName" >
                            </div>
                        </li>
                        <li class="longSize">
                            <p>备注</p>
                            <input class="form-control" name="memo" />
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                    </ul>
                    <ul id="accept_common" class="clear bgGray">
                        <li>
                            <p>数据来源</p>
                            <input class="form-control" id="acceptUp_source" value="回款录入" disabled/>
                        </li>
                        <li>
                            <p>收到日期</p>
                            <input class="form-control" value="" placeholder="请录入" name="receiveDate" id="common_receiveDate"/>
                        </li>
                        <li>
                            <p>用途</p>
                            <input class="form-control" value="" name="purpose" disabled/>
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                        <li>
                            <p>摘要</p>
                            <input class="form-control" value="" name="summary" disabled/>
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                    </ul>
                    <div class="line"></div>
                    <ul class="clear bgGray">
                        <li>
                            <p>付款单位</p>
                            <input class="form-control" type="text" name="payer" id="acceptUp_payer" disabled />
                        </li>
                        <li>
                            <p class="modItemTtl">汇票号 </p>
                            <input class="form-control" type="text" name="returnNo" value=""/>
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                        <li>
                            <p>金额</p>
                            <input class="form-control" type="text" name="amount" value="20,000.00" id="acceptUp_money" oninput="clearNoNumN(this, 2)">
                        </li>
                        <li>
                            <p>到期日</p>
                            <input class="form-control" type="text" name="expireDate" id="acceptUp_billoutdata"  />
                        </li>
                        <li>
                            <p>出具的银行</p>
                            <input class="form-control" type="text"   name="bankName"  />
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                        <li>
                            <p class="corpTtl">最初出具的单位</p>
                            <input class="form-control" type="text"   name="originalCorp" />
                            <i class="fa fa-times clearInputVal"></i>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="acceptUpdata_sure($(this))">确 定</span>
            </div>
        </div>
    </div>
    <!-- 修改记录 -->
    <div class="bonceContainer bounce-blue" id="recordList" style="width: 500px;">
        <div>
            <div class="bonceHead">
                <span>修改记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td>资料状态</td>
                        <td>修改的结果</td>
                        <td>创建人/修改人</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
            </div>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="recordSee" style="width:800px;">
        <div class="bonceHead">
            <span>承兑汇票查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon align-left">
            <div class="smallSize">
                <div class="clear">
                    <p class="ty-left tTtl">票面信息</p>
                </div>
                <table  class="ty-table">
                    <tbody>
                    <tr>
                        <td width="40%" class="record_snTtl">汇票号</td>
                        <td colspan="3" id="record_billSn"></td>
                    </tr>
                    <tr>
                        <td>金额</td>
                        <td id="record_billAmount"></td>
                    </tr>
                    <tr>
                        <td>到期日</td>
                        <td id="record_billDueDate"></td>
                    </tr>
                    <tr>
                        <td class="record_unitTtl">最初出具的单位</td>
                        <td id="record_billUnit"></td>
                    </tr>
                    <tr>
                        <td>出具的银行</td>
                        <td id="record_billBank"></td>
                    </tr>
                    <tr>
                        <td>付款单位</td>
                        <td id="record_acceptSee_payer"></td>
                    </tr>
                    </tbody>
                </table>
                <p class="paddT tTtl">其他信息</p>
                <table  class="ty-table" >
                    <tbody>
                    <tr>
                        <td width="40%">数据来源</td>
                        <td id="record_acceptSee_item">收入</td>
                    </tr>
                    <tr>
                        <td>用途</td>
                        <td id="record_acceptSee_use"></td>
                    </tr>
                    <tr>
                        <td>摘要</td>
                        <td id="record_acceptSee_abstract"></td>
                    </tr>
                    <tr>
                        <td>收到日期</td>
                        <td id="record_acceptSee_getbilldata"></td>
                    </tr>
                    <tr class="record_sourceIncome">
                        <td>所开具发票或收据的金额</td>
                        <td id="record_acceptSee_billMoney"></td>
                    </tr>
                    <tr class="record_sourceIncome">
                        <td>经手人</td>
                        <td id="record_acceptSee_guy"></td>
                    </tr>
                    <tr class="record_sourceIncome">
                        <td>备注</td>
                        <td id="record_acceptSee_remark"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关  闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>回款票据</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer">
            <input type="hidden" id="showMainConNum" />
            <div class="ty-container">
                <div class="mainCon mainCon1">
                    <ul class="ty-secondTab">
                        <li class="ty-active" onclick="accept()">承兑汇票台账</li>
                        <li onclick="bill()">转账支票台账</li>
                    </ul>
                    <div class="ty-mainData">
                        <div class="opinionCon">
                            <p class="ty-left">以下为所收到且尚在手中的外部承兑汇票，共<span id="total_bill"> </span>张，总计<span id="amount_bill"> </span>元</p>
                            <div class="ty-right link-blue moreBtn" onclick="moreData(2)">更多数据</div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="10%">汇票号后六位</td>
                                <td width="15%">到期日</td>
                                <td width="10%">金额</td>
                                <td width="10%">付款单位</td>
                                <td width="15%">出具的银行</td>
                                <td width="15%">创建</td>
                                <td width="10%">汇票是否修改过</td>
                                <td width="15%">操作</td>
                                </thead>
                                <tbody id="accept_tbody"></tbody>
                            </table>

                        </div>
                        <div class="opinionCon hd">
                            <p class="ty-left">以下为所收到且尚在手中的外部转账支票，共<span id="total"> </span>张，总计<span id="amount"> </span>元</p>
                            <div class="ty-right link-blue moreBtn" onclick="moreData(1)">更多数据</div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="10%">支票号后六位</td>
                                <td width="15%">到期日</td>
                                <td width="10%">金额</td>
                                <td width="10%">付款单位</td>
                                <td width="15%">出具的银行</td>
                                <td width="15%">创建</td>
                                <td width="10%">支票是否修改过</td>
                                <td width="15%">操作</td>
                                </thead>
                                <tbody id="bill_tbody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="mainCon mainCon2">
                    <div class="backPage">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(1);">返  回</span>
                    </div>
                    <div class="more-size">
                        <ul class="moreList">
                            <li>
                                <span>今年收到<span class="panelTtl"></span></span>
                                <span id="thisYearNum">XX张</span>
                                <span id="thisYearAmount">XXXX.XX元</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="periodScan(1)">查  看</span>
                            </li>
                            <li>
                                <span>去年收到<span class="panelTtl"></span></span>
                                <span id="lastYearNum">XX张</span>
                                <span id="lastYearAmount">XXXX.XX元</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="periodScan(2)">查  看</span>
                            </li>
                            <li>
                            <span>前年收到<span class="panelTtl"></span></span>
                                <span id="agoYearNum">XX张</span>
                                <span id="agoYearAmount">XXXX.XX元</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="periodScan(3)">查  看</span>
                            </li>
                        </ul>
                        <div class="moreY">
                            <span>更多年份的数据</span>
                            <input id="moreYSearch" value="" placeholder="请选择年份"/>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="periodScan(4)">确  定</span>
                        </div>
                    </div>
                </div>
                <div class="mainCon mainCon3">
                    <div class="backPage">
                        <input id="prevNum" type="hidden" />
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(1);">返回首页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(2);">返回上一页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" id="draftCountBtn" onclick="draftCount(1);">统  计</span>
                    </div>
                    <div>
                        <p class="periodDetail">XXXX-XX-XX至XXXX-XX-XX期间收到的外部承兑汇票共XX张，XXXXXXX.XX元</p>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td width="10%"><span class="cateTtl"></span>号后六位</td>
                            <td width="15%">到期日</td>
                            <td width="10%">金额</td>
                            <td width="10%">付款单位</td>
                            <td width="15%">出具的银行</td>
                            <td width="15%">创建</td>
                            <td width="10%"><span class="cateTtl"></span>是否修改过</td>
                            <td width="15%">操作</td>
                            </thead>
                            <tbody id="billPeriod"></tbody>
                        </table>
                    </div>
                </div>
                <div class="mainCon mainCon4">
                    <div class="backPage">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(1);">返回首页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(3);">返回上一页</span>
                    </div>
                    <div class="more-size">
                        <p class="periodDetail">XXXX-XX-XX至XXXX-XX-XX期间收到的外部承兑汇票共XX张，XXXXXXX.XX元</p>
                        <ul class="moreList">
                            <li>
                                <span>还在手中的</span>
                                <span id="stillNum">XX张</span>
                                <span id="stillAmount">XXXX.XX元</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="countScan(1)">查  看</span>
                            </li>
                            <li>
                                <span>存入银行的</span>
                                <span id="bankNum">XX张</span>
                                <span id="bankAmount">XXXX.XX元</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="countScan(2)">查  看</span>
                            </li>
                            <li>
                                <span>又付出去的</span>
                                <span id="payNum">XX张</span>
                                <span id="payAmount">XXXX.XX元</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="countScan(4)">查  看</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="mainCon mainCon5">
                    <div class="backPage">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(1);">返回首页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="showMainCon(4);">返回上一页</span>
                    </div>
                    <div>
                        <p class="byCountPeriod">XXXX-XX-XX至XXXX-XX
                            -XX期间收到的外部承兑汇票共XX张，XXXXXXX.XX元</p>
                        <div>
                            <table class="ty-table ty-table-control byCurrently">
                                <thead>
                                <td width="10%"><span class="cateTtl"></span>号后六位</td>
                                <td width="14%">到期日</td>
                                <td width="10%">金额</td>
                                <td width="10%">付款单位</td>
                                <td width="14%">出具的银行</td>
                                <td width="18%">创建</td>
                                <td width="10%"><span class="cateTtl"></span>是否修改过</td>
                                <td width="14%">操作</td>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <table class="ty-table ty-table-control byBank">
                                <thead>
                                <td width="10%"><span class="cateTtl"></span>号后六位</td>
                                <td width="10%">金额</td>
                                <td width="10%">付款单位</td>
                                <td width="22%">到期日/存入银行的日期/到账日期</td>
                                <td width="18%">所存入的银行账户</td>
                                <td width="20%">在系统内操作“存入银行”者</td>
                                <td width="10%">操作</td>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <table class="ty-table ty-table-control byPayOut">
                                <thead>
                                <td width="10%"><span class="cateTtl"></span>号后六位</td>
                                <td width="15%">到期日</td>
                                <td width="10%">金额</td>
                                <td width="10%">付款单位</td>
                                <td width="14%">接收单位</td>
                                <td width="14%">接收日期</td>
                                <td width="18%">在系统内操作“又付出去”者</td>
                                <td width="14%">操作</td>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/finance/replyBill.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>