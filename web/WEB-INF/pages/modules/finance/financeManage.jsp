<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<link rel="stylesheet" type="text/css" href="../css/finance/financeManage.css?v=SVN_REVISION" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="tips" style="width:450px; ">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p style="text-align: center;padding: 10px 0;" class="tipWord"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="editDetails" style="width:650px; ">
        <div class="bonceHead">
            <span>修改详情</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <table class="ty-table ty-table-none">
                <tbody>

                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="fundManagement" style="width: 700px;">
        <div class="bonceHead">
            <span>初始资金管理</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div class="limitWrap">
                <div class="initFund">
                    当前，备用金/现金账户的初始资金为<span id="initAmount"></span>元。
                </div>
                <div class="ty-hr"></div>
                <p class="clear">
                    如该金额与实际不符，可修改。
                    <span class="ty-right ty-btn ty-btn-gray" id="initFundBtn">修改</span>
                </p>
                <p class="clear">
                    备用金/现金账户初始资金的修改记录
                    <span class="ty-right ty-btn ty-btn-blue" onclick="initFundRecordDetail(1)">查看</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关 闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="new-finance" style="width:450px; ">
        <div class="bonceHead">
            <span>新增银行账户</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <%--<form form class="form-horizontal" method="post" action="${pageContext.request.contextPath}/finance/addAccount.do" id="form1">--%>
                <input type="hidden" name="accountType" value="2">
                <table class="ty-table ty-table-none">
                    <tbody id="add_account">
                    <tr>
                        <td class="form-title"><span class="ty-color-red">* </span>账户类型：</td>
                        <td class="form-con">
                            <div class="radioBox" id="operationRadio">
                                <span class="ty-radio" value="对公户">
                                    <i class="fa fa-circle-o"></i>
                                    <span>对公户</span>
                                </span>
                                <span class="ty-radio" value="非对公户">
                                    <i class="fa fa-circle-o"></i>
                                    <span>非对公户</span>
                                </span>
                                <input id="new-isPublic" type="hidden" name="operation" value="1">
                            </div>
                        </td>
                    </tr>
                    <tr class="hd" id="baseAccountSet">
                        <td></td>
                        <td class="form-con">
                            <div class="radioBox">
                                <span class="ty-radio" value="基本户" id="baseAccount">
                                    <i class="fa fa-circle-o"></i>
                                    <span>基本户</span>
                                </span>
                                <span class="ty-radio" value="非基本户" onclick="publicOption($(this))">
                                    <i class="fa fa-circle-o"></i>
                                    <span>非基本户</span>
                                </span>
                                <input type="hidden" id="new-isBasic" value="1">
                            </div>
                        </td>
                    </tr>
                    <tr class="entrySect">
                        <td class="form-title"><span class="ty-color-red">* </span>账户名称：</td>
                        <td class="form-con">
                            <div class="pack">
                                <input type="text" class="ty-inputText accountName" name="name">
                             <%--   <i class="fa fa-times-circle chacha"> </i>--%>
                            </div>
                        </td>
                    </tr>
                    <tr class="entrySect">
                        <td class="form-title"><span class="ty-color-red">* </span>开户行：</td>
                        <td class="form-con">
                            <input type="text" class="ty-inputText bankName"  placeholder="" name="bankName">
                        </td>
                    </tr>
                    <tr class="entrySect">
                        <td class="form-title"><span class="ty-color-red">* </span>账号：</td>
                        <td class="form-con">
                            <input type="text" class="ty-inputText account"  placeholder="" name="account" onkeyup="clearNumO(this)">
                        </td>
                    </tr>
                    <tr class="entrySect">
                        <td class="form-title"><span class="ty-color-red">* </span>初始资金：</td>
                        <td class="form-con">
                            <input type="text" class="ty-inputText balance" value="0.00" placeholder="" name="balance"  onkeyup="clearNoNumO(this)">
                        </td>
                    </tr>
                    <tr class="entrySect">
                        <td class="form-title">可否取现：</td>
                        <td class="form-con">
                            <div class="radioBox radioDisabled" id="select2Radio">
                                            <span class="ty-radio" value="1">
                                                <i class="fa fa-circle-o"></i>
                                                <span>可取现</span>
                                            </span>
                                <span class="ty-radio" value="2">
                                                <i class="fa fa-dot-circle-o"></i>
                                                <span>不可取现</span>
                                            </span>
                                <input type="hidden" id="cashable" value="2">
                            </div>
                        </td>
                    </tr>
                    <tr class="entrySect">
                        <td class="form-title">备注：</td>
                        <td class="form-con">
                            <input type="text" class="ty-inputText memo"  placeholder="" name="memo">
                            <input type="hidden" class="isPublic"  placeholder="" name="isPublic" value="2">
                            <%--<input type="text" class="hd accoutType"  placeholder="" name="accoutType" value="2">--%>
                        </td>
                    </tr>
                    </tbody>
                </table>
            <%--</form>--%>
            <%--<form class="form-horizontal" method="post" action="${pageContext.request.contextPath}/finance/addAccount.do" id="form1">--%>
            <%--<input type="hidden" name="accountType" value="2">--%>
            <%--<div class="form-group">--%>
            <%--<label class="col-md-3 control-label">账户名称</label>--%>
            <%--<div class="col-md-9">--%>
            <%--<input type="text" class="form-control input-inline input-medium required"  placeholder="" name="name">--%>
            <%--</div>--%>
            <%--</div>--%>
            <%--<div class="form-group">--%>
            <%--<label class="col-md-3 control-label">开户行</label>--%>
            <%--<div class="col-md-9">--%>
            <%--<input type="text" class="form-control input-inline input-medium required" placeholder="" name="bankName">--%>
            <%--&lt;%&ndash;<span class="help-inline"> 请输入开户行 </span>&ndash;%&gt;--%>
            <%--</div>--%>
            <%--</div>--%>
            <%--<div class="form-group">--%>
            <%--<label class="col-md-3 control-label">账号</label>--%>
            <%--<div class="col-md-9">--%>
            <%--<input type="text" class="form-control input-inline input-medium required number"  placeholder="" name="account">--%>
            <%--</div>--%>
            <%--</div>--%>
            <%--<div class="form-group">--%>
            <%--<label class="col-md-3 control-label">初始资金</label>--%>
            <%--<div class="col-md-9">--%>
            <%--<input type="text" class="form-control input-inline input-medium"  placeholder="" id="balance" name="balance">--%>
            <%--</div>--%>
            <%--</div>--%>
            <%--<div class="form-group">--%>
            <%--<label class="col-md-3 control-label">账户类型</label>--%>
            <%--<div class="col-md-9">--%>
            <%--<select class="form-control input-inline input-medium" name="operation" id="operation" onchange="linkCash(this)">--%>
            <%--</select>--%>
            <%--</div>--%>
            <%--</div>--%>
            <%--<div class="form-group">--%>
            <%--<label class="col-md-3 control-label">可否取现</label>--%>
            <%--<div class="col-md-9">--%>
            <%--<select class="form-control input-inline input-medium" name="cashable" id="select2">--%>

            <%--</select>--%>
            <%--</div>--%>
            <%--</div>--%>
            <%--<div class="form-group">--%>
            <%--<label class="col-md-3 control-label">备注</label>--%>
            <%--<div class="col-md-9">--%>
            <%--<textarea class="form-control" rows="5" name="memo"></textarea>--%>
            <%--</div>--%>
            <%--</div>--%>
            <%--<input type="hidden" name="fid" value="${sessionScope.user.userID}">--%>
            <%--</form>--%>
        </div>
        <div class="bonceFoot entrySect">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 newFinanceBtn" onclick="sureNewFinance()">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="checkAccount" style="width:450px; ">
        <div class="bonceHead">
            <span>查看账户信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td class="form-title">账户类型：</td>
                    <td class="form-con">
                        <span class="check_operation"></span>
                    </td>
                </tr>
                <tr>
                    <td class="form-title">账户名称：</td>
                    <td class="form-con">
                        <span class="check_accountName"></span>
                    </td>
                </tr>
                <tr>
                    <td class="form-title">开户行：</td>
                    <td class="form-con">
                        <span class="check_bankName"></span>
                    </td>
                </tr>
                <tr>
                    <td class="form-title">账号：</td>
                    <td class="form-con">
                        <span class="check_account"></span>
                    </td>
                </tr>
                <tr>
                    <td class="form-title">当前状态：</td>
                    <td class="form-con">
                        <span class="check_nowState">正常</span>
                    </td>
                </tr>
                <tr>
                    <td class="form-title">初始资金：</td>
                    <td class="form-con">
                        <span class="check_balanceInit"></span>
                    </td>
                </tr>
                <tr>
                    <td class="form-title">当前余额：</td>
                    <td class="form-con">
                        <span class="check_balance"></span>
                    </td>
                </tr>
                <tr>
                    <td class="form-title">备注：</td>
                    <td class="form-con">
                        <span class="check_memo"></span>
                    </td>
                </tr>
                <tr style="margin-top:20px;">
                    <td class="form-title">账户创建时间：</td>
                    <td class="form-con">
                        <span class="check_createDate"></span>
                    </td>
                </tr>
                <tr>
                    <td class="form-title">创建人：</td>
                    <td class="form-con">
                        <span class="check_createName"></span>
                    </td>
                </tr>
                </tbody>
            </table>
            <table class="ty-table ty-table-none checkRecord">
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--creator:lyt date:2018/9/19 银行账户-备用金-修改--%>
    <div class="bonceContainer bounce-blue" id="basicAccountEdit">
        <div class="bonceHead">
            <span>修改</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div class="editCon">
                <span class="form-title"><span class="ty-color-red">* </span>初始资金</span>
                <input id="updateBasic" type="text" onkeyup="clearNoNumO(this)"/>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateAccountSure(1)">确定</span>
        </div>
    </div>
    <%--creator:lyt date:2018/9/19 银行账户-非备用金-修改--%>
    <div class="bonceContainer bounce-blue" id="accountEdit">
        <div class="bonceHead">
            <span>修改账户信息</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <input type="hidden" name="accountType" value="2">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td class="form-title"><span class="ty-color-red">* </span>账户类型：</td>
                    <td class="form-con">
                        <div class="update_publicType">
                            <span></span>
                            <span>对公户</span>
                            <input type="hidden" id="update_isPublic" value="1">
                        </div>
                    </td>
                </tr>
                <tr id="public-select">
                    <td></td>
                    <td class="form-con">
                        <div class="radioBox" id="operationBtn">
                            <span class="ty-radio" value="基本户">
                                <i class="fa fa-circle-o"></i>
                                <span>基本户</span>
                            </span>
                            <span class="ty-radio" value="非基本户">
                                <i class="fa fa-circle-o"></i>
                                <span>非基本户</span>
                            </span>
                            <input type="hidden" id="update_isBasic" value="1">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="form-title"><span class="ty-color-red">* </span>账户名称：</td>
                    <td class="form-con">
                        <div class="pack">
                            <input type="text" class="ty-inputText update_accountName"  placeholder="" name="name" />
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="form-title"><span class="ty-color-red">* </span>开户行：</td>
                    <td class="form-con">
                        <input type="text" class="ty-inputText update_bankName"  placeholder="" name="bankName">
                    </td>
                </tr>
                <tr>
                    <td class="form-title"><span class="ty-color-red">* </span>账号：</td>
                    <td class="form-con">
                        <input type="text" class="ty-inputText update_account"  placeholder="" name="account" onkeyup="clearNumO(this)">
                    </td>
                </tr>
                <tr>
                    <td class="form-title">可否取现：</td>
                    <td class="form-con">
                        <div class="radioBox radioDisabled" id="editCaseRadio">
                                        <span class="ty-radio" value="1">
                                            <i class="fa fa-circle-o"></i>
                                            <span>可取现</span>
                                        </span>
                            <span class="ty-radio" value="2">
                                            <i class="fa fa-dot-circle-o"></i>
                                            <span>不可取现</span>
                                        </span>
                            <input type="hidden" name="cashable" value="2">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="form-title"><span class="ty-color-red">* </span>初始资金：</td>
                    <td class="form-con">
                        <input type="text" class="ty-inputText update_balance" value="0.00" placeholder="" name="initialAmount"  onkeyup="clearNoNumO(this)">
                    </td>
                </tr>
                <tr>
                    <td class="form-title">备注：</td>
                    <td class="form-con">
                        <input type="text" class="ty-inputText update_memo"  placeholder="" name="memo">
                        <input type="hidden" class="isPublic"  placeholder="" name="isPublic" value="2">
                        <input type="hidden" class="accoutType"  placeholder="" name="accoutType" value="2">
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="updateConfirm" onclick="updateAccountSure(2)">确定</button>
        </div>
    </div>
    <%--creator:lyt date:2018/9/19 银行账户-备用金/现金账户初始资金的修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="seeAccountEditRecord" style="width:550px;">
        <div class="bonceHead">
            <span>备用金/现金账户初始资金的修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>修改时间</td>
                    <td>修改前金额</td>
                    <td>修改后金额</td>
                    <td>修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--creator:lyt date:2018/9/19 银行账户-非备用金-修改记录列表--%>
    <div class="bonceContainer bounce-blue" id="accountEditRecord">
        <div class="bonceHead">
            <span>账户修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>修改时间</td>
                    <td>修改人</td>
                    <td>详情</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--creator:lyt date:2018/9/19 银行账户-非备用金-关闭账户--%>
    <div class="bonceContainer bounce-blue" id="closeTip">
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div>
                <div class="shu1">
                    <p id="close_tip_ms" style="text-align: center; padding:10px 0">您确定要关闭该账户吗？ </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 canClose" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 canClose updateAccountState" onclick="">确定</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 hd notClose" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
</div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>账户管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <div class="mainContent">
                    <div class="screenBody">
                        <span>数据筛选</span>
                        <select id="searchOrg" onchange="screenOrgData($(this))">
                            <option></option>
                        </select>
                    </div>
                    <div class="pull-right">
                        <c:if test="${userType!='super'}">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="newBank()">新增银行账户</span>
                            <%-- <a class="ty-btn ty-btn-green ty-btn-big ty-circle-5" href="${pageContext.request.contextPath}/finance/initCashFinance.do">初始化现金账户</a>--%>
                        </c:if>
                    </div>
                    <div class="clr"></div>
                    <br>
                    <div class="countStr">下表的银行账户中，对公户共0个，非对公户共0个</div>
                    <div class="ty-mainData">
                        <span id="operationStatus" class="hd">${status}</span>
                        <table class="ty-table ty-table-control accountsList">
                            <thead>
                            <tr>
                                <td> 账户名称</td>
                                <td> 开户行 </td>
                                <td> 账号 </td>
                                <td> 账户类型 </td>
                                <td class="belongOrg"> 所属的机构 </td>
                                <td> 当前余额 </td>
                                <td> 创建时间 </td>
                                <td> 当前状态 </td>
                                <td> 操作 </td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/function.js?v=SVN_REVISION"></script>
<script src="../script/jquery.validate.min.js?v=SVN_REVISION"></script>
<script src="../script/messages_zh.min.js?v=SVN_REVISION"></script>
<script src="../script/finance/financelManage.js?v=SVN_REVISION"></script>
</body>
</html>
