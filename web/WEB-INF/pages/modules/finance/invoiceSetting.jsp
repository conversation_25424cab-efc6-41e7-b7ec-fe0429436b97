<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include file="../../common/headerTop.jsp" %>
<link rel="stylesheet" href="../css/general/invoiceSetting.css?v=SVN_REVISION">
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed2">
    <%-- 是否确定启用税率 --%>
    <div class="bonceContainer bounce-green" id="startTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p style="text-align: center; padding:10px 0"> 您确定要启用税率 <span class="ty-color-blue" id="stLv">12%</span> 吗？
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="startLv()">确定</span>
        </div>
    </div>
    <%-- 查看修改记录已停用的税率 --%>
    <div class="bonceContainer bounce-blue" id="scanStopLvs2">
        <div class="bonceHead">
            <span>已停用的税率</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:300px;overflow:auto;  ">
            <table class="ty-table ty-table-control">
                <thead>
                <td>税率</td>
                <td>停用时间</td>
                <td>停用者</td>
                <td>启用时间</td>
                <td>启用者</td>
                <td>创建时间</td>
                <td>创建者</td>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-green" id="F_tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%-- 新增税率 --%>
    <div class="bonceContainer bounce-blue" id="addLvCon">
        <div class="bonceHead">
            <span>新增税率</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">
            <input type="text" placeholder="请输入税率" id="add_lv" onkeyup="clearNoNum(this)"/> %
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="addLvOk()">确定</span>
        </div>
    </div>
    <%-- 发票设置 修改记录详情 --%>
    <div class="bonceContainer bounce-green" id="editeLogInfo">
        <div class="bonceHead">
            <span id="infoTtl">修改前详情</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div> <%-- 0 --%>
                <input type="hidden" id="logID">
                <span class="ttl">发票种类</span> <span id="logCat"></span>
            </div>
            <div> <%-- 1 --%>
                <span class="ttl">发票有无附页</span> <span id="logFu"></span>
            </div>
            <div><%-- 2 --%>
                <span class="ttl" id="infoHang">每张发票可开的行数上限</span><span id="logHang"></span>
            </div>
            <div> <%-- 3 --%>
                <span class="ttl">同一张发票上是否允许选择不同税率的商品</span><span id="logMultipleRate"></span>
            </div>
            <div> <%-- 4 --%>
                <span class="ttl">可供选择的税率</span>
                <span id="stopLv2" onclick="scanStopLvs(1)">已停用的税率</span>
                <div id="lvs_info"></div>
            </div>
            <div><%-- 5 --%>
                <span class="ttl" id="amountTtl2">每张发票可开的金额上限（不含税）</span> <span id="logAmount"></span> 元
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 查看停用的税率 --%>
    <div class="bonceContainer bounce-blue" id="scanStopLvs">
        <div class="bonceHead">
            <span>已停用的税率</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:300px;overflow:auto;  ">
            <table class="ty-table ty-table-control">
                <thead>
                <td>税率</td>
                <td>停用时间</td>
                <td>停用者</td>
                <td>启用时间</td>
                <td>启用者</td>
                <td>创建时间</td>
                <td>创建者</td>
                <td>操作</td>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>

</div>
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-green" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 发票设置 --%>
    <div class="bonceContainer bounce-green" id="invoiceSet">
        <div class="bonceHead">
            <span id="invoiceSetTtl">发票设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <input type="hidden" id="type">
            <input type="hidden" id="id">
            <input type="hidden" id="disableTaxRate">
            <div id="setCatoryCon"> <%-- 0 --%>
                <p>请选择发票种类</p>
                <input type="hidden" id="setCatory">
                <span class="item_i" id="setCatory1" >
                    <span class="mask"></span>
                    <span onclick="setCatory($(this) , 1)">
                        <i class="fa fa-circle-o"></i> 增值税专用发票
                    </span>
                </span>
                <span class="item_i" id="setCatory2">
                    <span class="mask"></span>
                    <span onclick="setCatory($(this) , 2)">
                        <i class="fa fa-dot-circle-o"></i> 增值税普通发票
                    </span>
                </span>
                <span class="item_i" id="setCatory3">
                    <span class="mask"></span>
                    <span onclick="setCatory($(this) , 3)">
                        <i class="fa fa-dot-circle-o"></i> 其他普通发票
                    </span>
                </span>
            </div>
            <div> <%-- 1 --%>
                <span class="ttl">发票有无附页</span>
                <span class="item_i" id="setFu1" onclick="setFu($(this) , 1)"><i
                        class="fa fa-dot-circle-o"></i> 有</span>
                <span class="item_i" id="setFu0" onclick="setFu($(this) , 0)"><i class="fa fa-circle-o"></i> 无</span>
                <input type="hidden" id="setFu">
            </div>
            <div><%-- 2 --%>
                <span class="ttl">每张发票可开的行数上限 <i>*</i></span>
                <input type="text" onkeyup="clearNum(this)" id="hangNum"> 行
            </div>
            <div> <%-- 3 --%>
                <span class="ttl">同一张发票上是否允许选择不同税率的商品</span>
                <span class="item_i" id="setDiffLv1" onclick="setDiffLv($(this) , 1)"><i class="fa fa-dot-circle-o"></i> 是</span>
                <span class="item_i" id="setDiffLv0" onclick="setDiffLv($(this) , 0)"><i
                        class="fa fa-circle-o"></i> 否</span>
                <input type="hidden" id="setDiffLv">
            </div>
            <div> <%-- 4 --%>
                <span class="ttl">请输入可供选择的税率 <i>*</i></span>
                <a id="addLv" onclick="addLv()"> + 增加新税率 </a>
                <span id="stopLv" onclick="scanStopLvs()">已停用的税率</span>
                <div id="lvs"></div>
            </div>
            <div><%-- 5 --%>
                <span class="ttl" id="amountTtl"></span>
                <input type="text" onkeyup="clearNum(this)" id="amountNum"> 元
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" id="addInvoiceOk">确定</span>
        </div>
    </div>
    <%-- 发票设置修改记录 --%>
    <div class="bonceContainer bounce-blue" id="editLog">
        <div class="bonceHead">
            <span>发票设置修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <input type="hidden" id="logSetID">
            <table class="ty-table ty-table-control" id="editLogTb">
                <tr>
                    <td width="30%">修改时间</td>
                    <td width="20%">修改人</td>
                    <td width="25%">修改前</td>
                    <td width="25%">修改后</td>
                </tr>
                <tr>
                    <td>修改时间</td>
                    <td>修改人</td>
                    <td class="ty-td-control"><span onclick="editeLogInfo($(this) , 0)" class="ty-color-blue">查看</span>
                    </td>
                    <td class="ty-td-control"><span onclick="editeLogInfo($(this) , 1)" class="ty-color-blue">查看</span>
                    </td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include file="../../common/contentHeader.jsp" %>
<div class="page-container">
    <%@ include file="../../common/contentSliderNav.jsp" %>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>发票设置</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container">
                <span id="navControl"></span>
                <button class="ty-btn ty-btn-gray ty-btn-big ty-circle-5 ty-left" id="addBtn">发票设置</button>
                <div class="clr"></div>
                <br>

                <div class="ty-mainData">
                    <table class="ty-table ty-table-control" id="mainList">
                        <thead>
                        <tr>
                            <td width="15%">本公司可开的发票种类</td>
                            <td width="10%">有无附页</td>
                            <td width="10%">行数</td>
                            <td width="10%">税率</td>
                            <td width="15%">金额上限</td>
                            <td width="15%">创建时间</td>
                            <td width="10%">创建人</td>
                            <td width="15%">操作</td>
                        </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>
<%@ include file="../../common/contentSliderLitbar.jsp" %>
</div>
<%@ include file="../../common/footerTop.jsp" %>
<%@ include file="../../common/footerScript.jsp" %>
<script src="../script/finance/invoiceSetting.js?v=SVN_REVISION"></script>

</body>
</html>





