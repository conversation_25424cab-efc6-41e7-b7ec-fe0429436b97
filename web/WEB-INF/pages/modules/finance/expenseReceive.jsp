<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" href="../css/finance/expenseReceive.css?v=SVN_REVISION">
<link href="../css/common/process.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>
<style>
    .tip{
        text-align: center;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 btn" data-name="changeState">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="newCostCategory">
        <div class="bonceHead">
            <span>新增费用类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: auto">
                <p><span class="ty-color-red">!!重要提示</span></p>
                <p>您新增的费用类别<span class="ty-color-red">不可包含需入库及领用的各类物品</span></p>
                <p>需入库及领用的各类物品使用采购模块进行管理，方可确保系统使用的最佳效果</p>
                <div style="width: 260px; margin: auto">类别名称：<input type="text" class="ty-inputText" name="costCategory" require></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-name="newCostCategory" disabled>确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>报销设置</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;min-width:1200px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="limitBody">
                    <div class="main">
                        <div class="gap-tp">
                            <p>在系统内进行非采购类的报销时，报销者需选择“费用类别”与“票据种类”。</p>
                            <div>
                                <p class="costTip">“费用类别”与“票据种类”均有各自的选项。对“费用类别”选项的管理需在本页面进行。</p>
                                <p class="billTypeTip">“费用类别”与“票据种类”均有各自的选项。此二者选项的管理需在本页面进行。</p>
                            </div>
                        </div>
                        <ul class="ty-secondTab">
                            <li class="ty-active">费用类别</li>
                            <li>票据种类</li>
                        </ul>
                        <div class="ty-mainData">
                            <div class="tblContainer">
                                <div class="gap-tp clear">
                                    如需要，可改变各“费用类别”的状态，可“新增费用类别”。
                                    <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="newCostCategoryBtn">新增费用类别</span>
                                </div>
                                <table class="ty-table ty-table-control fee">
                                    <thead>
                                    <td width="14%">序号</td>
                                    <td width="30%">费用类别名称</td>
                                    <td width="28%">状态</td>
                                    <td width="28%">操作</td>
                                    </thead>
                                    <tbody id="category"></tbody>
                                </table>
                            </div>
                            <div class="tblContainer" style="display: none">
                                <p>系统内“票据种类”的选项如下表。系统不支持对“票据种类”选项的编辑。</p>
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="30%">序号</td>
                                    <td width="70%">种类名称</td>
                                    </thead>
                                    <tbody id="billType"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="second" style="display: none">
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="back()">返回</span>
                        <div class="tblContainer">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>序号</td>
                                    <td>二级费用类别</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript" src="../script/finance/expenseReceive.js?v=SVN_REVISION" ></script>
<%@ include  file="../../common/footerBottom.jsp"%>