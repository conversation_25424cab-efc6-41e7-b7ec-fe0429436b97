<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" href="../css/finance/expenseReceive.css?v=SVN_REVISION">
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <div class="bonceContainer bounce-red" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="tip_cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="confirm">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip" style="text-align: center; padding:10px 0"></p>
                    <div class="hd">
                        <span id="setType"></span>
                        <span id="catID"></span>
                    </div>

                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="setSatus()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="cata_addAccount">
        <div class="bonceHead">
            <span>新增费用类别</span>
            <a class="bounce_close" onclick="catagory_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="sale_delete">
                    <p style="text-align:center;padding:10px 0;" class="catagory_Input">
                        <span>类别名称</span>
                        <span>:</span>
                        <input type="text" name="" id="cateroy_name">
                    </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="catagory_addsure()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="catagory_cancel()">取消</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="catagory_stopAccount">
        <div class="bonceHead">
            <span>系统提示</span>
            <a class="bounce_close" onclick="catastop_close()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p style="text-align:center;padding:10px 0;" id="cateroyDelTip"></p>
                    <p id="del_cateroyID" class="hd"></p>
                    <p> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="catadel_sure()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="catadel_cancel()">取消</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>报销受理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;min-width:1200px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div>
                    <p><span id="third_n">类别设置</span> </p>
                    <ul class="ty-firstTab">
                        <li onclick="location.href = 'expenseReceive.do'">报销受理</li>
                        <li class="ty-active" onclick="location.href = 'expenseReceiveCateroy.do'">类别设置</li>
                    </ul>
                    <div class="ty-right" style="margin-top: -40px">
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="category_addbtn" onclick="category_addbtn($(this))">新增费用类别</span>
                    </div>
                    <div class="clr"></div>
                    <br>
                </div>
                <ul class="ty-secondTab">
                    <li class="ty-active">费用类别</li>
                    <li>票据种类</li>
                </ul>
                <div class="ty-mainData">
                    <div class="opinionCon">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td width="16%">序号</td>
                            <td width="42%">费用类别名称</td>
                            <td width="42%">操作</td>
                            </thead>
                            <tbody id="category"></tbody>
                        </table>
                    </div>
                    <div class="opinionCon category_table" style="display: none">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td width="30%">序号</td>
                            <td width="70%">种类名称</td>
                            </thead>
                            <tbody id="billType"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript" src="../script/finance/expenseReceiveCateroy.js?v=SVN_REVISION" ></script>
<%@ include  file="../../common/footerBottom.jsp"%>