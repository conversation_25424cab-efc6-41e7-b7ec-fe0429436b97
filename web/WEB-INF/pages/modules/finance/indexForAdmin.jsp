<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <%--<link href="${pageContext.request.contextPath}/css/dateL.css" rel="stylesheet" type="text/css">--%>
    <link href="../css/finance/indexForAdmin.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
    <link href="../css/common/process.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">

    <div class="bonceContainer bounce-blue" id="oppositeAddDiv" style="width: 446px;">
        <div class="bonceHead">
            <span>新增收款单位</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon" >
            <div>
                <p>请录入收款单位（含个人）的信息。</p>
                <p>供应商需由采购部门管理，公司职工需由总务部门管理，故请勿在此处新增供应商或公司职工！</p>
                <p class="e3">
                    <span><i class="ty-color-red">*</i>收款单位名称</span>
                    <input type="text" placeholder="请录入" id="oopName">
                </p>
                <p class="e3">
                    <span style="position: relative; top: -75px;">说明</span>
                    <textarea placeholder="请录入" id="oopMemo" onkeyup="setNum($(this))" style=" width: 229px; height: 91px;"></textarea>
                    <span class="showNum" style=" width: 50px;"></span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addOopOK()">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="oppositeCorpDiv">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce.cancel();$('#stakeholderCategory').hide();"></a>
        </div>
        <div class="bonceCon" style="max-height: 300px;">
            <div>
                <ul id="chooseC">
                    <li><i class="fa fa-circle-o"></i><span>撒的发生的</span></li>
                    <li><i class="fa fa-circle-o"></i><span>撒的发生的</span></li>
                    <li><i class="fa fa-circle-o"></i><span>撒的发生的</span></li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); $('#stakeholderCategory').hide();">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="chooseCOK()">确定</span>
        </div>
    </div>
    <%--温馨提示--%>
    <div class="bonceContainer bounce-green" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p class="tipWord" style="text-align: center; padding:10px 0"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="outerCheck" style="width: 900px">
        <div class="bonceHead">
            <span>选择外部支票：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="acceptCheck">
                <table class="ty-table">
                    <thead>
                    <tr>
                        <td></td>
                        <td>支票号</td>
                        <td>金额</td>
                        <td>出具支票银行</td>
                        <td>支票到期日</td>
                    </tr>
                    </thead>
                    <tbody id="outerCheckCon"></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureOuterCheck($(this))">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="acceptCheck" style="width: 900px">
        <div class="bonceHead">
            <span>选择承兑汇票：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="acceptCheck">
                <table class="ty-table">
                    <thead>
                    <tr>
                        <td></td>
                        <td>收到汇票日期</td>
                        <td>付款单位</td>
                        <td>金额</td>
                        <td>汇票号</td>
                        <td>汇票到期日</td>
                        <td>原始出具汇票单位</td>
                        <td>出具汇票银行</td>
                    </tr>
                    </thead>
                    <tbody id="acceptCheckCon"></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureAcceptCheck($(this))">确定</span>
        </div>
    </div>
    <%-- 综合查看 / --%>
    <div class="bonceContainer bounce-blue" id="generalScan">
        <div class="bonceHead">
            <span class="bonceHeadName" id="generalScanTtl">综合查看 / 本笔借款不再付款 </span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 600px; overflow-y: auto">
            <div class="ordLoanDetail">
                <div class="item">
                    <div class="col-md-4"><span class="item-title">出资方</span><span class="item-content lender"></span></div>
                    <div class="col-md-4"><span class="item-title">借款方</span><span class="item-content borrower"></span></div>
                    <div class="col-md-4"><span class="item-title">本金金额</span><span class="item-content principalAmount"></span></div>
                </div>
                <div class="item">
                    <div class="col-md-4"><span class="item-title">名义利率</span><span class="item-content nominalRate"></span></div>
                    <div class="col-md-4">
                        <span class="item-title">本金型式</span><span class="item-content incomeMethod"></span>
                    </div>
                    <div class="col-md-4 tansCommon trans3">
                        <span class="item-title">支票类别</span>
                        <span class="item-content withinOrAbroad"> </span>
                    </div>
                    <div class="col-md-4 tansCommon trans4">
                        <span class="item-title"><span>汇票号</span></span><span class=" item-content  billNo"></span> </div>
                </div>

                <div class="incomeMethodCon1 incomeMethodCon">
                    <%--转账支票/内部 --%>
                    <div class="inOrOut1 tansCommon ">
                        <div class="item">
                            <div class="col-md-4"> <span class="item-title">银行账户</span> <span class=" item-content accountId"  ></span> </div>
                            <div class="col-md-4"> <span class="item-title">支票号</span> <span class=" item-content billNo" ></span> </div>
                            <div class="col-md-4"> <span class="item-title">支票到期日</span><span class=" item-content  billEndDate"></span> </div>
                        </div>
                    </div>
                    <%--转账支票 /外部 --%>
                    <div class="inOrOut0 tansCommon ">
                        <div class="item">
                            <div class="col-md-4"> <span class="item-title">支票号</span> <span class="item-content billNo"></span> </div>
                        </div>
                    </div>
                    <%-- 付款信息 --%>
                    <div class="trans1 trans3 trans4 transFu item tansCommon trans">
                        <div class="col-md-4 fu2">
                            <span class="item-title">付款日期</span>
                            <span class=" item-content paymentDate"></span>
                        </div>
                        <div class="col-md-4 fu1">
                            <span class="item-title">收款日期</span>
                            <span class=" item-content paymentDate"></span>
                        </div>
                        <div class="col-md-4 fu2">
                            <span class="item-title">付款经手人</span>
                            <span class=" item-content operatorName"></span>
                        </div>
                        <div class="col-md-4 fu1 fu2">
                            <span class="item-title">收款经手人</span>
                            <span class=" item-content partnerName"></span>

                        </div>
                    </div>
                    <%--银行转账--%>
                    <div class="trans5 item tansCommon"  style="display: none">
                        <div class="col-md-4">
                            <span class="item-title">付款日期</span>
                            <span class=" item-content paymentDate"></span>
                        </div>
                        <div class="col-md-4">
                            <span class="item-title">付款银行</span>
                            <span class=" item-content receiveBank"></span>
                        </div>
                        <div class="col-md-4">
                            <span class="item-title">付款经手人</span>
                            <span class=" item-content operatorName"></span>
                        </div>
                    </div>
                    <%--银行转账--%>
                    <div class="trans21 item tansCommon"  style="display: none">
                        <div class="col-md-4">
                            <span class="item-title">收款日期</span>
                            <span class=" item-content paymentDate"></span>
                        </div>
                        <div class="col-md-4">
                            <span class="item-title">收款经手人</span>
                            <span class=" item-content operatorName"></span>
                        </div>
                    </div>
                    <div class="trans5 item tansCommon"  style="display: none">
                        <div class="col-md-4">
                            <span class="item-title">借款方账户名称</span>
                            <span class=" item-content oppositeAccount"></span>

                        </div>
                        <div class="col-md-4">
                            <span class="item-title">开户行</span>
                            <span class=" item-content oppositeBankno"></span>
                        </div>
                        <div class="col-md-4">
                            <span class="item-title">账号</span>
                            <span class=" item-content oppositeBankcode"></span>
                        </div>
                    </div>
                </div>

                <div class="incomeMethodCon2 incomeMethodCon">
                    <div class="trans1 trans"></div>
                    <%--转账支票/承兑汇票--%>
                    <div class="trans3 trans4" style="display: none">
                        <div class="item">
                            <div class="col-md-4"> <span class="item-title"><span class="trans3">收到支票日期</span><span class="trans4">收到汇票日期</span></span> <span class="item-content billReceiveDate"></span> </div>
                            <div class="col-md-4"> <span class="item-title"><span class="trans3">支票号</span><span class="trans4">汇票号</span></span> <span class="item-content billNo">汇票号</span> </div>
                            <div class="col-md-4"> <span class="item-title"><span class="trans3">支票到期日</span><span class="trans4">汇票到期日</span></span> <span class="item-content billEndDate">汇票到期日</span> </div>
                        </div>
                        <div class="item">
                            <span class="col-md-4"> <span class="item-title"><span class="trans3">出具支票单位</span><span class="trans4">原始出具汇票单位</span></span> <span class="item-content billSource">原始出具汇票单位</span> </span>
                            <span class="col-md-4"> <span class="item-title"><span class="trans3">出具支票银行</span><span class="trans4">出具汇票银行</span></span> <span class="item-content billBank">出具汇票银行</span> </span>
                        </div>
                    </div>
                    <%--转账银行--%>
                    <div class="trans5" style="display: none">
                        <div class="item">
                            <div class="col-md-4"> <span class="item-title">到账日期</span> <span class="item-content arriveDate"></span> </div>
                            <div class="col-md-8"> <span class="item-title">收款银行</span> <span style="width: 300px;" class="item-content receiveBank"></span></div>
                        </div>
                    </div>
                </div>

                <div class="item">
                    <div class="col-md-4">
                        <span class="item-title">归还本金的约定</span><span class="item-content repaymentDate"></span>
                    </div>
                </div>

                <div class="item">
                    <div class="col-md-8">
                        <span class="item-title">利息的支付方式</span><span class="item-content interestMethod"></span>
                    </div>
                </div>
                <div>
                    <div class="interest0 interest"></div>
                    <div class="item interest2 interest3" style="display: none">
                        <div class="col-md-4"> <span class="item-title">开始计息日期</span><span class="item-content interestAccrualDate"></span></div>
                        <div class="col-md-4"> <span class="item-title"><span class="interest2">每月还款日</span><span class="interest3">每年还款日</span></span><span class="item-content periodRepaymentDate"></span></div>
                        <div class="col-md-4"> <span class="item-title">每次应付金额</span><span class="item-content periodRepaymentAmount"></span></div>
                    </div>
                </div>
                <div class="item">
                    <div class="col-md-12"><span class="item-title">备注</span><span class="item-content memo"></span></div>
                </div>
            </div>
            <div class="recordInfo" style="margin-top: 8px">
                <div class="recordList"></div>
            </div>
            <div class="repaymentList">
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td id="logttl0">已付款金额</td>
                        <td class="sumRepaymentAmount"></td>
                        <td ></td>
                        <td>本金金额</td>
                        <td class="principalAmount"></td>
                    </tr>
                    <tr>
                        <td id="logttl1">付款日期</td>
                        <td id="logttl2">付款金额</td>
                        <td id="logttl3">支付方式</td>
                        <td>录入者</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="handleBtn">
                <span class="ty-btn end bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn end bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
            </div>
        </div>
    </div>
    <%--报销数据查看--%>
    <div class="bonceContainer bounce-blue" id="reimburse" style="width: 1200px">
        <div class="bonceHead">
            <span class="bonceHeadName">报销查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  style="height: 600px; overflow-y: auto">
            <div class="processStep">
                <div class="line"><div><div></div></div></div>
                <div class="steps">
                </div>
            </div>
            <div class="processList">
                <div class=""></div>
            </div>
            <div class="" style="margin-top: 12px">
                <div><div class="item-title"><b>摘要</b></div> <div class="item-content-big summary"></div></div>
                <div><div class="item-title"><b>用途</b></div> <div class="item-content-big purpose"></div>
                </div>
            </div>
            <div class="billDetail" style="margin-top: 12px">
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td>票据种类</td>
                        <td>单张票据金额</td>
                        <td>票据数量</td>
                        <td>票据金额合计</td>
                        <td>申请报销的金额</td>
                        <td>查看</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>增值税专用发票</td>
                        <td>606.5</td>
                        <td>1</td>
                        <td>606.5</td>
                        <td>606.5</td>
                        <td>
                            <span class="ty-color-blue">票据内容</span>
                            <span class="ty-color-blue">票据图片</span>
                        </td>
                    </tr>
                    <tr>
                        <td>其他普通发票</td>
                        <td>606.5</td>
                        <td>1</td>
                        <td>606.5</td>
                        <td>606.5</td>
                        <td>
                            <span class="ty-color-blue">票据内容</span>
                            <span class="ty-color-blue">票据图片</span>
                        </td>
                    </tr>
                    <tr>
                        <td>定额普通发票</td>
                        <td>--</td>
                        <td>1</td>
                        <td>606.5</td>
                        <td>606.5</td>
                        <td>
                            <span class="ty-color-blue">票据内容</span>
                            <span class="ty-color-blue">票据图片</span>
                        </td>
                    </tr>
                    <tr>
                        <td>增值税普通发票</td>
                        <td>606.5</td>
                        <td>1</td>
                        <td>606.5</td>
                        <td>606.5</td>
                        <td>
                            <span class="ty-color-blue">票据内容</span>
                            <span class="ty-color-blue">票据图片</span>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>81</td>
                        <td>1615.6</td>
                        <td>1600</td>
                        <td></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
</div>
    <%--回款数据查看--%>
    <div class="bonceContainer bounce-blue" id="collectDetail" style="width: 660px">
        <div class="bonceHead">
            <span class="bonceHeadName">查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" id="collectDetailSee" style="min-height: 120px; overflow-y: auto">
            <ul>
                <li>
                    <p class="pTtl">客户名称</p>
                    <p class="pCon" id="cltCustomer"></p>
                </li>
                <li>
                    <p class="pTtl">回款金额</p>
                    <p class="pCon" id="cltAmount"></p>
                </li>
            </ul>
            <div>
                <ul class="cltByCrah"><%--回款方式   1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐'--%>
                    <li>
                        <p class="pTtl">收入方式</p>
                        <p class="pCon">现金</p>
                    </li>
                    <li>
                        <p class="pTtl">收到日期</p>
                        <p class="pCon" id="cashRecive"></p>
                    </li>
                </ul>
                <ul class="cltByCheque">
                    <li>
                        <p class="pTtl">收入方式</p>
                        <p class="pCon">转账支票</p>
                    </li>
                    <li>
                        <p class="pTtl">收到日期</p>
                        <p class="pCon" id="cqRecive"></p>
                    </li>
                    <li>
                        <p class="pTtl">支票号</p>
                        <p class="pCon" id="cqSn"></p>
                    </li>
                    <li>
                        <p class="pTtl">到期日</p>
                        <p class="pCon" id="cqDueDate"></p>
                    </li>
                    <li>
                        <p class="pTtl">出具的单位</p>
                        <p class="pCon" id="cqUnit"></p>
                    </li>
                    <li>
                        <p class="pTtl">出具的银行</p>
                        <p class="pCon" id="cqBank"></p>
                    </li>
                    <li>
                        <p class="pTtl">存入银行</p>
                        <p class="pCon" id="cqDepositBank"></p>
                    </li>
                    <li>
                        <p class="pTtl">存入时间</p>
                        <p class="pCon" id="cqDepositDate"></p>
                    </li>
                    <li>
                        <p class="pTtl">存入经手人</p>
                        <p class="pCon" id="cqDepositMan"></p>
                    </li>
                    <li>
                        <p class="pTtl">到账时间</p>
                        <p class="pCon" id="cqArriveTime"></p>
                    </li>
                </ul>
                <ul class="cltByBill">
                    <li>
                        <p class="pTtl">收入方式</p>
                        <p class="pCon">承兑汇票</p>
                    </li>
                    <li>
                        <p class="pTtl">收到日期</p>
                        <p class="pCon" id="billRecive"></p>
                    </li>
                    <li>
                        <p class="pTtl">汇票号</p>
                        <p class="pCon" id="billSn"></p>
                    </li>
                    <li>
                        <p class="pTtl">到期日</p>
                        <p class="pCon" id="billDueDate"></p>
                    </li>
                    <li>
                        <p class="pTtl">最初出具的单位</p>
                        <p class="pCon" id="billUnit"></p>
                    </li>
                    <li>
                        <p class="pTtl">出具的银行</p>
                        <p class="pCon" id="billBank"></p>
                    </li>
                    <li>
                        <p class="pTtl">存入银行</p>
                        <p class="pCon" id="billDepositBank"></p>
                    </li>
                    <li>
                        <p class="pTtl">存入时间</p>
                        <p class="pCon" id="billDepositDate"></p>
                    </li>
                    <li>
                        <p class="pTtl">存入经手人</p>
                        <p class="pCon" id="billDepositMan"></p>
                    </li>
                    <li>
                        <p class="pTtl">到账时间</p>
                        <p class="pCon" id="billArriveTime"></p>
                    </li>
                </ul>
                <ul class="cltByBank">
                    <li>
                        <p class="pTtl">收入方式</p>
                        <p class="pCon">银行转账</p>
                    </li>
                    <li>
                        <p class="pTtl">到账日期</p>
                        <p class="pCon" id="bankRecive"></p>
                    </li>
                    <li>
                        <p class="pTtl">收款银行</p>
                        <p class="pCon" id="bankReciveBank"></p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%-- 存入银行的数据修改 --%>
    <div class="bonceContainer bounce-blue" id="updateBillInfo" >
        <div class="bonceHead">
            <span>修改</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="updateFormWrap">
                <div class="singleRow">
                    <i class="fa fa-circle-o" data-val="2" onclick="changeMethod($(this))"></i> <span class="changeTtl">更换承兑汇票</span>
                    <div class="changeNo toggleCon hd">
                        <select class="form-control" id="billNolist">
                            <option>请选择</option>
                        </select>
                    </div>
                </div>
                <div class="singleRow">
                    <i class="fa fa-circle-o" data-val="1" onclick="changeMethod($(this))"></i> <span>修改存入银行的信息</span>
                    <div class="updateOtherInfo toggleCon hd">
                        <div class="check_See bank_Con">
                            <span>存入银行账户</span>
                            <span>*</span>
                            <select id="turnToBank_1"> </select>
                        </div>
                        <div class="check_See bank_Con">
                            <span>存入时间</span>
                            <span>*</span>
                            <input type="text" class="laydate-icon" id="bill_depositDate">
                        </div>
                        <div class="check_See bank_Con">
                            <span>存入经手人</span>
                            <span> </span>
                            <input type="text" name="" id="bill_depositorName">
                        </div>
                        <div class="check_See bank_Con">
                            <span>到账时间</span>
                            <span>*</span>
                            <input type="text"  class="laydate-icon" id="billReceiveAccountDate">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取 消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" id="updateInfoBtn" onclick="updateBillInfoOk()">确 定</span>
        </div>
    </div>
</div>
<%-- 二级弹窗 --%>
<div class="bounce_Fixed">
    <%-- 税款录入提示--%>
    <div id="changeTip" class="bonceContainer bounce-green">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="changeTipCancel()"></a>
        </div>
        <div class="bonceCon">
            <p style="text-align: center">个人所得税，需在“工资管理”模块中操作！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="changeTipCancel()">关 闭</span>
        </div>
    </div>
    <%-- 修改借款信息、付款记录 的 修改记录 --%>
    <div class="bonceContainer bounce-blue" id="updateLog">
        <div class="bonceHead">
            <span class="bonceHeadName">修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <input type="hidden" id="logStatus">
            <table class="ty-table ty-table-control">
                <thead>
                <td width="40%">修改时间</td>
                <td width="20%">修改人</td>
                <td width="20%">修改前</td>
                <td width="20%">修改后</td>
                </thead>
                <tbody>
                <tr>
                    <td>2018-9-15 15:15:15</td>
                    <td>菜名</td>
                    <td class="ty-td-control"><span class="ty-color-blue info1">查看</span></td>
                    <td><span class="ty-color-blue info2">查看</span></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%-- 报销 - 票据内容 --%>
    <div class="bonceContainer bounce-blue" id="billInfo" style="width: 1200px">
        <div class="bonceHead">
            <span class="bonceHeadName">票据内容</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <%-- 单行的情况--%>
                <div class="oneRow" style="display: none;">
                    <p id="summary"></p>
                    <%-- 增普/其普，收据 --%>
                    <table class="ty-table ty-table-control receipt">
                        <thead>
                        <tr>
                            <td>费用类别</td>
                            <td>发票号码</td>
                            <td>开票日期</td>
                            <td id="ticketText">票据内容</td>
                            <td>规格型号</td>
                            <td>单位</td>
                            <td>数量</td>
                            <td>所开具发票或收据的金额</td>
                            <td>金额</td>
                            <td>发票图片</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <%-- 定额 --%>
                    <table class="ty-table ty-table-control receipt">
                        <thead>
                        <tr>
                            <td>费用类别</td>
                            <td>单张票据金额</td>
                            <td>数量</td>
                            <td>票据金额合计</td>
                            <td>实际支出金额</td>
                            <td>票据图片</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <%--多行--%>
                <div class="moreRow">
                    <%--增值税专用发票，增值税普通发票货物--%>
                    <table class="ty-table ty-table-control VATGood">
                        <thead>
                        <tr>
                            <td>费用类别</td>
                            <td>货物或应税劳务、服务名称</td>
                            <td>规格型号</td>
                            <td>单位</td>
                            <td>数量</td>
                            <td>单价</td>
                            <td>金额</td>
                            <td>税率</td>
                            <td>税额</td>
                            <td>含税合计</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <%--定额普通发票--%>
                    <div class="quotaGood" style="display: none">
                        <div class="item">
                            <div class="item-title">票据种类</div>
                            <div class="item-content billCatName">定额普通发票</div>
                        </div>
                        <div class="item">
                            <div class="item-title">费用类别</div>
                            <div class="item-content feeCatName">交通费-其他</div>
                        </div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>单张票据金额</td>
                                <td>数量</td>
                                <td>合计</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--其他普通发票货物--%>
                    <table class="ty-table ty-table-control otherGood" style="display: none">
                        <thead>
                        <tr>
                            <td>费用类别</td>
                            <td>货物或应税劳务、服务名称</td>
                            <td>规格型号</td>
                            <td>单位</td>
                            <td>数量</td>
                            <td>单价</td>
                            <td>金额</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <%--收据货物--%>
                    <table class="ty-table ty-table-control receipt" style="display: none">
                        <thead>
                        <tr>
                            <td>费用类别</td>
                            <td>票据内容</td>
                            <td>规格型号</td>
                            <td>单位</td>
                            <td>物品数量</td>
                            <td>单价</td>
                            <td>金额</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>

            <div class="item text-right singleAmount">
                （单张票据金额）价税合计 <span class="amount ty-color-blue"></span> 元
            </div>
            <div class="item billQuantity text-right">
            </div>
            <div class="item">
                <div class="item-title">备注</div>
                <div class="item-content memo"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">确定</button>
        </div>
    </div>
        <div class="bonceContainer bounce-blue" id="mtTip">
            <div class="bonceHead">
                <span>温馨提示：</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="addpayDetails">
                    <div class="shu1">
                        <p id="mt_tip_msg" style="text-align: center; padding:10px 0"> </p>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
            </div>
        </div>
</div>
<%-- 三级弹窗 --%>
<div class="bounce_Fixed2">
    <%-- 外部支票 --%>
    <div class="bonceContainer bounce-blue" id="financeReturns" style="width: 800px">
        <div class="bonceHead">
            <span>选择外部支票</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td></td>
                    <td>支票号</td>
                    <td>金额</td>
                    <td>出具支票银行</td>
                    <td>支票到期日</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="sureChooseCheque($(this))">确定</button>
        </div>
    </div>
    <%-- 承兑汇票 --%>
    <div class="bonceContainer bounce-blue" id="acceptanceBills" style="width: 1200px">
        <div class="bonceHead">
            <span>选择承兑汇票</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td></td>
                    <td>收到日期</td>
                    <td>付款单位</td>
                    <td>金额</td>
                    <td>汇票号码</td>
                    <td>到期日</td>
                    <td>最初出具的单位</td>
                    <td>出具的银行</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="sureChooseCheque($(this))">确定</button>
        </div>
    </div>
        <%-- 借款信息 修改前后详情 --%>
        <div class="bonceContainer bounce-blue" id="borrowInfo">
            <div class="bonceHead">
                <span>修改前、后</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="ordLoanDetail">
                    <div class="item">
                        <div class="col-md-4"><span class="item-title">出资方</span><span class="item-content lender"></span></div>
                        <div class="col-md-4"><span class="item-title">借款方</span><span class="item-content borrower"></span></div>
                        <div class="col-md-4"><span class="item-title">本金金额</span><span class="item-content principalAmount"></span></div>
                    </div>
                    <div class="item">
                        <div class="col-md-4"><span class="item-title">名义利率</span><span class="item-content nominalRate"></span></div>
                        <div class="col-md-4">
                            <span class="item-title">本金型式</span><span class="item-content incomeMethod"></span>
                        </div>
                        <div class="col-md-4 tansCommon trans3">
                            <span class="item-title">支票类别</span>
                            <span class="item-content withinOrAbroad"> </span>
                        </div>
                        <div class="col-md-4 tansCommon trans4">
                            <span class="item-title"><span>汇票号</span></span><span class=" item-content returnNo"></span> </div>
                    </div>
                    <%--借出的或者借入现金--%>
                    <div class="incomeMethodCon1 incomeMethodCon">
                        <%--转账支票/内部 --%>
                        <div class="inOrOut1 tansCommon ">
                            <div class="item">
                                <div class="col-md-4"> <span class="item-title">银行账户</span> <span class=" item-content accountBank"  ></span> </div>
                                <div class="col-md-4"> <span class="item-title">支票号</span> <span class=" item-content returnNo" ></span> </div>
                                <div class="col-md-4"> <span class="item-title">支票到期日</span><span class=" item-content expireDate"></span> </div>
                            </div>
                        </div>
                        <%--转账支票 /外部 --%>
                        <div class="inOrOut0 tansCommon ">
                            <div class="item">
                                <div class="col-md-4"> <span class="item-title">支票号</span> <span class="item-content returnNo"></span> </div>
                            </div>
                        </div>
                        <%-- 付款信息 --%>
                        <div class="trans1 trans3 trans4 transFu item tansCommon trans">
                            <div class="col-md-4 fu2">
                                <span class="item-title">付款日期</span>
                                <span class=" item-content paymentDate"></span>
                            </div>
                            <div class="col-md-4 fu1">
                                <span class="item-title">收款日期</span>
                                <span class=" item-content paymentDate"></span>
                            </div>
                            <div class="col-md-4 fu2">
                                <span class="item-title">付款经手人</span>
                                <span class=" item-content operatorName"></span>
                            </div>
                            <div class="col-md-4 fu1 fu2">
                                <span class="item-title">收款经手人</span>
                                <span class=" item-content partnerName"></span>

                            </div>
                        </div>
                        <%--银行转账--%>
                        <div class="trans5 item tansCommon"  style="display: none">
                            <div class="col-md-4">
                                <span class="item-title">付款日期</span>
                                <span class=" item-content paymentDate"></span>
                            </div>
                            <div class="col-md-4">
                                <span class="item-title">付款银行</span>
                                <span class=" item-content receiveBank"></span>
                            </div>
                            <div class="col-md-4">
                                <span class="item-title">付款经手人</span>
                                <span class=" item-content operatorName"></span>
                            </div>
                        </div>
                        <%--银行转账--%>
                        <div class="trans21 item tansCommon"  style="display: none">
                            <div class="col-md-4">
                                <span class="item-title">收款日期</span>
                                <span class=" item-content paymentDate"></span>
                            </div>
                            <div class="col-md-4">
                                <span class="item-title">收款经手人</span>
                                <span class=" item-content receiveOperatorName"></span>
                            </div>
                        </div>
                        <div class="trans5 item tansCommon"  style="display: none">
                            <div class="col-md-4">
                                <span class="item-title">借款方账户名称</span>
                                <span class=" item-content oppositeAccount"></span>

                            </div>
                            <div class="col-md-4">
                                <span class="item-title">开户行</span>
                                <span class=" item-content oppositeBankno"></span>
                            </div>
                            <div class="col-md-4">
                                <span class="item-title">账号</span>
                                <span class=" item-content oppositeBankcode"></span>
                            </div>
                        </div>
                    </div>
                    <%--// 借入的--%>
                    <div class="incomeMethodCon2 incomeMethodCon">
                        <div class="trans1 trans"></div>
                        <%--转账支票/承兑汇票--%>
                        <div class="trans3 trans4" style="display: none">
                            <div class="item">
                                <div class="col-md-4"> <span class="item-title"><span class="trans3">收到日期</span><span class="trans4">收到日期</span></span> <span class="item-content receiveDate"></span> </div>
                                <div class="col-md-4"> <span class="item-title"><span class="trans3">支票号</span><span class="trans4">汇票号</span></span> <span class="item-content returnNo">汇票号</span> </div>
                                <div class="col-md-4"> <span class="item-title"><span class="trans3">到期日</span><span class="trans4">到期日</span></span> <span class="item-content expireDate"></span> </div>
                            </div>
                            <div class="item">
                                <span class="col-md-4"> <span class="item-title"><span class="trans3">出具的单位</span><span class="trans4">最初出具的单位</span></span> <span class="item-content originalCorp"></span> </span>
                                <span class="col-md-4"> <span class="item-title"><span class="trans3">出具的银行</span><span class="trans4">出具的银行</span></span> <span class="item-content bankName"></span> </span>
                            </div>
                        </div>
                        <%--转账银行--%>
                        <div class="trans5" style="display: none">
                            <div class="item">
                                <div class="col-md-4"> <span class="item-title">到账日期</span> <span class="item-content arriveDate"></span> </div>
                                <div class="col-md-8"> <span class="item-title">收款银行</span> <span style="width: 300px;" class="item-content receiveBank"></span></div>
                            </div>
                        </div>
                    </div>

                    <div class="item">
                        <div class="col-md-4">
                            <span class="item-title">归还本金的约定</span><span class="item-content repaymentDate"></span>
                        </div>
                    </div>

                    <div class="item">
                        <div class="col-md-8">
                            <span class="item-title">利息的支付方式</span><span class="item-content interestMethod"></span>
                        </div>
                    </div>
                    <div>
                        <div class="interest0 interest"></div>
                        <div class="item interest2 interest3" style="display: none">
                            <div class="col-md-4"> <span class="item-title">开始计息日期</span><span class="item-content interestAccrualDate"></span></div>
                            <div class="col-md-4"> <span class="item-title"><span class="interest2">每月还款日</span><span class="interest3">每年还款日</span></span><span class="item-content periodRepaymentDate"></span></div>
                            <div class="col-md-4"> <span class="item-title">每次应付金额</span><span class="item-content periodRepaymentAmount"></span></div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="col-md-12"><span class="item-title">备注</span><span class="item-content memo"></span></div>
                    </div>
                </div>

            </div>
            <div class="bonceFoot"></div>
        </div>
    <%-- 查看付款 --%>
        <div class="bonceContainer bounce-blue" id="payInfo">
            <div class="bonceHead">
                <span class="bonceHeadName"></span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div>
                    <div class="item">
                        <span class="item-title payInfoTtl1">付款金额</span>
                        <span class="item-content repaymentAmount"></span>
                    </div>
                    <div class="item">
                        <span class="item-title payInfoTtl2">付款日期</span>
                        <span class="item-content repaymentTime"></span>
                    </div>
                    <div class="item">
                        <span class="item-title payInfoTtl3">支付方式</span>
                        <span class="item-content repaymentMethod"></span>
                    </div>

                    <div class="optPay">
                        <%-- 付款录入 转账支票 --%>
                        <%-- 转账支票 --%>
                        <div class="opt" style="display: none;">
                            <div class="item opt3 opt31 opt32">
                                <span class="item-title"></span>
                                <span class="item-content withinOrAbroad billType"></span>
                            </div>
                            <%--内部支票 / 外部支票--%>
                            <div class="item opt31">
                                <span class="item-title">银行账户</span>
                                <span class="item-content bank bankAccount" style="width:275px;"></span>
                            </div>
                            <div class="item opt31">
                                <span class="item-title">支票号</span>
                                <span class="item-content billNo"></span>
                            </div>
                            <div class="item opt32 opt4">
                                <span class="item-title chequeName">支票号</span>
                                <span class="item-content billNo"></span>
                            </div>
                            <div class="item opt31"><span class="item-title">支票到期日</span><span class="item-content billEndDate"></span></div>
                            <div class="item opt31"><span class="item-title">接收日期</span><span class="item-content billReceiveDate"></span></div>
                            <div class="item opt31"><span class="item-title">接收经手人</span><span class="item-content receiveOperatorName"></span></div>
                            <div class="item opt31"><span class="item-title">支付经手人</span><span class="item-content paymentOperatorName"></span></div>
                            <%-- 银行转账 --%>
                            <div class="item opt5">
                                <span class="item-title">转账银行</span>
                                <span class="item-content bank transBank" style="width:275px"></span>
                            </div>
                        </div>


                        <%-- 收款录入 --%>
                        <div class="payx">
                            <div class="payx1"> <%--现金--%>
                                <div class="item"><span class="item-title" >收款经手人</span> <span class="item-content receiveOperatorName"></span></div>
                            </div>
                            <div class="payx3 payx4"> <%--转账支票 / 承兑汇票--%>
                                <div class="item"><span class="item-title pay1" >收到支票日期</span> <span class="item-content billReceiveDate" ></span></div>
                                <div class="item"><span class="item-title pay2" >支票号</span> <span class="item-content billNo"></span></div>
                                <div class="item"><span class="item-title pay3" >支票到期日</span> <span class="item-content billEndDate" ></span></div>
                                <div class="item"><span class="item-title pay4" >出具支票单位</span> <span class="item-content billSource"></span></div>
                                <div class="item"><span class="item-title pay5" >出具支票银行</span> <span class="item-content billBank"></span></div>
                            </div>
                            <div class="payx5">
                                <div class="item"><span class="item-title" >到账日期</span> <span class="item-content arriveDate" ></span></div>
                                <div class="item"> <span class="item-title">收款银行</span> <span class="item-content receiveBank"></span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot"></div>
        </div>
    <%-- 付款记录 详情 --%>
        <div class="bonceContainer bounce-blue" id="payInfo2">
            <div class="bonceHead">
                <span class="bonceHeadName"></span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div>
                    <div class="item">
                        <span class="item-title payInfoTtl1">付款金额</span>
                        <span class="item-content repaymentAmount"></span>
                    </div>
                    <div class="item">
                        <span class="item-title payInfoTtl2">付款日期</span>
                        <span class="item-content repaymentTime"></span>
                    </div>
                    <div class="item">
                        <span class="item-title payInfoTtl3">支付方式</span>
                        <span class="item-content repaymentMethod"></span>
                    </div>

                    <div class="optPay">
                        <%-- 转账支票 --%>
                        <div class="opt" style="display: none;">
                            <div class="item opt3 opt31 opt32">
                                <span class="item-title"></span>
                                <span class="item-content withinOrAbroad"></span>
                            </div>
                            <%--内部支票 / 外部支票--%>
                            <div class="item opt31">
                                <span class="item-title">银行账户</span>
                                <span class="item-content bank bankAccount" style="width:275px;"></span>
                            </div>
                            <div class="item opt31">
                                <span class="item-title">支票号</span>
                                <span class="item-content returnNo"></span>
                            </div>
                            <div class="item opt32 opt4">
                                <span class="item-title chequeName">支票号</span>
                                <span class="item-content returnNo"></span>
                            </div>
                            <div class="item opt31"><span class="item-title">支票到期日</span><span class="item-content expireDate"></span></div>
                            <div class="item opt31"><span class="item-title">接收日期</span><span class="item-content receiveDate"></span></div>
                            <div class="item opt31"><span class="item-title">接收经手人</span><span class="item-content receiver"></span></div>
                            <div class="item opt31"><span class="item-title">支付经手人</span><span class="item-content operator"></span></div>
                            <%-- 银行转账 --%>
                            <div class="item opt5">
                                <span class="item-title">转账银行</span>
                                <span class="item-content bank receiveBank" style="width:275px"></span>
                            </div>
                        </div>


                        <%-- 收款录入 --%>
                        <div class="payx">
                            <div class="payx1"> <%--现金--%>
                                <div class="item"><span class="item-title" >收款经手人</span> <span class="item-content receiveOperatorName"></span></div>
                            </div>
                            <div class="payx3 payx4"> <%--转账支票 / 承兑汇票--%>
                                <div class="item"><span class="item-title pay1" >收到支票日期</span> <span class="item-content receiveDate" ></span></div>
                                <div class="item"><span class="item-title pay2" >支票号</span> <span class="item-content returnNo"></span></div>
                                <div class="item"><span class="item-title pay3" >支票到期日</span> <span class="item-content expireDate" ></span></div>
                                <div class="item"><span class="item-title pay4" >出具支票单位</span> <span class="item-content originalCorp "></span></div>
                                <div class="item"><span class="item-title pay5" >出具支票银行</span> <span class="item-content bankName "></span></div>
                            </div>
                            <div class="payx5">
                                <div class="item"><span class="item-title" >到账日期</span> <span class="item-content arriveDate" ></span></div>
                                <div class="item"> <span class="item-title">收款银行</span> <span class="item-content receiveBank "></span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot"></div>
        </div>
</div>
<div class="hd" id="chargeSubmitStatus">${state}</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>数据查看</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container" >
                <div>
                    <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue" id="goPrev" onclick="goPrev()" style="display: none;">返回</span>
                    <p id="navControl" style="display: none;">
                        <span class="navTxt"><i class="fa fa-home"></i> 财务管理</span>
                        <span class="nav_"> / </span>
                        <span class="navTxt"><a id="dataScan" onclick="$('#theDay').click();$(this).parent().nextAll().remove()"><i class=""></i>数据查看</a></span>
                    </p>
                </div>
                <div>
                    <div class="dataNav">
                        <%-- 展示四个大数据 --%>
                        <div id="mainDataNav">
                            <div class="navtab">
                                <div class="left"><span id="betweenDate" ></span></div>
                                <div class="right" style="position:relative; ">
                                    <span class="btnGroup" style="margin-right:200px; ">
                                        <span class="btnn btnn-big btnnActive" id="theDay">本日</span>
                                        <span class="btnn btnn-big" id="theMonth">本月</span>
                                        <span class="btnn btnn-big" id="theYear">本年</span>
                                        <span class="clr"></span>
                                    </span>
                                    <span class="btnn btnn-big riBtn dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" id="searchDIY_btn">自定义查询</span>
                                    <ul class="dropdown-menu dropdown-menu-default searchCon"  >
                                        <span class="trigle"></span>
                                        <li>
                                            <span class="ttl">时间范围：</span><input type="text" class="laydate-icon" id="searchStart">
                                        </li>
                                        <li>
                                            <span class="ttl">到：</span><input type="text" class="laydate-icon" id="searchEnd">
                                        </li>
                                        <li class="ctl">
                                            <span class="btnn btnn-big" id="searchDIY">查询</span>
                                            <span class="btnn btnn-big">取消</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="clr"></div>
                            </div>
                            <div class="mainNav">
                                <div class="bg_blue pal">
                                    <div class="visual">
                                        <i class="indexBg prevMonth"></i>
                                    </div>
                                    <div class="details">
                                        <div class="number">
                                            <span data-counter="counterup" id="allPreviousBalance"> </span>
                                        </div>
                                        <div class="desc" id="prevBalance"> 上月总余额 </div>
                                    </div>
                                </div>
                                <div class="bg_green pal">
                                    <div class="visual">
                                        <i class="indexBg theMonthGet"></i>
                                    </div>
                                    <div class="details">
                                        <div class="number">
                                            <span data-counter="counterup" id="allCredit"></span></div>
                                        <div class="desc" id="curGet"> 本月总收入 </div>
                                    </div>
                                </div>
                                <div class="bg_red pal">
                                    <div class="visual">
                                        <i class="indexBg theMonthPay"></i>
                                    </div>
                                    <div class="details">
                                        <div class="number">
                                            <span data-counter="counterup" id="allDebit"></span>
                                        </div>
                                        <div class="desc" id="curOut"> 本月总支出 </div>
                                    </div>
                                </div>
                                <div class="bg_purple pal ">
                                    <div class="visual">
                                        <i class="indexBg theMonthLast"></i>
                                    </div>
                                    <div class="details">
                                        <div class="number">
                                            <span data-counter="counterup" id="allBalance" ></span></div>
                                        <div class="desc" id="curBalance"> 本月总余额 </div>
                                    </div>
                                </div>
                            </div>
                            <div class="navtab">
                                <div class="btnGroup searchType">
                                    <span class="btnn btnn-big" id="searchByFlow">按月流水查看</span>
                                    <span class="btnn btnn-big" id="searchByTime">按时间查看</span>
                                    <span class="btnn btnn-big" id="searchByAccount">按账户查看</span>
                                </div>
                                <div class="screenArea screenBody">
                                    <span>数据筛选</span>
                                    <select id="searchOrg" onchange="screenOrgData($(this))">
                                        <option></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <%-- 查按时间段查询 按年显示某段时间的  --%>
                        <div id="duringLevel">
                            <table class="ty-table">
                                <thead>
                                <td width="30%">日期</td>
                                <td width="20%" id="nva1">上期余额</td>
                                <td width="15%" id="nva2">本期收入</td>
                                <td width="15%" id="nva3">本期支出</td>
                                <td width="20%" id="nva4">本期余额</td>
                                </thead>
                                <tbody id="duringLevel_table">
                                <tr>
                                    <td id="nav5">2016-05-03 ~ 2016-04-30</td>
                                    <td id="nav6">40000</td>
                                    <td id="nav7">400</td>
                                    <td id="nav8">400</td>
                                    <td id="nav9">40000</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="dataList">
                        <%-- dataInfo --%>
                        <div id="dataInfo">
                            <table class="ty-table ty-table-control">
                                <thead><tr>
                                    <td width="15%">时间</td>
                                    <td width="25%">摘要</td>
                                    <td class="belongOrg">所属的机构</td>
                                    <td width="10%">收入</td>
                                    <td width="10%">支出</td>
                                    <td width="10%">经手人</td>
                                    <td width="15%">操作</td>
                                </tr></thead>
                                <tbody id="dataInfo_tbl"></tbody>
                            </table>
                        </div>
                        <%-- dataByLevel 按 月/年/时间段 查询 ， 展示列表 --%>
                        <div id="dataByLevel">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="15%" id="td0">日期</td>
                                <td width="15%" id="td1">昨日余额</td>
                                <td class="belongOrg">所属的机构</td>
                                <td width="15%" id="td2">当日收入</td>
                                <td width="15%" id="td3">当日支出</td>
                                <td width="15%" id="td4">当日余额</td>
                                <td width="15%">操作</td>
                                </thead>
                                <tbody id="dataByLevel_tbl"></tbody>
                            </table>
                        </div>
                        <%-- 按账户查询 --%>
                        <div id="dataByAccount" style="display:none;  ">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td class='textLeft' width="25%" >账户</td>
                                <td class="belongOrg">所属的机构</td>
                                <td id="ac1" width="12%">昨日余额</td>
                                <td id="ac2" width="12%">本日收入</td>
                                <td id="ac3" width="12%">本日支出</td>
                                <td id="ac4" width="12%">当前余额</td>
                                <td width="16%">操作</td>
                                </thead>
                                <tbody id="dataByAccount_btl"> </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="dataInfo">
                        <div>
                            <%--   1-来源于报销数据的现金/银行转账信息  --%>
                            <div class="" id="detailKind_1">
                                <div class="bonceCon">
                                    <div class="approvCon applyYes" id="approvCon">
                                    </div>
                                    <%--同步传输的表单--%>
                                    <form action="../update/updatePerReim.do" method="post" enctype="multipart/form-data" id="changeApply">
                                        <table class="info table table-bordered " style="margin-top:30px; ">
                                            <input type="hidden" class="processId" name="id">
                                            <input type="hidden" class="billCatName" name="billCatName">
                                            <input type="hidden" class="billDate" name="billDate">
                                            <div class="hd bigImag"></div>
                                            <input type="text" class="hd type" name="type">
                                            <tr> <td width="30%" class="applyTtl">申请人</td><td width="70%" id="applyName"> </td> </tr>
                                            <tr> <td class="applyTtl">申请时间</td><td id="applyTime"> </td> </tr>
                                            <tr> <td class="applyTtl">费用类别</td><td id="applyKind">
                                                <span class="scantd" id="scan_feeCatName_1"></span>
                                                <select class="update" name="feeCat" id="update_feeCatName_1">
                                                    <option value="">请选择类别</option>
                                                    <option value="1">货款</option>
                                                    <option value="2">借款</option>
                                                    <option value="3">投资款</option>
                                                    <option value="4">废品</option>
                                                    <option value="5">其他</option>
                                                </select>
                                            </td> </tr>
                                            <tr> <td class="applyTtl">票据种类</td><td id="applyCat">
                                                <span class="scantd" id="scan_billCatName_1" >增值票</span>
                                                <span class="updateRadio" id="update_billCatName_1" >
                                                <span class="radioBtn" onclick="changeTicketType($(this) , 1 )" id="applyRadio_billCat_1" >
                                                    <span class="radioShow"></span>
                                                </span>
                                                <span class="valShow">增值票</span>
                                                <span class="radioBtn" onclick="changeTicketType($(this) , 1 )" id="applyRadio_billCat_2"  >
                                                    <span class="radioShow"></span>
                                                </span>
                                                <span class="valShow">普通票</span>
                                                <span class="radioBtn" onclick="changeTicketType($(this) , 1 )" id="applyRadio_billCat_3" >
                                                    <span class="radioShow"></span>
                                                </span>
                                                <span class="valShow">收据</span>
                                            </span>
                                            </td>
                                            </tr>
                                            <tr> <td class="applyTtl">票据所属月份</td><td id="applyMonth">
                                                <span class="scantd" id="scan_billDate_1">本月票据</span>
                                                <span class="updateRadio" id="update_billDate_1">
                                                <span class="radioBtn" onclick="changeTicketMonth($(this) , 1 )" id="applyRadio_billMonth_1" >
                                                    <span class="val">{"value":1 , "name":"dataNav"}</span>
                                                    <span class="radioShow"></span>
                                                </span>
                                                <span class="valShow">本月票据</span>
                                                <span class="radioBtn" onclick="changeTicketMonth($(this) , 1)" id="applyRadio_billMonth_2"  >
                                                    <span class="val">{"value":2 , "name":"dataNav"}</span>
                                                    <span class="radioShow"></span>
                                                </span>
                                                <span class="valShow">非本月票据</span>
                                            </span>
                                            </td>
                                            </tr>
                                            <tr> <td class="applyTtl">摘要</td><td>
                                                <span class="scantd" id="scan_summery_1" >去北京</span>
                                                <input type="text" name="Summary" id="update_summery_1" class="update">
                                            </td> </tr>
                                            <tr> <td class="applyTtl">用途</td><td>
                                                <span class="scantd" id="scan_purpose_1">去北京</span>
                                                <input type="text" name="purpose" class="update" id="update_purpose_1">
                                            </td> </tr>
                                            <tr> <td class="applyTtl">票据数量</td><td >
                                                <span class="scantd" id="scan_billAccount_1">2</span>
                                                <input type="text" name="billQuantity" class="update" id="update_billAccount_1" onkeyup="clearNum(this)">
                                            </td> </tr>
                                            <tr> <td class="applyTtl">实际金额</td><td>
                                                <span class="scantd" id="scan_amount_1">2</span>
                                                <input type="text" name="amount" class="update" id="update_amount_1" onkeyup="clearNoNum(this)">
                                            </td> </tr>
                                            <tr> <td class="applyTtl">发票金额</td><td>
                                                <span class="scantd" id="scan_billAmount_1">2</span>
                                                <input type="text" name="amount" class="update" id="update_billAmount_1" disabled>
                                            </td> </tr>
                                            <tr> <td class="applyTtl">备注</td><td>
                                                <span class="scantd"  id="scan_memo_1">备注</span>
                                                <input type="text" name="memo" id="update_memo_1" class="update">
                                            </td> </tr>
                                            <tr style="position:relative; " >
                                                <td class="applyTtl">附件<div id="bigImagcon"><img id="bigImag" src=""></div></td>
                                                <td>

                                                    <div id="info_images"></div>
                                                    <div class="uploadFiles">
                                                        <input type="file" text="上传附件" class="update" name="files" id="update_file_1" multiple="multiple">
                                                        <span class="ty-btn" onclick="$(this).prev().val('')">取消选择</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </form>
                                    <div class="itemTr">
                                        <span class="ttl"></span>
                                        <span class="con text-right">
                                        <span class="update updateSubmit" onclick="updateSubmit($(this),1)">提交</span>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <%--   2-项目为收入的现金/银行转账信息      --%>
                            <div class="" id="detailKind_2">
                                <div class="bonceCon1">
                                    <div class="itemTr">
                                        <span class="ttl">项目</span>
                                        <span class="con">
                                        <span id="scan_type_2" class="scan">收入</span>
                                        <span id="update_type_2" class="update disable">收入</span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">类别</span>
                                        <span class="con">
                                        <span class="scan" id="scan_feeCat_2">货款</span>
                                        <select class="update" id="update_feeCat_2" onchange="changeCatergray($(this))">
                                            <option value="">请选择类别</option>
                                            <option value="1">货款</option>
                                            <option value="2">借款</option>
                                            <option value="3">投资款</option>
                                            <option value="4">废品</option>
                                            <option value="5">其他</option>
                                        </select>
                                    </span>
                                    </div>
                                    <div class="itemTr" id="genre_2">
                                    <span class="con">
                                        <span class="scan" id="scan_categoryDesc_2"></span>
                                        <input type="text" class="update isGenre" id="update_categoryDesc_2">
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">票据种类</span>
                                        <span class="con ">
                                        <span class="mark"></span>
                                        <span class="scan" id="scan_billCat_2">增值票</span>
                                        <span id="update_billCat_2" class="updateRadio">
                                            <span class="radioBtn" onclick="changeTicketType($(this) , 2 )" >
                                                <span class="val">{"value":"增值票" , "name":"dataNav"}</span>
                                                <span class="radioShow"></span>
                                            </span>
                                            <span class="valShow">增值票</span>

                                            <span class="radioBtn" onclick="changeTicketType2($(this) , 2 )"  >
                                                <span class="val">{"value":"普通票"  , "name":"dataNav"}</span>
                                                <span class="radioShow"></span>
                                            </span>
                                            <span class="valShow">普通票</span>

                                            <span class="radioBtn" onclick="changeTicketType2($(this) , 2 )" >
                                                <span class="val">{"value":"收据" , "name":"dataNav"}</span>
                                                <span class="radioShow"></span>
                                            </span>
                                            <span class="valShow">收据</span>
                                        </span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">票据所属月份</span>
                                        <span class="con" disabled >
                                        <span class="mark"></span>
                                        <span class="scan" id="scan_billMonth_2">本月票据</span>
                                        <span id="update_billMonth_2" class="updateRadio">
                                            <span class="radioBtn" onclick="changeTicketMonth($(this) , 2 )"  >
                                                <span class="val">{"value":1 , "name":"dataNav"}</span>
                                                <span class="radioShow"></span>
                                            </span>
                                            <span class="valShow">本月票据</span>

                                            <span class="radioBtn" onclick="changeTicketMonth($(this) , 2 )"  >
                                                <span class="val">{"value":2 , "name":"dataNav"}</span>
                                                <span class="radioShow"></span>
                                            </span>
                                            <span class="valShow">非本月票据</span>
                                        </span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">摘要</span>
                                        <span class="con"><span class="scan" id="scan_summery_2">收入</span><input type="text" class="update" id="update_summery_2"/></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">票据数量</span>
                                        <span class="con" disabled >
                                        <%--<span class="mark"></span>--%>
                                        <span class="scan" id="scan_billAccount_2" style="background:rgba(0,0,0,0.15)"></span>
                                        <span class="update" id="update_billAccount_2" style="background:rgba(0,0,0,0.15)"></span></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">金额</span>
                                        <span class="con"><span class="scan" id="scan_amount_2">200</span><input type="text" class="update" id="update_amount_2" onkeyup="clearNoNum(this)"/></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">所开具发票或收据的金额</span>
                                        <span class="con"><span class="scan" id="scan_billAmount_2">200</span><input type="text" class="update" id="update_billAmount_2" disabled/></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">用途</span>
                                        <span class="con"><span class="scan" id="scan_purpose_2">不知道</span><input type="text" class="update" id="update_purpose_2" /></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">经手人</span>
                                        <span class="con"><span class="scan" id="scan_auditorName_2">收入</span><input type="text" class="update" id="update_auditorName_2" /></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">收入方式</span>
                                        <span class="con"><span class="scan" id="scan_method_2">现金</span><span class="update disable" id="update_method_2">现金</span></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">付款单位</span>
                                        <span class="con"><span class="scan" id="scan_oppositeCorp_2">阿里巴巴</span><input type="text" class="update" id="update_oppositeCorp_2" /></span>
                                    </div>
                                    <div id="update_method5_2">
                                        <div class="itemTr">
                                            <span class="ttl">到账时间</span>
                                            <span class="con"><span class="scan" id="scan_receiveAccountDate_2"></span><input type="text" class="update" id="update_receiveAccountDate_2" /></span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">收款银行</span>
                                            <span class="con"><span class="scan" id="scan_accountBank_2"></span><select type="text" class="update accountBank" id="update_accountBank_2"></select></span>
                                        </div>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">备注</span>
                                        <span class="con"><span class="scan" id="scan_memo_2">收入</span><input type="text" class="update" id="update_memo_2" /></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl"></span>
                                        <span class="con text-right">
                                        <span class="update updateSubmit" onclick="updateSubmit($(this),2)">提交</span>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <%-- 3-项目为支出的现金/银行转账信息 --%>
                            <div class="" id="detailKind_3">
                                <div class="bonceCon1">
                                    <div class="itemTr">
                                        <span class="ttl">数据类别</span>
                                        <span class="con">
                                        <span id="scan_type_3" class="scan">支出</span>
                                        <span id="update_type_3" class="update disable">支出</span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">支出类别</span>
                                        <span class="con">
                                            <span class="scan" id="scan_exp_3">收入</span>
                                            <span id="update_exp_3" class="update disable">支出</span>
                                        </span>
                                    </div>
                                    <div class="oral1">
                                        <div class="itemTr">
                                            <span class="ttl">票据日期</span>
                                            <span class="con">
                                                <span class="scan" id="scan_billDate_3"> </span>
                                                <input type="text" class="update" id="update_billDate_3"/>
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">用途</span>
                                            <span class="con">
                                                <span class="scan" id="scan_purpose_3"></span>
                                                <input type="text" class="update" id="update_purpose_3" />
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">摘要</span>
                                            <span class="con">
                                                <span class="scan" id="scan_summery_3">收入</span>
                                                <input type="text" class="update" id="update_summery_3"/>
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">票据数量</span>
                                            <span class="con">
                                            <span class="scan" id="scan_billAccount_3"> </span>
                                            <input type="text" class="update" id="update_billAccount_3" onkeyup="clearNum(this)"/></span>
                                        </div>
                                        <div class="itemTr update" style="border: none; width: 548px; ">
                                            <span class="ttl">&nbsp;</span>
                                            <span class="con">
                                                <span id="uploadBtn22Btn" class="linkBtn " onclick="upBtn2($('#uploadBtn22'), 'scan_imgs_32')">上传发票图片</span> （最多可上传九张）
                                            </span>
                                        </div>
                                        <div class="hd" id="uploadBtn22"></div>
                                        <div id="scan_imgs_32"></div>
                                        <div class="itemTr" id="billAmountTr">
                                            <span class="ttl">票据金额合计</span>
                                            <span class="con">
                                        <span class="scan" id="scan_billAmount_3"> </span>
                                        <input type="text" class="update" id="update_billAmount_3" disabled/></span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl" id="actMoney">实际金额合计</span>
                                            <span class="con">
                                        <span class="scan" id="scan_amount_3"> </span>
                                        <input type="text" class="update" id="update_amount_3" onkeyup="clearNoNum(this)"/></span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl" id="actPayDate">实际付款日期</span>
                                            <span class="con">
                                        <span class="scan" id="scan_factDate_3"> </span>
                                        <input type="text" class="update" id="update_factDate_3" /></span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">经手人</span>
                                            <span class="con">
                                        <span class="scan" id="scan_auditorName_3"> </span>
                                        <input type="text" class="update" id="update_auditorName_3" />
                                    </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">支出方式</span>
                                            <span class="con">
                                                <span id="scan_method_3" class="scan"> </span>
                                                <span id="update_method_3" class="update disable"></span>
                                            </span>
                                        </div>
                                        <div id="update_method5_3" style="display: none">
                                            <div class="itemTr">
                                                <span class="ttl">转账银行</span>
                                                <span class="con">
                                                    <span class="scan" id="scan_accountBank_3"></span>
                                                    <select type="text" class="update accountBank" id="update_accountBank_3"></select>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="itemTr stakeholderCategoryContsiner">
                                            <span class="ttl">收款单位</span>
                                            <span class="con poReL">
                                            <a class="linkBtn addOopBtn" onclick="addOopBtn($(this))" >新增</a>
                                            <span class="scan stakeholderCategoryText" id="scan_stakeholderCategory_3"> </span>
                                            <span class="update openOopBtn" id="update_stakeholderCategory_3" onclick="openOop($(this))"></span>
                                            <span id="stakeholderCategory" class="stakeholderCategory">
                                                <option value="0">请选择</option>
                                                <option value="1">供应商</option>
                                                <option value="2">公司职工</option>
                                                <option value="3">财务自行录入的收款单位（含个人）</option>
                                            </span>
                                        </span>
                                        </div>
                                        <div class="itemTr " >
                                            <span class="ttl"> </span>
                                            <span class="con">
                                            <span class="scan" id="scan_oppositeCorp_3"> </span>
                                            <input type="text" readonly class="update" id="update_oppositeCorp_3" />

                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">备注</span>
                                            <span class="con">
                                                <span class="scan" id="scan_memo_3"> </span>
                                                <input type="text" class="update" id="update_memo_3" />
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl"></span>
                                            <span class="con text-right">
                                                <span class="update updateSubmit" onclick="updateSubmit($(this),3)">提交</span>
                                            </span>
                                        </div>
                                    </div>
                                    <%-- 汇划费 的查看 --%>
                                    <div class="oral2">
                                        <div class="itemTr">
                                            <span class="ttl">支出日期</span>
                                            <span class="con">
                                                <span class="scan" id="scan_paydate_3"> </span>
                                                <input type="text" class="update" id="update_paydate_3"/>
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">用途/摘要</span>
                                            <span class="con">
                                                <span class="scan" id="scan_summaryPurpose_3"> </span>
                                                <input type="text" class="update" id="update_summaryPurpose_3" disabled />
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">支出金额</span>
                                            <span class="con">
                                                <span class="scan" id="scan_payAmount_3"> </span>
                                                <input type="text" class="update" id="update_payAmount_3"/>
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">开户行</span>
                                            <span class="con">
                                                <span class="scan" id="scan_payBank_3"> </span>
                                                <select class="update accountBank" id="update_payBank_3"></select>
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">凭证照片</span>
                                            <span class="con">
                                                 <span id="uploadBtn2Btn" class="linkBtn update" onclick="upBtn2($('#uploadBtn2'), 'scan_imgs_3')">上传发票图片</span> （最多可上传九张）
                                            </span>
                                        </div>
                                        <div class="hd" id="uploadBtn2"></div>
                                        <div class="itemTr">
                                            <span class="ttl">&nbsp;</span>
                                            <span class="con">
                                                <div id="scan_imgs_3"></div>
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">备注</span>
                                            <span class="con">
                                                <div class="scan" id="scan_payMemo_3"> </div>
                                                <input type="text" class="update" id="update_payMemo_3"/>
                                            </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl"></span>
                                            <span class="con text-right">
                                                <span class="update updateSubmit" onclick="updateSubmit($(this),30)">提交</span>
                                            </span>
                                        </div>
                                    </div>




                                </div>
                                <%--  税款录入--%>
                                <div id="rateEntryFrm" class="bonceCon2">
                                    <div class="edit000">
                                        <div class="bonceCon">
                                            <div class="cc">
                                                <div>
                                                    <span class="rateTtl"><i class="ty-color-red">*</i>税种</span>
                                                    <select id="rateCat" class="form-control bg" onchange="rateCatChange($(this))"></select>
                                                </div>
                                                <div class="ty-color-blue tip">在税种的选项中如找不到缴税凭证上的税种时，需请会计添加！</div>
                                                <div>
                                                    <span class="rateTtl"><i class="ty-color-red">*</i>税款所属时期</span>
                                                    <input type="text" id="rateStartDate" placeholder="请选择" class="form-control sm"  > <span> -- </span>
                                                    <input type="text" id="rateEndDate" placeholder="请选择" class="form-control sm"  />
                                                </div>
                                                <div>
                                                    <span class="rateTtl"><i class="ty-color-red">*</i>实际缴纳日期</span>
                                                    <input id="rateRealDate" placeholder="请选择" type="text" class="form-control bg" />
                                                </div>
                                                <div>
                                                    <span class="rateTtl"><i class="ty-color-red">*</i>实缴金额</span>
                                                    <input id="moneyReal" placeholder="请录入" type="text" class="form-control bg" onkeyup="clearNoNumN(this, 2)" />
                                                </div>
                                                <div>
                                                    <span class="rateTtl"><i class="ty-color-red">*</i>支出方式</span>
                                                    <select id="patMethod" type="text" class="form-control bg" disabled></select>
                                                </div>
                                                <div>
                                                    <span class="rateTtl">缴税凭证照片</span>
                                                    <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="upBtn3()">上传</span>
                                                    <span>（仅可上传一张。有多行税种的缴税凭证，只传一次即可）</span>
                                                    <div id="uploadBtn3" style="display: none;"></div>
                                                </div>
                                                <div id="ratePic" ></div>
                                                <div>
                                                    <span class="rateTtl"><i class="ty-color-red">*</i>申报记录</span>
                                                    <select id="rateApplyLog" class="form-control bg">
                                                        <option value="">请选择申报起止日期正确的申报记录</option>
                                                    </select>
                                                </div>
                                                <div class="ty-color-blue tip">没有可选的申报记录时，如确定本页面内容录入无误，则需请会计检查！</div>
                                                <div>
                                                    <span class="rateTtl">备注</span>
                                                    <input id="rateMemo" placeholder="请录入" type="text" class="form-control bg" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="bonceFoot">
                                            <span class="ty-btn ty-btn-big ty-circle-3" onclick="goPrev()">取消</span>
                                            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="rateAddtrok()">确定</span>
                                        </div>
                                    </div>
                                    <div class="scan000">
                                        <p><span class="ttl30">税种</span><span class="ratename"></span></p>
                                        <p><span class="ttl30">税款所属时期</span><span class="ratenDur"></span></p>
                                        <p><span class="ttl30">实际缴纳日期</span><span class="rateFactDate"></span></p>
                                        <p><span class="ttl30">实缴金额</span><span class="rateAmount"></span></p>
                                        <p><span class="ttl30">支出方式</span><span class="rateMethod"></span></p>
                                        <p><span class="ttl30">缴税凭证照片</span><span class="ratePic"></span></p>
                                        <p><span class="ttl30">申报记录</span><span class="rateReportLog"></span></p>
                                        <p><span class="ttl30">备注</span><span class="rateMemo"></span></p>
                                    </div>
                                </div>

                            </div>
                            <%-- 4-现金支票/内部转账支票的信息  --%>
                            <div class="" id="detailKind_3_">
                                <div class="bonceCon1">
                                    <div class="itemTr">
                                        <span class="ttl">数据类别</span>
                                        <span class="con">
                                        <span id="scan_type_3_" class="scan">支出</span>
                                        <span id="update_type_3_" class="update disable">支出</span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">支出类别</span>
                                        <span class="con ">
                                            <span class="scan" id="scan_exp_3_">收入</span>
                                            <span id="update_exp_3_" class="update disable">支出</span>
                                        </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">票据日期</span>
                                        <span class="con "  >
                                            <span class="scan" id="scan_billDate_3_"> </span>
                                            <input type="text" class="update" id="update_billDate_3_"/>
                                        </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">摘要</span>
                                        <span class="con">
                                        <span class="scan" id="scan_summery_3_">收入</span>
                                        <input type="text" class="update" id="update_summery_3_"/>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">用途</span>
                                        <span class="con">
                                        <span class="scan" id="scan_purpose_3_"></span>
                                        <input type="text" class="update" id="update_purpose_3_" />
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">票据数量</span>
                                        <span class="con">
                                        <span class="scan" id="scan_billAccount_3_"> </span>
                                        <input type="text" class="update" id="update_billAccount_3_" onkeyup="clearNum(this)"/></span>
                                    </div>
                                    <div class="itemTr update" style="border: none; width: 548px;">
                                        <span class="ttl">&nbsp;</span>
                                        <span class="con">
                                                <span id="uploadBtn22Btn_" class="linkBtn " onclick="upBtn2($('#uploadBtn22_'), 'scan_imgs_3_')">上传发票图片</span> （最多可上传九张）
                                            </span>
                                    </div>
                                    <div class="hd" id="uploadBtn22_"></div>
                                    <div id="scan_imgs_3_"></div>
                                    <div class="itemTr">
                                        <span class="ttl">票据金额合计</span>
                                        <span class="con">
                                        <span class="scan" id="scan_billAmount_3_"> </span>
                                        <input type="text" class="update" id="update_billAmount_3_" disabled/></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">实际金额合计</span>
                                        <span class="con">
                                        <span class="scan" id="scan_amount_3_"> </span>
                                        <input type="text" class="update" id="update_amount_3_" onkeyup="clearNoNum(this)"/></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">实际付款日期</span>
                                        <span class="con">
                                        <span class="scan" id="scan_factDate_3_"> </span>
                                        <input type="text" class="update" id="update_factDate_3_" /></span>
                                    </div>

                                    <div class="itemTr">
                                        <span class="ttl">经手人</span>
                                        <span class="con">
                                        <span class="scan" id="scan_auditorName_3_"> </span>
                                        <input type="text" class="update" id="update_auditorName_3_" />
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">支出方式</span>
                                        <span class="con">
                                        <span id="scan_method_3_" class="scan"> </span>
                                        <select id="update_method_3_" class="update">
                                            <option value="1">转账支票</option>
                                            <option value="2">承兑汇票</option>
                                        </select>
                                    </span>
                                    </div>
                                    <div class="update_method_3_1">
                                        <div class="itemTr">
                                            <span class="ttl"> </span>
                                            <span class="con">
                                            <span id="scan_method_3_2" class="scan"> </span>
                                            <select id="update_method_3_2" class="update">
                                                <option value="1">内部支票</option>
                                                <option value="2">外部支票</option>
                                            </select>
                                        </span>
                                        </div>
                                        <div class="update_method_3_1_1">
                                            <div class="itemTr">
                                                <span class="ttl">银行账户</span>
                                                <span class="con">
                                                <span id="scan_bankAccount_3_" class="scan"> </span>
                                                <select id="update_bankAccount_3_" class="update">
                                                    <option value="">工商银行天安支行（基本户）8998 8989 8998 8998 898</option>
                                                </select>
                                            </span>
                                            </div>
                                            <div class="itemTr">
                                                <span class="ttl">支票号</span>
                                                <span class="con">
                                                <span id="scan_checkNo_3_" class="scan"> </span>
                                                <select id="update_checkNo_3_1" class="update">
                                                    <option value="">支票账号</option>
                                                </select>
                                            </span>
                                            </div>
                                            <input type="text" class="hd update_bankAccount_3_">
                                            <input type="text" class="hd update_checkNo_3_1">
                                            <input type="text" class="hd update_checkId_3_1">
                                            <div class="itemTr">
                                                <span class="ttl">支票到期日</span>
                                                <span class="con">
                                                <span class="scan" id="scan_checkendDate_3_"> </span>
                                                <input type="text" class="update laydate-icon" id="update_checkendDate_3_" style="height:30px;" />
                                            </span>
                                            </div>
                                            <div class="itemTr">
                                                <span class="ttl">接收日期</span>
                                                <span class="con">
                                                <span class="scan" id="scan_receiveDate_3_"> </span>
                                                <input type="text" class="update laydate-icon" id="update_receiveDate_3_" />
                                            </span>
                                            </div>
                                            <div class="itemTr">
                                                <span class="ttl">接收经手人</span>
                                                <span class="con">
                                                <span class="scan" id="scan_receivePerson_3_"> </span>
                                                <input type="text" class="update" id="update_receivePerson_3_" />
                                            </span>
                                            </div>
                                            <div class="itemTr">
                                                <span class="ttl">支付经手人</span>
                                                <span class="con">
                                                <span class="scan" id="scan_payPerson_3_"> </span>
                                                <input type="text" class="update" id="update_payPerson_3_" />
                                            </span>
                                            </div>
                                        </div>
                                        <div class="update_method_3_1_2">
                                            <div class="itemTr">
                                                <span class="ttl">支票号</span>
                                                <span class="con">
                                                <input type="text" class="update" id="update_chequeNo_3_1" />
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="update_method_3_2" style="display:none">
                                        <div class="itemTr">
                                            <span class="ttl">汇票号</span>
                                            <span class="con">
                                        <input type="text" class="update" id="update_chequeNo_3_2" />
                                    </span>
                                        </div>
                                    </div>

                                    <div class="itemTr stakeholderCategoryContsiner">
                                        <span class="ttl">收款单位</span>
                                        <span class="con poReL">
                                            <a class="linkBtn addOopBtn"  onclick="addOopBtn($(this))" >新增</a>
                                            <span class="scan stakeholderCategoryText" id="scan_stakeholderCategory_3_"> </span>
                                            <span class="update openOopBtn" id="update_stakeholderCategory_3_" onclick="openOop($(this))"></span>
                                            <span class="stakeholderCategory">
                                                <option value="0">请选择</option>
                                                <option value="1">供应商</option>
                                                <option value="2">公司职工</option>
                                                <option value="3">财务自行录入的收款单位（含个人）</option>
                                            </span>
                                        </span>
                                    </div>
                                    <div class="itemTr " >
                                        <span class="ttl"> </span>
                                        <span class="con">
                                            <span class="scan" id="scan_oppositeCorp_3_"> </span>
                                            <input readonly type="text" class="update" id="update_oppositeCorp_3_" />

                                        </span>
                                    </div>

                                    <div class="itemTr">

                                        <span class="ttl">备注</span>
                                        <span class="con">
                                        <span class="scan" id="scan_memo_3_"> </span>
                                        <input type="text" class="update" id="update_memo_3_" />
                                    </span>
                                    </div>

                                    <div class="itemTr">
                                        <span class="ttl"></span>
                                        <span class="con text-right">
                                        <span class="update updateSubmit" onclick="updateSubmit($(this),4)">提交</span>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <%-- 5- 承兑汇票/外部转账支票的信息 --%>
                            <div class="" id="detailKind_5">
                                <div class="bonceCon1">
                                    <div class="itemTr">
                                        <span class="ttl">项目</span>
                                        <span class="con">
                                        <span id="scan_type_5" class="scan">收入</span>
                                        <span id="update_type_5" class="update disable">收入</span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">类别</span>
                                        <span class="con">
                                        <span id="scan_cat_5" class="scan"></span>
                                        <span id="update_cat_5" class="update disable"></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                    <span class="con">
                                        <span class="scan" id="scan_categoryDesc_5"></span>
                                        <span class="update disable" id="update_categoryDesc_5"></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">票据种类</span>
                                        <span class="con">
                                        <span id="scan_checkCat_5" class="scan" style="background:#eaeaea; "></span>
                                        <span id="update_checkCat_5" class="update disable" style="background:#eaeaea; "></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">票据所属月份</span>
                                        <span class="con">
                                        <span id="scan_checkMonth_5" class="scan" style="background:#eaeaea; "></span>
                                        <span id="update_checkMonth_5" class="update disable" style="background:#eaeaea; "></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">摘要</span>
                                        <span class="con">
                                        <span class="scan" id="scan_summery_5">收入</span>
                                        <span class="update disable" id="update_summery_5"></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">票据数量</span>
                                        <span class="con">
                                        <span class="scan" id="scan_billAccount_5" style="background:#eaeaea; "> </span>
                                        <span type="text" class="update disable" id="update_billAccount_5" style="background:#eaeaea; "></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">所开具发票或收据的金额</span>
                                        <span class="con">
                                        <span class="scan" id="scan_billAmount_5"> </span>
                                        <span class="update disable" id="update_billAmount_5" ></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">用途</span>
                                        <span class="con">
                                        <span class="scan" id="scan_purpose_5"></span>
                                        <span type="text" class="update disable" id="update_purpose_5" ></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">经手人</span>
                                        <span class="con">
                                        <span class="scan" id="scan_auditorName_5"> </span>
                                        <span type="text" class="update disable" id="update_auditorName_5" ></span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">收入方式</span>
                                        <span class="con">
                                        <span id="scan_method_5" class="scan"> </span>
                                        <span id="update_method_5" class="update disable"></span>
                                    </span>
                                    </div>
                                    <%--   转账支票   --%>
                                    <div id="method_trans">
                                        <div class="itemTr">
                                            <span class="ttl">收到日期</span>
                                            <span class="con">
                                            <span id="scan_recevDate_5" class="scan"> </span>
                                            <span id="update_recevDate_5" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">付款单位</span>
                                            <span class="con">
                                            <span id="scan_payCmp_5" class="scan"> </span>
                                            <span id="update_payCmp_5" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">支票号</span>
                                            <span class="con">
                                            <span id="scan_checkno_5" class="scan"> </span>
                                            <span id="update_checkno_5" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">金额</span>
                                            <span class="con">
                                        <span class="scan" id="scan_amount_5"> </span>
                                        <span class="update disable" id="trans_update_amount_5" ></span>
                                    </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">到期日</span>
                                            <span class="con">
                                            <span id="scan_endDate_5" class="scan"> </span>
                                            <span id="update_endDate_5" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">出具的单位</span>
                                            <span class="con">
                                            <span id="scan_origCmp_5" class="scan"> </span>
                                            <span id="update_origCmp_5" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">出具的银行</span>
                                            <span class="con">
                                            <span id="scan_origBank_5" class="scan"> </span>
                                            <span id="update_origBank_5" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">财务经手人</span>
                                            <span class="con">
                                            <span id="scan_jingshou_5" class="scan"> </span>
                                            <span id="update_jingshou_5" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">录入时间</span>
                                            <span class="con">
                                            <span id="scan_inputTime_5" class="scan"> </span>
                                            <span id="update_inputTime_5" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">备注</span>
                                            <span class="con">
                                            <span id="scan_memos_5" class="scan"> </span>
                                            <span id="update_memos_5" class="update disable"></span>
                                        </span>
                                        </div>
                                    </div>
                                    <%-- 承兑汇票 --%>
                                    <div id="method_receive">
                                        <div class="itemTr">
                                            <span class="ttl">收到日期</span>
                                            <span class="con">
                                            <span id="scan_recevDate_5_1" class="scan"> </span>
                                            <span id="update_recevDate_5_1" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">付款单位</span>
                                            <span class="con">
                                            <span id="scan_payCmp_5_1" class="scan"> </span>
                                            <span id="update_payCmp_5_1" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">汇票号</span>
                                            <span class="con">
                                            <span id="scan_checkno_5_1" class="scan"> </span>
                                            <span id="update_checkno_5_1" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">金额</span>
                                            <span class="con">
                                        <span class="scan" id="scan_amount_5_1"> </span>
                                        <span class="update disable" id="receive_update_amount_5" ></span>
                                    </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">到期日</span>
                                            <span class="con">
                                            <span id="scan_endDate_5_1" class="scan"> </span>
                                            <span id="update_endDate_5_1" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">最初出具的单位</span>
                                            <span class="con">
                                            <span id="scan_origCmp_5_1" class="scan"> </span>
                                            <span id="update_origCmp_5_1" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">出具的银行</span>
                                            <span class="con">
                                            <span id="scan_origBank_5_1" class="scan"> </span>
                                            <span id="update_origBank_5_1" class="update disable"></span>
                                        </span>
                                        </div>
                                        <div class="itemTr">
                                            <span class="ttl">备注</span>
                                            <span class="con">
                                            <span id="scan_memos_5_1" class="scan"> </span>
                                            <span id="update_memos_5_1" class="update disable"></span>
                                        </span>
                                        </div>
                                    </div>

                                    <div class="itemTr">
                                        <span class="ttl">存入银行</span>
                                        <span class="con">
                                        <span id="scan_cunBank_5" class="scan"> </span>
                                        <select id="update_cunBank_5" class="update"></select>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">存入时间</span>
                                        <span class="con">
                                        <span id="scan_cunTime_5" class="scan"> </span>
                                        <input type="text" id="update_cunTime_5" class="update laydate-icon" style="height:30px;" />
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">存入经手人</span>
                                        <span class="con">
                                        <span id="scan_cunPerson_5" class="scan"> </span>
                                        <input type="text" id="update_cunPerson_5" class="update" />
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">到账时间</span>
                                        <span class="con">
                                        <span id="scan_receTime_5" class="scan"> </span>
                                        <input type="text" id="update_receTime_5" class="update laydate-icon " style="height:30px;" />
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl"></span>
                                        <span class="con text-right">
                                        <span class="update updateSubmit" onclick="updateSubmit($(this),5)">提交</span>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <%-- 6-内部非支出性转账的三种--%>
                            <div class="" id="detailKind_4">
                                <div class="bonceCon1">
                                    <div class="itemTr">
                                        <span class="ttl">项目</span>
                                        <span class="con"><span class="scan">内部非支出性转账</span><span class="update disable">内部非支出性转账</span></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">转账类型</span>
                                        <span class="con"><span class="scan" id="scan_transKind_4">存现金</span>
                                        <span class="update disable" id="update_transKind_4">存现金</span></span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">付款账户</span>
                                        <span class="con">
                                        <span class="scan" id="scan_payAccount_4"> </span>
                                        <span class="update disable" id="update_payAccount_4"> </span>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">收款账户</span>
                                        <span class="con"><span class="scan" id="scan_receiveAccount_4"></span>
                                        <span class="update disable" id="update_receiveAccount_4"></span></span>
                                    </div>
                                    <div class="itemTr" id="checkCon" class="hd">
                                        <span class="ttl">支票号</span>
                                        <span class="con">
                                        <span class="scan" id="scan_checkNo_4"></span>
                                        <select class="update" id="update_checkNo_4"></select>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">转账金额</span>
                                        <span class="con">
                                        <span class="scan" id="scan_money_4">250</span>
                                        <input type="text" class="update" id="update_money_4" onkeyup="clearNoNum(this)"/>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">业务发生时期</span>
                                        <span class="con">
                                        <span class="scan" id="scan_auditDate_4"></span>
                                        <input type="text" class="update laydate-icon" id="update_auditDate_4" style="height:30px; line-height:30px;"/>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl">备注</span>
                                        <span class="con">
                                        <span class="scan" id="scan_memo_4">250</span>
                                        <input type="text" class="update" id="update_memo_4"/>
                                    </span>
                                    </div>
                                    <div class="itemTr">
                                        <span class="ttl"></span>
                                        <span class="con text-right">
                                        <span class="update updateSubmit" onclick="updateSubmit($(this),6)">提交</span>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <%-- 来源于工资管理的数据 --%>
                            <div id="detailKind_wage" style=" width: 80%; margin: 30px auto;">
                                <div>
                                    <div class="">
<%--                                        <span class="ty-btn ty-btn-green ty-btn-big ty-left type2" onclick="goPrev()">返 回</span>--%>
                                        <h3 class="ty-center" id="tt3">工资所属月份：XXXX年XX月</h3>
                                    </div>
                                    <div class="martop30">
                                        <div class="flexbox">
                                            <div><span class="date_t">支出日期 </span><span class="date_i"></span></div>
                                            <div><span class="number_t">职工人数 </span><span class="number_i"></span></div>
                                            <div style="flex: 2;"><span class="method_t">支出方式 </span><span class="method_i"></span></div>
                                            <div><span class="amount_t">本月职工应缴个所税总额 </span><span class="amount_i"></span></div>
                                        </div>
                                        <div>
                                            <span>创建：</span>
                                            <span class="createD">XXX XXXX-XX-XX XX:XX:XX</span>
                                        </div>
                                    </div>
                                    <table class="ty-table martop30 " id="scanSalary">
                                        <tr>
                                            <td width="12%">姓名</td>
                                            <td width="15%">手机号</td>
                                            <td width="30%">部门/岗位</td>
                                            <td width="20%"><span class="placeTitle"></span>金额</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/finance/indexForAdmin.js?v=SVN_REVISION"></script>
<script src="../script/ConventionalBorrowing/ConventionalBorrowing.js?v=SVN_REVISION"></script>
</body>
</html>
