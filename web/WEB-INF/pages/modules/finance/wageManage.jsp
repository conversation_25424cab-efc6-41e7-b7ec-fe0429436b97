<%--
  Created by IntelliJ IDEA.
  User: lyt
  Date: 2021/6/3
  Time: 9:04
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/finance/wageManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="alertTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip ty-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 修改记录查看 --%>
    <div class="bonceContainer bounce-green" id="editRecordScan" style="width: 750px;">
        <div class="bonceHead recordScan clear">
            <span class="ty-left ty-btn bounce-ok" onclick="bounce_Fixed.cancel()">关 闭</span>
            <h4 class="ty-right ty-center kindTtl" style="color: #666;"></h4>
        </div>
        <div class="bonceCon">
            <div>
                <div class="flexbox">
                    <div><span class="datettl">发放日期</span><span class="dateVal"></span></div>
                    <div style="flex: 2;"><span class="methodttl">发放方式</span><span class="methodVal"></span></div>
                    <div style="text-align: right" class="scanCount"></div>
                </div>
                <div class="clear">
                    <p class="ttl ty-left">※  本次修改后的数据被标为了<span class="ty-color-red">红色</span>。</p>
                    <p class="ty-right scanCreate">修改人：</p>
                </div>
                <table class="ty-table" id="recordScanSalary">
                    <tr>
                        <td>姓名</td>
                        <td>手机号</td>
                        <td>部门/岗位</td>
                        <td><span class="placeTitle"></span>金额</td>
                    </tr>

                </table>
            </div>
        </div>
        <div class="bonceFoot">
<%--            <div class="ty-center"> ——以下无内容——</div>--%>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 修改提示 --%>
    <div class="bonceContainer bounce-blue" id="updateTip" >
        <div class="bonceHead">
            <span>修改</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tip0">如发放信息与职工工资均修改，请修改两次</p>
            <p>
                <span style="margin-left: 90px;">内容修改</span>
                <select class="form-control" style="width: 200px;">
                    <option value="">请选择</option>
                    <option class="op1" value="1">发放信息</option>
                    <option class="op2" value="2">职工工资</option>
                </select>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="updateTipOk()">确定</span>
        </div>
    </div>
    <%-- 查看职工工资发放明细 --%>
    <div class="bonceContainer bounce-blue" id="scanPerson" style="width: 990px;">
        <div class="bonceHead">
            <span>查看职工工资发放明细</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
             <h4>XXXX年XX月职工工资</h4>
            <p>
                <span>姓名 <span class="name"></span></span>
                <span style="margin-left: 50px;">手机号 <span class="mobile"></span></span>
                <span style="margin-left: 50px;">部门/岗位  <span class="depart"></span></span>
            </p>
            <p>本月实发金额合计  <span class="sunAmount"></span>元</p>
            <table class="ty-table tbControl">
                <tr>
                    <td>发放日期</td>
                    <td>发放方式</td>
                    <td>发放金额</td>
                    <td>创建</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 已离职的职工 --%>
    <div class="bonceContainer bounce-blue" id="unStaff" style="width: 560px;">
        <div class="bonceHead">
            <span>已离职的职工</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <ul>
                <li> <i class="fa fa-circle-o"></i> 姓名 - 手机号</li>
            </ul>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5 unStaffOK" onclick="unStaffOK()">确定</span>
        </div>
    </div>
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue" id="editRecord" style="width: 560px;">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear noRecord">
                <p class="ty-left">当前资料尚未经修改。</p>
                <p class="ty-right">创建人：<span class="createInfo"></span></p>
            </div>
            <div class="clear hasRecord">
                <p class="editTip ty-left"></p>
                <p class="ty-right">修改人：<span class="createInfo"></span></p>
                <table class="ty-table" id="recordList">
                    <tbody>
                    <tr>
                        <td width="30%">资料状态</td>
                        <td width="20%">操  作</td>
                        <td width="50%">创建人/修改人</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="cancelTip">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip ty-center">确定放弃您刚编辑的内容吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="backPrev">确定</span>
        </div>
    </div>
    <%-- 修改取消提示--%>
    <div class="bonceContainer bounce-blue" id="cancelTip2">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip ty-center">确定放弃您刚编辑的内容吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="backCheck()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>工资管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container">
                <%-- 主页面 --%>
                <input type="hidden" id="showMainConNum">
                <div class="mainCon mainCon1">
                    <div>
                        <div class="clear line">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-left" data-fun="ssBackMain" id="indexBack">返 回</span>
                            <div class="ty-right searchSect">
                                <div class="ty-left keywordSearch">
                                    查询其他年份数据
                                    <%--<input class="kj-input kj-input-blue" placeholder="请选择年份" id="yearSearch" />--%>
                                    <input id="yearSearch" type="text" placeholder="请选择年份"/>
<%--                                        <option value="">请选择年份</option>--%>
<%--                                    </input>--%>
                                </div>
                                <button class="ty-left ty-btn ty-btn-blue" style="height: 26px;line-height: 26px;" onclick="searchKeySure()">确定</button>
                            </div>
                        </div>
                        <div>
                            <div>以下为<span id="yearDesc"></span>年度职工的工资等数据。尚无数据的，可“手动录入”。</div>
                            <div class="line">已有数据的，展示为数据的产生日期。“查看”中，还可修改。</div>
                            <table class="simpleTable mainList">
                                <thead>
                                <tr>
                                    <td>月份</td>
                                    <td>职工工资</td>
                                    <td>个人所得税</td>
                                    <td>社保</td>
                                    <td>公积金</td>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <%-- 工资表 --%>
                <div class="mainCon mainCon2">
                    <div>
                        <h3 class="ty-center mar salaryTitle"></h3>
                        <div class="clear ">
                            <div class="ty-left">
                                <div class="entryTip tip1">
                                    请在页面上录入本次所发工资的各项数据，并“保存”。<br/>
                                    工资数据为手动录入时，每位职工实发金额的和，需与本次实发总额相等，否则无法成功保存。<br/>
                                    工资采用多种方式发放，或工资发放多次，先录入一次的，之后可再次录入。<br/>
                                    下表中为当前在职的职工。<br/>
                                    如有已离职的职工本月发放了工资，请点击“添加已离职的职工”并添加。
                                </div>
                                <div class="entryTip tip2">
                                    <%-- 个人所得税--%>
                                    下表中为当前在职的职工。如有已离职的职工本月发放了工资，请选择并添加。<br/><br/>
                                    在本页面，除需录入每位职工的数据，数据合计也需录入。<br/>
                                    每位职工的数据与总和相等，可在一定程度上降低录入错误的概率。<br/>
                                    所以二者不相等时，系统不允许保存。<br/>
                                </div>
                                <div class="entryTip tip3">
                                    <%-- 社保--%>
                                    下表中为当前在职的职工。如有已离职的职工本月发放了工资，请选择并添加。<br/><br/>
                                    在本页面，除需录入每位职工的数据，数据合计也需录入。 <br/>
                                    每位职工的数据与总和相等，可在一定程度上降低录入错误的概率。<br/>
                                    所以二者不相等时，系统不允许保存。
                                </div>
                                <div class="entryTip tip4">
                                    <%-- 公积金--%>
                                    下表中为当前在职的职工。如有已离职的职工本月发放了工资，请选择并添加。<br/><br/>
                                    在本页面，除需录入每位职工的数据，数据合计也需录入<br/>
                                    每位职工的数据与总和相等，可在一定程度上降低录入错误的概率。<br/>
                                    所以二者不相等时，系统不允许保存。<br/>
                                </div>
                            </div>
                            <div class="ty-right">
                                <div class="line" style="text-align: right;"><span class="ty-btn ty-btn-blue ty-btn-big getUnStaff" data-area="1" data-fun="getUnStaff">添加已离职的职工</span></div>
                                <div>
                                    <span class="ty-btn ty-btn-yellow ty-btn-big" id="cancelbtn" onclick="bounce.show($('#cancelTip'));">取 消</span>
                                    <span class="ty-btn ty-btn-blue ty-btn-big" onclick="saveSalary()">保 存</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="clear line flexbox">
                                <div>
                                    <span id="date_span"></span>
                                    <input type="text" class="laydate form-control" id="date_input" placeholder="请选择" >
                                </div>
                                <div>
                                    <span id="method_span"></span>
                                    <select type="text" id="method_input" class="form-control" placeholder="请选择"></select>
                                </div>
                                <div>
                                    <span id="amount_span"></span>
                                    <input type="text" id="amount_input" class="form-control" placeholder="请录入金额" onkeyup="clearNoNum(this)">
                                </div>
                            </div>
                            <table class="ty-table tbControl" id="staffList">
                                <tbody>
                                <tr>
                                    <td width="12%">姓名</td>
                                    <td width="15%">手机号</td>
                                    <td width="30%">部门/岗位</td>
                                    <td width="20%"><span class="tbTitle"></span>金额</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <%-- 工资记录查看 --%>
                <div class="mainCon mainCon31">
                    <div>
                        <div class="line">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-left" data-fun="backMain">返 回</span>
                            <h3 class="ty-center" id="tt31"></h3>
                        </div>
                        <div class="line">
                            <div class="flexbox">
                                <div>
                                    工资采用多种方式发放，或某月工资发放多次时，请点击“继续录入本月工资”。
                                </div>
                                <div style="text-align: right">
                                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="scanByUser">按职工查看</span>
                                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="entryMore">继续录入本月工资</span>
                                    <span class="hd" id="salar"></span>
                                </div>
                            </div>
                        </div>
                        <table class="ty-table ty-table-control" id="scanSalaryList">
                            <tr>
                                <td>发放日期</td>
                                <td>发放方式</td>
                                <td>发放金额</td>
                                <td>发放人数</td>
                                <td>创建</td>
                                <td>操作</td>
                            </tr>
                            <tr>
                                <td>发放日期</td>
                                <td>发放方式</td>
                                <td>发放金额</td>
                                <td>发放人数</td>
                                <td>创建</td>
                                <td>
                                    <span class="jumpBtn ty-color-blue">查看</span>
                                    <span class="jumpBtn ty-color-blue">修改</span>
                                    <span class="jumpBtn ty-color-blue">修改记录</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <%-- 查看 --%>
                <div class="mainCon mainCon3">
                    <div>
                        <div class="line">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-left type2" data-fun="backMain">返 回</span>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-left type1" data-fun="back31">返 回</span>
                            <h3 class="ty-center" id="tt3"></h3>
                        </div>
                        <div class="line type1">
                            <div class="flexbox">
                                <div>
                                    <span class="number_t">以下共计   </span><span class="number_i"></span>
                                </div>
                                <div style="text-align: right">
                                    <span class="amount_t">本月实发总额 </span><span class="amount_i"></span>
                                </div>
                            </div>
                        </div>
                        <div class="line type2">
                            <div class="flexbox">
                                <div style="flex: 2">
                                    <div>
                                        <span class="date_t">支出日期 </span><span class="date_i"></span>
                                        <span class="number_t" style="margin-left:30px;">职工人数 </span><span class="number_i"></span>
                                        <span class="method_t" style="margin-left:30px;">支出方式 </span><span class="method_i"></span>
                                    </div>
                                    <div>
                                        <span>创建：</span>
                                        <span class="createD">XXX XXXX-XX-XX XX:XX:XX</span>
                                    </div>
                                </div>
                                <div style="text-align: right">
                                    <div>
                                        <span class="amount_t">本月职工应缴个所税总额 </span><span class="amount_i"></span>元
                                    </div>
                                    <div>
                                        <span class="jumpBtn itemUpdate2" data-fun="itemUpdate2">修改</span>
                                        <span class="jumpBtn marL itemUpdateLog2" data-fun="itemUpdateLog2">修改记录</span>
                                        <span id="itemInfo" class="hd"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table class="ty-table ty-table-control type1" id="scanSalary1">
                            <tr>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>部门/岗位</td>
                                <td><span class="placeTitle"></span>金额</td>
                                <td>操作</td>
                            </tr>
                        </table>
                        <table class="ty-table type2" id="scanSalary">
                            <tr>
                                <td width="12%">姓名</td>
                                <td width="15%">手机号</td>
                                <td width="30%">部门/岗位</td>
                                <td width="20%"><span class="placeTitle"></span>金额</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <%-- 按职工查看 --%>
                <div class="mainCon mainCon32">
                    <div>
                        <div class="line">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-left type1" data-fun="back32">返 回</span>
                            <h3 class="ty-center">工资所属月份：<span id="tt32"></span></h3>
                        </div>
                        <div class="line type1">
                            <div class="flexbox">
                                <div>
                                    <span class="number_t">以下共计   </span><span class="number_i"></span>人
                                </div>
                                <div style="text-align: right">
                                    <span class="amount_t">本月实发总额 </span><span class="amount_i"></span>
                                </div>
                            </div>
                        </div>
                        <table class="ty-table type1" id="scanSalaryByUser">
                            <tr>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>部门/岗位</td>
                                <td><span class="placeTitle"></span>金额</td>
                                <td>操作</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <%-- 修改 --%>
                <div class="mainCon mainCon4">
                    <div>
                        <p>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-fun="backScan">返 回</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="nextStepEdit">下一步</span>
                        </p>
                        <h4 style="text-align: center" class="tt4"></h4>
                        <p class="tipd"></p>
                        <div>
                            <table class="ty-table checkStaff">
                                <tr>
                                    <td class="toggle" data-type="all"><i class="fa fa-square-o"></i></td>
                                    <td>姓名</td>
                                    <td>手机号</td>
                                    <td>部门/岗位</td>
                                    <td><span class="placeTitle"></span>金额</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <%-- 下一步 --%>
                <div class="mainCon mainCon5">
                    <div>
                        <h4 style="text-align: center" class="tt4"></h4>
                        <div class="clear">
                            <div class="flexbox edittype1">
                                <div>
                                    <span class="tip1">请在页面上修改工资的发放方式。</span>
                                </div>
                                <div style="text-align: right;">
                                    <span class="ty-btn ty-btn-yellow ty-btn-big backCheck" data-fun="backCheck">放弃已编辑数据，退出</span>
                                    <span class="ty-btn ty-btn-blue ty-btn-big" data-fun="saveUpdateBtn" >保  存</span>
                                </div>
                            </div>
                            <div class="flexbox edittype2">
                                <div style="flex: 4;">
                                    <div class="type1">
                                        请在页面上录入本次所发工资的各项数据，并“保存”。<br/>
                                        工资数据为手动录入时，每位职工实发金额的和，需与本次实发总额相等，否则无法成功保存。<br/><br/>
                                        下表中为当前在职的职工。<br/>
                                        如有已离职的职工本月发放了工资，请点击“添加已离职的职工”并添加。
                                    </div>
                                    <div class="type2 type3 type4">
                                        下表中为当前在职的职工。如有已离职的职工本月发放了工资，请选择并添加。<br/><br/>
                                        在本页面，除需录入每位职工的数据，数据合计也需录入。<br/>
                                        每位职工的数据与总和相等，可在一定程度上降低录入错误的概率。<br/>
                                        所以二者不相等时，系统不允许保存。
                                    </div>
                                </div>
                                <div style="text-align: right; flex:3;">
                                    <span class="ty-btn ty-btn-yellow ty-btn-big backCheck" data-fun="backCheck" >放弃已编辑数据，退出</span>
                                    <span class="ty-btn ty-btn-blue ty-btn-big" data-fun="saveUpdateBtn">保  存</span><br/><br/>
                                    <span class="ty-btn ty-btn-blue ty-btn-big getUnStaff" data-area="2" data-fun="getUnStaff">添加已离职的职工</span>

                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="clear line flexbox edittype1">
                                <div>
                                    <span class="date_span1"></span>
                                    <input type="text" class="laydate form-control" id="date_input1" placeholder="请选择" >
                                </div>
                                <div>
                                    <span class="method_span1"></span>
                                    <select type="text" id="method_input1" class="form-control" placeholder="请选择"></select>
                                </div>
                                <div>
                                    <span class="amount_span1"></span>
                                    <span id="amount_input1"></span>
                                </div>
                            </div>
                            <div class="clear line edittype2">
                                <div class="ty-right">
                                    <span class="amount_span1"></span>
                                    <input id="updateTotalAmount" class="form-control" style="width: 150px; display: inline-block;" placeholder="请录入金额" onkeyup="clearNoNum(this)"/>
                                    <div class="hd"></div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <table class="ty-table tbControl updateSalary" id="updateSalary">
                                <tbody>
                                <tr>
                                    <td width="12%">姓名</td>
                                    <td width="15%">手机号</td>
                                    <td width="30%">部门/岗位</td>
                                    <td width="20%"><span class="placeTitle"></span>金额</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/finance/wageManage.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>

