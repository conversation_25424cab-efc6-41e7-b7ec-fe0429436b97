<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" href="../css/finance/dataImport.css?v=SVN_REVISION">

<link href="../css/dailyAffairs/dailyAffairs.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>

<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce" style="position: fixed">

    <%--    税款录入--%>
    <div id="rateEntryFrm" class="bonceContainer bounce-green">
        <div class="bonceHead">
            <span>税款录入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="cc">
                <div>
                    <span class="rateTtl"><i class="ty-color-red">*</i>税种</span>
                    <select id="rateCat" class="form-control bg" onchange="rateCatChange($(this))"></select>
                </div>
                <div class="ty-color-blue tip">在税种的选项中如找不到缴税凭证上的税种时，需请会计添加！</div>
                <div>
                    <span class="rateTtl"><i class="ty-color-red">*</i>税款所属时期</span>
                    <input type="text" id="rateStartDate" placeholder="请选择" class="form-control sm"  > <span> -- </span>
                    <input type="text" id="rateEndDate" placeholder="请选择" class="form-control sm"  />
                </div>
                <div>
                    <span class="rateTtl"><i class="ty-color-red">*</i>实际缴纳日期</span>
                    <input id="rateRealDate" placeholder="请选择" type="text" class="form-control bg" />
                </div>
                <div>
                    <span class="rateTtl"><i class="ty-color-red">*</i>实缴金额</span>
                    <input id="moneyReal" placeholder="请录入" onkeyup="clearNoNumN(this, 2)" type="text" class="form-control bg" />
                </div>
                <div>
                    <span class="rateTtl"><i class="ty-color-red">*</i>支出方式</span>
                    <select id="patMethod" type="text" class="form-control bg" ></select>
                </div>
                <div>
                    <span class="rateTtl">缴税凭证照片</span>
                    <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="upBtn3()">上传</span>
                    <span>（仅可上传一张。有多行税种的缴税凭证，只传一次即可）</span>
                    <div id="uploadBtn3" style="display: none;"></div>
                </div>
                <div id="ratePic" ></div>
                <div>
                    <span class="rateTtl"><i class="ty-color-red">*</i>申报记录</span>
                    <select id="rateApplyLog" class="form-control bg">
                        <option value="">请选择申报起止日期正确的申报记录</option>
                    </select>
                </div>
                <div class="ty-color-blue tip">没有可选的申报记录时，如确定本页面内容录入无误，则需请会计检查！</div>
                <div>
                    <span class="rateTtl">备注</span>
                    <input id="rateMemo" placeholder="请录入" type="text" class="form-control bg" />
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="rateAddtrok()">确定</span>
        </div>
    </div>

    <%--    税款删除 --%>
    <div id="rateDel" class="bonceContainer bounce-green">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">
            确定删除？
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="rateDelok()">确定</span>
        </div>
    </div>

    <%--报销申请页面(新)--%>
    <div class="bonceContainer bounce-green" id="reimburse" style="width: 1200px;" >
        <div class="bonceHead">
            <span>报销申请</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:650px;overflow: auto ">
            <p class="ty-alert ty-alert-info"><span class="ty-color-red">！！需入库</span>的物品<span class="ty-color-red">请勿</span>在此报销！</p>
            <div class="main">
                <form id="form_reimburse">
                    <div class="formItem">
                        <div class="formTitle">
                            <span><span class="ty-color-red">*</span> 事件的发生日期</span>
                        </div>
                        <div class="formCon">
                            <input type="text" name="beginDate" id="reimburseBeginDate" require> - <input type="text" name="endDate" id="reimburseEndDate" require>
                        </div>
                        <div class="formTitle">
                            <span><span class="ty-color-red">*</span> 是否属于销售事务？</span>
                        </div>
                        <div class="formCon transactionType">
                            <input type="radio" name="transactionType" value="1">是
                            <input type="radio" name="transactionType" value="2">否
                        </div>
                    </div>
                    <div class="formItem">
                        <div class="formTitle">
                            <span><span class="ty-color-red">*</span> 报销事由</span>
                        </div>
                        <div class="formCon">
                            <textarea name="purpose" cols="30" rows="3" style="width: 500px" require onkeyup="countWords($(this),255)"></textarea>
                            <div class="textMax text-right">255/255</div>
                        </div>
                    </div>
                </form>
                <div class="ty-alert ty-alert-info" style=" width: 1170px; display: block;  line-height: 31px;">
                    您已录入票据共 <b class="ty-color-blue billCountNumber"> 0 </b> 张，票据金额共 <b class="ty-color-blue billCountAmount"> 0 </b> 元，需报销的金额为 <b class="ty-color-blue countAmount"> 0 </b> 元。
                    <button type="btn" class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-name="goodEntry">票据录入</button>
                    <button type="btn" style="margin-right: 20px;" class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-name="invoiceUpload">票据上传</button>
                </div>
                <div class="isMonthTip ty-color-red" style="display: none">
                    本公司财务规定：一次报销中不可既含有本月的发票，又含有非本月的发票。
                    <div>您所录入的发票中既有本月又有非本月的票据。您要么删除所有非本月票据，要么删除所有本月票据，之后方可提交审批</div>
                </div>
                <div>
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td>票据种类</td>
                            <td>单张票据金额</td>
                            <td>票据数量</td>
                            <td>票据金额合计</td>
                            <td>申请报销的金额</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <%--<p class="tip" id="tip"></p>--%>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelReimburseBtn(); setLastname0(); ">取消</span>
            <button class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" type="btn" id="reimburseBtn" data-name="reimburse">提交审批</button>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="oppositeAddDiv" style="width: 446px;">
        <div class="bonceHead">
            <span>新增收款单位</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon" >
            <div>
                <p>请录入收款单位（含个人）的信息。</p>
                <p>供应商需由采购部门管理，公司职工需由总务部门管理，故请勿在此处新增供应商或公司职工！</p>
                <p class="e3">
                    <span><i class="ty-color-red">*</i>收款单位名称</span>
                    <input type="text" placeholder="请录入" id="oopName">
                </p>
                <p class="e3">
                    <span style="position: relative; top: -75px;">说明</span>
                    <textarea placeholder="请录入" id="oopMemo" onkeyup="setNum($(this))" style=" width: 229px; height: 91px;"></textarea>
                    <span class="showNum" style=" width: 50px;"></span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addOopOK()">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="oppositeCorpDiv">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce.cancel();$('#stakeholderCategory').hide();"></a>
        </div>
        <div class="bonceCon" style="max-height: 300px;">
            <div>
                <ul id="chooseC">
                    <li><i class="fa fa-circle-o"></i><span>撒的发生的</span></li>
                    <li><i class="fa fa-circle-o"></i><span>撒的发生的</span></li>
                    <li><i class="fa fa-circle-o"></i><span>撒的发生的</span></li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel(); $('#stakeholderCategory').hide();">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="chooseCOK()">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>


    <div class="bonceContainer bounce-blue" id="tipClass3">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;">
                需入库物品购买费用的报销，请采购人员在采购模块中操作！
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="setLastname0();">关闭</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="tipClass4">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;">
                社保或公积金，需在“工资管理”模块中操作！
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="setLastname0();">关闭</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="tipClass5">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;">
                录入向外借出的款项，请到常规借款模块中操作！
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="setLastname0();">关闭</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="tipClass6">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;">
                录入还款，请到常规借款模块中操作！
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="setLastname0();">关闭</span>
        </div>
    </div>


    <div class="bonceContainer bounce-orange" id="confirmOk">
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <p style="text-align: center;">该笔支出确定为现金?</p>
                <p id="editType" class="hd"></p>
                <p id="confirm_ms_tip" style="text-align: center; padding:10px 0"> </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="confirm()">确定</span>
        </div>
    </div>
    <div class="checkInner bounce-blue bonceContainer" id="checkInner" style="width: 700px"><!--这就是弹框-->
        <div class="bonceHead">
            <span>选择内部支票</span>
            <a class="bounce_close" onclick="cancelPayMethod(1)"></a>
        </div>
        <div class="bonceCon">
            <div class="checkTr">
                <div class="itemTrtd1"><span class="ty-color-red">*</span> 银行账户</div>
                <div class="itemTrtd2">
                    <select id="financeAccountId_payout_bounce" onchange="setCheckNo($(this).val())" >
                    </select>
                </div>
            </div>
            <div class="checkTr">
                <div class="itemTrtd1"><span class="ty-color-red">*</span> 支票号</div>
                <div class="itemTrtd2">
                    <select id="checkNumber_payout_bounce"> </select>
                </div>
            </div>
            <div class="center" id="innerTip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelPayMethod(1)" >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5"  onclick="showInner()" >确定</span>
        </div>
    </div>
    <div class="checkOuter bounce-blue  bonceContainer" id="checkOuter" style="width: 1000px">
        <div class="bonceHead">
            <span>选择外部支票</span>
            <a class="bounce_close" onclick="cancelPayMethod(1)"></a>
        </div>
        <div class="bonceCon">
            <table class="table table-bordered table-hover ">
                <thead>
                <th width="10%"></th>
                <th width="25%">支票号</th>
                <th width="20%">金额</th>
                <th width="25%">出具的银行</th>
                <th width="20%">支票到期日</th>
                </thead>
                <tbody id="outerCheck"></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5"  onclick="cancelPayMethod(1)" >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5"  onclick="showOuter()" >确定</span>
        </div>
    </div>
    <div class="acceptSelect bounce-blue  bonceContainer" id="acceptSelect"  style="width: 1000px">
        <div class="bonceHead">
            <span>选择承兑汇票</span>
            <a class="bounce_close" onclick="cancelPayMethod()"></a>
        </div>
        <div class="bonceCon">
            <table class="table table-bordered table-hover "    >
                <thead>
                <th width="10%"></th>
                <th width="15%">收到汇票日期</th>
                <th width="10%">付款单位</th>
                <th width="10%">金额</th>
                <th width="10%">汇票号</th>
                <th width="15%">到期日</th>
                <th width="15%">最初出具的单位</th>
                <th width="15%">出具的银行</th>
                </thead>
                <tbody id="acceptCheck">  </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5"  onclick="cancelPayMethod()" >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5"  onclick="selectAcceptCheck()" >确定</span>
        </div>
    </div>
</div>

<div class="bounce_Fixed">
    <%-- 税款录入提示--%>
    <div id="changeTip" class="bonceContainer bounce-green">
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="changeTipCancel()"></a>
        </div>
        <div class="bonceCon">
            <p style="text-align: center">个人所得税，需在“工资管理”模块中操作！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" onclick="changeTipCancel()">关 闭</span>
        </div>
    </div>

    <div class="bonceContainer bounce-green" id="invoiceUpload1" >
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel() "></a>
        </div>
        <div class="hide" style="display: block; ">
            <div id="invoiceUp"></div>
        </div>
        <div class="bonceCon clearfix">
            <div>
                <p>这张发票的识别需录入验证码！</p>
                <p>
                    <span>* 验证码</span>
                    <input type="text" id="inputCode" placeholder="请录入验证码"> <span id="color_hint">请 录入验证码中的文字</span>
                </p>
                <p>
                    <span id="code" onclick="reFreashQR()"></span> <span class="linkBtn">点击图片刷新</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="invoiceUpload1OK" onclick="validate()">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="billEntry" style="min-width:1200px;">
        <div class="bonceHead">
            <span>票据信息</span>
            <a class="bounce_close" onclick="cancelfile() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="ty-alert ty-alert-info"> 开票日期 <span class="issueDate"></span></div>
            <div>
                <%--增值税专用发票，增值税普通发票货物--%>
                <table class="ty-table ty-table-control VATGood">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td id="m2">货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>税率</td>
                        <td>税额</td>
                        <td>含税合计</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--其他普通发票货物--%>
                <table class="ty-table ty-table-control otherGood" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <%--收据货物--%>
                <table class="ty-table ty-table-control receipt" style="display: none">
                    <thead>
                    <tr>
                        <td>费用类别</td>
                        <td>票据内容</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>物品数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="handleBtn">
                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="goodEntryNext">录入本张票据上的下一种货物</button>
                <div class='ty-form-checkbox' skin="green" level="1" style="margin-top: 8px">
                    <i class="fa fa-check"></i>
                    <span>本张票据录入完毕</span>
                </div>
            </div>
            <form id="form_billEntry" style="display: none">
                <div class="formItem formItem_auto">
                    <div class="formTitle t1">为尽量避免录入错误，请再次录入您手中这张票据的总金额 <span class="ty-color-red">*</span></div>
                    <div class="formTitle t2">请录入本票据的价税合计 <span style="width:200px; "></span> 价税合计 <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input type="text" name="billAmount" require onkeyUp = 'testNum(this)' onchange="setToFixed2($(this));setAmount()"> 元<div class="isBillAmountTip ty-color-red">价税合计与本票据其他数据有冲突，请检查并修正！</div></div>
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">内容与本票据完全相同，且开票月份也为<span class="issueMonth"></span>的票据总数量 <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input data-type="num" id="billnum" name="number" require onchange="setAmount()" onkeyUp = 'clearNum(this)' placeholder="1"> 张</div>
                </div>
                <div class="formItem formItem_auto repeatBill" style="display: none">
                    <div class="formTitle">本张发票号码为 <span class="thisBillNo"></span>，<span class="ty-color-orange">请输入其他 <span class="thisBillNum"></span>张发票的发票号码 <span class="ty-color-red">*</span></span> </div>
                    <div class="formCon"><div class="repeatCon"></div></div>
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">如您想要申请报销的金额不是 <span class="ty-color-blue countAllAmount"></span>元，则请修改申请报销的金额  <span class="ty-color-red">*</span></div>
                    <div class="formCon"><input class="inputAmount" name="amount" data-type="num" require onkeyUp = 'testNum(this)' onchange="setToFixed2($(this));"> 元</div>
                </div>
                <%-- <div class="formItem formItem_auto">
                     <div class="formTitle">备注</div>
                     <div class="formCon"><textarea name="memo"  cols="30" rows="2" style="width: 500px" onkeyup="countWords($(this),60)"></textarea><div class="textMax text-right">255/255</div></div>
                 </div>--%>
                <div class="ty-alert ty-alert-info">
                    您可点击浏览以上传票据图片。票据是否需上传、如何拍照、是否粘贴等，请按财务的要求。
                </div>
                <div class="formItem formItem_auto">
                    <div class="formTitle">
                        <span>票据图片</span>
                    </div>
                    <div class="formCon">
                        <div class="formConBg">
                            <div class="cp_imgShow clearfix"></div>
                            <div class="cp_imgUpload"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelfile()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="billEntryBtn" type="btn" data-name="billEntry">确定</button>
        </div>
    </div>
</div>

<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-green" id="goodEntry" style="min-width:600px;">
        <div class="bonceHead">
            <span>票据录入</span>
            <a class="bounce_close" onclick="cancelGoodsEntery()  "></a>
        </div>
        <div class="bonceCon clearfix">
            <form action="" id="form_goodEntry">
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 票据种类</span>
                    </div>
                    <div class="formCon">
                        <select name="billCat" id="billCat" require></select>
                    </div>
                </div>
                <div class="ty-nextTip">想要录入的票据上只有一行内容吗？<br>
                    <span type="btn" data-name="iconOneRow" data-val="1"><i class="fa fa-circle-o"></i>是</span>
                    <input type="hidden" id="onlyOneRow"><span class="wid"></span>
                    <span type="btn" data-name="iconOneRow" data-val="0"><i class="fa fa-circle-o"></i>不，内容超过一行</span>
                </div>
                <div>
                    <div class="kindInvoice">
                        <%--增值税发票 普通发票 和 收据 --%>
                        <div class="VATInvoice" style="display: none">
                            <div class="formItem">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> 开票日期</span></div>
                                <div class="formCon"><input type="text" name="issueDate" class="issueDate" id="issueDate" require></div>
                            </div>
                            <div class="formItem formItem_billNo">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> 发票号码</span></div>
                                <div class="formCon"><input type="text" class="billNo" onchange='countWords($(this),8)' require></div>
                            </div>
                            <div class="ty-alert ty-alert-info">！请录入您手中这张票据的内容。<span class="ty-color-orange">如该票据中有多行内容</span>，则先录第一行。</div>
                            <div class="formItem firstFeeCat">
                                <div class="formTitle">
                                    <span><span class="ty-color-red">*</span> 费用类别</span>
                                </div>
                                <div class="formCon">
                                    <select class="feeCat" id="feeCat" require>
                                        <option value="0">---请选择费用类别---</option>
                                    </select>
                                </div>
                            </div>
                            <div class="formItem secondFeeCat" style="display: none">
                                <div class="formTitle">
                                    <span><span class="ty-color-red">*</span></span>
                                </div>
                                <div class="formCon">
                                    <select class="secondFeeCat" require>
                                        <option value="0">---请选择二级费用类别---</option>
                                    </select>
                                </div>
                            </div>
                            <div class="formItem formItem_itemName">
                                <div class="formTitle"><span class="ty-color-red">* </span><span class="titleName" id="m1">货物或应税劳务、服务名称</span></div>
                                <div class="formCon"><input type="text" name="itemName" onchange='countWords($(this),14)' require></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>规格型号</span></div>
                                <div class="formCon"><input type="text" name="model" onchange='countWords($(this),10)'></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>单位</span></div>
                                <div class="formCon"><input type="text" name="unit" onchange='countWords($(this),6)'></div>
                            </div>
                            <div class="formItem formItem_itemQuantity">
                                <div class="formTitle"><span><span class="ty-color-red">*</span> <span class="titleName">数量</span></span></div>
                                <div class="formCon"><input type="text" name="itemQuantity" onkeyUp = 'clearNum(this)' onchange='countWords($(this),10)' require></div>
                            </div>
                            <div class="formItem">
                                <div class="formTitle"><span>单价</span></div>
                                <div class="formCon"><input type="text" name="uniPrice" disabled></div>
                            </div>
                            <div class="formItem formItem_price">
                                <div class="formTitle"><span><span class="ty-color-red">*</span><span class="titleName">金额</span></span></div>
                                <div class="formCon"><input type="text" name="price" onkeyUp = 'testNum(this)' onchange="countWords($(this),8);setToFixed2($(this))" require></div>
                            </div>
                            <div class="taxInput">
                                <div class="formItem">
                                    <div class="formTitle"><span>税率</span></div>
                                    <div class="formCon"><input type="text" name="taxRate" disabled style="width: 158px"> %<div class="tipTaxRate ty-color-red text-right">税率超过100%</div></div>
                                </div>
                                <div class="formItem">
                                    <div class="formTitle"><span><span class="ty-color-red">*</span> 税额</span></div>
                                    <div class="formCon"><input type="text" name="taxAmount" onkeyUp = 'testNum(this)' onchange="countWords($(this),8);setToFixed2($(this))" require></div>
                                </div>
                                <div class="formItem">
                                    <div class="formTitle"><span>含税合计</span></div>
                                    <div class="formCon"><input type="text" name="amount" onchange="countWords($(this),8);setToFixed2($(this))" disabled></div>
                                </div>
                            </div>
                            <div class="formItem formItem_itemMemo">
                                <div class="formTitle"><span>发票上的备注</span></div>
                                <div class="formCon"><input type="text" class="billMemo" name="memo" onchange="countWords($(this), 30)" ><span class="textMax"></span></div>
                            </div>
                        </div>
                        <%--定额发票内容--%>
                        <div class="quotaInvoice" style="display: none">
                            <div class="ty-alert ty-alert-info">！ 您在此可录入多种定额发票。请按财务要求上传票据照片。系统支持每行均上传一张。</div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td><span class="ty-color-blue" data-name="newRow" type="btn" id="newRow">增加行</span></td>
                                </tr>
                                <tr>
                                    <td><span class="ty-color-red">*</span>费用类别</td>
                                    <td><span class="ty-color-red">*</span>单张发票金额</td>
                                    <td><span class="ty-color-red">*</span>数量</td>
                                    <td>发票金额合计</td>
                                    <td><span class="ty-color-red">*</span>实际支出金额</td>
                                </tr>
                                </thead>
                                <tbody> </tbody>
                            </table>
                            <div class="summaryTip" id="summary2">您已录入定额发票共 0 张，发票金额总计 0 元，实际支出（即您将报销）总计 0 元。</div>
                            <div>
                                <%--<div class="formItem">--%>
                                <%--<span>备注</span> <input type="text" id="memo" style="display: inline-block; width:400px; ">--%>
                                <%--</div>--%>
                            </div>

                        </div>
                        <%--单行录入增普/其他发票/收据--%>
                        <div class="oneRowInvoice">
                            <div class="ty-alert ty-alert-info">！ 您在此可录入多张只有一行内容的 <span class="in4"></span>，请按财务要求上传票据照片。系统支持每行均上传一张。</div>
                            <div>
                                <span type="btn" data-name="inputNextBtn" id="inputNextBtn" class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-3">录入下一张只有一行内容的增值税普通发票</span>
                                <div class="ty-alert ty-alert-info ty-clear"> </div>
                                <table class="ty-table ty-table-control oneRowTab" style="min-width:1500px;">
                                    <thead>
                                    <tr>
                                        <td><span class="ty-color-red">*</span>费用类别</td>
                                        <td><span class="ty-color-red in2">*</span>发票号码</td>
                                        <td><span class="ty-color-red">*</span>开票日期</td>
                                        <td><span class="ty-color-red">*</span><span class="in3">货物或应税劳务、服务名称</span><span class="in1">票据内容</span></td>
                                        <td>规格型号</td>
                                        <td>单位</td>
                                        <td>数量</td>
                                        <td><span class="ty-color-red">*</span><span id="in4">发票金额</span></td>
                                        <td><span class="ty-color-red">*</span>实际支出金额</td>
                                        <td>发票上的备注</td>

                                    </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                            <div class="summaryTip" id="summaryBen2"></div>
                            <div>
                                <%--<div class="formItem">--%>
                                <%--<span>备注</span> <input type="text" id="memo2" name="memo" style="display: inline-block; width:400px; ">--%>
                                <%--</div>--%>
                            </div>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelGoodsEntery() ">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="goodEntryBtn" type="btn" data-name="sureGoodEntry">录入完毕，下一步</button>
        </div>
    </div>
</div>

<div class="bounce_Fixed3">
    <div class="bonceContainer bounce-red" id="tip" style="min-width:600px;">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center">
                财务规定：<span class="ty-color-red">一次报销中不可既有发票又有收据</span>。
                <div class="tip"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="selectFee">
        <div class="bonceHead">
            <span>选择费用类别</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center">
                <p>费用类别：<select onchange='setNextCat($(this))' class="feeCat_1"></select></p>
                <p style="display: none;" id="pFeeCat2">二级类别：<select class="feeCat_2" ></select></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="selectFeeOk();  ">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addMemo">
        <div class="bonceHead">
            <span>发票上的备注</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center">
                <textarea placeholder="您手中发票上的备注如有内容，您可在此录入。如觉不必要录入，或无备注，则请忽略。"
                          id="memo3" onkeyup="countWords($(this),60)"></textarea>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="addMemoOk();  ">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="tipDelTr">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="text-align: center" class="deltip">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel() ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="tipDelTrOk() ">确定</span>
        </div>
    </div>
    <a href="" target="_blank" download="" id="bigScan"></a>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>数据录入</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="  position:relative;  ">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="maincontainer">
                    <div class="dataNav">
                        <div class="itemTr">
                            <div class="itemTrtd1"><span class="ty-color-red">*</span>数据类别</div>
                            <div class="itemTrtd2 " id="nav">
                                <select class="form-control" id="firstname" onchange="dataImportNav($(this))">
                                    <option value="">请选择</option>
                                    <option value="0">收入</option>
                                    <option value="1">支出</option>
                                    <option value="2">内部非支出性转账</option>
                                </select>

                            </div>
                        </div>
                    </div>
                    <div class="dataContainer hd">
                        <div class="notTransfor ">
                            <div class="itemTr income ">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 类别</div>
                                <div class="itemTrtd2">
                                    <select id="genre" onchange="changeCatergray($(this))">
                                        <option value="">请选择类别</option>
                                        <option value="1">客户的回款</option>
                                        <option value="2">款项借入</option>
                                        <option value="6">收回借款</option>
                                        <option value="3">投资款</option>
                                        <option value="4">废品</option>
                                        <option value="5">其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="itemTr expend exCat">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span>支出类别</div>
                                <div class="itemTrtd2">
                                    <select class="form-control" id="lastname" onchange="expendMethod($(this))">
                                        <option value="0">请选择</option>
                                        <option value="1">本人的公务支出（财务费用除外、需入库物品的购买费用除外）</option>
                                        <option value="2">其他同事的公务支出（财务费用除外、需入库物品的购买费用除外）</option>
                                        <option value="3">需入库物品购买费用的报销</option>
                                        <option value="9">款项借出</option>
                                        <option value="10">还款</option>
                                        <option value="4">社保/公积金</option>
                                        <option value="5">税款</option>
                                        <option value="6">汇划费</option>
                                        <option value="7">其他财务费用的支出</option>
                                        <option value="8">以上类别以外的支出</option>
                                    </select>
                                </div>
                            </div>
                            <div class="swi3">
                                <p class="bluetip">此类支出也可由您同事在其“日常事务-我要报销”中自行录入！</p>
                                <div class="itemTr swi32">
                                    <div class="itemTrtd1"><span class="ty-color-red">*</span>同事姓名</div>
                                    <div class="itemTrtd2">
                                        <select id="partnerName"></select>
                                    </div>
                                </div>
                                <div class="itemTr swi32">
                                    <div class="itemTrtd1"></div>
                                    <div class="itemTrtd2">
                                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5"
                                              style="width: 100px; text-align: center; position: relative; left: 410px;"
                                              onclick="swi32Next()">下一步</span>
                                    </div>
                                </div>
                            </div>
                            <div class="huihua">
                                <div class="itemTr">
                                    <div class="itemTrtd1"><span class="ty-color-red">*</span>支出日期</div>
                                    <div class="itemTrtd2"><input type="text" id="paydate"></div>
                                </div>
                                <div class="itemTr">
                                    <div class="itemTrtd1">用途/摘要</div>
                                    <div class="itemTrtd2"><input type="text" id="summaryPurpose" value="汇划费" disabled style="background: #eee;"></div>
                                </div>
                                <div class="itemTr">
                                    <div class="itemTrtd1"><span class="ty-color-red">*</span>支出金额</div>
                                    <div class="itemTrtd2"><input type="text" id="payAmount"></div>
                                </div>
                                <div class="itemTr">
                                    <div class="itemTrtd1"><span class="ty-color-red">*</span>开户行</div>
                                    <div class="itemTrtd2"><select id="payBank"></select></div>
                                </div>
                                <div class="itemTr">
                                    <div class="itemTrtd1">凭证照片</div>
                                    <div class="itemTrtd2">
                                        （最多可上传九张）<span class="linkBtn" onclick="upBtn2()">上传票据照片</span>
                                    </div>
                                </div>
                                <div class="itemTr ex3">
                                    <div class=" hd" id="uploadBtn2"></div>
                                    <div class="imgList" id="imgList2">
                                    </div>
                                </div>
                                <div class="itemTr">
                                    <div class="itemTrtd1">备注</div>
                                    <div class="itemTrtd2"><input type="text" id="payMemo"></div>
                                </div>
                            </div>
                            <div class="shuifee">
                                <p>
                                    缴费凭证一次录入多张，且可为不同月份。如何录入，可自行决定。
                                    <span class="ty-btn ty-btn-big ty-btn-blue" onclick="rateEntry()">录 入</span>
                                    <span class="ty-btn ty-btn-big ty-btn-blue" onclick="rateSubmit()">录入完成</span>
                                </p>

                                <table class="ty-table ty-table-control" id="taxTab" style="width:80%; margin:0 auto;">
                                    <tr>
                                        <td>税种</td>
                                        <td>税款所属时期</td>
                                        <td>实缴金额</td>
                                        <td>支出方式</td>
                                        <td>操作</td>
                                    </tr>
                                    <tr></tr>
                                </table>

                            </div>
                            <div class="itemTr isGenre isGenre2">
                                <div class="itemTrtd1"> </div>
                                <div class="itemTrtd2"><input type="text" id="categoryDesc"></div>
                            </div>
                            <div class="swi6">
                                <p class="bluetip">如不确定所录入票据是否属于“财务费用”，请先咨询会计！</p>
                            </div>
                            <div class="itemTr">
                                <div class="dataMark"></div>
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 票据日期</div>
                                <div class="itemTrtd2">
                                    <input type="text" id="categoryDate">
                                </div>
                            </div>
                            <div class="itemTr">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 用途</div>
                                <div class="itemTrtd2"><input type="text" id="purpose" onchange="getToummary()"></div>
                            </div>
                            <div class="itemTr">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 摘要</div>
                                <div class="itemTrtd2"><input type="text" id="summary"></div>
                            </div>
                            <div class="itemTr">
                                <div class="dataMark"></div>
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 票据数量</div>
                                <div class="itemTrtd2">
                                    <input style="width: 150px;" type="text" id="billQuantity" onkeyup="clearNum(this)"/>
                                    <span class="linkBtn" onclick="upBtn()">上传票据照片</span>（最多可上传九张）
                                </div>
                            </div>
                            <div class="itemTr ex3">
                                <div class=" hd" id="uploadBtn"></div>
                                <div class="imgList" id="imgList">
                                </div>
                            </div>
                            <div class="itemTr">
                                <div class="itemTrtd1"><span class="invoiceTtl">发票金额</span></div>
                                <div class="itemTrtd2"><input type="text" id="billMoney" onkeyup="clearNoNum(this)"></div>
                            </div>
                            <div class="itemTr actual1" id="billMoneyCon">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> <span class="actualTtl">金额</span></div>
                                <div class="itemTrtd2"><input type="text" id="money" onkeyup="clearNoNum(this)"></div>
                            </div>
                            <div class="itemTr income7">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> <span class="act2">实际付款日期</span></div>
                                <div class="itemTrtd2"><input type="text" id="actualDate" onkeyup="clearNoNum(this)"></div>
                            </div>
                            <div class="itemTr">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 经手人</div>
                                <div class="itemTrtd2"><input type="text" id="auditorName"></div>
                            </div>
                            <div class="income">
                                <div class="itemTr">
                                    <div class="itemTrtd1"><span class="ty-color-red">*</span> 收入方式</div>
                                    <div class="itemTrtd2">
                                        <select onchange="changeAcceptType($(this))" id="incomeMethod">
                                            <option value="1">现金</option>
                                            <option value="3">转账支票</option>
                                            <option value="4">承兑汇票</option>
                                            <option value="5">银行转账</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="itemTr">
                                    <div class="itemTrtd1"><span class="ty-color-red">*</span> 付款单位</div>
                                    <div class="itemTrtd2">
                                        <input type="text" id="oppositeCorp" placeholder="请输入付款单位">
                                    </div>
                                </div>
                                <div class="payTypeIncome hd">
                                    <div class="transf">
                                        <div class="itemTr" >
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 收到日期</div>
                                            <div class="itemTrtd2"><input type="text" id="receiveDate" class="laydate-icon"></div>
                                        </div>
                                        <div class="itemTr" >
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 支票号</div>
                                            <div class="itemTrtd2"><input type="text" id="checkNumber"></div>
                                        </div>
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span>金额</div>
                                            <div class="itemTrtd2"><input type="text" id="money3" onkeyup="clearNoNum(this)"></div>
                                        </div>
                                        <div class="itemTr" >
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 到期日</div>
                                            <div class="itemTrtd2"><input type="text" id="expireDate" class="laydate-icon"></div>
                                        </div>
                                        <div class="itemTr" >
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 出具的单位</div>
                                            <div class="itemTrtd2"><input type="text" id="originalCorp"></div>
                                        </div>
                                        <div class="itemTr" >
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 出具的银行</div>
                                            <div class="itemTrtd2"><input type="text" id="bankName"></div>
                                        </div>
                                    </div>
                                    <div class="accept">
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 收到日期</div>
                                            <div class="itemTrtd2"><input type="text" id="receiveDate_4"  class="laydate-icon"></div>
                                        </div>
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 汇票号</div>
                                            <div class="itemTrtd2"><input type="text" id="checkNumber_4"></div>
                                        </div>
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span>金额</div>
                                            <div class="itemTrtd2"><input type="text" id="money2" onkeyup="clearNoNum(this)"></div>
                                        </div>
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 到期日</div>
                                            <div class="itemTrtd2"><input type="text" id="expireDate_4"  class="laydate-icon"></div>
                                        </div>
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 最初出具的单位</div>
                                            <div class="itemTrtd2"><input type="text" id="originalCorp_4"></div>
                                        </div>
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 出具的银行</div>
                                            <div class="itemTrtd2"><input type="text" id="bankName_4"></div>
                                        </div>
                                    </div>
                                    <div class="bank">
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 到账时间</div>
                                            <div class="itemTrtd2"><input type="text" id="receiveAccountDate" class="laydate-icon"></div>
                                        </div>
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 收款银行</div>
                                            <div class="itemTrtd2">
                                                <select id="financeAccountId">
                                                    <option value=""><span class="ty-color-red">*</span> 选择收款银行</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="expend hd">
                                <div class="itemTr">
                                    <div class="itemTrtd1"><span class="ty-color-red">*</span> 支出方式</div>
                                    <div class="itemTrtd2">
                                        <select onchange="changeAccountType($(this))" id="method">
                                            <option value="1">现金</option>
                                            <option value="3">转账支票</option>
                                            <option value="4">承兑汇票</option>
                                            <option value="5" selected="selected">银行转账</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="payTypeExpend hd">
                                    <div class="transf ">
                                        <div class="itemTr" >
                                            <div class="itemTrtd1"></div>
                                            <div class="itemTrtd2">
                                                <select onchange="changeTypeOfTransform($(this))" id="withinOrAbroad">
                                                    <option value="0">请选择支票</option>
                                                    <option value="1">内部支票</option>
                                                    <option value="2">外部支票</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="Inner hd" id="Inner">
                                            <div class="itemTr" >
                                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 银行账户</div>
                                                <div class="itemTrtd2">
                                                    <select  id="financeAccountId_payout" onchange="setCheckNo($(this).val())">
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="itemTr" >
                                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 支票号</div>
                                                <div class="itemTrtd2">
                                                    <select id="checkNumber_payout" >
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="itemTr" >
                                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 支票到期日</div>
                                                <div class="itemTrtd2"><input type="text" id="expireDate_payout" class="laydate-icon"></div>
                                            </div>
                                            <div class="itemTr" >
                                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 接收日期</div>
                                                <div class="itemTrtd2"><input type="text" id="receiveDate_payout" class="laydate-icon"></div>
                                            </div>
                                            <div class="itemTr" >
                                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 接收经手人</div>
                                                <div class="itemTrtd2"><input type="text" id="receiver_payout"></div>
                                            </div>
                                            <div class="itemTr" >
                                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 支付经手人</div>
                                                <div class="itemTrtd2"><input type="text" id="operator_payout"></div>
                                            </div>
                                        </div>
                                        <div class="Outer hd" id="Outer">
                                            <div class="itemTr" >
                                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 支票号</div>
                                                <div class="itemTrtd2"><input type="text" id="outerCheckNo" disabled></div>
                                                 <input type="text" id="outerCheckid" style="display: none;"> 
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accept ">
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 汇票号</div>
                                            <div class="itemTrtd2">
                                                <span onclick="selectAcceptBtn()" id="checkNo_payTypeExpendAccept">选择承兑汇票号</span>
                                                <input type="text" id="checkId_payTypeExpendAccept" style="display: none;">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bank">
                                        <div class="itemTr">
                                            <div class="itemTrtd1"><span class="ty-color-red">*</span> 转账银行</div>
                                            <div class="itemTrtd2">
                                                <select id="financeAccountId_payTypeExpendBank">
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--把这个DIV 写到外面去这个就是弹框的父元素-->

                                <div class="clr"></div>
                                <div class="itemTr" style="height: 20px; ">
                                    <div class="itemTrtd1"> </div>
                                    <div class="itemTrtd2">
                                        <span style="border: none; text-align: right;">
                                            <span onclick="addOopBtn()" class="linkBtn" style=" position: relative; right: -2px; top: -20px;">新增</span>
                                        </span>
                                    </div>
                                </div>
                                <div class="itemTr">
                                    <div class="itemTrtd1"><span class="ty-color-red">*</span> 收款单位</div>
                                    <div class="itemTrtd2">
                                        <span class="stakeholderCategoryContsiner">
                                            <span id="stakeholderCategoryText" onclick="openOop()"></span>
                                            <span id="stakeholderCategory" >
                                                <option value="0">请选择</option>
                                                <option value="1">供应商</option>
                                                <option value="2">公司职工</option>
                                                <option value="3">财务自行录入的收款单位（含个人）</option>
                                            </span>
                                        </span>
                                    </div>
                                </div>
                                <div class="itemTr oop2">
                                    <div class="itemTrtd1"></div>
                                    <div class="itemTrtd2">
                                        <input type="text" readonly id="oppositeCorp2"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="transfer hd">
                            <div class="itemTr">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 转账种类</div>
                                <div class="itemTrtd2">
                                    <select onchange="setTransMethod( $(this).val() )" id="method_transfer">
                                        <option value="6">存现金</option>
                                        <option value="7">取现金</option>
                                        <option value="8">其他内部转账</option>
                                    </select>
                                </div>
                            </div>
                            <div class="methodTrans">
                                <div id="cun">
                                    <div class="itemTr">
                                        <div class="itemTrtd1"><span class="ty-color-red">*</span> 付款账户</div>
                                        <div class="itemTrtd2">
                                            <span>备用金/现金账户</span><input type="text" id="fromId_cun" style="display:none; ">
                                        </div>
                                    </div>
                                    <div class="itemTr">
                                        <div class="itemTrtd1"><span class="ty-color-red">*</span> 收款账户</div>
                                        <div class="itemTrtd2">
                                            <select id="toId_cun">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="hd" id="qu">
                                    <div class="itemTr">
                                        <div class="itemTrtd1"><span class="ty-color-red">*</span> 付款账户</div>
                                        <div class="itemTrtd2">
                                            <select id="fromId_qu" onchange="changeFromId($(this).val())">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="itemTr">
                                        <div class="itemTrtd1"><span class="ty-color-red">*</span> 收款账户</div>
                                        <div class="itemTrtd2">
                                            <span>备用金/现金账户</span>
                                            <input type="text" id="toId_qu" style="display:none; ">
                                        </div>
                                    </div>
                                    <div class="itemTr" id="EnCrashMent">
                                        <div class="itemTrtd1"><span class="ty-color-red">*</span> 是否使用现金支票</div>
                                        <div class="itemTrtd2">
                                                <span class="radioBtn" onclick="changeEnCrashMent($(this))"  >
                                                    <span class="val">{"value":1 , "name":"cashable"}</span>
                                                    <span class="radioShow"></span>
                                                </span>
                                                <span class="valShow">是</span>
                                                <span class="radioBtn" onclick="changeEnCrashMent($(this))"  >
                                                    <span class="val">{"value":2 , "name":"cashable"}</span>
                                                    <span class="radioShow"></span>
                                                </span>
                                                <span class="valShow">否</span>
                                        </div>
                                    </div>
                                    <div class="itemTr" id="quCheckCon" style="display: none;">
                                        <div class="itemTrtd1"><span class="ty-color-red">*</span> 支票号</div>
                                        <div class="itemTrtd2">
                                            <select id="checkId_qu">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="hd" id="tans">
                                    <div class="itemTr">
                                        <div class="itemTrtd1"><span class="ty-color-red">*</span> 付款账户</div>
                                        <div class="itemTrtd2">
                                            <select id="fromId_tans">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="itemTr">
                                        <div class="itemTrtd1"><span class="ty-color-red">*</span> 收款账户</div>
                                        <div class="itemTrtd2">
                                            <select id="toId_tans">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="itemTr">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 转账金额</div>
                                <div class="itemTrtd2"><input type="text" id="transMoeny" onkeyup="clearNoNum(this)"></div>
                            </div>
                            <div class="itemTr">
                                <div class="itemTrtd1"><span class="ty-color-red">*</span> 业务发生日期</div>
                                <div class="itemTrtd2"><input type="text" id="occurrenceTime" class="laydate-icon"></div>
                            </div>
                        </div>
                        <div class="itemTr memotr" >
                            <div class="itemTrtd1">备注</div>
                            <div class="itemTrtd2"><input type="text" id="memo"></div>
                        </div>
                        <div class="footer center">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="submitCheck" onclick="submitCheck()">保存</span>
                            <%--<span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelCheck()">取消</span>--%>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript" src="../script/finance/dataImport.js?v=SVN_REVISION"></script>
<script type="text/javascript" src="../script/finance/dailyAffairsCommon2.js?v=SVN_REVISION"></script>
<%@ include  file="../../common/footerBottom.jsp"%>