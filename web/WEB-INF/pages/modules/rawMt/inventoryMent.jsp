<%--
  Created by IntelliJ IDEA.
  User: heb20
  Date: 2023/12/13
  Time: 11:19
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/rawMt/inventoryMent.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed2"></div>
<div class="bounce_Fixed"></div>
<div class="bounce"></div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>盘点管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div class="ty-container companyDetails">
                <div class="clear choseraw opacel oteradd ty-alert company">
                    <div class="ty-left choserant placne">
                        <span class="dgrap">其他年份</span>
                        <span class="second dgrap">
                                <select id="chostoryment" onchange="" class="sel-rund unclick"></select>
                            </span>
                    </div>
                    <div class="ty-right">
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="" style="background: rgb(204, 204, 204); cursor: not-allowed;">自主盘点</span>
                    </div>
                </div>
                <div class="pageStyle container_item">
                    <div class="clear initBody bnck">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>月份</td>
                                <td>已完成自主盘点</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>XXXX年XX月</td>
                                    <td>XX次</td>
                                    <td>
                                        <span class="ty-color-blue" style="color: grey; cursor: not-allowed;">查看</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/rawMt/inventoryMent.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>