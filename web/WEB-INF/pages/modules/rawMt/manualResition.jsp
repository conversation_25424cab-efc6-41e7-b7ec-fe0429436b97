<%--
  Created by IntelliJ IDEA.
  User: heb20
  Date: 2023/12/14
  Time: 16:42
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

<link href="../css/rawMt/manualResition.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%--选择材料--%>
    <div class="bonceContainer bounce-blue" id="taksectmral" style="width: 544px;">
        <div class="bonceHead">
            <span>选择材料</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon bonceConbox">
            <div class="takone" style="margin-bottom: 20px;">
                <div class="citem gap">
                    <span class="ty-color-red">*</span>材料代号
                </div>
                <div>
                    <select class="material ibige" onchange="getMaterm($(this),1)"></select>
                </div>
            </div>
            <div class="taktwo" style="margin-bottom: 20px;">
                <div class="citem gap">
                    <span class="ty-color-red">*</span>材料名称
                </div>
                <div>
                    <select class="materialname ibige" onchange="getMaterm($(this),2)"></select>
                </div>
            </div>
            <div class="takthree flex-box plence2">
                <div class="citem">
                    <div class="cale_ttl2 caleall">
                        <span>规格</span>
                    </div>
                    <div class="sale_con2 saleall">
                        <input type="text" id="specifications" class="scale unclickhide" name="specificatial" disabled="disabled"
                               style="width: 150px;">
                    </div>
                </div>
                <div class="citem">
                    <div class="cale_ttl3 caleall">
                        <span>型号</span>
                    </div>
                    <div class="sale_con3 saleall">
                        <input type="text" id="model" class="scale unclickhide" name="modelal" style="width: 150px;" disabled="disabled">
                    </div>
                </div>
                <div class="citem">
                    <div class="cale_ttl4 caleall">
                        <span>计量单位</span>
                    </div>
                    <div class="sale_con4 saleall">
                        <input type="text" id="measurement" class="scale unclickhide" name="measuremental" style="width: 150px;" disabled="disabled">
                    </div>
                </div>
            </div>
            <div class="takfour" style="margin-bottom: 20px;">
                <p class="leMar gap" style="display: flex;">
                    <input type="hidden" id="chosestoation">
                    <span onclick="chosestoation(1,$(this))" class="radioCon">
                        <i id="chosestoation1" class="fa fa-circle-o"></i>从库位上选择材料
                    </span>
                    <span onclick="chosestoation(2,$(this))" class="radioCon kwal">
                        <i id="chosestoation2" class="fa fa-circle-o"></i>仅录入入库数量，暂不选择库位
                    </span>
                </p>
                <div class="streatbox2" style="display: none;">
                    <div class="flex-box" style="margin-bottom: 20px;display: flex;justify-content: space-between;">
                        <div>
                            <div class="citem gap">
                                <span class="ty-color-red">*</span>供应商
                            </div>
                            <div>
                                <select class="suppliduct ibige2" onchange="getSuppmore($(this))"></select>
                            </div>
                        </div>
                        <div>
                            <div class="citem gap">
                                <span class="ty-color-red">*</span>数量
                            </div>
                            <div>
                                <input type="text" placeholder="请录入" class="protest1">
                                <span class="blueLinkBtn ty-color-blue funBtn thin-btn addmore" onclick="addMore(1)">新增</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="streatbox3" style="display: none;">
                    <div class="up gap">
                        <div class="citem gap">
                            <span class="ty-color-red">*</span>供应商
                        </div>
                        <div>
                            <select class="suppliduct ibige" onchange="getSuppmore($(this))"></select>
                        </div>
                    </div>
                    <div class="bottom stoboot">
                        <div class="flex-box ston2" style="display: flex;justify-content: space-between;">
                            <div>
                                <div class="citem gap">
                                    <span class="ty-color-red">*</span>库位
                                </div>
                                <div>
                                    <select class="stoation ibige2" onchange="getstoration($(this))"></select>
                                </div>
                            </div>
                            <div>
                                <div class="citem gap">
                                    <span class="ty-color-red">*</span>数量
                                </div>
                                <div>
                                    <input type="text" placeholder="请录入" class="protest2">
                                    <span class="blueLinkBtn ty-color-blue funBtn thin-btn addmore" onclick="addMore(2)">新增</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel();" id="closewinden">取消</span>
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="backlast()" id="lastpont">上一步</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontnext()" id="nextture">下一步</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="madehsre()" id="mahesure">确定</span>
        </div>
    </div>
    <%--删除弹窗1--%>
    <div class="bonceContainer bounce-red" id="dettelpont1">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="name">
                <p class="detel">
                    <span>确定删除吗?</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--删除弹窗3--%>
    <div class="bonceContainer bounce-red" id="dettelpont3">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="name">
                <p class="detel2">
                    <span>确定后，所选库位及所录数量将被清空。</span>
                    <span>确定继续吗？</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontsure(3)">确定</span>
        </div>
    </div>
    <%--删除弹窗2--%>
    <div class="bonceContainer bounce-red" id="dettelpont2">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="name">
                <p class="detel">
                    <span>确定后，已录入数据将被清空</span>
                    <span>确定继续吗？</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="pontsure()">确定</span>
        </div>
    </div>
    <%--选择材料提示弹窗--%>
    <div class="bonceContainer bounce-blue" id="chosepropo">
        <div class="bonceHead">
            <span>选择材料</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="name">
                <p class="detel">
                    <span>放置于各库位材料数量的和，与所录入的总数不相等。</span>
                    <br />
                    <span>请检查并更正。</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2"></div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>手动领料</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="taktoper plence">
                    <div class="takone bonceCon boxall">
                        <div class="takleft movea">
                            <div class="takttl1 gap">
                                <span class="ty-color-red">*</span>领料日期
                            </div>
                            <div class="takcon1">
                                <input id="taklistdate" class="ty-inputText ibige" value="" placeholder="请选择" name="taklisttime" require/>
                            </div>
                        </div>
                        <div class="takright moveb">
                            <div class="takttl2 gap">
                                <span>领料用途</span>
                            </div>
                            <div class="takcon2">
                                <select class="takuseplce ibige">
                                    <option value="" style="color: #606266;">请选择</option>
                                    <option value="1">生产</option>
                                    <option value="2">炼制</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="taktwo bonceCon boxall">
                        <div class="takleft movea">
                            <div class="takttl3 gap">
                                <span>领料人</span>
                            </div>
                            <div class="takcon3">
                                <select class="takpicker ibige"></select>
                            </div>
                        </div>
                        <div class="takright moveb">
                            <div class="takttl4 namefont gap" style="display: flex;">
                                <span>备注</span>
                                <span class="numbsersi movec">0</span>/30
                            </div>
                            <div class="takcon4 numberfont">
                                <input type="text" id="addeqthngd" class="add_eqthng inpfont ibige" name="eqthng">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="takbotter plence">
                    <div class="top boxall2">
                        <div class="title" style="margin: 0 82px;">
                            待领库的材料
                            <span class="ty-color-blue funBtn thin-btn pull-right" onclick="chosepret()" id="choseprojet" style="font-weight: bold;">选择材料</span>
                        </div>
                    </div>
                    <div class="bottom boxall2" style="padding: 10px 175px;">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>数量</td>
                                <td>计量单位</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>XXXXX</td>
                                <td>XXXX</td>
                                <td>XX</td>
                                <td></td>
                                <td>XXX.XX</td>
                                <td>XXX</td>
                                <td>
                                    <span class="ty-color-red" onclick="detaddlink()">移除</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="bottener boxall2" style="padding: 10px 175px;">
                        <div class="pull-right">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="madesure2()">提交</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/rawMt/manualResition.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>

</body>
</html>