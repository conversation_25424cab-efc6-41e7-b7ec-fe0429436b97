<%--
  Created by IntelliJ IDEA.
  User: heb20
  Date: 2023/12/18
  Time: 9:43
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

<link href="../css/rawMt/entryExitRecords.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce"></div>
<div class="bounce_Fixed"></div>
<div class="bounce_Fixed2"></div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>出入库记录</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div class="ty-container">
<%--                <button type="button" class="btn btn-default stockJump" onclick="stockJump(0)">返回原辅材料库主页</button>--%>
                <div class="pageStyle container_item">
                    <div data-part="6">
                        <div id="checkInOutInfo">
                            <ul class="ty-secondTab">
                                <li class="ty-active">入库流水</li>
                                <li>领料流水</li>
                            </ul>
                            <%--待入库--%>
                            <div class="ty-mainData">
                                <div class="p0">
                                    <%--原辅材料库——入库流水--%>
                                    <h3>原辅材料库——入库流水</h3>
                                    <table class="ty-table ty-table-none bg-yellow" id="inStorageInfo">
                                        <thead>
                                        <tr>
                                            <td>材料代号</td>
                                            <td>材料名称</td>
                                            <td>规格</td>
                                            <td>型号</td>
                                            <td>计量单位</td>
                                            <td>入库数量</td>
                                            <td>供应商</td>
                                            <td>仓库确认时间</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="pageP0"></div>
                                </div>
                                <div class="p1" style="display: none;">
                                    <%--原辅材料库——领料流水--%>
                                    <h3>原辅材料库——领料流水</h3>
                                    <table class="ty-table ty-table-none bg-yellow" id="outStorageInfo" >
                                        <thead>
                                        <tr>
                                            <td>材料代号</td>
                                            <td>材料名称</td>
                                            <td>规格</td>
                                            <td>型号</td>
                                            <td>计量单位</td>
                                            <td>领料数量</td>
                                            <td>领料部门</td>
                                            <td>仓库确认时间</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="pageP1"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="ye_accept"></div>
            </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/rawMt/entryExitRecords.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>

</body>
</html>