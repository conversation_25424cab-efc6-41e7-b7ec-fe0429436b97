<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/wenjian.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/discussion/discussion.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<style>
    .mainChat .messageInput{
        min-height: 31px;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" onclick="$('.atContainer').hide();">
<%-- 参与人的弹窗 --%>
<div class="atContainer">
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce_Fixed2">
    <%--查看历史记录详情--%>
    <div class="bonceContainer bounce-green " id="seeHistoryDetail">
        <div class="bonceHead">
            <span>讨论组详情</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="field">
                <label>主题：</label>
                <div class="see_title"></div>
            </div>
            <div class="field">
                <label>描述：</label>
                <div class="see_des"></div>
            </div>
            <div class="field">
                <label>主持人：</label>
                <div class="see_compere"></div>
            </div>
            <div class="field">
                <label>参与人</label>
                <div class="see_roleList"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--confirm--%>
    <div class="bonceContainer bounce-blue" id="fixed_confirm">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 iknowBtn">确定</button>
        </div>
    </div>
    <%--tip--%>
    <div class="bonceContainer bounce-blue" id="fixed_tip">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn">确定</button>
        </div>
    </div>

    <%--修改记录--%>
    <div class="bonceContainer bounce-green " id="discussDetailChangeHistory" style="width: 600px">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 580px">
            <div class="ty-alert ty-alert-warning changeTip">
                当前资料为第n次修改后的结果。<div class="ty-right">修改人：</div>
            </div>
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td>操 作</td>
                    <td>发起人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 sureBtn">确定</span>
        </div>
    </div>

    <%--查看讨论详情--%>
    <div class="bonceContainer bounce-green " id="seeDetail">
        <div class="bonceHead">
            <span>查看详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <label>审批记录</label>
                <span class="ty-btn ty-btn-blue ty-circle-2 ty-right" type="btn" data-name="discussDetailChangeHistory">修改记录</span>
            </div>
            <div class="see_processList"></div>
            <div>
                <button class="ty-btn ty-btn-green ty-circle-2 ty-right" type="btn" data-name="discussDetailChange" id="discussDetailChangeBtn">修改</button>
            </div>
            <div class="field">
                <label>主题：</label>
                <div class="see_title"></div>
            </div>
            <div class="field">
                <label>描述：</label>
                <div class="see_des"></div>
            </div>
            <div class="field">
                <label>主持人：</label>
                <div class="see_compere"></div>
            </div>
            <div class="field">
                <label>附件列表</label>
            </div>
            <div class="see_fileList"></div>
            <div class="field">
                <label>参与人</label>
                <div class="roleList"></div>
            </div>

        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>处理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="app_avatar">
        <div class="themeList" drag="right">
            <div class="searchBar">

            </div>
            <ul class="list"></ul>
        </div>
        <div class="mainChat">
            <div class="chatBar">
                <div class="chat_title">
                    主题：<span class="ty-color-blue title"></span>
                    <span class="m_btn" type="btn" data-name="seeDetail">查看详情 <i class="fa fa-angle-down"></i></span>
                </div>
            </div>
            <div class="unReadMsg" type="btn" data-name="unReadMsg"><i class="fa fa-angle-double-up"></i> <span class="num"></span></div>
            <div class="chat_avatar">
                <div class="text-center"><span class="seeMore" type="btn" data-name="seeMore"><i class="fa fa-angle-double-up"></i> 查看更多消息</span></div>
                <div class="chat_main"></div>
            </div>
            <div class="messageInput" drag="top">
                <div class="textInput_disabled">
                    <div class="toolbarbox">
                        <div class="history" type="btn" data-name="messageHistory"><i class="fa fa-clock-o"></i>消息记录</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="history_avatar">
            <div class="functionBar">

            </div>
            <div class="historyBar">
                <ul class="historyTab">
                    <li class="active" to="roleList">参与人 <span class="corner-word"></span></li>
                    <li to="allMessage">全部消息</li>
                    <li to="imageList">图片 <span class="sort sortName"><span class="corner-word"></span> <i class="fa fa-long-arrow-down fa-long-arrow-up"></i></span></li>
                    <li to="fileList">文件 <span class="sort sortName"><span class="corner-word"></span> <i class="fa fa-long-arrow-down fa-long-arrow-up"></i></span></li>
                </ul>
            </div>
            <div class="tabCon" id="roleList">
                <ul class="tblContainer"></ul>
            </div>
            <div class="tabCon allMessage" id="allMessage" style="display: none">
                <div class="ty-alert-small queryAlert">已搜索到 <span class="queryNum">8</span>条消息记录 <span class="queryBack" type="btn" data-name="queryBack">返回</span></div>
                <ul class="tblContainer"></ul>
                <div class="footBar">
                    <div class="dateChoose">
                        <span class="query ty-btn ty-btn-blue" type="btn" data-name="query">查找</span>
                        <i class="fa fa-calendar"></i>
                        <input type="text" id="calendar">
                        <span class="pageBtnGroup">
                                        <i class="fa fa-angle-double-left" data-type="first"></i>
                                        <i class="fa fa-angle-left" data-type="prev"></i>
                                        <i class="fa fa-angle-right" data-type="next"></i>
                                        <i class="fa fa-angle-double-right" data-type="last"></i>
                                    </span>
                    </div>
                    <div class="queryCondition">
                        范围
                        <select name="type">
                            <option value="2">最近一周</option>
                            <option value="3">最近一个月</option>
                            <option value="4">最近三个月</option>
                            <option value="5">最近一年</option>
                            <option value="1">全部</option>
                        </select>
                        参与人
                        <select name="findUser"></select>
                        消息类别
                        <select name="mesType">
                            <option value="2">@我的</option>
                            <option value="3">回复我的</option>
                            <option value="1">全部</option>
                        </select>
                        <span class="ty-btn ty-btn-blue" type="btn" data-name="sureQuery">确定</span>
                    </div>
                </div>
            </div>
            <div class="tabCon" id="imageList" style="display: none">
                <ul class="tblContainer">
                    <div class="imageList"></div>
                </ul>
            </div>
            <div class="tabCon" id="fileList" style="display: none">
                <ul class="tblContainer">
                    <div class="fileList"></div>
                </ul>
            </div>
        </div>
    </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<!-- Include JS file. -->
<script src="../script/discussion/discussHandleIndex.js?v=SVN_REVISION"></script>
</body>
</html>