<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/fileTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/wenjian.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/discussion/discussion.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" onclick="$('.atContainer').hide();">
<%-- 参与人的弹窗 --%>
<div class="atContainer">
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="fixed2_tip">
        <div class="bonceHead">
            <span class="new_title">！提示</span>
            <a class="bounce_close cancelBtn" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3 cancelBtn" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <%--查看历史记录详情--%>
    <div class="bonceContainer bounce-green " id="seeHistoryDetail">
        <div class="bonceHead">
            <span>讨论组详情</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="field">
                <label>主题：</label>
                <div class="see_title"></div>
            </div>
            <div class="field">
                <label>描述：</label>
                <div class="see_des"></div>
            </div>
            <div class="field">
                <label>主持人：</label>
                <div class="see_compere"></div>
            </div>
            <div class="field">
                <label>参与人</label>
                <div class="see_roleList"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</button>
        </div>
    </div>
    <%--选择保存位置--%>
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 1050px">
        <div class="bonceHead">
            <span class="bounce_title">选择保存位置</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-fileContent">
                <%-- 文件夹列表 --%>
                <div class="ty-colFileTree folder_list" data-name="main"></div>
                <%-- 文件列表 --%>
                <div class="mar">
                    <ul class="ty-secondTab" id="fileSort" style="display: none">
                        <li class="ty-active">发布时间<span class="sort sortName"><i class="fa fa-long-arrow-down"></i></span></li>
                        <li type="1">文件编号<span class="sort sortName" style="display: none"><i class="fa fa-long-arrow-down"></i></span></li>
                    </ul>
                    <div class="ty-fileList mainFileList" >
                        <%--文件列表--%>
                    </div>
                    <div id="ye_con"></div>
                </div>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="sureChooseFolder" id="sureChooseFolderBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--confirm--%>
    <div class="bonceContainer bounce-blue" id="fixed_confirm">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 iknowBtn">我知道了</button>
        </div>
    </div>
    <%--tip--%>
    <div class="bonceContainer bounce-blue" id="fixed_tip">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <%--继续添加参与人--%>
    <div class="bonceContainer bounce-green" id="addRole">
        <div class="bonceHead">
            <span class="bounce_title">继续添加参与人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px; margin: 0 auto">
                <span>请选择</span>
                <div class="roleList"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>

    <%--删除参与人参与人--%>
    <div class="bonceContainer bounce-red " id="delRole">
        <div class="bonceHead">
            <span>删除</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            是否移除该参与人？
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">否</button>
            <button class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type="btn" data-name="sureDelRole">是</button>
        </div>
    </div>

    <%--修改讨论组信息--%>
    <div class="bonceContainer bounce-green " id="discussDetailChange" style="width: 600px">
        <div class="bonceHead">
            <span>修改讨论组信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 580px">
            <div style="width: 500px;margin: auto">
                <div>
                    <div class="field">
                        <label>讨论主题</label>
                        <div>
                            <input type="text" name="title" placeholder="请填写讨论主题" require onkeyup="countWords($(this),80)" style="width: 420px"><div class="textMax text-right" max="80"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>描述</label>
                        <div>
                            <textarea rows="1" type="text" name="content" placeholder="请填写内容" onkeyup="countWords($(this),500)" style="width: 420px"></textarea><div class="textMax text-right" max="500"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>主持人</label>
                        <div>
                            <select class="role_list" name="compere" require></select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="sureDiscussDetailChangeBtn" onclick="sureChangeDiscussDetail()">确定</button>
        </div>
    </div>

    <%--修改记录--%>
    <div class="bonceContainer bounce-green " id="discussDetailChangeHistory" style="width: 600px">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 580px">
            <div class="ty-alert ty-alert-warning changeTip">
                当前资料为第n次修改后的结果。<div class="ty-right">修改人：</div>
            </div>
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td>操 作</td>
                    <td>发起人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>

    <%--上传文件弹窗--%>
    <div class="bonceContainer bounce-blue" id="fileUpload" style="width: 500px">
        <div class="bonceHead">
            <span class="bounce_title">文件发布申请</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="upload_avatar">

            </div>
            <div class="inputPart">
                <div class="item-row">
                    <div class="item-title">文件编号 <span class="ty-color-red">*</span></div>
                    <div class="item-content">
                        <input type="text" name="fileNo" class="ty-inputText" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title">文件名称 <span class="ty-color-red">*</span></div>
                    <div class="item-content">
                        <input type="text" name="name" class="ty-inputText" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-row">
                    <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注：您可根据公司实际要求，修改系统给予的文件编号与文件名称</div>
                </div>
                <div class="item-row">
                    <div class="item-title item-title-long">请选择保存位置</div>
                    <div class="item-content">
                        <button class="ty-btn ty-btn-blue ty-circle-3" type="btn" data-name="chooseSaveFolder">选择</button>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title"></div>
                    <div class="item-content">
                        <div class="savePlace"></div>
                    </div>
                </div>
            </div>
            <div class="seePart" style="display: none">
                <div class="item-row">
                    <div class="item-title">文件编号</div>
                    <div class="item-content">
                        <div class="text_disabled see_fileNo"></div>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title">文件名称</div>
                    <div class="item-content">
                        <div class="text_disabled see_fileName"></div>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title">保存位置</div>
                    <div class="item-content">
                        <div class="text_disabled see_savePlace"></div>
                    </div>
                </div>
            </div>
            <div class="hr"></div>
            <div class="item-row">
                <div class="item-title item-title-long">
                    <div class="ty-radio">
                        <input type="radio" name="isNeedOther" value="0" id="noNeedOther">
                        <label for="noNeedOther"></label> <span id="uploadDirect">本文件直接提交给文管，无需他人审批</span>
                    </div>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title item-title-long">
                    <div class="ty-radio">
                        <input type="radio" name="isNeedOther" id="needOther" value="1" checked>
                        <label for="needOther"></label> 本文件需审批
                    </div>
                </div>
                <div class="item-content">
                    <select class="ty-inputSelect chooseApprover">
                        <option value="选择审批人" selected></option>
                    </select>
                </div>
            </div>
            <div class="item-column">
                <p class="inputPart">说明</p>
                <p class="seePart">换版原因</p>
                <input type="text" name="content" class="ty-inputText inputPart" placeholder="您可在此录入必要的说明。如无说明，可忽略。">
                <input type="text" name="content" class="ty-inputText seePart" placeholder="您可在此阐述换版原因。如无，可忽略。" style="display: none">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureUploadNewFile" id="sureUploadNewFileBtn">确定</button>
        </div>
    </div>

    <%--使用说明--%>
    <div class="bonceContainer bounce-blue " id="instruction" style="width: 600px">
        <div class="bonceHead">
            <span>使用说明</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px;margin: auto">
                <p>系统中您的讨论能否成功发起，最终取决于文管的审批。</p>
                <p>讨论发起如先获得上级批准，则更可能通过文管的审批。</p>
                <p>您也可直接提交给文管，而无需他人审批。</p>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">我知道了</button>
        </div>
    </div>
    <%--导入文件 - 文件换版（选择要换版的文件）--%>
    <div class="bonceContainer bounce-blue" id="chooseChangeVersionFile" style="width: 600px">
        <div class="bonceHead">
            <span>文件换版</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <span>要对哪个文件换版？请选择</span>
            <div class="fileList"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <%--新建讨论主题的提示--%>
    <div class="bonceContainer bounce-green " id="newDisTip">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>您的讨论组经审批后即可使用！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

    <%--发起讨论--%>
    <div class="bonceContainer bounce-green " id="newDiscussion" style="width: 600px">
        <div class="bonceHead">
            <span>发起讨论</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 500px;margin: auto">
                <div class="ty-alert ty-alert-warning">
                    <div>
                        <span class="ty-color-red">特别提示： 公司管理人员能查阅所有讨论内容！</span>
                        <span class="ty-color-gray">
                            所以讨论组内不要讨论与工作无关的事，更不得涉及反动、暴力、 色情、宗教、迷信或其他违法内容。
                        </span>
                    </div>
                </div>
                <div class="ty-secondTab">
                    <li class="ty-active">仅与一人讨论</li>
                    <li>发起多人讨论</li>
                </div>
                <form class="insertOneForum">
                    <div class="field">
                        <label>参与人员</label>
                        <select class="roleList" name="forumPostUser"></select>
                    </div>
                </form>
                <form id="discussion" class="insertForum" style="display: none">
                    <div class="field">
                        <label>讨论主题</label>
                        <div>
                            <input type="text" name="title" placeholder="请填写讨论主题" require onkeyup="countWords($(this),80)" style="width: 420px"><div class="textMax text-right" max="80"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>描述</label>
                        <div>
                            <textarea rows="1" type="text" name="content" placeholder="请填写内容" onkeyup="countWords($(this),500)" style="width: 420px"></textarea><div class="textMax text-right" max="500"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>附件</label>
                        <div class="file_box">
                            <div class="left_choose_file">
                                <div class="fileUpload"></div>
                                <div class="fileChooseBtn_sys">
                                    <span class="uploadify-button">系统内文件</span>
                                    <ul class="chooseDialog">
                                        <li onclick="openResourceCenter('newDiscussion', 1)">公司的文件与资料</li>
                                        <li class="disabled">我的公务云盘</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="right_show_file">
                                <div class="fileShowList"></div>
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label>参与人员</label>
                        <div class="input_choose">
                            <span class="ty-btn ty-btn-blue" onclick="chooseRole()">+</span>
                            <div class="input_show">
                            </div>
                        </div>
                    </div>
                    <div class="field" style="margin: 0">
                        <label></label>
                        <div class="input_choose_list" style="position: absolute;left: 130px;z-index: 2;"></div>
                    </div>

                    <div class="field">
                        <div class="item-title item-title-long" style="width: 250px">
                            <div class="ty-radio">
                                <input type="radio" name="isNeedApprove" id="needApprove" value="1" checked>
                                <label for="needApprove"></label> 讨论申请需审批
                            </div>
                        </div>
                        <a class="useBtn" onclick="instructionBtn()">使用说明</a>
                        <div class="item-content">
                            <select class="ty-inputSelect discussChooseApprover">
                                <option value="选择审批人" selected></option>
                            </select>
                        </div>
                    </div>
                    <div class="field">
                        <div class="ty-radio">
                            <input type="radio" name="isNeedApprove" value="0" id="noNeedApprove">
                            <label for="noNeedApprove"></label> <span>直接向文管提交讨论申请，无需他人审批</span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="discussApplyBtn" onclick="sure_NewDiscuss()">确定</button>
        </div>
    </div>

    <%--查看讨论详情--%>
    <div class="bonceContainer bounce-blue" id="seeDetail" style="width: 600px">
        <div class="bonceHead">
            <span>查看详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div>
                    <label>审批记录</label>
                    <span class="ty-btn ty-btn-blue ty-circle-3 ty-right" type="btn" data-name="discussDetailChangeHistory">修改记录</span>
                </div>
                <div class="see_processList"></div>
                <div>
                    <button class="ty-btn ty-btn-green ty-circle-3 ty-right" type="btn" data-name="discussDetailChange" id="discussDetailChangeBtn">修改</button>
                </div>
                <div class="field">
                    <label>主题：</label>
                    <div class="see_title"></div>
                </div>
                <div class="field">
                    <label>描述：</label>
                    <div class="see_des"></div>
                </div>
                <div class="field">
                    <label>主持人：</label>
                    <div class="see_compere"></div>
                </div>
                <div class="field">
                    <label>原始版本的附件</label>
                    <div class="see_fileList"></div>
                </div>

                <div class="field">
                    <label>参与人</label>
                    <div class="roleList"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--附件归档--%>
    <div class="bonceContainer bounce-blue " id="fileFile" style="width: 650px">
        <div class="bonceHead">
            <span>附件归档</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>请确定附件的用途，并选择一个附件。</div>
            <div class="ty-radioBox">
                <div class="ty-radio">
                    <input type="radio" id="fileForNew" name="fileFor" value="1">
                    <label for="fileForNew"></label>
                    <b>附件作为新文件发布</b>
                </div>
                <div class="ty-radio">
                    <input type="radio" id="fileForUpdate" name="fileFor" value="2">
                    <label for="fileForUpdate"></label>
                    <b>附件用作换版</b>
                </div>
            </div>
            <div class="fileChoose_avatar">
                <div class="query_fileChoose_avatar">
                    筛选
                    <div style="margin-left: 8px">
                        <div class="ty-radio">
                            <input type="radio" id="choosePic" name="fileChoose" value="1">
                            <label for="choosePic"></label>
                            在图片中选择
                        </div>
                        <div class="ty-radio">
                            <input type="radio" id="chooseFile" name="fileChoose" value="2">
                            <label for="chooseFile"></label>
                            在非图片中选择
                        </div>
                    </div>
                </div>
                <div class="fileShow_avatar">

                </div>
                <div id="ye_archiveFile"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="fileFileBtn" onclick="sureFileFile()">确定</button>
        </div>
    </div>

    <%--讨论 - 转发文件--%>
    <div class="bonceContainer bounce-blue" id="bubbleForward" style="width: 750px">
        <div class="bonceHead">
            <span>转发文件</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="chox">
                <div class="chox_choose">
                    <div class="chox_tip">要转发至哪个讨论组？请选择</div>
                    <div class="themeList_avatar" data-name="forwardFrom" style="max-height: 490px;"></div>
                </div>
                <div class="chox_show">
                    <div class="chox_tip">已选主题</div>
                    <div class="chox_show_avatar">
                        <div class="themeList_avatar" data-name="forwardTo" style="max-height: 444px;"></div>
                    </div>
                    <div class="chox_input">
                        <input type="text" placeholder="此处可输入留言" class="forwardContent">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureBubbleForward()">确定</button>
        </div>
    </div>
    <%--讨论 - 文件换版记录--%>
    <div class="bonceContainer bounce-blue" id="bubbleChangeVersionRecord" style="width: 800px">
        <div class="bonceHead">
            <span>历史版本</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="fileList">

            </div>
            <div id="ye_bubbleHistoryRecord"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%--讨论 - 导入文件--%>
    <div class="bonceContainer bounce-blue" id="importFile" style="width: 600px">
        <div class="bonceHead">
            <span>导入文件</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="con" style="width: 400px; margin: 32px auto">
                <div class="field">想要导入的文件在哪里？</div>
                <div class="field">在Wonderss系统的“文件与资料”中 <div class="text-right"><span class="link-blue" type="btn" data-name="openResourceCenter">选择</span></div></div>
                <div class="field">在Wonderss系统的“我的公务云盘”中 <div class="text-right"><span class="link-gray">选择</span></div></div>
                <div class="field">在本地电脑 <div class="text-right"><span class="link-blue" type="btn" data-name="uploadNewFile">选择</span></div></div>
                <div class="kj-hr" style="margin: 16px 0"></div>
                <div class="field">除可导入新文件外，还可对讨论组内某文件换版。<div class="text-right"><span class="link-blue" type="btn" data-name="chooseChangeVersionFile">换版</span></div></div>
                <small class="ty-color-blue">注 文件换版后，其历史版本仍随时能查阅！</small>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>讨论区</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div id="picShow" style="display: none;">
                    <img src=""/>
                </div>
                <div class="app_avatar">
                    <div class="themeList" drag="right" style="display: none">
                        <div class="btnBar">
                            <button class="kj-btn green" id="discuss_apply" onclick="discussApply();">发起讨论</button>
                            <button class="kj-btn blue" id="disabledDiscuss" onclick="disabledDiscuss();">已停用讨论组</button>
                            <button class="kj-btn blue" id="searchBackToMainBtn" style="display: none" onclick="backToMain();">退出</button>
                        </div>
                        <div class="searchBar">
                            <div class="searchInput">
                                <i class="fa fa-search" type="btn" data-name="searchTheme"></i>
                                <input type="text" class="ty-search" placeholder="请输入讨论主题所含内容" id="theme_search">
                                <i class="fa fa-times-circle clearInput"></i>
                            </div>
                            <div class="search_choose_list" style="display: none">
                                <div class="search_choose_list_head">搜索结果 <span class="loading" style="display: none"><i class="fa fa-refresh"></i> 加载中</span></div>
                                <div class="search_choose_list_avatar"></div>
                            </div>
                        </div>
                        <ul class="themeList_avatar" id="mainThemeList"></ul>
                    </div>
                    <div class="mainChat" style="display: none">
                        <div class="">
                            <div id="changeVersion" style="display: none">
                                <div class="fileUpload"></div>
                            </div>
                        </div>
                        <div class="chatBar">
                            <div class="chat_title">
                                主题：<span class="ty-color-blue title"></span>
                                <span class="m_btn" type="btn" data-name="seeDetail">查看详情 <i class="fa fa-angle-down"></i></span>
                            </div>
                        </div>
                        <div class="unReadMsg" type="btn" data-name="unReadMsg"><i class="fa fa-angle-double-up"></i> <span class="num"></span></div>
                        <div class="chat_avatar">
                            <div class="text-center"><span class="seeMore" type="btn" data-name="seeMore"><i class="fa fa-angle-double-up"></i> 查看更多消息</span></div>
                            <div class="chat_main"></div>
                        </div>
                        <div class="messageInput" drag="top">
                            <div class="textInput_disabled" style="display: none;">
                                <div class="toolbarbox">
                                    <div class="history" type="btn" data-name="messageHistory"><i class="fa fa-clock-o"></i>消息记录</div>
                                </div>
                                <div class="inputBox text-center">
                                    <div>本讨论组因长期无人留言，已被停用。</div>
                                    <div>被停用的讨论组不再支持留言！</div>
                                </div>
                            </div>
                            <div class="textInput">
                                <div class="editorMain" style="position: relative;">
                                    <div id="eidtor">
                                        <div style="position: relative;">
                                            <div class="replyAt"></div>
                                            <i class="fa fa-times-circle clearInput" style="right: 5px; top: 2px;display: inline-block;"></i>
                                        </div>
                                        <script id="issueOpinionContainer" name="content" type="text/plain"></script>
                                    </div>
                                    <div class="history" type="btn" data-name="messageHistory"><i class="fa fa-clock-o"></i>消息记录</div>
                                </div>
                                <div class="sendBar">
                                    <div style="display: flex; align-items: end">
                                        <div class="left_choose_file">
                                            <div class="imgUpload hd"></div>
                                            <button class="ty-btn ty-btn-blue" type="btn" data-name="importFile" id="importFileBtn">导入文件</button>
                                        </div>
                                        <div class="right_show_file">
                                            <div class="fileShowList"></div>
                                        </div>
                                    </div>
                                    <button class="ty-btn ty-btn-blue ty-right sendBtn" type="btn" data-name="send" id="send">发送</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="history_avatar" style="display: none">
                        <div class="functionBar">
                            <button id="attachmentFiling" class="Features ty-btn ty-btn-blue ty-circle-3" onclick="fileFileBtn()">附件归档</button>
                            <button id="readingSettings" class="Features ty-btn ty-btn-cyan ty-circle-3" onclick="readSettingBtn()">阅览设置</button>
                            <button id="delete" class="Features ty-btn ty-btn-red ty-circle-3" onclick="delDiscussBtn()">删除</button>
                        </div>
                        <div class="historyBar">
                            <ul class="historyTab">
                                <li class="active" to="roleList">参与人 <span class="corner-word"></span></li>
                                <li to="allMessage">全部消息</li>
                                <li to="imageList">图片 <span class="sort sortName"><span class="corner-word"></span> <i class="fa fa-long-arrow-down fa-long-arrow-up"></i></span></li>
                                <li to="fileList">文件 <span class="sort sortName"><span class="corner-word"></span> <i class="fa fa-long-arrow-down fa-long-arrow-up"></i></span></li>
                            </ul>
                        </div>
                        <div class="tabCon" id="roleList">
                            <ul class="tblContainer"></ul>
                            <div class="continueAdd">
                                <span class="ty-btn ty-btn-green" type="btn" data-name="addRole">继续添加</span>
                            </div>
                        </div>
                        <div class="tabCon allMessage" id="allMessage" style="display: none">
                            <div class="ty-alert-small queryAlert">已搜索到 <span class="queryNum">8</span>条消息记录 <span class="queryBack" type="btn" data-name="queryBack">返回</span></div>
                            <ul class="tblContainer"></ul>
                            <div class="footBar">
                                <div class="dateChoose">
                                    <span class="query ty-btn ty-btn-blue" type="btn" data-name="query">查找</span>
                                    <i class="fa fa-calendar"></i>
                                    <input type="text" id="calendar">
                                    <span class="pageBtnGroup">
                                        <i class="fa fa-angle-double-left" data-type="first"></i>
                                        <i class="fa fa-angle-left" data-type="prev"></i>
                                        <i class="fa fa-angle-right" data-type="next"></i>
                                        <i class="fa fa-angle-double-right" data-type="last"></i>
                                    </span>
                                </div>
                                <div class="queryCondition">
                                    范围
                                    <select name="type">
                                        <option value="2">最近一周</option>
                                        <option value="3">最近一个月</option>
                                        <option value="4">最近三个月</option>
                                        <option value="5">最近一年</option>
                                        <option value="1">全部</option>
                                    </select>
                                    参与人
                                    <select name="findUser"></select>
                                    消息类别
                                    <select name="mesType">
                                        <option value="2">@我的</option>
                                        <option value="3">回复我的</option>
                                        <option value="1">全部</option>
                                    </select>
                                    <span class="ty-btn ty-btn-blue" type="btn" data-name="sureQuery">确定</span>
                                </div>
                            </div>
                        </div>
                        <div class="tabCon" id="imageList" style="display: none">
                            <ul class="tblContainer">
                                <div class="imageList"></div>
                            </ul>
                        </div>
                        <div class="tabCon" id="fileList" style="display: none">
                            <ul class="tblContainer">
                                <div class="fileList"></div>
                            </ul>
                        </div>
                    </div>
                    <div class="app_null">
                        <i class="icon icon_discuss"></i>
                        <h4>WTM 即时通讯</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<!-- Include JS file. -->
<script src="../script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/common/ueditor/ueditor.config.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/common/ueditor/ueditor.all.min.js?v=SVN_REVISION" type="text/javascript"></script>

<script src="../script/common/ueditor/lang/zh-cn/zh-cn.js?v=SVN_REVISION" type="text/javascript" charset="utf-8" ></script>
<script src="../script/common/ueditor/addCustomizeButton.js?v=SVN_REVISION" type="text/javascript"></script>
<%--<script src="../script/common/ueditor/addCustomizeCombox.js?v=SVN_REVISION" type="text/javascript"></script>--%>
<%--<script src="../script/common/ueditor/addCustomizeDialog.js?v=SVN_REVISION" type="text/javascript"></script>--%>
<script src="../script/discussion/discussion.js?v=SVN_REVISION"></script>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    var isGeneral = generalType === '1' || generalType === '2'
    var isSuper = generalType === '0'
    if(isGeneral){
        $("#fileUploadBtn").html("上传文件");
        $("#uploadDirect").html("本文件直接发布，无需他人审批");

    }else {
        $("#fileUploadBtn").html("文件发布申请");
        $("#folderUploadBtn").remove()
        $("#uploadDirect").html("本文件直接提交给文管，无需他人审批");
    }
</script>
</body>
</html>
