 <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/role/roleIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<link href="../css/authority/approveSetting.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed2">
    <%--必填项尚未填写提示--%>
        <div class="bonceContainer bounce-blue" id="bounceFixed2_tip">
            <div class="bonceHead">
                <span>提示</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="tipCon" style="width: 360px; margin: auto">还有必填项尚未填写！</div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
            </div>
        </div>
    <div class="bonceContainer bounce-red" id="limitTip">
        <div class="bonceHead">
            <span> ！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="requireTipMsg" class="ty-center">还有必填项尚未填写！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()"  >我知道了</span>
        </div>
    </div>
    <%--放弃本次编辑提示--%>
    <div class="bonceContainer bounce-red" id="cancelTip">
        <div class="bonceHead">
            <span> ！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">确定放弃本次编辑吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()"  >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="cancelSure()"  >确定</span>
        </div>
    </div>
    <%-- Wonderss中的加班功能--%>
    <div class="bonceContainer bounce-blue" id="wonderssOverTimeTips">
        <div class="bonceHead">
            <span>Wonderss中的加班功能</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
                <pre style="border: none;background-color:inherit">
Wonderss系统中关于加班的功能如下：

1 职工加班需提前申请，且需经审批通过。
&nbsp;&nbsp;&nbsp;* 申请者每次最多可提出24小时的加班申请。
&nbsp;&nbsp;&nbsp;* 跨天的加班，申请者需分次提交申请。
&nbsp;&nbsp;&nbsp;* 加班需提前多久申请，系统带有默认值，可修改。
&nbsp;&nbsp;&nbsp;* 系统默认加班申请需经最高领导的审批，可修改。

2 职工未提前申请的加班，总务确认无误后可修改其考勤。
&nbsp;&nbsp;&nbsp;* 系统带有默认为关闭状态的“补报加班”功能，该状态可修改。

3 加班后，职工还需填报实际加班的情况，并需经审批。
&nbsp;&nbsp;&nbsp;* 实际加班情况的审批流程，与加班申请的审批流程相同。
&nbsp;&nbsp;&nbsp;* 实际加班的数据超过规定时间未提交的，将由系统驳回。

4 实际加班情况审批通过后，加班数据即进入考勤模块。
                </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- Wonderss中的请假功能--%>
    <div class="bonceContainer bounce-blue" id="wonderssLeaveTips">
        <div class="bonceHead">
            <span>Wonderss中的请假功能</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
            <pre style="border: none;background-color:inherit">
Wonderss系统中关于请假的功能如下：

1 职工请假需提前申请，且需经审批通过。
  * 请假需提前多久申请，系统带有默认值，可修改。
  * 系统默认请假申请需经最高领导的审批，可修改。

2 职工未提前申请的请假，总务确认无误后可修改其考勤。
  * 系统带有默认为关闭状态的“事后补假”功能，该状态可修改。

3 职工请假后还可提出“提前结束假期”的申请，但需经审批。
  * 提前结束假期申请的审批流程，与请假申请的审批流程相同。

4 请假申请经审批通过后，其数据将进入考勤模块。
            </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- 仓库模式的说明--%>
    <div class="bonceContainer bounce-blue" id="stockModeDes" style="width: 700px">
        <div class="bonceHead">
            <span>仓库模式的说明</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
                <pre style="border: none;background-color:inherit">
1 Wonderss系统为机构提供两种仓库模式：智能仓库与非智能仓库。

2 在仓库中，需对材料的数量进行管理。

3 非智能仓库模式下，还可对材料的供应商与包装进行管理。

4 智能仓库模式下，需对材料的供应商、包装、保质期、库位及批次号进行管理，此外系统还提供先进先出功能。
                </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="overTimeApplyAdvanceTime">
        <div class="bonceHead">
            <span>加班申请的提出时间</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px; margin: auto">
                <div class="ty-alert">
                    职工要加班，需至少提前多久提出申请？
                </div>
                <select name="ruleTime" class="ty-inputSelect " style="width: 100%">
                    <option value="30">30分钟</option>
                    <option value="60">60分钟</option>
                    <option value="90">90分钟</option>
                    <option value="120">120分钟</option>
                </select>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()"  >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="leaveApplyAdvanceTime">
        <div class="bonceHead">
            <span>请假申请的提出时间</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px; margin: auto">
                <div class="ty-alert">
                    职工要请假，需至少提前多久提出申请？
                </div>
                <select name="ruleTime" class="ty-inputSelect " style="width: 100%">
                    <option value="30">30分钟</option>
                    <option value="60">60分钟</option>
                    <option value="90">90分钟</option>
                    <option value="120">120分钟</option>
                </select>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()"  >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="changeFactApplyDurationRule">
        <div class="bonceHead">
            <span>提交实际加班数据的规则</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px; margin: auto">
                <div class="ty-alert">
                    职工加班后，需在几天内提交实际加班的数据？
                </div>
                <div>
                    <select name="applyDuration" class="ty-inputSelect " style="width: 70%">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                    </select> 天内
                </div>
                <div style="margin: 8px 0">
                    <small class="ty-color-blue">注：过期不提交的，系统将记作实际未加班！</small>
                </div>
                <div class="ty-radio">
                    <input type="radio" id="anyTimeApply">
                    <label for="anyTimeApply"></label>职工实际加班的数据拖至何时提交都可以
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()"  >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeRule()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 请求处理 选择审批人--%>
    <div class="bonceContainer bounce-blue" id="chooseApproveP" >
        <div class="bonceHead">
            <span>添加审批人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="approvePList userList">
                <%--<div>
                    <p onclick='toggleFa($(this))'><span class="fa fa-circle-o"></span> <span>直接上级</span></p>
                </div>--%>
                <div id="approveTree"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <p id="tp2" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="selectOK()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
        </div>
    </div>
    <%-- 二级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="secdTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="secdtipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">确认</span>
        </div>
    </div>
    <%-- 修改提示--%>
    <div class="bonceContainer bounce-red " id="overTimeEditTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center">修改某级审批者，则之后的各级审批均需重新设置。</div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3 " onclick="bounce_Fixed.cancel();"  >取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " onclick="chooseTipOk()"  >确定要修改</span>
        </div>
    </div>
    <%--  加班查看 --%>
    <div class="bonceContainer bounce-blue " id="overTime">
        <div class="bonceHead">
            <span>查看加班审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="overTimeSetting">
                <div class="text-right"><a class="ty-color-blue" onclick="wonderssOverTimeTips()">Wonderss中的加班功能</a></div>
                <div class="p_row">加班设置的现状如下，如需要，可修改。	</div>
                <div class="p_row"><div>1 职工要加班，需至少提前 <span class="upperLimit"></span> 分钟提出申请 </div><a class="ty-color-blue" onclick="changeOverTimeApplyAdvanceTime()">修改</a></div>
                <div class="p_row"><div>2 职工端“补报加班”的功能处于 <span class="state_makeAfterFact"></span> 状态 </div><a class="ty-color-blue" onclick="changeRepayOverTimeState()">修改</a></div>
                <div class="p_row"><div>3 职工加班后，需 <span class="state_factDurationRule"></span> 天内提交实际加班的数据 </div><a class="ty-color-blue" onclick="changeFactApplyDurationRule()">修改</a></div>
                <div class="p_row"><div>4 加班的审批 </div><a class="ty-color-blue" onclick="overTimeEdit()">修改</a></div>
                <div id="approvList"></div>
                <small class="applyTip">
                    <div>注：</div>
                    <p>加班跨天时，申请者需分次提交申请。</p>
                    <p>申请者每次最多可提出24小时的加班申请。</p>
                </small>
            </section>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  报销查看 --%>
    <div class="bonceContainer bounce-blue " id="reimburse">
        <div class="bonceHead">
            <span>查看报销审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="applyTip">
                <div>审批记录：</div>
                <p></p>
            </div>
            <div id="flowList31"> </div>

        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red " id="reimburseTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p style="text-align: center">确定放弃本次编辑吗？</p>
            <p></p>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="finCancelAll()"  >确定</span>
        </div>
    </div>
    <%--  请假查看 --%>
    <div class="bonceContainer bounce-blue" id="leave">
        <div class="bonceHead">
            <span>查看请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="overTimeSetting" style="width: 400px;margin: 0 auto;">
                <p class="text-right"><a class="ty-color-blue" onclick="wonderssLeaveTips()">Wonderss中的请假功能</a></p>
                <div class="p_row">请假设置的现状如下，如需要，可修改。	</div>
                <div class="p_row"><div>1 职工要请假，需至少提前 <span class="upperLimit"></span> 分钟提出申请 </div><a class="ty-color-blue" onclick="changeLeaveApplyAdvanceTime()">修改</a></div>
                <div class="p_row"><div>2 职工端“事后补假”的功能处于 <span class="state_makeAfterFact"></span> 状态	</div><a class="ty-color-blue" onclick="changeRepayLeaveState()">修改</a></div>
                <div class="p_row"><div>3 请假的审批	 </div><a class="ty-color-blue" onclick="leaveEdit()">修改</a></div>
                <div id="leaveApprovList"></div>
                <small class="ty-color-blue leaveSeeTip">职工无法提交超过<span class="maxHur"></span>的请假申请。</small>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  请假修改记录查看 --%>
    <div class="bonceContainer bounce-blue" id="leaveRecordSee">
        <div class="bonceHead">
            <span>查看请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear">
                <div id="approvRecord" class="ty-right">
                </div>
            </div>
            <div id="leaveSetList"></div>
            <div class="leaveSeeTip">
                职工无法提交超过<span class="maxHur"></span>的请假申请。
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%-- 请假 设置下一级--%>
    <div class="bonceContainer bounce-red" id="leaveNextLevel">
        <div class="bonceHead">
            <span> ！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="leaveNextStep">
                <p onclick="setNextLevel($(this))"><span><i class="fa fa-circle-o" data-step="1"></i> 设置下一级</span></p>
                <p onclick="setNextLevel($(this))"><span><i class="fa fa-circle-o" data-step="2"></i> 选择完毕，且设置完毕。</span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="leaveNextOk()">确定</span>
        </div>
    </div>
    <%-- 报销 设置下一级--%>
    <div class="bonceContainer bounce-red" id="finNextLevel" data-forItem="">
        <div class="bonceHead">
            <span> ！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="padding-left:50px; ">
            <div id="finNextStep">
                <p onclick="setNextLevel($(this))"><span><i class="fa fa-circle-o" data-step="1"></i> 设置下一级</span></p>
                <p onclick="setNextLevel($(this))"><span><i class="fa fa-circle-o" data-step="2"></i> 选择完毕，且设置完毕。</span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="cancelNext()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="finNextOk()">确定</span>
        </div>
    </div>
    <%--  修改设置 --%>
    <div class="bonceContainer bounce-blue" id="saleApprove_common" style="width: 500px;">
        <div class="bonceHead">
            <span>修改设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div>
                    <p class="saleTip1">对于客户发来订单中的数量与交期，是否需公司各部门评审</p>
                    <p class="saleTip2">对于采购来的材料，入库前是否需要检验</p>
                    <p class="saleTip3">生产出的货物入成品库前，是否需要检验</p>
                </div>
                <div style="margin-bottom: 8px">
                    <div class="changeDot">
                        <div class="ty-radio">
                            <input type="radio" name="applyType" id="need" value="1">
                            <label for="need"></label> <span class="needStr">需要</span>
                        </div>
                    </div>
                    <div class="changeDot">
                        <div class="ty-radio">
                            <input type="radio" name="applyType" id="noNeed" value="0">
                            <label for="noNeed"></label> <span class="noNeedStr">不需要</span>
                        </div>
                    </div>
                </div>
                <div id="startDateSale" >
                    本修改将于 <input type="text" placeholder="请选择时间"/> 生效
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="saveCommoditycharge" onclick="applySubmit()">提交</button>
        </div>
    </div>
    <%-- 采购审批设置的修改--%>
    <div class="bonceContainer bounce-blue" id="purchaseApprovalSettingsChange">
        <div class="bonceHead">
            <span class="bounce-title">审批设置的修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 0 auto">
                <div>
                    <div class="page_needApprove">
                        <div class="item-flex">
                            本页面上可更换审批者，可添加一层或多层审批，为多层审批时，还可删除某层审批！
                        </div>
                        <div class="item-flex">
                            <div class="item-auto text-right">
                                <button class="link-blue" onclick="addOneLevel($(this))">添加一层审批</button>
                            </div>
                        </div>
                        <div class="flows"></div>
                    </div>
                    <div class="page_noNeedApprove">
                        <div class="item">
                            确定要将此项审批修改至无需审批吗？
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title"><span class="extraTip">如确定，</span>请选择本次修改的拟生效时间！</div>
                    <input class="ty-inputText" type="text" name="openDate" id="openDate" placeholder="请选择">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeApprove()">提交</button>
        </div>
    </div>
    <%--  产品基本信息的创建模式-修改设置 --%>
    <div class="bonceContainer bounce-blue" id="ptCreationModeChange" style="width: 800px;">
        <div class="bonceHead">
            <span>修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="ptMode">
                    <p>产品图号与名称的创建分为模式1与模式2两种</p>
                    <div>模式1为手动关联，即商品与产品需各自创建并维护，二者间需逐一关联</div>
                    <div>模式2为自动关联，即商品创建后，产品由系统生成，其基本信息与商品完全相同，修改时为联动修改</div>
                    <hr/>
                    <div class="modeSect modeOption" data-align="1">
                        <p>与专属商品有关产品图号与名称的创建模式</p>
                        <div class="ty-radio">
                            <input type="hidden" value="1">
                            <span class="fa fa-circle-o" id="zs_modlue1" data-val="1"></span> <span>模式1</span>
                        </div>
                        <div class="ty-radio">
                            <input type="hidden" value="2">
                            <span class="fa fa-circle-o" id="zs_modlue2" data-val="2"></span> <span>模式2</span>
                        </div>
                    </div>
                    <hr/>
                    <div class="modeSect modeOption" data-align="2">
                        <p>与通用型商品有关产品图号与名称的创建模式</p>
                        <div class="ty-radio">
                            <span class="fa fa-circle-o" id="cm_modlue1" data-val="1"></span> <span>模式1</span>
                        </div>
                        <div class="ty-radio">
                            <span class="fa fa-circle-o" id="cm_modlue2" data-val="2"></span> <span>模式2</span>
                        </div>
                    </div>
                    <hr/>
                    <div class="modeSect authorAble" data-align="3">
                        <p><span class="authorCare">通用型</span>商品与产品的基本信息完全相同，一处如修改，另一处将跟随变动。该修改由以下哪方进行？</p>
                        <div class="ty-radio">
                            <span class="fa fa-circle-o" id="createAble" data-val="1"></span> <span>由有权限创建商品的职工修改</span>
                        </div>
                        <div class="ty-radio">
                            <span class="fa fa-circle-o" id="handAble" data-val="2"></span> <span>由有产品操作权限的职工修改</span>
                        </div>
                    </div>
                </div>
                <div id="pt_startDate">
                    本修改将于 <input type="text" placeholder="请选择时间"/> 生效
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="ptCreationModeChangeTj" onclick="applySubmit()">提交</button>
        </div>
    </div>
    <%-- 采购审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="purchaseApprovalSettingsSee" >
        <div class="bonceHead">
            <span class="bounce-title">采购审批设置查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">采购需 <span class="level"></span> 级审批</div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">开始执行的时间</div>
                    <div class="item-auto text-right openDate"></div>
                </div>
                <div class="kj-hr"></div>
                <div class="approvalFlows"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- 产品基本信息的创建模式-修改记录查看--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="ptModeSettingsSee" style="width: 700px;">
        <div class="bonceHead">
            <span class="bounce-title">查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <p>产品图号与名称的创建分为模式1与模式2两种</p>
                <div class="scan-row">
                    模式1为手动关联，即商品与产品需各自创建并维护，二者间需逐一关联<br/>
                    模式2为自动关联，即商品创建后，产品由系统生成，其基本信息与商品完全相同，修改时为联动修改
                </div>
                <hr/>
                <div class="scan-row">与专属商品有关产品图号与名称的创建模式：<span class="zs_modeType"></span></div>
                <hr/>
                <div class="scan-row">与通用型商品有关产品图号与名称的创建模式:<span class="cm_modeType"></span></div>
                <hr/>
                <div class="role_modeType">
                    <span></span>商品与产品的基本信息完全相同，一处如修改，另一处将跟随变动。该修改<span></span>进行</div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- 修改仓库的模式--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="stockModeChange" >
        <div class="bonceHead">
            <span class="bounce-title">修改仓库的模式</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">仓库将改为<span class="newMode"></span>的模式！</div>
                    <div class="item-auto text-right">
                        <button class="link-blue" onclick="stockModeDes()">仓库模式的说明</button>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">本修改将于</div>
                    <div class="item-fix openDate"><input class="ty-inputText" type="text" name="openDate" id="stockModeChangeDate"></div>
                    <div class="item-auto text-right">生效</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="applySubmit()">确定</button>
        </div>
    </div>
    <%-- 修改仓库的模式 - 提示--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="stockModeChange_tip" >
        <div class="bonceHead">
            <span class="bounce-title">！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix tips">贵机构改为智能仓库模式，需缴纳年费</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 nextStep">下一步</button>
        </div>
    </div>
    <%-- 成品库修改设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="productLogisticsCheck_set" >
        <div class="bonceHead">
            <span class="bounce-title">修改设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div>
                    <p>商品出库时，仓库确认后是否需要物流人员再次确认？</p>
                </div>
                <div style="margin-bottom: 8px">
                    <div class="ty-radio-group">
                        <div class="changeDot">
                            <div class="ty-radio">
                                <input type="radio" name="applyType" id="noNeed1" value="0">
                                <label for="noNeed1"></label> <span class="noNeedStr">不需要</span>
                            </div>
                        </div>
                        <div class="changeDot">
                            <div class="ty-radio">
                                <input type="radio" name="applyType" id="need1" value="1">
                                <label for="need1"></label> <span class="needStr">需要</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="effectTime">
                    本修改将于 <input type="text" id="effectTime_productLogisticsCheck" placeholder="请选择时间"/> 生效
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="applySubmit()">提交</button>
        </div>
    </div>
    <%-- 成品库修改设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="chengPinKu_set" >
        <div class="bonceHead">
            <span class="bounce-title">修改设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div>
                    <p>销售系统中，是否启用成品库。</p>
                </div>
                <div style="margin-bottom: 8px">
                    <div class="ty-radio-group">
                        <div class="changeDot">
                            <div class="ty-radio">
                                <input type="radio" name="applyType" id="need2" value="1">
                                <label for="need2"></label> <span class="needStr">启用</span>
                            </div>
                        </div>
                        <div class="changeDot">
                            <div class="ty-radio">
                                <input type="radio" name="applyType" id="noNeed2" value="0">
                                <label for="noNeed2"></label> <span class="noNeedStr">不启用</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="effectTime">
                    本修改将于 <input type="text" id="effectTime_chengPinKu" placeholder="请选择时间"/> 生效
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="applySubmit()">提交</button>
        </div>
    </div>
    <%-- 成品库修改设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="inStockChecker_set" >
        <div class="bonceHead">
            <span class="bounce-title">修改设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div>
                    <p>外购物料入库时，包装完好情况的检查者</p>
                </div>
                <div style="margin-bottom: 8px">
                    <div class="ty-radio-group">
                        <div class="changeDot">
                            <div class="ty-radio">
                                <input type="radio" name="applyType" id="kuguan" value="0">
                                <label for="kuguan"></label> <span>库管</span>
                            </div>
                        </div>
                        <div class="changeDot">
                            <div class="ty-radio">
                                <input type="radio" name="applyType" id="jianyanyuan" value="1">
                                <label for="jianyanyuan"></label> <span>检验员</span>
                            </div>
                        </div>
                        <div class="changeDot">
                            <div class="ty-radio">
                                <input type="radio" name="applyType" id="allcheck" value="2">
                                <label for="allcheck"></label> <span class="noNeedStr">库管与检验员都需检查确认</span>
                            </div>
                        </div>
                        <div class="changeDot">
                            <div class="ty-radio">
                                <input type="radio" name="applyType" id="allNoCheck" value="3">
                                <label for="allNoCheck"></label> <span class="noNeedStr">包装完好情况不需检查</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="effectTime">
                    本修改将于 <input type="text" id="effectTime_inStockChecker" placeholder="请选择时间"/> 生效
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="applySubmit()">提交</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="Tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs">    </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">确认</span>
        </div>
    </div>
    <%-- 审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="itemApply" >
        <div class="bonceHead">
            <span>权限变更 - 审批设置修改</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix120">审批级别</div>
                    <div class="item-auto">
                        <select name="level" onchange="changeAuth()">
                            <option value="1">一级审批</option>
                            <option value="2">二级审批</option>
                        </select>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">一级审批人</div>
                    <div class="item-auto">
                        <select name="firstApprover"></select>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">最终审批人</div>
                    <div class="item-auto">
                        <select name="lastApprover"></select>
                    </div>
                </div>
            </div>
<%--            <div>--%>
<%--                <div class="approvalLevel">--%>
<%--                    <span class="ttl">审批级别</span>--%>
<%--                    <span class="con" id="authLevelCon">--%>
<%--                        <select class="conInput level" id="authLevel"  >--%>
<%--                            <option value="1">一级审批</option>--%>
<%--                            <option value="2">二级审批</option>--%>
<%--                        </select>--%>
<%--                    </span>--%>
<%--                </div>--%>
<%--                <div class="approval" id="authLevelContent">--%>

<%--                </div>--%>
<%--            </div>--%>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="saveAuthcharge()">保存</span>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 付款设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="paymentSet" >
        <div class="bonceHead">
            <span id="paymentTtl">付款设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <%--<div class="">
                <p class="leTip"> 报销或借款等与资金支出有关申请审批过后、<br>出纳实际付款前需再经付款审批。</p>
                <p>您可更换付款审批的审批人。</p>
                <p>付款审批的审批人：
                    <span id="scanU">董事长139XXXXXXXX</span>
                    <input type="hidden" id="paymentID">
                    <span class="ty-right applyTip payEditBtn" id="payEditBtn"  onclick="payEditBtn()">修改</span>
                </p>
                <div id="startDate2" >
                    本修改将对 <input type="text"/>之后提交的付款申请生效
                </div>
            </div>--%>
            <div>
                <p style="line-height: 30px;">
                    付款审批的设置
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" onclick="updatePaySet()">修改付款设置</span>
                </p>
                <div style="padding-left:30px; ">
                    <div class="tiph">
                        <span>内容：</span>
                        <div>与资金支出有关的申请（如报销申请、借款申请）审批通过后、出纳实际付款前的关于是否付款专门的审批</div>
                    </div>
                    <div class="tiph">
                        <span>功能：</span>
                        <div>系统默认需由最高管理者审批。可更换为其他人或“无需审批”</div>
                    </div>
                    <div>
                        当前付款审批的设置：<span id="cur1"></span>
                    </div>
                </div>
                <hr style=" margin-top: 10px; border-top: 1px solid #ccc; margin-bottom: 8px;">
                <p>
                    付款复核的设置
                </p>
                <div style="padding-left:30px; ">
                    <div class="tiph">
                        <span>内容：</span>
                        <div>出纳选择为现金外的对外支付方式时，是否需要财务负责人复核</div>
                    </div>
                    <div class="tiph">
                        <span>功能：</span>
                        <div>系统默认需由财务负责人复核。可改为“无需审批”</div>
                    </div>
                    <div>
                        当前付款复核的设置：<span id="cur2"></span>
                    </div>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <%--<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="savePaycharge"  onclick="applySubmit()">提交修改申请</span>--%>
            <span class="ty-btn ty-btn-big ty-circle-3" id="savePaychargeNo" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 采购审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="purchaseApprovalSettings" >
        <div class="bonceHead">
            <span class="bounce-title">采购审批设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">目前，本公司采购需 <span class="level"></span> 级审批</div>
                    <div class="item-auto text-right">
                        <button class="link-blue btn_changeNoApprove" onclick="changeApprove(0)">修改至无需审批</button>
                        <button class="link-blue" onclick="changeApprove(1)">其他修改</button>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">开始执行的时间</div>
                    <div class="item-auto text-right openDate"></div>
                </div>
                <div class="kj-hr"></div>
                <div class="approvalFlows"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- 修改付款设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="paymentSetUpdate" >
        <div class="bonceHead">
            <span>修改付款设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>
                    付款审批的审批者既可更换为其他职工，也可选择为“无需审批”
                </p>
                <div style="padding-left: 50px; ">
                    <span>付款审批者：</span>
                    <div class="selectUcon">
                        <input type="text" id="userInput">
                        <div id="selectU" class="selectU"></div>
                    </div>
                    <%--<select class="form-item" id="selectU" onchange="showtime()"></select>--%>
                </div>
                <hr style="margin-top:10px; margin-bottom:10px; border-top:1px solid #ccc; ">
                <p>
                    出纳选择为现金外的对外支付方式时，是否需要财务负责人复核？
                </p>
                <div style="padding: 0 44px;" class="sp">
                    <p><i data-val="1" class="fa fa-circle-o"></i> 需要</p>
                    <p><i data-val="0" class="fa fa-circle-o"></i> 不需要</p>
                </div>
                <div class="times">
                    本修改将对 <input id="timesInput" class="form-item" /> 之后提交的付款申请生效
                </div>
            </div>
        </div>
        <div class="bonceFoot submitcon">
            <span class="ty-btn ty-cancel ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="submitcon()">提交</span>
        </div>
    </div>

    <%--  加班修改--%>
    <div class="bonceContainer bounce-blue " id="overTimeEdit">
        <div class="bonceHead">
            <span>修改加班审批的审批设置</span>
            <a class="bounce_close" onclick=" endTimer();bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="applyTip">
                <div>说明：</div>
                <p>1  设置多级审批时，可选择“直接上级”作为审批者，但一旦选择特定职工作为审批者，则之后的各级审批者将无法再选择“直接上级”。</p>
                <p>2  审批者最高可审批不超过24小时的加班。</p>
                <input type="hidden" id="flowData">
            </div>
            <div id="flowList">
                    <div class="apItem">
                        <p>
                            <span class="levelName">审批者</span>
                            <span class="chargeName">董事长</span>
                            <div class="clr"></div>
                        </p>
                        <p>
                            该审批者有权批准不高于<select class="chargeHours"><option value="24">24</option></select>小时的加班
                        </p>
                    </div>
            </div>
            <div id="nextStep">
                <p onclick="toggleFa2($(this), 1)"><span><i class="fa fa-circle-o"></i> 设置下一级</span></p>
                <p onclick="toggleFa2($(this), 2)"><span><i class="fa fa-circle-o"></i> 不设置下一级，已设置完毕。</span></p>
            </div>
            <div id="startDate" >
                本修改将对 <input type="text" readonly>之后提交的加班申请生效
            </div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <div class="btns">
                <%--<span class="ty-btn bounce-cancel ty-btn-big ty-circle-3 " id="editCancel" onclick="bounce.cancel()"  >取消</span>--%>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="editOverTimeOk" onclick="editOverTimeOk()"  >确定</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="applySubmit" onclick="applySubmit()"  >提交修改申请</span>
            </div>

        </div>
    </div>
    <%--  请假修改--%>
    <div class="bonceContainer bounce-blue " id="leaveEdit">
        <div class="bonceHead">
            <span>修改请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>新设置的请假审批流程如下：</div>
            <div id="leaveFlowList" data-hours="0">
            </div>
            <p class="leaveFlowTip">职工无法提交超过<span id="leaveFlowDur"></span>的请假申请。</p>
            <div>
                <div id="addApprover">
                    <div class="itemLe">
                        <span class="levelName">审批者</span>
                        <span class="chargeName itemSp" onclick="chooseTip($(this), 'leave')"></span>
                        <div class="clear"></div>
                    </div>
                    <div class="applyTip">
                        <div>说明：</div>
                        <p>设置多级审批时，可选择“直接上级”作为审批者，但一旦选择特定职工作为审批者，则之后的各级审批者将无法再选择“直接上级”。</p>
                    </div>
                    <p class="itemLe">该审批者有权批准</p>
                    <div class="limitReq">
                        <p class="leaveHour">
                            <span class="higher">当日不高于</span>
                            <select id="leaveHour" data-type="1" onchange="leaveTimeSt($(this))" require>
                                <option value=""></option>
                                <option value="0.5">0.5</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="4">4</option>
                                <option value="8">8</option>
                                <option value="12">12</option>
                            </select>小时的请假
                        </p>
                        <p>
                            <span class="higher">不高于</span><input id="leaveDays" type="text" data-type="2" onkeyup="leaveTimeSt($(this))" require/>天的请假
                        </p>
                        <div class="wishDay">
                            <span class="fa fa-circle-o" data-type="3" onclick="leaveTimeSt($(this))" require></span>
                            <span class="wish">任何天数的请假</span>
                        </div>
                    </div>
                </div>
                <div>
                    <p>本修改对哪天及之后提交的请假申请生效？</p>
                    <div class="itemLe">
                        <span>请确定本修改的生效日期</span>
                        <input id="leaveStartDate" />
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" id="leaveNext" onclick="nextStep()">下一步</span>
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" id="leaveApplyCancel" onclick="bounce_Fixed2.show($('#cancelTip'))">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="leaveApplySubmit" onclick="applySubmit()">提交修改申请</span>
        </div>
    </div>
    <%-- 报销修改 --%>
    <div class="bonceContainer bounce-blue " id="finananceEdit">
        <div class="bonceHead">
            <span>查看报销审批的审批设置</span>
            <a class="bounce_close" onclick="endTimer();bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>目前本公司报销的审批流程如下：
                    <span class="ty-right applyTip overTimeEdit" onclick="finananceEditBtn()">修改</span>
                </p>
                <div id="flowList3"> </div>
                <div class="applyTip" style="margin-left:65px;"> </div>
            </div>

            <div id="editFlows">
                <div>
                    <p class="">新设置的报销审批流程如下：</p>
                    <div id="flowList4"> </div>
                    <p class="apItem editShow">
                        <span class="levelName">审批者</span><span data-mobile="" data-id="" id="finPerson" class="chargeName" onclick="chooseTip($(this) , 'finace')"> -- 请选择 -- </span>
                    </p>
                    <div class="applyTip editShow">
                        <div>说明：</div>
                        <p>设置多级审批时，可选择“直接上级”作为审批者，但一旦选择特定职工作为审批者，则之后的各级审批者将无法再选择“直接上级”。</p>
                    </div>
                    <p class="apItem editShow" style="margin-bottom:5px; "> 该审批者有权批准 </p>
                    <p class="apItem editShow">不高于 <input type="text" id="amountInput" onkeyup="clearNoNum(this)"> 元的报销</p>
                    <p class="apItem editShow" style="margin-top:5px;" data-anyAmount="0" id="anyAmount" onclick="toggleThis($(this))"><i class="fa fa-circle-o"></i>  任何金额的报销</p>
                </div>
                <div id="startDate3" >
                    <div class="applyTip" id="endFinTip"> 。</div>
                    <p style="margin-top:30px; ">本修改对哪天及之后提交的报销申请生效？</p>
                    请确定本修改的生效日期 <input type="text" id="finStartDate" readonly>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <div class="">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="nextFin" onclick="nextFin()"  >下一步</span>
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3 " id="cacelFin" onclick="cacelFin()"  >取消</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="closeFin2" onclick="bounce.cancel()"  >关闭</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="applyFin" onclick="applySubmit()"  >提交修改申请</span>
            </div>

        </div>
    </div>
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue " id="requestLog">
        <div class="bonceHead">
            <span id="logType">修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="summary"> </p>
            <table class="kj-table" id="log">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td id="log2">状态</td>
                    <td>开始执行日期</td>
                    <td id="log1">操作</td>
                    <td id="log13">付款复核者</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()"  >关闭</span>
        </div>
    </div>
    <%-- 仓库修改记录 --%>
    <div class="bonceContainer bounce-blue" id="stockModeLog" style="width: 700px">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="text-right"><span class="link-blue" onclick="stockModeDes()">仓库模式的说明</span></p>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td>设置的结果</td>
                    <td>开始执行日期</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()"  >关闭</span>
        </div>
    </div>
    <%-- 外购物料入库时，包装完好情况检查者 修改记录 --%>
    <div class="bonceContainer bounce-blue" id="inStockCheckerSetLog" style="width: 700px">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="state0">
                <p>外购物料入库时，包装完好情况检查者尚未经修改。</p>
                <p class="create">创建人  XXX XXXX-XX-XX XX:XX:XX</p>
            </div>
            <div class="state1">
                <p>当前，外购物料入库时，包装完好情况检查者为第 <span class="num"></span> 次修改后的结果</p>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>资料状态</td>
                        <td>执行的状态</td>
                        <td>开始执行日期</td>
                        <td>修改后的检查者</td>
                        <td>创建人/修改人</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  来自客户订单的评审 --%>
    <div class="bonceContainer bounce-blue" id="sale_common" style="width: 520px">
        <div class="bonceHead">
            <span>来自客户订单的评审</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 420px; margin: 0 auto">
                <div class="text-right">
                    <button class="ty-btn ty-btn-blue ty-btn-big" data-fun="saleApprove">修改设置</button>
                </div>
                <div style="margin-top: 8px">
                    <p>内容：<span id="msgLog">对于客户发来订单中的数量与交期，是否需公司各部门评审</span></p>
                    <p>功能：<span id="funLog">系统默认需要评审。可修改为“无需评审”</span></p>
                    <p>当前的设置：<span id="currentSettings"></span></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big fatSize" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--  产品基本信息的创建模式 --%>
    <div class="bonceContainer bounce-blue" id="productCreationMode" style="width: 720px">
        <div class="bonceHead">
            <span>产品基本信息的创建模式</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 600px; margin: 0 auto">
                <div class="text-right">
                    <button class="ty-btn ty-btn-blue ty-btn-big" data-fun="productSetting">修 改</button>
                </div>
                <div class="yard_item">
                    <p>内容</p>
                    <div>1 系统中，产品基本信息包含图号、名称、规格、型号、计量单位等内容</div>
                    <div>2 某些机构产品的图号与名称，内部采用一套体系，销售给客户时采用另一套体系，有些机构则内外部完全相同</div>
                    <div>3 本系统提供的模式1与模式2，分别适用于上述两种情况</div>
                    <div>模式1为手动关联模式，即商品与产品需各自创建并维护，二者间需逐一关联</div>
                    <div>模式2为自动关联模式，即商品创建后，产品由系统生成，其基本信息与商品完全相同，修改时为联动修改</div>
                </div>
                <div class="yard_item">
                    <p>功能</p>
                    <div>1、系统提供默认的设置，机构可根据自身情况给予修改</div>
                    <div>2、默认的设置为：客户专属的商品与产品间为模式1，通用型商品与产品间为模式2</div>
                </div>
                <div class="yard_item">
                    <p>当前产品图号与名称的创建模式</p>
                    <div>与专属商品相关的产品为模式<span id="zsModel"></span>，与通用型商品相关的产品为模式<span id="tyModel"></span></div>
                    <div>产品信息<span id="reviseModel"></span></div>
                    <div class="currentMode hd"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big fatSize" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%-- 仓库的模式--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="stockMode"  style="width: 600px">
        <div class="bonceHead">
            <span class="bounce-title">仓库的模式</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">当前的仓库模式：<span class="nowModeName"></span></div>
                    <div class="item-auto text-right">
                        <button class="link-blue changeBtn">修改</button>
                        <button class="link-blue" onclick="stockModeDes()">仓库模式的说明</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%--  成品出库时物流人员的复核 --%>
    <div class="bonceContainer bounce-blue" id="productLogisticsCheck" style="width: 520px">
        <div class="bonceHead">
            <span>成品出库时物流人员的复核</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 420px; margin: 0 auto">
                <div class="text-right">
                    <button class="ty-btn ty-btn-blue ty-btn-big" onclick="productLogisticsCheck_set()">修改设置</button>
                </div>
                <div style="margin-top: 8px">
                    <p>内容：<span>商品出库时，仓库确认后是否需要物流人员再次确认。</span></p>
                    <p>功能：<span>系统默认无需物流人员的再次确认，可修改。</span></p>
                    <p>当前的设置：<span class="currentSettings"></span></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big fatSize" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--  成品库 --%>
    <div class="bonceContainer bounce-blue" id="chengPinKuSet" style="width: 520px">
        <div class="bonceHead">
            <span>成品库</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 420px; margin: 0 auto">
                <div class="text-right">
                    <button class="ty-btn ty-btn-blue ty-btn-big" onclick="chengPinKu_set()">修改设置</button>
                </div>
                <div style="margin-top: 8px">
                    <p>内容：<span>销售系统中，是否启用成品库。</span></p>
                    <p>功能：<span>系统默认成品库已启用，可修改为未启用。</span></p>
                    <p>当前的设置：<span class="currentSettings"></span></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big fatSize" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--  外购物料入库时，包装完好情况的检查 --%>
    <div class="bonceContainer bounce-blue" id="inStockCheckerSet" style="width: 520px">
        <div class="bonceHead">
            <span>外购物料入库时，包装完好情况的检查</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 420px; margin: 0 auto">
                <div class="text-right">
                    <button class="ty-btn ty-btn-blue ty-btn-big" onclick="inStockChecker_set()">修改设置</button>
                </div>
                <div style="margin-top: 8px">
                    <p>内容：<span>销售系统中，外购物料入库时，包装完好情况的检查者。 <br> <small class="ty-color-blue">注：设置后，检察者需在系统内填写检查记录，所以设置时请考虑检查者的工作量！</small></span></p>
                    <p>功能：<span>系统默认由库管负责此项工作。可修改。</span></p>
                    <p>当前的设置：<span class="currentSettings"></span></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big fatSize" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>审批设置</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container" id="infoCon">
                <span class="hd" id="navControl"></span>

                <div class="ty-mainData">
                    <%--审批权限--%>
                    <div class="tblContainer" style="width: 1200px">
                        <table class="kj-table kj-table-striped">
                            <thead>
                            <tr>
                                <td rowspan="2" style="width: 300px"> 审批事项 </td>
                                <td rowspan="2" style="width: 150px"> 当前状态 </td>
                                <td colspan="3"> 当前审批者 </td>
                                <td rowspan="2"> 操作 </td>
                            </tr>
                            <tr>
                                <td>一级审批者</td>
                                <td>二级审批者</td>
                                <td>最终审批者</td>
                            </tr>
                            </thead>
                            <tbody id="chargeList">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/authority/approveSettings.js?v=SVN_REVISION"></script>

</body>
</html>