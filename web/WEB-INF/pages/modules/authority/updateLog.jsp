 <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    .red{color:red;}
    #look p{margin-bottom:0;}
    #look .bonceCon>div{margin:0 auto;width:60%;}
    .changTo{margin: 15px 0;}
</style>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-green" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="tip_ms" style="text-align: center; "> </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-green" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- creater 姚宗涛   2018/3/14   查看弹框--%>
    <div class="bonceContainer bounce-green" id="look">
        <div class="bonceHead">
            <span>修改申请</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p><span id="description"></span> 时，由当前需要</p>
                <div id="old" class=""> </div>
                <p class="red changTo">更改为：</p>
                <div class="red" id="new"> </div>

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-green" onclick="charge(1)">批准</span>
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-red" onclick="charge(0)">驳回</span>
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>修改记录</span>
                <i class="close_x"></i>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <ul class="ty-secondTab">
                    <li class="ty-active">待处理</li>
                    <li>已批准</li>
                    <li>已驳回</li>
                </ul>
                <div class="ty-mainData">
                    <%-- 待处理 --%>
                    <table class="ty-table ty-table-control">
                        <thead>
                            <tr>
                                <td>申请时间</td>
                                <td>申请人</td>
                                <td>事项</td>
                                <td>操作</td>
                            </tr>
                        </thead>
                        <tbody id="tbl1"> </tbody>
                    </table>
                    <%-- 已批准 、已驳回--%>
                    <table class="ty-table ty-table-control hd">
                        <thead>
                            <tr>
                                <td>申请时间</td>
                                <td>申请人</td>
                                <td>事项</td>
                                <td>审批人</td>
                                <td>审批时间</td>
                                <td>操作</td>
                            </tr>
                        </thead>
                        <tbody id="tbl2"></tbody>
                    </table>
                    <div id="ye"></div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/authority/updateLog.js?v=SVN_REVISION"></script>

</body>
</html>
