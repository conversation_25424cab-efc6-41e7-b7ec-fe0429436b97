<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    .search>input{height:30px;margin-left:15px;}
    #details{ width: 800px; }
    #details .fa-check{ color: #5d9cec;  }
    #details .fa-times{ color: #ed5565;  }
    #biao{ position: relative; padding: 0; }
    .biaotou{  border-top: 35px rgba(0,0,0,0) solid; border-left: 20px rgba(200,200,200,0.5) solid;
        width: 100%;  position: absolute ; top:0 ; ;left:0;  }
    .tt1, .tt2{ position: absolute;  }
    .tt1{ top:5px; right:5px; }
    .tt2{ bottom:5px; left: 5px;    }
    #authList_1_1 tr:nth-child(1) td{ height: 80px; }
    #auth_1_1 , #auth_1_2{ float: left;  }
    #auth_1_1{ width:10%;   }
    #auth_1_2{ min-width:800px; overflow-x: auto; width:82%; border-right:1px solid #ddd;  }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确认</span>
        </div>
    </div>
    <%-- 当前权限查看  查看按钮 弹框 --%>
    <div class="bonceContainer bounce-blue" id="details">
        <div class="bonceHead">
            <span> <span id="detailsName"></span> — <span id="detailsModel">管理权限</span> </span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin:0 auto;">
                <table class="ty-table">
                    <tr id="ttl_popm">  </tr>
                    <tr id="check_popm">  </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big ty-circle-5 ty-btn-blue" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>职工权限</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">

            <div class="ty-container">
                <ul class="ty-secondTab">
                    <li class="ty-active">当前权限查看</li>
                    <li>审批设置查看</li>
                </ul>
                <div class="ty-right search" style="margin-top:-36px;">
                    <input type="text" value="" id="departName" placeholder="请输入部门"><span class="ty-btn ty-btn-big ty-btn-blue  ty-circle-3" id="Sizer">筛选</span>
                    <input type="text" value="" id="userName" placeholder="请输入姓名"><span class="ty-btn ty-btn-big ty-btn-blue  ty-circle-3" id="search">搜索</span>
                </div>
                <div>
                    <div class="ty-mainData">
                        <table class="ty-table" style="width:200px" id="auth_1_1">
                            <tbody id="authList_1_1">
                            <tr>
                                <td>部门</td>
                                <td>姓名</td>
                            </tr>

                            </tbody>
                        </table>
                        <div class="scrollTable"  id="auth_1_2">
                            <table class="ty-table ty-table-control" id="auth_1_21" style="width: 2000px;">
                                <tbody id="authList_1_2">
                                <tr>
                                    <td colspan="11" id="colspan">项目</td>
                                </tr>
                                <tr id="modelTTl_2">
                                    <td>权限管理</td>
                                    <td>总务管理</td>
                                    <td>财务管理</td>
                                    <td>销售管理</td>
                                    <td>生产管理</td>
                                    <td>商品管理</td>
                                    <td>物料管理</td>
                                    <td>项目管理</td>
                                    <td>文件与资料</td>
                                    <td>个人中心</td>
                                    <td>关于</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="clr"></div>
                    </div>
                    <div class="ty-mainData hd">
                        <table class="ty-table ty-table-control">
                            <tbody id="authList_2">
                            <tr>
                                <td>部门</td>
                                <td id="biao">
                                    <div class="biaotou"></div>
                                    <span class="tt1">项目</span>
                                    <span class="tt2">姓名</span>
                                </td>
                                <td>加班申请</td>
                                <td>请假申请</td>
                                <td>报销申请</td>
                                <td>职工档案修改</td>
                                <td>岗位设置修改</td>
                                <td>审批设置修改</td>
                                <td>新项目立项</td>
                                <td>新项目开发</td>
                            </tr>
                            <tr>
                                <td>技术部</td>
                                <td>贾宝玉</td>
                                <td>一级审批者</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>技术部</td>
                                <td>宣宝才</td>
                                <td>一级审批者</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>技术部</td>
                                <td>是冒菜</td>
                                <td>一级审批者</td>
                                <td></td>
                                <td></td>
                                <td>二级审批者</td>
                                <td></td>
                                <td></td>
                                <td>最终审批者</td>
                                <td></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/authority/workerAuthority.js?v=SVN_REVISION"></script>

</body>
</html>
