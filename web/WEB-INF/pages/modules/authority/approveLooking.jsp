<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/role/roleIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<style>
    .ttl{ width:30%!important; }
    .con{ width:50%!important; }

    .tiph{ position: relative; margin-top:6px;  }
    .tiph>span{ position: absolute; }
    .tiph>div{ margin-left: 40px; }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed2">
    <%-- Wonderss中的加班功能--%>
    <div class="bonceContainer bounce-blue" id="wonderssOverTimeTips">
        <div class="bonceHead">
            <span>Wonderss中的加班功能</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
                <pre style="border: none;background-color:inherit">
Wonderss系统中关于加班的功能如下：

1 职工加班需提前申请，且需经审批通过。
&nbsp;&nbsp;&nbsp;* 申请者每次最多可提出24小时的加班申请。
&nbsp;&nbsp;&nbsp;* 跨天的加班，申请者需分次提交申请。
&nbsp;&nbsp;&nbsp;* 加班需提前多久申请，系统带有默认值，可修改。
&nbsp;&nbsp;&nbsp;* 系统默认加班申请需经最高领导的审批，可修改。"

2 职工未提前申请的加班，总务确认无误后可修改其考勤。
&nbsp;&nbsp;&nbsp;* 系统带有默认为关闭状态的“补报加班”功能，该状态可修改。

3 加班后，职工还需填报实际加班的情况，并需经审批。
&nbsp;&nbsp;&nbsp;* 实际加班情况的审批流程，与加班申请的审批流程相同。
&nbsp;&nbsp;&nbsp;* 实际加班的数据超过规定时间未提交的，将由系统驳回。

4 实际加班情况审批通过后，加班数据即进入考勤模块。
                </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- Wonderss中的请假功能--%>
    <div class="bonceContainer bounce-blue" id="wonderssLeaveTips">
        <div class="bonceHead">
            <span>Wonderss中的请假功能</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
            <pre style="border: none;background-color:inherit">
Wonderss系统中关于请假的功能如下：

1 职工请假需提前申请，且需经审批通过。
  * 请假需提前多久申请，系统带有默认值，可修改。
  * 系统默认请假申请需经最高领导的审批，可修改。"

2 职工未提前申请的请假，总务确认无误后可修改其考勤。
  * 系统带有默认为关闭状态的“事后补假”功能，该状态可修改。

3 职工请假后还可提出“提前结束假期”的申请，但需经审批。
  * 提前结束假期申请的审批流程，与请假申请的审批流程相同。

4 请假申请经审批通过后，其数据将进入考勤模块。
            </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 审批权限 - 请求处理选择审批人--%>
    <div class="bonceContainer bounce-blue" id="chooseApproveP" style="margin: 240px 0 0 45%;">
        <div class="bonceHead">
            <span>添加审批人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="approvePList">
                <div class="approvePItem" >
                    <input type="radio" value="0" name="approveP" >直接上级
                </div>
                <ul id="approveTree"></ul>
            </div>
        </div>
        <div class="bonceFoot">
            <p id="tp2" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="selectOK()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
        </div>
    </div>
    <%-- 二级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="secdTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="secdtipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">确认</span>
        </div>
    </div>
    <%--  加班 --%>
    <div class="bonceContainer bounce-blue" id="overTime">
        <div class="bonceHead">
            <span>查看加班审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="overTimeSetting">
                <div class="text-right"><a class="ty-color-blue" onclick="wonderssOverTimeTips()">Wonderss中的加班功能</a></div>
                <div class="p_row">加班设置的现状如下，如需要，可修改。	</div>
                <div class="p_row"><div>1 职工要加班，需至少提前 <span class="upperLimit"></span> 分钟提出申请 </div></div>
                <div class="p_row"><div>2 职工端“补报加班”的功能处于 <span class="state_makeAfterFact"></span> 状态 </div></div>
                <div class="p_row"><div>3 职工加班后，需 <span class="state_factDurationRule"></span> 天内提交实际加班的数据 </div></div>
                <div class="p_row"><div>4 加班的审批 </div></div>
                <div id="approvList"></div>
                <small class="applyTip">
                    <div>注：</div>
                    <p>加班跨天时，申请者需分次提交申请。</p>
                    <p>申请者每次最多可提出24小时的加班申请。</p>
                </small>
            </section>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  报销查看 --%>
    <div class="bonceContainer bounce-blue " id="reimburse">
        <div class="bonceHead">
            <span>查看报销审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="applyTip">
                <div>审批记录：</div>
                <p></p>
            </div>
            <div id="flowList31"> </div>

        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  请假修改记录查看 --%>
    <div class="bonceContainer bounce-blue" id="leaveRecordSee">
        <div class="bonceHead">
            <span>查看请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear">
                <div id="approvRecord" class="ty-right">
                </div>
            </div>
            <div id="leaveSetList"></div>
            <div class="leaveSeeTip">
                职工无法提交超过<span class="maxHur"></span>的请假申请。
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  查看请假审批的审批设置  --%>
    <div class="bonceContainer bounce-blue " id="leave">
        <div class="bonceHead">
            <span>查看请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="overTimeSetting" style="width: 400px;margin: 0 auto;">
                <p class="text-right"><a class="ty-color-blue" onclick="wonderssLeaveTips()">Wonderss中的请假功能</a></p>
                <div class="p_row">请假设置的现状如下，如需要，可修改。	</div>
                <div class="p_row"><div>1 职工要请假，需至少提前 <span class="upperLimit"></span> 分钟提出申请 </div></div>
                <div class="p_row"><div>2 职工端“事后补假”的功能处于 <span class="state_makeAfterFact"></span> 状态	</div></div>
                <div class="p_row"><div>3 请假的审批	 </div></div>
                <div id="leaveApprovList"></div>
                <small class="ty-color-blue leaveSeeTip">职工无法提交超过<span class="maxHur"></span>的请假申请。</small>
            </section>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 采购审批设置的修改--%>
    <div class="bonceContainer bounce-blue" id="purchaseApprovalSettingsChange">
        <div class="bonceHead">
            <span class="bounce-title">审批设置的修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 0 auto">
                <div>
                    <div class="page_needApprove">
                        <div class="item-flex">
                            本页面上可更换审批者，可添加一层或多层审批，为多层审批时，还可删除某层审批！
                        </div>
                        <div class="item-flex">
                            <div class="item-auto text-right">
                                <button class="link-blue" onclick="addOneLevel($(this))">添加一层审批</button>
                            </div>
                        </div>
                        <div class="flows"></div>
                    </div>
                    <div class="page_noNeedApprove">
                        <div class="item">
                            确定要将此项审批修改至无需审批吗？
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title"><span class="extraTip">如确定，</span>请选择本次修改的拟生效时间！</div>
                    <input class="ty-inputText" type="text" name="openDate" id="openDate" placeholder="请选择">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeApprove()">提交</button>
        </div>
    </div>
    <%-- 采购审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="purchaseApprovalSettingsSee" >
        <div class="bonceHead">
            <span class="bounce-title">采购审批设置查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">采购需 <span class="level"></span> 级审批</div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">开始执行的时间</div>
                    <div class="item-auto text-right openDate"></div>
                </div>
                <div class="kj-hr"></div>
                <div class="approvalFlows"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="Tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs">    </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">确认</span>
        </div>
    </div>
    <%--  财务权限审批--%>
    <div class="bonceContainer bounce-blue" id="myModal_finance" style="width:540px;">
        <div class="bonceHead">
            <span>报销申请 - 详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>
                <div class="trItem">
                    <span class="ttl">审批级别</span> <span class="con" id="financeLevel"> </span>
                </div>
                <div id="financeFlow"> </div>
            </div>

        </div>
        <div class="bonceFoot">
            <p id="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 审批设置修改 --%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="itemApply" >
        <div class="bonceHead">
            <span>审批设置修改 - 详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix120">审批级别</div>
                    <div class="item-auto">
                        <select name="level" onchange="changeAuth()" disabled>
                            <option value="1">一级审批</option>
                            <option value="2">二级审批</option>
                        </select>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">一级审批人</div>
                    <div class="item-auto">
                        <select name="firstApprover" disabled></select>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">最终审批人</div>
                    <div class="item-auto">
                        <select name="lastApprover" disabled></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

    <%-- 付款设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="paymentSet" >
        <div class="bonceHead">
            <span id="paymentTtl">付款设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <%--<div class="">
                <p class="leTip"> 报销或借款等与资金支出有关申请审批过后、<br>出纳实际付款前需再经付款审批。</p>
                <p>您可更换付款审批的审批人。</p>
                <p>付款审批的审批人：
                    <span id="scanU">董事长139XXXXXXXX</span>
                    <input type="hidden" id="paymentID">
                    <span class="ty-right applyTip payEditBtn" id="payEditBtn"  onclick="payEditBtn()">修改</span>
                </p>
                <div id="startDate2" >
                    本修改将对 <input type="text"/>之后提交的付款申请生效
                </div>
            </div>--%>
            <div>
                <p style="line-height: 30px;">
                    付款审批的设置
<%--                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" onclick="updatePaySet()">修改付款设置</span>--%>
                </p>
                <div style="padding-left:30px; ">
                    <div class="tiph">
                        <span>内容：</span>
                        <div>与资金支出有关的申请（如报销申请、借款申请）审批通过后、出纳实际付款前的关于是否付款专门的审批</div>
                    </div>
                    <div class="tiph">
                        <span>功能：</span>
                        <div>系统默认需由最高管理者审批。可更换为其他人或“无需审批”</div>
                    </div>
                    <div>
                        当前付款审批的设置：<span id="cur1"></span>
                    </div>
                </div>
                <hr style=" margin-top: 10px; border-top: 1px solid #ccc; margin-bottom: 8px;">
                <p>
                    付款复核的设置
                </p>
                <div style="padding-left:30px; ">
                    <div class="tiph">
                        <span>内容：</span>
                        <div>出纳选择为现金外的对外支付方式时，是否需要财务负责人复核</div>
                    </div>
                    <div class="tiph">
                        <span>功能：</span>
                        <div>系统默认需由财务负责人复核。可改为“无需审批”</div>
                    </div>
                    <div>
                        当前付款复核的设置：<span id="cur2"></span>
                    </div>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <%--<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="savePaycharge"  onclick="applySubmit()">提交修改申请</span>--%>
            <span class="ty-btn ty-btn-big ty-circle-3" id="savePaychargeNo" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>

    <%-- 采购审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="purchaseApprovalSettings" >
        <div class="bonceHead">
            <span class="bounce-title">采购审批设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">目前，本公司采购需 <span class="level"></span> 级审批</div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">开始执行的时间</div>
                    <div class="item-auto text-right openDate"></div>
                </div>
                <div class="kj-hr"></div>
                <div class="approvalFlows"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- 报销修改 --%>
    <div class="bonceContainer bounce-blue " id="finananceEdit">
        <div class="bonceHead">
            <span>查看报销审批的审批设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>目前本公司报销的审批流程如下：
                </p>
                <div id="flowList3"> </div>
                <div class="applyTip" style="margin-left:65px;"> </div>
            </div>

        </div>
        <div class="bonceFoot">
            <div class="">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="closeFin2" onclick="bounce.cancel()"  >关闭</span>
            </div>

        </div>
    </div>
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue " id="requestLog">
        <div class="bonceHead">
            <span id="logType">修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="summary"> </div>
            <table class="kj-table" id="log">
                <tr>
                    <td>资料状态</td>
                    <td id="log2">状态</td>
                    <td>开始执行日期</td>
                    <td id="log1">操作</td>
                    <td id="log13">付款复核者</td>

                    <td>创建人/修改人</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--  来自客户订单的评审 --%>
    <div class="bonceContainer bounce-blue" id="sale_common" style="width: 520px">
        <div class="bonceHead">
            <span>来自客户订单的评审</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="area-body">
                <div class="area-con">
                    <div>内容：<span id="msgLog">对于客户发来订单中的数量与交期，是否需公司各部门评审</span></div>
                    <div>功能：<span id="funLog">系统默认需要评审。可修改为“无需评审”</span></div>
                    <div>当前的设置：<span id="currentSettings"></span></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>审批查看</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container" id="infoCon">
                <span class="hd" id="navControl"></span>

                <div class="ty-mainData">
                    <%--审批权限--%>
                    <div class="tblContainer">

                        <table class="kj-table kj-table-striped">
                            <thead>
                            <tr>
                                <td rowspan="2"> 审批事项 </td>
                                <td rowspan="2"> 当前状态 </td>
                                <td colspan="3"> 当前审批者 </td>
                                <td rowspan="2"> 操作 </td>
                            </tr>
                            <tr>
                                <td>一级审批者</td>
                                <td>二级审批者</td>
                                <td>最终审批者</td>
                            </tr>
                            </thead>
                            <tbody id="chargeList"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/authority/approveLooking.js?v=SVN_REVISION"></script>

</body>
</html>