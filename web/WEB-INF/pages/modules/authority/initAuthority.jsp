<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/orgInit/initCommon.css" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <%--必须分配的权限 - 编辑--%>
    <div class="bonceContainer bounce-blue" id="editRole" style="width: 500px">
        <div class="bonceHead">
            <span>请选择人员</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="text-center">
                <span class="ty-color-red">* </span> <select name="role"></select>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn" id="sureEditRoleBtn">确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>初始化</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container" id="home">
                <div class="ty-page-header" style="display: none">
                    <div class="page-header__left" onclick="back()">
                        <i class="iconfont icon-houtui icon-back"></i>
                        <span>返回</span>
                    </div>
                    <div class="page-header__content" onclick="backToMain()">
                        <i class="iconfont icon-zhuye icon-back"></i>
                        <span>主页</span>
                    </div>
                </div>
                <div class="page" page="main" style="display: block">
                    <div class="ty-alert">
                        <div>
                            <div>公司高层已开通总务与采购功能，这要求以下 <span class="num"></span> 项初始化权限必须分配。</div>
                            <small class="ty-color-blue">注：以下权限在初始化阶段仅可分配给一人，初始化后可按实际情况结合系统功能再调整。</small>
                        </div>
                    </div>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td>权限</td>
                            <td>已拥有此权限者</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>采购的初始化</td>
                            <td>--</td>
                            <td><span class="link-blue">编辑</span></td>
                        </tr>
                        <tr>
                            <td>原辅材料库的初始化</td>
                            <td>--</td>
                            <td><span class="link-blue">编辑</span></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/orgInit/initCommon.js?v=SVN_REVISION"></script>
<script>
    $(function (){
        initMainData()
    })
    // creator: 张旭博，2023-09-06 04:55:34， 初始化首页的数据
    function initMainData() {
        // 加载必须分配的权限显示内容
        $.ajax($.webRoot + '/initialize/mustAllocationList.do').then(res => {
            let data = res.data
            let setData = []
            if (data) {
                setData = data.filter(item => item.userName)
            }
            $(".totalItem").html(data.length)
            $(".setItem").html(setData.length)
            console.log('data', data)

            // 直接在此处填充了弹窗的表格，避免多次调用
            let str = ''
            if (data) {
                for (let item of data) {
                    console.log('item', item)
                    str += `<tr data-code="\${item.code}">
                            <td>\${item.name}</td>
                            <td>\${item.userName || '--'}</td>
                            <td><span class="link-blue" data-state="\${item.state}" onclick="editRole($(this), 'haveToSetGeneralAuthority')">编辑</span></td>
                        </tr>`
                }
            }
            $(".page[page='main'] tbody").html(str)
            $(".page[page='main'] .num").html(data.length)
        })
    }
    // creator: 张旭博，2023-09-05 12:00:14， 选择人员
    function editRole(selector, name) {
        let state = selector.data("state")
        if (state === 1) {
            layer.msg("本项初始化已完成！")
            return false
        }
        bounce_Fixed.show($("#editRole"))
        $.ajax('../popedom/getUserList.do').then(res => {
            let data = res.userList
            let optionStr = '<option value="">----- 请选择 -----</option>'
            for(let item of data) {
                optionStr += `<option value="\${item.userID}">\${item.userName} \${item.mobile}</option>`
            }
            $("#editRole [name='role']").html(optionStr)
        })
        $("#editRole .sureBtn").unbind().on('click', function (){
            let passiveUserId = $("#editRole [name='role']").val()
            let code = selector.parents("tr").data("code")
            if (passiveUserId === '') {
                layer.msg('请选择人员！')
                return false
            }
            $.ajax({
                url: '../initialize/saveMustAllocation.do',
                data: {
                    code: code,
                    passiveUserId: passiveUserId,
                }
            }).then(res => {
                let data = res.data
                if (data === 1) {
                    layer.msg('操作成功！')
                    bounce_Fixed.cancel()
                    initMainData()
                } else {
                    layer.msg('操作失败')
                }
            })
        })
    }
</script>
</body>
</html>