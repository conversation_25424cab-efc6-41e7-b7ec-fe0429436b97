<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%--<link rel="stylesheet" href="https://demos.telerik.com/kendo-ui/grid/frozen-columns/styles/kendo.common.min.css" />--%>
<%--<link rel="stylesheet" href="../../../../styles/kendo.default.min.css" />
<link rel="stylesheet" href="../../../../styles/kendo.default.mobile.min.css" />--%>
<style>
    .ty-table thead td{
        text-align: center;
    }
    .ty-table td {
        padding: 0;
    }
    .ty-table td .text {
        padding: 0 10px;
        color: #666;
    }
    .ty-form-checkbox{
        margin-left: 16px;
    }
    .ty-form-checkbox[skin="green"] span {
        width: 180px;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed">
    <%--温馨提示-警告--%>
    <div class="bonceContainer bounce-red" id="F_errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--温馨提示-正常--%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--温馨提示-警告--%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--总务权限分配--%>
    <div class="bonceContainer bounce-green" id="generalAuthority" style="width: 900px">
        <div class="bonceHead">
            <span>权限分配</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-secondTab">
                <li class="ty-active">仅能分配给一人的权限</li>
                <li>可分配给多人的权限</li>
            </div>
            <div class="tblContainer">
                <div class="ty-alert">
                    <div>
                        下列权限仅可分配给一人，请勾选需分配给 <span class="userName"></span> 的权限。
                        <div class="ty-color-red">下某权限如已分配给某职工，则再次勾选生效后，原职工将不再拥有此权限。</div>
                    </div>
                </div>
                <table class="ty-table ty-table-left">
                    <thead>
                    <tr>
                        <td style="width: 25%">模块</td>
                        <td style="width: 75%">
                            <table class="ty-table" frame="void">
                                <thead>
                                <tr>
                                    <td style="width: 35%">子模块</td>
                                    <td style="width: 30%">二级子模块</td>
                                    <td style="width: 40%">已拥有此权限者</td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="tblContainer" style="display: none">
                <div class="ty-alert">
                    请勾选需分配给 <span class="userName"></span> 的权限。
                </div>
                <table class="ty-table ty-table-left">
                    <thead>
                    <tr>
                        <td style="width: 25%">模块</td>
                        <td style="width: 75%">
                            <table class="ty-table" frame="void">
                                <thead>
                                <tr>
                                    <td style="width: 30%">子模块</td>
                                    <td style="width: 30%">二级子模块</td>
                                    <td style="width: 40%">已拥有此权限者</td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="sureAllocateAuthority()" id="allocateAuthorityBtn">确定</span>
        </div>
    </div>
    <%--财务权限分配--%>
    <div class="bonceContainer bounce-green" id="financeAuthority" style="width: 700px">
        <div class="bonceHead">
            <span>权限分配</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <table class="ty-table ty-table-left">
                <thead>
                <tr>
                    <td>模块</td>
                    <td>子模块</td>
                    <td>二级子模块</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green" level="1">
                            <span>权限管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" level="2">
                            <span>权限设置</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" level="3">
                            <span>常规权限设置</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                </tr>
                <%--财务管理--%>
                <tr>
                    <td rowspan="8">
                        <div class='ty-form-checkbox' skin="green">
                            <span>财务管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>数据查看</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>账户设置</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>支票管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>数据录入</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>回款票据</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>报销受理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>发票管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>修改记录</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="editRoleSubmit()" id="editRoleSubmit">确认</span>
        </div>
    </div>
    <%--销售权限分配--%>
    <div class="bonceContainer bounce-green" id="saleAuthority" style="width: 700px">
        <div class="bonceHead">
            <span>权限分配</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <table class="ty-table ty-table-left">
                <thead>
                <tr>
                    <td>模块</td>
                    <td>子模块</td>
                    <td>二级子模块</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green" level="1">
                            <span>权限管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" level="2">
                            <span>权限设置</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green" level="3">
                            <span>常规权限设置</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                </tr>
                <%--销售管理--%>
                <tr>
                    <td rowspan="3">
                        <div class='ty-form-checkbox' skin="green">
                            <span>销售管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>客户管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>订单管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>要货计划处理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <%--商品管理--%>
                <tr>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>商品管理</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td>
                        <div class='ty-form-checkbox' skin="green">
                            <span>基本信息</span>
                            <i class="fa fa-check"></i>
                        </div>
                    </td>
                    <td></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="editRoleSubmit()" id="editSaleRoleSubmit">确认</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>权限设置</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container" id="infoCon">
                <span class="hd" id="navControl"></span>

                <div class="ty-mainData">
                    <%--常规权限列表--%>
                    <div class="tplContainer">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>姓名</td>
                                <td>性别</td>
                                <td>手机号</td>
                                <td>部门</td>
                                <td>职位</td>
                                <td>直接上级</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="ye_generalSettings"></div>
                </div>
            </div>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/authority/generalSettings.js?v=SVN_REVISION"></script>

</body>
</html>