 <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    td > div:last-child{
        border: none;
    }
    .ty-table td {
        padding: 0;
    }
    .importantTip{
        width: 638px;
        height: 128px;
        border:2px dashed #0099FF;
        padding:20px 40px;
        text-align: center;
        background-color: #fff;
        margin-bottom: 8px;
    }
    .ty-table .ty-head li{
        border:none;
    }
    .ty-table .ty-body{
        background: #fff;
    }
    .ty-table ul{
        width: 100%;
        overflow: hidden;
    }
    .ty-table ul li{
        float: left;
        border:1px solid #efefef;
        min-height: 40px;
        line-height:40px;
        text-align: center;
        margin-right: -1px;
        height:100%;
        overflow:hidden;
        padding-bottom:9999px;
        margin-bottom:-9999px
    }
    .ty-table .ty-head li{
        border:none;
    }
    .ty-table .ty-body{
        background: #fff;
    }
    .ty-table ul{
        width: 100%;
        overflow: hidden;
    }
    .ty-table ul li{
        float: left;
        border:1px solid #efefef;
        min-height: 40px;
        line-height:40px;
        text-align: center;
        margin-right: -1px;
        height:100%;
        overflow:hidden;
        padding-bottom:9999px;
        margin-bottom:-9999px
    }

</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确认</span>
        </div>
    </div>
    <%-- confirm 框  --%>
    <div class="bonceContainer bounce-blue" id="confirm">
        <div class="bonceHead">
            <span id="confirmTtl">提示信息</span>
            <a class="bounce_close" onclick="confirCancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="confirMs">    </div>
        </div>
        <div class="bonceFoot">
            <span class="hd" id="confirmType"></span>
            <span class="ty-btn  ty-btn-big ty-circle-5" onclick="confirCancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="confirOK()">确认</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>当前权限</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container" id="infoCon">
                <ul class="ty-secondTab">
                    <li class="ty-active">已分配权限</li>
                    <li>未分配权限</li>
                </ul>
                <div class="ty-mainData">
                    <%--审批权限--%>
                    <div class="tblContainer">
                        <div class="ty-table" id="assigned">
                            <ul class="ty-head">
                                <li style="width: 10%">模块</li>
                                <li style="width: 18%">子模块</li>
                                <li style="width: 14.4%">二级子模块</li>
                                <li style="width: 36%">功能描述</li>
                                <li style="width: 21.6%">已拥有此权限者</li>
                            </ul>
                            <div class="ty-body"></div>
                        </div>
                        <div class="importantTip" style="display: none">
                            <h4>重要提示!!</h4>
                            <p>您把公司最高销售、财务与总务管理人员手机号码录入到高管管理中，才能更好地使用本系统。</p>
                        </div>
                    </div>
                    <div class="tblContainer" id="unassigned" style="display: none">
                        <div class="ty-table">
                            <ul class="ty-head">
                                <li style="width: 10%">模块</li>
                                <li style="width: 18%">子模块</li>
                                <li style="width: 72%">功能描述</li>
                            </ul>
                            <div class="ty-body"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/authority/currentSettings.js?v=SVN_REVISION"></script>

</body>
</html>