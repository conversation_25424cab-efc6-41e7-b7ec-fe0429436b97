<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/activityIndex/activityIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed3">
    <%-- 指标设置 --%>
    <div class="bonceContainer bounce-blue backwall" id="indicatorsetting1">
        <div class="bonceHead Ater operd">
            <span>指标设置</span>
            <a class="bounce_close sett" onclick="agadifften(1)"></a>
            <a class="bounce_close linkt" onclick="agadifften(2)"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words">
                    <span class="partition">
                        设:<br />
                        每日登录总次数为a,每日登录总时长为b，每日实际登录人数为c,每日零时在册职工与浏览者人数为d
                    </span>
                    <span class="partition">
                           期间内a的和为A,b的和为B，c的和为C，d的和为D
                    </span>
                    <span class="partition">
                          自创建之日起至查看之日前一日的天数为E
                    </span>
                    <span class="partition">
                         期间招聘/调查扫描者人数为F
                    </span>
                    <span class="partition">
                        机构创建之日至之后的3天算第一组，及之后每4天算第一组。发生招聘/调查扫描的组，招聘/调查扫描者分布离散值记作1，否则记作0
                    </span>
                    <br />
                    <span class="partition tagging">
                        注1 以下除扫描者外，其他指标统计对象包含暂为机构在册职工与浏览者
                    </span>
                    <span class="partition tagging">
                        注2 需纳入机构在册职工、浏览者或招聘/调查扫描者以外人员时，另行确定
                    </span>
                    <br />
                </p>
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td width="16%">指标</td>
                        <td width="6%">算法</td>
                        <td width="7%">算出的值</td>
                        <td width="9%">指标的值</td>
                        <td width="10%">算出的值</td>
                        <td width="10%">指标的值</td>
                        <td width="10%">算出的值</td>
                        <td width="10%">指标的值</td>
                        <td width="9%">算出的值</td>
                        <td width="9%">指标的值</td>
                        <td width="10%" class="sett">操作</td>
                    </tr>
                    </thead>
                    <tbody id="zbtool">
                    <tr>
                        <td>每日人均登录测试指标</td>
                        <td>A/DE</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>每日人均登录时长指标</td>
                        <td>B/DE</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>登录者每日人均登录次数指标</td>
                        <td>A/CE</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>登录者每日人均登录时长指标</td>
                        <td>B/CE</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>招聘/调查扫描者人数指标</td>
                        <td>F</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>招聘/调查扫描者分别离散指标</td>
                        <td>按“设”</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5 sett" onclick="agadifften(1)">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5 linkt" onclick="agadifften(2)">关闭</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 botten giveup"
                        onclick="madetune()" id="madetune">确定</button>
            </span>
        </div>
    </div>
    <%--指标设置记录--%>
    <div class="bonceContainer bounce-blue insetred" id="IndicatorSettingRecord">
        <div class="bonceHead">
            <span>指标设置记录</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend operd">
                    <span class="befon">当前数据未修改</span>
                    <span class="nowon">当前数据为第X次修改后的结果。</span>
                    <span class="timr">修改时间：XXXX-XX-XX XX:XX:XX</span>
                </p>
                <table class="ty-table ty-table-control boxn tb-icn">
                    <thead>
                    <tr>
                        <td>记录</td>
                        <td>操作</td>
                        <td>创建者/修改者</td>
                    </tr>
                    </thead>
                    <tbody id="icalink">
                    <tr>
                        <td>原始信息</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="gettdcator($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    <tr>
                        <td>第1次修改后</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="gettdcator($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    <tr>
                        <td>第2次修改后</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="gettdcator($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%--活跃指数1权重设置--%>
    <div class="bonceContainer bonce-blue topn" id="weightsetting1">
        <div class="bonceHead">
            <span class="sett">活跃指数1权重设置</span>
            <span class="linkt">活跃指数1权重设置记录</span>
            <a class="bounce_close sett" onclick="adadifen(1)"></a>
            <a class="bounce_close linkt" onclick="adadifen(2)"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend">
                    <span class="partition">
                        活跃指数1——创建不足90天机构的wonderss使用指数
                    </span>
                    <span class="partition tagging">
                        注1 以下除扫描者外，其他指标统计对象包含暂为机构在册职工与浏览者
                    </span>
                    <span class="partition tagging">
                        注2 需纳入机构在册职工、浏览者或招聘/调查扫描者以外人员时，另行确定
                    </span>
                    <span class="partition tagging">
                        注3 权重的和需为100%时方可成功
                    </span>
                    <br />
                <table class="ty-table ty-table-control boxn">
                    <thead>
                    <tr>
                        <td>指标</td>
                        <td>权重</td>
                    </tr>
                    </thead>
                    <tbody id="boxpont">
                    <tr id="pont1"></tr>
                    <tr id="pont2"></tr>
                    <tr id="pont3"></tr>
                    <tr id="pont4"></tr>
                    <tr id="pont5"></tr>
                    <tr id="pont6"></tr>
                    <tr id="pont7">
                    </tr>
                    </tbody>
                </table>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5 sett" onclick="adadifen(1)">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5 linkt" onclick="adadifen(2)">关闭</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 botten giveup"
                        onclick="updanteud()">提交</button>
            </span>
        </div>
    </div>
    <%--活跃指数1权重设置记录--%>
    <div class="bonceContainer bonce-blue insetred" id="weightsetink1">
        <div class="bonceHead">
            <span>活跃指数1权重设置记录</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend operd">
                    <span class="befon">当前数据未修改</span>
                    <span class="nowon">当前数据为第X次修改后的结果。</span>
                    <span class="timr">修改时间：XXXX-XX-XX XX:XX:XX</span>
                </p>
                <table class="ty-table ty-table-control boxn tb-icn">
                    <thead>
                    <tr>
                        <td>记录</td>
                        <td>操作</td>
                        <td>创建者/修改者</td>
                    </tr>
                    </thead>
                    <tbody id="setink">
                    <tr>
                        <td>原始信息</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="getsetlink($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    <tr>
                        <td>第1次修改后</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="getsetlink($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    <tr>
                        <td>第2次修改后</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="getsetlink($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%--历代负责人--%>
    <div class="bonceContainer bonce-blue insetred" id="Previousleaders">
        <div class="bonceHead">
            <span>历代负责人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend operd">
                    <span id="salename">XXXXXXXXXXX(机构名称)的历任销售负责人</span>
                </p>
                <table class="ty-table ty-table-control boxn tb-icn">
                    <thead>
                    <tr>
                        <td>姓名</td>
                        <td>手机号</td>
                        <td>上任的操作记录</td>
                        <td>卸任的操作记录</td>
                    </tr>
                    </thead>
                    <tbody id="preioead"></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%--销售更换记录--%>
    <div class="bonceContainer bounce-green" id="changeSalerRecord" style="width: 800px;">
        <div class="bonceHead">
            <span>销售负责人更换记录</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-green ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--具体指标设置--%>
    <div class="bonceContainer bonce-blue boxd" id="spcificnexeting1">
        <div class="bonceHead">
            <span>具体指标设置</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words comment">
                    <span class="partition" id="Indicator">
                        每日人均登录次数指标
                    </span>
                    <br />
                    <br />
                    <span class="partition">
                        下表中a、b、c为“算出的值”的上下限，各默认值可修改
                    </span>
                </p>
                <table class="ty-table ty-table-control boxn">
                    <thead>
                    <tr></tr>
                    </thead>
                    <tbody>
                    <tr id="tb1"></tr>
                    <tr id="tb2"></tr>
                    <tr id="tb3"></tr>
                    </tbody>
                </table>
                <br />
                <span class="partition comment">
                        下表中，各“算出的值”的范围均需对应指标的值，如必要可修改
                </span>
                <table class="ty-table ty-table-control annotate">
                    <thead>
                    <tr>
                        <td class="count">“算出的值”的范围</td>
                        <td>指标的值</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr id="tb4"></tr>
                    <tr id="tb5"></tr>
                    <tr id="tb6"></tr>
                    <tr id="tb7"></tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5" onclick="bounce_Fixed2.cancel();bounce_Fixed3.show($('#indicatorsetting1'))">取消</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 botten" id="Submit"
                        onclick="pointsure($(this))">提交</button>
            </span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 删除 --%>
    <div class="bonceContainer bounce-green" id="tipConImg" >
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <img src="" id="tipImg" style="width:100%; ">
        </div>
    </div>

</div>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>活跃指数1</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <!--
            <div class="ty-container">
                <%-- 主页面 --%>
                <div class="mainCon mainCon1">
                    <div class="clear line">
                        <span>以下为创建不足90天且尚未收过费的机构，共XX个</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right marle20" data-fun="goStop" >指标设置</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right marle50" data-fun="goStop" >活跃指数1权重设置</span>

                        <div class="ty-right searchSect">
                            <div class="ty-left keywordSearch"><span class="ty-left">查找</span>
                                <div class="inputBox ty-right">
                                    <i></i>
                                    <input type="text" id="searchKey" placeholder=" 请输入关键字或素材代号">
                                </div>
                            </div>
                            <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchBtn1">确定</button>
                        </div>

                    </div>
                    <div class="marTop30">
                        <table class="ty-table ty-table-control widthTab">
                            <tr>
                                <td width="14%">机构名称</td>
                                <td width="23%">创建</td>
                                <td width="10%">创建之日至今的天数</td>
                                <td width="10%">当前内部用户数</td>
                                <td width="10%">当前外部用户数</td>
                                <td width="10%">活跃指数1</td>
                                <td width="23%">相关图表</td>
                            </tr>
                            <tr>
                                <td>机构名称</td>
                                <td>创建</td>
                                <td>创建之日至今的天数</td>
                                <td>当前内部用户数</td>
                                <td>当前外部用户数</td>
                                <td class="ty-td-control">
                                    <span class="ty-color-blue funBtn" data-fun="activity">活跃指数</span>
                                </td>
                                <td>
                                    <span class="ty-color-blue funBtn" data-fun="chart" data-type="1">图1</span>
                                    <span class="ty-color-blue funBtn" data-fun="chart" data-type="2">图2</span>
                                    <span class="ty-color-blue funBtn" data-fun="chart" data-type="3">图3</span>
                                    <span class="ty-color-blue funBtn" data-fun="chart" data-type="4">图4</span>
                                    <span class="ty-color-blue funBtn" data-fun="chart" data-type="5">图5</span>
                                    <span class="ty-color-blue funBtn" data-fun="chart" data-type="6">图6</span>
                                </td>
                            </tr>

                        </table>
                        <div id="page1"></div>
                        <div id="page1Search"></div>
                    </div>
                </div>
                <%--   活跃指数详情  --%>
                <div class="mainCon mainCon2">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 " data-fun="goMain">返回</span>
                        <p class=" marTop30">
                            <span>机构名称 XXXXXXXXXXXXXX</span>
                            <span class="marle50">创建 XXX XXXX-XX-XX XX:XX:XX</span>
                        </p>
                    </div>
                    <div class="marbtm30">
                        <table class="ty-table ty-table-control" style="width: 600px">
                            <tr>
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                            </tr>
                        </table>
                        <hr/>
                        <div>
                            <p>设：</p>
                            <p>每日登录总次数为a，每日登录总时长为b，每日实际登录人数为c，每日零时在册职工与浏览者人数为d
                                期间内a的和为A，b的和为B，c的和为C，d的和为D
                            </p>
                            <p>自创建之日起至查看之日前一日的天数为E </p>
                            <p>期间招聘/调查扫描者人数为F，从机构创建之日起，创建之日算第1天，第1-4天为一组，及之后每4天算一组，发生招聘/调查扫描的组，招聘/调查扫描者分布离散值记作1，否则记作0</p>
                        </div>
                        <div class="bluetip marbtm30">
                            <p>注1 以下除扫描者外，其他指标统计对象包含暂为机构在册职工与浏览者</p>
                            <p>注2 需纳入机构在册职工、浏览者或招聘/调查扫描者以外人员时，另行确定</p>
                        </div>
                        <table class="ty-table ty-table-control ">
                            <tr>
                                <td width="14%">指标</td>
                                <td width="23%">算法</td>
                                <td width="10%">算出的值</td>
                                <td width="10%">权重</td>
                                <td width="10%">指标的值</td>
                            </tr>
                            <tr>
                                <td>每日人均登录次数指标</td>
                                <td>A/DE</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>每日人均登录时长指标</td>
                                <td>B/DE</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>登录者每日人均登录次数指标</td>
                                <td>A/CE</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>登录者每日人均登录时长指标</td>
                                <td>B/CE</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>招聘/调查扫描者人数指标</td>
                                <td>F</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>招聘/调查扫描者分布离散指标</td>
                                <td>按“设”</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>

                        </table>
                    </div>
                </div>
                <%--   图 123456   --%>
                <div class="mainCon mainCon3">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 " data-fun="goMain">返回</span>
                        <p class=" marTop30">
                            <span>机构名称 XXXXXXXXXXXXXX</span>
                            <span class="marle50">创建 XXX XXXX-XX-XX XX:XX:XX</span>
                        </p>
                    </div>
                    <div class="marbtm30">
                        <table class="ty-table ty-table-control" style="width: 600px">
                            <tr>
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                            </tr>
                        </table>
                        <hr/>
                        <div class="poRele">
                            <div class="tuli">
                                <div>
                                    <div class="tuli1">
                                        <p><span class="ttl">图中</span><span>每日零时在册职工与浏览者人数d展示于白底色框中</span></p>
                                        <p><span class="ttl"></span><span>每日实际登录人数c展示于橙底色框中</span></p>
                                        <p><span class="ttl"></span><span>白底色框中的百分比为每日的登录比</span></p>
                                    </div>
                                    <div class="tuli2">
                                        <p><span>期初外部用户数  XXX</span></p>
                                        <p><span>各框最上端数字代表该日实际的外部用户数</span></p>
                                        <p><span>橙底色里的数字代表期初基础上增加的人数</span></p>
                                        <p><span>绿底色里的数字代表期初基础上减少的人数</span></p>
                                    </div>
                                    <div class="tuli3">
                                        <p><span class="ttl">期间</span><span>登录总次数 XXX</span></p>
                                        <p><span class="ttl"></span><span>日人均登录XXX次</span></p>
                                        <p><span class="ttl"></span><span>登录者日人均登录XXX次</span></p>
                                    </div>
                                    <div class="tuli4">
                                        <p><span class="ttl">期间</span><span>登录总时长 XXX</span></p>
                                        <p><span class="ttl"></span><span>日人均登录XXX时长</span></p>
                                        <p><span class="ttl"></span><span>登录者人均登录XXX时长</span></p>
                                    </div>
                                    <div class="tuli5">
                                        <p><span class="ttl">期间</span><span>登录总次数 XXX</span></p>
                                    </div>
                                    <div class="tuli6">
                                        <p><span class="ttl">期间</span><span>登录总次数 XXX</span></p>
                                    </div>

                                </div>

                                <div class="searchDate">
                                    <select id="year" class="form-control">
                                        <option value="2020">2020</option>
                                        <option value="2021">2021</option>
                                        <option value="2022">2022</option>
                                    </select>
                                    <select id="month" class="form-control">
                                        <option value="01">1月</option>
                                        <option value="02">2月</option>
                                        <option value="03">3月</option>
                                        <option value="04">4月</option>
                                        <option value="05">5月</option>
                                        <option value="06">6月</option>
                                        <option value="07">7月</option>
                                        <option value="08">8月</option>
                                        <option value="09">9月</option>
                                        <option value="10">10月</option>
                                        <option value="11">11月</option>
                                        <option value="12">12月</option>
                                    </select>
                                </div>
                            </div>
                            <div class="tu">
                                <p id="chartTtl"></p>
                                <div id="chart" ></div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>-->
            <div class="smallt">
                <!--首页-->
                <div class="ty-container" id="home">
                    <div class="clse" id="picShow">
                        <img src=""/>
                    </div>
                    <div>
                        <div class="main manet">
                            <div class="word catch" id="day">以下为创建不足90天且尚未收过费的机构，共<span id="day1">XX</span>个</div>
                            <span class="catch serch">搜索</span>
                            <span class="Search">
                                <input id="se0" type="text" placeholder="请输入关键词" />
                                <span class="se" onclick="makesure(1,20)" id="peachstreat">确定</span>
                            </span>
                            <div class="pull-right"><!--下面这四个按钮需要根据不同情况进行判断，假设销售高管权限是1，则不为1时这四个按钮展示，反之则不展示。-->
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="contractImport" onclick="getindicator1($(this))" style="padding: 0px 15px;">指标设置</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="contraImpLink" onclick="getindicaink($(this))" style="padding: 0px 15px;">指标设置记录</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="addCot" onclick="getweight1($(this))" style="padding: 0px 15px;">活跃指数1权重设置</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="addCotLink" onclick="getweightink($(this))" style="padding: 0px 15px;">活跃指数1权重设置记录</span>
                            </div><br>
                            <div class="opinionCon gauge">
                                <table class="ty-table ty-table-control">
                                    <thead class="boad">
                                    <td width="14%">机构名称</td>
                                    <td width="10%">销售负责人</td>
                                    <td width="20%">创建</td>
                                    <td width="11%">创建之日至今的天数</td>
                                    <td width="10%">当前内部用户数</td>
                                    <td width="10%">当前外部用户数</td>
                                    <td width="10%">活跃指数1
                                        <%--  Head of Sales <button onclick="pointone($(this))">活跃指数1</button>--%>
                                    </td>
                                    <td width="24%">
                                        相关图表
                                                                                <!--<button onclick="getpicture1($(this))">图1</button>
                                                                                <button onclick="getpicture2($(this))">图2</button>
                                                                                <button onclick="getpicture3($(this))">图3</button>
                                                                                <button onclick="getpicture4($(this))">图4</button>
                                                                                <button>图5</button>-->
                                    </td>
                                    </thead>
                                    <tbody class="sale_Tbody" id="cotManage_body">
                                    <!--<tr>
                                        <td>供应商1号</td>
                                        <td>员工 2022-11-29 11:44:12</td>
                                        <td>2</td>
                                        <td>3</td>
                                        <td>4</td>
                                        <td class="ty-td-control">
                                            <span class="ty-color-blue funBtn" data-fun="activity" onclick="pointone($(this))">活跃指数</span>
                                        </td>
                                        <td>
                                            <span class="ty-color-blue funBtn" data-fun="chart" data-type="1" onclick="getpicture1($(this))">图1</span>
                                            <span class="ty-color-blue funBtn" data-fun="chart" data-type="2" onclick="getpicture2($(this))">图2</span>
                                            <span class="ty-color-blue funBtn" data-fun="chart" data-type="3" onclick="getpicture3($(this))">图3</span>
                                            <span class="ty-color-blue funBtn" data-fun="chart" data-type="4" onclick="getpicture4($(this))">图4</span>
                                            <span class="ty-color-blue funBtn" data-fun="chart" data-type="5" onclick="getpicture5($(this))">图5</span>
                                            <span class="ty-color-blue funBtn" data-fun="chart" data-type="6" onclick="getpicture6($(this))">图6</span>
                                        </td>
                                    </tr>-->
                                    </tbody>
                                </table>
                                <div id="ye_Activityindex"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                    </div>
                </div>
                <!--搜索页-->
                <div class="ty-container clse" id="showcatch">
                    <div>
                        <button type="button" class="btn btn-default back" id="backst" onclick="comeckst()">返回</button>
                        <div class="main">
                            <div class="word catch" id="dayst">符合条件的数据共<span id="dayst1">XX</span>条，具体如下： </div>
                            <div class="opinionCon gauge">
                                <table class="ty-table ty-table-control">
                                    <thead class="boad">
                                    <td width="14%">机构名称</td>
                                    <td width="20%">创建</td>
                                    <td width="11%">创建之日至今的天数</td>
                                    <td width="10%">当前内部用户数</td>
                                    <td width="10%">当前外部用户数</td>
                                    <td width="10%">活跃指数1</td>
                                    <td width="24%">相关图表</td>
                                    </thead>
                                    <tbody class="sale_Tbody" id="cotManage_bodyst"></tbody>
                                </table>
                                <div id="ye_Activityindexst"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                    </div>
                </div>
                <!--指数详情页-->
                <div class="ty-container clse" id="indexmore1">
                    <button type="button" class="btn btn-default back" id="back1" onclick="comebck1()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title tatle" id="titale1">
                                <span>机构名称:</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table boxb">
                                <thead class="boad">
                                <td width="18%">当前内部用户数</td>
                                <td width="18%">当前外部用户数</td>
                                <td width="18%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="actitytble">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <%--                        <div class="link"></div>--%>
                        <hr class="ht"/>
                        <p class="words">
                            <span class="partition">
                                设：
                            </span>
                            <span>
                                每日登录总次数为a，每日登录总时长为b，每日实际登录人数为c，每日零时在册职工与浏览者人数为d
                            </span>
                            <span class="partition">
                                期间内a的和为A，b的和为B，c的和为C，d的和为D
                            </span>
                            <span class="partition">
                                自创建之日起至查看之日前一日的天数为E
                            </span>
                            <span class="partition">
                                期间招聘/调查扫描者人数为F，从机构创建之日起，创建之日算第1天，第1-4天为一组，及之后每4天算一组，
                                发生招聘/调查扫描的组，招聘/调
                            </span>
                            <span class="partition">查扫描者分布离散值记作1，否则记作0</span>
                            <br />
                            <span class="partition tagging">
                                注1 以下除扫描者外，其它指标统计对象包含暂为机构在册职工与浏览者
                            </span>
                            <span class="partition tagging">
                                注2 需纳入机构在册职工、浏览者或招聘/调查扫描者以外人员时，另行确定
                            </span>
                            <br />
                        </p>
                        <%--<table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td width="14%">指标</td>
                                <td width="23%">算法</td>
                                <td width="10%">算出的值</td>
                                <td>指标的值</td>
                                <td>算出的值</td>
                                <td>指标的值</td>
                                <td>算出的值</td>
                                <td>指标的值</td>
                                <td>算出的值</td>
                                <td>指标的值</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>--%>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td width="14%">指标</td>
                                <td width="23%">算法</td>
                                <td width="10%">算出的值</td>
                                <td width="10%">权重</td>
                                <td width="10%">指标的值</td>
                            </tr>
                            </thead>
                            <tbody id="acdexinx">
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--图标页(图标1)-->
                <div class="ty-container clse" id="iconpoint1">
                    <button type="button" class="btn btn-default back" id="back12" onclick="comebck12()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge tatle" id="titale12">
                                <span>机构名称:</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activity12">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr class="ht"/>
                        <%--                        <div class="link"></div>--%>
                        <%--下面是图表内容--%>
                        <div id="outerbox1">
                            <div id="tumisu1">
                                <div>
                                    <%--描述文字--%>
                                    <div id="mite1" class="write detxt">
                                    </div>
                                    <!-- 图表1 -->
                                    <p id="misu1"></p>
                                    <div id="main" class="pictune"></div>
                                    <!--<p id="chartTtl"></p>-->
                                    <!--<div id="chart" ></div>-->
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contract11" class="choase form-control" onchange="chaneTime(1,1)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contract12" class="choase form-control" onchange="chaneTime(1,1)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标2)-->
                <div class="ty-container clse" id="iconpoint2">
                    <button type="button" class="btn btn-default back" id="back22" onclick="comebck22()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title tat" id="titale22">
                                <span>机构名称</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activity22">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr />
                        <%--                        <div class="link"></div>--%>
                        <!--下面是图表内容-->
                        <div id="outerbox2">
                            <div id="tumisu2">
                                <!--描述文字-->
                                <div id="mite2" class="write">
                                    <%--                                    <p><span>期初外部用户数  XXX</span></p>--%>
                                    <%--                                    <p><span>各框最上端数字代表该日实际的外部用户数</span></p>--%>
                                    <%--                                    <p><span>橙底色里的数字代表期初基础上增加的人数</span></p>--%>
                                    <%--                                    <p><span>绿底色里的数字代表期初基础上减少的人数</span></p>--%>
                                </div>
                                <!--图表2-->
                                <p id="misu2"></p>
                                <div id="main2" class="pictune"></div>
                                <!--筛选-->
                                <div class="choose dressy">
                                    <select id="contract21" class="choase form-control" onchange="chaneTime(1,2)">
                                        <option value="2020">2020</option>
                                        <option value="2021">2021</option>
                                        <option value="2022">2022</option>
                                        <option value="2023">2023</option>
                                        <option value="2024">2024</option>
                                        <option value="2025">2025</option>
                                        <option value="2026">2026</option>
                                        <option value="2027">2027</option>
                                        <option value="2028">2028</option>
                                        <option value="2029">2029</option>
                                    </select>
                                    <select id="contract22" class="choase form-control" onchange="chaneTime(1,2)">
                                        <option value="1">1月</option>
                                        <option value="2">2月</option>
                                        <option value="3">3月</option>
                                        <option value="4">4月</option>
                                        <option value="5">5月</option>
                                        <option value="6">6月</option>
                                        <option value="7">7月</option>
                                        <option value="8">8月</option>
                                        <option value="9">9月</option>
                                        <option value="10">10月</option>
                                        <option value="11">11月</option>
                                        <option value="12">12月</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标3)-->
                <div class="ty-container clse" id="iconpoint3">
                    <button type="button" class="btn btn-default back" id="back32" onclick="comebck32()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge" id="titale32">
                                <span>机构名称</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activity32">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr />
                        <%--                        <div class="link"></div>--%>
                        <!--下面是图表内容-->
                        <div id="outerbox3">
                            <div id="tumisu3">
                                <div>
                                    <!--描述文字-->
                                    <div id="mite3" class="write">
                                        <%--                                        <p><span class="ttl">期间</span><span>登录总次数 XXX</span></p>--%>
                                        <%--                                        <p><span class="ttl"></span><span>日人均登录XXX次</span></p>--%>
                                        <%--                                        <p><span class="ttl"></span><span>登录者日人均登录XXX次</span></p>--%>
                                    </div>
                                    <!--图表3-->
                                    <p id="misu3"></p>
                                    <div id="main3" class="pictune"></div>
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contract31" class="choase form-control" onchange="chaneTime(1,3)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contract32" class="choase form-control" onchange="chaneTime(1,3)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标4)-->
                <div class="ty-container clse" id="iconpoint4">
                    <button type="button" class="btn btn-default back" id="back42" onclick="comebck42()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge" id="titale42">
                                <span>机构名称</span><span>XXXXX</span>
                                <span>创建</span><span>XXX  XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activity42">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr />
                        <%--                        <div class="link"></div>--%>
                        <!--下面是图表内容-->
                        <div id="outerbox4">
                            <div id="tumisu4">
                                <div>
                                    <!--描述文字-->
                                    <div id="mite4" class="write">
                                        <%--                                        <p><span class="ttl">期间</span><span>登录总时长 XXX</span></p>--%>
                                        <%--                                        <p><span class="ttl"></span><span>日人均登录XXX时长</span></p>--%>
                                        <%--                                        <p><span class="ttl"></span><span>登录者人均登录XXX时长</span></p>--%>
                                    </div>
                                    <!-图表4-->
                                    <div id="main4" class="pictune"></div>
                                    <!-筛选-->
                                    <div class="choose dressy">
                                        <select id="contract41" class="choase form-control" onchange="chaneTime(1,4)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contract42" class="choase form-control" onchange="chaneTime(1,4)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标5)-->
                <div class="ty-container clse" id="iconpoint5">
                    <button type="button" class="btn btn-default back" id="back52" onclick="comebck52()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge" id="titale52">
                                <span>机构名称</span><span>XXXXX</span>
                                <span>创建</span><span>XXX  XXXX-XX-XX  XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activity52">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr />
                        <%--                        <div class="link"></div>--%>
                        <!--下面是图表内容-->
                        <div id="outerbox5">
                            <div id="tumisu5">
                                <div>
                                    <!--描述文字-->
                                    <div id="mite5" class="write">
                                        <%--                                        <p><span class="ttl">期间</span><span>登录总次数 XXX</span></p>--%>
                                    </div>
                                    <!--图表5-->
                                    <div id="main5" class="pictune"></div>
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contract51" class="choase form-control" onchange="chaneTime(1,5)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contract52" class="choase form-control" onchange="chaneTime(1,5)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标6)-->
                <div class="ty-container clse" id="iconpoint6">
                    <button type="button" class="btn btn-default back" id="back62" onclick="comebck62()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge" id="titale62">
                                <span>机构名称:</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activity62">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr />
                        <!--下面是图表内容-->
                        <div id="outerbox6">
                            <div id="tumisu6">
                                <div>
                                    <!--描述文字-->
                                    <div id="mite6">
                                        <%--                                        <p><span class="ttl">期间</span><span>登录总次数 XXX</span></p>--%>
                                    </div>
                                    <!--图表6-->
                                    <div id="main6" class="pictune"></div>
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contract61" class="choase form-control" onchange="chaneTime(1,6)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contract62" class="choase form-control" onchange="chaneTime(1,6)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/activityIndex/activityIndex.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/common/echarts.common.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    $(function(){
        //获取活跃指数1首页数据列表
        getactiveable(1,20);
    })
</script>
</body>
</html>
