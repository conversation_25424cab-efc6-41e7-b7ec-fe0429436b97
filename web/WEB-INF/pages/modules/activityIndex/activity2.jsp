<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/activityIndex/activityIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed3">
    <%-- 指标设置 --%>
    <div class="bonceContainer bounce-blue backwall" id="indicatorsetting2">
        <div class="bonceHead">
            <span>指标设置</span>
            <a class="bounce_close sett" onclick="agadifften2(1)"></a>
            <a class="bounce_close linkt" onclick="agadifften2(2)"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words">
                    <span class="partition">
                        设:
                        每日登录总次数为a,每日登录总时长为b，每日实际登录人数为c,每日零时在册职工与浏览者人数为d
                    </span>
                    <span class="partition">
                        期间内a的和为A,b的和为B，c的和为C，d的和为D
                    </span>
                    <span class="partition">
                        自创建之日起至查看之日前一日的天数为E
                    </span>
                    <span class="partition">
                        期间招聘/调查扫描者人数为F
                    </span>
                    <span class="partition">
                        机构创建之日至之后的3天算第一组，及之后每4天算第一组。发生招聘/调查扫描的组，招聘/调查扫描者分布离散值记作1，否则记作0
                    </span>
                    <br />
                    <span class="partition tagging">
                        注1 以下除扫描者外，其他指标统计对象包含暂为机构在册职工与浏览者
                    </span>
                    <span class="partition tagging">
                        注2 需纳入机构在册职工、浏览者或招聘/调查扫描者以外人员时，另行确定
                    </span>
                    <br />
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td width="16%">指标</td>
                        <td width="6%">算法</td>
                        <td width="7%">算出的值</td>
                        <td width="9%">指标的值</td>
                        <td width="10%">算出的值</td>
                        <td width="10%">指标的值</td>
                        <td width="10%">算出的值</td>
                        <td width="10%">指标的值</td>
                        <td width="9%">算出的值</td>
                        <td width="9%">指标的值</td>
                        <td width="10%" class="sett">操作</td>
                    </tr>
                    </thead>
                    <tbody id="zbtool2">
                    <tr>
                        <td>每日人均登录测试指标</td>
                        <td>A/DE</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>每日人均登录时长指标</td>
                        <td>B/DE</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>登录者每日人均登录次数指标</td>
                        <td>A/CE</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>登录者每日人均登录时长指标</td>
                        <td>B/CE</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>招聘/调查扫描者人数指标</td>
                        <td>F</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    <tr>
                        <td>招聘/调查扫描者分别离散指标</td>
                        <td>按“设”</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn">设置</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5 sett" onclick="agadifften2(1)">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5 linkt" onclick="agadifften2(2)">关闭</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 botten giveup2"
                        onclick="madetune2()" id="madetune2">确定</button>
            </span>
        </div>
    </div>
    <%-- 指标设置记录 --%>
    <div class="bonceContainer bounce-blue insetred" id="IndicatorSettingRecord2">
        <div class="bonceHead">
            <span>指标设置记录</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend operd">
                    <span class="befon">当前数据未修改</span>
                    <span class="nowon">当前数据为第X次修改后的结果。</span>
                    <span class="timr">修改时间：XXXX-XX-XX XX:XX:XX</span>
                </p>
                <table class="ty-table ty-table-control boxn tb-icn">
                    <thead>
                    <tr>
                        <td>记录</td>
                        <td>操作</td>
                        <td>创建者/修改者</td>
                    </tr>
                    </thead>
                    <tbody id="icalink2">
                    <tr>
                        <td>原始信息</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="gettdcator2($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    <tr>
                        <td>第1次修改后</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="gettdcator2($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    <tr>
                        <td>第2次修改后</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="gettdcator2($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%-- 活跃指数2权重设置 --%>
    <div class="bonceContainer bonce-blue topn" id="weightsetting2">
        <div class="bonceHead">
            <span class="sett">活跃指数2权重设置</span>
            <span class="linkt">活跃指数2权重设置记录</span>
            <a class="bounce_close sett" onclick="adadifen2(1)"></a>
            <a class="bounce_close linkt" onclick="adadifen2(2)"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend">
                    <span class="partition">
                        活跃指数2——创建已满90天但尚未收过费机构的wonderss月使用指数
                    </span>
                    <span class="partition tagging">
                        注1 以下除扫描者外，其他指标统计对象包含暂为机构在册职工与浏览者
                    </span>
                    <span class="partition tagging">
                        注2 需纳入机构在册职工、浏览者或招聘/调查扫描者以外人员时，另行确定
                    </span>
                    <span class="partition tagging">
                        注3 权重的和需为100%时方可成功
                    </span>
                    <br />
                <table class="ty-table ty-table-control boxn">
                    <thead>
                    <tr>
                        <td>指标</td>
                        <td>权重</td>
                    </tr>
                    </thead>
                    <tbody id="boxpont2">
                    <tr id="pont21"></tr>
                    <tr id="pont22"></tr>
                    <tr id="pont23"></tr>
                    <tr id="pont24"></tr>
                    <tr id="pont25"></tr>
                    <tr id="pont26"></tr>
                    <tr id="pont27">
                    </tr>
                    </tbody>
                </table>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5 sett" onclick="adadifen2(1)">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5 linkt" onclick="adadifen2(2)">关闭</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 botten giveup2"
                        onclick="updanteud2()">提交</button>
            </span>
        </div>
    </div>
    <%--活跃指数2权重设置记录--%>
    <div class="bonceContainer bonce-blue insetred" id="weightsetink2">
        <div class="bonceHead">
            <span>活跃指数2权重设置记录</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend operd">
                    <span class="befon">当前数据未修改</span>
                    <span class="nowon">当前数据为第X次修改后的结果。</span>
                    <span class="timr">修改时间：XXXX-XX-XX XX:XX:XX</span>
                </p>
                <table class="ty-table ty-table-control boxn tb-icn">
                    <thead>
                    <tr>
                        <td>记录</td>
                        <td>操作</td>
                        <td>创建者/修改者</td>
                    </tr>
                    </thead>
                    <tbody id="setink2">
                    <tr>
                        <td>原始信息</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="getsetlink2($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    <tr>
                        <td>第1次修改后</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="getsetlink2($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    <tr>
                        <td>第2次修改后</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue funBtn" onclick="getsetlink2($(this))">查看</span>
                        </td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%--历代负责人--%>
    <div class="bonceContainer bonce-blue insetred" id="Previousleaders2">
        <div class="bonceHead">
            <span>历代负责人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend operd">
                    <span id="salename2">XXXXXXXXXXX(机构名称)的历任销售负责人</span>
                </p>
                <table class="ty-table ty-table-control boxn tb-icn">
                    <thead>
                    <tr>
                        <td>姓名</td>
                        <td>手机号</td>
                        <td>上任的操作记录</td>
                        <td>卸任的操作记录</td>
                    </tr>
                    </thead>
                    <tbody id="preioead2"></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%-- 销售更换记录--%>
     <div class="bonceContainer bounce-green" id="changeSalerRecord" style="width: 800px;">
            <div class="bonceHead">
                <span>销售负责人更换记录</span>
                <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
            </div>
            <div class="bonceCon">
                <table class="ty-table">
                    <tbody></tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-btn-green ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
    <%-- 具体指标设置 --%>
    <div class="bonceContainer bonce-blue boxd" id="spcificnexeting2">
        <div class="bonceHead">
            <span>具体指标设置</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="words pend">
                    <span class="partition" id="Indicator2">
                        每日人均登录次数指标
                    </span>
                    <br />
                    <br />
                    <span class="partition">
                        下表中a、b、c为“算出的值”的上下限，各默认值可修改
                    </span>
                <table class="ty-table ty-table-control boxn">
                    <thead>
                    <tr></tr>
                    </thead>
                    <tbody>
                    <tr id="tb21"></tr>
                    <tr id="tb22"></tr>
                    <tr id="tb23"></tr>
                    </tbody>
                </table>
                <br />
                <span class="partition">
                        下表中，各“算出的值”的范围均需对应指标的值，如必要可修改
                    </span>
                </p>
                <table class="ty-table ty-table-control boxn">
                    <thead>
                    <tr>
                        <td class="count">“算出的值”的范围</td>
                        <td>指标的值</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr id="tb24"></tr>
                    <tr id="tb25"></tr>
                    <tr id="tb26"></tr>
                    <tr id="tb27"></tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-5" onclick="bounce_Fixed2.cancel();bounce_Fixed3.show($('#indicatorsetting2'))">取消</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 botten" id="Submit2"
                        onclick="pointsure2($(this))">提交</button>
            </span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 删除 --%>
    <div class="bonceContainer bounce-green" id="tipConImg" >
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <img src="" id="tipImg" style="width:100%; ">
        </div>
    </div>
    <%-- 删除 --%>
    <div class="bonceContainer bounce-green" id="bankDel" >
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="bankDelOk">确定</span>
        </div>
    </div>
    <%-- 删除 --%>
    <div class="bonceContainer bounce-green" id="quesDel" >
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" class="tip">确定删除本道问题吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="qDelOk">确定</span>
        </div>
    </div>
    <%-- 退出新增问卷 --%>
    <div class="bonceContainer bounce-green" id="backAddBank" >
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" class="tip">退出后，系统将不保留您本次编辑的问卷。<br>确定退出？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="back">确定</span>
        </div>
    </div>

    <%-- 停启用 --%>
    <div class="bonceContainer bounce-green" id="bankStartOrStop" >
        <div class="bonceHead">
            <span>！ 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="bankStartOrStopOk">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-green" id="editQuestion1" >
        <div class="bonceHead">
            <span class="editQuestion1Ttl"></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form autocomplete="off">
                <div class="pannel">
                    <div>
                        <span> 问题内容</span>
                        <span class="lenTip ty-right">0/80</span>
                    </div>
                    <div class="clearInput">
                        <textarea id="qContent" class="form-control" placeholder="" onchange="setWordsNum($(this),80)"></textarea>
                        <i class="fa fa-times clearTextAreaVal" onmousedown="clearPrevVal($(this))"></i>
                    </div>
                </div>
                <div>
                    <p>请选择题型</p>
                    <div class="radioArea">
                        <div class="type1and2">
                            <span data-val="1"><i class="fa fa-circle-o"></i> 常规问答题<span class="bluetip">注：录入框为一个且不可新增、仅可输入文字</span></span>
                            <span data-val="2"><i class="fa fa-circle-o"></i> 正误判断题</span>
                        </div>
                        <div class="type3and4">
                            <span data-val="3"><i class="fa fa-circle-o"></i> 常规单选题</span>
                            <span data-val="4"><i class="fa fa-circle-o"></i> 常规多选题</span>
                        </div>
                        <div class="type5and6">
                            <span data-val="5"><i class="fa fa-circle-o"></i> 几种特殊型式的问答题</span>
                            <span data-val="6" style="color:#aaa; "><i class="fa fa-circle-o" style="color:#aaa; "></i> 需上传附件的题</span>
                        </div>
                        <div class="typenum typenum5">
                            <span data-val="51"><i class="fa fa-circle-o"></i> 录入必须为电子邮箱格式的问答题</span>
                            <span data-val="52"><i class="fa fa-circle-o"></i> 录入必须为11位的手机号的问答题</span>
                            <span data-val="53"><i class="fa fa-circle-o"></i> 可新增录入框的问答题</span>
                        </div>
                        <div class="type7and8">
                            <span data-val="7"><i class="fa fa-circle-o"></i> 国内省、市、地区或地址的题</span>
                            <span data-val="8"><i class="fa fa-circle-o"></i> 年、月或日期的题</span>
                        </div>
                        <div class="typenum typenum7">
                            <p class="bluetip">注：选项中的国内省份均含国内各省、自治区、直辖市及港澳台</p>
                            <span data-val="71"><i class="fa fa-circle-o"></i> 仅可单选，且选项为国内省份</span>
                            <span data-val="72"><i class="fa fa-circle-o"></i> 仅可单选，且选项为国内省份与城市</span>
                            <span data-val="73"><i class="fa fa-circle-o"></i> 仅可单选，且选项为国内省份、城市与地区</span>
                            <span data-val="74"><i class="fa fa-circle-o"></i> 需单选，且选项为国内省份、城市与地区，此外还可录入具体地址</span>
                        </div>
                        <div class="typenum typenum8">
                            <%--<p class="bluetip">注：附件需为图片、word、excel、ppt或pdf型式。且单个不得大于20M，否则不得上传</p>--%>
                            <span data-val="81"><i class="fa fa-circle-o"></i> 选项为年历，答题者需选择为具体哪年的题</span>
                            <span data-val="82"><i class="fa fa-circle-o"></i> 选项为月历，答题者需选择为具体哪年哪月的题</span>
                            <span data-val="83"><i class="fa fa-circle-o"></i> 选项为日历，答题者需选择具体日期的题</span>
                        </div>
                    </div>
                </div>
                <div class="qType">
                    <div class="qType1"></div>
                    <div class="qType2 marTop20">
                        <p>选项默认为“是”、“否”。如需要，可修改。</p>
                        <div class="pannel">
                            <div>
                                <span>选项1</span>
                            </div>
                            <div class="clearInput">
                                <input type="text" class="form-control" placeholder="" />
                                <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                            </div>

                        </div>
                        <div class="pannel">
                            <div>
                                <span>选项2</span>
                            </div>
                            <div class="clearInput">
                                <input type="text" class="form-control" placeholder="" />
                                <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                            </div>
                        </div>
                    </div>
                    <div class="qType3 qType4 marTop20">
                        <p>
                            <span>请录入各选项的内容。如需要，可“增加选项”。</span>
                            <span class="linkBtn ty-right" data-fun="addOption">新增选项</span></p>
                        <div class="optionPannel">
                            <div class="pannel ">
                                <div>
                                    <span>选项</span>
                                    <span class="lenTip ty-right">0/18</span>
                                </div>
                                <div class="clearInput">
                                    <textarea class="form-control" placeholder="请录入，字数上限为80个。" onchange="setWordsNum($(this),80)"></textarea>
                                    <i class="fa fa-times clearTextAreaVal" onmousedown="clearPrevVal($(this))"></i>
                                </div>
                            </div>
                            <div class="pannel ">
                                <div>
                                    <span>选项</span>
                                    <span class="lenTip ty-right">0/18</span>
                                </div>
                                <div class="clearInput">
                                    <textarea class="form-control" placeholder="请录入，字数上限为80个。" onchange="setWordsNum($(this),80)"></textarea>
                                    <i class="fa fa-times clearTextAreaVal" onmousedown="clearPrevVal($(this))"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="saveQuestion">保存</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>活跃指数2</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <!--下面是暂时不需要的部分
            <div class="ty-container">
                <%-- 主页面 --%>
                <div class="mainCon mainCon1">
                    <div class="clear line">
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" data-fun="addBank">新增问卷</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" data-fun="goStop" >已停用的问卷</span>
                        <div class="ty-right searchSect">
                            <div class="ty-left keywordSearch"><span class="ty-left">查找</span>
                                <div class="inputBox ty-right">
                                    <i></i>
                                    <input type="text" id="searchKey" placeholder=" 请输入关键字或素材代号">
                                </div>
                            </div>
                            <button class="ty-left ty-btn ty-btn-blue ty-btn-big" data-fun="searchBtn1">确定</button>
                        </div>
                    </div>
                    <div>
                        <p>发起调查时，可供选用的问卷共以下 <span class="num1"></span> 个</p>
                        <table class="ty-table ty-table-control widthTab">
                            <tr>
                                <td width="60%">问卷标题</td>
                                <td width="20%">创建</td>
                                <td width="20%">操作</td>
                            </tr>

                        </table>
                        <div id="page1"></div>
                        <div id="page1Search"></div>
                    </div>
                </div>

            </div>-->
            <div class="smallt">
                <!--首页-->
                <div class="ty-container" id="home2">
                    <div id="picShow" class="clse">
                        <img src=""/>
                    </div>
                    <div>
                        <div class="main manet">
                            <div class="word catch">以下为创建已满90天但尚未收过费的机构，共<span id="day2">XX</span>个</div>
                            <!-- 筛选部分 -->
                            <span class="catch tencho" style="margin-left: 42px;">筛选</span>
                            <select id="contract1" class="choase" onchange="changemoth(2)" style="width: 123px;">
                                <option value="0">请选择</option>
                                <option value="2020">2020</option>
                                <option value="2021">2021</option>
                                <option value="2022">2022</option>
                                <option value="2023">2023</option>
                                <option value="2024">2024</option>
                                <option value="2025">2025</option>
                                <option value="2026">2026</option>
                                <option value="2027">2027</option>
                                <option value="2028">2028</option>
                                <option value="2029">2029</option>
                            </select>
                            <select id="contract2" class="choase" onchange = "changemoth(2)" style="width: 123px;">
                                <option value="0">请选择</option>
                                <option value="1">1月</option>
                                <option value="2">2月</option>
                                <option value="3">3月</option>
                                <option value="4">4月</option>
                                <option value="5">5月</option>
                                <option value="6">6月</option>
                                <option value="7">7月</option>
                                <option value="8">8月</option>
                                <option value="9">9月</option>
                                <option value="10">10月</option>
                                <option value="11">11月</option>
                                <option value="12">12月</option>
                            </select>
                            <span class="catch serch" style="margin-left: 58px;">搜索</span>
                            <span class="Search" style="width: 200px;">
                                <input id="se02" type="text" placeholder="请输入关键词" />
                               <span class="se" onclick="makesure2(1,20)" id="peachstreat2">确定</span>
                            </span>
                            <div class="pull-right">
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="contractImport2" onclick="getindicator2($(this))" style="padding: 0px 15px;">指标设置</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="contraImpLink2" onclick="getindicaink2($(this))" style="padding: 0px 15px;">指标设置记录</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="addCot2" onclick="getweight2($(this))" style="padding: 0px 15px;">活跃指数2权重设置</span>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="addCotLink" onclick="getweightink2($(this))" style="padding: 0px 15px;">活跃指数2权重设置记录  </span>
                            </div><br>
                            <div class="opinionCon gauge">
                                <table class="ty-table ty-table-control">
                                    <thead class="boad">
                                    <td width="14%">机构名称</td>
                                    <td width="10%">销售负责人</td>
                                    <td width="20%">创建</td>
                                    <td width="11%">创建之日至今的天数</td>
                                    <td width="10%">当前内部用户数</td>
                                    <td width="10%">当前外部用户数</td>
                                    <td width="10%">活跃指数2</td>
                                    <%--                                        <button onclick="pointtwo($(this))">活跃指数2</button></td>--%>
                                    <td width="24%">相关图表</td>
                                    </thead>
                                    <tbody class="sale_Tbody" id="cotManage_body2"></tbody>
                                </table>
                                <div id="ye_Activityindex2"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                    </div>
                </div>
                <!--搜索页-->
                <div class="ty-container clse" id="showcatch2">
                    <div>
                        <button type="button" class="btn btn-default back" id="backst2" onclick="comeckst2()">返回</button>
                        <div class="main">
                            <div class="word catch" id="daystd">符合条件的数据共<span id="daystd2">XX</span>条，具体如下: </div>
                            <div class="opinionCon gauge">
                                <table class="ty-table ty-table-control">
                                    <thead class="boad">
                                    <td width="14%">机构名称</td>
                                    <td width="20%">创建</td>
                                    <td width="11%">创建之日至今的天数</td>
                                    <td width="10%">当前内部用户数</td>
                                    <td width="10%">当前外部用户数</td>
                                    <td width="10%">活跃指数2</td>
                                    <td width="24%">相关图表</td>
                                    </thead>
                                    <tbody class="sale_Tbody" id="cotManage_bodyst2"></tbody>
                                </table>
                                <div id="ye_Activityindexst2"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                    </div>
                </div>
                <!--指数详情页-->
                <div class="ty-container clse" id="indexmore2">
                    <button type="button" class="btn btn-default back" id="back2" onclick="comebck2()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title tatle" id="titaleed21">
                                <span>机构名称:</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table boxb">
                                <thead class="boad">
                                <td width="18%">当前内部用户数</td>
                                <td width="18%">当前外部用户数</td>
                                <td width="18%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="actitytble2"></tbody>
                            </table>
                        </div>
                        <hr class="ht"/>
                        <p class="words">
                            <span class="partition">
                                设：
                            </span>
                            <span>
                                每日登录总次数为a，每日登录总时长为b，每日实际登录人数为c，每日零时在册职工与浏览者人数为d
                            </span>
                            <span class="partition">
                                期间内a的和为A，b的和为B，c的和为C，d的和为D
                            </span>
                            <span class="partition">
                                自创建之日起至查看之日前一日的天数为E
                            </span>
                            <span class="partition">
                                期间招聘/调查扫描者人数为F，从机构创建之日起，创建之日算第1天，第1-4天为一组，及之后每4天算一组，
                                发生招聘/调查扫描的组，招聘/调
                            </span>
                            <span class="partition">查扫描者分布离散值记作1，否则记作0</span>
                            <br />
                            <span class="partition tagging">
                                注1 以下除扫描者外，其它指标统计对象包含暂为机构在册职工与浏览者
                            </span>
                            <span class="partition tagging">
                                注2 需纳入机构在册职工、浏览者或招聘/调查扫描者以外人员时，另行确定
                            </span>
                            <br />
                        </p>
                        <%--<table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>指标</td>
                                <td>算法</td>
                                <td>算出的值</td>
                                <td>指标的值</td>
                                <td>算出的值</td>
                                <td>指标的值</td>
                                <td>算出的值</td>
                                <td>指标的值</td>
                                <td>算出的值</td>
                                <td>指标的值</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>--%>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td width="14%">指标</td>
                                <td width="23%">算法</td>
                                <td width="10%">算出的值</td>
                                <td width="10%">权重</td>
                                <td width="10%">指标的值</td>
                            </tr>
                            </thead>
                            <tbody id="acdexinxt2">
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--图标页(图标1)-->
                <div class="ty-container clse" id="iconpointindext21">
                    <button type="button" class="btn btn-default back" id="backindt21" onclick="comebckt21()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge tatle" id="titaleindt21">
                                <span>机构名称:</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activityindt21"></tbody>
                            </table>
                        </div>
                        <hr class="ht"/>
                        <%--下面是图表内容--%>
                        <div id="outerboxindt21">
                            <div id="tumisuindt21">
                                <div>
                                    <%--描述文字--%>
                                    <div id="miteindt21" class="write detxt"></div>
                                    <!-- 图表1 -->
                                    <p id="misuindt21"></p>
                                    <div id="mainindt21" class="pictune"></div>
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contractindt21" class="choase form-control" onchange="chaneTime(2,1)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contractindt22" class="choase form-control" onchange="chaneTime(2,1)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 批注
                        <div id="comments1" style="display: inline-block;position: absolute;margin-left: -380px;">
                            <div>
                                <span>图中</span>
                                <span style="margin-left: 10px;">每日零时在册职工与浏览者人数d展示于白底色框中</span>
                            </div>
                            <span style="margin-left: 38px;">每日实际登录人数c展示于橙底色框中</span>
                            <span style="display: block;margin-left: 38px;">白底色框中的百分比为每日的登录比</span>
                        </div>-->
                    </div>
                </div>
                <!--图表页(图标2)-->
                <div class="ty-container clse" id="iconpointindex22">
                    <button type="button" class="btn btn-default back" id="backind22" onclick="comebckt22()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title tat" id="titaleind22">
                                <span>机构名称</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activityind22">
                                </tbody>
                            </table>
                        </div>
                        <hr class="ht"/>
                        <%--                        <div class="link"></div>--%>
                        <!--下面是图表内容-->
                        <div id="outerboxt2">
                            <div id="tumisut2">
                                <!--描述文字-->
                                <div id="mitet2" class="write">
                                    <%--                                    <p><span>期初外部用户数  XXX</span></p>--%>
                                    <%--                                    <p><span>各框最上端数字代表该日实际的外部用户数</span></p>--%>
                                    <%--                                    <p><span>橙底色里的数字代表期初基础上增加的人数</span></p>--%>
                                    <%--                                    <p><span>绿底色里的数字代表期初基础上减少的人数</span></p>--%>
                                </div>
                                <!--图表2-->
                                <p id="misut2"></p>
                                <div id="maint2" class="pictune"></div>
                                <!--筛选-->
                                <div class="choose dressy">
                                    <select id="contractt21" class="choase form-control" onchange="chaneTime(2,2)">
                                        <option value="2020">2020</option>
                                        <option value="2021">2021</option>
                                        <option value="2022">2022</option>
                                        <option value="2023">2023</option>
                                        <option value="2024">2024</option>
                                        <option value="2025">2025</option>
                                        <option value="2026">2026</option>
                                        <option value="2027">2027</option>
                                        <option value="2028">2028</option>
                                        <option value="2029">2029</option>
                                    </select>
                                    <select id="contractt22" class="choase form-control" onchange="chaneTime(2,2)">
                                        <option value="1">1月</option>
                                        <option value="2">2月</option>
                                        <option value="3">3月</option>
                                        <option value="4">4月</option>
                                        <option value="5">5月</option>
                                        <option value="6">6月</option>
                                        <option value="7">7月</option>
                                        <option value="8">8月</option>
                                        <option value="9">9月</option>
                                        <option value="10">10月</option>
                                        <option value="11">11月</option>
                                        <option value="12">12月</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标3)-->
                <div class="ty-container clse" id="iconpointtin3">
                    <button type="button" class="btn btn-default back" id="backt32" onclick="comebckt32()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge" id="titalet32">
                                <span>机构名称</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activityt32">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr class="ht"/>
                        <%--                        <div class="link"></div>--%>
                        <!--下面是图表内容-->
                        <div id="outerboxt3">
                            <div id="tumisut3">
                                <div>
                                    <!--描述文字-->
                                    <div id="mitet3" class="write">
                                        <%--                                        <p><span class="ttl">期间</span><span>登录总次数 XXX</span></p>--%>
                                        <%--                                        <p><span class="ttl"></span><span>日人均登录XXX次</span></p>--%>
                                        <%--                                        <p><span class="ttl"></span><span>登录者日人均登录XXX次</span></p>--%>
                                    </div>
                                    <!--图表3-->
                                    <p id="misut3"></p>
                                    <div id="maint3" class="pictune"></div>
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contractt31" class="choase form-control" onchange="chaneTime(2,3)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contractt32" class="choase form-control" onchange="chaneTime(2,3)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标4)-->
                <div class="ty-container clse" id="iconpointtin4">
                    <button type="button" class="btn btn-default back" id="backt42" onclick="comebckt42()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge" id="titalet42">
                                <span>机构名称</span><span>XXXXX</span>
                                <span>创建</span><span>XXX  XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activityt42">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr class="ht"/>
                        <%--                        <div class="link"></div>--%>
                        <!--下面是图表内容-->
                        <div id="outerboxt4">
                            <div id="tumisut4">
                                <div>
                                    <!--描述文字-->
                                    <div id="mitet4" class="write">
                                        <%--                                        <p><span class="ttl">期间</span><span>登录总时长 XXX</span></p>--%>
                                        <%--                                        <p><span class="ttl"></span><span>日人均登录XXX时长</span></p>--%>
                                        <%--                                        <p><span class="ttl"></span><span>登录者人均登录XXX时长</span></p>--%>
                                    </div>
                                    <!--图表4-->
                                    <div id="maint4" class="pictune"></div>
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contractt41" class="choase form-control" onchange="chaneTime(2,4)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contractt42" class="choase form-control" onchange="chaneTime(2,4)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标5)-->
                <div class="ty-container clse" id="iconpointtin5">
                    <button type="button" class="btn btn-default back" id="backt52" onclick="comebckt52()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge" id="titalet52">
                                <span>机构名称</span><span>XXXXX</span>
                                <span>创建</span><span>XXX  XXXX-XX-XX  XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activityt52">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr class="ht"/>
                        <%--                        <div class="link"></div>--%>
                        <!--下面是图表内容-->
                        <div id="outerboxt5">
                            <div id="tumisut5">
                                <div>
                                    <!--描述文字-->
                                    <div id="mitet5" class="write">
                                    </div>
                                    <!--图表5-->
                                    <div id="maint5" class="pictune"></div>
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contractt51" class="choase form-control" onchange="chaneTime(2,5)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contractt52" class="choase form-control" onchange="chaneTime(2,5)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--图表页(图标6)-->
                <div class="ty-container clse" id="iconpointtin6">
                    <button type="button" class="btn btn-default back" id="backt62" onclick="comebckt62()">返回</button>
                    <div class="pageStyle container_item">
                        <div class="initBody">
                            <div class="title gauge" id="titalet62">
                                <span>机构名称:</span><span>XXXXX</span>
                                <span>创建</span><span>XXX XXXX-XX-XX XX:XX:XX</span>
                            </div>
                            <table class="ty-table">
                                <thead class="boad">
                                <td width="30%">当前内部用户数</td>
                                <td width="30%">当前外部用户数</td>
                                <td width="40%">创建之日至今的天数</td>
                                </thead>
                                <tbody class="ty-table" id="activityt62">
                                <td>12</td>
                                <td>12</td>
                                <td>23</td>
                                </tbody>
                            </table>
                        </div>
                        <hr class="ht"/>
                        <!--下面是图表内容-->
                        <div id="outerboxt6">
                            <div id="tumisut6">
                                <div>
                                    <!--描述文字-->
                                    <div id="mite6" class="write detxt"></div>
                                    <%--                                        <p><span class="ttl">期间</span><span>登录总次数 XXX</span></p>--%>
                                    <!--图表6-->
                                    <div id="maint6" class="pictune"></div>
                                    <!--筛选-->
                                    <div class="choose dressy">
                                        <select id="contractt61" class="choase form-control" onchange="chaneTime(2,6)">
                                            <option value="2020">2020</option>
                                            <option value="2021">2021</option>
                                            <option value="2022">2022</option>
                                            <option value="2023">2023</option>
                                            <option value="2024">2024</option>
                                            <option value="2025">2025</option>
                                            <option value="2026">2026</option>
                                            <option value="2027">2027</option>
                                            <option value="2028">2028</option>
                                            <option value="2029">2029</option>
                                        </select>
                                        <select id="contractt62" class="choase form-control" onchange="chaneTime(2,6)">
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/activityIndex/activityIndex.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/common/echarts.common.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    $(function(){
        //获取活跃指数2首页数据列表
        startact2();
    })
</script>
</body>
</html>
