<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/wenjian.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/event/event.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/event/icon/iconfont.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%-- 一弹窗 --%>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align:center; font-size:16px; margin-top: 20px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

    <div class="bonceContainer bounce-blue" id="collect">
        <div class="bonceHead">
            <span class="bounce_title">收藏</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">
                <div>
                    <div>请选择收藏夹。</div>
                    <div>带有“>”符号的，含有子收藏夹，需点击展开。</div>
                </div>
                <div class="btn-group text-right">
                    <button class="ty-btn ty-btn-blue ty-circle-2" onclick="newFolderBtn('col')">新建收藏夹</button>
                </div>
            </div>
            <div id="collect_tree">
                <div class="ty-colFolderTree"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" id="sureCollectBtn">确定</button>
        </div>
    </div>

    <%--历史记录--%>
    <div class="bonceContainer bounce-blue" id="historyRecord" style="width: 1000px">
        <div class="bonceHead">
            <span>历史记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 500px; overflow-y: auto">
            <p>所设置的日程</p>
            <table class="kj-table schedule">
                <thead>
                <tr>
                    <th>设置的提醒时间</th>
                    <th>内容</th>
                    <th>创建日期</th>
                    <th>类型</th>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div class="ty-alert">
                该日程的历史记录
                <div class="btn-group text-right">
                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="delAllHistoryBtn" onclick="delAllHistoryBtn()">删除全部历史记录</button>
                </div>
            </div>
            <table class="kj-table noSide historyList">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>提醒时间</th>
                    <th></th>
                    <th>状态</th>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--修改日程--%>
    <div class="bonceContainer bounce-green" id="updateEvent">
        <div class="bonceHead">
            <span class="titleUpdate">日程修改</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon">
            <form class="ty-form" id="form_updateEvent" style="min-width: 320px">
                <div class="formItem">
                    <span>描述</span>
                    <div><textarea cols="30" rows="2" data-type="textarea" name="description" placeholder="请在此录入内容" onkeyup="countWords($(this),1000)"></textarea></div>
                    <div class="textMax text-right">0/1000</div>
                </div>
                <div class="formItem">
                    <span>需要设置提醒吗</span>
                    <input type="radio" data-type="radio" name="notify" value="true" disabled> 需要
                    <input type="radio" data-type="radio" name="notify" value="false" disabled> 不需要
                    <input type="hidden" name="notifyReady" value="false" />
                    <div class="friendlyTip">注：您可到【备忘事项】中管理不需要提醒的事件。</div>
                </div>
                <div class="trans_notify" style="display: none;">
                    <div class="formItem">
                        <span>重复周期</span>
                        <select data-type="select" name="freqType" style="width:300px;">
                            <option value="1">不重复</option>
                            <option value="4">每日</option>
                            <option value="8">每周</option>
                            <option value="16">每月</option>
                            <option value="32">每年</option>
                        </select>
                    </div>
                    <div class="formItem">
                        <div>
                            <span>从何时开始提醒</span>
                            <input type="text" data-type="date" id="activeStartDate_updateEvent" style="width: 150px" require>
                            <input type="text" data-type="date" id="activeStartDate_updateEvent1" style="width: 150px;display: none;" placeholder="请选择月份" require>
                            <select data-type="select" id="activeStartTime_updateEvent" style="width:100px" require>
                                <option value="">0:00</option>
                            </select>
                        </div>
                        <div class="trans16" style="text-align: right; display: none">
                            <input type="checkbox" data-type="singleCheck" name="special"  style="vertical-align: bottom"/>
                            每月倒数第
                            <select type="text" data-type="num" name="specialInterval" style="width: 100px" require>
                                <option value="">请选择</option>
                                <option value="1">1天</option>
                                <option value="2">2天</option>
                                <option value="3">3天</option>
                            </select>
                        </div>
                    </div>
                    <div class="formItem tip">
                        <div class="ty-color-blue ty-tips">系统将于2017年7月20日11:00提醒</div>
                    </div>
                </div>
                <div class="formItem">
                    <span>此处可上传照片</span>
                    <div class="uploadImgItem updateImg clear" data-type="uploadImg">
                        <div id="uploadImgSect_updateEvent"></div>
                    </div>
                </div>
                <div class="formItem">
                    <span>标题</span>
                    <input type="text" data-type="text" name="title" value="" placeholder="标题将由系统将按描述自动生成，如必要您可自行修改" style="padding-right: 20px;"/>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2 sure" onclick="bounce_Fixed.show($('#updateEventLastSure'))">确定</button>
        </div>
    </div>
    <%--修改记录--%>
    <div class="bonceContainer bounce-blue" id="updateRecord" style="min-width:500px;">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:450px;overflow-y: auto;">
            <div class="clear noneUpdated">
                <span class="ty-left">当前资料未经修改。</span>
                <span class="ty-right"></span>
            </div>
            <div class="">
                <p>当前资料为第<span class="eidtNum"></span>次修改后的结果。</p>
                <p class="updaterInfo" style="text-align: right"> </p>
                <table class="kj-table noSide updateRecordList">
                    <tr>
                        <td>资料状态</td>
                        <td>操作</td>
                        <td>创建人/修改人</td>
                    </tr>
                </table>
            </div>
            <div id="editRecordPage"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%--分享管理--%>
    <div class="bonceContainer bounce-blue" id="shareManage" style="min-width:500px;">
        <div class="bonceHead">
            <span>分享管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
           <table class="kj-table shareManageList">
               <thead>
               <tr>
                   <th>分享时间</th>
                   <th>接收人</th>
               </tr>
               </thead>
               <tbody></tbody>
           </table>
            <div id="ye_shareManage"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
</div>
<%-- 二级弹窗 --%>
<div class="bounce_Fixed">
    <%--confirm--%>
    <div class="bonceContainer bounce-blue" id="fixed_confirm">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 iknowBtn">确定</button>
        </div>
    </div>
    <%--tip--%>
    <div class="bonceContainer bounce-blue" id="fixed_tip">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn">确定</button>
        </div>
    </div>
    <%--查看日程--%>
    <div class="bonceContainer bounce-blue" id="seeEvent" style="min-width:530px;">
        <div class="bonceHead">
            <span class="titleTip">日程查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-form">
                <div class="formItem additional">
                </div>
                <div class="formItem noticeData">
                    <div class="form_label text-justify">重复周期</div>
                    <div class="form_content small repetitive">不重复</div>
                    <div class="form_other">从何时开始提醒</div>
                    <div class="form_content mid activeStartDate">2019/6/20 16:00</div>
                </div>
                <div class="formItem">
                    <div class="form_label text-justify">标题</div>
                    <span class="form_content title"></span>
                </div>
                <div class="formItem">
                    <div class="form_label text-justify">描述</div>
                    <pre class="form_content description"></pre>
                </div>
                <div class="formItem">
                    <div class="form_label text-justify">照片</div>
                    <span class="form_content images">
                    </span>
                </div>
                <div class="formItem eventInfo ty-color-blue"></div>
                <div class="supplyList"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>

    <%--不再提醒--%>
    <div class="bonceContainer bounce-red" id="noRemind">
        <div class="bonceHead">
            <span>不再提醒</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="noRemindContent"  style="text-align: center; width: 380px; margin: auto">您确定本日程不再需要提醒吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-2" data-name="noRemind">确定</span>
        </div>
    </div>

    <%--补充记录--%>
    <div class="bonceContainer bounce-blue" id="extraRecord">
        <div class="bonceHead">
            <span>补充记录</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon">
            <p class="ty-tips">描述</p>
            <div><textarea name="" cols="30" rows="2" style="width: 100%;" class="supplement" onkeyup="countWords($(this),1000)" placeholder="请在此录入内容"></textarea></div>
            <div class="textMax text-right">0/1000</div>
            <span>此处可上传照片</span>
            <div class="uploadImgItem supplyUploadImg clear">
                <div id="uploadImgSupply"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 extraRecordBtn node" data-name="extraRecord" disabled>确定</button>
        </div>
    </div>

    <%--删除日程--%>
    <div class="bonceContainer bounce-red" id="delEvent">
        <div class="bonceHead">
            <span>删除</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="delContent" style="text-align: center; width: 380px; margin: auto">
                <p>删除后，此日程将不再显示于您的日程中，将不再给予提醒，同时 <span class="ty-color-red">此日程的所有历史记录也将同时删除。</span></p>
                <p><span class="ty-color-red">确定删除？</span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-red ty-circle-2" data-name="delEvent">确定</button>
        </div>
    </div>

    <%--修改日程确定--%>
    <div class="bonceContainer bounce-green" id="updateEventLastSure">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="updateContent" style="text-align: center; width: 380px; margin: auto">
                <p>修改后，您还可在修改记录中看到修改前内容。</p>
                <p>确定修改吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2" id="updateEventSure" data-name="updateEvent">确定</button>
        </div>
    </div>

    <%--修改记录日程查看--%>
    <div class="bonceContainer bounce-blue" id="updateHisSee" style="min-width:530px;">
        <div class="bonceHead">
            <span>原始信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-form">
                <div class="formItem">
                    <div class="form_label text-justify">重复周期</div>
                    <div class="form_content small">不重复</div>
                    <div class="form_other">从何时开始提醒</div>
                    <div class="form_content mid">2019/6/20 16:00</div>
                </div>
                <div class="formItem">
                    <div class="form_label text-justify">标题</div>
                    <span class="form_content title"></span>
                </div>
                <div class="formItem">
                    <div class="form_label text-justify">描述</div>
                    <pre class="form_content description"></pre>
                </div>
                <div class="formItem">
                    <div class="form_label text-justify">照片</div>
                    <span class="form_content">1、 2</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-color-blue" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>

    <%--新建收藏夹--%>
    <div class="bonceContainer bounce-blue" id="newFolder" style="width: 400px">
        <div class="bonceHead">
            <span>新建收藏夹</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 250px;margin: 0 auto">
                <div>在【<span class="folderName"></span>】下新建子收藏夹</div>
                <div class="ty-alert" style="margin: 0">子收藏夹名称</div>
                <input class="ty-inputText" type="text" placeholder="请录入名称" style="width: 100%">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="sureNewFolder()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="editFavoriteFolder">
        <div class="bonceHead">
            <span>修改收藏夹的名称</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 250px;margin: 0 auto">
                <div class="ty-alert" style="margin: 0">请录入收藏夹的新名称</div>
                <input class="ty-inputText" type="text" placeholder="请录入名称" style="width: 100%">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="sureChangeFolder()">确定</span>
        </div>
    </div>

</div>
<%-- 三级弹窗 --%>
<div class="bounce_Fixed2">
    <%--confirm--%>
    <div class="bonceContainer bounce-blue" id="fixed2_confirm">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 iknowBtn">我知道了</button>
        </div>
    </div>
    <%--tip--%>
    <div class="bonceContainer bounce-blue" id="fixed2_tip">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn">确定</button>
        </div>
    </div>
    <%--新增日程--%>
    <div class="bonceContainer bounce-green" id="newEvent" style="width:550px;">
        <div class="bonceHead">
            <span class="new_title">新增备忘或日程</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon">
            <form class="ty-form" id="form_newEvent" style="min-width: 320px">
                <div class="formItem">
                    <span>描述</span>
                    <div>
                        <textarea cols="30" rows="8" data-type="textarea" name="description" placeholder="请在此录入内容" class="autoFill" max="1000"></textarea>
                    </div>
                    <div class="textMax text-right">0/1000</div>
                </div>
                <div class="formItem">
                    <span>需要设置提醒吗</span>
                    <input type="radio" data-type="text" name="notify" value="1"> 需要
                    <input type="radio" data-type="text" name="notify" value="0"> 不需要
                    <div class="friendlyTip">注：您可到【备忘事项】中管理不需要提醒的事件。</div>
                </div>
                <div class="trans_notify" style="display: none;">
                    <div class="formItem">
                        <span>重复周期</span>
                        <select data-type="select" name="freqType" style="width:300px;">
                            <option value="1">不重复</option>
                            <option value="4">每日</option>
                            <option value="8">每周</option>
                            <option value="16">每月</option>
                            <option value="32">每年</option>
                        </select>
                    </div>
                    <div class="formItem">
                        <div>
                            <span>从何时开始提醒</span>
                            <input type="text" data-type="date" id="activeStartDate" style="width: 150px" placeholder="请选择日期" require>
                            <input type="text" data-type="date" id="activeStartDate1" style="width: 150px" placeholder="请选择月份" require>
                            <select data-type="select" id="activeStartTime" style="width:100px" require>
                            <option value="">0:00</option>
                            </select>
                        </div>
                        <div class="trans16" style="text-align: right; display: none; margin-top: 10px;">
                            <input type="checkbox" data-type="singleCheck" name="special"/>
                            每月倒数第
                            <select type="text" data-type="num" name="specialInterval" style="width: 100px" require>
                                <option value="">请选择</option>
                                <option value="1">1天</option>
                                <option value="2">2天</option>
                                <option value="3">3天</option>
                            </select>
                        </div>
                    </div>
                    <div class="formItem tip">
                        <div class="ty-color-blue ty-tips">系统将于2017年7月20日11:00提醒</div>
                    </div>
                </div>
                <div class="formItem">
                    <span class="uploadImgTip">此处可上传照片</span>
                    <div class="uploadImgItem uploadImgAdd clear" data-type="addImg">
                        <div id="uploadImgSect"></div>
                    </div>
                </div>
                <div class="formItem">
                    <span>标题</span>
                    <div class="combinSect">
                        <input data-open="1" type="text" data-type="text" name="title" placeholder="标题将由系统将按描述自动生成，如必要您可自行修改" style="padding-right: 20px;" />
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="formItem">
                    <span>分享</span>
                    <div class="combinSect">
                        <div class="share_avatar_row">
                            <div class="shareChooseList"></div>
                            <span class="ty-btn ty-btn-green ty-circle-2" onclick="addRole()">请选择</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2" id="sureNewEvent" data-name="newEvent">确定</button>
        </div>
    </div>
    <%--删除补充记录--%>
    <div class="bonceContainer bounce-red" id="delExtraRecord">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center; width: 380px; margin: auto">
                <p><span class="ty-color-red">删除后，本条补充记录将不再可见。</span></p>
                <p><span class="ty-color-red">确定删除？</span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-red ty-circle-2" data-name="delSupplySure">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%--选择分享人--%>
    <div class="bonceContainer bounce-green" id="addRole">
        <div class="bonceHead">
            <span>选择分享人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <span>请选择分享人</span>
            <table class="kj-table">
                <thead>
                    <tr>
                        <th></th>
                        <th>姓名</th>
                        <th>手机号</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed3.cancel()">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-2 sureBtn">确定</button>
        </div>
    </div>
</div>
<div class="float">
    <div class="share_avatar">
        <div class="share_head">请选择分享对象（可多选）</div>
        <ul class="sharePerson">

        </ul>
        <div class="share_foot">
            <button class="ty-btn ty-btn-blue ty-circle-2" onclick="sureShare()">确定</button>
        </div>
    </div>
    <div class="more_avatar">

    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>事件管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container" >
                <div id="picShow" style="display: none;">
                    <img src=""/>
                </div>
                <div>
                    <div class="mainBtnGroup eventHandle" style="overflow: hidden">
                        <div class="ty-alert">
                            您的日程安排如下：<button class="ty-btn ty-btn-green ty-btn-big ty-circle-2" id="newEventBtn" onclick="newEventBtn()" style="margin: 0 8px">新增</button>
                            <div class="ty-search">
                                <input placeholder="请输入您要查询内容的关键字" id="searchByKey" />
                                <i class="fa fa-search" other data-name="searchByKey"></i>
                            </div>
                            <div class="btn-group text-right">
                                <button class="ty-btn ty-btn-blue ty-btn-big" other id="myShare" data-name="eventShare">我的分享</button>
                                <button class="ty-btn ty-btn-blue ty-btn-big" other id="memoMatters" data-name="eventMemo">备忘事项</button>
                                <button class="ty-btn ty-btn-blue ty-btn-big" other id="historical" data-name="seeFutureOrHistory">历史日程</button>
                            </div>
                        </div>
                    </div>
                    <div class="otherBtn" style="display: none">
                        <div class="shareBtnGroup">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" other data-name="goback">返回</span>
                        </div>
                        <div class="memoBtnGroup">
                            <div class="ty-alert">
                                <div>
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" other data-name="goback">返回</button>
                                    <button class="ty-btn ty-btn-green ty-btn-big ty-circle-2" data-name="newMemo" other id="newMemoBtn">新增</button>
                                </div>
                                <div class="btn-group text-right">
                                    <div class="ty-search">
                                        <input placeholder="请输入您要查询内容的关键字" class="memoSearchKey" name="memo"/>
                                        <i class="fa fa-search" other data-name="memoSearchByKey"></i>
                                    </div>
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" data-name="memoCollectors" other id="memoCollectors">备忘收藏夹</button>
                                </div>
                            </div>
                        </div>
                        <div class="favoritesBtnGroup">
                            <div class="ty-alert">
                                <div>
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" other data-name="goback">返回备忘与日程主页</button>
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" other data-name="backToPrev">返回上一页</button>
                                    <button class="ty-btn ty-btn-green ty-btn-big ty-circle-2" data-name="newFavorite" other id="newFavorite">新建收藏夹</button>
                                </div>
                                <div class="btn-group text-right">
                                    <div class="ty-search">
                                        <input placeholder="请输入您要查询内容的关键字" class="memoSearchKey" name="favorites"/>
                                        <i class="fa fa-search" other data-name="memoSearchByKey"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="historyBtnGroup">
                            <div class="ty-alert">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" other data-name="goback">返回</span>
                                <div class="btn-group text-right">
                                    <div class="ty-search">
                                        <input placeholder="请输入您要查询内容的关键字" id="hisSearchKey"/>
                                        <i class="fa fa-search" other data-name="hisSearchByKey"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="searchBtn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" search onclick="gobackOther()">返回</span>
                        </div>
                    </div>
                </div>
                <div class="applyList">
                    <div list data-name="repeatSchedule" class="repeatSchedule">
                        <table class="kj-table noSide list">
                            <thead>
                            <tr>
                                <th>序号</th>
                                <th>设置的提醒时间</th>
                                <th>标题</th>
                                <th></th>
                                <th>创建人</th>
                                <th>类型</th>
                                <th>状态</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_repeatSchedule"></div>
                    </div>
                    <%--我的分享--%>
                    <div list data-name="myShare" class="myShare" style="display: none">
                        <ul class="ty-secondTab">
                            <li class="ty-active">发出的分享</li>
                            <li>接收的分享</li>
                        </ul>
                        <div class="tblContainer">
                            <table class="kj-table noSide shareSend">
                                <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>标题</th>
                                    <th>创建人</th>
                                    <th>分享时间</th>
                                    <th>接收人</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            <table class="kj-table noSide shareReceive" style="display: none">
                                <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>标题</th>
                                    <th>创建人</th>
                                    <th>接收时间</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            <div id="ye_share"></div>
                        </div>
                    </div>
                    <%--备忘事项--%>
                    <div list data-name="memoMatters" class="memoMatters" style="display: none">
                        <div class="ty-alert">
                            以下为您常用的备忘事项。不常用的，您可收到本页的备忘收藏夹里。
                        </div>
                        <table class="kj-table noSide memoMattersList list">
                            <thead>
                            <tr>
                                <th>序号</th>
                                <th>标题</th>
                                <th></th>
                                <th>创建时间/修改时间</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <div id="ye_eventMemo"></div>
                    </div>
                    <%--备忘收藏夹--%>
                    <div list data-name="memoFavorites" class="memoFavorites" style="display: none">
                        <div class="folderRecordList">
                            <div class="ty-alert">
                                以下为您所建立的收藏夹。您可修改收藏夹的名称，也可取出某备忘事项，之后将其再收藏至其他收藏夹。
                            </div>
                        </div>
                        <div class="memoRecord" style="display:none;">
                            <div class="ty-alert">
                                以下为【<span class="folderName"></span>】下的备忘事项。
                            </div>
                            <table class="kj-table noSide">
                                <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>标题</th>
                                    <th></th>
                                    <th>创建时间/修改时间</th>
                                </tr>
                                </thead>
                                <tbody >

                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div list data-name="historyList" class="historyList" style="display: none">
                        <table class="kj-table noSide">
                            <thead>
                            <tr>
                                <th>序号</th>
                                <th>设置的提醒时间</th>
                                <th>标题</th>
                                <th></th>
                                <th>创建人</th>
                                <th>类型</th>
                                <th>状态</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <div id="ye_eventHistory"></div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/memo/jquery.tablesort.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/memo/event.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
