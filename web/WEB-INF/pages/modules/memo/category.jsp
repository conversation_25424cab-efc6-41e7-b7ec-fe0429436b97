<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
    .ty-panel{  background: none;    }
    .ty-colFileTree{ float: left ; max-height:700px; width:310px;    }
    .mar{ margin-left: 330px;  }
    .fa-folder+span{ width:100px; display:inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
    #scanSet { width:760px;  }
    #allRight .ty-gray , #nowRight .ty-gray { color:#aaa; }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%--  update--%>
    <div class="bonceContainer bounce-blue bounce-changeClass">
        <div class="bonceHead">
            <span>修改类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            修改名称：<input type="text" class="ty-inputText categoryName">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeClass()">确定</span>
        </div>
    </div>
    <%-- delete --%>
    <div class="bonceContainer bounce-red bounce-deleteClass">
        <div class="bonceHead">
            <span>删除类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>是否删除此类别？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn ty-btn-red ty-btn-big" onclick="sureDeleteClass()">确定</span>
        </div>
    </div>
    <%-- 新增子级类别 --%>
    <div class="bonceContainer bounce-green bounce-newClass">
        <div class="bonceHead">
            <span>新增子级类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            名称：<input type="text" class="ty-inputText categoryName">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewClass()">确定</span>
        </div>
    </div>
    <%-- 新增同级类别 --%>
    <div class="bonceContainer bounce-green bounce-newSameClass">
        <div class="bonceHead">
            <span>新增同级类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            名称：<input type="text" class="ty-inputText categoryName">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewSameClass()">确定</span>
        </div>
    </div>
    <%-- 查看设置 --%>
    <div class="bonceContainer bounce-blue" id="scanSet">
        <div class="bonceHead">
            <span>查看设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="ty-alert">请选择可使用该类别文件的人员</p>
            <div class="departTree">
                <ul class="ty-left" id="allRight"> </ul>
                <div class="ty-left arrow"><i class="fa fa-exchange"></i></div>
                <form class="ty-left" id="deparCon">
                    <input type="hidden" name="categoryId" id="categoryId">
                    <ul id="nowRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="sureChangeRight">确定</button>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p id="tipMess"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>类别设置</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" style="min-width: 1206px">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container clearfix">
                <div class="ty-fileContent">
                    <%--竖型文件树容器--%>
                    <div class="ty-colFileTree"></div>
                    <div class="ty-mainData mar">
                        <%--此类别信息--%>
                        <div class="ty-panel">
                            <div class="ty-panelHeader nowFolder">
                                <h3 class="ty-left"></h3>
                                <div class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right " id="addBtn" style="margin-top:9px" onclick="newSameClass()">新增同级类别</div>
                            </div>
                            <table class="ty-table ty-table-control CategoryMessage">
                                <thead>
                                <tr>
                                    <td width="15%">创建日期</td>
                                    <td width="15%">修改日期</td>
                                    <td width="15%">修改名称</td>
                                    <td width="15%">删除</td>
                                    <td width="15%">操作</td>
                                    <td width="25%">新增子级类别</td>
                                </tr>
                                </thead>
                                <%--此类别容器--%>
                                <tbody></tbody>
                            </table>
                        </div>
                        <%--子类别信息--%>
                        <div class="ty-panel" style="margin-top:130px">
                            <div class="ty-panelHeader childFolder">
                                <h3>子类别</h3>
                            </div>
                            <table class="ty-table ty-table-control cCategoryMessage">
                                <thead>
                                <tr>
                                    <td width="60%">类别名称</td>
                                    <td width="20%">创建日期</td>
                                    <td width="20%">修改日期</td>
                                </tr>
                                </thead>
                                <%--子类别容器--%>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/gonggao.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    if(generalType != 1 && generalType != 2){
        $("#addBtn").hide(); $("#sureChangeRight").hide();
    }
</script>
</body>
</html>
