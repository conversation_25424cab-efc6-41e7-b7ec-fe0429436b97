<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/docManage.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
     <%--  update--%>
         <%--移动弹窗--%>
     <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 650px">
         <div class="bonceHead">
             <span class="bounce_title">移动到</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <div class="folder_list ty-colFileTree" name="chooseFolder"></div>
         </div>
         <div class="bonceFoot">
             <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" id="sureChooseFolderBtn" onclick="sureMoverFolder()">确定</button>
         </div>
     </div>
     <div class="bonceContainer bounce-blue bounce-changeClass">
         <div class="bonceHead">
             <span>修改文件夹名称</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <p>将文件夹【<span class="folderNameChange"></span>】的名称修改为</p>
             <p class="text-center"><input type="text" class="ty-inputText categoryName" placeholder="vbxfg" style="width: 374px;"></p>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeClass()">确定</span>
         </div>
     </div>
     <%-- delete --%>
     <div class="bonceContainer bounce-red bounce-deleteClass">
         <div class="bonceHead">
             <span>删除文件夹</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon text-center">
             <p>确定删除该文件夹？</p>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-red ty-btn-big" onclick="sureDeleteClass()">确定</span>
         </div>
     </div>
     <%-- 新增子级类别 --%>
     <div class="bonceContainer bounce-green bounce-newClass">
         <div class="bonceHead">
             <span>新建子文件夹</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <p>在【<span class="recentParentFolder"></span>】下新建子文件夹</p>
             <p>子文件夹名称</p>
             <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
<%--             <p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。--%>
<%--                <span class="ty-right btnLink" onclick="scanSetBtn('son')">去设置使用权限</span>--%>
<%--             </p>--%>
             <span class="hd" id="havRightUserCon"></span>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewClass()">确定</span>
         </div>
     </div>
     <%-- 全部文件下新增文件夹 --%>
     <div class="bonceContainer bounce-green bounce-newSameClass">
         <div class="bonceHead">
             <span>新建子文件夹</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <p>在【全部文件】下新建子文件夹</p>
             <p>子文件夹名称</p>
             <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
<%--             <p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。--%>
<%--                 <span class="ty-right btnLink" onclick="scanSetBtn('same')">去设置使用权限</span>--%>
<%--             </p>--%>
             <span class="hd" id="havRightUserCon_same"></span>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewSameClass()">确定</span>
         </div>
     </div>
     <%-- 使用权限设置 --%>
     <div class="bonceContainer bounce-blue" id="scanSet">
         <div class="bonceHead">
             <span>使用权限设置</span>
             <a class="bounce_close" onclick="cancelChangeRight()"></a>
         </div>
         <div class="bonceCon ">
             <input type="hidden" id="isNew">
             <p>文件夹名称：<span class="packName">一级文件夹</span></p>
             <p class="">
                 请选择需使用本文件夹的职工<span style="margin-left:100px; " class="btnLink" data-type="allSelect">全选</span>
                <span class="ty-right txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    已选职工：<span class="selectedNum">20</span>位
                    <span class="btnLink" data-type="allClear">全部清空</span>
                </span>
             </p>
             <div class="departTree ">
                 <ul class="ty-left">
                     <p class="txtR"><span class="btnLink" id="changeTypeLeft" onclick="changeType($(this), 'left')">直接展示全部职工</span></p>
                     <div id="allRight"></div>
                 </ul>
                 <div class="ty-left arrow"><i class="fa fa-exchange"></i></div>
                 <form class="ty-left" id="deparCon">
                     <input type="hidden" name="categoryId" id="categoryId">
                     <p class="txtR"><span class="btnLink" id="changeTypeRight" onclick="changeType($(this), 'right')">切换为按部门展示</span></p>
                     <ul id="nowRight"></ul>
                 </form>
                 <div class="clr"></div>
             </div>
             <p>&nbsp;
                 <span class="cancelCon">以下<span id="cancelNum">22</span>位职工刚被取消了本文件夹的使用权限。</span>
                 <span class="ty-right getCon txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    以下<span id="getNum"></span>位职工刚获得了本文件夹的使用权限。
                </span>
             </p>
             <div class="departTree changeCon">
                 <ul class="ty-left cancelCon" style="height:150px;">
                     <div id="cancelRight"></div>
                 </ul>
                 <form class="ty-right getCon" style="margin-right:15px;height:150px;">
                     <ul id="getRight"></ul>
                 </form>
                 <div class="clr"></div>
             </div>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelChangeRight()">取消</span>
             <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeRight()" id="sureChangeRight">确定</button>
         </div>
     </div>
     <%-- 使用权限设置 --%>
     <div class="bonceContainer bounce-blue" id="scanSetLog">
         <div class="bonceHead">
             <span>使用权限操作记录</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon ">
             <table class="ty-table">
                 <thead>
                 <tr>
                     <td width="20%">文件名称/文件夹名称</td>
                     <td width="13%">操作性质</td>
                     <td width="27%">操作结果</td>
                     <td width="20%">操作者</td>
                     <td width="20%">当时路径</td>
                 </tr>
                 </thead>
                 <tbody id="rightLog"></tbody>
             </table>
             <div id="logYe"></div>
         </div>
         <div class="bonceFoot">
             <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()" >确定</button>
         </div>
     </div>
     <%-- 文件夹名称修改记录 --%>
     <div class="bonceContainer bounce-green" id="nameEditRecord" style="width: 660px;">
         <div class="bonceHead">
             <span>文件夹名称修改记录</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <h4 class="folderNameSee">作业指导书</h4>
             <div class="clear">
                 <p class="ty-left recordTip"></p>
                 <p class="ty-right recordEditer"></p>
             </div>
             <table class="ty-table ty-table-control historyCon">
                 <thead>
                 <tr>
                     <td>状态</td>
                     <td>文件夹名称</td>
                     <td>创建人/修改人</td>
                 </tr>
                 </thead>
                 <tbody></tbody>
             </table>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce.cancel()">关闭</span>
         </div>
     </div>
     <%-- tip --%>
     <div class="bonceContainer bounce-blue " id="tip">
         <div class="bonceHead">
             <span>温馨提示</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <div id="tipMess" style="text-align: center"></div>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel()">我知道了</span>
         </div>
     </div>
     <div class="bonceContainer bounce-blue" id="bounce_tip">
         <div class="bonceHead">
             <span>提示</span>
             <a class="bounce_close" onclick="bounce.cancel()"></a>
         </div>
         <div class="bonceCon">
             <div class="text-center tipMsg"></div>
         </div>
         <div class="bonceFoot">
             <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
             <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" type="btn">确定</span>
         </div>
     </div>
</div>
<div class="bounce_Fixed">
    <%-- scanSetLog --%>
    <div class="bonceContainer bounce-blue " id="scanSetLogScan" style="width:800px; ">
        <div class="bonceHead">
            <span>使用权限操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
        <table width="100%">
            <tr>
                <td><span id="logCat">文件夹</span>名称：<span class="name"></span></td>
                <td>操作者：<span class="createName"></span></td>
                <td>操作性质：<span class="operateType"></span></td>
            </tr>
            <tr>
                <td colspan="3">当时路径：<span class="path"></span></td>
            </tr>
            <tr>
                <td colspan="3"><p style="margin-top:20px; ">操作结果：<span class="result">如下X位职工获得了使用权限</span></p></td>
            </tr>
            <tr>
                <td colspan="3">
                    <div class="users"><p>XXx(3251651261313)</p></div>
                </td>
            </tr>
        </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip2">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess2" style="text-align: center;"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%-- scanSetTip --%>
    <div class="bonceContainer bounce-blue " id="scanSetTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeRight(1)">我知道了</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>目录管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" style="min-width: 1206px">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container clearfix">
                <div class="ty-fileContent">
                    <%--竖型文件树容器--%>
                    <div class="ty-colFileTree setBgHeight" name="main"></div>
                    <div class="ty-mainData mar">
                        <%--此类别信息--%>
                        <div class="ty-panel">
                            <div class="nowFolder">
                                <div class="headPanel">
                                    <h3>全部</h3>
                                    <div class="operableBtn"></div>
                                </div>
                                <div class="docLastIntrol">
                                    <table class="def-table CategoryMessage">
                                        <thead>
                                        <tr>
                                            <td width="25%">包含</td>
                                            <td width="25%">大小</td>
                                            <td width="25%">创建人</td>
                                            <td width="25%">创建时间</td>
                                        </tr>
                                        </thead>
                                        <%--此类别容器--%>
                                        <tbody>
                                        <tr class="generalFolder">
                                            <td></td>
                                            <td>
                                                - -
<%--                                                <span class="ty-btn ty-btn-blue" type="btn" fileType="1" name="fileSize">有效文件</span>--%>
<%--                                                <span class="ty-btn ty-btn-blue" type="btn" fileType="2" name="fileSize">历史文件</span>--%>
                                            </td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right hd" id="" style="margin-top:9px" onclick="newSameClass()">新增同级类别</div>
                            </div>
                            <div class="cCategoryMessage">
                                <p class="generalTip">一级子文件夹共有如下<span class="bubu"></span>个</p>
                                <p class="nomalTip hd">直属于 <span class="normalName"></span> 的子文件夹共有如下 <span></span> 个</p>
                                <table class="def-table addBold">
                                    <thead>
                                    <tr>
                                        <td width="20%">文件夹名称</td>
                                        <td width="20%">包含</td>
                                        <td width="20%">大小</td>
                                        <td width="20%">创建人</td>
                                        <td width="20%">创建时间</td>
                                    </tr>
                                    </thead>
                                    <%--此类别容器--%>
                                    <tbody class="childlFolderInfo"></tbody>
                                </table>
                            </div>
                        </div>
                        <%--子类别信息--%>
                        <div class="ty-panel hd" style="margin-top:130px">
                            <div class="ty-panelHeader childFolder">
                                <h3>子类别</h3>
                            </div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                    <tr>
                                        <td width="60%">类别名称</td>
                                        <td width="20%">创建日期</td>
                                        <td width="20%">修改日期</td>
                                    </tr>
                                </thead>
                                <%--子类别容器--%>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseMiniApp/gonggao.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    if(generalType != 1 && generalType != 2){
        $("#addBtn").hide(); $("#sureChangeRight").hide();
    }
</script>
</body>
</html>
