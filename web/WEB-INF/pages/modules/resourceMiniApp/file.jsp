<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/r_wenjian.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/fileTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
    .contentlist table{
        width: 100%;
    }
    .contentlist{
        border:1px solid #ccc;
        border-radius: 4px;
        padding:15px;
    }
    .hd2{ display: none; }
    .circleCon{
        margin-top:5px;
    }
    .linkbtn{ color:#0b9df9; cursor: pointer; margin-right: 20px; }
    .linkbtn:hover{ text-decoration: underline  }
    #addTxt textarea { width: 83%;height: 100px; }
    #addVedio input, #addPic input{ width: 450px; }
    .feediv{
        position: absolute;
        top: 30%;
        left: 50%;
        width: 75px;
        /*opacity: 0.6;*/
    }
    .hel{ display: none; }
    .ty-fileItem .fa-file-text-o{
        color: #65baf7;
        font-size: 40px;
        margin-right: 14px;
    }
    #fileScan p>span:nth-child(1){
        width: 100px;
        text-align: right;
        display: inline-block;
        margin-right: 20px;
    }
    #fileScan .conList{
        width: 35%;
        position: relative;
        right: -120px;
    }
    #fileContentScan textarea{
        width: 80%;
        height: 80px;
    }
    #chooseSaveFolder2 .mar{
        width: 735px;
    }
    .fileShow{ margin-top: 10px; }
    .fileShow .ty-radio{
        display: none!important;
    }
    #fileContentScan input{ width: 85% }
    #fileContentScan .ty-fileItem{ height: 88px;  }
    #fileContentScan .ty-fileType{
        width: 50px;
        height: 66px;
        /*margin-right: 16px;*/
        /*background: url(../../../css/content/img/other.png) no-repeat 0 1px;*/
        background-size: 50px;
        position: relative;
        top:10px;
    }
    #fileContentScan .flexCon{ position: relative}
    #fileContentScan .ctrlBtns{ position: absolute; top:55px; right: 21px; }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed3">
    <%--移动弹窗的确定弹窗--%>
    <div class="bonceContainer bounce-blue" id="bounceFixed3_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed3.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 sureBtn">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="seeBorrowPerson" style="width: 600px">
        <div class="bonceHead">
            <span>借阅人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clearfix">
                <h4 class="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件名称：</span><span class="con" name="name"></span></div>
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" name="fileSn"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con" name="changeNum"></span></div>
                    <div class="trItem"><span class="ttl2">借阅人数：</span><span class="con" name="borrowNum"></span></div>
                </div>
            </div>
            <table class="ty-table">
                <thead>
                <tr>
                    <td>姓名</td>
                    <td>性别</td>
                    <td>手机号</td>
                    <td>部门</td>
                    <td>职位</td>
                </tr>
                </thead>
                <tbody class="table_borrowPerson"></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed3.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder2" style="width: 1050px">
        <div class="bonceHead">
            <span class="bounce_title">选择文件</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-fileContent">
                <%-- 文件夹列表 --%>
                <div class="ty-colFileTree folder_list" data-name="main"></div>
                <%-- 文件列表 --%>
                <div class="mar">
<%--                    <ul class="ty-secondTab" id="fileSort2" style="display: none">--%>
<%--                        <li class="ty-active">发布时间<span class="sort sortName"><i class="fa fa-long-arrow-down"></i></span></li>--%>
<%--                        <li type="1">文件编号<span class="sort sortName" style="display: none"><i class="fa fa-long-arrow-down"></i></span></li>--%>
<%--                    </ul>--%>
                    <div class="ty-fileList mainFileList" >
                        <%--文件列表--%>
                    </div>
                    <div id="ye_con2"></div>
                </div>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 funbtn" data-fun="sureChooseFolderBtn2ok" id="sureChooseFolderBtn2" >确定</button>
        </div>
    </div>

    <div class="bonceContainer bounce-blue " id="fileContentScan">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon type1" style="text-align: center;">
            <div>
                <textarea readonly></textarea>
            </div>
        </div>
        <div class="bonceCon type2">
            <p>
                <span>图片介绍</span>
                <input class="picDesc" type="text" readonly />
            </p>
            <div>
                <span>图片</span>
                <div class="flexCon">
                </div>
            </div>
            <p>
                <span>图片名称</span>
                <input type="text" readonly class="picName">
            </p>
        </div>
        <div class="bonceCon type3">
            <p>
                <span>视频介绍</span>
                <input class="picDesc" type="text" readonly />
            </p>
            <div>
                <span>视频</span>
                <div class="flexCon">
                </div>
            </div>
            <p>
                <span>视频名称</span>
                <input type="text" class="picName" readonly>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" id="bounce_Fixed2Cancel" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%--移动弹窗的确定弹窗--%>
    <div class="bonceContainer bounce-blue" id="bounceFixed2_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 sureBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- addTxt --%>
    <div class="bonceContainer bounce-blue " id="delCon">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">
            确定删除本条内容吗？
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big funbtn"  data-fun="delConOk">确定</span>
        </div>
    </div>
    <%--fileScan--%>
    <div class="bonceContainer bounce-blue" id="fileScan" style="width: 680px">
        <div class="bonceHead">
            <span class="bounce_title">文章基本信息</span>
            <a class="bounce_close" onclick=" bounce_Fixed.cancel(); "></a>
        </div>
        <div class="bonceCon">
            <p><span>文章名称</span><span class="filename"></span></p>
            <p><span>展示位置</span><span class="savePlace"></span></p>
            <p><span>价格</span><span class="price"></span></p>
            <p><span>内容</span><span class=""></span></p>
            <table class="conList">
                <tr>
                    <td><span>内容1</span> <span>文字</span></td>
                    <td class="ty-td-control"><span class="ty-color-blue"></span></td>
                </tr>
                <tr>
                    <td><span>内容1</span> <span>文字</span></td>
                    <td class="ty-td-control"><span class="ty-color-blue"></span></td>
                </tr>
                <tr>
                    <td><span>内容1</span> <span>文字</span></td>
                    <td class="ty-td-control"><span class="ty-color-blue"></span></td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
<%--            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 funbtn" data-fun="preFileScan" >预览</button>--%>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " onclick=" bounce_Fixed.cancel();">关闭</button>
        </div>
    </div>
    <%-- addTxt --%>
    <div class="bonceContainer bounce-blue " id="addTxt">
        <div class="bonceHead">
            <span>录入文字</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">
            <textarea onblur="wordtip($(this), 100)" placeholder="请录入&#10;本次“录入文字”，将展示在一段里 &#10;如需展示为多段，请多次“录入文字”"></textarea>
            <span class="wordTip">0/100</span>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="addContent(1)">确定</span>
        </div>
    </div>
    <%-- addPic --%>
    <div class="bonceContainer bounce-blue " id="addPic" style="width:720px;">
        <div class="bonceHead">
            <span>导入图片</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span>图片介绍</span>
                <input id="addp" type="text" onblur="wordtip($(this), 30)" placeholder="无需录入内容时可忽略" />
                <span class="wordTip">0/100</span>
                <span class="linkbtn funbtn">展示位置</span>
            </p>
            <div>
                <span>图片</span>
                <span style="margin-left: 40px;" class="linkbtn funbtn" data-fun="chooseSorce" data-place="2" id="uploadPic">选择</span>
                <div class="fileShow"></div>
                <div class="ty-color-blue" style="padding:16px; line-height:14px; font-size: 0.8em;">
                    <p>注1 每次导入，仅可导入一个文件</p>
                    <p>注2 需更换已选文件时，请点击选择，并重新选择文件</p>
                    <p>注3 所导入文件如在wonderss系统内换版，小程序端将自动更新！</p>
                </div>
            </div>
            <p>
                <span>图片名称</span>
                <input id="pics" type="text" onblur="wordtip($(this), 30)" placeholder="无需录入内容时可忽略">
                <span class="wordTip">0/100</span>
                <span class="linkbtn funbtn">展示位置</span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="addContent(2)">确定</span>
        </div>
    </div>
    <%-- addVedio --%>
    <div class="bonceContainer bounce-blue " id="addVedio" style="width:720px;">
        <div class="bonceHead">
            <span>导入视频</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p>
                <span>视频介绍</span>
                <input id="vds" type="text" onblur="wordtip($(this), 30)" placeholder="无需录入内容时可忽略">
                <span class="wordTip">0/100</span>
                <span class="linkbtn funbtn">展示位置</span>
            </p>
            <div>
                <span>视频</span>
                <span style="margin-left: 40px;" class="linkbtn funbtn" data-fun="chooseSorce" data-place="3" id="uploadVedio">选择</span>
                <div class="fileShow"></div>
                <div class="ty-color-blue" style="padding:16px; line-height:14px; font-size: 0.8em;">
                    <p>注1 每次导入，仅可导入一个文件</p>
                    <p>注2 需更换已选文件时，请点击选择，并重新选择文件</p>
                    <p>注3 所导入文件如在wonderss系统内换版，小程序端将自动更新！</p>
                </div>
            </div>
            <p>
                <span>视频名称</span>
                <input id="vdn" type="text" onblur="wordtip($(this), 30)" placeholder="无需录入内容时可忽略">
                <span class="wordTip">0/100</span>
                <span class="linkbtn funbtn">展示位置</span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="addContent(3)">确定</span>
        </div>
    </div>
    <%-- scanSetTip --%>
    <div class="bonceContainer bounce-blue " id="scanSetTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;">

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeRight(1)">我知道了</span>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip2">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess2" style="text-align: center;"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%--各种操作记录--%>
    <div class="bonceContainer bounce-blue" id="handleRecord" style="width: 650px">
        <div class="bonceHead">
            <span class="recordTitleName">修改文件编号</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:300px; overflow-y: auto; ">
            <table class="ty-table recordTable"></table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="F_errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">确定</span>
        </div>
    </div>
    <%--选择保存位置--%>
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 650px">
        <div class="bonceHead">
            <span class="bounce_title">选择保存位置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="folder_list ty-colFileTree" data-name="chooseSaveFolder"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn" data-name="sureChooseFolder" id="sureChooseFolderBtn">确定</button>
        </div>
    </div>
    <%--移动弹窗--%>
    <div class="bonceContainer bounce-blue" id="moveFile">
        <div class="bonceHead">
            <span>移动到</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <%-- 移动到的文件夹列表 --%>
        <div class="bonceCon">
            <div class="ty-colFileTree" data-name="moveTo"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 sureBtn"  type="btn" data-name="sureMoveFile" id="sureMoveFileBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="fileHandleLog">
        <div class="bonceHead">
            <span>操作记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <tr>
                    <td>操作</td>
                    <td>操作者</td>
                    <td>操作后的文章</td>
                </tr>
            </table>
            <div id="logpage"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="reusefileTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg">确定后，小程序将重新显示本文章！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 funbtn" data-fun="reUseOk"> 确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="stopfileTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg">确定后，小程序上将不再显示本文章！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 funbtn" data-fun="stopOk"> 确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" type="btn">确定</span>
        </div>
    </div>
    <%-- 使用权限设置 --%>
    <div class="bonceContainer bounce-blue" id="scanSet">
        <div class="bonceHead">
            <span>使用权限设置</span>
            <a class="bounce_close" onclick="cancelChangeRight()"></a>
        </div>
        <div class="bonceCon ">
            <input type="hidden" id="isNew">
            <p><span class="packType">文件夹</span>名称：<span class="packName"></span> </p>
            <p class="">
                请选择需使用本<span class="packType">文件夹</span>的职工 <span style="margin-left:100px; " class="btnLink" data-type="allSelect">全选</span>
                <span class="ty-right txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    已选职工：<span class="selectedNum">20</span>位
                    <span class="btnLink" data-type="allClear">全部清空</span>
                </span>
            </p>
            <div class="departTree">
                <ul class="ty-left">
                    <p class="txtR"><span class="btnLink changeTypeLeft" id="changeTypeLeft" onclick="changeType($(this), 'left')">直接展示全部职工</span></p>
                    <div id="allRight"></div>
                </ul>
                <div class="ty-left arrow"><i class="fa fa-exchange"></i></div>
                <form class="ty-left" id="deparCon">
                    <input type="hidden" name="categoryId" id="categoryId">
                    <p class="txtR"><span class="btnLink" id="changeTypeRight" onclick="changeType($(this), 'right')">切换为按部门展示</span></p>
                    <ul id="nowRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
            <p>&nbsp;
                <span class="cancelCon">以下<span id="cancelNum">22</span>位职工刚被取消了本<span class="packType">文件夹</span>的使用权限。</span>
                <span class="ty-right getCon txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    以下<span id="getNum"></span>位职工刚获得了本<span class="packType">文件夹</span>的使用权限。
                </span>
            </p>
            <div class="departTree changeCon">
                <ul class="ty-left cancelCon" style="height:150px;">
                    <div id="cancelRight"></div>
                </ul>
                <form class="ty-right getCon" style="margin-right:15px;height:150px;">
                    <ul id="getRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelChangeRight()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeRight()" id="sureChangeRight">确定</button>
        </div>
    </div>
    <%-- 新增子级类别 --%>
    <div class="bonceContainer bounce-green bounce-newClass">
        <div class="bonceHead">
            <span>新建子文件夹</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>在【<span class="recentParentFolder"></span>】下新建子文件夹</p>
            <p>子文件夹名称</p>
            <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
            <p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。
                <span class="ty-right btnLink" id="scanSetFolerBtn" onclick="scanSetBtn()">去设置使用权限</span>
            </p>
        </div>
        <span class="hd" id="havRightUserCon"></span>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewClass()">确定</span>
        </div>
    </div>
    <%-- 提示 --%>
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align:center; font-size:16px; margin-top: 20px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--上传文件弹窗--%>
    <div class="bonceContainer bounce-blue" id="fileUpload" style="width: 680px">
        <div class="bonceHead">
            <span class="bounce_title">编辑并发布文章</span>
            <a class="bounce_close" onclick=" bounce.cancel(); "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="inputPart">
                <div class="show_fileOrFiles">
                    <div class="item-row">
                        <div class="item-title">文件名称 <span class="ty-color-red">*</span></div>
                        <div class="item-content">
                            <input type="text"  name="fileNo" class="fileNo ty-inputText" require>
                            <i class="fa fa-times-circle clearInput"></i>
                        </div>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title ">展示位置</div>
                    <div class="item-content">
                        <button class="ty-btn ty-btn-blue ty-circle-3" type="btn" id="filechooseSaveFolder" data-name="chooseSaveFolder">选择</button>
                        <span class="savePlace"></span>
                    </div>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title">收费设置</div>
                <div class="item-content">
                    <div class="circleCon fee" >
                        <i class="fa fa-circle-o" data-val="false"></i>
                        <span>免费</span>
                        <i class="fa fa-circle-o" data-val="true" style="margin-left:50px;"></i>
                        <span>收费</span>
                        <span class="feeshow">
                            <input id="feea" onkeyup="clearNoNum(this)" type="text" style="display: inline-block; margin-left: 30px; width: 70px;">
                            <span>人民币元</span>
                            <br />
                            <span>剩余可售数量</span>
                            <input id='stock' onkeyup='clearNoNum(this)' type='text' style='display: inline-block; width: 75px;'>
                            <span>件</span>
                            <span>，购买时长</span>
                            <input id='ctypeExpires' onkeyup='clearNoNum(this)' type='text' style='display: inline-block; width: 50px;'>
                            <select id='ctypeId' class='form-control input-inline input-sm input-small' name='ctypeId' style='width: 60px;'>
                                <option value='-1'>不限</option>
                                <option value='1'>年</option>
                                <option value='2'>季度</option>
                                <option value='3'>月</option>
                                <option value='4'>日</option>
                                <option value='5'>小时</option>
                                <option value='6'>分钟</option>
                            </select>
                        </span>
                    </div>
                </div>
            </div>
            <div>
                <p>
                    <span>编辑内容</span>
                    <span style="margin-left:50px;" data-cat="1" data-fun="content_add" data-type="1" class="linkbtn funbtn">录入文字</span>
                    <span data-cat="2" data-fun="content_add" data-type="1"  class="linkbtn funbtn">导入图片</span>
                    <span data-cat="3" data-fun="content_add" data-type="1"  class="linkbtn funbtn">导入视频</span>
                    <span style="float: right; margin-top:-10px;" data-fun="content_preScan" data-type="1"  class="funbtn ty-btn ty-btn-blue ty-btn-big ty-circle-3">预览</span>
                </p>
                <div class="contentlist">
                   <table id="contentlist">
                       <tr>
                           <td><span>内容一</span><span>文字</span></td>
                           <td class="ty-td-control">
                               <span>在上方</span>
                               <span class="ty-color-blue" data-cat="1" data-fun="content_add" data-type="2">录入文字</span>
                               <span class="ty-color-blue" data-cat="2" data-fun="content_add" data-type="2" >导入图片</span>
                               <span class="ty-color-blue" data-cat="3" data-fun="content_add" data-type="2" >导入视频</span>
                           </td>
                           <td class="ty-td-control">
                               <span class="ty-color-blue" data-fun="content_edit">修改</span>
                               <span class="ty-color-red" data-fun="content_del">删除</span>
                           </td>
                       </tr>

                   </table>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title item-title-long">
                    <div class="circleCon fabu">
                        <i class="fa fa-circle-o"></i>
                        <span>文章编辑完毕，在小程序中正式发布</span>
                    </div>
                </div>
               <%-- <div class="item-content">
                    <select class="ty-inputSelect chooseApprover">
                        <option value="选择审批人" selected></option>
                    </select>
                </div>--%>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick=" bounce.cancel();">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 funbtn"   data-fun="savefile" id="sureUploadNewFileBtn">确定</button>
        </div>
    </div>
    <%--文件夹上传弹窗--%>
    <div class="bonceContainer bounce-blue" id="folderUpload" style="width: 500px">
        <div class="bonceHead">
            <span>上传文件夹</span>
            <a class="bounce_close" onclick="chargeXhr()"></a>
        </div>
        <div class="bonceCon clearfix">
            <p>请选择要上传的文件夹<span id="folderStatus" style="padding-left: 20px;"></span></p>
            <div class="upload_avatar"><input name="file" type="file"  id="upload-folder-01"></div>
            <div class="item-row">
                <div class="item-title item-title-long">请选择保存位置</div>
                <div class="item-content">
                    <button class="ty-btn ty-btn-blue ty-circle-3" type="btn" id="chooseSaveFolderBtn" data-name="chooseSaveFolder">选择</button>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title">文件夹名称 <span class="ty-color-red">*</span> </div>
                <div class="item-content">
                    <input type="text" id="folder_name" class="ty-inputText" require>
                    <i class="fa fa-times-circle clearInput"></i>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title"></div>
                <div class="item-content">
                    <div class="savePlace"></div>
                </div>
            </div>
            <div class="item-row">
                <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注1  上传成功后请检查各文件的名称或编号。不符合期望的，您可自行修改</div>
            </div>
            <div class="item-row">
                <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i><div>注2  本系统不支持部分格式文件的上传，也不支持超过1.5G的单个文件的上传。系统将提供<span class="ty-color-red">未能成功上传的文件清单</span>，以便您妥善安排</div></div>
            </div>
            <div class="item-row">
                <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注3  对于某次上传的持续时间，本系统本身并不限制，但断网、断电，及浏览
                    器或本页面被关闭等情况将导致已上传的文件全部丢失。</div>
            </div>
            <div class="item-row">
                <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注4  对于一次上传的文件总数，本系统本身虽不限制，但建议不要超过1000个。</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="chargeXhr()">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3"  type="btn" data-name="sureUploadNewFolder" id="sureUploadNewFolderBtn">确定</button>
        </div>
    </div>

    <%-- 文件查看基本信息 --%>
    <div class="bonceContainer bounce-blue " id="docInfoScan" style="width:800px">
        <div class="bonceHead">
            <span>基本信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <div class="infoCon clearfix">
                <h4 class="info_title" id="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" id="info_sn"></span></div>
                    <div class="trItem"><span class="ttl">保存位置：</span><span class="con" id="info_category"></span></div>
                    <div class="trItem"><span class="ttl">文件大小：</span><span class="con" id="info_size"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con" id="info_gn"></span></div>
                    <div class="trItem"><span class="ttl2">创建人：</span><span class="con"><span id="info_createName" class="info_name"></span><span id="info_createDate"></span></span></div>
                    <div class="trItem"><span class="ttl2">文件类型：</span><span class="con" id="info_version"></span></div>
                </div>
            </div>
            <div>
                <div><div class="trItem info_content"><span class="ttl">说明：</span><span class="con" id="info_content" style="max-width: 670px"></span></div></div>
            </div>
            <div class="infoCon clearfix">
               <%-- <div class="ty-left processHistory">
                    <div class="item-header" style="margin-left: 10px">
                        审批记录
                    </div>
                    <div class="processList">
                    </div>
                </div>--%>
                <div class="ty-left censusInfo">
                    <div class="item-header">
                        统计信息
                    </div>
                    <div class="trItem">
                        <span class="ttl3">浏览次数：</span>
                        <span class="con times view_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="viewNumBtn" onclick="seeHandelRecordBtn(1)">浏览记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">下载次数：</span>
                        <span class="con times download_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="downloadNumBtn" onclick="seeHandelRecordBtn(2)">下载记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">移动次数：</span>
                        <span class="con times move_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="moveNumBtn" onclick="seeHandelRecordBtn(5)">移动记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件名称修改次数：</span>
                        <span class="con times name_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="changeNameNumBtn" onclick="seeHandelRecordBtn(6)">修改记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件编号修改次数：</span>
                        <span class="con times no_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="changeNoNumBtn" onclick="seeHandelRecordBtn(7)">修改记录</button>
                    </div>
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 高级查询弹窗 --%>
    <div class="bonceContainer bounce-blue " id="advancedSearch" data-type="0" style="width: 600px">
        <div class="bonceHead">
            <span>高级搜索</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <ul class="ty-secondTab">
                <li class="ty-active">文件搜索</li>
                <li>文件类别搜索</li>
            </ul>
            <div class="searchCon">
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件名称
                    </div>
                    <div class="eq_r">
                        <input type="text" class="name">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件编号
                    </div>
                    <div class="eq_r">
                        <input type="text" class="fileSn">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件类型
                    </div>
                    <div class="eq_r">
                        <select class="version">
                            <option value="">--请选择文件类型--</option>
                            <option value="doc">*.doc</option>
                            <option value="docx">*.docx</option>
                            <option value="xls">*.xls</option>
                            <option value="xlsx">*.xlsx</option>
                            <option value="zip">*.zip</option>
                            <option value="rar">*.rar</option>
                            <option value="apk">*.apk</option>
                            <option value="ipa">*.ipa</option>
                            <option value="ppt">*.ppt</option>
                            <option value="txt">*.txt</option>
                            <option value="pdf">*.pdf</option>
                            <option value="png">*.png</option>
                            <option value="jpg">*.jpg</option>
                            <option value="wps">*.wps</option>
                            <option value="et">*.et</option>
                        </select>
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        上传人
                    </div>
                    <div class="eq_r">
                        <input type="text" class="createName" />
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        上传时间
                    </div>
                    <div class="eq_r">
                        <input type="text" class="createDateBegin" id="uploadDateStart"> ~
                        <input type="text" class="createDateEnd" id="uploadDateEnd">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        换版人
                    </div>
                    <div class="eq_r">
                        <input type="text" class="updateName">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        换版时间
                    </div>
                    <div class="eq_r" id="postDate">
                        <input type="text" class="updateDateBegin" id="ChangeDateStart"> ~
                        <input type="text" class="updateDateEnd" id="ChangeDateEnd">
                    </div>
                </div>
            </div>
            <div class="searchCon" style="display: none">
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件类别
                    </div>
                    <div class="eq_r">
                        <input type="text" class="folderName">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="advancedSearchBtn">确定</button>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
    <%--更改文件名称--%>
    <div class="bonceContainer bounce-blue" id="changeFileName">
        <div class="bonceHead">
            <span>修改文件名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>当前文件名称：</div>
            <b class="currentFileName"></b>
            <div>请录入新的文件名称：</div>
            <input type="text" class="changeFileName" style="width: 100%">
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="changeFileNameBtn"  onclick="sureChangeFileName()">确定</button>
        </div>
    </div>

    <%--更改文件编号--%>
    <div class="bonceContainer bounce-blue" id="changeFileNo">
        <div class="bonceHead">
            <span>修改文件编号</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>当前文件编号：</div>
            <b class="currentFileNo"></b>
            <div>请录入新的文件编号</div>
            <input type="text" class="changeFileNo" style="width: 100%">
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="changeFileNoBtn" onclick="sureChangeFileNo()">确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>文章管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container" style="position:relative; ">
                <div id="btn-group" style="width: 1280px;text-align: right">
<%--                    <span class="ty-search">--%>
<%--                        &lt;%&ndash;<span class="ty-search-ttl"></span>&ndash;%&gt;--%>
<%--                        <input class="ty-searchInput" id="fileNameOrSn" type="text" placeholder="文件名称/文件编号">--%>
<%--                        <div class="ty-searchBtn" onclick="searchBtn()"></div>--%>
<%--                    </span>--%>
<%--                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 customQuery" id="customQueryBtn" onclick="advanceSearchBtn()">高级搜索</button>--%>
<%--                    <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" type="btn" id="newFoler" onclick="newClass()">新建文件夹</button>--%>
                    <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3 funbtn"   data-fun="fileUploadbtn" id="fileUploadBtn">编辑并发布文章</button>
<%--                    <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" type="btn" data-name="folderUpload" id="folderUploadBtn">上传文件夹</button>--%>
                </div>
                <div>
                    <div id="main" class="ty-mainData">
                        <div class="ty-fileContent">
                            <%-- 文件夹列表 --%>
                            <div class="ty-colFileTree setBgHeight" id="listDoc" data-name="main"></div>
                            <%-- 文件列表 --%>
                            <div class="mar">
                                <ul class="ty-secondTab" id="fileSort" style="display: none">
                                    <li class="ty-active">发布时间
<%--                                        <span class="sort sortName"><i class="fa fa-long-arrow-down"></i></span>--%>
                                    </li>
<%--                                    <li type="1">文件编号--%>
<%--                                        <span class="sort sortName" style="display: none"><i class="fa fa-long-arrow-down"></i></span>--%>
<%--                                    </li>--%>
                                </ul>
                                <div class="ty-fileList mainFileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_con"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                        <div class="ty-searchContent" style="display: none">
                            <div class="ty-right">
                                <button class="ty-btn ty-btn-blue ty-circle-3" id="rebackBtn" onclick="goBack('query')">返回</button>
                            </div>
                            <div class="mar">
                                <ul class="ty-secondTab" id="searchSort" style="display: none; margin-bottom:8px">
                                    <li>发布人 <select name="" id="search_applier"></select></li>
                                    <li>发布时间<span class="sort sortName" style="display: none"><i class="fa fa-long-arrow-down"></i></span></li>
                                </ul>
                                <div class="searchInfo">
                                    <div class="folderContent">
                                        <div class="searchFolder">
                                            <div class="searchFolderContent"></div>
                                            <div id="ye-search-folder"></div>
                                        </div>
                                        <div class="childFolder"></div>
                                    </div>
                                    <div class="fileContent">
                                        <div class="searchFile"></div>
                                        <div id="ye-search-file"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="resHistory" style="display: none">
                        <div class="ty-searchContent">
                            <div class="ty-right">
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="goBack('record')">返回</button>
                            </div>
                            <div class="mar">
                                <div class="fileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_record"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    var isGeneral = generalType === '1' || generalType === '2'
    var isSuper = generalType === '0'
    // if(isGeneral){
    //     $("#fileUploadBtn").html("上传文件");
    //     $("#uploadDirect").html("本文件直接发布，无需他人审批");
    //
    // }else {
    //     $("#fileUploadBtn").html("文件发布申请");
    //     $("#folderUploadBtn").remove()
    //     $("#uploadDirect").html("本文件直接提交给文管，无需他人审批");
    // }
</script>
<script src="../script/resourseMiniApp/wenjian.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
