<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/fileTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/r_wenjian.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/publicInfo/public.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn" type="btn">确定</span>
        </div>
    </div>
    <%--上传文件弹窗--%>
    <div class="bonceContainer bounce-blue" id="fileUpload" style="width: 500px">
        <div class="bonceHead">
            <span class="bounce_title">上传文件</span>
            <a class="bounce_close" onclick="chargeXhr()"></a>
        </div>
        <div class="bonceCon clearfix">
            <p>请选择要上传的文件</p>
            <div class="upload_avatar">
                <input name="file" type="file"  id="upload-file-01">
            </div>
            <div class="show_fileOrFiles">
                <div class="item-row">
                    <div class="item-title">文件编号 <span class="ty-color-red">*</span></div>
                    <div class="item-content">
                        <input type="text" name="fileNo" class="ty-inputText" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-row">
                    <div class="item-title">文件名称 <span class="ty-color-red">*</span></div>
                    <div class="item-content">
                        <input type="text" name="name" class="ty-inputText" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-row">
                    <div class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>注：您可根据公司实际要求，修改系统给予的文件编号与文件名称</div>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title item-title-long">请选择保存位置</div>
                <div class="item-content">
                    <button class="ty-btn ty-btn-blue ty-circle-3" type="btn" data-name="chooseSaveFolder">选择</button>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title"></div>
                <div class="item-content">
                    <div class="savePlace"></div>
                </div>
            </div>
            <div class="hr"></div>
            <div class="item-column">
                <p class="inputPart">说明</p>
                <input type="text" name="content" class="ty-inputText inputPart" placeholder="您可在此录入必要的说明。如无说明，可忽略。">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="chargeXhr()">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureUploadNewFile" id="sureUploadFileBtn">确定</button>
        </div>
    </div>
    <%--文件换版--%>
    <div class="bonceContainer bounce-blue" id="changeFileVersion" style="width: 500px">
        <div class="bonceHead">
            <span class="bounce_title">换版</span>
            <a class="bounce_close" onclick="chargeXhr()"></a>
        </div>
        <div class="bonceCon clearfix">
            <p>请选择要上传的文件</p>
            <div class="upload_avatar">
                <input name="file" type="file"  id="upload-file-02">
            </div>
            <div class="item-row">
                <div class="item-title">文件编号</div>
                <div class="item-content">
                    <div class="text_disabled see_fileNo"></div>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title">文件名称</div>
                <div class="item-content">
                    <div class="text_disabled see_fileName"></div>
                </div>
            </div>
            <div class="item-row">
                <div class="item-title">保存位置</div>
                <div class="item-content">
                    <div class="text_disabled see_savePlace"></div>
                </div>
            </div>
            <div class="hr"></div>
            <div class="item-column">
                <p class="seePart">换版原因</p>
                <input type="text" name="reason" class="ty-inputText seePart" placeholder="您可在此阐述换版原因。如无，可忽略。">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="chargeXhr()">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureChangeVersion" id="sureChangeVersionBtn">确定</button>
        </div>
    </div>
    <%--编辑资料内容--%>
    <div class="bonceContainer bounce-blue" id="editDataContent" style="width: 500px">
        <div class="bonceHead">
            <span class="bounce_title">编辑资料内容</span>
            <a class="bounce_close" onclick="chargeXhr()"></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="dataType2">
                <p>请保持本开票信息的有效性，以便有需要的同事能随时正常使用！</p>
                <div class="item-col">
                    <div class="item-title">公司名称</div>
                    <div class="item-content">
                        <input type="text" name="companyName" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">纳税人识别号</div>
                    <div class="item-content">
                        <input type="text" name="taxIdNumber" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">地址、电话</div>
                    <div class="item-content">
                        <input type="text" name="address" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">开户行</div>
                    <div class="item-content">
                        <input type="text" name="bankName" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">账号</div>
                    <div class="item-content">
                        <input type="text" name="bankNo" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
            </div>
            <div class="dataType3">
                <p>请保持本开票信息的有效性，以便有需要的同事能随时正常使用！</p>
                <div class="item-col">
                    <div class="item-title">公司名称</div>
                    <div class="item-content">
                        <input type="text" name="companyName" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">纳税人识别号</div>
                    <div class="item-content">
                        <input type="text" name="taxIdNumber" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
            </div>
            <div class="dataType4">
                <p>有时，有的同事需向外部提供公司的收款资料。在此选一个合适的账户，可为此提供便利。</p>
                <p>另外，请维持所选账户资料的有效性！</p>
                <div class="item-col">
                    <div class="item-title">公司名称</div>
                    <div class="item-content">
                        <input type="text" name="companyName" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">开户行</div>
                    <div class="item-content">
                        <input type="text" name="bankName" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">账号</div>
                    <div class="item-content">
                        <input type="text" name="bankNo" class="ty-inputText" placeholder="请录入内容" require>
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">备注</div>
                    <div class="item-content">
                        <input type="text" name="memo" class="ty-inputText" placeholder="请录入内容">
                        <i class="fa fa-times-circle clearInput"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureEditDataContent" id="sureEditDataContentBtn">确定</button>
        </div>
    </div>
    <%--查看资料内容--%>
    <div class="bonceContainer bounce-blue" id="seeDataContent" style="width: 500px">
        <div class="bonceHead">
            <span class="bounce_title">查看资料内容</span>
            <a class="bounce_close" onclick="chargeXhr()"></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="dataType2">
                <p>以下为本公司的开票信息。</p>
                <div class="item-col">
                    <div class="item-title">公司名称</div>
                    <div class="item-content">
                        <div class="see item_see" name="companyName"></div>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">纳税人识别号</div>
                    <div class="item-content">
                        <div class="see item_see" name="taxIdNumber"></div>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">地址、电话</div>
                    <div class="item-content">
                        <div class="see item_see" name="address"></div>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">开户行</div>
                    <div class="item-content">
                        <div class="see item_see" name="bankName"></div>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">账号</div>
                    <div class="item-content">
                        <div class="see item_see" name="bankNo"></div>
                    </div>
                </div>
            </div>
            <div class="dataType3">
                <p>以下为本公司的开票信息。</p>
                <div class="item-col">
                    <div class="item-title">公司名称</div>
                    <div class="item-content">
                        <div class="see item_see" name="companyName"></div>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">纳税人识别号</div>
                    <div class="item-content">
                        <div class="see item_see" name="taxIdNumber"></div>
                    </div>
                </div>
            </div>
            <div class="dataType4">
                <p>以下为本公司的收款资料。</p>
                <div class="item-col">
                    <div class="item-title">公司名称</div>
                    <div class="item-content">
                        <div class="see item_see" name="companyName"></div>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">开户行</div>
                    <div class="item-content">
                        <div class="see item_see" name="bankName"></div>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">账号</div>
                    <div class="item-content">
                        <div class="see item_see" name="bankNo"></div>
                    </div>
                </div>
                <div class="item-col">
                    <div class="item-title">备注</div>
                    <div class="item-content">
                        <div class="see item_see" name="memo"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- 文件查看基本信息 --%>
    <div class="bonceContainer bounce-blue" id="docInfoScan" style="width:800px">
        <div class="bonceHead">
            <span>基本信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <div class="infoCon clearfix">
                <h4 class="info_title" id="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" id="info_sn"></span></div>
                    <div class="trItem"><span class="ttl">保存位置：</span><span class="con" id="info_category"></span></div>
                    <div class="trItem"><span class="ttl">文件大小：</span><span class="con" id="info_size"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con" id="info_gn"></span></div>
                    <div class="trItem"><span class="ttl2">创建人：</span><span class="con"><span id="info_createName" class="info_name"></span><span id="info_createDate"></span></span></div>
                    <div class="trItem"><span class="ttl2">文件类型：</span><span class="con" id="info_version"></span></div>
                </div>
            </div>
            <div>
                <div><div class="trItem info_content"><span class="ttl">说明：</span><span class="con" id="info_description" style="max-width: 670px"></span></div></div>
            </div>
            <div class="infoCon clearfix">
                <div class="ty-left censusInfo">
                    <div class="item-header">
                        统计信息
                    </div>
                    <div class="trItem">
                        <span class="ttl3">移动次数：</span>
                        <span class="con times move_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="moveNumBtn" onclick="seeHandelRecordBtn(5)">移动记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件名称修改次数：</span>
                        <span class="con times name_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="changeNameNumBtn" onclick="seeHandelRecordBtn(6)">修改记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件编号修改次数：</span>
                        <span class="con times no_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-3" id="changeNoNumBtn" onclick="seeHandelRecordBtn(7)">修改记录</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--更改文件名称--%>
    <div class="bonceContainer bounce-blue" id="changeFileName">
        <div class="bonceHead">
            <span>修改文件名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>当前文件名称：<b class="currentFileName"></b></div>

            <div>请录入新的文件名称：</div>
            <input type="text" class="changeFileName" style="width: 100%">
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="changeFileNameBtn"  onclick="sureChangeFileName()">确定</button>
        </div>
    </div>
    <%--更改文件编号--%>
    <div class="bonceContainer bounce-blue" id="changeFileNo">
        <div class="bonceHead">
            <span>修改文件编号</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>当前文件编号：<b class="currentFileNo"></b></div>

            <div>请录入新的文件编号</div>
            <input type="text" class="changeFileNo" style="width: 100%">
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="changeFileNoBtn" onclick="sureChangeFileNo()">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue " id="tip2">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess2" style="text-align: center;"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%--选择保存位置--%>
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 650px">
        <div class="bonceHead">
            <span class="bounce_title">选择保存位置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <nav class="ty-colFileTree" data-name="chooseSaveFolder">
                <ul>
                    <li>
                        <div class="ty-treeItem" title="全部" children="2" level="0" parent="null" directoryAble="false">
                            <i class="fa fa-angle-right"></i>
                            <i class="fa fa-folder"></i>
                            <span>全部</span>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" type="btn" data-name="sureChooseFolder" id="sureChooseFolderBtn">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="moveFile" style="width: 650px">
        <div class="bonceHead">
            <span class="bounce_title">移动到</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <nav class="ty-colFileTree" data-name="moveTo"></nav>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" type="btn" data-name="sureMoveFile" id="sureMoveToBtn">确定</button>
        </div>
    </div>
    <%--各种操作记录--%>
    <div class="bonceContainer bounce-blue" id="handleRecord" style="width: 650px">
        <div class="bonceHead">
            <span class="recordTitleName">修改文件编号</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:300px; overflow-y: auto; ">
            <table class="ty-table recordTable"></table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>信息</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" style="min-width: 1206px">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container clearfix">
                <div class="main">
                    <div class="page page_main">
                        <header>
                            <div class="text-right">
                                <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green" type="btn" data-name="fileUpload" id="fileUploadBtn">上传文件</button>
                            </div>
                        </header>
                        <section class="section_body">
                            <%--竖型文件树容器--%>
                            <section class="section_left">
                                <nav id="publicTree" class="left ty-colFileTree" data-name="main"></nav>
                            </section>
                            <section class="section_right">
                                <%--<ul class="ty-secondTab" id="fileSort" style="display: none">--%>
                                <%--<li class="ty-active">发布时间 <span class="sort sortName"> <i class="fa fa-sort-amount-down-alt"></i></span></li>--%>
                                <%--<li type="1">文件编号 <span class="sort sortName" style="display: none"> <i class="fa fa-sort-amount-down-alt"></i></span></li>--%>
                                <%--</ul>--%>
                                <div class="ty-fileList mainFileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_con"></div>
                            </section>
                        </section>
                    </div>
                    <div class="page page_fileHistory" style="display: none">
                        <div class="btn-group">
                            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="goBack('record')">返回</button>
                        </div>
                        <section class="fileHistory">
                            <div class="ty-fileList historyFileList" >
                                <%--文件列表--%>
                            </div>
                            <div id="ye_record"></div>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/publicInfo/publicInfo.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
