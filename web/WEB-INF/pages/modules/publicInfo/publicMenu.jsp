<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/docManage.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/publicInfo/public.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
  <div class="bonceContainer bounce-blue" id="changeClass">
    <div class="bonceHead">
      <span>修改文件夹名称</span>
      <a class="bounce_close" onclick="bounce.cancel()"></a>
    </div>
    <div class="bonceCon">
      <p>将文件夹【<span class="folderNameChange"></span>】的名称修改为</p>
      <p class="text-center"><input type="text" class="ty-inputText categoryName" placeholder="vbxfg" style="width: 374px;"></p>
    </div>
    <div class="bonceFoot">
      <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
      <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeClass()">确定</span>
    </div>
  </div>
  <%-- 删除类别 --%>
  <div class="bonceContainer bounce-red" id="deleteClass">
    <div class="bonceHead">
      <span>删除文件夹</span>
      <a class="bounce_close" onclick="bounce.cancel()"></a>
    </div>
    <div class="bonceCon text-center">
      <p>确定删除该文件夹？</p>
    </div>
    <div class="bonceFoot">
      <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
      <span class="ty-btn ty-btn-red ty-btn-big" onclick="sureDeleteClass()">确定</span>
    </div>
  </div>
  <%-- 新增子级类别 --%>
  <div class="bonceContainer bounce-green" id="newClass">
    <div class="bonceHead">
      <span>新建子文件夹</span>
      <a class="bounce_close" onclick="bounce.cancel()"></a>
    </div>
    <div class="bonceCon">
      <p>在【<span class="recentParentFolder"></span>】下新建子文件夹</p>
      <p>子文件夹名称</p>
      <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
      <%--<p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。--%>
      <%--<span class="ty-right btnLink" onclick="scanSetBtn('son')">去设置使用权限</span>--%>
      <%--</p>--%>
      <%--<span class="hd" id="havRightUserCon"></span>--%>
    </div>
    <div class="bonceFoot">
      <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
      <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewClass()">确定</span>
    </div>
  </div>
  <%-- 文件夹名称修改记录 --%>
  <div class="bonceContainer bounce-blue" id="nameEditRecord" style="width: 660px;">
    <div class="bonceHead">
      <span>文件夹名称修改记录</span>
      <a class="bounce_close" onclick="bounce.cancel()"></a>
    </div>
    <div class="bonceCon">
      <h4 class="folderNameSee">作业指导书</h4>
      <div class="clear">
        <p class="ty-left recordTip"></p>
        <p class="ty-right recordEditer"></p>
      </div>
      <table class="ty-table ty-table-control historyCon">
        <thead>
        <tr>
          <td>状态</td>
          <td>文件夹名称</td>
          <td>创建人/修改人</td>
        </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
    <div class="bonceFoot">
      <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
    </div>
  </div>
  <%-- tip --%>
  <div class="bonceContainer bounce-blue" id="tip">
    <div class="bonceHead">
      <span>温馨提示</span>
      <a class="bounce_close" onclick="bounce.cancel()"></a>
    </div>
    <div class="bonceCon">
      <div id="tipMess" style="text-align: center"></div>
    </div>
    <div class="bonceFoot">
      <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel()">我知道了</span>
    </div>
  </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
  <%@ include  file="../../common/contentSliderNav.jsp"%>
  <div class="ty-header">
    <ul>
      <li class="active">
        <span>目录</span>
        <i class="close_x"></i>
      </li>
    </ul>
  </div>
  <div class="page-content-wrapper" style="min-width: 1206px">
    <div class="page-content">
      <!--放内容的地方-->
      <div class="ty-container clearfix">
        <%--竖型文件树容器--%>
        <section class="section_left">
          <nav id="publicTree" class="left ty-colFileTree" data-name="main">
            <ul>
              <li>
                <div class="ty-treeItem ty-treeItemActive" title="全部" children="4" level="0" parent="null" directoryAble="false">
                  <i class="fa fa-angle-right"></i>
                  <i class="fa fa-folder"></i>
                  <span>全部</span>
                </div>
              </li>
            </ul>
          </nav>
        </section>
        <section class="section_right">
          <header class="nowFolder">
            <div class="headPanel">
              <h3 class="folderName">全部</h3>
              <div class="operableBtn"></div>
            </div>
            <table class="def-table table_folderInfo">
              <thead>
              <tr>
                <td width="25%">包含</td>
                <td width="25%" class="sizePart">大小</td>
                <td width="25%">创建人</td>
                <td>创建时间</td>
              </tr>
              </thead>
              <%--此类别容器--%>
              <tbody class="generalFolder"></tbody>
            </table>
          </header>
        </section>
      </div>
    </div>
  </div>
  <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/publicInfo/publicMenu.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
