<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/cashBack/cashBack.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/orderManege.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    html,.ty-mainData{background: #fff; }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%-- ajax Loading --%>
<div class="zhe_AjaxPic">
    <div class="zhe_circleCon">
        <h3 class="zhe_loadingTip">正在加载 . . . </h3>
    </div>
</div>
<div class="bounce_Fixed3">
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签：</span>
                    <span class="sale-con" id="see_contactTag"></span>
                </div>
            </div>
            <%--<p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>--%>
            <table class="see_otherContact ty-table" style="width:442px;">
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" onkeyup="countWords($(this),20)"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
    <%-- 订购信息展示 --%>
    <div class="bonceContainer bounce-red" id="linkOrd">
        <div class="bonceHead">
            <span class="noGs">订购信息</span>
            <span class="ysGs">！ 重要提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="noGs">当前该商品无 <span class="ty-color-red">其他</span> 发货需求</div>
            <div class="ysGs">
                <p><span id="linkOrdName">内部名称</span> 当前虽有 <span id="linkOrdStock">当前库存</span> <span id="linkOrdUnit">计量单位</span> ，但该商品尚有如下发货需求：</p>
                <div style="max-height:300px; overflow: auto; ">
                    <table class="ty-table" id="linkOrdTb">
                        <tr>
                            <td width="10%">序号</td>
                            <td width="15%">订单号</td>
                            <td width="15%">负责人</td>
                            <td width="10%">数量</td>
                            <td width="15%">要求到货时间</td>
                            <td width="35%">客户名称</td>
                        </tr>
                        <tr>
                            <td>序号</td>
                            <td>订单号</td>
                            <td>负责人</td>
                            <td>数量</td>
                            <td>要求到货时间</td>
                            <td>客户名称</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">确定</span>
        </div>
    </div>

    <%-- 历史价格 --%>
    <div class="bonceContainer bounce-blue" id="cHistoryPrice" style="width: 874px; ">
        <div class="bonceHead">
            <span>历史价格</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="width: 80%; margin:0 auto; ">
            <p>以下为本商品最近的 <span class="cNum"></span>个价格。</p>
            <table class="ty-table ty-table-control ">
                <tr><td>商品价格</td><td>下单时间</td></tr>
            </table>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>

    <%-- 选择联系人--%>
    <div class="bonceContainer bounce-blue" id="chooseCusContact" style="width: 450px">
        <div class="bonceHead">
            <span>选择客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="background:#f0f8ff ">
            <input type="hidden" id="target">
            <div class="p0">
                <p style='text-align:center;'>暂无客户数据，请通过新增添加</p>
            </div>
            <div class="p1">
                <p>以下为可供选择的客户联系人</p>
                <ul class="cusList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="linkBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseCusContactOk()">确定</button>
        </div>
    </div>
    <%--系统提示--%>
    <div class="bonceContainer bounce-red" id="fix2MsTips">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p>本系统不支持同一订单上存在多种开票情况的商品。</p>
            <p>您已选择了<span class="msTxtTip"></span>，请继续选择同类型商品。</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 删除商品提示 --%>
    <div class="bonceContainer bounce-red" id="delGoodTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center ">
                <p>确定删除该行商品吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="delGoodTipOk()">确定</span>
        </div>
    </div>
    <%-- 开票要求 编辑--%>
    <div class="bonceContainer bounce-blue" id="invoiceSet">
        <div class="bonceHead">
            <span>开票要求</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-left:60px ">
                <p>本订单各商品需开具什么发票？请选择</p>
                <p class="setItem" data-type="1"><i class="fa fa-circle-o"></i><span>需开具增值税专用发票</span></p>
                <p class="setItem" data-type="2"><i class="fa fa-circle-o"></i><span>需开具其他发票</span></p>
                <p class="setItem" data-type="3"><i class="fa fa-circle-o"></i><span>不开发票</span></p>
                <p class="tipsmall">注：您新增的某订单上，只能录入开具同种发票的商品！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="invoiceSetOk()">确定</span>
        </div>
    </div>
    <%--收货地点功能说明--%>
    <div class="bonceContainer bounce-blue" id="reviceAddress">
        <div class="bonceHead">
            <span>收货地点功能说明</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p>选择商品时，页面上将带有此处选择的收货地点。</p>
            <p>您可按实际需要，对该页上的地点进行修改。</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--新增收货信息--%>
    <div class="bonceContainer bounce-green" id="newReceiveInfo" style="width: 450px">
        <div class="bonceHead">
            <span>收货信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">收货地址：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="ReceiveAddress" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">收货人：</span>
                <span class="sale_con1 chooseCusCon" data-target="#ReceiveName">
                <input type="text" style="width: 108px;" readonly placeholder="请选择" id="ReceiveName" require/>
                <span class="hd"></span>
                <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
            </span>
                <span class="linkBtn" onclick="addContactInfo(2)">新增</span>
            </p>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addReceive" onclick="addReceive()">提交</button>
        </div>
    </div>
    <%--新增联系人--%>
    <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
        <div class="bonceHead">
            <span>新增联系人</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <form id="newContectData">
                <p>
                    <span class="sale_ttl1">联系人标签：</span>
                    <span class="sale_gap" style="position:relative; top: 6px;" id="contactFlag"></span>
                </p>
                <p>
                    <span class="sale_ttl1">姓名：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                </p>
                <p>
                    <span class="sale_ttl1">手机：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactNumber" require/></span>
                    <a class="sale_ttl1 addMore" onclick="addMore($(this))">添加更多联系方式</a>
                    <select style="display: none;width: 167px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                        <option value="0"></option>
                        <option value="1">手机</option>
                        <option value="2">QQ</option>
                        <option value="3">Email</option>
                        <option value="4">微信</option>
                        <option value="5">微博</option>
                        <option value="9">自定义</option>
                    </select>
                </p>
                <ul class="otherContact">
                </ul>
                <div id="contactsCard">
                    <span class="sale_ttl1">名片：</span>
                    <div id="uploadCard" class="cardUploadBtn"></div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addContact" onclick="addContact()" data-name="addContact">提交</button>
        </div>
    </div>
    <%--新增商品 修改商品--%>
    <div class="bonceContainer bounce-green" id="newGood"  style="width:600px;">
        <div class="bonceHead">
            <span>新增商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="invoiceCatTip"></p>
            <div class="clearfix orderInfo">
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">商品代号 <i class="red">*</i>
                        <span class="ty-right linkBtn isZ" onclick="getContractDetail($(this))">所属的合同</span>
                        <span class="ty-right linkBtn isT" onclick="getHistoryPrice($(this))">历史价格</span>
                    </div>
                    <input type="text" class="ty-inputSelect outerSn">
                    <ul class="ty-optionCon"></ul>
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">商品名称</div>
                    <input type="text" class="ty-inputText outerName" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">订购数量 <i class="red">*</i></div>
                    <input type="text" class="ty-inputText goodNum" onkeyup="tofixed3(this)">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">计量单位</div>
                    <input type="text" class="ty-inputText unit" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">当前库存
                        <span class="linkBtn" style="position: relative;left: 27px" onclick="linkOrds($(this))">本商品的其他订购信息</span></div>
                    <input type="text" class="ty-inputText currentStock" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">最低库存</div>
                    <input type="text" class="ty-inputText minimumStock" disabled="disabled">
                </div>
                <%----------------------------------------%>
                <div class="orderItem orderItemMiddle invoice1">
                    <div class="orderItemTitle">税率 <i class="red redRequie modify">*</i></div>
                    <select class="ty-inputText rate" disabled="disabled" onchange="setprice(3)"></select>
                </div>
                <div class="orderItem orderItemMiddle invoice1">
                    <div class="orderItemTitle">不含税单价 <i class="red redRequie modify">*</i></div>
                    <input type="text" class="ty-inputText noPrice" disabled="disabled" onkeyup="clearNum(this); setprice(1)">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle"><span class="changeTtl">含税单价</span> <i class="red redRequie modify">*</i></div>
                    <input type="text" class="ty-inputText price" disabled="disabled" onkeyup="clearNum(this); setprice(2)">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">价格说明<span class="ty-right linkBtn modifyPrice isZ" data-fun="modifyPrice">临时调价</span></div>
                    <input type="text" class="ty-inputText priceMemo" disabled="disabled" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                </div>
                <%----------------------------------------%>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">要求到货日期 <i class="xing">*</i></div>
                    <input type="text" class="ty-inputText itemCon w600 requireDate"  value="" id="DateOfArrival"
                           name="DateOfArrival" autocomplete="off">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">货物件数</div>
                    <input type="text" class="ty-inputText goodNum_stock" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">收货地点 <i class="xing">*</i>
                        <span class="linkBtn ty-right" data-fun="newReceiveInfo2">新增收货地点</span>
                    </div>
                    <select type="text" class="ty-inputSelect receiveAddress receiveAddress2"></select>
                </div>
                <div class="orderItem orderItemMiddle hd">
                    <div class="invoiceTip"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="sureNewGood()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span class="ty-color-red">！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="limitTextTip">
                订单总额尚低于回款金额，请修改！
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
        <div class="bonceContainer bounce-red" id="clearInput" style="width:400px;">
            <div class="bonceHead">
                <span class="ty-color-red">！提示</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <p class="limitTextTip" type="1">您录入的金额有误，请检查后重新录入！</p>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-btn-red ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
            </div>
        </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="payHandle" style="width:1000px;">
        <div class="bonceHead">
            <span>回款处置</span>
            <a class="bounce_close" onclick="cancelDo()"></a>
        </div>
        <div class="bonceCon">
            <div id="amountAllocated">
                <ul class="clear allocatedDetail">
                    <li>
                        <span class="tt-row">客户名称</span>
                        <span id="mngCuster"></span>
                    </li>
                    <li>
                        <span class="tt-row">金额</span>
                        <span id="mngAmount"></span>
                    </li><%--method：1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐--%>
                    <li>
                        <span class="tt-row">收入方式</span>
                        <span id="mngIncome"></span>
                    </li>
                    <li class="cashType">
                        <span class="tt-row">收到日期</span>
                        <span id="mngCashGetDate"></span>
                    </li>
                    <li class="checkType">
                        <span class="tt-row">收到日期</span>
                        <span id="mngCkReciveDate"></span>
                    </li>
                    <li class="checkType">
                        <span class="tt-row">支票号</span>
                        <span id="mngCkSn"></span>
                    </li>
                    <li class="checkType">
                        <span class="tt-row">到期日</span>
                        <span id="mngCkDueDate"></span>
                    </li>
                    <li class="checkType">
                        <span class="tt-row">出具的单位</span>
                        <span id="mngCkUnit"></span>
                    </li>
                    <li class="checkType">
                        <span class="tt-row">出具的银行</span>
                        <span id="mngCkBank"></span>
                    </li>
                    <li class="billType">
                        <span class="tt-row">收到日期</span>
                        <span id="mngBlReciveDate"></span>
                    </li>
                    <li class="billType">
                        <span class="tt-row">汇票号</span>
                        <span id="mngBlSn"></span>
                    </li>
                    <li class="billType">
                        <span class="tt-row">到期日</span>
                        <span id="mngBlDueDate"></span>
                    </li>
                    <li class="billType">
                        <span class="tt-row">最初出具的单位</span>
                        <span id="mngBlUnit"></span>
                    </li>
                    <li class="billType">
                        <span class="tt-row">出具的银行</span>
                        <span id="mngBlBank"></span>
                    </li>
                    <li class="bankType">
                        <span class="tt-row">到账日期</span>
                        <span id="financeReceiveDate"></span>
                    </li>
                    <li class="bankType">
                        <span class="tt-row">收款银行</span>
                        <span id="financeReceiveBank"></span>
                    </li>
                </ul>
                <p class="manageTip">请根据实际情况，在某订单/合同后的“本笔回款中应属于该订单/合同的金额”栏内录入金额。</p>
                <table class="ty-table ty-table-control listPay">
                    <thead>
                    <tr>
                        <td>订单号/合同号</td>
                        <td>订单金额</td>
                        <td>已回款比例/金额</td>
                        <td>本笔回款中应属于该订单/合同的金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="smallPanel">
                    <span>您共已分配本笔回款中的 <span id="collected">0</span> 元,对于尚未分配的<span id="noneCollect"></span>元：</span>
                    <p class="ty-color-blue">注：贵公司业务如不涉及“订单”，请忽略“订单”两个选项。“合同”同理！</p>
                </div>
                <div>
                    <ul class="makeAgain">
                        <li sym="0" onclick="changeWay($(this))">
                            <i class="fa fa-square-o" val="1"></i>
                            立即补发新的订单
                        </li>
                        <li sym="0" onclick="changeWay($(this))">
                            <i class="fa fa-square-o" val="2"></i>
                            稍后补发新的订单
                        </li>
                        <%--<li sym="0" onclick="changeWay($(this))">
                            <i class="fa fa-square-o" val="2"></i>
                            立即补发新的合同
                        </li>
                        <li sym="0" onclick="changeWay($(this))">
                            <i class="fa fa-square-o" val="2"></i>
                            稍后补发新的合同
                        </li>--%>
                        <li sym="0" onclick="changeWay($(this))">
                            <i class="fa fa-square-o" val="5"></i>
                            属于客户多付的，需返还给客户，暂计入系统的常规借款中，返还事宜由财务负责处理
                        </li>
                    </ul>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelDo()">取 消</span>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" id="paySure" disabled onclick="payOrderType()">确 定</button>
        </div>
    </div>
    <%--新增订单--%>
    <div class="bonceContainer bounce-green" id="newOrder"  style="min-width: 1098px; width:90%; ">
        <div class="bonceHead">
            <span>新增订单</span>
            <a class="bounce_close" onclick="bounce.show($('#payHandle'))"></a>
        </div>
        <div class="bonceCon" style="max-height:450px; overflow: auto">

            <div class="orderDetail">
                <div class="flexcon">
                    <div><p>客户名称 <i class="red">*</i></p><input require class="form-control customerName"/> </div>
                    <div><p>订单号 <i class="red">*</i></p><input type="text" require class="form-control orderNumber" placeholder="请录入"></div>
                    <div><p>订单收到日期 <i class="red">*</i></p><input type="text" require id="OrderReceivedDate" class="form-control" placeholder="请选择"></div>
                    <div><p>客户代号 </p><input type="text" class="form-control customerId" disabled="disabled"></div>
                </div>
                <div class="flexcon">
                    <div>
                        <p>收货地点<i class="red">*</i>
                            <span class="linkBtn uphide" data-fun="newReceiveInfo">新增收货地点</span>
                            <span class="linkBtn uphide" data-fun="addressExplain">功能说明</span>
                        </p>
                        <select class="form-control receiveAddress receiveAddress1" require></select>
                    </div>
                    <div style="position: relative;">
                        <p>生产方的评审负责人<i class="red">*</i></p>
                        <select class="form-control principal " require ></select>
                        <span class=" uphide" style="position: absolute;left: 1px;top: 63px;color: #5a94ff;font-size: 0.7em;">注：需为多人时，订单需拆为多个录入</span>
                    </div>
                    <div><p>开票要求 <i class="red">*</i><span style="position: relative; left: 134px;" class="linkBtn uphide" data-fun="invoiceRequireEdit">编辑</span></p>
                        <input type="text" class="form-control invoiceRequire"  require readonly onclick="invoiceRequireEdit()">
                    </div>
                    <div><p>订单总额 </p><input type="text" class="form-control orderTotal" disabled="disabled"></div>
                </div>
            </div>
            <div class="orderList">
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5 newGood" onclick="newGoodBtn('add' , 2)" disabled="">选择专属商品</button>
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5 newGoodCommon" onclick="newGoodBtn('add' , 1)" disabled="">选择通用型商品</button>
                <table class="ty-table ty-table-control goodList canOrder">
                    <thead>
                    <tr>
                        <td>商品代号</td>
                        <td>商品名称</td>
                        <td>单价</td>
                        <td>计量单位</td>
                        <td>数量</td>
                        <td>要求到货日期</td>
                        <td>收货地点</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="gsBd">
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5 canOrder" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 canOrder" id="sureNewOrder" onclick="sureNewOrder()">保存</span>
        </div>
    </div>
</div>
<!--放内容的地方-->
<div class="collectDealCon" style="min-height:750px;">
    <div class="ty-container">
    </div>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/cashBack/payBackMng.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/cashBack/payBack.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/sales/orderManegeCommon.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
