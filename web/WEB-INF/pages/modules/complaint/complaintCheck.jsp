<%--
  Created by IntelliJ IDEA.
  User: 侯杏哲
  Date: 2017/8/11
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/complaint/complaint.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
    .bonceCon input,.bonceCon select,.bonceCon textarea {
        border:1px solid #dee8f0;
        background-color: #fff;
        line-height: 36px;
        text-align: left;
        padding: 0 8px;
        color: #3f3f3f;
        width: 180px;
    }
    .bonceCon textarea{
        line-height: 20px;
        padding:5px;
    }
    .bonceCon input{
        height: 36px;
    }
    .bonceCon input:disabled{
        background-color: #dff0ff;
    }
    .bonceCon input:focus,.bonceCon select:focus,.bonceCon textarea:focus{
        border:1px solid #5d9cec;
    }
    .hoverDetail{
        cursor: default;
    }
    .hoverDetailCon{
        position: absolute;
        top: 0;
        right: 50%;
        width: 300px;
        min-height: 120px;
        background-color: #fff;
        padding: 10px 20px 20px 20px;
        border:1px solid #ccc;
        box-shadow: 0 0 5px #ccc;
        display: none;
        text-align: left;
        z-index: 999;
    }
    .hoverDetail:hover .hoverDetailCon{
        display: block;
    }
    .radioBox{
        width: 220px;
        padding:5px 10px;
        margin: 0 auto;
    }
    .radioBox .ty-radio{
        background-color: #e8f0f5;
        margin-bottom: 8px;
        padding:8px 16px;
        border-radius: 3px;
        cursor: pointer;
    }
    .radioBox .ty-radio i.fa{
        margin-right: 5px;
    }
    .radioDisabled,.radioDisabled .ty-radio{
        background-color: #d6dde7;
        cursor: not-allowed;
    }
    .radioBox .numOk.ty-radioActive{
        background-color: #5d9cec;
        color: #fff;
    }
    .radioBox .numNo.ty-radioActive{
        background-color: #ed5565;
        color: #fff;
    }
    .countAll{
        width: 100%;
        height: 35px;
        line-height: 35px;
        background-color: #ffffe1;
        border: 1px dashed #e6d8b6;
        padding: 0 15px;
        color: #b38f6c;;
        margin-top: 8px;
    }
    .applyAll{
        width: 100%;
        height: 35px;
        line-height: 35px;
        background-color: #f3f3f3;
        padding: 0 15px;
        color: #666;;
        margin-top: 8px;
        text-align: right;
    }
    .ty-panel{  background: none;    }
    .ty-colFileTree{ float: left ; max-height:700px; width:310px;    }
    .mar{ margin-left: 330px;  }
    .fa-folder+span{ width:100px; display:inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
    .departTree{  }
    .departTree>ul , .departTree>form{ width: 315px; padding:15px 0; margin-left: 10px; border:1px solid #ccc; position: relative;
        box-shadow: 0 0 3px #ccc;height:300px; overflow:auto;   }
    .departTree>form{ margin-left: 80px; margin-top:-72px;  }
    .departTree i.fa{  color: #4E9DFF; font-weight: bolder; margin:0 20px;   }
    .departTree li>div{ position: relative; padding:3px 0 3px 25px; height:30px;    }
    .departTree li>div:hover{ background: #eaeaea; box-shadow:0 0 2px #ccc; cursor: pointer; color: #333; font-weight: bold;     }
    .departTree li>div>span:hover{  }
    .departTree li>div>i.fa-angle-right ,.departTree li>div>i.fa-angle-down , .departTree li>div>i.fa-info{ position:absolute; left:-10px; font-size:16px;     }
    .departTree li>div>i.fa-plus-square:hover ,.departTree li>div>i.fa-minus-square:hover , .departTree li>div>i.fa-angle-right:hover{ color:#0075ff;    }
    .departTree li>div>i.fa-plus-square, .departTree li>div>i.fa-minus-square{ float: right;     }
    .departTree ul ul{ display: none;padding-left:15px;  }
    #scanSet { width:760px;  }
    .arrow{  color: #4E9DFF; font-size:50px; top:95px; width:75px; position: relative;    }
    #allRight .ty-gray , #nowRight .ty-gray { color:#aaa; }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--投诉查看--%>
    <div class="bonceContainer bounce-blue" id="seeComplaint" style="min-width:1000px;">
        <div class="bonceHead">
            <span>投诉查看</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix" style="max-height: 500px;overflow: auto">
            <p class="ty-panelHead">投诉详情</p>
            <div class="cp_scan_box">
                <div class="formItem">
                    <div class="formTitle"><span><span class="ty-color-red">*</span> 客户</span></div>
                    <div class="formCon"><div class="cp_scan cp_customerName"></div></div>
                    <div class="formTitle"><span>客户代号</span></div>
                    <div class="formCon"><div class="cp_scan cp_customerCode"></div></div>
                    <div class="formTitle"><span>客户联系人</span></div>
                    <div class="formCon"><div class="cp_scan cp_customerContact"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>接到投诉的日期</span></div>
                    <div class="formCon"><div class="cp_scan cp_acceptDate"></div></div>
                    <div class="formTitle"><span>联系方式</span></div>
                    <div class="formCon"><div class="cp_scan cp_mobile"></div></div>
                    <div id="cp_processor">
                        <div class="formTitle"><span>投诉处理者</span></div>
                        <div class="formCon"><div class="cp_scan cp_handlerName"></div></div>
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>投诉内容</span></div>
                    <div class="formCon"><div class="cp_scan cp_content" style="width: 780px"></div></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>备注</span></div>
                    <div class="formCon"><div class="cp_scan cp_memo" style="width: 780px"></div></div>
                </div>
            </div>
            <%--附件--%>
            <div class="cp_fileInfo"></div>
            <%--补充材料--%>
            <div class="cp_scan_annex"></div>
            <%--立案驳回--%>
            <div class="cp_registerReject"></div>
            <%--结案详情--%>
            <div class="cp_caseInfo"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</button>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-header">
                <h3>投诉查看</h3>
                <p>
                    <span class="navTxt"><i class="fa fa-home"></i> 投诉管理</span>
                    <span class="nav_"> / </span>
                    <span class="navTxt"><i class=""></i><a onclick="goback()">投诉查看</a></span>
                </p>
            </div>
            <div class="ty-container mainPage">
                <ul class="ty-secondTab">
                    <li class="ty-active">立案待审批</li>
                    <li>立案驳回</li>
                    <li>立案通过</li>
                    <li>结案待审批</li>
                    <li>结案驳回</li>
                    <li>结案通过</li>
                </ul>
                <div class="ty-mainData">
                    <%--立案待审批--%>
                    <div class="tblContainer">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>客户</td>
                                <td>客户代号</td>
                                <td>客户联系人</td>
                                <td>联系方式</td>
                                <td>接到投诉的日期</td>
                                <td>录入者</td>
                                <td>录入时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--立案驳回--%>
                    <div class="tblContainer" style="display: none;">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>客户</td>
                                <td>客户代号</td>
                                <td>客户联系人</td>
                                <td>联系方式</td>
                                <td>接到投诉的日期</td>
                                <td>录入者</td>
                                <td>录入时间</td>
                                <td>审批时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--立案通过--%>
                    <div class="tblContainer" style="display: none;">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>客户</td>
                                <td>客户代号</td>
                                <td>客户联系人</td>
                                <td>联系方式</td>
                                <td>接到投诉的日期</td>
                                <td>录入者</td>
                                <td>录入时间</td>
                                <td>投诉处理人</td>
                                <td>审批时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--结案待审批--%>
                    <div class="tblContainer" style="display: none;">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>客户</td>
                                <td>客户代号</td>
                                <td>客户联系人</td>
                                <td>联系方式</td>
                                <td>接到投诉的日期</td>
                                <td>录入者</td>
                                <td>录入时间</td>
                                <td>投诉处理人</td>
                                <td>结案说明</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--结案驳回--%>
                    <div class="tblContainer" style="display: none;">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>客户</td>
                                <td>客户代号</td>
                                <td>客户联系人</td>
                                <td>联系方式</td>
                                <td>接到投诉的日期</td>
                                <td>录入者</td>
                                <td>录入时间</td>
                                <td>投诉处理人</td>
                                <td>审批时间</td>
                                <td>结案说明</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <%--结案通过--%>
                    <div class="tblContainer" style="display: none;">
                        <div class="ty-right flagTab">
                            <div class="loginQuery" style="position: relative;display: inline-block">
                                <span class="ty-btn ty-btn-big ty-circle-5 loginQuery" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" id="loginQueryBtn" value="4">自定义时间</span>
                                <ul class="dropdown-menu dropdown-menu-default searchCon"  >
                                    <div class="formItem">
                                        <div class="formTitle"><span>起时间</span></div>
                                        <div class="formCon"><input type="text" id="queryBeginTime"></div>
                                    </div>
                                    <div class="formItem">
                                        <div class="formTitle"><span>止时间</span></div>
                                        <div class="formCon"><input type="text" id="queryEndTime"></div>
                                    </div>
                                    <div class="formItem">
                                        <div class="formTitle"></div>
                                        <div class="formCon">
                                            <span class="ty-btn ty-btn-big ty-circle-3">取消</span>
                                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="getPassedCase(1,20,4)">查询</span>
                                        </div>
                                    </div>
                                </ul>
                            </div>
                            <div class="ty-btn-group" id="changeState">
                                <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-active-blue" value="1">本年</span>
                                <span class="ty-btn ty-btn-big ty-circle-5" value="2">前年</span>
                                <span class="ty-btn ty-btn-big ty-circle-5" value="3">去年</span>
                            </div>
                        </div>
                        <div class="passedPage">
                            <div class="tplContainer">
                                <div class="tip"></div>
                                <table class="ty-table ty-table-control" style="margin-top: 10px">
                                    <thead>
                                    <tr>
                                        <td>客户</td>
                                        <td>客户代号</td>
                                        <td>客户联系人</td>
                                        <td>联系方式</td>
                                        <td>接到投诉的日期</td>
                                        <td>录入者</td>
                                        <td>录入时间</td>
                                        <td>投诉处理人</td>
                                        <td>审批时间</td>
                                        <td>结案说明</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="ye_complaint_integrate"></div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/complaint/complaintCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/complaint/complaintCheck.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/downLoadFile/downLoadFile.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
