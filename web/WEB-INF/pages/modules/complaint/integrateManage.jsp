<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/complaint/complaint.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<%@ include  file="../../common/headerBottom.jsp"%>
</head>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%-- 新增投诉立案者--%>
    <div class="bonceContainer bounce-blue" id="newDivision">
        <div class="bonceHead">
            <span class="red">！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <div class="filing">
                    <p>请将<span class="red">可能接触到客户投诉信息</span>的同事选择为投诉的立案者。</p>
                    <p>立案者能见到其录入的立案申请，并可向您提交相应投诉的结案申请。</p>
                </div>
                <div class="handle">
                    <p>请将<span class="red">能全权处理客户投诉</span>的同事选择为投诉的处理者。</p>
                    <p>您可在系统中指定该处理者负责处理某个具体投诉。</p>
                </div>
            </div>
            <div class="ty-center">
                <select id="userList">
                    <option value="">—— 请选择 ——</option>
                </select>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="sureNewDivisionBtn" onclick="sureNewDivision()">确定</button>
        </div>
    </div>
    <%-- 新增投诉/选择商品 --%>
    <div class="bonceContainer bounce-green" id="selectGoods" style="min-width:1000px;">
        <div class="bonceHead">
            <span>选择问题产品</span>
            <a class="bounce_close" onclick="cancelSubmitfilepro()"></a>
        </div>
        <div class="bonceCon" style="max-height: 500px;overflow: auto">
            <div class="main">
                <input type="hidden" id="editGsType">
                <table>
                    <tr>
                        <td>
                            <input type="hidden" id="gsID">
                            <input type="hidden" id="mtID">
                            <span>商品代号<span class="xing">*</span></span>
                            <select id="outSn" onchange="resetGsList('outSn')"></select>
                        </td>
                        <td>
                            <span>商品名称<span class="xing">*</span></span>
                            <select id="outName" onchange="resetGsList('outName')"></select>
                        </td>
                        <td>
                            <span>产品图号<span class="xing">*</span></span>
                            <select id="inSn" onchange="resetGsList('inSn')"></select>
                        </td>
                        <td>
                            <span>产品名称<span class="xing">*</span></span>
                            <select id="inName" onchange="resetGsList('inName')"></select>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4" class="left lit">
                            ！您在内商品代号或名称中选择任何一项，均可见到向 <span class="red"></span> 销售的所有产品。
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4" class="left">
                            <span>问题产品可能的签收日期</span>
                            <select id="outPutTime" onchange="setOutPutTime()"></select>
                            <span id="timeCon">
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4" class="left">
                            <span>问题产品的数量或比例</span>
                            <input type="text" placeholder="数量" onkeyup="clearNoNum(this)" id="pro_amount"/>
                            <span>或</span>
                            <input type="text" placeholder="比例" onkeyup="clearNoNum(this)" id="pro_percent"/> %
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <span>问题描述</span>
                            <textarea id="pro_problem"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <span>后果分析</span>
                            <textarea id="pro_consequence"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <span>顾客要求/解决时限</span>
                            <textarea id="pro_appeal"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <span>备注</span>
                            <textarea id="pro_memo"></textarea>
                        </td>
                    </tr>
                    <%-- eq(9) --%>
                    <tr class="center">
                        <td colspan="4">
                            <div class="fileCon">
                                <div>
                                    <span>附件</span>
                                    <div>
                                        <div id="fileUploadProBtn" class="upBtn"></div>
                                    </div>
                                </div>
                                <div>
                                    <span>照片</span>
                                    <div>
                                        <div id="imgUploadProBtn" class="upBtn"></div>
                                    </div>
                                </div>
                            </div>

                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="cancelSubmitfilepro()">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="addGoods" onclick="addBtn(1)">确定</button>
        </div>
    </div>
    <%-- 清空客户提示 --%>
    <div class="bonceContainer bounce-red" id="changeCus">
        <div class="bonceHead">
            <span class="red">!提示</span>
            <a class="bounce_close" onclick="emptyCus(0)"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>清空客户会导致之前您填写的所有内容全部清空，</div>
            <div>您确定要清空客户？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="emptyCus(0)">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="emptyCus(1)">确定</span>
        </div>
    </div>
    <%-- 停权立案者或者处理者 --%>
    <div class="bonceContainer bounce-red" id="stopRight">
        <div class="bonceHead">
            <span class="red">!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="stopRightTip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="stopRightOk()">确定</span>
        </div>
    </div>
    <%-- 删除投诉的商品提示 --%>
    <div class="bonceContainer bounce-red" id="delGsTip">
        <div class="bonceHead">
            <span class="red">!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tip">您确定要删除投诉的该商品？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="delGsOk()">确定</span>
        </div>
    </div>

</div>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>投诉管理</span>
                <i class="fa fa-close"></i>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container ">
                <div class="divisionPage" style="display: none">
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-left"  onclick="goBack('division')">返回</span>
                    <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right" id="queryBtn" onclick="newDivisionBtn()">新增</span>
                </div>
                <div class="clr"></div>
                <br>
                <%-- 主页面 --%>
                <div class="mainPage">
                    <div class="ty-mainData">
                        <div class="control">
                            <div class="isHe">
                                <p>
                                    您可在分工设置中对投诉的分工进行设置、查看或修改。
                                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="divisionSettingBtn" onclick="divisionSettingBtn()">分工设置</span>
                                </p>
                                <p>需录入投诉时，请填写下表各项内容后提交。</p>
                            </div>
                            <div class="notHe">
                                <p>有投诉需要录入时，您按实际情况在此录入后点击提交即可。</p>
                            </div>
                        </div>
                        <div class="main">
                            <table>
                                <tr>
                                    <td>
                                        <span>客户<span class="xing">*</span></span>
                                        <select id="cusID" onchange="chargeCus()">
                                        </select>
                                        <input type="hidden" id="cusName">
                                        <input type="hidden" id="cusIdOld">
                                    </td>
                                    <td>
                                        <span>客户代号</span>
                                        <input type="text" id="cusCode" readonly/>
                                    </td>
                                    <td>
                                        <span>接到投诉的日期</span>
                                        <input type="text" id="receiptTime"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>客户联系人</span>
                                        <input type="text" id="contactName"/>
                                    </td>
                                    <td>
                                        <span>联系方式</span>
                                        <input type="text" id="contactPhone"/>
                                    </td>
                                    <td>
                                        <span>职位</span>
                                        <input type="text" id="post"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <span>客户联系人说明</span>
                                        <input class="bigCon" type="text" id="contact"/>
                                    </td>
                                    <td class="isHe">
                                        <span>投诉处理者<span class="xing">*</span></span>
                                        <select id="charger"></select>
                                    </td>
                                </tr>
                                <tr class="blueTr"><td colspan="3"><div></div></td></tr>
                                <%-- eq(4) --%>
                                <tr class="chargePro center">
                                    <td colspan="3">
                                        <div>
                                            <input type="hidden" id="type">
                                            <span>投诉与具体产品无关？</span>
                                            <span class="ty-btn ty-btn-blue ty-circle-3" onclick="addBtn(0)">直接录入投诉内容</span>
                                        </div>
                                        <div>
                                            <span>投诉与具体产品有关？</span>
                                            <span class="ty-btn ty-btn-blue ty-circle-3" onclick="selectBtn()">选择问题产品</span>
                                        </div>
                                    </td>
                                </tr>
                                <%-- eq(5) --%>
                                <tr class="noPro">
                                    <td colspan="3">
                                        <span>顾客抱怨/问题描述</span>
                                        <textarea id="problem"></textarea>
                                    </td>
                                </tr>
                                <tr class="noPro">
                                    <td colspan="3">
                                        <span>后果分析</span>
                                        <textarea id="consequence"></textarea>
                                    </td>
                                </tr>
                                <tr class="noPro">
                                    <td colspan="3">
                                        <span>顾客要求/解决时限</span>
                                        <textarea id="appeal"></textarea>
                                    </td>
                                </tr>
                                <tr class="noPro">
                                    <td colspan="3">
                                        <span>备注</span>
                                        <textarea id="memo"></textarea>
                                    </td>
                                </tr>
                                <tr class="noPro center ">
                                    <td colspan="3">
                                        <div class="fileCon" id="nop">
                                            <div>
                                                <span>附件</span>
                                                <div>
                                                    <div id="fileUploadBtn" class="upBtn"></div>
                                                </div>
                                            </div>
                                            <div>
                                                <span>照片</span>
                                                <div>
                                                    <div id="imgUploadBtn" class="upBtn"></div>
                                                </div>
                                            </div>
                                            <p style="clear: both;"></p>
                                        </div>

                                    </td>
                                </tr>
                                <tr class="noPro">
                                    <td colspan="3">
                                        <p>&nbsp;</p>
                                        <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-3" onclick="submitBtn(0)">取消</span>
                                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="submitBtn(1)">提交</span>
                                    </td>
                                </tr>
                                <tr class="yesPro center ">
                                    <td colspan="3">
                                        <div>
                                            <span class="bigttl">问题产品清单</span>
                                            <span class="ty-btn ty-btn-blue ty-circle-3" onclick="selectBtn(1)">增加其他问题产品</span>
                                        </div>
                                        <div></div>
                                    </td>
                                </tr>
                                <%-- eq(11) --%>
                                <tr class="yesPro center ">
                                    <td colspan="3">
                                        <table class="ty-table ty-table-control" id="goodsList">
                                            <tr>
                                                <td>商品代号</td>
                                                <td>商品名称</td>
                                                <td>产品图号</td>
                                                <td>产品名称</td>
                                                <td>操作</td>
                                            </tr>
                                            <tr>
                                                <td>商品代号</td>
                                                <td>商品名称</td>
                                                <td>产品图号</td>
                                                <td>产品名称</td>
                                                <td>
                                                    <span class="ty-color-blue">修改</span>
                                                    <span class="ty-color-red">修改</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="yesPro">
                                    <td colspan="3">
                                        <p>&nbsp;</p>
                                        <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-3" onclick="submitBtn(2)">取消</span>
                                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="submitBtn(3)">提交</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <%--分工设置--%>
                <div class="divisionPage" style="display: none">
                    <ul class="ty-secondTab">
                        <li class="ty-active" code="filing">立案者</li>
                        <li code="handle">处理者</li>
                    </ul>
                    <div class="ty-mainData">
                        <%--立案者列表--%>
                        <div class="tblContainer">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>姓名</td>
                                    <td>性别</td>
                                    <td>手机号</td>
                                    <td>部门</td>
                                    <td>职位</td>
                                    <td>录入时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <%--立案者列表--%>
                        <div class="tblContainer" style="display: none;">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>姓名</td>
                                    <td>性别</td>
                                    <td>手机号</td>
                                    <td>部门</td>
                                    <td>职位</td>
                                    <td>录入时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <%--立案者列表--%>
                        <div class="tblContainer" style="display: none;">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>姓名</td>
                                    <td>性别</td>
                                    <td>手机号</td>
                                    <td>部门</td>
                                    <td>职位</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                    <div id="ye_divisionSetting"></div>
                </div>
            </div>

        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/complaint/integrateManage.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
