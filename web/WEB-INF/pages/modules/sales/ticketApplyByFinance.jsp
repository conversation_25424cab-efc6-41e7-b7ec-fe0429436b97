<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/ticketApplyByFinance.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<%@ include  file="../../common/headerBottom.jsp"%>
</head>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="zhe_AjaxPic">
    <div class="zhe_circleCon">
        <h3 class="zhe_loadingTip">正在加载 . . . </h3>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-red" id="backTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="ty-center">
                <p>确定放弃已录入的数据吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="ticketApplyBack() ">确 定</span>
        </div>
    </div>
    <%-- 客户的开票资料--%>
    <div class="bonceContainer bounce-blue" id="InvoiceMessage" style="width:1100px;">
        <div class="bonceHead">
            <span>客户的开票资料</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="sale_c">
                <div class="sale_ttl1">公司名称：</div>
                <div class="sale_con1">
                    <span class="invoiceName"></span>
                </div>
                <div class="sale_ttl1">地址：</div>
                <div class="sale_con2">
                    <span class="invoiceAddress"></span>
                </div>
                <div class="sale_ttl1">电话：</div>
                <div class="sale_con">
                    <span class="telephone"></span>
                </div>
            </div>
            <div class="sale_c">
                <div class="sale_ttl1">开户行：</div>
                <div class="sale_con1">
                    <span class="bankName"></span>
                </div>
                <div class="sale_ttl1">账号：</div>
                <div class="sale_con1" >
                    <span class="bank_no"></span>
                </div>
                <div class="sale_ttl1">税号：</div>
                <div class="sale_con">
                    <span class="taxpayerID"></span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--已录入的发票--%>
    <div class="bonceContainer bounce-blue" id="ticketEnteredLog" style="width:800px; ">
        <div class="bonceHead">
            <span>已录入的发票</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                客户名称：<span class="cusName mar"></span>
                客户代号：<span class="cusNo mar"></span>
                <span class="ticketCat"></span>
            </p>
            <p>已录入<span class="entryedNum"></span>张发票，具体如下：</p>
            <table class="ty-table ty-table-control">
                <tbody>
                <tr>
                    <td width="20%">发票号码</td>
                    <td width="20%">开票日期</td>
                    <td width="15%">发票金额</td>
                    <td width="15%">行数</td>
                    <td width="30%">操作</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关 闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--系统提示--%>
    <div class="bonceContainer bounce-red" id="delInvoiceMessage">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>确定后，该张发票上的数据将全部释放。</p>
                <p>确定删除本条吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="delInvoiceMessageSure()">确 定</span>
        </div>
    </div>
    <%--已开发票的信息--%>
    <div class="bonceContainer bounce-blue" id="entryInvoiceMessage" style="width:800px; ">
        <div class="bonceHead">
            <span>已开发票的信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="detailCon">以下商品共<span></span>种，含税总额<span></span>元。请选择所对应发票的号码与开票日期。</p>
            <table class="ty-table" id="goodsDetails">
                <tr>
                    <td width="10%">序号</td>
                    <td width="16%">商品名称</td>
                    <td width="10%">单位</td>
                    <td width="12%">数量</td>
                    <td width="12%">单价</td>
                    <td width="12%">金额</td>
                    <td width="12%">税率</td>
                    <td width="14%">税额</td>
                </tr>
            </table>
            <div class="selectSect">
                发票号码
                <select id="tickets">
                    <option value="">请选择</option>
                </select>
                发票上的开票日期
                <input id="invoiceDate" class="ty-right" placeholder="请选择" value=""/>

            </div>
            <div class="checkAssistance clear">
                <span>如需要，可将发票上的价税合计录入，有利于进一步核对。如不需要，可忽略</span>
                <input id="priceTaxTotal" class="ty-right" placeholder="录入价税合计" value=""/>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="invoiceMessageSure()">确 定</span>
        </div>
    </div>
        <%--编辑发票信息--%>
    <div class="bonceContainer bounce-blue" id="editInvoiceMessage" style="width:800px; ">
        <div class="bonceHead">
            <span>编辑发票信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="detailCon">在本页面可根据实际情况，重新选定发票号码，或修改开票日期。</p>
            <table class="ty-table" id="e_goodsDetails">
                <tr>
                    <td width="10%">序号</td>
                    <td width="16%">商品名称</td>
                    <td width="10%">单位</td>
                    <td width="12%">数量</td>
                    <td width="12%">单价</td>
                    <td width="12%">金额</td>
                    <td width="12%">税率</td>
                    <td width="14%">税额</td>
                </tr>
            </table>
            <div class="selectSect">
                发票号码
                <select id="e_tickets">
                    <option value="">请选择</option>
                </select>
                发票上的开票日期
                <input id="e_invoiceDate" class="ty-right" placeholder="请选择" value=""/>

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="e_invoiceMessageSure()">确 定</span>
        </div>
    </div>
</div>
<%-- 开票申请单主页面 --%>
<div class="ty-mainData">
    <div class="centerCon" id="mainshow">
        <div class="formHead">
            <h4 id="title">开票申请单</h4>
            <p class="p-right">编号：<span>123456</span></p>
            <div class="clear">
                <div class="hd" id="applyDetail"></div>
                <div class="ty-left cusInfo">
                    客户：<span id="ticketCustomerName"></span>/<span id="ticketCode"></span>
                </div>
                <span class="ty-left careful" onclick="getInvoiceMessage()">客户的开票资料</span>
                <div class="ty-right">
                    <div>申请：<span id="ticketApplyer"></span></div>
                    <p>审批：<span id="ticketApprover"></span></p>
                </div>
            </div>
            <div class="clear">
                <div class="ty-left">
                    <div>
                        <span class="mar">订单号：<span id="ticketSn"></span></span>
                        <span id="ticketType" class="mar"></span>
                        <span class="mar">申请开票总金额：<span id="ticketTotal"></span>元</span>
                    </div>
                </div>
                <div class="ty-right">
                    <span id="enteredInfo"></span>
                    <span class="careful" onclick="getTempTickets()">查看</span>
                </div>
            </div>
        </div>
        <div class="ticketOpen">
            <div class="clear">
                <div class="ty-left tipCare">提示： </div>
                <div class="ty-left">
                    <p>以下为需要开具发票的货物清单。发票开好后，需将发票信息录入至系统。您可从任何一张开好的发票开始。</p>
                    <p>在下表中逐一勾选某张发票上的货物并录入数量，进入下一步后，录入发票号码与开票日期。反复操作，直至发票信息全部录入至系统。</p>
                </div>
            </div>
            <div class="ty-panel">
                <div class="tpl">
                    <table class="ty-table" id="ticketTab">
                        <thead>
                        <tr>
                            <td width="5%" class="tdOrg">勾选</td>
                            <td width="5%">商品代号</td>
                            <td width="5%">商品名称</td>
                            <td width="4%">单位</td>
                            <td width="4%">数量</td>
                            <td width="8%">单价</td>
                            <td width="8%">申请总数</td>
                            <td width="8%">已开总数</td>
                            <td width="8%">本张发票上的数量</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="goodsSelect">
                    <div class="checkSect">
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="1"></i>本张发票上的货物已选完，进入下一步：录入该张发票的号码、开票日期与金额。
                        </div>
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="0"></i>本次开票申请中的货物均已开具发票，且发票信息已全部录入至系统，任务已完成。
                        </div>
                    </div>
                    <div class="clear">
                        <span class="ty-right ty-btn ty-btn-big ty-btn-blue ty-circle-5 mar" onclick="ticketApplyByFinanceSure()">确 定</span>
                        <span class="ty-right ty-btn ty-btn-big ty-circle-5" onclick="bounce.show($('#backTip'))">取 消</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/sales/ticketApplyByFinance.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>
</body>
</html>
