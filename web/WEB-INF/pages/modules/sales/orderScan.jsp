<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/orderScan.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="ty-tip" id="tipCon">
    <div class="ty-tipcon"  >
        <div class="ty-trigl-1"><span></span></div>
        <div id="tipitem"></div>
    </div>
</div>
<div class="bounce">
    <%-- 一级提示框 --%>
    <div class="bonceContainer bounce-red" id = "tip" >
        <div class="bonceHead" >
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
             <p class="tip" id="tipMs"></p>
        </div>
        <div class="bonceFoot" >
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5 request" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id = "tjTip" >
        <div class="bonceHead" >
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p style="text-align:center;" id="tjTipMs"></p>
        </div>
        <div class="bonceFoot" >
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 request" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 处理的详情 --%>
    <div class="bonceContainer bounce-green" id = "detail" >
        <div class="bonceHead">
            <span>处理记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div class="ordInfo_item">
                <p>客户名称</p><span class="cusName"> </span>
            </div>
            <div class="ordInfo_item">
                <p>客户代号</p><span id="cusCode"> </span>
            </div>
            <div class="ordInfo_item">
                <p>订单号</p><span id="ordSn"> </span>
            </div>
            <div class="ordInfo_item">
                <p>订单收到日期</p><span id="ordSignDate"> </span>
            </div>
            <div class="ordInfo_item">
                <p>录入者</p><span id="ordCreator"> </span>
            </div>
            <div class="ordInfo_item">
                <p>录入时间</p><span id="ordCreatDate"> </span>
            </div>
            <div class="clr"></div>
            <div>
                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-right" id="addGoods">新增商品</span>
                <div class="ty-dropdown">
                    <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-dropdownBtn " target="roleCon_update" id="setRole_update" onclick="setRoleBtn( $(this) )" >筛 选</span>
                    <div class="ty-dropdownCon" id="roleCon_update" >
                        <div class="ty-trigl-1"><span></span></div>
                        <div>
                            <div class="orderItemTiny"><i isSet="1" code="1" class="fa fa-dot-circle-o"></i>商品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="2" class="fa fa-circle-o"></i>外部名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="3" class="fa fa-circle-o"></i>产品图号</div>
                            <div class="orderItemTiny"><i isSet="0" code="4" class="fa fa-circle-o"></i>内部名称</div>
                        </div>
                        <div>
                            <div class="orderItemTiny"><i isSet="0" code="5" class="fa fa-square-o"></i>商品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="6" class="fa fa-square-o"></i>外部名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="7" class="fa fa-square-o"></i>产品图号</div>
                            <div class="orderItemTiny"><i isSet="0" code="8" class="fa fa-square-o"></i>内部名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="9" class="fa fa-square-o"></i>单位</div>
                            <div class="orderItemTiny"><i isSet="0" code="10" class="fa fa-square-o"></i>含税单价</div>
                            <div class="orderItemTiny"><i isSet="0" code="11" class="fa fa-check-square-o"></i>备注</div>
                        </div>
                        <div>
                            <span class="ty-btn ty-circle-3" onclick="cancelSert($('#setRole_update'))">取消</span>
                            <span class="ty-btn ty-btn-green ty-circle-3" onclick="setRole($('#goodsInfo') , $('#setRole_update') , 'roleCon_update')">确定</span>
                        </div>
                    </div>
                </div>
            </div>
            <table class = "ty-table ty-table-control ">
                <thead>
                    <tr>
                        <td width = "6%">序号</td>
                        <td width = "8%">商品代号</td>
                        <td width = "6%">数量</td>
                        <td width = "10%">要求到货日期</td>
                        <td width = "10%">能否按时到货</td>
                        <td width = "10%">按期到货数量</td>
                        <td width = "14%">剩余数量到货日期</td>
                        <td width = "14%">处理时间</td>
                        <td width = "7%">处理人</td>
                        <td width = "7%">备注</td>
                        <td width = "8%">操作</td>
                    </tr>
                </thead>
                <tbody id = "goodsInfo">

                </tbody>
            </table>
    </div>
        <div class="bonceFoot" style = "background:#f0f8ff">
            <button class="ty-btn  ty-btn-big ty-circle-5 request" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5 request" id="charge" onclick="charge()">提交</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="sure" onclick="sure()">确定</button>
        </div>
</div>
    <%-- 订单评审 --%>
    <div class="bonceContainer bounce-blue" id="saleOrderReview" style="width: 1090px;">
        <div class="bonceHead">
            <span>订单评审</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div class="ordInfoElem clear">
                <div class="ordInfo_l">
                    <span>客户名称</span><p class="cusName"> </p>
                </div>
                <div class="noGap">
                    <span>客户代号</span><p class="cusCode"> </p>
                </div>
                <div>
                    <span>生产方的评审负责人</span><p class="ordprincipal"> </p>
                </div>
                <div>
                    <span>订单收到日期</span><p class="ordSignDate"> </p>
                </div>
                <div class="noGap">
                    <span>订单号</span><p class="ordSn"> </p>
                </div>
            </div>
            <div class="ordInfoElem clear middCon">
                <div>订单由<span class="ordCreator"></span>于<span class="ordCreatDate"></span>创建</div>
                <div class="ordInfo_l">订单内共<span id="ordSums"></span>条数据</div>
                <div><span id="byReviewed"></span>提交的生产评审结果中</div>
                <div>能按时到货的<span id="abledSums"></span>条<span class="scanGoods blueLinkBtn" onclick="goodsScanList(1)">查看</span></div>
                <div class="noGap">不能按时到货的<span id="unabledSums"></span>条<span class="scanGoods blueLinkBtn" onclick="goodsScanList(0)">查看</span>
                    <span class="ty-right blueLinkBtn" onclick="goodsScanList(2)">查看全部</span>
                </div>
                <div class="hd ordGoodsList"></div>
            </div>
            <p>是否接受上述评审结果？请选择！</p>
            <div class="saleHandle">
                <div class="acceptDot"><span><i class="fa fa-circle-o gapR" data-icon="2"></i>接受，本订单正式生效。</span></div>
                <div>
                    <span><i class="fa fa-circle-o gapR" data-icon="3"></i>不接受，调整要货需求，之后重新评审。</span>
                    <div class="ty-right rtSect">
                        <span>调整后的要货需求</span>
                        <span class="scanGoods blueLinkBtn" onclick="adjustGoodsScan()">查看</span>
                        <span class="hd adjustGoodsDataScan"></span>
                        <span class="ty-right adjustGoods">调整要货需求</span>
                    </div>
                </div>
                <p class="ty-color-blue">注：调整要货需求前，建议先与客户沟通并达成一致！</p>
            </div>
            <div class="adjustGoodsData hd"></div>
    </div>
        <div class="bonceFoot" style = "background:#f0f8ff">
            <button class="ty-btn  ty-btn-big ty-circle-5 request" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="saleOrderReviewOk" onclick="saleOrderReviewOk()">确定</button>
        </div>
</div>
    <%-- 订单修改 --%>
    <div class="bonceContainer bounce-blue" id="chargeOrdInit" style="width: 400px;">
        <div class="bonceHead">
            <span>修改订单</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div class="narrowBody">
                <p>请选择要进行哪项修改：</p>
                <div class="checkWrap">
                    <div class="changeDot">
                        <span class="fa fa-circle-o" data-type="1"></span>修改商品的要货信息
                    </div>
                    <div class="changeDot">
                        <span class="fa fa-circle-o" data-type="2"></span>修改订单号或订单收到的日期
                    </div>
                    <div class="changeDot">
                        <span class="fa fa-circle-o" data-type="3"></span>更换生产方的评审负责人
                    </div>
                    <div class="changeDot">
                        <span class="fa fa-circle-o" data-type="4"></span>修改商品价格
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot" style = "background:#f0f8ff">
            <button class="ty-btn  ty-btn-big ty-circle-5 request" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chargeOrdInitOk()">确定</button>
        </div>
</div>
</div>
<div class = "bounce_Fixed" >
    <%-- 修改商品 --%>
    <div class="bonceContainer bounce-green" id="editGood1" style="width: 430px;">
        <div class="bonceHead">
            <span>编辑商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clearfix orderInfo" id="goodsCon">
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">商品代号</div>
                    <input type="text" class="ty-inputText outerSn">
                    <ul class="ty-optionCon"></ul>
                </div>
                <input type="hidden" id="customerProductId" class="ty-inputText customerProductId" disabled="disabled">
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">外部名称</div>
                    <input id="outName" type="text" class="ty-inputText outerName" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">产品图号</div>
                    <input type="text" id="innerSn" class="ty-inputText cInnerSn" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">内部名称</div>
                    <input type="text" id="innerName" class="ty-inputText cInnerSnName" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">单价</div>
                    <input type="text" class="ty-inputText unitPrice" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">计量单位</div>
                    <input type="text" class="ty-inputText unit" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">当前库存</div>
                    <input type="text" class="ty-inputText current_stock" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <%--<div class="orderItemTitle">可选数量</div>--%>
                    <%--<input type="text" class="ty-inputText canchoose_stock" disabled="disabled">--%>
                    <div class="orderItemTitle">&nbsp;</div>
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 linkOrds" onclick="linkOrds($(this))">相关订购信息</span>
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">最低库存</div>
                    <input type="text" class="ty-inputText minimumi_stock" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">数量 <i class="xing">*</i></div>
                    <input type="text" class="ty-inputText goodNum" onkeyup="clearNum(this)">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">货物件数</div>
                    <input type="text" class="ty-inputText goodNum_stock" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">要求到货日期 <i class="xing">*</i></div>
                    <input type="text" class="ty-inputText laydate-icon" value="" id="dateGoods">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">收货地点</div>
                    <select type="text" class="ty-inputSelect receiveAddress"></select>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <div id="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="submitGoods1()">确定</span>
        </div>
    </div>
    <%-- 调整要货需求 --%>
    <div class="bonceContainer bounce-blue" id="adjustGoods" style="width: 1530px;">
        <div class="bonceHead">
            <span>调整要货需求</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="orderList" style=" margin-top: 12px;">
                <div class="gapBt">
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 newGood" onclick="newGoodBtn('add' , 2)">增加专属商品</button>
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 newGoodCommon" onclick="newGoodBtn('add' , 1)">增加通用型商品</button>
                    <input value="" type="hidden" id="adjustSource" />
                </div>
                <table class="ty-table ty-table-control goReviewList">
                    <thead>
                    <tr>
                        <td rowspan="2">商品代号</td>
                        <td rowspan="2">商品名称</td>
                        <td rowspan="2">计量单位</td>
                        <td colspan="3">销售<span class="saleOrd"></span>的需求</td>
                        <td colspan="2">生产最近一次的评审（<span class="pudLastReview"></span>）</td>
                        <td colspan="3">本次需调整的数据（请仅对需调整的项目进行操作）</td>
                        <td rowspan="2">操作</td>
                    </tr>
                    <tr>
                        <td>订购数量</td>
                        <td>要求到货日期</td>
                        <td>收货地点</td>
                        <td>能按时到货的数量</td>
                        <td>剩余数量的到货日期</td>
                        <td>订购数量</td>
                        <td>要求到货日期</td>
                        <td>收货地点</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>
                            <span>分批发货</span>
                            <span>删除</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="adjustGoodsOk()">确定</span>
        </div>
    </div>
        <%--修改订单号或订单收到的日期--%>
        <div class="bonceContainer bounce-blue" id="updateOrderInfo"  style="width:1000px; ">
            <div class="bonceHead">
                <span>修改订单号或订单收到的日期</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon" style="max-height:450px; overflow: auto">
                <div class="orderDetail">
                    <div class="flexcon">
                        <div class="dbWrap"><p>客户名称</p>
                            <input type="text" id="cusSearchInput" class="form-control customerName" name="customer_name" disabled="disabled"/>
                        </div>
                        <div><p>订单号 <i class="red upSn">*</i></p><input type="text" name="sn" class="form-control orderNumber" placeholder="请录入"></div>
                        <div><p>订单收到日期 <i class="red upSn">*</i></p><input type="text" require id="OrderReceivedDate" class="form-control" name="sign_date" placeholder="请选择"></div>
                        <div><p>客户代号 </p><input type="text" class="form-control customerId" name="sn" disabled="disabled"></div>
                    </div>
                    <div class="flexcon">
                        <div class="dbWrap">
                            <p>收货地点</p>
                            <input type="text" class="form-control customerId" name="sn" disabled="disabled">
                        </div>
                        <div>
                            <p>生产方的评审负责人<i class="red upPl">*</i></p>
                            <select class="form-control principal " disabled="disabled" name="principal" ></select>
                            <span class="upPl uphide" style="color: #5a94ff;font-size: 0.7em;">注：需为多人时，订单需拆为多个录入</span>
                        </div>
                        <div><p>开票要求</p>
                            <input type="text" class="form-control invoiceRequire" name="invoice_require" require disabled="disabled">
                        </div>
                        <div><p>订单总额 </p><input type="text" class="form-control orderTotal" name="amount" disabled="disabled"></div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <div class="inputTip"></div>
                <span class="ty-btn ty-btn-big ty-circle-5 canOrder" onclick="bounce_Fixed.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 canOrder" id="sureNewOrder" onclick="updateOrderInfoSure()">确 定</span>
            </div>
        </div>
        <%-- 修改商品价格 --%>
        <div class="bonceContainer bounce-blue" id="editGoodPriceList" style="width: 1000px;">
            <div class="bonceHead">
                <span>修改商品价格</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <table class="ty-table" id="editGoodList">
                    <tr>
                        <td>商品代号</td>
                        <td>商品名称</td>
                        <td>单价</td>
                        <td>计量单位</td>
                        <td>数量</td>
                        <td>要求到货日期</td>
                        <td>收货地点</td>
                        <td>操作</td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
            </div>
        </div>
        <%-- 商品查看 --%>
        <div class="bonceContainer bounce-blue" id="goodsListScan" style="width: 1330px;">
            <div class="bonceHead">
                <span>商品查看</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="orderList" style=" margin-top: 12px;">
                    <table class="ty-table ty-table-control goodsScanList">
                        <thead>
                        <tr>
                            <td width="10%">商品代号</td>
                            <td width="10%">商品名称</td>
                            <td width="10%">计量单位</td>
                            <td width="10%">订购数量</td>
                            <td width="10%">要求到货日期</td>
                            <td width="20%">收货地点</td>
                            <td width="15%">能按时到货的数量</td>
                            <td width="15%">剩余数量的到货日期</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            </div>
        </div>
        <%-- 调整要货需求查看 --%>
        <div class="bonceContainer bounce-blue" id="adjustGoodsScan" style="width: 1530px;">
            <div class="bonceHead">
                <span>查看</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="orderList" style=" margin-top: 12px;">
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td rowspan="2">商品代号</td>
                            <td rowspan="2">商品名称</td>
                            <td rowspan="2">计量单位</td>
                            <td colspan="3">销售<span class="saleOrd"></span>的需求</td>
                            <td colspan="2">生产最近一次的评审（<span class="pudLastReview"></span>）</td>
                            <td colspan="3">本次需调整的数据（请仅对需调整的项目进行操作）</td>
                        </tr>
                        <tr>
                            <td>订购数量</td>
                            <td>要求到货日期</td>
                            <td>收货地点</td>
                            <td>能按时到货的数量</td>
                            <td>剩余数量的到货日期</td>
                            <td>订购数量</td>
                            <td>要求到货日期</td>
                            <td>收货地点</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
        <%--改为分批发货--%>
        <div class="bonceContainer bounce-blue" id="batchDelivery"  style="width:660px;">
            <div class="bonceHead">
                <span>改为分批发货</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="clearfix goodsInfo">
                    <p>本条商品的到货数量</p>
                    <div class="itemCn">
                        <div class="orderItemTitle">之前数据
                        </div>
                        <input type="text" class="form-control" id="recentAmount" disabled="disabled">
                    </div>
                    <div class="itemCn itemGap">
                        <div class="orderItemTitle">新数据</div>
                        <input type="text" class="form-control" placeholder="请录入" id="newAmount" />
                        <div class="ty-color-blue">注：到货数量如无变动，则无需录入！</div>
                    </div>
                    <div class="clr"></div>
                    <hr/>
                    <div class="itemCn">
                        <div class="orderItemTitle">请录入本条商品如何分批发货</div>
                    </div>
                    <div class="itemCn itemGap">
                        <div class="checkBtn grayBg"><span class="fa fa-circle-o"></span><span class="ty-color-red">本条商品</span>不再分批发货</div>
                    </div>
                    <div class="clr"></div>
                    <div class="text-right addMore">
                        <span class="ty-color-gray">增加行</span>
                    </div>
                    <div class="batchWrapper">
                        <div class="clear batchItem">
                            <div class="itemCn">
                                <div class="orderItemTitle">到货日期 <i class="xing">*</i></div>
                                <input type="text" class="form-control batchDeliveryDate1"  value="" placeholder="请录入"
                                       name="DateOfArrival" autocomplete="off">
                            </div>
                            <div class="itemCn itemGap">
                                <div class="orderItemTitle">该日期需到货的数量<i class="xing">*</i></div>
                                <input type="text" class="form-control batchAmount" name="amount" placeholder="请录入" />
                            </div>
                        </div>
                        <div class="clear batchItem">
                            <div class="itemCn">
                                <div class="orderItemTitle">到货日期 <i class="xing">*</i></div>
                                <input type="text" class="form-control batchDeliveryDate2"  value="" placeholder="请录入"
                                       name="DateOfArrival" autocomplete="off">
                            </div>
                            <div class="itemCn itemGap">
                                <div class="orderItemTitle">该日期需到货的数量<i class="xing">*</i></div>
                                <input type="text" class="form-control batchAmount" name="amount" placeholder="请录入" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" id="batchDeliveryOk" onclick="batchDeliveryOk()">确 定</button>
            </div>
        </div>
        <%-- 修改商品价格 --%>
        <div class="bonceContainer bounce-blue" id="newGood" style="width: 600px;">
            <div class="bonceHead">
                <span>修改商品价格</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="clearfix orderInfo">
                    <div class="orderItem orderItemMiddle">
                        <div class="orderItemTitle">商品代号 <i class="red">*</i>
                            <span class="ty-right blueLinkBtn isZ" onclick="getContractDetail($(this))">所属的合同</span>
                            <span class="ty-right blueLinkBtn isT" onclick="getHistoryPrice($(this))">历史价格</span>
                        </div>
                        <input type="text" class="ty-inputSelect outerSn">
                        <ul class="ty-optionCon"></ul>
                    </div>
                    <div class="orderItem orderItemMiddle">
                        <div class="orderItemTitle">商品名称</div>
                        <input type="text" class="ty-inputText outerName" disabled="disabled">
                    </div>
                    <div class="orderItem orderItemMiddle">
                        <div class="orderItemTitle">订购数量 <i class="red">*</i></div>
                        <input type="text" class="ty-inputText goodNum" onkeyup=" tofixed3(this)">
                    </div>
                    <div class="orderItem orderItemMiddle">
                        <div class="orderItemTitle">计量单位</div>
                        <input type="text" class="ty-inputText unit" disabled="disabled">
                    </div>
                    <div class="orderItem orderItemMiddle">
                        <div class="orderItemTitle">当前库存
                            <span class="blueLinkBtn ty-right" onclick="linkOrds($(this))">本商品的其他订购信息</span></div>
                        <input type="text" class="ty-inputText currentStock" disabled="disabled">
                    </div>
                    <div class="orderItem orderItemMiddle">
                        <div class="orderItemTitle">最低库存</div>
                        <input type="text" class="ty-inputText minimumStock" disabled="disabled">
                    </div>
                    <%----------------------------------------%>
                    <div class="orderItem orderItemMiddle invoice1 priceMod">
                        <div class="orderItemTitle">税率 <i class="red redRequie modify">*</i></div>
                        <select class="ty-inputText rate" disabled="disabled" onchange="setprice(3)"></select>
                    </div>
                    <div class="orderItem orderItemMiddle invoice1 priceMod">
                        <div class="orderItemTitle">不含税单价 <i class="red redRequie modify">*</i></div>
                        <input type="text" class="ty-inputText noPrice" disabled="disabled" onkeyup="clearNoNum(this); setprice(1)">
                    </div>
                    <div class="orderItem orderItemMiddle priceMod">
                        <div class="orderItemTitle"><span class="changeTtl">含税单价</span> <i class="red modify">*</i></div>
                        <input type="text" class="ty-inputText price" disabled="disabled" onkeyup="clearNoNum(this); setprice(2)">
                    </div>
                    <div class="orderItem orderItemMiddle priceMod">
                        <div class="orderItemTitle">价格说明<span class="ty-right linkBtn modifyPrice isZ" onclick="modifyPrice()">临时调价</span></div>
                        <input type="text" class="ty-inputText priceMemo" disabled="disabled" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    </div>
                    <%----------------------------------------%>
                    <div class="orderItem orderItemMiddle">
                        <div class="orderItemTitle">要求到货日期 <i class="xing">*</i></div>
                        <input type="text" class="ty-inputText itemCon w600 requireDate"  value="" id="DateOfArrival"
                               name="DateOfArrival" autocomplete="off">
                    </div>
                    <div class="orderItem orderItemMiddle">
                        <div class="orderItemTitle">货物件数</div>
                        <input type="text" class="ty-inputText goodNum_stock" disabled="disabled">
                    </div>
                    <div class="orderItem orderItemMiddle_l">
                        <div class="orderItemTitle">收货地点 <i class="xing">*</i>
                        </div>
                        <select type="text" class="ty-inputSelect receiveAddress receiveAddress2" onchange="receiveAddress2($(this))"></select>
                        <span class="hd"></span>
                    </div>
                    <div class="orderItem orderItemMiddle hd">
                        <div class="invoiceTip"></div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="submitGoods()">确定</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed3">
    <%-- 订购信息展示 --%>
    <div class="bonceContainer bounce-red" id="linkOrd">
        <div class="bonceHead">
            <span class="noGs">订购信息</span>
            <span class="ysGs">！ 重要提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="noGs">当前该商品无 <span class="ty-color-red">其他</span> 发货需求</div>
            <div class="ysGs">
                <p><span id="linkOrdName">内部名称</span> 当前虽有 <span id="linkOrdStock">当前库存</span> <span id="linkOrdUnit">计量单位</span> ，但该商品尚有如下发货需求：</p>
                <table class="ty-table" id="linkOrdTb">
                    <tr>
                        <td width="10%">序号</td>
                        <td width="15%">订单号</td>
                        <td width="15%">负责人</td>
                        <td width="10%">数量</td>
                        <td width="15%">要求到货时间</td>
                        <td width="35%">客户名称</td>
                    </tr>
                    <tr>
                        <td>序号</td>
                        <td>订单号</td>
                        <td>负责人</td>
                        <td>数量</td>
                        <td>要求到货时间</td>
                        <td>客户名称</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">确定</span>
        </div>
    </div>
        <%-- 合同查看 --%>
        <div class="bonceContainer bounce-blue" id="cScan" style="width: 874px; ">
            <div class="bonceHead">
                <span>查看合同</span>
                <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
            </div>
            <div class="bonceCon" style="width: 80%; margin:0 auto; ">
                <div class="nothas" style="text-align: center; line-height: 50px; ">
                    本商品没有所属的合同！
                </div>
                <div class="has">
                    <p>本版本合同的创建 <span class="create"></span></p>
                    <table class="ty-table ty-table-control leftTab">
                        <tr><td>合同编号</td><td class="cNos"></td></tr>
                        <tr><td>签署日期</td><td class="cSignDates"></td></tr>
                        <tr><td>合同的有效期</td><td class="cvalidDates"></td></tr>
                        <tr><td>合同的扫描件或照片</td><td class="cImgs"></td></tr>
                        <tr><td>合同的可编辑版</td><td>
                            <a class="ty-color-blue cWord node" data-fun="cWord">查看</a>
                        </td></tr>
                        <tr><td>本合同下的商品（共<span class="gNum"></span>种）</td><td class="cGoodss">
                            <span class="ty-color-blue node" data-fun="gNum">查看</span>
                        </td></tr>
                        <tr><td>备注</td><td class="cMemos"></td></tr>
                        <tr><td>本版本合同的修改记录</td><td class="cEditLog">
                            <span class="ty-color-blue node" data-fun="cEditLog">查看</span>
                        </td></tr>
                        <tr><td>本合同的续约记录</td><td class="cRenewalLog">
                            <span class="ty-color-blue node" data-fun="cRenewalLog">查看</span>
                        </td></tr>
                    </table>
                    <div class="enabledList">  </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed3.cancel()">关闭</span>
            </div>
        </div>
        <%--！提示--%>
        <div class="bonceContainer bounce-blue" id="changeAddressTip">
            <div class="bonceHead">
                <span>！提示</span>
                <a class="bounce_close" onclick="changeCancel()"></a>
            </div>
            <div class="bonceCon" style="text-align: center">
                <div class="msTip">该商品在该交货地点的价格需重新录入！</div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="changeCancel();">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="changeAddressNext()">下一步</span>
            </div>
        </div>
        <%-- 历史价格 --%>
        <div class="bonceContainer bounce-blue" id="cHistoryPrice" style="width: 874px; ">
            <div class="bonceHead">
                <span>历史价格</span>
                <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
            </div>
            <div class="bonceCon" style="width: 80%; margin:0 auto; ">
                <p>以下为本商品最近的 <span class="cNum"></span>个价格。</p>
                <table class="ty-table ty-table-control ">
                    <tr><td>商品价格</td><td>下单时间</td></tr>
                </table>

            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed3.cancel()">关闭</span>
            </div>
        </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>订单评审</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container contain">
                <div class="mainCon mainCon1">
                    <div class="clear gapBt">
                        <ul class="ty-secondTab ty-left">
                            <li class="ty-active" data-align="1">待销售评审的订单</li>
                            <li data-align="2">待生产评审的订单</li>
                        </ul>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5 ty-right">已终止评审的订单</span>
                    </div>
                    <table class="ty-table ty-table-control setList0">
                        <thead>
                        <tr>
                            <td width="10%">订单收到日期</td>
                            <td width="15%">客户名称</td>
                            <td width="15%">订单号</td>
                            <td width="15%">评审者</td>
                            <td width="15%">创建</td>
                            <td width="30%">操作</td>
                        </tr>
                        </thead>
                        <tbody>  </tbody>
                    </table>
                    <table class="ty-table ty-table-control setList1">
                        <thead>
                        <tr>
                            <td width="10%">订单收到日期</td>
                            <td width="15%">客户名称</td>
                            <td width="15%">订单号</td>
                            <td width="15%">评审者</td>
                            <td width="15%">创建</td>
                            <td width="30%">操作</td>
                        </tr>
                        </thead>
                        <tbody>  </tbody>
                    </table>
                </div>
                <div class="mainCon mainCon2">
                    <table class="ty-table ty-table-control setList1">
                        <thead>
                            <td width="20%">订单收到日期</td>
                            <td width="20%">订单号</td>
                            <td width="20%">客户名称</td>
                            <td width="20%">录入时间</td>
                            <td width="20%">处理</td>
                        </thead>
                        <tbody class="orderScan_t">
                            <tr>
                                <td width="20%">2016-08-01</td>
                                <td width="20%">000000000002</td>
                                <td width="20%">这个是特殊修改.分开点</td>
                                <td width="20%">2016-08-01 08:00:00</td>
                                <%--<td width="17%">备注</td>--%>
                                <td width="20%"><span class="ty-color-green" onclick="chargeOrd(1)">处理</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div id="ye_setList"></div>
                </div>
                <div class="clr"></div>
            </div>

        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/sales/orderScan.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
