<%--
  Created by IntelliJ IDEA.
  User: houxingzhe
  Date: 2023/6/12
  Time: 11:40
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
  <%-- 头部样式 --%>
  <link href="../css/common/theme/green.css?v=SVN_REVISION"  rel="stylesheet" type="text/css" >
    <link href="../assets/global/plugins/font-awesome/css/font-awesome.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

  <%--<%@ include  file="../../common/headerTop.jsp"%>--%>
<style>
  body{ color: #333; }
  .line{ border-bottom: 1px solid #ccc;  }
  table{ width:100%;
    border-collapse: collapse;
    border-spacing: 0;
  }
  td{ padding:5px 10px;  }
  .linkBtn{ color: #0b94ea; }
  .linkBtn:hover{  text-decoration: underline  }
  .line{
    border-top:1px solid #ccc;
    border-bottom:1px solid #ccc;
    padding: 10px;
    margin:10px 0;
  }
  .tt1{
    display: inline-block;
    width: 140px;
  }
  #cus1, #invoiceSet2{
    display: none;
  }
  .fa{
    color: #0b94ea;
    margin-right: 14px;
  }
  .sale-con{
    padding-left: 40px;
    color: #666;
  }
  .redFlag{
    color: #ea4747;
  }

</style>
</head>
<body>
<div class="bonceContainer bounce-blue" id="cus" style="width:600px; display: block; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span>客户的开票资料</span>
    <a class="bounce_close" onclick="closeIframe()"></a>
  </div>
  <div class="bonceCon" id="InvoiceMessage">
    <div>以下为在用资料：
      <div class="ty-right">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="edit()">修 改</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-type="invoiceRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span>
      </div>
      <table>
        <tr><td width="25%">名称：</td><td class="invoiceName"></td></tr>
        <tr><td>纳税人识别号：</td><td class="taxpayerID"></td></tr>
        <tr><td>地址、电话：</td><td><span class="invoiceAddress"></span><span class="telephone"></span></td></tr>
        <tr><td>开户行及账号：</td><td><span class="bankName"></span><span class="bank_no"></span></td></tr>
      </table>
      <div class="line">
        <span class="tt1">编辑人及时间：</span>
        <span class="editU"></span>
        <span class="andTime"></span>
      </div>
    </div>
  </div>
  <div class="bonceFoot">
    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeIframe()">关闭</span>
  </div>
</div>

<div class="bonceContainer bounce-blue" id="cus1" style="width:600px; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span>开票信息修改</span>
    <a class="bounce_close" onclick="closeK()"></a>
  </div>
  <div class="bonceCon" style="padding: 10px 15px 0;">
    <form id="updateInvoice">
      <p style="margin:5px 15px; text-align: right; ">
        <span style="margin: 0 20px 5px 0; ">客户对发票方面的要求</span>
        <span>尚未设置</span>
        <span class="linkBtn goset_update" data-type="update"  onclick="goset($(this))">去设置</span>
      </p>
      <table>
        <tr>
          <td>公司名称：<br/>
            <input class="invoiceName" data-name="invoiceName" type="text" placeholder="请录入" require/>
          </td>
          <td>
            地址：<br/>
            <input class="invoiceAddress" data-name="invoiceAddress" type="text" placeholder="请录入" require/>
          </td></tr>
        <tr>
          <td>
            电话：<br/>
            <input class="telephone" data-name="telephone" type="text" placeholder="请录入" require/>
          </td>
          <td>
            开户行：<br/>
            <input class="bankName" data-name="bankName" type="text" placeholder="请录入" require/>
          </td>
        </tr>
        <tr>
          <td>
            账号：<br/>
            <input class="bank_no" style="width: 186px;" data-name="bank_no" type="text" placeholder="请录入" require/>
          </td>
          <td>
            税号：<br/>
            <input class="taxpayerID" data-name="taxpayerID" type="text" placeholder="请录入" require/>
          </td>
        </tr>
      </table>
    </form>
  </div>
  <div class="bonceFoot" style="padding: 0px 15px 10px;">
    <button class="ty-btn ty-btn-big ty-circle-5" onclick="closeK()">取消</button>
    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="updataInvoiceSure" onclick="updataInvoice_sure()">提交</button>
  </div>
</div>

<%--客户对发票方面要求的设置--%>
<div class="bonceContainer bounce-blue" id="invoiceSet2" style="width:600px; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span>客户对发票方面要求的设置</span>
    <a class="bounce_close" onclick="closeI()"></a>
  </div>
  <div class="bonceCon">
    <div style="margin-left:60px ">
      <p>请选择此客户对发票方面的要求</p>
      <p class="setItem" data-type="1"><i class="fa fa-circle-o"></i><span>需要主要为增值税专用发票</span></p>
      <p class="setItem" data-type="2"><i class="fa fa-circle-o"></i><span>需要主要为增值税专用发票以外的发票</span></p>
      <p class="setItem" data-type="3"><i class="fa fa-circle-o"></i><span>基本不需要开发票</span></p>
      <p class="setItem" data-type="4"><i class="fa fa-circle-o"></i><span>要求不定，维持不设置的状态</span></p>
      <p class="tipsmall">注：此项设置完成后，新增订单时往往更便捷！</p>
    </div>
  </div>
  <div class="bonceFoot">
    <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="closeI()">取消</span>
    <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="invoiceSetOk2()">确定</span>
  </div>
</div>

<div class="bonceContainer bounce-blue" id="updateRecords" style="width:600px; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span class="recordTtl">开票信息修改记录</span>
    <a class="bounce_close" onclick="close3()"></a>
  </div>
  <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
    <div class="createRecord clear">
      <div class="clear">
        <p class="ty-left recordTip">当前资料尚未经修改。</p>
        <p class="ty-right recordEditer"></p>
      </div>
    </div>
    <table class="ty-table ty-table-control changeRecord">
      <thead>
      <tr>
        <td>记  录</td>
        <td>操  作</td>
        <td>创建者/修改者</td>
        <td class="recordFinance">财务的确认记录</td>
      </tr>
      </thead>
      <tbody>
      </tbody>
    </table>
  </div>
  <div class="bonceFoot">
    <input type="hidden" id="from">
    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="close3()">关闭</span>
  </div>
</div>

<%--发票修改记录查看--%>
<div class="bonceContainer bounce-blue" id="invoiceRecordsDetail" style="width:600px; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span class="detaileTtl"></span>
    <a class="bounce_close" onclick="close4()"></a>
  </div>
  <div class="bonceCon">
    <table>
      <tr>
        <td>公司名称：
          <div class="sale-con" id="inv_invoiceName"></div>
        </td>
        <td>
          地址：
          <div class="sale-con" id="inv_invoiceAddress"></div>
        </td></tr>
      <tr>
        <td>
          电话：
          <div class="sale-con" id="inv_phone"></div>
        </td>
        <td>
          开户行：
          <div class="sale-con" id="inv_bank"></div>
        </td>
      </tr>
      <tr>
        <td>
          账号：
          <div class="sale-con" id="inv_bankNo"></div>
        </td>
        <td>
          税号：
          <div class="sale-con" id="inv_taxpayerID"></div>
        </td>
      </tr>
    </table>
  </div>
  <div class="bonceFoot">
    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="close4()">关闭</span>
  </div>
</div>




<%@ include  file="../../common/footerScript.jsp"%>

<script>
  $(function(){
    getInvoiceMessage()
    $("#invoiceSet2 .setItem").click(function(){
      $("#invoiceSet2 .fa").attr("class","fa fa-circle-o");
      $(this).children(".fa").attr("class","fa fa-dot-circle-o");
    })

  })

  function closeI() {
    $("#cus").hide()
    $("#cus1").show()
    $("#invoiceSet2").hide()
  }
  function closeK() {
    $("#cus").show()
    $("#cus1").hide()
    $("#invoiceSet2").hide()
  }

  function getRecordList(thisObj) {
    $("#cus").hide()
    $("#updateRecords").show()

    var customerId = getUrlParam("customerID");
    $.ajax({
      url: '../sales/getRecordInvoiceList.do',
      data: {
        'customerId': customerId
      },
      success: function (data) {
        var status = data.status;
        if (status === '1' || status === 1) {
          var getList = data.list;
          if(getList.length > 0) {
            var str = '';
            var eidtNumber = getList.length - 1;
            $(".createRecord .recordTip").html('当前数据为第' + eidtNumber + '次修改后的结果。');
            $(".createRecord .recordEditer").html('修改时间：' + new Date(data.updateDate).format('yyyy-MM-dd hh:mm:ss'));
            $(".changeRecord").show();
            $(".recordFinance").show()
            for (let r in getList) {
              var lastTd = '';
              if (r == '0') {
                lastTd = ` <td>——</td>`;
                str +=
                        '<tr>' +
                        '   <td>原始信息</td>' +
                        '   <td><span class="ty-color-blue" data-id="' + getList[r].id + '" data-frontid="0" data-type="invoiceRecords" onclick="cus_recordDetail($(this))">查看</span></td>' +
                        '   <td>' + getList[r].createName + ' &nbsp; ' + new Date(getList[r].createDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        lastTd +
                        '</tr>';
              } else {
                var front = Number(r) - 1;
                lastTd = '<td>' + (getList[r].financerName || '') + ' &nbsp; ' + new Date(getList[r].financeTime).format('yyyy-MM-dd hh:mm:ss') + '</td>';
                str +=
                        '<tr>' +
                        '   <td>第' + r + '次修改后</td>' +
                        '   <td><span class="ty-color-blue" data-id="' + getList[r].id + '" data-frontid="' + getList[front].id + '" data-type="invoiceRecords" onclick="cus_recordDetail($(this))">查看</span></td>' +
                        '   <td>' + getList[r].updateName + ' &nbsp; ' + new Date(getList[r].updateDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' + lastTd +
                        '</tr>';
              }
            }
            $(".changeRecord tbody").html(str);
          }else {
            $(".createRecord .recordTip").html('当前资料未经修改');
            $(".createRecord .recordEditer").html('创建人：'+ data.createName + '&nbsp;&nbsp;' + new Date(data.createDate).format('yyyy-MM-dd hh:mm:ss'));
            $(".changeRecord").hide();
          }
        } else {
          layer.msg("查看失败！");
        }
      }
    })
  }
  function close3() {
    $("#cus").show()
    $("#updateRecords").hide()
  }
  function close4() {
    $("#updateRecords").show()
    $("#invoiceRecordsDetail").hide()
  }

  function cus_recordDetail(obj){
    $("#updateRecords").hide()
    $("#invoiceRecordsDetail").show()

    var json ={
      'id': obj.data('id'),
      'frontId': obj.data('frontid')
    };
    $.ajax({
      url : "../sales/getRecordInvoiceDetails.do" ,
      data : json,
      success:function(data){
        var nowData = data['data']['now'];
        var frontData = data['data']['front'];
        if(frontData == null){
          $("#invoiceRecordsDetail .detaileTtl").html('原始信息');
          $("#inv_invoiceName").html(nowData.invoiceName);
          $("#inv_phone").html(nowData.telephone);
          $("#inv_invoiceAddress").html(nowData.invoiceAddress);
          $("#inv_bank").html(nowData.bankName);
          $("#inv_bankNo").html(nowData.bankNo);
          $("#inv_taxpayerID").html(nowData.taxpayerID);
        }else{
          $("#invoiceRecordsDetail .detaileTtl").html(obj.parent().prev().html());
          $("#inv_invoiceName").html(compareD(frontData.invoiceName,nowData.invoiceName));
          $("#inv_phone").html(compareD(frontData.telephone,nowData.telephone));
          $("#inv_invoiceAddress").html(compareD(frontData.invoiceAddress,nowData.invoiceAddress));
          $("#inv_bank").html(compareD(frontData.bankName,nowData.bankName));
          $("#inv_bankNo").html(compareD(frontData.bankNo,nowData.bankNo));
          $("#inv_taxpayerID").html(compareD(frontData.taxpayerID,nowData.taxpayerID));
        }
      },
      error: function (msg) {
        layer.msg("连接错误，请稍后重试！");
        return false;
      }
    });
  }

  function compareD(front,now){
    if(front == now){
      return '<span>' + handleNull(now) +' </span>'
    }else{
      return '<span class="redFlag">' + handleNull(now) + '</span>'
    }
  }

  function goset(thisObj) {
    let type = thisObj.data("type")
    $("#cus").hide()
    $("#cus1").hide()
    $("#invoiceSet2").show()
    $("#invoiceSet2").data("type" , type).find(".fa-dot-circle-o").attr("class", "fa fa-circle-o")
  }
  function invoiceSetOk2() {
    var selectP = $("#invoiceSet2 .fa-dot-circle-o").parent()
    let type = selectP.data("type");
    let txt = '尚未设置'
    if(Number(type) > 0  && Number(type) !== 4){
      txt = selectP.children("span").html();
    }
    $(".goset_update").data("invoice", type).prev().html(txt);
    $("#cus").hide()
    $("#cus1").show()
    $("#invoiceSet2").hide()
  }

  // creator : hxz 2021-05-26 修改开票信息确定
  function updataInvoice_sure(){
    var id = getUrlParam("customerID");
    var data = { 'id': id };
    $("#updateInvoice input").each(function(){
      var name = $(this).data('name');
      data[name] = $(this).val();
    })
    data['invoiceRequire'] =  $(".goset_update").data("invoice")
    $.ajax({
      url: "/sales/updatePdCustomerInvoice.do",
      data: data,
      type: "post",
      dataType: "json",
      success: function (data) {
        var status = data.status;
        if(status == '1'){
          $("#cus").show()
          $("#cus1").hide()
          $("#invoiceSet2").hide()
          getInvoiceMessage()
        }
      }
    })
  }

  function edit() {
    let info = $("#InvoiceMessage").data('info');
    $("#cus").hide()
    $("#cus1").show()
    $("#updateInvoice .invoiceName").val(info.name)
    $("#updateInvoice .invoiceAddress").val(info.address)
    $("#updateInvoice .telephone").val(info.telephone)
    $("#updateInvoice .bankName").val(info.bank_name)
    $("#updateInvoice .bank_no").val(info.bank_no)
    $("#updateInvoice .taxpayerID").val(info.taxpayerID)

  }

  function closeIframe(){
    console.log('closeIframe')
    $(window.parent.document).find("#somethingItemDetailsPage").hide()
  }

  // creator : hxz 2021-05-25  查看开票资料
  function getInvoiceMessage() {
    let customerID = getUrlParam("customerID");
    $.ajax({
      "url":"../sales/getCustomerInvoice",
      "data":{ "id": customerID},
      success:function (res) {
        if(res['data']){
          let info = res['data'] && res['data'][0]
          $("#InvoiceMessage").data('info', info);
          $("#InvoiceMessage .invoiceName").html(info.name);
          $("#InvoiceMessage .invoiceAddress").html(info.address);
          $("#InvoiceMessage .telephone").html(info.telephone);
          $("#InvoiceMessage .bankName").html(info.bank_name);
          $("#InvoiceMessage .bank_no").html(info.bank_no);
          $("#InvoiceMessage .taxpayerID").html(info.taxpayerID);
          if(info.update_name && info.update_date){
            $("#InvoiceMessage .editU").html( info.update_name );
            $("#InvoiceMessage .andTime").html(new Date(info.update_date).format('yyyy-MM-dd hh:mm:ss') );
          }else{
            $("#InvoiceMessage .editU").html( info.create_name );
            $("#InvoiceMessage .andTime").html(new Date(info.create_date).format('yyyy-MM-dd hh:mm:ss') );
          }


        }else{
          layer.msg("未获取开票信息")
        }
      }
    })
  }

</script>
</body>
</html>
