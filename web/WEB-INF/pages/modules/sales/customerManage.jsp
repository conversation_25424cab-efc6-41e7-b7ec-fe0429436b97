<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/wonderssProduct/sysCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/sale.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/region.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed5">
    <div class="bonceContainer bounce-blue" id="seeModule" style="width: 700px">
        <div class="bonceHead">
            <span>模块查看</span>
            <a class="bounce_close" onclick="bounce_Fixed5.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><b class="moduleName"></b></h4>
                <table class="kj-table moduleTable">
                    <thead>
                    <tr>
                        <td class="thisLevel">一级菜单</td>
                        <td class="nextLevel">
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td class="thisLevel">二级菜单</td>
                                    <td class="nextLevel">
                                        <table class="kj-table">
                                            <thead>
                                            <tr>
                                                <td class="thisLevel">三级菜单</td>
                                            </tr>
                                            </thead>
                                        </table>
                                    </td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed5.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed4">
    <div class="bonceContainer bounce-blue" id="includeGoodContract" style="width: 800px;">
        <div class="bonceHead">
            <span>已包含某商品的销售合同</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
<%--            <div class="row-flex cusInfo">--%>
<%--                客户代号/客户名称--%>
<%--            </div>--%>
            <div class="row-flex goodInfo">
<%--                商品代号/商品名称/型号/规格/计量单位--%>
            </div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>合同编号</td>
                    <td>所涉商品</td>
                    <td>有效期</td>
                    <td>签署日期</td>
                    <td>本版本合同的创建</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel()">关闭</span>
        </div>
    </div>
    <%--联系方式查看--%>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签：</span>
                    <span class="sale-con" id="see_contactTag"></span>
                </div>
            </div>
            <%--<p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>--%>
            <table class="see_otherContact kj-table" style="width:442px;">
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel()">关闭</span>
        </div>
    </div>
    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" onkeyup="countWords($(this),20)"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
    <%--套餐查看--%>
    <div class="bonceContainer bounce-blue" id="seeSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐查看</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><span class="setMenuName"></span></h4>
                <div class="ty-alert">
                    组成本套餐的模块
                    <div class="btn-group">
                        创建 <span class="create"></span>
                    </div>
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%-- 删除提示 --%>
    <div class="bonceContainer bounce-red" id="contractDelTip"  >
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;">确定删除本条合同吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="contractDelTipOk()">确定</span>
        </div>
    </div>
    <%-- 向本合同添加商品 从本合同移出商品 本合同下的商品 --%>
    <div class="bonceContainer bounce-blue" id="tipcontractGoods" style="width: 800px; max-height:400px; ">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="row-flex" id="tips">
                <div class="tip"></div>
                <div class="btn-group countStr">
                    已选 <span class="count">0</span> 种
                </div>
            </div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td class="selectTd"></td>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                    <td>已包含的合同</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 addOrCancel ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn bounce-ok addOrCancel ty-btn-big ty-circle-3" onclick="addOrCancelOk()">确定</span>
            <span class="ty-btn bounce-ok cScanc ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%-- 暂停履约/终止合同 、 恢复履约/重启合作 提示 --%>
    <div class="bonceContainer bounce-red" id="cEndTip" >
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" id="tipsc">

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big"  data-name="cEndOk">确定</span>
        </div>
    </div>
    <%-- 恢复履约/重启合作（已过期提示) --%>
    <div class="bonceContainer bounce-red" id="cEndTip2" >
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                本合同已于 <span class="vaildEnd"></span> 到期。请确定具体要进行哪项操作：
                <div class="kj-radio-group" style="margin-top: 16px; padding-left: 80px;">
                    <div class="kj-radio">
                        <input type="radio" name="expire" id="radio_c_no" value="1">
                        <label for="radio_c_no">不再执行，转入“已到期的合同”</label>
                    </div>
                    <div class="kj-radio">
                        <input type="radio" name="expire" id="radio_c_next" value="2">
                        <label for="radio_c_next">续约</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big"  data-name="cEndOk2">确定</span>
        </div>
    </div>
    <%--联系人修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="contactRecordsDetail" style="width: 550px">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">姓名：</span>
                <span class="sale-con" id="record_contactName"></span>
                <span class="sale_ttl1">职位：</span>
                <span class="" id="record_position"></span>
            </p>
            <table class="record_otherContact kj-table">
            </table>
            <span class="sale_ttl1">名片：</span>
            <div id="record_contactsCard">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%--收货地址修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="shRecordsDetail">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="shDisUse ty-color-red"></p>
            <p>
                <span class="sale_ttl1 shAddressTtl">收货地址：</span>
                <span class="sale-con" id="shAddress"></span>
            </p>
            <p>
                <span class="sale_ttl1">收货人：</span>
                <span class="sale-con" id="shName"></span>
            </p>
            <p>
                <span class="sale_ttl1">收货电话：</span>
                <span class="sale-con" id="shNumber"></span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%--邮寄地址修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="fpRecordsDetail">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="fpDisUse ty-color-red"></p>
            <p>
                <span class="sale_ttl1">邮寄地址：</span>
                <span class="sale-con" id="fpAddress"></span>
            </p>
            <p>
                <span class="sale_ttl1">发票接收人：</span>
                <span class="sale-con" id="fpName"></span>
            </p>
            <p>
                <span class="sale_ttl1">联系电话：</span>
                <span class="sale-con" id="fpMobile"></span>
            </p>
            <p>
                <span class="sale_ttl1">邮寄编码：</span>
                <span class="sale-con" id="fpNumber"></span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%--基本信息修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="BaseRecordsDetail" style="min-width: 1200px">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" id="">
            <div class="part">
                <div class="item">
                    <div class="item"><span class="item_title">客户名称：</span><span class="item_content" id="base_cusfullName" style="width: 600px"></span></div>
                    <div class="item"><span class="item_title">客户代号：</span><span class="item_content" id="base_cuscoding"></span></div>
                </div>
                <div class="item">
                    <div class="item"><span class="item_title">地址：</span><span class="item_content" id="base_address" style="width: 600px"></span></div>
                    <div class="item"><span class="item_title">客户简称：</span><span class="item_content" id="base_cusname"></span></div>
                </div>
                <%--<div class="clear rowGap"><span class="ty-left" id="see_address"></span></div>--%>
                <div class="specialOrgPart">最高负责人 <span id="base_supervisorName"></span> <span id="base_supervisorMobile"></span>（手机号）</div>
                <div class="firstBuyTime"></div>
            </div>
            <div class="part_end">
                <div class="item">
                    <div class="item_title">全景照片：</div>
                    <div class="item_content" id="base_qImgUpload"></div>
                </div>
                <div class="item">
                    <div class="item_title">产品图片：</div>
                    <div class="item_content" id="base_pImgUpload"></div>
                </div>
                <div class="item">
                    <div class="item">
                        <div class="item_title">首次接触时间：</div>
                        <div class="item_content" id="base_firstContactTime"></div>
                    </div>
                    <div class="item">
                        <div class="item_title">首次接触地点：</div>
                        <div class="item_content" id="base_firstAddress"></div>
                    </div>
                    <div class="item">
                        <div class="item_title">信息获取渠道：</div>
                        <div class="item_content" id="base_infoSource"></div>
                    </div>
                </div>
                <div class="item">
                    <div class="item_title">备注：</div>
                    <div class="item_content" id="base_memo"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%--发票修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="invoiceRecordsDetail" style="width: 800px">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="sale_c">
                <div class="sale_ttl1">公司名称：</div>
                <div class="sale-con" id="inv_invoiceName"></div>
                <div class="sale_ttl1">地址：</div>
                <div class="sale-con" id="inv_invoiceAddress"></div>
                <div class="sale_ttl1">电话：</div>
                <div class="sale-con" id="inv_phone"></div>
            </div>
            <div class="sale_c">
                <div class="sale_ttl1">开户行：</div>
                <div class="sale-con" id="inv_bank"></div>
                <div class="sale_ttl1">账号：</div>
                <div class="sale-con" id="inv_bankNo"></div>
                <div class="sale_ttl1">税号：</div>
                <div class="sale-con" id="inv_taxpayerID"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>

    <%-- 机构信息变动记录详细页面 --%>
    <div class="bonceContainer bounce-blue" id="bodcerMore" style="width: 680px;z-index: 1;">
        <%-- 顶部标签 --%>
        <div class="bonceHead">
            <span class="bounce_title">选择产品</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel();bounce.cancel()"></a>
        </div>
        <%-- 机构信息变动记录 --%>
        <div class="bonLock" style="margin-left: 14px;margin-right: 14px;">
            <%--根据上一个弹窗列表中每一行的数据进行下面这两个div的隐藏和显示--%>
            <div class="boxe"></div>
            <%--不变的表格👇--%>
            <table class="kj-table kj-table-none ordge">
                <tr>
                    <td><span class="ty-color-red"></span> 机构名称</td>
                    <td><input type="text" name="fullName" class="ty-inputText" style="background-color: #eee;
                height: 30px;" placeholder="请输入" require></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red"></span> 机构简称</td>
                    <td><input type="text" name="name" class="ty-inputText " style="background-color: #eee;
                height: 30px;" placeholder="请输入" require></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red"></span>机构地址</td>
                    <td><input type="text" name="address" class="ty-inputText" style="background-color: #eee;
                height: 30px;" la require></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管姓名</td>
                    <td><input type="text" name="headermaster" style="background-color: #eee;
                height: 30px;" class="ty-inputText"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td><span class="ty-color-red"></span> 超管手机</td>
                    <td><input type="text" name="headermasterPhone" style="background-color: #eee;
                height: 30px;" class="ty-inputText" require
                               onkeyup="clearNum(this)"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td><span class="ty-color-red"></span> 数据存储地点</td>
                    <td>
                        <select type="text" name="uploadStorageType" style="background-color: #eee;
                height: 30px;" class="ty-inputSelect" require>
                            <option value=""></option>
                            <option value="NFS">NFS</option>
                            <option value="Seafile" disabled>Seafile</option>
                        </select>
                    </td>
                </tr>
            </table>
        </div>
        <%-- 关闭按钮 --%>
        <div class="seeProductBtn" style="text-align: right;margin-right: 44px;margin-bottom: 20px;
            margin-top: 28px;">
            <span class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce_Fixed3.cancel();
            bounce.cancel('#bodcerLine')"
                  style="color: #fff;background: #0070c0;">关闭</span>
        </div>
    </div>
    <%-- 选择联系人--%>
    <div class="bonceContainer bounce-blue" id="chooseCusContact" style="width: 450px">
        <div class="bonceHead">
            <span>选择客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" style="background:#f0f8ff ">
            <input type="hidden" id="target">
            <div class="p0">
                <p style='text-align:center;'>暂无客户数据，请通过新增添加</p>
            </div>
            <div class="p1">
                <p>以下为可供选择的客户联系人</p>
                <ul class="cusList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="linkBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</span>
            <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="chooseCusContactOk()">确定</button>
        </div>
    </div>
    <%--新增联系人--%>
    <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
        <div class="bonceHead">
            <span>联系人</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon linkUploadify" style="max-height:500px;overflow-y: auto;">
            <form id="newContectData">
                <p>
                    <span class="sale_ttl1">联系人标签：</span>
                    <span class="sale_gap" id="contactFlag"></span>
                </p>
                <p>
                    <span class="sale_ttl1">姓名：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                </p>
                <p>
                    <span class="sale_ttl1">手机：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactNumber" require/></span>
                    <a class="sale_ttl1 addMore" onclick="addMore($(this))">添加更多联系方式</a>
                    <select style="display: none;width: 194px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                        <option value="0"></option>
                        <option value="1">手机</option>
                        <option value="2">QQ</option>
                        <option value="3">Email</option>
                        <option value="4">微信</option>
                        <option value="5">微博</option>
                        <option value="9">自定义</option>
                    </select>
                </p>
                <ul class="otherContact">
                </ul>
                <div id="contactsCard">
                    <span class="sale_ttl1">名片：</span>
                    <div id="uploadCard" class="cardUploadBtn"></div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addContact" data-name="addContact">提交</button>
        </div>
    </div>
    <%--套餐清单查看--%>
    <div class="bonceContainer bounce-blue" id="seeUsableSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐清单查看</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="ty-alert">
                    本模板的用户不可选择的模块数为A，设系统内已有套餐数为B
                    需根据A、B，算出系统内不包含A的已有套餐数C，并需列出C的清单
                    机构可选择的套餐，即为上述C的清单，具体如下：
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>套餐名称</td>
                        <td>创建</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel();">关闭</span>
        </div>
    </div>
    <%-- 修改记录 - 合同查看 --%>
    <div class="bonceContainer bounce-blue" id="cScanHis" style="width: 800px; ">
        <div class="bonceHead">
            <span>查看合同</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <table class="kj-table kj-table-control leftTab">
                    <tr><td>合同编号</td><td class="contract_see_sn"></td></tr>
                    <tr><td>签署日期</td><td class="contract_see_signTime"></td></tr>
                    <tr><td>合同的有效期</td><td class="contract_see_validTime"></td></tr>
                    <tr><td>合同的扫描件或照片</td><td class="contract_see_image"></td></tr>
                    <tr><td>合同的可编辑版</td><td class="contract_see_file"></tr>
                    <tr>
                        <td>本合同下专属的商品（共<span class="contract_see_zsNum"></span>种）</td>
                        <td class="cGoodss">
                            <span class="link-blue node" data-fun="zsNum">查看</span>
                        </td>
                    </tr>
                    <tr>
                        <td>本合同下通用型的商品（共<span class="contract_see_tyNum"></span>种）</td>
                        <td class="cGoodss">
                            <span class="link-blue node" data-fun="tyNum">查看</span>
                        </td>
                    </tr>
                    <tr><td>备注</td><td class="contract_see_memo"></td></tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <span id="orgMenuList" style="display: none;"></span>
    <%-- 合同查看 --%>
    <div class="bonceContainer bounce-blue" id="cScan" style="width: 800px; ">
        <div class="bonceHead">
            <span>查看合同</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p>本版本合同的创建 <span class="contract_see_create"></span></p>
                <table class="kj-table kj-table-control leftTab">
                    <tr><td>合同编号</td><td class="contract_see_sn"></td></tr>
                    <tr><td>签署日期</td><td class="contract_see_signTime"></td></tr>
                    <tr><td>合同的有效期</td><td class="contract_see_validTime"></td></tr>
                    <tr><td>合同的扫描件或照片</td><td class="contract_see_image"></td></tr>
                    <tr><td>合同的可编辑版</td><td class="contract_see_file"></tr>
                    <tr>
                        <td>本合同下专属的商品（共<span class="contract_see_zsNum"></span>种）</td>
                        <td class="cGoodss">
                            <span class="link-blue node" data-fun="zsNum">查看</span>
                        </td>
                    </tr>
                    <tr>
                        <td>本合同下通用型的商品（共<span class="contract_see_tyNum"></span>种）</td>
                        <td class="cGoodss">
                            <span class="link-blue node" data-fun="tyNum">查看</span>
                        </td>
                    </tr>
                    <tr><td>备注</td><td class="contract_see_memo"></td></tr>
                    <tr>
                        <td>本版本合同的修改记录</td>
                        <td class="cEditLog">
                            <span class="link-blue node" data-fun="cEditLog">查看</span>
                        </td>
                    </tr>
                    <tr>
                        <td>本合同的续约记录</td>
                        <td class="cRenewalLog">
                            <span class="link-blue node" data-fun="cRenewalLog">查看</span>
                        </td>
                    </tr>
                </table>
                <div class="enabledList"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="invoiceSet" style="width: 450px">
        <div class="bonceHead">
            <span>客户对发票方面要求的设置</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-left:60px ">
                <p>请选择此客户对发票方面的要求</p>
                <p class="setItem" data-type="1"><i class="fa fa-circle-o"></i><span>需要主要为增值税专用发票</span></p>
                <p class="setItem" data-type="2"><i class="fa fa-circle-o"></i><span>需要主要为增值税专用发票以外的发票</span></p>
                <p class="setItem" data-type="3"><i class="fa fa-circle-o"></i><span>基本不需要开发票</span></p>
                <p class="setItem" data-type="4"><i class="fa fa-circle-o"></i><span>要求不定，维持不设置的状态</span></p>
                <p class="tipsmall">注：此项设置完成后，新增订单时往往更便捷！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="invoiceSetOk()">确定</span>
        </div>
    </div>
    <%--修改记录--%>
    <div class="bonceContainer bounce-blue" id="updateRecords">
        <div class="bonceHead">
            <span class="recordTtl">修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <div class="createRecord clear">
                <div class="clear">
                    <p class="ty-left recordTip">当前资料尚未经修改。</p>
                    <p class="ty-right recordEditer"></p>
                </div>
            </div>
            <table class="kj-table kj-table-control changeRecord">
                <thead>
                <tr>
                    <td>记  录</td>
                    <td>操  作</td>
                    <td>创建者/修改者</td>
                    <td class="recordFinance">财务的确认记录</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <input type="hidden" id="from">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>

    <div class="bonceContainer bounce-red" id="tip3" style="width: 450px">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p class="tipMs" style="text-align: center; padding:10px 0 8px"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">确定</span>
        </div>
    </div>
    <%--查看访谈记录--%>
    <div class="bonceContainer bounce-blue" id="seeInterviewInfo" style="width: 650px">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <span class="sale_ttl1">访谈日期：</span>
            <span class="sale-con" id="see_interviewDate"></span>
            <span class="sale_ttl1">访谈对象：</span>
            <span class="sale-con" id="see_interviewee"></span>
            <div style="text-align: right" id="inputInfo"></div>
            <span class="sale_ttl1">访谈内容：</span>
            <p id="see_interviewContent" style="padding-left: 50px;"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>

    <%-- 导入的重要提示 --%>
    <div class="bonceContainer bounce-red" id="importantTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="gap-lt">
                <h4>导入失败！</h4>
                <div>原因可能为：</div>
                <div>1、修改了所下载表格中的“列”。</div>
                <div>2、选错了文件。</div>
                <div>3、文件太大，或里面含有图片等。</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed2.cancel()">知道了</span>
        </div>
    </div>
    <%-- 查看产品--%>
    <div class="bonceContainer bounce-blue" id="seeProduct" style="width: 700px">
        <div class="bonceHead">
            <span>产品查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><span class="pdName"></span></h4>
                <div class="item-flex">
                    <div class="item-title">所选模板：</div>
                    <div class="item-content"><span class="pdModel"></span><div class="ty-right">操作人 <span class="pdTime"></span></div></div>
                </div>
                <div class="ty-alert">
                    已重命名模块的数量 <b class="renameNumber"></b> 个
                </div>
                <table class="kj-table tbl_see_rename" style="width: 70%">
                    <thead>
                    <tr>
                        <td>原名称</td>
                        <td>新名称</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert">主套餐内的模块</div>
                <table class="kj-table kj-table-striped tbl_see_mainModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    使用本模板的用户增值功能的模块
                </div>
                <table class="kj-table kj-table-striped tbl_see_valueAddedModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    与本模板增值功能模块对应的已有套餐
                    <div class="btn-group">
                        <span class="link-blue" onclick="seeUsableSetMenuBtn('seeModel')">查看</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel();">关闭</span>
        </div>
    </div>

    <%-- 机构变动 --%>
    <div class="bonceContainer bounce-blue" id="bodcerLine" style="display: block;
        position: fixed;left: 611.5px;top: -83px;width: 680px;z-index: 1;">
        <%-- 顶部标签 --%>
        <div class="bonceHead">
            <span class="bounce_title">选择产品</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel();bounce.cancel()"></a>
        </div>
        <%-- 机构信息变动记录 --%>
        <div class="bonceHis" style="margin: 0 45px;margin-top: 49px;">
            <table class="kj-table kj-table-control orgHList" style="
            border: 1px solid gray;">
                <thead>
                <tr>
                    <td style="border: 1px solid #d7d7d7;text-align: center;padding: 0;
                    background: #fff">资料状态
                    </td><!--dataState-->
                    <td style="border: 1px solid #d7d7d7;text-align: center;
                    background: #fff">操作</td>
                    <td style="text-align: center;border: 1px solid #d7d7d7;
                    background: #fff">创建人/修改人</td>
                </tr>
                </thead>
            </table>
            <!--这里有一个关闭按钮-->
        </div>
        <%-- 关闭按钮 --%>
        <div class="seeProductBtn" style="text-align: right;margin-right: 44px;
            margin-top: 44px;padding-bottom: 16px;">
            <span class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce_Fixed2.cancel();
            bounce.show('#selectModule')"
                  style="color: #fff;background: #0070c0;">关闭</span>
        </div>
    </div>
    <%-- 暂停服务/恢复服务的操作记录 --%>
    <div class="bonceContainer bounce-blue" id="stopRecoveryRecord" style="width: 680px;">
        <div class="bonceHead">
            <span class="bounce_title">暂停服务/恢复服务的操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel();"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table">
                <thead>
                <tr>
                    <td>操作性质</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big" onclick="bounce_Fixed2.cancel()">关闭</button>
        </div>
    </div>
    <%--新增收货信息--%>
    <div class="bonceContainer bounce-green" id="newReceiveAddressInfo" style="width: 450px">
        <div class="bonceHead">
            <span>新增收货地址</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1"><span class="ty-color-red">*</span>收货地址</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="ReceiveAddress" require/></span>
            </p>
            <p>
                <span class="sale_ttl1"><span class="ty-color-red">*</span>联系人</span>
                <span class="sale_con1 chooseCusCon" data-target="#ReceiveName">
                    <input type="text" readonly placeholder="请选择" id="ReceiveName" require/>
                    <span class="hd"></span>
                    <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                </span>
                <span class="linkBtn" onclick="addContactInfo(2)">新增</span>
            </p>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addReceive" data-fun="addReceive">提交</button>
        </div>
    </div>
    <%--新增到货区域--%>
    <div class="bonceContainer bounce-green" id="newReceiveAreaInfo" style="width: 700px">
        <div class="bonceHead">
            <span>新增到货区域</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 180px;">
            <div class="areaForm">
                <div class="regionMode">
                    <span class="sale_ttl1"><span class="ty-color-red">*</span>货物需到达的城市或地区</span>
                    <div class="sale_con1 regionText" onclick="regionCheck()">
                        <input type="text" readonly placeholder="请选择" id="regionCon" />
                        <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                        <span class="hd"></span>
                    </div>
                    <div class="regionBox">
                        <ul class="region-tab"></ul>
                        <div class="region-content">
                            <ul data-level="1"></ul>
                        </div>
                    </div>
                </div>
                <p>
                    <span class="sale_ttl1">对需到达地点的特殊要求</span>
                    <span class="sale_con1 clearVal">
                        <input type="text" placeholder="请录入" id="requirements"/>
                        <i class="clearInputVal">X</i>
                    </span>
                </p>
                <p>
                    <span class="sale_ttl1"><span class="ty-color-red">*</span>联系人</span>
                    <span class="sale_con1 chooseCusCon" data-target="#areaName">
                    <input type="text" readonly placeholder="请选择" id="areaName" require/>
                    <span class="hd"></span>
                    <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                </span>
                    <span class="linkBtn" onclick="addContactInfo(4)">新增</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" data-fun="addAreaReceive">提交</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="updateAddressTip" style="width: 550px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon wideBold">
            <p>此处的修改，适用于该地址中错别字之类的修改。</p>
            <p>修改为另一个地点，请勿使用本“修改”！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="updateAddressKeep()">继续操作</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">

    <div class="bonceContainer bounce-red" id="tip" style="width: 450px">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p class="tipMs" style="text-align: center; padding:10px 0 8px"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="stopDelContact" style="width:600px">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table kj-table-control">
                <tr>
                    <td>名称</td>
                    <td>操作</td>
                </tr>

            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 提示框 --%>
    <div class="bonceContainer bounce-red" id="mtTip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%-- 已到期的合同 --%>
    <div class="bonceContainer bounce-blue" id="contractEndData">
        <div class="bonceHead">
            <span>已到期的合同</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table">
                <thead>
                <tr>
                    <td>合同编号</td>
                    <td>到期日</td>
                    <td>操作</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 已暂停/终止的合同 --%>
    <div class="bonceContainer bounce-blue" id="contractStopData" style="width:600px; ">
        <div class="bonceHead">
            <span>已暂停履约/终止的合同</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table">
                <thead>
                <tr>
                    <td>合同编号</td>
                    <td>暂停履约/终止的时间</td>
                    <td>操作</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 暂停合作/恢复合作的操作记录 --%>
    <div class="bonceContainer bounce-blue" id="suspendRecord">
        <div class="bonceHead">
            <span>暂停合作/恢复合作的操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table">
                <thead>
                <tr>
                    <td>操作性质</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>

    <%--新增、修改、续约 合同--%>
    <div class="bonceContainer bounce-green" id="newContractInfo" style="width: 500px">
        <div class="bonceHead">
            <span>新增合同</span>
            <a class="bounce_close" onclick="editContractOk(0)"></a>
        </div>
        <div class="bonceCon linkUploadify">
            <div class="bounceMainCon">
                <p class="cRenewalTip">请录入续约后的合同信息。</p>
                <div class="flex-box cusItem">
                    <div class="citem" style="flex: auto">
                        <p>客户</p>
                        <input type="text" name="customerName" disabled style="width: 100%">
                    </div>
                </div>
                <div class="flex-box">
                    <div class="citem">
                        <p><i class="red">*</i> 合同编号</p>
                        <input type="text" placeholder="请录入" class="cNo">
                    </div>
                    <div class="citem">
                        <p>签署日期</p>
                        <input type="text" placeholder="请选择" readonly class="cSignDate">
                    </div>
                </div>
                <div class="flex-box">
                    <div class="citem">
                        <p><i class="red">*</i> 合同的有效期</p>
                        <input type="text" placeholder="请选择" readonly class="cStartDate">
                    </div>
                    <div class="citem">
                        <p>&nbsp;</p>
                        <span>至</span>
                    </div>
                    <div class="citem">
                        <p>&nbsp;</p>
                        <input type="text" placeholder="请选择" readonly class="cEndDate">
                    </div>
                </div>
                <div class="citem2">
                    <p>合同的扫描件或照片(共可上传9张) <span class="linkBtn ty-right" id="cUpload1"></span></p>
                    <div class="fileCon">
                        <div class="fileCon1"></div>
                        <div class="hd deleteFile"></div>
                        <div style="clear: both"></div>
                    </div>
                </div>
                <div class="citem2">
                    <p>合同的可编辑版 <span class="linkBtn ty-right" id="cUpload2"></span></p>
                    <div class="fileCon ">
                        <div class="fileCon2"></div>
                        <div style="clear: both"></div>
                    </div>
                </div>
                <div class="citem2 cGSZ">
                    <div class="row-flex">
                        本合同下的专属商品 <span data-fun="scanZsGs" type="btn" class="link-blue zsGoodNumber">0</span> 种
                        <div class="btn-group">
                            <span class="link-blue" type="btn" data-fun="removeZsGs">移出商品</span>
                            <span class="link-blue" type="btn" data-fun="addZsGs">添加商品</span>
                        </div>
                    </div>
                    <div class="fileCon" style=" background: #f0f0f0;">
                        <div class="zsGoodList" style="font-size: 13px"></div>
                    </div>
                </div>
                <div class="citem2 cGST">
                    <div class="row-flex">
                        本合同下的通用型商品 <span data-fun="scanTyGs" type="btn" class="link-blue tyGoodNumber">0</span> 种
                        <div class="btn-group">
                            <span class="link-blue" type="btn" data-fun="removeTyGs">移出商品</span>
                            <span class="link-blue" type="btn" data-fun="addTyGs">添加商品</span>
                        </div>
                    </div>
                    <div class="fileCon" style=" background: #f0f0f0;">
                        <div class="tyGoodList" style="font-size: 13px"></div>
                    </div>
                </div>
                <div class="citem2">
                    <p>备注</p>
                    <input class="cMemo" type="text" style="width: 100%" placeholder="请录入">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="editContractOk(0)">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="editContractOk" data-name="editContractOk">提交</button>
        </div>
    </div>
    <%--修改记录--%>
    <div class="bonceContainer bounce-green" id="contractChangeLog" style="width: 600px">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p class="tips">
                    当前数据为本版本合同第 <span class="changeNum"></span> 次修改后的结果。
                    <span class="ty-right">
                        修改时间：<span class="updateTime"></span>
                    </span>
                </p>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>记录</td>
                        <td>操作</td>
                        <td>创建者/修改者</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--续约记录--%>
    <div class="bonceContainer bounce-green" id="contractRenewLog" style="width: 600px">
        <div class="bonceHead">
            <span>续约记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table">
                <thead>
                <tr>
                    <td>版本</td>
                    <td>操作</td>
                    <td>创建（时间为各版本合同的续约时间）</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--新增发票邮寄地址--%>
    <div class="bonceContainer bounce-green" id="newMailInfo" style="width: 450px">
        <div class="bonceHead">
            <span>发票邮寄信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">邮寄地址：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="mailAddress" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">邮寄编码：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="mailNumber" require/></span>
            <p id="mailNumError">请输入正确的邮寄编码！</p>
            </p>
            <p>
                <span class="sale_ttl1">发票接收人：</span>
                <span class="sale_con1 chooseCusCon" data-target="#mailName">
                    <input type="text" readonly placeholder="请选择" id="mailName" require/>
                    <span class="hd"></span>
                    <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
                </span>
                <span class="linkBtn" onclick="addContactInfo(3)">新增</span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="addMail" data-name="addMail">提交</button>
        </div>
    </div>
    <%--新增访谈记录--%>
    <div class="bonceContainer bounce-green" id="newInterviewInfo" style="min-width: 980px">
        <div class="bonceHead">
            <span>新增访谈记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form id="newInterview">
                <div class="sale_c">
                    <span class="sale_ttl1">访谈对象：</span>
                    <input type="text" name="interviewer" placeholder="请录入" require/>
                    <span class="sale_ttl1">职位：</span>
                    <input type="text" name="post" placeholder="请录入" require/>
                    <span class="sale_ttl1">访谈日期：</span>
                    <input type="text" id="interviewDate" placeholder="请选择" require/>
                </div>
                <div class="sale_c">
                    <span class="sale_ttl1">访谈内容：</span>
                    <textarea name="content" style="width:710px;" type="text" placeholder="请录入" onkeyup="countWords($(this),500)" require></textarea>
                </div>
                <div class="textMax text-right" style="margin-right:100px;">0/500</div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="newInterviewSure" data-type="new" onclick="newInterviewSure($(this))">提交</button>
        </div>
    </div>
    <%--删除、停用、启用提示--%>
    <div class="bonceContainer bounce-red" id="turnStateTip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="shu1">
                <p id="turnTipMs" style="text-align: center; padding:10px 0 8px"> </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" id="oprationSure">确定</span>
        </div>
    </div>
    <%--删除访谈记录提示--%>
    <div class="bonceContainer bounce-red" id="delInterviewTip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p style="text-align: center; padding:10px">删除后，本访谈记录依旧显示在访谈记录的列表中，但仅可查看。 </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="deletInterViewSure()">确定</span>
        </div>
    </div>
    <%--访谈记录修改记录--%>
    <div class="bonceContainer bounce-blue" id="delInterviewTip" style="width: 450px">
        <div class="bonceHead">
            <span>访谈记录修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear noneUpdated">
                <span class="ty-left">当前资料尚未经修改。</span>
                <span class="ty-right">创建人：XXX XXXX/XX/XX XX.XX.XX</span>
            </div>
            <div class="">
                <div>
                    <p class="ty-left">当前资料为第<span class="eidtNum"></span>次修改后的结果。</p>
                    <p class="updaterInfo ty-right">修改时间：XXXX/XX/XX XX:XX:XX</p>
                </div>
                <table class="kj-table kj-table-control updateRecordList">
                    <tr>
                        <td>记  录</td>
                        <td>操  作</td>
                        <td>创建者/修改者</td>
                    </tr>
                    <tr>
                        <td>原始信息</td>
                        <td><span class="ty-color-blue">查看</span></td>
                        <td>缘根       XXXX/XX/XX XX:XX:XX</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="">确定</span>
        </div>
    </div>
    <%--基本信息修改--%>
    <div class="bonceContainer bounce-blue" id="updataccount" style="min-width:1100px;">
        <div class="bonceHead">
            <span>基本信息修改</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon">
            <form class="addpayDetails" id="sale_updataBase">
                <div>
                    <div class="sale_c">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>客户名称：</div>
                        <div class="sale_con1"><input type="text" id="updata_cusname" class="add_cusName" name="fullName" placeholder="请录入" require style="width: 580px"></div>

                        <div class="sale_ttl1">客户代号：</div>
                        <div class="sale_con1"><input type="text" id="updata_cuscoding" name="code" placeholder="请录入"></div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">地址：</div>
                        <div class="sale_con1"><input type="text" id="updata_cusSite" name="address" placeholder="请录入" style="width: 580px"></div>
                        <div class="sale_ttl1">客户简称：</div>
                        <div class="sale_con1"><input type="text" id="updata_cusfullName" name="name" placeholder="请录入"></div>
                    </div>
                </div>
                <div class="flex-box specialOrgPart" style="justify-content: flex-start">
                    <div class="sale_c">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span><span>最高负责人:</span></div>
                        <div class="sale_con1"><input type="text" name="supervisorName" id="updata_supervisorName" require></div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span><span>手 机:</span></div>
                        <div class="sale_con2"><input type="text" name="supervisorMobile" id="updata_supervisorMobile" require></div>
                    </div>
                </div>
                <div class="sale_c">
                    <span style="margin-left:50px;">是否已购买过产品或服务？</span>
                    <label><input type="radio" name="buyCase" value="1" onclick="purchasedAny($(this))">是</label>
                    <label><input type="radio" name="buyCase" value="0" onclick="purchasedAny($(this))">否</label>
                    <span class="purchased">
                        <span>首次购买时间：</span>
                        <label><input type="radio" name="firstTime" onclick="checkMonth($(this))" value="1">今年</label>
                        <input id="initialPeriod" name="initialPeriod" placeholder="请选择月份" style="display: none;" require/>
                        <label><input type="radio" name="firstTime" value="2" onclick="checkMonth($(this))">去年</label>
                        <label><input type="radio" name="firstTime" value="3" onclick="checkMonth($(this))">更久之前</label>
                    </span>
                </div>
                <%-- <div class="sale_c purchased">
                     <span style="margin-left:50px;">是否已与该客户签订合同，且合同处于有效期内？<span class="ty-color-red">*</span></span>
                     <label><input type="radio" name="hasContract" value="1" onclick="isContract($(this))">是</label>
                     <label><input type="radio" name="hasContract" value="0" onclick="isContract($(this))">否</label>

                 </div>
                 <div class="sale_c hasContract">
                     <div class="sale_ttl1" style="margin-left:35px;">合同编号：</div><div class="sale_con1"><input id="update_contractSn" name="contractSn" placeholder="请输入"/></div>
                     <div class="sale_ttl1">有效期至：</div><div class="sale_con1"><input id="update_expiresTime" name="expiresTime" placeholder="请选择日期"/></div>
                     <div class="sale_ttl1">签署日期：</div><div class="sale_con1"><input id="update_contractTime" name="contractTime" placeholder="请选择日期"/></div>
                 </div>--%>
                <div class="sale_c">
                    <div class="sale_ttl1">全景照片：</div>
                    <div id="edit_qImages">
                        <div id="edit_panoramaBtn" class="saleUploadBtn ty-left"></div>
                        <div class="sale_ttl1">（共可上传9张）</div>
                        <div class="imgWall"></div>
                    </div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">产品图片：</div>
                    <div id="edit_pImages">
                        <div id="edit_productPicsBtn" class="saleUploadBtn ty-left"></div>
                        <div class="sale_ttl1">（共可上传9张）</div>
                        <div class="imgWall"></div>
                    </div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">首次接触时间：</div>
                    <div class="sale_con1">
                        <input name="firstTime" id="edit_firstTime" placeholder="请选择" value=""/>
                    </div>
                    <div class="sale_ttl1">首次接触地点：</div>
                    <div class="sale_con1">
                        <input name="firstContactAddress" type="text" placeholder="请录入"/>
                    </div>
                    <div class="sale_ttl2">信息获取渠道：</div>
                    <div class="sale_con1">
                        <input name="infoSource" type="text" value="" placeholder="请录入" />
                    </div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">备注：</div>
                    <div class="sale_con4">
                        <input name="memo" type="text" onkeyup="countWords($(this),255)" />
                        <div class="textMax text-right">0/255</div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="chargeXhr($(this))">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="updateAccountBtn" onclick="updata_sure()" disabled>提交</button>
        </div>
    </div>
    <%--开票信息修改--%>
    <div class="bonceContainer bounce-blue" id="updateInvoice" style="width:1100px;">
        <div class="bonceHead">
            <span>开票信息修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form id="sale_updataInvoice">
                <p style="margin-right:12px; text-align: right; ">
                    <span style="margin-right:20px;">客户对发票方面的要求</span>
                    <span>尚未设置</span>
                    <span class="linkBtn goset_update" data-type="update"  onclick="goset($(this))">去设置</span>
                </p>
                <div class="sale_c">
                    <div class="sale_ttl1">公司名称：</div>
                    <div class="sale_con1">
                        <input data-name="invoiceName" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">地址：</div>
                    <div class="sale_con2">
                        <input data-name="invoiceAddress" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">电话：</div>
                    <div class="sale_con">
                        <input data-name="telephone" type="text" placeholder="请录入" require/>
                    </div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">开户行：</div>
                    <div class="sale_con1">
                        <input data-name="bankName" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">账号：</div>
                    <div class="sale_con1" style="margin-right: 100px;">
                        <input data-name="bank_no" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">税号：</div>
                    <div class="sale_con">
                        <input data-name="taxpayerID" type="text" placeholder="请录入" require/>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="updataInvoiceSure" onclick="updataInvoice_sure()">提交</button>
        </div>
    </div>

    <%--名片查看--%>
    <div class="bonceContainer bounce-blue" id="visitCardDetail">
        <div class="bonceHead">
            <span>名片</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="see_contactsCard">

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 机构信息修改 --%>
    <div class="bonceContainer bounce-blue" id="orgInfoChange" style="width: 680px;">
        <div class="bonceHead">
            <span class="bounce_title">机构信息修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel();"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table kj-table-none orgBase">
                <tr>
                    <td>机构名称</td>
                    <td><input type="text" name="fullName" class="ty-inputText" placeholder="请输入" disabled></td>
                </tr>
                <tr>
                    <td>机构简称</td>
                    <td><input type="text" name="name" class="ty-inputText" placeholder="请输入" disabled></td>
                </tr>
                <tr>
                    <td>机构地址</td>
                    <td><input type="text" name="address" class="ty-inputText" disabled></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管姓名</td>
                    <td><input type="text" name="supervisorName" class="ty-inputText" disabled></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管手机</td>
                    <td><input type="text" name="supervisorMobile" class="ty-inputText" disabled></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>数据存储地点 <span class="ty-color-red">*</span></td>
                    <td>
                        <select type="text" name="uploadStorageType" class="ty-inputSelect" require>
                            <option value="">请选择</option>
                            <option value="NFS">NFS</option>
                            <option value="Seafile" disabled>Seafile</option>
                        </select>
                    </td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureOrgInfoChange" id="sureOrgInfoChangeBtn" disabled>提交</button>
        </div>
    </div>
    <%-- 产品信息修改 --%>
    <div class="bonceContainer bounce-blue" id="productInfoChange" style="width: 580px;">
        <div class="bonceHead">
            <span class="bounce_title">产品信息修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel();bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table kj-table-none orgBase">
                <tr>
                    <td>机构名称 </td>
                    <td><input type="text" name="fullName" class="ty-inputText" placeholder="请输入" disabled></td>
                </tr>
                <tr>
                    <td>机构简称</td>
                    <td><input type="text" name="name" class="ty-inputText" placeholder="请输入" disabled></td>
                </tr>
                <tr>
                    <td>机构地址</td>
                    <td><input type="text" name="address" class="ty-inputText" disabled></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管姓名</td>
                    <td><input type="text" name="supervisorName" class="ty-inputText" disabled></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管手机</td>
                    <td><input type="text" name="supervisorMobile" class="ty-inputText" placeholder="请输入" disabled onkeyup="clearNum(this)"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>数据存储地点</td>
                    <td>
                        <select type="text" name="uploadStorageType" class="ty-inputSelect" disabled>
                            <option value="">请选择</option>
                            <option value="NFS">NFS</option>
                            <option value="Seafile" disabled>Seafile</option>
                        </select>
                    </td>
                </tr>
                <tr class="productPart">
                    <td>所选产品 <span class="ty-color-red">*</span></td>
                    <td><select name="packageId" class="ty-inputSelect productSelect"></select></td>
                </tr>
                <tr class="productPart">
                    <td></td>
                    <td>
                        <a class="ty-color-blue" type="btn" data-name="seeProduct">查看所选产品</a>
                    </td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce_Fixed.cancel();bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureProductInfoChange" id="sureProductInfoChangeBtn">提交</button>
        </div>
    </div>
    <%-- 新增完客户后的新增机构 --%>
    <div class="bonceContainer bounce-blue" id="newCusOrg" style="width: 680px;">
        <div class="bonceHead">
            <span class="bounce_title">新增机构</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel();bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table kj-table-none orgBase">
                <tr>
                    <td>机构名称 <span class="ty-color-red">*</span> </td>
                    <td><input type="text" name="fullName" class="ty-inputText" placeholder="请输入" require></td>
                </tr>
                <tr>
                    <td>机构简称 <span class="ty-color-red">*</span></td>
                    <td><input type="text" name="name" class="ty-inputText" placeholder="请输入" require></td>
                </tr>
                <tr>
                    <td>机构地址 <span class="ty-color-red">*</span></td>
                    <td><input type="text" name="address" class="ty-inputText"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管姓名 <span class="ty-color-red">*</span></td>
                    <td><input type="text" name="supervisorName" class="ty-inputText"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管手机 <span class="ty-color-red">*</span></td>
                    <td><input type="text" name="supervisorMobile" class="ty-inputText" placeholder="请输入" require onkeyup="clearNum(this)"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>数据存储地点 <span class="ty-color-red">*</span></td>
                    <td>
                        <select type="text" name="uploadStorageType" class="ty-inputSelect" require>
                            <option value="">请选择</option>
                            <option value="NFS">NFS</option>
                            <option value="Seafile" disabled>Seafile</option>
                        </select>
                    </td>
                </tr>
                <tr class="productPart">
                    <td>所选产品 <span class="ty-color-red">*</span></td>
                    <td><select name="packageId" class="ty-inputSelect productSelect"></select></td>
                </tr>
                <tr class="productPart">
                    <td></td>
                    <td>
                        <a class="ty-color-blue" type="btn" data-name="seeProduct">查看所选产品</a>
                    </td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureNewCusOrg" id="sureNewCusOrgBtn" disabled="disabled">提交</button>
        </div>
    </div>
    <%-- 编辑收货信息 --%>
    <div class="bonceContainer bounce-blue" id="receiveInfo" style="width: 900px;">
        <div class="bonceHead">
            <span>编辑收货信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="wideBody">
                <div class="checkItem">
                    <i class="fa fa-circle-o" data-type="1"></i>向该客户只提供服务，不提供实体货物，不需编辑收货地址
                </div>
                <div class="checkItem">
                    <i class="fa fa-circle-o" data-type="2"></i>向该客户提供实体货物
                </div>
                <div class="receiveMethod">
                    <div>
                        <div class="flexBox">
                            <div>如向该客户提供送货上门服务，请点击“新增收货地址”，以录入可能的收货地址</div>
                            <span class="linkBtn" data-fun="addAddress" data-type="new">新增收货地址</span>
                        </div>
                        <div class="dataList receiveList">
                            <table class="kj-table kj-table-control">
                                <tbody>
                                <tr>
                                    <td>收货地址</td>
                                    <td>收货人</td>
                                    <td>收货电话</td>
                                    <td>操作</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div>
                        <div class="flexBox">
                            <div>如向该客户提供配送至某城市的服务，请点击“新增到货区域”，以录入可能的到货区域</div>
                            <span class="linkBtn" data-fun="addArea" data-type="new">新增到货区域</span>
                        </div>
                        <div class="areaList">
                            <table class="kj-table kj-table-control">
                                <tbody>
                                <tr>
                                    <td>到货区域</td>
                                    <td>收货人</td>
                                    <td>收货电话</td>
                                    <td>操作</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div>
                        <div class="flexBox">
                            <div>本选项为“上门自提”选项。当前状态为“<span id="currentState"></span>”。如需要可“改变状态”。</div>
                            <span class="linkBtn selfState" data-icon="0" data-fun="turnSelfState">改变状态</span>
                        </div>
                        <div class="careful">注：状态为“未开启”的，录入该客户商品或订单的到货地点时，“上门自提”选项不展示，“开启”则反之</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 funBtn" data-fun="reciveInfoSure">确定</span>
        </div>
    </div>
    <%-- 编辑收货信息查看 --%>
    <div class="bonceContainer bounce-blue" id="receiveInfoScan" style="width: 700px;">
        <div class="bonceHead">
            <span>收货信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="wideBody">
                <div class="receiveScan">
                    <div>
                        <div class="flexBox">
                            <div>客户提供送货上门服务</div>
                        </div>
                        <div class="dataList">
                            <table class="kj-table kj-table-control recivePlaceList">
                                <thead>
                                <tr>
                                    <td width="30%">收货地址</td>
                                    <td width="20%">收货人</td>
                                    <td width="20%">收货电话</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                    <div>
                        <div class="flexBox">
                            <div>向该客户提供配送至某城市的服务</div>
                        </div>
                        <div class="areaSectScan">
                            <table class="kj-table kj-table-control areaListSee">
                                <thead>
                                <tr>
                                    <td width="30%">到货区域</td>
                                    <td width="20%">收货人</td>
                                    <td width="20%">收货电话</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="ownPick"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">关 闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="confirm_tip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="flex-row">当前为客户创建阶段，客户下的专属商品尚未创建，所以新增的合同里无法添加专属商品。</div>
                <div class="flex-row">您可“继续操作”，待有了专属商品的数据后再完善相关信息。</div>
            </div>
            <div>
                <span class="fa fa-circle-o gapFar" onclick="noMoreTip($(this))"></span>知道了，不再提示！
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="confirm_tipOk()">继续操作</span>
        </div>
    </div>
    <%-- 温馨提示 --%>
    <div class="bonceContainer bounce-red" id="mtTip" style="width: 400px;">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center;font-size:14px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 一级提示 --%>
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg text-center"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <%-- 导入 --%>
    <div class="bonceContainer bounce-green" id="leading" style="width:750px">
        <div class="bonceHead">
            <span>导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <iframe src="../script/sales/cusImport.jsp" frameborder="0" id="importCon"> </iframe>

    </div>
    <%-- 暂停合作/恢复合作提示 --%>
    <div class="bonceContainer bounce-blue" id="confirmSuspendCooperation" style="width: 400px;">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center stop">
                <p>在系统中无法向被暂停合作的客户录入订单。</p>
                <p>确定暂停与该客户的合作吗？</p>
            </div>
            <div class="text-center start" style="display: none">
                <p>确定与该客户恢复合作吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureSuspendCooperation()">确定</span>
        </div>
    </div>
    <%-- 新增客户 --%>
    <div class="bonceContainer bounce-blue" id="addAccount" style="min-width: 1200px;">
        <div class="bonceHead">
            <span>新增客户</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon"  style="height: 600px;overflow: auto">
            <form class="sale_updata" id="addpayDetails">
                <div class="">
                    <p>基本信息</p>
                    <div class="sale_c">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>客户名称：</div>
                        <div class="sale_con1"><input type="text" id="add_cusname" class="add_cusName" name="fullName" placeholder="请录入" require style="width: 580px"></div>

                        <div class="sale_ttl1">客户代号：</div>
                        <div class="sale_con1"><input type="text" id="add_cuscoding" name="code" placeholder="请录入"></div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">地址：</div>
                        <div class="sale_con1"><input type="text" id="add_address1" name="address" placeholder="请录入" style="width: 580px"></div>
                        <div class="sale_ttl1">客户简称：</div>
                        <div class="sale_con1"><input type="text" id="add_cusfullName" name="name" placeholder="请录入"></div>
                    </div>

                    <div class="sale_c specialOrgPart">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>最高负责人：</div>
                        <div class="sale_con1"><input type="text" id="add_supervisorName" name="supervisorName" placeholder="请录入" require></div>
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>手机：</div>
                        <div class="sale_con2"><input type="text" id="add_supervisorMobile" name="supervisorMobile" placeholder="请录入" require onkeyup="clearNum(this)"></div>
                    </div>
                    <div class="sale_c">
                        <span style="margin-left:35px;">是否已购买过产品或服务？</span>
                        <label><input type="radio" name="buyCase" value="1" onclick="purchasedAny($(this))">是</label>
                        <label><input type="radio" name="buyCase" value="0" onclick="purchasedAny($(this))">否</label>
                        <span class="purchased">
                            <span>首次购买时间：</span>
                            <label><input type="radio" name="firstTime" onclick="checkMonth($(this))" value="1">今年</label>
                            <input id="buyMonth" name="initialPeriod" placeholder="请选择月份" style="display: none;" />
                            <label><input type="radio" name="firstTime" value="2" onclick="checkMonth($(this))">去年</label>
                            <label><input type="radio" name="firstTime" value="3" onclick="checkMonth($(this))">更久之前</label>
                        </span>
                    </div>
                    <%--     <div class="sale_c purchased">
                             <span style="margin-left:50px;">是否已与该客户签订合同，且合同处于有效期内？<span class="ty-color-red">*</span></span>
                             <label><input type="radio" name="hasContract" value="1" onclick="isContract($(this))">是</label>
                             <label><input type="radio" name="hasContract" value="0" onclick="isContract($(this))">否</label>

                         </div>
                         <div class="sale_c hasContract">
                             <div class="sale_ttl1" style="margin-left:35px;">合同编号：</div><div class="sale_con1"><input name="contractSn" placeholder="请输入"/></div>
                             <div class="sale_ttl1">有效期至：</div><div class="sale_con1"><input id="expiresTime" name="expiresTime" placeholder="请选择日期"/></div>
                             <div class="sale_ttl1">签署日期：</div><div class="sale_con1"><input id="contractTime" name="contractTime" placeholder="请选择日期"/></div>
                         </div>--%>
                    <div class="sale_c">
                        <div class="sale_ttl1">全景照片：</div>
                        <div id="qImages">
                            <div id="panoramaBtn" class="saleUploadBtn ty-left"></div>
                            <div class="sale_ttl1">（共可上传9张）</div>
                            <div class="imgWall"></div>
                        </div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">产品图片：</div>
                        <div id="pImages">
                            <div id="productPicsBtn" class="saleUploadBtn ty-left"></div>
                            <div class="sale_ttl1">（共可上传9张）</div>
                            <div class="imgWall"></div>
                        </div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">首次接触时间：</div>
                        <div class="sale_con1">
                            <input id="firstContactTime" placeholder="请选择" value=""/>
                        </div>
                        <div class="sale_ttl1">首次接触地点：</div>
                        <div class="sale_con1">
                            <input name="firstContactAddress" type="text"/>
                        </div>
                        <div class="sale_ttl2">信息获取渠道：</div>
                        <div class="sale_con1">
                            <input name="infoSource" type="text" value="" placeholder="请录入">
                        </div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">备注：</div>
                        <div class="sale_con4">
                            <input name="memo" type="text" onkeyup="countWords($(this),255)" />
                            <div class="textMax text-right">0/255</div>
                        </div>
                    </div>
                    <p>开票信息
                        <span class="ty-right" style="margin-right:80px; ">
                            <span style="margin-right:20px;">客户对发票方面的要求</span>
                            <span>尚未设置</span>
                            <span class="linkBtn goset_add" data-type="add" onclick="goset($(this))">去设置</span>
                        </span>
                    </p>
                    <div class="sale_c" style="overflow: initial;">
                        <div class="sale_ttl1">公司名称：</div>
                        <div class="sale_con1">
                            <input name="invoiceName" type="text" placeholder="请录入"/>
                        </div>
                        <div class="sale_ttl1">地址：</div>
                        <div class="sale_con2">
                            <input name="invoiceAddress" type="text" placeholder="请录入"/>
                        </div>
                        <div class="sale_ttl1">电话：</div>
                        <div class="sale_con">
                            <input name="telephone" type="text" placeholder="请录入"/>
                        </div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">开户行：</div>
                        <div class="sale_con1">
                            <input name="bankName" type="text" placeholder="请录入"/>
                        </div>
                        <div class="sale_ttl1">账号：</div>
                        <div class="sale_con2">
                            <input name="bankNo" type="text" placeholder="请录入"/>
                        </div>
                        <div class="sale_ttl1">税号：</div>
                        <div class="sale_con">
                            <input name="taxpayerID" type="text" placeholder="请录入"/>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl"><span class="ty-color-red">*</span>货物的交付地点与方式</span>
                        <span id="newAddress" class="ty-btn ty-btn-blue funBtn ty-btn-big" data-type="new" data-fun="receiveInfo" data-source="addCustomer">编 辑</span>
                        <div>
                            <div class="goodsAddress require-box"></div>
                            <div class="hd"></div>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">合同信息</span>
                        <span id="newContract" class="ty-btn ty-btn-blue ty-btn-big node" data-type="new" data-name="contractInfo" data-source="addCustomer">新 增</span>
                        <span style="color: #5399c2;  font-size: 0.8em; margin-left: 50px;">注：点击“新增”，可录入该客户的合同信息。如与该客户有多个合同，需多次“新增”。</span>
                        <div class="dataList contractList">
                            <table class="kj-table kj-table-control">
                                <thead>
                                <td>合同编号</td>
                                <td>签署日期</td>
                                <td>合同的有效期</td>
                                <td>操作</td>
                                </thead>
                                <tbody id="clist"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">发票邮寄信息</span>
                        <span id="newMail" class="ty-btn ty-btn-blue ty-btn-big node" data-type="new" data-name="mailInfo" data-source="addCustomer">新增</span>
                        <div class="dataList mailList">
                            <table class="kj-table kj-table-control"></table>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">联系人</span>
                        <span id="newContact" class="ty-btn ty-btn-blue ty-btn-big node" data-type="new" data-name="contactInfo" data-source="addCustomer">新增</span>
                        <div class="dataList contectList"></div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="chargeXhr($(this))">取消</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="addAccountBtn" onclick="sale_addsure()">提交</button>
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 node" type="btn" data-name="notSelectModule" id="notSelectModuleBtn">录入完毕，暂不选择产品</button>
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 node" type="btn" data-name="selectModule" id="selectModuleBtn">录入完毕，选择产品</button>
            </span>
            <%--<div class="specialOrgPart">--%>
            <%--<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="" onclick="sale_addsure()">录入完毕，暂不勾选模块</button>--%>
            <%--<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="" onclick="sale_addsure()">录入完毕，勾选模块</button>--%>
            <%--</div>--%>
        </div>
    </div>
    <%-- 客户信息查看 --%>
    <div class="bonceContainer bounce-blue" id="seeAccount" style="min-width:1220px;">
        <div class="bonceHead">
            <span>客户信息查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails sale_see">
                <section class="part salePanel">
                    <div class="row-flex">
                        <div class="item"><span class="item_title">客户名称：</span><span class="item_content" id="see_cusname" style="width: 600px"></span></div>
                        <div class="item"><span class="item_title">客户代号：</span><span class="item_content" id="see_cuscoding"></span></div>
                    </div>
                    <div class="row-flex">
                        <div class="item"><span class="item_title">地址：</span><span class="item_content" id="see_address" style="width: 600px"></span></div>
                        <div class="item"><span class="item_title">客户简称：</span><span class="item_content" id="see_cusfullName"></span></div>
                    </div>
                    <%--<div class="clear rowGap"><span class="ty-left" id="see_address"></span></div>--%>
                    <div class="row-flex specialOrgPart">
                        最高负责人 <span id="see_supervisorName"></span> <span id="see_supervisorMobile"></span>（手机号）
                    </div>
                    <div class="firstBuyTime"></div>
                    <div class="row-flex">
                        <div class="item"><span class="item_title">创建者：</span><span class="item_content"><span id="see_createName"></span> <span id="see_createDate"></span></span></div>
                        <div class="right_btn"><button class="ty-btn ty-btn-blue ty-circle-3"  onclick="getSuspendRecordList($(this))">暂停合作/恢复合作的操作记录</button></div>
                        <div class="right_btn"><button class="ty-btn ty-btn-blue ty-circle-3"  data-type="baseRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</button></div>
                    </div>
                </section>
                <div class="kj-hr"></div>
                <section class="part salePanel">
                    <div class="row-flex">
                        <div class="item">
                            <div class="item_title">全景照片：</div>
                            <div class="item_content" id="overallImgUpload"></div>
                        </div>
                    </div>
                    <div class="row-flex">
                        <div class="item">
                            <div class="item_title">产品图片：</div>
                            <div class="item_content" id="productImgUpload"></div>
                        </div>
                    </div>
                    <div class="row-flex">
                        <div class="item">
                            <div class="item_title">首次接触时间：</div>
                            <div class="item_content" id="see_firstContactTime"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">首次接触地点：</div>
                            <div class="item_content" id="firstContactAddress"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">信息获取渠道：</div>
                            <div class="item_content" id="infoSource"></div>
                        </div>
                    </div>
                    <div class="row-flex">
                        <div class="item">
                            <div class="item_title">备注：</div>
                            <div class="item_content" id="see_memo"></div>
                        </div>
                    </div>
                </section>
                <div class="kj-hr"></div>
                <section class="part salePanel">
                    <div class="panel_title">
                        <div class="title">开票信息</div>
                        <div class="btn-group">
                            客户对发票方面要求的设置：<span id="see_invoiceRequire" style="color: #62707e"></span>
                            <button class="ty-btn ty-btn-blue ty-circle-3"  data-type="invoiceRecords" data-obj="see" onclick="getRecordList($(this))" style="margin-left: 36px">修改记录</button>
                        </div>
                    </div>
                    <div class="panel_content">
                        <div class="row-flex">
                            <div class="item">
                                <div class="item_title">公司名称：</div>
                                <div class="item_content" id="see_invoiceName"></div>
                            </div>
                            <div class="item">
                                <div class="item_title">地址：</div>
                                 <div class="item_content" id="see_invoiceAddress"></div>
                            </div>
                            <div class="item">
                                <div class="item_title">电话：</div>
                                <div class="item_content" id="see_phone"></div>
                            </div>
                        </div>
                        <div class="row-flex">
                            <div class="item">
                                <div class="item_title">开户行：</div>
                                <div class="item_content" id="see_bank"></div>
                            </div>
                            <div class="item">
                                <div class="item_title">账号：</div>
                                <div class="item_content" id="see_bankNo"></div>
                            </div>
                            <div class="item">
                                <div class="item_title">税号：</div>
                                <div class="item_content" id="taxpayerID"></div>
                            </div>
                        </div>
                    </div>
                </section>
                <%--<div class="ty-alert ty-alert-warning">联系人、收货信息或发票邮寄信息中，红色字体者均为被停用的。</div>--%>
                <section class="item_avatar salePanel">
                    <div class="panel_title">
                        <div class="title">联系人</div> <span style="margin-left: 36px">该客户现共有如下<span class="contactNum"></span>位联系人</span>
                    </div>
                    <div class="panel_content">
                        <div class="see_contactList" style="overflow: hidden"></div>
                    </div>
                </section>
                <section class="item_avatar salePanel">
                    <div class="panel_title">
                        <div class="title">合同信息</div>
                    </div>
                    <div class="panel_content">
                        <table class="kj-table kj-table-control contractPlaceList">
                            <thead>
                            <tr>
                                <td>合同编号</td>
                                <td>签署日期</td>
                                <td>合同的有效期</td>
                                <td>本合同下的通用型商品</td>
                                <td>本合同下的专属商品</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </section>
                <section class="item_avatar salePanel">
                    <div class="panel_title">
                        <div class="title">货物的交付地点与方式</div>
                        <div class="btn-group">
                            <span class="ty-btn ty-btn-blue ty-circle-3 recivePlaceScanBtn" onclick="recivePlaceScan()">查看</span>
                        </div>
                    </div>
                    <div class="panel_content">
                        <div class="reciveDescript"></div>
                        <div class="hd"></div>
                    </div>
                </section>
                <section class="item_avatar salePanel">
                    <div class="panel_title">
                        <div class="title">发票邮寄信息</div>
                    </div>
                    <div class="panel_content">
                        <table class="kj-table kj-table-control mailPlaceList">
                            <thead>
                            <tr>
                                <td width="10%">序号</td>
                                <td width="30%">邮寄地址</td>
                                <td width="20%">发票接收人</td>
                                <td width="20%">邮政编码</td>
                                <td width="20%">操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </section>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 空间与流量 --%>
    <div class="bonceContainer bounce-blue" id="resourceManage" >
        <div class="bonceHead">
            <span>空间与流量</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="rscon">
                <p class="flextr"><span>机构名称</span><span class="orgfullname"></span></p>
                <p class="flextr"><span>机构简称</span><span class="orgname"></span></p>
            </div>
            <div class="rscon">
                <p class="flextr"><span>空间上限</span><span class="zoneTop"></span></p>
                <p class="flextr"><span>已用空间</span><span class="usedZone"></span></p>
            </div>
            <div class="rscon">
                <p class="zoneduraing">2021年1月1日 - 2020年5月6日</p>
                <p class="flextr"><span>期间的流量上限</span><span class="flowAll"></span></p>
                <p class="flextr"><span>此期间已用流量</span><span class="flowUsed"></span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 删除客户 --%>
    <div class="bonceContainer bounce-red" id="deleteAccount" style="width:800px">
        <div class="bonceHead">
            <span>删除客户</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="sale_delete">
                    <p style="text-align:center;padding:10px 0;" id="customerDelTip"></p>
                    <p id="del_saleID" class="hd"></p>
                    <p> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="sale_delsure()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 访谈记录 --%>
    <div class="bonceContainer bounce-blue" id="newInterviewRecords" style="min-width:1100px;">
        <div class="bonceHead">
            <span>访谈记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <span class="intervieweeName">阿里巴巴网络技术有限公司</span>
            <button class="ty-btn ty-btn-green ty-btn-big" data-type="new" id="newInterviewBtn" onclick="newInterview($(this))">新增</button>
            <table class="kj-table kj-table-control interviewRecordsList">
                <thead>
                <tr>
                    <td>序号</td>
                    <td>访谈对象</td>
                    <td>职位</td>
                    <td>访谈日期</td>
                    <td>录入者</td>
                    <td>操作</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div id="ye-interview"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 修改客户总列表 --%>
    <div class="bonceContainer bounce-blue" id="updateCustomerPanel" style="min-width:800px;">
        <div class="bonceHead">
            <span>客户信息管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p id="customer_main" class="ty-color-green"></p>
                <table class="kj-table kj-table-control indexUpdateList">
                    <thead>
                    <tr>
                        <td>名称</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>基本信息</td>
                        <td>
                            <span class="link-blue" data-name="updateBase" onclick="sale_updatabtn($(this))">修改</span>
                            <span class="link-blue" data-type="baseRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>
                        </td>
                    </tr>
                    <tr>
                        <td>开票信息</td>
                        <td>
                            <span class="link-blue" data-name="updateInvoice" onclick="sale_updatabtn($(this))">修改</span>
                            <span class="link-blue" data-type="invoiceRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>
                        </td>
                    </tr>
                    <tr id="contractPanel">
                        <td>合同信息</td>
                        <td>
                            <span class="link-blue node" data-name="contractInfo" data-type="new" data-source="updateCustomer">新增</span>
                            <span class="link-blue node" data-name="contractEndData" data-type="1" data-source="updateCustomer">已到期的合同</span>
                            <span class="link-blue node" data-name="contractStopData" data-type="2" data-source="updateCustomer">已暂停/终止的合同</span>
                        </td>
                    </tr>
                    <tr id="receivePanel">
                        <td>收货地址（需送货上门情况下）</td>
                        <td>
                            <span class="link-blue edit" data-name="receiveInfo" data-type="new" data-source="updateCustomer">新增</span>
                            <span class="link-blue edit" data-name="receiveStopData" data-type="1" data-source="updateCustomer">已被停用的数据</span>
                        </td>
                    </tr>
                    <tr id="mailPanel">
                        <td>发票邮寄信息</td>
                        <td>
                            <span class="link-blue edit" data-name="mailInfo" data-type="new" data-source="updateCustomer">新增</span>
                            <span class="link-blue edit" data-name="mailStopData" data-type="2" data-source="updateCustomer">已被停用的数据</span>
                        </td>
                    </tr>
                    <tr id="contactPanel">
                        <td>联系人</td>
                        <td>
                            <span class="link-blue edit" data-name="contactInfo" data-type="new" data-source="updateCustomer">新增</span>
                            <span class="link-blue edit" data-name="contactDelData">已被删除的数据</span>
                        </td>
                    </tr>


                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 批量导入 --%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="custormerLeading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #ddebf7;">
            <form action="../export/ImportMaterial.do" id="materielImport" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：下载空白的“客户清单”。</p>
                        <div class="flexRow">
                            <span>客户清单</span>
                            <%--<a href="../assets/oralResource/template/material_blank_sheet1.xls"--%>
                            <a href="../assets/oralResource/template/customer_blank.xls"
                               id="mould1" download="客户清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“客户清单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="uploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="matImportOk('cancel');">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="matImportOk()">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加、删除或修改所下载“客户清单”空白表的“列”，否则上传会失败。</span></br>
                            <span>2、在电脑上保存“客户清单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot" style="background: #ddebf7;">
        </div>
    </div>
    <%-- 机构信息 --%>
    <div class="bonceContainer bounce-blue" id="orgInfo" style="width: 500px;">
        <div class="bonceHead">
            <span class="bounce_title">机构信息</span>
            <a class="bounce_close" onclick="bounce.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div class="text-right btn-group">
                <a class="ty-color-blue" type="btn" data-name="orgInfo" id="changeOrgInfoBtn">修改机构信息</a>
                <a class="ty-color-blue" type="btn" data-name="orgChangeHistory">机构信息变动记录</a>
            </div>
            <div class="text-right btn-group">
                <a class="ty-color-blue" type="btn" data-name="stopRecoveryRecord">暂停服务/恢复服务的操作记录</a>
            </div>
            <table class="kj-table kj-table-none orgBase">
                <tr>
                    <td>机构名称</td>
                    <td><span class="orgInfo_content orgInfo_fullName"></span></td>
                </tr>
                <tr>
                    <td>机构简称</td>
                    <td><span class="orgInfo_content orgInfo_name"></span></td>
                </tr>
                <tr>
                    <td>经营地址</td>
                    <td><span class="orgInfo_content orgInfo_address"></span></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管姓名</td>
                    <td><span class="orgInfo_content orgInfo_supervisorName"></span></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管手机</td>
                    <td><span class="orgInfo_content orgInfo_supervisorMobile"></span></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>数据存储地点</td>
                    <td><span class="orgInfo_content orgInfo_uploadStorageType"></span></td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce.cancel();">关闭</button>
        </div>
    </div>
    <%-- 产品信息 --%>
    <div class="bonceContainer bounce-blue" id="productInfo" style="width: 680px;">
        <div class="bonceHead">
            <span class="bounce_title">产品信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table kj-table-none">
                <tr>
                    <td>机构名称</td>
                    <td><span class="orgInfo_content orgInfo_fullName"></span></td>
                </tr>
                <tr>
                    <td>机构简称</td>
                    <td><span class="orgInfo_content orgInfo_name"></span></td>
                </tr>
                <tr>
                    <td>经营地址</td>
                    <td><span class="orgInfo_content orgInfo_address"></span></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管姓名</td>
                    <td><span class="orgInfo_content orgInfo_supervisorName"></span></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管手机</td>
                    <td><span class="orgInfo_content orgInfo_supervisorMobile"></span></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>数据存储地点</td>
                    <td><span class="orgInfo_content orgInfo_uploadStorageType"></span></td>
                </tr>
                <tr class="productPart">
                    <td>所选产品</td>
                    <td><span class="orgInfo_content orgInfo_mpPackageName"></span></td>
                </tr>
                <tr class="productPart">
                    <td><span class="ty-color-gray seeProductBtnPart">所选产品修改记录</span></td>
                    <td>
                        <a class="ty-color-blue seeProductBtnPart" type="btn" data-name="changeProduct" id="changeProductBtn" style="margin-right: 90px">修改所选产品</a>
                        <a class="ty-color-blue" type="btn" data-name="seeProduct">查看所选产品</a>
                    </td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce.cancel();">关闭</button>
        </div>
    </div>
    <%-- 新增机构 --%>
    <div class="bonceContainer bounce-blue" id="newOrg" style="width: 600px;">
        <div class="bonceHead">
            <span class="bounce_title">新增机构</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table kj-table-none orgBase">
                <tr>
                    <td>机构名称 <span class="ty-color-red">*</span> </td>
                    <td><input type="text" name="fullName" class="ty-inputText" placeholder="请输入" require></td>
                </tr>
                <tr>
                    <td>机构简称 <span class="ty-color-red">*</span></td>
                    <td><input type="text" name="name" class="ty-inputText" placeholder="请输入" require></td>
                </tr>
                <tr>
                    <td>经营地址</td>
                    <td><input type="text" name="address" class="ty-inputText" placeholder="请输入"></td>
                </tr>
                <tr>
                    <td>注册地址</td>
                    <td><input type="text" name="registeredAddress" class="ty-inputText" placeholder="请输入"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管姓名</td>
                    <td><input type="text" name="supervisorName" class="ty-inputText"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>超管手机 <span class="ty-color-red">*</span></td>
                    <td><input type="text" name="supervisorMobile" class="ty-inputText" placeholder="请输入" require onkeyup="clearNum(this)"></td>
                </tr>
                <tr class="orgInfoPart">
                    <td>数据存储地点 <span class="ty-color-red">*</span></td>
                    <td>
                        <select type="text" name="uploadStorageType" class="ty-inputSelect" require>
                            <option value="">请选择</option>
                            <option value="NFS">NFS</option>
                            <option value="Seafile" disabled>Seafile</option>
                        </select>
                    </td>
                </tr>
                <tr class="productPart">
                    <td>所选产品 <span class="ty-color-red">*</span></td>
                    <td><select name="packageId" class="ty-inputSelect productSelect" require></select></td>
                </tr>
                <tr class="productPart">
                    <td></td>
                    <td>
                        <a class="ty-color-blue" type="btn" data-name="seeProduct">查看所选产品</a>
                    </td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureNewOrg" id="sureNewOrgBtn" disabled="disabled">提交</button>
        </div>
    </div>
    <%-- 增值服务 --%>
    <div class="bonceContainer bounce-blue" id="addService" style="width: 600px;">
        <div class="bonceHead">
            <span class="bounce_title">增值服务</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <table class="kj-table kj-table-none">
                    <tr>
                        <td style="width: 150px">机构名称</td>
                        <td><span class="ty-spanText" name="fullName"></span></td>
                    </tr>
                    <tr>
                        <td>机构简称</td>
                        <td><span class="ty-spanText" name="name"></span></td>
                    </tr>
                    <tr>
                        <td>所用产品</td>
                        <td>
                            <span class="ty-spanText" name="mpPackageName"></span>
                            <input name="packageId" style="display: none">
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td><a class="link-blue" type="btn" data-name="seeProduct">查看所选产品</a></td>
                    </tr>
                </table>
                <div class="item-flex">
                    <div class="ty-radio">
                        <input type="radio" name="checkAngle" id="angle_module" value="1" onchange="chooseCheckAngle()">
                        <label for="angle_module"></label> 作为该产品的增值服务，仅从模块的角度勾选
                    </div>
                </div>
                <table class="kj-table kj-table-striped tbl_asModule">
                    <thead>
                    <tr>
                        <td style="width: 50px"></td>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td style="width: 80px">操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="item-flex">
                    <div class="ty-radio">
                        <input type="radio" name="checkAngle" id="angle_mpSet" value="2" onchange="chooseCheckAngle()">
                        <label for="angle_mpSet"></label> 作为该产品的增值服务，仅从套餐的角度勾选
                    </div>
                </div>
                <table class="kj-table kj-table-striped tbl_asMpSet">
                    <thead>
                    <tr>
                        <td style="width: 50px"></td>
                        <td>套餐名称</td>
                        <td style="width: 80px">操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="sureAddService">提交</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>客户管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <div id="picShow" style="display: none;">
                        <img src=""/>
                    </div>
                    <div>
                        <div class="main">
                            <div class="ty-alert">
                                <div class="Search">
                                    <input id="se0" type="text" placeholder="客户名称/客户代号"/>
                                    <span class="se" onclick="searchCustomer()">搜索</span>
                                </div>
                                <div class="btn-group">
                                    <span class="panel_4 ty-btn ty-btn-big ty-btn-cyan ty-circle-3" id="customerImport" onclick="leadingShow()">批量导入</span>
                                    <span class="panel_4 ty-btn ty-btn-big ty-btn-green ty-circle-3" id="addCus" onclick="sale_addbtn_confirm($(this))">新增客户</span>
                                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3"  onclick="suspendedCustomer()" id="suspendedCustomerBtn">已暂停合作的客户</span>
                                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="outOfOrg" id="outOfOrgBtn" style="display: none">已暂停服务的机构</span>
                                </div>
                            </div>
                            <div class="opinionCon">
                                <table class="kj-table kj-table-striped">
                                    <thead>
                                    <tr>
                                        <td>序号</td>
                                        <td>客户代号</td>
                                        <td>客户名称</td>
                                        <td>创建者</td>
                                        <td>销售负责人</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody class="sale_Tbody" id="cusManage_body"></tbody>
                                </table>
                                <div id="ye_customerManage"></div>
                            </div>
                            <div class="clr"></div>
                        </div>
                        <%--已暂停合作的客户--%>
                        <div class="page suspendedCustomer" name="suspendedCustomer" style="display: none;">
                            <div>
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="goBack()">返回</span>
                            </div>
                            <div>
                                <p style="margin-top:20px; ">以下客户已暂停合作</p>
                                <table class="kj-table kj-table-striped">
                                    <thead>
                                    <tr>
                                        <td>序号</td>
                                        <td>客户代号</td>
                                        <td>客户名称</td>
                                        <td>创建者</td>
                                        <td>暂停合作的时间</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div id="ye_suspendCustomer"></div>
                            </div>
                        </div>
                        <div class="page orgManage" name="orgManage" style="display: none;">
                            <div>
                                <span class="ty-btn ty-btn-big ty-btn-orange ty-circle-3" type="btn" data-name="goBack">返回</span>
                            </div>
                            <div>
                                <div class="ty-alert">
                                    <b class="orgName ty-color-blue" style="margin-right: 8px; font-size: 14px"></b> 名下有如下机构：
                                    <div class="btn-group">
                                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" type="btn" data-name="newOrg">新增机构</span>
                                    </div>
                                </div>
                                <%-- 机构列表 --%>
                                <table class="kj-table kj-table-striped orgList">
                                    <thead>
                                    <tr>
                                        <td>机构简称</td><!--name-->
                                        <td>机构名称</td><!--fullName-->
                                        <td>超管姓名</td><!--supervisorName-->
                                        <td>超管手机</td><!--supervisorMobile-->
                                        <td>经营地址</td><!--address-->
<%--                                        <td>注册地址</td><!--registeredAddress-->--%>
                                        <td>创建者</td><!--不变-->
                                        <td>操作</td><!--不变-->
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="page loginRecord" name="loginRecord" style="display: none;">
                            <div>
                                <span class="ty-btn ty-btn-big ty-btn-orange ty-circle-3" type="btn" data-name="goBack">返回</span>
                            </div>
                            <div class="clear">
                                <div class="ty-left">
                                    <span class="gapT">登录记录</span>
                                    <div class="gapT">
                                        <div class="gapL" style="display: inline-block;">
                                            <span class="sale-ttl">机构名称</span>
                                            <span id="sysOrgName"></span>
                                        </div>
                                        创建人：<span id="orgCreateName"></span>
                                    </div>
                                </div>
                                <div class="ty-right flagTab">
                                    <div class="loginQuery" style="position: relative;display: inline-block;vertical-align: middle;">
                                        <span class="ty-btn ty-btn-big loginQuery" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" id="loginQueryBtn" value="4">自定义查询</span>
                                        <ul class="dropdown-menu dropdown-menu-default searchCon"  >
                                            <table class="kj-table definedSize">
                                                <tbody>
                                                <tr>
                                                    <td>起时间：</td>
                                                    <td><input type="text" class="ty-inputText" id="queryBeginTime"></td>
                                                </tr>
                                                <tr>
                                                    <td>止时间：</td>
                                                    <td><input type="text" class="ty-inputText" id="queryEndTime"></td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td align="right">
                                                        <span class="ty-btn ty-btn-big ty-circle-3">取消</span>
                                                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureLoginQuery()">查询</span>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </ul>
                                    </div>
                                    <div class="ty-btn-groupCom" id="changeState">
                                        <span class="ty-btn ty-btn-big ty-btn-active-blue" value="2">本月</span>
                                        <span class="ty-btn ty-btn-big" value="3">本年</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <%-- 机构列表 --%>
                                <table class="kj-table kj-table-striped" id="userLoginRecord">
                                    <thead>
                                    <tr>
                                        <td class="dateType">登录月份</td>
                                        <td>登录人数</td>
                                        <td>登录总时长</td>
                                        <td>登录总次数</td>
                                        <td>电脑端登录总次数</td>
                                        <td>手机端登录总次数</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="page page_outOfOrg" name="page_outOfOrg" style="display: none;">
                            <div>
                                <span class="ty-btn ty-btn-big ty-btn-orange ty-circle-3" type="btn" data-name="goBack">返回</span>
                            </div>
                            <div>
                                <div class="ty-alert">
                                    已被暂停服务的机构如下：
                                </div>
                                <%-- 机构列表 --%>
                                <table class="kj-table kj-table-striped orgList">
                                    <thead>
                                    <tr>
                                        <td>序号</td>
                                        <td>拥有最高权限的手机号码</td>
                                        <td>超管姓名</td>
                                        <td>机构名称</td>
                                        <td>暂停服务的操作者</td>
                                        <td>所属租户</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div id="ye_outOfOrg"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<%--<script src="../script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>--%>
<script src="../script/wonderssProduct/sysCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/sales/customerManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/sales/region.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    $(function(){
        if (sphdSocket.user.oid === 0) {
            $(".specialOrgPart").show()
            $("#addAccountBtn").hide().siblings().show()
        } else {
            $(".specialOrgPart").hide()
            $("#addAccountBtn").show().siblings().hide()
        }
        if (chargeRole("超管")) { // 不是超管，都显示新增按钮
            $("#addCus").remove();
        }else{
            getCustomerMes(1 , 20, '');

        }
    })
</script>
</body>
</html>