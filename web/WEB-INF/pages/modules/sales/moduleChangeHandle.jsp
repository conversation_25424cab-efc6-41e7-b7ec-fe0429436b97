<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/wonderssProduct/sysCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/sale.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .main{
        width: 620px;
    }
    .processList{
        margin: 8px 0;
        text-align: right;
    }
    .approveBtn{
        margin-bottom: 8px;
        text-align: right;
    }
    .tipCon{
        width: 300px;
        margin: auto;
    }
    .tipCon textarea{
        width: 100%;
    }
    .difference{
        color: #ed5565;
    }
    .compare_icon{
        padding: 4px 8px;
        color: #aaa;
    }
    .productName{
        font-size: 16px;
        font-weight: bold;
    }
    .modelNameRow{
        font-size: 14px;
        line-height: 36px;
        border-bottom: 1px solid #eee;
        color: #999;
    }
    .modelNameRow .modelName{
        margin-left: 8px;
        color: #666;
    }
    .changeNum{
        margin: 0 4px;
        font-weight: bold;
    }
    .nullStr{
        padding: 4px;
        text-align: center;
        color: #999;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed5">
    <div class="bonceContainer bounce-blue" id="seeModule" style="width: 700px">
        <div class="bonceHead">
            <span>模块查看</span>
            <a class="bounce_close" onclick="bounce_Fixed5.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><b class="moduleName"></b></h4>
                <table class="kj-table moduleTable">
                    <thead>
                    <tr>
                        <td class="thisLevel">一级菜单</td>
                        <td class="nextLevel">
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td class="thisLevel">二级菜单</td>
                                    <td class="nextLevel">
                                        <table class="kj-table">
                                            <thead>
                                            <tr>
                                                <td class="thisLevel">三级菜单</td>
                                            </tr>
                                            </thead>
                                        </table>
                                    </td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed5.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed4">
    <%--套餐查看--%>
    <div class="bonceContainer bounce-blue" id="seeSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐查看</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><span class="setMenuName"></span></h4>
                <div class="ty-alert">
                    组成本套餐的模块
                    <div class="btn-group">
                        创建 <span class="create"></span>
                    </div>
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%--套餐清单查看--%>
    <div class="bonceContainer bounce-blue" id="seeUsableSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐清单查看</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="ty-alert">
                    本模板的用户不可选择的模块数为A，设系统内已有套餐数为B
                    需根据A、B，算出系统内不包含A的已有套餐数C，并需列出C的清单
                    机构可选择的套餐，即为上述C的清单，具体如下：
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>套餐名称</td>
                        <td>创建</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 查看产品--%>
    <div class="bonceContainer bounce-blue" id="seeProduct" style="width: 700px">
        <div class="bonceHead">
            <span>产品查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><span class="pdName"></span></h4>
                <div class="item-flex">
                    <div class="item-title">所选模板：</div>
                    <div class="item-content"><span class="pdModel"></span><div class="ty-right">操作人 <span class="pdTime"></span></div></div>
                </div>
                <div class="ty-alert">
                    已重命名模块的数量 <b class="renameNumber"></b> 个
                </div>
                <table class="kj-table tbl_see_rename" style="width: 70%">
                    <thead>
                    <tr>
                        <td>原名称</td>
                        <td>新名称</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert">主套餐内的模块</div>
                <table class="kj-table kj-table-striped tbl_see_mainModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    使用本模板的用户增值功能的模块
                </div>
                <table class="kj-table kj-table-striped tbl_see_valueAddedModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    与本模板增值功能模块对应的已有套餐
                    <div class="btn-group">
                        <span class="link-blue" onclick="seeUsableSetMenuBtn('seeModel')">查看</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="tip" style="width: 450px">
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipCon text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="agreeTip" style="width: 450px">
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipCon text-center">确定同意该修改申请吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="approveTrial(1);">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="rejectTip" style="width: 450px">
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipCon">
                <p>请输入驳回理由：</p>
                <textarea class="reason" cols="30" rows="3"></textarea>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="approveTrial(2);">确定</span>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>处理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <div id="picShow" style="display: none;">
                        <img src=""/>
                    </div>
                    <div class="main">
                        <div class="ty-alert discribe"></div>
                        <table class="kj-table changeInfo">
                            <tbody>
                                <tr>
                                    <td>修改后产品</td>
                                    <td></td>
                                    <td>修改前产品</td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="processList"></div>
                        <div class="approveBtn" style="display: none;">
                            <button class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type="btn" data-name="reject">驳回</button>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="agree">批准</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="${pageContext.request.contextPath }/script/sales/customerManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="${pageContext.request.contextPath }/script/wonderssProduct/sysCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="${pageContext.request.contextPath }/script/sales/moduleChangeHandle.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
