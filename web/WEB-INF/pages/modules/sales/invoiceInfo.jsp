<%--
  Created by IntelliJ IDEA.
  User: houxingzhe
  Date: 2023/11/1
  Time: 11:40
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
  <%-- 头部样式 --%>
  <link href="../css/common/theme/green.css?v=SVN_REVISION"  rel="stylesheet" type="text/css" >
    <link href="../assets/global/plugins/font-awesome/css/font-awesome.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.4/clipboard.min.js"></script>
  <%--<%@ include  file="../../common/headerTop.jsp"%>--%>
<style>
  body{ color: #333; font-size: 14px }

  table{ width:100%;
    border-collapse: collapse;
    border-spacing: 0;
  }
  td{ padding:5px 10px;  font-size: 14px; }
  .linkBtn{ color: #0b94ea; cursor: pointer; }
  .linkBtn:hover{  text-decoration: underline  }
  .line{
    border-top:1px solid #ccc;
    border-bottom:1px solid #ccc;
    padding: 10px;
    margin:10px 0;
    border-bottom: 1px solid #ccc;
  }


  .sale_c{
    margin-left:20px;
    font-size: 13px;
    font-weight: 400;
    vertical-align: middle;
    padding: 4px 0;
    overflow: hidden;
    display: flex;
  }
  .sale_ttl1{
    margin-right: 10px;
    display: inline-block;
    min-width: 100px;
    text-align: right;
    line-height: 32px;
    vertical-align: top;
  }
  .sale_ttl2{
    display: inline-block;
    padding: 0 10px;
    line-height: 32px;
    min-width: 120px;
  }
  .sale_con2>div{
    background-color: #f1f1f1;
    border: 1px solid #e4e4e4;
    width: 155px;
    padding: 5px 35px;
    text-align: center;
  }
  .sale_con,.sale_con1,.sale_con2,.sale_con3,.sale_con4,.sale_seecon,.sale_seecon1{
    display: inline-block;
  }
  .sale_con1>input{
    width: 220px;
  }
  .sale_con2>input{
    width: 320px;
  }
  .sale_con4>input{
    width: 895px;
  }

</style>
</head>
<body>
<div class="bonceContainer bounce-blue" id="cus" style="width:600px; display: block; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span>开票资料</span>
    <a class="bounce_close" onclick="closeIframe()"></a>
  </div>
  <div class="bonceCon" >
    <div id="infoccc">
      <table id="now">
        <tr><td colspan="2" class="changeInfo"></td><td><span class="linkBtn" onclick="getHis()">修改记录</span></td></tr>
        <tr><td>名称：</td><td class="invoiceName" id="foo1"></td><td><span class="linkBtn copyFun" onclick="copyFun()" data-clipboard-target="#foo1">复制</span></td></tr>
        <tr><td>纳税人识别号：</td><td class="taxpayerID" id="foo2"></td><td><span class="linkBtn copyFun" onclick="copyFun()" data-clipboard-target="#foo2">复制</span></td></tr>
        <tr><td>地址、电话：</td><td class="invoiceAddress" id="foo3"></td><td><span class="linkBtn copyFun" onclick="copyFun()" data-clipboard-target="#foo3">复制</span></td></tr>
        <tr><td>开户行及账号：</td><td class="bankName" id="foo4"></td><td><span class="linkBtn copyFun" onclick="copyFun()" data-clipboard-target="#foo4">复制</span></td></tr>
      </table>
      <div class="line">
        <table id="front">
          <tr><td colspan="3">上一版的开票资料：</td></tr>
          <tr><td>名称：</td><td class="invoiceName"></td></tr>
          <tr><td>纳税人识别号：</td><td class="taxpayerID"></td></tr>
          <tr><td>地址、电话：</td><td class="invoiceAddress"></td></tr>
          <tr><td>开户行及账号：</td><td class="bankName"></td></tr>
        </table>
      </div>
      <div >
        请更新公司开票系统中的资料！
      </div>
    </div>
  </div>
  <div class="bonceFoot">
    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeIframe()">关闭</span>
  </div>
</div>

<div class="bonceContainer bounce-blue" id="updateRecords" style="display: none; width: 740px;margin-top: 0; margin-bottom: 0; ">
  <div class="bonceHead">
    <span class="recordTtl">开票信息修改记录</span>
    <a class="bounce_close" onclick="$('#cus').show().siblings().hide()"></a>
  </div>
  <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
    <div class="createRecord clear">
      <div class="clear">
        <p class="ty-left recordTip">当前资料尚未经修改。</p>
        <p class="ty-right recordEditer"></p>
      </div>
    </div>
    <table class="ty-table ty-table-control changeRecord">
      <thead>
      <tr>
        <td>记  录</td>
        <td>操  作</td>
        <td>创建者/修改者</td>
        <td class="recordFinance">财务的确认记录</td>
      </tr>
      </thead>
      <tbody>
      </tbody>
    </table>
  </div>
  <div class="bonceFoot">
    <input type="hidden" id="from">
    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="$('#cus').show().siblings().hide()">关闭</span>
  </div>
</div>

<%--发票修改记录查看--%>
<div class="bonceContainer bounce-blue" id="invoiceRecordsDetail" style="width: 800px; display:none; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span class="detaileTtl"></span>
    <a class="bounce_close" onclick="$('#updateRecords').show().siblings().hide()"></a>
  </div>
  <div class="bonceCon">
    <div class="sale_c">
      <div class="sale_ttl1">公司名称：</div>
      <div class="sale-con" id="inv_invoiceName"></div>
      <div class="sale_ttl1">地址：</div>
      <div class="sale-con" id="inv_invoiceAddress"></div>
      <div class="sale_ttl1">电话：</div>
      <div class="sale-con" id="inv_phone"></div>
    </div>
    <div class="sale_c">
      <div class="sale_ttl1">开户行：</div>
      <div class="sale-con" id="inv_bank"></div>
      <div class="sale_ttl1">账号：</div>
      <div class="sale-con" id="inv_bankNo"></div>
      <div class="sale_ttl1">税号：</div>
      <div class="sale-con" id="inv_taxpayerID"></div>
    </div>
  </div>
  <div class="bonceFoot">
    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="$('#updateRecords').show().siblings().hide()">关闭</span>
  </div>
</div>


<%@ include  file="../../common/footerScript.jsp"%>

<script>
  $(function(){
    // 实例化复制按钮
    new ClipboardJS('.copyFun');
    getInvoiceMessage()

  })

  function closeIframe(){
    console.log('closeIframe')
    $(window.parent.document).find("#somethingItemDetailsPage").hide()
  }
  function getHis(){
    let type = 'invoiceRecords'
    let customerId = getUrlParam("customerId");
    $.ajax({
      url: '../sales/getRecordInvoiceList.do',
      data: {
        'customerId': customerId
      },
      success: function (data) {
        var status = data.status;
        if (status === '1' || status === 1) {
          getRecordsList(data, type);
        } else {
          layer.msg("查看失败！");
        }
      }
    })
  }
  // creator: 李玉婷，2019-09-10 16:43:23，基本信息、发票信息修改记录列表数据获取
  function getRecordsList(data,type){
    var getList = data.list;
    if(getList.length > 0) {
      var str = '';
      var eidtNumber = getList.length - 1;
      $(".createRecord .recordTip").html('当前数据为第' + eidtNumber + '次修改后的结果。');
      $(".createRecord .recordEditer").html('修改时间：' + data.updateName + ' ' + new Date(data.updateDate).format('yyyy-MM-dd hh:mm:ss'));
      $(".changeRecord").show();
      type === 'invoiceRecords' ? $(".recordFinance").show():$(".recordFinance").hide();
      for (let r in getList) {
        var lastTd = '';
        if (r == '0') {
          if (type === 'invoiceRecords') {
            lastTd = ` <td>——</td>`;
          }
          str +=
                  '<tr>' +
                  '   <td>原始信息</td>' +
                  '   <td><span class="ty-color-blue" data-id="' + getList[r].id + '" data-frontid="0" data-type="'+ type +'" onclick="cus_recordDetail($(this))">查看</span></td>' +
                  '   <td>' + getList[r].createName + ' &nbsp; ' + new Date(getList[r].createDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                  lastTd +
                  '</tr>';
        } else {
          var front = Number(r) - 1;
          if (type === 'invoiceRecords') {
            lastTd = '<td>' + (getList[r].financerName || '') + ' &nbsp; ' + new Date(getList[r].financeTime).format('yyyy-MM-dd hh:mm:ss') + '</td>';
          }
          str +=
                  '<tr>' +
                  '   <td>第' + r + '次修改后</td>' +
                  '   <td><span class="ty-color-blue" data-id="' + getList[r].id + '" data-frontid="' + getList[front].id + '" data-type="'+ type +'" onclick="cus_recordDetail($(this))">查看</span></td>' +
                  '   <td>' + getList[r].updateName + ' &nbsp; ' + new Date(getList[r].updateDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' + lastTd +
                  '</tr>';
        }
      }
      $(".changeRecord tbody").html(str);
    }else {
      $(".createRecord .recordTip").html('当前资料未经修改');
      $(".createRecord .recordEditer").html('创建人：'+ data.createName + '&nbsp;&nbsp;' + new Date(data.createDate).format('yyyy-MM-dd hh:mm:ss'));
      $(".changeRecord").hide();
    }
    $("#updateRecords").show().siblings().hide();

  }

  /*修改记录前、后查看*/
  function cus_recordDetail(obj){
    $(".initValue").html("");
    var json ={
      'id': obj.data('id'),
      'frontId': obj.data('frontid')
    };
    $.ajax({
      url : "../sales/getRecordInvoiceDetails.do" ,
      data : json,
      success:function(data){
        var nowData = data['data']['now'];
        var frontData = data['data']['front'];
        if(frontData == null){
          $("#invoiceRecordsDetail .detaileTtl").html('原始信息');
          $("#inv_invoiceName").html(nowData.invoiceName);
          $("#inv_phone").html(nowData.telephone);
          $("#inv_invoiceAddress").html(nowData.invoiceAddress);
          $("#inv_bank").html(nowData.bankName);
          $("#inv_bankNo").html(nowData.bankNo);
          $("#inv_taxpayerID").html(nowData.taxpayerID);
        }else{
          $("#invoiceRecordsDetail .detaileTtl").html(obj.parent().prev().html());
          $("#inv_invoiceName").html(compareD(frontData.invoiceName,nowData.invoiceName));
          $("#inv_phone").html(compareD(frontData.telephone,nowData.telephone));
          $("#inv_invoiceAddress").html(compareD(frontData.invoiceAddress,nowData.invoiceAddress));
          $("#inv_bank").html(compareD(frontData.bankName,nowData.bankName));
          $("#inv_bankNo").html(compareD(frontData.bankNo,nowData.bankNo));
          $("#inv_taxpayerID").html(compareD(frontData.taxpayerID,nowData.taxpayerID));
        }
        $("#invoiceRecordsDetail").show().siblings().hide();
      },
      error: function (msg) {
        layer.msg("连接错误，请稍后重试！");
        return false;
      }
    });

  }

  //creator:lyt date:2018/11/28 比较修改记录修改前后不同
  function compareD(front,now){
    if(front == now){
      return '<span>' + (now || "") +' </span>'
    }else{
      return '<span class="redFlag">' + (now || "") + '</span>'
    }
  }
  function copyFun(){
    layer.msg('已复制到剪切板')
  }
  // creator : hxz 2023-11-25  查看开票资料
  function getInvoiceMessage() {
    let frontId = getUrlParam("frontId");
    let id = getUrlParam("id");
    $.ajax({
      "url":"../sales/getRecordInvoiceDetails.do",
      "data":{ 'id': id,  "frontId": frontId},
      success:function (res) {
        console.log('getRecordInvoiceDetails = ', res)
        let data = res.data
        if(data){
          let now = data.now
          let front = data.front
          $("#now .invoiceName").html(now.invoiceName);
          $("#now .taxpayerID").html(now.taxpayerID);
          $("#now .invoiceAddress").html(now.invoiceAddress + ' ' + now.telephone );
          $("#now .bankName").html(now.bankName + ' ' + now.bankNo );

          $("#front .invoiceName").html(front.invoiceName);
          $("#front .taxpayerID").html(front.taxpayerID);
          $("#front .invoiceAddress").html(front.invoiceAddress + ' ' + front.telephone );
          $("#front .bankName").html(front.bankName + ' ' + front.bankNo );
          let createDate = new Date(now.createDate).format('yyyy-MM-dd hh:mm:ss')
          $("#now .changeInfo").html('该客户的开票资料已被' + (now.createName ) + '于' + createDate +'更新，具体如下：'   );

        }else{
          layer.msg("未获取开票信息")
        }
      }
    })
  }

</script>
</body>
</html>
