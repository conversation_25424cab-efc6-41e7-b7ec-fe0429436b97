<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/serviceProject.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .time{ cursor: pointer;  }
    #addGoodsInit .dotItem .changeDot{ width: 192px; }
    #editNumGoods span.ttl{
        width: 139px;
        display: inline-block;
        margin-left: 50px;
    }
    .rtCon span{ float: right; }
    .bonceCon input.inputNum{
        width: 60px;
    }
    .ty-radio-group.column .ty-radio{
        display: block;
        margin-top: 4px;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed3">
    <%-- 商品 查看 --%>
    <div class="bonceContainer bounce-blue" id="scanGoods">
        <div class="bonceHead">
            <span>查看商品</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center; ">
                <p id="scanGoodsTip"></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div id="operatInstructions" class="bonceContainer bounce-blue" style="width: 538px;">
        <div class="bonceHead">
            <span>操作说明</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="operatInstructionsStr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel(); ">关闭</span>
        </div>
    </div>

    <div id="deliveryTimeSet" class="bonceContainer bounce-blue" >
        <div class="bonceHead">
            <span>设置对发货时间的要求</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center">
                <select class="timeBetween">
                    <option value="">请选择</option>
                    <option value="1">合同签订后</option>
                    <option value="2">服务开始前</option>
                    <option value="3">服务开始后</option>
                    <option value="13">服务结束后</option>
                </select>
                <input type="text" class="days" onkeyup="clearNum(this)" style="width: 60px;"/>日内
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="setTrailItemQuantity" style="width: 1000px">
        <div class="bonceHead">
            <span>设置试用流程中的项目数量</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>项目代号/名称</td>
                        <td>计量单位</td>
                        <td>参考单价</td>
                        <td>每个套餐中 <br>该项目几个周期</td>
                        <td>试用流程中 <br>该项目给几个周期</td>
                        <td>每个套餐中 <br>有几个该项目</td>
                        <td>试用流程中 <br>给几个该项目</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel(); ">关闭</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="setTrailDeliveryQuantity" style="width: 1000px">
        <div class="bonceHead">
            <span>设置试用流程中的商品数量</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>商品代号/名称</td>
                        <td>计量单位</td>
                        <td>参考单价</td>
                        <td>发货时间的要求</td>
                        <td>套餐中该商品的数量</td>
                        <td>试用流程中该商品的数量</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel(); ">关闭</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="invoiceInfoLog1" style="width: 700px">
        <div class="bonceHead">
            <span>开票资料“货物或应税劳务、服务名称”的编辑记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p class="tips text-right"></p>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>资料状态</td>
                        <td>编辑后的数据</td>
                        <td>操作者</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel(); ">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="invoiceInfoLog2" style="width: 700px">
        <div class="bonceHead">
            <span>开票资料“规格型号”的编辑记录</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p class="tips text-right"></p>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>资料状态</td>
                        <td>编辑后的数据</td>
                        <td>操作者</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel(); ">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="seeProject" style="width: 700px">
        <div class="bonceHead">
            <span>查看套餐</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p>套餐所包含的项目</p>
                <p class="tips">项目</p>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>服务项目代号/名称</td>
                        <td>参考单价</td>
                        <td>数量</td>
                        <td>原价金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                    <tr>
                        <td colspan="2">原价合计</td>
                        <td colspan="2" class="sumAll">00.00</td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel(); ">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="seeGood" style="width: 700px">
        <div class="bonceHead">
            <span>查看套餐</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p>套餐所包含的商品</p>
                <p class="tips"></p>
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>商品项目代号/名称</td>
                        <td>参考单价</td>
                        <td>发货事宜</td>
                        <td>数量</td>
                        <td>原价金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                    <tr>
                        <td colspan="2">原价合计</td>
                        <td colspan="3" class="sumAll">00.00</td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel(); ">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="seeServiceProject" style="width: 800px;">
        <div class="bonceHead">
            <span>查看套餐</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="text-center"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="changeStateTip" style="width: 800px;">
        <div class="bonceHead">
            <span>改变状态</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="text-center">确定后，这项试用流程/体验环节将不再出现在服务项目选项中！</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <%-- 修改记录详情--%>
    <div class="bonceContainer bounce-blue" id="logDetails" style="width: 800px;">
        <div class="bonceHead">
            <span>查看套餐</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>
                    <span id="logDetails_durInfo">本套餐服务费按周期收取。</span>
                </p>
                <div style="text-align: right" id="logDetails_create">
                </div>
                <div>
                </div>
                <table class="kj-table">
                    <tr>
                        <td>代号</td>
                        <td colspan="3">名称</td>
                        <td colspan="2">说明</td>
                    </tr>
                    <tr>
                        <td id="log_code">代号</td>
                        <td colspan="3" id="log_name"></td>
                        <td colspan="2" id="log_desc"></td>
                    </tr>
                    <tr>
                        <td colspan="3">开增值税专用发票时</td>
                        <td rowspan="2">开普票时的开票单价</td>
                        <td rowspan="2">不开发票时的单价</td>
                        <td rowspan="2">参考单价</td>
                    </tr>
                    <tr>
                        <td>税率</td>
                        <td>不含税单价</td>
                        <td>含税单价</td>
                    </tr>
                    <tr>
                        <td id="logDetails_tax"></td>
                        <td id="logDetails_priceNoTax"></td>
                        <td id="logDetails_priceContain"></td>
                        <td id="logDetails_pricePu"></td>
                        <td id="logDetails_priceNoPiao"></td>
                        <td id="logDetails_peiceCanKao"></td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td colspan="5" id="logDetails_desc"></td>
                    </tr>

                </table>
                <hr>
                <p>套餐所包含的内容</p>
                <div class="s_tbc_1">
                    <p>项目</p>
                    <table class="kj-table" id="log_tab1">
                        <tr>
                            <td>服务项目代号/名称</td>
                            <td>参考单价</td>
                            <td>数量</td>
                            <td>原价金额</td>
                        </tr>
                        <tr class="guDing">
                            <td colspan="3">原价合计</td>
                            <td id="logTab1Sum" class="sumAll"></td>
                        </tr>
                    </table>
                </div>
                <div class="s_tbc_2">
                    <p>仅第一个收费周期需提供的商品</p>
                    <table class="kj-table" id="log_tab2">
                        <tr>
                            <td>商品项目代号/名称</td>
                            <td>参考单价</td>
                            <td>发货事宜</td>
                            <td>数量</td>
                            <td>原价金额</td>
                        </tr>
                        <tr>
                            <td>商品项目代号/名称</td>
                            <td>参考单价</td>
                            <td>发货事宜</td>
                            <td>数量</td>
                            <td>原价金额</td>
                        </tr>
                        <tr class="guDing">
                            <td colspan="4">原价合计</td>
                            <td id="logTab2Sum" class="sumAll"></td>
                        </tr>
                    </table>
                </div>
                <div class="s_tbc_3">
                    <p>每个收费周期都需提供的商品</p>
                    <table class="kj-table" id="log_tab3">
                        <tr>
                            <td>商品项目代号/名称</td>
                            <td>参考单价</td>
                            <td>发货事宜</td>
                            <td>数量</td>
                            <td>原价金额</td>
                        </tr>
                        <tr>
                            <td>商品项目代号/名称</td>
                            <td>参考单价</td>
                            <td>发货事宜</td>
                            <td>数量</td>
                            <td>原价金额</td>
                        </tr>
                        <tr class="guDing">
                            <td colspan="4">原价合计</td>
                            <td id="logTab3Sum" class="sumAll"></td>
                        </tr>
                    </table>
                </div>

                <hr>
                <div class="rtCon">
                    <p id="log_feeZhouC">
                        收费周期 <span id="log_feeZhou">每XXXX收取一次</span>
                    </p>
                    <p>
                        收费时间<span id="log_feeTime"></span>
                    </p>

                </div>

            </div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>


    <%--向套餐内添加商品 修改数量 --%>
    <div class="bonceContainer bounce-blue" id="editNumGoods">
        <div class="bonceHead">
            <span>修改数量</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="item-flex periodPart">
                    <div class="item-title item180">当前数量</div>
                    <div class="item-content">
                        <input class="inputNum oldDeliveryQuantity" type="text" disabled>
                    </div>
                </div>
                <div class="item-flex" style="margin-top: 8px">
                    <div class="item-title item180">修改为</div>
                    <div class="item-content">
                        <input class="inputNum deliveryQuantity" type="text" onkeyup="clearNum(this)"/>
                    </div>
                </div>
                <div class="deliverySetPart" style="margin-top: 16px">
                    <div class="item-flex periodPart">
                        <div class="item-title item180">当前发货时间的要求</div>
                        <div class="item-content">
                            <input class="inputText oldDeliveryTimeSet" type="text" disabled>
                        </div>
                    </div>
                    <div class="item-flex" style="margin-top: 8px">
                        <div class="item-title item180">修改为</div>
                        <div class="item-content deliverySet">
                            <input class="inputText deliveryTimeSet" type="text" readonly placeholder="请选择" onclick="deliveryTimeSet($(this))">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>



    <%-- 删除项目 或 商品 --%>
    <div class="bonceContainer bounce-blue" id="delProTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center; ">
                确定要删除？
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>

    <%--向套餐内添加项目 修改数量 --%>
    <div class="bonceContainer bounce-blue" id="editNumPro">
        <div class="bonceHead">
            <span>修改数量</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="item-flex periodPart">
                    <div class="item-title item120">每个套餐中该项目</div>
                    <div class="item-content">
                        <input class="inputNum periodQuantity" type="text" onkeyup="clearNum(this)"/> 个周期
                    </div>
                </div>
                <div class="periodTip"><small class="ty-color-blue">注：各项目此数据需相同，故修改后其他项目将跟随变化！</small></div>
                <div class="item-flex" style="margin-top: 16px">
                    <div class="item-title item120">每个套餐中有</div>
                    <div class="item-content">
                        <input class="inputNum itemQuantity" type="text" onkeyup="clearNum(this)"/> 个该项目
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <%--向套餐内添加项目--%>
    <div class="bonceContainer bounce-blue" id="addProject" style="width: 720px;">
        <div class="bonceHead">
            <span>向套餐内添加项目</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <div class="ty-alert">
                    请选择组成套餐的项目
                    <div class="btn-group">
                        <span><input class="noLimit" type="checkbox" onclick="noLimit()"><span style="margin-left: 4px">无限制选择项目</span></span>
                        <span data-fun="operatInstructions" class="link-blue funBtn">操作说明</span>
                    </div>
                </div>
                <div class="ty-alert">如直接选择某项目，则系统对之后所选项目有限制。如需要不受限地选择，请点击“无限制选择项目”。</div>
                <table class="kj-table tbl_addProject">
                    <thead>
                    <tr>
                        <td>选择</td>
                        <td>项目代号/名称</td>
                        <td>计量单位</td>
                        <td>参考单价</td>
                        <td>每个套餐中<br>该项目几个周期</td>
                        <td>每个套餐中<br>有几个该项目</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="addProjectSure()">确定</span>
        </div>
    </div>
    <%--向套餐内添加商品--%>
    <div class="bonceContainer bounce-blue" id="addGoodsInit" style="width: 520px;">
        <div class="bonceHead">
            <span>向套餐内添加商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="narrowBody">
                    <div class="part_isPeriod">
                        <div class="question">所添加的商品是否每个收费周期都需提供？</div>
                        <div class="clear dotItem">
                            <div class="ty-radio">
                                <input type="radio" name="deliveryTerm" value="1" id="deliveryTerm1">
                                <label for="deliveryTerm1"></label> 每个收费周期都需提供
                            </div>
                            <div class="ty-radio">
                                <input type="radio" name="deliveryTerm" value="2" id="deliveryTerm2">
                                <label for="deliveryTerm2"></label> 仅第一个收费周期提供
                            </div>
                        </div>
                    </div>
                    <div class="question">所添加的商品是存放于本机构，还是需通过某种方式交到客户手中？</div>
                    <div class="clear dotItem">
                        <div class="ty-radio">
                            <input type="radio" name="deliveryPlace" value="1" id="deliveryPlace1">
                            <label for="deliveryPlace1"></label> 存放于本机构
                        </div>
                        <div class="ty-radio">
                            <input type="radio" name="deliveryPlace" value="2" id="deliveryPlace2">
                            <label for="deliveryPlace2"></label> 需交到客户手中
                        </div>
                    </div>
                    <div class="kj-hr"></div>
                    <div class="question">是否需要客户先付套餐的款，之后才向其提供商品？</div>
                    <div class="clear dotItem">
                        <div class="ty-radio">
                            <input type="radio" name="deliverySequence" value="1" id="deliverySequence1" onclick="showNext($(this))">
                            <label for="deliverySequence1"></label> 不需要
                        </div>
                        <div class="ty-radio">
                            <input type="radio" name="deliverySequence" value="2" id="deliverySequence2" onclick="showNext($(this))">
                            <label for="deliverySequence2"></label> 需要
                        </div>
                    </div>
                    <div class="deliverySequence2">
                        <div class="question">
                            <div>客户付款后，商品提供给客户需要 <input type="text" style="width: 60px;" id="deliverySequenceNum" onkeyup="clearNoNum(this)"/> 天</div>
                            <small class="ty-color-blue">注：商品的准备时间需考虑进去</small>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="addGoodsInitSure()">确定</span>
        </div>
    </div>
    <%--向套餐内添加商品2 --%>
    <div class="bonceContainer bounce-blue" id="addGoods" style="width: 850px;">
        <div class="bonceHead">
            <span>向套餐内添加商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>请选择套餐内应包含的商品，选择后再输入所需的数量。</p>
                <table class="kj-table tbl_addGood">
                    <thead>
                    <tr>
                        <td>选择</td>
                        <td>商品代号/名称</td>
                        <td>计量单位</td>
                        <td>参考单价</td>
                        <td>发货时间的要求</td>
                        <td>数量</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="addGoodsSure()">确定</span>
        </div>
    </div>
    <%--设置体验环节/试用流程--%>
    <div class="bonceContainer bounce-blue" id="setExperienceOrTrail" style="width: 850px;">
        <div class="bonceHead">
            <span>设置体验环节/试用流程</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <p>本项目需设置体验环节还是试用流程？</p>
                <div class="ty-radio">
                    <input type="radio" name="setExperienceOrTrail" id="radio_setExperience" value="2">
                    <label for="radio_setExperience"></label> 体验环节
                </div>
                <div class="ty-radio">
                    <input type="radio" name="setExperienceOrTrail" id="radio_setTrail" value="3">
                    <label for="radio_setTrail"></label> 试用流程
                </div>
                <section class="trail">
                    <div class="kj-hr"></div>
                    <p>以下为套餐所包含的内容，请设置<span class="typeName"></span>流程应包含哪些内容</p>
                    <section class="menu_content menu1">
                        <p>项目 <span class="link-blue ty-right" onclick="setTrailNum(1)">设置</span></p>
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>服务项目代号/名称</td>
                                <td>参考单价</td>
                                <td>数量</td>
                                <td>原价金额</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                            <tfoot>
                            <tr>
                                <td colspan="2">原价合计</td>
                                <td colspan="2" class="sumAll">00.00</td>
                            </tr>
                            </tfoot>
                        </table>
                    </section>
                    <section class="menu_content menu2">
                        <p>仅第一个收费周期需提供的商品 <span class="link-blue ty-right" onclick="setTrailNum(2)">设置</span></p>
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>商品代号/名称</td>
                                <td>参考单价</td>
                                <td>发货事宜</td>
                                <td>数量</td>
                                <td>原价金额</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                            <tfoot>
                            <tr>
                                <td colspan="3">原价合计</td>
                                <td colspan="2" class="sumAll">00.00</td>
                            </tr>
                            </tfoot>
                        </table>
                    </section>
                    <section class="menu_content menu3">
                        <p>每个收费周期都需提供的商品 <span class="link-blue ty-right" onclick="setTrailNum(3)">设置</span></p>
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>商品代号/名称</td>
                                <td>参考单价</td>
                                <td>发货事宜</td>
                                <td>数量</td>
                                <td>原价金额</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                            <tfoot>
                            <tr>
                                <td colspan="3">原价合计</td>
                                <td colspan="2" class="sumAll">00.00</td>
                            </tr>
                            </tfoot>
                        </table>
                    </section>
                </section>
                <section class="experience">
                    <div class="kj-hr"></div>
                    <div class="item-flex trialUpper">
                        <div class="item-title item180 noPeriodStr">某客户可<span class="typeName"></span>的数量上限 <span class="ty-color-red">*</span></div>
                        <div class="item-content flex-end">
                            <input type="text" name="trialUpper" require onkeyup="clearNum(this)">
                        </div>
                    </div>
                    <div class="item-flex trialDays">
                        <div class="item-title item180">客户可<span class="typeName"></span>的天数 <span class="ty-color-red">*</span></div>
                        <div class="item-content flex-end">
                            <input type="text" name="trialDays" require onkeyup="clearNum(this)"> 天
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="item-title item180"><span class="typeName"></span>是否收费？ <span class="ty-color-red">*</span></div>
                        <div class="item-content">
                            <div class="ty-radio">
                                <input type="radio" name="isChargeable" id="edit_isStable1" value="1">
                                <label for="edit_isStable1"></label> <span class="radioLabel">收费</span>
                            </div>

                            <div class="ty-radio">
                                <input type="radio" name="isChargeable" id="edit_isStable2" value="2">
                                <label for="edit_isStable2"></label> <span class="radioLabel">免费</span>
                            </div>
                        </div>
                    </div>
                    <div class="part_fee">
                        <div class="priceTp">
                            <div><span class="typeName"></span>价格 <span class="ty-color-red">*</span></div>
                            <small class="ty-color-blue">注：下表内各种单价中，不涉及的可以不录，但至少需录入一种。</small>
                        </div>
                        <table class="kj-table priceForm">
                            <thead>
                            <tr>
                                <td width="40%" colspan="3">开增值税专用发票时</td>
                                <td width="20%" rowspan="2">开普票时的开票单价</td>
                                <td width="20%" rowspan="2">不开发票时的单价</td>
                                <td width="20%" rowspan="2">参考单价</td>
                            </tr>
                            <tr>
                                <td>税率</td>
                                <td>不含税单价</td>
                                <td>含税单价</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    <select name="taxRate" onchange="chargeRate($(this))">
                                    </select>
                                </td>
                                <td><input need name="unitPrice" type="text" onkeyup="clearNoNumN(this, 8)" onblur="setPrice($(this), 1)"/></td>
                                <td><input need name="unitPriceNotax" type="text" onkeyup="clearNoNumN(this, 8)" onblur="setPrice($(this), 2)"/></td>
                                <td><input need name="unitPriceInvoice" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                                <td><input need name="unitPriceNoinvoice" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                                <td><input need name="unitPriceReference" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                            </tr>
                            <tr>
                                <td>价格说明</td>
                                <td colspan="6"><input name="priceDesc" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="origin">
                            <span class="gapRt">收费时间<i class="xing"></i></span>
                            <select class="entry chargeStage" name="chargeStage" style="margin-left: 17px;">
                                <option value="">请选择</option>
                                <option value="1">合同签订后</option>
                                <option value="2">服务开始前</option>
                                <option value="3">服务开始后</option>
                                <option value="13">服务结束后</option>
                            </select>
                            <input need class="chargeLimit" name="chargeLimit" type="text" placeholder="请填写" onkeyup="clearNum(this)"/>天内
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureSetExperienceOrTrail()">确定</span>
        </div>
    </div>
    <%--编辑服务套餐的开票资料 --%>
    <div class="bonceContainer bounce-blue" id="editInvoiceInfo1" style="width: 750px;">
        <div class="bonceHead">
            <span>编辑服务套餐的开票资料</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="tipEdit">
                    目前，本服务套餐开票资料“货物或应税劳务、服务名称”尚无数据，需采用哪项数据？<br>
                    <small class="ty-color-blue">注1 财务人员对本服务套餐开发票时，需使用到此数据，建议进行设置！</small><br>
                    <small class="ty-color-blue">注2 此数据设置前，往往需先与客户沟通，并达成一致。</small>
                </div>
                <div class="tipDone">
                    目前，本服务套餐开票资料“货物或应税劳务、服务名称”的数据为：<br>
                    <span class="content"></span>
                    <div class="kj-hr"></div>
                    需将此数据修改为哪一项？请选择。<br>
                    <small class="ty-color-blue">注1 此数据修改前，往往需先与客户沟通，并达成一致。</small>
                </div>
                <div class="ty-radio-group column">
                    <div class="ty-radio">
                        <input type="radio" name="useDataType1" id="useDataType1" value="1">
                        <label for="useDataType1"></label> 采用服务套餐代号的当前数据
                    </div>
                    <div class="ty-radio">
                        <input type="radio" name="useDataType1" id="useDataType2" value="2">
                        <label for="useDataType2"></label> 采用服务套餐名称的当前数据
                    </div>
                    <div class="ty-radio">
                        <input type="radio" name="useDataType1" id="useDataType3" value="3">
                        <label for="useDataType3"></label> 上述代号与名称都使用，代号在前
                    </div>
                    <div class="ty-radio">
                        <input type="radio" name="useDataType1" id="useDataType4" value="4">
                        <label for="useDataType4"></label> 上述代号与名称都使用，名称在前
                    </div>
                    <div class="ty-radio">
                        <input type="radio" class="custom" name="useDataType1" id="useDataType5" value="5">
                        <label for="useDataType5"></label> 自定义
                    </div>
                </div>
                <input type="text" name="userDefined" disabled style="margin-left: 20px; margin-top: 8px; width: 300px">
                <div class="kj-hr"></div>
                <p>当前，本服务套餐代号与名称数据如下：</p>
                <p>服务套餐代号：<span class="code"></span><span class="link-blue ty-right funBtn" data-fun="copyTextToClipboard">复制</span></p>
                <p>服务套餐名称：<span class="name"></span><span class="link-blue ty-right funBtn" data-fun="copyTextToClipboard">复制</span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditInvoiceInfo(1)">确定</span>
        </div>
    </div>
    <%--编辑服务套餐的开票资料 --%>
    <div class="bonceContainer bounce-blue" id="editInvoiceInfo2" style="width: 750px;">
        <div class="bonceHead">
            <span>编辑服务套餐的开票资料</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="tipEdit">
                    目前，本服务套餐开票资料“规格型号”尚无数据，需采用哪项数据？<br>
                    <small class="ty-color-blue">注1 财务人员对本服务套餐开发票时，需使用到此数据，建议进行设置！</small><br>
                    <small class="ty-color-blue">注2 此数据设置前，往往需先与客户沟通，并达成一致。</small>
                </div>
                <div class="tipDone">
                    目前，本服务套餐开票资料“规格型号”的数据为：<br>
                    <span class="content"></span>
                    <div class="kj-hr"></div>
                    需将此数据修改为哪一项？请选择。<br>
                    <small class="ty-color-blue">注1 此数据修改前，往往需先与客户沟通，并达成一致。</small>
                </div>
                <div class="ty-radio-group column">
                    <div class="ty-radio">
                        <input type="radio" name="useDataType2" id="useDataType6" value="1">
                        <label for="useDataType6"></label> 发票上的“规格型号”为空，无需填写
                    </div>
                    <div class="ty-radio">
                        <input type="radio" name="useDataType2" class="custom" id="useDataTyp7" value="5">
                        <label for="useDataTyp7"></label> 自定义
                    </div>
                </div>
                <input type="text" name="userDefined" disabled style="margin-left: 20px; margin-top: 8px; width: 300px">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditInvoiceInfo(2)">确定</span>
        </div>
    </div>
    <%--状态改变记录 --%>
    <div class="bonceContainer bounce-blue" id="changeStateLog" style="width: 750px;">
        <div class="bonceHead">
            <span>状态改变记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <table class="kj-table">
                    <thead>
                    <tr>
                        <td>操作前状态</td>
                        <td>改变状态的操作者</td>
                        <td>设置结果</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="addGoodsSure()">确定</span>
        </div>
    </div>
    <%-- 查看套餐 基本信息 修改记录  --%>
    <div class="bonceContainer bounce-blue" id="editLog" style="width: 600px;">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p id="logResult">当前资料为第n次修改后的结果，修改人：XXX XXXX-XX-XX XX：XX：XX</p>
                <table class="kj-table" id="logTab">
                    <thead>
                    <tr>
                        <td>资料状态</td>
                        <td>操作</td>
                        <td>创建人/修改人</td></tr>
                    <tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<div class="bounce">
    <%--新增 套餐 1--%>
    <div class="bonceContainer bounce-blue" id="serviceInitChoose">
        <div class="bonceHead">
            <span>新增套餐</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <p>本项目是否按周期（如按年、按月）收费？</p>
                <div class="clear dotItem">
                    <div class="changeDot">
                        <input type="radio" name="isPeriodical" value="1"> 是
                    </div>
                    <div class="changeDot">
                        <input type="radio" name="isPeriodical" value="0"> 否
                    </div>
                </div>
            </div>
            <div class="narrowBody">
                <p>套餐内须含项目。除项目外，该套餐是否包含商品？</p>
                <div class="clear dotItem">
                    <div class="changeDot">
                        <input type="radio" name="isMixture" value="1"> 是
                    </div>
                    <div class="changeDot">
                        <input type="radio" name="isMixture" value="0"> 否
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 funBtn" data-fun="serviceInitChoose">确定</span>
        </div>
    </div>
    <%--新增 套餐2--%>
    <div class="bonceContainer bounce-blue" id="addService" style="width:840px;">
        <div class="bonceHead">
            <span>新增套餐</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <section class="service_main">
                    <div class="ty-alert headTip">请录入按周期收费且包含商品的套餐！</div>
                    <div class="请定义本套餐">
                        <div>请定义本套餐</div>
                        <small class="ty-color-blue">注：此处录入的数据可供不同的服务合同重复引用。</small>
                    </div>
                    <table class="kj-table byCycle">
                        <thead>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="40%">说明</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td><input need name="code" type="text" require onblur="checkCode($(this))"/><div class="errorTip"><small class="ty-color-red">这个代号已被使用，请换一个！</small></div></td>
                            <td><input need name="name" type="text" placeholder="此处最多可录入15字" maxlength="15" require onkeyup="limitWord($(this), 15)"/></td>
                            <td><input need name="memo" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this), 100)"/></td>
                        </tr>
                        </tbody>
                    </table>
                </section>
                <section class="service_content">
                    <div class="kj-hr"></div>
                    <div class="ty-alert goods">
                        <span>套餐所包含的内容</span>
                        <div class="btn-group">
                            <span class="link-blue" data-where="new" onclick="addProject($(this))">添加项目</span>
                            <span class="link-blue addGoodsBtn" data-where="new" onclick="addGoodsInit($(this))">添加商品</span>
                        </div>
                    </div>
                    <div class="projectOrGoods projectContent">
                        <div>项目</div>
                        <div>
                            <table class="kj-table serviceTab">
                                <thead>
                                <tr>
                                    <td>服务项目代号/名称</td>
                                    <td>参考单价</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>

                                </tbody>
                                <tfoot>
                                <tr class="guDing">
                                    <td colspan="3">原价合计</td>
                                    <td colspan="2" class="sumAll"></td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <div class="projectOrGoods good2Content">
                        <div style="margin:15px 0 10px;">仅第一个收费周期需提供的商品</div>
                        <div>
                            <table class="kj-table goods2Tab">
                                <thead>
                                <tr>
                                    <td>商品代号名称</td>
                                    <td>参考单价</td>
                                    <td>发货事宜</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                                <tfoot>
                                <tr class="guDing">
                                    <td colspan="4">原价合计</td>
                                    <td colspan="2" class="sumAll"></td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <div class="projectOrGoods good1Content">
                        <div style="margin:15px 0 10px;">每个收费周期都需提供的商品</div>
                        <div>
                            <table class="kj-table goods1Tab">
                                <thead>
                                <tr>
                                    <td>商品代号名称</td>
                                    <td>参考单价</td>
                                    <td>发货事宜</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                                <tfoot>
                                <tr class="guDing">
                                    <td colspan="4">原价合计</td>
                                    <td colspan="2" class="sumAll"></td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </section>
                <section class="service_other">
                    <div class="kj-hr"></div>
                    <div class="priceTp">
                        <div>价格<i class="xing"></i></div>
                        <small class="ty-color-blue">注：下表内各种单价中，不涉及的可以不录，但至少需录入一种。</small>
                    </div>
                    <table class="kj-table priceForm">
                        <thead>
                        <tr>
                            <td width="40%" colspan="3">开增值税专用发票时</td>
                            <td width="20%" rowspan="2">开普票时的开票单价</td>
                            <td width="20%" rowspan="2">不开发票时的单价</td>
                            <td width="20%" rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>
                                <select name="taxRate" onchange="chargeRate($(this))"></select>
                            </td>
                            <td><input need name="unitPrice" type="text" onkeyup="clearNoNumN(this, 8)" onblur="setPrice($(this), 1)"/></td>
                            <td><input need name="unitPriceNotax" type="text" onkeyup="clearNoNumN(this, 8)" onblur="setPrice($(this), 2)"/></td>
                            <td><input need name="unitPriceInvoice" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                            <td><input need name="unitPriceNoinvoice" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                            <td><input need name="unitPriceReference" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="6"><input name="priceDesc" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="byCycle gapTp">
                        <div class="origin periodDurationInfo">
                            <span class="gapRt">收费周期<i class="xing"></i></span>
                            每
                            <select class="entry periodDuration" name="periodDuration" >
                                <option value=""></option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                            </select>
                            <select class="entry periodUnit" name="periodUnit" onchange="chargeUnit($(this))">
                                <option value="">请选择</option>
                                <option value="7">年</option>
                                <option value="4">个月</option>
                            </select>
                            收费一次
                        </div>
                        <div class="origin">
                            <span class="gapRt">收费时间<i class="xing"></i></span>
                            <select class="entry chargeStage" name="chargeStage" style="margin-left: 17px;">
                                <option value="">请选择</option>
                                <option value="1">合同签订后</option>
                                <option value="2">服务开始前</option>
                                <option value="3">服务开始后</option>
                                <option value="13">服务结束后</option>
                            </select>
                            <input class="chargeLimit" need name="chargeLimit" type="text" placeholder="请填写" onkeyup="clearNum(this)"/>天内
                        </div>
                    </div>
                </section>
                <section class="service_trail">
                    <div class="kj-hr"></div>
                    <p>
                        <span>本套餐如有体验环节或试用流程，可通过“改变状态”来设置，如无，则无需操作。</span>
                        <span class="link-blue ty-right" onclick="changeService()">修改</span>
                    </p>
                    <div class="trailContent"></div>
                </section>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 funBtn" data-fun="addServiceSure">录入完毕</span>
        </div>
    </div>
    <%--  查看 套餐  --%>
    <div class="bonceContainer bounce-blue" id="serviceScan" style="width: 900px;">
        <div class="bonceHead">
            <span>查看套餐</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <span class="hd"></span>
                <section>
                    <div class="ty-alert">
                        <div class="item-title">基本信息</div>
                        <div class="btn-group">
                            <span class="link-blue funBtn" data-fun="editBtn">修改</span>
                            <span class="link-blue funBtn" data-fun="editLog">修改记录</span>
                        </div>
                    </div>
                    <table class="kj-table">
                        <tbody>
                        <tr>
                            <td style="width: 220px">代号</td>
                            <td><span class="service_code"></span></td>
                        </tr>
                        <tr>
                            <td>名称</td>
                            <td><span class="service_name"></span></td>
                        </tr>
                        </tbody>
                    </table>
                </section>
                <section>
                    <div class="ty-alert">
                        <div class="item-title">价格信息</div>
                    </div>
                    <table class="kj-table">
                        <tbody>
                        <tr>
                            <td style="width: 220px">开增值税专用发票时</td>
                            <td><span class="service_VATInvoice"></span></td>
                        </tr>
                        <tr>
                            <td>开普通发票时</td>
                            <td><span class="service_unitPriceInvoice"></span></td>
                        </tr>
                        <tr>
                            <td>不开发票时</td>
                            <td><span class="service_unitPriceNoinvoice"></span></td>
                        </tr>
                        <tr>
                            <td>参考单价</td>
                            <td><span class="service_unitPriceReference"></span></td>
                        </tr>
                        </tbody>
                    </table>
                </section>
                <section>
                    <div class="ty-alert">
                        <div class="item-title">收费模式及说明等</div>
                    </div>
                    <table class="kj-table">
                        <tbody>
                        <tr>
                            <td style="width: 220px">概述</td>
                            <td><span class="service_overview"></span></td>
                        </tr>
                        <tr>
                            <td>收费周期</td>
                            <td><span class="service_feeCycle"></span></td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td><span class="service_feeTime"></span></td>
                        </tr>
                        <tr>
                            <td>套餐说明</td>
                            <td><span class="service_memo"></span></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td><span class="service_priceDesc"></span></td>
                        </tr>
                        </tbody>
                    </table>
                </section>
                <section>
                    <div class="ty-alert">
                        <div class="item-title">套餐所包含的内容</div>
                    </div>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td style="width: 220px"></td>
                            <td>正式服务下的数量</td>
                            <td>体验环节/试用流程下的数量</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>项目</td>
                            <td><span class="service_projectNum"></span><span class="link-blue ty-right funBtn" type="main" data-fun="seeGoodOrProject">查看</span></td>
                            <td><span class="service_projectNum_trial"></span><span class="link-blue ty-right funBtn" type="trail" data-fun="seeGoodOrProject">查看</span></td>
                        </tr>
                        <tr>
                            <td>仅第一个收费周期需提供的商品</td>
                            <td><span class="service_firstGoodNum"></span><span class="link-blue ty-right funBtn" good="1" type="main" data-fun="seeGoodOrProject">查看</span></td>
                            <td><span class="service_firstGoodNum_trial"></span><span class="link-blue ty-right funBtn" good="1" type="trail" data-fun="seeGoodOrProject">查看</span></td>
                        </tr>          <tr>
                            <td>每个收费周期都需提供的商品</td>
                            <td><span class="service_everyGoodNum"></span><span class="link-blue ty-right funBtn" good="2" type="main" data-fun="seeGoodOrProject">查看</span></td>
                            <td><span class="service_everyGoodNum_trial"></span><span class="link-blue ty-right funBtn" good="2" type="trail" data-fun="seeGoodOrProject">查看</span></td>
                        </tr>
                    </table>
                </section>
                <section class="part_sy">
                    <div class="ty-alert">
                        <div class="item-title">体验环节/试用流程</div>
                    </div>
                    <table class="kj-table">
                        <tbody></tbody>
                    </table>
                </section>
                <section>
                    <div class="ty-alert">
                        <div class="item-title">服务套餐的两项开票资料</div>
                    </div>
                    <table class="kj-table">
                        <tbody>
                        <tr>
                            <td style="width: 220px">货物或应税劳务、服务名称</td>
                            <td>
                                <span class="service_nameInvoice"></span>
                                <div class="btn-group ty-right">
                                    <span class="link-blue funBtn" type="1" data-fun="editInvoiceInfo">编辑</span>
                                    <span class="link-blue funBtn" type="1" data-fun="editInvoiceInfoLog">编辑记录</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>规格型号</td>
                            <td>
                                <span class="service_ggInvoice"></span>
                                <div class="btn-group ty-right">
                                    <span class="link-blue funBtn" type="2" data-fun="editInvoiceInfo">编辑</span>
                                    <span class="link-blue funBtn" type="2" data-fun="editInvoiceInfoLog">编辑记录</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </section>
                <div class="text-right" style="margin-top: 8px" id="cre1"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel(); " >关闭</span>
        </div>
    </div>
    <%--  修改套餐  --%>
    <div class="bonceContainer bounce-blue" id="editService" style="width:900px; ">
        <div class="bonceHead">
            <span>修改套餐</span>
            <a class="bounce_close" onclick="bounce.show($('#serviceScan'));"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <section class="service_main">
                    <div class="请定义本套餐">
                        <div>请定义本套餐</div>
                        <small class="ty-color-blue">注：此处录入的数据可供不同的服务合同重复引用。</small>
                    </div>
                    <table class="kj-table byCycle">
                        <thead>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="40%">说明</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td><input need name="code" type="text" require onblur="checkCode($(this))"/><div class="errorTip"><small class="ty-color-red">这个代号已被使用，请换一个！</small></div></td>
                            <td><input need name="name" type="text" placeholder="此处最多可录入15字" maxlength="15" require onkeyup="limitWord($(this), 15)"/></td>
                            <td><input need name="memo" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this), 100)"/></td>
                        </tr>
                        </tbody>
                    </table>
                </section>
                <section class="service_content">
                    <div class="kj-hr"></div>
                    <div class="ty-alert goods">
                        <span>套餐所包含的内容</span>
                        <div class="btn-group">
                            <span class="link-blue" data-where="new" onclick="addProject($(this))">添加项目</span>
                            <span class="link-blue addGoodsBtn" data-where="new" id="addGoodsBtn" onclick="addGoodsInit($(this))">添加商品</span>
                        </div>
                    </div>
                    <div class="projectOrGoods projectContent">
                        <div>项目</div>
                        <div>
                            <table class="kj-table serviceTab">
                                <thead>
                                <tr>
                                    <td>服务项目代号/名称</td>
                                    <td>参考单价</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>

                                </tbody>
                                <tfoot>
                                <tr class="guDing">
                                    <td colspan="3">原价合计</td>
                                    <td colspan="2" class="sumAll" id="sumAll"></td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <div class="projectOrGoods good2Content">
                        <div style="margin:15px 0 10px;">仅第一个收费周期需提供的商品</div>
                        <div>
                            <table class="kj-table goods2Tab">
                                <thead>
                                <tr>
                                    <td>商品代号名称</td>
                                    <td>参考单价</td>
                                    <td>发货事宜</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                                <tfoot>
                                <tr class="guDing">
                                    <td colspan="4">原价合计</td>
                                    <td colspan="2" class="sumAll" id="sumAll2"></td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <div class="projectOrGoods good1Content">
                        <div style="margin:15px 0 10px;">每个收费周期都需提供的商品</div>
                        <div>
                            <table class="kj-table goods1Tab">
                                <thead>
                                <tr>
                                    <td>商品代号名称</td>
                                    <td>参考单价</td>
                                    <td>发货事宜</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                                <tfoot>
                                <tr class="guDing">
                                    <td colspan="4">原价合计</td>
                                    <td colspan="2" class="sumAll" id="sumAll3"></td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </section>
                <section class="service_other">
                    <div class="kj-hr"></div>
                    <div class="priceTp">
                        <div>价格<i class="xing"></i></div>
                        <small class="ty-color-blue">注：下表内各种单价中，不涉及的可以不录，但至少需录入一种。</small>
                    </div>
                    <table class="kj-table priceForm">
                        <thead>
                        <tr>
                            <td width="40%" colspan="3">开增值税专用发票时</td>
                            <td width="20%" rowspan="2">开普票时的开票单价</td>
                            <td width="20%" rowspan="2">不开发票时的单价</td>
                            <td width="20%" rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>
                                <select name="taxRate" onchange="chargeRate($(this))"></select>
                            </td>
                            <td><input need name="unitPrice" type="text" onkeyup="clearNoNumN(this, 8)" onblur="setPrice($(this), 1)"/></td>
                            <td><input need name="unitPriceNotax" type="text" onkeyup="clearNoNumN(this, 8)" onblur="setPrice($(this), 2)"/></td>
                            <td><input need name="unitPriceInvoice" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                            <td><input need name="unitPriceNoinvoice" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                            <td><input need name="unitPriceReference" type="text" onkeyup="clearNoNumN(this, 8)" /></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="6"><input name="priceDesc" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="byCycle gapTp">
                        <div class="origin periodDurationInfo">
                            <span class="gapRt">收费周期<i class="xing"></i></span>
                            每
                            <select class="entry periodDuration" name="periodDuration">
                                <option value=""></option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                            </select>
                            <select class="entry periodUnit" name="periodUnit" onchange="chargeUnit($(this))">
                                <option value="">请选择</option>
                                <option value="7">年</option>
                                <option value="4">个月</option>
                            </select>
                            收费一次
                        </div>
                        <div class="origin">
                            <span class="gapRt">收费时间<i class="xing"></i></span>
                            <select class="entry chargeStage" name="chargeStage" style="margin-left: 17px;">
                                <option value="">请选择</option>
                                <option value="1">合同签订后</option>
                                <option value="2">服务开始前</option>
                                <option value="3">服务开始后</option>
                                <option value="13">服务结束后</option>
                            </select>
                            <input class="chargeLimit" need name="chargeLimit" type="text" placeholder="请填写" onkeyup="clearNum(this)"/>天内
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.show($('#serviceScan'));" > 取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 funBtn" data-fun="editServiceOk">确定</span>
        </div>
    </div>

    <%--  停用/启用 套餐  --%>
    <div class="bonceContainer bounce-blue" id="stopOrStartService">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.show($('#serviceScan'));"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;" id="stopTip">

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()" > 取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 funBtn" data-fun="stopOrStartService">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>服务套餐</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <input type="hidden" id="showMainConNum">
                    <div class="mainCon mainCon1">
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div>公司如有常规的服务套餐，请在此录入。各常规套餐可供录入合同、订单或套餐时选用。</div>
                                <div>
                                    <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3 funBtn" data-fun="addService">新增</span>
                                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 funBtn" data-fun="stoppedService">已停用的套餐</span>
                                </div>
                            </div>
                            <div class="ty-alert">
                                <div>系统内在用的常规服务套餐共 <span id="mainsNum"></span> 条，具体如下：</div>
                                <div class="ty-search">
                                    <span class="search">查找</span>
                                    <input class="ty-searchInput" type="text" placeholder="请输入要查找服务套餐的名称" id="search1" style="width: 300px">
                                    <i class="ty-searchBtn funBtn" data-fun="searchList"></i>
                                </div>
                            </div>
                            <table class="kj-table" id="main1Tab">
                                <thead>
                                <tr>
                                    <td>服务套餐（代号/名称）</td>
                                    <td>价格</td>
                                    <td>服务费用的收取</td>
                                    <td>创建时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div id="ye_service"></div>
                        </div>
                    </div>
                    <div class="mainCon mainCon2">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="reback">返回</span>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div>系统内已停用的套餐共 <span id="snum"></span> 条，具体如下：</div>
                                <div class="ty-search">
                                    <span class="search">查找</span>
                                    <input class="ty-searchInput" type="text" placeholder="请输入要查找服务套餐的名称" id="search2" style="width: 300px">
                                    <i class="ty-searchBtn funBtn" data-fun="searchListStop"></i>
                                </div>
                            </div>
                            <table class="kj-table" id="main2Tab">
                                <thead>
                                <tr>
                                    <td>服务套餐（代号/名称）</td>
                                    <td>价格</td>
                                    <td>服务费用的收取</td>
                                    <td>停用时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            <div id="ye_serviceStop"></div>
                        </div>
                    </div>
                    <div class="mainCon mainCon3">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="reback()">返回</span>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div>符合查询条件的数据共XX条，具体如下：</div>
                            </div>
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td>服务套餐（代号/名称）</td>
                                    <td>价格</td>
                                    <td>服务费用的收取</td>
                                    <td>创建时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div id="ye3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/sales/serviceSetMeal.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
