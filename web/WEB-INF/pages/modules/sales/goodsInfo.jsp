<%--
  Created by IntelliJ IDEA.
  User: houxingzhe
  Date: 2023/11/1
  Time: 11:40
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
  <link href="../css/common/theme/green.css?v=SVN_REVISION"  rel="stylesheet" type="text/css" />

</head>
<style>
  body{ color: #333; }
  .line{ border-bottom: 1px solid #ccc;  }
  table{ width:100%;
    border-collapse: collapse;
    border-spacing: 0;
  }
  td{ padding:5px 10px; border:1px solid #ccc; text-align: center; font-size: 14px;  }
  .linkBtn{ color: #0b94ea; }
  .linkBtn:hover{  text-decoration: underline  }
  .line{
    border-top:1px solid #ccc;
    border-bottom:1px solid #ccc;
    padding: 10px;
    margin:10px 0;
  }
</style>
<body>


<div class="bonceContainer bounce-blue" id="cus" style="width:1040px; display: block; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span>商品清单</span>
    <a class="bounce_close" onclick="closeIframe()"></a>
  </div>
  <div class="bonceCon" id="InvoiceMessage">
    <div>
      <p><span class="listNum"></span>条商品需采用新的价格，新价格如下：</p>
      <table id="gl">
        <tr>
          <td>商品代号/名称/规格/型号	</td>
          <td>货物或应税劳务、服务名称</td>
          <td>规格型号</td>
          <td>新价格</td>
          <td>修改时间</td>
          <td>其他</td>
        </tr>
        <tr>
          <td>商品代号/名称/规格/型号	</td>
          <td>货物或应税劳务、服务名称</td>
          <td>规格型号</td>
          <td>新价格</td>
          <td>修改时间</td>
          <td>
            <span class="linkBtn">改前数据</span>
            <span class="linkBtn">修改记录</span>
          </td>
        </tr>
      </table>

      <div class="line">
        请更新公司开票系统中的资料！
      </div>
    </div>
  </div>
  <div class="bonceFoot">
    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeIframe()">关闭</span>
  </div>
</div>

<%--  商品价格信息的修改记录  --%>
<div class="bonceContainer bounce-blue" id="epPriceLog" style="width: 1040px;display: none; margin-top: 0; margin-bottom: 0;">
  <div class="bonceHead">
    <span>商品价格信息的修改记录</span>
    <a class="bounce_close" onclick="$('#cus').show().siblings().hide()"></a>
  </div>
  <div class="bonceCon">
    <p id="epPriceLogInfo">当前资料为第n次修改后的结果，修改人：XXX XXXX-XX-XX XX：XX：XX</p>
    <table class="ty-table" id="epPriceLogTab">
      <tr>
        <td>数据</td>
        <td>有效期</td>
        <td>创建人/修改人</td>
        <td>修改原因</td>
        <td>财务的确认记录</td>
      </tr>

    </table>
  </div>
  <div class="bonceFoot main80">
    <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="$('#cus').show().siblings().hide()">关 闭</span>
  </div>
</div>




<%@ include  file="../../common/footerScript.jsp"%>

<script>
  $(function(){
    getGoodsList()
  })
  function closeIframe(){
    console.log('closeIframe')
    $(window.parent.document).find("#somethingItemDetailsPage").hide()
  }

  // creator : hxz 2023-11-3  商品清单
  function getGoodsList() {
    let goodsList = localStorage.getItem('goodsList')
    goodsList = JSON.parse(goodsList)
    console.log('goodsList =', goodsList )
    $(".listNum").html(goodsList.length)
    let str = ''
    goodsList.forEach( goods => {
      let upDate = new Date(goods.update_date).format("yyyy-MM-dd hh:mm:ss")
      let td1 = goods.outer_sn + '/' + goods.outer_name + '/' + goods.specifications + '/' + goods.model
      str = `
        <tr>
            <td>`+ td1 +`</td>
            <td>`+ (goods.snCode || '') +`</td>
            <td>`+ (goods.codeSpe || '') +`</td>
            <td>`+ (goods.item_price || '') +`</td>
            <td>`+ upDate +`</td>
            <td>
              <span class="linkBtn" onclick="showOldPrice($(this))">改前数据</span>
              <span class="linkBtn" onclick="getHis($(this))">修改记录</span>
              <span class="hd">`+ JSON.stringify(goods) +`</span>
            </td>
          </tr>
      `
    })
    $("#gl tr:gt(0)").remove()
    $("#gl").append(str)

  }

  function showOldPrice(thisObj) {
    let info = thisObj.siblings('.hd').html()
    info = JSON.parse(info)
    layer.msg( `本次改前的价格为：` + (info.old_price || ''))
  }
  function getHis(thisObj) {
    let info = thisObj.siblings('.hd').html()
    info = JSON.parse(info)
    let pm_id = info.pm_id
    let source = info.type; // 1-通用 2-专属
    $.ajax({
      'url':'../product/getPdMerchandisePriceRecordList.do',
      'data': { id: pm_id },
      success:function(res){
        let list = res.data.list || []
        if(list.length === 0){
          layer.msg('没有价格修改记录！')
          return false
        }
        $("#epPriceLog").show().siblings().hide()
        $("#epPriceLog table tr:gt(0)").remove();
        let str = ``
        var priceStrAll = ''
        list.forEach( (info, index) => {
          var unitPrice = info.unitPrice ? info.unitPrice.toFixed(2) : '0.00' ;
          var unitPriceNotax = info.unitPriceNotax ? info.unitPriceNotax.toFixed(2) : '0.00' ;
          var unitPriceInvoice = info.unitPriceInvoice ? info.unitPriceInvoice.toFixed(2) : '0.00' ;
          var unitPriceNoinvoice = info.unitPriceNoinvoice ? info.unitPriceNoinvoice.toFixed(2) : '0.00' ;


          if(source == 1){ // 通用
            var unitPriceReference = info.unitPriceReference ? info.unitPriceReference.toFixed(2) : '0.00' ;
            var specialDes = handleNull(info.taxRate)=='' && handleNull(info.unitPrice)=='' && handleNull(info.unitPriceNotax)==''? '':'税率'+handleNull(info.taxRate)+'%，含税单价'+unitPrice+'元，不含税价'+unitPriceNotax+'元';
            let generalDes = handleNull(info.unitPriceInvoice)==''?'':'开票单价' +unitPriceInvoice+'元';
            let noDes = handleNull(info.unitPriceNoinvoice)==''?'':'不开票价'+unitPriceNoinvoice+ '元';
            let referenceDes = handleNull(info.unitPriceReference)==''?'': '参考单价'+ unitPriceReference+'元';
            priceStrAll = specialDes ? specialDes + ';<br/>' : '' +
                          generalDes ? generalDes + ';<br/>' : '' +
                          noDes ? noDes + ';<br/>' : '' +
                          referenceDes ? referenceDes + ';<br/>' : ''

          }
          else{ // 专属
            let invoiceCategoryStr = ``, priceStr = ``;
            if (info["invoiceCategory"] == 1) {
              invoiceCategoryStr = `该商品需开税率为`+ info.taxRate +`%的增值税专用发票`;
              priceStr =  `含税单价`+ unitPrice +`元，不含税价`+ unitPriceNotax +`元`;
            } else if (info["invoiceCategory"] == 2) {
              invoiceCategoryStr = `该商品需开增值税专用发票以外的发票`;
              priceStr =  `开普通发票的开票单价`+ unitPriceInvoice +`元`;
            } else if (info["invoiceCategory"] == 4) {
              invoiceCategoryStr = `该商品不开发票`;
              priceStr = `不开票单价`+ unitPriceNoinvoice +`元`;
            }
            priceStrAll = invoiceCategoryStr + `<br/>` + priceStr
          }
          let effectiveDate = new Date(info.effectiveDate).format('yyyy-MM-dd')
          let endDate = new Date(info.endDate).format('yyyy-MM-dd')
          let createDate = new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss')
          let financerTime = new Date(info.financerTime).format('yyyy-MM-dd hh:mm:ss')
          let uptor = info.createName
          if(index > 0){
            uptor = info.updateName
          }
          str += `
                 <tr>
                    <td>`+ priceStrAll +`</td>
                    <td>`+ (effectiveDate || '--') +` 至 `+ (endDate || '--' ) +`</td>
                    <td>`+ (uptor || '') +` `+ createDate +`</td>
                    <td>`+ (info.priceReason || '') +`</td>
                    <td>`+ (info.financerName || '') + financerTime +`</td>
                </tr>
                `
        })
        $("#epPriceLogTab tr:gt(0)").remove();
        $("#epPriceLogTab").append(str);

        let lastInfo = list[list.length - 1]
        let updateDate2 = new Date(lastInfo.updateDate).format('yyyy-MM-dd hh:mm:ss')

        let infoStr = `当前资料为第`+ (list.length - 1) +`次修改后的结果，修改人：`+ (lastInfo.updateName ) + updateDate2
        $("#epPriceLogInfo").html(infoStr);

      }
    })

  }

  function handleNull( str) {
    return str? str : ''
  }


</script>
</body>
</html>
