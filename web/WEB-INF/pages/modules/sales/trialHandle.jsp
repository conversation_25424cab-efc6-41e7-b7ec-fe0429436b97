<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/sale.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/wonderssProduct/sysCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .main{
        width: 620px;
        margin-left: 32px;
    }
    .processList{
        margin: 16px 0;
        text-align: right;
        color: #666;
    }
    .approveBtn{
        display: none;
        margin-bottom: 8px;
        text-align: right;
    }
    .tipCon{
        width: 300px;
        margin: auto;
    }
    .tipCon textarea{
        width: 100%;
    }
    .brdTop{
        display: block;
        margin: 16px 0;
        line-height: 1.5;
        border: 1px solid #0b94ea;
        padding: 8px;
        background: #e5f3ff;
        font-weight: bold;
        border-left-width: 8px;
    }
    .brdTopRed{ border-color:#ed5565;color: #ed5565; }
    .brdTopGreen{ border-color:#48cfad;color: #48cfad;}
    .brdTopBlue{ border-color:#5d9cec;color: #5d9cec; }
    #addServiceChangeInfo{
        width: 1300px;
    }
    .compare_icon{
        padding: 4px 8px;
        color: #aaa;
    }
    .productName{
        font-size: 16px;
        font-weight: bold;
    }
    .modelNameRow{
        font-size: 14px;
        line-height: 36px;
        border-bottom: 1px solid #eee;
        color: #999;
    }
    .modelNameRow .modelName{
        margin-left: 8px;
        color: #666;
    }
    .changeNum{
        margin: 0 4px;
        font-weight: bold;
    }
    .nullStr{
        padding: 4px;
        text-align: center;
        color: #999;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed5">
    <div class="bonceContainer bounce-blue" id="seeModule" style="width: 700px">
        <div class="bonceHead">
            <span>模块查看</span>
            <a class="bounce_close" onclick="bounce_Fixed5.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><b class="moduleName"></b></h4>
                <table class="kj-table moduleTable">
                    <thead>
                    <tr>
                        <td class="thisLevel">一级菜单</td>
                        <td class="nextLevel">
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td class="thisLevel">二级菜单</td>
                                    <td class="nextLevel">
                                        <table class="kj-table">
                                            <thead>
                                            <tr>
                                                <td class="thisLevel">三级菜单</td>
                                            </tr>
                                            </thead>
                                        </table>
                                    </td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed5.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed4">
    <%--套餐查看--%>
    <div class="bonceContainer bounce-blue" id="seeSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐查看</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><span class="setMenuName"></span></h4>
                <div class="ty-alert">
                    组成本套餐的模块
                    <div class="btn-group">
                        创建 <span class="create"></span>
                    </div>
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%--套餐清单查看--%>
    <div class="bonceContainer bounce-blue" id="seeUsableSetMenu" style="width: 700px">
        <div class="bonceHead">
            <span>套餐清单查看</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="ty-alert">
                    本模板的用户不可选择的模块数为A，设系统内已有套餐数为B
                    需根据A、B，算出系统内不包含A的已有套餐数C，并需列出C的清单
                    机构可选择的套餐，即为上述C的清单，具体如下：
                </div>
                <table class="kj-table kj-table-striped">
                    <thead>
                    <tr>
                        <td>套餐名称</td>
                        <td>创建</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-red" id="tip3" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p class="tipMs" style="text-align: center; padding:10px 0 8px"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">确定</span>
        </div>
    </div>
    <%-- 查看产品--%>
    <div class="bonceContainer bounce-blue" id="seeProduct" style="width: 700px">
        <div class="bonceHead">
            <span>产品查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <h4><span class="pdName"></span></h4>
                <div class="item-flex">
                    <div class="item-title">所选模板：</div>
                    <div class="item-content"><span class="pdModel"></span><div class="ty-right">操作人 <span class="pdTime"></span></div></div>
                </div>
                <div class="ty-alert">
                    已重命名模块的数量 <b class="renameNumber"></b> 个
                </div>
                <table class="kj-table tbl_see_rename" style="width: 70%">
                    <thead>
                    <tr>
                        <td>原名称</td>
                        <td>新名称</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert">主套餐内的模块</div>
                <table class="kj-table kj-table-striped tbl_see_mainModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    使用本模板的用户增值功能的模块
                </div>
                <table class="kj-table kj-table-striped tbl_see_valueAddedModule">
                    <thead>
                    <tr>
                        <td>模块名称</td>
                        <td>下辖的一级菜单数量</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="ty-alert" style="margin-top: 16px">
                    与本模板增值功能模块对应的已有套餐
                    <div class="btn-group">
                        <span class="link-blue" onclick="seeUsableSetMenuBtn('seeModel')">查看</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="tip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p class="tipMs" style="text-align: center; padding:10px 0 8px"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%-- 提示框 --%>
    <div class="bonceContainer bounce-red" id="mtTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type="btn" data-name="">确定</span>
        </div>
    </div>
    <%--新增收货信息--%>
    <div class="bonceContainer bounce-green" id="newReceiveInfo" style="width: 450px">
        <div class="bonceHead">
            <span>收货信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">收货地址：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="ReceiveAddress" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">收货人：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="ReceiveName" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">收货电话：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="ReceiveCallNo" require/></span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addReceive" data-name="addReceive">提交</button>
        </div>
    </div>
    <%--新增发票邮寄地址--%>
    <div class="bonceContainer bounce-green" id="newMailInfo" style="width: 450px">
        <div class="bonceHead">
            <span>发票邮寄信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">邮寄地址：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="mailAddress" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">发票接收人：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="mailName" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">联系电话：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="mailContact" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">邮寄编码：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="mailNumber" require/></span>
            <p id="mailNumError">请输入正确的邮寄编码！</p>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addMail" data-name="addMail">提交</button>
        </div>
    </div>
    <%--新增联系人--%>
    <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
        <div class="bonceHead">
            <span>联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <form id="newContectData">
                <p>
                    <span class="sale_ttl1">姓名：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                </p>
                <p>
                    <span class="sale_ttl1">手机：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactNumber" require/></span>
                    <a class="sale_ttl1 addMore" onclick="addMore($(this))">添加更多联系方式</a>
                    <select style="display: none;width: 167px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                        <option value="0"></option>
                        <option value="1">手机</option>
                        <option value="2">QQ</option>
                        <option value="3">Email</option>
                        <option value="4">微信</option>
                        <option value="5">微博</option>
                        <option value="9">自定义</option>
                    </select>
                </p>
                <ul class="otherContact">
                </ul>
                <div id="contactsCard">
                    <span class="sale_ttl1">名片：</span>
                    <div id="uploadCard" class="cardUploadBtn"></div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addContact" data-name="addContact">提交</button>
        </div>
    </div>
    <%--新增访谈记录--%>
    <div class="bonceContainer bounce-green" id="newInterviewInfo" style="min-width: 980px">
        <div class="bonceHead">
            <span>新增访谈记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form id="newInterview">
                <div class="sale_c">
                    <span class="sale_ttl1">访谈对象：</span>
                    <input type="text" name="interviewer" placeholder="请录入" require/>
                    <span class="sale_ttl1">职位：</span>
                    <input type="text" name="post" placeholder="请录入" require/>
                    <span class="sale_ttl1">访谈日期：</span>
                    <input type="text" id="interviewDate" placeholder="请选择" require/>
                </div>
                <div class="sale_c">
                    <span class="sale_ttl1">访谈内容：</span>
                    <textarea name="content" style="width:710px;" type="text" placeholder="请录入" onkeyup="countWords($(this),500)" require></textarea>
                </div>
                <div class="textMax text-right" style="margin-right:100px;">0/500</div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="newInterviewSure" data-type="new" onclick="newInterviewSure($(this))">提交</button>
        </div>
    </div>
    <%--删除、停用、启用提示--%>
    <div class="bonceContainer bounce-red" id="turnStateTip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="shu1">
                <p id="turnTipMs" style="text-align: center; padding:10px 0 8px"> </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" id="oprationSure">确定</span>
        </div>
    </div>
    <%--删除访谈记录提示--%>
    <div class="bonceContainer bounce-red" id="delInterviewTip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p style="text-align: center; padding:10px">删除后，本访谈记录依旧显示在访谈记录的列表中，但仅可查看。 </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="deletInterViewSure()">确定</span>
        </div>
    </div>
    <%--访谈记录修改记录--%>
    <div class="bonceContainer bounce-blue" id="delInterviewTip" style="width: 450px">
        <div class="bonceHead">
            <span>访谈记录修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear noneUpdated">
                <span class="ty-left">当前资料尚未经修改。</span>
                <span class="ty-right">创建人：XXX XXXX/XX/XX XX.XX.XX</span>
            </div>
            <div class="">
                <div>
                    <p class="ty-left">当前资料为第<span class="eidtNum"></span>次修改后的结果。</p>
                    <p class="updaterInfo ty-right">修改时间：XXXX/XX/XX XX:XX:XX</p>
                </div>
                <table class="ty-table ty-table-control updateRecordList">
                    <tr>
                        <td>记  录</td>
                        <td>操  作</td>
                        <td>创建者/修改者</td>
                    </tr>
                    <tr>
                        <td>原始信息</td>
                        <td><span class="ty-color-blue">查看</span></td>
                        <td>缘根       XXXX/XX/XX XX:XX:XX</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="">确定</span>
        </div>
    </div>
    <%--基本信息修改--%>
    <div class="bonceContainer bounce-blue" id="updataccount" style="min-width:1100px;">
        <div class="bonceHead">
            <span>基本信息修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form class="addpayDetails" id="sale_updataBase">
                <div class="flex-box">
                    <div class="sale_c">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span><span>客户名称:</span></div>
                        <div class="sale_con1"><input type="text" data-name="name" id="updata_cusname" require></div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1"><span>地    址:</span></div>
                        <div class="sale_con2"><input type="text" data-name="address" id="updata_cusSite" require></div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1"><span>客户代号：</span></div>
                        <div class="sale_con"><input type="text" data-name="code" id="updata_cuscoding" require></div>
                    </div>
                </div>
                <div class="sale_c">
                    <span style="margin-left:50px;">是否已购买过产品或服务？</span>
                    <label><input type="radio" name="buyCase" value="1" onclick="purchasedAny($(this))">是</label>
                    <label><input type="radio" name="buyCase" value="0" onclick="purchasedAny($(this))">否</label>
                    <span class="purchased">
                        <span>首次购买时间：</span>
                        <label><input type="radio" name="firstTime" onclick="checkMonth($(this))" value="1">今年</label>
                        <input id="initialPeriod" name="initialPeriod" placeholder="请选择月份" style="display: none;" require/>
                        <label><input type="radio" name="firstTime" value="2" onclick="checkMonth($(this))">去年</label>
                        <label><input type="radio" name="firstTime" value="3" onclick="checkMonth($(this))">更久之前</label>
                    </span>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">全景照片：</div>
                    <div class="imgWall" id="edit_qImages">
                        <div id="edit_panoramaBtn" class="saleUploadBtn"></div>
                    </div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">产品图片：</div>
                    <div class="imgWall" id="edit_pImages">
                        <div id="edit_productPicsBtn" class="saleUploadBtn"></div>
                    </div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">首次接触时间：</div>
                    <div class="sale_con1">
                        <input id="edit_firstTime" placeholder="请选择" value="" require/>
                    </div>
                    <div class="sale_ttl1">首次接触地点：</div>
                    <div class="sale_con1">
                        <input data-name="firstContactAddress" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl2">信息获取渠道：</div>
                    <div class="sale_con1">
                        <input data-name="infoSource" type="text" value="" placeholder="请录入" require />
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="updateAccountBtn" onclick="updata_sure()" disabled>提交</button>
        </div>
    </div>
    <%--开票信息修改--%>
    <div class="bonceContainer bounce-blue" id="updateInvoice" style="width:1100px;">
        <div class="bonceHead">
            <span>开票信息修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form id="sale_updataInvoice">
                <div class="sale_c">
                    <div class="sale_ttl1">公司名称：</div>
                    <div class="sale_con1">
                        <input data-name="invoiceName" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">地址：</div>
                    <div class="sale_con2">
                        <input data-name="invoiceAddress" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">电话：</div>
                    <div class="sale_con">
                        <input data-name="telephone" type="text" placeholder="请录入" require/>
                    </div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">开户行：</div>
                    <div class="sale_con1">
                        <input data-name="bankName" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">账号：</div>
                    <div class="sale_con1" style="margin-right: 100px;">
                        <input data-name="bank_no" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">税号：</div>
                    <div class="sale_con">
                        <input data-name="taxpayerID" type="text" placeholder="请录入" require/>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="updataInvoiceSure" onclick="updataInvoice_sure()">提交</button>
        </div>
    </div>
    <%--修改记录--%>
    <div class="bonceContainer bounce-blue" id="updateRecords">
        <div class="bonceHead">
            <span class="recordTtl">修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <div class="createRecord clear">
                <div class="clear">
                    <p class="ty-left recordTip">当前资料尚未经修改。</p>
                    <p class="ty-right recordEditer"></p>
                </div>
            </div>
            <table class="ty-table ty-table-control changeRecord">
                <thead>
                <tr>
                    <td>记  录</td>
                    <td>操  作</td>
                    <td>创建者/修改者</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--联系方式查看--%>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>联系方式</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>
            <ul class="see_otherContact">
            </ul>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--名片查看--%>
    <div class="bonceContainer bounce-blue" id="visitCardDetail">
        <div class="bonceHead">
            <span>名片</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="see_contactsCard">

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 新建机构 --%>
    <div class="bonceContainer bounce-blue" id="selectModule" style="width: 680px;">
        <div class="bonceHead">
            <span class="bounce_title">勾选模块</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel();bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none orgBase">
                <tr>
                    <td><span class="ty-color-red">*</span> 机构名称</td>
                    <td><input type="text" name="fullName" class="ty-inputText" placeholder="请输入" require></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 机构简称</td>
                    <td><input type="text" name="name" class="ty-inputText" placeholder="请输入" require></td>
                </tr>
                <tr>
                    <td>超管姓名</td>
                    <td><input type="text" name="supervisorName" class="ty-inputText"></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 超管手机</td>
                    <td><input type="text" name="supervisorMobile" class="ty-inputText" placeholder="请输入" require onkeyup="clearNum(this)"></td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 数据存储地点</td>
                    <td>
                        <select type="text" name="uploadStorageType" class="ty-inputSelect" require>
                            <option value="">请选择</option>
                            <option value="NFS">NFS</option>
                            <option value="Seafile" disabled>Seafile</option>
                        </select>
                    </td>
                </tr>
            </table>
            <div class="module_avatar">
                <table class="ty-table ty-table-none">
                    <tbody>
                    <tr>
                        <td style="width: 236px"><span class="ty-color-red">*</span> 请勾选模块</td>
                        <td>
                            <div class="ty-checkbox">
                                <input type="checkbox" id="0-0" name="checkAll">
                                <label for="0-0"></label> 模块
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="module_header">
                    <div class="module_list">
                        <div class="module_row" level="1">
                            <div class="module_item">
                                <div class="ty-checkbox">
                                    模块
                                </div>
                            </div>
                            <div class="module_list">
                                <div class="module_row" level="2">
                                    <div class="module_item">
                                        <div class="ty-checkbox">
                                            子模块
                                        </div>
                                    </div>
                                    <div class="module_list">
                                        <div class="module_row" level="3">
                                            <div class="module_item">
                                                <div class="ty-checkbox">
                                                    二级子模块
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="module_body"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="newModuleBtn">
                <span class="ty-btn ty-btn-big ty-circle-3"  onclick="bounce_Fixed.cancel();bounce.cancel()">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="submitModule" id="submitModuleBtn" disabled="disabled">提交</button>
            </div>
            <div class="seeModuleBtn">
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="changeOrg">修改</button>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="orgChangeHistory" disabled="disabled">修改记录</button>
            </div>
            <div class="changeModuleBtn">
                <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();bounce.cancel()">取消</button>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="sureChangeOrg" id="sureChangeOrgBtn" disabled="disabled">提交</button>
            </div>
        </div>
    </div>
</div>
<div class="bounce">
    <div style="width:750px" class="bonceContainer bounce-green" id="leading">
        <div class="bonceHead">
            <span>导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <iframe src="../script/sales/cusImport.jsp" frameborder="0" id="importCon"> </iframe>

    </div>
    <div class="bonceContainer bounce-red" id="mtTip" style="width: 400px;">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center;font-size:14px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="agreeTip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipCon text-center">确定同意该机构试用系统？</div>
            <div class="tipCon text-center">
                <span class="ty-color-red">*</span> 电脑端登陆网址
                <select name="tyDomain" id="tyDomain" class="ty-inputSelect" require></select>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="approveTrial(1);">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="bounce_tip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipCon text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn" type="btn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="rejectTip" style="width: 450px">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipCon">
                <p>请输入驳回理由：</p>
                <textarea class="reason" cols="30" rows="3" onkeyup="countWords($(this),50)"></textarea>
                <div class="textMax text-right" max="50"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addAccount" style="min-width: 1200px;">
        <div class="bonceHead">
            <span>新增客户</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  style="height: 600px;overflow: auto">
            <p>以下各项中仅客户名称为必填项，但为确保系统其他功能正常使用，<span class="ty-color-red">强烈建议</span>将客户的<span class="ty-color-red">收货信息</span>填写完整。</p>
            <form class="sale_updata" id="addpayDetails">
                <div class="">
                    <p>基本信息</p>
                    <div class="sale_c">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>客户名称：</div>
                        <div class="sale_con1"><input type="text" id="add_cusname" data-name="name" placeholder="请录入" require></div>
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>地址：</div>
                        <div class="sale_con2"><input type="text" id="add_address1" data-name="address" placeholder="请录入" require></div>
                        <div class="sale_ttl2">客户代号:</div>
                        <div class="sale_con3"><input type="text" id="add_cuscoding" data-name="code" placeholder="请录入"></div>
                    </div>
                    <div class="sale_c specialOrgPart">
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>最高负责人：</div>
                        <div class="sale_con1"><input type="text" id="add_supervisorName" data-name="supervisorName" placeholder="请录入" require></div>
                        <div class="sale_ttl1"><span class="ty-color-red">*</span>手机：</div>
                        <div class="sale_con2"><input type="text" id="add_supervisorMobile" data-name="supervisorMobile" placeholder="请录入" require onkeyup="clearNum(this)"></div>
                    </div>
                    <div class="sale_c">
                        <span style="margin-left:50px;">是否已购买过产品或服务？</span>
                        <label><input type="radio" name="buyCase" value="1" onclick="purchasedAny($(this))">是</label>
                        <label><input type="radio" name="buyCase" value="0" onclick="purchasedAny($(this))">否</label>
                        <span class="purchased">
                            <span>首次购买时间：</span>
                            <label><input type="radio" name="firstTime" onclick="checkMonth($(this))" value="1">今年</label>
                            <input id="buyMonth" name="initialPeriod" placeholder="请选择月份" style="display: none;" />
                            <label><input type="radio" name="firstTime" value="2" onclick="checkMonth($(this))">去年</label>
                            <label><input type="radio" name="firstTime" value="3" onclick="checkMonth($(this))">更久之前</label>
                        </span>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">全景照片：</div>
                        <div class="imgWall" id="qImages">
                            <div id="panoramaBtn" class="saleUploadBtn"></div>
                        </div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">产品图片：</div>
                        <div class="imgWall" id="pImages">
                            <div id="productPicsBtn" class="saleUploadBtn"></div>
                        </div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">首次接触时间：</div>
                        <div class="sale_con1">
                            <input id="firstContactTime" placeholder="请选择" value=""/>
                        </div>
                        <div class="sale_ttl1">首次接触地点：</div>
                        <div class="sale_con1">
                            <input data-name="firstContactAddress" type="text"/>
                        </div>
                        <div class="sale_ttl2">信息获取渠道：</div>
                        <div class="sale_con1">
                            <input data-name="infoSource" type="text" value="" placeholder="请录入">
                        </div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">备注：</div>
                        <div class="sale_con4">
                            <input data-name="memo" type="text" onkeyup="countWords($(this),255)" />
                            <div class="textMax text-right">0/255</div>
                        </div>
                    </div>
                    <p>开票信息</p>
                    <div class="sale_c">
                        <div class="sale_ttl1">公司名称：</div>
                        <div class="sale_con1">
                            <input data-name="invoiceName" type="text" placeholder="请录入"/>
                        </div>
                        <div class="sale_ttl1">地址：</div>
                        <div class="sale_con2">
                            <input data-name="invoiceAddress" type="text" placeholder="请录入"/>
                        </div>
                        <div class="sale_ttl1">电话：</div>
                        <div class="sale_con">
                            <input data-name="telephone" type="text" placeholder="请录入"/>
                        </div>
                    </div>
                    <div class="sale_c">
                        <div class="sale_ttl1">开户行：</div>
                        <div class="sale_con1">
                            <input data-name="bankName" type="text" placeholder="请录入"/>
                        </div>
                        <div class="sale_ttl1">账号：</div>
                        <div class="sale_con1" style="margin-right: 100px;">
                            <input data-name="bankNo" type="text" placeholder="请录入"/>
                        </div>
                        <div class="sale_ttl1">税号：</div>
                        <div class="sale_con">
                            <input data-name="taxpayerID" type="text" placeholder="请录入"/>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">收货信息</span>
                        <span id="newAddress" class="ty-btn ty-btn-green ty-btn-big node" data-type="new" data-name="receiveInfo" data-source="addCustomer">新增</span>
                        <div class="dataList receiveList"></div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">发票邮寄信息</span>
                        <span id="newMail" class="ty-btn ty-btn-green ty-btn-big node" data-type="new" data-name="mailInfo" data-source="addCustomer">新增</span>
                        <div class="dataList mailList">
                            <table class="ty-table ty-table-control"></table>
                        </div>
                    </div>
                    <div class="addOtherInfo">
                        <span class="par_ttl">联系人</span>
                        <span id="newContact" class="ty-btn ty-btn-green ty-btn-big node" data-type="new" data-name="contactInfo" data-source="addCustomer">新增</span>
                        <div class="dataList contectList"></div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="addAccountBtn" onclick="sale_addsure()">提交</button>
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 node" type="btn" data-name="notSelectModule" id="notSelectModuleBtn">录入完毕，暂不勾选模块</button>
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 node" type="btn" data-name="selectModule" id="selectModuleBtn">录入完毕，勾选模块</button>
            </span>
            <%--<div class="specialOrgPart">--%>
            <%--<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="" onclick="sale_addsure()">录入完毕，暂不勾选模块</button>--%>
            <%--<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="" onclick="sale_addsure()">录入完毕，勾选模块</button>--%>
            <%--</div>--%>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="seeAccount" style="min-width:1220px;">
        <div class="bonceHead">
            <span>客户信息查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails sale_see">
                <div class="part">
                    <div class="item">
                        <div class="item"><span class="item_title">客户名称：</span><span class="item_content" id="see_cusname" style="width: 600px"></span></div>
                        <div class="item"><span class="item_title">客户代号：</span><span class="item_content" id="see_cuscoding"></span></div>
                    </div>
                    <div class="item">
                        <div class="item"><span class="item_title">地址：</span><span class="item_content" id="see_address" style="width: 600px"></span></div>
                        <div class="item"><span class="item_title">客户简称：</span><span class="item_content" id="see_cusfullName"></span></div>
                    </div>
                    <%--<div class="clear rowGap"><span class="ty-left" id="see_address"></span></div>--%>
                    <div class="specialOrgPart">最高负责人 <span id="see_supervisorName"></span> <span id="see_supervisorMobile"></span>（手机号）</div>
                    <div class="firstBuyTime"></div>
                    <div class="item">
                        <div class="item"><span class="item_title">创建者：</span><span class="item_content"><span id="see_createName"></span> <span id="see_createDate"></span></span></div>
                    </div>
                </div>
                <div class="part">
                    <div class="item">
                        <div class="item_title">全景照片：</div>
                        <div class="item_content" id="overallImgUpload"></div>
                    </div>
                    <div class="item">
                        <div class="item_title">产品图片：</div>
                        <div class="item_content" id="productImgUpload"></div>
                    </div>
                    <div class="item">
                        <div class="item">
                            <div class="item_title">首次接触时间：</div>
                            <div class="item_content" id="see_firstContactTime"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">首次接触地点：</div>
                            <div class="item_content" id="firstContactAddress"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">信息获取渠道：</div>
                            <div class="item_content" id="infoSource"></div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item_title">备注：</div>
                        <div class="item_content" id="see_memo"></div>
                    </div>

                </div>
                <div class="part">
                    <div class="item">
                        <div class="item_theme"><span class="title">开票信息</span></div>
                        <div class="right_btn">
                            <span style="display:inline-block; margin-right:100px; ">客户对发票方面要求的设置: <span id="see_invoiceRequire"></span></span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item">
                            <div class="item_title">公司名称：</div>
                            <div class="item_content" id="see_invoiceName"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">地址：</div>
                            <div class="item_content" id="see_invoiceAddress"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">电话：</div>
                            <div class="item_content" id="see_phone"></div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item">
                            <div class="item_title">开户行：</div>
                            <div class="item_content" id="see_bank"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">账号：</div>
                            <div class="item_content" id="see_bankNo"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">税号：</div>
                            <div class="item_content" id="taxpayerID"></div>
                        </div>
                    </div>
                </div>
                <div class="part_end">
                    <%--<div class="ty-alert ty-alert-warning">联系人、收货信息或发票邮寄信息中，红色字体者均为被停用的。</div>--%>
                    <div class="item_avatar">
                        <div class="item">
                            <div class="item_theme"><span class="title">联系人</span></div>
                            <div class="item_content">
                                该客户现共有如下<span class="contactNum"></span>位联系人
                                <div class="see_contactList"></div>
                            </div>
                        </div>
                    </div>
                    <div class="item_avatar">
                        <div class="item">
                            <div class="item_theme"><span class="title">合同信息</span></div>
                        </div>
                        <div class="item">
                            <div class="item_title"></div>
                            <div class="item_content">
                                <table class="ty-table ty-table-control contractPlaceList">
                                    <thead>
                                    <tr>
                                        <td>合同编号</td>
                                        <td>签署日期</td>
                                        <td>合同的有效期</td>
                                        <td>本合同下的商品</td>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="item_avatar">
                        <div class="item">
                            <div class="item_theme"><span class="title">收货信息</span></div>
                        </div>
                        <div class="item">
                            <div class="item_title"></div>
                            <div class="item_content">
                                <table class="ty-table ty-table-control recivePlaceList">
                                    <thead>
                                    <tr>
                                        <td width="10%">序号</td>
                                        <td width="30%">收货地址</td>
                                        <td width="20%">收货人</td>
                                        <td width="20%">收货电话</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="item_avatar">
                        <div class="item">
                            <div class="item_theme"><span class="title">发票邮寄信息</span></div>
                        </div>
                        <div class="item">
                            <div class="item_title"></div>
                            <div class="item_content">
                                <table class="ty-table ty-table-control mailPlaceList">
                                    <thead>
                                    <tr>
                                        <td width="10%">序号</td>
                                        <td width="30%">邮寄地址</td>
                                        <td width="20%">发票接收人</td>
                                        <td width="20%">邮政编码</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>处理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <div id="picShow" style="display: none;">
                        <img src=""/>
                    </div>
                    <div class="main">
                        <div class="tips ty-alert"></div>
                        <table class="kj-table" id="trialInfo">
                            <tbody>
                            <tr>
                                <td>机构名称</td>
                                <td><span class="trial_fullName"></span> <span class="link-blue cusDetailBtn" onclick="sale_seebtn($(this))">详情</span><div class="hd"><span class="cusProId"></span></div></td>
                                <td>超管手机</td>
                                <td><span class="trial_supervisorMobile"></span></td>
                            </tr>
                            <tr>
                                <td>机构简称</td>
                                <td><span class="trial_name"></span></td>
                                <td>超管姓名</td>
                                <td><span class="trial_supervisorName"></span></td>
                            </tr>
                            <tr>
                                <td>数据存储地点</td>
                                <td><span class="trial_uploadStorageType"></span></td>
                                <td>所选产品</td>
                                <td><span class="trial_packageName"></span></td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="processList"></div>
                        <div class="approveBtn">
                            <button class="ty-btn ty-btn-red ty-btn-big ty-circle-3" type="btn" data-name="reject">驳回</button>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="btn" data-name="agree">批准</button>
                        </div>
                        <div>
                            <%--产品详情--%>
                            <div id="mainProductInfo">
                                <h4><span class="pdName"></span></h4>
                                <div class="item-flex">
                                    <div class="item-title">所选模板：</div>
                                    <div class="item-content"><span class="pdModel"></span><div class="ty-right">操作人 <span class="pdTime"></span></div></div>
                                </div>
                                <div class="ty-alert">
                                    已重命名模块的数量 <b class="renameNumber"></b> 个
                                </div>
                                <table class="kj-table tbl_see_rename" style="width: 70%">
                                    <thead>
                                    <tr>
                                        <td>原名称</td>
                                        <td>新名称</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div class="ty-alert">主套餐内的模块</div>
                                <table class="kj-table kj-table-striped tbl_see_mainModule">
                                    <thead>
                                    <tr>
                                        <td>模块名称</td>
                                        <td>下辖的一级菜单数量</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div class="ty-alert" style="margin-top: 16px">
                                    使用本模板的用户增值功能的模块
                                </div>
                                <table class="kj-table kj-table-striped tbl_see_valueAddedModule">
                                    <thead>
                                    <tr>
                                        <td>模块名称</td>
                                        <td>下辖的一级菜单数量</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div class="ty-alert" style="margin-top: 16px">
                                    与本模板增值功能模块对应的已有套餐
                                    <div class="btn-group">
                                        <span class="link-blue" onclick="seeUsableSetMenuBtn('seeModel')">查看</span>
                                    </div>
                                </div>
                            </div>
                            <%--增值模块详情--%>
                            <div id="addServiceInfo">
                                <div class="ty-alert">增值服务</div>
                                <div>
                                    <table class="kj-table kj-table-striped tbl_asModule">
                                        <thead>
                                        <tr>
                                            <td>模块名称</td>
                                            <td>下辖的一级菜单数量</td>
                                            <td style="width: 80px">操作</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <table class="kj-table kj-table-striped tbl_asMpSet">
                                        <thead>
                                        <tr>
                                            <td>套餐名称</td>
                                            <td style="width: 80px">操作</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                            <%--增值模块修改详情--%>
                            <div id="addServiceChangeInfo">
                                <div class="ty-alert">增值服务的修改</div>
                                <div style="display: flex">
                                    <div class="compare_old" style="width: 620px">
                                        <table class="kj-table kj-table-striped tbl_asModule">
                                            <thead>
                                            <tr>
                                                <td>模块名称</td>
                                                <td>下辖的一级菜单数量</td>
                                                <td style="width: 80px">操作</td>
                                            </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <table class="kj-table kj-table-striped tbl_asMpSet hd">
                                            <thead>
                                            <tr>
                                                <td>套餐名称</td>
                                                <td style="width: 80px">操作</td>
                                            </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                    <div class="compare_icon" style="margin-top: 16px">
                                        <i class="fa fa-arrow-right"></i>
                                    </div>
                                    <div class="compare_new" style="width: 620px">
                                        <table class="kj-table kj-table-striped tbl_asModule">
                                            <thead>
                                            <tr>
                                                <td>模块名称</td>
                                                <td>下辖的一级菜单数量</td>
                                                <td style="width: 80px">操作</td>
                                            </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <table class="kj-table kj-table-striped tbl_asMpSet hd">
                                            <thead>
                                            <tr>
                                                <td>套餐名称</td>
                                                <td style="width: 80px">操作</td>
                                            </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="${pageContext.request.contextPath }/script/sales/customerManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="${pageContext.request.contextPath }/script/wonderssProduct/sysCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="${pageContext.request.contextPath }/script/sales/trialHandle.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
