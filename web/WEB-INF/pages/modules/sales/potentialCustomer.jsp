<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/sale.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/potential.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-red" id="mtTip" style="width: 400px;">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center;font-size:14px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--新增访谈记录--%>
    <div class="bonceContainer bounce-blue" id="newInterviewInfo" style="width: 900px">
        <div class="bonceHead">
            <span class="interviewTtl">新增访谈记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon recordForm" style="height:320px; overflow: auto;">
            <form id="addInterview" class="inputSize">
                <div class="addInterview">
                    <input type="hidden" id="editType" />
                    <input need type="hidden" data-name="id"  />
                    <table>
                        <tr>
                            <td>访谈日期：</td>
                            <td colspan="2">
                                <div><input type="text" need id="interviewDate" placeholder="请选择" data-name="interviewDate"></div>
                            </td>
                        </tr>
                        <tr>
                            <td>访谈对象：</td>
                            <td colspan="2">
                                <div class="flexBox">
                                    <div id="interviewer">尚未选择</div>
                                    <span class="hd"></span>
                                    <span class="linkBtn" data-fun="choosePotContact">选择</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>同行者（同事）：</td>
                            <td colspan="2">
                                <div class="flexBox">
                                    <div id="fellowTravelers">尚未选择</div>
                                    <span class="hd"></span>
                                    <span class="linkBtn" data-fun="fellowTravelers" data-target="#fellowTravelers">选择</span>
                                </div></td>
                        </tr>
                        <tr>
                            <td>同行者（非同事）：</td>
                            <td colspan="2">
                                <div class="flexBox">
                                    <div id="fellowUnColleague">尚未选择</div>
                                    <span class="hd"></span>
                                    <span class="linkBtn" data-fun="fellowUnColleague" data-target="fellowUnColleague">选择</span>
                                </div></td>
                        </tr>
                        <tr><td colspan="3"><hr/></td></tr>
                        <tr>
                            <td>访谈目标：</td>
                            <td colspan="2">
                                <div class="flexBox">
                                    <div class="careBlue">注：如必要，或公司有要求，请点击“编辑”！</div>
                                    <span class="linkBtn" data-fun="contentEntry" data-target="purpose">编辑</span>
                                </div>
                                <div class="purposeCon"></div>
                            </td>
                        </tr>
                        <tr class="interviewerContent">
                            <td>谈话要点：</td>
                            <td colspan="2">
                                <div class="flexBox">
                                    <div class="careBlue">注：如某些访谈对象或同行者发言较重要，请点击“新增”！</div>
                                    <span class="linkBtn" data-fun="addTalkingPoints" data-type="add">新增</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="gains">
                            <td>成果/结论：</td>
                            <td colspan="2">
                                <div class="flexBox">
                                    <div class="careBlue">注：如本次访谈有成果或结论，或公司有要求，请点击“新增”！</div>
                                    <span class="linkBtn" data-fun="contentEntry" data-target="gains" data-type="add">新增</span>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-blue ty-btn-big" id="newInterviewSure" data-type="new" data-name="addInterview">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 选择联系人--%>
    <div class="bonceContainer bounce-blue" id="choosePotContact" style="width: 450px">
        <div class="bonceHead">
            <span>选择客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="background:#f0f8ff ">
            <input type="hidden" id="target">
            <%--<div class="p0">
                <p style='text-align:center;'>暂无客户数据，请通过新增添加</p>
            </div>
            <div class="p1">
                <p>以下为可供选择的客户联系人</p>
                <ul class="cusList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="linkBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>--%>
            <div class="wrapBody">
                <div class="posRt"><span class="linkBtn" onclick="addInterviewer()">新增访谈对象</span></div>
                <p>请选择本次访谈的访谈对象（可多选）</p>
                <ul class="cusList viewsList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="linkBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="choosePotContactOk()">提交</button>
        </div>
    </div>
        <%-- 选择同行者（同事）--%>
        <div class="bonceContainer bounce-blue" id="chooseFellow" style="width: 650px">
            <div class="bonceHead">
                <span>选择同行者（同事）</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <input type="hidden" class="target">
                <div class="wrapBody">
                    <p>请选择本次访谈的同行者（同事）（可多选）</p>
                    <ul class="fellowList viewsList">
                        <li>
                            <i class="fa fa-circle-o"></i>
                            <span>姓名</span>
                            <span>职位的前八个字</span>
                            <span>15200000000</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseFellowOk()">确定</button>
            </div>
        </div>
        <%-- 选择同行者（非同事）--%>
        <div class="bonceContainer bounce-blue" id="chooseFellowUn" style="width: 650px">
            <div class="bonceHead">
                <span>选择同行者（非同事）</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <input type="hidden" class="target">
                <div class="narrowA">
                    <span class="ty-right linkBtn" data-fun="addOtherTogether">录入同事外的人</span>
                    <p>本次访谈的同行者（非同事）共0人</p>
                    <ul class="fellowList viewsList">
                    </ul>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseFellowUnOk()">确定</button>
            </div>
        </div>
        <%--访谈目标--%>
        <div class="bonceContainer bounce-blue" id="contentEntry" style="width:650px">
            <div class="bonceHead">
                <span>访谈目标</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="aBody">
                    <input type="hidden" class="target">
                    <div class="hd" id="storageData"></div>
                    <div class="widBt clear">
                        <div class="ty-left">
                            <span class="headTip con0">请录入本次访谈的访谈目标</span>
                            <span class="headTip con1">请录入本次访谈的成果/结论</span>
                        </div>
                        <span class="ty-right redLinkBtn" data-fun="purposeDel">清空内容</span>
                    </div>
                    <div class="purposeEntry entryCon">
                        <textarea placeholder="请录入" rows="6" maxlength="200" onkeyup="countWords($(this),200)"></textarea>
                        <p class="posRt textMax">0/200</p>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="contentEntryOk()">确定</span>
            </div>
        </div>
        <%-- 谈话要点--%>
        <div class="bonceContainer bounce-blue" id="addTalkingPoints" style="width: 650px">
            <div class="bonceHead">
                <span>谈话要点</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="aBody">
                    <div class="widBt">
                        <span class="red">*</span>发言者：
                        <select id="participant"></select>
                    </div>
                    <p>
                        <span class="red">*</span>请录入该发言者的谈话要点
                        <span class="ty-right redLinkBtn" data-fun="purposeDel">清空内容</span>
                    </p>
                    <div class="pointEntry entryCon">
                        <textarea placeholder="请录入" rows="10" maxlength="200" onkeyup="countWords($(this),200)"></textarea>
                        <p class="posRt textMax">0/200</p>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addTalkingPointsOk()">确定</button>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
    <%--新增联系人--%>
    <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
        <div class="bonceHead">
            <span>联系人</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <div id="contactSource">联系人标签  <span></span>访谈</div>
            <form id="newContectData">
                <p>
                    <span class="sale_ttl1">姓名：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                </p>
                <p>
                    <span class="sale_ttl1">手机：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactNumber" require/></span>
                    <a class="addMore" onclick="addMore($(this))">添加更多联系方式</a>
                    <select style="display: none;width: 167px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                        <option value="0"></option>
                        <option value="1">手机</option>
                        <option value="2">QQ</option>
                        <option value="3">Email</option>
                        <option value="4">微信</option>
                        <option value="5">微博</option>
                        <option value="9">自定义</option>
                    </select>
                </p>
                <ul class="otherContact">
                </ul>
                <div id="contactsCard">
                    <span class="sale_ttl1">名片：</span>
                    <div id="uploadCard" class="cardUploadBtn"></div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addContact" data-name="addContact">提交</button>
        </div>
    </div>
        <%--录入同事外的人--%>
        <div class="bonceContainer bounce-blue" id="addOtherTogether" style="width: 450px">
            <div class="bonceHead">
                <span>录入同事外的人</span>
                <a class="bounce_close" onclick="chargeXhr($(this))"></a>
            </div>
            <div class="bonceCon">
                <div class="narrowA">
                    <p>请录入本次访谈与您同行的同事外的人员</p>
                    <div class="itemC">
                        <p>姓名<i class="xing"></i></p>
                        <input type="text" id="otherTogetherName" class="form-control" placeholder="请录入" name="name" require />
                    </div>
                    <div class="itemC">
                        <p>所在公司或组织</p>
                        <input type="text" class="form-control" placeholder="请录入" name="company" require />
                    </div>
                    <div class="itemC">
                        <p>职位</p>
                        <input type="text" class="form-control" placeholder="请录入" name="post" require />
                    </div>
                    <div class="itemC">
                        <p>手机
                            <span class="ty-right">
                                <a class="addMore" onclick="addMore($(this))">添加更多联系方式</a>
                            <select style="display: none;width: 167px;" class="addMoreContact" onchange="addMoreContact($(this))" value="0">
                                <option value="0"></option>
                                <option value="1">手机</option>
                                <option value="2">QQ</option>
                                <option value="3">Email</option>
                                <option value="4">微信</option>
                                <option value="5">微博</option>
                                <option value="9">自定义</option>
                            </select>
                            </span>
                        </p>
                        <input type="text" class="form-control contract_endTime" placeholder="请录入" name="mobile" require />
                        <ul class="otherContact">
                        </ul>
                    </div>
                    <div class="itemC">
                        <span>名片：<span class="ty-right cardUploadBtn"></span></span>
                        <div class="businessCard"></div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="chargeXhr($(this))">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addOtherTogetherSure()">使用</button>
            </div>
        </div>
</div>
<div class="bounce_Fixed3">
    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" maxlength="20"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
    <%--联系方式查看--%>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签：</span>
                    <span class="sale-con" id="see_contactTag">潜在客户期间录入</span>
                </div>
            </div>
            <%--<p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>--%>
            <ul class="see_otherContact" style="width:442px">
            </ul>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
        <%-- 客户名称更换提示--%>
        <div class="bonceContainer bounce-blue" id="delTip">
            <div class="bonceHead">
                <span>提示</span>
                <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="aBody ty-center">
                    <div>确定删除该条数据？</div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">取 消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="delOtherOk()">确定</button>
            </div>
        </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>潜在客户</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <div id="picShow" style="display: none;">
                        <img src=""/>
                    </div>
                    <form id="plCustomerInput" class="inputMain">
                        <div class="">
                            <p>基本信息</p>
                            <div class="sale_c">
                                <div class="sale_ttl1"><span class="ty-color-red">*</span>客户名称：</div>
                                <div class="sale_con1"><input type="text" id="add_cusname" data-name="fullName" placeholder="请录入"></div>
                                <div class="sale_ttl1">地址：</div>
                                <div class="sale_con2"><input type="text" id="add_address1" data-name="address" placeholder="请录入"></div>
                            </div>
                            <div class="sale_c isSpecial" style="display: none;">
                                <div class="sale_ttl1">最高负责人：</div>
                                <div class="sale_con1"><input type="text" data-name="supervisorName" placeholder="请录入"></div>
                                <div class="sale_ttl1">手机：</div>
                                <div class="sale_con2"><input type="text" data-name="supervisorMobile" placeholder="请录入"></div>
                            </div>
                            <div class="sale_c">
                                <div class="sale_ttl1">全景照片：</div>
                                <div class="imgWall" id="qImages">
                                    <div id="panoramaBtn" class="saleUploadBtn"></div>
                                </div>
                            </div>
                            <div class="sale_c">
                                <div class="sale_ttl1">产品图片：</div>
                                <div class="imgWall" id="pImages">
                                    <div id="productPicsBtn" class="saleUploadBtn"></div>
                                </div>
                            </div>
                            <div class="sale_c">
                                <div class="sale_ttl1">首次接触日期：</div>
                                <div class="sale_con1">
                                    <input id="firstContactTime" placeholder="请选择" value=""/>
                                </div>
                                <div class="sale_ttl1">首次接触地点：</div>
                                <div class="sale_con1">
                                    <input data-name="firstContactAddress" type="text" placeholder="请录入"/>
                                </div>
                                <div class="sale_ttl1">信息获取渠道：</div>
                                <div class="sale_con1">
                                    <input data-name="infoSource" type="text" value="" placeholder="请录入">
                                </div>
                            </div>
                            <div class="addOtherInfo">
                                <span class="par_ttl">联系人</span>
                                <span id="newContact" class="ty-btn ty-btn-big ty-btn-blue node" data-type="new" data-name="contactInfo">新增</span>
                                <div class="dataList contectList">
                                </div>
                            </div>
                            <div class="addOtherInfo">
                                <span class="par_ttl">访谈记录</span>
                                <span id="newInterview" class="ty-btn ty-btn-big ty-btn-blue node" data-type="new" data-name="interviewInfo">新增</span>
                                <div class="dataList interviewList"></div>
                            </div>
                        </div>
                    </form>
                    <div class="tjBtn">
                        <button class="ty-btn ty-btn-big ty-btn-blue" disabled id="addAccountBtn" onclick="cunstomerInputSure()">提交</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/sales/potential.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
