<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
 <link href="../css/sales/invoiceServise.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%-- 一级提示框 --%>
    <div class="bonceContainer bounce-red" id = "tip" >
        <div class="bonceHead" >
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tip" id="tipMs"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5 request" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 发票核对 --%>
    <div class="bonceContainer bounce-green" id = "invoiceCheck" style="min-width: 900px;">
        <div class="bonceHead">
            <span>发票核对</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p class="checkInit">请逐张核对您手中的发票与开票申请单内信息是否一致。</p>
                <p class="checkDiff">核对结果为红字的发票存在问题。您需妥善处理完毕后，并重新核对无误后，再将发票发出给客户。</p>
                <p class="checkSame">经核对，您未发现发票有问题。您可将发票发出给客户，并在系统中记录。</p>
            </div>
            <div class="resultInit">
                <table class="ty-table ty-table-control initList">
                    <thead>
                    <tr>
                        <td>发票种类</td>
                        <td>发票号码</td>
                        <td>发票金额</td>
                        <td>操作</td>
                        <td class="checkResult">核对结果</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <ul class="hd">
                    <li>
                        <p>客户名称不一致的原因分析：</p>
                        <p>1  拿错发票</p>
                        <p>2  财务人员在本系统选择发票号码时选错</p>
                    </li>
                    <li>
                        <p>商品信息不一致的原因分析：</p>
                        <p>1  拿错发票</p>
                        <p>2  财务人员开票时选错商品或录错数量</p>
                        <p>3  本系统内单价与财务开票系统内单价不一致</p>
                    </li>
                    <li>
                        <p>客户名称不一致的原因分析：</p>
                        <p>1  开票时漏开了货物</p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot conCenter">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 checkSame hd" onclick="bounce.cancel()">确定</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 checkDiff hd" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 发票发出登记 --%>
    <div class="bonceContainer bounce-green" id="issueRegist" style="min-width: 750px;">
        <div class="bonceHead">
            <span>发票发出登记</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>客户名称：<span class="reg-customer"></span></p>
                <div class="allElect">
                    <p>请您选择您要发出的发票</p>
                    <p>
                        <span id="checkAll"><i class="fa fa-square-o" onclick="checkAllBtn($(this))"></i>全选</span>
                    </p>
                </div>
                <table class="ty-table ty-table-control" id="invoicesCheck">
                    <thead>
                    <tr>
                        <td>发票种类</td>
                        <td>发票号码</td>
                        <td>发票金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="rowCl">
                    <div class="rowTtl"><span>发票接收人</span></div>
                    <div class="rowCon">
                        <select id="receiver">
                            <option>请选择</option>
                        </select>
                    </div>
                </div>
                <div class="rowCl">
                    <div class="rowTtl"><span>发票的发出方式</span></div>
                    <div class="deliveType send-deliveType">
                        <div class="deliveRadio" id="invoiceDeliveType">
                            <span check="0" onclick="setDeliveType($(this),1)">
                                <i class="fa fa-square-o" type_express="A"></i>快递
                            </span>
                            <span check="0" onclick="setDeliveType($(this),2)">
                                <i class="fa fa-square-o" type_express="B"></i>随身携带
                            </span>
                        </div>
                        <div class="type-express hd">
                            <span check="0" id="type_express_alone" onclick="setDeliveType($(this),3)"><i class="fa fa-square-o" type="1"></i>单独寄出</span>
                            <span check="0" id="type_express_togethor" onclick="setDeliveType($(this),4)"><i class="fa fa-square-o" type="2"></i>与其他物品共同寄出</span>
                        </div>
                    </div>
                </div>
                <div id="deliverCon">
                    <div class="type_express_alone rowCl hd">
                        <div class="rowTtl_d"><span>快递公司</span></div>
                        <div class="rowCon">
                            <input id="expressCompany" class="long1" type="text"/>
                        </div>
                        <div class="rowTtl_d"><span>快递单号</span></div>
                        <div class="rowCon">
                            <input id="expressSn" class="long2" type="text"/>
                        </div>
                        <div class="rowTtl_d"><span>邮寄日期</span></div>
                        <div class="rowCon">
                            <input class="long3" id="mailDate"/>
                        </div>
                    </div>
                    <div class="type_carry rowCl hd">
                        <div class="rowTtl_d"><span>携带者</span></div>
                        <div class="rowCon">
                            <input id="carryer" class="long1" type="text"/>
                        </div>
                        <div class="rowTtl_d"><span>联系方式</span></div>
                        <div class="rowCon">
                            <input id="carryerCall" class="long2" type="text"/>
                        </div>
                        <div class="rowTtl_d"><span>移交日期</span></div>
                        <div class="rowCon">
                            <input class="long3" id="carryDate"/>
                        </div>
                    </div>
                    <div class="type_express_alone rowCl hd">
                        <div class="rowTtl_d"><span>备注</span></div>
                        <div class="rowCon long4">
                            <input id="sign_mome" type="text"/>
                        </div>
                    </div>
                    <div class="rowCl hd">
                        <div class="rowTtl"><span>请选择快递单号</span></div>
                        <div class="rowCon">
                            <select id="togetherWith">
                                <option>请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="hd togetherInfo">
                        <div class="rowTtl_d"><span>快递公司</span></div>
                        <div class="rowCon">
                            <span id="with_company" class="long1 cells">快递公司</span>
                        </div>
                        <div class="rowTtl_d"><span>快递单号</span></div>
                        <div class="rowCon">
                            <span id="with_expressSn" class="long2 cells">快递公司</span>
                        </div>
                        <div class="rowTtl_d"><span>邮寄日期</span></div>
                        <div class="rowCon">
                            <span class="long3 cells" id="with_mailDate"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="sendRegistSure" onclick="sendRegistSure()">确定</button>
        </div>
    </div>
    <%-- 修改发票发出登记 --%>
    <div class="bonceContainer bounce-green" id="issueRegistUpdate" style="min-width: 750px;">
        <div class="bonceHead">
            <span>修改发票发出登记</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p id="customeName"></p>
                <div class="allElect">
                    <p>请选择您要发出的发票</p>
                </div>
                <table class="ty-table ty-table-control" id="invoicesCheckUpdate">
                    <thead>
                    <tr>
                        <td>发票种类</td>
                        <td>发票号码</td>
                        <td>发票金额</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div class="rowCl">
                    <div class="rowTtl"><span>发票接收人</span></div>
                    <div class="rowCon">
                        <select id="reciverUpdate">
                            <option>请选择</option>
                        </select>
                    </div>
                </div>
                <div class="rowCl">
                    <div class="rowTtl"><span>发票的发出方式</span></div>
                    <div class="deliveType">
                        <div class="deliveRadio" id="updateDeliveType">
                            <span check="0" onclick="setDeliveTypeUpdate($(this),1)">
                                <i class="fa fa-square-o" up_express="A"></i>快递
                            </span>
                            <span check="0" onclick="setDeliveTypeUpdate($(this),2)">
                                <i class="fa fa-square-o" up_express="B"></i>随身携带
                            </span>
                        </div>
                        <div class="type-expressUpdate hd">
                            <span check="0" onclick="setDeliveTypeUpdate($(this),3)"><i class="fa fa-square-o" val="1"></i>单独寄出</span>
                            <span check="0" onclick="setDeliveTypeUpdate($(this),4)"><i class="fa fa-square-o" val="2"></i>与其他物品共同寄出</span>
                        </div>
                    </div>
                </div>
                <div id="deliverConUpdate">
                    <div class="type_express_alone rowCl hd">
                        <div class="rowTtl_d"><span>快递公司</span></div>
                        <div class="rowCon">
                            <input id="up_company" class="long1" type="text"/>
                        </div>
                        <div class="rowTtl_d"><span>快递单号</span></div>
                        <div class="rowCon">
                            <input id="up_sn" class="long2" type="text"/>
                        </div>
                        <div class="rowTtl_d"><span>邮寄日期</span></div>
                        <div class="rowCon">
                            <input class="long3" id="mailDateUpdate"/>
                        </div>
                    </div>
                    <div class="type_carry rowCl hd">
                        <div class="rowTtl_d"><span>携带者</span></div>
                        <div class="rowCon">
                            <input id="up-carrier" class="long1" type="text"/>
                        </div>
                        <div class="rowTtl_d"><span>联系方式</span></div>
                        <div class="rowCon">
                            <input id="up-call" class="long2" type="text"/>
                        </div>
                        <div class="rowTtl_d"><span>移交日期</span></div>
                        <div class="rowCon">
                            <input class="long3" id="carryDateUpdate"/>
                        </div>
                    </div>
                    <div class="type_express_alone rowCl hd">
                        <div class="rowTtl_d"><span>备注</span></div>
                        <div class="rowCon long4">
                            <input id="up-memo" type="text"/>
                        </div>
                    </div>
                    <div class="rowCl hd">
                        <div class="rowTtl_d"><span>请选择快递单号</span></div>
                        <div class="rowCon">
                            <select id="edit-together">
                                <option>请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="hd editTogetherInfo">
                        <div class="rowTtl_d"><span>快递公司</span></div>
                        <div class="rowCon">
                            <span id="with_companyEdit" class="long1 cells"></span>
                        </div>
                        <div class="rowTtl_d"><span>快递单号</span></div>
                        <div class="rowCon">
                            <span id="with_expressSnEdit" class="long2 cells"></span>
                        </div>
                        <div class="rowTtl_d"><span>邮寄日期</span></div>
                        <div class="rowCon">
                            <span class="long3 cells" id="with_mailDateEdit"></span>
                        </div>
                    </div>
                </div>
                <div class="rowCl recordLine">
                    <div class="rowTtl_d"><span>操作记录：</span></div>
                    <div class="rowCon">
                        <span class="record-date"></span>
                        <span class="record-operate"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="invoiceIssueUpdate" onclick="updateSure()">确定</button>
        </div>
    </div>
    <%-- 待签收--发票发出信息修改记录 --%>
    <div class="bonceContainer bounce-green" id = "issueUpdateRecordList" style="min-width: 520px;">
        <div class="bonceHead">
            <span>发票发出信息修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td>修改时间</td>
                        <td>修改人</td>
                        <td>修改前</td>
                        <td>修改后</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--签收登记--%>
    <div class="bonceContainer bounce-green" id = "signRegister" style="min-width: 900px;">
        <div class="bonceHead">
            <span>发票签收登记</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div class="rowLine"><span class="item-ttl">客户名称：</span><span id="sign-customer"></span></div>
            <div class="rowLine">
                <div class="rowInr"><span class="item-ttl">发票接收人</span><span class="sign-receiver">陆逊</span></div>
                <div class="rowInr"><span class="item-ttl">发票发出方式</span><span class="sign-sendType">快递-单独寄出</span></div>
            </div>
            <div>
                <div class="rowLine sign-express">
                    <div class="rowInr1"><span class="item-ttl">快递公司</span><span class="busNote sign-bus">申通</span></div>
                    <div class="rowInr1"><span class="item-ttl">快递单号</span><span class="numNote sign-sn">392999000390</span></div>
                    <div class="rowInr1"><span class="item-ttl">邮寄日期</span><span class="dateNote sign-sendDate">2018-12-24</span></div>
                </div>
                <div class="rowLine sign-carryWay">
                    <div class="rowInr1"><span class="item-ttl">携带者</span><span class="busNote sign-carryer"></span></div>
                    <div class="rowInr1"><span class="item-ttl">联系方式</span><span class="numNote sign-carryerCall"></span></div>
                    <div class="rowInr1"><span class="item-ttl">移交日期</span><span class="dateNote sign-carryDate"></span></div>
                </div>
            </div>
            <div class="rowLine">
                <span class="item-ttl">备注</span><span class="sign-notice">快递员电话：12545875252</span>
            </div>
            <div class="rowLine">
                <span class="item-ttl">签收记录</span>
                <span id="signOperate">
                      <span check="0" onclick="sign($(this))"><i class="fa fa-circle-o"></i>已全部正常签收</span>
                </span>
            </div>
            <table class="ty-table ty-table-control signSet">
                <thead>
                <tr>
                    <td>发票种类</td>
                    <td>发票号码</td>
                    <td>发票金额</td>
                    <td>正常签收</td>
                    <td>未能签收</td>
                    <td>未能签收原因</td>
                    <td>处理办法</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div class="signInforEntry">
                <div class="rowLine">
                    <div class="rowInr"><span class="item-ttl">实际收件人<span class="ty-color-red">*</span></span><input id="signatory" type="text" placeholder="请录入签收人"/></div>
                    <div class="rowInr"><span class="item-ttl xing">签收日期<span class="ty-color-red">*</span></span><input id="signDate" type="text" placeholder="请选择"/></div>
                </div>
                <div class="rowLine">
                    <span class="item-ttl posTop">其他记录</span>
                    <textarea id="otherInfo" class="otherInfo"></textarea>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="signForReceive" onclick="signForSure()">确定</button>
        </div>
    </div>
    <%--未全部正常签收 确定后提示--%>
    <div class="bonceContainer bounce-red" id = "notAllSignTip" >
        <div class="bonceHead" >
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>如客户由于各种原因拒收发票，请以最快速度回收发票并交财务，以免产生不必要的税费；</p>
            <p>如发票丢失，则请及时告知财务及上级领导，以妥善处理。</p>
            <p>未能签收的发票中，选择了重新开票的，相应货物已经回到了待开票申请状态。您需重新填写开票申请。</p>
            <p>选择了不予重新开票的，您若想再次申请开票，则需先在发票签收记录中修改为需重新开发票。</p>
        </div>
        <div class="bonceFoot" >
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="notAllSignTip()">确定</span>
        </div>
    </div>
    <%--发票签收记录 - 查看 --%>
    <div class="bonceContainer bounce-green" id = "signedSee" style="min-width: 900px;">
        <div class="bonceHead">
            <span>发票签收登记查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div class="rowLine"><span class="item-ttl">客户名称：</span><span class="see-customer"></span></div>
            <div class="rowLine">
                <div class="rowInr"><span class="item-ttl">发票接收人</span><span class="see-receiver"></span></div>
                <div class="rowInr"><span class="item-ttl">发票发出方式</span><span class="see-sendWay"></span></div>
            </div>
            <div>
                <div class="rowLine see-express">
                    <div class="rowInr1"><span class="item-ttl">快递公司</span><span class="busNote see-sendBus"></span></div>
                    <div class="rowInr1"><span class="item-ttl">快递单号</span><span class="numNote see-sendSn"></span></div>
                    <div class="rowInr1"><span class="item-ttl">邮寄日期</span><span class="dateNote see-sendDate"></span></div>
                </div>
                <div class="rowLine see-carryWay">
                    <div class="rowInr1"><span class="item-ttl">携带者</span><span class="busNote see-carryer"></span></div>
                    <div class="rowInr1"><span class="item-ttl">联系方式</span><span class="numNote see-carryerCall"></span></div>
                    <div class="rowInr1"><span class="item-ttl">移交日期</span><span class="dateNote see-carryDate"></span></div>
                </div>
            </div>
            <div class="rowLine">
                <span class="item-ttl">备注</span><span class="see-notice">快递员电话：12545875252</span>
            </div>
            <table class="ty-table ty-table-control signRecordSee">
                <thead>
                <tr>
                    <td>发票种类</td>
                    <td>发票号码</td>
                    <td>发票金额</td>
                    <td>正常签收</td>
                    <td>未能签收</td>
                    <td>未能签收原因</td>
                    <td>处理办法</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div>
                <div class="rowLine">
                    <div class="rowInr"><span class="item-ttl">实际收件人</span><span class="see-signer">马良</span></div>
                    <div class="rowInr"><span class="item-ttl">签收日期</span><span class="see-signDate"></span></div>
                </div>
                <div class="rowLine">
                    <span class="item-ttl posTop">其他记录</span>
                    <span class="see-memo"></span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--发票签收记录 - 修改 --%>
    <div class="bonceContainer bounce-green" id = "signedUpdateReason" >
        <div class="bonceHead" >
            <span>修改发票签收信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" id="updateReason">
            <p onclick="updateReason($(this))"><i class="fa fa-circle-o" value="1"></i>之前操作失误，该组发票实际尚未被签收</p>
            <p onclick="updateReason($(this))"><i class="fa fa-circle-o" value="2"></i>其它修改</p>
        </div>
        <div class="bonceFoot" >
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="signedUpdateBtn" onclick="signedUpdate()">确定</button>
        </div>
    </div>
    <%--发票签收记录 - 修改签收登记 --%>
    <div class="bonceContainer bounce-green" id = "signRegisterUpdate" style="min-width: 930px;">
        <div class="bonceHead">
            <span>修改发票签收登记</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div class="rowLine"><span class="item-ttl">客户名称：</span><span class="signUp-customer"></span></div>
            <div class="rowLine">
                <div class="rowInr"><span class="item-ttl">发票接收人</span><span class="signUp-receiver"></span></div>
                <div class="rowInr"><span class="item-ttl">发票发出方式</span><span class="signUp-sendWay"></span></div>
            </div>
            <div>
                <div class="rowLine signUp-express">
                    <div class="rowInr1"><span class="item-ttl">快递公司</span><span class="busNote signUp-sendBus"></span></div>
                    <div class="rowInr1"><span class="item-ttl">快递单号</span><span class="numNote signUp-sendSn"></span></div>
                    <div class="rowInr1"><span class="item-ttl">邮寄日期</span><span class="dateNote signUp-sendDate"></span></div>
                </div>
                <div class="rowLine signUp-carryWay">
                    <div class="rowInr1"><span class="item-ttl">携带者</span><span class="busNote signUp-carryer"></span></div>
                    <div class="rowInr1"><span class="item-ttl">联系方式</span><span class="numNote signUp-carryerCall"></span></div>
                    <div class="rowInr1"><span class="item-ttl">移交日期</span><span class="dateNote signUp-carryDate"></span></div>
                </div>
            </div>
            <div class="rowLine">
                <span class="item-ttl">备注</span><span class="signUp-memo"></span>
            </div>
            <div class="rowLine">
                <span class="item-ttl">签收记录</span>
                <span id="editOperate">
                  <span><i class="fa fa-circle-o"></i>已全部正常签收</span>
            </span>
            </div>
            <table class="ty-table ty-table-control" id="signedUpdate">
                <thead>
                <tr>
                    <td>发票种类</td>
                    <td>发票号码</td>
                    <td>发票金额</td>
                    <td>正常签收</td>
                    <td>未能签收</td>
                    <td>未能签收原因</td>
                    <td>处理办法</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div class="signInforEntryEidt">
                <div class="rowLine">
                    <div class="rowInr"><span class="item-ttl">实际收件人<span class="ty-color-red">*</span> </span><input id="signUp-signer" type="text" placeholder="请录入签收人"/></div>
                    <div class="rowInr"><span class="item-ttl">签收日期<span class="ty-color-red">*</span> </span><input id="signDateUpdate" type="text" placeholder="请选择"/></div>
                </div>
                <div class="rowLine">
                    <span class="item-ttl posTop">其他记录</span>
                    <textarea id="signUp-other" class="otherInfo"></textarea>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="signedUpdateSure" onclick="signedUpdateSure()">确定</button>
        </div>
    </div>
    <%--发票签收记录 - 修改记录列表 --%>
    <div class="bonceContainer bounce-green" id = "signedUpdateRecord" >
        <div class="bonceHead" >
            <span>发票签收信息修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control" id="signedRecordList">
                <thead>
                <tr>
                    <td>修改时间</td>
                    <td>修改人</td>
                    <td>修改前</td>
                    <td>修改后</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot" >
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class = "bounce_Fixed" >
    <%--  发票详情核对 --%>
    <div class="bonceContainer bounce-green" id = "invoiceDetailCheck" style="min-width: 900px;">
        <div class="bonceHead">
            <span>发票核对</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <p>下表信息取自于您提交的开票申请。</p>
            <p>发票发出给客户前，建议逐项确认发票内的各项信息与下表是否一致。</p>
            <div>
                <table class="ty-table tr-table-control">
                    <thead>
                    <tr>
                        <td colspan="4">项目</td>
                        <td>检查结果</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr class="partOne">
                        <td colspan="4" class="sec-ttl partOneCon"></td>
                        <td>
                            <button class="ty-btn" org="1" onclick="checkResult($(this), 1)">无问题</button>
                            <button class="ty-btn" org="2" onclick="checkResult($(this), 0)">发票不对</button>
                        </td>
                    </tr>
                    <tr class="partTwo">
                        <td class="sec-ttl">二 客户名称</td>
                        <td colspan="3" class="sec-ttl partTwoCustorm"></td>
                        <td>
                            <button class="ty-btn check-sign" choose="1" onclick="checkResult($(this))">一致</button>
                            <button class="ty-btn check-sign" choose="0" onclick="checkResult($(this))">不一致</button>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="5" class="sec-ttl">三 商品信息</td>
                    </tr>
                    <tr class="partThrTable">
                        <td>货物或应税劳务、服务名称</td>
                        <td>规格型号”</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>检查结果</td>
                    </tr>
                    <tr class="partFour">
                        <td class="sec-ttl">四 发票金额</td>
                        <td colspan="3" class="partFourAmount"></td>
                        <td>
                            <button class="ty-btn check-sign" choose="1" onclick="checkResult($(this))">一致</button>
                            <button class="ty-btn check-sign" choose="0" onclick="checkResult($(this))">不一致</button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 miss" onclick="invoiceMissed()">本张发票已丢失</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5  hd finded" onclick="invoiceFinded()">本张发票找到了</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5 checkFinish" onclick="checkFinished()">本张发票核对完毕</button>
        </div>
    </div>
    <%--待签收  发票发出信息修改记录详情查看 --%>
    <div class="bonceContainer bounce-green" id = "noSignUpdateRecord" style="min-width: 840px;">
        <div class="bonceHead">
            <span id="noSignRecord">发票发出登记——修改前</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div class="rowLine"><span class="item-ttl">客户名称：</span><span id="his-customer"></span></div>
            <div class="rowLine">
                <div class="rowInr"><span class="item-ttl">发票接收人</span><span id="his-reciver"></span></div>
                <div class="rowInr"><span class="item-ttl">发票发出方式</span><span id="his-sendType"></span></div>
            </div>
            <div class="sendAllWay">
                <div class="rowLine expressWay">
                    <div class="rowInr1"><span class="item-ttl">快递公司</span><span class="busNote his-sendBus"></span></div>
                    <div class="rowInr1"><span class="item-ttl">快递单号</span><span class="numNote his-sendSn"></span></div>
                    <div class="rowInr1"><span class="item-ttl">邮寄日期</span><span class="dateNote his-sendDate"></span></div>
                </div>
                <div class="rowLine carryWay">
                    <div class="rowInr1"><span class="item-ttl">携带者</span><span class="busNote his-carryer"></span></div>
                    <div class="rowInr1"><span class="item-ttl">联系方式</span><span class="numNote his-carryerCall"></span></div>
                    <div class="rowInr1"><span class="item-ttl">移交日期</span><span class="dateNote his-carryDate"></span></div>
                </div>
            </div>
            <div class="rowLine">
                <div><span class="item-ttl">备注</span><span class="his-notice"></span></div>
            </div>
            <table class="lookDetailTb">
                <thead>
                <tr>
                    <td>发票种类</td>
                    <td>发票号码</td>
                    <td>发票金额</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--  发票签收记录 - 修改记录查看 --%>
    <div class="bonceContainer bounce-green" id = "signedUpdateRecordSee" style="min-width: 900px;">
        <div class="bonceHead">
            <span id="editLook">发票签收登记查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <div class="rowLine"><span class="item-ttl">客户名称：</span><span class="record-customer"></span></div>
            <div class="rowLine">
                <div class="rowInr"><span class="item-ttl">发票接收人</span><span class="record-receiver">陆逊</span></div>
                <div class="rowInr"><span class="item-ttl">发票发出方式</span><span class="record-sendWay">快递-单独寄出</span></div>
            </div>
            <div>
                <div class="rowLine record-expressWay">
                    <div class="rowInr1"><span class="item-ttl">快递公司</span><span class="busNote record-sendBus"></span></div>
                    <div class="rowInr1"><span class="item-ttl">快递单号</span><span class="numNote record-sendSn"></span></div>
                    <div class="rowInr1"><span class="item-ttl">邮寄日期</span><span class="dateNote record-sendDate"></span></div>
                </div>
                <div class="rowLine record-carryWay">
                    <div class="rowInr1"><span class="item-ttl">携带者</span><span class="busNote record-carryer"></span></div>
                    <div class="rowInr1"><span class="item-ttl">联系方式</span><span class="numNote record-carryerCall"></span></div>
                    <div class="rowInr1"><span class="item-ttl">移交日期</span><span class="dateNote record-carryDate"></span></div>
                </div>
            </div>
            <div class="rowLine">
                <span class="item-ttl">备注</span><span class="notice record-memo">快递员电话：12545875252</span>
            </div>
            <div class="rowLine">
                <span class="item-ttl">签收记录</span>
            </div>
            <table class="ty-table ty-table-control signedRecordSee">
                <thead>
                <tr>
                    <td>发票种类</td>
                    <td>发票号码</td>
                    <td>发票金额</td>
                    <td>正常签收</td>
                    <td>未能签收</td>
                    <td>未能签收原因</td>
                    <td>处理办法</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div class="signInforEntry">
                <div class="rowLine">
                    <div class="rowInr"><span class="item-ttl">实际收件人</span><span class="record-signer">马良</span></div>
                    <div class="rowInr"><span class="item-ttl">签收日期</span><span class="record-signDate"></span></div>
                </div>
                <div class="rowLine">
                    <span class="item-ttl posTop">其他记录</span><span class="record-other"></span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 发票丢失提示 --%>
    <div class="bonceContainer bounce-red" id="missedTip">
        <div class="bonceHead">
            <span class="noGs">！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p id="tipContent"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5 tipBtn" onclick="">我知道了</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>发票送达管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <ul class="ty-secondTab">
                    <li class="ty-active">待发出</li>
                    <li>待签收</li>
                    <li>发票签收记录</li>
                </ul>
                <div class="ty-mainData">
                    <table id="serviceList" class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td>申请开票时间</td>
                            <td>申请人</td>
                            <td>开票完成时间</td>
                            <td>开票人</td>
                            <td>客户名称</td>
                            <td>客户代号</td>
                            <td>订单号</td>
                            <td>发票总额</td>
                            <td>发票数量</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <table id="noSigned" class="hd ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td colspan="6">发票基本信息</td>
                            <td colspan="5">发票发出信息</td>
                            <td rowspan="2">操作</td>
                        </tr>
                        <tr>
                            <td>申请开票时间</td>
                            <td>申请人</td>
                            <td>客户名称</td>
                            <td>客户代号</td>
                            <td>发票总额</td>
                            <td>发票数量</td>
                            <td>发出方式</td>
                            <td>发出发票数量</td>
                            <td>发出日期</td>
                            <td>发票接收人</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <table id="signRecord" class="hd ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td colspan="6">发票基本信息</td>
                            <td colspan="4">发票发出信息</td>
                            <td colspan="2">发票签收信息</td>
                        </tr>
                        <tr>
                            <td>申请开票时间</td>
                            <td>申请人</td>
                            <td>客户名称</td>
                            <td>客户代号</td>
                            <td>发票总额</td>
                            <td>发票数量</td>
                            <td>发出方式</td>
                            <td>发出发票数量</td>
                            <td>发出日期</td>
                            <td>发票接收人</td>
                            <td>状态</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye_issued"></div>
                </div>
            </div>

        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/sales/invoiceServise.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
