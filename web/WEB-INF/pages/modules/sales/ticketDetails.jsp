<%--
  Created by IntelliJ IDEA.
  User: houxingzhe
  Date: 2023/6/12
  Time: 11:40
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
  <title>票据详情</title>
  <style>
    body{
      background: #fff;
    }

    .ty-invoiceTicket{ color: #d25f5f;  }
    .ty-invoiceTicket .ty-ticketName{     display: inline-block;
      margin: 20px auto; border-style: double;  border-color: #d25f5f; border-top:none;border-left:none;border-right:none; }
    .ty-ticketNameC{
      text-align: center;
    }
    .ty-ticketTab{
      width: 100%;  border-collapse: collapse;
    }
    .ty-ticketTab td.ty-ticket-txt-left{
      text-align: left;
    }
    .ty-ticketTab tr.ty-ticket-no-border td.ty-ticket-td-border{
      border-top: 1px solid #d25f5f ;
    }
    .ty-ticketTab tr.ty-ticket-no-border td{
      border-top: none;
      border-bottom: none;
    }
    .ty-ticketTab td.ty-ticket-txt-black{
      color: #666!important;
    }
    .ty-ticketTab td.ty-ticket-txt-blue{
      color: #0a9bee!important;
    }
    .ty-ticketTab td{
      padding: 5px;
      border: 1px solid #d25f5f; text-align: center;
    }

    .ty-ticket-ttl>span>span:nth-child(1){
      display: inline-block;
      width: 150px;
    }
    .ty-ticket-ttl>span>span:nth-child(2){
      color: #0a9bee!important;
      font-size: 0.9em;
    }
    #tabss{
      width: 980px;
      margin: 0 auto;
    }
    .gsListItem{
      font-size: 0.9em;
    }
    .floatRight{
      position: absolute;
      right: 100px;
      top: 32px;
      text-align: left;
      font-size: 0.9em;
    }
    .invNo,.invDate,.sumAll,.sumAll2, .sum2ttl{ color: #0a9bee!important; }
    .sum2ttl{ margin-left: 100px; }
  </style>
</head>
<body>
<div class="ty-invoiceTicket">
  <div class="ty-ticketNameC" style="position: relative; height: 100px;">
    <span class="ty-ticketName" id="cat"></span>
    <div class="floatRight">
      <div>发票号码： <span class="invNo"></span></div>
      <div>开票日期：<span class="invDate"></span></div>
    </div>
  </div>

  <table class="ty-ticketTab" id="tabss">
    <tr>
      <td>购 <br>买<br>方</td>
      <td colspan="9" class="ty-ticket-txt-left ty-ticket-ttl">
        <span><span>名称：</span><span class="supName"></span></span><br>
        <span><span>纳税人识别号：</span><span class="supNo"></span></span><br>
        <span><span>地址、电话：</span><span class="addATel"></span></span><br>
        <span><span>开户行及账号：</span><span class="bankAcc"></span></span><br>
      </td>
    </tr>
    <tr class="gsList">
      <td>序号</td>
      <td>商品代号/名称/规格/型号</td>
      <td>货物或应税劳务、服务名称</td>
      <td>规格型号</td>
      <td>单位</td>
      <td>数量</td>
      <td>单价</td>
      <td>金额</td>
      <td>税率</td>
      <td>税额</td>
    </tr>
    <tr class="ty-ticket-no-border gsListItem">
      <td class="ty-ticket-txt-black">1</td>
      <td class="ty-ticket-txt-black">商品代号/名称/规格/型号</td>
      <td class="ty-ticket-txt-blue">货物或应税劳务、服务名称</td>
      <td class="ty-ticket-txt-blue">规格型号</td>
      <td class="ty-ticket-txt-blue">单位</td>
      <td class="ty-ticket-txt-blue">数量</td>
      <td class="ty-ticket-txt-blue">单价</td>
      <td class="ty-ticket-txt-blue">金额</td>
      <td class="ty-ticket-txt-blue">税率</td>
      <td class="ty-ticket-txt-blue">税额</td>
    </tr>
    <tr class="ty-ticket-no-border ty-blank-tr">
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
    </tr>
    <tr class="ty-ticket-no-border">
      <td colspan="3" class="ty-ticket-td-border">合计</td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
    </tr>
    <tr>
      <td colspan="3">价税合计（大写）</td>
      <td colspan="7" class="ty-ticket-txt-left">
        <span class="sumAll"></span>
        <span class="sum2ttl">（小写）</span><span class="sumAll2"></span>
      </td>
    </tr>
  </table>


  <table class="ty-ticketTab" id="tabss2">
    <tr>
      <td>购 <br>买<br>方</td>
      <td colspan="7" class="ty-ticket-txt-left ty-ticket-ttl">
        <span><span>名称：</span><span class="supName"></span></span><br>
        <span><span>纳税人识别号：</span><span class="supNo"></span></span><br>
        <span><span>地址、电话：</span><span class="addATel"></span></span><br>
        <span><span>开户行及账号：</span><span class="bankAcc"></span></span><br>
      </td>
    </tr>
    <tr class="gsList">
      <td>序号</td>
      <td>商品代号/名称/规格/型号</td>
      <td>货物或应税劳务、服务名称</td>
      <td>规格型号</td>
      <td>单位</td>
      <td>数量</td>
      <td>含税单价</td>
      <td>本行金额</td>
    </tr>
    <tr class="ty-ticket-no-border gsListItem">
      <td class="ty-ticket-txt-black">1</td>
      <td class="ty-ticket-txt-black">商品代号/名称/规格/型号</td>
      <td class="ty-ticket-txt-blue">货物或应税劳务、服务名称</td>
      <td class="ty-ticket-txt-blue">规格型号</td>
      <td class="ty-ticket-txt-blue">单位</td>
      <td class="ty-ticket-txt-blue">数量</td>
      <td class="ty-ticket-txt-blue">单价</td>
      <td class="ty-ticket-txt-blue">金额</td>
    </tr>
    <tr class="ty-ticket-no-border ty-blank-tr">
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
    </tr>
    <tr class="ty-ticket-no-border">
      <td colspan="3" class="ty-ticket-td-border">合计</td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
    </tr>
    <tr>
      <td colspan="3">价税合计（大写）</td>
      <td colspan="7" class="ty-ticket-txt-left">
        <span class="sumAll"></span>
        <span class="sum2ttl">（小写）</span><span class="sumAll2"></span>
      </td>
    </tr>
  </table>





</div>
<%@ include  file="../../common/footerScript.jsp"%>

<script>
  $(function(){
  // :id/:invoiceId/:way/:fid'

    getTicketDetail()


  })

  // creator: hxz，2023-06-13 获取详情
  function getTicketDetail(){
    let way = getUrlParam("way");
    let fid = getUrlParam("fid");
    let id = getUrlParam("id");
    let invoiceId = getUrlParam("invoiceId");
    let data  = {
      'itemId': invoiceId, 'applicationId': id
    }
    if (fid && fid !== "undefined" && (way === '2' || way === 2)){
      data.fid = fid
    }
    $.ajax({
      'url':"../sale/getItemDetailsById.do",
      "data":data,
      success:function(res){
        let list = res.data
        let info = res.base[0]
        let sum = 0
        var str = ``
        list.forEach( (goods, trIndex) => {
          let mtinfo = goods
          // let mtinfo = JSON.parse(JSON.stringify(goods))
          let merchandiseInvoices = mtinfo.merchandiseInvoices || []
          let huoStr = mtinfo.codeAndName || ''
          let modelStr = mtinfo.modelAndSpec || ''
          merchandiseInvoices.forEach( typeI => {

          })
          let jinStr = (mtinfo.item_amount && mtinfo.item_amount.toFixed(2))
          let shuiStr = ((mtinfo.invoice_amount && mtinfo.invoice_amount.toFixed(2)) || '' )
            sum += Number(jinStr) + Number(shuiStr) ;
          if(info.invoice_category == 1){
            str += '<tr class="ty-ticket-no-border gsListItem">' +
                    '<td class="ty-ticket-txt-black">'+ ( trIndex + 1) +'</td>'   +
                    '<td class="ty-ticket-txt-black">' + mtinfo.outer_sn + ' / '+ mtinfo.name + ' / '+ mtinfo.specifications + ' / '+ mtinfo.model  +'</td>'+
                    '<td class="ty-ticket-txt-blue">'+ huoStr + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ modelStr + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ mtinfo.unit + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ mtinfo.item_quantity + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ mtinfo.item_price + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ jinStr + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ mtinfo.tax_rate + '%</td>'+
                    '<td class="ty-ticket-txt-blue">'+ shuiStr + '</td>'+
                    '</tr>'
          }else if(info.invoice_category == 2){
            str += '<tr class="ty-ticket-no-border gsListItem">' +
                    '<td class="ty-ticket-txt-black">'+ ( trIndex + 1) +'</td>'   +
                    '<td class="ty-ticket-txt-black">' + mtinfo.outer_sn + ' / '+ mtinfo.name + ' / '+ mtinfo.specifications + ' / '+ mtinfo.model  +'</td>'+
                    '<td class="ty-ticket-txt-blue">'+ huoStr + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ modelStr + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ mtinfo.unit + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ mtinfo.item_quantity + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ mtinfo.item_price + '</td>'+
                    '<td class="ty-ticket-txt-blue">'+ jinStr + '</td>'+
                    '</tr>'
          }


        })
        if(info.invoice_category == 1){
          $("#tabss .gsListItem").remove()
          $("#tabss .gsList").after(str)
          $("#tabss").show();
          $("#tabss2").hide();

        }else if(info.invoice_category == 2){
          $("#tabss2 .gsListItem").remove()
          $("#tabss2 .gsList").after(str)
          $("#tabss").hide();
          $("#tabss2").show();
        }
        $(".supName").html(info.invoice_name)
        $(".supNo").html(info.taxpayerID)
        $(".addATel").html(info.invoice_address + ' ' + info.telephone)
        $(".bankAcc").html(info.bank_name + ' ' + info.bank_no)
        let dsX = convertCurrency(sum);
        $(".sumAll").html(dsX)
        $(".sumAll2").html(sum.toFixed(2))
        let invoice_category = info.invoice_category == 1 ? '增值税专用发票' : (info.invoice_category == 2 ?'增值税普通发票': ( info.invoice_category == 3 ?'其他票': ''))
        $("#cat").html(invoice_category)
        $(".invNo").html(info.invoice_no)
        $(".invDate").html( new Date(info.operate_date).format('yyyy-MM-dd') )
      }
    })
  }



</script>
</body>
</html>
