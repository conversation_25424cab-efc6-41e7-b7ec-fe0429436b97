<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/serviceProject.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <%--模式设置--%>
    <div class="bonceContainer bounce-blue" id="modeSetting" style="width: 720px;">
        <div class="bonceHead">
            <span>模式设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="clear dotItem">
                    <p>本项目的收费是否与交付/验收有关？</p>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="1"></i>是
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="0"></i>否
                    </div>
                </div>
                <div class="clear dotItem">
                    <p>本项目服务费用系一次性收取，还是分多期收取？</p>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="1"></i> 一次性收取
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="2"></i>分多期收取
                    </div>
                </div>
                <div class="clear dotItem">
                    <p>本项目各期服务费用的收取，直接录入金额还是录入比例？</p>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="1"></i> 直接录入金额
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="2"></i>录入比例
                    </div>
                </div>
                <div class="clear dotItem">
                    <p>金额上限取哪个价格？</p>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="1"></i> 专票不含税价
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="2"></i>专票含税价
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="3"></i> 普票价
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="4"></i> 不开票价
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="5"></i>参考单价
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="modeSettingSure()">确 定</span>
        </div>
    </div>
        <%--修改服务项目信息-周期--%>
        <div class="bonceContainer bounce-blue" id="editService" style="width:900px;">
            <div class="bonceHead">
                <span>修改服务项目信息</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon specialForm">
                <div style="padding: 0 20px;">
                    <div class="gapTp">
                        <div>为服务项目定义</div>
                        <div class="ty-color-blue">注：此处录入的数据可供不同的服务合同重复引用，也可供套餐设置时引用。</div>
                    </div>
                    <table class="ty-table ty-table-control byCycle">
                        <tbody>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="40%">说明</td>
                        </tr>
                        <tr>
                            <td><input need class="form-control" name="code" type="text" require onblur="codeCheck($(this))"/></td>
                            <td><input need class="form-control" name="name" type="text" placeholder="此处最多可录入15字" maxlength="15" require /></td>
                            <td><input need class="form-control" name="memo" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this), 100)"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <table class="ty-table ty-table-control noByCycle">
                        <tbody>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="30%">单位<i class="xing"></i><span onclick="addUnit('edit_unitSelect')" style="font-weight: bold; color:#0b94ea; padding:0 5px; ">新增</span></td>
                        </tr>
                        <tr>
                            <td><input need class="form-control" name="code" type="text" require onblur="codeCheck($(this))" /></td>
                            <td><input need class="form-control" name="name" type="text" placeholder="此处最多可录入15字" onkeyup="limitWord($(this), 15)" require /></td>
                            <td>
                                <select need type="text" id="edit_unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                                <input need type="hidden" name="unit" id="edit_unitName">
                            </td>
                        </tr>
                        <tr>
                            <td>说明</td>
                            <td colspan="3"><input class="form-control" need name="memo" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this), 100)"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="priceTp">
                        <div>价格<i class="xing"></i></div>
                        <div class="ty-color-blue">注：下表内各种单价中，不涉及的可以不录，但至少需录入一种。</div>
                    </div>
                    <table class="ty-table ty-table-control priceForm">
                        <tbody>
                        <tr>
                            <td width="40%" colspan="3">开增值税专用发票时</td>
                            <td width="20%" rowspan="2">开普票时的开票单价</td>
                            <td width="20%" rowspan="2">不开发票时的单价</td>
                            <td width="20%" rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td style="min-width: 90px;">税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr>
                            <td>
                                <select need class="rate" name="taxRate" onchange="setprice(3, $(this))">
                                    <option value=""></option>
                                </select>
                            </td>
                            <td><input class="noPrice" need name="unitPriceNotax" type="text" oninput="clearNoNumN(this, 2);setprice(1, $(this)) " /></td>
                            <td><input class="price" need name="unitPrice" type="text" oninput="clearNoNumN(this, 2);setprice(2, $(this))" /></td>
                            <td><input need name="unitPriceInvoice" type="text" oninput="clearNoNumN(this, 2)"/></td>
                            <td><input need name="unitPriceNoinvoice" type="text" oninput="clearNoNumN(this, 2)"/></td>
                            <td><input need name="unitPriceReference" type="text" oninput="clearNoNumN(this, 2)"/></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="6"><input class="form-control" name="priceDesc" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateServiceSure()">确 定</span>
            </div>
        </div>
        <%--修改服务项目信息-不按周期--%>
        <div class="bonceContainer bounce-blue" id="editServiceNoCycle" style="width:860px;">
            <div class="bonceHead">
                <span>修改服务项目信息</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="scan-wrap" style="padding: 0 20px;">
                    <div class="elemFlex">
                        <div>项目的基础信息</div>
                    </div>
                    <div>
                        <div class="byCycle gapTp">
                            <table class="ty-table ty-table-control">
                                <tbody>
                                <tr>
                                    <td width="12%">代号</td>
                                    <td width="48%" colspan="3">名称</td>
                                    <td width="40%" colspan="2">说明</td>
                                </tr>
                                <tr>
                                    <td need data-name="code"></td>
                                    <td need data-name="name" colspan="3"></td>
                                    <td need data-name="memo" colspan="2"></td>
                                </tr>
                                <tr>
                                    <td colspan="3">开增值税专用发票时</td>
                                    <td rowspan="2">开普票时的开票单价</td>
                                    <td rowspan="2">不开发票时的单价</td>
                                    <td rowspan="2">参考单价</td>
                                </tr>
                                <tr>
                                    <td>税率</td>
                                    <td>不含税单价</td>
                                    <td>含税单价</td>
                                </tr>
                                <tr>
                                    <td need data-name="taxRate"></td>
                                    <td need data-name="unitPriceNotax"></td>
                                    <td need data-name="unitPrice"></td>
                                    <td need data-name="unitPriceInvoice"></td>
                                    <td need data-name="unitPriceNoinvoice"></td>
                                    <td need data-name="unitPriceReference"></td>
                                </tr>
                                <tr>
                                    <td>价格说明</td>
                                    <td colspan="5" need data-name="priceDesc"></td>
                                </tr>
                                </tbody>
                            </table>
                            <hr/>
                            <div class="origin">
                                <span class="gapRt">收费周期<i class="xing"></i></span>
                                每
                                <select class="entry" name="periodDuration" require>
                                    <option value=""></option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                </select>
                                <select class="entry" name="periodUnit" onchange="switchPeriod($(this))" require>
                                    <option value="">请选择</option>
                                    <option value="7">年</option>
                                    <option value="4">个月</option>
                                </select>
                                收费一次
                            </div>
                            <div class="origin">
                                <span class="gapRt">收费时间<i class="xing"></i></span>
                                <select class="entry" name="chargeStage" style="margin-left: 17px;" require>
                                    <option value="">请选择</option>
                                    <option value="1">合同签订后</option>
                                    <option value="2">服务开始前</option>
                                    <option value="3">服务开始后</option>
                                    <option value="4">服务结束后</option>
                                </select>
                                <input need name="chargeLimit" type="text" placeholder="请填写" oninput="clearNum(this)" require/>天内
                            </div>
                        </div>
                        <div class="noByCycleSet">
                            <table class="ty-table ty-table-control">
                                <tbody>
                                <tr>
                                    <td width="12%">代号</td>
                                    <td width="64%" colspan="4">名称</td>
                                    <td width="24%">单位</td>
                                </tr>
                                <tr>
                                    <td need data-name="code"></td>
                                    <td need data-name="name" colspan="4"></td>
                                    <td need data-name="unit"></td>
                                </tr>
                                <tr>
                                    <td>说明</td>
                                    <td need data-name="memo" colspan="5"></td>
                                </tr>
                                <tr>
                                    <td colspan="3">开增值税专用发票时</td>
                                    <td rowspan="2">开普票时的开票单价</td>
                                    <td rowspan="2">不开发票时的单价</td>
                                    <td rowspan="2">参考单价</td>
                                </tr>
                                <tr>
                                    <td>税率</td>
                                    <td>不含税单价</td>
                                    <td>含税单价</td>
                                </tr>
                                <tr>
                                    <td need data-name="taxRate"></td>
                                    <td need data-name="unitPriceNotax"></td>
                                    <td need data-name="unitPrice"></td>
                                    <td need data-name="unitPriceInvoice"></td>
                                    <td need data-name="unitPriceNoinvoice"></td>
                                    <td need data-name="unitPriceReference"></td>
                                </tr>
                                <tr>
                                    <td>价格说明</td>
                                    <td colspan="5" need data-name="priceDesc"></td>
                                </tr>
                                </tbody>
                            </table>
                            <hr/>
                            <div class="modeSetted">
                                <div class="clear oneTime">
                                    <div class="ty-left">
                                        <span class="sm-ttl">收费时间<i class="xing"></i></span>
                                    </div>
                                    <div class="ty-right">
                                        <input name="id" type="hidden"/>
                                        <select class="entry" name="chargeStage" require>
                                            <option value="">请选择</option>
                                            <option value="1">合同签订后</option>
                                            <option value="2">服务开始前</option>
                                            <option value="3">服务开始后</option>
                                            <option value="4">交付前</option>
                                            <option value="5">交付后</option>
                                            <option value="10">通过验收后</option>
                                            <option value="13">服务结束后</option>
                                        </select>
                                        <input name="chargeLimit" type="text" oninput="clearNum(this)" require/>天内
                                    </div>
                                </div>
                                <div class="manyTime">
                                    <div class="many_relate">
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">预付款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务早期需收取的款项</div>
                                                    <div>本项目如需收取预付款，请设置</div>
                                                </div>
                                            </div>
                                            <div class="price-box">
                                                <input name="id" type="hidden"/>
                                                <span class="gapRt0 amountType">
                                                <span></span><input name="amount" type="text"/><span></span>
                                            </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="1">合同签订后</option>
                                                    <option value="2">服务开始前</option>
                                                    <option value="3">服务开始后</option>
                                                </select>
                                                <input name="chargeLimit" type="text" oninput="clearNum(this)" />天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">尾款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务结束后才能收到的各种款项</div>
                                                    <div>本项目如有尾款，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn fun-btn withBold addOne" data-type="addOne">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                                <input name="id" type="hidden"/>
                                                <span class="gapRt0 amountType">
                                            <span></span><input name="amount" type="text"/><span></span>
                                        </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="8">最终交付前</option>
                                                    <option value="9">最终交付后</option>
                                                    <option value="12">最终验收通过后</option>
                                                    <option value="13">服务结束后</option>
                                                </select>
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">中间款项</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指除预付款与尾款外的应收款项</div>
                                                    <div>本项目如需收取中间款项，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn fun-btn withBold addOne" data-type="addMore">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                                <div>本期中间款项的收取信息</div>
                                                <input name="id" type="hidden"/>
                                                <span class="gapRt0 amountType">
                                            <span></span><input name="amount" type="text"/><span></span>
                                        </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="6">本期交付前</option>
                                                    <option value="7">本期交付后</option>
                                                    <option value="11">本期验收通过后</option>
                                                </select>
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                                <div class="midPay">
                                                    <div class="clear">
                                                        <div>
                                                            <span class="gapRt0">交付/验收环节的名称</span>
                                                            <input class="middleSize" name="stageName" type="text" maxlength="20" onkeyup="limitWord($(this), 20)"/>
                                                        </div>
                                                        <div class="lenTip ty-right" style="clear: both">0/20</div>
                                                    </div>
                                                    <div class="clear">
                                                        <div>
                                                            <span class="gapRt0">交付/验收的内容描述</span>
                                                            <input class="middleSize" name="stageDesc" type="text" maxlength="40" onkeyup="limitWord($(this), 40)"/>
                                                        </div>
                                                        <div class="lenTip ty-right" style="clear: both">0/40</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="many_noRelate">
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">预付款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务早期需收取的款项</div>
                                                    <div>本项目如需收取预付款，请设置</div>
                                                </div>
                                            </div>
                                            <div class="price-box">
                                                <input name="id" type="hidden"/>
                                                <span class="gapRt0 amountType">
                                            <span></span><input name="amount" type="text"/><span></span>
                                        </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="1">合同签订后</option>
                                                    <option value="2">服务开始前</option>
                                                    <option value="3">服务开始后</option>
                                                </select>
                                                <input name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">尾款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务结束后才能收到的各种款项</div>
                                                    <div>本项目如有尾款，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn fun-btn withBold addOne" data-type="no_addOne">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                                <input name="id" type="hidden"/>
                                                <span class="gapRt0 amountType">
                                            <span></span><input name="amount" type="text"/><span></span>
                                        </span>
                                                收费时间为服务结束后
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">中间款项</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指除预付款与尾款外的应收款项</div>
                                                    <div>本项目如需收取中间款项，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn fun-btn withBold addOne" data-type="no_addMore">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                                <input name="id" type="hidden"/>
                                                <span class="gapRt0 amountType">
                                            <span></span><input name="amount" type="text"/><span></span>
                                        </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateServiceSure()">确 定</span>
            </div>
        </div>
        <%--系统提示--%>
        <div class="bonceContainer bounce-red" id="delService">
            <div class="bonceHead">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="ty-center">
                    <p>确定删除该项目吗？</p>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="delServiceSure()">确 定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-red" id="stopService" style="width: 500px">
            <div class="bonceHead">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="ty-center" id="tipMsg">
                    <p>确定后，套餐或合同中将无法选到该项目。</p>
                    <p>确定停用该项目吗？</p>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="stopServiceSure()">确 定</span>
            </div>
        </div>
        <%-- 服务项目修改记录 --%>
        <div class="bonceContainer bounce-blue" id="updateServiceRecords" style="width: 520px;">
            <div class="bonceHead">
                <span>修改记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div style="padding:20px;">
                    <p class="curSta"></p>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td>资料状态</td>
                            <td>创建人/修改人</td>
                            <td>操 作</td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="ye_log"></div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
            </div>
        </div>
    <%--设置体验环节/试用流程--%>
        <div class="bonceContainer bounce-blue" id="trialSetting" style="width: 900px;">
            <div class="bonceHead">
                <span>设置体验环节/试用流程</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="trialSect">本项目需设置体验环节还是试用流程？</div>
                <div class="clear trialSect trialType">
                    <div class="typeDot">
                        <i class="fa fa-circle-o" data-type="2"></i> 体验环节
                    </div>
                    <div class="typeDot">
                        <i class="fa fa-circle-o" data-type="3"></i> 试用流程
                    </div>
                </div>
                <div class="mar">
                    <div>
                        <div class="trialSect marCircle">
                            <span class="gapRt">客户可<span class="tagCon">试用</span>的天数<i class="xing"></i></span><input need name="trailDays" type="text" placeholder="请填写" oninput="clearNum(this)" require/>天
                        </div>
                        <div class="trialSect marNoCircle">
                            <span class="gapRt">某客户可<span class="tagCon">试用</span>的数量上限<i class="xing"></i></span><input need name="trailUpper" type="text" placeholder="请填写" oninput="clearNum(this)" require/>
                            <span class="marUnit"></span>
                        </div>
                    </div>
                    <div class="clear trialSect trialCharge">
                        <div class="ty-left gapRt"><span class="tagCon">试用</span>是否收费？<span class="xing"></span></div>
                        <div class="chargeDot">
                            <i class="fa fa-circle-o" data-type="1"></i>  收费
                        </div>
                        <div class="chargeDot">
                            <i class="fa fa-circle-o" data-type="0"></i> 免费
                        </div>
                    </div>
                </div>
                <div class="mar">
                    <div class="clear trialSect trialDeliveryAcceptable">
                        <div class="ty-left gapRt"><span class="tagCon">试用</span>是否需交付/验收？<span class="xing"></span></div>
                        <div class="acceptDot">
                            <i class="fa fa-circle-o" data-type="1"></i>  是
                        </div>
                        <div class="acceptDot">
                            <i class="fa fa-circle-o" data-type="0"></i> 否
                        </div>
                    </div>
                </div>
                <div class="mar">
                    <div class="trialSect">
                        <div><span class="tagCon">试用</span>价格<i class="xing"></i></div>
                        <div class="ty-color-blue">注：下表内各种单价中，不涉及的可以不录，但至少需录入一种。</div>
                    </div>
                    <table class="ty-table ty-table-control priceForm">
                        <tbody>
                        <tr>
                            <td width="40%" colspan="3">开增值税专用发票时</td>
                            <td width="20%" rowspan="2">开普票时的开票单价</td>
                            <td width="20%" rowspan="2">不开发票时的单价</td>
                            <td width="20%" rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td style="min-width: 90px;">税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr>
                            <td>
                                <select need class="rate" name="taxRate" onchange="setprice(3, $(this))">
                                    <option value=""></option>
                                </select>
                            </td>
                            <td><input class="noPrice" need name="unitPriceNotax" type="text" oninput="clearNoNumN(this, 2);setprice(1, $(this)) " /></td>
                            <td><input class="price" need name="unitPrice" type="text" oninput="clearNoNumN(this, 2);setprice(2, $(this))" /></td>
                            <td><input need name="unitPriceInvoice" type="text" oninput="clearNoNumN(this, 2)"/></td>
                            <td><input need name="unitPriceNoinvoice" type="text" oninput="clearNoNumN(this, 2)"/></td>
                            <td><input need name="unitPriceReference" type="text" oninput="clearNoNumN(this, 2)"/></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="6"><input class="form-control" name="priceDesc" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="origin">
                        <span class="gapRt">收费时间<i class="xing"></i></span>
                        <select class="entry" name="chargeStage" style="margin-left: 17px;" require>
                            <option value="">请选择</option>
                            <option value="1">合同签订后</option>
                            <option value="2">试用开始前</option>
                            <option value="3">试用开始后</option>
                            <option value="4">试用结束后</option>
                        </select>
                        <input name="chargeLimit" type="text" placeholder="请填写" oninput="clearNum(this)" require/>天内
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <div class="trialSettingBtn">
                    <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="trialSettingSure()">确 定</span>
                </div>
            </div>
        </div>
        <%-- 体验环节/试用流程-改变状态  --%>
        <div class="bonceContainer bounce-green" id="changeState" style="width: 600px;">
            <div class="bonceHead">
                <span>改变状态</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div style="margin-left: 40px;">
                    <p>确定后，这项试用流程/体验环节将不再出现在服务项目选项中！</p>
                    <div class="updateTrailTip">进程中的业务，需自行妥善处理！</div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="changeStateSure()">确定</span>
            </div>
        </div>
        <%-- 设置体验环节/试用流程修改记录 --%>
        <div class="bonceContainer bounce-blue" id="updateTrailRecords" style="width: 620px;">
            <div class="bonceHead">
                <span>状态改变记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div style="padding:20px;">
                    <p class="curSta"></p>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td>操作前状态</td>
                            <td>改变状态的操作者</td>
                            <td>设置结果</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
            </div>
        </div>
        <%-- 编辑服务项目的开票资料  --%>
        <div class="bonceContainer bounce-green" id="invoiceInfoSet" style="width: 800px;">
            <div class="bonceHead">
                <span>编辑服务项目的开票资料</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="wrapWidth">
                    <div>
                        <div class="emptyTip1">
                            <div>目前，本服务项目开票资料“货物或应税劳务、服务名称”尚无数据，需采用哪项数据？</div>
                            <div class="careTip">注1 财务人员对本服务项目开发票时，需使用到此数据，建议进行设置！</div>
                            <div class="careTip">注2 此数据设置前，往往需先与客户沟通，并达成一致。</div>
                        </div>
                        <div class="repeatTip1">
                            <div class="inv_con">
                                <p>目前，本服务项目开票资料“货物或应税劳务、服务名称”的数据为：</p>
                                <div class="inv_ttl1"></div>
                            </div>
                            <div>需将此数据修改为哪一项？请选择。</div>
                            <div class="careTip">注1 此数据修改前，往往需先与客户沟通，并达成一致。</div>
                        </div>
                    </div>
                    <div class="cateChoose">
                        <div><span class="fa fa-circle-o" data-val="1" data-type="1"></span>采用服务项目代号的当前数据</div>
                        <div><span class="fa fa-circle-o" data-val="2" data-type="1"></span>采用服务项目名称的当前数据</div>
                        <div><span class="fa fa-circle-o" data-val="3" data-type="1"></span>上述代号与名称都使用，代号在前</div>
                        <div><span class="fa fa-circle-o" data-val="4" data-type="1"></span>上述代号与名称都使用，名称在前</div>
                        <div><span class="fa fa-circle-o" data-val="5" data-type="1"></span>自定义</div>
                        <input class="form-control" type="text" value="" placeholder="请录入" disabled />
                    </div>
                    <div class="copyIcon">
                        <p>当前，本服务项目代号与名称数据如下：</p>
                        <p>服务项目代号：<span id="serviceCode"></span>
                            <span class="def-btn ty-right" onclick="copyServiceInfo($(this))">复制</span>
                        </p>
                        <p>服务项目名称：<span id="serviceName"></span>
                            <span class="def-btn ty-right" onclick="copyServiceInfo($(this))">复制</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="invoiceInfoSet(1)">确定</span>
            </div>
        </div>
        <%-- 规格型号  --%>
        <div class="bonceContainer bounce-green" id="invoiceFormateSet" style="width: 800px;">
            <div class="bonceHead">
                <span>编辑服务项目的开票资料</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="wrapWidth">
                    <div>
                        <div class="emptyTip2">
                            <div>目前，本服务项目开票资料“规格型号”尚无数据，需采用哪项数据？</div>
                            <div class="careTip">注1 财务人员对本服务项目开发票时，如使用此数据，请设置！</div>
                            <div class="careTip">注2 此数据设置前，往往需先与客户沟通，并达成一致。</div>
                        </div>
                        <div class="repeatTip2">
                            <div class="inv_con">
                                <p>目前，本服务项目开票资料“规格型号”的数据为：</p>
                                <div class="inv_ttl2"></div>
                            </div>
                            <div>需将此数据修改为哪一项？请选择。</div>
                            <div class="careTip">注1 此数据修改前，往往需先与客户沟通，并达成一致。</div>
                        </div>
                    </div>

                    <div class="cateChoose">
                        <div><span class="fa fa-circle-o" data-val="1" data-type="2"></span>发票上的“规格型号”为空，无需填写</div>
                        <div><span class="fa fa-circle-o" data-val="2" data-type="2"></span>自定义</div>
                        <input class="form-control" type="text" value="" placeholder="请录入" disabled />
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="invoiceInfoSet(2)">确定</span>
            </div>
        </div>
        <%-- 开票资料“货物或应税劳务、服务名称”的编辑记录 --%>
        <div class="bonceContainer bounce-blue" id="invoiceUpdateRecords" style="width: 520px;">
            <div class="bonceHead">
                <span></span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div style="padding:20px;">
                    <p class="curSta"></p>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td>资料状态</td>
                            <td>编辑后的数据</td>
                            <td>操作者</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
            </div>
        </div>
</div>
<div class="bounce">
    <%--新增服务项目选项--%>
    <div class="bonceContainer bounce-blue" id="serviceInitChoose">
        <div class="bonceHead">
            <span>新增服务项目</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <p>本项目是否按周期（如按年、按月）收费？</p>
                <div class="clear dotItem">
                    <div class="changeDot" data-type="1">
                        <i class="fa fa-circle-o"></i>是
                    </div>
                    <div class="changeDot" data-type="0">
                        <i class="fa fa-circle-o"></i>否
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="serviceInitChoose()">确 定</span>
        </div>
    </div>
        <%--新增服务项目--%>
        <div class="bonceContainer bounce-blue" id="addService" style="width:1000px;">
            <div class="bonceHead">
                <span>新增服务项目</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon specialForm">
                <div style="padding: 0 20px;">
                    <div class="headTip">请录入需分多次收费，且收费与交付/验收有关的项目！</div>
                    <div class="gapTp flexBox">
                        <div>
                            <div>为服务项目定义</div>
                            <div class="ty-color-blue">注：此处录入的数据可供不同的服务合同重复引用，也可供套餐设置时引用。</div>
                        </div>
                        <div class="noByCycle ty-color-gray">批量导入</div>
                    </div>
                    <table class="ty-table ty-table-control byCycle">
                        <tbody>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="40%">说明</td>
                        </tr>
                        <tr>
                            <td><input need class="form-control" name="code" type="text" require onblur="codeCheck($(this))"/></td>
                            <td><input need class="form-control" name="name" type="text" placeholder="此处最多可录入15字" maxlength="15" require /></td>
                            <td><input need class="form-control" name="memo" type="text" placeholder="此处最多可录入100字" maxlength="100"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <table class="ty-table ty-table-control noByCycle">
                        <tbody>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="30%">单位<i class="xing"></i><span onclick="addUnit('add_unitSelect')" style="font-weight: bold; color:#0b94ea; padding:0 5px; ">新增</span></td>
                        </tr>
                        <tr>
                            <td><input need class="form-control" name="code" type="text" require onblur="codeCheck($(this)) "/></td>
                            <td><input need class="form-control" name="name" type="text" placeholder="此处最多可录入15字" maxlength="15" require /></td>
                            <td>
                                <select need type="text" id="add_unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                                <input need type="hidden" name="unit" id="add_unitName">
                            </td>
                        </tr>
                        <tr>
                            <td>说明</td>
                            <td colspan="3"><input class="form-control" need name="memo" type="text" placeholder="此处最多可录入100字" maxlength="100"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="priceTp">
                        <div>价格<i class="xing"></i></div>
                        <div class="ty-color-blue">注：下表内各种单价中，不涉及的可以不录，但至少需录入一种。</div>
                    </div>
                    <table class="ty-table ty-table-control priceForm">
                        <tbody>
                        <tr>
                            <td width="40%" colspan="3">开增值税专用发票时</td>
                            <td width="20%" rowspan="2">开普票时的开票单价</td>
                            <td width="20%" rowspan="2">不开发票时的单价</td>
                            <td width="20%" rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr>
                            <td>
                                <select need class="rate" name="taxRate" onchange="setprice(3, $(this))">
                                    <option value=""></option>
                                </select>
                            </td>
                            <td><input class="noPrice" need name="unitPriceNotax" type="text" oninput="clearNoNumN(this, 2);setprice(1, $(this))"  /></td>
                            <td><input class="price" need name="unitPrice" type="text" oninput="clearNoNumN(this, 2);setprice(2, $(this))"  /></td>
                            <td><input need name="unitPriceInvoice" type="text" oninput="clearNoNumN(this, 2)"/></td>
                            <td><input need name="unitPriceNoinvoice" type="text" oninput="clearNoNumN(this, 2)"/></td>
                            <td><input need name="unitPriceReference" type="text" oninput="clearNoNumN(this, 2)"/></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="6"><input class="form-control" name="priceDesc" type="text" placeholder="此处最多可录入100字" maxlength="100"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="byCycle gapTp">
                        <div class="origin">
                            <span class="gapRt">收费周期<i class="xing"></i></span>
                            每
                            <select class="entry" name="periodDuration" require>
                                <option value=""></option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                            </select>
                            <select class="entry" name="periodUnit" onchange="switchPeriod($(this))" require>
                                <option value="">请选择</option>
                                <option value="7">年</option>
                                <option value="4">个月</option>
                            </select>
                            收费一次
                        </div>
                        <div class="origin">
                            <span class="gapRt">收费时间<i class="xing"></i></span>
                            <select class="entry" name="chargeStage" style="margin-left: 17px;" require>
                                <option value="">请选择</option>
                                <option value="1">合同签订后</option>
                                <option value="2">服务开始前</option>
                                <option value="3">服务开始后</option>
                                <option value="4">服务结束后</option>
                            </select>
                            <input need name="chargeLimit" type="text" placeholder="请填写" oninput="clearNum(this)" require/>天内
                        </div>
                    </div>
                    <div class="noByCycle gapLn">
                        <div class="flexBox modeNoSetted">
                            <div>
                                <div>服务费用的收取，有时与交付/验收有关，还可能分多期收取。</div>
                                <div>“模式设置”，相当于建立对服务费用收取的系统性管控。如需要，请设置！</div>
                                <div class="ty-color-blue">注：模式设置后，如需要还可重新设置。</div>
                            </div>
                            <div><span class="def-btn fun-btn withBold" data-type="modeSetting">模式设置</span></div>
                        </div>
                        <div class="modeSetted">
                            <div class="flexBox">
                                <div>服务费用的收取模式已进行了设置，请根据实际情况填写以下各项内容。
                                    <br/>
                                    如需要，模式可重新设置，但“重新设置模式”后，已有数据将被清空。
                                </div>
                                <div><span class="def-btn fun-btn withBold" data-type="reModeSetting">重新设置模式</span></div>
                            </div>
                            <div class="noByCycleSet">
                                <div class="clear oneTime">
                                    <div class="ty-left">
                                        <span class="sm-ttl">收费时间<i class="xing"></i></span>
                                    </div>
                                    <div class="ty-right">
                                        <select class="entry" name="chargeStage" require>
                                            <option value="">请选择</option>
                                            <option value="1">合同签订后</option>
                                            <option value="2">服务开始前</option>
                                            <option value="3">服务开始后</option>
                                            <option value="4">交付前</option>
                                            <option value="5">交付后</option>
                                            <option value="10">通过验收后</option>
                                            <option value="13">服务结束后</option>
                                        </select>
                                        <input name="chargeLimit" type="text" oninput="clearNum(this)" require/>天内
                                    </div>
                                </div>
                                <div class="manyTime">
                                    <div class="many_relate">
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">预付款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务早期需收取的款项</div>
                                                    <div>本项目如需收取预付款，请设置</div>
                                                </div>
                                            </div>
                                            <div class="price-box">
                                        <span class="gapRt0 amountType">
                                                金额<input name="amount" type="text"/>元
                                        </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="1">合同签订后</option>
                                                    <option value="2">服务开始前</option>
                                                    <option value="3">服务开始后</option>
                                                </select>
                                                <input name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">尾款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务结束后才能收到的各种款项</div>
                                                    <div>本项目如有尾款，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn fun-btn withBold addOne" data-type="addOne">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                            <span class="gapRt0 amountType">
                                                    金额<input need name="amount" type="text"/>元
                                            </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="8">最终交付前</option>
                                                    <option value="9">最终交付后</option>
                                                    <option value="12">最终验收通过后</option>
                                                    <option value="13">服务结束后</option>
                                                </select>
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">中间款项</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指除预付款与尾款外的应收款项</div>
                                                    <div>本项目如需收取中间款项，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn fun-btn withBold addOne" data-type="addMore">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                                <div>本期中间款项的收取信息</div>
                                                <span class="gapRt0 amountType">
                                                金额<input need name="amount" type="text"/>元
                                            </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="6">本期交付前</option>
                                                    <option value="7">本期交付后</option>
                                                    <option value="11">本期验收通过后</option>
                                                </select>
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                                <div class="midPay">
                                                    <div class="clear">
                                                        <div>
                                                            <span class="gapRt0">交付/验收环节的名称</span>
                                                            <input class="middleSize" name="stageName" type="text" maxlength="20" oninput="limitWord($(this), 20)"/>
                                                        </div>
                                                        <div class="lenTip ty-right" style="clear: both">0/20</div>
                                                    </div>
                                                    <div class="clear">
                                                        <div>
                                                            <span class="gapRt0">交付/验收的内容描述</span>
                                                            <input class="middleSize" name="stageDesc" type="text" maxlength="40" oninput="limitWord($(this), 40)"/>
                                                        </div>
                                                        <div class="lenTip ty-right" style="clear: both">0/40</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="many_noRelate">
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">预付款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务早期需收取的款项</div>
                                                    <div>本项目如需收取预付款，请设置</div>
                                                </div>
                                            </div>
                                            <div class="price-box">
                                                <span class="gapRt0 amountType">
                                                    金额<input name="amount" type="text"/>元
                                                </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="1">合同签订后</option>
                                                    <option value="2">服务开始前</option>
                                                    <option value="3">服务开始后</option>
                                                </select>
                                                <input name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">尾款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务结束后才能收到的各种款项</div>
                                                    <div>本项目如有尾款，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn fun-btn withBold addOne" data-type="no_addOne">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                            <span class="gapRt0 amountType">
                                                    金额<input need name="amount" type="text"/>元
                                            </span>
                                                收费时间为服务结束后
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">中间款项</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指除预付款与尾款外的应收款项</div>
                                                    <div>本项目如需收取中间款项，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn fun-btn withBold addOne" data-type="no_addMore">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                                <span class="gapRt0 amountType">
                                                    金额<input need name="amount" type="text"/>元
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="gapLn">
                        <div class="elemFlex">
                            <div>本项目如有体验环节或试用流程，可通过“改变状态”来设置，如无，则无需操作。</div>
                            <span class="def-btn" onclick="updateTrialSetting('add')">修改</span>
                            <span class="hd" id="trialingData"></span>
                        </div>
                        <table class="ty-table posLeft trialingShow">
                            <tbody>
                            <tr>
                                <td width="30%" class="posCenter">状态</td>
                                <td width="70%">
                                    <span class="trialState" data-state="0">未设置</span>
                                    <span class="def-btn ty-right" onclick="changeTrialState('add')">改变状态</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addServiceSure()">确 定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-blue" id="serviceProjectScan" style="width:800px;">
            <div class="bonceHead">
                <span>服务项目查看</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="hd serviceData"></div>
                <div class="scan-wrap scanTb">
                    <div class="elemFlex">
                        <div>基本信息</div>
                        <div class="baseUpdateBtns">
                            <span class="def-btn fun-btn ableBtn" data-icon="1" data-type="updateService">修改</span>
                            <span class="def-btn fun-btn" data-icon="1" data-type="updateServiceRecords">修改记录</span>
                        </div>
                    </div>
                    <table class="ty-table">
                        <tbody>
                        <tr>
                            <td width="30%">代号</td>
                            <td width="70%" need data-name="code"></td>
                        </tr>
                        <tr>
                            <td>名称</td>
                            <td need data-name="name"></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="gapTp">价格信息</div>
                    <table class="ty-table">
                        <tbody>
                        <tr>
                            <td width="30%">开增值税专用发票时</td>
                            <td width="70%" need data-name="unitPrice"></td>
                        </tr>
                        <tr>
                            <td>开普通发票时</td>
                            <td need data-name="unitPriceInvoice"></td>
                        </tr>
                        <tr>
                            <td>不开发票时</td>
                            <td need data-name="unitPriceNoinvoice"></td>
                        </tr>
                        <tr>
                            <td>参考单价</td>
                            <td need data-name="unitPriceReference"></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="elemFlex">
                        <span>收费模式及说明等</span>
                        <div class="chargeUpdateBtns">
                            <span class="def-btn fun-btn ableBtn" data-icon="1" data-type="updateService">修改</span>
                            <span class="def-btn fun-btn" data-icon="1" data-type="updateServiceRecords">修改记录</span>
                        </div>
                    </div>
                    <table class="ty-table" id="scan_mode">
                        <tbody>
                        <tr>
                            <td width="30%">概述</td>
                            <td width="70%"></td>
                        </tr>
                        <tr>
                            <td>收费周期</td>
                            <td><span>每<span class="feeCycle"></span>收取一次</span></td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td><span><span class="timeCycle"></span>天内</span></td>
                        </tr>
                        <tr>
                            <td>项目说明</td>
                            <td need data-name="memo"></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td need data-name="priceDesc"></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="elemFlex">
                        <span>体验环节/试用流程</span>
                        <div class="trailUpdateBtns">
                            <span class="def-btn fun-btn ableBtn" onclick="updateTrialSetting('update')">修改</span>
                            <span class="def-btn" onclick="updateTrailRecords(1)">修改记录</span>
                        </div>
                    </div>
                    <table class="ty-table" id="scan_trail">
                        <tbody>
                        <tr>
                            <td width="30%">状态</td>
                            <td width="70%">
                                <span id="scan_trail_state"></span>
                                <div class="ty-right">
                                    <span class="def-btn gapR ableBtn" onclick="changeTrialState('update')">改变状态</span>
                                    <span class="def-btn" onclick="updateTrailRecords(2)">状态改变记录</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="elemFlex">
                        <span>服务项目的两项开票资料</span>
                    </div>
                    <table class="ty-table" id="scan_invoice">
                        <tbody>
                        <tr>
                            <td width="30%">货物或应税劳务、服务名称</td>
                            <td width="70%">
                                <span id="scan_invoice_name"></span>
                                <div class="ty-right">
                                    <span class="def-btn gapR ableBtn" onclick="changeInvoiceCon(1)">编辑</span>
                                    <span class="def-btn" onclick="invoiceUpdateRecords(1)">编辑记录</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>规格型号</td>
                            <td>
                                <span id="scan_invoice_formate"></span>
                                <div class="ty-right">
                                    <span class="def-btn gapR ableBtn" onclick="changeInvoiceCon(2)">编辑</span>
                                    <span class="def-btn" onclick="invoiceUpdateRecords(2)">编辑记录</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="clear">
                    <div class="operations">
                        <div><span class="oper">创建</span><span id="seeCreater"></span></div>
                        <div class="reLog">
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(2)">确定</span>
        </div>
    </div>
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="reModTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>确定后，本模式下的已有数据将被清空。</p>
            <p>确定继续本操作吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取 消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="reModSure()">确 定</span>
        </div>
    </div>
        <%--修改记录查看服务项目--%>
    <div class="bonceContainer bounce-blue" id="serviceLogScan" style="width:900px;">
        <div class="bonceHead">
            <span>查看服务项目</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap scanTb">
                <div class="elemFlex">
                    <div>基本信息</div>
                </div>
                <table class="ty-table">
                    <tbody>
                    <tr>
                        <td width="30%">代号</td>
                        <td width="70%" need data-name="code"></td>
                    </tr>
                    <tr>
                        <td>名称</td>
                        <td need data-name="name"></td>
                    </tr>
                    </tbody>
                </table>
                <div class="gapTp">价格信息</div>
                <table class="ty-table">
                    <tbody>
                    <tr>
                        <td width="30%">开增值税专用发票时</td>
                        <td width="70%" need data-name="unitPrice"></td>
                    </tr>
                    <tr>
                        <td>开普通发票时</td>
                        <td need data-name="unitPriceInvoice"></td>
                    </tr>
                    <tr>
                        <td>不开发票时</td>
                        <td need data-name="unitPriceNoinvoice"></td>
                    </tr>
                    <tr>
                        <td>参考单价</td>
                        <td need data-name="unitPriceReference"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
        <%--收费修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="chargeLogScan" style="width:900px;">
        <div class="bonceHead">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap scanTb">
                <div class="elemFlex">
                    <span>收费模式及说明等</span>
                </div>
                <table class="ty-table" id="chargeLogScanCon">
                    <tbody>
                    <tr>
                        <td width="30%">概述</td>
                        <td width="70%"></td>
                    </tr>
                    <tr>
                        <td>收费周期</td>
                        <td><span>每<span class="feeCycle"></span>收取一次</span></td>
                    </tr>
                    <tr>
                        <td>收费时间</td>
                        <td><span><span class="timeCycle"></span>天内</span></td>
                    </tr>
                    <tr>
                        <td>项目说明</td>
                        <td need data-name="memo"></td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td need data-name="priceDesc"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
        <%--修改记录查看服务项目--%>
        <div class="bonceContainer bounce-blue" id="trailLogScan" style="width:800px;">
            <div class="bonceHead">
                <span>查看体验环节/试用流程</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="scan-wrap scanTb">
                    <table class="ty-table" id="trailLogScanCon">
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
            </div>
        </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>服务项目</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <input type="hidden" id="showMainConNum">
                    <div class="mainCon mainCon1">
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div>公司如有常规的服务项目，请在此录入。各常规项目可供录入合同、订单或套餐时选用。</div>
                                <div>
                                    <span class="funBtn ty-btn-blue" data-fun="addService">新 &nbsp; 增</span>
                                    <span class="funBtn ty-btn-blue" data-fun="stoppedService">已停用的项目</span>
                                </div>
                            </div>
                            <div class="ty-alert">
                                <div>系统内在用的常规服务项目共<span class="num"></span>条，具体如下：</div>
                                <div>
                                    <span class="search">查找
                                        <input type="text" placeholder="请输入要查找服务项目的名称"><span onclick="serviceSearch($(this), 1)">确 定</span>
                                    </span>
                                </div>
                            </div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>服务项目（代号/名称）</td>
                                    <td>价格</td>
                                    <td>服务费用的收取</td>
                                    <td>创建时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                </tr>
                                </tbody>
                            </table>
                            <div id="ye1"></div>
                        </div>
                    </div>
                    <div class="mainCon mainCon2">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback(1)">返回</span>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div>系统内已停用的常规服务项目共<span class="num"></span>条，具体如下：</div>
                                <div>
                                    <span class="search">查找
                                        <input type="text" placeholder="请输入要查找服务项目的名称"><span onclick="serviceSearch($(this), 0)">确定</span>
                                    </span>
                                </div>
                            </div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>服务项目（代号/名称）</td>
                                    <td>价格</td>
                                    <td>服务费用的收取</td>
                                    <td>停用时间</td>
                                    <td>创建时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            <div id="ye2"></div>
                        </div>
                    </div>
                    <div class="mainCon mainCon3">
                        <div class="back-btn">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback(2)">返回</span>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div>符合查询条件的数据共<span class="num"></span>条，具体如下：</div>
                            </div>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>服务项目（代号/名称）</td>
                                    <td>价格</td>
                                    <td>服务费用的收取</td>
                                    <td>创建时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                </tr>
                                </tbody>
                            </table>
                            <div id="ye3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/sales/serviceProjects.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
