<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/serviceContract.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed4">
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed4.cancel(); ">我知道了</span>
        </div>
    </div>
    <div id="inputTime" class="bonceContainer bounce-blue" >
        <div class="bonceHead">
            <span>设置对发货时间的要求</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center">
                <select id="sendDian">
                    <option value="">请选择</option>
                    <option value="1">合同签订后</option>
                    <option value="2">服务开始前</option>
                    <option value="3">服务开始后</option>
                    <option value="13">服务结束后</option>
                </select>
                <input type="text" id="sebdDDay" onkeyup="clearNum(this)" style="width: 60px;"/>日内
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed4.cancel(); ">关 闭</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 funBtn" data-fun="inputTimeOk">确定</span>
        </div>
    </div>
    <div id="operatInstructions" class="bonceContainer bounce-blue" style="width: 538px;">
        <div class="bonceHead">
            <span>操作说明</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>本类别的服务套餐：</p>
                <p>1 按周期收费，且项目与商品均需至少有一种。</p>
                <p>2 不可既包含按周期收费的项目，又包含不按周期收费的项目。</p>
                <p>3 所选如为不按周期收费的项目，套餐的收费周期需另行设置。</p>
                <p>4 所选为按周期收费项目时，项目的数量仅可为1不可编辑，套餐的收费周期也由系统确定为该项目的收费周期，也不可编辑。</p>
                <p>5 选择一个按周期收费项目后，继续选择时，仅可选择相同收费周期的项目。</p>
                <p>6 系统不支持设置与交付/验收有关的套餐，故所选入套餐的项目，即便收费与交付/验收有关，相关事项也无法进入套餐。</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed4.cancel(); ">关 闭</span>
        </div>
    </div>
        <div class="bonceContainer bounce-blue" id="reModTip">
            <div class="bonceHead">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
            </div>
            <div class="bonceCon ty-center">
                <p>确定后，本模式下的已有数据将被清空。</p>
                <p>确定继续本操作吗？</p>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed4.cancel(); ">取 消</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="reModSure()">确 定</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed3">
    <%-- 商品 查看 --%>
    <div class="bonceContainer bounce-blue" id="scanGoods">
        <div class="bonceHead">
            <span>查看商品</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center; ">
                <p id="scanGoodsTip"></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <%-- 删除项目 或 商品 --%>
    <div class="bonceContainer bounce-blue" id="delProTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center; ">
                确定要删除？
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="delProSure()">确 定</span>
        </div>
    </div>
    <%--向套餐内添加项目 修改数量 --%>
    <div class="bonceContainer bounce-blue" id="editNumPro">
        <div class="bonceHead">
            <span>修改数量</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center; ">
                <p class="zhouP">每个套餐中该项目 <input type="text" class="form-control" onkeyup="clearNum(this)" id="durNum"/> 个周期</p>
                <p>每个套餐中有 <input type="text" class="form-control" onkeyup="clearNum(this)" id="proNum"/> 个该项目</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="editNumProSure()">确 定</span>
        </div>
    </div>
    <%--向套餐内添加商品 修改数量 --%>
    <div class="bonceContainer bounce-blue" id="editNumGoods">
        <div class="bonceHead">
            <span>修改数量</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p><span class="ttl">当前数量</span><span class="form-control oldNum"></span></p>
                <p><span class="ttl">修改为</span><input type="text" class="form-control newNum"/></p>
                <div class="sendD">
                    <p><span class="ttl">当前发货时间的要求</span> <span class="form-control oldSendD"></span></p>
                    <p><span class="ttl">修改为</span><input type="text" class="form-control newSendD funbtn" readonly data-fun="inputTime" placeholder="请选择" /></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="editNumGoodsSure()">确 定</span>
        </div>
    </div>
    <%--向套餐内添加项目--%>
    <div class="bonceContainer bounce-blue" id="addProject" style="width: 720px;">
        <div class="bonceHead">
            <span>向套餐内添加项目</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>请选择组成套餐的项目<span data-fun="operatInstructions" class="ty-right linkBtn">操作说明</span></p>
                <table id="addpTab1" class="ty-table">
                    <tr>
                        <td>选择</td>
                        <td>项目代号/名称</td>
                        <td>计量单位</td>
                        <td>参考单价</td>
                        <td>每个套餐中<br>该项目几个周期</td>
                        <td>每个套餐中<br>有几个该项目</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addProjectSure()">确 定</span>
        </div>
    </div>
    <%--向套餐内添加商品--%>
    <div class="bonceContainer bounce-blue" id="addGoods1" style="width: 520px;">
        <div class="bonceHead">
            <span>向套餐内添加商品</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>以下各项，请根据要设置套餐的情况选择 </p>
                <div class="narrowBody">
                    <p>所添加的商品</p>
                    <div class="clear dotItem">
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="durSet" data-val="2"></i>仅第一个收费周期提供
                        </div>
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="durSet" data-val="1"></i>每个收费周期都需提供
                        </div>
                    </div>
                    <div class="clear dotItem">
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="whereSet" data-val="2"></i>需交给客户
                        </div>
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="whereSet" data-val="1"></i>需存放于本机构
                        </div>
                    </div>
                </div>
                <hr>
                <div class="narrowBody">
                    <p>是否需要客户先付套餐的款，之后才向其提供商品？</p>
                    <div class="clear dotItem">
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="fuBefore" data-val="1"></i>不需要
                        </div>
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="fuBefore" data-val="2"></i>需要
                        </div>
                    </div>
                    <p class="fuBefore2">
                        客户付款后，商品提供给客户需要 <input type="text" class="form-control" style="width: 60px;" id="fuBeforeNum"/>天
                        <br><span class="ty-color-blue" style="font-size: 0.8em;">注：商品的准备时间需考虑进去</span>
                    </p>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addGoodsSure()">确 定</span>
        </div>
    </div>
    <%--向套餐内添加商品2 --%>
    <div class="bonceContainer bounce-blue" id="addGoods2" style="width: 850px;">
        <div class="bonceHead">
            <span>向套餐内添加商品</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>请选择套餐内应包含的商品，选择后再输入所需的数量。</p>
                <table id="addgTab1" class="ty-table">
                    <tr>
                        <td>选择</td>
                        <td>商品代号/名称</td>
                        <td>计量单位</td>
                        <td>参考单价</td>
                        <td id="sendTtl">发货时间的要求</td>
                        <td>数量</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addProductSure()">确 定</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(2)">确定</span>
        </div>
    </div>
    <%--模式设置--%>
    <div class="bonceContainer bounce-blue" id="modeSetting" style="width: 720px;">
        <div class="bonceHead">
            <span>模式设置</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="clear dotItem">
                    <p>本项目的收费是否与交付/验收有关？</p>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="1"></i>是
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="0"></i>否
                    </div>
                </div>
                <div class="clear dotItem">
                    <p>本项目服务费用系一次性收取，还是分多期收取？</p>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="1"></i> 一次性收取
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="2"></i>分多期收取
                    </div>
                </div>
                <div class="clear dotItem">
                    <p>本项目各期服务费用的收取，直接录入金额还是录入比例？</p>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="1"></i> 直接录入金额
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="2"></i>录入比例
                    </div>
                </div>
                <div class="clear dotItem">
                    <p>金额上限取哪个价格？</p>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="1"></i> 专票不含税价
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="2"></i>专票含税价
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="3"></i> 普票价
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="4"></i> 不开票价
                    </div>
                    <div class="changeDot">
                        <i class="fa fa-circle-o" data-type="5"></i>参考单价
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="modeSettingSure()">确 定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--查看服务项目-周期--%>
    <div class="bonceContainer bounce-blue" id="seeServiceByCycle" style="width:800px;">
        <div class="bonceHead">
            <span>查看服务项目</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <div class="headTip"></div>
                <div class="clear">
                    <div class="ty-right">创建：<span class="seeCreater"></span></div>
                </div>
                <div class="elemFlex">
                    <div>项目的基础信息</div>
                </div>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="12%">代号</td>
                        <td width="48%" colspan="3">名称</td>
                        <td width="40%" colspan="2">说明</td>
                    </tr>
                    <tr>
                        <td need data-name="code"></td>
                        <td need data-name="name" colspan="3"></td>
                        <td need data-name="memo" colspan="2"></td>
                    </tr>
                    <tr>
                        <td colspan="3">开增值税专用发票时</td>
                        <td rowspan="2">开普票时的开票单价</td>
                        <td rowspan="2">不开发票时的单价</td>
                        <td rowspan="2">参考单价</td>
                    </tr>
                    <tr>
                        <td>税率</td>
                        <td>不含税单价</td>
                        <td>含税单价</td>
                    </tr>
                    <tr>
                        <td need data-name="taxRate"></td>
                        <td need data-name="unitPriceNotax"></td>
                        <td need data-name="unitPrice"></td>
                        <td need data-name="unitPriceInvoice"></td>
                        <td need data-name="unitPriceNoinvoice"></td>
                        <td need data-name="unitPriceReference"></td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td colspan="5" need data-name="priceDesc"></td>
                    </tr>
                    </tbody>
                </table>
                <hr/>
                <div class="elemFlex">
                    <span>收费周期</span>
                    <span>每<span class="feeCycle"></span>收取一次</span>
                </div>
                <div class="elemFlex">
                    <span>收费时间</span>
                    <span><span class="timeCycle"></span>天内</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%--查看服务项目-不按周期--%>
    <div class="bonceContainer bounce-blue" id="seeServiceNoCycle" style="width:900px;">
        <div class="bonceHead">
            <span>查看服务项目</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="scan-wrap">
                <div class="copyArea">
                    <div class="headTip"></div>
                    <div class="clear">
                        <div class="ty-right">创建：<span class="seeCreater"></span></div>
                    </div>
                    <div class="elemFlex">
                        <div>项目的基础信息</div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="12%">代号</td>
                            <td width="64%" colspan="4">名称</td>
                            <td width="24%">单位</td>
                        </tr>
                        <tr>
                            <td need data-name="code"></td>
                            <td need data-name="name" colspan="4"></td>
                            <td need data-name="unit"></td>
                        </tr>
                        <tr>
                            <td>说明</td>
                            <td need data-name="memo" colspan="5"></td>
                        </tr>
                        <tr>
                            <td colspan="3">开增值税专用发票时</td>
                            <td rowspan="2">开普票时的开票单价</td>
                            <td rowspan="2">不开发票时的单价</td>
                            <td rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr>
                            <td need data-name="taxRate"></td>
                            <td need data-name="unitPriceNotax"></td>
                            <td need data-name="unitPrice"></td>
                            <td need data-name="unitPriceInvoice"></td>
                            <td need data-name="unitPriceNoinvoice"></td>
                            <td need data-name="unitPriceReference"></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="5" need data-name="priceDesc"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <hr/>
                <div>
                    <div class="modeSettings">
                        <div class="gapTp">
                            <div class="oneTime">
                                <div class="elemFlex">
                                    <span>收费时间</span>
                                    <div>
                                        <span class="timeCycle">XXXXXXX  XX</span>天内
                                    </div>
                                </div>
                            </div>
                            <div class="manyTime">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
        <%--新增服务项目选项--%>
        <div class="bonceContainer bounce-blue" id="serviceInitChoose">
            <div class="bonceHead">
                <span>新增服务项目</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="narrowBody">
                    <p>本项目是否按周期（如按年、按月）收费？</p>
                    <div class="clear dotItem">
                        <div class="changeDot" data-type="1">
                            <i class="fa fa-circle-o"></i>是
                        </div>
                        <div class="changeDot" data-type="0">
                            <i class="fa fa-circle-o"></i>否
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="serviceInitChoose()">确 定</span>
            </div>
        </div>
        <%--新增服务项目--%>
        <div class="bonceContainer bounce-blue" id="addService" style="width:900px;">
            <div class="bonceHead">
                <span>新增服务项目</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon specialForm">
                <div style="padding: 0 20px;">
                    <div class="headTip">请录入需分多次收费，且收费与交付/验收有关的项目！</div>
                    <div class="gapTp">
                        <div>为服务项目定义</div>
                        <div class="ty-color-blue">注：此处录入的数据可供不同的服务合同重复引用，也可供套餐设置时引用。</div>
                    </div>
                    <table class="ty-table ty-table-control byCycle">
                        <tbody>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="40%">说明</td>
                        </tr>
                        <tr>
                            <td><input need class="form-control" name="code" type="text" require /></td>
                            <td><input need class="form-control" name="name" type="text" placeholder="此处最多可录入15字" maxlength="15" require /></td>
                            <td><input need class="form-control" name="memo" type="text" placeholder="此处最多可录入100字" maxlength="100"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <table class="ty-table ty-table-control noByCycle">
                        <tbody>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="30%">单位<i class="xing"></i><span onclick="addUnit('add_unitSelect')" style="font-weight: bold; color:#0b94ea; padding:0 5px; ">新增</span></td>
                        </tr>
                        <tr>
                            <td><input need class="form-control" name="code" type="text" require /></td>
                            <td><input need class="form-control" name="name" type="text" placeholder="此处最多可录入15字" maxlength="15" require /></td>
                            <td>
                                <select need type="text" id="add_unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                                <input need type="hidden" name="unit" id="add_unitName">
                            </td>
                        </tr>
                        <tr>
                            <td>说明</td>
                            <td colspan="3"><input class="form-control" need name="memo" type="text" placeholder="此处最多可录入100字" maxlength="100"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="priceTp">
                        <div>价格<i class="xing"></i></div>
                        <div class="ty-color-blue">注：下表内各种单价中，不涉及的可以不录，但至少需录入一种。</div>
                    </div>
                    <table class="ty-table ty-table-control priceForm">
                        <tbody>
                        <tr>
                            <td width="40%" colspan="3">开增值税专用发票时</td>
                            <td width="20%" rowspan="2">开普票时的开票单价</td>
                            <td width="20%" rowspan="2">不开发票时的单价</td>
                            <td width="20%" rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr class="priceSetGroup">
                            <td class="priceSet rateDot">
                                <select need class="rate" name="taxRate" onchange="setprice(3, $(this))">
                                </select>
                            </td>
                            <td class="priceSet noPriceDot"><input class="noPrice" need name="unitPriceNotax" type="text" oninput="clearNoNumN(this, 2);setprice(1, $(this))"  /></td>
                            <td class="priceSet priceDot"><input class="price" need name="unitPrice" type="text" oninput="clearNoNumN(this, 2);setprice(2, $(this))"  /></td>
                            <td><input need name="unitPriceInvoice" type="text" oninput="clearNoNumN(this, 2)"/></td>
                            <td><input need name="unitPriceNoinvoice" type="text" oninput="clearNoNumN(this, 2)"/></td>
                            <td><input need name="unitPriceReference" type="text" oninput="clearNoNumN(this, 2)"/></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="6"><input class="form-control" name="priceDesc" type="text" placeholder="此处最多可录入100字" maxlength="100"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="byCycle gapTp">
                        <div class="origin">
                            <span class="gapRt">收费周期<i class="xing"></i></span>
                            每
                            <select class="entry" name="periodDuration" require>
                                <option value=""></option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                            </select>
                            <select class="entry" name="periodUnit" onchange="switchPeriod($(this))" require>
                                <option value="">请选择</option>
                                <option value="7">年</option>
                                <option value="4">个月</option>
                            </select>
                            收费一次
                        </div>
                        <div class="origin">
                            <span class="gapRt">收费时间<i class="xing"></i></span>
                            <select class="entry" name="chargeStage" style="margin-left: 17px;" require>
                                <option value="">请选择</option>
                                <option value="1">合同签订后</option>
                                <option value="2">服务开始前</option>
                                <option value="3">服务开始后</option>
                                <option value="4">服务结束后</option>
                            </select>
                            <input need name="chargeLimit" type="text" placeholder="请填写" oninput="clearNum(this)" require/>天内
                        </div>
                    </div>
                    <div class="noByCycle gapLn">
                        <div class="flexBox modeNoSetted">
                            <div>
                                <div>服务费用的收取，有时与交付/验收有关，还可能分多期收取。</div>
                                <div>“模式设置”，相当于建立对服务费用收取的系统性管控。如需要，请设置！</div>
                                <div class="ty-color-blue">注：模式设置后，如需要还可重新设置。</div>
                            </div>
                            <div><span class="def-btn funBtn withBold" data-fun="modeSetting">模式设置</span></div>
                        </div>
                        <div class="modeSetted">
                            <div class="flexBox">
                                <div>服务费用的收取模式已进行了设置，请根据实际情况填写以下各项内容。
                                    <br/>
                                    如需要，模式可重新设置，但“重新设置模式”后，已有数据将被清空。
                                </div>
                                <div><span class="def-btn funBtn withBold" data-fun="reModeSetting">重新设置模式</span></div>
                            </div>
                            <div class="noByCycleSet">
                                <div class="clear oneTime">
                                    <div class="ty-left">
                                        <span class="sm-ttl">收费时间</span>
                                    </div>
                                    <div class="ty-right">
                                        <select class="entry" name="chargeStage">
                                            <option value="">请选择</option>
                                            <option value="1">合同签订后</option>
                                            <option value="2">服务开始前</option>
                                            <option value="3">服务开始后</option>
                                            <option value="4">交付前</option>
                                            <option value="5">交付后</option>
                                            <option value="10">通过验收后</option>
                                            <option value="13">服务结束后</option>
                                        </select>
                                        <input name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                    </div>
                                </div>
                                <div class="manyTime">
                                    <div class="many_relate">
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">预付款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务早期需收取的款项</div>
                                                    <div>本项目如需收取预付款，请设置</div>
                                                </div>
                                            </div>
                                            <div class="price-box">
                                        <span class="gapRt0 amountType">
                                                金额<input name="amount" type="text"/>元
                                        </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="1">合同签订后</option>
                                                    <option value="2">服务开始前</option>
                                                    <option value="3">服务开始后</option>
                                                </select>
                                                <input name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">尾款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务结束后才能收到的各种款项</div>
                                                    <div>本项目如有尾款，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn funBtn withBold addOne" data-fun="addOne">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                            <span class="gapRt0 amountType">
                                                    金额<input need name="amount" type="text"/>元
                                            </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="8">最终交付前</option>
                                                    <option value="9">最终交付后</option>
                                                    <option value="12">最终验收通过后</option>
                                                    <option value="13">服务结束后</option>
                                                </select>
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">中间款项</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指除预付款与尾款外的应收款项</div>
                                                    <div>本项目如需收取中间款项，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn funBtn withBold addOne" data-fun="addMore">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                                <div>本期中间款项的收取信息</div>
                                                <span class="gapRt0 amountType">
                                                金额<input need name="amount" type="text"/>元
                                            </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="6">本期交付前</option>
                                                    <option value="7">本期交付后</option>
                                                    <option value="11">本期验收通过后</option>
                                                </select>
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                                <div class="midPay">
                                                    <div class="clear">
                                                        <div>
                                                            <span class="gapRt0">交付/验收环节的名称</span>
                                                            <input class="middleSize" name="stageName" type="text" maxlength="20" oninput="limitWord($(this), 20)"/>
                                                        </div>
                                                        <div class="lenTip ty-right" style="clear: both">0/20</div>
                                                    </div>
                                                    <div class="clear">
                                                        <div>
                                                            <span class="gapRt0">交付/验收的内容描述</span>
                                                            <input class="middleSize" name="stageDesc" type="text" maxlength="40" oninput="limitWord($(this), 40)"/>
                                                        </div>
                                                        <div class="lenTip ty-right" style="clear: both">0/40</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="many_noRelate">
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">预付款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务早期需收取的款项</div>
                                                    <div>本项目如需收取预付款，请设置</div>
                                                </div>
                                            </div>
                                            <div class="price-box">
                                                <span class="gapRt0 amountType">
                                                    金额<input name="amount" type="text"/>元
                                                </span>
                                                收费时间
                                                <select class="entry">
                                                    <option value=""></option>
                                                    <option value="1">合同签订后</option>
                                                    <option value="2">服务开始前</option>
                                                    <option value="3">服务开始后</option>
                                                </select>
                                                <input name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">尾款</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指服务结束后才能收到的各种款项</div>
                                                    <div>本项目如有尾款，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn funBtn withBold addOne" data-fun="no_addOne">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                            <span class="gapRt0 amountType">
                                                    金额<input need name="amount" type="text"/>元
                                            </span>
                                                收费时间为服务结束后
                                                <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            </div>
                                        </div>
                                        <div class="parts">
                                            <div class="clear">
                                                <div class="ty-left">
                                                    <span class="sm-ttl">中间款项</span>
                                                </div>
                                                <div class="ty-left">
                                                    <div>指除预付款与尾款外的应收款项</div>
                                                    <div>本项目如需收取中间款项，请设置。为多期时，请点击“增加一期”</div>
                                                </div>
                                                <span class="ty-right def-btn funBtn withBold addOne" data-fun="no_addMore">增加一期</span>
                                            </div>
                                            <div class="price-box">
                                                <span class="gapRt0 amountType">
                                                    金额<input need name="amount" type="text"/>元
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addServiceSure()">确 定</span>
            </div>
        </div>
        <%--新增 套餐 1--%>
        <div class="bonceContainer bounce-blue" id="mealInitChoose">
            <div class="bonceHead">
                <span>新增套餐</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="narrowBody">
                    <p>本项目是否按周期（如按年、按月）收费？</p>
                    <div class="clear dotItem">
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="isdur" data-val="1"></i>是
                        </div>
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="isdur" data-val="0"></i>否
                        </div>
                    </div>
                </div>
                <div class="narrowBody">
                    <p>套餐内须含项目。除项目外，该套餐是否包含商品？</p>
                    <div class="clear dotItem">
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="isgoods" data-val="1"></i>是
                        </div>
                        <div class="changeDot">
                            <i class="fa fa-circle-o" data-type="isgoods" data-val="0"></i>否
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 funBtn" data-fun="mealInitChoose">确 定</span>
            </div>
        </div>
        <%--新增 套餐2--%>
        <div class="bonceContainer bounce-blue" id="addMealService" style="width:840px;">
            <div class="bonceHead">
                <span>新增套餐</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon specialForm">
                <div style="padding: 0 20px;">
                    <div class="headTip">请录入按周期收费且包含商品的套餐！</div>
                    <div class="请定义本套餐">
                        <div>请定义本套餐</div>
                        <div class="ty-color-blue">注：此处录入的数据可供不同的服务合同重复引用。</div>
                    </div>
                    <table class="ty-table ty-table-control byCycle">
                        <tbody>
                        <tr>
                            <td width="20%">代号<i class="xing"></i></td>
                            <td width="40%">名称<i class="xing"></i></td>
                            <td width="40%">说明</td>
                        </tr>
                        <tr>
                            <td><input need id="code_sv" class="form-control" name="code" type="text" require /></td>
                            <td><input need id="name_sv" class="form-control" name="name" type="text" placeholder="此处最多可录入15字" maxlength="15" require onkeyup="limitWord($(this), 15)"/></td>
                            <td><input need id="desc_sv" class="form-control" name="memo" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this), 100)"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <hr>
                    <div class="goods">
                        <p>
                            <span>套餐所包含的内容</span>
                            <span class="linkBtn ty-right" data-fun="addProject">添加项目</span>
                            <span class="linkBtn ty-right" id="addGoodsBtn" data-fun="addGoods">添加商品</span>
                        </p>

                    </div>
                    <div class="prijectOrGoods prijectOrGoods1">
                        <div>项目</div>
                        <div>
                            <table class="ty-table ty-table-control" id="serviceTab">
                                <tr>
                                    <td>服务项目代号/名称</td>
                                    <td>参考单价</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <span class="ty-color-blue">修改数量</span>
                                        <span class="ty-color-blue">删除</span>
                                        <span class="hd"></span>
                                    </td>
                                </tr>
                                <tr class="guDing">
                                    <td colspan="3">原价合计</td>
                                    <td colspan="2" class="sumAll" id="sumAll"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="prijectOrGoods prijectOrGoods2">
                        <div style="margin:15px 0 10px;">仅第一个收费周期需提供的商品</div>
                        <div>
                            <table class="ty-table ty-table-control" id="goodsTab">
                                <tr>
                                    <td>商品代号名称</td>
                                    <td>参考单价</td>
                                    <td>发货时间</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                <tr>
                                    <td>商品代号名称</td>
                                    <td>参考单价</td>
                                    <td>发货时间</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>
                                        <span class="ty-color-blue">查看</span>
                                        <span class="ty-color-blue">修改</span>
                                        <span class="ty-color-blue">删除</span>
                                        <span class="hd"></span>
                                    </td>
                                </tr>
                                <tr class="guDing">
                                    <td colspan="4">原价合计</td>
                                    <td colspan="2" class="sumAll" id="sumAll2"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="prijectOrGoods prijectOrGoods3">
                        <div style="margin:15px 0 10px;">每个收费周期都需提供的商品</div>
                        <div>
                            <table class="ty-table ty-table-control" id="goodsTab2">
                                <tr>
                                    <td>商品代号名称</td>
                                    <td>参考单价</td>
                                    <td>发货时间</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>操作</td>
                                </tr>
                                <tr>
                                    <td>商品代号名称</td>
                                    <td>参考单价</td>
                                    <td>发货时间</td>
                                    <td>数量</td>
                                    <td>原价金额</td>
                                    <td>
                                        <span class="ty-color-blue">查看</span>
                                        <span class="ty-color-blue">修改</span>
                                        <span class="ty-color-blue">删除</span>
                                        <span class="hd"></span>
                                    </td>
                                </tr>
                                <tr class="guDing">
                                    <td colspan="4">原价合计</td>
                                    <td colspan="2" class="sumAll" id="sumAll3"></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <hr>

                    <div class="priceTp">
                        <div>价格<i class="xing"></i></div>
                        <div class="ty-color-blue">注：下表内各种单价中，不涉及的可以不录，但至少需录入一种。</div>
                    </div>
                    <table class="ty-table ty-table-control priceForm">
                        <tbody>
                        <tr>
                            <td width="40%" colspan="3">开增值税专用发票时</td>
                            <td width="20%" rowspan="2">开普票时的开票单价</td>
                            <td width="20%" rowspan="2">不开发票时的单价</td>
                            <td width="20%" rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr class="priceSetGroup">
                            <td class="priceSet rateDot">
                                <select id="taxRate" class="rate" name="taxRate" onchange="setprice(3, $(this))">
                                </select>
                            </td>
                            <td class="priceSet noPriceDot"><input id="unitPriceNotax" class="noPrice" need name="unitPriceNotax" type="text" oninput="clearNoNumN(this, 2);setprice(1, $(this))"  /></td>
                            <td class="priceSet priceDot"><input id="unitPrice" class="price" need name="unitPrice" type="text" oninput="clearNoNumN(this, 2);setprice(2, $(this))"  /></td>
                            <td><input need id="unitPriceInvoice" type="text"/></td>
                            <td><input need id="unitPriceNoinvoice" type="text"/></td>
                            <td><input need id="unitPriceReference" type="text"/></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="6"><input class="form-control" id="priceDesc" type="text" placeholder="此处最多可录入100字" onkeyup="limitWord($(this))"/></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="byCycle gapTp">
                        <div class="origin" id="periodDurationInfo">
                            <span class="gapRt">收费周期<i class="xing"></i></span>
                            每
                            <select class="entry" id="periodDuration" >
                                <option value=""></option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                            </select>
                            <select class="entry" id="periodUnit"  onchange="switchPeriod($(this))">
                                <option value="">请选择</option>
                                <option value="7">年</option>
                                <option value="4">个月</option>
                            </select>
                            收费一次
                        </div>
                        <div class="origin">
                            <span class="gapRt">收费时间<i class="xing"></i></span>
                            <select class="entry" id="chargeStage" style="margin-left: 17px;">
                                <option value="">请选择</option>
                                <option value="1">合同签订后</option>
                                <option value="2">服务开始前</option>
                                <option value="3">服务开始后</option>
                                <option value="13">服务结束后</option>
                            </select>
                            <input need id="chargeLimit" type="text" placeholder="请填写"/>天内
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 funBtn" data-fun="addMealServiceSure">确 定</span>
            </div>
        </div>
        <%--  查看 套餐  --%>
        <div class="bonceContainer bounce-blue" id="serviceScan" style="width: 1000px;">
            <div class="bonceHead">
                <span>查看套餐</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon" style="padding:25px;">
                <div>
                    <p id="re1">本套餐按周期收费。</p>
                    <p style="text-align: right" id="cre1"></p>
                    <p>
                        套餐的基础信息与价格信息
                    </p>
                    <table class="ty-table">
                        <tr><td>代号</td><td colspan="3">名称</td><td colspan="2">说明</td></tr>
                        <tr>
                            <td id="s_code">代号</td>
                            <td colspan="3" id="s_name">名称</td>
                            <td colspan="2" id="s_desc">说明</td>
                        </tr>
                        <tr>
                            <td colspan="3">开增值税专用发票时</td>
                            <td rowspan="2">开普票时的开票单价</td>
                            <td rowspan="2">不开发票时的单价</td>
                            <td rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr>
                            <td id="s_rate"></td>
                            <td id="s_price1"></td>
                            <td id="s_price2"></td>
                            <td id="s_price3"></td>
                            <td id="s_price4"></td>
                            <td id="s_price5"></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="5" id="s_priceDesc">价格说明</td>
                        </tr>
                    </table>
                    <hr>
                    <p>套餐所包含的内容</p>
                    <div class="s_tbc_1">
                        <p>项目</p>
                        <table class="ty-table" id="s_tab1">
                            <tr>
                                <td>服务项目代号/名称</td>
                                <td>参考单价</td>
                                <td>数量</td>
                                <td>原价金额</td>
                            </tr>
                            <tr class="guDing">
                                <td colspan="3">原价合计</td>
                                <td id="sTab1Sum" class="sumAll"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="s_tbc_2">
                        <p>仅第一个收费周期需提供的商品</p>
                        <table class="ty-table" id="s_tab2">
                            <tr>
                                <td>商品项目代号/名称</td>
                                <td>参考单价</td>
                                <td>发货时间</td>
                                <td>数量</td>
                                <td>原价金额</td>
                            </tr>
                            <tr>
                                <td>商品项目代号/名称</td>
                                <td>参考单价</td>
                                <td>发货时间</td>
                                <td>数量</td>
                                <td>原价金额</td>
                            </tr>
                            <tr class="guDing">
                                <td colspan="4">原价合计</td>
                                <td id="sTab2Sum" class="sumAll"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="s_tbc_3">
                        <p>每个收费周期都需提供的商品</p>
                        <table class="ty-table" id="s_tab3">
                            <tr>
                                <td>商品项目代号/名称</td>
                                <td>参考单价</td>
                                <td>发货时间</td>
                                <td>数量</td>
                                <td>原价金额</td>
                            </tr>
                            <tr>
                                <td>商品项目代号/名称</td>
                                <td>参考单价</td>
                                <td>发货时间</td>
                                <td>数量</td>
                                <td>原价金额</td>
                            </tr>
                            <tr class="guDing">
                                <td colspan="4">原价合计</td>
                                <td id="sTab3Sum" class="sumAll"></td>
                            </tr>
                        </table>
                    </div>

                    <hr>
                    <div class="rtCon">
                        <p id="s_feeZhouC">
                            收费周期 <span id="s_feeZhou">每XXXX收取一次</span>
                        </p>
                        <p>
                            收费时间<span id="s_feeTime"></span>
                        </p>

                    </div>

                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); " >关 闭</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="editCollectionTime" style="width:1020px;">
        <div class="bonceHead bounce-blue">
            <span>设置收款时间节点</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrW">
                <p class="topTip">本项目收款时间节点共如下<span id="timeSum"></span>个，请设置各时间节点的具体日期</p>
                <div class="itemBox conBody">
                    <div class="clear">
                        <p class="orgCare">合同签订后XX天内需收XXX（预付款）</p>
                        <div class="modItem">
                            <p>
                                合同签订的日期
                            </p>
                            <input class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                        <div class="modItem">
                            <p>
                                应收金额<span class="thin-btn ty-right" data-fun="editRentPay">修改</span>
                            </p>
                            <input class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                    </div>
                    <div class="clear">
                        <p class="orgCare">本期交付前XX天内需收XXX（交付/验收环节1的名称） <span class="rt-arrow">></span></p>
                        <div class="modItem">
                            <p>
                                XXXXXX的日期
                            </p>
                            <input class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                        <div class="modItem">
                            <p>
                                应收金额<span class="thin-btn ty-right" data-fun="editRentPay">修改</span>
                            </p>
                            <input class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                    </div>
                    <div class="clear">
                        <p class="orgCare">本期验收通过后XX天内需收XXX（交付/验收环节2的名称） <span class="rt-arrow">></span></p>
                        <div class="modItem">
                            <p>
                                XXXXXX的日期
                            </p>
                            <input class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                        <div class="modItem">
                            <p>
                                应收金额<span class="thin-btn ty-right" data-fun="editRentPay">修改</span>
                            </p>
                            <input class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                    </div>
                    <div class="clear">
                        <p class="orgCare">最终交付后XX天内需收XXX（尾款）</p>
                        <div class="modItem">
                            <p>
                                XXXXXX的日期
                            </p>
                            <input class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                        <div class="modItem">
                            <p>
                                应收金额<span class="thin-btn ty-right" data-fun="editRentPay">修改</span>
                            </p>
                            <input class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                    </div>
                    <div class="clear surplusSect">
                        <div class="surplusTip"></div>
                        <div class="modItem">
                            <p>
                                <span class="surplusTtl"></span><i class="xing"></i>
                            </p>
                            <input id="surplusDate" class="form-control" value="" placeholder="请录入" name="address" require/>
                        </div>
                        <div class="modItem">
                            <p>
                                剩余款项的金额<i class="xing"></i>
                            </p>
                            <div class="gray-box">
                                <span class="priceItem"></span>
                                <span>(占合同总金额的<span class="percentage"></span>%)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="editCollectionTimeSure()">确 定</span>
        </div>
    </div>
    <%-- 开票要求 编辑--%>
    <div class="bonceContainer bounce-blue" id="invoiceSet">
        <div class="bonceHead">
            <span>编辑开票要求</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-left:60px ">
                <p>本订单各商品需开具什么发票？请选择</p>
                <p class="setItem" data-type="1"><i class="fa fa-circle-o"></i><span>需开具增值税专用发票</span></p>
                <p class="setItem" data-type="2"><i class="fa fa-circle-o"></i><span>需开具其他发票</span></p>
                <p class="setItem" data-type="3"><i class="fa fa-circle-o"></i><span>不开发票</span></p>
                <p class="tipsmall">注：您新增的某订单上，只能录入开具同种发票的商品！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="invoiceSetOk()">确定</span>
        </div>
    </div>
    <%--新增、修改、续约 合同--%>
    <div class="bonceContainer bounce-green" id="newContractInfo" style="width: 450px">
        <div class="bonceHead">
            <span>编辑合同文本方面的信息</span>
            <a class="bounce_close" onclick="editContractOk(0)"></a>
        </div>
        <div class="bonceCon linkUploadify">
            <div class="flex-box">
                <div class="citem">
                    <p><i class="red">*</i> 合同编号</p>
                    <input type="text" placeholder="请录入" class="cNo">
                </div>
                <div class="citem">
                    <p><i class="red">*</i>签署日期</p>
                    <input type="text" placeholder="请选择" readonly class="cSignDate">
                </div>
            </div>
            <div class="flex-box">
                <div class="citem">
                    <p>合同的有效期</p>
                    <input type="text" placeholder="请选择" readonly class="cStartDate">
                </div>
                <div class="citem">
                    <p>&nbsp;</p>
                    <span>至</span>
                </div>
                <div class="citem">
                    <p>&nbsp;</p>
                    <input type="text" placeholder="请选择" readonly class="cEndDate">
                </div>
            </div>
            <div class="citem2">
                <p>合同的扫描件或照片(共可上传9张) <span class="linkBtn ty-right" id="cUpload1"></span></p>
                <div class="fileCon">
                    <div class="fileCon1"></div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div class="citem2">
                <p>合同的可编辑版 <span class="linkBtn ty-right" id="cUpload2"></span></p>
                <div class="fileCon ">
                    <div class="fileCon2"></div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div class="citem2">
                <p>备注</p>
                <input class="cMemo" type="text" style="width:420px; " placeholder="请录入">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="editContractOk(0)">取 消</span>
            <button class="ty-btn ty-btn-green ty-btn-big funBtn" id="editContractOk" data-fun="editContractOk">确 定</button>
        </div>
    </div>
    <%--改选其他模板--%>
    <div class="bonceContainer bounce-blue" id="reTemplateTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>确定后，已选模板及相应数据将被清空。</p>
            <p>确定继续本操作吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取 消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="reTemplateSure()">确 定</span>
        </div>
    </div>
    <%--在项目模板中选择--%>
    <div class="bonceContainer bounce-blue" id="proTemplate" style="width: 1000px;">
        <div class="bonceHead">
            <span>在项目模板中选择</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bw-alert">
                <span>以下项目模板中如没有本合同要选的，可新增项目模板。</span>
                <span class="linkBtn funBtn" data-fun="addServiceProject">新增项目模板</span>
            </div>
            <table class="ty-table ty-table-control specialTb">
                <tbody>
                <tr>
                    <td>选择</td>
                    <td>项目代号/名称</td>
                    <td>价格</td>
                    <td>费用的收取方式</td>
                    <td>创建时间</td>
                    <td>操作</td>
                </tr>
                </tbody>
            </table>
            <div id="template1"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="templateSetOk(1)">确定</span>
        </div>
    </div>
    <%--在套餐模板中选择--%>
    <div class="bonceContainer bounce-blue" id="mealTemplate" style="width: 1000px;">
        <div class="bonceHead">
            <span>在套餐模板中选择</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bw-alert">
                <span>以下套餐模板中如没有本合同要选的，可新增套餐模板。</span>
                <span class="linkBtn" data-fun="addServiceMeal">新增套餐模板</span>
            </div>
            <table class="ty-table ty-table-control specialTb" id="otherMaterial">
                <tbody>
                <tr>
                    <td>选择</td>
                    <td>套餐代号/名称</td>
                    <td>价格</td>
                    <td>费用的收取方式</td>
                    <td>创建时间</td>
                    <td>操作</td>
                </tr>
                </tbody>
            </table>
            <div id="template2"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="templateSetOk(2)">确定</span>
        </div>
    </div>
    <%--本合同所涉及的商品--%>
    <div class="bonceContainer bounce-blue" id="productConScan" style="width: 850px;">
        <div class="bonceHead">
            <span>本合同所涉及的商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="min-height: 100px;">
            <div>
                <table class="ty-table">
                    <tbody>
                    <tr>
                        <td>商品代号/商品名称</td>
                        <td>应发货数量</td>
                        <td>发货时间的要求</td>
                        <td>备注</td>
                        <td>操作</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="productConEdit" style="width: 850px;">
        <div class="bonceHead">
            <span>本合同所涉及的商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <div>以下为本合同所涉及的<span id="productNum"></span>种商品，对于收货地址栏为可编辑状态的，请根据合同实际情况进行编辑。</div>
                <table class="ty-table">
                    <tr>
                        <td>商品代号/商品名称</td>
                        <td>应发货数量</td>
                        <td>发货时间的要求</td>
                        <td>备注</td>
                        <td>收货地址<i class="xing"></i></td>
                        <td>操作</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addProductAddress()">确 定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="deliveryTimeSet" style="width: 600px;">
        <div class="bonceHead">
            <span>发货时间节点的设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowA">
                <p>本合同所选套餐中，需发货商品的发货时间取决于以下日期的设置，请按合同进行设置。</p>
                <div class="itemC">
                    <p>合同签订的日期<i class="xing"></i></p>
                    <input type="text" class="form-control contract_signTime" placeholder="请选择">
                </div>
                <div class="itemC">
                    <p>服务开始的日期<i class="xing"></i></p>
                    <input type="text" class="form-control contract_beginTime" placeholder="请选择">
                </div>
                <div class="itemC">
                    <p>服务结束的日期<i class="xing"></i></p>
                    <input type="text" class="form-control contract_endTime" placeholder="请选择">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取 消</span>
            <span  class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="deliveryTimeSure()">确 定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--新增服务合同--%>
    <div class="bonceContainer bounce-blue" id="newServiceContract" style="width:1050px;">
        <div class="bonceHead bounce-blue">
            <span>新增服务合同</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addContractForm">
                <div class="modInfo clear low-line customersInfo">
                    <div class="modItem-m">
                        <p>客户名称<i class="xing"></i></p>
                        <div class="customersSearch">
                            <input type="text" id="cusSearchInput" require class="form-control customerName" onchange="" />
                            <div class="cusSearchItems">  </div>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <div class="ty-left">
                            <p>客户代号</p>
                            <input type="text" class="form-control customerId" disabled="disabled">
                        </div>
                        <div class="ty-right">
                            <p>合同金额</p>
                            <input class="form-control contractAmount" value="" name="amount" disabled/>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <p>合同收到的日期<i class="xing"></i></p>
                        <input class="form-control" value="" placeholder="请选择" name="receiveTime" id="contractReceiveTime" require/>
                        <input type="hidden" value="2" name="contractType" />
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                    <div class="modItem-m">
                        <p>
                            开票要求<i class="xing"></i>
                            <span class="ty-right linkBtn uphide" data-fun="invoiceRequireEdit">编辑</span>
                        </p>
                        <input type="text" class="form-control invoiceRequire"  require readonly onclick="invoiceRequireEdit()">
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                </div>
                <div>
                    <div class="clear">
                        <div class="ty-left">
                            <div class="noSelectMod con-txt">
                                请根据合同实际情况选择一个合适的项目或套餐模板。
                            </div>
                            <div class="selectedMod con-txt">
                                <div>您已选择了一个模板，请实际情况填写以下各项内容。</div>
                                <div>如需要，可重选模板，但已选模板及相应数据将被清空。</div>
                            </div>
                        </div>
                        <div class="ty-right selectBtns">
                            <span class="linkBtn" onclick="getTemplate(1, 1)">在项目模板中选择</span>
                            <span class="linkBtn" onclick="getTemplate(1, 2)">在套餐模板中选择</span>
                        </div>
                    </div>
                    <div class="careful noSelectMod">注：本系统新增合同时只能选一个项目，或一个套餐。其他需求，可通过设置新的项目或套餐来实现。</div>
                </div>
                <div class="serviceTemplateInfo">
                    <div class="serviceInfo hd"></div>
                    <div class="customerAddressCon hd"></div>
                    <div class="modInfo clear priceSetGroup s_contact s_contact1">
                        <div class="modItem-m">
                            <p>
                                项目名称
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="name" disabled/>
                        </div>
                        <div class="modItem-m">
                            <p>
                                项目代号
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="code" disabled/>
                        </div>
                        <div class="modItem-m">
                            <p>
                                合同文本方面的信息<i class="xing"></i>
                                <span class="ty-right linkBtn" data-fun="contractInfoEdit">编辑</span>
                            </p>
                            <div class="gray-box onContractText"></div>
                            <span class="hd"></span>
                        </div>
                        <div class="modItem-m">
                            <p>
                                费用的收取方式
                            </p>
                            <div class="gray-box chargingMethod"></div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                价格说明
                            </p>
                            <input class="ty-inputText" value="" name="priceDesc" disabled/>
                        </div>
                        <div class="modItem-m priceSet noPriceDot">
                            <p>
                                不含税价<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNotax" disabled require oninput="clearNoNumN(this, 2);setprice(1, $(this))"/>
                            </div>
                        </div>
                        <div class="modItem-m priceSet rateDot" style="margin-right: 80px;">
                            <p>
                                税率<i class="xing"></i>
                            </p>
                            <div>
                                <select class="ty-inputText" name="taxRate" require onchange="setprice(3, $(this))"></select>
                            </div>
                        </div>
                        <div class="modItem-m priceSet priceDot">
                            <p>
                                含税价<i class="xing"></i>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPrice" oninput="clearNoNumN(this, 2);setprice(2, $(this))" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m priceSet">
                            <p>
                                开普通发票的开票价<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceInvoice" disabled require/>
                        </div>
                        <div class="modItem-m priceSet">
                            <p>
                                不开发票的价格<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNoinvoice" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                已设置的收费时间
                            </p>
                            <div class="gray-box chargeStage"></div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                <span class="chargeStageTtl"></span>的日期<i class="xing"></i>
                            </p>
                            <div>
                                <input class="ty-inputText chargeStageDate" value="" placeholder="请录入" name="chargeTime" require/>
                            </div>
                        </div>
                    </div>
                    <div class="modInfo clear priceSetGroup s_contact s_contact2">
                        <div class="modItem-m">
                            <p>
                                项目名称/代号
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="name" disabled/>
                        </div>
                        <div class="modItem-m">
                            <p>
                                合同文本方面的信息<i class="xing"></i>
                                <span class="ty-right linkBtn" data-fun="contractInfoEdit">编辑</span>
                            </p>
                            <div class="gray-box onContractText"></div>
                            <span class="hd"></span>
                        </div>
                        <div class="modItem-m">
                            <p>
                                费用的收取方式
                            </p>
                            <div class="gray-box chargingMethod"></div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                价格说明
                            </p>
                            <input class="ty-inputText" value="" name="priceDesc" disabled/>
                        </div>
                        <div class="modItem-m priceSet rateDot">
                            <p>
                                税率<i class="xing"></i>
                            </p>
                            <div>
                                <select class="ty-inputText" name="taxRate" onchange="setprice(3, $(this))" require></select>
                            </div>
                        </div>
                        <div class="modItem-m priceSet noPriceDot">
                            <p>
                                不含税价<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNotax" disabled require oninput="clearNoNumN(this, 2);setprice(1, $(this))"/>
                            </div>
                        </div>
                        <div class="modItem-m priceSet priceDot">
                            <p>
                                含税价<i class="xing"></i>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPrice" oninput="clearNoNumN(this, 2);setprice(2, $(this))" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m priceSet" style="margin-right: 80px;">
                            <p>
                                开普通发票的开票价<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceInvoice" disabled require/>
                        </div>
                        <div class="modItem-m priceSet" style="margin-right: 80px;">
                            <p>
                                不开发票的价格<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNoinvoice" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m add_modeNoSetted">
                            <p>
                                收款日期<i class="xing"></i>
                            </p>
                            <div>
                                <input class="ty-inputText chargeStageDate" value="" placeholder="请录入" name="chargeTime" require/>
                                <div class="orgLightCare">注：此日期需选为已签署合同中的收款日期。选定后，该日期到来前5日将您将收到系统消息。</div>
                            </div>
                        </div>
                        <div class="modItem-m add_modeSetted">
                            <p>
                                收款时间节点<i class="xing"></i>
                                <span class="ty-right linkBtn" data-fun="collectionTimeSet">设置</span>
                            </p>
                            <div class="gray-box collectionTime"></div>
                            <div class="hd collectionTimeData"></div>
                        </div>
                    </div>
                    <div class="modInfo clear priceSetGroup s_contact s_contact3">
                        <div class="modItem-m">
                            <p>
                                套餐名称/代号
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="name" disabled/>
                        </div>
                        <div class="modItem-m">
                            <p>
                                合同文本方面的信息<i class="xing"></i>
                                <span class="ty-right linkBtn" data-fun="contractInfoEdit">编辑</span>
                            </p>
                            <div class="gray-box onContractText"></div>
                            <span class="hd"></span>
                        </div>
                        <div class="modItem-m">
                            <p>
                                费用的收取方式
                            </p>
                            <div class="gray-box chargingMethod"></div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                价格说明
                            </p>
                            <input class="ty-inputText" value="" name="priceDesc" disabled/>
                        </div>
                        <div class="modItem-m priceSet rateDot">
                            <p>
                                税率<i class="xing"></i>
                            </p>
                            <div>
                                <select class="ty-inputText" name="taxRate" onchange="setprice(3, $(this))" disabled require></select>
                            </div>
                        </div>
                        <div class="modItem-m priceSet noPriceDot">
                            <p>
                                不含税价<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNotax" oninput="clearNoNumN(this, 2);setprice(1, $(this))" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m priceSet priceDot">
                            <p>
                                含税价<i class="xing"></i>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPrice" oninput="clearNoNumN(this, 2);setprice(2, $(this))" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m priceSet" style="margin-right: 80px">
                            <p>
                                开普通发票的开票价<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceInvoice" disabled require/>
                        </div>
                        <div class="modItem-m priceSet" style="margin-right: 80px">
                            <p>
                                不开发票的价格<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNoinvoice" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                本合同所涉及的商品
                                <span class="ty-right linkBtn" data-fun="productConScan">查看</span>
                            </p>
                            <div>
                                <div class="gray-box productCon"></div>
                            </div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                已设置的收费时间
                            </p>
                            <div class="gray-box chargeStage"></div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                <span class="chargeStageTtl"></span>的日期<i class="xing"></i>
                            </p>
                            <input class="ty-inputText chargeStageDate" value="" placeholder="请录入" name="chargeTime" require/>
                        </div>
                    </div>
                    <div class="modInfo clear priceSetGroup s_contact s_contact4">
                        <div class="modItem-m">
                            <p>
                                套餐名称
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="name" disabled/>
                        </div>
                        <div class="modItem-m">
                            <p>
                                套餐代号
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="code" disabled />
                        </div>
                        <div class="modItem-m">
                            <p>
                                合同文本方面的信息<i class="xing"></i>
                                <span class="ty-right linkBtn" data-fun="contractInfoEdit">编辑</span>
                            </p>
                            <div class="gray-box onContractText"></div>
                            <span class="hd"></span>
                        </div>
                        <div class="modItem-m">
                            <p>
                                费用的收取方式
                            </p>
                            <div class="gray-box chargingMethod"></div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                价格说明
                            </p>
                            <input class="ty-inputText" value="" name="priceDesc" disabled/>
                        </div>
                        <div class="modItem-m priceSet rateDot">
                            <p>
                                税率<i class="xing"></i>
                            </p>
                            <div>
                                <select class="ty-inputText" name="taxRate" onchange="setprice(3, $(this))" disabled require></select>
                            </div>
                        </div>
                        <div class="modItem-m priceSet noPriceDot">
                            <p>
                                不含税价<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNotax" oninput="clearNoNumN(this, 2);setprice(1, $(this))" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m priceSet priceDot">
                            <p>
                                含税价<i class="xing"></i>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPrice" oninput="clearNoNumN(this, 2);setprice(2, $(this))" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m priceSet" style="margin-right: 0">
                            <p>
                                开普通发票的开票价<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceInvoice" disabled require/>
                        </div>
                        <div class="modItem-m priceSet" style="margin-right: 0">
                            <p>
                                不开发票的价格<i class="xing"></i>
                                <span class="ty-right linkBtn editPrice" data-fun="editPrice">修改价格</span>
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNoinvoice" disabled require/>
                            </div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                已设置的收费时间
                            </p>
                            <div class="gray-box chargeStage"></div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                收款与发货的时间节点<i class="xing"></i>
                                <span class="ty-right linkBtn" data-fun="deliveryTimeSet">设置</span>
                            </p>
                            <div class="gray-box deliveryTime"></div>
                            <div class="hd"></div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                本合同所涉及的商品
                                <span class="ty-right linkBtn" data-fun="productConScan">查看</span>
                            </p>
                            <div>
                                <div class="gray-box productCon"></div>
                                <div class="hd"></div>
                            </div>
                        </div>
                        <div class="modItem-m">
                            <p>
                                客户的收款地址<i class="xing"></i>
                                <span class="ty-right linkBtn" data-fun="productConEdit">设置</span>
                            </p>
                            <div class="gray-box productPlace"></div>
                            <div class="hd"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="newServiceContractSure()">确 定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>服务合同</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <input type="hidden" id="showMainConNum">
                    <div class="mainCon mainCon1">
                        <div class="panel-box">
                            <div class="bw-alert">
                                <div>带有服务项目的合同纳入系统管理，需在本模块录入。</div>
                                <div class="btnSize">
                                    <span class="funBtn ty-btn ty-btn-big ty-btn-blue" data-fun="addServiceContract">新增服务合同</span>
                                    <span class="ty-btn ty-btn-big ty-btn-gray" data-fun="stoppedService">待补发的服务合同</span>
                                    <span class="ty-btn ty-btn-big ty-btn-gray" data-fun="stoppedService">已完结的服务合同</span>
                                    <span class="funBtn ty-btn ty-btn-big ty-btn-blue" data-fun="stoppedService">已终止的服务合同</span>
                                </div>
                            </div>
                            <div class="bw-alert">
                                <div>
                                    处于进程中服务合同共有 <span id="mainsNum"></span>个，具体如下表所示。
                                    <p class="careful">注：下表中，各项百分比均为金额的百分比。</p>
                                </div>
                                <div>
                                    <span class="search">查找
                                    <input type="text" placeholder="客户名称/客户代号/服务合同号" id="search1"><span class="funBtn" data-fun="searchList">确 定</span>
                                </span>
                                </div>
                            </div>
                            <table class="ty-table ty-table-control" id="main1Tab">
                                <thead>
                                <tr>
                                    <td>合同号</td>
                                    <td>客户</td>
                                    <td>签署日期</td>
                                    <td>有效期</td>
                                    <td>在系统中的创建时间</td>
                                    <td>综合管理</td>
                                    <td>已交付</td>
                                    <td>已验收</td>
                                    <td>已开票</td>
                                    <td>已回款</td>
                                </tr>
                                </thead>
                                <tbody>

                                </tbody>
                            </table>
                            <div id="ye1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/sales/serviceContract.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
