<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/sale.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/cashBack/cashBack.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    #mtTip span,#mtTip p{font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;}
    #mtTip p{margin:0;}
    .bonceHead{font-size:16px;font-weight: normal;}
    .bonceContainer {font-size: 14px;}
    .receive{  margin: 5px 0;  }

</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="tip" style="width: 450px">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p class="tipMs" style="text-align: center; padding:10px 0 8px"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--修改记录--%>
    <div class="bonceContainer bounce-blue" id="updateRecords">
        <div class="bonceHead">
            <span class="recordTtl">修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <div class="createRecord clear">
                <div class="clear">
                    <p class="ty-left recordTip">当前资料尚未经修改。</p>
                    <p class="ty-right recordEditer"></p>
                </div>
            </div>
            <table class="ty-table ty-table-control changeRecord">
                <thead>
                <tr>
                    <td>记  录</td>
                    <td>操  作</td>
                    <td>创建者/修改者</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--联系方式查看--%>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>联系方式</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>
            <ul class="see_otherContact">
            </ul>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--名片查看--%>
    <div class="bonceContainer bounce-blue" id="visitCardDetail">
        <div class="bonceHead">
            <span>名片</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="see_contactsCard">

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--停权提示--%>
    <div class="bonceContainer bounce-red" id="recallPower" style="width:400px;">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <p class="handleCenter"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5"  onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-5" id="recallPowerSure" onclick="recallPowerSure()">确定</span>
        </div>
    </div>
    <%--增加回款录入对象--%>
    <div class="bonceContainer bounce-green" id="addCollectionEntryObject" style="width:600px;">
        <div class="bonceHead">
            <span>增加回款录入对象</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <table class="ty-table ty-table-control objectSelect">
                <thead>
                <tr>
                    <td colspan="2" style="text-align: left;">请在下列客户中选择</td>
                    <td><span id="quickBtn" class="ty-color-blue">全选</span></td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="objectSelectSure" onclick="objectSelectSure()">确定</button>
        </div>
    </div>
    <%-- 暂停合作/恢复合作的操作记录 --%>
    <div class="bonceContainer bounce-blue" id="suspendRecord">
        <div class="bonceHead">
            <span>暂停合作/恢复合作的操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>操作性质</td>
                    <td>操作者</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-red" id="mtTip" style="width: 400px;">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center;font-size:14px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="seeCustomer" style="min-width:1220px;">
        <div class="bonceHead">
            <span>客户信息查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails sale_see">
                <div class="part">
                    <div class="item">
                        <div class="item"><span class="item_title">客户名称：</span><span class="item_content" id="see_cusname" style="width: 600px"></span></div>
                        <div class="item"><span class="item_title">客户代号：</span><span class="item_content" id="see_cuscoding"></span></div>
                    </div>
                    <div class="item">
                        <div class="item"><span class="item_title">地址：</span><span class="item_content" id="see_address" style="width: 600px"></span></div>
                        <div class="item"><span class="item_title">客户简称：</span><span class="item_content" id="see_cusfullName"></span></div>
                    </div>
                    <%--<div class="clear rowGap"><span class="ty-left" id="see_address"></span></div>--%>
                    <div class="specialOrgPart">最高负责人 <span id="see_supervisorName"></span> <span id="see_supervisorMobile"></span>（手机号）</div>
                    <div class="firstBuyTime"></div>
                    <div class="item">
                        <div class="item"><span class="item_title">创建者：</span><span class="item_content"><span id="see_createName"></span> <span id="see_createDate"></span></span></div>
                        <div class="right_btn"><button class="ty-btn ty-btn-blue ty-circle-3"  onclick="getSuspendRecordList($(this))">暂停合作/恢复合作的操作记录</button></div>
                        <div class="right_btn"><button class="ty-btn ty-btn-blue ty-circle-3"  data-type="baseRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</button></div>
                    </div>
                </div>
                <div class="part">
                    <div class="item">
                        <div class="item_title">全景照片：</div>
                        <div class="item_content" id="overallImgUpload"></div>
                    </div>
                    <div class="item">
                        <div class="item_title">产品图片：</div>
                        <div class="item_content" id="productImgUpload"></div>
                    </div>
                    <div class="item">
                        <div class="item">
                            <div class="item_title">首次接触时间：</div>
                            <div class="item_content" id="see_firstContactTime"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">首次接触地点：</div>
                            <div class="item_content" id="firstContactAddress"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">信息获取渠道：</div>
                            <div class="item_content" id="infoSource"></div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item_title">备注：</div>
                        <div class="item_content" id="see_memo"></div>
                    </div>

                </div>
                <div class="part">
                    <div class="item">
                        <div class="item_theme"><span class="title">开票信息</span></div>
                        <div class="right_btn"><button class="ty-btn ty-btn-blue ty-circle-3"  data-type="invoiceRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</button></div>
                    </div>
                    <div class="item">
                        <div class="item">
                            <div class="item_title">公司名称：</div>
                            <div class="item_content" id="see_invoiceName"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">地址：</div>
                            <div class="item_content" id="see_invoiceAddress"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">电话：</div>
                            <div class="item_content" id="see_phone"></div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item">
                            <div class="item_title">开户行：</div>
                            <div class="item_content" id="see_bank"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">账号：</div>
                            <div class="item_content" id="see_bankNo"></div>
                        </div>
                        <div class="item">
                            <div class="item_title">税号：</div>
                            <div class="item_content" id="taxpayerID"></div>
                        </div>
                    </div>
                </div>
                <div class="part_end">
                    <%--<div class="ty-alert ty-alert-warning">联系人、收货信息或发票邮寄信息中，红色字体者均为被停用的。</div>--%>
                    <div class="item_avatar">
                        <div class="item">
                            <div class="item_theme"><span class="title">联系人</span></div>
                            <div class="item_content">
                                该客户现共有如下<span class="contactNum"></span>位联系人
                                <div class="see_contactList"></div>
                            </div>
                        </div>
                    </div>
                    <div class="item_avatar">
                        <div class="item">
                            <div class="item_theme"><span class="title">收货信息</span></div>
                        </div>
                        <div class="item">
                            <div class="item_title"></div>
                            <div class="item_content">
                                <table class="ty-table ty-table-control recivePlaceList">
                                    <thead>
                                    <tr>
                                        <td width="10%">序号</td>
                                        <td width="30%">收货地址</td>
                                        <td width="20%">收货人</td>
                                        <td width="20%">收货电话</td>
                                        <td width="20%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="item_avatar">
                        <div class="item">
                            <div class="item_theme"><span class="title">发票邮寄信息</span></div>
                        </div>
                        <div class="item">
                            <div class="item_title"></div>
                            <div class="item_content">
                                <table class="ty-table ty-table-control mailPlaceList">
                                    <thead>
                                    <tr>
                                        <td width="10%">序号</td>
                                        <td width="30%">邮寄地址</td>
                                        <td width="20%">发票接收人</td>
                                        <td width="20%">邮政编码</td>
                                        <td width="20%">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--更换销售负责人--%>
    <div class="bonceContainer bounce-green" id="changeSaler">
        <div class="bonceHead">
            <span>更换销售负责人</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="changeDetails">
                <div class="gapLine">
                    <span class="sale-ttl">原销售负责人</span>
                    <span class="sale-con" id="orgSale">李东</span>
                </div>
                <div class="gapLine">
                    <span class="sale-ttl">新销售负责人</span>
                    <select class="salesList"></select>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="changeSalerSure" onclick="sale_updateSure()">确定</button>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--销售更换记录--%>
    <div class="bonceContainer bounce-green" id="changeSalerRecord" style="width:800px;">
        <div class="bonceHead">
            <span>销售负责人更换记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-green ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--回款录入新增--%>
    <div class="bonceContainer bounce-green" id="addPayInputer" style="width:510px;">
        <div class="bonceHead">
            <span class="addRightTtl"></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="checkWrap">
                <span class="ty-btn ty-btn-big selectBtn allSelectBtn" status="0" onclick="byAllType($(this))">在全部职工中选择</span>
                <div class="byAll"></div>
            </div>
            <div class="checkWrap">
                <span class="ty-btn ty-btn-big selectBtn departSelectBtn" status="0" onclick="byDepartType($(this))">分部门查看并选择</span>
                <div class="byDepart"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-5 addInputerSure" onclick="addInputerSure()">确定</button>
        </div>
    </div>
    <%--回款录入 ----------取消本权限 --%>
    <div class="bonceContainer bounce-red" id="changeLimit" style="width:400px;">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
           <p class="limitTextTip">您确定取消其回款录入的权限吗！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-5" id="limitChangeSure" onclick="limitChangeSure()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="objectManagementIndex" style="width:900px;">
        <div class="bonceHead">
            <span>回款录入的对象</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear">
                <p class="ty-left"><span class="enteredName"></span>可录入如下<span class="enteredNum">XXX</span>家客户的<span class="entereType"></span>。</p>
                <p class="ty-left"><button class="ty-btn ty-btn-big ty-btn-blue" onclick="addObjectManagement()">增加</button></p>
            </div>
            <table class="ty-table ty-table-control objectManagementList">
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--基本信息修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="BaseRecordsDetail" style="min-width: 1200px">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" id="">
            <div class="flex-box">
                <div><span class="sale_ttl1">客户名称：</span><span class="initValue sale-con" data-name="name" id="base_cusname"></span></div>
                <div><span class="sale_ttl1">地址：</span><span class="sale-con" id="base_address" data-name="address"></span></div>
                <div><span class="sale_ttl1">客户代号：</span><span class="initValue sale-con" id="base_cuscoding" data-name="code"></span></div>
                <div class="sale-con" style="margin-left:10px;"><span class="initValue" id="base_firstBuyTime"></span></div>
            </div>
            <div>
                <div class="sale_c">
                    <div class="sale_ttl1">全景照片：</div>
                    <div class="initValue" id="base_qImgUpload"></div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">产品图片：</div>
                    <div class="initValue" id="base_pImgUpload"></div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">首次接触时间：</div>
                    <div class="sale-con initValue" id="base_firstContactTime"></div>
                    <div class="sale_ttl1">首次接触地点：</div>
                    <div class="sale-con initValue" id="base_firstAddress"></div>
                    <div class="sale_ttl1">信息获取渠道：</div>
                    <div class="sale-con initValue" id="base_infoSource"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%--发票修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="invoiceRecordsDetail" style="width: 800px">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="sale_c">
                <div class="sale_ttl1">公司名称：</div>
                <div class="sale-con" id="inv_invoiceName"></div>
                <div class="sale_ttl1">地址：</div>
                <div class="sale-con" id="inv_invoiceAddress"></div>
                <div class="sale_ttl1">电话：</div>
                <div class="sale-con" id="inv_phone"></div>
            </div>
            <div class="sale_c">
                <div class="sale_ttl1">开户行：</div>
                <div class="sale-con" id="inv_bank"></div>
                <div class="sale_ttl1">账号：</div>
                <div class="sale-con" id="inv_bankNo"></div>
                <div class="sale_ttl1">税号：</div>
                <div class="sale-con" id="inv_taxpayerID"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%--收货地址修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="shRecordsDetail">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="shDisUse ty-color-red"></p>
            <p>
                <span class="sale_ttl1">收货地址：</span>
                <span class="sale-con" id="shAddress"></span>
            </p>
            <p>
                <span class="sale_ttl1">收货人：</span>
                <span class="sale-con" id="shName"></span>
            </p>
            <p>
                <span class="sale_ttl1">收货电话：</span>
                <span class="sale-con" id="shNumber"></span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%--邮寄地址修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="fpRecordsDetail">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="fpDisUse ty-color-red"></p>
            <p>
                <span class="sale_ttl1">邮寄地址：</span>
                <span class="sale-con" id="fpAddress"></span>
            </p>
            <p>
                <span class="sale_ttl1">发票接收人：</span>
                <span class="sale-con" id="fpName"></span>
            </p>
            <p>
                <span class="sale_ttl1">联系电话：</span>
                <span class="sale-con" id="fpMobile"></span>
            </p>
            <p>
                <span class="sale_ttl1">邮寄编码：</span>
                <span class="sale-con" id="fpNumber"></span>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%--联系人修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="contactRecordsDetail" style="width: 550px">
        <div class="bonceHead">
            <span class="detaileTtl"></span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">姓名：</span>
                <span class="sale-con" id="record_contactName"></span>
                <span class="sale_ttl1">职位：</span>
                <span class="" id="record_position"></span>
            </p>
            <ul class="record_otherContact">
            </ul>
            <span class="sale_ttl1">名片：</span>
            <div id="record_contactsCard">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>销售分工</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <div id="picShow" style="display: none;">
                        <img src=""/>
                    </div>
                    <div class="btnGroup" style="margin:20px;">
                        <span class="ty-btn ty-btn-big ty-btn-blue" id="rebackBtn" onclick="backIndex($(this))" style="display: none;">返回</span>
                        <span class="ty-btn ty-btn-big ty-btn-green" id="addInputer" data-name="payCash" onclick="addInputer($(this))" style="display: none;">新增</span>

                    </div>
                    <div id="mainPane">
                        <div class="dividePane">
                            <div>
                                <span>查看或修改当前客户的销售负责人，请点击</span>
                                <span class="ty-right dvdBtn ty-color-blue" onclick="turnTab(1)">销售负责人管理</span>
                            </div>
                            <div>
                                <span>查看及管理有回款录入权限的人，请点击</span>
                                <span class="ty-right dvdBtn ty-color-blue" onclick="turnTab(2)">回款录入者管理</span>
                            </div>
                            <div>
                                <span>查看及管理有潜在客户录入权限的人，请点击</span>
                                <span class="ty-right dvdBtn ty-color-blue" onclick="turnTab(3)">潜在客户录入者管理</span>
                            </div>
                            <div>
                                <span>查看及管理有客户访谈记录权限的人，请点击</span>
                                <span class="ty-right dvdBtn ty-color-blue" onclick="turnTab(4)">访谈管理</span>
                            </div>
                        </div>
                        <div class="opinionCon hd">
                            <div class="main">
                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 ty-right" id="suspendedBtn"  onclick="suspendedCustomer()" style="margin-top: -50px;">已暂停合作的客户</span>
                                <p>以下为与本公司合作中的全部客户，您可在此更换客户的销售负责人</p>
                                <table class="ty-table ty-table-control divisionList">
                                    <thead>
                                    <td width="10%">序号</td>
                                    <td width="10%">客户代号</td>
                                    <td width="20%">客户名称</td>
                                    <td width="20%">创建者</td>
                                    <td width="10%">销售负责人</td>
                                    <td width="30%">操作</td>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div id="ye_division"></div>
                            </div>
                            <%--已暂停合作的客户--%>
                            <div class="suspendedCustomer" style="display: none;">
                                <div>
                                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="goBack()">返回</span>
                                </div>
                                <div>
                                    <p style="margin-top:20px; ">以下客户已暂停合作</p>
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <td>序号</td>
                                        <td>客户代号</td>
                                        <td>客户名称</td>
                                        <td>创建者</td>
                                        <td>暂停合作的时间</td>
                                        <td>操作</td>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye_suspendCustomer"></div>
                                </div>
                            </div>
                        </div>
                        <div class="moneyBack hd">
                            <div class="inputerTip">
                                <p>下列人员已有回款录入权限，通过“新增”可使更多的人拥有本权限。</p>
                                <p>您需对“新增”者进行“对象管理”，以进一步确定其具体可录入哪些客户的回款。</p>
                            </div>
                            <table class="ty-table ty-table-control payBackList">
                                <thead>
                                <td width="10%">序号</td>
                                <td width="15%">姓名</td>
                                <td width="15%">手机号</td>
                                <td width="10%">部门</td>
                                <td width="10%">职位</td>
                                <td width="15%">回款录入的对象</td>
                                <td width="25%">操作</td>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                        <div class="potentialCustomers hd">
                            <p class="inputerTip">下列人员已有潜在客户录入的权限。您在“新增”里的操作，将使新选择的人也有该权限。</p>
                            <table class="ty-table ty-table-control potentialCustomersList">
                                <thead>
                                <td width="10%">序号</td>
                                <td width="20%">姓名</td>
                                <td width="20%">手机号</td>
                                <td width="10%">部门</td>
                                <td width="20%">职位</td>
                                <td width="20%">操作</td>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>

                        <div class="interview hd">
                            <div class="inputerTip">
                                <p>下列人员已有访谈记录权限，通过“新增”可使更多的人拥有本权限。</p>
                                <p>“新增”者需经过“<span class="red">对象管理</span>”，以确定可录入哪些客户的访谈记录。</p>
                            </div>
                            <table class="ty-table ty-table-control interviewList">
                                <thead>
                                <td width="10%">序号</td>
                                <td width="15%">姓名</td>
                                <td width="15%">手机号</td>
                                <td width="10%">部门</td>
                                <td width="10%">职位</td>
                                <td width="15%">对象</td>
                                <td width="25%">操作</td>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/sales/salesDivision.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
