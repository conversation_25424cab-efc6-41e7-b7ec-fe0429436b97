<%--
  Created by IntelliJ IDEA.
  User: sy
  Date: 2023/2/8
  Time: 15:37
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/equipment/nameManageMent.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed2"></div>
<div class="bounce_Fixed"></div>
<div class="bounce">
    <%-- 新增名称 --%><!--修改名称用同一个弹窗 -->
    <div class="bonceContainer bounce-blue" id="addname" style="display: block;position: fixed;left: 721.5px;top: -52.5px;">
        <div class="bonceHead" style="cursor: move;">
            <span id="addne">新增名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="margin: 56px 20px 0px 20px;">
            <div class="sale_ttl1" style="width: 162px;margin-left: 89px;margin-bottom: 5px;">
                <span>装备器具的名称</span>
            </div>
            <div class="sale_con1" style="width: 216px;margin-left: 85px;">
                <input type="text" id="addequname" class="add_eqname" name="eqname" style="width: 216px;">
                <br />
                <span id="tishi" style="margin-left: 196px;">0</span>/8
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel($('#addname'))" id="cancel">取消</span>
            <span><!--add那个是新增，up是修改-->
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="addsubmit" onclick="addsubna()">确定</button>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="upmodify" onclick="upmodina()">确定</button>
            </span>
        </div>
    </div>
    <%-- 装备器具名称的修改记录 --%>
    <div class="bonceContainer bounce-blue" id="modificationrecord">
        <div class="bonceHead">
            <span>装备器具名称的修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <thead>
                <td>事件</td>
                <td>操作</td>
                <td>操作后的名称</td>
                </thead>
                <tbody id="takenotes">
                <tr>
                    <td>第1次修改后</td>
                    <td>刘  2023-02-15 12:11:20</td>
                    <td>器具一</td>
                </tr>
                <tr>
                    <td>第2次修改后</td>
                    <td>刘 2023-02-20 20:12:20</td>
                    <td>器具二</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" id="closedown" onclick="bounce.cancel($('#modificationrecord'));" style="background-color:#5d9cec;color: white;">关闭</span>
        </div>
    </div>
    <%-- 本装备用于本产品的操作记录 --%>
    <div class="bonceContainer bounce-blue" id="producnacord">
        <div class="bonceHead">
            <span>操作记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="title">
                <span>装备信息：系统赋予的编码/装备器具编号/名称/型号</span>
                <span>产品信息：图号（代号）/名称/规格/型号</span>
            </div>
            <div>本装备用于本产品的操作记录</div>
            <table class="ty-table bg-yellow ty-table ty-table-control">
                <thead style="background: none;" id="perationreco">
                <tr>
                    <td>操作</td>
                    <td>操作者</td>
                    <td>操作时间</td>
                </tr>
                </thead>
                <tbody id="perericond" class="ty-table">
                <tr>
                    <td>选用</td>
                    <td>XXX</td>
                    <td>XXXX-XX-XX XX:XX:XX</td>
                </tr>
                <tr>
                    <td>取消选用</td>
                    <td>XXX</td>
                    <td>XXXX-XX-XX XX:XX:XX</td>
                </tr>
                <tr>
                    <td>选用</td>
                    <td>XXX</td>
                    <td>XXXX-XX-XX XX:XX:XX</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" id="proclose" onclick="bounce.cancel($('#producnacord'));" style="background-color:#5d9cec;color: white;">关闭</span>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>名称管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content">
            <!--放内容的地方-->
            <div style="min-height: 750px;">
                <div class="ty-container">
                    <%--名称管理首页列表--%>
                    <div class="ty-container" id="nameagent">
                        <div>
                            <div class="main rolor">
                                <div style="position: relative;">
                                    <div id="describe" class="word" style="position: absolute;top: 5px;">装备器具现有<span class="number">XX</span>种：具体如下</div>
                                    <div style="position: absolute;right: 111px;top: 0px;">
                                        <span class="catch" style="margin: 10px 20px;">查找</span>
                                        <input id="se1" placeholder="请输入名称中的关键字" style="height: 31px;" /><span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="seachlook" onclick="getsecher()">确定</span>
                                    </div>
                                    <div class="pull-right" style="position: absolute;top: 0px;right: 0px;">
                                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="addnewna" onclick="addname($(this))">新增名称</span>
                                    </div>
                                </div>
                                <br />
                                <div class="equipmentdata" style="margin-top: 54px;">
                                    <table class="ty-table ty-table-control" id="namescay">
                                        <thead style="background: none;">
                                        <td>装备器具名称</td>
                                        <td>型号 </td>
                                        <td>来源</td>
                                        <td>单位</td>
                                        <td>数量</td>
                                        <td>创建</td>
                                        <td>操作</td>
                                        </thead>
                                        <tbody class="sale_Tbody" id="equipmentmess">
                                        <td>器具一</td>
                                        <td>AD52147</td>
                                        <td>2个来源</td>
                                        <td>20</td>
                                        <td class="ty-td-control">
                                            <span class="ty-color-blue funBtn" onclick="quantitymess($(this))">XX</span>
                                        </td>
                                        <td>小莫 2023-02-09  14：20：12</td>
                                        <td>
                                            <span class="ty-color-blue funBtn" onclick="upeqname($(this))">修改名称</span>
                                            <span class="ty-color-blue funBtn" onclick="upnamerecord($(this))">名称修改记录</span>
                                            <span class="hd">JSON值</span>
                                        </td>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <%-- 名称搜索结果列表 --%>
                    <div class="ty-container" id="searchname" style="display: none;margin: 35px 20px 20px 20px;">
                        <button type="button" class="btn btn-default" id="backsech" onclick="turnback()" style="margin: 0 96px;width: 76px;">返回</button>
                        <div class="pageStyle container_item">
                            <div class="initBody" style="margin: 0 96px;">
                                <div class="title" style="margin-top: 42px;">
                                    装备器具现有<span id="several">XX</span>种：具体如下
                                </div>
                                <table class="ty-table bg-yellow ty-table-control">
                                    <thead style="background: none;" id="seachnamecy">
                                    <tr>
                                        <td>装备器具名称</td>
                                        <td>型号 </td>
                                        <td>来源</td>
                                        <td>单位</td>
                                        <td>数量</td>
                                        <td>创建</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody id="seanameank"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <%--点击数量跳转的表格页面--%>
                    <div class="ty-container" id="quantitydetails" style="display: none;margin: 35px 20px 20px 20px;">
                        <button type="button" class="btn btn-default" id="backbeg" onclick="backerimg()" style="margin: 0 96px;width: 76px;">返回</button>
                        <div class="pageStyle container_item">
                            <div class="initBody" style="margin: 0 96px;">
                                <div class="title" style="margin-top: 42px;">
                                    <span id="zbqjna">XX</span>共<span id="pace">XX</span>台（套），按型号展示如下：
                                    <span id="lookall" class="ty-color-blue funBtn thin-btn pull-right" onclick="getlookall($(this))" style="font-weight: bold;">直接查看全部</span>
                                </div>
                                <table class="ty-table bg-yellow ty-table-control">
                                    <thead style="background: none;" id="tatle">
                                    <tr>
                                        <td>型号</td>
                                        <td>数量（台/套）</td>
                                        <td>来源</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody id="modelbox" class="ty-table">
                                    <tr>
                                        <td>XXXXXXX</td>
                                        <td>XX</td>
                                        <td>XX个来源</td>
                                        <td>
                                            <span class="ty-color-blue funBtn" onclick="lookmore($(this))">查看</span>
                                            <span class="hd">JSON值</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <%--点击“直接查看全部”后跳转的表格页面--%>
                    <div class="ty-container" id="categorycode" style="display: none;margin: 35px 20px 20px 20px;">
                        <button type="button" class="btn  btn-default" id="backbeg1" onclick="backerimgup()" style="margin: 0 96px;width: 76px;">返回</button>
                        <div class="pageStyle container_item">
                            <div class="initBody" style="margin: 0 96px;">
                                <div class="title" style="margin-top: 42px;">
                                    <span class="eqcaname1">XX</span>共<span class="acover1">XX</span>台（套），具体如下：
                                </div>
                                <table class="ty-table bg-yellow ty-table-control">
                                    <thead style="background: none;" id="tatle1">
                                    <tr>
                                        <td>系统赋予的编码</td>
                                        <td>装备器具编号</td>
                                        <td>型号</td>
                                        <td>来源</td>
                                        <td>所属类别</td>
                                        <td>创建</td>
                                        <td>参与加工的产品</td>
                                    </tr>
                                    </thead>
                                    <tbody id="codingdetails" class="ty-table">
                                    <tr>
                                        <td>XXXXXXX</td>
                                        <td>XXXXXXX</td>
                                        <td>XXXXXXX</td>
                                        <td>XXXXX</td>
                                        <td>XXXXX/XXXXXXX</td>
                                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                        <td>
                                            <span class="ty-color-blue funBtn" onclick="">XX种</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <%--点击有型号的表格中“参与加工的产品”下面的XX种跳转的页面表格--%>
                    <div class="ty-container" id="drawingnumber" style="display: none;margin: 35px 20px 20px 20px;">
                        <button type="button" class="btn btn-default" id="backbeg2" onclick="backerimgmid()" style="margin: 0 96px;width: 76px;">返回</button>
                        <div class="pageStyle container_item">
                            <div class="initBody" style="margin: 0 96px;">
                                <div class="title" style="margin-top: 42px;">
                                    <span>装备信息：系统赋予的编码/装备器具编号/名称/型号</span>
                                    <br />
                                    <span>本装备参与加工的产品：</span>
                                </div>
                                <table class="ty-table bg-yellow ty-table-control">
                                    <thead style="background: none;" id="tatle3">
                                    <tr>
                                        <td>图号（代号）</td>
                                        <td>名称</td>
                                        <td>规格</td>
                                        <td>型号</td>
                                        <td>计量单位</td>
                                        <td>本装备用于本产品的操作时间</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody id="allcodeproduct" class="ty-table">
                                    <tr>
                                        <td>XXXXXXX</td>
                                        <td>XXXXXXX</td>
                                        <td>XXXXXXX</td>
                                        <td>XXXXXXX</td>
                                        <td>XXXXXXX</td>
                                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                        <td>
                                            <span class="ty-color-blue funBtn" onclick="viewrecords($(this))">本装备用于本产品的操作记录</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <%-- 点击查看后展示的有筛选的列表 --%>
                    <div class="ty-container" id="screenboder" style="display: none;margin: 35px 20px 20px 20px;">
                        <button type="button" class="btn btn-default" id="tunback" onclick="gunback()" style="margin: 0 96px; width: 76px;">返回</button>
                        <div class="pageStyle container_item">
                            <div class="initBody" style="margin: 0 96px;">
                                <div class="title" style="margin-top: 39px;display: flex;position: relative;margin-bottom: 20px;">
                                    <div style="margin-top: 10px;width: 387px;">
                                        <span class="modeler">XXX</span>的<span class="eqcaname">XX</span>共<span class="acover">XX</span>台（套），具体如下：
                                    </div>
                                    <div style="position: absolute;right: 0px;top: 0px;">
                                        <span style="margin-right: 20px;">筛选</span>
                                        <select id="screenthng" onchange="chonseotherq($(this))" value="0" style="width: 366px;height: 41px;"></select>
                                    </div>
                                </div>
                                <table class="ty-table be-yellow ty-table-control">
                                    <thead style="background: none;" id="tatle4">
                                    <tr>
                                        <td>系统赋予的编码</td>
                                        <td>装备器具编号</td>
                                        <td>来源</td>
                                        <td>所属类别</td>
                                        <td>创建</td>
                                        <td>参与加工的产品</td>
                                    </tr>
                                    </thead>
                                    <tbody id="codenamey" class="ty-table">
                                    <tr>
                                        <td>XXXXXXX</td>
                                        <td>XXXXXXX</td>
                                        <td>XXXXX</td>
                                        <td>XXXXX/XXXXXXXXXX/XXXXXXXXXXX…</td>
                                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                                        <td>
                                            <span class="ty-color-blue funBtn" onclick="">XX种</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/equipment/nameManageMent.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>