<%--
  Created by IntelliJ IDEA.
  User: 侯杏哲
  Date: 2023/2/8
  Time: 15:45
  装备器具 - 补录
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/equipment/equitmentAddRe.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="addName">
        <div class="bonceHead">
            <span>新增名称</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                装备器具的名称<br>
                <input type="text" class="form-control " /><br>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="addNameOk">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addUnit">
        <div class="bonceHead">
            <span>新增单位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                单位<br>
                <input type="text" class="form-control" id="unitName"/><br>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="addUnitOk">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addSup">
        <div class="bonceHead">
            <span>增加供应商/加工方的选项</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <span class="ty-color-red">*</span>供应商/加工方名称<br>
                <input type="text" class="form-control" id="supFullName" placeholder="请录入"/><br>
            </div>

            <div>
                <span class="ty-color-red">*</span>供应商/加工方简称<br>
                <input type="text" class="form-control" id="supName" placeholder="请录入"/><br>
            </div>

            <div>
                <span class="ty-color-red">*</span>*供应商/加工方代号<br>
                <input type="text" class="form-control" id="supCode" placeholder="请录入"/><br>
                <span class="ty-color-blue">
                    注：对于供应商代号，系统仅要求不能重复，具体如何编号，请与采购部门沟通。如无规则请随意录入。
                </span>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="addSupOk">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="selectCat">
        <div class="bonceHead">
            <span>请选择类别</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="catContainer">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="selectCatOk">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="operDescription" style="width: 800px;">
        <div class="bonceHead">
            <span>关于“数量”的操作说明</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="msgCon">
                <p>1 “数量”大于“1”时，“装备器具名称”、“型号”、“供应商/加工方”及“单位”都需要有数据，否则无法成功点击“确定”按钮。</p>
                <p>2 “数量”大于“1”并成功点击“确定”后，数据给予保存。保存后，对列表中某装备器具的“装备器具名称”、“型号”、“供应商/加工方”或“单位”进行修改后，列表中其他“装备器具”的数据将跟随变动。</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">知道了</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 装备器具地的零星补录 --%>
    <div class="bonceContainer bounce-green" id="litAdd" style="width: 1100px;">
        <div class="bonceHead">
            <span>装备器具的零星补录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="new">
                <tr class="noBr"><td colspan="5"><div class="linkBtn ty-right" onclick="bounce_Fixed.show($('#operDescription'))">关于数量的操作说明</div></td></tr>
                <tr>
                    <td width="160px">
                        <div>装备器具名称<span class="ty-color-red">*</span><span class="linkBtn ty-right" data-fun="addName">新增</span></div>
                    </td>
                    <td>
                        <div>型号</div>
                    </td>
                    <td>
                        <div>供应商/加工方<span class="linkBtn ty-right" data-fun="addSup">新增</span></div>
                    </td>
                    <td>
                        <div>单位<span class="linkBtn ty-right" data-fun="addUnit">新增</span></div>
                    </td>
                    <td>
                        <div>数量<span class="ty-color-red">*</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <select class="form-control" id="eqName">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td>
                        <input type="text" class="form-control" id="model" placeholder="请录入"/>
                    </td>
                    <td>
                        <select class="form-control" id="sup">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td>
                        <select class="form-control" id="unit">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td>
                        <input type="text" id="amount" class="form-control" placeholder="请录入" onkeyup="clearNum(this)" onchange="changeLitSnGroups($(this))"/>
                    </td>
                </tr>
            </table>
            <table class="new eqOtherItems">
                <tr>
                    <td>
                        <div>装备器具编号</div>
                    </td>
                    <td>
                        <div>原值</div>
                    </td>
                    <td>
                        <div>预期的使用寿命</div>
                    </td>
                    <td>
                        <div>到厂日期</div>
                    </td>
                    <td>
                        <div>到厂时的新旧情况</div>
                    </td>
                    <td>
                        <div>类别</div>
                    </td>
                    <td colspan="2">
                        <div>其他需要备注的内容</div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <input type="text" id="code" class="form-control code" placeholder="请录入"/>
                    </td>
                    <td>
                        <input type="text" class="form-control litInput originalValue" id="originalValue" placeholder="请录入" onkeyup="clearNoNumN(this, 2)" onchange="fixed2($(this))" /><span class="litUnit">元</span>
                    </td>
                    <td>
                        <input type="text" class="form-control litInput lifeSpan" placeholder="请录入" id="lifeSpan" onkeyup="clearNum(this)"/><span class="litUnit">年</span>
                    </td>
                    <td>
                        <input type="text" id="inFDate" class="form-control inFDate" readonly placeholder="请选择"/>
                    </td>
                    <td>
                        <select class="form-control conditions" id="conditions">
                            <option value="">请选择</option>
                            <option value="1">新</option>
                            <option value="2">旧</option>
                        </select>
                    </td>
                    <td>
                        <input type="text" class="form-control funBtn cat" readonly id="cat" data-fun="selectCatBtn" />
                    </td>
                    <td colspan="2">
                        <input type="text" class="form-control textarea setLimitRoleInput memo" id="memo" placeholder="可录入不超过50字" onchange="setLimitRole($(this), 50)" />
                        <%--<span class="limtRole">0/50</span>--%>
                    </td>
                </tr>
            </table>

            <table class="impotEdit">
                <tr>
                    <td>
                        <div>装备器具名称<span class="ty-color-red">*</span></div>
                        <select class="form-control" id="eqName_e">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td>
                        <div>型号</div>
                        <input type="text" class="form-control" id="model_e" placeholder="请录入"/>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div>单位</div>
                        <select class="form-control" id="unit_e">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td>
                        <div>装备器具编号</div>
                        <input type="text" id="code_e" class="form-control" placeholder="请录入"/>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div>预期的使用寿命</div>
                        <input type="text" class="form-control litInput" placeholder="请录入" id="lifeSpan_e" onkeyup="clearNum(this)"/><span class="litUnit">年</span>
                    </td>
                    <td>
                        <div>原值</div>
                        <input type="text" class="form-control litInput" id="originalValue_e" placeholder="请录入" onkeyup="clearNoNumN(this, 2)" onchange="fixed2($(this))" /><span class="litUnit">元</span>
                    </td>

                </tr>
                <tr>
                    <td>
                        <div>到厂日期</div>
                        <input type="text" id="inFDate_e" class="form-control" readonly placeholder="请选择"/>
                    </td>
                    <td>
                    </td>
                </tr>
            </table>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5 new" data-fun="litAddOk">确定</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5 impotEdit" data-fun="importEditOk">确定</span>
        </div>
    </div>
    <%-- 装备器具的批量补录 --%>
    <div class="bonceContainer bounce-green" id="uploadAdd" >
        <div class="bonceHead">
            <span>装备器具的批量补录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form action="../equipment/model/import" id="leading" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：点击“下载”，以下载空白的“装备器具清单”。</p>
                        <div class="flexRow">
                            <span>装备器具清单</span>
                            <a href="<%=System.getProperty("BaseUrl")%>/assets/template/eqList.xls"
                               id="mould1" download="装备器具清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“装备器具清单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”，之后选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="importUploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="importOk('cancel');">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="importOk()">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加或删除“装备器具清单”空白表的“列”，也不要修改修改各列的名字，否则上传会失败。</span></br>
                            <span>2、“装备器具清单”“另存为”至电脑上时，可使用新的文件名，但点击本页面的“浏览”时如选错文件，上传可能失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
<%--        <div class="bonceFoot">--%>
<%--            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>--%>
<%--            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="back">确定</span>--%>
<%--        </div>--%>
    </div>
    <%-- 装备器具的批量补录 删除提示 --%>
    <div class="bonceContainer bounce-red" id="uploadAddDeltip" >
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                确定删除这条数据吗？
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="uploadAddDeltipOk">确定</span>
        </div>
    </div>
    <%-- 装备器具的批量补录 放弃提示 --%>
    <div class="bonceContainer bounce-red" id="delAlltip" >
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                确定放弃全部数据吗？
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="delAlltipOk">确定</span>
        </div>
    </div>


</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>补录</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <%-- 主页面 --%>
                <div class="mainCon mainCon1">
                    <div class="maintip">
                        <p>补录的装备器具，需为已有但尚未录入至系统的装备器具。</p>
                        <p>强烈建议：新购买或新制作的装备器具请走申购流程，不要在此补录！</p>
                        <p>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="litAdd">零星补录</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="uploadAdd">批量补录</span>
                        </p>
                    </div>
                </div>
                <div class="mainCon mainCon2">
                    <table class="preTab">
                        <tr>
                            <td>
                                您刚刚操作导入的装备器具共<span id="allImportNum"></span>条。<br/>
                                以下<span id="redImportNum"></span>条被标为红色的部分不符合右侧的要求，<span class="ty-color-red">无法保存至系统。</span><br/>
                                “修改”无误后，方可点击“下一步”。"
                            </td>
                            <td>
                                系统对表格各列数据的要求：<br>
                                “装备器具编号”不能重号；<br>
                                “装备器具名称”是必填项；<br>
                                “到厂日期”型式需为六位数字；<br>
                                “预期的使用寿命”中需填入正整数；<br>
                                “原值”中填入数据的小数点后需带有两位有效数字。
                            </td>
                            <td>
                                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3" data-fun="uploadCancel">放弃</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" data-fun="uploadNext">下一步</span>

                            </td>
                        </tr>
                    </table>
                    <table class="ty-table ty-table-control">
                        <thead>
                            <tr>
                                <td>装备器具编号</td>
                                <td>装备器具名称</td>
                                <td>型号</td>
                                <td>单位</td>
                                <td>到厂日期</td>
                                <td>预期的使用寿命</td>
                                <td>原值</td>
                                <td>操作</td>
                            </tr>
                        </thead>
                        <tbody id="import1">
                        <tr>
                            <td>装备器具编号</td>
                            <td>装备器具名称</td>
                            <td>型号</td>
                            <td>单位</td>
                            <td>到厂日期</td>
                            <td>预期的使用寿命</td>
                            <td>原值</td>
                            <td>
                                <span class="ty-color-blue funBtn" data-fun="editUpload">修改</span>
                                <span class="ty-color-red funBtn" data-fun="delUpload">删除</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mainCon mainCon3">
                    <p>
                        本页各项如不需要可不操作，可直接点击“完成”。
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-fun="uploadComplete">完成</span>
                        <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 ty-right" data-fun="uploadCancel">放弃</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-suptype="2" data-fun="addSup" style="margin-right: 50px;">增加供应商/加工方的选项</span>
                    </p>
                    <table class="ty-table ty-table-control" id="tab2">
                        <tr>
                            <td>装备器具编号/名称/型号</td>
                            <td>类别</td>
                            <td>到厂时的新旧情况</td>
                            <td>供应商/加工方</td>
                            <td>其他需要备注的内容</td>
                            <td>操作</td>
                        </tr>
                        <tr>
                            <td>装备器具编号/名称/型号</td>
                            <td>类别</td>
                            <td>到厂时的新旧情况</td>
                            <td>供应商/加工方</td>
                            <td>其他需要备注的内容</td>
                            <td>
                                <span class="ty-color-blue">修改</span>
                                <span class="ty-color-red">删除</span>
                            </td>
                        </tr>
                    </table>


                </div>

            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/equipment/equitmentAddRe.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>