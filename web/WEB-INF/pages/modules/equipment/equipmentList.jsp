<%--
  Created by IntelliJ IDEA.
  User: 侯杏哲
  Date: 2023/2/8
  Time: 15:45
  装备器具 - 装备清单
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/equipment/equipList.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="addName">
        <div class="bonceHead">
            <span>新增名称</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                装备器具的名称<br>
                <input type="text" class="form-control" onchange="setLimitRole($(this), 8)"/><br>
                &nbsp;<span class="limtRole">XX/8</span>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="addNameOk">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addUnit">
        <div class="bonceHead">
            <span>新增单位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                单位<br>
                <input type="text" class="form-control" id="unitName"/><br>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="addUnitOk">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addSup">
        <div class="bonceHead">
            <span>增加供应商/加工方的选项</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <span class="ty-color-red">*</span>供应商/加工方名称<br>
                <input type="text" class="form-control" id="supFullName" placeholder="请录入"/><br>
            </div>

            <div>
                <span class="ty-color-red">*</span>供应商/加工方简称<br>
                <input type="text" class="form-control" id="supName" placeholder="请录入"/><br>
            </div>

            <div>
                <span class="ty-color-red">*</span>供应商/加工方代号<br>
                <input type="text" class="form-control" id="supCode" placeholder="请录入"/><br>
                <span class="ty-color-blue">
                    注：对于供应商代号，系统仅要求不能重复，具体如何编号，请与采购部门沟通。如无规则请随意录入。
                </span>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="addSupOk">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="selectCat" style="width: 600px">
        <div class="bonceHead">
            <span>请选择类别</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="catContainer">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="selectCatOk">确定</span>
        </div>
    </div>
    <%-- 装备器具 修改记录详情 --%>
    <div class="bonceContainer bounce-blue" id="eqLogScan" style="width: 1000px">
        <div class="bonceHead">
            <span>装备器具信息查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <tr>
                    <td>
                        <div>装备器具名称</div>
                    </td>
                    <td>
                        <div>型号</div>
                    </td>
                    <td>
                        <div>供应商/加工方</div>
                    </td>
                    <td>
                        <div>单位</div>
                    </td>
                    <td class="baseLogScan">
                        <div>数量</div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="name"></div>
                    </td>
                    <td>
                        <div class="model"></div>
                    </td>
                    <td>
                        <div class="sup"></div>
                    </td>
                    <td>
                        <div class="unit"></div>
                    </td>
                    <td class="baseLogScan">
                        <div class="amountLog"></div>
                    </td>
                </tr>
            </table>
            <table class="ty-table subLogScan">
                <tr>
                    <td>
                        <div>装备器具编号</div>
                    </td>
                    <td>
                        <div>原值</div>
                    </td>
                    <td>
                        <div>预期的使用寿命</div>
                    </td>
                    <td>
                        <div>到厂日期</div>
                    </td>
                    <td>
                        <div>到厂时的新旧情况</div>
                    </td>
                    <td>
                        <div>类别</div>
                    </td>
                    <td>
                        <div>备注</div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="eqNo"></div>
                    </td>
                    <td>
                        <div class="oral"></div>
                    </td>
                    <td>
                        <div class="expireYear"></div>
                    </td>
                    <td>
                        <div class="factDate"></div>
                    </td>
                    <td>
                        <div class="newOrOld"> </div>
                    </td>
                    <td>
                        <div class="cat"></div>
                    </td>
                    <td>
                        <div class="memo"></div>
                    </td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="amountChangeTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <div>增加装备的数量，可通过”补录“的方式！</div>
            <div>减少装备的数量，请进行”停用“的操作！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 信息修改记录 --%>
    <div class="bonceContainer bounce-blue" id="editLog" style="width: 600px;">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce.show($('#eqScan'))"></a>
        </div>
        <div class="bonceCon">
            <p class="cOrUtip"></p>
            <table class="ty-table" id="editLogTab">
                <tbody>
                    <tr>
                        <td>资料状态</td>
                        <td>操作</td>
                        <td>创建人/修改人</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.show($('#eqScan'))">关闭</span>
        </div>
    </div>
    <%-- 公共信息的修改 --%>
    <div class="bonceContainer bounce-blue" id="msgEdit" style="width: 1100px">
        <div class="bonceHead">
            <span>公共信息的修改</span>
            <a class="bounce_close" onclick="bounce.show($('#eqScan'))"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-color-blue publicInfor">注：本处的修改对于页面上的XX台装备共同生效！</div>
            <div class="ty-color-blue morePartTip">注：如修改装备器具的名称、型号或供应商/加工方，说明本台装备与其他X台装备不是相同的设备，修改后本台设备将不再展示于这些设备的列表中！</div>
            <table class="ty-table infoEdit">
                <tr>
                    <td>
                        <div>装备器具名称<span class="ty-color-red">*</span><span class="linkBtn ty-right" data-fun="addName">新增</span></div>
                    </td>
                    <td>
                        <div>型号</div>
                    </td>
                    <td>
                        <div>供应商/加工方<span class="linkBtn ty-right" data-fun="addSup">新增</span></div>
                    </td>
                    <td class="singlePart">
                        <div>单位<span class="linkBtn ty-right" data-fun="addUnit">新增</span></div>
                    </td>
                    <td class="publicInfor">
                        <div>数量</div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <select name="equipment" class="form-control" id="eqName">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td>
                        <input name="modelName" type="text" id="model" placeholder="请录入"/>
                    </td>
                    <td>
                        <select name="supplier" class="form-control" id="sup">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td class="singlePart">
                        <select name="unitId" class="form-control" id="unit">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td class="publicInfor">
                        <input type="text" name="eqNum" class="sTip eqNum" readonly onclick="bounce_Fixed.show($('#amountChangeTip'))" />
                    </td>
                </tr>
            </table>
            <div class="equipEdit">
                <hr/>
                <table class="ty-table">
                    <tbody>
                    <tr>
                        <td width="15%"><div>装备器具编号</div></td>
                        <td width="15%"><div>原值</div></td>
                        <td width="15%"><div>预期的使用寿命</div></td>
                        <td width="10%"><div>到厂日期</div></td>
                        <td width="15%"><div>到厂时的新旧情况</div></td>
                        <td width="10%"><div>类别</div></td>
                        <td width="20%"><div>其他需要备注的内容</div></td>
                    </tr>
                    <tr>
                        <td>
                            <input type="text" id="code" name="modelCode" class="form-control" placeholder="请录入"/>
                        </td>
                        <td>
                            <div class="limitPos">
                                <input type="text" name="originalValue" class="form-control litInput" id="originalValue" placeholder="请录入" onkeyup="clearNoNumN(this, 2)" onchange="fixed2($(this))" /><span class="litUnit">元</span>
                            </div>
                        </td>
                        <td>
                            <div class="limitPos">
                                <input type="text" name="lifeSpan" class="form-control litInput" placeholder="请录入" id="lifeSpan" onkeyup="clearNum(this)"/><span class="litUnit">年</span>
                            </div>
                        </td>
                        <td>
                            <input type="text" name="rDate" id="inFDate" class="form-control" readonly placeholder="请选择"/>
                        </td>
                        <td>
                            <select name="conditions" class="form-control" id="conditions">
                                <option value="">请选择</option>
                                <option value="1">新</option>
                                <option value="2">旧</option>
                            </select>
                        </td>
                        <td>
                            <input type="text" name="category" class="form-control funBtn" readonly id="cat" data-fun="selectCatBtn" />
                        </td>
                        <td>
                            <input type="text" name="memo" class="form-control textarea" id="memo" placeholder="可录入不超过50字" onchange="setLimitRole($(this), 50)" />
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.show($('#eqScan'))">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="editMsgOk">确定</span>
        </div>
    </div>
    <%-- 装备器具管理 --%>
    <div class="bonceContainer bounce-blue" id="eqScan" style="width: 1000px;">
        <div class="bonceHead">
            <span>装备器具管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="singleSect">
                <span>基本信息</span>
            </div>
            <table class="ty-table ty-table-control">
                <tr>
                    <td>
                        <div>装备器具名称</div>
                    </td>
                    <td>
                        <div>型号</div>
                    </td>
                    <td>
                        <div>供应商/加工方</div>
                    </td>
                    <td>
                        <div>单位</div>
                    </td>
                    <td>
                        <div>数量</div>
                    </td>
                    <td>
                        <div>操作</div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="name"></div>
                    </td>
                    <td>
                        <div class="model"></div>
                    </td>
                    <td>
                        <div class="sup"></div>
                    </td>
                    <td>
                        <div class="unit"></div>
                    </td>
                    <td>
                        <div class="eqCount"></div>
                    <%-- <input type="text" id="amount" readonly class="form-control" onclick="bounce_Fixed.show($('amountChangeTip'))" />--%>
                    </td>
                    <td>
                        <span class="ty-color-blue" onclick="updateBtn($(this))">修改</span>
                        <span class="ty-color-blue" onclick="editLog($(this), 1)">修改记录</span>
                        <span class="hd" id="eqInfo"></span>
                    </td>
                </tr>
            </table>
            <div class="moreBd"></div>
            <div class="singleSect gapTp">
                <span>其他信息</span>
            </div>
            <div class="eqDetails">
                <div>
                    <span>创建：</span>
                    <span>系统赋予的编码：XXXXXXXXX</span>
                    <span>装备器具的编号：XXXXXXXXX</span>
                    <span class="ty-right">类别：XXXXX>XXXXXX>XXXXXXXXXXXX>XXX>XXXX>XXX…</span>
                </div>
                <table>
                    <tr>
                        <td>
                            <div>到厂日期</div>
                        </td>
                        <td>
                            <div>到厂时的新旧情况</div>
                        </td>
                        <td>
                            <div>原值</div>
                        </td>
                        <td>
                            <div>预期的使用寿命</div>
                        </td>
                        <td>
                            <div>备注</div>
                        </td>
                        <td>
                            <div>操作</div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="factDate"></div>
                        </td>
                        <td>
                            <div class="newOrOld"> </div>
                        </td>
                        <td>
                            <div class="oral"></div>
                        </td>
                        <td>
                            <div class="expireYear"></div>
                        </td>
                        <td>
                            <div class="memo"></div>
                        </td>
                        <td>
                            <span class="linkBtn" data-fun="updateEqBtn">修改</span>
                            <span class="linkBtn" data-fun="editLog">修改记录</span>
                        </td>
                    </tr>
                </table>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel()">关 闭</span>
        </div>
    </div>
        <%-- 统一设置 --%>
        <div class="bonceContainer bounce-blue" id="uniformSettings" style="width: 600px;">
            <div class="bonceHead">
                <span>统一设置</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="catBody">
                    <div>要统一设置为哪个类别？请选择！</div>
                    <div class="ty-color-blue">注：“确定”后，各行均将加载为统一设置的类别，无论之前是否已单选！</div>
                    <div class="uniformCatName">
                        <input id="uniformCatName" type="text" onclick="uniformSet()" /><span class="downBtn"><i class="fa fa-sort-down"></i></span>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取 消</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="uniformSettingsSure()">确 定</span>
            </div>
        </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>装备清单</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div class="ty-container">
                <%-- 主页面 --%>
                <div class="mainCon mainCon0">
                    <table class="allWidth">
                        <tr>
                            <td>
                                待处理的数据共<span class="unCount"></span>条<span class="linkBtn gapLt" data-fun="handleNoCategory">去处理</span>
                            </td>
                            <td style="text-align: right;"><div>经处理，已确认不属于装备器具的数据共<span class="notTip"></span>条 <span class="ty-color-gray gapLt">去查看</span></div></td>
                        </tr>
                    </table>
                    <hr class="hr">
                    <span>以下数据共<span id="handledNum"></span>条</span>
                    <div class="filter">
                        <span>筛选</span>
                        <input type="text" id="filterCat" readonly class="form-control funBtn" data-fun="filterCatBtn"/>
                        <i class="fa fa-angle-down"></i>
                    </div>
                    <div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>装备器具名称/型号</td>
                                <td>来源</td>
                                <td>单位</td>
                                <td>数量</td>
                                <td>所属类别</td>
                                <td>参与加工的产品</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody id="allList">

                            </tbody>

                        </table>
                        <div id="ye0"></div>
                    </div>
                </div>
                <div class="mainCon mainCon1">
                    <div class="allWidth">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backMain" data-num="0">返 回</span>
                        <div class="ty-right alltip">
                            <i class="fa fa-circle-o funBtn" data-fun="toogeCircle" id="sflag"></i>
                            待处理数据共<span class="unCount"></span>条，本次操作确认了<span class="readySet">0</span>条。本次操作已完成！
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapLt" onclick="selectCatSure()">确  定</span>
                        </div>
                    </div>
                    <div>
                        <div class="clear gapB">
                            <div class="ty-left">
                                <div>以下为待处理的数据，请确认哪些属于装备器具，并对属于装备器具的选定类别。</div>
                                <div>需分类的数据较多时，可“批量处理”。</div>
                            </div>
                            <span class="ty-right linkBtn" data-fun="batchClass">批量处理</span>
                        </div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>系统赋予的编码</td>
                                <td>装备器具编号/名称/型号</td>
                                <td>来源</td>
                                <td colspan="2">所属类别</td>
                            </tr>
                            </thead>
                            <tbody id="tbContent">

                            </tbody>

                        </table>
                        <div id="ye"></div>
                    </div>
                </div>
                <div class="mainCon mainCon2">
                    <div class="allWidth">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backMain" data-num="0">返回主页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backCon" data-num="1">返回上一页</span>
                        <div class="ty-right">
                            <i class="fa fa-circle-o funBtn" data-fun="toogeCircle" id="toogeCircle"></i>
                            待处理数据共<span id="weiNum"></span>条，本次操作确认了<span id="selectNum"></span>条。本次操作已完成！
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapLt" data-fun="saveBatchCat">确定</span>
                        </div>
                    </div>
                    <div class="pa40">
                        <p>点击类别右侧的“<span class="fa fa-angle-right"></span>”（进入符号）后，可在随后所见的页上选择属于该类别的装备器具。</p>
                        <p>带有子类别的类别右侧无“<span class="fa fa-angle-right"></span>”，但左侧带有“<span class="fa fa-angle-double-down"></span>”（展开符号）。点击该符号后，将展示该类别下的子类别。</p>
                    </div>
                    <div id="batchClassCon">
                    </div>
                </div>
                <div class="mainCon mainCon3">
                    <div>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backMain" data-num="0">返回主页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backCon" data-num="2">返回上一页</span>
                        <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="selectEQOK" data-num="2">确 定</span>
                    </div>
                    <div style="padding:20px 40px;">
                        <p>
                            请选择属于<span id="catPath"></span>的装备器具。
                            <span class="ty-right selectCount">已选：<span id="cSelectNum"></span>项</span>
                        </p>
                        <table class="ty-table" id="selectTab">
                            <tbody>
                            <tr>
                                <td>系统赋予的编码</td>
                                <td>固定资产编号</td>
                                <td>固定资产名称</td>
                                <td>型号</td>
                                <td>创建</td>
                            </tr>
                            </tbody>
                        </table>

                    </div>

                </div>
                <div class="mainCon mainCon4">
                    <div>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backMain">返回</span>
                    </div>
                    <div style="padding:20px 40px;">
                        <p class="filterTxt"></p>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>装备器具名称/型号</td>
                                <td>来源</td>
                                <td>单位</td>
                                <td>数量</td>
                                <td>所属类别</td>
                                <td>参与加工的产品</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody id="tbContent2">

                            </tbody>

                        </table>
                        <div id="ye2"></div>


                    </div>

                </div>
                <div class="mainCon mainCon5">
                    <div>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backMain" data-num="0">返回主页</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backCon" data-num="2" id="subPage">返回上一页</span>
                        <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="sameEqComplete" data-num="1">确 定</span>
                        <div class="ty-right setTip"><i class="fa fa-circle-o funBtn" data-fun="toogeCircle"></i>  设置完成！</div>
                    </div>
                    <div style="padding:40px 0;">
                        <p><span class="sameTip"></span>为防遗漏，特一并列出，请为这些设备设置类别。</p>
                        <div class="clear">
                            <div class="ty-left">
                                <div class="ty-color-blue">注1 设置时，可统一设置，也可分别设置。</div>
                                <div class="ty-color-blue">注2 设置规则为，如选择了一二三四类中的某个类别，则不可以在选择一二三四类中的其他类别。</div>
                            </div>
                            <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="initUniform()">统一设置</span>
                        </div>
                        <table class="ty-table" id="selectCatPath">
                            <tbody>
                            <tr>
                                <td>系统赋予的编码</td>
                                <td>装备器具编号</td>
                                <td>创建</td>
                                <td colspan="2">所属类别</td>
                            </tr>
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/equipment/equipList.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
