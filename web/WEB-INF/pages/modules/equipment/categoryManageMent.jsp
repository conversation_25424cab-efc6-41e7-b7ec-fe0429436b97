<%--
  Created by IntelliJ IDEA.
  User: sy
  Date: 2023/2/8
  Time: 15:39
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/equipment/categoryManageMent.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed2"></div>
<div class="bounce_Fixed">
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue divbox" id="modifyrecord" style="left: 528px;top: -100.5px;z-index: 1;width: 1001px;display: block;position: fixed;">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td style="width: 14%;">事件</td>
                    <td style="width: 25%;">操作</td>
                    <td style="width: 18%;">操作后的类别名称</td>
                    <td style="width: 42%;">操作后的所含内容/使用的主要场合</td>
                </tr>
                </thead>
                <tbody id="mdifrordbox">
                <tr>
                    <td>第X次修改后</td>
                    <td>XXX XXXX-XX-XX XX:XX:XX</td>
                    <td>XXXXX</td>
                    <td>XXXXXXXXXXXXXXXXX</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" id="closerecord" onclick="closeon()" style="background-color:#5d9cec;color: white;">关闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 新增一级类别 --%><%-- 原来只需要给下面的div设置上display:none;就可以避免它乱跑的问题了。--%>
    <div class="bonceContainer bounce-blue divbox" id="addcate" style="left: 642.5px;top: -110px;width: 610px;z-index: 1;">
        <div class="bonceHead">
            <span>新增一级类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon bonceConbox">
            <div class="cale_ttl1 namefont">
                <span>一级类别名称</span>
            </div>
            <div class="sale_con1 numberfont">
                <span id="catishi1" class="numbersi">0</span>/10
                <br />
                <input type="text" id="addequcate" class="add_eqcate inpfont" name="eqcate">
            </div>
            <br />
            <div class="cale_ttl2 namefont">
                <span>所含内容/使用的主要场合</span>
            </div>
            <div class="sale_con2 numberfont">
                <span id="catishi2" class="numbersi">0</span>/30
                <br />
                <input type="text" id="addeqthng" class="add_eqthng inpfont" name="eqthng">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel($('#addcate'))">取消</span>
            <span class=" ty-btn-blue ty-btn ty-btn-big ty-circle-3" onclick="addcamad()" id="addcasur">确定</span>
        </div>
    </div>
    <%-- 更多操作说明 --%>
    <div class="bonceContainer bounce-blue divbox" id="moreerainsucons" style="left: 615.5px;top: -26px;width: 691px;">
        <div class="bonceHead">
            <span>更多操作说明</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="title" style="margin-top: 44px;">
                <div class="titlebox">
                    <span  class="nubfon">1</span>
                    <div class="wordfon">
                        <span>系统自带9个装备器具的一级类别。</span>
                        <br />
                        <span>如需要，可创建各类别的同级类别或子类别，或修改各类别的基本信息。</span>
                    </div>
                </div>
                <div class="titlebox">
                    <span class="nubfon">2</span>
                    <div class="wordfon">
                        <span>贵公司如已启用“工序管理”功能，以下内容需继续阅读，否则请忽略。</span>
                    </div>
                </div>
                <div class="titlebox">
                    <span class="nubfon">3</span>
                    <div class="wordfon">
                        <span>“工序管理”单元下</span>
                    </div>
                </div>
                <div class="titlebox">
                    <span class="nubfon">3.1</span>
                    <div class="wordobo">
                        <span>含有“制造用的机器/设备”“制造用的夹具、模具”与“检测用的设备/手段”三</span>
                        <br />
                        <span>个栏目。这三个栏目均需选择选项；</span>
                    </div>
                </div>
                <div class="titlebox">
                    <span class="nubfon">3.2</span>
                    <div class="wordobo">
                        <span>“制造用的机器/设备”的选项，默认为一类装备中的各装备器具；</span>
                    </div>
                </div>
                <div class="titlebox">
                    <span class="nubfon">3.3</span>
                    <div class="wordobo">
                        <span>“制造用的夹具、模具”的选项，默认为二类装备中的各装备器具；</span>
                    </div>
                </div>
                <div class="titlebox">
                    <span class="nubfon">3.4</span>
                    <div class="wordobo">
                        <span>“检测用的设备/手段”的选项，默认为三类装备与四类装备中的各装备器具；</span>
                    </div>
                </div>
                <div style="display: flex;margin-left: 68px;">
                    <span class="nubfon">3.5</span>
                    <div class="wordobo">
                        <span>如需要，可修改上述选项的范围，但需在“工序管理”中操作。</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn-blue ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel($('#moreerainsucons'))">关闭</span>
        </div>
    </div>
    <%--修改类别的基本信息--%>
    <div class="bonceContainer bounce-blue divbox" id="moiybaicifomtion" style="left: 603px;top: -176px;z-index: 1;width: 681px;">
        <div class="bonceHead">
            <span>修改类别的基本信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon bonceConbox">
            <div class="cale_ttl1 caleall">
                <span>当前的类别名称</span>
                <span class="ty-color-blue funBtn thin-btn pull-right typeface" onclick="catrecord($(this))">修改记录</span>
            </div>
            <div class="sale_con1 saleall">
                <input type="text" id="curetctname" class="oldcetme scale" name="curetctegrname">
            </div>
            <br />
            <div class="cale_ttl2 caleall">
                <span>当前所含内容/使用的主要场合</span>
            </div>
            <div class="sale_con2 saleall">
                <input type="text" id="contentusage" class="impoentsg scale" name="cotntage">
            </div>
            <br />
            <div class="cale_ttl3 caleall">
                <span>新的类别名称</span>
            </div>
            <div class="sale_con3 saleall">
                <span id="catnewme1" class="pachage">0</span>/10
                <br />
                <input type="text" id="newcateorname" class="add_catname scale" name="newcatne">
            </div>
            <br />
            <div class="cale_ttl4 caleall">
                <span>新的所含内容/使用的主要场合</span>
            </div>
            <div class="sale_con4 saleall">
                <span id="catnewme2" class="pachage">0</span>/30
                <br />
                <input type="text" id="newconusge" class="add_conng scale" name="newcoag">
            </div>
            <div class="numberfont" style="color: #5d96e6;margin-top: 3px;">注：修改哪项填写哪项，不修改的无需理会！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel($('#mmoiybaicifomtion'))">取消</span>
            <span class="ty-btn-blue ty-btn ty-btn-big ty-circle-3 parent" onclick="upmakesure($(this))">确定</span>
            <span class="ty-btn-blue ty-btn ty-btn-big ty-circle-3 child" onclick="upchidmdsre($(this))">确定</span>
        </div>
    </div>
    <%-- 新增同级子类别 --%>
    <div class="bonceContainer bounce-blue divbox" id="addsimchid" style="width: 610px;">
        <div class="bonceHead">
            <span>新增同级子类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon bonceConbox">
            <div style="margin-left: 88px;">新增<span id="simbcat">XXXXXXXX</span>的同级子类别</div>
            <br />
            <div class="cale_ttl1 namefont">
                <span>子类别名称</span>
            </div>
            <div class="sale_con1 numberfont">
                <span id="catchid1" class="numbersi">0</span>/10
                <br />
                <input type="text" id="addchidname" class="add_chid inpfont" name="chidname">
            </div>
            <br />
            <div class="cale_ttl2 namefont">
                <span>所含内容/使用的主要场合</span>
            </div>
            <div class="sale_con2 numberfont">
                <span id="catchid2" class="numbersi">0</span>/30
                <br />
                <input type="text" id="addchidthng" class="add_chid inpfont" name="chidthng">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel($('#addsimchid'))">取消</span>
            <span class="ty-btn-blue ty-btn ty-btn-big ty-circle-3" onclick="addchdsure($(this))" id="addcachid">确定</span>
        </div>
    </div>
    <%-- 删除提示弹窗 --%>
    <div class="bonceContainer bounce-red" id="detunemd">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="box">
                <p>
                    <span>确定删除该类别吗?</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="detenalno()" id="detsure">确定</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updetalno()" id="updetun">确定</span>
        </div>
    </div>
    <%-- 停用提示弹窗 --%>
    <div class="bonceContainer bounce-red" id="stpunten">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="box">
                <p>
                    <span>确定停用该类别吗？</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="stoaneld()" id="stopnen">确定</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="stochidld()" id="stopchid">确定</span>
        </div>
    </div>
    <%-- 恢复提示弹窗 --%>
    <div class="bonceContainer bounce-red" id="tenbck">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="box">
                <p>
                    <span>确定恢复使用该类别吗？</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="tebmadure()" id="tebsure">确定</span>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>类别管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div style="min-height: 750px;">
                <div class="ty-container">
                    <%--类别管理首页列表--%>
                    <div class="ty-container" id="cateagent">
                        <div>
                            <div class="main rolor">
                                <div style="position: relative;">
                                    <div class="word" style="position: absolute;">
                                        装备器具现有如下<span class="connber">XX</span>个一级类别。您可“新增一级类别”，也可管理各直属子类别。
                                        <br />
                                        此外如必要，还可“停用”或“删除”某类别，或修改各类别的基本信息。
                                    </div>
                                    <div class="deposit" id="depositall"></div>
                                    <div class="deposit" id="depositchid"></div>
                                    <div class="deposit" id="depositthe"></div>
                                    <div class="pull-right" style="position: absolute;top: 5px;right: 0px;">
                                        <span class="ty-color-blue funBtn thin-btn typeface" id="stopoldcategory" style="margin-left: -90px;margin-right: 132px;"
                                              onclick="stopcatfi()">被停用的类别</span>
                                        <span class="ty-color-blue funBtn thin-btn typeface" id="newonecategory" style="margin-left: -90px;margin-right: 39px;"
                                              onclick="addcatfri()">新增一级类别</span>
                                        <span class="ty-color-blue funBtn thin-btn typeface" id="operatiitructions" onclick="moretack()">更多操作说明</span>
                                    </div>
                                </div>
                                <br />
                                <div class="equipcatdata" style="margin-top: 46px;">
                                    <table class="ty-table ty-table-control">
                                        <thead style="background: none">
                                        <tr>
                                            <td>类别名称</td>
                                            <td>所含内容/使用的主要场合</td>
                                            <td>直属的子类别</td>
                                            <td>操作</td>
                                        </tr>
                                        </thead>
                                        <tbody class="sale_Tbody" id="equipcatmess">
                                        <tr>
                                            <td>一级装备</td>
                                            <td>直接用于生产产品或零件的机器、设备及其他类似装备</td>
                                            <td class="ty-td-control">
                                                <span class="ty-color-blue funBtn" onclick="subcategory()">XX个</span>
                                            </td>
                                            <td class="ty-td-control">
                                                <span class="ty-color-blue funBtn" onclick="upbscination()">修改基本信息</span>
                                                <span class="ty-color-red funBtn" onclick="">停用</span>
                                                <span class="ty-color-red funBtn" onclick="">删除</span>
                                                <span class="hd">JSON值</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>二级装备</td>
                                            <td>直接用于生产产品或零件的模具、夹具、工装及其他类似装备</td>
                                            <td class="ty-td-control">
                                                <span class="ty-color-blue funBtn" onclick="subcategory()">XX个</span>
                                            </td>
                                            <td class="ty-td-control">
                                                <span class="ty-color-blue funBtn" onclick="upbscination()">修改基本信息</span>
                                                <span class="ty-color-red funBtn" onclick="">停用</span>
                                                <span class="ty-color-red funBtn" onclick="">删除</span>
                                                <span class="hd">JSON值</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>XXXXXXX</td>
                                            <td>直接用于产品或零件的检测仪器及其他类似装备</td>
                                            <td class="ty-td-control">
                                                <span class="ty-color-blue funBtn" onclick="subcategory()">XX个</span>
                                            </td>
                                            <td class="ty-td-control">
                                                <span class="ty-color-blue funBtn" onclick="upbscination()">修改基本信息</span>
                                                <span class="ty-color-red funBtn" onclick="">停用</span>
                                                <span class="ty-color-red funBtn" onclick="">删除</span>
                                                <span class="hd">JSON值</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>XXXXXXX</td>
                                            <td>直接用于产品或零件的量具、检具及其他类似装备</td>
                                            <td class="ty-td-control">
                                                <span class="ty-color-blue funBtn" onclick="subcategory()">XX个</span>
                                            </td>
                                            <td class="ty-td-control">
                                                <span class="ty-color-blue funBtn" onclick="upbscination()">修改基本信息</span>
                                                <span class="ty-color-red funBtn" onclick="">停用</span>
                                                <span class="ty-color-red funBtn" onclick="">删除</span>
                                                <span class="hd">JSON值</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <%-- 直属的子类别列表 --%>
                    <div class="ty-container" id="diretsbaeory" style="display: none;margin: 35px 20px 20px 20px;">
                        <button type="button" class="btn  btn-default" id="dirback" onclick="dirseorby()" style="margin: 0 96px;width: 76px;">返回</button>
                        <div class="pageStyle container_item">
                            <div class="initBody" style="margin: 0 96px;">
                                <div class="title" style="margin-top: 42px;">
                                    直属于<span class="name">XX</span>类别名称的子类别现有<span class="pont">0</span>个。
                                    您可“新增同级子类别”，也可管理各直属的子类别。
                                    <br />
                                    此外如必要，还可“停用”或“删除”某类别，或修改各类别的基本信息。
                                    <span class="ty-color-blue funBtn thin-btn pull-right" onclick="addchidcat()" id="addsamchid" style="font-weight: bold;">新增同级子类别</span>
                                </div>
                                <table class="ty-table bg-yellow ty-table-control">
                                    <thead style="background: none;" id="dirstl">
                                    <tr>
                                        <td>类别名称</td>
                                        <td>所含内容/使用的主要场合</td>
                                        <td>直属的子类别</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody id="dircatbox" class="ty-table">
                                    <tr>
                                        <td>XXXXXXX</td>
                                        <td>XXXXXXXXXXXXX</td>
                                        <td class="ty-td-control">
                                            <span class="ty-color-blue funBtn" onclick="">XX个</span>
                                        </td>
                                        <td>
                                            <span class="ty-color-blue funBtn" onclick="upbscination()">修改基本信息</span>
                                            <span class="ty-color-red funBtn" onclick="">停用</span>
                                            <span class="ty-color-red funBtn" onclick="">删除</span>
                                            <span class="hd">JSON值</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <%-- 停用类别列表 --%>
                    <div class="ty-container" id="stopcatoney" style="display: none;margin: 35px 20px 20px 20px;">
                        <button type="button" class="btn btn-default" id="stpback" onclick="stopcoey()" style="margin: 0 96px;width: 76px;">返回</button>
                        <div class="pageStyle container_item">
                            <div class="initBody" style="margin: 0 96px;">
                                <div class="title" style="margin-top: 42px;">
                                    以下为被停用的类别
                                </div>
                                <table class="ty-table bg-yellow ty-table-control">
                                    <thead style="background: none;" id="stosd">
                                    <tr>
                                        <td>类别名称</td>
                                        <td>所含内容/使用的主要场合</td>
                                        <td>停用时间</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody id="stocatbox" class="ty-table">
                                    <tr>
                                        <td>XXX</td>
                                        <td>XXXXXX</td>
                                        <td>2023.05.08 11:38:20</td>
                                        <td>
                                            <span class="ty-color-blue funBtn" onclick="">恢复使用</span>
                                            <span class="ty-color-red funBtn" onclick="">删除</span>
                                            <span class="hd">JSON值</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/equipment/categoryManageMent.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
