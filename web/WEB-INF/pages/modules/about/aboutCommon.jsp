<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/fileTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .section_body{
        display: flex;
    }
    .section_left{
        min-width: 270px;
        flex: none;
    }
    .section_left nav{
        width: 100%;
        background-color: #f2f2f2;
        min-height: 600px;
        padding: 16px;
    }
    .section_right{
        background-color: #f9f9f9;
        width: 1000px;
        padding: 16px;
        flex: none;
    }
    .ty-fileHandle{
        flex: auto;
        text-align: right;
        position: relative;
    }
    .page{
        width: 1270px;
    }
    .ty-fileItem{
        height: inherit;

    }
    .ty-fileType{
        width: 20px;
        height: 24px;
        -webkit-background-size: 20px;
        background-size: 20px;
        margin-right: 8px;
    }
    .ty-pre{
        border: none;
        background: inherit;
        font-family: inherit;
        white-space: pre-line;
    }
    .ty-fileNull{
        height: 128px;
        padding-top: 120px;
        text-align: center;
        min-height: 593px;
        color: #aaa;
        font-size: 14px;
    }
    .ty-fileNull img{
        width: 96px;
    }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span class="pageTotalName"><%--使用帮助--%></span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="main">
                    <div class="page page_main">
                        <section class="section_header search">
                            <div class="text-right">
                            <span class="ty-search">
                                <input class="ty-searchInput" id="fileNameOrSn" type="text" placeholder="请录入您要查找的关键词">
                                <div class="ty-searchBtn" onclick="searchBtn()"></div>
                            </span>
                            </div>
                        </section>
                        <section class="section_body">
                            <%--竖型文件树容器--%>
                            <section class="section_left">
                                <nav id="aboutTree" class="left ty-colFileTree" data-name="main">

                                </nav>
                            </section>
                            <section class="section_right">
                                <div class="ty-fileList mainFileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_con"></div>
                            </section>
                        </section>
                    </div>
                    <div class="page page_search" style="display: none">
                        <div class="ty-right">
                            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-orange" onclick="backToMainPage()">返回</button>
                        </div>
                        <div class="mar">
                            <div class="searchInfo">
                                <div class="fileContent">
                                    <div class="searchFile"></div>
                                    <div id="ye-search-file"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="txtShow" style="display: none">
                    <div class="btn-group">
                        <button class="ty-btn ty-btn-big ty-btn-orange ty-circle-2" onclick="backToMain()">返回</button>
                    </div>
                    <section class="txtContent">
                        <pre class="ty-pre"></pre>
                    </section>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/about/aboutCommon.js?v=SVN_REVISION"></script>

<%@ include  file="../../common/footerBottom.jsp"%>
