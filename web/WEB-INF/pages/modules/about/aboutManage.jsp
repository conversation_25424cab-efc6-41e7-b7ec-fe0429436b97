<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/fileTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/about/about.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<%--<link href="../css/resourceCenter/docManage.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>--%>
<style>
    header{
        margin-bottom: 8px;
    }
    .section_body{
        display: flex;
    }
    .section_left{
        min-width: 270px;
        flex: none;
    }
    .section_left nav{
        width: 100%;
        background-color: #f2f2f2;
        min-height: 600px;
        padding: 16px;
    }
    .section_right{
        background-color: #f9f9f9;
        width: 1000px;
        padding: 16px;
        flex: none;
    }
    .ty-fileHandle{
        flex: auto;
        text-align: right;
        position: relative;
    }
    .page{
        width: 1270px;
    }
    .ty-pre{
        white-space: normal;
        border: 1px solid #eee;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%-- 文件查看基本信息 --%>
    <div class="bonceContainer bounce-blue" id="docInfoScan" style="width:800px">
        <div class="bonceHead">
            <span>基本信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ">
            <div class="infoCon clearfix">
                <h4 class="info_title" id="info_title"></h4>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl">文件编号：</span><span class="con" id="info_sn"></span></div>
                    <div class="trItem"><span class="ttl">保存位置：</span><span class="con" id="info_category"></span></div>
                </div>
                <div class="ty-left">
                    <div class="trItem"><span class="ttl2">版本号：</span><span class="con" id="info_gn"></span></div>
                    <div class="trItem"><span class="ttl2">创建人：</span><span class="con"><span id="info_createName" class="info_name"></span><span id="info_createDate"></span></span></div>
                </div>
            </div>
            <div>
                <div><div class="trItem info_content"><span class="ttl">说明：</span><span class="con" id="info_description" style="max-width: 670px"></span></div></div>
            </div>
            <div class="infoCon clearfix">
                <div class="ty-left processHistory">
                    <div class="item-header" style="margin-left: 10px">
                        审批记录
                    </div>
                    <div class="processList">
                    </div>
                </div>
                <div class="ty-left censusInfo">
                    <div class="item-header">
                        统计信息
                    </div>
                    <%--<div class="trItem">--%>
                    <%--<span class="ttl3">查看次数：</span>--%>
                    <%--<span class="con times view_num"></span>--%>
                    <%--<button class="ty-btn ty-btn-blue ty-btn-small" id="viewNumBtn" onclick="seeHandelRecordBtn(1)">查看记录</button>--%>
                    <%--</div>--%>
                    <div class="trItem">
                        <span class="ttl3">移动次数：</span>
                        <span class="con times move_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-2" id="moveNumBtn" onclick="seeHandelRecordBtn(5)">移动记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件名称修改次数：</span>
                        <span class="con times name_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-2" id="changeNameNumBtn" onclick="seeHandelRecordBtn(6)">修改记录</button>
                    </div>
                    <div class="trItem">
                        <span class="ttl3">文件编号修改次数：</span>
                        <span class="con times no_num"></span>
                        <button class="ty-btn ty-btn-blue ty-circle-2" id="changeNoNumBtn" onclick="seeHandelRecordBtn(7)">修改记录</button>
                    </div>
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--更改文件名称--%>
    <div class="bonceContainer bounce-blue" id="changeFileName">
        <div class="bonceHead">
            <span>修改文件名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>当前文件名称：<b class="currentFileName"></b></div>

            <div>请录入新的文件名称：</div>
            <input type="text" class="changeFileName" style="width: 100%">
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="changeFileNameBtn"  onclick="sureChangeFileName()">确定</button>
        </div>
    </div>
    <%--更改文件编号--%>
    <div class="bonceContainer bounce-blue" id="changeFileNo">
        <div class="bonceHead">
            <span>修改文件编号</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>当前文件编号：<b class="currentFileNo"></b></div>

            <div>请录入新的文件编号</div>
            <input type="text" class="changeFileNo" style="width: 100%">
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="changeFileNoBtn" onclick="sureChangeFileNo()">确定</button>
        </div>
    </div>
    <%-- 高级查询弹窗 --%>
    <div class="bonceContainer bounce-blue " id="advancedSearch" data-type="0" style="width: 600px">
        <div class="bonceHead">
            <span>高级搜索</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <ul class="ty-secondTab">
                <li class="ty-active">文件搜索</li>
                <li>文件类别搜索</li>
            </ul>
            <div class="searchCon">
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件名称
                    </div>
                    <div class="eq_r">
                        <input type="text" class="name">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件编号
                    </div>
                    <div class="eq_r">
                        <input type="text" class="fileSn">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        上传人
                    </div>
                    <div class="eq_r">
                        <input type="text" class="createName" />
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        上传时间
                    </div>
                    <div class="eq_r">
                        <input type="text" class="createDateBegin" id="uploadDateStart"> ~
                        <input type="text" class="createDateEnd" id="uploadDateEnd">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        换版人
                    </div>
                    <div class="eq_r">
                        <input type="text" class="updateName">
                    </div>
                </div>
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        换版时间
                    </div>
                    <div class="eq_r" id="postDate">
                        <input type="text" class="updateDateBegin" id="ChangeDateStart"> ~
                        <input type="text" class="updateDateEnd" id="ChangeDateEnd">
                    </div>
                </div>
            </div>
            <div class="searchCon" style="display: none">
                <div class="eq_item clearfix">
                    <div class="eq_l">
                        文件类别
                    </div>
                    <div class="eq_r">
                        <input type="text" class="folderName">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="advancedSearchBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--选择保存位置--%>
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 650px">
        <div class="bonceHead">
            <span class="bounce_title">选择保存位置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <nav class="ty-colFileTree" data-name="chooseSaveFolder">
                <ul>
                    <li>
                        <div class="ty-treeItem" title="全部" children="2" level="0" parent="null" directoryAble="false">
                            <i class="fa fa-angle-right"></i>
                            <i class="fa fa-folder"></i>
                            <span>全部</span>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" type="btn" data-name="sureChooseFolder" id="sureChooseFolderBtn">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="moveFile" style="width: 650px">
        <div class="bonceHead">
            <span class="bounce_title">移动到</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <nav class="ty-colFileTree" data-name="moveTo">
                <ul>
                    <li>
                        <div class="ty-treeItem ty-treeItemActive" title="全部" children="4" level="0" parent="null" directoryAble="false">
                            <i class="fa fa-angle-right"></i>
                            <i class="fa fa-folder"></i>
                            <span>全部</span>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" type="btn" data-name="sureMoveFile" id="sureMoveToBtn">确定</button>
        </div>
    </div>
    <%--各种操作记录--%>
    <div class="bonceContainer bounce-blue" id="handleRecord" style="width: 650px">
        <div class="bonceHead">
            <span class="recordTitleName">修改文件编号</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:300px; overflow-y: auto; ">
            <table class="ty-table recordTable"></table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>内容管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" style="min-width: 1206px">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container clearfix">
                <div class="main">
                    <div class="page page_main">
                        <header>
                            <div class="text-right">
                            <span class="ty-search">
                                <%--<span class="ty-search-ttl"></span>--%>
                                <input class="ty-searchInput" id="fileNameOrSn" type="text" placeholder="文件名称/文件编号">
                                <div class="ty-searchBtn" onclick="searchBtn()"></div>
                            </span>
                                <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue customQuery" id="customQueryBtn" onclick="advanceSearchBtn()">高级搜索</button>
                                <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green" type="btn" data-name="txtUpload" id="txtUploadBtn">上传内容</button>
                            </div>
                        </header>
                        <section class="section_body">
                            <%--竖型文件树容器--%>
                            <section class="section_left">
                                <nav id="aboutTree" class="left ty-colFileTree" data-name="main">
                                    <ul>
                                        <li>
                                            <div class="ty-treeItem ty-treeItemActive" title="全部" children="4" level="0" parent="null" directoryAble="false">
                                                <i class="fa fa-angle-right"></i>
                                                <i class="fa fa-folder"></i>
                                                <span>全部</span>
                                            </div>
                                        </li>
                                    </ul>
                                </nav>
                            </section>
                            <section class="section_right">
                                <%--<ul class="ty-secondTab" id="fileSort" style="display: none">--%>
                                <%--<li class="ty-active">发布时间 <span class="sort sortName"> <i class="fa fa-sort-amount-down-alt"></i></span></li>--%>
                                <%--<li type="1">文件编号 <span class="sort sortName" style="display: none"> <i class="fa fa-sort-amount-down-alt"></i></span></li>--%>
                                <%--</ul>--%>
                                <div class="ty-fileList mainFileList" >
                                    <%--文件列表--%>
                                </div>
                                <div id="ye_con"></div>
                            </section>
                        </section>
                    </div>
                    <div class="page page_search" style="display: none">
                        <div class="text-right">
                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="rebackBtn" onclick="goBack('query')">返回</button>
                        </div>
                        <div class="mar">
                            <div class="searchInfo">
                                <div class="folderContent">
                                    <div class="searchFolder">
                                        <div class="searchFolderContent"></div>
                                        <div id="ye-search-folder"></div>
                                    </div>
                                    <div class="childFolder"></div>
                                </div>
                                <div class="fileContent">
                                    <div class="searchFile"></div>
                                    <div id="ye-search-file"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="page page_contentApply" style="display: none">
                        <div class="btn-group">
                            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-orange" type="btn" data-name="back">返回</button>
                            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" type="btn" data-name="sureTxtUpload">提交</button>
                        </div>
                        <section class="txtApply">
                            <div class="item-row">
                                <div class="item-title">文件名称 <span class="ty-color-red">*</span></div>
                                <div class="item-content">
                                    <input type="text" name="name" class="ty-inputText" require>
                                    <i class="fa fa-times-circle clearInput"></i>
                                </div>
                                <div class="item-title">文件编号 <span class="ty-color-red">*</span></div>
                                <div class="item-content">
                                    <input type="text" name="fileSn" class="ty-inputText" require>
                                    <i class="fa fa-times-circle clearInput"></i>
                                </div>
                            </div>
                            <div class="formInput">
                                <div class="item-row">
                                    <div class="item-title">
                                        请选择保存位置
                                        <button class="ty-btn ty-circle-2 ty-btn-blue" type="btn" data-name="chooseSaveFolder" id="chooseSaveFolderBtn">选择</button>
                                    </div>
                                </div>
                                <div class="item-row">
                                    <div class="item-title"></div>
                                    <div class="item-content">
                                        <div class="savePlace"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="formShow">
                                <div class="item-row">
                                    <div class="item-title">保存位置</div>
                                    <div class="item-content">
                                        <div class="savePlace"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="item-row">
                                <div class="item-title" style="width: 404px">
                                    <div class="ty-radio">
                                        <input type="radio" name="isNeedOther" value="0" id="noNeedOther">
                                        <label for="noNeedOther"></label> <span id="uploadDirect">本文件直接提交给文管，无需他人审批</span>
                                    </div>
                                </div>
                                <div class="item-title">
                                    <div class="ty-radio">
                                        <input type="radio" name="isNeedOther" id="needOther" value="1" checked>
                                        <label for="needOther"></label> 本文件需审批
                                    </div>
                                </div>
                                <div class="item-content">
                                    <select class="ty-inputSelect chooseApprover">
                                        <option value="选择审批人" selected></option>
                                    </select>
                                </div>
                            </div>
                            <div class="item-row formInput">
                                <div class="item-title">
                                    说明
                                </div>
                                <div class="item-content">
                                    <textarea name="description" cols="30" rows="1"></textarea>
                                </div>
                            </div>
                            <div class="item-row formShow">
                                <div class="item-title">
                                    换版原因
                                </div>
                                <div class="item-content">
                                    <textarea name="reason" cols="30" rows="1"></textarea>
                                </div>
                            </div>
                            <div class="item-row">
                                <div class="item-title">
                                    内容 <span class="ty-color-red">*</span>
                                </div>
                                <div class="item-content">
                                    <textarea name="content" cols="30" rows="8" require></textarea>
                                </div>
                            </div>
                        </section>
                    </div>
                    <div class="page page_fileHistory" style="display: none">
                        <div class="btn-group">
                            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-orange" type="btn" data-name="back">返回</button>
                        </div>
                        <section class="fileHistory">
                            <div class="ty-fileList historyFileList" >
                                <%--文件列表--%>
                            </div>
                            <div id="ye_record"></div>
                        </section>
                    </div>
                </div>
                <div class="txtShow" style="display: none">
                    <div class="btn-group">
                        <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-orange" type="btn" data-name="back">返回</button>
                    </div>
                    <section class="txtContent">
                        <pre class="ty-pre"></pre>
                    </section>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var generalType = '${generalType}' ; // 标记当前用户身份 ，0 - 超管 1 - 大总务 ， 2 - 小总务 ， null - 普通员工
    var isGeneral = generalType === '1' || generalType === '2'
    var isSuper = generalType === '0'
    console.log('generalType', generalType)
    if(isGeneral){
        $("#txtUploadBtn").html("上传内容");
        $("#uploadDirect").html("本文件直接发布，无需他人审批");

    }else {
        $("#txtUploadBtn").html("内容发布申请");
        $("#folderUploadBtn").remove()
        $("#uploadDirect").html("本文件直接提交给文管，无需他人审批");
    }
</script>
<script src="../script/about/aboutManage.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
