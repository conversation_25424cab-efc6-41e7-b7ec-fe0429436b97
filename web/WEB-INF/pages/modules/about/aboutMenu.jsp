<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/file/fileTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<%--<link href="../css/resourceCenter/docManage.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>--%>
<style>
    .ty-container{
        display: flex;
    }
    .section_left{
        min-width: 270px;
        flex: none;
    }
    .section_left nav{
        width: 100%;
        background-color: #f2f2f2;
        min-height: 600px;
    }
    .section_right{
        margin-left: 20px;
        flex: auto;
    }
    .nowFolder{overflow: hidden;margin-bottom: 70px; display: flex}
    .headPanel{ margin-right:30px; color: #5d9ded; min-width:250px; flex: none}
    .headPanel h3 {display: block;  height:35px; line-height: 35px; font-size: 18px;  font-weight: 500;  margin: 0; }
    .headPanel .operableBtn { line-height: 35px; border-top:2px solid #5d9ded;}
    .table_folderInfo{flex: auto}
    .def-table{width: 100%; max-width: 100%; margin-bottom: 20px;}
    .def-table thead tr td{ color:#777;border-bottom: 1px solid #ddd;}
    .def-table tr td{ padding: 8px; line-height: 1.42857;  vertical-align: top;text-align: center; color: #333; }
    .ty-colFileTree{  float: left;  min-width: 270px; max-width: 500px; color: #333;}
    .ty-colFileTree .ty-treeItem{  height: 30px;  line-height:30px;  vertical-align: middle;  padding:0 5px;  cursor: default;  }
    .ty-colFileTree .ty-treeItemActive{  background-color: #5d9ded; color:#fff   }
    .ty-colFileTree .ty-treeItem>i{  display: inline-block; vertical-align: top;  color: #666;  margin: 8px 0 0 3px;  width: 15px;  height: 15px;  text-align: center;  }
    .ty-colFileTree .ty-treeItem>i.fa-folder,.ty-colFileTree .ty-treeItem>i.fa-file{  color: #ddc667; margin-right: 5px }
    .ty-colFileTree .ty-treeItem>i.fa-trash{ color: #666; margin-right: 5px}
    .ty-colFileTree .ty-treeItemActive>i.fa{  color: #fff;}

    /*.ty-colFileTree .level2,.ty-colFileTree .level3,.ty-colFileTree .level4,.ty-colFileTree .level5{   }*/
    .ty-colFileTree li>ul{  margin-left:20px; }
    .operableBtn .fa{
        margin-right: 26px;
        cursor: pointer;
        font-size: 15px;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%--  update--%>
    <%--移动弹窗--%>
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 650px">
        <div class="bonceHead">
            <span class="bounce_title">移动到</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="folder_list ty-colFileTree" name="chooseFolder"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" type="btn" id="sureChooseFolderBtn" onclick="sureMoverFolder()">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-changeClass bounce-blue">
        <div class="bonceHead">
            <span>修改文件夹名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>将文件夹【<span class="folderNameChange"></span>】的名称修改为</p>
            <p class="text-center"><input type="text" class="ty-inputText categoryName" placeholder="vbxfg" style="width: 374px;"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeClass()">确定</span>
        </div>
    </div>
    <%-- delete --%>
    <div class="bonceContainer bounce-red" id="deleteClass">
        <div class="bonceHead">
            <span>删除文件夹</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>确定删除该文件夹？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big" onclick="sureDeleteClass()">确定</span>
        </div>
    </div>
    <%-- 新增子级类别 --%>
    <div class="bonceContainer bounce-newClass bounce-green">
        <div class="bonceHead">
            <span>新建子文件夹</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>在【<span class="recentParentFolder"></span>】下新建子文件夹</p>
            <p>子文件夹名称</p>
            <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
            <%--<p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。--%>
            <%--<span class="ty-right btnLink" onclick="scanSetBtn('son')">去设置使用权限</span>--%>
            <%--</p>--%>
            <%--<span class="hd" id="havRightUserCon"></span>--%>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewClass()">确定</span>
        </div>
    </div>
    <%-- 全部下新增文件夹 --%>
    <div class="bonceContainer bounce-newSameClass bounce-green">
        <div class="bonceHead">
            <span>新建子文件夹</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>在【全部】下新建子文件夹</p>
            <p>子文件夹名称</p>
            <p class="text-center"><input type="text" class="ty-inputText categoryName" style="width: 374px"></p>
            <%--<p>有权限使用该文件夹的职工：<span class="hasSettedNum"></span>位。--%>
            <%--<span class="ty-right btnLink" onclick="scanSetBtn('same')">去设置使用权限</span>--%>
            <%--</p>--%>
            <%--<span class="hd" id="havRightUserCon_same"></span>--%>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewSameClass()">确定</span>
        </div>
    </div>
    <%-- 文件夹名称修改记录 --%>
    <div class="bonceContainer bounce-blue" id="nameEditRecord" style="width: 660px;">
        <div class="bonceHead">
            <span>文件夹名称修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <h4 class="folderNameSee">作业指导书</h4>
            <div class="clear">
                <p class="ty-left recordTip"></p>
                <p class="ty-right recordEditer"></p>
            </div>
            <table class="ty-table ty-table-control historyCon">
                <thead>
                <tr>
                    <td>状态</td>
                    <td>文件夹名称</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center tipMsg"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" type="btn">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--选择保存位置--%>
    <div class="bonceContainer bounce-blue" id="chooseSaveFolder" style="width: 650px">
        <div class="bonceHead">
            <span class="bounce_title">选择保存位置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <nav id="chooseSaveFolderTree" class="ty-colFileTree" data-name="chooseSaveFolder">
                <ul>
                    <li>
                        <div class="ty-treeItem ty-treeItemActive" title="全部" children="4" level="0" parent="null" directoryAble="false">
                            <i class="fa fa-angle-right"></i>
                            <i class="fa fa-folder"></i>
                            <span>全部</span>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn" type="btn" data-name="sureChooseFolder" id="sureChooseFolderBtn">确定</button>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-blue " id="tip2">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="tipMess2" style="text-align: center;"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>目录</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" style="min-width: 1206px">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container clearfix">
                <%--竖型文件树容器--%>
                <section class="section_left">
                    <nav id="aboutTree" class="left ty-colFileTree" data-name="main">
                        <ul>
                            <li>
                                <div class="ty-treeItem ty-treeItemActive" title="全部" children="4" level="0" parent="null" directoryAble="false">
                                    <i class="fa fa-angle-right"></i>
                                    <i class="fa fa-folder"></i>
                                    <span>全部</span>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </section>
                <section class="section_right">
                    <header class="nowFolder">
                        <div class="headPanel">
                            <h3 class="folderName">全部</h3>
                            <div class="operableBtn"></div>
                        </div>
                        <table class="def-table table_folderInfo">
                            <thead>
                            <tr>
                                <td width="33%">包含</td>
                                <td width="33%">创建人</td>
                                <td>创建时间</td>
                            </tr>
                            </thead>
                            <%--此类别容器--%>
                            <tbody class="generalFolder"></tbody>
                        </table>
                    </header>
                    <article class="childFolder">
                        <div class="ty-alert"></div>
                        <table class="def-table addBold">
                            <thead>
                            <tr>
                                <td width="25%">文件夹名称</td>
                                <td width="25%">包含</td>
                                <td width="25%">创建人</td>
                                <td width="25%">创建时间</td>
                            </tr>
                            </thead>
                            <%--此类别容器--%>
                            <tbody></tbody>
                        </table>
                    </article>
                </section>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/about/aboutMenu.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
