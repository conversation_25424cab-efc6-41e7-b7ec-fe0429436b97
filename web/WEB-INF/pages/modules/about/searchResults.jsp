<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>

<%@ include  file="../../common/headerTop.jsp"%>
    <style type="text/css">
        .tr_class{
            border: medium solid #ffffff;
        }
        .hd{display: none}
    </style>
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="row juli1">
                <div class="col-md-1">
                    <div class="form-group">
                        <form action="../about/useHelp.do" id="form3">
                            <div class="input-group">
                                <span onclick="back()" class="input-group-addon btn btn-primary" style="margin-top: 100px;width: 100px;height: 45px;">返回</span>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="col-md-5">
                    <div class="form-group">
                        <form action="../about/searchResults.do" id="form2">
                            <div class="input-group">
                                <input id="find1" name="word" type="text" value="" style="width: 550px;" class="form-control input-lg"><span onclick="find()" class="input-group-addon btn btn-primary" style="width: 500px;">搜索</span>
                            </div>
                        </form>
                    </div>
                </div>

            </div>

            <div class="row juli1">
                <div class="col-md-4">
                    <div class="form-group">
                        <div id="table2" style="display: block">
                            <table class="table  table-hover">
                                </br>
                                </br>
                                <span align="center" height="80px;"><font size="5" color="aqua"> ${word}的搜索结果</font>
                                </span>
                                </br>
                                </br>
                                </br>
                                </br>
                                <tbody>
                                <c:forEach items="${contentList}" var="content">
                                    <tr>
                                        <td align="center" height="50px;"><font size="4" color="aqua"> ${content.title}</font> </td>
                                    </tr>
                                    <tr>
                                        <td align="center" height="50px;"><font size="3"> ${content.content}</font> </td>
                                    </tr>
                                </tbody>
                                </c:forEach>
                                </tbody>
                            </table>
                        </div>


                            <div class="form-group"  style="margin-top: 120px;">
                                <form  action="../about/addHelpFeedback.do" id="form1" method="post">
                                    <table class="table table-striped">
                                        <tbody>
                                        <tr>
                                            <font size="5" color="aqua">没有找到答案，直接反馈给我们</font>
                                        </tr>
                                        </br>
                                        </br>
                                        <tr class="tr_class">
                                            <td height="80px;" align="center"><input class="form-control" id="age" type="text" placeholder="标题" name="titile" style="width: 280px;height: 50px;"/></td>
                                            <td align="center"><textarea class="form-control" style="height: 50px;width: 580px;" placeholder="内容" name="content"></textarea></td>
                                            <td><input style="margin-left: 25px;height: 50px;width: 100px;" class="btn green" type="button" value="提交" onclick="tijiao()"></td>
                                        </tr>
                                        </tbody>
                                    </table>

                                </form>
                            </div>
                        </div>

                </div>


            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script type="text/javascript">
    function back(){
        $("#form3").submit();
    }

    function find(){
        $("#form2").submit();
    }

    function tijiao(){
        $("#form1").submit();
    }
</script>

<%@ include  file="../../common/footerBottom.jsp"%>