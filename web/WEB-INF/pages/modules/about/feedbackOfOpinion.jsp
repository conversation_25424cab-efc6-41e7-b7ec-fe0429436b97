<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

    <style>
        .hd{display: none}
        table tr td{ height:35px; overflow:hidden; }
        .btn{ border:1px solid #00a0e9; }
        .btn:hover{ background:#eaeaea;   }
    </style>

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>意见\问题反馈</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div>
                    <span type="button" class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="review();">我要反馈</span>
                </div>
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box ">
                    <div class="portlet-title">

                        <div class="tools">
                            <a href="javascript:;" class="collapse"> </a>
                            <a href="#portlet-config" data-toggle="modal" class="config"> </a>
                            <a href="javascript:;" class="reload"> </a>
                            <a href="javascript:;" class="remove"> </a>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="table-scrollable">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td width = "10"> 序号 </td>
                                    <td width = "15"> 标题 </td>
                                    <td width = "15"> 首次反馈时间 </td>
                                    <td width = "15"> 最新动态 </td>
                                    <td width = "10"> 回复次数 </td>
                                    <td width = "10"> 追问次数 </td>
                                    <td width = "10"> 状态 </td>
                                    <td width = "15"> 更多 </td>
                                </tr>
                                </thead>
                                <tbody>
                                <c:forEach items="${contentFeedbacks}" var="cf">
                                    <tr>
                                        <td> ${cf.id} </td>
                                        <td title = "${cf.titile}" class="ttl_">

                                        </td>
                                        <td> ${fn:substring(cf.createDate,0,19)} </td>
                                        <td> ${fn:substring(cf.updateDate,0,19)} </td>
                                        <td> ${cf.replyNum} </td>
                                        <td> ${cf.questionNum} </td>
                                        <td>
                                            <c:if test="${cf.state=='0'}"><span class="ty-color-orange">未解决</span></c:if>
                                            <c:if test="${cf.state=='1'}"><span class="ty-color-green">已解决</span></c:if>
                                        </td>
                                        <td>
                                            <span class="ty-color-blue" onclick="openWindow('../about/seeMore.do?id=${cf.id}','1450','800')">查看</span>
                                        </td>
                                    </tr>
                                </c:forEach>
                                <tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- END SAMPLE TABLE PORTLET-->
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    $(function(){
        $(".ttl_").each(function(){
            var ttl = $(this).attr("title") ;
            $(this).html(ttl.substr(0,10))
        })

    })
    function review(){
        openWindow('../about/iNeedFeedback.do','1300','800');
    }
</script>
<%@ include  file="../../common/footerBottom.jsp"%>
