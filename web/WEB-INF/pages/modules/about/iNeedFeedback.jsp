<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
    <meta charset="utf-8" />
    <title>通用框架</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <link href="../css/main/main.css?v=SVN_REVISION" rel="stylesheet" type="text/css" id="style_color"   />
    <link href="../assets/global/plugins/bootstrap/css/bootstrap.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../css/about.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

    <style>
        /*.juZuo{margin-left:180px}*/
        /*.juli{margin-top: 10px}*/
    </style>
    <%@ include  file="../../common/headerTop.jsp"%>
<%@ include  file="../../common/headerBottom.jsp"%>

<body style="background-color:#f0f8ff;">

<div class="page-container">
    <div class="page-content-wrapper">
        <div class="ty-header">
            <h3>新增反馈</h3>
            <p>
                <span class="navTxt"><i class="fa fa-home"></i>意见\问题反馈</span>
                <span class="nav_"> / </span>
                <span class="navTxt"><a href="javascript:;"><i class=""></i>新增反馈</a></span></span>
            </p>
        </div>
        <div class="ty-container">
            <div class="ty-mainData">
                <form class="form-horizontal" role="form" action="../about/addFeedback.do" id="form1" method="post">

                    <div class="form-group">
                        <label class="col-sm-3 control-label">标题</label>
                        <div class="col-sm-6">
                            <input type="text" name="titile" class="form-control" id="fourthname" value=""
                                   placeholder="字数控制在30字以内" maxlength="30">
                        </div>
                    </div>


                    <div class="form-group">
                        <label class="col-sm-3 control-label">内容</label>
                        <textarea  name="content" placeholder="字数控制在200字以内" style="width:470px;margin-left: 17px" rows="4" maxlength="200"></textarea>
                    </div>
                </form>
                <div class="modal-footer">
                    <span type="button" class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="tijiao()">提交</span>
                    <span type="button" class="ty-btn ty-btn-big ty-circle-5" onclick="window.close();">取消</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../assets/global/plugins/jquery.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script type="text/javascript">
    function tijiao(){
        $("#form1").submit();
    }
</script>
<%@ include  file="../../common/footerBottom.jsp"%>