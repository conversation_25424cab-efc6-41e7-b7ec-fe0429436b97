<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <style>
        .hd{ display: none;   }
    </style>

    <link href="../css/about.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="clearfix"> </div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>版本说明</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" id="paContainer">
            <!--放内容的地方-->
            <div class="ty-container">
                <br> 当前版本 ：版本 ${content.version} <br>${content.createDate}<br>
                <div class="contn">
                    <div class="Con">
                        <div class="vrContent">
                           ${content.content}
                        </div>
                        <div class="vr_history">
                            <h3>历史版本 </h3>
                            <div>
                                <div class="vr_history_item" onclick="versionShow($(this))">
                                    <form action="../about/versionDescription.do" method="post" id="zuixin">
                                    </form>
                                    <span class="vr_no" onclick="$('#zuixin').submit();">${content1.version}</span>
                                    <span class="vrIcon azury" onclick="$('#zuixin').submit();">最新</span>
                                </div>

                                <c:forEach items="${contentHistories}" var="ch">

                                    <div class="vr_history_item" onclick="versionShow($(this))">
                                        <span class="vr_no" onclick="checkContentHistories(${ch.id});">${ch.version}</span>
                                    </div>
                                </c:forEach>
                                <form action="../about/getContentHistoryById.do" method="post" id="formh">
                                    <input type="hidden" name="chId" id="chId"/>
                                </form>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>



        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script>
    function checkContentHistories(chId){
        $("#chId").val(chId);
        $("#formh").submit();
    }
</script>

<%@ include  file="../../common/footerBottom.jsp"%>
