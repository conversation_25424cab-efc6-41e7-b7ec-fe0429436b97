<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link href="../css/about/about.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>扫描下载</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="Con">
                    <div class="scanCon">
                        <h3>扫描后，可下载Wonderss手机端！</h3>
                        <div id='qrcode' class="qrCode"></div>
                        <a id='download' download='通用框架二维码.jpg'></a>
                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id='save'>下载通用框架二维码</button>
                        <div class="ty-color-blue">注：扫描时，建议使用手机自带的扫描器！</div>
                    </div>
                    <c:if test="${salaryTreasure}">
                    <div class="scanCon">
                        <h3>扫描后，可下载Wonderss手机端！（测试中，非测试人员请勿下载）</h3>
                        <div id='salary_qrcode' class="qrCode"></div>
                        <a id='salary_download' download='薪资宝二维码.jpg'></a>
                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id='salary_save'>下载薪资宝二维码</button>
                        <div class="ty-color-blue">注：扫描时，建议使用手机自带的扫描器！</div>
                    </div>
                    </c:if>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../assets/global/plugins/jquery.qrcode.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
$(function () {
    //wyu:这两个变量在部署的时候会被部署系统修改，请不要修改格式。
    var androidIcon = '${androidQRIcon}';
    var androidUrl = '${androidDownloadUrl}';
    //wyu:如果远程图片无法显示，可以将cross改为false，此时可以显示远程图片但无法用js保存二维码。
    var image = new Image();
    image.crossOrigin = 'anonymous';
    image.src = androidIcon;
    image.onload=function(){//wyu:图像可以跨域访问
        $('#qrcode').qrcode({
            text: androidUrl,
            height: 200,
            width: 200,
            src: androidIcon,
            cross: true
        });
        $("#save").css('visibility','visible');
    };
    image.onerror=function(){
        var image = new Image();
        image.src = androidIcon;
        image.onload=function(){//wyu:图像不可以跨域访问
            $('#qrcode').qrcode({
                text: androidUrl,
                height: 200,
                width: 200,
                src: androidIcon,
                cross: false
            });
            $("#save").css('visibility','hidden');
        }
        image.onerror=function(){//wyu:图像无法访问
            $('#qrcode').qrcode({
                text: androidUrl,
                height: 200,
                width: 200
            });
            $("#save").css('visibility','visible');
        };
    }

    //下载
    $("#save").click(function(){
        var canvas = $('#qrcode').find("canvas").get(0);
        try {//解决IE转base64时缓存不足，canvas转blob下载
            var blob = canvas.msToBlob();
            navigator.msSaveBlob(blob, '通用框架二维码.jpg');
        } catch (e) {//如果为其他浏览器，使用base64转码下载
            var url = canvas.toDataURL('image/jpeg');
            $("#download").attr('href', url).get(0).click();
        }
        return false;
    })
})
$(function () {
    //wyu:这两个变量在部署的时候会被部署系统修改，请不要修改格式。
    var androidSalaryIcon = '${androidSalaryIcon}';
    var androidSalaryUrl = '${androidSalaryUrl}';
    //wyu:如果远程图片无法显示，可以将cross改为false，此时可以显示远程图片但无法用js保存二维码。
    var image = new Image();
    image.crossOrigin = 'anonymous';
    image.src = androidSalaryIcon;
    image.onload=function(){//wyu:图像可以跨域访问
        $('#salary_qrcode').qrcode({
            text: androidSalaryUrl,
            height: 200,
            width: 200,
            src: androidSalaryIcon,
            cross: true
        });
        $("#salary_save").css('visibility','visible');
    };
    image.onerror=function(){
        var image = new Image();
        image.src = androidSalaryIcon;
        image.onload=function(){//wyu:图像不可以跨域访问
            $('#salary_qrcode').qrcode({
                text: androidSalaryUrl,
                height: 200,
                width: 200,
                src: androidSalaryIcon,
                cross: false
            });
            $("#salary_save").css('visibility','hidden');
        }
        image.onerror=function(){//wyu:图像无法访问
            $('#salary_qrcode').qrcode({
                text: androidSalaryUrl,
                height: 200,
                width: 200
            });
            $("#salary_save").css('visibility','visible');
        };
    }

    //下载
    $("#salary_save").click(function(){
        var canvas = $('#salary_qrcode').find("canvas").get(0);
        try {//解决IE转base64时缓存不足，canvas转blob下载
            var blob = canvas.msToBlob();
            navigator.msSaveBlob(blob, '薪资宝二维码.jpg');
        } catch (e) {//如果为其他浏览器，使用base64转码下载
            var url = canvas.toDataURL('image/jpeg');
            $("#salary_download").attr('href', url).get(0).click();
        }
        return false;
    })
})
</script>

<%@ include  file="../../common/footerBottom.jsp"%>
