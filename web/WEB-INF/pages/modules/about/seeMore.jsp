<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    .juZuo{margin-left:180px}
    .juli{margin-top: 10px}
</style>
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="">

<form action="../about/addFeedbackReply.do?cfId=${cfId}" method="post" id="form1">
 <input type="hidden" name="content" value="" id="content">
</form>

<div class="page-content-wrapper juzuo">
    <div class="page-content" id="mleft">
        <!--放内容的地方-->
        <div class="row" >
            <div style="width: 1100px" class="position">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box">
                    <div class="portlet-body ">
                        <form class="form-horizontal" role="form" action="" method="post">
                            <h2><font color="black">${contentFeedback.titile}</font> </h2>
                            <h5>${fn:substring(contentFeedback.createDate,0,19)}</h5>
                            <h4>${contentFeedback.content}</h4>
                            </br>
                            </br>
                            <table class="table table-hover" id="table1">
                                <tbody>
                                    <c:forEach items="${contentFeedbackReplies}" var="cfr">
                                    <tr>
                                        <th width="20%">
                                            <c:if test="${cfr.category==1}"><font color="#5d9cec">后台回复</font></c:if>
                                            <c:if test="${cfr.category==2}"><font color="#f58410">我的反馈</font></c:if>
                                        </th>
                                        <th width="50%"><font color="#48cfad"> ${cfr.content}</font></th>
                                        <th width="20%"><font color="#a9a9a9">${fn:substring(cfr.createDate,0,19)}</font></th>
                                    </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </form>

                    </div>
                    <div class="modal-footer">
                        <c:if test="${contentFeedback.state==0}">
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="$('#solutionForm').submit();">好的,解决了</span>
                            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="addElement();">未解决，继续发问</span>
                        </c:if>

                        <%--onclick="refreshOpener();"--%>
                    </div>

                    <form action="../about/solutionFeedback.do?id=${cfId}" method="post" id="solutionForm"></form>
                </div>
            </div>
        </div>
    </div>
</div>


<%@ include  file="../../common/footerScript.jsp"%>
<%--<!-- END THEME LAYOUT SCRIPTS -->--%>
<script type="text/javascript">
    function tijiao(){
        window.close();
    }

    function addElement(){
        var tr="<tr><th><font color='f58410'>我的反馈</font>"+"</th> <td> <input style=\"width: 500px;\" type=\"text\" id=\"aa\" name=\"check\"/></td> <td><span class=\"ty-btn ty-btn-blue ty-btn-big ty-circle-5\" onclick=\"tijiao();\">提交</span> <span class=\"ty-btn ty-btn-big ty-circle-5\" onclick=\"delElement();\">取消</span></td> </tr>";
        $("#table1").append(tr);
    }

    function tijiao(){
        var content=$("#aa").val();
        $("#content").val(content);
        $("#form1").submit();
    }

    function delElement(){
        $("#table1 tr:last").remove();
    }
</script>
<%@ include  file="../../common/footerBottom.jsp"%>
