<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/home/<USER>" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed3">
    <%-- creater: 张旭博，2020-12-08 13:59:31，修改考勤 查看请假 查看按钮弹框 --%>
    <div class="bonceContainer bounce-blue" id="leaveRecord">
        <div class="bonceHead">
            <span class="bounce_title">请假记录</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="detail"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed3.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- creator: 张旭博，2020-12-08 14:35:11，修改记录 - 查看工作记录  --%>
    <div class="bonceContainer bounce-blue" id="workRecordSee" style="width: 1100px" >
        <div class="bonceHead">
            <span class="name">工作记录</span>
            <a class="bounce_close cancelBtn" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-panel">
                <div class="item">
                    <div class="item_content">
                        <span class="currentDayWeek"></span>
                    </div>
                    <div class="item_content" style="width: 400px">
                        <span class="workReason"></span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_content leave">
                        当日有请假：
                        <div class="leaveList"></div>
                    </div>
                    <div class="item_content timeSpentTotalVisible">
                        当日已录入内容合计耗时：<span class="timeSpentTotal"></span>小时
                    </div>
                </div>
            </div>
            <div class="ty-panel routineWork">
                <div class="ty-panel-title"><span class="title">日常工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>结果</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel innerWork">
                <div class="ty-panel-title"><span class="title">计划内的工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>计划开始时间</td>
                            <td>计划耗时</td>
                            <td>结果</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short outterWork">
                <div class="ty-panel-title"><span class="title">当日所做的计划外工作</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table text-left">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>所属日期</td>
                            <td>开始时间</td>
                            <td>耗时</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="ty-panel short recentWork">
                <div class="ty-panel-title"><span class="title">近日的工作计划</span></div>
                <div class="ty-panel-content">
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td>工作内容</td>
                            <td>预计开始时间</td>
                            <td>预计耗时</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed2.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- creator: 张旭博，2020-12-08 13:55:31，提示  --%>
    <div class="bonceContainer bounce-blue" id="bounceFixed_tip">
        <div class="bonceHead">
            <span class="name">！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tips text-center"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue sureBtn">确定</button>
        </div>
    </div>
    <%-- creator: 张旭博，2020-12-08 13:55:31，工作记录的修改记录  --%>
    <div class="bonceContainer bounce-blue" id="workRecord" style="width: 700px">
        <div class="bonceHead">
            <span class="name">工作记录的修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">当前资料为第 <span class="changeNum"></span> 次修改后的结果。</div>
            <div class="ty-panel-content">
                <table class="kj-table text-left">
                    <thead>
                    <tr>
                        <td>资料状态</td>
                        <td>操作</td>
                        <td>创建人/修改人</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-green" id="timetableSetting" style="width:500px;">
        <div class="bonceHead">
            <span>作息时间查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="monthsTip">2018年8月份与9月份的作息计划如下，您可根据实际情况勾选修改。</p>
            <ul class="ty-secondTab monthsNav" id="monthsScan">
                <li>2018年11月</li>
                <li>2018年12月</li>
            </ul>
            <div class="clendars" id="clendarsScan" ></div>
            <div class="mask"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-2" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- creator: 张旭博，2020-12-08 13:55:31，录入当日的工作内容  --%>
    <div class="bonceContainer bounce-blue" id="workSee" style="width: 1100px">
        <div class="bonceHead">
            <span class="name">工作记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-panel">
                <div class="item">
                    <div class="item_content">
                        <span class="currentDayWeek"></span>
                    </div>
                    <div class="item_content" style="width: 400px">
                        <span class="workReason"></span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_content leave">
                        当日有请假：
                        <div class="leaveList"></div>
                    </div>
                    <div class="item_content timeSpentTotalVisible">
                        当日已录入内容合计耗时：<span class="timeSpentTotal"></span>小时
                    </div>
                </div>
                <div class="item lastOperateTimeVisible">
                    <div class="item_content">
                        最后的操作时间 <span class="lastOperateTime"></span>
                    </div>
                    <div class="item_content workRecordBtn">
                        <button class="ty-btn ty-btn-blue ty-circle-2" onclick="workRecordBtn()">修改记录</button>
                    </div>
                </div>
            </div>
            <div class="kj-hr"></div>
            <div class="reviewPart panel_review">加载中。。。</div>
            <div class="kj-hr panel_review"></div>
            <div class="workPart">
                <div class="ty-panel routineWork">
                    <div class="ty-panel-title"><span class="title">日常工作</span></div>
                    <div class="ty-panel-content">
                        <table class="kj-table text-left">
                            <thead>
                            <tr>
                                <td>工作内容</td>
                                <td>计划开始时间</td>
                                <td>结果</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="ty-panel innerWork">
                    <div class="ty-panel-title"><span class="title">计划内的工作</span></div>
                    <div class="ty-panel-content">
                        <table class="kj-table text-left">
                            <thead>
                            <tr>
                                <td>工作内容</td>
                                <td>计划开始时间</td>
                                <td>计划耗时</td>
                                <td>结果</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="ty-panel short outterWork">
                    <div class="ty-panel-title"><span class="title">当日所做的计划外工作</span></div>
                    <div class="ty-panel-content">
                        <table class="kj-table text-left">
                            <thead>
                            <tr>
                                <td>工作内容</td>
                                <td>所属日期</td>
                                <td>开始时间</td>
                                <td>耗时</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="ty-panel short recentWork">
                    <div class="ty-panel-title"><span class="title">近日的工作计划</span></div>
                    <div class="ty-panel-content">
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>工作内容</td>
                                <td>预计开始时间</td>
                                <td>预计耗时</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>工作记录</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <input id="pageNum" type="hidden" value="1" />
            <div class="ty-container" id="home">
                <div class="backBtn" style="display: none;">
                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="backToMainBtn" onclick="backToMain()">返回工作记录主页</button>
                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="back()">返回上一页</button>
                </div>
                <%--主页面--%>
                <div class="page page_main" page="main">
                    <%-- //  工作记录初始页 顶部操作--%>
                    <div class="workQuery">
                        <div class="ty-alert btn-input">
                            今天是 <span class="ty-color-blue nowDay"></span>
                        </div>
                        <div class="ty-alert btn-input">
                            <div class="queryItem">
                                <span class="ssTtl">查看其他月份的工作记录汇总</span>
                                <input class="kj-input kj-input-blue" id="otherMonthWork" placeholder="请选择月份"/>
                            </div>
                            <div class="btn-group text-right">
                                <div class="queryItem" style="width: 500px">
                                    查看工作年报
                                    <input class="kj-input kj-input-blue" id="yearReport" placeholder="请选择年份"/>
                                </div>
                                <div class="queryItem" style="width: 500px">
                                    查看工作月报
                                    <input class="kj-input kj-input-blue" id="monthReport" placeholder="请选择月份"/>
                                </div>
                            </div>
                        </div>
                        <div class="ty-title">工作记录汇总</div>
                        <div class="ty-alert">
                            <div style="display: inline-block">
                                <span class="workTip"></span>
                                <button class="ty-btn ty-btn-blue ty-circle-2" type="btn" data-name="seeTimeSetting" style="margin-left: 16px">查看</button>
                            </div>
                            <div class="btn-group text-right">
                                <div class="queryItem">
                                    <span class="ssTtl">按部门筛选</span>
                                    <select class="kj-select kj-select-blue departSelect"></select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <%-- //  考勤管理初始页--%>
                        <div class="ty-tblContainer workRecordTotal">
                            <table class="kj-table hover">
                                <thead>
                                <tr>
                                    <td style="width: 15%">姓名</td>
                                    <td style="width: 20%">手机号 </td>
                                    <td style="width: 15%">工作日之外的加班天数 </td>
                                    <td style="width: 15%">截至目前应交天数 </td>
                                    <td style="width: 15%">截至目前未交天数 </td>
                                    <td style="width: 20%">操作 </td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div id="ye_attendanceDays"></div>
                        </div>
                    </div>
                </div>
                <div class="page page_employee" page="employee" style="display: none">
                    <div>
                        <div class="ty-alert ty-alert-info" style="margin-top: 8px">
                            ※  点击某天，即可查看这天的工作记录。
                        </div>
                        <div class="ty-alert">
                            <span class="onedayWorkTitle"></span>
                        </div>
                        <div class="ty-tblContainer tbl_month"></div>
                    </div>
                </div>
                <div class="page" page="monthReportPre" style="display: none">
                    <div class="ty-title">工作月报</div>
                    <div class="ty-alert">
                        <div style="display: inline-block">
                            <span class="workTip"></span>
                            <button class="ty-btn ty-btn-blue ty-circle-2" type="btn" data-name="seeTimeSetting" style="margin-left: 16px">查看</button>
                        </div>
                        <div class="btn-group text-right">
                            <div class="queryItem">
                                <span class="ssTtl">按部门筛选</span>
                                <select class="kj-select kj-select-blue departSelect"></select>
                            </div>
                        </div>
                    </div>
                    <div class="ty-tblContainer workRecordTotal">
                        <table class="kj-table hover">
                            <thead>
                            <tr>
                                <td style="width: 15%">姓名</td>
                                <td style="width: 20%">手机号 </td>
                                <td style="width: 15%">工作日之外的加班天数 </td>
                                <td style="width: 15%">应交天数 </td>
                                <td style="width: 15%">未交天数 </td>
                                <td style="width: 20%">操作 </td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_attendanceReport"></div>
                    </div>
                </div>
                <div class="page" page="monthReport" style="display: none;">
                    <div class="ty-alert">
                        <span class="tips"></span>
                        <div class="btn-group text-right">
                            <span class="link-blue seeAllWorkBtn" onclick="seeMonthReportDetail()">查看本月全部工作</span>
                        </div>
                    </div>
                    <table class="kj-table">
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="monthReportDetail" style="display: none">
                    <div class="ty-alert">
                        <span class="tips"></span>
                    </div>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td rowspan="2">类别</td>
                            <td rowspan="2">创建时间</td>
                            <td colspan="2">计划</td>
                            <td rowspan="2">结果</td>
                            <td colspan="2">工作记录</td>
                        </tr>
                        <tr>
                            <td>内容</td>
                            <td>时间</td>
                            <td>时间</td>
                            <td>内容</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var setStatus = "${status}" ; // status 1-已设置 0-未设置
    // 获取开始日期
    var startUp = "${startUsingSystemTime}";
    // 获取时间误差
    var hostTime = <%= System.currentTimeMillis() %>;

    var diff = hostTime - new Date().getTime();
    if(diff < 30000){
        diff = 0;
    }
    var startMonth = startUp.substr(0,7) + '-02' ;
    var ssEnd = new Date(hostTime).format('yyyy-MM') + '-20';
</script>
<script src="../script/general/attendance/myCanlendar.js?v=SVN_REVISION"></script>
<script src="../script/home/<USER>"></script>
<script src="../script/general/work/overviewWork.js?v=SVN_REVISION"></script>
</body>
</html>
