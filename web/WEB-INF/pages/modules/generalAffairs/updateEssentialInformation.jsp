<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/user/handleEntry.css?v=SVN_REVISION">
<link rel="stylesheet" type="text/css" href="../css/general/recruit.css?v=SVN_REVISION" />
<style>
    .levelItem{margin-bottom:6px;}
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce">
    <%--  修改部门、职位 --%>
    <div class="bonceContainer bounce-green bounce-upBtnP">
        <div class="bonceHead">
            <span>编辑职位</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            修改名称：<input type="text" class="ty-inputText" id="positionName" />
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn ty-btn-green ty-btn-big" onclick="updatePosition()">确定</span>
        </div>
    </div>
        <div class="bonceContainer bounce-red bounce-delBtnP">
            <div class="bonceHead">
                <span>删除职位</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon text-center">
                <p>确定删除所选择的职位吗？</p>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
                <span class="ty-btn ty-btn-red ty-btn-big" onclick="delPosition()">确定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-green bounce-addBtnP">
            <div class="bonceHead">
                <span>新增职位</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon text-center">
                职位名称：<input type="text" class="ty-inputText" id="positionName_add" />
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
                <span class="ty-btn ty-btn-green ty-btn-big" onclick="addPosition()">确定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-green bounce-addDepartBtn">
            <div class="bonceHead">
                <span>新增同级部门</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon text-center">
                部门名称：<input type="text" class="ty-inputText" id="departName_add" />
                <span id="addType" class="hd"></span>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
                <span class="ty-btn ty-btn-green ty-btn-big" onclick="addDepartment()">确定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-green bounce-upBtn">
            <div class="bonceHead">
                <span>修改部门</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon text-center">
                部门名称：<input type="text" class="ty-inputText" id="departName" />
                <input id="departId" type="hidden" />
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
                <span class="ty-btn ty-btn-green ty-btn-big" onclick="updateDepartment()">确定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-red bounce-delDepartBtn">
            <div class="bonceHead">
                <span>删除部门</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon text-center">
                <p>确定删除所选中的部门吗？</p>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
                <span class="ty-btn ty-btn-red ty-btn-big" onclick="deleteDepartment()">确定</span>
            </div>
        </div>
    <div class="bonceContainer bounce-blue" id="alertMS">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="altMS" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>职工档案</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div>
                    <span class="goBack ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="goIndex()">返回</span>
                </div>
                    <div class="employeeEdit us_input">
                        <form action="../general/updateEssentialInformation.do" method="post" id="updateUser" name="updateUser" enctype="multipart/form-data">
                            <input type="hidden" name="userID" value="${user.userID}"/>
                            <input class="screenCondition" type="hidden" name="screen"/>
                            <div class="addStaffInfo">
                                <div class="sect">
                                    <div>
                                        <span class="small_ttl">基本信息</span>
                                    </div>
                                    <div class="con_part overflow">
                                        <div class="masterInfo ty-left">
                                            <ul class="overflow">
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">姓名：</span>
                                                    <c:choose>
                                                        <c:when test="${user.roleCode=='staff'}">
                                                            <input type="text" class="required" maxlength="20" id="userName" value="${user.userName}"
                                                                   placeholder="" name="userName">
                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" value="${user.userName}" disabled>
                                                            <input type="hidden" value="${user.userName}" name="userName" id="userName">
                                                        </c:otherwise>
                                                    </c:choose>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">性别：</span>
                                                    <select class="select_normal" name="gender" id="gender" autocomplete="off">
                                                        <option value="" ${user.gender == ""?"selected='selected'":""}></option>
                                                        <option value="1" ${user.gender == "1"?"selected='selected'":""}>男</option>
                                                        <option value="0" ${user.gender == "0"?"selected='selected'":""}>女</option>
                                                    </select>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">出生年月：</span>
                                                    <input type="text" id="birthday" value="${fn:substring(user.birthday, 0, 10)}" placeholder="" name="birthday1" style="width:153px;"/>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">最高学历：</span>
                                                    <select name="degree" class="select_normal" id="user_edu" autocomplete="off">
                                                        <option value="" ${user.degree == ""?"selected":""}></option>
                                                        <option value="1" ${user.degree == 1?"selected":""}>研究生</option>
                                                        <option value="2" ${user.degree == 2?"selected":""}>本科</option>
                                                        <option value="3" ${user.degree == 3?"selected":""}>大专</option>
                                                        <option value="4" ${user.degree == 4?"selected":""}>中专或高中</option>
                                                        <option value="5" ${user.degree == 5?"selected":""}>其它</option>
                                                    </select>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">婚姻状况：</span>
                                                    <select name="marry" id="marry" class="select_normal" autocomplete="off">
                                                        <option value="" ${user.marry == ""?"selected='selected'":""}></option>
                                                        <option value="1" ${user.marry == "1"?"selected='selected'":""}>未婚</option>
                                                        <option value="0" ${user.marry == "0"?"selected='selected'":""}>已婚</option>
                                                    </select>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">民族：</span>
                                                    <input type="text" value="${user.nation}" placeholder="请输入民族" name="nation">
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">政治面貌：</span>
                                                    <input type="text" value="${user.politicalStatus}" placeholder="" name="politicalStatus">
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">籍贯：</span> <input id="us_origin" type="text" name="nativePlace" value="${user.nativePlace}"/></li>
                                                <li class="col-xs-6 col-sm-8"><span class="us_field">特长：</span> <input name="interesting" type="text" value="${user.interesting}" style="width:370px;"/></li>
                                                <li class="col-xs-6 col-sm-8"><span class="us_field">爱好：</span> <input type="text" value="${user.speciality}" name="speciality" style="width:370px;"/></li>
                                            </ul>
                                            <ul class="overflow">
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">身份证号：</span>
                                                    <input id="idCard" type="text" value="${user.idCard}" placeholder="请输入身份证号" name="idCard" autocomplete="off" maxlength="18" />
                                                </li>
                                                <li class="col-xs-6 col-sm-8"><span class="us_field">家庭住址：</span>
                                                    <input type="text" value="${user.homeAddress}" placeholder="请输入家庭住址" name="homeAddress" maxlength="100" style="width: 300px;" autocomplete="off">
                                                </li>
                                                <li class="col-xs-6 col-sm-4 mobileInfo"><span class="us_field">联系电话：</span>
                                                    <c:choose>
                                                        <c:when test="${user.roleCode=='staff'}">
                                                            <input id="mobile" type="text" class="required number minlength:11" maxlength="11" value="${user.mobile}"
                                                                   placeholder="" name="mobile" autocomplete="off" maxlength="11"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text"  value="${user.mobile}" disabled/>
                                                            <input type="hidden"  value="${user.mobile}" name="mobile"/>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">E-mail：</span>
                                                    <input type="text"  value="${user.email}" placeholder="" name="email" autocomplete="off" maxlength="50">
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">QQ:</span>
                                                    <input type="text" value="${user.qq}" placeholder="" name="qq">
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="ty-left">
                                            <div class="viewerTx">
                                                <img id="preview" align="center" src="../${user.imgPath}" onerror="this.src='../assets/oralResource/user.png'" alt="用户头像" />
                                                <%--<img src="staff.png" title="员工照片">--%>
                                            </div>
                                            <div class="imgOption">
                                               <%-- <form action="${pageContext.request.contextPath}/general/updateUserImage.do" method="post" id="saveImage">--%>
                                                <input name="imgFile" id="imgFile" type="file" onchange="setImagePreview();" style="display:none;" />
                                                <input type="hidden" name="num" class="editFile" value="0"/>
                                                <span class="ty-btn ty-btn-green ty-circle-3" onclick="$('#imgFile').click()">上传照片</span>
                                                <span class="ty-btn ty-btn-green ty-circle-3" onclick="deleteUerImg()">删除照片</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="sect">
                                    <div>
                                        <span class="small_ttl">岗位信息</span>
                                    </div>
                                    <div class="con_part">
                                        <div class="masterInfo ">
                                            <ul>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">入职时间：</span>
                                                    <input type="text" id="date1" name="date1" value="${fn:substring(user.onDutyDate, 0, 10)}" placeholder="请选择入职时间" style="width:153px;"/>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">部门：</span>
                                                    <div class="department_panel" style="display:inline-block;   ">
                                                        <div>
                                                            <input type="hidden" id="oId" name="department" value="${user.department}"/>
                                                            <input type="text" val="0" name="departName" onclick="toggleSelect($(this))" readonly id="oName" placeholder="请选择部门" value="${user.departName}"/>
                                                        </div>
                                                        <div class="option_con hd">
                                                            <div class="chooseDepart">
                                                                <div class='levelItem' level="1">
                                                                    <div style="display:none; ">
                                                                        <span class="activeId" ></span>
                                                                    </div>
                                                                    <a class="depatlevel">一级部门</a>
                                                                    <c:forEach items="${departmentList}" var="dl">
                                                                        <span class="ty-btn departItem" id="${dl.id}" pid="${dl.pid}" onclick="getKidsDepart($(this))">${dl.name}</span>
                                                                    </c:forEach>
                                                                </div>
                                                            </div>
                                                            <div class="ty-btn" onclick="$('#oName').click();"><i class="fa fa-angle-up" style="margin-top: 5px;"></i></div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">职位：</span>
                                                    <div class="department_panel" style="display:inline-block;" >
                                                        <div>
                                                            <input type="hidden" id="zhiId" name="postID" value="${user.postID}"/>
                                                            <input type="text" val="0" name="postName" onclick="toggleSelect($(this))" readonly id="zhiName" value="${user.postName}" placeholder="请选择职位"/>
                                                            <%--<img src="../common/img/down.png" onclick="toggleSe($(this))">--%>
                                                        </div>
                                                        <div class="option_con hd">
                                                            <div class="choosePosition">
                                                                <div level="1">
                                                                    <c:forEach items="${postList}" var="pl">
                                                                        <span class="ty-btn" id="${pl.id}" onclick="getPosition($(this))">${pl.name}</span>
                                                                    </c:forEach>
                                                                </div>
                                                            </div>
                                                            <div class="ty-btn" onclick="$('#zhiName').click();"><i class="fa fa-angle-up" style="margin-top: 5px;"></i></div>
                                                        </div>
                                                    </div>

                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">是否为普通员工：</span>
                                                    <c:choose>
                                                        <c:when test="${user.roleCode=='staff'}">
                                                            <c:if test="${status==1}">
                                                                <c:if test="${user.ordinaryEmployees==1}">
                                                                    <input type="hidden" name="ordinaryEmployees" value="1">
                                                                    <select class="select_normal ordinaryEmployees"  disabled>
                                                                        <option value="1" selected>是</option>
                                                                        <option value="0">否</option>
                                                                    </select>
                                                                </c:if>
                                                                <c:if test="${user.ordinaryEmployees!=1}">
                                                                    <input type="hidden" name="ordinaryEmployees" value="0">
                                                                    <select class="select_normal ordinaryEmployees"   disabled>
                                                                        <option value="0" selected>否</option>
                                                                        <option value="1">是</option>
                                                                    </select>
                                                                </c:if>
                                                            </c:if>
                                                            <c:if test="${status==0}">
                                                                <c:if test="${user.ordinaryEmployees==1}">
                                                                    <select class="select_normal ordinaryEmployees" name="ordinaryEmployees">
                                                                        <option value="1">是</option>
                                                                        <option value="0">否</option>
                                                                    </select>
                                                                </c:if>
                                                                <c:if test="${user.ordinaryEmployees!=1}">
                                                                    <select class="select_normal ordinaryEmployees" name="ordinaryEmployees">
                                                                        <option value="0">否</option>
                                                                        <option value="1">是</option>
                                                                    </select>
                                                                </c:if>
                                                            </c:if>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:if test="${user.ordinaryEmployees!=1}">
                                                                <input type="text"  disabled  value="否"/>
                                                                <input type="hidden" name="ordinaryEmployees" value="0">
                                                            </c:if>
                                                            <c:if test="${user.ordinaryEmployees==1}">
                                                                <input type="text"  disabled  value="是"/>
                                                                <input type="hidden" name="ordinaryEmployees" value="1">
                                                            </c:if>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">所属高管：</span>
                                                    <input type="hidden" name="manager">
                                                    <input type="hidden" name="managerCode">
                                                    <c:choose>
                                                        <c:when test="${user.roleCode=='staff'}">
                                                            <select id="manage" class="select_normal manage1">
                                                                <%--<option value="${user.manager}-${user.managerCode}">${user.manageName}</option>--%>
                                                                <c:forEach items="${manages}" var="ma">
                                                                    <c:choose>
                                                                    <c:when test="${ma.userID.equals(user.manager)}" >
                                                                        <option selected value="${ma.userID}-${ma.managerCode}">${ma.manageName}</option>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <option value="${ma.userID}-${ma.managerCode}">${ma.manageName}</option>
                                                                    </c:otherwise>
                                                                    </c:choose>
                                                                </c:forEach>
                                                            </select>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <input value="--" id="manage" disabled style="background:#ededed;">
                                                        </c:otherwise>
                                                    </c:choose>
                                                </li>
                                                <li class="col-xs-6 col-sm-4"><span class="us_field">直接上级：</span>
                                                    <c:choose>
                                                        <c:when test="${user.roleCode=='staff'}">
                                                            <select id="leader" name="leader" class="select_normal">
                                                                <option value="${chaoguan.userID}">${chaoguan.userName}</option>
                                                                <c:forEach items="${userList}" var="u">
                                                                    <option value="${u.userID}">${u.userName}</option>
                                                                </c:forEach>
                                                            </select>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <select class="select_normal" disabled>
                                                                <option value="${user.leader}">${user.leaderName}
                                                                </option>
                                                            </select>
                                                            <input type="hidden" name="leader" value="${chaoguan.userID}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                </li>
                                                <div style="clear: both"></div>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="oprateBtns">
                                    <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="saveEmployeeInfo" onclick="saveEmployeeEdit()">保存</button>
                                </div>
                            </div>
                        </form>
                        <div class="seeStaffInfoStatus hd">
                            <div class="sect">
                                <div>
                                    <span class="small_ttl">基本信息</span>
                                </div>
                                <div class="con_part overflow">
                                    <div class="masterInfo ty-left">
                                        <ul class="overflow">
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">姓名：</span> <span>${user.userName}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">性别：</span>
                                                <span>
                                                    <c:choose>
                                                        <c:when test="${user.gender=='0'  }" >女</c:when>
                                                        <c:when test="${user.gender=='1'  }" >男</c:when>
                                                        <c:otherwise></c:otherwise>
                                                    </c:choose>
                                                </span>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">出生年月：</span>
                                                <span>
                                                    ${fn:substring(user.birthday, 0, 10)}
                                                </span>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">最高学历：</span> <span>
                                                <c:choose>
                                                    <c:when test="${user.degree=='1'}">研究生</c:when>
                                                    <c:when test="${user.degree=='2'}">本科</c:when>
                                                    <c:when test="${user.degree=='3'}">大专</c:when>
                                                    <c:when test="${user.degree=='4'}">中专或高中</c:when>
                                                    <c:when test="${user.degree=='5'}">其它</c:when>
                                                    <c:otherwise></c:otherwise>
                                                </c:choose>
                                            </span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">婚姻状况：</span>
                                                <span>
                                                    <c:choose>
                                                        <c:when test="${user.marry=='0' || user.marry=='已婚'}" >已婚</c:when>
                                                        <c:when test="${user.marry=='1' || user.marry=='未婚'}" >未婚</c:when>
                                                        <c:otherwise></c:otherwise>
                                                    </c:choose>
                                                </span>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">民族：</span> <span>${user.nation}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">政治面貌：</span> <span>${user.politicalStatus}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">籍贯：</span> <span>${user.nativePlace}</span></li>
                                            <li class="col-xs-6 col-sm-8"><span class="us_field">特长：</span> <span>${user.interesting}</span></li>
                                            <li class="col-xs-6 col-sm-8"><span class="us_field">爱好：</span> <span>${user.speciality}</span></li>
                                        </ul>
                                        <ul class="overflow">
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">身份证号：</span> <span>${user.idCard}</span></li>
                                            <li class="col-xs-6 col-sm-8"><span class="us_field">家庭住址：</span> <span>${user.homeAddress}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">联系电话：</span> <span>${user.mobile}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">E-mail：</span> <span>${user.email}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">QQ:</span> <span>${user.qq}</span></li>
                                        </ul>
                                    </div>
                                    <div class="ty-left">
                                        <div class="viewerTx">
                                            <img src="../${user.imgPath}" onerror="this.src='../assets/oralResource/user.png'" alt="用户头像" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="sect">
                                <div>
                                    <span class="small_ttl">岗位信息</span>
                                </div>
                                <div class="con_part">
                                    <div class="masterInfo">
                                        <ul class="overflow">
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">入职时间：</span> <span>${fn:substring(user.onDutyDate, 0, 10)}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">部门：</span> <span>${user.departName}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">职位：</span> <span>${user.postName}</span></li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">是否为普通员工：</span> <span><c:if test="${user.ordinaryEmployees==1}">是</c:if><c:if test="${user.ordinaryEmployees!=1}">否</c:if></span></li>

                                            <c:choose>
                                                <c:when test="${user.roleCode=='staff'}">
                                                    <li class="col-xs-6 col-sm-4"><span class="us_field">所属高管：</span> <span>${user.manageName}</span></li>
                                                </c:when>
                                                <c:otherwise>
                                                    <li class="col-xs-6 col-sm-4"><span class="us_field">所属高管：</span> <span>--</span></li>
                                                </c:otherwise>
                                            </c:choose>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">直接上级：</span> <span>${user.leaderName}</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="oprateBtns">
                                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="editEmployeeInfo" onclick="editStaffInfo(${user.userID})">编辑</span>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">教育背景</span>
                                <span class="ty-right ty-btn ty-btn-green ty-btn-green ty-btn-big ty-circle-5 eduAvailable" address="toAddPersonalEducation.do" userId="${user.userID}"
                                      onclick="openWindowSure('userId',$(this))">新增</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5 eduVailable hd">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="us_edu">
                                    <thead>
                                    <td width="15%">学校名称</td>
                                    <td width="15%">学习时间</td>
                                    <td width="15%">专业</td>
                                    <td width="15%">学历</td>
                                    <td width="20%">说明</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${personalEducations}" var="pe">
                                        <tr>
                                            <td>${pe.collegeName}</td>
                                            <td><%--${pe.beginTime}--%>${fn:substring(pe.beginTime, 0, 10)} ~ <%--${pe.endTime}--%>${fn:substring(pe.endTime, 0, 10)}</td>
                                            <td>${pe.major}</td>
                                            <td>${pe.degree}</td>
                                            <td>${pe.memo}</td>
                                            <td>
                                                <span class="ty-color-blue" address="toUpdatePersonalEducation.do" userId="${pe.id}" onclick="openWindowSure('id',$(this))">编辑</span>
                                                <span class="ty-color-red" onclick="shanchu('deletepe' , $(this) )">删除</span></td>
                                            <form action="../general/deletePersonalEducation.do?" method="post" id="deletepe">
                                                <input type="hidden" name="id" value="${pe.id}"/>
                                                <input type="hidden" name="userId" value="${user.userID}">
                                            </form>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">工作经历</span>
                                <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 occupAvailable"
                                      onclick="if(chargeRole('超管')){ $('#altMS').html('您没有此操作权限！'); bounce.show($('#alertMS'));return false;  }; openWindow('../general/toAddPersonnelOccupation.do?userId=${user.userID}','800','700')">新增</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5 occupVailable hd">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="us_work">
                                    <thead>
                                    <td width="10%">公司名称</td>
                                    <td width="15%">工作时间</td>
                                    <td width="10%">薪资水平</td>
                                    <td width="10%">在职职位</td>
                                    <td width="20%">工作职责</td>
                                    <td width="20%">未继续工作的原因</td>
                                    <td width="15%">操作</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${personnelOccupations}" var="po">
                                        <tr>
                                            <td>${po.corpName}</td>
                                            <td> ${fn:substring(po.beginTime, 0, 10)} ~ ${fn:substring(po.endTime, 0, 10)}</td>
                                            <td>${po.salary}</td>
                                            <td>${po.post}</td>
                                            <td>${po.operatingDuty}</td>
                                            <td>${po.memo}</td>
                                            <td>
                                                <span class="ty-color-blue"
                                                      onclick="if(chargeRole('超管')){ $('#altMS').html('您没有此操作权限！'); bounce.show($('#alertMS'));return false;  }; openWindow('../general/toUpdatePersonnelOccupation.do?id=${po.id}','800','700')">编辑</span>
                                                <span class="ty-color-red" onclick="shanchu('deletepo' , $(this) )">删除</span>
                                            </td>
                                            <form action="../general/deletePersonnelOccupation.do" method="post"  >
                                                <input type="hidden" name="id" value="${po.id}" />
                                                <input type="hidden" name="userId" value="${user.userID}" >
                                            </form>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">家庭成员</span>
                                <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5" address="toAddOrUpdateFolk.do" userId="${user.userID}" onclick="openWindowSure('userId',$(this))">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="us_family">
                                    <thead>
                                    <td width="15%">姓名</td>
                                    <td width="15%">性别</td>
                                    <td width="15%">年龄</td>
                                    <td width="15%">与本人关系</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${folkses}" var="pf">
                                        <tr>
                                            <td>${pf.name}</td>
                                            <td>${pf.gender}</td>
                                            <td>${pf.age}</td>
                                            <td>${pf.relation}</td>
                                            <td>
                                                <span class="ty-color-blue" address="toAddOrUpdateFolk.do" userId="${user.userID}" folkId="${pf.id}" onclick="openWindowSure('folkId',$(this))">编辑</span>
                                                <span class="ty-color-red" onclick="shanchu('deletepo' , $(this) )">删除</span>
                                            </td>
                                            <form action="../general/deletePersonnelFolks.do" method="post"  id="deletepf" >
                                                <input type="hidden" name="folkId" value="${pf.id}" />
                                                <input type="hidden" name="userId" value="${user.userID}" >
                                            </form>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">薪资情况</span>
                                <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5" address="toAddPersonnelSalaryLog.do" userId="${user.userID}" onclick="openWindowSure('userId',$(this))">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="us_salary">
                                    <thead>
                                    <td width="15%">修改时间</td>
                                    <td width="15%">修改原因</td>
                                    <td width="15%">薪资</td>
                                    <td width="15%">变更人</td>
                                    <td width="15%">备注</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${personnelSalaryLogs}" var="ps">
                                        <tr>
                                            <td>${ps.operateTime}</td>
                                            <td>${ps.admustResaon}</td>
                                            <td>${ps.salary}</td>
                                            <td>${ps.operatorName}</td>
                                            <td>${ps.memo}</td>
                                            <td>
                                                <span class="ty-color-blue" address="toUpdatePersonnelSalaryLog.do" userId="${ps.id}" onclick=" openWindowSure('id',$(this))">编辑</span>
                                                <span class="ty-color-red" onclick="shanchu('deleteps' , $(this) )">删除</span></td>
                                            <form action="../general/deletePersonnelSalaryLog.do" method="post" id="deleteps">
                                                <input type="hidden" name="id" value="${ps.id}"/>
                                                <input type="hidden" name="userId" value="${user.userID}">
                                            </form>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">奖惩情况</span>
                                <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5" address="toAddPersonalRewardPunishment.do" userId="${user.userID}" onclick="openWindowSure('userId',$(this))">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="us_prize">
                                    <thead>
                                    <td width="15%">修改时间</td>
                                    <td width="15%">内容</td>
                                    <td width="15%">变更人</td>
                                    <td width="15%">备注</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${personalRewardPunishments}" var="pr">
                                        <tr>
                                            <td>${pr.occurDate}</td>
                                            <td>${pr.content}</td>
                                            <td>${pr.operatorName}</td>
                                            <td>${pr.memo}</td>
                                            <td>
                                                <span class="ty-color-blue" address="toUpdatePersonalRewardPunishment.do" userId="${pr.id}" onclick="openWindowSure('id',$(this))">编辑</span>
                                                <span class="ty-color-red" onclick="shanchu('deletepr' , $(this) )">删除</span></td>
                                            <form action="../general/deletePersonalRewardPunishment.do" method="post" id="deletepr">
                                                <input type="hidden" name="id" value="${pr.id}"/>
                                                <input type="hidden" name="userId" value="${user.userID}">
                                            </form>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">评论情况</span>
                                <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 assessAvailable" address="toAddPersonalAssessment.do" userId="${user.userID}" onclick="openWindowSure('userId',$(this))">新增</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5 assessVailable hd">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="us_evalute">
                                    <thead>
                                    <td width="15%">修改时间</td>
                                    <td width="15%">内容</td>
                                    <td width="15%">变更人</td>
                                    <td width="15%">备注</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${personalAssessments}" var="pa">
                                        <tr>
                                            <td>${pa.assessDate}</td>
                                            <td>${pa.content}</td>
                                            <td>${pa.assessUserName}</td>
                                            <td>${pa.memo}</td>
                                            <td>
                                                <span class="ty-color-blue" address="toUpdatePersonalAssessment.do" userId="${pa.id}" onclick="openWindowSure('id',$(this))">编辑</span>
                                                <span class="ty-color-red" onclick="shanchu('deletepa' , $(this) )">删除</span></td>
                                            <form action="../general/deletePersonalAssessment.do" method="post" id="deletepa">
                                                <input type="hidden" name="id" value="${pa.id}"/>
                                                <input type="hidden" name="userId" value="${user.userID}">
                                            </form>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/jquery.validate.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/messages_zh.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/general/updateEssentialInforMation.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    var leadID = "${user.leader}";
    $("#leader").children("option").each(function () {
        var id = $(this).val();
        if (leadID == id) {
            $(this).attr("selected", "selected");
        }
    });

</script>
</body>

</html>
