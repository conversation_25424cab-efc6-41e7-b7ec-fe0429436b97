<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2016/8/17
  Time: 11:39
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
    <title>工作经历</title>
    <link href="../assets/global/plugins/bootstrap/css/bootstrap.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../assets/global/css/components.min.css?v=SVN_REVISION" rel="stylesheet" id="style_components" type="text/css" />

    <script src="../assets/global/plugins/jquery.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/js.cookie.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/jquery.blockui.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>

    <%--<script src="${pageContext.request.contextPath }/My97DatePicker/WdatePicker.js"></script>--%>
    <script src="../script/function.js?v=SVN_REVISION"></script>
    <%--日历插件--%>
    <script src="../assets/laydate/laydate.js?v=SVN_REVISION" type="text/javascript"></script>
</head>
<body>

<form id="personOccupation" action="../general/addOrUpdatePersonnelOccupation.do" method="post">
    <input type="hidden" name="id" value="${personnelOccupation.id}"/>
    <input type="hidden" name="userId" value="${userId}"/>
    <div class="row" >
        <div style="width: 1000px" class="position">
            <!-- BEGIN SAMPLE TABLE PORTLET-->

            <%--<div class="portlet box green">--%>
            <div class="portlet">
                <div class="portlet-title">
                    <div class="caption" style="margin-left: 350px">
                        <h2>编辑工作经历</h2>
                    </div>
                </div>
                <div class="portlet-body">
                    </br>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">公司名称：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" id="firstname" value="${personnelOccupation.corpName}"
                                       placeholder="" name="corpName">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">薪资水平：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" value="${personnelOccupation.salary}"
                                       placeholder="" name="salary"  >
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">在职职位：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" id="" value="${personnelOccupation.post}"
                                       placeholder="" name="post" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">工作时间：从</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control laydate-icon"  value="${fn:substring(personnelOccupation.beginTime, 0, 10)}"
                                       placeholder="" name="beginTime1" id="date1" readonly="" style="height:35px;" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">工作时间：到</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control laydate-icon" value="${fn:substring(personnelOccupation.endTime, 0, 10)}"
                                       placeholder="" name="endTime1" id="date2" readonly="" style="height:35px;">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">离职原因：</label>
                            <div class="col-sm-6">
                                <textarea class="form-control" style="height: 100px" name="memo">${personnelOccupation.memo}</textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">工作职责：</label>
                            <div class="col-sm-6">
                                <textarea class="form-control" style="height: 100px"  name="operatingDuty">${personnelOccupation.operatingDuty}</textarea>
                            </div>
                        </div>

                    </div>
                </div>
                <div align="center">
                    <input type="submit" class="btn green" style="width:125px"
                           value="保存"/>
                    <input type="button" class="btn green" style="width:125px"
                           value="取消" onclick="refreshOpener();"/>
                </div>
            </div>
        </div>
    </div>
</form>
<script src="../script/jquery.validate.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/messages_zh.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    //creator:lyt date:2017/12/5 验证薪资字段
    $("#personOccupation").validate({
        rules:{
            salary:{
                number:true
            }
        }
    });
    laydate({ elem: '#date1' }) ;
    laydate({ elem: '#date2' }) ;
</script>
</body>
</html>
