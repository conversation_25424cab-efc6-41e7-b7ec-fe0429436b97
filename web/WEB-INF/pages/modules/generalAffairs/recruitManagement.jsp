<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/general/recruit.css?v=SVN_REVISION" />
<link rel="stylesheet" type="text/css" href="../css/event/icon/iconfont.css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="addSynthesize" style="width:400px; ">
        <div class="bonceHead">
            <span>增加综合项</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <input class="kj-input autoFill" max="30" name="" style="width: 100% "/>
                <div class="textMax text-right">0/30</div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="deadlineResume">
        <div class="bonceHead">
            <span>截止日期履历</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">
                本次招聘简历提交的截止日期：<span class="deadline"></span>
            </div>
            <div class="ty-alert">
                之前设置过的截止日期如下：
            </div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>截止日期</td>
                    <td>创建/修改的操作</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- 新增岗位 --%>
    <div class="bonceContainer bounce-green" id="addPost">
        <div class="bonceHead">
            <span>新增岗位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bonceConMain">
                <div class="item">
                    <div class="item-title">
                        <b class="ty-color-red">*</b> 岗位名称
                    </div>
                    <div class="item-content">
                        <input class="kj-input autoFill" max="10" name="name" require />
                        <div class="textMax text-right">0/10</div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">
                        岗位职责
                        <div class="ty-right">
                            <span class="link-blue" onclick="addRow($(this), 'duty')">增加一行</span>
                        </div>
                    </div>
                    <div class="item-content dutyPanel"></div>
                </div>
                <div class="item">
                    <div class="item-title">
                        岗位要求
                        <div class="ty-right">
                            <span class="link-blue" onclick="addRow($(this), 'requirement')">增加一行</span>
                        </div>
                    </div>
                    <div class="item-content requirementPanel"></div>
                </div>
                <div class="item">
                    <div class="item-title">
                        综合
                        <div class="ty-right">
                            <span class="link-blue" onclick="addSynthesizeBtn($(this))">增加</span>
                        </div>
                    </div>
                    <div class="custom_select">
                        <div class="item-content synthesisPanel">
                            <div class="input_choose" name="synthesis">
                                <div class="input_show">
                                    <input type="text" class="search">
                                </div>
                            </div>
                        </div>
                        <div class="field" style="margin: 0">
                            <div class="item-title"></div>
                            <div class="input_choose_list" style="position: absolute;left: 54px;z-index: 2;"></div>
                        </div>
                    </div>
                    <small class="ty-color-blue">注：此项可多选，描述不合适可自行新增。</small>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="sureAddPost()">确定</button>
        </div>
    </div>
    <%--分享到微信后的效果 --%>
    <div class="bonceContainer bounce-blue" id="shareWechatEffect" style="width: 1200px">
        <div class="bonceHead">
            <span>分享到微信后的效果</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <img src="../../../../css/general/wxshare_example.png" alt="">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="QR_code" style="width: 600px">
        <div class="bonceHead">
            <span>二维码</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="qrCode"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="functionIntroduce" style="width: 600px">
        <div class="bonceHead">
            <span>关于人脉招聘的功能说明</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="functionCon">
                <div>“人脉招聘”菜单下，所创建的是可在微信中转发的招聘启事，以便利用人脉招聘。</div>
                <div class="ty-hr"></div>
                <div>总体流程：</div>
                <div>1（可在微信中转发的）招聘启事的创建</div>
                <div>1.1  点击“创建招聘启事”，在所见页上按页面要求，进行编辑岗位需求等操作</div>
                <div>1.2  编辑完后，可点击“预览”以检查内容并确认视觉效果</div>
                <div>1.3  内容敲定后，勾选页面右上角“…设置完毕”并点击“确定”，招聘启事即创建完毕</div>
                <div>2  在微信中转发某招聘启事</div>
                <div>2.1  点击系统手机端-工作-招聘管理，所见为进行中的招聘启事</div>
                <div>2.2  点击要分享到微信的招聘启事，之后点击“分享”</div>
                <div>2.3  选择转发对象</div>
                <div>3  打开链接的人如有意应聘，可填写并提交简历</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--招聘启事管理 --%>
    <div class="bonceContainer bounce-blue" id="recruitManage">
        <div class="bonceHead">
            <span>招聘启事管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="manageCon">
                <div class="voidRecruit" style="display: none">
                    <div class="item">
                        <span>
                            本次招聘简历提交的截止日期已过！<br>
                            该截止日期：<span class="deadline"></span>
                        </span>
                        <div class="ty-right">
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="deadlineResume($(this))">截止日期履历</button>
                        </div>
                    </div>
                    <div class="item">
                        <div>使用Wonderss的手机端，即可将所创建的招聘启事分享到微信！</div>
                        <div><small class="ty-color-blue">注：Wonderss手机端里，该功能在“工作”下的“招聘管理”中</small></div>
                    </div>
                    <div class="item">
                        <div class="text-right"><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="shareWechatEffect()">分享到微信后的效果</button></div>
                    </div>
                    <div>
                        <div class="ty-radio">
                            <input type="radio" id="radio0" name="voidnew">
                            <label for="radio0"></label>以本次为基础，创建一个新招聘启事
                        </div>
                    </div>
                </div>
                <div class="normalRecruit">
                    <div class="item">
                        <div>使用Wonderss的手机端，即可将所创建的招聘启事分享到微信！</div>
                        <div><small class="ty-color-blue">注：Wonderss手机端里，该功能在“工作”下的“招聘管理”中</small></div>
                    </div>
                    <div class="item">
                        <div class="text-right"><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="shareWechatEffect()">分享到微信后的效果</button></div>
                    </div>
                    <div class="item">
                        <div>简历提交后，多长时间内可修改？请设置</div>
                        <div>
                            <select class="kj-select" name="" id="changeResumeIntervals" style="width: 100%">
                                <option value="">请选择</option>
                                <option value="1">1分钟</option>
                                <option value="10">10分钟</option>
                                <option value="20">20分钟</option>
                                <option value="30">30分钟</option>
                            </select>
                        </div>
                    </div>
                    <div class="item">
                        <div>
                            <div class="ty-radio">
                                <input type="radio" name="recruitSetting" id="radio1" class="radioToggle" for="changeStopTime" value="1">
                                <label for="radio1"></label>修改简历提交的截止日期
                            </div>
                            <div class="ty-right"><span class="hd deadline"></span><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="deadlineResume($(this))">截止日期履历</button></div>
                        </div>

                    </div>
                    <div class="item toggleItem" id="changeStopTime"><input class="kj-input" type="text" id="changeResumeStopTime" style="width: 100%" placeholder="请选择"></div>
                    <div class="item">
                        <div>
                            <div class="ty-radio">
                                <input class="radioToggle" name="recruitSetting" type="radio" id="radio2" for="needDisable" value="2">
                                <label for="radio2"></label>已发出去的招聘启事有问题，需立即作废
                            </div>
                        </div>
                        <div class="toggleItem" id="needDisable" style="padding-left: 20px">
                            <div class="ty-radio">
                                <input type="radio" name="endnew" id="radio3" value="1">
                                <label for="radio3"></label>本次招聘终止
                            </div>
                            <br>
                            <div class="ty-radio">
                                <input type="radio" name="endnew" id="radio4" value="2">
                                <label for="radio4"></label>以本次为基础，创建一个新招聘启事
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureRecruitManage()">确定</span>
        </div>
    </div>
    <%------------------启事各项目编辑弹窗--------------------%>
    <%--编辑项目内容 - 分享至微信链接封面的图片 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_image">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">
                        封面图片
                        <div class="ty-right"><span class="link-blue" onclick="changePic()">更换</span></div>
                    </div>
                    <div class="item-content">
                        <div class="upload_avatar">
                            <div class="imgUpload hd"></div>
                            <div class="imgShow">
                                <img src="../../../../assets/images/recruit_default.jpg" alt="" path="">
                            </div>
                        </div>
                        <small class="ty-color-blue">注：微信发布的链接封面上将展示此图片。如需要，可更换。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn" onclick="sureEditNotice()">确定</button>
        </div>
    </div>
    <%--编辑项目内容 - 公司介绍 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_introduce">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">公司介绍<div class="textMax ty-right">0/200</div></div>
                    <div class="item-content">
                        <textarea class="kj-textarea autoFill" max="200" name="introduce" cols="30" rows="2" style="width: 100%" placeholder="请录入"></textarea>
                        <small class="ty-color-blue">注：此项内容如不需要在招聘启事上显示，则无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditNotice()">确定</span>
        </div>
    </div>
    <%--编辑项目内容 - 作息时间 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_workRest">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">作息时间</div>
                    <div class="item-content">
                        <div>
                            <div class="ty-radio">
                                <input type="radio" id="radio_workRest3" name="workRestSet" value="true">
                                <label for="radio_workRest3"></label> 此项统一设置
                            </div>
                            <br>
                            <div class="ty-radio">
                                <input type="radio" id="radio_workRest4" name="workRestSet" value="false">
                                <label for="radio_workRest4"></label> 此项不统一设置，每个岗位都需要设置
                            </div>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">上班时间</div>
                    <div class="item-content">
                        <select class="kj-select halfTime" name="beginTime" style="width: 100%"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">下班时间</div>
                    <div class="item-content">
                        <select class="kj-select halfTime" name="endTime" style="width: 100%"></select>
                        <small class="ty-color-blue">注：不需在招聘启事上显示的内容，无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditNotice()">确定</span>
        </div>
    </div>
    <%--编辑项目内容 - 工作地点 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_address">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">工作地点</div>
                    <div class="item-content">
                        <div>
                            <div class="ty-radio">
                                <input type="radio" id="radio_address3" name="addressSet" value="true">
                                <label for="radio_address3"></label> 此项统一设置
                            </div>
                            <br>
                            <div class="ty-radio">
                                <input type="radio" id="radio_address4" name="addressSet" value="false">
                                <label for="radio_address4"></label> 此项不统一设置，每个岗位都需要设置
                            </div>
                        </div>

                    </div>
                </div>
                <div class="item">
                    <div class="item-content">
                        <input type="text" class="kj-input" name="address" style="width: 100%">
                        <small class="ty-color-blue">注：不需在招聘启事上显示的内容，无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditNotice()">确定</button>
        </div>
    </div>
    <%--编辑项目内容 - 补充说明 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_description">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">补充说明<div class="textMax ty-right">0/200</div></div>
                    <div class="item-content">
                        <textarea class="kj-textarea autoFill" max="200" name="description" cols="30" rows="2" style="width: 100%" placeholder="请录入"></textarea>
                        <small class="ty-color-blue">注：此项内容如不需要在招聘启事上显示，则无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditNotice()">确定</span>
        </div>
    </div>
    <%--编辑项目内容 - 已提交简历的修改 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_resumeChanges">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">简历提交后，多长时间内可修改？请设置</div>
                    <div class="item-content">
                        <select name="resumeChanges" style="width: 100%">
                            <option value="">请选择</option>
                            <option value="1">1分钟</option>
                            <option value="10">10分钟</option>
                            <option value="20">20分钟</option>
                            <option value="30">30分钟</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditNotice()">确定</span>
        </div>
    </div>
    <%--编辑项目内容 - 简历提交截止日期 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_deadline">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title"><span class="ty-color-red">*</span>简历提交的截止日期</div>
                    <div class="item-content">
                        <input type="text" id="notice_deadline" name="deadline" style="width: 100%" placeholder="请选择">
                        <small class="ty-color-blue">注：此项必须选择！</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditNotice()">确定</span>
        </div>
    </div>
    <%--编辑项目内容 - 本次招聘的联系人及联系方式 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_contact">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                本次招聘的联系人及联系方式
                <div class="item">
                    <div class="item-title">联系人<div class="textMax ty-right">0/10</div></div>
                    <div class="item-content">
                        <input class="autoFill" max="10" type="text" name="contacter" style="width: 100%">
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">电话</div>
                    <div class="item-content">
                        <input type="text" name="telephone" style="width: 100%" onkeyup="clearNum(this)">
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">邮箱</div>
                    <div class="item-content">
                        <input type="text" name="email" style="width: 100%">
                        <small class="ty-color-blue">注：此项内容如不需要在招聘启事上显示，则无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditNotice()">确定</span>
        </div>
    </div>
    <%--编辑项目内容 - 自定义 --%>
    <div class="bonceContainer bounce-blue" id="editNotice_custom">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">
                        <span class="itemName"></span>
                        <div class="textMax ty-right">0/50</div>
                    </div>
                    <div class="item-content">
                        <textarea class="kj-textarea autoFill" name="content" max="50" cols="30" rows="1" style="width: 100%"></textarea>
                        <small class="ty-color-blue">注：不需在招聘启事上显示的内容，无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditNotice()">确定</span>
        </div>
    </div>
    <%--增加一行 --%>
    <div class="bonceContainer bounce-blue" id="addOneNoticeProject">
        <div class="bonceHead">
            <span class="bounce_title">增加一行</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title"><span class="ty-color-red">*</span>项目名称<div class="textMax ty-right">0/10</div></div>
                    <div class="item-content">
                        <input class="ty-inputText autoFill" max="10" type="text" name="itemName" style="width: 100%" placeholder="请录入要增加这行的“项目名称”">
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">项目内容<div class="textMax ty-right">0/50</div></div>
                    <div class="item-content">
                        <input class="ty-inputText autoFill" max="50" type="text" name="content" style="width: 100%" placeholder="请录入要增加这行的“项目内容”">
                    </div>
                </div>
                <div class="item">
                    <div>对于增加这行，是否需要出现在以后的招聘启事模板中？</div>
                    <small class="ty-color-blue">注：招聘启事模板上哪条内容如不需要，可设置为“不显示”！</small>
                </div>
                <div class="item">
                    <div>
                        <div class="ty-radio">
                            <input type="radio" id="radio_addNoticeNeed" name="unitive" value="true">
                            <label for="radio_addNoticeNeed"></label> 需要
                        </div>
                        <div class="ty-radio" style="margin-left: 64px">
                            <input type="radio" id="radio_addNoticeNeed2" name="unitive" value="false">
                            <label for="radio_addNoticeNeed2"></label> 不需要
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureAddOneNoticeProject()">确定</span>
        </div>
    </div>
    <%------------------岗位各项目编辑弹窗--------------------%>
    <%--编辑项目内容 - 岗位名称 --%>
    <div class="bonceContainer bounce-blue" id="editPost_postName">
        <div class="bonceHead">
            <span>编辑职位需求</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title"><span class="ty-color-red">*</span>岗位名称 <div class="ty-right"><span class="link-blue" onclick="addNewPosition()">新增岗位</span></div></div>
                    <div class="item-content">
                        <select class="kj-select" id="postName" name="postName" style="width: 100%"></select>
                        <small class="ty-color-blue">注：此项必须选择！</small>
                    </div>
                </div>
                <div class="postInfo" style="display: none">
                    <div class="item">
                        <div class="item-title">岗位职责</div>
                        <div class="item-content">
                            <div class="postSee_duty"></div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item-title">岗位要求</div>
                        <div class="item-content">
                            <div class="postSee_requirement"></div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item-title">综合</div>
                        <div class="item-content">
                            <div class="postSee_synthesis"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditPost()">确定</button>
        </div>
    </div>
    <%--编辑项目内容 - 招聘人数 --%>
    <div class="bonceContainer bounce-blue" id="editPost_recruitingNumbers">
        <div class="bonceHead">
            <span>编辑职位需求</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">
                        <span class="ty-color-red">*</span>招聘人数
                        <div class="textMax ty-right">0/10</div>
                    </div>
                    <div class="item-content">
                        <input class="kj-input autoFill" max="10" name="recruitingNumbers" require  style="width: 100%" onkeyup="clearNum(this)"/>
                        <small class="ty-color-blue">注：此项必须录入！</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditPost()">确定</button>
        </div>
    </div>
    <%--编辑项目内容 - 其他条件与要求 --%>
    <div class="bonceContainer bounce-blue" id="editPost_otherAsk">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div>其他条件与要求</div>
                    <small class="ty-color-blue">注：以下各项中，不需在招聘启事上显示的内容，无需录入。</small>
                </div>
                <div class="item">
                    <div class="item-title">年龄</div>
                    <div class="item-content">
                        <input class="kj-input" name="age" onkeyup="clearNum(this)"/> 周岁
                        <select name="ageThreshold" style="width: 174px">
                            <option value="">请选择</option>
                            <option value="2">以上</option>
                            <option value="1">以下</option>
                            <option value="0">不限</option>
                        </select>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">性别</div>
                    <div class="item-content">
                        <select name="gender" style="width: 100%">
                            <option value="">请选择</option>
                            <option value="2">男</option>
                            <option value="1">女</option>
                            <option value="0">不限</option>
                        </select>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">学历</div>
                    <div class="item-content">
                        <select name="education" style="width: 200px">
                            <option value="">请选择</option>
                            <option value="1">硕士</option>
                            <option value="2">本科</option>
                            <option value="3">大专</option>
                            <option value="4">高中或中专</option>
                        </select>
                        <select name="educationThreshold" style="width: 195px">
                            <option value="">请选择</option>
                            <option value="2">或以上</option>
                            <option value="1">或以下</option>
                            <option value="0">不限</option>
                        </select>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">户籍</div>
                    <div class="item-content">
                        <select name="residence" style="width: 100%">
                            <option value="">请选择</option>
                            <option value="0">不限</option>
                            <option value="1">本地户籍</option>
                            <option value="2">非本地户籍</option>
                        </select>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">工作经验<div class="textMax ty-right">0/100</div></div>
                    <div class="item-content">
                        <textarea class="kj-textarea autoFill" name="experiences" cols="30" rows="1" max="100" style="width: 100%"></textarea>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">性格<div class="textMax ty-right">0/100</div></div>
                    <div class="item-content">
                        <textarea class="kj-textarea autoFill" name="characters" cols="30" rows="1" max="100" style="width: 100%"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditPost()">确定</button>
        </div>
    </div>
    <%--编辑项目内容 - 作息时间 --%>
    <div class="bonceContainer bounce-blue" id="editPost_workRest">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">作息时间</div>
                    <div class="item-content">
                        <div>
                            <div class="ty-radio">
                                <input type="radio" id="radio_workRest1" name="workRestSet" value="true">
                                <label for="radio_workRest1"></label> 此项统一设置
                            </div>
                            <br>
                            <div class="ty-radio">
                                <input type="radio" id="radio_workRest2" name="workRestSet" value="false">
                                <label for="radio_workRest2"></label> 此项不统一设置，每个岗位都需要设置
                            </div>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">上班时间</div>
                    <div class="item-content">
                        <select class="kj-select halfTime" name="beginTime" style="width: 100%"></select>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">下班时间</div>
                    <div class="item-content">
                        <select class="kj-select halfTime" name="endTime" style="width: 100%"></select>
                        <small class="ty-color-blue">注：不需在招聘启事上显示的内容，无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditPost()">确定</button>
        </div>
    </div>
    <%--编辑项目内容 - 工作地点 --%>
    <div class="bonceContainer bounce-blue" id="editPost_address">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">工作地点</div>
                    <div class="item-content">
                        <div>
                            <div class="ty-radio">
                                <input type="radio" id="radio_address1" name="addressSet" value="true">
                                <label for="radio_address1"></label> 此项统一设置
                            </div>
                            <br>
                            <div class="ty-radio">
                                <input type="radio" id="radio_address2" name="addressSet" value="false">
                                <label for="radio_address2"></label> 此项不统一设置，每个岗位都需要设置
                            </div>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-content">
                        <input type="text" class="kj-input" name="address" style="width: 100%">
                        <small class="ty-color-blue">注：不需在招聘启事上显示的内容，无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditPost()">确定</button>
        </div>
    </div>
    <%--编辑项目内容 - 岗位名称 --%>
    <div class="bonceContainer bounce-blue" id="editPost_salary">
        <div class="bonceHead">
            <span>编辑职位需求</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">薪资待遇 <div class="textMax ty-right">0/30</div></div>
                    <div class="item-content">
                        <input type="text" class="kj-input autoFill" max="30" name="salary" style="width: 100%">
                        <small class="ty-color-blue">注：不需在招聘启事上显示的内容，无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditPost()">确定</button>
        </div>
    </div>
    <%--编辑项目内容 - 自定义 --%>
    <div class="bonceContainer bounce-blue" id="editPost_custom">
        <div class="bonceHead">
            <span>编辑项目内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title">
                        <span class="itemName"></span>
                        <div class="textMax ty-right">0/50</div>
                    </div>
                    <div class="item-content">
                        <textarea class="kj-textarea autoFill" name="content" max="50" cols="30" rows="1" style="width: 100%"></textarea>
                        <small class="ty-color-blue">注：不需在招聘启事上显示的内容，无需录入。</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureEditPost()">确定</span>
        </div>
    </div>
    <%--增加一行 --%>
    <div class="bonceContainer bounce-blue" id="addOnePostProject">
        <div class="bonceHead">
            <span>增加一行</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addCon">
                <div class="item">
                    <div class="item-title"><span class="ty-color-red">*</span>项目名称<div class="textMax ty-right">0/10</div></div>
                    <div class="item-content">
                        <input class="ty-inputText autoFill" max="10" type="text" name="itemName" style="width: 100%" placeholder="请录入要增加这行的“项目名称”">
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">项目内容<div class="textMax ty-right">0/50</div></div>
                    <div class="item-content">
                        <input class="ty-inputText autoFill" max="50" type="text" name="content" style="width: 100%" placeholder="请录入要增加这行的“项目内容”">
                    </div>
                </div>
                <div class="item">
                    <div>对于增加这行，是否需要出现在以后的招聘启事模板中？</div>
                    <small class="ty-color-blue">注：招聘启事模板上哪条内容如不需要，可设置为“不显示”！</small>
                </div>
                <div class="item">
                    <div>
                        <div class="ty-radio">
                            <input type="radio" id="radio_addPostNeed" name="unitive" value="true">
                            <label for="radio_addPostNeed"></label> 需要
                        </div>
                        <div class="ty-radio" style="margin-left: 64px">
                            <input type="radio" id="radio_addPostNeed2" name="unitive" value="false">
                            <label for="radio_addPostNeed2"></label> 不需要
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureAddOnePostProject()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>招聘管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container" id="home">
                <div class="ty-page-header" style="display: none">
                    <div class="page-header__left" onclick="back()">
                        <i class="iconfont icon-houtui icon-back"></i>
                        <span>返回</span>
                    </div>
                    <div class="page-header__content" onclick="backToMain()">
                        <i class="iconfont icon-zhuye icon-back"></i>
                        <span>主页</span>
                    </div>
                </div>
                <div class="page mainCompany" page="main">
                    <div class="page-content-avatar">
                        <div class="panel-box">
                            <div class="ty-alert">
                                本菜单下，所创建的是可在微信中转发的招聘启事，以便公司利用微信的人脉招聘。
                                <div class="text-right btn-group">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="shareWechatEffect()">效果查看</button>
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.show($('#functionIntroduce'))">功能说明</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                以下为进行中的招聘启事
                                <div class="text-right btn-group">
                                    <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="invalidRecruitNoticeBtn()">已失效的招聘启事</button>
                                    <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="createRecruitNotice()">创建招聘启事</button>
                                </div>
                            </div>
                            <table class="kj-table table_recruitNoticeManage">
                                <thead>
                                <tr>
                                    <td>创建</td>
                                    <td>失效日期</td>
                                    <td>招聘职位</td>
                                    <td>已收简历</td>
                                    <td>通知面试</td>
                                    <td>已面试</td>
                                    <td>已入职</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <%-- 统计（已收简历、通知面试、已面试、已入职）--%>
                <div class="page" page="recruitCount">
                    <div class="ty-alert">
                        <div class="tip"></div>
                    </div>
                    <table class="kj-table table_recruitCountManage">
                        <thead>
                        <tr>
                            <td>姓名</td>
                            <td>性别</td>
                            <td>年龄</td>
                            <td>应聘职位</td>
                            <td>简历提交时间</td>
                            <td>联系电话</td>
                            <td>通知面试的信息</td>
                            <td>参加面试的信息</td>
                            <td>最终决定</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <%-- 创建招聘启事 --%>
                <div class="page" page="createRecruitNotice">
                    <div class="page-content-avatar">
                        <div class="topRightBtn">
                            <div class="ty-radio">
                                <input type="radio" id="recruitSetDone" name="recruitSetDone">
                                <label for="recruitSetDone"></label> 本页内容已设置完毕！
                            </div>
                            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="recruitSetDone()">确定</button>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div class="tips" style="font-size: 16px; color: #666"></div>
                                <div class="text-right btn-group">
                                    <span class="link-blue" onclick="newPostNeed()" style="margin-right: 16px">增加人才需求</span>
                                    <span class="link-blue" onclick="seeInputPostNeedDetail()">查看/编辑</span>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                招聘启示中的公共信息
                                <span style="margin-left: 32px">状态：<b class="commonInfoState">未设置</b></span>
                                <div class="text-right btn-group">
                                    <span class="link-blue" onclick="editRecruitCommon()">查看/设置</span>
                                </div>
                            </div>
                            <div class="ty-alert">
                                预览本次招聘启事分享到微信后的效果
                                <div class="text-right btn-group">
                                    <span class="link-blue" onclick="effectPreviewBtn()">预览</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page" page="editRecruitCommon">
                    <div class="topRightBtn">
                        <div class="ty-radio">
                            <input type="radio" id="recruitCommonSetDone" name="recruitCommonSetDone">
                            <label for="recruitCommonSetDone"></label> 本页内容已设置完毕！
                        </div>
                        <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="recruitCommonSetDone()">确定</button>
                    </div>
                    <div class="panel-box">
                        <div class="ty-alert">
                            <div>
                                <div>下表为招聘启事中的公共信息，请根据需要进行操作。</div>
                                <small class="ty-color-blue">注：以下各项中，“尚无内容”的，招聘启事中将不显示！</small><br>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="ty-alert">
                            <div class="ty-radio">
                                <input type="radio" id="clearContent" name="clearContent">
                                <label for="clearContent"></label> 已编辑的内容给予一次性清空！
                            </div>
                            <span class="link-blue" onclick="clearRecruitDataBtn()">确定</span>
                        </div>
                        <table class="kj-table tbl_RecruitNotice">
                            <thead>
                            <tr>
                                <td>项目名称</td>
                                <td>项目内容</td>
                                <td>当前状态</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>

                </div>
                <%-- 已失效的招聘启事 --%>
                <div class="page" page="invalidRecruitNotice">
                    <div class="ty-alert">
                        以下为已失效的招聘启事，时间区间：<span class="ty-color-blue beginDay"></span> 至 <span class="ty-color-blue endDay"></span>
                        <div class="btn-group text-right">
                            <div class="queryItem">
                                <span class="ssTtl">其他年月的数据</span>
                                <input class="kj-input kj-input-blue" id="countMonth" placeholder="请选择月份"/>
                            </div>
                        </div>
                    </div>
                    <table class="kj-table tbl_invalidRecruitNotice">
                        <thead>
                        <tr>
                            <td>创建</td>
                            <td>失效日期</td>
                            <td>终止日期</td>
                            <td>招聘职位</td>
                            <td>已收简历</td>
                            <td>通知面试</td>
                            <td>已面试</td>
                            <td>已入职</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <%-- 查看岗位需求列表 --%>
                <div class="page" page="postNeedList">
                    <div class="panel-box">
                        <div class="ty-alert">
                            在招聘启示中增加人才需求，请点击“新增”。
                            <div class="btn-group text-right">
                                <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="newPostNeed()" style="margin-left: 16px">新增</button>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="ty-alert">
                            <div class="tips"></div>
                        </div>
                        <table class="kj-table tbl_postNeedList">
                            <thead>
                            <tr>
                                <td>岗位名称</td>
                                <td>招聘数量</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <%-- 编辑岗位需求 --%>
                <div class="page" page="editPostNeed">
                    <div class="topRightBtn">
                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="savePostData()">确定</button>
                    </div>
                    <div class="ty-alert">以下各项中，“当前状态”为“显示”的，将在招聘启事上显示。点击“改变状态”，可在是否显示之间切换。</div>
                    <div class="ty-alert">
                        <div>
                            <div class="ty-color-blue">
                                <small class="ty-color-blue">注 如需要，可增加行。</small>
                            </div>
                        </div>
                        <div class="btn-group text-right">
                            <span class="link-blue" onclick="addOnePostProject()">增加一行</span>
                        </div>
                    </div>
                    <table class="kj-table tbl_PostNeed">
                        <thead>
                        <tr>
                            <td>项目名称</td>
                            <td>项目内容</td>
                            <td>当前状态</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <%-- 简历二维码 --%>
                <div class="page" page="qrCode">
                    <div class="ty-mainData">
                        <div class="main">
                            <div class="clearfix">
                                <div class="tblContainer recruit">
                                    <div class="ty-alert">本系统所支持的招聘流程可在很大程度上降低人事管理者的工作量。</div>
                                    <div class="declare_avatar">
                                        <div>
                                            <h4>使用流程</h4>
                                            <p>1. 应聘者扫描右侧二维码，在所见页面填写内容并提交后，应聘资料即进入系统</p>
                                            <p>2. 人事的手机端会收到提示</p>
                                            <p>3. 人事查看应聘者的资料后，可为其选择一位或多位面试官</p>
                                            <p>4. 所选各位面试官的手机端会收到提示</p>
                                            <p>5. 面试官面试时可在手机端填写面试意见</p>
                                            <p>6. 人事可查看面试意见，并可随时终止面试或向高层建议入职</p>
                                        </div>
                                        <div class="qrCode">
                                            <div id="qrCode_small"></div>
                                            <div>供求职者扫描的二维码</div>
                                            <div>可点击放大后截图保存使用</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <%-- 效果预览 --%>
                <div class="page" page="effectPreview">
                    <div class="row-flex">
                        <div class="phone_avatar">
                            <div class="nav_bar">
                                <div class="nav_left_btnGroup">
                                    <i data-name="collect" class="iconfont icon-houtui" title="后退"></i>
                                </div>
                                <div class="nav_title">微信</div>
                                <div class="nav_right_btnGroup"></div>
                            </div>
                            <div class="phone_container">
                                <div class="msgItem selfMsg" style="margin-top: 364px">
                                    <div class="avatar">w</div>
                                    <div class="msgItemCon">
                                        <div class="bubble_avatar">
                                            <div class="share_avatar">
                                                <div class="share_main">
                                                    <div class="share_des">
                                                        <div class="share_des_title">诚聘</div>
                                                        <div class="share_des_content">
                                                            <div class="share_post"></div>
                                                            <div>简历提交的截止日期 <span class="share_deadline"></span></div>
                                                        </div>
                                                    </div>
                                                    <div class="share_img"><img class="effect_notice_image" src="../../../../assets/images/recruit_default.jpg" alt=""></div>
                                                </div>
                                                <div class="app_info">
                                                    <span class="orgName"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="foot_bar"></div>
                            </div>
                        </div>
                        <div class="phone_avatar">
                            <div class="nav_bar">
                                <div class="nav_left_btnGroup">
                                    <i data-name="collect" class="iconfont icon-houtui" title="后退"></i>
                                </div>
                                <div class="nav_title">招聘启事</div>
                                <div class="nav_right_btnGroup"></div>
                            </div>
                            <div class="phone_container">
                                <div class="company effect_introduce"></div>
                                <div class="company">本公司现诚聘如下人才：</div>
                                <table class="kj-table effect_tbl_post">
                                    <thead>
                                    <tr>
                                        <td>岗位名称</td>
                                        <td>招聘人数</td>
                                        <td>操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div class="otherData" style="margin-top: 8px;background: #fff;">
                                    <div class="item-row effect_notice_workRest">
                                        <div class="item-title">作息时间：</div>
                                        <div class="item-content">--</div>
                                    </div>
                                    <div class="item-row effect_notice_address">
                                        <div class="item-title">工作地点：</div>
                                        <div class="item-content">--</div>
                                    </div>
                                    <div class="item-row effect_notice_description">
                                        <div class="item-title">补充说明：</div>
                                        <div class="item-content">--</div>
                                    </div>
                                    <div class="item-row effect_notice_deadline">
                                        <div class="item-title">提交截止日期：</div>
                                        <div class="item-content">--</div>
                                    </div>
                                    <div class="item-row effect_notice_contact">
                                        <div class="item-title">联系人：</div>
                                        <div class="item-content">--</div>
                                    </div>
                                    <div class="item-row effect_notice_telephone">
                                        <div class="item-title">电话：</div>
                                        <div class="item-content">--</div>
                                    </div>
                                    <div class="item-row effect_notice_email">
                                        <div class="item-title">邮箱：</div>
                                        <div class="item-content">--</div>
                                    </div>
                                    <div class="effect_notice_custom"></div>
                                </div>
                            </div>
                        </div>
                        <div class="phone_avatar">
                            <div class="nav_bar">
                                <div class="nav_left_btnGroup">
                                    <i data-name="collect" class="iconfont icon-houtui" title="后退"></i>
                                </div>
                                <div class="nav_title">招聘启事</div>
                                <div class="nav_right_btnGroup"></div>
                            </div>
                            <div class="phone_container">
                                <div style="margin-top: 8px;background: #fff;">
                                    <div class="item-row effect_post_postName">
                                        <div class="item-title">岗位名称：</div>
                                        <div class="item-content"></div>
                                    </div>
                                    <div class="item-row effect_post_recruitingNumbers">
                                        <div class="item-title">招聘人数：</div>
                                        <div class="item-content"></div>
                                    </div>
                                    <div class="otherPostData">
                                        <div class="item-column effect_post_duty">
                                            <div class="item-title">岗位职责：</div>
                                            <div class="item-content"></div>
                                        </div>
                                        <div class="item-column effect_post_requirement">
                                            <div class="item-title">岗位要求：</div>
                                            <div class="item-content"></div>
                                        </div>
                                        <div class="item-column effect_post_synthesis">
                                            <div class="item-title">综合：</div>
                                            <div class="item-content"></div>
                                        </div>
                                        <div class="item-column effect_post_otherAsk">
                                            <div class="item-title">其他条件与要求：</div>
                                            <div class="item-content"></div>
                                        </div>
                                        <div class="item-row effect_post_salary">
                                            <div class="item-title">薪资待遇：</div>
                                            <div class="item-content"></div>
                                        </div>
                                        <div class="item-row effect_post_workRest">
                                            <div class="item-title">作息时间：</div>
                                            <div class="item-content"></div>
                                        </div>
                                        <div class="item-row effect_post_address">
                                            <div class="item-title">工作地点：</div>
                                            <div class="item-content"></div>
                                        </div>
                                        <div class="effect_post_custom"></div>
                                    </div>
                                </div>
                                <div class="item-row effect_post_address">
                                    <button class="ty-btn ty-btn-blue ty-circle-3 ty-btn-big" style="width: 100%" onclick="acceptFake()">应聘此岗位，填写简历</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../assets/global/plugins/jquery.qrcode.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/general/recruit.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>