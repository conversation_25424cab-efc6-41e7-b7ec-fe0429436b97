<%--
  Created by IntelliJ IDEA.
  User: 侯杏哲
  Date: 2017/8/11
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .bonceCon input,.bonceCon select,.bonceCon textarea {
        border:1px solid #dee8f0;
        background-color: #fff;
        line-height: 36px;
        text-align: left;
        padding: 0 8px;
        color: #3f3f3f;
        width: 180px;
    }
    .bonceCon textarea{
        line-height: 20px;
        padding:5px;
    }
    .bonceCon input{
        height: 36px;
    }
    .bonceCon input:disabled{
        background-color: #dff0ff;
    }
    .bonceCon input:focus,.bonceCon select:focus,.bonceCon textarea:focus{
        border:1px solid #5d9cec;
    }
    .importIntro{
        float: left;
        padding:20px;
        color: #646c74;
        border: 1px dotted #5d9cec;
        width: 32%;
        margin-top: 10px;
        background-color: #fff;
        min-width: 420px ;
        height: 280px;
        margin-right: 8px;
        display: none;
    }
    .importIntro h3{
        font-weight: bold;
        font-size: 16px;
        color: #5d9cec;
        margin: 5px 0 8px 0;
    }
    .importIntro p{
        margin: 5px;
        font-size: 14px;
    }
    .handleTip{
        height: 180px;
    }
    .userRole{
        font-weight: bold;
        font-size: 16px;
        margin-left: 16px;
    }
    .ty-colFileTree>ul ul{display: none ; padding-left:15px }
    .ty-colFileTree .ty-treeItemActive>i.fa-file{  color: #fff;}
    .planIndent{text-indent: 2em;}
</style>
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>

<div class="bounce_Fixed">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="normalTip">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureSaveCoreUser()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--新增投诉立案者--%>
    <div class="bonceContainer bounce-blue" id="newDivision" style="width: 500px">
        <div class="bonceHead">
            <span>请选择负责人</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon clearfix" style="height: 300px;overflow: auto">
            <div class="ty-colFileTree"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="sureNewDivisionBtn" onclick="confirmSaveCoreUser()">确定</button>
        </div>
    </div>
    <%--负责人修改记录--%>
    <div class="bonceContainer bounce-blue" id="coreChangeHistory" style="width: 800px">
        <div class="bonceHead">
            <span><span class="module"></span>负责人修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon clearfix" style="height: 300px;overflow: auto">
            <h4 class="userRole ty-color-green"></h4>
            <div class="ty-alert ty-alert-info" style="justify-content: space-between"><span class="isChange"></span><span class="createInfo"></span></div>
            <table class="ty-table ty-table-control changeHistory">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td><span class="module"></span>负责人</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
</div>

<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>分工设置</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainData">
                    <p class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>您在此可确定或修改如下事项的负责人：</p>
                    <div class="importIntro core">
                        <div class="handleTip">
                            <h3>投诉管理</h3>
                            <p>投诉管理负责人全权负责投诉事项，将获得如下操作权限：</p>
                            <p>1 . 在系统内添加可录入投诉信息的职工</p>
                            <p>2 . 决定某条新录入的投诉是否立案</p>
                            <p>3 . 指定某条投诉的处理负责人</p>
                            <p>4 . 审批某条投诉的结案申请</p>
                            <p>5 . 确定何人可以查看投诉清单</p>
                        </div>
                        <p class="ty-alert ty-alert-info" style="justify-content:space-between">
                            <span>当前负责人：</span>
                            <b class="ty-color-blue coreUser"></b>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(0)">分配或修改负责人</button>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(1)">修改记录</button>
                        </p>
                    </div>
                    <div class="importIntro projectCore">
                        <div class="handleTip">
                            <h3>项目管理</h3>
                            <p>项目管理负责人全权负责项目管理，将获得如下操作权限：</p>
                            <p>1 . 在系统内添加可对项目管理立项的职工</p>
                            <p>2 . 决定某条新录入的项目管理项目是否立案</p>
                            <p>3 . 审批某项目管理项目的结案申请</p>
                        </div>
                        <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                            <span>当前负责人：</span>
                            <b class="ty-color-blue coreUser"></b>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(1)">分配或修改负责人</button>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(5)">修改记录</button>
                        </p>
                    </div>
                    <div class="importIntro improvementCore">
                        <div class="handleTip">
                            <h3>持续改进管理</h3>
                            <p>持续改进管理负责人全权负责持续改进，将获得如下操作权限：</p>
                            <p>1 . 在系统内添加可对持续改进立项的职工</p>
                            <p>2 . 决定某条新录入的持续改进项目是否立案</p>
                            <p>3 . 审批某持续改进项目的结案申请</p>
                        </div>
                        <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                            <span>当前负责人：</span>
                            <b class="ty-color-blue coreUser"></b>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(2)">分配或修改负责人</button>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(7)">修改记录</button>
                        </p>
                    </div>
                    <div class="importIntro vehicleCore">
                        <div class="handleTip">
                            <h3>车务管理</h3>
                            <p>系统内，车务管理负责人将获如下操作权限：</p>
                            <p>1 . 车辆管理：新增或停用车辆、修改车辆信息等</p>
                            <p>2 . 司机管理</p>
                            <p>3 . 向用车申请者派车。</p>
                        </div>
                        <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                            <span>当前负责人：</span>
                            <b class="ty-color-blue coreUser"></b>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(3)">分配或修改负责人</button>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(9)">修改记录</button>
                        </p>
                    </div>
                    <div class="importIntro pickingCore">
                        <div class="handleTip">
                            <h3>领料分工</h3>
                            <p>系统内，领料分工负责人将负责：</p>
                            <p>1 . 选择领料者；</p>
                            <p>2 . 所选领料者所能领用的材料。</p>
                        </div>
                        <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                            <span>当前负责人：</span>
                            <b class="ty-color-blue coreUser"></b>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(4)">分配或修改负责人</button>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(11)">修改记录</button>
                        </p>
                    </div>
                    <div class="importIntro buySalesCore">
                        <div class="handleTip ">
                            <h3>购销统筹</h3>
                            <div class="planIndent">
                                <div style="margin-bottom: 20px;">客户订单评审完后，购销统筹负责人将收到相关消息。</div>
                                <div>系统内，购销统筹负责人负责：</div>
                                <div>确定所需采购材料最少需购买多少，及最迟不得晚于何时到货。</div>
                            </div>
                        </div>
                        <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                            <span>当前负责人：</span>
                            <b class="ty-color-blue coreUser"></b>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(5)">分配或修改负责人</button>
                            <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(13)">修改记录</button>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/general/divisionSetting.js?v=SVN_REVISION"></script>
<%@ include  file="../../common/footerBottom.jsp"%>
</body>
</html>