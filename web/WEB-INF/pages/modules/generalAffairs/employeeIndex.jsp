<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/main/personalInformation.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/general/employeeIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/general/employeeInport.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="hd" id="importInfoSuccess">${success}</div>
<div class="hd" id="importInfoError">${error}</div>
<div class="bounce">
    <%--办理入职--%>
    <div class="bonceContainer bounce-green" id="handleEntry" style="width:700px;">
        <div class="bonceHead">
            <span>办理入职</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form id="handleForm">
                <ul class="handleFiled clear">
                    <li>
                        <span class="xing filedTtl">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</span>
                        <input value="" id="userName" name="userName" maxlength="20" require/>
                    </li>
                    <li>
                        <span class="xing filedTtl">手&nbsp;&nbsp;机&nbsp;&nbsp;号</span>
                        <input value="" id="mobile" name="mobile" maxlength="11" require/>
                    </li>
                    <li>
                        <span class="filedTtl">入职时间</span>
                        <input id="joinTime" name="date1" require/>
                    </li>
                    <li>
                        <span class="filedTtl">部&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;门</span>
                        <div class="department_panel" style="display:inline-block;">
                            <div>
                                <input type="hidden" id="oId" name="department" require/>
                                <input type="text" info="0" name="departName" onclick="toggleSelect('new',1,$(this))" readonly id="oName" style="float:left;" placeholder="请选择部门"/>
                                <span class="select-down downpositin" onclick="toggleSe($(this))" ></span>
                                <div class="clr"></div>
                            </div>
                            <div id="deparCon" class="option_con hd">
                                <div class="levelCon">
                                    <div class="levelItem" id="join_fristLevel">
                                        <div style="display:none; ">
                                            <span class="activeId" ></span>
                                            <span class="isKidsShow" ></span>
                                        </div>
                                        <a class="depatlevel">一级部门</a>
                                        <a class='depatlevel_num hd'>1</a>
                                        <div id="join_depart">
                                        </div>
                                    </div>
                                </div>
                                <div class="upBtn" onclick="$('#oName').click();">[ 收起 ]</div>
                            </div>
                        </div>
                    </li>
                    <li>
                        <span class="filedTtl">职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</span>
                        <select name="postID" require></select>
                    </li>
                    <li>
                        <span class="filedTtl">是否有下属</span>
                        <select name="ordinaryEmployees" require>
                            <option value='1'>无</option>
                            <option value='0'>有</option>
                        </select>
                    </li>
                    <li>
                        <span class="filedTtl">工作特点</span>
                        <select name="managerCode" require>
                            <option value="general">不含销售工作与财会工作</option>
                            <option value="finance">包含财务工作</option>
                            <option value="sale">含有销售工作</option>
                            <option value="accounting">包含会计工作</option>
                        </select>
                    </li>
                    <li>
                        <span class="filedTtl">直接上级</span>
                        <select name="pid" require>
                            <option>开发部</option>
                        </select>
                    </li>
                </ul>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="saveStuff" onclick="newStuffCheck()">确定</button>
        </div>
    </div>
    <%--档案管理--%>
    <div class="bonceContainer bounce-green" id="handleUpdate" style="width:700px;">
        <div class="bonceHead">
        </div>
        <div class="bonceCon">
            <div class="leaveLt">
                <div class="clear">
                    <h4 class="ty-color-green ty-left">档案管理</h4>
                    <div class="ty-right text-right" style="width: 120px">
                        <button class="ty-btn ty-btn-blue ty-btn-big" id="fileMgRecord" onclick="handleRecord(1, 10)">修改记录</button>
                        <button class="ty-btn ty-btn-blue ty-btn-big" id="helpActivateBtn" onclick="helpActivateBtn()" style="margin-top: 4px">帮他激活</button>
                    </div>

                </div>
                <p class="ty-color-red">您可对以下各项提交修改申请，或直接修改。</p>
            </div>
            <form id="handleUpdateForm">
                <ul class="handleFiled clear">
                    <li>
                        <span class="xing filedTtl">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</span>
                        <input id="updateUserName" name="userName" data-type="1" data-see="userName" maxlength="20" require/>
                    </li>
                    <li>
                        <span class="xing filedTtl">手&nbsp;&nbsp;机&nbsp;&nbsp;号</span>
                        <input id="updateMobile" maxlength="11" name="mobile" data-type="1" data-see="mobile" onblur="updateMobileNo($(this))" require/>
                        <input id="oldMobile" name="oldMobile" type="hidden" />
                    </li>
                    <li>
                        <span class="filedTtl">入职时间</span>
                        <input name="date1" data-type="3" id="onDutyDateEdit" require/>
                    </li>
                    <li>
                        <span class="filedTtl">部&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;门</span>
                        <div class="department_panel" style="display:inline-block;">
                            <div>
                                <input type="hidden" id="update_oId" name="department" data-type="1" require/>
                                <input type="text" info="0" name="departName" onclick="toggleSelect('update',1,$(this))" readonly id="update_oName" style="float:left;" placeholder="请选择部门"/>
                                <span class="select-down downpositin" onclick="toggleSe($(this))" ></span>
                                <div class="clr"></div>
                            </div>
                            <div id="update_deparCon" class="option_con hd">
                                <div class="levelCon">
                                    <div class="levelItem" id="fristLevel">
                                        <div style="display:none; ">
                                            <span class="activeId" ></span>
                                            <span class="isKidsShow" ></span>
                                        </div>
                                        <a class="depatlevel">一级部门</a>
                                        <a class='depatlevel_num hd'>1</a>
                                        <div id="update_depart">
                                        </div>
                                    </div>
                                </div>
                                <div class="upBtn" onclick="$('#update_oName').click();">[ 收起 ]</div>
                            </div>
                        </div>
                    </li>
                    <li>
                        <span class="filedTtl">职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</span>
                        <div class="department_panel" style="display:inline-block;" >
                            <select name="postID" require></select>
<%--                            <div>--%>
<%--                                <input autocomplete="off" type="hidden" id="update_zhiId" data-type="1" name="postID" require />--%>
<%--                                <input autocomplete="off" type="text" name="postName" info="0" onclick="toggleSelect('update',2,$(this))" readonly id="update_zhiName" style="float:left;" placeholder="请选择职位" />--%>
<%--                                <span class="select-down downpositin" onclick="toggleSe($(this))" ></span>--%>
<%--                                <div class="clr"></div>--%>
<%--                            </div>--%>
<%--                            <div id="update_zhiCon" class="option_con hd">--%>
<%--                                <div>--%>
<%--                                    <div>--%>
<%--                                        <span id="update_activeZhiId" class="hd"  ></span>--%>
<%--                                        <div id="update_join_zhi"> </div>--%>
<%--                                    </div>--%>
<%--                                </div>--%>
<%--                                <div class="upBtn"  onclick="$('#update_zhiName').click();">[ 收起 ]</div>--%>
<%--                            </div>--%>
                        </div>
                    </li>
                    <li>
                        <span class="filedTtl">是否有下属</span>
                        <select name="ordinaryEmployees" data-type="1" data-see="ordinaryEmployees" require onchange="changeOrdinary($(this))">
                            <option value="1">无</option>
                            <option value="0">有</option>
                        </select>
                    </li>
                    <li>
                        <span class="filedTtl">工作特点</span>
                        <select name="managerCode"  data-type="2" data-see="managerCode" require>
                            <option value="general">不含销售工作与财会工作</option>
                            <option value="finance">包含财务工作</option>
                            <option value="sale">含有销售工作</option>
                            <option value="accounting">包含会计工作</option>
                        </select>
                    </li>
                    <li>
                        <span class="filedTtl">直接上级</span>
                        <select name="leader" data-type="2" data-see="leaderName" require>
                            <option>开发部</option>
                        </select>
                    </li>
                    <li>
                        <span class="filedTtl">紧急联系人</span>
                        <span class="disEdit" id="emergencyName"></span>
                    </li>
                    <li>
                        <span class="filedTtl" style="margin-left: -36px;">紧急联系人的联系方式</span>
                        <span class="disEdit" id="emergencyContact"></span>
                    </li>
                </ul>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <button id="handleUpdateSure" class="ty-btn ty-btn-green ty-btn-big ty-circle-2" onclick="handleUpdateSure()">确定</button>
        </div>
    </div>
    <%--员工信息预览--%>
    <div class="bonceContainer bounce-green" id="employeeInforView"  style="width: 810px;">
        <div class="bonceHead"></div>
        <div class="bonceCon" style="max-height: 500px;overflow-y: auto">
            <div class="btnSect clear">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce.cancel()">退出预览</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-right" id="employeeEditBtn" onclick="getUpdateInfor()">修改</button>
            </div>
            <div id="employeeBaseInfo">
                <div class="userBaseInfo">
                    <div class="userImg">
                        <img id="uimg" onerror="this.src='../assets/oralResource/user.png'" alt="用户头像" />
                    </div>
                    <div class="userCon">
                        <h4 id="employeeUserName" data-name="userName"></h4>
                        <p class="baseDetail">
                            <span data-name="gender" require></span>
                            <span data-name="nation" require></span>
                            <span data-name="birthday" require></span>
                            <span data-name="birthplace" require></span>
                            <span data-name="politicalStatus" require></span>
                            <span data-name="marry" require></span>
                            <span data-name="degree" require></span>
                        </p>
                        <ul class="contectList clear"></ul>
                    </div>
                </div>
                <div class="otherInfo" id="skills">
                    <h5 class="ttlH5">个人技能</h5>
                    <div class="charact">
                        <div class="mmTtl">外语：</div>
                        <div class="mmCon">
                            <p>
                                <span>第一外语语种：<span data-name="firstLanguage" need></span></span>
                                </span><span data-name="firstForeignLevel" need></span>
                            </p>
                            <p>
                                <span>第二外语语种：<span data-name="secondLanguage" need></span></span>
                                </span><span data-name="secondForeignLevel" need></span>
                            </p>
                        </div>
                    </div>
                    <div class="charact">
                        <div class="mmTtl">计算机：</div>
                        <div class="mmCon">
                            <p data-name="computerLevel" need></p>
                        </div>
                    </div>
                    <div class="charact">
                        <div class="mmTtl">其它技能描述：</div>
                        <div class="mmCon">
                            <p data-name="otherSkills" need></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="otherInfo">
                <h5 class="ttlH5">教育经历</h5>
                <div id="eduHashMap">
                </div>
            </div>
            <div class="otherInfo">
                <h5 class="ttlH5">工作经历</h5>
                <div id="workHashMap">
                </div>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <div class="bonceContainer bounce-red" id="alertMS">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="altMS" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="mtTip" style="width:500px; ">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--导入--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #ddebf7;">
            <form action="../export/importUser.do" id="employeeImport" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：下载空白的“职工名单”。</p>
                        <div class="flexRow">
                            <span>职工名单</span>
                            <a href="../assets/oralResource/template/employee_blank_sheet.xls"
                               id="mould1" download="职工名单.xls" class="ty-btn ty-btn-blue">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“职工名单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="sysUseUploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="bounce.cancel()">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="sysUseImportOk()">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加、删除或修改所下载“职工档案”空白表的“列”，否则上传会失败。</span></br>
                            <span>2、在电脑上保存“职工名单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot" style="background: #ddebf7;">
        </div>
    </div>
    <%--修改职工信息--%>
    <div class="bonceContainer bounce-blue" id="updateUser">
        <div class="bonceHead">
            <span>修改职工信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="userForm">
                <div class="formItem">
                    <div class="left">姓名</div><input type="text" class="ty-inputText userName">
                    <i class="clearBtn" onclick="clearTxt($(this))">x</i>
                </div>
                <div class="formItem">
                    <div class="left">手机号</div><input type="text" onkeyup="clearNum(this)" class="ty-inputText userPhone">
                    <i class="clearBtn" onclick="clearTxt($(this))">x</i>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-blue" disabled="disabled" id="importUpdateUser" onclick="updateUserInfo()">确定</button>
        </div>
    </div>
    <%--删除职工--%>
    <div class="bonceContainer bounce-red" id="delUser">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">确定删除所导入的这个职工吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="delUserSure()">确定</span>
        </div>
    </div>
    <%--放弃后，本次批量导入--%>
    <div class="bonceContainer bounce-red" id="clearUser">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>放弃后，本次批量导入的数据将消失不见。</p>
                <p>确定放弃吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" data-name="clearUser">确定</span>
        </div>
    </div>
    <%--进入下一步--%>
    <div class="bonceContainer bounce-red" id="nextStep">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <div class="safeCondition">
                    <p>还有<span id="noSaveMbSum"></span>个手机号无法保存至系统。</p>
                    <p>进入下一步，这些号码将被舍弃。</p>
                </div>
                <p>确定进入下一步吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" data-name="nextStep">确定</span>
        </div>
    </div>
    <%--保存--%>
    <div class="bonceContainer bounce-red" id="lastSave">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>您共导入职工<span id="saveSum"></span>条，可保存至系统的共<span id="saveAble"></span>条。</p>
                <p>确定保存吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" data-name="lastSaveSure">确定</span>
        </div>
    </div>
    <%-- 上次的批量导入尚未完成。  --%>
    <div class="bonceContainer bounce-red" id="importNotCompleted">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody unfinishedForm">
                <p>上次的批量导入尚未完成。</p>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="1"></span>继续上次的操作
                </div>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="0"></span>放弃上次的操作，重新批量导入
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-name="judgeImport">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--二级警告提示--%>
    <div class="bonceContainer bounce-red" id="errorTip" style="width:400px; ">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--员工基本信息修改页面--%>
    <div class="bonceContainer bounce-blue" id="employeeUpdateIndex"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed.cancel()">返回</span>
                    <span class="panelTtl ty-left"><span id="userNameEdit"></span>信息</span>
                </div>
                <div class="opertionList" id="updateDetailsIndex">
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td class="tct">资料名称</td>
                            <td class="tct">操作</td>
                        </tr>
                        <tr>
                            <td>基本信息</td>
                            <td>
                                <span class="ty-color-blue" onclick="updateEmployeeBase()">编辑</span>
                                <span class="ty-color-blue" data-name="base" onclick="employeeBaseRecord('base', '', 1, 10)">修改记录</span>
                            </td>
                        </tr>
                        <tr class="eduList">
                            <td>教育经历</td>
                            <td>
                                <span class="ty-color-blue" data-type="new" onclick="employeeEduUpdate($(this))">新增</span>
                            </td>
                        </tr>
                        <tr class="workList">
                            <td>工作经历</td>
                            <td>
                                <span class="ty-color-blue" data-type="new" onclick="employeeWorkUpdate($(this))">新增</span>
                            </td>
                        </tr>
                        <tr>
                            <td>身份证</td>
                            <td>
                                <span class="ty-color-blue" onclick="identityCardEdit()">编辑</span>
                                <span class="ty-color-blue" onclick="employeeBaseRecord('card', '', 1, 10)">修改记录</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%--查重提示--%>
    <div class="bonceContainer bounce-red" id="checkTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center" id="checkTip_ms">
            </div>
        </div>
        <div class="bonceFoot">
            <div class="notAllow">
                <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" onclick="bounce_Fixed.cancel()">我知道了</span>
            </div>
            <div class="canAllow">
                <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-yellow" onclick="bounce_Fixed.cancel()">取  消</span>
                <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" id="tjSure" onclick="bounce_Fixed.cancel()">确定</span>
            </div>
        </div>
    </div>
    <%--确定修改确认--%>
    <div class="bonceContainer bounce-red" id="confirmEditMobile">
        <div class="bonceHead">
            <span id="mg">！提示</span>
            <a class="bounce_close" onclick="cancelTip()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>您此次的修改将导致该手机号无法登录系统。</p>
                <p>确定修改吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelTip()">取消</span>
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" id="editType1" onclick="updateMobileNoSure()">确定</span>
        </div>
    </div>
    <%--是否有下属切换提示--%>
    <div class="bonceContainer bounce-red" id="changeOrdinary">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>该职工已被选择为其他职工的直接上级。</p>
                <p>请确认！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%--帮他激活-第一层提示--%>
    <div class="bonceContainer bounce-red" id="helpActivateTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>该手机卡确实在该职工手中时，才可以点击确定。<br>——否则安全风险很大！！</p>
                <p>
                    <div class="ty-checkbox">
                        <input type="checkbox" name="isLeave" id="has_leave" value="0">
                        <label for="has_leave"></label> 经确认，<span class="helpActivateTip_mobile"></span>的手机卡确实在<span class="helpActivateTip_userName"></span>手中。
                    </div>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="helpActivateConfirmBtn()">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%--基本信息--%>
    <div class="bonceContainer bounce-green" id="employeeUpdate"  style="width: 930px;">
        <div class="bonceHead">
            <span>基本信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 500px;overflow-y: auto">
            <div class="clear addInfoSect">
                <form id="updateBaseDetails">
                    <ul class="baseSect">
                        <li>
                            <span>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</span>
                            <span id="updateBaseName">兰亭序</span>
                        </li>
                        <li>
                            <span>性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</span>
                            <input type="radio" name="sex" value="1" id="man_male" data-org="" />
                            <label for="man_male">男</label>
                            <input type="radio" name="sex" value="0" id="man_female" data-org="" />
                            <label for="man_female">女</label>
                        </li>
                        <li>
                            <span>民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族</span>
                            <input name="nation" value="" require/>
                        </li>
                        <li>
                            <span>出生日期</span>
                            <input id="base_birthday" name="birthday" value="" require/>
                        </li>
                        <li>
                            <span>出&nbsp;&nbsp;生&nbsp;&nbsp;地</span>
                            <input id="personBirthAddress" name="birthplace" value="" require/>
                        </li>
                        <li>
                            <span>婚姻状况</span>
                            <select id="personMarry" name="marry" data-org="" require>
                                <option value="1" selected="selected">未婚</option>
                                <option value="0">已婚</option>
                            </select>
                        </li>
                        <li>
                            <span>政治面貌</span>
                            <input id="personPolitical" name="politicalStatus" value="" require/>
                        </li>
                        <li>
                            <span>最高学历</span>
                            <select id="personEdu" name="degree" data-org="0">
                                <option selected="selected" style="display: none" value="0"></option>
                                <option value="1">研究生</option>
                                <option value="2">本科</option>
                                <option value="3">大专</option>
                                <option value="4">中专或高中</option>
                                <option value="5">其他</option>
                            </select>
                        </li>
                        <li>
                            <span>第一外语语种</span>
                            <input id="personLanguage1" name="firstLanguage" value="" require/>
                        </li>
                        <li>
                            <span>水平或证书</span>
                            <input id="personCertificate1" name="firstForeignLevel" value="" require/>
                        </li>
                        <li>
                            <span>第二外语语种</span>
                            <input id="personLanguage2" name="secondLanguage" value="" require/>
                        </li>
                        <li>
                            <span>水平或证书</span>
                            <input id="personCertificate2" name="secondForeignLevel" value="" require/>
                        </li>
                        <li class="resetStyle">
                            <span>计算机水平或证书</span>
                            <input id="personComputerLevel" name="computerLevel" value="" require/>
                        </li>
                        <li class="resetStyle">
                            <span>其他技能描述</span>
                            <input id="personOtherSkills" name="otherSkills" value="" require/>
                        </li>
                        <li class="resetStyle">
                            <span>手机号码</span>
                            <span id="personPhone">13522200220</span>
                            <a class="ty-blue addMoreContact" onclick="addMoreContact($(this))">添加更多联系方式</a>
                            <select class="addMoreSelect" style="display: none;width: 150px;" onchange="addMoreChange($(this))">
                                <option value="0" style="display: none"></option>
                                <option value="1">手机</option>
                                <option value="2">QQ</option>
                                <option value="3">Email</option>
                                <option value="4">微信</option>
                                <option value="5">微博</option>
                                <option value="9">自定义</option>
                            </select>
                        </li>
                    </ul>
                </form>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button id="updateEmployeeBase" class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="updateEmployeeBaseSure()">确定</button>
        </div>
    </div>
    <%--新增教育经历--%>
    <div class="bonceContainer bounce-green" id="addEduInfo"  style="width: 730px;">
        <div class="bonceHead">
            <span>教育经历</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 500px;overflow-y: auto">
            <form id="eduInfoForm">
                <ul class="editSect">
                    <li>
                        <span>学习时间</span>
                        <input id="studyBegin" name="beginTime" require />
                        <span>—— ——</span>
                        <input id="studyEnd" name="endTime" require />
                    </li>
                    <li>
                        <span>毕业院校</span>
                        <input type="text" class="longSize" name="collegeName" require/>
                    </li>
                    <li>
                        <span>院&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;系</span>
                        <input value="" name="departmentName" require/>
                        <span>专&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业</span>
                        <input value="" name="major" require/>
                    </li>
                    <li>
                        <span>学历/学位</span>
                        <input id="degree" value="" name="degreeDesc" require/>
                    </li>
                    <li>
                        <span class="pickTop">专业描述</span>
                        <textarea id="professional" value="" class="longSize" name="majorDesc" require></textarea>
                    </li>
                    <li>
                        <span class="pickTop">补充说明</span>
                        <textarea id="moreNotes" value="" class="longSize" name="memo" require></textarea>
                    </li>
                </ul>
            </form>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="eduExperienceTj" onclick="employeeEduUpdateTj()">确定</button>
        </div>
    </div>
    <%--新增工作经历--%>
    <div class="bonceContainer bounce-green" id="addWorkInfo"  style="width: 730px;">
        <div class="bonceHead">
            <span>工作经历</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 500px;overflow-y: auto">
            <form id="WorkInfoForm">
                <ul class="editSect">
                    <li>
                        <span>服务时间</span>
                        <input id="workBegin" name="beginTime" require />
                        <span>—— ——</span>
                        <input id="workEnd" name="endTime" require />
                    </li>
                    <li>
                        <span>公司名称</span>
                        <input type="text" class="longSize" name="corpName" require/>
                    </li>
                    <li>
                        <span>公司规模</span>
                        <input value="" name="corpSize" require/>
                        <span>公司性质</span>
                        <input value="" name="corpNature" require/>
                    </li>
                    <li>
                        <span>所在部门</span>
                        <input value="" name="corpDepartment" require/>
                        <span>职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</span>
                        <input value="" name="post" require/>
                    </li>
                    <li>
                        <span class="pickTop">工作描述</span>
                        <textarea id="workContent" value="" class="longSize" name="jobDesc" require></textarea>
                    </li>
                    <li>
                        <span class="pickTop">补充说明</span>
                        <textarea id="workMoreNotes" value="" class="longSize" name="memo" require></textarea>
                    </li>
                </ul>
            </form>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="workExperienceTj" onclick="employeeWorkUpdateTj()">确定</button>
        </div>
    </div>
    <%--基本信息修改记录--%>
    <div class="bonceContainer bounce-blue" id="baseInfoRecord"  style="width: 730px;">
        <div class="bonceHead"></div>
        <div class="bonceCon">
            <div class="btnSect clear">
                <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed2.cancel()">返回</p>
                <p class="panelTtl ty-left" id="baseRecordName">基本信息修改记录</p>
            </div>
            <h4 class="notEdit notEditBase">基本信息尚未修改过。</h4>
            <div class="recordMain">
                <div class="clear">
                    <p class="ty-left">当前数据为第<span class="baseUpdateNum"></span>次修改后的结果。</p>
                    <p class="ty-right">修改者：<span class="baseUpdateInfo"></span></p>
                </div>
                <table class="ty-table ty-table-control updateBaseMessage">
                </table>
            </div>
            <div id="basePageInfo"></div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--教育经历修改记录/工作经历修改记录--%>
    <div class="bonceContainer bounce-blue" id="eduInfoRecord"  style="width: 730px;">
        <div class="bonceHead"></div>
        <div class="bonceCon">
            <div class="btnSect clear">
                <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed2.cancel()">返回</p>
                <p class="panelTtl ty-left" id="recordName">教育经历修改记录</p>
            </div>
            <h4 class="notEdit notEditOther">教育经历尚未修改过。</h4>
            <div class="recordMain">
                <div class="clear deltedAfter" style="display: none;">
                    <p class="ty-left deltedTtl"></p>
                    <p class="ty-left deltedCon"></p>
                </div>
                <div class="clear">
                    <p class="ty-left othereUpdateNum"></p>
                    <p class="ty-right">修改者：<span class="otherUpdateInfo"></span></p>
                </div>
                <table class="ty-table ty-table-control updateRecord">
                </table>
            </div>
            <div id="otherPageInfo"></div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--删除--%>
    <div class="bonceContainer bounce-red" id="deleteDetail">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMessage">
                <p class="deleteClear">确定删除本条<span class="deleteTtl"></span>吗？</p>
                <p class="deleteHold">删除后，您所预览的个人资料中将不再含有本条数据。</p>
                <p class="deleteHold">如需要，您可在修改记录中查看。</p>
                <p class="deleteHold">确定删除本条<span class="deleteTtl"></span>吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="deleteExperienceSure()">确定</span>
        </div>
    </div>
    <%--身份证编辑--%>
    <div class="bonceContainer bounce-green" id="identityCardEdit"  style="width: 730px;">
        <div class="bonceHead">
            <span>身份证</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p><span class="ty-color-red">！！重要提示</span>  职工身份证号与住址，全公司仅您自己可见，其他人都见不到，故请注意保密！</p>
            <form id="cardEditForm" class="gapSect">
                <ul>
                    <li>
                        <span>身份证号</span>
                        <input type="text" value="" id="cardNumber" />
                        <span class="ty-color-red" style="display: none" id="cardNumberTip">请填写正确的身份证号码！</span>
                    </li>
                    <li>
                        <span>身份证上的住址</span>
                        <input type="text" value="" id="cardAddress" style="width: 500px;" />
                    </li>
                </ul>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="identityCardEditSure" onclick="identityCardEditSure()">确定</button>
        </div>
    </div>
    <%--帮他激活-第二层提示--%>
    <div class="bonceContainer bounce-red" id="helpActivateConfirmTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>本修改将导致号码 <span class="helpActivateConfirmTip_mobile"></span> 无法再登录系统。<br>确定修改吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="sureHelpActivate()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%--链接错误提示--%>
    <div class="bonceContainer bounce-red" id="errorMS">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="shu1">
                <p id="errorMSTip" style="text-align: center; padding:10px 0"> </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">确定</span>
        </div>
    </div>
    <%--个人基本信息查看--%>
    <div class="bonceContainer bounce-blue" id="seeBaseInfor"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed3.cancel()">返回</p>
                    <p class="panelTtl ty-left">基本信息</p>
                </div>
                <ul class="baseSect clear" id="baseDetails">
                    <li>
                        <div class="ltTtl">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</div>
                        <span data-name="userName" require>兰亭序</span>
                    </li>
                    <li>
                        <div class="ltTtl">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</div>
                        <div class="rtCon" data-name="gender" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族</div>
                        <div class="rtCon" data-name="nation" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">出生日期</div>
                        <div class="rtCon" data-name="birthday" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">出&nbsp;&nbsp;生&nbsp;&nbsp;地</div>
                        <div class="rtCon" data-name="birthplace" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">婚姻状况</div>
                        <div class="rtCon" data-name="marry" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">政治面貌</div>
                        <div class="rtCon" data-name="politicalStatus" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">最高学历</div>
                        <div class="rtCon" data-name="degree" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">第一外语语种</div>
                        <div class="rtCon" data-name="firstLanguage" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">水平或证书</div>
                        <div class="rtCon" data-name="firstForeignLevel" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">第二外语语种</div>
                        <div class="rtCon" data-name="secondLanguage" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">水平或证书</div>
                        <div class="rtCon" data-name="secondForeignLevel" require>兰亭序</div>
                    </li>
                    <li class="resetStyle">
                        <div class="ltTtl">计算机水平或证书</div>
                        <div class="rtCon longSize" data-name="computerLevel" require>兰亭序</div>
                    </li>
                    <li class="resetStyle">
                        <div class="ltTtl">其他技能描述</div>
                        <div class="rtCon longSize" data-name="otherSkills" require>兰亭序</div>
                    </li>
                    <li class="resetStyle">
                        <div class="ltTtl">手机号码</div>
                        <span data-name="mobile" require></span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--教育经历信息查看--%>
    <div class="bonceContainer bounce-blue" id="seeEduInfor"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed3.cancel()">返回</p>
                    <p class="panelTtl ty-left">教育经历</p>
                </div>
                <ul class="recordSee" id="eduRecordSee">
                    <li>
                        <div class="ltTtl">学习时间</div>
                        <div class="rtCon" data-name="beginTime" require></div>
                        <div class="ltTtl">— —</div>
                        <div class="rtCon" data-name="endTime" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">毕业院校</div>
                        <div class="rtCon longSize" data-name="collegeName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">院&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;系</div>
                        <div class="rtCon" data-name="departmentName" require></div>
                        <div class="ltTtl">专&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业</div>
                        <div class="rtCon" data-name="major" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">学历/学位</div>
                        <div class="rtCon" data-name="degreeDesc" require></div>
                    </li>
                    <li>
                        <div class="ltTtl pickTop">专业描述</div>
                        <div class="rtArea" data-name="majorDesc" require></div>
                    </li>
                    <li>
                        <div class="ltTtl pickTop">补充说明</div>
                        <div class="rtArea" data-name="memo" require></div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--工作经历信息查看--%>
    <div class="bonceContainer bounce-blue" id="seeWorkInfor"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed3.cancel()">返回</p>
                    <p class="panelTtl ty-left">工作经历</p>
                </div>
                <ul class="recordSee" id="workRecordSee">
                    <li>
                        <div class="ltTtl">服务时间</div>
                        <div class="rtCon" data-name="beginTime" require></div>
                        <div class="ltTtl">— —</div>
                        <div class="rtCon" data-name="endTime" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">公司名称</div>
                        <div class="rtCon longSize" data-name="corpName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">公司规模</div>
                        <div class="rtCon" data-name="corpSize" require></div>
                        <div class="ltTtl">公司性质</div>
                        <div class="rtCon" data-name="corpNature" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">所在部门</div>
                        <div class="rtCon" data-name="corpDepartment" require></div>
                        <div class="ltTtl">职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</div>
                        <div class="rtCon" data-name="post" require></div>
                    </li>
                    <li>
                        <div class="ltTtl pickTop">工作描述</div>
                        <div class="rtArea" data-name="jobDesc" require></div>
                    </li>
                    <li>
                        <div class="ltTtl pickTop">补充说明</div>
                        <div class="rtArea" data-name="memo" require></div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--身份证查看--%>
    <div class="bonceContainer bounce-green" id="identityCardSee"  style="width: 730px;">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel">
            <div class="btnSect clear">
                <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed3.cancel()">返回</p>
                <p class="panelTtl ty-left">身份证</p>
            </div>
            <p><span class="ty-color-red">！！重要提示</span>  职工身份证号与住址，全公司仅您自己可见，其他人都见不到，故请注意保密！</p>
            <div class="gapSect">
                <ul>
                    <li>
                        <span>身份证号</span>
                        <span id="cardNumberSee"></span>
                    </li>
                    <li>
                        <span>身份证上的住址</span>
                        <span id="cardAddressSee"></span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" onkeyup="countWords($(this),20)"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
    <%--职工档案查看--%>
    <div class="bonceContainer bounce-blue" id="seeHandleInfor"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed3.cancel()">返回</p>
                    <p class="panelTtl ty-left">档案管理</p>
                </div>
                <ul class="baseSect" id="fileManage">
                    <li>
                        <div class="ltTtl">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</div>
                        <div class="rtCon" data-name="userName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">手&nbsp;机&nbsp;号</div>
                        <div class="rtCon" data-name="mobile" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">入职时间</div>
                        <div class="rtCon" data-name="onDutyDate" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">部&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;门</div>
                        <div class="rtCon" data-name="departName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</div>
                        <div class="rtCon" data-name="postName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">是否有下属</div>
                        <div class="rtCon" data-name="ordinaryEmployees" require>无</div>
                    </li>
                    <li>
                        <div class="ltTtl">工作特点</div>
                        <div class="rtCon" data-name="manageCode" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">直接上级</div>
                        <div class="rtCon" data-name="leaderName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">紧急联系人</div>
                        <div class="rtCon" data-name="emergencyName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">紧急联系人的联系方式</div>
                        <div class="rtCon" data-name="emergencyContact" require></div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--导入失败--%>
    <div class="bonceContainer bounce-red" id="importantTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <h4>导入失败！</h4>
                <div>
                    <div>原因可能为：</div>
                    <div>1、修改了所下载表格中的“列”。</div>
                    <div>2、选错了文件。</div>
                    <div>3、文件太大，或里面含有图片等。</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed3.cancel()">知道了</span>
        </div>
    </div>
    <%--知道了 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed3.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道了 - 提示--%>
    <div class="bonceContainer bounce-red" id="iknowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="iknowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed3.cancel()">我知道了</span>
        </div>
    </div>
</div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>职工档案</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div>
                    <%--筛选查询部分--%>
                    <div class="employeeQuery main" style="position: relative;float:left;" >
                        <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3 customQuery" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" id="customQueryBtn" value="4">筛选</button>
                        <ul class="dropdown-menu dropdown-menu-default searchCon">
                            <div class="eq_item clearfix belongScreenBody">
                                <div class="eq_l">
                                    所属的机构
                                    <div class="allOrgCheck" onclick="checkAllOrg(event)">全选</div>
                                </div>
                                <div class="eq_r" id="belongOrg">
                                    <%--从系统导入所属的机构列表--%>
                                </div>
                            </div>
                            <div class="eq_item clearfix">
                                <div class="eq_l">
                                    最高学历
                                </div>
                                <div class="eq_r" id="education">
                                    <div class='ty-form-checkbox' skin="green">
                                        <span data-val="1">研究生</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                    <div class='ty-form-checkbox' skin="green">
                                        <span data-val="2">本科</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                    <div class='ty-form-checkbox' skin="green">
                                        <span data-val="3">大专</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                    <div class='ty-form-checkbox' skin="green">
                                        <span data-val="4">中专或高中</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                    <div class='ty-form-checkbox' skin="green">
                                        <span data-val="5">其他</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="eq_item clearfix">
                                <div class="eq_l">
                                    性别
                                </div>
                                <div class="eq_r" id="gender">
                                    <div class='ty-form-checkbox' skin="green">
                                        <span value="1">男</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                    <div class='ty-form-checkbox' skin="green">
                                        <span value="0">女</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="eq_item clearfix">
                                <div class="eq_l">
                                    部门
                                </div>
                                <div class="eq_r" id="department">
                                    <%--从系统导入职部门列表--%>
                                    <c:forEach items="${departmentList}" var="d">
                                        <div class='ty-form-checkbox' skin="green">
                                            <span>${d.name}</span>
                                            <i class="fa fa-check"></i>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                            <div class="eq_item clearfix">
                                <div class="eq_l">
                                    职位
                                </div>
                                <div class="eq_r" id="post">
                                    <c:forEach items="${postList}" var="p">
                                        <%--从系统导入职位列表--%>
                                        <div class='ty-form-checkbox' skin="green">
                                            <span>${p.name}</span>
                                            <i class="fa fa-check"></i>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                            <div class="eq_item clearfix">
                                <div class="eq_l">
                                    婚否
                                </div>
                                <div class="eq_r" id="marry">
                                    <div class='ty-form-checkbox' skin="green">
                                        <span value="1">未婚</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                    <div class='ty-form-checkbox' skin="green">
                                        <span value="0">已婚</span>
                                        <i class="fa fa-check"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="eq_item clearfix">
                                <div class="eq_l">
                                    出生年月
                                </div>
                                <div class="eq_r">
                                    <input type="text" id="birthday1" style="width: 120px"> ~
                                    <input type="text" id="birthday2" style="width: 120px">
                                </div>
                            </div>
                            <div class="eq_item clearfix">
                                <div class="eq_l">
                                    入职时间
                                </div>
                                <div class="eq_r" id="postDate">
                                    <input type="text" id="onDutyDate1" style="width: 120px"> ~
                                    <input type="text" id="onDutyDate2" style="width: 120px">
                                </div>
                            </div>
                            <div style="text-align: right">
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="queryEmployeeBtn">查询</button>
                            </div>
                        </ul>
                    </div>
                    <%--操作按钮--%>
                    <div class="tools main" align="right"><%--joinStaff(${pa.id})--%>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="leadingStart()">批量导入</span>
                        <span class="ty-btn ty-btn-big ty-btn-blue  ty-circle-3" onclick="joinStaff()">办理入职</span>
                        <a class="ty-btn ty-btn-big ty-btn-green ty-circle-3" href="../export/exportAllUser.do">导出</a>
                        ${success}
                        ${error}
                    </div>
                        <div class="importOpertion">
                            <div class="hd importing stepItem">
                                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 btn" data-type="cancelSave" style="margin-right: 250px;">放 弃</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 btn" id="ok">保 存</span>
                            </div>
                            <div class="importNoSave stepItem" style="display: none">
                                <span class="ty-btn ty-btn-yellow ty-btn-big btn" data-type="clearNoSave" style="margin-right: 250px;">放 弃</span>
                                <span class="ty-btn ty-btn-blue ty-btn-big btn" data-type="stepNext">下一步</span>
                            </div>
                        </div>
                    <span class="goBack ty-btn ty-btn-big ty-btn-green ty-circle-3 hd" onclick="goback()">返回</span>
                </div>
                <div class="clr"></div>
                <br>
                <div class="ty-mainPage">
                    <ul class="ty-secondTab main">
                        <li class="ty-active">在职列表</li>
                        <li>离职列表</li>
                    </ul>
                    <div class="ty-mainData">
                        <div class="main">
                            <div class="tblContainer">
                                <%--在职列表--%>
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td>姓名</td>
                                    <td>性别</td>
                                    <td>手机号</td>
                                    <td class="belongOrg">所属的机构</td>
                                    <td>部门</td>
                                    <td>职位</td>
                                    <td>直接上级</td>
                                    <td>最高学历</td>
                                    <td>更多</td>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div id="ye_employee"></div>
                            </div>
                            <div class="tblContainer" style="display: none">
                                <%--离职列表--%>
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td>姓名</td>
                                    <td>性别</td>
                                    <td>手机号</td>
                                    <td class="belongOrg">所属的机构</td>
                                    <td>部门</td>
                                    <td>职位</td>
                                    <td>直接上级</td>
                                    <td>最高学历</td>
                                    <td>操作</td>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="importNoSave narrowLamp" style="display: none">
                            <p>您共导入职工<span class="initAll"></span>条，其中以下<span class="initWrong"></span>条存在问题，<span class="ty-color-red"> 无法保存至系统</span>。</p><br>
                            <p>姓名或手机号未录入、手机号错误或与系统中已有号码相同等，均算作问题。</p><br>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>操作</td>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>赵策</td>
                                    <td>123655</td>
                                    <td>
                                        <span class="ty-color-blue btn" data-type="update">修改</span>
                                        <span class="ty-color-red btn" data-type="del">删除</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="importing" style="display: none">
                            <p>您共导入职工<span class="importSum"></span>条，可保存至系统的共<span class="saveSum"></span>条。</p>
                            <p>1、为每位职工选定“工作特点”后，才可保存至系统。</p>
                            <p>2、Wonderss内请假、加班、报销等均与“直接上级”有关，故建议按实际情况给予选定。</p>
                            <p class="exportStep">找到<span class="ty-color-red">最高领导的直接下属</span>后，将其“直接上级”选择为最高领导，之后逐级选择的操作方式较易理解。</p><br>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>是否有下属</td>
                                <td>直接上级</td>
                                <td>工作特点</td>
                                <td>操作</td>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var imgPath = '${user.imgPath}';
    if( typeof imgPath === 'string' && imgPath.length > 0 ) {
        $("#uimg").attr("src", $.fileUrl + imgPath)
    }
</script>
<script src="../script/function.js?v=SVN_REVISION"></script>
<script src="../script/jquery.validate.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/general/employeeIndex.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/general/employeeIndexCommon.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
