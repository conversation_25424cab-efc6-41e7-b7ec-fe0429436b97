<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/user/handleEntry.css?v=SVN_REVISION">
<link rel="stylesheet" type="text/css" href="../css/general/recruit.css?v=SVN_REVISION" />
<style>
    .departItem{padding:2px;}
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="alertMS">
        <div class="bonceHead">   
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="altMS" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>职工档案</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div>
                    <span class="goBack ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="history.back();">返回</span>
                </div>
                <form action="../general/addUser.do" id="saveUser" method="post" enctype="multipart/form-data">
                    <div class="employeeJoin us_input">
                        <div class="addStaffInfo">
                            <div class="sect">
                                <div>
                                    <span class="small_ttl">基本信息</span>
                                </div>
                                <div class="con_part overflow">
                                    <div class="masterInfo ty-left">
                                        <ul class="overflow">
                                            <li class="col-xs-6 col-sm-4"><span class="us_field xing">姓名：</span>
                                                <input autocomplete="off" type="text" name="userName"  maxlength="20" id="userName"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">性别：</span>
                                                <select id="news_sex" class="select_normal" name="gender">
                                                    <option value="1">男</option>
                                                    <option value="0">女</option>
                                                </select>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">出生年月：</span>
                                                <input autocomplete="off" type="text" name="date" value="" id="birthday" placeholder="请选择出生日期">
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">最高学历：</span>
                                                <select id="user_edu" name="degree" class="select_normal">
                                                    <option disabled selected value></option>
                                                    <option value="1">研究生</option>
                                                    <option value="2">本科</option>
                                                    <option value="3">大专</option>
                                                    <option value="4">中专或高中</option>
                                                    <option value="5">其它</option>
                                                </select>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">婚姻状况：</span>
                                                <select id="user_marry" name="marry" class="select_normal">
                                                    <option value="1">未婚</option>
                                                    <option value="0">已婚</option>
                                                </select>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">民族：</span>
                                                <input autocomplete="off" type="text" name="nation" maxlength="20"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">政治面貌：</span>
                                                <input autocomplete="off" type="text"  name="politicalStatus" maxlength="20"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">籍贯：</span>
                                                <input autocomplete="off" type="text" name="nativePlace" maxlength="100"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-10"><span class="us_field">特长：</span>
                                                <input autocomplete="off" type="text" name="interesting" maxlength="100" style="width:370px;" />
                                            </li>
                                            <li class="col-xs-6 col-sm-10"><span class="us_field">爱好：</span>
                                                <input autocomplete="off" type="text" name="speciality" maxlength="100" style="width:370px;" />
                                            </li>
                                        </ul>
                                        <ul class="overflow">
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">身份证号：</span>
                                                <input autocomplete="off" type="text" name="idCard" maxlength="18" id="idCard"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-8"><span class="us_field">家庭住址：</span>
                                                <input autocomplete="off" type="text" name="homeAddress" maxlength="100" style="width: 300px;"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field xing">联系电话：</span>
                                                <input autocomplete="off" type="text" name="mobile" maxlength="11" id="mobile"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">E-mail：</span>
                                                <input autocomplete="off" type="text" name="email" maxlength="50"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">QQ:</span>
                                                <input autocomplete="off" type="text" name="qq" maxlength="20"/>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="ty-left">
                                        <div class="viewerTx">
                                            <img id="preview" align="center" src="../assets/oralResource/user.png" onerror="this.src='../assets/oralResource/user.png'" alt="用户头像" />
                                            <%--<img src="staff.png" title="员工照片">--%>
                                        </div>
                                        <div class="imgOption">
                                            <input name="imgFile" id="imgFile" type="file" onchange="setImagePreview();" style="display:none;" disabled="disabled"/>
                                            <span class="ty-btn ty-btn-green ty-circle-3" onclick="uploadPhoto()">上传照片</span>
                                            <span class="ty-btn ty-btn-green ty-circle-3" onclick="deleteUerImg()">删除照片</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="sect">
                                <div>
                                    <span class="small_ttl">岗位信息</span>
                                </div>
                                <div class="con_part">
                                    <div class="masterInfo ">
                                        <ul>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">入职时间：</span>
                                                <input autocomplete="off" type="text"  name="date1" id="date1" placeholder="请选择入职时间"/>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field" style="line-height:32px;">部门：</span>
                                                <div class="department_panel" style="display:inline-block;   ">
                                                    <div>
                                                        <input type="text" id="oId" name="department" style="display:none;" />
                                                        <input type="text" info="0" name="departName" onclick="toggleSelect(1,$(this))" readonly id="oName" style="float:left;" placeholder="请选择部门"/>
                                                        <span class="select-down downpositin" onclick="toggleSe($(this))" ></span>
                                                        <div class="clr"></div>
                                                    </div>
                                                    <div id="deparCon" class="option_con hd">
                                                        <div id="levelCon">
                                                            <div class="levelItem">
                                                                <div style="display:none; ">
                                                                    <span class="activeId" ></span>
                                                                    <span class="isKidsShow" ></span>
                                                                </div>
                                                                <a class="depatlevel">一级部门</a>
                                                                <a class='depatlevel_num hd'>1</a>
                                                                <div>
                                                                    <c:forEach items="${departmentList}" var="dl">
                                                                    <span class="departItem" onclick="getKidsDepart($(this))">
                                                                        <span>${dl.name}</span>
                                                                        <span class="hd oid">${dl.id}</span>
                                                                        <span class="hd pid">${dl.pid}</span>
                                                                    </span>
                                                                    </c:forEach>
                                                                    <div class="clr"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="upBtn" onclick="$('#oName').click();">[ 收起 ]</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">职位：</span>
                                                <span style="clear:both"></span>
                                                <div class="department_panel" style="display:inline-block;" >
                                                    <div>
                                                        <input autocomplete="off" type="text" id="zhiId" name="postID" style="display:none;" />
                                                        <input autocomplete="off" type="text" name="postName" info="0" onclick="toggleSelect(2,$(this))" readonly id="zhiName" style="float:left;" placeholder="请选择职位" />
                                                        <span class="select-down downpositin" onclick="toggleSe($(this))" ></span>
                                                        <div class="clr"></div>
                                                    </div>
                                                    <div id="zhiCon" class="option_con hd">
                                                        <div>
                                                            <div>
                                                                <span id="activeZhiId" class="hd"  ></span>
                                                                <c:forEach items="${postList}" var="pl">
                                                                <span class="zhiItem" onclick="getZhi($(this))">
                                                                    <span>${pl.name}</span>
                                                                    <span class="hd">${pl.id}</span>
                                                                </span>
                                                                </c:forEach>
                                                                <div class="clr"> </div>
                                                            </div>
                                                        </div>
                                                        <div class="upBtn"  onclick="$('#zhiName').click();">[ 收起 ]</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">是否为普通员工：</span>
                                                <select id="is_ordinary" class="select_normal" title="0" name="ordinaryEmployees" onclick="toggleSelect($(this))">
                                                    <option value="0">否</option>
                                                    <option value="1">是</option>
                                                </select>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">所属高管：</span>
                                                <input autocomplete="off" type="text" name="manager" style="display: none">
                                                <input autocomplete="off" type="text" name="managerCode" style="display: none">
                                                <select class="manage select_normal">
                                                    <c:forEach items="${manages}" var="ma">
                                                        <c:if test="${ma.manageName=='总务'}">
                                                            <option value="${ma.userID}-${ma.managerCode}">${ma.manageName}</option>
                                                        </c:if>
                                                    </c:forEach>
                                                    <c:forEach items="${manages}" var="ma">
                                                        <c:if test="${ma.manageName!='总务'}">
                                                            <option value="${ma.userID}-${ma.managerCode}">${ma.manageName}</option>
                                                        </c:if>
                                                    </c:forEach>
                                                </select>
                                            </li>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">直接上级：</span>
                                                <select class="superior select_normal" name="pid" onclick="toggleSelect($(this))">
                                                    <%--<option disabled selected value></option>--%>
                                                    <%--&lt;%&ndash;<c:if test="${user.userID!=pUser.userID}">&ndash;%&gt;--%>
                                                    <%--<option value="${pUser.userID}">${pUser.userName}</option>--%>
                                                    <%--&lt;%&ndash;</c:if>&ndash;%&gt;--%>
                                                    <option value="${chaoguan.userID}">${chaoguan.userName}</option>
                                                    <c:forEach items="${userList}" var="u">
                                                        <option value="${u.userID}">${u.userName}</option>
                                                    </c:forEach>
                                                </select>
                                            </li>
                                            <div style="clear:both;"></div>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="oprateBtns">
                            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="saveStuff" onclick="newStuff()" disabled>保存</button>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">教育背景</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="new_edu">
                                    <thead>
                                    <td width="15%">学校名称</td>
                                    <td width="15%">学习时间</td>
                                    <td width="15%">专业</td>
                                    <td width="15%">学历</td>
                                    <td width="20%">说明</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">工作经历</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="new_work">
                                    <thead>
                                    <td width="15%">公司名称</td>
                                    <td width="15%">工作时间</td>
                                    <td width="10%">薪资水平</td>
                                    <td width="10%">在职职位</td>
                                    <td width="10%">工作职责</td>
                                    <td width="20%">未继续工作的原因</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">家庭成员</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5" disabled>新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="new_family">
                                    <thead>
                                    <td width="15%">姓名</td>
                                    <td width="15%">性别</td>
                                    <td width="15%">年龄</td>
                                    <td width="15%">与本人关系</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">薪资情况</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="new_salary">
                                    <thead>
                                    <td width="15%">修改时间</td>
                                    <td width="15%">修改原因</td>
                                    <td width="15%">薪资</td>
                                    <td width="15%">变更人</td>
                                    <td width="15%">备注</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">奖惩情况</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="new_prize">
                                    <thead>
                                    <td width="15%">修改时间</td>
                                    <td width="15%">内容</td>
                                    <td width="15%">变更人</td>
                                    <td width="15%">备注</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div class="overflow">
                                <span class="small_ttl">评论情况</span>
                                <span class="ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-5">新增</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control" id="new_evalute">
                                    <thead>
                                    <td width="15%">修改时间</td>
                                    <td width="15%">内容</td>
                                    <td width="15%">变更人</td>
                                    <td width="15%">备注</td>
                                    <td width="20%">操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/jquery.validate.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/messages_zh.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/general/addUser.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
