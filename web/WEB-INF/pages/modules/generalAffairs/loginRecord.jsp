<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/loginrecords/loginrecord.css?v=SVN_REVISION" />
<link rel="stylesheet" href="../css/typicons.min.css?v=SVN_REVISION">
<style>
    .laydate_body .laydate_y .laydate_yms{width:122px!important;}
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce">
    <div class="bonceContainer bounce" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_close()"></a>
        </div>
        <div class="bonceCon ">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-circle-5 ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>登录记录</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <div>
                    <div class="left">今天是 <span id="nowDate"></span></div>
                    <div class="ty-right flagTab">
                        <div class="loginQuery" style="position: relative;display: inline-block">
                            <span class="ty-btn ty-btn-big ty-circle-5 loginQuery" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" id="loginQueryBtn" value="4">自定义查询</span>
                            <ul class="dropdown-menu dropdown-menu-default searchCon"  >
                                <table class="ty-table">
                                    <tbody>
                                    <tr>
                                        <td>起时间：</td>
                                        <td><input type="text" class="ty-inputText" id="queryBeginTime"></td>
                                    </tr>
                                    <tr>
                                        <td>止时间：</td>
                                        <td><input type="text" class="ty-inputText" id="queryEndTime"></td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td align="right">
                                            <span class="ty-btn ty-btn-big ty-circle-5">取消</span>
                                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureLoginQuery()">查询</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </ul>
                        </div>
                        <div class="ty-btn-group" id="changeState">
                            <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-active-blue" value="1">本日</span>
                            <span class="ty-btn ty-btn-big ty-circle-5" value="2">本月</span>
                            <span class="ty-btn ty-btn-big ty-circle-5" value="3">本年</span>
                        </div>
                    </div>
                    <div class="ty-right goBack hd" >
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="goBack()">返回</button>
                    </div>
                    <div class="ty-right diyGoBack hd">
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="diyGoBack()">返回</button>
                    </div>
                    <div class="clr"></div>
                </div>
                <div class="ty-mainData">
                    <div>
                        <%--展示模块--%>
                        <div class="mainDataNav firstWrapper">
                            <div class="loginPersonInfo">
                                <div>
                                    <div class="LPI_time ty-inline-block"></div>
                                    <div class="LPI_name ty-inline-block"></div>
                                       <%-- <div class="ty-inline-block">
                                            <div class="LPI_post"></div>
                                            <div class="LPI_org"></div>
                                        </div>--%>
                                </div>

                                <div>
                                    <div class="total_log"></div>
                                    <div class="ttl_sign">登录总时长</div>
                                </div>
                            </div>
                            <div class="totalLogin">
                                <div class="ty-inline-block">
                                    <div class="loginSum"></div>
                                    <div class="loginSumDesc">登录总次数</div>
                                </div>
                            </div>

                            <div class="mobileLogin">
                                <i class="fa fa-mobile"></i>
                                <div class="ty-inline-block">
                                    <div class="mobileNum"></div>
                                    <div class="mobileNumDesc">手机端</div>
                                </div>

                            </div>
                            <div class="pcLogin">
                                <i class="fa fa-desktop"></i>
                                <div class="ty-inline-block">
                                    <div class="pcNum"></div>
                                    <div class="pcNumDesc">电脑端</div>
                                </div>

                            </div>
                        </div>
                        <%--主展示区域--%>
                        <div class="dataNav">
                            <%--时间姓名切换--%>
                            <div class="sortTab" style="margin-top: 10px">
                                <div class="ty-btn-group">
                                    <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-active-blue" value="1">按时间排列</span>
                                    <span class="ty-btn ty-btn-big ty-circle-5" value="2">按姓名排列</span>
                                </div>
                            </div>
                            <%--所有表格--%>
                            <div class="dataList">
                                <%-- 本日时间排序 --%>
                                <div id="dayInfo">
                                    <table class="ty-table ty-table-control">
                                        <thead><tr>
                                            <td width="15%">姓名</td>
                                            <td width="15%">部门名称</td>
                                            <td width="10%">职位</td>
                                            <td width="15%">登录时间</td>
                                            <td width="15%">登录时长</td>
                                            <td width="15%">登录端口</td>
                                            <td width="15%">登录地点</td>
                                        </tr></thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-dayInfo"></div>
                                </div>
                                <%-- 本日按姓名排列 --%>
                                <div id="dayName" class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead><tr>
                                            <td width="10%">姓名</td>
                                            <td width="10%">部门名称</td>
                                            <td width="10%">职位</td>
                                            <td width="10%">登录总时长</td>
                                            <td width="15%">最后登录时间</td>
                                            <td width="10%">登录总次数</td>
                                            <td width="10%">电脑登录总次数</td>
                                            <td width="10%">手机登录总次数</td>
                                            <td width="15%">更多</td>
                                        </tr></thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-dayName"></div>
                                </div>

                                <%-- 本月按时间排序 --%>
                                <div id="monthInfo" class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>登录日期</td>
                                            <td>登录人数</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-monthInfo"></div>
                                </div>
                                <%-- 本月按姓名排序 --%>
                                <div id="monthName" class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>姓名</td>
                                            <td>部门名称</td>
                                            <td>职位</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-monthName"></div>
                                </div>
                                <%-- 本月按姓名排序-查看详情 --%>
                                <div id="monthDetail"  class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>姓名</td>
                                            <td>部门名称</td>
                                            <td>职位</td>
                                            <td>登录日期</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-monthDetail"></div>
                                </div>

                                <%-- 本年按时间排序 --%>
                                <div id="yearInfo" class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>登录月份</td>
                                            <td>登录人数</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-yearInfo"></div>
                                </div>
                                <%-- 本年按姓名排序 --%>
                                <div id="yearName"  class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>姓名</td>
                                            <td>部门名称</td>
                                            <td>职位</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-yearName"></div>
                                </div>
                                <%-- 本年按时间排序-查看详情 --%>
                                <div id="yearInfoDetail"  class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>登录日期</td>
                                            <td>登录人数</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-yearInfoDetail"></div>
                                </div>
                                <%-- 本年按姓名排序-查看详情 --%>
                                <div id="yearDetail"  class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>姓名</td>
                                            <td>部门名称</td>
                                            <td>职位</td>
                                            <td>登录月份</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-yearDetail"></div>
                                </div>

                                <%-- 自定义时间排序 --%>
                                <div id="defineInfo"  class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td id="">登录年份</td>
                                            <td>登录人数</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody id="diySearch"></tbody>
                                    </table>
                                    <div id="ye-defineInfo"></div>
                                </div>
                                <%-- 自定义时间排序 - 查看详情 --%>
                                <div id="defineInfoDetail"  class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>登录月份</td>
                                            <td>登录人数</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-defineInfoDetail"></div>
                                </div>
                                <%-- 自定义姓名排序 --%>
                                <div id="defineName"  class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>姓名</td>
                                            <td>部门名称</td>
                                            <td>职位</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-defineName"></div>
                                </div>
                                <%-- 自定义按姓名排序-查看详情 --%>
                                <div id="defineDetail"  class="hd" >
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>姓名</td>
                                            <td>部门名称</td>
                                            <td>职位</td>
                                            <td>登录年份</td>
                                            <td>登录总时长</td>
                                            <td>登录总次数</td>
                                            <td>电脑端登录总次数</td>
                                            <td>手机端登录总次数</td>
                                            <td>更多</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="ye-defineDetail"></div>
                                </div>
                            </div>
                        </div>
                        <%--额外按姓名最后一级的显示区域--%>
                        <div class="moreList">
                            <div class="signTab" style="margin-top: 10px">
                                <div class="ty-btn-group">
                                    <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-active-blue" value="1">电脑端登录记录</span>
                                    <span class="ty-btn ty-btn-big ty-circle-5" value="2">手机端登录记录</span>
                                </div>
                            </div>
                            <div class="dataList">
                                <%--本日--%>
                                <div class="dataContainer">
                                    <%--按姓名电脑端登录记录--%>
                                    <div id="pcInfo">
                                        <table class="ty-table ty-table-control">
                                            <thead><tr>
                                                <td width="25%">登录日期</td>
                                                <td width="25%">登录时间</td>
                                                <td width="25%">登录时长</td>
                                                <td width="25%">登录地点</td>
                                            </tr></thead>
                                            <tbody></tbody>
                                        </table>
                                        <div id="ye-pcInfo"></div>
                                    </div>
                                    <%-- 按姓名手机端登录记录--%>
                                    <div id="mbInfo" style="display:none;">
                                        <table class="ty-table ty-table-control">
                                            <thead><tr>
                                                <td width="25%">登录日期</td>
                                                <td width="25%">登录时间</td>
                                                <td width="25%">登录时长</td>
                                                <td width="25%">登录地点</td>
                                            </tr></thead>
                                            <tbody></tbody>
                                        </table>
                                        <div id="ye-mbInfo"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<%--<script src="${pageContext.request.contextPath }/script/function.js"></script>--%>
<script src="../script/loginrecord/loginrecord.js?v=SVN_REVISION"></script>
</body>
</html>
