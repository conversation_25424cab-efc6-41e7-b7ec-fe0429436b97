<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/role/roleIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<link href="../css/general/companyMessage.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" >
<div class="bounce">
    <%--温馨提示-正常--%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 修改企业名称 --%>
    <div class="bonceContainer bounce-blue" id="changeBusinessName" style="width: 540px">
        <div class="bonceHead">
            <span>修改企业名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="changeNameContainer">
                    <div class="item">
                        <div class="item_head">机构名称</div>
                        <div class="item_content">
                            <!--<input type="text" name="fullName" placeholder="请输入" id="up_cusName">
                            <i class="fa fa-times clearVal" onmousedown="clearTxt($(this))"></i>-->
                        </div>
                    </div>
                    <div class="item">
                        <div class="item_head">机构简称</div>
                        <div class="item_content">
                            <!--<input type="text" name="name" placeholder="请输入" maxlength="6">
                            <i class="fa fa-times clearVal" onmousedown="clearTxt($(this))"></i>-->
                        </div>
                    </div>
                </div>
                <div class="ty-color-blue">注1：输入如为汉字，最多可输入六个。</div>
                <div class="ty-color-blue">注2：所有职工登录机构所见到均为此名称，修改时请慎重！</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce.cancel()">取 消</span>
            <button class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="sureChangeBusinessName()">确 定</button>
        </div>
    </div>
    <%--场地信息--%>
    <div class="bonceContainer bounce-blue" id="siteInformation" style="width: 800px;">
        <div class="bonceHead">
            <span id="siteInfoder">场地信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="siteBtnGroup">
                    <span class="funBtn ty-btn-blue nodeBtn jgone" data-fun="editSite">编辑当前场地信息</span><!--机构地址-->
                    <span class="funBtn ty-btn-blue nodeBtn qtone" data-fun="editSiteq">编辑该场地的当前信息</span><!--其它地址-->
                    <span class="funBtn ty-btn-blue nodeBtn jgtwo" data-fun="updateSite">更换为新场地</span><!--机构地址-->
                    <span class="funBtn ty-btn-blue nodeBtn qttwo" data-fun="updateSiteq">更换为新场地</span><!--其它地址-->
                    <span class="funBtn ty-btn-blue nodeBtn jgthree" data-fun="updateSiteLog">场地信息操作记录</span><!--机构地址-->
                    <span class="funBtn ty-btn-blue nodeBtn qtthree" data-fun="updateSiteLogq" onclick="updateSiteLogq($(this))">场地信息操作记录</span><!--其它地址-->
                </div>
                <div>
                    <span class="con-txt">当前的场地信息</span>
                    <table  class="ty-table tb-align-left" id="siteDetail">
                        <tbody>
                        <tr>
                            <td width="30%">具体地址</td>
                            <td width="70%"></td>
                        </tr>
                        <tr>
                            <td>场地情况</td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="siteInfoCare">
                        <span class="con-txt">点击页面上“编辑当前场地信息”，以录入当前场地的更多信息。</span>
                    </div>

                    <table  class="ty-table" id="siteTaxList">
                        <tbody>
                        <tr>
                            <td>应缴税种名称</td>
                            <td>应缴税款金额</td>
                            <td>应缴税款时间区间</td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="hydroGas">
                        <span class="con-txt">场地开始使用时水电煤气等的初始数据</span>
                        <table class="ty-table">
                            <tbody>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
            <div class="hd" id="siteStorage"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel()">关 闭</span>
        </div>
    </div>
    <%--机构名称的管理--%>
    <div class="bonceContainer bounce-blue" id="institmangent" style="width: 800px;">
        <div class="bonceHead">
            <span>机构名称的管理</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="border-bottom: 1px solid #c7c7c7;padding-bottom: 24px;width: 80%;margin: auto;padding-top: 24px;">
                <div class="ty-alert" style="justify-content: space-between;padding-bottom: 0;margin-bottom: 0;">
                    <p style="margin-bottom: 0;">机构名称或简称的修改，及修改记录的查看需在本页面进行！</p>
                    <span class="thin-btn" data-fun="updateNameLog">修改记录</span>
                </div>
                <div>
                    <div class="ty-color-blue">
                        <p style="margin-bottom: 0;">注1 机构简称最多可输入12个字符。</p>
                        <p>注2 所有职工登录机构均可见到机构简称，故修改时请慎重！</p>
                    </div>
                </div>
            </div>
            <div class="narrowBody">
                <div class="changeNameContainer">
                    <div class="item">
                        <div class="item_head">机构名称</div>
                        <div class="item_content">
                            <input type="text" name="fullName" placeholder="请输入" id="up_cusName" style="width: 540px;">
                            <i class="fa fa-times clearVal" onmousedown="clearTxt($(this))"></i>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item_head">机构简称</div>
                        <div class="item_content">
                            <input type="text" name="name" placeholder="请输入" maxlength="6" style="width: 540px;">
                            <i class="fa fa-times clearVal" onmousedown="clearTxt($(this))"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="sureChangeBusinessName1()">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-blue" id="editSite" style="width:1050px;">
        <div class="bonceHead bounce-blue">
            <span id="Leader">编辑当前场地信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modInput">
                <div id="jgby">
                    <span>请根据实际，录入本机构当前的地址等信息。</span>
                </div>
                <div id="qtby">
                    <span>
                        机构可能有类似为职工租赁的宿舍等其他地址。
                        <br />
                        本机构如有此类地址，可在此录入相关信息，以便管控。
                    </span>
                </div>
                <div class="modInfo clear part0">
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            具体地址
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="address" require/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            场地情况
                        </p>
                        <select class="ty-inputSelect" type="text" id="siteConditions" name="ownership" onchange="conditionsAssign($(this))" require>
                            <option value="">请选择</option>
                            <option value="1">公司自有</option>
                            <option value="2">租赁或其他</option>
                        </select>
                    </div>
                    <div class="modItem-m mediaBody">
                        <p class="modItemTtl">
                            场地照片(共可上传9张)
                            <span class="nodeBtn thin-btn ty-right" id="filePic"></span>
                        </p>
                        <div class="modPics">
                            <div class="file-box filePicBox clear" id="imageList"><span class="inTip">请上传</span></div>
                            <div class="clearPicsBtn" onclick="clearPicsBtn($(this))">清除</div>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <div class="ty-left">
                            <p class="modItemTtl">
                                总面积
                            </p>
                            <div class="input-group">
                                <input class="ty-inputText" value="" placeholder="请录入" name="totalArea" onkeyup="clearNoNumN(this, 2)" require/>
                                <span class="input-group-btn">
                                        <span class="unit-btn">平方米</span>
                                    </span>
                            </div>
                        </div>
                        <div class="ty-right">
                            <p class="modItemTtl">
                                建筑面积
                            </p>
                            <div class="input-group">
                                <input class="ty-inputText" value="" placeholder="请录入" name="buildingArea" onkeyup="clearNoNumN(this, 2)" require/>
                                <span class="input-group-btn">
                                        <span class="unit-btn" type="button">平方米</span>
                                    </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modInfo clear part part1">
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            租赁合同的扫描件或照片(共可上传9张)
                            <span class="nodeBtn thin-btn ty-right" id="filePic1"></span>
                        </p>
                        <div class="modPics">
                            <div class="file-box filePicBox clear" id="contractPic"><span class="inTip">请上传</span></div>
                            <div class="clearPicsBtn" onclick="clearPicsBtn($(this))">清除</div>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <div class="ty-left">
                            <p class="modItemTtl">
                                租赁合同可编辑版
                                <span class="nodeBtn thin-btn ty-right" id="filePic2"></span>
                            </p>
                            <div class="modPics">
                                <div class="file-box filePicBox doc-box clear" id="contractDoc"><span class="inTip">请上传</span></div>
                                <div class="clearPicsBtn" onclick="clearPicsBtn($(this))">清除</div>
                            </div>
                        </div>
                        <div class="ty-right">
                            <p class="modItemTtl">
                                场地押金
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="pledge" onkeyup="clearNoNumN(this, 2)" require/>
                                <i class="fa fa-times clearInputVal"></i>
                            </div>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <div class="ty-left">
                            <p class="modItemTtl">
                                合同开始日期
                            </p>
                            <div>
                                <input id="contractBeginTime" class="ty-inputText" value="" placeholder="请录入" name="beginTime" require/>
                                <i class="fa fa-times clearInputVal"></i>
                            </div>
                        </div>
                        <div class="ty-right">
                            <p class="modItemTtl">
                                合同结束日期
                            </p>
                            <div>
                                <input id="contractEndTime" class="ty-inputText" value="" placeholder="请录入" name="endTime" require/>
                                <i class="fa fa-times clearInputVal"></i>
                            </div>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <div class="ty-left">
                            <p class="modItemTtl">
                                每年租金
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="amount"  onkeyup="clearNoNumN(this, 2)" need require/>
                                <i class="fa fa-times clearInputVal"></i>
                            </div>
                        </div>
                        <div class="ty-right">
                            <p class="modItemTtl">
                                房东联系方式
                            </p>
                            <div>
                                <input class="ty-inputText" value="" placeholder="请录入" name="landlord" require/>
                                <i class="fa fa-times clearInputVal"></i>
                            </div>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            合同有效期内，租金需缴纳的次数与每次需缴纳的金额
                            <span class="thin-btn ty-right" data-fun="editRentPay">编辑</span>
                        </p>
                        <div class="pay-box clear" id="otherPayBox"></div>
                    </div>
                    <div class="modItem-m">
                        <div class="ty-left">
                            <p class="modItemTtl">
                                租金是否含税
                            </p>
                            <div>
                                <select class="ty-inputSelect" name="hasInvoice" require>
                                    <option value="">请选择</option>
                                    <option value="1">含税</option>
                                    <option value="0">不含税</option>
                                </select>
                            </div>
                        </div>
                        <div class="ty-right">
                            <p class="modItemTtl">
                                是否需额外缴纳税款
                            </p>
                            <div>
                                <select class="ty-inputSelect" name="hasTax" onchange="changeHasTax($(this))" require>
                                    <option value="">请选择</option>
                                    <option value="1">需要</option>
                                    <option value="0">不需要</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modInfo clear part part2">
                    <div class="modItem-l">
                        <p><span class="con-txt">请录入应缴税种的信息。</span></p>
                        <div>
                            <div class="ty-left">
                                <div class="con-txt careful">注1 应缴税款金额可录入比例，也可录入金额。如难以填写，可暂时不填。</div>
                                <div class="con-txt careful">注2 如需录入多个税种，可点击“增加税种”按钮。</div>
                            </div>
                            <span class="ty-right ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addTaxCategory()">增加税种</span>
                        </div>
                    </div>
                    <div class="categoryGroup">
                    </div>
                </div>
            </div>
            <div class="livingCost part part3">
                <div class="costs ty-left edit_hydroGas">
                    <p class="fill"><span class="con-txt">请录入场地开始使用时水电煤气的初始数据。</span></p>
                    <div class="modItem-flex">
                        <div class="modItem-s">
                            <p><span class="con-txt">水</span></p>
                            <input value="" placeholder="请录入初始数据" name="amount" onkeyup="clearNoNumN(this, 2)"  />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                        <div class="modItem-s">
                            <p><span class="con-txt">电</span></p>
                            <input value="" placeholder="请录入初始数据" name="amount" onkeyup="clearNoNumN(this, 2)"  />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                        <div class="modItem-s">
                            <p><span class="con-txt">煤气</span></p>
                            <input value="" placeholder="请录入初始数据" name="amount" onkeyup="clearNoNumN(this, 2)"  />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                    </div>
                </div>
                <div class="costs ty-left">
                    <p class="modItemTtl">
                        场地开始使用时，如还有其他项目的初始数据，也请录入。
                    </p>
                    <div class="modItem-flex gap">
                        <div class="modItem-s ty-left">
                            <input value="" placeholder="请录入项目名称" name="item" />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                        <div class="modItem-s ty-left">
                            <input value="" placeholder="请录入项目名称" name="item" />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                        <div class="modItem-s ty-left">
                            <input value="" placeholder="请录入项目名称" name="item" />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                    </div>
                    <div class="modItem-flex">
                        <div class="modItem-s ty-left">
                            <input value="" placeholder="请录入数据" name="amount" onkeyup="clearNoNumN(this, 2)" />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                        <div class="modItem-s ty-left">
                            <input value="" placeholder="请录入数据" name="amount" onkeyup="clearNoNumN(this, 2)" />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                        <div class="modItem-s ty-left">
                            <input value="" placeholder="请录入数据" name="amount" onkeyup="clearNoNumN(this, 2)" />
                            <i class="fa fa-times clearInputVal"></i>
                        </div>
                    </div>
                    <div class="con-txt careful">注1 请录入项目的名称及该项目的初始数据。</div>
                    <div class="con-txt careful">注2 系统仅支持录入三种自定义的项目。</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateSiteSure()" id="stituress">确 定</span><!--原始编辑和更换新地址-->
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addotheress(4)" id="otheraddresssure">确 定</span><!--其它地址新增-->
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addsysress(3)" id="sysaddresssure">确 定</span><!--机构地址新增-->
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="upotherSure(5)" id="othersure">确定</span><!--其它地址编辑-->
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="upnewotherSure(6)" id="othernewsure">确定</span><!--其它地址更换新地址-->
        </div>
    </div>
    <%--企业名称修改记录查看--%>
    <div class="bonceContainer bounce-blue" id="businessNameEditLogScan">
        <div class="bonceHead">
            <span>企业名称</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="formContainer">
                    <div class="item">
                        <div class="item_head">机构名称</div>
                        <div class="item_content">
                            <span class="log_fullName">机构名称11</span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item_head">机构简称</div>
                        <div class="item_content">
                            <span class="log_name">机构名称11</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">关 闭</span>
        </div>
    </div>
    <%-- 场地信息操作记录 --%>
    <div class="bonceContainer bounce-blue" id="siteEditLog" style="width: 761px;">
        <div class="bonceHead">
            <span>场地信息操作记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="padding:20px;">
                <p class="curSta"></p>
                <table class="ty-table">
                    <tbody>
                    <tr>
                        <td>资料状态</td>
                        <td>操 作</td>
                        <td>操作类别</td>
                        <td>创建人/修改人</td>
                    </tr>
                    </tbody>
                </table>
                <div id="ye_siteLog"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
        </div>
    </div>
    <%-- 机构名称修改记录 --%>
    <div class="bonceContainer bounce-blue" id="businessNameEditLog" style="width: 520px;">
        <div class="bonceHead">
            <span>机构名称修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="padding:20px;">
                <p class="curSta"></p>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td>资料状态</td>
                        <td>操 作</td>
                        <td>创建人/修改人</td>
                    </tr>
                    </tbody>
                </table>
                <div id="ye_log"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 编辑与缴纳租金有关的信息  --%>
    <div class="bonceContainer bounce-blue" id="editRentPay" style="width: 650px;">
        <div class="bonceHead">
            <span>编辑与缴纳租金有关的信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <p>系统支持录入租期内可能缴纳常见费用的金额与截止日期。</p>
            <p>租金等各均可按合同算出具体缴纳日期及金额后录入下表。</p>
            <p>每个截止日期的前10天，系统将给予提示。</p>
            <div class="moreLine">
                如需缴纳多次，请点击“增加行”。
                <span class="ty-btn bounce-ok ty-circle-3" onclick="addLine()">增加行</span>
            </div>
            <table class="ty-table ty-table-control" id="otherPay">
                <tbody>
                <tr>
                    <td>项目</td>
                    <td>金额</td>
                    <td>截止日期</td>
                    <td></td>
                </tr>
                <tr>
                    <td>
                        <select>
                            <option></option>
                            <option>场地租金</option>
                            <option>物业费</option>
                            <option>取暖费</option>
                            <option>需额外缴纳的车位费</option>
                        </select>
                    </td>
                    <td><input value="" type="text" onkeyup="clearNoNumN(this, 2)"/></td>
                    <td><input value="" type="text" class="rent_date"/></td>
                    <td><span class="delLine ty-color-red" onclick="delLine($(this))">删除本行</span></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取 消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="editRentPaySure(); ">确 定</span>
        </div>
    </div>
    <%--场地信息操作记录-查看--%>
    <div class="bonceContainer bounce-blue" id="updateSiteScan" style="width: 700px;">
        <div class="bonceHead">
            <span>场地信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div>
                    <span class="con-txt">当前的场地信息</span>
                    <table  class="ty-table tb-align-left" id="siteDetail_log">
                        <tbody>
                        <tr>
                            <td width="30%">具体地址</td>
                            <td width="70%"></td>
                        </tr>
                        <tr>
                            <td>场地情况</td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <table  class="ty-table" id="siteTaxList_log">
                        <tbody>
                        <tr>
                            <td>应缴税种名称</td>
                            <td>应缴税款金额</td>
                            <td>应缴税款时间区间</td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="hydroGas_log">
                        <span class="con-txt">场地开始使用时水电煤气等的初始数据</span>
                        <table class="ty-table">
                            <tbody>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed2.cancel()">关 闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%-- 需缴租金一览表  --%>
    <div class="bonceContainer bounce-blue" id="rentSchedule">
        <div class="bonceHead">
            <span>需缴租金一览表</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <table class="ty-table">
                <tr>
                    <td width="30%">项目</td>
                    <td width="30%">金额</td>
                    <td width="40%">截止日期</td>
                </tr>
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot"></div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>企业信息</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <!--<div class="companyDetails">
                    <h3 id="fullName" class="ty-color-orange"></h3>
                    <div class="panel-box">
                        <p>创建于<span id="comCreateDate"></span></p>
                        <div class="ty-alert">
                            <div class="main_item">机构简称<span class="con-txt" id="name"></span></div>
                            <div>
                                <span class="thin-btn" data-fun="updateName">修改企业名称</span>
                                <span class="thin-btn" data-fun="updateNameLog">企业名称修改记录</span>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="ty-alert">
                            <div class="com_address"><span>机构地址</span><span class="con-txt" id="address"></span></div>
                            <span class="thin-btn" data-fun="siteInfo">场地信息</span>
                        </div>
                    </div>
                </div>-->

                <div class="companyDetails" style="width: 89%;margin-left: 97px;">
                    <div class="panel-box" style="border-bottom: 1px solid #c7c7c7;">
                        <div class="ty-alert" style="margin: 20px 0;">
                            <div>
                                <h3 id="fullName" class="ty-color-orange" style="margin-top: 10px;margin-bottom: 5px;"></h3>
                                <div class="main_item">机构简称<span class="con-txt" id="name"></span></div>
                            </div>
                            <div>
                                <div class="ty-alert" style="padding-bottom: 0;">
                                    <span></span>
                                    <span class="thin-btn" data-fun="setmamng">管理</span>
                                </div>
                                <p>创建于<span id="comCreateDate"></span></p>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box" style="border-top: none;padding-top: 0;">
                        <div class="ty-alert">
                            <div class="com_address">机构地址</div>
                            <div>
                                <span class="thin-btn" data-fun="" id="stopaddre">已停用的地址</span>
                                <span class="thin-btn" data-fun="addotheraddress" id="fixothermal" style="margin-right: 0;">创建其他地址</span>
                                <div class="hd"></div>
                            </div>
                        </div>
                        <div>
                            <table class="ty-table ty-table-control" id="addres">
                                <thead style="background: none;">
                                <td>用途</td>
                                <td>具体地址</td>
                                <td>场地情况</td>
                                <td>租赁合同到期日</td>
                                <td>应交税种</td>
                                <td>创建</td>
                                <td>操作</td>
                                </thead>
                                <tbody class="sale_Tbody" id="address_body"></tbody>
                            </table>
<%--                            <div id="ye_companymessage"></div>--%>
                        </div>
                        <div class="clr"></div>
                    </div>
                    <!--<h3 id="fullName" class="ty-color-orange"></h3>
                    <div class="panel-box">
                        <p>创建于<span id="comCreateDate"></span></p>
                        <div class="ty-alert">
                            <div class="main_item">机构简称<span class="con-txt" id="name"></span></div>
                            <div>
                                <span class="thin-btn" data-fun="updateName">修改企业名称</span>
                                <span class="thin-btn" data-fun="updateNameLog">企业名称修改记录</span>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="ty-alert">
                            <div class="com_address">需要时，请创建地址</div>
                            <div>
                                <span class="thin-btn" data-fun="addotheraddress" id="fixothermal">创建其他地址</span>
                                <span class="thin-btn" data-fun="addsyress" id="fixemal" style="margin-right: 0px;" >创建机构地址</span>
                                <div class="hd"></div>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box" id="otherbox">
                        <%--                        <div class="ty-alert">--%>
                        <%--                            <div class="com_address"><span>机构地址</span><span class="con-txt" id="address"></span></div>--%>
                        <%--                            <div>--%>
                        <%--                                <span class="thin-btn" data-fun="siteInfo" style="display: block;margin-right: 0px;text-align: right;">管理</span>--%>
                        <%--                                <div id="tidme">--%>
                        <%--                                </div>--%>
                        <%--                            </div>--%>
                        <%--                        </div>--%>
                        <%--                        <div class="ty-alert" style="display: flex">--%>
                        <%--                            <div class="com_address"><span>其它地址</span><span class="con-txt" id="addressoter"></span> </div>--%>
                        <%--                            <span class="thin-btn" data-fun="siteInfo1">管理</span>--%>
                        <%--                        </div>--%>
                    </div>-->
                </div>
                <div id="inforStorage" class="hd"></div>
            </div>

        </div>

    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/role/companyMessage.js?v=SVN_REVISION"></script>
</body>
</html>
