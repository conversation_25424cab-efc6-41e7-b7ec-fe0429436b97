<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2016/8/24
  Time: 9:42
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
    <title>评价情况</title>
    <link href="../assets/global/plugins/bootstrap/css/bootstrap.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../assets/global/css/components.min.css?v=SVN_REVISION" rel="stylesheet" id="style_components" type="text/css" />

    <script src="../assets/global/plugins/bootstrap/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/js.cookie.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/jquery.blockui.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>

    <script src="../My97DatePicker/WdatePicker.js?v=SVN_REVISION"></script>
    <script src="../script/function.js?v=SVN_REVISION"></script>


</head>
<body>

<form action="../general/addOrUpdatePersonalAssessment.do" method="post">
    <input type="hidden" name="id" value="${personalAssessment.id}"/>
    <input type="hidden" name="userId" value="${userId}"/>

    <div class="row" >
        <div style="width: 1000px" class="position">
            <!-- BEGIN SAMPLE TABLE PORTLET-->

            <%--<div class="portlet box green">--%>
            <div class="portlet">
                <div class="portlet-title">
                    <div class="caption" style="margin-left: 350px">
                        <h2>编辑评价情况</h2>
                    </div>
                </div>
                <div class="portlet-body">
                    </br>
                    <div class="form-horizontal">
                        <%--<div class="form-group">--%>
                        <%--<label class="col-sm-3 control-label">修改时间：</label>--%>
                        <%--<div class="col-sm-6">--%>
                        <%--<input type="text" class="form-control" id="firstname" value="${personnelSalaryLog.operateTime}"--%>
                        <%--placeholder="" name="operateTime" onclick="WdatePicker();"/>--%>
                        <%--</div>--%>
                        <%--</div>--%>

                        <div class="form-group">
                            <label  class="col-sm-3 control-label">内容：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control"  value="${personalAssessment.content}"
                                       placeholder="" name="content"/>
                            </div>
                        </div>

                        <%--<div class="form-group">--%>
                        <%--<label class="col-sm-3 control-label">变更人：</label>--%>
                        <%--<div class="col-sm-6">--%>
                        <%--<input type="text" class="form-control" id="" value="${personnelSalaryLog.operatorName}"--%>
                        <%--placeholder="" name="operatorName" />--%>
                        <%--</div>--%>
                        <%--</div>--%>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">备注：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control"  value="${personalAssessment.memo}"
                                       placeholder="" name="memo" />
                            </div>
                        </div>

                    </div>
                </div>
                <div align="center">
                    <input type="submit" class="btn green" style="width:125px"
                           value="保存"/>
                    <input type="button" class="btn green" style="width:125px"
                           value="取消" onclick="refreshOpener();"/>
                </div>
            </div>
        </div>
    </div>
</form>

</body>
</html>
