<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/general/zw.css?v=SVN_REVISION">
<link rel="stylesheet" type="text/css" href="../css/user/handleEntry.css?v=SVN_REVISION">
<link rel="stylesheet" type="text/css" href="../css/general/recruit.css?v=SVN_REVISION" />

</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="alertMS">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="altMS" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-header">
                <ul>
                    <li class="active">
                        <span>职工档案</span>
                        <i class="close_x"></i>
                    </li>
                </ul>
            </div>
            <div class="ty-container">
                <span class="goBack ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="historyBack();">返回</span>
                <div class="employeeView">
                    <div class="sect">
                        <div>
                            <span class="small_ttl">基本信息</span>
                        </div>
                        <div class="con_part overflow">
                            <div class="masterInfo ty-left">
                                <ul class="overflow">
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">姓名：</span> <span>${user.userName}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">姓别：</span>
                                        <span>
                                                <c:choose>
                                                    <c:when test="${user.gender=='0'  }" >女</c:when>
                                                    <c:when test="${user.gender=='1'  }" >男</c:when>
                                                    <c:otherwise>  尚不明确 </c:otherwise>
                                                </c:choose>
                                                </span>
                                    </li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">出生年月：</span> <span>${fn:substring(user.birthday, 0, 10)}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">最高学历：</span> <span>
                                            <c:choose>
                                                <c:when test="${user.degree=='1'}">研究生</c:when>
                                                <c:when test="${user.degree=='2'}">本科</c:when>
                                                <c:when test="${user.degree=='3'}">大专</c:when>
                                                <c:when test="${user.degree=='4'}">中专或高中</c:when>
                                                <c:when test="${user.degree=='5'}">其它</c:when>
                                                <c:otherwise> </c:otherwise>
                                            </c:choose>
                                        </span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">婚姻状况：</span> <span>
                                                <c:choose>
                                                    <c:when test="${user.marry=='0' || user.marry=='已婚'}" >已婚</c:when>
                                                    <c:when test="${user.marry=='1' || user.marry=='未婚'}" >未婚</c:when>
                                                    <c:otherwise>尚不明确</c:otherwise>
                                                </c:choose>
                                            </span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">民族：</span> <span>${user.nation}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">政治面貌：</span> <span>${user.politicalStatus}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">籍贯：</span> <span>${user.nativePlace}</span></li>
                                    <li class="col-xs-6 col-sm-8"><span class="us_field">特长：</span> <span>${user.interesting}</span></li>
                                    <li class="col-xs-6 col-sm-8"><span class="us_field">爱好：</span> <span>${user.speciality}</span></li>
                                </ul>
                                <ul class="overflow">
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">身份证号：</span> <span>${user.idCard}</span></li>
                                    <li class="col-xs-6 col-sm-8"><span class="us_field">家庭住址：</span> <span>${user.homeAddress}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">联系电话：</span> <span>${user.mobile}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">E-mail：</span> <span>${user.email}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">QQ:</span> <span>${user.qq}</span></li>
                                </ul>
                            </div>
                            <div class="ty-left">
                                <div class="viewerTx">
                                    <img src="../${user.imgPath}" onerror="this.src='../assets/oralResource/user.png'" alt="用户头像" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sect">
                        <div>
                            <span class="small_ttl">岗位信息</span>
                        </div>
                        <div class="con_part">
                            <div class="masterInfo">
                                <ul class="overflow">
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">入职时间：</span> <span>${fn:substring(user.onDutyDate, 0, 10)}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">部门：</span> <span>${user.departName}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">职位：</span> <span>${user.postName}</span></li>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">是否为普通员工：</span> <span><c:if test="${user.ordinaryEmployees==1}">是</c:if><c:if test="${user.ordinaryEmployees!=1}">否</c:if></span></li>
                                    <c:choose>
                                        <c:when test="${user.roleCode=='staff'}">
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">所属高管：</span> <span>${user.manageName}</span></li>
                                        </c:when>
                                        <c:otherwise>
                                            <li class="col-xs-6 col-sm-4"><span class="us_field">所属高管：</span> <span>--</span></li>
                                        </c:otherwise>
                                    </c:choose>
                                    <li class="col-xs-6 col-sm-4"><span class="us_field">直接上级：</span> <span>${user.leaderName}</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="sect">
                        <div>
                            <span class="small_ttl">教育背景</span>
                        </div>
                        <div class="con_part">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="15%">学校名称</td>
                                <td width="15%">学习时间</td>
                                <td width="15%">专业</td>
                                <td width="15%">学历</td>
                                <td width="20%">说明</td>
                                </thead>
                                <tbody>
                                <c:forEach items="${personalEducations}" var="pe">
                                    <tr>
                                        <td>${pe.collegeName}</td>
                                        <td><%--${pe.beginTime}--%>${fn:substring(pe.beginTime, 0, 10)} ~ <%--${pe.endTime}--%>${fn:substring(pe.endTime, 0, 10)}</td>
                                        <td>${pe.major}</td>
                                        <td>${pe.degree}</td>
                                        <td>${pe.memo}</td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="sect">
                        <div>
                            <span class="small_ttl">工作经历</span>
                        </div>
                        <div class="con_part">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="10%">公司名称</td>
                                <td width="15%">工作时间</td>
                                <td width="15%">薪资水平</td>
                                <td width="15%">在职职位</td>
                                <td width="10%">工作职责</td>
                                <td width="15%">未继续工作的原因</td>
                                </thead>
                                <tbody>
                                <c:forEach items="${personnelOccupations}" var="po">
                                    <tr>
                                        <td>${po.corpName}</td>
                                        <td> ${fn:substring(po.beginTime, 0, 10)} ~ ${fn:substring(po.endTime, 0, 10)}</td>
                                        <td>${po.salary}</td>
                                        <td>${po.post}</td>
                                        <td>${po.operatingDuty}</td>
                                        <td>${po.memo}</td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="sect">
                        <div>
                            <span class="small_ttl">家庭成员</span>
                        </div>
                        <div class="con_part">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="15%">姓名</td>
                                <td width="15%">性别</td>
                                <td width="15%">年龄</td>
                                <td width="15%">与本人关系</td>
                                </thead>
                                <tbody>
                                <c:forEach items="${user.personnelFolksHashSet}" var="pf">
                                    <tr>
                                        <td>${pf.name}</td>
                                        <td>${pf.gender}</td>
                                        <td>${pf.age}</td>
                                        <td>${pf.relation}</td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="sect">
                        <div>
                            <span class="small_ttl">薪资情况</span>
                        </div>
                        <div class="con_part">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="15%">修改时间</td>
                                <td width="15%">修改原因</td>
                                <td width="15%">薪资</td>
                                <td width="15%">变更人</td>
                                <td width="15%">备注</td>
                                </thead>
                                <tbody>
                                <c:forEach items="${personnelSalaryLogs}" var="ps">
                                    <tr>
                                        <td>${ps.operateTime}</td>
                                        <td>${ps.admustResaon}</td>
                                        <td>${ps.salary}</td>
                                        <td>${ps.operatorName}</td>
                                        <td>${ps.memo}</td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="sect">
                        <div>
                            <span class="small_ttl">奖惩情况</span>
                        </div>
                        <div class="con_part">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="15%">修改时间</td>
                                <td width="15%">内容</td>
                                <td width="15%">变更人</td>
                                <td width="15%">备注</td>
                                </thead>
                                <tbody>
                                <c:forEach items="${personalRewardPunishments}" var="pr">
                                    <tr>
                                        <td>${pr.occurDate}</td>
                                        <td>${pr.content}</td>
                                        <td>${pr.operatorName}</td>
                                        <td>${pr.memo}</td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="sect">
                        <div>
                            <span class="small_ttl">评论情况</span>
                        </div>
                        <div class="con_part">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td width="15%">修改时间</td>
                                <td width="15%">内容</td>
                                <td width="15%">变更人</td>
                                <td width="15%">备注</td>
                                </thead>
                                <tbody>
                                <c:forEach items="${personalAssessments}" var="pa">
                                    <tr>
                                        <td>${pa.assessDate}</td>
                                        <td>${pa.content}</td>
                                        <td>${pa.assessUserName}</td>
                                        <td>${pa.memo}</td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/function.js?v=SVN_REVISION"></script>
<script type="text/javascript">
    //creator:李玉婷 date:2017/12/25 返回到职工档案-员工列表
    function historyBack(){
        //保存筛选条件
       var screenCondition = getUrlParam("condition");
       location.href='../general/employeeIndex.do?condition='+screenCondition;
    }
</script>

</body>


</html>
