<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/attendance/attendance.css?v=SVN_REVISION" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed2">
    <%-- updator: 侯杏哲 2018-4-28  考勤设置 选择部门 --%>
    <div class="bonceContainer bounce-green" id="scanSet">
        <div class="bonceHead">
            <span>请选择使用部门</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel() "></a>
        </div>
        <div class="bonceCon">
            <div class="departTree">
                <ul class="ty-left" id="all" >
                    <li>
                        <div lass="departid" info="all">
                            <i class="fa fa-angle-right"></i> <span>全部</span>
                            <i class="fa fa-plus-square plusAll" onclick="plus($(this), event)"></i>
                        </div>
                        <ul id="allRight"></ul>
                    </li>
                </ul>
                <div class="ty-left arrow"><i class="fa fa-arrows-h"></i></div>
                <form class="ty-left" id="deparCon">
                    <input type="hidden" name="categoryId" id="categoryId">
                    <ul id="nowRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel() ">取消</span>
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-2" id="selectDepartOk" >确定</span>
        </div>
    </div>
        <%--creator:lyt 2023/11/13 模式更换记录详情--%>
        <div class="bonceContainer bounce-blue" id="modeChangeRecordDetails" style="width:800px;">
            <div class="bonceHead">
                <span>详情查看</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
               <div class="wrapperCon">
                   <p>正常班的上下班时间</p>
                   <table class="ty-table" id="restSetList">
                       <tbody>
                       <tr>
                           <td>部门</td>
                           <td>上下班时间</td>
                           <td>午休时间</td>
                           <td>午休前后是否考勤</td>
                       </tr>
                       </tbody>
                   </table>
                   <div class="byDevicesInfo">
                       <p>迟到、早退与旷工</p>
                       <div>上班时间到后<span class="rd_lateLimit"></span>分钟的到岗算作迟到，更晚者为旷工；下班时间前<span class="rd_earlyLimit"></span>分钟内的离岗算作早退，更早者为旷工。</div>
                       <div>请假情况下的到岗离岗使用考勤宝考核，过早的离岗为早退或旷工，过晚的到岗为迟到或旷工，具体的计算规则与上下班的均相同。</div>
                       <div class="gapTp">
                           <p>更换模式前使用中的考勤设备</p>
                           <table class="ty-table" id="devicesList">
                               <tbody>
                               <tr>
                                   <td>考勤设备编号</td>
                                   <td>品牌/型号</td>
                                   <td>唯一标识</td>
                                   <td>设备的启用</td>
                               </tr>
                               </tbody>
                           </table>
                       </div>
                   </div>
               </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed2.cancel() ">关  闭</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="tipSure">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="tipRepeat">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">您提交的修改还未审批，请审批后再操作</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
        <%-- updater : hxz 2018-04-27  提示 其他部门未设置， 是否设置 --%>
        <div class="bonceContainer bounce-green" id="setAnotherTip">
            <div class="bonceHead">
                <span>提示</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon" style="text-align: center">
                <div class="AnotherTip">还有部门尚未设置考勤时间</div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="setAnother(0)">去设置</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="notSaveSetting()">暂不设置</span>
            </div>
        </div>
    <%-- updater : hxz 2018-04-27  作息时间 设置 --%>
    <div class="bonceContainer bounce-blue" id="attenceOtherSetting" style="width:600px;">
        <div class="bonceHead">
            <span>其他考勤设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="firstShow">请确定开始使用本系统记录考勤的日期<input type="text" class="" id="roleStartDate1" /></p>
            <p class="firstShow">请确每日考勤录入时间
                <select id="attendanceTime1">
                    <option value="07:30">07:30</option>
                    <option selected value="08:00">08:00</option>
                    <option value="08:30">08:30</option>
                    <option value="09:00">09:00</option>
                    <option value="09:30">09:30</option>
                    <option value="10:00">10:00</option>
                    <option value="10:30">10:30</option>
                    <option value="11:00">11:00</option>
                </select>
            </p>
            <p class="atTip firstShow">如考勤在此之前未录入，则所有职工将被记为<span>旷工</span>。您需要逐条去修改。</p>
            <p>请根据您的实际情况，重新勾选确定本月及下月的作息时间。</p>
            <div class="">
                <ul class="ty-secondTab monthsNav" id="months">
                    <li>2018年11月</li>
                    <li>2018年12月</li>
                </ul>
                <div id="clendars" class="clendars"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
        </div>
    </div>
    <%--  creater 姚宗涛 2018/2/9  08:58:00  修改考勤  查看加班记录  详情 查看按钮 弹框--%>
    <div class="bonceContainer bounce-green" id="fix_overtimeDetails">
        <div class="bonceHead">
            <span>加班记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
        </div>
    </div>
        <%-- creater 姚宗涛 2018/2/8 16:35:09 修改考勤 查看请假 查看按钮弹框 --%>
        <div class="bonceContainer bounce-green" id="details">
            <div class="bonceHead">
                <span>请假记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="clearFlt">
                    <dl>
                        <dd><span class="leaveName">王金锁</span> <span class="leaveTime">2017年10月10日</span>请假情况 </dd>
                    </dl>
                </div>
                <div id="seeleaveDetail">
                </div>
            </div>
            <div class="bonceFoot"></div>
        </div>
    <div class="bonceContainer bounce-blue" id="leaveRecord">
        <div class="bonceHead">
            <span class="bounce_title">请假记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="detail"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- creater 张旭博 2020/11/13 16:35:09 修改考勤 弹窗--%>
    <div class="bonceContainer bounce-blue" id="updateAttend" style="width: 600px">
        <div class="bonceHead">
            <span class="bounce_title">修改考勤</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">
                下表中带 <span class="ty-color-red">*</span> 的必须填写，且考勤修改后需经审批后方才生效。
            </div>
            <div class="mainChange">
                <div class="bounceItem">
                    <div class="bounceItem_title">修改理由 <span class="ty-color-red">*</span></div>
                    <div class="bounceItem_content">
                        <input class="kj-input" type="text" name="updateDesc" placeholder="所填写的理由最多不得超过30字" require onkeyup="countWords($(this),30)" style="width: 100%">
                        <div class="textMax text-right" max="30"></div>
                    </div>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">上班考勤 <span class="ty-color-red">*</span></div>
                    <div class="bounceItem_content">
                        <div class="ty-radio">
                            <input type="radio" name="upIsNormal" id="up_normal" value="1">
                            <label for="up_normal"></label> 正常
                        </div>
                        <div class="ty-radio">
                            <input type="radio" name="upIsNormal" id="up_late" value="0">
                            <label for="up_late"></label> 迟到
                        </div>
                    </div>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">下班考勤 <span class="ty-color-red">*</span></div>
                    <div class="bounceItem_content">
                        <div class="ty-radio">
                            <input type="radio" name="downIsNormal" id="down_normal" value="1">
                            <label for="down_normal"></label> 正常
                        </div>
                        <div class="ty-radio">
                            <input type="radio" name="downIsNormal" id="down_early" value="0">
                            <label for="down_early"></label> 早退
                        </div>
                    </div>
                </div>
                <div class="bounceItem">
                    <div class="bounceItem_title">其他</div>
                    <div class="bounceItem_content otherInput">
                        <div class="ty-checkbox">
                            <input type="checkbox" name="isLeave" id="has_leave" value="0">
                            <label for="has_leave"></label> 有请假
                        </div>
                        <div class="ty-checkbox">
                            <input type="checkbox" name="isOverTime" id="has_overtime" value="1">
                            <label for="has_overtime"></label> 有加班
                        </div>
                        <div class="ty-checkbox">
                            <input type="checkbox" name="isAbsenteeism" id="has_out" value="2">
                            <label for="has_out"></label> 有旷工
                        </div>
                    </div>
                </div>
            </div>
            <div class="otherChange">
                <div class="kj-panel leavePart" style="display:none;">
                    <div class="ty-alert">
                        请录入请假信息
                        <div class="btn-group text-right">
                            <span class="ty-btn ty-btn-green ty-circle-2" onclick="addNewInput($(this))">增加新的请假信息</span>
                        </div>
                    </div>
                    <div class="input_model" >
                        <div class="inputInfo_item">
                            <div class="text-right inputInfo_item_del">
                                <span class="ty-btn ty-btn-red ty-circle-2" title="删除此条请假信息" onclick="delInput($(this))">删除此条请假信息</span>
                            </div>
                            <div class="inputInfo_item_content">
                                <div class="bounceItem">
                                    <div class="bounceItem_title">请假类型 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select" name="leaveType" require>
                                            <option value="">---请选择---</option>
                                            <option value="1">事假</option>
                                            <option value="2">病假</option>
                                            <option value="3">年假</option>
                                            <option value="4">调休</option>
                                            <option value="5">婚假</option>
                                            <option value="6">产假</option>
                                            <option value="7">陪产假</option>
                                            <option value="8">路途假</option>
                                            <option value="9">其他</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="bounceItem">
                                    <div class="bounceItem_title">开始时间 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select halfTime" name="leaveBeginDate" require></select>
                                    </div>
                                    <div class="bounceItem_title">结束时间 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select halfTime" name="leaveEndDate" require></select>
                                    </div>
                                </div>
                                <div class="bounceItem">
                                    <div class="bounceItem_title">请假说明</div>
                                    <div class="bounceItem_content">
                                        <input class="kj-input" type="text" placeholder="如无可忽略。录入内容最多不得超过30字" name="leaveReason" onkeyup="countWords($(this),30)" style="width: 100%">
                                        <div class="textMax text-right" max="30"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inputInfo_avatar"></div>
                </div>
                <div class="kj-panel overTimePart" style="display: none;">
                    <div class="ty-alert">
                        请录入加班信息
                        <div class="btn-group text-right">
                            <span class="ty-btn ty-btn-green ty-circle-2" onclick="addNewInput($(this))">增加新的加班信息</span>
                        </div>
                    </div>
                    <div class="input_model">
                        <div class="inputInfo_item">
                            <div class="text-right inputInfo_item_del">
                                <span class="ty-btn ty-btn-red ty-circle-2" title="删除此条加班信息" onclick="delInput($(this))">删除此条加班信息</span>
                            </div>
                            <div class="inputInfo_item_content">
                                <div class="bounceItem">
                                    <div class="bounceItem_title">加班时长 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <input class="kj-input" name="overDuration" onkeyup="clearNoNum(this)" require/> h
                                    </div>
                                </div>
                                <div class="bounceItem">
                                    <div class="bounceItem_title">加班事由</div>
                                    <div class="bounceItem_content">
                                        <input class="kj-input" type="text"  name="overReason" placeholder="如无可忽略。录入内容最多不得超过30字" onkeyup="countWords($(this),30)" style="width: 100%"/>
                                        <div class="textMax text-right" max="30"></div>
                                    </div>
                                </div>
                                <div class="bounceItem">
                                    <div class="bounceItem_title">备注</div>
                                    <div class="bounceItem_content">
                                        <input class="kj-input" type="text"  name="overMemo" placeholder="如无可忽略。录入内容最多不得超过30字" onkeyup="countWords($(this),30)" style="width: 100%">
                                        <div class="textMax text-right" max="30"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inputInfo_avatar"></div>
                </div>
                <div class="kj-panel outPart" style="display: none">
                    <div class="ty-alert">
                        请录入旷工信息
                        <div class="btn-group text-right">
                            <span class="ty-btn ty-btn-green ty-circle-2" onclick="addNewInput($(this))">增加新的旷工信息</span>
                        </div>
                    </div>
                    <div class="input_model">
                        <div class="inputInfo_item">
                            <div class="text-right inputInfo_item_del">
                                <span class="ty-btn ty-btn-red ty-circle-2" title="删除此条旷工信息" onclick="delInput($(this))">删除此条旷工信息</span>
                            </div>
                            <div class="inputInfo_item_content">
                                <div class="bounceItem">
                                    <div class="bounceItem_title">开始时间 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select halfTime" name="aBeginDate" require></select>
                                    </div>
                                    <div class="bounceItem_title">结束时间 <span class="ty-color-red">*</span></div>
                                    <div class="bounceItem_content">
                                        <select class="kj-select halfTime" name="aEndDate" require></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inputInfo_avatar"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot cent">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="sureChangeAttendance()">提交</button>
        </div>
    </div>
        <%--creator:lyt 2018/3/21 考勤设置 考勤时间设置--%>
        <div class="bonceContainer bounce-blue" id="initeSetting" style="width:600px;">
            <div class="bonceHead">
                <span>设置正常班的上下班时间</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon" id="roles">
                <div class="clear clearfix busyTimeSetting" atID=''>
                    <div class="dutyItem">
                        <div class="dutyItemTitle">应上班时间 <i class="xing"></i></div>
                        <select class="workBeginTime"></select>
                    </div>
                    <div class="dutyItem">
                        <div class="dutyItemTitle">应下班时间 <i class="xing"></i></div>
                        <select class="workEndTime"></select>
                    </div>
                    <div class="dutyItem dutyItemLong">
                        <div class="dutyItemTitle ty-left">午休前后是否考勤 <i class="xing"></i></div>
                        <div class="dutyItemTitle ty-right isSetNoon">
                            <span class="fa fa-circle-o" lang="1" onclick="setNoonToggle($(this))"></span>是
                            <span class="fa fa-circle" lang="0" onclick="setNoonToggle($(this))"></span>否
                        </div>
                    </div>
                    <div class="dutyItem dutyItemLong noonTime">
                        <div class="dutyItemTitle">午休时间<i class="xing isNeed"></i></div>
                        <select class="noonBeginTime" id="noonBeginTime"></select>
                        <select class="noonEndTime ty-right" id="noonEndTime"></select>
                    </div>
                    <div class="dutyItem dutyItemLong">
                        <div class="dutyItemTitle">适用部门 <i class="xing"></i></div>
                        <div class="department overflow">
                            <div class="departSelected" id="departSelected"> </div>
                            <div class="addDepart" onclick="addDepart($(this))"><i class="fa fa-bars"></i> </div>
                        </div>
                    </div>
                    <div class="dutyItem dutyItemLong" id="updateAT">
                        <div class="dutyItemTitle">本次设置的生效日期 <i class="xing"></i></div>
                        <input type="text" id="editOprate"/>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <p>
                    <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
                    <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-2" id="attendanceSure" >确定</span>
                </p>
            </div>
        </div>
        <%-- creator:lyt 2023-09-27  作息时间 设置 --%>
        <div class="bonceContainer bounce-blue" id="workRestSetting" style="width:700px;">
            <div class="bonceHead">
                <span>作息日期编辑</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div>
                    <div class="weekdayDefault">
                        <div>以下为<span class="yearTab"></span>默认的作息日期。请确认如何执行。</div>
                        <div class="blueCare">注：每月25日起，系统将给予关于需确认下个月作息日期的系统消息！</div>
                    </div>
                    <div class="weekdayEdit">
                        <div>哪些“班”需变为“休”？哪些“休”需变为“班”？请点击！</div>
                        <div class="blueCare">功能举例：某日期如为“班”，点击后即变为“休”！</div>
                    </div>
                </div>
                <div>
                    <ul class="ty-secondTab monthsNav" id="monthsNav" data-time="1">
                        <li>2018年11月</li>
                    </ul>
                    <div id="clendarsTab" class="clendars"></div>
                    <div class="weekdayCheckRadio">
                        <div class="weekdayCheck">
                            <div><span class="fa fa-circle-o" data-icon="1"></span>使用默认的作息日期</div>
                            <div><span class="fa fa-circle-o" data-icon="2"></span>需对默认的作息日期进行编辑，进入编辑状态</div>
                        </div>
                        <div class="weekdayEdit"><span class="fa fa-circle-o" data-icon="3"></span>编辑完成</div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="dateSetSure()">确定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-blue" id="clockFunction" style="width:800px;">
            <div class="bonceHead">
                <span>考勤宝功能说明</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="ckWrap">
                    <p>选择使用考勤宝后，完成“考勤设置”后，还需若干台安卓手机当作考勤设备。</p>
                    <p>1 考勤设备的准备</p>
                    <p>1.1 请根据实际情况，决定使用几台考勤设备。</p>
                    <p>1.2 考勤设备需为安卓手机，需能连接网络，并需能下载APP。</p>
                    <p>2 考勤设备的使用</p>
                    <p>2.1 在考勤设备上，需先下载并安装wonderss APP，登录总务的号码。</p>
                    <p>2.2 在wonderss-工作-考勤设备中下载并安装考勤宝，之后使用总务号码登录。</p>
                    <p>2.3 在考勤宝中点击“启用”。</p>
                    <p>2.4 对每台要用作考勤的手机均操作上述2.1-2.3过程，直至全部完成。</p>
                    <p>3 后续任务</p>
                    <p>3.1 保持各台考勤设备有电、有网，放在职工将要打卡的地方，打开“考勤宝”APP。</p>
                    <p>3.2 参与考勤的职工需按规定打卡，具体为在wonderss-工作-考勤设备中选择要进行的是上班打卡还是其他，选择后将所显示的二维码置于考勤设备前扫描打卡即可。</p>
                    <p>3.3 公司如要求职工的打卡需带有实时照片，则需告知打卡者在打卡时多停留点时间，以便考勤设备拍摄。</p>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关 闭</span>
            </div>
        </div>
        <div class="bonceContainer bounce-blue" id="modeChangeRecord" style="width:800px;">
            <div class="bonceHead">
                <span>模式更换记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <table class="ty-table" id="modeRecordList">
                    <tbody>
                    <tr>
                        <td>考勤数据进入系统的模式</td>
                        <td>该模式的创建</td>
                        <td>该模式的启用日期</td>
                        <td>该模式被更换的时间</td>
                        <td>操作</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed.cancel()">关闭</span>
            </div>
        </div>
        <%--creator:lyt 2018/4/13 考勤设置 修改详情--%>
        <div class="bonceContainer bounce-blue" id="editRecordDetails" style="width:800px;">
            <div class="bonceHead">
                <span>修改详情：</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="recordDetailsCon">
                    <div class="detailsHead">
                        <span class="title_dl">修改时间</span><span class="infoEditDate title_dl" id="infoEditDate">2018/1/20 8:36:35</span>
                        <span class="title_dl">修改人</span><span class="infoEditDate title_dl" id="infoEditor">露水</span>
                        <span class="takeEffectTime" id="takeEffectTime">本修改自XX年X月X日起生效</span>
                    </div>
                    <table class="ty-table" id="restRecordDetails">
                        <tbody>
                        <tr>
                            <td>部门</td>
                            <td>上下班时间</td>
                            <td>午休时间</td>
                            <td>午休前后是否考勤</td>
                            <td>生效日期</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce_Fixed.cancel() ">取消</span>
            </div>
        </div>
</div>
<div class="bounce">
    <%--打卡记录--%>
    <div class="bonceContainer bounce-blue" id="clockRecord" style="width:1110px;">
        <div class="bonceHead">
            <span id="recordTime">提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p><span id="recordName"></span>打卡记录</p>
            <table class="ty-table ty-table-control" id="clockRecordList">
                <tbody>
                <tr>
                    <td>考勤日期</td>
                    <td>打卡时间</td>
                    <td>打卡类型</td>
                    <td>打卡用手机的品牌</td>
                    <td>打卡用手机的型号</td>
                    <td>打卡用手机的唯一标识</td>
                    <td>考勤设备的编号</td>
                </tr>
                </tbody>
            </table>
            <div id="clockRecordPage"></div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%--提示--%>
    <div class="bonceContainer bounce-green" id="mtTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-green" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">很遗憾，上月已经结账了！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
    <%-- creater 姚宗涛 2018/2/8  09:38:00  考勤录入  下班考勤弹框  --%>
    <div class="bonceContainer bounce-green" id="attendance">
        <div class="bonceHead">
            <span class="name">贾宝玉10月26号星期四</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="attDetails hd">11111</div>
            <div>
                <div class="updaterInfo">
                    <p>上班考勤</p>
                    <div class="yestDayMorn">正常</div>
                </div>
                <div class="updaterInfo">
                    <p class="isAbsent">旷工 <span class="fa fa-circle-o" spot="0" outRadio="0" onclick="isSure($(this))"></span> <button class="hd ty-btn ty-circle-2 ty-btn-green ty-right outOfworkAdd" disabled="disabled" onclick="outOfworkAdd($(this))">新增</button></p>
                    <div class="outOfwork" id="offDuty_absent"></div>
                </div>
                <div class="updaterInfo" id="offWorkSet">
                    <p>下班考勤</p>
                    <div>
                        <dl>
                            <dd>正常 <span class="fa fa-circle-o" onclick="yesOrNot(1,$(this))"></span> </dd>
                            <dd>早退 <span class="fa fa-circle-o" onclick="yesOrNot(3,$(this))"></span> </dd>
                            <input type="hidden" value="0" id="endWorkAttend">
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green offSureBtn" onclick="endAttendSure()">确定</button>
        </div>
    </div>
    <%-- creator 李玉婷 2018/3/26  考勤录入  录入上班考勤弹窗  --%>
    <div class="bonceContainer bounce-green" id="first_attendance">
        <div class="bonceHead">
            <span class="name">贾宝玉</span><span>10月26号星期四</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="hd worker"></div>
            <div>
                <div class="updaterInfo">
                    <p>上班考勤</p>
                    <div id="attendanceState">
                        <dl id="isoutWork">
                            <dd>正常 <span class="fa fa-circle-o" att="1" onclick="yesOrNot(1,$(this))"></span> </dd>
                            <dd>迟到 <span class="fa fa-circle-o" onclick="yesOrNot(2,$(this))"></span> </dd>
                            <input type="hidden" type="text" value="7" id="workAttend">
                            <dd class="hd outWork">旷工 </dd>
                        </dl>
                    </div>
                </div>
                <div class="clr"></div>
                <div class="updaterInfo">
                    <p>旷工 <span id="outofworkBtn" class="fa fa-circle-o" spot="1" outRadio="0" onclick="isSure($(this))"></span></p>
                    <div class="outOfwork">
                        <dl class="hd">
                            <dd class="xing">开始时间</dd>
                            <dd id="notInTime">
                                <select id="outOfworkStrTime" name=""></select>
                            </dd>
                            <dd>结束时间</dd>
                            <dd>
                                <select id="outOfworkEndTime" name=""></select>
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-circle-2 ty-btn-big ty-btn-green mornAttendSure" disabled="disabled" onclick="startAttendSure()">确定</button>
        </div>
    </div>
    <%-- creater 姚宗涛 2018/2/8  15:20:00  修改考勤  查看请假，早退，旷工，迟到。。。。弹框   --%>
    <div class="bonceContainer bounce-blue" id="log" style="min-width:610px;">
        <div class="bonceHead">
            <span class="personName">贾宝玉</span>的<span class="type">请假</span>记录
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table"></table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--  creater 姚宗涛 2018/2/9  08:58:00  修改考勤  查看加班记录  详情 查看按钮 弹框--%>
    <div class="bonceContainer bounce-green" id="overtimeDetails">
        <div class="bonceHead">
            <span>加班记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="">
                <dl>
                    <dd><span>王金锁</span><span>2017年10月26日</span>加班情况 </dd>
                </dl>
            </div>
            <div class="">
                <p>加班</p>
                <div>
                    <dl>
                        <dd>批准时长</dd>
                        <dd>日期</dd>
                        <dd>说明</dd>
                        <dd>批准时间</dd>
                    </dl>
                    <dl>
                        <dd class="">2h</dd>
                        <dd>2017年10月26日</dd>
                        <dd>我就批准你2小时</dd>
                        <dd>2017/10/27 11:00</dd>
                    </dl>
                    <dl>
                        <dd style="margin-top:90px;">批准人</dd>
                    </dl>
                    <dl>
                        <dd style="margin-top:90px;">包莹莹</dd>
                    </dl>
                </div>
                <div class="clr"></div>
            </div>
            <div class="clr"></div>
            <div class="">
                <div>
                    <dl>
                        <dd>申报时长</dd>
                        <dd>日期</dd>
                        <dd>时间</dd>
                        <dd>加班事由</dd>
                        <dd>申请时间</dd>
                    </dl>
                    <dl>
                        <dd>3h</dd>
                        <dd>2017年10月26日</dd>
                        <dd>17:00-20:00</dd>
                        <dd>作图</dd>
                        <dd>2017/10/27 09:00</dd>
                    </dl>
                </div>
                <div class="clr"></div>
            </div>
            <div class="clr"></div>
            <div class="">
                <div>
                    <dl>
                        <dd>计划时长</dd>
                        <dd>日期</dd>
                        <dd>时间</dd>
                        <dd>加班事由</dd>
                        <dd>批准时间</dd>
                        <dd>申请时间</dd>
                    </dl>
                    <dl>
                        <dd>3h</dd>
                        <dd>2017年10月26日</dd>
                        <dd>17:00-20:00</dd>
                        <dd>作图</dd>
                        <dd>2017/10/27 16:00</dd>
                        <dd>2017/10/27 14:00</dd>
                    </dl>
                    <dl>
                        <dd style="margin-top:120px;">批准人</dd>
                    </dl>
                    <dl>
                        <dd style="margin-top:120px;">包莹莹</dd>
                    </dl>
                </div>
                <div class="clr"></div>
            </div>
            <div class="clr"></div>
        </div>
    </div>
    <%--  creater 姚宗涛 2018/2/11 14:41:08  考勤管理页面数据（应出勤人数，迟到人数，早退人数，请假人数，旷工人数，加班人数。。。）查看弹框 --%>
    <div class="bonceContainer bounce-blue" id="seeDetails" style="width: 900px">
        <div class="bonceHead">
            <span class="bounce_title">2017年10月26日应出勤人员</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <table class="kj-table">
                    <thead></thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--creator:lyt 2018/3/28 考勤设置 作息时间查看--%>
    <div class="bonceContainer bounce-green" id="timetableSetting" style="width:700px;">
        <div class="bonceHead">
            <span>作息时间设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="monthsTip">2018年8月份与9月份的作息计划如下，您可根据实际情况勾选修改。</p>
            <ul class="ty-secondTab monthsNav" id="monthsScan">
                <li>2018年11月</li>
                <li>2018年12月</li>
            </ul>
            <div class="clendars" id="clendarsScan" ></div>
            <div class="mask"></div>
        </div>
        <div class="bonceFoot">
            <p class="scan">
                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-2 restSet" onclick="beforeScanAttcs();scanAttc(1);">编辑</span>
                <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-2" onclick="bounce.cancel()">取消</span>
            </p>
            <p class="edit">
                <input type="hidden" id="today">
                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-2 restSet"
                      onclick="editAttendanceOK($(this))">确定</span>
                <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-2" onclick="beforeScanAttcs();scanAttc(0);">取消</span>
            </p>

        </div>
    </div>
    <%--creator:lyt 2018/4/13 考勤设置 修改考勤录入时间弹窗--%>
    <div class="bonceContainer bounce-blue" id="editTime">
        <div class="bonceHead">
            <span>修改考勤录入时间</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <span>考勤录入时间</span>
                <select id="endInputTime" class="ty-inputSelect">
                    <option value="07:30">7:30</option>
                    <option value="08:00">8:00</option>
                    <option value="08:30">8:30</option>
                    <option value="09:00">9:00</option>
                    <option value="09:30">9:30</option>
                    <option value="10:00">10:00</option>
                    <option value="10:30">10:30</option>
                    <option value="11:00">11:00</option>
                </select>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="updateSetTimeBtn()">确定</span>
        </div>
    </div>
    <%--creator:lyt 2018/3/27 考勤修改 修改提交后的查看 --%>
    <div class="bonceContainer bounce-blue" id="attendanceChangeDetail" style="width:560px;">
        <div class="bonceHead">
            <span class="bounce_title">考勤详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">
                应出勤时间&nbsp;<span class="shouldTime"></span>
            </div>
            <div class="ty-alert">
                <div class="btn-group text-center">
                    <span class="title-big">实际考勤</span>
                    <button class="ty-btn ty-btn-blue ty-circle-2 changeBtn" onclick="attendUpdate()">修改</button>
                </div>
            </div>
            <table class="kj-table">
                <tbody></tbody>
            </table>
            <div class="process text-right creator"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 multi-details-ok" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="attendanceSetRecord" style="width:760px;">
        <div class="bonceHead">
            <span>考勤设置修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table editRecord">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>修改时间</th>
                    <th>修改人</th>
                    <th>生效日期</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody id="updateLog"></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2 ty-btn-blue" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="newLeaveType">
        <div class="bonceHead">
            <span>新增请假类型</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 250px; margin: auto; padding: 8px 0;">
                <input class="ty-input" type="text" placeholder="请录入要增加请假类型的名称" style="width: 100%">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-green" onclick="sureAddLeaveType()">确定</span>
        </div>
    </div>
        <%--正常班的考勤设置--%>
        <div class="bonceContainer bounce-blue" id="attendanceNormalSetting" style="width:1060px;">
            <div class="bonceHead">
                <span class="bounce_title">正常班的考勤设置</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="wrapLab">
                    <div class="panelCon">
                        <div class="clear">
                            <span class="ty-left">考勤数据进入系统的模式<span class="ty-color-red">*</span></span>
                            <div class="ty-left modeSelect">
                                <span class="modelPattern" data-icon="1"><span class="fa fa-circle-o"></span>考勤宝</span>
                                <span class="modelPattern" data-icon="2"><span class="fa fa-circle-o"></span>手动录入</span>
                            </div>
                            <span class="ty-right blueLink modeRecord" onclick="modeChangeRecord()">模式更换记录</span>
                        </div>
                        <span class="blueCare">注：目前wonderss系统仅支持上述两种考勤方式。</span>
                    </div>
                    <div class="panelCon">
                        <div class="mode1">
                            <span>本模式下，除需设置以下内容外，还需若干台安卓手机当作考勤设备。</span>
                            <span class="ty-right blueLink" onclick="bounce_Fixed.show($('#clockFunction'))">详细说明</span>
                        </div>
                        <div class="mode2">本模式下，需每日手动录入职工的出勤情况，超时未录入的将由系统记作旷工。您需逐条修改！</div>
                    </div>
                    <div class="panelCon">
                        正常班的上下班时间<span class="ty-color-red">*</span>
                        <div class="ty-right">
                            <span>还有部门尚未设置</span>
                            <span class="blueLink gapL noSet" onclick="setInitOperate(0)">去设置</span>
                            <%--<span class="blueLink gapL" onclick="setInitOperate(2)">去设置</span>--%>
                        </div>
                        <table class="tab_con ty-table" id="roleConList">
                            <tbody>
                            <tr class="unavailableLine">
                                <td>部门</td>
                                <td>上下班时间</td>
                                <td>午休时间</td>
                                <td>午休前后是否考勤</td>
                                <td>操作</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="panelCon">
                        <div class="spaceBt">
                            <span>作息日期<span class="ty-color-red">*</span></span>
                            <div class="ty-right">
                                <span>本月状态：<span class="recState">未</span>编辑</span>
                                <span class="blueLink gapL" onclick="setWorkRestDate(0)">去编辑</span>
                                <span class="gapL">下月状态：<span class="recState">未</span>编辑</span>
                                <span class="blueLink gapL" onclick="setWorkRestDate(1)">去编辑</span>
                                <div class="hd" id="recSettings"></div>
                                <div class="hd" id="nextSettings"></div>
                            </div>
                        </div>
                        <div>系统带有默认值，但每个月均需给予编辑。请至少把本月的作息日期编辑完！</div>
                        <div class="blueCare">注：页面上未列出下下月及更以后的月份。每月25日开始，系统将给予设置下个月作息日期的提示！</div>
                    </div>
                    <div class="mode1 panelCon">
                        <div class="noDuty">
                            <span>上班时间到后几分钟的到岗算作迟到？<span class="ty-color-red">*</span></span>
                            <span class="blueCare gapFar">注：更晚到岗者为旷工。</span>
                            <div class="ty-right smCon">
                                <input placeholder="请录入" id="lateLimit"/>
                                <span>分钟</span>
                            </div>
                        </div>
                        <div class="noDuty">
                            <span>下班时间前几分钟内的离岗算作早退？<span class="ty-color-red">*</span></span>
                            <span class="blueCare gapFar">注：更早时间离岗为旷工。</span>
                            <div class="ty-right smCon">
                                <input placeholder="请录入"  id="earlyLimit"/>
                                <span>分钟</span>
                            </div>
                        </div>
                        <p>请假情况下的到岗离岗是否使用考勤宝考核？<span class="ty-color-red">*</span></p>
                        <div class="leaveCase">
                            <p><span class="fa fa-circle-o" data-icon="1"></span>使用（功能为：过早的离岗为早退或旷工，过晚的到岗为迟到或旷工，且计算规则需与上下班的相同）</p>
                            <div><span class="fa fa-circle-o" data-icon="0"></span>不使用</div>
                        </div>
                    </div>
                    <div class="panelCon">
                        <div class="mode2 noDuty">
                            录入考勤的时间设置为每天几点？<span class="ty-color-red">*</span>
                            <select id="attendanceTime" class="ty-right">
                                <option value="">请选择</option>
                                <option value="07:30">07:30</option>
                                <option selected value="08:00">08:00</option>
                                <option value="08:30">08:30</option>
                                <option value="09:00">09:00</option>
                                <option value="09:30">09:30</option>
                                <option value="10:00">10:00</option>
                                <option value="10:30">10:30</option>
                                <option value="11:00">11:00</option>
                            </select>
                        </div>
                        <div>
                            本次考勤设置从哪天开始正式使用？<span class="ty-color-red">*</span>
                            <input class="ty-right" placeholder="请选择" id="roleStartDate" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="attendanceModeOk()">确定</span>
            </div>
        </div>
        <div class="bonceContainer bounce-blue" id="updateLimit" style="width:1000px;">
            <div class="bonceHead">
                <span class="bounce_title">迟到、早退与旷工的修改</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="wrapLab">
                    <div class="panelCon">
                        <div class="noDuty">
                            <span>上班时间到后几分钟的到岗算作迟到？<span class="ty-color-red">*</span></span>
                            <span class="blueCare gapFar">注：更晚到岗者为旷工。</span>
                            <div class="ty-right smCon">
                                <input placeholder="请录入" id="lateLimit_up"/>
                                <span>分钟</span>
                            </div>
                        </div>
                        <div class="noDuty">
                            <span>下班时间前几分钟内的离岗算作早退？<span class="ty-color-red">*</span></span>
                            <span class="blueCare gapFar">注：更早时间离岗为旷工。</span>
                            <div class="ty-right smCon">
                                <input placeholder="请录入"  id="earlyLimit_up"/>
                                <span>分钟</span>
                            </div>
                        </div>
                        <p>请假情况下的到岗离岗是否使用考勤宝考核？<span class="ty-color-red">*</span></p>
                        <div class="leaveCase">
                            <p><span class="fa fa-circle-o" data-icon="1"></span>使用（功能为：过早的离岗为早退或旷工，过晚的到岗为迟到或旷工，且计算规则需与上下班的相同）</p>
                            <div><span class="fa fa-circle-o" data-icon="0"></span>不使用</div>
                        </div>
                    </div>
                    <div class="panelCon">
                        <div>您的修改将于明日生效！ </div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="updateLimitOk()">确定</span>
            </div>
        </div>
        <%--停用/启用考勤设备--%>
        <div class="bonceContainer bounce-red" id="iotTip">
            <div class="bonceHead">
                <span>！提示</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="ty-center">
                    确定<span id="operMsg"></span>本设备吗？
                    <span class="hd epId"></span>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue" onclick="iotTerminalsSetSure()">确定</span>
            </div>
        </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>考勤管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <input id="pageNum" type="hidden" value="1" />
            <div class="ty-container">
                <%--主页面--%>
                <div class="page main">
                    <%-- //  考勤管理初始页 顶部操作--%>
                    <div class="collectionBtn">
                        <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2" id="attendLog" onclick="attendanceInputBtn()" disabled>考勤录入</button>
                        <button class="ty-btn ty-btn-big ty-btn-cyan ty-circle-2" id="attendSet" onclick="attendanceSetBtn($(this))">考勤设置</button>
                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="updateAttendance" onclick="attendanceUpdateBtn($(this))" disabled>修改考勤</button>
                        <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 back hd" onclick="back($(this))">返回</button>
                    </div>
                    <div class="attendanceQuery">
                        <div class="ty-alert ty-alert-info">
                            今天是 <span class="ty-color-blue nowDay"><span class="nowDay_date"></span> <span class="workOrRest"></span></span>
                            <div class="btn-group text-right">
                                <div class="queryItem">
                                    <span class="ssTtl">查看考勤统计</span>
                                    <input class="kj-input kj-input-blue" id="countKey" placeholder="请选择月份" disabled/><!--
                                --><button class="ty-btn ty-btn-big ty-btn-blue" data-name="countScreen" disabled>确定</button>
                                </div>
                                <div class="queryItem">
                                    <span class="ssTtl">查看考勤明细</span>
                                    <input class="kj-input kj-input-blue" id="detailedKey" placeholder="请选择月份" disabled/><!--
                                --><button class="ty-btn ty-btn-big ty-btn-blue" data-name="detailScreen" disabled>确定</button>
                                </div>
                                <div style="display: none;" id="byLastMonth">如上月尚未结账，您还可修改上月的职工考勤。<button class="ty-btn ty-btn-big ty-btn-blue" onclick="changeLastMonthAttendance(1)">修改上月的考勤</button></div>
                                <div style="display: none;" id="byNowMonth"><button class="ty-btn ty-btn-big ty-btn-blue" onclick="changeLastMonthAttendance(0)">修改本月的考勤</button></div>
                            </div>
                        </div>
                        <div class="ty-alert ty-alert-info">
                            <div style="display: inline-block">
                                <span id="attendanceTip">以下为近三日的考勤情况</span>
                                <small id="updateAttendanceTip" class="ty-color-red" style="display: none;">如需修改某人考勤，点击其考勤即可进行。</small>
                            </div>
                            <div class="btn-group text-right">
                                <div class="queryItem" id="byUserKey" style="display: none">
                                    <span class="ssTtl">按职工查找</span>
                                    <select class="kj-select kj-select-blue" id="userKey" disabled></select><!--
                                    --><button class="ty-btn ty-btn-big ty-btn-blue" data-name="userScreen" disabled>确定</button>
                                </div>
                                <div class="queryItem">
                                    <span class="ssTtl">按部门筛选</span>
                                    <select class="kj-select kj-select-blue" id="departSelect" disabled></select><!--
                                    --><button class="ty-btn ty-btn-big ty-btn-blue" data-name="departScreen" disabled>确定</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <%-- //  考勤管理初始页--%>
                        <div class="ty-tblContainer collectInfo">
                            <table class="kj-table hover">
                                <thead>
                                <tr>
                                    <th style="width: 9%"> </th>
                                    <th style="width: 9%"> 应出勤人数 </th>
                                    <th style="width: 9%"> 不参与考勤人数 </th>
                                    <th style="width: 9%"> 实际出勤人数 </th>
                                    <th style="width: 9%"> 请假人数 </th>
                                    <th style="width: 9%"> 出差人数 </th>
                                    <th style="width: 9%"> 外出人数 </th>
                                    <th style="width: 9%"> 迟到人数</th>
                                    <th style="width: 9%"> 早退人数 </th>
                                    <th style="width: 9%"> 旷工人数 </th>
                                    <th style="width: 9%"> 加班人数 </th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                        <%--   // 考勤统计 --%>
                        <div class="ty-tblContainer count hd">
                            <table class="kj-table hover" id="attendCountList">
                                <thead>
                                <tr>
                                    <th rowspan="2">姓名</th>
                                    <th rowspan="2">部门</th>
                                    <th>请假</th>
                                    <th>出差</th>
                                    <th>外出</th>
                                    <th>迟到</th>
                                    <th>早退</th>
                                    <th>旷工</th>
                                    <th>加班</th>
                                </tr>
                                <tr>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                    <th>(次)</th>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div id="count_page"></div>
                        </div>
                        <%--   // 考勤明细 --%>
                        <div class="ty-tblContainer detail hd">
                            <div class="ty-alert ty-alert-warning">
                                <%--<p class="attendMark">--%>
                                <%--考勤说明：--%>
                                <%--<span class="fa fa-check"></span><span>正常</span>--%>
                                <%--<span class="fa ">/</span><span>迟到</span>--%>
                                <%--<span class="fa fa-times"></span><span>旷工</span>--%>
                                <%--<span class="fa fa-circle-o"></span><span>外出</span>--%>
                                <%--<span class="fa ty-pentagon">--%>
                                <%--<span></span><span></span><span></span><span></span><span></span>--%>
                                <%--</span>--%>
                                <%--<span>出差</span>--%>
                                <%--<span class="fa ty-diamond"></span><span>早退</span>--%>
                                <%--<span class="fa fa-caret-up fa-3x">--%>
                                <%--</span><span>请假</span>--%>
                                <%--<span class="fa fa-minus"></span><span>无需考勤</span>--%>
                                <%--<span class="fa fa-star-o"></span><span>复杂</span>--%>
                                <%--</p>--%>
                                考勤说明：
                                <div class="icon_mark">
                                    <span class="fa fa-check"></span>
                                    <span>正常</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-level-down"></span>
                                    <span>迟到</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-times"></span>
                                    <span>旷工</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-bicycle"></span>
                                    <span>外出</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-plane"></span>
                                    <span>出差</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-level-up"></span>
                                    <span>早退</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-clock-o"></span>
                                    <span>请假</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-minus"></span>
                                    <span>无需考勤</span>
                                </div>
                                <div class="icon_mark">
                                    <span class="fa fa-star-o"></span>
                                    <span>复杂</span>
                                </div>
                            </div>
                            <div class="table-fixed-avatar">
                                <table id="attendDetList" class="kj-table attendDetList fixed hover">
                                    <thead></thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <div id="eidt_page"></div>
                        </div>
                    </div>
                </div>

                <%--考勤设置--%>
                <div class="page attendanceSet" style="display: none">
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="back($(this))">返 回</button>
                    <div class="ty-tblContainer">
                        <div class="noSet">
                            <div class="spaceT">
                                <%--本机构尚未设置考勤规则
                                <div class="btn-group text-right">
                                    <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2" onclick="setInitOperate(0)">去设置</button>
                                </div>--%>
                                <span>本机构尚未完成考勤设置！</span>
                                <span class="gapMid blueLink" onclick="normalClass()">正常班设置</span>
                                <span class="blueLink" onclick="shiftsTip()">倒班设置</span>
                            </div>
                        </div>
                            <%--区别作息时间查看、编辑状态下，日期能否点击 0：查看 1：编辑--%>
                        <div class="attendanceSet_avatar">
                            <ul class="ty-secondTab">
                                <li class="ty-active">正常班</li>
                                <li class="disabled" onclick="shiftsTip()">倒班</li>
                            </ul>
                            <div>
                                <div class="cldType hd">0</div>
                                <div>
                                    <div class="ty-alert">
                                        <div>考勤数据进入系统，自<span id="openDate"></span>采用为“<span id="modeStr"></span>”的模式。</div>
                                        <div class="btn-group clear">
                                            <div class="ty-right onManual gapF">
                                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="editTimeBtn()">修改录入时间</span>
                                            </div>
                                            <div class="ty-right onEquip gapF">
                                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce_Fixed.show($('#clockFunction'))">功能介绍</span>
                                                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="equipScan(1)">考勤设备</span>
                                            </div>
                                            <span class="ty-right ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="changeMode()">更换模式</span>
                                        </div>
                                    </div>
                                    <div class="levGap onManual">本模式下，需每日<span id="inputTime"></span>前手动录入职工的出勤情况。</div>
                                    <span class="blueCare ty-left onManual">注：超时未录入的将由系统记作旷工。您需逐条修改！</span>
                                    <div class="onEquip ty-left">本模式下，需若干台安卓手机当作考勤设备，职工需使用wonderss手机端打卡。</div>
                                    <div class="ty-right">该模式的创建：<span class="modeCreate"></span></div>
                                    <div style="clear:both;"></div>
                                </div>
                                <div class="sdLine">
                                    <div class="ty-alert">
                                        <div>正常班的上下班时间</div>
                                        <div>
                                            <span class="otherDepar">
                                                <span>还有部门尚未设置</span>
                                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-5 gapL" onclick="setInitOperate(2)">去设置</button>
                                            </span>
                                            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="editRecord()">修改记录</button>
                                        </div>
                                    </div>
                                    <table class="tab_con ty-table" id="roleCon">
                                        <tbody>
                                        <tr>
                                            <td>部门</td>
                                            <td>上下班时间</td>
                                            <td>午休时间</td>
                                            <td>午休前后是否考勤</td>
                                            <td>生效日期</td>
                                            <td>操作</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="sdLine onEquip">
                                    <div class="ty-alert">
                                        <div><span class="viewTtl">迟到、早退与旷工</span>
                                            <span class="arrowBtn"><span class="blueLink">展开</span><i class="fa fa-angle-down"></i></span>
                                        </div>
                                        <div class="viewContent">
                                            <span class="hd" id="leaveLimit"></span>
                                            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="updateLimit()">修  改</span>
                                            <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-5">修改记录</span>
                                        </div>
                                    </div>
                                    <div class="viewContent">
                                        <div>上班时间到后<span class="lateLimitInfo"></span>分钟的到岗算作迟到，更晚者为旷工；下班时间前<span class="earlyLimitInfo"></span>分钟内的离岗算作早退，更早者为旷工。</div>
                                        <div>
                                            <div class="isLeaveWork">请假情况下的到岗离岗使用考勤宝考核，过早的离岗为早退或旷工，过晚的到岗为迟到或旷工，其计算规则与上下班的均相同。</div>
                                            <div>请假情况下的到岗离岗不使用考勤宝考核。</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="sdLine">
                                    <div class="ty-alert">
                                        <div><span class="viewTtl">作息日期</span>
                                            <span class="arrowBtn"><span class="blueLink">展开</span><i class="fa fa-angle-down"></i></span>
                                        </div>
                                        <div class="dailyView viewContent">
                                            <span>上月状态：<span id="lastMonthState"></span>编辑</span>
                                            <span class="blueLink gapL" onclick="getTimeDetail(2)">去编辑</span>
                                            <span>本月状态：<span id="thisMonthState"></span>编辑</span>
                                            <span class="blueLink gapL" onclick="getTimeDetail(0)">去编辑</span>
                                            <span>下月状态：<span id="nextMonthState"></span>编辑</span>
                                            <span class="blueLink gapL" onclick="getTimeDetail(1)">去编辑</span>
                                            <span class="hd" id="timeLimit"></span>
                                        </div>
                                    </div>
                                    <div class="viewContent">
                                        <div>系统带有默认值，但每个月均需给予编辑。已编辑过的日期可再次编辑，但系统不保留编辑之前的老数据！</div>
                                        <div class="blueCare">注1：页面上未列出下下月及更以后的月份。每月25日开始，系统将给予设置下个月作息日期的提示！</div>
                                        <div class="blueCare">注2：系统也支持对过去的作息日期进行“编辑”，但仅支持“编辑”至上月，且仅支持将“工作日”改作“休息日”。</div>
                                    </div>
                                </div>
                                <div class="sdLine">
                                    <div class="ty-alert">
                                        <div><span class="viewTtl">请假类型</span>
                                            <span class="arrowBtn"><span class="blueLink">展开</span><i class="fa fa-angle-down"></i></span>
                                        </div>
                                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5 viewContent" onclick="leaveTypeSetting()">设置请假类型</span>
                                    </div>
                                    <div class="viewContent">系统自带几种请假类型，如必要，可另行设置。</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <%--考勤录入--%>
                <div class="page attendanceInput" style="display: none">
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="back($(this))">返回</button>
                    <div class="ty-tblContainer">
                        <p class="hd" id="workTime"></p>
                        <table class="kj-table" id="attendInput" open="0">
                            <tbody></tbody>
                        </table>
                    </div>
                    <div id="log_page"></div>
                </div>

                <%--请假类型设置--%>
                <div class="page leaveTypeSet" style="display: none">
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="attendSet()">返回</button>
                    <div class="ty-tblContainer">
                        <div class="ty-alert">
                            职工请假时，需在以下请假类型中能选择。如有必要，您可新增请假类型，也可对已有的暂停使用。
                            <div class="btn-group text-right">
                                <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2" onclick="newLeaveType()">新增请假类型</button>
                            </div>
                        </div>
                        <table class="kj-table" id="leaveTypeList">
                            <thead>
                            <tr>
                                <th>请假类型</th>
                                <th>创建人</th>
                                <th>暂停使用/恢复使用的操作时间	</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div id=""></div>
                </div>
                <div class="page mainCon2">
                    <div class="equipCon1">
                        <div class="gapBt">
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="back($(this))">返回主页</button>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="attendSet()">返回上一页</button>
                        </div>
                        <span>以下为使用中的考勤设备</span>
                        <span class="ty-right ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="equipScan(0)">已停用的考勤设备</span>
                        <table class="ty-table" id="equipList1">
                            <tbody>
                            <tr>
                                <td>考勤设备编号</td>
                                <td>品牌/型号</td>
                                <td>唯一标识</td>
                                <td>设备的启用</td>
                                <td>操作</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="equipCon0">
                        <div class="gapBt">
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="back($(this))">返回主页</button>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="equipScan(1)">返回上一页</button>
                        </div>
                        <span>以下为已停用的考勤设备</span>
                        <table class="ty-table" id="equipList0">
                            <tbody>
                            <tr>
                                <td>考勤设备编号</td>
                                <td>品牌/型号</td>
                                <td>唯一标识</td>
                                <td>设备的停用</td>
                                <td>操作</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    var setStatus = "${status}" ; // status 1-已设置 0-未设置
    // 获取开始日期
    var startUp = "${startUsingSystemTime}";
    // 获取时间误差
    var hostTime = <%= System.currentTimeMillis() %>;

    var diff = hostTime - new Date().getTime();
    if(diff < 30000){
        diff = 0;
    }
    var startMonth = startUp.substr(0,7) + '-02' ;
    var ssEnd = new Date(hostTime).format('yyyy-MM') + '-20';
</script>
<script src="../script/general/attendance/attendance.js?v=SVN_REVISION"></script>
<script src="../script/general/attendance/myCanlendar.js?v=SVN_REVISION"></script>
<script src="../script/general/attendance/setAttendanceRole.js?v=SVN_REVISION"></script>
</body>
</html>
