<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2016/8/18
  Time: 13:59
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
    <title>教育经历</title>
    <link href="../assets/global/plugins/bootstrap/css/bootstrap.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../assets/global/css/components.min.css?v=SVN_REVISION" rel="stylesheet" id="style_components" type="text/css" />

    <script src="../assets/global/plugins/bootstrap/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/js.cookie.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/jquery.blockui.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js?v=SVN_REVISION" type="text/javascript"></script>
    <script src="../assets/global/plugins/bootstrap/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>

    <script src="../script/function.js?v=SVN_REVISION"></script>
    <%--日历插件--%>
    <script src="../assets/laydate/laydate.js?v=SVN_REVISION" type="text/javascript"></script>
    <style>
        .laydate_body .laydate_y .laydate_yms ul li{width:59px;}
    </style>
</head>
<body>

<form action="../general/addOrUpdatePersonalEducation.do" method="post">
    <input type="hidden" name="id" value="${personalEducation.id}"/>
    <input type="hidden" name="userId" value="${userId}"/>

    <div class="row" >
        <div style="width: 1000px" class="position">
            <!-- BEGIN SAMPLE TABLE PORTLET-->

            <%--<div class="portlet box green">--%>
            <div class="portlet">
                <div class="portlet-title">
                    <div class="caption" style="margin-left: 350px">
                        <h2>编辑教育经历</h2>
                    </div>
                </div>
                <div class="portlet-body">
                    </br>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">学校名称：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" id="firstname" value="${personalEducation.collegeName}"
                                       placeholder="" name="collegeName">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">学习时间：从</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control laydate-icon"  value="${fn:substring(personalEducation.beginTime, 0, 10)}"
                                       placeholder="" name="beginTime1" id="date2" readonly="" style="height:35px;" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">学习时间：到</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control laydate-icon" value="${fn:substring(personalEducation.endTime, 0, 10)}"
                                       placeholder="" name="endTime1" id="date1" readonly="" style="height:35px;" >
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">专业：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" id="" value="${personalEducation.major}"
                                       placeholder="" name="major" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">学历：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control"  value="${personalEducation.degree}"
                                       placeholder="" name="degree" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="firstname" class="col-sm-3 control-label">说明：</label>
                            <div class="col-sm-6">
                                <textarea class="form-control" style="height: 100px"  name="memo">${personalEducation.memo}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div align="center">
                    <input type="submit" class="btn green" style="width:125px"
                           value="保存"/>
                    <input type="button" class="btn green" style="width:125px"
                           value="取消" onclick="refreshOpener();"/>
                </div>
            </div>
        </div>
    </div>
</form>
<script>
    laydate({ elem: '#date1' }) ;
    laydate({ elem: '#date2' }) ;
</script>
</body>
</html>
