<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include file="../../common/headerTop.jsp" %>
<link href="../css/general/employeeInport.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include file="../../common/contentHeader.jsp" %>
<div class="bounce_Fixed">
    <%-- 导入的重要提示 --%>
    <div class="bonceContainer bounce-red" id="importantTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <h4>导入失败！</h4>
                <div>
                    <div>原因可能为：</div>
                    <div>1、修改了所下载表格中的“列”。</div>
                    <div>2、选错了文件。</div>
                    <div>3、文件太大，或里面含有图片等。</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed.cancel()">知道了</span>
        </div>
    </div>
    <%--知道了 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed.cancel()">知道了</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%--我知道了 - 提示--%>
    <div class="bonceContainer bounce-red" id="iknowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="iknowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed3.cancel()">我知道了</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--提示--%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <p id="tipMs" style="text-align: center; padding:10px 0"></p>
                <input type="hidden" id="tipType">
            </div>
        </div>
        <div class="bonceFoot">
            <span id="btn1" class="ty-btn ty-btn-big ty-btn-gay ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span id="btn2" class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="tipOk()">确定</span>
        </div>
    </div>
<%--导入账号重复提示--%>
    <div class="bonceContainer bounce-blue" id="importCfTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="common_bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p id="cf_tip_ms">您导入的手机号里，有与系统中其他职工相同的。</p>
                <p>请确认！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="common_bounce.cancel()">我知道了</span>
        </div>
    </div>
    <%--导入--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #ddebf7;">
            <form action="../export/importUser.do" id="employeeImport" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：下载空白的“职工档案”。</p>
                        <div class="flexRow">
                            <span>职工名单</span>
                            <a href="../assets/oralResource/template/employee_blank_sheet.xls"
                            id="mould1" download="职工名单.xls" class="ty-btn ty-btn-blue">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“职工名单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="uploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="bounce.cancel()">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="importOk()">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加、删除或修改所下载“职工档案”空白表的“列”，否则上传会失败。</span></br>
                            <span>2、在电脑上保存“职工名单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot" style="background: #ddebf7;">
        </div>
    </div>
    <%--修改职工信息--%>
    <div class="bonceContainer bounce-blue" id="updateUser">
        <div class="bonceHead">
            <span>修改职工信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="userForm">
                <div class="formItem">
                    <div class="left">姓名</div><input type="text" class="ty-inputText userName">
                    <i class="clearBtn" onclick="clearTxt($(this))">x</i>
                </div>
                <div class="formItem">
                    <div class="left">手机号</div><input type="text" onkeyup="clearNum(this)" class="ty-inputText userPhone">
                    <i class="clearBtn" onclick="clearTxt($(this))">x</i>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-blue" disabled="disabled" id="importUpdateUser" onclick="updateUserInfo()">确定</button>
        </div>
    </div>
    <%--删除职工--%>
    <div class="bonceContainer bounce-red" id="delUser">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">确定删除所导入的这个职工吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="delUserSure()">确定</span>
        </div>
    </div>
    <%--放弃后，本次批量导入--%>
    <div class="bonceContainer bounce-red" id="clearUser">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>放弃后，本次批量导入的数据将消失不见。</p>
                <p>确定放弃吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" data-name="clearUser">确定</span>
        </div>
    </div>
    <%--进入下一步--%>
    <div class="bonceContainer bounce-red" id="nextStep">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <div class="safeCondition">
                    <p>还有<span id="noSaveMbSum"></span>个手机号无法保存至系统。</p>
                    <p>进入下一步，这些号码将被舍弃。</p>
                </div>
                <p>确定进入下一步吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" data-name="nextStep">确定</span>
        </div>
    </div>
    <%--保存--%>
    <div class="bonceContainer bounce-red" id="lastSave">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>您共导入职工<span id="saveSum"></span>条，可保存至系统的共<span id="saveAble"></span>条。</p>
                <p>确定保存吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" data-name="lastSaveSure">确定</span>
        </div>
    </div>
    <%-- 上次的批量导入尚未完成。  --%>
    <div class="bonceContainer bounce-red" id="importNotCompleted">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody unfinishedForm">
                <p>上次的批量导入尚未完成。</p>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="1"></span>继续上次的操作
                </div>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="0"></span>放弃上次的操作，重新批量导入
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-name="judgeImport">确定</span>
        </div>
    </div>
</div>
<div class="page-container" onclick="$('.options').hide();">
    <%@ include file="../../common/contentSliderNav.jsp" %>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>职工档案</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->

            <div class="ty-container">
                <div class="importOpertion">
                    <div class="hd importing stepItem">
                        <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 btn" data-type="cancelSave" style="margin-right: 250px;">放 弃</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 btn" id="ok">保 存</span>
                    </div>
                    <div class="importNoSave stepItem">
                        <span class="ty-btn ty-btn-yellow ty-btn-big btn" data-type="clearNoSave" style="margin-right: 250px;">放 弃</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big btn" data-type="stepNext">下一步</span>
                    </div>
                </div>
                <div class="clr"></div>
                <br>
                <div class="ty-mainPage">
                    <ul class="ty-secondTab brfore">
                        <li class="ty-active">在职列表</li>
                        <li style="color:#aaa; ">离职列表</li>
                    </ul>
                    <div class="ty-mainData">
                        <div class="hd brfore">
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td>姓名</td>
                                <td>性别</td>
                                <td>手机号</td>
                                <td>部门</td>
                                <td>职位</td>
                                <td>直接上级</td>
                                <td>最高学历</td>
                                <td>更多</td>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            <div style="margin:50px 0 0 100px;">
                                <span class="ty-btn ty-btn-big ty-btn-blue  ty-circle-3" onclick="startBtn()">仅有上述职工，开始正式使用系统</span>
                                <span class="ty-btn ty-btn-big ty-btn-green ty-circle-3"
                                      onclick="importBtn()">批量导入其他职工</span>
                            </div>
                        </div>
                        <div class="hd importNoSave narrowLamp">
                            <p>您共导入职工<span class="initAll"></span>条，其中以下<span class="initWrong"></span>条存在问题，<span class="ty-color-red"> 无法保存至系统</span>。</p>
                            <p>姓名或手机号未录入、手机号错误或与系统中已有号码相同等，均算作问题。</p><br>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>操作</td>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>赵策</td>
                                    <td>123655</td>
                                    <td>
                                        <span class="ty-color-blue btn" data-name="update">修改</span>
                                        <span class="ty-color-red btn" data-name="del">删除</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="hd importing">
                            <p>您共导入职工<span class="importSum"></span>条，可保存至系统的共<span class="saveSum"></span>条。</p>
                            <p>1、为每位职工选定“工作特点”后，才可保存至系统。</p>
                            <p>2、Wonderss内请假、加班、报销等均与“直接上级”有关，故建议按实际情况给予选定。</p>
                            <p class="exportStep">找到<span class="ty-color-red">最高领导的直接下属</span>后，将其“直接上级”选择为最高领导，之后逐级选择的操作方式较易理解。</p><br>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>是否有下属</td>
                                <td>直接上级</td>
                                <td>工作特点</td>
                                <td>操作</td>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include file="../../common/contentSliderLitbar.jsp" %>
</div>
<%@ include file="../../common/footerTop.jsp" %>
<%@ include file="../../common/footerScript.jsp" %>
<script>
    var importState = "${importState}";
</script>
<script src="../script/general/employeeImportBefore.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/general/employeeImport.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
