<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    .bonceCon textarea {
        width: 100%;
    }
    .tipWord{
        padding:20px 30px;
        text-align: center;
        font-size: 16px;
    }
    .snNamePart1,.snNamePart2{
        min-width: 50px;
    }
    .snNamePart1{
        width: 80px;
    }
    .snNamePart2{
        width: 98px;
    }
    .ty-table-control td span.sort{
        font-size: 12px;
        font-family: 宋体;
        padding: 2px 8px 2px;
        border-radius: 1px;
        font-weight: bold;
        cursor: pointer;
        background-color: #ccc;
        color: #fff;
        margin-left: 5px;
    }
    .ty-table-control td span.sort-active{
        background-color:#5d9cec;
    }
    .reasonHover{
        cursor: default;
    }
    .reasonHoverCon{
        position: absolute;
        top: 0;
        left: 50%;
        width: 300px;
        min-height: 120px;
        background-color: #fff;
        padding: 10px 20px 20px 20px;
        border:1px solid #ccc;
        box-shadow: 0 0 5px #ccc;
        display: none;
        text-align: left;
        text-indent: 24px;
    }
    .reasonHover:hover .reasonHoverCon{
        display: block;
    }

</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="voidInvoice">
        <div class="bonceHead">
            <span>作废发票</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <h5>您确定该发票实际已经被作废了吗？</h5>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="sureInvoiceEntry()">确定</span>
        </div>
    </div>
    <%--温馨提示-警告--%>
    <div class="bonceContainer bounce-red" id="F_errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--温馨提示-正常--%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

    <%--温馨提示-警告--%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

    <%--新增固定资产--%>
    <div class="bonceContainer bounce-green" id="newCapitalAssert" style="width: 800px">
        <div class="bonceHead">
            <span>新增固定资产</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td align="right">固定资产序号：</td>
                    <td>
                        <input type="text" class="ty-inputText snNamePart1" id="snNamePart1" style="display: inline;">
                        <input type="text" class="ty-inputText snNamePart2" id="snNamePart2" onkeyup="clearNoNum(this)" style="display: inline;" placeholder="请输入数字">
                    </td>
                    <td align="right"><span class="ty-color-red">*</span> 固定资产名称：</td>
                    <td>
                        <input type="text" class="ty-inputText name" placeholder="请输入名称" id="name" onblur="chargenewCapitalAsser()"  />
                    </td>
                </tr>
                <tr>
                    <td>型号：</td>
                    <td>
                        <input type="text" class="ty-inputText modeNumber" placeholder="请输入型号" id="modeNumber">
                    </td>
                    <td>采购员：</td>
                    <td>
                        <input type="text" class="ty-inputText buyerName" placeholder="请输入采购员名字" id="buyerName">
                    </td>
                </tr>
                <tr>
                    <td>采购日期：</td>
                    <td>
                        <input type="text" class="ty-inputText buyDate" id="buyDate" placeholder="请输入日期">
                    </td>
                    <td>入库日期：</td>
                    <td>
                        <input type="text" class="ty-inputText storageDate" id="storageDate" placeholder="请输入日期">
                    </td>
                </tr>
                <tr>
                    <td>原值：</td>
                    <td>
                        <input type="text" class="ty-inputText originalValue" id="originalValue" onkeyup="clearNum(this)">
                    </td>
                    <td>备注：</td>
                    <td>
                        <input type="text" class="ty-inputText memo" id="memo">
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="sureNewCapitalAssertBtn" onclick="sureNewCapitalAssert()" disabled="disabled">提交</button>
        </div>
    </div>

    <%--维修记录--%>
    <div class="bonceContainer bounce-blue" id="repairHistory" style="width: 1200px;">
        <div class="bonceHead">
            <span>维修记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-control">
                <thead>
                <tr>
                    <td>序号</td>
                    <td>报修日期</td>
                    <td>故障原因</td>
                    <td>维修性质</td>
                    <td>维修内容</td>
                    <td>存在问题</td>
                    <td>维修工姓名</td>
                    <td>联系方式</td>
                    <td>维修单位</td>
                </tr>
                </thead>
                <tbody id="repairHistoryList"></tbody>
            </table>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>

    <%--归还/报废记录--%>
    <div class="bonceContainer bounce-blue" id="giveBackScrapHistory" style="width: 1000px;">
        <div class="bonceHead">
            <span>领用/归还记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <thead>
                <tr>
                    <td>序号</td>
                    <td>领用日期</td>
                    <td>领用部门</td>
                    <td>领用者</td>
                    <td>归还日期</td>
                    <td>归还者</td>
                    <td>归还备注</td>
                </tr>
                </thead>
                <tbody id="giveBackScrapHistoryList"></tbody>
            </table>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>

    <%--领用--%>
    <div class="bonceContainer bounce-cyan" id="toTake" style="width: 670px;">
        <div class="bonceHead">
            <span>领用</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td><span class="ty-color-red">*</span> 领用日期：</td>
                    <td>
                        <input type="text" class="ty-inputText receiveDate" placeholder="请选择日期" id="receiveDate" onblur="chargeTaken()">
                    </td>
                    <td>领用部门：</td>
                    <td>
                        <input type="text" class="ty-inputText receiveDeptName" placeholder="请输入领用部门" id="receiveDeptName">
                    </td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span> 领用者：</td>
                    <td>
                        <input type="text" class="ty-inputText receiverName" placeholder="请输入领用者" id="receiverName" onblur="chargeTaken()" >
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-cyan ty-btn-big ty-circle-5" id="sureToTakeBtn" onclick="sureToTake()">确定</button>
        </div>
    </div>

    <%--归还--%>
    <div class="bonceContainer bounce-green" id="giveBack" style="width: 650px;">
        <div class="bonceHead">
            <span>归还</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td><span class="ty-color-red">*</span>归还日期</td>
                    <td>
                        <input type="text" class="ty-inputText returnDate" placeholder="请选择日期" id="returnDate" onblur="chargeGiveBack()">
                    </td>
                    <td><span class="ty-color-red">*</span>归还者</td>
                    <td>
                        <input type="text" class="ty-inputText returnerName" placeholder="请输入归还者" id="returnerName"  onblur="chargeGiveBack()" >
                    </td>
                </tr>
                <tr>
                    <td>归还备注</td>
                    <td colspan="3">
                        <textarea id="giveBackMemo" rows="3" placeholder="请填写归还备注" class="ty-textarea memo" style="width: 98%"></textarea>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="sureGiveBackBtn" onclick="sureGiveBack()">确定</button>
        </div>
    </div>

    <%--报废--%>
    <div class="bonceContainer bounce-red" id="scrap" style="width: 500px;">
        <div class="bonceHead">
            <span>报废</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord">
                <p>你确定要报废该固定资产吗？</p>
                <textarea id="scrapReason" rows="3" placeholder="请填写报废原因" class="ty-textarea reason"></textarea>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-red ty-btn-big ty-circle-5" id="sureScrapBtn" onclick="sureScrap()">确定</button>
        </div>
    </div>

    <%--维修单--%>
    <div class="bonceContainer bounce-orange" id="repair" style="width: 950px;">
        <div class="bonceHead">
            <span>维修单</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td><span class="ty-color-red">*</span>报修日期</td>
                    <td>
                        <input type="text" class="ty-inputText repairDate" id="repairDate" onblur="chargeRepear()">
                    </td>
                    <td>故障原因</td>
                    <td>
                        <input type="text" class="ty-inputText faultReason" id="faultReason">
                    </td>
                    <td>维修性质</td>
                    <td>
                        <input type="text" class="ty-inputText nature" id="nature">
                    </td>
                </tr>
                <tr>
                    <td><span class="ty-color-red">*</span>维修内容</td>
                    <td colspan="5">
                        <textarea rows="3" class="ty-textarea content" id="content" onblur="chargeRepear()"></textarea>
                    </td>
                </tr>
                <tr>
                    <td>存在问题</td>
                    <td colspan="5">
                        <textarea rows="3" class="ty-textarea existProblem" id="existProblem"></textarea>
                    </td>
                </tr>
                <tr>
                    <td>维修工姓名</td>
                    <td>
                        <input type="text" class="ty-inputText workerName" id="workerName">
                    </td>
                    <td>联系方式</td>
                    <td>
                        <input type="text" class="ty-inputText contactPhone" id="contactPhone">
                    </td>
                    <td><span class="ty-color-red">*</span>维修单位</td>
                    <td>
                        <input type="text" class="ty-inputText contactCorp" id="contactCorp" onblur="chargeRepear()" >
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-orange ty-btn-big ty-circle-5" id="sureRepairBtn" onclick="sureRepair()">确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>固定资产</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" style="position:relative; ">
            <div class="ty-container" id="infoCon">
                <span class="hd" id="navControl"></span>
                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-right" onclick="newCapitalAssertBtn()">新增固定资产</span>

                <ul class="ty-secondTab">
                    <li class="ty-active">在用</li>
                    <li>已报废</li>
                </ul>
                <div class="ty-mainData">

                    <%--在用列表--%>
                    <div class="tplContainer">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>固定资产序号</td>
                                <td>固定资产名称<span class="sort sortName"><i class="fa fa-long-arrow-down"></i></span></td>
                                <td>型号</td>
                                <td>采购日期</td>
                                <td>采购员</td>
                                <td>原值</td>
                                <td>入库日期<span class="sort sortDate"><i class="fa fa-long-arrow-down"></i></span></td>
                                <td>领用者</td>
                                <td>领用日期</td>
                                <td>备注</td>
                                <td>维修记录</td>
                                <td>领用/归还记录</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody  id="usingList"></tbody>
                        </table>
                    </div>

                    <%--已报废列表--%>
                    <div class="tplContainer" style="display: none">
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>固定资产序号</td>
                                <td>固定资产名称<span class="sort sortName"><i class="fa fa-long-arrow-down"></i></span></td>
                                <td>型号</td>
                                <td>采购日期</td>
                                <td>采购员</td>
                                <td>原值</td>
                                <td>入库日期<span class="sort sortDate"><i class="fa fa-long-arrow-down"></i></span></td>
                                <td>报废日期</td>
                                <td>备注</td>
                                <td>报废原因</td>
                                <td>维修记录</td>
                                <td>领用/归还记录</td>
                            </tr>
                            </thead>
                            <tbody  id="scrappedList"></tbody>
                        </table>
                    </div>
                    <div id="ye"></div>
                </div>
            </div>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/general/capitalAsserts.js?v=SVN_REVISION"></script>

</body>
</html>