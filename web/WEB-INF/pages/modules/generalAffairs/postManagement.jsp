<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="../css/event/icon/iconfont.css" />
<style>
    .juZuo{margin-left:180px}
    .juli{margin-top: 10px}
    .hd{display: none}
    .btn{ border:1px solid #00a0e9 ; margin-top:15px;   }
    .btn:hover{ background:#eaeaea; color:#444;  }
    .parent1{ padding-bottom:10px; border-bottom:1px solid #e0e0e0;   }
    .ty-panel{  background: none;    }
    .ty-colFileTree{ float: left ; height:100%; overflow:auto;margin-bottom: 30px;}
    .clr{clear:both;}
    .departTree>ul{ width: 315px; padding:15px 0; margin-left: 10px; border:1px solid #ccc; position: relative; box-shadow: 0 0 3px #ccc;height:300px; overflow:auto;   }
    .departTree>ul:nth-child(2){   margin-left: 80px; }
    .departTree i.fa{  color: #4E9DFF; font-weight: bolder; margin:0 20px;   }
    .departTree li>div{ position: relative; padding:3px 0 3px 25px;    }
    .departTree li>div:hover{ background: #eaeaea; box-shadow:0 0 2px #ccc; cursor: pointer; color: #333; font-weight: bold;     }
    .departTree li>div>span:hover{  }
    .departTree li>div>i.fa-angle-right , .departTree li>div>i.fa-info{ position:absolute; left:-10px; font-size:16px;     }
    .departTree li>div>i.fa-plus-square:hover ,.departTree li>div>i.fa-minus-square:hover , .departTree li>div>i.fa-angle-right:hover{ color:#0075ff;    }
    .departTree li>div>i.fa-plus-square, .departTree li>div>i.fa-minus-square{ float: right;     }
    .departTree ul ul{ display: none;padding-left:15px;  }
    #scanSet { width:760px;  }
    .arrow{  color: #4E9DFF; font-size:50px; top:95px; width:75px; position: relative;    }
    .default_img{margin:auto;width:128px;}
    .default_img img{width:100%;vertical-align:middle;}
    .initialSect{margin-top:100px;}
    .initialSect p{text-align:center;}
    .ty-mainData{  position: relative;  width: 100%;  height: 100%;  }
    .newPost{  position: absolute;  right: 0;  top: -60px;  }
    .ty-colFileTree{max-width: 800px; min-height: 200px}
    .ty-treeItem{min-width: 200px;}
    #sameClass input{color:#333;}
    .departPage{position: relative;}
    .suspendDepart {  position:absolute; right: 0;top: -58px;}
    .gap{margin: 16px 0;}
    .gapLeft{margin-left: 130px;}
    .cCategoryMessage{width: 70%;}
    .conWrap{margin: auto;width: 80%;}
    .conWrap>div{margin-bottom: 10px;}
    .narL {padding-left: 10px;}
    .longSet{width: 100%;}
    .btn-group{flex: auto}
    .bonceConMain{
        width: 350px;
        margin: 0 auto;
    }
    .item-flex{
        display: flex;
        line-height: 1.5;
        margin-bottom: 8px;
    }
    .item{
        line-height: 1.5;
        margin-bottom: 4px;
    }
    .item-fix{
        flex: none;
    }
    .item-auto{
        flex: auto;
    }
    .item-auto input, .item-auto select{
        width: 100%;
    }

    .fix200{
        width: 200px;
    }
    .fix120{
        width: 120px;
    }
    .item .item-row{
        position: relative;
    }
    .item-row label{
        position: absolute;
        top: 0;
        display: block;
        width: 30px;
        height: 30px;
        text-align: center;
        color: #aaa;
        background: #f5f5f5;
        left: -29px;
        border: 1px solid #dcdfe6;
        font-weight: normal;
        line-height: 28px;
    }
    .item .item-title{
        line-height: 1.8;
        margin-bottom: 4px;
    }
    .item .item-content input, .item .item-content select{
        width: 100%;
    }
    .textMax{
        font-size: 12px;
        color: #7f8188;
    }
    .page{
        width: 1300px;
        margin-left: 40px;
    }
    .departMain{
        display: flex;
    }
    .mar{
        flex: auto;
        margin-left: 16px;
    }
    .ty-page-header{
        display: flex;
        line-height: 24px;
        padding: 0 0 0 40px;
        color: #5d9cec;
        margin: 16px 0;
    }
    .page-header__left{
        display: flex;
        cursor: pointer;
        margin-right: 40px;
        position: relative;
    }
    .page-header__left::after {
        content: "";
        position: absolute;
        width: 1px;
        height: 16px;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        background-color: #dcdfe6;
    }
    .page-header__left .icon-back {
        font-size: 18px;
        margin-right: 6px;
        align-self: center;
        position: relative;
        top: 1px;
    }
    .input_choose{
        display: flex;
        position: relative;
        border: 1px solid #dcdfe6;
        min-height: 32px;
        width: 350px;
        background-color: #fff;
        padding: 4px 8px;
        color: #666;
    }
    .input_choose input.search{
        border: none;
        width: 20px;
    }
    .search{
        min-width: 80px;
    }
    .selected_item{
        display: inline-block;
        height: 26px;
        line-height: 26px;
        padding: 0 12px;
        background-color: #eef1f2;
        border-radius: 13px;
        font-size: 14px;
    }
    .input_choose_list {
        width: 350px;
        display: flex;
        flex-direction: column;
        background: #fff;
        display: none;
        max-height: 160px;
        overflow: auto;
        box-shadow: 0 1px 2px #ddd;
    }
    .input_choose_list.dir{
        display: flex;
        max-height: 300px
    }
    .input_choose_item{
        display: flex;
        padding: 8px 10px;
        cursor: default;
    }
    .input_choose_item .icon_avatar{
        width: 36px;
        height: 36px;
        margin-right: 8px;
        border-radius: 18px;
        background-color: #dcdfe6;
    }
    .input_choose_item_info{
        display: flex;
        flex-direction:column;
    }
    .input_choose_item_post{
        color: #aaa;
        font-size: 12px;
    }
    .input_choose_item.selected{
        opacity:0.4;
        filter:alpha(opacity=40);
    }
    .input_choose_item:not(.selected):hover{
        background-color: #e6f0f7;
    }

    .delRole, .delFile{
        transform: rotate(45deg);
        -moz-transform: rotate(45deg);
        display: inline-block;
        font-size: 18px;
        width: 18px;
        height: 18px;
        text-align: center;
        vertical-align: middle;
        line-height: 18px;
        cursor: pointer;
    }
    #post_see .item-content{
        background: #fff;
        border: 1px solid #e5eaee;
        padding: 6px;
        min-height: 35px;
        color: #525d66;
    }
    .input_up {
        color: #5d9cec;
        position: absolute;
        top: 0;
        display: block;
        right: -16px;
        line-height: 30px;
        cursor: pointer;
    }
    .input_down {
        color: #5d9cec;
        position: absolute;
        top: 0;
        display: block;
        right: -30px;
        line-height: 30px;
        cursor: pointer;
    }
    .input_up.disabled, .input_down.disabled{
        color: #ccc;
    }
    .input_del {
        color: #ef5161;
        position: absolute;
        top: 0;
        display: block;
        right: -48px;
        line-height: 30px;
        cursor: pointer;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span class="bounce_title">！！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <%--  修改部门 --%>
    <div class="bonceContainer bounce-blue bounce-changeClass">
        <div class="bonceHead">
            <span>部门改名</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="conWrap">
                <div class="saleChangeTip"><span class="ty-color-red">提示！ </span>销售部的部门名称如被改作非销售性质的部门，则会严重影响系统功能！</div>
                <div class="narL">部门名称</div>
                <input id="nameLen" type="text" class="ty-inputText categoryName longSet" value="" data-orgname="" onkeyup="btnColor($(this),'sureChangeClass')">
                <div id="changeEffect" style="padding-top: 10px;">
                    <p class="narL">请确定本修改的生效日期</p>
                    <input id="effectiveDate" class="longSet" placeholder="请选择"/>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3 change">确定</span>
        </div>
    </div>
    <%--修改记录--%>
    <div class="bonceContainer bounce-blue" id="updateRecords" style="width: 900px;">
        <div class="bonceHead">
            <span class="recordTtl">修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <div class="createRecord clear">
                <div class="recordNone">
                    <span>本部门尚未经修改。</span>
                    <span class="gapLeft">创建人：<span class="recordInfo">系统 XXXX-XX-XX XX:XX:XX</span></span>
                </div>
                <div class="recordEffect">
                    <p class="recordTip">当前的部门名称为<span class="record_postName"></span>，为第<span class="record_num"></span>次修改后的结果。</p>
                    <p class="recordEditer gapLeft">修改人：<span class="recordInfo"></span></p>
                </div>
                <div class="recordOrg">
                    <span>当前的部门名称为<span class="record_postName"></span>，为原始信息。</span>
                    <span class="gapLeft">创建人：<span class="recordInfo"></span></span>
                </div>
            </div>
            <table class="kj-table changeRecord gap">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td>部门名称</td>
                    <td>修改性质</td>
                    <td>状态</td>
                    <td>开始执行日期</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--删除部门--%>
    <div class="bonceContainer bounce-red bounce-deleteClass">
        <div class="bonceHead">
            <span>删除部门</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>是否删除此部门？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="sureDeleteClass()">确定</span>
        </div>
    </div>
    <%--停用--%>
    <div class="bonceContainer bounce-blue" id="stopOrReset" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="msgTip" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="sureStopOrReset()">确定</span>
        </div>
    </div>
    <%--新增子级部门--%>
    <div class="bonceContainer bounce-green bounce-newClass">
        <div class="bonceHead">
            <span>新增子级部门</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            名称<span style="color:red;margin: 0 5px;">*</span><input id="childClass" type="text" class="ty-inputText categoryName" onchange="btnColor($(this),'sureNewClass')">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3 newClass" onclick="sureNewClass()">确定</span>
        </div>
    </div>
    <%--新增同级部门--%>
    <div class="bonceContainer bounce-green bounce-newSameClass">
        <div class="bonceHead">
            <span>新增同级部门</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            名称 <span style="color:red;margin: 0 5px;">*</span> <input id="sameClass" type="text" class="ty-inputText categoryName" onchange=" btnColor($(this),'sureNewSameClass')">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3 newsaneClass" onclick="sureNewSameClass()">确定</span>
        </div>
    </div>
    <%-- 新增岗位 --%>
    <div class="bonceContainer bounce-green" id="addPost">
        <div class="bonceHead">
            <span>新增岗位</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bonceConMain">
                <div class="item">
                    <div class="item-title">
                        <b class="ty-color-red">*</b> 岗位名称
                    </div>
                    <div class="item-content">
                        <input class="kj-input autoFill" max="10" name="name" require />
                        <div class="textMax text-right">0/10</div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">
                        岗位职责
                        <div class="ty-right">
                            <span class="link-blue" onclick="addRow($(this), 'duty')">增加一行</span>
                        </div>
                    </div>
                    <div class="item-content dutyPanel"></div>
                </div>
                <div class="item">
                    <div class="item-title">
                        岗位要求
                        <div class="ty-right">
                            <span class="link-blue" onclick="addRow($(this), 'requirement')">增加一行</span>
                        </div>
                    </div>
                    <div class="item-content requirementPanel"></div>
                </div>
                <div class="item">
                    <div class="item-title">
                        综合
                        <div class="ty-right">
                            <span class="link-blue" onclick="addSynthesizeBtn($(this))">增加</span>
                        </div>
                    </div>
                    <div class="custom_select">
                        <div class="item-content synthesisPanel">
                            <div class="input_choose" name="synthesis">
                                <div class="input_show">
                                    <input type="text" class="search">
                                </div>
                            </div>
                        </div>
                        <div class="field" style="margin: 0">
                            <div class="item-title"></div>
                            <div class="input_choose_list" style="position: absolute;left: 54px;z-index: 2;"></div>
                        </div>
                    </div>
                    <small class="ty-color-blue">注：此项可多选，描述不合适可自行新增。</small>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="sureAddPost()">确定</button>
        </div>
    </div>
    <%-- 改错字 --%>
    <div class="bonceContainer bounce-blue" id="post_changeWord" style="width: 400px">
        <div class="bonceHead">
            <span>改错字</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bonceConMain">
                <div class="item">
                    <div class="item-title">
                        岗位名称
                    </div>
                    <div class="item-content">
                        <input class="kj-input autoFill" max="10" name="name" require onkeyup="c"/>
                        <div class="textMax text-right">0/10</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="updatePostCorrect()">确定</button>
        </div>
    </div>
    <%-- 岗位改名 --%>
    <div class="bonceContainer bounce-blue" id="post_changeName" style="width: 400px">
        <div class="bonceHead">
            <span>岗位改名</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bonceConMain">
                <div class="item">
                    <div class="item-title">
                        岗位名称
                    </div>
                    <div class="item-content">
                        <input class="kj-input autoFill" max="10" name="name" require />
                        <div class="textMax text-right">0/10</div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">
                        请确定本修改的生效日期
                    </div>
                    <div class="item-content">
                        <input class="kj-input" id="post_changeNameDate" placeholder="请选择"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="updatePostName()">确定</span>
        </div>
    </div>
    <%-- 修改岗位内容 --%>
    <div class="bonceContainer bounce-blue" id="post_changedContent">
        <div class="bonceHead">
            <span>修改岗位内容</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bonceConMain">
                <div class="item">
                    <div class="item-title">
                        岗位名称
                    </div>
                    <div class="item-content">
                        <input class="kj-input" name="name" require disabled/>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">
                        岗位职责
                        <div class="ty-right">
                            <span class="link-blue" onclick="addRow($(this), 'duty')">增加一行</span>
                        </div>
                    </div>
                    <div class="item-content dutyPanel"></div>
                </div>
                <div class="item">
                    <div class="item-title">
                        岗位要求
                        <div class="ty-right">
                            <span class="link-blue" onclick="addRow($(this), 'requirement')">增加一行</span>
                        </div>
                    </div>
                    <div class="item-content requirementPanel"></div>
                </div>
                <div class="item">
                    <div class="item-title">
                        综合
                        <div class="ty-right">
                            <span class="link-blue" onclick="addSynthesizeBtn($(this))">增加</span>
                        </div>
                    </div>
                    <div class="custom_select">
                        <div class="item-content synthesisPanel">
                            <div class="input_choose" name="synthesis">
                                <div class="input_show">
                                    <input type="text" class="search">
                                </div>
                            </div>
                        </div>
                        <div class="field" style="margin: 0">
                            <div class="item-title"></div>
                            <div class="input_choose_list" style="position: absolute;left: 54px;z-index: 2;"></div>
                        </div>
                    </div>
                    <small class="ty-color-blue">注：此项可多选，描述不合适可自行新增。</small>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="updatePostCont()">确定</button>
        </div>
    </div>
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue" id="post_changedRecord" style="width: 1000px">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tip"></div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td>改后的数据</td>
                    <td>修改性质</td>
                    <td>状态</td>
                    <td>开始执行日期</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- tip --%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p id="tipMess"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-blue" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 岗位查看 --%>
    <div class="bonceContainer bounce-blue" id="post_see">
        <div class="bonceHead">
            <span>岗位查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bonceConMain">
                <div class="item">
                    <div class="item-title">
                        岗位名称
                    </div>
                    <div class="item-content">
                        <div class="postSee_name"></div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">
                        岗位职责 <span class="link-blue" onclick="sortPost($(this), 'duty')">排序</span>
                    </div>
                    <div class="item-content">
                        <div class="postSee_duty"></div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">
                        岗位要求 <span class="link-blue" onclick="sortPost($(this), 'requirement')">排序</span>
                    </div>
                    <div class="item-content">
                        <div class="postSee_requirement"></div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title">
                        综合
                    </div>
                    <div class="item-content">
                        <div class="postSee_synthesis"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="addSynthesize" style="width:400px; ">
        <div class="bonceHead">
            <span>增加综合项</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <input class="kj-input autoFill" max="30" name="" style="width: 100% "/>
                <div class="textMax text-right">0/30</div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-blue" id="reorder">
        <div class="bonceHead">
            <span>重新排序</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bonceConMain" style="margin-top: 16px">
                <div class="item item_duty">
                    <div class="item-content dutyPanel"></div>
                </div>
                <div class="item item_requirement">
                    <div class="item-content requirementPanel"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="sureReorder()">确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>岗位管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container" id="home">
                <div class="ty-page-header" style="display: none">
                    <div class="page-header__left" onclick="back()">
                        <i class="iconfont icon-houtui icon-back"></i>
                        <span>返回</span>
                    </div>
                    <div class="page-header__content" onclick="backToMain()">
                        <i class="iconfont icon-zhuye icon-back"></i>
                        <span>主页</span>
                    </div>
                </div>
                <div class="page mainCon" page="main">
                    <ul class="ty-secondTab">
                        <li class="ty-active">部门列表</li>
                        <li>岗位列表</li>
                    </ul>
                    <div class="ty-mainData">
                        <div class="tplContainer">
                            <div class="initialSect" style="display: none">
                                <div>
                                    <div class="default_img">
                                        <img src="../assets/oralResource/initImg.png" />
                                    </div>
                                    <p>您还没有设置部门，点击<span class="ty-btn ty-btn-green newSameClass hd" onclick="newSameClass(1)">新增部门</span></p>
                                </div>
                            </div>
                            <div class="ty-fileContent">
                                <div class="departMain">
                                    <%--竖型文件树容器--%>
                                    <div class="ty-colFileTree"></div>
                                    <div class="mar">
                                        <%--此部门信息--%>
                                        <div class="ty-panel departPage">
                                            <div class="suspendDepart"><span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="stopDepartmentBtn()">已停用的部门</span></div>
                                            <div class="ty-panelHeader nowFolder">
                                                <h3 class="ty-left"></h3>
                                                <div class="ty-btn ty-btn-green ty-btn-big ty-circle-3 ty-right newSameClassBtn" style="margin-top:9px" onclick="newSameClass(0)">新增同级部门</div>
                                            </div>
                                            <table class="kj-table CategoryMessage">
                                                <thead>
                                                <tr>
                                                    <td width="30%">创建人</td>
                                                    <td width="10%">职工总数</td>
                                                    <td width="30%">修改部门名称</td>
                                                    <td width="30%">其他操作</td>
                                                </tr>
                                                </thead>
                                                <%--此部门容器--%>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                        <%--子部门信息--%>
                                        <div class="ty-panel childPanel" style="margin-top:8%;margin-bottom:4%;display: none">
                                            <div class="ty-panelHeader childFolder">
                                                <h3>子部门</h3>
                                            </div>
                                            <table class="kj-table cCategoryMessage">
                                                <thead>
                                                <tr>
                                                    <td width="40%">部门名称</td>
                                                    <td width="20%">职工总数</td>
                                                    <td width="40%">创建人</td>
                                                </tr>
                                                </thead>
                                                <%--子部门容器--%>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tplContainer" style="display: none">
                            <div class="postMain" page="postMain">
                                <div class="text-right" style="position:absolute; right: 0; top: -58px">
                                    <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="stopPostBtn()">已停用的岗位</span>
                                    <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="addNewPosition()">新增岗位</span>
                                </div>
                                <table class="kj-table" id="postList">
                                    <thead>
                                    <tr>
                                        <td>岗位名称</td>
                                        <td>人数</td>
                                        <td>修改岗位名称</td>
                                        <td>其他操作</td>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page" page="stopDepartment" style="display: none">
                    <div class="ty-alert">已停用的部门有如下<span id="inSuspendNum"></span>个</div>
                    <table class="kj-table inSuspendList">
                        <thead>
                        <tr>
                            <td width="25%">部门名称</td>
                            <td width="25%">创建人</td>
                            <td width="25%">停用人</td>
                            <td width="25%">操作</td>
                        </tr>
                        </thead>
                        <%--此部门容器--%>
                        <tbody>
                        <tr></tbody>
                    </table>
                </div>
                <div class="page" page="stopPost" style="display: none">
                    <div class="ty-alert">
                        已停用的岗位有如下 <span class="stopPostNum"></span> 个
                    </div>
                    <table class="kj-table" id="stopPostList">
                        <thead>
                        <tr>
                            <td>岗位名称</td>
                            <td>创建人</td>
                            <td>停用人</td>
                            <td>其他操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="roleList" style="display: none">
                    <div class="ty-alert tip"></div>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <td width="15%">姓名</td>
                            <td width="10%">性别</td>
                            <td width="15%">手机号</td>
                            <td width="10%">部门</td>
                            <td width="10%">职位</td>
                            <td width="13%">直接上级</td>
                        </tr>
                        </thead>
                        <%--此部门容器--%>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/function.js?v=SVN_REVISION"></script>
<%--<script src="${pageContext.request.contextPath }/script/role/managerEdit.js"></script>--%>
<script src="../script/role/postManagement.js?v=SVN_REVISION"></script>
</body>
</html>

