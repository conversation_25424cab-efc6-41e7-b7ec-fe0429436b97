<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2016/8/18
  Time: 13:59
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<head>
    <title>家庭成员</title>
    <link href="../assets/global/plugins/bootstrap/css/bootstrap.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="../assets/global/css/components.min.css?v=SVN_REVISION" rel="stylesheet" id="style_components" type="text/css" />
</head>
<body>

<form action="../general/addOrUpdateFolk.do" method="post" id="submit">
    <input type="hidden"  name="folkId" value="${personnelFolk.id}"/>
    <input type="hidden" name="userId"  id="userId" value=""/>

    <div class="row" >
        <div style="width: 1000px" class="position">
            <!-- BEGIN SAMPLE TABLE PORTLET-->

            <%--<div class="portlet box green">--%>
            <div class="portlet">
                <div class="portlet-title">
                    <div class="caption" style="margin-left: 350px">
                        <h2>编辑家庭成员</h2>
                    </div>
                </div>
                <div class="portlet-body">
                    </br>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label  class="col-sm-3 control-label">姓名：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control"  value="${personnelFolk.name}"
                                       placeholder="" name="name"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label  class="col-sm-3 control-label">性别：</label>
                            <div class="col-sm-6">
                                <select type="text" class="form-control" value="${personnelFolk.gender}" name="gender">
                                    <option>男</option>
                                    <option>女</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label  class="col-sm-3 control-label">年龄：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" value="${personnelFolk.age}"
                                       placeholder="" name="age"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">与本人关系：</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control"  value="${personnelFolk.relation}"
                                       placeholder="" name="relation" />
                            </div>
                        </div>

                    </div>
                </div>
                <div align="center">
                    <%--<input on class="btn green" style="width:125px"
                           value="保存"/>--%>
                    <span class="btn green" onclick="conserve()" style="width:125px">保存</span>
                    <input type="button" class="btn green" style="width:125px"
                           value="取消" onclick="refreshOpener();"/>
                </div>
            </div>
        </div>
    </div>
</form>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/jquery.validate.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/messages_zh.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    $("#submit").validate({
        rules:{
            age:{
                number:true
            }
        }
    });
    function conserve(){
        var id = getUrlParam("userId");
        $("#userId").val(id);
        $("#submit").submit();
    }
</script>
</body>
</html>
