<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/applet/miniAppsManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%--提示--%>
    <div class="bonceContainer bounce-red" id="bindTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <p>微信小程序的注册申请已提交！</p>
                <p>微信官方将在3-10个工作日向您反馈审</p>
                <p>核结果，届时您将收到系统消息，请耐心等待！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="bounce.cancel()">知道了</span>
        </div>
    </div>
    <%--与Wonderss绑定小程序--%>
    <div class="bonceContainer bounce-blue" id="bindApplet" style="width:640px;">
        <div class="bonceHead">
            <span>与Wonderss绑定小程序</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody bindArea">
                <p>请根据公司实际情况，在以下选项中选择</p>
                <div class="item">
                    <div class="ty-radio">
                        <input type="radio" name="changeImportManager" value="1" id="weDreact">
                        <label for="weDreact"></label> 注册一个新的微信小程序，直接注册，不使用已有的微信公众号
                    </div>
                </div>
                <div class="item">
                    <div class="ty-radio">
                        <input type="radio" name="changeImportManager" value="2" id="weSigned">
                        <label for="weSigned"></label> 注册一个新的微信小程序，使用已有、已完成认证的微信公众号
                    </div>
                </div>
                <div class="item">
                    <div class="ty-radio">
                        <input type="radio" name="changeImportManager" value="3" id="weExisting">
                        <label for="weExisting"></label> 使用已有的微信小程序
                    </div>
                </div>
                <div class="item">
                    <div class="ty-radio">
                        <input type="radio" name="changeImportManager" value="4" id="tikExisting">
                        <label for="tikExisting"></label> 使用已有的字节小程序（抖音小程序）
                    </div>
                </div>
                <p class="sm-careful">注：由于平台原因，Wonderss暂不支持在系统直接注册新的字节小程序！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取 消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bindAppletNext()">下一步</span>
        </div>
    </div>
    <%--注册新的微信小程序--%>
    <div class="bonceContainer bounce-blue" id="registerWeChatApp" style="width:840px;">
        <div class="bonceHead">
            <span>注册新的微信小程序</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="specialForm">
                <div class="gatBt">注册新的微信小程序，需按真实情况填写或选择以下各项，提交后，还需微信公众平台审核通过</div>
                <form id="newApp">
                    <div class="ap-group">
                        <p><span class="xing"></span>企业名称（需与工商部门登记信息一致）</p>
                        <input type="text" class="form-control" />
                        <span class="ty-color-blue">是“无主体名称的个体工商户”的，填“个体户+法人姓名”，如“个体户张三”</span>
                    </div>
                    <div class="ap-group">
                        <p><span class="xing"></span>企业代码类型</p>
                        <div>
                            <label class="radio-inline">
                                <input type="radio" value="1" name="codeType"> 统一社会信用代码（18 位）
                            </label>
                            <label class="radio-inline">
                                <input type="radio" value="2" name="codeType"> 组织机构代码（9位xxxxxxxx-x）
                            </label>
                            <label class="radio-inline">
                                <input type="radio" value="3" name="codeType"> 营业执照注册号(15 位)
                            </label>
                        </div>
                    </div>
                    <div class="ap-group companyCodeArea">
                        <p><span class="xing"></span>企业代码</p>
                        <div class="companyCodeData">
                            <div class="code code0">本框内仅可输入数字或英文字母，英文字母均按大写字母展示。选择企业代码类型后，本框方可填写！</div>
                            <div class="code code1">
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            </div>
                            <div class="code code2">
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" /> -
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            </div>
                            <div class="code code3">
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                                <input type="text" value="" maxlength="1" oninput="clearNum(this)" />
                            </div>
                        </div>
                    </div>
                    <div class="ap-group">
                        <p><span class="xing"></span>法人微信号</p>
                        <input type="text" class="form-control" />
                    </div>
                    <div class="ap-group">
                        <p><span class="xing"></span>法人真实姓名</p>
                        <input type="text" class="form-control" />
                    </div>
                </form>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="registerWeChatAppSure()">提 交</span>
        </div>
    </div>
    <%--查看服务项目-周期--%>
    <div class="bonceContainer bounce-blue" id="seeServiceByCycle" style="width:800px;">
        <div class="bonceHead">
            <span>查看服务项目</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="add-block ty-color-orange" id="stopDetails">
                <span id="stopName">XXX</span>已于<span id="stopDate">XXXX-XX-XX XX:XX:XX</span>将本产品“停止生产”！</span>
            </div>
            <div class="hd serviceData"></div>
            <div class="scan-wrap">
                <div class="headTip"></div>
                <div class="clear">
                    <div class="ty-right">创建：<span class="seeCreater"></span></div>
                </div>
                <div class="elemFlex">
                    <div>项目的基础信息</div>
                    <div>
                        <span class="def-btn fun-btn" data-icon="1" data-type="updateService">修改</span>
                        <span class="def-btn fun-btn" data-icon="1" data-type="updateServiceRecords">修改记录</span>
                    </div>
                </div>
                <table class="ty-table ty-table-control">
                    <tbody>
                    <tr>
                        <td width="12%">代号</td>
                        <td width="48%" colspan="3">名称</td>
                        <td width="40%" colspan="2">说明</td>
                    </tr>
                    <tr>
                        <td need data-name="code"></td>
                        <td need data-name="name" colspan="3"></td>
                        <td need data-name="memo" colspan="2"></td>
                    </tr>
                    <tr>
                        <td colspan="3">开增值税专用发票时</td>
                        <td rowspan="2">开普票时的开票单价</td>
                        <td rowspan="2">不开发票时的单价</td>
                        <td rowspan="2">参考单价</td>
                    </tr>
                    <tr>
                        <td>税率</td>
                        <td>不含税单价</td>
                        <td>含税单价</td>
                    </tr>
                    <tr>
                        <td need data-name="taxRate"></td>
                        <td need data-name="unitPrice"></td>
                        <td need data-name="unitPriceNotax"></td>
                        <td need data-name="unitPriceInvoice"></td>
                        <td need data-name="unitPriceNoinvoice"></td>
                        <td need data-name="unitPriceReference"></td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td colspan="5" need data-name="priceDesc"></td>
                    </tr>
                    </tbody>
                </table>
                <hr/>
                <div class="elemFlex">
                    <span>收费周期</span>
                    <span>每<span class="feeCycle"></span>收取一次</span>
                </div>
                <div class="elemFlex">
                    <span>收费时间</span>
                    <span><span class="timeCycle"></span>天内</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%--查看服务项目-不按周期--%>
    <div class="bonceContainer bounce-blue" id="seeServiceNoCycle" style="width:900px;">
        <div class="bonceHead">
            <span>查看服务项目</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="add-block ty-color-orange stopDetails">
                <span class="stopName">XXX</span>已于<span class="stopDate">XXXX-XX-XX XX:XX:XX</span>将本产品“停止生产”！</span>
            </div>
            <div class="hd serviceData"></div>
            <div class="scan-wrap">
                <div class="copyArea">
                    <div class="headTip"></div>
                    <div class="clear">
                        <div class="ty-right">创建：<span class="seeCreater"></span></div>
                    </div>
                    <div class="elemFlex">
                        <div>项目的基础信息</div>
                        <div class="funBtns">
                            <span class="def-btn fun-btn" data-icon="0" data-type="updateService">修改</span>
                            <span class="def-btn fun-btn" data-icon="0" data-type="updateServiceRecords">修改记录</span>
                        </div>
                    </div>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td width="12%">代号</td>
                            <td width="64%" colspan="4">名称</td>
                            <td width="24%">单位</td>
                        </tr>
                        <tr>
                            <td need data-name="code"></td>
                            <td need data-name="name" colspan="4"></td>
                            <td need data-name="unit"></td>
                        </tr>
                        <tr>
                            <td>说明</td>
                            <td need data-name="memo" colspan="5"></td>
                        </tr>
                        <tr>
                            <td colspan="3">开增值税专用发票时</td>
                            <td rowspan="2">开普票时的开票单价</td>
                            <td rowspan="2">不开发票时的单价</td>
                            <td rowspan="2">参考单价</td>
                        </tr>
                        <tr>
                            <td>税率</td>
                            <td>不含税单价</td>
                            <td>含税单价</td>
                        </tr>
                        <tr>
                            <td need data-name="taxRate"></td>
                            <td need data-name="unitPrice"></td>
                            <td need data-name="unitPriceNotax"></td>
                            <td need data-name="unitPriceInvoice"></td>
                            <td need data-name="unitPriceNoinvoice"></td>
                            <td need data-name="unitPriceReference"></td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td colspan="5" need data-name="priceDesc"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <hr/>
                <div>
                    <div class="elemFlex modeNoSetted">
                        <div>
                            <div>服务费用的收取，有时与交付/验收有关，还可能分多期收取。</div>
                            <div>“模式设置”，相当于建立对服务费用收取的系统性管控。如需要，请设置！</div>
                            <div class="ty-color-blue">注：模式设置后，如需要还可重新设置。</div>
                        </div>
                        <div><span class="def-btn fun-btn withBold" data-type="edit_modeSetting">模式设置</span></div>
                    </div>
                    <div class="modeSettings">
                        <div>服务费用的收取模式已进行了设置，且该模式下已有数据。</div>
                        <div>该模式下的已有数据可修改，此外如需要，模式也可“重新设置”。</div>
                        <div class="ty-color-blue">“重新设置模式”时，已有数据将被清空。</div>
                        <div class="ty-right">
                            <span class="def-btn fun-btn withBold" data-type="editModeService" id="editModeService">修改当前数据</span>
                            <span class="def-btn fun-btn withBold" data-type="editModeLog">数据的修改记录</span>
                            <span class="def-btn fun-btn withBold" data-type="reModeSet">重新设置模式</span>
                        </div>
                        <div class="gapTp">
                            <div class="oneTime">
                                <div class="elemFlex">
                                    <span>收费时间</span>
                                    <div>
                                        <span class="timeCycle">XXXXXXX  XX</span>天内
                                    </div>
                                </div>
                            </div>
                            <div class="manyTime">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>小程序管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" >
            <!--放内容的地方-->
            <div style="min-height:750px;">
                <div class="ty-container">
                    <input type="hidden" id="showMainConNum">
                    <div class="mainCon mainCon1">
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div>Wonderss支持小程序的绑定。如需要请点击“开始绑定”。
                                    <span class="funBtn ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="bindApplet">开始绑定</span>
                                    <span class="funBtn ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="stoppedService">功能说明</span>
                                </div>
                                <div class="clear">
                                    <div class="ty-left dropOption">
                                        <div class="tx-right"><span class="funBtn ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="inProgress">进程中的申请</span></div>
                                        <div class="inProgress">
                                            <div>待微信审核的小程序</div>
                                            <div>待设置基础信息的微信小程序</div>
                                            <div>待选择模板的小程序</div>
                                        </div>
                                    </div>
                                    <span class="funBtn ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="stoppedService">已解绑的小程序</span>
                                </div>
                            </div>
                            <p>已与Wonderss绑定的小程序有<span class="num"></span>个，具体如下：</p>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>小程序名称</td>
                                    <td>所属平台</td>
                                    <td>所用模板</td>
                                    <td>用户数</td>
                                    <td>与Wonderss的绑定时间</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <span class="ty-color-blue">小程序信息查看</span>
                                        <span class="ty-color-blue">更换模板</span>
                                        <span class="ty-color-blue">解绑</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div id="ye1"></div>
                        </div>
                    </div>
                    <div class="mainCon mainCon2">
                        <div class="ty-alert">
                            <div>待选择模板的小程序有如下<span class="num"></span>个。继续绑定，需为之选择模板并提交平台审核。</div>
                            <div>
                                <span class="linkBtn" data-fun="addService">审核中的申请</span>
                                <span class="linkBtn" data-fun="stoppedService">未通过审核的申请</span>
                            </div>
                        </div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>小程序名称</td>
                                <td>所属平台</td>
                                <td>与Wonderss的绑定时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <span>小程序信息查看</span>
                                    <span>选择模板</span>
                                    <span>终止绑定</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mainCon mainCon3">
                        <div class="ty-alert">
                            <div>已提交平台审核的小程序有如下XX个：</div>
                            <div class="ty-color-blue">注：审核通过与否，您均将收到系统消息。未收到消息，则说明还在审核中。</div>
                        </div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>小程序名称</td>
                                <td>所属平台</td>
                                <td>所选模板</td>
                                <td>与Wonderss的绑定时间</td>
                                <td>提交审核的时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <span>小程序信息查看</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mainCon mainCon4">
                        <div class="ty-alert">
                            <div>已提交但未通过平台审核的小程序有如下XX个，请根据审核结果，决定是否重新选择模板并重新提交。</div>
                        </div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>小程序名称</td>
                                <td>所属平台</td>
                                <td>所选模板</td>
                                <td>与Wonderss的绑定时间</td>
                                <td>提交审核的时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <span>小程序信息查看</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mainCon mainCon5">
                        <div class="ty-alert">
                            <div>微信小程序名称：XXXXXXXX  可选模板如下：</div>
                        </div>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>勾选</td>
                                <td>模板代号</td>
                                <td>模板名称</td>
                                <td>简要说明</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <span>效果预览</span>
                                    <span>功能说明</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div>
                            <p>字节小程序名称：XXXXXXXX  可选模板如下：</p>
                            <table class="ty-table ty-table-control">
                                <thead>
                                <tr>
                                    <td>勾选</td>
                                    <td>模板代号</td>
                                    <td>模板名称</td>
                                    <td>简要说明</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td><span class="fa fa-check-square-o"></span></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <span>效果预览</span>
                                        <span>功能说明</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mainCon mainCon12">
                        <div class="panel-box">
                            <div class="ty-alert">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback(1)">返 回</span>
                                <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">提 交</span>
                            </div>
                            <div class="bindCon">
                                <p>请按实际需要及提示填报以下各项，并提交微信公众平台审核</p>
                                <div class="ty-alert">
                                    <div>
                                        <p><span class="xing"></span>小程序名称
                                            <span class="linkBtn posRt">小程序名称检测</span>
                                        </p>
                                        <input type="text" class="form-control" value=""/>
                                        <p class="tx-right">X/30</p>
                                    </div>
                                    <div>
                                        <p><span class="xing"></span>小程序名称检测结果</p>
                                        <div class="testReport"></div>
                                        <div class="ty-color-blue">注：小程序名称填写后需通过检测，否则申请无法提交！</div>
                                    </div>
                                </div>
                                <div class="ty-alert">
                                    <div>
                                        <p><span class="xing"></span>头像
                                            <span class="linkBtn posRt">上传</span>
                                        </p>
                                        <div class="ty-color-blue">注：用以代表小程序，是品牌重要的表现部分。</div>
                                    </div>
                                    <div>
                                        <p><span class="xing"></span>小程序名称检测结果</p>
                                        <div class="txImg"></div>
                                    </div>
                                </div>
                                <div>
                                    <p><span class="xing"></span>功能介绍</p>
                                    <input type="text" class="form-control" value=""/>
                                    <p class="tx-right">X/120</p>
                                </div>
                                <div class="ty-alert">
                                    <div>
                                        <p>小程序被用户搜索时的状态<span class="linkBtn posRt">上传</span></p>
                                        <div class="txImg"></div>
                                        <div class="ty-color-blue">注：默认状态为可被搜索到，如需要，可点击“改变状态”。</div>
                                    </div>
                                    <div>
                                        <p><span class="xing"></span>小程序模板<span class="linkBtn posRt">选择模板</span></p>
                                        <div class="txImg"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mainCon mainCon13">
                        <div class="panel-box">
                            <div class="ty-alert">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reback(1)">返 回</span>
                                <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">确 定</span>
                            </div>
                            <div class="bindCon">
                                <p>以下内容中，版本号与版本说明为自用，其余均向普通机构展示</p>
                                <ul class="ty-alert">
                                    <li>
                                        <p><span class="xing"></span>模板代号</p>
                                        <input type="text" class="form-control" value=""/>
                                    </li>
                                    <li>
                                        <p><span class="xing"></span>模板名称</p>
                                        <input type="text" class="form-control" value=""/>
                                    </li>
                                    <li>
                                        <p><span class="xing"></span>版本号</p>
                                        <input type="text" class="form-control" value=""/>
                                    </li>
                                    <li>
                                        <p><span class="xing"></span>所属平台</p>
                                        <select>
                                            <option></option>
                                        </select>
                                    </li>
                                </ul>
                                <div class="descriptionEdit">
                                    <div class="linkBtn">编辑简要说明</div>
                                    <div class="linkBtn">编辑功能说明</div>
                                    <div class="linkBtn">编辑版本说明</div>
                                </div>
                                <div>
                                    <table>
                                        <tr>
                                            <td>1</td>
                                            <td>2</td>
                                            <td>3</td>
                                            <td>4</td>
                                            <td>5</td>
                                            <td>6</td>
                                            <td>7</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>上传</td>
                                            <td>上传</td>
                                            <td>上传</td>
                                            <td>上传</td>
                                            <td>上传</td>
                                            <td>上传</td>
                                            <td>上传</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/applet/miniAppsManage.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
