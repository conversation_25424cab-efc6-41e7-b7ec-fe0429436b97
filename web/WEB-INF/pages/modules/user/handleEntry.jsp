<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link rel="stylesheet" type="text/css" href="../css/user/handleEntry.css?v=SVN_REVISION">
    <link rel="stylesheet" type="text/css" href="../css/general/recruit.css?v=SVN_REVISION">
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>个人资料</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <div class="ty-mainData">
                    <div class="interviewerInfo">
                        <div class="sect scan1">
                            <div>
                                <span class="small_ttl">基本信息</span>
                            </div>
                            <div class="con_part overflow">
                                <div class="masterInfo ty-left">
                                    <ul class="overflow">
                                        <li class="col-xs-6 col-sm-4"><span>姓名：</span> <span>${user.userName}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>姓别：</span>
                                            <span>
                                            <c:choose>
                                                <c:when test="${user.gender=='0'  }" >女</c:when>
                                                <c:when test="${user.gender=='1'  }" >男</c:when>
                                                <c:otherwise></c:otherwise>
                                            </c:choose>
                                        </span>
                                        </li>
                                        <li class="col-xs-6 col-sm-4"><span>出生年月：</span> <span>${fn:substring(user.birthday, 0, 10)}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>最高学历：</span> <span>
                                        <c:choose>
                                            <c:when test="${user.degree=='1'}">研究生</c:when>
                                            <c:when test="${user.degree=='2'}">本科</c:when>
                                            <c:when test="${user.degree=='3'}">大专</c:when>
                                            <c:when test="${user.degree=='4'}">中专或高中</c:when>
                                            <c:when test="${user.degree=='5'}">其它</c:when>
                                            <c:otherwise></c:otherwise>
                                        </c:choose>
                                    </span></li>
                                        <li class="col-xs-6 col-sm-4"><span>婚姻状况：</span><c:if test="${user.marry=='1'}">未婚</c:if> <c:if test="${user.marry=='0'}">已婚</c:if></li>
                                        <li class="col-xs-6 col-sm-4"><span>民族：</span> <span>${user.nation}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>政治面貌：</span> <span>${user.politicalStatus}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>籍贯：</span> <span>${user.nativePlace}</span></li>
                                        <li class="col-xs-12 col-sm-10"><span>特长：</span> <span>${user.interesting}</span></li>
                                        <li class="col-xs-12 col-sm-10"><span>爱好：</span> <span>${user.speciality}</span></li>
                                    </ul>
                                    <ul class="overflow">
                                        <li class="col-xs-6 col-sm-4"><span>身份证号：</span> <span>${user.idCard}</span></li>
                                        <li class="col-xs-6 col-sm-8"><span>家庭住址：</span> <span>${user.homeAddress}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>联系电话：</span> <span>${user.mobile}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>E-mail：</span> <span>${user.email}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>QQ:</span> <span>${user.qq}</span></li>
                                    </ul>
                                </div>
                                <div class="ty-left">
                                    <div class="viewerTx">
                                        <img src="../${user.imgPath}" onerror="this.src='../assets/oralResource/user.png'" alt="用户头像" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="sect jobInfor">
                            <div>
                                <span class="small_ttl">岗位信息</span>
                            </div>
                            <div class="con_part">
                                <div class="">
                                    <ul class="overflow">
                                        <li class="col-xs-6 col-sm-4"><span>入职时间：</span> <span>${fn:substring(user.onDutyDate, 0, 10)}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>部门：</span> <span>${user.departName}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>职位：</span> <span>${user.postName}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>是否为普通员工：</span> <span><c:if test="${user.ordinaryEmployees==1}"> 是 </c:if> <c:if test="${user.ordinaryEmployees!=1}"> 否 </c:if></span></li>
                                        <li class="col-xs-6 col-sm-4"><span>所属高管：</span> <span>${user.manageName}</span></li>
                                        <li class="col-xs-6 col-sm-4"><span>直接上级：</span> <span>${user.leaderName}</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="sect">
                            <div>
                                <span class="small_ttl">教育背景</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="15%">学校名称</td>
                                    <td width="15%">学习时间</td>
                                    <td width="15%">专业</td>
                                    <td width="15%">学历</td>
                                    <td width="40%">说明</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${user.personalEducations}" var="pe">
                                        <tr>
                                            <td>${pe.collegeName}</td>
                                            <td>${fn:substring(pe.beginTime, 0, 10)}~${fn:substring(pe.endTime, 0, 10)}</td>
                                            <td>${pe.major}</td>
                                            <td>${pe.degree}</td>
                                            <td>${pe.memo}</td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div>
                                <span class="small_ttl">工作经历</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="15%">公司名称</td>
                                    <td width="15%">工作时间</td>
                                    <td width="15%">薪资水平</td>
                                    <td width="15%">在职职位</td>
                                    <td width="20%">工作职责</td>
                                    <td width="20%">未继续工作的原因</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${user.personnelOccupations}" var="po">
                                        <tr>
                                            <td>${po.corpName}</td>
                                            <td>${fn:substring(po.beginTime, 0, 10)}~${fn:substring(po.endTime, 0, 10)}</td>
                                            <td>${po.salary}</td>
                                            <td>${po.post}</td>
                                            <td>${po.operatingDuty}</td>
                                            <td>${po.memo}</td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div>
                                <span class="small_ttl">家庭成员</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="15%">姓名</td>
                                    <td width="15%">性别</td>
                                    <td width="15%">年龄</td>
                                    <td width="15%">与本人关系</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${user.personnelFolksHashSet}" var="pf">
                                        <tr>
                                            <td>${pf.name}</td>
                                            <td>${pf.gender}</td>
                                            <td>${pf.age}</td>
                                            <td>${pf.relation}</td>
                                        </tr>
                                    </c:forEach>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div>
                                <span class="small_ttl">薪资情况</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="20%">修改时间</td>
                                    <td width="20%">修改原因</td>
                                    <td width="20%">薪资</td>
                                    <td width="20%">变更人</td>
                                    <td width="20%">备注</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${user.personnelSalaryLogUser}" var="ps">
                                        <tr>
                                            <td>${ps.operateTime}</td>
                                            <td>${ps.admustResaon}</td>
                                            <td>${ps.salary}</td>
                                            <td>${ps.operatorName}</td>
                                            <td>${ps.memo}</td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div>
                                <span class="small_ttl">奖惩情况</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="20%">修改时间</td>
                                    <td width="20%">内容</td>
                                    <td width="20%">变更人</td>
                                    <td width="20%">备注</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${user.personalRewardPunishments}" var="pr">
                                        <tr>
                                            <td>${pr.occurDate}</td>
                                            <td>${pr.content}</td>
                                            <td>${pr.operatorName}</td>
                                            <td>${pr.memo}</td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="sect">
                            <div>
                                <span class="small_ttl">评论情况</span>
                            </div>
                            <div class="con_part">
                                <table class="ty-table ty-table-control">
                                    <thead>
                                    <td width="20%">修改时间</td>
                                    <td width="20%">内容</td>
                                    <td width="20%">变更人</td>
                                    <td width="20%">备注</td>
                                    </thead>
                                    <tbody>
                                    <c:forEach items="${user.personalAssessments}" var="pa">
                                        <div class="ty-process-item">
                                            <p><span class="dot"></span>${pa.assessDate}</p>
                                            <p>变更人：${pa.assessUserName}</p>
                                            <article>${pa.content}</article>
                                            <article>${pa.memo}</article>
                                        </div>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>

        </div>

    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>


</body>
</html>

