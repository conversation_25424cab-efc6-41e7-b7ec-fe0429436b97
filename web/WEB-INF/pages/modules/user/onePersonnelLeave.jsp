<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

<style>
    body{
        background-color: #fff;
    }
    .juzuo{overflow: hidden}
    .juli{margin-top: 10px}
    .portlet{
        width: 800px;
        margin:0 auto;
        margin-top:80px;
    }
</style>

<script src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>
<script src="../My97DatePicker/WdatePicker.js?v=SVN_REVISION"></script>

<script type="text/javascript">
    function tijiao(){
        var bankAccountId=$("#bankAccountId").val();
        $.ajax({
            url:'../user/applicationForBankAmeVerification.do?bankAccountId='+bankAccountId,
            dataType:'Text',
            success:function(course){
                if (course=="yes"){
                    window.open ('../user/yanZheng.do','newwindow','height=400,width=1000,toolbar=no,menubar=no,scrollbars=no, resizable=no,location=no, status=no')
                }else{
                    $("#form1").submit();
                }
            }
        })
    }
</script>

<body>

<div class="page-content-wrapper juzuo">
    <div class="content">
        <!--放内容的地方-->
        <div class="row" >
            <div class="portlet">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="caption text-center">
                        <h2>请假申请处理</h2>
                        <hr>
                    </div>
                    <div class="portlet-body">
                        <div class="row">
                            <div class="col-sm-8 col-sm-offset-2">
                                <table class="table table-striped table-bordered table-hover" id="sample_1">
                                    <tr>
                                        <th>申请人 </th>
                                        <th>${personnelLeave.user.userName}</th>
                                    </tr>
                                    <tr>
                                        <th>申请时间</th>
                                        <th>${fn:substring(personnelLeave.createDate,0,19)}<%--${fn:substring(personnelLeave.beginTime, 0, 10)}--%></th>
                                    </tr>
                                    <tr>
                                        <th>开始时间   </th>
                                        <th>${fn:substring(personnelLeave.beginTime , 0, 19)}<%--${fn:substring(personnelLeave.beginTime, 0, 10)}--%></th>
                                    </tr>
                                    <tr>
                                        <th>序号</th>
                                        <th>${personnelLeave.id}</th>
                                    </tr>
                                    <tr>
                                        <th>结束时间   </th>
                                        <th>${fn:substring(personnelLeave.endTime , 0, 19)}<%--${fn:substring(personnelLeave.endTime, 0, 10)}--%></th>
                                    </tr>
                                    <tr>
                                        <th>时长（小时） </th>
                                        <th>${personnelLeave.duration}</th>
                                    </tr>
                                    <tr>
                                        <th>请假类型 </th>
                                        <th>
                                            <c:if test="${personnelLeave.type==1}">事假</c:if>
                                            <c:if test="${personnelLeave.type==2}">病假</c:if>
                                            <c:if test="${personnelLeave.type==3}">年假</c:if>
                                            <c:if test="${personnelLeave.type==4}">调休</c:if>
                                            <c:if test="${personnelLeave.type==5}">婚假</c:if>
                                            <c:if test="${personnelLeave.type==6}">产假</c:if>
                                            <c:if test="${personnelLeave.type==7}">陪产假</c:if>
                                            <c:if test="${personnelLeave.type==8}">路途假</c:if>
                                            <c:if test="${personnelLeave.type==9}">其他</c:if>
                                        </th>
                                    </tr>
                                    <tr>
                                        <th>请假内容 </th>
                                        <th>${personnelLeave.reason}</th>
                                    </tr>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
