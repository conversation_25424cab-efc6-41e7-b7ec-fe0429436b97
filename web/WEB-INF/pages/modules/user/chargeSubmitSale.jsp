<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
 <link href="../css/common/process.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    #detail{ width:600px; }
    .ty-left{ width:50%;   }
    .infoCon{ padding:30px 15px 20px ; }
    .bonceCon input,.bonceCon select,.bonceCon textarea {
        width: 100%;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed"  onclick="imgOut()">
    <div class="bonceContainer bounce-red bigImgBnc" id="bigImgBnc" onclick="stopClick(event)" >
        <div id="bigImagcon"  >
            <img id="bigImag" src=""  />

        </div>
    </div>
    <p id="imgController" onclick="stopClick(event)">
        <span class="rotate" title="缩小" onclick="imgChange(0)"><i class="fa fa-compress "></i></span>
        <span class="rotate" title="放大" onclick="imgChange(1)"><i class="fa fa-expand "></i></span>
        <span class="rotate" title="旋转" onclick="rotateImg()"><i class="fa fa-rotate-right "></i></span>
        <span class="closeImg" title="关闭" onclick="imgOut()"><i class="fa fa-remove"></i></span>
    </p>
</div>
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="mtTip" style="width:500px; ">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--报销详情--%>
    <div class="bonceContainer bounce-blue" id="contactDetail" style="width: 750px;">
        <div class="bonceHead">
            <span>报销详情：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process">
                    <div class="infoHead ty-process-ttl"><span class="ty-btn ty-btn-green">审批流程</span></div>
                    <div class="conInfo ty-process-container" id="process">
                        <div class="infoList ty-process-item">
                            <p><span class="dot"></span>处理人：财务/超管 </p>
                            <p>2016-02-02 12：00:00</p>
                            <article>意见：</article>
                        </div>
                        <div class="infoList ty-process-item ">
                            <p><span class="dot-no"></span>处理人：财务/超管 </p>
                            <p>2016-02-02 12：00:00</p>
                            <article>意见：</article>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process">
                    <div class="infoHead ty-process-ttl "><span class="ty-btn ty-btn-green">详情信息</span></div>
                    <div class="infoCon">
                        <p class="procon"><span>申请人 ：       </span><span id="info_creator"></span></p>
                        <p class="procon"><span>申请时间 ：     </span><span id="info_createDate"></span></p>
                        <p class="procon"><span>费用类别 ：     </span><span id="info_feeCatName"></span></p>
                        <p class="procon"><span>票据种类 ：     </span><span id="info_billCatName"></span></p>
                        <p class="procon"><span>票据所属月份 ：  </span><span id="info_billDate"></span></p>
                        <p class="procon" style="display: none"><span>摘要 ：         </span><span id="info_summary"></span></p>
                        <p class="procon"><span>用途 ：         </span><span id="info_purpose"></span></p>
                        <p class="procon"><span>票据数量 ：      </span><span id="info_billQuantity"></span></p>
                        <p class="procon"><span>实际金额 ：      </span><span id="info_amount"></span></p>
                        <p class="procon"><span>发票金额 ：      </span><span id="info_billAmount"></span></p>
                        <p class="procon"><span>备注 ： </span><span id="info_memo"></span></p>
                        <p class="procon"><span>附件 ：

                            </span><span id="info_img"></span></p>
                    </div>
                </div>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="reject_btn($(this))">批准</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="reject_btn($(this))">驳回</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>处理报销请求</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container" id="tblList">
                <div class="hd">
                    <a id="for_n" onclick="pending( 1 , 1 , 15 )">待处理</a>
                </div>
                <ul class="ty-secondTab" >
                    <li class="ty-active" onclick="pending(1 , 1 , 15 , $(this) )">待处理</li>
                    <li onclick=" pending(2 , 1 , 15 , $(this) )">已批准</li>
                    <li onclick=" pending(3 , 1 , 15 , $(this) )">已驳回</li>
                </ul>
                <div class="ty-mainData">
                    <table class="ty-table ty-table-control" id="tab_15_1">
                        <thead>
                            <td width="10%">序号</td>
                            <td width="15%">申请人</td>
                            <td width="15%">申请时间</td>
                            <td width="8%">实际金额</td>
                            <td width="8%">发票金额</td>
                            <td width="14%">费用类别</td>
                            <td width="15%">票据种类</td>
                            <td width="15%">更多</td>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <table class="ty-table hd ty-table-control" id="tab_15_2">
                        <thead>
                            <td width="10%">序号</td>
                            <td width="15%">申请人</td>
                            <td width="15%">申请时间</td>
                            <td width="8%">实际金额</td>
                            <td width="8%">发票金额</td>
                            <td width="14%">费用类别</td>
                            <td width="15%">票据种类</td>
                            <td width="15%">更多</td>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <table class="ty-table hd ty-table-control" id="tab_15_3">
                        <thead class="bg">
                        <td width="10%">序号</td>
                        <td width="15%">申请人</td>
                        <td width="15%">申请时间</td>
                        <td width="8%">实际金额</td>
                        <td width="8%">发票金额</td>
                        <td width="14%">费用类别</td>
                        <td width="15%">票据种类</td>
                        <td width="15%">更多</td>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div id="ye"></div>
            </div>
            <div id="infoCon" class="ty-container hd">
                <!-- 待处理的查看处理框 -->
                <div  class="dealSee_continer deal_seehide" id="infoContainer">
                    <div class="wipe_deal" id="wipe_deal"> </div>
                    <div class="operate">
                            <span class="ty-btn ty-btn-big ty-btn-green" onclick="reject_btn($(this))">
                                <span>接受</span>
                                <span style="display:none;" class="panduan">1</span>
                                <span class="id" style="display:none;"></span>
                            </span>
                            <span class="ty-btn ty-btn-big ty-btn-red"  onclick="reject_btn($(this))">
                                <span>驳回</span>
                                <span style="display:none;" class="panduan">0</span>
                                <span class="id" style="display:none;"></span>
                            </span>
                    </div>
                    <div class="dealSee_process" id="dealSee_process"></div>
                </div>
                <div  class="table_con">
                    <div>
                        <table class="ty-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/user/chargeSubmitSale.js?v=SVN_REVISION"></script>

</body>
</html>
