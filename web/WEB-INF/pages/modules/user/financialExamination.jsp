<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link href="../css/user/leave.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <style>
        .hd{display: none}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="person_head">个人中心</div>
            <div class="person_Continer">
                <form action="../user/partRequestProcessing.do" method="post" id="form1">
                    <div class="person_border">
                        <div>
                            <div class="person_con">
                                <span>处理请求</span>
                                <a class="aboutbtn azury resourceAddBtn btn_normol "  onclick="ask_leave($(this))">处理加班请求<input type="text" style="display: none;"  value="1"/></a>
                                <a class="aboutbtn azury resourceAddBtn btn_normol "   onclick="ask_leave($(this))">处理请假请求<input type="text" style="display: none;"  value="2"/></a>
                                <a class="aboutbtn azury resourceAddBtn  btn_active" onclick="ask_leave($(this))">处理报销请求<input type="text" style="display: none;" value="3"/></a>
                            </div>
                        </div>
                    </div>
                <%--<div class="m-heading-1 border-green m-bordered">--%>
                    <%--<div class="portlet-body">--%>
                        <%--<i class="icon-settings font-dark"></i>--%>
                        <%--状态：<select  name="messageType" onchange="ti()">--%>

                        <%--<option value="">请选择</option>--%>
                        <%--<c:if test="${userType}=1">--%>
                            <%--<option value="1">财务申请</option>--%>
                        <%--</c:if>--%>
                        <%--<option value="2">加班申请</option>--%>
                        <%--<option value="3">请假申请</option>--%>
                    <%--</select>--%>

                    <%--</div>--%>

                <%--</div>--%>
            </form>
            <div class="person_tab">
                <div class="portlet-body">
                <div class="tabbable-line">
                    <ul class="nav nav-tabs person_ul">
                        <li class="active">
                            <a href="#tab_15_1" data-toggle="tab"> 待处理 </a>
                        </li>
                        <li>
                            <a href="#tab_15_2" data-toggle="tab"> 已批准 </a>
                        </li>
                        <li>
                            <a href="#tab_15_3" data-toggle="tab"> 已驳回 </a>
                        </li>

                    </ul>

                    <div class="tab-content">
                        <div class="tab-pane active" id="tab_15_1">
                            <div class="row">
                                <div class="col-md-12">
                                    <!-- BEGIN EXAMPLE TABLE PORTLET-->
                                    <div class="portlet  ">

                                        <div class="portlet-body">
                                            <table class="table table-striped table-bordered table-hover" id="sample_1">
                                                <thead class="bg">
                                                    <td width="10%">序号</td>
                                                    <td width="15%">申请人</td>
                                                    <td width="15%">申请时间</td>
                                                    <td width="15%">报销金额</td>
                                                    <td width="15%">费用类别</td>
                                                    <td width="15%">票据种类</td>
                                                    <td width="15%">更多</td>

                                                    <%--<td>序号</td>--%>
                                                    <%--<td>处理事项</td>--%>
                                                    <%--<td>申请时间</td>--%>
                                                    <%--<td>状态</td>--%>
                                                    <%--<td>处理人</td>--%>
                                                    <%--<td>处理时间</td>--%>
                                                    <%--<td>操作</td>--%>
                                                </thead>
                                                <c:forEach items="${userMessages1}" var="um">
                                                    <tr>
                                                        <td>${um.id}</td>
                                                        <td>来自${um.content}<c:if test="${um.accountOrDetailed==1}">账户</c:if><c:if test="${um.accountOrDetailed==2}">明细</c:if>的<c:if test="${um.operationType==1}"><font color="red">新增</font></c:if><c:if test="${um.operationType==2}"><font color="red">删除</font></c:if><c:if test="${um.operationType==3}"><font color="red">修改</font></c:if>申请</td>
                                                        <td>${um.createDate}</td>
                                                        <td>
                                                            <c:if test="${um.approvalStatus==1}">待处理</c:if>
                                                            <c:if test="${um.approvalStatus==2}">已批准</c:if>
                                                            <c:if test="${um.approvalStatus==3}">已驳回</c:if>
                                                        </td>
                                                        <td>${um.user.userName}</td>
                                                        <td>${um.handleTime}</td>
                                                        <td>
                                                            <c:if test="${um.accountOrDetailed=='1'&& um.operationType=='3'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/bankAccountChange.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='1'&& um.operationType=='2'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/bankAccountDel.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='2'&& um.operationType=='2'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/cashAccountDel.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='2'&& um.operationType=='3'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/cashAccountChange.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane" id="tab_15_2">
                            <div class="row">
                                <div class="col-md-12">
                                    <!-- BEGIN EXAMPLE TABLE PORTLET-->
                                    <div class="portlet  ">
                                        <div class="portlet-body">
                                            <table class="table table-striped table-bordered table-hover" id="sample_1">
                                                <thead class="bg">
                                                    <td width="10%">序号</td>
                                                    <td width="15%">申请人</td>
                                                    <td width="15%">申请时间</td>
                                                    <td width="15%">报销金额</td>
                                                    <td width="15%">费用类别</td>
                                                    <td width="15%">票据种类</td>
                                                    <td width="15%">更多</td>

                                                <%--<td>序号</td>--%>
                                                <%--<td>处理事项</td>--%>
                                                <%--<td>申请时间</td>--%>
                                                <%--<td>状态</td>--%>
                                                <%--<td>处理人</td>--%>
                                                <%--<td>处理时间</td>--%>
                                                <%--<td>操作</td>--%>
                                                </thead>
                                                <c:forEach items="${userMessages2}" var="um">
                                                    <tr>
                                                        <td>${um.id}</td>
                                                        <td>来自${um.content}<c:if test="${um.accountOrDetailed==1}">账户</c:if><c:if test="${um.accountOrDetailed==2}">明细</c:if>的<c:if test="${um.operationType==1}"><font color="red">新增</font></c:if><c:if test="${um.operationType==2}"><font color="red">删除</font></c:if><c:if test="${um.operationType==3}"><font color="red">修改</font></c:if>申请</td>
                                                        <td>${um.createDate}</td>
                                                        <td>
                                                            <c:if test="${um.approvalStatus==1}">待处理</c:if>
                                                            <c:if test="${um.approvalStatus==2}">已批准</c:if>
                                                            <c:if test="${um.approvalStatus==3}">已驳回</c:if>
                                                        </td>
                                                        <td>${um.user.userName}</td>
                                                        <td>${um.handleTime}</td>
                                                        <td>
                                                            <c:if test="${um.accountOrDetailed=='1'&& um.operationType=='3'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/bankAccountChange.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='1'&& um.operationType=='2'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/bankAccountDel.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='2'&& um.operationType=='2'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/cashAccountDel.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='2'&& um.operationType=='3'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/cashAccountChange.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane" id="tab_15_3">
                            <div class="row">
                                <div class="col-md-12">
                                    <!-- BEGIN EXAMPLE TABLE PORTLET-->
                                    <div class="portlet  ">

                                        <div class="portlet-body">
                                            <table class="table table-striped table-bordered table-hover" id="sample_1">
                                                <thead class="bg">
                                                    <td width="10%">序号</td>
                                                    <td width="15%">申请人</td>
                                                    <td width="15%">申请时间</td>
                                                    <td width="15%">报销金额</td>
                                                    <td width="15%">费用类别</td>
                                                    <td width="15%">票据种类</td>
                                                    <td width="15%">更多</td>

                                                <%--<td>序号</td>--%>
                                                <%--<td>处理事项</td>--%>
                                                <%--<td>申请时间</td>--%>
                                                <%--<td>状态</td>--%>
                                                <%--<td>处理人</td>--%>
                                                <%--<td>处理时间</td>--%>
                                                <%--<td>操作</td>--%>
                                                </thead>
                                                <c:forEach items="${userMessages3}" var="um">
                                                    <tr>
                                                        <td>${um.id}</td>
                                                        <td>来自${um.content}<c:if test="${um.accountOrDetailed==1}">账户</c:if><c:if test="${um.accountOrDetailed==2}">明细</c:if>的<c:if test="${um.operationType==1}"><font color="red">新增</font></c:if><c:if test="${um.operationType==2}"><font color="red">删除</font></c:if><c:if test="${um.operationType==3}"><font color="red">修改</font></c:if>申请</td>
                                                        <td>${um.createDate}</td>
                                                        <td>
                                                            <c:if test="${um.approvalStatus==1}">待处理</c:if>
                                                            <c:if test="${um.approvalStatus==2}">已批准</c:if>
                                                            <c:if test="${um.approvalStatus==3}">已驳回</c:if>
                                                        </td>
                                                        <td>${um.user.userName}</td>
                                                        <td>${um.handleTime}</td>
                                                        <td>
                                                            <c:if test="${um.accountOrDetailed=='1'&& um.operationType=='3'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/bankAccountChange.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='1'&& um.operationType=='2'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/bankAccountDel.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='2'&& um.operationType=='2'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/cashAccountDel.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                            <c:if test="${um.accountOrDetailed=='2'&& um.operationType=='3'}">
                                                                <input type="button" class="btn green" value="查看" onclick="openWindow('../user/cashAccountChange.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                                                            </c:if>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            </div>
         </div>



        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/function.js?v=SVN_REVISION"></script>
<script src="../script/user/leave.js?v=SVN_REVISION"></script>
<script type="text/javascript">
    function ti(){
        $("#form1").submit();
    }
</script>
</body>
</html>
