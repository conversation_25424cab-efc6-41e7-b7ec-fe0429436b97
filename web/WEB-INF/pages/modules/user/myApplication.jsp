<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link href="../css/user/myApplication.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    #detail{ width:600px; }
    .ty-left{ width:50%;   }
    .infoCon{ padding:30px 15px 20px ; }
    .bonceCon input,.bonceCon select,.bonceCon textarea {
        border:1px solid #ccc;
        text-align: left;
        padding: 0 8px;
        color: #3f3f3f;
        width: 180px;
        display: inline-block;
        height: 30px;
        line-height: 30px;
    }
    .bonceCon textarea{
        line-height: 20px;
        padding:5px;
    }
    .bonceCon input:disabled{
        background-color: #dff0ff;
    }
    .bonceCon input:focus,.bonceCon select:focus,.bonceCon textarea:focus{
        border:1px solid #5d9cec;
    }
    .formItem{
        margin-top: 8px;
        overflow: hidden;
        clear: both;
    }
    .formTitle,.formCon{
        float: left;
        line-height: 36px;

    }
    .formTitle{
        width: 200px;
        text-align: right;
        padding:0 10px;
    }
    .formCon{
    }
    #goodEntryBtn{
        margin:10px 200px;
    }
    .question{
        width: 300px;
        height: 35px;
        line-height: 35px;
        background-color: #ffffe1;
        border: 1px dashed #e6d8b6;
        padding: 0 15px;
        color: #b38f6c;;
        margin: 8px 0;
    }
    .recordTitle{
        padding: 0 20px;
        height: 26px;
        line-height: 26px;
        background-color: #f3f3f3;
        color: #666;
        border-left: 4px solid #5d9cec;
        font-weight: bold;
    }
    .recordHeader b{
        font-weight: normal;
        color: #888;
        margin-left: 20px;
    }
    .applyAll{
        width: 100%;
        height: 35px;
        line-height: 35px;
        background-color: #f3f3f3;
        padding: 0 15px;
        color: #666;;
        margin-top: 8px;
        text-align: right;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed"  onclick="imgOut()">
    <div class="bonceContainer bounce-red" id="F_errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red bigImgBnc" id="bigImgBnc" onclick="stopClick(event)" >
        <div id="bigImagcon"  >
            <img id="bigImag" src=""  />

        </div>
    </div>
    <p id="imgController" onclick="stopClick(event)">
        <span class="rotate" title="缩小" onclick="imgChange(0)"><i class="fa fa-compress "></i></span>
        <span class="rotate" title="放大" onclick="imgChange(1)"><i class="fa fa-expand "></i></span>
        <span class="rotate" title="旋转" onclick="rotateImg()"><i class="fa fa-rotate-right "></i></span>
        <span class="closeImg" title="关闭" onclick="imgOut()"><i class="fa fa-remove"></i></span>
    </p>
    <div class="bonceContainer bounce-red" id="deleteGood">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord">您确定要删除此条货物信息吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="sureDeleteGood($(this))">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green" id="goodEntry" style="min-width:600px;">
        <div class="bonceHead">
            <span>货物录入</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="goodEntryCon">
                <div class="formItem">
                    <div class="formTitle"><span><span class="ty-color-red">*</span> 开票日期</span></div>
                    <div class="formCon"><input type="text" class="billingDate" id="billingDate"></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span><span class="ty-color-red">*</span> 货物或应税劳务、服务名称</span></div>
                    <div class="formCon"><input type="text" class="goodName"></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>规格型号</span></div>
                    <div class="formCon"><input type="text" class="specificationType"></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>单位</span></div>
                    <div class="formCon"><input type="text" class="unit"></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span><span class="ty-color-red">*</span> 数量</span></div>
                    <div class="formCon"><input type="text" class="goodNum" onkeyUp = 'clearNum(this)'></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span>单价</span></div>
                    <div class="formCon"><input type="text" class="price" disabled></div>
                </div>
                <div class="formItem">
                    <div class="formTitle"><span><span class="ty-color-red">*</span> 金额</span></div>
                    <div class="formCon"><input type="text" class="amount" onkeyUp = 'clearNoNum(this)'></div>
                </div>
                <div class="taxInput">
                    <div class="formItem">
                        <div class="formTitle"><span>税率</span></div>
                        <div class="formCon"><input type="text" class="taxRate" disabled></div>
                    </div>
                    <div class="formItem">
                        <div class="formTitle"><span><span class="ty-color-red">*</span> 税额</span></div>
                        <div class="formCon"><input type="text" class="tax" onkeyUp = 'clearNoNum(this)'></div>
                    </div>
                    <div class="formItem">
                        <div class="formTitle"><span>含税合计</span></div>
                        <div class="formCon"><input type="text" class="taxTotal" disabled></div>
                    </div>
                </div>
                <input type="hidden" id="goodID">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel() ">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="addGoodBtn" onclick="sureAddGood()" disabled>确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="outStorageCheckOk">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord">您确定复核无误吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureOutStorageCheck($(this),1)">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">取消</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--报销详情--%>
    <div class="bonceContainer bounce-blue" id="contactDetail" style="width: 750px;">
        <div class="bonceHead">
            <span>报销详情：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process">
                    <div class="infoHead ty-process-ttl"><span class="ty-btn ty-btn-green">审批流程</span></div>
                    <div class="conInfo ty-process-container" id="process">
                        <div class="infoList ty-process-item">
                            <p><span class="dot"></span>处理人：财务/超管 </p>
                            <p>2016-02-02 12：00:00</p>
                            <article>意见：</article>
                        </div>
                        <div class="infoList ty-process-item ">
                            <p><span class="dot-no"></span>处理人：财务/超管 </p>
                            <p>2016-02-02 12：00:00</p>
                            <article>意见：</article>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process">
                    <div class="infoHead ty-process-ttl "><span class="ty-btn ty-btn-green">详情信息</span></div>
                    <div class="infoCon">
                        <p class="procon"><span>申请人 ： </span><span id="info_creator"></span></p>
                        <p class="procon"><span>申请时间 ： </span><span id="info_createDate"></span></p>
                        <p class="procon"><span>费用类别 ： </span><span id="info_feeCatName"></span></p>
                        <p class="procon"><span>票据种类 ： </span><span id="info_billCatName"></span></p>
                        <p class="procon"><span>票据所属月份 ： </span><span id="info_billDate"></span></p>
                        <p class="procon"><span>摘要 ： </span><span id="info_summary"></span></p>
                        <p class="procon"><span>用途 ： </span><span id="info_purpose"></span></p>
                        <p class="procon"><span>票据数量 ： </span><span id="info_billQuantity"></span></p>
                        <p class="procon"><span>实际金额 ： </span><span id="info_amount"></span></p>
                        <p class="procon"><span>发票金额 ： </span><span id="info_billAmount"></span></p>
                        <p class="procon"><span>备注 ： </span><span id="info_memo"></span></p>
                        <p class="procon">
                            <span>附件 ：
                               <%-- <div id="bigImagcon" onmouseover="bigImagOver()" onmouseleave="bigImagOut()" onclick="stopClick(event)" >
                                    <p >
                                        <span class="rotate" title="缩小" onclick="imgChange(0)"><i class="fa fa-compress "></i></span>
                                        <span class="rotate" title="放大" onclick="imgChange(1)"><i class="fa fa-expand "></i></span>
                                        <span class="rotate" title="旋转" onclick="rotateImg()"><i class="fa fa-rotate-right "></i></span>
                                        <span class="closeImg" title="关闭" onclick="imgOut()"><i class="fa fa-remove"></i></span>
                                    </p>
                                    <img id="bigImag" src=""  />
                                </div>--%>
                            </span>
                            <span id="info_img"></span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--报销申请页面(新)--%>
    <div class="bonceContainer bounce-green" id="expenseApply" style="width: 1200px;" >
        <div class="bonceHead">
            <span>报销申请</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:650px;overflow: auto ">
            <div class="main">
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 费用类别</span>
                    </div>
                    <div class="formCon">
                        <select class="feeCat" id="feeCat" name="feeCat">
                            <option value="0">---请选择费用类别---</option>
                        </select>
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 票据种类</span>
                    </div>
                    <div class="formCon">
                        <select class="billCat" id="billCat">
                            <option value="">请选择票据种类</option>
                            <option value="0">增值税专用发票</option>
                            <option value="0">增值税普通发票</option>
                            <option value="0">其他普通发票</option>
                            <option value="0">收据</option>
                        </select>
                    </div>
                </div>
            </div>
            <div id="chargeGood" style="margin-left: 200px;display: none;">
                <div class="question">要报销的发票内是否包含多项货物?</div>
                <div class='ty-form-checkbox' skin="green" value="1">
                    <span>是</span>
                    <i class="fa fa-check"></i>
                </div>
                <div class='ty-form-checkbox' skin="green" value="0">
                    <span>否</span>
                    <i class="fa fa-check"></i>
                </div>
            </div>
            <button class="ty-btn ty-btn-big ty-circle-3 ty-btn-green" id="goodEntryBtn" style="display: none">货物录入</button>
            <div id="goodEntryTbl" style="display: none">
                <div class="recordTitle">
                    发票信息
                    <div class="recordHeader ty-right">
                        <b>开票日期：</b><span class="billingDate"></span>
                    </div>
                </div>
                <table class="ty-table ty-table-control">
                    <thead>
                    <tr>
                        <td>货物或应税劳务、服务名称</td>
                        <td>规格型号</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>单价</td>
                        <td>金额</td>
                        <td class="taxInput">税率</td>
                        <td class="taxInput">税额</td>
                        <td class="taxInput">含税合计</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="goodList"></tbody>
                </table>
                <div class="recordTitle" id="accountAll">
                    合计
                    <div class="recordHeader ty-right">
                        <span id="amountAll"><b>金额合计：</b><span class="amountAll"></span></span>
                        <span id="taxAll"><b>税额合计：</b><span class="taxAll"></span></span>
                        <span class="taxTotalAll" style="display: none"></span>
                    </div>
                </div>
            </div>

            <div class="base" style="display: none">
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 票据所属月份</span>
                    </div>
                    <div class="formCon">
                        <select class="billDate" id="billDate" disabled>
                            <option value="1">本月票据</option>
                            <option value="2">非本月票据</option>
                        </select>
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 用途</span>
                    </div>
                    <div class="formCon">
                        <input id="purpose" type="text">
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 票据数量</span>
                    </div>
                    <div class="formCon">
                        <input id="billQuantity" type="text" onkeyup="clearNoNum(this)">
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 实际金额</span>
                    </div>
                    <div class="formCon">
                        <input id="amount" type="text" onkeyup="clearNoNum(this)">
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle">
                        <span><span class="ty-color-red">*</span> 发票金额</span>
                    </div>
                    <div class="formCon">
                        <input id="billAmount" type="text" onkeyup="clearNoNum(this)">
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle">
                        <span>备注</span>
                    </div>
                    <div class="formCon">
                        <input id="memo" type="text">
                    </div>
                </div>
                <div class="formItem">
                    <div class="formTitle">
                        <span>附件</span>
                    </div>
                    <div class="formCon">
                        <input type="file" id="ImportPicInput" name= "imgFile" multiple accept=".jpg,.jpeg,.png,.gif" style=" display: none"/>
                        <div class ="input-append">
                            <input type ="text" class="input-large" id= "importPicName" placeholder="请选择文件" readonly/>
                            <span class ="ty-btn ty-btn-blue ty-circle-3" onclick="$('#ImportPicInput').click();" >浏览</span>
                        </div >
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <%--<p class="tip" id="tip"></p>--%>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-circle-3 ty-btn-green expenseApplyBtn" id="expenseApplyBtn" onclick="sureExpenseApply()">确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-header">
                <h3>报销请求</h3>
                <p>
                    <span class="navTxt"><i class="fa fa-home"></i> 个人中心</span>
                    <span class="nav_"> / </span>
                    <span class="navTxt"><i class=""></i>我的请求</span>
                    <span class="nav_"> / </span>
                    <span class="navTxt"><i class=""></i>报销请求</span>
                    <span class="nav_"> / </span>
                    <span class="navTxt"><i class=""></i><span id="for_n">待处理</span></span>
                </p>
                <span class="ty-right ty-btn ty-btn-big ty-btn-green ty-circle-5 " onclick="expenseApplyBtn()">报销申请</span>
                <ul class="ty-firstTab">
                    <%--<li>加班请求</li>--%>
                    <%--<li>请假请求</li>--%>
                    <li class="ty-active">报销请求</li>
                </ul>
            </div>
            <div class="ty-container">
                <ul class="ty-secondTab" >
                    <li class="ty-active">待处理</li>
                    <li>已批准</li>
                    <li>已驳回</li>
                </ul>
                <div class="ty-mainData">
                    <table class="ty-table ty-table-control " id="tab_15_1">
                        <thead>
                        <td width="10%">序号</td>
                        <td width="18%">申请时间</td>
                        <td width="9%">实际金额</td>
                        <td width="9%">发票金额</td>
                        <td width="18%">费用类别</td>
                        <td width="18%">票据种类</td>
                        <td width="18%">更多</td>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <table class="ty-table hd ty-table-control" id="tab_15_2">
                        <thead>
                        <td width="10%">序号</td>
                        <td width="18%">申请时间</td>
                        <td width="9%">实际金额</td>
                        <td width="9%">发票金额</td>
                        <td width="18%">费用类别</td>
                        <td width="18%">票据种类</td>
                        <td width="18%">更多</td>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <table class="ty-table hd ty-table-control" id="tab_15_3">
                        <thead>
                        <td width="10%">序号</td>
                        <td width="18%">申请时间</td>
                        <td width="9%">实际金额</td>
                        <td width="9%">发票金额</td>
                        <td width="18%">费用类别</td>
                        <td width="18%">票据种类</td>
                        <td width="18%">更多</td>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="page"></div>
                </div>

            </div>
        </div>
    </div>
<%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/user/ajaxfileupload.js?v=SVN_REVISION"></script>
<script src="../script/user/myAccount.js?v=SVN_REVISION"></script>
</body>
</html>
