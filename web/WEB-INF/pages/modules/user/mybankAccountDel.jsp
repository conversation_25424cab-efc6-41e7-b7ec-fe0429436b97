<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2015/12/7
  Time: 15:03
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <style>
        .juZuo{margin-left:180px}
        .juli{margin-top: 10px}
    </style>
    <!-- END HEAD -->
    <script src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>

</head>

<body class="">

<div class="page-content-wrapper juzuo">
    <div class="page-content">
        <!--放内容的地方-->
        <div class="row" >
            <div style="width: 1000px" class="position">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="portlet-title">
                        <div class="caption" style="margin-left: 400px">
                            我的银行账户删除申请 </div>
                    </div>
                    <div class="portlet-body">
                        </br>
                        </br>
                        </br>
                        <form class="form-horizontal" role="form" action="">

                            <div style="margin-left:190px;margin-bottom: 30px">
                                状态：<c:if test="${approvalStatus==0}">未提交</c:if>
                                <c:if test="${approvalStatus==1}">待处理</c:if>
                                <c:if test="${approvalStatus==2}">已批准</c:if>
                                <c:if test="${approvalStatus==3}">已驳回</c:if>
                            </div>

                            <div class="form-horizontal">

                                <div class="form-group">
                                    <label for="firstname" class="col-sm-3 control-label">开户行</label>
                                    <div class="col-sm-6">
                                        <input type="text" class="form-control" id="firstname" value="${fa.bankName}"
                                               placeholder="" readonly="true">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="firstname" class="col-sm-3 control-label">账号</label>
                                    <div class="col-sm-6">
                                        <input type="text" class="form-control" id="secondname" value="${fa.account}"
                                               placeholder="" readonly="true">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="firstname" class="col-sm-3 control-label">户名</label>
                                    <div class="col-sm-6">
                                        <input type="text" class="form-control" id="thirdname" value="${fa.name}"
                                               placeholder="" readonly="true">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="firstname" class="col-sm-3 control-label">初始资金</label>
                                    <div class="col-sm-6">
                                        <input type="text" class="form-control" id="fourthname" value="${fa.balance}"
                                               placeholder="" readonly="true">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="firstname" class="col-sm-3 control-label">账户状态</label>
                                    <div class="col-sm-6">
                                        <c:if test="${fa.accountStatus!=0}">
                                            <input type="text" class="form-control" id="fivename" value="正常"
                                                   placeholder="" readonly="true">
                                        </c:if>
                                        <c:if test="${fa.accountStatus==0}">
                                            <input type="text" class="form-control" id="fivename" value="冻结"
                                                   placeholder="" readonly="true">
                                        </c:if>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="firstname" class="col-sm-3 control-label">账户类型</label>
                                    <div class="col-sm-6">
                                        <input type="text" class="form-control" readonly="true"  name="operation" value="${fa.operation}"/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="firstname" class="col-sm-3 control-label">备注</label>
                                    <textarea style="width:400px;margin-left: 17px" rows="4" readonly="true">${fa.memo}</textarea>
                                </div>

                                <div class="form-group">
                                    <label for="firstname" class="col-sm-3 control-label">删除说明</label>
                                    <textarea style="width:400px;margin-left: 17px" rows="4" readonly="true">${fa.illustrate}</textarea>
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%@ include  file="../../common/footerScript.jsp"%>

</body>
</html>