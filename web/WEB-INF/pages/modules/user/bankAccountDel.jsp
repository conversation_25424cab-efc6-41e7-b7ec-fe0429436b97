<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
    .juZuo{margin-left:180px}
</style>
</head>

<body class="">

<div class="page-content-wrapper juzuo">
    <div class="page-content">
        <!--放内容的地方-->
        <div class="row" >
            <div style="width: 1000px" class="position">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="portlet-title">
                        <div class="caption" style="margin-left: 450px">
                          银行账户删除申请 </div>
                    </div>
                    <div class="portlet-body">
                        </br>
                        </br>
                        </br>
                        <div class="form-horizontal">

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">开户行</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="firstname" value="${fa.bankName}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">账号</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="secondname" value="${fa.account}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">户名</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="thirdname" value="${fa.name}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">初始资金</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="fourthname" value="${fa.balance}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">账户状态</label>
                                <div class="col-sm-6">
                                    <c:if test="${fa.accountStatus!=0}">
                                        <input type="text" class="form-control" id="fivename" value="正常"
                                               placeholder="" readonly="true">
                                    </c:if>
                                    <c:if test="${fa.accountStatus==0}">
                                        <input type="text" class="form-control" id="fivename" value="冻结"
                                               placeholder="" readonly="true">
                                    </c:if>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">账户类型</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" readonly="true"  name="operation" value="${fa.operation}"/>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">备注</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4" readonly="true">${fa.memo}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">删除说明</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4" readonly="true">${fa.illustrate}</textarea>
                            </div>

                        </div>
                    </div>
                    <div class="modal-footer">
                        <form action="../user/deleteAccountById.do" method="post" id="formDel">
                        <input type="hidden" name="submitType" id="submitType" value=""/>
                        <input type="hidden" name="umId" value="${umId}">
                        <input type="hidden" name="id" value="${fa.id}">
                        <%--<div class="modal-footer">--%>
                            <c:if test="${approvalStatus!=2 && approvalStatus!=3}">
                                <input type="button" class="btn white" style="width:125px;color: red"
                                       value="批准" onclick="tijiaoType(1)"/>
                                <input type="button" class="btn white" style="width:125px;color: green"
                                       value="驳回" onclick="tijiaoType(0)"/>
                            </c:if>
                        <%--</div>--%>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    function tijiaoType(type){
        $("#submitType").val(type);
        $("#formDel").submit();
    }
</script>
</body>
</html>
