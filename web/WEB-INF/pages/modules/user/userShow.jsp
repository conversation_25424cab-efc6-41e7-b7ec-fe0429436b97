<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/green.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/user/userShow.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%--空间与流量--%>
    <div class="bonceContainer bounce-blue" id="spaceandflow">
        <div class="bonceHead">
            <span>空间与流量</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="mainCon">
                <div class="item-flex toptitle">
                    <div class="item-title instiuioname">机构名称</div>
                    <div class="item-content"><span class="privteprorty">xxx的私人领地</span></div>
                </div>
                <div class="item-flex">
                    <div class="item-title">空间上限</div>
                    <div class="item-content"><span class="spueup">XXX.XXG</span></div>
                </div>
                <div class="item-flex toptitle">
                    <div class="item-title">已用空间</div>
                    <div class="item-content"><span class="usespue">XXX.XXG</span></div>
                </div>
                <div class="ty-alert">
                    <p class="pname"></p>
<%--                    2021年1月1日-<d>XXXX</d>年<e>XX</e>月<c>XX</c>日--%>
                </div>
                <div class="item-flex">
                    <div class="item-title">期间的流量上限</div>
                    <div class="item-content"><span class="trafficlimit">XXX.XXG</span></div>
                </div>
                <div class="item-flex">
                    <div class="item-title">此期间已用流量</div>
                    <div class="item-content"><span class="usetramit">XXX.XXG</span></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<style>
    .btn-group{
        flex: auto;
    }
</style>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>用户展示</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <input id="pageNum" type="hidden" value="1" />
            <div class="ty-container">
                <%--主页面--%>
                <div class="ty-alert">
                    以下为系统内状态为“正常”的用户，共<span class="authAccNum ty-color-blue"></span>条
                    <div class="btn-group text-right">
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" disabled>已释放的手机号</button>
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" disabled>已注销的用户</button>
                    </div>
                </div>
                <table class="ty-table" id="mainList">
                    <thead>
                    <tr>
                        <td>账号</td>
                        <td>手机号</td>
                        <td>来源</td>
                        <td>所属机构个数</td>
                        <td>进入系统的时间</td>
                        <td>最新登录的时间</td>
                        <td>活跃指数</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div id="ye_authAcc"></div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/user/userShow.js?v=SVN_REVISION"></script>
</body>
</html>
