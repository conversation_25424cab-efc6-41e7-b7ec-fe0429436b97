<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

<style>
    body{
        background-color: #fff;
    }
    .juzuo{overflow: hidden}
    .juli{margin-top: 10px}
    .portlet{
        width: 800px;
        margin:0 auto;
        margin-top:80px;
    }
</style>

<script src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>
<script src="../My97DatePicker/WdatePicker.js?v=SVN_REVISION"></script>


<script type="text/javascript">
    function tijiao(){
        var bankAccountId=$("#bankAccountId").val();
        $.ajax({
            url:'../user/applicationForBankAmeVerification.do?bankAccountId='+bankAccountId,
            dataType:'Text',
            success:function(course){
                if (course=="yes"){
                    window.open ('../user/yanZheng.do','newwindow','height=400,width=1000,toolbar=no,menubar=no,scrollbars=no, resizable=no,location=no, status=no')
                }else{
                    $("#form1").submit();
                }
            }
        })
    }
</script>

<body>

<div class="page-content-wrapper juzuo">
    <div class="content">
        <!--放内容的地方-->
        <div class="row" >
            <div class="portlet">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="portlet-title">
                        <div class="caption text-center">
                            <h2>加班申请处理</h2>
                            <hr>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover" id="sample_1">
                            <tr>
                                <th>申请人 </th>
                                <th>${personnelOvertime.user.userName}</th>
                            </tr>
                            <tr>
                                <th>申请时间</th>
                                <th>${fn:substring(personnelOvertime.createDate , 0, 19)}<%--${fn:substring(personnelOvertime.beginTime, 0, 10)}--%></th>
                            </tr>
                            <tr>
                                <th>开始时间 </th>
                                <th>${fn:substring(personnelOvertime.beginTime , 0, 19)}</th>
                            </tr>
                            <tr>
                                <th>序号</th>
                                <th>${personnelOvertime.id}</th>
                            </tr>
                            <tr>
                                <th>结束时间   </th>
                                <th>${fn:substring(personnelOvertime.endTime , 0, 19)}</th>
                            </tr>
                            <tr>
                                <th>时长（小时） </th>
                                <th>${personnelOvertime.duration}</th>
                            </tr>
                            <tr>
                                <th>加班类型 </th>
                                <th><c:if test="${personnelOvertime.type==1}">工作日加班</c:if>
                                    <c:if test="${personnelOvertime.type==2}">周末假日加班</c:if>
                                    <c:if test="${personnelOvertime.type==3}">法定节假日加班</c:if>
                                </th>
                            </tr>
                            <tr>
                                <th>加班内容 </th>
                                <th>${personnelOvertime.reason}</th>
                            </tr>

                        </table>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
