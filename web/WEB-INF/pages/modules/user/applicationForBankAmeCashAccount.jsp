<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <style>
        .juZuo{margin-left:180px}
        .juli{margin-top: 10px}
        .btn{ border:1px solid #00a0e9 ; margin-top:15px;   }
        .btn:hover{ background:#eaeaea; color:#444;  }
        textarea{ border:1px solid #ccc;  }
    </style>
</head>

<body style="background:#fff; ">

<div class="  juzuo">
    <div class="page-content">
        <!--放内容的地方-->
        <div class="" >
            <div style="width: 1000px" class="position">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="portlet-title">
                        <div class="caption" style="margin-left: 350px">
                            <h2>银行账户修改申请</h2>
                        </div>
                    </div>
                    <div class="portlet-body">
                        </br>
                        </br>
                        </br>
                        <form class="form-horizontal" role="form" action="../user/submitForBankAmeCashAccount.do" id="form1" method="post">
                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">开户行</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="firstname" value="${financeAccount.bankName}"
                                           placeholder="" name="bankName">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">账号</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="secondname" value="${financeAccount.account}"
                                           placeholder="" name="account">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">账户名称</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="thirdname" value="${financeAccount.name}"
                                           placeholder="" name="name">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">初始资金</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="fourthname" value="${financeAccount.initialAmount}"
                                           placeholder="" name="initialAmount">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">账户状态</label>
                                <div class="col-sm-6">
                                    <select class="form-control" name="accountStatus">
                                        <c:if test="${financeAccount.accountStatus!=0}">
                                            <option value="1">正常</option>
                                            <option value="0">冻结</option>
                                        </c:if>
                                        <c:if test="${financeAccount.accountStatus==0}">
                                            <option value="0">冻结</option>
                                            <option value="1">正常</option>
                                        </c:if>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">账户类型</label>
                                <div class="col-sm-6">
                                    <select class="form-control" name="operation">
                                         <c:if test="${financeAccount.operation=='非基本户'}">
                                             <option value="非基本户">非基本户</option>
                                             <option value="基本户">基本户</option>
                                        </c:if>
                                        <c:if test="${financeAccount.operation=='基本户'}">
                                            <option value="基本户">基本户</option>
                                            <option value="非基本户">非基本户</option>
                                        </c:if>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">可否取现</label>
                                <div class="col-md-6">
                                    <select class="form-control" name="cashable">
                                        <c:choose>
                                            <c:when test="${financeAccount.accountType==1}">--</c:when>
                                            <c:otherwise>
                                                <c:if test="${financeAccount.cashable==1}">
                                                    <option value="1" selected="selected">可取现</option>
                                                    <option value="2">不可取现</option>
                                                </c:if>
                                                <c:if test="${financeAccount.cashable!=1}">
                                                    <option value="1">可取现</option>
                                                    <option value="2" selected="selected">不可取现</option>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">备注</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4" name="memo">${financeAccount.memo}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">修改说明</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4" name="illustrate">${financeAccount.illustrate}</textarea>
                            </div>
                            <input type="hidden" name="bankAccountId" value="${financeAccount.id}" id="bankAccountId">
                        </form>

                    </div>
                    <div class="modal-footer">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-btn-circle-5" onclick="tijiao()">提交</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<%@ include  file="../../common/footerScript.jsp"%>

<script type="text/javascript">
    function tijiao(){
        var bankAccountId=$("#bankAccountId").val();
        $.ajax({
            url:'../user/applicationForBankAmeVerification.do?bankAccountId='+bankAccountId,
            dataType:'Text',
            success:function(course){
                if (course=="yes"){
                    window.open ('../user/yanZheng.do','newwindow','height=400,width=1000,toolbar=no,menubar=no,scrollbars=no, resizable=no,location=no, status=no')
                }else{
                    $("#form1").submit();
                }
            }
        })
    }
</script>
</body>
</html>
