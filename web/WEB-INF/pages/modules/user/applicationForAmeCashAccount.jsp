<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2015/12/7
  Time: 15:03
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

    <style>
        .juZuo{margin-left:180px}
        .juli{margin-top: 10px}
    </style>

    <script src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>

</head>

<body class="">

<div class="page-content-wrapper juzuo">
    <div class="page-content">
        <!--放内容的地方-->
        <div class="row" >
            <div style="width: 1000px" class="position">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="portlet-title">
                        <div class="caption" style="margin-left: 350px">
                            <h2>现金账户修改申请</h2>
                        </div>
                    </div>
                    <div class="portlet-body">
                        </br>
                        </br>
                        </br>
                        <form class="form-horizontal" role="form" action="../user/submitForAmeCashAccount.do" id="form1" method="post">
                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">摘要</label>
                                <textarea name="summary" style="width:400px;margin-left: 17px" rows="4">${accountDetail.summary}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">发生时间</label>
                                <div class="col-sm-6">
                                    <input type="text" name="createDate1" class="form-control" id="firstname" value="${accountDetail.createDate}"
                                           placeholder="" onclick="WdatePicker();">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">方式</label>
                                <div class="col-sm-6">
                                    <select class="form-control" name="accountStatus">
                                        <c:if test="${accountDetail.debit!=null}">
                                            <option value="1">支出</option>
                                            <option value="0">收入</option>
                                        </c:if>
                                        <c:if test="${accountDetail.credit!=null}">
                                            <option value="0">收入</option>
                                            <option value="1">支出</option>
                                        </c:if>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">金额</label>
                                <div class="col-sm-6">
                                    <c:if test="${accountDetail.debit!=null}">
                                        <input type="text" class="form-control" value="${accountDetail.debit}"
                                               placeholder="" name="money"/>
                                    </c:if>
                                    <c:if test="${accountDetail.credit!=null}">
                                        <input type="text" class="form-control" value="${accountDetail.credit}"
                                               placeholder="" name="money"/>
                                    </c:if>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">经手人</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="fourthname" value="${accountDetail.auditorName}"
                                           placeholder=""  name="auditorName">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">备注</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="fivename" value="${accountDetail.memo}"
                                           placeholder=""  name="memo">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">说明</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4"  name="illustrate">${accountDetail.illustrate}</textarea>
                            </div>

                            <input type="hidden" name="accountId" id="accountId" value="${accountDetail.id}">
                            <input type="hidden" name="accountType" value="${accountType}">
                            <input type="hidden" name="fid" value="${fid}">
                        </form>

                    </div>
                    <div class="modal-footer">
                                <input type="button" class="btn white" style="width:125px;color: red"
                                       value="提交" onclick="tijiao()"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<%--<%@ include  file="../../common/footerTop.jsp"%>--%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    function tijiao(){
        var accountId=$("#accountId").val();
        $.ajax({
            url:'../user/applicationForAmeVerification.do?accountId='+accountId,
            dataType:'Text',
            success:function(course){
                if (course=="yes"){
                    window.open ('../user/yanZheng.do','newwindow','height=400,width=1000,toolbar=no,menubar=no,scrollbars=no, resizable=no,location=no, status=no')
                }else{
                    $("#form1").submit();
                }
            }
        })
    }
</script>
</body>
</html>
