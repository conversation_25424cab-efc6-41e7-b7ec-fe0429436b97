<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2016-06-20
  Time: 10:06
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
        .hd{display: none}
    </style>

    <script src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>
    <script type="text/javascript">
        function tia(){
            $("#form1").submit();
        }
    </script>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <p align="center">请求处理</p>
            <form action="../user/requestManagement.do" method="post" id="form1">

            <div class="m-heading-1 border-green m-bordered">
                <div class="portlet-body">
                            <i class="icon-settings font-dark"></i>
                            状态：<select name="approvalStatus" onchange="tia()">
                            <option value="0">请选择</option>
                            <option value="1">待处理</option>
                            <option value="2">已批准</option>
                            <option value="3">已驳回</option>
                        </select>
                </div>
            </div>
            </form>
            <table class="table table-striped table-bordered table-hover" id="sample_1">
                <tr>
                    <th>序号</th>
                    <th>处理事项</th>
                    <th>申请时间</th>
                    <th>状态</th>
                    <th>处理人</th>
                    <th>处理时间</th>
                    <th>操作</th>
                </tr>
                <c:forEach items="${userMessages}" var="um">
                <tr>
                    <td>${um.id}</td>
                    <td>来自${um.content}<c:if test="${um.accountOrDetailed==1}">账户</c:if><c:if test="${um.accountOrDetailed==2}">明细</c:if>的<c:if test="${um.operationType==1}"><font color="red">新增</font></c:if><c:if test="${um.operationType==2}"><font color="red">删除</font></c:if><c:if test="${um.operationType==3}"><font color="red">修改</font></c:if>申请</td>
                    <td>${um.createDate}</td>
                    <td>
                        <c:if test="${um.approvalStatus==1}">待处理</c:if>
                        <c:if test="${um.approvalStatus==2}">已批准</c:if>
                        <c:if test="${um.approvalStatus==3}">已驳回</c:if>
                    </td>
                    <td>${um.user.userName}</td>
                    <td>${um.handleTime}</td>
                    <td>
                        <c:if test="${um.accountOrDetailed=='1'&& um.operationType=='3'}">
                        <input type="button" class="btn green" value="查看" onclick="openWindow('../user/bankAccountChange.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                        </c:if>
                        <c:if test="${um.accountOrDetailed=='1'&& um.operationType=='2'}">
                        <input type="button" class="btn green" value="查看" onclick="openWindow('../user/bankAccountDel.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                        </c:if>
                        <c:if test="${um.accountOrDetailed=='2'&& um.operationType=='2'}">
                        <input type="button" class="btn green" value="查看" onclick="openWindow('../user/cashAccountDel.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                        </c:if>
                        <c:if test="${um.accountOrDetailed=='2'&& um.operationType=='3'}">
                        <input type="button" class="btn green" value="查看" onclick="openWindow('../user/cashAccountChange.do?id=${um.accountId}&umId=${um.id}&approvalStatus=${um.approvalStatus}','1300','800')"/>
                        </c:if>
                    </td>
                </tr>
                </c:forEach>
            </table>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/function.js?v=SVN_REVISION"></script>
</body>
</html>
