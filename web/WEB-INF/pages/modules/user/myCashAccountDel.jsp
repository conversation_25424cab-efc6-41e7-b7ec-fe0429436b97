<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2015/12/7
  Time: 15:03
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <style>
        .juZuo{margin-left:180px}
        .juli{margin-top: 10px}
    </style>
</head>

<body class="">

<div class="page-content-wrapper juzuo">
    <div class="page-content">
        <!--放内容的地方-->
        <div class="row" >
            <div style="width: 1000px" class="position">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="portlet-title">
                        <div class="caption" style="margin-left: 350px">
                            <h2>我的现金账户删除申请</h2>
                        </div>
                    </div>
                    <div class="portlet-body">
                        </br>
                        </br>
                        </br>
                        <form class="form-horizontal" role="form" action="">

                            <div style="margin-left:190px;margin-bottom: 30px">
                                状态：<c:if test="${approvalStatus==0}">未提交</c:if>
                                <c:if test="${approvalStatus==1}">待处理</c:if>
                                <c:if test="${approvalStatus==2}">已批准</c:if>
                                <c:if test="${approvalStatus==3}">已驳回</c:if>
                            </div>

                            <div class="form-group">
                                <label for="firstname2" class="col-sm-3 control-label">账户</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="firstname2" value="${ad.accountName}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">摘要</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4" readonly="true" value="">${ad.summary}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">发生时间</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="firstname" value="${ad.createDate}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">方式</label>
                                <div class="col-sm-6">
                                    <c:if test="${ad.debit!=null}">
                                        <input type="text" class="form-control" id="secondname" value="支出"
                                               placeholder="" readonly="true"/>
                                    </c:if>
                                    <c:if test="${ad.credit!=null}">
                                        <input type="text" class="form-control" id="secondname" value="收入"
                                               placeholder="" readonly="true"/>
                                    </c:if>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">金额</label>
                                <div class="col-sm-6">
                                    <c:if test="${ad.debit!=null}">
                                        <input type="text" class="form-control" value="${ad.debit}"
                                               placeholder="" readonly="true"/>
                                    </c:if>
                                    <c:if test="${ad.credit!=null}">
                                        <input type="text" class="form-control" value="${ad.credit}"
                                               placeholder="" readonly="true"/>
                                    </c:if>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">经手人</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="fourthname" value="${ad.auditorName}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">备注</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="fivename" value="${ad.memo}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">删除说明</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4" readonly="true">${ad.illustrate}</textarea>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/function.js?v=SVN_REVISION"></script>

</body>
</html>
