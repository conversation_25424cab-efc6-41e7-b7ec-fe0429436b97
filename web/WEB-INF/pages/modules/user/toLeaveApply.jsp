<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<style>
   body{  background-color: #fff;  }
    input[type=text]{  height:35px;  }
    #tip{ color:red;  }
</style>
<body>
    <div class="portlet box green">
        <div class="portlet-title">
            <div class="caption text-center">
                <h2>请假申请</h2>
                <hr>
            </div>
        </div>
        <div class="portlet-body">
            <form class="form-horizontal" role="form" action=" ../lo/addLeave.do" id="form1" method="post">
                <div class="form-group">
                    <label class="col-sm-3 control-label">开始时间</label>
                    <div class="col-sm-6">
                           <input type="text" name="beginTime1" id="startDate" class="laydate-icon form-control" placeholder="请选择时间" >
                    </div>
                </div>
                <div class="form-group">
                    <label  class="col-sm-3 control-label">结束时间</label>
                    <div class="col-sm-6">
                        <input type="text" name="endTime1" id="endDate" class="laydate-icon form-control" placeholder="请选择时间" >
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-3 control-label">请假类型</label>

                    <div class="col-sm-6">
                        <select class="form-control" name="type" id="type">
                            <option value="">请选择</option>
                            <option value="1">事假</option>
                            <option value="2">病假</option>
                            <option value="3">年假</option>
                            <option value="4">调休</option>
                            <option value="5">婚假</option>
                            <option value="6">产假</option>
                            <option value="7">陪产假</option>
                            <option value="8">路途假</option>
                            <option value="9">其他</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-3 control-label">请假事由</label>
                    <div class="col-sm-6">
                        <textarea name="reason" id="reason" class="form-control"></textarea>
                        <hr>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-6 col-sm-offset-3">
                        <p class="pull-right" id="tip"></p><br/>
                    </div>
                    <div class="col-sm-6 col-sm-offset-3">
                        <input type="button" class="btn blue pull-right" style="width:125px;color: #fff" value="提交" onclick="tijiao()"/>
                    </div>
                </div>
            </form>
        </div>
    </div>

<script src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>
<script src="../assets/laydate/laydate.js?v=SVN_REVISION" type="text/javascript"></script>

<script type="text/javascript">
    function tijiao(){
        var startDate = $("#endDate").val() ;
        var endDate = $("#endDate").val() ;
        var type = $("#type").val() ;
        var reason = $("#reason").val() ;
        if($.trim(startDate) == "" ){ $("#tip").html("开始时间不能为空") ; return false ;  }
        if($.trim(endDate) == "" ){ $("#tip").html("结束时间不能为空") ; return false ;  }
        if($.trim(type) == "" ){ $("#tip").html("请假类型不能为空") ; return false ;  }
        if($.trim(reason) == "" ){ $("#tip").html("请假事由不能为空") ; return false ;  }
        $("#form1").submit();

    }
    laydate({ elem: '#startDate', format: 'YYYY-MM-DD hh:mm:ss',istime: true , istoday: true ,festival: true   }) ;
    laydate({ elem: '#endDate', format: 'YYYY-MM-DD hh:mm:ss',istime: true , istoday: true ,festival: true   }) ;
</script>
</body>
</html>
