<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2016/10/20
  Time: 11:20
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

<style>
    body{
        background-color: #fff;
    }
    .juzuo{overflow: hidden}
    .juli{margin-top: 10px}
    .portlet{
        width: 1000px;
        margin:0 auto;
        margin-top:80px;
    }
</style>

<script src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>
<script src="../My97DatePicker/WdatePicker.js?v=SVN_REVISION"></script>
<script type="text/javascript">
    function tijiao(){
                    $("#form1").submit();
    }
</script>
<body>
<div class="page-content-wrapper juzuo">
    <div class="content">
        <!--放内容的地方-->
        <div class="row" >
            <div style="width: 1000px" class="portlet">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="portlet-title">
                        <div class="caption text-center">
                            <h2>加班申请</h2>
                            <hr>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <form class="form-horizontal" role="form" action="../user/addPersonnelOvertime.do" id="form1" method="post">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">计划开始时间</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control"  value="" id="startDate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'});"
                                           placeholder="请选择时间" name="beginTime1" onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endDate\')}'})">
                                </div>
                            </div>
                            <div class="form-group">
                                <label  class="col-sm-3 control-label">计划结束时间</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" value="" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'});"
                                           placeholder="请选择时间" name="endTime1" id="endDate" onfocus="WdatePicker({minDate:'#F{$dp.$D(\'startDate\')}'})">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">加班时段</label>

                                <div class="col-sm-6">
                                    <select class="form-control" name="type">
                                            <option value="">请选择</option>
                                            <option value="1">工作日加班</option>
                                            <option value="2">周末假日加班</option>
                                            <option value="3">法定节日加班</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">加班事由</label>
                                <div class="col-sm-6">
                                    <textarea class="form-control" name="reason" ></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-6 col-sm-offset-3">
                                    <input type="button" class="btn blue pull-right" style="width:125px;color: #fff"
                                           value="提交" onclick="tijiao()"/>
                                </div>
                            </div>
                        </form>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
