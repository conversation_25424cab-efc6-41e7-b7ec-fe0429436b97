<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/user/resource_history.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
    .ty-panel{  background: none;    }
    .ty-colFileTree{ float: left ;  }
    .mar{ float:left; padding:0 50px 50px 10px;  }
    .ty-fileList{ margin-top: 8px; }
    #scanSet { width:760px;  }
    .default_img{margin:auto;width:128px;}
    .default_img img{width:100%;vertical-align:middle;}
    .initialSect{margin-top:100px;}
    .initialSect p{text-align:center;}
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%--上传文件弹窗--%>
    <div class="bonceContainer bounce-blue bounce-uploadFile" style="width: 440px">
        <div class="bonceHead">
            <span>文件上传</span>
            <a class="bounce_close" onclick="chargeXhr('uploadFileBtn')"></a>
        </div>
        <div class="bonceCon clearfix">
            <div style="clear:both;width:200px;">
                <p>文件类别</p>
                <input type="text" class="ty-inputText" id="folderName" disabled="disabled"/>
            </div>
            <div class="ty-left right-line">
                <p>文件名称-<span class="ty-color-red">必填</span></p>
                <input type="text" class="ty-inputText" id="fileName">
            </div>
            <div class="ty-right">
                <p>文件编号</p>
                <input type="text" class="ty-inputText" id="fileNo">
            </div>
            <div style="clear:both;"></div>
            <div class="Upload fileUpload"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="chargeXhr('uploadFileBtn')">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big" id="uploadFileBtn" disabled="disabled" onclick="sureUploadNewFile()">确定</button>

        </div>
    </div>
    <%--换版上传文件弹窗--%>
    <div class="bonceContainer bounce-blue bounce-CVuploadFile" style="width: 440px">
        <div class="bonceHead">
            <span>换版文件上传</span>
            <a class="bounce_close" onclick="chargeXhr('CVuploadFileBtn')"></a>
        </div>
        <div class="bonceCon clearfix">
            <p class="hd updateFile"></p>
            <div>
                <p>文件类别</p><input type="text" class="ty-inputText" id="CV-folderName" disabled="disabled">
            </div>
            <div class="ty-left right-line">
                <p>文件名称</p>
                <input type="text" class="ty-inputText" id="CV-fileName" disabled="disabled">

            </div>
            <div class="ty-right">
                <p>文件编号</p>
                <input type="text" class="ty-inputText" id="CV-fileNo" disabled="disabled">
            </div>
                <a href="../upload/"></a>
            <div style="clear: both"></div>
            <div class="Upload CV-fileUpload"><div class="fileType ty-fileDoc hd"></div><span class="tips">请选择文件上传</span></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="chargeXhr('CVuploadFileBtn')">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big" id="CVuploadFileBtn" disabled="disabled" onclick="sureChangeVersion()">确定</button>
        </div>
    </div>
    <%--移动弹窗--%>
    <div class="bonceContainer bounce-blue bounce-RemoveFile">
        <div class="bonceHead">
            <span>移动到</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon ty-colFileTree" id="removeTree" style="height:400px;width:100%;overflow: auto">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3"  id="removeFilesure" onclick="removeSure()">确定</button>
        </div>
    </div>
    <%--删除文件弹窗的确定弹窗--%>
    <div class="bonceContainer bounce-red bounce-deleteFile">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="deleteId" class="hd"></p>
            <span id="deleteTip">您确定删除该文件吗？删除将不可修复。</span>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <button class="ty-btn ty-btn-red ty-btn-big ty-circle-3"  id="delteFilesure" onclick="deleteFileSure()">确定</button>
        </div>
    </div>
    <%--移动弹窗的确定弹窗--%>
    <div class="bonceContainer bounce-blue bounce-RemoveSure">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <span id="removeTip"></span>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn ty-btn-blue ty-btn-big" disabled="disabled" onclick="bouncesure()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>文件管理</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content clearfix">
            <!--放内容的地方-->
            <div class="ty-container clearfix">
                <span class="ty-btn ty-btn-big ty-btn-green ty-right uploadBtn" onclick="uploadNewFile()">上传文件</span>
                <div>
                    <div class="ty-mainDate initialSect hd">
                        <div>
                            <div class="default_img">
                                <img src="../assets/oralResource/initImg.png" />
                            </div>
                            <p>您还没有设置类别，点击<span class="ty-btn ty-btn-green" onclick="$('.mja').parent().click();location.href='../reference/goResourceCatory.do?state=0'">新增类别</span></p>
                        </div>
                    </div>
                    <div class="ty-fileContent">
                        <div class="ty-colFileTree mainContent" id="menuTree"></div>
                        <div class="ty-mainData mar">
                            <div class="ty-fileList">
                                <%--文件列表--%>
                            </div>

                            <div id="ye_con" class="hd" style="width:640px;"></div>
                        </div>
                        <div class="ty-filePreview ty-left">
                            <div class="ty-fileDetail">
                                <%--文件详细内容--%>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>

<script src="../script/user/resource_wenjian.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/downLoadFile/downLoadFile.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
