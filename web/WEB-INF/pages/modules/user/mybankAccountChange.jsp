<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2015/12/7
  Time: 15:03
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <style>
        .juzuo{margin-left:50px}
        .juli{margin-top: 10px}
        .juli1{margin-left: 50px}
    </style>
</head>

<body class="">

<div class="page-content-wrapper juzuo">
    <div class="page-content">
        <!--放内容的地方-->
        <div style="width: 1200px" class="position">
            <!-- BEGIN SAMPLE TABLE PORTLET-->
            <div class="portlet box green">
                <div class="portlet-title">
                    <div class="caption" style="margin-left: 520px">
                        我的银行账户修改申请 </div>
                </div>
                <div class="portlet-body">
                    </br>
                    </br>
                    </br>
                    <form class="form-horizontal" role="form" action="">

                        <div style="margin-left: 1000px;margin-bottom: 30px">
                            状态：<c:if test="${approvalStatus==0}">未提交</c:if>
                            <c:if test="${approvalStatus==1}">待处理</c:if>
                            <c:if test="${approvalStatus==2}">已批准</c:if>
                            <c:if test="${approvalStatus==3}">已驳回</c:if>
                        </div>

                        <div class="row juli1">
                            <div class="col-md-6">
                                <%--第一个--%>
                                <div class="form-group">

                                    <div style="width: 500px" >
                                        <!-- BEGIN SAMPLE TABLE PORTLET-->
                                        <div class="portlet box green">
                                            <div class="portlet-title">
                                                <div class="caption">
                                                    我的银行账户修改之前 </div>
                                            </div>
                                            <div class="portlet-body">
                                                </br>
                                                </br>
                                                </br>
                                                <form class="form-horizontal" role="form" action="">

                                                    <div class="form-group">
                                                        <label for="firstname" class="col-sm-3 control-label">开户行</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" id="firstname" value="${old.bankName}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="secondname" class="col-sm-3 control-label">账号</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" id="secondname" value="${old.account}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="thirdname" class="col-sm-3 control-label">户名</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" id="thirdname" value="${old.name}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="fourthname" class="col-sm-3 control-label">初始资金</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" id="fourthname" value="${old.initialAmount}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="fivename" class="col-sm-3 control-label">账户状态</label>
                                                        <div class="col-sm-6">
                                                            <c:if test="${old.accountStatus!=0}">
                                                                <input type="text" class="form-control" id="fivename" value="正常"
                                                                       placeholder="" readonly="true">
                                                            </c:if>
                                                            <c:if test="${old.accountStatus==0}">
                                                                <input type="text" class="form-control" id="fivename" value="冻结"
                                                                       placeholder="" readonly="true">
                                                            </c:if>
                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="sixname" class="col-sm-3 control-label">账户类型</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" id="sixname" value="${old.operation}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="col-sm-3 control-label">备注</label>
                                                        <textarea style="width:300px;margin-left: 17px" rows="4" readonly="true">${old.memo}</textarea>
                                                    </div>

                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <%--第二个--%>
                                <div class="form-group">

                                    <div style="width: 500px" >
                                        <!-- BEGIN SAMPLE TABLE PORTLET-->
                                        <div class="portlet box green">
                                            <div class="portlet-title">
                                                <div class="caption">
                                                    我的银行账户修改之后 </div>
                                            </div>
                                            <div class="portlet-body">
                                                </br>
                                                </br>
                                                </br>
                                                <form class="form-horizontal" role="form" action="">

                                                    <div class="form-group">
                                                        <label for="firstname1" class="col-sm-3 control-label">开户行</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" name="bankName" id="firstname1" value="${newf.bankName}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="secondname1" class="col-sm-3 control-label">账号</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" name="account" id="secondname1" value="${newf.account}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="thirdname1" class="col-sm-3 control-label">户名</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" id="thirdname1" name="name" value="${newf.name}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="fourthname1" class="col-sm-3 control-label">初始资金</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" id="fourthname1" name="balance" value="${newf.initialAmount}"
                                                                   placeholder="" readonly="true">
                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="col-sm-3 control-label">账户状态</label>
                                                        <div class="col-sm-6">
                                                            <c:if test="${newf.accountStatus!=0}">
                                                                <input type="text" class="form-control" id="fivename" value="正常"
                                                                       placeholder="" readonly="true">
                                                            </c:if>
                                                            <c:if test="${newf.accountStatus==0}">
                                                                <input type="text" class="form-control" id="fivename" value="冻结"
                                                                       placeholder="" readonly="true">
                                                            </c:if>
                                                        </div>
                                                        <%--<select class="form-control" name="accountStatus" style="width: 200px;margin-left: 140px">--%>
                                                        <%--<c:if test="${newf.accountStatus==0}">--%>
                                                        <%--<option value="0">冻结</option>--%>
                                                        <%--<option value="1">正常</option>--%>
                                                        <%--</c:if>--%>
                                                        <%--<c:if test="${newf.accountStatus==1}">--%>
                                                        <%--<option value="1">正常</option>--%>
                                                        <%--<option value="0">冻结</option>--%>
                                                        <%--</c:if>--%>
                                                        <%--</select>--%>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="col-sm-3 control-label">账户类型</label>
                                                        <div class="col-sm-6">
                                                            <input type="text" class="form-control" readonly="true"  name="operation" value="${newf.operation}"/>
                                                        </div>
                                                        <%--<select class="form-control" name="operation" style="width: 200px;margin-left: 140px">--%>
                                                        <%--<option value="">请选择账户类型</option>--%>
                                                        <%--<option value="基本户">基本户</option>--%>
                                                        <%--</select>--%>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="col-sm-3 control-label">备注</label>
                                                        <textarea style="width:300px;margin-left: 17px" rows="4" name="memo" readonly="true">${newf.memo}</textarea>
                                                    </div>

                                                </form>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="row" style="margin-left: 20px">

                            <div class="form-group">
                                <label class="col-sm-1 control-label juli" style="margin-left: 20px">编辑说明</label>
                                <textarea style="width:800px;margin-left: 70px" rows="4">${newf.illustrate}</textarea>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <%--<c:if test="${approvalStatus!=2 && approvalStatus!=3}">--%>
                                <%--<input type="button" class="btn green" style="width:125px"--%>
                                       <%--value="批准" onclick="tijiaoType(1)"/>--%>
                                <%--<input type="button" class="btn green" style="width:125px"--%>
                                       <%--value="驳回" onclick="tijiaoType(0)"/>--%>
                            <%--</c:if>--%>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/function.js?v=SVN_REVISION"></script>

</body>
</html>
