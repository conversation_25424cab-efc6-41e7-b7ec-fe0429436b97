<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2017/4/8
  Time: 10:12
  To change this template use File | Settings | File Templates.
--%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include file="../../common/headerTop.jsp" %>
<link href="../css/content/about/Huploadify.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<link href="../css/user/myApplication.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
<style>
    body {
        background-color: #fff;
    }
</style>
</head>
<body>
<div class="bounce_Fixed" style="min-height:120%">
    <%--两讫--%>
        <div class="bonceContainer bounce-blue" id="balanceReceive">
        <div class="bonceHead">
            <span>两讫</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="receiveTr">
                <span>支出方式：</span>
                <select id="payMethod" onchange="toggleTansBank()">
                    <option value="1">现金</option>
                    <option value="3">银行转账</option>
                </select>
            </div>
            <div class="tansBank receiveTr" id="tansBank" style="display: none;">
                <span>转账银行：</span>
                <select id="financeAccountId">
                </select>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="balance_deal()">确定下账</span>
        </div>
    </div>
    <%--两讫驳回--%>
        <div class="bonceContainer bounce-red" id="balancerejectAccount">
        <div class="bonceHead">
            <span>驳回</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="sale_delete">
                    <p style="text-align:center;padding:10px 0;" class="reject_Input">
                        <span class="reject_be">驳回原因</span>
                        <input type="text" name="" placeholder="请输入驳回原因" id="reject_reson">
                    </p>

                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="balanceReject_delsure()">确定驳回</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
        </div>
    </div>
    <%--财务审批驳回--%>
        <div class="bonceContainer bounce-red" id="rejectAccount">
            <div class="bonceHead">
                <span>驳回</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="addpayDetails">
                    <div class="sale_delete">
                        <p style="text-align:center;padding:10px 0;" class="reject_Input">
                            <span class="reject_be">驳回原因</span>
                            <input type="text" name="" placeholder="请输入驳回原因" id="deal_reson">
                        </p>

                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="reject_delsure()">确定驳回</span>
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            </div>
        </div>
    <%--超管驳回--%>
        <div class="bonceContainer bounce-red" id="superAccount">
            <div class="bonceHead">
                <span>驳回</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="addpayDetails">
                    <div class="sale_delete">
                        <p style="text-align:center;padding:10px 0;" class="reject_Input">
                            <span class="reject_be">驳回原因</span>
                            <input type="text" name="" placeholder="请输入驳回原因" id="super_reson">
                        </p>

                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="super_delsure()">确定驳回</span>
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            </div>
        </div>
</div>

<div style="margin:-10% 20%;padding-top:10%">
    <div>
        <div style="margin-top:20%">
            <!--放内容的地方-->
            <div class="ty-container">
                <div id="Details" class="finance_See ty-container ">
                    <div style="color:#3598dc" id="applyStatus">
                        <h2 style="text-align:center;">报销申请处理</h2>

                        <c:if test="${isNull!=1}">
                            <c:if test="${param.temp==1}">
                                <div class="operate">
                                    <c:forEach items="${processList}" var="user">
                                        <c:if test="${user.approveStatus==1&&user.level==1}">
                                            <span class="ty-btn ty-btn-big ty-btn-green" onclick="decide(1,${user.id})">
                                                <span>接受${user.level}</span>
                                            </span>
                                            <span class="ty-btn ty-btn-big ty-btn-red" onclick="supper_reject(${user.id})">
                                                <span>驳回</span>
                                            </span>
                                        </c:if>
                                        <c:if test="${user.approveStatus==1&&user.level!=1}">
                                            <span class="ty-btn ty-btn-big ty-btn-green" onclick="finance_agree(1,${user.id})">
                                                <span>接受</span>
                                            </span>
                                            <span class="ty-btn ty-btn-big ty-btn-red" onclick="reject_balancebtn(${user.id})">
                                                <span>驳回</span>
                                            </span>
                                        </c:if>
                                        <c:if test="${user.approveStatus==4}">
                                            <span class="ty-btn ty-btn-big ty-btn-green" onclick="balance_dealBtn(${user.id})">
                                                <span>两讫</span>
                                            </span>
                                            <span class="ty-btn ty-btn-big ty-btn-red" onclick="reject_balancebtn(${user.id})">
                                                <span>驳回</span>
                                            </span>
                                        </c:if>
                                    </c:forEach>
                                </div>
                            </c:if>


                        </c:if>


                        <div class="dealSee_process" id="dealSee_process">
                            <c:forEach items="${processList}" var="user">
                                <c:if test="${user.approveStatus==2}"><%--已批准--%>
                                    <div class="processTr">
                                        <span class="ok-icon"></span>
                                        <span class="touserInfo">处理人：${user.toUserName}/${user.userName}</span>
                                        <span class="timeInfo">处理时间：${fn:substring(user.handleTime,0,19)}</span>
                                    </div>
                                </c:if>
                                <c:if test="${user.approveStatus==5}"><%--已报销--%>
                                    <div class="processTr">
                                        <span class="ok-icon"></span>
                                        <span class="touserInfo">处理人：${user.toUserName}/${user.userName}</span>
                                        <span class="timeInfo">处理时间：${fn:substring(user.handleTime,0,19)}</span>
                                    </div>
                                </c:if>
                                <c:if test="${user.approveStatus==3}"><%--已驳回--%>
                                    <div class="processTr">
                                        <span class="no-icon"></span>
                                        <span class="touserInfo">处理人：${user.toUserName}/${user.userName}</span>
                                        <span class="timeInfo">处理时间：${fn:substring(user.handleTime,0,19)}</span>
                                        <p class="memoInfo">回复内容：${user.approveMemo}</p>
                                    </div>
                                </c:if>
                                <c:if test="${user.approveStatus==1}"><%--待处理--%>
                                    <div class="processTr">
                                        <span class="wait-icon"></span>
                                        <span class="touserInfo">处理人：${user.toUserName}/${user.userName}</span>
                                    </div>
                                </c:if>
                                <c:if test="${user.approveStatus==4}"><%--待两讫--%>
                                    <div class="processTr">
                                        <span class="wait-icon"></span>
                                        <span class="touserInfo">处理人：${user.toUserName}/${user.userName}</span>
                                    </div>
                                </c:if>

                            </c:forEach>

                        </div>


                    </div>
                    <table id="appliInfo" class="ty-table">
                        <tr>
                            <td width="30%">申请人</td>
                            <td width="70%" class="text-left" id="info_creator">${user.userName}</td>
                        </tr>
                        <tr>
                            <td>申请时间</td>
                            <td class="text-left"
                                id="info_createDate">${fn:substring(personnelReimburse.createDate,0,19)}</td>
                        </tr>
                        <tr>
                            <td>费用类别</td>
                            <td class="text-left" id="info_feeCatName">${personnelReimburse.feeCatName}</td>
                        </tr>
                        <tr>
                            <td>票据种类</td>
                            <td> ${personnelReimburse.billCatName} </td>
                        </tr>
                        <tr>
                            <td>票据所属月份</td>
                            <c:if test="${personnelReimburse.billDate==1}">
                                <td class="text-left" id="info_billDate">本月票据</td>
                            </c:if>
                            <c:if test="${personnelReimburse.billDate==2}">
                                <td class="text-left" id="info_billDate">非本月票据</td>
                            </c:if>
                        </tr>
                        <tr>
                            <td>摘要</td>
                            <td class="text-left" id="info_summary">${personnelReimburse.summary}</td>
                        </tr>
                        <tr>
                            <td>用途</td>
                            <td class="text-left" id="info_purpose">${personnelReimburse.purpose}</td>
                        </tr>
                        <tr>
                            <td>票据数量</td>
                            <td class="text-left" id="info_billQuantity">${personnelReimburse.billQuantity}</td>
                        </tr>
                        <tr>
                            <td>总金额</td>
                            <td class="text-left" id="info_amount">${personnelReimburse.amount}</td>
                        </tr>
                        <tr>
                            <td>备注</td>
                            <td class="text-left" id="info_memo">${personnelReimburse.memo}</td>
                        </tr>
                        <tr style="position:relative; ">
                            <td>附件
                                <c:forEach items="${personnelReimburse.personnelReimbursetAttachmentHashSet}"
                                           var="user">
                                    <div id="bigImagcon"><img id="bigImag" src="../${user.path}"></div>
                                </c:forEach>
                            </td>
                            <td class="text-left" id="info_img">
                                <c:forEach items="${personnelReimburse.personnelReimbursetAttachmentHashSet}"
                                           var="user">
                                    <img onmouseout="imgOut($(this))" onmouseenter="imgEnter($(this))"
                                         src="../${user.path}" alt="">
                                </c:forEach>
                            </td>

                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include file="../../common/contentSliderLitbar.jsp" %>
</div>

<%@ include file="../../common/footerScript.jsp" %>

<script src="../css/content/about/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/user/myLeave.js?v=SVN_REVISION"></script>
<script src="../script/user/myAccount.js?v=SVN_REVISION"></script>
<script src="../script/user/chargeSubmitSale.js?v=SVN_REVISION"></script>
<script src="../script/function.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/user/manageBill.js?v=SVN_REVISION"></script>
</body>
</html>
