<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <style>
        .juZuo{margin-left:180px}
        .juli{margin-top: 10px}
    </style>
</head>

<body class="">

<div class="page-content-wrapper juzuo">
    <div class="page-content">
        <!--放内容的地方-->
        <div class="row" >
            <div style="width: 1000px" class="position">
                <!-- BEGIN SAMPLE TABLE PORTLET-->
                <div class="portlet box green">
                    <div class="portlet-title">
                        <div class="caption" style="margin-left: 350px">
                            <h2>账户明细删除申请</h2>
                        </div>
                    </div>
                    <div class="portlet-body">
                        </br>
                        </br>
                        </br>
                        <form class="form-horizontal" role="form" action="">

                            <div class="form-group">
                                <label for="firstname2" class="col-sm-3 control-label">账户</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="firstname2" value="${ad.accountName}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">摘要</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4" readonly="true">${ad.summary}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">发生时间</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="firstname" value="${ad.createDate}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">方式</label>
                                <div class="col-sm-6">
                                    <c:if test="${ad.debit!=null}">
                                    <input type="text" class="form-control" id="secondname" value="支出"
                                           placeholder="" readonly="true"/>
                                    </c:if>
                                    <c:if test="${ad.credit!=null}">
                                        <input type="text" class="form-control" id="secondname" value="收入"
                                               placeholder="" readonly="true"/>
                                    </c:if>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">金额</label>
                                <div class="col-sm-6">
                                    <c:if test="${ad.debit!=null}">
                                        <input type="text" class="form-control" value="${ad.debit}"
                                               placeholder="" readonly="true"/>
                                    </c:if>
                                    <c:if test="${ad.credit!=null}">
                                        <input type="text" class="form-control" value="${ad.credit}"
                                               placeholder="" readonly="true"/>
                                    </c:if>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">经手人</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="fourthname" value="${ad.auditorName}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">备注</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" id="fivename" value="${ad.memo}"
                                           placeholder="" readonly="true">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="firstname" class="col-sm-3 control-label">删除说明</label>
                                <textarea style="width:400px;margin-left: 17px" rows="4" readonly="true">${ad.illustrate}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <form action="../user/deleteAccountDetailById.do" method="post" id="formDel">
                            <input type="hidden" name="submitType" id="submitType" value=""/>
                            <input type="hidden" name="umId" value="${umId}">
                            <input type="hidden" name="id" value="${ad.id}">
                            <%--<div class="modal-footer">--%>
                            <c:if test="${approvalStatus!=2 && approvalStatus!=3}">
                                <input type="button" class="btn white" style="width:125px;color: red"
                                       value="批准" onclick="tijiaoType(1)"/>
                                <input type="button" class="btn white" style="width:125px;color: green"
                                       value="驳回" onclick="tijiaoType(0)"/>
                            </c:if>
                            <%--</div>--%>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    function tijiaoType(type){
        $("#submitType").val(type);
        $("#formDel").submit();
    }
</script>
</body>
</html>
