<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>

</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" >
<div class="bounce">
    <%--  confirm  --%>
    <div class="bonceContainer bounce-orange" id="controlManage" >
        <div class="bonceHead">
            <span>确认提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p  id="controlMess"></p>
            <p class="hd" style="height:10px;" id="controlTip"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="cancelControlSubmit()">关闭</span>
            <span class="ty-btn ty-btn-red ty-circle-5 ty-btn-big" id="okControl" onclick="okControlSubmit()">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>系统设置</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <div class="ty-mainData">
                    <table class="ty-table">
                        <thead>
                        <tr class="active">
                            <td>功能</td>
                            <td>功能描述</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>财务是否可以使用手机端</td>
                            <td>启用意味着财务人员可使用手机端进行数据录入、编辑及查看等</td>
                            <td>
                                <div class="form-group">
                                    <form action="../user/updateLoginStatus.do" method="post" id="form1">
                                        <%--全部封装--%>
                                        <div class="ty-select" onclick="tyToggleSelect($(this) , 0 , event)">
                                            <c:choose>
                                                <c:when test="${loginStatus==1}">
                                                    <div class="ty-opTTL" >
                                                        <option readonly value="${loginStatus}" name="loginStatus">禁用 </option>
                                                        <span class="ty-down"></span>
                                                    </div>
                                                    <div class="ty-opItems">
                                                        <option class="ty-opItem" value="{loginStatus:0 ,type:0}" onclick="tySelect($(this) , upStatus)">启用</option>
                                                        <option class="ty-opItem ty-active" value="{loginStatus:1,type:0}" onclick="tySelect($(this) , upStatus)">禁用</option>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="ty-opTTL" >
                                                        <option readonly value="${loginStatus}" name="loginStatus">启用 </option>
                                                        <span class="ty-down"></span>
                                                    </div>
                                                    <div class="ty-opItems">
                                                        <option class="ty-opItem ty-active" value="{loginStatus:0 ,type:0}" onclick="tySelect($(this) , upStatus)">启用</option>
                                                        <option class="ty-opItem" value="{loginStatus:1,type:0}" onclick="tySelect($(this) , upStatus)">禁用</option>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </form>
                                </div>
                            </td>
                        </tr>

                        </tbody>
                    </table>
                    <br><br><br><br><br>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script>
    // 财务的回调方法
    function upStatus( obj ){
        var val = obj.val();
        // alert(val);
        $("#controlTip").html( val );
        console.log(val);
//        var valJson = JSON.parse( val ) ;
        var valJson = eval('('+ val +')');
        var type = valJson["type"] ;
        if( type == "0" ){
            $("#controlMess").html( "确定要改变所有财务人员的登录状态？" );
        }else{
            $("#controlMess").html( "确定要改变所有销售人员的登录状态？" );
        }
        bounce.show( $("#controlManage") );
    }

    function  okControlSubmit() {
        var val = $("#controlTip").html();
//        var valJson = JSON.parse( val ) ;
        var valJson = eval('('+ val +')');
        var type = valJson["type"] ;
        var loginStatus = valJson["loginStatus"] ;
        if( type == "0" ){
            location.href = "../user/updateLoginStatus.do?loginStatus=" + loginStatus ;
        }else{
            location.href = "../user/updateLoginStatus1.do?loginStatus=" + loginStatus ;
        }
    }
    function cancelControlSubmit(){
        var val = $("#controlTip").html() ;
        var valStr = "";
        if( val == "1"){ val = "0" ; valStr = "启用"; }else{ val = "1"; valStr = "禁用";  }
        $("#loginStatus").val(val).html( valStr ) ;
        bounce.cancel();
    }
</script>
</body>
</html>
