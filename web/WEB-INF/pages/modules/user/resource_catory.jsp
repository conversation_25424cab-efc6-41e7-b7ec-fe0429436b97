<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
 <link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
    .ty-panel{  background: none;    }
    .ty-colFileTree{ float: left ; max-width:300px; overflow-x:auto;     }
    .ty-treeItem{ max-width: 290px; overflow:hidden; text-overflow:ellipsis; white-space: nowrap;  }
    .mar{ margin-left: 300px;  }
    .departTree{  }
    .departTree>ul{ width: 315px; padding:15px 0; margin-left: 10px; border:1px solid #ccc; position: relative; box-shadow: 0 0 3px #ccc;height:300px; overflow:auto;   }
    .departTree>ul:nth-child(2){   margin-left: 80px; }
    .departTree i.fa{  color: #4E9DFF; font-weight: bolder; margin:0 20px;   }
    .departTree li>div{ position: relative; padding:3px 0 3px 25px;    }
    .departTree li>div:hover{ background: #eaeaea; box-shadow:0 0 2px #ccc; cursor: pointer; color: #333; font-weight: bold;     }
    .departTree li>div>span:hover{  }
    .departTree li>div>i.fa-angle-right , .departTree li>div>i.fa-info{ position:absolute; left:-10px; font-size:16px;     }
    .departTree li>div>i.fa-plus-square:hover ,.departTree li>div>i.fa-minus-square:hover , .departTree li>div>i.fa-angle-right:hover{ color:#0075ff;    }
    .departTree li>div>i.fa-plus-square, .departTree li>div>i.fa-minus-square{ float: right;     }
    .departTree ul ul{ display: none;padding-left:15px;  }
    #scanSet { width:760px;  }
    .arrow{  color: #4E9DFF; font-size:50px; top:95px; width:75px; position: relative;    }
    .default_img{margin:auto;width:128px;}
    .default_img img{width:100%;vertical-align:middle;}
    .initialSect{margin-top:100px;}
    .initialSect p{text-align:center;}
    .u-btn{font-size: 12px;  font-family: 宋体;  padding: 5px 15px 5px;  border-radius: 3px;  font-weight: bold;  cursor: pointer;}
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%--  edit && add --%>
    <div class="bonceContainer bounce-blue bounce-changeClass">
        <div class="bonceHead">
            <span>修改类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            修改名称：<input type="text" class="ty-inputText categoryName">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <button class="ty-btn ty-btn-blue ty-btn-big" onclick="sureChangeClass()">确定</button>
        </div>
    </div>
    <div class="bonceContainer bounce-red bounce-deleteClass">
        <div class="bonceHead">
            <span>删除类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>您确定删除该文件吗？删除将不可修复。</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn ty-btn-red ty-btn-big" onclick="sureDeleteClass()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green bounce-newClass">
        <div class="bonceHead">
            <span>新增子级类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            名称：<input type="text" class="ty-inputText categoryName">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <span class="ty-btn ty-btn-green ty-btn-big" onclick="sureNewClass()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-green bounce-newSameClass">
        <div class="bonceHead">
            <span>新增同级类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            名称：<input type="text" class="ty-inputText gwtre categoryName">
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">关闭</span>
            <button class="ty-btn ty-btn-green ty-btn-big submitBtn" disabled="disabled" onclick="sureNewSameClass()">确定</button>
        </div>
    </div>
        <div class="bonceContainer bounce-blue" id="mtTip">
            <div class="bonceHead">
                <span>温馨提示：</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="addpayDetails">
                    <div class="shu1">
                        <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
            </div>
        </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>类别设置</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container clearfix">
                <%--竖型文件树容器--%>
                <div class="ty-mainDate initialSect">
                    <div>
                        <div class="default_img">
                            <img src="../assets/oralResource/initImg.png" />
                        </div>
                        <p>您还没有设置类别，点击<span class="ty-btn ty-btn-green" onclick="newSameClass(1)">新增类别</span></p>
                    </div>
                </div>

                <div class="ty-fileContent">
                    <div class="ty-colFileTree hd"></div>
                    <div class="ty-mainData mar hd">
                        <%--此类别信息--%>
                        <div class="ty-panel">
                            <div class="ty-panelHeader nowFolder">
                                <h3 class="ty-left" style="overflow:hidden; text-overflow:ellipsis; white-space: nowrap; width:100%; "></h3>
                                <div class="ty-btn ty-btn-green ty-btn-big ty-right newSameClassBtn hd" style="margin-top:9px" onclick="newSameClass()">新增同级类别</div>
                            </div>
                            <table class="ty-table ty-table-control CategoryMessage">
                                <thead>
                                <tr>
                                    <td width="20%">创建日期</td>
                                    <td width="20%">修改日期</td>
                                    <td width="20%">修改名称</td>
                                    <td width="20%">删除</td>
                                    <td width="20%">新增子级类别</td>
                                </tr>
                                </thead>

                                <%--此类别容器--%>
                                <tbody></tbody>
                            </table>
                        </div>

                        <%--子类别信息--%>
                        <div class="ty-panel" style="margin-top:130px">
                            <div class="ty-panelHeader childFolder">
                                <h3>子类别</h3>
                            </div>
                            <table class="ty-table ty-table-control cCategoryMessage">
                                <thead>
                                <tr>
                                    <td width="60%">类别名称</td>
                                    <td width="20%">创建日期</td>
                                    <td width="20%">修改日期</td>
                                </tr>
                                </thead>

                                <%--子类别容器--%>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/user/resource_catory.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
