<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="java.util.Date" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
  <link href="../css/role/roleIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" >
<div class="bounce_Fixed">
    <%-- 审批权限 - 请求处理选择审批人--%>
    <div class="bonceContainer bounce-blue" id="chooseApproveP"  >
        <div class="bonceHead">
            <span>添加审批人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
           <%-- <div class="ty-roleSearch">
                <input type="text" placeholder="请输入人名搜索" class="ty-searchInput" id="searchInput" />
                <input type="text" class="ty-searchBtn" onclick="myapplySearch()" />
            </div>--%>
            <div class="approvePList">
                <div class="approvePItem" >
                    <input type="radio" value="0" name="approveP" >直接上级
                </div>
                <ul id="approveTree"></ul>
            </div>
        </div>
        <div class="bonceFoot">
            <p id="tp2" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="selectOK()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
        </div>
    </div>
    <%-- 二级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="secdTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="secdtipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">确认</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="Tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs">    </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确认</span>
        </div>
    </div>
    <%-- confirm 框  --%>
    <div class="bonceContainer bounce-blue" id="confirm">
        <div class="bonceHead">
            <span id="confirmTtl">提示信息</span>
            <a class="bounce_close" onclick="confirCancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="confirMs">    </div>
        </div>
        <div class="bonceFoot">
            <span class="hd" id="confirmType"></span>
            <span class="ty-btn  ty-btn-big ty-circle-5" onclick="confirCancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="confirOK()">确认</span>
        </div>
    </div>
    <%--  审批权限 - 财务权限审批--%>
    <div class="bonceContainer bounce-blue" id="myModal_finance" style="width:540px;">
        <div class="bonceHead">
            <span>权限变更</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>
                <div class="trItem">
                    <span class="ttl">审批级别</span>
                    <span class="con">
                     <%--   <select class="conInput" onchange="changeMethod($(this))" id="changeMethod">
                            <option value="1">一级审批</option>
                            <option value="2">二级审批</option>
                            <option value="3">三级审批</option>
                        </select>--%>

                        <div class="ty-select" onclick="tyToggleSelect($(this) , 0 , event)" style="width:80%;">
                            <div class="ty-opTTL" >
                                <option readonly value="1"  id="changeMethod">一级审批</option>
                                <span class="ty-down"></span>
                            </div>
                            <div class="ty-opItems">
                                <option class="ty-opItem" value="1" onclick="tySelect( $(this) , changeMethod )">一级审批</option>
                                <option class="ty-opItem" value="2" onclick="tySelect( $(this) , changeMethod )">二级审批</option>
                                <option class="ty-opItem" value="3" onclick="tySelect( $(this) , changeMethod )">三级审批</option>
                            </div>
                        </div>

                    </span>
                </div>
            </div>
            <div>
                <div id="method_1">
                    <div class="trItem">
                        <span class="ttl">审批级别</span>
                        <span class="con">
                            <span class="conInput" style="margin-left:-4px; ">超管</span>
                        </span>
                    </div>
                </div>
                <div id="method_2">
                    <div class="trItem"><span class="ttl">最终审批者</span><span class="con"><span class="conInput">超管</span></span></div>
                    <div class="trItem"><span class="ttl">直接审批者</span>
                        <span class="con">
                            <select class="conInput" id="changeMethod2_person">
                                <option value="">直接上级</option>
                                <option value="">贾胜强 15202266547</option>
                                <option value="">贾胜强2 15202266547</option>
                            </select>
                        </span></div>
                    <div class="trItem"><span class="ttl">金额上限</span><span class="con"><input id="amountCeiling" class="conInput" type="text"  onkeyup="clearNoNum(this)"></span></div>
                </div>
                <div id="method_3">
                    <div class="trItem"><span class="ttl">最终审批者</span><span class="con"><span class="conInput">超管</span></span></div>
                    <div class="trItem"><span class="ttl">次级审批者</span>
                                            <span class="con">
                                                <select class="conInput" id="changeMethod3_person2"  >
                                                    <option value="">贾胜强 15202266547</option>
                                                    <option value="">贾胜强2 15202266547</option>
                                                </select>
                                            </span></div>
                    <div class="trItem"><span class="ttl">次级金额上限</span><span class="con"><input id="secondaryAmountCeiling" class="conInput" type="text" onkeyup="clearNoNum(this)"></span></div>
                    <div class="trItem"><span class="ttl">直接审批者</span>
                                            <span class="con">
                                                <span class="conInput" style="margin-left:-4px;">直接上级</span>
                                            </span>
                    </div>
                    <div class="trItem"><span class="ttl">直接上级金额上限</span><span class="con"><input class="conInput" type="text" id="amountCeiling2" onkeyup="clearNoNum(this)"></span></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <p id="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="saveChargeMethod()">保存</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--  审批权限 - 项目管理审批--%>
    <div class="bonceContainer bounce-blue bounce-changeState">
        <div class="bonceHead">
            <span>权限变更</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>
                <div class="approvalLevel">
                    <span class="ttl">审批级别</span>
                    <span class="con">
                        <select class="conInput level" onchange="levelChange($(this))">
                            <option value="1">一级审批</option>
                            <option value="2">二级审批</option>
                        </select>
                    </span>
                </div>
                <div class="approval"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="saveProApprove()">保存</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--  审批权限 - 我的请求 变更审批 - 请假和加班 --%>
    <div class="bonceContainer bounce-blue " id="myApply">
        <div class="bonceHead">
            <span>权限变更</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div style="width: 90%">
                <div class="approvalLevel">
                    <span class="ttl">是否需要审批</span>
                    <span class="con">
                        <div class="ty-select" onclick="tyToggleSelect($(this) , 0 , event)" style="width:100%;">
                            <div class="ty-opTTL" >
                                <option readonly value="1" style="padding-top:3px;" id="isCharge">需要审批</option>
                                <span class="ty-down"></span>
                            </div>
                            <div class="ty-opItems">
                                <option class="ty-opItem ty-active" value="1" onclick="tySelect( $(this) , upChargeLevel )">需要审批</option>
                                <option class="ty-opItem" value="0" onclick="tySelect( $(this) , upChargeLevel )">不需要审批</option>
                            </div>
                        </div>
                    </span>
                </div>
                <div class="approval" id="myApplyCon">
                    <div class="trItem">
                        <span class="ttl">审批流程</span>
                        <div class="con">
                            <div id="approveList">
                                <div class="trList">
                                    <div class="trItem2" level="1">
                                        <span class="ttl2">第一级审批</span>
                                        <span class="con2 add-person" onclick="chooseApproveP($(this));" placeholder="">选择审批选择审批选择审批人</span>
                                        <span class="deleteItem" onclick="deleteItem($(this))">—</span>
                                        <div class="hd"></div>
                                    </div>
                                </div>
                            </div>
                            <div id="addItem2" class="addItem2">+添加审批级别</div>
                            <p id="beforeTip" style="color:red; text-align:center;  "></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-5 " onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="changeMyApplyAuthz()">保存</span>
        </div>
    </div>
    <%--  审批权限 - 项目立项、开发 tip --%>
    <div class="bonceContainer bounce-blue" id="bounce_success">
        <div class="bonceHead">
            <span>确认信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>
                保存成功！
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel();location.reload();">确认</span>
        </div>
    </div>
    <%--  常规权限 - 删除角色confirm --%>
    <div class="bonceContainer bounce-red" id="delRole">
        <div class="bonceHead">
            <span>移除</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="delTip"> </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="delRoleSubmit()">确认</span>
        </div>
    </div>
    <%--  常规权限 - 编辑角色 --%>
    <div class="bonceContainer bounce-green" id="editRole">
        <div class="bonceHead">
            <span>编辑角色</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>
                <input type="text" id="roleName" class="col-md-12 form-control" placeholder="请输入角色名称">
            </div>
            <p id="editTip"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="editRoleSubmit()" id="editRoleSubmit">确认</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>权限管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
    <div class="page-content">
        <div class="ty-container">
            <span class="hd" id="third_n">审批权限</span>
            <ul class="ty-secondTab">
                <li class="ty-active">审批权限</li>
                <li>常规权限</li>
                <li>权限分配</li>
            </ul>
            <div class="ty-mainData" style="padding:0 0 250px 0;">
                <%--审批权限--%>
                <div class="tab">
                    <table class="ty-table ty-table-control order-column">
                        <thead>
                        <tr>
                            <td> 模块 </td>
                            <td> 功能描述 </td>
                            <td> 行为 </td>
                            <td> 审核者 </td>
                            <td> 审核状态 </td>
                            <td> 操作 </td>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach items="${approvalItems}" var="a">
                            <tr class="odd gradeX">
                                    <%--<td>
                                      <input type="checkbox" class="checkboxes" value="1" /> </td>--%>
                                <td> ${a.name} </td>
                                <td>
                                        ${a.description}
                                </td>
                                <td> ${a.type} </td>
                                <td> ${a.approveUser} </td>
                                <td>
                                    <c:if test="${a.status==0}">
                                        <span class="label label-sm label-success"> 无需审批 </span><span style="display:none;">${a.level}</span>
                                    </c:if>
                                    <c:if test="${a.status==1}">
                                        <c:if test="${a.level==1}">
                                            <span class="label label-sm label-danger">一级审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                        <c:if test="${a.level==2}">
                                            <span class="label label-sm label-danger">二级审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                        <c:if test="${a.level==3}">
                                            <span class="label label-sm label-danger">三级审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                        <c:if test="${a.level==4}">
                                            <span class="label label-sm label-danger">四级审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                        <c:if test="${a.level==5}">
                                            <span class="label label-sm label-danger">五级审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                        <c:if test="${a.level==6}">
                                            <span class="label label-sm label-danger">六级审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                        <c:if test="${a.level==7}">
                                            <span class="label label-sm label-danger">七级审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                        <c:if test="${a.level==8}">
                                            <span class="label label-sm label-danger">八级审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                        <c:if test="${a.level==null}">
                                            <span class="label label-sm label-danger">无需审批</span><span style="display:none;">${a.level}</span>
                                        </c:if>
                                    </c:if>
                                    <c:if test="${a.status==2}">
                                        <span class="label label-sm label-info"> 待定 </span>
                                    </c:if>
                                </td>
                                <td>
                                    <c:if test="${a.name=='我的请求'}">
                                        <c:if test="${ a.description=='加班申请'}">
                                            <span class="ty-color-blue" onclick="requestSet('overTime', ${ a.id } , $(this) )">更改状态</span>
                                        </c:if>
                                        <c:if test="${ a.description=='请假申请'}">
                                            <span class="ty-color-blue" onclick="requestSet('leave', ${ a.id } , $(this))">更改状态</span>
                                        </c:if>
                                    </c:if>
                                    <c:if test="${a.name=='财务审批'}">
                                        <span class="ty-color-blue" onclick="finananceSet()">更改状态</span>
                                    </c:if>
                                    <c:if test="${a.name=='项目立项'}">
                                        <span class="ty-color-blue" onclick="establishmentSet(this)">更改状态</span>
                                    </c:if>
                                    <c:if test="${a.name=='项目开发'}">
                                        <span class="ty-color-blue" onclick="developmentSet(this)">更改状态</span>
                                    </c:if>
                                        <%--<a href="#" onclick="changeState('${a.id}','${a.belongTo}','${a.type}','${a.name}','${a.description}','${a.level}','${a.status}');" role="button" class="label label-sm label-primary" data-toggle="modal"> 更改状态 </a>--%>
                                </td>
                            </tr>
                        </c:forEach>
                        </tbody>
                    </table>
                </div>
                <%--常规权限--%>
                <div class="tab hd">
                    <table class="ty-table ty-table-control">
                        <a class="ty-btn ty-btn-big ty-btn-green ty-circle-5 ty-right" onclick="addRoleBtn()" >添加角色类型
                            <i class="fa fa-plus"></i>
                        </a>
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>角色</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach items="${roleList}" var="u">
                            <tr>
                                <td>${u.roleID}</td>
                                <td> ${u.roleName}</td>
                                <td>
                                    <span class="ty-color-blue" onclick="changeAuth('${u.roleID}','${u.roleName}')">变更 </span>
                                    <span class="ty-color-red"  onclick="deleteRole('${u.roleID}','${u.roleName}', $(this))">移除 </span>
                                </td>
                            </tr>
                        </c:forEach>
                        </tbody>
                    </table>
                </div>
                <%--权限分配--%>
                <div class="tab hd">
                    <table class="ty-table ty-table-control"  >
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>部门</td>
                            <td>职位</td>
                            <td>姓名</td>
                            <td>手机号</td>
                            <td>角色</td>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach items="${users}" var="u">
                            <tr>
                                <td>${u.userID}</td>
                                <td>${u.departName}</td>
                                <td>${u.postName}</td>
                                <td>${u.userName}</td>
                                <td>${u.mobile}</td>
                                <td>

                                    <c:choose>
                                        <c:when test="${u.userID==sessionScope.user.userID||u.roleID==1}">
                                            <div class="ty-select " disabled >
                                                <div class="ty-opTTL" >
                                                    <option value="${r.roleID}" readonly > ${r.roleName} </option>
                                                    <i class="ty-down"></i>
                                                </div>
                                                <div class="ty-opItems">
                                                    <option class="ty-opItem" value="0">无默认角色</option>
                                                    <c:forEach items="${roleList}" var="r">
                                                        <c:if test="${r.roleID==u.roleID}">
                                                            <option class="ty-opItem" value="${r.roleID}"  > ${r.roleName} </option>
                                                        </c:if>
                                                    </c:forEach>
                                                </div>
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="ty-select" onclick="tyToggleSelect($(this) , 0 , event)"  >
                                                <div class="ty-opTTL" >
                                                    <option readonly  value="0">无默认角色</option>
                                                    <i class="ty-down"></i>
                                                </div>
                                                <div class="ty-opItems">
                                                    <option value="0" class="ty-opItem">无默认角色</option>
                                                    <c:forEach items="${roleList}" var="r">
                                                        <c:choose>
                                                            <c:when test="${r.roleID==u.roleID}">
                                                                <option class="ty-opItem ty-active" value="${u.userID},${r.roleID}" onclick="tySelect( $(this) , changeRole )" > ${r.roleName} </option>
                                                            </c:when>
                                                            <c:when test="${r.roleID==1}">
                                                                <option class="ty-opItem " disabled  value="${r.roleID}"  > ${r.roleName} </option>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <option class="ty-opItem" value="${u.userID},${r.roleID}" onclick="tySelect( $(this) , changeRole )" > ${r.roleName} </option>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </c:forEach>
                                                </div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                </td>
                            </tr>
                        </c:forEach>


                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
  </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/role/roleIndex.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    function changeAuth(roleID,roleName){
        var usertype = chargeRole('总务');
        if (usertype) {
            $("#Tip #tipMs").html("您没有此权限！")
            bounce.show($("#Tip"));
        }else{
            openWindow("../sys/roleEdit.do?roleID="+roleID+"&roleName="+roleName+"",'1000','800');
        }
    }
</script>
</body>
</html>
