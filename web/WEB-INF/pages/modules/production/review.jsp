<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/production/review.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" style=" min-width:1200px; ">
<div class="ty-tip" id="tipCon">
    <div class="ty-tipcon"  >
        <div class="ty-trigl-1"><span></span></div>
        <div id="tipitem"></div>
    </div>
</div>
<div id="auth" style="display:none;  "> </div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--评审--%>
    <div class="bonceContainer bounce-green" id="approvePR"  style="max-width:1500px; width:95%;">
        <div class="bonceHead">
            <span>评审</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clearfix approveInfo">
                <div class="orderItem">
                    <div class="orderItemTitle">客户名称</div>
                    <div class="orderItemCon" id="reviewCusName">天津贝塔科技有限公司</div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">客户代号</div>
                    <div class="orderItemCon" id="reviewCusCode"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">订单号</div>
                    <div class="orderItemCon" id="reviewOrdSn"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">订单收到日期</div>
                    <div class="orderItemCon" id="reviewSignDate"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">录入者</div>
                    <div class="orderItemCon" id="reviewOrdCreator"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">录入时间</div>
                    <div class="orderItemCon" id="reviewCreateDate"></div>
                </div>
            </div>
            <div class="approveList">
                <div class="ty-dropdown">
                    <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-dropdownBtn " target="roleCon_update" id="setRole_update" onclick="setRoleBtn( $(this) )" >筛 选</span>
                    <div class="ty-dropdownCon" id="roleCon_update" >
                        <div class="ty-trigl-1"><span></span></div>
                        <div>
                            <div class="orderItemTiny"><i isSet="1" code="1" class="fa fa-dot-circle-o"></i>商品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="2" class="fa fa-circle-o"></i>商品名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="3" class="fa fa-circle-o"></i>产品图号</div>
                            <div class="orderItemTiny"><i isSet="0" code="4" class="fa fa-circle-o"></i>产品名称</div>
                        </div>
                        <div>
                            <div class="orderItemTiny"><i isSet="0" code="5" class="fa fa-square-o"></i>商品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="6" class="fa fa-square-o"></i>商品名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="7" class="fa fa-square-o"></i>产品图号</div>
                            <div class="orderItemTiny"><i isSet="0" code="8" class="fa fa-square-o"></i>产品名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="9" class="fa fa-square-o"></i>单位</div>
                            <div class="orderItemTiny"><i isSet="0" code="10" class="fa fa-square-o"></i>含税单价</div>
                            <div class="orderItemTiny"><i isSet="0" code="11" class="fa fa-check-square-o"></i>备注</div>
                        </div>
                        <div>
                            <span class="ty-btn ty-circle-3" onclick="cancelSert($('#setRole_update'))">取消</span>
                            <span class="ty-btn ty-btn-green ty-circle-3" onclick="setRole($('#goodsInfo') , $('#setRole_update') , 'roleCon_update')">确定</span>
                        </div>
                    </div>
                </div>
                <div style="max-height:350px; overflow:auto; ">
                    <table class="ty-table ty-table-control" width="100%">
                        <thead>
                        <tr>
                            <td width="6%">序号</td>
                            <td width="11%">商品代号</td>
                            <td width="8%">数量</td>
                            <td width="14%">要求到货日期</td>
                            <td width="14%">能否按时到货</td>
                            <td width="15%">按期到货数量</td>
                            <td width="15%">剩余数量到货日期</td>
                            <td width="15%">备注</td>
                        </tr>
                        </thead>
                        <tbody id="goodsInfo">
                          <%--  <tr class="throw"><td>1</td> <td>尚品一号</td> <td>10</td> <td>2017-06-15</td> <td>能</td> <td>— —</td> <td>— —</td> <td>一次评审</td> </tr>
                            <tr class="throw"><td>1</td> <td> </td> <td>20</td> <td>2017-06-15</td> <td>能</td> <td>— —</td> <td>— —</td> <td>二次评审</td> </tr>
                            <tr class="throw"><td>1</td> <td> </td> <td>100</td> <td>2017-06-15</td> <td>否</td> <td>50</td> <td>2017-06-30</td> <td>三次评审</td> </tr>
                            <tr><td>1</td> <td>尚品一号</td> <td>1000</td> <td>2017-07-15</td> <td>否</td> <td>500</td> <td>2017-07-30</td> <td>修改后第一次评审</td> </tr>
                            <tr><td>1</td> <td> </td> <td>1000</td> <td>2017-07-30</td> <td>能</td> <td>— —</td> <td>— —</td> <td>修改后第二次评审</td> </tr>
                            <tr><td>2</td> <td>尚品二号</td> <td>20</td> <td>2017-06-15</td> <td>能</td> <td>— —</td> <td>— —</td> <td>一次评审</td> </tr>
                            <tr><td>2</td> <td> </td> <td>50</td> <td>2017-06-15</td> <td>能</td> <td>— —</td> <td>— —</td> <td>二次评审</td> </tr>
                            <tr><td>2</td> <td> </td> <td>200</td> <td>2017-06-15</td> <td>能</td> <td>— —</td> <td>— —</td> <td>三次评审</td> </tr>--%>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-green ty-circle-5" onclick="review()">提交</span>
        </div>
    </div>
    <%--评审记录--%>
    <div class="bonceContainer bounce-blue" id="approvePRHistory"  style="max-width:1500px; width:95%;">
        <div class="bonceHead">
            <span>评审记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clearfix approveInfo">
                <div class="orderItem">
                    <div class="orderItemTitle">客户名称</div>
                    <div class="orderItemCon" id="cusName"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">客户代号</div>
                    <div class="orderItemCon" id="cusCode"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">订单号</div>
                    <div class="orderItemCon" id="ordSn"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">订单收到日期</div>
                    <div class="orderItemCon" id="ordReciveDate"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">录入者</div>
                    <div class="orderItemCon" id="creator"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">录入时间</div>
                    <div class="orderItemCon" id="createDate"></div>
                </div>
            </div>
            <div class="approveHistoryList">
                <div class="ty-dropdown">
                    <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-dropdownBtn" target="roleCon" id="setRole" onclick="setRoleBtn( $(this) )" >筛 选</span>
                    <div class="ty-dropdownCon" id="roleCon" >
                        <div class="ty-trigl-1"><span></span></div>
                        <div>
                            <div class="orderItemTiny"><i isSet="1" code="1" class="fa fa-dot-circle-o"></i>商品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="2" class="fa fa-circle-o"></i>商品名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="3" class="fa fa-circle-o"></i>产品图号</div>
                            <div class="orderItemTiny"><i isSet="0" code="4" class="fa fa-circle-o"></i>产品名称</div>
                        </div>
                        <div>
                            <div class="orderItemTiny"><i isSet="0" code="5" class="fa fa-square-o"></i>商品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="6" class="fa fa-square-o"></i>商品名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="7" class="fa fa-square-o"></i>产品图号</div>
                            <div class="orderItemTiny"><i isSet="0" code="8" class="fa fa-square-o"></i>产品名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="9" class="fa fa-square-o"></i>单位</div>
                            <div class="orderItemTiny"><i isSet="0" code="10" class="fa fa-square-o"></i>含税单价</div>
                            <div class="orderItemTiny"><i isSet="0" code="11" class="fa fa-check-square-o"></i>备注</div>
                        </div>
                        <div>
                            <span class="ty-btn ty-circle-3" onclick="cancelSert($('#setRole'))">取消</span>
                            <span class="ty-btn ty-btn-green ty-circle-3" onclick="setRole($('#historyList') , $('#setRole') , 'roleCon' )">确定</span>
                        </div>
                    </div>
                </div>
                <div style="height:250px;overflow:auto; ">
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td width="5%">序号</td>
                            <td width="10%">商品代号</td>
                            <td width="5%">数量</td>
                            <td width="10%">要求到货日期</td>
                            <td width="10%">能否按时到货</td>
                            <td width="10%">按期到货数量</td>
                            <td width="10%">剩余数量到货日期</td>
                            <td width="10%">评审时间</td>
                            <td width="10%">评审人</td>
                            <td width="10%">处理时间</td>
                            <td width="10%">处理人</td>
                        </tr>
                        </thead>
                        <tbody id="historyList"> </tbody>
                    </table>
                </div>

            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
        <%--商品交期确认--%>
        <div class="bonceContainer bounce-blue" id="deliveryDate"  style="width:700px;">
            <div class="bonceHead">
                <span>商品交期确认</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <p class="invoiceCatTip"></p>
                <p class="modHistoryMsg"></p>
                <div class="clear orderInfo">
                    <div class="itemCn">
                        <div class="orderItemTitle">商品代号
                        </div>
                        <div class="itemCon outer_sn" data-name="outer_sn"></div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">商品名称</div>
                        <div class="itemCon outer_name" data-name="outer_name"></div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">收货地点<span class="modHistory">（原/新）</span></div>
                        <div class="itemCon delivery_address" data-name="delivery_address"></div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">货物件数</div>
                        <div class="itemCon goodNum_stock" data-name="goodNum_stock"></div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">当前库存
                            <span class="linkBtn ty-right" onclick="linkOrds($(this))">本商品的其他订购信息</span></div>
                        <div class="itemCon current_stock" data-name="current_stock"></div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">最低库存</div>
                        <div class="itemCon minimumi_stock" data-name="minimumi_stock"></div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">订购数量<span class="modHistory">（原/新）</span>
                            <span class="modHistory ty-right shipped">已发货数量</span>
                        </div>
                        <div class="itemCon" data-name="amount">
                            <span></span>
                            <span class="modHistory shipped"></span>
                        </div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">计量单位</div>
                        <div class="itemCon unit" data-name="unit"></div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">要求到货日期<span class="modHistory">（原/新）</span> </div>
                        <div class="itemCon " data-name="delivery_date"></div>
                    </div>
                    <div class="itemCn">
                        <div class="orderItemTitle">能否按时到货</div>
                        <select type="text" class="form-control arriveState" onchange="arriveState($(this))">
                            <option value="">请选择</option>
                            <option value="1">能</option>
                            <option value="0">不能</option>
                        </select>
                    </div>
                    <div class="itemCn unableArrive">
                        <div class="orderItemTitle">能按时到货的数量</div>
                        <input type="text" class="form-control scheduledAmount" name="scheduledAmount" placeholder="请填写数量" />
                    </div>
                    <div class="itemCn unableArrive">
                        <div class="orderItemTitle">剩余数量的到货日期</div>
                        <input type="text" class="form-control surplusDate" name="surplusDate" placeholder="请选择日期" id="surplusDate">
                    </div>
                    <div class="itemCn itemCn-l unableArrive">
                        <div class="orderItemTitle">备注（如无备注，可忽略）
                            <span class="ty-right lenTip">0/30</span>
                        </div>
                        <input type="text" class="form-control memo" name="memo" placeholder="请录入" maxlength="30" onchange="setWordsNum($(this), 30)">
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
                <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="deliveryDateConfirm()">确 定</span>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
    <%-- 订购信息展示 --%>
    <div class="bonceContainer bounce-red" id="linkOrd">
        <div class="bonceHead">
            <span class="noGs">订购信息</span>
            <span class="ysGs">！ 重要提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="noGs">当前该商品无 <span class="ty-color-red">其他</span> 发货需求</div>
            <div class="ysGs">
                <p><span id="linkOrdName">内部名称</span> 当前虽有 <span id="linkOrdStock">当前库存</span> <span id="linkOrdUnit">计量单位</span> ，但该商品尚有如下发货需求：</p>
                <div style="max-height:300px; overflow: auto; ">
                    <table class="ty-table" id="linkOrdTb">
                        <tr>
                            <td width="10%">序号</td>
                            <td width="15%">订单号</td>
                            <td width="15%">负责人</td>
                            <td width="10%">数量</td>
                            <td width="15%">要求到货时间</td>
                            <td width="35%">客户名称</td>
                        </tr>
                        <tr>
                            <td>序号</td>
                            <td>订单号</td>
                            <td>负责人</td>
                            <td>数量</td>
                            <td>要求到货时间</td>
                            <td>客户名称</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">确定</span>
        </div>
    </div>
</div>
<%--</div>--%>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>订单评审</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="mainCon mainCon1">
                    <div class="clear gapBt">
                        <ul class="ty-secondTab ty-left">
                            <li class="ty-active" data-align="0">待生产评审的订单</li>
                            <li data-align="1">待销售评审的订单</li>
                        </ul>
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5 ty-right">已终止评审的订单</span>
                    </div>
                    <table class="ty-table ty-table-control reviewList0">
                        <thead>
                        <tr>
                            <td width="20%">订单收到日期</td>
                            <td width="15%">客户名称</td>
                            <td width="15%">订单号</td>
                            <td width="20%">评审者</td>
                            <td width="15%">创建</td>
                            <td width="15%">操作</td>
                        </tr>
                        </thead>
                        <tbody>  </tbody>
                    </table>
                    <table class="ty-table ty-table-control reviewList1">
                        <thead>
                        <tr>
                            <td width="20%">订单收到日期</td>
                            <td width="15%">客户名称</td>
                            <td width="15%">订单号</td>
                            <td width="20%">评审者</td>
                            <td width="15%">创建</td>
                            <td width="15%">操作</td>
                        </tr>
                        </thead>
                        <tbody>  </tbody>
                    </table>
                </div>
                <div class="mainCon mainCon2">
                    <div class="headGroup clear">
                        <div class="pageBack ty-left">
                            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" id="backInit" onclick="showMainCon(1)">返 回</span>
                        </div>
                        <div class="ty-right clear">
                            <span class="countInfo">共需评审<span id="totalNum"></span>条数据，已评审<span id="sumNum"></span>条，其中能按时到货的<span id="okNum"></span>条</span>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapFar" onclick="review()">评审完成，提交</span>
                        </div>
                    </div>
                    <div id="phaseNum" class="hd"></div>
                    <p class="yyTip">以下各条商品在“要求到货日期”能到货的数量分别为多少？请评估。对于不能全部到货的，还需确认“剩余数量的到货日期”，并选择！</p>
                    <table class="ty-table ty-table-control goReviewList0">
                        <thead>
                        <tr>
                            <td width="200">商品代号</td>
                            <td width="200">商品名称</td>
                            <td width="130">计量单位</td>
                            <td width="130">订购数量</td>
                            <td>要求到货日期</td>
                            <td width="170">收货地点</td>
                            <td>能按时到货的数量</td>
                            <td>剩余数量的到货日期</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <span>详情</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <table class="ty-table ty-table-control goReviewList1">
                        <thead>
                        <tr>
                            <td>状态</td>
                            <td width="200">商品代号</td>
                            <td width="200">商品名称</td>
                            <td width="130">计量单位</td>
                            <td width="130">订购数量</td>
                            <td>要求到货日期</td>
                            <td width="170">收货地点</td>
                            <td>能按时到货的数量</td>
                            <td>剩余数量的到货日期</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <span>详情</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div id="ye_setList"></div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="${pageContext.request.contextPath }/script/production/review.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
