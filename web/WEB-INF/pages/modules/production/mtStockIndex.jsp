<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/production/review.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>
    .adddis{ margin-bottom:20px;}
    .bonceCon input,.bonceCon select,.bonceCon textarea {
        border:1px solid #dee8f0;
        background-color: #fff;
        line-height: 36px;
        text-align: left;
        padding: 0 8px;
        color: #3f3f3f;
        width: 180px;
    }
    .bonceCon textarea{
        line-height: 20px;
        padding:5px;
    }
    .bonceCon input{
        height: 36px;
    }
    .bonceCon input:disabled{
        background-color: #dff0ff;
    }
    .bonceCon input:focus,.bonceCon select:focus,.bonceCon textarea:focus{
        border:1px solid #5d9cec;
    }
    .formItem{
        width: 270px;
        float: left;
        margin: 8px;
    }
    .formTitle,.formCon{
        float: left;
        line-height: 36px;
    }
    .formTitle{
        width: 90px;
        text-align: right;
        padding:0 10px;
    }
    .formCon{
        width: 180px;
    }
    .select2-container{
        z-index: 9999999;
    }
     .hoverDetail{
         cursor: default;
     }
    .hoverDetailCon{
        position: absolute;
        top: 0;
        right: 50%;
        width: 300px;
        min-height: 120px;
        background-color: #fff;
        padding: 10px 20px 20px 20px;
        border:1px solid #ccc;
        box-shadow: 0 0 5px #ccc;
        display: none;
        text-align: left;
        z-index: 999;
    }
    .hoverDetail:hover .hoverDetailCon{
        display: block;
    }
</style>
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
<%--入库申请--新增商品提示框--%>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-green" id="addGoods" style="min-width:600px;">
        <div class="bonceHead">
            <span>新增商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="tyFormControl">
                <div class="row">
                    <div class="formItem">
                        <div class="formTitle"><span class="ty-color-red">*</span> 商品代号</div>
                        <div class="formCon"><select id="outerSn" style="width:180px"><option></option></select></div>
                    </div>
                    <div class="formItem">
                        <div class="formTitle">商品名称</div>
                        <div class="formCon"><input type="text" id="outerName" disabled="disabled"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="formItem">
                        <div class="formTitle">产品图号</div>
                        <div class="formCon"><input type="text"  id="innerSn" disabled="disabled"></div>
                    </div>
                    <div class="formItem">
                        <div class="formTitle">产品名称</div>
                        <div class="formCon"><input type="text" id="innerName" disabled="disabled"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="formItem">
                        <div class="formTitle">单位</div>
                        <div class="formCon"><input type="text" id="unit" disabled="disabled"></div>
                    </div>
                    <div class="formItem">
                        <div class="formTitle"><span class="ty-color-red">*</span> 数量</div>
                        <div class="formCon"><input type="text" placeholder="请输入数量" id="inPlan" onkeyup="clearNoNum(this)"></div>
                    </div>

                </div>
                <div class="row">
                    <div class="formItem">
                        <div class="formTitle">生产日期</div>
                        <div class="formCon"><input type="text" placeholder="请选择生产日期" id="produceDate"></div>
                    </div>
                    <div class="formItem">
                        <div class="formTitle">产品到期日</div>
                        <div class="formCon"><input type="text" placeholder="请选择产品到期日" id="arriveDate" autocomplete="off" disableautocomplete readonly></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel() ">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" disabled="disabled" id="addGoodsBtn" onclick="sureAddGoods()">确定</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--入库申请提示框--%>
    <div class="bonceContainer bounce-green" id="storageApply" style="min-width:970px;">
        <div class="bonceHead">
            <span>入库申请</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 450px;overflow-y: auto">
            <span class="ty-btn ty-btn-big ty-btn-green ty-circle-3"  onclick="addGoodsBtn()">新增商品</span>
            <table class="ty-table ty-table-control goodlist">
                <thead>
                <tr>
                    <td>序号</td>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>数量</td>
                    <td>生产日期</td>
                    <td>产品到期日</td>
                    <td>操作</td>
                </tr>
                </thead>
                <tbody id="stockList">
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="stockApply" onclick="sureStockApply() ">提交</button>
        </div>
    </div>
    <%--入库申请-待处理-查看弹窗--%>
    <div class="bonceContainer bounce-blue" id="seeStorageApply" style="min-width:1100px;">
        <div class="bonceHead">
            <span>入库申请详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 450px;overflow-y: auto">
            <table class="ty-table ty-table-control goodlist">
                <thead>
                <tr>
                    <td>序号      </td>
                    <td>申请时间   </td>
                    <td>申请人   </td>
                    <td>商品代号 </td>
                    <td>商品名称 </td>
                    <td>产品图号 </td>
                    <td>产品名称 </td>
                    <td>单位     </td>
                    <td>数量     </td>
                    <td>生产日期  </td>
                    <td>产品到期日</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>入库申请</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <span class="hd" id="curN">待处理</span>
                <div>
                    <span class="ty-right ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="storageApplyBtn()">入库申请</span>
                </div>
                <ul class="ty-secondTab">
                    <li class="ty-active">待处理</li>
                    <li>待入库</li>
                    <li>已入库</li>
                    <li>已驳回</li>
                </ul>
                <%--待处理--%>
                <div class="tblContainer">
                    <table class="ty-table ty-table-control panel_1" id="waitHandle">
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>申请人</td>
                            <td>申请时间</td>
                            <td>查看详情</td>
                        </tr>
                        </thead>
                        <tbody id="tab1Bdy">
                        </tbody>
                    </table>
                </div>
                <%--待入库--%>
                <div class="tblContainer" style="display: none;">
                    <table class="ty-table ty-table-control panel_2" id="waitStorage">
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>申请时间</td>
                            <td>申请人</td>
                            <td>商品代号</td>
                            <td>商品名称</td>
                            <td>产品图号</td>
                            <td>产品名称</td>
                            <td>单位</td>
                            <td>数量</td>
                            <td>生产日期</td>
                            <td>产品到期日</td>
                            <td>入库流程</td>
                        </tr>
                        </thead>
                        <tbody id="tab2Bdy">
                        </tbody>
                    </table>
                </div>
                <%--已入库--%>
                <div class="tblContainer" style="display: none;">
                    <table class="ty-table ty-table-control panel_3" id="alreadyStorage">
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>申请时间</td>
                            <td>申请人</td>
                            <td>商品代号</td>
                            <td>商品名称</td>
                            <td>产品图号</td>
                            <td>产品名称</td>
                            <td>单位</td>
                            <td>申请入库数</td>
                            <td>实际入库数</td>
                            <td>生产日期</td>
                            <td>产品到期日</td>
                            <td>入库流程</td>
                        </tr>
                        </thead>
                        <tbody id="tab3Bdy">
                        </tbody>
                    </table>
                </div>
                <%--已驳回--%>
                <div class="tblContainer" style="display: none;">
                    <table class="ty-table ty-table-control panel_4" id="alreadyRejected">
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>申请时间</td>
                            <td>申请人</td>
                            <td>商品代号</td>
                            <td>商品名称</td>
                            <td>产品图号</td>
                            <td>产品名称</td>
                            <td>单位</td>
                            <td>数量</td>
                            <td>生产日期</td>
                            <td>产品到期日</td>
                            <td>入库流程</td>
                        </tr>
                        </thead>
                        <tbody id="tab4Bdy">
                        </tbody>
                    </table>
                </div>
                <div id="ye_mtStorkIndex"></div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/production/mtStockIndex.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
