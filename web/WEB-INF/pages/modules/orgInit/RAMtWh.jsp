<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/event/icon/iconfont.css" />
<link rel="stylesheet" type="text/css" href="../css/orgInit/initCommon.css" />
<link rel="stylesheet" type="text/css" href="../css/orgInit/RAMtWh.css" />
<%--<link href="../css/system/manages.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />--%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="bounce_iKnow">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon iKnowWord">
<%--                <p>上步初始化尚未完成，故本模块初始化暂无法开始！</p>--%>
<%--                <p>上步初始化：</p>--%>
<%--                <p>负责人：</p>--%>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue sureBtn" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue sureBtn" onclick="sureCompleteManager()">确定</span>
        </div>
    </div>

    <%--导入--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #ddebf7;">
            <form action="../export/importUser.do" id="employeeImport" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：下载空白的“职工名单”。</p>
                        <div class="flexRow">
                            <span>职工名单</span>
                            <a href="../assets/oralResource/template/employee_blank_sheet.xls"
                               id="mould1" download="职工名单.xls" class="ty-btn ty-btn-blue">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“职工名单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="sysUseUploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="bounce.cancel()">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="sysUseImportOk()">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加、删除或修改所下载“职工档案”空白表的“列”，否则上传会失败。</span></br>
                            <span>2、在电脑上保存“职工名单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot" style="background: #ddebf7;">
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--录入初始库存数量/库位--%>
    <div class="bonceContainer bounce-blue" id="enterInitialStock" style="width:1000px;">
        <div class="bonceHead">
            <span>录入初始库存数量/库位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <table class="kj-table" id="initialStockList">
                    <thead>
                    <tr>
                        <td>材料名称</td>
                        <td>材料代号</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>创建人</td>
                        <td>计量单位</td>
                        <td>初始库存</td>
                        <td>占用库位</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div>
                    <div class="noSup">
                        <p class="gap">系统中本材料未录入定点供应商。</p>
                        <div class="selectSect">
                            <div class="clear checkCondition">
                                <div><span class="fa fa-square-o" id="toggleCheck" onclick="toggleCheck($(this))"></span>仅录入初始库存数量，暂不选择库位
                                    <input type="hidden" id="toggleCheckNum"/></div>
                            </div>
                            <div>
                                <div class="toggleCheck1">
                                    <span> 请录入该材料的初始库存数量</span> <input type="text" id="initKu0" onkeyup="clearNoNum(this)"><span class="goodsUnit"></span>
                                </div>
                                <div class="toggleCheck0">
                                    <div class="supKuItem">
                                        <div>
                                            <div> </div>
                                            <div> <span class="addOneStore" id="addOneStore3" onclick="newStore($(this))">增加新库位</span> </div>
                                        </div>
                                        <div class="kuItem">
                                            <div>
                                                <span class="gapRt">库位</span><select onchange="fillStore($(this))" class="addInitStore"></select>
                                                <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                                            </div>
                                            <div>
                                                <span class="gapRt">数量</span> <input type="text" onkeyup="clearNoNum(this)"/><span class="goodsUnit"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="hasSup">
                        <p class="gap" style="text-indent: 2em;">系统中本材料被录入 <span id="supNum"></span>个供应商。</p>
                        <div class="selectSect">
                            <div class="clear checkCondition">
                                <div><span class="fa fa-square-o" onclick="turnCheck($(this) , 1)"></span>仅录入初始库存数量，暂不选择库位</div>
                                <div><span class="fa fa-square-o" onclick="turnCheck($(this) , 2)"></span>录入初始库存总数，不区分是哪家供应商的</div>
                            </div>
                            <div>
                                <input type="hidden" id="option">
                                <ul class="kuList">
                                    <li class="supKuItem">
                                        <div>
                                            <div> <p>供应商： SSSS</p> <p>简称： SSSS　 代号：XXXX</p>  </div>
                                            <div> <span class="addOneStore" onclick="newStore($(this))">增加新库位</span> </div>
                                        </div>
                                        <div class="kuItem">
                                            <div>
                                                <span class="gapRt">库位</span><select onchange="fillStore($(this))" class="addInitStore"></select>
                                                <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                                            </div>
                                            <div>
                                                <span class="gapRt">数量</span> <input type="text" onkeyup="clearNoNum(this)"/><span class="goodsUnit"></span>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                                <div class="Check1">
                                    <span> 请录入该材料的初始库存数量</span> <input type="text" id="initKu" onkeyup="clearNoNum(this)"><span class="goodsUnit"></span>
                                </div>
                                <div class="Check2">
                                    <div class="supKuItem">
                                        <div>
                                            <div> </div>
                                            <div> <span class="addOneStore" id="addOneStore2" onclick="newStore($(this))">增加新库位</span> </div>
                                        </div>
                                        <div class="kuItem">
                                            <div>
                                                <span class="gapRt">库位</span><select onchange="fillStore($(this))" class="addInitStore"></select>
                                                <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                                            </div>
                                            <div>
                                                <span class="gapRt">数量</span> <input type="text" onkeyup="clearNoNum(this)"/><span class="goodsUnit"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addInitialStock()">确定</span>
        </div>
    </div>
    <%--重新选择库位--%>
    <div class="bonceContainer bounce-blue" id="reselectLocation" style="width:1000px;">
        <div class="bonceHead">
            <span id="reselectTtl">重新选择库位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <table class="kj-table" id="reselectList">
                    <thead>
                    <tr>
                        <td>材料名称</td>
                        <td>材料代号</td>
                        <td>型号</td>
                        <td>规格</td>
                        <td>创建人</td>
                        <td>计量单位</td>
                        <td>当前库存</td>
                        <td>占用库位</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div>
                    <div class="noChoose">
                        <p><span class="red">！提示</span>您需先选择库位，之后录入该库位上所放置该材料的数量</p>
                        <div><span class="fa fa-square-o" onclick="reSetFa($(this),1)"></span>重新选择库位，且不再区分是哪家供应商的 </div>
                    </div>
                    <div class="hasChoose">
                        <input type="hidden" id="reSetFa">
                        <p><span class="red">！提示</span>重新选择库位，意味着放弃本材料目前所占的库位！</p>
                        <div class="case2"><span class="fa fa-square-o" onclick="reSetFa($(this),0)"></span>放弃本材料所占库位，且本材料暂不再选择库位 </div>
                        <div class="case1"><span class="fa fa-square-o" onclick="reSetFa($(this),1)"></span>重新选择库位，且不再区分是哪家供应商的 </div>
                    </div>
                    <div class="storeList">
                        <div id="noStore" >
                        </div>
                        <div id="noSup">
                            <div class="supKuItem">
                                <div>
                                    <div> </div>
                                    <div> <span class="addOneStore" id="addOneStore4" onclick="newStore($(this))">增加新库位</span> </div>
                                </div>
                                <div class="kuItem">
                                    <div>
                                        <span class="gapRt">库位</span><select onchange="fillStore($(this))" class="addInitStore"></select>
                                        <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                                    </div>
                                    <div>
                                        <span class="gapRt">数量</span> <input type="text" onkeyup="clearNoNum(this)"/><span class="goodsUnit"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="hasSup">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="updateLocation(2)">确定</span>
        </div>
    </div>
    <%-- 修改初始库存--%>
    <div class="bonceContainer bounce-blue" id="updateStores">
        <div class="bonceHead">
            <span>修改初始库存</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>请输入新的初始库存</p>
            <div>
                <input id="newStore" type="text" onkeyup="tofixed3(this)" />
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">确定</span>
        </div>
    </div>
    <%-- 我知道了  --%>
    <div class="bonceContainer bounce-red" id="knowTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <div id="knowTipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 还有必填项尚未填写！  --%>
    <div class="bonceContainer bounce-red" id="nullTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <div id="nullTipMs">还有必填项尚未填写！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 库位情况查看--%>
    <div class="bonceContainer bounce-blue" id="seeStores" style="min-width:835px;">
        <div class="bonceHead">
            <span>库位情况查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="seeStoresInfo">
                <span>库位</span>
                <span id="seeStoresName"></span>
            </div>
            <table class="ty-table ty-table-none  bg-yellow storeTb storesCreateInfo">
                <thead>
                <tr>
                    <td width="15%">所属库房</td>
                    <td width="20%">所属区域</td>
                    <td width="15%">所属货架</td>
                    <td width="10%">所属层数</td>
                    <td width="10%" class="hdNot"></td>
                    <td width="30%">创建人</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <p>该库位已放置的材料</p>
            <table class="ty-table ty-table-none  bg-yellow storeTb scienceCreateInfo">
                <thead>
                <tr>
                    <td width="20%">材料名称</td>
                    <td width="20%">材料代号</td>
                    <td width="15%">型号</td>
                    <td width="15%">规格</td>
                    <td width="15%">计量单位</td>
                    <td width="15%">数量</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel();">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>原辅材料库</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container" id="home">
                <div class="ty-page-header" style="display: none">
                    <div class="page-header__left" onclick="back()">
                        <i class="iconfont icon-houtui icon-back"></i>
                        <span>返回</span>
                    </div>
                    <div class="page-header__content" onclick="backToMain()">
                        <i class="iconfont icon-zhuye icon-back"></i>
                        <span>主页</span>
                    </div>
                </div>
                <div class="page" page="main">
                    <div class="panel-box">
                        <div class="ty-alert">
                            欢迎启用“原辅材料库”，请完成以下两项初始化工作！
                        </div>
                        <table class="kj-table tbl_main">
                            <thead>
                            <tr>
                                <td>需编辑的项</td>
                                <td>是否必须编辑</td>
                                <td>状态</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <%--初始库存编辑--%>
                <div class="page" page="initialInventory_edit">
                    <div class="topRightBtn hidePart">
                        <div class="ty-radio checkRadio">
                            <input type="radio" id="initialInventory_done1" name="">
                            <label for="initialInventory_done1"></label> 原辅材料的初始库存已全部编辑完成，初始化结束！
                        </div>
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="initComplete()">确定</button>
                    </div>
                    <div class="panel-box_item">
                        <div class="left_item">初始库存有待确定的原辅材料共如下<b class="ty-color-blue pendingNum"></b>条</div>
                        <div class="right_item hidePart">
                            <div></div>
                            <div>
                                初始库存已确定的原辅材料共<b class="ty-color-blue certainNum"></b>条
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="seeHandledRAMtWh()">查看</button>
                            </div>
                        </div>
                    </div>
                    <table class="kj-table kj-table-striped tbl_initialInventoryPending">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye_initialInventoryPending"></div>
                </div>
                <div class="page" page="initialInventory_see">
                    <div class="topRightBtn hidePart">
                        <div class="ty-radio checkRadio">
                            <input type="radio" id="initialInventory_done2" name="">
                            <label for="initialInventory_done2"></label> 原辅材料的初始库存已全部编辑完成，初始化结束！
                        </div>
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="initComplete()">确定</button>
                    </div>
                    <div>
                        <div class="panel-box">
                            <div class="panel-box_item lastEdit"></div>
                        </div>
                        <div class="panel-box">
                            <div class="panel-box_item">该次编辑之后</div>
                            <div class="ty-alert ty-alert-info">
                                尚有<b class="ty-color-blue toEditMtNum"></b>条材料的初始库存有待编辑
                                <div class="btn-group">
                                    <span class="link-blue" onclick="toEditMt()">编辑</span>
                                </div>
                            </div>
                            <section class="addedWarehouse">
                                <div class="ty-alert ty-alert-padding">
                                    <div class="LP_16">新增了 <b class="ty-color-blue addedWhNum"></b> 个仓库（如需要，请修改已编辑了初始库存的数据）</div>
                                </div>
                                <div class="addedWarehouse_box"></div>
                            </section>

                            <div class="ty-alert ty-alert-info" style="margin-bottom: 16px">
                                初始库存已确定的原辅材料共 <b class="ty-color-blue editedMtNum"></b> 条。目前，这些数据还可修改
                                <div class="btn-group">
                                    <span class="link-blue" onclick="seeHandledRAMtWh()">修改</span>
                                </div>
                            </div>
                        </div>
                        <section class="panel-box changedWarehouse">
                            <div class="panel-box_item">该次编辑之后，以下各仓库的布局又被修改，这些仓库内原辅材料的初始库存等信息需重新设置</div>
                            <div class="changedWarehouse_box"></div>
                        </section>
                    </div>
                </div>
                <div class="page" page="initialInventory_handled">
                    <div class="panel-box_item">
                        <div class="left_item">初始库存已确定的原辅材料共如下<b class="certainNum"></b>条</div>
                    </div>
                    <table class="kj-table kj-table-striped tbl_initialInventoryHandled">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>初始库存</td>
                            <td>最新一次的编辑</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye_initialInventoryHandled"></div>
                </div>
                <div class="page" page="initialInventory_changeWh_edit">
                    <div class="panel-box_item changeWh_editTip"></div>
                    <table class="kj-table kj-table-striped tbl_initialInventoryChangeWhEdit">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>初始库存总数</td>
                            <td>应重新选择库位的数量</td>
                            <td>是否已重新选择库位</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/orgInit/initCommon.js?v=SVN_REVISION"></script>
<script src="../script/orgInit/RAMtWh.js?v=SVN_REVISION"></script>
</body>
</html>