<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%--<link href="../css/system/manages.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />--%>
<link rel="stylesheet" type="text/css" href="../css/common/theme/menuTree.css?v=SVN_REVISION"/>
<link rel="stylesheet" type="text/css" href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"/>
<link rel="stylesheet" type="text/css" href="../css/general/employeeInport.css?v=SVN_REVISION"  />
<link rel="stylesheet" type="text/css" href="../css/event/icon/iconfont.css" />
<link href="../css/role/roleIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<link href="../css/authority/approveSetting.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<link rel="stylesheet" type="text/css" href="../css/orgInit/initCommon.css" />
<link rel="stylesheet" type="text/css" href="../css/orgInit/authority.css" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed3">
    <%--导入失败--%>
    <div class="bonceContainer bounce-red" id="importantTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <h4>导入失败！</h4>
                <div>
                    <div>原因可能为：</div>
                    <div>1、修改了所下载表格中的“列”。</div>
                    <div>2、选错了文件。</div>
                    <div>3、文件太大，或里面含有图片等。</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed3.cancel()">知道了</span>
        </div>
    </div>
    <%--知道了 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed3.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道了 - 提示--%>
    <div class="bonceContainer bounce-red" id="iknowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="iknowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed3.cancel()">我知道了</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%---------------- 主页部分------------------%>
    <%--必须分配的权限 - 编辑--%>
    <div class="bonceContainer bounce-blue" id="editRole" style="width: 500px">
        <div class="bonceHead">
            <span>请选择人员</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="text-center">
                <span class="ty-color-red">* </span> <select name="role"></select>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn" id="sureEditRoleBtn">确定</button>
        </div>
    </div>
    <%---------------- 审批设置部分------------------%>
    <%-- Wonderss中的加班功能--%>
    <div class="bonceContainer bounce-blue" id="wonderssOverTimeTips">
        <div class="bonceHead">
            <span>Wonderss中的加班功能</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
                <pre style="border: none;background-color:inherit">
Wonderss系统中关于加班的功能如下：

1 职工加班需提前申请，且需经审批通过。
&nbsp;&nbsp;&nbsp;* 申请者每次最多可提出24小时的加班申请。
&nbsp;&nbsp;&nbsp;* 跨天的加班，申请者需分次提交申请。
&nbsp;&nbsp;&nbsp;* 加班需提前多久申请，系统带有默认值，可修改。
&nbsp;&nbsp;&nbsp;* 系统默认加班申请需经最高领导的审批，可修改。

2 职工未提前申请的加班，总务确认无误后可修改其考勤。
&nbsp;&nbsp;&nbsp;* 系统带有默认为关闭状态的“补报加班”功能，该状态可修改。

3 加班后，职工还需填报实际加班的情况，并需经审批。
&nbsp;&nbsp;&nbsp;* 实际加班情况的审批流程，与加班申请的审批流程相同。
&nbsp;&nbsp;&nbsp;* 实际加班的数据超过规定时间未提交的，将由系统驳回。

4 实际加班情况审批通过后，加班数据即进入考勤模块。
                </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- Wonderss中的请假功能--%>
    <div class="bonceContainer bounce-blue" id="wonderssLeaveTips">
        <div class="bonceHead">
            <span>Wonderss中的请假功能</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
            <pre style="border: none;background-color:inherit">
Wonderss系统中关于请假的功能如下：

1 职工请假需提前申请，且需经审批通过。
  * 请假需提前多久申请，系统带有默认值，可修改。
  * 系统默认请假申请需经最高领导的审批，可修改。

2 职工未提前申请的请假，总务确认无误后可修改其考勤。
  * 系统带有默认为关闭状态的“事后补假”功能，该状态可修改。

3 职工请假后还可提出“提前结束假期”的申请，但需经审批。
  * 提前结束假期申请的审批流程，与请假申请的审批流程相同。

4 请假申请经审批通过后，其数据将进入考勤模块。
            </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- 仓库模式的说明--%>
    <div class="bonceContainer bounce-blue" id="stockModeDes" style="width: 700px">
        <div class="bonceHead">
            <span>仓库模式的说明</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
            <pre style="border: none;background-color:inherit">
1 Wonderss系统为机构提供两种仓库模式：智能仓库与非智能仓库。

2 在仓库中，需对材料的数量进行管理。

3 非智能仓库模式下，还可对材料的供应商与包装进行管理。

4 智能仓库模式下，需对材料的供应商、包装、保质期、库位及批次号进行管理，此外系统还提供先进先出功能。
            </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="overTimeApplyAdvanceTime">
        <div class="bonceHead">
            <span>加班申请的提出时间</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px; margin: auto">
                <div class="ty-alert">
                    职工要加班，需至少提前多久提出申请？
                </div>
                <select name="ruleTime" class="ty-inputSelect " style="width: 100%">
                    <option value="30">30分钟</option>
                    <option value="60">60分钟</option>
                    <option value="90">90分钟</option>
                    <option value="120">120分钟</option>
                </select>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()"  >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="leaveApplyAdvanceTime">
        <div class="bonceHead">
            <span>请假申请的提出时间</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px; margin: auto">
                <div class="ty-alert">
                    职工要请假，需至少提前多久提出申请？
                </div>
                <select name="ruleTime" class="ty-inputSelect " style="width: 100%">
                    <option value="30">30分钟</option>
                    <option value="60">60分钟</option>
                    <option value="90">90分钟</option>
                    <option value="120">120分钟</option>
                </select>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()"  >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="changeFactApplyDurationRule">
        <div class="bonceHead">
            <span>提交实际加班数据的规则</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px; margin: auto">
                <div class="ty-alert">
                    职工加班后，需在几天内提交实际加班的数据？
                </div>
                <div>
                    <select name="applyDuration" class="ty-inputSelect " style="width: 70%">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                    </select> 天内
                </div>
                <div style="margin: 8px 0">
                    <small class="ty-color-blue">注：过期不提交的，系统将记作实际未加班！</small>
                </div>
                <div class="ty-radio">
                    <input type="radio" id="anyTimeApply">
                    <label for="anyTimeApply"></label>职工实际加班的数据拖至何时提交都可以
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()"  >取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeRule()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%---------------- 审批设置部分------------------%>
    <%-- 请求处理 选择审批人--%>
    <div class="bonceContainer bounce-blue" id="chooseApproveP" >
        <div class="bonceHead">
            <span>添加审批人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="approvePList userList">
                <%--<div>
                    <p onclick='toggleFa($(this))'><span class="fa fa-circle-o"></span> <span>直接上级</span></p>
                </div>--%>
                <div id="approveTree"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <p id="tp2" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="selectOK()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
        </div>
    </div>
    <%-- 二级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="secdTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="secdtipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">确认</span>
        </div>
    </div>
    <%-- 修改提示--%>
    <div class="bonceContainer bounce-red" id="overTimeEditTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel();"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center">修改某级审批者，则之后的各级审批均需重新设置。</div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3 " onclick="bounce_Fixed.cancel();"  >取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " onclick="chooseTipOk()"  >确定要修改</span>
        </div>
    </div>
    <%-- 加班查看 --%>
    <div class="bonceContainer bounce-blue" id="overTime">
        <div class="bonceHead">
            <span>查看加班审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="overTimeSetting" style="width: 400px; margin: 0 auto">
                <div class="text-right"><div class="link-blue" onclick="wonderssOverTimeTips()">Wonderss中的加班功能</div></div>
                <div class="p_row">加班设置的现状如下，如需要，可修改。	</div>
                <div class="p_row"><div>1 职工要加班，需至少提前 <span class="upperLimit"></span> 分钟提出申请 </div><div class="link-blue" onclick="changeOverTimeApplyAdvanceTime()">修改</div></div>
                <div class="p_row"><div>2 职工端“补报加班”的功能处于 <span class="state_makeAfterFact"></span> 状态 </div><div class="link-blue" onclick="changeRepayOverTimeState()">修改</div></div>
                <div class="p_row"><div>3 职工加班后，需 <span class="state_factDurationRule"></span> 天内提交实际加班的数据 </div><div class="link-blue" onclick="changeFactApplyDurationRule()">修改</div></div>
                <div class="p_row"><div>4 加班的审批 </div><div class="link-blue" onclick="overTimeEdit()">修改</div></div>
                <div id="approvList"></div>
                <small class="applyTip">
                    <div>注：</div>
                    <p>加班跨天时，申请者需分次提交申请。</p>
                    <p>申请者每次最多可提出24小时的加班申请。</p>
                </small>
            </section>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%-- 报销查看 --%>
    <div class="bonceContainer bounce-blue" id="reimburse">
        <div class="bonceHead">
            <span>查看报销审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="applyTip">
                <div>审批记录：</div>
                <p></p>
            </div>
            <div id="flowList31"> </div>

        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="reimburseTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p style="text-align: center">确定放弃本次编辑吗？</p>
            <p></p>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="finCancelAll()"  >确定</span>
        </div>
    </div>
    <%-- 请假查看 --%>
    <div class="bonceContainer bounce-blue" id="leave">
        <div class="bonceHead">
            <span>查看请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="overTimeSetting" style="width: 400px;margin: 0 auto;">
                <p class="text-right"><div class="link-blue" onclick="wonderssLeaveTips()">Wonderss中的请假功能</div></p>
                <div class="p_row">请假设置的现状如下，如需要，可修改。	</div>
                <div class="p_row"><div>1 职工要请假，需至少提前 <span class="upperLimit"></span> 分钟提出申请 </div><div class="link-blue" onclick="changeLeaveApplyAdvanceTime()">修改</div></div>
                <div class="p_row"><div>2 职工端“事后补假”的功能处于 <span class="state_makeAfterFact"></span> 状态</div><div class="link-blue" onclick="changeRepayLeaveState()">修改</div></div>
                <div class="p_row"><div>3 请假的审批	 </div><div class="link-blue" onclick="leaveEdit()">修改</div></div>
                <div id="leaveApprovList"></div>
                <small class="ty-color-blue leaveSeeTip">职工无法提交超过<span class="maxHur"></span>的请假申请。</small>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%-- 请假修改记录查看 --%>
    <div class="bonceContainer bounce-blue" id="leaveRecordSee">
        <div class="bonceHead">
            <span>查看请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear">
                <div id="approvRecord" class="ty-right">
                </div>
            </div>
            <div id="leaveSetList"></div>
            <div class="leaveSeeTip">
                职工无法提交超过<span class="maxHur"></span>的请假申请。
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%-- 请假 设置下一级--%>
    <div class="bonceContainer bounce-red" id="leaveNextLevel">
        <div class="bonceHead">
            <span> ！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="leaveNextStep">
                <p onclick="setNextLevel($(this))"><span><i class="fa fa-circle-o" data-step="1"></i> 设置下一级</span></p>
                <p onclick="setNextLevel($(this))"><span><i class="fa fa-circle-o" data-step="2"></i> 选择完毕，且设置完毕。</span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="leaveNextOk()">确定</span>
        </div>
    </div>
    <%-- 报销 设置下一级--%>
    <div class="bonceContainer bounce-red" id="finNextLevel" data-forItem="">
        <div class="bonceHead">
            <span> ！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="padding-left:50px; ">
            <div id="finNextStep">
                <p onclick="setNextLevel($(this))"><span><i class="fa fa-circle-o" data-step="1"></i> 设置下一级</span></p>
                <p onclick="setNextLevel($(this))"><span><i class="fa fa-circle-o" data-step="2"></i> 选择完毕，且设置完毕。</span></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" onclick="cancelNext()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="finNextOk()">确定</span>
        </div>
    </div>
    <%-- 修改设置 --%>
    <div class="bonceContainer bounce-blue" id="saleApprove_common" style="width: 500px;">
        <div class="bonceHead">
            <span>修改设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div>
                    <p class="saleTip1">对于客户发来订单中的数量与交期，是否需公司各部门评审</p>
                    <p class="saleTip2">对于采购来的材料，入库前是否需要检验</p>
                    <p class="saleTip3">生产出的货物入成品库前，是否需要检验</p>
                </div>
                <div style="margin-bottom: 8px">
                    <div class="changeDot">
                        <div class="ty-radio">
                            <input type="radio" name="applyType" id="need" value="1">
                            <label for="need"></label> <span class="needStr">需要</span>
                        </div>
                    </div>
                    <div class="changeDot">
                        <div class="ty-radio">
                            <input type="radio" name="applyType" id="noNeed" value="0">
                            <label for="noNeed"></label> <span class="noNeedStr">不需要</span>
                        </div>
                    </div>
                </div>
                <div id="startDateSale" >
                    本修改将于 <input type="text" placeholder="请选择时间"/> 生效
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="saveCommoditycharge" onclick="applySubmit()">提交</button>
        </div>
    </div>
    <%-- 采购审批设置的修改--%>
    <div class="bonceContainer bounce-blue" id="purchaseApprovalSettingsChange">
        <div class="bonceHead">
            <span class="bounce-title">审批设置的修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 0 auto">
                <div>
                    <div class="page_needApprove">
                        <div class="item-flex">
                            本页面上可更换审批者，可添加一层或多层审批，为多层审批时，还可删除某层审批！
                        </div>
                        <div class="item-flex">
                            <div class="item-auto text-right">
                                <button class="link-blue" onclick="addOneLevel($(this))">添加一层审批</button>
                            </div>
                        </div>
                        <div class="flows"></div>
                    </div>
                    <div class="page_noNeedApprove">
                        <div class="item">
                            确定要将此项审批修改至无需审批吗？
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title"><span class="extraTip">如确定，</span>请选择本次修改的拟生效时间！</div>
                    <input class="ty-inputText" type="text" name="openDate" id="openDate" placeholder="请选择">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeApprove()">提交</button>
        </div>
    </div>
    <%-- 产品基本信息的创建模式-修改设置 --%>
    <div class="bonceContainer bounce-blue" id="ptCreationModeChange" style="width: 800px;">
        <div class="bonceHead">
            <span>修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="ptMode">
                    <p>产品图号与名称的创建分为模式1与模式2两种</p>
                    <div>模式1为手动关联，即商品与产品需各自创建并维护，二者间需逐一关联</div>
                    <div>模式2为自动关联，即商品创建后，产品由系统生成，其基本信息与商品完全相同，修改时为联动修改</div>
                    <hr/>
                    <div class="modeSect modeOption" data-align="1">
                        <p>与专属商品有关产品图号与名称的创建模式</p>
                        <div class="ty-radio">
                            <input type="hidden" value="1">
                            <span class="fa fa-circle-o" id="zs_modlue1" data-val="1"></span> <span>模式1</span>
                        </div>
                        <div class="ty-radio">
                            <input type="hidden" value="2">
                            <span class="fa fa-circle-o" id="zs_modlue2" data-val="2"></span> <span>模式2</span>
                        </div>
                    </div>
                    <hr/>
                    <div class="modeSect modeOption" data-align="2">
                        <p>与通用型商品有关产品图号与名称的创建模式</p>
                        <div class="ty-radio">
                            <span class="fa fa-circle-o" id="cm_modlue1" data-val="1"></span> <span>模式1</span>
                        </div>
                        <div class="ty-radio">
                            <span class="fa fa-circle-o" id="cm_modlue2" data-val="2"></span> <span>模式2</span>
                        </div>
                    </div>
                    <hr/>
                    <div class="modeSect authorAble" data-align="3">
                        <p><span class="authorCare">通用型</span>商品与产品的基本信息完全相同，一处如修改，另一处将跟随变动。该修改由以下哪方进行？</p>
                        <div class="ty-radio">
                            <span class="fa fa-circle-o" id="createAble" data-val="1"></span> <span>由有权限创建商品的职工修改</span>
                        </div>
                        <div class="ty-radio">
                            <span class="fa fa-circle-o" id="handAble" data-val="2"></span> <span>由有产品操作权限的职工修改</span>
                        </div>
                    </div>
                </div>
                <div id="pt_startDate">
                    本修改将于 <input type="text" placeholder="请选择时间"/> 生效
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel();">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="ptCreationModeChangeTj" onclick="applySubmit()">提交</button>
        </div>
    </div>
    <%-- 采购审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="purchaseApprovalSettingsSee" >
        <div class="bonceHead">
            <span class="bounce-title">采购审批设置查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">采购需 <span class="level"></span> 级审批</div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">开始执行的时间</div>
                    <div class="item-auto text-right openDate"></div>
                </div>
                <div class="kj-hr"></div>
                <div class="approvalFlows"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</button>
        </div>
    </div>
    <%-- 产品基本信息的创建模式-修改记录查看--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="ptModeSettingsSee" style="width: 700px;">
        <div class="bonceHead">
            <span class="bounce-title">查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <p>产品图号与名称的创建分为模式1与模式2两种</p>
                <div class="scan-row">
                    模式1为手动关联，即商品与产品需各自创建并维护，二者间需逐一关联<br/>
                    模式2为自动关联，即商品创建后，产品由系统生成，其基本信息与商品完全相同，修改时为联动修改
                </div>
                <hr/>
                <div class="scan-row">与专属商品有关产品图号与名称的创建模式：<span class="zs_modeType"></span></div>
                <hr/>
                <div class="scan-row">与通用型商品有关产品图号与名称的创建模式:<span class="cm_modeType"></span></div>
                <hr/>
                <div class="role_modeType">
                    <span></span>商品与产品的基本信息完全相同，一处如修改，另一处将跟随变动。该修改<span></span>进行</div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">关 闭</button>
        </div>
    </div>
    <%-- 修改仓库的模式--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="stockModeChange" >
        <div class="bonceHead">
            <span class="bounce-title">修改仓库的模式</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">仓库将改为<span class="newMode"></span>的模式！</div>
                    <div class="item-auto text-right">
                        <button class="link-blue" onclick="stockModeDes()">仓库模式的说明</button>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">本修改将于</div>
                    <div class="item-fix openDate"><input class="ty-inputText" type="text" name="openDate" id="stockModeChangeDate"></div>
                    <div class="item-auto text-right">生效</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="applySubmit()">确定</button>
        </div>
    </div>
    <%-- 修改仓库的模式 - 提示--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="stockModeChange_tip" >
        <div class="bonceHead">
            <span class="bounce-title">！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix tips">贵机构改为智能仓库模式，需缴纳年费</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 nextStep">下一步</button>
        </div>
    </div>
    <%---------------- 分工设置部分------------------%>
    <div class="bonceContainer bounce-blue" id="normalTip">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureSaveCoreUser()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue sureBtn" onclick="sureCompleteManager()">确定</span>
        </div>
    </div>


    <%--修改临时管理员 - 输入新的管理员--%>
    <div class="bonceContainer bounce-blue" id="changeTemporaryAdmin_input">
        <div class="bonceHead">
            <span class="bonceTitle">修改临时管理员</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: 0 auto">
                <div class="ty-alert">
                    请录入新临时管理员的姓名与手机号
                </div>
                <div class="page-content-avatar">
                    <div class="item">
                        <div class="item_title">姓名</div>
                        <div class="item_content">
                            <input type="text" class="kj-input" name="userName">
                        </div>
                    </div>
                    <div class="item">
                        <div class="item_title">手机</div>
                        <div class="item_content">
                            <input type="text" class="kj-input" name="mobile" onkeyup="clearNum(this)">
                            <div class="ty-color-blue">注：所录入号码需能接收短信验证码！</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.show($('#changeTemporaryAdmin_choose'))">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="inputTemporaryAdminBtn" onclick="inputTemporaryAdmin()">确定</button>
        </div>
    </div>

    <%--导入--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="background: #ddebf7;">
            <form action="../export/importUser.do" id="employeeImport" method="post"
                  enctype="multipart/form-data">
                <div class="exportStep">
                    <div class="stepItem">
                        <p>第一步：下载空白的“职工名单”。</p>
                        <div class="flexRow">
                            <span>职工名单</span>
                            <a href="../assets/oralResource/template/employee_blank_sheet.xls"
                               id="mould1" download="职工名单.xls" class="ty-btn ty-btn-blue">下 载</a>
                        </div>
                    </div>
                    <div class="stepItem">
                        第二步：在空白的“职工名单”中填写内容，并存至电脑。
                    </div>
                    <div class="stepItem">
                        <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                        <div class="flexRow">
                            <div class="upload_sect viewBtn">
                                <div id="sysUseUploadFile"></div>
                            </div>
                            <div class="fileFullName">尚未选择文件</div>
                        </div>
                    </div>
                    <div class="stepItem">
                        <p>第四步：点击“导入”。</p>
                        <div class="flexRow">
                            <span class="ty-btn ty-btn-yellow ty-btn-middle" onclick="bounce.cancel()">取 消</span>
                            <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="sysUseImportOk()">导 入</span>
                        </div>
                    </div>
                    <div class="importIntro stepItem">
                        <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                        <div style="text-align:left;">
                            <span>1、请勿增加、删除或修改所下载“职工档案”空白表的“列”，否则上传会失败。</span></br>
                            <span>2、在电脑上保存“职工名单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot" style="background: #ddebf7;">
        </div>
    </div>
    <%--修改职工信息--%>
    <div class="bonceContainer bounce-blue" id="updateUser">
        <div class="bonceHead">
            <span>修改职工信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="userForm">
                <div class="formItem">
                    <div class="left">姓名</div><input type="text" class="ty-inputText userName">
                    <i class="clearBtn" onclick="clearTxt($(this))">x</i>
                </div>
                <div class="formItem">
                    <div class="left">手机号</div><input type="text" onkeyup="clearNum(this)" class="ty-inputText userPhone">
                    <i class="clearBtn" onclick="clearTxt($(this))">x</i>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" disabled="disabled" id="importUpdateUser" onclick="updateUserInfo()">确定</button>
        </div>
    </div>
    <%--删除职工--%>
    <div class="bonceContainer bounce-red" id="delUser">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">确定删除所导入的这个职工吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-3" onclick="delUserSure()">确定</span>
        </div>
    </div>
    <%--放弃后，本次批量导入--%>
    <div class="bonceContainer bounce-red" id="clearUser">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>放弃后，本次批量导入的数据将消失不见。</p>
                <p>确定放弃吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="clearUser()">确定</span>
        </div>
    </div>
    <%--进入下一步--%>
    <div class="bonceContainer bounce-red" id="nextStep" style="padding: 0">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <div class="safeCondition">
                    <p>还有<span id="noSaveMbSum"></span>个手机号无法保存至系统。</p>
                    <p>进入下一步，这些号码将被舍弃。</p>
                </div>
                <p>确定进入下一步吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="sureStepNext()">确定</span>
        </div>
    </div>
    <%--保存--%>
    <div class="bonceContainer bounce-red" id="lastSave">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>您共导入职工<span id="saveSum"></span>条，可保存至系统的共<span id="saveAble"></span>条。</p>
                <p>确定保存吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-red ty-circle-3" onclick="lastSave()">确定</span>
        </div>
    </div>
    <%-- 上次的批量导入尚未完成。  --%>
    <div class="bonceContainer bounce-red" id="importNotCompleted">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody unfinishedForm">
                <p>上次的批量导入尚未完成。</p>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="1"></span>继续上次的操作
                </div>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="0"></span>放弃上次的操作，重新批量导入
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="judgeImport()">确定</span>
        </div>
    </div>
    <%-- 必须分配的权限。  --%>
    <div class="bonceContainer bounce-blue" id="haveToSetGeneralAuthorityBtn" style="width: 600px">
        <div class="bonceHead">
            <span>必须分配的权限</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-alert">
                <div>
                    <div>公司高层已开通总务与采购功能，这要求以下 <span class="num"></span> 项初始化权限必须分配。</div>
                    <small class="ty-color-blue">注：以下权限在初始化阶段仅可分配给一人，初始化后可按实际情况结合系统功能再调整。</small>
                </div>
            </div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>权限</td>
                    <td>已拥有此权限者</td>
                    <td>操作</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>采购的初始化</td>
                    <td>--</td>
                    <td><span class="link-blue">编辑</span></td>
                </tr>
                <tr>
                    <td>原辅材料库的初始化</td>
                    <td>--</td>
                    <td><span class="link-blue">编辑</span></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-circle-3 ty-btn-big" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%---------------- 常规权限部分------------------%>
    <%--总务权限分配--%>
    <div class="bonceContainer bounce-green" id="generalAuthority" style="width: 900px">
        <div class="bonceHead">
            <span>权限分配</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-secondTab">
                <li class="ty-active">仅能分配给一人的权限</li>
                <li>可分配给多人的权限</li>
            </div>
            <div class="tblContainer">
                <div class="ty-alert">
                    <div>
                        下列权限仅可分配给一人，请勾选需分配给 <span class="userName"></span> 的权限。
                        <div class="ty-color-red">下某权限如已分配给某职工，则再次勾选生效后，原职工将不再拥有此权限。</div>
                    </div>
                </div>
                <table class="ty-table ty-table-left">
                    <thead>
                    <tr>
                        <td style="width: 25%">模块</td>
                        <td style="width: 75%">
                            <table class="ty-table" frame="void">
                                <thead>
                                <tr>
                                    <td style="width: 30%">子模块</td>
                                    <td style="width: 30%">二级子模块</td>
                                    <td style="width: 40%">已拥有此权限者</td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="tblContainer" style="display: none">
                <div class="ty-alert">
                    请勾选需分配给 <span class="userName"></span> 的权限。
                </div>
                <table class="ty-table ty-table-left">
                    <thead>
                    <tr>
                        <td style="width: 25%">模块</td>
                        <td style="width: 75%">
                            <table class="ty-table" frame="void">
                                <thead>
                                <tr>
                                    <td style="width: 30%">子模块</td>
                                    <td style="width: 30%">二级子模块</td>
                                    <td style="width: 40%">已拥有此权限者</td>
                                </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="sureAllocateAuthority()" id="allocateAuthorityBtn">确定</span>
        </div>
    </div>
    <%---------------- 审批设置部分------------------%>
    <%-- 审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="itemApply" >
        <div class="bonceHead">
            <span>权限变更 - 审批设置修改</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix120">审批级别</div>
                    <div class="item-auto">
                        <select name="level" onchange="changeAuth()">
                            <option value="1">一级审批</option>
                            <option value="2">二级审批</option>
                        </select>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">一级审批人</div>
                    <div class="item-auto">
                        <select name="firstApprover"></select>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">最终审批人</div>
                    <div class="item-auto">
                        <select name="lastApprover"></select>
                    </div>
                </div>
            </div>
            <%--            <div>--%>
            <%--                <div class="approvalLevel">--%>
            <%--                    <span class="ttl">审批级别</span>--%>
            <%--                    <span class="con" id="authLevelCon">--%>
            <%--                        <select class="conInput level" id="authLevel"  >--%>
            <%--                            <option value="1">一级审批</option>--%>
            <%--                            <option value="2">二级审批</option>--%>
            <%--                        </select>--%>
            <%--                    </span>--%>
            <%--                </div>--%>
            <%--                <div class="approval" id="authLevelContent">--%>

            <%--                </div>--%>
            <%--            </div>--%>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="saveAuthcharge()">保存</span>
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%-- 付款设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="paymentSet" >
        <div class="bonceHead">
            <span id="paymentTtl">付款设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <%--<div class="">
                <p class="leTip"> 报销或借款等与资金支出有关申请审批过后、<br>出纳实际付款前需再经付款审批。</p>
                <p>您可更换付款审批的审批人。</p>
                <p>付款审批的审批人：
                    <span id="scanU">董事长139XXXXXXXX</span>
                    <input type="hidden" id="paymentID">
                    <span class="ty-right applyTip payEditBtn" id="payEditBtn"  onclick="payEditBtn()">修改</span>
                </p>
                <div id="startDate2" >
                    本修改将对 <input type="text"/>之后提交的付款申请生效
                </div>
            </div>--%>
            <div>
                <p style="line-height: 30px;">
                    付款审批的设置
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" onclick="updatePaySet()">修改付款设置</span>
                </p>
                <div style="padding-left:30px; ">
                    <div class="tiph">
                        <span>内容：</span>
                        <div>与资金支出有关的申请（如报销申请、借款申请）审批通过后、出纳实际付款前的关于是否付款专门的审批</div>
                    </div>
                    <div class="tiph">
                        <span>功能：</span>
                        <div>系统默认需由最高管理者审批。可更换为其他人或“无需审批”</div>
                    </div>
                    <div>
                        当前付款审批的设置：<span id="cur1"></span>
                    </div>
                </div>
                <hr style=" margin-top: 10px; border-top: 1px solid #ccc; margin-bottom: 8px;">
                <p>
                    付款复核的设置
                </p>
                <div style="padding-left:30px; ">
                    <div class="tiph">
                        <span>内容：</span>
                        <div>出纳选择为现金外的对外支付方式时，是否需要财务负责人复核</div>
                    </div>
                    <div class="tiph">
                        <span>功能：</span>
                        <div>系统默认需由财务负责人复核。可改为“无需审批”</div>
                    </div>
                    <div>
                        当前付款复核的设置：<span id="cur2"></span>
                    </div>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <%--<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="savePaycharge"  onclick="applySubmit()">提交修改申请</span>--%>
            <span class="ty-btn ty-btn-big ty-circle-3" id="savePaychargeNo" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- 采购审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="purchaseApprovalSettings" >
        <div class="bonceHead">
            <span class="bounce-title">采购审批设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">目前，本公司采购需 <span class="level"></span> 级审批</div>
                    <div class="item-auto text-right">
                        <button class="link-blue btn_changeNoApprove" onclick="changeApprove(0)">修改至无需审批</button>
                        <button class="link-blue" onclick="changeApprove(1)">其他修改</button>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">开始执行的时间</div>
                    <div class="item-auto text-right openDate"></div>
                </div>
                <div class="kj-hr"></div>
                <div class="approvalFlows"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- 修改付款设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="paymentSetUpdate" >
        <div class="bonceHead">
            <span>修改付款设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>
                    付款审批的审批者既可更换为其他职工，也可选择为“无需审批”
                </p>
                <div style="padding-left: 50px; ">
                    <span>付款审批者：</span>
                    <div class="selectUcon">
                        <input type="text" id="userInput">
                        <div id="selectU" class="selectU"></div>
                    </div>
                    <%--<select class="form-item" id="selectU" onchange="showtime()"></select>--%>
                </div>
                <hr style="margin-top:10px; margin-bottom:10px; border-top:1px solid #ccc; ">
                <p>
                    出纳选择为现金外的对外支付方式时，是否需要财务负责人复核？
                </p>
                <div style="padding: 0 44px;" class="sp">
                    <p><i data-val="1" class="fa fa-circle-o"></i> 需要</p>
                    <p><i data-val="0" class="fa fa-circle-o"></i> 不需要</p>
                </div>
                <div class="times">
                    本修改将对 <input id="timesInput" class="form-item" /> 之后提交的付款申请生效
                </div>
            </div>
        </div>
        <div class="bonceFoot submitcon">
            <span class="ty-btn ty-cancel ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="submitcon()">提交</span>
        </div>
    </div>
    <%--  加班修改--%>
    <div class="bonceContainer bounce-blue " id="overTimeEdit">
        <div class="bonceHead">
            <span>修改加班审批的审批设置</span>
            <a class="bounce_close" onclick=" endTimer();bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="applyTip">
                <div>说明：</div>
                <p>1  设置多级审批时，可选择“直接上级”作为审批者，但一旦选择特定职工作为审批者，则之后的各级审批者将无法再选择“直接上级”。</p>
                <p>2  审批者最高可审批不超过24小时的加班。</p>
                <input type="hidden" id="flowData">
            </div>
            <div id="flowList">
                <div class="apItem">
                    <p>
                        <span class="levelName">审批者</span>
                        <span class="chargeName">董事长</span>
                    <div class="clr"></div>
                    </p>
                    <p>
                        该审批者有权批准不高于<select class="chargeHours"><option value="24">24</option></select>小时的加班
                    </p>
                </div>
            </div>
            <div id="nextStep">
                <p onclick="toggleFa2($(this), 1)"><span><i class="fa fa-circle-o"></i> 设置下一级</span></p>
                <p onclick="toggleFa2($(this), 2)"><span><i class="fa fa-circle-o"></i> 不设置下一级，已设置完毕。</span></p>
            </div>
            <div id="startDate" >
                本修改将对 <input type="text" readonly>之后提交的加班申请生效
            </div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <div class="btns">
                <%--<span class="ty-btn bounce-cancel ty-btn-big ty-circle-3 " id="editCancel" onclick="bounce.cancel()"  >取消</span>--%>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="editOverTimeOk" onclick="editOverTimeOk()"  >确定</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="applySubmit" onclick="applySubmit()"  >提交修改申请</span>
            </div>

        </div>
    </div>
    <%--  请假修改--%>
    <div class="bonceContainer bounce-blue " id="leaveEdit">
        <div class="bonceHead">
            <span>修改请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>新设置的请假审批流程如下：</div>
            <div id="leaveFlowList" data-hours="0">
            </div>
            <p class="leaveFlowTip">职工无法提交超过<span id="leaveFlowDur"></span>的请假申请。</p>
            <div>
                <div id="addApprover">
                    <div class="itemLe">
                        <span class="levelName">审批者</span>
                        <span class="chargeName itemSp" onclick="chooseTip($(this), 'leave')"></span>
                        <div class="clear"></div>
                    </div>
                    <div class="applyTip">
                        <div>说明：</div>
                        <p>设置多级审批时，可选择“直接上级”作为审批者，但一旦选择特定职工作为审批者，则之后的各级审批者将无法再选择“直接上级”。</p>
                    </div>
                    <p class="itemLe">该审批者有权批准</p>
                    <div class="limitReq">
                        <p class="leaveHour">
                            <span class="higher">当日不高于</span>
                            <select id="leaveHour" data-type="1" onchange="leaveTimeSt($(this))" require>
                                <option value=""></option>
                                <option value="0.5">0.5</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="4">4</option>
                                <option value="8">8</option>
                                <option value="12">12</option>
                            </select>小时的请假
                        </p>
                        <p>
                            <span class="higher">不高于</span><input id="leaveDays" type="text" data-type="2" onkeyup="leaveTimeSt($(this))" require/>天的请假
                        </p>
                        <div class="wishDay">
                            <span class="fa fa-circle-o" data-type="3" onclick="leaveTimeSt($(this))" require></span>
                            <span class="wish">任何天数的请假</span>
                        </div>
                    </div>
                </div>
                <div>
                    <p>本修改对哪天及之后提交的请假申请生效？</p>
                    <div class="itemLe">
                        <span>请确定本修改的生效日期</span>
                        <input id="leaveStartDate" />
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" id="leaveNext" onclick="nextStep()">下一步</span>
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3" id="leaveApplyCancel" onclick="bounce_Fixed2.show($('#cancelTip'))">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="leaveApplySubmit" onclick="applySubmit()">提交修改申请</span>
        </div>
    </div>
    <%-- 报销修改 --%>
    <div class="bonceContainer bounce-blue " id="finananceEdit">
        <div class="bonceHead">
            <span>查看报销审批的审批设置</span>
            <a class="bounce_close" onclick="endTimer();bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>目前本公司报销的审批流程如下：
                    <span class="ty-right applyTip overTimeEdit" onclick="finananceEditBtn()">修改</span>
                </p>
                <div id="flowList3"> </div>
                <div class="applyTip" style="margin-left:65px;"> </div>
            </div>

            <div id="editFlows">
                <div>
                    <p class="">新设置的报销审批流程如下：</p>
                    <div id="flowList4"> </div>
                    <p class="apItem editShow">
                        <span class="levelName">审批者</span><span data-mobile="" data-id="" id="finPerson" class="chargeName" onclick="chooseTip($(this) , 'finace')"> -- 请选择 -- </span>
                    </p>
                    <div class="applyTip editShow">
                        <div>说明：</div>
                        <p>设置多级审批时，可选择“直接上级”作为审批者，但一旦选择特定职工作为审批者，则之后的各级审批者将无法再选择“直接上级”。</p>
                    </div>
                    <p class="apItem editShow" style="margin-bottom:5px; "> 该审批者有权批准 </p>
                    <p class="apItem editShow">不高于 <input type="text" id="amountInput" onkeyup="clearNoNum(this)"> 元的报销</p>
                    <p class="apItem editShow" style="margin-top:5px;" data-anyAmount="0" id="anyAmount" onclick="toggleThis($(this))"><i class="fa fa-circle-o"></i>  任何金额的报销</p>
                </div>
                <div id="startDate3" >
                    <div class="applyTip" id="endFinTip"> 。</div>
                    <p style="margin-top:30px; ">本修改对哪天及之后提交的报销申请生效？</p>
                    请确定本修改的生效日期 <input type="text" id="finStartDate" readonly>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <div class="">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="nextFin" onclick="nextFin()"  >下一步</span>
                <span class="ty-btn bounce-cancel ty-btn-big ty-circle-3 " id="cacelFin" onclick="cacelFin()"  >取消</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="closeFin2" onclick="bounce.cancel()"  >关闭</span>
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3 " id="applyFin" onclick="applySubmit()"  >提交修改申请</span>
            </div>

        </div>
    </div>
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue " id="requestLog">
        <div class="bonceHead">
            <span id="logType">修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p id="summary"> </p>
            <table class="kj-table" id="log">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td id="log2">状态</td>
                    <td>开始执行日期</td>
                    <td id="log1">操作</td>
                    <td id="log13">付款复核者</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()"  >关闭</span>
        </div>
    </div>
    <%-- 仓库修改记录 --%>
    <div class="bonceContainer bounce-blue" id="stockModeLog" style="width: 700px">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="text-right"><span class="link-blue" onclick="stockModeDes()">仓库模式的说明</span></p>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td>设置的结果</td>
                    <td>开始执行日期</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  来自客户订单的评审 --%>
    <div class="bonceContainer bounce-blue" id="sale_common" style="width: 520px">
        <div class="bonceHead">
            <span>来自客户订单的评审</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 420px; margin: 0 auto">
                <div class="text-right">
                    <button class="ty-btn ty-btn-blue ty-btn-big" data-fun="saleApprove">修改设置</button>
                </div>
                <div style="margin-top: 8px">
                    <p>内容：<span id="msgLog">对于客户发来订单中的数量与交期，是否需公司各部门评审</span></p>
                    <p>功能：<span id="funLog">系统默认需要评审。可修改为“无需评审”</span></p>
                    <p>当前的设置：<span id="currentSettings"></span></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big fatSize" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--  产品基本信息的创建模式 --%>
    <div class="bonceContainer bounce-blue" id="productCreationMode" style="width: 720px">
        <div class="bonceHead">
            <span>产品基本信息的创建模式</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 600px; margin: 0 auto">
                <div class="text-right">
                    <button class="ty-btn ty-btn-blue ty-btn-big" data-fun="productSetting">修 改</button>
                </div>
                <div class="yard_item">
                    <p>内容</p>
                    <div>1 系统中，产品基本信息包含图号、名称、规格、型号、计量单位等内容</div>
                    <div>2 某些机构产品的图号与名称，内部采用一套体系，销售给客户时采用另一套体系，有些机构则内外部完全相同</div>
                    <div>3 本系统提供的模式1与模式2，分别适用于上述两种情况</div>
                    <div>模式1为手动关联模式，即商品与产品需各自创建并维护，二者间需逐一关联</div>
                    <div>模式2为自动关联模式，即商品创建后，产品由系统生成，其基本信息与商品完全相同，修改时为联动修改</div>
                </div>
                <div class="yard_item">
                    <p>功能</p>
                    <div>1、系统提供默认的设置，机构可根据自身情况给予修改</div>
                    <div>2、默认的设置为：客户专属的商品与产品间为模式1，通用型商品与产品间为模式2</div>
                </div>
                <div class="yard_item">
                    <p>当前产品图号与名称的创建模式</p>
                    <div>与专属商品相关的产品为模式<span id="zsModel"></span>，与通用型商品相关的产品为模式<span id="tyModel"></span></div>
                    <div>产品信息<span id="reviseModel"></span></div>
                    <div class="currentMode hd"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big fatSize" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%-- 仓库的模式--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="stockMode"  style="width: 600px">
        <div class="bonceHead">
            <span class="bounce-title">仓库的模式</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">当前的仓库模式：<span class="nowModeName"></span></div>
                    <div class="item-auto text-right">
                        <button class="link-blue changeBtn">修改</button>
                        <button class="link-blue" onclick="stockModeDes()">仓库模式的说明</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%---------------- 分工设置部分------------------%>
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--新增投诉立案者--%>
    <div class="bonceContainer bounce-blue" id="newDivision" style="width: 500px">
        <div class="bonceHead">
            <span>请选择负责人</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon clearfix" style="height: 300px;overflow: auto">
            <div class="ty-colFileTree"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="sureNewDivisionBtn" onclick="confirmSaveCoreUser()">确定</button>
        </div>
    </div>
    <%--负责人修改记录--%>
    <div class="bonceContainer bounce-blue" id="coreChangeHistory" style="width: 800px">
        <div class="bonceHead">
            <span><span class="module"></span>负责人修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon clearfix" style="height: 300px;overflow: auto">
            <h4 class="userRole ty-color-green"></h4>
            <div class="ty-alert ty-alert-info" style="justify-content: space-between"><span class="isChange"></span><span class="createInfo"></span></div>
            <table class="ty-table ty-table-control changeHistory">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td><span class="module"></span>负责人</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>权限</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container" id="home">
                <div class="ty-page-header" style="display: none">
                    <div class="page-header__left" onclick="back()">
                        <i class="iconfont icon-houtui icon-back"></i>
                        <span>返回</span>
                    </div>
                    <div class="page-header__content" onclick="backToMain()">
                        <i class="iconfont icon-zhuye icon-back"></i>
                        <span>主页</span>
                    </div>
                </div>
                <div class="page" page="main">
                    <div class="panel-box">
                        <div class="panel-box-title"></div>
                        <div class="panel-box_item">
                            <div class="left_item" style="width: 560px">请根据实际情况与需要，在本页面设置初始的权限！</div>
                            <div class="right_item">
                                <div class="ty-radio">
                                    <input type="radio" name="" id="initCompleted">
                                    <label for="initCompleted"></label> 能设置的权限都已设置，初始化完成！
                                </div>
                                <button class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="initCompleteBtn()">确定</button>
                            </div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="panel-box-title">常规的权限</div>
                        <div class="panel-box_item">
                            <div class="left_item">
                                当前机构模式要求必须设置的常规权限：共 <b class="totalItem"></b> 项，已设置 <b class="setItem"></b> 项
                                <span class="link-blue" onclick="haveToSetGeneralAuthorityBtn()">去设置</span>
                            </div>
                            <div class="right_item">更多可使用的功能（常规权限） <span class="link-blue" type="btn" name="set" to="generalPower">去设置</span></div>
                        </div>
                        <div class="panel-box_item">
                            <div class="left_item">系统启用时，有些常规的权限就已分配给了某些职工。</div>
                            <div class="right_item">已分配给职工的常规权限 <span class="link-blue" type="btn" name="see" to="assignedPower">查看</span></div>
                        </div>
                        <div class="panel-box_item">
                            <div class="left_item"></div>
                            <div class="right_item">尚未分配给职工的常规权限 <span class="link-blue" type="btn" name="see" to="unassignedPower">查看</span></div>
                        </div>
                        <div class="panel-box_item">
                            <div class="left_item"></div>
                            <div class="right_item">职工们已有哪些常规的权限？ <span class="link-blue" type="btn" name="see" to="workerAuthorityPower">查看</span></div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="panel-box-title">审批权限</div>
                        <div class="panel-box_item">
                            系统支持对一些事务的审批，并带有默认的审批流程。请查看，不符合公司实际情况的，请重新设置。<div class="right_btn_item"><span class="link-blue" type="btn" name="set" to="approvePower">审批权限设置</span></div>
                        </div>
                        <div class="panel-box_item">
                            <div class="left_item"></div>
                            <div class="right_item">现在，哪些职工能进行哪些审批？ <span class="link-blue" type="btn" name="see" to="approvePower">查看</span></div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="panel-box-title">其他权限</div>
                        <div class="panel-box_item">
                            投诉、车务、领料等事务需先设置专项负责人，之后由该负责人继续操作。对负责人的管理，需点击右侧的“其他权限设置”！<div class="right_btn_item"><span class="link-blue" type="btn" name="set" to="otherPower">其他权限设置</span></div>
                        </div>
                    </div>
                    <div class="panel-box">
                        <div class="panel-box-title">职工导入</div>
                        <div class="panel-box_item">
                            操作过程中如发现尚未导入至系统的职工，请点击右侧的“批量导入其他职工”，之后按页面流程操作！<div class="right_btn_item"><span class="link-blue" type="btn" name="import" to="employees">批量导入其他职工</span></div>
                        </div>
                    </div>
                </div>
                <%-- 更多可使用的功能（常规权限）(来源 权限管理 - 权限设置)--%>
                <div class="page" page="generalPower_set">
                    <table class="kj-table kj-table-striped tbl_generalPower_set">
                        <thead>
                        <tr>
                            <td>姓名</td>
                            <td>性别</td>
                            <td>手机号</td>
                            <td>部门</td>
                            <td>职位</td>
                            <td>直接上级</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <%-- 已分配给职工的常规权限(来源 权限管理 - 当前权限 - 已分配权限)--%>
                <div class="page" page="assignedPower_see">
                    <div class="tblContainer">
                        <div class="ty-table" id="assigned">
                            <ul class="ty-head">
                                <li style="width: 10%">模块</li>
                                <li style="width: 18%">子模块</li>
                                <li style="width: 14.4%">二级子模块</li>
                                <li style="width: 36%">功能描述</li>
                                <li style="width: 21.6%">已拥有此权限者</li>
                            </ul>
                            <div class="ty-body"></div>
                        </div>
                        <div class="importantTip" style="display: none">
                            <h4>重要提示!!</h4>
                            <p>您把公司最高销售、财务与总务管理人员手机号码录入到高管管理中，才能更好地使用本系统。</p>
                        </div>
                    </div>
                </div>
                <%-- 尚未分配给职工的常规权限(来源 权限管理 - 当前权限 - 未分配权限)--%>
                <div class="page" page="unassignedPower_see">
                    <div class="tblContainer" id="unassigned">
                        <div class="ty-table">
                            <ul class="ty-head">
                                <li style="width: 10%">模块</li>
                                <li style="width: 18%">子模块</li>
                                <li style="width: 72%">功能描述</li>
                            </ul>
                            <div class="ty-body"></div>
                        </div>
                    </div>
                </div>
                <%-- 职工们已有哪些常规的权限？(来源 权限管理 - 职工权限 - 当前权限查看)--%>
                <div class="page" page="workerAuthorityPower_see" style="max-width: inherit">
                    <table class="ty-table" style="width:200px" id="auth_1_1">
                        <tbody id="authList_1_1">
                        <tr>
                            <td>部门</td>
                            <td>姓名</td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="scrollTable"  id="auth_1_2">
                        <table class="ty-table ty-table-control" id="auth_1_21" style="width: 2000px;">
                            <tbody id="authList_1_2">
                            <tr>
                                <td colspan="11" id="colspan">项目</td>
                            </tr>
                            <tr id="modelTTl_2">
                                <td>权限管理</td>
                                <td>总务管理</td>
                                <td>财务管理</td>
                                <td>销售管理</td>
                                <td>生产管理</td>
                                <td>商品管理</td>
                                <td>物料管理</td>
                                <td>项目管理</td>
                                <td>文件与资料</td>
                                <td>个人中心</td>
                                <td>关于</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="clr"></div>
                </div>
                <%-- 审批权限设置(来源 权限管理 - 审批设置)--%>
                <div class="page" page="approvePower_set">
                    <div class="tblContainer">
                        <table class="kj-table kj-table-striped">
                            <thead>
                            <tr>
                                <td rowspan="2"> 审批事项 </td>
                                <td rowspan="2"> 当前状态 </td>
                                <td colspan="3"> 当前审批者 </td>
                                <td rowspan="2"> 操作 </td>
                            </tr>
                            <tr>
                                <td>一级审批者</td>
                                <td>二级审批者</td>
                                <td>最终审批者</td>
                            </tr>
                            </thead>
                            <tbody id="chargeList"></tbody>
                        </table>
                    </div>
                </div>
                <%-- 现在，哪些职工能进行哪些审批？(来源 权限管理 - 职工权限 - 审批设置查看)--%>
                <div class="page" page="approvePower_see" style="max-width: inherit">
                    <div class="tblContainer">
                        <table class="ty-table ty-table-control">
                            <tbody id="authList_2">
                            <tr>
                                <td>部门</td>
                                <td id="biao">
                                    <div class="biaotou"></div>
                                    <span class="tt1">项目</span>
                                    <span class="tt2">姓名</span>
                                </td>
                                <td>加班申请</td>
                                <td>请假申请</td>
                                <td>报销申请</td>
                                <td>职工档案修改</td>
                                <td>岗位设置修改</td>
                                <td>审批设置修改</td>
                                <td>新项目立项</td>
                                <td>新项目开发</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <%-- 其他权限设置(来源 总务管理 - 分工设置)--%>
                <div class="page" page="otherPower_set" style="max-width: none">
                    <div class="ty-mainData">
                        <p class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>您在此可确定或修改如下事项的负责人：</p>
                        <div class="importIntro core">
                            <div class="handleTip">
                                <div class="bg">投</div>
                                <h3>投诉管理</h3>
                                <p>投诉管理负责人全权负责投诉事项，将获得如下操作权限：</p>
                                <p>1 . 在系统内添加可录入投诉信息的职工</p>
                                <p>2 . 决定某条新录入的投诉是否立案</p>
                                <p>3 . 指定某条投诉的处理负责人</p>
                                <p>4 . 审批某条投诉的结案申请</p>
                                <p>5 . 确定何人可以查看投诉清单</p>
                            </div>
                            <p class="ty-alert ty-alert-info" style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(0)">分配或修改负责人</button>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(1)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro projectCore">
                            <div class="handleTip">
                                <div class="bg">项</div>
                                <h3>项目管理</h3>
                                <p>项目管理负责人全权负责项目管理，将获得如下操作权限：</p>
                                <p>1 . 在系统内添加可对项目管理立项的职工</p>
                                <p>2 . 决定某条新录入的项目管理项目是否立案</p>
                                <p>3 . 审批某项目管理项目的结案申请</p>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(1)">分配或修改负责人</button>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(5)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro improvementCore">
                            <div class="handleTip">
                                <div class="bg">持</div>
                                <h3>持续改进管理</h3>
                                <p>持续改进管理负责人全权负责持续改进，将获得如下操作权限：</p>
                                <p>1 . 在系统内添加可对持续改进立项的职工</p>
                                <p>2 . 决定某条新录入的持续改进项目是否立案</p>
                                <p>3 . 审批某持续改进项目的结案申请</p>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(2)">分配或修改负责人</button>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(7)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro vehicleCore">
                            <div class="handleTip">
                                <h3>车务管理</h3>
                                <div class="bg">车</div>
                                <p>系统内，车务管理负责人将获如下操作权限：</p>
                                <p>1 . 车辆管理：新增或停用车辆、修改车辆信息等</p>
                                <p>2 . 司机管理</p>
                                <p>3 . 向用车申请者派车。</p>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(3)">分配或修改负责人</button>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(9)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro pickingCore">
                            <div class="handleTip">
                                <div class="bg">领</div>
                                <h3>领料分工</h3>
                                <p>系统内，领料分工负责人将负责：</p>
                                <p>1 . 选择领料者；</p>
                                <p>2 . 所选领料者所能领用的材料。</p>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(4)">分配或修改负责人</button>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(11)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro buySalesCore">
                            <div class="handleTip ">
                                <div class="bg">购</div>
                                <h3>购销统筹</h3>
                                <div class="planIndent">
                                    <div style="margin-bottom: 20px;">客户订单评审完后，购销统筹负责人将收到相关消息。</div>
                                    <div>系统内，购销统筹负责人负责：</div>
                                    <div>确定所需采购材料最少需购买多少，及最迟不得晚于何时到货。</div>
                                </div>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changePeopleInChargeBtn(5)">分配或修改负责人</button>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(13)">修改记录</button>
                            </p>
                        </div>
                    </div>
                </div>
                <%-- 批量导入其他职工(来源 总务管理 - 职工档案)--%>
                <div class="page" page="employees_import">
                    <div class="tblContainer tbl_employeesList">
                        <div class="ty-alert" align="right">
                            <div class="btn-group">
                                <button class="ty-btn ty-btn-big ty-btn-cyan ty-circle-3" onclick="leadingStart()">批量导入</button>
                                <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" disabled>办理入职</button>
                            </div>
                            ${success}
                            ${error}
                        </div>
                        <%--在职列表--%>
                        <table class="kj-table kj-table-striped">
                            <thead>
                            <tr>
                                <td>姓名</td>
                                <td>性别</td>
                                <td>手机号</td>
                                <td class="belongOrg">所属的机构</td>
                                <td>部门</td>
                                <td>职位</td>
                                <td>直接上级</td>
                                <td>最高学历</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_employee"></div>
                    </div>
                </div>
                <div class="page" page="importInfo" id="importEntryType">
                    <div class="importCon1">
                        <div class="topRightBtn importNoSave stepItem">
                            <button class="ty-btn ty-btn-orange ty-btn-big" style="margin-right: 16px;" onclick="clearNoSave()">放弃</button>
                            <button class="ty-btn ty-btn-blue ty-btn-big" onclick="stepNext()">下一步</button>
                        </div>
                        <p>您共导入职工<span class="initAll"></span>条，其中以下<span class="initWrong"></span>条存在问题，<span class="ty-color-red"> 无法保存至系统</span>。</p><br>
                        <p>姓名或手机号未录入、手机号错误或与系统中已有号码相同等，均算作问题。</p><br>
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="importCon2">
                        <div class="topRightBtn importing stepItem">
                            <button class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="cancelSave()" style="margin-right: 16px;">放弃</button>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="ok">保存</button>
                        </div>
                        <p>您共导入职工<span class="importSum"></span>条，可保存至系统的共<span class="saveSum"></span>条。</p>
                        <p>1、为每位职工选定“工作特点”后，才可保存至系统。</p>
                        <p>2、Wonderss内请假、加班、报销等均与“直接上级”有关，故建议按实际情况给予选定。</p>
                        <p class="exportStep">找到<span class="ty-color-red">最高领导的直接下属</span>后，将其“直接上级”选择为最高领导，之后逐级选择的操作方式较易理解。</p><br>
                        <table class="kj-table">
                            <thead>
                                <tr>
                                    <td>姓名</td>
                                    <td>手机号</td>
                                    <td>是否有下属</td>
                                    <td>直接上级</td>
                                    <td>工作特点</td>
                                    <td>操作</td>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/orgInit/initCommon.js?v=SVN_REVISION"></script>
<script src="../script/general/divisionSetting.js?v=SVN_REVISION"></script>
<script src="../script/authority/approveSettings.js?v=SVN_REVISION"></script>
<script src="../script/orgInit/authority.js?v=SVN_REVISION"></script>
</body>
</html>