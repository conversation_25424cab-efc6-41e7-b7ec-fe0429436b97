<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%--供应商部分引用--%>
<link href="../css/purchase/materialManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/supplierproduct/suppliecontract.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/sales/sale.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%--<link href="../css/system/manages.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />--%>
<link rel="stylesheet" type="text/css" href="../css/event/icon/iconfont.css" />
<link rel="stylesheet" type="text/css" href="../css/orgInit/initCommon.css" />
<link rel="stylesheet" type="text/css" href="../css/orgInit/purchase.css" />
<style>
    .item-default{
        flex: auto;
    }
</style>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg text-center"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</button>
        </div>
    </div>
    <%-- 编辑分类名称 --%>
    <div class="bonceContainer bounce-blue" id="editCat">
        <div class="bonceHead">
            <span>编辑分类名称</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="text-center">
                    <p>请输入修改后的分类名称</p>
                    <input type="text" id="kindEditName" placeholder="请输入修改后的分类名称" >
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <%-- 删除分类 --%>
    <div class="bonceContainer bounce-red" id="delCatTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align:center;">
            确定删除该分类？
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3 bounce-cancel" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="okDeleteKind()">确定</span>
        </div>
    </div>
    <%-- 删除材料  --%>
    <div class="bonceContainer bounce-red" id="mtDel">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">确定删除此材料？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="mtScanDel('mtDel', 1) ">确定</span>
        </div>
    </div>
    <%-- 新增材料类别  --%>
    <div class="bonceContainer bounce-green" id="addCat">
        <div class="bonceHead">
            <span>新增材料类别</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <input type="hidden" id="addkind_pid">
            <div class="ty-alert">新增的类别将隶属于：</div>
            <div class="text-center"><span style="display: inline-block; width: 300px; text-align: left"><b class="ty-color-blue" id="catParents"></b></span></div>
            <div class="kj-hr"></div>
            <div class="ty-alert">材料类别的名称</div>
            <div class="text-center"><input type="text" placeholder=" 请录入" id="newCatName" style="width: 300px"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="addCatOk()">确定</span>
        </div>
    </div>
    <%-- 材料录入 --%>
    <div class="bonceContainer bounce-blue" id="editRAAMt" style="width: 700px">
        <div class="bonceHead">
            <span class="bounce_title">材料录入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="form_editRAAMt">
                    <div class="normal">
                        <div class="row">
                            <div class="col-md-12">
                                您正在录入"其他原辅材料"。
                                <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="importBtn" onclick="leadingShow()">批量导入</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="item-title">材料名称 <span class="red">*</span></div>
                                <input name="name" type="text" require need/>
                            </div>
                            <div class="col-md-6">
                                <div class="item-title">材料代号 <span class="red">*</span></div>
                                <input name="code" type="text" require need/>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="item-title">规格</div>
                                <input name="specifications" require  type="text" />
                            </div>
                            <div class="col-md-6">
                                <div class="item-title">型号</div>
                                <input name="model" require  type="text" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div>
                                    <span class="item-title">计量单位 <span class="red">*</span></span>
                                    <span onclick="addUnit()" class="btnCat ty-right" id="unitSelectBtn"> 新增</span>
                                </div>
                                <select type="text" id="unitSelect" name="unitId" require need></select>
                            </div>
                            <div class="col-md-6" id="cats">
                                <div>
                                    <span class="item-title">材料类别 <span class="red">*</span></span>
                                    <span class="btnCat ty-right addCat" id="catSelectBtn"> 新增</span>
                                </div>
                                <div class="category_avatar categorySelectList">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="intelligentWh"> <%--智能库--%>
                        <div class="row">
                            <div class="col-md-6">
                                本材料是否有保质期方面的要求？ <span class="ty-color-red">*</span>
                            </div>
                            <div class="col-md-6">
                                <div class="ty-radio" style="width: 140px">
                                    <input type="radio" id="haveShelfLife" name="expRequired" value="1" onclick="changeExpRequired($(this))">
                                    <label for="haveShelfLife"></label> 有
                                </div>
                                <div class="ty-radio">
                                    <input type="radio" id="haveNotShelfLife" name="expRequired" value="0" onclick="changeExpRequired($(this))">
                                    <label for="haveNotShelfLife"></label> 没有
                                </div>
                            </div>
                        </div>
                        <div class="partHasExpRequired">
                            <div class="row">
                                <div class="col-md-6">
                                    （开封）开瓶后可使用几日？
                                </div>
                                <div class="col-md-6">
                                    <input placeholder="请录入" type="text" onkeyup="clearNum(this); countWords($(this),13)" name="openDuration" />
                                    <div class="textMax text-right" max="13"></div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    入库时，与保质期有关的数据需录入哪项？ <span class="ty-color-red">*</span>
                                </div>
                                <div class="col-md-6">
                                    <div class="ty-radio" style="width: 140px">
                                        <input type="radio" id="deadLine" name="relatedItem" value="1" onclick="changeRelatedItem($(this))">
                                        <label for="deadLine"></label>可使用的截至日期
                                    </div>
                                    <div class="ty-radio">
                                        <input type="radio" id="birthDate" name="relatedItem" value="2" onclick="changeRelatedItem($(this))">
                                        <label for="birthDate"></label> 生产日期
                                    </div>
                                </div>
                            </div>
                            <div class="part_birthDate">
                                <div class="row">
                                    <div class="col-md-6">
                                        不同供应商供应的本材料保质期是否相同？ <span class="ty-color-red">*</span>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="ty-radio" style="width: 140px">
                                            <input type="radio" id="haveSame" name="sameExpiration" value="1" onclick="changeSameExpiration($(this))">
                                            <label for="haveSame"></label> 相同
                                        </div>
                                        <div class="ty-radio">
                                            <input type="radio" id="haveNotSame" name="sameExpiration" value="0" onclick="changeSameExpiration($(this))">
                                            <label for="haveNotSame"></label> 不同
                                        </div>
                                    </div>
                                </div>
                                <div class="part_saveExpiration">
                                    <div class="row">
                                        <div class="col-md-6">
                                            本材料的保质期为自生产日期之后多久？ <span class="ty-color-red">*</span>
                                        </div>
                                        <div class="col-md-6">
                                            <input placeholder="请录入数字" type="text" name="expirationDays" id="expirationDays" onkeyup="clearNum(this);"  style="width: 260px"/> 天
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="normal">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="item-title">备注</div>
                                <input name="memo" type="text" require onkeyup="countWords($(this),100)" />
                                <div class="textMax text-right" max="100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="mtEditOk() ">确定</span>
        </div>
    </div>
    <%-- 材料查看 --%>
    <div class="bonceContainer bounce-blue" id="mtScan">
        <div class="bonceHead">
            <span>材料查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <div class="col-md-6">
                    <span class="scanMt_state">本材料录入时为“曾采购过的材料”。</span>
                </div>
                <div class="col-md-3"><span class="btnCat" id="editMtBtn" onclick="editMtBtn()">修改材料的基本信息</span></div>
                <div class="col-md-3" style="text-align: right"><span class="disabled btnCat">材料基本信息的修改记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-2">&nbsp; </div>
                <div class="col-md-6"><span class="item-title">创建人</span><span class="scanMt_create">张三丰给 2015-15-15 14：12：12</span></div>
                <div class="col-md-4" style="text-align: right"><span class="disabled btnCat ">材料暂停/恢复采购的操作记录</span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">材料名称</span><span class="scanMt_name">sdsdsfsdsdf</span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span class="scanMt_code">sdsdsfsdsdf</span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span class="scanMt_unit">sdsdsfsdsdf</span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">规格</span><span class="scanMt_specifications">sdskdsds</span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span class="scanMt_model">sdskdsds</span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="scanMt_expStr"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">备注</span><span class="scanMt_memo">sdfsadsa</span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">材料类别</span>
                    <span class="scanMt_cat">sdsds>sdsds>sdsds</span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>
    <%--操作说明--%>
    <div class="bonceContainer bounce-blue" id="des_inputMt" style="width: 600px">
        <div class="bonceHead">
            <span>操作说明</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 500px; margin: 8px auto">
                <p>1 采购只能录入“构成商品的原辅材料”、“商品的包装物”与“外购成品”以外的“其他原辅材料”，在于这三类材料与商品相关，系由产品拆分生成。</p>
                <p>2 请按实际情况录入”其他原辅材料“，不录入原辅材料，系统的初始化工作无法完成！</p>
                <p>3 采购初始化之后，为仓库的初始化，库管员在该步骤库里需录入系统内原辅材料的当前库存。</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-blue ty-btn-big" onclick="bounce.cancel()">知道了</span>
        </div>
    </div>
<%--    定点信息部分--%>
    <%--  添加材料的定点供应商 --%>
    <div class="bonceContainer bounce-green" id="addMtSup" style="width: 800px">
        <div class="bonceHead">
            <span>添加材料的定点供应商</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="threeCon bounceMainCon">
                <div class="item-flex">
                    <div class="item-title item100">供应商名称<span class="red">*</span></div>
                    <div class="item-content"><select id="supplier" onchange="matchSupper($(this))" style="width: 100%"></select></div>
                </div>
                <div class="item-flex">
                    <div class="item-title item100">供应商简称</div>
                    <div class="item-content"> <input type="text" id="e_gName1" disabled style="width: 100%"/></div>
                    <div class="item-title item100">供应商代号</div>
                    <div class="item-content"> <input type="text" id="e_gCode1" disabled style="width: 100%"/></div>
                </div>

                <div id="sup1" class="ty-alert ty-alert-border"></div>
                <div>
                    <div class="item-flex supInfo containThis" id="contread">
                        <div class="item-title item300">此材料是否包含于与供应商已签订的合同中？ <input type="hidden" id="ontractsigned"></div>
                        <div class="item-content text-right">
                            <span onclick="ontractsigned(1 , $(this))" class="radioCon"><span class="radioLabel">是</span> <i id="ontractsigned1" class="fa fa-circle-o"></i></span>
                            <span onclick="ontractsigned(0 , $(this))" class="radioCon"><span class="radioLabel">否</span> <i id="ontractsigned0" class="fa fa-circle-o"></i></span>
                        </div>
                    </div>
                    <%--手动新加的--%>
                    <div class="item-flex stable contrInfo">
                        <div class="item-title item300">本材料在哪个合同中</div>
                        <div class="item-content text-right">
                            <span class="link-blue" onclick="editContractBtn($(this), 'new')">新增合同</span><input type="hidden" id="materialcontract">
                            <select id="contract"></select>
                        </div>
                    </div>
                    <div id="edgbox"></div>

                    <div class="item-flex supInfo">
                        <div class="item-title item300">该供应商供应的本材料价格是否稳定？ <input type="hidden" id="isStable"></div>
                        <div class="item-content text-right">
                            <span onclick="isStable(1 , $(this))" class="radioCon"><span class="radioLabel">相对稳定</span> <i id="isStable1" class="fa fa-circle-o"></i></span>
                            <span onclick="isStable(2 , $(this))" class="radioCon"><span class="radioLabel">变动较频繁</span> <i id="isStable2" class="fa fa-circle-o"></i></span>
                        </div>
                    </div>
                    <div class="item-flex stable canInvoice">
                        <div class="item-title item300">购买本材料是否给开发票？ <input type="hidden" id="canInvoice"></div>
                        <div class="item-content text-right">
                            <span onclick="canInvoice(1 , $(this))" class="radioCon"><span class="radioLabel">是</span> <i id="canInvoice1" class="fa fa-circle-o"></i></span>
                            <span onclick="canInvoice(0 , $(this))" class="radioCon"><span class="radioLabel">否</span> <i id="canInvoice0" class="fa fa-circle-o"></i></span>
                            <span class="canInvoice2 radioCon" onclick="canInvoice(2 , $(this))"><span class="radioLabel">不确定</span> <i id="canInvoice2" class="fa fa-circle-o"></i></span>
                        </div>
                    </div>
                    <div class="item-flex stable incoiceType">
                        <div class="item-title item300">购买本材料给开何种发票？ <input type="hidden" id="incoiceType"></div>
                        <div class="item-content text-right">
                            <span onclick="incoiceType(1 , $(this))" class="radioCon" id="incoiceType1Con"><span class="radioLabel">增值税专用发票</span> <i id="incoiceType1" class="fa fa-circle-o"></i></span>
                            <span onclick="incoiceType(2 , $(this))" class="radioCon"><span class="radioLabel">其他发票</span> <i id="incoiceType2" class="fa fa-circle-o"></i></span>
                            <span class="incoiceType4" onclick="incoiceType(4 , $(this))" class="radioCon"><span class="radioLabel">不给开票</span> <i id="incoiceType4" class="fa fa-circle-o"></i></span>
                        </div>
                    </div>
                    <div class="item-flex stable priceInfo" ><input type="hidden" id="isParValue">
                        <div class="item-title item200 type1">已约定的不开票单价</div>
                        <span class="type2">
                            <span class="item80 type3">参考单价</span><input type="hidden" id="referPrice">
                            <span onclick="referPrice(1 , $(this))" class="radioCon">含税 <i id="referPrice1" class="fa fa-circle-o"></i></span>
                            <span onclick="referPrice(0 , $(this))" class="radioCon">不含税 <i id="referPrice0" class="fa fa-circle-o"></i></span>
                        </span>
                        <input class="item100" type="text" id="price" style="margin-left: 16px"/> 元/<span class="purUnit"></span>
                        <i class="taxWrap type2">
                            <span>税率</span>
                            <span class="taxRateBody">
                                <input type="hidden" id="taxRateId" />
                                <input class="item100" type="text" id="taxRate" readonly onclick="taxRateFun($(this))">
                                <span class="taxRateList">
                                    <span><span>10%</span><span>删除</span></span>
                                </span>
                            </span>
                            <span class="linkBtn funBtn" data-fun="addTaxRate">新增</span>
                        </i>
                    </div>
                    <div class="priceInfo stable">
                        <div class="item-flex">
                            <div class="item-title item200">该价格是否含运费?<span class="red">*</span></span> <input type="hidden" id="containYunFee"></div>
                            <div class="item-content">
                                <span onclick="containYunFee(1 , $(this))" class="radioCon"><i id="containYunFee1" class="fa fa-circle-o"></i> 为送货上门价格，含所有运费</span>
                                <div class="manageDeliveryLocations">
                                    <div class="ty-alert">
                                        送货至以下地点时，运费需由供应商承担
                                        <div class="btn-group">
                                            <span class="link-blue" onclick="manageDeliveryLocations(1)">管理交货地点</span>
                                        </div>
                                    </div>
                                    <div class="manageAddressList"></div>
                                    <div class="hd"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="priceInfo stable">
                        <div class="item-flex">
                            <div class="item-title item200"></div>
                            <div class="item-content">
                                <span onclick="containYunFee(2 , $(this))" class="radioCon"><i id="containYunFee2" class="fa fa-circle-o"></i> 含长途运输费用，配送至某城市或地区</span>
                                <div class="manageAddress">
                                    <div class="flexBox">配送至以下区域时，运费需由供应商承担<span class="linkBtn funBtn" data-fun="manageAddress">管理交货地点</span></div>
                                    <div class="manageAreaList"></div>
                                    <div class="hd"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="item-flex stable priceInfo">
                        <div class="item-title item200"></div>
                        <div class="item-content">
                            <span onclick="containYunFee(3 , $(this))" class="radioCon"><i id="containYunFee3" class="fa fa-circle-o"></i> 为离厂价格，不包含任何运费</span>
                        </div>
                    </div>
                    <div class="item-flex supInfo ">
                        <div class="item-title item300">所供应材料的包装方式 <input type="hidden" id="packgeType"></div>
                        <div class="item-content text-right">
                            <span onclick="packgeType(1 , $(this))" class="radioCon"><div class="radioLabel">基本固定</div> <i id="packgeType1" class="fa fa-circle-o"></i></span>
                            <span onclick="packgeType(2 , $(this))" class="radioCon"><div class="radioLabel">型式不定</div> <i id="packgeType2" class="fa fa-circle-o"></i></span>
                        </div>
                    </div>
                    <div class="item-flex supInfo">
                        <div class="item-title item80">采购周期</div>
                        <div class="item-content"><input type="text" class="item100" id="purTurn" onkeyup="clearNoNum1(this)"/> 天 <br><small class="ty-color-blue">注：下单后需多少天可入库</small></div>
                        <div class="item-title item80">最低采购量</div>
                        <div class="item-content"><input type="text" class="item100" id="minPur" onkeyup="clearNoNum1(this)"/> <span class="purUnit"></span></div>
                        <div class="item-title item80">最低库存</div>
                        <div class="item-content"><input type="text" class="item100" id="minStorage" onkeyup="clearNoNum1(this)"/> <span class="purUnit"></span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="addMtSupOk(); ">确定</span>
        </div>
    </div>
    <%-- 材料查看(跟材料录入的有所区别) --%>
    <div class="bonceContainer bounce-blue" id="mtScan2" style="width: 800px">
        <div class="bonceHead">
            <span>材料查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <div class="col-md-6"></div>
                <div class="col-md-6"><span class="item-title">创建人</span><span class="scanMt_create"></span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">材料名称</span><span class="scanMt_name"></span></div>
                <div class="col-md-4"><span class="item-title">材料代号</span><span class="scanMt_code"></span></div>
                <div class="col-md-4"><span class="item-title">计量单位</span><span class="scanMt_unit"></span></div>
            </div>
            <div class="item">
                <div class="col-md-4"><span class="item-title">规格</span><span class="scanMt_specifications"></span></div>
                <div class="col-md-4"><span class="item-title">型号</span><span class="scanMt_model"></span></div>
            </div>
            <div class="item scanExpShow">
                <div class="col-md-12"><span class="scanMt_expStr"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">备注</span><span class="scanMt_memo"></span></div>
            </div>
            <div class="item">
                <div class="col-md-12"><span class="item-title">材料类别</span><span class="scanMt_cat"></span></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel();">确定</span>
        </div>
    </div>

    <%--  添加材料的定点供应商（定点信息）/ 修改定点信息 --%>
    <div class="bonceContainer bounce-blue" id="editPointInfo" style="width: 850px;">
        <div class="bonceHead">
            <span class="bounce_title">查看定点信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <input type="hidden" id="edit_supplier">
            <div class="bounceMainCon">
                <section class="part_addPointMt">
                    <div class="item-flex">
                        <div class="col-md-12">
                            <span class="item-title item100">供应商名称<span class="red">*</span></span>
                            <select name="fullName" onchange="matchSupper($(this))"></select>
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-6">
                            <span class="item-title item100">供应商简称</span>
                            <input type="text" name="name" disabled/>
                        </div>
                        <div class="col-md-6">
                            <span class="item-title item100">供应商代号</span>
                            <input type="text" name="codeName" disabled/>
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-12">
                            <div class="ty-alert ty-alert-border supplierDes" style="line-height: 1.5"></div>
                        </div>
                    </div>
                </section>
                <section class="part_addPointSupplier">
                    <div class="item-flex">
                        <div class="col-md-12">
                            请选择材料
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-6">
                            <span class="item-title item100">材料名称</span>
                            <select name="mtname" onchange="matchMt($(this))"></select>
                        </div>
                        <div class="col-md-6">
                            <span class="item-title item100">材料代号</span>
                            <input type="text" name="mtcode" disabled/>
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-12">
                            <span class="item-title item100">供应商名称<span class="red">*</span></span>
                            <input type="text" name="fullName" disabled/>
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-6">
                            <span class="item-title item100">供应商简称</span>
                            <input type="text" name="name" disabled/>
                        </div>
                        <div class="col-md-6">
                            <span class="item-title item100">供应商代号</span>
                            <input type="text" name="codeName" disabled/>
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-12">
                            <div class="ty-alert ty-alert-border supplierDes" style="line-height: 1.5"></div>
                        </div>
                    </div>
                </section>
                <section class="part_changePointFixed">
                    <div class="item-flex">
                        <div class="col-md-4"><span class="item-title">材料名称：</span><span class="mt_name"></span></div>
                        <div class="col-md-4"><span class="item-title">材料代号：</span><span class="mt_code"></span></div>
                        <div class="col-md-4"><span class="item-title">计量单位：</span><span class="mt_unit"></span></div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-4"><span class="item-title">规格：</span><span class="mt_specifications"></span></div>
                        <div class="col-md-4"><span class="item-title">型号：</span><span class="mt_model"></span></div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-12"><span class="item-title">备注：</span><span class="mt_memo"></span></div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-12"><span class="item-title">材料类别：</span>
                            <span id="s_cat"></span>
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-12">
                            <div class="kj-hr"></div>
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-12">
                            <span class="item-title">供应商名称：</span>
                            <span class="sup_fullName"></span>
                        </div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-6"><span class="item-title">供应商简称：</span> <span class="sup_name"></span></div>
                        <div class="col-md-6"><span class="item-title">供应商代号：</span><span class="sup_codeName"></span></div>
                    </div>
                    <div class="item-flex">
                        <div class="col-md-12">
                            <div class="ty-alert ty-alert-border supplierDes" style="line-height: 1.5"></div>
                        </div>
                    </div>
                </section>
                <section class="part_relation mtAndSupplier">
                    <div class="item-flex">
                        <div class="col-md-12">
                            <div class="kj-hr"></div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div>
                            <div class="item-flex containThis" id="contread">
                                <div class="item-title item300">此材料是否包含于与供应商已签订的合同中？</div>
                                <div class="item-content flex-end">
                                    <span class="radioLabel">是</span>
                                    <div class="ty-radio">
                                        <input type="radio" name="hasContact" id="urgnistad1" value="1">
                                        <label for="urgnistad1"></label>
                                    </div>
                                    <span class="radioLabel">否</span>
                                    <div class="ty-radio">
                                        <input type="radio" name="hasContact" id="urgnistad0" value="0">
                                        <label for="urgnistad0"></label>
                                    </div>
                                </div>
                            </div>
                            <%--手动新加的--%>
                            <div class="hidePart part_contract">
                                <div class="item-flex ">
                                    <div class="item-title item300">本材料在哪个合同中</div>
                                    <div class="item-content flex-end">
                                        <span class="link-blue editContract" origin="editPointInfo" onclick="editContractBtn($(this), 'new')">新增合同</span><input type="hidden">
                                        <select class="kj-select" name="contractSn"></select>
                                    </div>
                                </div>
                                <div id="edgbox"></div>
                            </div>
                            <div class="item-flex">
                                <div class="item-title item300">该供应商供应的本材料价格是否稳定？</div>
                                <div class="item-content flex-end">
                                    <span class="radioLabel">相对稳定</span>
                                    <div class="ty-radio">
                                        <input type="radio" name="priceStable" id="edit_isStable1" value="1">
                                        <label for="edit_isStable1"></label>
                                    </div>
                                    <span class="radioLabel">变动较频繁</span>
                                    <div class="ty-radio">
                                        <input type="radio" name="priceStable" id="edit_isStable2" value="2">
                                        <label for="edit_isStable2"></label>
                                    </div>
                                </div>
                            </div>
                            <div class="item-flex part_isVoice">
                                 <div class="item-title item300">购买本材料是否给开发票？</div>
                                <div class="item-content flex-end">
                                    <span class="radioLabel">是</span>
                                    <div class="ty-radio">
                                        <input type="radio" name="materialInvoicable" id="edit_canInvoice1" value="1">
                                        <label for="edit_canInvoice1"></label>
                                    </div>
                                    <span class="radioLabel">否</span>
                                    <div class="ty-radio">
                                        <input type="radio" name="materialInvoicable" id="edit_canInvoice0" value="0">
                                        <label for="edit_canInvoice0"></label>
                                    </div>
                                    <div class="hidePart uncertain">
                                        <span class="radioLabel">不确定</span><div class="ty-radio">
                                            <input type="radio" name="materialInvoicable" id="edit_canInvoice2" value="2">
                                            <label for="edit_canInvoice2"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="hidePart part_voiceType">
                                <div class="item-flex">
                                    <div class="item-title item300">购买本材料给开何种发票？ <input type="hidden" id="edit_incoiceType"></div>
                                    <div class="item-content flex-end">
                                        <span class="radioLabel">增值税专用发票</span>
                                        <div class="ty-radio">
                                            <input type="radio" name="materialInvoiceCategory" id="edit_incoiceType1" value="1">
                                            <label for="edit_incoiceType1"></label>
                                        </div>
                                        <span class="radioLabel">其他发票</span>
                                        <div class="ty-radio">
                                            <input type="radio" name="materialInvoiceCategory" id="edit_incoiceType2" value="2">
                                            <label for="edit_incoiceType2"></label>
                                        </div>
                                        <%--                                        <span class="radioLabel">不给开票</span>--%>
                                        <%--                                        <div class="ty-radio">--%>
                                        <%--                                            <input type="radio" name="materialInvoiceCategory" id="edit_incoiceType4" value="4">--%>
                                        <%--                                            <label for="edit_incoiceType4"></label>--%>
                                        <%--                                        </div>--%>
                                    </div>
                                </div>
                            </div>
                            <div class="hidePart part_price">
                                <div class="item-flex flex-end">
                                    <input type="hidden" id="edit_isParValue">
                                    <div class="price_des"></div>
                                    <div class="hidePart part_isTax">
                                        <span class="radioLabel">含税</span>
                                        <div class="ty-radio">
                                            <input type="radio" name="isTax" id="edit_referPrice1" value="1">
                                            <label for="edit_referPrice1"></label>
                                        </div>
                                        <span class="radioLabel">不含税</span>
                                        <div class="ty-radio">
                                            <input type="radio" name="isTax" id="edit_referPrice0" value="0">
                                            <label for="edit_referPrice0"></label>
                                        </div>
                                    </div>
                                    <input type="text" class="item100" name="unitPrice" id="edit_price" style="margin-left: 16px"/> 元/<span class="purUnit"></span>
                                    <div class="hidePart part_taxRate" style="margin-left: 16px">
                                        <span>税率</span>
                                        <span class="taxRateBody" style="position: relative">
                                        <input type="hidden" name="supplierInvoice" id="edit_taxRateId" />
                                        <input class="item100" type="text" name="taxRate" id="edit_taxRate" readonly onclick="taxRateFun($(this))">
                                        <span class="taxRateList"></span>
                                    </span>
                                        <span class="linkBtn funBtn" onclick="addTaxRate($(this))">新增</span>
                                    </div>
                                </div>
                            </div>
                            <div class="hidePart part_freight">
                                <div class="priceInfo ">
                                    <div class="item-flex">
                                        <div class="item-title item200">该价格是否含运费?<span class="red">*</span></span> <input type="hidden" id="edit_containYunFee"></div>
                                        <div class="item-content">
                                            <div class="ty-radio">
                                                <input type="radio" name="inclusiveFreight" id="edit_containYunFee1" value="1">
                                                <label for="edit_containYunFee1"></label> <span>为送货上门价格</span>
                                            </div>
                                            <div class="hidePart part_sendHomePlace" style="margin-left: 20px">
                                                <div class="item-flex">
                                                    送货至以下地点时，运费需由供应商承担
                                                    <div style="flex: auto; text-align: right">
                                                        <span class="link-blue" onclick="manageDeliveryLocations(1)">管理交货地点</span>
                                                    </div>
                                                </div>
                                                <div class="address_box manageAddressList_1"></div>
                                                <div class="hd"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item-flex">
                                        <div class="item-title item200"></div>
                                        <div class="item-content">
                                            <div class="ty-radio">
                                                <input type="radio" name="inclusiveFreight" id="edit_containYunFee2" value="2">
                                                <label for="edit_containYunFee2"></label> <span>含长途运输费用，配送至某城市或地区</span>
                                            </div>
                                            <div class="hidePart part_sendLongPlace" style="margin-left: 20px">
                                                <div class="item-flex">
                                                    配送至以下区域时，运费需由供应商承担
                                                    <div style="flex: auto; text-align: right">
                                                        <span class="link-blue" onclick="manageDeliveryLocations(2)">管理交货地点</span>
                                                    </div>
                                                </div>
                                                <div class="address_box manageAddressList_2"></div>
                                                <div class="hd"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item-flex">
                                        <div class="item-title item200"></div>
                                        <div class="item-content">
                                            <div class="ty-radio">
                                                <input type="radio" name="inclusiveFreight" id="edit_containYunFee3" value="3">
                                                <label for="edit_containYunFee3"></label> <span> 为离厂价格，不包含任何运费</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="item-flex">
                                <div class="item-title item300">所供应材料的包装方式 <input type="hidden" id="edit_packgeType"></div>
                                <div class="item-content flex-end">
                                    <span class="radioLabel">基本固定</span>
                                    <div class="ty-radio">
                                        <input type="radio" name="packageMethod" id="edit_packgeType1" value="1">
                                        <label for="edit_packgeType1"></label>
                                    </div>
                                    <span class="radioLabel">型式不定</span>
                                    <div class="ty-radio">
                                        <input type="radio" name="packageMethod" id="edit_packgeType2" value="2">
                                        <label for="edit_packgeType2"></label>
                                    </div>
                                </div>
                            </div>
                            <div class="part_exp">
                                <div class="item-flex">
                                    <div class="item-title item350">该供应商供应本材料的保质期，为自生产日期之后多久？</div>
                                    <div class="item-content flex-end">
                                        <input class="item100" type="text" name="expirationDays" onkeyup="clearNoNum1(this)"> 天
                                    </div>
                                </div>
                            </div>

                            <div class="item-flex">
                                <div class="item-title item80">采购周期</div>
                                <div class="item-content"><input type="text" class="item100" name="perchaseCycle" id="edit_purTurn" onkeyup="clearNoNum1(this)"/> 天 <br><small class="ty-color-blue">注：下单后需多少天可入库</small></div>
                                <div class="item-title item80">最低采购量</div>
                                <div class="item-content"><input type="text" class="item100" name="minimumPurchase" id="edit_minPur" onkeyup="clearNoNum1(this)"/> <span class="purUnit"></span></div>
                                <div class="item-title item80">最低库存</div>
                                <div class="item-content"><input type="text" class="item100" name="minimumStock" id="edit_minStorage" onkeyup="clearNoNum1(this)"/> <span class="purUnit"></span></div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="edit ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="edit ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="editMtSupOk() ">确定</span>
        </div>
    </div>


    <%-- 未勾选完整提示弹窗 --%>
    <div class="bonceContainer bounc_blue" id="toolTipbox">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >
            <p>还有必填项尚未填写！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >
            <div id="contracto">
                <p>本材料已暂停从该供应商处采购。</p>
                <p>如需继续向其采购，请为其“恢复采购”！</p>
            </div>
            <div id="mustchoose">
                <p>还有必填项尚未填写！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce.cancel(); bounce.show($('#addSupply'))">我知道了</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">我知道了</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="bounceFixed_tip" style="width:400px;">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg text-center">确定删除吗?</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="addUnitOk(7)">确定</span>
        </div>
    </div>
    <%--导入--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="exportStep">
                <div class="stepItem">
                    <p>第一步：下载空白的“材料清单”。</p>
                    <div class="flexRow">
                        <span>材料清单</span>
                        <a href="../assets/oralResource/template/material_blank_sheet.xls" id="mould1" download="材料清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下载</a>
                    </div>
                </div>
                <div class="stepItem">
                    第二步：在空白的“材料清单”中填写内容，并存至电脑。
                </div>
                <div class="stepItem">
                    <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                    <div class="flexRow">
                        <button class="ty-btn ty-btn-middle ty-btn-blue" onclick="chooseMtFile()">浏览</button>
                        <div class="upload_sect viewBtn hd">
                            <div id="uploadFile"></div>
                        </div>
                        <div class="fileFullName">尚未选择文件</div>
                    </div>
                </div>
                <div class="stepItem">
                    <p>第四步：点击“导入”。</p>
                    <div class="flexRow">
                        <span class="ty-btn ty-btn-middle" onclick="importConfirm('cancel');">取消</span>
                        <span class="ty-btn ty-btn-blue ty-btn-middle" onclick="importConfirm()">导入</span>
                    </div>
                </div>
                <div class="importIntro stepItem">
                    <p class="ty-color-red"><span>导入说明：</span></p>
                    <div style="font-size: 13px;color: #3d566f;">
                        <span>1、请勿增加、删除或修改所下载“材料清单”空白表的“列”，否则上传会失败。</span></br>
                        <span>2、在电脑上保存“材料清单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%-- 下一步确定  --%>
    <div class="bonceContainer bounce-red" id="importListTj">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>还有<span id="errNum"></span>种材料无法保存至系统。</p>
            <p>进入下一步，这些材料将被舍弃。</p>
            <p>确定进入下一步吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="importListTjSure()">确定</span>
        </div>
    </div>
    <%-- 放弃  --%>
    <div class="bonceContainer bounce-red" id="importCancel">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon ty-center">
            <p>放弃后，本次批量导入的数据将消失不见。</p>
            <p>确定放弃吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" data-name="importCancelSure">确定</span>
        </div>
    </div>
    <%-- 保存  --%>
    <div class="bonceContainer bounce-red" id="importListSave">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <p>确定后：</p>
                <p class="saveTip"></p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" data-name="lastSaveSure">确定</span>
        </div>
    </div>
    <%-- 上次的批量导入尚未完成。  --%>
    <div class="bonceContainer bounce-red" id="importNotCompleted">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody unfinishedForm">
                <p>上次的批量导入尚未完成。</p>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="1"></span>继续上次的操作
                </div>
                <div class="changeDot">
                    <span class="fa fa-circle-o" data-type="0"></span>放弃上次的操作，重新批量导入
                </div>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" data-name="judgeImport">确定</span>
        </div>
    </div>
    <%-- 修改导入的材料 --%>
    <div class="bonceContainer bounce-green" id="updateImportMt" style="width: 900px">
        <div class="bonceHead">
            <span>材料修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item clear">
                <div class="col-md-6"><span class="item-title">材料名称<span class="ty-color-red">*</span></span><input name="name" type="text" require need/></div>
                <div class="col-md-6"><span class="item-title">材料代号<span class="ty-color-red">*</span></span><input name="code" type="text" require need/></div>
            </div>
            <div class="item clear">
                <div class="col-md-6"><span class="item-title">规格</span><input name="specifications" require  type="text" /></div>
                <div class="col-md-6"><span class="item-title">型号</span><input name="model" require  type="text" /></div>
            </div>
            <div class="item clear">
                <div class="col-md-12"><span class="item-title">备注</span><input name="memo" type="text" require style="width:700px; "  onkeyup="countWords($(this),100)" /><span class="textMax">0/100</span></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" data-name="updateImportMt">确定</span>
        </div>
    </div>
    <%-- 删除导入的材料  --%>
    <div class="bonceContainer bounce-red" id="importMtDel">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">确定删除所导入的这个材料吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="importMtDel()">确定</span>
        </div>
    </div>
    <%-- 材料清单 --%>
    <div class="bonceContainer bounce-blue" id="cmaterials">
        <div class="bonceHead">
            <span class="recordTtl">材料清单</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="createRecord clear" id="contactlist">
                <p class="ty-left recordTip">
                    <span></span>
                </p>
            </div>
            <table class="ty-table ty-table-control material" id="coactbank">
                <thead>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>计量单位</td>
                </tr>
                </thead>
                <tbody id="bothon"></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="cloose($(this))">关闭</span>
        </div>
    </div>

    <%-- 温馨提示 --%>
    <div class="bonceContainer bounce-red" id="mtTip" style="width: 400px;">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center;font-size: 14px;"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%-- 供应商常规信息的修改 --%>
    <div class="bonceContainer bounce-blue" id="changeMtSupplier" style="min-width: 750px;">
        <div class="bonceHead">
            <span>供应商常规信息的修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y:auto;">
            <div class="bounceMainCon">
                <h4 class="supplierName"></h4>
                <table class="kj-table tbl_changeMtSupplier">
                    <thead>
                    <tr>
                        <td>名称</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody class="box1">
                    <tr>
                        <td>基本信息</td>
                        <td><span class="link-blue" onclick="mtSupplierPartEdit($(this), 'base')">修改</span></td>
                    </tr>
                    <tr>
                        <td>开票信息</td>
                        <td><span class="link-blue" onclick="mtSupplierPartEdit($(this), 'invoice')">修改</span></td>
                    </tr>
                    <tr class="contract">
                        <td>合同信息</td>
                        <td><span class="link-blue"  origin="editMtSupplier" onclick="editContractBtn($(this), 'new')">新增</span></td>
                    </tr>
                    <tr class="mail">
                        <td>邮寄信息</td>
                        <td><span class="link-blue" onclick="editMailBtn($(this), 'new')">新增</span></td>
                    </tr>
                    <tr class="contact">
                        <td>联系人</td>
                        <td><span class="link-blue" onclick="editContactBtn($(this), 'new')">新增</span></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%-- 供应商信息查看 --%>
    <div class="bonceContainer bounce-blue" id="havelook" style="min-width: 1200px;position: absolute; z-index: 10">
        <div class="bonceHead">
            <span>供应商信息查看</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="height: 600px;overflow: auto">
            <div class="lina" style="margin: 43px;">
                <div class="part">
                    <div class="item" style="display: flex">
                        <div class="item" style="display: flex">
                            <span class="item_title" style="text-align: left;">供应商名称:</span>
                            <span class="item_content" style="width: 600px" id="see_name"></span>
                        </div>
                    </div>
                    <div class="item" style="display: flex">
                        <div class="item" style="display: flex">
                            <span class="item_title" style="text-align: left;">供应商简称:</span>
                            <span class="item_content" id="see_fullname" style="width: 300px"></span></div>
                        <div class="item" style="margin-left: 348px;">
                        <span class="item_title" style="margin-right: 0;">
                            供应商代号:
                        </span>
                            <span class="item_content" id="see_codde" style="width: 200px"></span>
                        </div>
                    </div>
                    <div class="item" style="display: flex">
                        <div class="item" style="display: flex">
                            <span class="item_title" style="text-align: left;">创建者:</span>
                            <span class="item_content">
                            <span id="see_createName"></span>
                            <span id="see_createDate"></span>
                        </span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item" id="prient">
                            <span class="item_title" style="text-align: left;">暂停采购:</span>
                            <span class="item_content" style="width: 300px"></span>
                        </div>
                    </div>
                </div>
                <div class="part">
                    <div class="item" style="display:flex;">
                        <div class="item_title" style="text-align: left;">全景照片：</div>
                        <div class="item_content" id="overallImgUpload"></div>
                    </div>
                    <div id="gotpose" style="display: flex;align-items: center">
                        <p style="margin: 0 0 0;">可接受挂账，账期<span>XX</span>天，则货物入库之日起计算，不需要预付款</p>
                    </div>
                </div>
                <div class="part">
                    <div class="item" style="align-items: center;">
                        <span class="item_title" style="text-align: left;">开票信息</span>
                    </div>
                    <div id="shuil" style="display: flex">
                        <p style="margin: 0 0 0;">该供应商能开税率为<span>X</span>%的增值税专用发票，可接收承兑汇票</p>
                    </div>
                </div>
                <div class="addOtherInfo">
                    <span class="par_ttl" style="margin-left: 8px;">合同信息</span>
                    <span style="color: #5399c2;font-size: 0.8em;margin-left: 50px;"></span>
                    <div class="dataList contractList">
                        <table class="ty-table ty-table-control">
                            <thead style="background: none">
                            <td>合同编号</td>
                            <td>签署日期</td>
                            <td>合同的有效期</td>
                            <td>操作</td>
                            </thead>
                            <tbody id="clist11"></tbody>
                        </table>
                    </div>
                </div>
                <div class="addOtherInfo">
                    <span class="par_ttl" style="margin-left: 8px;">邮寄信息</span>
                    <div class="dataList receiveList">
                        <table class="ty-table ty-table-control mailPlaceList">
                            <thead style="background: none">
                            <td>邮寄地址</td>
                            <td>联系人</td>
                            <td>邮政编码</td>
                            <td>手机</td>
                            </thead>
                            <tbody id="clist21"></tbody>
                        </table>
                    </div>
                </div>
                <div class="addOtherInfo">
                    <span class="par_ttl" style="margin-left: 8px;margin-right: -38px;">联系人</span>
                    该供应商现共有如下<span class="contactNum"></span>位联系人
                    <div class="dataList receiveListo">
                        <table class="ty-table ty-table-control">
                            <thead style="background: none">
                            <td>姓名</td>
                            <td>职位</td>
                            <td>手机</td>
                            </thead>
                            <tbody id="clist31"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 提示弹窗 --%>
    <div class="bonceContainer bounce-red" id="tiphoto" style="width:450px">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="shu1">
                <p id="prompttext" style="text-align:center;padding:10px 0 8px;">
                    <span class="sureno">确定后，相关材料将不再属于本合同。</span>
                    <span class="ttwo">确定进行本操作吗？</span>
                </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" id="beforeof" onclick="bounce_Fixed.cancel();bounce_Fixed.show($('#upcontent'))">取消</span>
            <span class="ty-btn ty-btn-big ty-circle-3" id="overstop" onclick="bounce_Fixed.cancel();bounce_Fixed2.show($('#stopboot'))">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" id="maisure" data-name="maisure">确定</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" id="stopaway" onclick="stopdowno($(this))">确定</span>
        </div>
    </div>
    <%-- 删除提示弹窗 --%>
    <div class="bonceContainer bounce-red" id="deleteConfirm">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">确定删除吗?</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <%-- 确定删除本合同么 --%>
    <div class="bonceContainer bounce-red" id="contractDelTip" style="width:450px;">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;">确定删除本合同么?</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="contractDelTipOk()">确定</span>
        </div>
    </div>
<%----------确定定点信息-------------%>
    <%-- 新增税率  --%>
    <div class="bonceContainer bounce-green" id="addTaxRate">
        <div class="bonceHead">
            <span>新增税率</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center"><input type="text" placeholder=" 请输入税率" id="taxRateVal" onkeyup="clearNoNum(this)" /></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="addTaxRatetOk()">确定</span>
        </div>
    </div>
    <%-- 管理交货地点--%>
    <div class="bonceContainer bounce-blue" id="chooseDelivery" style="width: 800px">
        <div class="bonceHead">
            <span>管理交货地点</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="background:#f0f8ff ">
            <div class="wideBody">
                <div class="ty-alert">
                    <span>系统内可供选择的收货地址如下，可多选</span>
                    <div class="btn-group">
                        <div class="link-blue btnName" onclick="newAddress()">新增收货地址</div>
                    </div>
                </div>
                <div class="blueCare">
                    <small class="care1">注：向未勾选的收获地址发货，录入采购订单时需录入新的价格，系统将给予提示。</small>
                    <small class="care2">注：向未勾选的到货区域发货，录入采购订单时需录入新的价格，系统将给予提示。</small>
                </div>
                <table class="kj-table kj-table-striped kj-table-none tbl_chooseDelivery_address" style="margin-top: 8px">
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="chooseDeliveryOk()">确定</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 导入的重要提示 --%>
    <div class="bonceContainer bounce-red" id="importantTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="gap-lt">
                <h4>导入失败！</h4>
                <div>原因可能为：</div>
                <div>1、修改了所下载表格中的“列”。</div>
                <div>2、选错了文件。</div>
                <div>3、文件太大，或里面含有图片等。</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red" onclick="bounce_Fixed2.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道 - 提示--%>
    <div class="bonceContainer bounce-red" id="iknowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="iknowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 新增供应商 --%>
    <div class="bonceContainer bounce-green window" id="editMtSupplier" style="min-width: 1114px">  <%--大神改的位置--%>
        <div class="bonceHead">
            <span class="bounce_title">新增供应商</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon sale_updata threeCon" id="addsupplier">
                <section class="base">
                    <div class="section_title"><span class="title">基本信息</span></div>
                    <div class="section_content">
                        <div class="item-flex">
                            <div class="item-title require item100">供应商名称: </div>
                            <div class="item-content"><input type="text" class="add_splName" name="fullName" placeholder="请录入" require style="width: 100%;"></div>
                        </div>
                        <div class="item-flex">
                            <div class="item-title require item100">供应商简称: </div>
                            <div class="item-content"><input type="text" name="name" placeholder="请录入" require style="width:407px"></div>
                            <div class="item-title require item100">供应商代号: </div>
                            <div class="item-content"><input type="text" name="codeName" placeholder="请录入" require style="width: 100%;"></div>
                        </div>
                        <div class="item-flex">
                            <div class="item-title item100">全景照片: </div>
                            <div class="item-content">
                                <div id="edit_panoramaBtn" class="saleUploadBtn ty-left"></div>
                                <div class="sale_ttl1">（共可上传9张）</div>
                                <div class="file_avatar"></div>
                            </div>
                        </div>
                        <div class="item-flex">
                            <div class="item-title item200"> 是否接受挂账？ </div>
                            <div class="item-content">
                                <div class="ty-radio">
                                    <input type="radio" name="chargeAcceptable" id="chargeAcceptable1" value="1" autocomplete="new-password">
                                    <label for="chargeAcceptable1"></label>
                                </div>
                                <span class="radioLabel">接受</span>
                                <div class="ty-radio">
                                    <input type="radio" name="chargeAcceptable" id="chargeAcceptable0" value="0" autocomplete="new-password">
                                    <label for="chargeAcceptable0"></label>
                                </div>
                                <span class="radioLabel">不接受</span>
                                <span class="hidePart part_chargePeriod">
                                    <span>请录入已约定的账期</span>
                                    <input type="text" name="chargePeriod" onkeyup="clearNoNum(this)" />天
                                </span>
                            </div>
                        </div>
                        <div class="item-flex hidePart part_isChargeBegin">
                            <div class="item-title item200"> 请选择从何时开始计算账期？ </div>
                            <div class="item-content">
                                <div class="ty-radio">
                                    <input type="radio" name="chargeBegin" id="chargeBegin1" value="1" autocomplete="new-password">
                                    <label for="chargeBegin1"></label>
                                </div>
                                <span class="radioLabel">自货入库之日起</span>
                                <div class="ty-radio">
                                    <input type="radio" name="chargeBegin" id="chargeBegin2" value="2" autocomplete="new-password">
                                    <label for="chargeBegin2"></label>
                                </div>
                                <span class="radioLabel">自发票入账之日起</span>
                            </div>
                        </div>
                        <div class="item-flex">
                            <div class="item-title item200"> 是否需要预付款？ </div>
                            <div class="item-content">
                                <div class="ty-radio">
                                    <input type="radio" name="isImprest" id="isImprest1" value="1" autocomplete="new-password">
                                    <label for="isImprest1"></label>
                                </div>
                                <span class="radioLabel">需要</span>
                                <div class="ty-radio">
                                    <input type="radio" name="isImprest" id="isImprest2" value="2" autocomplete="new-password">
                                    <label for="isImprest2"></label>
                                </div>
                                <span class="radioLabel">不需要</span>
                                <div class="ty-radio">
                                    <input type="radio" name="isImprest" id="isImprest0" value="0" autocomplete="new-password">
                                    <label for="isImprest0"></label>
                                </div>
                                <span class="radioLabel">不确定</span>
                            </div>
                        </div>
                        <div class="item-flex hidePart part_isImprestProportion">
                            <div class="item-title item200"> 请录入需预付的比例 </div>
                            <div class="item-content">
                                <input type="text" name="imprestProportion" onblur="clear0($(this))" onkeyup="clearNoNum(this)">%
                                <div class="ty-radio">
                                    <input type="radio" name="uncertainty" id="uncertainty" value="1">
                                    <label for="uncertainty"></label>
                                </div>
                                <span class="radioLabel">比例不确定</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="invoice">
                    <div class="section_title"><span class="title">开票信息</span></div>
                    <div class="section_content">
                        <div class="item-flex">
                            <div class="item-title require item200"> 该供应商是否能开发票？ </div>
                            <div class="item-content">
                                <div class="ty-radio">
                                    <input type="radio" name="invoicable" id="invoicable1" value="1">
                                    <label for="invoicable1"></label>
                                </div>
                                <span class="radioLabel">是</span>
                                <div class="ty-radio">
                                    <input type="radio" name="invoicable" id="invoicable0" value="0">
                                    <label for="invoicable0"></label>
                                </div>
                                <span class="radioLabel">否</span>
                            </div>
                        </div>
                        <div class="item-flex hidePart part_isVatsPayable">
                            <div class="item-title item200"> 是否能开增值税专用发票？ </div>
                            <div class="item-content">
                                <div class="ty-radio">
                                    <input type="radio" name="vatsPayable" id="vatsPayable1" value="1">
                                    <label for="vatsPayable1"></label>
                                </div>
                                <span class="radioLabel">是</span>
                                <div class="ty-radio">
                                    <input type="radio" name="vatsPayable" id="vatsPayable0" value="0">
                                    <label for="vatsPayable0"></label>
                                </div>
                                <span class="radioLabel">否</span>
                                <span class="hidePart part_taxRate">
                                    <span>请录入税率</span>
                                    <input type="text" name="taxRate" onkeyup="clearNoNum(this)" /> %
                                </span>
                            </div>
                        </div>
                        <div class="item-flex hidePart part_isDraftAcceptable">
                            <div class="item-title item200"> 是否可接受汇票？ </div>
                            <div class="item-content">
                                <div class="ty-radio">
                                    <input type="radio" name="draftAcceptable" id="draftAcceptable1" value="1" autocomplete="new-password">
                                    <label for="draftAcceptable1"></label>
                                </div>
                                <span class="radioLabel">可接受</span>
                                <div class="ty-radio">
                                    <input type="radio" name="draftAcceptable" id="draftAcceptable0" value="0" autocomplete="new-password">
                                    <label for="draftAcceptable0"></label>
                                </div>
                                <span class="radioLabel">不确定</span>
                            </div>
                        </div>
                    </div>
<%--                    <p class="leMar" id="havenoce" style="margin-bottom: 17px;display: flex"><span class="ty-color-red" style="text-align: left;margin-left: -10px;--%>
<%--                        width: auto;display: block;padding-right: 0px;">*</span>--%>
<%--                        该供应商是否能开发票？<input type="hidden" id="haveInvoice">--%>
<%--                        <span onclick="haveInvoice(1 , $(this))" class="radioCon" style="margin-left: 27px;">--%>
<%--                        <i id="haveInvoice1" class="fa fa-circle-o"></i>是--%>
<%--                     </span>--%>
<%--                        <span onclick="haveInvoice(0 , $(this))" class="radioCon" style="margin-left: 84px;">--%>
<%--                         <i id="haveInvoice0" class="fa fa-circle-o"></i>否--%>
<%--                    </span>--%>
<%--                    </p>--%>
<%--                    <p class="leMar noNext godemo" id="contemo" style="display: flex;align-items: center;">--%>
<%--                        是否能开增值税专用发票？ <input type="hidden" id="vatInvoice">--%>
<%--                        <span onclick="vatInvoice(1, $(this))" class="radioCon" style="margin-left: 13px;">--%>
<%--                        <i id="vatInvoice1" class="fa fa-circle-o"></i>是--%>
<%--                    </span>--%>
<%--                        <span onclick="vatInvoice(2, $(this))" class="radioCon" style="margin-left: 84px;">--%>
<%--                        <i id="vatInvoice2" class="fa fa-circle-o"></i>否--%>
<%--                     </span>--%>
<%--                        <span class="godemo_1" style="width: auto;margin-left: 122px;display: inline-block;">--%>
<%--                        <span class="litT">请录入税率</span> <input type="text" id="e_gRate0" onkeyup="clearNoNum(this)"--%>
<%--                                                               data-options="min:0,max:100">%--%>
<%--                    </span>--%>
<%--                    </p>--%>
<%--                    <p class="leMar order hd part hp" style="margin-margin-top: 17px;margin-bottom: 10px;display: block;margin-left: 40px;--%>
<%--                display: flex;">--%>
<%--                        是否可接受汇票？ <input type="hidden" id="huip">--%>
<%--                        <span onclick="huip(1 , $(this))" class="radioCon" style="margin-left: 70px;"><i id="huip1" class="fa fa-circle-o"></i>可接受</span>--%>
<%--                        <span onclick="huip(0 , $(this))" class="radioCon" style="margin-left: 56px;"><i id="huip0" class="fa fa-circle-o"></i>不确定</span>--%>
<%--                    </p>--%>
                </section>
                <section class="addOtherInfo">
                    <div class="section_title">
                        <span class="title">合同信息</span>
                        <span id="newContractBtn" class="ty-btn ty-btn-blue ty-circle-3 node" origin="editMtSupplier" onclick="editContractBtn($(this), 'new')">新增</span>
                    </div>
                    <div class="section_content contractList">
                        <table class="kj-table tbl_contract">
                            <thead>
                            <tr>
                                <td>合同编号</td>
                                <td>签署日期</td>
                                <td>合同的有效期</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </section>
                <section class="addOtherInfo">
                    <div class="section_title">
                        <span class="title">邮寄信息</span>
                        <span id="newMailBtn" class="ty-btn ty-btn-blue ty-circle-3 node" onclick="editMailBtn($(this), 'new')">新增</span>
                    </div>
                    <div class="section_content">
                        <table class="kj-table tbl_mail">
                            <thead>
                            <tr>
                                <td>邮寄地址</td>
                                <td>联系人</td>
                                <td>邮政编码</td>
                                <td>手机</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </section>
                <section class="addOtherInfo">
                    <div class="section_title">
                        <span class="title">联系人</span>
                        <span id="newContactBtn" class="ty-btn ty-btn-blue ty-circle-3 node" onclick="editContactBtn($(this), 'new')">新增</span>
                    </div>
                    <div class="section_content">
                        <table class="kj-table tbl_contact">
                            <thead>
                            <tr>
                                <td>姓名</td>
                                <td>职位</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </section>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button><span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="sureEditMtSupplierBtn" onclick="sureEditMtSupplier()" disabled="">提交</button>
        </span>
        </div>
    </div>
    <%-- 名片 --%>
    <div class="bonceContainer bounce-blue" id="phunte" >
        <div class="bonceHead">
            <span>名片</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel();bounce_Fixed.show($('#havelook'));"></a>
        </div>
        <div class="bonceCon" id="poncune"></div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel($('#phunte'));bounce_Fixed.show($('#havelook'))">关闭</span>
        </div>
    </div>
<%---------------定点信息-----------%>
    <%--新增收货信息--%>
    <div class="bonceContainer bounce-blue" id="editReceiveInfo" style="width: 450px">
        <div class="bonceHead">
            <span>收货信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item-flex">
                <span class="sale_ttl1">收货地址</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" name="address" require/></span>
            </div>
            <div class="item-flex">
                <span class="sale_ttl1">联系人</span>
                <span class="sale_con1" onclick="chooseContactInput()">
                    <input type="text" name="contact" readonly placeholder="请选择" class="contactName" require/>
                    <span class="hd"></span>
                </span>
                <span class="link-blue" origin="pointInfo" onclick="editContactBtn($(this), 'new')" style="margin-left: 8px">新增</span>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="sureEditReceiveInfo()">提交</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
<%---------------供应商部分-----------------%>
    <%-- 查看合同 --%>
    <div class="bonceContainer bounce-blue" id="lookpoot" style="width: 874px;">
        <div class="bonceHead">
            <span>查看合同</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel();"></a>
        </div>
        <div class="bonceCon" style="padding: 64px;padding-top: 64px;margin-left: 20px;padding-top: 40px;">
            <p style="margin-left: 18px;">本版本合同的创建<span id="contentmain">XXX XXXX-XX-XX XX:XX:XX</span></p>
            <table class="ty-table ty-table-control indexUpdateList">
                <tbody>
                <tr>
                    <td style="text-align: left;">合同编号</td>
                    <td id="contentmainSn" style="text-align: left;">XXXXXXXX</td>
                </tr>
                <tr>
                    <td>签署日期</td>
                    <td id="contentSignDate">XXXX-XX-XX</td>
                </tr>
                <tr>
                    <td>合同的有效期</td>
                    <td id="contentValidDate">XXXX-XX-XX至XXXX-XX-XX</td>
                </tr>
                <tr>
                    <td>合同的扫描件或照片</td>
                    <td id="contentImgs">
                    </td>
                </tr>
                <tr>
                    <td>合同的可编辑版</td>
                    <td>
                        <span class="ty-color-blue node " id="contentTxt">查看</span>
                    </td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td id="contentMemo"></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="conook" onclick="bounce_Fixed3.cancel()">关闭</span>      <!--供应商信息查看-->
        </div>
    </div>
    <%-- 新增合同 --%>
    <div class="bonceContainer bounce-green" id="editContract" style="width: 450px">
        <div class="bonceHead">
            <span class="bounce_title">新增合同</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="flex-box">
                    <div class="cItem">
                        <div><span class="ty-color-red">*</span> 合同编号</div>
                        <input class="ty-inputText" name="sn" placeholder="请录入" require>
                    </div>
                    <div class="cItem">
                        <div>签署日期</div>
                        <input class="ty-inputText" id="editContract_signTime" name="signTime" placeholder="请选择" readonly>
                    </div>
                </div>
                <div class="flex-box">
                    <div class="cItem">
                        <div>合同的有效期</div>
                        <input class="ty-inputText" id="editContract_validStart" name="validStart" placeholder="请选择" readonly>
                    </div>
                    <div class="cItem">
                        <div></div>
                        <span>至</span>
                    </div>
                    <div class="cItem">
                        <div></div>
                        <input class="ty-inputText" id="editContract_validEnd" name="validEnd" placeholder="请选择" readonly>
                    </div>
                </div>
                <div class="cItem uploadCon">
                    合同的扫描件或照片(共可上传9张)
                    <span class="ty-right" id="editContract_img"></span>
                    <span class="ty-right link-blue clearAll">清空</span>
                    <div class="file_avatar imgCon"></div>
                </div>
                <div class="cItem uploadCon">
                    合同的可编辑版
                    <span class="ty-right" id="editContract_doc"></span>
                    <span class="ty-right link-blue clearAll">清空</span>
                    <div class="file_avatar docCon"></div>
                </div>
                <div class="cItem">
                    <div>备注</div>
                    <input class="ty-inputText" name="memo" type="text" style="width:100%" placeholder="请录入">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</button>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="editContractOk1" onclick="sureEditContract()">提交</button>
        </div>
    </div>
<%---------------定点信息-----------------%>
    <%-- 选择联系人--%>
    <div class="bonceContainer bounce-blue" id="chooseCusContact" style="width: 450px">
        <div class="bonceHead">
            <span>选择客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon" style="background:#f0f8ff ">
            <table class="kj-table kj-table-striped kj-table-none tbl_cusContactChoose">
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="chooseCusContactOk()">确定</button>
        </div>
    </div>
    <%-- 新增邮寄信息 --%>
    <div class="bonceContainer bounce-green" id="editMail" style="width: 460px">
        <div class="bonceHead">
            <span class="bounce_title">新增邮寄信息</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel();"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">邮寄地址：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" name="address" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">邮寄编码：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" name="postcode" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">联系人：</span>
                <span class="sale_con1">
                    <select name="supplierContact" style="width: 220px"></select>
                </span>
                <span class="link-blue" onclick="editContactBtn($(this), 'new')" style="margin-left: 8px">新增</span> <br>
                <span class="link-blue" onclick="seeContactBtn()" style="margin-left: 115px">查看联系人</span>
            </p>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel();">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-3" onclick="sureEditMail()">提交</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed4">
<%---------------定点信息-----------------%>
    <%--联系方式查看--%>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed4.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签：</span>
                    <span class="sale-con" id="see_contactTag"></span>
                </div>
            </div>
            <%--<p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>--%>
            <table class="see_otherContact ty-table" style="width:442px;">
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed4.cancel()">关闭</span>
        </div>
    </div>

    <%--新增联系人--%>
    <div class="bonceContainer bounce-green" id="editContact" style="width: 750px">
        <div class="bonceHead">
            <span>新增客户联系人</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon linkUploadify">
            <div class="bounceMainCon">
                <div class="item-flex">
                    <div class="item-title">联系人标签：</div>
                    <div class="item-content"></div>
                </div>
                <div class="item-flex">
                    <div class="item-title">姓名：</div>
                    <div class="item-content"><input class="ty-inputText" type="text" placeholder="请录入" id="contactName" require/></div>
                    <div class="item-title">职位：</div>
                    <div class="item-content"><input class="ty-inputText" type="text" placeholder="请录入" id="position" require/></div>
                </div>
                <div class="item-flex">
                    <div class="item-title">手机：</div>
                    <div class="item-content"><input class="ty-inputText" type="text" placeholder="请录入" id="contactNumber" require/></div>
                    <div class="item-title"><span class="link-blue" onclick="addMore($(this))">添加更多联系方式</span></div>
                    <div class="item-content">
                        <select class="ty-select" style="display: none" id="addMoreContact" onchange="addMoreChange($(this))">
                            <option value="0">---请选择---</option>
                            <option value="1">手机</option>
                            <option value="2">QQ</option>
                            <option value="3">Email</option>
                            <option value="4">微信</option>
                            <option value="5">微博</option>
                            <option value="9">自定义</option>
                        </select>
                    </div>
                </div>
                <div class="otherContact"></div>
                <div class="item-flex" id="contactsCard">
                    <div class="item-title">名片：</div>
                    <div class="item-content">
                        <div id="uploadCard" class="cardUploadBtn"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="addContact" data-fun="addContactOk" onclick="sureEditContact()">提交</button>
        </div>
    </div>
</div>
<div class="bounce_Fixed5">
    <%-- tip --%>
    <div class="bonceContainer bounce-blue" id="bounceFixed5_tip" style="width:400px;">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed5.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed5.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed5.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLabel" type="text" placeholder="请录入联系方式的标签" onkeyup="countWords($(this),20)"/>
                <a class="clearLableText" onclick="$(this).prev().val('')">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed5.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="addNewLableSure" onclick="addNewContactLabel()">使用</button>
        </div>
    </div>
    <%-- 联系人查看 --%>
    <div class="bonceContainer bounce-blue" id="seeContact" style="width:600px;">
        <div class="bonceHead">
            <span>供应商联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed5.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactFlex">
                <div style="display: inline-block;margin-left: 29px;">
                    <span>姓名:</span>
                    <span class="sale-con see_name"></span>
                </div>
                <div style="display:inline-block;">
                    <span class="sale_ttl1">职位:</span>
                    <span class="sale-con see_post"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签:</span>
                    <span class="sale-con seeContactTag"></span>
                </div>
            </div>
            <table class="see_otherContact kj-table" style="width: 442px;margin: 20px auto;">
                <tbody>
                <tr></tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed5.cancel();">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>采购</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div id="picShow" style="display: none;">
            <img src=""/>
        </div>
        <div class="page-content">
            <div class="ty-container" id="home">
                <div class="ty-page-header" style="display: none">
                    <div class="page-header__left" onclick="back()">
                        <i class="iconfont icon-houtui icon-back"></i>
                        <span>返回</span>
                    </div>
                    <div class="page-header__content" onclick="backToMain()">
                        <i class="iconfont icon-zhuye icon-back"></i>
                        <span>主页</span>
                    </div>
                </div>
                <div class="page" page="main">
                    <div class="pagePart" part="edit">
                        <div class="panel-box">
                            <div class="panel-box_item">
                                <div class="left_item" style="width: 400px">欢迎对“采购”模块进行初始化！</div>
                                <div class="right_item">
                                    <div class="ty-radio checkRadio">
                                        <input type="radio" name="" id="initCompleted">
                                        <label for="initCompleted"></label> “必须编辑”的项均已编辑完，“采购”模块初始化完成！
                                    </div>
                                    <button class="ty-btn ty-btn-big ty-circle-3 ty-btn-blue" onclick="initComplete()">确定</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                请完成以下各项中“必须编辑”的项，之后勾选右上方的选项并点击“确定”！
                            </div>
                            <table class="kj-table tbl_purchase_item_edit">
                                <thead>
                                <tr>
                                    <td>需编辑的项</td>
                                    <td>是否必须编辑</td>
                                    <td>状态</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>材料供应商清单</td>
                                    <td>是</td>
                                    <td>编辑尚未完成</td>
                                    <td><div class="link-blue" type="btn" name="edit" to="mtSupplierList">编辑</div></td>
                                </tr>
                                <tr>
                                    <td>设备供应商清单</td>
                                    <td>是</td>
                                    <td>编辑尚未完成</td>
                                    <td><div class="link-blue" type="btn" name="edit" to="equiptSupplierList">编辑</div></td>
                                </tr>
                                <tr>
                                    <td>其他原辅材料清单</td>
                                    <td>是</td>
                                    <td>编辑尚未完成</td>
                                    <td><div class="link-blue" type="btn" name="edit" to="RAAMtList">编辑</div></td>
                                </tr>
                                <tr>
                                    <td>确定原辅材料的定点信息</td>
                                    <td>是</td>
                                    <td>编辑尚未完成</td>
                                    <td><div class="link-blue" type="btn" name="edit" to="RAAMtPoint">编辑</div></td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="tips" style="margin-top: 8px">
                                <small class="ty-color-blue">注 上表中第3行是“其他原辅材料清单”，之所以为“其他”，在于尚有“构成商品的原辅材料”、“外购成品”及“商品的包装物”三种系统自带类别的材料，这三种材料系产品“拆分”后由系统生成，而非由采购录入。</small><br>
                                <small class="ty-color-blue">——销售商品且使用wonderss销售模块的机构使用wonderss系统时，初始化工作中，采购需在产品“拆分”之后。</small><br>
                                <small class="ty-color-blue">——不销售商品，或虽销售商品但不使用wonderss销售模块的机构，没有“拆分”的模块，无处录入这三种材料，采购的初始化可正常进行。</small>
                            </div>
                        </div>
                    </div>
                    <div class="pagePart" part="see">
                        <div class="panel-box">
                            <div class="panel-box_item">
                                “采购”模块的初始化已完成，完成时间：<b class="purchaseCompleteTime"></b>。
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="panel-box_item">
                                系统内已有的“采购”数据如下：
                            </div>
                            <div style="padding-left: 80px">
                                <div class="panel-box_item">
                                    <div class="left_item">——材料供应商清单</div>
                                    <div class="right_item"><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="seeListBtn('mtSupplierList')">查看</button></div>
                                </div>
                                <div class="panel-box_item">
                                    <div class="left_item">——设备供应商清单</div>
                                    <div class="right_item"><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="seeListBtn('equiptSupplierList')">查看</button></div>
                                </div>
                                <div class="panel-box_item">
                                    <div class="left_item">——原辅材料清单</div>
                                    <div class="right_item"><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="seeListBtn('RAAMtList_edit')">查看</button></div>
                                </div>
                                <div class="panel-box_item">
                                    <div class="left_item">——选定原辅材料的供应商</div>
                                    <div class="right_item"><button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="seeListBtn('pointedRAAMt')">查看</button></div>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="panel-box_item">
                                如发现上述数据有问题，可待初始化结束后再修改！
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page" page="mtSupplierList_edit">
                    <div class="pagePart" part="init">
                        <div class="panel-box_item">
                            <div class="left_item">请录入材料的供应商！</div>
                            <div>
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="editMtSupplier($(this), 'new')">录入</button>
                                <button class="ty-btn ty-btn-cyan ty-btn-big ty-circle-3" disabled>批量导入</button>
                            </div>
                        </div>
                        <div class="panel-box_item">
                            <div class="left_item">
                                <div class="ty-radio checkRadio">
                                    <input type="radio" id="mtSupplierInput_noNeed">
                                    <label for="mtSupplierInput_noNeed"></label> 我公司没有需录入到系统的材料供应商！
                                </div>
                            </div>
                            <div>
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="initComplete()">确定</button>
                            </div>
                        </div>
                    </div>
                    <div class="pagePart" part="edit">
                        <div class="topRightBtn">
                            <div class="ty-radio checkRadio">
                                <input type="radio" id="mtSupplierInput_complete">
                                <label for="mtSupplierInput_complete"></label> 材料的供应商已全部录完！
                            </div>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="initComplete(1)">确定</button>
                        </div>
                        <div class="panel-box_item">
                            <div class="left_item">
                                <div class="tipInit">您可继续录入材料的供应商，如已全部录完，请操作右上角的内容！</div>
                                <div class="tipDone">
                                    材料的供应商已编辑完毕，完成时间：<span class="doneTime ty-color-orange"></span>。<br>
                                    <small class="ty-color-blue">注 采购初始化尚未完成，故如需要，本页面内容仍可编辑。编辑后：<br>
                                        ——右上角的内容需重新操作，编辑内容方可生效；<br>
                                        ——编辑内容生效后，“原辅材料的定点信息”也需再次编辑。</small>
                                </div>
                            </div>
                            <div class="right_item">
                                <div>
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="editMtSupplier($(this), 'new')">录入</button>
                                    <button class="ty-btn ty-btn-cyan ty-btn-big ty-circle-3" disabled>批量导入</button>
                                </div>
                                <div class="ty-search">
                                    查找
                                    <input id="mtSupplierSearchInput" class="ty-searchInput" type="text" placeholder="请输入供应商的代号或名称" style="width: 200px"><button class="ty-searchBtn" onclick="searchMtSupplier()"></button>
                                </div>
                            </div>
                        </div>
                        <table class="kj-table kj-table-striped tbl_supplierList">
                            <thead>
                            <tr>
                                <td>供应商名称</td>
                                <td>供应商简称</td>
                                <td>供应商代号</td>
                                <td>创建</td>
                                <td>是否给开票</td>
                                <td>是否接收挂账</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_supplierList"></div>
                    </div>
                </div>
                <div class="page" page="mtSupplierSearch">
                    <div class="panel-box_item">
                        符合条件的数据共 <b class="ty-color-blue searchNum"></b> 条，具体如下：
                    </div>
                    <table class="kj-table kj-table-striped tbl_supplierSearchList">
                        <thead>
                        <tr>
                            <td>供应商名称</td>
                            <td>供应商简称</td>
                            <td>供应商代号</td>
                            <td>创建</td>
                            <td>是否给开票</td>
                            <td>是否接收挂账</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye_searchSupplier"></div>
                </div>
                <div class="page" page="equiptSupplierList_edit">
                    <div class="pagePart" part="init">
                        <div class="panel-box_item">
                            <div class="left_item"> 请录入设备的供应商！</div>
                            <div>
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="editMtSupplier($(this), 'new')">录入</button>
                                <button class="ty-btn ty-btn-cyan ty-btn-big ty-circle-3" disabled>批量导入</button>
                            </div>
                        </div>
                        <div class="panel-box_item">
                            <div class="left_item">
                                <div class="ty-radio checkRadio">
                                    <input type="radio" id="aa" name="">
                                    <label for="aa"></label> 我公司没有需录入到系统的设备供应商！！
                                </div>
                            </div>
                            <div>
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="initComplete()">确定</button>
                            </div>
                        </div>
                    </div>
                    <div class="pagePart" part="edit">
                        <div class="topRightBtn">
                            <div class="ty-radio checkRadio">
                                <input type="radio" id="equiptSupplierInputComplete">
                                <label for="equiptSupplierInputComplete"></label> 设备的供应商已全部录完！
                            </div>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="initComplete(2)">确定</button>
                        </div>
                        <div class="panel-box_item">
                            <div class="left_item">
                                <div class="tipInit">您可继续录入设备的供应商，如已全部录完，请操作右上角的内容！</div>
                                <div class="tipDone">
                                    设备的供应商已编辑完毕，完成时间：<span class="doneTime ty-color-orange"></span>。<br>
                                    <small class="ty-color-blue">注 采购初始化尚未完成，故如需要，本页面内容仍可编辑。编辑后，右上角的内容需重新操作，编辑内容方可生效。</small>
                                </div>
                            </div>
                            <div class="right_item">
                                <div>
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" type="equipt" onclick="editMtSupplier($(this), 'new')">录入</button>
                                    <button class="ty-btn ty-btn-cyan ty-btn-big ty-circle-3" disabled>批量导入</button>
                                </div>
                                <div class="ty-search">
                                    查找
                                    <input class="ty-searchInput" type="text" placeholder="请输入供应商的代号或名称" style="width: 200px"><button class="ty-searchBtn" onclick="searchMtSupplier()"></button>
                                </div>
                            </div>
                        </div>
                        <table class="kj-table kj-table-striped tbl_supplierList">
                            <thead>
                            <tr>
                                <td>供应商名称</td>
                                <td>供应商简称</td>
                                <td>供应商代号</td>
                                <td>创建</td>
                                <td>是否给开票</td>
                                <td>是否接收挂账</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_supplierList2"></div>
                    </div>
                </div>
                <div class="page" page="RAAMtList_edit" style="max-width: inherit">
                    <div class="seeHide">
                        <div class="topRightBtn">
                            <div class="ty-radio checkRadio">
                                <input type="radio" id="RAAMtInput_complete">
                                <label for="RAAMtInput_complete"></label> “其他原辅材料”已全部录入！
                            </div>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="initComplete()">确定</button>
                        </div>
                    </div>
                    <div class="panel-box_item seeHide">
                        <div class="left_item">
                            <div class="tipInit"></div>
                            <div class="tipDone">
                                “其他原辅材料”已全部录入！完成时间：<span class="doneTime ty-color-orange"></span>。<br>
                                <small class="ty-color-blue">注 采购初始化尚未完成，故如需要，本页面内容仍可编辑。编辑后：<br>
                                    ——右上角的内容需重新操作，编辑内容方可生效；<br>
                                    ——编辑内容生效后，“原辅材料的定点信息”也需再次编辑。</small>
                            </div>
                        </div>
                    </div>
                    <div class="rowTree_avatar mainCon1">
                        <div class="tree_avatar">
                            <div class="tree_header seeHide"><i class="fa fa-angle-down"></i> 录入需采购的材料 <span class="ty-right btnCat" onclick="des_inputMtBtn()">操作说明</span></div>
                            <ul class="treeList_avatar seeHide" style="flex: none">
                                <li><span class="link-blue" onclick="editRAAMt($(this), 'new')">录入“其他原辅材料”</span></li>
                            </ul>
                            <div class="tree_header"><i class="fa fa-angle-down"></i> 全部（<span class="mtNum"></span>种）<span class="ty-right btnCat addCat seeHide">新增分类</span></div>
                            <ul class="treeList_avatar catAll">
                            </ul>
                            <div class="tree_footer">
                                <div class="handleBtn">
                                    <span class="btnCat" onclick="backPreCat(); ">返回上一级</span>
                                    <span class="ty-right btnCat" onclick="backPreCat(1); ">返回全部材料</span>
                                </div>
                            </div>
                        </div>
                        <div class="hd" id="catTree"></div>
                        <div class="contentBox_avatar">
                            <div class="panel-box_item">
                                <div class="left_item">当前分类： <span class="curCatory curCat"></span></div>
                                <div class="right_item">
                                    <div></div>
                                    <div class="ty-search">
                                        查找
                                        <input class="ty-searchInput" type="text" placeholder="请输入原辅材料的代号或名称" style="width: 250px"><button class="ty-searchBtn" onclick="search($(this))"></button>
                                    </div>
                                </div>
                            </div>
                            <table class="kj-table kj-table-striped tbl_RAAMt">
                                <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>计量单位</td>
                                    <td>创建人</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div id="ye_mtList"></div>
                        </div>
                    </div>
                </div>
                <div class="page" page="RAAMtPoint_edit">
                    <div class="topRightBtn">
                        <div class="ty-radio checkRadio">
                            <input type="radio" id="RAAMtPoint_ok" name="RAAMtPoint_ok">
                            <label for="RAAMtPoint_ok"></label> 原辅材料的定点信息已全部编辑完成！
                        </div>
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="initComplete()">确定</button>
                    </div>
                    <div class="pagePart" part="edit">
                        <div class="panel-box_item">
                            <div class="left_item">定点信息有待确定的原辅材料数据共 <span class="confirmNum"></span> 条，具体如下：</div>
                            <div class="right_item">
                                <div></div>
                                <div>
                                    定点信息已确定的原辅材料数据共 <span class="determinedNum"></span> 条
                                    <button class="ty-btn ty-btn-cyan ty-btn-big ty-circle-3" onclick="seeDeterminedRAAMtList()">查看</button>
                                </div>
                            </div>
                        </div>
                        <table class="kj-table kj-table-striped tbl_undeterminedRAAMt">
                            <thead>
                            <tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>计量单位</td>
                                <td>创建</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_mtList1"></div>
                    </div>
                    <div class="pagePart" part="editDone">
                        <div class="panel-box">
                            <div class="panel-box_item">
                                <div class="mtPoint_last_editTime"></div>
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="panel-box-title">该次编辑之后</div>
                            <div class="panel-box_item display_mtList1">
                                尚有 <b class="edited_mtList1_num"></b> 条的材料定点信息有待确定。
                                <div class="right_btn_item"><span class="link-blue" type="btn" to="mtToHandle" onclick="edited_handle($(this))">去处理</span></div>
                            </div>
                            <div class="panel-box_item">
                                定点信息已确定的原辅材料共 <b class="edited_mtList4_num"></b> 条。目前，这些数据还可修改。
                                <div class="right_btn_item"><span class="link-blue" type="btn" to="mtHandled" onclick="edited_handle($(this))">修改</span></div>
                            </div>
                        </div>
                        <div class="panel-box display_mtOrSupplierChange">
                            <div class="panel-box-title">该次编辑之后</div>
                            <div class="panel-box_item display_newMt">
                                新增了 <b class="edited_newMt_num"></b> 条原辅材料，请确定这些材料的定点供应商。
                                <div class="right_btn_item"><span class="link-blue" type="btn" to="mtAddHandle" onclick="edited_handle($(this))">去处理</span></div>
                            </div>
                            <div class="panel-box_item display_changeMt">
                                已编辑数据中，基本信息发生了变动的原辅材料有 <b class="edited_changeMt_num"></b> 条，请对这些数据进行处理。
                                <div class="right_btn_item"><span class="link-blue" type="btn" to="mtChangeHandle" onclick="edited_handle($(this))">去处理</span></div>
                            </div>
                            <div class="panel-box_item display_newSupplier">
                                新增了 <b class="edited_newSupplier_num"></b> 个供应商，请确认哪些材料在这些供应商处定点采购。
                                <div class="right_btn_item"><span class="link-blue" type="btn" to="supplierAddHandle" onclick="edited_handle($(this))">去处理</span></div>
                            </div>
                            <div class="panel-box_item display_changeSupplier">
                                已编辑数据中，供应商信息发生了变动的有 <b class="edited_changeSupplier_num"></b> 条，请对这些数据进行处理。
                                <div class="right_btn_item"><span class="link-blue" type="btn" to="supplierChangeHandle" onclick="edited_handle($(this))">去处理</span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <%-- 定点信息已确定的原辅材料数据--%>
                <div class="page" page="pointedRAAMt">
                    <div class="topRightBtn">
                        <div class="ty-search">
                            查找
                            <input class="ty-searchInput" type="text" placeholder="请输入原辅材料的代号或名称" style="width: 250px"><button class="ty-searchBtn" onclick="searchPointedRAAMt($(this))"></button>
                        </div>
                    </div>
                    <div class="ty-alert">
                        定点信息已确定的原辅材料数据共如下 <span class="determinedNum"></span> 条，如需修改定点信息（增加或移除供应商），请点击“供应商”列下的蓝色数字！
                    </div>
                    <table class="kj-table tbl_determinedRAAMt">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建</td>
                            <td>供应商</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye_mtList4"></div>
                </div>
                <%-- 定点信息已确定的原辅材料数据 - 搜索页面--%>
                <div class="page" page="pointedRAAMt_search">
                    <div class="ty-alert">
                        查找代号或名称含“ <span class="searchKey"></span> ”的已确定的原辅材料
                    </div>
                    <table class="kj-table tbl_determinedRAAMt">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建</td>
                            <td>供应商</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye_mtSearch4"></div>
                </div>
                <%-- 确定定点信息 材料确定供应商--%>
                <div class="page" page="determinePointInfo" style="max-width: inherit">
                    <div class="topRightBtn">
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="mtChangeHandle($(this), 1)">编辑完成</button>
                    </div>
                    <table class="kj-table tbl_mtShow" style="max-width: 1000px">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>创建人</td>
                            <td>计量单位</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="needAddMtSupplier" style="margin-top: 36px">
                        <div class="ty-alert">
                            <span class="pointSupplierTip"></span>
                            <div class="btn-group">
                                <span class="link-blue" id="addPoint" onclick="editPointInfoBtn($(this), 'new', 'mt')">添加本材料的定点供应商</span>
                                <span class="link-blue" id="noFixedPurchaseBtn" onclick="noFixedPurchaseBtn($(this))" style="margin-left: 16px">本材料无需定点采购</span>
                            </div>
                        </div>
                        <table class="kj-table tbl_mtHandle">
                            <thead>
                            <tr>
                                <td>供应商</td>
                                <td>采购合同</td>
                                <td>单价</td>
                                <td>是否开票</td>
                                <td>创建人</td>
                                <td>采购周期</td>
                                <td>当前库存</td>
                                <td>最低库存</td>
                                <td>初始库存</td>
                                <td>占用库位</td>
                                <td class="handle">操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <%-- 确定定点信息 供应商确定材料--%>
                <div class="page" page="determinePointInfo_editMt" style="max-width: inherit">
                    <div class="topRightBtn">
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="supplierAddHandle($(this), 1)">编辑完成</button>
                    </div>
                    <table class="kj-table tbl_supplierShow" style="max-width: 1000px">
                        <thead>
                        <tr>
                            <td>供应商名称</td>
                            <td>供应商简称</td>
                            <td>供应商代号</td>
                            <td>是否开票</td>
                            <td>是否挂账</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="needAddMtSupplier" style="margin-top: 36px">
                        <div class="ty-alert">
                            <span class="pointSupplierTip">供应商供应的材料</span>
                            <div class="btn-group">
                                <span class="link-blue" onclick="editPointInfoBtn($(this), 'new', 'supplier')">添加本供应商供应的材料</span>
                            </div>
                        </div>
                        <table class="kj-table tbl_supplierHandle">
                            <thead>
                            <tr>
                                <td>材料名称</td>
                                <td>采购合同</td>
                                <td>单价</td>
                                <td>是否开票</td>
                                <td>创建人</td>
                                <td>采购周期</td>
                                <td>当前库存</td>
                                <td>最低库存</td>
                                <td>初始库存</td>
                                <td>占用库位</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="page" page="RAAMtPoint_mtToHandle">
                    <div class="panel-box_item">
                        <div class="left_item">定点信息有待确定的原辅材料数据共 <span class="confirmNum"></span> 条，具体如下：</div>
                    </div>
                    <table class="kj-table kj-table-striped tbl_edited_mtToHandle">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="RAAMtPoint_mtHandled">
                    <div class="panel-box_item">
                        定点信息已确定的原辅材料数据共如下<b class="confirmNum"></b> 条，如需修改定点信息（增加或移除供应商），请点击“供应商”列下的蓝色数字！
                    </div>
                    <table class="kj-table kj-table-striped tbl_edited_mtHandled">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建</td>
                            <td>供应商</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="RAAMtPoint_mtAddHandle">
                    <div class="panel-box_item">
                        <div class="left_item">定点信息有待确定的原辅材料数据共 <span class="confirmNum"></span> 条，具体如下：</div>
                        <div class="right_item">
                            <div></div>
                            <div>
                                定点信息已确定的原辅材料数据共 <span class="determinedNum"></span> 条
                                <button class="ty-btn ty-btn-cyan ty-btn-big ty-circle-3" onclick="seeDeterminedRAAMtList()">查看</button>
                            </div>
                        </div>
                    </div>
                    <table class="kj-table kj-table-striped  tbl_edited_mtAddHandle">
                        <thead>
                        <tr>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="RAAMtPoint_mtChangeHandle">
                    <div class="panel-box_item">
                        <div class="mtPoint_last_editTime"></div>
                    </div>
                    <div class="panel-box_item">
                        该次编辑之后，已编辑数据中，基本信息发生了变动的原辅材料有 <b class="changeNum"></b> 条，请对这些数据进行处理。
                    </div>
                    <table class="kj-table kj-table-striped tbl_edited_mtChangeHandle">
                        <thead>
                        <tr>
                            <td>状态</td>
                            <td>材料名称</td>
                            <td>材料代号</td>
                            <td>型号</td>
                            <td>规格</td>
                            <td>计量单位</td>
                            <td>创建/修改</td>
                            <td>对已编辑的定点信息的处理</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="RAAMtPoint_supplierAddHandle">
                    <div class="panel-box_item">
                        <div class="mtPoint_last_editTime"></div>
                    </div>
                    <div class="panel-box_item">
                        该次编辑之后，新增了 <b class="addNum"></b> 个供应商。请确定这些供应商是否有供应着的材料。
                    </div>
                    <table class="kj-table kj-table-striped tbl_edited_supplierAddHandle">
                        <thead>
                        <tr>
                            <td>供应商名称</td>
                            <td>供应商简称</td>
                            <td>供应商代号</td>
                            <td>创建</td>
                            <td>是否给开票</td>
                            <td>是否接收挂账</td>
                            <td>是否有供应着的材料</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="RAAMtPoint_supplierChangeHandle">
                    <div class="panel-box_item">
                        <div class="mtPoint_last_editTime"></div>
                    </div>
                    <div class="panel-box_item">
                        该次编辑之后，已编辑数据中，供应商信息发生了变动的有 <b class="changeNum"></b> 条，请对这些数据进行处理。
                    </div>
                    <table class="kj-table kj-table-striped tbl_edited_supplierChangeHandle">
                        <thead>
                        <tr>
                            <td>状态</td>
                            <td>供应商名称</td>
                            <td>供应商简称</td>
                            <td>供应商代号</td>
                            <td>创建/修改</td>
                            <td>是否给开票</td>
                            <td>是否接收挂账</td>
                            <td>已编辑的数据是否需处理</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="mtSupplierList">
                    <div class="ty-alert">已录入材料供应商<b class="count"></b>个</div>
                    <table class="kj-table kj-table-striped">
                        <thead>
                        <tr>
                            <td>供应商名称</td>
                            <td>供应商简称</td>
                            <td>供应商代号</td>
                            <td>创建</td>
                            <td>是否给开票</td>
                            <td>是否接收挂账</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="equiptSupplierList">
                    <div class="ty-alert">已录入设备供应商<b class="count"></b>个</div>
                    <table class="kj-table kj-table-striped">
                        <thead>
                        <tr>
                            <td>供应商名称</td>
                            <td>供应商简称</td>
                            <td>供应商代号</td>
                            <td>创建</td>
                            <td>是否给开票</td>
                            <td>是否接收挂账</td>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="page" page="importInfo" id="importEntryType">
                    <div class="importCon1">
                        <div class="topRightBtn importNoSave stepItem">
                            <button class="ty-btn ty-btn-orange ty-btn-big btn" data-name="clearNoSave" style="margin-right: 16px;">放弃</button>
                            <button class="ty-btn ty-btn-blue ty-btn-big btn" data-name="stepNext">下一步</button>
                        </div>
                        <p>
                            您共导入材料<span class="initAll"></span>条，其中以下<span class="initWrong"></span>条存在问题，<span class="red">无法保存至系统</span>。<br>
                            <small class="ty-color-blue">名称或代号未录入，本次导入材料的代号互相重复或与系统中已有代号相同等情况，计量单位未填写或计量单位被停用。均算作问题。</small>
                        </p>
                        <div class="gap-Tp">
                            <div id="tureMtList" style="display: none;"></div>
                            <table class="kj-table">
                                <thead>
                                <tr>
                                    <td>材料名称</td>
                                    <td>材料代号</td>
                                    <td>型号</td>
                                    <td>规格</td>
                                    <td>备注</td>
                                    <td>计量单位 <span onclick="addUnitCommom(3)" class="link-blue"> 新增 </span></td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="importCon2">
                        <div class="topRightBtn importing stepItem">
                            <button class="ty-btn ty-btn-red ty-btn-big ty-circle-3 btn" data-name="cancelSave" style= "margin-right: 16px;">放弃</button>
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 btn" id="save">保存</button>
                        </div>
                        <p>
                            您共导入材料<span class="initAll"></span>条，可保存至系统的共<span class="inabledSum"></span>条。 <br>
                            <small class="ty-color-blue">材料还需选定“计量单位”，之后才可保存至系统。</small>
                        </p>

                        <div class="line matCategory categorySelectList">
                            <span>材料类别<span class="red">*</span></span>
                            <select class="category kj-select categorySelectList" onchange="setCat($(this).val(), $(this))">
                                <option value="">   请选择</option>
                            </select>
                        </div>
                        <table class="kj-table">
                            <thead>
                            <tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>备注</td>
                                <td><span class="red">*</span>计量单位<span onclick="addImportUnit()" class="btnCat">新增</span></td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/orgInit/initCommon.js?v=SVN_REVISION"></script>
<%--材料录入部分--%>
<script src="../script/orgInit/purchase.js?v=SVN_REVISION"></script>
<script>
    // 初始化跳转到主页，初始化主页数据
    jumpPage("main", function () {
        initMain()
    })
</script>

</body>
</html>