<%--
  Created by IntelliJ IDEA.
  User: hxz
  Date: 2021/3/10
  Time: 8:56
--%>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%--<link rel="stylesheet" type="text/css" href="../css/general/recruit.css?v=SVN_REVISION" />--%>
<style>
    .select2-container {  z-index: 9999;}
    #create{
        display: block;
        font-size: 12px;
        text-align: right;
        margin-right: 11px;
        margin-top: 10px;
    }
    #qrCode{
        text-align: center;
        margin-top: 10px;
    }
    .offerFeedBack{
        display: none;
    }
    .us_field{display: inline-block;width:120px;}
    .qrCode{
        text-align: center;
        padding:16px;
        color: #999;
        background-color: #fff;
        font-size:16px;
    }
    #qrCode_small{
        width: 250px;
        height:230px;
        display: flex;
        justify-content:center;
        align-items:center;
    }
    .bonceCon input{
        width: 148px;
    }
    .recruit{
        width: 1030px;
        margin: auto;
    }
    .declare_avatar{
        padding: 32px 50px;
        background: #f0f0f0;
        display: flex;
        justify-content: space-between;
        color: #333;
    }
    .declare_avatar p{
        margin-bottom:20px;
    }
    .declare_avatar h4{
        display: inline-block;
        padding-left: 8px;
        font-size: 16px;
        color: #666;
        font-weight: bold;
        border-left: 4px solid #5d9cec;
        margin-left: -12px;
        margin-bottom: 16px;
        line-height:27px ;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <div class="bonceContainer bounce-green" id="createQRcode">
        <div class="bonceHead">
            <span>生成二维码</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <form autocomplete="off">
                <span id="createTip"></span><br/>
                <input type="text" class="form-control" id="endDate">
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" data-fun="createQRcodeOK">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="QR_code" style="width: 600px">
        <div class="bonceHead">
            <span>二维码</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="qrCode"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>二维码</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <div class="ty-mainData">
                    <div class="main">
                        <div class="clearfix">
                            <div class="tblContainer recruit">
                                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="createQRcode">生成二维码</span>
                                <div class="declare_avatar" style="margin-top:20px;">
                                    <div style=" margin-right: 15px">
                                        <h4>使用说明：</h4>
                                        <p>1. “暂无二维码”状态下，如需要，可点击“生成二维码”。二维码有有效期，所以生成时需确定有效期的到期日。</p>
                                        <p>2. 二维码可截图后供企业扫描。企业扫码后提交的数据可到“信息调查”中查看。</p>
                                        <p>3. 如需更换处于有效期内的二维码，可点击“生成二维码”，即可生成新的二维码。</p>

                                    </div>
                                    <div class="qrCode">
                                        <div id="validityDate" class="hd">有效期至XXXX年XX月XX日</div>
                                        <div id="qrCode_small">暂无二维码</div>
                                        <div id="create" style="display: none" class="hd">生成时间：XXXX-XX-XX XX:XX:XX</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../assets/global/plugins/jquery.qrcode.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/survey/QRcode.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
