<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/resourceCenter/wenjian.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/event/event.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/event/icon/iconfont.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<style>
    .checkWrap{margin:14px 10px;}
    .checkWrap div{}
    .checkWrap div span{margin-right:8px;}
    .selectBtn{background: #48cfad;color: #fff}
    .byAll,.byDepart{display:none;}
    .byDepart span{display: inline-block;}
    /*.byDepart ul li{width:100px;padding:4px 10px;background:#dfedff;}*/
    .selectCon{
        background:#dfedff;
        padding: 8px;
    }
    .selectCon .fixname{
        display: inline-block;
        width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: bottom;
    }
    .selectCon li{
        margin-left: 16px;
    }
    .selectCon .ty-checkbox{
        margin-left: 16px;
    }
    .levelAcon,.stuff{
        padding: 4px 8px;
        background: #d7e2f0;
        margin-top: 4px;
    }
    .selectCon i.fa{
        color: #66707b;
        text-align: center;
        margin-right: 2px;
    }
    .levelAcon + div{
        margin-left: 16px;
    }
    .ty-checkbox input + label {
        border: 1px solid #7f95bb;
    }
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%-- 一弹窗 --%>
<div class="bounce">
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align:center; font-size:16px; margin-top: 20px;"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMsg" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn">确定</span>
        </div>
    </div>

    <%--增加有领料权限的职工--%>
    <div class="bonceContainer bounce-green" id="addMaterialCollector" style="width:510px;">
        <div class="bonceHead">
            <span class="addRightTtl">增加有领料权限的职工</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="checkWrap">
                <span class="ty-btn ty-btn-big selectBtn allSelectBtn" status="0" onclick="byAllType($(this))">在全部职工中选择</span>
                <div class="selectCon byAll"></div>
            </div>
            <div class="checkWrap">
                <span class="ty-btn ty-btn-big selectBtn departSelectBtn" status="0" onclick="byDepartType($(this))">分部门查看并选择</span>
                <div class="selectCon byDepart"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-green ty-circle-2 addInputerSure" onclick="addInputerSure()">确定</button>
        </div>
    </div>
    <%-- 使用权限设置 --%>
    <div class="bonceContainer bounce-blue" id="scanSet">
        <div class="bonceHead">
            <span>能领的材料</span>
            <a class="bounce_close" onclick="cancelChangeRight()"></a>
        </div>
        <div class="bonceCon ">
            <input type="hidden" id="isNew">
            <p>领料者：<span class="pickerName"></span></p>
            <p class="">
                请选择材料<span style="margin-left:100px; " class="btnLink" data-type="allSelect">全选</span>
                <span class="ty-right txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    已选材料：<span class="selectedNum"></span>个
                    <span class="btnLink" data-type="allClear">全部清空</span>
                </span>
            </p>
            <div class="departTree ">
                <ul class="ty-left">
                    <p class="txtR"><span class="btnLink" id="changeTypeLeft" onclick="changeType($(this), 'left')">直接展示全部材料</span></p>
                    <div id="allRight"></div>
                </ul>
                <div class="ty-left arrow"><i class="fa fa-exchange"></i></div>
                <form class="ty-left" id="deparCon">
                    <input type="hidden" name="categoryId" id="categoryId">
                    <p class="txtR"><span class="btnLink" id="changeTypeRight" onclick="changeType($(this), 'right')">切换为按分类展示</span></p>
                    <ul id="nowRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
            <p>&nbsp;
                <span class="cancelCon">取消以下<span id="cancelNum"></span>个材料的领料权限。</span>
                <span class="ty-right getCon txtR" style="width:300px; display: inline-block; margin-right: 20px;">
                    获得以下<span id="getNum"></span>个材料的领料权限
                </span>
            </p>
            <div class="departTree changeCon">
                <ul class="ty-left cancelCon" style="height:150px;">
                    <div id="cancelRight"></div>
                </ul>
                <form class="ty-right getCon" style="margin-right:15px;height:150px;">
                    <ul id="getRight"></ul>
                </form>
                <div class="clr"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="cancelChangeRight()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeRight()" id="sureChangeRight">确定</button>
        </div>
    </div>
</div>
<%-- 二级弹窗 --%>
<div class="bounce_Fixed">
    <%--confirm--%>
    <div class="bonceContainer bounce-blue" id="fixed_confirm">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 iknowBtn">确定</button>
        </div>
    </div>
    <%--tip--%>
    <div class="bonceContainer bounce-blue" id="fixed_tip">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn">确定</button>
        </div>
    </div>
</div>
<%-- 三级弹窗 --%>
<div class="bounce_Fixed2">
    <%--confirm--%>
    <div class="bonceContainer bounce-blue" id="fixed2_confirm">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 iknowBtn">我知道了</button>
        </div>
    </div>
    <%--tip--%>
    <div class="bonceContainer bounce-blue" id="fixed2_tip">
        <div class="bonceHead">
            <span class="new_title">提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center text"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed2.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2 sureBtn">确定</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>领料分工</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content" >
            <!--放内容的地方-->
            <div class="ty-container" >
                <div id="picShow" style="display: none;">
                    <img src=""/>
                </div>
                <div class="ty-alert">
                    <div class="btn-group text-right">
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" id="picker" data-name="picker" onclick="addInputer($(this))">新增领料者</button>
                    </div>
                </div>
                <div>
                    <div class="materialCollector">
                        <table class="kj-table noSide list">
                            <thead>
                            <tr>
                                <th>姓名</th>
                                <th>手机号</th>
                                <th>部门</th>
                                <th>职位</th>
                                <th>分工的操作者</th>
                                <th>能领的材料</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div id="ye_materialCollector"></div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/materialDivision/materialDivision.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
