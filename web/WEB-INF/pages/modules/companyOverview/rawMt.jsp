<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%--<link href="../css/production/storage.css" rel="stylesheet" type="text/css" />--%>
<link href="../css/storage/rawMt.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce_Fixed">
    <%--签收查看-详情查看--%>
    <div class="bonceContainer bounce-blue" id="signDetailCheck" style="min-width:630px;">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td style="width: 40%">收货方实际的收货数量：</td>
                    <td><input type="text" class="actualDeliveryNum" style="width: 100%" disabled></td>
                </tr>
                <tr>
                    <td>情况记录</td>
                    <td><textarea cols="30" rows="5" class="memo" style="width: 100%" disabled></textarea></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel();">确定</span>
        </div>
    </div>
    <%--录入初始库存数量/库位--%>
    <div class="bonceContainer bounce-blue" id="enterInitialStock" style="min-width:900px;">
        <div class="bonceHead">
            <span>录入初始库存数量/库位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none bg-yellow" id="initialStockList">
                <thead>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>创建人</td>
                    <td>计量单位</td>
                    <td>初始库存</td>
                    <td>占用库位</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div>
                <div class="noSup">
                    <p class="gap" style="text-indent: 2em;">系统中本材料未录入定点供应商。</p>
                    <div class="selectSect">
                        <div class="clear checkCondition">
                            <div><span class="fa fa-square-o" id="toggleCheck" onclick="toggleCheck($(this))"></span>仅录入初始库存数量，暂不选择库位
                                <input type="hidden" id="toggleCheckNum"/></div>
                        </div>
                        <div>
                            <div class="toggleCheck1">
                                <span> 请录入该材料的初始库存数量</span> <input type="text" id="initKu0" onkeyup="clearNoNum(this)"><span class="goodsUnit"></span>
                            </div>
                            <div class="toggleCheck0">
                                <div class="supKuItem">
                                    <div>
                                        <div> </div>
                                        <div> <span class="addOneStore" id="addOneStore3" onclick="newStore($(this))">增加新库位</span> </div>
                                    </div>
                                    <div class="kuItem">
                                        <div>
                                            <span class="gapRt">库位</span><select onchange="fillStore($(this))" class="addInitStore"></select>
                                            <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                                        </div>
                                        <div>
                                            <span class="gapRt">数量</span> <input type="text" onkeyup="clearNoNum(this)"/><span class="goodsUnit"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hasSup">
                    <p class="gap" style="text-indent: 2em;">系统中本材料被录入 <span id="supNum"></span>个供应商。</p>
                    <div class="selectSect">
                        <div class="clear checkCondition">
                            <div><span class="fa fa-square-o" onclick="turnCheck($(this) , 1)"></span>仅录入初始库存数量，暂不选择库位</div>
                            <div><span class="fa fa-square-o" onclick="turnCheck($(this) , 2)"></span>录入初始库存总数，不区分是哪家供应商的</div>
                        </div>
                        <div>
                            <input type="hidden" id="option">
                            <ul class="kuList">
                                <li class="supKuItem">
                                    <div>
                                        <div> <p>供应商： SSSS</p> <p>简称： SSSS　 代号：XXXX</p>  </div>
                                        <div> <span class="addOneStore" onclick="newStore($(this))">增加新库位</span> </div>
                                    </div>
                                    <div class="kuItem">
                                        <div>
                                            <span class="gapRt">库位</span><select onchange="fillStore($(this))" class="addInitStore"></select>
                                            <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                                        </div>
                                        <div>
                                            <span class="gapRt">数量</span> <input type="text" onkeyup="clearNoNum(this)"/><span class="goodsUnit"></span>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                            <div class="Check1">
                                <span> 请录入该材料的初始库存数量</span> <input type="text" id="initKu" onkeyup="clearNoNum(this)"><span class="goodsUnit"></span>
                            </div>
                            <div class="Check2">
                                <div class="supKuItem">
                                    <div>
                                        <div> </div>
                                        <div> <span class="addOneStore" id="addOneStore2" onclick="newStore($(this))">增加新库位</span> </div>
                                    </div>
                                    <div class="kuItem">
                                        <div>
                                            <span class="gapRt">库位</span><select onchange="fillStore($(this))" class="addInitStore"></select>
                                            <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                                        </div>
                                        <div>
                                            <span class="gapRt">数量</span> <input type="text" onkeyup="clearNoNum(this)"/><span class="goodsUnit"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="addInitialStock()">确定</span>
        </div>
    </div>
    <%--重新选择库位--%>
    <div class="bonceContainer bounce-blue" id="reselectLocation" style="min-width:900px;">
        <div class="bonceHead">
            <span id="reselectTtl">重新选择库位</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none bg-yellow" id="reselectList">
                <thead>
                <tr>
                    <td>材料名称</td>
                    <td>材料代号</td>
                    <td>型号</td>
                    <td>规格</td>
                    <td>创建人</td>
                    <td>计量单位</td>
                    <td>当前库存</td>
                    <td>占用库位</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div>
                <div class="noChoose">
                    <p><span class="red">！提示</span>您需先选择库位，之后录入该库位上所放置该材料的数量</p>
                    <div><span class="fa fa-square-o" onclick="reSetFa($(this),1)"></span>重新选择库位，且不再区分是哪家供应商的 </div>
                </div>
                <div class="hasChoose">
                    <input type="hidden" id="reSetFa">
                    <p><span class="red">！提示</span>重新选择库位，意味着放弃本材料目前所占的库位！</p>
                    <div class="case2"><span class="fa fa-square-o" onclick="reSetFa($(this),0)"></span>放弃本材料所占库位，且本材料暂不再选择库位 </div>
                    <div class="case1"><span class="fa fa-square-o" onclick="reSetFa($(this),1)"></span>重新选择库位，且不再区分是哪家供应商的 </div>
                </div>
                <div class="storeList">
                    <div id="noStore" >
                    </div>
                    <div id="noSup">
                        <div class="supKuItem">
                            <div>
                                <div> </div>
                                <div> <span class="addOneStore" id="addOneStore4" onclick="newStore($(this))">增加新库位</span> </div>
                            </div>
                            <div class="kuItem">
                                <div>
                                    <span class="gapRt">库位</span><select onchange="fillStore($(this))" class="addInitStore"></select>
                                    <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>
                                </div>
                                <div>
                                    <span class="gapRt">数量</span> <input type="text" onkeyup="clearNoNum(this)"/><span class="goodsUnit"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="hasSup">

                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel();">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="updateLocation(2)">确定</span>
        </div>
    </div>
    <%-- 修改初始库存--%>
    <div class="bonceContainer bounce-blue" id="updateStores" style="min-width:400px;">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>请输入新的初始库存</p>
            <div>
                <input id="newStore" type="text" onkeyup="tofixed3(this)" />
                <i></i>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateStoresSure()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--操作--%>
    <div class="bonceContainer bounce-blue" id="storageAcceptHandle" style="width: 450px">
        <div class="bonceHead">
            <span>操作</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="radioBox" id="operationRadio">
                <div class="ty-radio numOk" value="0">
                    <i class="fa fa-circle-o"></i>
                    <span>数量无误，入库</span>
                </div>
                <div class="ty-radio numNo" value="1">
                    <i class="fa fa-circle-o"></i>
                    <span>数量不对，修改数量</span>
                </div>
                <input type="text" name="" class="judgmentQuantity" style="display: none">
                <div id="productNum" style="display: none">
                    <span>请录入您清点的商品数量：</span>
                    <input type="text" class="productNum" style="width: 200px;" onkeyup="clearNum0(this)">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureStorageAcceptHandle()" id="storageAcceptHandleBtn" disabled>提交</button>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="outStorageApply" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库申请</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageApplyBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="4" class="deliveryDate"></td>
                    <td>订单号</td>
                    <td class="sn">1</td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList" id="goodList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>计划出库数量 <span class="ty-color-red">*</span></td>
                    <td>货物件数</td>
                    <td>操作</td>
                </tr>
                <tbody></tbody>
            </table>
            <div class="applyAll"></div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--出库申请-待出库-查看(出库申请单)--%>
    <div class="bonceContainer bounce-blue" id="outStorageOrder" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库申请单</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageOrderBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="4" class="deliveryDate"></td>
                    <td>订单号</td>
                    <td class="sn"></td>
                </tr>
                </tbody>
            </table>
            <div class="countAll" style="margin-top: 8px"></div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>当前库存</td>
                    <td>计划出库数量</td>
                    <td>货物件数</td>
                </tr>
                <tbody></tbody>
            </table>
            <div class="applyAll">申请人：王建 2017-20-23</div>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--签收查看--%>
    <div class="bonceContainer bounce-blue" id="signCheck" style="min-width:1400px;">
        <div class="bonceHead">
            <span>原辅材料出库查看</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-none" id="signCheckBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td width="18%">订单号</td>
                    <td width="18%" class="sn">1</td>
                    <td width="10%">原始录入时间</td>
                    <td width="18%" colspan="2" class="create_date"></td>
                    <td width="10%">订单修改时间</td>
                    <td width="18%" class="update_date"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="2" class="deliveryDate"></td>
                    <td colspan="2">搬运负责人</td>
                    <td colspan="2" class="carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="2" class="arriveDate"></td>
                    <td colspan="2">计划的发货方式</td>
                    <td colspan="2" class="deliveryWay"></td>
                </tr>
                </tbody>
            </table>
            <p class="countAll" style="margin-top: 8px"></p>
            <div id="signInfoRecord">
                <div class="recordTitle">
                    情况记录
                    <div class="recordHeader ty-right">
                        <b>签收人：</b><span class="signer"></span><b>签收时间：</b><span class="signTime"></span><b>录入者：</b><span class="recorder"></span><b>录入时间：</b><span class="recordTime"></span>
                    </div>
                </div>

                <div class="recordCon">
                    <div class="signDetail"></div>
                </div>
            </div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>要求到货日期</td>
                    <td>出库数量</td>
                    <td>货物件数</td>
                    <td>仓库审批时间</td>
                    <td>复核时间</td>
                    <td>实际签收数量</td>
                    <td>录入时间</td>
                    <td>情况记录</td>
                </tr>
                <tbody></tbody>
            </table>
            <p class="applyAll"></p>
        </div>
        <div class="bonceFoot"></div>
    </div>

    <%--出库申请-已出库-发运信息--%>
    <div class="bonceContainer bounce-blue" id="logisticInfo" style="min-width:1200px">
        <div class="bonceHead">
            <span>发运信息</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <div class="transInfo" style="display: none;text-align: center">未获取到发运信息</div>
            <div class="transDetail">
                <table class="ty-table ty-table-none">
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">确定</span>
        </div>
    </div>
    <%--按类别查看原辅材料 - 初始库存--%>
    <div class="bonceContainer bounce-blue" id="initialStockSee" style="min-width:900px;">
        <div class="bonceHead">
            <span>初始库存</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <span class="noteTtl">当前初始库存</span>
                <span class="noteTtl" id="stockAmount">DWF-1001</span>
                <span class="ty-btn ty-btn-blue ty-btn-big" onclick="updateStores()">修改</span>
                <table class="ty-table ty-table-control" id="initialStockRecord">
                    <thead>
                    <tr>
                        <td>修改人</td>
                        <td>修改时间</td>
                        <td>客户名称</td>
                        <td>修改前的初始库存</td>
                        <td>修改后的初始库存</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--  初始库存 --%>
    <div class="bonceContainer bounce-blue" id="initStock" style="width:675px; ">
        <div class="bonceHead">
            <span>初始库存</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p><span>当前数据</span><span id="curNum"></span>
                <span class="ty-btn ty-btn-blue ty-circle-3 ty-right" onclick="initStockEditBtn()">修改</span>
            </p>
            <table class="ty-table ty-table-control"  >
                <thead>
                <tr>
                    <td>修改人</td>
                    <td>修改时间</td>
                    <td>供应商名称</td>
                    <td>修改前的初始库存</td>
                    <td>修改后的初始库存</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>修改人</td>
                    <td>修改时间</td>
                    <td>供应商名称</td>
                    <td>修改前的初始库存</td>
                    <td>修改后的初始库存</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">关闭</span>
        </div>
    </div>
    <%--占用库位查看--%>
    <div class="bonceContainer bounce-blue" id="holdStockSee" style="width:1000px;">
        <div class="bonceHead">
            <span>占用库位查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-none bg-yellow" id="holdStockInfo">
                    <thead>
                    <tr>
                        <td width="15%">材料名称</td>
                        <td width="15%">材料代号</td>
                        <td width="10%">型号</td>
                        <td width="10%">规格</td>
                        <td width="20%">创建人</td>
                        <td width="10%">计量单位</td>
                        <td width="10%">当前库存</td>
                        <td width="10%">占用库位</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div id="onlyNumInfo" >该材料未选择库位</div>
                <div id="currentStation" class="resetCurrentStation"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce.cancel();">取消</span>
<%--            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="holdStockSeeReset" onclick="reSelectStock($(this));">去选择库位</span>--%>
        </div>
    </div>
    <%--初始库存-修改记录--%>
    <div class="bonceContainer bounce-blue" id="initStockUpdateRecord" style="min-width:900px;">
        <div class="bonceHead">
            <span>初始库存-修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-control" id="initUpdateRecord">
                    <thead>
                    <tr>
                        <td>修改人</td>
                        <td>修改时间</td>
                        <td>供应商名称</td>
                        <td>修改前的初始库存</td>
                        <td>修改后的初始库存</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--最低库存-修改记录--%>
    <div class="bonceContainer bounce-blue" id="safeStockUpdateRecord" style="min-width:900px;">
        <div class="bonceHead">
            <span>最低库存-修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="initBody">
                <table class="ty-table ty-table-control" id="safeRecord">
                    <thead>
                    <tr>
                        <td>修改人</td>
                        <td>修改时间</td>
                        <td>客户名称</td>
                        <td>修改前的最低库存</td>
                        <td>修改后的最低库存</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">确定</span>
        </div>
    </div>
    <%-- 我知道了  --%>
    <div class="bonceContainer bounce-red" id="knowTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <div id="knowTipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 还有必填项尚未填写！  --%>
    <div class="bonceContainer bounce-red" id="nullTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <div id="nullTipMs">还有必填项尚未填写！</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 库位情况查看--%>
    <div class="bonceContainer bounce-blue" id="seeStores" style="min-width:835px;">
        <div class="bonceHead">
            <span>库位情况查看</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="seeStoresInfo">
                <span>库位</span>
                <span id="seeStoresName"></span>
            </div>
            <table class="ty-table ty-table-none  bg-yellow storeTb storesCreateInfo">
                <thead>
                <tr>
                    <td width="15%">所属库房</td>
                    <td width="20%">所属区域</td>
                    <td width="15%">所属货架</td>
                    <td width="10%">所属层数</td>
                    <td width="10%" class="hdNot"></td>
                    <td width="30%">创建人</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <p>该库位已放置的材料</p>
            <table class="ty-table ty-table-none  bg-yellow storeTb scienceCreateInfo">
                <thead>
                <tr>
                    <td width="20%">材料名称</td>
                    <td width="20%">材料代号</td>
                    <td width="15%">型号</td>
                    <td width="15%">规格</td>
                    <td width="15%">计量单位</td>
                    <td width="15%">数量</td>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel();">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container" onclick="scanCtrlHide()">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>原辅材料库</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer"  >
            <!--放内容的地方-->
            <div class="ty-container">
                <button type="button" class="btn btn-default stockJump" onclick="stockJump(0)">返回原辅材料库主页</button>
                <div class="pageStyle container_item">
                    <%--首页--%>
                    <div id="productMain" class="stockHandle" data-part="0">
                        <div class="fragment">
                            <div class="ty-left">
                                <span class="frCon">待录入初始库存数量的材料共<span   id="waitingFor"></span>种</span>
<%--                                <span class="ty-btn ty-btn-blue" onclick="stockJump(1)">去处理</span>--%>
                            </div>
                            <div class="ty-right partRt">
<%--                                <span class="ty-btn ty-btn-blue frCheck" onclick="takeInventory()">去盘点</span>--%>
<%--                                <span class="ty-btn ty-btn-blue" onclick="stockJump(2)">重新选择库位</span>--%>
                            </div>
                        </div>
                        <div class="fragment">
                            <div class="ty-left">
                               <%-- <span class="frCon">待处理的原辅材料入库申请共<span id="dairuku"></span>笔</span>
                                <span class="ty-btn ty-btn-blue" onclick="stockJump(4)">去处理</span>--%>
                            </div>
                            <div class="ty-right partRt">
                                <span class="frCheck">出入库统计</span>
                                <span class="ty-btn ty-btn-blue" onclick="stockJump(6)">查看</span>
                            </div>
                        </div>
                        <div class="fragment">
                            <div class="ty-left">
                               <%-- <span class="frCon">待处理的原辅材料出库申请共<span id="daichuku"></span>笔</span>
                                <span class="ty-btn ty-btn-blue" onclick="stockJump(5)">去处理</span>--%>
                            </div>
                            <div class="ty-right partRt">
                                <span class="frCheck">盘点记录</span>
                                <span class="ty-btn ty-btn-gray">查看</span>
                            </div>
                        </div>
                        <div class="warehouse">
                            <table class="pt-table" id="finishedWarehouse">
                                <tbody>
                                <tr>
                                    <td class="td-orange">
                                        <p>已设置原辅材料库</p>
                                        <p><span houseReq data-name="warehouse_count"></span>个</p>
                                    </td>
                                    <td class="td-lightOrange">库内资源</td>
                                    <td>
                                        <p>区域数量</p>
                                        <p>货架数量</p>
                                        <p>库位总数/空库位数</p>
                                    </td>
                                    <td>
                                        <p houseReq data-name="region_count"></p>
                                        <p houseReq data-name="shelf_count"></p>
                                        <p>
                                            <span houseReq data-name="location_count"></span> /
                                            <span houseReq data-name="location_count_empty"></span>
                                        </p>
                                    </td>
                                    <td class="td-lightOrange">库内货物概览</td>
                                    <td>
                                        <p>种类总数</p>
                                        <p>净重合计</p>
                                        <p>总重合计</p></td>
                                    <td>
                                        <p data-name="material_count">- -</p>
                                        <p>未知</p>
                                        <p>未知</p>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="clear">
                            <h5 class="ty-left">您除可点击某原辅材料库以查看原辅材料外，还可按类别查看原辅材料，或直接查找某种原辅材料。</h5>
                            <span class="ty-right ty-btn ty-btn-blue" onclick="stockJump(3)">按类别查看原辅材料</span>
                        </div>
                        <div class="clear searchSect">
                            <div class="ty-left keywordSearch">
                                <span>查找：</span>
                                <input placeholder="请输入原辅材料的代号或名称" id="searchKey" />
                                <i></i>
                            </div>
                            <span class="ty-left ty-btn ty-btn-blue" onclick="stockJump(7)">确定</span>
                        </div>
                        <div class="warehouse">
                            <div class="clear">
                                <table class="pt-table single" id="singleHouse">
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                        <%--录入初始库存数量/库位--%>
                    <div class="stockHandle" data-part="1" style="display: none">
                        <p class="ty-color-orange">如下<span id="waitingTotle"></span>种原辅材料的初始库存数量/库位有待您录入。</p>
                        <table class="ty-table ty-table-none bg-yellow" id="waitingForEntry">
                            <thead>
                            <tr>
                                <td>材料名称</td>
                                <td>材料代号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建人</td>
                                <td>计量单位</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <div id="ye4"></div>
                    </div>
                        <%-- 重新选择库位--%>
                    <div class="stockHandle" data-part="2" style="display: none">
                        <p class="ty-color-orange">请选择您要重新选择库位的原辅材料。</p>
                        <table class="ty-table ty-table-none bg-yellow" id="resetStock">
                            <thead>
                            <tr>
                                <td>材料名称</td>
                                <td>材料图号</td>
                                <td>型号</td>
                                <td>规格</td>
                                <td>创建人</td>
                                <td>计量单位</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <div id="ye2"></div>
                    </div>
                    <div id="byCategory" data-part="3" class="seemore" style="display: none"><%--按类别查看原辅材料--%>
                       <div id="scanCtrlCon">
                           <ul>
<%--                               <li disabled="true">盘点记录</li>--%>
<%--                               <li disabled="true">出入库记录</li>--%>
<%--                               <li data-type="initStock">初始库存修改记录</li>--%>
                               <li data-type="stockChangeLog">库存变动记录</li>
                               <li data-type="minStock">最低库存修改记录</li>
                           </ul>
                       </div>
                        <div class="mainCon mainCon1">
                            <div class="leCat">
                                <div>全部（<span class="mtNum">250</span>种）<%--<span class="ty-right btnCat addCat">新增分类</span>--%></div>
                                <ul id="catAll">
                                </ul>
                                <ul id="bottom1" class="bottom" onclick="goPause()">
                                    <li>暂停采购的材料  <span class="catNum stopNum">22</span></li>
                                </ul>
                                <div id="bottom2" class="bottom">
                                    <span class="btnCat" onclick="backPreCat(); ">返回上一级</span>
                                    <span class="btnCat" onclick="backPreCat(1); ">返回全部材料</span>
                                </div>
                            </div>
                            <div class="hd" id="catTree"></div>
                            <div class="riMt">
                                <div>
                                    <span>当前分类： <span class="curCatory curCat"></span></span>
                                    <span class="search">查找：
                                <input type="text" placeholder="请输入原辅材料的代号或名称"><span onclick="search($(this), 1)">确定</span>
                            </span>
                                </div>
                                <div class="lePad">
                                    <table class="ty-table ty-table-control"  >
                                        <thead>
                                        <tr>
                                            <td>材料名称</td>
                                            <td>材料代号</td>
                                            <td>型号</td>
                                            <td>规格</td>
                                            <td>创建人</td>
                                            <td>计量单位</td>
                                            <td>最低库存</td>
                                            <td>当前库存</td>
                                            <td>初始库存</td>
                                            <td>占用库位</td>
                                            <td>操作</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td>材料名称</td>
                                            <td>材料代号</td>
                                            <td>型号</td>
                                            <td>规格</td>
                                            <td>计量单位</td>
                                            <td>创建人</td>
                                            <td>
                                                <span data-type="mtScan" class="ty-color-blue ">查看</span>
                                                <span data-type="mtDel" class="ty-color-red ">删除</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div id="ye1"></div>
                                </div>

                            </div>
                        </div>
                        <div class="mainCon mainCon2">
                            <div class="leCat">
                                <div>暂停采购的材料
                                    <span class="catNumPause">22</span>
                                </div>
                                <div class="bottom">
                                    <span class="btnCat" onclick="toggleSuspend(1)">返回上一级</span>
                                    <span class="btnCat">返回全部材料</span>
                                </div>
                            </div>
                            <div class="riMt">
                                <div>
                                    <span>当前分类： <span class="curCatory curCat2"> 全部 > 暂停采购的材料</span></span>
                                    <span class="search">查找：<input type="text" placeholder="请输入原辅材料的代号或名称"><span onclick="search($(this), 2)">确定</span></span>
                                </div>
                                <div class="lePad">
                                    <table class="ty-table ty-table-control">
                                        <thead>
                                        <tr>
                                            <td>材料名称</td>
                                            <td>材料代号</td>
                                            <td>型号</td>
                                            <td>规格</td>
                                            <td>创建人</td>
                                            <td>计量单位</td>
                                            <td>最低库存</td>
                                            <td>当前库存</td>
                                            <td>初始库存</td>
                                            <td>占用库位</td>
                                            <td>操作</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td>材料名称</td>
                                            <td>材料代号</td>
                                            <td>型号</td>
                                            <td>规格</td>
                                            <td>计量单位</td>
                                            <td>创建人</td>
                                            <td>材料被暂停采购的时间</td>
                                            <td>
                                                <span class="ty-color-blue">查看</span>
                                                <span class="ty-color-red">删除</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div data-part="4" style="display: none">
                        <%--原辅材料入库申请--%>
                        <table class="ty-table ty-table-none bg-yellow" id="inStorage">
                            <thead>
                            <tr>
                                <td>序号</td>
                                <td>申请时间</td>
                                <td>商品代号</td>
                                <td>商品名称</td>
                                <td>产品图号</td>
                                <td>产品名称</td>
                                <td>单位</td>
                                <td>数量</td>
                                <td>生产日期</td>
                                <td>产品到期日</td>
                                <td>入库流程</td>
                                <td>审批</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div data-part="5" style="display: none">
                        <%--原辅材料出库申请--%>
                        <table class="ty-table ty-table-none bg-yellow" id="outStorage">
                            <thead>
                            <tr>
                                <td>计划出库日期</td>
                                <td>客户名称</td>
                                <td>收货地址</td>
                                <td>计划的发货方式</td>
                                <td>订单号</td>
                                <td>商品类别总数</td>
                                <td>货物总件数</td>
                                <td>搬运负责人</td>
                                <td>计划到达日期</td>
                                <td>申请提交时间</td>
                                <td>申请最后修改时间</td>
                                <td>提交者</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div data-part="6" style="display: none">
                        <div id="checkInOutInfo">
                            <ul class="ty-secondTab">
                                <li class="ty-active">入库流水</li>
                                <li>领料流水</li>
                            </ul>
                            <%--待入库--%>
                            <div class="ty-mainData">
                                <div class="p0">
                                    <%--原辅材料库——入库流水--%>
                                    <h3>原辅材料库——入库流水</h3>
                                    <table class="ty-table ty-table-none bg-yellow" id="inStorageInfo">
                                        <thead>
                                        <tr>
                                            <td>材料代号</td>
                                            <td>材料名称</td>
                                            <td>规格</td>
                                            <td>型号</td>
                                            <td>计量单位</td>
                                            <td>入库数量</td>
                                            <td>供应商</td>
                                            <td>仓库确认时间</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div id="pageP0"></div>
                                </div>
                                <div class="p1">
                                    <%--原辅材料库——领料流水--%>
                                    <h3>原辅材料库——领料流水</h3>
                                    <table class="ty-table ty-table-none bg-yellow" id="outStorageInfo" >
                                        <thead>
                                        <tr>
                                            <td>材料代号</td>
                                            <td>材料名称</td>
                                            <td>规格</td>
                                            <td>型号</td>
                                            <td>计量单位</td>
                                            <td>领料数量</td>
                                            <td>领料部门</td>
                                            <td>仓库确认时间</td>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                     <div id="pageP1"></div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="stockScreen seemore" data-part="7" style="display: none"><%--要查找原辅材料的图号或名称--%>
                        <table class="ty-table ty-table-none bg-yellow" id="resultList">
                            <thead>
                            <td width="10%">材料名称</td>
                            <td width="10%">材料图号</td>
                            <td width="8%">型号</td>
                            <td width="8%">规格</td>
                            <td width="16%">创建人</td>
                            <td width="8%">计量单位</td>
                            <td width="8%">最低库存</td>
                            <td width="8%">当前库存</td>
                            <td width="8%">初始库存</td>
                            <td width="8%">占用库位</td>
                            <td width="8%">操作</td>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <div id="ye3"></div>
                    </div>
                    <div class="stockChange">
                        <div>
                            <table style="width: 100%; line-height: 30px;">
                                <tr>
                                    <td>
                                        <span class="mtInfo"></span>
                                    </td>
                                    <td>
                                        <span class="curSto"></span>
                                    </td>
                                    <td>
                                        <span class="unit"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="create"></span>
                                    </td>
                                    <td>
                                        <span class="mtWeightUnit"></span>
                                    </td>
                                    <td>
                                        <span class="mtWeightAll"></span>
                                    </td>
                                </tr>
                            </table>
                            <table class="ty-table ty-table-none bg-yellow" id="ee2">
                                <thead>
                                <tr>
                                    <td>发生日期</td>
                                    <td>事件</td>
                                    <td>对库存的影响</td>
                                    <td>事件发生后的库存</td>
                                    <td>备注</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody id="socketchangeTB">
                                <tr>
                                    <td>发生日期</td>
                                    <td>事件</td>
                                    <td>对库存的影响</td>
                                    <td>事件发生后的库存</td>
                                    <td>备注</td>
                                    <td><span class="ty-color-blue">查看</span></td>
                                </tr>
                                </tbody>
                            </table>
                            <div id="ye43"></div>

                        </div>
                    </div>
                </div>
                <div id="ye_accept"></div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/companyOverview/rawMt.js?v=SVN_REVISION" type="text/javascript"></script>

<%@ include  file="../../common/footerBottom.jsp"%>


</body>
</html>
