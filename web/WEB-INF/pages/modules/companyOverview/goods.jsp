<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/commodity/commodityCommon.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/commodity/basic.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%--商品录入--%>
    <div class="bonceContainer bounce-blue" id="addCommodity" style="width:984px;">
        <div class="bonceHead bounce-blue">
            <span></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modInput">
                <div class="modTip">
                    <p>您正在向“<span class="ty-color-red"><span id="categoryType"></span>-待分类</span>”下录入商品！</p>
                    <p class="ty-color-blue sold" style="font-size: 12px;">注：已销售过的商品录入后，系统将提示成品库的库管员<span class="ty-color-red">填写初始库存。</span></p>
                    <p class="ty-color-blue noSold" style="font-size: 12px;">注：未销售过的商品录入后，其<span class="ty-color-red">初始库存默认为零！</span></p>
                </div>
                <div class="modInfo clear">
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            <i class="red">*</i>商品代号
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="outerSn" require/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            <i class="red">*</i>商品名称
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入"name="outerName" require/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                    <div class="modItem-m">
                        <div class="ty-left">
                            <p class="modItemTtl">
                                规格
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="specifications"/>
                        </div>
                        <div class="ty-right">
                            <p class="modItemTtl">
                                型号
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="model"/>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <div class="ty-left">
                            <p class="modItemTtl">
                                <i class="red">*</i>计量单位
                                <span class="nodeBtn ty-right" data-fun="addUnit">新增</span>
                            </p>
                            <select class="ty-inputSelect" type="text" id="unitSelect" name="unitId" require onchange="unitAssign($(this))"></select>
                            <input type="hidden" name="unit" id="add_unitName">
                        </div>
                        <div class="ty-right">
                            <p class="modItemTtl">
                                <i class="red">*</i>最低库存
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="minimumStock" onkeyup="testNumSize3(this)" require/>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            商品照片(可上传不超过9张)
                            <span class="nodeBtn ty-right" id="filePic">上传</span>
                        </p>
                        <div class="modPics">
                            <div class="file-box filePicBox clear"><span class="inTip">请上传</span></div>
                            <div class="clearPicsBtn" onclick="clearPicsBtn($(this))">清除</div>
                        </div>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            商品视频(可上传一个，且不可超过15秒)
                            <span class="nodeBtn ty-right" id="fileVedio">上传</span>
                        </p>
                        <div class="file-box fileVedioBox clear"><span class="inTip">请上传</span></div>
                    </div>
                    <div class="modItem-l">
                        <p class="modItemTtl">
                            商品说明<span class="lenTip ty-right">0/100</span>
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="memo" onkeyup="limitWord($(this), 100)"/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                </div>
                <hr/>
                <div class="modInfo clear">
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            此商品是否包含于与客户已签订的合同中
                        </p>
                        <select class="ty-inputSelect" onchange="contractSelect($(this))">
                            <option value="">请选择</option>
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            本商品在哪个合同中<button class="nodeBtn ty-right" data-fun="newContract">新增合同</button>
                        </p>
                        <select class="ty-inputSelect contractList" name="contractId" disabled="disabled">
                            <option>请选择</option>
                        </select>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            该商品需开何种发票
                        </p>
                        <select class="ty-inputSelect" name="invoiceCategory" onchange="invoiceTypeSelect($(this))">
                        </select>
                    </div>
                    <div class="modItem-m priceType invoice1">
                        <div class="modItem-ss ty-left">
                            <p class="modItemTtl">
                                <i class="red">*</i>税率
                            </p>
                            <select class="ty-inputSelect" id="addInvoiceSpecial" name="taxRate" require>
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <div class="modItem-s ty-left">
                            <p class="modItemTtl">
                                <i class="red">*</i>不含税单价
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNotax" onkeyup="clearNoNum(this)" require/>
                        </div>
                        <div class="modItem-s ty-right">
                            <p class="modItemTtl">
                                <i class="red">*</i>含税单价
                            </p>
                            <input class="ty-inputText" value="" name="unitPrice" placeholder="请录入" onkeyup="clearNoNum(this)" require/>
                        </div>
                    </div>
                    <div class="modItem-m priceType invoice2">
                        <p class="modItemTtl">
                            <i class="red">*</i>开普通发票时的开票单价
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceInvoice" onkeyup="clearNoNum(this)" require/>
                    </div>
                    <div class="modItem-m priceType invoice4">
                        <p class="modItemTtl">
                            <i class="red">*</i>不开发票时的单价
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNoinvoice" onkeyup="clearNoNum(this)" require/>
                    </div>
                    <div class="modItem-l">
                        <p class="modItemTtl">
                            价格说明<span class="lenTip ty-right">0/100</span>
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="priceDesc" onkeyup="limitWord($(this), 100)"/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addZsCommoditySure()">确定</span>
        </div>
    </div>
    <%--商品查看--%>
    <div class="bonceContainer bounce-blue" id="seeCommodity" style="width: 900px;">
        <div class="bonceHead bounce-blue">
            <span>查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="add-block ty-color-orange" id="stopDetails">
                <span id="stopName">XXX</span>已于<span id="stopDate">XXXX-XX-XX XX:XX:XX</span>将本商品“暂停销售”！</span>
            </div>
            <div class="main-nav">
                <span>基本信息</span>
                <div class="ty-right">
                    <span class="nodeBtn editBtn" onclick="updateCommodityBase(2)">修改</span>
                    <span class="nodeBtn"  data-fun="baseRecord">修改记录</span>
                </div>
            </div>
            <div>
                <table  class="ty-table" id="commodityBase">
                    <tbody>
                    <tr>
                        <td>商品代号</td>
                        <td colspan="3" need data-name="outerSn"></td>
                    </tr>
                    <tr>
                        <td width="30%">商品名称</td>
                        <td colspan="3" need data-name="outerName"></td>
                    </tr>
                    <tr>
                        <td width="12%">规格</td>
                        <td need data-name="specifications"></td>
                        <td width="12%">型号</td>
                        <td need data-name="model"></td>
                    </tr>
                    <tr>
                        <td width="12%">计量单位</td>
                        <td need data-name="unit"></td>
                        <td width="12%">最低库存</td>
                        <td need data-name="minimumStock"></td>
                    </tr>
                    <tr>
                        <td>商品照片</td>
                        <td colspan="3" class="comPics"></td>
                    </tr>
                    <tr>
                        <td>商品视频</td>
                        <td colspan="3" class="comVideo"></td>
                    </tr>
                    <tr>
                        <td>商品说明<span class="lenTip"></span></td>
                        <td colspan="3" need data-name="memo">
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="elemFlex">
                <span class="ttl">其他信息</span>
                <div>
                    <span class="nodeBtn editBtn" onclick="updateOtherData()">修改</span>
                    <span class="nodeBtn">修改记录</span>
                </div>
            </div>
            <div>
                <table  class="ty-table" id="seeInvoice2">
                    <tbody>
                    <tr>
                        <td>关联的产品</td>
                        <td>
                            <div class="ty-left"><span need data-name="innerSn"></span>&nbsp;&nbsp;<span need data-name="productName"></span></div>
                            <div class="ty-right nodeBtn">关联记录</div>
                        </td>
                    </tr>
                    <tr>
                        <td>所属的合同</td>
                        <td>
                            <div class="ty-left" need data-name="contractNumber"></div>
                            <div class="ty-right nodeBtn" id="contractScan" data-fun="contractScan">查看</div>
                        </td>
                    </tr>
                    <tr>
                        <td>开票元素</td>
                        <td data-name="invoiceCategory">

                        </td>
                    </tr>
                    <tr>
                        <td>价格元素</td>
                        <td data-name="price">
                        </td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td need data-name="priceDesc"></td>
                    </tr>
                    </tbody>
                </table>
                <table  class="ty-table" id="seeInvoice1">
                    <tbody>
                    <tr>
                        <td>关联的产品</td>
                        <td>
                            <div class="ty-left"><span need data-name="innerSn"></span>&nbsp;&nbsp;<span need data-name="productName"></span></div>
                            <div class="ty-right nodeBtn">关联记录</div>
                        </td>
                    </tr>
                    <tr>
                        <td>开增值税专用发票时</td>
                        <td data-name="specialDes">
                        </td>
                    </tr>
                    <tr>
                        <td>开普通发票时</td>
                        <td data-name="generalDes">

                        </td>
                    </tr>
                    <tr>
                        <td>不开发票时</td>
                        <td data-name="noDes">
                        </td>
                    </tr>
                    <tr>
                        <td>参考单价</td>
                        <td data-name="referenceDes">
                        </td>
                    </tr>
                    <tr>
                        <td>价格说明</td>
                        <td need data-name="priceDesc"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="clear">
                <div class="operations">
                    <div><span class="oper">创建</span><span id="seeCreator"></span></div>
                    <div class="reLog">
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
    <%-- ！提示 -暂停销售 --%>
    <div class="bonceContainer bounce-red" id="pauseSales">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="pauseSalesTip">
            </div>
        </div>
        <div class="bonceFoot elemFlexSpace">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="suspendSaleSure()">确定</span>
        </div>
    </div>
    <%-- ！提示 -删除 --%>
    <div class="bonceContainer bounce-red" id="deleteCommodity">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="deleteTip" style="text-align: center;">
                系统不支持“删除”有关联、包装或订购信息的商
                品，而此商品在系统中有关联、包装或订购信息！</div>
        </div>
        <div class="bonceFoot">
            <span class="delRefuse ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">我知道了</span>
            <span class="deleteCan ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="deleteCan ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="deleteCommoditySure()">确定</span>
        </div>
    </div>
    <%-- 当前订购信息 --%>
    <div class="bonceContainer bounce-red" id="curOrders">
        <div class="bonceHead">
            <span id="curTtl">！ 重要提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <tr>
                    <td width="10%">序号</td>
                    <td width="15%">订单号</td>
                    <td width="15%">负责人</td>
                    <td width="15%">未发货数量</td>
                    <td width="15%">要求到货日期</td>
                    <td width="30%">客户名称</td>
                </tr>
                <tr>
                    <td>序号</td>
                    <td>订单号</td>
                    <td>负责人</td>
                    <td>未发货数量</td>
                    <td>要求到货日期</td>
                    <td>客户名称</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="errorTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="tipWord" style="text-align: center; padding:10px 0"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--修改商品基本信息--%>
    <div class="bonceContainer bounce-blue" id="editCommodityBase" style="width: 970px;">
        <div class="bonceHead">
            <span>修改商品的基本信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modInfo clear" id="updateCommodityBase">
                <div class="modItem-m">
                    <p class="modItemTtl">
                        <i class="red">*</i>商品代号
                    </p>
                    <input class="ty-inputText" value="" placeholder="请录入" name="outerSn" need require/>
                    <i class="fa fa-times clearInputVal"></i>
                </div>
                <div class="modItem-m">
                    <p class="modItemTtl">
                        <i class="red">*</i>商品名称
                    </p>
                    <input class="ty-inputText" value="" placeholder="请录入"name="outerName" need require/>
                    <i class="fa fa-times clearInputVal"></i>
                </div>
                <div class="modItem-m">
                    <div class="ty-left">
                        <p class="modItemTtl">
                            规格
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="specifications" need/>
                    </div>
                    <div class="ty-right">
                        <p class="modItemTtl">
                            型号
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="model" need/>
                    </div>
                </div>
                <div class="modItem-m">
                    <div class="ty-left">
                        <p class="modItemTtl">
                            <i class="red">*</i>计量单位
                            <span class="nodeBtn ty-right" data-fun="addUnit">新增</span>
                        </p>
                        <select class="ty-inputSelect" type="text" id="update_unitSelect" name="unitId" need require onchange="unitAssign($(this))"></select>
                        <input type="hidden" name="unit" id="update_unitName" need>
                    </div>
                    <div class="ty-right">
                        <p class="modItemTtl">
                            <i class="red">*</i>最低库存
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="minimumStock" onkeyup="testNumSize3(this)" need require/>
                    </div>
                </div>
                <div class="modItem-m">
                    <p class="modItemTtl">
                        商品照片(可上传不超过9张)
                        <span class="nodeBtn ty-right" id="filePic_edit">上传</span>
                    </p>
                    <div class="modPics">
                        <div class="file-box filePicBox clear"><span class="inTip">请上传</span></div>
                        <div class="clearPicsBtn" onclick="clearPicsBtn($(this))">清除</div>
                    </div>
                </div>
                <div class="modItem-m">
                    <p class="modItemTtl">
                        商品视频(可上传一个，且不可超过15秒)
                        <span class="nodeBtn ty-right" data-fun="editUploadVedio" id="fileVedio_edit">上传</span>
                    </p>
                    <div class="file-box fileVedioBox clear"><span class="inTip">请上传</span></div>
                </div>
                <div class="modItem-l">
                    <p class="modItemTtl">
                        商品说明<span class="lenTip ty-right">0/100</span>
                    </p>
                    <input class="ty-inputText" value="" placeholder="请录入" name="memo" need onkeyup="limitWord($(this), 100)"/>
                    <i class="fa fa-times clearInputVal"></i>
                </div>
            </div>
            <div style="margin-left: 32px;">
                <div class="ty-color-orange"><span class="gapSm">注1</span>此修改对已存在于系统中的订单无法生效！</div>
                <div class="main-nav">
                    <span class="ttl">系统中包含本商品的未完结订单共<span id="orderUnNum"></span>个</span>
                    <span class="ty-btn ty-btn-gray">去查看</span>
                </div>
                <p class="ty-color-orange"><span class="gapSm">注2</span>订单未完结时如需修改商品信息，可先终止订单，修改商品信息后再录入新订单。</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateCommodityBaseSure()">确定</span>
        </div>
    </div>
    <%--修改商品其他信息--%>
    <div class="bonceContainer bounce-blue" id="editCommodityOther" style="width: 980px;">
        <div class="bonceHead bounce-blue">
            <span>修改商品的其他信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="editOtherInvoice">
                <div class="modInfo clear">
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            商品代号
                        </p>
                        <div name="outerSn" need class="gray"></div>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            商品名称
                        </p>
                        <div name="outerName" need class="gray"></div>
                    </div>
                </div>
                <hr/>
                <div class="modInfo clear">
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            此商品是否包含于与客户已签订的合同中
                        </p>
                        <select class="ty-inputSelect" name="contractType" onchange="contractSelect($(this))">
                            <option value="">请选择</option>
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            本商品在哪个合同中<button class="nodeBtn ty-right" data-fun="newContract">新增合同</button>
                        </p>
                        <select class="ty-inputSelect edit_contractList" name="contractId" disabled="disabled" need>
                            <option>请选择</option>
                        </select>
                    </div>
                    <div class="modItem-m">
                        <p class="modItemTtl">
                            该商品需开何种发票
                        </p>
                        <select class="ty-inputSelect" name="invoiceCategory" onchange="invoiceTypeSelect($(this))" need>
                        </select>
                    </div>
                    <div class="modItem-m priceType invoice1">
                        <div class="modItem-ss ty-left">
                            <p class="modItemTtl">
                                <i class="red">*</i>税率
                            </p>
                            <select class="ty-inputSelect" id="updateInvoiceSpecial" name="taxRate" need require>
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <div class="modItem-s ty-left">
                            <p class="modItemTtl">
                                <i class="red">*</i>不含税单价
                            </p>
                            <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNotax" onkeyup="clearNoNum(this)" need require/>
                        </div>
                        <div class="modItem-s ty-right">
                            <p class="modItemTtl">
                                <i class="red">*</i>含税单价
                            </p>
                            <input class="ty-inputText" value="" name="unitPrice" placeholder="请录入" onkeyup="clearNoNum(this)" need require/>
                        </div>
                    </div>
                    <div class="modItem-m priceType invoice2">
                        <p class="modItemTtl">
                            <i class="red">*</i>开普通发票时的开票单价
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceInvoice" onkeyup="clearNoNum(this)" need require/>
                    </div>
                    <div class="modItem-m priceType invoice4">
                        <p class="modItemTtl">
                            <i class="red">*</i>不开发票时的单价
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="unitPriceNoinvoice" onkeyup="clearNoNum(this)" need require/>
                    </div>
                    <div class="modItem-l">
                        <p class="modItemTtl">
                            价格说明<span class="lenTip ty-right">0/100</span>
                        </p>
                        <input class="ty-inputText" value="" placeholder="请录入" name="priceDesc" onkeyup="limitWord($(this), 100)" need/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                </div>
            </div>
            <div style="margin-left: 32px">
                <div class="ty-color-orange main-nav"><span class="gapSm">注1</span>此修改对已存在于系统中的订单无法生效！</div>
                <div>
                    <span class="ttl">系统中包含本商品的未完结订单共<span id="orderUnNumOther"></span>个</span>
                    <span class="ty-btn ty-btn-gray">去查看</span>
                </div>
                <div class="main-nav ty-color-orange"><span class="gapSm">注2</span>订单未完结时如需修改商品信息，可先终止订单，修改商品信息后再录入新订单。</div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="updateOtherSure()">确定</span>
        </div>
    </div>
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue" id="commEditLog" style="width: 620px;">
        <div class="bonceHead">
            <span>修改记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="padding:20px;">
                <p class="curSta"></p>
                <table class="ty-table">
                    <tr>
                        <td>记  录</td>
                        <td>操  作</td>
                        <td>创建者/修改者</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%-- 查看合同 --%>
    <div class="bonceContainer bounce-blue" id="scaneditContract" style="width: 620px;">
        <div class="bonceHead">
            <span>查看合同</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="padding:20px;">
                <p>本版本合同的创建 XXX XXXX-XX-XX XX:XX:XX</p>
                <table class="ty-table">
                    <tr>
                        <td>合同编号</td>
                        <td>操  作</td>
                    </tr>
                    <tr>
                        <td>合同的有效期</td>
                        <td>操  作</td>
                    </tr>
                    <tr>
                        <td>合同的扫描件或照片</td>
                        <td>操  作</td>
                    </tr>
                    <tr>
                        <td>合同的可编辑版</td>
                        <td>操  作</td>
                    </tr>
                    <tr>
                        <td>本合同下的商品（共XX种）</td>
                        <td>操  作</td>
                    </tr>
                    <tr>
                        <td>备注</td>
                        <td>操  作</td>
                    </tr>
                    <tr>
                        <td>本版本合同的修改记录</td>
                        <td>操  作</td>
                    </tr>
                    <tr>
                        <td>本合同的续约记录</td>
                        <td>操  作</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <%-- 新增计量单位失败tip  --%>
    <div class="bonceContainer bounce-red" id="tip1">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center;" >

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">我知道了</span>
        </div>
    </div>
    <%-- 新增计量单位  --%>
    <div class="bonceContainer bounce-green" id="addUnit">
        <div class="bonceHead">
            <span>新增计量单位</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p class="ty-center">计量单位</p>
            <input type="hidden" id="updateType">
            <p class="ty-center"><input type="text" placeholder=" 请录入计量单位的名称" id="unitName"></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel(); ">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="addUnitOk(2)">确定</span>
        </div>
    </div>

    <%-- 提示 --%>
    <div class="bonceContainer bounce-red" id="unfilledTip">
        <div class="bonceHead">
            <span>!提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p id="unfilledTip_ms">还有必填项尚未填写！ </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">我知道了</span>
        </div>
    </div>
    <%--新增 合同--%>
    <div class="bonceContainer bounce-green" id="newContractInfo" style="width: 450px">
        <div class="bonceHead">
            <span>新增合同</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="flex-box">
                <div class="citem">
                    <p><i class="red">*</i>合同编号</p>
                    <input type="text" placeholder="请录入" class="cNo">
                </div>
                <div class="citem">
                    <p>签署日期</p>
                    <input type="text" placeholder="请选择" readonly class="cSignDate">
                </div>
            </div>
            <div class="flex-box">
                <div class="citem">
                    <p>合同的有效期</p>
                    <input type="text" placeholder="请选择" readonly class="cStartDate">
                </div>
                <div class="citem">
                    <p>&nbsp;</p>
                    <span>至</span>
                </div>
                <div class="citem">
                    <p>&nbsp;</p>
                    <input type="text" placeholder="请选择" readonly class="cEndDate">
                </div>
            </div>
            <div class="citem2">
                <p>合同的扫描件或照片(共可上传9张) <span class="linkBtn ty-right" id="cUpload1"></span></p>
                <div class="fileCon">
                    <div class="fileCon1"></div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div class="citem2">
                <p>合同的可编辑版 <span class="linkBtn ty-right" id="cUpload2"></span></p>
                <div class="fileCon ">
                    <div class="fileCon2"></div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div class="citem2 cGS">
                <p>本合同下的商品 <span data-fun="scanGs" class="linkBtn scanGs">XXX</span> 种
                    <span class="linkBtn ty-right" data-fun="addGs">添加商品</span>
                    <span class="linkBtn ty-right" style="position: relative;left: 20px;" data-fun="removeGs">移出商品</span>
                </p>
                <div class="fileCon" style=" background: #eee;">
                    <div class="goodList" style="    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        <span class="gsIm"></span>
                    </div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div class="citem2">
                <p>备注</p>
                <input class="cMemo" type="text" style="width:420px; " placeholder="请录入">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="editContractOk" onclick="editContractOk()">提交</button>
        </div>
    </div>
    <%-- 合同查看 --%>
    <div class="bonceContainer bounce-blue" id="cScan" style="width: 874px; ">
        <div class="bonceHead">
            <span>查看合同</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="width: 80%; margin:0 auto; ">
            <p>本版本合同的创建 <span class="create"></span></p>
            <table class="ty-table ty-table-control leftTab">
                <tr><td>合同编号</td><td class="cNos"></td></tr>
                <tr><td>签署日期</td><td class="cSignDates"></td></tr>
                <tr><td>合同的有效期</td><td class="cvalidDates"></td></tr>
                <tr><td>合同的扫描件或照片</td><td class="cImgs"></td></tr>
                <tr><td>合同的可编辑版</td><td class="cWord">
                </td></tr>
                <tr><td>本合同下的商品（共<span class="gNum"></span>种）</td><td class="cGoodss">
                    <span class="ty-color-blue node" data-fun="gNum">查看</span>
                </td></tr>
                <tr><td>备注</td><td class="cMemos"></td></tr>
                <tr><td>本版本合同的修改记录</td><td class="cEditLog">
                    <span class="ty-color-blue node" data-fun="cEditLog">查看</span>
                </td></tr>
                <tr><td>本合同的续约记录</td><td class="cRenewalLog">
                    <span class="ty-color-blue node" data-fun="cRenewalLog">查看</span>
                </td></tr>
            </table>
            <div class="enabledList">

            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%-- 向本合同添加商品 从本合同移出商品 本合同下的商品 --%>
    <div class="bonceContainer bounce-blue" id="tipcontractGoods" style="width: 676px; max-height:400px; ">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table" style="width: 592px; margin-left: 50px;">
                <tr>
                    <td width="25%" class="controlTd">商品代号</td>
                    <td width="30%">商品名称</td>
                    <td width="15%">型号</td>
                    <td width="15%">规格</td>
                    <td width="15%">计量单位</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel addOrCancel ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn bounce-ok addOrCancel ty-btn-big ty-circle-5" onclick="addOrCancelOk()">确定</span>
            <span class="ty-btn bounce-ok cScanc ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>商品</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" styly="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div id="picShow" style="display: none;">
                    <img src=""/>
                </div>
                <div id="video-box" style="display: none;">
                    <video id="Video1" controls style="border: 1px solid blue" height="320" width="240" title="video element">
                        HTML5 Video is required for this example
                    </video>
                </div>
                <div class="ty-mainData">
                    <div id="mtInfo" class="Con">
                        <div class="bigContainer">
                            <div class="left_container">
                                <%--<div class="indexInput">--%>
                                    <%--<div class="Btop"><span>录入商品</span></div>--%>
                                    <%--<ul class="faceul Left-label">--%>
                                        <%--<li class="faceul1">--%>
                                            <%--<a><button onclick="newSalesCommodity(1,2)">录入已销售过的商品</button></a>--%>
                                        <%--</li>--%>
                                        <%--<li class="faceul1">--%>
                                            <%--<a><button onclick="newSalesCommodity(0,2)">录入未销售过的商品</button></a>--%>
                                        <%--</li>--%>
                                    <%--</ul>--%>
                                <%--</div>--%>
                                <div>
                                    <div class="Btop" id="firstLevel"><span id="firstLevelName">全部</span>(<span id="firstLevelAmount">0</span>种)</div>
                                    <form>
                                        <ul class="faceul bottomTree" id="kindsTree"></ul>
                                        <div class="faceul1 suspendBtn">
                                            <a>
                                                <span><span onclick="suspendCommodyList($(this))">暂停销售的商品</span>（<span id="suspendCommodyNum">0</span>种)</span>
                                            </a>
                                        </div>
                                        <div class="left-bottom clear" style="display: none;">
                                            <div class="add-b" onclick="gobackLstLevel(1)"> <a> <span>返回上一级</span> </a>  </div>
                                            <div class="add-b" onclick="gobackLstLevel(2)" > <a> <span >返回全部</span>  </a> </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="between"><div class="between2"></div></div>
                            <div class="right_container">
                                <div class="Right-label" id="right_container">
                                    <div id="inSales">
                                        <div class="container_nav">
                                            <div class="conon">
                                                <div class="dq">
                                                    <span>当前分类</span> <span>：</span>
                                                    <span id="curID">
                                                        <span onclick="showkindNav($(this))" data-id="" data-name="">全部</span>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ty-right searchSect">
                                                <div class="ty-left keywordSearch">
                                                    查找商品
                                                    <input placeholder="请输入商品代号或名称" id="searchKeyBase" />
                                                </div>
                                                <span class="ty-left ty-btn ty-btn-blue" onclick="searchKeyBase()">确定</span>
                                            </div>
                                        </div>
                                        <div class="opinionCon">
                                            <div class="inSales inSalesList">
                                                <table class="ty-table ty-table-none bg-yellow" id="classifiedGoods">
                                                    <thead>
                                                    <td width="12%">商品代号</td>
                                                    <td width="15%">商品名称</td>
                                                    <td width="7%">型号</td>
                                                    <td width="7%">规格</td>
                                                    <td width="8%">计量单位</td>
                                                    <td width="8%">最低库存</td>
                                                    <td width="8%">当前库存</td>
                                                    <td width="200">创建人</td>
                                                    <td width="20%">操作</td>
                                                    </thead>
                                                    <tbody></tbody>
                                                </table>
                                            </div>
                                            <div class="inSuspend inSuspendList">
                                                <table class="ty-table ty-table-none bg-yellow" id="suspendZSList">
                                                    <thead>
                                                    <td width="12%">商品代号</td>
                                                    <td width="16%">商品名称</td>
                                                    <td width="8%">型号</td>
                                                    <td width="8%">规格</td>
                                                    <td width="8%">计量单位</td>
                                                    <td width="8%">最低库存</td>
                                                    <td width="8%">当前库存</td>
                                                    <td width="200">暂停销售的操作</td>
                                                    <td width="20%">操作</td>
                                                    </thead>
                                                    <tbody></tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div id="ye"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="clr"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <%@ include  file="../../common/contentSliderLitbar.jsp"%>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/companyOverview/basic.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/companyOverview/TyZsCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>
