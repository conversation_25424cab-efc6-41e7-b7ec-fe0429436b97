<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
 <link href="../css/companyOverview/authority.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/role/roleIndex.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed2">
    <%-- Wonderss中的加班功能--%>
    <div class="bonceContainer bounce-blue" id="wonderssOverTimeTips">
        <div class="bonceHead">
            <span>Wonderss中的加班功能</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
                <pre style="border: none;background-color:inherit">
Wonderss系统中关于加班的功能如下：

1 职工加班需提前申请，且需经审批通过。
&nbsp;&nbsp;&nbsp;* 申请者每次最多可提出24小时的加班申请。
&nbsp;&nbsp;&nbsp;* 跨天的加班，申请者需分次提交申请。
&nbsp;&nbsp;&nbsp;* 加班需提前多久申请，系统带有默认值，可修改。
&nbsp;&nbsp;&nbsp;* 系统默认加班申请需经最高领导的审批，可修改。"

2 职工未提前申请的加班，总务确认无误后可修改其考勤。
&nbsp;&nbsp;&nbsp;* 系统带有默认为关闭状态的“补报加班”功能，该状态可修改。

3 加班后，职工还需填报实际加班的情况，并需经审批。
&nbsp;&nbsp;&nbsp;* 实际加班情况的审批流程，与加班申请的审批流程相同。

4 实际加班情况审批通过后，加班数据即进入考勤模块。
                </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
    <%-- Wonderss中的请假功能--%>
    <div class="bonceContainer bounce-blue" id="wonderssLeaveTips">
        <div class="bonceHead">
            <span>Wonderss中的请假功能</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section>
            <pre style="border: none;background-color:inherit">
Wonderss系统中关于请假的功能如下：

1 职工请假需提前申请，且需经审批通过。
  * 请假需提前多久申请，系统带有默认值，可修改。
  * 系统默认请假申请需经最高领导的审批，可修改。"

2 职工未提前申请的请假，总务确认无误后可修改其考勤。
  * 系统带有默认为关闭状态的“事后补假”功能，该状态可修改。

3 职工请假后还可提出“提前结束假期”的申请，但需经审批。
  * 提前结束假期申请的审批流程，与请假申请的审批流程相同。

4 请假申请经审批通过后，其数据将进入考勤模块。
            </pre>
            </section>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 审批权限 - 请求处理选择审批人--%>
    <div class="bonceContainer bounce-blue" id="chooseApproveP" style="margin: 240px 0 0 45%;">
        <div class="bonceHead">
            <span>添加审批人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="approvePList">
                <div class="approvePItem" >
                    <input type="radio" value="0" name="approveP" >直接上级
                </div>
                <ul id="approveTree"></ul>
            </div>
        </div>
        <div class="bonceFoot">
            <p id="tp2" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="selectOK()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
        </div>
    </div>
    <%-- 二级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="secdTip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="secdtipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel(); ">确认</span>
        </div>
    </div>
    <%--  加班 --%>
    <div class="bonceContainer bounce-blue" id="overTime">
        <div class="bonceHead">
            <span>查看加班审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="overTimeSetting">
                <div class="text-right"><a class="ty-color-blue" onclick="wonderssOverTimeTips()">Wonderss中的加班功能</a></div>
                <div class="p_row">加班设置的现状如下，如需要，可修改。	</div>
                <div class="p_row"><div>1 职工要加班，需至少提前 <span class="upperLimit"></span> 分钟提出申请 </div></div>
                <div class="p_row"><div>2 职工端“补报加班”的功能处于 <span class="state_makeAfterFact"></span> 状态 </div></div>
                <div class="p_row"><div>3 加班的审批 </div></div>
                <div id="approvList"></div>
                <small class="applyTip">
                    <div>注：</div>
                    <p>加班跨天时，申请者需分次提交申请。</p>
                    <p>申请者每次最多可提出24小时的加班申请。</p>
                </small>
            </section>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  报销查看 --%>
    <div class="bonceContainer bounce-blue " id="reimburse">
        <div class="bonceHead">
            <span>查看报销审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="applyTip">
                <div>审批记录：</div>
                <p></p>
            </div>
            <div id="flowList31"> </div>

        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  请假修改记录查看 --%>
    <div class="bonceContainer bounce-blue" id="leaveRecordSee">
        <div class="bonceHead">
            <span>查看请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear">
                <div id="approvRecord" class="ty-right">
                </div>
            </div>
            <div id="leaveSetList"></div>
            <div class="leaveSeeTip">
                职工无法提交超过<span class="maxHur"></span>的请假申请。
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  查看请假审批的审批设置  --%>
    <div class="bonceContainer bounce-blue " id="leave">
        <div class="bonceHead">
            <span>查看请假审批的审批设置</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <section class="overTimeSetting" style="width: 400px;margin: 0 auto;">
                <p class="text-right"><a class="ty-color-blue" onclick="wonderssLeaveTips()">Wonderss中的请假功能</a></p>
                <div class="p_row">请假设置的现状如下，如需要，可修改。	</div>
                <div class="p_row"><div>1 职工要请假，需至少提前 <span class="upperLimit"></span> 分钟提出申请 </div></div>
                <div class="p_row"><div>2 职工端“事后补假”的功能处于 <span class="state_makeAfterFact"></span> 状态	</div></div>
                <div class="p_row"><div>3 请假的审批	 </div></div>
                <div id="leaveApprovList"></div>
                <small class="ty-color-blue leaveSeeTip">职工无法提交超过<span class="maxHur"></span>的请假申请。</small>
            </section>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()"  >关闭</span>
        </div>
    </div>
    <%-- 采购审批设置的修改--%>
    <div class="bonceContainer bounce-blue" id="purchaseApprovalSettingsChange">
        <div class="bonceHead">
            <span class="bounce-title">审批设置的修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 0 auto">
                <div>
                    <div class="page_needApprove">
                        <div class="item-flex">
                            本页面上可更换审批者，可添加一层或多层审批，为多层审批时，还可删除某层审批！
                        </div>
                        <div class="item-flex">
                            <div class="item-auto text-right">
                                <button class="link-blue" onclick="addOneLevel($(this))">添加一层审批</button>
                            </div>
                        </div>
                        <div class="flows"></div>
                    </div>
                    <div class="page_noNeedApprove">
                        <div class="item">
                            确定要将此项审批修改至无需审批吗？
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div class="item-title"><span class="extraTip">如确定，</span>请选择本次修改的拟生效时间！</div>
                    <input class="ty-inputText" type="text" name="openDate" id="openDate" placeholder="请选择">
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="sureChangeApprove()">提交</button>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 当前权限查看  查看按钮 弹框 --%>
    <div class="bonceContainer bounce-blue" id="details">
        <div class="bonceHead">
            <span> <span id="detailsName"></span> — <span id="detailsModel">管理权限</span> </span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin:0 auto;">
                <table class="ty-table">
                    <tr id="ttl_popm">  </tr>
                    <tr id="check_popm">  </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big ty-circle-5 ty-btn-blue" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>

    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="Tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs">    </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确认</span>
        </div>
    </div>
    <%--  财务权限审批--%>
    <div class="bonceContainer bounce-blue" id="myModal_finance" style="width:540px;">
        <div class="bonceHead">
            <span>报销申请 - 详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div>
                <div class="trItem">
                    <span class="ttl">审批级别</span> <span class="con" id="financeLevel"> </span>
                </div>
                <div id="financeFlow"> </div>
            </div>

        </div>
        <div class="bonceFoot">
            <p id="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 审批设置修改 --%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="itemApply" >
        <div class="bonceHead">
            <span>审批设置修改 - 详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix120">审批级别</div>
                    <div class="item-auto">
                        <select name="level" onchange="changeAuth()" disabled>
                            <option value="1">一级审批</option>
                            <option value="2">二级审批</option>
                        </select>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">一级审批人</div>
                    <div class="item-auto">
                        <select name="firstApprover" disabled></select>
                    </div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">最终审批人</div>
                    <div class="item-auto">
                        <select name="lastApprover" disabled></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

    <%-- 付款设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="paymentSet" >
        <div class="bonceHead">
            <span id="paymentTtl">付款设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <%--<div class="">
                <p class="leTip"> 报销或借款等与资金支出有关申请审批过后、<br>出纳实际付款前需再经付款审批。</p>
                <p>您可更换付款审批的审批人。</p>
                <p>付款审批的审批人：
                    <span id="scanU">董事长139XXXXXXXX</span>
                    <input type="hidden" id="paymentID">
                    <span class="ty-right applyTip payEditBtn" id="payEditBtn"  onclick="payEditBtn()">修改</span>
                </p>
                <div id="startDate2" >
                    本修改将对 <input type="text"/>之后提交的付款申请生效
                </div>
            </div>--%>
            <div>
                <p style="line-height: 30px;">
                    付款审批的设置
                    <%--                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" onclick="updatePaySet()">修改付款设置</span>--%>
                </p>
                <div style="padding-left:30px; ">
                    <div class="tiph">
                        <span>内容：</span>
                        <div>与资金支出有关的申请（如报销申请、借款申请）审批通过后、出纳实际付款前的关于是否付款专门的审批</div>
                    </div>
                    <div class="tiph">
                        <span>功能：</span>
                        <div>系统默认需由最高管理者审批。可更换为其他人或“无需审批”</div>
                    </div>
                    <div>
                        当前付款审批的设置：<span id="cur1"></span>
                    </div>
                </div>
                <hr style=" margin-top: 10px; border-top: 1px solid #ccc; margin-bottom: 8px;">
                <p>
                    付款复核的设置
                </p>
                <div style="padding-left:30px; ">
                    <div class="tiph">
                        <span>内容：</span>
                        <div>出纳选择为现金外的对外支付方式时，是否需要财务负责人复核</div>
                    </div>
                    <div class="tiph">
                        <span>功能：</span>
                        <div>系统默认需由财务负责人复核。可改为“无需审批”</div>
                    </div>
                    <div>
                        当前付款复核的设置：<span id="cur2"></span>
                    </div>
                </div>

            </div>
        </div>
        <div class="bonceFoot">
            <%--<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="savePaycharge"  onclick="applySubmit()">提交修改申请</span>--%>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="savePaychargeNo" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>

    <%-- 采购审批设置--%>
    <div class="bonceContainer bounce-blue bounce-changeState" id="purchaseApprovalSettings" >
        <div class="bonceHead">
            <span class="bounce-title">采购审批设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 400px; margin: 16px auto">
                <div class="item-flex">
                    <div class="item-fix fix200 tips">目前，本公司采购需 <span class="level"></span> 级审批</div>
                </div>
                <div class="item-flex">
                    <div class="item-fix fix120">开始执行的时间</div>
                    <div class="item-auto text-right openDate"></div>
                </div>
                <div class="kj-hr"></div>
                <div class="approvalFlows"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
    <%-- 报销修改 --%>
    <div class="bonceContainer bounce-blue " id="finananceEdit">
        <div class="bonceHead">
            <span>查看报销审批的审批设置</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div>
                <p>目前本公司报销的审批流程如下：
                </p>
                <div id="flowList3"> </div>
                <div class="applyTip" style="margin-left:65px;"> </div>
            </div>

        </div>
        <div class="bonceFoot">
            <div class="">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-5 " id="closeFin2" onclick="bounce.cancel()"  >关闭</span>
            </div>

        </div>
    </div>
    <%-- 修改记录 --%>
    <div class="bonceContainer bounce-blue " id="requestLog">
        <div class="bonceHead">
            <span id="logType">修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="summary"> </div>
            <table class="kj-table" id="log">
                <tr>
                    <td>资料状态</td>
                    <td id="log2">状态</td>
                    <td>开始执行日期</td>
                    <td id="log1">操作</td>
                    <td id="log13">付款复核者</td>

                    <td>创建人/修改人</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <p class="tp" style="color: red; "></p>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()"  >关闭</span>
        </div>
    </div>
    <%--  来自客户订单的评审 --%>
    <div class="bonceContainer bounce-blue" id="sale_common" style="width: 520px">
        <div class="bonceHead">
            <span>来自客户订单的评审</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="area-body">
                <div class="area-con">
                    <div>内容：<span id="msgLog">对于客户发来订单中的数量与交期，是否需公司各部门评审</span></div>
                    <div>功能：<span id="funLog">系统默认需要评审。可修改为“无需评审”</span></div>
                    <div>当前的设置：<span id="currentSettings"></span></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>

    <%--负责人修改记录--%>
    <div class="bonceContainer bounce-blue" id="coreChangeHistory" style="width: 800px">
        <div class="bonceHead">
            <span><span class="module"></span>负责人修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon clearfix" style="height: 300px;overflow: auto">
            <h4 class="userRole ty-color-green"></h4>
            <div class="ty-alert ty-alert-info" style="justify-content: space-between"><span class="isChange"></span><span class="createInfo"></span></div>
            <table class="ty-table ty-table-control changeHistory">
                <thead>
                <tr>
                    <td>资料状态</td>
                    <td><span class="module"></span>负责人</td>
                    <td>创建人/修改人</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>


</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>权限</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="mainc mainc0">
                    <div class="lev1">
                        <p>常规的权限</p>
                        <div class="lev2">
                            <div>已分配给职工的常规权限 <span class="linkBtn" data-fun="scanFun" data-type="1">查看</span></div>
                            <div>尚未分配给职工的常规权限 <span class="linkBtn" data-fun="scanFun" data-type="2">查看</span></div>
                            <div>职工们已有哪些常规的权限？ <span class="linkBtn" data-fun="scanFun" data-type="3">查看</span></div>
                        </div>
                    </div>
                    <hr/>
                    <div class="lev1">
                        <p>审批权限</p>
                        <div class="lev2">
                            <div>系统支持对一些事务的审批，当前状态如何？ <span class="linkBtn" data-fun="scanFun" data-type="4">查看</span></div>
                            <div>现在，哪些职工能进行哪些审批？ <span class="linkBtn" data-fun="scanFun" data-type="5">查看</span></div>
                        </div>
                    </div>
                    <hr/>
                    <div class="lev1">
                        <p>其他权限<span class="linkBtn" data-fun="scanFun" data-type="6">查看</span></p>
                    </div>
                </div>
                <div class="mainc mainc12 mainc1">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backHome">返回</span>
                    </p>
                    <div class="tblContainer">
                        <div class="ty-table" id="assigned">
                            <ul class="ty-head">
                                <li style="width: 10%">模块</li>
                                <li style="width: 18%">子模块</li>
                                <li style="width: 14.4%">二级子模块</li>
                                <li style="width: 36%">功能描述</li>
                                <li style="width: 21.6%">已拥有此权限者</li>
                            </ul>
                            <div class="ty-body"></div>
                        </div>
                    </div>
                </div>
                <div class="mainc mainc12 mainc2">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backHome">返回</span>
                    </p>
                    <div class="tblContainer" id="unassigned" >
                        <div class="ty-table">
                            <ul class="ty-head">
                                <li style="width: 10%">模块</li>
                                <li style="width: 18%">子模块</li>
                                <li style="width: 72%">功能描述</li>
                            </ul>
                            <div class="ty-body"></div>
                        </div>
                    </div>
                </div>
                <div class="mainc mainc3">
<%--                    当前权限查看--%>
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backHome">返回</span>
                    </p>
                    <div class="ty-mainData">
                        <div class="ty-right search" style="margin-top:-36px;">
                            <input type="text" id="departName0" placeholder="请输入部门"><span class="ty-btn ty-btn-big ty-btn-blue  ty-circle-3" data-fun="searchF" data-val="1">筛选</span>
                            <input type="text" id="userName0" placeholder="请输入姓名"><span class="ty-btn ty-btn-big ty-btn-blue  ty-circle-3" data-fun="searchF" data-val="2">搜索</span>
                        </div>
                        <table class="ty-table" style="width:200px" id="auth_1_1">
                            <tbody id="authList_1_1">
                            <tr>
                                <td>部门</td>
                                <td>姓名</td>
                            </tr>

                            </tbody>
                        </table>
                        <div class="scrollTable"  id="auth_1_2">
                            <table class="ty-table ty-table-control" id="auth_1_21" style="width: 2000px;">
                                <tbody id="authList_1_2">
                                <tr>
                                    <td colspan="11" id="colspan">项目</td>
                                </tr>
                                <tr id="modelTTl_2">
                                    <td>权限管理</td>
                                    <td>总务管理</td>
                                    <td>财务管理</td>
                                    <td>销售管理</td>
                                    <td>生产管理</td>
                                    <td>商品管理</td>
                                    <td>物料管理</td>
                                    <td>项目管理</td>
                                    <td>文件与资料</td>
                                    <td>个人中心</td>
                                    <td>关于</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="clr"></div>
                    </div>
                </div>
                <div class="mainc mainc4">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backHome">返回</span>
                    </p>

                    <%--审批权限--%>
                    <div class="tblContainer">

                        <table class="kj-table kj-table-striped">
                            <thead>
                            <tr>
                                <td rowspan="2"> 审批事项 </td>
                                <td rowspan="2"> 当前状态 </td>
                                <td colspan="3" style="text-align: center;"> 当前审批者 </td>
                                <td rowspan="2"> 操作 </td>
                            </tr>
                            <tr>
                                <td>一级审批者</td>
                                <td>二级审批者</td>
                                <td>最终审批者</td>
                            </tr>
                            </thead>
                            <tbody id="chargeList"></tbody>
                        </table>
                    </div>

                </div>
                <div class="mainc mainc5">
<%--                    审批设置查看--%>
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backHome">返回</span>
                    </p>
                    <div class="ty-mainData">
                        <div class="ty-right search" style="margin-top:-36px;">
                            <input type="text" id="departName1" placeholder="请输入部门"><span class="ty-btn ty-btn-big ty-btn-blue  ty-circle-3" data-fun="searchF" data-val="3">筛选</span>
                            <input type="text" id="userName1" placeholder="请输入姓名"><span class="ty-btn ty-btn-big ty-btn-blue  ty-circle-3" data-fun="searchF" data-val="4">搜索</span>
                        </div>
                        <table class="ty-table ty-table-control">
                            <tbody id="authList_2">
                            <tr>
                                <td>部门</td>
                                <td id="biao" style="min-width: 60px;">
                                    <div class="biaotou"></div>
                                    <span class="tt1">项目</span>
                                    <span class="tt2">姓名</span>
                                </td>
                                <td>加班申请</td>
                                <td>请假申请</td>
                                <td>报销申请</td>
                                <td>职工档案修改</td>
                                <td>岗位设置修改</td>
                                <td>审批设置修改</td>
                                <td>新项目立项</td>
                                <td>新项目开发</td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="mainc mainc6">
                    <p>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" data-fun="backHome">返回</span>
                    </p>
                    <div class="ty-mainData">
                        <p class="ty-alert ty-alert-info"><i class="fa fa-info-circle"></i>您在此可确定或修改如下事项的负责人：</p>
                        <div class="importIntro core">
                            <div class="handleTip">
                                <h3>投诉管理</h3>
                                <p>投诉管理负责人全权负责投诉事项，将获得如下操作权限：</p>
                                <p>1 . 在系统内添加可录入投诉信息的职工</p>
                                <p>2 . 决定某条新录入的投诉是否立案</p>
                                <p>3 . 指定某条投诉的处理负责人</p>
                                <p>4 . 审批某条投诉的结案申请</p>
                                <p>5 . 确定何人可以查看投诉清单</p>
                            </div>
                            <p class="ty-alert ty-alert-info" style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(1)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro projectCore">
                            <div class="handleTip">
                                <h3>项目管理</h3>
                                <p>项目管理负责人全权负责项目管理，将获得如下操作权限：</p>
                                <p>1 . 在系统内添加可对项目管理立项的职工</p>
                                <p>2 . 决定某条新录入的项目管理项目是否立案</p>
                                <p>3 . 审批某项目管理项目的结案申请</p>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(5)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro improvementCore">
                            <div class="handleTip">
                                <h3>持续改进管理</h3>
                                <p>持续改进管理负责人全权负责持续改进，将获得如下操作权限：</p>
                                <p>1 . 在系统内添加可对持续改进立项的职工</p>
                                <p>2 . 决定某条新录入的持续改进项目是否立案</p>
                                <p>3 . 审批某持续改进项目的结案申请</p>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(7)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro vehicleCore">
                            <div class="handleTip">
                                <h3>车务管理</h3>
                                <p>系统内，车务管理负责人将获如下操作权限：</p>
                                <p>1 . 车辆管理：新增或停用车辆、修改车辆信息等</p>
                                <p>2 . 司机管理</p>
                                <p>3 . 向用车申请者派车。</p>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(9)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro pickingCore">
                            <div class="handleTip">
                                <h3>领料分工</h3>
                                <p>系统内，领料分工负责人将负责：</p>
                                <p>1 . 选择领料者；</p>
                                <p>2 . 所选领料者所能领用的材料。</p>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(11)">修改记录</button>
                            </p>
                        </div>
                        <div class="importIntro buySalesCore">
                            <div class="handleTip ">
                                <h3>购销统筹</h3>
                                <div class="planIndent">
                                    <div style="margin-bottom: 20px;">客户订单评审完后，购销统筹负责人将收到相关消息。</div>
                                    <div>系统内，购销统筹负责人负责：</div>
                                    <div>确定所需采购材料最少需购买多少，及最迟不得晚于何时到货。</div>
                                </div>
                            </div>
                            <p class="ty-alert ty-alert-info"  style="justify-content:space-between">
                                <span>当前负责人：</span>
                                <b class="ty-color-blue coreUser"></b>
                                <button class="ty-btn ty-btn-blue ty-circle-3" onclick="changeHistory(13)">修改记录</button>
                            </p>
                        </div>
                    </div>
                </div>


        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/companyOverview/authority.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/authority/currentSettings.js?v=SVN_REVISION"></script>
<script src="../script/companyOverview/approveLooking.js?v=SVN_REVISION"></script>

<%@ include  file="../../common/footerBottom.jsp"%>
</body>
</html>
