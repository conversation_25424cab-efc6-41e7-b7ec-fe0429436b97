<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/sales/orderManege.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/cashBack/cashBack.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" onclick="$('.searchCon').hide();">
<div class="ty-tip" id="tipCon">
    <div class="ty-tipcon"  >
        <div class="ty-trigl-1"><span></span></div>
        <div id="tipitem"></div>
    </div>
</div>
<div class="bounce_Fixed3">
    <%-- 向本合同添加商品 从本合同移出商品 本合同下的商品 --%>
    <div class="bonceContainer bounce-blue" id="tipcontractGoods" style="width: 676px; max-height:400px; ">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table" style="width: 592px; margin-left: 50px;">
                <tr>
                    <td width="25%" class="controlTd">商品代号</td>
                    <td width="30%">商品名称</td>
                    <td width="15%">型号</td>
                    <td width="15%">规格</td>
                    <td width="15%">计量单位</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel addOrCancel ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">取消</span>
            <span class="ty-btn bounce-ok addOrCancel ty-btn-big ty-circle-5" onclick="addOrCancelOk()">确定</span>
            <span class="ty-btn bounce-ok cScanc ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签：</span>
                    <span class="sale-con" id="see_contactTag"></span>
                </div>
            </div>
            <%--<p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>--%>
            <table class="see_otherContact ty-table" style="width:442px;">
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">

    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" onkeyup="countWords($(this),20)"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
    <%--客户对发票方面要求的设置--%>
    <div class="bonceContainer bounce-blue" id="invoiceSet2" style="width: 450px">
        <div class="bonceHead">
            <span>客户对发票方面要求的设置</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-left:60px ">
                <p>请选择此客户对发票方面的要求</p>
                <p class="setItem" data-type="1"><i class="fa fa-circle-o"></i><span>需要主要为增值税专用发票</span></p>
                <p class="setItem" data-type="2"><i class="fa fa-circle-o"></i><span>需要主要为增值税专用发票以外的发票</span></p>
                <p class="setItem" data-type="3"><i class="fa fa-circle-o"></i><span>基本不需要开发票</span></p>
                <p class="setItem" data-type="4"><i class="fa fa-circle-o"></i><span>要求不定，维持不设置的状态</span></p>
                <p class="tipsmall">注：此项设置完成后，新增订单时往往更便捷！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="invoiceSetOk2()">确定</span>
        </div>
    </div>
    <%-- 合同查看 --%>
    <div class="bonceContainer bounce-blue" id="cScan" style="width: 874px; ">
        <div class="bonceHead">
            <span>查看合同</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="width: 80%; margin:0 auto; ">
            <div class="nothas" style="text-align: center; line-height: 50px; ">
                本商品没有所属的合同！
            </div>
            <div class="has">
                <p>本版本合同的创建 <span class="create"></span></p>
                <table class="ty-table ty-table-control leftTab">
                    <tr><td>合同编号</td><td class="cNos"></td></tr>
                    <tr><td>签署日期</td><td class="cSignDates"></td></tr>
                    <tr><td>合同的有效期</td><td class="cvalidDates"></td></tr>
                    <tr><td>合同的扫描件或照片</td><td class="cImgs"></td></tr>
                    <tr><td>合同的可编辑版</td><td>
                        <a class="ty-color-blue cWord node" data-fun="cWord">查看</a>
                    </td></tr>
                    <tr><td>本合同下的商品（共<span class="gNum"></span>种）</td><td class="cGoodss">
                        <span class="ty-color-blue node" data-fun="gNum">查看</span>
                    </td></tr>
                    <tr><td>备注</td><td class="cMemos"></td></tr>
                    <tr><td>本版本合同的修改记录</td><td class="cEditLog">
                        <span class="ty-color-blue node" data-fun="cEditLog">查看</span>
                    </td></tr>
                    <tr><td>本合同的续约记录</td><td class="cRenewalLog">
                        <span class="ty-color-blue node" data-fun="cRenewalLog">查看</span>
                    </td></tr>
                </table>
                <div class="enabledList">  </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>

    <%-- 历史价格 --%>
    <div class="bonceContainer bounce-blue" id="cHistoryPrice" style="width: 874px; ">
        <div class="bonceHead">
            <span>历史价格</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="width: 80%; margin:0 auto; ">
            <p>以下为本商品最近的 <span class="cNum"></span>个价格。</p>
            <table class="ty-table ty-table-control ">
                <tr><td>商品价格</td><td>下单时间</td></tr>
            </table>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>

    <%-- 选择联系人--%>
    <div class="bonceContainer bounce-blue" id="chooseCusContact" style="width: 450px">
        <div class="bonceHead">
            <span>选择客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="background:#f0f8ff ">
            <input type="hidden" id="target">
            <div class="p0">
                <p style='text-align:center;'>暂无客户数据，请通过新增添加</p>
            </div>
            <div class="p1">
                <p>以下为可供选择的客户联系人</p>
                <ul class="cusList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="linkBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="p1 ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseCusContactOk()">确定</button>
        </div>
    </div>
    <%-- 订购信息展示 --%>
    <div class="bonceContainer bounce-red" id="linkOrd">
        <div class="bonceHead">
            <span class="noGs">订购信息</span>
            <span class="ysGs">！ 重要提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="noGs">当前该商品无 <span class="ty-color-red">其他</span> 发货需求</div>
            <div class="ysGs">
                <p><span id="linkOrdName">内部名称</span> 当前虽有 <span id="linkOrdStock">当前库存</span> <span id="linkOrdUnit">计量单位</span> ，但该商品尚有如下发货需求：</p>
                <div style="max-height:300px; overflow: auto; ">
                    <table class="ty-table" id="linkOrdTb">
                        <tr>
                            <td width="10%">序号</td>
                            <td width="15%">订单号</td>
                            <td width="15%">负责人</td>
                            <td width="10%">数量</td>
                            <td width="15%">要求到货时间</td>
                            <td width="35%">客户名称</td>
                        </tr>
                        <tr>
                            <td>序号</td>
                            <td>订单号</td>
                            <td>负责人</td>
                            <td>数量</td>
                            <td>要求到货时间</td>
                            <td>客户名称</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">确定</span>
        </div>
    </div>
    <%--系统提示--%>
    <div class="bonceContainer bounce-red" id="fix2MsTips">
        <div class="bonceHead">
            <span>!提示</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p>本系统不支持同一订单上存在多种开票情况的商品。</p>
            <p>您已选择了<span class="msTxtTip"></span>，请继续选择同类型商品。</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--新增商品 修改商品--%>
    <div class="bonceContainer bounce-green" id="newGood"  style="width:600px;">
        <div class="bonceHead">
            <span>新增商品</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p class="invoiceCatTip"></p>
            <div class="clearfix orderInfo">
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">商品代号 <i class="red">*</i>
                        <span class="ty-right linkBtn isZ" onclick="getContractDetail($(this))">所属的合同</span>
                        <span class="ty-right linkBtn isT" onclick="getHistoryPrice($(this))">历史价格</span>
                    </div>
                    <input type="text" class="ty-inputSelect outerSn">
                    <ul class="ty-optionCon"></ul>
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">商品名称</div>
                    <input type="text" class="ty-inputText outerName" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">订购数量 <i class="red">*</i></div>
                    <input type="text" class="ty-inputText goodNum" onkeyup=" tofixed3(this)">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">计量单位</div>
                    <input type="text" class="ty-inputText unit" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">当前库存
                        <span class="linkBtn" style="position: relative;left: 27px" onclick="linkOrds($(this))">本商品的其他订购信息</span></div>
                    <input type="text" class="ty-inputText currentStock" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">最低库存</div>
                    <input type="text" class="ty-inputText minimumStock" disabled="disabled">
                </div>
                <%----------------------------------------%>
                <div class="orderItem orderItemMiddle invoice1">
                    <div class="orderItemTitle">税率 <i class="red redRequie modify">*</i></div>
                    <select class="ty-inputText rate" disabled="disabled" onchange="setprice(3)"></select>
                </div>
                <div class="orderItem orderItemMiddle invoice1">
                    <div class="orderItemTitle">不含税单价 <i class="red redRequie modify">*</i></div>
                    <input type="text" class="ty-inputText noPrice" disabled="disabled" onkeyup="clearNoNum(this); setprice(1)">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle"><span class="changeTtl">含税单价</span> <i class="red modify">*</i></div>
                    <input type="text" class="ty-inputText price" disabled="disabled" onkeyup="clearNoNum(this); setprice(2)">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">价格说明<span class="ty-right linkBtn modifyPrice isZ" data-fun="modifyPrice">临时调价</span></div>
                    <input type="text" class="ty-inputText priceMemo" disabled="disabled" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                </div>
                <%----------------------------------------%>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">要求到货日期 <i class="xing">*</i></div>
                    <input type="text" class="ty-inputText itemCon w600 requireDate"  value="" id="DateOfArrival"
                           name="DateOfArrival" autocomplete="off">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">货物件数</div>
                    <input type="text" class="ty-inputText goodNum_stock" disabled="disabled">
                </div>
                <div class="orderItem orderItemMiddle">
                    <div class="orderItemTitle">收货地点 <i class="xing">*</i>
                        <span class="linkBtn ty-right" data-fun="newReceiveInfo2">新增收货地点</span>
                    </div>
                    <select type="text" class="ty-inputSelect receiveAddress receiveAddress2"></select>
                </div>
                <div class="orderItem orderItemMiddle hd">
                    <div class="invoiceTip"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="sureNewGood()">确定</span>
        </div>
    </div>
    <%-- 删除商品提示 --%>
    <div class="bonceContainer bounce-red" id="delGoodTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center ">
                <p>确定删除该行商品吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="delGoodTipOk()">确定</span>
        </div>
    </div>
    <%-- 开票要求 编辑--%>
    <div class="bonceContainer bounce-blue" id="invoiceSet">
        <div class="bonceHead">
            <span>编辑开票要求</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="margin-left:60px ">
                <p>本订单各商品需开具什么发票？请选择</p>
                <p class="setItem" data-type="1"><i class="fa fa-circle-o"></i><span>需开具增值税专用发票</span></p>
                <p class="setItem" data-type="2"><i class="fa fa-circle-o"></i><span>需开具其他发票</span></p>
                <p class="setItem" data-type="3"><i class="fa fa-circle-o"></i><span>不开发票</span></p>
                <p class="tipsmall">注：您新增的某订单上，只能录入开具同种发票的商品！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="invoiceSetOk()">确定</span>
        </div>
    </div>
    <%-- 查看开票资料--%>
    <div class="bonceContainer bounce-blue" id="InvoiceMessage" style="width:1100px;">
        <div class="bonceHead">
            <span>查看开票资料</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="sale_c">
                <div class="sale_ttl1">公司名称：</div>
                <div class="sale_con1">
                    <span class="invoiceName"></span>
                </div>
                <div class="sale_ttl1">地址：</div>
                <div class="sale_con2">
                    <span class="invoiceAddress"></span>
                </div>
                <div class="sale_ttl1">电话：</div>
                <div class="sale_con">
                    <span class="telephone"></span>
                </div>
            </div>
            <div class="sale_c">
                <div class="sale_ttl1">开户行：</div>
                <div class="sale_con1">
                    <span class="bankName"></span>
                </div>
                <div class="sale_ttl1">账号：</div>
                <div class="sale_con1" >
                    <span class="bank_no"></span>
                </div>
                <div class="sale_ttl1">税号：</div>
                <div class="sale_con">
                    <span class="taxpayerID"></span>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="sale_updatabtn()">修改</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--开票信息修改--%>
    <div class="bonceContainer bounce-blue" id="updateInvoice" style="width:1100px;">
        <div class="bonceHead">
            <span>开票信息修改</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form id="sale_updataInvoice">
                <p style="margin-right:12px; text-align: right; ">
                    <span style="margin-right:20px;">客户对发票方面的要求</span>
                    <span>尚未设置</span>
                    <span class="linkBtn goset_update" data-type="update"  onclick="goset($(this))">去设置</span>
                </p>
                <div class="sale_c">
                    <div class="sale_ttl1">公司名称：</div>
                    <div class="sale_con1">
                        <input class="invoiceName" data-name="invoiceName" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">地址：</div>
                    <div class="sale_con2">
                        <input class="invoiceAddress" data-name="invoiceAddress" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">电话：</div>
                    <div class="sale_con">
                        <input class="telephone" data-name="telephone" type="text" placeholder="请录入" require/>
                    </div>
                </div>
                <div class="sale_c">
                    <div class="sale_ttl1">开户行：</div>
                    <div class="sale_con1">
                        <input class="bankName" data-name="bankName" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">账号：</div>
                    <div class="sale_con1" >
                        <input class="bank_no"style="width: 186px;" data-name="bank_no" type="text" placeholder="请录入" require/>
                    </div>
                    <div class="sale_ttl1">税号：</div>
                    <div class="sale_con">
                        <input class="taxpayerID" data-name="taxpayerID" type="text" placeholder="请录入" require/>
                    </div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="updataInvoiceSure" onclick="updataInvoice_sure()">提交</button>
        </div>
    </div>

    <%--删除订单提示--%>
    <div class="bonceContainer bounce-red" id="deleteGood">
        <div class="bonceHead">
            <span>系统提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="msTip">您确定要删除此条商品？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="sureDeleteGood()">确定</span>
        </div>
    </div>
    <%--系统提示--%>
    <div class="bonceContainer bounce-red" id="fixMsTips">
        <div class="bonceHead">
            <span>系统提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="msTip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--收货地点功能说明--%>
    <div class="bonceContainer bounce-blue" id="reviceAddress">
        <div class="bonceHead">
            <span>收货地点功能说明</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <p>选择商品时，页面上将带有此处选择的收货地点。</p>
            <p>您可按实际需要，对该页上的地点进行修改。</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
        </div>
    </div>
    <%--新增收货信息--%>
    <div class="bonceContainer bounce-green" id="newReceiveInfo" style="width: 450px">
        <div class="bonceHead">
            <span>收货信息</span>
            <a class="bounce_close" onclick="addReceive('cancel');"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="sale_ttl1">收货地址：</span>
                <span class="sale_con1"><input type="text" placeholder="请录入" id="ReceiveAddress" require/></span>
            </p>
            <p>
                <span class="sale_ttl1">收货人：</span>
                <span class="chooseCusCon" data-target="#ReceiveName">
                <input type="text" style="width: 108px;" readonly placeholder="请选择" id="ReceiveName" require/>
                <span class="hd"></span>
                <span class="chooseCusBtn"><i class="fa fa-chevron-down"></i></span>
            </span>
                <span class="linkBtn" onclick="addContactInfo(2)">新增</span>
            </p>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="addReceive('cancel');">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addReceive" onclick="addReceive()">提交</button>
        </div>
    </div>
    <%--新增联系人--%>
    <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
        <div class="bonceHead">
            <span>新增联系人</span>
            <a class="bounce_close" onclick="chargeXhr($(this))"></a>
        </div>
        <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
            <form id="newContectData">
                <p>
                    <span class="sale_ttl1">联系人标签：</span>
                    <span class="sale_gap" style="position:relative; top: 6px;" id="contactFlag"></span>
                </p>
                <p>
                    <span class="sale_ttl1">姓名：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                </p>
                <p>
                    <span class="sale_ttl1">手机：</span>
                    <span class="sale_gap"><input type="text" placeholder="请录入" id="contactNumber" require/></span>
                    <a class="sale_ttl1 addMore" onclick="addMore($(this))">添加更多联系方式</a>
                    <select style="display: none;width: 167px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                        <option value="0"></option>
                        <option value="1">手机</option>
                        <option value="2">QQ</option>
                        <option value="3">Email</option>
                        <option value="4">微信</option>
                        <option value="5">微博</option>
                        <option value="9">自定义</option>
                    </select>
                </p>
                <ul class="otherContact">
                </ul>
                <div id="contactsCard">
                    <span class="sale_ttl1">名片：</span>
                    <div id="uploadCard" class="cardUploadBtn"></div>
                </div>
            </form>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="chargeXhr($(this))">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big" id="addContact" onclick="addContact()" data-name="addContact">提交</button>
        </div>
    </div>
    <%-- 查看原始订单 --%>
    <div class="bonceContainer bounce-blue" id="seeOrderOral" style="width:90%; min-width:1000px; ">
        <div class="bonceHead">
            <span>查看原始订单</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="orderItem">
                <div class="orderItemTitle">客户名称</div>
                <div class="orderItemCon cusName" id="see_cusName"></div>
            </div>
            <div class="orderItem">
                <div class="orderItemTitle">订单收到日期</div>
                <div class="orderItemCon signDate" id="see_signDate"></div>
            </div>
            <div class="orderItem">
                <div class="orderItemTitle">是否开票</div>
                <div class="orderItemCon hasInvoice" id="see_hasInvoice"></div>
            </div>
            <div class="orderItem">
                <div class="orderItemTitle">客户代号</div>
                <div class="orderItemCon cusId" id="see_cusId"></div>
            </div>
            <div class="orderItem">
                <div class="orderItemTitle">订单号</div>
                <div class="orderItemCon sn" id="see_sn"></div>
            </div>
            <div class="orderItem">
                <div class="orderItemTitle">订单总额</div>
                <div class="orderItemCon contractAmount" id="see_amount"></div>
            </div>
            <div class="orderItem">
                <div class="orderItemTitle">录入者</div>
                <div class="orderItemCon createName" id="see_createName"></div>
            </div>
            <div class="orderItem">
                <div class="orderItemTitle">录入时间</div>
                <div class="orderItemCon createDate" id="see_createDate"></div>
            </div>
            <div style="padding: 0 30px">
                <table class="ty-table ty-table-control goodList">
                    <thead>
                    <tr>
                        <td>序号</td>
                        <td>商品代号</td>
                        <td>外部名称</td>
                        <td>产品代号</td>
                        <td>内部名称</td>
                        <td>含税单价</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>要求到货日期</td>
                    </tr>
                    </thead>
                    <tbody id="see_tbl"></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--  订单终止审批记录 --%>
    <div class="bonceContainer bounce-blue" id="endOrdLog">
        <div class="bonceHead">
            <span>订单终止审批记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table">
                <tr>
                    <td width="20%">修改者</td>
                    <td width="25%"></td>
                    <td width="20%">修改时间</td>
                    <td width="35%"></td>
                </tr>
                <tr>
                    <td>情况简述</td>
                    <td colspan="3"></td>
                </tr>
                <tr>
                    <td class="bg">终止原因</td>
                    <td class="bg" colspan="3"></td>
                </tr>
                <tr>
                    <td>一级审批者</td>
                    <td></td>
                    <td>审批时间</td>
                    <td></td>
                </tr>
                <tr>
                    <td>审批意见</td>
                    <td colspan="3"></td>
                </tr>
                <tr>
                    <td>最终审批者</td>
                    <td></td>
                    <td>审批时间</td>
                    <td></td>
                </tr>
                <tr>
                    <td>审批意见</td>
                    <td colspan="3"></td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="ticketErrorLog" style="width:900px; ">
        <div class="bonceHead">
            <span>发票异常记录</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table" id="tab5">
                <tr>
                    <td width="20%">发票日期</td>
                    <td width="20%">发票号码</td>
                    <td width="15%">发票金额</td>
                    <td width="45%">情况记录</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%-- 一并提交开票申请提示 --%>
    <div class="bonceContainer bounce-red" id="allTicketApplyTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                将本次提交的 <span id="allNum"></span> 张开票申请单提交审批？
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="allTick()">确定</span>
        </div>
    </div>
    <%-- 开票申请单删除 --%>
    <div class="bonceContainer bounce-red" id="ticketInfoDeleTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p style="text-align: center">
                确定删除该张发票申请单吗？
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="okDel()">确定</span>
        </div>
    </div>
    <%-- 开票申请单查看 --%>
    <div id="ticketInfoScan" class="bonceContainer bounce-blue">
        <div class="bonceHead">
            <span>开票申请单</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>本张开票申请中共选择了 <span id="tikNum"></span>种商品，
                需开具 <span id="tilCat"></span>，
                含税总金额为 <span id="tikSum"></span>元</p>
            <p>客户名称：<span id="tikCus"></span></p>
            <p>申请人：<span id="tickApply"></span> <span>创建时间：</span><span id="tickTime"></span></p>
            <table class="ty-table" id="tab3">
                <tr>
                    <td>序号</td>
                    <td>商品名称</td>
                    <td>单位</td>
                    <td>数量</td>
                    <td>单价</td>
                    <td>金额</td>
                    <td>税率</td>
                    <td>税额</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--新增订单--%>
    <div class="bonceContainer bounce-green" id="newOrder"  style="min-width: 1098px; width:90%; ">
        <div class="bonceHead">
            <span>新增订单</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height:450px; overflow: auto">

            <div class="orderDetail">
                <div class="flexcon">
                    <div><p>客户名称 <i class="red">*</i></p>
                        <div class="customersSearch">
                            <input type="text" id="cusSearchInput" require class="form-control customerName" onchange=""/>
                            <div class="cusSearchItems">  </div>
                        </div>
                    </div>
                    <div><p>订单号 <i class="red">*</i></p><input type="text" require class="form-control orderNumber" placeholder="请录入"></div>
                    <div><p>订单收到日期 <i class="red">*</i></p><input type="text" require id="OrderReceivedDate" class="form-control" placeholder="请选择"></div>
                    <div><p>客户代号 </p><input type="text" class="form-control customerId" disabled="disabled"></div>
                </div>
                <div class="flexcon">
                    <div>
                        <p>收货地点<i class="red">*</i>
                            <span class="linkBtn uphide" data-fun="newReceiveInfo">新增收货地点</span>
                            <span class="linkBtn uphide" data-fun="addressExplain">功能说明</span>
                        </p>
                        <select class="form-control receiveAddress receiveAddress1" require></select>
                    </div>
                    <div style="position: relative;">
                        <p>生产方的评审负责人<i class="red">*</i></p>
                        <select class="form-control principal " require ></select>
                        <span class=" uphide" style="position: absolute;left: 1px;top: 63px;color: #5a94ff;font-size: 0.7em;">注：需为多人时，订单需拆为多个录入</span>
                    </div>
                    <div><p>开票要求 <i class="red">*</i><span style="position: relative; left: 134px;" class="linkBtn uphide" data-fun="invoiceRequireEdit">编辑</span></p>
                        <input type="text" class="form-control invoiceRequire"  require readonly onclick="invoiceRequireEdit()">
                    </div>
                    <div><p>订单总额 </p><input type="text" class="form-control orderTotal" disabled="disabled"></div>
                </div>
            </div>
            <div class="orderList" style=" margin-top: 12px;">
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5 newGood" onclick="newGoodBtn('add' , 2)" disabled="">选择专属商品</button>
                <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5 newGoodCommon" onclick="newGoodBtn('add' , 1)" disabled="">选择通用型商品</button>
                <table class="ty-table ty-table-control goodList canOrder">
                    <thead>
                    <tr>
                        <td>商品代号</td>
                        <td>商品名称</td>
                        <td>单价</td>
                        <td>计量单位</td>
                        <td>数量</td>
                        <td>要求到货日期</td>
                        <td>收货地点</td>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="gsBd">
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5 canOrder" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 canOrder" id="sureNewOrder" onclick="sureNewOrder()">保存</span>
        </div>
    </div>
    <%--查看订单--%>
    <div class="bonceContainer bounce-blue" id="seeOrder"  style="width:90%; min-width:1000px; ">
        <div class="bonceHead">
            <span>订单详情</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 500px; overflow: auto;">
            <div class="ty-panel" style="padding-right:175px;position: relative; ">
                <span class="ty-btn ty-btn-big ty-btn-green ty-circle-5 ty-right ri" onclick="seeOrderOral()">原始查看订单</span>
                <span class="ty-btn ty-btn-big ty-btn-green ty-circle-5 ty-right ri" style="top: 100px;" onclick="ticketErrorLogBtn()">发票异常记录</span>
                <div class="tpl">
                    <table id="ordinfo" class="ty-table">
                        <tr>
                            <td width="10%">客户名称</td>
                            <td colspan="5" width="60%" id="see_cusName2"></td>
                            <td width="10%">客户代号</td>
                            <td width="20%" id="see_cusId2"></td>
                        </tr>
                        <tr>
                            <td>收货地点</td>
                            <td colspan="3" width="40%" id="address2"></td>
                            <td width="7%">收货人</td>
                            <td width="13%" id="addrPerson"></td>
                            <td>收货人电话</td>
                            <td id="addrPhone"></td>
                        </tr>
                        <tr>
                            <td>订单号</td>
                            <td id="see_sn2" width="15%"></td>
                            <td width="10%">订单收到日期</td>
                            <td id="see_signDate2" width="15%"></td>
                            <td>录入者</td>
                            <td id="see_createName2"></td>
                            <td>原始录入时间</td>
                            <td id="see_createDate2"></td>
                        </tr>

                    </table>
                </div>
                <input type="hidden" id="ordID">
            </div>
            <div class="ty-panel tpl" style="padding-right:190px;position: relative; ">
                <span class="ty-btn ty-btn-big ty-btn-green ty-circle-5 ty-right ri" id="scanEndOrdLog"
                      onclick="endOrdLog()">订单终止审批记录</span>
                <div class="tpl">
                    <table id="ordTicket" class="ty-table">
                        <tr>
                            <td class="bg_ea" colspan="3">开票信息汇总</td>
                            <td class="bg_d" colspan="3">发票送达信息</td>
                            <td colspan="4" class="bg_c">回款信息汇总</td>
                        </tr>
                        <tr>
                            <td width="10%" class="bg_ea">发票日期</td>
                            <td width="10%" class="bg_ea">发票号码</td>
                            <td width="10%" class="bg_ea">发票金额</td>
                            <td width="10%" class="bg_d">送达方式</td>
                            <td width="10%" class="bg_d">送达日期</td>
                            <td width="10%" class="bg_d">接收人</td>
                            <td width="10%" class="bg_c">回款日期</td>
                            <td width="10%" class="bg_c">回款金额</td>
                            <td width="10%" class="bg_c">收入方式</td>
                            <td width="10%" class="bg_c">票据号码</td>
                        </tr>
                        <tr>
                            <td class="bg_ea">2017-8-19</td>
                            <td class="bg_ea">12140002</td>
                            <td class="bg_ea">850</td>
                            <td class="bg_d">快递</td>
                            <td class="bg_d">2017-8-19</td>
                            <td class="bg_d">接收人</td>
                            <td class="bg_c">2017-8-19</td>
                            <td class="bg_c">1214</td>
                            <td class="bg_c">——</td>
                            <td class="bg_c">——</td>
                        </tr>
                        <tr>
                            <td class="bg_ea">2017-8-19</td>
                            <td class="bg_ea">12140002</td>
                            <td class="bg_ea">850</td>
                            <td class="bg_d">快递</td>
                            <td class="bg_d">2017-8-19</td>
                            <td class="bg_d">接收人</td>
                        </tr>

                    </table>
                </div>
            </div>
            <div class="ty-panel tpl">
                <table id="goodsList" class="ty-table">
                    <tr>
                        <td rowspan="2">序号</td>
                        <td rowspan="2">商品代号</td>
                        <td rowspan="2">外部名称</td>
                        <td rowspan="2">产品代号</td>
                        <td rowspan="2">内部名称</td>
                        <td rowspan="2">单位</td>
                        <td rowspan="2">数量</td>
                        <td rowspan="2">要求到货日期</td>
                        <td colspan="2">发货记录</td>
                        <td colspan="2">签收记录</td>
                        <td colspan="4">开票记录</td>
                    </tr>
                    <tr>
                        <td>发货时间</td>
                        <td>数量</td>
                        <td>实际签收日期</td>
                        <td>数量</td>
                        <td>发票日期</td>
                        <td>发票号码</td>
                        <td>数量</td>
                        <td>金额</td>
                    </tr>
                    <tr>
                        <td>序号</td>
                        <td>商品代号</td>
                        <td>外部名称</td>
                        <td>产品代号</td>
                        <td>内部名称</td>
                        <td>单位</td>
                        <td>数量</td>
                        <td>要求到货日期</td>
                        <td>发货时间</td>
                        <td> 数量</td>
                        <td>实际签收日期</td>
                        <td>数量</td>
                        <td>发票日期</td>
                        <td>发票号码</td>
                        <td>数量</td>
                        <td>金额</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--删除订单--%>
    <div class="bonceContainer bounce-red" id="deleteOrder">
        <div class="bonceHead">
            <span>系统提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="msTip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="deleteOrder()">确定</span>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
        </div>
    </div>
    <%--评审记录--%>
    <div class="bonceContainer bounce-blue" id="approvePRHistory"  style="min-width:1000px; width:95%;">
        <div class="bonceHead">
            <span>评审记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clearfix approveInfo">
                <div class="orderItem">
                    <div class="orderItemTitle">客户名称</div>
                    <div class="orderItemCon" id="cusName"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">客户代号</div>
                    <div class="orderItemCon" id="cusCode"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">订单号</div>
                    <div class="orderItemCon" id="ordSn"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">订单收到日期</div>
                    <div class="orderItemCon" id="ordReciveDate"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">录入者</div>
                    <div class="orderItemCon" id="creator"></div>
                </div>
                <div class="orderItem">
                    <div class="orderItemTitle">录入时间</div>
                    <div class="orderItemCon" id="createDate"></div>
                </div>
            </div>
            <div class="approveHistoryList">
                <div class="ty-dropdown">
                    <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 ty-dropdownBtn" target="roleCon" id="setRole" onclick="setRoleBtn( $(this) )" >筛 选</span>
                    <div class="ty-dropdownCon" id="roleCon" >
                        <div class="ty-trigl-1"><span></span></div>
                        <div>
                            <div class="orderItemTiny"><i isSet="1" code="1" class="fa fa-dot-circle-o"></i>商品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="2" class="fa fa-circle-o"></i>外部名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="3" class="fa fa-circle-o"></i>产品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="4" class="fa fa-circle-o"></i>内部名称</div>
                        </div>
                        <div>
                            <div class="orderItemTiny"><i isSet="0" code="5" class="fa fa-square-o"></i>商品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="6" class="fa fa-square-o"></i>外部名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="7" class="fa fa-square-o"></i>产品代号</div>
                            <div class="orderItemTiny"><i isSet="0" code="8" class="fa fa-square-o"></i>内部名称</div>
                            <div class="orderItemTiny"><i isSet="0" code="9" class="fa fa-square-o"></i>单位</div>
                            <div class="orderItemTiny"><i isSet="0" code="10" class="fa fa-square-o"></i>含税单价</div>
                            <div class="orderItemTiny"><i isSet="0" code="11" class="fa fa-check-square-o"></i>备注</div>
                        </div>
                        <div>
                            <span class="ty-btn ty-circle-3" onclick="cancelSert($('#setRole'))">取消</span>
                            <span class="ty-btn ty-btn-green ty-circle-3" onclick="setRole($('#historyList') , $('#setRole') , 'roleCon' )">确定</span>
                        </div>
                    </div>
                </div>

                <div style="height:250px;overflow:auto; ">
                    <table class="ty-table ty-table-control">
                        <thead>
                        <tr>
                            <td width="5%">序号</td>
                            <td width="10%">商品代号</td>
                            <td width="5%">数量</td>
                            <td width="10%">要求到货日期</td>
                            <td width="10%">能否按时到货</td>
                            <td width="10%">按期到货数量</td>
                            <td width="10%">剩余数量到货日期</td>
                            <td width="10%">评审时间</td>
                            <td width="10%">评审人</td>
                            <td width="10%">处理时间</td>
                            <td width="10%">处理人</td>
                        </tr>
                        </thead>
                        <tbody id="historyList"> </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--系统提示--%>
    <div class="bonceContainer bounce-red" id="msTips">
        <div class="bonceHead">
            <span>系统提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="msTip" id="tip1"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 开票提示 --%>
    <div class="bonceContainer bounce-red" id="msTips2">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div class="msTip"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
    <%-- 折线图 --%>
    <div class="bonceContainer bounce-blue" id="chart">
        <div class="bonceHead">
            <span id="ttl"></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div id="flow">
                <p>2017年11月10日已发出该订单10%的货物</p>
                <p>2017年11月15日已发出该订单90%的货物</p>
            </div>
            <div class="warming">
                <p><span>！重要提示</span> 下列发货未完成的订单中包含与本订单相同的货物。如必要您需尽早协调，以免误事。</p>
                <div style="max-height:200px; overflow: auto; ">
                    <table class="ty-table" id="warmingTbl">
                        <tr>
                            <td>序号</td>
                            <td>订单号</td>
                            <td>负责人</td>
                            <td>客户名称</td>
                        </tr>
                        <tr>
                            <td>1</td>
                            <td>352</td>
                            <td>柳随风</td>
                            <td>小米科技有限公司</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div id="linechartContainer" style="width:700px; height:300px;"></div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%-- 终止订单 --%>
    <div class="bonceContainer bounce-red" id="endOrd">
        <div class="bonceHead">
            <span>订单终止申请</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-indent: 2em;">
            <div class="ty-panel" id="endTip">
                <p>该订单的货物尚未完全交付。</p>
            </div>
            <div class="ty-panel">
                <p>若依然要终止该订单，请输入终止原因：</p>
                <textarea id="end_reason"></textarea>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="endOrdOk()">确定</span>
        </div>
    </div>
    <%-- 发票信息 --%>
    <div id="ticketTip" class="bonceContainer bounce-blue">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            本公司的开票规则
            <table class="ty-table">
                <tr>
                    <td>发票种类</td>
                    <td>每张可开行数</td>
                    <td>每张可开最高金额</td>
                </tr>
            </table>

            <p class="marTop">为方便财务开票，您填开的开票申请单也需遵循上述规则，超过上限时则需填开多张。</p>
            <p style="cursor: default" onclick="toggleIcon($(this))"><i class="fa fa-square-o"></i> 我知道了，以后不再提示</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="ticketTipOk()">OK，填写第一张开票申请单</span>
        </div>
    </div>
    <%-- 开票提示的确定 --%>
    <div id="ticketInfo" class="bonceContainer bounce-blue">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>本张开票申请中共选择了 <span id="count"></span> 种商品，需开具 <span id="cat"></span>，含税总金额为<span id="amount"></span>元</p>
            <table class="ty-table" id="tab1">
                <tr>
                    <td>序号</td>
                    <td>商品名称</td>
                    <td>单位</td>
                    <td>数量</td>
                    <td>单价</td>
                    <td>金额</td>
                    <td>税率</td>
                    <td>税额</td>
                </tr>
            </table>
            <p class="marTop">请选择下一步：</p>
            <div class="i-blueCon">
                <p onclick="togggle2($(this) , 1)"><i class="fa fa-circle-o"></i> 生成本张开票申请单， 并填写下一张</p>
                <p onclick="togggle2($(this) , 2)"><i class="fa fa-circle-o"></i> 生成本张开票申请单， 并提交审批</p>
                <p onclick="togggle2($(this) , 3)"><i class="fa fa-circle-o"></i> 重新填写本张开票申请单</p>
                <div class="contractTip">
                    <hr style=" border-bottom: 1px solid #ccc;">
                    <p onclick="togggle2($(this) , 4)"><i class="fa fa-square-o" id="cheContract4" style="font-weight: bold; font-size: 16px; margin: 0 10px 0 0;"></i>客户开票资料没有变化，<span class="red">或虽有变化，但已在系统中修改完毕</span>
                        <span class="ty-right linkBtn" onclick="getInvoiceMessage()">查看开票资料</span></p>
                </div>
                <input type="hidden" id="tik1">
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" id="ticketInfoOk" onclick="ticketInfoOk()">确定</span>
        </div>
    </div>
    <%-- 查看开票申请单 --%>
    <div id="scanTickInfo" class="bonceContainer bounce-blue">
        <div class="bonceHead">
            <span>查看开票申请单</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p><span class="mar">本次已填写如下 <span id="tick1"></span> 张开票申请单，申请开票总金额为 <span id="tickSum"></span> 元 </span>
                <span class="ty-btn ty-right ty-btn-big ty-btn-blue ty-circle-5 mar" onclick="showTp()">一并提交审批</span>
            </p>
            <p>客户名称：<span id="cusTick"></span></p>
            <p>申请人： <span id="applier"></span></p>

            <p onclick="togggle2($(this) , 5)"><i style="font-weight: bold;color: #5a94ff;font-size: 16px;margin-right: 10px;position: relative;top: 2px;" class="fa fa-square-o" id="cheContract5"></i>客户开票资料没有变化，<span class="red">或虽有变化，但已在系统中修改完毕</span>
                <span style="position: relative;  top: -12px;" class="ty-btn ty-right ty-btn-big ty-btn-blue ty-circle-5 mar" onclick="getInvoiceMessage()">查看开票资料</span>
            </p>
            <table class="ty-table ty-table-control" id="tab2">
                <tr>
                    <td>序号</td>
                    <td>创建时间</td>
                    <td>发票种类</td>
                    <td>金额</td>
                    <td>行数</td>
                    <td>操作</td>
                </tr>

            </table>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%-- 查看已选的货物 --%>
    <div id="scanGoodsInfo" class="bonceContainer bounce-blue">
        <div class="bonceHead">
            <span>本次开票申请已选的货物</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span class="mar">本次已填写<span id="inNum"></span>张开票申请单，申请开票总金额为<span id="inSum"></span>元 ，已选的货物如下： </span>
            </p>
            <p>客户名称：<span id="inCus"></span></p>
            <p>申请人：<span id="inApply"></span></p>
            <table class="ty-table" id="tab4">
                <tr>
                    <td>商品名称</td>
                    <td>单位</td>
                    <td>单价</td>
                    <td>是否开票</td>
                    <td>发票种类</td>
                    <td>订购数量</td>
                    <td>已发货数量</td>
                    <td>已签收数量</td>
                    <td>已开票数量</td>
                    <td>申请中的数量</td>
                    <td>可申请数量上限</td>
                    <td>本次申请数量</td>
                </tr>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 查看待补发订单--%>
    <div id="noSupplyingOrder" class="bonceContainer bounce-blue" style="min-width:1000px;">
        <div class="bonceHead">
            <span>尚未补发订单的回款</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon scanNoSupplying">
            <ul class="clear">
                <li>
                    <span class="tt-row">客户名称</span>
                    <span class="collectNameSl"></span>
                </li>
                <li>
                    <span class="tt-row">回款金额</span>
                    <span class="collectAmontSl"></span>
                </li>
                <li>
                    <span class="tt-row">收入方式</span>
                    <span class="collectTypeSl"></span>
                </li><%--'回款方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐'--%>
                <li class="cashTypeSl">
                    <span class="tt-row">收到日期</span>
                    <span class="cashReciveDateSl"></span>
                </li>
                <li class="chequeTypeSl">
                    <span class="tt-row">收到支票日期</span>
                    <span class="chequeReciveDateSl"></span>
                </li>
                <li class="chequeTypeSl">
                    <span class="tt-row">支票号</span>
                    <span class="chequeSnSl"></span>
                </li>
                <li class="chequeTypeSl">
                    <span class="tt-row">支票到期日</span>
                    <span class="chequeDueDateSl"></span>
                </li>
                <li class="chequeTypeSl">
                    <span class="tt-row">出具支票单位</span>
                    <span class="chequeUnitSl"></span>
                </li>
                <li class="chequeTypeSl">
                    <span class="tt-row">支票银行</span>
                    <span class="chequeBankSl"></span>
                </li>
                <li class="billTypeSl">
                    <span class="tt-row">收到汇票日期</span>
                    <span class="billReceiveDateSl"></span>
                </li>
                <li class="billTypeSl">
                    <span class="tt-row">汇票号</span>
                    <span class="billSnSl"></span>
                </li>
                <li class="billTypeSl">
                    <span class="tt-row">汇票到期日</span>
                    <span class="billDueDateSl"></span>
                </li>
                <li class="billTypeSl">
                    <span class="tt-row">原始出具汇票单位</span>
                    <span class="billDueUnitSl"></span>
                </li>
                <li class="billTypeSl">
                    <span class="tt-row">出具汇票银行</span>
                    <span class="billDueBankSl"></span>
                </li>
                <li class="bankTypeSl">
                    <span class="tt-row">到账日期</span>
                    <span class="bankReceiveDate"></span>
                </li>
                <li class="bankTypeSl">
                    <span class="tt-row">收款银行</span>
                    <span class="bankReceiveBank"></span>
                </li>
            </ul>
            <div class="recordSect clear">
                <p>录入者：<span class="recordInputer infoSize"></span>&nbsp;&nbsp;<span class="recordInputerTime"></span></p>
                <p>财&nbsp;&nbsp;&nbsp;务：<span class="recordFinance infoSize"></span>&nbsp;&nbsp;<span class="recordFinanceTime"></span></p>
                <p>销&nbsp;&nbsp;&nbsp;售：<span class="recordSale infoSize"></span>&nbsp;&nbsp;<span class="recordSaleTime"></span></p>
            </div>
            <div class="haveOrgOrders">
                <p class="manageTip">本笔回款中<span id="disedAmount"></span>元属于以下订单，剩余的<span id="lastAmount"></span>元需补发订单。</p>
                <table class="ty-table ty-table-control orderAgain">
                    <thead>
                    <tr>
                        <td>订单号</td>
                        <td>订单金额</td>
                        <td>已回款比例</td>
                        <td>已回款金额</td>
                        <td>本笔回款中应属于该订单的金额</td>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">我知道了</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <div id="picShow" style="display: none;">
        <img src=""/>
    </div>
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>销售订单</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div>
                    <div class="mainArea">
                    <span class="Search">
                        <input id="se0" type="text" placeholder="客户名称/客户代号"/>
                        <span class="se" onclick="searchOedsByCus(0)">搜索</span>
                    </span>
                        <span class="ty-right">
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" id="endBtn">被终止的订单</span>
                    </span>
                    </div>
                    <div class="endArea">
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5 mar" onclick="goPage(0)">返回</span>
                        <span class="Search">
                        <input id="se1" type="text" placeholder="客户名称/客户代号" class="ty-searchInput"/>
                        <span class="se" onclick="searchOedsByCus(1)">搜索</span>
                    </span>
                        <div class="ty-btn-group ty-right">
                            <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-active-blue" id="yearBtn"
                                  onclick="getList_year($(this))">本年</span>
                            <span class="ty-btn ty-btn-big ty-circle-5" id="searchBtn"
                                  onclick="searchBtn(event)">自定义</span>
                            <ul class="dropdown-menu dropdown-menu-default searchCon" onclick="stop(event)">
                                <span class="trigle"></span>
                                <li>
                                    <span class="ttl">时间范围：</span><input type="text" class="laydate-icon"
                                                                         id="searchStart">
                                </li>
                                <li>
                                    <span class="ttl">到：</span><input type="text" class="laydate-icon" id="searchEnd">
                                </li>
                                <li class="ctl">
                                    <span class="ty-btn ty-circle-5 ty-btn-gray"
                                          onclick="$('.searchCon').hide();">取消</span>
                                    <span class="ty-btn ty-circle-5 ty-btn-green" onclick="searchDIY(event)">查询</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="ticketOpen">
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5 mar" onclick="returnDele()">返回</span>
                        <span class="mar" id="ticket1">
                            <span class="mar">本次已填写<span id="applicationNum"></span>张开票申请单</span>
                            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5 mar"
                                  onclick="scanTickInfo()">查看开票申请单</span>
                            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5"
                                  onclick="scanGoodsInfo()">查看本次已选的货物</span>
                        </span>
                    </div>
                    <div class="supplyAgain">
                        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="goPage(0)">返回</span>
                    </div>
                </div>
                <div>
                    <%-- 订单管理主页面 --%>
                    <div class="mainArea ty-mainData">
                        <div class="ty-panel">
                            <p>当前共有 <span id="ordNum"></span> 个订单处于进程中。</p>
                            <p>表格里的各项百分比中，已开票与已回款为金额百分比，其余为货物数量百分比。</p>
                        </div>
                        <div class="tpl">
                            <table class="ty-table ty-table-control orderTable">
                                <thead>
                                <tr>
                                    <td width="5%">序号</td>
                                    <td width="10%">订单收到日期</td>
                                    <td width="10%">订单号</td>
                                    <td width="10%">客户名称</td>
                                    <td width="8%">录入时间</td>
                                    <td width="7%">订单详情</td>
                                    <td width="10%">可发货</td>
                                    <td width="10%">已发货</td>
                                    <td width="10%">已签收</td>
                                    <td width="10%">已开票</td>
                                    <td width="10%">已回款</td>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <%--分页容器--%>
                        <div id="ye_orderManage"></div>
                    </div>
                    <%-- 查询被终止的订单 --%>
                    <div class="endArea">
                        <div class="ty-panel">
                            <p><span id="endOrdDuring">X年X月X日—X年X月X日间</span> ，共有 <span id="ordNum2">666</span> 个被终止的订单<span id="isSearch"></span>。</p>
                            <p>表格里的各项百分比中，已开票与已回款为金额百分比，其余为货物数量百分比。</p>
                        </div>
                        <div class="tpl">
                            <table class="ty-table ty-table-control orderTableEnd">
                                <thead>
                                <tr>
                                    <td width="5%">序号</td>
                                    <td width="10%">订单收到日期</td>
                                    <td width="8%">订单号</td>
                                    <td width="10%">客户名称</td>
                                    <td width="10%">录入时间</td>
                                    <td width="10%">终止时间</td>
                                    <%--<td width="5%">终止人</td>--%>
                                    <%--<td width="7%">评审记录</td>--%>
                                    <td width="10%">订单详情</td>
                                    <td width="5%">可发货</td>
                                    <td width="5%">已发货</td>
                                    <td width="5%">已签收</td>
                                    <td width="5%">已开票</td>
                                    <td width="5%">已回款</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>序号</td>
                                    <td>订单收到日期</td>
                                    <td>订单号</td>
                                    <td>客户名称</td>
                                    <td>录入时间</td>
                                    <td>终止时间</td>
                                    <td>终止人</td>
                                    <td class="ty-td-control"><span class="ty-color-blue"
                                                                    onclick="chargeHistory( 'ordID' , $(this) )">查看</span>
                                    </td>
                                    <td class="ty-td-control"><span class="ty-color-blue" onclick="seeOrderBtn($(this))">查看订单详情</span>
                                    </td>
                                    <td class="ty-td-control"><span class="ty-color-blue" onclick="lineChart($(this) , 1)">90%</span>
                                    </td>
                                    <td class="ty-td-control"><span class="ty-color-blue" onclick="lineChart($(this) , 2)">90%</span>
                                    </td>
                                    <td class="ty-td-control"><span class="ty-color-blue" onclick="lineChart($(this) , 3)">90%</span>
                                    </td>
                                    <td class="ty-td-gray"></td>
                                    <td class="ty-td-gray"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <%--分页容器--%>
                        <div id="ye_orderManage2"></div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/companyOverview/orderManage.js?v=SVN_REVISION" type="text/javascript"></script>
<%--<script src="../script/sales/orderManegeCommon.js?v=SVN_REVISION" type="text/javascript"></script>--%>
<script src="../script/cashBack/payBack.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/common/echarts.common.min.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
