<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../css/projectsManage/baseManage.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" onclick="$('.searchCon').hide();">
<div class="bounce">
    <%-- 立案申请的审批  --%>
    <div class="bonceContainer bounce-red" id="registerPro">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span>您确定要驳回吗？</span><br> <textarea id="registerRejectReason" ></textarea>
            </p>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="liProOk()">确定</span>
        </div>
    </div>
    <%-- 结案申请的审批  --%>
    <div class="bonceContainer bounce-red" id="endProApply">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p>
                <span>您确定要驳回吗？</span><br> <textarea id="endProApplyReason" placeholder="200字以内"></textarea>
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="endProOk()">确定</span>
        </div>
    </div>
    <%-- 结案  --%>
        <div class="bonceContainer bounce-red" id="endProContent">
            <div class="bonceHead">
                <span>提示信息</span>
                <a class="bounce_close" onclick="clearInterval(timerSetendProOK) ;bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <p>
                    <span>实际结案日期<i class="xing">*</i></span></span><br> <input type="text" id="endProDate" readonly>
                </p>
                <p>
                    <span>请输入结案意见<i class="xing">*</i></span></span><br> <textarea id="endProComment"
                                                                                   placeholder="150字以内"></textarea>
                </p>
            </div>
            <div class="bonceFoot">
        <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5"
              onclick="clearInterval(timerSetendProOK) ;bounce.cancel()">取消</span>
                <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" id="endProOK" onclick="endProOK()">确定</span>
            </div>
        </div>
    <%-- 删除立案者 tip 提示框  --%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
            <div class="hd" id="tipID"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="deleteLiOK()">确定</span>
        </div>
    </div>
    <%--  creator:hxz 2018-06-04  分工设置人员列表  --%>
    <div class="bonceContainer bounce-green" id="addLabourSet">
        <div class="bonceHead">
            <span>请确定何人可拥有项目管理立案的权限</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <div class="ty-colFileTree"></div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="labourSetOk()">确定</span>
        </div>
    </div>
    <%--  creator:hxz 2018-06-04  变更立案者  --%>
    <div class="bonceContainer bounce-blue" id="change">
        <div class="bonceHead">
            <span>变更立案者</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon"  >
            <p><span>项目名称：</span><span id="peoName"></span></p>
            <p><span>立案申请者：</span><select id="peoLi">
                <option value='{ "userid" : 1 , "name": "zhangsan" }'>zhangsan</option>
            </select></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="changeOk()">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>综合管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <div>
                    <%-- 分工设置页面 --%>
                    <div class="labourSet">
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="goPage(0)">返回</span>
                    </div>
                    <%-- 综合管理主页面 --%>
                    <div class="ty-right proMain minControl">
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="goPage(1)">分工设置</span>
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="goPage(3)">新增项目</span>
                    </div>
                    <%-- 新增项目 --%>
                    <div class="addPro">
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="goPage(0)">返回</span>
                        <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5 addProOk"
                              onclick="addProOk()">确定新增</span>
                    </div>
                    <%-- 结案通过 --%>
                    <div class="endSuccess">
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="goPage(0,1)">返回</span>
                    </div>
                </div>
                <div>
                    <%-- 综合管理主页面 --%>
                    <div class="proMain">
                        <%-- 二级导航 --%>
                        <ul class="ty-secondTab">
                            <li class="ty-active">立案待审批</li>
                            <li>立案驳回</li>
                            <li>立案通过</li>
                            <li>结案待审批</li>
                            <li>结案驳回</li>
                            <li>结案通过</li>
                        </ul>
                        <%-- 结案通过 --%>
                        <div class="endSuccess endSuccessCon">
                            <span> <span id="during">自2018年02月01日—2018年02月20日，</span> 已结案的项目共 <span id="num"></span>个</span>
                            <div class="ty-btn-group ty-right" id="changeState">
                                <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-active-blue" >本年</span>
                                <span class="ty-btn ty-btn-big ty-circle-5">去年</span>
                                <span class="ty-btn ty-btn-big ty-circle-5">前年</span>
                                <span class="ty-btn ty-btn-big ty-circle-5">自定义时间</span>
                                <ul class="dropdown-menu dropdown-menu-default searchCon" onclick="stop(event)" >
                                    <span class="trigle"></span>
                                    <li>
                                        <span class="ttl">时间范围：</span><input type="text" class="laydate-icon" id="searchStart">
                                    </li>
                                    <li>
                                        <span class="ttl">到：</span><input type="text" class="laydate-icon" id="searchEnd">
                                    </li>
                                    <li class="ctl">
                                        <span class="ty-btn ty-circle-5 ty-btn-gray" onclick="$('.searchCon').hide();" >取消</span>
                                        <span class="ty-btn ty-circle-5 ty-btn-green" onclick="searchDIY(event)">查询</span>
                                    </li>
                                </ul>
                            </div>

                        </div>
                        <%-- 立案待审批、驳回、通过 --%>
                        <table class="ty-table ty-table-control" id="build">
                            <thead>
                            <td>项目名称</td>
                            <td>项目编号</td>
                            <td>类别</td>
                            <td>标签</td>
                            <td>项目负责人</td>
                            <td>预计开始日期</td>
                            <td>预计完成日期</td>
                            <td>立案申请者</td>
                            <td>操作</td>
                            </thead>
                            <tbody id="buildTbl">  </tbody>
                        </table>
                        <%-- 结案待审批、驳回、通过 --%>
                        <table class="hd ty-table ty-table-control" id="end" >
                            <thead>
                            <td>项目名称</td>
                            <td>项目编号</td>
                            <td>类别</td>
                            <td>标签</td>
                            <td>项目负责人</td>
                            <td>实际结案日期</td>
                            <td>结案者</td>
                            <td>操作</td>
                            </thead>
                            <tbody id="endPro">  </tbody>
                        </table>

                        <div class="endSuccess" id="ye"></div>
                    </div>
                    <%-- 分工设置页面 --%>
                    <div class="labourSet">
                        <p class="labourSetP">
                            <span>已有如下 <span id="liNum"></span> 人拥有项目管理立案的权限：</span>
                            <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="addLabourSetBtn()">新增</span>
                        </p>
                        <table class="ty-table ty-table-control">
                            <thead>
                            <td>姓名</td>
                            <td>性别</td>
                            <td>手机号</td>
                            <td>部门</td>
                            <td>职位</td>
                            <td>操作</td>
                            </thead>
                            <tbody id="labourSetTbl">  </tbody>
                        </table>
                    </div>
                    <%-- 新建项目 --%>
                    <div class="addPro">
                        <div id="addproCon">
                            <div class="proTr">
                                <span class="ttl">项目名称<i class="xing">*</i></span>
                                <input type="text" id="add_proName" onchange="setaddProOk()" placeholder="20字以内">
                                <span class="ttl">类别</span>
                                <input type="text" id="add_proKind" placeholder="15字以内">
                            </div>
                            <div class="proTr">
                                <span class="ttl">标签</span>
                                <input type="text" id="add_proTip" placeholder="15字以内">
                                <span class="ttl">项目编号</span>
                                <input type="text" id="add_proNo" placeholder="18字以内">
                            </div>
                            <div class="proTr">
                                <span class="ttl">立项依据</span>
                                <input type="text" id="add_proBy" placeholder="30字以内">
                                <span class="ttl">项目负责人</span>
                                <input type="text" id="add_proFu" placeholder="8字以内">
                            </div>
                            <div class="proTr">
                                <span class="ttl">小组成员</span>
                                <input type="text" id="add_users" class="w800" placeholder="60字以内">
                            </div>
                            <div class="proTr">
                                <span class="ttl">现状描述</span>
                                <textarea id="add_curShow" placeholder="100字以内" class="h100"> </textarea>
                            </div>
                            <div class="proTr">
                                <span class="ttl">立项目的</span>
                                <textarea id="add_purpose" placeholder="200字以内" class="h200"></textarea>
                            </div>
                            <div class="proTr">
                                <span class="ttl">项目简介</span>
                                <textarea id="add_abstract" placeholder="300字以内" class="h300"></textarea>
                            </div>
                            <div class="proTr">
                                <span class="ttl">进度计划</span>
                                <span class="ttlBig">预计开始日期</span>
                                <input  id="add_proBeginDate" readonly>
                                <span class="ttlBig">预计完成时间</span>
                                <input  id="add_proEndDate" readonly>
                            </div>
                            <div class="proTr">
                                <span class="ttl">相关展望</span>
                                <textarea id="add_outLook" placeholder="300字以内" class="h300"></textarea>
                            </div>
                            <div class="proTr">
                                <span class="ttl">输出资料</span>
                                <textarea id="add_outResource" placeholder="100字以内" class="h100"></textarea>
                            </div>
                            <div class="proTr">
                                <span class="ttl">附件</span>
                                <div id="add_accessory" class="panel ">
                                    <div id="add_fu"></div>
                                    <div class="clr"></div>
                                    <p style="padding:15px 0;"><span class="ty-btn ty-btn-green ty-circle-5 mar"
                                                                     onclick="uplooad()">添加附件</span></p>
                                    <div class="hd">
                                        <div id="fileUpload"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="proTr">
                                <span class="ttl">备注</span>
                                <textarea id="add_memo" placeholder="200字以内" class="h200"></textarea>
                            </div>
                            <div class="proTr ty-center">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5 addProOk"
                                  onclick="addProOk()">确定新增</span>
                            </div>

                        </div>
                    </div>
                    <%-- 立案,结案查看页面 --%>
                    <div class="proScan">
                        <p class="proScanP">
                            <span class="ty-right ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="goPage(0,2)">返回</span>
                        </p>
                        <div class="scanPro">
                            <div class="proTr">
                                <span class="ttl">项目名称<i class="xing"></i></span>
                                <span class="con" id="scan_proName"></span>
                                <span class="ttl">类别</span>
                                <span class="con" id="scan_proKind"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">标签</span>
                                <span id="scan_proTip" class="con"></span>
                                <span class="ttl">项目编号</span>
                                <span class="con" id="scan_proNo"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">立项依据</span>
                                <span class="con" id="scan_proBy"></span>
                                <span class="ttl">项目负责人</span>
                                <span class="con" id="scan_proFu"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">小组成员</span>
                                <span id="scan_users" class="w800 con"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">现状描述</span>
                                <span id="scan_curShow"  class="h100 textarea con"> </span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">立项目的</span>
                                <span id="scan_purpose" class="h200 textarea con"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">项目简介</span>
                                <span id="scan_abstract" class="h300 con textarea"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">进度计划</span>
                                <span class="ttlBig">预计开始日期</span>
                                <span  id="scan_proBeginDate" class="con lit"></span>
                                <span class="ttlBig">预计完成时间</span>
                                <span  id="scan_proEndDate" class="con lit"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">相关展望</span>
                                <span id="scan_outLook" class="h300 textarea con"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">输出资料</span>
                                <span id="scan_outResource" class="h100 textarea con"></span>
                            </div>
                            <div class="proTr">
                                <span class="ttl">附件</span>
                                <div id="scan_accessory" style="height: auto!important;" class="panel textarea con  ">
                                </div>
                            </div>
                            <div class="proTr">
                                <span class="ttl">备注</span>
                                <span id="scan_memo" class="h200 con textarea"></span>
                            </div>

                            <div class="endProInfo proTr">
                                <span class="ttl">实际结案日期</span>
                                <span id="scan_time" class="con lit"></span>
                            </div>
                            <div class="endProInfo proTr">
                                <span class="ttl">结案意见</span>
                                <span id="scan_Opinion" class="h200 con textarea"></span>
                            </div>
                        </div>

                        <%-- 审批流程 --%>
                        <div class="chargeCon">
                            <p onclick="showFL($(this))">审批记录 <i class="fa fa-angle-double-down"></i></p>
                            <div class=" ty-process-container" id="flowList">
                                <div class="ty-process-item">
                                    <p><span class="dot"></span>结案已批准 </p>
                                    <article>
                                        <p><span>结案审批者</span> <span>刘洪涛</span> <span>2017-11-11 10:10</span></p>
                                        <p><span>结案申请者</span> <span>刘洪涛</span> <span>2017-11-11 10:10</span></p>
                                    </article>
                                </div>
                                <div class="ty-process-item ">
                                    <p><span class="dot-no"></span>立案已批准</p>
                                    <article>
                                        <p><span>立案审批者</span> <span>刘洪涛</span> <span>2017-11-11 10:10</span></p>
                                        <p><span>立案申请者</span> <span>刘洪涛</span> <span>2017-11-11 10:10</span></p>
                                    </article>
                                </div>
                            </div>
                            <p onclick="hideFL($(this))">收起 <i class="fa fa-angle-double-up"></i></p>
                        </div>
                        <%-- 立案审批按钮 --%>
                        <div class="endProLiCon ty-center">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="liPro(0)">驳回</span>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="liPro(1)">批准</span>
                        </div>
                        <%-- 结案按钮 --%>
                        <div class="endProCon ty-center">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="endPro(2)">结案</span>
                        </div>
                        <%-- 结案审批按钮 --%>
                        <div class="endProApplyCon ty-center">
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="endPro(0)">驳回</span>
                            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="endPro(1)">批准</span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/resourseCenter/Huploadify/jquery.Huploadify.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/projectsManage/baseManage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../assets/downLoadFile/downLoadFile.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

</body>
</html>
