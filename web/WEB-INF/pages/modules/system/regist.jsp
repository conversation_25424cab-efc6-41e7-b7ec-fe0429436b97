<%@ page import="org.apache.commons.lang3.StringUtils" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<!DOCTYPE html>
<html class="no-js" xmlns="http://www.w3c.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>Wonderss</title>
    <meta http-equiv="Content-Language" content="zh-CN" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />
    <link href="<%=System.getProperty("BaseUrl")%>/assets/setPage/setPage.css?v=SVN_REVISION" rel="stylesheet" type="text/css">
    <link href="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/bootstrap/css/bootstrap.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="<%=System.getProperty("BaseUrl")%>/css/system/css/style.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/font-awesome/css/font-awesome.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

</head>
<body class="p-login" style="min-height:890px; " >

<div class="bg">
    <div class="content">
        <div class="main avatar">
            <div class="logo_avatar">
                <div class="logo"><span class="logo_w">W</span>onderss</div>
                <div class="logo_welcome">欢迎使用通用框架管理系统</div>
            </div>
            <div class="agency_simple_avatar">
                <div class="agency_avatar">
                    <%--头部 登录信息 --%>
                    <div class="tip">
                        <h4 class="color-blue">登录成功</h4>
                        <div class="color-orange">
                            <c:if test="${lastTime!=''}">
                            <span>上一次登陆时间: <span id="prevLoginTime">${lastTime}</span>
                        </c:if>
                        <c:if test="${lastIp!=''}">
                            <span>登录地: <span id="prevLoginAddr">${lastIp}</span>
                        </c:if>
                        </div>
                        <div>请选择您要进入的企业：<span class="loading"><i class="fa fa-refresh"></i> 加载中</span></div>
                    </div>
                    <%-- 少的情况 企业列表 --%>
                    <div id="lit" class="item">
                        <ul class="agencyList" id="listLogin"></ul>
                    </div>
                    <div class="item">
                        <button class="ty-btn logout_btn" type="btn" data-name="logout">退出登录</button>
                    </div>
                    <div class="item" style="text-align: right">
                        <c:if test="${displaySwitch == '1'}">
                            <a type="btn" data-name="applyAgency" style="float:left;">申请机构</a>
                        </c:if>
                        <a target="_blank" href="http://www.btransmission.com">关于我们</a>
                        <p id="tip2"></p>
                    </div>
                    <%--脚部 申请机构 --%>
                    <form action="../sys/sureLogin.do" method="post" class="hd submit">
                        <input type="text" name="userID">
                        <input type="text" name="logonName">
                        <input type="text" name="logonPwd">
                        <input type="text" name="oid">
                    </form>
                </div>
            </div>
        </div>
        <div class="seeAllAgency" style="display: none; margin-top: -180px">
            <%-- 多的情况 企业列表 --%>
            <div id="more" class="agency_all_avatar">
                <div class="logo_avatar" type="small">
                    <div class="logo"><span class="logo_w">W</span>onderss</div>
                    <div class="logo_welcome">欢迎使用通用框架管理系统</div>
                </div>
                <div class="agency_avatar_big">
                    <span class="color-red error"></span>
                    <div>请选择您要进入的企业：<span class="loading"><i class="fa fa-refresh"></i> 加载中</span></div>
                    <ul class="clearfix agencyList_big" id="moreOrg"></ul>
                    <div id="ye_agency"></div>
                    <div class="item">
                        <button class="ty-btn logout_btn" type="btn" data-name="logout">退出登录</button>
                    </div>
                </div>
            </div>
        </div>
        <%-- 申请机构 页面 --%>
        <div class="applyAgency avatar" style="display: none">
            <div class="logo_avatar">
                <div class="logo"><span class="logo_w">W</span>onderss</div>
                <div class="logo_welcome">欢迎使用通用框架管理系统</div>
            </div>
            <div class="agency_simple_avatar">
                <div class="apply_avatar">
                    <div>
                        <div class="item">
                            <h3>申请机构</h3>
                        </div>
                        <div class="item">
                            申请手机号：
                            <div class="phone"></div>
                        </div>
                        <div class="item">
                            <button class="ty-btn apply_btn" type="btn" data-name="submitApply">提交</button>
                        </div>
                        <div class="item">
                            <button class="ty-btn reback_btn" type="btn" data-name="reback">返回</button>
                        </div>
                    </div>
                    <div class="applyState_avatar" style="display: none">
                        <div class="item">
                            <h3 class="state">提交成功</h3>
                        </div>
                        <div class="item">
                            <div class="tip">请您耐心等待销售员审核</div>
                        </div>
                        <div class="item">
                            <button class="ty-btn reback_btn" type="btn" data-name="reback">返回</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/jquery.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/menu/jquery.cookie.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/base64/base64.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/auth.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    <%=StringUtils.isNotEmpty(response.getHeader("token"))?"auth.saveToken('"+response.getHeader("token")+"')":""%>
    $.webRoot = '<%= System.getProperty("BaseUrl") %>';//站点跟目录前缀
    $.ow365url = '<%= request.getAttribute("ow365url") %>';//ow365预览前缀
    $.uploadUrl = '<%= request.getAttribute("uploadUrl") %>';//同域下载前缀
    $.fileUrl = '<%= request.getAttribute("fileUrl") %>';//文件服务器下载前缀
</script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/bootstrap/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/layer/layer.js?v=SVN_REVISION"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/system/register.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/setPage/setPage.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/seleniumUpdateId.js?v=SVN_REVISION"></script>
<script async src="https://kendo.cdn.telerik.com/2019.2.619/js/kendo.all.min.js"></script>
<%-- 验证kendo插件 --%>
<script type="text/javascript">
$(function () {
    let checkKendoInteval = setInterval(() => {
        let checkKendo = typeof kendo != 'undefined'
        if (checkKendo) {
            $.cookie('kendoCDN', checkKendo, {path:'/'})
            clearInterval(checkKendoInteval)
        }
    }, 500)
})
</script>
</body>
</html>