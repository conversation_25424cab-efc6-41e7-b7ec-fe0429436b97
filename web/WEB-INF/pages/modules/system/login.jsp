<%@ page import="cn.sphd.miners.common.utils.GetLocalIPUtils" %>
<%@ page import="java.io.File" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<!DOCTYPE html>
<html class="no-js" xmlns="http://www.w3c.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>Wonderss</title>
    <meta http-equiv="Content-Language" content="zh-CN" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />
    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/bootstrap/css/bootstrap.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="<%=System.getProperty("BaseUrl")%>/css/system/css/style.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link href="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/font-awesome/css/font-awesome.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <%--轻量级弹窗样式--%>
    <link href="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/layer/layer.css?v=SVN_REVISION"  rel="stylesheet" type="text/css" >
    <!-- END HEAD -->
</head>
<body class="p-login" style="min-height:890px;">
<script>
    let webRoot = '<%= System.getProperty("BaseUrl") %>';//站点跟目录前缀
    <% request.setAttribute("jumpNew", new File(request.getServletContext().getRealPath("/vue/minersFrontEnd/dist/index.html")).exists()); %>
    <c:if test="${jumpNew}">
    location.href = webRoot + '/vue/minersFrontEnd/dist/index.html#/?logout=1'
    </c:if>
    console.log(${logonName})
</script>
<c:if test="${logonName != null}">
<div class="page-new">
    <div class="page-header">
        <%--logo--%>
        <div class="page-logo">
            <span class="logo-default minersLogo"><span class="green_logo">W</span>onderss</span>
        </div>
        <%--左侧菜单--%>
        <div class="left-menu">
        </div>
        <%--右侧菜单--%>
        <div class="top-menu">
            <ul class="nav navbar-nav pull-right">
            </ul>
        </div>
    </div>
    <div class="page-container">
        <div class="page-sidebar-wrapper"></div>
        <div class="page-content">
            <div class="ty-container">
                <%--短信验证--%>
                <div class="SMSVerification form">
                    <div class="error color-red"></div>
                    <div class="item">为了您数据的安全，本次登录需要短信验证码。</div>
                    <div class="item">
                        <div class="input_avatar">
                            <span class="input_title">账号</span>
                            <span class="SMS_phone"><c:out value="${logonName}"></c:out></span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="input_avatar">
                            <div>
                                <span class="input_title">验证码</span><input type="text" name="code" placeholder="请输入验证码" style="width: 260px">
                            </div>
                            <button class="text-btn text-btn-green getCodeBtn" type="btn" data-name="getCode" id="getSMSCode">获取短信验证码</button>
                        </div>
                    </div>
                    <div class="item text-right">
                        <button class="text-btn text-btn-red" type="btn" data-name="quit">退 出</button>
                        <button class="text-btn text-btn-green" type="btn" data-name="login_code_check">登 录</button>
                    </div>
                    <div class="item">
                        <div class="other_btn">
                            <span class="tip">注：如无法获取该手机号的短信验证码，您还可向公司的总务人员求助，请其为您修改手机号。</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</c:if>
<c:if test="${logonName == null}">
<div class="bg">
    <div class="content">
        <div class="row avatar">
            <div class="logo_avatar">
                <div class="logo"><span class="logo_w">W</span>onderss</div>
                <div class="logo_welcome">欢迎使用通用框架管理系统</div>
            </div>
            <div class="login_login_avatar">
                <div class="login_avatar form" id="usePassword">
                    <div class="item">
                        <div class="linkbb">
                            <span>账号密码登录</span>
                            <a href="http://www.btransmission.com" target="_blank">关于我们</a>
                            <a type="btn" class="riMar" data-name="register-choose">注册账号</a>
                        </div>
                    </div>
                    <form id="login_form" action="../sys/login.do" method="post">
                        <div class="item" style="margin-top: 24px">
                            <div class="input_avatar" id="phone">
                                <span class="input_title">账号</span>
                                <input type="text" name="logonName" autocomplete="off" placeholder="如无法确定，请咨询公司管理人员" onkeyup="clearNoNum(this)">
                                <i class="fa fa-times-circle clearInput"></i>
                                <div class="input_choose_list"> </div>
                            </div>
                        </div>
                        <div class="item">
                            <div class="input_avatar">
                                <span class="input_title">密码</span>
                                <input type="password" name="logonPwd" autocomplete="new-password" placeholder="请输入密码" id="logonPwd">
                                <i class="fa fa-times-circle clearInput"></i>
                            </div>
                        </div>
                        <span class="error color-red">${error}</span>
                    </form>
                    <div class="item" id="picVerification" style="display: none">
                        <div class="input_avatar">
                            <input type="text" name="logonPwd" placeholder="请输入右图中的字符" id="code_input">
                            <i class="fa fa-times-circle clearInput" style="right: 100px"></i>
                            <div class="verificationCode">
                                <div id="checkCode"></div>
                                <span>看不清？<a class="ty-color-blue" onclick="changePicVerification()">换一个</a></span>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <button class="ty-btn login_btn" type="btn" data-name="login_password">登 录</button>
                    </div>
                    <div>
                        <div class="item">
                            <span class="allMethod">登录方式</span>
                            <hr>
                            <div class="other_btn allMethodC">
                                <a type="btn" data-name="login-wx" ><i class="fa fa-weixin"></i><br/>微信</a>
                                <a type="btn" data-name="code"><i class="fa fa-envelope"></i><br/>手机短信</a>
                                <a type="btn" data-name="password"><i class="fa fa-user-circle-o"></i><br/>账号密码</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="login_avatar form" id="useCode" style="display:none ">
                    <div class="item">
                        <div class="linkbb">
                            <span>手机短信登录</span>
                            <a href="http://www.btransmission.com" target="_blank">关于我们</a>
                            <a type="btn" class="riMar" data-name="register-choose">注册账号</a>
                        </div>
                    </div>
                    <div class="item">
                        <div class="input_avatar">
                            <span class="input_title">账号</span>
                            <input type="text" name="phone" autocomplete="off" placeholder="如无法确定，请咨询公司管理人员" onkeyup="clearNoNum(this)">
                            <i class="fa fa-times-circle clearInput"></i>
                        </div>
                    </div>
                    <div class="item">
                        <div class="input_avatar">
                            <div>
                                <span class="input_title">验证码</span><input type="text" name="code" placeholder="请输入验证码" style="width: 110px">
                            </div>
                            <button class="ty-btn getCodeBtn" type="btn" data-name="getCode" id="getCode">获取验证码</button>
                        </div>
                    </div>
                    <span class="error color-red"></span>
                    <div class="item">
                        <button class="ty-btn login_btn" type="btn" data-name="login_code">登 录</button>
                    </div>
                    <div>
                        <div class="item">
                            <span class="allMethod">登录方式</span>
                            <hr>
                            <div class="other_btn allMethodC">
                                <a type="btn" data-name="login-wx" ><i class="fa fa-weixin"></i><br/>微信</a>
                                <a type="btn" data-name="code"><i class="fa fa-envelope"></i><br/>手机短信</a>
                                <a type="btn" data-name="password"><i class="fa fa-user-circle-o"></i><br/>账号密码</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="login_avatar form" id="loginWx" style="display:none">
                    <div class="item">
                        <div class="linkbb">
                            <span>微信登陆</span><span class="error color-red"></span>
                            <a href="http://www.btransmission.com" target="_blank">关于我们</a>
                            <a type="btn" class="riMar" data-name="register-choose">注册账号</a>
                        </div>
<%--                        <a type="btn" data-name="back" class="ty-color-blue"></a>--%>
<%--                        <a type="btn" data-name="back" class="ty-color-blue">< 返回</a>--%>
                    </div>
                    <div class="item">
                        <div class="" id="wxLogin">
                        </div>
                    </div>
                    <div class="item loginOther">
                        <span class="allMethod">登录方式</span>
                        <hr>
                        <div class="other_btn allMethodC">
                            <a type="btn" data-name="login-wx" ><i class="fa fa-weixin"></i><br/>微信</a>
                            <a type="btn" data-name="code"><i class="fa fa-envelope"></i><br/>手机短信</a>
                            <a type="btn" data-name="password"><i class="fa fa-user-circle-o"></i><br/>账号密码</a>
                        </div>
                    </div>
                </div>
                <div class="login_avatar form" id="register-choose" style="display:none ">
                    <div class="item">
                        <a type="btn" data-name="back" class="ty-color-blue">< 返回</a>
                    </div>
                    <div class="item">
                        <div class="title_big">
                            <span>注册Wonderss账号</span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="ty-radio">
                            <input type="radio" name="choose" id="sd" value="0">
                            <label for="sd">为公司注册，全公司的同事都将使用</label>
                             <br>
                            <input type="radio" name="choose" id="df" value="1">
                            <label for="df">为自己注册，仅个人使用</label>
                        </div>
                    </div>
                    <div class="item">
                        <button class="ty-btn login_btn" type="btn" data-name="next_register" disabled>下一步</button>
                    </div>
                </div>
                <div class="login_avatar" id="changePassword" style="display:none ">
                    <div class="error color-red"></div>
                    <div class="tip">
                        <div class="color-orange">请您设置密码，以便登录更快捷</div>
                        忘记密码时，您可使用验证码登录
                    </div>
                    <div class="item">
                        <div class="input_avatar">
                            <span class="input_title">密码</span>
                            <form id="login_form_reset" action="../sys/login.do" method="post">
                                <input type="password" name="logonPwd" autocomplete="new-password" placeholder="请输入密码">
                                <input class="hd" type="text" name="logonName">
                            </form>
                        </div>
                    </div>
                    <div class="item">
                        <div class="input_avatar">
                            <span class="input_title">确认密码</span>
                            <input type="password" name="logonPwd2" autocomplete="new-password" placeholder="请重新输入密码">
                        </div>
                    </div>
                    <div class="tip">注：密码需为8-16位，必须包括数字和英文字母，英文字母分大小写</div>
                    <div class="item">
                        <button class="ty-btn login_btn" type="btn" data-name="login_setPassword">设置完毕，登录系统</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</c:if>

<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/jquery.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    $.webRoot = '<%= System.getProperty("BaseUrl") %>';//站点跟目录前缀
    <% request.setAttribute("jumpNew", new File(request.getServletContext().getRealPath("/vue/minersFrontEnd/dist/index.html")).exists()); %>
    <c:if test="${jumpNew}">
        location.href = $.webRoot + '/vue/minersFrontEnd/dist/index.html'
    </c:if>
    console.log(${logonName})
</script>
<script src="<%=System.getProperty("BaseUrl")%>/script/base64/base64.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/auth.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/menu/jquery.cookie.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/assets/md5/md5.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/testForm.js?v=SVN_REVISION" type="text/javascript"></script>
<%--弹框能够自动消失的提示插件（用在成功操作时）--%>
<script src="<%=System.getProperty("BaseUrl")%>/assets/global/plugins/layer/layer.js?v=SVN_REVISION"></script>
<script async src="<%=System.getProperty("BaseUrl")%>/script/common/wxLogin.js?v=SVN_REVISION"></script>
<script defer src="<%=System.getProperty("BaseUrl")%>/script/common/qs.min.js?v=SVN_REVISION"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/system/login.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/common/seleniumUpdateId.js?v=SVN_REVISION"></script>
<script async src="https://kendo.cdn.telerik.com/2019.2.619/js/kendo.all.min.js"></script>
<%-- 验证kendo插件 --%>
<script type="text/javascript">
$(function () {
    let checkKendoInteval = setInterval(() => {
        let checkKendo = typeof kendo != 'undefined'
        if (checkKendo) {
            $.cookie('kendoCDN', checkKendo, {path:'/'})
            clearInterval(checkKendoInteval)
        }
    }, 500)
})
</script>
<%
    String hello = "Title";
    String ip= GetLocalIPUtils.getServerIp();
    int index=ip.lastIndexOf(".");
    if(index>=0) {
        hello=ip.substring(index+1);
    }
%>
<!-- <%=hello%> -->
</body>
</html>