<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="<%=System.getProperty("BaseUrl")%>/css/main/personalInformation.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%--个人信息--%>
    <div class="bonceContainer bounce-blue" id="personalInfor"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-left" id="backBtn" onclick="bounce.cancel()">返回</span>
                    <span class="panelTtl ty-left">个人信息</span>
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="personalInforView()">预览</span>
                </div>
                <div class="opertionList">
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td class="tct">资料名称</td>
                            <td class="tct">操作</td>
                        </tr>
                        <tr>
                            <td>基本信息</td>
                            <td>
                                <span class="ty-color-blue" onclick="updateBaseInfo()">编辑</span>
                                <span class="ty-color-blue" data-name="base" onclick="personUpdateRecord('base', '', 1, 10)">修改记录</span>
                            </td>
                        </tr>
                        <tr class="eduList">
                            <td>教育经历</td>
                            <td>
                                <span class="ty-color-blue" data-type="new" onclick="eduExperience($(this))">新增</span>
                            </td>
                        </tr>
                        <tr class="workList">
                            <td>工作经历</td>
                            <td>
                                <span class="ty-color-blue" data-type="new" onclick="workExperience($(this))">新增</span>
                            </td>
                        </tr>
                        <tr>
                            <td>紧急联系方式</td>
                            <td>
                                <span class="ty-color-blue" onclick="updateContactUpdate()">编辑</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="checkOperation">
                <span class="agreenTip" data-checked="0" onclick="checkInfoFinish($(this))"><i class="fa fa-circle-o"></i>我的资料已填写完成</span>
                <button class="ty-btn ty-btn-blue ty-btn-big" disabled id="checkInfoFinish" onclick="checkFinishSure()">确定</button>
            </div>
            <span class="ty-btn ty-btn-blue ty-btn-big" id="closePersonalInfor" style="display: none;" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--个人信息编辑--%>
    <div class="bonceContainer bounce-green" id="updatePerInfo"  style="width: 930px;">
        <div class="bonceHead">
            <span>基本信息</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 500px;overflow-y: auto">
            <div class="clear addInfoSect">
                <ul class="baseSect" id="updateBaseDetails">
                    <li>
                        <span>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</span>
                        <span id="updateBaseName">兰亭序</span>
                    </li>
                    <li>
                        <span>性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</span>
                        <input type="radio" name="sex" value="1" id="man_male" data-org="" />
                        <label for="man_male">男</label>
                        <input type="radio" name="sex" value="0" id="man_female" data-org="" />
                        <label for="man_female">女</label>
                    </li>
                    <li>
                        <span>民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族</span>
                        <input name="nation" value="" require/>
                    </li>
                    <li>
                        <span>出生日期</span>
                        <input id="personBirthday" name="birthday" value="" require/>
                    </li>
                    <li>
                        <span>出&nbsp;&nbsp;生&nbsp;&nbsp;地</span>
                        <input id="personBirthAddress" name="birthplace" value="" require/>
                    </li>
                    <li>
                        <span>婚姻状况</span>
                        <select id="personMarry" name="marry" data-org="" require>
                            <option value="1" selected="selected">未婚</option>
                            <option value="0">已婚</option>
                        </select>
                    </li>
                    <li>
                        <span>政治面貌</span>
                        <input id="personPolitical" name="politicalStatus" value="" require/>
                    </li>
                    <li>
                        <span>最高学历</span>
                        <select id="personEdu" name="degree" data-org="0">
                            <option selected="selected" style="display: none" value="0"></option>
                            <option value="1">研究生</option>
                            <option value="2">本科</option>
                            <option value="3">大专</option>
                            <option value="4">中专或高中</option>
                            <option value="5">其他</option>
                        </select>
                    </li>
                    <li>
                        <span>第一外语语种</span>
                        <input id="personLanguage1" name="firstLanguage" value="" require/>
                    </li>
                    <li>
                        <span>水平或证书</span>
                        <input id="personCertificate1" name="firstForeignLevel" value="" require/>
                    </li>
                    <li>
                        <span>第二外语语种</span>
                        <input id="personLanguage2" name="secondLanguage" value="" require/>
                    </li>
                    <li>
                        <span>水平或证书</span>
                        <input id="personCertificate2" name="secondForeignLevel" value="" require/>
                    </li>
                    <li class="resetStyle">
                        <span>计算机水平或证书</span>
                        <input id="personComputerLevel" name="computerLevel" value="" require/>
                    </li>
                    <li class="resetStyle">
                        <span>其他技能描述</span>
                        <input id="personOtherSkills" name="otherSkills" value="" require/>
                    </li>
                    <li class="canSeeTip resetStyle">
                        <p>注：您的联系方式在设置为<i class="ty-color-orange">同事可见</i>前，您同事无法看见，且修改无需审批。</p>
                    </li>
                    <li class="resetStyle">
                        <span>手机号码</span>
                        <span id="personPhone">13522200220</span>
                        <a class="ty-blue addMoreContact" onclick="addMoreContact($(this))">添加更多联系方式</a>
                        <select class="addMoreSelect" style="display: none;width: 150px;" onchange="addMoreChange($(this))">
                            <option value="0" style="display: none"></option>
                            <option value="1">手机</option>
                            <option value="2">QQ</option>
                            <option value="3">Email</option>
                            <option value="4">微信</option>
                            <option value="5">微博</option>
                            <option value="9">自定义</option>
                        </select>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="updateBaseInfoSure" disabled onclick="updateBaseInfoSure()">确定</button>
        </div>
    </div>
    <%--个人信息预览--%>
    <div class="bonceContainer bounce-green" id="personalInforView"  style="width: 810px;">
        <div class="bonceHead"></div>
        <div class="bonceCon" style="max-height: 500px;overflow-y: auto">
            <div class="btnSect clear">
                <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed.cancel()">退出预览</p>
            </div>
            <div id="baseInfoDetails">
                <div class="userBaseInfo">
                    <div class="userImg">
                        <img id="uimg" onerror="this.src='../assets/oralResource/user.png'" alt="用户头像" />
                    </div>
                    <div class="userCon">
                        <h4 id="employeeUserName"></h4>
                        <p class="baseDetail">
                            <span data-name="gender" require></span>
                            <span data-name="nation" require></span>
                            <span data-name="birthday" require></span>
                            <span data-name="birthplace" require></span>
                            <span data-name="politicalStatus" require></span>
                            <span data-name="marry" require></span>
                            <span data-name="degree" require></span>
                        </p>
                        <ul class="contectList clear">
                        </ul>
                    </div>
                </div>
                <div class="otherInfo" id="skills">
                    <h5 class="ttlH5">个人技能</h5>
                    <div class="charact">
                        <div class="mmTtl">外语：</div>
                        <div class="mmCon">
                            <p>
                                <span>第一外语语种：<span data-name="firstLanguage" need></span></span>
                                <span data-name="firstForeignLevel" need></span>
                            </p>
                            <p>
                                <span>第二外语语种：<span data-name="secondLanguage" need></span></span>
                                </span><span data-name="secondForeignLevel" need></span>
                            </p>
                        </div>
                    </div>
                    <div class="charact">
                        <div class="mmTtl">计算机：</div>
                        <div class="mmCon">
                            <p data-name="computerLevel" need></p>
                        </div>
                    </div>
                    <div class="charact">
                        <div class="mmTtl">其它技能描述：</div>
                        <div class="mmCon">
                            <p data-name="otherSkills" need></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="otherInfo">
                <h5 class="ttlH5">教育经历</h5>
                <div id="eduHashMap">
                </div>
            </div>
            <div class="otherInfo">
                <h5 class="ttlH5">工作经历</h5>
                <div id="workHashMap">
                </div>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--新增教育经历--%>
    <div class="bonceContainer bounce-green" id="addEduInfo"  style="width: 730px;">
        <div class="bonceHead">
            <span>教育经历</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 500px;overflow-y: auto">
            <form id="eduInfoForm">
                <ul class="editSect">
                    <li>
                        <span>学习时间</span>
                        <input id="studyBegin" name="beginTime" require />
                        <span>—— ——</span>
                        <input id="studyEnd" name="endTime" require />
                    </li>
                    <li>
                        <span>毕业院校</span>
                        <input type="text" class="longSize" name="collegeName" require/>
                    </li>
                    <li>
                        <span>院&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;系</span>
                        <input value="" name="departmentName" require/>
                        <span>专&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业</span>
                        <input value="" name="major" require/>
                    </li>
                    <li>
                        <span>学历/学位</span>
                        <input id="degree" value="" name="degreeDesc" require/>
                    </li>
                    <li>
                        <span class="pickTop">专业描述</span>
                        <textarea id="professional" value="" class="longSize" name="majorDesc" require></textarea>
                    </li>
                    <li>
                        <span class="pickTop">补充说明</span>
                        <textarea id="moreNotes" value="" class="longSize" name="memo" require></textarea>
                    </li>
                </ul>
            </form>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="eduExperienceTj" onclick="eduExperienceTj()">确定</button>
        </div>
    </div>
    <%--新增工作经历--%>
    <div class="bonceContainer bounce-green" id="addWorkInfo"  style="width: 730px;">
        <div class="bonceHead">
            <span>工作经历</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon" style="max-height: 500px;overflow-y: auto">
            <form id="WorkInfoForm">
                <ul class="editSect">
                    <li>
                        <span>服务时间</span>
                        <input id="workBegin" name="beginTime" require />
                        <span>—— ——</span>
                        <input id="workEnd" name="endTime" require />
                    </li>
                    <li>
                        <span>公司名称</span>
                        <input type="text" class="longSize" name="corpName" require/>
                    </li>
                    <li>
                        <span>公司规模</span>
                        <input value="" name="corpSize" require/>
                        <span>公司性质</span>
                        <input value="" name="corpNature" require/>
                    </li>
                    <li>
                        <span>所在部门</span>
                        <input value="" name="corpDepartment" require/>
                        <span>职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</span>
                        <input value="" name="post" require/>
                    </li>
                    <li>
                        <span class="pickTop">工作描述</span>
                        <textarea id="workContent" value="" class="longSize" name="jobDesc" require></textarea>
                    </li>
                    <li>
                        <span class="pickTop">补充说明</span>
                        <textarea id="workMoreNotes" value="" class="longSize" name="memo" require></textarea>
                    </li>
                </ul>
            </form>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="workExperienceTj" onclick="workExperienceTj()" disabled>确定</button>
        </div>
    </div>
    <%--编辑紧急联系人--%>
    <div class="bonceContainer bounce-green" id="updateContact"  style="width: 730px;">
        <div class="bonceHead">
            <span>紧急联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <form id="emergencyForm">
                <ul class="baseSect">
                    <li>
                        <span>紧急联系人</span>
                        <input type="text" value="" name="emergencyName" require />
                    </li>
                    <li>
                        <span>紧急联系人的联系方式</span>
                        <input type="text" value="" name="emergencyContact" require />
                    </li>
                </ul>
            </form>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="updateEmergencyContact" onclick="emergencyContact()">确定</button>
        </div>
    </div>
    <%--基本信息修改记录--%>
    <div class="bonceContainer bounce-blue" id="baseInfoRecord"  style="width: 730px;">
        <div class="bonceHead"></div>
        <div class="bonceCon">
            <div class="btnSect clear">
                <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed.cancel()">返回</p>
                <p class="panelTtl ty-left">基本信息修改记录</p>
            </div>
            <h4 class="notEdit notEditBase">基本信息尚未修改过。</h4>
            <div class="recordMain">
                <div class="clear">
                    <p class="ty-left">当前数据为第<span class="baseUpdateNum"></span>次修改后的结果。</p>
                    <p class="ty-right">修改时间：<span class="baseUpdateInfo"></span></p>
                </div>
                <table class="ty-table ty-table-control updateBaseMessage">
                </table>
            </div>
            <div id="basePageInfo"></div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--教育经历修改记录/工作经历修改记录--%>
    <div class="bonceContainer bounce-blue" id="eduInfoRecord"  style="width: 730px;">
        <div class="bonceHead"></div>
        <div class="bonceCon">
            <div class="btnSect clear">
                <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed.cancel()">返回</p>
                <p class="panelTtl ty-left" id="recordName">教育经历修改记录</p>
            </div>
            <h4 class="notEdit notEditOther">教育经历尚未修改过。</h4>
            <div class="recordMain">
                <div class="clear deltedAfter" style="display: none;">
                    <p class="ty-left deltedTtl"></p>
                    <p class="ty-left deltedCon"></p>
                </div>
                <div class="clear">
                    <p class="ty-left othereUpdateNum"></p>
                    <p class="ty-right">修改日期：<span class="otherUpdateInfo"></span></p>
                </div>
                <table class="ty-table ty-table-control updateRecord">
                </table>
            </div>
            <div id="otherPageInfo"></div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--删除--%>
    <div class="bonceContainer bounce-red" id="deleteDetail">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipMessage">
                <p class="deleteClear">确定删除本条<span class="deleteTtl"></span>吗？</p>
                <p class="deleteHold">删除后，您所预览的个人资料中将不再含有本条数据。</p>
                <p class="deleteHold">如需要，您可在修改记录中查看。</p>
                <p class="deleteHold">确定删除本条<span class="deleteTtl"></span>吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <div class="inputTip"></div>
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="deleteExperienceSure()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed2">
    <div class="bonceContainer bounce-red" id="alertMS">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="altMS" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">确定</span>
        </div>
    </div>
    <%--同时可见提示--%>
    <div class="bonceContainer bounce-red" id="canSeeMS">
        <div class="bonceHead">
            <span>提示：</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="canSeeTxt" style="text-align: center; padding:10px 0">该联系方式将显示于您同事的通讯录中。</p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="canSeeSure()">确定</span>
        </div>
    </div>
    <%--个人基本信息查看--%>
    <div class="bonceContainer bounce-blue" id="seeBaseInfor"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed2.cancel()">返回</p>
                    <p class="panelTtl ty-left">基本信息</p>
                </div>
                <ul class="baseSect clear" id="baseDetails">
                    <li>
                        <div class="ltTtl">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</div>
                        <span data-name="userName" require>兰亭序</span>
                    </li>
                    <li>
                        <div class="ltTtl">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别</div>
                        <div class="rtCon" data-name="gender" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族</div>
                        <div class="rtCon" data-name="nation" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">出生日期</div>
                        <div class="rtCon" data-name="birthday" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">出&nbsp;&nbsp;生&nbsp;&nbsp;地</div>
                        <div class="rtCon" data-name="birthplace" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">婚姻状况</div>
                        <div class="rtCon" data-name="marry" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">政治面貌</div>
                        <div class="rtCon" data-name="politicalStatus" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">最高学历</div>
                        <div class="rtCon" data-name="degree" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">第一外语语种</div>
                        <div class="rtCon" data-name="firstLanguage" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">水平或证书</div>
                        <div class="rtCon" data-name="firstForeignLevel" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">第二外语语种</div>
                        <div class="rtCon" data-name="secondLanguage" require>兰亭序</div>
                    </li>
                    <li>
                        <div class="ltTtl">水平或证书</div>
                        <div class="rtCon" data-name="secondForeignLevel" require>兰亭序</div>
                    </li>
                    <li class="resetStyle">
                        <div class="ltTtl">计算机水平或证书</div>
                        <div class="rtCon longSize" data-name="computerLevel" require>兰亭序</div>
                    </li>
                    <li class="resetStyle">
                        <div class="ltTtl">其他技能描述</div>
                        <div class="rtCon longSize" data-name="otherSkills" require>兰亭序</div>
                    </li>
                    <li class="resetStyle">
                        <div class="ltTtl">手机号码</div>
                        <span data-name="mobile" require></span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--教育经历信息查看--%>
    <div class="bonceContainer bounce-blue" id="seeEduInfor"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed2.cancel()">返回</p>
                    <p class="panelTtl ty-left">教育经历</p>
                </div>
                <ul class="recordSee" id="eduRecordSee">
                    <li>
                        <div class="ltTtl">学习时间</div>
                        <div class="rtCon" data-name="beginTime" require></div>
                        <div class="ltTtl">— —</div>
                        <div class="rtCon" data-name="endTime" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">毕业院校</div>
                        <div class="rtCon longSize" data-name="collegeName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">院&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;系</div>
                        <div class="rtCon" data-name="departmentName" require></div>
                        <div class="ltTtl">专&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业</div>
                        <div class="rtCon" data-name="major" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">学历/学位</div>
                        <div class="rtCon" data-name="degreeDesc" require></div>
                    </li>
                    <li>
                        <div class="ltTtl pickTop">专业描述</div>
                        <div class="rtArea" data-name="majorDesc" require></div>
                    </li>
                    <li>
                        <div class="ltTtl pickTop">补充说明</div>
                        <div class="rtArea" data-name="memo" require></div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--工作经历信息查看--%>
    <div class="bonceContainer bounce-blue" id="seeWorkInfor"  style="min-width:800px; ">
        <div class="bonceHead"></div>
        <div class="bonceCon wrap-panel" style="max-height: 500px; overflow: auto;">
            <div class="">
                <div class="btnSect clear">
                    <p class="ty-btn ty-btn-blue ty-btn-big ty-left" onclick="bounce_Fixed2.cancel()">返回</p>
                    <p class="panelTtl ty-left">工作经历</p>
                </div>
                <ul class="recordSee" id="workRecordSee">
                    <li>
                        <div class="ltTtl">服务时间</div>
                        <div class="rtCon" data-name="beginTime" require></div>
                        <div class="ltTtl">— —</div>
                        <div class="rtCon" data-name="endTime" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">公司名称</div>
                        <div class="rtCon longSize" data-name="corpName" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">公司规模</div>
                        <div class="rtCon" data-name="corpSize" require></div>
                        <div class="ltTtl">公司性质</div>
                        <div class="rtCon" data-name="corpNature" require></div>
                    </li>
                    <li>
                        <div class="ltTtl">所在部门</div>
                        <div class="rtCon" data-name="corpDepartment" require></div>
                        <div class="ltTtl">职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</div>
                        <div class="rtCon" data-name="post" require></div>
                    </li>
                    <li>
                        <div class="ltTtl pickTop">工作描述</div>
                        <div class="rtArea" data-name="jobDesc" require></div>
                    </li>
                    <li>
                        <div class="ltTtl pickTop">补充说明</div>
                        <div class="rtArea" data-name="memo" require></div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" onkeyup="countWords($(this),20)"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>我的桌面</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container" >
                <ul class="affair myDesktop">
                    <%--goUrl('../user/personalProfile.do')--%>
                    <li name="resume" id="resume" onclick="getPersonalInfor()">
                        <i class="fa fa-user"></i>
                        <span class="icon-name" title="个人信息">
                            <span>个人信息</span>
	                    </span>
                    </li>
                    <li name="addressBook" onclick="goUrl('../user/toAddressList.do')">
                        <i class="fa fa-address-book"></i>
                        <span class="icon-name" title="通讯录">
                            <span>通讯录</span>
	                    </span>
                    </li>
                    <li name="myWork" onclick="goUrl('../mywork/toMyworkPage.do')">
                        <i class="fa fa-file-text"></i>
                        <i class="fa fa-wordpress dbIcon" style="font-size:20px "></i>
                        <span class="icon-name" title="我的工作记录">
                            <span>我的工作记录</span>
                        </span>
                    </li>
                    <li name="addressBook" onclick="goUrl('../workAttendance/toMyAttendence.do')">
                        <i class="fa fa-calendar"></i>
                        <span class="icon-name" title="我的考勤">
                        <span>我的考勤</span>
                    </span>
                    </li>
                    <li name="addressBook" onclick="goUrl('../loginrecord/toMyLoginLog.do')">
                        <i class="fa fa-file-text"></i>
                        <i class="fa fa-user-o dbIcon" style="font-size:20px "></i>
                        <span class="icon-name" title="我的登录记录">
                        <span>我的登录记录</span>
                    </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="<%=System.getProperty("BaseUrl")%>/script/jquery.validate.min.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/user/personInfo.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="<%=System.getProperty("BaseUrl")%>/script/general/employeeIndexCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script>
    localStorage.removeItem("loginTry")
    var imgPath = '${user.imgPath}';
    if( typeof imgPath === 'string' && imgPath.length > 0 ) {
        $("#uimg").attr("src", $.fileUrl + imgPath)
    }
    function goUrl(url) {
        location.href = url ;
    }
    $(function(){
        var n = $("#mainMenu").children("li").length ;
        if(n < 5){
            $("#resume").remove();
        }
        var roleCode = sphdSocket.user.roleCode
        // 超级浏览者--browse   代理会计agentAccounting   代理小会计--agentSmallAccounting 这三个值的 都是外部人员，不给展示通讯录
        if (roleCode === 'browse' || roleCode === 'agentAccounting' || roleCode === 'agentSmallAccounting') {
            $("li[name='addressBook']").hide()
        } else if (roleCode === 'finance' || roleCode === 'sale' || roleCode === 'general' || roleCode === 'accounting' || roleCode === 'smallSuper') {
            $(".myDesktop li:last").siblings().hide();
        }
    })
</script>
</body>
</html>
