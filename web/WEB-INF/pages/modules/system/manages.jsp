<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../script/resourseCenter/Huploadify/Huploadify.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/system/manages.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce_Fixed3">
    <div class="bonceContainer bounce-blue" id="bounceFixed3_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="msg text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 sureBtn">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-orange" id="bounceFixed3_iknow">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="msg text-center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-orange ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">我知道了</span>
        </div>
    </div>
    <%--链接错误提示--%>
    <div class="bonceContainer bounce-red" id="errorMS">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="shu1">
                <p id="errorMSTip" style="text-align: center; padding:10px 0"> </p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="bounce_Fixed3.cancel()">确定</span>
        </div>
    </div>
    <%--导入失败--%>
    <div class="bonceContainer bounce-red" id="importantTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <h4>导入失败！</h4>
                <div>
                    <div>原因可能为：</div>
                    <div>1、修改了所下载表格中的“列”。</div>
                    <div>2、选错了文件。</div>
                    <div>3、文件太大，或里面含有图片等。</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big" onclick="bounce_Fixed3.cancel()">知道了</span>
        </div>
    </div>
    <%--知道了 - 提示--%>
    <div class="bonceContainer bounce-red" id="knowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="knowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue" onclick="bounce_Fixed3.cancel()">知道了</span>
        </div>
    </div>
    <%--我知道了 - 提示--%>
    <div class="bonceContainer bounce-red" id="iknowTip" style="width:400px; ">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="iknowWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-red" onclick="bounce_Fixed3.cancel()">我知道了</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%--二级警告提示--%>
    <div class="bonceContainer bounce-blue" id="errorTip" style="width:400px; ">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord" style="text-align: center"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <%--暂不设置最高负责人提示--%>
    <div class="bonceContainer bounce-red" id="noSmallSuper">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="text-align: center;">确定后，各项事务的负责人将由您直接管控。</div>
            <div style="text-align: center;">确定暂不再设置全权负责人吗？</div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-circle-3 ty-btn-big" onclick="bounce_Fixed.cancel()">取消</button>
            <button class="ty-btn ty-circle-3 ty-btn-big ty-btn-red" onclick="sureNoSmallSuper()">确定</button>
        </div>
    </div>
    <%--提示--%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <p>系统无法向您输入的手机号发送提示短信。</p>
                <p>请检查确认，或输入其他手机号！</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-red" onclick="bounce_Fixed.cancel()">我知道了</span>
        </div>
    </div>
    <%--修改子机构简称修改记录-查看--%>
    <div class="bonceContainer bounce-blue" id="branchNameEditLogScan">
        <div class="bonceHead">
            <span>分支机构简称</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody">
                <div class="formContainer">
                    <div class="item">
                        <div class="item_head">分支机构简称</div>
                        <div class="item_content">
                            <span class="log_name"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" onclick="bounce_Fixed.cancel()">关 闭</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--提示--%>
    <div class="bonceContainer bounce-red" id="mtTip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-red" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="bounce_tip">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="tipWord"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-2 ty-btn-big" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-2 ty-btn-big ty-btn-blue sureBtn" onclick="sureCompleteManager()">确定</span>
        </div>
    </div>
    <%--提示--%>
    <div class="bonceContainer bounce-red" id="addBranchTip">
        <div class="bonceHead">
            <span>！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <p>下一步为向系统导入职工等操作。</p>
                <p>确定向该临时管理员发送相应的系统短信吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取 消</span>
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="addBranchesSure()">确 定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-blue" id="changeAgentState">
        <div class="bonceHead">
            <span>修改代理状态</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <div class="agent_avatar">
                    <div class="item tips">当前贵公司会计业务不是客必盛代理 修改为由客必盛代理？</div>
                </div>
                <div class="formContainer agentForm">
                    <div class="item">请输入客必盛业务人员信息</div>
                    <div class="item">
                        <div class="item_head"><span class="ty-color-red">*</span> 手机号</div>
                        <div class="item_content"><input type="text" name="mobile" placeholder="请输入手机号" onkeyup="clearNum(this)"></div>
                    </div>
                    <div class="item">
                        <div class="item_head"><span class="ty-color-red">*</span> 姓名</div>
                        <div class="item_content"><input type="text" name="userName" placeholder="请输入姓名"></div>
                    </div>
                </div>
                <div class="item formContainer normalForm">
                    <div style="line-height: 32px;">请在在册职工中选择 <span class="ty-color-red">*</span></div>
                    <select name="role" class="ty-select selectRole"></select>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
            <button class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" id="sureChangeAgentStateBtn" onclick="sureChangeAgentState()">确定</button>
        </div>
    </div>

    <%-- 新增最高负责人提示 --%>
    <div class="bonceContainer bounce-blue" id="confirm">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>有全权负责人后，您将无法再直接管控其他事务的负责人。</p>
            <p>确定设置全权负责人吗？</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-gray" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="bounce.show($('#addManager'))">确定</span>
        </div>
    </div>

    <%--添加管理者--%>
    <div class="bonceContainer bounce-green" id="addManager">
        <div class="bonceHead">
            <span class="bonceTitle"></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="text-center">
                <div class="agent_avatar">
                    <div class="item">是否由客必盛代理会计业务</div>
                    <div class="item">
                        是 <input type="radio" name="isAgent" value="1" checked="checked" to="agentForm" style="margin-right: 16px">
                        否 <input type="radio" name="isAgent" value="0" to="normalForm">
                    </div>
                </div>
                <div class="formContainer agentForm">
                    <div class="item">请输入客必盛业务人员信息</div>
                    <div class="item">
                        <div class="item_head"><span class="ty-color-red">*</span> 手机号</div>
                        <div class="item_content"><input type="text" name="mobile" placeholder="请输入手机号" onkeyup="clearNum(this)"></div>
                    </div>
                    <div class="item">
                        <div class="item_head"><span class="ty-color-red">*</span> 姓名</div>
                        <div class="item_content"><input type="text" name="userName" placeholder="请输入姓名"></div>
                    </div>
                </div>
                <div class="item formContainer normalForm">
                    <div>请在在册职工中选择 <span class="ty-color-red">*</span></div>
                    <select name="role" class="ty-select selectRole"></select>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-circle-3 ty-btn-big ty-btn-green" id="addManagerBtn" onclick="sureAddManager()">确定</button>
        </div>
    </div>

    <%--历任负责人--%>
    <div class="bonceContainer bounce-blue" id="chargeHistory" style="width: 700px">
        <div class="bonceHead">
            <span class="bonceTitle">历任负责人</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="orgName item">张三</div>
            <div class="item"><b>地址</b> <span class="address"></span></div>
            <div class="item">历任<span class="roleName">全权负责人</span></div>
            <table class="kj-table">
                <thead>
                <tr>
                    <td>姓名</td>
                    <td>手机号</td>
                    <td>上任的操作记录</td>
                    <td>卸任的操作记录</td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">关闭</span>
        </div>
    </div>

    <%--修改临时管理员 - 选择--%>
    <div class="bonceContainer bounce-blue" id="changeTemporaryAdmin_choose">
        <div class="bonceHead">
            <span class="bonceTitle">修改临时管理员</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <div class="ty-radio">
                    <input type="radio" name="changeImportManager" value="0" id="changeAnotherTemporaryAdmin">
                    <label for="changeAnotherTemporaryAdmin"></label> 换另一位职工作为临时管理员来操作。
                </div>
            </div>
            <div class="item">
                <div class="ty-radio">
                    <input type="radio" name="changeImportManager" value="1" id="ownHandle">
                    <label for="ownHandle"></label> 亲自操作。
                </div>
            </div>
            <div class="ty-alert">
                注：修改后，原手机号将无法继续操作，该号码已导入的数据也将被清除！
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="chooseTemporaryAdminBtn" onclick="chooseTemporaryAdmin()">下一步</button>
        </div>
    </div>

    <%--修改临时管理员 - 输入新的管理员--%>
    <div class="bonceContainer bounce-blue" id="changeTemporaryAdmin_input">
        <div class="bonceHead">
            <span class="bonceTitle">修改临时管理员</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 350px; margin: 0 auto">
                <div class="ty-alert">
                    请录入新临时管理员的姓名与手机号
                </div>
                <div class="page-content-avatar">
                    <div class="item">
                        <div class="item_title">姓名</div>
                        <div class="item_content">
                            <input type="text" class="kj-input" name="userName">
                        </div>
                    </div>
                    <div class="item">
                        <div class="item_title">手机</div>
                        <div class="item_content">
                            <input type="text" class="kj-input" name="mobile" onkeyup="clearNum(this)">
                            <div class="ty-color-blue">注：所录入号码需能接收短信验证码！</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.show($('#changeTemporaryAdmin_choose'))">取消</button>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-2" id="inputTemporaryAdminBtn" onclick="inputTemporaryAdmin()">确定</button>
        </div>
    </div>

    <%--导入--%>
    <div style="width:550px" class="bonceContainer bounce-blue" id="leading">
        <div class="bonceHead">
            <span>批量导入</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="bounceMainCon">
                <div class="stepItem">
                    <p>第一步：下载空白的“职工名单”。</p>
                    <div class="flexRow">
                        <span>职工名单</span>
                        <a href="../assets/oralResource/template/employee_blank_sheet.xls"
                           id="mould1" download="职工名单.xls" class="ty-btn ty-btn-blue">下载</a>
                    </div>
                </div>
                <div class="stepItem">
                    第二步：在空白的“职工名单”中填写内容，并存至电脑。
                </div>
                <div class="stepItem">
                    <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                    <div class="flexRow">
                        <button class="ty-btn ty-btn-big ty-btn-blue" onclick="chooseEmployeeFile()">浏览</button>
                        <div class="upload_sect viewBtn hd">
                            <div id="sysUseUploadFile"></div>
                        </div>
                        <div class="fileFullName">尚未选择文件</div>
                    </div>
                </div>
                <div class="stepItem">
                    <p>第四步：点击“导入”。</p>
                    <div class="flexRow">
                        <span class="ty-btn ty-btn-big" onclick="bounce.cancel()">取消</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big" onclick="sysUseImportOk()">导入</span>
                    </div>
                </div>
                <div class="importIntro stepItem">
                    <p class="ty-color-red"><span>导入说明：</span></p>
                    <div style="font-size: 13px;color: #3d566f;">
                        <span>1、请勿增加、删除或修改所下载“职工档案”空白表的“列”，否则上传会失败。</span></br>
                        <span>2、在电脑上保存“职工名单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%--修改职工信息--%>
    <div class="bonceContainer bounce-blue" id="updateUser">
        <div class="bonceHead">
            <span>修改职工信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="userForm">
                <div class="formItem">
                    <div class="left">姓名</div><input type="text" class="ty-inputText userName">
                    <i class="clearBtn" onclick="clearTxt($(this))">x</i>
                </div>
                <div class="formItem">
                    <div class="left">手机号</div><input type="text" onkeyup="clearNum(this)" class="ty-inputText userPhone">
                    <i class="clearBtn" onclick="clearTxt($(this))">x</i>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" disabled="disabled" id="importUpdateUser" onclick="updateUserInfo()">确定</button>
        </div>
    </div>
    <%--删除职工--%>
    <div class="bonceContainer bounce-orange" id="delUser">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">确定删除所导入的这个职工吗？</div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-orange sureBtn">确定</span>
        </div>
    </div>
    <%--放弃后，本次批量导入--%>
    <div class="bonceContainer bounce-red" id="clearUser">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>放弃后，本次批量导入的数据将消失不见。</p>
                <p>确定放弃吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-btn-yellow" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" data-name="clearUser">确定</span>
        </div>
    </div>
    <%--进入下一步--%>
    <div class="bonceContainer bounce-orange" id="nextStep">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <div class="safeCondition">
                    <p>还有<span id="noSaveMbSum"></span>个手机号无法保存至系统。</p>
                    <p>进入下一步，这些号码将被舍弃。</p>
                </div>
                <p>确定进入下一步吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-orange ty-circle-3" onclick="sureNextStep('noSave')">确定</span>
        </div>
    </div>
    <%--保存--%>
    <div class="bonceContainer bounce-orange" id="lastSave">
        <div class="bonceHead">
            <span>提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-center">
                <p>您共导入职工<span id="saveSum"></span>条，可保存至系统的共<span id="saveAble"></span>条。</p>
                <p>确定保存吗？</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-orange ty-circle-3" onclick="lastSave()">确定</span>
        </div>
    </div>
    <%-- 上次的批量导入尚未完成。  --%>
    <div class="bonceContainer bounce-red" id="importNotCompleted">
        <div class="bonceHead">
            <span>！！提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="narrowBody unfinishedForm">
                <p>上次的批量导入尚未完成。</p>
                <div class="ty-radio">
                    <input type="radio" id="importNotCompletedC" value="1">
                    <label for="importNotCompletedC"></label> 继续上次的操作
                </div>
                <div class="ty-radio" style="margin-top: 8px">
                    <input type="radio" id="importNotCompletedG" value="0">
                    <label for="importNotCompletedG"></label> 放弃上次的操作，重新批量导入
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-3" onclick="judgeImport()">确定</span>
        </div>
    </div>
    <%--操作指南--%>
    <div class="bonceContainer bounce-blue" id="moreGuide" style="width: 650px">
        <div class="bonceHead">
            <span>操作指南</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="moreGuideCon">
                <ul>
                    <li>
                        1、“中枢管控”中各项要素按实际填写或操作，是系统正常使用的第一步。
                    </li>
                    <li>
                        2、总机构的“核心人物管控”中，您可选择一位“全权负责人”。
                        <div>选择后，系统内其他重要事务的负责人将由此人管控，此人则由您管控。</div>
                        <div>“全权负责人”可不选择，选择后也可更换。</div>
                    </li>
                    <li>
                        3、本产品有利于您对分支机构的管控。有分支机构时，可点击“增加分支机构”。
                        <div>新增后，分支机构的管控方式类似总机构。</div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel();">关 闭</span>
        </div>
    </div>
        <%-- 修改分支机构简称 --%>
        <div class="bonceContainer bounce-blue" id="changeBranchName" style="width: 540px">
            <div class="bonceHead">
                <span>修改分支机构简称</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="narrowBody">
                    <div class="con-txt">修改分支机构简称</div>
                    <div class="abPos">
                        <input class="form-control" name="name" value="" placeholder="请录入" maxlength="6" onkeyup="limitWord($(this), 6)"/>
                        <i class="fa fa-times clearInputVal"></i>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-btn-yellow ty-circle-3" onclick="bounce.cancel()">取 消</span>
                <button class="ty-btn ty-circle-3 ty-btn-big ty-btn-blue" onclick="sureChangeBranchName()">确 定</button>
            </div>
        </div>
        <%-- 修改分支机构简称修改记录 --%>
        <div class="bonceContainer bounce-blue" id="branchNameEditLog" style="width: 520px;">
            <div class="bonceHead">
                <span>修改记录</span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div style="padding:20px;">
                    <p class="curSta"></p>
                    <table class="ty-table ty-table-control">
                        <tbody>
                        <tr>
                            <td>资料状态</td>
                            <td>分支机构简称</td>
                            <td>创建人/修改人</td>
                        </tr>
                        </tbody>
                    </table>
                    <div id="ye_log"></div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn bounce-ok ty-btn-big ty-circle-3" onclick="bounce.cancel()">关 闭</span>
            </div>
        </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>高管管理</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <div class="page mainCompany" style="display: none">
                    <div class="page-header">
                        尊敬的<span class="main-color-black main_orgName"></span>公司：
                    </div>
                    <div class="page-content-avatar">
                        <div class="panel-box">
                            <div class="ty-alert">欢迎使用wonderss管理系统！今天是 <span class="main-color-black main_today"></span></div>
                            <div class="ty-alert">贵公司已于 <span class="main-color-black main_createDate"></span> 获准使用wonderss产品，拥有最高权限的手机号为 <span class="main-color-black main_superMobile"></span> </div>
                            <div class="ty-alert">
                                使用该号码登录系统后，通过在本页面的操作，可掌控系统的最高权限。
                                <div class="text-right btn-group">
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="bounce.show($('#moreGuide'))">更多操作指南</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box">
                            <div class="ty-alert">
                                <div class="ty-color-red main_item item_name">总机构</div>
                                <div class="main_item">全权负责人  <span class="main-color-black main_smallUserName"></span></div>
                            </div>
                            <div class="ty-alert">
                                <div class="main_item">地址  <span class="main-color-black main_address"></span></div>
                                <div class="main_item">职工总人数  <span class="main-color-black main_userNumber"></span></div>
                                <div class="text-right btn-group">
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" id="coreControl" onclick="coreControl()">核心人物管控</button>
                                </div>
                            </div>
                        </div>
                        <div class="panel-box" id="addBranches">
                            <div class="ty-alert">
                                贵公司如在其他地址有分支机构，可点击“增加分支机构”。
                                <div class="text-right btn-group">
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="jumpTo('branchesMain','addBranches')">增加分支机构</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page confirmHandler" style="display: none">
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="jumpTo('mainCompany')">返回</button>
                    <div class="part part_noSetManager">
                        <div class="page-header">
                            <div>系统的启用需先将职工导入，请确定导入的操作者。</div>
                        </div>
                        <div class="page-content-avatar">
                            <div class="item">
                                <div class="ty-radio">
                                    <input type="radio" name="confirmImportManager" value="0" id="chooseTemporaryManager">
                                    <label for="chooseTemporaryManager"></label> 选一位职工作为临时管理员来操作。
                                </div>
                            </div>
                            <div class="item">
                                <div class="ty-radio">
                                    <input type="radio" name="confirmImportManager" value="1" id="chooseOwnHandle">
                                    <label for="chooseOwnHandle"></label> 亲自操作。
                                </div>
                            </div>
                            <div class="text-right">
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="confirmImportManager()">下一步</button>
                            </div>
                        </div>
                    </div>
                    <div class="part part_hasSetManager">
                        <div class="ty-alert ty-alert-info">
                            您已选择的临时管理员
                            <div class="btn-group text-right">
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="changeTemporaryAdmin()">修改</button>
                            </div>
                        </div>
                        <div class="page-content-avatar">
                            <div class="item">
                                <div class="item_title">姓名</div>
                                <div class="item_content"><span class="userName"></span></div>
                            </div>
                            <div class="item">
                                <div class="item_title">手机</div>
                                <div class="item_content"><span class="mobile"></span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page handler" style="display: none">
                    <div class="part part_needHandler">
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="jumpTo('confirmHandler', 'noSetManager')">返回</button>
                        <div class="page-content-avatar">
                            <div class="item">
                                <div class="item_title">姓名</div>
                                <div class="item_content">
                                    <input type="text" class="kj-input" name="userName">
                                </div>
                            </div>
                            <div class="item">
                                <div class="item_title">手机</div>
                                <div class="item_content">
                                    <input type="text" class="kj-input" name="mobile" onkeyup="clearNum(this)">
                                    <div class="ty-color-blue">注：所录入号码需能接收短信验证码！</div>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="addTemporaryAdmin()">下一步</button>
                        </div>
                    </div>
                    <div class="page-content-avatar part part_own">
                        <div>
                            <div class="main">
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="reback()">返回</button>
                                <div class="ty-alert">
                                    系统中现有如下 <span class="main-color-black onDutyNum">0</span> 位在职人员。${success}${error}
                                    <div class="btn-group text-right">
                                        <button class="ty-btn ty-btn-green ty-btn-big ty-circle-2" onclick="leadingStartManage('manager')">批量导入其他职工</button>
                                    </div>
                                </div>
                                <div class="tblContainer">
                                    <table class="kj-table ty-table-control">
                                        <thead>
                                        <td>姓名</td>
                                        <td>手机号</td>
                                        <td>是否有下属</td>
                                        <td>直接上级</td>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="importNoSave narrowLamp" style="display: none">
                                <div class="stepItem">
                                    <span class="ty-btn ty-btn-red ty-btn-big ty-circle-2 btn" onclick="giveUp('noSave')" style="margin-right: 250px;">放弃</span>
                                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 btn" onclick="nextStep('noSave')">下一步</span>
                                </div>
                                <p>您共导入职工<span class="initAll"></span>条，其中以下<span class="initWrong"></span>条存在问题，<span class="ty-color-red"> 无法保存至系统</span>。</p>
                                <p>姓名或手机号未录入、手机号错误或与系统中已有号码相同等，均算作问题。</p><br>
                                <table class="kj-table kj-thead-warn">
                                    <thead>
                                    <td>姓名</td>
                                    <td>手机号</td>
                                    <td>操作</td>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                            <div class="importing" style="display: none">
                                <div class="stepItem">
                                    <span class="ty-btn ty-btn-red ty-btn-big ty-circle-2 btn" onclick="giveUp('save')" style="margin-right: 250px;">放弃</span>
                                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 btn" id="ok" onclick="confirmOrgTip()">下一步</button>
                                </div>
                                <p>您共导入职工<span class="importSum"></span>条，可保存至系统的共<span class="saveSum"></span>条。</p>
                                <p>1、为每位职工选定“工作特点”后，才可保存至系统。</p>
                                <p>2、Wonderss内请假、加班、报销等均与“直接上级”有关，故建议按实际情况给予选定。</p>
                                <p class="exportStep">找到<span class="ty-color-red">最高领导的直接下属</span>后，将其“直接上级”选择为最高领导，之后逐级选择的操作方式较易理解。</p><br>
                                <table class="kj-table kj-thead-warn">
                                    <thead>
                                    <td>姓名</td>
                                    <td>手机号</td>
                                    <td>是否有下属</td>
                                    <td>直接上级</td>
                                    <td>工作特点</td>
                                    <td>操作</td>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page manager" style="display: none">
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" id="manageBtn" onclick="jumpTo('mainCompany')">返回</button>
                    <div class="part">
                        <div class="ty-alert mainAreaCon">
                            <div>
                                <p>请确定以下各项事务的负责人。您可为机构选择全权负责人，也可直接确定其他各项。</p>
                                <p>选定全权负责人后，其他各项将由全权负责人管控。您将仅管控全权负责人。</p>
                            </div>
                        </div>
                        <%--最高负责人 --%>
                        <table class="kj-table managerList" data-source="">
                            <thead>
                            <tr>
                                <td>高级事务的负责人/高级权限的分配者</td>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>最后操作时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <div class="text-right">
                            <div class="ty-checkbox" id="chooseIsCompeteManger">
                                <input type="checkbox" name="confirmImportManager" id="completeManger">
                                <label for="completeManger"></label> 各项事务的负责人中，能确定的都已选定。
                            </div>
                            <div>
                                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" id="completeMangerBtn" onclick="completeManger()">下一步</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page managerDone" style="display: none">
                    <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2 backBtn" onclick="jumpTo('mainCompany')">返回</button>
                    <div class="part">
                        <%--最高负责人 --%>
                        <table class="kj-table managerList" data-source="">
                            <thead>
                            <tr>
                                <td>高级事务的负责人/高级权限的分配者</td>
                                <td>姓名</td>
                                <td>手机号</td>
                                <td>最后操作时间</td>
                                <td>操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="page branchesMain" style="display: none">
                    <div class="part part_addBranches">
                        <div class="clear">
                            <button class="ty-left ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="reback('addBranches')">返回</button>
                            <button class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="addBranchesNextStep()">下一步</button>
                        </div>
                        <div class="page-content-avatar">
                            <div class="brance_input">
                                <div class="form-group">
                                    <label for="branchAddress">分支机构的地址</label>
                                    <input type="text" class="form-control" id="branchAddress" placeholder="请录入">
                                </div>
                                <div class="form-group">
                                    <label for="branchShort">分支机构的简称<span class="xing"></span></label>
                                    <span class="lenTip ty-right">0/6</span>
                                    <input type="text" class="form-control" id="branchShort" maxlength="6" placeholder="请录入分支机构在全公司范围内的简称" oninput="limitWord($(this), 6)">
                                </div>
                            </div>
                            <div class="panel-box manager_addBranches">
                                <p>分支机构的启用需先将所属职工导入，请确定该项操作的临时挂管理员。</p>
                                <div class="ty-alert">
                                    <div class="ty-radio">
                                        <input type="radio" name="confirmImportManager" value="0" id="chooseManagerBySys">
                                        <label for="chooseManagerBySys"></label> 在已录入系统的职工中选择一人
                                    </div>
                                    <div>
                                        <select class="ty-select common-select selectBySys">
                                        </select>
                                    </div>
                                </div>
                                <div class="ty-alert">
                                    <div class="ty-radio">
                                        <input type="radio" name="confirmImportManager" value="1" id="addManagerPer">
                                        <label for="addManagerPer"></label>  另行录入一人
                                    </div>
                                    <div class="clear">
                                        <div class="ty-left preTtl">
                                            <span>姓名<span class="xing"></span></span>
                                            <input type="text" class="ty-inputText tempUserName common-select" placeholder="请录入">
                                        </div>
                                        <div class="ty-left preTtl">
                                            <span>手机号<span class="xing"></span></span>
                                            <div class="item_content">
                                                <input type="text" class="kj-input common-select tempMobile" name="mobile" maxlength='11' onblur="isMobileTest($(this))" placeholder="请录入">
                                                <div class="ty-color-blue tipMsg">注：该手机号需能正常使用！</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="ty-radio">
                                        <input type="radio" name="confirmImportManager" value="2" id="branchOwnHandle">
                                        <label for="branchOwnHandle"></label> 亲自操作
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="part part_managerBranch" style="display: none">
                        <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="jumpTo('mainCompany')">返回</button>
                        <div class="branchInfo">
                            <div class="ty-alert">
                                <div class="branceTitle ty-color-orange">
                                    分支机构 <span id="branchName"></span>
                                    <span class="hd"></span>
                                </div>
                                <div>
                                    <span class="thin-btn" data-fun="updateName">修改</span>
                                    <span class="thin-btn" data-fun="updateNameLog">修改记录</span>
                                </div>
                            </div>
                            <div class="ty-alert">
                                <div>
                                    地址 <span id="branchAddressCon"></span>
                                </div>
                                <div>
                                    <span class="thin-btn">与场地有关的信息</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="branch branch_sonHasSetManager">
                                <div class="ty-alert ty-alert-info">
                                    您已选择的临时管理员为<span class="branchTemporary"></span>
                                    <div class="btn-group text-right">
                                        <span class="thin-btn" onclick="changeTemporaryBranch()">修改</span>
                                    </div>
                                </div>
                            </div>
                            <div class="branch branch_manager" style="display: none">
                                <div>
                                    <%--最高负责人 --%>
                                    <table class="kj-table branch_managerList" data-source="1">
                                        <thead>
                                        <tr>
                                            <td>高级事务的负责人/高级权限的分配者</td>
                                            <td>姓名</td>
                                            <td>手机号</td>
                                            <td>最后操作时间</td>
                                            <td>操作</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                    <div class="text-right">
                                        <div class="ty-checkbox" id="branch_chooseIsCompeteManger">
                                            <input type="checkbox" name="confirmImportManager" id="branch_completeManger">
                                            <label for="branch_completeManger"></label> 各项事务的负责人中，能确定的都已选定。
                                        </div>
                                        <div>
                                            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" id="branch_completeMangerBtn" onclick="completeManger(1)">下一步</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="branch branch_managerDone" style="display: none">
                                <div>
                                    <%--最高负责人 --%>
                                    <table class="kj-table branch_managerList" data-source="1">
                                        <thead>
                                        <tr>
                                            <td>高级事务的负责人/高级权限的分配者</td>
                                            <td>姓名</td>
                                            <td>手机号</td>
                                            <td>最后操作时间</td>
                                            <td>操作</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/jquery.validate.min.js?v=SVN_REVISION"></script>
<script src="../script/messages_zh.min.js?v=SVN_REVISION"></script>
<%--<script src="../script/general/generalAffairs.js?v=SVN_REVISION" type="text/javascript"></script>--%>
<script src="../script/general/employeeIndexCommon.js?v=SVN_REVISION" type="text/javascript"></script>
<script src="../script/manage/manager.js?v=SVN_REVISION"></script>
</body>
</html>
