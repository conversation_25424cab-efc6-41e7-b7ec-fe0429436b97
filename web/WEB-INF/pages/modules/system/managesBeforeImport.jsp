<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include file="../../common/headerTop.jsp" %>
<style>
    .btnco {
        background-color: #ccc;
    }
    .kuaiji > td {
        border: 0;
        background: #F0F8FF;
    }

    .infoItem > span {
        width: 70px;
        text-align: right;
        display: inline-block;
        padding-right: 20px;
    }

    #addAccount > .bonceFoot, .YorN, #notice > .bonceFoot, .cent {
        text-align: center;
    }

    .YorN > input:nth-child(2) {
        margin-right: 50px;
    }

    .YorN > label {
        display: inline-block;
        height: 30px;
        line-height: 30px;
        width: 90px;
    }

    .bonceCon > p {
        text-align: center;
    }

    #ttl, #info {
        text-align: center;
    }

    .fa-circle-o, .fa-dot-circle-o {
        color: #48cfad;
    }
    .canAllow{text-align: center;}
    .canAllow .ty-btn{margin: 0 10px;text-align: center;}
</style>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="mtTip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"></p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 新增总经理提示 --%>
    <div class="bonceContainer bounce-blue" id="confirm">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <p style="text-align: center; padding:10px 0">
                您设置总经理后，则总务、财务、会计与销售高管的号码将均由该总经理管理
            </p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" onclick="bounce.show($('#addmanager'))">确定</span>
        </div>
    </div>
    <%-- creater   姚宗涛   2018/1/17  添加会计--%>
    <div class="bonceContainer bounce-green" id="addAccount">
        <div class="bonceHead">
            <span>添加会计</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="">
                <%--是否由客必盛代理--%>
                <div class="" style="text-align: center;">
                    <p>是否由客必盛代理会计业务</p>
                    <p class="YorN">
                        <span>是</span> <span style="margin-right:30px;" id="yes" class="fa fa-dot-circle-o"
                                             onclick="setAgentType(1 , $(this))"></span>
                        <span>否</span> <span class="fa fa-circle-o" id="no" onclick="setAgentType(0 , $(this))"></span>
                        <input type="hidden" id="AgentType" value="1">
                    </p>
                    <p></p>
                    <p>手机号 <input id="yes_phone" type="text" value=""></p>
                    <p>姓名 <input style="margin-left: 15px;" id="yes_name" type="text" value=""></p>
                    <p class="input_notice" style="color:red;"></p>
                    <div class="bonceFoot cent">
                        <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
                        <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-green addAccount"
                              onclick="addAccountCheck()">确定</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--添加管理者--%>
        <form class="bonceContainer bounce-green" method="post" id="addmanager">
            <div class="bonceHead">
                添加<span id="mgName"></span>
                <a class="bounce_close" onclick="bounce.cancel()"></a>
            </div>
            <div class="bonceCon">
                <input type="hidden" class="hd" name="manageName" readonly id="manageName">
                手机号:<input type="text" class="form-control" name="phone" onkeyup="clearNum(this)" id="managePhone"
                           onchange="addColor()">
                姓名:<input type="text" class="form-control" name="userName" id="addManagerName" onchange="addColor()">
                <p class="ty-color-red" style="height:10px;" id="addTip"></p>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">关闭</span>
                <span class="ty-btn ty-circle-5 ty-btn-big btnco" id="btclick">确定</span>
            </div>
        </form>

</div>
<%@ include file="../../common/contentHeader.jsp" %>
<div class="page-container">
    <%@ include file="../../common/contentSliderNav.jsp" %>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>高管管理</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <div id="addBtns"></div>

                <div class="ty-mainData">
                    <%-- 总经理 --%>
                    <table class="ty-table ty-table-control panel_0">
                        <thead>
                        <tr>
                            <td> 角色</td>
                            <td> 手机号</td>
                            <td> 姓名</td>
                            <td> 操作日期</td>
                            <td> 操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include file="../../common/contentSliderLitbar.jsp" %>
</div>
<%@ include file="../../common/footerTop.jsp" %>
<%@ include file="../../common/footerScript.jsp" %>
<script src="../script/manage/managerBefore.js?v=SVN_REVISION"></script>
</body>
</html>
