<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/interview/interviewRecord.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />

<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">

<div class="bounce">
    <%-- 新增编辑访谈记录 --%>
    <div class="bonceContainer bounce-blue" id="edit" style="width: 980px;">
        <div class="bonceHead">
            <span>新增访谈记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon recordForm" style="height:320px; overflow: auto;">
            <div class="addInterview">
                <input type="hidden" id="editType" />
                <input need type="hidden" data-name="id"  />
                <table>
                    <tr>
                        <td><i style="color: red">*</i>客户名称：</td>
                        <td>
                            <select need id="customer" class="form-control" onchange="changeCustomer($(this))" data-name="customer" style="/*width:420px*/;"><option value="">请选择</option></select>
                        </td>
                        <td class="posRt">
                            访谈日期：<input type="text" need id="interviewDate" placeholder="请选择" data-name="interviewDate">
                        </td>
                    </tr>
                    <tr>
                        <td>访谈对象：</td>
                        <td colspan="2">
                            <div class="flexRow">
                                <div id="interviewer">尚未选择</div>
                                <span class="hd"></span>
                                <span class="linkBtn" data-fun="interviewer" data-target="#interviewer">选择</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>同行者（同事）：</td>
                        <td colspan="2">
                            <div class="flexRow">
                                <div id="fellowTravelers">尚未选择</div>
                                <span class="hd"></span>
                                <span class="linkBtn" data-fun="fellowTravelers" data-target="#fellowTravelers">选择</span>
                            </div></td>
                    </tr>
                    <tr>
                        <td>同行者（非同事）：</td>
                        <td colspan="2">
                            <div class="flexRow">
                                <div id="fellowUnColleague">尚未选择</div>
                                <span class="hd"></span>
                                <span class="linkBtn" data-fun="fellowUnColleague" data-target="fellowUnColleague">选择</span>
                            </div></td>
                    </tr>
                    <tr><td colspan="3"><hr/></td></tr>
                    <tr>
                        <td>访谈目标：</td>
                        <td colspan="2">
                            <div class="flexRow">
                                <div class="careBlue">注：如必要，或公司有要求，请点击“编辑”！</div>
                                <span class="linkBtn" data-fun="contentEntry" data-target="purpose">编辑</span>
                            </div>
                            <div class="purposeCon"></div>
                        </td>
                    </tr>
                    <tr class="interviewerContent">
                        <td>谈话要点：</td>
                        <td colspan="2">
                            <div class="flexRow">
                                <div class="careBlue">注：如某些访谈对象或同行者发言较重要，请点击“新增”！</div>
                                <span class="linkBtn" data-fun="addTalkingPoints" data-type="add">新增</span>
                            </div>
                        </td>
                    </tr>
                    <tr class="gains">
                        <td>成果/结论：</td>
                        <td colspan="2">
                            <div class="flexRow">
                                <div class="careBlue">注：如本次访谈有成果或结论，或公司有要求，请点击“新增”！</div>
                                <span class="linkBtn" data-fun="contentEntry" data-target="gains" data-type="add">新增</span>
                            </div>
                        </td>
                    </tr>
                    <tr><td colspan="3"><hr/></td></tr>
                    <tr>
                        <td>何人可见：</td>
                        <td colspan="2">
                            <div class="flexRow">
                                <div>
                                    <div>
                                        <p>尚未选择</p>
                                        <p class="careBlue">注：本条访谈记录，销售经理可见到。如还需其他同事见到，请选择</p>
                                    </div>
                                    <div class="whoAbleData resultCon"></div>
                                </div>
                                <span class="linkBtn" data-fun="whoAble">选择</span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="editOk()">确定</span>
        </div>
    </div>
    <%--访谈记录修改记录--%>
    <div class="bonceContainer bounce-blue" id="interviewLog" style="width:650px">
        <div class="bonceHead">
            <span>访谈记录修改记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="clear noneUpdated">
                <span class="ty-left">当前资料尚未经修改。</span>
                <span class="ty-right">创建人：<span class="create"></span></span>
            </div>
            <div>
                <div>
                    <p class="ty-left">当前资料为第<span class="eidtNum"></span>次修改后的结果。</p>
                    <p class="updaterInfo ty-right">修改时间：<span class="editTime"></span></p>
                </div>
                <table class="ty-table">
                    <tr>
                        <td width="30%">记  录</td>
                        <td width="30%">操  作</td>
                        <td width="40%">创建者/修改者</td>
                    </tr>
                    <tr>
                        <td>原始信息</td>
                        <td><span class="ty-color-blue">查看</span></td>
                        <td>缘根       XXXX/XX/XX XX:XX:XX</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
</div>
<div class="bounce_Fixed">
    <%-- 选择联系人--%>
    <div class="bonceContainer bounce-blue" id="chooseCusContact" style="width: 650px">
        <div class="bonceHead">
            <span>选择客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <input type="hidden" id="target">
            <%--<div class="p0">
                <p style='text-align:center;'>暂无客户数据，请通过新增添加</p>
            </div>--%>
            <div class="wrapBody">
                <div class="posRt"><span class="linkBtn" onclick="addContactInfo(2)">新增访谈对象</span></div>
                <p>请选择本次访谈的访谈对象（可多选）</p>
                <ul class="cusList viewsList">
                    <li>
                        <i class="fa fa-circle-o"></i>
                        <span>姓名</span>
                        <span>职位的前八个字</span>
                        <span>15200000000</span>
                        <span class="linkBtn ty-right">查看</span>
                    </li>
                </ul>
            </div>

        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseCusContactOk()">确定</button>
        </div>
    </div>
        <%-- 选择同行者（同事）--%>
        <div class="bonceContainer bounce-blue" id="chooseFellow" style="width: 650px">
            <div class="bonceHead">
                <span>选择同行者（同事）</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <input type="hidden" class="target">
                <div class="wrapBody">
                    <p>请选择本次访谈的同行者（同事）（可多选）</p>
                    <ul class="fellowList viewsList">
                        <li>
                            <i class="fa fa-circle-o"></i>
                            <span>姓名</span>
                            <span>职位的前八个字</span>
                            <span>15200000000</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseFellowOk()">确定</button>
            </div>
        </div>
        <%-- 选择同行者（非同事）--%>
        <div class="bonceContainer bounce-blue" id="chooseFellowUn" style="width: 650px">
            <div class="bonceHead">
                <span>选择同行者（非同事）</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <input type="hidden" class="target">
                <div class="narrowA">
                    <span class="ty-right linkBtn" data-fun="addOtherTogether">录入同事外的人</span>
                    <p>本次访谈的同行者（非同事）共0人</p>
                    <ul class="fellowList viewsList">
                    </ul>
                </div>
                <div class="hd otherTogetherList"></div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="chooseFellowUnOk()">确定</button>
            </div>
        </div>
        <%--何人可见-选择同事--%>
        <div class="bonceContainer bounce-blue" id="whoAbleLog" style="width:650px">
            <div class="bonceHead">
                <span>选择同事</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="wrapBody">
                    <p>请选择可查看本条记录的同事（可多选）</p>
                    <ul class="collList viewsList">
                        <li>
                            <i class="fa fa-circle-o"></i>
                            <span>姓名</span>
                            <span>职位的前八个字</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="whoAbleLogSure()">确定</span>
            </div>
        </div>
        <%-- 查看访谈记录 --%>
        <div class="bonceContainer bounce-blue" id="scan" style="width: 700px;">
            <div class="bonceHead">
                <span>访谈记录</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="conBody">
                    <table style=" width: 100%; line-height: 30px;">
                        <tr>
                            <td>客户名称：</td>
                            <td><span class="customerName"></span></td>
                            <td class="posRt">访谈日期 ：<span class="interviewDate"></span></td>
                        </tr>
                        <tr>
                            <td>访谈对象：</td>
                            <td colspan="2" class="interviewerNames">
                            </td>
                        </tr>
                        <tr>
                            <td>同行者（同事）：</td>
                            <td colspan="2" class="tsNames"></td>
                        </tr>
                        <tr>
                            <td>同行者（非同事）：</td>
                            <td colspan="2" class="ftxNames"></td>
                        </tr>
                        <tr>
                            <td colspan="3" style="text-align: right;">录入者 ：<span class="createName"></span></td>
                        </tr>
                        <tr><td colspan="3"><hr/></td></tr>
                        <tr>
                            <td>访谈目标：</td>
                            <td colspan="2">
                                <div class="mbContent"></div>
                            </td>
                        </tr>
                        <tr class="scan_interviewerContent">
                            <td colspan="3">谈话要点：</td>
                        </tr>
                        <tr class="scan_gains">
                            <td colspan="3">成果/结论：</td>
                        </tr>
                        <tr><td colspan="3"><hr/></td></tr>
                        <tr>
                            <td>何人可见：</td>
                            <td colspan="2">
                                <div class="userNames"></div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">关闭</span>
            </div>
        </div>
        <%--访谈目标--%>
        <div class="bonceContainer bounce-blue" id="contentEntry" style="width:650px">
            <div class="bonceHead">
                <span>访谈目标</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="aBody">
                    <input type="hidden" class="target">
                    <div class="hd" id="storageData"></div>
                    <div class="widBt clear">
                        <div class="ty-left">
                            <span class="headTip con0">请录入本次访谈的访谈目标</span>
                            <span class="headTip con1">请录入本次访谈的成果/结论</span>
                        </div>
                        <span class="ty-right redLinkBtn clearAreaBtn" data-fun="purposeDel">清空内容</span>
                    </div>
                    <div class="purposeEntry entryCon">
                        <textarea placeholder="请录入" rows="6" maxlength="200" onkeyup="countWords($(this),200)"></textarea>
                        <p class="posRt textMax">0/200</p>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="contentEntryOk()">确定</span>
            </div>
        </div>
        <%-- 谈话要点--%>
        <div class="bonceContainer bounce-blue" id="addTalkingPoints" style="width: 650px">
            <div class="bonceHead">
                <span>谈话要点</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="aBody">
                    <div class="widBt">
                        <span class="red">*</span>发言者：
                        <select id="participant"></select>
                    </div>
                    <p>
                        <span class="red">*</span>请录入该发言者的谈话要点
                        <span class="ty-right redLinkBtn clearTalkArea" data-fun="purposeDel">清空内容</span>
                    </p>
                    <div class="pointEntry entryCon">
                        <textarea placeholder="请录入" rows="10" maxlength="200" onkeyup="countWords($(this),200)"></textarea>
                        <p class="posRt textMax">0/200</p>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addTalkingPointsOk()">确定</button>
            </div>
        </div>
        <%-- 客户名称更换提示--%>
        <div class="bonceContainer bounce-blue" id="changeCustomerTip">
            <div class="bonceHead">
                <span>提示</span>
                <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="aBody ty-center">
                    <div>更换客户后，已录入数据将消失。</div>
                    <div>确定更换客户吗</div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel()">取 消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="changeCustomerOk()">确定</button>
            </div>
        </div>
</div>
<div class="bounce_Fixed2">
    <%--联系方式查看--%>
    <div class="bonceContainer bounce-blue" id="contactSeeDetail" style="width:600px;">
        <div class="bonceHead">
            <span>客户联系人</span>
            <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="contactflex">
                <div>
                    <span>姓名：</span>
                    <span class="sale-con" id="see_contactName"></span>
                </div>
                <div>
                    <span class="sale_ttl1">职位：</span>
                    <span class="sale-con" id="see_position"></span>
                </div>
            </div>
            <div class="contactflex">
                <div>
                    <span>联系人标签：</span>
                    <span class="sale-con" id="see_contactTag"></span>
                </div>
            </div>
            <%--<p class="createDetail">创建者：<span class="see_createName"></span><span class="see_createDate"></span></p>--%>
            <table class="see_otherContact ty-table" style="width:442px">
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">关闭</span>
        </div>
    </div>
        <%--新增联系人--%>
        <div class="bonceContainer bounce-green" id="newContectInfo" style="width: 750px">
            <div class="bonceHead">
                <span>联系人</span>
                <a class="bounce_close" onclick="chargeXhr($(this))"></a>
            </div>
            <div class="bonceCon" style="max-height:500px;overflow-y: auto;">
                <form id="newContectData">
                    <p>
                        <span class="sale_ttl1">联系人标签：</span>
                        <span class="sale_gap" id="contactFlag"></span>
                    </p>
                    <p>
                        <span class="sale_ttl1">姓名：</span>
                        <span class="sale_gap"><input type="text" placeholder="请录入" id="contactName" require/></span>
                        <span class="sale_ttl1">职位：</span>
                        <span class="sale_gap"><input type="text" placeholder="请录入" id="position" require/></span>
                    </p>
                    <p>
                        <span class="sale_ttl1">手机：</span>
                        <span class="sale_gap"><input type="text" placeholder="请录入" id="contactNumber" require/></span>
                        <a class="sale_ttl1 addMore" onclick="addMore($(this))">添加更多联系方式</a>
                        <select style="display: none;width: 167px;" id="addMoreContact" onchange="addMoreChange($(this))" value="0">
                            <option value="0"></option>
                            <option value="1">手机</option>
                            <option value="2">QQ</option>
                            <option value="3">Email</option>
                            <option value="4">微信</option>
                            <option value="5">微博</option>
                            <option value="9">自定义</option>
                        </select>
                    </p>
                    <ul class="otherContact">
                    </ul>
                    <div id="contactsCard">
                        <span class="sale_ttl1">名片：</span>
                        <div id="uploadCard" class="cardUploadBtn"></div>
                    </div>
                </form>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-blue ty-btn-big" onclick="chargeXhr($(this))">取消</span>
                <button class="ty-btn ty-btn-green ty-btn-big" id="addContact" data-name="addContact" onclick="addContact()">提交</button>
            </div>
        </div>
        <%--录入同事外的人--%>
        <div class="bonceContainer bounce-blue" id="addOtherTogether" style="width: 450px">
            <div class="bonceHead">
                <span>录入同事外的人</span>
                <a class="bounce_close" onclick="chargeXhr($(this))"></a>
            </div>
            <div class="bonceCon">
                <div class="narrowA">
                    <p>请录入本次访谈与您同行的同事外的人员</p>
                    <div class="itemC">
                        <p>姓名<i class="xing"></i></p>
                        <input type="text" id="otherTogetherName" class="form-control" placeholder="请录入" name="name" require />
                    </div>
                    <div class="itemC">
                        <p>所在公司或组织</p>
                        <input type="text" class="form-control" placeholder="请录入" name="company" require />
                    </div>
                    <div class="itemC">
                        <p>职位</p>
                        <input type="text" class="form-control" placeholder="请录入" name="post" require />
                    </div>
                    <div class="itemC">
                        <p>手机
                            <span class="ty-right">
                                <a class="addMore" onclick="addMore($(this))">添加更多联系方式</a>
                            <select style="display: none;width: 167px;" class="addMoreContact" onchange="addMoreContact($(this))" value="0">
                                <option value="0"></option>
                                <option value="1">手机</option>
                                <option value="2">QQ</option>
                                <option value="3">Email</option>
                                <option value="4">微信</option>
                                <option value="5">微博</option>
                                <option value="9">自定义</option>
                            </select>
                            </span>
                        </p>
                        <input type="text" class="form-control contract_endTime" placeholder="请录入" name="mobile" require />
                        <ul class="otherContact">
                        </ul>
                    </div>
                    <div class="itemC">
                        <span class="sale_ttl1">名片：<span class="ty-right cardUploadBtn"></span></span>
                        <div class="businessCard"></div>
                    </div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="chargeXhr($(this))">取消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addOtherTogetherSure()">使用</button>
            </div>
        </div>
        <%-- 客户名称更换提示--%>
        <div class="bonceContainer bounce-blue" id="delTip">
            <div class="bonceHead">
                <span>提示</span>
                <a class="bounce_close" onclick="bounce_Fixed2.cancel()"></a>
            </div>
            <div class="bonceCon">
                <div class="aBody ty-center">
                    <div>确定删除该条数据？</div>
                </div>
            </div>
            <div class="bonceFoot">
                <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed2.cancel()">取 消</span>
                <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="delOtherOk()">确定</button>
            </div>
        </div>
</div>
<div class="bounce_Fixed3">
    <%--联系人自定义弹窗--%>
    <div class="bonceContainer bounce-blue" id="useDefinedLabel" style="width: 450px">
        <div class="bonceHead">
            <span>自定义标签</span>
            <a class="bounce_close" onclick="bounce_Fixed3.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="definedCon">
                <p class="ttl">自定义标签</p>
                <input id="defLable" type="text" placeholder="请录入联系方式的标签" maxlength="20"/>
                <a class="clearLableText" onclick="clearLableText($(this))">x</a>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce_Fixed3.cancel()">取消</span>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" id="addNewLableSure" onclick="addNewLable()">使用</button>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>访谈记录</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="mainCon mainCon1">
                    <div class="header_query elemFlex">
                        <div id="todayData"></div>
                        <div class="keySearch">
                            <span class="txtTip">查询</span>
                            <select id="creatorSearch">

                            </select>
                            <select id="customerSearch">
                                <option value="">全部客户</option>
                            </select>
                            <input type="text" id="yearSearch" />
                            <select id="monthSearch">
                                <option value="01">1月</option>
                                <option value="02">2月</option>
                                <option value="03">3月</option>
                                <option value="04">4月</option>
                                <option value="05">5月</option>
                                <option value="06">6月</option>
                                <option value="07">7月</option>
                                <option value="08">8月</option>
                                <option value="09">9月</option>
                                <option value="10">10月</option>
                                <option value="11">11月</option>
                                <option value="12">12月</option>
                            </select>
                            <select id="enableSearch">
                                <option value="1">正常的访谈记录</option>
                                <option value="0">已隐藏的访谈记录</option>
                            </select>
                            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="getList(1, 20)">确 定</span>
                        </div>
                    </div>
                    <div class="elemFlex">
                        <span>以下访谈记录共<span class="rowCount"></span>条，其访谈日期为<span class="timePeriod"></span>之间</span>
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="addNew()">新增访谈记录</span>
                    </div>
                    <table style="margin-top:10px; " class="ty-table ty-table-control" >
                        <thead>
                        <tr>
                            <td>客户名称</td>
                            <td>访谈对象</td>
                            <td>访谈日期</td>
                            <td>录入者</td>
                            <td>销售负责人</td>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody id="interviewList">

                        </tbody>
                    </table>
                    <div id="yeCon1"></div>
                </div>
            </div>
            <div class="clearfix"></div>
        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<script src="../script/interview/interviewRecord.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
