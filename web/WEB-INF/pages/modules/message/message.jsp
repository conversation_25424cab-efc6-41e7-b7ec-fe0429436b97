<html>
<head>
    <title>通用框架</title>
</head>
<body>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
 <link href="../css/message/message.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<%@ include  file="../../common/headerBottom.jsp"%>

<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div id="auth" style="display:none;  ">${rolePopedom}</div>
<div class="bounce_Fixed">
    <%--签收查看-详情查看--%>
    <div class="bonceContainer bounce-blue" id="signDetailCheck" style="min-width:630px;">
        <div class="bonceHead">
            <span></span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="ty-table ty-table-none">
                <tbody>
                <tr>
                    <td style="width: 40%">收货方实际的收货数量：</td>
                    <td><input type="text" class="actualDeliveryNum" style="width: 100%" disabled></td>
                </tr>
                <tr>
                    <td>情况记录</td>
                    <td><textarea cols="30" rows="5" class="memo" style="width: 100%" disabled></textarea></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce_Fixed.cancel();">确定</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确定</span>
        </div>
    </div>
    <%--加班详情--%>
    <div class="bonceContainer bounce-blue" id="overtimeDetail">
        <div class="bonceHead">
            <span>加班详情：</span>
            <a class="bounce_close" onclick="closeBounce()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process" >
                    <div class="infoHead ty-process-ttl"><span class="ty-btn ty-btn-green">审批流程</span></div>
                    <div class="conInfo ty-process-container" id="process">
                        <div class="infoList ty-process-item">
                            <p><span class="dot"></span>处理人：财务/超管 </p>
                            <p>2016-02-02 12：00:00</p>
                            <article>意见：</article>
                        </div>
                        <div class="infoList ty-process-item ">
                            <p><span class="dot-no"></span>处理人：财务/超管 </p>
                            <p>2016-02-02 12：00:00</p>
                            <article>意见：</article>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process">
                    <div class="infoHead ty-process-ttl "><span class="ty-btn ty-btn-green">详情信息</span></div>
                    <div class="infoCon">
                        <p class="procon"><span>申请人：</span><span id="createName"></span></p>
                        <p class="procon"><span>开始时间：</span><span id="beginTime"></span></p>
                        <p class="procon"><span>结束时间：</span><span id="endTime"></span></p>
                        <p class="procon"><span>请假类型：</span><span id="type"></span></p>
                        <p class="procon"><span>请假时长：</span><span id="during"></span></p>
                        <p class="procon"><span>请假内容：</span><span id="memo"></span></p>
                    </div>
                </div>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeBounce()">确定</span>
        </div>
    </div>
    <%--请假详情--%>
    <div class="bonceContainer bounce-blue" id="leaveDetail">
        <div class="bonceHead">
            <span>请假详情：</span>
            <a class="bounce_close" onclick="closeBounce()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process" >
                    <div class="infoHead ty-process-ttl"><span class="ty-btn ty-btn-green">审批流程</span></div>
                    <div class="conInfo ty-process-container" id="leaveProcess">
                        <div class="infoList ty-process-item">
                            <p><span class="dot"></span>处理人：财务/超管 </p>
                            <p>2016-02-02 12：00:00</p>
                            <article>意见：</article>
                        </div>
                        <div class="infoList ty-process-item ">
                            <p><span class="dot-no"></span>处理人：财务/超管 </p>
                            <p>2016-02-02 12：00:00</p>
                            <article>意见：</article>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process">
                    <div class="infoHead ty-process-ttl "><span class="ty-btn ty-btn-green">详情信息</span></div>
                    <div class="infoCon">
                        <p class="procon"><span>申请人：</span><span id="createName1"></span></p>
                        <p class="procon"><span>开始时间：</span><span id="beginTime1"></span></p>
                        <p class="procon"><span>结束时间：</span><span id="endTime1"></span></p>
                        <p class="procon"><span>请假类型：</span><span id="type1"></span></p>
                        <p class="procon"><span>请假时长：</span><span id="during1"></span></p>
                        <p class="procon"><span>请假内容：</span><span id="memo1"></span></p>
                    </div>
                </div>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeBounce();">确定</span>
        </div>
    </div>
    <%--报销详情--%>
    <div class="bonceContainer bounce-blue" id="financeDetail">
        <div class="bonceHead">
            <span>报销详情：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process" >
                    <div class="infoHead ty-process-ttl"><span class="ty-btn ty-btn-green">审批流程</span></div>
                    <div class="conInfo ty-process-container" id="financeProcess">
                    </div>
                </div>
            </div>
            <div class="ty-left ty-procon">
                <div class="ty-panel ty-process">
                    <div class="infoHead ty-process-ttl "><span class="ty-btn ty-btn-green">详情信息</span></div>
                    <div class="infoCon">
                        <p class="procon"><span>申请人：</span><span id="createName2"></span></p>
                        <p class="procon"><span>申请时间：</span><span id="beginTime2"></span></p>
                        <p class="procon"><span>费用类别：</span><span id="fee"></span></p>
                        <p class="procon"><span>票据种类：</span><span id="ticket"></span></p>
                        <p class="procon"><span>票据所属月份：</span><span id="ticketMonth"></span></p>
                        <p class="procon"><span>摘要：</span><span id="summary"></span></p>
                        <p class="procon"><span>用途：</span><span id="purpose"></span></p>
                        <p class="procon"><span>票据数量：</span><span id="billQuantity"></span></p>
                        <p class="procon"><span>总金额：</span><span id="amount"></span></p>
                        <p class="procon"><span>备注：</span><span id="memo2"></span></p>
                        <p class="procon"><span>附件：</span></p>
                        <div>
                            <p class="procon" id="pic"> </p>
                            <img id="bigPic" onerror="this.src='../css/common/theme/icon/error.png';" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeBounce();">确定</span>
        </div>
    </div>
    <%--仓库修正--%>
    <div class="bonceContainer bounce-blue" id="storageAcceptDetail">
        <div class="bonceHead">
            <span>仓库修正：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">

            <div class="clr"></div>
        </div>
        <div class="bonceFoot">
        </div>
    </div>
    <%--出库单--%>
    <div class="bonceContainer bounce-green" id="outStorageOk" style="min-width:1400px;">
        <div class="bonceHead">
            <span>出库单</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="outStorageOkBase">
                <tbody>
                <tr>
                    <td>出库日期</td>
                    <td class="approveTime"></td>
                    <td>订单号</td>
                    <td class="sn"></td>
                </tr>
                <tr>
                    <td>客户名称</td>
                    <td class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                </tbody>
            </table>
            <div class="countAll">本次计划出库共？种商品，共？件</div>
            <table class="ty-table ty-table-control tblList" style="margin-top: 8px">
                <thead>
                <tr>
                    <td>序号</td>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>出库数量</td>
                    <td>货物件数</td>
                    <td>仓库审批时间</td>
                    <td>复核时间</td>
                </tr>
                <tbody></tbody>
            </table>
            <div class="applyAll"></div>
        </div>
        <div class="bonceFoot"></div>
    </div>
    <%--签收查看--%>
    <div class="bonceContainer bounce-blue" id="signCheck" style="min-width:1400px;">
        <div class="bonceHead">
            <span>签收查看</span>
            <a class="bounce_close" onclick="bounce.cancel() "></a>
        </div>
        <div class="bonceCon clearfix">
            <table class="ty-table ty-table-control" id="signCheckBase">
                <tbody>
                <tr>
                    <td>客户名称</td>
                    <td colspan="4" class="customerName"></td>
                    <td>客户代号</td>
                    <td class="customerCode"></td>
                </tr>
                <tr>
                    <td>收货地点</td>
                    <td colspan="2" class="address"></td>
                    <td>收货人</td>
                    <td class="contact"></td>
                    <td>收货人电话</td>
                    <td class="mobile"></td>
                </tr>
                <tr>
                    <td width="18%">订单号</td>
                    <td width="18%" class="sn">1</td>
                    <td width="10%">原始录入时间</td>
                    <td width="18%" colspan="2" class="create_date"></td>
                    <td width="10%">订单修改时间</td>
                    <td width="18%" class="update_date"></td>
                </tr>
                <tr>
                    <td>计划出库日期</td>
                    <td colspan="2" class="deliveryDate"></td>
                    <td colspan="2">搬运负责人</td>
                    <td colspan="2" class="carrier"></td>
                </tr>
                <tr>
                    <td>计划到达日期</td>
                    <td colspan="2" class="arriveDate"></td>
                    <td colspan="2">计划的发货方式</td>
                    <td colspan="2" class="deliveryWay"></td>
                </tr>
                </tbody>
            </table>
            <p class="countAll"></p>
            <div id="signInfoRecord">
                <div class="recordTitle">
                    情况记录
                    <div class="recordHeader ty-right">
                        <b>签收人：</b><span class="signer"></span><b>签收时间：</b><span class="signTime"></span><b>录入者：</b><span class="recorder"></span><b>录入时间：</b><span class="recordTime"></span>
                    </div>
                </div>

                <div class="recordCon">

                    <div class="signDetail"></div>
                    <%--<div class="recordFooter">--%>
                    <%--<b >录入者：</b><span class="signer"></span>--%>
                    <%--</div>--%>
                </div>
            </div>
            <table class="ty-table ty-table-control tblList">
                <thead>
                <tr>
                    <td>商品代号</td>
                    <td>商品名称</td>
                    <td>产品图号</td>
                    <td>产品名称</td>
                    <td>单位</td>
                    <td>要求到货日期</td>
                    <td>出库数量</td>
                    <td>货物件数</td>
                    <td>仓库审批时间</td>
                    <td>复核时间</td>
                    <td>实际签收数量</td>
                    <td>录入时间</td>
                    <td>情况记录</td>
                </tr>
                <tbody></tbody>
            </table>
            <p class="applyAll"></p>
        </div>
        <div class="bonceFoot"></div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>消息提醒</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper" >
        <div class="page-content" id="paContainer" style="min-height:800px;">
            <!--放内容的地方-->
            <div class="ty-container">
                <span id="navTxt" class="hd">未处理</span>
                <div id="main">
                    <ul class="ty-secondTab">
                        <li class="ty-active">未处理</li>
                        <li>已处理</li>
                    </ul>
                    <div class="ty-mainData">
                        <table class="ty-table" id="preRead">
                            <thead>
                                <td width="10%">申请人</td>
                                <td width="15%">事件类型</td>
                                <td width="45%">申请事件</td>
                                <td width="30%">申请时间</td>
                            </thead>
                            <tbody id="Msg"></tbody>
                        </table>
                        <div id="ye-message"></div>
                    </div>
                </div>
                <%--清除浮动--%>
                <div class="clr"></div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/message/message.js?v=SVN_REVISION" type="text/javascript"></script>
<%@ include  file="../../common/footerBottom.jsp"%>

</body>
</html>
