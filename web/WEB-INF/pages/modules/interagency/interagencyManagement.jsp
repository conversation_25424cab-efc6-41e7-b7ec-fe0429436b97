<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/interagency/interagencyManagement.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
<style>

</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce">
    <%--操作指南--%>
    <div class="bonceContainer bounce-blue" id="moreGuide" style="width: 650px">
        <div class="bonceHead">
            <span>操作指南</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="moreGuideCon">
                <p>1、每位职工都有且只有一个所属机构。所属机构可修改。</p>
                <p>2、初始状态下，所有职工都没有可访问的机构，但可通过修改，设置职工可访问哪些机构。</p>
                <p>3、某职工可访问某机构，意味着该职工可与该机构的其他职工一样使用通讯录、讨论区、备忘与日程、文件与资料以及阅览室模块的功能。</p>
                <p>4、您可随时修改职的所属机构或可访问机构的状态，修改生效后，该职工即立即能够或立即无法登录该机构。</p>
                <p>5、任何修改均将生成无法修改的操作日志，故请谨慎操作。</p>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--修改可访问机构--%>
    <div class="bonceContainer bounce-blue" id="modAccessibleOrgan" style="width: 620px;">
        <div class="bonceHead">
            <span>修改可访问机构</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modMain">
                <p class="employeeInfo">xxx xxxxxx</p>
                <div>修改生效后，该职工即立即能够或立即无法登录该机构。</div>
                <div>另外，修改将生成无法修改的操作日志，请谨慎操作！</div>
                <div class="gapTp">
                    <ul class="organs clear">
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel();">取 消</span>
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="modAccessibleOrganSure();">确 定</span>
        </div>
    </div>
    <%--修改所属机构--%>
    <div class="bonceContainer bounce-blue" id="belongOrgan">
        <div class="bonceHead">
            <span>修改所属机构</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modMain">
                <p>xxx xxxxxx<span>当前所属的机构  XXXXXX</span></p>
                <div>修改生效后，该职工将立即能够或立即无法登录某机构。</div>
                <div>另外，修改将生成无法修改的操作日志，请谨慎操作！</div>
                <div class="flexBt">
                    <span>修改为</span>
                    <select>
                        <option>请选择</option>
                        <option>请选择</option>
                        <option>请选择</option>
                        <option>请选择</option>
                    </select>
                </div>
                <p>修改后，XXXXXX机构是否设置为XXXX的可登录机构？</p>
                <div>
                    <div><i class="fa fa-circle-o"></i> 是</div>
                    <div><i class="fa fa-circle-o"></i> 否</div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--跨机构管理操作记录—修改所属机构—修改前--%>
    <div class="bonceContainer bounce-blue" id="modbelongOrganScan" style="width: 560px;">
        <div class="bonceHead">
            <span>跨机构管理操作记录—修改所属机构—修改前</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modLogMain">
                <div class="paceVm">
                    <span class="employeeInfo"></span>
                    所属的机构
                    <span class="employeeOrg"></span>
                </div>
                <div>
                    修改后，原所属机构XXXXXX未设置为XXX的可登录机构。
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
    <%--跨机构管理操作记录—修改可访问机构—修改前--%>
    <div class="bonceContainer bounce-blue" id="modAccessOrganScan" style="width: 580px">
        <div class="bonceHead">
            <span>跨机构管理操作记录—修改可访问机构—<span class="scanType"></span></span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="modMain">
                <div>
                    <span class="employeeInfo"></span>
                </div>
                <div class="gapTp">
                    <ul class="organs clear">
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                        <li> <i class="fa fa-circle-o"></i>总机构</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big bounce-ok ty-circle-3" onclick="bounce.cancel();">关闭</span>
        </div>
    </div>
</div>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>跨机构管理</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainPage">
                    <div class="mainCon mainCon1">
                        <div class="ty-right">
                            <span class="funBtn ty-btn-blue" data-type="guide">操作指南</span>
                            <span class="funBtn ty-btn-blue" data-type="operationLog">操作日志</span>
                        </div>
                        <div class="vtip">下表中的某职工右侧，带√的机构代表该职工可访问，其中带深色底纹的是该职工所属的机构</div>
                        <div class="scrollWrap">
                            <table class="ty-table ty-table-control">
                                <tbody>
                                <tr>
                                    <td class="biaotou">
                                        <div class="oblique_line">
                                            <span style="float:left">职工</span>
                                            <span style="float:right;">机构</span>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="ye_inter"></div>
                    </div>
                    <div class="mainCon mainCon2">
                        <div><span class="funBtn ty-btn-blue" data-type="backMain" data-road="1">返 回</span></div>
                        <div class="logHead flexBt">
                            <div id="todayCon"></div>
                            <div class="clear">
                                <div class="ty-right">
                                    查看其他月份的数据 <input id="otherMonth" type="text" placeholder="请选择月份" />
                                    <span class="ty-right ty-btn ty-btn-big ty-btn-blue" onclick="getSearchKey($(this))">确 定</span>
                                </div>
                            </div>
                        </div>
                        <p>有跨机构管理操作的日期在日历中显示为了红色，点击后所见为详情页！</p>
                        <div class="tb-main">
                            <p id="queryMonth"></p>
                            <table class="ty-table ty-table-control" id="opertionLog">
                                <tbody>
                                <tr>
                                    <td>星期一</td>
                                    <td>星期二</td>
                                    <td>星期三</td>
                                    <td>星期四</td>
                                    <td>星期五</td>
                                    <td>星期六</td>
                                    <td>星期日</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mainCon mainCon3">
                        <div><span class="funBtn ty-btn-blue" data-type="backMain" data-road="2">返 回</span></div>
                        <p class="gapTp"><span class="dateInfo"></span>跨机构管理的操作日志</p>
                        <div class="tb">
                            <table class="ty-table ty-table-control" id="opertionDayLog">
                                <tbody>
                                <tr>
                                    <td>操作对象</td>
                                    <td>修改内容</td>
                                    <td>修改时间</td>
                                    <td>修改前</td>
                                    <td>修改后</td>
                                </tr>
                                </tbody>
                            </table>
                            <div id="dayLog_ye"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/interagency/interagencyManagement.js?v=SVN_REVISION" type="text/javascript"></script>
</body>
</html>
