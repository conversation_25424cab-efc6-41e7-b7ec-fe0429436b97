<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%--<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>--%>
<%@ include  file="../../common/headerTop.jsp"%>
   	<link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
	<link rel="stylesheet" href="../css/typicons.min.css?v=SVN_REVISION">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">
    <style>
        .hd{display: none}
		 body{
	font-size: 14px;
	}
   @font-face {
	   font-family: 'typicons';
	   src: url("../css/typicons.eot");
	   src: url("../css/typicons.eot?#iefix") format('embedded-opentype'),
	   url("../css/typicons.woff") format('woff'),
	   url("../css/typicons.ttf") format('truetype'),
	   url("../css/typicons.svg#typicons") format('svg');
	   font-weight: normal;
	   font-style: normal;
   }
   .btn_back{
	   background-color: #e7505a;
	   color: #ffffff;
   }
   .ul_test{
	   list-style: none;
	   float: right;
   }
   .li_test{
	   width: 83px;
	   float: left;
	   margin-right: 16px;

   }
   .li_test .div_li{
	   border: 1px solid #e7505a;
	   /*border-radius: 14px;*/
	   padding: 5px;
	   color: #e7505a;
	   cursor: pointer;
	   text-align: center;
   }
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
	<div class="ty-header">
		<ul>
			<li class="active">
				<span>账户列表</span>
			</li>
		</ul>
		<div></div>
	</div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<div class="ty-container">
				<div id="wait" style="width: 200px;height: 200px;top:300px;left:50%;position: absolute;display: none;"><img alt="" src="../assets/oralResource/loading.gif"></div>

				<div class="ty-mainData">
					<div>
						<div style="height: 38px;">
							<div style="color: #5C9BD1;float: left;line-height: 3;margin-left: 28px;font-size: 18px;font-weight: bold;"><s:property value="#request.realname"/></div>

							<ul class="ul_test">
								<%--<li class="li_test" id="curDetail"><div class="div_li">当月</div></li>--%>
								<%--<li class="li_test" id="prevDetail"><div class="div_li">上月</div></li>--%>
								<li class="li_test" ><div class="div_li"  id="li_list"><span id="dropdown_btn">年度</span><span class="caret"></span></div>

									<s:iterator value="#request.years" id="year">
										<div style="display: none;border: 1px solid #cccccc;margin-top: 3px;"  class="div_test P_year"><s:property value="#year.years"/></div>
									</s:iterator>
								</li>
								<li class="li_test" id="all"><div class="div_li">全部</div></li>

								<li class="li_test" id="reb"><div class="div_li" style="background-color: #3499dd;color: #ffffff;border: none;"><span class="typcn typcn-arrow-back-outline"></span>返回</div></li>
							</ul>

						</div>
						<hr>

						<ul id="myTab" class="nav nav-tabs">
							<li class="active"><a id="a_turnin">转入<br><span id="span_turnin">${sumin}</span></a></li>
							<li><a id="a_turnout">转出<br><span id="span_turnout">${sumout}</span></a></li>
						</ul>

						<div id="myTabContent" class="tab-content">
							<div class="tab-pane fade in active" id="turnin">
								<p>
								<table class="ty-table ty-table-control" id="table_turnin">
									<thead>
									<tr>
										<td align="center">提交申请时间</td>
										<td align="center">实际处理时间</td>
										<td align="center">转入金额</td>
										<td align="center">账户余额</td>
									</tr>
									</thead>

									<s:iterator value="#request.listin" id="ope">
										<tr>
												<%--<td align="center"><s:property value="#ope.realname"/></td>--%>
											<td align="center"><s:date name="#ope.addtime" format="yyyy-MM-dd HH:mm"/></td>
											<s:if test="#ope.state==0">
												<td align="center">未完成</td>
											</s:if>
											<s:else>
												<td align="center"><s:date name="#ope.okTime" format="yyyy-MM-dd HH:mm"/></td>
											</s:else>
												<%--<td align="center"><s:property value="#ope.optype"/></td>--%>
											<td align="center"><s:property value="#ope.price"/></td>
											<%--<td align="center"><fmt:formatNumber value="${ope.price}" type="currency" pattern=".00"/></td>--%>
											<td align="center"><s:property value="#ope.account"/></td>

										</tr>
									</s:iterator>

								</table>

								</p>
							</div>
							<div class="tab-pane fade" id="turnout">
								<p>
								<table class="ty-table ty-table-control" id="table_turnout">
									<thead>
									<tr>
										<td align="center">申请转出时间</td>
										<td align="center">实际处理时间</td>
										<td align="center">操作内容</td>
										<td align="center">申请转出金额</td>
										<td align="center">实际转出金额</td>
										<td align="center">账户余额</td>
										<td align="center">操作人</td>

									</tr>
									</thead>

									<s:iterator value="#request.listout" id="ope">
										<tr>
											<td align="center"><s:date name="#ope.addtime" format="yyyy-MM-dd HH:mm"/></td>

											<s:if test="#ope.state == 0 && #ope.isIgnore == 0">
												<td align="center" >未处理</td>
											</s:if>
											<s:else>
												<td align="center" >${oktime}</td>
											</s:else>

											<s:if test="#ope.isIgnore == 0">
												<td align="center" >转出</td>
											</s:if>
											<s:else>
												<td align="center" >忽略</td>
											</s:else>

											<td align="center" >${price}</td>

											<s:if test="#ope.isIgnore == 0 && #ope.state == 1">
												<td align="center" >${price}</td>
											</s:if>
											<s:else>
												<td align="center" >0</td>
											</s:else>

											<td align="center"><s:property value="#ope.account"/></td>

											<td align="center"><s:property value="#ope.operator"/></td>

										</tr>
									</s:iterator>

								</table>
								<%--<div id = "paging">--%>
								<%--<div id="prepage">上一页</div>--%>
								<%--<div id="nextpage">下一页</div>--%>
								<%--<div id="firstpage">首页</div>--%>
								<%--<div id="endpage">尾页</div>--%>
								<%--<div id="len"></div>--%>
								<%--<div id="cur"></div>--%>
								<%--</div>--%>
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>


    	</div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
    <%@ include  file="../../common/footerTop.jsp"%>
    <%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
	$(function(){
		if($("#con tr").length > 1)
		{
			var len = "${request.size}";
			window.len = len;
			if(len > 0)
			{
				$.getScript("../script/paging.js");
			}
		}

		$("#reb").click(function(){
			 history.go(-1);
		});
		var exportstyle;
		$("#a_turnin").click(function(){
			$(this).parent().addClass("active").siblings().removeClass("active");
			$("#turnin").show();
			$("#turnout").hide();
			exportstyle = "startTerm";
		});
		$("#a_turnout").click(function(){
			$(this).parent().addClass("active").siblings().removeClass("active");
			$("#turnin").hide();
			$("#turnout").show().addClass("in active");
			exportstyle = "curTerm";
		});
		$("#a_turnin").click();
		<%--$("#curDetail").click(function(){--%>
		<%--ajaxSubmit("cur","${username}");--%>
		<%--});--%>
		<%--$("#prevDetail").click(function(){--%>
		<%--ajaxSubmit("prev","${username}");--%>
		<%--});--%>
		$("#all").click(function(){
			ajaxSubmit("all","${username}");
		});
		$(".P_year").click(function(){
			$("#dropdown_btn").html($(this).html());
			$(".div_test").slideToggle();
			ajaxSubmit("year","${username}",$(this).html());
		});
		function ajaxSubmit(style,username,year)
		{
			$.ajax({
				type: "post",
				dataType: 'json',
				timeout: 3000,
				cache: false,
				url: "../role/salary_detailOperate.action" ,
				data:{"style":style,"username":username,"superid":"${session.superid}","year":year},
				beforeSend: function(XMLHttpRequest){ $("#wait").show(); 	},
				success: function(data, textStatus){
					var  json = data ;
					var sumin = json.sumin ; if( sumin == 0){ sumin = "0.0" ;  }
					var sumout = json.sumout ; if( sumout == 0){ sumout = "0.0" ;  }
					$("#span_turnin").html(sumin);
					$("#span_turnout").html(sumout);
					$("#table_turnin tr:gt(0)").remove();
					$("#table_turnout tr:gt(0)").remove();
					var list = json.listin ;
					for(var i = 0 ; i < list.length ; i++ ){
                        var addtime = list[i].addtime;
                        var state = list[i].state;
                        var flag = "";
                        if(!state){flag = "未完成";} else{ flag = list[i].oktime; }
                        var price = list[i].price;
                        var account = list[i].account;
                        var tr = "<tr>" +
                            "<td align='center'>" + addtime + "</td>" +
                            "<td align='center'>" + flag + "</td>" +
                            "<td align='center'>" + price + "</td>" +
                            "<td align='center'>" + account + "</td>" + "</tr>";
                        $("#table_turnin").append(tr);
					}
					var list_out = json.listout ;
					for(var j = 0 ; j < list_out.length ; j++ ){
                        var addtime = list_out[j].addtime;
                        var state = list_out[j].state;
                        var ignore = list_out[j].isIgnore;
                        var flagOpetime;
                        if(!state && ignore == 0){ flagOpetime = "未处理"; } else{ flagOpetime = list_out[j].oktime;}
                        var flagOpecon;
                        if(ignore == 0){ flagOpecon = "转出"; }else{ flagOpecon = "忽略";  }
                        var flagRealprice;
                        var price = list_out[j].price;
                        var account = list_out[j].account;
                        if(ignore == 0 && state){  flagRealprice = price;  }else{   flagRealprice = 0;  }
                        var operator = list_out[j].operator;
                        var tr = "<tr>" +
                            "<td align='center'>" + addtime + "</td>" +
                            "<td align='center'>" + flagOpetime + "</td>" +
                            "<td align='center'>" + flagOpecon + "</td>" +
                            "<td align='center'>" + price + "</td>" +
                            "<td align='center'>" + flagRealprice + "</td>" +
                            "<td align='center'>" + account + "</td>" +
                            "<td align='center'>" + operator + "</td></tr>";

                        $("#table_turnout").append(tr);
					}

					$("#wait").hide();
				},

				complete:function(data,textStatus)
				{
					$("#wait").hide();
				},
				error:function(data,textStatus){$("#wait").hide();alert("数据加载失败，请稍后重试");}

			});
		}
//		$("#curDetail").click();
//		$(".div_li:first").addClass("btn_back").css({"color":"#ffffff"});
		$("#li_list").click(function(){$(".div_test").slideToggle();})
		$(".div_li:not(:last)").click(function(){
			$(".div_li:not(:last)").removeClass("btn_back").css({"color":"#e7505a"});
			$(this).addClass("btn_back").css({"color":"#ffffff"});

		});
	});
</script>
</body>
</html>
