<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
	<link rel="stylesheet" href="../css/typicons.min.css?v=SVN_REVISION">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">
	<style>
        .hd{display: none}
		 body{
	font-size: 14px;
	}
		.btn_back{
			background-color: #e7505a;
			color: #ffffff;
		}
		.ul_test{
			list-style: none;
			float: right;
		}
		.li_test{
			width: 83px;
			float: left;
			margin-right: 16px;

		}
	 .li_test .div_li{
		 border: 1px solid #e7505a;
		 /*border-radius: 14px;*/
		 padding: 5px;
		 color: #e7505a;
		 cursor: pointer;
         text-align: center;
	 }
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>账户列表</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div id="wait" style="  width: 200px; height: 200px;  top:300px; left:50%; position: absolute; display: none; "><img alt="" src="../assets/oralResource/loading.gif"></div>

                <div class="ty-mainData">
                    <div>
                        <div style="height: 38px;">
                            <div style="color: #5C9BD1;float: left;line-height: 3;font-size: 18px;font-weight: bold;"><s:property value="#request.realname"/></div>

                            <ul class="ul_test">
                                <li class="li_test" id="curDetail"><div class="div_li">当月</div></li>
                                <li class="li_test" id="prevDetail"><div class="div_li">上月</div></li>
                                <li class="li_test" ><div class="div_li" id="li_list"><span id="dropdown_btn">年度</span><span class="caret"></span></div>

                                    <s:iterator value="#request.years" id="year">
                                        <div style="display: none;border: 1px solid #cccccc;margin-top: 3px;" class="P_year"><s:property value="#year.years"/></div>
                                    </s:iterator>
                                </li>
                                <li class="li_test" id="all"><div class="div_li">全部</div></li>

                                <li class="li_test" id="reb"><div class="div_li" style="background-color: #3499dd;color: #ffffff;border: none;"><span class="typcn typcn-arrow-back-outline"></span>返回</div></li>
                            </ul>


                        </div>
                        <hr>
                        <span>收益合计:</span><span id="span_sum">${sum}</span>
                        <table class="ty-table ty-table-control" id="con">
                            <thead>
                            <tr>
                                <td align="center">当天收益</td>
                                <td align="center">当天利率</td>
                                <td align="center">收益所属日期</td>
                            </tr>
                            </thead>

                            <s:iterator value="#request.list" id="interest">
                                <tr>
                                    <td align="center"><s:property value="#interest.interest"/></td>
                                    <td align="center"><s:property value="#interest.rate"/>%</td>
                                    <td align="center"><s:date name="#interest.addtime" format="yyyy-MM-dd"/></td>
                                </tr>
                            </s:iterator>

                        </table>
                        <div id="ye-salary"></div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        var span_sum = $("#span_sum").html() ;
        if(Number(span_sum) == 0){
            span_sum = 0 ;
        }else {
            span_sum = Number(span_sum).toFixed(2) ;
        }
        $("#span_sum").html(span_sum) ;

        $("#reb").click(function(){
            //	location.href = "${pageContext.request.contextPath}/role/salary_list.action?superid=${session.superid}";
            history.go(-1);
        });
        $("#curDetail").click(function(){
            ajaxSubmit("cur","${username}");
        });
        $("#prevDetail").click(function(){
            ajaxSubmit("prev","${username}");
        });
        $("#all").click(function(){
            ajaxSubmit("all","${username}");
        });
        $("#dropdown_btn").click(function(){
            $(".P_year").slideToggle();
        });
        $(".P_year").click(function(){
            $(".P_year").slideToggle();
            $("#dropdown_btn").html($(this).html());
            ajaxSubmit("year","${username}",$(this).html());
        });
        $(".div_li:first").addClass("btn_back").css({"color":"#ffffff"});
        $("#li_list").click(function () {
            $("#div_test").slideToggle();
        });
        $(".div_li:not(:last)").click(function(){
            $(".div_li:not(:last)").removeClass("btn_back").css({"color":"#e7505a"});
            $(this).addClass("btn_back").css({"color":"#ffffff"});

        });

    });
    function ajaxSubmit(type,username,year){
        var data = {
            "type":type,
            "username":username,
            "superid":"${session.superid}",
            "year":year
        };
        $.ajax({
            url: "../role/salary_historyInterest.action",
            data:data,
            type: "post",
            dataType: 'json',
            timeout: 3000,
            cache: false,
            beforeSend: function(XMLHttpRequest){
                $("#wait").show();
            },
            success: function(data, textStatus){
                var json = eval(data);
                var num = json.sum ;
                if(num == 0){ num = 0;  }else{ num = num.toFixed(2) }
                $("#span_sum").html(num);
                $("#con tr").remove();

                var headStr;
                switch (type){
                    case "cur":
                    case "prev":
                        headStr =   "<thead><tr> <td align='center'>当天收益</td><td align='center'>当天收益率</td><td align='center'>收益所属日期</td> </tr></thead>";
                        break;
                    case "year":
                        headStr =   "<thead><tr> <td align='center'>月收益</td><td align='center'>月平均收益率</td><td align='center'>收益所属月份</td> </tr></thead>";
                        break;
                    case "all":
                        if(json.isYears>0){
                            headStr = "<thead><tr> <td align='center'>年收益</td><td align='center'>年平均收益率</td><td align='center'>收益所属年份</td> </tr></thead>";
                        }else{
                            headStr = "<thead><tr> <td align='center'>月收益</td><td align='center'>月平均收益率</td><td align='center'>收益所属月份</td> </tr></thead>";
                        }
                        break;
                    default:
                        break;
                }
                $("#con").html(headStr);
                $.each(json.list, function (index, item) {
                    var interest = json.list[index].interest;
                    var rate = json.list[index].rate;
                    var addtime = json.list[index].addtime;
                    if(type==="year" || type==="all")
                    {
                        rate = json.rate;
                        if(rate > 0)
                            rate = rate.toFixed(2);
                        if(type==="year")
                        {
                            addtime = addtime.substring(0,7);
                        }
                        else
                        {
                            if(json.isYears > 0)
                                addtime = addtime.substring(0,4);
                            else
                                addtime = addtime.substring(0,7);
                        }
                    }
                    var tr = "<tr>" +
                        "<td align='center'>" + interest + "</td>" +
                        "<td align='center'>" + rate + "</td>" +
                        "<td align='center'>" + addtime + "</td></tr>";
                    $("#con").append(tr);
                });

                $("#wait").hide();
            },

            complete:function(data,textStatus)
            {
                $("#wait").hide();
            },
            error:function(data,textStatus){$("#wait").hide();alert("数据加载失败，请稍后重试");}

        });
    }
</script>
</body>
</html>
