<!DOCTYPE html>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<html>
<!-- BEGIN HEAD -->
<head>

  <meta charset="utf-8" />

  <title></title>

  <meta content="width=device-width, initial-scale=1.0" name="viewport" />

  <meta content="" name="description" />

  <meta content="" name="author" />

  <!-- BEGIN GLOBAL MANDATORY STYLES -->

  <link href="../media/css/bootstrap.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

  <link href="../media/css/bootstrap-responsive.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

  <link href="../media/css/font-awesome.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

  <link href="../media/css/style-metro.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

  <link href="../media/css/style.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

  <link href="../media/css/style-responsive.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

  <link href="../media/css/default.css?v=SVN_REVISION" rel="stylesheet" type="text/css" id="style_color"/>

  <link href="../media/css/uniform.default.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>
  <script type="text/javascript" src="../script/pmScript/Common.js?v=SVN_REVISION" ></script>
  <!-- END GLOBAL MANDATORY STYLES -->

  <!-- BEGIN PAGE LEVEL STYLES -->

  <link rel="stylesheet" type="text/css" href="../media/css/select2_metro.css?v=SVN_REVISION" />

  <link rel="stylesheet" href="../media/css/DT_bootstrap.css?v=SVN_REVISION" />

  <!-- END PAGE LEVEL STYLES -->

  <link rel="shortcut icon" href="../media/image/favicon.ico" />
<script type="text/javascript">
  function doDelete(){
    if(confirm("删除该员工后，该员工将进入离职员工列表，且系统为保障运行速度，会在30天后清除员工相关资料，确定删除吗？"))
      return true;
    else
      return false;
  }
  function addUser(){
    $("#form1").submit();
  }
  function exportUser(){
    $("#form2").submit();
  }
  function showLz(){
    window.location.href='../pm/toEmpActiveListLz.do';
  }
  function showWb(){
    window.location.href='../pm/toEmpActiveListWb.do';
  }
</script>
</head>

<!-- END HEAD -->

<!-- BEGIN BODY -->

<body class="page-header-fixed">
<div class="row-fluid">

  <div class="span12">

    <!-- BEGIN EXAMPLE TABLE PORTLET-->

    <div class="portlet box blue">

      <div class="portlet-title">

        <div class="caption"><i class="icon-edit"></i>员工列表</div>

        <div class="tools">

          <a href="javascript:;" class="collapse"></a>

        </div>

      </div>

      <div class="portlet-body">

        <div class="clearfix">

          <div class="btn-group">
              <a class="btn blue" href="#" onclick="openWindow('../pm/addUser.do?userID=${u.userID}','yes',1000,600);">新增员工</a></i>

          </div>

          <div class="btn-group pull-right">
            <button class="btn dropdown-toggle green" data-toggle="" onclick="showLz();">离职列表

            </button>
            <button class="btn dropdown-toggle green" data-toggle="" onclick="showWb();">外部人员

            </button>

            <button class="btn dropdown-toggle green" data-toggle="dropdown">操作 <i class="icon-angle-down"></i>

            </button>

            <ul class="dropdown-menu pull-right">

              <li><a class="btn green" href="../role/menuAction_export.action">员工导出</a></li>

              <li><a class=" btn green" data-toggle="modal" href="#stack1">员工导入</a></li>

              <li><a class="btn green" href="../role/menuAction_exportLx.action">导出通讯录</a></li>

            </ul>

          </div>

        </div>

        <table class="table table-striped table-hover table-bordered table-full-width" id="sample_editable_1">

          <thead>

          <tr>
            <th>员工序号</th>
            <th>姓名</th>

            <th>性别</th>

            <th>联系电话</th>

            <th>部门</th>

            <th>职位</th>

            <th>入职时间</th>
            <th>学历</th>
            <th>年龄</th>
            <th>工作日记</th>
            <th>基本信息</th>
            <th>详细信息</th>
            <th>操作</th>

          </tr>

          </thead>

          <tbody>
        <c:forEach items="${users}" var="u">
          <c:if test="${u.isDuty=='1'}">
          <tr class="">
            <td>${u.logonName}</td>
            <td>${u.userName}</td>
            <td>
                ${u.gender}
            </td>
            <td>${u.contactTel}</td>
            <td>${u.department}</td>
            <td>${u.postID}</td>
            <td>${u.onDutyDate}</td>
            <td>${u.edu}</td>
            <td>${u.age}</td>
            <td><a href="#" onclick="openWindow('../pm/toMolLook.do?userID=${u.userID}','yes',1000,1000);">查看</a></td>
            <td>
              <a href="#" onclick="openWindow('../pm/toEmpBasic.do?userID=${u.userID}','yes',1000,600);">查看</a>
            </td>
            <td>
              <a href="#" onclick="openWindow('../pm/toEmpDetail.do?userID=${u.userID}','yes',500,400);">查看</a>
            </td>
            <td>
              <a href="../system/lz.do?userID=${u.userID}" onclick="return doDelete();">离职</a>
            </td>
          </tr>
            </c:if>
          </c:forEach>
          </tbody>
        </table>
      </div>
    </div>
    <div id="stack1" class="modal hide fade" tabindex="-1" data-focus-on="input:first">
      <div class='modal-header'>
        <button class='close' data-dismiss='modal' type='button'>&times;</button>
        <h3>员工导入</h3>
      </div>
      <div class='modal-body'>
        <form namespace="/role" id="form2" action="../pm/importUserData.do" method="post" enctype="multipart/form-data">
          <div class='box-header green-background'>
            <div class='title'>文件选择</div>
            <div class='actions'>
              </a>
              </a>
            </div>
          </div>
          <div class='box-content'>
            <strong>请选择excel格式的文件进行导入</strong><a href="../role/menuAction_downUserExcel.action">下载Excel模板</a>
            <div>
              <input title='添加文件' type='file' name="excelFile" class="btn green"/>
            </div>
          </div>
        </form>
      </div>
      <div class='modal-footer'>
        <button class='btn' data-dismiss='modal'>关闭</button>
        <button class='btn btn-primary' onclick="exportUser()">保存</button>
      </div>
    </div>
    </div>
  <div id="responsive" class="modal hide fade" tabindex="-1" data-width="760">
    <div class='modal-header'>
      <button class='close' data-dismiss='modal' type='button'>&times;</button>
      <h3>新增员工</h3>
      <button class='btn' data-dismiss='modal'>关闭</button>
      <button class='btn btn-primary' onclick="addUser()">保存</button>
    </div>
    <div class='modal-body'>
      <form class='form' id="form1" style='margin-bottom: 0;' method="post" action="../system/addUser.do"/>
      <div class='control-group'>
        <div class='controls'>l<input data-rule-minlength='2' data-rule-required='true' id='validation_name' name='userName' placeholder='姓名' type='text' />
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          性&nbsp;&nbsp;&nbsp;&nbsp;别<select data-rule-required='true' id='validation_select' name='gender'>
          <option />
          <option />男l
          <option />女
        </select>
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          部&nbsp;&nbsp;&nbsp;&nbsp;门
          <select data-rule-required='true' id='validation_department' name='department'>
            <option value=""></option>
            <c:forEach items="${departments}" var="d">
              <option value="${d.name}">${d.name}</option>
            </c:forEach>
          </select>
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          职&nbsp;&nbsp;&nbsp;&nbsp;位
          <input data-rule-minlength='2' data-rule-password='true' data-rule-required='true' id='validation_postID' name='postID' placeholder='职位' type="text" />
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          学&nbsp;&nbsp;&nbsp;&nbsp;历<input   id='validation_edu' name='edu' placeholder='学历' type="text" />
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          薪&nbsp;&nbsp;&nbsp;&nbsp;酬 <input id='validation_salary' name='salary' placeholder='薪酬' type='text' />
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          身份证号<input  data-rule-minlength='18'  id='validation_idCard' name='idCard' placeholder='身份证号' type='text' />
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          出生日期<div class='datepicker input-append' id='datepicker'>
          <input id="validation_birthday" name="birthday" class='input-medium' data-format='yyyy-MM-dd' placeholder='yyyy/mm/dd' type='text' />
        </div>
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          手机号码<input  data-rule-minlength='11' data-rule-required='true' id='validation_contactTel' name='contactTel' placeholder='11位有效手机号' type='text' />
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          籍&nbsp;&nbsp;&nbsp;&nbsp;贯<select data-rule-required='true' id='validation_nativePlace' name='nativePlace'>
          <option value="北京">北京市</option>
          <option value="浙江省">浙江省</option>
          <option value="天津市">天津市</option>
          <option value="安徽省">安徽省</option>
          <option value="上海市">上海市</option>
          <option value="福建省">福建省</option>
          <option value="重庆市">重庆市</option>
          <option value="江西省">江西省</option>
          <option value="山东省">山东省</option>
          <option value="河南省">河南省</option>
          <option value="湖北省">湖北省</option>
          <option value="湖南省">湖南省</option>
          <option value="广东省">广东省</option>
          <option value="海南省">海南省</option>
          <option value="山西省">山西省</option>
          <option value="青海省">青海省</option>
          <option value="江苏省">江苏省</option>
          <option value="辽宁省">辽宁省</option>
          <option value="吉林省">吉林省</option>
          <option value="台湾省">台湾省</option>
          <option value="河北省">河北省</option>
          <option value="贵州省">贵州省</option>
          <option value="四川省">四川省</option>
          <option value="云南省">云南省</option>
          <option value="陕西省">陕西省</option>
          <option value="甘肃省">甘肃省</option>
          <option value="黑龙江省">黑龙江省</option>
          <option value="香港特别行政区">香港特别行政区</option>
          <option value="澳门特别行政区">澳门特别行政区</option>
          <option value="广西壮族自治区">广西壮族自治区</option>
          <option value="宁夏回族自治区">宁夏回族自治区</option>
          <option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
          <option value="内蒙古自治区">内蒙古自治区</option>
          <option value="西藏自治区">西藏自治区</option>
        </select>
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          户籍性质 <input   id='validation_nativeType' name='nativeType' placeholder='户籍性质' type='text' />
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          家庭住址<input   id='validation_address' name='address' placeholder='家庭住址' type='text' />
        </div>
      </div>
      <div class='control-group'>
        <div class='controls'>
          入职日期 <div class='datepicker input-append' id='datepicker3'>
          <input id="validation_onDutyDate" name="onDutyDate" class='input-medium' data-format='yyyy-MM-dd' placeholder='yyyy/mm/dd' type='text' />
        </div>
        </div>
      </div>
      </form>
    </div>
  </div>
    <!-- END EXAMPLE TABLE PORTLET-->
  </div>
</div>




<script src="../media/js/jquery-1.10.1.min.js?v=SVN_REVISION" type="text/javascript"></script>

<script src="../media/js/jquery-migrate-1.2.1.min.js?v=SVN_REVISION" type="text/javascript"></script>

<!-- IMPORTANT! Load jquery-ui-1.10.1.custom.min.js before bootstrap.min.js to fix bootstrap tooltip conflict with jquery ui tooltip -->

<script src="../media/js/jquery-ui-1.10.1.custom.min.js?v=SVN_REVISION" type="text/javascript"></script>

<script src="../media/js/bootstrap.min.js?v=SVN_REVISION" type="text/javascript"></script>

<!--[if lt IE 9]>

<script src="../media/js/excanvas.min.js?v=SVN_REVISION"></script>

<script src="../media/js/respond.min.js?v=SVN_REVISION"></script>

<![endif]-->

<script src="../media/js/jquery.slimscroll.min.js?v=SVN_REVISION" type="text/javascript"></script>

<script src="../media/js/jquery.blockui.min.js?v=SVN_REVISION" type="text/javascript"></script>

<script src="../media/js/jquery.cookie.min.js?v=SVN_REVISION" type="text/javascript"></script>

<script src="../media/js/jquery.uniform.min.js?v=SVN_REVISION" type="text/javascript" ></script>

<!-- END CORE PLUGINS -->

<!-- BEGIN PAGE LEVEL PLUGINS -->

<script type="text/javascript" src="../media/js/select2.min.js?v=SVN_REVISION"></script>

<script type="text/javascript" src="../media/js/jquery.dataTables.js?v=SVN_REVISION"></script>

<script type="text/javascript" src="../media/js/DT_bootstrap.js?v=SVN_REVISION"></script>

<!-- END PAGE LEVEL PLUGINS -->

<!-- BEGIN PAGE LEVEL SCRIPTS -->

<script src="../media/js/app.js?v=SVN_REVISION"></script>

<script src="../media/js/table-editable.js?v=SVN_REVISION"></script>

<script>

  jQuery(document).ready(function() {

    App.init();

    TableEditable.init();

  });

</script>

</body>

<!-- END BODY -->
</html>
