<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/xml" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <link rel="stylesheet" href="../css/typicons.min.css?v=SVN_REVISION">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">
    <style>
        .hd{display: none}
		  body{
		font-size: 14px; 	}
	 @font-face {
		 font-family: 'typicons';
		 src: url("../css/typicons.eot");
		 src: url("../css/typicons.eot?#iefix") format('embedded-opentype'),
		 url("../css/typicons.woff") format('woff'),
		 url("../css/typicons.ttf") format('truetype'),
		 url("../css/typicons.svg#typicons") format('svg');
		 font-weight: normal;
		 font-style: normal;
	 }
		.history_salary
		{
			color: #666666;border: 1px solid #cccccc;
			text-decoration: none;
		}
		.history_salary:hover{
			color: #333333;border: 1px solid #333333;
			text-decoration: none;
		}
		.a_salary{
			font-size: 12px;
			border: 1px solid #cccccc;
			padding: 2px 17px;
		}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
	<%--温馨提示--%>
	<div class="bonceContainer bounce-green" id="tip">
		<div class="bonceHead">
			<span>温馨提示：</span>
			<a class="bounce_close" onclick="bounce.cancel()"></a>
		</div>
		<div class="bonceCon">
			<div class="addpayDetails">
				<div class="shu1">
					<p id="tip_ms" style="text-align: center; padding:10px 0"> </p>
				</div>
			</div>
		</div>
		<div class="bonceFoot">
			<span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
		</div>
	</div>
	<%--修改后果查看--%>
	<div class="bonceContainer bounce-blue" id="upInfo">
		<div class="bonceHead">
			<span>修改后果查看</span>
			<a class="bounce_close" onclick="bounce.cancel()"></a>
		</div>
		<div class="bonceCon">
			<div class="addpayDetails">
				<div class="shu1">
					<p id="upInfo_ms" style="text-align: center; padding:10px 0"> </p>
				</div>
			</div>
		</div>
		<div class="bonceFoot">
			<span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
		</div>
	</div>
	<%-- 修改工资 --%>
	<div class="bonceContainer bounce-green" id="updateSalary">
		<div class="bonceHead">
			<span>修改工资</span>
			<a class="bounce_close" onclick="bounce.cancel()"></a>
		</div>
		<div class="bonceCon">
			 <span>修改后工资 ：</span> <input type="text" id="salaryNew" class="ty-input" onkeyup="clearNoNum(this)">
		</div>
		<div class="bonceFoot">
			<span id="tipms" style="color: red;"></span>
			<span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
			<span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="submitConfirm()">确定</span>
		</div>
	</div>
	<%-- 修改工资- 确认框 --%>
	<div class="bonceContainer bounce-green" id="upComfirm">
		<div class="bonceHead">
			<span>修改工资</span>
			<a class="bounce_close" onclick="bounce.cancel()"></a>
		</div>
		<div class="bonceCon">
			<div id="upComfirmMs">  </div>
		</div>
		<div class="bonceFoot">
			<span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="submitUp()">确定</span>
		</div>
	</div>
	<%-- 修改工资成功 --%>
	<div class="bonceContainer bounce-green" id="upOk">
		<div class="bonceHead">
			<span>修改工资成功：</span>
            <a class="bounce_close" onclick="location.href=''"></a>
		</div>
		<div class="bonceCon">
			<div id="upResult">  </div>
		</div>
		<div class="bonceFoot">
			<span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="location.href=''">确定</span>
		</div>
	</div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
	<div class="ty-header">
		<ul>
			<li class="active">
				<span>员工历史工资</span>
			</li>
		</ul>
		<div></div>
	</div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<div class="ty-container">
				<div class="ty-mainData">
					<div style="height: 38px;">
						<%--<div id="realname" style="color: #5C9BD1;float: left;line-height: 3;font-weight: bold;">${request.realname}</div>--%>
						<div style="float: right">
							<%--<button id="export" type="button" class="btn btn-default"><span class="typcn typcn-printer"></span>导出</button>--%>
							<span id="return" class="ty-btn ty-btn-big ty-circle-5"><span class="typcn typcn-arrow-back-outline"></span>返回</span>
						</div>
					</div>

					<ul class="ty-secondTab" >
						<li class="ty-active">${request.realname} 的工资修改/补录记录</li>
					</ul>
					<table class="ty-table ty-table-control" style="margin-bottom:50px;">
						<thead>
						<tr>
							<td>说明</td>
							<td>年份</td>
							<td>考勤所属月份</td>
							<td>原工资金额</td>
							<td>改后的工资金额</td>
							<td>原工资录入时间</td>
							<td>录入人</td>
							<td>修改/补录时间</td>
							<td>录入人</td>
							<td>操作</td>
						</tr>
						</thead>
						<tbody id="firList"></tbody>
					</table>
					<ul class="ty-secondTab" >
						<li class="ty-active">${request.realname} 的工资记录</li>
					</ul>
					<table id="con" class="ty-table ty-table-control">
						<thead>
						<tr>
							<td align="center">年份</td>
							<td align="center">考勤所属月份</td>
							<td align="center">工资</td>
							<td align="center">录入时间</td>
							<td align="center">录入人</td>
							<td align="center">操作</td>
						</tr>
						</thead>
						<tbody id="orlTbl">
						<s:iterator value="#request.list" id="salary">
							<tr>
								<td name="year"><s:property value="#salary.year"/></td>
								<td name="month"><s:property value="#salary.month"/></td>
								<td><s:property value="#salary.salary" /></td>
								<td><s:date name="#salary.addtime" format="yyyy-MM-dd HH:mm:ss"/></td>
								<td><s:property value="#salary.editor"/></td>
								<s:if test="#salary.updateable == 0">
									<td><span class="ty-color-blue" info="{'empid':'${empid}','id': '<s:property value="#salary.id"/>' , 'prompt':'<s:property value="#salary.prompt" />' , 'isShut':'${isShut}' }"  onclick="updateSalary0($(this))">修改</span> </td>
								</s:if>
								<s:else>
									<td><span class="ty-color-gray" info="{'empid':'${empid}','id': '<s:property value="#salary.id"/>' , 'prompt':'<s:property value="#salary.prompt" />' , 'isShut':'${isShut}' }"  >修改</span> </td>
								</s:else>
							</tr>
						</s:iterator>
						</tbody>
					</table>
					<div id = "paging">
						<div id="prepage">上一页</div>
						<div id="nextpage">下一页</div>
						<div id="firstpage">首页</div>
						<div id="endpage">尾页</div>
						<div id="len"></div>
						<div id="cur"></div>
					</div>
				</div>
			</div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    var isShut_G = "${isShut}"  ;
    $(function(){
        // 修改前的
		$("#orlTbl").children("tr").each(function(){
			var num = $(this).children(":eq(2)").html() ;
            $(this).children(":eq(2)").html(parseFloat(num)) ;
		}) ;

        // 返回按钮
        $("#return").click(function(){
            var curDate = getUrlParam("curDate");
            var salaryDay = getUrlParam("salaryDay");
            if(Boolean(curDate && salaryDay)){
                location.href = "../salaryPC/salaryPC_finance.action?curDate=" + curDate + "&salaryDay=" + salaryDay;
			}else{
                location.href = "../salaryPC/salaryPC_finance.action";
            }
        });
        // creator : 侯杏哲 2017-10-21 获取 所有补录和修改的工资数据
		$.ajax({
            url: "../role/salary_getSupplyAndHis.action",
			data:{ "username":getUrlParam("username") } ,
			success:function (data) {
				var list = data["listSupply"] ,  // 补录
					list2 = data["listHistory"] ,  // 修改
					str = "";
				if(list && list.length > 0){
				    for(var i=0 ; i<list.length ; i++){
                        if(list[i]["flag"] == 1 &&( list[i]["deliver"]==1)){
                            str += "<tr>" +
                                "<td>补录工资</td>" +
                                "<td>"+ list[i]["year"] +"</td>" +
                                "<td>"+ list[i]["month"] +"</td>" +
                                "<td>0</td>" +
                                "<td>"+ list[i]["salary"] +"</td>" +
                                "<td>— —</td>" +
                                "<td>— —</td>" +
                                "<td>"+ list[i]["addtime"].substr(0,19) +"</td>" +
                                "<td>"+ list[i]["editor"] +"</td>" ;
                            if( list[i]["updateable"] == 0){
                                str += "<td><span class='ty-color-blue' info='"+ JSON.stringify(list[i]) +"' onclick='updateSalary1($(this))'>修改</span> </td>" ;
                            }else {
                                str += "<td><span class='ty-color-gray' info='"+ JSON.stringify(list[i]) +"' >修改</span> </td>" ;
							}
                            str += "</tr>" ;
                        }
					}
				}
				if(list2 && list2.length > 0){
				    for(var i=0 ; i<list2.length ; i++){
						str += "<tr>" +
							"<td>修改工资</td>" +
							"<td>"+ list2[i]["year"] +"</td>" +
							"<td>"+ list2[i]["month"] +"</td>" +
							"<td>"+ list2[i]["salaryOld"] +"</td>" +
							"<td>"+ list2[i]["salaryNew"] +"</td>" +
							"<td>"+ list2[i]["addtimeOld"].substr(0,19) +"</td>" +
							"<td>"+ list2[i]["editorOld"] +"</td>" +
							"<td>"+ list2[i]["addtimeNew"].substr(0,19) +"</td>" +   // .substr(0,18)
							"<td>"+ list2[i]["editorNew"] +"</td>" ;
                        if( list2[i]["updateable"] == 0){
                            str += "<td><span class='ty-color-blue' info='"+ JSON.stringify(list2[i]) +"' onclick='updateSalary1($(this))'>修改</span> " ;
                        }else {
                            str += "<td><span class='ty-color-gray' info='"+ JSON.stringify(list2[i]) +"' >修改</span>" ;
                        }
                        str += "<span class='ty-color-blue' info='"+ JSON.stringify(list2[i]) +"' onclick='showMemo($(this))' >修改后果查看</span> </td>" +
							"</tr>" ;
					}
				}
				$("#firList").html(str) ;
            }
		});
    });
// creator : 侯杏哲 2017-10-24 下面的修改按钮
var editInfo = null ;
function updateSalary0(obj) {
    editInfo = {} ;
    var info =   obj.attr("info")  ;
	info = eval('('+ info +')');
    editInfo["id"] = info["id"] ;
    editInfo["prompt"] = info["prompt"] ;
    editInfo["empid"] = info["empid"] ;
    editInfo["editTr"] = obj.parent().parent() ;
    editInfo["superid"] = getUrlParam("superid") ;
    editInfo["realname"] = getUrlParam("realname") ;
    editInfo["username"] = getUrlParam("username") ;
    editInfo["isShut"] = info["isShut"] ;
    editInfo["year"] = obj.parent().siblings(":eq(0)").html() ;
    editInfo["month"] = obj.parent().siblings(":eq(1)").html() ;
    editInfo["salaryOld"] = obj.parent().siblings(":eq(2)").html() ;
	$("tipms").html("");
    bounce.show($("#updateSalary"));
    $("#salaryNew").val("");
}
// creator: 侯杏哲 2017-10-25 上面的修改按钮
function updateSalary1(obj) {
    editInfo = {} ;
    var info =   obj.attr("info")  ;
	info = eval('('+ info +')');
    editInfo["id"] = info["id"] ;
//    editInfo["prompt"] = info["prompt"] ;
    editInfo["empid"] = info["empid"] ;
    editInfo["editTr"] = obj.parent().parent() ;
    editInfo["superid"] = getUrlParam("superid") ;
    editInfo["realname"] = getUrlParam("realname") ;
    editInfo["username"] = getUrlParam("username") ;
    editInfo["isShut"] = isShut_G ;
    editInfo["year"] = obj.parent().siblings(":eq(1)").html() ;
    editInfo["month"] = obj.parent().siblings(":eq(2)").html() ;
    editInfo["salaryOld"] = obj.parent().siblings(":eq(4)").html() ;
	$("tipms").html("");
    bounce.show($("#updateSalary"));
}
// creator : 侯杏哲 2017-10-21 确定修改提示框
function submitConfirm() {
    var salaryNew = $("#salaryNew").val() ;
    if($.trim(salaryNew) == ""){
        $("#tipms").html("请输入修改后工资！") ;
        return false ;
	}
	bounce.show( $("#upComfirm") ) ;  $("#upComfirmMs").html("确定将工资修改为" + salaryNew + "元") ;
}
// creator : 侯杏哲 2017-10-21 确定修改
function submitUp(){
    var salaryNew = $("#salaryNew").val() ;
    if( $.trim( salaryNew ) == "" ){  $("tipms").html("请前些修改后工资"); 	return false ;   }
    $.ajax({
        url: "../role/salary_updateSalary.action",
		data:{
		    "superid": editInfo["superid"] ,
			"realname":editInfo["realname"] ,
			"username":editInfo["username"] ,
			"isShut":editInfo["isShut"] ,
			"year":editInfo["year"] ,
			"month":editInfo["month"] ,
			"salaryOld":editInfo["salaryOld"] ,
			"salaryNew":salaryNew ,
			"id":editInfo["id"] ,
			"empid":editInfo["empid"] ,
			"prompt" : editInfo["prompt"]
		} ,
		type:"post" ,
		beforeSend:function(){ loading.open();  },
		success:function (data) {
		    var result = data["result"] ;
		    if(result == -1){
                bounce.show(  $("#upOk") );  $("#upResult").html("该工资条还没有发放，不能修改。") ;
			}else{
                var str = data["prompt"];
                if(str == ""){
                    bounce.show(  $("#upOk") );  $("#upResult").html("还没到工资日，请返回上一页重新编辑") ;
                }else {
                    bounce.show(  $("#upOk") );  $("#upResult").html(str) ;
                }
			}

        },
		error:function () {
			bounce.show($("#tip")); $("#tip_ms").html("链接错误！")
        },
		complete:function () { loading.close();  }
	})
}
// creactor: 侯杏哲 2017-10-23  修改后果查看
function showMemo( obj ) {
	var info  = JSON.parse( obj.attr("info")) ;
	var prompt = info["prompt"] ;
	bounce.show($("#upInfo"));
	$("#upInfo_ms").html(prompt);
}
</script>
</body>
</html>
