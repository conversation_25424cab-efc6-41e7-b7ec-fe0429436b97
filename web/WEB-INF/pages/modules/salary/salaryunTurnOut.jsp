
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <link rel="stylesheet" href="../css/typicons.min.css?v=SVN_REVISION">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">

	<style>
        .hd{display: none}
		body{
	font-size: 14px;
	}
	@font-face {
		font-family: 'typicons';
		src: url("../css/typicons.eot");
		src: url("../css/typicons.eot?#iefix") format('embedded-opentype'),
		url("../css/typicons.woff") format('woff'),
		url("../css/typicons.ttf") format('truetype'),
		url("../css/typicons.svg#typicons") format('svg');
		font-weight: normal;
		font-style: normal;
	}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
	<div class="ty-header">
		<ul>
			<li class="active">
				<span>账户列表</span>
			</li>
		</ul>
		<div></div>
	</div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<div class="ty-container">
				<div>
					<p>
						<span class="navTxt"><i class="fa fa-home"></i> 薪资宝</span>
						<span class="nav_"> / </span>
						<span class="navTxt"
							  onclick="location.href = '../salaryPC/salaryPC_list.action?superid=${session.superid}'"><a
								href="javascript:;"><i class=""></i>转出列表</a></span>
						<span class="nav_"> / </span>
						<span class="navTxt"><i class=""></i>未完成列表</span>
					</p>
					<div class="ty-left">转出未完成用户列表  -- 共 <span class="ty-btn ty-btn-orange ty-circle-3">${sizeCur + sizeHis}</span> 条</div>
					<div class="clr"></div>
				</div>
				<ul class="ty-secondTab">
					<li class="ty-active">本月未完成</li>
					<li>往期未完成</li>
				</ul>
				<div class="ty-mainData">
					<table class="ty-table ty-table-control opinionCon">
						<thead>
						<tr>
							<td align="center">姓名</td>
							<td align="center">提交时间</td>
							<td align="center">申请转出金额</td>
							<td align="center">当前剩余</td>
							<td align="center">处理</td>
						</tr>
						</thead>

						<s:iterator value="#request.listCur" id="user">
							<tr>
								<td align="center"><s:property value="#user.realname"/></td>
								<td align="center"><s:date name="#user.addtime" format="yyyy-MM-dd HH:mm:ss"/></td>
								<td align="center"><s:property value="#user.price"/></td>
								<td align="center"><s:property value="#user.account"/></td>
								<td align="center">
									  <span class="ty-color-blue ok"
										  id="<s:property value='#user.id'/>"
										  price="<s:property value='#user.price'/>"
										  username="<s:property value='#user.username'/>"
										  account="<s:property value='#user.account'/>">完成</span>
									<span class="ty-color-red ignore"
										  id="<s:property value='#user.id'/>"
										  price="<s:property value='#user.price'/>"
										  username="<s:property value='#user.username'/>">忽略</span>
								</td>
							</tr>
						</s:iterator>
					</table>
					<table class="ty-table ty-table-control hd opinionCon">
						<thead>
						<tr>
							<td align="center">姓名</td>
							<td align="center">提交时间</td>
							<td align="center">申请转出金额</td>
							<td align="center">当前剩余</td>
							<td align="center">处理</td>
						</tr>
						</thead>

						<s:iterator value="#request.listHis" id="user">
							<tr>
								<td align="center"><s:property value="#user.realname"/></td>
								<td align="center"><s:date name="#user.addtime" format="yyyy-MM-dd HH:mm:ss"/></td>
								<td align="center"><s:property value="#user.price"/></td>
								<td align="center"><s:property value="#user.account"/></td>
								<td align="center">
									 <span class="ty-color-blue ok"
										  id="<s:property value='#user.id'/>"
										  price="<s:property value='#user.price'/>"
										  username="<s:property value='#user.username'/>"
										  account="<s:property value='#user.account'/>">完成</span>

									 <span class="ty-color-red ignore"
											id="<s:property value='#user.id'/>"
											price="<s:property value='#user.price'/>"
											username="<s:property value='#user.username'/>">忽略</span>
								</td>
							</tr>
						</s:iterator>
					</table>
				</div>
			</div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){

		//点击事件-切换列表
		$(".ty-secondTab li").on("click",function () {
			var i=parseInt($(this).index());

			//导航末尾添加状态名,设置切换状态cookie
			var name=$(this).text();
			$.cookie('state', i);
			$(".ty-header p span:last").html(name);

			//设置切换
			$(".ty-secondTab li").removeClass("ty-active");
			$(this).addClass("ty-active");
			//展示内容
			$(".ty-mainData .opinionCon").addClass("hd");
			$(".ty-mainData .opinionCon").eq(i).removeClass("hd");
		});


        $(".ok").click(function(){
            if(confirm("是否确定已经将金额转给用户?")){
                var str = "../role/salary_unTurnOut.action?superid=${session.superid}&id=" + $(this).attr("id") + "&price=" + $(this).attr("price") + "&username=" + $(this).attr("username") + "&account=" + $(this).attr("account");
                location.href = str;
                return true;
            }
            else
                return false;
        });

        $(".ignore").click(function(){
            if(confirm("是否确定忽略此次转出?")){
                var str = "../role/salary_ignoreTurnOut.action?superid=${session.superid}&id=" + $(this).attr("id") + "&username=" + $(this).attr("username") + "&price=" + $(this).attr("price");
                location.href = str;
                return true;
            }
            else
                return false;
        });
		init();

    });
	/* creator：张旭博，2017-04-11 09:56:09，初始化 */
	function init() {
		if($.cookie("state")){
			$(".ty-secondTab li").eq($.cookie("state")).click();
		}else{
			$(".ty-secondTab li:first").click();
		}
	}
    function showBg(name,salary) {
        var bh = window.screen.height;
        var bw = window.screen.width;
        $("#fullbg").css({
            height:bh,
            width:bw,
            display:"block"
        });
        $("#dialog [name='realname']").val(name);
        $("#dialog [name='salary']").val(salary);
        $("#dialog").show(200);
    }
    //关闭灰色 jQuery 遮罩
    function closeBg() {
        $("#fullbg").hide(200);
        $("#dialog").hide(200);
    }

</script>
</body>
</html>
