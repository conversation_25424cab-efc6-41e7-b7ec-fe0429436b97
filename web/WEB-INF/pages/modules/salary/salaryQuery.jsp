<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
   	<link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">
    <style>
        .hd{display: none}
			body{
	font-size: 14px;
	}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>薪资宝</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div style="width: 90%;margin-left: auto;margin-right: auto;margin-bottom: 10px;text-align: right;">
                    <span style="background-color: #f5fafe;border: 1px solid #cccccc;cursor: pointer;font-size: 12px;padding: 5px 10px;" id="reflush">刷新</span>
                </div>
                <table id="con" class="table table-striped table-hover table-bordered table-full-width">
                    <tr>
                        <td align="center">期末余额</td>
                        <td align="center" >期末账户总数</td>
                        <td align="center" >本月开户总数</td>
                        <td align="center" >本月转入总额</td>
                        <td align="center" >本月转出总额</td>
                        <td align="center" >平均年化收益率</td>
                        <td align="center" >本月利息支出</td>
                    </tr>
                    <%-- <s:iterator value="#session.pagelist" id="car"> --%>
                    <tr>
                        <td align="center">${account}</td>
                        <td align="center">${users}</td>
                        <td align="center">${monthuser}</td>
                        <td align="center">${inprice}</td>
                        <td align="center">${outprice}</td>
                        <td align="center">${session.avgRate}%</td>
                        <td align="center">${suminterest}</td>
                    </tr>
                   <%-- </s:iterator> --%>

                </table>

                <table id="con" class="table table-striped table-hover table-bordered table-full-width">
                    <tr>
                        <td align="center">截止到上月余额</td>
                        <td align="center">截止到上月账户总数</td>
                        <td align="center">上月开户总数</td>
                        <td align="center">上月转入总额</td>
                        <td align="center">上月转出总额</td>
                        <td align="center">上月平均年化收益率</td>
                        <td align="center">上月利息支出</td>
                    </tr>
                    <%-- <s:iterator value="#session.pagelist" id="car"> --%>
                    <%-- <s:iterator value="#session.pagelist" id="car"> --%>
                    <tr>
                        <td align="center">${preaccount}</td>
                        <td align="center">${preusers}</td>
                        <td align="center">${premonthuser}</td>
                        <td align="center">${preinprice}</td>
                        <td align="center">${preoutprice}</td>
                        <td align="center">${preavgRate}%</td>
                        <td align="center">${presuminterest}</td>
                    </tr>
                   <%-- </s:iterator> --%>
                </table>
                <div style="background-color:#f5fafe;width:90%;height: 38px;margin-left: auto;margin-right: auto;
                    margin-top: 50px;text-align: left;padding-left: 20px;font-size: 20px;line-height: 2;">
                    薪资宝总帐户查询
                </div>

                <form style="margin-top: 30px;text-align: center;margin-left: auto;margin-right: auto;font-size: 14px;"
                      action="../role/salary_queryBydate.action" id="query">
                    按时段
                    <input type="text" name="begin" onClick="WdatePicker();" readonly="readonly" id="d1"/> 至
                    <input type="text" name="end" onClick="WdatePicker({minDate:'#F{$dp.$D(\'d1\',{d:0})}'});" readonly="readonly"/>
                    <input type="hidden" name="superid" value="${session.superid}">
                    <input type="submit" value="查询"/>
                    <input id="expert" type="button" value="高级搜索"/>
                </form>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        $("#reflush").click(function(){
            location.href = "../role/salary_query.action?superid=${session.superid}";
        });
        /*
         $("#query").click(function(){
         if($("[name='begin']").val()=="" || $("[name='end']").val()=="")
         return false;
         });
         */
        $("#expert").click(function(){
            location.href = "../role/salary_ExpertQuery.action?superid=${session.superid}";
        });
    });
</script>
</body>
</html>
