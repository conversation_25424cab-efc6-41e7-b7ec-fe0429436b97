<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
       <LINK href="../css/salaryStyle.css.css?v=SVN_REVISION" type="text/css" rel="stylesheet">

    <style>
        .hd{display: none}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<form name="Form1" method="post" id="Form1" action="../salary/saveSalary.do">
    <br>
    <table cellSpacing="1" cellPadding="5" width="580" align="center" bgColor="#eeeeee" style="border:1px solid #8ba7e3" border="0">
        <tr>
            <td class="ta_01" align="center" colSpan="4" background="../images/b-info.gif">
                <font face="宋体" size="2"><strong>薪资宝系统设置</strong></font>
            </td>
        </tr>
        <tr>

            <input type="hidden" name="unitId" value="${unit.unitId}">


            <td width="15%" height="22" align="center" bgColor="#f5fafe" class="ta_01">工资总额告知时间：</td>
            <td width="35%" class="ta_01" bgColor="#ffffff">
                <input name="salaryNoticeTime" type="text" id="salaryNoticeTime"  size="20" maxlength="25" value="${salary.salaryNoticeTime}" onClick="WdatePicker();"> <font color="#FF0000">*</font></td>
        </tr>
        <tr>
            <td width="15%" height="22" align="center" bgColor="#f5fafe" class="ta_01">工资发放时间：</td>
            <td width="35%" class="ta_01" bgColor="#ffffff"><input name="salaryIssueTime" type="text" id="salaryIssueTime"  size="20" maxlength="25" value="${salary.salaryIssueTime}" onClick="WdatePicker();"> <font color="#FF0000">*</font></td>

        <tr>
            <td width="15%" height="22" align="center" bgColor="#f5fafe" class="ta_01">年化收益率：</td>
            <td width="35%" class="ta_01" bgColor="#ffffff">
                <input name="APY" type="text" id="APY"  size="20" maxlength="50" value="${salary.APY}"><font color="#FF0000">*</font></td>


        <tr>
            <td class="ta_01" style="WIDTH: 100%" align="center" bgColor="#f5fafe" colSpan="4">


                <input type="submit" name="BT_Submit" value="保存" id="BT_Submit" style="font-size:12px; color:black; height=22;width=55" onClick="check_null()">
                <FONT face="宋体">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</FONT>


                <INPUT style="font-size:12px; color:black; height=22;width=55" type="reset" value="关闭"  NAME="Reset1" onClick="window.close();">
            </td>
        </tr>
    </table>
    　
</form>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript" language="JavaScript" src="../script/function.js?v=SVN_REVISION" ></script>
<Script language="javascript">
    function check_null(){

        var theForm=document.Form1;



        if(Trim(theForm.salaryNoticeTime.value)=="")
        {
            alert("告知时间不可为空");
            theForm.salaryNoticeTime.focus();
            return false;
        }
        if(Trim(theForm.salaryIssueTime.value)=="")
        {
            alert("工资发放时间不可为空");
            theForm.salaryIssueTime.focus();
            return false;
        }
        if(Trim(theForm.APY.value)=="")
        {
            alert("年化收益率不可为空");
            theForm.APY.focus();
            return false;
        }

        document.Form1.submit();


    }

    function refreshThisOpener(sHref){

        opener.gotopage(sHref,"nowpage");
        window.close ();
    }
</script>
</body>
</html>
