<%@ page import="java.util.Date" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" pageEncoding="UTF-8"%>


<html>
<head>
    <LINK href="../css/Style.css?v=SVN_REVISION" type="text/css" rel="stylesheet">
    <title>更改申请单</title>
    <script type="text/javascript" src="../My97DatePicker/WdatePicker.js?v=SVN_REVISION"></script>
    <script type="text/javascript" language="JavaScript" src="../script/function.js?v=SVN_REVISION" ></script>
    <script type="text/javascript" src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>
    <script language="javascript">
        function check_null(){
            $("#Form1").attr("action","../app/saveApplication.do");
            $("#Form1").submit();
        }

        function refreshThisOpener(sHref){

            opener.gotopage(sHref,"addnewstation");
            window.close ();
        }

    </script>
</head>

<body>
<form name="Form1" method="post" id="Form1" action="#">
    <br>
    <table cellSpacing="1" cellPadding="5" width="580" align="center" bgColor="#eeeeee" style="border:1px solid #8ba7e3" border="0">

        <tr>
            <td class="ta_01" align="center" colSpan="4" background="../images/b-info.gif">
                <font face="宋体" size="2"><strong>填写申请单</strong></font>
            </td>
        </tr>

        <tr>
            <td width="15%" height="22" align="center" bgColor="#f5fafe" class="ta_01">申请日期：</td>
            <td width="35%" class="ta_01" bgColor="#ffffff">
                <input name="applyTime" id="applyTime" type="text" value="" onclick="WdatePicker();">
            </td>
            <td width="15%" height="22" align="center" bgColor="#f5fafe" class="ta_01">申请人：</td>
            <td width="35%" class="ta_01" bgColor="#ffffff">
                <input name="userName" type="text" id="userName"  size="20" maxlength="25" value="${sessionScope.user.userName}" readonly> <font color="#FF0000">*</font></td>
                <input type="hidden" name="userId" value="${sessionScope.user.userID}">
        </tr>
        <tr>
            <td width="15%" height="22" align="center" bgColor="#f5fafe" class="ta_01">将内容：</td>
            <td width="35%"width="35%" class="ta_01" bgColor="#ffffff"><textarea style="height: 100px;" name="oldContent"></textarea> <font color="#FF0000">*</font></td>
            <td width="15%" height="22" align="center" bgColor="#f5fafe" class="ta_01">修改为：</td>
            <td width="35%"width="35%" class="ta_01" bgColor="#ffffff"><textarea style="height: 100px;" name="newContent"></textarea> <font color="#FF0000">*</font></td>
        </tr>
        <tr>
            <td width="15%" height="22" align="center" bgColor="#f5fafe" class="ta_01">修改理由及摘要：</td>
            <td width="35%" class="ta_01" bgColor="#ffffff" colspan="4">
                <textarea name="content" style="width: 100%; height: 160px;"></textarea>
            </td>
        </tr>
        <tr>
            <td class="ta_01" style="WIDTH: 100%" align="center" bgColor="#f5fafe" colSpan="4">
                <input type="button" name="BT_Submit" value="提交" id="BT_Submit" style="font-size:12px; color:black; height:22px;width:55px;" onclick="check_null()">
                <FONT face="宋体">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</FONT>
                <INPUT style="font-size:12px; color:black; height=22;width=55" type="reset" value="关闭"  NAME="Reset1" onclick="javascript:history.go(-1);;">
            </td>
        </tr>
    </table>
    　
</form>
</body>
</HTML>
