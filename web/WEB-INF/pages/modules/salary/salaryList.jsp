<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>

    <link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">
    <style>
        .hd{display: none}
        body{
            font-size: 14px;
        }
        .a_salarylist{
            border: 1px solid #ccc;
            color: #666;
            font-size: 12px;
            padding: 3px 13px;
        }
        .shutdown:hover{
            color: #333333;border: 1px solid #333333;
            text-decoration: none;
        }
        .a_salary{
            font-size: 12px;
            border: 1px solid #cccccc;
            padding: 2px 17px;
            color: #ffffff;
        }

        .closeContainer {
            display: none;
        }
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<input type="hidden" id="superid" value="${session.superid}">
<div class="bounce">
    <%--温馨提示--%>
    <div class="bonceContainer bounce-blue" id="closeCount">
        <div class="bonceHead">
            <span>温馨提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <input type="hidden" id="setType">
                    <p style="text-align: center; padding:10px 0" id="tip">？</p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" onclick="closeCountOk()">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>账户列表</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div >
                    <div class="main">
                        <div style="float: left">
                            <span id="outCount" class="ty-btn ty-btn-blue ty-btn-big ty-circle-5">${outCount}条转出未完成</span>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-5"
                                  onclick="closeCountListBtn()">已关闭的账户</span>
                        </div>
                        <div style="float: right">
                            <form role="form" action="../salaryPC/salaryPC_list.action">
                                按部门 <select class="form-control" name="depart"
                                            style="width: 150px;display: inline-block;padding-top: 4px;height: 30px;"
                                            id="qdepart">
                                <option value="depart">全部</option>
                                <s:iterator value="#request.listDeparts" id="de">
                                    <option value=<s:property value='#de.depart'/>><s:property value='#de.depart'/></option>
                                </s:iterator>
                            </select>
                                按姓名 <select class="form-control" name="phone"
                                            style="width: 150px;display: inline-block;padding-top: 4px;height: 30px;"
                                            id="qname">
                                <option value="username">全部</option>
                                <s:iterator value="#request.listNames" id="uname">
                                    <option value=
                                                <s:property value='#uname.username'/> depart=<s:property
                                            value='#uname.depart'/>><s:property value='#uname.realname'/></option>
                                </s:iterator>
                            </select>
                                <%--<input type="submit" value="查询">--%>
                                <button type="submit" class="ty-btn ty-btn-green ty-btn-big ty-circle-5"
                                        style="outline: 0;border:none">查询
                                </button>
                                <input type="hidden" name="superid" value="${session.superid}">
                            </form>
                        </div>
                    </div>
                    <div class="closeContainer">
                        <span class="ty-btn ty-btn-blue ty-btn-big ty-right ty-circle-5" onclick="goback()">返回</span>
                    </div>
                </div>
                <div class="ty-mainData">
                    <div class="main">
                        <table id="con" class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td>员工编号</td>
                                <td>姓名</td>
                                <td>开户时间</td>
                                <td>账户余额</td>
                                <td>部门</td>
                                <td>职位</td>
                                <td>操作详情</td>
                                <td>收益详情</td>
                                <td>关闭账户</td>
                            </tr>
                            </thead>
                            <s:iterator value="#request.listUsers" id="user">
                                <tr username="<s:property value='#user.username'/>"
                                    realname="<s:property value='#user.realname'/>">
                                    <td><s:property value="#user.empid"/></td>
                                    <td><s:property value="#user.realname"/></td>
                                    <s:if test="#request.isDisplayRegTime">
                                        <td><s:date name="#user.regtime" format="yyyy-MM-dd HH:mm:ss"/></td>
                                    </s:if><s:else>
                                    <td><s:property value="#isDisplayRegTime"/>— —</td>
                                </s:else>
                                    <td><s:property value="#user.account"/></td>
                                    <td><s:property value="#user.depart"/></td>
                                    <td><s:property value="#user.duty"/></td>
                                    <td class="ty-td-control"><span class="ty-color-blue"
                                                                    onclick="scan($(this), 1)">查看</span></td>
                                    <td class="ty-td-control"><span onclick="scan($(this) , 2)"
                                                                    class="ty-color-blue">查看</span></td>
                                    <td class="ty-td-control"><a class="ty-color-red"
                                                                 onclick="closeOrStartBtn($(this) , 0)">关闭</a></td>
                                </tr>
                            </s:iterator>
                        </table>
                        <div id = "paging">
                            <div id="prepage">上一页</div>
                            <div id="nextpage">下一页</div>
                            <div id="firstpage">首页</div>
                            <div id="endpage">尾页</div>
                            <div id="len"></div>
                            <div id="cur"></div>
                        </div>
                    </div>
                    <div class="closeContainer">
                        <table class="ty-table ty-table-control" id="closeList">
                            <thead>
                            <tr>
                                <td>员工编号</td>
                                <td>姓名</td>
                                <td>关闭时间</td>
                                <td>部门</td>
                                <td>职位</td>
                                <td>操作详情</td>
                                <td>收益详情</td>
                                <td>开启账户</td>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>

                    <div id="dialog">
                        <p class="close"><img src="../assets/oralResource/close.png" name="close"/></p>
                        <div>
                            <div style="font-size: 12px;text-align: center; padding:10px;">
                                <form id="conditions" action="../salaryPC/salaryPC_editSalary.action">
                                    姓名：<input type="text" name="realname"/><br><br>
                                    当月工资：<input type="text" name="salary"/><br><br>
                                    <input type="hidden" name="superid" value="${session.superid}">
                                    <input type="hidden" name="username" value="">
                                    <input type="submit" id="subedit" value="保存"/>
                                </form>
                            </div>
                        </div>
                    </div>
                    <!-- jQuery 遮罩层 -->
                    <div id="fullbg"></div>
                    <!-- end jQuery 遮罩层 -->
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript" src="/script/salary/salaryList.js?v=SVN_REVISION"></script>
</body>
</html>
