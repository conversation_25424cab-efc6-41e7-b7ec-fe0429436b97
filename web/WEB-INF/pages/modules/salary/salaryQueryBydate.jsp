
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
     	<link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
		<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">

	<style>
        .hd{display: none}
		body{
	font-size: 14px;
	}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<div id="wait" style="width: 200px;height: 200px; top:300px;left:50%;position: absolute;display: none;">
                <img alt="" src="../images/loading.gif">
            </div>
            <div style="width: 90%;margin-left: auto;margin-right: auto;margin-bottom: 20px;font-size: 24px;color:#333333;text-align: left;">
                从 <span style="color:#e60012">${begin} </span> 到 <span style="color:#e60012">${end}</span> 期间账户资金情况如下
            </div>
            <table class="ty-table ty-table-control">
				<thead>
                <tr>
                    <td align="center">期末余额</td>
                    <td align="center">期末账户总数</td>
                    <td align="center">本期开户总数</td>
                    <td align="center">本期转入总额</td>
                    <td align="center">本期转出总额</td>
                    <td align="center">平均年化收益率</td>
                    <td align="center">本期利息支出</td>
                </tr>
                </thead>
                <%-- <s:iterator value="#session.pagelist" id="car"> --%>
				<tr>
                    <td align="center" id="ttacc">${account}</td>
					<td align="center">${users}</td>
					<td align="center">${monthuser}</td>
					<td align="center">${inprice}</td>
					<td align="center">${outprice}</td>
                    <td align="center">${session.avgRate}%</td>
					<td align="center">${suminterest}</td>
				</tr>
               <%-- </s:iterator> --%>
			</table>
            <div style="text-align:center;height: 38px;margin-left: auto;margin-right: auto;padding-left: 20px;font-size: 20px;line-height: 2;">
                <form id="qbyname" style="margin-top: 30px;text-align:center;margin-left: auto;margin-right: auto;font-size: 14px;" action="">按部门
                    <select style="width:150px;" name="depart" id="qdepart">
                        <option value="depart">全部</option>
                        <s:iterator value="#request.listdepart" id="depart">
                            <option value="<s:property value='#depart.depart'/>"><s:property value='#depart.depart'/></option>
                        </s:iterator>
                    </select>
                    按姓名
                    <select style="width:150px;" name="realname" id="qname">
                        <option value="realname">全部</option>
                        <s:iterator value="#request.listuname" id="uname">
                            <option value="<s:property value='#uname.username'/>" depart="<s:property value='#uname.depart'/>"><s:property value='#uname.realname'/></option>
                        </s:iterator>
                    </select>
                    <input type="hidden" name="superid" value="${session.superid}">
                    <input type="submit" value="查询"/>
                </form>
            </div>
            <table id="con" class="table table-striped table-hover table-bordered table-full-width">
				<tr>
					<td align="center">用户编号</td>
					<td align="center">姓名</td>
					<td align="center">部门</td>
					<td align="center">转入金额</td>
					<td align="center">转出金额</td>
                    <td align="center">年化收益率</td>
					<td align="center">利息支出</td>
					<td align="center">账户余额</td>
	            </tr>
                <%-- <s:iterator value="#session.pagelist" id="car"> --%>
				<%-- <tr onmouseover="this.style.backgroundColor = 'white'" onmouseout="this.style.backgroundColor = '#F5FAFE';">
                    <td align="center" width="3%" height="30px;">${account}</td>
					<td align="center" width="6%" height="30px;">${users}</td>
					<td align="center" width="6%" height="30px;">${monthuser}</td>
					<td align="center" width="5%" height="30px;">${outprice}</td>
					<td align="center" width="6%" height="30px;">${inprice}</td>
                    <td align="center" width="6%" height="30px;">${session.avgRate}</td>
					<td align="center" width="6%" height="30px;">${suminterest}</td>
					<td align="center" width="6%" height="30px;">${suminterest}</td>
				</tr> --%>
               <%-- </s:iterator> --%>
			</table>

            <div id = "paging">
                <div id="prepage">上一页</div>
                <div id="nextpage">下一页</div>
                <div id="firstpage">首页</div>
                <div id="endpage">尾页</div>
                <div id="len"></div>
                <div id="cur"></div>
            </div>
            <form style="margin-top: 30px;margin-left: auto;margin-right: auto;font-size: 14px;text-align:center;" action="../role/salary_queryBydate.action" id="query">
                按时段
                <input type="text" name="begin" onClick="WdatePicker();" readonly="readonly" id="d1"/> 至 <input type="text" name="end" onClick="WdatePicker({minDate:'#F{$dp.$D(\'d1\',{d:0})}'});" readonly="readonly"/>
                <input type="hidden" name="superid" value="${session.superid}">
                <input type="submit" value="查询"/>
            </form>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        $("#qdepart").change(function(){
            $("#qname").children().show();
            var depart = $(this).val();
            if(depart != 'depart')
                $("#qname").children("[depart!=" + depart + "]:not(':first')").hide();
        });

        $("#qbyname").submit(function(){
            var depart = $("[name='depart']").val();
            var realname = $("[name='realname']").val();
            if(depart == 'depart')
//				depart = "so.depart";
                depart = "depart";
            if(realname == 'realname')
//				realname = "so.username";
                realname = "username";

            ajaxSubmit("${begin}","${end}",depart,realname);
            return false;
        });
        function ajaxSubmit(begin,end,depart,realname)
        {
            $.ajax({

                type: "post",
                dataType: 'json',
                timeout: 3000,
                cache: false,
                url: "../role/salary_getQueryList.action",

                data:{"begin":begin,"end":end,"depart":depart,"realname":realname,"superid":"${session.superid}"},

                beforeSend: function(XMLHttpRequest){

                    $("#wait").show();
                },
                success: function(data, textStatus){
                    var json = eval(data);
                    //alert(json);
                    $("#con tr:gt(0)").remove();
                    //su.realname, su.regtime, su.account, su.depart, sinp.inprice, soutp.outprice, sinterest.outinterest
                    $.each(json, function (index, item) {
//						 	var ids = json[index].id;
                        var ids = json[index].empid;
                        var realname = json[index].realname;
                        var depart = json[index].depart;
                        //var regtime = json[index].regtime;
                        var account = json[index].account;
                        var inprice = json[index].inprice;
                        var outprice = json[index].outprice;
                        var outinterest = json[index].outinterest;
                        var tr = "<tr onmouseover=this.style.backgroundColor = 'white' onmouseout=this.style.backgroundColor = '#F5FAFE'>" +
                                "<td align='center' width='6%' height='30px;'>" + ids + "</td>" +
                                "<td align='center' width='3%' height='30px;'>" + realname + "</td>" +
                                "<td align='center' width='3%' height='30px;'>" + depart + "</td>" +
                                "<td align='center' width='3%' height='30px;'>" + inprice + "</td>" +
                                "<td align='center' width='3%' height='30px;'>" + outprice + "</td>" +
                                "<td align='center' width='3%' height='30px;'>" + "${session.avgRate}%" + "</td>" +
                                "<td align='center' width='3%' height='30px;'>" + outinterest + "</td>" +
                                "<td align='center' width='3%' height='30px;'>" + account + "</td>" + "</tr>";
                        $("#con").append(tr);
                    });
                    $("#querycon tr:gt(0)").hide();
                    if(json.length > 0)
                    {
                        $("#paging").show();
                        paging(json.length);
                    }

                },

                complete:function(data,textStatus)
                {
                    $("#wait").hide();
                },
                error:function(data,textStatus){alert("数据加载失败，请稍后重试");}

            });
        }

        function paging(obj)
        {
            //var arrayObj = new Array(1,2,3,4,5,6,7,8,9,10);
            //var len = arrayObj.length;
            var len = obj;
            //var len = "${request.size}";
            window.len = len;
            if(len > 0)
            {
                $.getScript("../script/paging.js");
            }
        }
        $("#qbyname").submit();
    });
</script>
</body>
</html>
