<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
   	<link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
	<link rel="stylesheet" href="../css/typicons.min.css?v=SVN_REVISION">
	<link rel="stylesheet" type="text/css" href="../css/dateL.css?v=SVN_REVISION">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">

<style>
	.hd{display: none}
	body{
		font-size: 14px;
	}

	.query_li{
		height: 80px;
		border: 1px solid #cccccc;
		float: left;
		width: 30%;
		margin-right: 20px;
		padding: 15px;
		cursor: pointer;
	}

	#cur_li:hover{border: 1px solid #2AB4C0;}
	#prev_li:hover{border: 1px solid #F36A5A;}
	#total_li:hover{border: 1px solid #5C9BD1;}

	.span_detail{
		color: #5C9BD1;
		cursor: pointer;
	}
</style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
	<div class="ty-header">
		<ul>
			<li class="active">
				<span>总查询</span>
			</li>
		</ul>
		<div></div>
	</div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<div class="ty-container">
				<p>
					<span class="navTxt"><i class="fa fa-home"></i> 薪资宝</span>
					<span class="nav_"> / </span>
					<span class="navTxt"><i class=""></i>总查询</span>
					<span class="nav_"> / </span>
					<span class="navTxt"><i class=""></i>按日期查询</span>
				</p>
				<div class="ty-mainData">
					<ul style="list-style: none;height: 80px;padding: 0;">
						<li style="" class="query_li" id="cur_li"><span style="color: #2AB4C0;font-size: 25px;">${curCount}</span><br><span>本期开户总数</span></li>
						<li style="" class="query_li" id="total_li"><span style="color: #5C9BD1;font-size: 25px;">${allCount}</span><br><span>期末账户总数</span></li>
					</ul>
					<div style="height: 38px;margin-top:10px">
						<div style="color: #5C9BD1;float: left;line-height: 3;">${first}~${last} 资金情况</div>
						<div style="float: right">
							<span id="export" class="ty-btn ty-btn-green ty-btn-big ty-circle-5"><span class="typcn typcn-printer"></span>导出</span>
							<span id="return" class="ty-btn ty-btn-big ty-circle-5"><span class="typcn typcn-arrow-back-outline"></span>返回</span>
						</div>
						<div class='dateL' style="float: right;margin-right: 50px;"></div>
					</div>
					<hr>
					<table id="con" class="ty-table ty-table-control">
						<thead>
						<tr>
							<td align="center" >时间</td>
							<td align="center" >转入总额</td>
							<td align="center" >转出总额</td>
							<td align="center" >平均年化收益率</td>
							<td align="center" >利息支出</td>
							<td align="center" >期末余额</td>
						</tr>
						</thead>
						<tr>
							<td align="center" >${first}~${last}</td>
							<td align="center" >${inprice}<span class="ty-btn ty-btn-blue"  id="in">查看详情</span></td>
							<td align="center" >${outprice}<span class="ty-btn ty-btn-blue" id="out">查看详情</span></td>
							<td align="center" >${session.avgRate}%</td>
							<td align="center" >${suminterest}</td>
							<td align="center" >${account}</td>
						</tr>
					</table>
				</div>
			</div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript" src="/script/dateL.js?v=SVN_REVISION"></script>
<script type="text/javascript">
    $(function(){

        var first = "${first}";
        var last = "${last}";
        var first_array = first.split("-");
        var last_array = last.split("-");
        DateLI("searchfun", 2010,2030,true,false,{start_y:parseInt(first_array[0]),start_m:parseInt(first_array[1]),start_d:parseInt(first_array[2])},{end_y:parseInt(last_array[0]),end_m:parseInt(last_array[1]),end_d:parseInt(last_array[2])});

        $("#return").click(function(){
            location.href = "../salaryPC/salaryPC_query2.action?superid=${session.superid}";
        });
        $("#export").click(function(){
            location.href = "../salaryPC/salaryPC_exportForOne.action?superid=${session.superid}&first=${first}&last=${last}";
        });

        $("#in").click(function () {
            location.href = "../salaryPC/salaryPC_operateDetailQuery.action?superid=${session.superid}&style=in&first=${first}&last=${last}";
        });
        $("#out").click(function () {
            location.href = "../salaryPC/salaryPC_operateDetailQuery.action?superid=${session.superid}&style=out&first=${first}&last=${last}";
        });

        $("#cur_li").click(function(){
            location.href = "../salaryPC/salaryPC_curQuery.action?superid=${session.superid}&start=${first}&end=${last}&what=本期";
        });

        $("#total_li").click(function(){
            location.href = "../salaryPC/salaryPC_totalQuery.action?superid=${session.superid}&start=${first}&end=${last}";
        });

    });
    function searchfun(){
        // GET EVERY INPUT'S VALUE
        var start_y = $(".start_y").val();
        var end_y = $(".end_y").val();
        var start_m = $(".start_m").val();
        var end_m = $(".end_m").val();
        var start_d = $(".start_d").val();
        var end_d = $(".end_d").val();
        if(start_m < 10)
            start_m = "0" + start_m;
        if(end_m < 10)
            end_m = "0" + end_m;
        if(start_d < 10)
            start_d = "0" + start_d;
        if(end_d < 10)
            end_d = "0" + end_d;
        var firstdate = start_y+"-"+start_m+"-"+start_d;
        var lastdate = end_y+"-"+end_m+"-"+end_d;
        location.href = "../salaryPC/salaryPC_queryForOne.action?superid=${session.superid}&first=" + firstdate + "&last=" + lastdate;
    }
</script>
</body>
</html>
