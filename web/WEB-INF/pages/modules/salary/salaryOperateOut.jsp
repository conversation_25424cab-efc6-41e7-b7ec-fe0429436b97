
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <link rel="stylesheet" href="../css/typicons.min.css?v=SVN_REVISION">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">

    <style>
        .hd{display: none}
		@font-face {
			font-family: 'typicons';
			src: url("../css/typicons.eot");
			src: url("../css/typicons.eot?#iefix") format('embedded-opentype'),
			url("../css/typicons.woff") format('woff'),
			url("../css/typicons.ttf") format('truetype'),
			url("../css/typicons.svg#typicons") format('svg');
			font-weight: normal;
			font-style: normal;
		}
		body{
		font-size: 14px;
		}

		.query_li{
			height: 80px;
			border: 1px solid #cccccc;
			border-radius: 5px;
			float: left;
			width: 30%;
			margin-right: 20px;
			padding: 15px;
			cursor: pointer;
		}

		#cur_li:hover{border: 2px solid #2AB4C0;}
		#prev_li:hover{border: 2px solid #F36A5A;}
		#total_li:hover{border: 2px solid #5C9BD1;}

		.span_detail{
			color: #5C9BD1;
			cursor: pointer;
		}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
	<div class="ty-header">
		<ul>
			<li class="active">
				<span>总查询</span>
			</li>
		</ul>
		<div></div>
	</div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
  		<div class="ty-container">
			<p>
				<span class="navTxt"><i class="fa fa-home"></i> 薪资宝</span>
				<span class="nav_"> / </span>
				<span class="navTxt"><i class=""></i> 总查询</span>
				<span class="nav_"> / </span>
				<span class="navTxt"><i class=""></i> ${head}</span>
			</p>
			<div class="ty-mainData">
				<div>
					<div style="height: 38px;">
						<div style="color: #5C9BD1;float: left;line-height: 3;">${first}~${last} ${title}</div>
						<div style="float: right">
							<span id="export" class="ty-btn ty-btn-green ty-btn-big ty-circle-5"><span class="typcn typcn-printer"></span>导出</span>
							<span id="return" class="ty-btn ty-btn-big ty-circle-5"><span class="typcn typcn-arrow-back-outline"></span>返回</span>
						</div>
					</div>
					<hr>
					<table id="con" class="ty-table ty-table-control">
						<thead>
						<tr>
							<td align="center" >员工编号</td>
							<td align="center" >姓名</td>
							<td align="center" >操作内容</td>
							<td align="center" >申请转出金额</td>
							<td align="center" >实际转出金额</td>
							<td align="center" >申请转出时间</td>
							<td align="center" >实际处理时间</td>
							<td align="center" >账户余额</td>
							<td align="center" >部门</td>
							<td align="center" >职位</td>
							<td align="center" >操作人</td>
						</tr>
						</thead>
						<s:iterator value="#request.listOut" id="user">
							<tr>
								<td align="center" >${empid}</td>
								<td align="center" >${realname}</td>
								<s:if test="#user.isIgnore == 0">
									<td align="center" >转出</td>
								</s:if>
								<s:else>
									<td align="center" >忽略</td>
								</s:else>
								<td align="center" >${price}</td>

								<s:if test="#user.isIgnore == 0 && #user.state == 1">
									<td align="center" >${price}</td>
								</s:if>
								<s:else>
									<td align="center" >0</td>
								</s:else>

								<td align="center" >${addtime}</td>
								<s:if test="#user.state == 0 && #user.isIgnore == 0">
									<td align="center" >未处理</td>
								</s:if>
								<s:else>
									<td align="center" >${oktime}</td>
								</s:else>
								<td align="center" >${account}</td>
								<td align="center" >${depart}</td>
								<td align="center" >${duty}</td>
								<td align="center">${operator}</td>
							</tr>
						</s:iterator>
					</table>
				</div>
			</div>
		</div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        $("#return").click(function(){
            location.href = "../salaryPC/salaryPC_query2.action?superid=${session.superid}";
        });
        $("#export").click(function(){
              location.href = "../salaryPC/salaryPC_exportOperateDetail.action?superid=${session.superid}&first=${first}&last=${last}&style=${head}";
        });
        <%--alert("${cur_first} + ${cur}");--%>
    });
</script>
</body>
</html>
