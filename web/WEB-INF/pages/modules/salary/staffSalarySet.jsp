
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <LINK href="../css/Style.css?v=SVN_REVISION" type="text/css" rel="stylesheet">
    <LINK href="../css/openView.css?v=SVN_REVISION" type="text/css" rel="stylesheet">

	<style>
        .hd{display: none}
		.activeNode{
			border: 2px solid red;
		}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<form id="Form1" name="Form1" action="" method="post" style="margin:0px;"> 
		<table cellspacing="1" cellpadding="0" width="90%" align="center" bgcolor="#f5fafe" border="0">
			<TR height=10><td></td></TR>
			<tr>
				<td class="ta_01" colspan=2 align="center" background="../images/b-info.gif">
					<font face="宋体" size="2"><strong>员工工资设置</strong></font>
				</td>
				
			</tr>
	    </table>	
	</form>

	<form id="Form2" name="Form2" action="" method="post">
	
		<table cellSpacing="1" cellPadding="0" width="90%" align="center" bgColor="#f5fafe" border="0">
			<TBODY>
				<TR height=10><td></td></TR>			
				<tr>
				  	<td>
		                <TABLE style="WIDTH: 105px; HEIGHT: 20px" border="0">
							<TR>
								<TD align="center" background="../images/cotNavGround.gif"><img src="../images/yin.gif" width="15"></TD>
								<TD class="DropShadow" background="../images/cotNavGround.gif">员工列表</TD>
							</TR>
			             </TABLE>
	                   </td>
					<td class="ta_01" align="right">
						<input style="font-size:12px; color:black; height=20;width=80" id="BT_Back" type="button" value="返回" name="BT_Back" 
						 onclick="javascript:history.go(-1);">&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
				  	<td class="ta_01" align="left" bgColor="#f5fafe" colspan="2">

	                   </td>
				</tr>
				<tr>
					<td class="ta_01" align="center" bgColor="#f5fafe" colspan="2">			
						<table cellspacing="0" cellpadding="1" rules="all" bordercolor="gray" border="1" id="DataGrid1"
							style="BORDER-RIGHT:gray 1px solid; BORDER-TOP:gray 1px solid; BORDER-LEFT:gray 1px solid; WIDTH:100%; WORD-BREAK:break-all; BORDER-BOTTOM:gray 1px solid; BORDER-COLLAPSE:collapse; BACKGROUND-COLOR:#f5fafe; WORD-WRAP:break-word">
						
							<tr style="FONT-WEIGHT:bold;FONT-SIZE:12pt;HEIGHT:25px;BACKGROUND-COLOR:#afd1f3">
								<td align="center" width="25%" height=22 background="../images/tablehead.jpg">员工姓名</td>
								<td align="center" width="25%" height=22 background="../images/tablehead.jpg">当前工资</td>
								<td width="20%" align="center" height=22 background="../images/tablehead.jpg">新工资</td>
								<td width="20%" align="center" height=22 background="../images/tablehead.jpg">操作</td>
							</tr>
									<tr onMouseOver="this.style.backgroundColor = 'white'" onMouseOut="this.style.backgroundColor = '#F5FAFE';">
										<td style="HEIGHT:22px" align="center" width="25%">
											${salaryTreasure.realName}
										</td>
										<td style="HEIGHT:22px" align="center" width="25%">
                                            ${salaryTreasure.salary}
										</td>
										<td style="HEIGHT:22px" align="center" width="20%">
											<input type="text" name="salary" id="salary">
                                            <input type="hidden" value="${salaryTreasure.strId}" id="strId">
										</td>
                                        <td style="HEIGHT:22px" align="center" width="20%">
                                            <input type="button" value="保存" onClick="updateSalary();">
                                        </td>
									</tr>
						</table>
					</td>
				</tr>     
			</TBODY>
		</table>
	</form>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
	<script language="javascript" src="../script/function.js?v=SVN_REVISION"></script>
	<script type="text/javascript" src="../script/highslide/highslide.js?v=SVN_REVISION"></script>
	<script type="text/javascript" src="../script/highslide/highslide-html.js?v=SVN_REVISION"></script>
<script type="text/javascript">
    function updateSalary(){
        var salary=document.getElementById("salary").value;
        var strId=document.getElementById("strId").value;
        window.location.href='../salary/updateStaffSalary.do?strId='+strId+'&salary='+salary;
    }

    hs.graphicsDir = '../script/highslide/graphics/';
    hs.outlineType = 'rounded-white';
    hs.outlineWhileAnimating = true;
</script>
</body>
</html>
