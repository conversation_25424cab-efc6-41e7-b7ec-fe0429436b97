<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link href="../assets/layouts/layout/css/custom.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
  	<link rel="stylesheet" type="text/css" href="../css/carStyle.css?v=SVN_REVISION">
	<style>
        .hd{display: none}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			
            <table cellspacing="0" cellpadding="0" width="90%" align="center" bgcolor="#f5fafe" border="0">
                <TR>
                    <TD align="center" background="../images/cotNavGround.gif" width=25><img src="../images/yin.gif" width="15"></TD>
                    <TD class="DropShadow" background="../images/cotNavGround.gif" width=80>司机列表</TD>
                    <td class="ta_01" align="right" ></td>
                </TR>
              </table>

            <table cellspacing="1" cellpadding="0" width="90%" align="center" bgcolor="#f5fafe" border="0">
                 <tr>
                      <td class="ta_01" align="center" bgcolor="#f5fafe">
                        <table cellspacing="0" cellpadding="1" rules="all" bordercolor="gray" border="1" id="DataGrid1" style="BORDER-RIGHT:gray 1px solid; BORDER-TOP:gray 1px solid; BORDER-LEFT:gray 1px solid; WIDTH:100%; WORD-BREAK:break-all; BORDER-BOTTOM:gray 1px solid; BORDER-COLLAPSE:collapse; BACKGROUND-COLOR:#f5fafe; WORD-WRAP:break-word">
                            <tr style="FONT-WEIGHT:bold;FONT-SIZE:12pt;HEIGHT:25px;BACKGROUND-COLOR:#afd1f3">
                                <td align="center" width="3%" height=22 background="../images/tablehead.jpg">序号</td>
                                <td align="center" width="6%" height=22 background="../images/tablehead.jpg">司机姓名</td>
                                <td align="center" width="6%" height=22 background="../images/tablehead.jpg">司机电话</td>
                                <td align="center" width="5%" height=22 background="../images/tablehead.jpg">司机性别</td>
                                <td align="center" width="6%" height=22 background="../images/tablehead.jpg">司机住址</td>
                                <td align="center" width="6%" height=22 background="../images/tablehead.jpg">注册时间</td>
                            </tr>

                            <s:iterator value="#session.pagelist" id="driver">
                            <tr onMouseOver="this.style.backgroundColor = 'white'" onMouseOut="this.style.backgroundColor = '#F5FAFE';">
                                <td align="center" width="3%"><s:property value="#driver.id"/></td>
                                <td align="center" width="6%"><s:property value="#driver.drivername"/></td>
                                <td align="center" width="6%"><s:property value="#driver.iphone"/></td>
                                <td align="center" width="5%"><s:property value="#driver.drivermale"/></td>
                                <td align="center" width="6%"><s:property value="#driver.driveraddress"/></td>
                                <td align="center" width="6%"><s:date name="#driver.regtime" format="yyyy-MM-dd HH:mm"/></td>
                            </tr>
                           </s:iterator>
                        </table>
                        </td>
                </tr>
                     </table>
            </form>

            <div>
            <s:include value="paging.jsp">
            <s:param name="table">driver</s:param>
            </s:include>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
</body>
</html>
