<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
  	<link rel="stylesheet" type="text/css" href="../css/salaryStyle.css.css?v=SVN_REVISION">

    <style>
        .hd{display: none}
			 #query{
	 height:100px;
	 border-bottom: 1px solid #ccc;
	 }
	 #queMem,#queDepart{
	 height:50px;
	 line-height: 50px;
	 }
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        /*
         $("#timeM,#timeD").change(function(){
         if($(this).is("#timeM"))
         {
         $("#ms").replaceWith("<input id='ms' style='width:100px;' type='text' name='end'/>");
         $("#me").replaceWith("<input id='me' style='width:100px;' type='text' name='end'/>");
         }
         else
         {
         $("#ds").replaceWith("<input id='ds' style='width:100px;' type='text' name='end'/>");
         $("#de").replaceWith("<input id='de' style='width:100px;' type='text' name='end'/>");
         }

         if($(this).val()=='日')
         {
         $(this).nextAll("input").attr("onclick","WdatePicker({dateFmt:'yyyy-MM-dd'})");
         }
         else if($(this).val()=='月')
         {
         $(this).nextAll("input").attr("onclick","WdatePicker({dateFmt:'yyyy-MM'})");
         }
         else if($(this).val()=='年')
         {
         $(this).nextAll("input").attr("onclick","WdatePicker({dateFmt:'yyyy'})");
         }
         });
         */
        //按人员查询
        $("#qm").click(function(){
            if($("#ms,#me").val()=='')
            {
                alert("请正确填写时间范围");
                return false;
            }
            operateStyle = $("#mstyle").val();
            $.getJSON("../role/salary_queryByMem.action",{"superid":"${request.superid}","mem":$("#mem").val(),"tstyle":$("#timeM").val(),"start":$("#ms").val(),"end":$("#me").val(),"style":$("#mstyle").val()},
                    function(data){

                        var sum = data.sum;
                        $("#sum").html(sum);
                        $("#con table").remove();
                        // 	var pricesType = $("#mstyle").val()=="收益" ? "item.interest" : "item.price";
                        var table = "<table border='0' cellpadding='0' cellspacing='1' width='100%' align='center' style='background-color: #999;'>" +
                                "<tr bgcolor='#FFF'>" +
                                "<th scope='col'>手机号</th>" +
                                "<th scope='col'>姓名</th>" +
                                "<th scope='col'>类型</th>" +
                                "<th scope='col'>金额</th>" +
                                "<th scope='col'>时间</th>" +
                                "</tr>";
                        var list = data.table;
                        $.each(list,function(index,item){
                            var tr = "<tr bgcolor='#FFF'>"+"<td>" + item.username + "</td>" +
                                    "<td>" + item.realname + "</td>" +
                                    "<td>" + $("#mstyle").val() + "</td>" +
                                    "<td>" + item.price + "</td>" +
                                    "<td>" + item.addtime + "</td></tr>";
                            table += tr;
                        });
                        table += "</table>";
                        $("#con").append(table);

                    });

        });
        //按部门查询
        $("#qd").click(function(){
            if($("#ds,#de").val()=='')
            {
                alert("请正确填写时间范围");
                return false;
            }
            operateStyle = $("#dstyle").val();
            $.getJSON("../role/salary_queryByDepart.action",{"superid":"${request.superid}","depart":$("#depart").val(),"tstyle":$("#timeD").val(),"start":$("#ds").val(),"end":$("#de").val(),"style":$("#dstyle").val()},
                    function(data){

                        var sum = data.sum;
                        $("#sum").html(sum);
                        $("#con table").remove();
                        // 	var pricesType = $("#dstyle").val()=="收益" ? "item.interest" : "item.price";
                        var table = "<table border='0' cellpadding='0' cellspacing='1' width='100%' align='center' style='background-color: #999;'>" +
                                "<tr bgcolor='#FFF'>" +
                                "<th scope='col'>部门</th>" +
                                "<th scope='col'>类型</th>" +
                                "<th scope='col'>金额</th>" +
                                "<th scope='col'>时间</th>" +
                                "</tr>";
                        var list = data.table;
                        $.each(list,function(index,item){
                            var tr = "<tr bgcolor='#FFF'>" +
                                    "<td>" + item.depart + "</td>" +
                                    "<td>" + $("#dstyle").val() + "</td>" +
                                    "<td>" + item.price + "</td>" +
                                    "<td>" + item.addtime + "</td></tr>";
                            table += tr;
                        });
                        table += "</table>";
                        $("#con").append(table);

                    });
        });

        $("#export").click(function(){
            location.href="../role/salary_exportQuery.action?style=" + operateStyle;
        });
    });
    var operateStyle;
</script>
</body>
</html>
