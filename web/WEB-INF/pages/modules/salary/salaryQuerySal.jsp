<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
	<style>
        .hd{display: none}
		 #query{
	 height:100px;
	 border-bottom: 1px solid #ccc;
	 }
	 #queMem,#queDepart{
	 height:50px;
	 line-height: 100px;
	 }
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>工资管理</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="ty-mainData">
                    <div id="query">
                        <div id="queDepart">
                            <div style="text-align:center;">
                                部门 <select style="width:100px;" id="depart"><option value="all">全部</option>
                                <s:iterator value="#request.listDepart" id="depart">
                                    <option><s:property value="#depart.depart"/></option>
                                </s:iterator>
                            </select>

                                人员 <select style="width:100px;" id="mem"><option value="all">全部</option>
                                <s:iterator value="#request.listUser" id="user">
                                    <option style="display:none;" value="<s:property value='#user.username'/>" depart="<s:property value='#user.depart'/>"><s:property value="#user.realname"/></option>
                                </s:iterator>
                            </select>
                                开始时间 <input style="width:100px;height: 30px;" id="ds" type="text" name="start" onclick="WdatePicker({dateFmt:'yyyy-MM'})" readonly="readonly"/>
                                结束时间 <input style="width:100px;height: 30px;" id="de" type="text" name="end" onclick="WdatePicker({dateFmt:'yyyy-MM'})" readonly="readonly"/>
                                <span id="qd" class="ty-btn ty-btn-blue ty-btn-big ty-circle-5">查询</span>
                            </div>
                        </div>
                    </div>

                    <div id="export" style="text-align: center;border-bottom: 1px solid #ccc;height:80px;">

                        <div style="color:#f20;font-size:22px;height:79px;padding-top: 25px;">
                            <span>合计:<span id="sum"></span></span> <button id="exportDepart" style="height:30px;margin-left: 53px;margin-right: 53px;">按部门导出</button> <button id="exportMem" style="height:30px;">按人员导出</button>
                        </div>

                    </div>

                    <div id="result">
                        <div id="conDepart" style="float:left;width:48%;margin-top:18px;">

                        </div>
                        <div id="conMem" style="float:right;width:48%;margin-top:18px;">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript" src="/My97DatePicker/WdatePicker.js?v=SVN_REVISION"></script>
<script type="text/javascript">
    $(function(){

        $("#exportDepart,#exportMem").hide();

        $("#depart").change(function(){
            $("#mem").children().show();
            var depart = $(this).val();
            if(depart != 'all')
                $("#mem").children("[depart!=" + depart + "]:not(':first')").hide();
            else
            {
                $("#mem").children(":not(':first')").hide();
            }
        });

        //按部门查询
        $("#qd").click(function(){
            if($("#ds").val()=='' || $("#de").val()=='')
            {
                alert("请正确填写时间范围");
                return false;
            }
            operateStyle = $("#dstyle").val();
            $.getJSON("../role/salary_salaryExpertQuery.action",{"superid":"${session.superid}","depart":$("#depart").val(),"mem":$("#mem").val(),"start":$("#ds").val(),"end":$("#de").val()},
                    function(data){

                        $("#conDepart table").remove();
                        $("#conMem table").remove();
                        //部门结果
                        var tableDepart = "<table border='0' cellpadding='0' cellspacing='1' width='100%' align='center' style='background-color: #999;'>" +
                                "<tr bgcolor='#FFF'>" +
                                "<th scope='col'>部门</th>" +
                                "<th scope='col'>人数</th>" +
                                "<th scope='col'>时间</th>" +
                                "<th scope='col'>工资总额</th>" +
                                "</tr>";
                        //人员结果
                        var tableMem = "<table border='0' cellpadding='0' cellspacing='1' width='100%' align='center' style='background-color: #999;'>" +
                                "<tr bgcolor='#FFF'>" +
                                "<th scope='col'>姓名</th>" +
                                "<th scope='col'>部门</th>" +
                                "<th scope='col'>时间</th>" +
                                "<th scope='col'>工资数</th>" +
                                "</tr>";

                        var listDepart = data.depart;
                        var listMem = data.mem;
                        //循环人员信息生成人员表
                        $.each(listMem,function(index,item){
                            var tr = "<tr bgcolor='#FFF'>" +
                                    "<td>" + item.realname + "</td>" +
                                    "<td>" + item.depart + "</td>" +
                                    "<td>" + item.year+'-'+item.month + "</td>" +
                                    "<td>" + item.salary + "</td></tr>";
                            tableMem += tr;
                        });
                        tableMem += "</table>";
                        //   alert(tableMem);
                        $("#conMem").append(tableMem);
                        //循环部门信息生成部门表
                        $.each(listDepart,function(index,item){
                            var tr = "<tr bgcolor='#FFF'>" +
                                    "<td>" + item.depart + "</td>" +
                                    "<td>" + item.memnum + "</td>" +
                                    "<td>" + item.year+'-'+item.month + "</td>" +
                                    "<td>" + item.salarySum + "</td></tr>";
                            tableDepart += tr;
                        });
                        tableDepart += "</table>";
                        $("#conDepart").append(tableDepart);
                        var sum = 0;
                        $("#conDepart table tr:gt(0)").each(function(){
                            sum += parseFloat($(this).find("td:last").html());
                        });
                        $("#sum").html(sum.toFixed(2));
                        if(sum > 0)
                        {
                            $("#exportDepart,#exportMem").show(100);
                            listD = listDepart;
                            listM = listMem;
                          //  alert(listMem.toJSONString());
                        }
                    });
        });

        $("#exportDepart").click(function(){
            location.href="../role/salary_exportSalary.action?style=depart&sum=" +  $("#sum").html();
        });

        $("#exportMem").click(function(){
            location.href="../role/salary_exportSalary.action?style=mem&sum=" +  $("#sum").html();
        });
    });
    var operateStyle;
    var listD;
    var listM;
</script>
</body>
</html>
