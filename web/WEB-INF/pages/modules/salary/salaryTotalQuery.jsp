<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
   	<link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <link rel="stylesheet" href="../css/typicons.min.css?v=SVN_REVISION">
    <link rel="stylesheet" type="text/css" href="../css/dateL.css?v=SVN_REVISION">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">

    <style>
        .hd{display: none}

		body{
		font-size: 14px;
		}

		.query_li{
			height: 80px;
			border: 1px solid #cccccc;
			border-radius: 5px;
			float: left;
			width: 30%;
			margin-right: 20px;
			padding: 15px;
			cursor: pointer;
		}

		#cur_li:hover{border: 2px solid #2AB4C0;}
		#prev_li:hover{border: 2px solid #F36A5A;}
		#total_li:hover{border: 2px solid #5C9BD1;}

		.span_detail{
			color: #5C9BD1;
			cursor: pointer;
		}

		.detail{
			border: 1px solid #ccc;
			color: #666;
			font-size: 12px;
			padding: 3px 13px;
		}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
	<div class="ty-header">
		<ul>
			<li class="active">
				<span>期末账户资金详情</span>
			</li>
		</ul>
		<div></div>
	</div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<div class="ty-container">
				<div class="ty-mainData">
					<div>
						<div style="height: 38px;float: left">
							<div style="color: #5C9BD1;float: left;line-height: 3;">${start}~${end} 账户清单</div>
							<%--<div class='dateL' style="float: right;margin-right: 50px;"></div>--%>
						</div>
						<div style="float: right">

							<span id="export" class="ty-btn ty-btn-green ty-btn-big ty-circle-5"><span class="typcn typcn-printer"></span>导出</span>
							<span id="return" class="ty-btn ty-btn-big ty-circle-5"><span class="typcn typcn-arrow-back-outline"></span>返回</span>

						</div>
						<div class='dateL' style="float: right;margin-right: 50px"></div>

						<hr style="margin-top:50px">

						<ul id="myTab" class="nav nav-tabs" style="margin:50px 0 0 0; ">
							<li class="active"><a id="a_start_account">期初账户总数<br><span><s:property value="#request.listStartTerm.size()"/></span></a></li>
							<li><a id="a_cur_account">本期开户总数<br><span><s:property value="#request.listCurTerm.size"/></span></a></li>
							<li><a id="a_cur_shut">本期关闭总数<br><span><s:property value="#request.listShutAccount.size"/></span></a></li>
							<li><a id="a_end_account">期末账户总数<br><span><s:property value="#request.listLastTerm.size"/></span></a></li>
						</ul>
						<div id="myTabContent" class="tab-content">
							<div class="tab-pane fade " id="start_account">
								<p>
								<table class="ty-table ty-table-control">
									<thead>
									<tr>
										<td align="center" >员工编号</td>
										<td align="center" >姓名</td>
										<td align="center" >账户余额</td>
										<td align="center" >开户时间</td>
										<td align="center" >部门</td>
										<td align="center" >职位</td>
										<td align="center" >操作详情</td>
										<td align="center" >收益详情</td>
									</tr>
									</thead>
									<s:iterator value="#request.listStartTerm" id="user">
										<tr>
											<td align="center" >${empid}</td>
											<td align="center" >${realname}</td>
											<td align="center" >${account}</td>
											<s:if test="#request.isDisplayRegTime">
												<td align="center"><s:date name="#user.regtime"
																		   format="yyyy-MM-dd HH:mm:ss"/></td>
											</s:if>
											<s:else>
												<td align="center">— —</td>
											</s:else>
											<td align="center" >${depart}</td>
											<td align="center" >${duty}</td>
											<td align="center" class="ty-td-control">
												<a class="ty-color-blue"
												   href="../salaryPC/salaryPC_getUserOperate.action?superid=${session.superid}&realname=${realname}&username=${username}">查看</a>
											</td>
											<td align="center">
												<a class="ty-color-blue"
												   href="../salaryPC/salaryPC_interestHistory.action?superid=${session.superid}&realname=${realname}&username=${username}&type=cur">查看</a>
											</td>
										</tr>
									</s:iterator>


							</table>
								</p>
							</div>
							<div class="tab-pane fade" id="cur_account">
								<p>
								<table class="ty-table ty-table-control">
									<thead>
									<tr>
										<td align="center" >员工编号</td>
										<td align="center" >姓名</td>
										<td align="center" >账户余额</td>
										<td align="center" >开户时间</td>
										<td align="center" >部门</td>
										<td align="center" >职位</td>
										<td align="center" >操作详情</td>
										<td align="center" >收益详情</td>
									</tr>
									</thead>
									<s:iterator value="#request.listCurTerm" id="user">
										<tr>
											<td align="center" >${empid}</td>
											<td align="center" >${realname}</td>
											<td align="center" >${account}</td>
											<s:if test="#request.isDisplayRegTime">
												<td align="center"><s:date name="#user.regtime"
																		   format="yyyy-MM-dd HH:mm:ss"/></td>
											</s:if>
											<s:else>
												<td align="center">— —</td>
											</s:else>
											<td align="center" >${depart}</td>
											<td align="center" >${duty}</td>
                                            <td align="center" class="ty-td-control"><a class="ty-color-blue"
                                                                                        href="../salaryPC/salaryPC_getUserOperate.action?superid=${session.superid}&realname=${realname}&username=${username}">查看</a>
                                            <td align="center"><a class="ty-color-blue"
                                                                  href="../salaryPC/salaryPC_interestHistory.action?superid=${session.superid}&realname=${realname}&username=${username}&type=cur">查看</a>
										</tr>
									</s:iterator>
								</table>
								</p>
							</div>
							<div class="tab-pane fade" id="cur_shut">
								<p>
								<table class="ty-table ty-table-control">
									<thead>
									<tr>
										<td align="center" >员工编号</td>
										<td align="center" >姓名</td>
										<td align="center" >账户余额</td>
										<td align="center" >开户时间</td>
										<td align="center" >部门</td>
										<td align="center" >职位</td>
										<td align="center" >操作详情</td>
										<td align="center" >收益详情</td>
									</tr>
									</thead>
									<s:iterator value="#request.listShutAccount" id="user">
										<tr>
											<td align="center" >${empid}</td>
											<td align="center" >${realname}</td>
                                            <td align="center">${account}</td>
											<s:if test="#request.isDisplayRegTime">
                                                <td align="center"><s:date name="#user.shuttime"
                                                                           format="yyyy-MM-dd HH:mm:ss"/></td>
											</s:if>
											<s:else>
												<td align="center">— —</td>
											</s:else>

                                            <td align="center" >${depart}</td>
											<td align="center" >${duty}</td>
                                            <td align="center" class="ty-td-control"><a class="ty-color-blue"
                                                                                        href="../salaryPC/salaryPC_getUserOperate.action?superid=${session.superid}&realname=${realname}&username=${username}">查看</a>
                                            <td align="center"><a class="ty-color-blue"
                                                                  href="../salaryPC/salaryPC_interestHistory.action?superid=${session.superid}&realname=${realname}&username=${username}&type=cur">查看</a>
										</tr>
									</s:iterator>
								</table>
								</p>
							</div>
							<div class="tab-pane fade in active" id="end_account">
								<p>
								<table class="ty-table ty-table-control">
									<thead>
									<tr>
										<td align="center" >员工编号</td>
										<td align="center" >姓名</td>
										<td align="center" >账户余额</td>
										<td align="center" >开户时间</td>
										<td align="center" >部门</td>
										<td align="center" >职位</td>
										<td align="center" >操作详情</td>
										<td align="center" >收益详情</td>
									</tr>
									</thead>
									<s:iterator value="#request.listLastTerm" id="user">
										<tr>
											<td align="center" >${empid}</td>
											<td align="center" >${realname}</td>
											<td align="center" >${account}</td>
											<s:if test="#request.isDisplayRegTime">
												<td align="center"><s:date name="#user.regtime"
																		   format="yyyy-MM-dd HH:mm:ss"/></td>
											</s:if>
											<s:else>
												<td align="center">— —</td>
											</s:else>
											<td align="center" >${depart}</td>
											<td align="center" >${duty}</td>
                                            <td align="center" class="ty-td-control"><a class="ty-color-blue"
                                                                                        href="../salaryPC/salaryPC_getUserOperate.action?superid=${session.superid}&realname=${realname}&username=${username}">查看</a>
                                            <td align="center"><a class="ty-color-blue"
                                                                  href="../salaryPC/salaryPC_interestHistory.action?superid=${session.superid}&realname=${realname}&username=${username}&type=cur">查看</a>
										</tr>
									</s:iterator>
								</table>
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
	  <script type="text/javascript" src="../script/dateL.js?v=SVN_REVISION"></script>
<script type="text/javascript">
	$(function(){
		var exportstyle = "curTerm";
		$("#return").click(function(){
			 history.go(-1);
		});

		$("#export").click(function(){
			location.href="../role/salary_exportTotalQuery.action?superid=${session.superid}&style=" + exportstyle + "&start=${start}&end=${end}";
		});
		<%--alert("${cur_first} + ${cur}");--%>
		$("#a_start_account").click(function(){
			$(this).parent().addClass("active").siblings().removeClass("active");
			$("#start_account").show().addClass("in active");;
			$("#cur_account").hide();
			$("#cur_shut").hide();
			$("#end_account").hide();
			exportstyle = "startTerm";
		});
		$("#a_cur_account").click(function(){
			$(this).parent().addClass("active").siblings().removeClass("active");
			$("#start_account").hide();
			$("#cur_account").show().addClass("in active");
			$("#cur_shut").hide();
			$("#end_account").hide();
			exportstyle = "curTerm";
		});
		$("#a_cur_shut").click(function(){
			$(this).parent().addClass("active").siblings().removeClass("active");
			$("#start_account").hide();
			$("#cur_account").hide();
			$("#cur_shut").show().addClass("in active");
			$("#end_account").hide();
			exportstyle = "curShut";
		});
		$("#a_end_account").click(function(){
			$(this).parent().addClass("active").siblings().removeClass("active");
			$("#start_account").hide();
			$("#cur_account").hide();
			$("#end_account").show().addClass("in active");
			$("#cur_shut").hide();
			exportstyle = "endTerm";
		});
//		 $("#a_start_account").click();
		$("#a_end_account").click();

		var first = "${start}";
		var last = "${end}";
		var first_array = first.split("-");
		var last_array = last.split("-");
		DateLI("searchfun", 2010,2030,true,false,{start_y:parseInt(first_array[0]),start_m:parseInt(first_array[1]),start_d:parseInt(first_array[2])},{end_y:parseInt(last_array[0]),end_m:parseInt(last_array[1]),end_d:parseInt(last_array[2])});

	});

	function searchfun(){
		// GET EVERY INPUT'S VALUE
		var start_y = $(".start_y").val();
		var end_y = $(".end_y").val();
		var start_m = $(".start_m").val();
		var end_m = $(".end_m").val();
		var start_d = $(".start_d").val();
		var end_d = $(".end_d").val();
		if(start_m < 10)
			start_m = "0" + start_m;
		if(end_m < 10)
			end_m = "0" + end_m;
		if(start_d < 10)
			start_d = "0" + start_d;
		if(end_d < 10)
			end_d = "0" + end_d;
		var firstdate = start_y+"-"+start_m+"-"+start_d;
		var lastdate = end_y+"-"+end_m+"-"+end_d;
        location.href = "../salaryPC/salaryPC_totalQuery.action?superid=${session.superid}&start=" + firstdate + "&end=" + lastdate;
	}

</script>
</body>
</html>
