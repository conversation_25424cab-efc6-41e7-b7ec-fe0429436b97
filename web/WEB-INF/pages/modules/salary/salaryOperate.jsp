
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.min.css?v=SVN_REVISION">
    <style>
    .hd{display: none}
    body{ font-size: 14px; 	}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			 <div style="height: 46px;margin-left: auto;margin-right: auto;width: 90%;">
				 <div style="width: 300px;color: #666666;display: inline-block;*display:inline;*zoom:1;font-size: 28px;width: 500px;">
					 薪资宝全体用户操作记录<s:property value="#request.realname"/></div>
					<%--<div id="reb" style="width: 50px;--%>
						<%--border: 1px solid #333333;--%>
						<%--cursor: pointer;--%>
						<%--float: right;--%>
						<%--height: 28px;--%>
						<%--line-height: 2;--%>
						<%--text-align: center;--%>
						<%--width: 56px;--%>
						<%--margin-right: 28px;--%>
					<%--">返回</div>--%>
			 </div>

			<table class="ty-table ty-table-control">
				<thead>
				<tr>
					<td>当月转入</td>
					<td >当月转出</td>
				</tr>
				</thead>
				<tr>
					<td align="center" width="3%"  height=22>
						<a href="../role/salary_record.action?superid=${session.superid}&optype='转入'" style="color:red">${requestScope.in}条记录</a>
					</td>

					<td align="center" width="3%"  height=22>
						<a href="../role/salary_record.action?superid=${session.superid}&optype='转出'" style="color:red">${requestScope.out}条记录</a>
					</td>
				</tr>
			</table>
			<table id="con" class="ty-table ty-table-control">
				<thead>
				<tr>
					<td align="center" width="3%">提交人</td>
					<td align="center" width="3%">提交申请时间</td>
					<td align="center" width="6%">操作内容</td>
					<td align="center" width="6%">金额</td>
					<td align="center" width="5%">余额</td>
					<td align="center" width="3%">完成时间</td>
				</tr>
				</thead>

                <c:forEach items="${requestScope.salaryOperates}" var="o">
					<tr>
						<td align="center" width="3%" height="30px;">${o.realname}</td>
						<td align="center" width="3%" height="30px;">${o.addtime}</td>
						<td align="center" width="6%" height="30px;">${o.optype}</td>

						<td align="center" width="6%" height="30px;">${o.price}</td>
						<td align="center" width="5%" height="30px;">${o.account}</td>
						<td><fmt:formatDate pattern="yyyy-MM-dd HH:mm:ss" value="${o.okTime}" type="both"/></td>
						 <%-- <s:if test="#ope.optype=='转出'"> --%>
						<%-- </s:if> --%>
						<%-- <s:else>
							<td align="center" width="6%" height="30px;">----</td>
						</s:else> --%>
					</tr>
				</c:forEach>
				
			</table>

    <div id = "paging">
	<div id="prepage">上一页</div>
	<div id="nextpage">下一页</div>
	<div id="firstpage">首页</div>
	<div id="endpage">尾页</div>
	<div id="len"></div>
	<div id="cur"></div>
</div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        if($("#con tr").length > 1)
        {
            var len = "${request.size}";
            window.len = len;
            if(len > 0)
            {
                $.getScript("../script/paging.js");
            }
        }

        $("#reb").click(function(){
            //	location.href = "${pageContext.request.contextPath}/role/salary_list.action?superid=${session.superid}";
            history.go(-1);
        });
    });
</script>
</body>
</html>
