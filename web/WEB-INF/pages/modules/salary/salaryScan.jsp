<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<%@ include  file="../../common/headerBottom.jsp"%>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white" >
<div class="bounce">
    <%-- 一级 tip 提示框  --%>
    <div class="bonceContainer bounce-blue" id="Tip">
        <div class="bonceHead">
            <span>提示信息</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" style="text-align: center">
            <div id="tipMs">    </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn  ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">确认</span>
        </div>
    </div>
    <%--修改后果查看--%>
    <div class="bonceContainer bounce-blue" id="upInfo">
        <div class="bonceHead">
            <span>修改后果查看</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="upInfo_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>工资查看</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <span class="hd  ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="yearBtn" style="float: right;">返回</span>
                <span class="hd ty-btn ty-btn-blue ty-btn-big ty-circle-3" id="monthBtn" style="float: right;">返回</span>
                <ul class="ty-secondTab">
                    <li class="ty-active" id="nav2">年度工资报表</li>
                </ul>
                <div class="ty-mainData"  >
                    <%-- 年列表 --%>
                    <table class="ty-table ty-table-control " id="yearCon">
                        <thead>
                        <tr>
                            <td> 年份 </td>
                            <td>所发工资总额</td>
                            <td>每月职工平均人数</td>
                            <td>人均月工资</td>
                            <td> 操作 </td>
                        </tr>
                        </thead>
                        <tbody id="year">
                            <tr><td>2017 </td><td><span class='ty-color-blue' onclick='getDetails($(this))'>查看</span></td></tr>
                        </tbody>
                    </table>
                    <%-- 月列表 --%>
                    <table class="ty-table ty-table-control hd" id="monCon1">
                        <thead>
                        <tr>
                            <td>月份 </td>
                            <td>所发工资总额</td>
                            <td>职工人数</td>
                            <td>人均工资</td>
                            <td> 操作 </td>
                        </tr>
                        </thead>
                        <tbody id="month1">
                        </tbody>
                    </table>
                    <%-- 人员列表 --%>
                    <div id="monCon2" class="hd">
                        <table class="ty-table ty-table-control " >
                            <thead>
                            <tr>
                                <td> 说明 </td>
                                <td> 人员编号 </td>
                                <td> 姓名 </td>
                                <td> 年份 </td>
                                <td> 考勤所属月份 </td>
                                <td> 原工资金额 </td>
                                <td> 改后的工资金额 </td>
                                <td> 原工资录入时间 </td>
                                <td> 录入人 </td>
                                <td> 修改/补录时间 </td>
                                <td> 录入人 </td>
                                <td> 操作 </td>
                            </tr>
                            </thead>
                            <tbody id="month3">
                            </tbody>
                        </table>
                        <ul class="ty-secondTab">
                            <li class="ty-active" id="nav21"></li>
                        </ul>
                        <table class="ty-table ty-table-control " >
                            <thead>
                                <tr>
                                    <td> 人员编号 </td>
                                    <td> 姓名 </td>
                                    <td> 电话 </td>
                                    <td> 部门 </td>
                                    <td> 工资金额 </td>
                                    <td> 录入时间 </td>
                                    <td> 录入人 </td>
                                </tr>
                            </thead>
                            <tbody id="month2">
                                <tr><td>2017 </td><td><span class='ty-color-blue' onclick='getDetails($(this))'>查看</span></td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/salary/salaryScan.js?v=SVN_REVISION" type="text/javascript" ></script>
</body>
</html>