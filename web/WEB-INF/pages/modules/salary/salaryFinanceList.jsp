<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <link href="../assets/layouts/layout/css/custom.min.css?v=SVN_REVISION" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <style>
        .hd{display: none}
        body{
            font-size: 14px;
        }
       /* .item{ padding-left: 115px; }
        .item>span{ position: relative; top:-10px; }*/
        .a_salary{
            font-size: 12px;
            border: 1px solid #cccccc;
            padding: 2px 17px;
            color: #ffffff;
        }
        .a_edit
        {
            background-color: #32c5d3;
        }
        .a_edit:hover{
            background-color: #26a1ab;
            color: #ffffff;
            text-decoration: none;
        }
        .deliver
        {
            border:1px solid #ccc;padding:2px 17px;font-size:12px;color:#00a0e9;background-color: #3499dd;color: #ffffff;
        }
        .deliver:hover{
            background-color:#217ebd;
        }
        .history_salary
        {
            color: #666666;border: 1px solid #cccccc;
            text-decoration: none;
        }
        .history_salary:hover{
            color: #333333;border: 1px solid #333333;
            text-decoration: none;
        }
        .shutdown:hover{
            color: #333333;border: 1px solid #333333;
            text-decoration: none;
        }
        .mar{ padding-left: 50px; }
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <%-- 补录工资 --%>
    <div class="bonceContainer bounce-green" id="setSale">
        <div class="bonceHead">
            <span>补录工资</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="item">
                <span class="mar">请选择年 ：</span>
                <div class="ty-select" onclick="tyToggleSelect($(this) , 0 , event)">
                    <div class="ty-opTTL" >
                        <option readonly id="slctYear"></option>
                        <span class="ty-down"></span>
                    </div>
                    <div class="ty-opItems" id="yearCon"></div>
                </div>

                <span class="mar">请选择月 ：</span>
                <div class="ty-select" onclick="tyToggleSelect($(this) , 0 , event)">
                    <div class="ty-opTTL" >
                        <option readonly id="slctMonth"></option>
                        <span class="ty-down"></span>
                    </div>
                    <div class="ty-opItems" id="monthCon"></div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="submitSetSale()">确定</span>
        </div>
    </div>
    <%--温馨提示--%>
    <div class="bonceContainer bounce-green" id="tip">
        <div class="bonceHead">
            <span>温馨提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="mt_tip_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" onclick="bounce.cancel()">确定</span>
        </div>
    </div>
    <%-- 编辑工资 --%>
    <div class="bonceContainer bounce-green" id="editSalary">
        <div class="bonceHead">
            <span>编辑工资</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon" >
            <form id="conditions" action="../salaryPC/salaryPC_editSalary.action" method="get">
                姓名：<input type="text" name="realname" readonly/><br><br>
                当月工资：<input type="text" id="salary" name="salary" placeholder="0.0" onkeyup="clearNoNum(this)"/><br><br>
                <input type="hidden" name="superid" value="${session.superid}">
                <input type="hidden" name="username" value="">
                <input type="hidden" name="department" value="">
                <input type="hidden" name="curDate" value="${preMonth}">
                <input type="hidden" name="salaryDay" value="${salaryDay}">
                <input type="hidden" name="empid"  >
                <input type="hidden" name="prompt" >
            </form>
        </div>
        <div class="bonceFoot">
            <p id="tipLit" style="text-align:right; color: red;  "></p>
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="subedit">确定</span>
        </div>
    </div>
    <%--编辑工资提示--%>
    <div class="bonceContainer bounce-green" id="confirmEdit">
        <div class="bonceHead">
            <span>确认提示：</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div class="addpayDetails">
                <div class="shu1">
                    <p id="confirmEdit_ms" style="text-align: center; padding:10px 0"> </p>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" id="editSalaryOk">确定</span>
        </div>
    </div>

</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>工资管理</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <div class="ty-container">
                <div>
                    <input type="hidden" id="infoCon" value="员工工资-请录入<span id='preMonth'>${preMonth}</span>考勤月份的工资,该工资应于 <span id='salaryDay'>${salaryDay}</span>发放" />
                    <input type="hidden" id="infosalaryDay" value="${salaryDay}">
                    <div style="color: #5C9BD1;float: left;">
                        <span id="tipInfo"></span>
                        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" onclick="setSale()" style="display:inline-block; margin-left: 50px; ">补录工资</span>
                    </div>
                    <div style="float: right">
                        <form action="../salaryPC/salaryPC_finance.action">
                            按部门 <select class="form-control" name="depart" style="width: 150px;display: inline-block;padding-top: 4px;height: 30px;" id="qdepart">
                            <option value="depart">全部</option>
                            <s:iterator value="#request.listDeparts" id="de">
                                <option value=<s:property value='#de.depart'/>><s:property value='#de.depart'/></option>
                            </s:iterator>
                        </select>
                            按姓名 <select class="form-control" name="uname" style="width: 150px;display: inline-block;padding-top: 4px;height: 30px;"  id="qname">
                            <option value="username">全部</option>
                            <s:iterator value="#request.listNames" id="uname">
                                <option value=<s:property value='#uname.username'/> depart=<s:property value='#uname.depart'/>><s:property value='#uname.realname'/></option>
                            </s:iterator>
                        </select>
                            <input type="hidden" name="curDate" value="${preMonth}">
                            <input type="hidden" name="salaryDay" value="${salaryDay}">
                            <button type="submit" class="ty-btn ty-btn-green ty-btn-big ty-circle-5" style="outline: 0;border:0">查询</button>
                            <%--<span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="expert">高级搜索</span>--%>
                            <input type="hidden" class="btn-success" name="superid" value="${session.superid}">
                        </form>
                    </div>
                </div>
                <div class="ty-mainData">
                    <div>
                        <table id="con" class="ty-table ty-table-control">
                            <thead>
                            <tr>
                                <td align="center">员工编号</td>
                                <td align="center">姓名</td>
                                <td align="center">电话</td>
                                <td align="center">部门</td>
                                <td align="center">编辑工资</td>
                                <td align="center">应发工资</td>
                                <td align="center">录入状态</td>
                                <td align="center">历史工资</td>
                            </tr>
                            </thead>
                            <s:iterator value="#request.listUsers" id="user">
                                <tr>
                                    <td><s:property value="#user.empid"/></td>
                                    <td><s:property value="#user.realname"/></td>
                                    <td><s:property value="#user.username"/></td>
                                    <td><s:property value="#user.depart"/></td>
                                    <td class="ty-td-control">
                                        <a idStr="<s:property value='#user.id'/>"
                                           prompt="<s:property value='#user.prompt'/>"
                                           empid="<s:property value='#user.empid'/>"
                                           name="<s:property value='#user.realname'/>"
                                           department="<s:property value='#user.depart'/>"
                                           salary="<s:property value='#user.realSalary'/>"
                                           username="<s:property value='#user.username'/>"
                                           isShut="<s:property value='#user.isShut'/>"
                                           lowrate="${lowrate}"
                                           highrate="${highrate}"
                                           inType="<s:property value='#user.inType'/>"
                                           class="edit ty-color-blue">编辑 </a>
                                    </td>
                                    <td><s:property value="#user.realSalary"/></td>
                                    <s:if test="#user.deliver==1">
                                        <td>
                                            <span class="colorBlue">录入完毕</span>
                                        </td>
                                    </s:if>
                                    <s:else>
                                        <td>
                                            <span class="colorGray" >尚未录入</span>
                                        </td>
                                    </s:else>
                                    <td align="center" >
                                        <a class="ty-color-blue"
                                           onclick="scanHis({'realname':'<s:property value='#user.realname'/>' , 'username':'<s:property value='#user.username'/>' , 'department':'<s:property value='#user.depart'/>' })"
                                        >查看</a> </td>
                                </tr>
                            </s:iterator>
                        </table>
                        <div id = "paging">
                            <div id="prepage">上一页</div>
                            <div id="nextpage">下一页</div>
                            <div id="firstpage">首页</div>
                            <div id="endpage">尾页</div>
                            <div id="len"></div>
                            <div id="cur"></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>

<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        // 处理未设置工资发放时间
        var infosalaryDay = $("#infosalaryDay").val();
        var strArr = infosalaryDay.split("-");
        var dayStr = strArr[2] ;
        if(dayStr == "0日"){
            $("#tipInfo").html("目前尚未设置工资发放日期，工资无法发放");
        }else{
            $("#tipInfo").html( $("#infoCon").val() );
        }

        $("#qdepart").change(function(){
            $("#qname").children().show();
            var depart = $(this).val();
            if(depart != 'depart')
            //$("#qname").children("[depart!=" + depart + "]").hide();
                $("#qname").children("[depart!=" + depart + "]:not(':first')").hide();
        });
        var len = "${request.size}";
        window.len = len;
        preparePageing();
        // 如果是补录工资， 不显示提示的后半句话
        var type = getUrlParam("type") ;
        if(type == 1){
            var str = $("#infoCon").val();
            $("#infoCon").val(str.split(",")[0])
        }

        // 编辑工资按钮
        $(".edit").click(function(){
            var isShut = $(this).attr("isShut") ;
            if(isShut == 1){
                bounce.show($("#tip")) ; $("#mt_tip_ms").html("账户已关闭，请先开启账户在编辑") ;
                return false ;
            }
            var inType = $(this).attr("inType") ;
            if(inType == 1){
                bounce.show($("#tip")) ; $("#mt_tip_ms").html("工资已发放，请到历史工资中修改") ;
                return false ;
            }
            if($(this).css("color")=="rgb(204, 204, 204)") { return false;  }
            var lowRate = $(this).attr("lowrate") ;
            var highRate = $(this).attr("highrate") ;
            if(Number(lowRate)>0 || Number(highRate)>0){

            }else{
                bounce.show($("#tip")) ; $("#mt_tip_ms").html("请先在“薪资宝-系统设置”中设置收益率") ;
                return false ;
            }
            var name = $(this).attr("name");
            var salary = $(this).attr("salary");
            var username = $(this).attr("username");
            var depart = $(this).attr("department");
            var id = $(this).attr("empid");
            var prompt = $(this).attr("prompt");
            $("#conditions [name='realname']").val(name);
            if(salary > 0) { $("#conditions [name='salary']").val(salary); }
            $("#conditions [name='username']").val(username);
            $("#conditions [name='department']").val(depart);
            $("#conditions [name='empid']").val(id);
            bounce.show( $("#editSalary") );
            $("#salary").val("");
        });
        // 编辑工资保存 -
        $("#subedit").click(function(){
            var salary = $("#salary").val() ;
            if( $.trim(salary) == "" || isNaN( Number(salary) ) ){
                $("#tipLit").html("请输入有效的工资数额");
                return false ;
            }
            bounce.show( $("#confirmEdit") ) ;
            $("#confirmEdit_ms").html("是否确定将工资数调成" + Number( $("#conditions").find("[name='salary']").val() ) + "?");
        });
        // 确定保存 编辑工资
        $("#editSalaryOk").click(function () {
            var type = getUrlParam("type") ;
            if(type == 1){
                $("#conditions").append("<input type='hidden' name='type' value='1'/>") ;
            }
            $("#conditions").submit();
        });

        $("#outCount").click(function(){
            location.href = "../salaryPC/salaryPC_unTurnOut.action?id=0&price=0&realname=0&superid=${session.superid}";
        });

        $("#expert").click(function(){
            location.href = "../salaryPC/salaryPC_salarySearch.action?superid=${session.superid}";
        });
        $(".shutdown").click(function(){
            if(confirm("是否确定将该员工薪资宝关闭？"))
            {
                var shutdown = $(this).parent().parent();
                $.getJSON("../role/salary_stopUse.action",{"superid":"${session.superid}","username":$(this).attr("username"),"pc":"1"},function(data){
                    var jsonbor = data.result;
                    if(jsonbor=='success')
                    {
                        shutdown.remove();
                        len = parseInt(len)-1;
                        if(len==0)
                            $("#paging").hide();
                        window.len = len;
                        preparePageing();
                        alert("关闭成功");
                    }
                    else
                        alert("账户余额不为0时不允许关闭该账户！");
                });
            }

        });
    });
    // 分页插件
    function preparePageing(){
        if(len > 0)
        {
            $.getScript("../script/paging.js",function(){
                if("${request.curpage}">1)
                    gotoPage("${request.curpage}");
            });
        }
    }
    function gotoPage(dirpage) {
        //alert(dirpage);
        curpage = dirpage;
        if(curpage <= pages)
            $("#cur").html(curpage + " / " + pages);
        $("#con tr:gt(0)").hide();
        temp = (curpage-1) * pageno;
        $("#con tr:lt(" + (temp + pageno + 1) + ")").show();
        $("#con tr:lt(" + (temp + 1) + ")").hide();
        $("#con tr:eq(0)").show();
    }
    function showBg(name,salary,username,depart) {
        var bh = window.screen.height;
        var bw = window.screen.width;
        $("#fullbg").css({
            height:bh,
            width:bw,
            display:"block"
        });
        $("#dialog [name='realname']").val(name);
        if(salary > 0)
            $("#dialog [name='salary']").val(salary);
        $("#dialog [name='username']").val(username);
        $("#dialog [name='department']").val(depart);
        $("#dialog").show(200);
    }
    //关闭灰色 jQuery 遮罩
    function clearNoNum(obj){
        obj.value = obj.value.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
        obj.value = obj.value.replace(/^\./g,"");  //验证第一个字符是数字而不是.
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的.
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    }
    // creator : 侯杏哲 2017-10-21 补录工资按钮
    var dateObj = null ;
    function setSale(){
        bounce.show($("#setSale"));
        $.ajax({
            url:"../role/salary_getSupplies.action" ,
            type:"post" ,
            dateType:"json" ,
            beforeSend:function(){ loading.open(); },
            success:function (data) {
                var list = data["listSupplies"] ;
                dateObj = list ;
                var str = "" , yearArr = [] ; // yearArr ： 年的数组
                if(list && list.length > 0){
                    for(var i = 0 ; i < list.length ; i++ ){
                        if(yearArr.indexOf(list[i]["year"])<0){
                            yearArr.push(list[i]["year"]) ;
                            str += "<option class='ty-opItem' value='"+ list[i]["year"] +"' onclick='tySelect($(this) , setYear )'>"+ list[i]["year"] +"</option>" ;
                            if( i == 0 ){
                                $("#slctYear").html( list[i]["year"]).val( list[i]["year"]);
                                $("#slctMonth").html( list[i]["month"]).val( list[i]["month"]);
                            }
                        }
                    }
                    $("#yearCon").html(str);
                    setYear(list[0]["year"]) ;
                }
            },
            error:function () {
                bounce.show($("#tip")) ;  $("#mt_tip_ms").html("链接错误！");
            },
            complete:function () { loading.close(); }
        })
    }
    // creator : 侯杏哲 2017-10-21  选择年的回调， 设置月份
    function setYear(year){
        var list = dateObj , str= "" , isSet = false;
//        var year = obj.val();
        if(list && list.length > 0){
            for(var i = 0 ; i < list.length ; i++ ){
                if(year == list[i]["year"]){
                    str += "<option class='ty-opItem' value='"+ list[i]["month"] +"' onclick='tySelect($(this) , setMon)'>"+ list[i]["month"] +"</option>" ;
                    if( !isSet  ){
                        $("#slctMonth").html( list[i]["month"]).val( list[i]["month"]); isSet = true ;
                    }
                }
            }
        }
        $("#monthCon").html(str);
    }
    // creator : 侯杏哲 2017-10-21  选择月的回调，
    function setMon(obj){
    }
    // creator : 侯杏哲 2017-10-21 确定补录月份
    function submitSetSale(){
        var year = $("#slctYear").val();
        var month = $("#slctMonth").val();
        if(year == "" || month == "0"){
            bounce.show( $("#tip") ) ;  $("#mt_tip_ms").html("请选择补录的年份和月份") ;
            return}
        if(Number(month)<10){ month = "0"+ month ;  }
        var time = year + "-" + month ;
        var preMonth = $("#preMonth").html() ;
        var salaryDay = $("#salaryDay").html() ;
        var dayStr = salaryDay.substr(0,10);
        var day = dayStr.split("-")[2] ;
        if(Number(day)<10){ day = "0"+ day ;  }
        salaryDay = preMonth + "-"+ day ;
        location.href = "../salaryPC/salaryPC_finance.action?type=1&curDate=" + time + "&salaryDay=" + salaryDay;
    }
    // creator:侯杏哲 2017-10-25 查看
    var superid_G = "${session.superid}" ;
    function scanHis(infoObj){
        var info = infoObj ;
        var realname = info["realname"];
        var username = info["username"];
        var department = info["department"];
        var curDate = getUrlParam("curDate");
        var salaryDay = getUrlParam("salaryDay");
        var url = "../salaryPC/salaryPC_salaryHistory.action?superid=" + superid_G + "&realname=" + realname + "&username=" + username;
        if( curDate && salaryDay ){
            url = "../salaryPC/salaryPC_salaryHistory.action?curDate=" + curDate + "&salaryDay=" + salaryDay + "&superid=" + superid_G + "&realname=" + realname + "&username=" + username;
        }
        location.href = url
    }
</script>
</body>
</html>
