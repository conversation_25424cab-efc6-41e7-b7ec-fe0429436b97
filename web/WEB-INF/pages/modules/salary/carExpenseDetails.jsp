<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
   	<link rel="stylesheet" type="text/css" href="../css/carStyle.css?v=SVN_REVISION">
    <style>
        .hd{display: none}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			
 	        <select id="qproject">
 		<option value="project">全部项目</option>
 		<s:iterator value="#session.listProject" id="cp">
 			<option value=<s:property value='#cp.project'/>><s:property value="#cp.project"/></option>
 		</s:iterator>
 	</select>
 	
 	        <select id="qupperson">
 		<option value="upperson">全部上传人</option>
 		<s:iterator value="#session.listupperson" id="person">
 			<option value=<s:property value="#person.upperson"/>><s:property value="#person.upperson"/></option>
 		</s:iterator>
 	</select>
 	        提交时间从<input type="text" onClick="WdatePicker();" id="beginTime">到<input type="text" onClick="WdatePicker();" id="endTime">
 	        <div class="carQuery">查询</div>
        </div>
        <table cellspacing="1" cellpadding="0" width="90%" align="center" bgcolor="#f5fafe" border="0">
	 <tr>
		  <td class="ta_01" align="center" bgcolor="#f5fafe">
			<table cellspacing="0" cellpadding="1" rules="all" bordercolor="gray" border="1" id="DataGrid1" style="BORDER-RIGHT:gray 1px solid; BORDER-TOP:gray 1px solid; BORDER-LEFT:gray 1px solid; WIDTH:100%; WORD-BREAK:break-all; BORDER-BOTTOM:gray 1px solid; BORDER-COLLAPSE:collapse; BACKGROUND-COLOR:#f5fafe; WORD-WRAP:break-word">
				<tr style="FONT-WEIGHT:bold;FONT-SIZE:12pt;HEIGHT:25px;BACKGROUND-COLOR:#afd1f3">
					<td align="center" width="8%" height=22 background="../images/tablehead.jpg">ID</td>
					<td align="center" width="8%" height=22 background="../images/tablehead.jpg">车牌号</td>
					<td align="center" width="8%" height=22 background="../images/tablehead.jpg">项目名称</td>
					<td align="center" width="7%" height=22 background="../images/tablehead.jpg">上传人</td>
					<td align="center" width="8%" height=22 background="../images/tablehead.jpg">提交时间</td>
                    <td align="center" width="8%" height=22 background="../images/tablehead.jpg">当前里程</td>
					<td align="center" width="8%" height=22 background="../images/tablehead.jpg">消费金额</td>
					<td align="center" width="5%" height=22 background="../images/tablehead.jpg">站点名称</td>
					<td align="center" width="5%" height=22 background="../images/tablehead.jpg">备注</td>
					<td align="center" width="8%" height=22 background="../images/tablehead.jpg">状态</td>
					<td align="center" width="8%" height=22 background="../images/tablehead.jpg">报销审核时间</td>
<!-- 					<td width="7%" align="center" height=22 background="images/tablehead.jpg">编辑</td>
					<td width="7%" align="center" height=22 background="images/tablehead.jpg">删除</td> -->
						
	            </tr>

                <s:iterator value="#session.pagelist" id="car">
				<tr onMouseOver="this.style.backgroundColor = 'white'" onMouseOut="this.style.backgroundColor = '#F5FAFE';">
					<td align="center" width="5%"><s:property value="#car.id"/></td>
                    <td align="center" width="5%"><s:property value="#car.platenumber"/></td>
					<td align="center" width="8%"><s:property value="#car.project"/></td>
					<td align="center" width="11%"><s:property value="#car.upperson"/></td>
					<td align="center" width="10%"><s:date name="#car.addtime" format="yyyy-MM-dd HH:mm"/></td>
					<td align="center" width="8%" style="HEIGHT: 22px"><s:property value="#car.carmileage"/></td>
                    <td align="center" width="8%"><s:property value="#car.price"/></td>
					<td align="center" width="5%"><s:property value="#car.stationname"/></td>
					<td align="center" width="5%"><s:property value='#car.remark'/></td>
					<td align="center" width="10%"><s:property value='#car.statue'/></td>
					<td align="center" width="10%" class="cl">
						<s:set var="check" value="#car.checktime"/>
					    <s:if test="#check < #car.addtime">
					    	<span class="confirm">确认审核</span>
					    </s:if>
					    <s:else>
					    	<s:date name="#car.checktime" format="yyyy-MM-dd HH:mm"/>
					    </s:else>
					</td>


				</tr>
               </s:iterator>


			
				
			</table>
			</td>
		</tr>

         </table>  
         
        <div>
        <s:include value="paging.jsp">
        <s:param name="table">carevent</s:param>
        </s:include>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>

</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
 <script type="text/javascript">
    $(function(){

        $(".confirm").click(function(){
            var id = $(this).parent().parent().children(":first").html();
            var temp = $(this);
            $.getJSON("car_confirm.action",{ "id" : id},function(data){
                var checktime = data.checktime;
                temp.removeClass("confirm");
                temp.html(checktime.substr(0,16));
            });
        });

        $(".carQuery").click(function(){
            var project = $("#qproject").val();
            var person = $("#qupperson").val();
            var beginTime = $("#beginTime").val();
            var endTime = $("#endTime").val();
            //	alert(beginTime + " -- " + endTime);
            location.href = "car_carExpenseDetails.action?platenumber=" + "${session.platenumber}" + "&project=" + project + "&person=" + person + "&beginTime=" + beginTime + "&endTime=" + endTime;
            //	alert($("tr:eq(2) td:eq(1)").html());
        });

    });
</script>
</body>
</html>
