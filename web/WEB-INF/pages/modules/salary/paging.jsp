<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>">
    
    <title>My JSP 'paging.jsp' starting page</title>
    
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">    
	<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
	<meta http-equiv="description" content="This is my page">
	<!--
	<link rel="stylesheet" type="text/css" href="styles.css">
	-->
<script src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>
<script type="text/javascript">
 $(function(){
	var table = "${param.table}";
	var pageno = "${session.pageno}";
	$("#prepage").click(function(){
		var cur = $("#curpage").html(); 
		if(parseInt(cur) > 1)
		{
			$("#curpage").html(parseInt(cur) - 1);
			location.href = "../role/car_paging.action?page=" + $("#curpage").html() +
			"&pageno=" + pageno + "&table=" + table;
		}
	});
	
	$("#nextpage").click(function(){
		var cur = parseInt($("#curpage").html());
		if(cur < parseInt($("#maxpage").html()))
		{
			$("#curpage").html(cur + 1);
			location.href = "../role/car_paging.action?page=" + $("#curpage").html() +
			"&pageno=" + pageno + "&table=" + table;
		} 
	});
	
	$("#firstpage").click(function(){
		 $("#curpage").html("1");
		 location.href = "../role/car_paging.action?page=" + $("#curpage").html() +
			"&pageno=" + pageno + "&table=" + table;
	});
	
	$("#endpage").click(function(){
		$("#curpage").html($("#maxpage").html());
		location.href = "../role/car_paging.action?page=" + $("#curpage").html() +
			"&pageno=" + pageno + "&table=" + table;
	});
});
    
    </script>
    
<style type="text/css">
    body{text-align: center;}
    #paging{
    margin-left: auto;
    margin-right: auto;
    margin-top: 30px;
    text-align: center;
    width: 500px;
    }
    
    #paging div{
    border: 1px solid #cccccc;
    cursor: pointer;
    font-size: 12px;
    margin-right: 13px;
    padding: 5px;
    width:50px;
    display: inline-block;
    *display:inline;
	*zoom:1;
    }
    </style>
  </head>
  
  <body>
  
<div id = "paging">

  	<s:set name="maxcount" value="#session.count"></s:set>
  	<s:set name="curpage" value="#session.page"></s:set>
  	<s:set name="pageno" value="#session.pageno"/>
  	<s:if test="#maxcount % #pageno == 0">
  		<s:set name="maxpages" value="#maxcount / #pageno"/>
  	</s:if>
  	<s:else>
  		<s:set name="maxpages" value="#maxcount/#pageno + 1" />
  	</s:else>
	<div id="prepage">上一页</div>
	<div id="nextpage">下一页</div>
	<div id="firstpage">首页</div>
	<div id="endpage">尾页</div>
	<div>共<s:property value="#maxcount"/>条</div>
	<div>
	<span id="curpage">
	<s:property value="#curpage"/>
	</span>
	/
	<span id="maxpage">
	<s:property value="#maxpages"/>
	</span>
	</div>
</div>
  </body>
</html>
