<%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2016-06-20
  Time: 10:06
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../../common/headerTop.jsp"%>
	<link rel="stylesheet" type="text/css" href="../bootstrap/bootstrap-theme.min.css?v=SVN_REVISION">
	<style>
        .hd{ display: none;  }
        .title{ font-weight: bold; }
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			
    height: 60px;
    line-height: 4;
    text-align: left;">
  <span style="color: #666666;font-size: larger; margin-right: 20px;">薪资宝</span>
  <span style="color: #333333;font-size: 14px;">使用帮助</span>
</div>

<div style="min-height: 300px; border: 1px solid #cccccc;border-radius: 5px;padding: 15px;">
  <%--<div style="height: 38px;">--%>
    <%--<div style="color: #5C9BD1;float: left;line-height: 3;">${first}~${last} ${title}</div>--%>
    <%--<div style="float: right">--%>
      <%--<button id="export" type="button" class="btn btn-default"><span class="typcn typcn-printer"></span>导出</button>--%>
      <%--<button id="return" type="button" class="btn btn-primary"><span class="typcn typcn-arrow-back-outline"></span>返回</button>--%>
    <%--</div>--%>
  <%--</div>--%>
  <p class="title">登录系统</p>
  <p>所有的工作，都需由管理员在系统登录页面输入用户名与密码，进入系统后才能进行。 超级管理员需要使用自己的常用手机号作为用户名，自己设定密码。</p>
  <p class="title">录入员工信息</p>
  <p>管理员可在"人事管理"下面的"员工信息"中点击"新增人员"，将新员工信息录入到系统；也可以通过点击"导出标准表"，在自己的电脑中把目前在册人员信息输入后，在点击"导入数据"，将员工信息直接导入到系统。这样可减少系统的输入工作量。 注意：所导出的标准表，不得增加或减少列，否则输入后无法导入。</p>
  <p class="title">系统设置</p>
  <p>3.1 输入工资发放时间。</p>
  <p>3.2 确认年化收益率，并输入。</p>
  <p>3.3 根据管理员喜好，确定用户转入时为默认全部转入还是默认转入0。
    系统推荐的是默认转入全部。</p>
  <p>4 使用通知</p>

  <p>开始使用薪资宝时，建议采用通知的形式向员工公告。<a href="../role/menuAction_downHelpExcel.action?type=3" class="blue">《关于本公司开始使用薪资宝的通知》</a>是一个参考文件，可通过点击上面的蓝色字体，将这个文件导出到自己的电脑中，改改之后应该就能使用。</p>
  <p class="title">员工下载及激活手机端</p>
    <p>员工需要使用智能手机，到管理员处下载本系统，并安装激活。
    管理员需要告知系统所使用的"管理员手机号"。
    具体按<a href="../role/menuAction_downHelpExcel.action?type=1" class="blue">《薪资宝用户使用指南》</a>。</p>
  <p class="title">借款协议</p>
  <p>使用薪资宝，某种程度上相当于向员工借款，故需与员工签订<a href="../role/menuAction_downHelpExcel.action?type=5" class="blue">《借款协议》</a>。这个文件也可以导出。</p>
  <p class="title">输入工资总额与发放</p>
  <p>
    <span style="color: #FF0000">工作人员需在发工资之日前五日内</span>，在"账户列表"中输入已经激活系统的员工当月应发工资。
    员工在手机端收到自己的应发工资总额后，操作转入金额后，工作人员需将差额发给员工，转入金额可由公司支配使用，只是需按日计算约定的年化收益率，以及在员工操作转出时，三日内无条件按其要求转账。</p>
  <p class="title">工资转出</p>
  <p>
    员工每月有三次转出操作权利。
    员工需要钱时，可在手机端输入自己需要的金额，随后可关注自己在公司备案的银行卡收支情况。公司转账应在三日内完成，并发出转账完成消息，转账完成之日起，转出金额不再计算利息。
    使用系统后，<span style="color: #FF0000">负责转出操作的工作人员需要实时登录系统</span>，以便及时收到员工的转出要求。收到转出要求，<span style="color: #FF0000">工作人员需在三日内完成转账，之后需要在系统内点击"完成"，系统方停止计息</span>。</p>
  <p class="title">查询</p>
  <p>
    管理员等有权限者，可随时登录系统，在总查询或用户列表中查看相关信息。</p>
  <p class="title">更改系统设置</p>
  <p>
    公司更改工资发放时间，可在系统设置内完成；
    公司根据需要，要更改年化收益率，并可选择对于已经存在公司的钱款是否有效，均可通过在系统设置内完成。
    <span style="color: #FF0000">年化收益率的更改对已经放在公司内钱款是否有效，建议慎重操作，三思后行；</span>
    公司根据员工反馈，可选择更改员工的转入方式，可将默认全部转入更改为默认不转入。此点也需谨慎操作，建议对系统更加了解之后才行考虑变更更为明智。</p>
  <p class="title">账务处理</p>
  <p>
    薪资宝的账务处理建议以及财务人员的一些注意事项，都在<a href="../role/menuAction_downHelpExcel.action?type=4" class="blue">《关于薪资宝财务操作方面的几点说明》</a>中，如果愿意使用，可在将文件导出后，在自己的电脑中编辑修改。</p>
  <p>13 员工信息有一些编辑、查询、导入与导出功能，比如生成通讯录，或按条件（如入职时间、岗位、部门等）查询，管理员可尽情使用。</p>

</div>
        </div>
    </div>
    <%@ include  file="../../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../../common/footerTop.jsp"%>
<%@ include  file="../../../common/footerScript.jsp"%>

<script type="text/javascript">
    $(function(){
        $(".text-contrast").closest(".accordion-inner").slideToggle();
    });
</script>
</body>
</html>
