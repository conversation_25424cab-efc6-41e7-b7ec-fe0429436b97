<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
    <style>
        .hd{display: none}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			 <div style="margin-left: auto;margin-right: auto; text-align: center;">
    	<form action="../role/salary_turnIN.action">
    		账户余额:<input type="text" name="account" readonly="readonly" id="acc"><br>
    		选择账户：<select name="realname">
    		<!-- <option value="0">真实姓名</option> -->
    		<s:iterator value="#request.list" id="user">
    		<option value="<s:property value='#user.realname'/>" account="<s:property value='#user.account'/>"><s:property value="#user.realname"/></option>
    		</s:iterator>
    		</select><br>
    		操作类型:<select name="optype"><option value="转入">转入</option><option value="转出">转出</option></select><br>
    		金额:<input type="text" name="opprice"><br>
    		<input type="hidden" name="superid" value="${session.superid}"> 
    		<input type="submit" value="保存">
    	</form>
    </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        $("#acc").val($("[name='realname']").find("option:selected").attr("account"));
        //alert($("[name='realname']").find("option:selected").attr("account"));
        $("[name='realname']").change(function(){
            $("#acc").val($(this).find("option:selected").attr("account"));
        });

    });
</script>
</body>
</html>
