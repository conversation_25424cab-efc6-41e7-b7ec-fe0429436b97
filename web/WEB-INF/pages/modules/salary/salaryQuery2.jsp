<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
  	<link rel="stylesheet" type="text/css" href="../css/salaryStyle.css?v=SVN_REVISION">
    <style>
        .hd{display: none}
			body{
		font-size: 14px;
		}

		.query_li{
			height: 80px;
			border: 1px solid #cccccc;
			float: left;
			width: 30%;
			margin-right: 20px;
			padding: 15px;
			cursor: pointer;
		}

		#cur_li:hover{border: 1px solid #2AB4C0;}
		#prev_li:hover{border: 1px solid #F36A5A;}
		#total_li:hover{border: 1px solid #5C9BD1;}

		.span_detail{
			color: #5C9BD1;
			cursor: pointer;
		}
    </style>
</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
	<div class="ty-header">
		<ul>
			<li class="active">
				<span>总查询</span>
			</li>
		</ul>
		<div></div>
	</div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<div class="ty-container">
				<div class="ty-mainData">
					<ul style="list-style: none;height: 80px;padding: 0;">
						<li style="" class="query_li" id="cur_li"><span style="color: #2AB4C0;font-size: 25px;">${monthuser}</span><br><span>本月开户总数</span></li>
						<%--<li style="" class="query_li" id="prev_li"><span style="color: #F36A5A;font-size: 25px;">${premonthuser}</span><br><span>上月开户总数</span></li>--%>
						<li style="" class="query_li" id="total_li"><span style="color: #5C9BD1;font-size: 25px;">${users}</span><br><span>期末账户总数</span></li>

					</ul>

					<div style="height: 38px;">
						<div style="color: #5C9BD1;float: left;line-height: 3;">近期资金情况</div>
						<div style="float: right"><span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" id="query">查询</span></div>
					</div>
					<hr>
					<table id="con" class="ty-table ty-table-control">
						<thead>
						<tr>
							<td align="center" >时间</td>
							<td align="center" >转入总额</td>
							<td align="center" >转出总额</td>
							<td align="center" >平均年化收益率</td>
							<td align="center" >利息支出</td>
							<td align="center" >期末余额</td>
						</tr>
						</thead>

						<tr>
							<td align="center" >${cur_first}~${cur}</td>
							<td align="center" >${inprice} <span class="ty-btn ty-btn-blue"  id="cur_in">查看详情</span></td>
							<td align="center" >${outprice} <span class="ty-btn ty-btn-blue" id="cur_out">查看详情</span></td>
							<td align="center" >${session.avgRate}%</td>
							<td align="center" >${suminterest}</td>
							<td align="center" >${account}</td>
						</tr>

						<tr>
							<td align="center" >${prev_first}~${prev_last}</td>
							<td align="center" >${preinprice} <span class="ty-btn ty-btn-blue" id="prev_in">查看详情</span></td>
							<td align="center" >${preoutprice} <span class="ty-btn ty-btn-blue" id="prev_out">查看详情</span></td>
							<td align="center" >${preavgRate}%</td>
							<td align="center" >${presuminterest}</td>
							<td align="center" >${preaccount}</td>
						</tr>

					</table>
				</div>
			</div>

        </div>
    </div>

    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script type="text/javascript">
    $(function(){
        $("#query").click(function(){
            location.href = "../salaryPC/salaryPC_queryForOne.action?superid=${session.superid}&first=all&last=all";
        });
        $("#expert").click(function(){
            location.href = "../salaryPC/salaryPC_ExpertQuery.action?superid=${session.superid}";
        });

        $("#cur_li").click(function(){
            location.href = "../salaryPC/salaryPC_curQuery.action?superid=${session.superid}&what=本月";
        });

        $("#prev_li").click(function(){
            location.href = "../salaryPC/salaryPC_prevQuery.action?superid=${session.superid}";
        });

        $("#total_li").click(function(){
            location.href = "../salaryPC/salaryPC_totalQuery.action?superid=${session.superid}";
        });

        $("#cur_in").click(function () {
            location.href = "../salaryPC/salaryPC_operateDetail.action?superid=${session.superid}&style=cur_in";
        });
        $("#cur_out").click(function () {
            location.href = "../salaryPC/salaryPC_operateDetail.action?superid=${session.superid}&style=cur_out";
        });
        $("#prev_in").click(function () {
            location.href = "../salaryPC/salaryPC_operateDetail.action?superid=${session.superid}&style=prev_in";
        });
        $("#prev_out").click(function () {
            location.href = "../salaryPC/salaryPC_operateDetail.action?superid=${session.superid}&style=prev_out";
        });

    });
</script>
</body>
</html>
