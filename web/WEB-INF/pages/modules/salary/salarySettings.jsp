<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link rel="stylesheet" type="text/css" href="../css/salary/salarySeting.css?v=SVN_REVISION">

</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<div class="bounce">
    <input type="hidden" id="superid" value="${session.user.superId}">
    <input type="hidden" id="type">
    <input type="hidden" id="type2">
    <input type="hidden" id="oldRate">
    <input type="hidden" id="date">

    <%--设置工资发放日期  --%>
    <div class="bonceContainer bounce-green" id="dateSetting">
        <div class="bonceHead">
            <span id="dateSettingTtl">设置工资发放日期  </span>
            <a class="bounce_close" onclick="bounce.cancel();clearInterval(timer) ; "></a>
        </div>
        <div class="bonceCon">
            <p id="dateTip"></p>
            <p>请在日历中确定每月的工资发放日期</p>
            <p class="textCenter"><input type="text" id="ntime" class="laydate-icon" readonly></p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray"
                  onclick="bounce.cancel();clearInterval(timer) ; ">取消</span>
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" id="dateOk">确定</span>
        </div>
    </div>
    <%--设置年化收益率  --%>
    <div class="bonceContainer bounce-green" id="lvSetting">
        <div class="bonceHead">
            <span id="lvSettingTtl">设置年化收益率</span>
            <a class="bounce_close" onclick="bounce.cancel();clearInterval(timer) ; "></a>
        </div>
        <div class="bonceCon">
            <p id="lvTip"></p>
            <p id="t1">请设置年化收益率</p>
            <p class="textCenter"><input type="text" id="rate" onkeyup="clearNoNum(this)"> %</p>
            <p id="t2">请确定薪资宝对几月份工资开始生效 </p>
            <p class="textCenter"><input type="text" class="laydate-icon" id="ctime" readonly>
                <input type="text" <%--class="laydate-icon" --%> class="layui-input" id="qtime" lay-key="4" readonly>
            </p>
            <p class="update top"><i class="fa fa-circle-o ccc"></i> 新年化收益率仅对此日期后新转入的资金生效</p>
            <p class="update"><i class="fa fa-dot-circle-o green"></i> 新年化收益率对用户的所有余额均生效</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray"
                  onclick="bounce.cancel();clearInterval(timer) ; ">取消</span>
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" id="okLv">确定</span>
        </div>
    </div>
</div>
<%@ include  file="../../common/contentHeader.jsp"%>
<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>系统设置</span>
            </li>
        </ul>
        <div></div>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
			<div class="ty-container">
				<div class="ty-mainData">
                    <div class="center">
                        <p><span>工资发放日期</span> <span id="dtime1">— —</span> <span>
							<span class="ty-btn ty-circle-5 ty-btn-big ty-btn-green" id="dateSetingBtn"
                                  onclick="dateSetingBtn()">设置</span></span>
                        </p>
                        <p><span>年化收益率</span> <span id="rate1">— —</span>
                            <span><span class="ty-btn ty-circle-5 ty-btn-big ty-btn-green" id="lvSetingBtn"
                                        onclick="lvSetingBtn()">设置</span></span></p>
                    </div>
                    <div>
                        <p class="logTtl">操作日志</p>
                        <table class="ty-table" id="logs">
                            <tbody>
                            <tr>
                                <td width="15%">操作时间</td>
                                <td width="75%">操作内容</td>
                                <td width="10%">操作者</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
			</div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>
<script src="../script/salary/salarySeting.js?v=SVN_REVISION" type="text/javascript"></script>

</body>
</html>
