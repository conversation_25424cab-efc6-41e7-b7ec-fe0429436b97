<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>">
    
    <title>通用框架</title>

	<link rel="stylesheet" type="text/css" href="../css/carStyle.css?v=SVN_REVISION">
	<script type="text/javascript" src="../script/jquery-1.4.2.js?v=SVN_REVISION"></script>
	<script type="text/javascript">
	 $(function(){
//	 	location.href = "car_list.action?driverid=ced107f850394c68aa8b8c7599b2c9ed";
	 });
	</script>
  </head>
  
  <body>
        
 <a href="car_list.action?page=1&pageno=10">获取数据</a>
  <a href="car_driverList.action?page=1&pageno=10">司机信息</a>
<!--  <a href="car_dispatch.action?page=1&pageno=30">跳转页面</a> -->
  
  
  	<br>
  <form name="Form2"  id="Form2" style="margin:0px;" >
	  <table cellspacing="0" cellpadding="0" width="90%" align="center" bgcolor="#f5fafe" border="0">
			<TR>
				<TD align="center" background="images/cotNavGround.gif" width=25><img src="images/yin.gif" width="15"></TD>
				<TD class="DropShadow" background="images/cotNavGround.gif" width=80>汽车列表</TD>
				<td class="ta_01" align="right" >

				</td>
			</TR>
	  </table>
 	  <table cellspacing="1" cellpadding="0" width="90%" align="center" bgcolor="#f5fafe" border="0">
		 <tr>
			  <td class="ta_01" align="center" bgcolor="#f5fafe">
				  <table cellspacing="0" cellpadding="1" rules="all" bordercolor="gray" border="1" id="DataGrid1" style="BORDER-RIGHT:gray 1px solid; BORDER-TOP:gray 1px solid; BORDER-LEFT:gray 1px solid; WIDTH:100%; WORD-BREAK:break-all; BORDER-BOTTOM:gray 1px solid; BORDER-COLLAPSE:collapse; BACKGROUND-COLOR:#f5fafe; WORD-WRAP:break-word">
					<tr style="FONT-WEIGHT:bold;FONT-SIZE:12pt;HEIGHT:25px;BACKGROUND-COLOR:#afd1f3">
						<td align="center" width="3%"  height=22 background="images/tablehead.jpg">序号</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">汽车品牌</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">车型</td>
						<td align="center" width="5%" height=22 background="images/tablehead.jpg">当前里程</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">车牌号</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">颜色</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">车架号</td>
						<td align="center" width="3%" height=22 background="images/tablehead.jpg">保养周期</td>
						<td align="center" width="3%" height=22 background="images/tablehead.jpg">燃油牌号</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">发动机号</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">购买日期</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">单价</td>
						<td align="center" width="6%" height=22 background="images/tablehead.jpg">查看消费详情</td>
	<!-- 					<td width="7%" align="center" height=22 background="images/tablehead.jpg">编辑</td>
						<td width="7%" align="center" height=22 background="images/tablehead.jpg">删除</td> -->

					</tr>

					<s:iterator value="#session.pagelist" id="car">
					<tr onmouseover="this.style.backgroundColor = 'white'" onmouseout="this.style.backgroundColor = '#F5FAFE';">
						<td align="center" width="3%">&nbsp; <s:property value="#car.id"/></td>
						<td align="center" width="6%">&nbsp;<s:property value="#car.carbrand"/></td>
						<td align="center" width="6%"><s:property value="#car.carmodel"/></td>
						<td align="center" width="5%"><s:property value="#car.carmileage"/></td>
						<td align="center" width="6%" style="HEIGHT: 22px"><s:property value="#car.platenumber"/></td>
						<td align="center" width="6%"><s:property value="#car.color"/></td>
						<td align="center" width="6%"><s:property value="#car.framenumber"/></td>
						<td align="center" width="3%"><s:property value='#car.maintenance'/></td>
						<td align="center" width="3%"><s:property value='#car.fuelgrade'/></td>
						<td align="center" width="6%"><s:property value='#car.enginenumber'/></td>
						<td align="center" width="6%"><s:date name="#car.purchasedate" format="yyyy-MM-dd HH:mm"/></td>
						<td align="center" width="6%"><s:property value="#car.price"/></td>
						<td align="center" width="6%">
						<a href="car_dispatch.action?platenumber=<s:property value='#car.platenumber'/>" class="cardetails">详情</td>

					</tr>
				   </s:iterator>




				</table>
			  </td>
		 </tr>
	  </table>
       </td>
     </tr>
	</table>
</form>

<div>
<s:include value="paging.jsp">
<s:param name="table">carinfo</s:param>
</s:include>
</div>

  </body>
</html>
