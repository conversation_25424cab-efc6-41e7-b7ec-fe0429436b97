<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page session="false" %>
<%@ include  file="../../common/headerTop.jsp"%>
<link href="../css/common/theme/menuTree.css?v=SVN_REVISION"  rel="stylesheet" type="text/css"/>
<link href="../css/frozenAccount/frozenAccount.css?v=SVN_REVISION" rel="stylesheet" type="text/css"/>

</head>
<body class="page-header-fixed page-sidebar-closed-hide-logo page-content-white">
<%-- 参与人的弹窗 --%>

<%@ include  file="../../common/contentHeader.jsp"%>
<div class="bounce_Fixed">
    <div class="bonceContainer bounce-red" id="tip_Fixed">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon text-center"></div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="confirm_Fixed">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce_Fixed.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>该账号系由<span class="frozenPerson">张三</span>冻结。</p>
            <p>建议您充分确认冻结原因后再操作。</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce_Fixed.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-2 iKnow">我知道了，继续操作</span>
        </div>
    </div>
</div>
<div class="bounce">
    <%--提示--%>
    <div class="bonceContainer bounce-red" id="tip">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>该账号系由<span class="frozenPerson">张三</span>冻结。</p>
            <p>您无权更换该账号的代办人！</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-2 determine">确定</span>
        </div>
    </div>
    <div class="bonceContainer bounce-red" id="confirm">
        <div class="bonceHead">
            <span>! 提示</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon text-center">
            <p>该账号系由<span class="frozenPerson">张三</span>冻结。</p>
            <p>建议您充分确认冻结原因后再操作。</p>
        </div>
        <div class="bonceFoot">
            <span class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</span>
            <span class="ty-btn ty-btn-red ty-btn-big ty-circle-2 iKnow">我知道了，继续操作</span>
        </div>
    </div>

    <%--冻结账号--%>
    <div class="bonceContainer bounce-blue " id="frozenAccount">
        <div class="bonceHead">
            <span>冻结账号</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px;margin: auto">
                <div class="ty-alert ty-alert-info alertAccount"></div>
                <div class="mainIdentity"></div>
                <div class="otherIdentity"></div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" type="btn" data-name="next" id="next" disabled>下一步</button>
        </div>
    </div>

    <%--换代办人--%>
    <div class="bonceContainer bounce-blue " id="changeAgency">
        <div class="bonceHead">
            <span>换代办人</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <div style="width: 300px;margin: auto">
                <div class="agencyRecord"></div>
                <div class="changeAgency">
                    <div class="agencyItem">
                        <div class="">更换系统内工作的代办者（该账号继续冻结）</div>
                        <select name="" id=""></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">取消</button>
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" type="btn" data-name="sureChangeAgency" id="sureChangeAgencyBtn" disabled>确定</button>
        </div>
    </div>

    <%--查看讨论详情--%>
    <div class="bonceContainer bounce-green" id="frozenRecord" style="width: 1200px">
        <div class="bonceHead">
            <span>账号冻结记录</span>
            <a class="bounce_close" onclick="bounce.cancel()"></a>
        </div>
        <div class="bonceCon">
            <table class="kj-table">
                <thead>
                <tr>
                    <th>被冻结者</th>
                    <th>性别</th>
                    <th>手机号</th>
                    <th>部门</th>
                    <th>职位</th>
                    <th>操作记录</th>
                    <th>代办者</th>
                    <th>操作者</th>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div id="ye_frozenRecord"></div>
        </div>
        <div class="bonceFoot">
            <button class="ty-btn ty-btn-big ty-circle-2" onclick="bounce.cancel()">关闭</button>
        </div>
    </div>
</div>

<div class="page-container">
    <%@ include  file="../../common/contentSliderNav.jsp"%>
    <div class="ty-header">
        <ul>
            <li class="active">
                <span>冻结账号</span>
                <i class="close_x"></i>
            </li>
        </ul>
    </div>
    <div class="page-content-wrapper">
        <div class="page-content">
            <!--放内容的地方-->
            <div class="ty-container">
                <div class="page main">
                    <div class="ty-alert ty-alert-warning"><i class="fa fa-exclamation-triangle"></i>在册职工账号一旦被冻结，解冻前无法再登录系统。故冻结账号可作为紧急情况下的临时措施。</div>
                    <div class="ty-alert ty-alert-info">
                        <i class="fa fa-info-circle"></i>以下为全部在册职工。账号被冻结的职工也在其中。
                        <div class="btn-group text-right">
                            <span class="ty-btn-blue ty-btn ty-btn-big ty-circle-2" type="btn" data-name="onlySeeFrozen">只看账号被冻结的职工</span>
                            <span class="ty-btn-blue ty-btn ty-btn-big ty-circle-2" type="btn" data-name="frozenRecord">账号冻结记录</span>
                        </div>
                    </div>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <th>姓名</th>
                            <th>性别</th>
                            <th>手机号</th>
                            <th>部门</th>
                            <th>职位</th>
                            <th>直接上级</th>
                            <th>最高学历</th>
                            <th style="width: 15%">操作</th>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="ye_frozenMain"></div>
                </div>
                <div class="page frozenRecord" style="display: none">
                    <p>
                        <span class="ty-btn-blue ty-btn ty-btn-big ty-circle-2" type="btn" data-name="goBack">返回</span>
                    </p>
                    <div class="ty-alert">以下为账号被冻结的职工。</div>
                    <table class="kj-table">
                        <thead>
                        <tr>
                            <th>姓名</th>
                            <th>性别</th>
                            <th>手机号</th>
                            <th>部门</th>
                            <th>职位</th>
                            <th>直接上级</th>
                            <th>最高学历</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <%@ include  file="../../common/contentSliderLitbar.jsp"%>
</div>
<%@ include  file="../../common/footerTop.jsp"%>
<%@ include  file="../../common/footerScript.jsp"%>

<!-- Include JS file. -->
<script src="../script/frozenAccount/frozenAccount.js?v=SVN_REVISION"></script>
</body>
</html>
