$(function () {
    // 持续改进部分六种状态切换
    $(".mainPage .ty-secondTab li").on("click", function () {

        //获取点击按钮的下标
        var index = $(this).index();
        if(index === 5){
            $(".passedPage").show().siblings().hide();
            $(".passedButton").show().siblings().hide();
            getPassedCase(1,20,1);
        }else{
            $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
            $(".ty-mainData .tblContainer").eq(index).show().siblings(".tblContainer").hide();
            getImprovementByState(index+1)
        }
    });

    $("body").on("click",function () {
        $(".hel").hide()
    });

    $("[data-to]>span").on("click",function (e) {
        var id = $(this).parent().data("to");
        $("#"+id).show();
        e.stopPropagation();
    })
    // 本月和本年按钮点事件
    $(".passedButton .ty-btn-group a").on("click",function(){
        var index = $(this).index();    //时间类别按钮 当前位置
        if(index < 3){
            $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");    //切换效果
            var state = $(".ty-secondTab .ty-active").data("state");   //待处理等 当前位置
            getPassedCase(1,20,index+1) ;
        }
    });

    $("#queryBtn").on("click", function (e) {
        getPassedCase(1,20,4);
        $("#custom").hide();
        e.stopPropagation();
    });

    //审批记录收起展开功能
    $("#seeImprovement").on("click",".approve_foot",function () {
        $(this).prev().slideToggle("fast");
        var i = $(this).children("i");
        if(i.hasClass("fa-angle-down")){
            i.removeClass("fa-angle-down").addClass("fa-angle-up");
        }else{
            i.removeClass("fa-angle-up").addClass("fa-angle-down");
        }
    })

    //人员列表树切换
    $(".departTree").on("click" , "li>div" , function () {
        $(this).next().toggle();
    });

    $("#newImprovement").find("input,textarea").on("keyup",function () {
        var max = $(this).attr("max");
        if($.trim($(this).val()).length > max){
            var text = $(this).val().substring(0,max);
            $(this).val(text);
            layer.msg("最多输入"+max+"个字符！")
        }
    })

    mainInit();
});

function mainInit() {
    var id = getUrlParam("id");
    var status = getUrlParam("status"); //0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
    if(id){
        var bounceFootStr = '';
        if (status === "3" || status === "5"){
            bounceFootStr = '<button class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel(); ">取消</button>' +
                '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="closeCaseBtn()">结案</button>';
        }

        $(".mainPage .ty-secondTab li").eq(status-1).click();
        $("#seeImprovement .bonceFoot").html(bounceFootStr);
        $("#seeImprovement .bonceCon").attr("id",id);
        getImprovementDetail("#seeImprovement",id,status-1);
        bounce.show($("#seeImprovement"));
    }else{
        $(".mainPage .ty-secondTab li").eq(2).click();
    }
}


//-----------------持续改进流程-------------------//

/* creator：张旭博，2018-01-17 14:51:09，新增持续改进 - 按钮 */
function newImprovementBtn(){
    var $newComplaint = $('#newImprovement');



    //清除所有内容（初始化）
    $newComplaint.find("input").val("");
    $newComplaint.find("textarea").val("");

    $("#newComplaint .cp_handlerName").html($("body").data("handle"));
    bounce.show($newComplaint);
    //开启表单验证
    bounce.everyTime('0.5s','newImprovement',function(){
        var state = 1;
        $newComplaint.find(".required").each(function () {
            if($.trim($(this).val()) === ''){
                state = 0;
            }
        });
        if( state === 0 ){
            $("#sureNewImprovementBtn").prop("disabled",true)
        }else {
            $("#sureNewImprovementBtn").prop("disabled",false)
        }
    });

}

/* creator：张旭博，2018-01-18 09:45:56，新增持续改进 - 确定 */
function sureNewImprovement() {

    //首先开启loading，防止用户误操作
    loading.open();

    //准备ajax参数
    var projectName     = $("#newImprovement .cp_add_box .projectName").val();      //项目名称（必填）
    var category        = $("#newImprovement .cp_add_box .category").val();         //类别
    var tag             = $("#newImprovement .cp_add_box .tag").val();              //标签
    var code            = $("#newImprovement .cp_add_box .code").val();             //项目编号
    var foundation      = $("#newImprovement .cp_add_box .foundation").val();       //立项依据
    var principalName   = $("#newImprovement .cp_add_box .principalName").val();    //项目负责人
    var member          = $("#newImprovement .cp_add_box .member").val();           //小组成员
    var currentSituation= $("#newImprovement .cp_add_box .currentSituation").val(); //项目现况
    var proposal        = $("#newImprovement .cp_add_box .proposal").val();         //立项目的
    var description     = $("#newImprovement .cp_add_box .description").val();      //项目描述
    var startTimeFind   = $("#newImprovement .cp_add_box .startTimeFind").val();    //预计开始日期
    var endTimeFind     = $("#newImprovement .cp_add_box .endTimeFind").val();      //预计完成日期
    var profitEstimate  = $("#newImprovement .cp_add_box .profitEstimate").val();   //收益估算
    var memo            = $("#newImprovement .cp_add_box .memo").val();             //备注

    var data= {
        "state":1,   //案子的状态，1是立案者上传，新增成功后在立案待审批页面显示这条案子。2是核心人物上传，新增成功后在立案已批准页面显示案子
        "projectName":projectName,
        "category":category,
        "tag":tag,
        "code":code,
        "foundation":foundation,
        "principalName":principalName,
        "member":member,
        "currentSituation":currentSituation,
        "proposal":proposal,
        "description":description,
        "startTimeFind":startTimeFind,
        "endTimeFind":endTimeFind,
        "profitEstimate":profitEstimate,
        "memo":memo
    };
    $.ajax({
        url:"../improvement/insertImprovementCase.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ },
        success:function( data ){
            var status = data["success"];
            if(status === 1){
                layer.msg("新增成功");
                bounce.cancel();
                $(".mainPage .ty-secondTab li").eq(0).click();
            }else{
                $("#errorTip .tipWord").html("新增失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-17 15:31:44，获取不同状态的持续改进列表 */
function getImprovementByState(state) {
    $.ajax({
        url:"../improvement/getImprovementCase.do",
        data:{
            "state":state,  //状态， 1-立案待审批 2-立案驳回 3-立案通过 4-结案待审批 5-结案驳回
            "role":2        //1-核心人物 2-立案者
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ) {
            var listData = data["listImprovement"];

            var listStr = '';
            if (listData.length > 0) {
                switch (state){
                    case 1:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].projectName + '</td>' +                //项目名称
                                '<td>' + listData[i].code + '</td>' +                       //项目编号
                                '<td>' + listData[i].category + '</td>' +                   //类别
                                '<td>' + listData[i].tag + '</td>' +                        //标签
                                '<td>' + listData[i].principalName + '</td>' +              //项目负责人
                                '<td>' + listData[i].beginTime.substring(0,10)+ '</td>' +              //预计开始日期
                                '<td>' + listData[i].endTime.substring(0,10) + '</td>' +                //预计完成日期
                                '<td>' + listData[i].createName + '</td>' +                 //立案申请者
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeImprovementBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 2:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].projectName + '</td>' +                //项目名称
                                '<td>' + listData[i].code + '</td>' +                       //项目编号
                                '<td>' + listData[i].category + '</td>' +                   //类别
                                '<td>' + listData[i].tag + '</td>' +                        //标签
                                '<td>' + listData[i].principalName + '</td>' +              //项目负责人
                                '<td>' + listData[i].beginTime.substring(0,10) + '</td>' +              //预计开始日期
                                '<td>' + listData[i].endTime.substring(0,10) + '</td>' +                //预计完成日期
                                '<td>' + listData[i].createName + '</td>' +                 //立案申请者
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeImprovementBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 3:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].projectName + '</td>' +                //项目名称
                                '<td>' + listData[i].code + '</td>' +                       //项目编号
                                '<td>' + listData[i].category + '</td>' +                   //类别
                                '<td>' + listData[i].tag + '</td>' +                        //标签
                                '<td>' + listData[i].principalName + '</td>' +              //项目负责人
                                '<td>' + listData[i].beginTime.substring(0,10) + '</td>' +              //预计开始日期
                                '<td>' + listData[i].endTime.substring(0,10) + '</td>' +                //预计完成日期
                                '<td>' + listData[i].createName + '</td>' +                 //立案申请者
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeImprovementBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 4:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].projectName + '</td>' +                //项目名称
                                '<td>' + listData[i].code + '</td>' +                       //项目编号
                                '<td>' + listData[i].category + '</td>' +                   //类别
                                '<td>' + listData[i].tag + '</td>' +                        //标签
                                '<td>' + listData[i].principalName + '</td>' +              //项目负责人
                                '<td>' + listData[i].settleTime + '</td>' +                 //实际结案日期
                                '<td>' + listData[i].settleName + '</td>' +                 //结案者
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeImprovementBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 5:
                        for (var i = 0; i < listData.length; i++) {
                            listStr += '<tr id="' + listData[i].id + '">' +
                                '<td>' + listData[i].projectName + '</td>' +                //项目名称
                                '<td>' + listData[i].code + '</td>' +                       //项目编号
                                '<td>' + listData[i].category + '</td>' +                   //类别
                                '<td>' + listData[i].tag + '</td>' +                        //标签
                                '<td>' + listData[i].principalName + '</td>' +              //项目负责人
                                '<td>' + listData[i].settleTime + '</td>' +                 //实际结案日期
                                '<td>' + listData[i].settleName + '</td>' +                 //结案者
                                '<td>' +
                                '<span class="ty-color-blue" onclick="seeImprovementBtn($(this))">查看</span>' +
                                '</td>' +
                                '</tr>';
                        }
                        break;
                    case 6:
                        getPassedCase(1,20,1);
                        break;
                }
                $(".mainPage .tblContainer").eq(state - 1).find("tbody").html(listStr);
            }else{
                $(".mainPage .tblContainer").eq(state - 1).find("tbody").html("");
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-18 15:26:31，持续改进查看 - 按钮 */
function seeImprovementBtn(selector){
    bounce.show($("#seeImprovement"));
    $(".ty-radioActive").removeClass("ty-radioActive").children("i").attr("class","fa fa-circle-o");
    var state = $(".mainPage .ty-secondTab li").index($(".mainPage .ty-secondTab li.ty-active"));
    var bonceFootStr = '';
    $("#cp_processor").html('<div class="cp_scan cp_handlerName"></div>');
    switch (state){
        //立案通过
        case 2:
            bonceFootStr =  '<button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</button>' +
                '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="closeCaseBtn()">结案</button>';
            break;
        //结案驳回
        case 4:
            bonceFootStr =  '<button class="ty-btn ty-btn-big ty-circle-3" onclick="bounce.cancel(); ">取消</button>' +
                '<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" onclick="closeCaseBtn()">结案</button>';
            break;
    }
    $("#seeImprovement .bonceFoot").html(bonceFootStr);
    var ImprovementId = selector.parent().parent().attr("id");
    $("#seeImprovement .bonceCon").attr("id",ImprovementId);
    getImprovementDetail("#seeImprovement",ImprovementId,state);
}

/* creator：张旭博，2018-01-31 09:50:48，持续改进查看 - 结案 - 按钮 */
function closeCaseBtn() {
    bounce_Fixed.show($("#closeCase")) ;
    $("#factTime").val("");
    $("#settleOpinion").val("");
    //开启表单验证
    bounce_Fixed.everyTime('0.5s','closeCase',function(){
        if( $("#factTime").val() === "" || $("#settleOpinion").val() === "" ){
            $("#sureCloseCaseBtn").prop("disabled",true)
        }else {
            $("#sureCloseCaseBtn").prop("disabled",false)
        }
    });
}

/* creator：张旭博，2018-01-31 09:59:04，持续改进查看 - 审批 - 确认弹窗（立案批准、立案驳回、结案批准、结案驳回） */
function approveImprovementConfirm(type,state){
    if(type === 1){
        //批准
        $("#tip .tipWord").html("您确定批准吗？") ;
        $("#tip .bonceFoot").html('<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="approveImprovement('+state+')" id="sureCloseCaseBtn">确定</button>') ;
    }else{
        //批准
        $("#tip .tipWord").html('<p>请输入驳回理由：</p><textarea id="rejectReason" cols="30" rows="3" style="width: 300px"></textarea>') ;
        $("#tip .bonceFoot").html('<button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="approveImprovement('+state+')" id="sureCloseCaseBtn">确定</button>') ;
    }
    bounce_Fixed.show($("#tip"));
}

/* creator：张旭博，2018-01-31 09:59:04，持续改进查看- 审批 - 确定（立案批准、立案驳回、结案批准、结案驳回、结案） */
function approveImprovement(state) {

    var id = $("#seeImprovement .bonceCon").attr("id");

    var data = {
        "id":id,            //持续改进id
        "state":state,      //状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
    };
    var url = "../improvement/approvalImprovementCase.do";
    switch (state){
        case 4:
            var factTime = $("#factTime").val();
            var settleOpinion = $("#settleOpinion").val();
            data["factTime"] = factTime;    //实际结案时间
            data["settleOpinion"] = settleOpinion;//结案意见
            break;
    }

    $.ajax({
        url:url ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var success = data["success"];
            if(success === 1){
                bounce.cancel();
                bounce_Fixed.cancel();
                switch (state){
                    //1-立案待审批
                    case 1:
                        break;
                    //2-立案驳回
                    case 2:
                        layer.msg("立案驳回成功");
                        break;
                    //3-立案通过
                    case 3:
                        layer.msg("立案批准成功");
                        break;
                    //4-结案待审批
                    case 4:
                        layer.msg("结案成功");
                        break;
                    //5-结案驳回
                    case 5:
                        layer.msg("结案驳回成功");
                        break;
                    //6-结案通过
                    case 6:
                        layer.msg("结案批准成功");
                        break;
                }
                $(".mainPage .ty-secondTab li.ty-active").click();
            }else{
                $("#errorTip .tipWord").html("提交失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            bounce_Fixed.cancel();
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}


//-----------------结案通过（数据筛选）-------------------//

/* creator：张旭博，2018-02-01 14:18:36，获得结案通过的持续改进 */
function getPassedCase(curr,totalPage,buttonType) {
    var data = {
        "buttenType":buttonType,//1代表获取本年的结案的case（即刚点进来时就要加载的方法，传2代表获取去年结案的case，传3代表获取前年结案的case，传4代表按某个特定的时间段获取结案的case
        "role":2,//身份信息 1是核心人物 2是立案者 3是处理者 4是查看者
        "currentPageNo":curr,
        "pageSize":totalPage,
        "state":6
    };
    if(buttonType === 4){
        var startTime = $("#queryBeginTime").val()+" 00:00:00";
        var endTime = $("#queryEndTime").val()+" 23:59:59";
        data["startTimeFind"] = startTime;//开始时间
        data["endTimeFind"] = endTime;//结束时间
    }

    $.ajax({
        url:"../improvement/getSettleCase.do",
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var listData = data["listBase"],
                pageInfo = data["pageInfo"];

            var startDay        = formatTime(data.timeBegin,false),
                endDay          = formatTime(data.timeEnd,false),
                totalResult     = pageInfo.totalResult

            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"],
                jsonStr = JSON.stringify({
                    "buttonType" : buttonType
                }) ;
            setPage( $("#ye_Improvement_integrate"), cur, total, "recruit",jsonStr) ;


            var tipStr  = '<div class="ty-alert ty-alert-warning">自'+startDay+'~'+endDay+'，已结案的持续改进项目共 <span class="ty-color-blue complaintResult">'+totalResult+'</span> 个</div>';
            var listStr = '';
            if(listData.length>0) {
                for (var i = 0; i < listData.length; i++) {
                    listStr += '<tr id="' + listData[i].id + '">' +
                        '<td>' + listData[i].projectName + '</td>' +                //项目名称
                        '<td>' + listData[i].code + '</td>' +                       //项目编号
                        '<td>' + listData[i].category + '</td>' +                   //类别
                        '<td>' + listData[i].tag + '</td>' +                        //标签
                        '<td>' + listData[i].principalName + '</td>' +              //项目负责人
                        '<td>' + listData[i].settleTime + '</td>' +                 //实际结案日期
                        '<td>' + listData[i].settleName + '</td>' +                 //结案者
                        '<td>' +
                        '<span class="ty-color-blue" onclick="seeImprovementBtn($(this))">查看</span>' +
                        '</td>' +
                        '</tr>';
                }
            }
            $(".passedPage .tplContainer").eq(0).find(".tip").html(tipStr);
            $(".passedPage .tplContainer").eq(0).find("tbody").html(listStr);
            if(buttonType === 4) {
                $("#customTimeBtn").addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        },
        complete:function(){ loading.close();}
    }) ;
}


//-----------------辅助方法-------------------//

// creator: 张旭博，2018-06-06 11:02:46，获取投诉详情
function getImprovementDetail(selector,improvementId,approveState) {
    /*
     * selector : 选择器
     * complaintId : 投诉id
     */
    $.ajax({
        url:"../improvement/showCaseMessage.do" ,
        data:{  "id":improvementId, "role":2 },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var improvementDetail = data["ciBase"];
            var approvalProcessList = data["approvalProcessList"];
            //查看页面通用信息赋值(包括附件)
            var projectName     = improvementDetail.projectName;                //项目名称
            var category        = improvementDetail.category;                   //类别
            var tag             = improvementDetail.tag;                        //标签
            var code            = improvementDetail.code;                       //项目编号
            var foundation      = improvementDetail.foundation;                 //立项依据
            var principalName   = improvementDetail.principalName;              //项目负责人
            var member          = improvementDetail.member;                     //小组成员
            var currentSituation= improvementDetail.currentSituation;           //项目现况
            var proposal        = improvementDetail.proposal;                   //立项目的
            var description     = improvementDetail.description;                //项目描述
            var startTimeFind   = improvementDetail.beginTime.substring(0,10);  //预计开始日期
            var endTimeFind     = improvementDetail.endTime.substring(0,10);    //预计完成日期
            var profitEstimate  = improvementDetail.profitEstimate;             //收益估算
            var memo            = improvementDetail.memo;                       //备注
            var createName      = improvementDetail.createName;                 //立案人
            var settleName      = improvementDetail.settleName;                 //结案人
            var settleOpinion      = improvementDetail.settleOpinion;           //结案意见
            var settleTimeFact      = improvementDetail.settleTimeFact;         //实际结案日期

            //基本信息部分
            $(selector+" .cp_scan_box .cp_projectName").html(projectName);
            $(selector+" .cp_scan_box .cp_category").html(category);
            $(selector+" .cp_scan_box .cp_tag").html(tag);
            $(selector+" .cp_scan_box .cp_code").html(code);
            $(selector+" .cp_scan_box .cp_foundation").html(foundation);
            $(selector+" .cp_scan_box .cp_principalName").html(principalName);
            $(selector+" .cp_scan_box .cp_member").html(member);
            $(selector+" .cp_scan_box .cp_currentSituation").html(currentSituation);
            $(selector+" .cp_scan_box .cp_proposal").html(proposal);
            $(selector+" .cp_scan_box .cp_description").html(description);
            $(selector+" .cp_scan_box .cp_startTimeFind").html(startTimeFind);
            $(selector+" .cp_scan_box .cp_endTimeFind").html(endTimeFind);
            $(selector+" .cp_scan_box .cp_profitEstimate").html(profitEstimate);
            $(selector+" .cp_scan_box .cp_memo").html(memo);
            $(selector+" .cp_scan_box .cp_createName").html(createName);
            $(selector+" .cp_scan_box .cp_settleName").html(settleName);

            //结案记录部分
            var endCaseRecordStr = '';
            if(Number(approveState) > 2 ){
                endCaseRecordStr =  '<div class="formItem">' +
                    '    <div class="formTitle"><span>实际结案日期</span></div>' +
                    '    <div class="formCon"><div class="cp_textarea cp_settleTimeFact">'+settleTimeFact+'</div></div>' +
                    '</div>' +
                    '<div class="formItem">' +
                    '    <div class="formTitle"><span>结案意见</span></div>' +
                    '    <div class="formCon"><div class="cp_textarea cp_settleOpinion">'+settleOpinion+'</div></div>' +
                    '</div>'
            }
            $(selector+" .cp_endCaseRecord").html(endCaseRecordStr);

            //审批记录部分
            var approvalProcessListStr = getApproveDetail(approvalProcessList);
            $(selector+" .cp_approveHistory").html(approvalProcessListStr);
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

// creator: 张旭博，2018-06-29 09:44:04，获取审批记录
function getApproveDetail(approveData) {
    var length = approveData.length,
        approve_word1 = '',
        approve_word2 = '',
        approve_icon1 = '',
        approve_icon2 = '',
        approve_reject1 = '',
        approve_reject2 = '',
        approveStr = '';

    if(length === 2 || length === 3){
        if(approveData[length-2].approveStatus === "2"){
            approve_word1 = '驳回';
            approve_icon1 = 'fa-times-circle';
            approve_reject1 = '<div class="approve_item"><span>驳回理由</span> <span class="rejectReason">'+approveData[length-2].reason+'</span></div>'
        }else if(approveData[length-2].approveStatus === "3"){
            approve_word1 = '批准';
            approve_icon1 = 'fa-check-circle';
        }
        approveStr =   '<div class="panel_approve">' +
            '    <div class="approve_con">' +
            '        <div class="approve_state">' +
            '           <i class="fa ' + approve_icon1 + '"></i>' +
            '           <span>立案已' + approve_word1 + '</span>'+
            '</div>' +
            '        <div class="approve_record">' + approve_reject1 +
            '            <div class="approve_item">' +
            '                <span class="approve_role">立案' + approve_word1 + '者</span>' +
            '                <span class="approve_name">' + approveData[length-2].toUserName + '</span>' +
            '                <span class="approve_time">' + approveData[length-2].createDate + '</span>' +
            '            </div>' +
            '            <div class="approve_item">' +
            '                <span class="approve_role">立案申请者</span>' +
            '                <span class="approve_name">' + approveData[length-1].toUserName + '</span>' +
            '                <span class="approve_time">' + approveData[length-1].createDate + '</span>' +
            '            </div>' +
            '        </div>' +
            '    </div>' +
            '    <div class="approve_foot">' +
            '        <span>审批记录</span>' +
            '        <i class="fa fa-angle-down"></i>' +
            '    </div>' +
            '</div>';
    }else if(length === 4) {
        if(approveData[0].approveStatus === "5"){
            approve_word1 = '驳回';
            approve_icon1 = 'fa-times-circle';
            approve_reject1 = '<div class="approve_item"><span>驳回理由</span> <span class="rejectReason">'+approveData[0].reason+'</span></div>'
        }else if(approveData[0].approveStatus === "6"){
            approve_word1 = '批准';
            approve_icon1 = 'fa-check-circle';
        }
        if(approveData[2].approveStatus === "2"){
            approve_word2 = '驳回';
            approve_icon2 = 'fa-times-circle';
            approve_reject2 = '<div class="approve_item"><span>驳回理由</span> <span class="rejectReason">'+approveData[2].reason+'</span></div>'
        }else if(approveData[2].approveStatus === "3"){
            approve_word2 = '批准';
            approve_icon2 = 'fa-check-circle';
        }
        approveStr =    '<div class="panel_approve">' +
            '    <div class="approve_con">' +
            '       <div class="approve_state">' +
            '          <i class="fa ' + approve_icon1 + '"></i>' +
            '          <span>结案已' + approve_word1 + '</span>'+
            '       </div>' +
            '       <div class="approve_record">' + approve_reject1 +
            '           <div class="approve_item">' +
            '               <span class="approve_role">结案' + approve_word1 + '者</span>' +
            '               <span class="approve_name">'+approveData[0].toUserName+'</span>' +
            '               <span class="approve_time">'+approveData[0].createDate+'</span>' +
            '           </div>' +
            '           <div class="approve_item">' +
            '               <span class="approve_role">结案申请者</span>' +
            '               <span class="approve_name">'+approveData[1].toUserName+'</span>' +
            '               <span class="approve_time">'+approveData[1].createDate+'</span>' +
            '           </div>' +
            '       </div>' +
            '       <div class="approve_state">' +
            '          <i class="fa ' + approve_icon2 + '"></i>' +
            '          <span>立案已' + approve_word2 + '</span>'+
            '       </div>' +
            '       <div class="approve_record">' + approve_reject2 +
            '           <div class="approve_item">' +
            '               <span class="approve_role">立案' + approve_word2 + '者</span>' +
            '               <span class="approve_name">'+approveData[2].toUserName+'</span>' +
            '               <span class="approve_time">'+approveData[2].createDate+'</span>' +
            '           </div>' +
            '           <div class="approve_item">' +
            '               <span class="approve_role">立案申请者</span>' +
            '               <span class="approve_name">'+approveData[3].toUserName+'</span>' +
            '               <span class="approve_time">'+approveData[3].createDate+'</span>' +
            '           </div>' +
            '       </div>' +
            '    </div>' +
            '    <div class="approve_foot">' +
            '        <span>审批记录</span>' +
            '        <i class="fa fa-angle-down"></i>' +
            '    </div>' +
            '</div>';
    }else{
        approveStr = '';
    }
    return approveStr;

}

/* creator：张旭博，2018-01-19 09:41:00，设置不同分工者下拉选项*/
function setDivisionOption(selector,coreCode){
    return $.ajax({
        url:"../coreSetting/getThreeUser.do" ,
        data:{
            "coreCode":coreCode
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                var listData = data["codeUsers"];

                var handlerStr = '<option value="">------请选择------</option>';
                for(var i in listData){
                    handlerStr += '<option value="'+listData[i].userID+'">'+listData[i].userName+'</option>';
                }
                selector.html(handlerStr);
            }else{
                $("#errorTip .tipWord").html("获取列表失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        }
    }) ;
}

// creator: 张旭博，2018-06-29 11:58:30，获取分工
function getDivision(coreCode) {
    return $.ajax({
        url:"../coreSetting/getThreeUser.do" ,
        data:{
            "coreCode":coreCode
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                var listData = data["codeUsers"];

                var handlerStr = '<option value="">------请选择------</option>';
                for(var i in listData){
                    handlerStr += '<option value="'+listData[i].userID+'">'+listData[i].userName+'</option>';
                }
                $("body").data(coreCode,handlerStr);
            }else{
                $("#errorTip .tipWord").html("获取列表失败!") ;
                bounce.show($("#errorTip")) ;
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        },
        complete:function () {}
    }) ;
}

/* creator：张旭博，2018-01-23 16:22:45，转换性别字符串 */
function chargeSex(gender) {
    return gender === "1"?"男":"女";
}

/* creator：张旭博，2018-01-18 09:59:33，返回主页 */
function goBack() {
    $(".mainPage").show().siblings().hide();
    $(".mainButton").show().siblings().hide();
}

//-----------------选人方法（候）--------------------//

/* creator: 侯杏哲 2017-12-06 工具方法 拼接部门人员需要的字符串 */
function setStr( data , method , userArr ) { // data:该机构中人员的数据 , method:加或者减的方法
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var hasKids = false ;
            var userList = data[i]["userList"]; // 人员列表
            var kidList = data[i]["subList"]; // 部门列表
            if( (userList && kidList.length > 0) || (userList && userList.length > 0) ){ // 有子级
                hasKids = true ;
                strAll += '<li><div lass="departid"  info="'+ data[i]["id"] +'"><i class="fa fa-angle-right"></i><span>'+ data[i]["name"] +'</span> <i class="fa fa-'+ method +'-square" onclick="'+ method +'($(this), event)"></i></div><ul>' ;
            }else{ // 无子级
                strAll += '<li><div lass="departid"  info="'+ data[i]["id"] +'"><i class="fa fa-angle-down"></i><span>'+ data[i]["name"] +'</span> <i class="fa fa-'+ method +'-square" onclick="'+ method +'($(this), event)"></i></div>' ;
            }
            if(kidList && kidList.length > 0){ // 遍历子级部门
                strAll += setStr( kidList , method  ) ;
            }
            if(userList && userList.length > 0){ // 遍历员工
                for(var j in userList){
                    strAll += '<li><input type="hidden" name="userID" value="'+ userList[j]["userID"] +'"><div lass="uid" info="'+ userList[j]["userID"] +'"><i class="fa fa-info"></i><span>'+ userList[j]["userName"] +'</span> <i class="fa fa-'+ method +'-square" onclick="' + method + '($(this), event)"></i></div></li>' ;
                    if(userArr){ // 需要获取所有用户
                        var jsonStr = { "userid" :  userList[j]["userID"]  } ;
                        userArr.push(jsonStr) ;
                    }
                }
            }
            if(hasKids){
                strAll += "</ul></li>" ;
            }else{
                strAll += "</li>" ;
            }
        }
    }
    return strAll ;
}

/* creator: 侯杏哲 2017-12-06 工具方法 - 设置右边的部门树  */
function setRightStr(right){
    if(right && right.length > 0){
        for(var i in right){
            var hasKids = false ;
            var userList = right[i]["userList"]; // 人员列表
            var kidList = right[i]["subList"]; // 部门列表
            if(kidList && kidList.length > 0){ // 遍历子级部门
                setRightStr( kidList ) ;
            }
            if(userList && userList.length > 0){ // 遍历员工
                for(var j in userList){
                    var userID = userList[j]["userID"] ;ddddd
                    var jsonStr = { "userid" :  userID } ;
                    userArr.push(jsonStr);
                    var isEObj = isExist( "uid" , userID , $("#allRight") ) ;
                    if(isEObj){
                        isEObj.children(".fa-plus-square").click() ;
                        isEObj.click() ;
                    }
                }
            }
        }
    }
}

/* creator: 侯杏哲 2017-12-06 工具方法 对比左右的字符串 */
function chargeBoth(leftObj , rightObj) {
    rightObj.find(".fa-info").each(function(){
        var _thisInfo = $(this) ;
        var userID = _thisInfo.parent().attr("info") ;
        var kls = _thisInfo.siblings(".fa-minus-square").hasClass("ty-gray") ; // 当前的这个是不是灰色的
        if(!kls){ // 当前是高亮的
            var isAllAct = true ; // 默认所有都是高亮
            var sib = _thisInfo.parent().parent().siblings("li") ;
            if(sib.length > 0){
                sib.each(function(){
                    var isAct = $(this).children("div").children(".fa-minus-square").hasClass("ty-gray") ;
                    if( isAct ){ // 不是高亮的
                        isAllAct = false ;
                    }
                })
            }
            if(isAllAct){ // 符合要求，父级设置为高亮
                var pDiv = _thisInfo.parent("div").parent("li").parent("ul").siblings("div") ;
                var lass = pDiv.attr("lass") ;
                var deparID = pDiv.attr("info") ;
                pDiv.children(".fa-minus-square").removeClass("ty-gray") ;
                var isE_DivObj = isExist( lass , deparID , $("#allRight") ) ;
                if(isE_DivObj){ isE_DivObj.children(".fa-plus-square").addClass("ty-gray") ;  }
            }
        }
    }) ;
    var bigAct = true ;
    rightObj.children("li:first").children("ul").children("li").each(function(){
        var isA = $(this).children("div").children(".fa-minus-square").hasClass("ty-gray") ;
        if(isA){ bigAct = false ;  }
    });
    if(bigAct){
        rightObj.children("li:first").children("div").children(".fa-minus-square")
    }
}

/* creator：侯杏哲，2017-12-07 13:46:44 权限设置 - 加号 */
function plus( obj , event) {
    event.stopPropagation() ;
    $("#sureChangeRight").attr("class" ,"ty-btn ty-btn-blue ty-btn-big ty-circle-5").attr("onclick" , "sureChangeRight()") ;
    var kls = obj.hasClass("ty-gray") ;
    if( kls ){
        console.log("不生效!");
    }else{
        var lass = obj.parent().attr("lass") , id = obj.parent().attr("info") ;
        var isE = isExist(lass , id , $("#nowRight")) ;
        if(isE){ // 右边已存在
            isE.children("i.fa-minus-square").removeClass("ty-gray") ; // 右边点亮
            var nUlObj = isE.next("ul") ;
            if(nUlObj){ nUlObj.find(".fa-minus-square").each(function(){ $(this).removeClass("ty-gray") ;  } ) }
            obj.addClass("ty-gray") ; // 左边熄灭
            var lUlObj = obj.parent().next("ul") ;
            if(lUlObj){ lUlObj.find(".fa-plus-square").each(function(){ $(this).addClass("ty-gray") ;  } ) }
        }else{
            // 以下是右边需要新增的情况
            var len = obj.parents("li").length ;
            if( len > 1){ // 不是一级文件夹
                var pDivObj = obj.parent("div") ;
                var pLiObj = pDivObj.parent(); // 点击的哪一级全部li
                var p2UlObj = pLiObj.parent("ul") ; // 点击的包含这一级的ul
                var kDivStr = pLiObj.html() ;
                pLiObj.html("REPLACE") ;
                var firstLiObj = p2UlObj.parents("li:last") ;
                var str = "<li>" + firstLiObj.html() + "</li>";
                var minusStr1 = str.replace( /fa-plus-square/g , "fa-minus-square ty-gray") ; // 上级的减号不能点
                minusStr1 = minusStr1.replace( /plus/g , "minus") ; // 将上级的方法替换
                var kidStr = kDivStr.replace( /plus/g , "minus") ; // 子级的加号替换为减号
                var minusStr = minusStr1.replace("REPLACE" , kidStr ) ; // 组成用来替换的字符串
                $("#nowRight").append( minusStr ) ;
                pLiObj.html(kDivStr) ;
                pLiObj.children().find("i.fa-plus-square").addClass("ty-gray") ;
            }else{ // 一级文件夹
                var liObj = obj.parents("li:last") ;
                var str = liObj.html() ;
                str = "<li>" + str + "</li>" ;
                var minusStr = str.replace( /plus/g , "minus") ;
                $("#nowRight").append( minusStr ) ;
                liObj.find("i.fa-plus-square").addClass("ty-gray") ;
            }
        }
        /* 补充显示 */
        chargeBoth( $("#allRight") , $("#nowRight") );
    }

}

/* creator：侯杏哲 2017-12-07 判断ulObj是否有选中的id , 有的话返回该div 对象 */
function isExist( lass , id , ulObj ) {
    var isE = false ;
    ulObj.children("li").each(function(){
        var _thisDiv = $(this).children("div") ;
        var _lass = _thisDiv.attr("lass") ;
        var _id = _thisDiv.attr("info") ;
        if(_lass == lass && _id == id){ // 右边有这个部门
            isE = _thisDiv ; return _thisDiv ;
        }
        if(!isE){
            var kUlObj = $(this).children("ul") ;
            if(kUlObj){ isE = isExist( lass , id , kUlObj ) }
        }
    }) ;
    return isE ;
}

/* creator：侯杏哲，2017-12-07 13:46:44 权限设置 - 减号 */
function minus( obj , event) {
    event.stopPropagation();
    $("#sureChangeRight").attr("class" ,"ty-btn ty-btn-blue ty-btn-big ty-circle-5").attr("onclick" , "sureChangeRight()") ;
    var kls = obj.hasClass("ty-gray") ;
    if( kls ){
        console.log("不生效!");
    }else{
        var pDivObj = obj.parent() ;
        var lass = pDivObj.attr("lass") , id = pDivObj.attr("info") ;
        var isE_DivObj = isExist( lass , id , $("#allRight") ) ;
        // 处理左边
        if(isE_DivObj){
            isE_DivObj.find("i.ty-gray").removeClass("ty-gray") ;
            var ulObj = isE_DivObj.next("ul");
            if(ulObj){ // 子级全部点亮
                ulObj.find("i.ty-gray").each(function () {
                    $(this).removeClass("ty-gray") ;
                })
            }
        }else {
            console.log("左面没找到!");
        }
        // 处理右边
        var pLiObj = pDivObj.parent() ;
        var ulID = pLiObj.parent().attr("id") ;
        if(ulID == "nowRight"){ // 是第一级的
            pLiObj.remove() ;
        }else{ //  不是第一级的
            obj.addClass("ty-gray") ;
            var sUlObj = pDivObj.next("ul") ;
            if(sUlObj){ // 处理子级 , 子级全部熄灭
                sUlObj.find(".fa-minus-square").each(function(){
                    $(this).addClass("ty-gray") ;
                })
            }
            // 判断整个大部门 有没有必要删除
            var count = 0 ;
            var firstLiObj = pDivObj.parents("li:last") ;
            var firstUlObj = firstLiObj.children("ul") ;
            if(firstUlObj){
                console.log(firstUlObj.find(".fa-minus-square").length) ;
                firstUlObj.find(".fa-minus-square").each(function(){
                    var ish =  $(this).hasClass("ty-gray") ;
                    if( !ish ){ count++ ; }

                });
            }
            if(count == 0){  // 没有选中的了,删除大部门
                var firDivObj = firstLiObj.children("div") ;
                var firLass = firDivObj.attr("lass") ;
                var firId = firDivObj.attr("info") ;
                var isEx = isExist( firLass , firId , $("#allRight") ) ;
                isEx.find(".fa-plus-square").removeClass("ty-gray") ;
                firstLiObj.remove() ;
            }
        }
        /* 补充显示 */
        chargeBoth( $("#allRight") , $("#nowRight") );
    }
}

laydate.render({elem: '#startTimeFind'});
laydate.render({elem: '#endTimeFind'});
laydate.render({elem: '#factTime'});
laydate.render({elem: '#queryBeginTime'});
laydate.render({elem: '#queryEndTime'});
