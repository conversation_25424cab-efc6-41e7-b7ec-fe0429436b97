/**
 * Created by 侯杏哲 on 2017/4/1 0001. 完成用户登录后 申请机构 以及 选择机构登录系统 的功能
 */

var perNum = 20; // 机构大页面 - 每页显示机构数

$(function () {
    let token = auth.getToken()
    // sessionStorage.clear()
    localStorage.removeItem("loginTry")
    if(typeof token === 'string' && token.length>0) {
        auth.saveToken(token)
    }
    //wyu：删除其他
    // localStorage.removeItem('lastOperationedtime');
    // localStorage.removeItem('lastAjaxtime');
    // 回到切换机构页面以后悬浮窗的状态重置
    localStorage.removeItem('floatMenu') // 清除悬浮窗的url
    // localStorage.removeItem("bnsShow")// 每次登陆浮窗都显示
    // localStorage.removeItem("floating") // 清楚悬浮窗设置的导航（跟返回有关的）
    // localStorage.removeItem("mainMenu");
    // localStorage.removeItem("messageMenu");
    // localStorage.removeItem("userBadgeNumbers");

    // 各个按钮点击事件
    $(".content").on("click", "[type='btn']",  function () {
        var name = $(this).data('name')
        switch (name) {
            case 'logout':
                // 登出
                window.location.href = '../sys/logout.do'
                break;
            case 'seeMore':
                // 更多
                seeMoreAgency()
                break;
            case 'login':
                // 更多
                var oid = $(this).find('.mobile').data('oid')
                var userID = $(this).find('.mobile').data('userid')
                var state = $(this).find('.mobile').data('state')
                if (state === 0) {
                    $(".submit [name='userID']").val(userID)
                    $(".submit [name='mobile']").val(auth.getAcc().mobile)
                    $(".submit [name='oid']").val(oid)
                    $(".submit").submit()
                } else {
                    layer.msg("贵公司已被暂停服务，请告知公司管理人员！")
                }

                break;
            case 'applyAgency':
                // 申请机构
                $(".applyAgency").show().siblings().hide()
                break;
            case 'submitApply':
                // 申请机构-提交
                applyAgencySubmit()
                break;
            case 'reback':
                // 申请机构 - 返回
                $(".main").show().siblings().hide()
                break;
        }
    })
    getAgencyList(0)
});

// 提交申请
function applyAgencySubmit(){
    var phone = $(".phone").html()
    $.ajax({
        url:"../sys/applyCustomer.do" ,
        data:{ logonName: phone },
        type:"post" ,
        dataType:"json" ,
        success:function( data ){
            var success = data["success"];
            $(".applyState_avatar").show().siblings().hide()
            if(success && success == 1){
                $(".applyState_avatar .state").html('提交成功')
                $(".applyState_avatar .tip").html('请您耐心等待销售员审核')
            }else{
                $(".applyState_avatar .state").html('提交失败')
                $(".applyState_avatar .tip").html('请退出系统，重新登录')
            }
        },
        error:function () {
            $(".applyState_avatar").show().siblings().hide()
            $(".applyState_avatar .state").html('提交失败')
            $(".applyState_avatar .tip").html('请退出系统，重新登录')
        }
    });
}

// creator: 张旭博，2018-08-31 10:47:26，更多机构
function seeMoreAgency() {
    // seeMoreList(1, true);
    $(".seeAllAgency").show().siblings().hide()
    getAgencyList(1)
    // $("#seeMore").html("正在获取更多机构......");
    // $("#more").show();
    // $("#loginCon2").hide();
    // $("#left").animate({"top": "-70px", "width": "210px"}, 300);
    // $(".content").animate({"top": "20%", "width": "90%"}, 300);
    // $(".loading").css("left", "50%");
}

// creator: 张旭博，2019-07-10 16:30:43，获取机构列表
function getAgencyList(currentPage) {
    if(typeof acc === 'object' && typeof acc.id !== 'undefined') {
        console.log(acc.id)
    }
    if(currentPage>0) {
        var w = parseInt($(window).width() * 0.9 / 250);
        var h = parseInt($(window).height() * 0.35 / 41);
        perNum = w * h;
    } else {
        perNum =10
    }
    $.ajax({
        url: "../sys/getOrgListPageInfo.do",
        data: {
            currentPageNo: currentPage,
            pageSize: perNum
        },
        dataType: "json",
        type: "post",
        beforeSend: function () {
            $(".loading").show();
        },
        success: function (res) {
            $(".loading").hide();
            var agencyList  = res.data
            if (currentPage<1) {
                var str = ''
                // 列表最多展示9个 （不包括更多）
                var n = 9
                if (agencyList.length < 9) {
                    n = agencyList.length
                }
                for (var i = 0; i < n; i++) {
                    str +=  '<li type="btn" data-name="login">' +
                            ( agencyList[i].msgCount === 0 ? '' : '<span class="message_corner">(' + agencyList[i].msgCount + ')</span>' ) +
                            '    <span class="mobile" data-userid="' + agencyList[i].userID + '" data-oid="' + agencyList[i].oid + '" data-state="'+agencyList[i].state+'">' + agencyList[i].oidName + '</span>' +
                            '</li>';
                }
                if (agencyList.length > 9) {
                    str += '<li type="btn" data-name="seeMore">更多</li>'
                }
                $(".agency_simple_avatar .agencyList").html(str)
            } else {
                var pageInfo    = res.page
                var totalPage = pageInfo["totalPage"];

                // 设置分页
                setPage($("#ye_agency"), currentPage, totalPage, "login", "{}");
                var str = ''
                for (var i in agencyList) {
                    str +=  '<li type="btn" data-name="login">' +
                            ( agencyList[i].msgCount === 0 ? '' : '<span class="message_corner">(' + agencyList[i].msgCount + ')</span>' ) +
                            '    <span class="mobile" data-userid="' + agencyList[i].userID + '" data-oid="' + agencyList[i].oid + '" data-state="'+agencyList[i].state+'">' + agencyList[i].oidName + '</span>' +
                            '</li>';
                }
                $(".agency_all_avatar .agencyList_big").html(str)
            }
        },
        error: function () {
            $(".loading").hide();
            $(".seeAllAgency .error").html("获取机构失败！");
        }
    });
}