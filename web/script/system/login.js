var verifyCode;
$(function () {
    const passportHost = 'passport.btransmission.com'
    const tgCode = 'weChat';
    const appId = 'wx6344f9634107c46e';
    let qs = window.Qs
    console.log('location', location, location.pathname, location.search,location.hash)
    let params = qs.parse(location.search, { ignoreQueryPrefix: true })
    console.log('qs', params, params.code, params.state, params.state == auth.getToken())
    if(typeof params.code !== 'undefined' && typeof params.state !== 'undefined' && params.state == auth.getToken()) {
        $.ajax({
            url : $.webRoot + '/tp/mpCodeLogin.do',
            type: 'POST',
            data: {
                appId: appId,
                tgCode: tgCode,
                code: params.code
            },
            success:function ( data ) {
                console.log(data)
                if(data.success > 0) {
                    console.log('登录成功！')
                    location.href = $.webRoot+'/sys/goBack.do'
                } else {
                    let error = data.error
                    console.log('登录失败！error msg', error)
                    // console.log('startPage check token', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
                    switch (error.code) {
                        case '2': //超过79天，需要短信验证激活
                            console.log('需要短信验证激活')
                            break
                        case '3': //需要注册，微信号未与通用框架账号绑定
                            console.log('微信号未与通用框架账号绑定')
                            layer.msg('操作失败，该微信未绑定账号！')
                            setTimeout(function(){
                                location.href  = $.webRoot+'/sys/login.do'
                            },3000)
                            break
                        case '4': //可能是授权或者code问题
                            console.log('授权问题，建议更换其他登录方式')
                            layer.msg('授权问题，建议更换其他登录方式！')
                            setTimeout(function(){
                                location.href  = $.webRoot+'/sys/login.do'
                            },3000)
                            break
                        case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
                            console.log('服务器问题，比如wonderss服务器访问微信服务器异常，建议更换其他登录方式')
                            layer.msg('服务器问题，比如wonderss服务器访问微信服务器异常，建议更换其他登录方式')
                            setTimeout(function(){
                                location.href  = $.webRoot+'/sys/login.do'
                            },3000)
                            break
                    }
                }
            }
        });
    }
    if(location.pathname.includes('/sys/logout')) {
        // localStorage.removeItem('token')
        localStorage.clear()
    }
    // sessionStorage.clear()
    // 回到切换机构页面以后悬浮窗的状态重置
    localStorage.bnsShow = true; // 每次登陆浮窗都显示
    localStorage.removeItem('floatMenu') // 清除悬浮窗的url

    // 获取系统缓存手机号并赋值，清除除手机号以外的其他cookie
    let cookies = $.cookie()
    let mobiles = {}
    for(var id in cookies) {
        if(id === 'mobiles') {
            mobiles = JSON.parse(cookies[id])
            updatePhoneList(mobiles)
        } else {
            $.removeCookie(id,{path : '/'})
        }
    }
    if ($(".page-new").is(":visible")) {
        auth.getGuestToken()
    }

    // × 点击事件
    $(".clearInput").on({
        "mousedown": function () {
            $(this).prev().val("");
            updatePhoneList(mobiles)
        },
        "mouseup": function () {
            $(this).prev().focus();
        }
    });

    // 账号下拉列表点击其他地方收回
    $(document).click(function () {
        $(".input_choose_list").slideUp('fast');
    });

    $(document).on("keydown",function (event) {
        var e = event || window.event || arguments.callee.caller.arguments[0];
        if(e && e.keyCode ===13 ){
            if ($("#usePassword").is(":visible")) {
                loginBtn()
            }
        }
    });

    // 账号输入事件，搜索功能
    $("#phone").on('input click', 'input', function () {
        var newList = []
        var val = $(this).val()
        for (var i in mobiles) {
            if (mobiles[i].indexOf(val) !== -1) {
                newList.push(mobiles[i])
            }
        }
        if (newList.length > 0) {
            $(".input_choose_list").slideDown('fast')
        } else {
            $(".input_choose_list").slideUp('fast')
        }
        updatePhoneList(newList)
        return false
    })

    // 账号下拉列表点击事件，选择账号
    $(".input_choose_list").on('click', '.input_choose_item', function () {
        $("#phone input").val($(this).html())
        $(".input_choose_list").slideUp('fast');
        return false
    })

    // 各个按钮点击事件
    $(".login_login_avatar").on("click", "[type='btn']",  function () {
        var name = $(this).data('name')
        switch (name) {
            case 'password':
                // 使用手机账号密码登录
                $("#usePassword").show().siblings().hide();
                $("#picVerification").hide().find("input").val("")
                break;
            case 'code':
                // 使用短信验证码登录
                $("#useCode").show().siblings().hide();
                $("#useCode input").val('')
                $("#useCode input[name='phone']").val( $("#usePassword input[name='logonName']").val())
                auth.getGuestToken()
                break;
            case 'login-wx':
                var id = $(this).parents(".login_avatar").attr("id")
                $("#loginWx").show().siblings().hide();
                $("#loginWx [data-name='back']").attr("to", id)
                auth.getGuestToken()
                console.log(location.protocol)
                console.log(location.protocol+'//'+passportHost+'/'+$.webRoot.substring($.webRoot.indexOf('://')+2)+'/sys/login.do')
                var obj = new WxLogin({
                    self_redirect:false,
                    id:"wxLogin",
                    appid: appId,
                    scope: "snsapi_login",
                    redirect_uri: encodeURIComponent(location.protocol+'//'+passportHost+'/'+$.webRoot.substring($.webRoot.indexOf('://')+2)+'/sys/login.do'),
                    state: auth.getToken(),
                    style: "",
                    href: `${$.webRoot}/css/thirdPlatform/iframeLogin.css`,
                });
                console.log('WxLogin', obj)
                break;
            case 'register-choose':
                var id = $(this).parents(".login_avatar").attr("id")
                $("#register-choose").show().siblings().hide();
                $("#register-choose [data-name='back']").attr("to", id)
                break
            case 'back':
                var to = $(this).attr("to")
                $("#" + to).show().siblings().hide();
                if(to === 'usePassword') {
                    console.log('clean token')
                    auth.cleanToken()
                }
                break
            case 'next-register':
                // 注册-下一步
                // 暂时先不做
                break
            case 'login_password':
                // 账号密码登录 -  登录按钮
                setLoginNum() // 计算账号登录次数（使用本地存储，未调用接口和使用后台参数）
                loginBtn();
                break;
            case 'login_code':
                // 短信验证码登录 - 登录按钮
                checkCode()
                break
            case 'getCode':
                // 获取验证码按钮
                var phone = $(this).parents(".form").find("[name='phone']").val()
                if (!testMobile(phone)){
                    $(this).parents(".form").find(".error").html('请输入正确的手机号')
                    return false
                } else {
                    $(this).parents(".form").find(".error").html('')
                    countdown(60, $(this))
                    getCode(phone, $(this))
                }
                break
            case 'login_setPassword':
                // 设置完成登录按钮
                setPasswordOk()
                break
        }
    })
    $(".SMSVerification").on("click", "[type='btn']",  function () {
        var name = $(this).data('name')
        switch (name) {
            case 'login_code_check':
                // 短信验证码登录 - 登录按钮
                var phone    = $(".SMSVerification .SMS_phone").html();
                var code    = $(".SMSVerification [name='code']").val();
                if ($.trim(code) === "") {
                    $(".SMSVerification .error").html('您输入的验证码有误！');
                    return false;
                } else {
                    $(".SMSVerification .error").html('');
                    $.ajax({
                        url : $.webRoot + "/auth/verificationCodeAndLogin.do",
                        type: 'POST',
                        data: {
                            mobile: phone,
                            code: code
                        },
                        success:function ( res ) {
                            if (res) {
                                window.location.href = $.webRoot + '/sys/codeLogin.do'
                            } else {
                                $(".SMSVerification .error").html('您输入的验证码有误！');
                            }
                        }
                    });
                }
                break
            case 'getCode':
                // 获取验证码按钮
                countdown(60, $(this))
                var phone = $(this).parents(".form").find(".SMS_phone").html()
                getCode(phone, $(this))
                break
            case 'quit':
                window.location.href = $.webRoot + '/sys/login.do'
                break
        }
    })
    verifyCode   = new GVerify({
        id: 'checkCode',
        width: '90',
        height: '30'
    });
    var loginTry = localStorage.getItem("loginTry")
    if (loginTry) {
        loginTry = JSON.parse(loginTry)
        $("#usePassword [name='logonName']").val(loginTry.mobile)
        if (loginTry.num > 9) {
            $("#picVerification").show()
        } else {
            $("#picVerification").hide()
        }
    }

});

!(function(window, document) {
    var size = 4;//设置验证码长度
    function GVerify(options) { //创建一个图形验证码对象，接收options对象为参数
        this.options = { //默认options参数值
            id: "", //容器Id
            canvasId: "verifyCanvas", //canvas的ID
            width: "100", //默认canvas宽度
            height: "30", //默认canvas高度
            type: "blend", //图形验证码默认类型blend:数字字母混合类型、number:纯数字、letter:纯字母
            code: "",
        }
        if(Object.prototype.toString.call(options) == "[object Object]"){//判断传入参数类型
            for(var i in options) { //根据传入的参数，修改默认参数值
                this.options[i] = options[i];
            }
        }else{
            this.options.id = options;
        }

        this.options.numArr = "0,1,2,3,4,5,6,7,8,9".split(",");
        this.options.letterArr = getAllLetter();

        this._init();
        this.refresh();
    }

    GVerify.prototype = {
        /**版本号**/
        version: '1.0.0',

        /**初始化方法**/
        _init: function() {
            var con = document.getElementById(this.options.id);
            var canvas = document.createElement("canvas");
            this.options.width = con.offsetWidth > 0 ? con.offsetWidth : "100";
            this.options.height = con.offsetHeight > 0 ? con.offsetHeight : "30";
            canvas.id = this.options.canvasId;
            canvas.width = this.options.width;
            canvas.height = this.options.height;
            canvas.style.cursor = "pointer";
            canvas.innerHTML = "您的浏览器版本不支持canvas";
            con.appendChild(canvas);
            var parent = this;
            canvas.onclick = function(){
                parent.refresh();
            }
        },

        /**生成验证码**/
        refresh: function() {
            this.options.code = "";
            var canvas = document.getElementById(this.options.canvasId);
            if(canvas.getContext) {
                var ctx = canvas.getContext('2d');
            }else{
                return;
            }

            ctx.textBaseline = "middle";

            ctx.fillStyle = "rgb(255,255,255)";
            ctx.fillRect(0, 0, this.options.width, this.options.height);

            if(this.options.type == "blend") { //判断验证码类型
                var txtArr = this.options.numArr.concat(this.options.letterArr);
            } else if(this.options.type == "number") {
                var txtArr = this.options.numArr;
            } else {
                var txtArr = this.options.letterArr;
            }

            for(var i = 1; i <=size; i++) {
                var txt = txtArr[randomNum(0, txtArr.length)];
                this.options.code += txt;
                ctx.font = randomNum(this.options.height/2, this.options.height) + 'px SimHei'; //随机生成字体大小
                ctx.fillStyle = randomColor(50, 160); //随机生成字体颜色
                ctx.shadowOffsetX = randomNum(-3, 3);
                ctx.shadowOffsetY = randomNum(-3, 3);
                ctx.shadowBlur = randomNum(-3, 3);
                ctx.shadowColor = "rgba(0, 0, 0, 0.3)";
                var x = this.options.width / (size+1) * i;
                var y = this.options.height / 2;
                var deg = randomNum(-30, 30);
                /**设置旋转角度和坐标原点**/
                ctx.translate(x, y);
                ctx.rotate(deg * Math.PI / 180);
                ctx.fillText(txt, 0, 0);
                /**恢复旋转角度和坐标原点**/
                ctx.rotate(-deg * Math.PI / 180);
                ctx.translate(-x, -y);
            }
            /**绘制干扰线**/
            for(var i = 0; i < 4; i++) {
                ctx.strokeStyle = randomColor(40, 180);
                ctx.beginPath();
                ctx.moveTo(randomNum(0, this.options.width), randomNum(0, this.options.height));
                ctx.lineTo(randomNum(0, this.options.width), randomNum(0, this.options.height));
                ctx.stroke();
            }
            /**绘制干扰点**/
            for(var i = 0; i < this.options.width/4; i++) {
                ctx.fillStyle = randomColor(0, 255);
                ctx.beginPath();
                ctx.arc(randomNum(0, this.options.width), randomNum(0, this.options.height), 1, 0, 2 * Math.PI);
                ctx.fill();
            }
        },

        /**验证验证码**/
        validate: function(code){
            var code = code.toLowerCase();
            var v_code = this.options.code.toLowerCase();
            if(code == v_code){
                return true;
            }else{
                this.refresh();
                return false;
            }
        }
    }
    /**生成字母数组**/
    function getAllLetter() {
        var letterStr = "a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z";
        return letterStr.split(",");
    }
    /**生成一个随机数**/
    function randomNum(min, max) {
        return Math.floor(Math.random() * (max - min) + min);
    }
    /**生成一个随机色**/
    function randomColor(min, max) {
        var r = randomNum(min, max);
        var g = randomNum(min, max);
        var b = randomNum(min, max);
        return "rgb(" + r + "," + g + "," + b + ")";
    }
    window.GVerify = GVerify;
})(window, document);

// creator: 张旭博，2019-07-04 10:15:56，验证键盘回车登录
// function inputKeydown(e){
//     var keynum = 0;
//     if(e === undefined)
//         e = window.event;
//     if (typeof(e.keyCode) != 'undefined') {// IE/Chrome
//         keynum = e.keyCode;
//     } else if (e.which) {// Netscape/Firefox/Opera
//         keynum = e.which;
//     }
//     switch (keynum) {
//         case 13://wyu: Enter
//             if($.trim($('#logonName').val()).length==0){
//                 $('#logonName').get(0).focus();
//                 return false;
//             } else if($.trim($('#logonPwd').val()).length==0){
//                 $('#logonPwd').get(0).focus();
//                 return false;
//             } else {
//                 return loginBtn();
//             }
//             break;
//     }
// }

// creator: 张旭博，2019-07-04 08:42:43，更新用户账号列表
function updatePhoneList(phoneList) {
    var str = ''
    for (var i in phoneList) {
        str +=  '<div class="input_choose_item">' +phoneList[i]+ '</div>'
    }
    $(".input_choose_list").html(str)
}

// creator: 张旭博，2019-07-03 14:29:54，获取验证码
function getCode(phone, selector) {
    $.ajax({
        url : $.webRoot + "/auth/sendMessageVerificationCode.do" ,
        type: 'POST',
        data: {
            phone : phone
        },
        success:function ( res ) {
            var success = res.success
            var data = res.data
            if (Number(success) === 1) {
                switch (Number(data)) {
                    case 1:
                        selector.parents(".form").find(".error").html('')
                        break;
                    case 2:
                    case 3:
                    case 4:
                        selector.parents(".form").find(".error").html('请输入正确的手机号')
                        break;
                    default:
                        selector.parents(".form").find(".error").html('')

                }
            }
        }
    });
}

// creator: 张旭博，2019-07-03 14:40:18，倒计时
function countdown(seconds, obj){
    if (seconds > 1){
        seconds--;
        obj.html(seconds+"秒后可重新获取 ").prop("disabled", true);//禁用按钮
        // 定时1秒调用一次
        setTimeout(function(){
            countdown(seconds,obj);
        },1000);
    }else{
        obj.html("获取验证码").prop("disabled", false);//启用按钮
    }
}

// creator: 张旭博，2019-07-03 15:28:04，点击验证码登录的登录按钮 （检查手机号和验证码是否正确）
function checkCode() {
    var phone   = $("#useCode [name='phone']").val();
    var code    = $("#useCode [name='code']").val();
    if ($.trim(phone) === ""){
        $("#useCode .error").html('请输入正确的手机号');
        return false;
    } else if ($.trim(code) === "") {
        $("#useCode .error").html('您输入的验证码有误！');
        return false;
    } else {
        $("#useCode .error").html('');
        $.ajax({
            url : $.webRoot + "/sendMessage/checkVerificationCode.do",
            type: 'POST',
            data: {
                phone: phone,
                verificationCode: code
            },
            success:function ( res ) {
                if (res) {
                    $("#changePassword").show().siblings().hide();
                    $("#changePassword [name='logonName']").val(phone);
                    $("#changePassword").data("code", code)
                } else {
                    $("#useCode .error").html('您输入的验证码有误！');
                }
            }
        });
    }
}

// creator: 张旭博，2019-07-03 18:10:42，设置新密码后登录
function setPasswordOk() {
    var logonPwd   = $("#changePassword [name='logonPwd']").val();
    var logonPwd2    = $("#changePassword [name='logonPwd2']").val();
    if (logonPwd !== logonPwd2) {
        $("#changePassword .error").html('两次输入的密码不一致，请重新设置！')
    } else {
        if (isPassword(logonPwd)) {
            $("#changePassword .error").html('')
            $('.login_btn').prop('disabled', true)
            var phone = $("#changePassword [name='logonName']").val()
            var data = {
                phone: phone,
                password : logonPwd,
                code: $("#changePassword").data("code")
            }
            $.ajax({
                url : "../auth/resetLoginPassWord.do" ,
                type: 'POST',
                data: data,
                success:function ( res ) {
                    if (res) {
                        window.location.href = $.webRoot + '/sys/codeLogin.do'
                        $("#login_form_reset").submit()
                    } else {
                        $('.login_btn').prop('disabled', false)
                        $("#useCode .error").html('设置密码失败！');
                    }
                }
            });
        } else {
            $("#changePassword .error").html('您设置的密码过于简单，请重新设置！')
        }
    }

}

// creator: 张旭博，2019-07-04 09:18:51，校验密码：只能输入8-16个字母、数字、下划线
function isPassword(val) {
    var reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$/;
    return reg.exec(val)
}

// updater: 张旭博，2019-07-03 13:58:10，登录
function loginBtn() {
    var logonName   = $("#usePassword [name='logonName']").val();
    var logonPwd    = $("#usePassword [name='logonPwd']").val();

    if ($.trim(logonName) === "" || $.trim(logonPwd) === "") {
        $("#usePassword .error").html('请输入正确的手机号');
        return false;
    } else {
        $("#usePassword .error").html('');
    }
    if ($("#picVerification").is(":visible")) {
        var code    = $("#code_input").val();
        if ($.trim(code) === '') {
            $("#usePassword .error").html('字符输入不正确，请重新输入！');
            return false;
        } else {

            var res = verifyCode.validate($("#code_input").val());
            if(!res){
                $("#usePassword .error").html('字符输入不正确，请重新输入！');
                return false;
            }
        }
    }
    $('.login_btn').prop('disabled', true)
    $('#login_form').submit();
    if (myBrowser() === 'FF') {
        $('#logonPwd').val('');
    }
}

// creator: 张旭博，2022/1/7 9:18，检查登录次数
function setLoginNum() {
    // 判断登录次数
    var loginTry = localStorage.getItem("loginTry")
    var mobile = $("#usePassword [name='logonName']").val()

    if (!loginTry) {
        // 第一次登录
        loginTry = {
            mobile: mobile,
            num: 0
        }
    } else {
        // 已有记录
        loginTry = JSON.parse(loginTry)
        if (loginTry.mobile === mobile) {
            // 重复手机号登录
            loginTry.num++
        } else {
            loginTry = {
                mobile: mobile,
                num: 0
            }
        }
    }
    localStorage.setItem("loginTry", JSON.stringify(loginTry))
}

//判断浏览器类型
function myBrowser() {
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("OPR") > -1;
    if (isOpera) {
        return "Opera"
    }
    ; //判断是否Opera浏览器 OPR/43.0.2442.991
    //if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) { return "IE"; }; //判断是否IE浏览器
    if (userAgent.indexOf("Firefox") > -1) {
        return "FF";
    } //判断是否Firefox浏览器  Firefox/51.0
    if (userAgent.indexOf("Trident") > -1 || userAgent.indexOf("MSIE") > -1) {
        return "IE";
    } //判断是否IE浏览器  Trident/7.0; rv:11.0
    if (userAgent.indexOf("Edge") > -1) {
        return "Edge";
    } //判断是否Edge浏览器  Edge/14.14393
    if (userAgent.indexOf("Chrome") > -1) {
        return "Chrome";
    }// Chrome/56.0.2924.87
    if (userAgent.indexOf("Safari") > -1) {
        return "Safari";
    } //判断是否Safari浏览器 AppleWebKit/534.57.2 Version/5.1.7 Safari/534.57.2
}

// creator: 张旭博，2022/5/27 9:56，换一个 - 按钮
function changePicVerification () {
    $("#verifyCanvas").click()
}