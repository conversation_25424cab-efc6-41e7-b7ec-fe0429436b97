class SphdSocket{
    constructor(){
        // console.log("Websock constructor")
        // console.log($.webRoot)
        this.isConnect = false
        this.listeners = Array()
        this.errorcount = 0
        this.errorlimit = 1024
        /**
         * @deprecated：仅用于兼容旧代码，建议直接auth.getUser()
         * 另外，当前账号手机号建议用 auth.getAcc().mobile 获取，在账号密码登录后有效。
         */
        this.user=auth.getUser()
        /**
         * @deprecated：仅用于兼容旧代码，建议直接auth.getOrg()
         */
        this.org=auth.getOrg()
        /**
         * @deprecated：仅用于兼容旧代码，建议直接auth.getSessionid()
         */
        this.sessionid=auth.getSessionid()
        // console.log('jsp',this.user,this.org,this.sessionid)
        this.connect()
        console.log("Endof SphdSocket constructor")
    }
    connect(force=false) {
        var that=this
        setTimeout(function(){
            that.socket = new SockJS('../socket')
            that.stompClient = Stomp.over(that.socket)
            that.stompClient.debug = null //wyu：不输出调试信息；如果需要输出stopjs调试信息的时候注释掉本行即可。
            // console.log('before call connect')
            that.stompClient.connect({
                token: auth.getToken()
            }, function (frame) {
                // console.log('connected: ' + frame)
                that.isConnect = true
                that.errorcount = 0
                that.resubscribe(force)
            }, function () {
                that.isConnect=false
                if( that.errorcount++ < that.errorlimit) {
                    // console.log('connect error,retry connect.')
                    that.stompClient.disconnect(function(){
                        that.socket.close()
                        setTimeout(function(){
                            that.reconnect()
                            console.log('Web disconnected, retry connect.',that.errorcount)
                        }, 100*Math.pow(that.errorcount,2))
                    })
                } else {
                    console.log('无法连接服务器，本窗口将被关闭！')
                }
            })
        },1)
        // console.log('after called connect')
    }
    reconnect() {
        var that=this
        that.socket = new SockJS('../socket')
        that.stompClient = Stomp.over(that.socket)
        that.stompClient.debug = null
        that.connect(true)
    }
    resubscribe(force=false) {
        var that = this
        //订阅服务器发送来的消息
        that.listeners.forEach(function (listener) {
            if (!listener.subscribed || force) {
                listener.subscribed = true
                var path= '/push/'
                switch(listener.type){
                    case 'acc':
                        path+=auth.getAccId()+'/'
                        break
                    case 'user':
                        path+=auth.getByName('userID')+'/'
                        break
                    case 'session':
                        path+=auth.getSessionid()+'/'
                        break
                    case 'custom':
                        if(typeof(listener.param)!='undefined' && listener.param.toString().length>0) {
                            path += listener.param + '/'
                        }
                    // default: //boardcast
                }
                path += listener.url
                // console.log(path)
                listener.subscribed = that.stompClient.subscribe(path,
                    function (event) {
                        if (typeof(listener.callback) == "function") {
                            // console.log('subscribe lister:'+event)
                            listener.callback(event.body)
                        }
                    },
                    {
                        token: auth.getToken()
                    })
                    // function () {
                    //     console.log('resubscribe failure')
                    //     if (typeof(listener.errorcallback) == "function") {
                    //         listener.errorcallback()
                    //     }
                    // })
            }
        })
    }

    subscribe(url, callback, errorcallback, type = 'session', param = '') { //wyu：type=广播：boardcast,用户：user,会话：session,自定义：custom(param 可以穿oid之类)
        var that = this
        var listener = {
            uuid: that.uuid(),
            url: url,
            param: param,
            type: type,
            callback: callback,
            errorcallback: errorcallback,
            subscribed: false
        }
        // console.log('param', listener.param)
        this.listeners.push(listener)
        var interval = setInterval(function () {
            if (that.isConnect) {
                that.resubscribe()
                clearInterval(interval)
            }
        }, 1)
        return listener.uuid
    }

    unsubscribe(uuid) {
        // console.log(this.listeners)
        // console.log(uuid)
        var that = this
        this.listeners.forEach(function (listener, index) {
            // console.log(listener.uuid)
            if (listener.uuid == uuid) {
                // console.log('==',listener.subscribed, listener.subscribed.id)
                if (listener.subscribed && listener.subscribed.id) {
                    // console.log(index,listener)
                    that.stompClient.unsubscribe(listener.subscribed.id)
                }
                that.listeners.splice(index, 1)
            }
        })
        // console.log(this.listeners)
    }

    guid() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)
            return v.toString(16)
        })
    }

    uuid() {
        return this.guid().replace(/-/g,'')
    }
    //发送信息
    send(url, message) {
        console.log('sphd.js send old', '/send/' + url, JSON.stringify(message))
        var that = this
        var interval=setInterval(function(){
            if(that.isConnect){
                that.stompClient.send("/send/"+url, {
                    token: auth.getToken()
                }, JSON.stringify(message))
                clearInterval(interval)
            }
        },1)
    }
}
window.sphdSocket = new SphdSocket()
console.log("SphdSocket constructed.")
$(window).on('unload', function () {
    if (sphdSocket.stompClient != null) {
        // console.log('sphdSocket.isConnect:',sphdSocket.isConnect)
        if(sphdSocket.isConnect) {
            sphdSocket.stompClient.disconnect(function(){
                // console.log("StompClient Disconnected")
            })
        }
        sphdSocket.socket.close()
        sphdSocket.stompClient=null
        sphdSocket.socket=null
        sphdSocket.isConnect=false
        sphdSocket=null
    }
    // console.log("Websock Disconnected")
})

//测试长连接
/*$(function(){
    $(".bounceFloating iframe").attr('src', $.webRoot+"/vue/message/dist/index.html")//wyu：移动到/script/common/common.js
    // //wyu：调试代码，订阅listenner测试
    // sphdSocket.subscribe('check',function(data){console.log('Socket recieved OK:'+data)},function(){console.log('Socket check Error:')},'boardcast')//wyu：广播，仅用于测试长连接
    // sphdSocket.subscribe('checkUser',function(data){console.log('Socket recieved web OK:'+data)},function(){console.log('Socket check Error:')},'user')
    // // sphdSocket.subscribe('checkSession',function(data){console.log('Socket recieved OK:'+data)},function(){console.log('Socket check Error:')},'session')
    // sphdSocket.subscribe('checkSession',function(data){console.log('Socket recieved by web OK:'+data)})
    // // sphdSocket.subscribe('checkCustom',function(data){console.log('Socket recieved OK:'+data)},function(){console.log('Socket check Error:')},'custom','18622650000')//wyu：例子，只接受手机号为18622650000用户发的消息
    // sphdSocket.subscribe('checkCustom',function(data){console.log('Socket recieved OK: '+data)},null,'custom','18622650000')//wyu：例子，只接受手机号为18622650000用户发的消息
    // sphdSocket.subscribe('customDelay',function(data){console.log('Socket recieved CustomDelay OK! message: '+data)},null,'custom',sphdSocket.org.id)//wyu：例子，只接受同机构用户发的消息
    // sphdSocket.subscribe('customDelayCall',function(data){console.log('Socket recieved OK: '+data)},null,'custom','18622650000')//wyu：例子，只接受手机号为18622650000用户发的消息
    // var callbackfun = function(data){
    //     console.log('Testing Socket recieved OK: '+data)
    //     sphdSocket.unsubscribe(subscribeid)
    // }
    // var subscribeid=sphdSocket.subscribe('customDelayCall',callbackfun,null,'custom','18622650000')//wyu：例子，只接受手机号为18622650000用户发的消息
    // //wyu：调试代码，发送消息测试
    // sphdSocket.send('check','Hello world! boardcast')
    // sphdSocket.send('checkUser',{'message':'Hello world! web user','user':sphdSocket.user.userID})
    // sphdSocket.send('checkSession',{'message':'Hello world! session send by web','session':$.cookie('sessionid')})
    // sphdSocket.send('checkCustom',{'message':'Hello world! custom ','user':sphdSocket.user.userID})
    // sphdSocket.send('customDelay',{'message':'Hello world! custom ','user':sphdSocket.org.id})
    // sphdSocket.send('customDelayCall',{'message':'Hello world! custom ','user':sphdSocket.user.userID})
})*/