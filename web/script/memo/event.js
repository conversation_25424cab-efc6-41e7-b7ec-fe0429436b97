// creator: hxz  2018-07-18  初始化 三级弹框 - fixed2
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#newEvent"));
bounce_Fixed3.show($("#addRole"));
bounce_Fixed2.cancel();
bounce_Fixed3.cancel();
$(function () {
    // 设置时间区间
    setTimeLine()
    // 设置操作浮窗的显隐事件
    setHandleFloat()

    getShareableUsers()

    $(".autoFill").on("input focus keyup", function () {
        var max = $(this).attr("max")
        var curLength = $(this).val().length;
        if (curLength > max) {
            curLength = max
            var cutValue = $(this).val().substring(0,max);
            $(this).val(cutValue);
        }
        $(this).parent().siblings(".textMax").text( curLength +'/' + max );
        var open = $("#newEvent [name='title']").data('open');
        if (open == '1'){
            if ($(this).data("state") === 0) {
                $("#newEvent [name='title']").val($(this).val().substring(0,10));
            }
            if (curLength > 10) {
                $(this).data("state", 1)
            }
        }
    })

    // × 点击事件
    $(".clearInput").on({
        "mousedown": function () {
            $(this).prev().val("");
            updatePhoneList(mobiles)
        },
        "mouseup": function () {
            $(this).prev().focus();
        }
    });

    // 表格每一行的直系按钮点击事件
    $(".applyList").on('click', '.handleBtn>span', function () {
        var name = $(this).data("name")
        var id = $(".historyList").is(":visible") ?$(this).parents("tr").data("sid") : $(this).parents("tr").data("id")
        switch (name) {
            case 'share':
                // 按钮
                $(".handleBtn").hide();
                $(".handleBtn span").removeClass("active");
                $(".share_avatar").hide();
                $(".do_avatar").hide();

                $(this).parents('.handleBtn').show();
                $(this).addClass("active").siblings("span").removeClass("active")
                $(".share_avatar").show().css({
                    "left": $(this).offset().left,
                    "top": $(this).offset().top + 30
                })
                $(".sharePerson").data("scheduleId", id)
                getShareableUsers(id)
                return false;
                break;
            case 'collect':
                // 收藏
                bounce.show($("#collect"))
                updateColFavorites()
                $("#collect .bounce_title").html("收藏")
                var that = $(this)
                $("#collect .sureBtn").unbind().on("click", function () {
                    sureCollect(id, that)
                })
                bounce.everyTime('0.5s','collect',function(){
                    var active = $("#collect .ty-treeItemActive")
                    $("#sureCollectBtn").prop("disabled", active.find("i").eq(0).attr("class") !== 'ty-fa'  || active.length === 0)
                });
                break;
            case 'more':
                // 更多操作
                $(".handleBtn").hide();
                $(".handleBtn span").removeClass("active");
                $(".share_avatar").hide();
                $(".do_avatar").hide();

                $(this).parents('.handleBtn').show();
                $(this).addClass("active").siblings("span").removeClass("active")
                $(this).next('.do_avatar').show();
                break;
            case 'editFavoriteFolder':
                bounce_Fixed.show($("#editFavoriteFolder"));
                $("#editFavoriteFolder input").val("")
                $("#editFavoriteFolder").data("id", id)
                break;
            case 'deleteFavoriteFolder':
                bounce_Fixed.show($("#fixed_tip"))
                $("#fixed_tip .text").html("确定删除该收藏夹吗？")
                var that = $(this)
                $("#fixed_tip .sureBtn").unbind().on("click", function () {
                    $.ajax({
                        url: '../scheduleFavorite/deleteScheduleFavorite.do',
                        data: {
                            id: id
                        },
                        success: function (res) {
                            if (res === 1) {
                                layer.msg("操作成功")
                                bounce_Fixed.cancel()
                                that.parents("tr").remove()
                            } else if (res === 2) {
                                bounce_Fixed2.show($("#fixed2_confirm"))
                                $("#fixed2_confirm .text").html('<p>操作失败！</p><p>因为该收藏夹下有备忘。</p>')
                                $("#fixed2_confirm .iknowBtn").unbind().on("click", function () {
                                    bounce_Fixed2.cancel()
                                    bounce_Fixed.cancel()
                                })
                            }
                        }
                    })
                })
                break
        }

    })

    $("#newEvent").on("click","[name='freqType']",function () {
        if($(this).val() != 1){
            var now = new Date().getTime() + 1800000;
            var getHours = new Date(now).getHours();
            if(getHours < 10){
                getHours = '0' + getHours;
            }
            var dateStr = getHours + ':30';
            $("#activeStartDate").val(new Date(now).format('yyyy/MM/dd'));
            $("#activeStartTime").val(dateStr);
        }
    })

    // 首页表头各个按钮点击事件
    $(".eventHandle").on("click","[other]",function () {
        var name = $(this).data("name");
        $(".mainBtnGroup").hide();
        $(".otherBtn").show();
        switch (name) {
            // 我的分享
            case 'eventShare':
                $(".shareBtnGroup").show().siblings().hide();
                $(".applyList .myShare").show().siblings().hide()
                getShareList(0, 1 ,20);
                break;
            // 查看备忘事项
            case 'eventMemo':
                $(".memoBtnGroup").show().siblings().hide();
                $(".applyList .memoMatters").data("viewType", '1');
                $(".applyList .memoMatters").show().siblings().hide();
                getMemoMattersList(1, 20);
                break;
            // 查看历史日程
            case 'seeFutureOrHistory':
                $(".historyBtnGroup").show().siblings().hide();
                $(".applyList .historyList").show().siblings().hide()
                getHistoryList(1, 20);
                break;
            // 搜索关键词 - 确定
            case 'searchByKey':
                $(".searchBtn").show().siblings().hide();
                $(".applyList .repeatSchedule").show().siblings().hide();
                getKeyWordList(1, 20);
                break;
        }
    })

    // 其他表头各个按钮点击事件
    $(".otherBtn").on('click',"[other]", function(){
        var name = $(this).data("name");
        switch (name) {
            case 'goback': //返回
                $(".otherBtn").hide()
                $(".folderRecordList table").remove()
                $(".memoRecord tbody").html("")
                $(".memoFavorites .memoRecord").hide()
                $(".memoFavorites .folderRecordList").show()
                getMainEventList(1, 20);
                break;
            case 'newMemo': //新增备忘事项
                $("#newEvent .new_title").html('新增备忘');
                $("#newEvent .friendlyTip").hide();
                $("#newEvent .uploadImgTip").html('如有必要请在此处上传照片');
                $("#newEvent .shareChooseList").html("")
                bounce_Fixed2.show($("#newEvent"));
                $("#newEvent").find("#uploadImgSect").html("")
                initUpload();
                $(".textMax").html('0/1000')
                document.getElementById('form_newEvent').reset()
                $("#newEvent [name='notify']").prop('disabled', true);
                $("#newEvent [name='notify']").eq(1).prop("checked",true)
                $("#newEvent .trans_notify").hide();
                $("#newEvent [name='title']").data('open', '1');
                $("#newEvent [name='description']").data('state', 0);
                $(".uploadImgAdd").children(".pictureWall").remove();
                bounce.everyTime('0.5s','newEvent',function(){
                    testNewEvent()
                });
                break;
            case 'memoSearchByKey': //备忘事项搜索
                var keyword = $(this).prev().val()
                getMemoKeyWordList(1, 20, keyword);
                $(".searchBtn").show().siblings().hide();
                $(".searchBtn").data("name", $(this).prev().attr("name"))
                $(".applyList .memoMatters").data("viewType", '2');
                $(".applyList .memoMatters").show().siblings().hide();
                break;
            case 'hisSearchByKey': //历史日程搜索
                $(".searchBtn").show().siblings().hide();
                $(".applyList .historyList").show().siblings().hide();
                getHisKeyWordList(1, 20);
                break;
            case 'memoCollectors':
                updateRowFavorite({pid: 0, pname: '全部收藏夹'})
                $(".favoritesBtnGroup").show().siblings().hide();
                $(".applyList .memoFavorites").show().siblings().hide();
                $("#newFavorite").show()
                break;
            case 'backToPrev':
                if ($(".memoFavorites .memoRecord").is(":visible")) {
                    $(".memoFavorites .memoRecord").hide()
                    $(".memoFavorites .folderRecordList").show()
                    $("#newFavorite").show()
                    updateRowFavorite()
                } else {
                    if ($(".memoFavorites .folderRecordList table").length > 1) {
                        $(".memoFavorites .folderRecordList table").last().remove()
                        $(".memoFavorites .folderRecordList table").last().show()
                        updateRowFavorite()
                    } else {
                        $(".memoFavorites .folderRecordList table").remove()
                        $(".memoMatters").show().siblings().hide()
                        $(".memoBtnGroup").show().siblings().hide()
                        getMemoMattersList(1, 20);
                    }
                }
                break;
            case 'newFavorite':
                newFolderBtn('row')
                break
        }
    })

    // 二级弹窗按钮事件
    $(".bounce_Fixed").on("click",".ty-btn",function () {
        var name = $(this).data("name");
        var id = $(".applyList").data('id')
        switch (name) {
            // 不再提醒弹窗 - 确定
            case 'noRemind':
                var url = '';
                var data = {};
                var obj = $("#noRemind").data("obj")
                var source = $("#noRemind").data("type");
                if(source === 'main'){
                    url = '/schedule/scheduleNoRemind.do';
                    data.id = id;
                }else if(source === 'history') {
                    url = '/schedule/memoJobNoRemind.do';
                    data.id = $("#noRemind").data("historyId");
                }
                $.ajax({
                    url: url,
                    data: data,
                    success: function (data) {
                        bounce_Fixed.cancel()
                        var status = data.status
                        if(status === 1) {
                            layer.msg("操作成功!")
                            if(source === 'history') {
                                historyRecord($("#historyRecord").data("historyId"));
                            }
                        } else {
                            layer.msg("操作失败!")
                        }
                    }
                })
                break;
                //修改日程
            case 'updateEvent':
                updateEventSure(id);
                break;
            // 补充记录弹窗 - 确定
            case 'extraRecord':
                var historyId = $(".historyList").data("historyId");
                var supplement = $("#extraRecord .supplement").val();
                var imgs = '';
                var data = {
                    'memoJobId': historyId,
                    'description': supplement,
                    'imagePaths': ''
                }
                $(".supplyUploadImg .pictureWall").each(function () {
                    imgs += $(this).children(".filePic").data('path') + ',';
                })
                var len = imgs.length -1;
                imgs = imgs.substr(0, len);
                data.imagePaths = imgs;
                $.ajax({
                    url: '../schedule/addMemoJobSupplement.do',
                    data: data,
                    success: function (data) {
                        bounce_Fixed.cancel()
                        // 取消删除程序
                        var groupUuidObj = $("#extraRecord").data("groupUuidObj")
                        cancelFileDel(groupUuidObj)

                        var status = data.success;
                        var note = $("#extraRecord").data('obj');
                        if(status === 1) {
                            layer.msg("补充成功!");
                        } else {
                            layer.msg("补充失败!")
                        }
                    }
                })
                break;
            // 删除日程弹窗 - 确定
            case 'delEvent':
                var type = $("#delEvent").data("type");
                var obj = $("#delEvent").data('obj');
                var url = ''
                var data = {}
                if(type === 'main'){
                    url = '/schedule/deleteSchedule.do'
                    data.id = id
                }else if(type === 'history') {
                    var position = $("#delEvent").data('position'); //1=历史日程删除 2=历史记录删除
                    if(position == '1'){
                        data.id = id
                    }else{
                        data.id = $(".historyList").data('historyId');
                    }
                    url = '/schedule/deleteOneMemoJob.do'
                }else if(type === 'allHistory'){
                    url = '/schedule/deleteMemoJob.do'
                    data.id = id
                }
                $.ajax({
                    url: url,
                    data: data,
                    success: function (data) {
                        var status = data.status
                        bounce_Fixed.cancel()
                        if(status === 1) {
                            layer.msg("删除成功!")
                            if(type === 'allHistory'){
                                $("#historyRecord .historyList tbody").html('')
                                $("#delAllHistoryBtn").prop("disabled", true)
                            } else {
                                obj.parents('tr').remove();
                            }
                        } else {
                            layer.msg("删除失败!")
                        }
                    }
                });
                break;
            case 'delSupply':
                $("#delExtraRecord").data('obj', $(this));
                bounce_Fixed2.show($("#delExtraRecord"));
                break;
        }
    })

    // 三级弹窗按钮事件
    $(".bounce_Fixed2").on("click",".ty-btn",function () {
        var name = $(this).data("name");
        //var id = $(".list").data('id')
        switch (name) {
            // 新增日程弹窗 - 确定
            case 'newEvent':
                sureNewEvent();
                break;
            case 'delSupplySure':
                var obj = $("#delExtraRecord").data('obj');
                var id = obj.parents(".supplyItem").data('supplementid');
                $.ajax({
                    url: '../schedule/deleteMemoJobSupplement.do',
                    data: {
                        'supplementId': id
                    },
                    success: function (data) {
                        var status = data.success;
                        bounce_Fixed2.cancel();
                        if(status === 1) {
                            layer.msg("删除成功!");
                            obj.parents(".supplyItem").remove();
                        } else {
                            layer.msg("删除失败!");
                        }
                    }
                });
                break;
        }
    });

    // 更多按钮点击事件
    $(".applyList").on('click', '[public]', function () {
        var pub_this = $(this);
        var name = $(this).data('name')
        var id = $(this).parents('tr').data('id')
        $(".applyList").data('id', id)
        switch (name) {
            // 查看
            case 'seeEvent':
                $("#seeEvent .titleTip").html('日程查看');
                $("#seeEvent .eventInfo").show();
                $("#seeEvent .noticeData").show();
                $("#seeEvent .additional").hide();
                $(".supplyList").html("").show();
                seeEventDetail(id, true);
                break;
            // 修改
            case 'updateEvent':

                $("#delEvent .titleUpdate").html('日程修改');
                $("#delEvent .friendlyTip").html('注：未设置提醒的事件均展示于【备忘事件查询】中').show();
                updateEventIndex(id)
                break;
            // 不再提醒
            case 'noRemind':
                var freq = pub_this.data("freqType");
                $("#noRemind").data("type", 'main');
                $("#noRemind").data("obj", pub_this);
                if(freq == 1){
                    $("#noRemind .noRemindContent").html('<p><span class="ty-color-red">您确定本日程不再需要提醒吗？</span></p>');
                }else {
                    $("#noRemind .noRemindContent").html('<p>本日程改为“不再提醒”后，您依旧可查询到与本日程有关的历史记录。</p>' +
                        '<p><span class="ty-color-red">您确定本日程不再需要提醒吗？</span></p>');
                }
                bounce_Fixed.show($("#noRemind"))
                break;
            // 历史记录
            case 'historyRecord':
                $("#historyRecord").data("historyId",id);
                bounce.show($("#historyRecord"));
                historyRecord(id);
                break;
            // 删除
            case 'delEvent':
                $("#delEvent").data('obj', pub_this);
                $("#delEvent").data('position', '1'); //历史日程删除
                var freq = $(this).data("freq");
                var type = $(this).data("type");
                bounce_Fixed.show($("#delEvent"));
                $("#delEvent").data("type", type);
                if(freq === 1){
                    $("#delEvent .delContent").html('<p>删除后，此日程将不再显示于您的日程中，将不再给予提醒，同时 <span class="ty-color-red">此日程的所有历史记录也将同时删除。</span></p>' +
                        '<p><span class="ty-color-red">确定删除？</span></p>');
                }else {
                    $("#delEvent .delContent").html('<p><span class="ty-color-red">删除后，此日程将不再显示于您的日程中，也将不再给予提醒。 确定删除？</span></p>')
                }
                break;
            // 复制，生成新日程
            case 'copyEvent':
                var url = '', data = {};
                var nodeType = pub_this.data('type');
                $("#newEvent .titleUpdate").html('新增备忘或日程');
                $("#newEvent .friendlyTip").html('注：未设置提醒的事件均展示于【备忘事件查询】中').show();
                $("#newEvent .uploadImgTip").html('此处可上传照片');
                if (nodeType == 'main'){
                    url = '/schedule/scheduleInfo.do';
                    data = { "id": id };
                }else if(nodeType == 'history'){
                    url = '/schedule/getMemoJobById.do';
                    data = { "memoJobId": id };
                }
                $.ajax({
                    url: url,
                    data: data,
                    success: function (data) {
                        var data = data['data']['memoSchedule'];
                        var memoImage = data['memoScheduleImageHashSet'];
                        $("#newEvent .new_title").html('新增备忘或日程');
                        $("#newEvent .shareChooseList").html("")
                        $("#newEvent [name='notify']").prop('disabled', false);
                        $("#newEvent [name='title']").data('open', '0');
                        $("#newEvent [name='description']").data('state', 0);
                        $(".uploadImgAdd").children(".pictureWall").remove();
                        bounce_Fixed2.show($("#newEvent"));
                        $("#newEvent").find("#uploadImgSect").html("")
                        initUpload()

                        for(var key in data) {
                            $("#newEvent :not(':radio')[name='"+key+"']").val(data[key])
                        }
                        var notify = data.notify
                        var special = data.special
                        var activeStartDate = data.activeStartDate
                        if(activeStartDate){
                            var date = new Date(data.activeStartDate).format('yyyy/MM/dd');
                            var time = new Date(data.activeStartDate).format('hh:mm');
                        }
                        if(notify){
                            $("#newEvent [name='notify']").eq(0).click()
                        }else {
                            $("#newEvent [name='notify']").eq(1).click()
                        }
                        if(special){
                            $("#newEvent [name='special']").prop("checked", true)
                        }else {
                            $("#newEvent [name='special']").prop("checked", false)
                        }
                        $(".textMax").html(data.description.length + '/1000');
                        $("#activeStartDate").val(date)
                        $("#activeStartTime").val(time)
                        $("#seeEvent .eventInfo").show();
                        if (memoImage.length > 0) {
                            var imgStr = '';
                            for (var t=0; t< memoImage.length; t++) {
                                var path = memoImage[t].normal
                                imgStr +=
                                    '<div class="pictureWall">' +
                                    '    <span onclick="cancleThis($(this))">x</span>' +
                                    '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                                    '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                                    '</div>';
                            }
                            $("#uploadImgSect").before(imgStr);
                        }
                    }
                })
                bounce.everyTime('0.5s','newEvent',function(){
                    testNewEvent()
                });
                break;
            //修改记录
            case 'updateRecord':
                var type = pub_this.data("type");
                if (type == 'main') {
                    updateRecord(id, 1,10, 1);
                } else if(type == 'memo') {
                    updateRecord(id, 1,10, 2);
                } else if(type == 'history'){
                    var scheduleId = $(this).data("scheduleid");
                    updateRecord(scheduleId, 1,10, 1);
                }
        }
    })

    // 分享列表切换
    $(".myShare .ty-secondTab li").on("click", function () {
        var index  = $(this).index()
        $(this).addClass("ty-active").siblings().removeClass("ty-active")
        $(".myShare .kj-table").eq(index).show().siblings(".kj-table").hide()
        getShareList(index, 1, 20)
    })
    // 备忘查看和修改
    $(".memoMatters,.memoRecord").on('click', '[memo]', function () {
        var name = $(this).data('name')
        var id = $(this).parents('tr').data('id')
        $(".applyList").data('id', id)
        switch (name) {
            // 查看
            case 'seeEvent':
                $("#seeEvent .titleTip").html('查看');
                $("#seeEvent .additional").hide();
                $("#seeEvent .noticeData").hide();
                $("#seeEvent .supplyItem").hide();
                $("#seeEvent .eventInfo").hide();
                seeEventDetail(id);
                break;
            // 修改
            case 'updateEvent':
                $("#delEvent .titleUpdate").html('修改');
                $("#delEvent .friendlyTip").hide();
                updateEventIndex(id)
                break;
        }
    })
    // 备忘查看和修改
    $(".memoFavorites").on('click', '[memo]', function () {
        var name = $(this).data('name')
        var id = $(this).parents('tr').data('id')
        $(".applyList").data('id', id)
        switch (name) {
            case 'moveToOtherFavorite':
                bounce.show($("#collect"))
                updateColFavorites()
                $("#collect .bounce_title").html("转移至其他收藏夹")
                $("#collect .sureBtn").unbind().on("click", function () {
                    sureMoveToOtherFavorite(id)
                })
                break;
            case 'collectToOtherFavorite':
                bounce.show($("#collect"))
                updateColFavorites()
                $("#collect .bounce_title").html("收藏至其他收藏夹")
                $("#collect .sureBtn").unbind().on("click", function () {
                    sureCollectToOtherFavorite(id)
                })
                break;
            case 'removeFromFavorite':
                bounce_Fixed.show($("#fixed_tip"))
                $("#fixed_tip .text").html("确定仅从本收藏夹中移除吗？")
                $("#fixed_tip .sureBtn").unbind().on("click", function () {
                    sureRemoveFromFavorite(id)
                })
                break;
            case 'removeFromAllFavorites':
                bounce_Fixed.show($("#fixed_tip"))
                $("#fixed_tip .text").html("确定从全部收藏夹中移除吗？")
                $("#fixed_tip .sureBtn").unbind().on("click", function () {
                    sureRemoveFromAllFavorites(id)
                })
                break;
        }
    })
    // 历史日程按钮点击事件
    $(".historyList").on('click', '[history="his"]', function () {
        var name = $(this).data('name')
        var historyId = $(this).parents('tr').data('id')
        $(".historyList").data('historyId', historyId)
        $("#delEvent").data('position', '2'); //历史记录删除
        switch (name) {
            // 查看
            case 'seeEvent':
                var eventSource = $(this).data('source');
                $.ajax({
                    'url': '../schedule/getMemoJobById.do',
                    'data': {
                        'memoJobId': historyId
                    },
                    success: function (data) {
                        var status = data.success;
                        if(status === 1) {
                            var eventData = data.data.memoSchedule;
                            var supplyList = data.data.scheduleSupplementList;
                            eventSeeInit(eventData);
                            $("#seeEvent .titleTip").html('日程查看');
                            $("#seeEvent .additional").hide();
                            if (eventSource == 'record') {
                                $("#seeEvent .noticeData").hide();
                                $("#seeEvent .eventInfo").hide();
                            } else {
                                $("#seeEvent .noticeData").show();
                                $("#seeEvent .eventInfo").show();
                            }
                            if (supplyList.length > 0) {
                                var supplyStr = '';
                                for(var t=0;t<supplyList.length;t++){
                                    var imgItems = supplyList[t].memoScheduleSupplementImageHashSet;
                                    var imgHtml = '';
                                    if(imgItems.length > 0){
                                        for (var y in imgItems){
                                            var num = 1 + Number(y);
                                            imgHtml +=
                                                '<a path="'+imgItems[y].normal +'" onclick="imgViewer($(this))">'+ num +'</a>、';
                                        }
                                        var len = imgHtml.length - 1;
                                        imgHtml = imgHtml.substr(0,len);
                                    }
                                    supplyStr +=
                                        '<div class="supplyItem" data-supplementId="'+ supplyList[t].id +'">' +
                                        '    <div class="clear">' +
                                        '        <div class="ty-left">补充记录<span class="supplyTime" style="margin-left:10px;">'+ new Date(supplyList[t].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</span></div>' +
                                        '        <span data-name="delSupply" class="ty-right delSupply ty-btn" data-type="main">删除</span>' +
                                        '    </div>' +
                                        '    <div class="formItem">' +
                                        '        <div class="form_label text-justify">描述<span></span></div>' +
                                        '        <div class="form_content" style="min-height:50px;max-height: 200px;overflow-y: auto">'+ supplyList[t].description +'</div>' +
                                        '    </div>' +
                                        '    <div class="formItem">' +
                                        '        <div class="form_label text-justify">照片</div>' +
                                        '        <span class="form_content supply_images">' + imgHtml +
                                        '        </span>' +
                                        '    </div>' +
                                        '</div>'
                                }
                                $(".supplyList").html(supplyStr).show();
                            } else {
                                $(".supplyList").html("").hide();
                            }
                            bounce_Fixed.show($("#seeEvent"))
                        }
                    }
                })
                break;
            // 不再提醒
            case 'noRemind':
                bounce_Fixed.show($("#noRemind"))
                $("#noRemind").data("type", 'history');
                $("#noRemind").data("historyId", historyId);
                $("#noRemind").data("obj", $(this));
                $("#noRemind .noRemindContent").html('<p><span class="ty-color-red">您确定本次日程不再需要提醒吗？</span></p>')
                break;
            // 补充记录
            case 'extraRecord':
                $("#extraRecord .supplement").val('');
                $("#uploadImgSupply .pictureWall").remove();
                bounce_Fixed.show($("#extraRecord"));
                $("#extraRecord").find("#uploadImgSupply").html("")
                initUpload();
                $("#extraRecord .textMax").html('0/1000');
                bounce_Fixed.everyTime('0.5s','extraRecord',function(){
                    if($.trim($("#extraRecord .supplement").val()) !== ''){
                        $("#extraRecord .extraRecordBtn").prop("disabled",false)
                    } else {
                        $("#extraRecord .extraRecordBtn").prop("disabled",true)
                    }
                });
                break;
            // 删除
            case 'delEvent':
                bounce_Fixed.show($("#delEvent"));
                $("#delEvent").data('type', 'history');
                $("#delEvent").data('obj', $(this));
                $("#delEvent .delContent").html('<p><span class="ty-color-red">删除后，本条补充记录将不再可见。确定删除吗？</span></p>')
                break;
        }
    })
    $(".chooseAll").on("click", function () {
        if($(this).prop("checked")){
            $(".searchRange").find('input:checkbox').prop("checked",true)
        }else {
            $(".searchRange").find('input:checkbox').prop("checked",false)
        }
    })
    // 分享管理点击事件
    $(".applyList").on("click", 'li', function () {
        var id = $(this).parents("tr").data("id")
        var name = $(this).data("name")
        switch (name) {
            case 'shareManage':
                bounce.show($("#shareManage"))
                $("#shareManage").data("scheduleId", id)
                if ($(".historyList").is(":visible")) {
                    $("#shareManage").data("scheduleId", $(this).parents("tr").data("sid"))
                } else {
                    $("#shareManage").data("scheduleId", id)
                }
                getShareManage(1, 20)
                break
        }
    })
    getMainEventList(1, 20)
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
    sphdSocket.subscribe('scheduleHomepage', function (data) {mainCallBack()}, null, 'user');
    sphdSocket.subscribe('memoJobPage', function (data) {historyCallBack()}, null, 'user');

    // 收藏弹窗中的收藏夹点击逻辑(树形列表）
    $(".ty-colFolderTree").on("click", ".ty-treeItem", function () {
        var treeItemThis = $(this)
        var categoryId = $(this).data("id")
        var url = '../scheduleFavorite/getSonFavorites.do'

        // 处理逻辑
        treeItemThis.parents(".ty-colFolderTree").find(".ty-treeItem").removeClass("ty-treeItemActive")   // 取消之前选中
        treeItemThis.addClass("ty-treeItemActive"); // 设置选中
        treeItemThis.treeItemSlide('toggle')        // icon切换（顶部jQuery方法拓展）

        if (!treeItemThis.children().eq(0).hasClass('ty-fa')) {
            $.ajax({
                url: url,
                data: { id: categoryId},
                success: function (res) {
                    var data = res.data
                    if (!data) {
                        $("#mt_tip_ms").html("连接失败，请刷新重试！");
                        bounce.show($("#mtTip"));
                    } else {
                        // 渲染返回的信息
                        if (treeItemThis.children().eq(0).hasClass('fa-angle-right')) {
                            treeItemThis.next().remove()
                        } else {
                            if (data.length > 0) {
                                showTreeData(data, treeItemThis);
                            }
                        }
                    }
                }
            })
        }
    })

    // 收藏中的收藏夹点击逻辑(横向层级列表）
    $(".folderRecordList").on("click", ".ty-rowFolderItem", function () {
        var categoryId = $(this).data("id")
        var categoryName = $(this).find(".collectName").html()
        var num = $(this).parent().next().data("num")
        if ($(this).find(".fa-angle-right").length > 0 || num === 0 ){
            updateRowFavorite({pid: categoryId, pname: categoryName})
        } else {
            $(".memoRecord").show()
            $(".folderRecordList").hide()
            $("#newFavorite").hide()
            $(".memoRecord").data("categoryId", categoryId)
            $(".memoRecord").data("categoryName", categoryName)
            updateMemoFavorite()
        }
    })
});

// creator: 张旭博，2021-02-01 09:52:12，设置操作浮窗的显隐事件
function setHandleFloat() {
    $(document).on("click", function (e) {
        var targetShare = $(e.target).parents(".share_avatar").length === 0
        var targetMore = $(e.target).parents(".do_avatar").length === 0
        var targetBtn= $(e.target).parents(".handleBtn").length === 0

        if (targetShare && targetMore && targetBtn) {
            $(".handleBtn").hide();
            $(".handleBtn span").removeClass("active");
            $(".share_avatar").hide();
            $(".do_avatar").hide();
        }
    })
    $(".applyList,#historyRecord").on('mouseover', 'tbody tr', function(){
        $(this).find('.handleBtn').show();
    })
    $(".applyList,#historyRecord").on('mouseout', 'tbody tr', function(){
        var node = $(this).find('.active');
        if(node.length === 0){　　//如果node是隐藏的则显示node元素，否则隐藏
            $(this).find('.handleBtn').hide();
        }
    })
}

// creator: 侯杏哲,2017-12-04 渲染获取的文件夹数据和页面 ( 适用于主页面文件夹树和移动文件弹框的文件夹树 )
function showTreeData(data , treeItemThis) {
    // data - 获取的数据 ， treeItemThis - 点击的那个文件夹对象 ， treeObj - 点击的树对象

    // 处理子文件夹数据
    var listNoticeChild = data;
    var level = parseInt(treeItemThis.parent().parent().attr("level")) ;
    var nextLevel = level + 1 ;
    var levelStr = "";
    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">' ;
    for(var i in listNoticeChild){
        if(listNoticeChild[i].memoFavoriteHashSet.length > 0){ // 1时代表此文件夹下有子文件夹
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"]+'</span>' +
                        '</div></li>'
        }else{
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"] + '</span>' +
                        '</div></li>'
        }
    }
    levelStr += '</div>';

    if (treeItemThis.next().length === 0) {
        treeItemThis.after(levelStr);
    }

}

// creator: 李玉婷，2019-09-04 08:13:25，主页回调
function mainCallBack(){
    getMainEventListData(1, 20)
}
// creator: 李玉婷，2019-09-04 08:21:25，历史日程回调
function historyCallBack() {
    getHistoryList(1, 20);
}
// creator: 张旭博，2018-09-10 10:07:13，新增事件 - 按钮
function newEventBtn(){
    $("#newEvent .new_title").html('新增备忘或日程');
    $("#newEvent [name='title']").data('open', '1');
    $("#newEvent [name='description']").data('state', 0);
    $("#newEvent [name='notify']").prop('disabled', false);
    $("#newEvent .friendlyTip").html('注：您可到【备忘事项】中管理不需要提醒的事件。').show();
    $("#newEvent .uploadImgTip").html('此处可上传照片');
    $("#newEvent .shareChooseList").html("")
    bounce_Fixed2.show($("#newEvent"));
    $("#newEvent").find("#uploadImgSect").html("")
    initUpload();
    // $("#newEvent [name='title']").attr('placeHolder',formatTime(Date.parse(new Date()), true) + ' 事件')
    $(".textMax").html('0/1000');
    document.getElementById('form_newEvent').reset()
    $("#newEvent [name='notify']").eq(1).prop("checked",true)
    $("#newEvent .trans_notify").hide()
    $(".uploadImgAdd").children(".pictureWall").remove();
    bounce.everyTime('0.5s','newEvent',function(){
        testNewEvent()
    });
}

// creator: 张旭博，2019-03-07 15:37:10，新增事件 - 表单验证
function testNewEvent() {
    $("#activeStartDate1").hide()
    $("#activeStartDate").show()
    var state = 0;
    var special = $("#newEvent [name='special']").prop("checked")
    var notify =  $("#newEvent [name='notify']:checked").val()
    var freqType =  $("#newEvent [name='freqType']").val()
    if(special){
        $("#newEvent [name='specialInterval']").prop("disabled", false)
    }else {
        $("#newEvent [name='specialInterval']").prop("disabled", true)
        $("#newEvent [name='specialInterval']").val('')
    }
    if(freqType === '16') {
        $("#newEvent .trans16").show()
        if(special){
            $("#activeStartDate1").show()
            $("#activeStartDate").hide().val("")
        }else {
            $("#activeStartDate1").hide().val("")
            $("#activeStartDate").show()
        }
    }else {
        $("#newEvent .trans16").hide()
    }
    if(notify === '1'){
        $("#newEvent .trans_notify").show()
    }else {
        $("#newEvent .trans_notify").hide()
    }
    $("#newEvent").find("[require]:visible:not([disabled])").each(function () {
        if($.trim($(this).val()) === ''){
            if($(this).attr("name") !== 'title'){
                state++
            }
        }
    });
    var val = $("#newEvent input[name='title']").val();
    if(val.length > 50){
        var temp = val.substr(0,50);
        $("#newEvent input[name='title']").val(temp);
    }
    if(notify === '1') {
        var day = $("#activeStartDate").val()
        var day1 = $("#activeStartDate1").val()
        var date = day.split(' ')[0]
        var time = $("#activeStartTime").val()
        var specialInterval = $("#newEvent [name='specialInterval']").val()
        if(day !== '') {
            $("#newEvent .tip").show()
            var tips = ''
            switch (freqType) {
                case '1':
                    tips = '系统将于'+date+' '+time+'提醒'
                    break;
                case '4':
                    tips = '系统将从'+date+'开始，每日'+time+'给予提醒'
                    break;
                case '8':
                    tips = '系统将从'+date+'开始，每周'+('日一二三四五六'.charAt(new Date(date).getDay()))+time+'给予提醒'
                    break;
                case '16':
                    tips = '系统将从'+date+'开始，每月'+(day.substring(8,10))+'日'+time+'给予提醒'
                    break;
                case '32':
                    tips = '系统将从'+date+' '+time+'开始，每年给一次提醒'
                    break;
                default:
                    tips = ''
            }
            $("#newEvent .ty-tips").html(tips)
        }else if(day1 !== ''){
            if(specialInterval!== ''){
                $("#newEvent .tip").show()
                var lastDate = (new Date(day1.split('/')[0],day1.split('/')[1],specialInterval*(-1)+1)).getDate()
                date = day1 + '/' + lastDate
                var time = $("#activeStartTime").val()
                $("#newEvent [name='special']").data('startDate',date+' '+time + ':00')
                tips = '系统将从'+date+'日开始，每月倒数第'+specialInterval+'日'+time+'给予提醒'
                $("#newEvent .ty-tips").html(tips)
            }
        }else {
            $("#newEvent .tip").hide()
        }
    }

    if( state > 0){
        $("#sureNewEvent").prop("disabled",true)
    }else {
        $("#sureNewEvent").prop("disabled",false)
    }
}

// creator: 张旭博，2019-02-20 10:31:20，新增事件 - 确定
function sureNewEvent() {
    var schedule = {
        title: '', // 标题
        notify: false, // 是否需要通知
        freqType:'', // 次日程的频率 1 = 仅一次,4 = 每日,8 = 每周,16 =每月,32 =每年
        special: false, // 是否为特殊时间(如月末倒数),true-是,false-否）
        startDate: '', // 可以开始执行提醒的日期
        specialInterval: '', // 倒数第某天
        place: '', // 地点
        description: '', // 详细描述
        imagePaths: ''
    }
    var imagePaths = '';
    $("#newEvent [name]:visible").each(function () {
        var name = $(this).attr("name")
        var type = $(this).data("type")
        if (type === 'num') {
            schedule[name] = Number($(this).val())
        } else if (type === 'singleCheck') {
            schedule[name] = $(this).is(':checked')
        } else {
            schedule[name] = $(this).val()
        }
    })
    schedule.notify =  $("#newEvent [name='notify']:checked").val() == 1
    if(schedule.notify){
        if(schedule.special){
            schedule.startDate = $("#newEvent [name='special']").data('startDate')
        }else {
            var dateItem = $("#activeStartDate").val() + ' ' + $("#activeStartTime").val() + ':00'
            schedule.startDate = new Date(dateItem).format('yyyy/MM/dd hh:mm:ss')
        }
    }
    var len = $('.uploadImgAdd .pictureWall').length;
    if (len > 0){
        $('.uploadImgAdd .pictureWall').each(function(){
            var path = $(this).find(".filePic").data('path');
            imagePaths += path + ',';
        })
        var strLen = imagePaths.length - 1;
        imagePaths = imagePaths.substr(0,strLen);
    }
    schedule.imagePaths = imagePaths
    var shareableUserIds = []
    $("#newEvent .share_avatar_row .selected_item").each(function () {
        shareableUserIds.push($(this).data("id"))
    })
    schedule.shareableUserIds = shareableUserIds.join(",")
    $.ajax({
        url: $.webRoot+'/schedule/addSchedule.do',
        data: schedule,
        success: function (data) {
            // 取消删除程序
            var groupUuid = $("#newEvent").data("groupUuidObj")
            cancelFileDel(groupUuid)

            var status = data.status
            if (status === 1) {
                layer.msg("新增成功")

                bounce.cancel()
                bounce_Fixed.cancel()
                bounce_Fixed2.cancel()
                $(".applyList [list]:visible").each(function () {
                    var name = $(this).data("name");
                    switch (name) {
                        case 'repeatSchedule':
                            getMainEventList(1, 20);
                            break;
                        case 'memoMatters':
                            getMemoMattersList(1, 20);
                            break;
                    }
                })
            } else if(status === 2) {
                layer.msg("时间已过时，日程不能新增")
            }
        }
    })
}

// creator: 张旭博，2019-02-28 08:48:11，获得日程列表
function getMainEventList(currentPageNo, pageSize) {
    $(".repeatSchedule .list tbody").html("");
    $(".repeatSchedule").show().siblings().hide();
    $(".mainBtnGroup").show();
    getMainEventListData(currentPageNo, pageSize);
}

// creator: 李玉婷，2020-01-16 17:00:08，获取主页列表
function getMainEventListData(currentPageNo, pageSize) {
    $.ajax({
        url: '/schedule/scheduleManageList.do',
        data: {
            currentPageNo: currentPageNo,
            pageSize: pageSize
        },
        success: function (data) {
            var list = data.memoScheduleList
            var pageInfo = data.pageInfo
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"]
            setPage( $("#ye_repeatSchedule"), cur, total, "repeatSchedule") ;
            if(data.number > 0) {
                var str = getListStr(list, 2);
                $(".repeatSchedule .list tbody").html(str);
            }
        }
    })
}

// creator: 张旭博，2019-02-28 14:55:15，日程列表 - 查看按钮
function seeEventDetail(id, isMain) {
    $.ajax({
        url: '/schedule/scheduleInfo.do',
        data: { id: id },
        success: function (data) {
            var eventData = data['data']['memoSchedule'];
            $("#seeEvent .supplyList").html("").show();
            eventSeeInit(eventData, isMain);
            bounce_Fixed.show($("#seeEvent"))
        }
    })
}

// creator: 李玉婷，2019-08-21 09:19:57，日程修改
function updateEventIndex(id) {
    document.getElementById('form_updateEvent').reset();
    $.ajax({
        url: '/schedule/scheduleInfo.do',
        data: { id: id },
        success: function (data) {
            var groupUuidObj = $("#updateEvent").data("groupUuidObj")
            cancelFileDel(groupUuidObj)
            bounce.show($("#updateEvent"));
            $("#updateEvent").find("#uploadImgSect_updateEvent").html("")
            initUpload()
            var eventData = data['data']['memoSchedule'];
            var memoImage = eventData['memoScheduleImageHashSet'];
            var tip = getTips(eventData)
            var freqType = Number(eventData.freqType)
            var state = Number(eventData.state)
            $("#updateEvent .ty-tips").html(tip);
            $("#form_updateEvent [name='title']").val(eventData.title);
            $("#form_updateEvent [name='description']").val(eventData.description).parent().siblings('.textMax').html(eventData.description.length + '/1000');
            $("#form_updateEvent [name='notify'][value='" + eventData.notify + "']").prop('checked',true);
            $("#form_updateEvent [name='notifyReady']").val(eventData.notify);
            $(".updateImg").children().not("#uploadImgSect_updateEvent").remove();
            if(eventData.notify){
                $("#updateEvent .trans_notify").show();
                $("#form_updateEvent [name='freqType']").val(freqType);
                $("#activeStartDate_updateEvent").val(new Date(eventData.activeStartDate).format('yyyy/MM/dd'));
                $("#activeStartTime_updateEvent").val(new Date(eventData.activeStartDate).format('hh:mm'));
            }else{
                $("#updateEvent .trans_notify").hide();
            }
            if (memoImage.length > 0) {
                var imgStr = '';
                for (var t=0; t< memoImage.length; t++) {
                    var path = memoImage[t].normal;
                    imgStr +=
                        '<div class="pictureWall">' +
                        '    <span onclick="cancleThis($(this))">x</span>' +
                        '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                        '</div>';
                }
                $("#uploadImgSect_updateEvent").before(imgStr);
            }
            //必填项验证
            bounce.everyTime('0.5s','updateEvent',function(){
                testUpdateEvent()
            });
        }
    })
}

// creator: 李玉婷，2019-03-07 15:37:10，修改事件 - 表单验证
function testUpdateEvent() {
    $("#activeStartDate_updateEvent1").hide()
    var state = 0;
    var special = $("#updateEvent [name='special']").prop("checked")
    var notify =  $("#updateEvent [name='notify']:checked").val()
    var freqType =  $("#updateEvent [name='freqType']").val()
    if(special){
        $("#updateEvent [name='specialInterval']").prop("disabled", false)
    }else {
        $("#updateEvent [name='specialInterval']").prop("disabled", true)
        $("#updateEvent [name='specialInterval']").val('')
    }
    if(freqType === '16') {
        $("#updateEvent .trans16").show()
        if(special){
            $("#activeStartDate_updateEvent1").show()
            $("#activeStartDate_updateEvent").hide().val("")
        }else {
            $("#activeStartDate_updateEvent1").hide().val("")
            $("#activeStartDate_updateEvent").show()
        }
    }else {
        $("#updateEvent .trans16").hide()
    }
    $("#updateEvent").find("[require]:visible:not([disabled])").each(function () {
        if($.trim($(this).val()) === ''){
            if($(this).attr("name") !== 'title'){
                state++
            }
        }
    });
    var val = $("#updateEvent input[name='title']").val();
    if(val.length > 50){
        var temp = val.substr(0,50);
        $("#updateEvent input[name='title']").val(temp);
    }
    if(notify === '1') {
        var day = $("#activeStartDate_updateEvent").val()
        var day1 = $("#activeStartDate_updateEvent1").val()
        var date = day.split(' ')[0]
        var time = $("#activeStartTime_updateEvent").val()
        var specialInterval = $("#updateEvent [name='specialInterval']").val()
        if(day !== '') {
            $("#updateEvent .tip").show()
            var tips = ''
            switch (freqType) {
                case '1':
                    tips = '系统将于'+date+' '+time+'提醒'
                    break;
                case '4':
                    tips = '系统将从'+date+'开始，每日'+time+'给予提醒'
                    break;
                case '8':
                    tips = '系统将从'+date+'开始，每周'+('日一二三四五六'.charAt(new Date(date).getDay()))+time+'给予提醒'
                    break;
                case '16':
                    tips = '系统将从'+date+'开始，每月'+(day.substring(8,10))+'日'+time+'给予提醒'
                    break;
                case '32':
                    tips = '系统将从'+date+' '+time+'开始，每年给一次提醒'
                    break;
                default:
                    tips = ''
            }
            $("#updateEvent .ty-tips").html(tips)
        }else if(day1 !== ''){
            if(specialInterval!== ''){
                $("#updateEvent .tip").show()
                var lastDate = (new Date(day1.split('/')[0],day1.split('/')[1],specialInterval*(-1)+1)).getDate()
                date = day1 + '/' + lastDate
                var time = $("#activeStartTime").val()
                $("#updateEvent [name='special']").data('startDate',date+' '+time + ':00')
                tips = '系统将从'+date+'日开始，每月倒数第'+specialInterval+'日'+time+'给予提醒'
                $("#updateEvent .ty-tips").html(tips)
            }
        }else {
            $("#updateEvent .tip").hide()
        }
    }
    if( state > 0){
        $("#updateEventSure").prop("disabled",true)
    }else {
        $("#updateEventSure").prop("disabled",false)
    }
}

// creator: 李玉婷，2019-08-21 14:46:56，图片删除
function cancleThis(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent(".pictureWall").remove();
}

// creator: 李玉婷，2019-08-21 13:05:03，日程修改提交
function updateEventSure(id){
    var schedule = {
        id: id,
        title: '', // 标题
        notify: false, // 是否需要通知
        freqType:'', // 次日程的频率 1 = 仅一次,4 = 每日,8 = 每周,16 =每月,32 =每年
        special: false, // 是否为特殊时间(如月末倒数),true-是,false-否）
        startDate: '', // 可以开始执行提醒的日期
        specialInterval: '', // 倒数第某天
        description: '', // 详细描述
        imagePaths: ''
    }
    var imagePaths = '';
    $("#form_updateEvent [name]:visible").each(function () {
        var name = $(this).attr("name")
        var type = $(this).data("type")
        if (type === 'num') {
            schedule[name] = Number($(this).val())
        } else if (type === 'singleCheck') {
            schedule[name] = $(this).is(':checked')
        } else {
            schedule[name] = $(this).val()
        }
    })
    if(schedule.title === ''){
        schedule.title= $("#updateEvent [name='title']").attr("placeHolder")
    }
    schedule.notify =  $("#updateEvent [name='notifyReady']").val();
    if(schedule.notify){
        if(schedule.special){
            schedule.startDate = $("#updateEvent [name='special']").data('startDate')
        }else {
            var dateItem = $("#activeStartDate_updateEvent").val() + ' ' + $("#activeStartTime_updateEvent").val() + ':00'
            schedule.startDate = new Date(dateItem).format('yyyy/MM/dd hh:mm:ss')
        }
    }
    var len = $('#updateEvent .pictureWall').length -1;
    $('.updateImg .pictureWall').each(function(){
        imagePaths += $(this).children(".filePic").data('path');
        var indx = $(this).index();
        if(indx != len){
            imagePaths += ',';
        }
    })
    schedule.imagePaths = imagePaths
    $.ajax({
        url: '/schedule/updateMemoSchedule.do',
        data: schedule,
        success: function (data) {
            var status = data.data.status
            if (status === '1' || status === 1) {
                layer.msg("修改成功");
                bounce.cancel();
                bounce_Fixed.cancel();
                $(".applyList [list]:visible").each(function () {
                    var name = $(this).data("name");
                    switch (name) {
                        case 'repeatSchedule':
                            getMainEventList(1, 20);
                            break;
                        case 'memoMatters':
                            var viewType = $(".applyList .memoMatters").data("viewType");
                            if (viewType == 1) {
                                getMemoMattersList(1, 20);
                            } else {
                                getMemoKeyWordList(1, 20);
                            }
                            break;
                        case 'memoFavorites':
                            updateMemoFavorite()
                            break;
                    }
                })
            } else {
                layer.msg("时间已过时，日程不能修改");
            }
        }
    })
}

// creator: 李玉婷，2019-08-21 15:27:17，修改记录列表
function updateRecord(id,currentPageNo, pageSize, seeType) {
    $(".updateRecordList").html("");
    $.ajax({
        url: '/schedule/getMemoScheduleHistories.do',
        data: {
            'currentPageNo': currentPageNo,
            'pageSize': pageSize,
            'id': id
        },
        success: function (res) {
            var status = res.success
            if (status === 1) {
                bounce.show($("#updateRecord"));
                var data = res.data;
                var list = data.memoScheduleHistoryList;
                var updateDate = new Date(data.updateDate).format('yyyy/MM/dd hh:mm:ss');
                if (list.length > 0){
                    var pageInfo = data.pageInfo;
                    //设置分页
                    var cur     = pageInfo["currentPageNo"],
                        total   = pageInfo["totalPage"];
                    var jsonStr = JSON.stringify({
                        'id': id,
                        'type': seeType
                    });
                    setPage( $("#editRecordPage"), cur, total, "editRecord", jsonStr) ;
                    $(".updaterInfo").html('修改人： ' + data.updateName + '&nbsp;&nbsp;' + updateDate);
                    $(".noneUpdated").hide().siblings().show();
                    $(".eidtNum").html(data.number);
                    var html =
                        '<tr>' +
                        '    <td>资料状态</td>' +
                        '    <td>操作</td>' +
                        '    <td>创建人/修改人</td>' +
                        '</tr>';
                    for(var t =0; t < list.length; t++){
                        html +=
                            '<tr>' +
                            '    <td>' + list[t].dataState + '</td>' +
                            '    <td>' +
                            '<span class="ty-color-blue" onclick="updateRecordSee(' + list[t].id + ',' + t + ',' + seeType +')">查看</span></td>' +
                            '    <td>'+ list[t].updateName + '&nbsp;&nbsp;' + new Date(list[t].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                            '</tr>';
                    }
                    $(".updateRecordList").html(html);
                }else {
                    var updateDate = new Date(data.updateDate).format('yyyy/MM/dd hh:mm:ss');
                    $(".noneUpdated .ty-right").html('创建人： ' + data.updateName + '&nbsp;&nbsp;' + updateDate);
                    $(".noneUpdated").show().siblings().hide();
                }
            } else {
                layer.msg("加载失败");
            }
        }
    })
}

// creator: 李玉婷，2019-08-22 09:51:43，日程修改记录查看
function updateRecordSee(id, times, type) {
    $.ajax({
        url: '/schedule/getMemoScheduleHistoryById.do',
        data: {
            'id': id
        },
        success: function (data) {
            var status = data.success;
            if (status === '1' || status === 1) {
                var eventData = data.data;
                if (times == '0') {
                    $("#seeEvent .titleTip").html('原始信息');
                    $(".additional").html('创建时间   &nbsp;&nbsp;' + new Date(eventData.createDate).format('yyyy/MM/dd hh:mm:ss'));
                }else{
                    $("#seeEvent .titleTip").html('第'+ times +'次修改后');
                    $(".additional").html('修改时间   &nbsp;&nbsp;' + new Date(eventData.updateDate).format('yyyy/MM/dd hh:mm:ss'));
                }
                if(type == '1'){ //主页查看
                    $("#seeEvent .noticeData").show();
                }else if(type == '2'){ //备忘查看
                    $("#seeEvent .noticeData").hide();
                }
                $("#seeEvent .additional").show();
                $("#seeEvent .supplyList").html("").show();
                $("#seeEvent .eventInfo").hide();
                bounce_Fixed.show($("#seeEvent"));
                eventSeeInit(eventData);
            } else {
                layer.msg("查看失败！");
            }
        }
    })
}

// creator: 张旭博, 2020-08-19 16:47:44, 获取我的分享列表
function getShareList(type, currentPageNo, pageSize) {
    var url = ''
    if (type === 0) {
        url = '/scheduleShare/shareMemoScheduleShares.do'
    } else {
        url = '/scheduleShare/receiveMemoScheduleShares.do'
    }
    $.ajax({
        url: url,
        data: {
            currentPageNo: currentPageNo,
            pageSize: pageSize
        },
        success: function (res) {
            var data = res.data.memoScheduleShares
            var list = ''

            var pageInfo = res.data.pageInfo;
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"]

            var jsonStr = JSON.stringify({
                'type': type
            });
            setPage( $("#ye_share"), cur, total, "share", jsonStr) ;
            if (data) {
                for (var i in data) {
                    list += '<tr data-id="'+data[i].scheduleId +'">' +
                            '<td>'+(-(-i-1))+'</td>' +
                            '<td><a public data-name="seeEvent">' + data[i].title+'</a></td>' +
                            '<td>' + data[i].createName + ' ' + formatTime(data[i].createTime, true) + '</td>' +
                            '<td>' + formatTime(data[i].createTime, true) + '</td>'
                    if (type === 0) {
                        var sharePeople = data[i].memoScheduleShareRecipientsHashSet
                        var sharePeopleArr = []
                        for (var j in sharePeople) {
                            sharePeopleArr.push(sharePeople[j].recipientUserName)
                        }
                        list += '<td>共'+sharePeopleArr.length+'人：' + sharePeopleArr.join("、") + '</td>'
                    }
                    list  += '</tr>'
                }
            }
            if (type === 0) {
                $(".shareSend tbody").html(list)
            } else {
                $(".shareReceive tbody").html(list)
            }
        }
    })
}

// creator: 李玉婷，2019-08-23 09:02:29，备忘日程列表
function getMemoMattersList(currentPageNo, pageSize){
    $(".memoMattersList tbody").html("");
    $.ajax({
        url: '/schedule/getMemoList.do',
        data: {
            currentPageNo: currentPageNo,
            pageSize: pageSize
        },
        success: function (data) {
            var status = data.success;
            if (status === '1' || status === 1) {
                var momelist = data.data.memoScheduleList;
                var pageInfo = data.data.pageInfo;
                //设置分页
                var cur     = pageInfo["currentPageNo"],
                    total   = pageInfo["totalPage"]
                setPage( $("#ye_eventMemo"), cur, total, "eventMemo") ;
                var html = getListStr(momelist, 1)
                $(".memoMattersList tbody").html(html);
            } else {
                layer.msg("查看失败！");
            }
        }
    })
}

// creator: 张旭博，2019-02-28 14:55:37，日程列表 - 历史记录按钮
function historyRecord(id) {
    $.ajax({
        url: '/schedule/getMemoJobList.do',
        data: { sid: id },
        success: function (data) {
            var memoSchedule = data.memoSchedule
            var memoJobList = data.memoJobList
            $("#seeEvent").data('data',memoSchedule)
            var scheduleStr = '<tr id="' + memoSchedule.id + '">' +
                '<td>'+ chargeDate(memoSchedule) +'</td>'+
                '<td>'+ memoSchedule.title +'</td>'+
                '<td>'+ new Date(memoSchedule.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>'+
                '<td>'+ chargeFreqType(memoSchedule.freqType) +'</td>'+
                '</tr>'
            var jobListStr = '';
            if(memoJobList.length === 0){
                $("#delAllHistoryBtn").prop("disabled", true)
            } else {
                $("#delAllHistoryBtn").prop("disabled", false)
            }
            for(var i  in memoJobList){
                var handleStr = ''
                var state = memoJobList[i].state
                if(state === 5 || state === 2){ // state;   //状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
                    handleStr =
                        '<div class="btnWrap">' +
                        '    <div class="handleBtn">' +
                        '        <span history="his" data-name="noRemind" class="iconfont icon-tongzhizhongxinjinyong" title="不再提醒"></span>' +
                        '        <span class="iconfont icon-tuwen disabled" title="补充记录"></span>' +
                        '        <span history="his" data-name="delEvent" class="iconfont icon-shanchu" title="删除"></span>' +
                        '    </div>' +
                        '</div>';
                }else{
                    handleStr =
                        '<div class="btnWrap">' +
                        '    <div class="handleBtn">' +
                        '        <span class="iconfont icon-tongzhizhongxinjinyong disabled" title="不再提醒"></span>' +
                        '        <span history="his" data-name="extraRecord" class="iconfont icon-tuwen" title="补充记录"></span>' +
                        '        <span history="his" data-name="delEvent" class="iconfont icon-shanchu" title="删除"></span>' +
                        '    </div>' +
                        '</div>';
                }
                jobListStr +=  '<tr data-id="'+memoJobList[i].id +'" data-sid="'+memoJobList[i].scheduleId+'">' +
                                '<td>' + (-(-i - 1)) + '</td>'+    //序号
                                '<td><a history="his" data-name="seeEvent" data-source="record" data-state="'+ state +'">' + new Date(memoJobList[i].runTime).format('yyyy/MM/dd hh:mm:ss') + '</a></td>'+    //设置的提醒时间
                                '<td>' + handleStr + '</td>'+    //操作
                                '<td class="state">' + chargeState(memoJobList[i].state) + '</td>'+    //状态
                                '<td class="hd">' + JSON.stringify(memoJobList[i]) + '</td>'+
                                '</tr>'
            }
            $("#historyRecord .schedule tbody").html(scheduleStr)
            $("#historyRecord .historyList tbody").html(jobListStr)
        }
    })
}

// -------------备忘收藏夹---------------

function newFolderBtn(type) {
    if (type === 'col') {
        // 竖型
        var folderName = $("#collect_tree .ty-treeItemActive span").text() || '全部收藏夹'

    } else if (type === 'row') {
        var folderName = $(".memoFavorites tbody:visible").attr("pname")
    }
    bounce_Fixed.show($("#newFolder"))
    $("#newFolder .folderName").html(folderName)
    $("#newFolder").find("input").val("")
}
// creator: 张旭博，2021-01-27 13:13:36，新建一级收藏夹接口
function sureNewFolder() {
    var name = $("#newFolder input").val()
    if ($.trim(name) === '') {
        layer.msg("请录入名称！")
        return false
    }
    if ($("#collect").is(":visible")) {
        var parentId = $("#collect_tree .ty-treeItemActive").data("id")
    } else {
        var parentId = $(".memoFavorites tbody:visible").attr("pid")
        var parentName = $(".memoFavorites tbody:visible").attr("pname")
    }

    var data = {
        name: name
    }
    if (parentId) {
        data.parentId = parentId
    }
    $.ajax({
        url: '../scheduleFavorite/addScheduleFavorite.do',
        data: data,
        success: function (res) {
            var data = res.data
            var state = data.state
            if (state === 1) {
                layer.msg("操作成功")
                bounce_Fixed.cancel()
                if ($("#collect").is(":visible")) {
                    var treeActive = $("#collect_tree .ty-treeItemActive")
                    if (treeActive.length > 0) {
                        if(treeActive.children().eq(0).hasClass("ty-fa")){
                            treeActive.children().eq(0).attr("class","fa fa-angle-right");
                            treeActive.click();
                        }else if (treeActive.children().eq(0).hasClass("fa-angle-down")){
                            var treeItemData = data.memoFavorite
                            var str =   '<li><div class="ty-treeItem" title="'+ treeItemData.name +'" data-id='+treeItemData.id+'><i class="ty-fa"></i><i class="fa fa-folder"></i><span>'+treeItemData.name+'</span></li>'
                            treeActive.next().append(str)
                        }
                    } else {
                        updateColFavorites()
                    }
                } else {
                    updateRowFavorite()
                }

            } else if (state === 2) {
                bounce_Fixed2.show($("#fixed2_confirm"))
                $("#fixed2_confirm .text").html('<p>操作失败！</p><p>因为收藏夹名称重复。</p>')
                $("#fixed2_confirm .iknowBtn").unbind().on("click", function () {
                    bounce_Fixed2.cancel()
                })
            } else if (state === 3) {
                layer.msg("父菜单下有日程关联，不能新增子级收藏夹")
            }

        }
    })
}

// creator: 张旭博，2021-02-02 11:26:43，修改收藏夹
function sureChangeFolder() {
    var name = $("#editFavoriteFolder input").val()
    if ($.trim(name) === '') {
        layer.msg("请录入名称！")
        return false
    }
    var id = $("#editFavoriteFolder").data("id")
    var data = {
        id: id, // 原收藏夹id
        name: name // 新收藏夹名称
    }
    $.ajax({
        url: '../scheduleFavorite/updateScheduleFavorite.do',
        data: data,
        success: function (res) {
            if (res === 1) {
                layer.msg("操作成功")
                $(".memoFavorites tbody:visible tr[data-id='"+id+"']").find(".collectName").html(name)
                bounce_Fixed.cancel()
            } else if (res === 2) {
                bounce_Fixed2.show($("#fixed2_confirm"))
                $("#fixed2_confirm .text").html("<p>操作失败！</p><p>因为收藏夹名称重复。</p>")
                $("#fixed2_confirm .iknowBtn").unbind().on("click", function () {
                    bounce_Fixed2.cancel()
                })
            } else {
                layer.msg("操作失败")
            }

        }
    })
}

// creator: 张旭博，2021-01-27 13:13:36，更新竖向收藏夹页面(父级信息， 新增还是更新）
function updateColFavorites() {
    var url = '', data = {}
        url = '../scheduleFavorite/getFirstFavorites.do'
    $.ajax({
        url: url,
        success: function (res) {
            var listFirstCategory = res.data ,
                str = '<div class="level1" level="1">';
            for(var i in listFirstCategory){
                var hasChildren = listFirstCategory[i].memoFavoriteHashSet.length > 0
                var iconStr = ''
                if(hasChildren){
                    iconStr = '<i class="fa fa-angle-right"></i>'
                }else{
                    iconStr = '<i class="ty-fa"></i>'
                }
                str +=  '<li>' +
                        '   <div class="ty-treeItem" title="'+ listFirstCategory[i]["name"] +'" data-id='+listFirstCategory[i]["id"]+'>' +
                                iconStr +
                        '       <i class="fa fa-folder"></i>' +
                        '       <span>'+listFirstCategory[i]["name"]+'</span>' +
                        '   </div>' +
                        '</li>'
            }
            $("#collect_tree .ty-colFolderTree").html(str)
        }
    })
}

// creator: 张旭博，2021-02-02 15:39:35，更新横向收藏夹页面(父级信息， 新增还是更新）
function updateRowFavorite(parent) {
    var url = '', data = {}
    var pid = parent?parent.pid:$(".memoFavorites tbody:visible").attr("pid")
    var pname = parent?parent.pname:$(".memoFavorites tbody:visible").attr("pname")
    if (Number(pid) !== 0) {
        url = '../scheduleFavorite/getSonFavorites.do'
        data.id = pid
    } else {
        url = '../scheduleFavorite/getFirstFavorites.do'
    }
    $.ajax({
        url: url,
        data: data,
        success: function (res) {
            var listFirstCategory = res.data,
                sonLevelStr = ''
            sonLevelStr +=  '<table class="kj-table noSide sortable">' +
                            '    <thead>' +
                            '    <tr>' +
                            '        <th>收藏夹名称</th>' +
                            '        <th>已有备忘事项的数量</th>' +
                            '        <th></th>' +
                            '        <th class="default-sort">创建时间</th>' +
                            '    </tr>' +
                            '    </thead>' +
                            '    <tbody pid="'+pid+'" pname="'+pname+'">';
            for(var i in listFirstCategory){
                var hasChildren = listFirstCategory[i].memoFavoriteHashSet.length > 0
                var iconStr = ''
                if(hasChildren){
                    iconStr = '<i class="fa fa-angle-right"></i>'
                }else{
                    iconStr = '<i class="ty-fa"></i>'
                }
                sonLevelStr +=      '<tr data-id="'+listFirstCategory[i].id+'">' +
                                    '   <td><div class="ty-rowFolderItem" data-id="'+listFirstCategory[i].id+'" data-pid="'+(listFirstCategory[i].parentId || '')+'">'+iconStr+' <span class="collectName">'+ listFirstCategory[i].name +'</span></div></td>'+
                                    '   <td data-num="'+listFirstCategory[i].scheduleNum+'">'+listFirstCategory[i].scheduleNum+' 条</td>'+
                                    '   <td style="width: 180px">'+
                                    '      <div class="handleBtn">'+
                                    '          <span class="iconfont icon-bianji" data-name="editFavoriteFolder" title="修改"></span>'+
                                    '          <span class="iconfont icon-shanchu" data-name="deleteFavoriteFolder" title="删除"></span>'+
                                    '      </div>'+
                                    '   </td>'+
                                    '   <td>'+listFirstCategory[i].createName + ' ' + formatTime(listFirstCategory[i].createTime, true)+'</td>'+
                                    '</tr>'
            }
            sonLevelStr +=      '   </tbody></table>';
            if (parent) {
                $(".folderRecordList table").hide()
                $(".folderRecordList").append(sonLevelStr)
            } else {
                $(".folderRecordList table:visible").replaceWith(sonLevelStr)
            }
            $('.folderRecordList table:visible').tablesort()
            $(".folderRecordList table:visible").data('tablesort').sort($("th.default-sort"));
            $(".folderRecordList table:visible").data('tablesort').sort($("th.default-sort"));
        }
    })

}

// creator: 张旭博，2021-02-03 09:05:36，
function updateMemoFavorite() {
    var categoryId = $(".memoRecord").data("categoryId")
    var categoryName = $(".memoRecord").data("categoryName")
    $.ajax({
        url: '../scheduleFavorite/getMemoFavoriteSchedules.do',
        data: { id: categoryId},
        success: function (res) {
            var momeList = res.data
            var html = getListStr(momeList, 1)
            $(".memoRecord tbody").html(html)
            $(".memoRecord .folderName").html(categoryName)
        }
    })
}

// creator: 张旭博，2021-01-28 13:35:29，收藏 - 确定
function sureCollect(scheduleId, selector) {
    var data = {
        newFavoriteId: $("#collect .ty-treeItemActive").data("id"),
        scheduleId: scheduleId
    }
    $.ajax({
        url: '../scheduleFavorite/collectToOtherFavorite.do',
        data: data,
        success: function (res) {
            if (res === 1) {
                layer.msg("操作成功")
                selector.parents("tr").remove()
                bounce.cancel()
            } else if (res === 2) {
                layer.msg("该收藏夹中已存在，无法再次收藏")
            } else if (res === 3) {
                layer.msg("新收藏夹并不存在，无法收藏")
            } else {
                layer.msg("操作失败")
            }
        }
    })
}

// creator: 张旭博，2021-02-02 18:40:34，转移至其他收藏夹
function sureMoveToOtherFavorite(scheduleId) {
    var oldFavoriteId = $(".memoRecord").data("categoryId")
    var newFavoriteId = $("#collect .ty-treeItemActive").data("id")
    var data = {
        oldFavoriteId : oldFavoriteId,  // 老收藏夹id
        newFavoriteId: newFavoriteId,   // 新收藏夹id
        scheduleId: scheduleId  //备忘id
    }
    $.ajax({
        url: '../scheduleFavorite/moveToOtherFavorite.do',
        data: data,
        success: function (res) {
            if (res === 1) {
                layer.msg("操作成功")
                updateMemoFavorite()
                bounce.cancel()
            } else if (res === 2) {
                layer.msg("该日程在老收藏夹中已不存在 无法转移")
            } else if (res === 3) {
                layer.msg("新收藏夹并不存在，无法转移")
            } else {
                layer.msg("操作失败")
            }
        }
    })
}

// creator: 张旭博，2021-02-02 18:45:10，收藏至其他收藏夹 - 确定
function sureCollectToOtherFavorite(scheduleId) {
    var newFavoriteId = $("#collect .ty-treeItemActive").data("id")
    var data = {
        newFavoriteId: newFavoriteId,   // 新收藏夹id
        scheduleId: scheduleId  //备忘id
    }
    $.ajax({
        url: '../scheduleFavorite/collectToOtherFavorite.do',
        data: data,
        success: function (res) {
            if (res === 1) {
                layer.msg("操作成功")
                bounce.cancel()
            } else if (res === 2) {
                layer.msg("该收藏夹中已存在，无法再次收藏")
            } else if (res === 3) {
                layer.msg("新收藏夹并不存在，无法收藏")
            } else {
                layer.msg("操作失败")
            }
        }
    })
}

// creator: 张旭博，2021-02-03 09:12:57，从本收藏夹移除 - 确定
function sureRemoveFromFavorite(scheduleId) {
    var favoriteId = $(".memoRecord").data("categoryId")
    $.ajax({
        url: '/scheduleFavorite/deleteMemoFavoriteSchedule.do',
        data: {
            favoriteId : favoriteId,
            scheduleId : scheduleId
        },
        success: function (data) {
            if (data === 1) {
                layer.msg("该条备忘已从本收藏夹移除")
                updateMemoFavorite()
                bounce_Fixed.cancel()
            } else if (data === 2) {
                layer.msg("要移除的本不存在")
            } else {
                layer.msg("操作失败")
            }
        }
    })
}

// creator: 张旭博，2021-02-03 09:12:57，从全部收藏夹中移除 - 确定
function sureRemoveFromAllFavorites(scheduleId) {
    $.ajax({
        url: '/scheduleFavorite/deleteAllFavoriteSchedule.do',
        data: {
            scheduleId : scheduleId
        },
        success: function (data) {
            if (data === 1) {
                layer.msg("该条备忘已从全部收藏夹中移除，且已回到常用备忘列表中")
                updateMemoFavorite()
                bounce_Fixed.cancel()
            } else {
                layer.msg("操作失败")
            }
        }
    })
}


// creator: 张旭博，2019-03-21 09:27:42，历史记录 - 删除全部历史记录 - 按钮
function delAllHistoryBtn() {
    bounce_Fixed.show($("#delEvent"))
    $("#delEvent").data("type", 'allHistory')
    $("#delEvent .delContent").html('<p>您确定删除此日程的全部历史记录吗？</p>')
}
// creator: 张旭博，2019-03-14 15:06:03，获取查看历史记录日程
function getHistoryList(currentPageNo, pageSize) {
    $(".applyList .historyList tbody").html("");
    $.ajax({
        url: '/schedule/getMemoJobListByUserId.do',
        data: {
            currentPageNo: currentPageNo,
            pageSize: pageSize
        },
        success: function (data) {
            var list = data.data.memoJobList
            var pageInfo = data.data.pageInfo
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"]
            setPage( $("#ye_eventHistory"), cur, total, "eventHistory") ;
            if(list){
                var str = getListStr(list, 3);
                $(".applyList .historyList tbody").html(str);
            }
        }
    })
}

// creator: 张旭博，2019-03-14 15:06:37，按关键词搜索 - 获取数据
function getKeyWordList(currentPageNo, pageSize){
    $(".repeatSchedule .list tbody").html("");
    var keyword = $("#searchByKey").val()
    var param = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        keyword: keyword
    }
    $.ajax({
        url: '/schedule/scheduleManageList.do',
        data: param,
        success: function (data) {
            var list = data.memoScheduleList
            var pageInfo = data.pageInfo
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"];
            setPage( $("#ye_repeatSchedule"), cur, total, "keyWord") ;
            if(list){
                var str = getListStr(list, 2)
                $(".repeatSchedule .list tbody").html(str);
            }
        }
    })
}
// creator: 李玉婷，2019-08-26 14:38:52，备忘事项搜索
function getMemoKeyWordList(currentPageNo, pageSize, keyword) {
    $(".memoMattersList tbody").html("");
    var param = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        keyword: keyword
    }
    $.ajax({
        url: '/schedule/searchMemoSchedule.do',
        data: param,
        success: function (data) {
            var list = data.data.memoScheduleList
            var pageInfo = data.data.pageInfo
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"];
            var json = JSON.stringify({"keyword": keyword}) ;
            setPage( $("#ye_eventMemo"), cur, total, "memoKeyWord", json) ;
            if(list){
                var str = getListStr(list, 1);
                $(".memoMattersList tbody").html(str);
            }
            $(".ty-search input").val("")
        }
    })
}
// creator: 李玉婷，2019-08-26 14:38:52，历史日程搜索
function getHisKeyWordList(currentPageNo, pageSize) {
    $(".applyList .historyList tbody").html("");
    var keyword = $("#hisSearchKey").val();
    var param = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        keyword: keyword
    }
    $.ajax({
        url: '/schedule/searchMemoJob.do',
        data: param,
        success: function (data) {
            var list = data.data.memoJobList
            var pageInfo = data.data.pageInfo
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"]
            setPage( $("#ye_eventHistory"), cur, total, "hisKeyWord") ;
            if(list){
                var str = getListStr(list, 3);
                $(".applyList .historyList tbody").html(str);
                $(".kj-table tbody tr").mouseover(function(){
                    $(this).find('.handleBtn').show();
                })
                $(".kj-table tbody tr").mouseout(function(){
                    var node = $(this).find('ul');
                    if(!node.is(':visible')){　　//如果node是隐藏的则显示node元素，否则隐藏
                        $(this).find('.handleBtn').hide();
                    }
                })
            }
        }
    })
}

function getListStr(list,listType) {
    var str = ''
    for(var i in list) {
        var handleStr = '', supplyAble = '', editEnable = '', noticeEnable = '';
        var freqType = list[i].freqType; //freqType 周期 1 = 仅一次,4 = 每日,8 = 每周,16 =每月,32 =每年
        var state = list[i].state  //state;   //状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        if(listType === 1){ //备忘事项
            // 未设置提醒
            if ($(".memoMatters").is(":visible")) {
                handleStr +=    '<div class="btnWrap">' +
                                '    <div class="handleBtn">' +
                                '        <span data-name="share" class="iconfont icon-fenxiang" title="分享"></span>' +
                                '        <span data-name="collect" class="iconfont icon-shoucang" title="收藏"></span>' ;
            } else {
                handleStr +=    '<div class="btnWrap">' +
                                '    <div class="handleBtn">' +
                                '        <span data-name="share" class="iconfont icon-fenxiang" title="分享"></span>' ;
            }
            if ($(".memoFavorites").is(":visible")) {
                handleStr +=
                    '        <span data-name="more" class="iconfont icon-Androidgengduo" title="更多"></span>' +
                    '        <ul class="do_avatar">' +
                    '            <li data-name="shareManage">分享管理</li>' +
                    '            <li public data-name="delEvent" data-freq="1" data-type="main">删  除</li>' +
                    '            <li memo data-name="updateEvent">修  改</li>' +
                    '            <li public data-name="updateRecord" data-type="memo">修改记录</li>' +
                    '            <li public data-name="copyEvent" data-type="main">生成日程</li>' +
                    '            <li memo data-name="moveToOtherFavorite">转移至其他收藏夹</li>' +
                    '            <li memo data-name="collectToOtherFavorite">收藏至其他收藏夹</li>' +
                    '            <li memo data-name="removeFromFavorite">仅从本收藏夹中移除</li>' +
                    '            <li memo data-name="removeFromAllFavorites">从全部收藏夹中移除</li>' +
                    '        </ul>' +
                    '    </div>' +
                    '</div>';
            } else {
                handleStr +=
                    '        <span data-name="more" class="iconfont icon-Androidgengduo" title="更多"></span>' +
                    '        <ul class="do_avatar">' +
                    '            <li data-name="shareManage">分享管理</li>' +
                    '            <li public data-name="delEvent" data-freq="1" data-type="main">删  除</li>' +
                    '            <li memo data-name="updateEvent">修  改</li>' +
                    '            <li public data-name="updateRecord" data-type="memo">修改记录</li>' +
                    '            <li public data-name="copyEvent" data-type="main">生成日程</li>' +
                    '        </ul>' +
                    '    </div>' +
                    '</div>';
            }
        }else if (listType === 2){ //设置提醒
            editEnable = list[i].state == '5' ? '<li public data-name="updateEvent">修  改</li>':'<li class="li-disabled">修  改</li>';
            if(freqType === 1){//一次性提醒
                if (state == '1' || state == '3' || state == '4') {
                    noticeEnable = '<span class="noMindBtn" title="不再提醒"></span>'
                }else{
                    noticeEnable = '<span data-name="noRemind" data-freqType="'+ freqType +'" public class="noMindAble" title="不再提醒"></span>'
                }
                handleStr =
                    '<div class="btnWrap">' +
                    '    <div class="handleBtn">' +
                    '        <span data-name="share" class="iconfont icon-fenxiang" title="分享"></span>' + noticeEnable +
                    '        <span data-name="more" class="iconfont icon-Androidgengduo" title="更多"></span>' +
                    '        <ul class="do_avatar">' +
                    '            <li data-name="shareManage">分享管理</li>' +
                    '            <li public data-name="delEvent" data-freq="1" data-type="main">删  除</li>' + editEnable +
                    '            <li public data-name="updateRecord" data-type="main">修改记录</li>' +
                    '            <li public data-name="copyEvent" data-type="main">复制，并生成新日程</li>' +
                    '        </ul>' +
                    '    </div>' +
                    '</div>';
            }else{
                if (state == '3') {
                    noticeEnable = '<span class="noMindBtn" title="不再提醒"></span>'
                }else{
                    noticeEnable = '<span data-name="noRemind" data-freqType="' + freqType + '" public class="noMindAble" title="不再提醒"></span>'
                }
                handleStr =
                    '<div class="btnWrap">' +
                    '    <div class="handleBtn">' +
                    '        <span data-name="share" class="iconfont icon-fenxiang" title="分享"></span>' + noticeEnable +
                    '        <span data-name="more" class="iconfont icon-Androidgengduo" title="更多"></span>' +
                    '        <ul class="do_avatar">' +
                    '            <li public data-name="historyRecord">历史记录</li>' +
                    '            <li data-name="shareManage">分享管理</li>' +
                    '            <li public data-name="delEvent" data-freq="0" data-type="main">删  除</li>' + editEnable +
                    '            <li public data-name="updateRecord" data-type="main">修改记录</li>' +
                    '            <li public data-name="copyEvent" data-type="main">复制，并生成新日程</li>' +
                    '        </ul>' +
                    '    </div>' +
                    '</div>';
            }
        }else {
            if(state === 2 || state === 5) {//state;   //状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
                supplyAble = '<span class="iconfont icon-tuwen disabled" title="补充记录"></span>';
            }else {
                supplyAble = '<span history="his" data-name="extraRecord" class="iconfont icon-tuwen" title="补充记录"></span>';
            }
            handleStr =
                '<div class="btnWrap">' +
                '    <div class="handleBtn">' +
                '        <span data-name="share" class="iconfont icon-fenxiang" title="分享"></span>' + supplyAble +
                '        <span data-name="more" class="iconfont icon-Androidgengduo" title="更多"></span>' +
                '        <ul class="do_avatar">' +
                '            <li data-name="shareManage">分享管理</li>' +
                '            <li public data-name="delEvent" data-freq="1" data-type="history">删  除</li>' +
                '            <li public data-name="updateRecord" data-type="history" data-scheduleId="'+ list[i].scheduleId +'">修改记录</li>' +
                '            <li public data-name="copyEvent" data-type="history">复制，并生成新日程</li>' +
                '        </ul>' +
                '    </div>' +
                '</div>';
        }
        if (listType === 2 || listType === '2') {
            str +=  '<tr data-id="'+list[i].id +'">' +
                    '   <td>' + (-(-i - 1)) + '</td>'+    //序号
                    '   <td>' + new Date(list[i].activeStartDate).format('yyyy-MM-dd hh:mm:ss') + '</td>'+    //设置的提醒时间
                    '   <td><a public data-name="seeEvent">' + list[i].title  + '</a></td>'+    //标题
                    '   <td>' + handleStr + '</td>'+
                    '   <td>' + list[i].createName + ' ' + new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') + '</td>'+    //创建日期
                    '   <td>' + chargeFreqType(list[i].freqType) + '</td>'+    //日程类型
                    '   <td class="state">' + (freqType === 1 ? chargeState(list[i].state) : '--') + '</td>'+    //状态
                    '</tr>'
        }else if(listType === 3) {
            str +=  '<tr data-id="'+list[i].id +'" data-sid="'+list[i].scheduleId +'">' +
                    '   <td>' + (-(-i - 1)) + '</td>'+    //序号
                    '   <td>' + new Date(list[i].runTime).format('yyyy-MM-dd hh:mm:ss') + '</td>'+    //设置的提醒时间
                    '   <td><a history="his" data-name="seeEvent" data-source="history" data-state="'+ state +'">' + list[i].title  + '</a></td>'+    //日程内容
                    '   <td>' + handleStr + '</td>' +
                    '   <td>' + list[i].createName + ' ' + new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') + '</td>'+    //创建日期
                    '   <td>' + chargeFreqType(list[i].freqType) + '</td>'+    //日程类型
                    '   <td class="state">' + chargeState(list[i].state) + '</td>'+    //状态
                    '</tr>';
        }else{
            str +=  '<tr data-id="'+list[i].id +'">' +
                    '   <td>' + (-(-i - 1)) + '</td>'+    //序号
                    '   <td><a memo data-name="seeEvent">' + list[i].title  + '</a></td>'+    //标题
                    '   <td>' + handleStr + '</td>'+
                    '   <td data-sort-value="'+list[i].updateDate+'">' + list[i].createName + ' ' + formatTime(list[i].updateDate, true) + '</td>'+    //创建日期
                    '</tr>'
        }
    }
    return str
}

// creator: 李玉婷，2019-08-26 14:25:50，搜索返回
function gobackOther() {
    $(".applyList [list]:visible").each(function () {
        var name = $(this).data('name');
        switch (name) {
            case 'repeatSchedule':
                getMainEventList(1, 20);
                $(".mainBtnGroup").show();
                $(".otherBtn").hide();
                break;
            case 'memoMatters':

                var name = $(".searchBtn").data("name")
                if (name === 'memo') {
                    $(".mainBtnGroup").hide();
                    $(".otherBtn").show();
                    $(".memoBtnGroup").show().siblings().hide();
                    $(".applyList .memoMatters").data("viewType", '1');
                    getMemoMattersList(1, 20);
                } else {
                    $(".favoritesBtnGroup").show().siblings().hide()
                    $(".memoFavorites").show().siblings().hide()
                }
                break;
            case 'historyList':
                $(".mainBtnGroup").hide();
                $(".otherBtn").show();
                $(".historyBtnGroup").show().siblings().hide();
                getHistoryList(1, 20);
                break;
        }
    })
}
// creator: 张旭博，2019-03-01 11:02:14，转换 - 日程类型
function chargeFreqType(freqType){
    switch (freqType) {
        case 1:
            return '一次性'
            break;
        case 4:
            return '重复性/每日'
            break;
        case 8:
            return '重复性/每周'
            break;
        case 16:
            return '重复性/每月'
            break;
        case 32:
            return '重复性/每年'
            break;
        default:
            return '--'
    }
}
// creator: 李玉婷，2019-08-20 09:45:20，查看中-重复周期字段
function cycleType(freqType){
    switch (freqType) {
        case 1:
            return '不重复'
            break;
        case 4:
            return '每日'
            break;
        case 8:
            return '每周'
            break;
        case 16:
            return '每月'
            break;
        case 32:
            return '每年'
            break;
        default:
            return '--'
    }
}
// creator: 张旭博，2019-03-01 11:02:35，转换 - 日程状态
function chargeState(state) {
    switch (state) {
        case 1:
            return '已完成'
            break;
        case 2:
            return '提醒中'
            break;
        case 3:
            return '不再提醒'
            break;
        case 4:
            return '提醒已过期'
            break;
        case 5:
            return '尚未开始提醒'
            break;
        default:
            return '未开始'
    }
}

function chargeDate(data) {
    var day = formatTime(data.activeStartDate, true)
    var tips = '--'
    if(day){
        var date = day.split(' ')[0]
        var time = day.split(' ')[1].substring(0,5)
        if(data.special) {
            tips = '每月倒数第'+data.specialInterval+'日'+time
        }else{
            switch (data.freqType) {
                case 1:
                    tips = date + ' ' + time
                    break;
                case 4:
                    tips = '每日' + ' ' +time
                    break;
                case 8:
                    tips = '每周'+('日一二三四五六'.charAt(new Date(date).getDay())) + ' ' + time
                    break;
                case 16:
                    tips = '每月'+(date.substring(8,10))+'日' + ' ' + time
                    break;
                case 32:
                    tips = '每年'+date+' '+time
                    break;
            }

        }
    }
    return tips
}

// creator: 张旭博，2019-03-01 11:01:32，获取时间区间下的时间（每隔半个小时）
function getData (start, delay) {
    let data = []
    let a = start.split(':')[0]
    let b = start.split(':')[1]
    for (let i = a; i < 24; i++) {
        if (i < 10) {
            i = '0' + i
        }
        data.push({ 'value': i + ':' + '00', 'label': i + ':' + '00' })
        data.push({ 'value': i + ':' + '30', 'label': i + ':' + '30' })
    }
    if (b === '30') {
        data.shift()
    }
    if (delay > 0) {
        for (let j = 0; j < delay; j++) {
            data.shift()
        }
    }
    return data
}

function getTips(eventData, isMain) {
    var state  = eventData.state
    // noticeNumber 实际提醒次数    activeEndDate（日程已完成状态时取） 已完成时间，nextRunTime 下次提醒时间
    var tip = ''
    // 状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
    var freqType = eventData.freqType
    var special = eventData.special
    var specialInterval = eventData.specialInterval
    var noticeNumber = eventData.noticeNumber // 实际提醒次数
    var activeEndDate = eventData.updateDate // 已完成时间
    var activeStartDate = eventData.activeStartDate // 已完成时间
    var lastRunTime = eventData.lastRunTime // 最后一次提醒时间
    var nextRunTime = eventData.nextRunTime // 下次提醒时间
    if(freqType === 0) {
        tip = ''
    }else if(freqType === 1){
        activeStartDate = new Date(activeStartDate).format('yyyy/MM/dd hh:mm');
        activeEndDate = new Date(activeEndDate).format('yyyy年MM月dd日 hh:mm');
        lastRunTime = new Date(lastRunTime).format('yyyy/MM/dd hh:mm');
        switch (state) {
            case 5:
                tip = '<p>系统将于'+activeStartDate+'给予提醒</p>'
                break;
            case 4:
                tip = '<p>系统应于'+activeStartDate+'给予提醒，本提醒已过期</p>'

                break;
            case 3:
                tip = '<p>系统应于'+activeStartDate+'给予提醒</p><p> 已提醒过'+noticeNumber+'次，不再提醒</p>'
                break;
            case 2:
                // 一次性日程 主页里面不展示“再次提醒”
                // var nowDate = formatTime(Date.parse(new Date()), true)
                if(noticeNumber > 1){
                    tip = '<p>系统应于'+activeStartDate+'给予提醒</p><p> 已提醒过'+noticeNumber+'次，并将于'+lastRunTime+'再次给予提醒</p>'
                } else {
                    tip = '系统应于'+activeStartDate+'给予提醒 正在提醒中'
                }
                break;
            case 1:
                tip = '<p>系统应于'+activeStartDate+'给予提醒，本活动已于'+activeEndDate+'完成</p>'
                break;
        }
    }else {
        var date = new Date(activeStartDate).format('yyyy/MM/dd')
        var time = new Date(activeStartDate).format('hh:mm')
        activeEndDate = new Date(activeEndDate).format('yyyy年MM月dd日 hh:mm');
        lastRunTime = new Date(lastRunTime).format('yyyy/MM/dd hh:mm');
        nextRunTime = new Date(nextRunTime).format('yyyy/MM/dd hh:mm');
        switch (freqType) {
            case 1:
                tip = '系统将于'+date+' '+time+'提醒'
                break;
            case 4:
                tip = '系统将从'+date+'开始，每日'+time+'给予提醒'
                break;
            case 8:
                tip = '系统将从'+date+'开始，每周'+('日一二三四五六'.charAt(new Date(date).getDay()))+time+'给予提醒'
                break;
            case 16:
                if(special){
                    tip = '系统将从'+date+'日开始，每月倒数第'+specialInterval+'日'+time+'给予提醒'
                }else {
                    tip = '系统将从'+date+'开始，每月'+(date.substring(8,10))+'日'+time+'给予提醒'
                }
                break;
            case 32:
                tip = '系统将从'+date+' '+time+'开始，每年给一次提醒'
                break;
            default:
                tip = ''
        }
        if (isMain) {
            switch (state) {
                case 5:
                    tip += ''
                    break;
                case 1:
                case 4:
                    tip += '<p>下次提醒时间 '+nextRunTime+'</p>'
                    break;
                case 3:
                    tip += '<p> 已提醒过'+noticeNumber+'次，不再提醒</p>'
                    break;
                case 2:
                    var nowDate = formatTime(Date.parse(new Date()), true)
                    if(lastRunTime > nowDate){
                        tip += '<p>下次提醒时间 '+nextRunTime+'</p>'
                    } else {
                        tip += ' 正在提醒中'
                    }
                    break;
            }
        } else {
            switch (state) {
                case 5:
                    tip += ''
                    break;
                case 4:
                    tip += '<p>已提醒过'+noticeNumber+'次，本提醒已过期</p>'
                    break;
                case 3:
                    tip += '<p> 已提醒过'+noticeNumber+'次，不再提醒</p>'
                    break;
                case 2:
                    var nowDate = formatTime(Date.parse(new Date()), true)
                    if(lastRunTime > nowDate){
                        tip += '<p> 已提醒过'+noticeNumber+'次，并将于'+lastRunTime+'再次给予提醒</p>'
                    } else {
                        tip += ' 正在提醒中'
                    }
                    break;
                case 1:
                    tip += '<p>已提醒过'+noticeNumber+'次，本活动已于'+activeEndDate+'完成</p>'
                    break;
            }
        }
    }
    return tip
}

// creator: 张旭博，2019-03-22 16:19:14，设置时间区间
function setTimeLine() {
    var timeData = getData('0:00', 0)
    var str = ''
    for(var i in timeData) {
        str += '<option value="'+timeData[i].value+'">'+timeData[i].label+'</option>'
    }
    $("#activeStartTime").html(str)
    $("#activeStartTime_updateEvent").html(str)
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.parent().siblings(".textMax").text( curLength +'/' + max );
}
// creator: 张旭博，2021-02-03 16:05:16，选择分享人
function addRole() {
    bounce_Fixed3.show($("#addRole"))
    $("#addRole tr input:checkbox").prop("checked", false)
    $(".share_avatar_row .selected_item").each(function () {
        var id = $(this).data("id")
        $("#addRole tr[data-id='"+id+"']").find("input:checkbox").prop("checked", true)
    })
    $("#addRole .sureBtn").on("click",function () {
        var str = ''
        $("#addRole .ty-checkbox input:checked").each(function () {
            var tr = $(this).parents("tr")
            var id = tr.data("id")
            var name = tr.data("name")
            str +=  '<div class="selected_item" data-id="'+id+'">' +
                    '    <span class="selected_name">'+name+'</span>' +
                    '    <span class="delRole" onclick="delRole($(this))">+</span>' +
                    '</div>'
        })
        $(".shareChooseList").html(str)
        bounce_Fixed3.cancel()
    })
}

// creator: 张旭博，2021-02-04 10:12:28，删除分享人
function delRole(selector) {
    selector.parents(".selected_item").remove()

}


// creator: 张旭博, 2020-08-18 08:34:55, 获取日程可分享人员列表
function getShareableUsers(scheduleId) {
    var data = {}
    if (scheduleId) {
        data.scheduleId = scheduleId
    }
    $.ajax({
        url: '/scheduleShare/getShareableUsers.do',
        data: data,
        beforeSend: function () {
          $(".sharePerson").html('<div class="loading"><i class="fa fa-refresh"></i> 加载中</div>')
        },
        success: function (res) {
            var data = res.data
            var listStr = ''
            var listStr2 = ''
            if (data) {
                for (var i in data) {
                    listStr +=  '<li data-id="' + data[i].userID + '">' +
                                '   <div class="ty-checkbox">' +
                                '       <input type="checkbox" id="u'+data[i].userID +'">' +
                                '       <label for="u'+data[i].userID +'"></label> ' + data[i].userName + '(' + data[i].mobile + ')' +
                                '   </div>' +
                                '</li>'
                    listStr2 += '<tr data-id="' + data[i].userID + '" data-name="'+data[i].userName+'">' +
                                '   <td>'+
                                '       <div class="ty-checkbox">' +
                                '           <input type="checkbox" id="n'+data[i].userID +'">' +
                                '           <label for="n'+data[i].userID +'"></label> ' +
                                '       </div>' +
                                '   </td>'+
                                '   <td>'+ data[i].userName+ '</td>'+
                                '   <td>'+ data[i].mobile+ '</td>'+
                                '</tr>'
                }
            }
            $("#addRole tbody").html(listStr2)
            $(".sharePerson").html(listStr)
        }
    })
}

// creator: 张旭博, 2020-08-19 00:50:06, 确定分享
function sureShare() {
    var scheduleId = $(".sharePerson").data("scheduleId")
    var shareableUserIds = []
    $(".sharePerson li input:checked").each(function () {
        var id = $(this).parents("li").data("id")
        shareableUserIds.push(id)
    })
    if (shareableUserIds.length === 0) {
        layer.msg("请选择至少一个分享对象！")
        return false
    }
    $.ajax({
        url: '/scheduleShare/shareScheduleToUsers.do',
        data: {
            scheduleId: scheduleId, // 日程id
            shareableUserIds: shareableUserIds.join(",") // 被分享人id
        },
        success: function (res) {
            var data = res.data
            if (data === 0) {
                layer.msg("分享失败！")
            } else if (data === 1) {
                layer.msg("分享成功！")
                $(".share_avatar").hide();
                $(".handleBtn span").removeClass("active")
                $(".handleBtn").hide()
            } else {
                layer.msg("加载失败！")
            }
        }
    })
}

// creator: 张旭博, 2020-08-19 16:08:21, 获取分享管理
function getShareManage(currentPageNo, pageSize) {
    var scheduleId = $("#shareManage").data("scheduleId")
    $.ajax({
        url: '/scheduleShare/scheduleShareManage.do ',
        data: {
            scheduleId: scheduleId,
            currentPageNo: currentPageNo,
            pageSize: pageSize
        },
        success: function (res) {
            var data = res.data
            var shares  = data.memoScheduleShares
            var pageInfo= data.pageInfo
            //设置分页
            var cur     = pageInfo["currentPageNo"],
                total   = pageInfo["totalPage"]
            setPage( $("#ye_shareManage"), cur, total, "shareManage") ;
            if (shares) {
                var shareManageList = ''
                for (var i in shares) {
                    var sharePeople = shares[i].memoScheduleShareRecipientsHashSet
                    var sharePeopleArr = []
                    for(var j in sharePeople) {
                        sharePeopleArr.push(sharePeople[j].recipientUserName)
                    }
                    shareManageList +=   '<tr>'+
                                        '<td>' + formatTime(shares[i].createTime, true) + '</td>' +
                                        '<td>共' +sharePeopleArr.length +'人：' + sharePeopleArr.join('、')  + '</td>'+
                                        '</tr>'
                }
                $(".shareManageList tbody").html(shareManageList)
            }
        }
    })

}

// let groupUuid=null;
// bounce_Fixed2.oldCancle = bounce.cancel;
// bounce_Fixed2.cancel = function () {
//     if(groupUuid!=null) {
//         $.ajax({url: $.webRoot+'/uploads/removeFilesByGroup.do', data: {groupUuid: groupUuid,userId: sphdSocket.user.userID}, beforeSend: function () {}, error: function () {}});
//     }
//     bounce_Fixed2.oldCancle();
// }
// creator: 李玉婷，2019-08-14 13:31:03，初始化上传图片
function initUpload(){
    var groupUuid = sphdSocket.uuid();
    //初始化新增日程的图片上传
    $('#uploadImgSect').Huploadify({
        headers:{token:auth.getToken()},
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"上传照片",
        formData:{
            module: '备忘与日程',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $('#uploadImgSect').attr("groupUuid", groupUuid)
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
                //name = file.name,           //文件名称

            $("#newEvent").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
            var imgStr =
                '<div class="pictureWall">' +
                '    <span fileUid="' + data.fileUid + '" onclick="cancleThis($(this))">x</span> ' +
                '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                '</div>';

            $('#uploadImgSect').before(imgStr);
        }
    });
    //历史日程补充记录的图片上传
    $('#uploadImgSupply').Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"上传照片",
        formData:{
            module: '备忘与日程',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader: $.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $('#uploadImgSupply').attr("groupUuid", groupUuid)
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                name = file.name,           //文件名称
                imgStr = '';
            $("#extraRecord").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
            imgStr =
                '<div class="pictureWall">' +
                '    <span fileUid="' + data.fileUid + '" onclick="cancleThis($(this))">x</span> ' +
                '    <div class="filePic" data-path="' +path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                '</div>';

            $('#uploadImgSupply').before(imgStr);
        }
    });
    //修改
    $('#uploadImgSect_updateEvent').Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"上传照片",
        formData:{
            module: '备忘与日程',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader: $.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            $("#updateEvent").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =
                '<div class="pictureWall">' +
                '    <span fileUid="' + data.fileUid + '" onclick="cancleThis($(this))">x</span> ' +
                '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                '</div>';

            $('#uploadImgSect_updateEvent').before(imgStr);
        }
    });
}
// creator: 李玉婷，2019-08-22 11:25:33，查看的公用方法
function eventSeeInit(eventData, isMain) {
    $("#seeEvent .images").html("");
    var memoImage = eventData['memoScheduleImageHashSet'] || eventData['memoScheduleImageHistoryHashSet'];
    var tip = getTips(eventData, isMain)
    $("#seeEvent .eventInfo").html(tip)
    var freqType = Number(eventData.freqType)
    var state = Number(eventData.state)
    $("#seeEvent .repetitive").html(cycleType(freqType));
    $("#seeEvent .activeStartDate").html(new Date(eventData['activeStartDate']).format('yyyy/MM/dd hh:mm'));
    $("#seeEvent .title").html(eventData['title']);
    $("#seeEvent .description").html(eventData['description']);
    if(memoImage.length > 0){
        var imgStr = '';
        var len = memoImage.length - 1;
        for (var ig in memoImage){
            var num = 1 + Number(ig);
            imgStr +=
                '<a path="' + memoImage[ig].normal + '" onclick="imgViewer($(this))">' + num + '</a>';
            if (len != ig) imgStr += '、';
        }
        $("#seeEvent .images").html(imgStr);
    }
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.attr('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");
}

// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    var list = []
    selector.parents(".bonceContainer").find("[groupUuid]").each(function () {
        list.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(list, true)
}
laydate.render({elem: '#activeStartDate', festival: true, format: 'yyyy/MM/dd'});
laydate.render({elem: '#activeStartDate1', type: 'month', festival: true, format: 'yyyy/MM'});
laydate.render({elem: '#activeStartDate_updateEvent', festival: true, format: 'yyyy/MM/dd'});
laydate.render({elem: '#activeStartDate1_updateEvent', type: 'month', festival: true, format: 'yyyy/MM/dd'});







