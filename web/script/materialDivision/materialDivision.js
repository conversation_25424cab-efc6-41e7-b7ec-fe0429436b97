$(function () {
    getPickerList()
    $(".byDepart").on("click",".levelAcon",function(){
        $(this).next().toggle();
    });
    $(".materialCollector").on("click", 'button', function () {
        var name = $(this).data("name")
        switch(name) {
            case 'setting':
                $("#getNum").html("")
                $("#cancelNum").html("")

                // 提交的时候需要用的id
                var thisId = $(this).parents("tr").data("id")
                var thisName = $(this).parents("tr").find("td").eq(0).html()

                $("#scanSet").data("userId", thisId)
                $(".pickerName").html(thisName)

                // 获取材料列表
                $.ajax({
                    url: "../picking/getMtList.do",
                    data: {
                        userID: thisId
                    },
                    success: function (res) {
                        var success = res.success;
                        var data = res.data
                        if (success === 1) {
                            // levelAndContentList 首次赋值

                            levelAndContentList = data
                            let isUpdate = initData(levelAndContentList); // 大于零表示修改
                            $("#scanSet").data("isupdate", isUpdate);
                            $("#allRight").data("type", "mt"); // 直接展示全部材料
                            $("#nowRight").data("type", "user"); // 切换为按分类展示
                            let info = {'key':"havRight", 'val':1};
                            var count = countSet(levelAndContentList, info);
                            $(".selectedNum").html(count);
                            chargeState();
                            $("#changeTypeLeft").html("直接展示全部材料");
                            $("#changeTypeRight").html("切换为按分类展示");
                            bounce.show($("#scanSet"));

                        } else {
                            layer.msg("获取数据失败，请刷新重试！")
                        }
                    }
                })
                break
            case 'cancelAuthority':
                bounce.show($("#tip"))
                var that = $(this)
                $("#tip .tipMsg").html("确定取消该职工领料的权限吗？")
                $("#tip .sureBtn").unbind().on("click", function () {
                    $.ajax({
                        url: '../coreSetting/deleteSpecialUser.do',
                        data: {
                            userId: that.parents("tr").data("id"),
                            coreCode: 'pickingPerson'
                        },
                        success: function (res) {
                            var status = res.status
                            if (status === 1) {
                                layer.msg("操作成功")
                                getPickerList()
                                bounce.cancel()
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break
        }
    })
    $("#allRight").on("click" , "li>div.mtName" , function () {
        changeVal($(this), "left");
    });
    $("#nowRight").on("click" , "li>div.mtName" , function () {
        changeVal($(this), "right");
    });
    $(".btnLink").click(function(){
        var type = $(this).data("type");
        if(type == "allClear"){ // 全部清空
            setVal(levelAndContentList, { "havRight":0 } , true)
            $(".selectedNum").html(countSet(levelAndContentList, {'key':"havRight", 'val':1}));
            chargeState();
        }else if(type == "allSelect"){ // 全选
            setVal(levelAndContentList, { "havRight":1}, true)
            $(".selectedNum").html(countSet(levelAndContentList, {'key':"havRight", 'val':1}));
            chargeState();
        }
    })
})

var userArr = [] ; // 存储查看设置之前的人员
var levelAndContentList = null; // 系统操作一套数据

// creator: 张旭博，2021-05-19 14:25:32，获取领料者列表
function getPickerList() {
    $.ajax({
        url: '../picking/getCoreUser.do',
        success: function (res) {
            var data = res.data
            var str = ''
            for (var i in data) {
                str +=  '<tr data-id="'+data[i].userID+'">' +
                        '   <td>' + data[i].userName + '</td>'+
                        '   <td>' + data[i].mobile + '</td>'+
                        '   <td>' + chargeNull(data[i].departName) + '</td>'+
                        '   <td>' + chargeNull(data[i].postName) + '</td>'+
                        '   <td>' + data[i].roleCreateUserName + ' ' + formatTime(data[i].roleCreateDate, true) + '</td>'+
                        '   <td>' + data[i].remark + '个</td>'+
                        '   <td>' +
                        '       <button class="ty-btn ty-btn-blue ty-circle-2" data-name="setting">设置</button>'+
                        '       <button class="ty-btn ty-btn-blue ty-circle-2" data-name="cancelAuthority">取消本权限</button>'+
                        '   </td>'+
                        '</tr>'
            }
            $(".materialCollector tbody").html(str)
        }
    })
}

// ------------------新增领料着弹窗------------------

// creator: 张旭博，2021-05-19 14:29:25，新增领料者 - 按钮
function addInputer(obj) {
    $(".byAll").html("").removeClass('isSelect').hide();
    $(".byDepart").html("").removeClass('isSelect').hide();
    $('.selectBtn').attr('status', '0');
    var name = obj.data('name');
    bounce.show($("#addMaterialCollector"));
    switch (name) {
        case 'picker':
            $(".addRightTtl").html('增加有领料权限的职工');
            $("#addMaterialCollector").data('code','gb');
            $(".addInputerSure").data('name','picker');
            break;
    }
}

// creator: 李玉婷，2019-05-20 11:24:14，全部员工职工列表
function byAllType(obj){
    var code = $("#addPayInputer").data('code');
    obj.next().toggle().addClass('isSelect');
    obj.attr('status','1');
    if($(".departSelectBtn").attr("status") == '1'){
        $(".byDepart ul").html("");
        $(".departSelectBtn").attr("status",'0');
        $(".departSelectBtn").next().toggle().removeClass('isSelect');
    }
    var html = '';
    $.ajax({
        url: "../picking/getPickingUserList.do",
        data: {
            code: 'pickingPerson'
        },
        success: function (data) {
            var stuffDate = data.data;
            if(stuffDate && stuffDate.length > 0){
                for(var s=0;s<stuffDate.length;s++){
                    html +=
                        '<li class="stuff" val="0">' +
                        '   <i class="fa fa-user-circle-o"></i>'+
                        '   <span class="fixname">'+ stuffDate[s].userName +'</span>' +
                        '   <span>'+ stuffDate[s].mobile +'</span>' +
                        '   <div class="ty-checkbox">' +
                        '       <input type="checkbox" name="isLeave" id="id'+stuffDate[s].userID+'" value="'+stuffDate[s].userID+'">' +
                        '       <label for="id'+stuffDate[s].userID+'"></label>' +
                        '   </div>'+
                        '</li>'
                }
                $(".byAll").html(html);
            }
        }
    })
}

// creator: 李玉婷，2019-05-20 11:24:52，获取按部门排序的职工列表
function byDepartType(obj){
    var code = $("#addPayInputer").data('code');
    obj.next().toggle().addClass('isSelect');
    obj.attr('status','1');
    if($(".allSelectBtn").attr("status") == '1'){
        $(".byAll").html("");
        $(".allSelectBtn").attr("status",'0');
        $(".allSelectBtn").next().toggle().removeClass('isSelect');
    }
    var html = '';
    $.ajax({
        url: "../coreSetting/getOrgListUserList.do",
        data: {
            coreCode: 'pickingPerson'
        },
        success: function (data) {
            var stuffDate = data.data;
            if(stuffDate && stuffDate.length > 0){
                html = '<ul>';
                for(var i in stuffDate){
                    var userList = stuffDate[i]["userList"]; // 人员列表
                    var kidList = stuffDate[i]["subList"]; // 部门列表
                    html += '<li class="levelA"><div class="levelAcon">'+ stuffDate[i].name +'</div>';
                    if(kidList && kidList.length > 0){ // 遍历子级部门
                        html += getUserList( kidList );
                    }
                    if(userList && userList.length > 0){ // 遍历员工
                        html += '<div style="display: none;">';
                        for(var j in userList){
                            html +=
                                '<div class="stuff" val="0">' +
                                '   <i class="fa fa-user-circle-o"></i>'+
                                '   <span class="fixname">'+ userList[j].userName +'</span>' +
                                '   <span>'+ userList[j].mobile +'</span>' +
                                '   <div class="ty-checkbox">' +
                                '       <input type="checkbox" name="isLeave" id="user'+userList[j].userID+'" value="'+userList[j].userID+'">' +
                                '       <label for="user'+userList[j].userID+'"></label>' +
                                '   </div>'+
                                '</div>';
                        }
                        html += '</div>';
                    }
                    html += '</li>';
                }
                html += '</ul>';
                $(".byDepart").html(html);
            }
        }
    })
}

// creator: 李玉婷，2019-05-20 11:25:30，获取子级字符串
function getUserList(arr){
    if(arr && arr.length > 0){
        var partStr = '<ul style="display: none;">';
        for(var i in arr){
            var userList = arr[i]["userList"]; // 人员列表
            var kidList = arr[i]["subList"]; // 部门列表
            partStr += '<li class="levelA"><div class="levelAcon">'+ arr[i].name +'</div>';
            if(kidList && kidList.length > 0){ // 遍历子级部门
                partStr += getUserList( kidList );
            }
            if(userList && userList.length > 0){ // 遍历员工
                partStr += '<div style="display: none;">';
                for(var j in userList){
                    partStr +=
                        '<div class="stuff" val="0" onclick="checkTab($(this))">' +
                        '   <i class="fa fa-user-circle-o"></i>'+
                        '   <span class="fixname">'+ userList[j].userName +'</span>' +
                        '   <span>'+ userList[j].mobile +'</span>' +
                        '   <div class="ty-checkbox">' +
                        '       <input type="checkbox" name="isLeave" id="depart'+userList[j].userID+'" value="'+userList[j].userID+'">' +
                        '       <label for="depart'+userList[j].userID+'"></label>' +
                        '   </div>'+
                        '</div>';
                }
                partStr += '</div>';
            }
            partStr += '</li>';
        }
        partStr += '</ul>';
        return partStr;
    }
}

// creator: 张旭博，2021-06-07 15:16:41，新增领料着 - 确定
function addInputerSure() {
    var checkLen = $("#addMaterialCollector input[type='checkbox']:checked").length;
    if(checkLen <= 0){
        $(".tipMs").html('您至少需选择一位职工后才可点击确定！');
        bounce_Fixed.show($("#tip"));
        return false;
    }
    var idArr = []
    $("#addMaterialCollector input[type='checkbox']:checked").each(function () {
        var id = $(this).val()
        idArr.push(id)
    })
    $.ajax({
        url: "../../coreSetting/addManyUsers.do",
        data: {
            coreCode: 'pickingPerson',
            userId: idArr.join(",")
        },
        success: function (data) {
            var status = data.status;
            if(status === 1){
                layer.msg("操作成功！")
                getPickerList();
                bounce.cancel();
            } else {
                layer.msg("返回值错误")
            }
        }
    })
}

// ------------------设置权限弹窗------------------

function initData(treeData, pName){
    let allCount = 0
    pName = pName || ""
    for(let i = 0 ; i < treeData.length; i++){
        let count = 0
        let mtId = treeData[i]['id']
        let pDepartname = treeData[i]['name']
        let mtList = treeData[i]["mtList"]; // 材料列表
        let subDeparList = treeData[i]["subList"]; // 材料分类列表
        let sonPName = pName + pDepartname + ">>"

        if(mtList && mtList.length>0) {
            for(let j = 0; j < mtList.length; j++){
                let isok = mtList[j]['isHave'] == 1 ;
                mtList[j]['mtId'] = mtId ;
                mtList[j]['mtName'] = sonPName ;
                mtList[j]['havRight'] = mtList[j]['isHave']
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += initData(subDeparList, sonPName);
        }
        treeData[i]['count'] = count;
        allCount += count ;
        treeData[i]['leftOpen'] = false;
        treeData[i]['rightOpen'] = false;
    }
    return allCount;
}

// create : hxz 2020-10-16 获取树里某键值对应的 人数
function countSet(treeData, info){
    let key = info['key']
    let val = info['val']
    let allCount = 0 ;
    for(let i = 0 ; i < treeData.length; i++){
        var count = 0
        var mtList = treeData[i]["mtList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(mtList && mtList.length>0) {
            for(let j = 0; j < mtList.length; j++){
                let isok = mtList[j][key] == val ;
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += countSet(subDeparList, info);
        }
        if(key == 'havRight'){ // 统计是否有权限的人数赋值给部门count键，方便页面读取
            treeData[i]['count'] = count;
        }0
        allCount += count;
    }
    return allCount;
}

function chargeState(){
    var leftType = $("#allRight").data("type");
    var rightType = $("#nowRight").data("type");
    if( leftType==="mt"){
        var str1 = renderDataByType(1, levelAndContentList, 1);
        $("#allRight").html(str1) ;
    }else if( leftType==="user"){
        var str1 = renderDataByType(2, levelAndContentList, 1);
        $("#allRight").html(str1) ;
    }
    if( rightType==="mt"){
        var str2 = renderDataByType(1, levelAndContentList, 2);
        $("#allRight").html(str1) ;
        $("#nowRight").html(str2) ;
    }else if( rightType==="user"){
        var str2 = renderDataByType(2, levelAndContentList, 2);
        $("#nowRight").html(str2) ;
    }
    let isupdate = $("#scanSet").data("isupdate");
    if(isupdate > 0){ // 修改的可以展示第二行
        var cancelStr = cancelOrGetStr(levelAndContentList, 1);
        var getStr = cancelOrGetStr(levelAndContentList, 2);
        if(cancelStr.length < 10 && getStr.length < 10){
            $(".getCon").hide();
            $(".cancelCon").hide();
            $(".changeCon").hide();
            $("#cancelNum").html(0)
            $("#getNum").html(0)
        }else {
            $(".changeCon").show();
        }
        if(cancelStr.length > 10){
            let info = {'key':"changeSta", 'val':1};
            let cancelNum = countSet(levelAndContentList, info)
            $("#cancelNum").html(cancelNum);
            $(".cancelCon").show();
            $("#cancelRight").html(cancelStr) ;
        }else{
            $(".cancelCon").hide();
            $("#cancelNum").html(0);
        }
        if(getStr.length > 10){
            let info = {'key':"changeSta", 'val':2};
            let getNum = countSet(levelAndContentList, info)
            $("#getNum").html(getNum);
            $(".getCon").show();
            $("#getRight").html(getStr);
        }else {
            $(".getCon").hide();
            $("#getNum").html(0);

        }
    }else{
        $(".getCon").hide();
        $(".cancelCon").hide();
        $(".changeCon").hide();
        $("#cancelNum").html(0)
        $("#getNum").html(0)
    }
}

/* creator : 侯杏哲 2017-12-04 设置权限 - 取消按钮  */
function cancelChangeRight(type){
    let isNew = $("#isNew").val();
    if(isNew != ""){
        if(isNew === "same"){
            bounce.show($(".bounce-newSameClass"));
        }else{
            bounce.show($(".bounce-newClass"));
        }
    }else{
        bounce.cancel()
    }
}
/* creator : 侯杏哲 2020-10-23 设置权限 - 确定按钮  */
function sureChangeRight(){
    // 判断选中的是否跟原来一样
    let RchangeightNum = diffCharge(levelAndContentList) ;
    if(RchangeightNum == 0){
        let havRightUser = getRightUser(levelAndContentList) ;
        let str = "当前选择的材料与原来完全相同！"
        if(havRightUser.addMt.length === 0 && havRightUser.delMt.length === 0){
            str = "您还没有选择任何材料！"
        }
        layer.msg(str)
        return false ;
    }
    let havRightUser = getRightUser(levelAndContentList) ;
    let a = havRightUser.addMt.map(v => v.name)
    $.ajax({
        url: "../picking/submitMtGrant.do",
        data: {
            userID: $("#scanSet").data("userId"),
            addIds: havRightUser.addMt.map(v => v.id).join(","),
            deleteIds: havRightUser.delMt.map(v => v.id).join(",")
        } ,
        success: function (data) {
            var success = data["success"] ;
            if( success == 1 ){
                bounce.cancel();
                layer.msg("设置成功") ;
                getPickerList()
            }else{
                bounce_Fixed.show( $("#fixed_tip") ) ;
                $("#fixed_tip .text").html( "设置失败！" ) ;
                $("#fixed_tip .sureBtn").unbind().on("click" ,function () {
                    bounce_Fixed.cancel()
                })
            }
        }
    })
}

/* creator: 侯杏哲 2020-10-16 获取有权限的职工  */
function getRightUser(treeData) {
    let havRightUser = {
        addMt: [],
        delMt: [],
    } ;
    for(let i = 0 ; i < treeData.length; i++){
        let addMt = []
        let delMt = []
        var mtList = treeData[i]["mtList"]; // 人员列表
        var subList = treeData[i]["subList"]; // 部门列表
        if(mtList && mtList.length>0) {
            for(let j = 0; j < mtList.length; j++){
                if(mtList[j].changeSta === 2){
                    addMt.push(mtList[j]);
                }
                if(mtList[j].changeSta === 1){
                    delMt.push(mtList[j]);
                }
            }
        }
        let sonHav = []
        if(subList && subList.length>0){
            sonHav = getRightUser(subList)
        }
        if(addMt.length > 0){
            havRightUser.addMt.push(...addMt);
        }
        if(delMt.length > 0){
            havRightUser.delMt.push(...delMt);
        }
        if(sonHav.addMt){
            havRightUser.addMt.push( ...sonHav.addMt);
            havRightUser.delMt.push( ...sonHav.delMt);
        }
    }
    return havRightUser;
}


// create :hxz 2020-10-13 切换展示员工列表和部门
function changeType(thisObj , location) {
    let type = thisObj.html() == "直接展示全部材料" ? "user" : "mt" ;
    let html = thisObj.html() == "直接展示全部材料" ? "切换为按分类展示" : "直接展示全部材料" ;
    thisObj.html(html);
    if(location == "left"){
        $("#allRight").data("type", type);
    }else{
        $("#nowRight").data("type", type);
    }
    chargeState();
}

// creator: 侯杏哲 2020-10-16  计算有权限变化的人员数目
function diffCharge(treeData) {
    let allCount = 0 ;
    for(let i = 0 ; i < treeData.length; i++){
        var count = 0
        var userList = treeData[i]["mtList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j]['changeSta'] == 1 || userList[j]['changeSta'] == 2 ;
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += diffCharge(subDeparList);
        }
        treeData[i]['count'] = count;
        allCount += count;
    }
    return allCount;
}

//  create :hxz 2020-10-13 渲染一四象限的数据
function renderDataByType(type, data, state) {
    // type : 1-按部门展示2-展示全部职工； data：数据； state:1-左边的2-右边的
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var hasKids = false ;
            var leftOpen = data[i]["leftOpen"];
            var rightOpen = data[i]["rightOpen"];
            var mtList = data[i]["mtList"]; // 人员列表
            var kidList = data[i]["subList"]; // 部门列表

            if( (kidList && kidList.length > 0) || (mtList && mtList.length > 0) ){ // 有子级
                hasKids = true ;
                if(type === 1){ // 按部门展示
                    if(state === 1){ // -左边的
                        var cartStr = " fa-caret-right";
                        var showStr = "";
                        if(leftOpen){
                            cartStr = " fa-caret-down";
                            showStr = " style='display:block;'";
                        }
                        strAll +=   '<li>' +
                                    '   <div class="mtName">' +
                                    '       <i class="fa'+ cartStr +'"></i>' +
                                    '       <span>'+ data[i]["name"] +'</span> ' +
                                    '       <span class="ctrl" data-type="mt" data-id="'+ data[i].id +'" onclick="plus($(this), event)">全选</span>' +
                                    '   </div>' +
                                    '   <ul'+ showStr +'>';
                    }else{ // 右边的
                        var cartStr = " fa-caret-right";
                        var showStr = "";
                        if(rightOpen){
                            cartStr = " fa-caret-down";
                            showStr = " style='display:block;'";
                        }
                        strAll +=   '<li>' +
                                    '   <div class="mtName">' +
                                    '       <i class="fa'+ cartStr +'"></i>' +
                                    '       <span>'+ data[i]["name"] +'</span> ' +
                                    '       <span data-id="'+ data[i].id +'" class="ctrl">已选材料：'+ data[i].count +'个</span>' +
                                    '   </div>' +
                                    '   <ul'+ showStr +'>';
                    }
                }
            }else{ // 无子级
                if(type === 1) { // 按部门展示
                    if(state === 1) { // -左边的
                        var cartStr = " fa-caret-right";
                        if(leftOpen){
                            cartStr = " fa-caret-down";
                        }
                        strAll +=   '<li>' +
                                    '   <div class="mtName">' +
                                    '       <i class="fa'+ cartStr +'"></i>' +
                                    '       <span>'+ data[i]["name"] +'</span> ' +
                                    '       <span class="hd">'+ JSON.stringify(data[i]) +'</span> ' +
                                    '       <span class="ctrl" data-type="mt" data-id="'+ data[i].id +'" onclick="plus($(this), event)">全选</span>' +
                                    '   </div>';
                    }else{ // 右边的
                        var cartStr = " fa-caret-right";
                        if(rightOpen){
                            cartStr = " fa-caret-down";
                        }
                        strAll +=   '<li>' +
                                    '   <div class="mtName">' +
                                    '       <i class="fa'+ cartStr +'"></i>' +
                                    '       <span>'+ data[i]["name"] +'</span> ' +
                                    '       <span class="hd">'+ JSON.stringify(data[i]) +'</span> ' +
                                    '       <span data-id="'+ data[i].id +'" class="ctrl">已选材料：'+ data[i].count +'个</span>' +
                                    '   </div>';
                    }
                }
            }
            if(kidList && kidList.length > 0){ // 遍历子级部门
                strAll += renderDataByType(type, kidList, state) ;
            }
            if(mtList && mtList.length > 0){ // 遍历员工
                for(var j in mtList){
                    var itemInfo2 = { "type":"user", "id":mtList[j]["id"] }; // 提示作用
                    let isO = (mtList[j]['havRight']===0)
                    if(state === 1) { // -左边的
                        if(isO){ // 没有权限的
                            strAll +=   '<li data-type="user" title="'+ mtList[j]['mtName'] +'" data-mtid="'+ mtList[j].category +'" data-id="'+ mtList[j].id  +'" onclick="plus($(this), event)">' +
                                        '   <div>' +
                                        '      <i class="fa fa-gear"></i>' +
                                        '      <span>'+ mtList[j]["name"] +'</span> ' +
                                        '      <span class="hd">'+ JSON.stringify(mtList[j]) +'</span> ' +
                                        '   </div>' +
                                        '</li>' ;
                        }
                    } else{ // 右边的
                        if(!isO){ // 有权限的
                            strAll +=   '<li title="'+ mtList[j]['mtName'] +'">' +
                                        '   <div>' +
                                        '      <i class="fa fa-gear"></i>' +
                                        '      <span>'+ mtList[j]["name"] +'</span> ' +
                                        '      <span class="hd">'+ JSON.stringify(mtList[j]) +'</span> ' +
                                        "      <span data-type='user' data-id='"+ mtList[j]["id"] +"' class='ctrl fa fa-times' onclick='minus($(this), event)'></span>"+
                                        '   </div>' +
                                        '</li>' ;
                        }
                    }
                }
            }
            if(hasKids){
                strAll += "</ul></li>" ;
            }else{
                strAll += "</li>" ;
            }
        }

    }
    return strAll ;
}

//  create :hxz 2020-10-13 渲染二三象限的数据
function cancelOrGetStr(data, state) {
    // data：数据； state:1-第二行左边 2-第二行右边
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var mtList = data[i]["mtList"]; // 人员列表
            var kidList = data[i]["subList"]; // 部门列表
            if(kidList && kidList.length > 0){
                strAll += cancelOrGetStr(kidList, state);
            }
            let method = "minus";
            if(state == 1){ method = "plus"; }
            if(mtList && mtList.length > 0){
                for(var j in mtList){
                    var itemInfo2 = { "type":"user", "id":mtList[j]["id"] };
                    if(state == mtList[j]["changeSta"]){
                        strAll +=   '<li title="'+  mtList[j]['mtName'] +'">' +
                                    '   <div>' +
                                    '      <i class="fa fa-gear"></i>' +
                                    '      <span>'+ mtList[j]["name"] +'</span> ' +
                                    "      <span data-type='user' data-id='"+ mtList[j]["id"] +"' class='ctrl fa fa-times' onclick='"+ method +"($(this), event)'></span>"+
                                    '   </div>' +
                                    '</li>' ;
                    }
                }
            }
        }
    }
    return strAll ;
}

// creator: 张旭博，2021-06-04 16:11:35，增加权限（某一级类别 或者 内容）
function plus(thisObj,event){
    event.stopPropagation();
    var type = thisObj.data('type');
    var id   = thisObj.data('id');
    let info = {
        type: type ,
        id: id,
        havRight: 1
    } ;
    setVal(levelAndContentList, info);

    // 获取有权限的人数
    var count = countSet(levelAndContentList, { key: "havRight", val: 1});
    $(".selectedNum").html(count);
    chargeState();
}

// creator: 张旭博，2021-06-04 16:12:28，删除权限（某一级类别 或者 内容）
function minus(thisObj,event){
    event.stopPropagation();
    var type = thisObj.data('type');
    var id   = thisObj.data('id');
    let info = {
        type: type ,
        id: id,
        havRight: 0
    } ;
    setVal(levelAndContentList, info);
    
    // 获取有权限的人数
    var count = countSet(levelAndContentList, { key: "havRight", val: 1});
    $(".selectedNum").html(count);
    chargeState();
}

// create:hxz 2020-10-13 设置值
function changeVal(thisObj, location){
    thisObj.next().toggle();
    var mtId = thisObj.children(".ctrl").data("id");
    setOpenVal(location,mtId, levelAndContentList);
    var iObj = thisObj.children("i") ;
    var hasRi = iObj.hasClass("fa-caret-right");
    if(hasRi){
        iObj.attr("class","fa fa-caret-down")
    }else{
        iObj.attr("class","fa fa-caret-right")
    }
}
function setOpenVal(location ,mtId,treeData){
    for(let i = 0 ; i < treeData.length; i++){
        var item_mtID =  treeData[i]["id"];
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(Number(item_mtID) === Number(mtId)){
            var isOpen = treeData[i][location + 'Open']
            treeData[i][location + 'Open'] = !isOpen;
        }
        if(subDeparList && subDeparList.length>0){
            setOpenVal(location,mtId,subDeparList)
        }
    }
}

// creator: 张旭博，2021-05-27 10:35:23，根据选中的内容给其他数据赋值
function setVal(treeData, info, allselect){
    // info:选中的数据（部门/用户 ， id ，要设置的权值）
    // allselect 是否全选，如果是部门全选，则子部门和下面的人都全选
    var type = info['type'];
    var id = info['id'];
    for(let i = 0 ; i < treeData.length; i++){
        var mtID =  treeData[i]["id"]
        var mtList = treeData[i]["mtList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(mtList && mtList.length>0) {
            for(var u = 0; u < mtList.length; u++){
                let userItem = mtList[u];
                var uid = userItem["id"];
                if((uid == id && type == "user") || allselect || (type =="mt" && mtID == id)){
                    userItem['havRight'] = info['havRight'] ; // 用户有权限（本次设置的）1-有 ， 0-没有
                    if(userItem["isHave"] == 1){ // 已有权限
                        if( userItem['havRight'] === 1){
                            userItem['changeSta'] = 0; // 原来有，现在有
                        }else{
                            userItem['changeSta'] = 1; // 原来有，现在没有 （第二行左边的）
                        }
                    }else{
                        if( userItem['havRight'] === 1){
                            userItem['changeSta'] = 2; // 原来没有，现在有（第二行右边的）
                        }else{
                            userItem['changeSta'] = 0; // 原来没有，现在没有
                        }
                    }
                    mtList[u] = userItem ;
                }
            }
        }
        treeData[i]["mtList"] = mtList
        if(subDeparList && subDeparList.length>0){
            if(allselect || (mtID == id && type == "mt")){
                setVal(treeData[i]["subList"], info, true);
            }else{
                setVal(treeData[i]["subList"], info);
            }
        }
    }
}

// creator: 张旭博，2021-06-07 10:13:25，格式化空值
function chargeNull(text) {
    return text === null? '--': text
}