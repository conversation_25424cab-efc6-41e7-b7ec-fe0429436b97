


/*
	searchfun 点击查询使用的方法
	yearStar 下拉框开始的时间
	yearEnd 下拉框结束的时间
	isSetTimeInterval 是否手动设置默认显示时间
	todayBool 非手动设置显示时间时，true 表示当前时间到月末，false 表示当前月1号到月末
	start_obj 手动 开始的时间
	end_obj 手动 结束的时间

*/

function DateLI(searchfun, yearStar,yearEnd,isSetTimeInterval,todayBool,start_obj,end_obj){

	dateconBirth(searchfun, yearStar,yearEnd);
	// 监听时间区间，使日期合法
	// setInterval(function(){  roleRightday(); },20);
	
	// 设置日期的默认值  // 获取当前日期
	var myDate = new Date();	
	var y = myDate.getFullYear();  	   
	var m = myDate.getMonth()+1;		
	var d = myDate.getDate();	
	if (isSetTimeInterval) {
		var syar_y = start_obj.start_y;
		var syar_m = start_obj.start_m;
		var syar_d = start_obj.start_d;
		var end_y = end_obj.end_y;
		var end_m = end_obj.end_m;
		var end_d = end_obj.end_d;
		setAutoday(syar_y,syar_m,syar_d,end_y,end_m,end_d);

	} else{
		setCurdate(y,m,d,todayBool);
	};	

}
// 手动设置默认时间时
// syar_y : 开始年
// syar_m : 开始月
// syar_d : 开始日
// end_y : 结束年
// end_m : 结束月
// end_d : 结束日
function setAutoday(syar_y,syar_m,syar_d,end_y,end_m,end_d){
	$(".start_y").val(syar_y); 	
	$(".end_y").val(end_y); 
	$(".start_m").val(syar_m); 	
	$(".end_m").val(end_m); 
	$(".start_d").val(syar_d); 	
	$(".end_d").val(end_d);
}

// START：start_y， start_m， start_d   
// END：end_y, end_m, end_d  
// searchMonthList IS A SEARCH METHOD 
function dateconBirth(searchMoth, yearStar,yearEnd){
	var str1 = "<div class='dateLcon'><select class='start_y' onchange='changeYear()'></select><span>年</span><select class='start_m' onchange='changeMonth()'>"+
		"</select><span>月</span><select class='start_d'></select><span>日</span> "+
		" <span class='dateL_to'> 至 </span><select class='end_y' ></select><span>年</span><select class='end_m' >" +
		"</select><span>月</span><select class='end_d' ></select><span>日</span><span class='ty-btn ty-btn-blue ty-btn-big ty-circle-5' "+
		" onclick='"+ searchMoth +"()'>查询</span></div>" ;
	$(".dateL").append(str1) 

	/*INSERT YEAR MONTH AND DAY */
	for (var i = yearStar; i <= yearEnd; i++) {
		var str2 = "<option value='"+ i +"'>"+ i +"</option>";
		$(".start_y").append(str2); 	
		$(".end_y").append(str2); 	
	};	
	for (var j = 1; j < 13; j++) {
		var str3 = "<option value='"+ j +"'>"+ j +"</option>";
		$(".start_m").append(str3); 	
		$(".end_m").append(str3); 									
	};
	for (var g = 1; g < 32; g++) {
		var str4 = "<option value='"+ g +"'>"+ g +"</option>";
		if (g == 29) {
			var str4 = "<option class='m2' value='29'>29</option>";
		}else if(g == 30){
			var str4 = "<option class='m4' value='30'>30</option>";
		}else if(g == 31){
			var str4 = "<option class='m5' value='31'>31</option>";
		};			
		$(".start_d").append(str4); 	
		$(".end_d").append(str4); 									
	};
    //
	// var cssStr="<link rel='stylesheet' href='dateL.css'/>";
	// $("head").append(cssStr); 
}
// changeYear
function changeYear(){
	$(".end_y").val($(".start_y").val());
	$(".start_m").val("1");
	$(".end_m").val("12");
	$(".start_d").val("1"); 
	var chargeData_1 = chargeRun($(".end_y").val(),$(".end_m").val());
	$(".end_d").val(chargeData_1.dayNum) ;
    roleRightday();
}
// changeMonth
function changeMonth(){
	$(".end_m").val($(".start_m").val());
	$(".start_d").val("1"); 
	var chargeData_1 = chargeRun($(".end_y").val(),$(".end_m").val());
	$(".end_d").val(chargeData_1.dayNum) ;
    roleRightday();
}
// SET CURRENT DATE , 
// IF bool is true star_d = cur , else true star_d = 1
function setCurdate(y,m,d,bool){
	$(".start_y").val(y); 	
	$(".end_y").val(y); 
	$(".start_m").val(m); 	
	$(".end_m").val(m); 
	if (bool){
		$(".start_d").val(d); 	
	} else{
		$(".start_d").val("1"); 	
	};	

	var chargeData= chargeRun(y,m);
	$(".end_d").val(chargeData.dayNum);

}

// CHARGE IF IT'S RUN AND  MONTHDAY
function chargeRun(y,m){
	var isrun_f = 0;
	var g_f = y%4; var w_f = y%100; var e_f = y%400;
	if((g_f == 0&& w_f != 0) || (e_f == 0)){ isrun_f = 1; } 
	if(isrun_f == 1){
		// 闰年
		if(m==1||m==3||m==5||m==7|| m==8||m==10||m==12){ d=31; }
		else if(m == 4||m == 6||m == 9||m == 11){ d = 30; }
		else if(m ==2){  d=29;}
	}else{
		// 非闰年
		if(m == 1||m == 3||m == 5||m == 7|| m == 8||m == 10||m == 12){ d = 31; }
		else if(m == 4||m == 6||m == 9||m == 11){ d = 30; }
		else if(m == 2){  d = 28;}
	}
	return {isRun:isrun_f, dayNum:d }
}

// TIMEINTERVAL ABOUT THE START AND END 
function roleRightday(){
	console.log("校验一次") ;
	var y_star = $(".start_y").val();
	var m_star = $(".start_m").val();
	var d_star = $(".start_d").val();
	var y_end = $(".end_y").val();
	var m_end = $(".end_m").val();
	var d_end = $(".end_d").val();
	var chargeData_1= chargeRun(y_star,m_star);
	var chargeData_2= chargeRun(y_end,m_end);
	var d_1 = chargeData_1.dayNum;
	var d_2 = chargeData_2.dayNum;
	if (d_1 == 28) {
		if (parseInt(d_star)>28) {
			$(".start_d").val("28");
		}  
		$(".start_d").children(".m2").hide();
		$(".start_d").children(".m4").hide();
		$(".start_d").children(".m5").hide();
	} else if(d_1 == 29){
		if (parseInt(d_star)>29) {
			$(".start_d").val("29");
		} 
		$(".start_d").children(".m2").show();
		$(".start_d").children(".m4").hide();
		$(".start_d").children(".m5").hide();
	}else if (d_1 == 30) {
		if (parseInt(d_star)>30) {
			$(".start_d").val("30");
		} 
		$(".start_d").children(".m2").show();
		$(".start_d").children(".m4").show();
		$(".start_d").children(".m5").hide();
	}else {
		$(".start_d").children(".m2").show();
		$(".start_d").children(".m4").show();
		$(".start_d").children(".m5").show();
	} ;

	if (d_2 == 28) {
		if (parseInt(d_end)>28) {
			$(".end_d").val("28");
		} 
		$(".end_d").children(".m2").hide();
		$(".end_d").children(".m4").hide();
		$(".end_d").children(".m5").hide();
	} else if(d_2 == 29){
		if (parseInt(d_end)>29) {
			$(".end_d").val("29");
		} 
		$(".end_d").children(".m2").show();
		$(".end_d").children(".m4").hide();
		$(".end_d").children(".m5").hide();
	}else if (d_2 == 30) {
		if (parseInt(d_end)>30) {
			$(".end_d").val("30");
		} 
		$(".end_d").children(".m2").show();
		$(".end_d").children(".m4").show();
		$(".end_d").children(".m5").hide();
	}else if (d_2 == 31){
		$(".end_d").children(".m2").show();
		$(".end_d").children(".m4").show();
		$(".end_d").children(".m5").show();
	} ;


}


























