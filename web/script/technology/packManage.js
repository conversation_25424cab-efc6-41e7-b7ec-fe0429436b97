var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
$(function () {
    showMainCon(1);
    $('body').on('click', '.linkBtn, .funbtn, .redLinkBtn, .faBtn', function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    });
    $(".modelPattern").click(function () {
        if ($(this).find('.fa-circle-o').length > 0) {
            let code = $(this).children().data("icon");
            let faObj = $(this).find('.fa')
            $(this).siblings(".modelPattern").children('.fa').attr('class' , 'fa fa-circle-o')
            faObj.attr('class' , 'fa fa-circle');
            if (code === 1) {
                $(".sectCon").show()
                $(".sectCon2").addClass("hrLine");
            } else {
                $(".sectCon").hide();
            }
        } else {
            $(".sectCon").hide();
            $(this).find('.fa').attr('class' , 'fa fa-circle-o')
        }
    });
    $("#searchKeyBase1").val("");
    getPackList();
    $(".clearInputVal").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            $(this).prev().focus();
        }
    });
})
//creator:lyt 2024/3/12 15:46 已设置的商品
function getPackList(currPage){
    showMainCon(1);
    let searchKey = $("#searchKeyBase1").val();
    $("#packList tbody tr:gt(0)").remove();
    getWszNum();
    $.ajax({
        "url": '../packing/getPackagingList.do',
        "data": {
            "pageSize": 20,
            "currentPageNo": 1
        },
        success:function(data){
            let list =  data.data || [];
            let str = ``;
            list.forEach((item)=>{
                if (searchKey === "" || item.outerSn.includes(searchKey) || item.outerName.includes(searchKey)) {
                    str += `<tr>
                    <td>${ item.outerSn }</td>
                    <td>${ item.outerName }/${ item.specifications }/${ item.model }</td>
                    <td>${ item.unit }</td>
                    <td>${ item.customerName || '' }</td>
                    <td>${ item.gs}</td>
                    <td class="ty-td-control">
                        <span data-fun="scanPackage" class="ty-color-blue funbtn">查看</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                `;
                }
            })
            $("#packList").find("tbody").append(str);
        }
    })
}
/*creator:lyt 2024/3/12 0012 下午 6:15 */
function scanPackage(obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    spanObj = obj;
    getPackagingDetail(info.id, info.packagingState);
    drawingDetailPage(2);
    $(".sc_commodityInfo").html(`${ info.outerName }/${ info.outerSn }/${ info.specifications }/${ info.model}/${ info.unit }`);
}
function getWszNum(){
    $.ajax({
        "url": '../packing/getWszNum.do',
        success:function(res){
            $("#wszNum").html(res.data);
        }
    })
}
function getUnpackList(currPage){
    showMainCon(2);
    let searchKey = $("#searchKeyBase2").val();
    $("#commodityListN tbody tr:gt(0)").remove();
    $.ajax({
        "url": '../packing/getWszPackagingList.do',
        "data": {
            "pageSize": 20,
            "currentPageNo": 1
        },
        success:function(data){
            let list =  data.data || [];
            let str = ``;
                list.forEach((item)=> {
                    if (searchKey === "" || item.outerSn.includes(searchKey) || item.outerName.includes(searchKey)) {
                        str += `
                        <tr>
                            <td>${item.outerSn}</td>
                            <td>${item.outerName}/${item.specifications}/${item.model}</td>
                            <td>${item.unit}</td>
                            <td>${item.customerName || ''}</td>
                            <td>${item.createName} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td class="ty-td-control">
                                <span data-fun="packingSetting" class="ty-color-blue funbtn">设置</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                            </td>
                        </tr>
                `
                }
            })
            $("#commodityListN").find("tbody").append(str);
        }
    })
}

// creator: 李玉婷，2024-02-27 08:07:22，包装设置
let spanObj = null;
function packingSetting(obj){
    spanObj = obj;
    let info = JSON.parse(obj.siblings(".hd").html());
    if (!info.packagingState || info.packagingState === 0) {
        $(".sectCon").hide();
        $(".sectCon1").show();
        $(".sectCon2").removeClass("hrLine");
        $("#commodityPacking").data("type", 'add').data("source", 1);
        $("#commodityPacking input").val("");
        $("#commodityPacking select").val("");
        $("#commodityPacking .hd").html("");
        $(".majorName").html(`尚未选择`);
        $(".subName").html(`尚未选择`);
        $(".majorPackBtn").data("type", "add").html(`选择主要包装物`);
        $("#commodityPacking").find(".fa").attr("class", "fa fa-circle-o");
        bounce.show($("#commodityPacking"));
        $("#commodityPacking .matUnit").html(info.unit);
        $("#commodityPacking .modWeightUnit").html('').data("val", "");
        $("#commodityPacking .bounce_title").html("包装设置");
        $("#packageAll tr:gt(3)").remove();
        getUnitList($("#slct_unit"));
    } else {
        drawingDetailPage(1);
        getPackagingDetail(info.id, info.packagingState);//packagingState，0未设置，1设置中 2设置完成
        $(".sc_commodityInfo").html(`${ info.outerName }/${ info.outerSn }/${ info.specifications }/${ info.model}/${ info.unit }`);
    }
}
//creator:lyt 2024/2/29 14:40 包装设置确定
function commodityPackingOk(){
    let type = $("#commodityPacking").data("type");
    let source = $("#commodityPacking").data("source");
    let spanInfo = JSON.parse(spanObj.siblings(".hd").html());
    if ((source === 1 && $(".modeSelect .fa-circle").length > 0) || source === 2) {
        if (source === 1) {
            let icon = $(".modeSelect .fa-circle").data("icon");
            if (icon === 2) {
                noNeedPacking();
                return false;
            }
        }
        let able = true;
        $("#packageAll .majorPack").each(function (){
            let str = $(this).html();
            if (str === "") {
                able = false;
                return false;
            }
        });
        $("#packageAll :input[require]").each(function (){
            let str = $(this).val();
            if (str === "") {
                able = false;
                return false;
            }
        });
        if (able) {
            $("#outerBase .outer_form input").val("");
            $("#outerBase select").val("3");
            $(".effectTimeCon").hide().find("#outerEffect").val("");
            $("#outerBase input:radio[name='outerShape']:checked").prop("checked", false);
            bounce.show($("#outerBase"));
            if (source === 2 && type === 'update'){
                let info = JSON.parse(linkObj.siblings(".packItemInfo").html());
                $("#outerBase [require]").each(function (){
                    let name = $(this).attr("name");
                    $(this).val(info[name]);
                });
                if (spanInfo.packagingState === 2) {
                    $(".effectTimeCon").show();
                }
                if (info.outerShape) {
                    $("#outerBase input:radio[name='outerShape'][value='"+ info.outerShape + "']")[0].checked = true;
                }
            }
        } else {
            layer.msg("您还有必填项未完成！");
        }
    } else {
        layer.msg("请选择商品是否需要包装");
    }
}
//creator:lyt 2024/3/20 0020 下午 8:57 */
function noNeedPacking(){
    let info = JSON.parse(spanObj.siblings(".hd").html());
    $.ajax({
        url: '../packing/addPackaging.do',
        data: {"productId": info.id, 'list': '[]'},
        success: function (res) {
            getPackagingDetail(res.data, 1);
            drawingDetailPage(1);
            $(".sc_commodityInfo").html(`${ info.outerName }/${ info.outerSn }/${ info.specifications }/${ info.model}/${ info.unit }`);
            bounce.cancel()
        }
    })
}
//creator:lyt 2024/3/7 10:32 商品最外层包装的信息
function outerBaseOk(){
    let type = $("#commodityPacking").data("type");
    let source = $("#commodityPacking").data("source");//1=未设置包装-包装设置，2=设置了-新增包装方式、编辑包装方式
    let icon = $(".modeSelect .fa-circle").data("icon");
    let info = JSON.parse(spanObj.siblings(".hd").html());
    let packList = [], url = `../packing/addPackaging.do`;
    let json = {"productId": info.id};
    let funStr = ``;
    $("#outerBase [require]:visible").each(function(){
        var name = $(this).attr("name");
        if (name==='outerLength' || name==='outerWidth' || name==='outerHeight') {
            if ($(this).val() !== "") json[name] = $(this).val();
        } else {
            json[name] = $(this).val();
        }
    });
    json.lengthUnit = $("#outerBase select[name='lengthUnitId'] option:selected").text();
    json.widthUnit = $("#outerBase select[name='widthUnitId'] option:selected").text();
    json.heightUnit = $("#outerBase select[name='heightUnitId'] option:selected").text();
    if ($("#outerBase input:radio[name='outerShape']:checked").length > 0 ) json.outerShape = $("#outerBase input:radio[name='outerShape']:checked").val();
    if (source === 2 || source === 1 && icon === 1) {
        $(".packageItem").each(function(index){
            var data = {
                "product": info.id,
                "level": index + 1,
                "productCount": $(this).next().next().find(":input[name='productCount']").val(),
                "zyPackaging": JSON.parse($(this).find(".majorPack").html())
            };
            if ($(this).next().find(".subPack").html() !== "") {
                data.itemList = JSON.parse($(this).next().find(".subPack").html())
            }
            packList.push(data);
        });
        json.list = JSON.stringify(packList);
    }
    if (source === 2 && type === 'update') {
        if (info.packagingState === 2) {
            let time = $("#outerEffect").val();
            if (!time || time === ""){
                layer.msg("请选择日期");
                return false;
            }
            json.effectTime = time;
        }
        if ($(".mainCon3").is(":visible")) {
            json.id = linkObj.parents(".mainOnlyRow").data("id");
        } else {
            json.id = linkObj.parents(".packageScanItem").data("id");
            funStr = `scanMorePacking`;
        }
        url = `../packing/updatePackaging.do`;
    }
    $.ajax({
        url: url,
        data: json,
        success: function (res) {
            if (source === 1) {
                drawingDetailPage(1);
                $(".sc_commodityInfo").html(`${ info.outerName }/${ info.outerSn }/${ info.specifications }/${ info.model}/${ info.unit }`);
            }
            getPackagingDetail(info.id, info.packagingState || 1, funStr);
            bounce.cancel()
        }
    })
}
// creator: 李玉婷，2024-02-27 11:11:16，选择主要包装物/辅助物
function selectPackage(obj){
    let type = obj.data("type");
    let source = obj.data("source");
    let pckId = ``, ttl = ``;
    $(".unitVal").html("");
    $(".usageUnitName").html("");
    $("#selectPackage input").val("");
    $("#selectPackage select").val("");
    $("#selectPackage").data("obj", obj);
    $("#selectPackage").data("source", source);
    $("#selectPackage").data("type", type);
    $("#weightUnit1").val("2");
    $("#weightUnit2").val("2");
    if (source === 1) {
        ttl = `选择/更换本层包装的主要包装物`;
        $("#packageName").prop("disabled", false);
    } else if (source === 2) {
        ttl = `增加本层包装的辅助包装物`;
        $("#packageName").prop("disabled", false);
    } else if (source === 3) {
        if (type === 'add') {
            ttl = `增加本层包装的辅助包装物`;
            $("#packageName").prop("disabled", false);
        } else {
            ttl = `选择/更换本层包装的主要包装物`;
            $("#packageName").prop("disabled", true);
        }
    }
    $("#selectPackage .bonceHead span").html(ttl);
    if (type === 'update') {
        let detail = ``;
        if (source === 1) {
            detail = JSON.parse(obj.parents("tr").find(".majorPack").html());
        } else {
            detail = JSON.parse(obj.siblings(".hd").html());
        }
        pckId = detail.material;
        $("#selectPackage .usageUnitName").html(handleNull(detail.usageUnit))
        $("#selectPackage input").each(function(){
            let name = $(this).attr("name");
            $(this).val(handleNull(detail[name]));
        });
        $("#selectPackage select").each(function(){
            let name = $(this).attr("name");
            $(this).val(handleNull(detail[name]));
        });
        weightCount($(":input[name='stockWeight']"), 2);
    }
    getPackageList($("#packageName"), pckId);
    bounce_Fixed.show($("#selectPackage"));
}
// creator: 李玉婷，2024-02-28 08:03:22，选择主要包装物-确定
function selectPackageSure(){
    let packId = $("#packageName").val();
    let unit = $("#slct_unit").val();
    let amount = $("#selectPackage :input[name='ratedAmout']").val();
    if (packId === "" || unit === "" || amount === "") {
        layer.msg("还有必填项尚未填写!")
        return false;
    }
    let obj = $("#selectPackage").data("obj");
    let type = $("#selectPackage").data("type");
    let source = $("#selectPackage").data("source");
    let curP =  obj.parents("tr");
    let packInfo = $("#packageName option:selected").data("info");
    delete packInfo.id;
    let data = {
        "material": packId,
        "usageUnit": "",
        "stockWeightUnit": "",
        "usageWeightUnit": "",
        "code":packInfo.code,
        "name":packInfo.name,
        "specifications":packInfo.specifications,
        "model":packInfo.model,
        "unit":packInfo.unit,
        "unitId":packInfo.unitId,
        "memo":packInfo.memo,
        "weightReferenceUnit": $(".weightReferenceUnit").data("type")
    }
    $("#otherBase input").each(function(){
        let val = $(this).val();
        let name = $(this).attr("name");
        if (name === 'stockWeight' || name === 'usageWeight') {
            if (val !== "") data[name] = val;
        } else {
            data[name] = val;
        }
    });
    $("#otherBase select").each(function(){
        let val = $(this).val();
        let name = $(this).attr("name");
        data[name] = val;
        if (name === 'usageUnitId' && val !== "") {
            data.usageUnit = $(this).children("option:selected").text();
        }
    });
    if ($("#weightUnit1 option").val() !== "") {
        data.stockWeightUnit =  $("#weightUnit1 option:selected").text();
    }
    if ($("#weightUnit2 option").val() !== "") {
        data.usageWeightUnit =  $("#weightUnit2 option:selected").text();
    }
    if (source === 1) {
        if (curP.find(".majorPackBtn").html() === '选择主要包装物') {curP.find(".majorPackBtn").html(`更换主要包装物`);}
        curP.find(".majorPackBtn").data("type", "update");
        curP.find(".majorPack").html(JSON.stringify(data));
        curP.find(".majorName").html(`${packInfo.code}/${packInfo.name}/${packInfo.model}/${packInfo.specifications}/${packInfo.unit}`);//包装物代号/名称/型号/规格/计量单位
        productNumCount(curP.next().next().find(":input[name='productCount']")[0]);
    } else if (source === 2) {
        let subList = curP.find(".subPack").html() !== ""?JSON.parse(curP.find(".subPack").html()): [];
        data.order = subList.length + 1;
        subList.push(data);
        curP.find(".subPack").html(JSON.stringify(subList));
        curP.find(".subName").html(`已选${subList.length}种`);
        productNumCount(curP.next().find(":input[name='productCount']")[0]);
    } else if (source === 3) { //查看/管理本层包装的辅助包装物
        let subList1 = scanObj.siblings(".subPack").html() !== ""?JSON.parse(scanObj.siblings(".subPack").html()): [];
        if (type === 'add') {
            data.order = subList1.length + 1;
            subList1.push(data);
        } else if (type === 'update') {
            let order = curP.data("order");
            let index = subList1.findIndex(item => Number(item.order) === Number(order))
            data.order = order;
            subList1[index] = data;
        }
        scanObj.siblings(".subPack").html(JSON.stringify(subList1));
        scanObj.siblings(".subName").html(`已选${subList1.length}种`);
        scanObj.click();
        productNumCount(scanObj.parents("tr").next().find(":input[name='productCount']")[0]);
    }
    bounce_Fixed.cancel();
}
// creator: 李玉婷，2024-02-28 11:03:22，选择主要包装物-返回
function backCancel(){
    let source = $("#selectPackage").data("source");
    if(source === 3) {
        bounce_Fixed.show($("#scanSubPack"));
    } else {
        bounce_Fixed.cancel();
    }
}
//creator:lyt 2024/3/14 10:15 详情页面某些按钮、段落显示设置
function drawingDetailPage(type){
    showMainCon(3)
    if (type === 1) {
        $("#backPreNum").val(2);
        $(".tjAll").show().find("i").attr("class","fa fa-circle-o faBtn");
        $(".sc_mode1").show();$(".sc_mode2").hide();
        $(".delPackage").show();$(".susPackage").hide();$(".updateRecord").hide();
    } else {
        $("#backPreNum").val(1);
        $(".tjAll").hide();
        $(".sc_mode1").hide();$(".sc_mode2").show();
        $(".delPackage").hide();$(".susPackage").show();$(".updateRecord").show();
    }
}
//creator:lyt 2024/3/14 09:08 本商品的包装已设置完毕确定
function packingFinishSure(){
    let isSelect = $("#sflag").hasClass('fa-dot-circle-o');
    if(isSelect){
        let info = JSON.parse(spanObj.siblings(".hd").html());
        $.ajax({
            'url':`../packing/submitSettings.do`,
            'data': {"productId": info.id},
            success:function(res){
                getPackList(1)
                getUnpackList(1);
                showMainCon(2);
            }
        })
    }else{
        layer.msg('请确认本商品的包装已设置完毕')
    }
}
// creator: 李玉婷，2024-02-28 08:45:46，增加包装物
function addPackage(){
    $("#addPackage input").val("");
    $("#addPackage select").val("");
    drawingUnitStr($("#buyingUnit"));
    bounce_Fixed2.show($("#addPackage"));
}
// creator: 李玉婷，2024-02-28 09:25:55，增加包装物-确定
function addPackageOk(){
    let code = $("#addPackage :input[name='code']").val();
    let name = $("#addPackage :input[name='name']").val();
    let unit = $("#buyingUnit").val();
    if (code === "" || name === "" || unit === "") {
        layer.msg("还有必填项尚未填写!")
        return false;
    }
    var data = {};
    $("#addPackage input").each(function(){
        let val = $(this).val();
        let name = $(this).attr("name");
        data[name] = val;
    });
    data.unitId = $("#buyingUnit").val();
    data.unit = $("#buyingUnit option:selected").text();
    $.ajax({
        url: '../packing/addBzw.do',
        data: data,
        success: function (res) {
            getPackageList($("#packageName"));
            bounce_Fixed2.cancel()
        }
    })
}
// creator: 李玉婷，2024-02-28 10:32:18，获取包装物
function getPackageList(curObj, selectedID){
    $.ajax({
        url: '../packing/getBzwList.do',
        success: function (res) {
            let list = res.data || [];
            let str = `<option value=''>请选择</option>`;
            let seleStr = ``
            for(var i in list){
                seleStr = ``;
                if(selectedID == list[i]["id"]) { seleStr = " selected" }
                str +=
                    `<option value='${list[i].id}' ${seleStr} data-info='${JSON.stringify(list[i])}'>${list[i].code}</option>`;
            }
            curObj.html(str);
        }
    })
}
//creator:lyt 2024/2/29 更换包装物
function changePackageName(obj){
    let val = obj.val();
    if (val === "") {
        $("#packageSelect input:disabled").val("");
    } else {
        let data = obj.children(":selected").data("info");
        if (data) {
            $("#packageSelect input:disabled").each(function (){
                let name = $(this).attr("name");
                $(this).val(data[name]);
            })
        }
    }
}
//creator:lyt 2024/3/12 08:55 包装信息查看页面
function getPackagingDetail(productId, type, funStr){
    let str = ``;
    let spanInfo = JSON.parse(spanObj.siblings(".hd").html());
    $.ajax({
        "url": '../packing/getPackagingDetail.do',
        "data": {
            "productId": productId
        },
        success:function(data){
            let isPacking = data.data.isPacking;//isPacking=0  无需包装
            let list = data.data.list || [];
            let susList =  data.data.suspendList || [];
            let arr = [];
            arr.push(...susList,...list);
            if (arr && arr.length > 0) {
                for (let n=0;n<arr.length;n++) {
                    let info =  arr[n] || {}, icon = -1;
                    let structureList = info.structureList || [];
                    info.packagingState= type;//packagingState，0未设置，1设置中 2设置完成
                    structureList.forEach((item, index)=>{
                        let netW=``, netU=``, grossAuto=``, netAuto=``, total=0;
                        item.zyPackaging.volum = ""
                        item.zyPackaging.weightReference = ""
                        if (handleNull(item.zyPackaging.ratedAmout) !== "" && handleNull(item.zyPackaging.stockWeight) !== "") {
                            let step = item.zyPackaging.ratedAmout * item.zyPackaging.stockWeight;
                            let refer = autoWeight(step, item.zyPackaging.stockWeightUnitId);
                            item.zyPackaging.weightReference = refer.val;
                            item.zyPackaging.weightReferenceUnit = refer.type;
                            let num1 = chargeWeight(step, item.zyPackaging.stockWeightUnitId);
                            total += num1;
                        }
                        if (handleNull(item.zyPackaging.weightReference) !== "" && handleNull(item.zyPackaging.stockWeightUnitId) !== "" && handleNull(item.zyPackaging.usageWeightUnit) !== "" && handleNull(item.zyPackaging.usageWeight) !== "") {
                            let num1 = chargeWeight(item.zyPackaging.weightReference, item.zyPackaging.weightReferenceUnit);
                            let num2 = chargeWeight(item.zyPackaging.usageWeight, item.zyPackaging.usageWeightUnitId);
                            let count = Math.floor(num2 / num1);
                            item.zyPackaging.volum = count;
                        }
                        for(let i=0;i<item.itemList.length;i++){
                            item.itemList[i].order = i+1;
                            item.itemList[i].volum = ""
                            item.itemList[i].weightReference = ""
                            if (handleNull(item.itemList[i].ratedAmout) !== "" && handleNull(item.itemList[i].stockWeight) !== "") {
                                let step = item.itemList[i].ratedAmout * item.itemList[i].stockWeight;
                                let refer = autoWeight(step, item.itemList[i].stockWeightUnitId);
                                let num1 = chargeWeight(step, item.itemList[i].stockWeightUnitId);
                                item.itemList[i].weightReference = refer.val;
                                item.itemList[i].weightReferenceUnit = refer.type;
                                total += num1;
                            }
                            if (handleNull(item.weightReference) !== "" && handleNull(item.stockWeightUnitId) !== "" && handleNull(item.usageWeightUnit) !== "" && handleNull(item.usageWeight) !== "") {
                                let num1 = chargeWeight(item.itemList[i].weightReference, item.itemList[i].weightReferenceUnit);
                                let num2 = chargeWeight(item.itemList[i].usageWeight, item.itemList[i].usageWeightUnitId);
                                let count = Math.floor(num2 / num1);
                                item.itemList[i].volum = count;
                            }
                        }
                        if (index === 0) {
                            netW = item.productCount * spanInfo.netWeight;
                            netU = spanInfo.weightUnit;
                            item.goodsNum = item.productCount;
                        } else {
                            netW = structureList[index-1].grossWeight;
                            netU = structureList[index-1].grossUnitId;
                            item.goodsNum = item.productCount * structureList[index-1].goodsNum;
                        }
                        netW = chargeWeight(netW, netU);//转为毫克
                        let tal = item.productCount * Number(netW);
                        grossAuto = autoWeight(tal + Number(total), 1);
                        netAuto = autoWeight(tal, 1);
                        item.netWeight = netAuto.val;
                        item.netUnit = charge(netAuto.type, 'weightUnit');
                        item.netUnitId = netAuto.type;
                        item.grossWeight = grossAuto.val;
                        item.grossUnit = charge(grossAuto.type, 'weightUnit');
                        item.grossUnitId = grossAuto.type;
                    })
                    if (Number(arr[n].enabled) === 0) {
                        icon = susList.findIndex(value => Number(value.id) === Number(arr[n].id))
                        if (icon > -1) susList[icon] = arr[n];
                    } else if (Number(arr[n].enabled) === 1) {
                        icon = list.findIndex(value => Number(value.id) === Number(arr[n].id))
                        if (icon > -1) list[icon] = arr[n];
                    }
                }
            }
            if (Number(isPacking) === 0) {
                $(".mainOnlyRow").hide();
            } else {
                if (list && list.length > 0) {
                    let ttl = '';
                    let info =  list[0] || {};
                    let structureList = info.structureList || [];
                    structureList.forEach((item, index)=> {
                        if (structureList.length > 1 && index === 0) {
                            ttl = '最小包装';
                        } else if (index === structureList.length - 1) {
                            ttl = '最外层包装';
                        } else {
                            ttl = `最小包装的上${index + 2}层包装`;
                        }
                        str += `<div class="pckRow">
                                        <span class="gapTtl">${ttl}</span>
                                        主要使用${item.zyPackaging.name}包装，辅助包装物共${item.itemList.length}种
                                        <span class="ty-right linkBtn" onclick="scanItemPack($(this))">查看</span>
                                        <span class="hd">${JSON.stringify(item)}</span>
                                    </div>`;
                    });
                    $("#pckLevels").html(str);
                    $("#scanMore").data("type", type);
                    $(".mainOnlyRow").data("id", info.id).show();
                    $(".mainOnlyRow .packItemInfo").html(JSON.stringify(info));
                    $(".createInfo").html(`${info.createName} ${new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss')}`);
                    $(".packageOuter").html(`本包装方式最外层主要使用${structureList[0].zyPackaging.name}包装，长宽高：${info.outerLength || ''}*${info.outerWidth || ''}*${info.outerHeight || ''}，形状：${charge(info.outerShape, 'outerShape')}`);
                    $(".packageOuterOther").html(`本包装方式共${structureList.length}层，装有${structureList[structureList.length-1].goodsNum}${info.unit}本商品，商品净重为${Number(structureList[structureList.length-1].netWeight).toFixed(2)}
                    ${structureList[structureList.length-1].netUnit}，含包装的毛重为${Number(structureList[structureList.length-1].grossWeight).toFixed(2)}${structureList[structureList.length-1].grossUnit}`);
                } else {
                    $(".mainOnlyRow").hide();
                }
            }
            if (type === 2) {
                $(".susPackingList").show();
            } else {
                $(".susPackingList").hide();
            }
            $(".packageKinds").html(list.length);
            $(".susKinds").html(susList.length);
            $("#susPacking").html(JSON.stringify(susList));
            $("#detailCon3").html(JSON.stringify(list));
            switch (funStr){
                case 'scanMorePacking':
                    scanMorePacking($("#scanMore"));
                    break;
                case 'susPackingList':
                    susPackingList();
                    break;
                default:
                    break;
            }
        }
    })
}
//creator:lyt 2024/3/12 09:21 改为无需包装,2=删除、3=停用
function updatePackingTip(obj){ //source:1=改为无需包装,2=删除、3=停用
    let source = obj.data("source");
    let spanInfo = JSON.parse(spanObj.siblings(".hd").html());
    $("#withoutPackageTip").data("source", source);
    switch (source) {
        case 1:
            if (Number(spanInfo.packagingState) === 2) {
                $(".msgCon").html("确定后，在用的包装方式均将停用！");
            } else {
                $(".msgCon").html("确定后，已编辑的包装方式均将消失不见！");
            }
            break;
        case 2:
            $(".msgCon").html("确定删除本种包装方式吗？");
            $("#withoutPackageTip").data("obj", obj);
            break;
        case 3:
            let enabled = Number(obj.data("type"));
            if (enabled === 0) {
                $(".msgCon").html("确定停用这种包装方式吗？");
            } else {
                $(".msgCon").html("确定启用这种包装方式吗？");
            }
            $("#withoutPackageTip").data("obj", obj);
            break;
    }
    bounce.show($("#withoutPackageTip"));
}
//creator:lyt 2024/3/12 10:28 改为无需包装确定
function withoutPackageOk(){
    let info = ``, url = ``,id =``,list = ``,obj = ``, data = {};
    let page = $("#showMainConNum").val();
    let source = $("#withoutPackageTip").data("source");
    let spanInfo = JSON.parse(spanObj.siblings(".hd").html());
    if ( page === "3") {
        list = JSON.parse($("#detailCon3").html());
        if (source !== 1) {
            id = $(".mainOnlyRow").data("id");
            info = list.find(item => Number(item.id) === Number(id))
        }
    } else if ( page === "4") {
        obj = $("#withoutPackageTip").data("obj");
        info = JSON.parse(obj.siblings(".hd").html());
    }
    if (source === 1) {
        info = list[0];
        url = `../packing/addPackaging.do`;
        data ={"productId": spanInfo.id, "list": '[]'}
    } else if (source === 2) {
        data ={"id": info.id}
        url = `../packing/deletePackaging.do`;
    } else if (source === 3) {
        obj = $("#withoutPackageTip").data("obj");
        let enabled = Number(obj.data("type"));
        url = `../packing/stopOrStartPackaging.do`;
        data = {"id": info.id, "enabled": enabled}
    }
    $.ajax({
        url: url,
        data: data,
        success: function (res) {
            bounce.cancel();
            let funStr = ``;
            if (source === 2 && page === '4' || source === 3 && data.enabled === 0) {
                obj.parents(".packageScanItem").remove();
            } else if (source === 3 && data.enabled === 1) {
                funStr = 'susPackingList'
            }
            getPackagingDetail(spanInfo.id,spanInfo.packagingState || 1,funStr);
        }
    })
}
//creator:lyt 2024/3/12 16:32 新增包装方式
function addPacking(){
    let info = JSON.parse(spanObj.siblings(".hd").html());
    $(".sectCon").show();
    $(".sectCon1").hide();
    $(".sectCon2").removeClass("hrLine");
    $("#commodityPacking input").val("");
    $("#commodityPacking select").val("");
    $("#commodityPacking .hd").html("");
    $(".majorName").html(`尚未选择`);
    $(".subName").html(`尚未选择`);
    $(".majorPackBtn").html(`选择主要包装物`).data("type", 'add');
    $("#commodityPacking").data("type", 'add').data("source", 2);
    $("#commodityPacking").find(".fa").attr("class", "fa fa-circle-o");
    $("#commodityPacking .matUnit").html(info.unit);
    $("#commodityPacking .modWeightUnit").html('').data("val", "");
    $("#commodityPacking .bounce_title").html("新增包装方式");
    $("#packageAll tr:gt(3)").remove();
    getUnitList($("#slct_unit"));
    bounce.show($("#commodityPacking"));
}
//creator:lyt 2024/3/13 09:34 查看更多的包装方式
function scanMorePacking(obj){
    let list = JSON.parse($("#detailCon3").html());
    let packagingState = obj.data("type");
    $(".usingCon").show();
    mainCon4Str(list,packagingState, 0);
}
function mainCon4Str(list,packagingState, state){
    let str = ``, ableStr = state === 0? "停用": "启用";
    showMainCon(4);
    list.forEach((item, index)=>{
        let structureList = item.structureList;
        str += `<div class="packageScanItem" data-id="${item.id}">
                                <div class="linkWrap clear">
                                    以下为<span>${item.createName} ${new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}</span>创建的包装方式。
                                    <div class="ty-right">
                                         <span class="linkBtn gap" data-fun="updatePackage">编辑</span>
                                    ${packagingState === 2?'<span class="redLinkBtn susPackage" data-fun="updatePackingTip" data-type="'+ state +'" data-source="3">' + ableStr+'</span>':""}
                                    ${!packagingState || packagingState === 1?'<span class="redLinkBtn delPackage" data-fun="updatePackingTip" data-source="2">删除</span>':""}
                                    ${packagingState === 2?'<span class="linkBtn updateRecord gapLt" data-fun="getUpdateRecord">操作记录</span>':""}
                                    <span class="hd packItemInfo">${JSON.stringify(item)}</span>
                                    </div>   
                                </div>
                                <div class="pckRow">本包装方式最外层主要使用${structureList[0].zyPackaging.name}包装，长宽高：${item.outerLength || ''}*${item.outerWidth || ''}*${item.outerHeight || ''}，形状：${charge(item.outerShape, 'outerShape')}</div>
                                <div class="pckRow">本包装方式共${structureList.length}层，装有${structureList[structureList.length-1].goodsNum}${item.unit}，
                                商品净重为${structureList[structureList.length-1].netWeight}${structureList[structureList.length-1].netUnit}，
                                含包装的毛重为${Number(structureList[structureList.length-1].grossWeight).toFixed(2)}${structureList[structureList.length-1].grossUnit}</div>
                                `
        structureList.forEach((pack, index)=>{
            let ttl = '';
            if (structureList.length > 1 && index === 0) {
                ttl = '最小包装';
            } else if (index === structureList.length - 1){
                ttl = '最外层包装';
            }else{
                ttl = `最小包装的上${index+ 2}层包装`;
            }
            str += `<div class="pckRow">
                            <span class="gapTtl">${ttl}</span>
                            主要使用${pack.zyPackaging.name}包装，辅助包装物共${pack.itemList.length}种
                            <span class="ty-right linkBtn" onclick="scanItemPack($(this))">查看</span>
                            <span class="hd">${JSON.stringify(pack)}</span>
                        </div>`;
        })
        str += `</div>`;
    })
    $("#scanMoreCon").html(str);
}
//creator:lyt 2024/3/14 10:49 编辑包装方式
let linkObj = ``;
function updatePackage(obj){
    let page = $("#showMainConNum").val();
    let id =``,list = ``,info = ``;
    let spanInfo = JSON.parse(spanObj.siblings(".hd").html());
    linkObj = obj;
    if ( page === "3") {
        id = $(".mainOnlyRow").data("id");
        list = JSON.parse($("#detailCon3").html());
        info = list.find(item => Number(item.id) === Number(id))
    } else {
        id = obj.parents(".packageScanItem").data("id");
        info = JSON.parse(obj.siblings(".hd").html());
    }
    $(".sectCon").show();
    $(".sectCon1").hide();
    $(".sectCon2").removeClass("hrLine");
    $("#commodityPacking input").val("");
    $("#commodityPacking select").val("");
    $("#commodityPacking .hd").html("");
    $(".majorName").html(`尚未选择`);
    $(".subName").html(`尚未选择`);
    $("#commodityPacking").data("type", 'update').data("source", 2).find(".fa").attr("class", "fa fa-circle-o");
    bounce.show($("#commodityPacking"));
    $("#commodityPacking .matUnit").html(spanInfo.unit);
    $("#commodityPacking .bounce_title").html("编辑包装方式");
    $("#packageAll tr:gt(3)").remove();
    getUnitList($("#slct_unit"));
    info.structureList.forEach((item, index)=>{
        if(index > 0) {
            addMoreLayer();
        }
        let lastObj = $(".packageItem:last");
        lastObj.find(".majorName").html(`${item.zyPackaging.code}/${item.zyPackaging.name}/${item.zyPackaging.model}/${item.zyPackaging.specifications}/${item.zyPackaging.unit}`);
        lastObj.find(".majorPackBtn").data("type", "update").html(`更换主要包装物`);
        lastObj.find(".majorPack").html(JSON.stringify(item.zyPackaging));
        lastObj.nextAll().eq(0).find(".subPack").html(JSON.stringify(item.itemList));
        lastObj.nextAll().eq(0).find(".subName").html(`已选${item.itemList.length}种`);
        lastObj.nextAll().eq(1).find(":input[name='productCount']").val(item.productCount);
        productNumCount(lastObj.nextAll().eq(1).find(":input[name='productCount']")[0]);
        //lastObj.nextAll().eq(2).find(".modNum").val(item.packagingCount);
    })
}
//creator:lyt 2024/3/14 16:39 查看
function scanItemPack(obj){
    let html = ``;
    let ttl = obj.siblings(".gapTtl").html();
    let info = JSON.parse(obj.siblings(".hd").html());
    let spanInfo = JSON.parse(spanObj.siblings(".hd").html());
    $("#packName").html(ttl);
    $("#packItemWeight").html(info.netWeight+ info.netUnit);
    $("#packItemGrossWeight").html(info.grossWeight+ info.grossUnit);
    $("#scanItemPackList tbody tr:gt(0)").remove()
    if (info.level === 1) {
        $("#packItemCount").html('商品'+info.productCount+ spanInfo.unit);
    } else {
        $("#packItemCount").html(`${info.packagingCount}个上一层的小包装，共包装有商品${info.goodsNum}${spanInfo.unit}`);
    }
    if (info.zyPackaging && info.zyPackaging.id){
        if (handleNull(info.zyPackaging.ratedAmout) !== "" && handleNull(info.zyPackaging.stockWeight) !== "") {
            let refer = autoWeight(info.zyPackaging.ratedAmout * info.zyPackaging.stockWeight, info.zyPackaging.stockWeightUnitId)
            info.zyPackaging.weightReference = refer.val;
            info.zyPackaging.weightReferenceUnit = refer.type
        }
        if (handleNull(info.zyPackaging.weightReference) !== "" && handleNull(info.zyPackaging.stockWeightUnitId) !== "" && handleNull(info.zyPackaging.usageWeightUnit) !== "" && handleNull(info.zyPackaging.usageWeight) !== "") {
            let num1 = chargeWeight(info.zyPackaging.weightReference, info.zyPackaging.weightReferenceUnit);
            let num2 = chargeWeight(info.zyPackaging.usageWeight, info.zyPackaging.usageWeightUnitId);
            let count = Math.floor(num2 / num1);
            info.zyPackaging.volum = count;
        }
        html += `<tr>
                <td>主要包装物</td>
                <td>${info.zyPackaging.code}/${info.zyPackaging.name}/${info.zyPackaging.specifications}/${info.zyPackaging.model}/${info.zyPackaging.unit}</td>
                <td>${info.zyPackaging.ratedAmout}${info.zyPackaging.usageUnit}</td>
                <td>${Number(info.zyPackaging.weightReference).toFixed(2)}${charge(info.zyPackaging.weightReferenceUnit, 'weightUnit')}</td>
                <td>${info.zyPackaging.volum}</td>
            </tr>`;
    }
    info.itemList.forEach((item, index)=>{
        if (handleNull(item.ratedAmout) !== "" && handleNull(item.stockWeight) !== "") {
            let refer = autoWeight(item.ratedAmout * item.stockWeight, item.stockWeightUnitId)
            item.weightReference = refer.val;
            item.weightReferenceUnit = refer.type
        }
        if (handleNull(item.weightReference) !== "" && handleNull(item.stockWeightUnitId) !== "" && handleNull(item.usageWeightUnit) !== "" && handleNull(item.usageWeight) !== "") {
            let num1 = chargeWeight(item.weightReference, item.weightReferenceUnit);
            let num2 = chargeWeight(item.usageWeight, item.usageWeightUnitId);
            let count = Math.floor(num2 / num1);
            item.volum = count;
        }
        html += `<tr>
                <td>辅助包装物</td>
                <td>${item.code}/${item.name}/${item.specifications}/${item.model}/${item.unit}</td>
                <td>${item.ratedAmout}${item.usageUnit}</td>
                <td>${Number(item.weightReference).toFixed(2)}${charge(item.weightReferenceUnit, 'weightUnit')}</td>
                <td>${item.volum}</td>
            </tr>`;
    })
    $("#scanItemPackList tbody").append(html);
    bounce.show($("#scanItemPack"));
}
//creator:lyt 2024/2/29 更换计量单位2*（使用时)
function changeUnitName(obj){
    let val = obj.val();
    if (val === "") {
        $(".usageUnitName").html("");
    } else {
        let wd = obj.children(":selected").text();
        $(".usageUnitName").html(wd);
    }
}

function addUnit(obj) {
    let idStr = obj.data("targ");
    bounce_Fixed3.show($("#addUnit"));
    $("#addUnit input").val("");
    $("#updateType").val(idStr);
}
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed3.cancel();
                var idStr = $("#updateType").val();
                getUnitList($("#" + idStr), module);
                if (idStr === 'buyingUnit') {
                    getUnitList($("#slct_unit"), module, $("#slct_unit").val());
                }
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed4.show($("#tip1"));
            }
        }
    })
}
// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
let unitList = [];
function getUnitList(obj, module, selectedID) {
    // 　1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">请选择</option>';
            if(list && list.length >0){
                unitList = list;
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['name'] +'</option>';
                }
            }
            obj.html(str);
        }
    })
}
function drawingUnitStr (obj, selectedID){
    var str = '<option value="">请选择</option>';
    if(unitList && unitList.length >0){
        for(var i = 0 ; i < unitList.length ; i++){
            var item = unitList[i], seleStr = " ";
            if(selectedID == item["id"]) { seleStr = " selected" }
            str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['name'] +'</option>';
        }
    }
    obj.html(str);
}

// creator: 李玉婷，2024-02-28 08:03:22，增加一层包装
function addMoreLayer(){
    let info = JSON.parse(spanObj.siblings(".hd").html());
    let curObj = $("#packageAll .packageItem:last");
    let major = curObj.find(".majorPack").html();
    let count = curObj.next().next().find(":input[name=\"productCount\"]").val();
    if (major === "" || count === "") {
        layer.msg("您还有必填项未完成！");
        return false;
    }
    let level = $("#packageAll .packageItem").length + 1;
    let html = ` <tr class="packageItem" data-lev="${level}">
                            <td><span class="spTtl">本层包装的主要包装物<span class="red">*</span></span></td>
                            <td class="majorName">尚未选择</td>
                            <td>
                                <span class="redLinkBtn ty-right delThisLayerBtn" data-fun="delThisLayer" data-source="1">删除本层</span>
                            </td>
                            <td colspan="2">
                                <span class="linkBtn ty-right majorPackBtn" data-fun="selectPackage" data-source="1">选择/更换主要包装物</span>
                                <div class="hd majorPack"></div>
                            </td>
                        </tr>
                        <tr>
                            <td><span class="spTtl">本层包装的辅助包装物</span></td>
                            <td>
                                <span class="subName">尚未选择</span>
                                <span class="linkBtn ty-right" data-fun="scanSubPackList">查看/管理</span>
                                <div class="hd subPack"></div>
                            </td>
                            <td colspan="2">
                                <span class="linkBtn ty-right" data-fun="selectPackage" data-source="2">增加辅助包装物</span>
                            </td>
                        </tr>
                        <tr>
                            <td><span class="spTtl">包含几个上一层的小包装<span class="red">*</span></span></td>
                            <td>
                                <input type="text" name="productCount" placeholder="请录入数字" oninput="clearNum(this)" onkeyup="productNumCount(this)" require />
                                个
                            </td>
                            <td>本层包装内商品的净重</td>
                            <td>
                                <input class="modNetWeight" type="text" value="" disabled  />
                                 <span class="modWeightUnit"></span>
                            </td>
                        </tr>
                        <tr>
                            <td>单个包装内商品的数量</td>
                            <td>
                                <input type="text" class="modNum" disabled />
                                <span>${info.unit}</span>
                            </td>
                            <td>本层包装的毛重（含包装）</td>
                            <td>
                                 <input class="modGrossWeight" type="text" disabled/>
                                <span class="modWeightUnit"></span>
                            </td>
                        </tr>`;
    $("#packageAll .delThisLayerBtn").hide();
    $("#packageAll tbody").append(html);
}
//creator:lyt 2024/2/29 13:31 删除本层
function delThisLayer(obj){
    $("#delThisLayer").data("obj", obj);
    bounce_Fixed.show($("#delThisLayer"));
}
function delThisLayerOk(){
    let obj = $("#delThisLayer").data("obj");
    obj.parents(".packageItem").next().remove();
    obj.parents(".packageItem").next().remove();
    obj.parents(".packageItem").next().remove();
    obj.parents(".packageItem").remove();
    $("#packageAll .delThisLayerBtn:last").show();
    bounce_Fixed.cancel();
}
//creator:lyt 2024/2/29 13:31 删除本层
function delTip(obj){
    $("#delTip").data("obj", obj);
    bounce_Fixed.show($("#delTip"));
}
//creator:lyt 2024/2/29 16:51 查看/管理
let scanObj = null;
function scanSubPackList(obj){
    let str = ``, len = 0;
    let list = obj.siblings(".hd").html();
    scanObj = obj;
    $("#scanSubPack tbody tr:gt(0)").remove();
    if (list !== "") {
        list = JSON.parse(list);
        list.forEach((item)=>{
            str +=  `
                    <tr data-order="${item.order}">
                        <td>${item.code}/${item.name}/${item.specifications}/${item.model}/${item.unit}</td>
                        <td>${ item.ratedAmout }</td>
                        <td>${ Number(item.weightReference).toFixed(2) }${ charge(item.weightReferenceUnit, 'weightUnit') }</td>
                        <td>${ item.volum || ""}</td>
                        <td>
                            <span class="ty-color-blue" onclick="selectPackage($(this))" data-source="3" data-type="update">修改</span>
                            <span class="ty-color-red" onclick="delTip($(this))">删除</span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                    </tr>`;
        })
        len = list.length;
        $("#scanSubPack tbody").append(str);
    }
    $("#scanSubPack .subPackLen").html(len);
    bounce_Fixed.show($("#scanSubPack"));
}

//creator:lyt 2024/3/11 11:31 删除
function delScanPackage(){
    let obj = $("#delTip").data("obj");
    let order = obj.parents("tr").data("order");
    let subList1 = scanObj.siblings(".subPack").html() !== ""?JSON.parse(scanObj.siblings(".subPack").html()): [];
    let index = subList1.findIndex(item => Number(item.order) === Number(order))
    subList1.splice(index, 1)
    scanObj.siblings(".subPack").html(JSON.stringify(subList1));
    scanObj.siblings(".subName").html(`已选${subList1.length}种`);
    $("#scanSubPack .subPackLen").html(subList1.length);
    obj.parents("tr").remove();
    productNumCount(scanObj.parents("tr").next().find(":input[name='productCount']")[0])
    bounce_Fixed.show($("#scanSubPack"));
}

//creator:lyt 2024/3/17 09:34 操作记录
function getUpdateRecord(obj){
    let page = $("#showMainConNum").val();
    let id =``;
    if ( page === "3") {
        id = $(".mainOnlyRow").data("id");
    } else {
        id = obj.parents(".packageScanItem").data("id");
    }
    $("#backNum").val(page);
    $("#recordList tbody tr:gt(0)").remove();
    showMainCon(5);
    $.ajax({
        url: '../packing/getPackagingRecordList.do',
        data: {
            "id": id
        },
        success: function (res) {
            let str = ``;
            let list = res.data || [];
            //操作:1-增,2-删,3-改,4-启用,5-停用
            let arr = ['','创建','删','修改','启用','停用']
            list.forEach((item)=>{
                str +=  `
                    <tr>
                        <td>${arr[item.operation]}</td>
                        <td>${ item.createName } ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }</td>
                        <td>${ new Date(item.effectTime).format('yyyy-MM-dd hh:mm:ss') }</td>
                        <td>
                            <span class="ty-color-blue" onclick="updateRecordScan($(this))">查看</span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                    </tr>`;
            })
            $("#recordList tbody").append(str);
        }
    })
}
//creator:lyt 2024/3/17 10:11 操作记录-查看
function updateRecordScan(obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    let spanInfo = JSON.parse(spanObj.siblings(".hd").html());
    showMainCon(6);
    $.ajax({
        url: '../packing/getPackagingRecordDetail.do',
        data: {
            "recordId": info.id
        },
        success: function (res) {
            let str = ``,headStr = ``;
            let data = res.data || {};
            data.structureList.forEach((pack, index) => {
                let ttl = '', netW=``, netU=``, grossAuto=``, netAuto=``, total=0;
                pack.zyPackaging.volum = ""
                pack.zyPackaging.weightReference = ""
                if (handleNull(pack.zyPackaging.ratedAmout) !== "" && handleNull(pack.zyPackaging.stockWeight) !== "") {
                    let step = pack.zyPackaging.ratedAmout * pack.zyPackaging.stockWeight;
                    let refer = autoWeight(step, pack.zyPackaging.stockWeightUnitId)
                    pack.zyPackaging.weightReference = refer.val;
                    pack.zyPackaging.weightReferenceUnit = refer.type
                    let num1 = chargeWeight(step, pack.zyPackaging.stockWeightUnitId);
                    total += num1;
                }
                if (handleNull(pack.zyPackaging.weightReference) !== "" && handleNull(pack.zyPackaging.stockWeightUnitId) !== "" && handleNull(pack.zyPackaging.usageWeightUnit) !== "" && handleNull(pack.zyPackaging.usageWeight) !== "") {
                    let num1 = chargeWeight(pack.zyPackaging.weightReference, pack.zyPackaging.weightReferenceUnit);
                    let num2 = chargeWeight(pack.zyPackaging.usageWeight, pack.zyPackaging.usageWeightUnitId);
                    let count = Math.floor(num2 / num1);
                    pack.zyPackaging.volum = count;
                }
                for(let i=0;i<pack.itemList.length;i++){
                    pack.itemList[i].order = i+1;
                    pack.itemList[i].volum = ""
                    pack.itemList[i].weightReference = ""
                    if (handleNull(pack.itemList[i].ratedAmout) !== "" && handleNull(pack.itemList[i].stockWeight) !== "") {
                        let step = pack.itemList[i].ratedAmout * pack.itemList[i].stockWeight;
                        let refer = autoWeight(step, pack.itemList[i].stockWeightUnitId)
                        pack.itemList[i].weightReference = refer.val;
                        pack.itemList[i].weightReferenceUnit = refer.type
                        let num1 = chargeWeight(step, pack.itemList[i].stockWeightUnitId);
                        total += num1;
                    }
                    if (handleNull(pack.weightReference) !== "" && handleNull(pack.stockWeightUnitId) !== "" && handleNull(pack.usageWeightUnit) !== "" && handleNull(pack.usageWeight) !== "") {
                        let num1 = chargeWeight(pack.itemList[i].weightReference, pack.itemList[i].weightReferenceUnit);
                        let num2 = chargeWeight(pack.itemList[i].usageWeight, pack.itemList[i].usageWeightUnitId);
                        let count = Math.floor(num2 / num1);
                        pack.itemList[i].volum = count;
                    }
                }
                if (index === 0) {
                    netW = pack.productCount * spanInfo.netWeight;
                    netU = spanInfo.weightUnit;
                    pack.goodsNum = pack.productCount;
                } else {
                    netW = data.structureList[index-1].grossWeight;
                    netU = data.structureList[index-1].grossUnitId;
                    pack.goodsNum = pack.productCount * data.structureList[index-1].goodsNum;
                }
                netW = chargeWeight(netW, netU);//转为毫克
                let tal = pack.productCount * Number(netW);
                grossAuto = autoWeight(tal + Number(total), 1);
                netAuto = autoWeight(tal, 1);
                pack.netWeight = netAuto.val;
                pack.netUnit = charge(netAuto.type, 'weightUnit');
                pack.netUnitId = netAuto.type;
                pack.grossWeight = grossAuto.val;
                pack.grossUnit = charge(grossAuto.type, 'weightUnit');
                pack.grossUnitId = grossAuto.type;
                if (data.structureList.length > 1 && index === 0) {
                    ttl = '最小包装';
                } else if (index === data.structureList.length - 1) {
                    ttl = '最外层包装';
                } else {
                    ttl = `最小包装的上${index + 2}层包装`;
                }
                if (index > 0) {
                    pack.productCount *= data.structureList[index-1].productCount;
                }
                str += `<div class="pckRow">
                            <span class="gapTtl">${ttl}</span>
                            主要使用${pack.zyPackaging.name}包装，辅助包装物共${pack.itemList.length}种
                        </div>`;
            })
            headStr= `<div class="pckRow">本包装方式最外层主要使用${data.structureList[0].zyPackaging.name}包装，
长宽高：${data.outerLength || ''}*${data.outerWidth || ''}*${data.outerHeight || ''}，
形状：${charge(data.outerShape, 'outerShape')}</div>
                    <div class="pckRow">本包装方式共${data.structureList.length}层，装有${data.structureList[data.structureList.length-1].goodsNum}${spanInfo.unit}，
                    商品净重为${Number(data.structureList[data.structureList.length-1].netWeight).toFixed(2)}${data.structureList[data.structureList.length-1].netUnit}，
                    含包装的毛重为${Number(data.structureList[data.structureList.length-1].grossWeight).toFixed(2)}${data.structureList[data.structureList.length-1].grossUnit}</div>
                    `
            str = headStr + str +`</div>`;
            $(".recordScan").html(str);
        }
    })


}
//creator:lyt 2024/3/18 15:29 已停用的数据
function susPackingList(){
    let list = JSON.parse($("#susPacking").html());
    $(".usingCon").hide();
    mainCon4Str(list, 2, 1);
}


function toogeCircle(thisObj) {
    if(thisObj.hasClass('fa-circle-o')){
        thisObj.removeClass('fa-circle-o').addClass('fa-dot-circle-o')
    }else{
        thisObj.removeClass('fa-dot-circle-o').addClass('fa-circle-o')
    }
}
/*creator:lyt 2024/5/16 0016 上午 11:13 计算单重1合计自动换算、出材量*/
function stockWeightUnitChange(obj){
    weightCount($(":input[name='stockWeight']"), 2);
    volumCount($("#weight2")[0], 1);
}
//creator:lyt 2024/3/1 13:37 单重1合计
function weightCount(obj, type){
    let val = obj.val();
    let count = 0;
    let name = '';
    if (type === 1) {
        name = 'stockWeight';
    } else {
        name = "ratedAmout"
    }
    if (val === "" || $("input[name="+name+"]").val() === "") {
        $("#weightCount").val("");
        $(".weightReferenceUnit").html("").data("type", "");
    } else {
        let  unit1= $("#weightUnit1").val();
        count = val * $("input[name="+name+"]").val();
        let weight = autoWeight(count, unit1);
        $("#weightCount").val(Number(weight.val).toFixed(2));
        $(".weightReferenceUnit").data("type", weight.type).html(charge(weight.type,'weightUnit'));
    }
    volumCount($("#weight2")[0], 1)
}
function productNumCount(obj){
    let val = $(obj).val();
    let trObj = $(obj).parents("tr");
    let netW = "", netU = "";
    if (val !== ""){
        let tal = 0,total = 0;//本层包装单重1合计
        let grossW = {'val': '', 'type': ''},netAuto ={'val': '', 'type': ''};
        let trIndex = $("#packageAll tr").index(trObj);
        let trArr = ``;
        let spanInfo = JSON.parse(spanObj.siblings(".hd").html());
        if (trIndex <= 2) {
            trArr = $(".packageItem");
        } else {
            trArr = trObj.prevAll().eq(2).nextAll(".packageItem");
        }
        trArr.each(function (){
            let level = $(this).data("lev");
            let num = $(this).next().next().find(":input[name='productCount']").val();
            if (num !== "") {
                let preNum = num;
                let major = $(this).find(".majorPack").html();
                let subList = $(this).next().find(".subPack").html();
                if (level === 1) {
                    netW = num*spanInfo.netWeight;
                    netU = spanInfo.weightUnit;
                } else {
                    netW = $(this).prev().find(".modGrossWeight").val();
                    netU = $(this).prev().find(".modWeightUnit").data("val");
                    preNum = $(this).prev().find(".modNum").val() * num;
                    preNum = Number(preNum).toFixed(2)
                }
                if (major !== "") {
                    major = JSON.parse(major);
                    total += chargeWeight(major.weightReference, major.weightReferenceUnit);
                }
                subList = subList === "" ? []: JSON.parse(subList);
                subList.forEach((item) => {
                    if (item.weightReference !== "") {
                        total += chargeWeight(item.weightReference, item.weightReferenceUnit);
                    }
                })
                netW = chargeWeight(netW, netU);//转为毫克
                tal = num * Number(netW);
                grossW = autoWeight(tal + Number(total), 1);
                netAuto = autoWeight(tal, 1);
                $(this).nextAll().eq(1).find(".modNetWeight").val(Number(netAuto.val).toFixed(2));
                $(this).nextAll().eq(1).find(".modWeightUnit").html(charge(netAuto.type,'weightUnit')).data("val", netAuto.type);
                $(this).nextAll().eq(2).find(".modNum").val(preNum);
                $(this).nextAll().eq(2).find(".modGrossWeight").val(Number(grossW.val).toFixed(2));
                $(this).nextAll().eq(2).find(".modWeightUnit").html(charge(grossW.type,'weightUnit')).data("val", grossW.type);
            } else {
                $(this).find(":input[disabled]").val("");
                $(this).nextAll().find(":input[disabled]").val("");
                return false;
            }
        })
    } else {
        trObj.find(":input[disabled]").val("");
        trObj.nextAll().find(":input[disabled]").val("");
    }
}
/*creator:lyt 2024/3/12 0012 下午 7:18 */
function backPre(num){
    if (num === 9) {
        num = $("#backPreNum").val();
    } else if (num === 8){
        num = $("#backNum").val();
    }
    if (num === 1 || num === '1'){
        getPackList();
    } else if (num === 2 || num === '2'){
        getUnpackList();
    }
    showMainCon(num);
}
// creator: 李玉婷，2022-04-12 08:57:55，显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    num === 1 || num === 2? $(".search input").val(""): "";
    $(".mainCon" + num).show().siblings().hide();
}
//计算出材量
function volumCount(obj, type){
    clearNoNumN(obj, 10);
    let val = $(obj).val();
    let count = 0, val2 = ``;
    if (type === 1) {
        val2 = $("#weightUnit2").val();
    } else if (type === 2){
        val2 = $("#weight2").val();
    }
    let weight1 = $("#weightCount").val();
    let  unit1= $("#weightUnit1").val();
    if (val === "" || !val2 || val2 === "" || weight1 === "" || !unit1 || unit1 === "") {
        $("#volumCon").val("");
    } else {
        let num1 = chargeWeight(weight1, $(".weightReferenceUnit").data("type")); //单重1合计
        let num2 = chargeWeight(val, val2);
        if (type === 2) num2 = chargeWeight(val2, val);
        if (num1 === 0) {
            count = '';
        } else {
            count = Math.floor(num2 / num1);//出材量
        }
        $("#volumCon").val(count);
    }
}
// create:hxz 2020-06-21 翻译键值
function charge(val , type) {
    var str = ""
    switch (type){
        case 'outerShape':
            if(val == "1"){ str = "长方体"; }
            if(val == "2"){ str = "圆柱体"; }
            if(val == "3"){ str = "其它形状"; }
            break;
        case 'weightUnit':
            if(val == "1"){ str = "毫克"; }
            if(val == "2"){ str = "克"; }
            if(val == "3"){ str = "千克"; }
            if(val == "4"){ str = "吨"; }
            break;
    }
    return str
}
function chargeWeight(val , type) { //转为毫克
    var str = 0;
    if (handleNull(val) !== "" && handleNull(type) !== "") str = val * Math.pow(1000, type-1);
    return str;
}
function autoWeight(val , type) { //重量自动换算
    let num = Number(type);
    if (val < 1000 || num === 4){
        return {'val':val, 'type': num};
    }
    ++num;
    return autoWeight(val/ 1000,num);
}
laydate.render({elem: '#outerEffect', min: 1});
