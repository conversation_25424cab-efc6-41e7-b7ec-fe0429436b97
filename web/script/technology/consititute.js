var perNUM = 20 , editObj = null; // editObj - 操作编辑/删除的对象 ； perNUM - 分页的每页条数
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#partEntry"));
bounce_Fixed2.cancel();
bounce_Fixed3.show($("#giveTip6"));
bounce_Fixed3.cancel();
$(function(){
    $("input").each(function(){
        var key = $(this).attr("name")
        $(this).attr("autocomplete",'off')
    });
    getMainData();
    // 页面按钮
    $("span.ty-btn").click(function(){
        var type = $(this).data("type");
        switch (type){
            case 'illustrate': // 查看操作说明
                var num = $(this).data("num");
                bounce.show($("#illustrate" + num)) ;
                break;
            case 'goMain': // 返回构成管理主页
                goMain();
                selectGS = []
                notSelectGS = []
                break;
            case 'goPre': // 返回上一页
                goPre();
                break;
            case 'handle': // mainCon2 去处理
                var navIndex = $(this).data("num");
                $("#handleLog").html(navIndex);//0:待确认加工方，1：待确认构成
                getGsList(navIndex, 1)
                if(navIndex === 0 || navIndex === 1){
                    var idNum = navIndex + 1 ;
                    var option = $("#select" + idNum).children("option:selected").html();
                    $(".twoInFront").show();
                    $(".mainCon3 .nav0").html("  请在下列选项中选择" + option);
                    selectGsCon(0);
                }else {
                    $(".twoInFront").hide();
                    $(".mainCon3 .nav0").html("");
                }
                break;
            case 'split': // mainCon1 去处理
                getGsList(2, 1)
                break;
            case 'nextStep': // 挑选完毕，下一步
                nextStep();
                break;
            case 'submitGs': // 挑选完毕，确定
                submitGs();
                break;
            case 'partEntry': // 录入直接组成该产品的零组件
                bounce_Fixed2.show($("#partEntry")) ;
                $("#addUnitBtn").show();
                $("#partEntry .textMax").html("0/100");
                partEntryBtn();
                $("#innerSn").attr("onclick", "startMatch(event)");
                $("#partEntry .bonceHead span").html("零组件录入");
                $("#partEntry .bonceFoot .bounce-ok").html("录入完毕");
                $("#partEntry [disabled]").removeAttr("disabled");
                getUnitList($("#unitId"), 4)
                break;
            default:
        }
    })
    // mainCon3 二级导航
    $(".mainCon3 .ty-secondTab").children().click(function () {
        selectGsCon($(this).index()) ;
    })
    // mainCon3 选择按钮
    $(".mainCon3 table").on('click', '.fa', function(){
        var klass = $(this).hasClass('fa-check-square-o');
        var item = $(this).parent().siblings(":last").find(".hd").html();
        item = JSON.parse(item) ;
        if(klass){
            $(this).attr("class" , "fa  fa-square-o");
            setKeyByID(item.id ,"selected", 0);
        }else{
            $(this).attr("class" , "fa  fa-check-square-o");
            setKeyByID(item.id ,"selected", 1);
        }
        countGsList();
        $(this).parent().parent().remove();

    });
    // 所有table中的查看
    $("table").on('click', 'span[type="btn"]', function(){
        var type = $(this).data('type');
        var item = JSON.parse($(this).siblings(".hd").html());

        if(type == "scan"){
            var str = itemStr(item,1) +
                "    <td>"+ charge(item['state'], 'state') +"</td>" +
                "    <td>"+ item['processDeptName'] +"</td>" ;
            $("#scanGS tbody>tr:eq(1)").html(str);
            $("#scanGS .memo").html(item['memo']);
            $("#scanGS .create").html(item['createName'] + " " + (new Date(item['createDate'])).format("yyyy-MM-dd hh:mm:ss"));
            bounce.show($("#scanGS"));
        }else if(type == "splitBtn"){
            splitBtn(item , "");

        }else if(type == "splitBtn2"){
            splitBtn(item , "2");

        }else if(type == "partsEdit"){
            bounce_Fixed2.show($("#partEntry")) ;
            $("#addUnitBtn").hide();
            $("#partEntry .bonceHead span").html("零组件修改");
            $("#partEntry .bonceFoot .bounce-ok").html("确定");
            partEntryBtn();
            // $("#innerSn").removeAttr("onclick");
            setGSInfo(item , 1);
        }else if(type == "delBtn"){
            var delTip = '';
            if (item.num > 1) {
                delTip = '<p>本件还参与构成了其他产品或组件。</p><p>确定后，本件将仅不参与本组件的构成！</p>';
            } else {
                delTip = '<p>确定后，本件将彻底删除！</p>';
            }
            $("#giveTip7 #msg").html(delTip);
            bounce_Fixed2.show($("#giveTip7")) ;
            editObj = $(this) ;
        }else if(type == "noSplit"){
            layer.msg("<p>本件在此不可编辑！</p><p>如需编辑，请到“零组件”模块中进行</p>");
        }
    });

    // 零部件录入 选中
    $("#selecGS").on('click', 'option', function(){
        setSelectOption($(this));
        $("#selecGS").hide();
        $(".bounce_Fixed2").stopTime("gsMatch")
    });
    
    $("#process").change(function () {
        setCompoAndProcess();
    })
    $("#composition").change(function () {
        setCompoAndProcess();
    })
});

// create:hxz 2020-06-27 零部件匹配录入限制
function setCompoAndProcess() {
    var process = $("#process").val(),composition = $("#composition").val(), setDis = false;
    if(process == 1){
        $("#composition").val("").attr("disabled" , "true");
    }else if(process == 2 || process == 3){
        $("#composition").removeAttr("disabled");
        if(composition == 2 && process != 1){
            setDis = true
        }
    }
    if(setDis){
        $("#weightUnit").val("2").attr("disabled" , "true");
        $("#netWeight").val("").attr("disabled" , "true");
    }else{
        $("#weightUnit").val("").removeAttr("disabled");
        $("#netWeight").val("").removeAttr("disabled");
    }
}
// create:hxz 2020-06-27 零部件匹配
function startMatch(e){
    e.stopPropagation();
    $(".bounce_Fixed2").everyTime('0.5s',"gsMatch",function(){
        gsMatch();
    });
}
function stopMtch(e){
    $("#selecGS").hide();
    $(".bounce_Fixed2").stopTime("gsMatch")
}
// create:hxz 2020-06-25 返回上一页
function goPreOK() {
    $(".mainCon3").show().siblings().hide();
    selectGsCon(0);
}
function goPre() {
    var show = $(".mainCon3 .ty-secondTab").is(":visible");
    if(show){
        if(selectGS.length >0){
            bounce.show($("#giveTip1"));
        }else{
            goPreOK();
        }
    }else{
        goPreOK();
    }
}
// create:hxz 2020-06-25 返回主页
function goMainOK(){
    $(".mainCon1").show().siblings().hide();
    getMainData();
    selectGS = []
    notSelectGS = []
}
function goMain(){
    var show = $(".mainCon3 .ty-secondTab").is(":visible");
    if(show){
        if(selectGS.length >0){
            bounce.show($("#giveTip1"));
        }else{
            goMainOK();
        }
    }else{
        goMainOK();
    }

}
// create:hxz 2020-06-25 拆分商品/零部件
function splitBtn(item , type) {
    var process = item['process'] ;
    var composition = item['composition'] ;
    if(type == "" || ((process == "2" || process == "3") && composition == "2")){
        getParts(item, type);
        if(type == "2"){
            bounce_Fixed.show($("#splitGS2"));
        }else{
            bounce.show($("#splitGS"));
        }
    }else{
        if(process == "1"){
            $("#giveTip8 .msg").html("该零组件为外购成品，无需在此编辑！");
        }else if(composition == "3"){
            $("#giveTip8 .msg").html("该零件的材料需由多种原料先混和，无需在此编辑！");
        }else if(composition == "4"){
            $("#giveTip8 .msg").html("该零件系由一种材料直接加工而成，无需在此编辑！");
        }else{
            $("#giveTip8 .msg").html("后台，， 把他的 process 和 composition 返回！！");
        }
        bounce_Fixed2.show($("#giveTip8"));
    }
}
// create:hxz 2020-06-25 删除零部件
function delPartOk() {
    var item = JSON.parse(editObj.siblings(".hd").html());
    var curP = partsList[partsList.length - 1] ;
    $.ajax({
        "url":"../constitute/lingJianDelete.do",
        "data":{ "id":item.id , "parentId":curP.gsItem.id },
        success:function (res) {
            bounce_Fixed2.cancel();
            var code = res['code'];
            if(code == 200){
                layer.msg("操作成功");
                let lastPart = partsList[partsList.length - 1];
                let list = lastPart.data.filter(sys=>sys.id !== item.id);
                lastPart.data = list;
                setPartsData(lastPart , lastPart.type);
                // editObj.parent().parent().remove();
            }else{
                layer.msg("操作失败");
            }

        }
    })
}
// create:hxz 2020-06-25 设置零部件录入的默认值
function setGSInfo(item , innerSnFalse) {
    for(var key in item){
        $("#" + key).val(item[key]).attr("disabled", "true").parent().attr("disabled", "true");
        if(key == "id"){
            $("#oldId").val(item[key]);
        }
    }
    getUnitList($("#unitId"), 4, item['unitId']);
    var demoLen = item['demo'] ? item['demo'].length :"0" ;
    $(".textMax").html(demoLen + "/100");
    if(innerSnFalse === 1){

    }else{
        $("#innerSn").removeAttr("disabled").parent().removeAttr("disabled");
    }
    $("#amount").removeAttr("disabled").parent().removeAttr("disabled");
}
// create:hxz 2020-06-25 与录入匹配
function gsMatch() {
    $("#selecGS").show();
    var innerEntry = $("#innerSn").val();
    $("#selecGS option").hide();
    var hasMatch = false;  
    $("#selecGS option").each(function () {
        var i_innerSn = $(this).html();
        if(i_innerSn.indexOf(innerEntry) != -1){
            $(this).show();
        }
        if(i_innerSn === innerEntry){
            hasMatch = true;
            setSelectOption($(this));

        }
    });
    if(!hasMatch){
        $("#partEntry td").removeAttr("disabled");
        $("#partEntry input:not(#innerSn)").val("").removeAttr("disabled");
        $("#partEntry select").val("").removeAttr("disabled");
        $("#partEntry textarea").val("").removeAttr("disabled");
    }
}
function setSelectOption(thisObj) {
    var i_item = thisObj.val();
    i_item = JSON.parse(i_item);
    $("#id").val(i_item['id']);
    for(var key in i_item){
        if(key != "innerSn"){
            $("#" + key).val(i_item[key]).attr("disabled", "true").parent().attr("disabled", true);
        }
    }
    $("#innerSn").val(i_item['innerSn']);
    $("#amount").removeAttr("disabled").parent().removeAttr("disabled");
}
// create:hxz 2020-06-25 拆分返回/关闭
function goBack(type){
    // 0 - 零部件的返回 ; 1 - 返回产品拆分 ; 2 - 产品拆分的返回主页
    if(type == 1){
        var lastPart =  partsList[0];
        setPartsData(lastPart , "");
    }else if(type == 2){
        getGsList(type, 1);
        bounce.cancel();
    }else if(type == 0){
        partsList.pop();
        var lastPart =  partsList[partsList.length - 1];
        setPartsData(lastPart , lastPart.type);
    }
}
// create:hxz 2020-06-25 确定拆分完毕
function splitOKBtn(type){
    var len = $("#splitGS tbody:eq(1)").children("tr").length ;
    if(len == 1){
        $("#giveTip6 .tipC").html("您尚未录入构成该产品的零组件！");
        bounce_Fixed3.show($("#giveTip6"));
        return false;
    }
    var isAll = true, splitGSCon = "#splitGS" ;
    if(type == 2){ splitGSCon = "#splitGS2" ; }
    $(splitGSCon + " tbody:eq(1)").children("tr:gt(0)").each(function () {
        var td9 = $(this).children(":eq(9)").html();
        if (td9 == "未拆分完"){
            isAll = false ;
        }
    })
    if(isAll){
        splitOK();
    }else {
        $("#giveTip9 .type" + type).show().siblings().hide();
        bounce_Fixed3.show($("#giveTip9"))
    }
}
function splitOK(){
    var curP = partsList[partsList.length - 1];
    var gsItem = curP.gsItem ;
    var pid = gsItem.id ;
    $.ajax({
        // "url": "../constitute/updateProductBase.do",
        "url": "../constitute/splitComplete.do",
        "data":{"id": pid },
        success:function (res) {
            bounce_Fixed3.cancel();
            var code = res['code'];
            if(code == 200){
                layer.msg("操作成功");
                partsList.pop();
                if(partsList.length > 0){
                    var lastPart =  partsList[partsList.length - 1];
                    partsList.pop();
                    getParts(lastPart.gsItem, lastPart.type); // 刷新上一个零部件
                }else{
                    bounce.cancel(); // 产品的拆分完了
                    var cur = $("#yeCon .yecur").html();
                    getGsList(2, cur)
                }
            }else{
                layer.msg("操作失败：" + res.msg);
            }
            
        }
    })
}
// create:hxz 2020-06-25 录入零部件 - 获得可选的图号
function partEntryBtn(){
    $("#partEntry input").val("")
    $("#partEntry select").val("")
    $("#partEntry textarea").val("")
    var curGS = partsList[partsList.length - 1] ;
    var item = curGS.gsItem
    $.ajax({
        "url": "../constitute/selectLingJianListByParent.do",
        "data":{ "id":item.id  },
        success:function (res) {
            var list = res['data'], str = "" ;
            if(list){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<option value='"+ JSON.stringify(list[i]) +"'>"+ list[i]['innerSn'] +"</option>"
                }
            }
            $("#selecGS").html(str);
        }
    });
    $(".bounce").everyTime('0.5s','partEntry',function(){
        var isNull = false;
        var data = {} ;
        var id = $("#id").val();
        if(id){
            var amount = $("#amount").val();
            if(!amount){ isNull = true; }
        }else{
            var notMust = ["innerSn", "name", "process", "unitId", "unit", "amount"];
            for(var key in notMust){
                var val = $("#" + notMust[key]).val();
                data[notMust[key]] = val ;
                if(!val){
                    isNull = true
                }
            }
            if(!isNull){
                if(data['process'] == 1){
                    var netWeight = $("#netWeight").val()
                    var weightUnit = $("#weightUnit").val()
                    if(netWeight == "" || weightUnit == ""){
                        isNull = true;
                    }
                }else{
                    var composition = $("#composition").val();
                    if(composition == 2){
                    }else{
                        var netWeight = $("#netWeight").val();
                        var weightUnit = $("#weightUnit").val();
                        if(netWeight == "" || weightUnit == ""){
                            isNull = true;
                        }
                    }
                }
            }
        }
        if(isNull){
            $("#partEntryOK").attr("class", "ty-btn bounce-cancel ty-btn-big ty-circle-5");
        }else{
            $("#partEntryOK").attr("class", "ty-btn bounce-ok ty-btn-big ty-circle-5");
        }

    })
}
function partEntryOK(){
    var isNull = $("#partEntryOK").hasClass("bounce-cancel");
    if(isNull){
        $("#giveTip6 .tipC").html("还有必填项尚未填写");
        bounce_Fixed3.show($("#giveTip6"));
        return false;
    }
    if($("#netWeight").val().length > 8){
        layer.msg("您录入的数字位数太多了，请选择合适的重量单位");
        return false;
    }
    var curP = partsList[partsList.length-1];
    var gsItem = curP.gsItem ;
    var type = curP.type ;
    var pid = gsItem.id ;
    var data = { "pdBaseId": pid } ;
    $("#partEntry .entry").each(function(){
        var key = $(this).attr("id");
        var val = $(this).val();
        data[key] = val ;
    });
    if(!data["id"]){
        if(!data["process"]){
            $("#giveTip6 .tipC").html("请选择加工情况");
            bounce_Fixed3.show($("#giveTip6"));
            return false;
        }else{
            if(data["process"]=='1'){

            }else{
                if(!data["composition"]){
                    $("#giveTip6 .tipC").html("请选择构成情况");
                    bounce_Fixed3.show($("#giveTip6"));
                    return false;
                }
            }
        }
    }else{
        if(!data["process"] && !data["composition"]){
            $("#giveTip6 .tipC").html("该产品未走完构成流程，如想使用请先处理");
            bounce_Fixed3.show($("#giveTip6"));
            return false;
        }
    }

    $.ajax({
        "url":"../constitute/addPdBaseLingJian.do",
        "data":data ,
        success:function (res) {
            bounce_Fixed2.cancel();
            var code = res['code'];
            if(code == "200"){
                layer.msg("操作成功");
                partsList.pop();
                getParts(gsItem, type);

            }else{
                layer.msg("操作失败:" + res.msg);
            }

        }
    })
}
// create:hxz 2020-06-21 渲染拆分数据
function setPartsData(res, type){
    if(type == ""){
        bounce_Fixed.cancel();
    }
    var itemGS = res['gsItem'] , netWeightCount = 0;
    $("#gsName").html(itemGS['name']); // 顺便给零件录入的赋值
    var str = itemStr(itemGS,1) +
        "    <td>"+ netWeightLimit(itemGS.netWeight,itemGS.weightUnit) +"</td>" ;
    $("#splitGS" + type).find("tbody:eq(0)").children(":eq(1)").html(str);
    var listStr = "" , list = res['data'];
    if(list){
        var btnStr = '';
        for(var i = 0 ; i < list.length ; i++){
            var item = list[i] ;
            let {weightUnit, netWeight, amount} = item;
            if (weightUnit && netWeight && !isNaN(netWeight) && handleNull(weightUnit) != ""){
                var power = 1;
                if (weightUnit == '2') {
                    power = 1000;
                } else if (weightUnit == '3') {
                    power = 1000000;
                } else if (weightUnit == '4') {
                    power = 1000000000;
                }
                netWeightCount += Number(netWeight) * power * (amount || 0);
            }
            item.num > 1? btnStr="<span type=\"btn\" data-type=\"noSplit\" class=\"ty-color-gray\">编辑</span>":btnStr="<span type=\"btn\" data-type=\"splitBtn2\" class=\"ty-color-blue\">编辑</span>";
            listStr += "<tr>" + itemStr(item,1) +
                "    <td>"+  netWeightLimit(netWeight,weightUnit) +"</td>" +
                "    <td>"+ (amount || "") +"</td>" +
                "    <td>"+ charge(item['process'] , 'process') +"</td>" +
                "    <td>"+ charge(item['composition'] , 'composition') +"</td>" +
                "    <td>"+ charge(item['split'] , 'split') +"</td>" +
                "    <td>" + btnStr +
                "        <span type=\"btn\" data-type=\"partsEdit\" class=\"ty-color-blue\">修改装配数量</span>" +
                "        <span type=\"btn\" data-type=\"delBtn\" class=\"ty-color-blue\">删除</span>" +
                "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                "    </td>" +
                "</tr>";
        }
        if(netWeightCount > 0 ) {
            var weightUnitStr = weightUnitCharge(netWeightCount);
            $("#splitGS" + type).find("tbody:eq(0)").children(":eq(1)").find(":last").html(weightUnitStr);
        }
    }
    $("#splitGS" + type).find("tbody:eq(1)").children(":gt(0)").remove() ;
    $("#splitGS" + type).find("tbody:eq(1)").append(listStr);
}
// create:hxz 2020-06-21 获取拆分零部件列表
var partsList = [] ; // 零件拆分树列表
function getParts(item, type) {
    // type : ""-产品 ， 2-零件
    var str = "" ;
    $.ajax({
        "url": "../constitute/getPdOrLingJianList.do"  ,
        "data":{ 'id':item.id } ,
        success:function(res){
            if(type == 2){ // 零件的拆分管理
                res['gsItem'] = item ;
                res['type'] = type ;
                partsList.push(res);
                setPartsData(res , type);
            }else{
                res['gsItem'] = item ;
                res['type'] = "" ;
                partsList = [res];
                setPartsData(res , type);
            }
        }
    })
}
// create:hxz 2020-06-21 翻译键值
function charge(val , type) {
    var str = ""
    switch (type){
        case 'process':
            if(val == "1"){ str = "外购"; }
            if(val == "2"){ str = "自制"; }
            if(val == "3"){ str = "自制+外包"; }
            break;
        case 'weightUnit':
            if(val == "1"){ str = "毫克"; }
            if(val == "2"){ str = "克"; }
            if(val == "3"){ str = "千克"; }
            if(val == "4"){ str = "吨"; }
            break;
        case 'composition':
            if(val == "1"){ str = "成品"; }
            if(val == "2"){ str = "装配"; }
            if(val == "3"){ str = "制造"; }
            if(val == "4"){ str = "制造"; }
            break;
        case 'split':
            if(val == "0"){ str = "无需拆分"; }
            if(val == "1"){ str = "未拆分完"; }
            if(val == "2"){ str = "拆分完毕"; }
            break;
        case 'state':
            if(val == "0"){ str = ""; }
            if(val == "1"){ str = "开发中"; }
            if(val == "2"){ str = "开发完成"; }
            break;
    }
    return str
}
// create:hxz 2020-06-21 设置商品键值对
function setKeyByID(id , key , val) {
    var list = GSList ;
    for(var i = 0 ; i < list.length ; i++){
        if(list[i]['id'] == id){
            list[i][key] = val ;
        }
    }
}
// create:hxz 2020-06-18 获取主页面数据
function getMainData() {
   $("#select1").val("3");
   $("#select2").val("1");
    $.ajax({
        "url":"../constitute/getCompositionHomeData.do",
        "data":{ },
        success:function(res){
            var data = res['data'];
            $(".mainCon1 .process").html(data['jiagong'] );
            $(".mainCon1 .compose").html(data['goucheng']);
            $(".mainCon1 .split").html(data['chaifen'] );
        }
    })
}
// create:hxz 2020-06-18  获取mainCon2 需要的数据
var GSList = [] , selectGS = [] , notSelectGS = [] ;
function getGsList(state, cuePage){
    //state - 0:待确认加工方，1：待确认构成，2：待拆分
    var url = "../constitute/getConfirmedList.do";
    if(state == 1){
        url = "../constitute/getCompositionToBeConfirmedList.do";
    } else if(state == 2){
        url = "../constitute/getSplitList.do";
    }
    $.ajax({
        "url":url,
        // "data":{"state":state},
        "async":false,
        success:function(res){
            loading.close();
            var list = res['data'];
            var dataNum = res['dataNum'];
            GSList = list ;
            countGsList();
            if (state == 2) {
                setPageData(cuePage, perNUM , state);
            }

        }
    })
}
// create:hxz 2020-06-18  maincon2 渲染待拆分表格数据
function setPageData(cur, per, state) {
    var list = GSList , str = "" ;
    var start = (cur-1)*per ;
    var end = start + per ;
    if(list){
        for(var i = start ; i < end ; i++){
            var item = list[i];
            if(item){
                str += "<tr>" + itemStr(item) +
                    "    <td>" +
                    "<span type=\"btn\" data-type='scan' class=\"ty-color-blue\">查看</span>" +
                    "<span type=\"btn\" data-type='splitBtn' class=\"ty-color-blue\">拆分</span>" +
                    "<span class=\"hd\">"+ JSON.stringify(item) +"</span>" ;
                /*if(state == 3){
                    str += "<span type=\"btn\" data-type='splitBtn' class=\"ty-color-blue\">拆分</span>" ;
                }*/
                str += "</td></tr>";
            }
        }
    }
    $(".mainCon2 tbody").html(str);
    $(".mainCon2").show().siblings().hide();
    var all = parseInt(list.length / per ) ;
    if(list.length % per){ all++; }
    setPageGs( $("#yeCon") , cur , all, 'setPageData', perNUM )

}
function itemStr(item , num) { // num = 1 不显示create
    var str = "<tr>" ;
    if(item){
        str = 
            "    <td>"+ item['innerSn'] +"</td>" +
            "    <td>"+ item['name'] +"</td>" +
            "    <td>"+ (item['model'] || "") +"</td>" +
            "    <td>"+ (item['specifications'] || "") +"</td>" +
            "    <td>"+ (item['unit'] || "" ) +"</td>" ;
        if(num !== 1){
            str += "<td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) + "</td>";
        }
    }
    return str ;
}
// create:hxz 2020-06-18  maincon2 渲染分页数据
function setPageGs(obj , cur , countall , cbStr , perNum ){
    var all = Number(countall);
    cur = Number(cur);
    if( all == 0){ all = 1 ; }
    if( cur == 0){ cur = 1 ; }
    var div_obj = obj;
    div_obj.html("");
    var str = "<div class='yeCon'>";
    if(all > 8){
        var star = cur - 3; // 开始页
        var n = 0;
        var end = parseInt(cur) + 3;
        if( star < 1 ){ star = 1; n = 3 - cur; end += n ;  }
        if( end > all ){ end = all ; }
        if( end - star < 5 ){ star = end - 5 ; }
        if( star < 1 ){ star = 1 ; }

        if( cur != 1 ){ str += "<div class='ye' onclick='"+ cbStr +"(1, "+ perNum +")'> 首页 </div>"; }
        if( star > 1 ){ str = str + "<span style='padding:5px; ' ></span>....<span style='padding:5px; ' ></span>"; }
        for (var i = star; i <= end; i++) {
            if( i == cur ){ str += "<div class='yecur'>"+i+"</div>"; }
            else {
                str += "<div class='ye' onclick='"+ cbStr +"("+ i +", "+ perNum +")'>" + i + "</div>";
            }
        }
        if( end < all ){ str = str + "<span style='padding:5px; ' ></span>....<span style='padding:5px; ' ></span>"; }
        if( cur != all ){ str += "<div class='ye' onclick='"+ cbStr +"("+ countall +", "+ perNum +")'> 尾页 </div>"; }

    }else{
        for (var i = 1; i <= all; i++) {
            if(i==cur){
                str=str+"<div class='yecur'>"+i+"</div>";
            }else{
                str=str+"<div class='ye' onclick='"+ cbStr +"("+ i +", "+ perNum +")'>"+i+"</div>";
            }
        }
    }
    // json = json || "{}";
    str=str+"<div class='yeC'>共"+all+"页</div>"+
        "<div class='yeC'>当前第"+cur+"页</div></div>";
        // "<div class='hd type'>"+ Typestr +"</div>"+
        // "<div class='hd json'>"+ json +"</div>";
    div_obj.append(str);
}
// create:hxz 2020-06-18  maincon3 渲染表格数据
function selectGsCon(state){
    $(".mainCon3").show().siblings().hide();
    $(".mainCon3 .ty-secondTab").children().removeClass('ty-active');
    $(".mainCon3 .ty-secondTab").children(":eq("+ state +")").addClass('ty-active');
    $(".mainCon3 .nav"+state).show().siblings().hide();
    $(".mainCon3 .selectedCon").hide().siblings().show();
    setPageData3(1 , perNUM );
}
function setPageData3(cur, per){
    var list = [] , faStr = "" ;
    var navIndex = $(".mainCon3 .ty-secondTab li").index($(".mainCon3 .ty-secondTab .ty-active"));
    if(navIndex == 0){
        faStr = "<td><span class=\"fa fa-square-o\"></span></td>";
        list = notSelectGS ;
    }else if(navIndex == 1){
        faStr = "<td><span class=\"fa fa-check-square-o\"></span></td>";
        list = selectGS ;
    }
    var str = "" ;
    var start = (cur-1)*per ;
    var end = start + per ;
    if(list){
        for(var i = start ; i < end ; i++){
            var item = list[i];
            if(item){
                str += "<tr>" +  faStr + itemStr(item) +
                    "    <td>" +
                        "<span type=\"btn\" data-type='scan' class=\"ty-color-blue\">查看</span>" +
                        "<span class='hd'>"+ JSON.stringify(item) +"</span>" +
                    "</td>" +
                    "</tr>";
            }
        }
    }
    $(".mainCon3 tbody:eq(0)").html(str);
    var all = parseInt(list.length / per ) ;
    if(list.length % per){ all++; }
    setPageGs( $("#yeCon3") , cur , all, 'setPageData3', perNUM);
}
// create:hxz 2020-06-18  maincon3  挑选完毕，下一步
function nextStep(state){
    var len = $(".mainCon3 tbody").find(".fa-check-square-o").length;
    if(len >0){
        var navIndex = Number($("#handleLog").html());
        if(navIndex === 0){
            $(".mainCon3 .selectedCon .nav1").show().siblings().hide();

            var option = $("#select1").val();
            if(option == 1){
                bounce.show($("#giveTip4")) ;
                $("#giveTip4 .num").html(selectGS.length);
            }else if(option == 3){
                bounce.show($("#giveTip3")) ;
                $("#giveTip3 .num").html(selectGS.length);

            }else if(option == 2){
                okNextStep()
            }
        }else if(navIndex === 1){
            var option = $("#select2").val();
            $(".mainCon3 .selectedCon .nav"+ option).show().siblings().hide();

            if(option == 1){
                bounce.show($("#giveTip5")) ;
                $("#giveTip5 .num").html(selectGS.length);

            }else{
                okNextStep();
            }
        }
    }else{
        bounce.show($("#giveTip2")) ;
    }
}
function okNextStep(){
    $(".mainCon3 .selectedCon ").show().siblings().hide();
    bounce.cancel();
    setSelectPageData(1,perNUM);
}
// create:hxz 2020-06-19  maincon3  挑选完毕，确定
function submitGs(){
    var navIndex = Number($("#handleLog").html());
    var data = {} , url = "" , list = selectGS;
    data.baseList = [];
    for(var j = 0 ; j < list.length ; j++){
        data.baseList.push({'id':list[j]['id'], 'netWeight':list[j]['netWeight'], 'weightUnit':list[j]['weightUnit']})
    }
    data.baseList = JSON.stringify( data.baseList);
    if(navIndex === 0){ // 待确认加工方
        url = "../constitute/processingConfirmation.do" ;
        var option = $("#select1").val() ;
        if(option == 1){
            data['process'] = 2 ;
        }else if(option == 3){
            data['process'] = 3 ;
        }else if(option == 2){
            data['process'] = 1;
        }
    }else if(navIndex === 1){ // 待确认构成
        url = "../constitute/compositionToBeConfirmed.do" ;
        var option = $("#select2").val();
        if(option == 1){
            data['composition'] = 2 ;
        }else if(option == 2){
            data['composition'] = 4 ;
        }else if(option == 3){
            data['composition'] = 3 ;
        }
    }
    $.ajax({
        'url': url ,
        'data': data ,
        success:function(res){
            var code = res.code ;
            bounce.cancel();
            //$(".mainCon2").show().siblings().hide();
            goMainOK();
            if(code == "200"){
                layer.msg("操作成功！")
            }else {
                layer.msg("操作失败！")
            }
        }
    })
}
// create:hxz 2020-06-22  计算选中未选中的数据
function countGsList(){
    var list = [] , list2 = [] ;
    for(var j = 0 ; j < GSList.length ; j++){
        if(GSList[j]["selected"] == 1){
            list.push(GSList[j]);
        }else{
            list2.push(GSList[j]);
        }
    }
    selectGS = list ;
    notSelectGS = list2 ;
    $(".mainCon3 .ty-secondTab").children("li:eq(0)").find("span").html(notSelectGS.length);
    $(".mainCon3 .ty-secondTab").children("li:eq(1)").find("span").html(selectGS.length);
}
// create:hxz 2020-06-19  maincon3  渲染选中的数据
function setSelectPageData(cur , per){
    var list = selectGS , str = "" ;
    var start = (cur-1)*per ;
    var end = start + per ;
    if(list){
        for(var i = start ; i < end ; i++){
            var item = list[i];
            if(item){
                str += " <tr>" +  itemStr(item , 1) +
                    "<td><input type=\"text\" onchange=\"setKeyByID("+ item['id'] +" ,'netWeight', $(this).val())\"></td>" +
                    "<td><select onchange=\"setKeyByID("+ item['id'] +", 'weightUnit', $(this).val())\">" +
                    "    <option value=\"1\">毫克（mg）</option>" +
                    "    <option value=\"2\">克（g）</option>" +
                    "    <option value=\"3\">千克（kg）</option>" +
                    "    <option value=\"4\">吨（T）</option>" +
                    "</select></td>" +
                    "</tr>";
                setKeyByID(item['id'], 'weightUnit', 1);
            }
        }
    }
    $(".selectedCon tbody").html(str);
    $(".selectedCon .navs").find("span").html(list.length);
    var all = parseInt(list.length / per ) ;
    if(list.length % per){ all++; }
    setPageGs( $("#yeCon31") , cur , all, 'setSelectPageData', perNUM);
}


// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.siblings(".textMax").text(curLength + '/' + max);
}


// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectID) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    if(selectID == item['id']){
                        str += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit() {
    bounce_Fixed3.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed3.cancel();
                getUnitList($("#unitId"), module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed3.show($("#tip1"));
            }
        }
    })
}

// creator: hxz，2020-09-02 14:51:41   计量单位设置
function unitAssign(obj) {
    var unitName = obj.children("option:selected").html();
    obj.siblings("input").val(unitName)
}
// creator: 李玉婷，2022-01-14 09:59:15，限制单重最多8位
function toFixedNetweight(str,size) {
    var result = str;
    if (str.toString().length > size) {
        result = str.substr(0,size);
        if (result.charAt(result.toString().length-1) == '.'){
            result = str.substr(0,Number(size)+1);
        }
    }
    return result;
}
// creator: 李玉婷，2022-02-11 15:47:01，单重+单位
function netWeightLimit(val, unit) {
    var netWeight = 0,result = '';
    if (handleNull(val) != "" && handleNull(unit) != "") {
        if (unit == '1') {
            netWeight = Number(val);
        } else if (unit == '2') {
            netWeight = Number(val)*1000;
        } else if (unit == '3') {
            netWeight = Number(val)*1000000;
        } else if (unit == '4') {
            netWeight = Number(val)*1000000000;
        }
        result = weightUnitCharge(netWeight);
    } else {
        result = '--';
    }
    return result;
}
// creator: 李玉婷，2022-02-11 11:33:33，单重换算
function weightUnitCharge(netWeightCount) {
    var weightUnitStr = '', unitStr = '';
    if (netWeightCount > 10 && netWeightCount < 1000000) {
        unitStr = ' 克';
        weightUnitStr = (netWeightCount/1000).toFixed(3);
    } else if (netWeightCount >= 1000000 && netWeightCount < 1000000000) {
        unitStr = ' 千克';
        weightUnitStr = (netWeightCount/1000000).toFixed(3);
    } else if (netWeightCount >= 1000000000) {
        unitStr = ' 吨';
        weightUnitStr = (netWeightCount/1000000000).toFixed(3);
    } else{
        unitStr = ' 毫克';
        weightUnitStr = netWeightCount.toFixed(3);
    }
    return toFixedNetweight(weightUnitStr) + unitStr;
}