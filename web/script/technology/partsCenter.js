var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
let  editObj = null,pType = '',partList = [];
$(function () {
    showMainCon(1);
    $(".mainCon1 select").val("");
    setInparts("","");
    // 查看/删除/修改分类
    $('body').on('click', '.ty-btn,.linkBtn', function(){
        var type = $(this).data('type');
        let info = ``;
        let _this = $(this);
        switch (type){
            case 'backMain' :
                showMainCon(1);
                break;
            case 'partScan' :
                let icon = $(this).data("num");
                info = JSON.parse($(this).siblings(".hd").html());
                $("#stopDetails").hide();
                $(".partsScan2").show();
                $("#seeParts .bonceHead span").html("零组件查看");
                if (icon == 1 || icon == 2 || icon == 3) {
                    $("#seeParts").data("id", info.id);
                    $("#seeParts").data("source", icon);
                    partList = [info.id];
                }
                if (icon == 1) {
                    $(".partsScan1").show();
                } else if (icon == 2) {
                    $(".partsScan1").hide();
                    $("#seeParts .bonceHead span").html("暂存于系统的零组件");
                } else if (icon == 3) {
                    $(".partsScan1").show();
                    $("#stopDetails").show();
                } else if (icon == 4) {
                    partList.push(info.id)
                    $(".partsScan1").show();
                    $(".partsScan2").hide();
                    $("#stopDetails").hide();
                }
                partScan(info.id);
                break;
            case 'directlyProducts':
                var lang = _this.data("lang");
                partIntrolCommon($("#seePartsDetail"));
                $("#seePartsDetail .combin tbody").children(":eq(0)").nextAll().remove();
                seePartsDetail(lang);
                break;
            case 'updateParts':
                var part = JSON.parse($("#partData").html());
                getTicketAndPerson();
                $("#updateParts [need]").each(function(){
                    var nameData = $(this).attr('name');
                    $(this).val(part[nameData]);
                });
                getUnitList($("#unitSelect2"), 3 , part.unitId);
                bounce_Fixed.show($('#updateParts'));
                if (part.composition == 2) {
                    $("#update_netWeight").prop("disabled", true);
                    $("#update_weightUnit").addClass("selectDis");
                } else {
                    $("#update_netWeight").prop("disabled", false);
                    $("#update_weightUnit").removeClass("selectDis");
                }
                break;
            case 'baseRecords':
                baseRecordList(1);
                break;
            case 'compositionRecords':
                baseRecordList(2);
                break;
            case 'baseRecordScan':
                let hub = _this.data("source");
                if (hub == 1) {
                    baseRecordScan(_this);
                } else if  (hub == 2) {
                    compositionRecordScan(_this);
                }
                break;
            case 'editSource':
                var part = JSON.parse($("#partData").html());
                $(".assemblingTip").hide();
                $("#editInsidePart").hide();
                $("#editPartsSource .creatInfo").html(part.createName + '&nbsp;&nbsp;' + new Date(part.createDate).format('yyyy-MM-dd hh:mm:ss'));
                partIntrolCommon($("#editPartsSource"));
                //设置option
                $("#editPartsSource option").prop("disabled" , false);
                if (part.process && handleNull(part.process) !=="") {
                    $("#edit_process").val(part.process);
                    $("#actual_process").val(part.process);
                    $("#edit_composition").val(part.composition);
                    $("#actual_composition").val(part.composition);
                    $("#edit_process option").eq(part.process).prop("disabled" , true);
                    if (part.process == 1) {
                        $("#edit_composition option").removeClass("grayOpt");
                        $("#edit_composition").prop("disabled" , true);
                        $("#edit_composition").removeAttr("readonly onfocus onchange");
                    } else if (part.process == 2 || part.process == 3) {
                        $("#edit_composition").prop("disabled", false);
                        $("#edit_composition option:eq(0)").nextAll().addClass("grayOpt");
                        $("#edit_composition").attr({readonly:"readonly",onfocus:"this.defOpt=this.selectedIndex",onchange:"this.selectedIndex=this.defOpt"});
                        if (part.composition == 2) {
                            $("#editInsidePart").show();
                        }
                    }
                } else{
                    $("#editPartsSource select").val("");
                    $("#edit_process option").prop("disabled" , false);
                    $("#edit_composition option").removeClass("grayOpt");
                    $("#edit_composition").prop("disabled" , true);
                    $("#edit_composition").removeAttr("readonly onfocus onchange");
                }
                setCompoAndProcess($("#edit_process"), $("#edit_composition"));
                bounce_Fixed.show($("#editPartsSource"));
                break;
            case 'editInsidePart':
                var part = JSON.parse($("#partData").html());
                $("#splitGS2 .bonceHead span").html("修改零组件");
                splitBtn(part , "2");
                break;
            case 'allParts':
                partIntrolCommon($("#seePartsGroup"));
                allRelatedParts();
                break;
            case 'allOnceParts':
                partIntrolCommon($("#seeOncePartsGroup"));
                allRelatedPartsOnce();
                break;
            case 'viewSettings':
                viewSettings();
                break;
            case 'partEntry': // 录入直接组成该产品的零组件
                bounce_Fixed2.show($("#partEntry")) ;
                $("#addUnitBtn").show();
                $("#partEntry .textMax").html("0/100");
                partEntryBtn();
                $("#innerSn").attr("onclick", "startMatch(event)");
                $("#partEntry .bonceHead span").html("零组件录入");
                $("#partEntry .bonceFoot .bounce-ok").html("录入完毕");
                $("#partEntry [disabled]").removeAttr("disabled");
                getUnitList($("#unitId"), 4)
                break;
            case 'ptSuspendRecord':
                ptSuspendRecord();
                break;
            default:
                break;
        }
    });
    // 所有table中的查看
    $("table").on('click', 'span[type="btn"]', function(){
        var type = $(this).data('type');
        var item = JSON.parse($(this).siblings(".hd").html());

        if(type == "splitBtn2"){
            splitBtn(item , "2");
        }else if(type == "partsEdit"){
            bounce_Fixed2.show($("#partEntry")) ;
            $("#addUnitBtn").hide();
            $("#partEntry .bonceHead span").html("零组件修改");
            $("#partEntry .bonceFoot .bounce-ok").html("确定");
            partEntryBtn();
            // $("#innerSn").removeAttr("onclick");
            setGSInfo(item , 1);
        }else if(type == "delBtn"){
            var delTip = '';
            if (item.num > 1) {
                delTip = '<p>本件还参与构成了其他产品或组件。</p><p>确定后，本件将仅不参与本组件的构成！</p>';
            } else {
                delTip = '<p>确定后，本件将彻底删除！</p>';
            }
            $("#giveTip7 #msg").html(delTip);
            bounce_Fixed2.show($("#giveTip7")) ;
            editObj = $(this) ;
        }else if(type == "noSplit"){
            layer.msg("<p>本件在此不可编辑！</p><p>如需编辑，请到“零组件”模块中进行</p>");
        }
    });
    $("#edit_process").change(function () {
        $("#actual_process").val($(this).val());
        if ($(this).val() == 1) {
            $("#actual_composition").val("");
            $("#editInsidePart").hide();
        }
        setCompoAndProcess($("#edit_process"), $("#edit_composition"));
    });
    $("#edit_composition").change(function () {
        var part = JSON.parse($("#partData").html());
        if (part.process != 2 && part.process != 3) {
            $("#actual_composition").val($(this).val());
            setCompoAndProcess($("#edit_process"), $("#edit_composition"));
        }
    });
    $("#process").change(function () {
        setCompoAndProcess($("#process"), $("#composition"));
    })
    $("#composition").change(function () {
        setCompoAndProcess($("#process"), $("#composition"));
    })
    // 全部零组件查看设置 选择按钮
    $("#viewSettings .settingsCon").on('click', '.radioCon', function(){
        $(this).siblings().children("i").attr("class" , "fa fa-circle-o");
        $(this).children("i").attr("class" , "fa fa-dot-circle-o");
    });
    // 零部件录入 选中
    $("#selecGS").on('click', 'option', function(){
        setSelectOption($(this));
        $("#selecGS").hide();
        $(".bounce_Fixed2").stopTime("gsMatch")
    });
    // creator:hxz 请选择加工单位
    $(".ty-colFileTree").on("click", ".ty-treeItem", function () {
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //点击文件夹箭头方向的切换
        if ($(this).find("i").eq(0).hasClass("fa-angle-right")) {
            $(this).find("i").eq(0).removeClass("fa-angle-right");
            $(this).find("i").eq(0).addClass("fa-angle-down");
            $(this).next().show();
        } else if ($(this).find("i").eq(0).hasClass("fa-angle-down")) {
            $(this).find("i").eq(0).removeClass("fa-angle-down");
            $(this).find("i").eq(0).addClass("fa-angle-right");
            $(this).next().hide();
        }
    });
})
// creator: 李玉婷，2021-11-04 08:18:56，零组件首页初始设置
function setInparts(category, categoryName) {
    pType = 1;
    var pData =  {
        "pageSize": 20,
        "currPage": 1,
        "category": category,
        "categoryName": categoryName,
        "process": '',
        "composition": '',
        "param": ''
    }
    $(".mainCon1 input").val("");
    $(".mainCon1 select").val("");
    $("#ye").html("");
    $("#suspendPdNum").html("0");
    $("#addProductionBtn").show();
    $("#kindsTree li").remove();
    $(".inProduct").show().siblings().hide();
    if (pData.categoryName == "" || pData.categoryName == "全部"){
        $(".suspendBtn").show();
        $(".left-bottom").hide();
        $("#firstLevelName").html("全部").data("categoryid","");
    } else{
        $(".suspendBtn").hide();
        $(".left-bottom").show();
        $("#firstLevelName").html(pData.categoryName).data("categoryid",pData.category);
    }
    getPartsList(pData);
}
// creator: 李玉婷，2021-11-02 11:37:45，零组件列表
function getPartsList(param) {
    $.ajax({
        "url":"../parts/getPartsList.do",
        "data":param,
        success:function(res){
            var count = 0;
            var list = res['data'];
            var categoryList = res.mtCategories;
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = param;
            jsonStr = JSON.stringify(jsonStr) ;
            var str = '';
            if (list && list.length>0){
                for(var i=0;i<list.length;i++){
                    str +=
                        '<tr data-id="'+ list[i].id +'">' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name + ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+ ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+'</td>' +
                        '    <td>'+ list[i].unit +'</td>' +
                        '    <td>'+ netWeightLimit(list[i].netWeight,list[i]['weightUnit']) +'</td>' +
                        '    <td>'+ handleNull(list[i].proNum) +'</td>' +
                        '    <td>'+ handleNull(list[i].num) +'</td>' +
                        '    <td class="createInfo">'+ list[i].createName + '&nbsp;&nbsp;' + new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue linkBtn" data-type="partScan" data-num="1">查看</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[i])+'</span>' +
                        '    </td>' +
                        '</tr>';
                }
            }
            if (pType == 1) {
                $("#xRow").html(res.totalRows);
                setPage( $("#ye") , curr ,  totalPage , "InProductPart", jsonStr );
                if(param.categoryName == '全部' ||param.categoryName == ''){
                    $("#suspendPdNum").html(res.suspendNum);
                }
                if (categoryList && categoryList.length > 0){
                    var menu = '';
                    for(var m=0;m<categoryList.length;m++){
                        count += categoryList[m].num;
                        if (param.categoryName != '待分类'){
                            menu +=
                                '<li class="faceul1">' +
                                '    <a>' +
                                '        <span onclick="kindBtn($(this))">'+ categoryList[m].value +'（'+ categoryList[m].num +'种）</span>' +
                                '        <div class="hd">' +
                                '            <span class="kindId">'+ JSON.stringify(categoryList[m])+'</span>' +
                                '        </div>' +
                                '    </a>' +
                                '</li>';
                        }
                    }
                    $("#kindsTree").html(menu);
                }
                $("#firstLevelAmount").html(count);
                $("#productionList tbody").html(str);
            } else if (pType == 2) {
                $("#productionListQ").data("type", 1);
                $("#ssNum").html(res.countNum);
                $(".con3Ttl:eq(0)").html("直接参与组装的产品");
                $(".con3Ttl:eq(1)").html("直接参与组装的组件");
                $("#productionListQ tbody").html(str);
                setPage( $("#ye2") , curr ,  totalPage , "InProductPart", jsonStr );
            } else if (pType == 3 || pType == 4) {
                $("#xRow").html(res.countNum);
                $("#productionList tbody").html(str);
                setPage( $("#ye") , curr ,  totalPage , "InProductPart", jsonStr );
            }
        }
    })
}
// creator: 李玉婷，2020-03-25 11:25:05，获取部门 和 员工、商品和客户 列表
var selectObj = null; // 全部部门
function getTicketAndPerson() {
    $.ajax({
        "url": "../sale/getDetailsByInnerSn.do",
        "data": {"innerSn": ""},
        success: function (res) {
            selectObj = {};
            selectObj["orgMap"] = res["orgMap"];
            //selectObj["userMap"] = res["userMap"];
            //selectObj["baseMap"] = res["baseMap"];
            //selectObj["customerMap"] = res["customerMap"];
        }
    });
}
function selectDepartOk() {
    var str =     $("#selectDeapar").data("obj");
    var deparId = $("#selectDeapar .ty-treeItemActive").attr("id");
    var deparName = $("#selectDeapar .ty-treeItemActive > span").html();
    $("#"+ str +"Org").val(deparName);
    $("#"+ str +"OrgID").val(deparId);
    bounce_Fixed2.cancel();
}
// creator: 李玉婷，2020-03-25 11:59:04，选择部门
function selectDepart(str) {
    bounce_Fixed2.show($("#selectDeapar"));
    $("#selectDeapar").data("obj",str);
    var list = selectObj["orgMap"];
    var departmentStr = '';
    departmentStr += '<ul class="level1" level="1">' + getNextLevelList(list) + '</ul>';
    $("#selectDeapar .ty-colFileTree").html(departmentStr);
}
function getNextLevelList(list) {
    var departmentStr = '';
    for (var i = 0; i < list.length; i++) {
        var deparId = list[i]["id"];
        if (deparId === 0) {
            // 选择负责单位时， 不能选 “其他”部门
        } else {
            var subList = list[i].subList;
            var userList = list[i].userList;
            if (subList.length === 0 && userList.length === 0) {
                departmentStr += '<li>' +
                    '<div class="ty-treeItem" id=' + list[i]["id"] + '>' +
                    '<i class="ty-fa"></i>' +
                    '<i class="fa fa-folder"></i>' +
                    '<span>' + list[i]["name"] + '</span>' +
                    '</div>';
            } else if (subList.length > 0 || userList.length > 0) {
                departmentStr += '<li>' +
                    '<div class="ty-treeItem" id=' + list[i]["id"] + '>' +
                    '<i class="fa fa-angle-right"></i>' +
                    '<i class="fa fa-folder"></i>' +
                    '<span>' + list[i]["name"] + '</span>' +
                    '</div>' +
                    '<ul>';
            }
            if (subList.length > 0) {
                departmentStr += getNextLevelList(subList);
            }
            if (userList.length > 0) {
                for (var j in userList) {
                    departmentStr += '<li>' +
                        '<div class="ty-treeItem" id=' + userList[j]["userID"] + '>' +
                        '<i class="ty-fa"></i>' +
                        '<i class="fa fa-file"></i>' +
                        '<span>' + userList[j]["userName"] + '</span>' +
                        '</div>' +
                        '</li>';
                }
            }
            if (subList.length > 0 || userList.length > 0) {
                departmentStr += '</ul></li>';
            } else {
                departmentStr += '</li>';
            }
        }
    }
    return departmentStr;
}
// update: 李玉婷，2021-10-14 15:37:33， 零部件匹配录入限制
function setCompoAndProcess(objP,objC) {
    var process = objP.val(), composition = objC.val(), setDis = false;
    if(process == 1){
        objC.val("").attr("disabled" , "true");
    }else if(process == 2 || process == 3){
        objC.removeAttr("disabled");
        if(composition == 2 && process != 1){
            setDis = true
        }
    }
    if(setDis){
        if ($("#edit_process").is(":visible")) {
            $(".assemblingTip").show();
        } else if($("#process").is(":visible")){
            $("#weightUnit").val("2").attr("disabled" , "true");
            $("#netWeight").val("").attr("disabled" , "true");
        }
    }else{
        if ($("#edit_process").is(":visible")) {
            $(".assemblingTip").hide();
        } else if($("#process").is(":visible")){
            $("#weightUnit").val("2").removeAttr("disabled");
            $("#netWeight").val("").removeAttr("disabled");
        }
    }
}
// create :hxz 2021-1-11  显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    $(".mainCon" + num).show().siblings().hide();
}
// creator: 李玉婷，2021-11-03 13:12:36，暂存于系统中的零部件
function tempStorage () {
    pType = 1;
    var pData =  {
        "pageSize": 20,
        "currPage": 1,
        "process": '',
        "composition": '',
        "param": ''
    }
    $(".mainCon2 input").val("");
    $(".mainCon2 select").val("");
    showMainCon(2);
    getTempPartsList(pData);
}
function getTempPartsList(pData) {
    $.ajax({
        "url":"../parts/getPartsListForTemporary.do",
        "data":pData,
        success:function(res) {
            var list = res['data'];
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = pData;
            jsonStr = JSON.stringify(jsonStr);
            var str = '';

            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    str +=
                        '<tr data-id="' + list[i].id + '">' +
                        '    <td>' + list[i].innerSn + '</td>' +
                        '    <td>' + list[i].name + ((handleNull(list[i].model) === "") ? '' : '、' + list[i].model) + ((handleNull(list[i].specifications) === "") ? '' : '、' + list[i].specifications) + '</td>' +
                        '    <td>' + list[i].unit + '</td>' +
                        '    <td>' + netWeightLimit(list[i].netWeight,list[i]['weightUnit']) + '</td>' +
                        '    <td>' + handleNull(list[i].proNum) + '</td>' +
                        '    <td>' + handleNull(list[i].num) + '</td>' +
                        '    <td class="createInfo">' + list[i].createName + '&nbsp;&nbsp;' + new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue linkBtn" data-type="partScan" data-num="2">查看</span>' +
                        '        <span class="hd">' + JSON.stringify(list[i]) + '</span>' +
                        '    </td>' +
                        '</tr>';
                }
            }
            if (pType == 1) {
                $("#xRow_temp").html(res.totalRows);
                setPage($("#ye_temp"), curr, totalPage, "partsTemporary", jsonStr);
                $("#partsTemporaryList tbody").html(str);
            } else if (pType == 2) {
                $("#productionListQ").data("type", 2);
                $("#ssNum").html(res.countNum);
                $(".con3Ttl:eq(0)").html("直接参与组装的产品");
                $(".con3Ttl:eq(1)").html("参与组装的全部组件");
                $("#productionListQ tbody").html(str);
                setPage($("#ye2"), curr, totalPage, "partsTemporary", jsonStr);
            } else if (pType == 3 || pType == 4) {
                $("#xRow_temp").html(res.totalRows);
                $("#partsTemporaryList tbody").html(str);
                setPage($("#ye_temp"), curr, totalPage, "partsTemporary", jsonStr);
            }
        }
    })
}
// creator: 李玉婷，2021-11-03 14:37:24，零组件查看
function partScan(id){
    $.ajax({
        "url":"../parts/getPartsDetail.do",
        "data":{
            "id": id
        },
        beforeSend:function(){ loading.open() ; },
        success:function(res){
            var data = res['data'];
            var part = data['part'];
            var assemble = data['pdAssemble'];
            var detail = '';
            $("#partData").html(JSON.stringify(part));
            $("#seeCreater").html(handleNull(part.createName) + ' ' + (new Date(part['createDate']).format("yyyy-MM-dd hh:mm:ss")));
            $("#updateLast").html(handleNull(part.updateName) + ' ' + (new Date(part['updateDate']).format("yyyy-MM-dd hh:mm:ss")));
            $("#stopName").html(handleNull(part.updateName) + ' ');
            $("#stopDate").html(new Date(part['updateDate']).format("yyyy-MM-dd hh:mm:ss"));
            partIntrolCommon($("#seePartsInit"));
            //中部查看
            $("#seeParts .several").each(function() {
                var name = $(this).data("name");
                $(this).html((assemble[name] || '0'));
            })
            if (part.process == '1') {
                detail = '本件委托外部加工';
                $(".only").hide();
                loading.close() ;
            } else {
                $(".only").show();
                if (part.process == '2') {
                    detail = '本件为自制,';
                } else if (part.process == '3') {
                    detail = '本件本公司能自制，但有时外包,';
                }
                if (part.composition == '2'){
                    $(".matInfo3").show().siblings().hide();
                    detail += '为装配件';
                    getviewSettings();
                    getDirectlyPart(id)
                }else if (part.composition == '3'){
                    $(".matInfo2").show().siblings().hide();
                    detail += '材料需由多种原料先混合';
                    getPartFormulaList(id);
                }else if (part.composition == '4'){
                    $(".matInfo1").show().siblings().hide();
                    detail += '由所购买的单一材料直接加工而成';
                    getPartMaterialList(id);
                }
            }
            $("#compositionDetails").html(detail);
            bounce.show($("#seeParts"));
        }
    })
}
// creator: 李玉婷，2022-01-14 15:48:23，获取材料
function getPartMaterialList(id){
    $.ajax({
        "url":"../parts/getMaterialList.do",
        "data":{
            "id": id
        },
        success:function(res){
            var list = res['data'];
            var str = '';
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    str +=
                        '<tr>' +
                        '    <td>' + list[i].code + '</td>' +
                        '    <td>' + list[i].name+ ((handleNull(list[i].specifications) === "") ? '' : '、' + list[i].specifications) + ((handleNull(list[i].model) === "") ? '' : '、' + list[i].model)  + '</td>' +
                        '    <td>' + list[i].unit + '</td>' +
                        '</tr>';
                }
            }
            $(".matInfo1 table tbody").html(str);
        }
    })
}
// creator: 李玉婷，2022-01-14 16:28:23，获取配方
function getPartFormulaList(id) {
    $.ajax({
        "url":"../parts/getFormulaList.do",
        "data":{
            "id": id
        },
        success:function(res){
            var list = res['data'];
            var str = '';
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    str +=
                        '<tr>' +
                        '    <td>' + list[i].code + '</td>' +
                        '    <td>' + list[i].name + '</td>' +
                        '    <td>' + list[i].majorIngredient + '</td>' +
                        '    <td>' + list[i].minorIngredient + '</td>' +
                        '</tr>';
                }
            }
            $(".matInfo2 table tbody").html(str);
        }
    })
}
// creator: 李玉婷，2022-01-24 18:24:46，获取当前状态下本件直接包含的零组件
function getDirectlyPart(id) {
    $.ajax({
        "url": '../parts/getAllPartsByParts.do',
        "data": {
            "id": id,
            "level": "1"
        },
        success: function (res) {
            var list = res.data;
            var html = '';
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    html +=
                        '<tr data-id="'+ list[i].id +'">' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name + ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+ ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+'</td>' +
                        '    <td>'+ list[i].unit +'</td>' +
                        '    <td>'+ handleNull(list[i].amount) +'</td>' +
                        '    <td>'+ charge(list[i].process,"process") +'</td>' +
                        '    <td>'+ charge(list[i].composition,"composition") +'</td>' +
                        '    <td>'+ netWeightLimit(list[i].netWeight,list[i]['weightUnit']) +'</td>' +
                        '    <td class="createInfo">' + list[i].gcName + '&nbsp;&nbsp;' + new Date(list[i].date).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue linkBtn" data-type="partScan" data-num="4">查看</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[i])+'</span>' +
                        '    </td>' +
                        '</tr>';
                }
            }
            $(".matInfo3").find("tbody").html(html);
        }
    })
}
// creator: 李玉婷，2021-11-03 08:58:31，首页查询
function searchKeyPartsBase(num, obj){
    pType = num;
    var categoryName = $("#firstLevelName").html();
    var category = '';
    if (categoryName != '全部') {
        category = $("#firstLevelName").data("categoryid");
    }
    var pData = {
        "pageSize":20,
        "currPage":1,
        "param": '',
        "process": '',
        "composition": ''
    }
    if (num == 2) {
        var keyword = $("#searchKeyBase").val();
        pData.param = keyword;
    } else if (num == 3) {
        pData.process = obj.val();
    } else if (num == 4) {
        pData.composition = obj.val();
    }
    if ($(".inProduct").is(":visible")){
        pData.category = category;
        pData.categoryName = categoryName;
        getPartsList(pData);
    } else if ($(".inSuspend").is(":visible")){
        getSuspendPtList(pData);
    }
    if (num == 2) {
        showMainCon(3);
    }
}
// creator: 李玉婷，2021-11-03 08:58:31，暂存查询
function searchTempParts(num, obj){
    pType = num;
    var pData = {
        "pageSize":20,
        "currPage":1,
        "param": '',
        "process": '',
        "composition": ''
    }
    if (num == 2) {
        var keyword = $("#searchKeyBase1").val();
        pData.param = keyword;
    } else if (num == 3) {
        pData.process = obj.val();
    } else if (num == 4) {
        pData.composition = obj.val();
    }
    getTempPartsList(pData);
    if (num == 2) {
        showMainCon(3);
    }
}
// creator: 李玉婷，2020-03-27 17:42:06，分类
function kindBtn(obj) {
    var json = obj.siblings("div").children(".kindId").html();
    json = JSON.parse(json);
    var strson = '<span data-id="'+ json.id +'" data-name="'+ json.value +'"> >'+ json.value +'</span>';
    $("#curID").append(strson);
    setInparts(json.id,json.value)
}
// creator: 李玉婷，2020-04-03 08:42:37，停用零组件
function suspendPartSet(obj){
    pType = 1;
    $(".mainCon1 input").val("");
    $(".mainCon1 select").val("");
    $(".inSuspend").show().siblings().hide();
    $(".suspendBtn").hide();
    $(".left-bottom").show();
    $("#kindsTree").html('');
    $("#addProductionBtn").hide();
    $("#firstLevelAmount").html('0');
    $("#firstLevelName").html(obj.html());
    var strson = '<span data-id="" data-name="'+ obj.html() +'"> >'+ obj.html() +'</span>';
    $("#curID").append(strson);
    var resData = {
        "pageSize":20,
        "currPage":1,
        "param": "",
        "process": "",
        "composition": ""
    };
    getSuspendPtList(resData);
}
// creator: 李玉婷，2020-03-31 11:21:01，获取停止生产的零组件
function getSuspendPtList(resData) {
    $.ajax({
        "url": "../parts/suspendPartsList.do",
        "data": resData,
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = resData;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye") , curr ,  totalPage , "InSuspendPd", jsonStr );
            $("#firstLevelAmount").html(res.suspendNum);
            var list = res.data;
            var html = '';
            if (list && list.length > 0){
                for(var i=0;i<list.length;i++){
                    html +=
                        '<tr data-id="'+ list[i].id +'">' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name + ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+ ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+'</td>' +
                        '    <td>'+ list[i].unit +'</td>' +
                        '    <td>'+ netWeightLimit(list[i].netWeight,list[i]['weightUnit']) +'</td>' +
                        '    <td>'+ handleNull(list[i].proNum) +'</td>' +
                        '    <td>'+ handleNull(list[i].num) +'</td>' +
                        '    <td class="createInfo">'+ list[i].createName + '&nbsp;&nbsp;' + new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue linkBtn" data-type="partScan" data-num="3">查看</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[i])+'</span>' +
                        '    </td>' +
                        '</tr>';
                }
            }
            if (pType == 1) {
                setPage( $("#ye") , curr ,  totalPage , "InSuspendPd", jsonStr );
                $("#xRow").html(res.suspendNum);
                $("#firstLevelAmount").html(res.suspendNum);
                $("#suspendList tbody").html(html);
            } else if  (pType == 2) {
                $("#productionListQ").data("type", 3);
                $("#ssNum").html(res.suspendNum);
                $(".con3Ttl:eq(0)").html("曾直接参与组装的产品");
                $(".con3Ttl:eq(1)").html("曾直接参与组装的组件");
                $("#productionListQ tbody").html(html);
                setPage( $("#ye2") , curr ,  totalPage , "InSuspendPd", jsonStr );
            } else if (pType == 3 || pType == 4) {
                $("#xRow").html(res.suspendNum);
                $("#suspendList tbody").html(html);
            }
        }
    });
}
//  返回上一级
function gobackLstLevel(type){
    if (type == 1){
        var  n = $("#curID").children().length-1 ;
        var kindId = "",kindName = "";
        var m = n - 1 ;
        if( m >= 0 ){
            $("#curID").children(":eq("+ n +")").remove() ;
            kindId = $("#curID").children(":eq("+ m +")").data("id");
            kindName = $("#curID").children(":eq("+ m +")").data("name");
            setInparts(kindId, kindName);
        }
    }else{
        $("#curID").children(":eq(0)").nextAll().remove() ;
        setInparts("","");
    }
}
// creator: 李玉婷，2021-12-06 13:02:24，中部查看
function seePartsDetail(num) {
    var pId = $("#seeParts").data("id");
    var url = '../parts/getProductListForZhiJie.do';
    if (num == 2) {
        url = '../parts/getProductListForJanJie.do';
    } else if (num == 3) {
        url = '../parts/getPartsListForZhiJie.do';
    } else if (num == 4) {
        url = '../parts/getProductListForBeforeZhiJie.do';
    } else if (num == 5) {
        url = '../parts/getProductListForBeforeJianjie.do';
    } else if (num == 6) {
        url = '../parts/getPartsListForBeforeZhiJie.do';
    }
    $.ajax({
        "url": url,
        "data": {
            "id":pId
        },
        success: function (res) {
            var list = res.data.list;
            var html = '',ttl1 = '';
            if (num == 1) {
                ttl1 = '当前直接含有本件的产品';
                $(".introTtl").html("当前直接含有本件的产品有"+list.length+"种，具体如下：");
            } else  if (num == 2) {
                ttl1 = '当前间接含有本件的产品';
                $(".introTtl").html("当前间接含有本件的产品有"+list.length+"种，具体如下：");
            } else  if (num == 3) {
                ttl1 = '当前直接含有本件的组件';
                $(".introTtl").html("当前直接含有本件的组件有"+list.length+"种，具体如下：");
            } else  if (num == 4) {
                ttl1 = '曾直接含有本件的产品';
                $(".introTtl").html("曾直接含有本件的产品有"+list.length+"种，具体如下：");
            } else  if (num == 5) {
                ttl1 = '曾间接含有本件的产品';
                $(".introTtl").html("曾间接含有本件的产品有"+list.length+"种，具体如下：");
            } else  if (num == 6) {
                ttl1 = '曾直接含有本件的组件';
                $(".introTtl").html("曾直接含有本件的组件有"+list.length+"种，具体如下：");
            }
            $("#seePartsDetail .bonceHead span").html(ttl1);
            if (list && list.length > 0){
                for(var i=0;i<list.length;i++){
                    html+=
                        '<tr data-id="'+ list[i].id +'">' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name + ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+ ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+'</td>' +
                        '    <td>'+ handleNull(list[i].level) +'</td>' +
                        '    <td>'+ handleNull(list[i].amount) +'</td>' +
                        '    <td class="createInfo">'+ list[i].gcName + '&nbsp;&nbsp;' + new Date(list[i].date).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '</tr>';
                }
            }
            $("#seePartsDetail .combin tbody").append(html);
            bounce_Fixed.show($("#seePartsDetail"));

        }
    });
}
// creator: 李玉婷，2021-12-13 08:06:44，组件当前全部的零组件
function allRelatedParts() {
    var pId = $("#seeParts").data("id");
    var url = '/parts/getAllPartsByParts.do';
    $("#seePartsGroup .tbMain").find("tr:eq(0)").nextAll().remove();
    var view = JSON.parse($("#viewSettingsState").html());
    var ttl = '名称';
    if (view.frame == 1) {
        $(".trends-specifications").hide();
        $(".trends-model").hide();
        if (view.component == 1) {
            ttl = '名称/规格/型号';
            $(".trends-name").html();
        } else if (view.component == 2) {
            ttl = '名称';
        } else if (view.component == 3) {
            ttl = '名称/规格';
        } else {
            ttl = '名称/型号';
        }
    } else {
        if (view.component == 1) {
            $(".trends-specifications").show();
            $(".trends-model").show();
        } else if (view.component == 2) {
            $(".trends-specifications").hide();
            $(".trends-model").hide();
        } else {
            $(".trends-specifications").show();
            $(".trends-model").hide();
        }
    }
    $(".trends-name").html(ttl);
    $.ajax({
        "url": url,
        "data": {
            "id": pId
        },
        success: function (res) {
            var list = res.data;
            var html = '';
            if (list && list.length > 0) {
                var str = '';
                for (var t = 0; t < list.length; t++) {
                    str = resultAllParts(view,list[t]);
                    html +=
                        '<tr data-id="' + list[t].id + '">' +
                        '    <td>' + list[t].level + '</td>' +
                        '    <td>' + handleNull(list[t].innerSn) + '</td>' +
                        str +
                        '    <td>'+ handleNull(list[t].unit) +'</td>' +
                        '    <td>'+ handleNull(list[t].amount) +'</td>' +
                        '    <td>'+ charge(list[t].process,"process") +'</td>' +
                        '    <td>'+ charge(list[t].composition,"composition") +'</td>' +
                        '    <td>'+ netWeightLimit(list[t].netWeight,list[t]['weightUnit']) +'</td>' +
                        '    <td class="createInfo">' + list[t].gcName + '&nbsp;&nbsp;' + new Date(list[t].date).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '</tr>';
                }
            }
            $("#seePartsGroup .tbMain").find("tbody").append(html);
            bounce_Fixed.show($("#seePartsGroup"));
        }
    })
}
// creator: 李玉婷，2022-01-04 15:11:04，全部零组件展示页上的当前设置输出
function resultAllParts(view,data){
    var result = '';
    if (view.frame == 1) {
        if (view.component == 1) {
            result = '<td>' + data.name + ((handleNull(data.specifications) === "") ? '' : '、' + data.specifications) + ((handleNull(data.model) === "") ? '' : '、' + data.model) + '</td>';
        } else if (view.component == 2) {
            result = '<td>' + data.name + '</td>' ;
        } else if (view.component == 3) {
            result = '<td>'  + data.name + ((handleNull(data.specifications) === "") ? '' : '、' + data.specifications) + '</td>';
        } else if (view.component == 4) {
            result = '<td>' + data.name + ((handleNull(data.model) === "") ? '' : '、' + data.model) + '</td>';
        }
    } else {
        if (view.component == 1) {
            result =
                '<td>' + data.name + '</td>' +
                '<td>' + data.specifications + '</td>' +
                '<td>' + data.model + '</td>' ;
        } else if (view.component == 2) {
            result = '<td>' + data.name + '</td>' ;
        } else if (view.component == 3) {
            result = '<td>' + data.name + '</td>' +
                '<td>' + data.specifications + '</td>' ;
        } else if (view.component == 4) {
            result = '<td>' + data.name + '</td>' +
                '<td>' + data.model + '</td>' ;
        }
    }
    return result;
}
// creator: 李玉婷，2021-12-13 08:06:44，曾经包含的零组件
function allRelatedPartsOnce() {
    var pId = $("#seeParts").data("id");
    var url = '/parts/getAllPartsByBeforeParts.do';
    $("#seeOncePartsGroup .tbMain2 tr:gt(0)").remove();
    $.ajax({
        "url": url,
        "data": {
            "id": pId
        },
        success: function (res) {
            var list = res.data;
            var html = '',str = '';
            if (list && list.length > 0) {
                for (var t = 0; t < list.length; t++) {
                    html +=
                        '<tr data-id="' + list[t].id + '">' +
                        '    <td>' + list[t].level + '</td>' +
                        '    <td>' + handleNull(list[t].innerSn) + '</td>' +
                        '    <td>'+ list[t].name + ((handleNull(list[t].model) === "")?'':'、'+ list[t].model)+ ((handleNull(list[t].specifications) === "")?'':'、'+list[t].specifications)+'</td>' +
                        '    <td>'+ handleNull(list[t].unit) +'</td>' +
                        '    <td>'+ handleNull(list[t].amount) +'</td>' +
                        '    <td>'+ charge(list[t].process,"process") +'</td>' +
                        '    <td>'+ charge(list[t].composition,"composition") +'</td>' +
                        '    <td>'+ netWeightLimit(list[t].netWeight,list[t]['weightUnit']) +'</td>' +
                        '    <td class="createInfo">' + list[t].gcName + '&nbsp;&nbsp;' + new Date(list[t].date).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '    <td class="createInfo">' + list[t].delName + '&nbsp;&nbsp;' + new Date(list[t].delDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '</tr>';
                }
            }
            $("#seeOncePartsGroup .tbMain tbody").append(html);
            bounce_Fixed.show($("#seeOncePartsGroup"));
        }
    })
}
// creator: 李玉婷，2022-01-04 14:53:26，获取零组件设置
function getviewSettings(){
    $.ajax({
        "url": '/parts/getViewSetting.do',
        "data": "",
        async: false,
        success: function (res) {
            var data = res.data;
            $("#viewSettingsState").html(JSON.stringify(data));
        }
    })
}
// creator: 李玉婷，2021-12-15 11:49:51，查看零组件设置
function viewSettings() {
    var data = JSON.parse($("#viewSettingsState").html());
    var mar = Number(data.component) - 1;
    var dot = Number(data.frame) - 1;
    $("#settingsCon .leMar i").attr("class","fa fa-circle-o");
    $(".settingsCon .leMar:eq(0)").find("i").eq(mar).attr("class","fa fa-dot-circle-o");
    $(".settingsCon .leMar:eq(1)").find("i").eq(dot).attr("class","fa fa-dot-circle-o");
    $(".settingsCon .leMar:eq(2)").find("i").eq(0).attr("class","fa fa-dot-circle-o");
    bounce_Fixed.show($('#viewSettings'));
}
// creator: 李玉婷，2021-12-15 12:25:00，零组件设置编辑提交
function viewSettingsOk() {
    var state = $(".settingsCon .leMar:eq(2) i").index($(".settingsCon .leMar:eq(2) .fa-dot-circle-o"));
    var mar = $(".settingsCon .leMar:eq(0) i").index($(".settingsCon .leMar:eq(0) .fa-dot-circle-o"));
    var dot = $(".settingsCon .leMar:eq(1) i").index($(".settingsCon .leMar:eq(1) .fa-dot-circle-o"));
    mar = Number(mar) +1;
    dot = Number(dot) +1;
    var param = {
        "component": mar,
        "frame": dot
    }
    $("#viewSettingsState").html(JSON.stringify(param));
    bounce_Fixed.cancel();
    if (state == 0) {
        $.ajax({
            "url": '/parts/setViewSetting.do',
            "data": param,
            success: function (res) {
            }
        })
    }
}
// creator: 李玉婷，2021-12-15 21:49:48，修改构成确定
function editPartsSourceSure() {
    var editP = $("#actual_process").val();
    var editC = $("#actual_composition").val();
    var info = JSON.parse($("#partData").html());
    var pId = info.id;
    var process = info.process,composition = info.composition;
    if (editP == '' || (editP != 1 && editC == "")) {
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }
    if (process == editP && composition == editC) {
        layer.msg("未进行修改，请修改后再确定！");
        return false;
    }
    $.ajax({
        "url": '/parts/updateComposition.do',
        "data": {
            "id": pId,
            "process": editP,
            "composition": editC
        },
        success: function (res) {
            bounce_Fixed.cancel();
            partScan(pId);
            if ((!composition || composition == "") && editC == 2) {
                var item = JSON.parse($("#partData").html());
                item.process = editP;
                item.composition = editC;
                $("#splitGS2 .bonceHead span").html("零组件拆分");
                splitBtn(item , "2");
            } else {
                layer.msg("修改成功！");
            }
        }
    })
}
// creator: 李玉婷，2021-12-22 12:23:08，3秒提示
function giveTip() {
    var info = JSON.parse($("#partData").html());
    var process = info.process,composition = info.composition;
    if (process == '2' || process == '3') {
        layer.msg("<p>操作失败！</p><p>如本件确需此项修改，请新增产品。</p>");
    }
}
// create:hxz 2020-06-25 拆分商品/零部件
function splitBtn(item , type) {
    var process = item['process'] ;
    var composition = item['composition'] ;
    if(type == "" || ((process == "2" || process == "3") && composition == "2")){
        getParts(item, type);
        if(type == "2"){
            bounce_Fixed.show($("#splitGS2"));
        }/*else{
            bounce_Fixed.show($("#splitGS"));
        }*/
    }else{
        if(process == "1"){
            $("#giveTip8 .msg").html("该零组件为外购成品，无需在此编辑！");
        }else if(composition == "3"){
            $("#giveTip8 .msg").html("该零件的材料需由多种原料先混和，无需在此编辑！");
        }else if(composition == "4"){
            $("#giveTip8 .msg").html("该零件系由一种材料直接加工而成，无需在此编辑！");
        }else{
            $("#giveTip8 .msg").html("后台，， 把他的 process 和 composition 返回！！");
        }
        bounce_Fixed2.show($("#giveTip8"));
    }
}
// create:hxz 2020-06-25 删除零部件
function delPartOk() {
    var item = JSON.parse(editObj.siblings(".hd").html());
    var curP = partsList[partsList.length - 1] ;
    $.ajax({
        "url":"../constitute/lingJianDelete.do",
        "data":{ "id":item.id , "parentId":curP.gsItem.id },
        success:function (res) {
            bounce_Fixed2.cancel();
            var code = res['code'];
            if(code == 200){
                let lastPart = partsList[partsList.length - 1];
                let list = lastPart.data.filter(sys=>sys.id !== item.id);
                lastPart.data = list;
                layer.msg("操作成功");
                setPartsData(lastPart , lastPart.type);
                //editObj.parent().parent().remove();
            }else{
                layer.msg("操作失败");
            }

        }
    })
}
// create:hxz 2020-06-25 设置零部件录入的默认值
function setGSInfo(item , innerSnFalse) {
    for(var key in item){
        $("#" + key).val(item[key]).attr("disabled", "true").parent().attr("disabled", "true");
        if(key == "id"){
            $("#oldId").val(item[key]);
        }
    }
    getUnitList($("#unitId"), 4, item['unitId']);
    var demoLen = item['demo'] ? item['demo'].length :"0" ;
    $(".textMax").html(demoLen + "/100");
    if(innerSnFalse === 1){

    }else{
        $("#innerSn").removeAttr("disabled").parent().removeAttr("disabled");
    }
    $("#amount").removeAttr("disabled").parent().removeAttr("disabled");
}
// create:hxz 2020-06-25 与录入匹配
function gsMatch() {
    $("#selecGS").show();
    var innerEntry = $("#innerSn").val();
    $("#selecGS option").hide();
    var hasMatch = false;
    $("#selecGS option").each(function () {
        var i_innerSn = $(this).html();
        if(i_innerSn.indexOf(innerEntry) != -1){
            $(this).show();
        }
        if(i_innerSn === innerEntry){
            hasMatch = true;
            setSelectOption($(this));

        }
    });
    if(!hasMatch){
        $("#partEntry td").removeAttr("disabled");
        $("#partEntry input:not(#innerSn)").val("").removeAttr("disabled");
        $("#partEntry select").val("").removeAttr("disabled");
        $("#partEntry textarea").val("").removeAttr("disabled");
    }
}
function setSelectOption(thisObj) {
    var i_item = thisObj.val();
    i_item = JSON.parse(i_item);
    $("#id").val(i_item['id']);
    for(var key in i_item){
        if(key != "innerSn"){
            $("#" + key).val(i_item[key]).attr("disabled", "true").parent().attr("disabled", true);
        }
    }
    $("#innerSn").val(i_item['innerSn']);
    $("#amount").removeAttr("disabled").parent().removeAttr("disabled");
}
// create:hxz 2020-06-25 拆分返回/关闭
function goBack(type){
    // 0 - 零部件的返回 ; 1 - 返回产品拆分 ; 2 - 产品拆分的返回主页
    if(type == 0){
        partsList.pop();
        if (partsList.length > 0) {
            var lastPart =  partsList[partsList.length - 1];
            setPartsData(lastPart , lastPart.type);
        } else {
            bounce_Fixed.cancel();
        }
    }

}
// create:hxz 2020-06-25 确定拆分完毕
function splitOKBtn(type){
    var len = $("#splitGS tbody:eq(1)").children("tr").length ;
    if(len == 1){
        $("#giveTip6 .tipC").html("您尚未录入构成该产品的零组件！");
        bounce_Fixed3.show($("#giveTip6"));
        return false;
    }
    var isAll = true, splitGSCon = "#splitGS" ;
    if(type == 2){ splitGSCon = "#splitGS2" ; }
    $(splitGSCon + " tbody:eq(1)").children("tr:gt(0)").each(function () {
        var td9 = $(this).children(":eq(9)").html();
        if (td9 == "未拆分完"){
            isAll = false ;
        }
    })
    if(isAll){
        splitOK();
    }else {
        layer.msg("本件尚有零组件未拆分完，故系统中为暂存。拆分完的结果才予正式保存。");
        bounce_Fixed.cancel();
    }
}
function splitOK(){
    var curP = partsList[partsList.length - 1];
    var gsItem = curP.gsItem ;
    var pid = gsItem.id ;
    $.ajax({
        // "url": "../constitute/updateProductBase.do",
        "url": "../constitute/splitComplete.do",
        "data":{"id": pid },
        success:function (res) {
            bounce_Fixed2.cancel();
            var code = res['code'];
            if(code == 200){
                partsList.pop();
                if(partsList.length > 0){
                    layer.msg("操作成功");
                    var lastPart =  partsList[partsList.length - 1];
                    partsList.pop();
                    getParts(lastPart.gsItem, lastPart.type); // 刷新上一个零部件
                }else{
                    // 产品的拆分完了
                    layer.msg("修改成功！");
                    partScan(pid);
                    bounce_Fixed.cancel();
                }
            }else{
                layer.msg("操作失败：" + res.msg);
            }

        }
    })
}
// create:hxz 2020-06-25 录入零部件 - 获得可选的图号
function partEntryBtn(){
    $("#partEntry input").val("")
    $("#partEntry select").val("")
    $("#partEntry textarea").val("")
    var curGS = partsList[partsList.length - 1] ;
    var item = curGS.gsItem
    $.ajax({
        "url": "../constitute/selectLingJianListByParent.do",
        "data":{ "id":item.id  },
        success:function (res) {
            var list = res['data'], str = "" ;
            if(list){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<option value='"+ JSON.stringify(list[i]) +"'>"+ list[i]['innerSn'] +"</option>"
                }
            }
            $("#selecGS").html(str);
        }
    });
    $(".bounce_Fixed2").everyTime('0.5s','partEntry',function(){
        var isNull = false;
        var data = {} ;
        var id = $("#id").val();
        if(id){
            var amount = $("#amount").val();
            if(!amount){ isNull = true; }
        }else{
            var notMust = ["innerSn", "name", "process", "unitId", "unit", "amount"];
            for(var key in notMust){
                var val = $("#" + notMust[key]).val();
                data[notMust[key]] = val ;
                if(!val){
                    isNull = true
                }
            }
            if(!isNull){
                if(data['process'] == 1){
                    var netWeight = $("#netWeight").val()
                    var weightUnit = $("#weightUnit").val()
                    if(netWeight == "" || weightUnit == ""){
                        isNull = true;
                    }
                }else{
                    var composition = $("#composition").val();
                    if(composition == 2){
                    }else{
                        var netWeight = $("#netWeight").val();
                        var weightUnit = $("#weightUnit").val();
                        if(netWeight == "" || weightUnit == ""){
                            isNull = true;
                        }
                    }
                }
            }
        }
        if(isNull){
            $("#partEntryOK").attr("class", "ty-btn bounce-cancel ty-btn-big ty-circle-5");
        }else{
            $("#partEntryOK").attr("class", "ty-btn bounce-ok ty-btn-big ty-circle-5");
        }

    })
}
// create:hxz 2020-06-27 零部件匹配
function startMatch(e){
    e.stopPropagation();
    $(".bounce_Fixed2").everyTime('0.5s',"gsMatch",function(){
        gsMatch();
    });
}
function stopMtch(e){
    $("#selecGS").hide();
    $(".bounce_Fixed2").stopTime("gsMatch")
}
function partEntryOK(){
    var isNull = $("#partEntryOK").hasClass("bounce-cancel");
    if(isNull){
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }
    if($("#netWeight").val().length > 8){
        layer.msg("您录入的数字位数太多了，请选择合适的重量单位！");
        return false;
    }
    var curP = partsList[partsList.length-1];
    var gsItem = curP.gsItem ;
    var type = curP.type ;
    var pid = gsItem.id ;
    var data = { "pdBaseId": pid } ;
    $("#partEntry .entry").each(function(){
        var key = $(this).attr("id");
        var val = $(this).val();
        data[key] = val ;
    });
    if(!data["id"]){
        if(!data["process"]){
            $("#giveTip6 .tipC").html("请选择加工情况");
            bounce_Fixed3.show($("#giveTip6"));
            return false;
        }else{
            if(data["process"]=='1'){

            }else{
                if(!data["composition"]){
                    $("#giveTip6 .tipC").html("请选择构成情况");
                    bounce_Fixed3.show($("#giveTip6"));
                    return false;
                }
            }
        }
    }else{
        if(!data["process"] && !data["composition"]){
            $("#giveTip6 .tipC").html("该产品未走完构成流程，如想使用请先处理");
            bounce_Fixed3.show($("#giveTip6"));
            return false;
        }
    }

    $.ajax({
        "url":"../constitute/addPdBaseLingJian.do",
        "data":data ,
        success:function (res) {
            bounce_Fixed2.cancel();
            var code = res['code'];
            if(code == "200"){
                layer.msg("操作成功");
                partsList.pop();
                getParts(gsItem, type);

            }else{
                layer.msg("操作失败:" + res.msg);
            }

        }
    })
}
// create:hxz 2020-06-21 渲染拆分数据
function setPartsData(res, type){
    var itemGS = res['gsItem'] , netWeightCount = 0;
    $("#gsName").html(itemGS['name']); // 顺便给零件录入的赋值
    var str = itemStr(itemGS,1) +
        "    <td>"+ netWeightLimit(itemGS.netWeight,itemGS.weightUnit) +"</td>" ;
    $("#splitGS" + type).find("tbody:eq(0)").children(":eq(1)").html(str);
    var listStr = "" , list = res['data'];
    if(list){
        var btnStr = '';
        for(var i = 0 ; i < list.length ; i++){
            var item = list[i];
            let {weightUnit, netWeight, amount} = item;
            if (weightUnit && netWeight && !isNaN(netWeight) && handleNull(weightUnit) != ""){
                var power = 1;
                if (weightUnit == '2') {
                    power = 1000;
                } else if (weightUnit == '3') {
                    power = 1000000;
                } else if (weightUnit == '4') {
                    power = 1000000000;
                }
                netWeightCount += Number(netWeight) * power * (amount || 0);
            }
            item.num > 1? btnStr="<span type=\"btn\" data-type=\"noSplit\" class=\"ty-color-gray\">编辑</span>":btnStr="<span type=\"btn\" data-type=\"splitBtn2\" class=\"ty-color-blue\">编辑</span>";
            listStr += "<tr>" + itemStr(item,1) +
                "    <td>"+ netWeightLimit(netWeight,weightUnit) +"</td>" +
                "    <td>"+ (amount || "") +"</td>" +
                "    <td>"+ charge(item['process'] , 'process') +"</td>" +
                "    <td>"+ charge(item['composition'] , 'composition') +"</td>" +
                "    <td>"+ charge(item['split'] , 'split') +"</td>" +
                "    <td>" + btnStr  +
                "        <span type=\"btn\" data-type=\"partsEdit\" class=\"ty-color-blue\">修改装配数量</span>" +
                "        <span type=\"btn\" data-type=\"delBtn\" class=\"ty-color-blue\">删除</span>" +
                "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                "    </td>" +
                "</tr>";
        }
        if(netWeightCount > 0 ) {
            var weightUnitStr = weightUnitCharge(netWeightCount);
            $("#splitGS" + type).find("tbody:eq(0)").children(":eq(1)").find(":last").html(weightUnitStr);
        }
    }
    $("#splitGS" + type).find("tbody:eq(1)").children(":gt(0)").remove() ;
    $("#splitGS" + type).find("tbody:eq(1)").append(listStr);
}
// create:hxz 2020-06-21 获取拆分零部件列表
var partsList = [] ; // 零件拆分树列表
function getParts(item, type) {
    // type : ""-产品 ， 2-零件
    var str = "" ;
    $.ajax({
        "url": "../constitute/getPdOrLingJianList.do"  ,
        "data":{ 'id':item.id } ,
        success:function(res){
            if(type == 2){ // 零件的拆分管理
                res['gsItem'] = item ;
                res['type'] = type ;
                partsList.push(res);
                setPartsData(res , type);
            }else{
                res['gsItem'] = item ;
                res['type'] = "" ;
                partsList = [res];
                setPartsData(res , type);
            }
        }
    })
}

// create:hxz 2020-06-21 翻译键值
function charge(val , type) {
    var str = ""
    switch (type){
        case 'process':
            if(val == "1"){ str = "外购"; }
            if(val == "2"){ str = "自制"; }
            if(val == "3"){ str = "自制+外包"; }
            break;
        case 'weightUnit':
            if(val == "1"){ str = "毫克"; }
            if(val == "2"){ str = "克"; }
            if(val == "3"){ str = "千克"; }
            if(val == "4"){ str = "吨"; }
            break;
        case 'composition':
            if(val == "1"){ str = "成品"; }
            if(val == "2"){ str = "装配"; }
            if(val == "3"){ str = "制造"; }
            if(val == "4"){ str = "制造"; }
            break;
        case 'split':
            if(val == "0"){ str = "无需拆分"; }
            if(val == "1"){ str = "未拆分完"; }
            if(val == "2"){ str = "拆分完毕"; }
            break;
        case 'state':
            if(val == "0"){ str = ""; }
            if(val == "1"){ str = "开发中"; }
            if(val == "2"){ str = "开发完成"; }
            break;
    }
    return str
}
// create:hxz 2020-06-21 设置商品键值对
function setKeyByID(id , key , val) {
    var list = GSList ;
    for(var i = 0 ; i < list.length ; i++){
        if(list[i]['id'] == id){
            list[i][key] = val ;
        }
    }
}
function itemStr(item , num) { // num = 1 不显示create
    var str = "<tr>" ;
    if(item){
        str =
            "    <td>"+ item['innerSn'] +"</td>" +
            "    <td>"+ item['name'] +"</td>" +
            "    <td>"+ (item['model'] || "") +"</td>" +
            "    <td>"+ (item['specifications'] || "") +"</td>" +
            "    <td>"+ (item['unit'] || "" ) +"</td>" ;
        if(num !== 1){
            str += "<td>"+ item['createName'] + " " + (new Date(item['createName']).format("yyyy-MM-dd hh:mm:ss")) + "</td>";
        }
    }
    return str ;
}
// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectID) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    if(selectID == item['id']){
                        str += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit(obj) {
    bounce_Fixed3.show($("#addUnit"));
    $("#addUnit input").val("");
    $("#addUnit").data("obj", obj);
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    var obj = $("#addUnit").data("obj");
    obj = obj.parents("tr").next().find("select[name='unitId']");
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed3.cancel();
                loading.open();
                getUnitList(obj, module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed3.show($("#tip1"));
            }
        }
    })
}
// creator: hxz，2020-09-02 14:51:41   计量单位设置
function unitAssign(obj) {
    var unitName = obj.children("option:selected").html();
    obj.siblings("input").val(unitName)
}
// creator: 李玉婷，2021-12-15 17:22:12，修改基本信息提交
function updatePartSure(){
    var emptyNum = 0;
    $("#updateParts [require]").each(function(){
        if($(this).val() == ""){ emptyNum++; }
    });
    if (emptyNum > 0){
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }else{
        var id = $("#seeParts").data("id");
        var params = {
            id: id
        };
        $("#updateParts [need]").each(function(){
            var nameData = $(this).attr('name');
            var html = $(this).val();
            params[nameData] = html;
        });
        $.ajax({
            "url": "../parts/updatePartsBase.do",
            "data": params,
            success: function (res) {
                if (res.success == 1) {
                    let jsonObj = {}, door = false;
                    if (pType == 1) {
                        jsonObj = JSON.parse($("#ye").find(".json").html());
                        if ($(".inSuspend").is(":visible")) {door = true;}
                    } else {
                        jsonObj = JSON.parse($("#ye2").find(".json").html());
                        if ($("#productionListQ").data("type") == 3) {door = true;}
                    }
                    bounce_Fixed.cancel();
                    partScan(id);
                    if (door) {
                        getSuspendPtList(jsonObj);
                    } else {
                        getPartsList(jsonObj);
                    }
                }else{
                    var msg = res.error.message;
                    $("#unfilledTip_ms").html(msg);
                    bounce_Fixed2.show($("#unfilledTip"));
                }
            }
        });
    }
}
// creator: 李玉婷，2021-12-16 09:05:59，基本信息修改记录
function baseRecordList(num) {
    let url = ``;
    var info = JSON.parse($("#partData").html());
    $("#assemblyTip").hide();
    if (num == 1) {
        url = `../parts/getRecordBaseList.do`;
        $("#baseRecords .bonceHead span").html("基本信息的修改记录");
    } else if (num == 2) {
        url = `../parts/getCompositionRecordBaseList.do`;
        $("#baseRecords .bonceHead span").html("来源/构成修改记录");
    }
    $.ajax({
        "url":url,
        "data": { "id": info.id  } ,
        success:function(res) {
            let list = res.data.list || [];
            bounce_Fixed.show($("#baseRecords"));
            let curStaStr = ``;
            if(list.length >0){
                let str = ``;
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    i == 0 ? item.front = 0:item.front = list[Number(i) - 1].id;
                    str += `<tr>
                             <td>${i===0 ? "原始信息":"第"+ i +"次修改后"}</td>
                             <td class="ty-td-control">
                                <span class="linkBtn ty-color-blue" data-type="baseRecordScan" data-source="${num}">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                                <td>${i===0 ?item.createName:item.updateName} ${i===0 ?new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss"):new Date(item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                $("#baseRecords tbody").children(":gt(0)").remove();
                $("#baseRecords tbody").append(str);
                let n = list.length
                let last = list[n-1]
                curStaStr = `<span style="float: right;">修改人：${last.updateName} ${new Date(last.updateDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料为第${n-1}次修改后的结果。`
                $("#baseRecords table").show();
            }else{
                $("#baseRecords table").hide();
                curStaStr = `<span style="float: right;">创建人：${info.createName} ${new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料尚未经修改。`
            }
            $("#baseRecords .curSta").html(curStaStr);
        }
    });
}
// creator: 李玉婷，2021-12-16 10:00:57，基本信息修改记录查看
function baseRecordScan(obj) {
    var info = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        "url":"../parts/getRecordBaseDetails.do",
        "data": { "id": info.id,"front": info.front  } ,
        success:function(res) {
            var info = res.data.now;
            var key = '';
            $("#scanBaseRecords [need]").each(function(){
                key = $(this).data("name")
                if (key == 'phrase'){
                    $(this).html(charge(info[key] , 'state'));
                }else if (key == 'name'){
                    $(this).html(info[key] + ((handleNull(info["specifications"]) === "")?'':'、'+ info["specifications"])+ ((handleNull(info['model']) === "")?'':'、'+info['model']));
                }else if (key == 'netWeight'){
                    $(this).html(netWeightLimit(info[key],info['weightUnit']));
                }else {
                    $(this).html(info[key]);
                }
            });
            bounce_Fixed2.show($("#scanBaseRecords"));
        }
    });
}

// creator: 李玉婷，2022-03-17 13:39:28，构成修改记录详情
function compositionRecordScan(obj) {
    var orgInfo = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        "url":"../parts/getCompositionRecordBaseDetails.do",
        "data": { "id": orgInfo.id,"front": orgInfo.front  } ,
        success:function(res) {
            var info = res.data.now;
            var key = '', detail = '', html = '';
            $("#scancompositionRecords [need]").each(function(){
                key = $(this).data("name")
                if (key == 'phrase'){
                    $(this).html(charge(info[key] , 'state'));
                }else if (key == 'name'){
                    $(this).html(info[key] + ((handleNull(info["specifications"]) === "")?'':'、'+ info["specifications"])+ ((handleNull(info['model']) === "")?'':'、'+info['model']));
                }else if (key == 'netWeight'){
                    $(this).html(netWeightLimit(info[key],info['weightUnit']));
                }else {
                    $(this).html(info[key]);
                }
            });
            bounce_Fixed2.show($("#scancompositionRecords"));
            if (info.process) {
                if (info.process == '1') {
                    detail = '本件委托外部加工';
                    $(".otherOnly").hide();
                    loading.close() ;
                } else {
                    $(".otherOnly").show();
                    if (info.process == '2') {
                        detail = '本件为自制,';
                    } else if (info.process == '3') {
                        detail = '本件本公司能自制，但有时外包,';
                    }
                    const list = info.list || [];
                    if (info.composition == '2'){
                        $(".compRecord3").show().siblings().hide();
                        detail += '为装配件';
                        let frontList = [];
                        if (orgInfo.front == '0'){
                            frontList = list || [];
                        }else{
                            frontList = res.data.front.list || [];
                        }
                        if (list && list.length > 0) {
                            let red = ``, markRed = ``;
                            for (var i = 0; i < list.length; i++) {
                                //标红
                                frontList.findIndex((value)=>value.id==list[i].id) == -1? red = `trRed`:red = ``;
                                if (red == ``) {
                                    var line = frontList.filter(item=>item.id == list[i].id);
                                    handleNull(line[0].amount) == handleNull(list[i].amount)? markRed = ``:markRed = `tdRed`;
                                } else {
                                    markRed = ``;
                                }
                                html +=
                                    '<tr data-id="'+ list[i].id +'" class="'+ red +'">' +
                                    '    <td>'+ list[i].innerSn +'</td>' +
                                    '    <td>'+ list[i].name + ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+ ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+'</td>' +
                                    '    <td>'+ list[i].unit +'</td>' +
                                    '    <td class="'+ markRed +'">'+ handleNull(list[i].amount) +'</td>' +
                                    '    <td>'+ charge(list[i].process,"process") +'</td>' +
                                    '    <td>'+ charge(list[i].composition,"composition") +'</td>' +
                                    '    <td>'+ netWeightLimit(list[i].netWeight,list[i]['weightUnit']) +'</td>' +
                                    '    <td>' +
                                    '        <span class="ty-color-blue">查看</span>' +
                                    '    </td>' +
                                    '</tr>';
                            }
                            for (var i = 0; i < frontList.length; i++) {
                                //标红
                                if (list.findIndex((value)=>value.id==frontList[i].id) == -1){
                                    html +=
                                        '<tr data-id="'+ frontList[i].id +'" class="lineThrough">' +
                                        '    <td>'+ frontList[i].innerSn +'</td>' +
                                        '    <td>'+ frontList[i].name + ((handleNull(frontList[i].specifications) === "")?'':'、'+frontList[i].specifications)+ ((handleNull(frontList[i].model) === "")?'':'、'+ frontList[i].model)+'</td>' +
                                        '    <td>'+ frontList[i].unit +'</td>' +
                                        '    <td class="'+ markRed +'">'+ handleNull(frontList[i].amount) +'</td>' +
                                        '    <td>'+ charge(frontList[i].process,"process") +'</td>' +
                                        '    <td>'+ charge(frontList[i].composition,"composition") +'</td>' +
                                        '    <td>'+ netWeightLimit(frontList[i].netWeight,frontList[i]['weightUnit']) +'</td>' +
                                        '    <td>' +
                                        '        <span class="ty-color-blue">查看</span>' +
                                        '    </td>' +
                                        '</tr>';
                                }
                            }
                        }
                        $(".compRecord3").find("tbody").html(html);
                    }else if (info.composition == '3'){
                        $(".compRecord2").show().siblings().hide();
                        detail += '材料需由多种原料先混合';
                        if (list && list.length > 0) {
                            for (var i = 0; i < list.length; i++) {
                                html +=
                                    '<tr>' +
                                    '    <td>' + list[i].code + '</td>' +
                                    '    <td>' + list[i].name + '/' + list[i].materialName + '</td>' +
                                    '    <td>' + list[i].majorIngredient + '</td>' +
                                    '    <td>' + list[i].minorIngredient + '</td>' +
                                    '</tr>';
                            }
                        }
                        $(".compRecord2 table tbody").html(html);
                    }else if (info.composition == '4'){
                        $(".compRecord1").show().siblings().hide();
                        detail += '由所购买的单一材料直接加工而成';
                        if (list && list.length > 0) {
                            for (var i = 0; i < list.length; i++) {
                                html +=
                                    '<tr>' +
                                    '    <td>' + list[i].code + '</td>' +
                                    '    <td>' + list[i].name+ ((handleNull(list[i].specifications) === "") ? '' : '、' + list[i].specifications) + ((handleNull(list[i].model) === "") ? '' : '、' + list[i].model)  + '</td>' +
                                    '    <td>' + list[i].unit + '</td>' +
                                    '</tr>';
                            }
                        }
                        $(".compRecord1 table tbody").html(html);
                    }
                }
                $("#cDescript").html(detail);
            } else {
                $(".otherOnly").hide();
            }
        }
    });
}
// creator: 李玉婷，2022-03-28 12:47:39，停止生产的操作记录
function ptSuspendRecord() {
    let info = JSON.parse($("#partData").html());
    $('#partSuspendRecord tbody tr:gt(0)').remove();
    $.ajax({
        "url": '../product/getProductStopAndStartRecord.do',
        "data": { "id": info.id  } ,
        success:function(res) {
            let list = res.data.list || [];
            let str = ``, item = ``;
            if(list.length >0){
                for(let i = 0 ; i < list.length;i++){
                    item = list[i];
                    str += `<tr>
                                <td>${i===0 ?item.createName:item.updateName} ${i===0 ?new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss"):new Date(item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                                <td>${item.enabled === '0' ? "停止生产":"恢复生产"}</td>
                            </tr>`
                }
            }
            $('#partSuspendRecord tbody').append(str);
            bounce_Fixed.show($('#partSuspendRecord'));
        }
    });
}
// creator: 李玉婷，2022-01-25 08:19:56，关闭零组件查看
function closePartScan() {
    var len = partList.length;
    if (len <= 1){
        partList = [];
        bounce.cancel();
    } else {
        partList.splice(len-1,1);
        if (partList.length == 1) {
            var icon =  $("#seeParts").data("source");
            $(".partsScan2").show();
            if (icon == 1) {
                $(".partsScan1").show();
                $("#stopDetails").hide();
            } else if (icon == 2) {
                $(".partsScan1").hide();
                $("#stopDetails").hide();
            } else if (icon == 3) {
                $(".partsScan1").show();
                $("#stopDetails").show();
            }
        }
        partScan(partList[len-2]);
    }
}
// creator: 李玉婷，2021-12-15 21:17:22，当前零组件详情
function partIntrolCommon(obj) {
    var part = JSON.parse($("#partData").html());
    var key = '';
    obj.find("[need]").each(function(){
        key = $(this).data("name");
        if (key == 'phrase'){
            $(this).html(phraseFormat(part[key]));
        }else if (key == 'name'){
            $(this).html(part[key] + ((handleNull(part["specifications"]) === "")?'':'、'+ part["specifications"])+ ((handleNull(part['model']) === "")?'':'、'+part['model']));
        }else if (key == 'netWeight'){
            $(this).html(netWeightLimit(part[key],part['weightUnit']));
        }else{
            $(this).html(handleNull(part[key]));
        }
    });
}
//creator:hxz 2018-08-27 工具方法 - 格式化所处开发阶段
function phraseFormat(type) {
    if (type == 1) {
        return "开发中";
    }
    else if (type == 2) {
        return "开发完成";
    }
    else return "";
}
// creator: 李玉婷，2022-01-14 09:59:15，限制单重最多8位
function toFixedNetweight(str,size) {
    var result = str;
    if (str.toString().length > size) {
        result = str.substr(0,size);
        if (result.charAt(result.toString().length-1) == '.'){
            result = str.substr(0,Number(size)+1);
        }
    }
    return result;
}
// creator: 李玉婷，2022-02-11 15:47:01，单重+单位
function netWeightLimit(val, unit) {
    var netWeight = 0,result = '';
    if (handleNull(val) != "" && handleNull(unit) != "") {
        if (unit == '1') {
            netWeight = Number(val);
        } else if (unit == '2') {
            netWeight = Number(val)*1000;
        } else if (unit == '3') {
            netWeight = Number(val)*1000000;
        } else if (unit == '4') {
            netWeight = Number(val)*1000000000;
        }
        result = weightUnitCharge(netWeight);
    } else {
        result = '--';
    }
    return result;
}
// creator: 李玉婷，2022-02-11 11:33:33，单重换算
function weightUnitCharge(netWeightCount) {
    var weightUnitStr = '', unitStr = '';
    if (netWeightCount > 10 && netWeightCount < 1000000) {
        unitStr = ' 克';
        weightUnitStr = (netWeightCount/1000).toFixed(3);
    } else if (netWeightCount >= 1000000 && netWeightCount < 1000000000) {
        unitStr = ' 千克';
        weightUnitStr = (netWeightCount/1000000).toFixed(3);
    } else if (netWeightCount >= 1000000000) {
        unitStr = ' 吨';
        weightUnitStr = (netWeightCount/1000000000).toFixed(3);
    } else{
        unitStr = ' 毫克';
        weightUnitStr = netWeightCount.toFixed(3);
    }
    return toFixedNetweight(weightUnitStr) + unitStr;
}







