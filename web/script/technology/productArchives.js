var isGeneral = false;
var isSuper = false;
jQuery.fn.treeItemSlide = function (type) {
    return $(this).each(function(){
        var iconNode = $(this).children('i').eq(0);
        if (!iconNode.hasClass("ty-fa")) {
            if (type === 'up') {
                return iconNode.removeClass('fa-angle-down').addClass('fa-angle-right');
            } else if (type === 'down') {
                return iconNode.removeClass('fa-angle-right').addClass('fa-angle-down');
            } else if (type === 'toggle') {
                if (iconNode.hasClass("fa-angle-right")) {
                    return iconNode.eq(0).removeClass('fa-angle-right').addClass('fa-angle-down');
                } else {
                    return iconNode.eq(0).removeClass('fa-angle-down').addClass('fa-angle-right')
                }
            }
        }
    });
}
var perNUM = 20,    pType = 1,partList = [];
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#selectDeapar"));
bounce_Fixed3.show($("#tip1"));
bounce_Fixed2.cancel();
bounce_Fixed3.cancel();
let editType = 2;
$(function () {
    showMainCon(1);
    getProductionList(1, perNUM, "", "", "");
    // // creator:hxz 请选择加工单位
    // $(".ty-colFileTree").on("click", ".ty-treeItem", function () {
    //     //添加文件夹选中样式
    //     $(".ty-treeItem").removeClass("ty-treeItemActive");
    //     $(this).addClass("ty-treeItemActive");
    //     //点击文件夹箭头方向的切换
    //     if ($(this).find("i").eq(0).hasClass("fa-angle-right")) {
    //         $(this).find("i").eq(0).removeClass("fa-angle-right");
    //         $(this).find("i").eq(0).addClass("fa-angle-down");
    //         $(this).next().show();
    //     } else if ($(this).find("i").eq(0).hasClass("fa-angle-down")) {
    //         $(this).find("i").eq(0).removeClass("fa-angle-down");
    //         $(this).find("i").eq(0).addClass("fa-angle-right");
    //         $(this).next().hide();
    //     }
    // });
    $('body').on('click', '.ty-btn,.linkBtn', function () {
        var type = $(this).data('type');
        let info = ``;
        let _this = $(this);
        switch (type) {
            case 'backMain' :
                showMainCon(1);
                break;
            case 'directlyProducts':
                var lang = _this.data("lang");
                productIntrolCommon($("#seePartsDetail"));
                $("#seePartsDetail .combin tbody").children(":eq(0)").nextAll().remove();
                seePartsDetail(lang);
                break;
            case 'baseRecords':
                baseRecordList(1);
                break;
            case 'compositionRecords':
                baseRecordList(2);
                break;
            case 'baseRecordScan':
                let hub = _this.data("source");
                if (hub == 1) {
                    baseRecordScan(_this);
                } else if (hub == 2) {
                    compositionRecordScan(_this);
                }
                break;
            case 'editSource':
                var part = JSON.parse($("#productInfo").html());
                if ((part.process !== "1") && (handleNull(part.composition) == "") || handleNull(part.process) == "") {
                    layer.msg("请先在构成处理进行选择。");
                } else {
                    $(".assemblingTip").hide();
                    $("#editInsidePart").hide();
                    $("#editPartsSource select").val("");
                    $("#editPartsSource .creatInfo").html(part.createName + '&nbsp;&nbsp;' + new Date(part.createDate).format('yyyy-MM-dd hh:mm:ss'));
                    productIntrolCommon($("#editPartsSource"));
                    $("#edit_process").val(part.process);
                    $("#actual_process").val(part.process);
                    $("#edit_composition").val(part.composition);
                    $("#actual_composition").val(part.composition);
                    //设置option
                    $("#editPartsSource option").prop("disabled", false);
                    $("#edit_composition").prop("disabled", true);
                    $("#edit_process option").eq(part.process).prop("disabled", true);
                    if (part.process == 1) {
                        $("#edit_composition option").removeClass("grayOpt");
                        $("#edit_composition").prop("disabled", true);
                        $("#edit_composition").removeAttr("readonly onfocus onchange");
                    } else if (part.process == 2 || part.process == 3) {
                        $("#edit_composition").prop("disabled", false);
                        $("#edit_composition option:eq(0)").nextAll().addClass("grayOpt");
                        $("#edit_composition").attr({
                            readonly: "readonly",
                            onfocus: "this.defOpt=this.selectedIndex",
                            onchange: "this.selectedIndex=this.defOpt"
                        });
                        if (part.composition == 2) {
                            $("#editInsidePart").show();
                        }
                    }
                    setCompoAndProcess($("#edit_process"), $("#edit_composition"));
                    bounce_Fixed.show($("#editPartsSource"));
                }
                break;
            case 'editInsidePart':
                var part = JSON.parse($("#productInfo").html());
                bounce_Fixed.cancel();
                $("#splitGS .bounce_close").attr("onclick", "goBack(3)");
                $("#splitGS .bonceFoot span:eq(0)").attr("onclick", "goBack(3)");
                splitBtn(part, "");
                break;
            case 'allParts':
                allRelatedParts();
                break;
            case 'allOnceParts':
                productIntrolCommon($("#seeOncePartsGroup"));
                allRelatedPartsOnce();
                break;
            case 'viewSettings':
                viewSettings();
                break;
            case 'partEntry': // 录入直接组成该产品的零组件
                bounce_Fixed2.show($("#partEntry"));
                $("#addUnitBtn").show();
                $("#partEntry .textMax").html("0/100");
                partEntryBtn();
                $("#innerSn").attr("onclick", "startMatch(event)");
                $("#partEntry .bonceHead span").html("零组件录入");
                $("#partEntry .bonceFoot .bounce-ok").html("录入完毕");
                $("#partEntry [disabled]").removeAttr("disabled");
                getUnitList($("#unitId"), 4)
                break;
            case 'ptSuspendRecord':
                ptSuspendRecord();
                break;
            default:
                break;
        }
    });
    // 所有table中的查看
    $("table").on('click', 'span[type="btn"]', function () {
        var type = $(this).data('type');
        var item = JSON.parse($(this).siblings(".hd").html());

        if (type == "splitBtn2") {
            splitBtn(item, "2");
        } else if (type == "partsEdit") {
            bounce_Fixed2.show($("#partEntry"));
            $("#addUnitBtn").hide();
            $("#partEntry .bonceHead span").html("零组件修改");
            $("#partEntry .bonceFoot .bounce-ok").html("确定");
            partEntryBtn();
            // $("#innerSn").removeAttr("onclick");
            setGSInfo(item, 1);
        } else if (type == "delBtn") {
            var delTip = '';
            if (item.num > 1) {
                delTip = '<p>本件还参与构成了其他产品或组件。</p><p>确定后，本件将仅不参与本组件的构成！</p>';
            } else {
                delTip = '<p>确定后，本件将彻底删除！</p>';
            }
            $("#giveTip7 #msg").html(delTip);
            bounce_Fixed2.show($("#giveTip7"));
            editObj = $(this);
        } else if (type == "noSplit") {
            layer.msg("<p>本件在此不可编辑！</p><p>如需编辑，请到“零组件”模块中进行</p>");
        }
    });
    $("#edit_process").change(function () {
        $("#actual_process").val($(this).val());
        if ($(this).val() == 1) {
            $("#actual_composition").val("");
            $("#editInsidePart").hide();
        }
        setCompoAndProcess($("#edit_process"), $("#edit_composition"));
    });
    $("#edit_composition").change(function () {
        var part = JSON.parse($("#productInfo").html());
        if (part.process != 2 && part.process != 3) {
            $("#actual_composition").val($(this).val());
            setCompoAndProcess($("#edit_process"), $("#edit_composition"));
        }
    });
    $("#process").change(function () {
        setCompoAndProcess($("#process"), $("#composition"));
    })
    $("#composition").change(function () {
        setCompoAndProcess($("#process"), $("#composition"));
    })
    // 全部零组件查看设置 选择按钮
    $("#viewSettings .settingsCon").on('click', '.radioCon', function () {
        $(this).siblings().children("i").attr("class", "fa fa-circle-o");
        $(this).children("i").attr("class", "fa fa-dot-circle-o");
    });
    // 零部件录入 选中
    $("#selecGS").on('click', 'option', function () {
        setSelectOption($(this));
        $("#selecGS").hide();
        $(".bounce_Fixed2").stopTime("gsMatch")
    });
    // $(".ty-colFileTree").on("click", ".ty-treeItem", function () {
    //     //添加文件夹选中样式
    //     $(".ty-treeItem").removeClass("ty-treeItemActive");
    //     $(this).addClass("ty-treeItemActive");
    //     //点击文件夹箭头方向的切换
    //     if ($(this).find("i").eq(0).hasClass("fa-angle-right")) {
    //         $(this).find("i").eq(0).removeClass("fa-angle-right");
    //         $(this).find("i").eq(0).addClass("fa-angle-down");
    //         $(this).next().show();
    //     } else if ($(this).find("i").eq(0).hasClass("fa-angle-down")) {
    //         $(this).find("i").eq(0).removeClass("fa-angle-down");
    //         $(this).find("i").eq(0).addClass("fa-angle-right");
    //         $(this).next().hide();
    //     }
    // });
    getEditState();
    //文件夹列表排序部分
    $("#fileSort li").on("click", function () {
        if ($(this).hasClass('ty-active')) {
            // 如果点击的是已经点击过的，改为升序，获取升序列表
            $(this).find("i").toggleClass("fa-long-arrow-up");
        } else {
            // 如果点击的是未被点击的，获取降序列表
            $(this).find('.sort').show();
            $(this).siblings().find('.sort').hide();
            $(this).find("i").removeClass("fa-long-arrow-up");
        }
        $(this).addClass("ty-active").siblings().removeClass("ty-active");
        var categoryId = $("#chooseSaveFolder .ty-treeItemActive").data("id");
        var type = $("#fileSort li.ty-active").attr("type");
        // "1"代表获取的是按时间降序的列表，“2”代表获取的是时间升序的列表，“3”代表获取的是文件编号升序的列表，“4”代表的是获取文件编号降序的列表
        if (type !== '1') {
            if (!$(this).find("i").hasClass('fa-long-arrow-up')) {
                $("#fileSort").data('type', 1);
            } else {
                $("#fileSort").data('type', 2);
            }
        } else {
            if ($(this).find("i").hasClass('fa-long-arrow-up')) {
                $("#fileSort").data('type', 3);
            } else {
                $("#fileSort").data('type', 4);
            }
        }
        getFile(1, 20, categoryId);
    });
    // creator:hxz 请选择加工单位
    $(".ty-colFileTree").on("click", ".ty-treeItem", function () {
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //点击文件夹箭头方向的切换
        if ($(this).find("i").eq(0).hasClass("fa-angle-right")) {
            $(this).find("i").eq(0).removeClass("fa-angle-right");
            $(this).find("i").eq(0).addClass("fa-angle-down");
            $(this).next().show();
        } else if ($(this).find("i").eq(0).hasClass("fa-angle-down")) {
            $(this).find("i").eq(0).removeClass("fa-angle-down");
            $(this).find("i").eq(0).addClass("fa-angle-right");
            $(this).next().hide();
        }

        var treeItemThis = $(this);
        var categoryId = $(this).data("id");
        var avatarName = $(this).parents(".ty-colFileTree").data("name");
        var url = isGeneral || isSuper ? '../res/getFolderAndChildFolderManager.do' : '../res/getFolderAndChildFolder.do'
        // 处理逻辑
        treeItemThis.parents(".ty-colFileTree").find(".ty-treeItem").removeClass("ty-treeItemActive")   // 取消之前选中
        treeItemThis.addClass("ty-treeItemActive"); // 设置选中
        treeItemThis.treeItemSlide('toggle');        // icon切换（顶部jQuery方法拓展）
        $.ajax({
            url: url,
            data: {"categoryId": categoryId, "type": 1},
            success: function (res) {
                var data = res.data;
                if (!data) {
                    $("#mt_tip_ms").html("连接失败，请刷新重试！");
                    bounce.show($("#mtTip"));
                } else {
                    // 渲染返回的信息
                    if (treeItemThis.children().eq(0).hasClass('fa-angle-right')) {
                        treeItemThis.next().remove();
                    } else {
                        showTreeData(data, treeItemThis);
                    }
                    var type = 1; // 1-可以选择文件 0-只能选择文件夹
                    if (type === 1) {
                        $("#fileSort li").eq(0).addClass("ty-active").siblings().removeClass("ty-active");
                        $("#fileSort li").eq(0).find('.sort').show();
                        $("#fileSort li").eq(0).siblings().find('.sort').hide();
                        $("#fileSort li").eq(0).find("i").removeClass("fa-long-arrow-up");
                        var pageInfo = data.pageInfo,
                            fileInfo = data.list;
                        //设置分页信息
                        var currentPageNo = pageInfo["currentPageNo"],
                            totalPage = pageInfo["totalPage"],
                            jsonStr = JSON.stringify({"categoryId": categoryId});
                        //设置文件信息
                        var fileListStr = getFileListStr(fileInfo, true);
                        setPage($("#ye_con"), currentPageNo, totalPage, "fileMessage", jsonStr);
                        $(".ty-fileList").html(fileListStr);
                        $(".ty-fileList").attr("type", "folder");
                    }
                }
            }
        })
        //sy添加的部分--------
        $(".mainFileList .ty-fileItem .sizeote").hide();
        //-------------------------
    })
    $("#chooseSaveFolder").on("click", ".ty-fileItem [type='btn']", function () {
        var name = $(this).attr("name");
        if ($(this).attr("disable") === 'true') {
            return false;
        }
        switch (name) {
            case 'seeOnline': // 在线预览
                seeOnline($(this));
                break
            case 'download': // 下载
                getDownLoad($(this));
                break
        }
    });

    // creator: sy 2023-10-11   加载文件功能
    $("#addfilelook").on("click",".ty-fileItem [type='btn']",function(){
        var name = $(this).attr("name");
        if($(this).attr("disable") === 'true'){
            return false;
        }
        switch (name) {
            case 'seeOnline'://在线预览
                fileSeeOnline($(this));
                break;
            case 'download'://下载
                fileDownload($(this));
                break;
            case 'basicMsg'://基本信息
                basicMessage($(this));
                break;
            case 'changeVersionRecord'://换版记录
                chargeRecord($(this));
                break;
        }
    })

    //弹窗内按钮点击事件
    $(".bounce,.bounce_Fixed,.bounce_Fixed2").on("click", "[type='btn']", function () {
        var name = $(this).data("name");
        switch (name) {
            //存储数据
            case 'sureChooseFolder':
                //这里存储数据应该是可以存储在sureChooseFolderBtn的data属性里，然后在产品录入弹窗上展示数据个数的时候可以在点击‘确定’时再进行存储，
                //然后进行获取
                // var chooseFolderNode = $("#chooseSaveFolder .ty-treeItem.ty-treeItemActive");
                var chooseFileNode = $("#chooseSaveFolder .ty-fileItem .choseting");
                // var tryen = chooseFileNode.hasClass("choseting");//明明选择哪一条那一条才会有choseting，为何获取到的一直是同一条呢？
                // if(tryen){//含有choseting属性
                var json = chooseFileNode.next().next().next().next().html();
                // //json就是选中的文件数据
                //获取存储数组中此时的数据
                var insidchose = $(".addprodun").data("box");
                json = JSON.parse(json);
                insidchose.push(json);//这里一开始是两个
                $(".haveloonk").data("projson",insidchose);
                // var insidchose2 = $("#addPurover").data("addprot");
                // if(insidchose2 == undefined){
                //     insidchose2 = [];
                // }
                // insidchose2.push(json);//这里是三个，然后上面那个两个的也变成三个了
                $("#addPurover").data("addprot",insidchose);
                // $("#addPurover").data("addprot",insidchose2);
                $(".pronumb").html(insidchose.length);
                bounce_Fixed.cancel($("#chooseSaveFolder"));
                bounce.show($("#addProduct"));
                // }else{}
                break;
            case 'sureChooseFolder2':
                var chooseFileNode = $("#chooseSaveFolder .ty-fileItem .choseting");
                var json = chooseFileNode.next().next().next().next().html();//开心获取到值了
                var insidchoset2 = $(".addproct").data("box");
                json = JSON.parse(json);
                var procid = $("#sureChooseFolderBtn").data("proid");
                var version = json.version;
                switch (version) {
                    case "txt"://文件
                        var answer = 3;
                        break;
                    case 'doc'://文件
                        var answer = 3;
                        break;
                    case 'docx'://文件
                        var answer = 3;
                        break;
                    case 'xls'://文件
                        var answer = 3;
                        break;
                    case 'xlsx'://文件
                        var answer = 3;
                        break;
                    case 'ppt'://文件
                        var answer = 3;
                        break;
                    case 'jpg'://图片
                        var answer = 1;
                        break;
                    case 'jpeg'://图片
                        var answer = 1;
                        break;
                    case 'png'://图片
                        var answer = 1;
                        break;
                    case 'gif'://图片
                        var answer = 1;
                        break;
                    case 'mp4'://视频
                        var answer = 2;
                        break;
                }
                insidchoset2.push(json);
                $(".protest").html(insidchoset2.length);
                $(".haveloonk2").data("projson",insidchoset2);
                $.ajax({
                    url:"../product/saveOrUpdatePdResource.do",
                    data:{
                        //Integer product         //产品id
                        // Integer resource        //资源文件id
                        // String type             //类型:1-图片,2-视频,3-文档
                        // String title            //标题
                        product:procid,
                        resource:json.id,//…………json中没有resource这个字段怎么办呢？？？？？貌似没有什么影响诶
                        type:answer,
                        title:json.name
                    },
                    success:function(res) {
                        if (res.success == 1) {
                            bounce_Fixed.cancel($("#chooseSaveFolder"));
                            bounce.show($("#seeProduct"));
                            getprosucce(procid);
                        }
                    }
                })
                break;
        }
    })
    $("#picShow").on("click",'',function(){
        $("#picShow").fadeOut("fast");
    })
})
// creator: 李玉婷，2020-03-24 16:49:26，产品录入
function addProduction() {
    var str1 = `
            <tbody>
                    <tr>
                        <td>商品图号</td>
                        <td>商品名称/规格/型号</td>
                        <td>计量单位</td>
                        <td>所属客户</td>
                        <td>创建人</td>
                        <td>关联操作人</td>
                        <td>操作</td>
                    </tr>
            </tbody>
        `;
    $("#viewrecords #prosoiaion").html(str1);
    var str2 = `
         <tbody>
            <tr>
              <td>商品图号</td>
              <td>商品名称/规格/型号</td>
              <td style="width: 26%;">计量单位</td>
              <td>创建人</td>
              <td>关联操作人</td>
              <td>操作</td>
            </tr>
         </tbody>
    `
    $("#viewrecords #tytblen").html(str2);
    $("#viewrecords #pronumber").html(0);
    // $("#lookcatbook").data("generol",1);
    // $("#closewinden2").hide();
    $("#closewinden").show();
    $(".oveadd").hide();
    $(".oveadd2").hide();
    initCUpload($("#cpUplond-1"),'img');
    $(".send1").children("#file_upload_1-button").addClass("adden");
    initCUpload2($("#cpUplond-2"),'img');
    initCUpload5($("#cpUplond-5"),'img');
    $(".send5").children("#file_upload_1-button").addClass("adden");
    initCUpload6($("#cpUplond-6"),'img');
    $(".detent").hide();
    $(".unupent").show();
    $("#cpUplond-1").show();
    $(".upent").hide();
    $(".oveadd").hide();
    $(".unupent").addClass("offen");
    $("#cpUplond-1").addClass("pend");
    $(".detent2").hide();
    $(".unupent2").show();
    $("#cpUplond-5").show();
    $(".upent2").hide();
    $(".unupent2").addClass("offen");
    $("#cpUplond-5").addClass("pend");
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else{
        $(".customerList:gt(0)").remove();
        $(".zsGoodsList").val("");
        getAllCustomer();
        getTYProductList();//获取通用型商品
        getTicketAndPerson();
        $("#productionFrom input").val("");
        $("#productionFrom select").val("");
        $("#productionFrom .zsGoodsList").html("<option value='' onchange='getzslist($(this))'>请选择专属商品</option>");
        $("#addProduct .wordCount").html("0/100");
        bounce.show($('#addProduct'));
        getUnitList($("#unitSelect"), 3);
        getMonthnum();
        let stant = $("#association").data("keyed") || "";
        if(stant == 2 || stant == ""){
            $("#association").data("keyed",1);//代表是点击‘录入产品’后的情况
        }
        let generalbox = [];//用于存储选择的通用型商品数据
        $("#association").data("general",generalbox);
        // $("#lookcatbook").data("general",[]);
        let strk = `未`;
        $(".merchandise").html(strk);
        $(".exclsieoucts").html(0);
        var chosepro = [];//用于存储选择的文件数据
        $(".addprodun").data("box",chosepro);
        $(".pronumb").html(0);
        //判断一个div是不是展示的
        if($(".upent").is(':visible')){
            $(".oveadd").show();
        }else{
            $(".oveadd").hide();
        }
        $("#association").data("msgbox",[]);
        // $("#zsGoods").children().slice(1).remove();//将所选父级下除第一个子级外的其他子级都删除
        // var ksenlist = $("#addProductionBtn").data("addpeople");
        // $("#lookcatbook").data("addpeiple", ksenlist);
    }
}



// creator: 李玉婷，2020-03-25 14:04:30，产品录入完毕
function addProductionSure() {
    var param = {};
    var invilitNum = 0;
    $("#productionFrom [require]").each(function(){
        var val = $(this).val();
        if(val == '' || val == undefined){
            invilitNum++;
        }
    });
    if (invilitNum>0){
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }else{
        let zsGoods  = [];
        $("#productionFrom [need]").each(function(){
            var name = $(this).attr('name');
            var key = $(this).val();
            param[name] = key;//param存储的是弹窗中填写的产品相关数据
        });
        // $("#association").data("prolist",param);
        // var listchose = $("#entrover").data("middlist");
        var listchose = $("#addPurover").data("chosen");
        if(listchose == undefined){
            // param.tYProductId = "";
            // param.zsProductList = "";
        }else{
            if(listchose.zsProductList == []){
                // param.zsProductList = "";
            }else{
                param.zsProductList = listchose.zsProductList;
            }
            if(listchose.tYProductId == ""){
                // param.tYProductId = "";
            }else{
                param.tYProductId = listchose.tYProductId;
                // param.zsProductList = listchose.zsProductList;
            }
        }
        // let cmId = $("#cmGoods").val();
        // if (cmId && cmId !== "") {
        //     param.tYProductId = $("#cmGoods").val();//获取选择的通用型商品对应的id
        // }
        // $(".zsCase").each(function (){
        //     let zsId = $(this).find(".zsGoodsList").val();
        //     if (zsId && zsId !== "") {
        //         let index = zsGoods.findIndex(value => Number(value.productId) === Number(zsId))
        //         if (index === -1) zsGoods.push({"productId":zsId});
        //     }
        // });
        // param.zsProductList = JSON.stringify(zsGoods);
        //现在相当于param里面的数据还差新增的文件和两个图纸的数据
        var produlink = $("#addPurover").data("addprot");
        if(produlink == undefined){
            // param.resourceList = "";
        }else{
            var newprolink = [];
            $.each(produlink,function(i,el){
                if($.inArray(el,newprolink) === -1) newprolink.push(el);
            })
            var probox = [];
            newprolink.forEach(itempro => {
                var jsonpro = {
                    title:"",
                    type:"",
                    resource:""
                };
                jsonpro.title = itempro.name;
                var typekee = itempro.version;//判断新增的文件数据中哪个是图片哪个是文件
                switch (typekee) {
                    case 'jpg'://图片
                        jsonpro.type = 1;
                        break;
                    case 'mp4'://视频
                        jsonpro.type = 2;
                        break;
                    case 'txt'://文件
                        jsonpro.type = 3;
                        break;
                }
                jsonpro.resource = itempro.id;
                probox.push(jsonpro);
            })
            probox = JSON.stringify(probox);
            param.resourceListString = probox;
        }
        //下面是将产品图纸相关数据加在param中
        var propicre = $("#addProduct").data("img");
        if(propicre == undefined){
            // param.cptzImage = "";
        }else{
            propicre.forEach(itempic => {
                var projson = {
                    title:"",
                    type:"",
                    uplaodPath:""
                };
                projson.title = itempic.displayName;
                var typename = itempic.lastName;//用于判断在本地上传的是图片、视频还是文件
                switch (typename) {
                    case 'jpg'://图片
                        projson.type = 1;
                        break;
                    case 'mp4'://视频
                        projson.type = 2;
                        break;
                    case 'txt'://文件
                        projson.type = 3;
                        break;
                }
                projson.uplaodPath = itempic.filename;
                projson = JSON.stringify(projson);
                param.cptzImageString = projson;
                // propicbox.push(projson);
            })
        }
        //propicre是个数组
        // var propicbox = [];
        //下一步是将控制点序号数据也加上
        var propont = $("#addProduct").data("img2");
        if(propont == undefined){
            // param.xhImage = "";
        }else{
            propont.forEach(itempon => {
                var projson = {
                    title:"",
                    type:"",
                    uplaodPath:""
                };
                projson.title = itempon.displayName;
                var typename = itempon.lastName;
                switch (typename) {
                    case 'jpg'://图片
                        projson.type = 1;
                        break;
                    case 'png'://图片
                        projson.type = 1;
                        break;
                    case 'jpeg'://图片
                        projson.type = 1;
                        break;
                    case 'gif'://图片
                        projson.type = 1;
                        break;
                    case 'mp4'://视频
                        projson.type = 2;
                        break;
                    case 'txt'://文件
                        projson.type = 3;
                        break;
                }
                projson.uplaodPath = itempon.filename;
                projson = JSON.stringify(projson);
                param.xhImageString = projson;
            })
        }
        //propont是个数组
        // var propontbox = [];
        // param = JSON.stringify(param);
        $.ajax({
            "url": "../product/addPdBase.do",
            "data": param,
            success: function (res) {
                if (res.code == 200){
                    bounce.cancel();
                    var jsonObj = JSON.parse($("#ye").find(".json").html());
                    $("#addPurover").data("addprot",[]);
                    getPdBaseListData(jsonObj);
                }else{
                    var msg = res.msg;
                    $("#unfilledTip_ms").html(msg);
                    bounce_Fixed2.show($("#unfilledTip"));
                }
            }
        });
    }
}

// creator: sy 2023-09-22   点击‘查看’展示产品图纸
function propiclook(checked,ask){
    switch (checked) {
        case 1://代表是产品图纸
            switch (ask) {
                case 'has'://代表该产品已经设置了产品图纸
                    var getpropet = $(".propiclook").data("getproper");
                    var getsrc = getpropet.uplaodPath;//图片的地址
                    if(getsrc == undefined){
                        getpropet.forEach(item => {
                            getsrc = item.filename;
                        })
                    }
                    $("#picShow img").attr('src',$.fileUrl + getsrc);
                    $("#picShow").fadeIn("fast");//图片淡入淡出效果
                    break;
                default://代表该产品未设置产品图纸
                    var getpropet = $(".propiclook").data("img");
                    getpropet.forEach(item => {
                        var getstr2 = item.filename;
                        $("#picShow img").attr('src',$.fileUrl + getstr2);
                        $("#picShow").fadeIn("fast");//图片淡入淡出效果
                    })
                    break;
            }
            break;
        case 2://代表是控制点顺序号图片
            switch (ask) {
                case 'has'://代表该产品设置了控制点顺序号图片
                    var imgbox =   $(".propiclook2").data("getponpro");
                    var imgsrc = imgbox.uplaodPath;
                    if(imgsrc == undefined){
                        imgsrc = imgbox.filename;
                    }
                    $("#picShow img").attr('src',$.fileUrl + imgsrc);
                    $("#picShow").fadeIn("fast");//图片淡入淡出效果
                    break;
                default://代表该产品未设置控制点顺序号图片
                    var imgbox =  $(".propiclook2").data("imgbox");
                    imgbox.forEach(item2 => {
                        var imgsrc = item2.filename;
                        $("#picShow img").attr('src',$.fileUrl + imgsrc);
                        $("#picShow").fadeIn("fast");//图片淡入淡出效果
                    })
                    break;
            }
            break;
    }
}

// create :hxz 2021-1-11  显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    $(".mainCon" + num).show().siblings().hide();
}
// creator: 李玉婷，2020-03-25 14:38:20，获取产品列表
function getProductionList(currPage,pageSize,category,categoryName,keyword) {
    pType = 1;
    $("#ye").html("");
    $("#suspendPdNum").html("0");
    $("#addProductionBtn").show();
    $("#productionList tbody").html("");
    $("#kindsTree li").remove();
    $(".inProduct").show().siblings().hide();
    $(".mainCon1 input").val("");
    $(".mainCon1 select").val("");
    if (categoryName == "" || categoryName == "全部"){
        $(".suspendBtn").show();
        $(".left-bottom").hide();
        $("#firstLevelName").html("全部").data("categoryid","");
    } else{
        $(".suspendBtn").hide();
        $(".left-bottom").show();
        $("#firstLevelName").html(categoryName).data("categoryid",category);
    }
    let prams = {
        "pageSize":pageSize,
        "currPage":currPage,
        "category": category,
        "categoryName": categoryName,
        "param": keyword
    }
    getPdBaseListData(prams);
}
// creator: 李玉婷，2021-11-02 08:30:28，产品搜索、来源、构成
function getPdBaseListData(prams) {
    $.ajax({
        "url": '../product/getPdBaseList.do',
        "data": prams,
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var count = 0;
            var list = res.data;
            var categoryList = res.mtCategories;
            var str = '';
            var jsonStr = prams ;
            jsonStr = JSON.stringify(jsonStr) ;

            if (list && list.length>0){
                //sy添加的部分------
                // $("#addProductionBtn").data("addpeople",list);
                //-----------------------
                for(var i=0;i<list.length;i++){
                    str +=
                        '<tr data-id="'+ list[i].id +'">' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name + ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+ ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+'</td>' +
                        '    <td>'+ list[i].unit +'</td>' +
                        '    <td>'+ netWeightLimit(list[i].netWeight,list[i]['weightUnit']) +'</td>' +
                        '    <td class="createInfo">'+ list[i].createName + '&nbsp;&nbsp;' + new Date(list[i].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue tb-btn-sm" onclick="seeProduct($(this), 1)">查看</span>' +
                        //'        <span class="ty-color-blue tb-btn-sm">拆分</span>' +
                        '        <span class="ty-color-blue tb-btn-sm" onclick="suspendPd($(this),0)">停止生产</span>' +
                        '        <span class="ty-color-red tb-btn-sm" onclick="deleteProductJudge($(this),0)">删除</span>' +
                        '    </td>' +
                        '</tr>';
                }
            }
            if (pType == 1) {
                $("#xRow").html(res.countNum);
                setPage( $("#ye") , curr ,  totalPage , "InProduct", jsonStr );
                if(prams.categoryName == '全部' ||prams.categoryName == ''){
                    $("#suspendPdNum").html(res.suspendNum);
                }
                if (categoryList && categoryList.length > 0){
                    var menu = '';
                    for(var m=0;m<categoryList.length;m++){
                        count += categoryList[m].num;
                        if (prams.categoryName != '待分类'){
                            menu +=
                                '<li class="faceul1">' +
                                '    <a>' +
                                '        <span onclick="kindBtn($(this))">'+ categoryList[m].value +'（'+ categoryList[m].num +'种）</span>' +
                                '        <div class="hd">' +
                                '            <span class="kindId">'+ JSON.stringify(categoryList[m])+'</span>' +
                                '        </div>' +
                                '    </a>' +
                                '</li>';
                        }
                    }
                    $("#kindsTree").html(menu);
                }
                $("#firstLevelAmount").html(count);
                $("#productionList tbody").html(str);
            } else if (pType == 2) {
                $("#ssNum").html(res.countNum);
                $(".con3Ttl").html("创建人");
                $("#productionListQ tbody").html(str);
                setPage( $("#ye2") , curr ,  totalPage , "InProduct", jsonStr );
            } else if (pType == 3 || pType == 4) {
                $("#xRow").html(res.countNum);
                $("#productionList tbody").html(str);
                setPage( $("#ye") , curr ,  totalPage , "InProduct", jsonStr );
            }
        }
    });
}
// creator: 李玉婷，2020-03-27 17:42:06，分类
function kindBtn(obj) {
    var json = obj.siblings("div").children(".kindId").html();
    json = JSON.parse(json);
    var keyword = $("#searchKeyBase").val();
    var strson = '<span onclick="showkindNav($(this))" data-id="'+ json.id +'" data-name="'+ json.value +'"> >'+ json.value +'</span>';
    $("#curID").append(strson);
    getProductionList(1,20,json.id,json.value,keyword);
}
// creator: 李玉婷，2020-04-03 08:42:37，停止生产的产品
function suspendPdSet(obj){
    pType = 1;
    $(".mainCon1 input").val("");
    $(".mainCon1 select").val("");
    $(".inSuspend").show().siblings().hide();
    $(".suspendBtn").hide();
    $(".left-bottom").show();
    $("#kindsTree").html('');
    $("#addProductionBtn").hide();
    $("#firstLevelAmount").html('0');
    $("#firstLevelName").html(obj.html());
    var strson = '<span> >'+ obj.html() +'</span>';
    $("#curID").append(strson);
    let susPram = {
        "pageSize":perNUM,
        "currPage":1,
        "param": ""
    }
    getSuspendPdList(susPram);
}
// creator: 李玉婷，2020-03-31 11:21:01，获取暂停销售的商品数据
function getSuspendPdList(pram) {
    $.ajax({
        "url": "../product/suspendPdBaseList.do",
        "data": pram,
        success: function (res) {
            //设置分页
            var html = '';
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = pram;
            jsonStr = JSON.stringify(jsonStr) ;

            var spList = res.data;
            if (spList && spList.length > 0){
                for(var t=0;t<spList.length;t++){
                    html+=
                        ' <tr data-id="'+ spList[t].id +'">' +
                        '     <td>'+ spList[t].innerSn +'</td>' +
                        '     <td>'+ spList[t].name + ((handleNull(spList[t].model) === "")?'':'、'+ spList[t].model)+ ((handleNull(spList[t].specifications) === "")?'':'、'+spList[t].specifications)+'</td>' +
                        '     <td>'+ spList[t].unit +'</td>' +
                        '    <td>'+ netWeightLimit(spList[t].netWeight,spList[t].weightUnit) +'</td>' +
                        '     <td class="createInfo">'+ spList[t].updateName + ' &nbsp; ' + new Date(spList[t].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '     <td>' +
                        '        <span class="ty-color-blue tb-btn-sm" onclick="seeProduct($(this),2)">查看</span>' +
                        '        <span class="ty-color-blue tb-btn-sm" onclick="suspendPd($(this),1)">恢复生产</span>' +
                        '        <span class="ty-color-red tb-btn-sm" onclick="deleteProductJudge($(this),1)">删除</span>' +
                        '     </td>' +
                        ' </tr>';
                }
            }
            if (pType == 1) {
                setPage( $("#ye") , curr ,  totalPage , "InSuspendPd", jsonStr );
                $("#xRow").html(res.suspendNum);
                $("#firstLevelAmount").html(res.suspendNum);
                $("#suspendList tbody").html(html);
            } else if  (pType == 2) {
                $("#ssNum").html(res.suspendNum);
                $(".con3Ttl").html("停止生产的操作");
                $("#productionListQ tbody").html(html);
                setPage( $("#ye2") , curr ,  totalPage , "InSuspendPd", jsonStr );
            } else if (pType == 3 || pType == 4) {
                $("#xRow").html(res.suspendNum);
                $("#suspendList tbody").html(html);
            }
        }
    });
}
// creator: 李玉婷，2020-03-25 16:35:15，查看产品信息
function seeProduct(obj,bool){
    initCUpload3($("#cpUplond-3"),'img');
    initCUpload4($("#cpUplond-4"),'img');
    initCUpload7($("#cpUplond-7"),'img');
    initCUpload8($("#cpUplond-8"),'img');
    // var pdId = obj.parents("tr").data("id");
    //---------------sy添加的部分------------------
    var chosk = $.isNumeric(obj);
    if(chosk == true){
        var pdId = obj;
    }else{
        var pdId = obj.parents("tr").data("id");
    }
    // --------------------------------------------------
    $('#seeProduct').data("id", pdId);
    //---------------sy添加的部分------------------
    $("#cpUplond-3").data("proid",pdId);
    $("#cpUplond-4").data("proid",pdId);
    $("#cpUplond-7").data("proid",pdId);
    $("#cpUplond-8").data("proid",pdId);
    $("#retuem4").data("proid",pdId);
    $("#retuem3").data("proid",pdId);
    $(".swsc1").show();
    $(".yyc1").hide();
    $(".swsc2").show();
    $(".yyc2").hide();
    $("#catlistollk0").data("proid",pdId);
    $("#catlistollk1").data("proid",pdId);
    $("#catlistollk2").data("proid",pdId);
    $(".addproct").data("box",[]);//用来存储选择的文件
    $(".haveloonk2").data("proid",pdId);
    $(".addproct").data("proid",pdId);
    //--------------------------------------
    $(".partsScan2").show();
    if (bool == 1 || bool == 2) {
        $(".middleScan").hide();
        $("#seeProduct").data("id", pdId);
        $("#seeProduct").data("source", bool);
        partList = [pdId];
        bool == 1? $("#stopDetails").hide():$("#stopDetails").show();
    } else if (bool == 4) {
        partList.push(pdId);
        $(".middleScan").show();
        $(".partsScan2").hide();
        $("#stopDetails").hide();
    }
    getPdDetails(pdId);
    //pdId是产品id
    getprosucce(pdId);
}
// creator: 李玉婷，2020-03-25 16:51:45，获取查看产品信息数据
function getPdDetails(productId){
    $('#seeProduct').data('id',productId);
    $("#seeCreater").html('');
    $.ajax({
        url: "../parts/getPartsDetail.do",
        data: {
            id: productId
        },
        success: function (data) {
            var info = data['data']['part'], detail='';
            //sy 添加----------------
            $("#catlistollk0").data("catl",info);
            $("#catlistollk1").data("catl",info);
            $("#catlistollk2").data("catl",info);
            //-------------------------
            var assemble = data['data']['pdAssemble'];
            var creater = info.createName + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss');
            var updateDate = new Date(info.updateDate).format('yyyy-MM-dd hh:mm:ss');
            $('#seeProduct').data("orderNum",info.orderNum)
            $('#stopName').html(info.updateName);
            $('#stopDate').html(updateDate);
            $('#productInfo').html(JSON.stringify(info));
            $("#seeCreater").html(creater);
            productIntrolCommon($("#seeProductInit"));
            //中部查看
            if (assemble) {
                $("#seeProduct .several").each(function() {
                    var name = $(this).data("name");
                    $(this).html((assemble[name] || '0'));
                })
            }
            if ((info.process !== '1') && (handleNull(info.composition) =="") || handleNull(info.process) ==""){
                $(".afterConstitute").hide();
                $("#updateLast").html(creater);
            } else {
                $(".afterConstitute").show();
                $("#updateLast").html(handleNull(info.updateName) + ' ' + (new Date(info['updateDate']).format("yyyy-MM-dd hh:mm:ss")));
                if (info.process == '1') {
                    detail = '本件委托外部加工';
                    $(".only").hide();
                    loading.close() ;
                } else {
                    $(".only").show();
                    if (info.process == '2') {
                        detail = '本件为自制,';
                    } else if (info.process == '3') {
                        detail = '本件本公司能自制，但有时外包,';
                    }
                    if (info.composition == '2'){
                        $(".matInfo3").show().siblings().hide();
                        detail += '为装配件';
                        getviewSettings();
                        getDirectlyPart(productId)
                    }else if (info.composition == '3'){
                        $(".matInfo2").show().siblings().hide();
                        detail += '材料需由多种原料先混合';
                        getPartFormulaList(productId);
                    }else if (info.composition == '4'){
                        $(".matInfo1").show().siblings().hide();
                        detail += '由所购买的单一材料直接加工而成';
                        getPartMaterialList(productId);
                    }
                }
            }

            $("#compositionDetails").html(detail);
            bounce.show($('#seeProduct'));
        }
    })
}
// creator: 李玉婷，2020-03-26 09:54:06，修改产品信息数据获取
function updateProduction(){
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else{
        var memo = $("#seeProductInit [need][data-name='memo']").html();
        var orderNum = $('#seeProduct').data("orderNum");
        let info = JSON.parse($('#productInfo').html());
        if (editType === 1 && info.addType !== null) {
            layer.msg("对不起，您无权进行此项修改！");
            return false;
        }
        $('#orderUnNum').html(orderNum);
        $("#updateMemo").html(memo.length + '/100');
        getTicketAndPerson();
        $("#updateProduction [need]").each(function(){
            var nameData = $(this).attr('name');
            var html = info[nameData];
            $(this).val(html);
        });
        bounce_Fixed.show($('#updateProduct'));
        getUnitList($("#unitSelect2"), 3 , info.unitId);
        if (info.composition == 2) {
            $("#update_netWeight").prop("disabled", true);
            $("#update_weightUnit").addClass("selectDis");
        } else {
            $("#update_netWeight").prop("disabled", false);
            $("#update_weightUnit").removeClass("selectDis");
        }
    }
}
function updateProductSure(){
    var emptyNum = 0;
    $("#updateProduction [require]").each(function(){
        if($(this).val() == ""){ emptyNum++; }
    });
    if (emptyNum > 0){
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }else{
        var id = $('#seeProduct').data('id');
        var params = {
            id: id
        };
        $("#updateProduction [need]").each(function(){
            var nameData = $(this).attr('name');
            var html = $(this).val();
            params[nameData] = html;
        });
        $.ajax({
            "url": "../parts/updatePartsBase.do",
            "data": params,
            success: function (res) {
                if (res.success == 1) {
                    let jsonObj = {}, door = false;
                    bounce_Fixed.cancel();
                    if (pType == 1) {
                        jsonObj = JSON.parse($("#ye").find(".json").html());
                        if ($(".inSuspend").is(":visible")) {door = true;}
                    } else {
                        jsonObj = JSON.parse($("#ye2").find(".json").html());
                        if ($("#productionListQ").data("type") == 3) {door = true;}
                    }
                    getPdDetails(id);
                    if (door) {
                        getSuspendPdList(jsonObj);
                    } else {
                        getPdBaseListData(jsonObj);
                    }
                }else{
                    var msg = res.error.message;
                    $("#unfilledTip_ms").html(msg);
                    bounce_Fixed2.show($("#unfilledTip"));
                }
            }
        });
    }
}
// creator: 李玉婷，2020-03-26 10:51:36，删除产品
var gblObj = null;
function deleteProductJudge(obj, state) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else {
        gblObj = obj;
        var productId = obj.parents("tr").data("id");
        $('#deleteProduct').data('id', productId);
        $('#deleteProduct').data('state', state);
        bounce.show($('#deleteProduct'));
        $.ajax({
            "url": "../product/deletePdBaseJudge.do",
            "data": {
                'pdBaseId': productId
            },
            success: function (res) {
                if (res.code == '1'){
                    $("#deleteTip").html('<p class="ty-center">确定删除此产品？</p>');
                    $(".deleteCan").show();
                    $(".delRefuse").hide();
                } else{
                    $("#deleteTip").html('<p class="ty-indent">已有商品与此产品关联，或此产品已被拆分，所以此产品不可被删除！</p>');
                    $(".deleteCan").hide();
                    $(".delRefuse").show();
                }
            }
        });
    }
}
// creator: 李玉婷，2020-03-19 11:38:49，删除商品确定
function deleteProductSure(){
    var id = $('#deleteProduct').data('id');
    var state = $('#deleteProduct').data('state');
    $.ajax({
        "url": "../product/deletePdBase.do",
        "data": {
            'pdBaseId': id
        },
        success: function (res) {
            if (res.code == 200){
                bounce.cancel();
                let jsonObj = {}
                if ($(".mainCon1").is(":visible")) {
                    jsonObj = JSON.parse($("#ye").find(".json").html());
                } else {
                    gblObj.parents("tr").remove();
                    return false;
                }
                if (state == 1){
                    let susPram = {
                        "pageSize":perNUM,
                        "currPage":1,
                        "param": jsonObj.param
                    }
                    getSuspendPdList(susPram);
                }else{
                    getPdBaseListData(jsonObj)
                }
            }
        }
    });
}
// creator: 李玉婷，2020-03-19 09:28:36，暂停或恢复商品销售,1恢复 0暂停
function suspendPd(obj, state) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else{
        var id = obj.parents("tr").data("id");
        var tips = '';
        var data = {
            'pdBaseId': id,
            'state': state
        };
        var param = JSON.stringify(data);
        gblObj = obj;
        $("#pauseProduct").data('data', param);
        $("#pauseProduct").data('state', state);
        $(".pauseRefuse").html('取消');
        $(".pauseCan").show();
        if (state == 1){
            tips =
                '<p>操作成功后，还请及时将其与商品进行关联！</p>'+
                '<p>确定“恢复生产”此产品？</p>';
            $("#pauseProductTip").html(tips);
            bounce.show($("#pauseProduct"));
        }else{
            $.ajax({
                "url": "../product/suspensionOfPdBaseJudge.do",
                "data": data,
                success: function (res) {
                    if (res.code == '1'){ //能停
                        tips = '确定对此产品“停止生产”？';
                    }else {
                        $(".pauseRefuse").html('我知道了');
                        $(".pauseCan").hide();
                        tips =
                            '已有商品与此产品关联，所以此产品不可被“停止生产”！';
                    }
                    $("#pauseProductTip").html(tips);
                    bounce.show($("#pauseProduct"));
                }
            });
        }
    }
}
// creator: 李玉婷，2020-03-19 10:31:42，暂停或恢复商品销售确定
function suspendSaleSure() {
    var json = JSON.parse($("#pauseProduct").data('data'));
    var state = $("#pauseProduct").data('state');
    $.ajax({
        "url": "../product/suspensionOfPdBase.do",
        "data": json,
        success: function (res) {
            if (res.code == 200){
                bounce.cancel();
                var jsonObj = {};
                if ($(".mainCon1").is(":visible")) {
                    jsonObj = JSON.parse($("#ye").find(".json").html());
                } else {
                    gblObj.parents("tr").remove();
                    return false;
                }
                if (state == 1){
                    let susPram = {
                        "pageSize":perNUM,
                        "currPage":1,
                        "param": jsonObj.param
                    }
                    getSuspendPdList(susPram);
                }else{
                    getPdBaseListData(jsonObj)
                }
            }
        }
    });
}
// creator: 李玉婷，2020-03-25 11:25:05，获取部门 和 员工、商品和客户 列表
var selectObj = null; // 全部部门
function getTicketAndPerson() {
    $.ajax({
        "url": "../sale/getDetailsByInnerSn.do",
        "data": {"innerSn": ""},
        success: function (res) {
            selectObj = {};
            selectObj["orgMap"] = res["orgMap"];
            //selectObj["userMap"] = res["userMap"];
            //selectObj["baseMap"] = res["baseMap"];
            //selectObj["customerMap"] = res["customerMap"];
        }
    });
}
// creator: 李玉婷，2020-03-25 11:59:04，选择部门
function selectDepart(str) {
    bounce_Fixed2.show($("#selectDeapar"));
    $("#selectDeapar").data("obj",str);
    var list = selectObj["orgMap"];
    var departmentStr = '';
    departmentStr += '<ul class="level1" level="1">' + getNextLevelList(list) + '</ul>';
    $("#selectDeapar .ty-colFileTree").html(departmentStr);
}
function getNextLevelList(list) {
    var departmentStr = '';
    for (var i = 0; i < list.length; i++) {
        var deparId = list[i]["id"];
        if (deparId === 0) {
            // 选择负责单位时， 不能选 “其他”部门
        } else {
            var subList = list[i].subList;
            var userList = list[i].userList;
            if (subList.length === 0 && userList.length === 0) {
                departmentStr += '<li>' +
                    '<div class="ty-treeItem" id=' + list[i]["id"] + '>' +
                    '<i class="ty-fa"></i>' +
                    '<i class="fa fa-folder"></i>' +
                    '<span>' + list[i]["name"] + '</span>' +
                    '</div>';
            } else if (subList.length > 0 || userList.length > 0) {
                departmentStr += '<li>' +
                    '<div class="ty-treeItem" id=' + list[i]["id"] + '>' +
                    '<i class="fa fa-angle-right"></i>' +
                    '<i class="fa fa-folder"></i>' +
                    '<span>' + list[i]["name"] + '</span>' +
                    '</div>' +
                    '<ul>';
            }
            if (subList.length > 0) {
                departmentStr += getNextLevelList(subList);
            }
            if (userList.length > 0) {
                for (var j in userList) {
                    departmentStr += '<li>' +
                        '<div class="ty-treeItem" id=' + userList[j]["userID"] + '>' +
                        '<i class="ty-fa"></i>' +
                        '<i class="fa fa-file"></i>' +
                        '<span>' + userList[j]["userName"] + '</span>' +
                        '</div>' +
                        '</li>';
                }
            }
            if (subList.length > 0 || userList.length > 0) {
                departmentStr += '</ul></li>';
            } else {
                departmentStr += '</li>';
            }
        }
    }
    return departmentStr;
}
function selectDepartOk() {
    var str =     $("#selectDeapar").data("obj");
    var deparId = $("#selectDeapar .ty-treeItemActive").attr("id");
    var deparName = $("#selectDeapar .ty-treeItemActive > span").html();
    $("#"+ str +"Org").val(deparName);
    $("#"+ str +"OrgID").val(deparId);
    bounce_Fixed2.cancel();
}

// creator: 李玉婷，2020-03-27 14:28:26，搜索
function searchKeyBase(num, obj) {
    pType = num;
    var categoryName = $("#firstLevelName").html();
    var category = '';
    if (categoryName != '全部') {
        category = $("#firstLevelName").data("categoryid");
    }
    let prams = {
        "pageSize":perNUM,
        "currPage":1,
        "param": '',
        "process": '',
        "composition": ''
    }
    if (num == 2) {
        var keyword = $("#searchKeyBase").val();
        prams.param = keyword;
    } else if (num == 3) {
        prams.process = obj.val();
    } else if (num == 4) {
        prams.composition = obj.val();
    }
    if ($(".inProduct").is(":visible")){
        prams.category = category;
        prams.categoryName = categoryName;
        getPdBaseListData(prams);
    } else if ($(".inSuspend").is(":visible")){
        getSuspendPdList(prams);
    }
    if (num == 2) {
        showMainCon(2);
    }
}
// creator: 李玉婷，2020-04-03 13:46:22，当前分类
function showkindNav(obj){
    pType = 1;
    var id = obj.data('id');
    var name = obj.data('name');
    obj.nextAll().remove();
    if (id == "" || $(".inProduct:visible").length > 0){
        getProductionList(1,perNUM,id,name,"");
    } else if ($(".inSuspend:visible").length > 0){
        let susPram = {
            "pageSize":perNUM,
            "currPage":1,
            "param": ""
        }
        getSuspendPdList(susPram);
    }
}
//  返回上一级
function gobackLstLevel(type){
    if (type == 1){
        var  n = $("#curID").children().length-1 ;
        var kindId = "",kindName = "";
        var m = n - 1 ;
        if( m >= 0 ){
            $("#curID").children(":eq("+ n +")").remove() ;
            kindId = $("#curID").children(":eq("+ m +")").data("id");
            kindName = $("#curID").children(":eq("+ m +")").data("name");
            getProductionList(1,perNUM,kindId,kindName,"" );
        }
    }else{
        $("#curID").children(":eq(0)").nextAll().remove() ;
        getProductionList(1,perNUM,"","","" );
    }
}

// creator: 李玉婷，2020-04-03 10:28:28，描述、说明字数限制
function limitWord(obj) {
    var val = obj.val();
    var length = val.length;
    if (length <= 100){
        obj.parents("table").siblings("p.wordCount").html(length + '/100');
    }else{
        var str = val.slice(0,100);
        obj.val(str);
        obj.parents("table").siblings("p.wordCount").html(str.length + '/100');
    }
}


// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module , selectedId) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    if(selectedId == item['id']){
                        str += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';

                    }
                }
            }
            obj.html(str)

        }
    })
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit(obj) {
    bounce_Fixed3.show($("#addUnit"));
    $("#addUnit input").val("");
    $("#addUnit").data("obj", obj);
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    var obj = $("#addUnit").data("obj");
    obj = obj.parents("tr").next().find("select[name='unitId']");
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed3.cancel();
                getUnitList(obj, module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed3.show($("#tip1"));
            }
        }
    })
}
//creator: hxz，2020-09-02 14:51:41 录入- 计量单位设置
function unitAssign(obj) {
    var unitName = obj.children("option:selected").html();
    obj.siblings("input").val(unitName)
}

// creator: 李玉婷，2022-01-14 15:48:23，获取材料
function getPartMaterialList(id){
    $.ajax({
        "url":"../parts/getMaterialList.do",
        "data":{
            "id": id
        },
        success:function(res){
            var list = res['data'];
            var str = '';
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    str +=
                        '<tr>' +
                        '    <td>' + list[i].code + '</td>' +
                        '    <td>' + list[i].name+ ((handleNull(list[i].specifications) === "") ? '' : '、' + list[i].specifications) + ((handleNull(list[i].model) === "") ? '' : '、' + list[i].model)  + '</td>' +
                        '    <td>' + list[i].unit + '</td>' +
                        '</tr>';
                }
            }
            $(".matInfo1 table tbody").html(str);
        }
    })
}
// creator: 李玉婷，2022-01-14 16:28:23，获取配方
function getPartFormulaList(id) {
    $.ajax({
        "url":"../parts/getFormulaList.do",
        "data":{
            "id": id
        },
        success:function(res){
            var list = res['data'];
            var str = '';
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    str +=
                        '<tr>' +
                        '    <td>' + list[i].code + '</td>' +
                        '    <td>' + list[i].name + '/' + list[i].materialName + '</td>' +
                        '    <td>' + list[i].majorIngredient + '</td>' +
                        '    <td>' + list[i].minorIngredient + '</td>' +
                        '</tr>';
                }
            }
            $(".matInfo2 table tbody").html(str);
        }
    })
}
// creator: 李玉婷，2022-01-24 18:24:46，获取当前状态下本件直接包含的零组件
function getDirectlyPart(id) {
    $.ajax({
        "url": '../parts/getAllPartsByParts.do',
        "data": {
            "id": id,
            "level": "1"
        },
        success: function (res) {
            var list = res.data;
            var html = '';
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    html +=
                        '<tr data-id="'+ list[i].id +'">' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name + ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+ ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+'</td>' +
                        '    <td>'+ list[i].unit +'</td>' +
                        '    <td>'+ handleNull(list[i].amount) +'</td>' +
                        '    <td>'+ charge(list[i].process,"process") +'</td>' +
                        '    <td>'+ charge(list[i].composition,"composition") +'</td>' +
                        '    <td>'+ netWeightLimit(list[i].netWeight,list[i]['weightUnit']) +'</td>' +
                        '    <td class="createInfo">' + list[i].gcName + '&nbsp;&nbsp;' + new Date(list[i].date).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue" onclick="seeProduct($(this), 4)">查看</span>' +
                        '        <span class="hd">'+ JSON.stringify(list[i])+'</span>' +
                        '    </td>' +
                        '</tr>';
                }
            }
            $(".matInfo3").find("tbody").html(html);
        }
    })
}
// creator: 李玉婷，2021-12-06 13:02:24，中部查看
function seePartsDetail(num) {
    var pId = $("#seeProduct").data("id");
    var url = '../parts/getProductListForZhiJie.do';
    if (num == 2) {
        url = '../parts/getProductListForJanJie.do';
    } else if (num == 3) {
        url = '../parts/getPartsListForZhiJie.do';
    } else if (num == 4) {
        url = '../parts/getProductListForBeforeZhiJie.do';
    } else if (num == 5) {
        url = '../parts/getProductListForBeforeJianjie.do';
    } else if (num == 6) {
        url = '../parts/getPartsListForBeforeZhiJie.do';
    }
    $.ajax({
        "url": url,
        "data": {
            "id":pId
        },
        success: function (res) {
            var list = res.data.list;
            var html = '',ttl1 = '';
            if (num == 1) {
                ttl1 = '当前直接含有本件的产品';
                $(".introTtl").html("当前直接含有本件的产品有"+list.length+"种，具体如下：");
            } else  if (num == 2) {
                ttl1 = '当前间接含有本件的产品';
                $(".introTtl").html("当前间接含有本件的产品有"+list.length+"种，具体如下：");
            } else  if (num == 3) {
                ttl1 = '当前直接含有本件的组件';
                $(".introTtl").html("当前直接含有本件的组件有"+list.length+"种，具体如下：");
            } else  if (num == 4) {
                ttl1 = '曾直接含有本件的产品';
                $(".introTtl").html("曾直接含有本件的产品有"+list.length+"种，具体如下：");
            } else  if (num == 5) {
                ttl1 = '曾间接含有本件的产品';
                $(".introTtl").html("曾间接含有本件的产品有"+list.length+"种，具体如下：");
            } else  if (num == 6) {
                ttl1 = '曾直接含有本件的组件';
                $(".introTtl").html("曾直接含有本件的组件有"+list.length+"种，具体如下：");
            }
            $("#seePartsDetail .bonceHead span").html(ttl1);
            if (list && list.length > 0){
                for(var i=0;i<list.length;i++){
                    html+=
                        '<tr data-id="'+ list[i].id +'">' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name + ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+ ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+'</td>' +
                        '    <td>'+ handleNull(list[i].level) +'</td>' +
                        '    <td>'+ handleNull(list[i].amount) +'</td>' +
                        '    <td class="createInfo">'+ list[i].gcName + '&nbsp;&nbsp;' + new Date(list[i].date).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                        '</tr>';
                }
            }
            $("#seePartsDetail .combin tbody").append(html);
            bounce_Fixed.show($("#seePartsDetail"));

        }
    });
}
// creator: 李玉婷，2021-12-13 08:06:44，组件当前全部的零组件
function allRelatedParts() {
    var pId = $("#seeProduct").data("id");
    var url = '/parts/getAllPartsByParts.do';
    var part = JSON.parse($("#productInfo").html());
    var view = JSON.parse($("#viewSettingsState").html());
    var ttl = {}, str='';
    $("#seePartsGroup tbody").find("tr:eq(0)").nextAll().remove();
    let proStr =
        `<tr>
            <td>${handleNull(part.innerSn)}</td>
            ${resultAllParts(view,"product",part)}
            <td>${handleNull(part.unit)}</td>
            <td>${charge(part.phrase, 'state')}</td>
            <td>${handleNull(part.processDeptName)}</td>
            <td>${netWeightLimit(part.netWeight,part['weightUnit'])}</td>
        </tr>
        <tr>
            <td>说明</td>
            <td colspan="8" class="algnL">${handleNull(part.memo)}</td>
        </tr>`;
    $("#seePartsGroup tbody:eq(0)").append(proStr);
    if (view.frame == 1) {
        $(".pro-model").hide();
        $(".pro-specifications").hide();
        $(".trends-specifications").hide();
        $(".trends-model").hide();
        for (var s in view){
            if (s == 'product' || s == 'component') {
                if (view[s] == 1) {
                    str = '名称/规格/型号';
                } else if (view[s] == 2) {
                    str = '名称';
                } else if (view[s] == 3) {
                    str = '名称/规格';
                } else {
                    str = '名称/型号';
                }
                ttl[s] = str;
            }
        }
    } else {
        if (view.product == 1) {
            $(".pro-model").show();
            $(".pro-specifications").show();
        } else if (view.product == 2) {
            $(".pro-model").hide();
            $(".pro-specifications").hide();
        } else {
            $(".pro-model").hide();
            $(".pro-specifications").show();
        }
        if (view.component == 1) {
            $(".trends-specifications").show();
            $(".trends-model").show();
        } else if (view.component == 2) {
            $(".trends-specifications").hide();
            $(".trends-model").hide();
        } else {
            $(".trends-specifications").show();
            $(".trends-model").hide();
        }
    }
    $(".pro-name").html(ttl.product);
    $(".trends-name").html(ttl.component);
    $.ajax({
        "url": url,
        "data": {
            "id": pId
        },
        success: function (res) {
            var list = res.data;
            var html = '';
            if (list && list.length > 0) {
                var stu = '';
                for (var t = 0; t < list.length; t++) {
                    stu = resultAllParts(view,"component",list[t]);
                    html +=
                        '<tr data-id="' + list[t].id + '">' +
                        '    <td>' + list[t].level + '</td>' +
                        '    <td>' + handleNull(list[t].innerSn) + '</td>' +
                        stu +
                        '    <td>'+ handleNull(list[t].unit) +'</td>' +
                        '    <td>'+ handleNull(list[t].amount) +'</td>' +
                        '    <td>'+ charge(list[t].process,"process") +'</td>' +
                        '    <td>'+ charge(list[t].composition,"composition") +'</td>' +
                        '    <td>'+ netWeightLimit(list[t].netWeight,list[t]['weightUnit']) +'</td>' +
                        '    <td class="createInfo">' + list[t].gcName + '&nbsp;&nbsp;' + new Date(list[t].date).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '</tr>';
                }
            }
            $("#seePartsGroup .tbMain").find("tbody").append(html);
            bounce_Fixed.show($("#seePartsGroup"));
        }
    })
}
// creator: 李玉婷，2022-01-04 15:11:04，全部零组件展示页上的当前设置输出
function resultAllParts(view,key,data){
    var result = '';
    if (view.frame == 1) {
        if (view[key] == 1) {
            result = '<td>' + data.name + ((handleNull(data.specifications) === "") ? '' : '、' + data.specifications) + ((handleNull(data.model) === "") ? '' : '、' + data.model) + '</td>';
        } else if (view[key] == 2) {
            result = '<td>' + data.name + '</td>' ;
        } else if (view[key] == 3) {
            result = '<td>'  + data.name + ((handleNull(data.specifications) === "") ? '' : '、' + data.specifications) + '</td>';
        } else if (view[key] == 4) {
            result = '<td>' + data.name + ((handleNull(data.model) === "") ? '' : '、' + data.model) + '</td>';
        }
    } else {
        if (view[key] == 1) {
            result =
                '<td>' + data.name + '</td>' +
                '<td>' + data.specifications + '</td>' +
                '<td>' + data.model + '</td>' ;
        } else if (view[key] == 2) {
            result = '<td>' + data.name + '</td>' ;
        } else if (view[key] == 3) {
            result = '<td>' + data.name + '</td>' +
                '<td>' + data.specifications + '</td>' ;
        } else if (view[key] == 4) {
            result = '<td>' + data.name + '</td>' +
                '<td>' + data.model + '</td>' ;
        }
    }
    return result;
}
// creator: 李玉婷，2021-12-13 08:06:44，曾经包含的零组件
function allRelatedPartsOnce() {
    var pId = $("#seeProduct").data("id");
    var url = '/parts/getAllPartsByBeforeParts.do';
    $("#seeOncePartsGroup .tbMain").find("tr:eq(0)").nextAll().remove();
    $.ajax({
        "url": url,
        "data": {
            "id": pId
        },
        success: function (res) {
            var list = res.data;
            var html = '';
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    html +=
                        '<tr data-id="' + list[i].id + '">' +
                        '    <td>' + handleNull(list[i].level) + '</td>' +
                        '    <td>' + list[i].innerSn + '</td>' +
                        '    <td>' + list[i].name + ((handleNull(list[i].model) === "") ? '' : '、' + list[i].model) + ((handleNull(list[i].specifications) === "") ? '' : '、' + list[i].specifications) + '</td>' +
                        '    <td>' + handleNull(list[i].unit) + '</td>' +
                        '    <td>' + handleNull(list[i].amount) + '</td>' +
                        '    <td>'+ charge(list[i].process,"process") +'</td>' +
                        '    <td>'+ charge(list[i].composition,"composition") +'</td>' +
                        '    <td>'+ netWeightLimit(list[i].netWeight,list[i]['weightUnit']) +'</td>' +
                        '    <td class="createInfo">' + list[i].gcName + '&nbsp;&nbsp;' + new Date(list[i].date).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '    <td class="createInfo">' + list[i].delName + '&nbsp;&nbsp;' + new Date(list[i].delDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                        '</tr>';
                }
            }
            $("#seeOncePartsGroup .tbMain").find("tbody").append(html);
            bounce_Fixed.show($("#seeOncePartsGroup"));
        }
    })
}
// creator: 李玉婷，2022-01-04 14:53:26，获取零组件设置
function getviewSettings(){
    $.ajax({
        "url": '/parts/getViewSetting.do',
        "data": "",
        async: false,
        success: function (res) {
            var data = res.data;
            $("#viewSettingsState").html(JSON.stringify(data));
        }
    })
}
// creator: 李玉婷，2021-12-15 11:49:51，查看零组件设置
function viewSettings() {
    var data = JSON.parse($("#viewSettingsState").html());
    var pro = Number(data.product) - 1;
    var mar = Number(data.component) - 1;
    var dot = Number(data.frame) - 1;
    $(".settingsCon .leMar i").attr("class","fa fa-circle-o");
    $(".settingsCon .leMar:eq(0)").find("i").eq(pro).attr("class","fa fa-dot-circle-o");
    $(".settingsCon .leMar:eq(1)").find("i").eq(mar).attr("class","fa fa-dot-circle-o");
    $(".settingsCon .leMar:eq(2)").find("i").eq(dot).attr("class","fa fa-dot-circle-o");
    $(".settingsCon .leMar:eq(3)").find("i").eq(0).attr("class","fa fa-dot-circle-o");
    bounce_Fixed.show($('#viewSettings'));
}
// creator: 李玉婷，2021-12-15 12:25:00，零组件设置编辑提交
function viewSettingsOk() {
    var pro = $(".settingsCon .leMar:eq(0) i").index($(".settingsCon .leMar:eq(0) .fa-dot-circle-o"));
    var mar = $(".settingsCon .leMar:eq(1) i").index($(".settingsCon .leMar:eq(1) .fa-dot-circle-o"));
    var dot = $(".settingsCon .leMar:eq(2) i").index($(".settingsCon .leMar:eq(2) .fa-dot-circle-o"));
    var state = $(".settingsCon .leMar:eq(3) i").index($(".settingsCon .leMar:eq(3) .fa-dot-circle-o"));
    pro = Number(pro) + 1;
    mar = Number(mar) + 1;
    dot = Number(dot) + 1;
    var param = {
        "product": pro,
        "component": mar,
        "frame": dot
    }
    $("#viewSettingsState").html(JSON.stringify(param));
    bounce_Fixed.cancel();
    if (state == 0) {
        $.ajax({
            "url": '/parts/setViewSetting.do',
            "data": param,
            success: function (res) {
            }
        })
    }
}
// update: 李玉婷，2021-10-14 15:37:33， 零部件匹配录入限制
function setCompoAndProcess(objP,objC) {
    var process = objP.val(), composition = objC.val(), setDis = false;
    if(process == 1){
        objC.val("").attr("disabled" , "true");
    }else if(process == 2 || process == 3){
        objC.removeAttr("disabled");
        if(composition == 2 && process != 1){
            setDis = true
        }
    }
    if(setDis){
        if ($("#edit_process").is(":visible")) {
            $(".assemblingTip").show();
        } else if($("#process").is(":visible")){
            $("#weightUnit").val("2").attr("disabled" , "true");
            $("#netWeight").val("").attr("disabled" , "true");
        }
    }else{
        if ($("#edit_process").is(":visible")) {
            $(".assemblingTip").hide();
        } else if($("#process").is(":visible")){
            $("#weightUnit").val("2").removeAttr("disabled");
            $("#netWeight").val("").removeAttr("disabled");
        }
    }
}
// creator: 李玉婷，2021-12-15 21:49:48，修改构成确定
function editPartsSourceSure() {
    var editP = $("#actual_process").val();
    var editC = $("#actual_composition").val();
    var info = JSON.parse($("#productInfo").html());
    var pId = info.id;
    var process = info.process,composition = info.composition;
    if (editP == '' || (editP != 1 && editC == "")) {
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }
    if (process == editP && composition == editC) {
        layer.msg("未进行修改，请修改后再确定！");
        return false;
    }
    $.ajax({
        "url": '/parts/updateComposition.do',
        "data": {
            "id": pId,
            "process": editP,
            "composition": editC
        },
        success: function (res) {
            bounce_Fixed.cancel();
            if ((!composition || composition == "") && editC == 2) {
                var item = JSON.parse($("#productInfo").html());
                item.process = editP;
                item.composition = editC;
                $("#splitGS .bounce_close").attr("onclick","goBack(2)");
                $("#splitGS .bonceFoot span:eq(0)").attr("onclick","goBack(2)");
                splitBtn(item , "");
            } else {
                layer.msg("修改成功！");
                getPdDetails(pId);
            }
        }
    })
}
// creator: 李玉婷，2021-12-22 12:23:08，3秒提示
function giveTip() {
    var info = JSON.parse($("#productInfo").html());
    var process = info.process,composition = info.composition;
    if (process == '2' || process == '3') {
        layer.msg("<p>操作失败！</p><p>如本件确需此项修改，请新增产品。</p>");
    }
}


// create:hxz 2020-06-25 拆分商品/零部件
function splitBtn(item , type) {
    var process = item['process'] ;
    var composition = item['composition'] ;
    if(type == "" || ((process == "2" || process == "3") && composition == "2")){
        getParts(item, type);
        if(type == "2"){
            bounce_Fixed.show($("#splitGS2"));
        }else{
            bounce.show($("#splitGS"));
        }
    }else{
        if(process == "1"){
            $("#giveTip8 .msg").html("该零组件为外购成品，无需在此编辑！");
        }else if(composition == "3"){
            $("#giveTip8 .msg").html("该零件的材料需由多种原料先混和，无需在此编辑！");
        }else if(composition == "4"){
            $("#giveTip8 .msg").html("该零件系由一种材料直接加工而成，无需在此编辑！");
        }else{
            $("#giveTip8 .msg").html("后台，， 把他的 process 和 composition 返回！！");
        }
        bounce_Fixed2.show($("#giveTip8"));
    }
}
// create:hxz 2020-06-25 删除零部件
function delPartOk() {
    var item = JSON.parse(editObj.siblings(".hd").html());
    var curP = partsList[partsList.length - 1] ;
    $.ajax({
        "url":"../constitute/lingJianDelete.do",
        "data":{ "id":item.id , "parentId":curP.gsItem.id },
        success:function (res) {
            bounce_Fixed2.cancel();
            var code = res['code'];
            if(code == 200){
                let lastPart = partsList[partsList.length - 1];
                let list = lastPart.data.filter(sys=>sys.id !== item.id);
                lastPart.data = list;
                layer.msg("操作成功");
                setPartsData(lastPart , lastPart.type);
                //editObj.parent().parent().remove();
            }else{
                layer.msg("操作失败");
            }

        }
    })
}
// create:hxz 2020-06-25 设置零部件录入的默认值
function setGSInfo(item , innerSnFalse) {
    for(var key in item){
        $("#" + key).val(item[key]).attr("disabled", "true").parent().attr("disabled", "true");
        if(key == "id"){
            $("#oldId").val(item[key]);
        }
    }
    getUnitList($("#unitId"), 4, item['unitId']);
    var demoLen = item['demo'] ? item['demo'].length :"0" ;
    $(".textMax").html(demoLen + "/100");
    if(innerSnFalse === 1){

    }else{
        $("#innerSn").removeAttr("disabled").parent().removeAttr("disabled");
    }
    $("#amount").removeAttr("disabled").parent().removeAttr("disabled");
}
// create:hxz 2020-06-25 与录入匹配
function gsMatch() {
    $("#selecGS").show();
    var innerEntry = $("#innerSn").val();
    $("#selecGS option").hide();
    var hasMatch = false;
    $("#selecGS option").each(function () {
        var i_innerSn = $(this).html();
        if(i_innerSn.indexOf(innerEntry) != -1){
            $(this).show();
        }
        if(i_innerSn === innerEntry){
            hasMatch = true;
            setSelectOption($(this));

        }
    });
    if(!hasMatch){
        $("#partEntry td").removeAttr("disabled");
        $("#partEntry input:not(#innerSn)").val("").removeAttr("disabled");
        $("#partEntry select").val("").removeAttr("disabled");
        $("#partEntry textarea").val("").removeAttr("disabled");
    }
}
function setSelectOption(thisObj) {
    var i_item = thisObj.val();
    i_item = JSON.parse(i_item);
    $("#id").val(i_item['id']);
    for(var key in i_item){
        if(key != "innerSn"){
            $("#" + key).val(i_item[key]).attr("disabled", "true").parent().attr("disabled", true);
        }
    }
    $("#innerSn").val(i_item['innerSn']);
    $("#amount").removeAttr("disabled").parent().removeAttr("disabled");
}
// create:hxz 2020-06-25 拆分返回/关闭
function goBack(type){
    // 0 - 零部件的返回 ; 1 - 返回产品拆分 ; 2 - 产品拆分的返回主页
    if(type == 1){
        var lastPart =  partsList[0];
        setPartsData(lastPart , "");
    }else if(type == 2){
        var pId = $("#seeProduct").data("id");
        getPdDetails(pId);
    }else if(type == 3){
        bounce.show($('#seeProduct'));
        bounce_Fixed.show($('#editPartsSource'));
    }else if(type == 0){
        partsList.pop();
        var lastPart =  partsList[partsList.length - 1];
        setPartsData(lastPart , lastPart.type);
    }
}
// create:hxz 2020-06-25 确定拆分完毕
function splitOKBtn(type){
    var len = $("#splitGS tbody:eq(1)").children("tr").length ;
    if(len == 1){
        $("#giveTip6 .tipC").html("您尚未录入构成该产品的零组件！");
        bounce_Fixed3.show($("#giveTip6"));
        return false;
    }
    var isAll = true, splitGSCon = "#splitGS" ;
    if(type == 2){ splitGSCon = "#splitGS2" ; }
    $(splitGSCon + " tbody:eq(1)").children("tr:gt(0)").each(function () {
        var td9 = $(this).children(":eq(9)").html();
        if (td9 == "未拆分完"){
            isAll = false ;
        }
    })
    if(isAll){
        splitOK();
    }else {
        layer.msg("本件尚有零组件未拆分完，故系统中为暂存。拆分完的结果才予正式保存。");
        if(type == 2){
            bounce_Fixed2.cancel();
        } else {
            bounce.cancel();
        }
    }
}
function splitOK(){
    var curP = partsList[partsList.length - 1];
    var gsItem = curP.gsItem ;
    var pid = gsItem.id ;
    $.ajax({
        // "url": "../constitute/updateProductBase.do",
        "url": "../constitute/splitComplete.do",
        "data":{"id": pid },
        success:function (res) {
            bounce_Fixed2.cancel();
            var code = res['code'];
            if(code == 200){
                partsList.pop();
                if(partsList.length > 0){
                    layer.msg("操作成功");
                    var lastPart =  partsList[partsList.length - 1];
                    partsList.pop();
                    getParts(lastPart.gsItem, lastPart.type); // 刷新上一个零部件
                }else{
                    // 产品的拆分完了
                    layer.msg("修改成功！");
                    getPdDetails(pid);
                    bounce_Fixed.cancel();
                }
            }else{
                layer.msg("操作失败：" + res.msg);
            }

        }
    })
}
// create:hxz 2020-06-25 录入零部件 - 获得可选的图号
function partEntryBtn(){
    $("#partEntry input").val("")
    $("#partEntry select").val("")
    $("#partEntry textarea").val("")
    var curGS = partsList[partsList.length - 1] ;
    var item = curGS.gsItem
    $.ajax({
        "url": "../constitute/selectLingJianListByParent.do",
        "data":{ "id":item.id  },
        success:function (res) {
            var list = res['data'], str = "" ;
            if(list){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<option value='"+ JSON.stringify(list[i]) +"'>"+ list[i]['innerSn'] +"</option>"
                }
            }
            $("#selecGS").html(str);
        }
    });
    $(".bounce_Fixed2").everyTime('0.5s','partEntry',function(){
        var isNull = false;
        var data = {} ;
        var id = $("#id").val();
        if(id){
            var amount = $("#amount").val();
            if(!amount){ isNull = true; }
        }else{
            var notMust = ["innerSn", "name", "process", "unitId", "unit", "amount"];
            for(var key in notMust){
                var val = $("#" + notMust[key]).val();
                data[notMust[key]] = val ;
                if(!val){
                    isNull = true
                }
            }
            if(!isNull){
                if(data['process'] == 1){
                    var netWeight = $("#netWeight").val()
                    var weightUnit = $("#weightUnit").val()
                    if(netWeight == "" || weightUnit == ""){
                        isNull = true;
                    }
                }else{
                    var composition = $("#composition").val();
                    if(composition == 2){
                    }else{
                        var netWeight = $("#netWeight").val();
                        var weightUnit = $("#weightUnit").val();
                        if(netWeight == "" || weightUnit == ""){
                            isNull = true;
                        }
                    }
                }
            }
        }
        if(isNull){
            $("#partEntryOK").attr("class", "ty-btn bounce-cancel ty-btn-big ty-circle-5");
        }else{
            $("#partEntryOK").attr("class", "ty-btn bounce-ok ty-btn-big ty-circle-5");
        }

    })
}
// create:hxz 2020-06-27 零部件匹配
function startMatch(e){
    e.stopPropagation();
    $(".bounce_Fixed2").everyTime('0.5s',"gsMatch",function(){
        gsMatch();
    });
}
function stopMtch(e){
    $("#selecGS").hide();
    $(".bounce_Fixed2").stopTime("gsMatch")
}
function partEntryOK(){
    var isNull = $("#partEntryOK").hasClass("bounce-cancel");
    if(isNull){
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }
    if($("#netWeight").val().length > 8){
        layer.msg("您录入的数字位数太多了，请选择合适的重量单位！");
        return false;
    }
    var curP = partsList[partsList.length-1];
    var gsItem = curP.gsItem ;
    var type = curP.type ;
    var pid = gsItem.id ;
    var data = { "pdBaseId": pid } ;
    $("#partEntry .entry").each(function(){
        var key = $(this).attr("id");
        var val = $(this).val();
        data[key] = val ;
    });
    if(!data["id"]){
        if(!data["process"]){
            $("#giveTip6 .tipC").html("请选择加工情况");
            bounce_Fixed3.show($("#giveTip6"));
            return false;
        }else{
            if(data["process"]=='1'){

            }else{
                if(!data["composition"]){
                    $("#giveTip6 .tipC").html("请选择构成情况");
                    bounce_Fixed3.show($("#giveTip6"));
                    return false;
                }
            }
        }
    }else{
        if(!data["process"] && !data["composition"]){
            $("#giveTip6 .tipC").html("该产品未走完构成流程，如想使用请先处理");
            bounce_Fixed3.show($("#giveTip6"));
            return false;
        }
    }

    $.ajax({
        "url":"../constitute/addPdBaseLingJian.do",
        "data":data ,
        success:function (res) {
            bounce_Fixed2.cancel();
            var code = res['code'];
            if(code == "200"){
                layer.msg("操作成功");
                partsList.pop();
                getParts(gsItem, type);

            }else{
                layer.msg("操作失败:" + res.msg);
            }

        }
    })
}
// create:hxz 2020-06-21 渲染拆分数据
function setPartsData(res, type){
    if(type == ""){
        bounce_Fixed.cancel();
    }
    var itemGS = res['gsItem'] , netWeightCount = 0;
    $("#gsName").html(itemGS['name']); // 顺便给零件录入的赋值
    var str = itemStr(itemGS,1) +
        "    <td>"+ netWeightLimit(itemGS.netWeight,itemGS.weightUnit) +"</td>" ;
    $("#splitGS" + type).find("tbody:eq(0)").children(":eq(1)").html(str);
    var listStr = "" , list = res['data'];
    if(list){
        var btnStr = '';
        for(var i = 0 ; i < list.length ; i++){
            var item = list[i];
            let {weightUnit, netWeight, amount} = item;
            if (weightUnit && netWeight && !isNaN(netWeight) && handleNull(weightUnit) != ""){
                var power = 1;
                if (weightUnit == '2') {
                    power = 1000;
                } else if (weightUnit == '3') {
                    power = 1000000;
                } else if (weightUnit == '4') {
                    power = 1000000000;
                }
                netWeightCount += Number(netWeight) * power * (amount || 0);
            }
            item.num > 1? btnStr="<span type=\"btn\" data-type=\"noSplit\" class=\"ty-color-gray\">编辑</span>":btnStr="<span type=\"btn\" data-type=\"splitBtn2\" class=\"ty-color-blue\">编辑</span>";
            listStr += "<tr>" + itemStr(item,1) +
                "    <td>"+ netWeightLimit(netWeight,weightUnit) +"</td>" +
                "    <td>"+ (amount || "") +"</td>" +
                "    <td>"+ charge(item['process'] , 'process') +"</td>" +
                "    <td>"+ charge(item['composition'] , 'composition') +"</td>" +
                "    <td>"+ charge(item['split'] , 'split') +"</td>" +
                "    <td>" + btnStr  +
                "        <span type=\"btn\" data-type=\"partsEdit\" class=\"ty-color-blue\">修改装配数量</span>" +
                "        <span type=\"btn\" data-type=\"delBtn\" class=\"ty-color-blue\">删除</span>" +
                "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                "    </td>" +
                "</tr>";
        }
        if(netWeightCount > 0 ) {
            var weightUnitStr = weightUnitCharge(netWeightCount);
            $("#splitGS" + type).find("tbody:eq(0)").children(":eq(1)").find(":last").html(weightUnitStr);
        }
    }
    $("#splitGS" + type).find("tbody:eq(1)").children(":gt(0)").remove() ;
    $("#splitGS" + type).find("tbody:eq(1)").append(listStr);
}
// create:hxz 2020-06-21 获取拆分零部件列表
var partsList = [] ; // 零件拆分树列表
function getParts(item, type) {
    $.ajax({
        "url": "../constitute/getPdOrLingJianList.do"  ,
        "data":{ 'id':item.id } ,
        success:function(res){
            if(type == 2){ // 零件的拆分管理
                res['gsItem'] = item ;
                res['type'] = type ;
                partsList.push(res);
                setPartsData(res , type);
            }else{
                res['gsItem'] = item ;
                res['type'] = "" ;
                partsList = [res];
                setPartsData(res , type);
            }
        }
    })
}
// create:hxz 2020-06-21 设置商品键值对
function setKeyByID(id , key , val) {
    var list = GSList ;
    for(var i = 0 ; i < list.length ; i++){
        if(list[i]['id'] == id){
            list[i][key] = val ;
        }
    }
}
function itemStr(item , num) { // num = 1 不显示create
    var str = "<tr>" ;
    if(item){
        str =
            "    <td>"+ item['innerSn'] +"</td>" +
            "    <td>"+ item['name'] +"</td>" +
            "    <td>"+ (item['model'] || "") +"</td>" +
            "    <td>"+ (item['specifications'] || "") +"</td>" +
            "    <td>"+ (item['unit'] || "" ) +"</td>" ;
        if(num !== 1){
            str += "<td>"+ item['createName'] + " " + (new Date(item['createName']).format("yyyy-MM-dd hh:mm:ss")) + "</td>";
        }
    }
    return str ;
}


// creator: 李玉婷，2021-12-15 17:22:12，修改基本信息提交
function updatePartSure(){
    var emptyNum = 0;
    $("#updateParts [require]").each(function(){
        if($(this).val() == ""){ emptyNum++; }
    });
    if (emptyNum > 0){
        $("#unfilledTip_ms").html('还有必填项尚未填写!');
        bounce_Fixed2.show($("#unfilledTip"));
        return false;
    }else{
        var id = $("#seeProduct").data("id");
        var params = {
            id: id
        };
        $("#updateParts [need]").each(function(){
            var nameData = $(this).attr('name');
            var html = $(this).val();
            params[nameData] = html;
        });
        $.ajax({
            "url": "../parts/updatePartsBase.do",
            "data": params,
            success: function (res) {
                if (res.success == 1) {
                    let jsonObj = {}, door = false;
                    if (pType == 1) {
                        jsonObj = JSON.parse($("#ye").find(".json").html());
                        if ($(".inSuspend").is(":visible")) {door = true;}
                    } else {
                        jsonObj = JSON.parse($("#ye2").find(".json").html());
                        if ($("#productionListQ").data("type") == 3) {door = true;}
                    }
                    bounce_Fixed.cancel();
                    getPdDetails(id);
                    if (door) {
                        getSuspendPtList(jsonObj);
                    } else {
                        getPartsList(jsonObj);
                    }
                }else{
                    var msg = res.error.message;
                    $("#unfilledTip_ms").html(msg);
                    bounce_Fixed2.show($("#unfilledTip"));
                }
            }
        });
    }
}
// creator: 李玉婷，2021-12-16 09:05:59，基本信息修改记录
function baseRecordList(num) {
    let url = ``;
    var info = JSON.parse($("#productInfo").html());
    $("#assemblyTip").hide();
    if (num == 1) {
        url = `../parts/getRecordBaseList.do`;
        $("#baseRecords .bonceHead span").html("基本信息的修改记录");
    } else if (num == 2) {
        url = `../parts/getCompositionRecordBaseList.do`;
        $("#baseRecords .bonceHead span").html("来源/构成修改记录");
    }
    $.ajax({
        "url": url,
        "data": { "id": info.id  } ,
        success:function(res) {
            let list = res.data.list || [];
            bounce_Fixed.show($("#baseRecords"));
            let curStaStr = ``;
            if(list.length >0){
                let str = ``;
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    i == 0 ? item.front = 0:item.front = list[Number(i) - 1].id;
                    str += `<tr>
                             <td>${i===0 ? "原始信息":"第"+ i +"次修改后"}</td>
                             <td class="ty-td-control">
                                <span class="linkBtn ty-color-blue" data-type="baseRecordScan" data-source="${num}">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                                <td>${i===0 ?item.createName:item.updateName} ${i===0 ?new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss"):new Date(item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                $("#baseRecords tbody").children(":gt(0)").remove();
                $("#baseRecords tbody").append(str);
                let n = list.length
                let last = list[n-1]
                curStaStr = `<span style="float: right;">修改人：${last.updateName} ${new Date(last.updateDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料为第${n-1}次修改后的结果。`
                $("#baseRecords table").show();
                if (num == 2 && info.composition == '2') {
                    $("#assemblyTip").show();
                }
            }else{
                $("#baseRecords table").hide();
                curStaStr = `<span style="float: right;">创建人：${info.createName} ${new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料尚未经修改。`
            }
            $("#baseRecords .curSta").html(curStaStr);
        }
    });
}
// creator: 李玉婷，2021-12-16 10:00:57，基本信息修改记录查看
function baseRecordScan(obj) {
    var info = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        "url":"../parts/getRecordBaseDetails.do",
        "data": { "id": info.id,"front": info.front  } ,
        success:function(res) {
            var info = res.data.now;
            var key = '';
            $("#scanBaseRecords [need]").each(function(){
                key = $(this).data("name")
                if (key == 'phrase'){
                    $(this).html(charge(info[key] , 'state'));
                }else if (key == 'name'){
                    $(this).html(info[key] + ((handleNull(info["specifications"]) === "")?'':'、'+ info["specifications"])+ ((handleNull(info['model']) === "")?'':'、'+info['model']));
                }else if (key == 'netWeight'){
                    $(this).html(netWeightLimit(info[key],info['weightUnit']));
                }else {
                    $(this).html(info[key]);
                }
            });
            bounce_Fixed2.show($("#scanBaseRecords"));
        }
    });
}
// creator: 李玉婷，2022-03-17 13:39:28，构成修改记录详情
function compositionRecordScan(obj) {
    var orgInfo = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        "url":"../parts/getCompositionRecordBaseDetails.do",
        "data": { "id": orgInfo.id,"front": orgInfo.front  } ,
        success:function(res) {
            var info = res.data.now;
            var key = '', detail = '', html = '';
            $("#scancompositionRecords [need]").each(function(){
                key = $(this).data("name")
                if (key == 'phrase'){
                    $(this).html(charge(info[key] , 'state'));
                }else if (key == 'name'){
                    $(this).html(info[key] + ((handleNull(info["specifications"]) === "")?'':'、'+ info["specifications"])+ ((handleNull(info['model']) === "")?'':'、'+info['model']));
                }else if (key == 'netWeight'){
                    $(this).html(netWeightLimit(info[key],info['weightUnit']));
                }else {
                    $(this).html(info[key]);
                }
            });
            bounce_Fixed2.show($("#scancompositionRecords"));
            if (info.process) {
                if (info.process == '1') {
                    detail = '本件委托外部加工';
                    $(".otherOnly").hide();
                    loading.close() ;
                } else {
                    $(".otherOnly").show();
                    if (info.process == '2') {
                        detail = '本件为自制,';
                    } else if (info.process == '3') {
                        detail = '本件本公司能自制，但有时外包,';
                    }
                    const list = info.list || [];
                    if (info.composition == '2'){
                        $(".compRecord3").show().siblings().hide();
                        detail += '为装配件';
                        let frontList = [];
                        if (orgInfo.front == '0'){
                            frontList = list || [];
                        }else{
                            frontList = res.data.front.list || [];
                        }
                        if (list && list.length > 0) {
                            let red = ``, markRed = ``;
                            for (var i = 0; i < list.length; i++) {
                                //标红
                                frontList.findIndex((value)=>value.id==list[i].id) == -1? red = `trRed`:red = ``;
                                if (red == ``) {
                                    var line = frontList.filter(item=>item.id == list[i].id);
                                    handleNull(line[0].amount) == handleNull(list[i].amount)? markRed = ``:markRed = `tdRed`;
                                } else {
                                    markRed = ``;
                                }
                                html +=
                                    '<tr data-id="'+ list[i].id +'" class="'+ red +'">' +
                                    '    <td>'+ list[i].innerSn +'</td>' +
                                    '    <td>'+ list[i].name + ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+ ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+'</td>' +
                                    '    <td>'+ list[i].unit +'</td>' +
                                    '    <td class="'+ markRed +'">'+ handleNull(list[i].amount) +'</td>' +
                                    '    <td>'+ charge(list[i].process,"process") +'</td>' +
                                    '    <td>'+ charge(list[i].composition,"composition") +'</td>' +
                                    '    <td>'+ netWeightLimit(list[i].netWeight,list[i]['weightUnit']) +'</td>' +
                                    '    <td>' +
                                    '        <span class="ty-color-blue">查看</span>' +
                                    '    </td>' +
                                    '</tr>';
                            }
                            for (var i = 0; i < frontList.length; i++) {
                                //标红
                                if (list.findIndex((value)=>value.id==frontList[i].id) == -1){
                                    html +=
                                        '<tr data-id="'+ frontList[i].id +'" class="lineThrough">' +
                                        '    <td>'+ frontList[i].innerSn +'</td>' +
                                        '    <td>'+ frontList[i].name + ((handleNull(frontList[i].specifications) === "")?'':'、'+frontList[i].specifications)+ ((handleNull(frontList[i].model) === "")?'':'、'+ frontList[i].model)+'</td>' +
                                        '    <td>'+ frontList[i].unit +'</td>' +
                                        '    <td class="'+ markRed +'">'+ handleNull(frontList[i].amount) +'</td>' +
                                        '    <td>'+ charge(frontList[i].process,"process") +'</td>' +
                                        '    <td>'+ charge(frontList[i].composition,"composition") +'</td>' +
                                        '    <td>'+ netWeightLimit(frontList[i].netWeight,frontList[i]['weightUnit']) +'</td>' +
                                        '    <td>' +
                                        '        <span class="ty-color-blue">查看</span>' +
                                        '    </td>' +
                                        '</tr>';
                                }
                            }
                        }
                        $(".compRecord3").find("tbody").html(html);
                    }else if (info.composition == '3'){
                        $(".compRecord2").show().siblings().hide();
                        detail += '材料需由多种原料先混合';
                        if (list && list.length > 0) {
                            for (var i = 0; i < list.length; i++) {
                                html +=
                                    '<tr>' +
                                    '    <td>' + list[i].code + '</td>' +
                                    '    <td>' + list[i].name + '/' + list[i].materialName + '</td>' +
                                    '    <td>' + list[i].majorIngredient + '</td>' +
                                    '    <td>' + list[i].minorIngredient + '</td>' +
                                    '</tr>';
                            }
                        }
                        $(".compRecord2 table tbody").html(html);
                    }else if (info.composition == '4'){
                        $(".compRecord1").show().siblings().hide();
                        detail += '由所购买的单一材料直接加工而成';
                        if (list && list.length > 0) {
                            for (var i = 0; i < list.length; i++) {
                                html +=
                                    '<tr>' +
                                    '    <td>' + list[i].code + '</td>' +
                                    '    <td>' + list[i].name+ ((handleNull(list[i].specifications) === "") ? '' : '、' + list[i].specifications) + ((handleNull(list[i].model) === "") ? '' : '、' + list[i].model)  + '</td>' +
                                    '    <td>' + list[i].unit + '</td>' +
                                    '</tr>';
                            }
                        }
                        $(".compRecord1 table tbody").html(html);
                    }
                }
                $("#cDescript").html(detail);
            } else {
                $(".otherOnly").hide();
            }
        }
    });
}
// creator: 李玉婷，2022-03-28 12:47:39，停止生产的操作记录
function ptSuspendRecord() {
    let info = JSON.parse($("#productInfo").html());
    $('#partSuspendRecord tbody tr:gt(0)').remove();
    $.ajax({
        "url": '../product/getProductStopAndStartRecord.do',
        "data": { "id": info.id  } ,
        success:function(res) {
            let list = res.data.list || [];
            let str = ``, item = ``;
            if(list.length >0){
                for(let i = 0 ; i < list.length;i++){
                    item = list[i];
                    str += `<tr>
                                <td>${i===0 ?item.createName:item.updateName} ${i===0 ?new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss"):new Date(item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                                <td>${item.enabled === '0' ? "停止生产":"恢复生产"}</td>
                            </tr>`
                }
            }
            $('#partSuspendRecord tbody').append(str);
            bounce_Fixed.show($('#partSuspendRecord'));
        }
    });
}
// creator: 李玉婷，2022-01-25 08:19:56，关闭零组件查看
function closePartScan() {
    var len = partList.length;
    if (len <= 1){
        partList = [];
        bounce.cancel();
    } else {
        partList.splice(len-1,1);
        if (partList.length == 1) {
            var icon =  $("#seeProduct").data("source");
            $(".middleScan").hide();
            $(".partsScan2").show();
            if (icon == 1) {
                $("#stopDetails").hide();
            } else if (icon == 2) {
                $("#stopDetails").show();
            }
        }
        getPdDetails(partList[len-2]);
    }
}
//lyt:1=由有权限创建商品的职工修改,2=由有产品操作权限的职工修改
function getEditState() {
    $.ajax({
        "url": "../popedom/getCurrentItem.do",
        "data": {"code": 'commodityProduct'},
        success: function (res) {
            let model = res.data.pdModelSettings;
            editType = model.reviseModel;
        }
    });
}
// creator: 李玉婷，2021-12-15 21:17:22，当前零组件详情
function productIntrolCommon (obj) {
    var part = JSON.parse($("#productInfo").html());
    var key = '';
    obj.find("[need]").each(function(){
        key = $(this).data("name");
        if (key == 'phrase'){
            $(this).html(charge(part[key], 'state'));
        }else if (key == 'name'){
            $(this).html(part[key] + ((handleNull(part["specifications"]) === "")?'':'、'+ part["specifications"])+ ((handleNull(part['model']) === "")?'':'、'+part['model']));
        }else if (key == 'netWeight'){
            $(this).html(netWeightLimit(part[key],part['weightUnit']));
        }else{
            $(this).html(handleNull(part[key]));
        }
    });
}
// create:hxz 2020-06-21 翻译键值
function charge(val , type) {
    var str = ""
    switch (type){
        case 'process':
            if(val == "1"){ str = "外购"; }
            if(val == "2"){ str = "自制"; }
            if(val == "3"){ str = "自制+外包"; }
            break;
        case 'weightUnit':
            if(val == "1"){ str = "毫克"; }
            if(val == "2"){ str = "克"; }
            if(val == "3"){ str = "千克"; }
            if(val == "4"){ str = "吨"; }
            break;
        case 'composition':
            if(val == "1"){ str = "成品"; }
            if(val == "2"){ str = "装配"; }
            if(val == "3"){ str = "制造"; }
            if(val == "4"){ str = "制造"; }
            break;
        case 'split':
            if(val == "0"){ str = "无需拆分"; }
            if(val == "1"){ str = "未拆分完"; }
            if(val == "2"){ str = "拆分完毕"; }
            break;
        case 'state':
            if(val == "0"){ str = ""; }
            if(val == ""){ str = "未知"; }
            if(val == "1"){ str = "开发中"; }
            if(val == "2"){ str = "开发完成"; }
            break;
    }
    return str
}
// creator: 李玉婷，2022-01-14 09:59:15，限制单重最多8位
function toFixedNetweight(str,size) {
    var result = str;
    if (str.toString().length > size) {
        result = str.substr(0,size);
        if (result.charAt(result.toString().length-1) == '.'){
            result = str.substr(0,Number(size)+1);
        }
    }
    return result;
}
// creator: 李玉婷，2022-02-11 15:47:01，单重+单位
function netWeightLimit(val, unit) {
    var netWeight = 0,result = '';
    if (handleNull(val) != "" && handleNull(unit) != "") {
        if (unit == '1') {
            netWeight = Number(val);
        } else if (unit == '2') {
            netWeight = Number(val)*1000;
        } else if (unit == '3') {
            netWeight = Number(val)*1000000;
        } else if (unit == '4') {
            netWeight = Number(val)*1000000000;
        }
        result = weightUnitCharge(netWeight);
    } else {
        result = '--';
    }
    if(val == null || unit == null){
        result = "";
    }
    return result;
}
// creator: 李玉婷，2022-02-11 11:33:33，单重换算
function weightUnitCharge(netWeightCount) {
    var weightUnitStr = '', unitStr = '';
    if (netWeightCount > 10 && netWeightCount < 1000000) {
        unitStr = ' 克';
        weightUnitStr = (netWeightCount/1000).toFixed(3);
    } else if (netWeightCount >= 1000000 && netWeightCount < 1000000000) {
        unitStr = ' 千克';
        weightUnitStr = (netWeightCount/1000000).toFixed(3);
    } else if (netWeightCount >= 1000000000) {
        unitStr = ' 吨';
        weightUnitStr = (netWeightCount/1000000000).toFixed(3);
    } else{
        unitStr = ' 毫克';
        weightUnitStr = netWeightCount.toFixed(3);
    }
    return toFixedNetweight(weightUnitStr) + unitStr;
}
// creator: 李玉婷，2023-04-21 14:51:41，获取通用商品列表
function getTYProductList(){
    let cmHtml = `<option value="">请选择通用型商品</option>`;
    $.ajax({
        url: "../product/getTYProductList.do",
        data: {},
        success: function (data) {
            var list = data.data;
            $(".haveloonk").data("genalmodel",list);
            if (list && list.length > 0){
                for(var t=0;t<list.length;t++){
                    cmHtml += `<option value="${list[t].id}">${handleNull(list[t].outerName)}</option>`;
                }
            }
            $("#cmGoods").html(cmHtml);
        }
    })
}

// creator: sy 2023-11-1    请选择专属商品
function getzslist(oibe){
    var unip = [];
    var zsbeal = "";
    zsbeal =    $(".zsprolist").data("spication");
    if(zsbeal == undefined){
        zsbeal = $(".zsGoodsList").data("spication2");
    }
    if(zsbeal !== undefined){
        $.each(zsbeal, function(i, el){
            if($.inArray(el, unip) === -1) unip.push(el);
        });
        zsbeal = unip;
    }
    // var khmeg = $("#entrover").data("spciaprose");//客户数据
    //zsbeal是专属商品数据
    $("#entrover").data("spciaprose",zsbeal);
    var otder = $(".zsGoodsList").data("spication2");//otder似乎每次触发的时候只有对应客户的数据
    $("#association").data("spciaen",zsbeal);
    $("#association").data("spciaen2",otder);//otder是不是只有一个客户的数据呢？
    //是不是需要手动保存下呢
}

//creator:lyt 2023/4/11 0011 上午 11:05 请选择客户
function getZsProductList(obj){
    //sy添加的部分------------------
    var newbel = $(".customerList").data("spciaprosen");
    //-----------------------------------
    let customer = obj.val();
    let html = `<option value="" class="zsprolist">请选择专属商品</option>`;
    let able = false;
    obj.parent(".zsCase").siblings().each(function (){
        let item = $(this).find(".customerList").val();
        if(item === customer) { able = true;}
    })
    if (able) {
        layer.msg("已有该组客户与商品，不能再选了！");
        obj.val("");
        obj.siblings("select").html(html);
        return false;
    } else {
        if (customer !== "") {
            $.ajax({
                url: "../product/getZsProductList.do",
                data: {"customerId": customer},
                success: function (data) {
                    var list = data.data;
                    //sy添加的部分------------------
                    if(newbel == undefined){
                        $(".customerList").data("spciaprosen",list);
                        $("#entrover").data("spciaprose",list);
                        $(".zsprolist").data("spication",list);
                        // $(".redLinkBtn").data("spciaproce",list);
                    }else{
                        list.forEach(itemli => {
                            newbel.push(itemli);
                        })
                        $(".customerList").data("spciaprosen",newbel);
                        $("#entrover").data("spciaprose",newbel);
                        $(".redLinkBtn").data("spciaproce",newbel);
                        $(".zsprolist").data("spication",newbel);
                        $(".zsGoodsList").data("spication2",list);
                        // var keyd = $(".customerList").data("addmeg");
                        // if(keyd == 1){//代表已经新增了一条数据
                        //     $(".redLinkBtn").data("spciaproce",newbel);
                        // }
                    }

                    //-----------------------------------
                    if (list && list.length > 0){
                        for(var t=0;t<list.length;t++){
                            html += `<option value="${list[t].id}">${handleNull(list[t].outerName)}</option>`;
                        }
                    }
                    obj.siblings("select").html(html);
                }
            })
        }
    }
}
let customersList = []
function getAllCustomer(){
    $.ajax({
        url: "../product/getAllCustomer.do",
        data: {},
        success: function (data) {
            var customers = data.data;
            customersList = customers;
            setCustomerList($(".customerList").eq(0))
        }
    })
}
function setCustomerList(obj){
    let menu = `<option value="">请选择客户</option>`;
    if (customersList && customersList.length > 0){
        for(var m=0;m<customersList.length;m++){
            menu += `<option value='${customersList[m].id}'>${customersList[m].fullName }</option>`;
        }
    }
    obj.html(menu);
}
//creator:lyt 2023/4/23 16:04 新增一组客户、专属商品
function addMore() {
    let html = `<div class="zsCase">
                   <select class="customerList" onchange="getZsProductList($(this))">
                       <option value="">请选择客户</option>
                   </select>
                   <select class="zsGoodsList" onchange="getzslist($(this))">
                       <option value="" class="zsprolist">请选择专属商品</option>
                   </select>
                   <span class="redLinkBtn" onclick="deleteGroup($(this))">删除</span>
               </div>`;
    $("#zsGoods").append(html);
    setCustomerList($("#zsGoods .zsCase:last .customerList"));
    // $(".customerList").data("addmeg",1);
}
function deleteGroup(obj){
    //sy添加的部分-------
    var unid = obj.parent().find(".zsGoodsList").val();
    unid = Number(unid);
    var lanks = $(".redLinkBtn").data("spciaproce");
    $("#closewinden").data("dettend",lanks);//全部数据
    for(var j in lanks){
        var id = lanks[j].id;
        if(id == unid){
            lanks.splice(j,1);
        }
    }
    $(".customerList").data("spciaprosen",lanks);//修改之后的数据
    $("#entrover").data("spciaprose",lanks);
    //------------------------
    obj.parent().remove();
    // var strd = obj.parent().html();
    // $("#closewinden").data("dettmsg",strd);
}


// creator: sy 2023-07-31   点击“关联”展示“与商品关联”弹窗
function openasoiat(){
    // var lank1 = $("#association").data("prolist");//应该是还没点击录入完毕按钮，所以获取不到
    var lank1 = {};
    //👇获取上一个弹窗输入的产品数据
    $("#productionFrom [need]").each(function(){
        var name = $(this).attr('name');
        var key = $(this).val();
        lank1[name] = key;
    })
    var str1 = ``;
    var process= lank1.phrase;
    switch (process) {
        case '1':
            var stage = '开发中';
            break;
        case '2':
            var stage = '开发完成';
            break;
    }
    str1 += `
        <tbody>
            <tr>
                <td width="18%">产品图号</td>
                <td width="18%">产品名称</td>
                <td width="10%">计量单位</td>
                <td width="10%">所处阶段</td>
                <td width="10%">单重</td>
                <td width="10%">加工部门</td>
            </tr>
            <tr>
                <td>${lank1.innerSn}</td>
                <td>${lank1.name}/${lank1.specifications}/${lank1.model}</td>
                <td>${lank1.unit}</td>
                <td>${handleNull(stage)}</td>
                <td>--</td>
                <td>${lank1.processDeptName}</td>
            </tr>
            <tr>
                <td>产品说明</td>
                <td colspan="8">${handleNull(lank1.memo)}</td>
            </tr>
        </tbody>
        `;
    $("#productionlook").html(str1);
    let keyed = $("#association").data("keyed");
    var khmsg = $(".customerList").val();
    if(khmsg !== undefined || khmsg !== ""){
        keyed = 2;
    }
    switch(keyed){
        case 1://初次点击‘关联’
            $("#zsGoods").children().slice(1).remove();//将所选父级下除第一个子级外的其他子级都删除
            var strbef = `<option value="">请选择专属商品</option>`;
            $(".zsGoodsList").html(strbef);
            break;
        case 2://不是初次的情况
            let generalbox = $("#association").data("general");//用于存储选择的通用型商品数据
            var zslank = $("#association").data("changemeg");
            console.log('zslank',zslank);
            if(zslank){
                $("#zsGoods").html(zslank);//replaceWith指覆盖掉目前展示的zsGoods本身
            }

            var alllink = $("#association").data("msgbox");//alllink包含id和专属数据
            if(alllink.length == 0){}
            else if(alllink){
                var zslist = alllink.zsProductList;//专属数据   zslist是个数组
                zslist = JSON.parse(zslist);//选择的包含客户名称和专属数据的数组
                var boxall5 = $("#association").data("spciaen");
                var boxall = $("#association").data("spciaen");
                //应该需要想办法在boxall5和boxall中将专属数据对应的客户名称给赋值上
                var boxallc =  $("#association").data("spciaen2");//在选择一个客户的时候boxallc的数据就是对应客户的专属数据
                zslist.forEach(function(itemzs,index){
                    var unid = itemzs.productId;
                    unid = Number(unid);//专属数据的value的值
                    var khId = itemzs.khId;
                    khId = Number(khId);//客户数据的value的值
                    var name = itemzs.name;//客户名称
                    var html = `<option value="" class="zsprolist">请选择专属商品</option>`;
                    $.ajax({
                        url: "../product/getZsProductList.do",
                        data:{"customerId":khId},
                        success:function(data){
                            var list = data.data;//list就是单个客户对应的专属数据
                            list.forEach(itemdel => {
                                html += `<option value="${itemdel.id}">${handleNull(itemdel.outerName)}</option>`;
                            })
                            if(index == 0){
                                //怎么将存储的客户名称和专属数据赋值在筛选框中并展示为选中呢
                                $("#zsGoods").find(".zsCase:eq(0)").find(".customerList").val(khId);
                                // var html5 = `<option value="" class="zsprolist">请选择专属商品</option>`;
                                // if(boxall5){
                                // //     boxall5.forEach(itemall => {
                                // //         var productId = itemzs.productId;
                                // //         productId = Number(productId);
                                // //         if(itemall.id == productId){
                                // //             itemall.name = itemzs.name;
                                // //         }
                                // //     })
                                //     boxall5.forEach(itemdel => {
                                //         html5 += `<option value="${itemdel.id}">${handleNull(itemdel.outerName)}</option>`;
                                //     })
                                // }
                                $("#zsGoods").find(".zsCase:eq(0)").find(".zsGoodsList").html(html);
                                $("#zsGoods").find(".zsCase:eq(0)").find(".zsGoodsList").val(unid);
                                //$("#select_id ").val(4);  //设置Select的Value值为4的项选中
                                // $(".customerList").change(function(){
                                //     $(".zsGoodsList").empty();
                                //     var productid = Number(itemzs.productId);
                                //     // var option = $("<option>").val(productid).text(itemzs.outerName);
                                //     // $(".zsGoodsList").append(option);
                                // })
                            }else{
                                var numb = index;
                                $("#zsGoods").find(".zsCase").eq(numb).find(".customerList").val(khId);
                                // var html = `<option value="" class="zsprolist">请选择专属商品</option>`;
                                // console.log('boxall的格式',boxall);//boxall只是专用数据的数组
                                // console.log('customersList',customersList);//customersList中只有客户数据，一点客户同专属数据的联系都没有

                                // if(boxall){
                                //     boxall.forEach(itemall => {//boxall是修改前后所选客户下的所有专属商品，需要根据选择的客户的不同清楚下多余的专属商品
                                //         //为何有的时候boxall中的数据就都是修改后的客户下的数据呢，
                                //         var productId = itemzs.productId;
                                //         productId = Number(productId);
                                //         if(itemall.id == productId){
                                //             itemall.name = itemzs.name;
                                //         }
                                //     })
                                //     boxallc.forEach(itemdel => {
                                //         html += `<option value="${itemdel.id}">${handleNull(itemdel.outerName)}</option>`;
                                //     })
                                // }
                                $("#zsGoods").find(".zsCase").eq(numb).find(".zsGoodsList").html(html);
                                $("#zsGoods").find(".zsCase").eq(numb).find(".zsGoodsList").val(unid);
                                //哇，生效了诶,原来eq()括号中的若不是直接的数字格式，就需要在外面写上.eq(numb)这样才能生效
                                //为何在修改客户数据后，专属数据筛选框还是修改前的可供选择的数据呢，所以才会获取不到
                                //是修改了客户后并没有联动专属数据么？存储的选中的数据的value也获取到了，结果筛选框的数据没有刷新…………
                                //怎么说呢，就是手动修改的时候若是只改专属商品，点击‘取消’按钮后数据正确回到修改前的样子，但是修改
                                //客户数据的话，同客户数据关联的专属数据就获取不到了…………或是说专属数据获取有误，唉，真不知道怎么改了…………
                                //哇，成功啦！！！！！
                            }
                        }
                    })
                    // boxall5.forEach(function(itemed,index) {
                    //     if(itemed.id == boxallc.id){
                    //     }else{
                    //         boxall5.splice(index,1);
                    //     }
                    // })
                    //如：$(".selector1").change(function(){
                    //      // 先清空第二个
                    //       $(".selector2").empty();
                    //      // 实际的应用中，这里的option一般都是用循环生成多个了
                    //       var option = $("<option>").val(1).text("pxx");
                    //       $(".selector2").append(option);
                    // });
                })
            }
            // if(!zslist){
            //     //思路：获取上一次点击确定按钮保存的数据，将带有新增按钮的zsCase作为固定的div，渲染保存的数据的时候让index=0的数据渲染在固定的div上
            //     //其余的数据渲染在末尾带哟删除的div中，例如index=1渲染在带有删除的div中
            //     zslist.forEach(function(itemzs,index){
            //         if(index == 0){
            //             let menu = `<option value="">请选择客户</option>`;
            //             if (customersList && customersList.length > 0){
            //                 for(var m=0;m<customersList.length;m++){
            //                     menu += `<option value='${customersList[m].id}'>${customersList[m].fullName }</option>`;
            //                 }
            //             }
            //
            //         }else{//index >
            //
            //         }
            //     })
            // }
            //点击确定才获取最新数据，点击取消还是原数据
            // let keyed = $("#association").data("keyed");
            // switch (keyed) {}
            // case 1://初次点击‘关联’
            //     let keyed1 = keyed;
            //     $("#entrover").data("keyed",keyed1);
            //     //清空前一次选择的缓存数据
            //     $("#cmGoods").val("");
            //     $(".customerList").val("");
            //     $(".zsGoodsList").val("");
            //     $("#zsGoods").children().slice(1).remove();//将所选父级下除第一个子级外的其他子级都删除
            //     $("#closewinden2").hide();
            //     $("#closewinden").show();
            //     break;
            // case "2-1"://初次点击关联后点击“取消”
            //     break;
            // case 2://已经设置过一次‘关联’了
            //     let keyed2 = keyed;
            //     $("#entrover").data("keyed",keyed2);
            //     var keylink = $("#association").data("ycoffer");//移除后获取到的数据
            //     if(keylink == undefined){
            //         $("#cmGoods").val("");
            //         $(".customerList").val("");
            //         $(".zsGoodsList").val("");
            //         $("#zsGoods").children().slice(1).remove();//将所选父级下除第一个子级外的其他子级都删除
            //     }
            //     else if(keylink.length == 0){//代表包括通用型商品也被移除了
            //         $("#cmGoods").val("");
            //         $(".customerList").val("");
            //         $(".zsGoodsList").val("");
            //         $("#zsGoods").children().slice(1).remove();//将所选父级下除第一个子级外的其他子级都删除
            //     }else{//仍有剩余数据
            //         $("#closewinden2").show();
            //         $("#closewinden").hide();
            //         var zsbox = [];
            //         if(keylink.length == 1){//不是只有通用型商品就是只有一个专用型商品
            //             keylink.forEach(itemk => {
            //                 if(itemk.name == true || itemk.name == false || itemk.name == "" || itemk.name == undefined){//这是那个通用型数据
            //                     //这时只需要展示一个空白选择框的专属型数据一行，通用型商品继续展示原来的数据
            //                     $(".customerList").val("");
            //                     $(".zsGoodsList").val("");
            //                     $("#zsGoods").children().slice(1).remove();//将所选父级下除第一个子级外的其他子级都删除
            //                 }else{//只有专属型数据的时候
            //                     $("#cmGoods").val("");
            //                     zsbox.push(itemk.name);
            //                     $("#zsGoods").children().slice(1).remove();
            //                 }
            //             })
            //         }else{//有不止一个数据
            //             var tyor = $("#association").data('tyor');//通用筛选框不能清空
            //             keylink.forEach(function(itemk,index){
            //                 if(index == 0){//第一条数据
            //                     if(itemk.name == undefined || itemk.name == "") {//第一条是通用
            //                         itemk.name = "";
            //                         // zsbox.push(itemk.name);
            //                         if(tyor == 'tyhas'){
            //                         }else{//通用输入框清空
            //                             $("#cmGoods").val("");
            //                         }
            //                     }else{
            //                         zsbox.push(itemk.name);
            //                         if(tyor == 'tyhas'){
            //                         }else{//通用输入框清空
            //                             $("#cmGoods").val("");
            //                         }
            //                     }
            //                 }else{
            //                     zsbox.push(itemk.name);
            //                     if(tyor == 'tyhas'){
            //                     }else{//通用输入框清空
            //                         $("#cmGoods").val("");
            //                     }
            //                 }
            //             })
            //             //还有关联了四条数据，移除了通用型数据，剩下三条专属型商品
            //             var lengthd = keylink.length;//可能包含了通用型商品     3   4   3
            //             //lengthd是全部数据的长度，其中可能包含通用型商品
            //             var n = 1;
            //             if(zsbox.length == lengthd){//3=3   lengthd中不包含通用商品
            //                 //索引值从0开始计算，2个专属数据的话，就是从索引值2开始
            //                 $("#zsGoods").children().slice(lengthd).remove();//2
            //             }else{//3<4     其中有一个通用     lengthd需要减去1个通用，再减去1个0，相当于专用是1的话，一共数据是2，就是从索引值0开始
            //                 $("#zsGoods").children().slice(lengthd-n).remove(); //2-1
            //             }
            //         }
            //     }
            //     break;
            // }
            //选择框无需清空了
            $("#entrover").data("general",generalbox);
            break;
    }

    //  var oldlist= $("#association").data("befall");
    //  var zsbox = oldlist.zsProductList;
    //  zsbox = JSON.parse(zsbox);//旧的数据
    // //点击取消移除的部分再回来
    //  //怎么将获取到的数据展示在筛选框中呢，就选中的一个数据
    //  var allbox = $("#association").data("detlist");

    bounce_Fixed.show($("#catasoiation"));
    bounce.cancel($("#addProduct"));
}
// creator: sy 2023-09-11   判断获取的值是否为空
function handleNull(str,keyd){
    if(keyd == 1){
        var result = str == null || str == undefined || str == 'null' || '' ? '':str;
    }else{
        var result = str == null || str == undefined || str == 'null' || '' ? '--':str;
    }
    return result;
}
// creator: sy 2023-09-26   给上传按钮存储上产品id
function changeup7(){
    var proid =  $("#cpUplond-7").data("proid");
    $("#cpUplond-7 #file_upload_1-button").data("id",proid);
}
function changeup8(){
    var proid = $("#cpUplond-8").data("proid");
    $("#cpUplond-8 #file_upload_1-button").data("id",proid);
}
// creator: sy 2023-09-26   给更换按钮存储上产品id
function changedown3(){
    var proid = $("#cpUplond-3").data("proid");
    $("#cpUplond-3 #file_upload_1-button").data("id",proid);
}
function changedown4(){
    var proid = $("#cpUplond-4").data("proid");
    $("#cpUplond-4 #file_upload_1-button").data("id",proid);
}
// creator: sy 2023-09-27   移除产品下的图纸
function removedettle(keyed){
    switch (keyed) {
        case 0://移除产品图纸
            var product = $("#retuem3").data("proid");//产品id
            var category = 1;
            break;
        case 1://移除控制点图片
            var product = $("#retuem4").data("proid");//控制点序号图
            var category = 2;
            break;
    }
    $.ajax({
        url:"../product/removePdBaseImage.do",
        data:{
            //Integer product              //产品id
            // Integer category        //  类型：1-产品图纸,2-控制点序号图
            product:product,
            category:category
        },
        success:function(responsed){
            var kress = responsed.success;
            if(kress == 1){
                switch (keyed) {
                    case 0:
                        $(".uppic1").hide();
                        $(".pic1").show();
                        $(".swsc1").hide();
                        $(".yyc1").show();
                        $("#cpUplond-7").show();
                        break;
                    case 1:
                        $(".uppic2").hide();
                        $(".pic2").show();
                        $(".swsc2").hide();
                        $(".yyc2").show();
                        $("#cpUplond-8").show();
                        break;
                }
            }
        }
    })
}
// creator: sy 2023-09-07   点击‘关联’弹窗中的‘确定’按钮保存数据
function entrycomple(){
    let khchose = $(".customerList").val();//是不是只要获取value就可以知道这个筛选框是否选择了数据了呢
    let zschose = $(".zsGoodsList").val();
    let tychose = $("#cmGoods").val();
    //现在瞧着是客户和专属必须必选
    if(khchose !== ""){//选择了客户，未选择专属
        if(zschose == ""){
            layer.msg("请选择专属商品！");
            return false;
        }
    }
    if(zschose !== ""){
        if(khchose == ""){
            layer.msg("请选择客户！");
            return false;
        }
    }
    if(khchose == "" && zschose == "" && tychose == ""){//三个都未选择
        bounce_Fixed.cancel($("#catasoiation"));
        bounce.show($("#addProduct"));
        return false;
    }

    // $("#lookcatbook").data("generol",2);
    // var list1 = $("#entrover").data("middlist");
    // $("#addPurover").data("chosen",list1);
    let generalbox = $("#entrover").data("general");//用于存储选择的通用型商品数据
    //需要手动存储‘请选择通用型商品’中选择的商品、‘请选择客户’和‘请选择专属商品’中选择的数据
    //请选择通用型商品
    var jsongen = {
        id:""
    };
    var newlist = [];
    let cmIdo = $("#cmGoods").val();
    jsongen.id = cmIdo;
    if(jsongen.id == ""){
        generalbox = [];
    }else{
        generalbox = [];
        generalbox.push(jsongen);
        $.each(generalbox,function(i,el){
            if($.inArray(el,newlist) === -1) newlist.push(el);
        })
    }
    generalbox = newlist;
    // $("#lookcatbook").data("general",generalbox);//用于存储选择的通用型商品数据
    if(generalbox.length > 0){
        let strk = `已`;
        $(".merchandise").html(strk);
    }
    var lank2 = {};
    let cmId = $("#cmGoods").val();
    if (cmId && cmId !== "") {
        lank2.tYProductId = $("#cmGoods").val();
    }else{
        lank2.tYProductId = "";
    }
    var zslankall = $("#entrover").data("spciaprose");//获取所有的专属商品数据,现在是全部的数据了
    if(zslankall == undefined){//没有专属数据
    }else{//有专属数据
        var boxzs = [];
        //数组中每个json数据竟然都是字符串的样子，该怎么修改下呢
        zslankall.forEach(itemzs => {
            var united = itemzs.unit;
            if(united == undefined){
                itemzs = JSON.parse(itemzs);
                boxzs.push(itemzs);
            }else{
                boxzs.push(itemzs);
            }
        })
        var middentl = [];
        $.each(boxzs,function(i,el){
            if($.inArray(el,middentl) === -1) middentl.push(el);
        });
        boxzs = middentl;
        if(boxzs.length == 0){}else{
            zslankall = boxzs;
        }
        var zsGoods = [];
        $(".zsCase").each(function (){//这样获取选择的客户及专属型商品数据是能全面获取的
            let khId = $(this).find(".customerList").val();//获取选择的客户的value数据
            let zsId = $(this).find(".zsGoodsList").val();//获取专属商品数据
            // let cuName = $(".customerList option:selected").text();//客户名称
            // $("#ddlregtype").find("option:selected").text();
            let cuName = $(this).find(".customerList").find("option:selected").text();//客户名称
            let outerName = $(this).find(".zsGoodsList").find("option:selected").text();//专属数据
            if (zsId && zsId !== "") {
                let index = zsGoods.findIndex(value => Number(value.productId) === Number(zsId))
                // if (index === -1) zsGoods.push({"productId":zsId,"name":cuName});
                if (index === -1) zsGoods.push({"productId":zsId,"name":cuName,"outerName":outerName,"khId":khId});
            }
        })
        zsGoods.forEach(itemna => {
            var unid = itemna.productId;
            unid = Number(unid);
            zslankall.forEach(itemmg => {
                if (unid == itemmg.id) {
                    itemmg.name = itemna.name;
                }
            })
            //需要将zslankall中重复的数据删除掉
            // var array = [{id:1,name:2}, {id:1,name:2}, {id:2,name:3}];
            var zsResult = [];
            for(var i =0; i<zslankall.length; i++){
                zslankall[i] = JSON.stringify(zslankall[i]);//对象转成字符串的方法
                zsResult.push(zslankall[i]);//重新填装成一个《字符串》组成的数组
            }
            zslankall = [];
            zsResult = $.unique(zsResult);
            for(var j=0;j<zsResult.length;j++){
                zsResult[j] = JSON.parse(zsResult[j]);// 重新给字符串转换成对象
                zslankall.push(zsResult[j]);//重新填装成一个《对象》组成的数组
            }
        })
        // $("#lookcatbook").data("spciaprose",zslankall);//全部的数据
        let lengtd = zsGoods.length;
        $(".exclsieoucts").html(lengtd);
        lank2.zsProductList = JSON.stringify(zsGoods);//这里的肯定是旧的数据
        $("#addPurover").data("chosen",lank2);
        $("#association").data("msgbox",lank2);
        // $("#lookcatbook").data("chosen",zsGoods);
    }
    // let keyed = $("#entrover").data("keyed");
    // switch (keyed) {
    //     case 1:
    //         $("#association").data("keyed",2);
    //         break;
    //     case 2:
    //         break;
    // }
    $("#association").data("keyed",2);
    var allin = $("#zsGoods").children().clone(true);//clone用于克隆某个div及div 中的数据
    $("#association").data("changemeg",allin);
    bounce_Fixed.cancel($("#catasoiation"));
    bounce.show($("#addProduct"));
}
// creator: sy 2023-09-22   查看产品信息
function getprosucce(produid){
    $.ajax({
        url:"../product/getPdBaseOne.do",
        data:{
            pdBaseId:produid
        },
        success:function(resdata){
            var messglist = resdata.data;
            $(".haveloonk2").data("list",messglist);
            var cptzImage = resdata.data.cptzImage;
            if(cptzImage == null){//代表产品未设置产品图纸
                $(".pic1").show();
                $(".swsc1").show();
                $(".yyc1").hide();
                $(".uppic1").hide();
                $("#cpUplond-7").show();
            }else{//代表产品设置了产品图纸
                $(".uppic1").show();
                $(".pic1").hide();
                $("#propiclook").hide();
                $("#propiclookhas").show();
                $(".propiclook").data("getproper",cptzImage);//获取之前设置的产品图纸数据
            }
            var xhImage = resdata.data.xhImage;
            if(xhImage == null){//代表产品未设置顺序号图片
                $(".pic2").show();
                $(".uppic2").hide();
                $(".swsc2").show();
                $(".yyc2").hide();
                $("#cpUplond-8").show();
            }else{//代表产品设置了顺序号图片
                $(".pic2").hide();
                $(".uppic2").show();
                $("#propiclook2").hide();
                $("#propiclookhas2").show();
                $(".propiclook2").data("getponpro",xhImage);
            }
            var resourceList = resdata.data.resourceList;
            var idbox = [];//存储的文件的Id
            var cklist = [];//存储关联文件内容的数组
            resourceList.forEach(item => {
                var jsonid = {id:""};
                jsonid.id = item.resource;
                idbox.push(jsonid);//idbox是个数组
                cklist.push(item);
            })
            $(".haveloonk2").data("idbox",idbox);
            $(".haveloonk2").data("cklank",cklist);
            if(resourceList == null){
                $(".protest").html(0);
            }else{//该产品设置了技术文件
                $(".protest").html(resourceList.length);
                $(".haveloonk2").data("allmessg",resourceList);//将获取到的所有数据都存储起来
            }

        }
    })
}
// creator: sy 2023-08-01   点击“查看”展示产品关联列表
// function lookcatbook(){
//     var looklist1 = {};
//     $("#productionFrom [need]").each(function(){
//         var name = $(this).attr('name');
//         var key = $(this).val();
//         looklist1[name] = key;
//     })
//     var strk1 = ``;
//     strk1 += `
//         <tbody>
//             <tr>
//                 <td style="width: 100px;">产品图号</td>
//                 <td style="width: 170px;">产品名称/规格/型号</td>
//                 <td style="width: 100px;">计量单位</td>
//                 <td style="width: 100px;">产品单重</td>
//                 <td style="width: 100px;">重量单位</td>
//                 <td style="width: 105px;">来源</td>
//                 <td style="width: 105px;">构成</td>
//                 <td style="width: 194px;">创建人</td>
//             </tr>
//             <tr>
//                 <td>${looklist1.innerSn}</td>
//                 <td>${looklist1.name}/${looklist1.specifications}/${looklist1.model}</td>
//                 <td>${looklist1.unit}</td>
//                 <td>--</td>
//                 <td>--</td>
//                 <td>--</td>
//                 <td>--</td>
//                 <td>--</td>
//             </tr>
//         </tbody>`;
//     $("#procatbook").html(strk1);
//     var offerlink = $("#lookcatbook").data("generol");//0  ""
//     var keyoter = $("#lookcatbook").data("keyoter");
//     //为什么offerlink=0时就走offerlink=""的情况，反之就走=0的呢，
//     if(offerlink == 2){
//         var listmodall = $(".haveloonk").data("genalmodel");//全部通用数据
//         let listmod = $("#lookcatbook").data("general");//用于存储选择的通用型商品数据
//         var custommesg = $("#lookcatbook").data("spciaprose");//获取全部的客户数据
//         var chosecustmg =  $("#lookcatbook").data("chosen");//获取选择的专用型商品数据
//         //custommesg会出现重复的数据,需要删除重复数据
//         var midall = [];
//         if(custommesg == undefined || custommesg.length == 0){}else{
//             let uniqueArr = Array.from(new Set(custommesg.map(JSON.stringify))).map(JSON.parse);
//             custommesg = uniqueArr;
//         }
//         // if(custommesg == undefined || custommesg.length == 0){}
//         // else if(custommesg !== undefined || custommesg.length !== undefined || custommesg.length !== 0){
//         //     var new_arr = [];
//         //     custommesg.forEach(itemall => {
//         //         if(new_arr.length == 0){
//         //             new_arr.push(itemall);
//         //         }else{
//         //             for(var k = 0; k < new_arr.length; k ++){
//         //                 if(new_arr[k].id == itemall.id){
//         //                     new_arr.splice(k,1);
//         //                 }else{
//         //                     new_arr.push(itemall);
//         //                 }
//         //             }
//         //         }
//         //     })
//         //     // custommesg = new_arr;
//         // }
//         // $(".pronumber").html(listmod.length);
//         var allmsg = [];
//         var str1 = ``;//用于存储专属数据
//         var str2 = ``;//用于存储通用数据
//
//         // if(listmodall == undefined || listmodall.length == 0){//一个通用型数据都没有
//         //     if(listmod == undefined || listmod.length == 0){//没有选择通用型数据
//         //         if(custommesg == undefined || custommesg.length == 0){//一个专用型数据都没有
//         //             if(chosecustmg == undefined || chosecustmg.length == 0){//没有选择专用型商品数据
//         //                 str1 +=`
//         //                 <tbody>
//         //                     <tr>
//         //                         <td>商品图号</td>
//         //                         <td>商品名称/规格/型号</td>
//         //                         <td>计量单位</td>
//         //                         <td>所属客户</td>
//         //                         <td>创建人</td>
//         //                         <td>关联操作人</td>
//         //                         <td>操作</td>
//         //                     </tr>
//         //                 </tbody>`;
//         //             }
//         //         }else{//库里至少有一个专用型商品
//         //             if(chosecustmg && chosecustmg.length > 0){//至少选了一个专用型商品
//         //                 str1 += `
//         //                 <tbody>
//         //                     <tr>
//         //                         <td>商品图号</td>
//         //                         <td>商品名称/规格/型号</td>
//         //                         <td>计量单位</td>
//         //                         <td>所属客户</td>
//         //                         <td>创建人</td>
//         //                         <td>关联操作人</td>
//         //                          <td>操作</td>
//         //                     </tr>
//         //             `;
//         //                 custommesg.forEach(itemzy => {
//         //                     chosecustmg.forEach(itemche => {
//         //                         itemche.productId = Number(itemche.productId);
//         //                         if(itemche.productId == itemzy.id){
//         //                             var answer2 = undefined;
//         //                             allmsg.push(itemzy);
//         //                             str1 += `
//         //                             <tr>
//         //                                 <td>${handleNull(itemzy.outerSn)}</td>
//         //                                 <td>${handleNull(itemzy.outerName)}/${handleNull(itemzy.specifications)}/${handleNull(itemzy.model)}</td>
//         //                                 <td>${handleNull(itemzy.unit)}</td>
//         //                                 <td>${handleNull(itemzy.name)}</td>
//         //                                 <td>${handleNull(answer2)}</td>
//         //                                 <td>${handleNull(answer2)}</td>
//         //                                 <td>
//         //                                      <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this))">移除</span>
//         //                                      <span class="hd">${JSON.stringify(itemzy)}</span>
//         //                                 </td>
//         //                             </tr>
//         //                         </tbody>`;
//         //                         }
//         //                     })
//         //                 })
//         //             }
//         //         }
//         //     }
//         // }else{//库里至少有一个通用型商品
//         //     // var addmesslist = $("#lookcatbook").data("addpeiple");
//         //     // addmesslist.forEach(itemad => {
//         //     //
//         //     // })
//         //     var answer = undefined;
//         //     var allchosebox = [];
//         //     if(listmodall && listmodall.length > 0){//库里至少有一个通用型商品
//         //         if(listmod && listmod.length > 0){//至少选择了一个通用型商品
//         //             str1 += `
//         //                 <tbody>
//         //                     <tr>
//         //                         <td>商品图号</td>
//         //                         <td>商品名称/规格/型号</td>
//         //                         <td>计量单位</td>
//         //                         <td>所属客户</td>
//         //                         <td>创建人</td>
//         //                         <td>关联操作人</td>
//         //                          <td>操作</td>
//         //                     </tr>
//         //             `;
//         //             listmodall.forEach(itemall => {
//         //                 listmod.forEach(item1 => {
//         //                     item1.id = Number(item1.id);
//         //                     if(itemall.id == item1.id){
//         //                         allchosebox.push(itemall);
//         //                         allmsg.push(itemall);
//         //                         str1 += `
//         //                         <tr>
//         //                             <td>${handleNull(itemall.outerSn)}</td>
//         //                             <td>${handleNull(itemall.outerName)}/${handleNull(itemall.specifications)}/${handleNull(itemall.model)}</td>
//         //                             <td>${handleNull(itemall.unit)}</td>
//         //                             <td>${handleNull(answer)}</td>
//         //                             <td>${handleNull(answer)}</td>
//         //                             <td>${handleNull(answer)}</td>
//         //                             <td>
//         //                                 <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this))">移除</span>
//         //                                 <span class="hd">${JSON.stringify(itemall)}</span>
//         //                             </td>
//         //                         </tr>
//         //                     `;
//         //                     }
//         //                 })
//         //             })
//         //             if(custommesg == undefined || custommesg.length == 0){//一个专用型数据都没有
//         //                 if(chosecustmg == undefined || chosecustmg.length == 0){//没有选择专用型商品数据
//         //                     str1 += `</tbody>`;
//         //                 }
//         //             }else {//库里至少有一个专用型商品
//         //                 if(chosecustmg && chosecustmg.length > 0){//至少选了一个专用型商品
//         //                     custommesg.forEach(itemzy => {
//         //                         chosecustmg.forEach(itemche => {
//         //                             itemche.productId = Number(itemche.productId);
//         //                             if(itemche.productId == itemzy.id){
//         //                                 allchosebox.push(itemzy);
//         //                                 var answer2 = undefined;
//         //                                 allmsg.push(itemzy);
//         //                                 str1 += `
//         //                             <tr>
//         //                                 <td>${handleNull(itemzy.outerSn)}</td>
//         //                                 <td>${handleNull(itemzy.outerName)}/${handleNull(itemzy.specifications)}/${handleNull(itemzy.model)}</td>
//         //                                 <td>${handleNull(itemzy.unit)}</td>
//         //                                 <td>${handleNull(itemzy.name)}</td>
//         //                                 <td>${handleNull(answer2)}</td>
//         //                                 <td>${handleNull(answer2)}</td>
//         //                                 <td>
//         //                                      <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this))">移除</span>
//         //                                      <span class="hd">${JSON.stringify(itemzy)}</span>
//         //                                 </td>
//         //                             </tr></tbody>`;
//         //                             }
//         //                         })
//         //                     })
//         //                 }
//         //             }
//         //         }else{//未选择通用型商品
//         //             if(custommesg == undefined || custommesg.length == 0){//一个专用型数据都没有
//         //                 if(chosecustmg == undefined || chosecustmg.length == 0){//没有选择专用型商品数据
//         //                     str1 +=`
//         //                 <tbody>
//         //                     <tr>
//         //                         <td>商品图号</td>
//         //                         <td>商品名称/规格/型号</td>
//         //                         <td>计量单位</td>
//         //                         <td>所属客户</td>
//         //                         <td>创建人</td>
//         //                         <td>关联操作人</td>
//         //                         <td>操作</td>
//         //                     </tr>
//         //                 </tbody>`;
//         //                 }
//         //             }else{//库里至少有一个专用型商品
//         //                 if(chosecustmg && chosecustmg.length > 0){//至少选了一个专用型商品
//         //                     str1 += `
//         //                 <tbody>
//         //                     <tr>
//         //                         <td>商品图号</td>
//         //                         <td>商品名称/规格/型号</td>
//         //                         <td>计量单位</td>
//         //                         <td>所属客户</td>
//         //                         <td>创建人</td>
//         //                         <td>关联操作人</td>
//         //                          <td>操作</td>
//         //                     </tr>
//         //             `;
//         //                     custommesg.forEach(itemzy => {
//         //                         chosecustmg.forEach(itemche => {
//         //                             itemche.productId = Number(itemche.productId);
//         //                             if(itemche.productId == itemzy.id){
//         //                                 var answer2 = undefined;
//         //                                 allmsg.push(itemzy);
//         //                                 str1 += `
//         //                             <tr>
//         //                                 <td>${handleNull(itemzy.outerSn)}</td>
//         //                                 <td>${handleNull(itemzy.outerName)}/${handleNull(itemzy.specifications)}/${handleNull(itemzy.model)}</td>
//         //                                 <td>${handleNull(itemzy.unit)}</td>
//         //                                 <td>${handleNull(itemzy.name)}</td>
//         //                                 <td>${handleNull(answer2)}</td>
//         //                                 <td>${handleNull(answer2)}</td>
//         //                                 <td>
//         //                                      <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this))">移除</span>
//         //                                      <span class="hd">${JSON.stringify(itemzy)}</span>
//         //                                 </td>
//         //                             </tr>
//         //                         </tbody>`;
//         //                             }
//         //                         })
//         //                     })
//         //                 }
//         //             }
//         //         }
//         //     }
//         // }
//         // $("#viewrecords #prosoiaion").html(str1);
//         if(listmodall == undefined || listmodall.length == 0){//一个通用型数据都没有
//             if(listmod == undefined || listmod.length == 0) {//没有选择通用型数据
//                 str2 +=`
//                     <tbody>
//                         <tr>
//                             <td style="width: 13%;">商品图号</td>
//                             <td style="width: 22.4%;">商品名称/规格/型号</td>
//                             <td style="width: 25.8%;">计量单位</td>
//                             <td>创建人</td>
//                             <td>关联操作人</td>
//                             <td>操作</td>
//                         </tr>
//                     </tbody>
//                 `;
//             }
//         }else{//库里至少有一个通用型商品
//             var answer = undefined;
//             var allchosebox = [];
//             if(listmodall && listmodall.length > 0) {//库里至少有一个通用型商品
//                 if(listmod && listmod.length > 0) {//至少选择了一个通用型商品
//                     str2 += `
//                         <tbody>
//                             <tr>
//                                <td>商品图号</td>
//                                 <td>商品名称/规格/型号</td>
//                                 <td style="width: 26%;">计量单位</td>
//                                 <td>创建人</td>
//                                 <td>关联操作人</td>
//                                 <td>操作</td>
//                             </tr>
//                     `;
//                     listmodall.forEach(itemall => {
//                         listmod.forEach(item1 => {
//                             item1.id = Number(item1.id);
//                             if(itemall.id == item1.id){
//                                 allchosebox.push(itemall);
//                                 // allmsg.push(itemall);
//                                 str2 += `
//                                     <tr>
//                                         <td>${handleNull(itemall.outerSn)}</td>
//                                         <td>${handleNull(itemall.outerName)}/${handleNull(itemall.specifications)}/${handleNull(itemall.model)}</td>
//                                         <td>${handleNull(itemall.unit)}</td>
//                                         <td>${handleNull(answer)}</td>
//                                         <td>${handleNull(answer)}</td>
//                                         <td>
//                                             <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this),2)">移除</span>
//                                             <span class="hd">${JSON.stringify(itemall)}</span>
//                                         </td>
//                                     </tr>
//                                 </tbody>
//                                 `
//                             }
//                         })
//                     })
//                 }
//             }
//             $("#viewrecords #tytblen").html(str2);
//         }
//         if(custommesg == undefined || custommesg.length == 0) {//一个专用型数据都没有
//             if (chosecustmg == undefined || chosecustmg.length == 0) {//没有选择专用型商品数据
//                 str1 += `
//                      <tbody>
//                         <tr>
//                             <td>商品图号</td>
//                             <td>商品名称/规格/型号</td>
//                             <td>计量单位</td>
//                             <td>所属客户</td>
//                             <td>创建人</td>
//                             <td>关联操作人</td>
//                             <td>操作</td>
//                         </tr>
//                      </tbody>
//                 `;
//             }
//         }else {//库里至少有一个专用型商品
//             if (chosecustmg && chosecustmg.length > 0) {//至少选了一个专用型商品
//                 str1 += `
//                         <tbody>
//                             <tr>
//                                  <td>商品图号</td>
//                                 <td>商品名称/规格/型号</td>
//                                 <td>计量单位</td>
//                                 <td>所属客户</td>
//                                 <td>创建人</td>
//                                 <td>关联操作人</td>
//                                 <td>操作</td>
//                             </tr>
//                     `;
//                 custommesg.forEach(itemzy => {
//                     chosecustmg.forEach(itemche => {
//                         itemche.productId = Number(itemche.productId);
//                         if (itemche.productId == itemzy.id) {
//                             var answer2 = undefined;
//                             allchosebox.push(itemzy);
//                             allmsg.push(itemzy);
//                             str1 += `
//                                      <tr>
//                                         <td>${handleNull(itemzy.outerSn)}</td>
//                                         <td>${handleNull(itemzy.outerName)}/${handleNull(itemzy.specifications)}/${handleNull(itemzy.model)}</td>
//                                         <td>${handleNull(itemzy.unit)}</td>
//                                         <td>${handleNull(itemzy.name)}</td>
//                                         <td>${handleNull(answer2)}</td>
//                                         <td>${handleNull(answer2)}</td>
//                                         <td>
//                                             <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this),1)">移除</span>
//                                             <span class="hd">${JSON.stringify(itemzy)}</span>
//                                         </td>
//                                      </tr>
//                                 </tbody>
//                                 `;
//                         }
//                     })
//                 })
//             }
//             $("#viewrecords #prosoiaion").html(str1);
//         }
//         $(".pronumber").html(allmsg.length);
//         //     if(custommesg == undefined || custommesg.length == 0){//一个专用型商品都没有
//         //             var str1 = `
//         //             <tbody>
//         //                     <tr>
//         //                         <td>商品图号</td>
//         //                         <td>商品名称/规格/型号</td>
//         //                         <td>计量单位</td>
//         //                         <td>所属客户</td>
//         //                         <td>创建人</td>
//         //                         <td>关联操作人</td>
//         //                         <td>操作</td>
//         //                     </tr>
//         //             </tbody>
//         //         `;
//         //             $("#viewrecords #prosoiaion").html(str1);
//         //             bounce.cancel($("#addProduct"));
//         //             bounce_Fixed.show($("#viewrecords"));
//         //             $(".pronumber").html(0);
//         //             return false;
//         //         }else{//库里有至少一个专用型商品
//         //             if(chosecustmg == undefined || chosecustmg.length == 0){//没有选择专用型商品
//         //                 var str1 = `
//         //             <tbody>
//         //                     <tr>
//         //                         <td>商品图号</td>
//         //                         <td>商品名称/规格/型号</td>
//         //                         <td>计量单位</td>
//         //                         <td>所属客户</td>
//         //                         <td>创建人</td>
//         //                         <td>关联操作人</td>
//         //                         <td>操作</td>
//         //                     </tr>
//         //             </tbody>
//         //         `;
//         //                 $("#viewrecords #prosoiaion").html(str1);
//         //                 bounce.cancel($("#addProduct"));
//         //                 $(".pronumber").html(0);
//         //                 bounce_Fixed.show($("#viewrecords"));
//         //                 return false;
//         //             }else{//选择了专用型商品
//         //
//         //             }
//         //         }
//         // else{
//         //     if(listmod == undefined || listmod.length == 0){
//         //         var str1 = `
//         //         <tbody>
//         //                 <tr>
//         //                     <td>商品图号</td>
//         //                     <td>商品名称/规格/型号</td>
//         //                     <td>计量单位</td>
//         //                     <td>所属客户</td>
//         //                     <td>创建人</td>
//         //                     <td>关联操作人</td>
//         //                     <td>操作</td>
//         //                 </tr>
//         //         </tbody>
//         //     `;
//         //         $("#viewrecords #prosoiaion").html(str1);
//         //         bounce.cancel($("#addProduct"));
//         //         $(".pronumber").html(0);
//         //         bounce_Fixed.show($("#viewrecords"));
//         //         return false;
//         //     }else{
//         //         var str2 = ``;
//         //         var answer = undefined;
//         //         var allchosebox = [];
//         //         if(listmodall && listmodall.length > 0){
//         //             listmodall.forEach(itemall => {
//         //                 listmod.forEach(item1 => {
//         //                     item1.id = Number(item1.id);
//         //                     if(itemall.id == item1.id){
//         //                         allchosebox.push(itemall);
//         //                         str2 += `
//         //                         <tbody>
//         //                         <tr>
//         //                             <td>商品图号</td>
//         //                             <td>商品名称/规格/型号</td>
//         //                             <td>计量单位</td>
//         //                             <td>所属客户</td>
//         //                             <td>创建人</td>
//         //                             <td>关联操作人</td>
//         //                             <td>操作</td>
//         //                         </tr>
//         //                         <tr>
//         //                             <td>${handleNull(itemall.outerSn)}</td>
//         //                             <td>${handleNull(itemall.outerName)}/${handleNull(itemall.specifications)}/${handleNull(itemall.model)}</td>
//         //                             <td>${handleNull(itemall.unit)}</td>
//         //                             <td>${handleNull(answer)}</td>
//         //                             <td>${handleNull(answer)}</td>
//         //                             <!--<td>${handleNull(itemall.createName)} &nbsp;${handleNull(new Date(itemall.createDate).format('yyyy-MM-dd hh:mm:ss'))}</td>-->
//         //                             <td>${handleNull(answer)}</td>
//         //                             <td>
//         //                                 <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this))">移除</span>
//         //                                 <span class="hd">${JSON.stringify(itemall)}</span>
//         //                             </td>
//         //                         </tr>
//         //                         </tbody>`;
//         //                         $("#viewrecords #prosoiaion").html(str2);
//         //                     }
//         //                 })
//         //             })
//         //         }
//         //     }
//         // }
//         $("#dettenct").data("chose",allchosebox);
//         $("#coseendwn").data("closed",allchosebox);
//     }
//     else  if(offerlink == 1 || offerlink.length == 0) {//全部数据都移除干净了
//         $(".pronumber").html(0);
//         var str1 = `
//              <tbody>
//                 <tr>
//                     <td>商品图号</td>
//                     <td>商品名称/规格/型号</td>
//                     <td style="width: 26%;">计量单位</td>
//                     <td>创建人</td>
//                     <td>关联操作人</td>
//                     <td>操作</td>
//                 </tr>
//              </tbody>
//         `;
//         var str2 = `
//             <tbody>
//                 <tr>
//                      <td>商品图号</td>
//                     <td>商品名称/规格/型号</td>
//                     <td>计量单位</td>
//                     <td>所属客户</td>
//                     <td>创建人</td>
//                     <td>关联操作人</td>
//                     <td>操作</td>
//                 </tr>
//             </tbody>
//         `;
//         $("#viewrecords #prosoiaion").html(str2);
//         $("#viewrecords #tytblen").html(str1);
//     } else{//移除过一次的数据
//         var strm1 = ``;
//         var strm2 = ``;
//         var zsbox = [];
//         if(keyoter.length == 0){//只有通用型商品
//             strm1 += `
//                     <tbody>
//                         <tr>
//                             <td style="width: 13.5%;">商品图号</td>
//                             <td style="width: 23.4%;">商品名称/规格/型号</td>
//                             <td style="width: 26.8%;">计量单位</td>
//                             <td style="width: 11.5%;">创建人</td>
//                             <td style="width: 15.5%;">关联操作人</td>
//                             <td>操作</td>
//                         </tr>
//                 `;
//         }else{
//             strm2 += `
//                     <tbody>
//                         <tr>
//                              <td>商品图号</td>
//                             <td>商品名称/规格/型号</td>
//                             <td>计量单位</td>
//                             <td>所属客户</td>
//                             <td>创建人</td>
//                             <td>关联操作人</td>
//                             <td>操作</td>
//                         </tr>
//                     `;
//         }
//         offerlink.forEach(itemzy => {
//             var answer2 = undefined;
//             var json = {name:""};
//             if(itemzy.name == undefined || itemzy.name == "") {//代表这条数据是通用
//                 strm1 += `
//                      <tr>
//                             <td>${handleNull(itemzy.outerSn)}</td>
//                             <td>${handleNull(itemzy.outerName)}/${handleNull(itemzy.specifications)}/${handleNull(itemzy.model)}</td>
//                             <td>${handleNull(itemzy.unit)}</td>
//                             <td>${handleNull(answer2)}</td>
//                             <td>${handleNull(answer2)}</td>
//                             <td>
//                                 <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this),2)">移除</span>
//                                 <span class="hd">${JSON.stringify(itemzy)}</span>
//                             </td>
//                      </tr>
//                 </tbody>
//                 `;
//             }else{//代表这条数据是专属
//                 json.name = itemzy.name;
//                 zsbox.push(json.name);
//                 strm2 += `
//             <tr>
//                  <td>${handleNull(itemzy.outerSn)}</td>
//                 <td>${handleNull(itemzy.outerName)}/${handleNull(itemzy.specifications)}/${handleNull(itemzy.model)}</td>
//                 <td>${handleNull(itemzy.unit)}</td>
//                 <td>${handleNull(itemzy.name,1)}</td>
//                 <td>${handleNull(answer2)}</td>
//                 <td>${handleNull(answer2)}</td>
//                 <td>
//                  <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this))">移除</span>
//                  <span class="hd">${JSON.stringify(itemzy)}</span>
//                 </td>
//             </tr>
//     </tbody>
//     `;
//             }
//         })
//         strm2 += `</tbody>`;
//         if(keyoter.length == 0){
//             if(offerlink.length == 0){
//                 $("#viewrecords #tytblen").html("");
//                 $("#viewrecords #prosoiaion").html("");
//             }else{
//                 $("#viewrecords #tytblen").html(strm1);
//             }
//         }else{
//             if(zsbox.length == 0){
//                 $("#viewrecords #prosoiaion").html("");
//             }else{
//                 $("#viewrecords #prosoiaion").html(strm2);
//             }
//         }
//         $(".pronumber").html(keyoter.length);
//         $("#dettenct").data("chose",offerlink);
//         $("#coseendwn").data("close",offerlink);
//         // $("#association").data("offerbox",offerlink);
//     }
//     bounce.cancel($("#addProduct"));
//     bounce_Fixed.show($("#viewrecords"));
// }

// creator: sy 2023-09-15   移除关联的通用型商品数据
// function movedetten(obje,keyd){
//     let list = $("#dettenct").data("chose");
//     let lioe = obje.siblings(".hd").html();
//     var nonen = "";
//     lioe = JSON.parse(lioe);
//     list.forEach(function(item,index){
//         if(lioe.id == item.id){
//             list.splice(index,1);
//         }
//         if(item.name == undefined || item.name == ""){
//             nonen = 1;
//         }
//     })
//     var lengtd = list.length;
//     if(lengtd == 0){
//         let strk = `未`;
//         $(".merchandise").html(strk);
//         // $("#lookcatbook").data("generol",1);//关联的数据都清空了
//         $("#cmGoods").val("");
//         $(".exclsieoucts").html(0);
//         $("#association").data("ycoffer",list);//全部移除后的数据
//         bounce_Fixed.cancel($("#viewrecords"));
//         bounce.show($("#addProduct"));
//     }else{
//         var strm1 = ``;
//         var strm2 = ``;
//         var zsbox = [];
//         switch (keyd) {
//             case 1://专属
//                 strm1 += `
//                     <tbody>
//                         <tr>
//                             <td>商品图号</td>
//                             <td>商品名称/规格/型号</td>
//                             <td>计量单位</td>
//                             <td>所属客户</td>
//                             <td>创建人</td>
//                             <td>关联操作人</td>
//                             <td>操作</td>
//                         </tr>
//                 `;
//                 break;
//             case 2://通用
//                 strm2 += `
//                  <tbody>
//                         <tr>
//                             <td>商品图号</td>
//                             <td>商品名称/规格/型号</td>
//                             <td style="width: 26%;">计量单位</td>
//                             <td>创建人</td>
//                             <td>关联操作人</td>
//                             <td>操作</td>
//                         </tr>
//                 `;
//                 break;
//         }
//         list.forEach(function(itemzy,index){
//             var answer2 = undefined;
//             var json = {name:""};
//             if(itemzy.name == undefined || itemzy.name == ""){//代表这条数据是通用
//                 strm2 += `
//                     <tr>
//                         <td>${handleNull(itemzy.outerSn)}</td>
//                         <td>${handleNull(itemzy.outerName)}/${handleNull(itemzy.specifications)}/${handleNull(itemzy.model)}</td>
//                         <td>${handleNull(itemzy.unit)}</td>
//                         <td>${handleNull(answer2)}</td>
//                         <td>${handleNull(answer2)}</td>
//                         <td>
//                             <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this),2)">移除</span>
//                             <span class="hd">${JSON.stringify(itemzy)}</span>
//                         </td>
//                     </tr>
//                 </tbody>
//                 `;
//             }else{//代表这条数据是专属
//                 json.name = itemzy.name;
//                 zsbox.push(json);
//                 strm1 += `
//                      <tr>
//                         <td>${handleNull(itemzy.outerSn)}</td>
//                         <td>${handleNull(itemzy.outerName)}/${handleNull(itemzy.specifications)}/${handleNull(itemzy.model)}</td>
//                         <td>${handleNull(itemzy.unit)}</td>
//                         <td>${handleNull(itemzy.name)}</td>
//                         <td>${handleNull(answer2)}</td>
//                         <td>${handleNull(answer2)}</td>
//                         <td>
//                             <span class="ty-color-blue" id="dettenct" onclick="movedetten($(this),1)">移除</span>
//                             <span class="hd">${JSON.stringify(itemzy)}</span>
//                         </td>
//                      </tr>
//                 </tbody>
//                 `
//             }
//         })
//         strm1 += `</tbody>`;
//         switch (keyd) {
//             case 1:
//                 $("#viewrecords #prosoiaion").html(strm1);
//                 var londeg = zsbox.length;
//                 $(".pronumber").html(londeg);
//                 if(londeg == 0){
//                     $("#viewrecords #prosoiaion").html("");
//                 }
//                 $(".exclsieoucts").html(londeg);
//                 break;
//             case 2:
//                 $("#viewrecords #tytblen").html(strm2);
//                 break;
//         }
//         // if(nonen == 1){
//         //     $(".exclsieoucts").html(lengtd);
//         // }
//
//         //这会儿的list是移除之后的数据
//         // $("#lookcatbook").data('generol',list);
//         // $("#lookcatbook").data("keyoter",zsbox);
//         $("#dettenct").data("chose",list);
//         $("#coseendwn").data("closed",list);//移除之后的数据
//         bounce_Fixed.show($("#viewrecords"));
//     }
// }

// creator: sy 2023-09-11   获取点击‘产品录入’时是模式1还是模式2
//generalModel：通用模式:1-模式1(默认),2-模式2
//dedicatedModel：专用模式:1-模式1(默认),2-模式2
function getMonthnum(){
    $.ajax({
        "url":"../popedom/getCurrentItem.do",
        "data":{"code":'commodityProduct'},
        success:function(res){
            let modelry = res.data.pdModelSettings;
            let generalModel = modelry.generalModel;//通用模式
            let dedicatedModel = modelry.dedicatedModel;//专用模式
            //思路：产品录入弹窗中，当专用模式的值为2时，$(".podutsition")隐藏
            switch (dedicatedModel) {
                case 1:
                    $(".podutsition").show();
                    break;
                case 2:
                    $(".podutsition").hide();
                    break;
            }
            let jsonbox = {
                generalmodel:generalModel,
                exclusivemode:dedicatedModel
            };
            let genmodle = jsonbox.generalmodel;
            switch (genmodle) {
                case 1:
                    $("#generlist").show();
                    break;
                case 2:
                    $("#generlist").hide();
                    break;
            }
            // $("#association").data("model",jsonbox);
        }
    })
}
// creator: sy 2023-09-07   点击’增加文件‘上传文件数据
function addproduct(type){
    if(type === 1){
        // $("#chooseSaveFolder").width(1050);
        $("#chooseSaveFolder").width(1310);
        $("#chooseSaveFolder .mar").show();
    }else{
        $("#chooseSaveFolder").width(600);
        $("#chooseSaveFolder .mar").hide();
    }
    //会不会是哪里写错了呢？所以获取不到…………
    bounce_Fixed.show($("#chooseSaveFolder"));
    getFirstDoc();
    $("#fileSort").hide();
    $("#chooseSaveFolder .ty-fileList").html("");
    $("#ye_con").html("");
    setEveryTime(bounce_Fixed, 'chooseSaveFolder');
    bounce.cancel($("#addProduct"));
    $("#sureChooseFolderBtn").data("name","sureChooseFolder");
}



//-------------------------下面是复制的添加系统文件相关方法----------
// ------------------------ 从文件与资料中引入 ---------------------//
// creator: 侯杏哲 2017-12-04  获取一级文件夹列表
function getFirstDoc(){
    var url = "../res/getInitialFolder.do" ;
    // if( isGeneral || isSuper){ // 超管或总务
    //     url = "../res/getInitialFolderByManage.do" ;
    // }
    $.ajax({
        url: url ,
        data: { "type": 1  },
        success: function (res) {
            var data = res["data"] ,
                listFirstCategory = data["listFirstFolder"],
                authFolder = data["authFolder"],    //  0是没有权限 1是有权限
                firstLevelStr = "";
            firstLevelStr += '<div class="level1" level="1">';
            if(listFirstCategory.length === 0){
                $("#fileUploadBtn").attr("class","ty-btn ty-btn-big ty-btn-gray ty-circle-3");
                $(".ty-colFileTree[data-name='main']").html('<div class="null"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无数据</p></div>');
                return false
            }
            for(var i in listFirstCategory){
                if(Number(i) === listFirstCategory.length - 1 && (isGeneral || isSuper)){
                    //最后一个值为回收站，有特殊图标(讨论区此处不能选择，所以不展示）
                    firstLevelStr += ''
                }else{
                    if(listFirstCategory[i]["childStatus"] === "1"){ //  1 - 下有子文件夹，没有值代表没有子文件夹
                        firstLevelStr += '<li><div class="ty-treeItem" title="'+ listFirstCategory[i]["name"] +'" data-id='+listFirstCategory[i]["id"]+'>' +
                            '<i class="fa fa-angle-right"></i>' +
                            '<i class="fa fa-folder"></i>' +
                            '<span>'+listFirstCategory[i]["name"]+'</span></div></li>'
                    }else{
                        firstLevelStr += '<li><div class="ty-treeItem" title="'+ listFirstCategory[i]["name"] +'" data-id='+listFirstCategory[i]["id"]+'>' +
                            '<i class="ty-fa"></i>' +
                            '<i class="fa fa-folder"></i>' +
                            '<span>'+ listFirstCategory[i]["name"] +'</span></div></li>'
                    }
                }
            }
            firstLevelStr += '<p id="tstip"></p>'+
                '</div>';
            $(".ty-colFileTree[data-name='main']").html(firstLevelStr);
        }
    });
}
// creator: 侯杏哲,2017-12-04 渲染获取的文件夹数据和页面 ( 适用于主页面文件夹树和移动文件弹框的文件夹树 )
function showTreeData(data , treeItemThis) {
    // data - 获取的数据 ， treeItemThis - 点击的那个文件夹对象 ， treeObj - 点击的树对象

    // 处理子文件夹数据
    var listNotice      = data["parentFolder"] || [];
    var listNoticeChild = data["childFolder"];
    var level = parseInt(treeItemThis.parent().parent().attr("level")) ;
    var nextLevel = level + 1 ;
    var levelStr = "";
    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">' ;
    for(var i in listNoticeChild){
        if(listNoticeChild[i]["childStatus"] === '1'){ // 1时代表此文件夹下有子文件夹
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"]+'</span>' +
                '</div></li>'
        }else{
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"] + '</span>' +
                '</div></li>'
        }
    }
    levelStr += '</div>';
    localStorage.setItem("noticemax",listNotice["childStatus"] ); // childStatus : 1 是有文件夹; 2 是有文件; null标识什么都没有

    treeItemThis.attr("child", listNoticeChild.length > 0)
    if (listNoticeChild.length > 0) {
        if (treeItemThis.next().length === 0) {
            treeItemThis.after(levelStr);
        }
    }

}
/* creator : 侯杏哲 2017-12-16 判断当前是否为总务 */
function chargeZW(testID , arr) {
    var isZW = false ;
    if(arr && arr.length > 0){
        for(var i = 0 ; i < arr.length ; i++){
            var id = arr[i]["userID"] ;
            if( id == testID){ isZW = true ;  }
        }
    }
    return isZW ;
}


// creator: 张旭博，2018-04-24 10:17:16，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo, hasChoose) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        if ($(".ty-fileContent").is(":visible")) {
            $("#ye_con").show() ;
            $("#fileSort").show() ;
        }
        for(var i = 0; i < fileInfo.length; i++){
            var id          = fileInfo[i].id,
                file        = fileInfo[i].file,
                category    = fileInfo[i].category,
                name        = fileInfo[i].name,
                size        = fileInfo[i].size,
                path        = fileInfo[i].path,
                fileSn      = fileInfo[i].fileSn,
                changeNum   = fileInfo[i].changeNum,
                fileType    = fileInfo[i].version,
                operation   = fileInfo[i].operation,
                updateName  = fileInfo[i].updateName,
                createName  = fileInfo[i].createName,
                updateDate  = fileInfo[i].updateDate,
                createDate  = fileInfo[i].createDate,
                updator     = fileInfo[i].updator,
                creator     = fileInfo[i].creator,
                version     = fileInfo[i].version.toLowerCase(),
                loginUser   = $("#loginUser").html(),
                loginUserId = 0,
                applyDisabledClass = '',    //定义换版申请禁用class
                recordDisabledClass = '',   //定义换版记录禁用class
                handleStr = '',             //定义操作按钮部分字符串
                recycleId = Number($("#listDoc").data("recycleId")); //回收站id



            //获取当前userId
            loginUser   = JSON.parse(loginUser);
            loginUserId = loginUser.userID;

            //格式化数据
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';

            updateName  === null || updateName  == undefined? updateName = createName:updateName;   //更新人为空时 赋值 创建人，最后统一取更新时间 ， 下同
            updateDate  === null || updateDate  == undefined? updateDate = createDate:updateDate;
            if (!updator) {updator = creator}

            operation   === '3' ? applyDisabledClass  = 'ty-disabled': applyDisabledClass  = ''; //换版申请后的禁用（换版申请后  换版和移动按钮被禁用）
            changeNum   === 0   ? recordDisabledClass = 'ty-disabled': recordDisabledClass = ''; //换版记录的禁用 （换版为0 换版记录按钮禁用）

            let funcName = {
                seeOnline:  '在线预览',
                download:   '下载',
                basicMsg:   '基本信息',
                recovery:   '还原',
                delete:     '删除',
                move:       '移动',
                more:       '更多',
                changeVersion:      '换版',
                changeVersionApply: '换版申请',
                changeVersionRecord:'换版记录',
                scanSet:            '使用权限设置',
                changeFileName:     '修改文件名称',
                changeFileNo:       '修改文件编号',
                disable:            '禁用',
                relateSheet:        '相关表格',
                sheetRelate:        '表格关联',
                sheetRelateHandleRecord: '表格关联的操作记录',
                sheetRelateRemove:  '解除关联'
            }

            let func = {}, moreFunc = {}

            func = {
                seeOnline: { path: path },
                download: { path: path, download: name + '.' + fileType },
                basicMsg:{},
                changeVersionRecord:{},
                // sheetRelateRemove: {}
            }
            for (let key in func) {
                handleStr += '<a class="aBtn" type="btn" name="'+key+'"'
                let attr = func[key]
                for (var item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</a>'
            }
            handleStr += '<ul class="hel" style="display: none">'
            for (let key in moreFunc) {
                handleStr += '<li class="liBtn" type="btn" name="'+key+'"'
                let attr = moreFunc[key]
                for (let item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</li>'
            }
            handleStr += '</ul>'

            var chooseStr = ''
            if (hasChoose) {
                chooseStr =     '   <div class="ty-radio">' +
                    '       <input type="radio" id="resource_choose_'+id+'" name="resource_choose">' +
                    '       <label for="resource_choose_'+id+'"></label>' +
                    '   </div>'
            }
            //单条文件代码字符串拼接
            fileListStr +=  '<div class="ty-fileItem fileItemRadioChoose" id="'+id+'" pid="'+category+'" data-id="'+id+'">'+ chooseStr +
                '   <div class="ty-fileType ty-file_'+fileType+' sized"></div>'+
                '   <div class="ty-fileInfo sizeo oterme">'+
                '      <div class="ty-fileRow">'+
                '          <div class="ty-fileName sizenm" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                '          <div class="ty-fileNo sizeno"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                '          <div class="ty-fileVersion sizesi" changeNum="'+ changeNum +'">'+'G'+changeNum+'</div>'+
                '      </div>'+
                '      <div class="ty-fileRow">'+
                '          <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate + '</div>'+
                '          <div class="ty-fileHandle sizehd">'+ handleStr + '</div>'+
                '      </div>'+
                '   </div>'+
                '   <div class="ty-fileInfo sizeote" style="display: none;">' +
                '       <div class="ty-fileRow">' +
                '          <div style="margin-left: auto;">选择：'+ handleNull(fileInfo[i].createName) + ' &nbsp;&nbsp; ' + handleNull(new Date(fileInfo[i].createTime).format("yyyy-MM-dd hh:mm:ss"))  + '</div>'+
                '       </div>'+
                '       <div class="ty-fileRow">' +
                '          <a onclick="overconact($(this))" id="dettenadd" class="aBtn">解除关联</a>'+
                '       </div>'+
                '   </div>'+
                '   <div class="hd">'+JSON.stringify(fileInfo[i])+'</div>'+
                '</div>';
        }
    }
    //返回文件列表字符串
    return fileListStr;
}
// updater: 张旭博，2018-04-20 10:30:37，设置文件列表
function getFile(currentPageNo , pageSize , categoryId){
    var url = "../res/getFile.do" ;
    if(isGeneral || isSuper){
        url = "../res/getFileByManager.do" ;
    }
    var type = $("#fileSort").data('type')
    if(!type){
        type = 1;
    }
    // type - "1"代表获取的是按时间降序的列表，“2”代表获取的是时间升序的列表，“3”代表获取的是文件编号升序的列表，“4”代表的是获取文件编号降序的列表
    var data = {
        "categoryId" : categoryId,
        "currentPageNo" : currentPageNo,
        "pageSize" : pageSize,
        "type" : type
    }
    $.ajax({
        url: url ,
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"],
                jsonStr = JSON.stringify( { "categoryId" : categoryId} ) ;

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo, true);
            if($(".ty-searchContent").is(':visible')){
                setPage( $("#ye-search-file"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                $(".ty-searchContent .fileContent").find('.searchFile').html(fileListStr).attr("type","folder");
            }else{
                setPage( $("#ye_con"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                $(".ty-fileList").html(fileListStr);
                $(".ty-fileList").attr("type","folder");
            }
        }
    })
}
// creator: 张旭博，2019-04-03 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 归档页面的验证
            case 'fileFile':
                if ($("#fileFile .active").length === 0 || $("#fileFile input:radio[name='fileFor']:checked").length === 0) {
                    $("#fileFileBtn").prop("disabled", true)
                } else {
                    $("#fileFileBtn").prop("disabled", false)
                }
                break;
            // 归档选择文件后文件上传部分的验证
            case 'fileUpload':

                var state = 0

                var isNeedOther = $("#fileUpload input:radio:checked").val()
                if (isNeedOther === '1' && $("#fileUpload .chooseApprover").val() === ''){
                    state++
                }
                $("#fileUpload input[require]").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })

                if ($("#fileUpload").data("fileInfo") === '') {
                    state ++
                }

                if ( state > 0 || $("#fileUpload .savePlace").html() === '') {
                    $("#sureUploadNewFileBtn").prop("disabled",true)
                }else {
                    $("#sureUploadNewFileBtn").prop("disabled",false)
                }
                break;
            // 归档选择文件后文件换版部分的验证
            case 'changeVersion':
                var state = 0
                var isNeedOther = $("#fileUpload input:radio:checked").val()
                if (isNeedOther === '1' && $("#fileUpload .chooseApprover").val() === ''){
                    state++
                }
                if ($("#fileUpload").data("fileInfo") === '') {
                    state ++
                }
                if ( state > 0 ) {
                    $("#sureUploadNewFileBtn").prop("disabled",true)
                }else {
                    $("#sureUploadNewFileBtn").prop("disabled",false)
                }
                break
            // 文件上传/文件换版 选择文件位置验证
            case 'chooseSaveFolder':
                let isDis = true
                if ($(".mar").is(":visible")) {
                    if($("input[name='resource_choose']:checked").length > 0 && $("#chooseSaveFolder .ty-colFileTree .ty-treeItemActive").length > 0){
                        isDis = false
                    }
                    //creator:sy 尝试给选中的复选框的父级做属性的增加和删除
                    $("input[name='resource_choose']:checked").each(function () {
                        $(this).parent("div").addClass("choseting");
                    })
                    $("input[name='resource_choose']:not(:checked)").each(function () {
                        $(this).parent("div").removeClass("choseting");
                    })
                } else {
                    if($("#chooseSaveFolder .ty-colFileTree .ty-treeItemActive").length > 0){
                        isDis = false
                    }
                }
                $("#sureChooseFolderBtn").prop("disabled",isDis );

                break;
        }

    });
}
// creator: 张旭博，2019-06-19 14:02:04，文件功能 - 基本信息
function basicMessage(obj){
    var pid=obj.parents(".ty-fileItem").data("id");
    var loginId = sphdSocket.user.userID;
    $("#docInfoScan").data("currentFileId",pid);

    var url = '../res/getFileMessage.do'

    var history = $("#resHistory").is(":visible") // 是否是换版记录中的文件
    if (history) {
        url = "../res/getUpFileMes.do"
    }
    $.ajax({
        url: url,
        data: {"id" :pid},
        success: function (res) {
            var state = res["success"] ;
            if(state !== 1){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list    = res["data"].resource, // 文件信息
                    arr     = res["data"].listUser, // 总务的列表
                    approvalProcess  = res["data"].listAp, // 审批流程
                    listUser  = res["data"].listUser, // 审批流程
                    size        = list.size,
                    reason      = list.reason,
                    view_num    = list.viewNum,         // 浏览次数
                    download_num= list.downloadNum,     // 下载次数
                    move_num    = list.moveNum,         // 移动次数
                    change_num  = list.changeNum,       // 换版次数
                    upName      = res["data"].upName,   // 文件名称修改次数
                    upFileSn    = res["data"].upFileSn, // 文件编号修改次数
                    categoryName= res["data"].categoryName;

                typeof (reason) === "undefined" ? reason = "--":reason;
                size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size = parseFloat(size/1048576).toFixed(2) + 'MB';

                $("#info_title").html(list["name"]);

                // left
                $("#info_sn").html(list["fileSn"]);
                $("#info_category").html(categoryName);
                $("#info_size").html(size);
                $("#info_version").html( ( list["version"] || "未知") );
                $("#info_content").html(list["content"]);


                // right
                var creator = '<span class="info_createName info_name">'+list["createName"]+'</span> <span class="info_createDate">'+list["createDate"]+'</span>'
                var updater = change_num === 0?'--':'<span class="info_updateName info_name">'+list["updateName"]+'</span> <span class="info_updateDate">'+list["updateDate"]+'</span>'
                $("#info_gn").html("G" + change_num);
                $("#info_creator").html(creator);
                $("#info_updater").html(updater);
                if (isGeneral) {
                    var resSignedMes  = res["data"].resSignedMes // 签收信息
                    $("#docInfoScan .generalPart").show()
                    $("#docInfoScan .sign_num").html(resSignedMes?(resSignedMes.haveSignedNum + '/' + resSignedMes.signedNum):'--/--');
                    $("#docInfoScan").data("docInfo", {
                        resHisId: resSignedMes.resHisId,
                        fileName: list.name,
                        fileSn: list.fileSn,
                        changeNum: list.changeNum,
                        creator: list.changeNum > 0?('换版人：' + list.updateName+' ' + list.updateDate):('创建人：' + list.createName+' ' + list.createDate)
                    })
                } else {
                    $("#docInfoScan .generalPart").hide()
                }

                var approveStr = '<div class="trItem"><span class="ttl">申请人：</span><span class="con" style="max-width: 280px"><span class="info_name">' + approvalProcess[0].userName + '</span><span>' + formatTime(approvalProcess[0].createDate, true) +'</span></div>'
                // 审批流程
                for (var i = 0; i < approvalProcess.length; i++) {
                    var name = ''
                    if ( i === approvalProcess.length - 1) {
                        name = '文 管'
                    } else {
                        name = '审批人'
                    }
                    approveStr += '<div class="trItem"><span class="ttl">' + name + '：</span><span class="con" style="max-width: 280px"><span class="info_name">' + approvalProcess[i].toUserName + '</span><span>' + formatTime(approvalProcess[i].handleTime, true) +'</span></div>'
                }
                $("#docInfoScan .processList").html(approveStr)
                if( change_num > 0){ // 换过版
                    $("#docInfoScan .info_content .ttl").html("换版原因：")
                }else{
                    $("#docInfoScan .info_content .ttl").html("说明：")
                }

                var userLimit = chargeRole('超管') || chargeRole('小超管') || chargeRole('总务') || chargeZW(loginId , arr)

                view_num !== 0 && userLimit      ? $("#viewNumBtn").prop("disabled",false)    :$("#viewNumBtn").prop("disabled",true);
                download_num !== 0 && userLimit  ? $("#downloadNumBtn").prop("disabled",false):$("#downloadNumBtn").prop("disabled",true);

                move_num === 0      ? $("#moveNumBtn").prop("disabled",true)        :$("#moveNumBtn").prop("disabled",false);
                upName === 0        ? $("#changeNameNumBtn").prop("disabled",true)  :$("#changeNameNumBtn").prop("disabled",false);
                upFileSn === 0      ? $("#changeNoNumBtn").prop("disabled",true)    :$("#changeNoNumBtn").prop("disabled",false);
                $("#docInfoScan .censusInfo .view_num").html(view_num);
                $("#docInfoScan .censusInfo .download_num").html(download_num);
                $("#docInfoScan .censusInfo .move_num").html(move_num);
                $("#docInfoScan .censusInfo .name_num").html(upName);
                $("#docInfoScan .censusInfo .no_num").html(upFileSn);

                if (history) {
                    $("#docInfoScan .censusInfo").hide()
                } else {
                    $("#docInfoScan .censusInfo").show()
                }
                bounce.show($("#docInfoScan")) ;
                bounce_Fixed.cancel($("#addfilelook"));
            }
        }
    })
}
// creator: 张旭博，2021-07-21 09:07:57，文件功能 - 换版记录
function chargeRecord(obj){
    $("#resHistory").show().siblings().hide();
    $("#btn-group").hide()
    $("#resHistory").data("fileId", obj.parents(".ty-fileItem").data("id"));
    getRecord(1, 20)
}
// creator: 张旭博，2018-05-24 11:04:53，文件功能 - 预览
function fileSeeOnline(selector) {
    let isChargeRecord = $("#resHistory").is(":visible")
    let isSheetPage = $("#page_sheetRelate").is(":visible")
    if(!isChargeRecord && !isSheetPage){
        // 获取文件信息(后台要求的，不知道为啥)
        getFileInfo(selector);
        //记录点击次数
        var currentFileId = selector.parents(".ty-fileItem").data("id");
        $.ajax({
            url:"../res/viewAndDownloadRecord.do",
            data:{
                "id"  : currentFileId,
                "type": 1
            },
            success: function(data){
                if(data["success"] !== 1){
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }

    seeOnline(selector)
}
/* creator：张旭博，2017-05-08 16:30:58，文件预览 */
var customVersion = ['doc','docx','xls','xlsx','zip','rar','apk','ipa','ppt','pptx','txt','pdf','png','jpg','wps','et','md']
function seeOnline(obj) {
    console.log(obj)
    if(!(obj instanceof jQuery)){
        obj = $(obj)
    }
    var path = obj.attr("path");
    var pathArr = path.split(".");

    var type = pathArr[pathArr.length - 1].toLowerCase() ;
    switch (type){
        case 'doc':case 'docx':
        case 'xls':case 'xlsx':
        case 'ppt':case 'pptx':
            obj.attr('target','_blank').attr('href','https://view.officeapps.live.com/op/view.aspx?src='+$.fileUrl+path);
            break;
        case 'png':case 'jpg':case 'jpeg':case 'gif':
        case "pdf":
            obj.attr('target','_blank').attr('href',$.fileUrl+path);
            break;
        case "md":
            obj.attr('target','_blank').attr('href',$.webRoot+'/assets/md/index.html?src='+$.fileUrl+path);
            break;
        default:
            // case "rar":case "zip":
            // case "pdf": // wyu：ow365有文件大小(5M)限制，改为直接用浏览器预览
            // case 'et':
            // case "txt":
            obj.attr('target','_blank').attr('href',$.ow365url+path);
    }
}
// creator: 张旭博，2018-05-24 11:05:09，文件功能 - 下载
function fileDownload(selector) {
    //记录点击次数
    if (!$("#resHistory").is(":visible") && !$("#page_sheetRelate").is(":visible")) {
        var currentFileId = selector.parents(".ty-fileItem").data("id");
        $.ajax({
            url:"../res/viewAndDownloadRecord.do",
            data:{
                "id"  : currentFileId,
                "type": 2
            },
            success: function(data){
                if(data["success"] !== 1){
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }
    getDownLoad(selector)
}
// creator: 张旭博，2018-07-05 14:28:42，获取文件详情
function getFileInfo(obj){
    var fileId = obj.parents(".ty-fileItem").data("id");
    $.ajax({
        url: "../res/getFileMessage.do",
        data: {"id" : fileId},
        type: "post",
        dataType: "json",
        beforeSend:function(){  } ,
        success: function (data) {
            var status = data["success"] ;
            if(status != 1){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }
            // else{
            //     bounce.show($("#docInfoScan"));
            // }
        },
        error:function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){  }
    });
}
// creator: 张旭博，2018-05-16 16:29:28，查看各种操作记录
function seeHandelRecordBtn(type){
    //打开弹窗
    bounce_Fixed.show($("#fileHandleRecord"));

    //更改弹窗标题
    var recordTitleName = '';
    switch (type){
        case 1:
            recordTitleName = '浏览记录';
            break;
        case 2:
            recordTitleName = '下载记录';
            break;
        case 5:
            recordTitleName = '移动记录';
            break;
        case 6:
            recordTitleName = '文件名称修改记录';
            break;
        case 7:
            recordTitleName = '文件编号修改记录';
            break;
    }
    $("#fileHandleRecord").find(".recordTitleName").html(recordTitleName);

    //渲染操作记录表格
    setFileHandelRecord(type);
}
// creator: 张旭博，2018-05-16 16:46:20，获取各种操作记录
function setFileHandelRecord(type) {
    var currentFileId = $("#docInfoScan").data("currentFileId"),
        tableStr = '';
    switch (type){
        case 1:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>浏览人</td>'+
                '<td>浏览时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 2:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>下载人</td>'+
                '<td>下载时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 5:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>移动前</td>'+
                '<td>移动后</td>'+
                '<td>移动人</td>'+
                '<td>移动时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 6:
        case 7:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>修改前</td>'+
                '<td>修改后</td>'+
                '<td>修改人</td>'+
                '<td>修改时间</td>'+
                '</tr>'+
                '</thead>';
            break;
    }
    if(type === 1||type === 2){
        $.ajax({
            url:"../res/getRecordByShow.do",
            data:{
                "id"  : currentFileId,
                "type": type
            },
            success: function(data){
                if(data["success"] === 1){
                    data = data["data"];
                    tableStr += '<tbody>';
                    for(var i = 0; i < data.length; i++){
                        tableStr += '<tr>' +
                            '<td style="width: 10%">' + (i + 1) +'</td>'+
                            '<td style="width: 45%">' + data[i].createName + '</td>'+
                            '<td style="width: 45%">' + data[i].createDate.substring(0,19) + '</td>'+
                            '</tr>'
                    }
                    tableStr += '</tbody>';
                    $("#fileHandleRecord .recordTable").html(tableStr)
                }else{
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }else{
        $.ajax({
            url:"../res/getRecordByUpAndMove.do",
            data:{
                "fileId"  : currentFileId,
                "operation": type
            },
            success: function(data){
                if(data["success"] === 1){
                    data = data["data"];
                    tableStr += '<tbody>';
                    for(var i = 0; i < data.length; i++){
                        tableStr += '<tr>' +
                            '<td style="width: 7%">' + (i + 1) +'</td>'+
                            '<td style="width: 30%">' + data[i].nameBefore + '</td>'+
                            '<td style="width: 30%">' + data[i].nameAfter + '</td>'+
                            '<td style="width: 15%">' + data[i].createName + '</td>'+
                            '<td style="width: 18%">' + data[i].createDate.substring(0,19) + '</td>'+
                            '</tr>'
                    }
                    tableStr += '</tbody>';
                    $("#fileHandleRecord .recordTable").html(tableStr)
                }else{
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }
}

//updator:王静 2017-08-03 15:38:23 获取换版后的历史文件列表
function getRecord(currentPageNo, totalPage){
    //得到地址栏传过来的ID
    var fileId = $("#resHistory").data("fileId");
    var data = {
        "fileId": fileId,
        "currentPageNo": currentPageNo,
        "pageSize": totalPage
    }
    $.ajax({
        // url: "getUpdateFile.do",
        url: "../res/getUpdateFile.do",
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"],
                jsonStr = JSON.stringify( { "fileId" : fileId} ) ;

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo);
            setPage( $("#ye_record"), currentPageNo, totalPage, "changeRecord", jsonStr) ;
            $("#resHistory .fileList").html(fileListStr)
            //---------sy添加的部分--------
            bounce_Fixed2.show($("#resHistory"));
            //----------------------------------
        }
    })
}
//--------------------------------------------------------------------------


// creator: sy 2023-08-01   点击“查看”展示文件记录
function vieilrcrds(){
    var vierlist1 = {};
    $("#productionFrom [need]").each(function(){//获取手动添加的产品信息
        var name = $(this).attr('name');
        var key = $(this).val();
        vierlist1[name] = key;
    })
    var weightUnit = vierlist1.weightUnit;
    var source = vierlist1.source;
    var composition = vierlist1.composition;
    switch (weightUnit) {
        case "1"://毫克
            var weightunit = "毫克";
            break;
        case "2"://克
            var weightunit = "克";
            break;
        case "3"://千克
            var weightunit = "千克";
            break;
        case "4"://吨
            var weightunit = "吨";
            break;
    }
    switch (source) {
        case "1"://外购
            var sorce = "外购";
            break;
        case "2"://自制
            var sorce = "自制";
            break;
    }
    switch (composition) {
        case "1"://购买
            var comp = "购买";
            break;
        case "2"://制造
            var comp = "制造";
            break;
        case "3"://装配
            var comp = "装配";
            break;
    }
    var strvi1 = ``;
    strvi1 += `
        <tbody>
            <tr>
                <td style="width: 100px;">产品图号</td>
                <td style="width: 170px;">产品名称/规格/型号</td>
                <td style="width: 100px;">计量单位</td>
                <td style="width: 100px;">产品单重</td>
                <td style="width: 100px;">重量单位</td>
                <td style="width: 105px;">来源</td>
                <td style="width: 105px;">构成</td>
                <td style="width: 194px;">创建人</td>
            </tr>
            <tr>
                <td>${handleNull(vierlist1.innerSn)}</td>
                <td>${handleNull(vierlist1.name)}/${handleNull(vierlist1.specifications)}/${handleNull(vierlist1.model)}</td>
                <td>${handleNull(vierlist1.unit)}</td>
                <td>${handleNull(vierlist1.netWeight)}</td>
                <td>${handleNull(weightunit)}</td>
                <td>${handleNull(sorce)}</td>
                <td>${handleNull(comp)}</td>
                 <td>${handleNull(vierlist1.createName)} ${handleNull(new Date(vierlist1.createDate).format('yyyy-MM-dd hh:mm:ss'))}</td>
            </tr>
       </tbody>`;
    $("#procatloken").html(strvi1);
    var prochose = $(".haveloonk").data("projson");
    if(prochose == undefined || prochose.length == 0){
        var othenr = 0;
        $(".pronemd").html(othenr);
        layer.msg('未关联文件');
        return false;
    }else{
        $(".pronemd").html(prochose.length);
    }
    // prochose.forEach(itempro => {
    //     itempro.createTime = itempro.createDate;
    // })
    //prochose是个数组
    var str1 =``;
    str1 = getFileListStr(prochose);
    $("#addfilelook .addProductForm").children("p").next().next().next().html(str1);//为什么存入数组中的数据都是新增的那一条呢？明明选择的是其余两条呀
    $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeote").show();
    $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeo").removeClass("oterme");
    var numb = $(".sizesi").html();
    if(numb == "G0"){
        $(".sizehd a[name='changeVersionRecord']").addClass("ty-disabled");
    }else{//numb是除去G0外的情况
        $(".sizehd a[name='changeVersionRecord']").removeClass("ty-disabled");
    }
    $("#dettenadd").data("projsond",prochose);
    $("#dettenadd").data("keyen","");
    //给div设定data属性得确保该div已经渲染在页面上了，否则data的值会设定失败
    bounce.cancel($("#addProduct"));
    bounce_Fixed.show($("#addfilelook"));
    $("#closeoff").data("keyd","");
    $("#keepsure").data("defiit","");//代表是产品录入
}
// creator: sy 2023-08-01   点击“解除关联”展示弹窗
function overconact(jsond){
    var bunk = $("#dettenadd").data("keyen");
    if(bunk == "taklook"){
        var lank = jsond.parent().parent().next().html();//’解除关联‘所在那行的数据
        lank = JSON.parse(lank);
        // var list = $("#dettenadd").data("projsond");//一共的数据
        $("#keepsure").data("defiit","prolook");//prolook代表是产品查看
        $("#keepsure").data("choosen",lank);//选中的那条数据
        var allmegs = $("#dettenadd").data("allbox");
        $("#keepsure").data("allbox",allmegs);
        bounce_Fixed2.show($("#relasecntact"));
        bounce.show($("#seeProduct"));
    }else{
        var lank = jsond.parent().parent().next().html();//’解除关联‘所在那行的数据
        lank = JSON.parse(lank);
        var list = $("#dettenadd").data("projsond");//一共的数据
        //是不是不该在这里删除呢？？？？？？
        bounce_Fixed.cancel($("#addfilelook"));
        bounce_Fixed2.show($("#relasecntact"));
        $("#keepsure").data("messge",list);//一共的数据
        $("#keepsure").data("chose",lank);//选择的数据
    }
}
// creator: sy 2023-09-19   解除关联确定按钮
function madesure(){
    var difotter = $("#keepsure").data("defiit");//用于区分是产品查看还是产品录入
    if(difotter == "prolook"){//产品查看
        var chooen = $("#keepsure").data("choosen");//选中的数据
        var alllank = $("#keepsure").data("allbox");
        var resourceid = "";
        // alllank.forEach(function(itema,index){
        //     var choseid = chooen.id;
        //     var allid = itema.resource;
        //     if(choseid == allid){
        //         resourceid = itema.resource;
        //         alllank.splice(index,1);//这个位置为什么只走到第二条就不走了？第三条重复时也不删除了？
        //     }
        // })
        var choseid = chooen.id;//选中的文件的id
        let len = alllank.length;
        while(len--){
            if(alllank[len].resource === choseid){
                resourceid = alllank[len].resource;
                alllank.splice(len,1);
            }
        }
        //let arr = [
        //   { id: 1, sex: "1" },
        //   { id: 2, sex: "2" },
        //   { id: 3, sex: "1" },
        //   { id: 4, sex: "1" },
        //   { id: 5, sex: "2" },
        //   { id: 6, sex: "1" },
        //   { id: 7, sex: "2" },
        // ];
        //
        // for (let i = arr.length - 1; i >= 0; i--) {
        //   if (arr[i].sex === "1") arr.splice(i, 1);
        // }
        // console.log(arr);//[ { id: 2, sex: '2' }, { id: 5, sex: '2' }, { id: 7, sex: '2' } ]
        //let arr = [1,2,2,3,4]
        // let len = arr.length
        // while(len--){
        //  if(arr[len] === 2) arr.splice(len,1)
        // }
        // console.log(arr) // [1,3,4]
        //let arr = [1,2,2,3,4]
        // let len = arr.length
        // for(let i = len; i >= 0; i--){
        //   if(arr[i] === 2) arr.splice(i,1)
        // }
        // console.log(arr) // [1,3,4]

        var proid = $("#dettenadd").data("proid");
        $.ajax({
            url:"../product/securePdResource.do",
            data:{
                //Integer product         //产品id
                // Integer resourceId        //资源文件resource
                product:proid,
                resourceId:resourceid
            },
            success:function(res){
                if(res.success == 1){
                    var indexed = alllank.length;
                    if(indexed == 0){
                        $(".pronemd").html(0);
                        bounce_Fixed.cancel($("#addfilelook"));
                        bounce_Fixed2.cancel($("#relasecntact"));
                        var nuall = 0;
                        haveloonk2(nuall);
                    }else{
                        bounce_Fixed2.cancel($("#relasecntact"));
                        bounce_Fixed.show($("#addfilelook"));
                        var json = {
                            id:resourceid
                        };
                        haveloonk2(json,chooen);
                    }
                }else{
                    layer.msg("关联关系不存在，或已解除关联");
                }
            }
        })
    }else{//产品录入
        var list = $("#keepsure").data("messge");//一共的数据
        var chosen = $("#keepsure").data("chose");//选择的数据
        var chosenid = chosen.id;
        let len = list.length;
        while(len--){//去除数组中重复的数据
            if(list[len].id == chosenid){
                this.cesort = list[len];
                list.splice(len,1);
            }
        }
        // for(let i in list){
        //     if(chosen.id == list[i].id){
        //         this.cesort = list[i];
        //         list.splice(i,1);//删除后的全部数据
        //     }
        // }
        var meggest = list;//meggest是删除后剩下的数据
        if(meggest.length == 0){
            $(".messgnend").html("");
            $(".pronemd").html(0);
            $(".pronumb").html(0);
            bounce_Fixed.cancel($("#addfilelook"));
            bounce_Fixed2.cancel($("#relasecntact"));
            bounce.show($("#addProduct"));
            return false;
        }else{
            $(".pronemd").html(meggest.length);
            $(".pronumb").html(meggest.length);
            var str2 = ``;
            str2 = getFileListStr(meggest);
            $("#addfilelook .addProductForm").children("p").next().next().next().html(str2);
            $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeote").show();
            $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeo").removeClass("oterme");
            var numb = $(".sizesi").html();
            if(numb == "G0"){
                $(".sizehd a[name='changeVersionRecord']").addClass("ty-disabled");
            }else{//numb是除去G0外的情况
                $(".sizehd a[name='changeVersionRecord']").removeClass("ty-disabled");
            }
        }
        $("#closeoff").data("addprot",meggest);
        $("#addPurover").data("addprot",meggest);
        bounce_Fixed2.cancel($("#relasecntact"));
        bounce_Fixed.show($("#addfilelook"));
    }
}
// creator: sy 2023-07-31   点击“取消”关闭弹窗
function closenwind(kep){
    switch (kep) {
        case 1:
            // $("#association").data("keyed","2-1");

            // var lanks = $("#closewinden").data("msgbox");//现在是旧的数据
            // // $("#association").data("befall",lanks);
            // // var dettmsg= $("#closewinden").data("dettmsg");
            // // $("#association").data("detlist",dettmsg);
            // // var dettmsg = $("#closewinden").data("dettend");
            // // $("#association").data("detlist",dettmsg);
            // if(lanks.tYProductId == ""){//没有通用数据
            //     let strk = `未`;
            //     $(".merchandise").html(strk);
            // }else{//有通用数据
            //     let strk = `已`;
            //     $(".merchandise").html(strk);
            // }
            // var zsbox = lanks.zsProductList;
            // zsbox = JSON.parse(zsbox);
            // if(zsbox.length == 0){
            //     $(".exclsieoucts").html(0);
            // }else{
            //     $(".exclsieoucts").html(zsbox.length);
            // }
            bounce_Fixed.cancel($("#catasoiation"));
            bounce.show($("#addProduct"));
            break;
        // case 2:
        //     // var box = $("#coseendwn").data("nozsmg");
        //     // $("#association").data("nowlink",box);
        //     // var box2 = $("#coseendwn").data("closed");//移除之后的数据
        //     // if(box2 == undefined){
        //     //     $(".exclsieoucts").html(0);
        //     //     $("#association").data("oren",0);
        //     // }else{
        //     //     var lengtd2 = box2.length;//可能包含通用+专属
        //     //     var n = 1;
        //     //     if(lengtd2 == 0){//没有数据了
        //     //         $(".exclsieoucts").html(0);
        //     //         let strk = `未`;
        //     //         $(".merchandise").html(strk);
        //     //         // $("#lookcatbook").data("generol",1);//关联的数据都清空了
        //     //     }
        //     //     else if(lengtd2-n == 0){//有一条数据，可能有专属，也有可能没有      有种情况，lengtd2=1,但是那条数据正好是专属数据
        //     //         box2.forEach(itemask => {
        //     //             if(itemask.name !== undefined){//那条数据是专属
        //     //                 $(".exclsieoucts").html(lengtd2);
        //     //                 let strk = `未`;
        //     //                 $(".merchandise").html(strk);
        //     //             }else{//那条数据是通用
        //     //                 $(".exclsieoucts").html(0);
        //     //                 $("#association").data('oren',0);
        //     //                 $("#association").data("keyde","noned");
        //     //             }
        //     //         })
        //     //     }else{//有不止一条数据
        //     //         //不止一条数据的时候：
        //     //         //1.有一条通用，至少1条专属；2.没有通用，至少1条专属
        //     //         var keypont = "";
        //     //         box2.forEach(function(itemseh,index) {
        //     //             //是不是只需要判断第一条数据是不是通用就可以了？反正不管第一条是不是通用，
        //     //             //第二条都是专属
        //     //             if(index == 0){//第一条数据
        //     //                 if(itemseh.name == undefined || itemseh.name == ""){//第一条数据是通用
        //     //                     let strk = `已`;
        //     //                     $(".merchandise").html(strk);
        //     //                     $(".exclsieoucts").html(lengtd2-n);
        //     //                     $("#association").data('oren',lengtd2-n);
        //     //                     $("#association").data('tyor',"tyhas");
        //     //                     keypont = 1;
        //     //                 }else{//第一条数据是专属
        //     //                     $(".exclsieoucts").html(lengtd2);
        //     //                     let strk2 = `未`;
        //     //                     $(".merchandise").html(strk2);
        //     //                     keypont = 2;
        //     //                     $("#association").data('tyor',0);
        //     //                 }
        //     //             }else{//第二条数据
        //     //                 switch (keypont) {
        //     //                     case 1://第一条是通用，第二条开始是专属
        //     //                         $(".exclsieoucts").html(lengtd2-n);
        //     //                         break;
        //     //                     case 2:
        //     //                         $(".exclsieoucts").html(lengtd2);
        //     //                         break;
        //     //                 }
        //     //             }
        //     //         })
        //     //     }
        //     //     $("#association").data("ycoffer",box2);//移除后剩下的数据
        //     // }
        //     // $("#association").data("keyed",2);
        //     // bounce_Fixed.cancel($("#viewrecords"));
        //     // bounce.show($("#addProduct"));
        //     break;
        case 3:
            var trune = $("#closeoff").data("keyd");
            if(trune == "looker"){//产品查看弹窗中查看
                bounce_Fixed.cancel($("#addfilelook"));
                bounce.show($("#seeProduct"));
                var things = $("#closeoff").data("poid");
                seeProduct(things,1);
            }else{
                var lank = $("#closeoff").data("addprot");
                $("#addPurover").data("addprot",lank);
                bounce_Fixed.cancel($("#addfilelook"));
                bounce.show($("#addProduct"));
            }
            break;
        case 4:
            bounce_Fixed2.cancel($("#relasecntact"));
            bounce_Fixed.show($("#addfilelook"));
            break
        case 5:
            bounce.show($("#addProduct"));
            bounce_Fixed.cancel($("#deteation"));
            break;
        case 6:
            bounce.show($("#seeProduct"));
            bounce_Fixed.cancel($("#procatlist"));
            break;
    }
}
// creator: sy 2023-08-02   图片上传
function initCUpload(obj,type){
    let fileTypeExtsStr = '';
    let multi = true;
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type == "doc"){
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``;
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:fileTypeExtsStr,
        itemTemplate:itemTemplate,
        multi:multi,
        buttonText:"上传",
        formData:{
            module:'产品档案',
            userId:sphdSocket.user.userID,
            groupUuid:groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            if(type == "img"){
                var imgs = $("#addProduct").data("img") || [];//获取到全部图片
                // var len = imgs.length;
                imgs.push(data);//将新添加的图片存入数组
                $("#addProduct").data("img",imgs);//将新的数组赋值给data("img")，用于在其他地方进行数据获取
                $("#tacklook").data("imgbox",imgs);
                // updateImgs(imgs);//更新所有图片
                $(".upent").show();
                $(".unupent").hide();
                $(".detent").hide();
                $(".oveadd").show();
                $("#cpUplond-1").hide();
                $("#cpUplond-2").show();
            }
        }
    })
}
function initCUpload2(obj,type){
    let fileTypeExtsStr = '';
    let multi = true;
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }
    let itemTemplate = ``;
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:fileTypeExtsStr,
        itemTemplate:itemTemplate,
        multi:multi,
        buttonText:"更换",
        formData:{
            module:'产品档案',
            userId:sphdSocket.user.userID,
            groupUuid:groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            if(type == "img"){
                var imgs = $("#addProduct").data("img") || [];//获取到全部图片
                // var len = imgs.length;
                // imgs = [];
                imgs.push(data);//将新添加的图片存入数组
                $("#addProduct").data("img",imgs);//将新的数组赋值给data("img")，用于在其他地方进行数据获取
                // updateImgs(imgs);//更新所有图片
                $(".upent").show();
                $(".unupent").hide();
                $(".detent").hide();
                $(".oveadd").show();
                $("#cpUplond-1").hide();
                $("#cpUplond-2").show();
            }
        }
    })
}
function initCUpload3(obj,type){
    let fileTypeExtsStr = '';
    let multi = true;
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }
    let itemTemplate = ``;
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:fileTypeExtsStr,
        itemTemplate:itemTemplate,
        multi:multi,
        buttonText:"更换",
        formData:{
            module:'产品查看',
            userId:sphdSocket.user.userID,
            groupUuid:groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            if(type == "img"){
                var imgs = $("#seeProduct").data("img") || [];
                imgs.push(data);
                $("#seeProduct").data("img",imgs);
                $(".propiclook").data("img",imgs);
                $(".propiclook").data("imgbox",imgs);
                var product = $("#cpUplond-3 #file_upload_1-button").data("id");
                var coden = data.lastName;
                switch(coden){
                    case 'jpg'://图片
                        var typekd = 1;
                        break;
                    case 'png'://图片
                        var typekd = 1;
                        break;
                    case 'jpeg'://图片
                        var typekd = 1;
                        break;
                    case 'mp4'://视频
                        var typekd = 2;
                        break;
                    case 'txt'://文件
                        var typekd = 3;
                        break;
                }
                $.ajax({
                    url:"../product/saveOrUpdatePdBaseImage.do",
                    data:{
                        // Integer product         //产品id
                        // Integer category        //类型：1-产品图纸,2-控制点序号图
                        // String  title           //标题
                        // Integer type            //类型:1-图片,2-视频,3-文档
                        // String  uplaodPath      //文件上传路径
                        product:product,
                        category:1,
                        title:data.displayName,
                        type:typekd,
                        uplaodPath:data.filename
                    },
                    success:function(reskey){
                        var success = reskey.success;
                        if(success == 1){
                            $("#propiclookhas1").hide();
                            $("#propiclook1").show();
                            $(".propiclook").data("getproper",imgs);
                        }else{
                            $("#propiclookhas1").show();
                            $("#propiclook1").hide();
                        }
                    }
                })
            }
        }
    })
}
function initCUpload4(obj,type){
    let fileTypeExtsStr = '';
    let multi = true;
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }
    let itemTemplate = ``;
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:fileTypeExtsStr,
        itemTemplate:itemTemplate,
        multi:multi,
        buttonText:"更换",
        formData:{
            module:'产品查看',
            userId:sphdSocket.user.userID,
            groupUuid:groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            if(type == "img"){
                var imgs = $("#seeProduct").data("img3") || [];
                imgs.push(data);
                // imgs = [];
                $("#seeProduct").data("img3",imgs);
                $(".propiclook2").data("imgbox",imgs);
                var product = $("#cpUplond-4 #file_upload_1-button").data("id");
                var coden = data.lastName;
                switch (coden) {
                    case 'jpg'://图片
                        var typekd = 1;
                        break;
                    case 'png'://图片
                        var typekd = 1;
                        break;
                    case 'jpeg'://图片
                        var typekd = 1;
                        break;
                    case 'gif'://图片
                        var typekd = 1;
                        break;
                    case 'mp4'://视频
                        var typekd = 2;
                        break;
                    case 'txt'://文件
                        var typekd = 3;
                        break;
                }
                $.ajax({
                    url:"../product/saveOrUpdatePdBaseImage.do",
                    data: {
                        // Integer product         //产品id
                        // Integer category        //类型：1-产品图纸,2-控制点序号图
                        // String  title           //标题
                        // Integer type            //类型:1-图片,2-视频,3-文档
                        // String  uplaodPath      //文件上传路径
                        product: product,
                        category: 2,
                        title: data.displayName,
                        type: typekd,
                        uplaodPath: data.filename
                    },
                    success:function(reskey){
                        var success = reskey.success;
                        if(success == 1){
                            $("#propiclookhas2").hide();
                            $("#propiclook2").show();
                            $(".propiclook2").data("getponpro",imgs);
                        }else{
                            $("#propiclookhas2").show();
                            $("#propiclook2").hide();
                        }
                    }
                })
            }
        }
    })
}
function initCUpload5(obj,type){
    let fileTypeExtsStr = '';
    let multi = true;
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }
    let itemTemplate = ``;
    obj.html("");
    obj.Huploadify({
        auto: true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText: "上传",
        formData: {
            module: '产品档案',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit: 40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent: false,
        showUploadedSize: false,
        removeTimeout: 99999999,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadSuccess: function (file, data) {
            data = JSON.parse(data);
            if (type == "img") {
                var imgs = $("#addProduct").data("img2") || [];//获取到全部图片
                // imgs = [];
                imgs.push(data);//将新添加的图片存入数组
                $("#addProduct").data("img2", imgs);//将新的数组赋值给data("img")，用于在其他地方进行数据获取
                $("#tacklook2").data("imgbox",imgs);
                $(".upent2").show();
                $(".unupent2").hide();
                $(".detent2").hide();
                $(".oveadd2").show();
                $("#cpUplond-5").hide();
                $("#cpUplond-6").show();
            }
        }
    })
}
function initCUpload6(obj,type){
    let fileTypeExtsStr = '';
    let multi = true;
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }
    let itemTemplate = ``;
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:fileTypeExtsStr,
        itemTemplate:itemTemplate,
        multi:multi,
        buttonText:"更换",
        formData:{
            module:'产品档案',
            userId:sphdSocket.user.userID,
            groupUuid:groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            if(type == "img"){
                var imgs = $("#addProduct").data("img2") || [];//获取到全部图片
                // imgs = [];
                imgs.push(data);//将新添加的图片存入数组
                $("#addProduct").data("img2",imgs);//将新的数组赋值给data("img")，用于在其他地方进行数据获取
                $("#tacklook2").data("imgbox",imgs);
                $(".upent").show();
                $(".unupent").hide();
                $(".detent").hide();
                $(".oveadd").show();
                $("#cpUplond-5").hide();
            }
        }
    })
}
function initCUpload7(obj,type){
    let fileTypeExtsStr = '';
    let multi = true;
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }
    let itemTemplate = ``;
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:fileTypeExtsStr,
        itemTemplate:itemTemplate,
        multi:multi,
        buttonText:"上传",
        formData:{
            module:'产品查看',
            userId:sphdSocket.user.userID,
            groupUuid:groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            if(type == "img"){
                var imgs = $("#seeProduct").data("img") || [];
                imgs.push(data);
                $("#seeProduct").data("img",imgs);
                var product = $("#cpUplond-7 #file_upload_1-button").data("id");
                var coden = data.lastName;
                switch (coden) {
                    case 'jpg'://图片
                        var typekd = 1;
                        break;
                    case 'png'://图片
                        var typekd = 1;
                        break;
                    case 'jpeg'://图片
                        var typekd = 1;
                        break;
                    case 'gif'://图片
                        var typekd = 1;
                        break;
                    case 'mp4'://视频
                        var typekd = 2;
                        break;
                    case 'txt'://文件
                        var typekd = 3;
                        break;
                }
                $.ajax({
                    url:"../product/saveOrUpdatePdBaseImage.do",
                    data:{
                        // Integer product         //产品id
                        // Integer category        //类型：1-产品图纸,2-控制点序号图
                        // String  title           //标题
                        // Integer type            //类型:1-图片,2-视频,3-文档
                        // String  uplaodPath      //文件上传路径
                        product:product,
                        category:1,
                        title:data.displayName,
                        type:typekd,
                        uplaodPath:data.filename
                    },
                    success:function(reskey){
                        var success = reskey.success;
                        if(success == 1){
                            $("#propiclook").show();
                            $("#propiclookhas").hide();
                            $("#cpUplond-3").show();
                            $("#cpUplond-7").hide();
                            $(".pic1").hide();
                            $(".uppic1").show();
                            $(".propiclook").data("img",imgs);
                        }else{
                            $("#propiclook").hide();
                            $("#propiclookhas").show();
                            $("#cpUplond-3").hide();
                            $("#cpUplond-7").show();
                            $(".pic1").show();
                            $(".uppic1").hide();
                        }
                    }
                })
            }
        }
    })
}
function initCUpload8(obj,type){
    let fileTypeExtsStr = '';
    let multi = true;
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type == "doc"){
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``;
    obj.html("");
    obj.Huploadify({
        auto:true,
        fileTypeExts:fileTypeExtsStr,
        itemTemplate:itemTemplate,
        multi:multi,
        buttonText:"上传",
        formData:{
            module:'产品查看',
            userId:sphdSocket.user.userID,
            groupUuid:groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data);
            if(type == "img"){
                var imgs = $("#seeProduct").data("imgbox") || [];
                imgs.push(data);
                $("#seeProduct").data("imgbox",imgs);
                // $("#cpUplond-4").show();
                // $("#cpUplond-8").hide();
                // $(".pic2").hide();
                // $(".uppic2").show();
                // $("#propiclook2").show();
                // $("#propiclookhas2").hide();
                var product = $("#cpUplond-8 #file_upload_1-button").data("id");
                var coden = data.lastName;
                switch (coden) {
                    case 'jpg'://图片
                        var typekd = 1;
                        break;
                    case 'png'://图片
                        var typekd = 1;
                        break;
                    case 'jpeg'://图片
                        var typekd = 1;
                        break;
                    case 'gif'://图片
                        var typekd = 1;
                        break;
                    case 'mp4'://视频
                        var typekd = 2;
                        break;
                    case 'txt'://文件
                        var typekd = 3;
                        break;
                }
                $.ajax({
                    url:"../product/saveOrUpdatePdBaseImage.do",
                    data:{
                        // Integer product         //产品id
                        // Integer category        //类型：1-产品图纸,2-控制点序号图
                        // String  title           //标题
                        // Integer type            //类型:1-图片,2-视频,3-文档
                        // String  uplaodPath      //文件上传路径
                        product:product,
                        category:2,
                        title:data.displayName,
                        type:typekd,
                        uplaodPath:data.filename
                    },
                    success:function(reskey){
                        var success = reskey.success;
                        if(success == "1"){
                            $("#cpUplond-4").show();
                            $("#cpUplond-8").hide();
                            $(".pic2").hide();
                            $(".uppic2").show();
                            $("#propiclook2").show();
                            $("#propiclookhas2").hide();
                            $(".propiclook2").data("imgbox",imgs);
                        }else{
                            $("#cpUplond-4").hide();
                            $("#cpUplond-8").show();
                            $(".pic2").show();
                            $(".uppic2").hide();
                            $("#propiclook2").hide();
                            $("#propiclookhas2").show();
                        }
                    }
                })
            }
        }
    })
}
// creator: sy 2023-08-02   移除数据
function detented(keyd){
    bounce.cancel($("#addProduct"));
    bounce_Fixed.show($("#deteation"));
    switch (keyd) {
        case 1:
            var str1 = `
                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="closenwind(5)">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="keepsure(1)">确定</span>
            `;
            $(".qspent").html(str1);
            break;
        case 2:
            var str2 = `
                <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-5" onclick="closenwind(5)">取消</span>
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="keepsure(2)">确定</span>
            `;
            $(".qspent").html(str2);
            break;
    }
}
// creator: sy 2023-08-02   移除弹窗确定
function keepsure(upen){
    switch (upen) {
        case 1:
            $(".upent").hide();
            $(".detent").show();
            $(".unupent").hide();
            $(".oveadd").hide();
            $("#cpUplond-1").show();
            $("#cpUplond-2").hide();
            var imgs = $("#addProduct").data("img") || [];//获取到全部图片
            imgs = [];
            $("#addProduct").data("img",imgs);
            break;
        case 2:
            $(".upent2").hide();
            $(".unupent2").hide();
            $(".detent2").show();
            $(".oveadd2").hide();
            $("#cpUplond-5").show();
            $("#cpUplond-6").hide();
            var imgs2 = $("#addProduct").data("img2") || [];
            imgs2 = [];
            $("#addProduct").data("img2",imgs2);
            break;
    }
    bounce_Fixed.cancel($("#deteation"));
    bounce.show($("#addProduct"));
}
// creator: sy 2023-09-19   点击‘查看’查看本地上传的图片
function tacklook(checked){
    switch(checked){
        case 1:
            var imgbox = $("#tacklook").data("imgbox");
            //该怎么编写就能让图片在点击查看的时候放大
            imgbox.forEach(itemd => {
                var src = itemd.filename;//src应该等于图片的地址
                $("#picShow img").attr('src',$.fileUrl + src);
                $("#picShow").fadeIn("fast");//图片淡入淡出效果
            })
            break;
        case 2:
            var imgbox = $("#tacklook2").data("imgbox");
            imgbox.forEach(itemea => {
                var src = itemea.filename;
                $("#picShow img").attr('src',$.fileUrl + src);
                $("#picShow").fadeIn("fast");//图片淡入淡出效果
            })
            break;
    }
}
// creator: sy 2023-08-02   点击“操作记录”展示操作记录弹窗
function catlistollk(type){
    switch (type) {
        case 0:
            bounce_Fixed.show($("#procatlist"));
            bounce.cancel($("#seeProduct"));
            var propicbook = $("#catlistollk0").data("catl");
            //propicbook是个json数据
            var creater = propicbook.createName + '&nbsp;&nbsp;' + new Date(propicbook.createDate).format('yyyy/MM/dd hh:mm:ss');
            $("#seeCreater2").html(creater);
            var key = '';
            $("#prolistbook").find("[need]").each(function(){
                key = $(this).data('name');
                switch (key) {
                    case 'phrase':
                        $(this).html(charge(propicbook[key], 'state'));
                        break;
                    case 'name':
                        $(this).html(propicbook[key] + ((handleNull(propicbook["specifications"]) === "")?'':'/'+ propicbook["specifications"])+ ((handleNull(propicbook['model']) === "")?'':'/'+propicbook['model']));
                        break;
                    case 'netWeight':
                        $(this).html(netWeightLimit(propicbook[key],propicbook['weightUnit']));
                        break;
                    default:
                        $(this).html(handleNull(propicbook[key]));
                        break;
                }
            })
            $(".titme").html("图片—图片格式的产品图纸");
            //需要获取对应的产品的id
            var proid = $("#catlistollk0").data("proid");//产品的id
            $.ajax({
                url:"../product/getPdBaseImageRecord.do",
                data:{
                    //Integer product         //产品id
                    // Integer category        //类型：1-产品图纸,2-控制点序号图
                    product:proid,
                    category:1
                },
                success:function(resden){
                    var listk = resden.data;
                    var strall = ``;
                    strall += `
                            <tbody>
                                <tr>
                                    <td width="18%">操作的名称</td>
                                    <td width="18%">操作者</td>
                                    <td width="18%">操作后的图片</td>
                                </tr>
                        `;
                    listk.forEach(itemd => {
                        var operation = itemd.operation;//操作名称
                        var updateName = itemd.updateName;//操作人
                        var updateDate = itemd.updateDate;//操作时间
                        var creatro = updateName + '&nbsp;&nbsp;' + new Date(updateDate).format('yyyy-MM-dd hh:mm:ss');
                        if(creatro == 'null&nbsp;&nbsp;'){
                            creatro = null;
                        }
                        var str1 = ``;
                        var str2 = ``;
                        var str3 = ``;
                        switch (operation) {//1-上传,2-移除,3-更换
                            case "1"://上传
                                var answer = "上传";
                                strall += `
                                    <tr class="upsent">
                                        <td class="upsend">${answer}</td>
                                        <td class="upsendpeo">${handleNull(creatro)}</td>
                                        <td>
                                            <span class="ty-color-blue" onclick="havetrlook($(this))">查看</span>
                                            <span class="hd">${JSON.stringify(itemd)}</span>
                                        </td>
                                    </tr>
                                `;
                                break;
                            case "2"://移除
                                var answer = "移除";
                                strall += `
                                    <tr class="dettal">
                                        <td class="dettleal">${answer}</td>
                                        <td class="dettlealall">${handleNull(creatro)}</td>
                                        <td>
                                            <span class="ty-color-blue" onclick="delooker()">查看</span>
                                            <span class="hd">${JSON.stringify(itemd)}</span>
                                        </td>
                                    </tr>
                                `;
                                break;
                            case "3"://更换
                                var answer = "更换";
                                strall +=`
                                    <tr class="upnewble">
                                        <td class="upnewblce">${answer}</td>
                                        <td class="upnewblceo">${handleNull(creatro)}</td>
                                        <td>
                                            <span class="ty-color-blue" onclick="havetrlook($(this))">查看</span>
                                            <span class="hd">${JSON.stringify(itemd)}</span>
                                        </td>
                                    </tr>
                                `;
                                break;
                        }
                    })
                    strall += `</tbody>`;
                    $("#procatpicok").html(strall);
                }
            })
            break;
        case 1:
            bounce_Fixed.show($("#procatlist"));
            bounce.cancel($("#seeProduct"));
            var propicbook = $("#catlistollk1").data("catl");
            var creater = propicbook.createName + '&nbsp;&nbsp;' + new Date(propicbook.createDate).format('yyyy/MM/dd hh:mm:ss');
            $("#seeCreater2").html(creater);
            var key = '';
            $("#prolistbook").find("[need]").each(function(){
                key = $(this).data('name');
                switch (key) {
                    case 'phrase':
                        $(this).html(charge(propicbook[key], 'state'));
                        break;
                    case 'name':
                        $(this).html(propicbook[key] + ((handleNull(propicbook["specifications"]) === "")?'':'/'+ propicbook["specifications"])+ ((handleNull(propicbook['model']) === "")?'':'/'+propicbook['model']));
                        break;
                    case 'netWeight':
                        $(this).html(netWeightLimit(propicbook[key],propicbook['weightUnit']));
                        break;
                    default:
                        $(this).html(handleNull(propicbook[key]));
                        break;
                }
            })
            $(".titme").html("图片—标记有控制点顺序号的图片");
            var proid = $("#catlistollk1").data("proid");//产品的id
            $.ajax({
                url:"../product/getPdBaseImageRecord.do",
                data:{
                    //Integer product         //产品id
                    // Integer category        //类型：1-产品图纸,2-控制点序号图
                    product:proid,
                    category:2
                },
                success:function(resden){
                    var listk = resden.data;
                    var strall = ``;
                    strall += `
                            <tbody>
                                <tr>
                                    <td width="18%">操作的名称</td>
                                    <td width="18%">操作者</td>
                                    <td width="18%">操作后的图片</td>
                                </tr>
                        `;
                    listk.forEach(itemd => {
                        var operation = itemd.operation;//操作名称
                        var updateName = itemd.updateName;//操作人
                        var updateDate = itemd.updateDate;//操作时间
                        var creatro = updateName + '&nbsp;&nbsp;' + new Date(updateDate).format('yyyy-MM-dd hh:mm:ss');
                        if(creatro == 'null&nbsp;&nbsp;'){
                            creatro = null;
                        }
                        var str1 = ``;
                        var str2 = ``;
                        var str3 = ``;
                        switch (operation) {//1-上传,2-移除,3-更换
                            case "1"://上传
                                var answer = "上传";
                                strall += `
                                    <tr class="upsent">
                                        <td class="upsend">${answer}</td>
                                        <td class="upsendpeo">${handleNull(creatro)}</td>
                                        <td>
                                            <span class="ty-color-blue" onclick="havetrlook($(this))">查看</span>
                                            <span class="hd">${JSON.stringify(itemd)}</span>
                                        </td>
                                    </tr>
                                `;
                                break;
                            case "2"://移除
                                var answer = "移除";
                                strall += `
                                    <tr class="dettal">
                                        <td class="dettleal">${answer}</td>
                                        <td class="dettlealall">${handleNull(creatro)}</td>
                                        <td>
                                            <span class="ty-color-blue" onclick="delooker()">查看</span>
                                            <span class="hd">${JSON.stringify(itemd)}</span>
                                        </td>
                                    </tr>
                                `;
                                break;
                            case "3"://更换
                                var answer = "更换";
                                strall +=`
                                    <tr class="upnewble">
                                        <td class="upnewblce">${answer}</td>
                                        <td class="upnewblceo">${handleNull(creatro)}</td>
                                        <td>
                                            <span class="ty-color-blue" onclick="havetrlook($(this))">查看</span>
                                            <span class="hd">${JSON.stringify(itemd)}</span>
                                        </td>
                                    </tr>
                                `;
                                break;
                        }
                    })
                    strall += `</tbody>`;
                    $("#procatpicok").html(strall);
                }
            })

            break;
        case 2:
            bounce_Fixed.show($("#procatlist"));
            bounce.cancel($("#seeProduct"));
            var propicbook = $("#catlistollk2").data("catl");
            var creater = propicbook.createName + '&nbsp;&nbsp;' + new Date(propicbook.createDate).format('yyyy/MM/dd hh:mm:ss');
            $("#seeCreater2").html(creater);
            var key = '';
            $("#prolistbook").find("[need]").each(function(){
                key = $(this).data('name');
                switch (key) {
                    case 'phrase':
                        $(this).html(charge(propicbook[key], 'state'));
                        break;
                    case 'name':
                        $(this).html(propicbook[key] + ((handleNull(propicbook["specifications"]) === "")?'':'/'+ propicbook["specifications"])+ ((handleNull(propicbook['model']) === "")?'':'/'+propicbook['model']));
                        break;
                    case 'netWeight':
                        $(this).html(netWeightLimit(propicbook[key],propicbook['weightUnit']));
                        break;
                    default:
                        $(this).html(handleNull(propicbook[key]));
                        break;
                }
            })
            $(".titme").html("图纸/其他技术文件");
            var str = ``;
            str += `
                <tbody>
                    <tr>
                        <td width="18%">操作的名称</td>
                        <td width="18%">操作者</td>
                        <td width="18%">操作对象</td>
                    </tr>
                    <tr>
                        <td>与文件关联</td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                        <td>
                             <span class="ty-color-blue">查看</span>
                        </td>
                    </tr>
                    <tr>
                        <td>与文件解除关联</td>
                        <td>XXX XXXX-XX-XX XX:XX:XX</td>
                        <td>
                             <span class="ty-color-blue">查看</span>
                        </td>
                    </tr>
                </tbody>`;
            $("#procatpicok").html(str);
            var proid = $("#catlistollk2").data("proid");//产品的id
            $.ajax({
                url:"../product/getPdResourceRecord.do",
                data:{
                    product:proid
                },
                success:function(resden){
                    var lanks = resden.data;
                    //lanks是个数组
                    var strallen = ``;
                    strallen +=`
                        <tbody>
                            <tr>
                                <td width="18%">操作的名称</td>
                                <td width="18%">操作者</td>
                                <td width="18%">操作对象</td>
                            </tr>`;
                    lanks.forEach(itemk =>{
                        var operation = itemk.operation;//1-与文件关联,2-解除
                        var updateName = itemk.updateName;//操作人
                        var updateDate = itemk.updateDate;//操作时间
                        var resource = itemk.resource;//资源文件id
                        var creatro = updateName + '&nbsp;&nbsp;' + new Date(updateDate).format('yyyy-MM-dd hh:mm:ss');
                        if(creatro == 'null&nbsp;&nbsp;'){
                            creatro = null;
                        }
                        switch (operation) {
                            case "1"://与文件关联
                                var answer = "与文件关联";
                                strallen += `
                                    <tr class="association">
                                        <td class="associaend">${answer}</td>
                                        <td class="assotionpeo">${handleNull(creatro)}</td>
                                        <td>
                                            <span class="ty-color-blue" onclick="associaeno($(this))">查看</span>
                                            <span class="hd">${JSON.stringify(itemk)}</span>
                                        </td>
                                    </tr>`;
                                break;
                            case "2"://解除
                                var answer = "与文件解除关联";
                                strallen += `
                                    <tr class="secure">
                                        <td class="secueal">${answer}</td>
                                        <td class="secuedal">${handleNull(creatro)}</td>
                                        <td>
                                            <span class="ty-color-blue" onclick="secuneo()">查看</span>
                                            <span class="hd">${JSON.stringify(itemk)}</span>
                                        </td>
                                    </tr>`;
                                break;
                        }
                    })
                    strallen += `</tbody>`;
                    $("#procatpicok").html(strallen);
                }
            })
            break;
    }
}
// creator: sy 2023-10-12   点击‘查看’展示文件内容
function associaeno(obj){
    var lank = obj.next().html();
    lank = JSON.parse(lank);
    var proid = lank.resource;
    $.ajax({
        url:"../res/getResForProduct.do",
        data:{
            resIds:proid
        },
        success:function(res){
            var lanks = res.data.list;//lanks是个数组
            lanks.forEach(item =>{
                var id = item.id;
                if(id == proid){
                    var path = item.path;//文件的地址
                    //该怎么把文件内容展示出来呢？？？？？
                    var pathArr = path.split(".");
                    var type = pathArr[pathArr.length - 1].toLowerCase() ;
                    switch (type){//瞧着似乎已经对照范例判断了不同格式的文件，可是怎么让它跳转到新的页面展示呢？？？？
                        case 'doc':case 'docx':
                        case 'xls':case 'xlsx':
                        case 'ppt':case 'pptx':
                            // obj.attr('target','_blank').attr('href','https://view.officeapps.live.com/op/view.aspx?src='+$.fileUrl+path);
                            $("#docShow .ty-searchContent .fileList div").attr("href",'https://view.officeapps.live.com/op/view.aspx?src='+$.fileUrl + path);
                            bounce_Fixed2.show($("#docShow"));
                            break;
                        case 'png':case 'jpg':case 'jpeg':case 'gif':
                        case "pdf":
                            // obj.attr('target','_blank').attr('href',$.fileUrl+path);
                            //是不是可以参考另一个展示图片的方法在页面上展示选择的图片呢？
                            $("#picShow img").attr('src',$.fileUrl + path);
                            $("#picShow").fadeIn("fast");//图片淡入淡出效果
                            break;
                        case "md":
                            // obj.attr('target','_blank').attr('href',$.webRoot+'/assets/md/index.html?src='+$.fileUrl+path);
                            $("#docShow .ty-searchContent .fileList div").attr("href",$.webRoot+'/assets/md/index.html?src='+$.fileUrl + path);
                            bounce_Fixed2.show($("#docShow"));
                            break;
                        default:
                            // obj.attr('target','_blank').attr('href',$.ow365url+path);
                            $("#docShow .ty-searchContent .fileList div").attr("href",$.ow365url + path);
                            bounce_Fixed2.show($("#docShow"));
                            break;
                    }
                }
            })
        }
    })
}
function secuneo(){
    layer.msg("解除后，就没有可供查看的图片或文件了！");
}
// creator: sy 2023-08-02   点击“移除”后的“查看”按钮
function delooker(){
    layer.msg("移除后，就没有可供查看的图片了！");
}
// creator: sy 2023-10-07   点击‘查看’展示图片
function havetrlook(obj){
    var lank = obj.next().html();
    lank = JSON.parse(lank);
    var getsrc = lank.uplaodPath;//图片的地址
    if(getsrc == undefined){
        lank.forEach(item => {
            getsrc = item.filename;
        })
    }
    $("#picShow img").attr('src',$.fileUrl + getsrc);
    $("#picShow").fadeIn("fast");//图片淡入淡出效果
}
// creator: sy 2023-10-07   产品查看弹窗中增加文件
function addproct(typed){
    if(typed == 1){
        $("#chooseSaveFolder").width(1310);
        $("#chooseSaveFolder .mar").show();
    }else{
        $("#chooseSaveFolder").width(600);
        $("#chooseSaveFolder .mar").hide();
    }
    bounce_Fixed.show($("#chooseSaveFolder"));
    getFirstDoc();
    $("#fileSort").hide();
    $("#chooseSaveFolder .ty-fileList").html("");
    $("#ye_con").html("");
    setEveryTime(bounce_Fixed, 'chooseSaveFolder');
    bounce.cancel($("#seeProduct"));
    $("#sureChooseFolderBtn").data("name","sureChooseFolder2");
    $("#deteeten").data("deffint","prolook");
    var proid = $(".addproct").data("proid");
    $("#sureChooseFolderBtn").data("proid",proid);
}
// creator: sy 2023-10-07   查看文件数据
function haveloonk2(keyedn,chone){
    var havelook1 = $(".haveloonk2").data("list");
    // String weightUnit   //重量单位:1-毫克(mg),2-克(g),3-千克(kg),4-吨(T)
    // String source       //来源:1-外购,2-自制
    // String composition  //构成:1-购买,2-制造,3-装配
    var weightUnit = havelook1.weightUnit;
    var source = havelook1.source;
    var composition = havelook1.composition;
    switch (weightUnit) {
        case "1"://毫克
            var weightunit = "毫克";
            break;
        case "2"://克
            var weightunit = "克";
            break;
        case "3"://千克
            var weightunit = "千克";
            break;
        case "4"://吨
            var weightunit = "吨";
            break;
    }
    switch (source) {
        case "1"://外购
            var sorce = "外购";
            break;
        case "2"://自制
            var sorce = "自制";
            break;
    }
    switch (composition) {
        case "1"://购买
            var comp = "购买";
            break;
        case "2"://制造
            var comp = "制造";
            break;
        case "3"://装配
            var comp = "装配";
            break;
    }
    var strhve1 =``;
    strhve1 += `
            <tbody>
            <tr>
                <td style="width: 100px;">产品图号</td>
                <td style="width: 170px;">产品名称/规格/型号</td>
                <td style="width: 100px;">计量单位</td>
                <td style="width: 100px;">产品单重</td>
                <td style="width: 100px;">重量单位</td>
                <td style="width: 105px;">来源</td>
                <td style="width: 105px;">构成</td>
                <td style="width: 194px;">创建人</td>
            </tr>
            <tr>
                <td>${handleNull(havelook1.innerSn)}</td>
                <td>${handleNull(havelook1.name)}/${handleNull(havelook1.specifications)}/${handleNull(havelook1.model)}</td>
                <td>${handleNull(havelook1.unit)}</td>
                <td>${handleNull(havelook1.netWeight)}</td>
                <td>${handleNull(weightunit)}</td>
                <td>${handleNull(sorce)}</td>
                <td>${handleNull(comp)}</td>
                <td>${handleNull(havelook1.createName)} ${handleNull(new Date(havelook1.createDate).format('yyyy-MM-dd hh:mm:ss'))}</td>
            </tr>
       </tbody>`;
    $("#procatloken").html(strhve1);
    // var idbox = $(".haveloonk2").data("idbox");//获取的文件的id
    var ckbox = $(".haveloonk2").data("cklank");//获取关联的文件数据
    if(chone !== undefined){
        var choseid = chone.id;
        let len = ckbox.length;
        while(len--){
            if(ckbox[len].resource == choseid){
                ckbox.splice(len,1);
            }
        }
        // ckbox.forEach(function(itemck,index){
        //     var choseid = chone.id;
        //     var ckid = itemck.resource;
        //     if(choseid == ckid){
        //         ckbox.splice(index,1);
        //     }
        // })
    }
    //既然另外一个接口调用的时候会出现明明移除了但还是会获取该文件数据的情况，就需要在这里获取前面调用查看弹窗的接口
    //根据产品查看弹窗中获取到的数据的id判断哪些文件的Id该传
    if(keyedn == 0){//已经没有文件数据了
        var other = 0;
        $(".pronemd").html(other);
        $(".protest").html(other);
        // layer.msg('未关联文件');
        $(".pronemd").html(0);
        $(".messgnend").html("");
        return false;
    }else if(keyedn !== undefined){
        var proid = keyedn.id;
        var str1 = ``;
        var ckmeglist = [];
        if(ckbox == undefined){
            var other = 0;
            $(".pronemd").html(other);
            layer.msg('未关联文件');
            $(".pronemd").html(0);
            $(".messgnend").html("");
            return false;
        }
        ckbox.forEach(function(itemck,index){
            // var jsonbox = {
            //     prolist:"",
            //     time:""
            // };
            var resEntity = itemck.resEntity;//这个应该是关联的文件的细节数据
            var createTime = itemck.createDate;//createDate
            resEntity.createTime = createTime;
            //之前调用接口获取的数据就能在resEntity中获取到，实在获取不到的，在ckbox中也能获取到
            //原来是获取到的数据是一个数组，现在ckbox是一个数组，resEntity是个json数据了
            console.log('resEntity的格式样式',resEntity);
            //resEntity的值和prochosed中单条数据相同,所以需要将每一个resEntity单独获取，整体存入一个新的数组
            //就能跟prochosed的格式一样了
            ckmeglist.push(resEntity);
        })
        console.log('ckmeglist的格式',ckmeglist);
        if(ckmeglist == undefined){
            var other = 0;
            $(".pronemd").html(other);
            layer.msg('未关联文件');
            $(".pronemd").html(0);
            $(".messgnend").html("");
            return false;
        }else{
            $(".pronemd").html(ckmeglist.length);
        }
        str1 = getFileListStr(ckmeglist);
        $("#addfilelook .addProductForm").children("p").next().next().next().html(str1);
        //sy添加的部分-------------
        $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeote").show();
        $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeo").removeClass("oterme");
        var numb = $(".sizesi").html();
        if(numb == "G0"){
            $(".sizehd a[name='changeVersionRecord']").addClass("ty-disabled");
        }
        $("#dettenadd").data("projsond",ckmeglist);
        bounce.cancel($("#seeProduct"));
        bounce_Fixed.show($("#addfilelook"));
        $("#dettenadd").data("keyen","taklook");
        var proid = $(".haveloonk2").data("proid");
        $("#dettenadd").data("proid",proid);
        var pdId = $(".haveloonk2").data("proid");
        $("#closeoff").data("poid",pdId);
        var alllist = $(".haveloonk2").data("allmessg");//获取到文件数据
        var allmegbox = [];
        alllist.forEach(itema =>{
            var jsona = {
                id:itema.id,
                resource:itema.resource
            };
            allmegbox.push(jsona);
        })
        $("#dettenadd").data("allbox",allmegbox);//存着该产品的所有文件数据

        // for(let i in idbox){
        //     if(proid == idbox[i].id){
        //         idbox.splice(i,1);
        //     }
        // }
        // var idboxe = [];
        // idbox.forEach(itemd => {
        //     idboxe.push(itemd.id);
        // })
        // idbox = idboxe;
        // idbox = JSON.stringify(idbox);//将数组转换成字符串
        // //传值需要是个字符串，貌似字符串中不能有数组的中括号，那该怎么去除呢？？？
        // idbox = idbox.replace(/\[|]/g,'');//奇迹，真的成功啦

        // $.ajax({
        //     url:"../res/getResForProduct.do",
        //     data:{
        //         resIds:idbox
        //     },
        //     success:function(res){
        //         var prochosed = res.data.list;//文件数据
        //         if(prochosed == undefined){
        //             var other = 0;
        //             $(".pronemd").html(other);
        //             layer.msg('未关联文件');
        //             $(".pronemd").html(0);
        //             $(".messgnend").html("");
        //             return false;
        //         }else{
        //             $(".pronemd").html(prochosed.length);
        //         }
        //         var str1 =``;
        //         console.log("prochosed的格式",prochosed);
        //         str1 = getFileListStr(prochosed);
        //         $("#addfilelook .addProductForm").children("p").next().next().next().html(str1);
        //         //sy添加的部分-------------
        //         $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeote").show();
        //         $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeo").removeClass("oterme");
        //         var numb = $(".sizesi").html();
        //         if(numb == "G0"){
        //             $(".sizehd a[name='changeVersionRecord']").addClass("ty-disabled");
        //         }
        //         $("#dettenadd").data("projsond",prochosed);
        //         bounce.cancel($("#seeProduct"));
        //         bounce_Fixed.show($("#addfilelook"));
        //         $("#closeoff").data("keyd","looker");
        //         $("#dettenadd").data("keyen","taklook");
        //         var proid = $(".haveloonk2").data("proid");
        //         $("#dettenadd").data("proid",proid);
        //         var pdId = $(".haveloonk2").data("proid");
        //         $("#closeoff").data("poid",pdId);
        //         var alllist = $(".haveloonk2").data("allmessg");//获取到文件数据
        //         //思路：将调用‘产品查看’接口获取到的数据给查看按钮设置上，在设置按钮
        //         //将文件数据的id和resource的值都存入一个数组中,在点击‘查看’按钮的时候获取存入的数据，
        //         //用获取到的数据同列表中选定的数据进行比较，找到选中的数据的resource的值
        //         var allmegbox = [];
        //         alllist.forEach(itema =>{
        //             var jsona = {
        //                 id:itema.id,
        //                 resource:itema.resource
        //             };
        //             allmegbox.push(jsona);
        //         })
        //         $("#dettenadd").data("allbox",allmegbox);//存着该产品的所有文件数据
        //     }
        // })
    }else{
        var ckmeglist = [];
        if(ckbox == undefined){
            var other = 0;
            $(".pronemd").html(other);
            layer.msg('未关联文件');
            $(".pronemd").html(0);
            $(".messgnend").html("");
            return false;
        }
        ckbox.forEach(function(itemck,index){
            var resEntity = itemck.resEntity;//这个应该是关联的文件的细节数据
            var createTime = itemck.createDate;
            resEntity.createTime = createTime;
            //之前调用接口获取的数据就能在resEntity中获取到，实在获取不到的，在ckbox中也能获取到
            //原来是获取到的数据是一个数组，现在ckbox是一个数组，resEntity是个json数据了
            console.log('resEntity的格式样式',resEntity);
            ckmeglist.push(resEntity);
        })
        var messtble = $(".protest").html();
        messtble = Number(messtble);
        if(messtble == 0){
            layer.msg('未关联文件');
            return false;
        }
        if(ckmeglist == undefined){
            var other = 0;
            $(".pronemd").html(other);
            layer.msg('未关联文件');
            $(".pronemd").html(0);
            $(".messgnend").html("");
            return false;
        }else{
            $(".pronemd").html(ckmeglist.length);
        }
        str1 = getFileListStr(ckmeglist);
        $("#addfilelook .addProductForm").children("p").next().next().next().html(str1);
        //sy添加的部分-------------
        $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeote").show();
        $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeo").removeClass("oterme");
        var numb = $(".sizesi").html();
        if(numb == "G0"){
            $(".sizehd a[name='changeVersionRecord']").addClass("ty-disabled");
        }
        $("#dettenadd").data("projsond",ckmeglist);
        bounce.cancel($("#seeProduct"));
        bounce_Fixed.show($("#addfilelook"));
        $("#closeoff").data("keyd","looker");
        $("#dettenadd").data("keyen","taklook");
        var proid = $(".haveloonk2").data("proid");
        $("#dettenadd").data("proid",proid);
        var pdId = $(".haveloonk2").data("proid");
        $("#closeoff").data("poid",pdId);
        var alllist = $(".haveloonk2").data("allmessg");//获取到文件数据
        var allmegbox = [];
        alllist.forEach(itema =>{
            var jsona = {
                id:itema.id,
                resource:itema.resource
            };
            allmegbox.push(jsona);
        })
        $("#dettenadd").data("allbox",allmegbox);//存着该产品的所有文件数据
        // var idboxe = [];
        // idbox.forEach(itemd => {
        //     idboxe.push(itemd.id);
        // })
        // idbox = idboxe;
        // idbox = JSON.stringify(idbox);//将数组转换成字符串
        //传值需要是个字符串，貌似字符串中不能有数组的中括号，那该怎么去除呢？？？
        // idbox = idbox.replace(/\[|]/g,'');//奇迹，真的成功啦

        // $.ajax({
        //     url:"../res/getResForProduct.do",
        //     data:{
        //         resIds:idbox
        //     },
        //     success:function(res){
        //         var prochosed = res.data.list;//文件数据
        //         if(prochosed == undefined){
        //             var other = 0;
        //             $(".pronemd").html(other);
        //             layer.msg('未关联文件');
        //             $(".pronemd").html(0);
        //             $(".messgnend").html("");
        //             return false;
        //         }else{
        //             $(".pronemd").html(prochosed.length);
        //         }
        //         var str1 =``;
        //         console.log("prochosed的格式",prochosed);
        //         str1 = getFileListStr(prochosed);
        //         $("#addfilelook .addProductForm").children("p").next().next().next().html(str1);
        //         //sy添加的部分-------------
        //         $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeote").show();
        //         $("#addfilelook .specialForm .addProductForm .ty-fileItem .sizeo").removeClass("oterme");
        //         var numb = $(".sizesi").html();
        //         if(numb == "G0"){
        //             $(".sizehd a[name='changeVersionRecord']").addClass("ty-disabled");
        //         }
        //         $("#dettenadd").data("projsond",prochosed);
        //         bounce.cancel($("#seeProduct"));
        //         bounce_Fixed.show($("#addfilelook"));
        //         $("#closeoff").data("keyd","looker");
        //         $("#dettenadd").data("keyen","taklook");
        //         var proid = $(".haveloonk2").data("proid");
        //         $("#dettenadd").data("proid",proid);
        //         var pdId = $(".haveloonk2").data("proid");
        //         $("#closeoff").data("poid",pdId);
        //         var alllist = $(".haveloonk2").data("allmessg");//获取到文件数据
        //         //思路：将调用‘产品查看’接口获取到的数据给查看按钮设置上，在设置按钮
        //         //将文件数据的id和resource的值都存入一个数组中,在点击‘查看’按钮的时候获取存入的数据，
        //         //用获取到的数据同列表中选定的数据进行比较，找到选中的数据的resource的值
        //         var allmegbox = [];
        //         alllist.forEach(itema =>{
        //             var jsona = {
        //                 id:itema.id,
        //                 resource:itema.resource
        //             };
        //             allmegbox.push(jsona);
        //         })
        //         $("#dettenadd").data("allbox",allmegbox);//存着该产品的所有文件数据
        //     }
        // })
    }
}
// creator: sy 2023-10-09   关闭新增文件的弹窗
function closewindw(){
    //思路：应该是需要区分下是产品录入还是产品查看
    var chonse = $("#deteeten").data("deffint");
    if(chonse == "prolook"){//产品查看
        bounce_Fixed.cancel();
        bounce.show($("#seeProduct"));
    }else{//产品录入
        bounce_Fixed.cancel();
        bounce.show($('#addProduct'));
    }
}