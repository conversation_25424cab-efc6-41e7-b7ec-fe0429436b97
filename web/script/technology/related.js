var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#productRelatedCm"));
bounce_Fixed2.cancel();
$(function () {
    showMainCon(1) ;
    getWaitRelativeProList(1,20);
    $(".ty-secondTab li").on("click",function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        var index = $(this).index();
        if(index === 0){
            getWaitRelativeProList(1,20);
            $("#waitRelativeCommody").show().siblings().hide();
        }else{
            getRelativedProList(1,20,"");
            $("#relativedCommody").show().siblings().hide();
        }
    });
    $("body").on("click",".linkBtn,.funBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
})
// creator: 李玉婷，2020-03-24 15:53:31，获取待关联商品列表
function getWaitRelativeProList(currPage,pageSize){
    $("#waitNum").html("0");
    $("#ye_accept").html("");
    $("#waitRelativeList tbody").html("");
    $.ajax({
        "url": "../product/getWaitRelevanceProList.do",
        "data": {
            "pageSize":pageSize,
            "currPage":currPage
        },
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            setPage( $("#ye_accept") , curr ,  totalPage , "waitRelative" );

            var list = res.data;
            if (list && list.length >0){
                var html = '';
                for(var i=0;i<list.length;i++){
                    html +=
                        '<tr data-info=\''+ JSON.stringify(list[i]) +'\'>' +
                        '    <td>'+ list[i].outerSn +'</td>' +
                        '    <td>'+ list[i].outerName +'</td>' +
                        '    <td>'+ list[i].model +'</td>' +
                        '    <td>'+ list[i].specifications +'</td>' +
                        '    <td>'+ list[i].unit +'</td>' +
                        '    <td>'+ list[i].customerName +'</td>' +
                        '    <td class="createInfo">'+ list[i].createName + '&nbsp;&nbsp;' + new Date(list[i].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td><span class="ty-color-blue" onclick="choosePoduct($(this))">去选择产品</span></td>' +
                        '</tr>';
                }
                $("#waitNum").html(res.totalRows);
                $("#waitRelativeList tbody").html(html);
            }
        }
    });
}
// creator: 李玉婷，2020-03-27 13:15:17，已关联的商品-搜索
function searchReadyCommody() {
    var keyword = $("#searchReadyKey").val();
    getRelativedProList(1,20,keyword);
}
// creator: 李玉婷，2020-03-24 16:12:29，获取已关联商品列表
function getRelativedProList(currPage,pageSize,key){
    $("#relatedReady").html("0");
    $("#ye_accept").html("");
    $("#relativedList tbody").html("");
    $.ajax({
        "url": "../product/getAlreadyProList.do",
        "data": {
            "pageSize":pageSize,
            "currPage":currPage,
            "param": key
        },
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = {
                "param": key
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#ye_accept") , curr ,  totalPage , "relatived", jsonStr );
            var list = res.data;
            if (list && list.length >0){
                var html = '';
                for(var a=0;a<list.length;a++){
                    html +=
                        '<tr data-info=\''+ JSON.stringify(list[a]) +'\'>' +
                        '    <td>'+ list[a].outerSn +'</td>' +
                        '    <td>'+ list[a].outerName +'</td>' +
                        '    <td>'+ list[a].model +'</td>' +
                        '    <td>'+ list[a].specifications +'</td>' +
                        '    <td>'+ list[a].unit +'</td>' +
                        '    <td>'+ list[a].customerName +'</td>' +
                        '    <td class="createInfo">'+ list[a].createName + '&nbsp;&nbsp;' + new Date(list[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td class="createInfo">'+ list[a].createName + '&nbsp;&nbsp;' + new Date(list[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td><span class="ty-color-blue" onclick="productCorrelationSee($(this))">查看</span></td>' +
                        '</tr>';
                }
                $("#relativedList tbody").html(html);
                $("#relatedReady").html(res.totalRows);
            }
        }
    });
}
// creator: 李玉婷，2020-02-12 16:50:46，去选择产品
function choosePoduct(obj) {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else{
        $("#commodityProductrelate input").val('');
        $("#productDetails tbody tr:gt(0)").remove();
        var info = obj.parents("tr").data("info");
        var html =
            '<tr>' +
            '    <td>'+ info.outerSn +'</td>' +
            '    <td>'+ info.outerName +'</td>' +
            '    <td>'+ info.model +'</td>' +
            '    <td>'+ info.specifications +'</td>' +
            '    <td>'+ info.unit +'</td>' +
            '    <td>'+ info.customerName +'</td>' +
            '    <td>'+ info.createName + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss') +'<span class="hd">'+ JSON.stringify(info) +'</span>' +
            '</td>' +
            '</tr>';
        $("#productDetails tbody").append(html);
        getWaitProductList(1,20,"");
    }
}
// creator: 李玉婷，2020-03-27 13:20:12，去选择产品-查找产品
function searchPdKey() {
    var sKey = $("#searchPdKey").val();
    getWaitProductList(1,20,sKey);
}
function getWaitProductList(currPage,pageSize,param) {
    $("#waiteRl").html("");
    $("#relationList tbody tr:gt(0)").remove();
    $.ajax({
        "url": "../product/getWaitPdPerList.do",
        "data": {
            "pageSize":pageSize,
            "currPage":currPage,
            "param":param
        },
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var jsonStr = {
                "param": param
            } ;
            jsonStr = JSON.stringify(jsonStr) ;
            setPage( $("#waiteRl") , curr ,  totalPage , "waiteRl", jsonStr );
            var products = res.data;
            if (products && products.length>0){
                var html = '';
                for(var a=0;a<products.length;a++){
                    html +=
                        '<tr>' +
                        '    <td><i class="fa fa-circle-o" onclick="productRelated($(this))"></i>' +
                        '<span class="hd">'+ JSON.stringify(products[a]) +'</span>' +
                        '</td>' +
                        '    <td>'+ handleNull(products[a].innerSn) +'</td>' +
                        '    <td>'+ handleNull(products[a].name) +'</td>' +
                        '    <td>'+ handleNull(products[a].model) +'</td>' +
                        '    <td>'+ handleNull(products[a].specifications) +'</td>' +
                        '    <td>'+ handleNull(products[a].unit) +'</td>' +
                        '    <td>'+ charge(products[a].source, "source") +'</td>' +
                        '    <td>'+ charge(products[a].composition,"composition") +'</td>' +
                        '    <td>'+ handleNull(products[a].netWeight) +'</td>' +
                        '    <td>'+ weightUnitCharge(products[a].weightUnit) +'</td>' +
                        '    <td class="createInfo">'+ products[a].createName + '&nbsp;&nbsp;' + new Date(products[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '</tr>';
                }
                $("#relationList tbody").append(html);
            }
            bounce.show($("#commodityProductrelate"));
        }
    });
}
// creator: 李玉婷，2020-03-26 14:13:13，勾选后详情展示
function productRelated(obj) {
    var goods = $("#productDetails tbody tr").find(".hd").html();
    var product = obj.siblings("span.hd").html();
    $("#relatedGoods tbody tr:gt(0)").remove();
    $("#relatedProduct tbody tr:gt(0)").remove();
    $("#productSee tbody tr:gt(0)").remove();
    if (goods !== '') goods = JSON.parse(goods);
    if (product !== '') product = JSON.parse(product);
    var goodsHtml =
        '<tr data-id="'+ goods.id +'">' +
        '    <td>'+ goods.outerSn +'</td>' +
        '    <td>'+ goods.outerName +'</td>' +
        '    <td>'+ handleNull(goods.model) +'</td>' +
        '    <td>'+ handleNull(goods.specifications) +'</td>' +
        '    <td>'+ handleNull(goods.unit) +'</td>' +
        '    <td>'+ handleNull(goods.customerName) +'</td>' +
        '    <td>'+ goods.createName + '&nbsp;&nbsp;' + new Date(goods.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
        '</tr>';
    $("#relatedGoods tbody").append(goodsHtml);
    var pdHtml =
        '<tr data-id="'+ product.id +'">' +
        '    <td>'+ product.innerSn +'</td>' +
        '    <td>'+ product.name +'</td>' +
        '    <td>'+ handleNull(product.model) +'</td>' +
        '    <td>'+ handleNull(product.specifications) +'</td>' +
        '    <td>'+ handleNull(product.unit) +'</td>' +
        '    <td>'+ charge(product.source,"source") +'</td>' +
        '    <td>'+ charge(product.composition,"composition") +'</td>' +
        '    <td>'+ handleNull(product.netWeight) +'</td>' +
        '    <td>'+ weightUnitCharge(product.weightUnit) +'</td>' +
        '    <td>'+ product.createName + '&nbsp;&nbsp;' + new Date(product.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
        '</tr>';
    $("#productRelated").data("id",product.id);
    $("#relatedProduct tbody").append(pdHtml);
    $("#productSee tbody").append(pdHtml);
    bounce_Fixed.show($("#productRelated"));
    getRelateGoods(product.id);
}
// creator: 李玉婷，2020-03-26 14:34:18，获取勾选的产品还关联的商品
function getRelateGoods(productId){
    let count = 0;
    $("#relatedNum").html("0");
    $("#relatedRecord tbody tr:gt(0)").remove();
    $.ajax({
        "url": "../product/getProductListByPdBase.do",
        "data": {
            "pdBaseId": productId
        },
        success: function (res) {
            var list = res.list;
            if (list && list.length >0){
                count = list.length
                var str = '';
                for(var a=0;a<list.length;a++){
                    str +=
                        ' <tr>' +
                        '    <td>'+ list[a].outerSn  +'</td>' +
                        '    <td>'+ list[a].outerName  +'</td>' +
                        '    <td>'+ list[a].model  +'</td>' +
                        '    <td>'+ list[a].specifications  +'</td>' +
                        '    <td>'+ list[a].unit  +'</td>' +
                        '    <td>'+ list[a].customerName  +'</td>' +
                        '    <td>'+ list[a].createName + '&nbsp;&nbsp;' + new Date(list[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[a].correlaterName + '&nbsp;&nbsp;' + new Date(list[a].correlateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '</tr>';
                }
                $("#relatedRecord tbody").append(str);
                $("#relatedNum").html(list.length);
            }
            $(".relatedNumSee").html(count);
        }
    });
}
// creator: 李玉婷，2020-03-26 16:22:18，确认关联
function relateGoodsScan(){
    bounce_Fixed2.show($("#productRelatedCm"));
}
// creator: 李玉婷，2020-03-26 16:22:18，确认关联
function commodyRelatedSure() {
    var goodsId = $("#relatedGoods tbody tr:eq(1)").data("id");
    var pdId = $("#relatedProduct tbody tr:eq(1)").data("id");
    $.ajax({
        "url": "../product/confirmAssociation.do",
        "data": {
            "pdBaseId": pdId,
            "productId":goodsId
        },
        success: function (res) {
            bounce.cancel();
            bounce_Fixed.cancel();
            getWaitRelativeProList(1,20);
        }
    });
}
// creator: 李玉婷，2020-03-27 13:26:59，重新关联
function rechoosePoduct() {
    if(hasAuthority(0) == true) {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }else {
        choosePoduct(ObjElem);
    }
}
// creator: 李玉婷，2020-03-26 16:55:01，关联查看
var ObjElem = null;
function productCorrelationSee(obj) {
    ObjElem = obj;
    $("#productSee tbody tr:gt(0)").remove();
    $("#relatedRecord tbody tr:gt(0)").remove();
    $("#relatedGoodsSee tbody tr:gt(0)").remove();
    $("#relatedProductSee tbody tr:gt(0)").remove();
    var goodsInfo = obj.parents("tr").data("info");
    var goodsId = goodsInfo.id;
    var goodsHtml =
        '<tr>' +
        '    <td>'+ goodsInfo.outerSn +'</td>' +
        '    <td>'+ goodsInfo.outerName +'</td>' +
        '    <td>'+ goodsInfo.model +'</td>' +
        '    <td>'+ goodsInfo.specifications +'</td>' +
        '    <td>'+ goodsInfo.unit +'</td>' +
        '    <td>'+ goodsInfo.customerName +'</td>' +
        '    <td>'+ goodsInfo.createName + '&nbsp;&nbsp;' + new Date(goodsInfo.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
        '</tr>';
    $("#relatedGoodsSee tbody").append(goodsHtml);
    $("#productRelatedSee").data("id",goodsId);
    $.ajax({
        "url": "../product/getProductCorrelation.do",
        "data": {
            "productId":goodsId
        },
        success: function (res) {
            var data = res.msg;
            var product = data["pdBase"];
            var spList = data["productList"];
            var pdHtml =
                '<tr data-id="'+ product.id +'">' +
                '    <td>'+ product.innerSn +'</td>' +
                '    <td>'+ product.name +'</td>' +
                '    <td>'+ handleNull(product.model) +'</td>' +
                '    <td>'+ handleNull(product.specifications) +'</td>' +
                '    <td>'+ handleNull(product.unit) +'</td>' +
                '    <td>'+ charge(product.source,"source") +'</td>' +
                '    <td>'+ charge(product.composition,"composition") +'</td>' +
                '    <td>'+ handleNull(product.netWeight) +'</td>' +
                '    <td>'+ weightUnitCharge(product.weightUnit) +'</td>' +
                '    <td>'+ product.createName + '&nbsp;&nbsp;' + new Date(product.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                '</tr>';
            $("#relatedProductSee tbody").append(pdHtml);
            if (spList && spList.length >0){
                var str = '',relatedNum = 0;
                for(var a=0;a<spList.length;a++){
                    if (spList[a].id == goodsId){
                       $("#currName").html(spList[a].correlaterName);
                       $("#currDate").html(new Date(spList[a].correlateDate).format('yyyy-MM-dd hh:mm:ss'));
                    }else{
                        relatedNum++;
                        str +=
                            ' <tr>' +
                            '    <td>'+ spList[a].outerSn  +'</td>' +
                            '    <td>'+ spList[a].outerName  +'</td>' +
                            '    <td>'+ spList[a].model  +'</td>' +
                            '    <td>'+ spList[a].specifications  +'</td>' +
                            '    <td>'+ spList[a].unit  +'</td>' +
                            '    <td>'+ spList[a].customerName  +'</td>' +
                            '    <td>'+ spList[a].createName + '&nbsp;&nbsp;' + new Date(spList[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                            '    <td>'+ spList[a].correlaterName + '&nbsp;&nbsp;' + new Date(spList[a].correlateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                            '</tr>';
                    }
                }
                $("#relatedNum").html(relatedNum);
                $("#relatedRecord tbody").append(str);
                $("#productRelatedSee .relatedNumSee").html(relatedNum);
            }
            bounce.show($("#productRelatedSee"));
            $("#productSee tbody").append(pdHtml);
        }
    });
}
// creator: 李玉婷，2020-05-06 13:29:20，产品构成
function pdComposition(type) {
    switch (type){
        case 1:
            compositeEditBtn($("#productRelated"), 0);
            break;
        case 2:
            compositeEditBtn($("#productRelatedSee"), 0);
            break;
    }
}
// creator: 李玉婷，2022-02-11 15:47:01，单位转换
function weightUnitCharge(unit) {
    var result = '';
    if (handleNull(unit) != "") {
        if (unit == '1') {
            result = '毫克';
        } else if (unit == '2') {
            result = '克';
        } else if (unit == '3') {
            result = '千克';
        } else if (unit == '4') {
            result = '吨';
        }
    } else {
        result = '--';
    }
    return result;
}

//  create:hxz 2021-4-9 返回 按钮
function backPrePage(obj) {
    let page = obj.data("page");
    showMainCon(page) ;
}
// create : hxz 2021-1-34 显示主页面
function showMainCon(num){
    $(".mainCon").hide();$(".mainCon" + num).show();
}
//creator:lyt 2023/4/24 08:26 按产品展示关联情况
function getListByProduct(type, currPage){
    let data = {
        "pageSize": 20,
        "currPage": currPage,
        "param": '',
        "source": "",
        "composition": ""
    };
    if (type === 1) {
        showMainCon(2) ;
        $(".mainCon2 .lineQuery input").val("");
        $(".mainCon2 .lineQuery select").val("");
    } else if (type === 2) {
        showMainCon(3) ;
        data.param = $("#searchKeyBase").val();
    } else if (type === 3) {
        data.source = $("#bySource").val();
    } else if (type === 4) {
        data.composition = $("#byCompose").val();
    }
    let count = 0;
    $.ajax({
        "url": "../product/getPdBaseGuanLianList.do",
        "data": data,
        success: function (res) {
            //设置分页
            var totalPage = res["totalPage"];//总页数
            var curr = res["currPage"];//当前页
            var html = '';
            var list = res.data;
            if (list && list.length >0){
                count = res.totalRows;
                for(var i=0;i<list.length;i++){
                    html +=
                        '<tr>' +
                        '    <td>'+ list[i].innerSn +'</td>' +
                        '    <td>'+ list[i].name + ((handleNull(list[i].model) === "")?'':'、'+ list[i].model)+ ((handleNull(list[i].specifications) === "")?'':'、'+list[i].specifications)+'</td>' +
                        '    <td>' +
                        '       <span class="ty-color-blue" onclick="getPdRelationSee($(this))">'+ list[i].num +'种</span>' +
                        '       <span class="gapG">'+ list[i].productNames +'</span>' +
                        '       <span class="hd">'+ JSON.stringify(list[i]) +'</span>' +
                        '   </td>' +
                        '</tr>';
                }
            }
            if (type === 2) {
                $("#countNum").html(count);
                $("#searchProductList tbody").html(html);
                setPage( $("#ye_2") , curr ,  totalPage , "byProductSearch", JSON.stringify({"type": type}) );
            } else {
                $("#productList tbody").html(html);
                setPage( $("#ye_3") , curr ,  totalPage , "byProductSearch", JSON.stringify({"type": type}) );
            }
        }
    });
}
//creator:lyt 2023/4/24  7:40 所关联的商品XX种查看
function getPdRelationSee(obj){
    let page = $(".mainCon2").is(":visible") ? 2: 3;
    $(".relationSee").data("page",page)
    $("#byProductSee tbody tr:gt(0)").remove();
    $("#byProductRelated tbody tr:gt(0)").remove();
    var product = obj.siblings(".hd").html();
    if (product !== "") product = JSON.parse(product);
    $.ajax({
        "url": "../product/getProductListByPdBase.do",
        "data": {
            "pdBaseId": product.id
        },
        success: function (res) {
            var spList = res["list"];
            var str = '',relatedNum = 0;
            var pdHtml =
                '<tr>' +
                '    <td>'+ product.innerSn +'</td>' +
                '    <td>'+ product.name + (handleNull(product.model) !== "" ? '/' + handleNull(product.model): "")
                            + (handleNull(product.specifications) !== "" ? '/' + handleNull(product.specifications): "") +
                '    </td>' +
                '    <td>'+ handleNull(product.unit) +'</td>' +
                '    <td>'+ charge(product.source, "source") +'</td>' +
                '    <td>'+ charge(product.composition,"composition") +'</td>' +
                '    <td>'+ handleNull(product.netWeight) +'</td>' +
                '    <td>'+ weightUnitCharge(product.weightUnit) +'</td>' +
                '    <td>'+ product.createName + '&nbsp;&nbsp;' + new Date(product.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                '</tr>';
            if (spList && spList.length >0){
                for(var a=0;a<spList.length;a++){
                    relatedNum++;
                    str +=
                        ' <tr>' +
                        '    <td>'+ spList[a].outerSn  +'</td>' +
                        '    <td>'+ spList[a].outerName + (handleNull(spList[a].model) !== "" ? '/' + handleNull(spList[a].model): "")
                                    + (handleNull(spList[a].specifications) !== "" ? '/' + handleNull(spList[a].specifications): "") +'</td>' +
                        '    <td>'+ spList[a].unit  +'</td>' +
                        '    <td>'+ spList[a].customerName  +'</td>' +
                        '    <td>'+ spList[a].createName + '&nbsp;&nbsp;' + new Date(spList[a].createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ spList[a].correlaterName + '&nbsp;&nbsp;' + new Date(spList[a].correlateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '</tr>';
                }
                $("#byProductRelated tbody").append(str);
            }
            $("#relatedCountNum").html(relatedNum);
            $("#byProductSee tbody").append(pdHtml);
            showMainCon(4) ;
        }
    });
}
//creator:lyt 2023/4/25  下午 1:07 翻译键值
function charge(val , type) {
    var str = ""
    switch (type){
        case 'source':
            if(val == "1"){ str = "外购"; }
            if(val == "2"){ str = "自制"; }
            if(val == "3"){ str = "自制+外包"; }
            break;
        case 'composition':
            if(val == "1"){ str = "成品"; }
            if(val == "2"){ str = "装配"; }
            if(val == "3"){ str = "制造"; }
            if(val == "4"){ str = "制造"; }
            break;
        default:
            str = "";
            break;
    }
    return str
}