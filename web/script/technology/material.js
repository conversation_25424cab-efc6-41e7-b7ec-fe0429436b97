var pageSize = 20 ; // 分页的每页条数
var GSList = [] ; // 待选择的产品列表
var selectGS = [],notSelectGS = [] ;
$(function () {
    $("input").each(function(){
        var key = $(this).attr("name")
        $(this).attr("autocomplete",'off')
    });
    //获取主页数据
    getList(1, '');
    $(".ty-btn").click(function(){
        var type = $(this).data('type');
        switch (type){
            case "goMain": // 返回材料首页
                goMainCon(1);
                var cur = $("#yeCon1 .yecur").html();
                var key = $(".search input").val();
                getList(cur, key);
                break;
            case "gopre2": // 返回上一页
                goMainCon(2);
                break;
            case "goPre3": // 返回上一页
                goMainCon(3);
            case "goPre69": // 返回上一页
                var num = $(this).data("num");
                goMainCon(num);
            case "goback2": // 返回上一页
                var num = $(this).data("num");
                goMainCon(num);
                break;
            case "goNext": // 下一步
                var selectMt = $("#goNext").data("selectMt") ;
                if(selectMt == ""){
                    layer.msg('请先选择材料！');
                    return false;
                }else{
                    getPdMtBaseListNoPage();
                    goMainCon(4);
                    var item = JSON.parse(selectMt) ;
                    var str = "<tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "        <span data-type=\"mtScan\" class=\"btn ty-color-blue \">查看</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>" ;
                    $(".mainCon4 tbody:eq(0)").children(":gt(0)").remove();
                    $(".mainCon4 tbody:eq(0)").append(str);
                }
                break;
        }
    });
    // mainCon4 二级导航
    $(".mainCon4 .ty-secondTab").children().click(function () {
        toggleSelect($(this).index()) ;
    })
    $(".btnCat").click(function(){
        var type = $(this).data('type');
        switch (type){
            case "addUnit": // 新增材料
                addUnit();
                break;
            case "goSelect": // 去处理
                goMainCon(2);
                getPdMtBaseList(1);
                break;
            case "goStopMt": // 去查看已停用材料
                goMainCon(5);
                getStopMtBaseList(1);
                break;
            case "addMtBtn": // 新增材料
                $("#mtEntry .bonceHead span").html("材料录入");
                bounce.show($("#mtEntry"));
                $("#mtEntry .case2").show().siblings().hide();
                $(".isCurrent .fa").attr("class","fa fa-circle-o");
                $("#mtEntry input[name='name']").attr("onclick","");
                $("#mtEntry input").val("");
                $("#mtEntry input").removeAttr("disabled");
                $("#mtEntry select").removeAttr("disabled");
                $("#mtEntry .textMax").html("0/100");
                getUnitList($("#unitSelect"), 8);
                break;
            case "searchBtn": // 搜索材料
                var key = $(".search input").val();
                getList(1, key);
                break;
            case "chooseByMt": // 去试试
                goMainCon(3);
                $("#goNext").data("selectMt", "");
                getMtBaseList(1);
                break;
            case "selectMtOkBtn": // 挑选完毕，确定
                if(selectGS.length > 0){
                    bounce.show($("#selectTip"))
                }else{
                    layer.msg("请至少选择一项！")
                }
                break;
            case "mtUesdGs": // 曾使用过该材料的产品及零件
                goMainCon(8)
                var str = $(".mainCon6 tbody:eq(0)").children("tr:eq(1)").html();
                var id = $(".mainCon6 tbody:eq(0)").data('mtID');
                $(".mainCon8 tbody:eq(0)").children("tr:eq(1)").html(str);
                $(".mainCon8 tbody:eq(0)").data('mtID', id);
                $("#goback2").data("num", "6");
                getBeforeBindingPdList(1)
                break;
        }
    });
    $(".isCurrent").on('click', ".fa", function () {
        $(this).attr("class", "fa fa-dot-circle-o").siblings().attr("class","fa fa-circle-o");
        var num = $(this).data("num");
        $("input[name='isCurrent']").val(num);
    });
    $(".terminateOrders").on('click', ".fa", function () {
        $(this).attr("class", "fa fa-dot-circle-o").siblings().attr("class","fa fa-circle-o");
        var num = $(this).data("num");
        $("input[name='terminateOrders']").val(num);
    });
    $("tbody").on("click" , "span.btn" , function () {
        var type = $(this).data("type") ;
        switch (type){
            case "mtScan": // 查看
                bounce.show($("#mtScan"));
                $("#stopTime").html("");
                $(".catCon").hide();
                var info = JSON.parse($(this).siblings(".hd").html());
                var num = $("input[name='mainConShow']").val();
                if(num == 1 || num == ""){
                    $("#editMtBtn").data("info", JSON.stringify(info)).show();
                    $("#startEndLog").hide();
                }else if(num == 5){
                    $("#editMtBtn").hide();
                    $("#startEndLog").show();
                    $.ajax({
                        "url":"../productMaterial/getStopMtBaseData.do",
                        "data":{ "id": info['id'] },
                        success:function(res) {
                            info = res['data'];
                            $("#stopTime").html(info['updateName'] + "已于" + (new Date(info['updateDate']).format("yyyy-MM-dd hh:mm:ss")) + "将本材料“停用”！");
                            $(".catCon").show();
                            $(".scanMt_cat").html(info['categoryName']);
                        }
                    })
                }else{
                    $("#editMtBtn").hide();
                    $("#startEndLog").hide();
                }
                for(var key in info){
                    if(key == 'create_name' || key == 'create_date' ){

                    }else{
                        $(".scanMt_" + key).html(info[key]);
                    }
                }
                $(".scanMt_create").html(info['createName'] + " " + (new Date(info['createDate']).format('yyyy-MM-dd hh:mm:ss')));
                break;
            case "mtStop": // 暂停使用
                var info = JSON.parse($(this).siblings(".hd").html());
                bounce.show($("#stopTip"));
                $("#stopTip .msg").html("<p>操作成功后，该材料在系统中将无法被选择。</p><p>确定停用该材料？</p>");
                $("#stopTip").data("mtID", info.id);
                $("#stopTip").data("state", 0);
                break;
            case "mtStart": // 恢复使用
                var info = JSON.parse($(this).siblings(".hd").html());
                bounce.show($("#stopTip"));
                $("#stopTip .msg").html("<p>操作成功后，再下采购订单时将可选择该材料。</p><p>确定恢复使用该材料？</p>");
                $("#stopTip").data("mtID", info.id);
                $("#stopTip").data("state", 1);
                break;
            case "mtDel":  // 删除
                var info = JSON.parse($(this).siblings(".hd").html());
                bounce.show($("#delTip"));
                $("#delTip").data("mtID", info.id).data("type", info.enabled);
                break;
            case "gsScan":  // 产品查看
                bounce.show($("#scanGS"));
                var info = JSON.parse($(this).siblings(".hd").html());
                for(var key in info){
                    if(key == "phrase"){
                        $("#scanGS ." + key).html(charge('phrase', info[key]));
                    }else{
                        $("#scanGS ." + key).html(info[key]);
                    }
                }
                $("#scanGS .create").html(info['createName'] + (new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss"))) ;
                break;
            case "gsSelect":  // 产品去处理
                var info = JSON.parse($(this).siblings(".hd").html());
                $("#mtEntry .bonceHead span").html("选择/录入材料");
                bounce.show($("#mtEntry"));
                $("#mtEntry input").val("");
                $("#mtEntry input").removeAttr("disabled");
                $("#mtEntry select").removeAttr("disabled");
                $("#mtEntry .textMax").html("0/100");
                $("#mtEntry .case1").show().siblings().hide();
                $("#mtEntry .case1 .red").html(info.name);
                $("#mtEntry .case1 .changeMtTtl").hide();
                $("#mtEntry input[name='product_']").val(info.id);
                $("#mtEntry input[name='name']").attr("onclick","startMatch(event)");
                $.ajax({
                    "url": "../productMaterial/getMtBaseListNoPage.do",
                    success:function (res) {
                        var list = res['list'], str = "" ;
                        if(list){
                            for(var i = 0 ; i < list.length ; i++){
                                str += "<option value='"+ JSON.stringify(list[i]) +"'>"+ list[i]['name'] +"</option>"
                            }
                        }
                        $("#selectMt").html(str);
                    }
                })
                getUnitList($("#unitSelect"),8);
                break;
            case "changeMt":  // 更换材料
                var info = JSON.parse($(this).siblings(".hd").html());
                $("#mtEntry .bonceHead span").html("更换材料");
                bounce.show($("#mtEntry"));
                $("#mtEntry .case1").show().siblings().hide();
                $("#mtEntry .case1 .red").html(info.name);
                $("#mtEntry .case1 .changeMtTtl").show();
                $("#mtEntry input").val("");
                $("#mtEntry .textMax").html("0/100");
                $("#mtEntry input[name='product_']").val(info.product);
                $("#mtEntry input[name='name']").attr("onclick","startMatch(event)");
                $.ajax({
                    "url": "../productMaterial/getMtBaseListNoPage.do",
                    success:function (res) {
                        var list = res['list'], str = "" ;
                        if(list){
                            for(var i = 0 ; i < list.length ; i++){
                                str += "<option value='"+ JSON.stringify(list[i]) +"'>"+ list[i]['name'] +"</option>"
                            }
                        }
                        $("#selectMt").html(str);
                    }
                })
                getUnitList($("#unitSelect"),8);
                break;
            case "changeMtLog":  // 材料更换记录
                var num = $("input[name='mainConShow']").val();
                goMainCon(7);
                $("#goPre69").data("num", num);
                var item = JSON.parse($(this).siblings(".hd").html());
                var str =   "    <td>"+ item['innerSn'] +"</td>" +
                             "    <td>"+ item['name'] +"</td>" +
                             "    <td>"+ item['model'] +"</td>" +
                             "    <td>"+ item['specifications'] +"</td>" +
                             "    <td>"+ item['unit'] +"</td>" +
                             "    <td>"+ (item['createName']) + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                             "    <td>" +
                             "       <span data-type=\"gsScan\" class=\"btn ty-color-blue\">查看</span>" +
                             "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                             "    </td>" ;
                $(".mainCon7 tbody:eq(0)").children("tr:eq(1)").html(str);
                getMtListByPdBase(1);
                break;
            case "mtUesdGs":  // 已停用的材料 - 曾使用过该材料的产品及零件
                goMainCon(8);
                var item = JSON.parse($(this).parent().next().children(".hd").html());
                var str =  "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ item['code'] +"</td>" +
                    "    <td>"+ item['model'] +"</td>" +
                    "    <td>"+ item['specifications'] +"</td>" +
                    "    <td>"+ item['unit'] +"</td>" +
                    "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                $(".mainCon8 tbody:eq(0)").children("tr:eq(1)").html(str);
                $(".mainCon8 tbody:eq(0)").data('mtID', item['id']);
                $("#goback2").data("num", "5");
                getBeforeBindingPdList(1);
                break;
            case "mtUsingGs":  // 使用该材料的产品及零件
                goMainCon(6);
                var item = JSON.parse($(this).parent().next().children(".hd").html());
                var str =  "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ item['code'] +"</td>" +
                    "    <td>"+ item['model'] +"</td>" +
                    "    <td>"+ item['specifications'] +"</td>" +
                    "    <td>"+ item['unit'] +"</td>" +
                    "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" ;
                $(".mainCon6 tbody:eq(0)").children("tr:eq(1)").html(str);
                $(".mainCon6 tbody:eq(0)").data('mtID', item['id']);
                getBindingPdList(1);
                break;

        }



    })
    $("#editMtBtn").click(function () {
        editMtBtn();
    })
    $(".mainCon3 tbody").on("click",".fa", function () {
        var hasOk = $(this).hasClass("fa-dot-circle-o");
        if(hasOk){
            $(this).attr("class","fa fa-circle-o");
            $("#goNext").data("selectMt", "");
        }else{
            $(".mainCon3 tbody .fa").attr("class","fa fa-circle-o") ;
            $(this).attr("class","fa fa-dot-circle-o");
            $("#goNext").data("selectMt", $(this).parent().siblings(":last").children(".hd").html());
        }
    });
    $(".mainCon4 table").on('click', '.fa', function(){
        var klass = $(this).hasClass('fa-check-square-o');
        var item = $(this).parent().siblings(":last").find(".hd").html();
        item = JSON.parse(item) ;
        if(klass){
            $(this).attr("class" , "fa  fa-square-o");
            setKeyByID(item.id ,"selected", 0);
        }else{
            $(this).attr("class" , "fa  fa-check-square-o");
            setKeyByID(item.id ,"selected", 1);
        }
        countGsList();
        $(this).parent().parent().remove();

    });
    // 材料录入 选中
    $("#selectMt").on('click', 'option', function(){
        setSelectOption($(this));
        $("#selectMt").hide();
        $(".bounce").stopTime("mtMatch")
    });
});
// create：hxz 2020-08-26 更换材料提示 取消
function changeMtTipCancel() {
    bounce_Fixed.cancel();
    $("input[name='isCurrent']").val("");
}
// 选择录入材料
function mtEditOk(typeStr) {
    var caseNum = 0;
    if( $("#mtEntry .case1").is(":visible")){ caseNum = 1 ; }
    if( $("#mtEntry .case2").is(":visible")){ caseNum = 2 ; }
    if( $("#mtEntry .case3").is(":visible")){ caseNum = 3 ; }
    var data = {};
    $("#mtEntry [need]").each(function(){
        var key = $(this).attr("name");
        data[key] = $(this).val();
    });
    if(!data['name']){ layer.msg("材料名称为必填项！"); return false; }
    if(!data['code']){ layer.msg("材料代号为必填项！"); return false; }
    if(!data['unitId']){ layer.msg("计量单位为必填项！"); return false; }
    var url = "../productMaterial/addCompositionMaterial.do" ;
    var ttl = $("#mtEntry .bonceHead span").html();
    if(caseNum === 1){ // 选择或录入材料  更换材料
        if(!data['product_']){
            layer.msg("未获取产品信息");  return false;
        }
        if(data['id'] == "" && !data['isCurrent']){
            bounce_Fixed.show($("#mtAddTip"));
            $(".isCurrent .fa").attr("class","fa fa-circle-o");
            return false
        }
        if(ttl == "更换材料" && typeStr !== "changeOk"){
            bounce_Fixed.show($("#changeMtTip"));
            $("input[name='isCurrent']").val("");
            $(".isCurrent .fa").attr("class","fa fa-circle-o");
            return false;
        }
    }else if(caseNum === 2){ // 新增材料
        if(!data['isCurrent']){ layer.msg("请确认是否需要库管员填写本材料的当前库存数量！"); return false; }
    }else if(caseNum === 3){ // 修改材料
        url = "../productMaterial/updateMaterial.do" ;
        if (!data['terminateOrders']){ layer.msg("请确认采购人员需要终止未完结的采购订单！"); return false; }
    }else{
        layer.msg("无法判断情况");
        return false;
    }
    $.ajax({
        "url": url ,
        "data":data,
        success:function (res) {
            bounce.cancel();
            bounce_Fixed.cancel();
            if(res['code'] == 200){
                layer.msg("操作成功");
            }else{
                layer.msg(res['msg']);
            }
            if(caseNum === 1){ // 选择或录入材料
                if(ttl == "更换材料"){
                    var cur = $("#yeCon6 .yecur").html();
                    getBindingPdList(cur);
                }else{
                    var cur = $("#yeCon2 .yecur").html();
                    getPdMtBaseList(cur);
                }

            }else if(caseNum === 2){ // 新增材料
                var cur = $("#yeCon1 .yecur").html();
                var key = $(".search input").val();
                getList(cur,key);
            }else if(caseNum === 3){ // 修改材料
                var cur = $(".mainCon1 .yecur").html();
                var key = $(".search input").val();
                getList(cur,key)
            }
        }
    })
}

// create:hxz 2020-06-25 根据材料获取已绑产品列表
function getBindingPdList(cur) {
    var id = $(".mainCon6 tbody:eq(0)").data('mtID');
    $.ajax({
        "url":"../productMaterial/getBindingPdList.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "id":id },
        success:function (res) {
            var totalRows = res['totalRows'];      //
            var totalPage = res['totalPage'];      //
            var usedNum = res['beforeNum'];      //

            $(".mainCon6 .usedNum").html(usedNum);
            $(".mainCon6 .totalRows").html(totalRows);
            $(".mainCon6 tbody:eq(1)").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['innerSn'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ (item['updateName'] || item['createName']) + " " + (new Date(item['updateDate'] || item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "       <span data-type=\"changeMt\" class=\"btn ty-color-blue\">更换材料</span>" +
                        "       <span data-type=\"changeMtLog\" class=\"btn ty-color-blue\">材料更换记录</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon6 tbody:eq(1)").append(str);
            setPage($("#yeCon6") , cur , totalPage, "BindingPdList", "");
        }
    })
}
// create:hxz 2020-06-27 零部件匹配
function startMatch(e){
    e.stopPropagation();
    $(".bounce").everyTime('0.5s',"mtMatch",function(){
        mtMatch();
    });
}
// create:hxz 2020-06-25 与录入匹配
function mtMatch() {
    $("#selectMt").show();
    var innerEntry = $("#mtEntry [name='name']").val();
    $("#selectMt option").hide();
    var hasMatch = false;
    $("#selectMt option").each(function () {
        var i_innerSn = $(this).html();
        if(i_innerSn.indexOf(innerEntry) != -1){
            $(this).show();
        }
        if(i_innerSn === innerEntry){
            hasMatch = true;
            setSelectOption($(this));

        }
    });
    if(!hasMatch){
        $("#mtEntry input:visible:not([name='name'])").val("").removeAttr("disabled");
        $("#unitSelect").val("").removeAttr("disabled");
        $("#addUnitBtn").show();
        $("#mtEntry input[name='id']").val("") ;
        $("#mtEntry .textMax").html("0/100")
    }
}
function setSelectOption(thisObj) {
    var i_item = thisObj.val();
    i_item = JSON.parse(i_item);
    $("#mtEntry [name='id']").val(i_item['id']);
    for(var key in i_item){
        if(key != "name"){
            $("#mtEntry [name='"+ key +"']").val(i_item[key]).attr("disabled", "true") ;
        }
    }
    $("#addUnitBtn").hide();
    $("#mtEntry [name='name']").val(i_item['name']);
    $("#mtEntry .textMax").html( i_item['memo'].length + "/100");
}
function stopMtch(e){
    $("#selectMt").hide();
    $(".bounce").stopTime("mtMatch")
}


// create:hxz 2020-06-21 设置商品键值对
function setKeyByID(id , key , val) {
    var list = GSList ;
    for(var i = 0 ; i < list.length ; i++){
        if(list[i]['id'] == id){
            list[i][key] = val ;
        }
    }
}
// create:hxz 2020-06-22  计算选中未选中的数据
function countGsList(){
    var list = [] , list2 = [] ;
    for(var j = 0 ; j < GSList.length ; j++){
        if(GSList[j]["selected"] == 1){
            list.push(GSList[j]);
        }else{
            list2.push(GSList[j]);
        }
    }
    selectGS = list ;
    notSelectGS = list2 ;
    $(".mainCon4 .ty-secondTab").children("li:eq(0)").find("span").html(notSelectGS.length);
    $(".mainCon4 .ty-secondTab").children("li:eq(1)").find("span").html(selectGS.length);
}
// create :hxz 2020-7-20 获取待选择产品列表(无分页)
function getPdMtBaseListNoPage(){
    $.ajax({
        "url":"../productMaterial/getPdMtBaseListNoPage.do",
        success:function (res) {
            var list = res['data'], str = "" ;
            GSList = list ;
            countGsList();
            $(".mainCon4 .ty-secondTab li:eq(0)").click();

        }
    })
}
// create:hxz 2020-07-22  maincon4 渲染表格数据
function setPageData(cur, per) {
    var list = [] , faStr = "" ;
    $(".mainCon4 tbody:eq(1)").children(":gt(0)").remove();
    var navIndex = $(".mainCon4 .ty-secondTab li").index($(".mainCon4 .ty-secondTab .ty-active"));
    if(navIndex == 0){
        faStr = "<td><span class=\"fa fa-square-o\"></span></td>";
        list = notSelectGS ;
    }else if(navIndex == 1){
        faStr = "<td><span class=\"fa fa-check-square-o\"></span></td>";
        list = selectGS ;
    }
    var str = "" ;
    var start = (cur-1)*per ;
    var end = start + per ;
    if(list){
        for(var i = start ; i < end ; i++){
            var item = list[i];
            if(item){
                str +=" <tr>" + faStr +
                    "    <td>"+ item['innerSn'] +"</td>" +
                    "    <td>"+ item['name'] +"</td>" +
                    "    <td>"+ item['model'] +"</td>" +
                    "    <td>"+ item['specifications'] +"</td>" +
                    "    <td>"+ item['unit'] +"</td>" +
                    "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                    "    <td>" +
                    "        <span class=\"btn ty-color-blue\" data-type=\"gsScan\">查看</span>" +
                    "       <span class=\"hd\">"+ JSON.stringify(item) +"</span>" ;
                    "    </td>" +
                    "</tr>";
            }
        }
    }
    $(".mainCon4 tbody:eq(1)").append(str);
    var all = parseInt(list.length / per ) ;
    if(list.length % per){ all++; }
    setPageGs( $("#yeCon4") , cur , all, 'setPageData', pageSize )

}
// create:hxz 2020-06-18  maincon2 渲染分页数据
function setPageGs(obj , cur , countall , cbStr , perNum ){
    var all = Number(countall);
    cur = Number(cur);
    if( all == 0){ all = 1 ; }
    if( cur == 0){ cur = 1 ; }
    var div_obj = obj;
    div_obj.html("");
    var str = "<div class='yeCon'>";
    if(all > 8){
        var star = cur - 3; // 开始页
        var n = 0;
        var end = parseInt(cur) + 3;
        if( star < 1 ){ star = 1; n = 3 - cur; end += n ;  }
        if( end > all ){ end = all ; }
        if( end - star < 5 ){ star = end - 5 ; }
        if( star < 1 ){ star = 1 ; }

        if( cur != 1 ){ str += "<div class='ye' onclick='"+ cbStr +"(1, "+ perNum +")'> 首页 </div>"; }
        if( star > 1 ){ str = str + "<span style='padding:5px; ' ></span>....<span style='padding:5px; ' ></span>"; }
        for (var i = star; i <= end; i++) {
            if( i == cur ){ str += "<div class='yecur'>"+i+"</div>"; }
            else {
                str += "<div class='ye' onclick='"+ cbStr +"("+ i +", "+ perNum +")'>" + i + "</div>";
            }
        }
        if( end < all ){ str = str + "<span style='padding:5px; ' ></span>....<span style='padding:5px; ' ></span>"; }
        if( cur != all ){ str += "<div class='ye' onclick='"+ cbStr +"("+ countall +", "+ perNum +")'> 尾页 </div>"; }

    }else{
        for (var i = 1; i <= all; i++) {
            if(i==cur){
                str=str+"<div class='yecur'>"+i+"</div>";
            }else{
                str=str+"<div class='ye' onclick='"+ cbStr +"("+ i +", "+ perNum +")'>"+i+"</div>";
            }
        }
    }
    // json = json || "{}";
    str=str+"<div class='yeC'>共"+all+"页</div>"+
        "<div class='yeC'>当前第"+cur+"页</div></div>";
    // "<div class='hd type'>"+ Typestr +"</div>"+
    // "<div class='hd json'>"+ json +"</div>";
    div_obj.append(str);
}
// create :hxz 2020-7-20 去试试 获取材料列表
function getMtBaseList(cur){
    $.ajax({
        "url":"../productMaterial/getMtBaseList.do",
        "data":{ "currPage":cur , "pageSize":pageSize },
        success:function (res) {
            var totalRows = res['totalRows']      //如下X种材料与产品或零件有关
            var totalPage = res['totalPage']      //总页数
            $(".mainCon3 .totalRows").html(totalRows);
            $(".mainCon3 tbody").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td><i class=\"fa fa-circle-o\"></i></td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "        <span data-type=\"mtScan\" class=\"btn ty-color-blue \">查看</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>" ;

                }
            }
            $(".mainCon3 tbody").append(str);
            setPage($("#yeCon3") , cur , totalPage, "techMtBase", '');
        }
    })
}
// create :hxz 2020-7-19 获取主页数据
function getList(cur, keyword){
    $.ajax({
        "url":"../productMaterial/getHomeData.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "keyword":keyword },
        success:function (res) {
            var pdNum     = res['pdNum']      //现有X种“由所购买的单一材料直接加工而成”的产品或零件有待选择材料
            var stopNum   = res['stopNum']      //已停用的材料有X种
            var totalRows = res['totalRows']      //如下X种材料与产品或零件有关
            var totalPage = res['totalPage']      //总页数
            $(".mainCon1 .pdNum").html(pdNum);
            $(".mainCon1 .stopNum").html(stopNum);
            $(".mainCon1 .totalRows").html(totalRows);
            $(".mainCon1 tbody").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ (item['unit'] || "") +"</td>" +
                        "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td class=\"ty-td-control\"><span data-type=\"mtUsingGs\" class=\"btn ty-color-blue \">"+ item['num'] +"种</span></td>" +
                        "    <td>" +
                        "        <span data-type=\"mtScan\" class=\"btn ty-color-blue \">查看</span>" +
                        "        <span data-type=\"mtStop\" class=\"btn ty-color-blue \">暂停使用</span>" +
                        "        <span data-type=\"mtDel\" class=\"btn ty-color-red \">删除</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon1 tbody").append(str);
            var json = JSON.stringify({"keyword": keyword}) ;
            setPage($("#yeCon1") , cur , totalPage, "techMt", json);
        }
    })
}
// create :hxz 2020-7-19 获取待选择产品列表
function getPdMtBaseList(cur){
    $.ajax({
        "url":"../productMaterial/getPdMtBaseList.do",
        "data":{ "currPage":cur , "pageSize":pageSize },
        success:function (res) {
            var totalRows = res['totalRows'];      //X种“由所购买的单一材料直接加工而成”的产品或零件有待选择材料
            var totalPage = res['totalPage'];      //

            $(".mainCon2 .totalRows").html(totalRows);
            $(".mainCon2 tbody").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['innerSn'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ item['createName'] + " " + (new Date(item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "       <span data-type=\"gsScan\" class=\"btn ty-color-blue\">查看</span>" +
                        "       <span data-type=\"gsSelect\" class=\"btn ty-color-blue\">去处理</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon2 tbody").append(str);
            setPage($("#yeCon2") , cur , totalPage, "techPd", "");
        }
    })
}

// create :hxz 2020-7-19 根据已停用材料获取曾经绑定的产品
function getBeforeBindingPdList(cur){
    var id = $(".mainCon8 tbody:eq(0)").data('mtID');
    $.ajax({
        "url":"../productMaterial/getBeforeBindingPdList.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "id": id },
        success:function (res) {
            var totalRows = res['totalRows'];      //X种“由所购买的单一材料直接加工而成”的产品或零件有待选择材料
            var totalPage = res['totalPage'];      //

            $(".mainCon8 .totalRows").html(totalRows);
            $(".mainCon8 tbody:eq(1)").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['innerSn'] +"</td>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ (item['updateName'] || item['createName']) + " " + (new Date(item['updateDate'] || item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" +
                        "       <span data-type=\"changeMtLog\" class=\"btn ty-color-blue\">材料更换记录</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon8 tbody:eq(1)").append(str);
            setPage($("#yeCon8") , cur , totalPage, "BeforeBindingPdList", "");
        }
    })
}
// create :hxz 2020-6-30 获取材料变更记录
function getMtListByPdBase(cur){
    var info = JSON.parse($(".mainCon7 tbody:eq(0)").children("tr:eq(1)").children(":last").find(".hd").html());
    var id = info['product'];
    $.ajax({
        "url":"../productMaterial/getMtListByPdBase.do",
        "data":{ "currPage":cur , "pageSize":pageSize, "id": id },
        success:function (res) {
            var totalRows = res['totalRows'];      //X种“由所购买的单一材料直接加工而成”的产品或零件有待选择材料
            var totalPage = res['totalPage'];      //

            // $(".mainCon7 .totalRows").html(totalRows);
            $(".mainCon7 tbody:eq(1)").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['innerSn'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ (item['updateName'] || item['createName']) + " " + (new Date(item['updateDate'] || item['createDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>" + charge("operation", item['operation'])+ "</td>" +
                        "</tr>";
                }
            }
            $(".mainCon7 tbody:eq(1)").append(str);
            setPage($("#yeCon7") , cur , totalPage, "MtListByPdBase", "");
        }
    })
}

// create :hxz 2020-6-29 获取获取已停用材料列表
function getStopMtBaseList(cur){
    $.ajax({
        "url":"../productMaterial/getStopMtBaseList.do",
        "data":{ "currPage":cur , "pageSize":pageSize },
        success:function (res) {
            var totalRows = res['totalRows'];      //X
            var totalPage = res['totalPage'];      //

            $(".mainCon5 .totalRows").html(totalRows);
            $(".mainCon5 tbody").children(":gt(0)").remove();
            var list = res['data'], str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ item['updateName'] + " " + (new Date(item['updateDate']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td class=\"ty-td-control\"><span data-type=\"mtUesdGs\" class=\"btn ty-color-blue \">"+ item['num'] +"种</span></td>" +
                        "    <td>" +
                        "       <span data-type=\"mtScan\" class=\"btn ty-color-blue \">查看</span>\n" +
                        "        <span data-type=\"mtStart\" class=\"btn ty-color-blue \">恢复使用</span>\n" +
                        "        <span data-type=\"mtDel\" class=\"btn ty-color-red \">删除</span>" +
                        "        <span class=\"hd\">"+ JSON.stringify(item) +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }
            $(".mainCon5 tbody").append(str);
            setPage($("#yeCon5") , cur , totalPage, "StopMtBaseList", "");
        }
    })
}

// creater :hxz 2020-06-05 停启用材料
function mtStopOk() {
    var mtID = $("#stopTip").data("mtID");
    var state = $("#stopTip").data("state");
    $.ajax({
        "url":"../productMaterial/startStopMt.do",
        "data":{  "id": mtID , "state":state },
        success:function (res) {
            bounce.cancel();

            if(res['code'] == 200){
                layer.msg("操作成功");
                if(state == 0){ // 停用的
                    var curPage = $("#yeCon1 .yecur").html();
                    var key = $(".search input").val();
                    getList(curPage, key);
                }else{
                    var curPage = $("#yeCon5 .yecur").html();
                    getStopMtBaseList(curPage );
                }
            }else{
                var str =  "操作失败，因为还有使用该材料的产品、零件或配方！" ;
                $("#iknow .msg").html(str);
                bounce.show( $("#iknow"))
            }
        }
    })
}
// creater :hxz 2020-06-05 删除材料
function mtDelOk() {
    var mtID = $("#delTip").data("mtID");
    var type = $("#delTip").data("type");
    $.ajax({
        "url":"../productMaterial/deleteMt.do",
        "data":{  "id": mtID },
        success:function (res) {
            bounce.cancel();
            if(res['code'] == 200){
                layer.msg("操作成功");
                if(type == "1"){
                    var curPage = $("#yeCon1 .yecur").html();
                    var key = $(".search input").val();
                    getList(curPage, key);
                }else {
                    var curPage = $("#yeCon5 .yecur").html();
                    getStopMtBaseList(curPage);
                }

            }else{
                var str = "删除失败，因为此材料在系统中已有与采购有关的数据！" ;
                if(false){
                    str =  "操作失败，因为还有使用该材料的产品、零件或配方！" ;
                }
                $("#iknow .msg").html(str);
                bounce.show( $("#iknow"))
            }
        }
    })
}
// creater :hxz 2020-06-05 修改材料的基本信息
function editMtBtn() {
    bounce.show($("#mtEntry"));
    $("#mtEntry .case3").show().siblings().hide();
    $("#mtEntry .bonceHead span").html("材料修改");
    $("#mtEntry input[name='name']").attr("onclick","");
    $("#mtEntry .textMax").html("0/100");
    $(".terminateOrders .fa").attr("class","fa fa-circle-o");
    var info = JSON.parse($("#editMtBtn").data("info"));
    getUnitList($("#unitSelect"), 8, info['unitId']);
    $("#mtEntry input").removeAttr("disabled");
    $("#unitSelect").removeAttr("disabled");
    for(var key in info){
        if(key == 'create_name' || key == 'create_date' ){

        }else{
            $(".scanMt_" + key).html(info[key]);
            $("#mtEntry input[name='"+ key +"']").val(info[key]);
        }
    }
    $.ajax({
        "url":"../productMaterial/getOrderNum.do",
        "data" :{ "id": info['id'] },
        success:function (res) {
            var orderNum = res['data'];
            $("#mtEntry .orderNum").html(orderNum);
        }
    })
}
// creater :hxz 2020-06-05 确定选择好产品
function selectMt() {
    var item = JSON.parse( $("#goNext").data("selectMt") ) ;
    var mtId = item.id , baseList = [];
    var list = selectGS ;
    for(var i = 0 ; i < list.length ; i++){
        baseList.push(list[i]["id"]);
    }
    baseList = baseList.toString();
    $.ajax({
        "url":"../productMaterial/bindOperation.do",
        "data":{ "baseList":baseList, "mtId":mtId },
        success:function (res) {
            bounce.cancel();
            if(res.code == 200){
                layer.msg("操作成功");
            }else{
                layer.msg("操作失败");
            }
            goMainCon(1);
            getList(1, '');
        }
    })
}
// creater :hxz 2020-06-05 切换选中未选择
function toggleSelect(state) {
    $(".mainCon4 .ty-secondTab").children().removeClass('ty-active');
    $(".mainCon4 .ty-secondTab").children(":eq("+ state +")").addClass('ty-active');
    $(".mainCon4 .select"+state).show().siblings().hide();
    setPageData(1, pageSize );

}
// creater :hxz 2020-06-05 切换页面
function goMainCon(num) {
    $("input[name='mainConShow']").val(num);
    $(".mainCon" + num).show().siblings().hide();
}
// creator: hxz 2020-08-25 转换值
function charge(type , val) {
    var str = "";
    switch (type){
        case "operation": //1选的材料 3变更材料
            if (val == 1){
                str = "选定材料";
            }else if(val == 3){
                str = "变更材料";
            }
            break;
        case 'phrase':
            if (val == 1){
                str = "开发中";
            }else if(val == 2){
                str = "开发完成";
            }
            break
    }
    return str;
}
// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.siblings(".textMax").text(curLength + '/' + max);
}

// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectVal) {
    // 1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    if(selectVal == item['id']){
                        str += '<option selected value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }else{
                        str += '<option value="'+ item['id'] +'">'+ item['name'] +'</option>';
                    }
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41，新增计量单位
function addUnit() {
    bounce_Fixed.show($("#addUnit"));
    $("#addUnit input").val("");
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed.cancel();
                getUnitList($("#unitSelect"), module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed.show($("#tip1"));
            }
        }
    })
}

// creator: 李玉婷，2020-03-31 12:13:08，录入- 计量单位设置
function unitAssign(obj) {
    var unitName = obj.children("option:selected").html();
    obj.siblings("input").val(unitName)
}

