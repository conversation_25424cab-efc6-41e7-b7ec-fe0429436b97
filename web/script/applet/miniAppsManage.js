$(function() {
    $("body").on("click",".funBtn, .linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    });
    $("#newApp input[name='codeType']").on('click',function (event){
        let flag = $(this).prop("checked");
        let val = 0;
        if (flag) {val = $(event.target).val();}
        $('.companyCodeData .code' + val).show().siblings().hide();
    })
});
//creator:lyt 2023/1/3 上午 8:12 与Wonderss绑定小程序
function bindApplet(){
    $("#bindApplet input[type=\"radio\"]").prop('checked',false)
    bounce.show($("#bindApplet"));
}
//creator:lyt 2023/1/3 上午 8:12 与Wonderss绑定小程序-下一步
function bindAppletNext(){
    let val = $('#bindApplet input:checked').val();
    if (val === '1') {
        $('.companyCodeData .code0').show().siblings().hide();
        document.getElementById('newApp').reset()
        bounce.show($("#registerWeChatApp"));
    } else {
        bounce.cancel();
    }
}

