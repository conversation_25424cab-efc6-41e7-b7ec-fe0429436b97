!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Qs=t()}(function(){return function o(n,i,a){function p(e,t){if(!i[e]){if(!n[e]){var r="function"==typeof require&&require;if(!t&&r)return r(e,!0);if(c)return c(e,!0);throw(t=new Error("Cannot find module '"+e+"'")).code="MODULE_NOT_FOUND",t}r=i[e]={exports:{}},n[e][0].call(r.exports,function(t){return p(n[e][1][t]||t)},r,r.exports,o,n,i,a)}return i[e].exports}for(var c="function"==typeof require&&require,t=0;t<a.length;t++)p(a[t]);return p}({1:[function(t,e,r){"use strict";var o=String.prototype.replace,n=/%20/g,i="RFC1738",a="RFC3986";e.exports={default:a,formatters:{RFC1738:function(t){return o.call(t,n,"+")},RFC3986:function(t){return String(t)}},RFC1738:i,RFC3986:a}},{}],2:[function(t,e,r){"use strict";var o=t("./stringify"),n=t("./parse"),t=t("./formats");e.exports={formats:t,parse:n,stringify:o}},{"./formats":1,"./parse":3,"./stringify":4}],3:[function(t,e,r){"use strict";function c(t,e){var r,o,n,i,a={},t=e.ignoreQueryPrefix?t.replace(/^\?/,""):t,p=e.parameterLimit===1/0?void 0:e.parameterLimit,c=t.split(e.delimiter,p),l=-1,f=e.charset;if(e.charsetSentinel)for(r=0;r<c.length;++r)0===c[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===c[r]?f="utf-8":"utf8=%26%2310003%3B"===c[r]&&(f="iso-8859-1"),l=r,r=c.length);for(r=0;r<c.length;++r)r!==l&&((i=-1===(i=-1===(i=(o=c[r]).indexOf("]="))?o.indexOf("="):i+1)?(n=e.decoder(o,s.decoder,f,"key"),e.strictNullHandling?null:""):(n=e.decoder(o.slice(0,i),s.decoder,f,"key"),u.maybeMap(h(o.slice(i+1),e),function(t){return e.decoder(t,s.decoder,f,"value")})))&&e.interpretNumericEntities&&"iso-8859-1"===f&&(i=i.replace(/&#(\d+);/g,function(t,e){return String.fromCharCode(parseInt(e,10))})),-1<o.indexOf("[]=")&&(i=y(i)?[i]:i),m.call(a,n)?a[n]=u.combine(a[n],i):a[n]=i);return a}function l(t,e,r,o){if(t){var n=r.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,i=/(\[[^[\]]*])/g,a=0<r.depth&&/(\[[^[\]]*])/.exec(n),t=a?n.slice(0,a.index):n,p=[];if(t){if(!r.plainObjects&&m.call(Object.prototype,t)&&!r.allowPrototypes)return;p.push(t)}for(var c=0;0<r.depth&&null!==(a=i.exec(n))&&c<r.depth;){if(c+=1,!r.plainObjects&&m.call(Object.prototype,a[1].slice(1,-1))&&!r.allowPrototypes)return;p.push(a[1])}a&&p.push("["+n.slice(a.index)+"]");for(var l=p,t=e,f=r,u=o?t:h(t,f),y=l.length-1;0<=y;--y){var s,d,b,g=l[y];"[]"===g&&f.parseArrays?s=[].concat(u):(s=f.plainObjects?Object.create(null):{},d="["===g.charAt(0)&&"]"===g.charAt(g.length-1)?g.slice(1,-1):g,b=parseInt(d,10),f.parseArrays||""!==d?!isNaN(b)&&g!==d&&String(b)===d&&0<=b&&f.parseArrays&&b<=f.arrayLimit?(s=[])[b]=u:"__proto__"!==d&&(s[d]=u):s={0:u}),u=s}return u}}var u=t("./utils"),m=Object.prototype.hasOwnProperty,y=Array.isArray,s={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:u.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},h=function(t,e){return t&&"string"==typeof t&&e.comma&&-1<t.indexOf(",")?t.split(","):t};e.exports=function(t,e){var r=function(t){if(!t)return s;if(null!==t.decoder&&void 0!==t.decoder&&"function"!=typeof t.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var e=(void 0===t.charset?s:t).charset;return{allowDots:void 0===t.allowDots?s.allowDots:!!t.allowDots,allowPrototypes:("boolean"==typeof t.allowPrototypes?t:s).allowPrototypes,allowSparse:("boolean"==typeof t.allowSparse?t:s).allowSparse,arrayLimit:("number"==typeof t.arrayLimit?t:s).arrayLimit,charset:e,charsetSentinel:("boolean"==typeof t.charsetSentinel?t:s).charsetSentinel,comma:("boolean"==typeof t.comma?t:s).comma,decoder:("function"==typeof t.decoder?t:s).decoder,delimiter:("string"==typeof t.delimiter||u.isRegExp(t.delimiter)?t:s).delimiter,depth:"number"==typeof t.depth||!1===t.depth?+t.depth:s.depth,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:("boolean"==typeof t.interpretNumericEntities?t:s).interpretNumericEntities,parameterLimit:("number"==typeof t.parameterLimit?t:s).parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:("boolean"==typeof t.plainObjects?t:s).plainObjects,strictNullHandling:("boolean"==typeof t.strictNullHandling?t:s).strictNullHandling}}(e);if(""===t||null==t)return r.plainObjects?Object.create(null):{};for(var o="string"==typeof t?c(t,r):t,n=r.plainObjects?Object.create(null):{},i=Object.keys(o),a=0;a<i.length;++a)var p=i[a],p=l(p,o[p],r,"string"==typeof t),n=u.merge(n,p,r);return!0===r.allowSparse?n:u.compact(n)}},{"./utils":5}],4:[function(t,e,r){"use strict";function N(t,e){o.apply(t,T(e)?e:[e])}function M(t,e,r,o,n,i,a,p,c,l,f,u,y,s,d,b){for(var g=t,m=b,h=0,S=!1;void 0!==(m=m.get(B))&&!S;){var v=m.get(t);if(h+=1,void 0!==v){if(v===h)throw new RangeError("Cyclic object value");S=!0}void 0===m.get(B)&&(h=0)}if("function"==typeof p?g=p(e,g):g instanceof Date?g=f(g):"comma"===r&&T(g)&&(g=U.maybeMap(g,function(t){return t instanceof Date?f(t):t})),null===g){if(n)return a&&!s?a(e,W.encoder,d,"key",u):e;g=""}if("string"==typeof(j=g)||"number"==typeof j||"boolean"==typeof j||"symbol"==typeof j||"bigint"==typeof j||U.isBuffer(g)){if(a){var j=s?e:a(e,W.encoder,d,"key",u);if("comma"===r&&s){for(var A=_.call(String(g),","),O="",w=0;w<A.length;++w)O+=(0===w?"":",")+y(a(A[w],W.encoder,d,"value",u));return[y(j)+(o&&T(g)&&1===A.length?"[]":"")+"="+O]}return[y(j)+"="+y(a(g,W.encoder,d,"value",u))]}return[y(e)+"="+y(String(g))]}var P,x=[];if(void 0===g)return x;P="comma"===r&&T(g)?[{value:0<g.length?g.join(",")||null:void 0}]:T(p)?p:(j=Object.keys(g),c?j.sort(c):j);for(var E=o&&T(g)&&1===g.length?e+"[]":e,F=0;F<P.length;++F){var R,k=P[F],I="object"==typeof k&&void 0!==k.value?k.value:g[k];i&&null===I||(k=T(g)?"function"==typeof r?r(E,k):E:E+(l?"."+k:"["+k+"]"),b.set(t,h),(R=D()).set(B,b),N(x,M(I,k,r,o,n,i,a,p,c,l,f,u,y,s,d,R)))}return x}var D=t("side-channel"),U=t("./utils"),u=t("./formats"),y=Object.prototype.hasOwnProperty,s={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},T=Array.isArray,_=String.prototype.split,o=Array.prototype.push,n=Date.prototype.toISOString,t=u.default,W={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:U.encode,encodeValuesOnly:!1,format:t,formatter:u.formatters[t],indices:!1,serializeDate:function(t){return n.call(t)},skipNulls:!1,strictNullHandling:!1},B={};e.exports=function(t,e){var r=t,o=function(t){if(!t)return W;if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||W.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=u.default;if(void 0!==t.format){if(!y.call(u.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var o=u.formatters[r],n=W.filter;return"function"!=typeof t.filter&&!T(t.filter)||(n=t.filter),{addQueryPrefix:("boolean"==typeof t.addQueryPrefix?t:W).addQueryPrefix,allowDots:void 0===t.allowDots?W.allowDots:!!t.allowDots,charset:e,charsetSentinel:("boolean"==typeof t.charsetSentinel?t:W).charsetSentinel,delimiter:(void 0===t.delimiter?W:t).delimiter,encode:("boolean"==typeof t.encode?t:W).encode,encoder:("function"==typeof t.encoder?t:W).encoder,encodeValuesOnly:("boolean"==typeof t.encodeValuesOnly?t:W).encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:("function"==typeof t.serializeDate?t:W).serializeDate,skipNulls:("boolean"==typeof t.skipNulls?t:W).skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:("boolean"==typeof t.strictNullHandling?t:W).strictNullHandling}}(e),n=("function"==typeof o.filter?r=(0,o.filter)("",r):T(o.filter)&&(p=o.filter),[]);if("object"!=typeof r||null===r)return"";var t=e&&e.arrayFormat in s?e.arrayFormat:!(e&&"indices"in e)||e.indices?"indices":"repeat",i=s[t];if(e&&"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var a="comma"===i&&e&&e.commaRoundTrip,p=p||Object.keys(r);o.sort&&p.sort(o.sort);for(var c=D(),l=0;l<p.length;++l){var f=p[l];o.skipNulls&&null===r[f]||N(n,M(r[f],f,i,a,o.strictNullHandling,o.skipNulls,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,c))}t=n.join(o.delimiter),e=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?e+="utf8=%26%2310003%3B&":e+="utf8=%E2%9C%93&"),0<t.length?e+t:""}},{"./formats":1,"./utils":5,"side-channel":16}],5:[function(t,e,r){"use strict";function p(t,e){for(var r=e&&e.plainObjects?Object.create(null):{},o=0;o<t.length;++o)void 0!==t[o]&&(r[o]=t[o]);return r}var l=t("./formats"),c=Object.prototype.hasOwnProperty,b=Array.isArray,f=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}();e.exports={arrayToObject:p,assign:function(t,r){return Object.keys(r).reduce(function(t,e){return t[e]=r[e],t},t)},combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],o=0;o<e.length;++o)for(var n=e[o],i=n.obj[n.prop],a=Object.keys(i),p=0;p<a.length;++p){var c=a[p],l=i[c];"object"==typeof l&&null!==l&&-1===r.indexOf(l)&&(e.push({obj:i,prop:c}),r.push(l))}for(var f=e;1<f.length;){var u=f.pop(),y=u.obj[u.prop];if(b(y)){for(var s=[],d=0;d<y.length;++d)void 0!==y[d]&&s.push(y[d]);u.obj[u.prop]=s}}return t},decode:function(e,t,r){e=e.replace(/\+/g," ");if("iso-8859-1"===r)return e.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(e)}catch(t){return e}},encode:function(t,e,r,o,n){if(0===t.length)return t;var i=t;if("symbol"==typeof t?i=Symbol.prototype.toString.call(t):"string"!=typeof t&&(i=String(t)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"});for(var a="",p=0;p<i.length;++p){var c=i.charCodeAt(p);45===c||46===c||95===c||126===c||48<=c&&c<=57||65<=c&&c<=90||97<=c&&c<=122||n===l.RFC1738&&(40===c||41===c)?a+=i.charAt(p):c<128?a+=f[c]:c<2048?a+=f[192|c>>6]+f[128|63&c]:c<55296||57344<=c?a+=f[224|c>>12]+f[128|c>>6&63]+f[128|63&c]:(p+=1,c=65536+((1023&c)<<10|1023&i.charCodeAt(p)),a+=f[240|c>>18]+f[128|c>>12&63]+f[128|c>>6&63]+f[128|63&c])}return a},isBuffer:function(t){return!(!t||"object"!=typeof t)&&!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if(b(t)){for(var r=[],o=0;o<t.length;o+=1)r.push(e(t[o]));return r}return e(t)},merge:function o(n,i,a){if(!i)return n;if("object"!=typeof i){if(b(n))n.push(i);else{if(!n||"object"!=typeof n)return[n,i];(a&&(a.plainObjects||a.allowPrototypes)||!c.call(Object.prototype,i))&&(n[i]=!0)}return n}if(!n||"object"!=typeof n)return[n].concat(i);var t=n;return b(n)&&!b(i)&&(t=p(n,a)),b(n)&&b(i)?(i.forEach(function(t,e){var r;c.call(n,e)?(r=n[e])&&"object"==typeof r&&t&&"object"==typeof t?n[e]=o(r,t,a):n.push(t):n[e]=t}),n):Object.keys(i).reduce(function(t,e){var r=i[e];return c.call(t,e)?t[e]=o(t[e],r,a):t[e]=r,t},t)}}},{"./formats":1}],6:[function(t,e,r){},{}],7:[function(t,e,r){"use strict";var o=t("get-intrinsic"),n=t("./"),i=n(o("String.prototype.indexOf"));e.exports=function(t,e){e=o(t,!!e);return"function"==typeof e&&-1<i(t,".prototype.")?n(e):e}},{"./":8,"get-intrinsic":11}],8:[function(t,e,r){"use strict";var o=t("function-bind"),t=t("get-intrinsic"),n=t("%Function.prototype.apply%"),i=t("%Function.prototype.call%"),a=t("%Reflect.apply%",!0)||o.call(i,n),p=t("%Object.getOwnPropertyDescriptor%",!0),c=t("%Object.defineProperty%",!0),l=t("%Math.max%");if(c)try{c({},"a",{value:1})}catch(t){c=null}e.exports=function(t){var e=a(o,i,arguments);return p&&c&&p(e,"length").configurable&&c(e,"length",{value:1+l(0,t.length-(arguments.length-1))}),e};function f(){return a(o,n,arguments)}c?c(e.exports,"apply",{value:f}):e.exports.apply=f},{"function-bind":10,"get-intrinsic":11}],9:[function(t,e,r){"use strict";var c=Array.prototype.slice,l=Object.prototype.toString;e.exports=function(e){var r=this;if("function"!=typeof r||"[object Function]"!==l.call(r))throw new TypeError("Function.prototype.bind called on incompatible "+r);for(var o,t,n=c.call(arguments,1),i=Math.max(0,r.length-n.length),a=[],p=0;p<i;p++)a.push("$"+p);return o=Function("binder","return function ("+a.join(",")+"){ return binder.apply(this,arguments); }")(function(){var t;return this instanceof o?(t=r.apply(this,n.concat(c.call(arguments))),Object(t)===t?t:this):r.apply(e,n.concat(c.call(arguments)))}),r.prototype&&((t=function(){}).prototype=r.prototype,o.prototype=new t,t.prototype=null),o}},{}],10:[function(t,e,r){"use strict";t=t("./implementation");e.exports=Function.prototype.bind||t},{"./implementation":9}],11:[function(t,e,r){"use strict";var o,y=SyntaxError,n=Function,s=TypeError,i=function(t){try{return n('"use strict"; return ('+t+").constructor;")()}catch(t){}},d=Object.getOwnPropertyDescriptor;if(d)try{d({},"")}catch(t){d=null}function a(){throw new s}function b(t){var e,r;return"%AsyncFunction%"===t?e=i("async function () {}"):"%GeneratorFunction%"===t?e=i("function* () {}"):"%AsyncGeneratorFunction%"===t?e=i("async function* () {}"):"%AsyncGenerator%"===t?(r=b("%AsyncGeneratorFunction%"))&&(e=r.prototype):"%AsyncIteratorPrototype%"===t&&(r=b("%AsyncGenerator%"))&&(e=l(r.prototype)),m[t]=e}var p=d?function(){try{return a}catch(t){try{return d(arguments,"callee").get}catch(t){return a}}}():a,c=t("has-symbols")(),l=Object.getPrototypeOf||function(t){return t.__proto__},g={},f="undefined"==typeof Uint8Array?o:l(Uint8Array),m={"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":c?l([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":g,"%AsyncGenerator%":g,"%AsyncGeneratorFunction%":g,"%AsyncIteratorPrototype%":g,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":n,"%GeneratorFunction%":g,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":c?l(l([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&c?l((new Map)[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&c?l((new Set)[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":c?l(""[Symbol.iterator]()):o,"%Symbol%":c?Symbol:o,"%SyntaxError%":y,"%ThrowTypeError%":p,"%TypedArray%":f,"%TypeError%":s,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet},h={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},c=t("function-bind"),S=t("has"),v=c.call(Function.call,Array.prototype.concat),j=c.call(Function.apply,Array.prototype.splice),A=c.call(Function.call,String.prototype.replace),O=c.call(Function.call,String.prototype.slice),w=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,P=/\\(\\)?/g;e.exports=function(t,e){if("string"!=typeof t||0===t.length)throw new s("intrinsic name must be a non-empty string");if(1<arguments.length&&"boolean"!=typeof e)throw new s('"allowMissing" argument must be a boolean');var r=function(t){var e=O(t,0,1),r=O(t,-1);if("%"===e&&"%"!==r)throw new y("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new y("invalid intrinsic syntax, expected opening `%`");var n=[];return A(t,w,function(t,e,r,o){n[n.length]=r?A(o,P,"$1"):e||t}),n}(t),o=0<r.length?r[0]:"",n=function(t,e){var r,o=t;if(S(h,o)&&(o="%"+(r=h[o])[0]+"%"),S(m,o)){var n=m[o];if(void 0!==(n=n===g?b(o):n)||e)return{alias:r,name:o,value:n};throw new s("intrinsic "+t+" exists, but is not available. Please file an issue!")}throw new y("intrinsic "+t+" does not exist!")}("%"+o+"%",e),i=(n.name,n.value),a=!1,n=n.alias;n&&(o=n[0],j(r,v([0,1],n)));for(var p=1,c=!0;p<r.length;p+=1){var l=r[p],f=O(l,0,1),u=O(l,-1);if(('"'===f||"'"===f||"`"===f||'"'===u||"'"===u||"`"===u)&&f!==u)throw new y("property names with quotes must have matching quotes");if("constructor"!==l&&c||(a=!0),S(m,f="%"+(o+="."+l)+"%"))i=m[f];else if(null!=i){if(!(l in i)){if(e)return;throw new s("base intrinsic for "+t+" exists, but the property is not available.")}i=d&&p+1>=r.length?(c=!!(u=d(i,l)))&&"get"in u&&!("originalValue"in u.get)?u.get:i[l]:(c=S(i,l),i[l]),c&&!a&&(m[f]=i)}}return i}},{"function-bind":10,has:14,"has-symbols":12}],12:[function(t,e,r){"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=t("./shams");e.exports=function(){return"function"==typeof o&&("function"==typeof Symbol&&("symbol"==typeof o("foo")&&("symbol"==typeof Symbol("bar")&&n())))}},{"./shams":13}],13:[function(t,e,r){"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;r=Object.getOwnPropertySymbols(t);if(1!==r.length||r[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){r=Object.getOwnPropertyDescriptor(t,e);if(42!==r.value||!0!==r.enumerable)return!1}return!0}},{}],14:[function(t,e,r){"use strict";t=t("function-bind");e.exports=t.call(Function.call,Object.prototype.hasOwnProperty)},{"function-bind":10}],15:[function(r,o,i){var t="function"==typeof Map&&Map.prototype,e=Object.getOwnPropertyDescriptor&&t?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,S=t&&e&&"function"==typeof e.get?e.get:null,K=t&&Map.prototype.forEach,e="function"==typeof Set&&Set.prototype,t=Object.getOwnPropertyDescriptor&&e?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,v=e&&t&&"function"==typeof t.get?t.get:null,X=e&&Set.prototype.forEach,j="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,A="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,O="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,Y=Boolean.prototype.valueOf,a=Object.prototype.toString,Z=Function.prototype.toString,tt=String.prototype.match,w=String.prototype.slice,P=String.prototype.replace,p=String.prototype.toUpperCase,x=String.prototype.toLowerCase,f=RegExp.prototype.test,E=Array.prototype.concat,F=Array.prototype.join,et=Array.prototype.slice,n=Math.floor,R="function"==typeof BigInt?BigInt.prototype.valueOf:null,u=Object.getOwnPropertySymbols,k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,I="function"==typeof Symbol&&"object"==typeof Symbol.iterator,N="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===I||"symbol")?Symbol.toStringTag:null,M=Object.prototype.propertyIsEnumerable,D=("function"==typeof Reflect?Reflect:Object).getPrototypeOf||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function U(t,e){if(t===1/0||t===-1/0||t!=t||t&&-1e3<t&&t<1e3||f.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var o=t<0?-n(-t):n(t);if(o!==t)return t=String(o),o=w.call(e,t.length+1),P.call(t,r,"$&_")+"."+P.call(P.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}return P.call(e,r,"$&_")}var T=r("./util.inspect"),t=T.custom,_=L(t)?t:null;function W(t,e,r){r="double"===(r.quoteStyle||e)?'"':"'";return r+t+r}function B(t){return!("[object Array]"!==q(t)||N&&"object"==typeof t&&N in t)}function C(t){return!("[object RegExp]"!==q(t)||N&&"object"==typeof t&&N in t)}function L(t){if(I)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return 1;if(t&&"object"==typeof t&&k)try{return k.call(t),1}catch(t){}}o.exports=function o(r,t,n,i){var a=t||{};if(G(a,"quoteStyle")&&"single"!==a.quoteStyle&&"double"!==a.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(G(a,"maxStringLength")&&("number"==typeof a.maxStringLength?a.maxStringLength<0&&a.maxStringLength!==1/0:null!==a.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');t=!G(a,"customInspect")||a.customInspect;if("boolean"!=typeof t&&"symbol"!==t)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(G(a,"indent")&&null!==a.indent&&"\t"!==a.indent&&!(parseInt(a.indent,10)===a.indent&&0<a.indent))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(G(a,"numericSeparator")&&"boolean"!=typeof a.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var e=a.numericSeparator;if(void 0===r)return"undefined";if(null===r)return"null";if("boolean"==typeof r)return r?"true":"false";if("string"==typeof r)return function t(e,r){if(e.length>r.maxStringLength)return o=e.length-r.maxStringLength,o="... "+o+" more character"+(1<o?"s":""),t(w.call(e,0,r.maxStringLength),r)+o;var o=P.call(P.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,rt);return W(o,"single",r)}(r,a);if("number"==typeof r){if(0===r)return 0<1/0/r?"0":"-0";var p=String(r);return e?U(r,p):p}if("bigint"==typeof r)return p=String(r)+"n",e?U(r,p):p;e=void 0===a.depth?5:a.depth;if(e<=(n=void 0===n?0:n)&&0<e&&"object"==typeof r)return B(r)?"[Array]":"[Object]";var c,l,f,u,y,s,p=function(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"==typeof t.indent&&0<t.indent))return null;r=F.call(Array(t.indent+1)," ")}return{base:r,prev:F.call(Array(e+1),r)}}(a,n);if(void 0===i)i=[];else if(0<=$(i,r))return"[Circular]";function d(t,e,r){return e&&(i=et.call(i)).push(e),r?(e={depth:a.depth},G(a,"quoteStyle")&&(e.quoteStyle=a.quoteStyle),o(t,e,n+1,i)):o(t,a,n+1,i)}if("function"==typeof r&&!C(r))return"[Function"+((h=function(t){if(t.name)return t.name;t=tt.call(Z.call(t),/^function\s*([\w$]+)/);if(t)return t[1];return null}(r))?": "+h:" (anonymous)")+"]"+(0<(h=J(r,d)).length?" { "+F.call(h,", ")+" }":"");if(L(r))return h=I?P.call(String(r),/^(Symbol\(.*\))_[^)]*$/,"$1"):k.call(r),"object"!=typeof r||I?h:H(h);if(function(t){if(!t||"object"!=typeof t)return;if("undefined"!=typeof HTMLElement&&t instanceof HTMLElement)return 1;return"string"==typeof t.nodeName&&"function"==typeof t.getAttribute}(r)){for(var b="<"+x.call(String(r.nodeName)),g=r.attributes||[],m=0;m<g.length;m++)b+=" "+g[m].name+"="+W((c=g[m].value,P.call(String(c),/"/g,"&quot;")),"double",a);return b+=">",r.childNodes&&r.childNodes.length&&(b+="..."),b+="</"+x.call(String(r.nodeName))+">"}if(B(r)){if(0===r.length)return"[]";var h=J(r,d);return p&&!function(t){for(var e=0;e<t.length;e++)if(0<=$(t[e],"\n"))return;return 1}(h)?"["+Q(h,p)+"]":"[ "+F.call(h,", ")+" ]"}if(!("[object Error]"!==q(h=r)||N&&"object"==typeof h&&N in h))return u=J(r,d),"cause"in Error.prototype||!("cause"in r)||M.call(r,"cause")?0===u.length?"["+String(r)+"]":"{ ["+String(r)+"] "+F.call(u,", ")+" }":"{ ["+String(r)+"] "+F.call(E.call("[cause]: "+d(r.cause),u),", ")+" }";if("object"==typeof r&&t){if(_&&"function"==typeof r[_]&&T)return T(r,{depth:e-n});if("symbol"!==t&&"function"==typeof r.inspect)return r.inspect()}return function(t){if(!S||!t||"object"!=typeof t)return;try{S.call(t);try{v.call(t)}catch(t){return 1}return t instanceof Map}catch(t){}return}(r)?(l=[],K.call(r,function(t,e){l.push(d(e,r,!0)+" => "+d(t,r))}),z("Map",S.call(r),l,p)):function(t){if(!v||!t||"object"!=typeof t)return;try{v.call(t);try{S.call(t)}catch(t){return 1}return t instanceof Set}catch(t){}return}(r)?(f=[],X.call(r,function(t){f.push(d(t,r))}),z("Set",v.call(r),f,p)):function(t){if(!j||!t||"object"!=typeof t)return;try{j.call(t,j);try{A.call(t,A)}catch(t){return 1}return t instanceof WeakMap}catch(t){}return}(r)?V("WeakMap"):function(t){if(!A||!t||"object"!=typeof t)return;try{A.call(t,A);try{j.call(t,j)}catch(t){return 1}return t instanceof WeakSet}catch(t){}return}(r)?V("WeakSet"):function(t){if(!O||!t||"object"!=typeof t)return;try{return O.call(t),1}catch(t){}return}(r)?V("WeakRef"):"[object Number]"!==q(u=r)||N&&"object"==typeof u&&N in u?function(t){if(!t||"object"!=typeof t||!R)return;try{return R.call(t),1}catch(t){}return}(r)?H(d(R.call(r))):"[object Boolean]"!==q(e=r)||N&&"object"==typeof e&&N in e?"[object String]"!==q(t=r)||N&&"object"==typeof t&&N in t?("[object Date]"!==q(e=r)||N&&"object"==typeof e&&N in e)&&!C(r)?(t=J(r,d),e=D?D(r)===Object.prototype:r instanceof Object||r.constructor===Object,y=r instanceof Object?"":"null prototype",s=!e&&N&&Object(r)===r&&N in r?w.call(q(r),8,-1):y?"Object":"",e=(!e&&"function"==typeof r.constructor&&r.constructor.name?r.constructor.name+" ":"")+(s||y?"["+F.call(E.call([],s||[],y||[]),": ")+"] ":""),0===t.length?e+"{}":p?e+"{"+Q(t,p)+"}":e+"{ "+F.call(t,", ")+" }"):String(r):H(d(String(r))):H(Y.call(r)):H(d(Number(r)))};var c=Object.prototype.hasOwnProperty||function(t){return t in this};function G(t,e){return c.call(t,e)}function q(t){return a.call(t)}function $(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,o=t.length;r<o;r++)if(t[r]===e)return r;return-1}function rt(t){var t=t.charCodeAt(0),e={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return e?"\\"+e:"\\x"+(t<16?"0":"")+p.call(t.toString(16))}function H(t){return"Object("+t+")"}function V(t){return t+" { ? }"}function z(t,e,r,o){return t+" ("+e+") {"+(o?Q(r,o):F.call(r,", "))+"}"}function Q(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+F.call(t,","+r)+"\n"+e.prev}function J(t,e){var r=B(t),o=[];if(r){o.length=t.length;for(var n=0;n<t.length;n++)o[n]=G(t,n)?e(t[n],t):""}var i,a="function"==typeof u?u(t):[];if(I)for(var p={},c=0;c<a.length;c++)p["$"+a[c]]=a[c];for(i in t)!G(t,i)||r&&String(Number(i))===i&&i<t.length||I&&p["$"+i]instanceof Symbol||(f.call(/[^\w$]/,i)?o.push(e(i,t)+": "+e(t[i],t)):o.push(i+": "+e(t[i],t)));if("function"==typeof u)for(var l=0;l<a.length;l++)M.call(t,a[l])&&o.push("["+e(a[l])+"]: "+e(t[a[l]],t));return o}},{"./util.inspect":6}],16:[function(t,e,r){"use strict";function p(t,e){for(var r,o=t;null!==(r=o.next);o=r)if(r.key===e)return o.next=r.next,r.next=t.next,t.next=r}var o=t("get-intrinsic"),n=t("call-bind/callBound"),c=t("object-inspect"),l=o("%TypeError%"),f=o("%WeakMap%",!0),u=o("%Map%",!0),y=n("WeakMap.prototype.get",!0),s=n("WeakMap.prototype.set",!0),d=n("WeakMap.prototype.has",!0),b=n("Map.prototype.get",!0),g=n("Map.prototype.set",!0),m=n("Map.prototype.has",!0);e.exports=function(){var n,i,a,e={assert:function(t){if(!e.has(t))throw new l("Side channel does not contain "+c(t))},get:function(t){if(f&&t&&("object"==typeof t||"function"==typeof t)){if(n)return y(n,t)}else if(u){if(i)return b(i,t)}else{var e;if(a)return(e=p(e=a,t))&&e.value}},has:function(t){if(f&&t&&("object"==typeof t||"function"==typeof t)){if(n)return d(n,t)}else if(u){if(i)return m(i,t)}else if(a)return!!p(a,t);return!1},set:function(t,e){var r,o;f&&t&&("object"==typeof t||"function"==typeof t)?(n=n||new f,s(n,t,e)):u?(i=i||new u,g(i,t,e)):(e=e,(o=p(r=a=a||{key:{},next:null},t=t))?o.value=e:r.next={key:t,next:r.next,value:e})}};return e}},{"call-bind/callBound":7,"get-intrinsic":11,"object-inspect":15}]},{},[2])(2)});