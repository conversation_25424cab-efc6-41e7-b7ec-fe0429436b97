//
//
// /**
//  * 读取 文件的url 转成base64
//  * @param {Object} file 图片文件的file，一般是文件上传的file信息
//  * @returns 转base64后的base64图片地址，或错误信息
//  */
// const readImgToBase64 = (file) => {
//     return new Promise((resolve, reject) => {
//         try {
//             let xhr = new XMLHttpRequest();
//             xhr.open("get", file, true);
//             xhr.responseType = "blob";
//             xhr.onload = function () {
//                 if (this.status == 200) {
//                     // if (callback) {
//                     // callback();
//                     console.log('this.response', this.response)
//                     const reader = new FileReader()
//                     reader.readAsDataURL(this.response);
//                     reader.onload = function () {
//                         console.log('reader.result', reader.result)
//                         // 转base64结果
//                         const base64Url = reader.result;
//                         resolve(base64Url);
//                     }
//                     reader.onerror = (err) => {
//                         reject(err);
//                     }
//                     // reader.readAsText(this.response);
//                 }
//             };
//             xhr.send();
//
//         } catch (error) {
//             reject(error);
//         }
//     });
// }
