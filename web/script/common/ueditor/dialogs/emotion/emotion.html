<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="robots" content="noindex, nofollow"/>
    <script type="text/javascript" src="../internal.js"></script>
    <link rel="stylesheet" type="text/css" href="emotion.css">
</head>
<body>
<div id="tabPanel" class="wrapper">
    <div id="tabHeads" class="tabhead">
        <span><var id="lang_input_choice"></var></span>
    </div>
    <div id="tabBodys" class="tabbody">
        <div id="tab0"></div>
    </div>
</div>
<div id="tabIconReview">
    <img id='faceReview' class='review' src="../../themes/default/images/spacer.gif"/>
</div>
<script type="text/javascript" src="emotion.js"></script>
<script type="text/javascript">
    var emotion = {
        tabNum:7, //切换面板数量
        SmilmgName:{ tab0:['j_00', 77]}, //图片前缀名
        imageFolders:{ tab0:'jx2/'}, //图片对应文件夹路径
        imageCss:{tab0:'jd'}, //图片css类名
        imageCssOffset:{tab0:35}, //图片偏移
        SmileyInfor:{
            tab0:['花', '枯萎', 'ok']
        }
    };
</script>
</body>
</html>