/**
 * Created by Administrator on 2017/4/10.
 */

//wyu：20180409新增检查有无操作的代码
let operationed = 1;//新开页面，刷新页面也算有操作
let unlocked = 1;
let lockedJumpUrl = null;
let operationedtime = new Date().getTime();
function setLockedJumpRoot() {
    lockedJumpUrl = $.webRoot;
}
function clearLockedJumpRoot() {
    lockedJumpUrl = null;
}
localStorage.setItem('lastAjaxtime', operationedtime);
function mainOperating() {
    operationed = 1;
    operationedtime = new Date().getTime();
    closeMsgTipTimer();
}
$(function(){
    window.localStorage.setItem('fileDelArr', JSON.stringify([]))
    // 去掉浏览器默认提示框
    $("input").each(function(){
        var key = $(this).attr("name")
        $(this).attr("autocomplete",'new-password')
    });

    // console.log('user && session:',sphdSocket.user.userID, ':',sphdSocket.org.id,':',sphdSocket.sessionid);
    $(document).mousewheel(function () {
        mainOperating();
    });
    $(document).mousedown(function () {
        mainOperating();
    });
    $(document).mousemove(function () {
        mainOperating();
    });
    $(document).mouseup(function () {
        mainOperating();
    });
    $(document).keydown(function () {
        mainOperating();
    });
    $(document).keyup(function () {
        mainOperating();
    });
    switch(localStorage.getItem('lockScreen')) {//wyu：修改密码提示下刷新页面，直接弹窗。
        case 'password_changed':
            $(".pastTip_msg").html('用户密码已修改，请输入新密码解锁');
            //wyu:此处不能加break
            // break;
        case 'lock':
            common_pop.show($('#reLogin'));
            unlocked = 0;
            break;
        default:
            sendOperatortime(true);
    }
    //wyu：刷新服务器上的无操作时间
    setInterval(sendOperatortime,15000);
    //wyu：订阅锁屏事件
    sphdSocket.subscribe('lockScreenCheck',lockscreen);
    sphdSocket.subscribe('lockPasswordchange',lockscreen,null,'custom',sphdSocket.user.mobile);
    function lockscreen(data){
        // console.log('锁屏返回：'+data);
        if (data=='logout') {//wyu：30分钟超时，已经被注销
            location.href= $.webRoot+'/sys/logout.do';
        }
        if(data=='success') {//修改密码的回调
            localStorage.setItem('lockScreen','password_changed');
            $(".pastTip_msg").html('用户密码已修改，请输入新密码解锁');
        }
        // console.log(Array('success','lock').indexOf(data)>=0, unlocked, !operationed);
        if (Array('success','lock').indexOf(data)>=0 && unlocked && !operationed) {//wyu:条件：需要弹窗、未弹窗、上次发送操作至当前无操作，操作：弹窗
            unlocked = 0;
            if (data == 'success') {
                $(".pastTip_msg").html('用户密码已修改，请输入新密码解锁');
            } else if(!operationed) {
                localStorage.setItem('lockScreen','lock');
                $(".pastTip_msg").html('屏幕已锁定，请输入密码解锁');
            }
            $(".accountPsd").val('');
            if(lockedJumpUrl===null) {
                common_pop.show($('#reLogin'));
            } else {
                location.href = lockedJumpUrl;
                lockedJumpUrl = null;
            }
        }
    }
});
// 监听浏览器关闭
window.onbeforeunload = function(event){
    delAllFile()
};
$(window).on("unload", function(){
    delAllFile()
});
function delAllFile() {
    let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]');
    fileDelArr.forEach(function (option) {
        fileDelAjax(option)
    })
}
/*
*
*  create : hxz 2021-3-13  删除文件公用方法
* // option:{'type':'fileId', 'fileId':'sdfsdafsdfsdfsd' }
* 或者 {'type':'groupUuid', 'groupUuid':  'ssdfsdfsadf'}
* */
function fileDelAjax(option) {
    let type = option.type
    let url = $.webRoot+'/uploads/removeByFile.do' ; // 默认按照文件删除
    let data = {
        fileUid: option[type],
        userId: sphdSocket.user.userID
    }
    if (type === 'groupUuid'){
        url = $.webRoot+'/uploads/removeFilesByGroup.do' ;
        data = {
            groupUuid: option[type],
            userId: sphdSocket.user.userID
        }
    }
    $.ajax({url: url, data: data, beforeSend: function () {}, error: function () {}});
    cancelFileDel(option)
}
/*
* create : hxz 2021-3-13  取消删除文件
* （文件上传的时候都指定的默认的删除程序，不取消在页面关闭或者页面不关闭超过三天，文件会被删除）
* */
// updater: 张旭博，2021-03-18 13:17:07，本地存储删除对应文件（1.option - 传数组对象或一个对象 删除对应一个或多个文件， 传'all' 删除所有文件 2.isDel 传true 同时调用删除接口）
function cancelFileDel(option, isDel){
    console.log(typeof option)
    let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]');
    // 传值为 all，赋值为存储的所有文件
    if (option === 'all') {option = fileDelArr}

    // 判断传值类型
    if (typeof option === 'object') {
        if (!isArrayFn(option)) { option = [option] } // 如果为对象转换为数组
        option.forEach(function (it) {
            let n = -1
            fileDelArr.forEach(function(item,index){
                if(it.type == item.type && it[it.type] == item[item.type]){
                    n = index;
                    fileDelArr.splice(index, 1);
                    // clearTimeout(item['fileDelTimer']);
                }
            })
            if(n === -1 ){
                console.log('可能面临风险');
                console.log('没匹配到：', option);
            }
            // 如果传了此字段为true，那么也同时会调用删除文件接口
            if (isDel) {
                let type = it.type
                let url = $.webRoot+'/uploads/removeByFile.do' ; // 默认按照文件删除
                let data = {
                    fileUid: it[type],
                    userId: sphdSocket.user.userID
                }
                if (type === 'groupUuid'){
                    url = $.webRoot+'/uploads/removeFilesByGroup.do' ;
                    data = {
                        groupUuid: it[type],
                        userId: sphdSocket.user.userID
                    }
                }
                $.ajax({url: url, data: data, beforeSend: function () {}, error: function () {}});
            }
        })
        window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    } else {
        console.log('类型错误')
    }

}

// creator: 张旭博，2021-03-18 13:23:43，判断是不是数组
function isArrayFn(value){
    if (typeof Array.isArray === "function") {
        return Array.isArray(value);
    }else{
        return Object.prototype.toString.call(value) === "[object Array]";
    }
}

/**
 * <AUTHOR>
 * @since 2019/2/19 17:37
 * 20200401 Wyu：刷新操作改为每25分钟post提交一次，避免长时间没有HTTP访问导致后端session过期。
 * 20200404 Wyu：变量改为localStorage保存，减少多页面重复提交。
 */
function sendOperatortime(send=false){
    // console.log('sendOperatortime:'+sphdSocket.sessionid);
    var url = send? 'lockScreenCheck' : 'refreshOperatortime';
    if((send || new Date().getTime()>parseInt(localStorage.getItem('lastOperationedtime'))+15000 && operationed) && unlocked){//距上一次发送的操作时间超过15秒，两次发送间隙操作时间有更新，而且目前没锁屏
        getHostTime(function (hosttime) {//获取服务器当前时间
            console.log('hosttime1:', new Date(hosttime));
            var op = operationed && unlocked;//!$("#reLogin").is(":visible");//wyu：弹窗状态 operationed=false;
            localStorage.setItem('lastOperationedtime', operationedtime);
            localStorage.setItem('lastOperationedtimeLocal', new Date(operationedtime));
            operationed = 0;
            let sendOperationedtime = hosttime.getTime() + operationedtime - new Date().getTime()
            localStorage.setItem('lastOperationedtimeRemote', new Date(sendOperationedtime));
            if(send || !sphdSocket.isConnect || hosttime.getTime()<parseInt(localStorage.getItem('lastAjaxtime'))+1500000) {
                sphdSocket.send(url,  {'operatortime':sendOperationedtime,'operationed':op});//用服务器时间矫正本地时间
            } else {//距上一次ajax发送的操作时间超过25分钟，用ajax发送，避免session超时，否则用长连接发送。
                localStorage.setItem("lastAjaxtime", hosttime.getTime());
                $.ajax({url: $.webRoot + '/sys/refreshOperatortime.do', data: {data: JSON.stringify({'operatortime': hosttime.getTime() + sendOperationedtime - new Date().getTime(), 'operationed': op, 'sessionid': sphdSocket.sessionid})}, beforeSend: function () {}, complete: function() {}});
            }
            // console.log('send operatortime', url ,new Date(hosttime.getTime() + operationedtime - new Date().getTime()));
        });
    }
}

//判断浏览器类型
function myBrowser() {
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("OPR") > -1;
    if (isOpera) {
        return "Opera"
    } //判断是否Opera浏览器 OPR/43.0.2442.991
    //if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) { return "IE"; }; //判断是否IE浏览器
    if (userAgent.indexOf("Firefox") > -1) { return "FF"; } //判断是否Firefox浏览器  Firefox/51.0
    if (userAgent.indexOf("Trident") > -1 || userAgent.indexOf("MSIE") > -1) { return "IE"; } //判断是否IE浏览器  Trident/7.0; rv:11.0
    if (userAgent.indexOf("Edge") > -1) { return "Edge"; } //判断是否Edge浏览器  Edge/14.14393
    if (userAgent.indexOf("Chrome") > -1) { return "Chrome"; }// Chrome/56.0.2924.87
    if (userAgent.indexOf("Safari") > -1) { return "Safari"; } //判断是否Safari浏览器 AppleWebKit/534.57.2 Version/5.1.7 Safari/534.57.2
}

// creator: 张旭博，2021-06-10 16:17:39，获取头像
function getAvatarPath(gender, type) {
    var path = ''
    if (gender === '1') {
        gender = 'male'
    } else if (gender === '0') {
        gender = 'female'
    } else {
        gender = 'default'
    }
    path = $.webRoot + '/assets/images/avatar/avatar_' + gender + (type? '_'+type: '') + '.png'
    return path
}

// creator ： 侯杏哲 2017-05-10  获取网页url地址中的参数
function GetUrlQuery(name){
    var r = window.location.href ;
    var qur = r.split("?")[1] ;
    if(qur == undefined){ return undefined ; }
    var ar = qur.split("&") ;
    var queryJson = {} ;
    for(var i = 0 ; i < ar.length ; i++){
        var js = ar[i].split("=") ;
        var na = js[0] , val = js[1] ;
        // queryJson[name] = val ;
        if(name == na){ return val ;  }
    }
    return undefined ; 
}

/* creator：张旭博，2017-08-07 18:07:46，获取地址栏参数 */
function getUrlParam(key){
    // 获取参数
    var url = window.location.search;
    // 正则筛选地址栏
    var reg = new RegExp("(^|&)"+ key +"=([^&]*)(&|$)");
    // 匹配目标参数
    var result = url.substr(1).match(reg);
    //返回参数值
    return result ? decodeURIComponent(result[2]) : null;
}

/* creator：张旭博，2017-05-22 17:45:40，判断是否为当前权限 */
function hasAuthority(AuthorityType) {
    // 0 超管； 1 财务 ； 2 浏览者； 3 总务 ; 4 销售
    var roleName = "超管";
    if (AuthorityType == 1) {
        roleName = "财务";
    }
    if (AuthorityType == 2) {
        roleName = "超级浏览者";
    }
    if (AuthorityType == 3) {
        roleName = "总务";
    }
    if (AuthorityType == 4) {
        roleName = "销售";
    }

    return (chargeRole(roleName));
}

/* creator:侯杏哲 2017-06-01 ， 将时间戳格式化为时间格式  */
function add0(m){return m<10?'0'+m:m }

function formatTime( shijianchuo , _bool ) {
    if( !shijianchuo || shijianchuo === null ){ return "" ;  }
    //shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    if(_bool){
        return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
    }else{
        return y+'-'+add0(m)+'-'+add0(d);
    }
}

//wyu：获取服务器时间
function getHostTime (callback) {
    // 获取头信息，type=HEAD即可
    $.ajax({
        type: 'HEAD',
        url : $.webRoot+"/css/loginrecords/images/pix.bmp",
        beforeSend: function () {}, error: function () {},
        complete: function( xhr ) {
            callback(new Date(xhr.getResponseHeader('Date')));
        }
    });
}

//wyu: ********新增核对密码
var inputErrorCount = 0;
function checkPassword(){
    var url = $.webRoot + '/sys/checkPassword.do';
    var password = $(".accountPsd").val();
    if($.trim(password).length==0){
        $(".errorTip").html('密码不能为空!');
        $(".errorTip").show();
    }
    $.ajax({
        url: url,
        data: {password:password},
        type: "post",
        dataType: "text",
        success: function (data) {
            $('.accountPsd').val('');
            switch (data) {
                case "true":
                    sendOperatortime(true);
                    inputErrorCount = 0;
                    operationed = 1;//wyu：验证密码成功，将operationed设为1，中断无操作时间，避免连续弹窗。
                    unlocked = 1;
                    localStorage.removeItem('lockScreen')
                    $(".errorTip").hide();
                    common_pop.cancel();
                    break;
                case "false":
                    if (++inputErrorCount < 3) {
                        $(".errorTip").html('密码错误！');
                        $(".errorTip").show();
                        break;
                    }
                default://inputErrorCount>=3 || return null
                    $(".errorTip").html('已注销！');
                    $(".errorTip").show();
                    location.href = $.webRoot + '/sys/logout.do';
            }
        },
        error: function (xhr, textStatus, errorThrown) {
            /*错误信息处理*/
            console.log("进入error---");
            console.log("状态码："+xhr.status);
            console.log("状态:"+xhr.readyState);//当前状态,0-未初始化，1-正在载入，2-已经载入，3-数据进行交互，4-完成。
            console.log("错误信息:"+xhr.statusText );
            console.log("返回响应信息："+xhr.responseText );//这里是详细的信息
            console.log("请求状态："+textStatus);
            console.log(xhr);
            console.log(textStatus);
            console.log(errorThrown);
            alert("服务器错误");
        }
    });
}
//wyu: 20180416新增锁屏密码输入键盘时间
function passwordKeydown(e) {
    var keynum = 0;
    if(typeof(e.keyCode) != 'undefined' ) {// IE/Chrome
        keynum = e.keyCode;
    } else if(e.which) {// Netscape/Firefox/Opera
        keynum = e.which;
    }
    switch(keynum){
        case 13://wyu: Enter
            checkPassword();
            break;
        case 27://wyu: Esc
            location.href= $.webRoot+'/sys/logout.do';
            break;
    }
}

// creator : 侯杏哲 2018-04-27 预定义异步调用
(function ajaxSetupInit(){
    $.ajaxSetup({
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open(); } ,
        error:function (jqXHR, textStatus, errorMsg) {
            layer.msg("链接错误！");
            console.log('链接错误！', jqXHR, textStatus, errorMsg)
        } ,
        complete:function () {
            loading.close() ;
        }
    });
})() ;
// 异步调用初始化
// ajaxSetupInit() ;
/**
 * creator: 张旭博，2018-05-14 17:53:21，用于在某一个元素中插入一条提示
 * (String)type : 'success'代表正确提示,'info'代表正常提示,'warning'警告提示;,'error'错误提示;
 * (String)content : 提示内容
 */

(function($){
    $.fn.alert = function(type,content){
        var alertNode = '<div class="ty-alert ty-alert-'+type+'">'+content+'</div>';
        this.html(alertNode);
    }
    $.fn.initHalfTime = function(start, delay){
        var data = []; //保存时间点
        var a = start.split(":")[0];
        var b = start.split(":")[1];
        if(a.substr(0,1) === "0"){
            a = a.substr(1,1);
        }
        for(var i=a;i<24;i++){
            if (i < 10) {
                data.push({"text": '0' + i + ':00'});
                data.push({"text": '0' + i + ':30'});
            } else {
                data.push({"text": i + ':00'});
                data.push({"text": i + ':30'});
            }
        }
        if(b === "30"){
            data.shift();
        }
        if(delay>0){
            for(var j=0;j<delay;j++){
                data.shift();
            }
        }
        var optionStr = '<option value="">---请选择---</option>'
        for (var i in data) {
            optionStr+= '<option value="'+data[i].text+'">'+data[i].text+'</option>'
        }
        this.html(optionStr);
    }
    $.fn.treeItemSlide = function (type) {
        return $(this).each(function(){
            var iconNode = $(this).children('i').eq(0)
            if (!iconNode.hasClass("ty-fa")) {
                if (type === 'up') {
                    return iconNode.removeClass('fa-angle-down').addClass('fa-angle-right')
                } else if (type === 'down') {
                    return iconNode.removeClass('fa-angle-right').addClass('fa-angle-down')
                } else if (type === 'toggle') {
                    if (iconNode.hasClass("fa-angle-right")) {
                        return iconNode.eq(0).removeClass('fa-angle-right').addClass('fa-angle-down')
                    } else {
                        return iconNode.eq(0).removeClass('fa-angle-down').addClass('fa-angle-right')
                    }
                }
            }
        });
    }
})(jQuery);

/**
 * creator: 李玉婷，2018-10-19 10:53:21，将js Date对象格式化为指定格式,添加一个原型方法
 * 返回指定format的string
 * 使用如:
 * new Date().format('yyyy-MM-dd');    // echo: '2015-12-01'
 * new Date().format('yyyy年MM月dd日');    // echo: '2015年12月01'
 * new Date().format('yyyy/MM/dd');    // echo: '2015/12/01'
 * new Date("Tue Oct 25 2018 09:05:51 GMT+0800").format('yyyy年MM月dd日')   ;
 * new Date("2018-05-05 8:00:56.0").format('yyyy年M月d日')  ;
 * new Date( 1540170634600 ).format('MM月dd日')  ;
 * new Date( 1540170634600 ).format('yyyy-MM-dd hh:mm:ss')  ;
 * WuYu 2019/3/19 13:55 修改说明 如果Date值为null或者0，返回空字符串。
 **/
Date.prototype.format = function(format) {
    if(this.getTime()>0) {
        var o = {
            "M+": this.getMonth() + 1,
            "d+": this.getDate(),
            "h+": this.getHours(),
            "m+": this.getMinutes(),
            "s+": this.getSeconds(),
            "q+": Math.floor((this.getMonth() + 3) / 3),
            "S": this.getMilliseconds()
        };
        if (/(y+)/.test(format)) {
            format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(format)) {
                format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
            }
        }
    } else {
        format = '';
    }
    return format;
};

/*
* creator:hxz 2020-12-22 将传入对象的指定 字符串a 替换为 字符串b
* 注：该方法改变的是传入对象的值
* demo:
* Object.setValaToValb(jsonObj, null,'--'); // 将 null 替换为 --
* Object.setValaToValb(arrayObj, null,''); // 将 null 替换为 空字符串
* */
Array.prototype.setValaToValb = function (obj , vala, valb) {
    for(var key of Object.keys(obj)){
        if(obj[key] === vala) {
            obj[key] = valb ;
        }else {
            if(typeof obj[key] === "object"){
                arguments.callee(obj[key] , vala, valb)
            }
        }
    }
}
/*
* creator:hxz 2020-12-22 对象调用该方法 将null替换为指定字符串，并返回操作对象
* demo:
* arrayObj.nullToStr(); // 数组的 null 替换为 空字符串
* arrayObj.nullToStr('--'); // 数组的 null 替换为 '--'
* (new Array()).nullToStr.call(res); // 返回值res中的 null 替换为 空字符串,  res为json
* [].nullToStr.call(res, '--'); // 返回值res中的 null 替换为'--',  res为json
* */
Array.prototype.nullToStr = function (replaceStr) {
    replaceStr = replaceStr ? replaceStr.toString() : '';
    let thisObj = this
    for(var key of Object.keys(thisObj)){
        if(thisObj[key] === null) {
            thisObj[key] = replaceStr ;
        }else {
            if(typeof this[key] === "object"){
                // arguments.callee(replaceStr)
                Array.prototype.setValaToValb(this[key], null, '');
            }
        }
    }
    return thisObj;
};
Object.defineProperty(Array.prototype,'nullToStr',{ enumerable:false });
Object.defineProperty(Array.prototype,'setValaToValb',{ enumerable:false });
// creator:侯杏哲 2018-10-22 获取当前时间
function getCurDate(type) {
    var date = new Date();
    var seperator1 = "-";
    var seperator2 = ":";
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate
        + " " + date.getHours() + seperator2 + date.getMinutes()
        + seperator2 + date.getSeconds();
    if (!type) {
        currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;
    }
    return currentdate;
}

var msgNumTipTimer = 0; // 消息提示的定时器
// setMsgNum(50 , true);
// creator：hxz 2018-11-27 给信封处的消息赋值
function setMsgNum(bool, num) {
    if (Number(num) && Number(num) > 0) {
        $("#msgNum").html(num).show();
        if (bool) {
            console.log(bool)
            msgNumTipTimer = setInterval(function () {
                $("title").html("您有未读的新消息！");
                setTimeout(function () {
                    $("title").html("通用框架");
                }, 1000)
            }, 1500);
            $("title").html("您有未读的新消息");
        }
    } else {
        $("#msgNum").html(0).hide();
    }
}
function closeMsgTipTimer() {
    if (msgNumTipTimer) {
        clearInterval(msgNumTipTimer);
        $("title").html("通用框架");
        msgNumTipTimer = 0;
    }

}

// creator: 张旭博，2019-07-01 14:08:06，设置一个自执行函数获取悬浮窗目录和总角标（铅笔图标）
setTotalCorner();
function setTotalCorner() {
    var badgeNumbers =  JSON.parse(localStorage.getItem('userBadgeNumbers'));
    var countAll =  badgeNumbers && badgeNumbers.list.handle;
    setMsgNum(false, countAll)
    // alert($.webRoot)
    $(".bounceFloating iframe").attr('src',  "../vue/message/dist/index.html?v=SVN_REVISION")
}
// console.log(JSON.parse(localStorage.getItem('userBadgeNumbers')));

// creator : 侯杏哲 2017-05-20  跳转到消息处理页
function goMessage(){
    $.cookie('pid', "", { path: '/' });
    $.cookie('sid', "", { path: '/' });
    location.href=$.webRoot + '/message/messagePage.do';
}

//wyu：长连接获取论坛角标数量
sphdSocket.subscribe('formPostNumforSuperscript',
    function(data){
        if (data) {
            var res  = JSON.parse(data)
            var forumNum    = res.forumNum
            $("#discussNum").hide();
            // 判断讨论区图标闪烁代码
            if (window.location.href.indexOf("discussionIndex") === -1 ) {
                $(".fa-commenting").addClass("twink")
            }
        }

        //TODO：此处增加判断讨论区图标闪烁代码
        console.log('formPostNumforSuperscript recieved web OK:'+data);
    },
    function(){console.log('formPostNumforSuperscript Error:')},
    'user'
);
//wyu：设置论坛角标数量
function setForumCount(postNum) {
    //creator : 李玉婷 2017-05-20  获取讨论区当前未读消息数
    if (postNum === 0) {
        $("#discussNum").hide();
    } else {
        $("#discussNum").html(postNum).show();
    }
}
//wyu：长连接获取旧消息角标数量
sphdSocket.subscribe('userMessageNumber',
    function(data){
        setUserMessageNumber(data);
        console.log('userMessageNumber recieved web OK:'+data);
    },
    function(){console.log('userMessageNumber Error:')},
    'user'
);
//wyu：设置旧消息角标数量
function setUserMessageNumber(num) {
    if (num === 0) {
        $("#msgNum2").html("");
    } else {
        $("#msgNum2").html(num).show();
    }
}
//wyu：打开页面首次获取讨论区和旧消息角标数量
$.ajax({
    url: '../message/getMessageNum.do',
    type: "post",
    dataType: "json",
    beforeSend: function () {
    },
    success: function (data) {
        var num = data['userMessageNumber'];
        var postNum = data['forumCount'];
        setUserMessageNumber(num);
        $("#discussNum").hide();
    },
    error: function () {
        // console.log("未获取到数据");
        clearInterval(stop);
    },
    complete:function () {
    }
});

// creator: 李玉婷，2019-04-29 10:52:27，处理null
function handleNull(str) {
    var result = str === 'null' || str === null || str === undefined ? '':str;
    return result;
}

(function () {
    var datepicker = {}
    datepicker.getMonthData = function (year, month) {
        var ret = []
        if(!year || !month) {
            var today = new Date()
            year = today.getFullYear()
            month = today.getMonth()
        }

        var firstDay = new Date(year, month - 1, 1)
        var firstDayWeekDay = firstDay.getDay()
        if (firstDayWeekDay === 0) { firstDayWeekDay = 7 }

        var preMonthDayCount = firstDayWeekDay - 1
        var lastDay = new Date(year, month, 0)
        var lastDate = lastDay.getDate()

        var length = Math.ceil((preMonthDayCount + lastDate) / 7)
        for (var i = 0; i < 7 * length; i++) {
            var date = i + 1 - preMonthDayCount
            var showDate = date;
            var thisMonth = month
            if (date <= 0) {
                thisMonth = month - 1
                showDate = ''
            } else if (date > lastDate) {
                thisMonth = month + 1
                showDate = ''
            }
            if (thisMonth === 0) thisMonth = 12
            if (thisMonth === 13) thisMonth = 1
            ret.push({
                month: thisMonth,
                data: date,
                showDate: showDate
            })
        }

        return ret

    }
    datepicker.buildUI = function (year, month) {
        var monthData = datepicker.getMonthData(year, month)
        console.log(monthData)
        var html =  '<table class="kj-table kj-table-center hover">' +
                    '    <thead>' +
                    '    <tr>' +
                    '        <td style="width: 14%">星期一</td>' +
                    '        <td style="width: 14%">星期二</td>' +
                    '        <td style="width: 14%">星期三</td>' +
                    '        <td style="width: 14%">星期四</td>' +
                    '        <td style="width: 14%">星期五</td>' +
                    '        <td style="width: 15%">星期六</td>' +
                    '        <td style="width: 16%">星期日</td>' +
                    '    </tr>' +
                    '    </thead>' +
                    '    <tbody>';
        for (var i = 0; i < monthData.length; i++) {
            var date = monthData[i]
            if (i % 7 === 0) {
                html += '<tr>'
            }
            html += '<td day="'+date.showDate+'"><div class="day_avatar"><div class="work_state"></div><span class="work_dayNo">'+date.showDate+'</span></div></td>'
            if (i % 7 === 6) {
                html += '</tr>'
            }
        }
        html +=     '    </tbody>' +
                    '</table>'
        return html
    }
    datepicker.init = function ($dom, year, month) {
        var html = datepicker.buildUI(year, month)
        $dom.html(html)
    }
    window.dateppicker = datepicker
})()

//wyu：防止注销
class DelayLogout{
    constructor() {
        this.intevalLogout = null;
        this.stopILTime = null;
    }
    clearDelayLogoutTime() {
        clearInterval(this.intevalLogout);
        this.intevalLogout = null;
        this.stopILTime = null;
    }

    /**
     * 调用防止注销方法window.delayLogout.setDelayLogoutTime(atime)
     * <AUTHOR>
     * @since 2021/2/20 18:33
     * @Param atime: 超时的时间点（与服务器时间比较）的getTime()
     * @return: null
     */
    setDelayLogoutTime(atime) {
        // console.log("set stopILTime 1", (!isNaN(atime) && typeof atime === 'number'))
        // console.log("set stopILTime 2", (isNaN(this.stopILTime) || typeof this.stopILTime !== 'number' || this.stopILTime < atime))
        if ((!isNaN(atime) && typeof atime === 'number') && (isNaN(this.stopILTime) || typeof this.stopILTime !== 'number' || this.stopILTime < atime)) {
            //atime为数字且stopILTime为空或者stopILTime < atime
            this.stopILTime = atime;
            // console.log("set stopILTime3", this.stopILTime)
        }
        if (this.intevalLogout == null) {
            this.refreshLogoutTime();
            this.intevalLogout = setInterval(this.refreshLogoutTime, 25 * 60 * 1000);//25分钟调用一次
        }
    }
    refreshLogoutTime() {
        // console.log("refreshLogoutTime0")
        // console.log("refreshLogoutTime1",delayLogout.stopILTime)
        $.ajax({
            url: $.webRoot + '/sys/refreshLogoutTime.do', dataType: 'text', type: 'GET', beforeSend: function () {
            }, error: function () {
            }, complete: function (xhr) {
                if (delayLogout.stopILTime) {
                    let now = new Date(xhr.getResponseHeader('Date')).getTime();
                    // console.log("refreshLogoutTime.do2",delayLogout.stopILTime)
                    // console.log(xhr.getResponseHeader('Date'));
                    // console.log(now);
                    // console.log(delayLogout.stopILTime + 25 * 60 * 1000 > now)
                    if (delayLogout.stopILTime + 25 * 60 * 1000 < now) {
                        delayLogout.clearDelayLogoutTime();
                    }
                }
            }
        });
    }
}
window.delayLogout = new DelayLogout();

// creator: 张旭博，2023-05-05 04:22:34， 判断当前浏览器是不是微信浏览器
function isWeiXin() {
    var ua = window.navigator.userAgent.toLowerCase();
    return ua.match(/MicroMessenger/i) == 'micromessenger'
}


// creator: hxz，2021-06-17  小写金额转大写金额
function convertCurrency(money) {
    //汉字的数字
    var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖');
    //基本单位
    var cnIntRadice = new Array('', '拾', '佰', '仟');
    //对应整数部分扩展单位
    var cnIntUnits = new Array('', '万', '亿', '兆');
    //对应小数部分单位
    var cnDecUnits = new Array('角', '分', '毫', '厘');
    //整数金额时后面跟的字符
    var cnInteger = '整';
    //整型完以后的单位
    var cnIntLast = '元';
    //最大处理的数字
    var maxNum = 999999999999999.9999;
    //金额整数部分
    var integerNum;
    //金额小数部分
    var decimalNum;
    //输出的中文金额字符串
    var chineseStr = '';
    //分离金额后用的数组，预定义
    var parts;
    if (money == '') { return ''; }
    money = parseFloat(money);
    if (money >= maxNum) {
        //超出最大处理数字
        return '';
    }
    if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger;
        return chineseStr;
    }
    //转换为字符串
    money = money.toString();
    if (money.indexOf('.') == -1) {
        integerNum = money;
        decimalNum = '';
    } else {
        parts = money.split('.');
        integerNum = parts[0];
        decimalNum = parts[1].substr(0, 4);
    }
    //获取整型部分转换
    if (parseInt(integerNum, 10) > 0) {
        var zeroCount = 0;
        var IntLen = integerNum.length;
        for (var i = 0; i < IntLen; i++) {
            var n = integerNum.substr(i, 1);
            var p = IntLen - i - 1;
            var q = p / 4;
            var m = p % 4;
            if (n == '0') {
                zeroCount++;
            } else {
                if (zeroCount > 0) {
                    chineseStr += cnNums[0];
                }
                //归零
                zeroCount = 0;
                chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
            }
            if (m == 0 && zeroCount < 4) {
                chineseStr += cnIntUnits[q];
            }
        }
        chineseStr += cnIntLast;
    }
    //小数部分
    if (decimalNum != '') {
        var decLen = decimalNum.length;
        for (var i = 0; i < decLen; i++) {
            var n = decimalNum.substr(i, 1);
            if (n != '0') {
                chineseStr += cnNums[Number(n)] + cnDecUnits[i];
            }
        }
    }
    if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger;
    } else if (decimalNum == '') {
        chineseStr += cnInteger;
    }
    return chineseStr;
}