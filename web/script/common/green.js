/*
 * creaor: 侯杏哲 2017-03-30 
 * 配套green.css 规范的插件，基于jquery 
* */

/* Creator : 侯杏哲 2017-07-24 下拉提示框的 js */
function dropdownToggle( _this ){
    var targetID = _this.attr("target");
    $("#"+ targetID).toggle() ;
}


/*  creator ： 侯杏哲 2017-03-30  select控件 */
function tyToggleSelect(obj , num , event){
    event && event.stopPropagation();
    // tySelectCancel() ; // 初始化
    $(".ty-opItems").hide() ; $(".ty-up").attr("class" , "ty-down"); $(".ty-select").attr("onclick" , "tyToggleSelect($(this) , 0 )");
    if( num == 0){
        obj.children(".ty-opItems").show();
        num = 1 ;  obj.children(".ty-opTTL").find(".ty-down").attr("class" , "ty-up");
    }else {
        obj.children(".ty-opItems").hide();
        num = 0 ;  obj.children(".ty-opTTL").find(".ty-up").attr("class" , "ty-down");
    }
    obj.attr("onclick" , "tyToggleSelect($(this) , "+ num +" )");
    var val = obj.children(".ty-opTTL").find("option:first").val();
    obj.children(".ty-opItems").children().each(function(){
        if($(this).val() == val ){
            $(this).addClass("ty-active").siblings().removeClass("ty-active") ;
            return false ;  
        }
    });
}
// 选中的操作  
function tySelect(obj , fn){
    var selectObj = obj.parent().siblings(".ty-opTTL").children("option:first") ; 
    if(obj.val() === selectObj.val()){ return false;  }
    selectObj.val(obj.val()).html(obj.html());
    fn && fn(obj);
}

function tySelectSetInit(){
    $(".ty-opItems").each(function(){
        var valObj = $(this).find(".ty-active") ; 
        var val = valObj.val() ; 
        var name = valObj.html() ; 
        $(this).prev().find("option:first").val( val ).html( name ) ; 
    })
}
















