class Auth {
    cleanToken () {
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        localStorage.removeItem('org')
        this.removeCookie('token')
        this.removeCookie('accId')
        this.removeCookie('mobile')
        this.removeCookie('userID')
        this.removeCookie('oid')
        console.log('token cleand')
    }
    getToken () {
        let token = localStorage.getItem('token')
        if (typeof token === 'string' && token.length > 0) {
            return token
        } else if ((token = this.getCookie('token')) && typeof token === 'string' && token.length > 0) {
            return token
        } else {
            return null
        }
    }
    saveToken (token) {
        let verify = this.verifyToken(token)
        if (verify === 1) {
            localStorage.setItem('token', token)
            if (typeof $.cookie === 'function') {
                $.cookie('token', token, {path: '/'})
            }
            if (typeof $.ajaxSetup === 'function') {
                $.ajaxSetup({
                    headers: {token: token}
                })
            }
            console.log('token saved', token)
        } else if (verify === -1) {
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            localStorage.removeItem('org')
            if (typeof $.removeCookie === 'function') {
                $.removeCookie('token', {path: '/'})
            }
            if (typeof $.ajaxSetup === 'function') {
                $.ajaxSetup({
                    headers: {}
                })
            }
            console.log('token clean', token)
        }
    }
    getGuestToken () {
        let result;
        $.ajax({
            url:$.webRoot+'/auth/getGuestToken.do',
            type:'post',
            async:false,
            dataType:'text',
            success: function(data) {
                result = data
            },
            beforeSend: function () {},
            error: function () {}
        })
        if (typeof result === 'string' && result.length > 0) {
            // this.saveToken(result)
            return result
        } else {
            return null
        }
    }
    refreshCurrentUserOrg () {
        $.ajax({
            url:$.webRoot+'/sys/refreshCurrentUserOrg.do',
            type:'get',
            async:false,
            dataType:'json',
            success: function(res) {
                let data = res.data
                let user = data.user
                if (user != null) {
                    localStorage.setItem('user', user)
                }
                let org = data.org
                if (org != null) {
                    localStorage.setItem('org', org)
                }
            },
            beforeSend: function () {},
            error: function () {}
        })
    }
    getTokenPayload (token) {
        if (typeof token !== 'string') {
            token = this.getToken()
        }
        if (typeof token === 'string' && token.length > 147 && token.length < 1024 ) {
            let tokenArr = token.split('.')
            if (typeof tokenArr === 'object' && tokenArr instanceof Array && tokenArr.length === 3) {
                return Base64.decode(tokenArr[1])
            }
        }
        return null
    }
    getByName (name, token) {
        let tokenStr = this.getTokenPayload(token)
        if (typeof tokenStr === 'string') {
            let tokenObj = JSON.parse(tokenStr)
            if (typeof tokenObj === 'object') {
                return tokenObj[name]
            }
        }
        return null
    }
    getExp(token) {
        return this.getByName('exp', token)
    }
    getAcc () {
        return this.getByName('acc')
        // return this.getByName('acc')
    }
    getSessionid () {
        return this.getByName('sessionid')
    }
    getUser () {
        if (localStorage.getItem('user') != null) {
            return JSON.parse(localStorage.getItem('user'))
        } else if (this.getByName('userID') != null) {
            this.refreshCurrentUserOrg()
            if (localStorage.getItem('user') != null) {
                return JSON.parse(localStorage.getItem('user'))
            }
        }
        return null
    }
    getOrg () {
        if (localStorage.getItem('org') != null) {
            return JSON.parse(localStorage.getItem('org'))
        } else if (this.getByName('userID') != null) {
            this.refreshCurrentUserOrg()
            if (localStorage.getItem('org') != null) {
                return JSON.parse(localStorage.getItem('org'))
            }
        }
        return null
    }
    getCookie (name) { // 获取cookie，仅在同域名iframe中可用
        var arr = document.cookie.match(new RegExp('(^| )' + name + '=([^;]*)(;|$)'))
        if (arr != null) {
            return decodeURIComponent(arr[2])
        } else {
            return null
        }
    }
    removeCookie (name) {
        // var cookieKeys = document.cookie.match(/[^ =;]+(?=\=)/g) // 获取cookie
        var date = new Date() // 获取当前时间
        date.setTime(date.getTime() - 10000) // 改变当前时间
        document.cookie = name + "=0; expires=" + date.toGMTString() + "; path=/"
        // if (cookieKeys) {
        //     for (var i = 0; i < cookieKeys.length; i++) {
        //         if (cookieKeys[i] === name) {
        //             document.cookie = cookieKeys[i] + "=0; expires=" + date.toGMTString() + "; path=/"
        //         }
        //     }
        // }
    }
    getUrl (url) {
        let xmlhttp = new XMLHttpRequest()
        xmlhttp.open('GET', url, false)
        let token
        if ((token = this.getToken()) !== null) {
            xmlhttp.setRequestHeader('token', token)
        }
        xmlhttp.send(null)
        return xmlhttp.responseText
    }
    verifyToken (token) {
        typeof token === 'string' && console.log('verifyToken', typeof token === 'string' && token.length > 147 && token.length < 2048 && token.split('.').length === 3, typeof token === 'string', token.length > 300, token.length < 600, token.split('.').length)
        if (typeof token === 'string' && token.length > 147 && token.length < 1024 && token.split('.').length === 3) {
            if(this.getExp(token) * 1000 > Date.now().valueOf()) {
                return 1
            } else {
                return -1
            }
        } else {
            return 0
        }
    }
    _CheckResponseToken (response) {
        const defaultError = '未登陆或者登陆状态已过期！'
        let errorInfo = defaultError
        if (response?.status === 200 && (errorInfo=this._CheckResponseErrorInfo(response?.data?.error))!=null
            || response?.status === 500 && JSON.stringify(response?.data??'').indexOf('IllegalAccessException: 未登陆或者登陆状态已过期！') >= 0
            || response?.status === 401 || JSON.stringify(response?.data??'').indexOf('401 Authorization Required') >= 0) { // token过期
            console.log('interceptors 500')
            this.cleanToken()
            console.log(errorInfo == '请登录账号！' ? defaultError : errorInfo)
            return errorInfo == '请登录账号！' ? defaultError : errorInfo
        } else {
            return true
        }
    }
    _CheckResponseErrorInfo(error) {
        const logoutErrorInfoMap = new Map([
            ['2', '无认证、未登陆或者登陆状态已过期！'],
            ['3', '未登陆或者登陆状态已过期！'],
            ['5', '请登录账号！']
        ])
        for(let [key, value] of logoutErrorInfoMap.entries()) {
            if (error?.code == key && error?.message == value) {
                return value
            }
        }
        return null
    }
}
window.auth = new Auth();
$(function () {
    $.ajaxSetup({
        headers: {token:auth.getToken()}
    })
    $(document).ajaxError(function (event,xhr) {
        if(xhr.status==500 && xhr.responseText.indexOf('IllegalAccessException: 未登陆或者登陆状态已过期！') > 0) {
            location.href = $.webRoot+'/sys/login.do'
        }
    })
    $(document).ajaxSuccess(function (event,xhr) {
        console.log('ajaxSuccess xhr', xhr)
        let response = {}
        try{
            response.data = JSON.parse(xhr.responseText)
        } catch (e) {
            response.data = xhr.responseText
        }
        response.status = xhr.status
        console.log('ajaxSuccess response', response)
        let msg
        if((msg=auth._CheckResponseToken(response))!==true) {
            layer.msg(msg);
            console.log('_CheckResponseToken', msg)
            setTimeout(() => {location.href = $.webRoot+'/sys/login.do'}, 3000)
        }
        let token = xhr.getResponseHeader('token')
        if(typeof token === 'string' && token.length > 0) {
            auth.saveToken(token)
        }
    })
})