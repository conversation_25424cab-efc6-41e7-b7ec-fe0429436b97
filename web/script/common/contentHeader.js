var common_bounce = new Bounce(".common_bounce");
var common_bounce1 = new Bounce(".common_bounce1");
common_bounce.show($("#checkTip"));
common_bounce1.show($("#unableTip"));
common_bounce.cancel();
common_bounce1.cancel();
// creator: 李玉婷，2020-03-09 16:51:06，账号修改
function updateUserAccount() {
    var mobile = sphdSocket.user.mobile ;
    $("#receiveTip input:radio").prop('checked', false);
    $("#receiveTip .orgAccount").html(mobile);
    common_bounce.show($("#receiveTip"));
}

// creator: 张旭博，2024-11-04 04:20:13， 能否收到验证码 - 下一步
function receiveTipNextStep(){
    var state = $("#receiveTip input:checked").val();
    if (state == 0){
        $.ajax({
            url: "../userInfo/getLeaderUserName.do",
            data: {'userId': sphdSocket.user.userID},
            success: function (res) {
                var resName = res.data;
                var str = '';
                if (chargeRole('超管')){
                    str = resName;
                } else if (chargeRole('普通员工')){
                    str = '管理人员' + resName;
                } else {
                    str = '领导' + resName;
                }
                $("#unableTip_ms").html('<p style="text-indent: 2em">如想修改登录账号又无法获取相应手机号的短信验证码，可请'+ str +'为您修改。</p>');
                common_bounce1.show($("#unableTip"));
            }
        });
    } else if (state == 1) {
        common_bounce.cancel();
        // 修改登录账号
        accountUpdate();
    }
}

// creator: 张旭博，2024-11-07 09:04:11， 修改登录账号（验证旧账号）
function accountUpdate() {
    common_bounce.show($("#changeUser"));
    let phone = sphdSocket.user.mobile
    $("#changeUser [name='user']").val(phone)
    $("#changeUser [name='code']").val('')
}

// creator: 张旭博，2024-11-06 04:06:25， 旧帐号发送验证码验证
function sendCode_oldPhone(selector) {
    let sendCodeDisabled = selector.prop("disabled")
    let oidPhone = sphdSocket.user.mobile
    let data = { phone: oidPhone }
    if (sendCodeDisabled) return false
    if(data.phone.length === 11) {
        $.ajax({
            url: $.webRoot + '/userInfo/orgEditPhoneVerificationCode.do',
            data: data
        }).then(res => {
            let success = res.success
            let errorMsg = res.errorMsg
            if (success === 1) {
                layer.msg('发送成功！')
                countdown(60, selector)
            } else {
                layer.msg(errorMsg)
            }
        })
    } else {
        layer.msg('手机号码不正确！')
    }
}

// creator: 张旭博，2024-11-07 09:04:19， 修改登录账号（验证旧账号） 确定（检查验证码）
function checkCode_oldPhone() {
    let data = {
        phone: sphdSocket.user.mobile,
        code: $("#changeUser [name='code']").val()
    }
    if(data.code.length === 0){
        layer.msg('请先输入验证码!')
        return false
    }
    $.ajax({
        url: '/userInfo/checkOrgEditVerificationCode.do',
        data: data
    }).then(res => {
        let success = res.success
        // 返回值： true- 验证成功 false-验证失败
        if(success === 1){
            // 修改登录账号（输入新账号）
            common_bounce.show($("#editNewAccount"));
            $("#editNewAccount input").val('')
        }else{
            layer.msg('您输入的验证码有误!')
        }
    })
}

// creator: 张旭博，2024-11-06 04:06:51， 新账号发送验证码
function sendCode_newPhone(selector){
    let sendCodeDisabled = selector.prop("disabled")
    let newPhone = $("#editNewAccount [name='user']").val()
    let data = { phone: newPhone }
    if (sendCodeDisabled) return false
    if(data.phone.length === 11) {
        $.ajax({
            url: '/userInfo/orgNewPhoneVerificationCode.do',
            data: data
        }).then(res => {
            let success = res.success
            let errorMsg = res.errorMsg
            if (success === 1) {
                layer.msg('发送成功！')
                countdown(60, selector)
            } else {
                layer.msg(errorMsg)
            }
        })
    } else {
        layer.msg('手机号码不正确！')
    }
}

// creator: 张旭博，2024-11-04 04:44:30， 修改登录账号（新账号） - 确定
function editNewAccount_submit() {
    let newPhone = $("#editNewAccount [name='user']").val()
    let code = $("#editNewAccount [name='code']").val()
    let data = {
        phone: newPhone,
        code: code,
    }
    if(data.code.length === 0){
        layer.msg('请先输入验证码!')
        return false
    }
    $.ajax({
        url: '/userInfo/checkOrgNewPhoneVerificationCode.do',
        data: data
    }).then(res => {
        let success = res.success
        // 返回值： true- 验证成功 false-验证失败
        if(success === 1){
            // 修改登录账号（输入新账号）
            common_bounce.show($("#editNewPassword"));
            $("#editNewPassword input").val('')
            $("#editNewPassword").data('phone', newPhone)
            $("#editNewPassword").data('code', code)
        }else{
            layer.msg('您输入的验证码有误!')
        }
    })
}

// creator: 张旭博，2024-11-06 04:56:17， 验证新密码 提交
function editNewPassword_submit() {
    let phone = $("#editNewPassword").data('phone')
    let code = $("#editNewPassword").data('code')
    let pass1 = $("#editNewPassword [name='passW']").val()
    let pass2 = $("#editNewPassword [name='passW2']").val()
    if(pass1 !== pass2){
        layer.msg("两次输入的密码不一致，请重新设置！")
        return false
    } else if(!isPassword(pass1)){
        layer.msg("您设置的密码过于简单，请重新设置！")
        return false
    }
    let json = { "userId": sphdSocket.user.userID,
        "phone": phone,
        "password": pass1,
        "code": code
    };
    $.ajax({
        url: '/userInfo/updateUserPhonePassWord.do',
        data: json
    }).then(res => {
        if(resData) {
            common_bounce.show($("#checkTip"))
            $("#checkTip .phone").html(phone)
            setTimeout(function (){
                location.href = "../sys/logout.do";
            }, 3000)
        } else  {
            layer.msg('密码修改失败，请重新设置！')
        }
    })
}

// creator: 张旭博，2024-11-07 03:02:12， 新账号审查
function newMobileCheck() {
    let newPhone = $("#editNewAccount [name='user']").val()
    let oldPhone = sphdSocket.user.mobile
    if (testMobile(newPhone) && newPhone != oldPhone) {
        let dataSend = { "mobile": newPhone }
        $.ajax({
            url: '/userInfo/mobileCheckRepeat.do',
            data: dataSend
        }).then(res => {
            let data = res.data
            let status = data.state
            // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同
            if (status === 1) {
                var str =
                    '<p>您此次的修改将导致原手机号无法登录系统。</p>' +
                    '<p>确定修改吗？</p>' ;
                common_bounce1.show($("#tips"))
                $("#tips .tipMsg").html(str)
                $("#tips .sureBtn").unbind().on("click", function () {
                    common_bounce1.cancel()
                })
            } else if (status === 2 || status === 3) {
                common_bounce1.show($("#tips"))
                $("#tips .tipMsg").html(data.cont)
                $("#tips .sureBtn").unbind().on("click", function () {
                    common_bounce1.cancel()
                })
            } else if (status === 0) {
                common_bounce1.show($("#tipIKnow"))
                $("#tipIKnow .tipMsg").html(data.cont)
                $("#tipIKnow .sureBtn").unbind().on("click", function () {
                    common_bounce1.cancel()
                    $("#editNewAccount [name='user']").val('')
                })
            }
            
        }).catch(err => {
            console.log('err=', err)
        })
    }
}

// creator: 张旭博，2024-11-08 08:57:16， 修改密码
function changePassword() {
    common_bounce.show($("#changePassword"))
    $("#changePassword input").val('')
    $("#changePassword [name='user']").val(sphdSocket.user.mobile)
}

// creator: 张旭博，2024-11-08 08:57:51， 修改密码 - 发送验证码
function sendCode_changePassword(selector) {
    // 直接修改密码 - 发送验证码
    let sendCodeDisabled = selector.prop("disabled")
    let oidPhone = sphdSocket.user.mobile
    let data = { phone: oidPhone }
    if (sendCodeDisabled) return false
    if(data.phone.length === 11) {
        $.ajax({
            url: $.webRoot + '/auth/editPasswordVerificationCode.do',
            data: data
        }).then(res => {
            let success = res.success
            let errorMsg = res.errorMsg
            if (success === 1) {
                layer.msg('发送成功！')
                countdown(60, selector)
            } else {
                layer.msg(errorMsg)
            }
        })
    } else {
        layer.msg('手机号码不正确！')
    }
}

// creator: 张旭博，2024-11-08 08:57:51， 修改密码 - 确定
function checkCode_phone() {
    let code = $("#changePassword [name='code']").val()
    let data = {
        phone: sphdSocket.user.mobile,
        code: code,
    }
    if(data.code.length === 0){
        layer.msg('请先输入验证码!')
        return false
    }
    $.ajax({
        url: $.webRoot + '/auth/checkPasswordVerificationCode.do',
        data: data
    }).then(res => {
        let success = res.success
        // 返回值： true- 验证成功 false-验证失败
        if(success === 1){
            common_bounce.show($("#changePassword_set"))
            $("#changePassword_set input").val('')
        }else{
            layer.msg('您输入的验证码有误!')
        }
    }).catch(err => {
        layer.msg('系统错误!')
    })
}

// creator: 张旭博，2024-11-08 09:01:45， 修改密码 - 录入新密码 - 确定
function changePassword_set_submit() {
    let oldPassword = $("#changePassword_set [name='passW']").val()
    let passW = $("#changePassword_set [name='passW']").val()
    let passW2 = $("#changePassword_set [name='passW2']").val()
    // 直接修改密码，最后提交修改的密码
    if(passW !== passW2){
        layer.msg("两次输入的密码不一致，请重新设置！")
        return false
    } else if(!this.isPassword(passW)){
        layer.msg("您设置的密码过于简单，请重新设置！")
        return false
    }
    let data = {
        newPassword: passW
    }
    $.ajax({
        url: $.webRoot + '/auth/editPassword.do',
        data: data
    }).then(res => {
        let data = res.data
        let success = res.success
        let errorMsg = res.errorMsg
        if(success === 1) {
            common_bounce1.show($("#tips"))
            $("#tips .tipMsg").html(data + '<br>您wonderss系统的密码已修改，请重新登录。')
            $("#tips .sureBtn").unbind().on("click", function (){
                location.href = "../sys/logout.do";
            })
            setTimeout(function(){
                location.href = "../sys/logout.do";
            },3000);
        } else  {
            if (errorMsg) {
                layer.msg(errorMsg)
            } else {
                layer.msg('设置失败，请重新设置！')
            }
        }
    }).catch(err=>{
        layer.msg('密码修改失败，请重新设置！')
    })
}

// creator: 张旭博，2024-11-06 04:56:06， 正则表达式验证密码
function isPassword(password) {
    return /^(?=.*[a-zA-Z])(?=.*\d)[^]{8,16}$/.test(password);
}

// creator: 张旭博，2024-11-07 11:22:37， 测试手机号
function testMobile(val) {
    var pattern =/^(0|86|17951)?(13[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|14[0-9]|19[0-9])[0-9]{8}$/;
    if (pattern.exec(val)==null) { return false ; 	}
    return true ;
}

// creator: 张旭博，2019-07-03 14:40:18，倒计时
function countdown(seconds, obj){
    if (seconds > 1){
        seconds--;
        obj.html(seconds+"秒后可重新获取 ").prop("disabled", true);//禁用按钮
        // 定时1秒调用一次
        setTimeout(function(){
            countdown(seconds,obj);
        },1000);
    }else{
        obj.html("获取验证码").prop("disabled", false);//启用按钮
    }
}

// creator: 张旭博，2024-11-07 09:30:39， 登出
function logout() {
    $.ajax({
        url: '/sys/logout.do',
    }).then(res => {
        console.log('logOut res=', res)
        let status = res.status
        if(status === 200){
            this._ClearLocal()
            sphdSocket.reflushAuth()
            this.$router.replace('Start')
            location.reload(true)
            this._ToStart()
        }else{
            layer.msg('退出失败！')
        }
    }).catch(err => {
        console.log('logOut err=', err)
        layer.msg('退出失败！')
    })
}
function _ClearLocal() {
    clearStorage()
    //清空全局定时任务
    for(let interval of this.intervals) {
        clearInterval(interval)
    }
    this.intervals.length = 0
    //清空全局订阅
    sphdSocket.unsubscribeAll(sphdSocket.level.acc)
}
function tokenErrorLogout(msg) {
    this.$message.error(msg)
    setTimeout(()=>{
        this._ToStart()
    },3000)
}

function _ToStart() {
    this._ClearLocal()
    sphdSocket.reflushAuth()
    this.$router.replace('Start')
    location.reload(true)
}
