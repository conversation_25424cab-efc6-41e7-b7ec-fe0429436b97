
//装备器具 - 装备清单 js
$(function () {
    $(".mainCon0").show()
    $("#filterCat").val("").data('id','')
    getEqList(1, 0 , 'home')
    $("body").on("click",".funBtn, .ty-btn, .linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    $(".ty-secondTab").on('click', 'li', function () {
        let index = $(this).index()
        $(".ty-secondTab li").removeAttr('class')
        $(this).attr('class','ty-active')
        // 全部 的时候 不穿传 category ； 未分类 category = -1 ； 已分类 category=0  筛选时， category =分类ID
        switch (index){
            case 0: // 全部
                getEqList(1)
                break;
            case 1: // 已分类
                getEqList(1, 0)
                break;
            case 2: // 未分类
                getEqList(1, -1)
                break;
            default:
        }
    })
})

// creater:hxz 2023-02-23 新增名称
function addName() {
    $("#addName input").val("")
    bounce_Fixed.show($("#addName"));
    $("#addName .limtRole").html('0/8')
}
function addNameOk() {
    const txt = $("#addName input").val()
    if(txt.length === 0){
        layer.msg('装备器具的名称不能为空')
        return false
    }
    bounce_Fixed.cancel()
    $.ajax({
        'url':'../equipment/add',
        'data':{ 'fullName': txt },
        success:function(res){
            console.log(res)
            if(res.code === 0){
                layer.msg('新增成功')
                getNameList($("#eqName"));
            }else{
                layer.msg('操作失败，因为系统里已有这个名称了！')

            }

        }
    })
}
// creater:hxz 2023-02-23 新增单位
function addUnit() {
    $("#addUnit input").val("")
    bounce_Fixed.show($("#addUnit"));
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk() {
    var module = 11
    var name = $("#unitName").val();
    bounce_Fixed.cancel();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": 11 },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                var idStr = $("#updateType").val();
                getUnitList($("#unit"), 11);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                layer.msg(tipStr)
            }
        }
    })
}

// creater:hxz 2023-02-23 新增供应商
function addSup(thisObj) {
    let type = thisObj.data('suptype')
    $("#addSup").data("suptype", type)
    $("#addSup input").val("")
    bounce_Fixed.show($("#addSup"))

}
function addSupOk() {
    bounce_Fixed.cancel()
    let suptype = $("#addSup").data("suptype")
    let data = {'type':2}
    data.fullName = $("#supFullName").val()
    data.name = $("#supName").val()
    data.codeName = $("#supCode").val()
    $.ajax({
        'url':'../supplier/addSupplier.do',
        'data':data,
        success:function(res){
            console.log(res)
            if(res.success === 1){
                layer.msg('新增成功')
                if(suptype === 2){
                    refrashSupSelect()
                }else{
                    getSupList($("#sup"));
                }
            }else{
                layer.msg('新增失败！')
            }
        }
    })
}

function refrashSupSelect() {
    $.ajax({
        "url":'../supplier/getSupplierList.do',
        "data":{
            "currentPageNo":1,
            "pageSize":9999,
            'type': 2
        },
        success:function(res) {
            var list = res.data.list|| [] ;
            $("#tab2 tr:gt(0)").each(function(){
                let supSelect = $(this).find('.sup')
                let supVal = supSelect.val()
                supSelect.html(setSupSelect( list, supVal))
            })
        }
    })
}

function filterCatBtn(thisObj) {
    $("#selectCat").data('type','filterCatBtn')
    bounce_Fixed.show($("#selectCat"))
    getCatList( $("#catContainer"), '')
}
var batchEqList = []
// creater:hxz 2023-03-21  批量分类
function batchClass() {
    $(".mainCon").hide();
    $(".mainCon2").show();
    const num = Number($(".unCount").html())
    $("#weiNum").html(num);
    $("#selectNum").html(0);
    getCatList2($("#batchClassCon"), '',40)
}
// creater:hxz 2023-02-23 类别列表
function getCatList2(obj, parent, paddingLeft) {
    let data = { 'enabled': 1, 'parent':0 }
    if(parent){
        data.parent = parent
    }
    $.ajax({
        "url":'/equipment/category/list',
        "data": data,
        success:function(res) {
            var list = res ;
            var str = ``
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i]
                    str += `
                        <div class="catItem" style="padding-left:${paddingLeft}px; ">
                            ${ item.childrens&&item.childrens > 0 ? '' : ' <span class="fa fa-angle-right funBtn" data-fun="selectEqBtn"></span>'    }
                             <div class="catItemName funBtn" data-fun="showKids">
                                <span class="fa ${ item.childrens&&item.childrens > 0 ? 'fa-angle-double-down' : '' }"></span>
                                ${ item['name'] }：${ item['content'] } （已选：<span class="selectNum">0</span>项）---- 子级类别 有 ${ item.childrens}个
                                <span class="hd">${ JSON.stringify(item) }</span>
                            </div>
                        </div>
                    `
                }
            }
            str += ``
            if(parent){
                obj.after(str);
            }else{
                obj.html(str);
            }
        }
    })
}
function showKids(thisObj) {
    const thisFa = thisObj.children('.fa')
    const thisTr = thisObj.parent('.catItem')
    const nextTr = thisObj.parent('.catItem').next()
    const thisPaddingLeft = Number(thisTr.css('padding-left').replace('px',''))
    let nextPaddingLeft = 0
    if(nextTr.length > 0){
        nextPaddingLeft = Number(nextTr.css('padding-left').replace('px',''))
    }
    if(thisPaddingLeft >= nextPaddingLeft){ // 同级的或者下一个是上级的,需要加载子级
        const info = JSON.parse(thisObj.find(".hd").html())
        if(info.childrens && info.childrens>0){
            const parent = info.id
            getCatList2(thisTr, parent, Number(thisPaddingLeft)+40)
            thisFa.attr('class', 'fa fa-angle-double-up')
        }else{
            layer.msg('该类别没有子级了！')
        }

    }else{ // 已经有子级了
        let notChildren = 0
        let childrenList = []
        const isUp = thisFa.hasClass('fa-angle-double-up')
        thisTr.nextAll('.catItem').each(function(){
                console.log(this)
                const itemPaddingLeft = Number($(this).css('padding-left').replace('px',''))
                if(itemPaddingLeft <= thisPaddingLeft ){ // 到这里以后都不是子级了
                    // notChildren++
                    console.log('跳出循环')
                    return false
                }else{ // 处理子级
                    childrenList.push(this);
                    if(isUp){ // 本级向下，子级隐藏
                        $(this).hide()
                    }else{ // 本级向上，子级显示
                        $(this).show()
                    }
                }
        })
        if(isUp){ // 本级向下，子级隐藏
            thisFa.attr('class','fa fa-angle-double-down')
        }else{ // 本级向上，子级显示
            thisFa.attr('class','fa fa-angle-double-up')
        }
    }
}
var selectCat = null
// creater:hxz 2023-03-21  跳转 选择未分类的装备
function selectEqBtn(thisObj) {
    selectCat = thisObj
    $(".mainCon").hide();
    $(".mainCon3").show();
    if(batchEqList.length > 0){
        renderBatchEQ(batchEqList)
    }else{
        getEqList(1, -1, 'batchClass')
    }
    const catInfo = JSON.parse(thisObj.siblings('.catItemName').find('.hd').html())
    $("#catPath").html(catInfo.path)
}
function toggleCheck(thisObj) {
    const isCheck = thisObj.hasClass('fa-square-o')
    let cSelectNum = Number($("#cSelectNum").html())
    if(isCheck){
        thisObj.attr('class','fa fa-check-square-o funBtn')
        $("#cSelectNum").html(++cSelectNum)

    }else{
        thisObj.attr('class','fa fa-square-o funBtn')
        $("#cSelectNum").html(--cSelectNum)

    }
}
// creater:hxz 2023-03-21 选择未分类的装备 -确定
function selectEQOK(thisObj) {
    // 类别信息
    const catItemNameObj = selectCat.siblings('.catItemName')
    let catInfo = JSON.parse(catItemNameObj.find('.hd').html())
    // 装备
    let eqIds = []
    $("#selectTab .fa-check-square-o").each(function(){
        let eqId = $(this).siblings('span').html()
        eqIds.push(Number(eqId))
    })
    let selectAllNum = 0
    batchEqList.forEach(item => {
        const thisID = item.id
        const indexnn = eqIds.indexOf(thisID)
        if(indexnn > -1){
            item.selected = 1
            item.selectedCat = catInfo.id
        }
        if(item.selected == 1){
            selectAllNum++
        }
    })
    backCon(thisObj)
    catInfo.selectEqIds = eqIds
    catItemNameObj.find('.hd').html(JSON.stringify(catInfo))
    catItemNameObj.find('.selectNum').html(eqIds.length)
    const weiNum = $("#weiNum").html()
    $("#selectNum").html(selectAllNum)
    //$("#noSelectNum").html(weiNum - selectAllNum)
}
function toogeCircle(thisObj) {
    if(thisObj.hasClass('fa-circle-o')){
        thisObj.attr('class','fa fa-dot-circle-o funBtn')
    }else{
        thisObj.attr('class','fa fa-circle-o funBtn')
    }
}
// creater:hxz 2023-03-21  保存
function saveBatchCat() {
    let isSelect = $("#toogeCircle").hasClass('fa-dot-circle-o')
    if(isSelect){
        let str = ``
        $("#batchClassCon .catItem").each(function(){
            const catInfo = JSON.parse($(this).find('.hd').html());
            if(catInfo.selectEqIds && catInfo.selectEqIds.length > 0){
                str += `ids=${catInfo.id}-${ catInfo.selectEqIds.join('-')}&`
            }
        })
        str = str.slice(0, -1)
        $.ajax({
            'url':`../equipment/model/batchClassification?${ str }`,
            'method':'get',
            success:function(res){
                backCon($("#backCon1"))
                getEqList(1, -1)
            }
        })
    }else{
        layer.msg('请确认是不是已完成')
    }
}
// creater:hxz 2023-03-21  获取列表信息
function getEqList(cur,category,batchClassType) {
    let data = {
        'pageSize': 20,
        'currentPageNo': cur
    }
    if(category !== undefined && category !== ""){
        data['category'] = category
    }
    if(batchClassType === 'batchClass'){
        data.pageSize = 9999
    }
    console.log(data)
    $.ajax({
        'url':'../equipment/model/pageList',
        'data':data,
        success:function(res){
            const list = res.data || []
            const all = res.all || 0 // 全部
            const yetCategory = res.yetCategory || 0 // 已分类
            const noCategory = res.noCategory || 0 // 未分类
            const pageInfo = res.pageInfo
            const jsonStr = JSON.stringify({
                'category': category,
                'batchClassType': batchClassType,
            })
            switch (batchClassType){
                case 'batchClass':
                    batchEqList = list;
                    renderBatchEQ(list)
                    break;
                case 'filterCatBtn':
                    $(".mainCon").hide()
                    $(".mainCon4").show()
                    let catName = $("#filterCat").val()
                    setPage($("#ye2"), pageInfo.currentPageNo, pageInfo.totalPage, 'equipmentList', jsonStr)
                    $(".filterTxt").html(`系统内的装备器具中，<span class="ty-color-blue">${catName }</span> 共 ${ list.length }台（套），具体如下：`)
                    let str2 = ``
                    list.forEach(item => {
                        let emCount = item.emCount && item.emCount !== "" ? item.emCount.split(","): "";
                        item.ids = item.emCount;
                        item.length = emCount.length;
                        str2 += `
                     <tr>
                        <td>${ item.equipmentName || '' }/${ item.modelName }</td>
                        <td>${ item.supplierName || '未知' }</td>
                        <td>${ item.units || '' }</td>
                        <td>${ emCount.length }</td>
                        <td>${ item.path }</td>
                        <td class="ty-td-control"><span class="ty-color-blue">${ item.productCount || '0' }种</span></td>
                        <td>
                            <span class="ty-color-blue funBtn" data-fun="manageBtn">管理</span>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                    </tr>
                `
                    })
                    $("#tbContent2").html(str2)
                    break;
                case 'home':
                    $(".notTip").html(res.noc || 0);
                    $(".unCount").html(noCategory);
                    $("#handledNum").html(yetCategory)

                    setPage($("#ye0"), pageInfo.currentPageNo, pageInfo.totalPage, 'equipmentList', jsonStr)
                    let str = ``
                    list.forEach(item => {
                        let emCount = item.emCount && item.emCount !== "" ? item.emCount.split(","): "";
                        item.ids = item.emCount;
                        item.length = emCount.length;
                        str += `
                     <tr>
                        <td>${ item.equipmentName || '' }/${ item.modelName }</td>
                        <td>${ item.supplierName || '未知' }</td>
                        <td>${ item.units || '' }</td>
                        <td>${ emCount.length }</td>
                        <td>${ item.path }</td>
                        <td class="ty-td-control"><span class="ty-color-blue">${ item.productCount || '0' }种</span></td>
                        <td>
                            <span class="ty-color-blue funBtn" data-fun="manageBtn">管理</span>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                    </tr>
                `
                    })
                    $("#allList").html(str);
                    $(".mainCon0").show().siblings().hide();
                    break;
                default:
                    let html = ``
                    batchEqList = list;
                    $(".readySet").html(0);
                    $(".unCount").html(noCategory);
                    $("#sflag").attr('class','fa fa-circle-o funBtn');
                    setPage($("#ye"), pageInfo.currentPageNo, pageInfo.totalPage, 'equipmentList', jsonStr)

                    list.forEach(item => {
                        let emCount = item.emCount;
                        let size = emCount && emCount !== "" ? emCount.split(",").length : 0;
                        item.ids = item.emCount;
                        html += `
                             <tr class="${size > 1 ? 'isMore' : 'isOne'}">
                                 <td>${ item.lpadId }</td>
                                 <td>${ item.modelCode || '' }/${ item.equipmentName || '' }/${ item.modelName }</td>
                                 <td>${ item.supplierName || '未知' }</td>
                                 <td>${ item.path === '--' ? '尚未选择':item.path  }</td>
                                 <td class="grayBg" data-align="1" onclick="getSameEqList($(this))"><i class="fa fa-sort-down"></i>
                                 <span class="hd">${ JSON.stringify(item) }</span>
                                 </td>
                             </tr>`;
                    })
                    $("#tbContent").html(html);
                    $(".mainCon1").show().siblings().hide();
            }
        }
    })
}
function backMain() {
    $('.filter').show();
    let cur = $("#ye").find(".yecur").html()
    getEqList(cur,0, 'home')
    $("#filterCat").val("").data('id','')
}
function renderBatchEQ(list) {
    // 类别信息
    const catItemNameObj = selectCat.siblings('.catItemName')
    let catInfo = JSON.parse(catItemNameObj.find('.hd').html())
    let str2 = ``
    let selectNum = 0
    list.forEach(item => {
        let size = item.emCount.split(",").length;
        if(item.selected !== 1){
            str2 += `
                <tr ${size > 1 ? "class='isMore'": "class='isOne'"}>
                    <td>
                        <div class="tdRela">
                            <i class="fa fa-square-o funBtn" data-align="2" data-fun="${size > 1 ? "getSameEqList": "toggleCheck"}"></i>
                            <span>${ item.lpadId }</span>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </div>
                    </td>
                    <td>${ item.modelCode || '' }</td>
                    <td>${ item.equipmentName || '' }</td>
                    <td>${ item.modelName || '' }</td>
                    <td>${ item.createName } ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }</td>
                </tr>
                `
        }else if(item.selectedCat === catInfo.id){
            selectNum++;
            str2 += `
                <tr ${size > 1 ? "class='isMore'": "class='isOne'"}>
                    <td>
                        <div class="tdRela">
                        <i class="fa fa-check-square-o funBtn" data-align="2" data-fun="${size > 1 ? "getSameEqList": "toggleCheck"}"></i>
                        <span>${ item.id }</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                        </div>
                    </td>
                    <td>${ item.modelCode || '' }</td>
                    <td>${ item.equipmentName || '' }</td>
                    <td>${ item.modelName || '' }</td>
                    <td>${ item.createName } ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }</td>
                </tr>
                `
        }

    })
    $("#selectTab tbody tr:gt(0)").remove()
    $("#selectTab tbody").append(str2)
    $("#cSelectNum").html(selectNum)

}
var editTrEq = '', editEqList = [];
// creater:hxz 2023-02-23  管理
function manageBtn(thisObj) {
    editTrEq = thisObj
    const info = JSON.parse(thisObj.siblings(".hd").html())
    $("#eqScan .name").html(info.equipmentName || '')
    $("#eqScan .model").html(info.modelName || '')
    $("#eqScan .sup").html(info.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）')
    $("#eqScan .unit").html(info.unitName || '')
    $("#eqInfo").html(JSON.stringify(info));
    getEqDetail(info.ids);
    bounce.show($("#eqScan"))
}
/*creator:lyt 2024/4/8 0008 下午 2:55 */
function getEqDetail (ids) {
    $.ajax({
        'url':'../equipment/model/list',
        'data':{
            "ids": ids,
        },
        success:function(res){
            const list = res || []
            let str2 = ``;
            editEqList = list;
            if (list.length > 1) {
                $(".moreBd").show();
                $(".singleSect").hide();
            } else {
                $(".moreBd").hide();
                $(".singleSect").show();
            }
            $("#eqScan .eqCount").html(list.length)
            list.forEach((item, index) => {
                item.ids = ids;
                item.length = list.length;
                str2 += `
                     <div>
                    <span>创建： ${item.createName} ${new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}</span>
                    <span>系统赋予的编码：${item.lpadId}</span>
                    <span>装备器具的编号：${item.modelCode}</span>
                    <span class="ty-right cateCon" title="${item.path}">类别：${item.path}</span>
                </div>
                <table class="ty-table ty-table-control">
                    <tr>
                        <td>
                            <div>到厂日期</div>
                        </td>
                        <td>
                            <div>到厂时的新旧情况</div>
                        </td>
                        <td>
                            <div>原值</div>
                        </td>
                        <td>
                            <div>预期的使用寿命</div>
                        </td>
                        <td>
                            <div>备注</div>
                        </td>
                        <td>
                            <div>操作</div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="factDate">${new Date(item.receive_date).format('yyyyMM')}</div>
                        </td>
                        <td>
                            <div class="newOrOld"> ${item.conditions == 1 ? '新' : `${ item.conditions == 2 ? '旧' : '' }`}</div>
                        </td>
                        <td>
                            <div class="oral">${item.original_value ? `${item.original_value } 元` : ''}</div>
                        </td>
                        <td>
                            <div class="expireYear">${item.life_span || ''}</div>
                        </td>
                        <td>
                            <div class="memo" title="${item.memo || ""}">${item.memo || ""}</div>
                        </td>
                        <td>
                            <span class="ty-color-blue" onclick="updateEqBtn($(this))">修改</span>
                            <span class="ty-color-blue" onclick="editLog($(this), 2)">修改记录</span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                    </tr>
                </table>`;
            })
            $(".eqDetails").html(str2)
        }
    })
}
// creater:hxz 2023-02-23  管理 - 公共信息的修改
let subObj = ``;
function updateBtn(obj) {
    subObj = obj;
    bounce.show($("#msgEdit"))
    $("#msgEdit input").val('')
    $("#msgEdit select").val('')
    $("#msgEdit textarea").val('')
    $("#msgEdit .bonceHead span").html('公共信息的修改');
    $("#msgEdit").data('source', 1)
    const detail = JSON.parse(obj.siblings(".hd").html());
    // 预备数据
    getNameList($("#eqName"), detail.equipmentId);
    getUnitList($("#unit"),11,detail.unit_id);
    getSupList($("#sup"), detail.supplier);

    $("#model").val(detail.modelName)
    $("#code").val(detail.modelCode)
    $(".eqNum").val(detail.length);
    $(".morePart").show();
    $(".singlePart").show();
    $(".equipEdit").hide();
    $(".morePartTip").hide();
    if (detail.length > 1) {
        $(".publicInfor").show();
    } else {
        $(".publicInfor").hide();
    }
}
function updateEqBtn(obj) {
    subObj = obj;
    bounce.show($("#msgEdit"))
    $("#msgEdit input").val('')
    $("#msgEdit select").val('')
    $("#msgEdit textarea").val('')
    $("#msgEdit").data('source', 2)
    $("#msgEdit .bonceHead span").html('修改装备器具的信息');
    const detail = JSON.parse(obj.siblings(".hd").html());
    // 预备数据
    getNameList($("#eqName"), detail.equipmentId);
    getSupList($("#sup"), detail.supplier);

    if (detail.length > 1) {
        $(".morePart").show();
        $(".singlePart").hide();
        $(".morePartTip").show();
    } else {
        $(".morePart").hide();
        $(".singlePart").show();
        $(".morePartTip").hide();
        getUnitList($("#unit"),11,detail.unit_id);
    }
    $(".publicInfor").hide();
    $(".equipEdit").show();
    $("#cat").data('id',detail.category || '').data('orgId',detail.category || '').val(detail.path)
    $("#model").val(detail.modelName)
    $("#code").val(detail.modelCode)
    $(".eqNum").val(detail.length);

    $("#lifeSpan").val(detail.life_span)
    $("#originalValue").val(detail.original_value)
    $("#conditions").val(detail.conditions)
    $("#memo").val(detail.memo)
    $("#inFDate").val(new Date(detail.receive_date).format("yyyyMM"))
}
function editMsgOk() {
    let equipment = $("#eqName").val();
    if( Number(equipment) === 0 ){
        layer.msg('装备器具名称为必填项！')
        return false
    }
    let source = $("#msgEdit").data('source')
    const info = JSON.parse(subObj.siblings(".hd").html());
    let url = "../equipment/model/edit";
    let data = {'emCount': info.length}
    if (source === 1) { //公共信息的修改
        data.operation = 6;
        if (Number(info.length) > 1) {
            data.ids = info.ids;
            data.eid = $("#eqName").val();
            data.modelName = $("#model").val();
            data.supplier = $("#sup").val();
            data.unitId = $("#unit").val();
            data.unit = $("#unit option:selected").text();
            url = "../equipment/model/emEdit";
        } else {
            data.emCount = 1;
            data.id = info.id;
            $("#msgEdit input:visible").each(function (){
                let name = $(this).attr("name");
                if (name !== 'eqNum') data[name] = $(this).val();
            })
            $("#msgEdit select:visible").each(function (){
                let name = $(this).attr("name");
                data[name] = $(this).val();
            })
        }
    } else {//修改装备器具的信息
        data.id = info.id;
        data.operation = 7;
        data.category = $("#cat").data('id');
        $("#msgEdit input:visible").each(function (){
            let name = $(this).attr("name");
            if (name !== "category") data[name] = $(this).val();
        })
        $("#msgEdit select:visible").each(function (){
            let name = $(this).attr("name");
            data[name] = $(this).val();
        })
        if (Number(info.length) > 1) {
            data.unitId = info.unit_id;
        }
    }
    $.ajax({
        'url': url,
        'data':data,
        success:function(res){
            bounce.show($('#eqScan'));
            let catId = Number($("#filterCat").data('id'))
            let cur = $("#ye0 .yecur").html()
            let category = ''
            let json = $("#ye0 .json").html()
            let type = 'home'
            //let editInfo = JSON.parse(editTrEq.siblings(".hd").html());
            if(catId > 0){ // 筛选的已分类
                cur = $("#ye2 .yecur").html()
                json = $("#ye2 .json").html()
                type = 'filterCatBtn'
            }
            json = json && JSON.parse(json)
            category = json && json.category;

            for(var key in data) {
                info[key] = data[key];
            }
            info.equipmentName = $("#eqName option:selected").text();
            info.supplierName = $("#sup option:selected").text();
            info.ids = res.ids;
            $("#eqScan .name").html(info.equipmentName || '')
            $("#eqScan .model").html(info.modelName || '')
            $("#eqScan .sup").html(info.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）')
            if (source === 1) { //公共信息的修改
                if (info.length > 1) {
                    info.equipmentId = data.eid;
                } else {
                    info.equipmentId = data.equipment;
                }
                info.unit_id = data.unitId;
                info.unitName = $("#unit option:selected").text();
                info.length = !res.ids || res.ids === "" ? 0: res.ids.split(",").length;
                $("#eqScan .unit").html(info.unitName || '')
                $("#eqInfo").html(JSON.stringify(info));
            }
            getEqDetail(res.ids);
            getEqList(cur,category, type);
        }
    })

}
// creater:hxz 2023-02-23  管理 - 修改记录
function editLog(obj, num) {
    const info = JSON.parse(obj.siblings(".hd").html());
    bounce.show($("#editLog"))
    let data = { id: info.id };
    num === 1? data.operation = 6:data.operation = 7;
    $.ajax({
        'url':'../equipment/model/history/histories',
        'method':'get',
        'data': data,
        success:function(res){
            let strTip = ``
            const list = res || []
            if(list.length === 0){
                $("#editLogTab").hide()
                strTip = `<span>当前资料尚未经修改。</span><span class="ty-right"> 创建人：${ info.createName } ${ new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss') }</span>`
            }else{
                $("#editLogTab").show()
                const listLast = list[list.length - 1]
                strTip = `<span>当前资料为第${ list.length-1 }次修改后的结果。</span><span class="ty-right"> 修改人：${ listLast.createName } ${ new Date(listLast.createDate).format('yyyy-MM-dd hh:mm:ss') }</span>`
                let tabStr = ``
                list.forEach( (item, index) => {
                    tabStr += `
                    <tr>
                        <td>${ index === 0 ? '原始信息': `第${ index }次修改后` }</td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue" onclick="logScan($(this), ${num})">查看</span>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                        <td>${ item.createName } ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }</td>
                    </tr>
                    `
                })
                $("#editLogTab tbody tr:gt(0)").remove()
                $("#editLogTab tbody").append(tabStr)
            }
            $(".cOrUtip").html(strTip)
        }
    })

}
function logScan(objThis, soNum) {
    const info = JSON.parse(objThis.siblings('.hd').html())
    $("#eqLogScan .name").html(info.equipmentName)
    $("#eqLogScan .model").html(info.modelName)
    $("#eqLogScan .unit").html(info.unitName)
    $("#eqLogScan .sup").html(info.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）')
    if (soNum === 1) {
        $("#eqLogScan .unit").html(info.unitName)
        $("#eqLogScan .amountLog").html(info.quantity || 0);
        $("#eqLogScan .baseLogScan").show();
        $("#eqLogScan .subLogScan").hide();
    } else {
        $("#eqLogScan .eqNo").html(info.modelCode)
        $("#eqLogScan .cat").html(info.categoryName)
        $("#eqLogScan .expireYear").html(info.lifeSpan)
        $("#eqLogScan .oral").html(info.originalValue ? `${info.originalValue } 元` : '')
        $("#eqLogScan .factDate").html( new Date(info.receiveDate).format('yyyyMM'))
        $("#eqLogScan .newOrOld").html(info.conditions == 1 ? '新' : `${ info.conditions == 2 ? '旧' : '' }`)
        $("#eqLogScan .memo").html(info.memo)
        $("#eqLogScan .baseLogScan").hide();
        $("#eqLogScan .subLogScan").show();
    }
    bounce_Fixed.show($("#eqLogScan"))
}
// creater:hxz 2023-02-23  demo
function setLimitRole(thisObj, all) {
    const limeRole = thisObj.siblings('.limtRole')
    //const txt = limeRole.html();
    //let all = Number(txt.split('/')[1])
    let newV = thisObj.val();
    if(newV.length > all){
        newV = newV.substr(0,all)
        thisObj.val(newV)
    }
    limeRole.html(`${newV.length}/${all}`)
}

laydate.render({elem: '#inFDate',format: 'yyyyMM', 'type':'month'});

// creater:hxz 2023-02-23 名称列表
function getNameList(obj, selectedID) {
    $.ajax({
        'url':'../equipment/list',
        'data':{ 'fullName': '' },
        success:function(res){
            var list = res ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['fullName'] +'</option>';
                }
            }
            obj.html(str);

        }
    })

}
// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectedID) {
    // 　1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料 11装备器具
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['name'] +'</option>';
                }
            }
            obj.html(str);
        }
    })
}
// creater:hxz 2023-02-23 供应商列表
function getSupList(obj,selectedID) {
    $.ajax({
        "url":'../supplier/getSupplierList.do',
        "data":{
            "currentPageNo":1,
            "pageSize":9999,
            'type': 2
        },
        success:function(res) {
            var list = res.data.list|| [] ;
            var str = `<option value="0" ${ selectedID ? '' : 'selected'}>整机系自行装配（零部件可能为外购、外加工或自制）</option>`;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['fullName'] +'</option>';
                }
            }
            obj.html(str);
        }
    })

}

// creater:hxz 2023-02-23 类别列表
let catSelectList = [];
function getCatList(obj, parent) {
    let data = { 'enabled': 1, 'parent':0 }
    if(parent){
        data.parent = parent
    }
    $.ajax({
        "url":'/equipment/category/list',
        "data": data,
        success:function(res) {
            var list = res ;
            var str = `<ul>`
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    let levArr = item.path.split('/');
                    if (!catSelectList.includes(item.id) && (levArr[0] === '一类装备' || levArr[0] === '二类装备' || levArr[0] === '三类装备' || levArr[0] === '四类装备' )) catSelectList.push(item.id);
                    str += `
                        <li>
                            <div class="catName funBtn" data-fun="selectThis" data-id="${ item['id'] }" data-name="${ item['name'] }">${ item['name'] }：${ item['content'] }
                                <span class="hd">${ JSON.stringify(item) }</span>
                            </div>
                        </li>
                    `
                }
            }
            str += '</ul>';
            if(parent){
                obj.after(str);
            }else{
                obj.html(str);
            }
        }
    })
}
// creater:hxz 2023-03-30  选择类别
function selectCatBtn(thisObj) {
    bounce_Fixed.show($("#selectCat"))
    getCatList( $("#catContainer"), '')
    $("#selectCat").data('type','selectCatBtn')
}
function backCon(thisObj) {
    const num = thisObj.data('num')
    $(".mainCon").hide()
    $(`.mainCon${num}`).show()
    if(num == 1){
        batchEqList = []
    }
}
// creater:hxz 2023-02-23 选择类别 - 点击类别
function selectThis(thisObj) {
    const id = thisObj.data('id')
    $("#catContainer .selectedCat").attr('class', 'catName funBtn')
    thisObj.attr('class', 'catName selectedCat funBtn')
    thisObj.siblings('ul').remove()
    getCatList(thisObj , id )
}
//let catLimit = 0; //0=未选过类别，1=选择了一二三四类中的某个类别，2=
function selectCatOk() {
    const selectedCat = $("#catContainer .selectedCat")
    if(selectedCat.length === 0){
        layer.msg('请先选择类别')
        return false
    }
    let id = selectedCat.data('id')
    let name = selectedCat.data('name')
    let catInfo = JSON.parse(selectedCat.find('.hd').html());
    let type = $("#selectCat").data('type')
    let msg = `<p>操作失败！</p><div>已有设备选择了一二三四类中的某个类别后，系统不支持其他设备再选择一二三四类中的其他类别。</div>`;
    if(type === 'filterCatBtn'){
        $("#filterCat").data('id',id).val(name)
        getEqList(1, id, type)
        bounce_Fixed.cancel()
    }else if(type === 'selectCatBtn'){
        let levArr = catInfo.path.split('/');
        if (editEqList.length <= 1 || !(levArr[0] === '一类装备' || levArr[0] === '二类装备' || levArr[0] === '三类装备' || levArr[0] === '四类装备' )) {
            $("#cat").data('id',id).val(name);
            bounce_Fixed.cancel()
        } else {
            let limitCat = ``;
            let editInfo = JSON.parse(subObj.siblings(".hd").html());
            for (let a=0;a<editEqList.length;a++){
                if (editInfo.id !== editEqList[a].id) {
                    let arr = editEqList[a].path.split('/');
                    if (arr[0] === '一类装备' || arr[0] === '二类装备' || arr[0] === '三类装备' || arr[0] === '四类装备' ) {
                        limitCat = arr[0].category;
                    }
                }
            }
            if (limitCat === '') {
                $("#cat").data('id',id).val(name);
                bounce_Fixed.cancel()
            } else {
                layer.msg(msg);
            }
        }
    }else if(type === 'moreSelectPathBtn'){
        selectObj.data('id',id);
        selectObj.prev().html(catInfo.path);
        if(!selectObj.hasClass('isSetted')){
            let len = Number($(".readySet").html()) + 1;
            $(".readySet").html(len);
            selectObj.addClass('isSetted');
        }
        bounce_Fixed.cancel();
    }else if(type === 'selectPathBtn'){
        let levArr = catInfo.path.split('/');
        if (!(levArr[0] === '一类装备' || levArr[0] === '二类装备' || levArr[0] === '三类装备' || levArr[0] === '四类装备' )) {
            selectObj.data('id',id);
            selectObj.removeClass('isSingleOne');
            selectObj.prev().html(catInfo.path);
            bounce_Fixed.cancel()
        } else {
            if (selectObj.parents("tbody").find(".isSingleOne").length > 0) {
                if (selectObj.hasClass("isSingleOne") || (selectObj.parents("tbody").find(".isSingleOne").data("id") === id)) {
                    selectObj.data('id',id);
                    selectObj.prev().html(catInfo.path);
                    bounce_Fixed.cancel()
                } else {
                    layer.msg(msg);
                }
            } else {
                if (catSelectList.includes(id)) {
                    selectObj.addClass("isSingleOne");
                }
                selectObj.data('id',id);
                selectObj.prev().html(catInfo.path);
                bounce_Fixed.cancel()
            }
        }
    }else if(type === 'uniformSet'){
        $("#uniformCatName").data('id',id).val(name)
        bounce_Fixed.cancel()
    }
}
//creator:lyt 2023/12/16 去处理
function handleNoCategory() {
    getEqList(1, -1, 'unHandled');
}
/*creator:lyt 2023/12/18 0018 下午 4:00 */
function getSameEqList(obj){
    let flag = obj.parents("tr").hasClass("isOne");
    if (flag) {
        moreSelectPathBtn(obj);
    } else {
        let icon = obj.data("align");
        let info = ``;
        if(icon === 1) {
            info = JSON.parse(obj.children(".hd").html());
            $("#subPage").data("num", 1);
        } else {
            info = JSON.parse(obj.siblings(".hd").html());
            $("#subPage").data("num", 3);
        }
        catSelectList = [];
        $(".mainCon").hide()
        $(".mainCon5").show()
        $(".setTip .fa").attr("class",'fa fa-circle-o funBtn');
        $("#selectCatPath tr:gt(0)").remove();
        $.ajax({
            'url':'../equipment/model/list',
            'data':{
                "ids": info.emCount
            },
            success:function(res){
                const list = res || []
                let str2 = ``
                list.forEach(item => {
                    str2 += `
                     <tr>
                        <td>${ item.lpadId }</td>
                        <td>${ item.modelCode || '' }</td>
                        <td>${ item.createName } ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }</td>
                        <td>${ item.path === '--' ? '尚未选择':item.path }</td>
                        <td class="grayBg funBtn" data-fun="selectPathBtn">
                            <i class="fa fa-sort-down"></i>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                    </tr>
                `
                })
                $(".sameTip").html(`以下${list.length}台设备的名称均为${info.equipmentName}，型号均为${info.modelName}，来源均为${info.supplierName || '未知'}。`);
                $("#selectCatPath tbody").append(str2)
            }
        })
    }
}
/*creator:lyt 2023/12/19 0019 下午 8:49 */
let selectObj = "";
function selectPathBtn(thisObj) {
    selectObj = thisObj;
    $("#selectCat").data('type','selectPathBtn')
    bounce_Fixed.show($("#selectCat"));
    getCatList( $("#catContainer"), '')
}
function moreSelectPathBtn(thisObj) {
    selectObj = thisObj;
    $("#selectCat").data('type','moreSelectPathBtn')
    bounce_Fixed.show($("#selectCat"));
    getCatList( $("#catContainer"), '')
}
/*creator:lyt 2023/12/20 0020 上午 11:59 */
function selectCatSure(){
    let isSelect = $("#sflag").hasClass('fa-dot-circle-o');
    if($("#tbContent tr").length <= 0){
        return false;
    }
    if(isSelect){
        let str = ``;
        $("#tbContent tr.isOne").each(function (){
            let info = JSON.parse($(this).find('.hd').html());
            let catId = $(this).find(".grayBg").data("id");
            if(catId && catId !== ""){
                str += `ids=${catId}-${info.id}&`
            }
        })
        str = str.slice(0, -1)
        $.ajax({
            'url':`../equipment/model/batchClassification?${ str }`,
            'method':'get',
            success:function(res){
                getEqList(1, -1)
            }
        })
    }else{
        layer.msg('请确认是不是已完成')
    }
}
//creator:lyt 2023/12/18 设置完成
function sameEqComplete(){
    let len = $(".mainCon5 .fa-dot-circle-o").length;
    if($("#selectCatPath tr:gt(0)").length <= 0){
        return false;
    }
    if(len > 0){
        let str = ``;
        $("#selectCatPath tr:gt(0)").each(function (){
            let info = JSON.parse($(this).find('.hd').html());
            let catId = $(this).find(".grayBg").data("id");
            if (catId) {
                str += `ids=${catId}-${info.id}&`
            }
        })
        str = str.slice(0, -1)
        $.ajax({
            'url':`../equipment/model/batchClassification?${ str }`,
            'method':'get',
            success:function(res){
                getEqList(1, -1)
            }
        })
    }else{
        layer.msg('请确认是不是已选择类别')
    }
}
//creator:lyt 2023/12/20 统一设置
function initUniform(){
    $("#uniformCatName").val("");
    bounce.show($("#uniformSettings"));
}
function uniformSet() {
    $("#selectCat").data('type','uniformSet')
    bounce_Fixed.show($("#selectCat"))
    getCatList( $("#catContainer"), '')
}
//creator:lyt 2024/1/2 统一设置确定
function uniformSettingsSure(){//ids的模板为 分类id-装备id
    let catName = $("#uniformCatName").val();
    if(catName !== ""){
        let str = ``;
        let catId = $("#uniformCatName").data("id");
        $("#selectCatPath tr:gt(0)").each(function (){
            let info = JSON.parse($(this).find('.hd').html());
            str += `-${info.id}`
        });
        $.ajax({
            'url':`../equipment/model/batchClassification?ids=${ catId + str }`,
            'method':'get',
            success:function(res){
                bounce.cancel()
                //backCon($("#subPage"))
                getEqList(1, -1)
            }
        })
    }else{
        layer.msg('请确认是不是已选择类别')
    }
}

function fixed2(thisObj) {
    let val = thisObj.val()
    if(val.length > 0){
        thisObj.val(Number(val).toFixed(2))

    }
}