
///装备器具 - 补录 js
$(function () {
    $(".setLimitRoleInput").on('input focus keyup',function(){
        setLimitRole($(this))
    })

    $(".mainCon1").show()
    $("body").on("click",".funBtn, .ty-btn, .linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
})
// creator: hxz，2023-04-17 17:12:49，导入
function importOk(type) {
    if(type === 'cancel'){
        bounce.cancel()
    }else{
        if ($(".matListUpload").length <= 0) {
            layer.msg('您需选择一个文件后才能“导入”！');
        } else {
            const file = $("#leading .fileFullName").data('file')
            $.ajax({
                'url':"../model/import/preImport",
                'data':{ 'path': file.path },
                success:function(res){
                    $(".mainCon").hide()
                    $(".mainCon2").show()
                    let list = res.data || []
                    bounce.cancel()
                    renderPreImportList(list)
                }
            })
        }
    }
}
// creator: hxz，2023-04-20 渲染导入第一步的数据
function renderPreImportList(list) {
    let str = ''
// "注：
// 1  装备器具编号 不能重复
// 2 “装备器具名称”为必填项；
// 3 “到厂日期”型式为六位数字，型如201907，代表2019年07月；
// 4 “预期的使用寿命”单位为年，表格中仅需填入数字，需填入正整数；
// 5 “原值”中填入数据的小数点后需带有两位有效数字。"
    let codeArr = []
    list.forEach( item => {
        str += ` 
                <tr>
                    <td class="${ Number(item.repeat) !== 1 && (item.modelCode && codeArr.indexOf(item.modelCode) === -1) ||  item.modelCode === '' ? codeArr.push(item.modelCode) : 'color-red' }">${ item.modelCode || '' }</td>
                    <td class="${ item.equipmentName ? '' : 'color-red' }">${ item.equipmentName || '--' }</td>
                    <td>${ item.modelName || '' }</td>
                    <td>${ item.unit || '' }</td>
                    <td class="${ ((item.rDate || '').length === 6 && (new RegExp('^[0-9a-zA-Z]{6,16}$')).test(item.rDate)) || item.rDate === '' ? '' : 'color-red' }">${ item.rDate || '' }</td>
                    <td class="${ (item.oLifeSpan && item.oLifeSpan%1 === 0 && item.oLifeSpan>0 ) || item.oLifeSpan === '' ? '' : 'color-red' }"> ${ item.oLifeSpan || '' }</td>
                    <td class="${ (item.oValue && (item.oValue).split(".")[1] && (item.oValue).split(".")[1].length === 2) || item.oValue === ''  ? '' : 'color-red' }">${ item.oValue || '' }</td>
                    <td>
                        <span class="ty-color-blue funBtn" data-fun="editUpload" data-type="1">修改</span>
                        <span class="ty-color-red funBtn" data-fun="delUpload">删除</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>`
    })
    $("#import1").html(str)
    $("#allImportNum").html(list.length)
    $("#redImportNum").html(countRedTrNum())
    $(".mainCon").hide()
    $(".mainCon2").show()
    if(countRedTrNum() === 0){
        uploadNext()
    }

}
// creator: hxz，2023-04-20 计算红的条数
function countRedTrNum(){
    let redTrNum = 0
    $("#import1 tr").each(function(){
        const isred = $(this).find('.color-red')
        if(isred.length > 0){
            redTrNum++
        }
    })
    return redTrNum
}
// creator: hxz，2023-05-06 下一步
function uploadNext() {
    let redNum = Number($("#redImportNum").html())
    let countNum = $("#import1 tr")
    if(redNum > 0){
        layer.msg(`以下${ redNum }条被标为红色的部分不符合右侧的要求，无法保存至系统。`)
    }else{
        $.ajax({
            'url':"../model/import/check",
            'method': 'get',
            success:function(res){
                let list = res || [];
                if(list.length > 0){ // 代号重复的
                    list.forEach(item => {
                        const id = item.id
                        $("#import1 tr").each(function(){
                            let info = JSON.parse($(this).find('.hd').html())
                            if(info.id == id){
                                let codeTd = $(this).children("td:eq(0)")
                                codeTd.attr('class','color-red')
                            }
                        })
                    })
                    layer.msg(`${ list.length }条数据的装备器具编号重复`)
                    $("#redImportNum").html(countRedTrNum())
                }else{
                    uploadNext2()
                }

            }
        })
    }
}
// creator: hxz，2023-05-06 下一步的获取下一步数据
function uploadNext2(){
    $(".mainCon").hide()
    $(".mainCon3").show()
    $.ajax({
        "url":'../supplier/getSupplierList.do',
        "data":{
            "currentPageNo":1,
            "pageSize":9999,
            'type': 2
        },
        success:function(res) {
            var list = res.data.list|| [] ;
            let strTB = ''
            $("#import1 tr").each(function(){
                const item = JSON.parse($(this).find('.hd').html())
                strTB += `
                         <tr>
                            <td>${ item.modelCode }/${ item.equipmentName }/${ item.modelName }</td>
                            <td><input value="${ item.categoryName || '' }" data-id="${ item.category || '' }" class="form-control inputLit funBtn" placeholder="请选择" data-cattype="2" data-fun="selectCatBtn" readonly/></td>
                            <td>
                                <select value="${ item.conditions }" class="form-control inputLit conditions" onchange="editImportBtn($(this), 'conditions')">
                                    <option value="">请选择</option>
                                    <option ${ item.conditions === 1 ? 'selected':''  } value="1">新</option>
                                    <option ${ item.conditions === 2 ? 'selected':''  } value="2">旧</option>
                                </select>
                            </td>
                            <td>
                                <select class="form-control sup inputLit" onchange="editImportBtn($(this), 'supplier')">
                                    ${ setSupSelect(list, item.supplier) }
                                </select>
                            </td>
                            <td><input type="text" value="${ item.memo || '' }" class="form-control memo inputLit" onchange="editImportBtn($(this), 'memo')"/></td>
                            <td>
                                <span class="ty-color-blue funBtn" data-fun="editUpload" data-type="2">修改</span>
                                <span class="ty-color-red funBtn" data-fun="delUpload">删除</span>
                                <span class="hd" >${ JSON.stringify(item) }</span>
                            </td>
                        </tr>
                    `
            })
            $("#tab2 tr:gt(0)").remove()
            $("#tab2").append(strTB)
        }
    })
}
// creator: hxz，2023-04-21 导入-完成
function uploadComplete() {
    $.ajax({
        'url':'../model/import/import',
        success:function(res){
            if(res.code === 0){
                layer.msg('导入成功')
                $(".mainCon").hide()
                $(".mainCon1").show()
            }else{
                layer.msg('导入失败')
            }

        }
    })
}
// creator: hxz，2023-04-17 08:35:09， 初始化上传组建
function initImportUpload() {
    $('#importUploadFile').html('')
    $('#importUploadFile').Huploadify({
        auto:true ,
        fileTypeExts:'*.*;',
        //fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{
            module: '装备器具',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:10240,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader: "../uploads/uploadfyByFile.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item matListUpload" style="display: none">' +
            '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
            '</div>',
        onUploadStart:function(){
        },
        onInit:function(){},
        onSelect:function (file) {
            $('.matListUpload:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data);
            var filePath = data.filename;
            var fileUid =data.fileUid;
            let json = {
                "fileUid": fileUid,
                "path": filePath
            }
            console.log("#leading .fileFullNam", json)
            $("#leading .fileFullName").data('file', json)
        } ,
        onCancel:function(file){
        }
    });
}

// creater:hxz 2023-02-23  刷新限制数目
function setLimitRole(thisObj, all) {
    const limeRole = thisObj.siblings('.limtRole')
    //const txt = limeRole.html();
    //let all = Number(txt.split('/')[1])
    let newV = thisObj.val();
    if(newV.length > all){
        newV = newV.substr(0,all)
        thisObj.val(newV)
    }
    limeRole.html(`${newV.length}/${all}`)
}
// creater:hxz 2023-02-23 名称列表
function getNameList(obj, selectedID) {
    $.ajax({
        'url':'../equipment/list',
        'data':{ 'fullName': '' },
        success:function(res){
            var list = res ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['fullName'] +'</option>';
                }
            }
            obj.html(str);

        }
    })

}
// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectedID) {
    // 　1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料 11装备器具
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['name'] +'</option>';
                }
            }
            obj.html(str);
        }
    })
}
let catSelectList = [];
// creater:hxz 2023-02-23 类别列表
function getCatList(obj, parent) {
    let data = { 'enabled': 1, 'parent':0 }
    if(parent){
        data.parent = parent
    }
    $.ajax({
        "url":'/equipment/category/list',
        "data": data,
        success:function(res) {
            var list = res ;
            var str = `<ul>`
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i]
                    let levArr = item.path.split('/');
                    if (!catSelectList.includes(item.id) && (levArr[0] === '一类装备' || levArr[0] === '二类装备' || levArr[0] === '三类装备' || levArr[0] === '四类装备' )) catSelectList.push(item.id);
                    str += `
                        <li>
                            <div class="catName funBtn" data-fun="selectThis" data-id="${ item['id'] }">${ item['name'] }
                                <span class="hd">${ JSON.stringify(item) }</span>
                            </div>
                        </li>
                    `
                }
            }
            str += '</ul>'
            if(parent){
                obj.after(str);
            }else{
                obj.html(str);
            }

        }
    })
}
var selectCatInput = null
// creater:hxz 2023-03-30  选择类别
function selectCatBtn(thisObj) {
    selectCatInput = thisObj
    bounce_Fixed.show($("#selectCat"))
    getCatList( $("#catContainer"), '')
}
// creater:hxz 2023-02-23 选择类别 - 点击类别
function selectThis(thisObj) {
    const id = thisObj.data('id')
    $("#catContainer .selectedCat").attr('class', 'catName funBtn')
    thisObj.attr('class', 'catName selectedCat funBtn')
    thisObj.siblings('ul').remove()
    getCatList(thisObj , id )
}
function selectCatOk() {
    const selectedCat = $("#catContainer .selectedCat")
    if(selectedCat.length === 0){
        layer.msg('请先选择类别')
        return false
    }
    let id = selectedCat.data('id')
    let name = selectedCat.html()
    const cattype = selectCatInput.data('cattype')
    let catInfo = JSON.parse(selectedCat.find('.hd').html());
    if(cattype === 2){
        let item = JSON.parse(selectCatInput.parents('tr').find('.hd').html())
        let data = {
            'id': item.id,
            'name': name,
            'category': id
        }
        editImport(data,'category')
    }else{
        let levArr = catInfo.path.split('/');
        if (!(levArr[0] === '一类装备' || levArr[0] === '二类装备' || levArr[0] === '三类装备' || levArr[0] === '四类装备' )) {
            selectCatInput.data('id',id).val(name).removeClass('isSingleOne');
            bounce_Fixed.cancel()
        } else {
            if (selectCatInput.parents("tbody").find(".isSingleOne").length > 0) {
                if (selectCatInput.hasClass("isSingleOne") || (selectCatInput.parents("tbody").find(".isSingleOne").data("id") === id)) {
                    selectCatInput.data('id',id).val(name)
                    bounce_Fixed.cancel()
                } else {
                    let msg = `<p>操作失败！</p><div>已有设备选择了一二三四类中的某个类别后，系统不支持其他设备再选择一二三四类中的其他类别。</div>`;
                    layer.msg(msg);
                }
            } else {
                if (catSelectList.includes(id)) {
                    selectCatInput.addClass("isSingleOne");
                }
                selectCatInput.data('id',id).val(name)
                bounce_Fixed.cancel()
            }
        }
    }
}
var conditionsInput = null
function editImportBtn(thisObj, type) {
    conditionsInput = thisObj
    let item = JSON.parse(thisObj.parents('tr').find('.hd').html())
    let data = {
        'id': item.id,
    }
    data[type] = thisObj.val()
    editImport(data,type)
}
// create: hxz 2023-04-21
function editImport(data,type){
    if(type === 'category'){
        var name = data.name
        delete data.name
    }
    $.ajax({
        'url':'../model/import/edit',
        'data':data,
        success:function(res){
            if(res.code === 0){
                switch (type){
                    case 'category':
                        layer.msg('操作成功')
                        selectCatInput.data('id',data.category).val(name)
                        bounce_Fixed.cancel()
                        break;
                    case 'conditions':

                        break;
                    case 'supplier':

                        break;
                    case 'memo':

                        break;
                    default:
                }
            }else{
                layer.msg('操作失败')
                switch (type){
                    case 'category':
                        break;
                    case 'conditions':
                        conditionsInput.val("")
                        break;
                    case 'supplier':
                        conditionsInput.val("0")
                        break;
                    case 'memo':
                        conditionsInput.val("")
                        break;
                    default:
                }
            }
        }
    })
}
// creater:hxz 2023-02-23 供应商列表
function getSupList(obj,selectedID) {
    $.ajax({
        "url":'../supplier/getSupplierList.do',
        "data":{
            "currentPageNo":1,
            "pageSize":9999,
            'type': 2
        },
        success:function(res) {
            var list = res.data.list|| [] ;
            var str = `<option value="0" ${ selectedID ? '' : 'selected'}>整机系自行装配（零部件可能为外购、外加工或自制）</option>`;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['fullName'] +'</option>';
                }
            }
            obj.html(str);
        }
    })
}



// creater:hxz 2023-02-23 零星补录
function litAdd() {
    $("#cat").data('id','')
    $("#litAdd input").val('')
    $("#litAdd select").val('')
    $("#litAdd textarea").val('')
    bounce.show($("#litAdd"));
    $(".impotEdit").hide()
    $(".new").show()
    // 预备数据
    $("#litAdd #amount").val('1')
    $("#litAdd .bonceHead span").html('装备器具的零星补录')
    $("#litAdd .eqOtherItems tr:gt(1)").remove();

    getNameList($("#eqName"));
    getUnitList($("#unit"),11);
    getSupList($("#sup"));
}
// creater:hxz 2023-03-06  零星补录 确定
function litAddOk() {
    let num = Number($("#amount").val());
    if (num >= 1) {
        let addList = [];
        let equipment = $("#eqName").val();
        let model = $("#model").val();
        let sup = $("#sup").val();
        let unit = $("#unit").val();
        if(num > 1 && (model === "" || sup === "" || unit === "") ){
            layer.msg(`<p>操作失败！</p> “数量”大于“1”时，系统要求“装备器具名称”、“型号”、“供应商/加工方”及“单位”都需要有数据！`)
            return false
        }
        if( Number(equipment) > 0 ){

        }else{
            layer.msg('装备器具名称为必填项！')
            return false
        }
        let len = $(".eqOtherItems tr").length - 1;
        if (num === len) {
            $(".eqOtherItems tr:gt(0)").each(function (index){
                let data = {
                    equipment,
                    "modelName" : model, // 型号
                    "modelCode" : $(this).find(".code").val(), // 编号
                    "unitId" : unit, // 单位
                    "category" : $(this).find(".cat").data('id'), //  类别
                    "lifeSpan" : $(this).find(".lifeSpan").val(), //  预期的使用寿命
                    "supplier" : sup, //  供应商
                    "originalValue" : $(this).find(".originalValue").val(), //  原值
                    "rDate" : $(this).children(":eq(3)").find("input").val(), //  到厂日期
                    "conditions" : $(this).find(".conditions").val(), //   到厂时的新旧情况
                    "memo" : $(this).find(".memo").val()
                }
                addList.push(data);
            });
            bounce.cancel()
            $.ajax({
                //'url':'../equipment/model/add',原接口
                'url':'../equipment/model/batchModelAdd',//新接口
                "headers": {
                    "Content-Type": "application/json"
                },
                "data": JSON.stringify(addList),
                "contentType": 'application/json;charset=utf-8',
                success:function(res){
                    if(res.code === 0){
                        layer.msg('操作成功')
                    }else{
                        layer.msg('操作失败')
                    }
                }
            })
        }
    } else {
        layer.msg(`“数量”需大于“0”`);
    }
}
// creater:hxz 2023-02-23 批量补录
function uploadAdd() {
    // 先查看有没有上次的暂存
    $.ajax({
        'url':'../model/import/list',
        success:function(res){
            let list = res || []
            if(list.length > 0){
                renderPreImportList(list)
            }else{
                $('#select_btn_1').val("");
                $('.matListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                initImportUpload()
                bounce.show($("#uploadAdd"));
            }
        }
    })


}
var editTr = null
// creater:hxz 2023-02-23 批量补录 - 导入 - 修改
function editUpload(thisObj) {
    bounce.show($("#litAdd"));
    $(".impotEdit").show()
    $(".new").hide()
    $("#litAdd .bonceHead span").html('装备器具基本信息的修改')
    $("#litAdd input").val('')
    $("#litAdd select").val('')
    $("#litAdd textarea").val('')
    const info = JSON.parse(thisObj.siblings('.hd').html())
    editTr = thisObj
    // 预备数据
    getNameList($("#eqName_e"), info.equipment);
    getUnitList($("#unit_e"),11, info.unitId);
    //$("#eqName_e").val(info.equipmentName)
    $("#model_e").val(info.modelName)
    //$("#unit_e").val(info.unit)
    $("#code_e").val(info.modelCode)
    if(info.oValue && (info.oValue).split(".")[1] && (info.oValue).split(".")[1].length === 2 ){
        $("#originalValue_e").val(info.oValue)
    }else{
        $("#originalValue_e").val('')

    }
    if(info.oLifeSpan && info.oLifeSpan%1 === 0 && info.oLifeSpan>0 ){
        $("#lifeSpan_e").val(info.oLifeSpan)
    }else{
        $("#lifeSpan_e").val('')
    }
    if((info.rDate || '').length === 6 && (new RegExp('^[0-9a-zA-Z]{6,16}$')).test(info.rDate) ){
        $("#inFDate_e").val(info.rDate)
    }else{
        $("#inFDate_e").val("")
    }



}
// creater:hxz 2023-02-23 批量补录 - 导入 - 修改确定
function importEditOk() {
    const info = JSON.parse(editTr.siblings('.hd').html())
    let data = {
        'id': info.id,
        'equipment': $("#eqName_e").val(),
        'equipmentName': $("#eqName_e option:selected").text(),
        'modelName': $("#model_e").val(),
        'modelCode': $("#code_e").val(),
        'unit': $("#unit_e").val() === ""?"":$("#unit_e option:selected").text(),
        'unitId': $("#unit_e").val(),
        'oLifeSpan': $("#lifeSpan_e").val(),
        'oValue': $("#originalValue_e").val(),
        'rDate': $("#inFDate_e").val(),
    }
    if(data.equipment === ""){
        data.equipmentName = '';
    }
    // 就是 code  name specifications model
    if(data.equipmentName === info.equipmentName){
        delete data.equipmentName
    }
    if(data.modelName === info.modelName){
        delete data.modelName
    }
    var changeRepeat = true
    if(data.modelCode === info.modelCode){
        delete data.modelCode
        changeRepeat = false
    }
    $.ajax({
        'url':"../model/import/edit",
        'data':data,
        success:function(res){
            if(res.code === 0){
                layer.msg('操作成功')
                bounce.cancel()
                //info.equipmentName = data.equipmentName || info.equipmentName
                //info.modelName = data.modelName || info.modelName
                info.equipment = data.equipment === "" ? data.equipment : data.equipment || info.equipment
                info.equipmentName = data.equipmentName === "" ? data.equipmentName : data.equipmentName || info.equipmentName
                info.modelName = data.modelName === "" ? data.modelName : data.modelName || info.modelName
                info.modelCode = data.modelCode === "" ? data.modelCode : data.modelCode || info.modelCode
                info.unit = data.unit
                info.oLifeSpan = data.oLifeSpan
                info.oValue = data.oValue
                info.rDate = data.rDate
                if(changeRepeat){
                    info.repeat = ''
                }
                let item = info
                let type = editTr.data('type')
                if(type === 1){
                    let codeArr = []
                    $("#import1 tr").each(function () {
                        let trInfo = JSON.parse($(this).find('.hd').html())
                        if(trInfo.id != info.id){
                            codeArr.indexOf(trInfo.modelCode) === -1 ? codeArr.push(trInfo.modelCode) : ''
                        }
                    })
                    let str = ``
                    str = ` 
                     <td class="${Number(item.repeat) !== 1 && (item.modelCode && codeArr.indexOf(item.modelCode) === -1) ||  item.modelCode === '' ? codeArr.push(item.modelCode) : 'color-red' }">${ item.modelCode || '' }</td>
                    <td class="${ item.equipmentName ? '' : 'color-red' }">${ item.equipmentName || '--' }</td>
                    <td>${ item.modelName || '' }</td>
                    <td>${ item.unit || '' }</td>
                    <td class="${ ((item.rDate || '').length === 6 && (new RegExp('^[0-9a-zA-Z]{6,16}$')).test(item.rDate)) || item.rDate === '' ? '' : 'color-red' }">${ item.rDate || '' }</td>
                    <td class="${ (item.oLifeSpan && item.oLifeSpan%1 === 0 && item.oLifeSpan>0 ) || item.oLifeSpan === '' ? '' : 'color-red' }"> ${ item.oLifeSpan || '' }</td>
                    <td class="${ (item.oValue && (item.oValue).split(".")[1] && (item.oValue).split(".")[1].length === 2) || item.oValue === ''  ? '' : 'color-red' }">${ item.oValue || '' }</td>
                    <td>
                        <span class="ty-color-blue funBtn" data-fun="editUpload" data-type="1">修改</span>
                        <span class="ty-color-red funBtn" data-fun="delUpload">删除</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>`
                    editTr.parents('tr').html(str)
                    $("#redImportNum").html(countRedTrNum())

                }else{
                    editTr.parent('td').siblings(":first").html(`${ item.modelCode }/${ item.equipmentName }/${ item.modelName }`)
                    editTr.siblings(".hd").html(JSON.stringify(item))
                }
                $("#redImportNum").html(countRedTrNum())

            }else{
                layer.msg('操作失败')
            }
        }
    })

}
// creater:hxz 2023-02-23 批量补录 - 导入 - 删除
function delUpload(obj) {
    editTr = obj
    bounce.show($("#uploadAddDeltip"));
}
function uploadAddDeltipOk() {
    let info = JSON.parse(editTr.siblings('.hd').html())
    bounce.cancel();
    removePreEq(info.id, 'single')
}
function delAlltipOk() {
    let sr = []
    $("#import1 tr").each(function(){
        console.log($(this).find('.hd').html())
        let info = JSON.parse($(this).find('.hd').html())
        sr.push(info.id)
    })
    removePreEq(sr.join(), 'all')
}
function uploadCancel() {
    bounce.show($("#delAlltip"))
}
function removePreEq(ids, type) {
    console.log('ids=', ids)
    bounce.cancel()
    $.ajax({
        'url':'../model/import/remove',
        'data':{ 'ids': ids },
        success:function(res){
            layer.msg(res.msg)
            if(res.msg === '操作成功'){
                if(type === 'all'){
                    $(".mainCon").hide()
                    $(".mainCon1").show()
                }else if(type === 'single'){
                    let all = Number($("#allImportNum").html())
                    all = all - 1
                    if(all === 0){
                        layer.msg("本次导入数据已经全部删除")
                        $(".mainCon").hide()
                        $(".mainCon1").show()
                    }else{
                        $("#allImportNum").html(all)
                        editTr.parents('tr').remove()
                        $("#redImportNum").html(countRedTrNum())
                    }

                }

            }
        }
    })
}
// creater:hxz 2023-02-23 新增名称
function addName() {
    $("#addName input").val("")
    bounce_Fixed.show($("#addName"));
    $("#addName .limtRole").html('0/8')
}
function addNameOk() {
    const txt = $("#addName input").val()
    if(txt.length === 0){
        layer.msg('装备器具的名称不能为空')
        return false
    }
    bounce_Fixed.cancel()
    $.ajax({
        'url':'../equipment/add',
        'data':{ 'fullName': txt },
        success:function(res){
            console.log(res)
            if(res.code === 0){
                layer.msg('新增成功')
                getNameList($("#eqName"));
            }else{
                layer.msg('操作失败，因为系统里已有这个名称了！')

            }

        }
    })
}
// creater:hxz 2023-02-23 新增单位
function addUnit() {
    $("#addUnit input").val("")
    bounce_Fixed.show($("#addUnit"));
}
// creator: hxz，2020-09-02 14:51:41，确定新增计量单位
function addUnitOk() {
    var module = 11
    var name = $("#unitName").val();
    bounce_Fixed.cancel();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": 11 },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                var idStr = $("#updateType").val();
                getUnitList($("#unit"), 11);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                layer.msg(tipStr)
            }
        }
    })
}

// creater:hxz 2023-02-23 新增供应商
function addSup(thisObj) {
    let type = thisObj.data('suptype')
    $("#addSup").data("suptype", type)
    $("#addSup input").val("")
    bounce_Fixed.show($("#addSup"))

}
function addSupOk() {
    bounce_Fixed.cancel()
    let suptype = $("#addSup").data("suptype")
    let data = {'type':2}
    data.fullName = $("#supFullName").val()
    data.name = $("#supName").val()
    data.codeName = $("#supCode").val()
    $.ajax({
        'url':'../supplier/addSupplier.do',
        'data':data,
        success:function(res){
            console.log(res)
            if(res.success === 1){
                layer.msg('新增成功')
                if(suptype === 2){
                    refrashSupSelect()
                }else{
                    getSupList($("#sup"));
                }
            }else{
                layer.msg('新增失败！')
            }
        }
    })
}

function refrashSupSelect() {
    $.ajax({
        "url":'../supplier/getSupplierList.do',
        "data":{
            "currentPageNo":1,
            "pageSize":9999,
            'type': 2
        },
        success:function(res) {
            var list = res.data.list|| [] ;
            $("#tab2 tr:gt(0)").each(function(){
                let supSelect = $(this).find('.sup')
                let supVal = supSelect.val()
                supSelect.html(setSupSelect( list, supVal))
            })
        }
    })
}
function setSupSelect(list, selectedID) {
    var str = `<option value="0" ${ selectedID ? '' : 'selected'}>整机系自行装配（零部件可能为外购、外加工或自制）</option>`;
    if(list && list.length >0){
        for(var i = 0 ; i < list.length ; i++){
            var item = list[i], seleStr = " ";
            if(selectedID == item["id"]) { seleStr = " selected" }
            str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['fullName'] +'</option>';
        }
    }
    return str
}
function fixed2(thisObj) {
    let val = thisObj.val()
    if(val.length > 0){
        thisObj.val(Number(val).toFixed(2))

    }
}
function changeLitSnGroups(obj){
    let num = obj.val();
    if (num >= 1) {
        let line = $(".eqOtherItems tr").length -1;
        let count = num - line;
        if (count > 0) {
            for (let i=0;i<count;i++){
                addSnItem();
            }
        } else if (count < 0){
            $(".eqOtherItems tr:gt("+ num +")").remove();
        }
    } else {
        obj.val("1");
        $(".eqOtherItems tr:gt(1)").remove();
    }
}
function addSnItem(){
    let num = $(".eqOtherItems tr").length ;
    let html = `<tr>
                    <td>
                        <input type="text" id="code" class="form-control code" placeholder="请录入"/>
                    </td>
                    <td>
                        <input type="text" class="form-control litInput originalValue" placeholder="请录入" onkeyup="clearNoNumN(this, 2)" onchange="fixed2($(this))" /><span class="litUnit">元</span>
                    </td>
                    <td>
                        <input type="text" class="form-control litInput lifeSpan" placeholder="请录入" id="lifeSpan" onkeyup="clearNum(this)"/><span class="litUnit">年</span>
                    </td>
                    <td>
                        <input type="text" class="form-control inFDate${num}" readonly placeholder="请选择"/>
                    </td>
                    <td>
                        <select class="form-control conditions">
                            <option value="">请选择</option>
                            <option value="1">新</option>
                            <option value="2">旧</option>
                        </select>
                    </td>
                    <td>
                        <input type="text" class="form-control funBtn cat" readonly data-fun="selectCatBtn" />
                    </td>
                    <td colspan="2">
                        <input type="text" class="form-control textarea setLimitRoleInput memo" placeholder="可录入不超过50字" onchange="setLimitRole($(this), 50)" />
                    </td>
                </tr>`;
    $(".eqOtherItems tbody").append(html);
    laydate.render({elem: '.inFDate' + num,format: 'yyyyMM', 'type':'month'});
}
laydate.render({elem: '#inFDate',format: 'yyyyMM', 'type':'month'});
laydate.render({elem: '#inFDate_e',format: 'yyyyMM', 'type':'month'});
