/*
* Created by sy 2023-02-08
 */
//初始化三级弹窗
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
// bounce_Fixed2.show($("#addcate"));    这一行可以随便选择一个div
bounce_Fixed2.cancel();
var bounce_Fixed = new Bounce(".bounce_Fixed");
// bounce_Fixed.show($("#addcate"));    这一行可以随便选择一个div
bounce_Fixed.cancel();

$(function(){
    //对输入框中输入的字符进行统计
    $("#addequname").on('input focus keyup',function(){
        var str  = $(this).val();
        var remain = str.length;
        $(this).next().next().html(remain);
        //设置input输入框最多能输入多少字符👇
        $(this).attr('maxlength',8);
    })
    //展示装备器具列表
    getlistall(1,20);
    $("#se1").val("");
})

// creator: sy 2023-02-13 展示装备器具列表
function getlistall(currentPageNo,pageSize,keyword){
    $.ajax({
        url:"../equipment/list",
        type:"post",
        data:{
            //fullName:名称模糊查询
            //fullName的值若是没有就是空
            fullName:keyword
        },
        success:function(res){
            let bank = res || [];
            if(keyword == undefined){//非搜索后列表
                $("#equipmentmess").html("");
                $("#addnewna").data("lunk",res);
            }else{//搜索后列表
                $("#seanameank").html("");
            }
            if(bank != undefined && bank.length >0){
                for(var i = 0; i<bank.length;i++){
                    var langer = bank.length;
                    if(keyword == undefined){//初始总列表
                        $("#describe .number").html(langer);
                    }else{//搜索后的列表
                        $("#several").html(langer);
                    }
                    var str = ``;
                    str += `
                        <tr>
                            <td>${bank[i].fullName}</td>
                            <td>`;
                            if(bank[i].modelCount == null){
                                str += `<span id="onexh">--</span>`;
                            }else{
                                str += `<span id="onexh">${handleNull(bank[i].modelCount)}个型号</span>`;
                            }
                            str +=`
                            </td>
                            <td>`;
                            if(bank[i].supplierCount == null) {
                                str += ` <span id="onecl">--</span>`;
                            }else {
                                str += `<span id="onecl">${handleNull(bank[i].supplierCount)}个来源</span>`;
                            }
                            str += `
                            </td>
                            <td>${handleNull(bank[i].units)}</td>
                            <td class="ty-td-control">`;
                            if(bank[i].quantity == null){
                                str += `<span class="ty-color-blue funBtn" onclick="quantitymess($(this))" id="tunch">--</span>`;
                            }else{
                                str += `<span class="ty-color-blue funBtn" onclick="quantitymess($(this))" id="tunch">${handleNull(bank[i].quantity)}种</span>`;
                            }
                            str += `
                            </td>
                            <td>${new Date(handleNull(bank[i].createDate)).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td>
                                <span class="ty-color-blue funBtn" onclick="upeqname($(this))" id="upname">修改名称</span>
                                <span class="ty-color-blue funBtn" onclick="upnamerecord($(this))">名称修改记录</span>
                                <span class="hd">${JSON.stringify(res[i])}</span>
                            </td>
                        </tr> `;
                    if(keyword == undefined){//初始列表
                        $("#equipmentmess").append(str);
                    }else{//搜索后的列表
                        $("#seanameank").append(str);
                    }
                }
            }
        }
    })
}

// creator: sy 2023-02-21 显示搜索结果
function getsecher(){   //若不想同一个接口调用两遍的话
    var name = $("#se1").val();
    $("#nameagent").hide();
    $("#searchname").show();
    getlistall(1,20,name);
}

// creator: sy 2023-02-09 显示新增装备器具名称弹窗
function addname(obj){
    $("#tishi").html(0);
    $("#addne").html("新增名称");
    $("#addsubmit").show();
    $("#upmodify").hide();
    $("#addequname").val("");
    var bonk = $("#addnewna").data("lunk");
    $("#addsubmit").data("box",bonk);
    bounce.show($("#addname"));
}

// creator: sy 2023-02-10 点击新增装备名称弹窗中“确定”按钮
function addsubna(){
    //需要将获取到的输入框中的值跟首页列表中的特定属性值进行比较，若是相同给予3秒提示，否则走下面的接口
    //对于小于等于0和出现重复的情况给予提示，输入框输入超过8位无法输入
    //范例：var one  = json.name;
    //  if(addname == name){
    //       layer.msg("操作失败，因为系统里已有这个名称了！");
    //}else{
    //      走接口
    //  }
    var addname = $("#addequname").val();
    var lank = $("#addsubmit").data("box");
    let d = 0;
    for(var a = 0;a<lank.length;a++){
        var name = lank[a].fullName;
        if(name == addname){
            d = 1;
            layer.msg("操作失败，因为系统里已有这个名称了！");
            return false;
        }else if(addname.length == 0 || addname.length < 0){
            d = 1;
            layer.msg("操作失败，因为系统里已有这个名称了！");
            return false;
        }else{
            d = 0;
        }
    }
    if(d == 0){
        $.ajax({
            url:'../equipment/add',
            type:"post",
            data:{
                //fullName:装备器具名称
                fullName:addname
            },
            success:function(res){
                bounce.cancel($("#addname"));
                getlistall(1,20);
            }
        })
    }else{
        bounce.show($("#addname"));
    }
}

// creator: sy 2023-02-10 点击装备器具列表中的“修改名称”按钮
function upeqname(obj){
    //获取装备器具对应的字段的值，将值显示在$("#addequname")输入框中
    var lank = obj.next().siblings(".hd").html();
    lank = JSON.parse(lank);
    var fullName = lank.fullName;
    var id = lank.id;
    $("#upmodify").data("upid",id);
    $("#addequname").val(fullName);
    var lend = fullName.length;
    $("#tishi").html(lend);
    $("#addne").html("修改名称");
    $("#addsubmit").hide();
    $("#upmodify").show();
    bounce.show($("#addname"));
}

// creator: sy 2023-02-10 点击修改装备名称弹窗中的“确定”按钮
function upmodina(){
    var upname = $("#addequname").val();
    var upid = $("#upmodify").data("upid");
    var lank1 = $("#addnewna").data("lunk");
    let c = 0;
    for(var a = 0;a<lank1.length;a++){
        var name = lank1[a].fullName;
        if(upname == name){
            c = 1;
            layer.msg("操作失败，因为系统里已有这个名称了！");
            return false;
        }else if(upname.length == 0 || upname.length < 0){
            c = 1;
            layer.msg("操作失败，因为系统里已有这个名称了!");
            return false;
        }else{
            c = 0;
        }
    }
    if(c == 0){
        $.ajax({
            url:'../equipment/edit',
            type:"post",
            data:{
                //id:装备器具id,
                //fullName:新名称
                id:upid,
                fullName:upname
            },
            success:function(res){
                getlistall(1,20);
                bounce.cancel($("#addname"));
            }
        })
    }else{
        bounce.show($("#addname"));
    }
}

// creator: sy 2023-02-10 点击装备器具列表中的“名称修改记录”按钮
function upnamerecord(ong){
    var link = ong.siblings(".hd").html();
    link = JSON.parse(link);
    var unid = link.id;
    unid = Number(unid);
    $.ajax({
        url:"../equipment/history/details",
        type:"POST",
        data:{
            //id:名称id
            id:unid
        },
        success:function(res){
            //注意：第几次修改取versionNo,当versionNo为0时是“数据的创建"
            $("#takenotes").html("");
            if(res != undefined && res.length >0){
                for(var i = 0;i<res.length;i++){
                    var versionNo = res[i].versionNo;
                    var str3 = ``;
                    if(versionNo == 0){//versionNo = 0时
                        str3 += `
                            <tr>
                                <td>数据的创建</td>
                                <td>${handleNull(res[i].createName)}&nbsp;${new Date(handleNull(res[i].createDate)).format("yyyy-MM-dd hh:mm:ss")}</td>
                                <td>${res[i].fullName}</td>
                            </tr>`;
                    }else{
                        str3 += `
                        <tr>
                            <td>第<span>${res[i].versionNo}</span>次修改后</td>
                            <td>${handleNull(res[i].createName)}&nbsp;${new Date(handleNull(res[i].createDate)).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td>${res[i].fullName}</td>
                        </tr>`;
                    }
                    $("#takenotes").append(str3);
                }
            }
        }
    })
    bounce.show($("#modificationrecord"));
}

// creator: sy 2023-02-10 点击数量列表中的“直接查看全部”按钮
function getlookall(){
    //传值时型号传空就可以获取到该装备器具名称下的所有型号的数据了
    var name = $("#zbqjna").html(); //名称
    $(".eqcaname1").html(name);
    var id = $("#lookall").data("id");
    getcatelink(2,"",id);

    $("#categorycode").show();
    $("#quantitydetails").hide();
}

// creator: sy 2023-03-31 获取型号列表数据
function getcatelink(key,name,id){
    var jsond = {}
    if(key == 2){
        jsond = {
            equipment:id
        }
    }else if(key == 1){
        jsond = {
            modelName:name,
            equipment:id
        }
    }
    $.ajax({
        url:"../equipment/model/list",
        type:"POST",
        // data:{
        //     //modelName:汇总列表返回的型号名称
        //     modelName:name,
        //     equipment:id
        // },
        data:jsond,
        success:function(res){
            let lunk = res || [];
            if(key == 1){
                var stonal = [];//来源
                var all = [];//全部的
                $("#codenamey").html("");
                $("#screenthng").html("");
                if( lunk.length >0){
                    let hasS0 = false;
                    for(var i =0;i<lunk.length;i++){
                        var lang = lunk.length;
                        $(".acover").html(lang);
                        var str2 = ``;
                        str2 += `
                            <tr>
                                <td>${handleNull(lunk[i].lpadId)}</td>
                                <td>${handleNull(lunk[i].modelCode)}</td>`;
                        if(lunk[i].supplier == 0) {
                            str2 += `<td>整机系自行装配（零部件可能为外购、外加工或自制）</td>`;
                        }else {
                            str2 += ` <td>${handleNull(lunk[i].supplierName)}</td>`;
                        }
                        str2 += `
                                <td>${handleNull(lunk[i].path)}</td>
                                <td>${handleNull(lunk[i].createName)}&nbsp;${new Date(handleNull(lunk[i].createDate)).format("yyyy-MM-dd hh:mm:ss")}</td>
                                <td>
                                    <span class="ty-color-blue funBtn numbeqn" onclick="lookallmore()">${handleNull(lunk[i].productCount)}种</span>
                                    <span class="hd">${JSON.stringify(lunk[i])}</span>
                                </td>
                            </tr>`;
                        $("#codenamey").append(str2);
                        //判断supplierName是否有值，若有值则展示否则为空
                        lunk[i]['supplierName'] ?  stonal.push(lunk[i].supplierName) : "";
                        if(lunk[i].supplier == 0){
                            //当supplier == 0时hasS0=true
                           hasS0 = true;
                        }
                        all.push(lunk[i]);//存储全部数据
                    }
                    if(hasS0 == true){//将所要展示的文字只在数组中插入一遍
                        stonal.push("整机系自行装配（零部件可能为外购、外加工或自制）");
                    }
                    $("#screenthng").data("box",stonal);
                    $("#screenthng").data("all",all);
                    var str = "";
                    var newbk = [];
                    var newtno = [];
                    stonal.forEach(item =>{//遍历stonal
                        console.log(newbk.indexOf(item));   //查看item单个数据对应的索引值
                        newbk.indexOf(item) === -1 ? newbk.push(item) : "";
                    })
                    var optionStr = "";
                    newbk.forEach(item => {//遍历newbk
                        optionStr += `<option value="${item}">${item}</option>`;
                    })
                    $("#screenthng").html(optionStr);//将获取到的数据在筛选框中展示

                    // if(stonal.length == 0){
                    //     $("#screenthng").html("");
                    // }else{}

                }else{
                    $(".acover").html(0);
                }
            }else if(key == 2){
                $("#codingdetails").html("");
                if(lunk != undefined && lunk.length >0){
                    for(var j =0;j<lunk.length;j++){
                        var lang2 = lunk.length;
                        $(".acover1").html(lang2);
                        var str4 = ``;
                        str4 += `
                            <tr>
                                <td>${handleNull(lunk[j].lpadId)}</td>
                                <td>${handleNull(lunk[j].modelCode)}</td>
                                <td>${handleNull(lunk[j].modelName)}</td>`;
                        if(lunk[j].supplier == 0){str4 += `<td>整机系自行装配（零部件可能为外购、外加工或自制）</td>`;}
                        else{str4 +=`<td>${handleNull(lunk[j].supplierName)}</td>`;}
                        str4 += `
                                <td>${handleNull(lunk[j].path)}</td>
                                <td>${handleNull(lunk[j].createName)}&nbsp;${new Date(handleNull(lunk[j].createDate)).format("yyyy-MM-dd hh:mm:ss")}</td>
                                <td>
                                    <span class="ty-color-blue funBtn numbeqn" onclick="lokallmoeall()">${handleNull(lunk[j].productCount)}种</span>
                                </td>
                            </tr>`;
                        $("#codingdetails").append(str4);
                    }
                }else if(lunk.length == 0){
                    $(".acover1").html(0);
                }
            }
        }
    })
}

// creator: sy 2023-03-16 点击“参与加工的产品”下面的XX种按钮
function lokallmoeall(){
    var k = 2;
    $(".numbeqn").data("oon",k);
    $("#categorycode").hide();
    $("#drawingnumber").show();
    $("#allcodeproduct").html("");
}

// creator: sy 2023-03-13 筛选框进行筛选
function chonseotherq(){
    var name = $("#screenthng option:selected").html();//选择的来源
    var box = $("#screenthng").data("all"); //筛选框下面列表中所有数据
    //思路：获取到name，name是筛选框中选择的想要查看的来源，筛选框供选择的部分数据根据筛选框下面表格中“来源”一列的数据进行显示；倘若选择了某一个供应商
    //名称，则从筛选框下面的列表中的数据里寻找含有该供应商名称的数据，将其显示在表格中即可。
    var strt1 = "";
    $("#codenamey").html("");
    for(var i = 0;i<box.length;i++){
        if(box[i].supplierName == name){
            $(".acover").html(box.length);
            strt1 +=`
                <tr>
                    <td>${handleNull(box[i].id)}</td>
                    <td>${handleNull(box[i].modelCode)}</td>
                    <td>${handleNull(box[i].supplierName)}</td>
                    <td>${handleNull(box[i].path)}</td>
                    <td>${handleNull(box[i].createName)}&nbsp;${new Date(handleNull(box[i].createDate)).format("yyyy-MM-dd hh:mm:ss")}</td>
                    <td>
                        <span class="ty-color-blue funBtn numbeqn" onclick="lookallmore()">${handleNull(box[i].productCount)}种</span>
                        <span class="hd">${JSON.stringify(box[i])}</span>
                    </td>
                </tr>`;
            $("#codenamey").append(strt1);
        }else{
            $(".acover").html(0);
        }
    }
}

// creator: sy 2023-02-14 点击返回按钮回到首页
function backerimg(){
    $("#quantitydetails").hide();
    $("#nameagent").show();
    getlistall(1,20);
}

// creator: sy 2023-02-24 点击返回按钮回到列表首页
function turnback(){
    $("#searchname").hide();
    $("#nameagent").show();
    $("#se1").val("");
    getlistall(1,20);
}

// creator: sy 2023-02-14 点击首页名称列表中的”数量“进行跳转
function quantitymess(jse){
    var lanks = jse.parent("td").next().next().find(".hd").html();
    lanks = JSON.parse(lanks);
    var id = lanks.id;
    $("#lookall").data("id",id);
    // $(".listlook").data("id",id);
    var fullName = lanks.fullName;
    $("#zbqjna").html(fullName);
    id = Number(id);
    $.ajax({
        url:"../equipment/model/groupList",
        type:"GET",
        data:{
            //equipment:名称管理列表返回的id
            equipment:id
        },
        success:function(res){
            $("#modelbox").html("");
            if(res != undefined && res.length >0){
                for(var i = 0; i<res.length;i++){
                    var nambe = res.length;
                    $("#pace").html(nambe);
                    var str1 = ``;
                    str1 += `
                        <tr>
                            <td>${handleNull(res[i].modelName)}</td>
                            <td>${handleNull(res[i].count)}</td>
                            <td>
                                <span>${handleNull(res[i].supplierCount)}</span>个来源
                            </td>
                            <td>
                                <span class="ty-color-blue funBtn listlook" onclick="lookmore($(this))">查看</span>
                                <span class="hd">${JSON.stringify(res[i])}</span>
                                <span class="hd">${id}</span>
                            </td>
                        </tr>`;
                    $("#modelbox").append(str1);
                }
            }
            else if(res.length == 0){
                $("#pace").html(0);
            }
        }
    })
    $("#nameagent").hide();
    $("#searchname").hide();
    $("#quantitydetails").show();
}

// creator: sy 2023-02-15 点击型号列表中的查看
function lookmore(lank){
    var list = lank.siblings(".hd").html();
    list = JSON.parse(list);
    var modelName = list.modelName; //型号
    var name = $("#zbqjna").html(); //名称
    $(".modeler").html(modelName);
    $(".eqcaname").html(name);
    var unid = lank.next().next().html();
    unid = JSON.parse(unid);
    getcatelink(1,modelName,unid);

    $("#quantitydetails").hide();
    $("#drawingnumber").hide();
    $("#screenboder").show();
}

// creator: sy 2023-02-15 点击返回按钮返回到上一页
function backerimgup(){
    $("#categorycode").hide();
    $("#quantitydetails").show();
}

// creator:sy 2023-02-15 点击“参与加工的产品”下面的XX种按钮
function lookallmore(){
    // $.ajax({
    //     url:"../",
    //     type:"POST",//"GET"
    //     data:"",
    //     success:function(res){
    //
    //     }
    // })
    var k = 1;
    $(".numbeqn").data("oon",k);
    $("#categorycode").hide();
    $("#drawingnumber").show();
    $("#screenboder").hide();
    $("#allcodeproduct").html("");
}

// creator: sy 2023-02-15 点击返回按钮返回到上一页
function backerimgmid(){
    var knw = $(".numbeqn").data("oon");
    if(knw == 1){
        $("#screenboder").show();
        $("#drawingnumber").hide();
    }else if(knw == 2){
        $("#categorycode").show();
        $("#drawingnumber").hide();
    }
}

// creator: sy 2023-02-15 点击“本装备用于本产品的操作记录”按钮
function viewrecords(){

    bounce.show($("#producnacord"));
}

// creator: sy 2023-02-15 点击返回按钮回到上一页
function gunback(){
    $("#screenboder").hide();
    $("#quantitydetails").show();
}

// creator: sy 2023-02-22 处理null
function handleNull(str){
    var result = str == null || str == undefined || str == 'null' || ''?'--':str;
    return result;
}