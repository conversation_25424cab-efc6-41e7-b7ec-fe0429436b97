
//装备器具 - 供应商 js
$(function () {
    getSupList()
    $(".mainCon1").show()
    $("body").on("click",".funBtn, .ty-btn, .linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
})
// creater:hxz 2023-02-23  刷新限制数目
function getSupList(fullName, enabled) {
    // enabled  0 - 已暂停采购的供应商  1-正常采购的供应商
    // fullName 名称模糊查询
    $.ajax({
        'url':'../equipment/supplier/list',
        'data': {'fullName': fullName , 'enabled': enabled},
        success:function(res){
            const list = res || []
            let str = ''
            list.forEach( item => {
                str += `
                     <tr>
                        <td>${ item.fullName }</td>
                        <td>${ item.createName } ${ new Date(item.createName).format('yyyy-MM-dd hh:mm:ss')}</td>
                        <td class="ty-td-control">${ item.exclusiveCount }种</td>
                        <td>
                            <span class="ty-color-blue">查看</span>
                            <span class="ty-color-blue">修改</span>
                            <span class="ty-color-blue">删除</span>
                            <span class="ty-color-blue">暂停采购</span>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                     </tr>
                `
            })
            $("#supTab").html(str)
            $("#num1").html(list.length)


        }
    })

}


laydate.render({elem: '#inFDate'});
