/*
* Created by sy 2023-02-16
 */
var user = null;
var contractDel = null;
var contractInfo = null;
var editContractObj = null;
//初始化三级弹窗
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#addcate"));
bounce_Fixed2.cancel();
var bounce_Fixed = new Bounce(".bounce_Fixed");
// bounce_Fixed.show($("#addcate"));    这一行可以随便选择一个div
bounce_Fixed.cancel();

$(function(){
    //进行输入框字符个数的统计👇
    $("#addequcate").on('input focus keyup,',function(){
        var str  = $(this).val();
        var remain = str.length;
        $(this).parents(".sale_con1").find("#catishi1").html(remain);
        $(this).attr('maxlength',10);
    });
    $("#addeqthng").on('input focus keyup,',function(){
        var str1 = $(this).val();
        var remain1 = str1.length;
        $(this).parents(".sale_con2").find("#catishi2").html(remain1);
        $(this).attr('maxlength',30);
    })
    $("#newcateorname").on('input focus keyup,',function(){
        var str2 = $(this).val();
        var remain2 = str2.length;
        $(this).parents(".sale_con3").find("#catnewme1").html(remain2);
        $(this).attr('maxlength',10);
    })
    $("#newconusge").on('input focus keyup,',function(){
        var str3 = $(this).val();
        var remain3 = str3.length;
        $(this).parents(".sale_con4").find("#catnewme2").html(remain3);
        $(this).attr('maxlength',30);
    })
    $("#addchidname").on('input focus keyup,',function(){
        var str4 = $(this).val();
        var remain4 = str4.length;
        $(this).parents(".sale_con1").find("#catchid1").html(remain4);
        $(this).attr('maxlength',10);
    })
    $("#addchidthng").on('input focus keyup,',function(){
        var str5 = $(this).val();
        var remain5 = str5.length;
        $(this).parents(".sale_con2").find("#catchid2").html(remain5);
        $(this).attr('maxlength',30);
    })
    $("#newcateorname").on('input focus keyup',function(){
        var str6 = $(this).val();
        var remain6 = str6.length;
        $(this).parents(".sale_con3").find("#catnewme1").html(remain6);
        $(this).attr('maxlength',10);
    })
    $("#newconusge").on('input focus keyup',function(){
        var str7 = $(this).val();
        var remain7 = str7.length;
        $(this).parents(".sale_con4").find("#catnewme2").html(remain7);
        $(this).attr('maxlength',30);
    })
    getcatlook(1);
})

// creator: sy 2023-02-16 展示类别管理首页列表
function getcatlook(enabled,parent){
    var daenbox = [];
    $.ajax({
        url:"../equipment/category/list",
        type:"POST",
        data:{
            //enabled:0-查询已停用的列表  1-查询正常的列表
            //parent：查询当前类别的直属子类别 ，传递当前类别的id
            enabled:enabled,
            parent:0
        },
        success:function(res){
            $("#equipcatmess").html("");
            $("#newonecategory").data("box",res);
            if(res != undefined && res.length >0){
                for(var k = 0;k<res.length;k++){
                    var lanker = res.length;
                    $(".connber").html(lanker);
                    var str  = ``;
                    str +=`
                        <tr id="${res[k].id}">
                            <td>${res[k].name}</td>
                            <td>${res[k].content}</td>
                            <td class="ty-td-control">
                                <span class="ty-color-blue funBtn" onclick="subcategory($(this))">${res[k].childrens}个</span>
                            </td>
                            <td class="ty-td-control">
                                <span class="ty-color-blue funBtn" onclick="upfoundation($(this))" id="upolding">修改基本信息</span>
                                <span class="ty-color-red funBtn" onclick="stopuse($(this))">停用</span>
                                <span class="ty-color-red funBtn" onclick="detaleune($(this),1)" id="remnedetter">删除</span>
                                <span class="hd" id="speialbox">${JSON.stringify(res[k])}</span>
                            </td>
                        </tr> `;
                    $("#equipcatmess").append(str);
                    daenbox.push(res[k]);
                }
                var lunk = $("#speialbox").html();
                $("#depositall").html(lunk);
                lunk = JSON.parse(lunk);
                $("#depositthe").data("allbox",daenbox);
            }else{
                $(".connber").html(0);
            }
        }
    })
}

// creator: sy 2023-03-27 展示针对子类别的类别列表
function getchildbank(id){
    var are = $("#depositthe").data("allbox");
    $.ajax({
        url: "../equipment/category/list",
        type: "POST",
        data:{
            enabled: 1,
            parent:id
        },
        success:function(res){
            $("#dircatbox").html("");
            if (res != undefined && res.length > 0) {
                for (var d = 0; d < res.length; d++) {
                    var lanth = res.length;
                    $(".pont").html(lanth);
                    var str1 = ``;
                    str1 += `
                        <tr id="${res[d].id}">
                            <td>${res[d].name}</td>
                            <td>${res[d].content}</td>
                            <td class="ty-td-control">
                                <span class="ty-color-blue funBtn" onclick="subcategory($(this))">${res[d].childrens}个</span>
                            </td>
                            <td>
                                <span class="ty-color-blue funBtn upnameth" onclick="upbscination($(this))" id="chidbox">修改基本信息</span>
                                <span class="ty-color-red funBtn" onclick="upstopune($(this))">停用</span>
                                <span class="ty-color-red funBtn" onclick="updetalune($(this))">删除</span>
                                <span class="hd" id="thisanswer">${JSON.stringify(res[d])}</span>
                            </td>
                        </tr>`;
                    $("#dircatbox").append(str1);
                    are.push(res[d]);
                    var uounk = $("#thisanswer").html();
                    $("#depositchid").html(uounk);
                    $("#dirback").data("onak", uounk);
                    $("#dirback").data("key",0);
                }
            }else{
                $(".pont").html(0);
            }
            $("#depositthe").data("allbox",are);
        }
    })
}

// creator: sy 2023-02-16  点击"新增一级类别"按钮
function addcatfri(){
    $("#addequcate").val("");
    $("#addeqthng").val("");
    $("#catishi1").html(0);
    $("#catishi2").html(0);
    var bend = $("#newonecategory").data("box");
    $("#addcasur").data("masure",bend);
    bounce.show($("#addcate"));
}

// creator: sy 2023-02-16 点击"更多操作说明"按钮
function moretack(){
    bounce.show($("#moreerainsucons"));
}

// creator: sy 2023-02-17 点击“修改基本信息"按钮
function upbscination(oter){
    $(".parent").hide();
    $(".child").show();
    var lank = oter.next().next().siblings(".hd").html();
    lank = JSON.parse(lank);
    var anid = lank.id; //类别id
    var anname = lank.name; //修改前的名称
    var ancontent = lank.content;//修改前的所属内容/使用的主要场合
    $(".upnameth").data("id",anid);
    $("#upolding").data("chid",anid);//奇怪的逻辑思路
    $(".upnameth").data("name",anname);
    $(".upnameth").data("content",ancontent);
    $("#curetctname").val(anname);
    $("#contentusage").val(ancontent);
    $("#curetctname").prop('disabled','disabled');
    $("#contentusage").prop('disabled','disabled');
    $("#curetctname").css("background","#D9D9D9");
    $("#contentusage").css("background","#D9D9D9");
    $("#newcateorname").val("");
    $("#newconusge").val("");
    bounce.show($("#moiybaicifomtion"));
}

// creator: sy 2023-02-20 点击“修改记录”按钮
function catrecord(){
    var unid = $("#upolding").data("id");   //类别id
    if(unid == undefined){
        unid = $("#upolding").data("chid");
    }
    unid = Number(unid);
    $.ajax({
        url:"../equipment/category/history/list",
        type:"POST",
        data:{
            // category:类型id
            category:unid
        },
        success:function(res){
            $("#mdifrordbox").html("");
            if(res != undefined && res.length > 0){
                var length = res.length;
                for(var t = 0;t<res.length;t++){
                    var versionNo = res[t].versionNo;
                    var strt = ``;
                    if(versionNo == 0){//versionNo = 0时
                        strt += `
                            <tr>
                                <td>数据的创建</td>
                                <td>${handleNull(res[t].createName)}&nbsp;${new Date(handleNull(res[t].createDate)).format("yyyy-MM-dd hh:mm:ss")}</td>
                                <td>${handleNull(res[t].name)}</td>
                                <td>${handleNull(res[t].content)}</td>
                            </tr>`;
                    }else{//versionNo != 0时
                        strt += `
                        <tr>
                            <td>第<span>${res[t].versionNo}</span>次修改后</td>
                            <td>${handleNull(res[t].createName)}&nbsp;${new Date(handleNull(res[t].createDate)).format("yyyy-MM-dd hh:mm:ss")}</td>
                            <td>${handleNull(res[t].name)}</td>
                            <td>${handleNull(res[t].content)}</td>
                        </tr>`;
                    }
                    $("#mdifrordbox").append(strt);
                }
            }
        }
    })
    bounce_Fixed.show($("#modifyrecord"));
}

// creator: sy 2023-02-20 点击“直属的子类别”下面的数字按钮
function subcategory(straget,other){
    var lunk = '';
    // $("#newonecategory").is(":hidden");//是否隐藏
    //$("#newonecategory").is(":visible");//是否可见
    if($('#newonecategory').is(':visible')&&$("#addsamchid").is(":hidden")){
        $(".name").data("id",9);    //代表上一页是一级类别列表
    }else if($('#newonecategory').is(':hidden')&&$("#addsamchid").is(":visible")){
        $(".name").data("id",7);    //代表上一页是子类别类别
    }

    if(other == 1){
        lunk = straget;
    }else{
        lunk = straget.parent("td").next().find(".hd").html();
        lunk = JSON.parse(lunk);
    }
    $("#addsamchid").data("box",lunk);
    var id = lunk.id;   //当前类别id
    var parent = lunk.parent;
    var oten = {
        id:id,
        parent:parent
    }
    $("#dirback").data("keyunid",oten);//类别id
    $("#addsamchid").data("chid",id);
    id = Number(id);
    var name = lunk.name;   //类别名称
    $(".name").html(name);
    var unid = $(".name").data("id");
    var are = $("#depositthe").data("allbox");
    getcatlank(id,are);

    $("#cateagent").hide();
    $("#diretsbaeory").show();
}

// creator: sy 2023-03-28 获取类别列表1
function getcatlank(id,are){
    $.ajax({
        url:"../equipment/category/list",
        type:"POST",
        data:{
            //enabled:0-查询已停用的列表  1-查询正常的列表
            //parent:查询当前类别的直属子类别 ，传递当前类别的id
            enabled:1,
            parent:id
        },
        success:function(res){
            $("#dircatbox").html("");
            if(res != undefined && res.length > 0){
                var num = 0;
                for(var d = 0; d<res.length; d++){
                    var lanth = res.length;
                    $(".pont").html(lanth);
                    var str1 = ``;
                    str1 += `
                        <tr id="${res[d].id}">
                            <td>${res[d].name}</td>
                            <td>${res[d].content}</td>
                            <td class="ty-td-control">
                                <span class="ty-color-blue funBtn" onclick="subcategory($(this))">${res[d].childrens}个</span>
                            </td>
                            <td>
                                <span class="ty-color-blue funBtn upnameth" onclick="upbscination($(this))" id="chidbox">修改基本信息</span>
                                <span class="ty-color-red funBtn" onclick="upstopune($(this))">停用</span>
                                <span class="ty-color-red funBtn" onclick="updetalune($(this))">删除</span>
                                <span class="hd" id="thisanswer">${JSON.stringify(res[d])}</span>
                            </td>
                        </tr>`;
                    $("#dircatbox").append(str1);
                    are.push(res[d]);
                }
            }else{
                $(".pont").html(0);
            }
            var uounk = $("#thisanswer").html();
            if($("#thisanswer").html() == undefined){
                $("#dirback").data("key",1);
                return false;
            }else{
                $("#dirback").data("key",0);
            }
            $("#depositchid").html(uounk);
            uounk = JSON.parse(uounk);
            var chosen = {
                id:uounk.id,
                parent:uounk.parent
            }
            $("#dirback").data("onak",chosen);
            // for (var i = 0; i < are.length; i++) {
            //     for (var j = i + 1; j < are.length;) {
            //         if (are[i].id == are[j].id && are[i].name == are[j].name) { //此处是你要判断的值
            //             are.splice(j, 1);//去除重复的对象；
            //             break;
            //         } else {
            //             j++;
            //         }
            //     }
            // }
            // return are;
            $("#depositthe").data("allbox",are);
        }
    })
}

// creator: sy 2023-03-28 获取类别列表2
function getcaglonk(id,are,keyd,kond){//keyd一直没用到，该怎将它用上呢?
    if(keyd == 2){ //先尝试下keyd == 2的时候回到一级类别首页
        if(kond == 1){
            getmiddebox(id,are);
        }else if(kond == 2){
            getcatlook(1);
            $("#diretsbaeory").hide();
            $("#cateagent").show();
            return  false;
        }
    }else if(keyd == 1){
        getmiddebox(id,are);
    }
}

// creator: sy 2023-04-28
function getmiddebox(id,are){
    $.ajax({
        url:"../equipment/category/list",
        type:"POST",
        data:{
            enabled:1,
            parent:id
        },
        success:function(res){
            $("#dircatbox").html("");
            if(res != undefined && res.length > 0){
                var num = 0;
                for(var d = 0; d<res.length; d++){
                    var lanth = res.length;
                    $(".pont").html(lanth);
                    var str1 = ``;
                    str1 += `
                        <tr id="${res[d].id}">
                            <td>${res[d].name}</td>
                            <td>${res[d].content}</td>
                            <td class="ty-td-control">
                                <span class="ty-color-blue funBtn" onclick="subcategory($(this))">${res[d].childrens}个</span>
                            </td>
                            <td>
                                <span class="ty-color-blue funBtn upnameth" onclick="upbscination($(this))" id="chidbox">修改基本信息</span>
                                <span class="ty-color-red funBtn" onclick="upstopune($(this))">停用</span>
                                <span class="ty-color-red funBtn" onclick="updetalune($(this))">删除</span>
                                <span class="hd" id="thisanswer">${JSON.stringify(res[d])}</span>
                            </td>
                        </tr>`;
                    $("#dircatbox").append(str1);
                    are.push(res[d]);
                    var uounk = $("#thisanswer").html();
                    $("#depositchid").html(uounk);
                    uounk = JSON.parse(uounk);
                    var chosen = {
                        id:uounk.id,        //188
                        parent:uounk.parent     //174
                    }
                    $("#dirback").data("onak",chosen);
                    $("#depositthe").data("allbox",are);
                }
            }else{
                $(".pont").html(0);
            }
        }
    })
    $("#cateagent").hide();
    $("#diretsbaeory").show();
}

// creator: sy 2023-02-20 点击返回回到类别管理首页
function dirseorby(){
    //思路：将all和chid存的数据存在一个数组中
    var unid = $(".name").data("id");
    if(unid == 9){  //从一级子类别1的列表回到首页列表
        getcatlook(1);
        $("#diretsbaeory").hide();
        $("#cateagent").show();
    }else if(unid == 7){
        var boxa = $("#depositthe").data("allbox"); //需要将所有数据都存入这个数组中
        //疑问：当列表中所有数据都被删除后如何让chons还有值
        var chons = $("#dirback").data("onak"); //现在显示的数据
        var keyen = $("#dirback").data("key");  //作为判断
        var bacdk = $("#dirback").data("qundt");//父级为0时
        var bacdq = $("#dirback").data("qundc");//父级不为0时
        var tryun = $("#dirback").data("start") || {};
        var id = "";

        if(chons == undefined){
            var box = $("#dirback").data("keyunid");
            var path1 = box.id;
            var parent1 = box.parent;
        }else{
            var odg = chons.parent;
            if(odg == undefined){
                chons = JSON.parse(chons);
            }
            var path1 = chons.parent;    //列表数据的父级id
        }
        if(bacdq == "s"){
            tryun = {};
        }
        if(tryun.parent == 0){
            getcatlook(1);
            $("#diretsbaeory").hide();
            $("#cateagent").show();
            $("#dirback").data("start",{});
            return false;
        }else{
            var obj1 = $.grep(boxa,function(e){return  e.id == path1});//获取带有想要搜索的id的数据
            for(var a = 0; a < obj1.length; a++){
                var path1 = obj1[a].id;
                var name = obj1[a].name;   //类别名称
                var parent = obj1[a].parent;
            }
            chons = {
                id:path1,   //174
                parent:parent   //0
            }
            $("#dirback").data("start",chons);
            if(keyen == 1){
                id = path1;
                $(".name").html(name);
                if(bacdk == "t"){//父级为0
                    if(unid == 9){
                        getcatlook(1);
                        $("#diretsbaeory").hide();
                        $("#cateagent").show();
                        return  false;
                    }else if(unid == 7){
                        var are = $("#depositthe").data("allbox");
                        if(chons.parent !== 0){
                            getcaglonk(id,are,2,1);
                        }else{
                            getcaglonk(id,are,2,2);
                        }
                    }
                }else if(bacdq == "s"){//父级不为0
                    if(unid == 9){
                        getcatlook(1);
                        $("#diretsbaeory").hide();
                        $("#cateagent").show();
                        return  false;
                    }else if(unid == 7){
                        if(bacdq != "s"){//有一种情况，bacdq=s,但是因为上面的加载parent=0,所以下面再加载的时候就不对了
                            if(parent == 0){
                                getcatlook(1);
                                $("#diretsbaeory").hide();
                                $("#cateagent").show();
                                return  false;
                            }else{//parent != 0
                                id = parent;
                                var objdrt = $.grep(boxa,function(e){return e.id == parent});
                                for(var b =0;b<objdrt.length;b++){
                                    name = objdrt[b].name;
                                    $(".name").html(name);
                                }
                            }
                        }else{//bacdq==s
                            if(parent == 0){
                                getcatlook(1);
                                $("#diretsbaeory").hide();
                                $("#cateagent").show();
                                return  false;
                            }else{//parent !=0
                                id = parent;
                                var objdrt = $.grep(boxa,function(e){return e.id == parent});
                                for(var b =0;b<objdrt.length;b++){
                                    name = objdrt[b].name;
                                    $(".name").html(name);
                                }
                            }
                        }
                        var are = $("#depositthe").data("allbox");
                        if(parent !== 0){
                            getcaglonk(id,are,2,1);
                        }else{
                            getcaglonk(id,are,2,2);
                        }
                    }
                }else{
                    //第一种情况：因为并没有在丙的页面新增数据，所以列表是空的，
                    //                     到这现在是bacdk和bacdq都为undefined,keyen=1,parent = 0
                    if(unid == 9){
                        getcatlook(1);
                        $("#diretsbaeory").hide();
                        $("#cateagent").show();
                        return  false;
                    }else if(unid == 7){//因为是从丙到乙，所以unid=7

                        var are = $("#depositthe").data("allbox");
                        if(chons.parent !== 0){
                            getcaglonk(id,are,2,1);
                        }else{
                            getcaglonk(id,are,2,2);
                        }
                    }
                }
            }else if (keyen == 0){
                var path2 = parent;    //列表数据的父级的父级id
                if(path2 == 0){
                    getcatlook(1);
                    $("#diretsbaeory").hide();
                    $("#cateagent").show();
                    return false;
                }else{
                    var obj2 = $.grep(boxa, function(e){ return e.id == path2; });//获取带有想要搜索的id的数组
                    for(var a = 0; a < obj2.length; a++){
                        var name = obj2[a].name;   //类别名称
                    }
                    id = path2;//类别id
                    $(".name").html(name);
                    if(bacdk == "t"){
                        if(unid == 9){
                            getcatlook(1);
                            $("#diretsbaeory").hide();
                            $("#cateagent").show();
                            return  false;
                        }else if(unid == 7){
                            var are = $("#depositthe").data("allbox");
                            if(path2 !== 0){
                                getcaglonk(id,are,1,1);
                            }else{
                                getcaglonk(id,are,1,2);
                            }
                        }
                    }else if(bacdq == "s"){
                        if(unid == 9){
                            getcatlook(1);
                            $("#diretsbaeory").hide();
                            $("#cateagent").show();
                            return  false;
                        }else if(unid == 7){
                            var are = $("#depositthe").data("allbox");
                            getcaglonk(id,are,1);
                        }
                    }else{
                        if(unid == 9){
                            getcatlook(1);
                            $("#diretsbaeory").hide();
                            $("#cateagent").show();
                            return  false;
                        }else if(unid == 7){
                            var are = $("#depositthe").data("allbox");
                            getcaglonk(id,are,1);
                        }
                    }
                }
            }
        }

        // $("#cateagent").hide();
        // $("#diretsbaeory").show();
    }
}

// creator: sy 2023-02-21  点击“新增同级子类别”按钮
function addchidcat(){
    var udname = $(".name").html();
    $("#simbcat").html(udname);
    $("#addchidname").val("");
    $("#addchidthng").val("");
    $("#catchid1").html(0);
    $("#catchid2").html(0);
    bounce.show($("#addsimchid"));
}

// creator: sy 2023-02-27 点击“确定”进行类别新增
function addcamad(){
    var name = $("#addequcate").val();
    var place = $("#addeqthng").val();
    var link = $("#addcasur").data("masure");
    let e = 0;
    for(var b = 0;b<link.length;b++){
        var unname = link[b].name;
        var uncontent = link[b].content;
        if(name == unname){
            e = 1;
            layer.msg("操作失败，因为系统里已有这个名称和内容了");
            return false;
        }else if(name.length == 0 || place.length == 0 || name.length < 0 || place.length < 0){
            e = 1;
            layer.msg("操作失败，因为系统里已有这个名称和内容了");
            return false;
        }else{
            e = 0;
        }
    }
    if(e == 0){
        $.ajax({
            url:"../equipment/category/add",
            type:"POST",
            data:{
                //name:类别名称
                //content:所属内容/使用的主要场合
                //parent:父节点id  若为一级节点传0  若为子级节点则传递父节点id
                name:name,
                content:place,
                parent:0
            },
            success:function(res){
                bounce.cancel($("#addcate"));
                getcatlook(1);
            }
        })
    }
}

// creator: sy 2023-02-27 进行列表信息的修改
function upfoundation(one){
    $(".parent").show();
    $(".child").hide();
    $("#catnewme1").html(0);
    $("#catnewme2").html(0);
    var lunk = one.next().next().siblings(".hd").html();
    lunk = JSON.parse(lunk);
    var unid = lunk.id; //类别id
    var unname = lunk.name; //修改前的名称
    var uncontent = lunk.content; //修改前的所属内容/使用的主要场合
    $("#upolding").data("id",unid);
    $("#upolding").data("name",unname);
    $("#upolding").data("content",uncontent);
    $("#curetctname").val(unname);
    $("#contentusage").val(uncontent);
    //将输入框设置成灰色背景，不可点击，鼠标移至时变成禁止
    $("#curetctname").attr("onfocus", "this.blur()");
    $("#curetctname").css("background", "#CCCCCC");
    $("#curetctname").css("cursor", "not-allowed");
    $("#contentusage").attr("onfocus", "this.blur()");
    $("#contentusage").css("background", "#CCCCCC");
    $("#contentusage").css("cursor", "not-allowed");
    $("#newcateorname").val("");
    $("#newconusge").val("");
    bounce.show($("#moiybaicifomtion"));
}

// creator: sy 2023-02-28 点击“确定”进行类别修改
function upmakesure(two){
    var upname = $("#newcateorname").val(); //修改后的名称
    var upthink = $("#newconusge").val();   //修改后的内容
    var upid = $("#upolding").data("id");   //类别id
    var bename = $("#upolding").data("name");   //修改前的名称
    var becontent = $("#upolding").data("content"); //修改前的内容
    var json = {    //对需要传的值进行统计存储
        name:"",
        content:""
    };
    if(upname == ""){
        json.name = bename;
    }else{
        json.name = upname;
    }
    if(upthink == ""){
        json.content = becontent;
    }else{
        json.content = upthink;
    }
    $.ajax({
        url:"../equipment/category/edit",
        type:"POST",
        data:{
            //id:类别id
            //name:修改后的名称
            //content:修改后的所属内容/使用的主要场合
            //enabled:0-停用  1-启用
            id:upid,
            name:json.name,
            content:json.content
        },
        success:function(res){
            getcatlook(1);
            bounce.cancel($("#moiybaicifomtion"));
        }
    })
}

// creator: sy 2023-03-02 点击“新增同级子类别”弹窗中的确定按钮
function addchdsure() {
    var unid = $("#addsamchid").data("chid");
    var name = $("#addchidname").val(); //新的子类别名称
    var thing = $("#addchidthng").val();    //新的所含内容/使用的主要场合
    $.ajax({
        url: "../equipment/category/add",
        type: "POST",
        data: {
            /*name:类别名称
            content:所属内容/使用的主要场合
            parent:父节点id  若为一级节点传0  若为子级节点则传递父节点id
            */
            name: name,
            content: thing,
            parent: unid
        },
        success: function (res) {
            bounce.cancel($("#addsimchid"));
            //var lunk = $("#addsamchid").data("box");
            getchildbank(unid);

            var fater = {
                id:"",
                parent:""
            };
            var box1 = $("#depositthe").data("allbox");//获取存有数据的整个盒子
            var ond1 = box1.find(function(ond1){//在盒子中找到需要新增数据的页面父级的数据
                return ond1.id == unid;
            })
            fater.id = ond1.id;//父级id
            fater.parent = ond1.parent;//父级是否含有父级
            if(fater.parent == 0){
                $("#dirback").data("qundt","t");
                $(".name").data("id",9);
            }else if(fater.parent != 0){
                $("#dirback").data("qundc","s");
                $(".name").data("id",7);
            }
        }
    })
}

// creator: sy 2023-03-27 点击“关闭”按钮进行修改记录弹窗关闭
function closeon(){
    bounce_Fixed.cancel($('#modifyrecord'));
}

// creator: sy 2023-03-03 点击类别列表中的“删除”按钮
function detaleune(deten,type){
    var lunk = deten.siblings(".hd").html()
    lunk = JSON.parse(lunk);
    var id = lunk.id;
    $("#detsure").data("unk",lunk);
    $("#detsure").data("unid",id);
    //注：1.前四行数据不能进行停用和删除，2.当该装备下有装备器具时给予提示“操作失败，因为该类别下尚有装备器具！”
    //       3.当该装备“直属的子类别”一列有数据时给予提示，有子类别的显示"操作失败，该类别不可删除！"提示
    //       4.有修改不能删除，显示"操作失败，该类别不可删除！"提示
    //   给予提示的判断顺序：1=>2=>3=>4=>接口返回值判断

    if(lunk.isSystem == 1){
        layer.msg("操作失败，该类别不可删除！");
        return false;
    }else if(lunk.equipmentCount != null && lunk.equipmentCount > 0){
        layer.msg("操作失败，因为该类别下尚有装备器具！");
        return false;
    }
    $("#detsure").show();
    $("#updetun").hide();
    $("#detsure").data("key",type);
    bounce.show($("#detunemd"));
}

// creator: sy 2023-03-03 点击删除提示弹窗中的确定按钮
function detenalno(){
    var lank = $("#detsure").data("unk");
    if(lank.childrens != 0){
        layer.msg("操作失败，该类别不可删除!");
        return false;
    }else if(lank.revisable == 0){
        layer.msg("操作失败，该类别不可删除!");
        return false;
    }
    var deid = $("#detsure").data("unid");
    deid = Number(deid);
    $.ajax({
        url:"../equipment/category/remove",
        type:"POST",
        data:{
            /*ids:类别id 多个用逗号分割 */
            ids:deid
        },
        success:function(res){
            if(res.code == 500){
                layer.msg("操作失败，该类别不可删除！");
            }else if(res.code == 0){
                var key = $("#detsure").data("key");
                if(key == "1"){
                    getcatlook(1);
                }else if(key == "2"){
                    stopcatfi();
                }
            }
        }
    })
    bounce.cancel($("#detunemd"));
}

// creator: sy 2023-03-06 点击子类别列表中”修改类别的基本信息“弹窗中的确定按钮
function upchidmdsre(){
    var upname = $("#newcateorname").val();//修改后的名称
    var upthink = $("#newconusge").val();//修改后的内容
    var upid = $(".upnameth").data("id");   //类别id
    var bename = $(".upnameth").data("name");//修改前的名称
    var becontent = $(".upnameth").data("content");//修改前的内容
    var alte = {    //对需要传的值进行统计存储
        name:"",
        content:""
    };
    if(upname == ""){
        alte.name = bename;
    }else{
        alte.name = upname;
    }
    if(upthink == ""){
        alte.content = becontent;
    }else{
        alte.content = upthink;
    }
    $.ajax({
        url:"../equipment/category/edit",
        type:"POST",
        data:{
            //id:类别id
            //name:修改后的名称
            //content:修改后的所属内容/使用的主要场合
            //enabled:0-停用  1-启用
            id:upid,
            name:alte.name,
            content:alte.content
        },
        success:function(res){
            bounce.cancel($("#moiybaicifomtion"));
            var lunk = $("#addsamchid").data("box");
            subcategory(lunk,1);
        }
    })
}

// creator: sy 2023-03-06 点击”停用“按钮对一级类别数据数据进行停用
function stopuse(stonp){
    var link = stonp.next().siblings(".hd").html();
    link = JSON.parse(link);
    var unid = link.id;
    $("#stopnen").data("unk",link);
    $("#stopnen").data("id",unid);
    //注：1.前四行数据不能进行停用和删除，2.当该装备下有装备器具时给予提示“操作失败，因为该类别下尚有装备器具！”
    //       3.当该装备“直属的子类别”一列有数据时给予提示，有子类别的显示"操作失败，该类别不可停用！"提示
    //   给予提示的判断顺序：1=>2=>3=>接口返回值判断

    if(link.isSystem == 1){
        layer.msg("操作失败，该类别不可停用！");
        return false;
    }else if(link.equipmentCount != null && link.equipmentCount > 0){
        layer.msg("操作失败，因为该类别下尚有装备器具！");
        return false;
    }
    $("#stopnen").show();
    $("#stopchid").hide();
    bounce.show($("#stpunten"));
}

// creator: sy 2023-05-09 被停用的类型列表
function stopcatfi(){
    $("#cateagent").hide();
    $("#stopcatoney").show();
    $.ajax({
        url:"../equipment/category/list",
        data:{
            enabled:0
        },
        success:function(res){
            $("#stocatbox").html("");
            //当res是一个含有多个json数据的数组时，就可以使用下面的遍历方式进行在页面渲染数据
            res.forEach(item => {
                var str1 = `
                    <tr>
                        <td>${handleNull(item.name)}</td>
                        <td>${handleNull(item.content)}</td>
                        <td>${handleNull(item.updateName)}&nbsp;${new Date(handleNull(item.enabledTime)).format("yyyy-MM-dd hh:mm:ss")}</td>
                        <td>
                             <span class="ty-color-blue funBtn" onclick="bcktun($(this))">恢复使用</span>
                             <span class="ty-color-red funBtn" onclick="detaleune($(this),2)">删除</span>
                               <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                    </tr>`;
                $("#stocatbox").append(str1);
            })
        }
    })
}

// creator: sy 2023-05-09 点击停用列表中的”恢复使用“按钮
function bcktun(bck){
    var lank = bck.next().siblings(".hd").html();
    lank = JSON.parse(lank);
    var unid = lank.id;
    $("#tebsure").data("id",unid);
    bounce.show($("#tenbck"));
}

// creaotr: sy 2023-05-09 点击恢复弹窗中的确定按钮
function tebmadure(){
    var stid = $("#tebsure").data("id");
    stid = Number(stid);
    $.ajax({
        url:"../equipment/category/edit",
        data:{
            id:stid,
            enabled:1
        },
        success:function(res){
            if(res.code == 0){
                stopcatfi();
            }
        }
    })
    bounce.cancel($("#tenbck"));
}

// creator: sy 2023-05-09 返回首页
function stopcoey(){
    $("#cateagent").show();
    $("#stopcatoney").hide();
    getcatlook(1);
}

// creator: sy 2023-03-06 点击停用提示弹窗中的确定按钮
function stoaneld(){
    var lank = $("#stopnen").data("unk");
    if(lank.childrens != 0){
        layer.msg("操作失败，该类别不可停用!");
        return false;
    }
    var stid = $("#stopnen").data("id");
    stid = Number(stid);
    $.ajax({
        url:"../equipment/category/edit",
        type:"POST",
        data: {
            id:stid,
            enabled:0   //0-停用，1-启用
        },
        success:function(res){
            if(res.code == 500){
                layer.msg("操作失败，该类别不可停用！");
            }else if(res.code == 0){
                getcatlook(1);
            }
        }
    })
    bounce.cancel($("#stpunten"));
}

// creator: sy 2023-03-06 点击子类别列表中”停用“按钮
function upstopune(stopen){
    var link = stopen.next().siblings(".hd").html();
    link = JSON.parse(link);
    var unid = link.id;
    $("#stopchid").data("unk",link);
    $("#stopchid").data("id",unid);
    //注：1.前四行数据不能进行停用和删除，2.当该装备下有装备器具时给予提示“操作失败，因为该类别下尚有装备器具！”
    //       3.当该装备“直属的子类别”一列有数据时给予提示，有子类别的显示"操作失败，该类别不可停用！"提示
    //   给予提示的判断顺序：1=>2=>3=>接口返回值判断

    if(link.isSystem == 1) {
        layer.msg("操作失败，该类别不可停用！");
        return false;
    }else if(link.equipmentCount != null && link.equipmentCount > 0){
        layer.msg("操作失败，因为该类别下尚有装备器具！");
        return false;
    }
    $("#stopnen").hide();   //一级类别时点击的按钮
    $("#stopchid").show();  //子类别时点击的按钮
    bounce.show($("#stpunten"));
}

// creator: sy 2023-03-06 点击子类别列表中停用提示弹窗中的确定按钮
function stochidld(){
    var lank = $("#stopchid").data("unk");
    if(lank.childrens != 0){
        layer.msg("操作失败，该类别不可停用！");
        return false;
    }
    var stid = $("#stopchid").data("id");
    stid = Number(stid);
    $.ajax({
        url:"../equipment/category/edit",
        type:"POST",
        data:{
            /*id:类别id 多个用逗号分割 */
            id:stid,
            enabled:0
        },
        success:function(res){
            if(res.code == 500){
                layer.msg("操作失败，该类别不可停用！");
            }else if(res.code == 0){
                var lunk = $("#addsamchid").data("box");
                subcategory(lunk,1);
                var updep = {
                    id:"",
                    parent:""
                };
                var box3 = $("#depositthe").data("allbox");
                var ond3 = box3.find(function(ond3){
                    return ond3.id = stid;
                })
                updep.id = ond3.id;
                updep.parent = ond3.parent;
                if(updep.parent == 0){
                    $("#dirback").data("qundt","t");
                    $(".name").data("id",9);
                }else if(updep.parent != 0){
                    $("#dirback").data("qundc","s");
                    $(".name").data("id",7);
                }
            }
        }
    })
    bounce.cancel($("#stpunten"));
}

// creator: sy 2023-03-07 点击子类别类别中的删除按钮
function updetalune(deupt){
    var lunk = deupt.siblings(".hd").html();
    lunk = JSON.parse(lunk);
    var upid = lunk.id;
    $("#updetun").data("unk",lunk);
    $("#updetun").data("unid",upid);
//注：1.前四行数据不能进行停用和删除，2.当该装备下有装备器具时给予提示“操作失败，因为该类别下尚有装备器具！”
    //       3.当该装备“直属的子类别”一列有数据时给予提示，有子类别的显示"操作失败，该类别不可删除！"提示
    //       4.有修改不能删除，显示"操作失败，该类别不可删除！"提示
    //   给予提示的判断顺序：1=>2=>3=>4=>接口返回值判断

    if(lunk.isSystem == 1){
        layer.msg("操作失败，该类别不可删除！");
        return false;
    }else if(lunk.equipmentCount != null && lunk.equipmentCount > 0){
        layer.msg("操作失败，因为该类别下尚有装备器具！");
        return false;
    }
    $("#detsure").hide();
    $("#updetun").show();
    bounce.show($("#detunemd"));
}

// creator: sy 2023-03-07 点击删除提示弹窗中的确定按钮
function updetalno(){
    var lank = $("#updetun").data("unk");
    if(lank.childrens != 0){
        layer.msg("操作失败，该类别不可删除!");
        return false;
    }else if(lank.revisable == 0){
        layer.msg("操作失败，该类别不可删除");
        return false;
    }
    var deid = $("#updetun").data("unid");
    deid = Number(deid);
    $(".pont").data("keyid",deid);//被删除的子类别的id
    $.ajax({
        url:"../equipment/category/remove",
        type:"POST",
        data:{
            /*ids:类别id 多个用逗号分割 */
            ids:deid
        },
        success:function(res){
            if(res.code == 500){
                layer.msg("操作失败，该类别不可删除！");
            }else if(res.code == 0){
                var lunk = $("#addsamchid").data("box");
                subcategory(lunk,1);
                var chold = {
                    id:"",
                    parent:""
                };
                var box2 = $("#depositthe").data("allbox");
                var ond2 = $.grep(box2, function(e){ return e.id == deid; });//获取带有想要搜索的id的数组
                for(var a = 0; a < ond2.length; a++){
                    chold.id = ond2[a].id;
                    chold.parent = ond2[a].parent;
                }
                if(chold.parent == 0){
                    $("#dirback").data("qundt","t");
                    $(".name").data("id",9);
                }else if(chold.parent != 0){//相当于是在丙页进行删除
                    $("#dirback").data("qundc","s");
                    $(".name").data("id",7);
                }
            }
        }
    })
    bounce.cancel($("#detunemd"));
}