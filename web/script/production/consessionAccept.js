/**
 * Created by Administrator on 2017/8/14 0014.
 */
// 技术管理--让步受理
//切换显示页面
$(function () {
    $(".ty-secondTab li").on("click",function () {
        var index = $(this).index();
        $(this).siblings("li").removeClass("ty-active");
        $(this).addClass("ty-active");
        $(".tblContainer").hide().eq(index).show();
        if(index === 2){
            index ++
        }
        getConcessionAcceptList(1,15,parseInt(index+1));

    });
    $(".ty-radio").on("click",function () {
        $(this).siblings().removeClass("ty-radioActive");
        $(this).addClass("ty-radioActive");
        $(this).siblings().children("i").attr("class","fa fa-circle-o")
        $(this).children("i").attr("class","fa fa-dot-circle-o")
        $(this).siblings(".hd").val($(this).attr("value"));
        if($(this).attr("value") === "1"){
            $(this).siblings("#productNum").show()
            $(this).siblings("#productNum").children("input").focus();
        }else{
            $(this).siblings("#productNum").hide()
        }
    });
    $(".ty-secondTab li").eq(0).click();
})

/* creator：张旭博，2017-08-22 16:03:05，获取入库申请列表 */
function getConcessionAcceptList(currPage,pageSize,state) {
    $.ajax({
        url:"../concession/concessionList.do" ,
        data:{
            "pageSize":pageSize,
            "currPage":currPage,
            "flag":2,
            "state":state
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            loading.close() ;

            //设置分页
            var totalPage = data["totalPage"];//总页数
            var currPage = data["currPage"];//当前页
            $("#ye_concessionAccept").html("");
            var jsonStr = JSON.stringify({"state":state}) ;
            setPage( $("#ye_concessionAccept") , currPage ,  totalPage , "concessionAccept" ,jsonStr );

            var index = $(".ty-secondTab li").index($(".ty-secondTab .ty-active"));
            var mtStockList = data["mtStockList"];
            var concessionApplyListStr = "";
            for(var j = 0 ;j < mtStockList.length ; j++){
                switch(index){
                    case 0://有申请操作；没有申请人、申请时间、审批流程
                        var createStr   =   '<td>'+ ( new Date(mtStockList[j].createDate).format('yyyy-MM-dd'))  +'</td>';
                        var handelStr   =   '<td><span class="ty-color-blue" onclick="concessionAcceptBtn($(this))">操作</span></td>';
                        break;
                    case 1: //有申请人和申请时间、没有操作
                        var createStr   =   '<td>'+ (new Date(mtStockList[j].createDate).format('yyyy-MM-dd') ) +'</td>';
                        var handelStr   =   '';
                        break;
                    case 2: //有申请人和申请时间、没有操作
                        var createStr   =   '';   //申请时间;
                        var handelStr   =   '';
                }

                //每一条审批流程组成的字符串
                var approcessList = mtStockList[j].approcessList;
                var processStr = "";
                var lastReason = '' // 显示的驳回理由
                for (var k = 0; k < approcessList.length; k++) {
                    var approveStatus = approcessList[k].approveStatus;
                    lastReason = approcessList[k].reason;
                    if(Number(approveStatus) === 4){
                        processStr +=       '<div class="infoList ty-process-item ">' +
                            '<p><i class="dot-no"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                            '<p>' + ( new Date(approcessList[k].createDate ).format('yyyy-MM-dd') ) + '</p>' +
                            '</div>';
                    }else{
                        processStr +=       '<div class="infoList ty-process-item ">' +
                            '<p><i class="dot"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                            '<p>' + ( new Date(approcessList[k].createDate ).format('yyyy-MM-dd') ) + '</p>' +
                            '</div>';
                    }
                }
                concessionApplyListStr +=   '<tr id="'+mtStockList[j].id+'" mid="'+mtStockList[j].applyId+'">' +
                                            '<td>'+(j+1)+'</td>'+                           //序号
                                            createStr+                                  //申请信息（申请人和申请时间）
                                            '<td>'+mtStockList[j].outerName+'</td>'+    //商品代号
                                            '<td>'+mtStockList[j].outerSn+'</td>'+    //商品名称
                                            '<td>'+mtStockList[j].innerSn+'</td>'+    //产品图号
                                            '<td>'+mtStockList[j].innerSnName+'</td>'+    //产品名称
                                            '<td>'+mtStockList[j].unit+'</td>'+    //单位
                                            '<td>'+mtStockList[j].inPlan+'</td>'+    //申请入库数量
                                            '<td>'+ (new Date(mtStockList[j].manufactureDate).format('yyyy-MM-dd')) +'</td>'+    //生产日期
                                            '<td>'+ (new Date(mtStockList[j]["invalidDate"]).format('yyyy-MM-dd'))  +'</td>'+    //产品到期日
                                            '<td class="hoverDetail"><span>查看入库流程</span> '+
                                                '<div style="position: relative;">' +
                                                    '<div class="hoverDetailCon">' +
                                                        '<div class="ty-panel ty-process" >'+
                                                            '<div class="infoHead ty-process-ttl"><b class="ty-btn ty-btn-green ty-circle-3">入库流程</b></div>'+
                                                            '<div class="conInfo ty-process-container" id="process">'+
                                                            processStr+
                                                            '</div>'+
                                                        '</div>'+
                                                    '</div>' +
                                                '</div>' +
                                            '</td>'+
                                            `${ index === 0 ?  '<td>'+ lastReason +'</td>' : '' }` +
                                            handelStr+  //操作
                                            '</tr>';
            }

            $(".tblContainer").eq(index).find("tbody").html(concessionApplyListStr);

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-08-24 21:00:53，操作让步受理-按钮 */
function concessionAcceptBtn(selector) {
    // if(hasAuthority(0)){
    //     layer.msg("您没有此权限！")
    //     return false;
    // }
    bounce.show($("#approveConcession"));
    selector.parent().parent().siblings().removeClass("concessionAcceptItemActive");
    selector.parent().parent().addClass("concessionAcceptItemActive");
}

/* creator：张旭博，2017-08-24 21:01:24，申请让步-确认 */
function concessionCheckout(qualified) {
    var mtStockId = $(".concessionAcceptItemActive").attr("id");
    var applyId = $(".concessionAcceptItemActive").attr("mid");
    $.ajax({
        url:"../concession/concessioncheckout.do" ,
        data:{
            "mtStockId":mtStockId,   //出库单id
            "applyId":applyId,   //出库单id
            "qualified":qualified   //是否通过 1-通过 2-不通过
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            bounce.cancel();
            var status = data["status"];
            if(status === undefined){
                $("#tip #tipMs").html("返回值未定义!") ;
                bounce.show($("#tip")) ;
            }else if(status === 1){
                layer.msg("让步评审成功!") ;
            }else if(status === 0){
                $("#tip #tipMs").html("让步评审失败!") ;
                bounce.show($("#tip")) ;
            }else{
                $("#tip #tipMs").html("返回值不存在!") ;
                bounce.show($("#tip")) ;
            }
            $(".ty-secondTab li.ty-active").click();
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;  }
    }) ;
}
// function storageAcceptHandleBtn(selector) {
//     bounce.show($("#storageAcceptHandle"));
//     selector.parent().parent().siblings().removeClass("storageAcceptItemActive");
//     selector.parent().parent().addClass("storageAcceptItemActive");
// }
//
// function sureStorageAcceptHandle() {
//     var judgmentQuantity    = $("#storageAcceptHandle .judgmentQuantity").val();
//     var productNum          = $("#storageAcceptHandle .productNum").val();
//     var ID                  = $(".storageAcceptItemActive").attr("id");
//
//     var data = {
//         "flag" : judgmentQuantity,  //Integer flag数量无误传0,需修改数量传1；
//         "ID"   : ID                 //Integer ID出入库表ID
//     };
//
//     if( Number(judgmentQuantity) === 1 ){
//         data["InFact"] = productNum;//Integer InFact修改数量
//     }
//
//     $.ajax({
//         url:"../mtStock/updateMtStockCount.do" ,
//         data:data ,
//         type:"post" ,
//         dataType:"json" ,
//         beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
//         success:function( data ){
//             loading.close() ;
//             var status = data["status"];
//             if(status === 1){
//                 $("#tip #tipMs").html("入库成功!") ;
//                 bounce.show($("#tip")) ;
//             }else{
//                 $("#tip #tipMs").html("入库失败!") ;
//                 bounce.show($("#tip")) ;
//             }
//
//         },
//         error:function(){
//             $("#tip #tipMs").html("系统错误，请重试!") ;
//             bounce.show($("#tip")) ;
//         } ,
//         complete:function(){ chargeClose(1) ;  }
//     }) ;
//
// }