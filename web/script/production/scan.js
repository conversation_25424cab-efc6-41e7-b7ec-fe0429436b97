/**
 * Created by Administrator on 2017/5/23.
 */
/**
 * Created by Administrator on 2017/5/23.
 */
$(function () {
    getList(1 , 10) ; // 获取处理确定的列表
});

/* creator：张旭博，2017-05-23 10:37:51，要货计划查看 */
// updator : 侯杏哲 2017-07-13 实现要货计划查看 
function seePRBtn( ordID ){
    if(ordID){
        bounce.show($("#seePR")); 
        $.ajax({
            url:"../sr/toProductionManage.do" ,
            data:{"orderId": ordID , "type" : 2  } ,
            type:"post" ,
            dataType:"json" ,
            beforeSend : function(){ loading.open() ; } ,
            success:function(res){
                var order = res["order"] ;
                var items = res["items"] ;  //  items就是订单明细，第一层map是评审记录，第二层data是有修改历史的记录
                if( items && order ){
                    bounce.show($("#seePR"));
                    order["signDate"] = formatTime(order["signDate"] ) ;
                    $("#cusName").html( order["customerName"] ) ;
                    $("#ordSignDate").html( order["signDate"] ) ;
                    $("#HasInvoice").html( chargeHasInvoice(order["hasInvoice"]) ) ;
                    $("#cusCode").html( order["cusCode"] ) ;
                    $("#sn").html( order["sn"] ) ;
                    $("#creator").html( order["createName"] ) ;
                    $("#creteDate").html( formatTime(order["createDate"]) ) ;
                    var str = "" , sum = order.contractAmount ;
                    for(var i = 0 ; i < items.length ; i++ ){
                        var num = i + 1 ;
                        items[i]["num"] = num ;
                        var data_1 = items[i]["map"] && items[i]["map"]["data"] ;
                        if( data_1 && data_1.length > 0  ){
                            var dataObj = data_1[data_1.length -1] ;
                            if( dataObj ){
                                items[i]["amount"] = dataObj["amount"] ;
                                items[i]["deliveryDate"] = dataObj["delivery_date"]  ;
                            } 
                        }
                        items[i]["map"] = "" ;
                        str += "<tr>" +
                            "<td>"+ items[i]["num"] +"</td>" +
                            "<td>"+ items[i]["slOuterSn"] +"</td>" +
                            "<td>"+ items[i]["slOutterSnName"] +"</td>" +
                            "<td>"+ items[i]["slInnerSn"] +"</td>" +
                            "<td>"+ items[i]["slInnerSnName"] +"</td>" +
                            "<td>"+ items[i]["slUnit"] +"</td>" +
                            "<td>"+ items[i]["amount"] +"</td>" +
                            "<td>"+ new Date(items[i]["deliveryDate"]).format('yyyy-MM-dd') +"</td>" +
                            "</tr>" ;

                    }
                    $("#scanDetail").html( str ) ;
                    $("#sum").html( sum.toFixed(4) ) ;
                }else{
                    $("#msTips .msTip").html("获取订单信息失败，请刷新重试！");
                    bounce.show($("#msTips"));
                }
            } , 
            error:function(){
                bounce.show($("#tip")) ; $("#tipMs").html( "链接失败，请刷新重试！" ) ;
            } , 
            complete:function(){ loading.close() ;   }
        }) ; 
    }else{
        bounce.show($("#tip")) ; $("#tipMs").html( "获取订单ID失败，请刷新重试！" ) ;
    }
}
// Creator :侯杏哲 2017-07-13  获取要货计划查看的 列表
function getList(cur , per){
    $.ajax({
        url:"../sr/plans.do" ,
        data:{ "curPage":cur,  "per":per , "type":3 } ,
        type:"post" ,
        dataType: "json" , 
        // beforeSend:function(){ loading.open();  } ,
        success:function( data ){
            var code = data["code"];
            if(code != 200){ bounce.show($("#tip")) ; $("#tipMs").html( "获取列表失败！" ) ;   }
            var totalPage = data["totalPage"];
            var cur = data["curPage"];
            var list = data["data"]; 
            $("#yeScan").html("") ;
            var jsonStr = JSON.stringify({ }) ;
            setPage( $("#yeScan") , cur ,  totalPage , "productionScan" ,jsonStr );
            var str = "" ;   
            if(list && list.length > 0){ 
                for(var i = 0 ; i < list.length ; i++){  
                    str += "<tr>" +  
                        "<td>"+ formatTime( list[i]["sign_date"] ) +"</td>" +
                        "<td>"+ (list[i]["sn"] || "") +"</td>" +
                        "<td>"+ list[i]["customer_name"] +"</td>" +
                        "<td>" + formatTime(list[i]["create_date"]) + "</td>" +
                        "<td>" +
                            "<span class='ty-color-blue' onclick='seePRBtn(" + list[i]["id"] + ")'>查看</span>" +
                        "</td>" +
                        "</tr>" ;  
                }
            } 
            $("#scanBdy").html( str ) ; 
            
        } , 
        error:function(){
            bounce.show($("#tip")) ; $("#tipMs").html( "链接失败，请刷新重试！" ) ;
        },
        complete:function(){  loading.close();   }
    })
}

/* creator：张旭博，2017-06-01 08:54:20，查看订单 数字转换字符 */
function chargeHasInvoice(num) {
    if(num == 1){
        return "是"
    }else{
        return "否"
    }
}