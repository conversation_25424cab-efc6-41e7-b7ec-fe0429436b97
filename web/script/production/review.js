/**
 * Created by 侯杏哲 on 2017/5/23.
 * 权限说明：超管：查看  ； 销售：查看，操作 ； 其他被赋予权限的人：查看，操作
 */
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#linkOrd"));
bounce_Fixed2.cancel();
$(function () {
    showMainCon(1);
    $(".reviewList0").show();
    //二级标签栏切换事件
    $(".ty-secondTab li").on("click",function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        let inx = $(this).index();
        $(".reviewList" + inx).show().siblings("table").hide();
    });
    getList( 1 , 10 );
});

/* Creator:获取需要评审的列表 */
function getList( cur , per ){
    $.ajax({
        url:"/orders/review/orderList" ,
        data:{"produce": 1 } ,
        success:function(res){
            var list = res || [] ;
            var str = "" ,html1 = `` ,html2 = ``;
            for(var i = 0 ; i < list.length ; i++ ){
                // phase 阶段0-未开始,1-生产评审,2-销售评审,3-完成
                str = "<tr>" +
                    "<td>"+ formatTime( list[i]["sign_date"] ) +"</td>" +
                    "<td>"+ list[i]["customer_name"] +"</td>" +
                    "<td>"+ (list[i]["sn"] || "") +"</td>" +
                    "<td>"+ (list[i]["reviewer_name"] || "") +"</td>" +
                    "<td>"+ formatTime( list[i]["create_date"] ) +"</td>" ;
                if (list[i].phase === 0 || list[i].phase === 2) {
                    var oper =  "<td class='ty-td-control'>" +
                        "<span class='ty-color-blue' onclick='chargeBtn("+ list[i]["id"] +" , $(this), " + list[i].phase +" )'>去评审</span>" +
                        "<span class='ty-color-gray'>评审记录</span>" +
                        "</td></tr>"  ;
                    html1 += str + oper;
                } else if (list[i].phase === 1) {
                    var oper =  "<td class='ty-td-control'>" +
                        "<span class='ty-color-blue' onclick='chargeBtn("+ list[i]["id"] +" , $(this), " + list[i].phase+" )'>修改评审结果</span>" +
                        "<span class='ty-color-gray'>评审记录</span>" +
                        //"<span class='ty-color-blue' onclick='chargeHistory("+ list[i]["id"] +" , $(this) )'>评审记录</span>" +
                        "</td></tr>"  ;
                    html2 += str + oper;
                }
            }
            $(".reviewList0 tbody").html( html1 ) ;
            $(".reviewList1 tbody").html( html2 ) ;
            //setPage( $("#ye_setList") , curPage ,  totalPage , "productReview"  );
        }
    });
}
/* Creator: 侯杏哲 2017-06-02 评审按钮,获取订单详情 */
var chargeObj = {} ;
function chargeBtn( ordID , _this , phase ){
    if(hasAuthority(0) === true){
        $("#tipMs").html("您没有此权限！");
        bounce.show($("#tip")); return false ;
    }
    let tip = ``, ableSix = false;
    chargeObj["spanObj"] = _this ;
    chargeObj["ordID"] = ordID ;
    $("#phaseNum").html(phase);
    $(".goReviewList0 tbody").html( "" ) ;
    $(".goReviewList1 tbody").html( "" ) ;
    $.ajax({
        type: "get",
        url: "/orders/review/reviewDetails?orderId="+ordID ,
        success: function(res){
            showMainCon(2);
            var items = res["items"]["data"] ;
            var order = res["orderBase"][0] || {};
            let html = ``, reviewLen =0;
            chargeObj["version_no"] = order.version_no || 0;
            if( !items ){
                bounce.show( $("#tip") ) ; $("#tipMs").html("访问失败") ;  return false ;
            }
            if( items && items.length > 0 ){
                for(var i = 0 ; i < items.length ; i++ ){  // 循环每个商品组
                    let lastHtml = ``,no = i+1 ;
                    let btn = ``, bgStr = ``;
                    reviewLen++;
                    no % 2 === 0 ? bgStr = 'grayBg': "";
                    if (phase === 0) {
                        html =
                            `<tr class='reviewTr' data-line="${no}">
                            <td title="${items[i].outer_sn}">${items[i].outer_sn}</td>
                            <td title="${items[i].outer_name}">${items[i].outer_name}</td>
                            <td title="${items[i].unit}">${items[i].unit}</td>
                            <td title="${items[i].amount}">${items[i].amount}</td>
                            <td title="${new Date(items[i].delivery_date).format("yyyy-MM-dd")}">${new Date(items[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td title="${items[i].delivery_address}">${items[i].delivery_address}</td>
                            <td><input type='text' onblur="testQuantity($(this))" value="${phase === 0 ? "": items[i].reviews[0].scheduledAmount}"  placeholder="请录入数量"/></td>
                            <td>
                                <input type='text' readonly id='goods${i}' disabled="disabled" placeholder="请选择日期" />
                            </td>
                            <td><span class='ty-color-blue' onclick='deliveryDateCon( $(this) , 0)'>详情</span><span class='hd'>${JSON.stringify(items[i])}</span>
                            </td>
                            </tr>`;
                    } else {
                        let type = Number(items[i].progress);//情况2数量日期或地点有一项或多项变动的、情况3新增的、情况4分批发货的及情况5数量被改为零的
                        if(type === 6) ableSix = true;
                        //显示上一次评审
                        let reviewsHistory = {}, icon = -1;
                        if (items[i].reviews.length > 0) {
                            icon = items[i].reviews.length -1;
                            reviewsHistory = items[i].reviews[icon];
                        } else {
                            reviewsHistory = items[i];
                        }
                        if(phase === 1) {
                            html =
                                `<tr>
                            <td>上一次评审</td>
                            <td title="${items[i].outer_sn}">${items[i].outer_sn}</td>
                            <td title="${items[i].outer_name}">${items[i].outer_name}</td>
                            <td title="${items[i].unit}">${items[i].unit}</td>
                            <td title="${items[i].amount}">${items[i].amount}</td>
                            <td title="${new Date(items[i].delivery_date).format("yyyy-MM-dd")}">${new Date(items[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td title="${items[i].delivery_address}">${items[i].delivery_address}</td>
                            <td title="${icon > -1?reviewsHistory.scheduledAmount:""}">${icon > -1?reviewsHistory.scheduledAmount:""}</td>
                            <td title="${icon > -1 && !reviewsHistory.isScheduled ? reviewsHistory.surplusDate:""}">
                                ${icon > -1 && !reviewsHistory.isScheduled ? reviewsHistory.surplusDate:""}</td>
                            <td></td>
                            </tr>`;
                        } else {
                            html =
                                `<tr class="${bgStr} ${type === 1 ? "reviewTr" : ""}" ${type === 1 ? "data-line="+no : ""}>
                            <td>上一次评审</td>
                            <td title="${type === 3 ? '' : (icon > -1?reviewsHistory.outerSn:"")}">${type === 3 || icon === -1 ? '--' : (icon > -1?reviewsHistory.outerSn:"")}</td>
                            <td title="${type === 3 ? '' : (icon > -1?reviewsHistory.outerName:"")}">${type === 3 || icon === -1 ? '--' : (icon > -1?reviewsHistory.outerName:"")}</td>
                            <td title="${type === 3 ? '' : (icon > -1?reviewsHistory.unit:"")}">${type === 3 || icon === -1 ? '--' : (icon > -1?reviewsHistory.unit:"")}</td>
                            <td title="${type === 3 ? '' : (icon > -1?reviewsHistory.oldAmount || reviewsHistory.amount:"")}">${type === 3 || icon === -1 ? '--' : (icon > -1?reviewsHistory.oldAmount || reviewsHistory.amount:"")}</td>
                            <td title="${type === 3 ? '' : (icon > -1?reviewsHistory.oldDeliveryDate || reviewsHistory.deliveryDate:"")}">${type === 3 || icon === -1 ? '--' : (icon > -1?reviewsHistory.oldDeliveryDate || reviewsHistory.deliveryDate:"")}</td>
                            <td title="${type === 3 ? '' : (icon > -1?reviewsHistory.oldAddress || reviewsHistory.deliveryAddress:"")}">${type === 3 || icon === -1 ? '--' : (icon > -1?reviewsHistory.oldAddress || reviewsHistory.deliveryAddress:"")}</td>
                            <td title="${type === 3 ? '' : (icon > -1?reviewsHistory.scheduledAmount:"")}">${type === 3 || icon === -1 ? '--' : (icon > -1?reviewsHistory.scheduledAmount:"")}</td>
                            <td title="${type === 3 ? '' : (icon > -1 && !reviewsHistory.isScheduled ? new Date(reviewsHistory.surplusDate).format('yyyy-MM-dd'):"")}">
                                ${type === 3 ? '--' : (icon > -1 && !reviewsHistory.isScheduled ? new Date(reviewsHistory.surplusDate).format('yyyy-MM-dd'):"")}</td>
                            <td>
                            ${type === 1 ? "<span class='ty-color-blue' onclick='deliveryDateCon( $(this) , "+phase+")'>去评审</span><span class='hd'>"+JSON.stringify(items[i])+"</span>" : ""}   
                            </td>
                            </tr>`;//1有“去评审”按钮,wu本次评审
                        }

                        if (phase === 1 || phase === 2 && type === 6) { //显示上一次评审、本次评审,,type === 2数量日期或地点有一项或多项变动的,=3新增的
                            lastHtml = `<tr class='reviewTr ${bgStr}' data-line="${no}"><td>本次评审</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><input type='text' onblur="testQuantity($(this))" value="" placeholder="请录入数量" /></td>
                            <td>
                                <input type='text' readonly id='goods${i}' disabled="disabled" placeholder="请选择日期" />
                            </td>
                            <td>
                            <span class='ty-color-blue' onclick='deliveryDateCon( $(this) , ${phase} )'> ${phase === 1 ? '修改' : '去评审'}</span>
                            <span class='hd'>${JSON.stringify(items[i])}</span>
                            </td>
                            </tr>`;
                        } else if (phase === 2 && (type === 2 || type === 3)) { //显示上一次评审、本次评审,,type === 2数量日期或地点有一项或多项变动的,=3新增的.
                            lastHtml = `<tr class='reviewTr ${bgStr}' data-line="${no}"><td>本次评审</td>
                            <td title="${type === 3 ? items[i].outer_sn : ''}" ${type === 3 ? ' class="red"': ""}>${type === 3 ? items[i].outer_sn : ''}</td>
                            <td title="${type === 3 ? items[i].outer_name : ''}" ${type === 3 ? ' class="red"': ""}>${type === 3 ? items[i].outer_name : ''}</td>
                            <td title="${type === 3 ? items[i].unit : ''}" ${type === 3 ? ' class="red"': ""}>${type === 3 ? items[i].unit : ''}</td>
                            <td title="${items[i].amount}" ${type === 3 || (type === 2 && items[i].amount !== reviewsHistory.oldAmount) ? 'class="red"': ""}>${items[i].amount}</td>
                            <td title="${new Date(items[i].delivery_date).format("yyyy-MM-dd")}" ${type === 3 || (type === 2 && reviewsHistory.oldDeliveryDate !== new Date(items[i].delivery_date).format("yyyy-MM-dd")) ? ' class="red"': ""}>${new Date(items[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td title="${items[i].delivery_address}" ${type === 3 || (type === 2 && items[i].delivery_address !== reviewsHistory.oldAddress) ? ' class="red"': ""}>${items[i].delivery_address}</td>
                            <td><input type='text' onblur="testQuantity($(this))" value="" placeholder="请录入数量" /></td>
                            <td>
                                <input type='text' readonly id='goods${i}' disabled="disabled" placeholder="请选择日期" />
                            </td>
                            <td>
                            <span class='ty-color-blue' onclick='deliveryDateCon( $(this) , ${phase} )'>去评审</span>
                            <span class='hd'>${JSON.stringify(items[i])}</span>
                            </td>
                            </tr>`;
                        } else if (phase === 2 && type === 4) {//情况4分批发货的
                            let ship = items[i].partialShipment;
                            for (let a=0; a< ship.length; a++) {
                                let brach = ``;
                                items[i].partialShipment = ship[a];
                                if (a === 0) {
                                    brach = html + `<tr class='reviewTr ${bgStr}' data-line="${no}"><td rowspan="${ship.length}">本次评审</td>`;
                                } else {
                                    reviewLen++;
                                    brach = `<tr class='reviewTr ${bgStr}' data-line="${no}">`;
                                }
                                brach += `<td></td>
                                                <td></td>
                                                <td></td>
                                                <td class="red">${ship[a].newAmount}</td>
                                                <td class="red">${new Date(ship[a].deliveryDate).format("yyyy-MM-dd")}</td>
                                                <td></td>
                                                <td><input type='text' onblur="testQuantity($(this), 4)" value="" placeholder="请录入数量" /></td>
                                                <td>
                                                    <input type='text' readonly id='goods${i + a}' disabled="disabled" placeholder="请选择日期" />
                                                </td>
                                                <td>
                                                <span class='ty-color-blue' onclick='deliveryDateCon( $(this) , ${phase} )'>去评审</span>
                                                <span class='hd'>${JSON.stringify(items[i])}</span>
                                                </td>
                                                </tr>`;
                                $(".goReviewList1 tbody").append( brach ) ;
                                let inum = i + a;
                                laydate.render({elem: "#goods" + inum, min: new Date(ship[a].deliveryDate+ 24*60*60*1000).format("yyyy-MM-dd")});
                            }
                        } else if (phase === 2 && type === 5) {//情况5数量被改为零的
                            lastHtml = `<tr class='${bgStr}'><td>本次评审</td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td class="red">0</td>
                                                <td></td>
                                                <td></td>
                                                <td><input type='text' value="" disabled="disabled" placeholder="请录入数量"/></td>
                                                <td>
                                                    <input type='text' readonly id='goods${i}' disabled="disabled" placeholder="请选择日期" disabled="disabled" />
                                                </td>
                                                <td>
                                                <span class='ty-color-gray'>去评审</span>
                                                <span class='hd'>${JSON.stringify(items[i])}</span>
                                                  </td>
                                                </tr>`;
                        }
                    }
                    if (phase === 0) {
                        $(".goReviewList0 tbody").append( html ) ;
                        laydate.render({elem: "#goods" + i, min: new Date(items[i].delivery_date + 24*60*60*1000).format("yyyy-MM-dd")});
                    } else {
                        if (!(phase === 2 && items[i].progress === '4')) {
                            $(".goReviewList1 tbody").append( html + lastHtml ) ;
                            laydate.render({elem: "#goods" + i, min: new Date(items[i].delivery_date + 24*60*60*1000).format("yyyy-MM-dd")});
                        }
                    }
                }
            }
            $("#totalNum").html(reviewLen);
            $("#okNum").html(0);
            $("#sumNum").html(0);
            if (phase === 0) {
                $(".yyTip").show();
                $(".goReviewList0").show().siblings("table").hide() ;
                tip = "以下各条商品在“要求到货日期”能到货的数量分别为多少？请评估。对于不能全部到货的，还需确认“剩余数量的到货日期”，并选择！";
            } else if(phase === 1){
                $(".yyTip").hide();
                $(".goReviewList1").show().siblings("table").hide() ;
            } else {
                $(".yyTip").show();
                $(".goReviewList1").show().siblings("table").hide() ;
                if (ableSix) {
                    tip =`${res.saleName}于${new Date(res.saleReviewDate).format("yyyy-MM-dd hh:mm:ss")}调整了部分商品的要货需求。能否按新要求完成本订单？请重新评审！`;
                } else {
                    tip ="以下各条商品在“要求到货日期”能到货的数量分别为多少？请评估。对于不能全部到货的，还需确认“剩余数量的到货日期”，并选择！";
                }
            }
            $(".yyTip").html(tip);
        }
    });
}

/* Creator : 侯杏哲 2017-07-18 评审 - 设置评审记录详情 */
function getOrderInfo( config ){
    var itemInfo = config["itemInfo"] ;
    var no = config["no"] ;
    var tbl = config["tbl"] ;
    var i = config["i"] ;

    var map = itemInfo["map"] ;
    var data_1 = itemInfo["map"]["data"] ;
    var data_history = map["itemHistories"] && map["itemHistories"]["data"] ;

    // 判断是不是刚刚修改完
    var hisTime = 0 , reviewTime = 0 ;
    var data_1_len = data_1.length ;
    if(data_history && data_history.length > 0){ hisTime = data_history[data_history.length -1]["update_date"] ;  }
    if(data_1 && data_1_len > 0){ reviewTime = data_1[data_1_len -1]["create_date"] ;  }
    if(hisTime <reviewTime){ // 修改之后进行过评审
        // 取最后评审的两条
        var lastData = data_1[data_1_len - 1] ;
        var lastData_2 = lastData["data"] && lastData["data"][0] ;
        if(lastData_2){
            var strNew =    "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
                                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                                "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
                                "<td>"+ lastData["itemAmount"] +"</td>" +
                                "<td>"+ formatTime(lastData["item_delivery_date"]) +"</td>" ;
            if( (lastData["surplus_date"] && lastData["scheduled_amount"]) || (lastData["surplus_date"] && lastData["scheduled_amount"] == 0) ){
                strNew += "<td>否</td><td>"+ lastData["scheduled_amount"] +"</td><td>"+ formatTime(lastData["surplus_date"]) +"</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ lastData["memo"] +"<span class='hd'>"+ lastData["id"] +"</span></td></tr>" ;
            }else{
                strNew += "<td>能</td><td>— —</td><td>— —</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ lastData["memo"] +"<span class='hd'>"+ lastData["id"] +"</span></td></tr>" ;
            }
            strNew += "<tr class='reviewTr' info='"+ JSON.stringify(itemInfo) +"'>" +
                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'> </td>" +
                "<td>"+ lastData_2["amount"] +"</td>" +
                "<td>"+ formatTime(lastData_2["delivery_date"] || "") +"</td>" +
                "<td>" +
                "<div class='ty-btn-group'>" +
                "<span class='ty-btn' value='1' onclick='togglePower($(this))'>是</span>" +
                "<span class='ty-btn ty-btn-active-red' value='0' onclick='togglePower($(this))' >否</span>" +
                "</div>" +
                "<input type='hidden' class='hd' value='0'/>" +
                "</td>" +
                "<td><input type='text' class='ty-inputText'></td>" +
                "<td><input type='text' class='ty-inputText laydate-icon' readonly id='goods"+ i +"'></td>" +
                "<td><input type='text' class='ty-inputText'></td>" +
                "</tr>" ;
            tbl.append( strNew ) ;
            laydate.render({elem: "#goods" + i});
        }else{ // 处理没修改 , 这一条可以重新评审 
            strNew += "<tr class='reviewTr' info='"+ JSON.stringify(itemInfo) +"'>" +
                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
                "<td>"+ lastData["amount"] +"</td>" +
                "<td>"+ formatTime(lastData["delivery_date"]) +"</td>" ;
            if( (lastData["surplus_date"] && lastData["scheduled_amount"]) || (lastData["surplus_date"] && lastData["scheduled_amount"] == 0) ){
                // strNew += "<td>否</td><td>"+ lastData["scheduled_amount"] +"</td><td>"+ lastData["surplus_date"] +"</td><td>"+ lastData["memo"] +"</td></tr>" ;
                strNew += "<td>" +
                "<div class='ty-btn-group'>" +
                "<span class='ty-btn' value='1' onclick='togglePower($(this))'>是</span>" +
                "<span class='ty-btn ty-btn-active-red' value='0' onclick='togglePower($(this))' >否</span>" +
                "</div>" +
                    "<input type='hidden' class='hd' value='0'/>" +
                "</td>" +
                "<td><input type='text' class='ty-inputText' value='"+ lastData["scheduled_amount"] +"'></td>" +
                "<td><input type='text' class='ty-inputText laydate-icon' readonly id='goods"+ i +"' value='"+ formatTime(lastData["surplus_date"]) +"'></td>" +
                "<td><input type='text' class='ty-inputText' value='"+ lastData["memo"] +"'></td>" +
                "</tr>" ;
            }else{
                // strNew += "<td>能</td><td>— —</td><td>— —</td><td>"+ lastData["memo"] +"</td></tr>" ;
                strNew += "<td>" +
                    "<div class='ty-btn-group'>" +
                    "<span class='ty-btn ty-btn-active-green' value='1' onclick='togglePower($(this))'>是</span>" +
                    "<span class='ty-btn' value='0' onclick='togglePower($(this))' >否</span>" +
                    "</div>" +
                    "<input type='hidden' class='hd' value='1'/>" +
                    "</td>" +
                    "<td>— —</td>" +
                    "<td>— —</td>" +
                    "<td><input type='text' class='ty-inputText' value='"+ lastData["memo"] +"'></td>" +
                    "</tr>" ;
            }
            tbl.append( strNew ) ;
            laydate.render({elem: "#goods" + i});
        }

    }else{  // 刚修改完
        if( data_1 && data_1.length == 1 && data_1[0]["data"] == null ){  // 处理没有修改过
            var reviewDate = data_1[0]["review_date"] ;  // 评审时间
            var isWrite = false ; // 标记 评审的那条有没有写入 ，默认没有
            if(data_history && data_history.length > 0 ){   // 如果有订单修改记录，那么所有的修改记录都写在前面
                for(var q = 0 ; q < data_history.length ; q++ ){
                    var updateDate = data_history[q]["update_date"] ;   //  订单修改的时间
                    if( reviewDate && (reviewDate < updateDate) && !isWrite ){ // 评审在前，再修改
                        var strReview = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
                            "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                            "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
                            "<td>"+ data_1[0]["itemAmount"] +"</td>" +
                            "<td>"+  (itemInfo["item_delivery_date"] || "") +"</td>" ;
                        if( data_1[0]["surplus_date"]){  // 否
                            strReview += "<td> 否 </td><td>"+ data_1[0]["scheduled_amount"] +"</td><td>"+ formatTime(data_1[0]["surplus_date"]) +"</td><td>"+ data_1[0]["memo"] +"</td></tr>" ;
                        }else{ // 是
                            strReview += "<td>是</td><td>— —</td><td>— —</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[0]["memo"] +"<span class='hd'>"+ data_1[0]["id"] +"</span></td></tr>" ;
                        }
                        tbl.append( strReview ) ;
                        isWrite = true ;
                    }
                    var str_history = "" ;
                    if(q == 0 ){
                        str_history = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
                            "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                            "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
                            "<td>"+ data_history[q]["amount"] +"</td>" +
                            "<td>"+ formatTime(data_history[q]["delivery_date"]) +"</td>" +
                            "<td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
                            "</tr>" ;
                    }else if( q < data_history.length - 1  ){
                        str_history = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
                            "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                            "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
                            "<td>"+ data_history[q]["amount"] +"</td>" +
                            "<td>"+ formatTime(data_history[q]["delivery_date"]) +"</td>" +
                            "<td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
                            "</tr>" ;
                    }else{
                        str_history = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
                            "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                            "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
                            "<td>"+ data_history[q]["amount"] +"</td>" +
                            "<td>"+ formatTime(data_history[q]["delivery_date"]) +"</td>" +
                            "<td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
                            "</tr>" ;
                    }

                    tbl.append( str_history ) ;
                }
            }
             
            var str = "<tr class='reviewTr' info='"+ JSON.stringify(itemInfo) +"'>" +
                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
                "<td>"+ data_1[0]["amount"] +"</td>" +
                "<td>"+ formatTime( data_1[0]["delivery_date"]) +"</td>" ;
            str += "<td>" +
                "<div class='ty-btn-group'>" +
                "<span class='ty-btn' value='1' onclick='togglePower($(this))'>是</span>" +
                "<span class='ty-btn ty-btn-active-red' value='0' onclick='togglePower($(this))' >否</span>" +
                "</div>" +
                "<input type='hidden' class='hd' value='0'/>" +
                "</td>" +
                "<td><input type='text' class='ty-inputText'></td>" +
                "<td><input type='text' class='ty-inputText laydate-icon' readonly id='goods"+ i +"'></td>" +
                "<td><input type='text' class='ty-inputText'></td>" +
                "</tr>" ;
            tbl.append( str ) ;
            laydate.render({elem: "#goods" + i});

        }else{ // 处理修改过 ， 要区分订单修改之前和订单修改之后的处理评审
            var hisNum = 0 ; // 标记历史写入的条数
            for( var k = 0 ; k < data_1.length ; k++ ){
                var str = "" ;
                var isStr = false ;
                var data_2 = data_1[k]["data"] && data_1[k]["data"][0] ;
                var dd = data_1[k] ;
                // 首先判断修改的数据
                if(data_history){
                    var reviewDate = data_1[k]["review_date"] ; // 本行的创建时间
                    // 加入历史表数据， 由此判断这条能不能被修改 ；
                    var his = hisNum ;
                    for( ; his < data_history.length ; his++ ){
                        var str_his = "" ;
                        var createDate = data_history[hisNum]["update_date"] ; // 订单修改的时间
                        if( reviewDate > createDate){ // 本有效行的时间晚  ， 订单修改的放在前面
                            if( hisNum == 0 && k == 0){
                                str_his = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
                                    "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                                    "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
                                    "<td>"+ data_history[hisNum]["amount"] +"</td>" +
                                    "<td>"+ formatTime(data_history[hisNum]["delivery_date"] || "") +"</td>" +
                                    "<td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
                                    "</tr>" ;
                            }else{
                                str_his = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
                                    "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                                    "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
                                    "<td>"+ data_history[hisNum]["amount"] +"</td>" +
                                    "<td>"+ formatTime(data_history[hisNum]["delivery_date"] || "") +"</td>" +
                                    "<td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
                                    "</tr>" ;
                            }
                            hisNum = his + 1  ;
                            tbl.append( str_his );
                        }
                    }
                }

                // 处理这一行数据
                if( !data_2 && (k == (data_1.length - 1)) ){ // 最后一次处理 ，还没评审
                    str += "<tr class='reviewTr' info='"+ JSON.stringify(itemInfo) +"'>" +
                        "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" ;
                    if( k == 0){ str += "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" ;  }else{ str += "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" ; }
                }else if( k == 0 ) {
                    str += "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
                        "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" +
                        "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" ;
                }else{
                    str += "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
                        "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" +
                        "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'> </td>" ;
                }

                str += "<td>"+ data_1[k]["itemAmount"] +"</td>" ;
                str += "<td>"+ formatTime( data_1[k]["item_delivery_date"] ) +"</td>" ;

                // 先判断之后还有没有历史， 有的话这一条直接写死划线，否则判断评审那一条
                if(data_history){
                    if( hisNum == data_history.length ){  // 修改的已经写完了
                        if( (data_1.length - 1) == k  ){
                            if( data_2 ){ // 这一条不评审，评审处理后的那条
                                if( (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"]) || (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"] == 0) ){
                                    str += "<td>否</td><td>"+ data_1[k]["scheduled_amount"] +"</td><td>"+ formatTime(data_1[k]["surplus_date"]) +"</td><td>"+ data_1[k]["memo"] +"</td></tr>" ;
                                }else{
                                    str += "<td>能</td><td>— —</td><td>— —</td><td>"+ data_1[k]["memo"] +"</td></tr>" ;
                                }
                                str += "<tr class='reviewTr' info='"+ JSON.stringify(itemInfo) +"'>" +
                                    "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                                    "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
                                    "<td>"+ data_2["amount"] +"</td>" +
                                    "<td>"+ formatTime( data_2["delivery_date"]) +"</td>" +
                                    "<td>" +
                                    "<div class='ty-btn-group'>" +
                                    "<span class='ty-btn' value='1' onclick='togglePower($(this))'>是</span>" +
                                    "<span class='ty-btn ty-btn-active-red' value='0' onclick='togglePower($(this))' >否</span>" +
                                    "</div>" +
                                    "<input type='text' class='hd' value='0'/>"+
                                    "</td>" +
                                    "<td><input type='text' class='ty-inputText'></td>" +
                                    "<td><input type='text' class='ty-inputText laydate-icon' readonly id='goods"+ i +"'></td>" +
                                    "<td><input type='text' class='ty-inputText'></td>" +
                                    "</tr>" ;
                            }else{ // 最后一个还可以修改
                                if( (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"]) || (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"] == 0) ){
                                    str += "<td>" +
                                        "<div class='ty-btn-group'>" +
                                        "<span class='ty-btn' value='1' onclick='togglePower($(this))'>是</span>" +
                                        "<span class='ty-btn ty-btn-active-red' value='0' onclick='togglePower($(this))' >否</span>" +
                                        "</div>" +
                                        "<input type='hidden' class='hd' value='0'/>" +
                                        "</td>" +
                                        "<td><input type='text' class='ty-inputText' value='"+ data_1[k]["scheduled_amount"] +"' ></td>" +
                                        "<td><input type='text' class='ty-inputText laydate-icon' readonly id='goods"+ i +"' value='"+ formatTime(data_1[k]["surplus_date"]) +"'></td>" +
                                        "<td><input type='text' class='ty-inputText' value='"+ data_1[k]["memo"] +"'></td></tr>" ;
                                }else{
                                    str += "<td>" +
                                        "<div class='ty-btn-group'>" +
                                        "<span class='ty-btn ty-btn-active-green' value='1' onclick='togglePower($(this))'>是</span>" +
                                        "<span class='ty-btn ' value='0' onclick='togglePower($(this))' >否</span>" +
                                        "</div>" +
                                        "<input type='hidden' class='hd' value='1'/>" +
                                        "</td>" +
                                        "<td>— —</td>" +
                                        "<td>— —</td>" +
                                        "<td><input type='text' class='ty-inputText' value='"+ data_1[k]["memo"] +"'></td></tr>" ;
                                }
                            }
                        }else{
                            if( (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"]) || (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"] == 0) ){
                                str += "<td>否</td><td>"+ data_1[0]["scheduled_amount"] +"</td><td>"+ formatTime(data_1[0]["surplus_date"]) +"</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[0]["memo"] +"<span class='hd'>"+ data_1[0]["id"] +"</span></td></tr>" ;
                            }else{
                                str += "<td>能</td><td>— —</td><td>— —</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[0]["memo"] +"<span class='hd'>"+ data_1[0]["id"] +"</span></td></tr>" ;
                            }
                        }
                    }else{ // 订单修改的没写完 ， 这一条直接写死划线
                        if( (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"]) || (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"] == 0) ){
                            str += "<td>否</td><td>"+ data_1[k]["scheduled_amount"] +"</td><td>"+ formatTime(data_1[k]["surplus_date"]) +"</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[k]["memo"] +"<span class='hd'>"+ data_1[k]["id"] +"</span></td></tr>" ;
                        }else{
                            str += "<td>能</td><td>— —</td><td>— —</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[k]["memo"] +"<span class='hd'>"+ data_1[k]["id"] +"</span></td></tr>" ;
                        }

                    }
                }else{
                    if( (data_1.length - 1) == k  ){
                        if( data_2 ){ // 这一条不评审，评审处理后的那条
                            if( (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"]) || (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"] == 0) ){
                                str += "<td>否</td><td>"+ data_1[k]["scheduled_amount"] +"</td><td>"+ formatTime(data_1[k]["surplus_date"]) +"</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[k]["memo"] +"<span class='hd'>"+ data_1[k]["id"] +"</span></td></tr>" ;
                            }else{
                                str += "<td>能</td><td>— —</td><td>— —</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[k]["memo"] +"<span class='hd'>"+ data_1[k]["id"] +"</span></td></tr>" ;
                            }
                            str += "<tr class='reviewTr' info='"+ JSON.stringify(itemInfo) +"'>" +
                                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                                "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
                                "<td>"+ data_2["amount"] +"</td>" +
                                "<td>"+ formatTime( data_2["delivery_date"]) +"</td>" +
                                "<td>" +
                                "<div class='ty-btn-group'>" +
                                "<span class='ty-btn' value='1' onclick='togglePower($(this))'>是</span>" +
                                "<span class='ty-btn ty-btn-active-red' value='0' onclick='togglePower($(this))' >否</span>" +
                                "</div>" +
                                "<input type='hidden' class='hd' value='0'/>" +
                                "</td>" +
                                "<td><input type='text' class='ty-inputText'></td>" +
                                "<td><input type='text' class='ty-inputText laydate-icon' readonly id='goods"+ i +"'></td>" +
                                "<td><input type='text' class='ty-inputText'></td>" +
                                "</tr>" ;
                        }else{ // 最后一个还可以修改
                            if( (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"]) || (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"] == 0) ){
                                str += "<td>" +
                                    "<div class='ty-btn-group'>" +
                                    "<span class='ty-btn' value='1' onclick='togglePower($(this))'>是</span>" +
                                    "<span class='ty-btn ty-btn-active-red' value='0' onclick='togglePower($(this))' >否</span>" +
                                    "</div>" +
                                    "<input type='hidden' class='hd' value='0'/>" +
                                    "</td>" +
                                    "<td><input type='text' class='ty-inputText' value='"+ data_1[k]["scheduled_amount"] +"' ></td>" +
                                    "<td><input type='text' class='ty-inputText laydate-icon' readonly  id='goods"+ i +"' value='"+ formatTime(data_1[k]["surplus_date"]) +"'></td>" +
                                    "<td><input type='text' class='ty-inputText' value='"+ data_1[k]["memo"] +"'></td></tr>" ;
                            }else{
                                str += "<td>" +
                                    "<div class='ty-btn-group'>" +
                                    "<span class='ty-btn ty-btn-active-green' value='1' onclick='togglePower($(this))'>是</span>" +
                                    "<span class='ty-btn ' value='0' onclick='togglePower($(this))' >否</span>" +
                                    "</div>" +
                                    "<input type='hidden' class='hd' value='1'/>" +
                                    "</td>" +
                                    "<td>— —</td>" +
                                    "<td>— —</td>" +
                                    "<td><input type='text' class='ty-inputText' value='"+ data_1[k]["memo"] +"'></td></tr>" ;
                            }
                        }
                    }else{
                        if( (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"]) || (data_1[k]["surplus_date"] && data_1[k]["scheduled_amount"] == 0) ){
                            str += "<td>否</td><td>"+ data_1[0]["scheduled_amount"] +"</td><td>"+ data_1[0]["surplus_date"] +"</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[0]["memo"] +"<span class='hd'>"+ data_1[0]["id"] +"</span></td></tr>" ;
                        }else{
                            str += "<td>能</td><td>— —</td><td>— —</td><td onmouseover='showChargeMess($(this))' onmouseout='hideshowChargeMess($(this))'>"+ data_1[0]["memo"] +"<span class='hd'>"+ data_1[0]["id"] +"</span></td></tr>" ;
                        }
                    }
                }

                tbl.append( str );
                laydate.render({elem: "#goods" + i});
            }

            if( data_history && hisNum != data_history.length  ){
                for( var g = hisNum ; g < data_history.length ; g++ ){
                    var str_his = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
                        "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                        "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
                        "<td>"+ data_history[hisNum]["amount"] +"</td>" +
                        "<td>"+ formatTime(data_history[hisNum]["delivery_date"]) +"</td>" +
                        "<td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
                        "</tr>" ;
                    tbl.append( str_his );
                }
                var strNew = "<tr class='reviewTr' info='"+ JSON.stringify(itemInfo) +"'>" +
                    "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                    "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
                    "<td>"+ data_1[0]["amount"] +"</td>" +
                    "<td>"+ formatTime(data_1[0]["delivery_date"]) +"</td>" +
                    "<td>" +
                    "<div class='ty-btn-group'>" +
                    "<span class='ty-btn' value='1' onclick='togglePower($(this))'>是</span>" +
                    "<span class='ty-btn ty-btn-active-red' value='0' onclick='togglePower($(this))' >否</span>" +
                    "</div>" +
                    "<input type='hidden' class='hd' value='0'/>" +
                    "</td>" +
                    "<td><input type='text' class='ty-inputText'></td>" +
                    "<td><input type='text' class='ty-inputText laydate-icon' readonly id='goods"+ i +"'></td>" +
                    "<td><input type='text' class='ty-inputText'></td>" +
                    "</tr>" ;
                tbl.append( strNew );
                laydate.render({elem: "#goods" + i});
            }
        }
        // 该划线的都划线
        var newTr = [] ;
        var kids = 0 ;
        tbl.children("tr").each(function(){
            var thisNo = $(this).children("td:eq(0)").children("div:eq(0)").html() ;
            if( thisNo == no ){
                newTr.push( $(this) ) ;
                var klass = $(this).attr("class") ;
                if( klass == "throw"){ kids = newTr.length - 1 ;  $(this).attr("class" , "hd");   }
            }
        }) ;
        for(var e = 0 ; e < newTr.length ; e++ ){
            if( kids > e ){
                newTr[e].attr("class" ,"throw") ;
            }
        }
    }
}
// creator : hxz 2018-07-18  新增/修改订单 中 编辑商品 - 相关订购信息
function linkOrds(obj) {
    bounce_Fixed2.show($("#linkOrd"));
    $("#linkOrdTb").find("tr:gt(0)").remove();
    var pConObj = obj.parents(".bonceContainer") ;
    var outerSn = pConObj.find(".outer_sn").html() ;
    var outerName = pConObj.find(".outer_name").val() ;
    var currentStock = pConObj.find(".current_stock").val() ;
    var unit = pConObj.find(".unit").val() ;
    if(outerSn == ''){
        layer.msg('请先选择商品！')
        return false
    }
    var data =  {"outerSn": outerSn  } ;
    data["orderId"] = chargeObj["ordID"] ;
    $.ajax({
        "url": "../sale/getOccOrder.do",
        "data": data ,
        success: function (res) {
            var list = res["data"], str = "";
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    str += "<tr>" +
                        "     <td>"+ (i+1) +"</td>" +
                        "     <td>"+ (list[i]["sn"] ||"") +"</td>" +
                        "     <td>"+ (list[i]["create_name"] ||"") +"</td>" +
                        "     <td>"+ (list[i]["t_amount"] ||"") +"</td>" +
                        "     <td>"+ formatTime(list[i]["earliest_delivery_date"]) +"</td>" +
                        "     <td>"+ (list[i]["customer_name"] ||"") +"</td>" +
                        " </tr>";
                }
                $("#linkOrdTb").append(str);
                $(".ysGs").show(); $(".noGs").hide();
                $("#linkOrdName").html(outerName); $("#linkOrdStock").html(currentStock) ; $("#linkOrdUnit").html(unit);
            }else{
                $(".ysGs").hide(); $(".noGs").show();
            }
        }
    })
}
/* 评审页面鼠标滑过备注显示处理人 */
function showChargeMess( _this ){
    var info = _this.parent().attr("info") ;
    var reviewID = _this.children(".hd").html() ; 
    info = JSON.parse(info);
    var offsetObj = _this.offset() ;
    var leftObj =offsetObj.left ;
    var topObj = offsetObj.top ;
    $("#tipCon").animate({ left: leftObj+ "px" , top:topObj+"px"  }, 10).show() ;
    var data_1 = info["map"] && info["map"]["data"] ; 
    var str = "暂无处理信息" ;
    if( data_1 && data_1.length > 0 ){
        for(var i = 0 ; i < data_1.length ; i++ ){
            var id = data_1[i]["id"] ;
            if(reviewID == id){
                var data_2 = data_1[i]["data"] && data_1[i]["data"][0] ;
                if(data_2){
                    str = "<span class='tipitemCon'> 处理人："+ ( data_2["reviewer_name"]||"" ) +"</span><span class='tipitemCon'> 处理时间："+ formatTime(data_2["create_date"]) +"</span>" ;
                }
                break ; 
            }
        }
    }
    $("#tipitem").html(str);
}
function hideshowChargeMess( _this  ){
    $("#tipCon").hide().offset({ left:0 , top:0 });
}
/* Creator: 侯杏哲 2017-06-02 评审 - 提交评审 */
function review(){
    var reviewList = new Array();
    var isok = 1 , phaseNum = Number($("#phaseNum").html());
    let obj = $(".goReviewList1").is(":visible") ? $(".goReviewList1"): $(".goReviewList0");
    obj.children("tbody").find("tr.reviewTr").each(function(){
        var scheduledAmout = "";
        var surplusDateObj = "";
        let info = JSON.parse($(this).find(".hd").html());
        let item = {"orders": chargeObj["ordID"],  "ordersItem":info.id, "isScheduled": 1}
         if (Number(info.amount) >= 0) {
             if(phaseNum === 2 && info.progress === "1") {
                 scheduledAmout = $(this).children().eq(7).html();
                 surplusDateObj = $(this).children().eq(8);
                 item.isScheduled = info.isScheduled;
                 item.scheduledAmount = scheduledAmout;
                 item.surplusDate = surplusDateObj.html();
             } else {
                 scheduledAmout = $(this).find("input").eq(0).val();
                 surplusDateObj = $(this).find("input").eq(1);
                 var no = $(this).data("line");
                 if( $.trim(scheduledAmout) == "" || (!surplusDateObj.prop("disabled") && $.trim(surplusDateObj.val()) == "") ){
                     layer.msg("请补充完整第"+ no +"条商品的'能按时到货的数量'和'剩余数量的到货日期'");
                     isok = 0 ;
                     return false ;
                 }
                 if( isNaN(Number(scheduledAmout)) ){
                     layer.msg("第"+ no +"条商品的'能按时到货的数量' 请合理输入数字");
                     isok = 0 ;
                     return false ;
                 }
                 item.scheduledAmount = scheduledAmout;
                 item.surplusDate = surplusDateObj.val();
                 let anum = phaseNum === 2 && info.progress === '4' ? Number(info.partialShipment.newAmount) : Number(info.amount);
                 if (Number(scheduledAmout) < anum) {
                     item.isScheduled = 0;
                 } else if (Number(scheduledAmout) > anum) {
                     layer.msg("能按时到货的数量不能大于订购数量！知道了");
                     isok = 0 ;
                     return false ;
                 }
                 if (phaseNum === 1) { //修改，需要传id
                     let history = info.reviews;
                     if(info.progress !== '3'){
                         item.id = history[(history.length - 1)].id
                     }
                 } else {
                     if (phaseNum === 2 && info.progress === '4') item.ordersReview = info.partialShipment.id;
                 }
             }
             item.memo = info.memo || "";
             reviewList.push(item) ;
         }
    });
    if(isok == 0){  return false ;     }
    loading.open() ;
    $.ajax({
        type: "POST",
        url: "../orders/review/produceHandle?versionNo="+ chargeObj["version_no"] +"&orderId=" + chargeObj["ordID"],
        data: JSON.stringify(reviewList)  ,//将对象序列化成JSON字符串
        dataType:"json",
        contentType : 'application/json;charset=utf-8', //设置请求头信息
        success: function(data){
            let msg = data.msg;
            if (data.code !== 500) {
                showMainCon(1);
                getList( 1 , 10 );
            } else {
                layer.msg(msg);
                chargeObj["spanObj"].click();
            }
        },
        error: function(res){
            loading.close() ; bounce.show( $("#tip") ) ; $("#tipMs").html("链接错误请刷新重试") ;
        }
    });
}
// Creator : 侯杏哲 2017-06-08  评审记录
function chargeHistory(ordID , _this ){
    $.ajax({
        url:"../sr/toProductionManage.do" ,
        data:{ "orderId" : ordID , "type":4  } ,
        type:"post" ,
        dataType:"json" ,
        success:function( res ){
            var list = res["items"] ;  // 商品列表
            var order = res["order"] ;  // 订单详情
            bounce.show( $("#approvePRHistory") ) ;
            $("#cusName").html( order["customerName"] ) ;
            $("#cusCode").html( order["cusCode"] ) ;
            $("#ordSn").html( order["sn"] ) ;
            $("#ordReciveDate").html( formatTime( order["earliestDeliveryDate"] ) ) ;
            $("#creator").html( order["createName"] ) ;
            $("#createDate").html( formatTime( order["createDate"] ) ) ;
            var str = "" ;
            if( list && list.length ){
                $("#historyList").html("") ;
                for(var i=0 ; i < list.length ; i++ ){
                    setHistoryStr( { "itemInfo":list[i] , "tbl": $("#historyList") , "i": i } ) ;
                }
            }
            setRoleInit("roleCon") ;
        } ,
        error:function(){

        }
    });  

}
/* Creator : 评审记录 的列表显示   */
function setHistoryStr( config ){
    var itemInfo = config["itemInfo"] ;
    var tbl = config["tbl"] ;
    var i = config["i"] ;

    var no = i + 1 ;
    var map = itemInfo["map"] ;
    var data_1 = itemInfo["map"]["data"] ;
    var data_history = map["itemHistories"] && map["itemHistories"]["data"] ;

    var hisNum = 0 ; // 标记历史写入的条数
    for( var k = 0 ; k < data_1.length ; k++ ){
        var str = "" ;
        var data_2 = data_1[k]["data"] && data_1[k]["data"][0] ;
        var dd = data_1[k] ;
        // 首先判断修改的数据
        if(data_history){
            var reviewDate = data_1[k]["review_date"] ; // 本行的创建时间
            // 加入历史表数据， 由此判断这条能不能被修改 ；
            var his = hisNum ;
            for( ; his < data_history.length ; his++ ){
                var str_his = "" ;
                var createDate = data_history[hisNum]["update_date"] ; // 订单修改的时间
                if( reviewDate > createDate){  // 本有效行的时间晚  ， 订单修改的放在前面
                    if( hisNum == 0 && k == 0){
                        str_his = "<tr class='throw'info='"+ JSON.stringify(itemInfo) +"'>" +
                            "<td><div>"+ no + "</div><div class='hd'>"+ JSON.stringify(itemInfo) +"</div></td>" +
                            "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
                            "<td>"+ data_history[his]["amount"] +"</td>" +
                            "<td>"+ formatTime(data_history[his]["delivery_date"]) +"</td>" +
                            "<td>— —</td><td>— —</td><td>— —</td><td>——</td><td>— —</td><td>— —</td><td>——</td>" +
                            "</tr>" ;
                    }else{
                        str_his = "<tr class='throw'info='"+ JSON.stringify(itemInfo) +"'>" +
                            "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                            "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
                            "<td>"+ data_history[his]["amount"] +"</td>" +
                            "<td>"+ formatTime(data_history[his]["delivery_date"]) +"</td>" +
                            "<td>— —</td><td>— —</td><td>— —</td><td>——</td><td>— —</td><td>— —</td><td>——</td>" +
                            "</tr>" ;
                    }
                    hisNum = his + 1  ;
                    tbl.append( str_his );
                }
            }
        }
        // 处理这一行数据
        if( !data_2 && (k == (data_1.length - 1)) ){ // 最后一次处理 ，还没评审
            str += "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" ;
            if( k == 0){ str += "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" ;  }
            else{ str += "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" ; }
        }else if( k == 0 ) {
            str += "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" +
                "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" ;
        }else{
            str += "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" +
                "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'> </td>" ;
        }

        str += "<td>"+ (data_1[k]["itemAmount"] || data_1[k]["amount"] ) +"</td>" +
            "<td>"+ formatTime( data_1[k]["delivery_date"] || data_1[k]["item_delivery_date"] ) +"</td>" ;

        if(data_1[k]["surplus_date"]){  // 评审 否
            str += "<td>否</td><td>"+ data_1[k]["scheduled_amount"] +"</td><td>"+ formatTime( data_1[k]["surplus_date"] ) +"</td>"
        }else{  //  评审 是
            if(data_1[k]["review_date"]){
                str += "<td>是</td><td>— —</td><td>— —</td>" ;
            }else{
                str += "<td>— —</td><td>— —</td><td>— —</td>" ;
            }
        }
        str +=  "<td>"+ formatTime(data_1[k]["review_date"]) +"</td>" +
            "<td>"+ (data_1[k]["reviewer_name"] || "") +"</td>" ;
        if( data_2 ){ // 这一条处理过，显示处理信息
            str+= "<td>"+ formatTime( data_2["create_date"] ) +"</td>" +
                "<td>"+ data_2["reviewer_name"] +"</td>" +
                "</tr>" ;
        }else{
            str+= "<td>— —</td>" +
                "<td>— —</td>" +
                "</tr>" ;
        }
        tbl.append( str );
    }
    if( data_history && hisNum != data_history.length  ){
        for( var g = hisNum ; g < data_history.length ; g++ ){
            str_his = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
                "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
                "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
                "<td>"+ data_history[g]["amount"] +"</td>" +
                "<td>"+ formatTime(data_history[g]["delivery_date"]) +"</td>" +
                "<td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
                "</tr>" ;
            tbl.append( str_his );
        }
    }

    // 判断是不是刚修改完
    var hisTime = 0 , reviewTime = 0 ; //
    var data_1_len = data_1.length ;
    if(data_history && data_history.length > 0){ hisTime = data_history[data_history.length -1]["update_date"] ;  }
    if(data_1 && data_1_len > 0){ reviewTime = data_1[data_1_len -1]["create_date"] ;  }
    if(hisTime > reviewTime) { // 刚修改完
        var strNew = "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
            "<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
            "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
            "<td>"+ data_1[0]["amount"] +"</td>" +
            "<td>"+ formatTime(data_1[0]["delivery_date"]) +"</td>" +
            "<td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
            "</tr>" ;
        tbl.append( strNew );
    }

    // 该划线的都划线
    var newTr = [] ;
    var kids = 0 ; // 标记最后一个修改记录的索引号
    tbl.children("tr").each(function(){
        var thisNo = $(this).children("td:eq(0)").children("div:eq(0)").html() ;
        if( thisNo == no ){
            newTr.push( $(this) ) ;
            var klass = $(this).attr("class") ;
            if( klass == "throw"){ kids = newTr.length - 1 ;  $(this).attr("class" , "hd") ; }
        }
    }) ;
    for(var e = 0 ; e < newTr.length ; e++ ){
        if( kids > e ){
            newTr[e].attr("class" ,"throw") ;
        }
    }

}

// Creator：侯杏哲 2017-06-03 解决动态时间控件显示异常的问题
function tyLay( i ){
    laydate.render({elem: "#goods" + i});
}
// Creator:侯杏哲 2017-06-03 切换显示评审-商品列表中“是”-“否” 的切换
function togglePower( _this ){
    var val = _this.attr("value") ;
    _this.parent().siblings("input").val( val ) ;
    if(val == "1"){
        _this.attr("class" , "ty-btn ty-btn-active-green").siblings().attr("class" , "ty-btn") ;
        _this.parent().parent().next().html("— —").next().html("— —");
    }else  {
        var no = _this.parent().parent().siblings(":eq(0)").html() ;
        var i = Number(no) - 1 ;
        _this.attr("class" , "ty-btn ty-btn-active-red ").siblings().attr("class" , "ty-btn") ;
        _this.parent().parent().next().html("<input type='text' class='ty-inputText'>")
            .next().html("<input readonly type='text' class='ty-inputText laydate-icon' readonly id='goods"+ i +"'>") ;
        laydate.render({elem: "#goods" + i});
    }
}

// ========================  筛选功能 ==================================
var role = { "radio":0 , "check":[]   } ;
/* Creator : 侯杏哲 2017-07-25 初始化筛选框 */
function setRoleInit( objStr ){
    $("#"+ objStr +">div:eq(1)").children().each(function(){ // 单选框
        if( $(this).index() == 0){
            $(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1") ;
        }else{
            $(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
        }
    }) ;
    $("#"+ objStr +">div:eq(2)").children().each(function(){ // 复选框
        $(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1");
    }) ;
    role = { "radio":1 , "check":["5" , "6" , "7" , "8" , "9" , "10" , "11" ] } ;
}
/* Creator : 侯杏哲 2017-07-25 初始化筛选框  */
function setRoleBtn( dropdownBtnObj ){
    dropdownToggle(dropdownBtnObj) ;
    // 根据当前权限设置默认值
    var radio = role["radio"] ;
    var check = role["check"] ;
    dropdownBtnObj.siblings(".ty-dropdownCon").children(":eq(1)").children().each(function(){
        var code = $(this).children("i").attr("code") ;
        if(code == radio){
            $(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1") ;
        }else{
            $(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
        }
    }) ;
    dropdownBtnObj.siblings(".ty-dropdownCon").children(":eq(2)").children().each(function(){
        var code = $(this).children("i").attr("code") ;
        var isIn = $.inArray(code, check);
        if(isIn == -1 ){ // 不在数组中
            $(this).children("i").attr("class" , "fa fa-square-o").attr("isSet" , "0") ;
        }else{
            $(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1") ;
        }
    }) ;

}
/* Creator : 侯杏哲 2017-07-25 筛选按钮  - 设置要显示的数据  */
function setRole( tbodyObj , dropdownBtnObj , roleCon ){
    var radio = 0 ; // 单选值
    $("#"+ roleCon +">div:eq(1)").children().each(function(){
        var isset = $(this).children("i").attr("isSet") ;
        if(isset == 1){ radio = $(this).children("i").attr("code") ; 		}
    });
    var check = [] ; // 复选值
    var llk = $("#roleCon div:eq(2)").children() ;
    $("#"+ roleCon +">div:eq(2)").children().each(function(){
        var isset = $(this).children("i").attr("isSet") ;
        if(isset == 1){  var code = $(this).children("i").attr("code") ;  check.push( code ) ; 		}
    });
    role = { "radio":radio , "check":check   } ;
    var codeName = chargeCode( radio ) ;
    tbodyObj.siblings("thead").children("tr").children(":eq(1)").html( codeName );
    tbodyObj.children().each(function(){
        var orcalO =  $(this).children(":eq(1)") ;
        var oralVal = $(this).children(":eq(1)").html() ;
        if($.trim(oralVal) !=""){
            var info = $(this).attr("info");
            info = info && JSON.parse(info) ;
            $(this).children(":eq(1)").html( chargeCodeVal(radio , info)  );
        }
    });
    dropdownToggle( dropdownBtnObj ) ;
}
/* Creator : 侯杏哲 2017-07-25 筛选 - 取消设置 */
function cancelSert( dropdownBtnObj ){
    dropdownToggle( dropdownBtnObj ) ;
}
/* Creator : 侯杏哲 2017-07-25 将代号翻译成明文*/
function chargeCode( code){
    switch(code){
        case "1" : return "外部图号";  break ;
        case "2" : return "外部名称";  break ;
        case "3" : return "内部图号";  break ;
        case "4" : return "内部名称";  break ;
        case "5" : return "外部图号";  break ;
        case "6" : return "外部名称";  break ;
        case "7" : return "内部图号";  break ;
        case "8" : return "内部名称";  break ;
        case "9" : return "单位";  break ;
        case "10" : return "含税单价";  break ;
        case "11" : return "备注";  break ;
        default: return "" ;
    }
}
/* Creator : 侯杏哲 2017-07-25 根据code 返回对应的键值  */
function chargeCodeVal( code , info){
    switch(code){
        case "1" : return info["slOuterSn"]|| "" ; break ;
        case "2" : return info["slOutterSnName"] || "" ; break ;
        case "3" : return info["slInnerSn"] || ""; break ;
        case "4" : return info["slInnerSnName"] || ""; break ;
        case "5" : return info["slOuterSn"] || "" ; break ;
        case "6" : return info["slOutterSnName"] || "" ; break ;
        case "7" : return info["slInnerSn"] || "" ; break ;
        case "8" : return info["slInnerSnName"] || "" ; break ;
        case "9" : return info["slUnit"] || "" ; break ;
        case "10" : return info["slUnitPrice"] || "" ; break ;
        case "11" : return info["memo"] || "" ; break ;
        default : return "" ; break ;
    }
}
// Creator : 侯杏哲 2017-07-25 筛选 - 单选按钮、复选按钮 的切换
$("div.orderItemTiny").on( "click", function(){
    var klass = $(this).children("i").attr("class") ;
    switch( klass ){
        case "fa fa-circle-o":
            $(this).siblings().each(function(){
                $(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
            }) ;
            $(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1");
            break ;
        case "fa fa-dot-circle-o":
            break ;
        case "fa fa-check-square-o":
            $(this).children("i").attr("class" , "fa fa-square-o").attr("isSet" , "0");
            break ;
        case "fa fa-square-o":
            $(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1");
            break ;
        default:
            break ;
    }
});
// 筛选数据的显示
function showMess( _this ){
    var info = _this.parent().attr("info") ;
    info = JSON.parse(info);
    var offsetObj = _this.offset();
    var leftObj =offsetObj.left;
    var topObj = offsetObj.top ;
    $("#tipCon").animate({ left: leftObj+ "px" , top:topObj+"px"  }, 10).show();
    var check = role["check"];
    var str = "" ;
    for(var k = 5 ; k <=11 ; k++ ){
        var code = String(k) ;
        var isIn = $.inArray(code, check);
        if(isIn != -1){
            var codeName = chargeCode( code ) ;
            var codeVal = chargeCodeVal( code , info ) ;
            str += "<span class='tipitemCon'>"+ codeName +"："+ codeVal +"</span>" ;
        }
    }
    $("#tipitem").html(str);
}
function hideMess( _this ){
    $("#tipCon").hide().offset({ left:0 , top:0 });
}
// ====================== 筛选功能 END ======================================
//creator:lyt 2023/8/30 9:40
function testQuantity(obj, type){
    let phase = $("#phaseNum").html();
    if (obj.val() !== "") {
        let val = Number(obj.val());
        let info = obj.parents("tr").find(".hd").html();
        info = JSON.parse(info);
        let amount = 0;
        if (phase === "2" && type === 4) {
            amount = info.partialShipment.newAmount;
        } else {
            amount = info.amount
        }
        if (val > Number(amount)) {
            obj.val("");
            layer.msg("能按时到货的数量不能大于订购数量！<br/>知道了");
        } else if (val >= 0 && val < Number(amount)){
            obj.parent("td").next().children("input").prop("disabled", false);
        } else {
            obj.parent("td").next().children("input").val("").prop("disabled", true);
        }
    }
    countOkNum();
}
function countOkNum (){
    let obj = ``,ok = 0,count = 0;
    if($(".goReviewList0").is(":visible")){
        obj = $(".goReviewList0");
    } else {
        obj = $(".goReviewList1");
    }
    obj.find("tbody tr.reviewTr").each(function (){
        let amount = $(this).find("input").eq(0).val();
        let date = $(this).find("input").eq(1).prop("disabled");
        if (amount !== "") count++;
        if (amount !== "" && date) {
            ok++;
        }
    })
    $("#sumNum").html(count);
    $("#okNum").html(ok);
}
//creator:lyt 2023/8/30 10:11
let goodObj = null;
function deliveryDateCon(obj, phase){
    goodObj = obj;
    let isScheduled = -1;
    let info = obj.parents("tr").find(".hd").html();
    info = JSON.parse(info);
    let icon = 0;
    let reviewsHistories = info.reviews; // 销售修改订单前数据
    let minDate = new Date(info.delivery_date);
    let trObj = obj.parents("tr");
    let aVal = trObj.find("input").eq(0).val();
    let dVal = trObj.find("input").eq(1).val();
    $(".modHistory").hide();
    $(".unableArrive").hide();
    $(".itemCn").removeClass("redWarning");
    $("#deliveryDate").data("phase", phase);
    $("#deliveryDate input").val("");
    $("#deliveryDate select").val("");
    $("#deliveryDate .lenTip").html("0 / 30");
    $(".modHistoryMsg").hide().html("");
    if (reviewsHistories.length > 0) icon = reviewsHistories.length - 1;
    $("#deliveryDate .itemCon").each(function (){
        let name = $(this).data("name");
        if (name === 'delivery_date') {
            if(phase === 2 && info.progress === "2" && new Date(info.delivery_date).format("yyyy-MM-dd") !== reviewsHistories[icon].oldDeliveryDate) {
                $(this).parents(".itemCn").addClass("redWarning").find(".modHistory").show();
                $(this).html(reviewsHistories[icon].oldDeliveryDate +'/'+new Date(info[name]).format("yyyy-MM-dd"));
            } else {
                $(this).html(new Date(info[name]).format("yyyy-MM-dd"));
            }
        } else if (name === 'amount') {
            if(phase === 2 && info.progress === "2" && info[name] !== reviewsHistories[icon]["oldAmount"]) {
                $(this).children().eq(0).html(reviewsHistories[icon]["oldAmount"] +'/'+ info[name]);
                $(this).children().eq(1).html(handleNull(info["yet"]));
                $(this).parents(".itemCn").addClass("redWarning").find(".modHistory").show();
            } else {
                $(this).children().eq(0).html(info[name]);
            }
        } else {
            if(phase === 2 && name === "delivery_address" && info.progress === "2" && info[name] !== reviewsHistories[icon]["oldAddress"]) {
                $(this).parents(".itemCn").addClass("redWarning").find(".modHistory").show();
                $(this).html(reviewsHistories[icon]["oldAddress"] +'/'+ info[name]);
            } else {
                $(this).html(info[name]);
            }
        }
    });
    if (aVal && aVal !== "") { //有未能按时到货的数量
        let initAmount = phase === 2 && info.progress === '4' ?info.partialShipment.newAmount : info.amount;
        if (Number(aVal) < Number(initAmount)) {
            isScheduled = 0;
            $(".unableArrive").show();
            $(".unableArrive .scheduledAmount").val(aVal);
            $(".unableArrive .surplusDate").val(dVal);
            $(".unableArrive .memo").val(info.memo || "");
            $(".unableArrive .lenTip").html((info.memo || "").length + " / 30");
        } else {
            isScheduled = 1;
        }
        $(".arriveState").val(isScheduled);
    }
    if(phase === 2){
        let msg = ``;
        if (info.progress === "2") {
            let tip = [];
            if(reviewsHistories[icon].amount !== info.amount) tip.push(`订购数量`);
            if(reviewsHistories[icon].delivery_date !== info.delivery_date) tip.push(`要求到货日期`);
            if(reviewsHistories[icon].delivery_address !== info.delivery_address) tip.push(`收货地点`);
            msg = `本商品${tip.join("、")}已被修改，请确认能否按时到货`;
        }
        if (info.progress === "3") msg = `本商品为新增，请确认能否按时到货`;
        if (info.progress === "4") msg = `本商品需分批发货，本页面展示为某次的分批发货，请确认能否按时到货`;
        $(".modHistoryMsg").show().html(msg);
    }
    bounce.show($("#deliveryDate"));
    surplusDate.config.min = {
        year: minDate.getFullYear(),
        month:minDate.getMonth(),//关键
        date:minDate.getDate() + 1,
        hours:minDate.getHours(),
        minutes:minDate.getMinutes(),
        seconds:minDate.getSeconds()
    };

}
function deliveryDateConfirm(){
    let val = $(".arriveState").val();
    let info = goodObj.siblings(".hd").html();
    info = JSON.parse(info);
    let phase = Number($("#deliveryDate").data("phase"));
    if (val !== "") {
        info.orders = chargeObj["ordID"];
        info.isScheduled = val;
        if (val === '1') {
            if(phase === 2 && info.progress === '1') {
                goodObj.parents("tr").children().eq(7).html(info.amount);
                goodObj.parents("tr").children().eq(8).html("");
            } else {
                goodObj.parents("tr").find("input").eq(0).val(info.progress && info.progress === '4' && phase===2 ? info.partialShipment.newAmount : info.amount);
                goodObj.parents("tr").find("input").eq(1).val("").prop("disabled", true);
            }
        } else if (val === '0') {
            let scheduledAmount = $(".unableArrive .scheduledAmount").val();
            let surplusDate = $(".unableArrive .surplusDate").val();
            let limit = info.progress && info.progress === '4' && phase===2 ? info.partialShipment.newAmount : info.amount;
            if (scheduledAmount > Number(limit)) {
                layer.msg("能按时到货的数量不能大于订购数量！<br/>知道了");
                return false;
            } else if (scheduledAmount < Number(limit)){
                info.memo = $(".unableArrive .memo").val();
                if (phase === 2 && info.progress === '1') {
                    goodObj.parents("tr").children().eq(7).html(scheduledAmount);
                    goodObj.parents("tr").children().eq(8).html(surplusDate);
                } else {
                    goodObj.parents("tr").find("input").eq(0).val(scheduledAmount);
                    goodObj.parents("tr").find("input").eq(1).val(surplusDate).prop("disabled", false);
                }
                goodObj.siblings(".hd").html(JSON.stringify(info));
            } else {
                layer.msg("能按时到货的数量录入错误！<br/>知道了");
                return false;
            }
        }
        bounce.cancel();
    }
    countOkNum();
}
function arriveState(obj){
    let val = obj.val();
    if (val === '0') {
        $(".unableArrive").show();
    } else {
        $(".unableArrive").hide();
    }
}
// create : hxz 2021-1-34 显示主页面
function showMainCon(num){
    //$("#showMainConNum").val(num);
    $(".mainCon").hide();$(".mainCon" + num).show();
}
// create：hxz 2021-1-21 字数超出限制
function setWordsNum(thisObj , maxLen){
    thisObj.siblings("div").find(".lenTip").html( thisObj.val().length + " / " + maxLen);
}
var surplusDate = laydate.render({elem: '#surplusDate'});



























