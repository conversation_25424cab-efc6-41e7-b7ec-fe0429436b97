/**
 * Created by Administrator on 2017/8/14 0014.
 */
$(function () {
    //下拉框同意默认样式
    $.fn.select2.defaults.set("theme", "default");

    //商品代号下拉框选中后绑定的事件（获取其他信息）
    $('#outerSn').on('select2:select', function (e) {
        var outerSn = e.params.data.text;   //获取选中文字
        if(outerSn !== '请选择'){
            getProductDetail(outerSn);          //获取商品其他信息并赋值
        }
    });

    //状态切换
    $(".ty-secondTab li").on("click",function () {
        var index = $(this).index();
        $(this).siblings("li").removeClass("ty-active");
        $(this).addClass("ty-active");
        getStorageApplyList(1,15,parseInt(index+1));
        $(".tblContainer").hide().eq(index).show();
    });

    //默认选中待处理
    $(".ty-secondTab li").eq(0).click();
});

/* creator：张旭博，2017-08-22 16:03:05，获取入库申请列表 */
function getStorageApplyList(currPage,pageSize,state) {
    $.ajax({
        url:"../mtStock/addMtStockIndex.do" ,
        data:{
            "pageSize":pageSize,// 每页长度
            "currPage":currPage,// 当前页
            "flag":0,           // 0-入库申请 1-入库检验列表
            "state":state       // 1-待处理 2-待入库 3-已入库 4-已驳回
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){

            //设置分页
            var totalPage = data["totalPage"]; // 总页数
            var currPage = data["currPage"];   // 当前页
            $("#ye_projectManage").html("");
            var jsonStr = JSON.stringify({"state":state}) ;
            setPage( $("#ye_mtStorkIndex") , currPage ,  totalPage , "mtStorkIndex" ,jsonStr );


            var index = $(".ty-secondTab li").index($(".ty-secondTab .ty-active")); //获取状态下标 （0-待处理 1-待入库 2-已入库 3-已驳回）
            var mtMaterialApply = data["mtMaterialApply"];                          //获取列表json
            var storageApplyListStr = "";                                           //承载列表字符串

            if(mtMaterialApply !== undefined) {
                //循环遍历json
                for (var i = 0; i < mtMaterialApply.length; i++) {
                    //待处理
                    if (index === 0) {
                        storageApplyListStr +=  '<tr id="' + mtMaterialApply[i].id + '">' +
                                                    '<td>' + (i + 1) + '</td>' +
                                                    '<td>' + mtMaterialApply[i].applicantName + '</td>' +
                                                    '<td>' + ( new Date(mtMaterialApply[i].applyDate).format("yyyy-MM-dd") )  + '</td>' +
                                                    '<td><span class="ty-color-blue" onclick="seeStorageApplyBtn($(this))">查看</span></td>' +
                                                '</tr>';

                    //待入库、已入库、已驳回（结构类似）
                    } else {
                        var approcessList = mtMaterialApply[i].approcessList;
                        var processDetailStr = getProcessStr(approcessList);
                        //已入库数据中有实际数量，其他不存在
                        if(index === 2){
                            var inFact = '<td>' + mtMaterialApply[i].inFact + '</td>';
                        }else{
                            var inFact = '';
                        }
                        storageApplyListStr +=  '<tr id="' + mtMaterialApply[i].id + '">' +
                                                    '<td>' + (i + 1) + '</td>' +                                            //序号
                                                    '<td>' + ( new Date( mtMaterialApply[i].createDate  ).format('yyyy-MM-dd') )  + '</td>' +     //申请时间
                                                    '<td>' + mtMaterialApply[i].createName + '</td>' +                      //申请时间
                                                    '<td>' + mtMaterialApply[i].outerSn + '</td>' +                         //商品代号
                                                    '<td>' + mtMaterialApply[i].outerName + '</td>' +                       //商品名称
                                                    '<td>' + mtMaterialApply[i].innerSn + '</td>' +                         //产品图号
                                                    '<td>' + mtMaterialApply[i].innerSnName + '</td>' +                     //产品名称
                                                    '<td>' + mtMaterialApply[i].unit + '</td>' +                            //单位
                                                    '<td>' + mtMaterialApply[i].inPlan + '</td>' +                          //申请数量
                                                    inFact+                                                                 //实际数量
                                                    '<td>' + ( new Date(mtMaterialApply[i].manufactureDate).format('yyyy-MM-dd') )  + '</td>' +//生产日期
                                                    '<td>' + ( new Date(mtMaterialApply[i].invalidDate).format('yyyy-MM-dd') )  + '</td>' + //产品到期日
                                                    processDetailStr +                                                      //入库流程
                                                '</tr>';
                    }
                }

                //遍历结束，将字符串写进对应表格中
                $(".tblContainer").eq(index).find("tbody").html(storageApplyListStr);
            }else{
                $(".tblContainer").eq(index).find("tbody").html("");
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-08-22 16:02:50，入库申请-按钮 */
function storageApplyBtn(){
    //清空申请数据
    $("#storageApply tbody").html("");
    //设置超管没有操作权限
    // if(hasAuthority(0)){
    //     layer.msg("您没有此权限！")
    //     return false;
    // }
    bounce.show($("#storageApply"));
    $('body').everyTime('0.5s','stockApply',function(){
        var i = 0;
        if($("#storageApply #stockList").html() === ""){
            i++;
        }
        if(i === 0){
            $("#stockApply").removeAttr("disabled")
        }else{
            $("#stockApply").attr("disabled","disabled")
        }
    });
}

/* creator：张旭博，2017-08-22 16:06:05，新增商品-按钮 */
function addGoodsBtn() {
    //设置超管没有操作权限
    // if(hasAuthority(0)){
    //     layer.msg("您没有此权限！")
    //     return false;
    // }
    bounce_Fixed.show($("#addGoods"));
    $("#addGoods #outerSn").html("<option></option>");
    $("#addGoods input").val("");
    getOuterSn();
    $('body').everyTime('0.5s','addGoods',function(){
        var i = 0;
        if($("#addGoods #outerSn").val() === "" ||$("#addGoods #inPlan").val() === ""){
            i++;
        }
        if(i === 0){
            $("#addGoodsBtn").removeAttr("disabled")
        }else{
            $("#addGoodsBtn").attr("disabled","disabled")
        }
    });
}

/* creator：张旭博，2017-08-22 16:06:50，新增商品-确定（未调接口，js操作） */
function sureAddGoods() {

    var outerId = $("#addGoods #outerSn").val();

    var outerSn     =   $.trim($("#addGoods #outerSn option:selected").text());
    var outerName   =   $.trim($("#addGoods #outerName").val());
    var innerSn     =   $.trim($("#addGoods #innerSn").val());
    var innerName   =   $.trim($("#addGoods #innerName").val());
    var unit        =   $.trim($("#addGoods #unit").val());
    var inPlan      =   $.trim($("#addGoods #inPlan").val());
    var produceDate =   $.trim($("#addGoods #produceDate").val());
    var arriveDate  =   $.trim($("#addGoods #arriveDate").val());
    var goodNo      =   $("#storageApply tbody tr").length+1;

    if (Number(inPlan) === 0) {
        layer.msg("数量不能为0！")
        return false
    }


    var goodStr     =   '<tr id="'+outerId+'">' +
                            '<td>'+goodNo+'</td>'+
                            '<td>'+outerSn+'</td>'+
                            '<td>'+outerName+'</td>'+
                            '<td>'+innerSn+'</td>'+
                            '<td>'+innerName+'</td>'+
                            '<td>'+unit+'</td>'+
                            '<td class="inPlan">'+Number(inPlan)+'</td>'+
                            '<td class="manufactureDate">'+produceDate+'</td>'+
                            '<td class="invalidDate">'+arriveDate+'</td>'+
                            '<td>' +
                                '<span class="ty-color-red" onclick="deleteGood($(this))">删除</span>'+
                            '</td>'+
                        '</tr>';
    $("#storageApply .goodlist tbody").append(goodStr);
    bounce_Fixed.cancel();
}

/* creator：张旭博，2017-08-22 16:07:13，入库申请-查看按钮 */
function seeStorageApplyBtn(selector) {
    bounce.show($("#seeStorageApply"));
    var ID = selector.parent().parent().attr("id");
    $.ajax({
        url:"../mtStock/getMtStockDetail.do" ,
        data:{
            "ID":ID
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            loading.close() ;
            var mtStockList = data["mtStockList"];
            var storageApplyDetailStr = "";
            for(var i = 0 ;i < mtStockList.length ; i++){
                storageApplyDetailStr +=    '<tr>' +
                                                '<td>'+(i+1)+'</td>'+                           //序号
                                                '<td>'+ ( new Date(mtStockList[i].createDate ).format("yyyy-MM-dd") )   +'</td>'+    //申请时间
                                                '<td>'+mtStockList[i].createName+'</td>'+    //申请人
                                                '<td>'+mtStockList[i].outerSn+'</td>'+    //商品代号
                                                '<td>'+mtStockList[i].outerName+'</td>'+    //商品名称
                                                '<td>'+mtStockList[i].innerSn+'</td>'+    //产品图号
                                                '<td>'+mtStockList[i].innerSnName+'</td>'+    //产品名称
                                                '<td>'+mtStockList[i].unit+'</td>'+    //单位
                                                '<td>'+mtStockList[i].inPlan+'</td>'+    //数量
                                                '<td>'+ ( new Date(mtStockList[i].manufactureDate ).format("yyyy-MM-dd") )  +'</td>'+    //生产日期
                                                '<td>'+ ( new Date( mtStockList[i]["invalidDate"]).format("yyyy-MM-dd") )   +'</td>'+    //产品到期日
                                            '</tr>';
            }
            $("#seeStorageApply tbody").html(storageApplyDetailStr);
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-08-23 10:10:24，删除商品 */
function deleteGood(selector) {
    var thisTr = selector.parent().parent(); //本行商品明细
    thisTr.nextAll().each(function () {
        var goodNo = $(this).children().eq(0).html();
        $(this).children().eq(0).html(goodNo-1)
    });
    thisTr.remove();
}

/* creator：张旭博，2017-08-23 10:27:07，入库申请-提交 */
function sureStockApply() {
    var save_pdProduct = [];
    $("#storageApply tbody tr").each(function () {
        var id= $(this).attr("id");
        var inPlan = $(this).children(".inPlan").html();
        var manufactureDate = $(this).children(".manufactureDate").html();
        var invalidDate = $(this).children(".invalidDate").html();
        save_pdProduct.push({
            "id":id,
            "inPlan":inPlan,
            "manufactureDate":manufactureDate,
            "invalidDate":invalidDate
        })
    });
    $.ajax({
        url:"../mtStock/addMtStock.do" ,
        data:{
            "save_pdProduct":JSON.stringify(save_pdProduct)
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            loading.close() ;
            bounce.cancel();
            var status = data["status"];
            if(status === undefined){
                $("#tip #tipMs").html("未知的返回状态!") ;
                bounce.show($("#tip")) ;
            }else if(status === 1){
                bounce.cancel();
                layer.msg("入库申请已提交");
            }else{
                $("#tip #tipMs").html("入库申请提交失败") ;
                bounce.show($("#tip")) ;
            }
            $(".ty-secondTab li.ty-active").click();

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){   }
    }) ;
}

//----------------------------------------辅助方法----------------------------------------//

/* creator：张旭博，2017-08-22 16:08:03，获取商品代号 */
function getOuterSn() {
    $.ajax({
        url:"../mtStock/getOuterSnLike.do" ,
        data:{} ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            loading.close() ;
            var pCPOS = data["pdCustomerProductOuterSn"];
            var options = [{
                "id": '0',
                "text": '请选择'
            }];
            for (var i = 0, len = pCPOS.length; i < len; i++) {
                var option = {
                    "id": pCPOS[i]["id"],
                    "text": pCPOS[i]["outerSn"]
                };
                options.push(option);
            }
            $("#outerSn").select2({
                placeholder: "请选择商品代号",
                data:options
            })

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){   }
    }) ;
}

function getProductDetail(outerSn) {
    $.ajax({
        url:"../mtStock/getOuterSnLike.do" ,
        data:{
            "outerSn":outerSn
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            loading.close() ;
            var pCPOS = data["pdCustomerProductOuterSn"];
            var cInnerSn = pCPOS[0].cInnerSn;
            var cInnerSnName = pCPOS[0].cInnerSnName;
            var outerName = pCPOS[0].outerName;
            var unit = pCPOS[0].unit;
            $("#addGoods #outerName").val(outerName);
            $("#addGoods #innerSn").val(cInnerSn);
            $("#addGoods #innerName").val(cInnerSnName);
            $("#addGoods #unit").val(unit);


        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){   }
    }) ;
}

/* creator：张旭博，2017-08-29 10:25:20，获取审批流程字符串（<td>...</td>） */
function getProcessStr(approcessList) {

    //每一条审批流程组成的字符串
    var processStr = "";
    for (var k = 0; k < approcessList.length; k++) {
        var approveStatus = approcessList[k].approveStatus;
        if(Number(approveStatus) === 4){
            processStr +=   '<div class="infoList ty-process-item ">' +
                                '<p><i class="dot-no"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                                '<p>' + (new Date(approcessList[k].createDate ).format("yyyy-MM-dd") ) + '</p>' +
                                '<p>驳回理由 ： ' + approcessList[k].reason + '</p>' +
                            '</div>';
        }else{
            processStr +=   '<div class="infoList ty-process-item ">' +
                                '<p><i class="dot"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                                '<p>' + (new Date(approcessList[k].createDate).format("yyyy-MM-dd") ) + '</p>' +
                            '</div>';
        }
    }

    //审批流程字符串
    var processDetailStr  = '<td class="hoverDetail"><span>查看入库流程</span> '+
                                '<div style="position: relative;">' +
                                    '<div class="hoverDetailCon">' +
                                        '<div class="ty-panel ty-process" >'+
                                            '<div class="infoHead ty-process-ttl"><b class="ty-btn ty-btn-green ty-circle-3">入库流程</b></div>'+
                                            '<div class="conInfo ty-process-container" id="process">'+
                                            processStr+
                                            '</div>'+
                                        '</div>'+
                                    '</div>' +
                                '</div>' +
                            '</td>';    //入库流程
    return processDetailStr;
}

laydate.render({elem: '#produceDate'});
laydate.render({elem: '#arriveDate'});