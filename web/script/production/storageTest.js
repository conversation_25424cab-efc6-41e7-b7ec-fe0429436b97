/**
 * Created by Administrator on 2017/8/14 0014.
 */
// 质量管理--入库检验
//切换显示页面
$(function () {
    $(".ty-secondTab li").on("click",function () {
        var index = $(this).index();
        $(this).siblings("li").removeClass("ty-active");
        $(this).addClass("ty-active");
        $(".tblContainer").hide().eq(index).show();
        if(index === 2){index++}
        getStorageTestList(1,15,parseInt(index+1));
    });
    $("#seeStorageCheck").on("click",".ty-btn-group span",function () {
        if($(this).index() === 0){
            $(this).addClass("ty-btn-active-green");
            $(this).next("span").removeClass("ty-btn-active-red");
            $(this).parent().attr("value",1);
            $(this).parent().parent().next().children("input").prop("disabled",true);
            $(this).parent().parent().next().children("input").val("");
        }else{
            $(this).addClass("ty-btn-active-red");
            $(this).prev("span").removeClass("ty-btn-active-green");
            $(this).parent().attr("value",0);
            $(this).parent().parent().next().children("input").prop("disabled",false);
            $(this).parent().parent().next().children("input").focus();
        }
    })
    $(".ty-secondTab li").eq(0).click();
})
// function panelShow(num, obj){
//     for(var i=1;i<=3;i++){ $(".panel_"+i).hide();}
//     $(".panel_"+num).show();
//     obj.addClass("ty-active").siblings().removeClass("ty-active");
//     $("#curN").html(obj.html());
// }
/* creator：张旭博，2017-08-22 16:03:05，获取入库申请列表 */
function getStorageTestList(currPage,pageSize,state) {
    $.ajax({
        url:"../mtStock/addMtStockIndex.do" ,
        data:{
            "pageSize":pageSize,
            "currPage":currPage,
            "flag":1,
            "state":state
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){

            //设置分页
            var totalPage = data["totalPage"];//总页数
            var currPage = data["currPage"];//当前页
            $("#ye_storageTest").html("");
            var jsonStr = JSON.stringify({"state":state}) ;
            setPage( $("#ye_storageTest") , currPage ,  totalPage , "storageTest" ,jsonStr );

            var index = $(".ty-secondTab li").index($(".ty-secondTab .ty-active"));
            var mtMaterialApply = data["mtMaterialApply"];
            var storageApplyListStr = "";
            switch(index){
                case 0:
                    for(var i = 0 ;i < mtMaterialApply.length ; i++){
                        storageApplyListStr +=  '<tr id="'+mtMaterialApply[i].id+'">' +
                                                    '<td>'+(i+1)+'</td>'+
                                                    '<td>'+mtMaterialApply[i].applicantName+'</td>'+
                                                    '<td>'+ ( new Date(mtMaterialApply[i].applyDate ).format('yyyy-MM-dd')) +'</td>'+
                                                    '<td><span class="ty-color-blue" onclick="seeStorageApplyBtn($(this))">查看</span></td>'+
                                                '</tr>';
                    }
                    break;
                case 1:
                case 2:
                    for(var j = 0 ;j < mtMaterialApply.length ; j++){

                        var approcessList = mtMaterialApply[j].approcessList;
                        var processDetailStr = getProcessStr(approcessList);  //获取入库流程

                        storageApplyListStr +=    '<tr id="'+mtMaterialApply[j].id+'">' +
                                                    '<td>'+(j+1)+'</td>'+                           //序号
                                                    '<td>'+ ( new Date( mtMaterialApply[j].createDate ).format('yyyy-MM-dd'))  +'</td>'+ //申请时间
                                                    '<td>'+mtMaterialApply[j].outerSn+'</td>'+    //商品代号
                                                    '<td>'+mtMaterialApply[j].outerName+'</td>'+    //商品名称
                                                    '<td>'+mtMaterialApply[j].innerSn+'</td>'+    //产品图号
                                                    '<td>'+mtMaterialApply[j].innerSnName+'</td>'+    //产品名称
                                                    '<td>'+mtMaterialApply[j].unit+'</td>'+    //单位
                                                    '<td>'+mtMaterialApply[j].inPlan+'</td>'+    //数量
                                                    '<td>'+ ( new Date( mtMaterialApply[j].manufactureDate ).format('yyyy-MM-dd'))   +'</td>'+  //生产日期
                                                    '<td>'+ ( new Date( mtMaterialApply[j]["invalidDate"] ).format('yyyy-MM-dd'))  +'</td>'+   //产品到期日
                                                    processDetailStr +                                                  //入库流程
                                                    '</tr>';
                    }
            }
            $(".tblContainer").eq(index).find("tbody").html(storageApplyListStr);

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-08-22 16:07:13，入库申请-查看按钮 */
function seeStorageApplyBtn(selector) {
    bounce.show($("#seeStorageCheck"));
    selector.parent().parent().siblings().removeClass("storageApplyItemActive");
    selector.parent().parent().addClass("storageApplyItemActive");

    var ID = selector.parent().parent().attr("id");
    $.ajax({
        url:"../mtStock/getMtStockDetail.do" ,
        data:{
            "ID":ID
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            loading.close() ;
            var mtStockList = data["mtStockList"];
            var storageApplyDetailStr = "";
            for(var i = 0 ;i < mtStockList.length ; i++){
                storageApplyDetailStr +=    '<tr id="'+mtStockList[i].id+'">' +
                                                '<td>'+(i+1)+'</td>'+                           //序号
                                                '<td>'+mtStockList[i].outerSn+'</td>'+    //商品代号
                                                '<td>'+mtStockList[i].outerName+'</td>'+    //商品名称
                                                '<td>'+mtStockList[i].innerSn+'</td>'+    //产品图号
                                                '<td>'+mtStockList[i].innerSnName+'</td>'+    //产品名称
                                                '<td>'+mtStockList[i].unit+'</td>'+    //单位
                                                '<td>'+mtStockList[i].inPlan+'</td>'+    //数量
                                                '<td>'+ ( new Date(mtStockList[i].manufactureDate).format("yyyy-MM-dd") ) +'</td>'+    //生产日期
                                                '<td>'+ ( new Date(mtStockList[i].invalidDate).format("yyyy-MM-dd") ) +'</td>'+    //产品到期日
                                                '<td class="hoverDetail"><span>查看入库流程</span> '+
                                                    '<div style="position: relative;">' +
                                                        '<div class="hoverDetailCon">' +
                                                            '<div class="ty-panel ty-process" >'+
                                                                '<div class="infoHead ty-process-ttl"><span class="ty-btn ty-btn-green">审批流程</span></div>'+
                                                                '<div class="conInfo ty-process-container" id="process">'+
                                                                    '<div class="infoList ty-process-item ">'+
                                                                        '<p><span class="dot"></span>申请人：'+mtStockList[i].createName+' 生产 </p>'+
                                                                        '<p>'+new Date(mtStockList[i].createDate).format("yyyy-MM-dd")+'</p>'+
                                                                    '</div>'+
                                                                '</div>'+
                                                            '</div>'+
                                                        '</div>' +
                                                    '</div>' +
                                                '</td>'+    //入库流程
                                                '<td>' +
                                                    '<div class="ty-btn-group checkIsQualified">' +
                                                        '<span class="ty-btn ty-circle-3">合格</span>' +
                                                        '<span class="ty-btn ty-circle-3">不合格</span>' +
                                                    '</div>' +
                                                '</td>'+    //检验
                                                '<td><input class="ty-inputText" type="text" disabled></td>'+    //驳回理由
                                            '</tr>';
            }
            $("#seeStorageCheck tbody").html(storageApplyDetailStr);
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    }) ;
    $('body').everyTime('0.5s','seeStorageCheck',function(){
        var i = 0;
        $(".ty-btn-group").each(function () {
            if($(this).attr("value") === undefined){
                i++;
            }
        });
        if(i === 0){
            $("#storageCheckBtn").removeAttr("disabled")
        }else{
            $("#storageCheckBtn").attr("disabled","disabled")
        }
    });
}

/* creator：张旭博，2017-09-19 08:45:03，入库申请-确认 */
function sureStorageCheck() {
    var save_qualified = [];
    var query_applyId = [];
    var ID = $(".storageApplyItemActive").attr("id");
    $("#seeStorageCheck .checkIsQualified").each(function () {
        var qualified = $(this).attr("value");
        var reason = $(this).parent().next().children("input").val();
        var mtStockId = $(this).parent().parent().attr("id");

        save_qualified.push({
            "mtStockId":mtStockId,
            "qualified":qualified,
            "reason":reason
        })
    });
    query_applyId.push({
        "ID":ID
    });

    $.ajax({
        url:"../mtStock/mtStockCheckout.do" ,
        data:{
            "save_qualified":JSON.stringify(save_qualified),
            "query_applyId":JSON.stringify(query_applyId)
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            loading.close() ;
            bounce.cancel();
            var status = data["status"];
            if(status === undefined){
                $("#tip #tipMs").html("未知的返回状态!") ;
                bounce.show($("#tip")) ;
            }else if(status === 1){
                layer.msg("入库检验已提交")
            }else{
                $("#tip #tipMs").html("入库检验提交失败") ;
                bounce.show($("#tip")) ;
            }
            $(".ty-secondTab li.ty-active").click();
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    }) ;

}

/* creator：张旭博，2017-08-29 10:25:20，获取审批流程字符串（<td>...</td>） */
function getProcessStr(approcessList) {

    //每一条审批流程组成的字符串
    var processStr = "";
    for (var k = 0; k < approcessList.length; k++) {
        var approveStatus = approcessList[k].approveStatus;
        if(Number(approveStatus) === 4){
            processStr +=   '<div class="infoList ty-process-item ">' +
                                '<p><i class="dot-no"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                                '<p>' +  (new Date(approcessList[k].createDate ).format("yyyy-MM-dd")) + '</p>' +
                                '<p>驳回理由 ： ' + approcessList[k].reason + '</p>' +
                            '</div>';
        }else{
            processStr +=   '<div class="infoList ty-process-item ">' +
                                '<p><i class="dot"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                                '<p>' + (new Date(approcessList[k].createDate ).format("yyyy-MM-dd")) + '</p>' +
                            '</div>';
        }
    }

    //审批流程字符串
    var processDetailStr  = '<td class="hoverDetail"><span>查看入库流程</span> '+
                                '<div style="position: relative;">' +
                                    '<div class="hoverDetailCon">' +
                                        '<div class="ty-panel ty-process" >'+
                                            '<div class="infoHead ty-process-ttl"><b class="ty-btn ty-btn-green ty-circle-3">入库流程</b></div>'+
                                            '<div class="conInfo ty-process-container" id="process">'+
                                            processStr+
                                            '</div>'+
                                        '</div>'+
                                    '</div>' +
                                '</div>' +
                            '</td>';    //入库流程
    return processDetailStr;
}
