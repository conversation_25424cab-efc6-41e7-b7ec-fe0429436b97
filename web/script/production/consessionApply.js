/**
 * Created by Administrator on 2017/8/14 0014.
 */
// ====================== 生产管理-让步申请 ========================//

$(function () {

    $(window).resize(function () {
        var window_width = $(window).width();
        console.log(window_width);
    })

    $("#tab1Bdy").on('mouseover', 'td',function(){
        $(this).attr("title", $(this).text());
    })
    //状态切换
    $(".ty-secondTab li").on("click",function () {
        var index = $(this).index();
        $(this).siblings("li").removeClass("ty-active");
        $(this).addClass("ty-active");
        $(".tblContainer").hide().eq(index).show();
        //获取让步申请列表
        getConcessionApplyList(1,15,parseInt(index));
    });
    //单选按钮事件
    $(".ty-radio").on("click",function () {
        $(this).siblings().removeClass("ty-radioActive");
        $(this).addClass("ty-radioActive");
        $(this).siblings().children("i").attr("class","fa fa-circle-o");
        $(this).children("i").attr("class","fa fa-dot-circle-o");
        $(this).siblings(".hd").val($(this).attr("value"));
        if($(this).attr("value") === "1"){
            $(this).siblings("#productNum").show()
            $(this).siblings("#productNum").children("input").focus();
        }else{
            $(this).siblings("#productNum").hide()
        }
    });
    $(".ty-secondTab li").eq(0).click();
});

/* creator：张旭博，2017-08-22 16:03:05，获取入库申请列表 */
function getConcessionApplyList(currPage,pageSize,state) {
    $.ajax({
        url:"../concession/concessionList.do" ,
        data:{
            "pageSize":pageSize,
            "currPage":currPage,
            "flag":1,
            "state":state
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){

            //设置分页
            var totalPage = data["totalPage"];//总页数
            var currPage = data["currPage"];//当前页
            $("#ye_concessionApply").html("");
            var jsonStr = JSON.stringify({"state":state}) ;
            setPage( $("#ye_concessionApply") , currPage ,  totalPage , "concessionApply" ,jsonStr );

            var index = $(".ty-secondTab li").index($(".ty-secondTab .ty-active"));
            var mtStockList = data["mtStockList"];
            var concessionApplyListStr = "";
            for(var j = 0 ;j < mtStockList.length ; j++){
                var approcessList = mtStockList[j].approcessList;
                var processDetailStr = getProcessStr(approcessList);

                //各种状态列表不同之处
                switch(index){
                    case 0: // 待让步申请---有申请操作；没有申请人、申请时间、申请人、审批流程、实际入库数量
                        var createStr   =   '';
                        var inFact      =   '';
                        var handelStr   =   '<td><span class="ty-color-blue" onclick="concessionApplyBtn($(this))">提出让步申请</span></td>';
                        processDetailStr  =   '';//审批流程置空
                        break;
                    case 1: // 待让步审批---有审批流程和申请时间；没有操作、实际入库数量
                    case 2: // 待入库---有审批流程和申请时间；没有操作、实际入库数量
                    case 4: // 已驳回---有审批流程和申请时间；没有操作、实际入库数量
                        var createStr   =   '<td>'+ (new Date(mtStockList[j].createDate).format("yyyy-MM-dd"))  +'</td>';   //申请时间;
                        var inFact      =   '';
                        var handelStr   =   '';
                        break;
                    case 3: // 已入库---有审批流程、申请时间、申请人和实际入库数量；没有操作
                        var createStr   =   '<td>'+ (new Date(mtStockList[j].createDate).format("yyyy-MM-dd"))  +'</td>'+   //申请时间;
                                            '<td>'+mtStockList[j].createName+'</td>' ;                  //申请人
                        var inFact      =   '<td>'+mtStockList[j].inFact+'</td>';
                        var handelStr   =   '';

                }
                switch(index){
                    case 0: // 待让步申请-- 改了字段
                        var approcessList = mtStockList[j]['approcessList'] || [] ;
                        var lastApprove = approcessList[approcessList.length - 1] ;
                        var reason = (lastApprove && lastApprove.reason) || ''
                        concessionApplyListStr +=   '<tr id="'+mtStockList[j].id+'">' +
                            createStr+                                          //申请信息（申请人和申请时间）
                            '<td>'+mtStockList[j].outerName+'</td>'+            //商品代号
                            '<td>'+mtStockList[j].outerSn+'</td>'+              //商品名称
                            '<td>'+mtStockList[j].innerSn+'</td>'+              //产品图号
                            '<td>'+mtStockList[j].innerSnName+'</td>'+          //产品名称
                            '<td>'+mtStockList[j].unit+'</td>'+                 //单位
                            '<td>'+mtStockList[j].inPlan+'</td>'+               //申请入库数量
                            inFact+                                             //实际入库数量
                            '<td>'+ (new Date(mtStockList[j].manufactureDate).format("yyyy-MM-dd")) +'</td>'+  //生产日期
                            '<td>'+ (new Date(mtStockList[j].invalidDate).format("yyyy-MM-dd")) +'</td>'+   //产品到期日
                            processDetailStr+                                   //入库流程
                            '<td>'+ reason +'</td>'+   // 不合格理由
                            handelStr+                                          //操作
                            '</tr>';
                        break;
                    case 1: // 待让步审批---有审批流程和申请时间；没有操作、实际入库数量
                    case 2: // 待入库---有审批流程和申请时间；没有操作、实际入库数量
                    case 4: // 已驳回---有审批流程和申请时间；没有操作、实际入库数量
                    case 3: // 已入库---有审批流程、申请时间、申请人和实际入库数量；没有操作
                        // 单条列表字符串累加（单独变量为不同之处）
                        concessionApplyListStr +=   '<tr id="'+mtStockList[j].id+'">' +
                            '<td>'+(j+1)+'</td>'+                               //序号
                            createStr+                                          //申请信息（申请人和申请时间）
                            '<td>'+mtStockList[j].outerName+'</td>'+            //商品代号
                            '<td>'+mtStockList[j].outerSn+'</td>'+              //商品名称
                            '<td>'+mtStockList[j].innerSn+'</td>'+              //产品图号
                            '<td>'+mtStockList[j].innerSnName+'</td>'+          //产品名称
                            '<td>'+mtStockList[j].unit+'</td>'+                 //单位
                            '<td>'+mtStockList[j].inPlan+'</td>'+               //申请入库数量
                            inFact+                                             //实际入库数量
                            '<td>'+ (new Date(mtStockList[j].manufactureDate).format("yyyy-MM-dd"))  +'</td>'+  //生产日期
                            '<td>'+ (new Date(mtStockList[j]["invalidDate"]).format("yyyy-MM-dd"))  +'</td>'+   //产品到期日
                            processDetailStr+                                   //入库流程
                            handelStr+                                          //操作
                            '</tr>';

                }

            }
            //插入列表字符串
            $(".tblContainer").eq(index).find("tbody").html(concessionApplyListStr);

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-08-24 21:00:53，申请让步-按钮 */
function concessionApplyBtn(selector) {
    // if(hasAuthority(0)){
    //     layer.msg("您没有此权限！")
    //     return false;
    // }
    bounce.show($("#applyConcession"));
    selector.parent().parent().siblings().removeClass("concessionApplyItemActive");
    selector.parent().parent().addClass("concessionApplyItemActive");
}

/* creator：张旭博，2017-08-24 21:01:24，申请让步-确认 */
function sureConcession() {
    var id = $(".concessionApplyItemActive").attr("id");
    $.ajax({
        url:"../concession/application.do" ,
        data:{
            "id":id   //出库单id
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var status = data["status"];
            if(status === undefined){
                $("#tip #tipMs").html("返回值未定义!") ;
                bounce.show($("#tip")) ;
            }else if(status === 1){
                bounce.cancel();
                layer.msg("让步成功")
            }else if(status ===0){
                $("#tip #tipMs").html("让步失败!") ;
                bounce.show($("#tip")) ;
            }else{
                $("#tip #tipMs").html("返回值不存在!") ;
                bounce.show($("#tip")) ;
            }
            $(".ty-secondTab li.ty-active").click();
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){  loading.close() ;bounce.cancel(); }
    }) ;
}

/* creator：张旭博，2017-08-29 10:25:20，获取审批流程字符串（<td>...</td>） */
function getProcessStr(approcessList) {

    //每一条审批流程组成的字符串
    var processStr = "";
    for (var k = 0; k < approcessList.length; k++) {
        var approveStatus = approcessList[k].approveStatus;
        if(Number(approveStatus) === 4){
            processStr +=   '<div class="infoList ty-process-item ">' +
                '<p><i class="dot-no"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                '<p>' + (new Date(approcessList[k].createDate ).format("yyyy-MM-dd")) + '</p>' +
                '</div>';
        }else{
            processStr +=   '<div class="infoList ty-process-item ">' +
                '<p><i class="dot"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName  + '</p>' +
                '<p>' + (new Date(approcessList[k].createDate ).format("yyyy-MM-dd")) + '</p>' +
                '</div>';
        }
    }

    //审批流程字符串
    var processDetailStr  = '<td class="hoverDetail"><span>查看入库流程</span> '+
                                '<div style="position: relative;">' +
                                    '<div class="hoverDetailCon">' +
                                        '<div class="ty-panel ty-process" >'+
                                            '<div class="infoHead ty-process-ttl"><b class="ty-btn ty-btn-green ty-circle-3">入库流程</b></div>'+
                                            '<div class="conInfo ty-process-container" id="process">'+
                                            processStr+
                                            '</div>'+
                                        '</div>'+
                                    '</div>' +
                                '</div>' +
                            '</td>';    //入库流程
    return processDetailStr;
}