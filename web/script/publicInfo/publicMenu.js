$(function () {

    // 所有树型列表点击事件

    $("#publicTree").on("click",".ty-treeItem",function (){
        let hasChildren = Number($(this).attr('children')) > 0
        let thisLevel = Number($(this).attr("level"))

        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");

        //点击文件夹箭头方向的切换
        if (hasChildren) {
            $(this).find("i").eq(0).toggleClass("fa-angle-down")
            //获取本级与子级文件夹内容
            let pms_childFolder = new Promise(resolve => {
                let url = '../pi/getPiDir.do'
                $.ajax({
                    url: url,
                    success: function (res) {
                        if (res) {
                            resolve(res.data)
                        }
                    }
                })
            })

            // 右侧内容处理
            let treeItemThis = $(this)
            pms_childFolder.then(res => {
                let thisFolder = {}, childFolder = [];
                if (thisLevel === 0 ) {
                    childFolder = res.listFirstFolder
                    thisFolder = {
                        name: treeItemThis.attr("title"),
                        children: childFolder.length,
                        leafs: res.fileNum,
                        folderSize: bytesToSize(res.fileSize),
                        category : 1,
                        createName: '系统',
                        createDate: childFolder[0].createDate,
                        type: treeItemThis.attr("type")
                    }
                }


                // 左侧文件夹填充子级内容
                let level = Number(treeItemThis.attr("level")) + 1
                let childFolderStr = setFolderStr(childFolder, level)
                treeItemThis.next().remove()
                if (treeItemThis.children(".fa-angle-down").length > 0) {
                    treeItemThis.after(childFolderStr)
                }

                // 设置右侧可操作的按钮
                let  handleStr = '<i class="fa fa-plus" type="btn" name="newClass" title="新建子文件夹"></i>'

                // 此文件夹内容赋值
                $(".headPanel .folderName").html(thisFolder.name);
                $(".headPanel .operableBtn").html(handleStr);


                let thisFolderInfoStr = '<tr>' +
                    '<td>' + thisFolder.leafs + '个文件，' + thisFolder.children + '个文件夹' +
                    '<td class="sizePart">'+thisFolder.folderSize+'</td>' +
                    '<td>'+(thisFolder.category == '1' ? '系统' : thisFolder.createName)+'</td>' +
                    '<td>'+(moment(thisFolder.createDate).format("YYYY-MM-DD HH:mm:ss"))+'</td>' +
                    '</tr>'

                $(".nowFolder tbody").html(thisFolderInfoStr)
                $(".nowFolder .sizePart").show()
            })
        } else {
            var thisFolder = JSON.parse($(this).find(".hd").html())
            // 设置右侧可操作的按钮
            let funcName = { changeClass: '修改文件夹名称', folderEditRecord: '文件夹名称修改记录', deleteClass: '删除'}

            let handleStr = ''

            if (!thisFolder.trash && thisFolder.type !==1 && thisFolder.type !== 2) {
                let func = {
                    changeClass: {icon: 'fa-edit'},
                    folderEditRecord: {icon: 'fa-file-text-o'},
                    deleteClass: {icon: 'fa-trash'}
                }

                for (let key in func) {
                    handleStr += '<i class="fa '+func[key].icon+'" type="btn" name="'+key+'" title="'+funcName[key]+'"></i>'
                }
            }


            // 此文件夹内容赋值
            $(".headPanel .folderName").html(thisFolder.name);
            $(".headPanel .operableBtn").html(handleStr);


            let thisFolderInfoStr = '<tr>' +
                '<td>' + thisFolder.leafs + '个文件，0个文件夹' +
                '<td class="sizePart">'+bytesToSize(thisFolder.size)+'</td>' +
                '<td>'+(thisFolder.category == '1' ? '系统' : thisFolder.createName)+'</td>' +
                '<td>'+(moment(thisFolder.createDate).format("YYYY-MM-DD HH:mm:ss"))+'</td>' +
                '</tr>'

            $(".nowFolder tbody").html(thisFolderInfoStr)

            if (thisFolder.type === 2) {
                $(".nowFolder .sizePart").hide()
            } else {
                $(".nowFolder .sizePart").show()
            }
        }


    });

    $(".section_right").on("click", "[type='btn']", function () {
        var name= $(this).attr("name")
        switch (name) {
            case 'newClass':
                newClassBtn()
                break
            case 'changeClass':
                changeClassBtn()
                break
            case 'folderEditRecord':
                folderEditRecordBtn()
                break
            case 'deleteClass':
                deleteClassBtn()
                break
        }
    })

    $("#publicTree .ty-treeItem").eq(0).click()
})

// creator: 张旭博，2021-09-26 11:31:11，获取文件夹树字符串
function setFolderStr(list, level) {
    // 构建文件夹树
    var str = '<ul>'
    for(var i in list){
        str +=
            '<li>' +
            '   <div class="ty-treeItem '+(list[i].isTrash?'trash':'')+'" title="'+ list[i].name +'" id="'+ list[i].id +'" children="'+list[i].children+'" level="'+level+'" parent="'+list[i].parent+'" type="'+list[i].type+'">' +
            (list[i].children > 0?'<i class="fa fa-angle-right"></i>':'<i class="ty-fa"></i>') +
            (list[i].trash?'<i class="fa fa-trash"></i>':'<i class="fa fa-folder"></i>') +
            '       <span>' + list[i]["name"] + '</span>' + '<div class="hd">'+(JSON.stringify(list[i]))+'</>'
            '   </div>' +
            '</li>';

    }
    str += '</ul>'
    return str
}

// creator: 张旭博，2021-09-28 08:33:09，点击新增子级文件夹按钮
function newClassBtn() {
    var folderName = $("#publicTree .ty-treeItemActive span").text();
    var folderId = $("#publicTree .ty-treeItemActive").attr("id");
    $(".recentParentFolder").html(folderName);
    //清空输入框
    $("#havRightUserCon").html("");
    $("#newClass .categoryName").val("");
    bounce.show($('#newClass'));
    $('#newClass').data("folderId", folderId)
}

// creator: 张旭博，2021-09-28 16:51:32，点击新增子级文件夹 确认 按钮
function sureNewClass() {
    var categoryName = $.trim($("#newClass .categoryName").val());
    if(categoryName === ""){
        layer.msg("文件夹名称不能为空！")
    }else{
        $.ajax({
            url: "../pi/addPiDir.do",
            data: {
                name: categoryName
            },
            success: function (res) {
                var data = res.data
                if (data) {
                    var status = data.state
                    if (status === 1) {
                        layer.msg("新增成功")
                        if($(".ty-treeItemActive").children().eq(0).hasClass("ty-fa")){
                            $(".ty-treeItemActive").children().eq(0).attr("class","fa fa-angle-right");
                            $(".ty-treeItemActive").attr("children","1");
                            $(".ty-treeItemActive").click();
                        }else{
                            $(".ty-treeItemActive").click();
                            $(".ty-treeItemActive").click();
                        }
                        bounce.cancel()
                    } else if (status === 2) {
                        bounce.show( $("#tip") ) ; $("#tipMess").html("文件夹下有文件， 无法新增！") ;
                    } else {
                        bounce.show( $("#tip") ) ; $("#tipMess").html("新增失败！") ;
                    }
                } else {
                    bounce.show( $("#tip") ) ; $("#tipMess").html("新增失败！") ;
                }

            }
        })
    }
}

// creator: 张旭博，2021-10-12 15:22:26，点击文件夹名称 修改 按钮
function changeClassBtn() {
    //清空输入框
    var treeActive = $(".ty-colFileTree[data-name='main'] .ty-treeItemActive")
    var title = treeActive.attr("title");
    var id = treeActive.attr("id")
    var parentId = treeActive.closest("ul").prev(".ty-treeItem").attr("id")
    $(".folderNameChange").text(title);
    $("#changeClass .categoryName").val('');
    $("#changeClass .categoryName").attr('placeholder', title);
    $("#changeClass").data("data", {
        id: id,
        parent: parentId
    })
    //显示弹窗
    bounce.show($('#changeClass'));
}

// creator: 张旭博，2021-10-12 15:22:39，点击修改文件夹 确认 按钮
function sureChangeClass() {
    var id = $("#changeClass").data("data").id;
    var parent = $("#changeClass").data("data").parent;
    var categoryName = $.trim($("#changeClass .categoryName").val());
    if(categoryName == ""){
        bounce.show( $("#tip") ) ; $("#tipMess").html( "文件夹名称不能为空" ) ;
    }else{
        $.ajax({
            url: "../pi/upPiDirName.do",
            data: {categoryId: id, name: categoryName  },
            success: function (res) {
                let data = res.data
                var state = data.state
                if (state == 1) {
                    bounce.cancel(); layer.msg("更改成功") ;
                    $("#changeClass .categoryName").val("");
                    $(".ty-treeItemActive span").html(categoryName);
                    $(".ty-treeItemActive").attr("title",categoryName);
                    $(".ty-treeItemActive").click();
                    $(".ty-treeItemActive").click();
                } else if (state == 2) {
                    layer.msg("文件夹名称重复，请更换文件夹名称！")
                } else {
                    bounce.show( $("#tip") ) ; $("#tipMess").html("操作失败！");
                }
            }
        })
    }
}

// creator: 张旭博，2021-10-13 15:30:42，点击修改文件夹记录 按钮
function folderEditRecordBtn() {
    let treeActive = $(".ty-colFileTree[data-name='main'] .ty-treeItemActive")
    let id = treeActive.attr("id")
    var title = treeActive.children("span").text();
    $(".folderNameSee").html(title);
    $.ajax({
        url : "../pi/getPiDirHis.do" ,
        data : { categoryId : id } ,
        success:function ( res ) {
            var success = res["success"] ;
            if(success != 1){
                bounce.show( $("#tip") ) ; $("#tipMess").html( "获取数据失败，请刷新重试！" ) ; return false ;
            }
            var html = '';
            var data = res["data"] ;
            var record = data["list"] ;
            if(record.length > 0){
                var lastIndex = record.length - 1;
                $(".recordTip").html('该名称为第'+ NumberToChinese(record.length) +'次修改后的结果');
                $(".recordEditer").html('修改人： ' + record[lastIndex].updateName + ' . ' + moment(record[lastIndex].updateDate).format("YYYY-MM-DD HH:mm:ss"));
                for(var a=0;a<record.length;a++){
                    if(a==0){
                        html +=
                            '<tr>' +
                            '    <td>原始信息</td>' +
                            '    <td>'+ record[a].name+'</td>' +
                            '    <td>' + record[a].createName + ' . ' + moment(record[a].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>' +
                            '</tr>';
                    }else{
                        html +=
                            '<tr>' +
                            '    <td>第'+ NumberToChinese(record[a].versionNo) +'次修改后</td>' +
                            '    <td>'+ record[a].name+'</td>' +
                            '    <td>' + record[a].updateName + ' . ' + moment(record[a].updateDate).format("YYYY-MM-DD HH:mm:ss") +'</td>' +
                            '</tr>';
                    }
                }
                $(".historyCon tbody").html(html);
                $(".historyCon").show();
            }else{
                var creater = $('.generalFolder td').eq(2).html();
                var createDate = $('.generalFolder td').eq(3).html();
                $(".recordTip").html('该名称未经修改');
                $(".recordEditer").html('创建人： ' + creater + ' . ' + createDate);
                $(".historyCon").hide();
            }
            bounce.show($("#nameEditRecord"));
        }
    });
}

/* creator：张旭博，2017-04-27 08:29:36，点击文件夹名称 删除 按钮 */
function deleteClassBtn(num) {
    let treeActive = $(".ty-colFileTree[data-name='main'] .ty-treeItemActive")
    if (Number(treeActive.attr("children")) > 0) {
        layer.msg("该目录下有文件夹，无法删除")
        return false
    }
    bounce.show($('#deleteClass'));
    let id = treeActive.attr("id")
    $("#deleteClass").data("id", id)
}
/* creator：张旭博，2017-05-04 13:17:56，点击删除文件夹 确认 按钮 */
function sureDeleteClass() {
    var id = $("#deleteClass").data("id");
    $.ajax({
        url: "../pi/delPiDir.do",
        data: { categoryId: id},
        success: function (res) {
            let data = res.data
            let state = data.state
            if (state === 1) {
                layer.msg("操作成功")
                bounce.cancel()
                if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                    location.reload();
                }
                if($(".ty-treeItemActive").parent().parent().children().length<2){
                    $(".ty-treeItemActive").parent().parent().prev().children().eq(0).attr("class","ty-fa");
                    $(".ty-treeItemActive").parent().parent().prev().attr("children",0);
                }
                $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                $(".ty-treeItemActive").click();
            } else if (state === 2) {
                bounce.show( $("#tip") ) ; $("#tipMess").html("该目录已被删除");
            } else if (state === 3) {
                bounce.show( $("#tip") ) ; $("#tipMess").html("该目录下有文件，无法删除");
            } else {
                bounce.show( $("#tip") ) ; $("#tipMess").html("系统错误！");
            }
        }
    })
}

// creator: 李玉婷，2019-07-15 19:43:10，工具方法-数字转化成汉语数字
var chnNumChar = ["零","一","二","三","四","五","六","七","八","九"];
var chnUnitSection = ["","万","亿","万亿","亿亿"];
var chnUnitChar = ["","十","百","千"];

function SectionToChinese(section){
    var strIns = '', chnStr = '';
    var unitPos = 0;
    var zero = true;
    while(section > 0){
        var v = section % 10;
        if(v === 0){
            if(!zero){
                zero = true;
                chnStr = chnNumChar[v] + chnStr;
            }
        }else{
            zero = false;
            strIns = chnNumChar[v];
            strIns += chnUnitChar[unitPos];
            chnStr = strIns + chnStr;
        }
        unitPos++;
        section = Math.floor(section / 10);
    }
    return chnStr;
}

function NumberToChinese(num){
    var unitPos = 0;
    var strIns = '', chnStr = '';
    var needZero = false;

    if(num === 0){
        return chnNumChar[0];
    }

    while(num > 0){
        var section = num % 10000;
        if(needZero){
            chnStr = chnNumChar[0] + chnStr;
        }
        strIns = SectionToChinese(section);
        strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
        chnStr = strIns + chnStr;
        needZero = (section < 1000) && (section > 0);
        num = Math.floor(num / 10000);
        unitPos++;
    }

    return chnStr;
}

function bytesToSize(bytes) {
    if (bytes === 0) return '0 B';
    var k = 1024, // or 1024
        sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
        i = Math.floor(Math.log(bytes) / Math.log(k));

    return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
}