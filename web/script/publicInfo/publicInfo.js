//重构关闭方法，取消选项和定时器。
bounce.oldCancle = bounce.cancel;
bounce.cancel = function () {
    delayLogout.clearDelayLogoutTime();
    if ($("#upload-folder-01").data("kendoUpload")) {
        $("#upload-folder-01").data("kendoUpload").removeAllFiles();

    }
    if ($("#upload-file-01").data("kendoUpload")) {
        $("#upload-file-01").data("kendoUpload").removeAllFiles();
    }
    this.oldCancle();
}
$(function () {
    // updater : 侯杏哲 2017-11-18 每一级目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容）
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        let treeName = $(this).parents(".ty-colFileTree").data("name")
        let hasChildren = Number($(this).attr('children')) > 0
        let thisLevel = Number($(this).attr("level"))

        //添加文件夹选中样式
        $(this).parents(".ty-colFileTree").find(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");

        var categoryId = $(this).data("id") ;
        //点击文件夹箭头方向的切换
        if (hasChildren) {
            $(this).find("i").eq(0).toggleClass("fa-angle-down")
            //获取本级与子级文件夹内容

            let pms_childFolder = new Promise(resolve => {
                let url = '../pi/getPiDir.do'
                let data = thisLevel === 0 ? { }:{ category:categoryId }
                $.ajax({
                    url: url,
                    data: data,
                    success: function (res) {
                        if (res) {
                            resolve(res.data)
                        }
                    }
                })
            })
            // 右侧内容处理
            let treeItemThis = $(this)
            pms_childFolder.then(res => {
                let thisFolder = {}, childFolder = [];
                if (thisLevel === 0 ) {
                    childFolder = res.listFirstFolder
                    if (treeName === 'chooseSaveFolder' || treeName === 'moveTo') {
                        delete childFolder[2]
                        delete childFolder[3]
                    }
                } else {
                    childFolder = res.childFolder
                }
                // 左侧文件夹填充子级内容
                let level = Number(treeItemThis.attr("level")) + 1
                let childFolderStr = setFolderStr(childFolder, level)
                treeItemThis.next().remove()
                if (treeItemThis.children(".fa-angle-down").length > 0) {
                    treeItemThis.after(childFolderStr)
                }
            })
            if (treeName === 'main') {
                // 非末级文件夹不显示文件列表
                $(".section_right").hide()
            }
        } else {
            if (treeName === 'main') {
                // 末级文件夹显示文件列表
                $(".section_right").show()
                getFileList( 1,20, categoryId);
            }
        }
    });

    $(".page_main header").on("click", "[type='btn']", function () {
        let name= $(this).data("name")
        switch (name) {
            // 上传内容
            case 'fileUpload':
                // 初始化表单
                $("#fileUpload").find("input.ty-inputText").val("")
                $("#fileUpload .savePlace").hide().html("")
                bounce.show($("#fileUpload"))
                // 初始化缓存数据
                $("#fileUpload").data("fileInfo", '')
                getApprover()
                // 初始化上传插件
                initKendoFileUpload()
                setEveryTime(bounce, 'fileUpload')
                break;

        }
    })

    $(".ty-container").on("click", ".ty-fileItem [type='btn']", function () {
        if ($(this).attr("disable") === 'true') {
            return false
        }
        let name= $(this).data("name")
        let id = $(this).parents(".ty-fileItem").data("id")
        let thisFileName = $(this).parents(".ty-fileItem").find(".ty-fileName").html();
        let thisFileNo = $(this).parents(".ty-fileItem").find(".ty-fileNo").html();
        let changenum = $(this).parents(".ty-fileItem").find(".ty-fileVersion").attr("changenum")
        let type = $(this).parents(".ty-fileItem").data("type")

        // 设置选中
        $(".ty-fileItem").removeClass("ty-fileItemActive");
        $(this).parents(".ty-fileItem").addClass("ty-fileItemActive");

        switch (name) {
            case 'seeOnline': // 在线预览
                fileSeeOnline($(this))
                break
            case 'download': // 下载
                fileDownload($(this))
                break
            case 'recovery': // 还原
                recoveryNoUseFile($(this))
                break
            case 'delete': // 删除
                deleteNoUseFile($(this))
                break
            case 'disable': // 禁用
                disableFile($(this))
                break
            case 'see':
                // 查看
                $("#seeDataContent .dataType" + type).show().siblings().hide()
                if (changenum == 0) {
                    layer.msg("该资料还没有上传至系统!")
                } else {
                    bounce.show($("#seeDataContent"))
                    getFileDetail(id).then(data => {
                        if ($(".page_fileHistory").is(":visible")) {
                            var piFile = data.piHisFile
                        } else {
                            var piFile = data.piFile
                        }
                        $("#seeDataContent [name]:visible").each(function() {
                            var name = $(this).attr("name")
                            $(this).html(piFile[name])
                        })
                    })
                }
                break
            case 'edit':
            // 公共信息2内容换版
                $("#editDataContent input").val("")
                $("#editDataContent .dataType" + type).show().siblings().hide()
                getFileDetail(id).then(data => {
                    var piFile = data.piFile
                    $("#editDataContent [name]:visible").each(function() {
                        var name = $(this).attr("name")
                        $(this).val(piFile[name])
                    })
                })
                bounce.show($("#editDataContent"))
                $("#editDataContent").data("fileId", id)
                $("#editDataContent").data("type", type)
                setEveryTime(bounce, 'edit')

                break
            // 换版文件
            case 'changeVersion':
                // 表单初始化
                initKendoFileUpload('changeVersion')
                $("#changeFileVersion").find("input.ty-inputText").val("")
                getFileDetail(id).then(data => {
                    let list = data.piFile
                    $("#changeFileVersion .see_fileNo").html(list.fileSn);
                    $("#changeFileVersion .see_fileName").html(list.name);
                    var chooseNode = $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive")
                    var folderNameObj = chooseNode.parents("li").children(".ty-treeItem").children("span")
                    var str = ''
                    folderNameObj.each(function (index) {
                        var name = $(this).text()
                        if(index === 0){
                            str = '<span class="ty-color-red">'+name+'</span>'
                        } else {
                            str = name + ' / ' + str
                        }
                    })
                    str = '<i class="fa fa-folder"></i>' + str
                    $("#changeFileVersion .see_savePlace").html(str)
                    $("#changeFileVersion .see_savePlace").data("categoryId", list.category)
                    bounce.show($("#changeFileVersion"));
                    $("#changeFileVersion").data("fileInfo", '')
                })
                setEveryTime(bounce, 'changeVersion')
                break;
            case 'basicMsg':
                getFileDetail(id).then(data => {
                    let res = data.piFile
                    if ($(".page_fileHistory").is(":visible")) {
                        res = data.piHisFile
                    }
                    let content      = res.content || '',
                        reason     = res.reason || ''
                        move_num    = data.moveNum,         // 移动次数
                        upName      = data.upNameNum,   // 文件名称修改次数
                        upFileSn    = data.upFileSnNum // 文件编号修改次数

                    var size = res.size || '--'
                    if (typeof size === 'number') {
                        size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size = parseFloat(size/1048576).toFixed(2) + 'MB';
                    }

                    var history = $(".page_fileHistory").is(":visible") // 是否是换版记录中的文件

                    $("#info_title").html(res.name);

                    // left
                    $("#info_sn").html(res.fileSn);
                    $("#info_category").html(data.categoryName);
                    $("#info_size").html(size);

                    // right
                    let updateDate = res.updateDate || res.createDate
                    $("#info_version").html( res.version || "--" );
                    $("#info_gn").html("G" + res.changeNum);
                    $("#info_createName").html(res.createName);
                    $("#info_createDate").html(moment(updateDate).format("YYYY-MM-DD HH:mm:ss"));

                    if( res.changeNum > 0){ // 换过版
                        $("#docInfoScan .info_content .ttl").html("换版原因：")
                        $("#info_description").html(reason);
                    }else{
                        $("#docInfoScan .info_content .ttl").html("说明：")
                        $("#info_description").html(content);
                    }


                    move_num === 0  ? $("#moveNumBtn").prop("disabled",true)        :$("#moveNumBtn").prop("disabled",false);
                    upName === 0    ? $("#changeNameNumBtn").prop("disabled",true)  :$("#changeNameNumBtn").prop("disabled",false);
                    upFileSn === 0  ? $("#changeNoNumBtn").prop("disabled",true)    :$("#changeNoNumBtn").prop("disabled",false);
                    $("#docInfoScan .censusInfo .move_num").html(move_num);
                    $("#docInfoScan .censusInfo .name_num").html(upName);
                    $("#docInfoScan .censusInfo .no_num").html(upFileSn);

                    if (history) {
                        $("#docInfoScan .censusInfo").hide()
                    } else {
                        $("#docInfoScan .censusInfo").show()
                    }
                    bounce.show($("#docInfoScan")) ;
                })
                break
            case 'changeVersionRecord': // 换版记录
            case 'editRecord': // 编辑换版记录
                $(".page_fileHistory").show().siblings(".page").hide()
                $(".page_fileHistory").data("fileId", id)
                getFileRecord(1, 20)
                break
            case 'move':
                $("#moveFile .bounce_title").html("移动到")
                bounce_Fixed.show($("#moveFile"));
                getFirstDoc("moveTo")
                $("#moveFile").data("id", id)
                setEveryTime(bounce_Fixed, 'moveTo');
                break
            case 'changeFileName':
                $("#changeFileName .changeFileName").val("");
                bounce.show($("#changeFileName"));
                $("#changeFileName").find(".currentFileName").html(thisFileName);
                $("#changeFileName").data("currentFileId",id);
                bounce.everyTime('0.2s','changeFileName',function(){
                    var changeFileName = $("#changeFileName .changeFileName").val();
                    if(changeFileName === ""){
                        $("#changeFileNameBtn").prop("disabled",true)
                    }else{
                        $("#changeFileNameBtn").prop("disabled",false)
                    }
                });
                break
            case 'changeFileSn':
                $("#changeFileNo .changeFileNo").val("");
                bounce.show($("#changeFileNo"));
                $("#changeFileNo").find(".currentFileNo").html(thisFileNo.substring(4,thisFileNo.length));
                $("#changeFileNo").data("currentFileId",id);
                bounce.everyTime('0.2s','changeFileNo',function(){
                    var changeFileName = $("#changeFileNo .changeFileNo").val();
                    if(changeFileName === ""){
                        $("#changeFileNoBtn").prop("disabled",true)
                    }else{
                        $("#changeFileNoBtn").prop("disabled",false)
                    }
                });
                break
        }
    })

    /* creator：张旭博，2017-05-06 15:17:01，每一个文件绑定事件（添加样式、获取文件详细信息、） */
    $(".ty-container").on("click",".ty-fileItem",function (){
        $(".ty-fileItem").removeClass("ty-fileItemActive");
        $(this).addClass("ty-fileItemActive");
    });

    $(".bounce").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            // 文件上传/文件发布申请/文件换版/文件换版申请 - 选择保存位置 - 按钮
            case 'chooseSaveFolder':
                bounce_Fixed.show($("#chooseSaveFolder"));
                getFirstDoc('chooseSaveFolder')
                setEveryTime(bounce_Fixed, 'chooseSaveFolder');
                break
            case 'sureUploadNewFile':
                sureUploadNewFile()
                break;
            // 公共信息2 - 编辑 - 确认
            case 'sureEditDataContent':
                sureEditDataContent()
                break
            case 'sureChangeVersion':
                sureChangeVersion()
                break;
            case 'sureUploadNewFolder':
                sureUploadNewFolder()
                break;
            case 'seeHandleRecordDetail':
                bounce_Fixed.show($("#handleRecordDetail"))
                var id = $(this).parents("tr").data("id")
                $("#handleRecordDetail").data("id", id)
                getOneSheetDetail()
                break;
        }
    })

    $(".bounce_Fixed").on("click", "[type='btn']", function () {
        let name= $(this).data("name")
        let thisNode = $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive")
        let thisCategoryId = thisNode.data("id")
        let chooseNode, categoryId
        switch (name) {
            case 'sureChooseFolder':
                chooseNode = $(".ty-colFileTree[data-name='chooseSaveFolder']").find(".ty-treeItemActive");
                categoryId = chooseNode.data("id");
                // 先判断文件夹是否设置了权限，没有就不能确定
                if (!categoryId) {
                    layer.msg("请选择目标文件夹")
                    return false
                }

                var folderNameObj = chooseNode.parents("li").children(".ty-treeItem").children("span")
                var str = '<'
                folderNameObj.each(function (index) {
                    var name = $(this).text()
                    if(index === 0){
                        str = '<span class="ty-color-red">'+name+'</span>'
                    } else {
                        str = name + ' / ' + str
                    }
                })
                str = '<i class="fa fa-folder"></i>' + str
                $("#fileUpload .savePlace").show().html(str)
                $("#fileUpload .savePlace").data("categoryId", categoryId)
                bounce_Fixed.cancel()
                break;
            case 'sureMoveFile':
                chooseNode = $(".ty-colFileTree[data-name='moveTo']").find(".ty-treeItemActive");
                categoryId = chooseNode.data("id");
                // 先判断文件夹是否设置了权限，没有就不能确定
                if (!categoryId) {
                    layer.msg("请选择目标文件夹")
                    return false
                }

                if (thisCategoryId === categoryId) {
                    layer.msg("不能移动到当前文件夹")
                    return false
                }
                var fileId      =  $("#moveFile").data("id");
                $.ajax({
                    url:"../pi/changePiFilePlace.do",
                    data:{ fileId :fileId, categoryId : categoryId},
                    success: function(res){
                        var data = res.data
                        var state = data.state
                        if(state === 1 ){
                            bounce_Fixed.cancel();
                            layer.msg("操作成功");
                            $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive").click()
                        } else if (state === 2) {
                            layer.msg("目标目录和原目录在同一级");
                        } else if (state === 3) {
                            layer.msg("目标目录不存在");
                        } else {
                            layer.msg("操作失败");
                        }
                    }
                });
                break
        }
    })

    // 文件-更多按钮点击事件
    $(".ty-container").on("click","[data-name='more']",function (e) {
        $(".hel").hide()
        $(this).next(".hel").show();
        e.stopPropagation()
    });
    // 点击body隐藏更多
    $("body").on("click",function () {
        $(".hel").hide()
    });
    // × 点击事件
    $(".clearInput").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            $(this).prev().focus();
        }
    });

    $("#advancedSearchBtn").on("click",function () {
        $("#searchSort").hide()
        $("#fileNameOrSn").val("");
        var type = $("#advancedSearch").data("type");
        type === 0 ? advancedSearch_file(1,20): advancedSearch_folder(1,20);
    })

    $("#advancedSearch .ty-secondTab li").on("click",function () {
        var index = $(this).index();
        $(this).addClass("ty-active").siblings('li').removeClass("ty-active");
        $("#advancedSearch .searchCon").eq(index).show().siblings(".searchCon").hide();
        $("#advancedSearch").data("type",index);
    })
    /* creator：张旭博，2017-05-06 15:17:01，为高级搜索 按文件类别搜索 的文件夹绑定事件（添加样式、获取自己文件夹信息、） */
    $(".page_search").on("click",".ty-folderItem",function (){
        $(this).addClass("ty-folderItemActive").siblings().removeClass("y-folderItemActive");
        var folderId        = $(this).attr("id"),
            folderParent    = $(this).find(".ty-folderParent").html(),
            folderName      = $(this).find(".ty-folderName").html(),
            nextFolderParent= '';
        folderParent === '' ? nextFolderParent = folderName :nextFolderParent = folderParent + '/' + folderName;
        //获取子级文件或者文件夹
        getChildFolder(folderId,nextFolderParent);
    });

    getFirstDoc("main")
})

// creator: 张旭博，2022-09-13 02:52:56， 所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        var state = 0
        var isDisabled = true
        switch (name) {
            // 新增客户
            case 'fileUpload':
                $("#fileUpload input[require]:visible").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                isDisabled = state > 0 || $("#fileUpload .savePlace").html() === '' || $("#fileUpload .k-upload-status").text() !== '上传成功'
                $("#sureUploadFileBtn").prop("disabled", isDisabled)
                break;
            case 'changeVersion':
                if ($("#changeFileVersion").data("fileInfo") === '') {
                    state ++
                }
                $("#sureChangeVersionBtn").prop("disabled", state > 0 )
                break
            case 'chooseSaveFolder':
                if($("#chooseSaveFolder .ty-treeItemActive").length > 0){
                    isDisabled = false
                }
                $("#sureChooseFolderBtn").prop("disabled", isDisabled );
                break;
            case 'moveTo':
                if($("#moveFile .ty-treeItemActive").length > 0){
                    isDisabled = false
                }
                $("#sureMoveToBtn").prop("disabled", isDisabled );
                break;
            case 'edit':
                $("#editDataContent input[require]:visible").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                $("#sureEditDataContentBtn").prop("disabled", state > 0 );
                break;
        }

    });
}

// creator: 张旭博，2022-09-13 02:52:41， 获取一级文件夹列表
function getFirstDoc(name){
    $.ajax({
        url: '../pi/getPiDir.do' ,
        success: function (res) {
            var data = res.data ,
                listFirstCategory = data.listFirstFolder,
                firstLevelStr = "";
            firstLevelStr += '<div class="level1" level="1">';
            for(var i in listFirstCategory){
                var item = listFirstCategory[i]
                var type = item.type // 1是“公共信息1” 2是“公共信息2” 9是“暂时不用的文件” null就是正常的文件夹
                var icon = type === 9 ? '<i class="fa fa-trash"></i>': '<i class="fa fa-folder"></i>'

                var str =   '<li>' +
                            '   <div class="ty-treeItem" fileType="'+type+'" title="'+ item.name +'" data-id="'+item.id+'" data-child="false">' +
                            '       <i class="ty-fa"></i>' + icon +
                            '       <span>'+item.name+'</span>' +
                            '   </div>' +
                            '</li>'
                switch (name) {
                    case 'main':
                        // 主页面
                        firstLevelStr += str
                        break
                    case 'chooseSaveFolder': // 选择文件夹（暂时包括上传和换版）
                        if (type === 2 || type === 9) {
                            firstLevelStr += ''
                        } else {
                            firstLevelStr += str
                        }
                        break
                    case 'moveTo': // 移动
                        if (type === 2) {
                            firstLevelStr += ''
                        } else {
                            firstLevelStr += str
                        }
                        break
                }

            }
            firstLevelStr += '<p id="tstip"></p>'+
                '</div>';
            $(".ty-colFileTree[data-name='"+name+"']").html(firstLevelStr)
        }
    });
}

// creator: 张旭博，2021-07-21 10:05:33，kendo 文件上传初始化
function initKendoFileUpload(type) {
    if (type === 'changeVersion') {
        // 换版
        $('#upload-file-02').parents('.upload_avatar').html('<input name="file" type="file" id="upload-file-02"></div>');
        $('#upload-file-02').kendoUpload({
            multiple: false,//只允许单文件上传，由于kendoUpload提交到async.removeUrl的参数只有文件名，当多文件上传时删除无法区别同名的文件。
            localization: {
                select: "选择文件",
                headerStatusUploading: "正在上传",
                headerStatusUploaded: "上传结束",
                headerStatusPaused: "暂停上传",
                invalidFileExtension: "系统暂不支持该类型文件的上传",
                invalidMaxFileSize: "文件超过1.5G"
            },
            async: {
                chunkSize: 1024 * 1024 * 2 - 500,// bytes
                saveUrl: $.webRoot+"/uploads/uploadfyByKendo.do",//断点续传后端。
                removeUrl: $.webRoot+"/uploads/removeByKendo.do",//删除已上传的后台文件。
                autoRetryAfter: 1000,
                maxAutoRetries: Number.MAX_VALUE,
                autoUpload: true
            },
            validation: {
                maxFileSize: 1024 * 1024 * 512 * 3 //1.5G
            },
            upload: function(e) {
                e.data = {module : '文件与资料', userId : sphdSocket.user.userID};
                //wyu：添加token
                e.XMLHttpRequest.addEventListener('readystatechange',function(e) {if (e.currentTarget.readyState == 1 /*OPENED*/) {e.currentTarget.setRequestHeader('token', auth.getToken())}})
            },
            select: function() {
                $("#fileUpload").data("fileInfo", '')
                //wyu：清空数据————由于kendoUI的BUG，如果文件很小，上传很快会导致headerStatusUploaded被removeAllFiles方法清空。
                // this.removeAllFiles();//清空之前上传的文件列表。
            },
            progress: function (e) {
                //wyu：上传进度，测试用
                // console.log("upload percentage:"+e.percentComplete+"%");
                if(e.percentComplete>0&&delayLogout.stopILTime==null) {
                    // console.log("e.percentComplete>0&&stopILTime==null",e.percentComplete>0&&delayLogout.stopILTime==null,delayLogout.stopILTime==null,delayLogout.stopILTime)
                    delayLogout.setDelayLogoutTime(new Date().getTime() + 72 * 3600 * 1000);//三天
                }
                // var files = e.files;
                // cuts = []
                console.log('progressE', e);
                // console.log('progress is touched');
            },
            success: function (e) {
                // console.log("successE",e)
                if(typeof e.response.token === 'string' && e.response.token.length > 0) {
                    auth.saveToken(e.response.token)
                }
                switch(e.operation) {
                    case 'upload':
                        var response = e.response;
                        //wyu：文件下载地址，不是预览地址，预览地址可以参考原预览代码
                        console.log($.uploadUrl + response.filename);
                        console.log("response", response)
                        //wyu：原始文件名
                        console.log("originalFilename:", response.originalFilename);
                        //wyu：上传后文件路径
                        console.log("filename:", response.filename);
                        //wyu：文件扩展名
                        console.log("lastName:", response.lastName);
                        //wyu：相对路径（空）
                        console.log("relativePath:", response.relativePath);
                        delayLogout.setDelayLogoutTime(new Date(response.createDate).getTime() + 72 * 3600 * 1000);//三天
                        var fileInfo = {
                            path: response.filename,
                            size: response.size,
                            version: response.lastName
                        }
                        if (response.originalFilename) {
                            if(response.lastName.length>0) {
                                $("#fileUpload input[name='name']").val(response.originalFilename.substring(0, response.originalFilename.length-response.lastName.length-1))
                                $("#fileUpload input[name='fileNo']").val(response.originalFilename.substring(0, response.originalFilename.length-response.lastName.length-1))
                            } else {//wyu：扩展名为空的情况，目前not used
                                $("#fileUpload input[name='name']").val(response.originalFilename)
                                $("#fileUpload input[name='fileNo']").val(response.originalFilename)
                            }
                            $("#changeFileVersion").data("fileInfo", fileInfo)
                        } else {
                            $("#changeFileVersion").data("fileInfo", '')
                        }
                        break;
                    case 'remove':
                        $("#fileUpload input[name='name']").val('');
                        $("#fileUpload").data("fileInfo", '')
                        break;
                }
            },
            remove: function(e) {
                e.async = false;
                e.files.forEach(function(file){
                    //wyu：文件名后加"\0"和文件uid，服务器端用uid处理，避免同名文件重复上传导致的BUG
                    file.name = file.name + "\0" + file.uid
                })
                e.data = {userId : sphdSocket.user.userID};
            }
        });
    } else {
        $('#upload-file-01').parents('.upload_avatar').html('<input name="file" type="file" id="upload-file-01"></div>');
        var groupUuid = sphdSocket.uuid();
        $('#upload-file-01').kendoUpload({
            multiple: false,//允许多文件上传，由于kendoUpload提交到async.removeUrl的参数只有文件名，当多文件上传时删除无法区别同名的文件。
            localization: {
                select: "选择文件", // 上传一个文件成功后改为选择下一个文件
                headerStatusUploading: "正在上传",
                headerStatusUploaded: "上传成功",
                headerStatusPaused: "暂停上传",
                invalidFileExtension: "系统暂不支持该类型文件的上传",
                invalidMaxFileSize: "文件超过1.5G"
            },
            async: {
                chunkSize: 1024 * 1024 * 2 - 500,// bytes
                saveUrl: $.webRoot+"/uploads/uploadfyByKendo.do",//断点续传后端。
                removeUrl: $.webRoot+"/uploads/removeByKendo.do",//删除已上传的后台文件。
                autoRetryAfter: 1000,
                maxAutoRetries: Number.MAX_VALUE,
                autoUpload: true
            },
            validation: {
                maxFileSize: 1024 * 1024 * 512 * 3 //1.5G
            },
            upload: function(e) {
                e.data = {module : '文件与资料', userId : sphdSocket.user.userID, groupUuid: groupUuid};
                //wyu：添加token
                e.XMLHttpRequest.addEventListener('readystatechange',function(e) {if (e.currentTarget.readyState == 1 /*OPENED*/) {e.currentTarget.setRequestHeader('token', auth.getToken())}})
            },
            progress: function (e) {
                //wyu：上传进度，测试用
                // console.log("upload percentage:"+e.percentComplete+"%");
                if(e.percentComplete>0&&delayLogout.stopILTime==null) {
                    // console.log("e.percentComplete>0&&stopILTime==null",e.percentComplete>0&&delayLogout.stopILTime==null,delayLogout.stopILTime==null,delayLogout.stopILTime)
                    delayLogout.setDelayLogoutTime(new Date().getTime() + 72 * 3600 * 1000);//三天
                }
                // var files = e.files;
                // cuts = []
                console.log('progressE', e);
                // console.log('progress is touched');
            },
            success: function (e) {
                // console.log("successE",e)
                if(typeof e.response.token === 'string' && e.response.token.length > 0) {
                    auth.saveToken(e.response.token)
                }
                switch(e.operation) {
                    case 'upload':
                        var response = e.response;
                        //wyu：文件下载地址，不是预览地址，预览地址可以参考原预览代码
                        console.log($.uploadUrl + response.filename);
                        console.log("response", response)
                        //wyu：原始文件名
                        console.log("originalFilename:", response.originalFilename);
                        //wyu：上传后文件路径
                        console.log("filename:", response.filename);
                        //wyu：文件扩展名
                        console.log("lastName:", response.lastName);
                        //wyu：相对路径（空）
                        console.log("relativePath:", response.relativePath);
                        delayLogout.setDelayLogoutTime(new Date(response.createDate).getTime() + 72 * 3600 * 1000);//三天
                        var fileInfo = {
                            path: response.filename,
                            size: response.size,
                            version: response.lastName
                        }
                        if (response.originalFilename) {
                            if(response.lastName.length>0) {
                                $("#fileUpload input[name='name']").val(response.originalFilename.substring(0, response.originalFilename.length-response.lastName.length-1))
                                $("#fileUpload input[name='fileNo']").val(response.originalFilename.substring(0, response.originalFilename.length-response.lastName.length-1))
                            } else {//wyu：扩展名为空的情况，目前not used
                                $("#fileUpload input[name='name']").val(response.originalFilename)
                                $("#fileUpload input[name='fileNo']").val(response.originalFilename)
                            }
                            $("#fileUpload").data("fileInfo", fileInfo)
                        } else {
                            $("#fileUpload").data("fileInfo", '')
                        }
                        break;
                    case 'remove':
                        $("#fileUpload input[name='name']").val('');
                        $("#fileUpload").data("fileInfo", '')
                        break;
                }
            },
            remove: function(e) {
                e.async = false;
                e.files.forEach(function(file){
                    //wyu：文件名后加"\0"和文件uid，服务器端用uid处理，避免同名文件重复上传导致的BUG
                    file.name = file.name + "\0" + file.uid
                })
                e.data = {userId : sphdSocket.user.userID};
            }
        });
    }
}

// creator: 张旭博, 2020-04-27 08:58:11, 文件上传 - 确定
function sureUploadNewFile(){
    //获取表单内容和接口需要的参数
    var fileInfo = $("#fileUpload").data("fileInfo");

    //参数json
    var data = {
        categoryId: $("#fileUpload .savePlace").data("categoryId"),
        name: $("#fileUpload [name='name']").val(),
        fileSn: $("#fileUpload [name='fileNo']").val(),
        content: $("#fileUpload [name='content']").val(),
        path: fileInfo.path,
        size: fileInfo.size,
        version: fileInfo.version,
        module: '公共信息',
        type: 5
    }
    $.ajax({
        url: '../pi/affirdPiFile.do',
        data: data,
        success: function (res) {
            var data = res.data
            var state = data.state
            if (state === 1) {
                layer.msg("操作成功")
                $("#upload-file-01").data("kendoUpload").clearAllFiles();
                var groupUuidObj = $("#fileUpload").data("groupUuidObj")
                cancelFileDel(groupUuidObj)
                $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive").click()
                bounce.cancel();
            } else if (state === 2) {
                layer.msg("操作失败！因为该文件的编号与系统内已有文件编号重复！")
            } else if (state === 3) {
                layer.msg("文件夹不存在!")
            } else {
                $("#mtTip #mt_tip_ms").html("操作失败!") ;
                bounce.show($("#mtTip"));
            }
        }
    })
}

// creator: 张旭博，2022-09-13 08:32:15， 公共信息2 - 编辑 - 确定
function sureEditDataContent() {
    var data = {
        fileId: $("#editDataContent").data("fileId"),
        type: $("#editDataContent").data("type"),
        module: '公共信息'
    }
    var editData = {}
    $("#editDataContent input:visible").each(function () {
        var name = $(this).attr("name")
        editData[name] = $(this).val()
    })
    var datas = {...data, ...editData}
    $.ajax({
        url: '../pi/changePiFileVersion.do',
        data: datas,
        success: function (res) {
            var success = res.success
            if (success === 1) {
                layer.msg("操作成功")
                bounce.cancel();
                $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive").click()
            } else {
                $("#mtTip #mt_tip_ms").html("操作失败!") ;
                bounce.show($("#mtTip"));
            }
        }
    })
}

// creator：张旭博，2017-05-06 15:11:35，换版弹窗 - 确定
function sureChangeVersion(){
    //获取表单内容和接口需要的参数
    var fileInfo = $("#changeFileVersion").data("fileInfo");
    var fileId = $(".ty-fileItemActive").data("id")
    var type = $(".ty-fileItemActive").data("type")

    if (!fileId) {
        layer.msg("文件发生移动，无法换版！")
        return false
    }
    //参数json
    var data = {
        fileId: fileId,
        reason: $("#changeFileVersion [name='reason']").val(),
        type: type,
        path: fileInfo.path,
        size: fileInfo.size,
        version: fileInfo.version,
        module: '公共信息',
        changeNum: $.trim($(".ty-fileItemActive .ty-fileVersion").attr("changeNum")) * 1 + 1,
    }
    if (type === 1) {
        data.path = fileInfo.path
        data.size = fileInfo.size
        data.version = fileInfo.version
    }
    console.log(`data: ${JSON.stringify(data)}`)
    $.ajax({
        url: '../pi/changePiFileVersion.do',
        data: data,
        success: function (res){
            var success = res.success
            if (success === 1) {
                layer.msg("操作成功")
                updateFile(fileId)
                if($("#upload-file-02").data("kendoUpload")) {
                    $("#upload-file-02").data("kendoUpload").clearAllFiles();
                }
                bounce.cancel();
            } else {
                $("#mtTip #mt_tip_ms").html("操作失败!") ;
                bounce.show($("#mtTip"));
            }
        }
    })
}

// creator：张旭博，2017-05-15 16:39:08，中断上传，关闭弹窗
function chargeXhr(){
    if(curXhr){ curXhr.abort(); curXhr = null;   }
    bounce.cancel();
    loading.close();
}

// creator: 张旭博，2018-04-24 10:17:16，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        if ($(".page_main").is(":visible")) {
            $("#ye_con").show() ;
            $("#fileSort").show() ;
        }
        for(var i = 0; i < fileInfo.length; i++){
            var id          = fileInfo[i].id,
                file        = fileInfo[i].file,
                category    = fileInfo[i].category,
                name        = fileInfo[i].name,
                size        = fileInfo[i].size,
                path        = fileInfo[i].path,
                fileType    = fileInfo[i].version ,
                fileSn      = fileInfo[i].fileSn,
                changeNum   = fileInfo[i].changeNum,
                updateName  = fileInfo[i].updateName,
                createName  = fileInfo[i].createName,
                updateDate  = fileInfo[i].updateDate,
                createDate  = fileInfo[i].createDate,
                updator     = fileInfo[i].updator,
                creator     = fileInfo[i].creator,
                isTrash     = fileInfo[i].trash,
                enabled     = fileInfo[i].enabled,
                type        = fileInfo[i].type, //1-公共信息1自带, 2-开票资料(增票),3-开票资料(普票),4-收款资料,5-普通文件 只有普通文件才能改名移动等操作
                isSystem    = type!==5, //1是系统自带 2是其他人上传的普通文件
                isSystem2   = type===2 || type === 3 || type === 4, //1是系统自带 2是其他人上传的普通文件
                handleStr   = ''             //定义操作按钮部分字符串

            //格式化数据
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';
            updateName  = updateName ||createName;   //更新人为空时 赋值 创建人，最后统一取更新时间 ， 下同
            updateDate  = moment(updateDate || createDate).format("YYYY-MM-DD HH:mm:ss");
            if (!updator) {updator = creator}

            let funcName = {
                see: '查看',
                edit: '编辑',
                editRecord: '编辑记录',
                seeOnline: '在线预览',
                download: '下载',
                basicMsg: '基本信息',
                more: '更多',
                changeVersion: '换版',
                changeVersionRecord: '换版记录',
                move: '移动',
                recovery:   '还原',
                delete:     '删除',
                changeFileName: '修改文件名称',
                changeFileSn: '修改文件编号',
                disable: '禁用'
            }

            let func = {}
            let moreFunc = {}

            if (file) {
                // 换版记录中的文件 - 功能

                if (type !== 1 && type !== 5) {
                    func = {
                        see: {},
                    }
                } else {
                    func = {
                        seeOnline: { path: path, disable: customVersion.indexOf(fileType) === -1 },
                        download: { path: path, download: name + '.' + fileType, disable: customVersion.indexOf(fileType) === -1 },
                        basicMsg: { history: '' },
                        disable: {color: 'red', disable: !enabled}
                    }
                }

            } else {
                if (type === 1) {
                    // 公共信息1自带
                    func = {
                        seeOnline: { path: path, disable: customVersion.indexOf(fileType) === -1},
                        download: { path: path, download: name + '.' + fileType, disable: customVersion.indexOf(fileType) === -1 },
                        changeVersion: {},
                        more: {}
                    }
                    moreFunc = {
                        basicMsg: {},
                        changeVersionRecord: { disable: changeNum < 2 }
                    }
                } else if (type === 5) {
                    // 普通文件
                    if (isTrash) {
                        func = {
                            seeOnline: { path: path, disable: customVersion.indexOf(fileType) === -1 },
                            download: { path: path, download: name + '.' + fileType, disable: customVersion.indexOf(fileType) === -1 },
                            recovery: {},
                            delete: { color: 'red' },
                            more: {}
                        }
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: changeNum  === 0 },
                        }
                    } else {
                        func = {
                            seeOnline: { path: path, disable: customVersion.indexOf(fileType) === -1 },
                            download: { path: path, download: name + '.' + fileType, disable: customVersion.indexOf(fileType) === -1 },
                            changeVersion: {},
                            more: {}
                        }
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: changeNum  === 0 },
                            move: {},
                            changeFileName: {},
                            changeFileSn: {}
                        }
                    }
                } else {
                    // 公共信息2自带
                    func = {
                        see: {},
                        edit: {},
                        editRecord: {disable: changeNum < 2}
                    }
                }

            }

            for (let key in func) {
                handleStr += '<a class="aBtn" type="btn" data-name="'+key+'"'
                let attr = func[key]
                for (var item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</a>'
            }
            handleStr += '<ul class="hel" style="display: none">'
            for (let key in moreFunc) {
                handleStr += '<li class="liBtn" type="btn" data-name="'+key+'"'
                let attr = moreFunc[key]
                for (let item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</li>'
            }
            handleStr += '</ul>'

            let disabledPngStr = enabled === false?'<img class="disablePng" src="'+$.webRoot +'/css/content/img/disabled.png">':''

            if (changeNum === 0 && isSystem && $(".fileHistory").is(":visible")) {
                // 换版记录不显示首条数据
                fileListStr += ''
            } else {
                fileListStr +=  '<div class="ty-fileItem" data-id="'+id+'" pid="'+category+'" data-id="'+id+'" data-type="'+type+'">'+
                                disabledPngStr +
                                '<div class="ty-fileType ty-file_'+fileType+'"></div>'+
                                '<div class="ty-fileInfo">'+
                                '   <div class="ty-fileRow">'+
                                '       <div class="ty-fileName" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                                '       <div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                                '       <div class="ty-fileVersion" changenum="'+ changeNum +'">'+'G'+changeNum+'</div>'+
                                '   </div>'+
                                '   <div class="ty-fileRow">'+
                                (isSystem && changeNum === 0?'<div class="ty-fileDetail">--</div>':'<div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + (isSystem2?' ':size) + ' &nbsp;&nbsp; ' + updateDate + '</div>')+
                                '       <div class="ty-fileHandle">'+ handleStr + '</div>'+
                                '   </div>'+
                                '</div>'+
                                '</div>';
            }

        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2021-10-15 08:24:23，获取文件列表
function getFileList (currentPageNo, pageSize, categoryId) {
    let pms_childFile = new Promise(resolve => {
        let url = '../pi/getPiCatFile.do'
        let data = { categoryId:categoryId ,currentPageNo : currentPageNo, pageSize : pageSize}
        $.ajax({
            url: url,
            data: data,
            success: function (res) {
                if (res) {
                    resolve(res.data)
                }
            }
        })
    })
    pms_childFile.then(res => {


        var pageInfo = res.pageInfo,
            fileInfo = res.list;

        //设置分页信息
        var currentPageNo   = pageInfo["currentPageNo"],
            totalPage       = pageInfo["totalPage"],
            jsonStr = JSON.stringify( { categoryId : categoryId} ) ;

        //设置文件信息
        var fileListStr = getFileListStr(fileInfo);

        setPage( $("#ye_con"), currentPageNo, totalPage, "publicFile", jsonStr) ;
        $(".ty-fileList").html(fileListStr);
    })
}

// creator: 张旭博，2021-10-15 08:24:38，获取文件详情
function getFileDetail(id) {
    return new Promise(resolve => {
        let url = '../pi/getPiFileMes.do'
        let data = { fileId: id}
        if ($(".page_fileHistory").is(":visible")) {
            url = '../pi/getPiHisFileMes.do'
            data = { fileHisId : id}
        }
        $.ajax({
            url: url,
            data: data,
            success: function (res) {
                if (res) {
                    let data = res.data
                    resolve(data)
                }
            }
        })
    })
}

// creator: 张旭博，2018-05-16 15:27:29，更改文件名称 - 确定
function sureChangeFileName() {
    var currentFileId = $("#changeFileName").data("currentFileId"),
        changeFileName = $("#changeFileName .changeFileName").val();
    $.ajax({
        url:"../pi/updatePiFileName.do",
        data:{
            fileId: currentFileId,
            name: changeFileName
        },
        success: function(res){
            var data = res.data
            var state = data.state
            bounce.cancel();
            if(state === 1){
                layer.msg("成功修改文件名称");
                updateFile(currentFileId);
            }else{
                layer.msg("操作失败");
            }
        }
    });
}

// creator: 张旭博，2018-05-16 15:27:29，更改文件编号 - 确定
function sureChangeFileNo() {
    var currentFileId = $("#changeFileNo").data("currentFileId"),
        changeFileNo = $("#changeFileNo .changeFileNo").val();
    $.ajax({
        url:"../pi/updatePiFileSn.do",
        data:{
            fileId: currentFileId,
            fileSn: changeFileNo
        },
        success: function(res){
            let data = res.data
            var state = data.state
            if (state == 1) {
                bounce.cancel();
                layer.msg("成功修改文件编号");
                updateFile(currentFileId);
            } else if (state == 2) {
                layer.msg("文件编号重复！")
            } else {
                layer.msg("操作失败");
            }
        }
    });
}

// creator: 张旭博，2021-07-21 09:14:10，更新文件数据
function updateFile(fileId){
    $.ajax({
        url: "../pi/getPiFileMes.do",
        data: {"fileId" :fileId},
        success: function (res) {
            if (res) {
                let data = res.data.piFile
                var datas = []
                datas.push(data)
                var listStr = getFileListStr(datas);
                $(".ty-fileItemActive").replaceWith(listStr);
            }
        }
    })
}

// creator: 张旭博，2021-10-15 09:17:53，获取换版后的历史文件列表
function getFileRecord(currentPageNo, totalPage){
    //得到地址栏传过来的ID
    var fileId = $(".page_fileHistory").data("fileId");
    var data = {
        fileId: fileId,
        currentPageNo: currentPageNo,
        pageSize: totalPage
    }
    $.ajax({
        url: "../pi/getPiFileHistory.do",
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"]

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo);
            setPage( $("#ye_record"), currentPageNo, totalPage, "publicFileRecord") ;
            $(".page_fileHistory .historyFileList").html(fileListStr)
        }
    })
}

// creator: 张旭博，2018-05-16 16:29:28，查看各种操作记录
function seeHandelRecordBtn(type){
    //打开弹窗
    bounce_Fixed.show($("#handleRecord"));

    //更改弹窗标题
    var recordTitleName = '';
    switch (type){
        case 5:
            recordTitleName = '移动记录';
            break;
        case 6:
            recordTitleName = '文件名称修改记录';
            break;
        case 7:
            recordTitleName = '文件编号修改记录';
            break;
    }
    $("#handleRecord").find(".recordTitleName").html(recordTitleName);

    //渲染操作记录表格
    setHandelRecord(type);
}

// creator: 张旭博，2018-05-16 16:46:20，获取各种操作记录
function setHandelRecord(type) {
    var currentFileId = $(".ty-fileItemActive").data("id"),
        tableStr = '';
    switch (type){
        case 5:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>移动前</td>'+
                '<td>移动后</td>'+
                '<td>移动人</td>'+
                '<td>移动时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 6:
        case 7:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>修改前</td>'+
                '<td>修改后</td>'+
                '<td>修改人</td>'+
                '<td>修改时间</td>'+
                '</tr>'+
                '</thead>';
            break;
    }
    $.ajax({
        url:"../pi/getPiUpFileRecord.do",
        data:{
            fileId  : currentFileId,
            operation: type
        },
        success: function(res){
            if(res["success"] === 1){
                var list = res.data.list;
                tableStr += '<tbody>';
                for(var i = 0; i < list.length; i++){
                    tableStr += '<tr>' +
                        '<td>' + (i + 1) +'</td>'+
                        '<td>' + list[i].nameBefore + '</td>'+
                        '<td>' + list[i].nameAfter + '</td>'+
                        '<td>' + list[i].createName + '</td>'+
                        '<td>' + moment(list[i].createTime).format("YYYY-MM-DD HH:mm:ss") + '</td>'+
                        '</tr>'
                }
                tableStr += '</tbody>';
                $("#handleRecord .recordTable").html(tableStr)
            }else{
                $("#tipMess2").html('系统错误！');
                bounce_Fixed.show($("#tip2"));
            }
        }
    });
}

// creator: 张旭博，2021-10-20 09:30:21，获取审批人
function getApprover() {
    $.ajax({
        url: "../res/getAllOidUserByResource.do" ,
        data: { userID: sphdSocket.user.userID },
        success: function (data) {
            var approverList = data["data"]["list"];
            var optStr = '<option value="">请选择本文件的审批人</option>';
            if (approverList && approverList.length >0){
                for(var i in approverList) {
                    optStr += '<option value="'+ approverList[i].userID +'">'+ approverList[i].userName +'</option>';
                }
            }
            $(".chooseApprover").html(optStr);
        }
    })
}

//------------------------- 文件上的所有按钮功能 ---------------------------//

// creator: 张旭博，2018-05-24 11:04:53，表格功能 - 预览
function fileSeeOnline(selector) {
    let isChargeRecord = $("#resHistory").is(":visible")
    if(!isChargeRecord){
        //记录点击次数
        var currentFileId = selector.parents(".ty-fileItem").data("id");
        $.ajax({
            url:"../res/viewAndDownloadRecord.do",
            data:{
                "id"  : currentFileId,
                "type": 1
            },
            success: function(data){
                if(data["success"] !== 1){
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }

    seeOnline(selector)
}

// creator: 张旭博，2018-05-24 11:05:09，表格功能 - 下载
function fileDownload(selector) {
    getDownLoad(selector)
}

// creator: 张旭博，2021-07-20 15:47:04，文件功能 - 还原弹窗(暂时不用的文件专属)
function recoveryNoUseFile(obj) {
    bounce.show($("#bounce_tip"))
    $("#bounce_tip").find(".tipMsg").html("文件“还原”后，还回到其原来的文件夹。<br>确定“还原”这个文件吗？")
    $("#bounce_tip").find(".sureBtn").unbind().on("click", function () {
        var fileId      = obj.parents(".ty-fileItem").data("id")
        $.ajax({
            url: '../pi/trashPiFileRestore.do',
            data: {
                fileId: fileId
            },
            success: function (res) {
                bounce.cancel()
                var state = res.data.state
                if (state === 1) {
                    layer.msg("操作成功")
                    obj.parents(".ty-fileItem").remove()
                } else if (state === 2 || state === 4) {
                    $("#tipMess2").html("还原失败！<br>因为原文件夹已被移动或删除！")
                    bounce_Fixed.show($("#tip2"))
                } else if (state === 3) {
                    $("#tipMess2").html("还原失败！<br>因为有文件与要还原文件的文件编号重复！！")
                    bounce_Fixed.show($("#tip2"))
                } else {
                    $("#tipMess2").html("还原失败！")
                    bounce_Fixed.show($("#tip2"))
                }
            }
        })
    })
}

// creator: 张旭博，2021-07-20 15:47:04，文件功能 - 删除弹窗(暂时不用的文件专属)
function deleteNoUseFile(obj) {
    bounce.show($("#bounce_tip"))
    $("#bounce_tip").find(".tipMsg").html("确定“删除”这个文件吗？")
    $("#bounce_tip .sureBtn").unbind().on("click", function () {
        var fileId      = obj.parents(".ty-fileItem").data("id")
        $.ajax({
            url: '../pi/delTrashFile.do',
            data: {
                fileId: fileId
            },
            success: function (res) {
                bounce.cancel()
                var state = res.data.state
                if (state === 1) {
                    layer.msg("文件已“删除”！之后3天，在“即将消失的文件”中您还能见到该文件!")
                    obj.parents(".ty-fileItem").remove()
                } else if (state === 4) {
                    $("#tipMess2").html("删除失败！<br>文件进入“暂时不用的文件”需满5天才能删除！")
                    bounce_Fixed.show($("#tip2"))
                } else {
                    $("#tipMess2").html("删除失败！")
                    bounce_Fixed.show($("#tip2"))
                }
            }
        })
    })
}

// creator: 张旭博，2021-07-20 15:47:04，文件功能 - 禁用文件弹窗
function disableFile(obj) {
    var fileHisId      = obj.parents(".ty-fileItem").data("id")
    $("#bounce_tip .tipMsg").html("禁用后，其他职工将无法打开该文件的这个版本。<br>确定禁用该文件的这个版本吗？");
    $("#bounce_tip .sureBtn").one("click", function () {
        var fileHisId  = obj.parents(".ty-fileItem").data("id")
        $.ajax({
            url:"../pi/disablePiFileHis.do",
            data:{ fileHisId: fileHisId},
            success: function(res){
                var state = res.data.state
                if(state === 1 ){
                    bounce.cancel();
                    layer.msg("操作成功");
                    getFileRecord(1, 20)
                } else if (state === 2) {
                    layer.msg("文件已经被禁用，请勿重复禁用");
                } else {
                    layer.msg("操作失败")
                }
            }
        });
    })
    bounce.show($("#bounce_tip")) ;

}

// creator: 张旭博，2018-05-24 11:24:08，返回
function goBack(type) {
    switch (type) {
        case 'record':
            $(".page_main").show().siblings(".page").hide();
            break;
    }
}