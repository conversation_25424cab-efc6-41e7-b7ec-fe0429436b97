/**
 * Created by Administrator on 2017/8/14 0014.
 */

var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#knowTip"));
bounce_Fixed2.cancel();

var mtCategories = null; // 全部种类数据
var listData = { 'category':false , 'state':false , 'keyword':'' } // 列表的传值数据

$(function () {
    $.ajax('../skl/getStockMode')
        .then(res => {
            let status = res.status
            $("body").data('whmode', status) //0-非智能 1-智能
            // 获取初始页面数据
            getIndexData();
        })

    // 点击分类获取分类下的物料
    $('#catAll').on('click', 'li', function(){
        let index = $(this).index();
        var id = $(this).data("id") ;
        getPdByCategory(id, 4, "",1,20)
    });
    // 出入库统计
    $("#checkInOutInfo .ty-secondTab li").click(function(){
        let _index = $(this).index();
        getLogList(1,20,_index)
        $("#checkInOutInfo .ty-secondTab li").attr("class","")
        $(this).attr("class","ty-active")
    })
    $('#scanCtrlCon').on('click', 'li', function(e){
        var type = $(this).data("type") ;
        var mtid = $('#scanCtrlCon').data("mtid") ;
        $("#scanCtrlCon").hide() ;
        switch (type){
            case "initStock":
                initStockUpdateRecord(mtid);
                break;
            case "minStock":
                safeStockUpdateRecord(mtid);
                break;
            case "stockChangeLog": // 库存变动记录
                var info = $('#scanCtrlCon').data("info") ;
                stockChangeLog(info);
                break;
        }
    });

    // 单选按钮事件
    $(".ty-radio").on("click",function () {
        $(this).addClass("ty-radioActive").siblings().removeClass("ty-radioActive");
        $(this).siblings().children("i").attr("class","fa fa-circle-o")
        $(this).children("i").attr("class","fa fa-dot-circle-o")
        $(this).siblings(".judgmentQuantity").val($(this).attr("value"));
        if($(this).attr("value") === "1"){
            $(this).siblings("#productNum").show()
            $(this).siblings("#productNum").children("input").focus();
        }else{
            $(this).siblings("#productNum").hide()
        }
    });
    // 点击body隐藏更多
    $("body").on("click",function () {
        $(".hel").hide()
    });
    $(".seemore").on("click",".cz_handle",function (e) {
        $(".hel").hide()
        $(this).find(".hel").show();
        e.stopPropagation()
    });

    $('tbody').on('click', 'span', function(e){
        let type = $(this).data('type');
        switch (type){
            case 'scanCtrl':// 查看
                var info = JSON.parse($(this).siblings(".hd").html()) ;
                editObj = $(this);
                var local = $(this).offset();
                $("#scanCtrlCon").css({ "left":(local.left-100) + "px" , "top":(local.top-15) + "px"  }).data("mtid", info.id).data("info", info).show() ;
                e.stopPropagation();
                break;
            case 'currentStock':
                var info = JSON.parse($(this).parent().siblings(":last").find(".hd").html()) ;
                editObj = $(this);
                showCurrentStorage(info);
                break
            case 'initialStock':
                var info = JSON.parse($(this).parent().siblings(":last").find(".hd").html()) ;
                editObj = $(this);
                showStorageHis(info);
                break;
            case 'locationNumber':
                var info = JSON.parse($(this).parent().next().find(".hd").html()) ;
                editObj = $(this);
                showLocation(info)
                break;
            default:
        }

    });

    // 点击横向导航栏
    $('.curCat').on('click', '.go2Cat', function(){
        var cat = $(this).find(".hd").html();
        $(this).nextAll().remove();
        $(this).remove();
        listData['state'] = "" ;
        getPdByCategory(cat, 4, '', 1 , 20 );
    });

});

// ====================== 仓库管理- 原辅材料库 ========================//
// creator ：侯杏哲 2022-02-16  库存变动记录
function stockChangeLog(info) {
    $(".stockChange").show().siblings().hide();
    $(".dataFilter").show();
    $(".stockChange .mtInfo").html(`${ info.code } ${ info.name } ${ info.model } ${ info.specifications } `)
    $(".stockChange .curSto").html(`当前库存 ${ info.current_stock }`)
    $(".stockChange .unit").html(`计量单位 ${ info.unit }`)
    $(".stockChange .create").html(`创建 ${ info.create_name } ${ new Date(info.create_date).format("yyyy-MM-dd hh:mm:ss") } `)
    $(".stockChange .mtWeightUnit").html(`材料单重 ${ info.unit } `)
    $(".stockChange .mtWeightAll").html(`材料总重 ${ info.unit } `)
    let dataInfo = {
        "id": info.id,
        "type": "('in','out','new_initial','modify_initial','inventory','wastage')",
        "startTime":'',
        "endTime":'',
        "pageNum": 1,
        "per": 20
    }
    stockChangeRecord(dataInfo);
}
function stockChangeRecord(dataInfo) {
    $.ajax({
        'url':"../mt/stockChangeRecord",
        data:{
            "id": dataInfo.id,
            "type": dataInfo.type ,
            "startTime":dataInfo.startTime ,
            "endTime": dataInfo.endTime ,
            "pageNum": dataInfo.pageNum ,
            "per": dataInfo.per,
            "year": dataInfo.year,
        },
        success:function(res){
            let list = res.data || [];
            let totalPage = res.totalPage || 1;
            let currPage = res.currPage || 1;
            let startTime = res.startTime ;
            let endTime = res.endTime ;
            let start = startTime.split('-')
            let start_year = start[0] ;
            let start_month = start[1] ;
            let start_day = start[2] ;
            let end = endTime.split('-')
            let end_year = end[0] ;
            let end_month = end[1] ;
            let end_day = end[2] ;
            let strDay = ''
            if(start_year == end_year ){
                if(start_month == end_month){
                    strDay = `${start_year}-${ start_month }`
                }else{
                    strDay = `${start_year}-${ start_month } 至 ${end_year}-${ end_month } `
                }
            }
            if(start_year != end_year ){
                strDay = `${start_year}-${ start_month } 至 ${end_year}-${ end_month } `
            }
            $(".stockChange .mtSearchDay").html(strDay)

            let str = ``
            list.forEach(function (item) {
                str += `
                    <tr>
                        <td>${ new Date(item.event_time).format("yyyy-MM-dd") }</td>
                        ${ formatEvent(item.event, item.quantity) } 
                        <td>${ item.end_stock || '' }</td>
                        <td>${ item.memo || '' }</td>
                        <td><span class="ty-color-gray">查看</span></td>
                    </tr>
                `
            })
            $("#socketchangeTB").html(str);
            setPage($("#ye43"),currPage,totalPage,"stockChangeRecord", JSON.stringify(dataInfo));
        }
    })
}
// creator ：侯杏哲 2022-02-16  格式化 事件
function formatEvent(event, num) {
    let addOrp = num > 0 ? "增加" : "减少";
    let absNum = Math.abs(num)
    let str = ``
    switch (event){
        case 'in':
            str = `<td>入库</td><td>${ addOrp }${ absNum }</td>`
            break;
        case 'out':
            str = `<td>出库</td><td>${ addOrp }${ absNum }</td>`
            break;
        case 'new_initial':
            str = `<td>录入初始库存</td><td>从0增至${ absNum }</td>`
            break;
        case 'modify_initial':
            str = `<td>修改初始库存</td><td>${ addOrp }${ absNum }</td>`
            break;
    }
    return str;
}
// creator ：侯杏哲 2022-02-16  数据筛选
function dataFilter(){
    bounce.show($("#dataFilterCon"))
    $("#s1").val("1");
    $("#s2").val("");
    $("#s3").val("0");
}
function dataFilterOk() {
    let type = $("#s1").val();
    let year = $("#s2").val();
    let month = $("#s3").val();
    var info = $('#scanCtrlCon').data("info") ;
    let dataInfo = {
        "id": info.id,
        "type": "('in','out','new_initial','modify_initial','inventory','wastage')",
        "startTime":'',
        "endTime":'',
        "pageNum": 1,
        "per": 20
    }
    if(type == '1'){
        dataInfo.type = "('in','out','new_initial','modify_initial','inventory','wastage')" ;
    }else if(type == '2'){
        dataInfo.type = "('new_initial','modify_initial')" ;
    }else if(type == '3'){
        dataInfo.type = "('out')" ;
    }else if(type == '4'){
        dataInfo.type = "('in')" ;
    }else if(type == '4'){
        dataInfo.type = "('in','out')" ;
    }
    if( year ){
        if(month == 0){
            dataInfo.startTime = `${ year }-01-01`
            dataInfo.endTime = `${ year }-12-31`
            let curTime = new Date()
            let endTime = new Date( dataInfo.endTime)
            if(curTime < endTime){
                dataInfo.endTime = curTime.format("yyyy-MM-dd")
            }
        } else {
            dataInfo.startTime = `${ year }-${ month }-01`
            dataInfo.endTime = `${ year }-${ month }-31`
            let isRun = (year / 4 === 0) && (year / 400 !== 0) ;
            if(month == "02"){
                if(isRun){ // 闰年
                    dataInfo.endTime = `${ year }-${ month }-29`
                }else{
                    dataInfo.endTime = `${ year }-${ month }-28`
                }
            }else if(month == "04" || month == "06" || month == "09" || month == "11" ){
                dataInfo.endTime = `${ year }-${ month }-30`
            }
        }
    }else{
        dataInfo.year = 'all'
    }
    bounce.cancel();
    stockChangeRecord(dataInfo);

}
// creator ：侯杏哲 2020-04-26  隐藏控制窗
function scanCtrlHide() {
    $("#scanCtrlCon").hide();
}
// creator ：侯杏哲 2020-04-26  初始库存 修改
function initStockEditBtn() {
    $("#updateStores").data("id",  $('#initStock').data('mtid')).data("type",1);
    updateStores()
}
// creator ：侯杏哲 2020-04-26  初始库存
function showStorageHis(info) {
    var mtid = info.id
    var unit = info.unit
    var initial_stock = info.initial_stock || 0 ;
    $("#curNum").html(initial_stock + '个');
    $('#initStock').data('mtid', mtid).data('initialstock', initial_stock)
    bounce.show($('#initStock'));
    var data = { "mtBaseId" : mtid }
    $.ajax({
        "url": "../material/modifyRecord.do" ,
        "data": data ,
        success: function (res) {
            var struts = res["struts"] ;
            var str = "" ;
            var initialList = res["initialList"] || [] ;
            var minlist = res["minlist"] ;
            var list = initialList;
            if(list && list.length > 0){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<tr>" +
                        "<td>"+ list[i]["createName"] +"</td>" +
                        "<td>"+ (new Date(list[i]["createDate"]).format("yyyy-MM-dd hh:mm:ss"))  +"</td>" +
                        "<td>"+ (list[i]["name"]||"") +"</td>" ;
                    str += "<td>"+ (list[i]["initialStock"] || "") +"</td><td width=\"20%\">"+ list[i]["afterInitialStock"] +"</td></tr>" ;

                }
            }
            $("#initStock tbody").html(str);
        }
    })
}

// creator: 张旭博，2025-02-23 03:17:55， 当前库存
function showCurrentStorage(mtInfo) {
    // 现在是二模式的展示（298还会修改 一模式下的展示）
    let mtStr = `<tr>
                        <td>${mtInfo.name || ''}</td>
                        <td>${mtInfo.code || ''}</td>
                        <td>${mtInfo.model || ''}</td>
                        <td>${mtInfo.specifications || ''}</td>
                        <td>${mtInfo.unit || ''}</td>
                        <td>${mtInfo.create_name + ' ' + moment(mtInfo.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                    </tr>`
    let mode = $("body").data('whmode')
    if (mode === 1) {
        // 智能库
        $("#currentStock_zhineng .tbl_mt tbody").html(mtStr)
        $.ajax({
            url: $.webRoot + '/iwre/currentStock',
            data: {
                id: mtInfo.id
            }
        }).then(res => {
            let data = res
            $("#currentStock_zhineng .currentNum").html(mtInfo.current_stock)
            $("#currentStock_zhineng .unit").html(mtInfo.unit || '')
            let withSupplierId = data.filter(item => item.hasOwnProperty('supplier_id')); // 有供应商得数据
            withSupplierId.sort((a, b) => a.supplier_id - b.supplier_id); // 按供应商id排序
            let withoutSupplierId = data.filter(item => !item.hasOwnProperty('supplier_id')); // 没有供应商得数据

            let sortArray = [...withSupplierId, ...withoutSupplierId]
            let tbStr = ''

            for (let item of sortArray) {
                item.mtInfo = mtInfo
                let isRowspan = data.find(it => it.supplier_id === item.supplier_id).id === item.id

                let groupArr = data.filter(it => it.supplier_id === item.supplier_id)
                let account = 0
                groupArr.forEach(item => { account += Number(item.amount) })
                let rowLength = groupArr.length
                tbStr += `<tr>
                                ${isRowspan?'<td rowspan="'+rowLength+'">'+((item.full_name + '/' + item.supplier_name + '/' + item.code_name) || '难以区分供应商')+'</td>':''}
                                <td>${moment(item.expiration_date).format("YYYY-MM-DD")}</td>
                                <td><span class="link-blue" type="supplier_amount" onclick="setStorage($(this))">${item.amount}</span></td>
                                ${isRowspan?'<td rowspan="'+rowLength+'"><span class="link-blue" type="supplier_count" onclick="setStorage($(this))">'+account+'</span></td>':''}
                                <td><span class="link-blue" onclick="seePacking($(this))">查看</span></td>
                                <td class="hd">${JSON.stringify(item)}</td>
                            </tr>`
            }
            $("#currentStock_zhineng .tbl_supplier tbody").html(tbStr)
            bounce.show($("#currentStock_zhineng"))
        })
    } else {
        // 非智能库
        $("#currentStock .tbl_mt tbody").html(mtStr)
        $.ajax({
            url: $.webRoot + '/material/setting/currentStock',
            data: {
                material: mtInfo.id
            }
        }).then(res => {
            let data = res.data;
            let state = data.state // 型式 （11 区分供应商 区分包装 12区分供应商不区分包装 21 不区分供应商区分包装 22都不区分）
            $("#currentStock .state" + state).show().siblings().hide()
            let storageData = data.map

            let tbStr = ''

            if (state === '11') {
                for (let item of storageData) {
                    let name = item.supplier_id ? `${item.full_name}/${item.supplier_name}/${item.code_name}` : '难以区分供应商的本材料'
                    item.mtInfo = mtInfo
                    tbStr += `<tr>
                                <td>${name}</td>
                                <td><span class="link-blue" onclick="setStorage()">${item.amount}</span></td>
                                <td><span class="link-blue" onclick="seePacking($(this))">查看</span></td>
                                <td class="hd">${JSON.stringify(item)}</td>
                            </tr>`
                }
                $("#currentStock .state" + state + ' tbody').html(tbStr)
                $("#currentStock .state" + state + " .currentNum").html(data.initialStock)
            }

            if (state === '12') {
                for (let item of storageData) {
                    let name = item.supplier_id ? `${item.full_name}/${item.supplier_name}/${item.code_name}` : '难以区分供应商的本材料'
                    item.mtInfo = mtInfo
                    tbStr += `<tr>
                                <td>${name}</td>
                                <td><span class="link-blue" onclick="setStorage()">${item.amount}</span></td>
                                <td><span class="link-gray">查看</span></td>
                                <td class="hd">${JSON.stringify(item)}</td>
                            </tr>`
                }
                $("#currentStock .state" + state + ' tbody').html(tbStr)
                $("#currentStock .state" + state + " .currentNum").html(data.initialStock)
            }

            if (state === '21') {
                let count = 0
                for (let item of storageData) {
                    tbStr = `<tr>
                                <td>没有包装</td>
                                <td>--</td>
                                <td>${item.packaging_count || 0}</td>
                                <td><span class="link-blue" onclick="setStorage()">${item.amount || 0}</span></td>
                            </tr>`
                    count += item.amount
                }
                $("#currentStock .state" + state + ' tbody').html(tbStr)
                $("#currentStock .state" + state + ' tfoot .countNum').html(count)
                $("#currentStock .state" + state + ' .currentNum').html(data.initialStock)
            }
            if (state === '22' || state === '00') {
                $("#currentStock .state" + state + ' .currentNum').html(data.initialStock)
            }
            $("#currentStock .state" + state + ' .unit').html(mtInfo.unit)
            bounce.show($("#currentStock"))
        })
    }

}

// creator: hxz，2020-01-21 11:02:34，当前库存占用库位(智能库)
function showLocation(info) {
    let mtStr = `<tr>
                        <td>${info.name || ''}</td>
                        <td>${info.code || ''}</td>
                        <td>${info.model || ''}</td>
                        <td>${info.specifications || ''}</td>
                        <td>${info.unit || ''}</td>
                        <td>${info.create_name + ' ' + moment(info.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                    </tr>`
    $("#showLocationOfStorage .tbl_mt tbody").html(mtStr);
    $("#showLocationOfStorage .stockNum").html(info.current_stock || 0);
    $("#showLocationOfStorage .unit").html(info.unit || '');
    $("#showLocationOfStorage .storageLocationNum").html(info.location_number || 0);
    $.ajax({
        url: $.webRoot + '/iwre/getOccLocationByMtId',
        data: {
            material: info.id
        }
    }).then(function(res) {
        let data = res
        data.sort((a, b) => a.location - b.location); // 按供应商id排序

        let tbStr = ''

        for (let item of data) {
            item.mtInfo = info
            let isRowspan = data.find(it => it.location === item.location).id === item.id
            let groupArr = data.filter(it => it.location === item.location)
            let account = 0
            groupArr.forEach(item => { account += Number(item.amount) })
            let rowLength = groupArr.length
            tbStr += `<tr>
                                ${isRowspan?'<td rowspan="'+rowLength+'">'+item.location_code + '-' + item.shelf_code+'</td>':''}
                                <td>${moment(item.expiration_date).format("YYYY-MM-DD")}</td>
                                <td>${item.full_name}/${item.supplier_name}/${item.code_name	}</td>
                                <td>${item.amount}</td>
                                ${isRowspan?'<td rowspan="'+rowLength+'">'+ account +'</td>':''}
                                <td><span class="link-blue" onclick="seePacking($(this), 'location')">查看</span></td>
                                <td class="hd">${JSON.stringify(item)}</td>
                            </tr>`
        }
        $("#showLocationOfStorage .tbl_location tbody").html(tbStr)
        bounce.show($("#showLocationOfStorage"))
    })
}

// creator: 张旭博，2025-02-23 05:36:59， 查看包装数据
function seePacking(selector, type) {
    bounce_Fixed.show($("#packingOfMt"))
    let data = JSON.parse(selector.parents('tr').find('.hd').html())
    console.log('data', data)
    let mtInfo = data.mtInfo
    let mtStr = ''
    mtStr += `<tr>
                <td>${mtInfo.name || ''}</td>
                <td>${mtInfo.code || ''}</td>
                <td>${mtInfo.model || ''}</td>
                <td>${mtInfo.specifications || ''}</td>
                <td>${mtInfo.unit || ''}</td>
                <td>${mtInfo.create_name + ' ' + moment(mtInfo.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
            </tr>`
    $("#packingOfMt .tbl_mt tbody").html(mtStr)


    let supplierStr = ''
    if (type === 'location') {
        let theadStr =    `<tr><td>库位</td><td>供应商</td><td>可使用的截止日期</td><td>数量</td></tr>`
        let name = data.supplier_id?`${data.full_name}/${data.supplier_name}/${data.code_name}`:'难以区分供应商的本材料'
        supplierStr = `<tr>
                <td>${data.location_code + '-' + data.shelf_code}</td>
                <td>${name}</td>
                <td>${moment(data.expiration_date).format("YYYY-MM-DD")}</td>
                <td>${data.amount}</td>
            </tr>`
        $("#packingOfMt .tbl_supplier thead").html(theadStr)
        $("#packingOfMt .tbl_supplier tbody").html(supplierStr)
    } else {
        let theadStr =    `<tr><td>供应商</td><td>可使用的截止日期</td><td>数量</td></tr>`
        let name = data.supplier_id?`${data.full_name}/${data.supplier_name}/${data.code_name}`:'难以区分供应商的本材料'
        supplierStr = `<tr>
                <td>${name}</td>
                <td>${moment(data.expiration_date).format("YYYY-MM-DD")}</td>
                <td>${data.amount}</td>
            </tr>`
        $("#packingOfMt .tbl_supplier thead").html(theadStr)
        $("#packingOfMt .tbl_supplier tbody").html(supplierStr)
    }

    $.ajax({
        url: $.webRoot + '/iwre/getPackByLocation',
        data: {
            id: data.id
        }
    }).then(res => {
        let packStr = ''
        if (res.length > 0) {
            let count = 0
            for (let item of res) {
                let itemData ={...data, ...{pack_id: item.id}} // 不是packingID(包装id),是上个接口返回的id
                packStr +=   `<tr id="${item.packaging}">
                                <td>${item.packaging?'没有包装':'没有包装'}</td>
                                <td>--</td>
                                <td>${item.packaging_count || 0}</td>
                                <td><span class="link-blue" type="packing" onclick="setStorage($(this))">${item.packaging_count || 0}</span></td>
                                <td class="hd">${JSON.stringify(itemData)}</td>
                            </tr>`
                count += (item.packaging_count || 0)
            }
            $("#packingOfMt .tbl_packing tfoot .countNum").html(count)
        } else {
            packStr =   `<tr id="0">
                            <td>没有包装</td>
                            <td>--</td>
                            <td>0</td>
                            <td>0</td>
                        </tr>`
            $("#packingOfMt .tbl_packing tfoot .countNum").html(0)
        }
        $("#packingOfMt .tbl_packing tbody").html(packStr)

    })
}

// creator: 张旭博，2025-02-23 06:12:25， 
function setStorage(selector) {
    $("#setStorage_zhineng .visible_packingInfo").hide()
    let mode = $("body").data('whmode')
    if (mode === 1) {
        let prevData = selector.parents('tr').find('.hd').html()
        prevData = JSON.parse(prevData)
        // 渲染材料部分
        let mtInfo = prevData.mtInfo
        let mtStr = `<tr>
                                <td>${mtInfo.name || ''}</td>
                                <td>${mtInfo.code || ''}</td>
                                <td>${mtInfo.model || ''}</td>
                                <td>${mtInfo.specifications || ''}</td>
                                <td>${mtInfo.unit || ''}</td>
                                <td>${mtInfo.create_name + ' ' + moment(mtInfo.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                            </tr>`
        $("#setStorage_zhineng .tbl_mt tbody").html(mtStr)
        // 渲染供应商部分（3种来源展示不同）
        let type = selector.attr("type")
        let params = {
            material: prevData.material_id,
            supplier: prevData.supplier_id,
            supplierMaterial: prevData.supplier_material,
        }
        let theadStr = ''
        let supplierStr = ''
        if (type === 'supplier_amount' || type === 'supplier_count') {
            theadStr = `<tr><td>供应商</td><td>可使用的截止日期</td><td>数量</td></tr>`
            let expirationDate = moment(prevData.expiration_date).format("YYYY-MM-DD")
            let amount = prevData.amount
            if(type === 'supplier_amount') {
                params.expirationDate = expirationDate
            } else {
                let rowSpan = selector.parents("td").attr('rowspan')
                if (rowSpan > 1) {
                    expirationDate = '多个'
                    amount = selector.text()
                }
            }
            supplierStr =   `<tr>
                                <td>${prevData.full_name}/${prevData.supplier_name}/${prevData.code_name}</td>
                                <td>${expirationDate}</td>
                                <td>${amount}</td> 
                            </tr>`
            $("#setStorage_zhineng .tbl_supplier thead").html(theadStr)
            $("#setStorage_zhineng .tbl_supplier tbody").html(supplierStr)
        }else if (type === 'packing') {
            let expiration = moment(prevData.expiration_date).format("YYYY-MM-DD")
            $("#setStorage_zhineng .visible_packingInfo").show()
            let packingTbodyStr = selector.parents('tr').clone(false)
            $("#setStorage_zhineng .tbl_packing tbody").html(packingTbodyStr)
            theadStr = `<tr><td>供应商</td><td>可使用的截止日期</td></tr>`
            supplierStr =   `<tr>
                                <td>${prevData.full_name}/${prevData.supplier_name}/${prevData.code_name}</td>
                                <td>${expiration}</td>
                            </tr>`
            $("#setStorage_zhineng .tbl_supplier thead").html(theadStr)
            $("#setStorage_zhineng .tbl_supplier tbody").html(supplierStr)
            params.packaging = prevData.pack_id
            params.expirationDate = expiration
        }
        bounce_Fixed2.show($("#setStorage_zhineng"))

        $.ajax({
            url: $.webRoot + '/iwre/currentStockByDate',
            data: params
        }).then(function(res) {
            let data = res
            let storageStr = ''
            for (let i = 0; i < data.length; i += 2) {
                storageStr += `<tr>
                                <td>${data[i].location_code + '-' + data[i].shelf_code}</td>
                                <td>${data[i].amount}</td>
                                <td>${(i + 1) < data.length ? (data[i + 1].location_code + '-' + data[i + 1].shelf_code) : ''}</td>
                                <td>${(i + 1) < data.length ? data[i + 1].amount : ''}</td>
                            </tr>`
            }
            $("#setStorage_zhineng .storageNum").html(data.length)
            $("#setStorage_zhineng .tbl_storage tbody").html(storageStr)

        })
    } else {
        bounce_Fixed2.show($("#setStorage"))
    }
}

// created:hxz 2020-03-11 搜索物料
function search(thisObj , type) { // type : 1-非暂停的，2-暂停的
    var keyword = thisObj.siblings('input').val();
    if(keyword == ""){
        layer.msg("请录入需要搜索的内容");
        return false;
    }
    if(type == 1){
        getPdByCategory('', 4, keyword,1,20)
    }else if(type == 2){
        getPdByCategory('', 2, keyword,1 , 20)
    }
}
// created:hxz 2020-09/17 首页返回上一级/返回全部
function backPreCat(num) {
    var index = $('.curCat .go2Cat').length - 2; // 返回全部
    if(num === 1){ index = 0;   }
    $('.curCat .go2Cat:eq('+ index +')').click();
}
function getIndexData() {
    $(".dataFilter").hide()
    $("#singleHouse tbody").html('');
    $.ajax({
        url:"../mt/index" ,
        data:{ 'org': sphdSocket.org.id },
        success:function(data){
            let getData = data;
            $(".stockHandle .waitingFor").html(getData.waitingFor)
            $(".stockHandle .warehouse_count").html(getData.warehouse_count)

            $(".stockHandle .material_count").html(getData.material_count)
            let mode = $("body").data('whmode')
            if (mode === 1) {
                $(".stockHandle .shelf_count").html(getData.shelf_count)
                $(".stockHandle .location_count").html(getData.location_count)
                $(".stockHandle .location_count_empty").html(getData.location_count_null)
            } else {
                $(".stockHandle .shelf_count").html('--')
                $(".stockHandle .location_count").html('--')
                $(".stockHandle .location_count_empty").html('--')
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
function setZero(num){
    return Number(num) > 0 ? num : 0 ;
}
// creator: hxz，2020-02-03 17:00:06，去处理
function stockJump(item) {
    $(".container_item").children("div").hide();
    $(".container_item").children("div").eq(item).show();
    if (item == '0' || item == 0) {
        $(".stockJump").hide();
    } else{
        $(".stockJump").show();
    }
    jumpTo(item, 1, 20);
}
function backIndex() {
    stockJump(0)
}
function jumpTo(num, cur, pageTotal) {
    switch (num) {
        case 0:
            getIndexData();
            break;
        case 1:
            getWaitingForLocationList(cur, pageTotal);
            break;
        case 2:
            resetLocationList(cur, pageTotal);
            break;
        case 3:
            $(".curCat").html("");
            getPdByCategory('', 4, '', 1 , 20);
            break;
        case 4:
            getStorageAcceptList(cur, pageTotal,0);
            break;
        case 5:
            getOutStorageList(cur, pageTotal);
            break;
        case 6:
            $("#checkInOutInfo .ty-secondTab li").eq(0).click();
            break;
        case 7:
            var searchKey = $.trim($("#searchKey").val());
            keywordSearch(searchKey, cur, pageTotal);
            break;
    }
}
// 出入库统计
function getLogList(cur,per,index){
    let url = "../inStock/inList"; //入库流水
    let data = { 'pageNum':cur , 'per':per }
    if(index == 1){
        url = "../picking/getPickingRecordList.do"
        data = { 'currPage':cur , 'pageSize':per }
    }
    $(`#checkInOutInfo .p${index}`).show().siblings().hide()
    $.ajax({
        "url":url,
        "data": data,
        success:function (res) {
            let info = { 'index': index }
            let str = ``
            let sunPage = 0
            if(index == 1){ // 领料
                let list1 = res.data || []
                sunPage = res.totalPage
                list1.forEach(function(item){
                    str += `
                     <tr>
                        <td>${ item.code }</td>
                        <td>${ item.name }</td>
                        <td>${ item.specifications }</td>
                        <td>${ item.model }</td>
                        <td>${ item.unit }</td>
                        <td>${ item.quantityFact }</td>
                        <td>${ item.departName }</td>
                        <td>${ item.createName }${ new Date(item.deliveryFact).format("yyyy-MM-dd hh:mm:ss") } </td>
                     </tr>
                `
                })
            }else{ // 入库
                let list0 = res.data || []
                sunPage = res.totalPage
                list0.forEach(function(item){
                    str += `
                 <tr>
                    <td>${ item.code }</td>
                    <td>${ item.name }</td>
                    <td>${ item.specifications }</td>
                    <td>${ item.model }</td>
                    <td>${ item.unit }</td>
                    <td>${ item.state == 8 ? item.quantity_fact :item.check_quantity  }</td>
                    <td>${ item.supplier_name }</td>
                    <td>${ item.check_name }${ new Date(item.check_date).format("yyyy-MM-dd hh:mm:ss") } </td>
                 </tr>
                `
                })
            }
            $(`#checkInOutInfo .p${index} tbody`).html(str)
            setPage( $(`#pageP${index}`), cur , sunPage , 'pickOrInList', JSON.stringify(info))

        }
    })
}
/*-----获取列表-----*/
// creator: hxz，2020-02-18 11:07:32，获取待录入初始库存列表
function getWaitingForLocationList(currPage,pageSize){
    $("#waitingForEntry tbody").html('');
    $("#ye4").html("");
    $("#waitingTotle").html("0");
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "state":3, "pageNum":currPage, "per":pageSize  };
    $.ajax({
        url:"../mt/list" ,
        data:data ,
        success:function(resData){
            var totalPage = Number(data["totalCount"]) > 0 ? data["totalCount"] : 1 ;//总页数
            var curr = currPage ;//当前页
            var jsonStr = JSON.stringify(data) ;
            setPage( $("#ye4") , curr ,  totalPage , "getWaitingForLocationList", jsonStr );

            var list = resData.data || [];
            if (list.length > 0) {
                var html = '';
                $("#waitingTotle").html(list.length);
                for(var a=0;a<list.length;a++){
                    html +=
                        '<tr data-info=\''+ JSON.stringify(list[a]) +'\'>' +
                        '    <td>'+ list[a].name +'</td>' +
                        '    <td>'+ list[a].code +'</td>' +
                        '    <td>'+ list[a].model +'</td>' +
                        '    <td>'+ list[a].specifications +'</td>' +
                        '    <td class="createInfo">'+ list[a].create_name + '  ' + new Date(list[a].create_date).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ (list[a].unit || "") +'</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue" onclick="enterInitialStock($(this))">录入初始库存数量/库位</span>' +
                        '    </td>' +
                        '</tr>'
                }
                $("#waitingForEntry tbody").html(html);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: hxz，2020-02-19 11:13:11，重新选择库位
function resetLocationList(currPage,pageSize){
    $("#resetStock tbody").html('');
    $("#ye2").html("");
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":'', "state":5 , "pageNum":currPage, "per":pageSize };
    $.ajax({
        // url:"../finished/getPdProductList.do" ,
        "url":"../mt/list",
        data: data ,
        success:function(data){
            if (data.code == '200') {
                // 设置分页
                var totalPage = data["totalCount"];//总页数
                var curr = currPage ;//当前页
                var partOne = $(".container_item>div:visible").data('part');
                var jsonStr = JSON.stringify({"param": partOne}) ;
                setPage( $("#ye2") , curr ,  totalPage , "resetLocationList", jsonStr );

                var html = '';
                var list = data.data;
                for (var a=0;a<list.length;a++) {
                    html +=
                        ' <tr data-id="'+ list[a].id +'">' +
                        '    <td>'+ list[a].name +'</td>' +
                        '    <td>'+ list[a].code +'</td>' +
                        '    <td>'+ list[a].model +'</td>' +
                        '    <td>'+ list[a].specifications +'</td>' +
                        '    <td class="createInfo">'+ list[a]['create_name'] +'&nbsp;&nbsp;'+ new Date(list[a]['create_date']).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[a].unit +'</td>' +
                        '    <td>' +
                        '        <span class="ty-color-blue" onclick="reSelectStock($(this), 1)">重新选择库位</span>' +
                        '        <span class="hd" >'+ JSON.stringify(list[a]) +'</span>' +
                        '    </td>' +
                        '</tr>';
                }
                $("#resetStock tbody").html(html);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: hxz，2020-02-19 11:13:11，按类别查看成品列表
var mtCategories = [] ;
function getPdByCategory(category, state, keyword, cur, per){
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时 4-原辅材料库
    // keyword : 查询的代号或名称
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":keyword , "pageNum":cur, "per":per };
    if (state){  data['state'] =  state  }
    if (category){  data['category'] =  category  }
    $.ajax({
        "url":"../mt/list",
        "data":data,
        success:function (res) {
            var totalPage = res["totalCount"];//总页数
            var curr = cur ;//当前页
            var jsonStr = JSON.stringify(data) ;
            setPage( $("#ye1") , curr ,  totalPage , "getPdByCategorySn", jsonStr );

            var categories = res['categories'];
            var count = res['count'];
            var mtArr = res['data'];
            var enabledCount = res['enabledCount'];
            $("#enabledCount").html(enabledCount);
            var pid = null;
            var path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span>";
            if(category){  pid = category;  }
            if(!pid){
                mtCategories = categories; mtCategoriesTree();
            }

            var strMt = "" ;
            if(mtArr && mtArr.length >0){
                for(var j = 0 ; j < mtArr.length ; j++){
                    var item = mtArr[j]
                    let current_stock = ( item['current_stock'] && parseFloat(Number( item['current_stock']).toFixed(4)))||0;
                    let minimum_stock = ( item['minimum_stock'] && parseFloat(Number( item['minimum_stock']).toFixed(4))) ||0;
                    strMt += `<tr>
                                <td>${item.name}</td>
                                <td>${item.code}</td>
                                <td>${item.model}</td>
                                <td>${item.specifications}</td>
                                <td>${item.create_name + ' ' + moment(item.create_date).format("YYYY-MM-DD HH:mm:ss")}</td>
                                <td>${item.unit}</td>
                                <td>${minimum_stock}</td>
                                <td><span data-type='currentStock' class="link-blue">${current_stock}</span></td>
                                <td><span>${setVal(item['initial_stock'], 1)}</span></td>
                                <td><span data-type='locationNumber' class="link-blue">${item.location_number || 0}</span></td>
                                <td>
                                    <span class="hd">${JSON.stringify(item)}</span>
                                    <span data-type="scanCtrl" class="link-blue">查看</spandata-type>
                                </td>`
                }
            }

            var conStr = ".mainCon1  tbody";
            if(state == 2){ // 暂停
                conStr = ".mainCon2  tbody";
                $("#bottom2").hide();  $("#bottom1").show();
                if(keyword){
                    path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span><span><span>查找代号或名称含“"+ keyword +"”的原辅材料 > </span></span>";
                    $(".curCat").append(path);
                }
            }else if(state == 4){ // 原辐材料库
                $("#bottom2").show();  $("#bottom1").hide();
                if(keyword){
                    path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span><span><span>查找代号或名称含“"+ keyword +"”的原辅材料 > </span></span>";
                }else if(category){
                    for(var i = 0 ; i < mtCategories.length ; i++){
                        if(category == categories[i]['id']){
                            path = "<span class='go2Cat'><span>"+ categories[i]['name'] +" > </span><span class='hd'>"+ categories[i]['id']  +"</span></span>"
                        }
                    }
                }
                $(".curCat").append(path);

            }else{
                if(keyword){
                    path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span><span class='ty-color-blue'><span>查找代号或名称含“"+ keyword +"”的原辅材料 > </span></span>";
                    $(".curCat").html(path);
                }else{
                    $(".curCat").append(path);
                }

            }
            $(conStr).html(strMt);

            $("#catAll").html("");
            for(var i = 0 ; i < categories.length ; i++){
                var pCat = categories[i]['parent'];
                if(pCat == pid){
                    var count = 0 ;
                    $("#catTree").find(".treeItem"+ categories[i]['id']).find(".countItem").each(function(){
                        count += Number($(this).html());
                    });
                    categories[i]['count'] = count ;
                    var str = "<li class='catID"+ categories[i]['id'] + "' data-id='"+ categories[i]['id'] +"'><span class='name'>"+ categories[i]['name'] +"</span><span class=\"catNum\">"+ categories[i]['count'] +"</span></li>"
                    $("#catAll").append(str)
                }
                if(category == categories[i]['id']){
                    path = "<span class='go2Cat'><span>"+ categories[i]['name'] +" > </span><span class='hd'>"+ categories[i]['id']  +"</span></span>"
                }
            }
            var allCount = 0
            $("#catTree").find(".countItem").each(function(){
                allCount += Number($(this).html());
            });
            $(".mtNum").html(allCount);
        }
    })
}
function getPdByCategoryByPage(category, state, keyword, cur, per){
    // state:查询总列表时不传,  1-查询待确认定点信息时, 2-查询暂停采购的材料时, 3-查询待录入初始库存数量时 4-原辅材料库
    // keyword : 查询的代号或名称
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":keyword , "pageNum":cur, "per":per };
    if (state){  data['state'] =  state  }
    if (category){  data['category'] =  category  }
    $.ajax({
        "url":"../mt/list",
        "data":data,
        success:function (res) {
            var totalPage = res["totalCount"];//总页数
            var curr = cur ;  //当前页
            var jsonStr = JSON.stringify(data) ;
            setPage( $("#ye1") , curr ,  totalPage , "getPdByCategorySn", jsonStr );

            var categories = res['categories'];
            var count = res['count'];
            var mtArr = res['data'];
            var enabledCount = res['enabledCount'];
            $("#enabledCount").html(enabledCount);
            var pid = null;
            var path = "<span class='go2Cat'><span>全部 > </span><span class='hd'></span></span>";
            if(category){  pid = category;  }
            if(!pid){
                mtCategories = categories; mtCategoriesTree();
            }

            var strMt = "" ;
            if(mtArr && mtArr.length >0){
                for(var j = 0 ; j < mtArr.length ; j++){
                    var item = mtArr[j]
                    let current_stock = ( item['current_stock'] && parseFloat(Number(item['current_stock']).toFixed(4))) || 0 ;
                    let minimum_stock = ( item['minimum_stock'] && parseFloat(Number(item['minimum_stock']).toFixed(4))) || 0 ;
                    strMt += " <tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ item['code'] +"</td>" +
                        "    <td>"+ item['model'] +"</td>" +
                        "    <td>"+ item['specifications'] +"</td>" +
                        "    <td>"+ item['create_name'] + (new Date(item['create_date']).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>"+ item['unit'] +"</td>" +
                        "    <td>"+ minimum_stock +"</td>" +
                        "    <td>"+ current_stock +"</td>" +
                        "    <td class='ty-td-control'><span data-type='initialStock' class=\"ty-color-blue \">"+ setVal(item['initial_stock'], 1) +"</span></td>" +
                        "    <td class='ty-td-control'><span data-type='locationNumber' class=\"ty-color-blue \">"+ setVal(item['location_number'], 1) +"个</span></td>" +
                        "    <td>" +
                        "        <span class=\"hd\">"+JSON.stringify(item)+"</span>" +
                        "        <span data-type=\"scanCtrl\" class=\"ty-color-blue \">查看</span>" +
                        "    </td>" +
                        "</tr>";
                }
            }

            var conStr = ".mainCon1  tbody";
            if(state == 2){ // 暂停
                conStr = ".mainCon2  tbody";

            }
            $(conStr).html(strMt);

            $("#catAll").html("");
            for(var i = 0 ; i < categories.length ; i++){
                var pCat = categories[i]['parent'];
                if(pCat == pid){
                    var count = 0 ;
                    $("#catTree").find(".treeItem"+ categories[i]['id']).find(".countItem").each(function(){
                        count += Number($(this).html());
                    });
                    categories[i]['count'] = count ;
                    var str = "<li class='catID"+ categories[i]['id'] + "' data-id='"+ categories[i]['id'] +"'><span class='name'>"+ categories[i]['name'] +"</span><span class=\"catNum\">"+ categories[i]['count'] +"</span></li>"
                    $("#catAll").append(str)
                }
                if(category == categories[i]['id']){
                    path = "<span class='go2Cat'><span>"+ categories[i]['name'] +" > </span><span class='hd'>"+ categories[i]['id']  +"</span></span>"
                }
            }
            var allCount = 0
            $("#catTree").find(".countItem").each(function(){
                allCount += Number($(this).html());
            });
            $(".mtNum").html(allCount);

        }
    })
}

// creator:hxz 2020-04-09 创建种类树
function mtCategoriesTree(){
    var catArr = mtCategories ;
    $("#catTree").html("");
    for(var i = 0 ; i <catArr.length ; i++){
        var id = catArr[i]['id']
        var pid = catArr[i]['parent']
        if(pid){
            var str = "<span class='treeItem treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").find(".treeItem"+pid).append(str);

        }else{ // 一级类别
            var str = "<span class='treeItem treeItem"+ id +"'><span class='countItem'>"+catArr[i]['count']+"</span></span>";
            $("#catTree").append(str);
        }
    }
}
// creator ：侯杏哲 2020-04-26  空赋值
function setVal(val, num) {
    var str = val?val:""
    if(str == "" && num ==1){ str = "0" }
    return str
}
// creator: hxz 2020-04-07 查看暂停采购
function goPause(){
    listData = { 'category':false , 'state':2 , 'keyword':'' }
    getPdByCategory('', 2, '',1 , 20)
    toggleSuspend(2)
}
// created:hxz 2020-03-11 切换主页与暂停页
function toggleSuspend(num){
    $('.mainCon' + num).show().siblings().hide();
}

// creator: hxz，2020-02-25 09:37:54，搜索
function keywordSearch(keyword,currPage,pageSize) {
    $("#resultList tbody").html('');
    $("#ye3").html("");
    var oid = sphdSocket.user.oid
    var data = {"oid":oid , "keyword":keyword, "type":1, "state":4, "pageNum":currPage, "per":pageSize };
    $.ajax({
        url:"../mt/list" ,
        data:data,
        success:function(data){
            //设置分页
            var totalPage = data["totalCount"];//总页数
            var curr = currPage ; //当前页
            var jsonStr = JSON.stringify(data) ;
            setPage( $("#ye3") , curr ,  totalPage , "keywordSearch", jsonStr );

            var html = '';
            var list = data.data || [];
            for (var a=0;a<list.length;a++) {
                html +=
                    ' <tr data-id="'+ list[a].id +'">' +
                    '    <td>'+ list[a].name +'</td>' +
                    '    <td>'+ list[a].code +'</td>' +
                    '    <td>'+ list[a].model +'</td>' +
                    '    <td>'+ list[a].specifications +'</td>' +
                    '    <td class="createInfo">'+ list[a]['create_name'] + '  '+ new Date(list[a]['create_date']).format('yyyy/MM/dd hh:mm:ss')  +'</td>' +
                    '    <td>'+ list[a].unit +'</td>' +
                    '    <td>'+ (list[a].minimum_stock || 0) +'</td>' +
                    '    <td>'+ (list[a].current_stock || 0) +'</td>' +
                    '    <td><span class="ty-color-blue" data-type="initialStock">' + (list[a].initial_stock||0) + '</span></td>' +
                    '    <td><span class="ty-color-blue" data-type="locationNumber">' + (list[a].location_number || 0) + '个</span></td>' +
                    '    <td>' +
                    '        <span class="hd">' + JSON.stringify(list[a]) + '</span>' +
                    '        <span class="ty-color-blue cz_handle">查看' +
                    '           <ul class="hel" style="display: none">' +
                    '            <li class="ulli ty-disabled"><span>盘点记录</span></li>' +
                    '            <li class="ulli ty-disabled"><span>出入库记录</span></li>' +
                    '            <li class="ulli" onclick="initStockUpdateRecord('+ list[a].id + ')"><span>初始库存修改记录</span></li>' +
                    '            <li class="ulli" onclick="safeStockUpdateRecord('+ list[a].id + ')"><span>最低库存修改记录</span></li>' +
                    '        </ul>' +
                    '        </span>' +
                    '    </td>' +
                    '</tr>';
            }
            $("#resultList tbody").html(html);
        }
    });
}
// creator: hxz，2020-01-20 09:38:33，录入初始库存数量/库位
function enterInitialStock(obj) {
    //初始化
    $("#initialStockList tbody").html('');
    $(".checkCondition .fa").removeClass('fa-check-square-o').addClass("fa-square-o");
    $(".storesAble").find("li").eq(0).siblings().remove();
    $("#enterInitialStock select").val(0);
    $("#enterInitialStock input").val("");
    var info = obj.parents('tr').data('info');
    $('#enterInitialStock').data("id",info.id);
    $('#enterInitialStock .kuList').show().siblings().hide();
    $("#option").val("");
    var goodsHtml =
        '<tr>' +
        '    <td>'+ info.name  + '</td>' +
        '    <td>'+ info.code  + '</td>' +
        '    <td>'+ info.model  + '</td>' +
        '    <td>'+ info.specifications  + '</td>' +
        '    <td class="createInfo">'+ info.create_name + '&nbsp;&nbsp;' + new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
        '    <td>'+ info.unit  + '</td>' +
        '    <td><span class="floatLocation">0</span></td>' +
        '    <td><span class="holdLocation">0</span>个</td>' +
        '</tr>';
    $("#initialStockList tbody").html(goodsHtml);
    $(".goodsUnit").html(info.unit);
    $.ajax({
        "url":"../mt/locations" ,
        "data": { 'org': sphdSocket.org.id },
        success:function(res){
            mtLocationList = res.data||[] ;
            // var options = '<option value="0"></option>';
            // if (mtLocationList.length > 0){
            //     for(var q = 0 ; q < mtLocationList.length ; q++){
            //         options += "<option value='"+ mtLocationList[q]["id"] +"'>"+ mtLocationList[q]["location_code"] +"</option>";
            //     }
            // }
            licationSelectArr = [] ; // 重置已选择的库位id
            // 获取供应商列表
            getSuppliers(info , mtLocationList)
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
    bounce_Fixed.show($("#enterInitialStock"));
    bounce_Fixed.everyTime('0.5s','enterStock',function(){
        if($(".checkCondition:visible .fa").hasClass('fa-square-o')){
            if ($(".notSelectStores:visible").length > 0){
                $(".notSelectStores").hide();
                $(".selectSect:visible .storesAble").show();
            }
            var fillNum = 0, storeNum = 0;
            $(".storesAble:visible input").each(function(){
                if ($(this).val() != '') fillNum += Number($(this).val());
            });
            $(".storesAble:visible select").each(function(){
                var tt = $(this).val();
                if ($(this).val() != null && $(this).val() !=0 && $(this).val() !="") {
                    storeNum++;
                }
            });
            $(".holdLocation").html(storeNum);
            $(".floatLocation").html(fillNum);
        } else if($(".checkCondition .fa").hasClass('fa-check-square-o')){
            if ($(".notSelectStores:visible").length <= 0){
                $(".notSelectStores").show();
                $(".selectSect:visible .storesAble").hide();
            }
            $(".holdLocation:visible").html('0');
            $(".floatLocation:visible").html(Number($(".onlyNumber:visible").val()));
        }
    });
}
// creater: hxz 2020-04-21 获取某物料的供应商列表
function getSuppliers(info , locationArr) {
    var mtId = info.id
    var unit = info.unit
    var data = { 'id': mtId  }
    $.ajax({
        "url":"../mt/suppliers",
        "data":data,
        success:function (res) {
            var list = res['data'] || [];
            var str = "" , localStr = "<option value='0'>请选择库位</option>";
            if(list.length>0){
                $("#initialStockList").data('supNum', 1);
                $(".hasSup").show().siblings().hide();
                for(var q = 0 ; q < locationArr.length ; q++){
                    localStr += "<option value='"+ locationArr[q]["id"] +"'>"+ locationArr[q]["location_code"] +"</option>";
                }
                $("#supNum").html(list.length)
                for(var i = 0 ; i <list.length ; i++){
                    var item = list[i];
                    str += "<li data-mtid='"+ mtId +"' data-mtsupid='"+ item['material_supplier_id'] +"' class=\"supKuItem\">" +
                        "    <div>" +
                        "        <div> <p>供应商： "+ item["full_name"] +"</p> <p>简称： "+ item["name"] +"　 代号："+ item["code_name"]  +"</p>  </div>" +
                        "        <div> <span class=\"addOneStore\" onclick=\"newStore($(this))\">增加新库位</span> </div>" +
                        "    </div>" +
                        "    <div class=\"kuItem\">" +
                        "        <div>" +
                        "            <span class=\"gapRt\">库位</span><select onchange=\"fillStore($(this))\" class=\"addInitStore\">"+ localStr +"</select>" +
                        "            <span class=\"ty-color-blue hd\" onclick=\"seeStoresDetail($(this))\">查看该库位情况</span>" +
                        "        </div>" +
                        "        <div>" +
                        "            <span class=\"gapRt\">数量</span> <input type=\"text\" onkeyup=\"clearNoNum(this)\"/><span class=\"goodsUnit\">"+ unit +"</span>" +
                        "        </div>" +
                        "    </div>" +
                        "</li>";
                }
                str += "<li data-mtid='"+ mtId +"' data-mtsupid='-1' class=\"supKuItem\">" +
                    "    <div>" +
                    "        <div> <p>难以区分供应商的本材料</p></div>" +
                    "        <div> <span class=\"addOneStore\" onclick=\"newStore($(this))\">增加新库位</span> </div>" +
                    "    </div>" +
                    "    <div class=\"kuItem\">" +
                    "        <div>" +
                    "            <span class=\"gapRt\">库位</span><select onchange=\"fillStore($(this))\" class=\"addInitStore\">"+ localStr +"</select>" +
                    "            <span class=\"ty-color-blue hd\" onclick=\"seeStoresDetail($(this))\">查看该库位情况</span>" +
                    "        </div>" +
                    "        <div>" +
                    "            <span class=\"gapRt\">数量</span> <input type=\"text\" onkeyup=\"clearNoNum(this)\"/><span class=\"goodsUnit\">"+ unit +"</span>" +
                    "        </div>" +
                    "    </div>" +
                    "</li>";
            }else{
                $(".noSup").show().siblings().hide();
                $("#initialStockList").data('supNum', 0);
                $(".toggleCheck0").show().siblings().hide();
                $("#toggleCheckNum").val(0);
                $("#toggleCheck").attr("class","fa fa-square-o");
                $(".toggleCheck0 .kuItem").remove();
                newStore($("#addOneStore3"));
            }
            $(".kuList").html(str);
        }
    })
}
// creater: hxz 2020-04-21 重新设置库位 - 获取某物料的供应商列表
function getResetSuppliers(info , locationArr) {
    var mtId = info.id
    var unit = info.unit
    var data = { 'id': mtId  }
    $.ajax({
        "url":"../mt/suppliers",
        "data":data,
        success:function (res) {
            var list = res['data'] || [];
            var str = "" , localStr = "<option value='0'>请选择库位</option>";
            if(list.length>0){
                for(var q = 0 ; q < locationArr.length ; q++){
                    localStr += "<option value='"+ locationArr[q]["id"] +"'>"+ locationArr[q]["location_code"] +"</option>";
                }
                for(var i = 0 ; i <list.length ; i++){
                    var item = list[i];
                    str += "<li data-mtid='"+ mtId +"' data-mtsupid='"+ item['material_supplier_id'] +"' class=\"supKuItem\">" +
                        "    <div>" +
                        "        <div> <p>供应商： "+ item["full_name"] +"</p> <p>简称： "+ item["name"] +"　 代号："+ item["code_name"]  +"</p>  </div>" +
                        "        <div> <span class=\"addOneStore\" onclick=\"newStore($(this))\">增加新库位</span> </div>" +
                        "    </div>" +
                        "    <div class=\"kuItem\">" +
                        "        <div>" +
                        "            <span class=\"gapRt\">库位</span><select onchange=\"fillStore($(this))\" class=\"addInitStore\">"+ localStr +"</select>" +
                        "            <span class=\"ty-color-blue hd\" onclick=\"seeStoresDetail($(this))\">查看该库位情况</span>" +
                        "        </div>" +
                        "        <div>" +
                        "            <span class=\"gapRt\">数量</span> <input type=\"text\" onkeyup=\"clearNoNum(this)\"/><span class=\"goodsUnit\">"+ unit +"</span>" +
                        "        </div>" +
                        "    </div>" +
                        "</li>";
                }
                str += "<li data-mtid='"+ mtId +"' data-mtsupid='-1' class=\"supKuItem\">" +
                    "    <div>" +
                    "        <div> <p>难以区分供应商的本材料</p></div>" +
                    "        <div> <span class=\"addOneStore\" onclick=\"newStore($(this))\">增加新库位</span> </div>" +
                    "    </div>" +
                    "    <div class=\"kuItem\">" +
                    "        <div>" +
                    "            <span class=\"gapRt\">库位</span><select onchange=\"fillStore($(this))\" class=\"addInitStore\">"+ localStr +"</select>" +
                    "            <span class=\"ty-color-blue hd\" onclick=\"seeStoresDetail($(this))\">查看该库位情况</span>" +
                    "        </div>" +
                    "        <div>" +
                    "            <span class=\"gapRt\">数量</span> <input type=\"text\" onkeyup=\"clearNoNum(this)\"/><span class=\"goodsUnit\">"+ unit +"</span>" +
                    "        </div>" +
                    "    </div>" +
                    "</li>";
                $("#hasSup").html(str);

            }else{
                $(".noSup").show().siblings().hide();
                $("#initialStockList").data('supNum', 0);
                $(".toggleCheck0").show().siblings().hide();
                $("#toggleCheckNum").val(0);
                $("#toggleCheck").attr("class","fa fa-square-o");
                $(".toggleCheck0 .kuItem").remove();
                newStore($("#addOneStore3"));
            }
        }
    })
}

// creator: hxz，2020-02-27 09:09:06，获取库位列表
var mtLocationList = []
function getLocationList() {
    $.ajax({
        url:"../mt/locations" ,
        data: { 'org': sphdSocket.org.id },
        success:function(res){
            locationList = res.data ;

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: hxz，2020-08-04 15:02:03，录入初始库存数量/库位确定
function addInitialStock(){
    var isOk = true ; // 库位数量同时为空可以； 单一缺少不能提交
    var supNum = $("#initialStockList").data('supNum');
    var mtID = $('#enterInitialStock').data("id");
    var data = {   "mtID":mtID   }
    if(supNum == 1){ // 有供应商
        var option = $("#option").val();
        if( option == "1"){
            data['initialStock'] = $("#initKu").val();
            data['operation'] = 7;

        }else if(option == "2"){
            var arr = [] ;
            $(".Check2 .kuItem").each(function(){
                var location = $(this).find("select").val() ;
                var amount = $(this).find("input").val()
                if(Number(amount)>0){
                } else{
                    isOk = false;
                }
                if(location>0){
                }else{
                    isOk = false;
                }
                if(location>0 && amount!=""){
                    arr.push({
                        "location": location,
                        "amount": amount ,
                        "unit": $(this).find(".goodsUnit").val(),
                        "supplierMaterial":0,
                        "material":mtID,
                        "createName":sphdSocket.user.userName,
                        "creator":sphdSocket.user.userID
                    })
                }

            })
            data['params'] = JSON.stringify(arr) ;
            data['operation'] = 8;

        }else{
            var arr = [] ;
            $(".kuList .supKuItem").each(function(){
                var supplierMaterial = $(this).data("mtsupid") ;
                $(this).find(".kuItem").each(function(){
                    var location = $(this).find("select").val() ;
                    var amount = $(this).find("input").val()
                    if(Number(amount)>0){
                    } else{
                        isOk = false;
                    }
                    if(location>0){
                    }else{
                        isOk = false;
                    }
                    if(location>0 && amount!="") {
                        arr.push({
                            "location": location,
                            "amount": amount ,
                            "unit": $(this).find(".goodsUnit").val(),
                            "supplierMaterial":supplierMaterial,
                            "material":mtID,
                            "createName":sphdSocket.user.userName,
                            "creator":sphdSocket.user.userID
                        })
                    }
                })
            });
            data['params'] = JSON.stringify(arr) ;
            data['operation'] = 8;
        }

    }else{ // 没有供应商
        var toggleCheckNum = $("#toggleCheckNum").val();
        data['operation'] = 8;
        if(toggleCheckNum == '1'){
            data['operation'] = 7 ;
            data['initialStock'] = $("#initKu0").val();

        }else {
            var arr = [] ;
            $(".toggleCheck0 .kuItem").each(function(){
                var location = $(this).find("select").val() ;
                var amount = $(this).find("input").val()
                if(Number(amount)>0){
                } else{
                    isOk = false;
                }
                if(location>0){
                }else{
                    isOk = false;
                }
                if(location>0 && amount!="") {
                    arr.push({
                        "location": location,
                        "amount": amount ,
                        "unit": $(this).find(".goodsUnit").val(),
                        "supplierMaterial":0,
                        "material":mtID,
                        "createName":sphdSocket.user.userName,
                        "creator":sphdSocket.user.userID
                    })
                }

            });
            data['params'] = JSON.stringify(arr) ;
        }
    }
    if(!isOk){
        layer.msg("还有必填项尚未填写！");
        return false;
    }
    $.ajax({
        url:"../mt/stockAndLocation" ,
        data:  data ,
        success:function( data ){
            if(data.code == 200){
                stockJump(1)
                bounce_Fixed.cancel();
            }else{
                layer.msg("系统错误，请重试!") ;
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    })
}
// creator: hxz，2020-02-18 16:13:33，库位选择事件
var licationSelectArr = [] ;
function fillStore(obj) {
    var locationID = obj.val();
    var index_ = licationSelectArr.indexOf(locationID);
    if(index_ > -1){
        layer.msg('本次已选择了相同的库位！')
        obj.val('0')
    }else{
        licationSelectArr.push(locationID);
        if ( locationID!= 0){
            obj.next("span").show();
        } else{
            obj.next("span").hide();
        }
    }
}
// creator: hxz，2020-01-20 09:23:01，直接录入初始库存数量，暂不选择库位
function turnCheck (obj , num) {
    if (obj.hasClass('fa-square-o')) {
        $(".selectSect").find(".fa-check-square-o").removeClass('fa-check-square-o').addClass('fa-square-o');
        obj.removeClass('fa-square-o').addClass('fa-check-square-o');
        $(".Check" + num).show().siblings().hide();
        $("#option").val(num);
        if(num == 2){
            $(".Check2").find(".kuItem").remove();
            newStore($("#addOneStore2"))
        }
    }else{
        obj.removeClass('fa-check-square-o').addClass('fa-square-o');
        $(".selectSect .kuList").show().siblings().hide();
        $("#option").val("");
    }
}
// creator: hxz，2020-01-20 09:23:01，直接录入初始库存数量，暂不选择库位
function toggleCheck (obj ) {
    var num = 0
    if (obj.hasClass('fa-square-o')) {
        obj.removeClass('fa-square-o').addClass('fa-check-square-o');
        num = 1 ;
    }else{
        obj.removeClass('fa-check-square-o').addClass('fa-square-o');
        num = 0 ;
    }
    $("#toggleCheckNum").val(num);
    $(".toggleCheck" + num ).show().siblings().hide();
}
// creator: hxz，2020-09-298 09:23:01 重新选择库位
function reSetFa (obj,option) {
    // option - 0:不选择库位，1：不区分供应商
    var num = 2 ; // 0：不选库位 ， 1：不区分供应商，2：区分供应商
    if (obj.hasClass('fa-square-o')) {
        obj.removeClass('fa-square-o').addClass('fa-check-square-o');
        obj.parent().siblings().each(function () {
            $(this).find(".fa").attr("class", "fa fa-square-o");
        });
        if(option == 0){
            num = 0 ;
        } else if(option == 1){
            num = 1 ;
        }
    }else{
        obj.removeClass('fa-check-square-o').addClass('fa-square-o');
        num = 2 ;
    }
    $("#reSetFa").val(num);
    $(".storeList").children(":eq("+ num +")").show().siblings().hide();
}
// creator: hxz，2020-01-20 10:09:44，增加新库位
function newStore(thisObj) {
    var emty = 0;
    var pObj = thisObj.parents(".supKuItem") ;
    pObj.find("select").each(function(){
        if ($(this).val() == 0) emty++;
    });
    pObj.find("input:visible").each(function(){
        if($(this).val() == '') emty++;
    });
    if(emty > 0){
        $("#nullTip #nullTipMs").html('录完一个库位的数据后才能增加新库位!');
        bounce_Fixed2.show($("#nullTip"));
    } else{
        var options = '<option value="0"></option>';
        if (mtLocationList.length > 0){
            for(var q = 0 ; q < mtLocationList.length ; q++){
                options += "<option value='"+ mtLocationList[q]["id"] +"'>"+ mtLocationList[q]["location_code"] +"</option>";
            }
        }
        var unit = $('.bg-yellow:visible tbody tr').find("td").eq(5).html();
        var str =
            '<div class="kuItem">' +
            '    <div>' +
            '        <span class="gapRt">库位</span>' +
            '        <select onchange="fillStore($(this))">' + options +
            '        </select>' +
            '        <span class="ty-color-blue hd" onclick="seeStoresDetail($(this))">查看该库位情况</span>' +
            '    </div>' +
            '    <div>' +
            '        <span class="gapRt">数量</span>' +
            '        <input type="text" onkeyup="clearNoNum(this)" /><span class="gapLt">'+ unit +'</span>'+
            '    </div>'+
            '</div>';
        pObj.append(str);
    }
}
// creator: hxz，2020-01-20 10:22:39，查看该库位情况
function seeStoresDetail(obj) {
    $(".storesCreateInfo tbody").html('');
    $(".scienceCreateInfo tbody").html('');
    var id= obj.siblings("select").val();
    $.ajax({
        // url:"../finished/getLocationDetail.do" ,
        "url":"../mt/locations" ,
        "data":{ 'org': sphdSocket.org.id,  'id': id },
        success:function(res){
            bounce_Fixed2.show($("#seeStores"));
            var info = res.data[0];
            var part1 = '', part2 = '', list = res['mts'] || [];
            $("#seeStoresName").html( info.location_code);
            part1 +=
                '<tr>' +
                '    <td>'+ (info.warehouse_code || "")  +'</td>' +
                '    <td>'+ (info.region_code ||"") +'</td>' +
                '    <td>'+ (info.shelf_code || "") +'</td>' +
                '    <td>'+ (info.layer || "") +'</td>' +
                '    <td></td>' +
                '    <td class="createInfo">'+ (info.create_name || "") + '&nbsp;&nbsp;' + new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                '</tr>';
            $(".storesCreateInfo tbody").html(part1);
            if (list.length > 0){
                for(var t=0;t<list.length;t++) {
                    part2 += '<tr>' +
                        '     <td>'+ list[t].name +'</td>' +
                        '     <td>'+ list[t].code +'</td>' +
                        '     <td>'+ list[t].model +'</td>' +
                        '     <td>'+ list[t].specifications +'</td>' +
                        '     <td>'+ list[t].unit +'</td>' +
                        '     <td>'+ list[t].amount +'</td>' +
                        ' </tr>';
                }
                $(".scienceCreateInfo tbody").html(part2);
            }
        }
    })
}
// creator: hxz，2020-02-05 15:11:28，初始库存-修改记录
function initStockUpdateRecord(mtid) {
    $("#initUpdateRecord tbody").html('');
    stockRecordCommon(mtid, 2);
}
// creator: hxz，2020-02-05 15:11:28，最低库存-修改记录
function safeStockUpdateRecord(mtid) {
    $("#safeRecord tbody").html("");
    var html='';
    $.ajax({
        url:"../finished/getMinimumStockRecord.do" ,
        data:{ id: mtid  },
        success:function(data){
            if (data.list.length > 0) {
                var list = data.list;
                for (var i=0; i<list.length;i++){
                    html +=
                        '<tr>' +
                        '    <td>'+ list[i].updateName +'</td>' +
                        '    <td>'+ new Date(list[i].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[i].customerName +'</td>' +
                        '    <td>'+ list[i].before +'</td>' +
                        '    <td>'+ list[i].after +'</td>' +
                        '</tr>';
                }
                $("#safeRecord tbody").html(html);
            }
            bounce.show($("#safeStockUpdateRecord"));
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: hxz，2020-02-04 16:21:35，重新选择库位初始化
function reSelectStock(obj, openType) {
    // type : 1 表示从重新选择库位进去的
    var info = null ;
    openType = openType? openType: 0 ;
    $("#reselectLocation").data("openType", openType);
    var infoStr = $("#holdStockInfo tbody").html();
    if(openType === 1){
        editObj = obj ;
        info = JSON.parse(editObj.siblings(".hd").html()) ;
        infoStr =
            '<tr>' +
            '    <td>'+ info.name +'</td>' +
            '    <td>'+ info.code +'</td>' +
            '    <td>'+ setVal(info.model) +'</td>' +
            '    <td>'+ setVal(info.specifications) +'</td>' +
            '    <td class="createInfo">'+ setVal(info.create_name) + '&nbsp;&nbsp;' + (info.createName? new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss'): '') +'</td>' +
            '    <td>'+ setVal(info.unit) +'</td>' +
            '    <td>'+ (info.current_stock || 0) +'</td>' +
            '    <td>'+ (info.location_number || 0) +'个</td>' +
            '</tr>';
    }else{
        info = JSON.parse(editObj.parent().next().find(".hd").html()) ;
    }
    var btnName = obj.html();
    $("#reselectList tbody").html(infoStr);
    bounce_Fixed.show($('#reselectLocation'));
    $.ajax({
        "url":"../mt/locations" ,
        "data": { 'org': sphdSocket.org.id },
        success:function(res){
            mtLocationList = res.data || [] ;
            licationSelectArr = [] ; // 重置已选择的库位id
            // 初始化
            $("#hasSup").html("");
            $("#reselectLocation .fa").attr("class","fa fa-square-o");
            $("#noSup .kuItem").remove();
            newStore($("#addOneStore4"));
            $('#reselectLocation input').val("");
            if(btnName == "重新选择库位"){
                $("#reselectTtl").html('重新选择库位');
                $(".noChoose").hide();$(".hasChoose").show();
                var type = $("#holdStockSeeReset").data("type" ) ; // -1 ：区分 ， 0：不区分供应商

            }else{
                $("#reselectTtl").html('选择库位');
                $(".noChoose").show();$(".hasChoose").hide();
            }
            $(".hasChoose .case2").show();$(".hasChoose .case1").show();
            $("#hasSup").show().siblings().hide();
            getResetSuppliers(info , mtLocationList)

        }
    });
}
// creator: hxz，2020-02-24 16:02:15，重新选择库位提交
function updateLocation() {
    var isOk = true ; // 库位数量同时为空可以； 单一缺少不能提交
    var openType = $("#reselectLocation").data("openType");
    var info = null ;
    if(openType === 1){
        info = JSON.parse(editObj.siblings(".hd").html()) ;
    } else{
        info = JSON.parse(editObj.parent().next().find(".hd").html()) ;
    }
    var mtID = info['id'];
    var createName = sphdSocket.user.userName;
    var creator = sphdSocket.user.userID;
    var data = { "mtID":mtID ,  "operation":8 ,  "re":1 }
    var num = $("#reSetFa").val();
    if(num === "0"){
        data['operation'] = 9 ;
        data['params'] = JSON.stringify([]) ;
    }else if(num === "1"){ // 不区分供应商
        var arr = [] ;
        $("#noSup .kuItem").each(function(){
            var location = $(this).find("select").val() ;
            var amount = $(this).find("input").val()
            if(location>0 && amount==""){
                isOk = false;
            }  if(location==0 && amount!=""){
                isOk = false;
            }
            if(location>0 && amount!=""){
                arr.push({
                    "location": location,
                    "amount": amount ,
                    "unit": $(this).find(".goodsUnit").val(),
                    "supplierMaterial":0,
                    "material":mtID,
                    "createName":createName,
                    "creator":creator
                })
            }
        })
        data['params'] = JSON.stringify(arr) ;
    }else{ // 有供应商
        var arr = [] ;
        $("#hasSup .supKuItem").each(function(){
            var supplierMaterial = $(this).data("mtsupid") ;
            $(this).find(".kuItem").each(function(){
                var location = $(this).find("select").val() ;
                var amount = $(this).find("input").val()
                if(location>0 && amount==""){
                    isOk = false;
                }  if(location==0 && amount!=""){
                    isOk = false;
                }
                if(location>0 && amount!="") {
                    arr.push({
                        "location": location,
                        "amount": amount ,
                        "unit": $(this).find(".goodsUnit").val(),
                        "supplierMaterial":supplierMaterial,
                        "material":mtID,
                        "createName":createName,
                        "creator":creator
                    })
                }
            })
        });
        data['params'] = JSON.stringify(arr) ;
    }

    if(!isOk){
        layer.msg("还有必填项尚未填写！");
        return false;
    }
    $.ajax({
        url:"../mt/stockAndLocation" ,
        data:  data ,
        success:function( data ){
            if(data.code == 200){
                bounce_Fixed.cancel();
                bounce.cancel();
                $("#byCategory .curCat>.go2Cat:eq(0)").click();
            }else{
                layer.msg("系统错误，请重试!") ;
            }
        }
    })
}
// creator: hxz，2020-02-24 20:08:36，重新选择库位公用接口
function updateLocationCommon(itemId) {
    $.ajax({
        url:"../finished/getLocationListByProduct.do" ,
        data:{
            id: itemId
        },
        success:function(data){
            if (data.code == 200) {
                var info = data.data;
                var list = info.list;
                var goodsInfo =
                    '<tr data-id="'+ info.id +'">' +
                    '    <td>'+ info.name +'</td>' +
                    '    <td>'+ info.innerSn +'</td>' +
                    '    <td>'+ info.model +'</td>' +
                    '    <td>'+ info.specifications +'</td>' +
                    '    <td class="createInfo">'+ info.createName + '&nbsp;&nbsp;' + new Date(info.createDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    '    <td>'+ info.unit +'</td>' +
                    '    <td>'+ info.currentStock +'</td>' +
                    '    <td>'+ info.num +'个</td>' +
                    '</tr>';
                $(".reUnit").html(info.unit);
                $("#reselectList tbody").html(goodsInfo);
                if (list.length > 0){
                    var len = Math.ceil(list.length/10);
                    $("#reselectTtl").html('重新选择库位');
                    for(var m=0;m<len;m++){
                        var tabStr =
                            '<table class="ty-table ty-table-control gap ">' +
                            '   <tr>' +
                            '       <td rowspan="2" class="td-orange">现况</td>';
                        var size = 10,locationCode = '',locationNum ='<tr>';
                        var render = list.length%10;
                        if (m==len-1 && render>0) {
                            size = render;
                        }
                        for(var n=0;n<size;n++){
                            var i = m*size + n;
                            locationCode += '<td>'+ list[i].locationCode +'</td>';
                            locationNum += '<td>'+ list[i].amount +'</td>';
                        }
                        tabStr += locationCode + locationNum +
                            '</tr></table>';
                    }
                    $("#reselectLocation .noStation").hide();
                    $("#reselectLocation .hasStation").show();
                    $("#resetCurrentStation").html(tabStr);
                }else{
                    $("#reselectLocation .noStation").show();
                    $("#reselectLocation .hasStation").hide();
                }
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: hxz，2020-01-20 11:22:34，去盘点
function takeInventory() {
    $("#tip #tipMs").html("请在手机端进行盘点！") ;
    bounce_Fixed2.show($("#tip")) ;
}
// creator: hxz，2020-01-20 14:59:46，初始库存列表
function stockInitial(obj) {
    var itemId = obj.parents("tr").data("id");
    $("#initialStockRecord tbody").html("");
    stockRecordCommon(itemId, 1);
    $("#updateStores").data("id", itemId);
}
// creator: hxz，2020-02-24 17:14:59，初始库存查看-公用 ,type:1=初始库存查看,2= 初始库存-修改记录
function stockRecordCommon(itemId,type) {
    var html='';
    $.ajax({
        url:"../finished/getInitialStockRecord.do" ,
        data:{ id: itemId },
        success:function(data){
            if (data.list.length > 0) {
                var list = data.list;
                for (var i=0; i<list.length;i++){
                    html +=
                        '<tr>' +
                        '    <td>'+ list[i].updateName +'</td>' +
                        '    <td>'+ new Date(list[i].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        '    <td>'+ list[i].customerName +'</td>' +
                        '    <td>'+ list[i].before +'</td>' +
                        '    <td>'+ list[i].after +'</td>' +
                        '</tr>';
                }
            }
            if (type == 1){
                var stock = data.initialStock;
                var unit = data.unit;
                $("#stockAmount").html(stock + '&nbsp;&nbsp;' + unit);
                $("#initialStockRecord tbody").html(html);
                bounce.show($("#initialStockSee"));
            }else{
                $("#initUpdateRecord tbody").html(html);
                bounce.show($("#initStockUpdateRecord"));
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        }
    });
}
// creator: hxz，2020-02-25 08:15:05，修改初始库存
function updateStores() {
    $("#newStore").val('');
    bounce_Fixed.show($('#updateStores'));
}
// creator: hxz，2020-01-20 16:52:08，修改初始库存提交
function updateStoresSure() {
    var html = '';
    if ($.trim($("#newStore").val()) == '') {
        html = '您还没输入具体数字呢！';
        $("#knowTipMs").html(html);
        bounce_Fixed2.show($("#knowTip"));
    } else if ($.trim($("#newStore").val()) < 0) {
        html =
            '<p>此数字将导致实际库存小于零。</p>' +
            '<p>请输入正确的数字！</p>';
        $("#knowTipMs").html(html);
        bounce_Fixed2.show($("#knowTip"));
    } else{
        var itemId = $("#updateStores").data("id");
        var type = $("#updateStores").data("type");

        let curNum = $('#initStock').data('initialstock')
        $.ajax({
            url:"../skl/updateInitialStock.do" ,
            data:{
                // id: 材料id   oldStock：当前库存  newStock：修改后的库存
                id: itemId,
                oldStock: curNum,
                newStock: $.trim($("#newStore").val())
            },
            success:function(data){
                bounce_Fixed.cancel();
                bounce.cancel();
                if (data == 0) {
                    layer.msg("修改失败  已申请数量大于修改后库存");
                }else if(data == 1){
                    layer.msg("修改成功");
                    var aa = $("#ye1").find(".json").html()
                    var a = JSON.parse(aa);
                    var cur =  $("#ye1").find(".yecur").html();
                    getPdByCategory(a.category, a.state, a.keyword, cur , 20);
                }else {
                    layer.msg("修改失败");
                }
            },
            error:function(){
                $("#tip #tipMs").html("系统错误，请重试!") ;
                bounce_Fixed2.show($("#tip")) ;
            }
        });

    }
}

//  create: hxz 2021-05-11 保留三位小数
function tofixed3(obj) {
    let v = obj.value
    v = v.replace(/[^\d.]/g, "");
    v = v.replace(/^\./g, "");
    v = v.replace(/\.{2,}/g, ".");
    v = v.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".")
    let vArr = v.split('.')
    if(vArr[0].length > 9){ // 最多9位
        vArr[0] = String(vArr[0]).substr(0,9);
    }
    v = vArr[0]
    if(vArr.length > 1){
        if(vArr[1].length > 4){
            vArr[1] = vArr[1].substr(0,4);
        }
        v += '.' + vArr[1];
    }
    obj.value = v
}



// 点击选择分类
function showkindBtn(obj){
    isall = false ;
    var name = obj.html();
    var id = obj.parent().children("div.hd").children(".kindId").html();
    var type = obj.parent().children("div.hd").children(".kindId").data("type");
    if(id != undefined || id != null){
        var strson_1 = "<span onclick='showkindNav($(this))'> < "+
            "<span>"+name+"</span>"+
            "<span class='hd' data-type='"+ type +"'>"+ id +"</span>"+
            "</span>";
        $("#curID").append(strson_1);
        $("#categoryName").html(name);
        getPdByCategory( id , type, 1 , 20 );
    }else{
        $("#mt_tip_ms").html("系统错误，请刷新请刷新重试！");
        $("#mtTip").show().parent().show();
    }
}
// 点击头部导航
function showkindNav( obj ){
    var id = obj.children(".hd").html() ;
    var type = obj.children(".hd").data("type");
    var name = obj.children("span").eq('0').html();
    if(id != undefined  ){
        $("#categoryName").html(name);
        obj.parent().nextAll().remove();
        getPdByCategory( id, type , 1 , 20 );
    }else{
        $("#mt_tip_ms").html("系统错误，请刷新请刷新重试！");
        $("#mtTip").show().parent().show();
    }
}
// ====================== 仓库管理-入库受理 ========================//
/*

/!* creator：张旭博，2017-08-22 16:03:05，获取成品入库申请列表 *!/
function getStorageAcceptList(currPage,pageSize,flag) {
    $("#ye_accept").html("");
    $.ajax({
        url:"../inOutStock/StockApply.do" ,
        data:{
            "pageSize":pageSize,
            "currPage":currPage,
            "flag":flag
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){

            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            var partOne = $(".container_item>div:visible").data('part');
            var jsonStr = JSON.stringify({"param": partOne}) ;
            setPage( $("#ye_accept") , curr ,  totalPage , "accept", jsonStr );

            var mtStockList = data["mtStockList"];
            var storageAcceptListStr = "";
            for(var j = 0 ;j < mtStockList.length ; j++){
                var approcessList = mtStockList[j].approcessList;
                var processDetailStr = getProcessStr(approcessList);
                if(flag === 0){
                    var inFactStr = '';
                    var handelStr = '<td><span class="ty-color-blue" onclick="storageAcceptHandleBtn($(this))">操作</span></td>';
                }else if(flag ===1){
                    var inFactStr = '<td>'+mtStockList[j].inFact+'</td>';
                    var handelStr = '';
                }
                storageAcceptListStr += '<tr id="'+mtStockList[j].id+'">' +
                                            '<td>'+(j+1)+'</td>'+ // 序号
                                            '<td>'+mtStockList[j].createDate.substring(0,10)+'</td>'+ //申请时间
                                            '<td>'+mtStockList[j].outerName+'</td>'+    //商品代号
                                            '<td>'+mtStockList[j].outerSn+'</td>'+    //商品名称
                                            '<td>'+mtStockList[j].innerSn+'</td>'+    //产品图号
                                            '<td>'+mtStockList[j].innerSnName+'</td>'+    //产品名称
                                            '<td>'+mtStockList[j].unit+'</td>'+    //单位
                                            '<td>'+mtStockList[j].inPlan+'</td>'+    //申请入库数量
                                            inFactStr+                              //实际入库数量
                                            '<td>'+mtStockList[j].manufactureDate.substring(0,10)+'</td>'+    //生产日期
                                            '<td>'+mtStockList[j]["invalidDate"].substring(0,10)+'</td>'+    //产品到期日
                                            processDetailStr+
                                            handelStr+  //操作
                                        '</tr>';
            }
            if(flag === 0){
                $("#inStorage tbody").html(storageAcceptListStr);
            }else if(flag ===1){
                $("#inStorageInfo tbody").html(storageAcceptListStr);
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        },
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/!* creator：张旭博，2017-11-06 14:21:43，获取成品出库申请列表 *!/
function getOutStorageList(currPage,pageSize) {
    $.ajax({
        url:"../inOutStock/OutputList.do" ,
        data:{
            "pageSize":pageSize,
            "currPage":currPage
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){

            //设置分页
            var totalPage = data["totalPage"];//总页数
            var curr = data["currPage"];//当前页
            $("#ye_accept").html("");
            var partOne = $(".container_item>div:visible").data('part');
            var jsonStr = JSON.stringify({"param": partOne}) ;
            setPage( $("#ye_accept") , curr ,  totalPage , "accept", jsonStr );

            var outStockList = data["list"];
            var outStorageListStr = "";
            for(var j = 0 ;j < outStockList.length ; j++){
                outStorageListStr +=    '<tr id="'+outStockList[j].id+'">' +
                                            '<td>'+formatTime(outStockList[j].delivery_date,false)+'</td>'+   //计划出库日期
                                            '<td>'+outStockList[j].customer_name+'</td>'+   //客户名称
                                            '<td>'+handleNull(outStockList[j].address)+'</td>'+         //收货地址
                                            '<td>'+outStockList[j].delivery_way+'</td>'+    //计划的发货方式
                                            '<td>'+handleNull(outStockList[j].sn)+'</td>'+          //订单号
                                            '<td>'+outStockList[j].gsum+'</td>'+            //商品类别总数
                                            '<td>'+outStockList[j].pack+'</td>'+            //货物总件数
                                            '<td>'+outStockList[j].carrier+'</td>'+         //搬运负责人
                                            '<td>'+formatTime(outStockList[j].arrive_date,false)+'</td>'+     //计划到达日期
                                            '<td>'+formatTime(outStockList[j].apply_date,true)+'</td>'+      //申请提交时间
                                            '<td>'+formatTime(outStockList[j].update_date,true)+'</td>'+     //申请最后修改时间
                                            '<td>'+outStockList[j].applicant_name+'</td>'+  //提交者
                                            '<td>'+                                         //操作
                                                '<span class="ty-color-blue" onclick="approveOutStorageBtn($(this))">审批</span>'+
                                                '<span class="ty-color-blue" onclick="checkOutStorageBtn($(this))">查看</span>'+
                                            '</td>'+
                                        '</tr>';
            }

            $("#outStorage").find("tbody").html(outStorageListStr);

        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/!* creator：张旭博，2017-11-18 09:46:38，出库评审弹窗赋值 *!/
function approveOutStorageBtn(selector){
    bounce.show($("#outStorageApply"));
    var outid = selector.parent().parent().attr("id");


    $.ajax({
        url:"../inOutStock/lookOutput.do" ,
        data:{
            "outid":outid
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;
            var base = data["data"];
            var deliveryList    = base.list;
            var deliveryListStr = '';
            $("#outStorageApply .applyAll").html('申请人： <span class="ty-color-blue">'+base.applicant_name+'</span> 申请时间：<span class="ty-color-blue">'+formatTime(base.apply_date,false) +'</span>');
            $("#outStorageApplyBase").attr("oid",outid);
            $("#outStorageApplyBase .customerName").html(base.customer_name);
            $("#outStorageApplyBase .customerCode").html(base.customer_code);
            $("#outStorageApplyBase .deliveryDate").html(formatTime(base.delivery_date,false));
            $("#outStorageApplyBase .sn").html(base.sn);

            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {
                    var state = deliveryList[i].approver_operate;
                    var handleStr = '';
                    if(state === null || state === ""){
                        handleStr = '<span class="ty-color-blue" onclick="approveOutStorage($(this),3)">同意出库</span>' +
                                    '<span class="ty-color-red" onclick="approveOutStorage($(this),2)">暂缓出库</span>' ;
                    }else if(state === "1"){
                        handleStr = '<span class="ty-color-gray"">已同意出库</span>'
                    }else if(state === "2"){
                        handleStr = '<span class="ty-color-gray"">已暂缓出库</span>'
                    }
                    deliveryListStr += '<tr id="'+deliveryList[i].id+'">' +
                        '<td>' + deliveryList[i].outer_sn + '</td>' +       //商品代号
                        '<td>' + deliveryList[i].outer_name + '</td>' +     //商品名称
                        '<td>' + handleNull(deliveryList[i].inner_sn) + '</td>' +       //产品图号
                        '<td>' + deliveryList[i].name + '</td>' +           //产品名称
                        '<td>' + deliveryList[i].unit + '</td>' +           //单位
                        '<td>' + deliveryList[i].out_plan + '</td>' +       //计划出库数量
                        '<td class="goodNum_stock">' + deliveryList[i].pack + '</td>' +           //货物件数
                        '<td>' + handleStr + '</td>' +
                        '</tr>'
                }
                $("#goodList tbody").html(deliveryListStr);
            }
            //统计商品种类和件数
            figureGoodInfo($("#outStorageApply"))
        },
        error:function(){
            $("#tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/!* creator：张旭博，2017-11-18 09:46:38，出库评审(确认出库和暂缓出库) *!/
function approveOutStorage(selector,state){
    bounce.show($("#outStorageApply"));
    var outid   = $("#outStorageApplyBase").attr("oid");
    var id      = selector.parent().parent().attr("id");

    // Integer outid //出库单id 必
    // Integer id  //商品序号id 必
    // Integer state //3.同意 2暂缓 必
    $.ajax({
        url:"../inOutStock/outboundApproval.do" ,
        data:{
            "outid":outid,
            "id":id,
            "state":state
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                if(state === 3){
                    selector.parent().html("已同意出库")
                }else{
                    selector.parent().html("已暂缓出库")
                }
                getOutStorageList(1,20);
                getIndexData();
            }
        },
        error:function(){
            $("#tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/!* creator：张旭博，2017-12-07 16:00:43，成品出库申请-查看（出库申请单） *!/
function checkOutStorageBtn(selector) {

    bounce.show($("#outStorageOrder"));
    var outid = selector.parent().parent().attr("id");
    $.ajax({
        url:"../inOutStock/lookOutput.do" ,
        data:{
            "outid":outid
        },
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;
            var base = data["data"];
            var deliveryList    = base.list;
            var deliveryListStr = '';
            $("#outStorageOrder .applyAll").html('申请人： <span class="ty-color-blue">'+base.applicant_name+'</span> 申请时间：<span class="ty-color-blue">'+formatTime(base.apply_date,false) +'</span>');
            $("#outStorageOrderBase").attr("oid",outid);
            $("#outStorageOrderBase .customerName").html(base.customer_name);
            $("#outStorageOrderBase .customerCode").html(base.customer_code);
            $("#outStorageOrderBase .deliveryDate").html(formatTime(base.delivery_date,false));
            $("#outStorageOrderBase .sn").html(base.sn);

            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {
                    deliveryListStr += '<tr id="'+deliveryList[i].id+'">' +
                        '<td>' + deliveryList[i].outer_sn + '</td>' +       //商品代号
                        '<td>' + deliveryList[i].outer_name + '</td>' +     //商品名称
                        '<td>' + deliveryList[i].inner_sn + '</td>' +       //产品图号
                        '<td>' + deliveryList[i].name + '</td>' +           //产品名称
                        '<td>' + deliveryList[i].unit + '</td>' +           //单位
                        '<td>' + deliveryList[i].current_stock + '</td>' +  //当前库存
                        '<td>' + deliveryList[i].out_plan + '</td>' +       //计划出库数量
                        '<td class="goodNum_stock">' + deliveryList[i].pack + '</td>' +           //货物件数
                        '</tr>'
                }
                $("#outStorageOrder .tblList tbody").html(deliveryListStr);
            }

            //统计商品种类和件数
            figureGoodInfo($("#outStorageOrder"))

        },
        error:function(){
            // $("#tipMs").html("系统错误，请重试!") ;
            // bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

function storageAcceptHandleBtn(selector) {
    // if(hasAuthority(0)){
    //     layer.msg("您没有此权限！")
    //     return false;
    // }
    bounce.show($("#storageAcceptHandle"));
    selector.parent().parent().siblings().removeClass("storageAcceptItemActive");
    selector.parent().parent().addClass("storageAcceptItemActive");
    $(".ty-radioActive").removeClass("ty-radioActive");
    $(".ty-radio i").removeClass("fa-dot-circle-o").addClass("fa-circle-o");
    $(".productNum").val("");
    $(".judgmentQuantity").val("");
    $("#productNum").hide();
    $('body').everyTime('0.5s','storageAcceptHandle',function(){
        var val = $(".ty-radioActive").attr("value");
        if(val === "0"||(val === "1"&&$(".productNum").val()!=="")){
            $("#storageAcceptHandleBtn").prop("disabled",false);
        }else{
            $("#storageAcceptHandleBtn").prop("disabled",true);
        }
    });
}

function sureStorageAcceptHandle() {
    var judgmentQuantity    = $("#storageAcceptHandle .judgmentQuantity").val();
    var productNum          = $("#storageAcceptHandle .productNum").val();
    var ID                  = $(".storageAcceptItemActive").attr("id");

    var data = {
        "flag" : judgmentQuantity,  //Integer flag数量无误传0,需修改数量传1；
        "ID"   : ID                 //Integer ID出入库表ID
    };

    if( Number(judgmentQuantity) === 1 ){
        data["InFact"] = productNum;//Integer InFact修改数量
    }

    $.ajax({
        url:"../mtStock/updateMtStockCount.do" ,
        data:data ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            loading.close() ;
            bounce.cancel();
            var status = data["status"];
            if(status != 1){
                $("#tip #tipMs").html("入库失败!") ;
                bounce_Fixed2.show($("#tip")) ;
            } else{
                var partOne = $(".container_item>div:visible").data('part');
                jumpTo(partOne, 1,20);
                getIndexData();
            }
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ chargeClose(1) ;  }
    }) ;

}

function goback() {
    $("#checkInOutInfo").hide().siblings(".ty-container").show();
    $("#queryBtn").show();
    var text = $("#inOutApply .ty-secondTab .ty-active").html();
    $("#curN").html(text)
}

/!* creator：张旭博，2017-08-29 10:25:20，获取审批流程字符串（<td>...</td>） *!/
function getProcessStr(approcessList) {

    //每一条审批流程组成的字符串
    var processStr = "";
    for (var k = 0; k < approcessList.length; k++) {
        var approveStatus = approcessList[k].approveStatus;
        if(Number(approveStatus) === 4){
            processStr +=   '<div class="infoList ty-process-item ">' +
                '<p><i class="dot-no"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                '<p>' + approcessList[k].createDate + '</p>' +
                '</div>';
        }else{
            processStr +=   '<div class="infoList ty-process-item ">' +
                '<p><i class="dot"></i>'+approcessList[k].toUserName+'：' + approcessList[k].userName + '</p>' +
                '<p>' + approcessList[k].createDate + '</p>' +
                '</div>';
        }
    }

    //审批流程字符串
    var processDetailStr  = '<td class="hoverDetail"><span>查看入库流程</span> '+
                                '<div style="position: relative;">' +
                                    '<div class="hoverDetailCon">' +
                                        '<div class="ty-panel ty-process" >'+
                                            '<div class="infoHead ty-process-ttl"><b class="ty-btn ty-btn-green ty-circle-3">入库流程</b></div>'+
                                            '<div class="conInfo ty-process-container" id="process">'+
                                            processStr+
                                            '</div>'+
                                        '</div>'+
                                    '</div>' +
                                '</div>' +
                            '</td>';    //入库流程
    return processDetailStr;
}

/!* creator：张旭博，2017-11-08 08:58:03，物流管理-》发货管理-》获取四个状态列表 *!/
function getDeliveryList(pageNum,per,state) {
    $("#ye_accept").html("");
    var data = {
        "pageNum":pageNum,
        "per":per
    };
    if(state !== 0){
        data["state"] = state
    }
    $.ajax({
        url:"../skl/deliveryList.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;

            var deliveryList    = data["data"];
            var deliveryListStr = '';
            if(deliveryList !== undefined && deliveryList !== null) {
                for (var i = 0; i < deliveryList.length; i++) {
                    deliveryListStr +=  '<tr id="'+deliveryList[i].tpa_id+'">' +
                        '<td>' + formatTime( deliveryList[i].last_review_time  , false)+ '</td>' +  //出库日期
                        '<td>' + deliveryList[i].customer_name + '</td>' +                          //客户名称
                        '<td>' + deliveryList[i].code + '</td>' +                                   //客户代号
                        '<td>' + handleNull(deliveryList[i].sn) + '</td>' +                                     //订单号
                        '<td>' + deliveryList[i].item_account + '</td>' +                           //商品类别总数
                        '<td>' + deliveryList[i].pack_amount + '</td>' +                            //货物总件数
                        '<td>' + formatTime( deliveryList[i].tpa_create_date , true) + '</td>' +    //申请提交时间
                        '<td>' + formatTime( deliveryList[i].last_approve_time , true) + '</td>' +  //仓库审批时间(最后)
                        '<td>' + formatTime( deliveryList[i].last_review_time , true) + '</td>' +   //复核时间(最后)
                        '<td>' + deliveryList[i].tpa_create_name + '</td>' +                        //提交者
                        '<td class="base" style="display: none">'+JSON.stringify(deliveryList[i])+'</td>'+
                        '<td>' +
                        '<span class="ty-color-blue" onclick="signCheckBtn($(this))">查看</span>' +
                        '<span class="ty-color-blue" onclick="transInfoBtn($(this))">发运信息</span>' +
                        '</td>' +
                        '</tr>';
                }
                $("#outStorageInfo tbody").html(deliveryListStr);
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/!* creator：张旭博，2017-12-04 09:40:03，物流管理-》发货管理-》已签收-》签收查看-按钮  *!/
function signCheckBtn(selector) {
    bounce.show($("#signCheck"))
    var outid = selector.parent().parent().attr("id");
    $.ajax({
        url:"../inOutStock/aTaoLook.do" ,
        data:{
            "outid":outid,
            "state":10
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var base = data["data"];
            var deliveryItemData = base.list;
            $("#signCheckBase .customerName").html(base.customer_name);
            $("#signCheckBase .customerCode").html(base.customer_code);
            $("#signCheckBase .address").html(base.address);
            $("#signCheckBase .contact").html(base.telephone);
            $("#signCheckBase .mobile").html(base.consignee);
            $("#signCheckBase .sn").html(base.sn);
            $("#signCheckBase .create_date").html(formatTime( base.order_c , false ));
            $("#signCheckBase .update_date").html(formatTime( base.order_u , false ));
            $("#signCheckBase .deliveryDate").html(formatTime( base.delivery_date , false ));
            $("#signCheckBase .carrier").html(base.carrier);
            $("#signCheckBase .arriveDate").html(formatTime( base.arrive_date , false ));
            $("#signCheckBase .deliveryWay").html(base.delivery_way);

            $("#signInfoRecord .signDetail").html(base.sign_record);
            $("#signInfoRecord .signer").html(base.sginer);
            $("#signInfoRecord .signTime").html(formatTime(base.sign_time,false));
            $("#signInfoRecord .recorder").html(base.sign_recorder_name);
            $("#signInfoRecord .recordTime").html(formatTime(base.sign_recorder_time,false));
            $("#signCheckBase").attr("oid",base.outid);
            $("#signCheck .applyAll").html(
                ' &nbsp;申请人： <span class="ty-color-blue">'+base.applicant_name+'</span> '+formatTime(base.apply_date,true) +
                ' &nbsp;仓库： <span class="ty-color-blue">'+base.approver_name+'</span> '+formatTime( base.approve_time , true ) +
                ' &nbsp;复核人： <span class="ty-color-blue">'+base.reviewer_name+'</span> '+formatTime(base.review_time,true)

            );

            var deliveryStr = '';
            for(var i=0;i<deliveryItemData.length;i++){
                deliveryStr +=  '<tr id="'+deliveryItemData[i].id+'" pack="'+deliveryItemData[i].pack+'">'+
                    '<td>' + deliveryItemData[i].outer_sn + '</td>' +                           //商品代号
                    '<td>' + deliveryItemData[i].outer_name + '</td>' +                         //商品名称
                    '<td>' + deliveryItemData[i].inner_sn + '</td>' +                           //产品图号
                    '<td>' + deliveryItemData[i].name + '</td>' +                               //产品名称
                    '<td>' + deliveryItemData[i].unit + '</td>' +                               //单位
                    '<td>' + formatTime( deliveryItemData[i].delivery_date , false ) + '</td>' +//要求到货日期
                    '<td>' + deliveryItemData[i].out_plan + '</td>' +                           //出库数量
                    '<td class="goodNum_stock">' + deliveryItemData[i].pack + '</td>' +                           //货物件数
                    '<td>' + formatTime( deliveryItemData[i].approve_time,false) + '</td>' +    //仓库审批时间
                    '<td>' + formatTime( deliveryItemData[i].review_time,false) + '</td>' +    //复核时间
                    '<td class="change_amount">' + chargeNull(deliveryItemData[i].sign_amount) + '</td>' +    //实际签收数量
                    '<td class="change_memo" style="display:none;">' + chargeNull(deliveryItemData[i].sign_record) + '</td>' ;    //情况记录
                var sign_time = deliveryItemData[i].review_time;
                if(sign_time === null || sign_time === undefined || sign_time === ''){
                    deliveryStr +=  '<td></td><td></td></tr>'
                }else{
                    deliveryStr +=  '<td>' + formatTime( deliveryItemData[i].sgin_time,false) + '</td>' +    //录入时间
                        '<td>' +
                        '<span class="ty-color-blue" onclick="signDetailCheckBtn($(this))">查看</span>'+
                        '</td>'+
                        '</tr>';
                }
            }
            $("#signCheck .tblList tbody").html(deliveryStr);

            //统计商品种类和件数
            figureGoodInfo($("#signCheck"));
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce_Fixed2.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/!* creator：张旭博，2017-11-29 16:56:31，货运信息-按钮 *!/
function transInfoBtn(selector) {
    bounce.show($("#logisticInfo"));
    var outId = selector.parent().parent().attr("id");
    $("#logisticInfo .transInfo").attr("id",outId);
    getTransInfo();
}

/!* creator：张旭博，2017-11-29 16:56:17，获取发运信息 *!/
function getTransInfo() {
    var outId   = $("#logisticInfo .transInfo").attr("id");
    $.ajax({
        url:"../skl/getShipmentState.do" ,
        data:{"outId":outId},
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            if(data[0].code && data[0].code === 400){
                $("#logisticInfo .transDetail").hide();
                $("#logisticInfo .transInfo").show();
            }else {
                $("#logisticInfo .transDetail").show();
                $("#logisticInfo .transInfo").hide();
                var transDetailStr = '';
                for (var i = 0; i < data.length; i++) {
                    var deliveryWay = data[i].deliveryWay;
                    var state = data[i].updateDate;
                    var dateStr = '';
                    if(state === ""){
                        dateStr = '录入日期';
                    }else{
                        dateStr = '修改日期';
                    }
                    switch (deliveryWay) {
                        case "A":
                            transDetailStr += '<tr>' +
                                '    <td>发运方式：</td>' +
                                '    <td>快递</td>' +
                                '    <td>快递单号：</td>' +
                                '    <td>' + data[i].sn + '</td>' +
                                '    <td>快递公司：</td>' +
                                '    <td>' + data[i].company + '</td>' +
                                '    <td>交寄日期：</td>' +
                                '    <td>' + data[i].deliveryTime + '</td>' +
                                '    <td>'+dateStr+'</td>' +
                                '    <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                        case "B":
                            transDetailStr += '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="B">随身携带</td>' +
                                '   <td>携带者：</td>' +
                                '   <td>' + data[i].operator + '</td>' +
                                '   <td>联系方式：</td>' +
                                '   <td>' + data[i].telephone + '</td>' +
                                '   <td>备注：</td>' +
                                '   <td>' + data[i].memo + '</td>' +
                                '   <td>'+dateStr+'</td>' +
                                '   <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                        case "C":
                            transDetailStr += '<tr>' +
                                '   <td>发运方式：</td>' +
                                '   <td class="deliveryWay" value="C">货运</td>' +
                                '   <td>联系人：</td>' +
                                '   <td>' + data[i].operator + '</td>' +
                                '   <td>联系电话：</td>' +
                                '   <td>' + data[i].telephone + '</td>' +
                                '   <td>牌照号码：</td>' +
                                '   <td>' + data[i].licenseTag + '</td>' +
                                '</tr>' +
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>乘运公司：</td>' +
                                '   <td>' + data[i].company + '</td>' +
                                '   <td>货运单号：</td>' +
                                '   <td>' + data[i].sn + '</td>' +
                                '</tr>'+
                                '<tr>' +
                                '   <td></td>' +
                                '   <td></td>' +
                                '   <td>备注：</td>' +
                                '   <td colspan="5">'+data[i].memo +'</td>' +
                                '   <td>'+dateStr+'</td>' +
                                '   <td>' + data[i].createDate + '</td>' +
                                '</tr>';
                            break;
                    }
                }
                $("#logisticInfo .transDetail tbody").html(transDetailStr);
                $("#logisticInfo .transDetail").show();
                $("#logisticInfo .chooseWay").hide();
                $("#logisticInfo .transInfo").hide();
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

// creator: 张旭博，2018-05-14 16:19:16，统计商品的类别数和件数
function figureGoodInfo(selector) {
    var goodIdArr = [],
        countAll = 0,
        pack_amount = 0;

    //遍历选择器下的所有商品id
    selector.find(".tblList tbody tr").each(function () {
        var innerSn = $(this).find("td").eq(2).text();
        goodIdArr.push(innerSn);
        console.log(innerSn)
        pack_amount += Number($(this).find(".goodNum_stock").text());
    });

    //id排序
    goodIdArr.sort();

    //统计类别数
    for (var i = 0; i < goodIdArr.length;) {
        var count = 0;
        for (var j = i; j < goodIdArr.length; j++) {
            if (goodIdArr[i] === goodIdArr[j]) {
                count++;
            }
        }
        countAll++;
        i+=count;
    }
    var alertHtml = '本次计划出库共 <span class="ty-color-blue"> '+countAll+' </span> 种商品，共<span class="ty-color-blue"> '+pack_amount+' </span>件';
    selector.find(".countAll").alert('success',alertHtml);
}
/!* creator：张旭博，2017-11-30 08:30:58，物流管理-》发货管理-》已签收-》已签收列表-》签收记录-》查看按钮 *!/
function signDetailCheckBtn(selector) {
    bounce_Fixed.show($("#signDetailCheck"));
    var sign_record = selector.parent().siblings(".change_memo").text();
    var sign_amount = selector.parent().siblings(".change_amount").text();
    $("#signDetailCheck .actualDeliveryNum").val(sign_amount);
    $("#signDetailCheck .memo").val(sign_record);
}

*/


// 为空赋值
function chargeNull(str){
    if(str === null || str==0 || str === undefined){
        return "--"
    }else{
        return str ;
    }
}

laydate.render({
    elem: '#s2',
    type:'year',
    max:(new Date().format("yyyy"))
});







