$(function () {
    getList() ;
}) ;

// creator: 侯杏哲 2017-10-16 获取列表
var searchInfo = { "year":"" , "month":"" , "level":1 } ;
function getList( year , month ) { // year:年度，不传查全部年度
    var level = 0 ;
    if(year){
        if(month){
            $("#nav2").html( "与"+ month +"月份工资一同发放的修改/补录工资" );
            level = 3 ;
            searchInfo = { "year":year , "month":month , "level": 3 } ; $("#nav21").html( year + "年" + month + "月工资列表查询" );
            $("#monthBtn").show().attr("onclick","getList("+ year +")") ; $("#yearBtn").hide() ;
        } else{
            $("#nav2").html(year +"年工资列表查询");
            level = 2 ;
            searchInfo = { "year":year , "month":"" , "level": 2 } ;
            $("#yearBtn").show().attr("onclick","getList()") ; $("#monthBtn").hide() ;
        }
    }else{
        $("#nav2").html("年度工资列表查询"); level = 1 ; searchInfo = { "year":"" , "month":"" , "level":1 } ;
        $("#yearBtn").hide() ; $("#monthBtn").hide() ;
    }
    var dataPost = {} ;
    dataPost["type"] = level ;
    if( level == 2 ){ dataPost["year"] = searchInfo["year"] ;  }
    else if( level == 3 ){  dataPost["year"] = searchInfo["year"] ;  dataPost["month"] = searchInfo["month"] ;   }
    $.ajax({
        "url": "../role/salary_salaryQuery.action" ,
        "data":dataPost ,
        success:function (data) {
            var list = null , str = "" ;
            if(level === 1){ // 查询年份的
                list = data["yearList"] ;
                if( list && list.length > 0 ){
                    for(var i = 0 ; i < list.length ; i++){
                        str += "<tr><td>"+ list[i]["year"] +"</td>" +
                            "<td>"+ list[i]["salary"] +"</td>" +
                            "<td>"+ list[i]["numAvg"] +"</td>" +
                            "<td>"+ list[i]["salaryAvg"] +"</td>" +
                            "<td><span class='ty-color-blue' onclick='getList("+ list[i]["year"] +")'>查看</span></td></tr>" ;
                    }
                }
                $("#year").html( str ) ; $("#yearCon").show().siblings().hide();
            }
            else if(level === 2){
                list = data["monthList"] ;
                if( list && list.length > 0 ){
                    for(var i = 0 ; i < list.length ; i++){
                        str += "<tr><td>"+ list[i]["month"] +"</td>" +
                            "<td>"+ list[i]["salary"] +"</td>" +
                            "<td>"+ list[i]["personNum"] +"</td>" +
                            "<td>"+ list[i]["salaryAvg"] +"</td>" +
                            "<td><span class='ty-color-blue' onclick='getList("+ dataPost["year"] + ","+ list[i]["month"] + ")'>查看</span></td></tr>" ;
                    }
                }
                $("#month1").html( str ) ; $("#monCon1").show().siblings().hide();
            }
            else if(level === 3){
                $("#monCon2").show().siblings().hide();
                var listAllSupply = data["listAllSupply"] ;// 补录
                var listHistory = data["listHistory"] ; // 修改
                list = data["employeeList"] ; // 正常发放的
                if( list && list.length > 0 ){
                    for(var i = 0 ; i < list.length ; i++){
                        str += "<tr>" +
                                "<td>"+ list[i]["empid"] +"</td>" +
                                "<td>"+ list[i]["realname"] +"</td>" +
                                "<td>"+ list[i]["username"] +"</td>" +
                                "<td>"+ list[i]["depart"] +"</td>" +
                                "<td>"+ list[i]["salary"] +"</td>" +
                                "<td>"+ (list[i]["addtime"]&&list[i]["addtime"].substr(0,19)) +"</td>" +
                                "<td>"+ list[i]["editor"] +"</td>" +
                            "</tr>" ;
                    }
                }
                $("#month2").html( str ) ;
                var str2 = "" ;
                if( listAllSupply && listAllSupply.length > 0 ){
                    for(var j = 0 ; j < listAllSupply.length ; j++){
                        str2 += "<tr>" +
                                "<td>补录工资</td>" +
                                "<td>"+ listAllSupply[j]["empid"] +"</td>" +
                                "<td>"+ listAllSupply[j]["realname"] +"</td>" +
                                "<td>"+ listAllSupply[j]["year"] +"</td>" +
                                "<td>"+ listAllSupply[j]["month"] +"</td>" +
                                "<td>0</td>" +
                                "<td>"+ listAllSupply[j]["salary"] +"</td>" +
                                "<td>— —</td>" +
                                "<td>— —</td>" +
                                "<td>"+ ( listAllSupply[j]["addtime"]&&listAllSupply[j]["addtime"].substr(0,19) ) +"</td>" +
                                "<td>"+ listAllSupply[j]["editor"] +"</td>" +
                                "<td> </td>" +
                            "</tr>" ;
                    }
                }
                if( listHistory && listHistory.length > 0 ){
                    $("#monCon2").show().siblings().hide();
                    for(var k = 0 ; k < listHistory.length ; k++){
                        str2 += "<tr>" +
                                "<td>修改工资</td>" +
                                "<td>"+ listHistory[k]["empid"]    +"</td>" +
                                "<td>"+ listHistory[k]["realname"] +"</td>" +
                                "<td>"+ listHistory[k]["year"] +"</td>" +
                                "<td>"+ listHistory[k]["month"] +"</td>" +
                                "<td>"+ listHistory[k]["salaryOld"] +"</td>" +
                                "<td>"+ listHistory[k]["salaryNew"] +"</td>" +
                                "<td>"+ (listHistory[k]["addtimeOld"] && listHistory[k]["addtimeOld"].substr(0,19)) +"</td>" +
                                "<td>"+ listHistory[k]["editorOld"] +"</td>" +
                                "<td>"+ (listHistory[k]["addtimeNew"] && listHistory[k]["addtimeNew"].substr(0,19)) +"</td>" +
                                "<td>"+ listHistory[k]["editorNew"] +"</td>" +
                                "<td><span class='ty-color-blue' info='"+ JSON.stringify(listHistory[k]) +"' onclick='showMemo($(this))'>修改后果查看</span></td>" +
                            "</tr>" ;
                    }
                }
                $("#month3").html( str2 );

            }
        }
    });
}

// creactor: 侯杏哲 2017-10-23  修改后果查看
function showMemo( obj ) {
    var info  = JSON.parse( obj.attr("info")) ;
    var prompt = info["prompt"] ;
    bounce.show($("#upInfo"));
    $("#upInfo_ms").html(prompt);
}




