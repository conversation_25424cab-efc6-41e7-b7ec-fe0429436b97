$(function () {
    getInfo();
});
var timer = null;
var currentDate = "";
// creater: hxz 2018-10-13  获取设置详情
function getInfo() {
    $.ajax({
        "url": "../role/salary_settingData.action",
        success: function (res) {
            var rate = res["rate"];
            var dtime = res["dtime"];
            var turntype = res["turntype"];
            var validtime = res["validtime"];
            var logs = res["logs"];
            var flag = res["flag"];
            currentDate = res["currentDate"];

            if (dtime) {
                $("#dtime1").html("每月" + dtime + "日");
                $("#type").val("1");
                $("#date").val(dtime);
                $("#dateSetingBtn").html("修改");
                $("#dateSettingTtl").html("修改工资发放日期");
                $("#dateTip").html("当前工资发放日期为<span class='red'>每月" + dtime + "日</span>");
                $("#dateSetting").attr("class", "bonceContainer bounce-red");
                $("#dateOk").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-red");
                if (rate) {
                    $("#rate1").html(rate + "%");
                    $("#type2").val("1");
                    $(".update").show();
                    $("#lvSetingBtn").html("修改");
                    $("#t1").html("请输入新的年化收益率");
                    $("#t2").html("请在日历中确定新年化收益率的生效日期");
                    $("#lvSettingTtl").html("修改年化收益率");
                    $("#lvTip").html("当前年化收益率为<span class='red'>" + rate + "%</span>");
                    $("#lvSetting").attr("class", "bonceContainer bounce-red");
                    $("#okLv").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-red");
                    $("#qtime").hide();
                    $("#ctime").show();
                } else {
                    $("#qtime").show();
                    $("#ctime").hide();
                    $("#t1").html("请设置年化收益率");
                    $("#t2").html("请确定薪资宝对几月份工资开始生效");
                }
                $("#lvSetingBtn").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-green").attr("onclick", "lvSetingBtn()");
            } else {
                $("#lvSetingBtn").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-gray").removeAttr("onclick", "");
            }
            var str = "";
            $("#logs tbody").children("tr:gt(0)").remove();
            if (logs && logs.length > 0) {
                for (var i = 0; i < logs.length; i++) {
                    var time = logs[i]["addtime"].substr(0, 19);
                    time = time.replace("-", "年");
                    time = time.replace("-", "月");
                    time = time.replace(" ", "日 ");
                    str += "<tr></tr><td>" + time + "</td>" +
                        "<td>" + logs[i]["content"] + "</td>" +
                        "<td>" + logs[i]["operatorName"] + "</td></tr>"
                }
            }
            $("#logs tbody").append(str);

        }
    })
}
// creater: hxz 2018-10-13  设置年化收益率
function lvSetingBtn() {
    bounce.show($("#lvSetting"));
    $("#lvSetting").find("input").val("");
    var oldRate = $("#rate1").html();
    oldRate = oldRate.replace("%", "");
    $("#oldRate").val(oldRate);
    timer = setInterval(function () {
        charge(2)
    }, 200);
}
function okLv() {
    var superid = $("#superid").val();
    var ctime = $("#ctime").val();
    var qtime = $("#qtime").val();
    var rate = $("#rate").val();
    var oldRate = $("#oldRate ").val();
    var type = $("#type2").val();
    type = type ? 2 : 1;
    if ($.trim(rate) == "") {
        layer.msg("请录入年化收益率！");
        return false;
    }
    if (Number(rate) < 0 || Number(rate) > 100) {
        layer.msg("请录入合法的年化收益率！");
        return false;
    }
    let validtime = "";
    if (type == 1) { // 首次设置收益率
        if ($.trim(qtime) == "") {
            layer.msg("请选择工资开始生效时间！");
            return false;
        }
        var curMonth = currentDate.substr(0, 7);
        validtime = qtime + "-" + ($("#date").val());
        // var timeCur = new Date(curMonth);
        // var timeValid = new Date(qtime);
        // if (timeValid < timeCur) {
        //     layer.msg("工资开始生效时间不能早于当前月！");
        //     return false;
        // }
    } else {
        if ($.trim(ctime) == "") {
            layer.msg("请选择工资开始生效时间！");
            return false;
        }
        var timeCur = new Date(currentDate);
        var timeValid = new Date(ctime);
        if (timeValid < timeCur) {
            layer.msg("工资开始生效时间不能早于当前日期！");
            return false;
        }
        validtime = ctime;
    }

    $.ajax({
        "url": "../role/salary_rate.action",
        "data": {"validtime": validtime, "superid": superid, "rate": rate, "type": type, "oldRate": oldRate, "": qtime},
        success: function (res) {
            clearInterval(timer);
            var rateState = res["rateState"];
            if (rateState == 1) {
                bounce.cancel();
                getInfo();
                layer.msg("操作成功！");
            } else {
                layer.msg("操作失败！");
            }
        }
    });
}
// creater: hxz 2018-10-13  设置工资发放日期
function dateSetingBtn() {
    bounce.show($("#dateSetting"));
    $("#dateSetting").find("input").val("");
    timer = setInterval(function () {
        charge(1)
    }, 200);
}
function dateOk() {
    var ntime = $("#ntime").val();
    var type = $("#type").val(); //1-设置  2-修改    操作日志需要体现出来是设置还是修改，所以需要此参数判别
    type = type ? 2 : 1;
    ntime = ntime.substr(8, 2);
    if (ntime < 6 || ntime > 25) {
        layer.msg("请选择6日至25日间的日期");
        return false;
    }
    $.ajax({
        "url": "../role/salary_notifyTime.action",
        "data": {"dtime": ntime, "type": type,},
        success: function (res) {
            clearInterval(timer);
            var notifyState = res["notifyState"];
            bounce.cancel();
            if (notifyState == 1) {
                getInfo();
                layer.msg("操作成功！");
            } else {
                layer.msg("操作失败！");
            }

        }
    })
}
// creater: hxz 2018-10-13  判断日期设置的确定是否可点击
function charge(type) { // type: 1 - 日期 ， 2 - 收益率
    if (type == 1) {
        var ntime = $("#ntime").val();
        if ($.trim(ntime) == "") {
            $("#dateOk").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-gray").removeAttr("onclick");
        } else {
            $("#dateOk").attr("onclick", "dateOk()");
            if ($("#type").val() == "1") { // 设置过
                $("#dateOk").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-red");
            } else { // 第一次设置
                $("#dateOk").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-green");
            }
        }
    } else {
        var ctime = $("#ctime").val();
        var qtime = $("#qtime").val();
        var rate = $("#rate").val();
        var isOk = true;
        if ($("#type2").val() == "1") { // 设置过
            if ($.trim(ctime) == "" || $.trim(rate) == "") {
                isOk = false;
            } else {
                var dot3 = rate.split(".")[1];
                if (dot3 && dot3.length > 3) {
                    isOk = false;
                }
            }
            if (isOk) {
                $("#okLv").attr("onclick", "okLv()").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-red");
            } else {
                $("#okLv").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-gray").removeAttr("onclick");
            }
        } else { // 第一次设置
            if ($.trim(qtime) == "" || $.trim(rate) == "") {
                isOk = false;
            } else {
                var dot3 = rate.split(".")[1];
                if (dot3 && dot3.length > 3) {
                    isOk = false;
                }
            }
            if (isOk) {
                $("#okLv").attr("onclick", "okLv()").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-green");
            } else {
                $("#okLv").attr("class", "ty-btn ty-circle-5 ty-btn-big ty-btn-gray").removeAttr("onclick");
            }

        }


    }

}

// laydate({elem: '#qtime', format: 'YYYY-MM', type: "month",});
// laydate.render({  elem: '#qtime' ,type: 'month' });
laydate.render({elem: '#ntime'});
laydate.render({elem: '#ctime'});
laydate.render({elem: '#qtime', type: 'month'});

