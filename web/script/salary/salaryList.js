$(function () {
    // 根据选择的部门展示姓名
    $("#qdepart").change(function () {
        $("#qname").children().show();
        var depart = $(this).val();
        if (depart != 'depart') {
            $("#qname").children("[depart!=" + depart + "]:not(':first')").hide();
        }
    });
    // 分页的处理
    var len = "${request.size}";
    window.len = len;
    if (len > 0) {
        $.getScript("${pageContext.request.contextPath}/script/paging.js", function () {
            if ("${request.curpage}" > 1)
                gotoPage("${request.curpage}");
        });
    }
    // 弹出层的隐藏显示
    $(".edit").click(function () {
        if ($(this).css("color") == "rgb(204, 204, 204)")
            return false;
        var name = $(this).attr("name");
        var salary = $(this).attr("salary");
        var username = $(this).attr("username");
        showBg(name, salary, username);
        return false;
    });
    $("img[name='close']").hover(function () {
        $(this).attr("src", "${pageContext.request.contextPath}/images/close-over.png");
    }, function () {
        $(this).attr("src", "${pageContext.request.contextPath}/images/close_c.png");
    });
    $("img[name='close']").click(function () {
        closeBg();
    });
    // 转出操作记录
    $("#outCount").click(function () {
        var superid = $("#superid").val();
        location.href = "../salaryPC/salaryPC_unTurnOut.action?id=0&price=0&realname=0&superid=" + superid;
    });
    /*
        $(".turned").each(function () {
            $(this).html((parseFloat($(this).prev().prev().html()) - parseFloat($(this).prev().html())).toFixed(2));
        });

        $(".deliver").click(function () {
            if ("${request.salaryDisplay}" == 1) return;
            if (parseFloat($(this).attr("salary")) == 0) {
                //alert("该员工没有工资可发，请检查转入剩余金额。");
                return;
            }
            else {
                if (confirm("请再次确认工资发放")) {
                    var superid = $("#superid").val();
                    location.href = "${pageContext.request.contextPath}/role/salary_list.action?superid=" + superid + "&phone=" + $(this).attr("username") + "&curpage=" + $("#cur").html().substring(0, 1);
                }
                else
                    return false;

            }
        });*/

    $("#subedit").click(function () {
        if (confirm("是否确定将工资数调成" + $("#conditions").find("[name='salary']").val() + "?")) {
            return true;
        }
        else
            return false;
    });

});

// creater:hxz 2018-10-15 查看操作详情或者收益详情
function scan(obj, type) {
    var trObj = obj.parents("tr");
    var realname = trObj.attr("realname");
    var username = trObj.attr("username");
    var superid = $("#superid").val();
    if (type == 1) { // 查看操作详情
        location.href = "../salaryPC/salaryPC_getUserOperate.action?superid=" + superid + "&realname=" + realname + "&username=" + username;
    } else { // 查看收益详情
        location.href = "../salaryPC/salaryPC_interestHistory.action?superid=" + superid + "&realname=" + realname + "&username=" + username + "&type=cur";
    }
}

// creater:hxz 2018-10-15 返回主页
function goback() {
    location.reload();
}

// creater:hxz 2018-10-15 关闭/开启账户
var eidtObi = null;

function closeOrStartBtn(obj, type) { // type:0-关闭 ， 1-启用
    bounce.show($("#closeCount"));
    eidtObi = obj;
    $("#setType").val(type);
    if (type == 1) {
        $("#tip").html("是否确定将该员工薪资宝开启?");
    } else {
        $("#tip").html("是否确定将该员工薪资宝关闭?");
    }
}
function closeCountOk() {
    var setType = $("#setType").val();
    var superid = $("#superid").val();
    var username = eidtObi.parents("tr").attr("username");
    var url = "/salaryPC/salaryPC_stopUse.action";
    if (setType == 1) {
        url = "/salaryPC/salaryPC_startUse.action";
    }
    $.ajax({
        "url": url,
        "data": {"superid": superid, "username": username, "pc": "1"},
        success: function (data) {
            bounce.cancel();
            var jsonbor = data.result;
            if (jsonbor == 'success') {
                eidtObi.parents("tr").remove();
                layer.msg("操作成功");
            } else {
                if (setType == 1) {
                    layer.msg("操作失败，请重试！");
                } else {
                    layer.msg("账户余额不为0时不允许关闭该账户！");
                }
            }
        }
    });

}
// creater:hxz 2018-10-15 已关闭账户列表
function closeCountListBtn() {
    $(".main").hide();
    $(".closeContainer").show();
    $(".ty-header>p").append("<span class=\"nav_\"> / </span><span class=\"navTxt\"><i class=\"\"></i>已关闭的账户</span>");
    $.ajax({
        "url": "../role/salary_shutList.action",
        success: function (res) {
            var shutList = res["shutList"], str = "";
            if (shutList && shutList.length > 0) {
                for (var i = 0; i < shutList.length; i++) {
                    str += "<tr username='" + shutList[i]["username"] + "' realname='" + shutList[i]["realname"] + "'>" +
                        "    <td>" + shutList[i]["empid"] + "</td>" +
                        "    <td>" + shutList[i]["realname"] + "</td>" +
                        "    <td>" + (shutList[i]["shuttime"].substr(0, 19)) + "</td>" +
                        "    <td>" + (shutList[i]["depart"] || "") + "</td>" +
                        "    <td>" + (shutList[i]["duty"] || "") + "</td>" +
                        "    <td class='ty-td-control'><span class='ty-color-blue' onclick='scan($(this))'>查看</span></td>" +
                        "    <td class='ty-td-control'><span class='ty-color-blue' onclick='scan($(this) , 2)'>查看</span></td>" +
                        "    <td class='ty-td-control'><span class='ty-color-red' onclick='closeOrStartBtn($(this) , 1)'>开启</span></td>" +
                        "</tr>";
                }
            }
            $("#closeList tbody").html(str)
        }
    })
}

function preparePageing() {
    if (len > 0) {
        $.getScript("${pageContext.request.contextPath}/script/paging.js", function () {
            if ("${request.curpage}" > 1)
                gotoPage("${request.curpage}");
        });
    }
}

function gotoPage(dirpage) {
    //alert(dirpage);
    curpage = dirpage;
    if (curpage <= pages)
        $("#cur").html(curpage + " / " + pages);
    $("#con tr:gt(0)").hide();
    temp = (curpage - 1) * pageno;
    $("#con tr:lt(" + (temp + pageno + 1) + ")").show();
    $("#con tr:lt(" + (temp + 1) + ")").hide();
    $("#con tr:eq(0)").show();
}
function showBg(name, salary, username) {
    var bh = window.screen.height;
    var bw = window.screen.width;
    $("#fullbg").css({
        height: bh,
        width: bw,
        display: "block"
    });
    $("#dialog [name='realname']").val(name);
    $("#dialog [name='salary']").val(salary);
    $("#dialog [name='username']").val(username);
    $("#dialog").show(200);
}

//关闭灰色 jQuery 遮罩
function closeBg() {
    $("#fullbg").hide(200);
    $("#dialog").hide(200);
}
 