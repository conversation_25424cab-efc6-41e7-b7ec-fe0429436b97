/* =========  考勤管理主页 ===========*/
let openStatus = false;
$(function(){
    // 查询按钮的点击事件
    $(".attendanceQuery").on("click","button",function () {
        var name = $(this).data("name");
        var page = Number($("#pageNum").val()); // 1=首页、2=统计页、3=详细页、4=考勤修改页明细、5=考勤修改页统计
        $("#byUserKey").show(); // 按职工查找只在首页不显示
        switch (name) {
            case 'countScreen':
                // 考勤统计
                if (page === 4 || page === 5) {
                    setTimeTip(5);
                } else {
                    setTimeTip(2);
                }
                // 最顶部按钮
                $(".collectionBtn .back").show().siblings().hide();
                $(".main .ty-tblContainer.count").show().siblings().hide();
                getAttendanceUsers();
                getAttendanceCount(1, 20);
                break;
            case 'detailScreen':
                //
                if (page === 4 || page === 5) {
                    setTimeTip(4);
                } else {
                    setTimeTip(3);
                }
                $(".collectionBtn .back").show().siblings().hide();
                $(".main .ty-tblContainer.detail").show().siblings().hide();
                getAttendanceUsers();
                getAttendanceDetail(1, 20);
                break;
            case 'userScreen':
                $("#departSelect").val("");
                if (page === 2 || page === 5) {
                    getAttendanceCount(1, 20);
                } else if (page === 3 || page === 4) {
                    getAttendanceDetail(1, 20);
                }
                break;
            case 'departScreen':
                $("#userKey").val("");
                if (page === 1) {
                    $("#byUserKey").hide();
                    var partId = $("#departSelect").val();
                    seeAttendantDetails(partId);
                } else if (page === 2 || page === 5) {
                    getAttendanceCount(1, 20);
                } else if (page === 3 || page === 4) {
                    getAttendanceDetail(1, 20);
                }
                break;
        }
    });

    // 未设置考勤 以及 设置后还未到开始时间都不能操作按钮（除了考勤设置）
    if (setStatus === '1' && (Date.parse(new Date(startUp)) <= hostTime)) {
        $(".main button").prop("disabled", false)
        $(".main").find("input,select").prop("disabled", false)
    }

    // 初始化数据
    $(".attendanceQuery").find('input,select').val("");
    selectFormInit("00:00",0,$(".halfTime")); // 所有半点输入框赋值
    setAllDeparts($("#departSelect")); // 赋值职工列表
    seeAttendantDetails(""); // 展示统计列表
    $.ajax({//1.323 是否考勤宝模式
        url: $.webRoot + "/workAttendance/getAttendancePattern.do",
        success: function (data) {
            openStatus = data?.success===1
        }
    })
});

// updater: 张旭博，2020-11-24 09:55:48，返回功能
function back(obj){
    $(".main .back").hide().siblings().show()
    $(".main .attendanceQuery .queryItem").show()
    $(".main .attendanceQuery #byUserKey").hide()
    $(".main .attendanceQuery #byLastMonth").hide()
    $(".main .attendanceQuery #byNowMonth").hide()
    $(".main .collectInfo").show().siblings().hide()
    $(".attendanceQuery").find("select,input").val("")
    seeAttendantDetails("");
}

// =========  考勤统计 ===========*/

// updater: 张旭博，2020-11-10 18:12:11，团队考勤主页
function seeAttendantDetails(id){
    setTimeTip(1);
    $.ajax({
        url: "../workAttendanceTeam/getAttendanceManage.do",
        data: {
            deptId: id,
            oid: sphdSocket.user.oid,
            userId: sphdSocket.user.userID
        },
        success: function (data) {
            var attendance = data.data.userList;
            var threeTitle = "";
            if(attendance && attendance.length > 0){
                if(attendance[0].workOrRest == "1"){
                    $(".workOrRest").html("今日无需考勤").attr("type","rest");
                }else{
                    $(".workOrRest").attr("type","work");
                }
                var str ="";
                var leftTdArr = ['今日', '昨日', '前日']
                for(var i=0 ;i<attendance.length;i++ ){
                    var dateStr = attendance[i].date;
                    var allStuff = JSON.stringify(attendance[i].userList); //应出勤人员
                    var realStuff = JSON.stringify(attendance[i].realUser); //实际出勤人员
                    var notInStuff = JSON.stringify(attendance[i].notInAttendanceUser); //不参与考勤人员
                    var lateStuff = JSON.stringify(attendance[i].lateUser); //迟到人员
                    var lateUser = JSON.stringify(attendance[i].leaveEarlyUser); //早退人员
                    var outsideUser = JSON.stringify(attendance[i].outsideUser); //外出人员
                    var travelUser = JSON.stringify(attendance[i].leaveUser); //请假人员
                    var leaveUser = JSON.stringify(attendance[i].travelUser); //出差人员
                    var absenteeismUser = JSON.stringify(attendance[i].absenteeismUser); //旷工人员
                    var overTimeUser = JSON.stringify(attendance[i].overTimeUser); //加班人员
                    str +=  '<tr date="'+ formatTime(attendance[i].date, true) +'"><th>'+leftTdArr[i]+'</th>';
                    str += attendance[i].userTotalNum   ? '<td class="hover" onclick="seeDetails(0,$(this))">'+ attendance[i].userTotalNum    + '<span class="hd">'+allStuff      +'</span></td>': '<td>--</td>'
                    str += attendance[i].notInAttendance? '<td class="hover" onclick="seeDetails(9,$(this))">'+ attendance[i].notInAttendance + '<span class="hd">'+notInStuff    +'</span></td>': '<td>--</td>'
                    str += attendance[i].realNum        ? '<td class="hover" onclick="seeDetails(1,$(this))">'+ attendance[i].realNum         + '<span class="hd">'+realStuff     +'</span></td>': '<td>--</td>'
                    str += attendance[i].leaveNum       ? '<td class="hover" onclick="seeDetails(5,$(this))">'+ attendance[i].leaveNum        + '<span class="hd">'+travelUser    +'</span></td>': '<td>--</td>'
                    str += attendance[i].travelNum      ? '<td class="hover" onclick="seeDetails(6,$(this))">'+ attendance[i].travelNum       + '<span class="hd">'+leaveUser     +'</span></td>': '<td>--</td>'
                    str += attendance[i].outsideNum     ? '<td class="hover" onclick="seeDetails(4,$(this))">'+ attendance[i].outsideNum      + '<span class="hd">'+outsideUser   +'</span></td>': '<td>--</td>'
                    str += attendance[i].lateNum        ? '<td class="hover" onclick="seeDetails(2,$(this))">'+ attendance[i].lateNum         + '<span class="hd">'+lateStuff     +'</span></td>': '<td>--</td>'
                    str += attendance[i].leaveEarlyNum  ? '<td class="hover" onclick="seeDetails(3,$(this))">'+ attendance[i].leaveEarlyNum   + '<span class="hd">'+lateUser      +'</span></td>': '<td>--</td>'
                    str += attendance[i].absenteeismNum ? '<td class="hover" onclick="seeDetails(7,$(this))">'+ attendance[i].absenteeismNum  + '<span class="hd">'+absenteeismUser+'</span></td>': '<td>--</td>'
                    str += attendance[i].overTimeNum    ? '<td class="hover" onclick="seeDetails(8,$(this))">'+ attendance[i].overTimeNum     + '<span class="hd">'+overTimeUser  +'</span></td>': '<td>--</td>'
                    str +=  '</tr>';
                }
                $(".collectInfo table tbody").html(str);
            }
        }
    })
}

// updater: 张旭博，2020-11-13 16:15:47，团队考勤主页 - 查看
function seeDetails(num, obj){
    // 应出勤人数 查看
    var info = JSON.parse(obj.find('.hd').html())
    var tHeadStr = ''
    var tBodyStr = ''
    if (info) {
        switch (num) {
            // 应出勤人数 - 查看
            case 0:
            // 实际出勤人数 - 查看
            case 1:
            // 外出人员 - 查看
            case 4:
            // 不参与考勤人员 - 查看
            case 9:
                tHeadStr =  '<tr>' +
                    '   <th>序号</th>' +
                    '   <th>部门</th>' +
                    '   <th>姓名</th>' +
                    '</tr>';
                for (var i = 0; i<info.length; i++) {
                    tBodyStr += '<tr>' +
                        '    <td>'+ ( + ( + i + 1)) +'</td>' +
                        '    <td>'+ handleNull(info[i].departName) +'</td>' +
                        '    <td>'+ handleNull(info[i].userName) +'</td>' +
                        '</tr>';
                }
                break;
            // 迟到人员 - 查看
            case 2:
            // 早退人员 - 查看
            case 3:
                tHeadStr =  '<tr>' +
                    '   <th>序号</th>' +
                    '   <th>部门</th>' +
                    '   <th>姓名</th>' +
                    '   <th>考勤人</th>' +
                    '   <th>考勤时间</th>' +
                    '</tr>';
                for (var i = 0; i<info.length; i++) {
                    tBodyStr += '<tr>' +
                        '    <td>'+ ( + i + 1) +'</td>' +
                        '    <td>'+ handleNull(info[i].departName) +'</td>' +
                        '    <td>'+ handleNull(info[i].userName) +'</td>' +
                        '    <td>'+ handleNull(info[i].createName) +'</td>' +
                        '    <td>'+ formatTime(info[i].updateDate, true) +'</td>' +
                        '</tr>';
                }
                break;
            // 请假人员 - 查看
            case 5:
                tHeadStr =  '<tr>' +
                    '   <th>序号</th>' +
                    '   <th>部门</th>' +
                    '   <th>姓名</th>' +
                    '   <th>请假类型</th>' +
                    '   <th>详情</th>' +
                    '</tr>';
                for(var j in info){
                    var leaveList = info[j].personnelAttendanceUserDetailList
                    for(var i in leaveList){
                        if(leaveList[i].type === "5"){
                            var source = {
                                source: leaveList[i].source,
                                business: leaveList[i].business,
                                id: leaveList[i].id
                            }
                            tBodyStr += '<tr>' +
                                '   <td>'+ ( + i + 1) +'</td>' +
                                '   <td>'+ handleNull(info[j].departName) +'</td>' +
                                '   <td>'+ handleNull(info[j].userName) +'</td>'+
                                '   <td>'+ handleNull(leaveList[i].leaveTypeName) +'</td>' +
                                '   <td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="seeLeaveRecord(\'leave\', $(this))">查看</button></td>' +
                                '   <td class="hd">'+JSON.stringify(source)+'</td>' +
                                '</tr>';
                        }
                    }
                }
                break;
            // 出差人员 - 查看
            case 6:
                tHeadStr =  '<tr>' +
                    '   <th>序号</th>' +
                    '   <th>部门</th>' +
                    '   <th>姓名</th>' +
                    '   <th>出差事由</th>' +
                    '</tr>';
                for (var i = 0; i<info.length; i++) {
                    tBodyStr += '<tr>' +
                        '    <td>'+ ( + i + 1) +'</td>' +
                        '    <td>'+ handleNull(info[i].departName) +'</td>' +
                        '    <td>'+ handleNull(info[i].userName) +'</td>' +
                        '    <td></td>' +
                        '</tr>';
                }
                break
            // 旷工人员 - 查看
            case 7:
                tHeadStr =  '<tr>' +
                    '   <th>序号</th>' +
                    '   <th>部门</th>' +
                    '   <th>姓名</th>' +
                    '   <th>开始时间</th>' +
                    '   <th>结束时间</th>' +
                    '   <th>考勤人</th>' +
                    '   <th>考勤时间</th>' +
                    '</tr>';
                for (var i = 0; i<info.length; i++) {
                    tBodyStr += '<tr>' +
                        '    <td>'+ ( + i + 1) +'</td>' +
                        '    <td>'+ handleNull(info[i].departName) +'</td>' +
                        '    <td>'+ handleNull(info[i].userName) +'</td>' +
                        '    <td>'+ (new Date(info[i].beginTimeType).format('hh:mm') ) +'</td>' +
                        '    <td>'+ (new Date(info[i].endTimeType).format('hh:mm') ) +'</td>' +
                        '    <td>'+ handleNull(info[i].createName) +'</td>' +
                        '    <td>'+ formatTime(info[i].updateDate, true)+'</td>' +
                        '</tr>';
                }
                break
            // 加班人员 - 查看
            case 8:
                var number = 0
                tHeadStr =  '<tr>' +
                    '   <th>序号</th>' +
                    '   <th>部门</th>' +
                    '   <th>姓名</th>' +
                    '   <th>批准时长</th>' +
                    '   <th>详情</th>' +
                    '</tr>';
                for(var i=0; i<info.length; i++){
                    var overList = info[i]["personnelAttendanceUserDetailList"];
                    for(var t=0;t<overList.length;t++){
                        if(overList[t].type == "8"){
                            number = number+1;
                            var source = {
                                source: overList[t].source,
                                business: overList[t].business,
                                id: overList[t].id
                            }
                            tBodyStr += '<tr>' +
                                '   <td>'+ number +'</td>' +
                                '   <td>'+ handleNull(info[i].departName)+'</td>' +
                                '   <td>'+ handleNull(info[i].userName)+'</td>' +
                                '   <td>'+ handleNull(overList[t].duration)+'h</td>' +
                                '   <td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="seeLeaveRecord(\'overTime\', $(this))">查看</button></td>' +
                                '   <td class="hd">'+JSON.stringify(source)+'</td>' +
                                '</tr>';
                        }
                    }
                }
                break
        }
    }

    var tipArr = ['应出勤人员', '实际出勤人员', '迟到人员', '早退人员', '外出人员', '请假人员', '出差人员', '旷工人员', '加班人员', '不参与考勤人员']
    // 设置标题
    $("#seeDetails .bounce_title").html(moment(obj.parent().attr("date")).format('YYYY年MM月DD日') + tipArr[num]) ;

    // 设置表单主体
    $("#seeDetails table thead").html(tHeadStr) ;
    $("#seeDetails table tbody").html(tBodyStr) ;
    bounce.show($("#seeDetails")) ;
}

// creator: 李玉婷，2020-07-17 06:56:55，查询考勤月查询统计
function getAttendanceCount(currPage,pageSize){
    var userId = $("#userKey").val();
    var deptId = $("#departSelect").val();
    var yearMonth = $("#attendanceTip").data("date");
    var timeKey = yearMonth.replace("-","");
    $("#attendCountList tbody").html("");
    $.ajax({
        url:"../workAttendanceTeam/getAttendanceMonthlyStatistics.do" ,
        data:{
            deptId: deptId,
            userId: userId,
            oid: sphdSocket.user.oid,
            yearMonth: timeKey,
            currentPageNo: currPage,
            pageSize: pageSize,
            loginUserId: sphdSocket.user.userID
        },
        success:function(res){
            var list = res.data;
            var pageInfo = res.page

            // 设置分页
            var totalPage = pageInfo.totalPage; //总页数
            var curr = pageInfo.currentPageNo; //当前页
            setPage( $("#count_page") , curr ,  totalPage , "attendanceCount" );

            // 设置内容
            if(list && list.length > 0) {
                var html = '';
                if (yearMonth == "") {
                    yearMonth = new Date().getTime() + diff;
                    yearMonth = new Date(yearMonth).format('yyyy-MM');
                }
                var timeNote = yearMonth + '-01 00:00:01';
                for (var i = 0; i < list.length; i++) {
                    html += '<tr data-id="'+list[i].user+'">';
                    if (openStatus){
                        html += '<td onclick="showClockRecord($(this))"><span class="blueFont">' + list[i].userName + '</span><span class="hd">'+ JSON.stringify(list[i]) +'</span>';
                    } else {
                        html +=  '<td>' + list[i].userName +'</span>';
                    }
                    html += '</td>' +
                        '    <td>' + (list[i].departmentName?list[i].departmentName:"") + '</td>';
                    html += list[i].leaveNum         ? '<td class="hover" onclick="logs(5,$(this))">'+ list[i].leaveNum       + '</td>': '<td>0</td>'; // 请假
                    html += list[i].travelNum       ? '<td class="hover" onclick="logs(6,$(this))">'+ list[i].travelNum     + '</td>': '<td>0</td>'; // 出差
                    html += list[i].outsideNum      ? '<td class="hover" onclick="logs(4,$(this))">'+ list[i].outsideNum    + '</td>': '<td>0</td>'; // 外出
                    html += list[i].lateNum         ? '<td class="hover" onclick="logs(2,$(this))">'+ list[i].lateNum       + '</td>': '<td>0</td>'; // 迟到
                    html += list[i].leaveEarlyNum   ? '<td class="hover" onclick="logs(3,$(this))">'+ list[i].leaveEarlyNum + '</td>': '<td>0</td>'; // 早退
                    html += list[i].absenteeismNum  ? '<td class="hover" onclick="logs(7,$(this))">'+ list[i].absenteeismNum+ '</td>': '<td>0</td>'; // 旷工
                    html += list[i].overtimeNum    ? '<td class="hover" onclick="logs(8,$(this))">'+ list[i].overtimeNum  + '</td>': '<td>0</td>'; // 加班
                    html += '</tr>' ;
                }
                $("#attendCountList tbody").html(html);
                $("#attendCountList").data('date', timeNote);
            }
        }
    });
}
let clockRecordPage = {}
function showClockRecord(obj){
    let info = JSON.parse(obj.find(".hd").html())
    let yearMonth =	info.yearmonth.toString().substring(0, 4) + '-' + info.yearmonth.toString().substring(4)
    $("#recordTime").html(yearMonth);
    $("#recordName").html(info.userName	 + ' ' + yearMonth);
    $("#clockRecordList tbody tr:gt(0)").remove()
    clockRecordPage = {
        "start" : yearMonth,
        "period" : 3 ,
        "currentPageNo": 1,
        "pageSize":20,
        "userID": info.user
    }
    getClockRecord(1)
}
function getClockRecord(pageNum){
    $("#clockRecordList tbody tr:gt(0)").remove()
    clockRecordPage.currentPageNo = pageNum
    //查询考勤记录，支持：分页/不分页；按时间段/年月日时分秒；用户id/姓名；部门id/名称；打卡手机唯一码/品牌/型号；打卡器唯一码/编号；
    // 打卡记录的卡号/打卡类型等查询，字符串查询属性均支持模糊查询。见1.323接口文档
    $.ajax({
        url: $.webRoot + "/workAttendance/getPersonnelAttendancePunches.do",
        data: clockRecordPage,
        success: function (data) {
            let list = data.data || [];
            let str = ``
            let dateGroup = list.map(item => item.punchDateStr)
            let dateList = [...new Set(dateGroup)]
            let jsonStr = JSON.stringify({}) ;
            setPage( $("#clockRecordPage") , data.page.currentPageNo ,  data.page["totalPage"] , "clockRecord" ,jsonStr );
            dateList.forEach(function (item, index){
                let result = list.filter(value => value.punchDateStr == item)
                if (result.length > 0) {
                    for (let i=0;i <result.length;i++) {
                        str +=
                            `<tr>
                            ${(i===0? '<td rowspan="'+ result.length+'">'+ result[i].punchDateStr +'</td>':'' )}
                                <td>${result[i].punchTimeStr}</td>
                                <td>${result[i].punchTypeStr || ''}</td>
                                <td>${result[i].brand || ''}</td>
                                <td>${result[i].model || ''}</td>
                                <td>${result[i].terminalUuid || ''}</td>
                                <td>${result[i].sn || ''}</td>
                            </tr>`
                    }
                }
            })
            $("#clockRecordList").append(str)
        }
    });
    bounce.show($("#clockRecord"));
}
// updater: 张旭博，2020-11-20 08:44:29，查询考勤月查询统计 - 查看
function logs(type, obj){
    var date = $("#attendCountList").data('date');
    var userId = obj.parents("tr").data('id');
    var param = {
        ttType: 2,
        type: type,
        beginTime: date,
        userId: userId
    };
    $.ajax({
        url: "../workAttendance/getPersonnelLeave.do",
        data: param,
        success: function (data) {
            var attendanceData = data["listMap"];
            var personName = data["userName"];
            if(attendanceData && attendanceData.length>0){
                $(".personName").html(personName) ;
                var  sequence = 0;
                //  请假记录
                if( type == 5 ) {
                    $("#log .type").html("请假") ;
                    var str =   '<thead>' +
                        '    <tr>' +
                        '        <th>序号</th>' +
                        '        <th>日期</th>' +
                        '        <th>请假类型</th>' +
                        '        <th>详情</th>' +
                        '    </tr>'+
                        '</thead>'+
                        '<tbody>';
                    for(var t= 0;t<attendanceData.length;t++){
                        var seeDetail = attendanceData[t];
                        sequence = 1+t;
                        var source = {
                            source: seeDetail.source,
                            business: seeDetail.business,
                            id: seeDetail.id
                        }
                        str +=  '   <tr>' +
                            '       <td>'+ sequence +'</td>' +
                            '       <td>'+ (new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') )+'</td>' +
                            '       <td>'+ seeDetail.leaveTypeName +'</td>'+
                            '       <td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="seeLeaveRecord(\'leave\', $(this))">查看</button></td>' +
                            '       <td class="hd">'+JSON.stringify(source)+'</td>' +
                            '   </tr>';
                    }
                    str +=      '</tbody>' ;
                }
                // 迟到记录
                if( type == 2 ){
                    $("#log .type").html("迟到") ;
                    var str =   '<thead>'+
                        '   <tr>' +
                        '       <th>序号</th>' +
                        '       <th>日期</th>' +
                        '       <th>考勤人</th>' +
                        '       <th>考勤时间</th>' +
                        '   </tr>' +
                        '</thead>'+
                        '<tbody>';
                    for(var w= 0;w<attendanceData.length;w++){
                        var seeDetail = attendanceData[w];
                        sequence = 1+w;
                        str +=  '   <tr>' +
                            '       <td>'+ sequence +'</td>' +
                            '       <td>'+ (new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') ) +'</td>' +
                            '       <td>'+ seeDetail.createName +'</td>' +
                            '       <td>'+ (new Date(seeDetail.createDate).format('yyyy-MM-dd') ) +'</td>' +
                            '   </tr>';
                    }
                    str +=      '</tbody>' ;
                }
                // 早退记录
                if( type == 3 ) {
                    $("#log .type").html("早退") ;
                    var str =   '<thead>' +
                        '   <tr>' +
                        '       <th>序号</th>' +
                        '       <th>日期</th>' +
                        '       <th>考勤人</th>' +
                        '       <th>考勤时间</th>' +
                        '   </tr>'+
                        '</thead>'+
                        '<tbody>';
                    for(var q= 0;q<attendanceData.length;q++){
                        var seeDetail = attendanceData[q];
                        sequence = 1+q;
                        str +=  '   <tr>' +
                            '       <td>'+ sequence +'</td>' +
                            '       <td>'+ (new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') ) +'</td>' +
                            '       <td>'+ seeDetail.createName +'</td>' +
                            '       <td>'+ (new Date(seeDetail.createDate).format('yyyy-MM-dd hh:mm') )  +'</td>' +
                            '   </tr>';
                    }
                    str +=      '</tbody>';
                }
                // 旷工记录
                if( type == 7 ) {
                    $("#log .type").html("旷工") ;
                    var str =   '<thead>'+
                        '   <tr>' +
                        '       <th>序号</th>' +
                        '       <th>日期</th>' +
                        '       <th>开始时间</th>' +
                        '       <th>结束时间</th>' +
                        '       <th>考勤人</th>' +
                        '       <th>考勤时间</th>' +
                        '   </tr>'+
                        '   </thead>'+
                        '<tbdoy>';
                    for(var w= 0;w<attendanceData.length;w++){
                        var seeDetail = attendanceData[w];
                        sequence = 1+w;
                        str +=  '   <tr>' +
                            '       <td>'+ sequence +'</td>' +
                            '       <td>'+ (new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') ) +'</td>' +
                            '       <td>'+ (new Date(seeDetail.beginTime).format('hh:mm') )+'</td>' +
                            '       <td>'+(new Date(seeDetail.endTime).format('hh:mm') ) +'</td>' +
                            '       <td>'+ seeDetail.createName +'</td>' +
                            '       <td>'+  (new Date(seeDetail.createDate).format('yyyy-MM-dd hh:mm:ss') )  +'</td>' +
                            '   </tr>';
                    }
                    str +=      '</tbody>' ;
                }
                // 加班记录
                if( type == 8 ) {
                    $("#log .type").html("加班") ;
                    var str =   '<thead>' +
                        '   <tr>' +
                        '       <th>序号</th>' +
                        '       <th>日期</th>' +
                        '       <th>批准时长</th>' +
                        '       <th>详情</th>' +
                        '   </tr>'+
                        '</thead>'+
                        '<tbody>';
                    for(var r= 0;r<attendanceData.length;r++){
                        var seeDetail = attendanceData[r];
                        sequence = 1+r;
                        var source = {
                            source: seeDetail.source,
                            business: seeDetail.business,
                            id: seeDetail.id
                        }
                        str +=  '   <tr>' +
                            '       <td>'+sequence+'</td>' +
                            '       <td>'+ (new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') ) +'</td>' +
                            '       <td>'+ seeDetail.duration +'h</td>' +
                            '       <td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="seeLeaveRecord(\'overTime\', $(this))">查看</button></td>' +
                            '       <td class="hd">'+JSON.stringify(source)+'</td>' +
                            '   </tr>';
                    }
                    str +=      '</tbody>' ;
                }
                // 出差记录
                if( type == 6 ) {
                    $("#log .type").html("出差") ;
                    for(var t= 0;t<attendanceData.length;t++){
                        sequence = 1+t;
                    }
                    var str = '' ;
                }
                // 外出记录
                if( type == 4 ) {
                    $("#log .type").html("外出") ;
                    for(var t= 0;t<attendanceData.length;t++){
                        sequence = 1+t;
                    }
                    var str = '' ;
                }
                $("#log .kj-table").html(str) ;
                bounce.show($("#log")) ;
            }
        }
    });
}


// =========  考勤明细 ===========*/

// creator: 李玉婷，2020-07-17 06:56:55，获取考勤明细列表
function getAttendanceDetail(currPage,pageSize){
    var pageNum = $("#pageNum").val();
    var userId = $("#userKey").val();
    var deptId = $("#departSelect").val();
    var yearMonth = $("#attendanceTip").data("date");
    var timeKey = yearMonth.replace("-","");
    $("#attendDetList thead").html("");
    $("#attendDetList tbody").html("");
    $.ajax({
        url: "../workAttendanceTeam/getAttendanceMonthlyTeam.do",
        data: {
            yearMonth : timeKey,
            deptId : deptId,
            userId : userId,
            currentPageNo: currPage,
            pageSize: pageSize,
            oid: sphdSocket.user.oid,
            loginUserId: sphdSocket.user.userID // 多加的参数
        },
        success: function (data) {
            var list = data["data"];
            // 设置分页
            var cur = data["page"]["currentPageNo"];
            var total=data["page"]["totalPage"];
            setPage( $("#eidt_page") , cur , total , "updateAttendant");
            if(yearMonth === '')yearMonth = new Date(new Date().getTime() + diff).format('yyyy-MM');
            var timeNote = yearMonth+'-01 00:00:01';
            var orgDate = new Date(timeNote);
            var orgYear = orgDate.getFullYear();
            var orgMonth = orgDate.getMonth();
            //获取该月有多少天
            var totleDays = new Date(orgYear, orgMonth + 1, 0);
            totleDays = totleDays.getDate();
            //获取1号是周几
            var firstDay = new Date(orgYear, orgMonth, 1);  //获得month的1号
            var firstDayWeekday = firstDay.getDay();
            if (firstDayWeekday == 0) {
                firstDayWeekday = 7;
            }
            var weekDay = ["日", "一", "二", "三", "四", "五", "六"];
            var rhtHtml = '';
            var tHead = '<tr>' +
                '   <th class="fixed" rowspan="2"> 姓名 </th>' +
                '   <th class="fixed2" rowspan="2"> 部门 </th>' +
                '   <th class="fixed3"> 星期 </th>';
            for(var l= firstDayWeekday;l<totleDays+firstDayWeekday;l++){
                var num = l%7 ;
                tHead += '  <th>'+ weekDay[num] +'</th>';
            }
            tHead +=    '</tr>';
            tHead +=    '<tr>' +
                '   <th class="fixed3">日期</th>';
            for(var g = 1; g <= totleDays; g++){
                tHead += '  <th><i>'+ g +'</i></th>';
            }
            tHead += '</tr>';
            $("#attendDetList thead").html(tHead);

            // 填充tbody数据
            if(list) {
                var overTimeStr = '';
                for (var h = 0; h < list.length; h++) {
                    var everyDayAttend = list[h];
                    overTimeStr = '<tr><th class="fixed3">加班</th>';
                    var variable = '';
                    var overTimeVar = '';
                    var dayStr = '<tr>' +
                        '<th class="fixed" rowspan="2" scope="row">'+ everyDayAttend.userName +'</th>' +
                        '<th class="fixed2" rowspan="2" scope="row">'+ handleNull(everyDayAttend.departmentName) +'</th>' +
                        '<th class="fixed3">考勤</th>';
                    for (var s = 1; s <= totleDays; s++) {
                        var infoDate = '';
                        if(s<10){
                            variable = everyDayAttend["dayStatus0" +s];
                            overTimeVar =everyDayAttend["overtimeDuration0" +s];
                            infoDate = yearMonth+'-0' + s;
                        }else{
                            variable = everyDayAttend["dayStatus" +s];
                            overTimeVar =everyDayAttend["overtimeDuration" +s];
                            infoDate = yearMonth +'-'+ s;
                        }
                        var info={
                            "date":infoDate,
                            "userID":everyDayAttend.user,
                            "name":everyDayAttend.userName
                        };
                        info = JSON.stringify(info);
                        switch (variable) {//1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 0-其他
                            case "complex":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(9,$(this))"><i class="fa fa-star-o"></i></td>';
                                break;
                            case "noNeed":
                                if (pageNum ==3) {
                                    dayStr += '<td><i class="fa fa-minus"></i></td>';
                                }else if (pageNum == 4) {
                                    dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(10,$(this))"><i class="fa fa-minus"></i></td>';
                                }
                                break;
                            case "normal":
                            case "overtime":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(1,$(this))"><i class="fa fa-check"></i></td>';
                                break;
                            case "late":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(2,$(this))"><i class="fa fa-level-down"></i></td>';
                                break;
                            case "leaveEarly":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(3,$(this))"><i class="fa fa-level-up"></i></td>';
                                break;
                            case "outside":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(4,$(this))"><i class="fa fa-bicycle"></i></td>';
                                break;
                            case "leave":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(5,$(this))"><i class="fa fa-clock-o"></i></td>';
                                break;
                            case "travel":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(6,$(this))"><i class="fa ty-plane"></i></td>';
                                break;
                            case "absenteeism":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(7,$(this))"><i class="fa fa-times"></i></td>';
                                break;
                            default:
                                dayStr += '<td><i></i></td>';
                                break;
                        }
                        if(overTimeVar) {
                            overTimeStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(1,$(this),1)"><span>' + overTimeVar + '</span></td>';
                        } else {
                            overTimeStr += '<td></td>';
                        }
                    }
                    dayStr += '</tr>';
                    overTimeStr += '</tr>';
                    rhtHtml += dayStr + overTimeStr;
                }
                $("#attendDetList tbody").html(rhtHtml);
            }
        },
    });
}

// updater: 张旭博，2020-11-27 10:27:31，获取考勤明细列表的详情
function takeDetail(type, obj, showType){
    // showType : 考勤类型 0-全部类型 / 其他类型,只在显示时用到
    // 此接口在考勤统计详情(type: 1..) / 月考勤详情中用到(type: all)
    var thisDay = obj.attr("someInfo");
    var thisDayInfo = {};
    if(thisDay){
        thisDay = JSON.parse(thisDay);
        thisDayInfo = thisDay;
    }
    var param = {
        ttType: 1, // 查询某月/某天的考勤情况（1-某天的考勤情况  2-某月的考勤情况）
        type: type, // 考勤类型（1-正常,2-迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，0-其他）
        beginTime: thisDay.date, // 开始时间 (当查询某天的考勤情况时，此时间为具体的某一天；当查询为某月的考勤情况时，此时间为此月的某一天传递，后台处理)
        userId: thisDay.userID //查询考勤人员id
    };
    $.ajax({
        url: "../workAttendance/getPersonnelLeave.do",
        data: param,
        success: function (res, status, xhr) {
            var userInfo    = res.personnelAttendanceUser1; // 列表信息
            var baseInfo    = res.personnelAttendanceUserDetail;
            var inputTimeEnd= res.inputTime;
            var beginTime   = res.beginTime;
            var endTime     = res.endTime;

            var pageNum = $("#pageNum").val();
            var todaySys = new Date(xhr.getResponseHeader('Date')).format('yyyy-MM-dd');
            thisDayInfo.beginTime = beginTime;
            thisDayInfo.endTime = endTime;
            if(userInfo == null){
                //无需考勤
                if(beginTime == null){
                    layer.msg("该部门未设置考勤，不可修改");
                }else{
                    $("#theDayMorn").val("0");
                    $("#theDayNoon").val("0");
                    $("#updateOutOfWork .fa").attr("haveOut","0");
                    $("#updateLeave .fa").attr("haveOut","0");
                    $("#updateOvertime .fa").attr("haveOut","0");
                    $(".commenWind span.fa").attr("class","fa fa-circle-o");
                    $(".editSure").attr("onclick","editOnceAttence()");
                    $("#updateAttend .import").html(JSON.stringify(thisDayInfo));
                    attendUpdate();
                }
            }else{
                if(userInfo){
                    // showType： 区分是考勤点进来的还是直接加班时间点进来的，展示不同内容
                    if (showType) {
                        // 直接加班时间点进来的，只显示加班内容
                        $("#attendanceChangeDetail .kj-table").show().siblings().hide()
                        $("#attendanceChangeDetail .bounce_title").html(thisDayInfo.name + ' '+thisDayInfo.date + '加班情况')
                        if(baseInfo){
                            var str = ''
                            for(var t=0;t<baseInfo.length;t++){
                                var typeId = Number(baseInfo[t].type);
                                if(typeId === 8){
                                    // 赋值加班时长
                                    var source = {
                                        source: baseInfo[t].source,
                                        business: baseInfo[t].business,
                                        id: baseInfo[t].id
                                    }
                                    str +=  '<tr>' +
                                        '    <th>加班</th>' +
                                        '    <td>加班时长：'+baseInfo[t].duration+'h <button class="ty-btn ty-btn-blue ty-circle-2 ty-right" onclick="seeLeaveRecord(\'overTime\', $(this))">查看</button></td>' +
                                        '    <td class="hd">'+JSON.stringify(source)+'</td>' +
                                        '</tr>'
                                }
                            }
                        }
                        $("#attendanceChangeDetail .kj-table").html(str)
                        bounce.show($("#attendanceChangeDetail"));
                        thisDayInfo.id = userInfo.id;
                        $("#updateAttend").data("info", thisDayInfo)
                    } else {
                        // 考勤点进来的，显示全部内容
                        $("#attendanceChangeDetail .kj-table").show().siblings().show()
                        $("#attendanceChangeDetail .bounce_title").html(thisDayInfo.name + ' '+thisDayInfo.date + '考勤情况')
                        $("#attendanceChangeDetail .shouldTime").html(' ' + (new Date(beginTime).format('yyyy-MM-dd hh:mm') ) + ' ~ ' + (new Date(endTime).format('yyyy-MM-dd hh:mm') ))
                        var str = ''
                        str +=  '<tr>' +
                            '    <th>上班</th>' +
                            '    <td>'+getType(userInfo.beginState)+'</td>' +
                            '</tr>';
                        // 未到录入时间时不显示下班
                        if (Number(userInfo.endState)) {
                            str +=  '<tr>' +
                                '    <th>下班</th>' +
                                '    <td>'+getType(userInfo.endState)+'</td>' +
                                '</tr>'
                        }

                        // 展示加班请假旷工等信息
                        if(baseInfo){
                            for(var t=0;t<baseInfo.length;t++){
                                var typeId = Number(baseInfo[t].type);
                                if(typeId === 7){
                                    // 赋值旷工时间段
                                    str +=  '<tr>' +
                                        '    <th>旷工</th>' +
                                        '    <td>'+(new Date(baseInfo[t].beginTime).format('hh:mm') ) + ' ~ ' + (new Date(baseInfo[t].endTime).format('hh:mm') )+'</td>' +
                                        '</tr>'
                                }else if(typeId === 8){
                                    // 赋值加班时长
                                    var source = {
                                        source: baseInfo[t].source,
                                        business: baseInfo[t].business,
                                        id: baseInfo[t].id
                                    }
                                    str +=  '<tr>' +
                                        '    <th>加班</th>' +
                                        '    <td>加班时长：'+baseInfo[t].duration+'h <button class="ty-btn ty-btn-blue ty-circle-2 ty-right" onclick="seeLeaveRecord(\'overTime\', $(this))">查看</button></td>' +
                                        '    <td class="hd">'+JSON.stringify(source)+'</td>' +
                                        '</tr>'
                                }else if(typeId === 5){
                                    // 赋值请假时长
                                    var source = {
                                        source: baseInfo[t].source,
                                        business: baseInfo[t].business,
                                        id: baseInfo[t].id
                                    }
                                    str +=  '<tr>' +
                                        '    <th>请假 - '+baseInfo[t].leaveTypeName+'</th>' +
                                        '    <td>'+(new Date(baseInfo[t].beginTime).format('hh:mm') ) + ' ~ ' + (new Date(baseInfo[t].endTime).format('hh:mm'))+' <button class="ty-btn ty-btn-blue ty-circle-2 ty-right" onclick="seeLeaveRecord(\'leave\', $(this))">查看</button></td>' +
                                        '    <td class="hd">'+JSON.stringify(source)+'</td>' +
                                        '</tr>'
                                }
                            }
                        }
                        thisDayInfo.id = userInfo.id;

                        if (thisDay.date !== todaySys && pageNum !== '3' && userInfo.endState !== "0") {
                            $("#attendanceChangeDetail .changeBtn").show()
                            // $(".editSure").attr("onclick","updateHistoryAttend()");
                        } else {
                            $("#attendanceChangeDetail .changeBtn").hide()
                        }
                        $("#attendanceChangeDetail .creator").html("创建人：" + userInfo.updateName + ' ' + userInfo.updateDate)
                        $("#attendanceChangeDetail .kj-table").html(str)
                        bounce.show($("#attendanceChangeDetail"));
                        $("#updateAttend").data("info", thisDayInfo)
                    }
                }
            }
        }
    });
}

// creator: 张旭博，2020-11-12 08:54:09，查看请假/加班记录
function seeLeaveRecord(name, selector) {

    bounce_Fixed.show($("#leaveRecord"))

    // 从上个接口在页面绑定的值中取值
    var sourceData = JSON.parse(selector.parents("td").siblings('.hd').html())
    var source = Number(sourceData.source)
    var id = source === 1 ? sourceData.business:sourceData.id


    switch (name) {
        case 'leave':
            $.ajax({
                url: '../workAttendance/getLeaveDetail.do',
                data: {
                    leaveId: id, // 请假详情id
                    source: source // 来源 1-审批 2-录入 source=1时，说明此请假来源为审批进入考勤系统的，那么leaveId为business字段值。 source=2时，说明此请假来源为考勤系统的录入，那么leaveId取详情id的字段值
                },
                success: function (res) {
                    var data = res.data
                    if (source === 1) {
                        var detail      = data.personnelLeave // 计划请假详情
                        var processList = data.processList1 // 计划请假的审批流程
                        var personnelLeaveItemList = data.personnelLeaveItems // 提前结束请假详情

                        var str =   '<div class="bounceItem">' +
                            '    <div class="bounceItem_title">职工姓名</div>' +
                            '    <div class="bounceItem_content">' +
                            '        <span class="name">'+detail.userName+'</span>' +
                            '    </div>' +
                            '</div>' +
                            '<div class="ty-hr"></div>';
                        var strLeave = ''
                        if (personnelLeaveItemList) {
                            for (var i in personnelLeaveItemList) {
                                var process = personnelLeaveItemList[i].processList

                                if (personnelLeaveItemList[i].approveStatus === '2') {
                                    str +=  '<div class="kj-panel">' +
                                        '    <div class="bounceItem">' +
                                        '        <div class="bounceItem_title">提前结束请假</div>' +
                                        '        <div class="bounceItem_content"></div>' +
                                        '    </div>' +
                                        '    <div class="bounceItem">' +
                                        '        <div class="bounceItem_title">计划上班时间</div>' +
                                        '        <div class="bounceItem_content">'+moment(personnelLeaveItemList[i].actualEndTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                                        '    </div>' +
                                        '    <div class="bounceItem">' +
                                        '        <div class="bounceItem_title">说明</div>' +
                                        '        <div class="bounceItem_content">'+chargeNull(personnelLeaveItemList[i].actualReason)+'</div>' +
                                        '    </div>' +
                                        '</div>' ;
                                    str +=  '<div class="process">' +
                                        '    <div>申请人 <span class="processName">' + personnelLeaveItemList[i].createName + '</span> ' + formatTime(personnelLeaveItemList[i].createDate, true) + '</div>' ;
                                    for (var k in process) {
                                        if (process[k].approveStatus === '2' || process[k].approveStatus === '3') {
                                            str += ' <div>审批人 <span class="processName">' + process[k].userName + '</span> ' + formatTime(process[k].handleTime, true) + '</div>'
                                        }
                                    }
                                    str +=  '</div><div class="ty-hr"></div>'
                                } else {
                                    strLeave +=     '<div class="ty-hr"></div>' +
                                        '<div class="kj-panel">' +
                                        '    <div class="bounceItem">' +
                                        '        <div class="bounceItem_title">提前结束请假</div>' +
                                        '        <div class="bounceItem_content"></div>' +
                                        '    </div>' +
                                        '    <div class="bounceItem">' +
                                        '        <div class="bounceItem_title">计划上班时间</div>' +
                                        '        <div class="bounceItem_content">'+moment(personnelLeaveItemList[i].actualEndTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                                        '    </div>' +
                                        '    <div class="bounceItem">' +
                                        '        <div class="bounceItem_title">说明</div>' +
                                        '        <div class="bounceItem_content">'+chargeNull(personnelLeaveItemList[i].actualReason)+'</div>' +
                                        '    </div>';

                                    for (var j in process) {
                                        if (process[j].approveStatus === '3') {
                                            strLeave +=     '    <div class="bounceItem ty-color-red">提前结束请假的申请被'+process[j].userName+'驳回！</div>' +
                                                '    <div class="bounceItem ty-color-red">' +
                                                '        <div class="bounceItem_title">驳回理由</div>' +
                                                '        <div class="bounceItem_content">'+chargeNull(process[j].reason)+'</div>' +
                                                '    </div>' ;
                                        }
                                    }
                                    strLeave +=     '</div>' +
                                        '<div class="process">' +
                                        '   <div>申请人 <span class="processName">' + personnelLeaveItemList[i].createName + '</span> ' + formatTime(personnelLeaveItemList[i].createDate, true) + '</div>' ;
                                    for (var k in process) {
                                        if (process[k].approveStatus === '2' || process[k].approveStatus === '3') {
                                            strLeave += '<div>审批人 <span class="processName">' + process[k].userName + '</span> ' + formatTime(process[k].handleTime, true) + '</div>'
                                        }

                                    }
                                    strLeave +=     '</div>' ;
                                }
                            }
                        }
                        str +=      '<div class="kj-panel">' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">开始时间</div>' +
                            '        <div class="bounceItem_content">'+moment(detail.beginTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">结束时间</div>' +
                            '        <div class="bounceItem_content">'+moment(detail.endTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">请假类型</div>' +
                            '        <div class="bounceItem_content">'+detail.leaveTypeName+'</div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">请假事由</div>' +
                            '        <div class="bounceItem_content">'+detail.reason+'</div>' +
                            '    </div>' +
                            '</div>' +
                            '<div class="process">' +
                            '    <div>申请人 <span class="processName">' + detail.createName + '</span> ' +formatTime(detail.createDate, true) + '</div>';

                        for (var m in processList) {
                            if (processList[m].approveStatus === '2' || processList[m].approveStatus === '3') {
                                str += ' <div>审批人 <span class="processName">' + processList[m].userName + '</span> ' + formatTime(processList[m].handleTime, true) + '</div>'
                            }

                        }
                        str +=      '</div>' + strLeave;
                    } else {
                        var detail      = data.personnelAttendanceUserDetail // 请假详情

                        var str =   '<div class="bounceItem">' +
                            '        <div class="bounceItem_title">职工姓名</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="name">'+detail.userName+'</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="ty-hr"></div>' +
                            '    <div class="kj-panel">' +
                            '        <div class="bounceItem">' +
                            '            <div class="bounceItem_title">开始时间</div>' +
                            '            <div class="bounceItem_content">'+moment(detail.beginTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                            '        </div>' +
                            '        <div class="bounceItem">' +
                            '            <div class="bounceItem_title">结束时间</div>' +
                            '            <div class="bounceItem_content">'+moment(detail.endTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                            '        </div>' +
                            '        <div class="bounceItem">' +
                            '            <div class="bounceItem_title">请假类型</div>' +
                            '            <div class="bounceItem_content">'+detail.leaveTypeName+'</div>' +
                            '        </div>' +
                            '        <div class="bounceItem">' +
                            '            <div class="bounceItem_title">请假事由</div>' +
                            '            <div class="bounceItem_content">'+detail.reason+'</div>' +
                            '        </div>' +
                            '    </div>';
                    }
                    // 赋值计划请假详情
                    $("#leaveRecord .bounce_title").html('请假记录')
                    $("#leaveRecord .detail").html(str)
                }
            })
            break
        case 'overTime':
            $.ajax({
                url: '../workAttendance/getOverTimeDetail.do',
                data: {
                    overTimeId: id, // 请假详情id
                    source: source // 来源 1-审批 2-录入 source=1时，说明此请假来源为审批进入考勤系统的，那么leaveId为business字段值。 source=2时，说明此请假来源为考勤系统的录入，那么leaveId取详情id的字段值
                },
                success: function (res) {
                    var data = res.data
                    if (source === 1) {
                        var detail      = data.personnelOvertime // 加班详情
                        var processList1 = data.approvalProcess1 // 计划加班审批流程
                        var processList2 = data.approvalProcess2 // 申报加班和批准加班的审批流程

                        str =   '<div class="bounceItem">' +
                            '    <div class="bounceItem_title">职工姓名</div>' +
                            '    <div class="bounceItem_content">' +
                            '        <span class="name">'+detail.userName+'</span>' +
                            '    </div>' +
                            '</div>' +
                            '<div class="bounceItem">' +
                            '    <div class="bounceItem_title">加班所属日期</div>' +
                            '    <div class="bounceItem_content">' +
                            '        <span class="overtime_beginTime">'+moment(detail.beginTime).format('YYYY-MM-DD dddd')+'</span>' +
                            '    </div>' +
                            '</div>' +
                            '<div class="ty-hr"></div>' +
                            '<div class="kj-panel approveDetail">' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">批准时长</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_approveDuration">'+detail.approveDuration+'</span>h' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">说明</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_approveExplain">'+chargeNull(detail.approveExplain)+'</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="processList">' +
                            '        <div class="process">';
                        for (var i in processList2) {
                            if (processList2[i].approveStatus === '2') {
                                str +=  '   <div>审批人 <span class="processName">'+processList2[i].userName+ '</span> ' +formatTime(processList2[i].handleTime, true)+'</div>';
                            }
                        }
                        str +=  '   </div>' +
                            '</div>' +
                            '<div class="ty-hr"></div>' +
                            '<div class="kj-panel applyDetail">' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">申报时长</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_actualDuration">'+detail.actualDuration+'h</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">申报起止时间</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_actualBeginTime">'+moment(detail.actualBeginTime).format('HH:mm')+'</span> ~ <span class="overtime_actualBeginTime">'+moment(detail.actualEndTime).format('HH:mm')+'</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">加班事由</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_actualReason">'+detail.actualReason+'</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="processList">' +
                            '        <div class="process">' +
                            '            <div>申请人 <span class="processName">' + detail.createName + '</span> ' + formatTime(detail.actualApplyTime, true)+'</div>' +
                            '        </div>' +
                            '    </div>' +
                            '</div>' +
                            '<div class="ty-hr"></div>' +
                            '<div class="kj-panel planDetail">' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">计划时长</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_duration">'+detail.duration+'h</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">计划起止时间</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_beginTime">'+moment(detail.beginTime).format('HH:mm')+'</span> ~ <span class="overtime_endTime">'+moment(detail.endTime).format('HH:mm')+'</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">加班事由</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_reason">'+handleNull(detail.reason)+'</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="processList">' +
                            '        <div class="process">' +
                            '            <div>申请人 <span class="processName">' + detail.createName + '</span> ' + formatTime(detail.createDate, true) + '</div>' ;
                        for (var j in processList2) {
                            if (processList1.length > 0 && processList1[j].approveStatus === '2') {
                                str +=  '        <div>审批人 <span class="processName">'+processList1[j].userName+ '</span> ' +formatTime(processList1[j].handleTime, true)+'</div>'
                            }
                        }
                        str +=  '            </div>' +
                            '        </div>' +
                            '    </div>';

                    } else {
                        var detail      = data.personnelAttendanceUserDetail // 加班详情
                        // 赋值加班详情
                        var str =   '<div class="bounceItem">' +
                            '    <div class="bounceItem_title">职工姓名</div>' +
                            '    <div class="bounceItem_content">' +
                            '        <span class="overtime_createName">'+detail.userName+'</span>' +
                            '    </div>' +
                            '</div>'+
                            '<div class="bounceItem">' +
                            '    <div class="bounceItem_title">加班所属日期</div>' +
                            '    <div class="bounceItem_content">' +
                            '        <span class="overtime_beginTime">'+moment(detail.attendanceDate).format('YYYY-MM-DD dddd')+'</span>' +
                            '    </div>' +
                            '</div>';
                        str +=  '<div class="ty-hr"></div>' +
                            '<div class="approveDetail">' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">加班时长</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_duration">'+detail.duration+'h</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">加班事由</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_reason">'+handleNull(detail.reason)+'</span>' +
                            '        </div>' +
                            '    </div>' +
                            '    <div class="bounceItem">' +
                            '        <div class="bounceItem_title">备注</div>' +
                            '        <div class="bounceItem_content">' +
                            '            <span class="overtime_duration">'+detail.memo+'</span>' +
                            '        </div>' +
                            '    </div>' +
                            '</div>'
                    }
                    $("#leaveRecord .detail").html(str)
                    $("#leaveRecord .bounce_title").html('加班记录')
                }
            })
            break
    }
}

// =========  工具方法 ===========*/

// creator : hxz 2018-04-27 获取全部部门的接口
var allDepartArr = []  ; // 存放所有部门， allDepartsTree 为树形
function setAllDeparts(obj) {
    allDepartArr = [];
    obj.html("");
    $.ajax({
        url: "../workAttendanceTeam/getCheckDepartmentTeam.do" ,
        data: {userId: sphdSocket.user.userID} ,
        success: function(data) {
            var list = data.data["organizations"] ;
            setAllDepr( list ) ;
            var str =  '<option readonly="" value="">请选择部门</option>' ;
            if(allDepartArr && allDepartArr.length >0){
                for(var i=0 ; i < allDepartArr.length; i++){
                    str += '<option value="'+ allDepartArr[i]["id"] +'">'+ allDepartArr[i]["name"] +'</option>'
                }
            }
            obj.html(str);
        }
    })
}

// creator: 侯杏哲 2018-05-02 获取所有部门
function setAllDepr( arr ){
    for(var i = 0 ; i < arr.length ; i++ ){
        allDepartArr.push( {"name":arr[i]["name"],"id":arr[i]["id"] }) ;
        var sub = arr[i]["subList"] ;
        if(sub && sub.length > 0){
            setAllDepr( sub ) ;
        }
    }
}

// creator: 李玉婷，2020-07-16 19:48:59，获取职工列表的接口
function getAttendanceUsers() {
    $("#userKey").html("");
    var time = $("#attendanceTip").data("date");
    time = time.replace("-", "");
    $.ajax({
        url : "../workAttendanceTeam/getAttendanceUsersTeam.do" ,
        data :{
            yearMonth: time,
            oid: sphdSocket.user.oid,
            userId: sphdSocket.user.userID
        } ,
        "success":function(data){
            var list = data["data"]["listMap"] ;
            var str = '<option value="">请选择职工</option>';
            for(var i=0 ; i < list.length; i++){
                str += '<option readonly="" value="'+ list[i]["userId"] +'">'+ list[i]["userName"] +'</option>';
            }
            $("#userKey").html(str);
        },
        "complete":function() {}
    })
}

// creator: 李玉婷，2020-07-17 06:46:42，获取时间提示
function setTimeTip(page) {;
    var currentTime = new Date().getTime() + diff;
    $(".main .nowDay .nowDay_date").html(new Date(currentTime).format('yyyy年MM月dd日'));
    $(".main .nowDay .workOrRest").html(' 星期' + "日一二三四五六".charAt(new Date(currentTime).getDay()));

    $("#pageNum").val(page); // 1=首页、2=统计页、3=详细页、4=考勤修改页明细、5=考勤修改页统计

    var countMonth = $("#countKey").val() ? moment($("#countKey").val()).format('YYYY年MM月') : moment(currentTime).format('YYYY年MM月')
    var detailMonth = $("#detailedKey").val() ? moment($("#detailedKey").val()).format('YYYY年MM月') : moment(currentTime).format('YYYY年MM月')

    switch (page) {
        case 1:
            // 首页
            $("#attendanceTip").html("以下为近三日的考勤情况").data("date", "");
            break;
        case 2:
        case 5:
            // 统计页和考勤修改统计页
            $("#attendanceTip").html("以下为" + countMonth + "的考勤统计").data("date", $("#countKey").val());
            break;
        case 3:
        case 4:
            // 详细页和考勤修改页明细
            $("#attendanceTip").html("以下为" + detailMonth + "的考勤明细").data("date", $("#detailedKey").val());
    }

    // 考勤修改页多一条提示
    if (page === 4) {
        $("#updateAttendanceTip").show();
    } else {
        $("#updateAttendanceTip").hide();
    }
    $(".attendanceQuery").find("input, select").val("");
}

// creator lyt 2018/5/30 考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 0-其他
function getType(num) {
    switch(num){
        case "1":return "正常";break;
        case "2":return "迟到";break;
        case "3":return "早退";break;
        case "4":return "外出";break;
        case "5":return "请假";break;
        case "6":return "出差";break;
        case "7":return "旷工";break;
        case "8":return "加班";break;
        default:return "其他";break;
    }
}

// updater: 张旭博，2020-11-27 10:50:24，时间相隔半点赋值
function selectFormInit(start,delay, obj ) {
    var data = []; //保存时间点
    var a = start.split(":")[0];
    var b = start.split(":")[1];
    if(a.substr(0,1) == "0"){
        a = a.substr(1,1);
    }
    for(var i=a;i<24;i++){
        if (i < 10) {
            data.push({"text": '0' + i + ':00'});
            data.push({"text": '0' + i + ':30'});
        } else {
            data.push({"text": i + ':00'});
            data.push({"text": i + ':30'});
        }
    }
    if(b === "30"){
        data.shift();
    }
    if(delay>0){
        for(var j=0;j<delay;j++){
            data.shift();
        }
    }
    var optionStr = '<option value="">---请选择---</option>'
    for (var i in data) {
        optionStr+= '<option value="'+data[i].text+'">'+data[i].text+'</option>'
    }
    obj.html(optionStr)
}

function chargeNull(data) {
    return data===null?'' :data
}

// 时间控件初始化
laydate.render({
    elem: '#countKey',
    type: 'month',
    min: startMonth,
    max: ssEnd,
    showBottom: false,
    change: function (value, date, endDate) { //监听日期被切换
        lay('#countKey').val(value);
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
        })
    }
});
laydate.render({elem: '#detailedKey',type: 'month',min: startMonth , max: ssEnd, showBottom: false,
    change: function (value, date, endDate) { //监听日期被切换
        lay('#detailedKey').val(value);
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
        })
    }});