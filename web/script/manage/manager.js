var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
$(function(){
    // 账号下拉列表点击其他地方收回
    $(document).click(function (e) {
        console.log(e.target)
        if(!$(".customSelect input").is(e.target)&&$(".customSelect input").has(e.target).length===0){
            $(".inputDrop").hide();
        }
    });
    $("body").on("click", '.customSelect .inputDrop option', function () {
        let data = $(this).html()
        $(this).parents(".customSelect").find("input").val(data)
        updateImportData($(this), 'leader')
    })
    $(".bounce").on('click', ".ty-btn",function() {
        var name = $(this).data('name');
        switch (name) {
            case "nextStep": // 下一步
                var list = [];
                var userList = $(".importNoSave").data("trueUserList");
                $(".importNoSave table tbody tr").each(function(){
                    var item = $(this).data("info");
                    list.push(item);
                });
                for (var b=0; b<userList.length;b++) {
                    var item = {
                        userName: userList[b].userName,
                        mobile: userList[b].mobile
                    }
                    list.push(item);
                }
                var sum = list.length;
                list = JSON.stringify(list);
                var json = {
                    usersList: list,
                    importSum: sum
                }
                saveImportList(json);
                break;
            case "clearUser": // 放弃
                clearUser()
                break;
            case "lastSaveSure": // 确定保存
                lastSave();
                break;
            case "judgeImport": // 批量导入尚未完成是否继续
                var flag = $(".unfinishedForm input:radio:checked").val()
                if (flag === '1') {
                    $(".importing").show();
                    $(".importNoSave").hide();
                    $(".main").hide();
                    bounce.cancel();
                    $(".importOpertion").siblings("button.ty-btn").hide()
                    var res = $("#importNotCompleted").data("userData");
                    getImportUserList(res);
                } else if (flag === '0') {
                    bounce.cancel();
                    turnCancel(1);
                } else {
                    layer.msg("请选择上次的批量导入尚未完成是否继续！");
                }
                break;
        }
    });
    //$(".managerDone").show().siblings().hide();
    if (sphdSocket.user.roleCode === 'super') {
        $(".ty-header .active span").html("中枢管控")
        let useInfo = auth.getOrg();
        useInfo.orgType === 1 ? $("#addBranches").show(): $("#addBranches").hide();
        getControlInfo()
        $(".mainCompany").show()
    } else {
        $(".ty-header .active span").html("高管管理")
        $("#chooseIsCompeteManger input:checkbox").prop("checked", false)
        $("#chooseIsCompeteManger").hide()
        // 获取高管列表
        $.ajax({
            url: '../org/getControlInfo.do',
            success: function (res) {
                var data = res.data
                var manageState = data.manageState
                if (manageState === 2) {
                    jumpTo('manager');
                    $("#manageBtn").hide()
                    $(".manager .ty-alert").html('请确定以下各项事务的负责人。')
                } else if (manageState === 3) {
                    $(".managerDone .backBtn").hide()
                    jumpTo('managerDone');
                }
            }
        })
    }

    // 高管列表按钮
    $("table").on("click", "[type='btn']", function () {
        let source = $(this).parents("table").data("source");
         var name = $(this).data("name")
         var managerId   = $(this).parents("tr").data("id");
         var roleCode    = $(this).parents("tr").data("code");
         var managerName = $(this).parents("tr").find(".manger_name").text();
        // 绑定跟数据
        $("#addManager").data("managerInfo", {
            source: source,
            managerId: managerId,
            roleCode: roleCode,
            managerName: managerName
        });
         switch (name) {
             // 添加（高管）
             case 'addUser':
                 if (source === 1) {
                     if (roleCode === "sale") {
                         layer.msg("<p>添加失败！\n系统暂不支持分支机构设立销售！</p>");
                         return false;
                     } else if (roleCode === "accounting") {
                         layer.msg("<p>添加失败！\n系统不支持分支机构设立会计！</p>");
                         return false;
                     } else if (roleCode === "finance") {
                         layer.msg("<p>温馨提示！\n分支机构不可设立基本账户！</p>");
                     }
                 }
                 // 获取职工
                 getSelectStaffUsers(roleCode, source)

                 // 会计不同显示
                 if (roleCode !== "accounting") {
                     $("#addManager .normalForm").show().siblings().hide()
                 } else {
                     $("#addManager .agent_avatar").show()
                     $(".agent_avatar input[type='radio']").eq(0).click()
                 }
                 // 初始化
                 $("#addManager").find(".bonceTitle").html("添加"+managerName);
                 $("#addManager").find("input:not(':radio'),select,textarea").val("");
                 // 显示弹窗
                 if (roleCode === "smallSuper") {
                     bounce.show($("#confirm"));
                 } else {
                     bounce.show($("#addManager"));

                 }
                 // 表单验证
                 setEveryTime(bounce, 'addManager')
                 $("#addManager").data("type", "add")
                 break;
             // 更换他人
             case 'changeCharge':
                 getSelectStaffUsers(roleCode, source)
                 bounce.show($("#addManager"))
                 $("#addManager input").val("")
                 $("#addManager select").val("")
                 $("#addManager").find(".bonceTitle").html("更换人员");
                 if (roleCode !== "accounting") {
                     $("#addManager .normalForm").show().siblings().hide()
                 } else {
                     var agentType = Number($(this).siblings("[data-name='changeAgentState']").data('agent'))
                     if (agentType === 1) {
                         $("#addManager .agentForm").show().siblings().hide()
                     } else {
                         $("#addManager .normalForm").show().siblings().hide()
                     }
                     $("#addManager").data("agentType", agentType)
                 }
                 // 表单验证
                 setEveryTime(bounce, 'addManager')
                 $("#addManager").data("type", "change")
                 break;
             // 历任负责人
             case 'chargeHistory':
                 bounce.show($("#chargeHistory"))
                 var manger_name = $(this).parents("tr").find(".manger_name").html()
                 getRoleChargeHistory(managerId, manger_name)
                 break;
             // 修改代理状态
             case 'changeAgentState':
                 getSelectStaffUsers(roleCode, source)
                 bounce.show($("#changeAgentState"))
                 var agentType = Number($(this).data('agent'))
                 if (agentType === 0) {
                     $("#changeAgentState .tips").html("当前贵公司会计业务不是客必盛代理<br>修改为由客必盛代理？")
                 } else {
                     $("#changeAgentState .tips").html("当前贵公司会计业务由客必盛代理<br>不再由客必盛代理？")
                 }
                 $("#changeAgentState .agent_avatar").show().siblings().hide()
                 $("#changeAgentState").data("agentType", agentType)
                 $("#changeAgentState").data("managerId", managerId)
                 setEveryTime(bounce, 'changeAgentState')
                 break;
             // 暂不再设置
             case 'noSetting':
                 bounce_Fixed.show($("#noSmallSuper"))
                 $("#noSmallSuper").data("managerId", managerId)
                 break
         }
     })
    // 代理切换
    $("#addManager .agent_avatar").on("click", "input[type='radio']", function () {
        var to = $(this).attr("to")
        $("#addManager ."+to).show().siblings(".formContainer").hide()
    })
    $("body").on("click", '.thin-btn, .nodeBtn', function () {
        var name = $(this).data("fun");
        switch(name) {
            case 'updateName':
                let name = $("#branchName").html();
                $("#changeBranchName input").val(name);
                bounce.show($("#changeBranchName"));
                break;
            case 'updateNameLog':
                updateBranchNameLog(1, 20)
                break;
        }
    });
    $(".clearInputVal").on({
        "mousedown": function () {
            $(this).prev().val("");
        }
    });
});

// creator: 张旭博，2019-05-24 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 添加高管
            case 'addManager':
                var state = 0
                $("#addManager [name]:not(':radio'):visible").each(function () {
                    if ($(this).val() === '') {
                        state++
                    }
                })
                if(state === 0){
                    $("#addManagerBtn").prop("disabled",false)
                }else{
                    $("#addManagerBtn").prop("disabled",true)
                }
                break;
            // 更改代理状态
            case 'changeAgentState':
                var state = 0
                $("#changeAgentState [name]:not(':radio'):visible").each(function () {
                    if ($(this).val() === '') {
                        state++
                    }
                })
                if(state === 0){
                    $("#sureChangeAgentStateBtn").prop("disabled",false)
                }else{
                    $("#sureChangeAgentStateBtn").prop("disabled",true)
                }
                break;
            // 选择负责人
            case 'chooseManager':
                var $smallSuper = $(".manager .managerList tbody tr[data-code='smallSuper']")
                var hasSmallSuper = $smallSuper.length > 0 && $smallSuper.data("id") !== ''
                if (hasSmallSuper) {
                    $("#chooseIsCompeteManger").hide()
                    $("#completeMangerBtn").hide()
                } else {
                    $("#chooseIsCompeteManger").show()
                    $("#completeMangerBtn").show()
                    var state = 0
                    $(".managerList tbody tr").each(function () {
                        if ($(this).data("id") !== '') {
                            state ++
                        }
                    })

                    if ($("#chooseIsCompeteManger input:checkbox").prop("checked") && state > 0) {
                        $("#completeMangerBtn").prop("disabled", false)
                    } else {
                        $("#completeMangerBtn").prop("disabled", true)
                    }
                }
                break;
            case 'son_chooseManager':
                var $smallSuper = $(".branch_manager .branch_managerList tbody tr[data-code='smallSuper']")
                var hasSmallSuper = $smallSuper.length > 0 && $smallSuper.data("id") !== ''
                if (hasSmallSuper) {
                    $("#branch_chooseIsCompeteManger").hide()
                    $("#branch_completeMangerBtn").hide()
                } else {
                    $("#branch_chooseIsCompeteManger").show()
                    $("#branch_completeMangerBtn").show()
                    var state = 0
                    $(".branch_managerList tbody tr").each(function () {
                        if ($(this).data("id") !== '') {
                            state ++
                        }
                    })

                    if ($("#branch_chooseIsCompeteManger input:checkbox").prop("checked") && state > 0) {
                        $("#branch_completeMangerBtn").prop("disabled", false)
                    } else {
                        $("#branch_completeMangerBtn").prop("disabled", true)
                    }
                }
                break;
        }
    });
}

// creator: 张旭博，2020-09-28 15:47:01，获取高管列表
function getList(type) {
    let req = {};
    if (type === 1) {
        req = { sonOid: $("#branchName").data("sonOid")};
    }
     $.ajax({
         "url": "../sys/dynamicManageList.do",
         data: req,
         success: function (res) {
             var managerList = res.manageList
             var initState = res.initState
             if (managerList && managerList.length > 0) {

                 var isHasCharge = managerList[0].user !== null // 有没有设置最高负责人
                 var isSuper = sphdSocket.user.roleCode === 'super'

                 var str = ''
                 for (var i = 0; i < managerList.length; i++) {
                     var code = managerList[i].code;
                     var name = managerList[i].name;
                     var user = managerList[i].user;

                     var btnStr = ''
                     var tip = ''

                     if (code === 'general') tip = '<small class="ty-blue">注：负责分配除销售与财会外的权限</small>'

                     if (isSuper && isHasCharge) {
                         if (code === 'smallSuper') {
                             if (initState === '0') {
                                 btnStr +=  '<button type="btn" data-name="changeCharge" class="ty-btn ty-btn-blue ty-circle-2">换为他人</button> '+
                                            '<button type="btn" data-name="noSetting" class="ty-btn ty-btn-blue ty-circle-2">暂不再设置</button>';
                             } else {
                                  btnStr +=  '<button type="btn" data-name="changeCharge" class="ty-btn ty-btn-blue ty-circle-2">换为他人</button> '+
                                            '<button type="btn" data-name="chargeHistory" class="ty-btn ty-btn-blue ty-circle-2">历任负责人</button> '+
                                            '<button type="btn" data-name="noSetting" class="ty-btn ty-btn-blue ty-circle-2">暂不再设置</button>';
                             }
                         } else {
                             if (user && initState === '1') {
                                 btnStr += '<button type="btn" data-name="chargeHistory" class="ty-btn ty-btn-blue ty-circle-2">历任负责人</button>';
                             }
                         }
                     } else {
                         if (user) {
                             if (initState === '0') {
                                 btnStr =   '<button type="btn" data-name="changeCharge" class="ty-btn ty-btn-blue ty-circle-2">换为他人</button> ';
                             } else {
                                 btnStr =   '<button type="btn" data-name="changeCharge" class="ty-btn ty-btn-blue ty-circle-2">换为他人</button> '+
                                            '<button type="btn" data-name="chargeHistory" class="ty-btn ty-btn-blue ty-circle-2">历任负责人</button> ';
                             }
                             if (code === 'accounting') {
                                 btnStr += '<button type="btn" data-name="changeAgentState" class="ty-btn ty-btn-orange ty-circle-2" data-agent="'+user.agentType+'">修改代理状态</button>'
                             }

                         } else {
                             btnStr =   '<button type="btn" data-name="addUser" class="ty-btn ty-btn-green ty-circle-2">添加</button>'
                         }
                     }
                     // 以下四行是新加的需求 第一行全权负责人没有内容 2021-10-22
                     // if (code === 'smallSuper') {
                     //     btnStr = ''
                     //     user = null
                     // }
                     str += '<tr data-code="'+code+'" data-id="'+(user?user.userID:'')+'">' +
                                '<td><div class="manger_name">'+name+'</div>'+tip+'</td>'+
                                '<td>'+(user?user.userName:'')+'</td>'+
                                '<td>'+(user?user.mobile:'')+'</td>'+
                                '<td>'+(user?moment(user.updateDate).format("YYYY-MM-DD HH:mm:ss"):'')+'</td>'+
                                '<td>'+
                                    btnStr+
                                '</td>'+
                            '</tr>'
                 }
             }
             if (type === 1) {
                 $(".branch_managerList tbody").html(str);
             } else {
                 $(".managerList tbody").html(str);
             }
         }
     })
 }

// creator: 张旭博，2020-09-27 11:48:59，获取换为他人 可选人员列表接口
function getSelectStaffUsers(roleCode, source) {
    let param = {
        roleCode: roleCode
    };
    if (source === 1) {
        param.sonOid = $("#branchName").data("sonOid");
    }
    $.ajax({
        url: '/sys/getSelectStaffUsers.do',
        data: param,
        success: function (res) {
            var roleList = res.data
            var str = '<option value="">-----请选择-----</option>'
            if (roleList.length === 0) {
                str = '<option value="">-----暂无在册职工-----</option>'
            } else {
                for (var i in roleList) {
                    if (!(roleCode === 'smallSuper' && roleList[i].roleCode === 'super')) {
                        str += '<option value="'+roleList[i].userID+'">'+roleList[i].userName + '-' + roleList[i].mobile +'</option>'
                    }
                }
            }
            $(".selectRole").html(str)
        }
    })
}

//--------------------------- 系统启动流程 -------------------------------//

// creator: 张旭博，2020-12-11 15:03:06，获取主页面数据
function getControlInfo() {
    $.ajax({
        url: '../org/getControlInfo.do',
        success: function (res) {
            var data = res.data
            let sons = data.sonOrgList, html = ``;
            var sysInfo = {
                orgName: data.orgName, // 机构名
                today: moment(data.today).format("YYYY年MM月DD日 dddd"), // 今日时间
                week: data.week, // 星期
                superMobile: data.superMobile, // 最高权限手机号
                createDate: (data.createDate?moment(data.createDate).format("YYYY年M月DD日"): '2017年01月01日'), // 机构创建时间 成立时间
                address: data.address || '--', // 地址
                userNumber: data.userNumber, // 在职员工数量（不含董事长）
                smallUserName: data.smallUserName || '--' // 全权负责人名
            }

            // 填充页面内容
            for (var key in sysInfo) {
                $(".mainCompany .main_" + key).html(sysInfo[key])
            }

            // 核心人物管控 - 按钮(3种向接口）
            var manageState = data.manageState
            $(".mainCompany").data("manageState", manageState)
            $(".mainCompany .panel-box:gt(2)").remove();
            if (sons && sons.length > 0) {
                for (let i =0;i<sons.length;i++) {
                    html+= `
                     <div class="panel-box">
                         <div class="ty-alert">
                             <div class="ty-color-red main_item item_name">分支机构  ${sons[i].sonName}</div>
                             <div class="main_item">全权负责人  <span class="main-color-black">--</span></div>
                         </div>
                         <div class="ty-alert">
                             <div class="main_item">地址  <span class="main-color-black">${sons[i].sonAddress || '--'}</span></div>
                             <div class="main_item">职工总人数  <span class="main-color-black">${sons[i].sonUserNumber}</span></div>
                             <div class="text-right btn-group">
                             <span class="hd">${JSON.stringify(sons[i])}</span>
                                 <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-2" onclick="branchDetail($(this))">分支机构管控</button>
                             </div>
                         </div>
                     </div>
                    `;
                }
            }
            $(".mainCompany .page-content-avatar").append(html);
        }
    })
}

// creator: 张旭博，2020-11-25 08:34:01，核心人物管控 - 按钮(3种向接口）
function coreControl() {
    $(".step1").show().siblings().hide()

    var state = $(".mainCompany").data("manageState")
    switch (state) {
        case 0:
            // 0 正式启用前，且没有设置临时管理员
            jumpTo('confirmHandler', 'noSetManager')
            break;
        case 1:
            // 1- 正式启用前， 设置了临时管理员，但未导入完成 跳转至查看临时管理员页面
            jumpTo('confirmHandler', 'hasSetManager')
            break;
        case 2:
            // 2- 正式启用前， 导入完成 进入高管管理页面 或者 启用前
            jumpTo('manager')
            break;
        case 3:
            // 2- 正式启用后
            jumpTo('managerDone')
            break;
        case 4:
            // 4- 批量导入过程中
            $(".part_own").data("storage",{"source": 2})
            jumpTo('handler', 'own')
            getPresentUsers()
            break
    }
}

// creator: 张旭博，2020-12-03 09:35:56，确定导入的操作者
function confirmImportManager() {
    var radio = $(".part_noSetManager input:radio:checked").val()
    if (typeof (radio) === 'undefined') {
        layer.msg("请选择导入的操作者！")
        return false
    }
    if (radio === '0') {
        jumpTo('handler', 'needHandler')
    } else {
        // 清除批量导入内容
        $.ajax({
            url: '../org/deleteTemporaryAdmin.do',
            success: function (res) {
                var data = res.data
                if (data === 1) {
                    $(".importing").hide();$(".importNoSave").hide();$(".main").show();getPresentUsers()
                    $(".part_own").data("storage",{"type": "add","source": 2})
                    jumpTo('handler', 'own')
                } else {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("删除原管理员失败！");
                }
            }
        })
    }
}
// creator: 张旭博，2020-12-03 10:00:43，启用前 添加 临时管理员接口
function addTemporaryAdmin() {
    var userName = $(".part_needHandler input[name='userName']").val()
    var mobile = $(".part_needHandler input[name='mobile']").val()
    if (userName === '') {
        $("#errorTip .tipWord").html('请输入姓名！')
        bounce_Fixed.show($("#errorTip"))
        return false
    }
    if (!testMobile(mobile)) {
        bounce_Fixed.show($("#tip"))
        return false
    }
    var data = {
        mobile: mobile, // 手机号
        userName: userName // 姓名
    }
    console.log(data)
    $.ajax({
        url: '../org/addTemporaryAdmin.do',
        data: data,
        success: function (res) {
            var data = res.data
            if (data === 1) {
                $("#errorTip .tipWord").html('系统将向该职工发送短信，以告知其开始导入。')
                bounce_Fixed.show($("#errorTip"))
                jumpTo('confirmHandler', 'hasSetManager')
            } else {
                bounce_Fixed.show($("#tip"))
            }
        }
    })
}

// creator: 张旭博，2020-12-03 11:51:00，查看当前临时管理员接口
function getTemporaryAdmin(type) {
    let req = {};
    if (type === 1) {
        let sonInfo = JSON.parse($("#branchName").siblings(".hd").html());
        req = { sonOid: sonInfo.sonOid};
    }
    $.ajax({
        url: '../org/getTemporaryAdmin.do',
        data: req,
        success: function (res) {
            var data = res.data
            var user = data.user
            if (user) {
                if (type === 1) {
                    $(".branchTemporary").html(user.userName + ' &nbsp;'+ user.mobile);
                } else {
                    $(".part_hasSetManager span.userName").html(user.userName)
                    $(".part_hasSetManager span.mobile").html(user.mobile)
                    $(".part_hasSetManager").data('manageId', user.userID)
                }
            }
        }
    })
}

// creator: 张旭博，2020-12-03 14:27:37，修改临时管理员 - 按钮
function changeTemporaryAdmin() {
    bounce.show($("#changeTemporaryAdmin_choose"))
    $("#changeTemporaryAdmin_choose input:radio").prop("checked", false)
}

// creator: 张旭博，2020-12-03 14:29:23，修改临时管理员 - 下一步
function chooseTemporaryAdmin() {
    var radio = $("#changeTemporaryAdmin_choose input:radio:checked").val()
    if (typeof (radio) === 'undefined') {
        layer.msg("请选择导入的操作者！")
        return false
    }
    if (radio === '0') {
        bounce.show($("#changeTemporaryAdmin_input"))
        $("#changeTemporaryAdmin_input input").val("")
    } else {
        bounce.cancel()
        $.ajax({
            url: '../org/deleteTemporaryAdmin.do',
            success: function (res) {
                var data = res.data
                if (data === 1) {
                    $(".importing").hide();$(".importNoSave").hide();$(".main").show();$(".part_own").data("storage",{"type": "edit","source": 2})
                    jumpTo('handler', 'own')
                    getPresentUsers()
                } else {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("删除原管理员失败！");
                }
            }
        })
    }
}

// creator: 张旭博，2020-12-03 14:31:20，修改临时管理员 - 输入新的管理员 - 确定
function inputTemporaryAdmin() {
    var userName = $("#changeTemporaryAdmin_input input[name='userName']").val()
    var mobile = $("#changeTemporaryAdmin_input input[name='mobile']").val()
    var userId = $(".part_hasSetManager").data('manageId')
    if (userName === '') {
        $("#errorTip .tipWord").html('请输入姓名！')
        bounce_Fixed.show($("#errorTip"))
        return false
    }
    if (!testMobile(mobile)) {
        bounce_Fixed.show($("#tip"))
        return false
    }
    var data = {
        userId: userId, // 原临时管理员
        mobile: mobile, // 手机号
        userName: userName // 姓名
    }
    $.ajax({
        url: '../org/updateTemporaryAdmin.do',
        data: data,
        success: function (data) {
            if (data === 1) {
                bounce.cancel()
                $("#errorTip .tipWord").html('系统将向该职工发送短信，以告知其开始导入。')
                bounce_Fixed.show($("#errorTip"))
                jumpTo('confirmHandler', 'hasSetManager')
            } else {
                bounce_Fixed.show($("#tip"))
            }
        }
    })
}

// creator: 张旭博，2020-12-15 09:12:14，确定负责人 - 下一步
function completeManger(type) {
    var $smallSuper = $(".manager .managerList tbody tr[data-code='smallSuper']")
    var hasSmallSuper = $smallSuper.length > 0 && $smallSuper.data("id") !== ''
    if (hasSmallSuper) {
        $("#bounce_tip .bonceCon").html("确定后，该全权负责人将收到需选择高管的提示短信。")
    } else {
        $("#bounce_tip .bonceCon").html("<p>确定后：</p><p>1、各位负责人将收到需设置权限的提示短信。</p><p>2、其他职工将收到本公司开始使用系统的提示短信。</p>")
    }
    $("#bounce_tip").data("source", type);
    bounce.show($("#bounce_tip"))
}

// creator: 张旭博，2020-12-15 09:12:14，确定负责人 - 下一步 - 弹窗 - 确认
function sureCompleteManager() {
    // let type = $("#bounce_tip").data("source");
    // let param = {};
    // if (type === 1) {
    //     param = {"sonOid": $("#branchName").data("sonOid")};
    // }
    // $.ajax({
    //     url: '../org/confirmOrg.do',
    //     data: param,
    //     success: function (res) {
    //         bounce.cancel()
    //         if(res === 1) {
    //             if (type === 1) {
    //                 jumpTo('mainCompany');
    //             } else {
    //                 window.location.href = '../sys/logout.do'
    //             }
    //         } else {
    //             layer.msg("操作失败")
    //         }
    //     }
    // })
    bounce.cancel()
    window.location.href = '../sys/logout.do'
}

// creator: 张旭博，2020-12-15 15:16:11，获取当前职工
function getPresentUsers(id) {
    let param = {};
    if (id && id !== "") {
        param.sonOid = id;
    }
    $.ajax({
        url: '../org/getPresentUsers.do',
        data: param,
        success: function (res) {
            var data = res.data
            var str = ''
            if (data) {
                for (var i = 0; i<data.length; i++) {
                    str += '<tr>' +
                        '<td>'+data[i].userName+'</td>' +
                        '<td>'+data[i].mobile+'</td>' +
                        '<td>'+chargeOrdinaryEmployees(data[i].ordinaryEmployees)+'</td>' +
                        '<td>'+handleNull(data[i].leaderName)+'</td>' +
                        '</tr>'
                }
                $(".main tbody").html(str)
                $(".onDutyNum").html(data.length)
            }
        }
    })
}

// creator: 张旭博，2020-11-25 08:39:06，跳转到...（哪一页的哪一部分）
function jumpTo(page, part, branch) {
    if (page) {
        $(".page." + page).show().siblings(".page").hide()
        switch (page) {
            case 'mainCompany':
                getControlInfo()
                break;
            case 'manager':
                getList();
                setEveryTime($(".manager"), "chooseManager")
                break;
            case 'managerDone':
                getList();
                break
            case 'branchesMain':
                break
        }
        if (part) {
            $(".page." + page + " .part_" + part).show().siblings(".part").hide()
            switch (part) {
                // 未设置临时管理员
                case 'noSetManager':
                    $(".part_noSetManager input:radio").prop("checked", false)
                    break
                // 已设置管理员
                case 'hasSetManager':
                    // 获取已设置的临时管理员
                    getTemporaryAdmin()
                    break;
                // 选择临时管理员
                case 'needHandler':
                    $(".part_needHandler input").val("")
                    break
                // 亲自操作
                case 'own':
                    break
                // 分支管控
                case 'addBranches':
                case 'sonNoSetManager':                // 未设置临时管理员
                    addBranches();
                    break
                case 'managerBranch':
                    break
            }
            if (branch) {
                $(".branch_" + branch).show().siblings().hide();
                switch (branch) {
                    // 已设置管理员
                    case 'sonHasSetManager':
                        // 获取已设置的临时管理员
                        getTemporaryAdmin(1)
                        break;
                    case 'manager':
                        getList(1);
                        setEveryTime($(".branch_manager"), "son_chooseManager")
                        break
                    case 'managerDone':
                        getList(1);
                        break;
                }
            }
        }
    }
}

//--------------------------- 高管操作方法 -------------------------------//

// updater: 张旭博，2020-09-28 14:11:14，添加管理者 - 确认
function sureAddManager() {
    var passiveUserId = $("#addManager .selectRole").val();
    var passiveUser = $("#addManager .selectRole option:selected").text();
    var passiveArr = passiveUser.split("-")
    var lastPassive = passiveArr.pop()
    var roleCode = $("#addManager").data("managerInfo").roleCode;
    var managerId = $("#addManager").data("managerInfo").managerId;

    var type = $("#addManager").data("type") // 判断新增还是修改
    let source = $("#addManager").data("managerInfo").source;

    if (type === 'add') {
        var url = "";
        var data = {}
        if (roleCode === "smallSuper") {
            url = "../sys/addOrgSmallSuper.do"
            data = {
                passiveUserId: passiveUserId  // 被换的人的id
            }
        } else if (roleCode === 'accounting') {
            var agentType = Number($("#addManager [name='isAgent']:checked").val())
            url = "../sys/addAccounting.do"
            data = {
                agentType: agentType,  // 会计代理状态 1-代理，0-不代理
            }
            if (agentType === 0) {
                data.passiveUserId = passiveUserId // 被换的人的id
                data.userName = passiveArr.join()
                data.phone = lastPassive
            } else {
                data.userName = $("#addManager [name='userName']").val()
                data.phone = $("#addManager [name='mobile']").val()
                if (!testMobile(data.phone)) {
                    layer.msg("请输入正确的手机号！")
                    return false
                }
            }
        } else {
            url = "../sys/addManage.do"
            data = {
                roleCode: roleCode,  // 管理者类型
                passiveUserId: passiveUserId  // 被换的人的id
            }
        }
        if (source === 1) {
            data.sonOid = $("#branchName").data("sonOid");
        }
        console.log('url', url)
        console.log('data', data)
        $.ajax({
            url: url,
            data: data,
            success: function (res) {
                var status = res.status; // status 1-成功 0-失败（参数传错了） 2 -登录人不是超管没资格新增 3-手机号已存在
                if (roleCode === "smallSuper") {
                    status = res.data.status;
                }
                if (status === 1) {
                    bounce.cancel();
                    layer.msg("新增成功");
                    getList(source);
                } else if (status === 2) {
                    if (roleCode === "smallSuper") {
                        bounce.show($("#mtTip"));
                        $("#mt_tip_ms").html("登录人不是董事长,操作失败");
                    } else {
                        bounce.show($("#mtTip"));
                        $("#mt_tip_ms").html("手机号已存在");
                    }
                } else if (status === 3) {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("手机号已存在");
                } else {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("新增失败！");
                }
            }
        })
    } else {
        var url = '../sys/replaceManages.do'
        var data = {
            manageId: managerId,
            passiveUserId: passiveUserId
        }
        if (roleCode === 'accounting') {
            var agentType = Number($("#addManager").data("agentType"))
            if (agentType === 1) {
                url = '../sys/editManage.do'
                delete data.passiveUserId
                data.userName = $("#addManager [name='userName']").val()
                data.phone = $("#addManager [name='mobile']").val()
                if (!testMobile(data.phone)) {
                    layer.msg("请输入正确的手机号！")
                    return false
                }
            }
        } else if (roleCode === 'smallSuper') {
            url = '../sys/updateOrgSmallSuper.do'
            data = {
                oldUserId: managerId,
                newUserId: passiveUserId
            }
        }
        if (source === 1) {
            data.sonOid = $("#branchName").data("sonOid");
        }
        $.ajax({
            url: url,
            data: data,
            success: function (res) {
                var data = res["data"]; // status 1-成功 0-失败（参数传错了） 2 -登录人不是超管没资格新增 3-手机号已存在
                if (roleCode === 'smallSuper') {
                    data = res.data.status
                }
                if (data === 1) {
                    bounce.cancel();
                    layer.msg("换为他人成功");
                    getList(source)
                }  else {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("换为他人失败！");
                }
            }
        })
    }
}

// creator: 张旭博，2018-08-20 11:10:55，暂不设置最高负责人 - 确定
function sureNoSmallSuper() {
    var userID  = $("#noSmallSuper").data("managerId");
    let source = $("#addManager").data("managerInfo").source;

    var data = {
        oldUserId: userID
    }
    if (source === 1) {
        data.sonOid = $("#branchName").data("sonOid");
    }
    $.ajax({
        url:"../sys/noSmallSuper.do" ,
        data: data ,
        success:function (res) { // status -0 失败，1-成功 2-手机号已存在，
            bounce.cancel();
            bounce_Fixed.cancel();
            var status = res.data.status ;
            if( status === 1 ){
                layer.msg("操作成功！")
                // 获取高管列表
                getList();
            } else{
                bounce.show($("#mtTip")) ;
                $("#mt_tip_ms").html("操作失败！");
            }
        }
    });
}

// creator: 张旭博，2020-09-28 18:59:41，获取历任负责人
function getRoleChargeHistory(manageId, manger_name) {
    $.ajax({
        url:"../sys/getRolePrincipalHistories.do" ,
        data:{ "manageId":manageId } ,
        success:function (res) {
            var data = res.data
            var manageInfo = data.manageInfo
            var orgName  = data.orgName
            var orgAddress = data.orgAddress || ''

            var rolePrincipalHistories = data.rolePrincipalHistories
            var str = ''
            for (var i in rolePrincipalHistories) {
                str += '<tr>' +
                    '<td>'+rolePrincipalHistories[i].newUserName+'</td>'+
                    '<td>'+rolePrincipalHistories[i].newUserMobile+'</td>'+
                    '<td>'+(rolePrincipalHistories[i].createName || '') + ' ' + formatTime(rolePrincipalHistories[i].createDate, true) +'</td>'+
                    '<td>'+(rolePrincipalHistories[i].updateName || '') + ' ' + formatTime(rolePrincipalHistories[i].updateDate, true)+'</td>'+
                    '</tr>'
            }
            $("#chargeHistory .orgName").html(orgName)
            $("#chargeHistory .address").html(orgAddress)
            $("#chargeHistory tbody").html(str)
            $("#chargeHistory .roleName").html(manger_name)
        }
    });
}

// creator: 张旭博，2020-09-29 09:13:59，修改代理状态 - 确认
function sureChangeAgentState() {
    var agentType  = Number($("#changeAgentState").data("agentType"));
    var managerId  = $("#changeAgentState").data("managerId");
    var isTipsShow = $("#changeAgentState .agent_avatar").is(":visible")
    if (isTipsShow) {
        if (agentType === 0) {
            $("#changeAgentState .agentForm").show().siblings().hide()
        } else {
            $("#changeAgentState .normalForm").show().siblings().hide()
        }
    } else {
        var data = {
            userId: managerId,
            agentType: 1^agentType // (0、1互换)
        }
        var passiveUser = $("#changeAgentState .selectRole option:selected").text();
        var passiveArr = passiveUser.split("-")
        var lastPassive = passiveArr.pop()
        var passiveUserId = $("#changeAgentState .selectRole").val();
        if ( agentType === 0) {
            data.userName = $("#changeAgentState [name='userName']").val()
            data.phone = $("#changeAgentState [name='mobile']").val()
            if (!testMobile(data.phone)) {
                layer.msg("请输入正确的手机号！")
                return false
            }
        } else {
            data.passiveUserId = passiveUserId // 被换的人的id
            data.userName = passiveArr.join()
            data.phone = lastPassive
        }
        console.log('data', data)
        $.ajax({
            url:"../sys/editAccountingAgentType.do" ,
            data: data,
            success:function (res) { // status -0 失败，1-成功 2-手机号已存在，
                bounce.cancel()
                var status = res["status"] ;
                if( status === 1 ){
                    layer.msg("操作成功")
                    getList()
                } else if (status === 2) {
                    $("#mtTip #mt_tip_ms").html("手机号已存在")
                    bounce.show($("#mtTip"))
                } else {
                    $("#mtTip #mt_tip_ms").html("操作失败")
                    bounce.show($("#mtTip"))
                }
            }
        });
    }
}

//--------------------------- 重写批量导入部分代码 -------------------------------//

// creator: 张旭博，2020-12-14 10:48:21，批量导入 - 上次的批量导入是否未完成？
function leadingStartManage(){
    let param = {};
    let storage = $(".part_own").data("storage")
    if (storage.source === 1) {param.sonOid = storage.sonOid;}
    $.ajax({
        url:"../userImport/unfinishedImportUser.do",
        data: param,
        success:function (data) {
            var userList = data.userImportList; // 上传数据
            if (userList.length > 0) {
                // 上次的批量导入尚未完成。
                $('#importNotCompleted').data("userData",data);
                bounce.show($('#importNotCompleted'));
                $('#importNotCompleted input:radio').prop("checked", false);
            } else {
                // 初始化上传弹窗
                $('#select_btn_1').val("");
                $('.userListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                bounce.show($('#leading'));
                // 初始化批量导入
                initLeading()
            }
        }
    });
}

// creator: 张旭博，2023-10-25 02:34:13，批量导入 - 上次未完成 - 是否继续？
function judgeImport() {
    let flag = $("#importNotCompleted input:radio:checked").val()
    if (flag === '1') {
        // 继续 上次的批量导入
        $(".importing").show();
        $(".importNoSave").hide();
        $(".main").hide();
        bounce.cancel();
        $(".importOpertion").siblings("button.ty-btn").hide()
        var res = $("#importNotCompleted").data("userData");
        getImportUserList(res);
    } else if (flag === '0') {
        // 放弃上次的导入
        bounce.cancel();
        giveUp('judge');
    } else {
        layer.msg("请选择上次的批量导入尚未完成是否继续！");
    }
}

// creator: 张旭博，2023-10-25 03:12:27， 初始化批量导入
function initLeading() {
    $("#sysUseUploadFile").html('')
    $('#sysUseUploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{
            module: '职工导入',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:10240,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onSelect:function (file) {
            // $("#leading .uploadify-queue").html('')
            $('#leading .uploadify-queue .uploadify-queue-item:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            var fileUid = data.fileUid
            let param =  {
                filePath: path
            };
            // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
            let storage = $(".part_own").data("storage")
            if (storage && storage.source === 1) {
                param.sonOid = storage.sonOid;
            }
            // 将上传的文件交给后台处理
            $.ajax({
                url: '../export/newImportUser.do',
                data: param,
                success: function (data) {
                    var status = data.status
                    $(".importNoSave table tbody").html("");
                    // 下面一句处理中枢管控自带的按钮消失
                    $(".importOpertion").siblings("button.ty-btn").hide()
                    if (status == '1') {
                        // 取消删除程序
                        cancelFileDel({type: 'fileUid', fileUid: fileUid})
                        var userList = data.trueUserList;
                        var falseList = data.falseUserList;
                        $(".importNoSave").data("trueUserList", userList);
                        $(".importNoSave table tbody").html("");
                        bounce.cancel();
                        if (falseList && falseList.length > 0) {
                            loading.close() ;
                            let html = '';
                            for (let item of falseList) {
                                let info = {
                                    userName: item.userName,
                                    mobile: item.mobile
                                }
                                html += `<tr data-info= ${JSON.stringify(info)}>
                                            <td>${handleNull(item.userName)}</td>
                                            <td>${handleNull(item.mobile)}</td>
                                            <td>
                                                <span class="link-blue" onclick="updateImport($(this), 'noSave')">修改</span>
                                                <span class="link-red" onclick="delImport($(this), 'noSave')">删除</span></td>
                                            </td>
                                        </tr>`
                            }
                            $(".importNoSave table tbody").html(html);
                            $(".importNoSave .initAll").html(data.importSum);
                            $(".importNoSave .initWrong").html(data.falseImportSum);
                            $(".importNoSave").show().siblings().hide();
                        } else {
                            let importList = userList.map(item => {
                                return {
                                    userName: item.userName,
                                    mobile: item.mobile
                                }
                            })
                            let json = {
                                usersList: JSON.stringify(importList),
                                importSum: data.importSum
                            }
                            saveImportList(json);
                        }
                    } else {
                        loading.close() ;
                        $('#select_btn_1').val("");
                        $('.userListUpload').remove();
                        $('#leading .fileFullName').html("尚未选择文件");
                        bounce_Fixed3.show($("#importantTip"));
                    }
                }
            })
        }
    });
}

// creator: 张旭博，2023-11-07 03:42:46， 浏览按钮 （触发选择文件按钮）
function chooseEmployeeFile() {
    $("#sysUseUploadFile .uploadify-button").click()
}

// creator: 张旭博，2023-11-07 03:49:19， 确定导入 （确定上传）
function sysUseImportOk() {
    if ($("#leading .uploadify-queue-item").length <= 0) {
        layer.msg("您需选择一个文件后才能“导入”！")
    } else {
        loading.open();
        $("#leading .uploadify-queue-item .uploadbtn").click();
    }
}

// creator: 张旭博，2023-11-07 04:39:17， 导入的数据有错误数据 - 处理完后下一步
function nextStep(type) {
    if (type === 'noSave') {
        var list = [];
        $(".importNoSave table tbody tr").each(function(){
            var item = $(this).data("info");
            list.push(item);
        });
        var sum = list.length;
        list = JSON.stringify(list);
        let param = {
            "usersList": list,
            "importSum": sum
        }
        // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            url: "../userImport/allImportUserEnter.do",
            data: param,
            success: function (data) {
                var noSaveSum = data.falseImportSum;
                if (noSaveSum > 0) {
                    $("#nextStep #noSaveMbSum").html(noSaveSum);
                    $("#nextStep .safeCondition").show();
                } else {
                    $("#nextStep .safeCondition").hide();
                }
                bounce.show($("#nextStep"));
            }
        });
    }
}

function sureNextStep() {
    var list = [];
    var userList = $(".importNoSave").data("trueUserList");
    $(".importNoSave table tbody tr").each(function(){
        var item = $(this).data("info");
        list.push(item);
    });
    for (var b=0; b<userList.length;b++) {
        var item = {
            userName: userList[b].userName,
            mobile: userList[b].mobile
        }
        list.push(item);
    }
    var sum = list.length;
    list = JSON.stringify(list);
    var json = {
        usersList: list,
        importSum: sum
    }
    saveImportList(json);
}

// creator: 张旭博，2023-11-08 10:09:54， 放弃(3处放弃）
function giveUp(type) {
    let param = {}, storage = $(".part_own").data("storage")

    if (storage.source === 1) {
        param.sonOid = storage.sonOid;
    }
    if (type === 'noSave') {
        // 检测出错误数据页面，还未正式保存
        $(".main").show().siblings().hide()
        getPresentUsers(param);
    } else {
        // 保存过放弃
        // type:  1.judge 一开始判断是否有未完成时的放弃(此种状态数据也保存过) 2.save 存入数据之后正常操作的放弃
        $.ajax({
            url: "../userImport/giveUpImportUser.do",
            data: param,
            success: function (data) {
                var state = data.status;
                if (state == '1') {
                    if (type === 'judge') {
                        $('#sysUseUploadFile').html('')
                        $('#leading .fileFullName').html("尚未选择文件");
                        initLeading()
                        bounce.show($('#leading'));
                    }
                    if (type === 'save') {
                        $(".main").show().siblings().hide()
                        getPresentUsers(param);
                    }
                }
            }
        })
    }
}

// creator: 张旭博，2023-11-07 03:46:08， 渲染上传到服务器的员工数据
function getImportUserList(data) {
    let staffUserList = data["userImportList"];
    let manageList = data["manageList"];
    let buttonState = data["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰
    $("#ok").prop("disabled", buttonState === 0)
    $(".importing .importSum").html(data.importSum);
    $(".importing .saveSum").html(data.tureImportSum);
    let str = '';
    if (staffUserList && staffUserList.length > 0) {
        for (let item of staffUserList) {
            let isP     = item.ordinaryEmployees // 是否普通员工
            let userID  = item.id // 用户id
            let leader  = handleNull(item.leader) // 直接上级id
            let leaderName  = handleNull(item.leaderName) // 直接上级name
            let managerCode = item.managerCode // 所属高管
            let subordinates = [{name: '有', value: 0, selected: false}, {name: '无', value: 1, selected: false}]
            subordinates[subordinates.findIndex(it => it.value === isP)].selected = true
            let workSpecials = []
            for (let it of manageList) {
                workSpecials.push({
                    userID: it.userID,
                    name: changeWorkFuc(it.roleCode),
                    value: it.roleCode,
                    selected: managerCode === it.roleCode
                })
            }

            let leaders = [{name: leaderName, value: leader, selected: true}] // 点击的时候再获取所有的上级
            let subordinateOption = getOptionStr(subordinates)
            let leaderOption = getOptionStr(leaders)
            let workSpecialOption = getOptionStr(workSpecials);
            str += `<tr data-id="${userID}">
                        <td>${item.userName}</td>
                        <td>${item.mobile}</td>
                        <td><select class="kj-select" onchange="updateImportData($(this), 'hasUnderling')">${subordinateOption}</select></td>
                        <td><div class="customSelect"><input type="text" class="kj-input" value="${leaders[0].name}" oninput="getLeadersOption($(this))" onclick="getLeadersOption($(this), ${userID})"><div class="inputDrop"></div></div></td>
                        <td><select class="kj-select" onchange="updateImportData($(this), 'workSpecial')">${workSpecialOption}</td>
                        <td>
                        <span class="link-blue" onclick="updateImport($(this), 'save')">修改</span>
                        <span class="link-red" onclick="delImport($(this), 'save')">删除</span></td>
                    </tr>`
        }
    }
    $(".importing tbody").html(str);
}

// creator: 张旭博，2023-11-07 03:51:09， 员工数据操作后的保存
function saveImportList(userData) {
    // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {userData.sonOid = storage.sonOid;}
    $.ajax({
        url: "../userImport/saveImportUser.do",
        data: userData,
        success: function (data) {
            $(".importing").show();
            $(".importNoSave").hide();
            $(".main").hide();
            bounce.cancel();
            getImportUserList(data);
        }
    });
}

// creator: 张旭博，2020-12-14 14:18:39，最后的确定保存
function lastSave(){
    let param = {};
    let storage = $(".part_own").data("storage")
    if (storage.source === 1) {param.sonOid = storage.sonOid;}
    $.ajax({
        url: "../userImport/completeImportUser.do",
        data: param,
        success: function (data) {
            var state = data.status;
            if (state == 1) {
                bounce.cancel();
                if (storage.source === 1){
                    jumpTo('branchesMain', 'managerBranch', 'manager')
                }else {
                    jumpTo('manager')
                }
            } else {
                layer.msg("保存失败！");
            }
        }
    })
}

// creator: 张旭博，2023-11-09 10:19:16， 修改导入的员工
function updateImport(selector, type) {
    const id = selector.parents("tr").data("id");
    const userName = selector.parents("tr").find("td").eq(0).html();
    const mobile = selector.parents("tr").find("td").eq(1).html();
    $("#updateUser").data({
        obj: selector,
        type: type,
        id: id,
        old: {
            userName: userName,
            mobile: mobile
        }
    });
    $("#updateUser .userName").val(userName);
    $("#updateUser .userPhone").val(mobile);
    $("#importUpdateUser").prop("disabled",true);
    bounce.show($("#updateUser"));

    bounce.everyTime('0.5s','importUpdate',function(){
        let thisMobile = $("#updateUser .userPhone").val();
        let thisName = $("#updateUser .userName").val();
        let isChange = thisMobile !== "" && thisName !== "" && (thisMobile !== mobile || thisName !== userName)
        $("#importUpdateUser").prop("disabled", !isChange);
    });
}

// creator: 张旭博，2023-11-09 10:19:05， 删除导入的员工
function delImport(selector, type) {
    bounce.show($("#delUser"));
    $("#delUser .sureBtn").unbind().on("click", function (){
        bounce.cancel();
        let trObj = selector.parents("tr")
        if (type === 'noSave'){
            trObj.remove();
            const len = $(".importNoSave tbody tr").length;
            $(".importNoSave .initWrong").html(len);
        }
        if (type === 'save') {
            let param = { id: trObj.data("id") };
            let storage = $(".part_own").data("storage")
            if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
            $.ajax({
                url: $.webRoot + "/userImport/deleteImportUser.do",
                data: param,
                success: function (res) {
                    let status = res.status;
                    if (status === 1) {
                        resetImportUser();
                    } else {
                        layer.msg("删除失败！");
                    }
                }
            })
        }
    })
}

// creator: 李玉婷，2020-08-27 09:58:54，修改职工信息
function updateUserInfo() {
    const type = $("#updateUser").data("type")
    const thisMobile = $("#updateUser .userPhone").val();
    const thisName = $("#updateUser .userName").val();
    const old = $("#updateUser").data("old") // 当前职工的当前姓名和手机号

    if (!testMobile(thisMobile)) {
        const str = '<p>您录入的手机号有误。</p><p>请确认！</p>';
        $("#iknowTip .iknowWord").html(str);
        bounce_Fixed3.show($("#iknowTip"))
    } else {
        let same = 0;
        let name = '';
        if (type === 'noSave') {
            $(".importNoSave table tbody tr").each(function () {
                var info = $(this).data("info");
                if (thisMobile === info.mobile && info.mobile !== old.mobile){
                    same++;
                    name = info.userName;
                }
            });
        }
        if (type === 'save') {
            $(".importing table tbody tr").each(function () {
                const trMobile = $(this).find("td").eq(1).html();
                const trName = $(this).find("td").eq(0).html();
                if (thisMobile === trMobile && trMobile !== old.mobile){
                    same++;
                    name = trName
                }
            });
        }
        if(same > 0) {
            const str = `<p>您录入的手机号与本次导入的${name}手机号相同。</p><p>请确认！</p>`;
            $("#iknowTip .iknowWord").html(str);
            bounce_Fixed3.show($("#iknowTip"));
        }else {
            $("#checkTip").data("name", "updateImportStuff");
            mobileCheckImport(thisMobile, thisName);
        }
    }
}

// creator: 李玉婷，2020-05-28 11:59:40，查重后操作
function mobileCheckImport(phone, name) {
    mobileCheckRepeatImport(phone, name).then((data) => {
        var status = data["status"]; // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同 4-与冻结一致
        if (status === 1) {
            updateImportUserSure();
        } else if (status === 2 || status === 3) {
            if (status === 2) {
                str = `您录入的手机号与公司已离职者${data.name}手机号相同。`
            } else if (status === 3) {
                str = `您录入的手机号与公司${data.name}曾用过的手机号相同。`
            }
            bounce_Fixed3.show($("#bounceFixed3_tip"))
            $("#bounceFixed3_tip .msg").html(str)
            $("#bounceFixed3_tip .sureBtn").unbind().on("click", function () {
                updateImportUserSure()
            })
        }else if(status === 0 || status === 4) {
            var str =
                '<p>您录入的手机号与公司'+ data.name +'手机号相同。</p>'+
                '<p>请确认！</p>';
            bounce_Fixed3.show($("#bounceFixed3_iknow"))
            $("#bounceFixed3_tip .msg").html(str)
        }
    })
}

// creator: 李玉婷，2020-10-21 11:21:59，修改职工信息确定
function updateImportUserSure(){
    var obj = $("#updateUser").data("obj");
    var type = $("#updateUser").data("type");
    var trObj = obj.parents("tr");
    var thisMobile = $("#updateUser .userPhone").val();
    var thisName = $("#updateUser .userName").val();
    if (type === 'noSave') {
        trObj.find("td").eq(0).html(thisName);
        trObj.find("td").eq(1).html(thisMobile);
        trObj.data("info", {
            mobile: thisMobile,
            userName: thisName
        });
        bounce.cancel();
        bounce_Fixed3.cancel();
    } else if (type === 'save') {
        let param = {
            id: trObj.data("id"),
            mobile: thisMobile,
            userName: thisName
        };
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            url: $.webRoot + "/userImport/updateImportUserMobile.do",
            data: param,
            success: function (res) {
                var status = res["status"];
                if (status == 1) {
                    bounce.cancel();
                    bounce_Fixed3.cancel();
                    trObj.find("td").eq(0).html(thisName);
                    trObj.find("td").eq(1).html(thisMobile);
                } else if (status == -1) {
                    layer.msg("和本次导入的其他手机号重复！");
                } else {
                    layer.msg("修改失败！");
                }
            }
        })
    }
}

// creator: 李玉婷，2020-11-22 10:25:36，输入手机号 查重接口
function mobileCheckRepeatImport(phone, name){
    return new Promise( (resolve, reject) => {
        var param = {
            "mobile": phone,
            "userName": name
        };
        // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            url: "../userImport/updateFalseUserEnter.do",
            data: param,
            async: false,
            beforeSend: function () {
                loading.open()
            },
            success: function (res) {
                resolve(res);
            },
            error: function () {
                reject('')
            },
            complete: function () {
                loading.close()
            }
        })
    });
}

// creator: 李玉婷，2020-11-02 10:15:50，工作特点转换
function changeWorkFuc(str) {
    var charact = '';
    switch (str) {
        case "general":
            charact = "不含销售工作与财会工作";
            break;
        case "finance":
            charact = "包含财务工作";
            break;
        case "sale":
            charact = "含有销售工作";
            break;
        case "accounting":
            charact = "包含会计工作";
            break;
    }
    return charact
}

// creator: 张旭博，2023-11-03 11:53:18， 获取直接上级选择内容（特殊下拉框，可搜索）
function getLeadersOption(selector, userID) {
    $(".inputDrop").hide();
    selector.siblings(".inputDrop").show()
    let value = selector.val()
    let param = { id: userID };
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) { param.sonOid = storage.sonOid; }
    if (userID) {
        $.ajax({
            url: '../userImport/selectOptionalLeader.do',
            data: param
        }).then(res => {
            let userList = res;
            let leaders = userList.map(item => {
                return {
                    name: item.userName + ' - ' + item.mobile,
                    value: item.id,
                    status: item.status
                }
            })
            let optionStr = getOptionStr(leaders)
            selector.siblings(".inputDrop").html(optionStr);
            selector.data('data', leaders);
        })
    } else {
        let data = selector.data('data')
        let matchData = data.filter(item => item.name.indexOf(value) !== -1)
        let optionStr = getOptionStr(matchData)
        selector.siblings(".inputDrop").html(optionStr);
    }

}

// creator: 张旭博，2023-11-03 11:52:59， 下一步（准备保存）
function confirmOrgTip() {
    var sum = $(".importing table tbody tr").length;
    var importNum = $(".importing .importSum").html();
    $("#lastSave #saveSum").html(importNum);
    $("#lastSave #saveAble").html(sum);
    bounce.show($("#lastSave"));
}

// creator: 张旭博，2023-11-03 11:52:42， 更新数据
function updateImportData(selector, type) {
    let userId = selector.parents("tr").data("id")
    let thisValue = Number(selector.val())

    let data = {
        userId: userId
    }
    switch (type) {
        case 'hasUnderling':
            // 是否有下属
            data.ordinaryEmployees = thisValue
            break
        case 'leader':
            let allLeaderData = selector.parents("tr").find("input").data("data")
            let thisData = allLeaderData[allLeaderData.findIndex(item => item.value === thisValue)]
            data.leaderSorce = thisData.status
            data.leaderId = thisValue
            break
        case 'workSpecial':
            // 工作特点
            data.manageCode = selector.val()
    }
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {data.sonOid = storage.sonOid;}
    $.ajax({
        url: "../userImport/updateImportUser.do",
        data: data,
        success: function (res) {
            let status = res.status
            let buttonState = res.buttonState; // 确定按钮状态1- 变亮 0- 置灰
            $("#ok").prop("disabled", buttonState !== 1)
            if (status === 1) { // 设置没问题
                if (type === 'hasUnderling') {
                    resetImportUser();
                }
            } else if (status === 2) {
                layer.msg("下级有员工，不能变成普通员工,设置失败！");
            } else {
                layer.msg("设置失败！");
            }
            loading.close();
        },
        complete:function(){  }
    })
}

// creator: 李玉婷，2020-12-11 11:12:02，删除、是否有下属更改对直接上级影响
function resetImportUser() {
    // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
    let param = {};
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
    $.ajax({
        url:"../userImport/unfinishedImportUser.do",
        data: param,
        success:function (data) {
            getImportUserList(data);
        }
    });
}

// creator: 李玉婷，2020-08-27 09:49:20，修改 - 一键清空
function clearTxt(obj) {
    obj.prev().val("");
}

// creator: 张旭博，2023-11-09 11:42:29， 根据数据获取下拉框html
function getOptionStr(data) {
    let str = ''
    for (let item of data) {
        let str1 = JSON.stringify(item)
        str += `<option value='${item.value}' data= ${str1} ${item.selected?'selected': ''}>${item.name}</option>`
    }
    return str
}

function chargeOrdinaryEmployees(data) {
    console.log(data)
    return data == 1?'否':'是'
}

// creator: 李玉婷，2022-07-18 8:17:25，增加分支机构
function addBranches() {
    $(".part_addBranches").data("type", "add")
    $(".part_addBranches input:not([type=radio])").val("");
    $(".part_addBranches select").val("");
    $(".part_addBranches input:radio").prop("checked",false);
    $(".part_addBranches .lenTip").html("0/6");
    $(".part_addBranches #branchAddress").prop("disabled", false);
    $(".part_addBranches #branchShort").prop("disabled", false);
    $.ajax({
        url: '../sys/getSelectStaffUsers.do',
        success: function (res) {
            var data = res.data
            let option = ` <option value="">请选择</option>`;
            for (let i=0;i<data.length;i++) {
                option += `<option value="${data[i].userID}">${data[i].userName} ${data[i].mobile}</option>`;
            }
            $(".selectBySys").html(option);
        }
    })
}
// creator: 李玉婷，2022-06-13 18:47:29，增加分支机构-下一步
function addBranchesNextStep() {
    let radio = $(".manager_addBranches input:radio:checked").val()
    let branchShort = $("#branchShort").val();
    if (branchShort === '') {
        layer.msg("请录入分支机构的简称！");
        return false
    } else if (typeof (radio) === 'undefined') {
        layer.msg("请选择导入的操作者！")
        return false
    }
    if (radio === '0') {
        if ($(".selectBySys").val() !== '') {
            bounce.show($("#addBranchTip"));
        }
    } else if (radio === '1') {
        if (!testMobile($(".tempMobile").val())) {
            layer.msg("<p>系统无法向您录入的手机号发送提示短信。</p><p>请检查确认，或换个其他号码！</p>")
            return false
        }
        if ($(".tempUserName").val() !== '' && $(".tempMobile").val() !== '') {
            bounce.show($("#addBranchTip"));
        }
    } else {
        addBranchesSure();
    }
}
// creator: 李玉婷，2022-07-29 08:28:28，增加分支机构-下一步 确定
function addBranchesSure() {
    let info = ``;
    let type = $(".part_addBranches").data("type");
    let url = `../sonOrg/addSonOrg.do`;
    let radio = $(".part_addBranches input:radio:checked").val()
    let reqData = {
        name: $("#branchShort").val(),
        address: $("#branchAddress").val()
    };
    if (radio === '0') {
        reqData.passiveUserId = $(".selectBySys").val()
    } if (radio === '1') {
        reqData.userName = $(".tempUserName").val()
        reqData.mobile = $(".tempMobile").val()
    }
    if (type === 'edit') {
        info = JSON.parse($("#branchName").siblings(".hd").html());
        url = `../sonOrg/updateSonOrg.do`;
        reqData.sonOid = info.sonOid;
    }
    $.ajax({
        url: url,
        data:reqData ,
        success: function (res) {
            bounce.cancel();
            if (res.data.state === 1 || res.data === 1) {
                if (radio === '0' || radio === '1') {
                    if (type === 'add') {
                        jumpTo('mainCompany');
                    } else {
                        jumpTo('branchesMain', 'managerBranch', 'sonHasSetManager')
                    }
                } else {
                    let type = $(".part_addBranches").data("type");
                    let storage = {"type": type,"source": 1};
                    if (type === 'add'){
                        storage.sonOid = res.data.oid;
                        $("#branchAddressCon").html($("#branchAddress").val());
                        $("#branchName").html($("#branchShort").val()).data("sonOid", res.data.oid);
                    } else {
                        storage.sonOid = info.sonOid;
                    }
                    $(".importing").hide();$(".importNoSave").hide();$(".main").show();
                    $(".part_own").data("storage", storage)
                    jumpTo('handler', 'own')
                    getControlInfo();
                    getPresentUsers(storage.sonOid);
                }
            } else if (res.data.state === 2) {
                $("#knowTip .knowWord").html("<p>您录入的手机号现在或过去是公司职工。</p><p>换个其他的号码吧！</p>");
                bounce_Fixed3.show($("#knowTip"));
            }
        }
    })
}
// creator: 李玉婷，2022-08-01 10:38:11，点击分支机构后，显示不同的页面
function branchDetail(obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    $("#branchAddressCon").html(info.sonAddress);
    $("#branchName").html(info.sonName).data("sonOid", info.sonOid);
    $("#branchName").siblings(".hd").html(JSON.stringify(info));
    if (info.sonManageState === 1) {//1- 正式启用前， 设置了临时管理员
        jumpTo('branchesMain', 'managerBranch', 'sonHasSetManager')
    } else if (info.sonManageState === 2) {
        jumpTo('branchesMain', 'managerBranch', 'manager')
    } else if (info.sonManageState === 3) {
        jumpTo('branchesMain', 'managerBranch', 'managerDone')
    } else if (info.sonManageState === 4 || info.sonManageState === 0) {
        // 4- 批量导入过程中
        $(".importing").hide();$(".importNoSave").hide();$(".main").show();
        $(".part_own").data("storage",{"type": 'add',"source": 1, "sonOid": info.sonOid})
        jumpTo('handler', 'own')
        getPresentUsers(info.sonOid);
    }
}
// creator: 李玉婷，2022-08-02 08:31:55，修改分支机构de临时管理员
function changeTemporaryBranch (){
    jumpTo('branchesMain','addBranches');
    let info = JSON.parse($("#branchName").siblings(".hd").html());
    $(".part_addBranches").data("type", "edit")
    $(".part_addBranches input:not([type=radio])").val("");
    $(".part_addBranches select").val("");
    $(".part_addBranches input:radio").prop("checked",false);
    $(".part_addBranches .lenTip").html(info.sonName.length + "/6");
    $(".part_addBranches #branchAddress").val(info.sonAddress).prop("disabled", true);
    $(".part_addBranches #branchShort").val(info.sonName).prop("disabled", true);
}
// creator: 李玉婷，2022-08-01 10:38:11，修改子机构简称
function sureChangeBranchName(){
    let val = $("#changeBranchName input").val();
    if (val !== ""){
        let data = {
            "name": $("#changeBranchName input").val(),
            "sonOid": $("#branchName").data("sonOid")
        }
        $.ajax({
            url: "../site/updateOrganizationInfo.do",
            data: data,
            success: function (res) {
                bounce.cancel();
                $("#branchName").html($("#changeBranchName input").val());
            }
        })
    }
}
// creator: 李玉婷，2022-08-01 08:18:23，修改子机构简称历史记录
function updateBranchNameLog(currPage  , pageSize){
    $.ajax({
        "url": '../site/organizationInfoEditHistories.do',
        "data": {
            "name": $("#branchName").html(),
            "sonOid": $("#branchName").data("sonOid"),
            "currentPageNo":currPage,
            "pageSize":pageSize
        } ,
        success:function(res) {
            let data = res.data;
            let list = data.organizationHistoryList || [];
            let curStaStr = ``;
            var cur = data.pageInfo["currentPageNo"];
            var totalPage = data.pageInfo["totalPage"];
            bounce.show($("#branchNameEditLog"));
            if(data.number >0){
                let str = ``;
                $("#ye_log").show();
                setPage( $("#ye_log") , cur ,  totalPage , "branchNameEditLog" );
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    str += `<tr>
                             <td>${item.dataState}</td>
                             <td>${handleNull(item.name)} </td>
                             <td>${handleNull(item.updateName)} ${new Date(item.updateTime).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                $("#branchNameEditLog tbody").children(":gt(0)").remove();
                $("#branchNameEditLog tbody").append(str);
                let n = data.number * 1 -1;
                curStaStr = `<p>分支机构的当前简称为${data.name},当前资料为第${n}次修改后的结果。</p><p>修改人：${handleNull(data.updateName) === ""? "系统": handleNull(data.updateName)} ${new Date(data.updateTime).format("yyyy-MM-dd hh:mm:ss")}</p> `
                $("#branchNameEditLog table").show();
            }else{
                $("#ye_log").hide();
                $("#branchNameEditLog table").hide();
                curStaStr = `<p>当前资料尚未经修改。</p>`
            }
            $("#branchNameEditLog .curSta").html(curStaStr);
        }
    });
}

// creator: 李玉婷，2022-08-18 10:43:12，批量导入其他职工页的返回
function isMobileTest(obj){
    let mobile = obj.val();
    if (mobile !== "" && !testMobile(obj.val())) {
        layer.msg("<p>系统无法向您录入的手机号发送提示短信。</p><p>请检查确认，或换个其他号码！</p>")
        return false
    }
}
// creator: 李玉婷，2022-08-18 10:43:12，批量导入其他职工页的返回
function reback(type){
    if (type === 'addBranches') {
        let type = $(".part_addBranches").data("type");
        if (type === 'add') {
            jumpTo('mainCompany');
        } else {
            jumpTo('branchesMain', 'managerBranch')
        }
    } else {
        let storage = $(".part_own").data("storage");//,{"type": 'add',"source": 1, "sonOid": info.sonOid}
        if (storage.source === 1) {
            jumpTo('mainCompany')
        } else {
            jumpTo('confirmHandler', 'noSetManager')
        }
    }
}
// creator: 李玉婷，2020-04-03 10:28:28，描述、说明字数限制
function limitWord(obj, num) {
    var val = obj.val();
    var length = val.length;
    obj.siblings(".lenTip").html(length + '/' + num);
}