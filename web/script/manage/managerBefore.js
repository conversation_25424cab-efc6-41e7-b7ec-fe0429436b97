$(function () {
    // 获取高管列表
    getList();
});
// creator: 侯杏哲 2018-07-19  获取高管列表
function getList(type) {
    $.ajax({
        "url": "../sys/dynamicManageList.do",
        success: function (res) {
            var list = res["manageList"], superButton = res["superButton"], manageButton = res["manageButton"];
            // manageList[{name 高管列表名,code,user 有值就是对应列表名的高管人员，isDuty （1-机构在职人员 有办理离职按钮，3代理人员 没有离职按钮）},{}]
            // superButton ： 有无操作总经理按钮 0-没有按钮，1有按钮
            // manageButton： 有无操作高管按钮 0-没有按钮，1有按钮
            var navStr = "", str = "", addBtnStr = "";
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var code = list[i]["code"];
                    var name = list[i]["name"].replace("人员", "");
                    var user = list[i]["user"];
                    // 二级导航栏
                    navStr += "<li class='" + code + "'>" + name + "</li>";
                    // 人员列表
                    if (user) {
                        str += "<tr class='" + code + "'>" +
                            "<td> " + name + " </td>" +
                            "<td> " + user["mobile"] + " </td>" +
                            "<td> " + user["userName"] + " </td>" +
                            "<td> " + ( user["handleTime"] && user["handleTime"].substr(0, 10)) + " </td>" +
                            "<td>";

                        str += "</td> </tr>";
                    } else {
                        str += "<tr class='" + code + "'>" +
                            "<td> " + name + " </td>" +
                            "<td></td> <td></td><td></td>" +
                            "<td>";
                        if (manageButton == "1") {
                            str += "<span class='ty-color-green' onclick='addManage(\"" + code + "\")' >添加</span>";
                        }
                        str += "</td> </tr>";
                    }

                }
            }
            // $("#addBtns").html(addBtnStr);
            $(".panel_0 tbody").html(str);

        }
    })
}

// creator: 侯杏哲 2018-07-19  新增高管
function addManage(code) {
    var strName = "";
    switch (code) {
        case "smallSuper" :
            strName = "总经理";
            break;
        case "general" :
            strName = "总务";
            break;
        case "finance" :
            strName = "财务";
            break;
        case "sale" :
            strName = "销售";
            break;
        case "account" :
            strName = "会计";
            break;
        case "accounting" :
            $("#yes_phone").val("");
            $("#yes_name").val("");
            bounce.show($("#addAccount"));//显示添加会计弹框
            setAgentType(0, $("#no"));
            break;
        default :
            break;
    }

    if (code != "accounting") {
        if (code == "smallSuper") {
            bounce.show($("#confirm"));
        } else {
            bounce.show($("#addmanager"));
        }
        $("#btclick").removeClass("ty-btn-green").addClass("btnco");
        $("#managePhone").html("");
        $("#userName").html("");
        $("#mgName").html(strName);
        $("#manageName").val(code);
        $("#managePhone").val("");
        $("#addManagerName").val("");
        $("#addTip").html("");
    }

}

// updator: 王静 2017-07-07 11:20:43 确定添加管理者
function okSubmit() {
    var phone = $("#managePhone").val();
    var name = $("#addManagerName").val();
    var code = $("#manageName").val();
    if (phone != "" && testMobile(phone)) {
        if (code == "smallSuper") {
            $.ajax({
                "url": "../sys/addOrgSmallSuper.do",
                "data": {"phone": phone, "userName": name}, // phone小超管手机号，userName小超管名字
                success: function (res) {
                    var status = res["status"]; // status 1-成功 0-失败（参数传错了） 2 -登录人不是超管没资格新增 3-手机号已存在
                    if (status == 1) {
                        bounce.cancel();
                        layer.msg("新增成功");
                        getList("smallSuper");
                    } else if (status == 0) {
                        layer.msg("新增失败");
                    } else if (status == 2) {
                        layer.msg("登录人不是超管没资格新增");
                    } else if (status == 3) {
                        layer.msg("手机号已存在");
                    }
                }
            })
        } else {
            $.ajax({
                "url": "../org/addInitialManage.do",
                "data": {"phone": phone, "userName": name, "manageName": code},
                success: function (res) {
                    var status = res["status"]; // status 1-成功 0-失败（参数传错了） 2 -登录人不是超管没资格新增 3-手机号已存在
                    if (status == 1) {
                        bounce.cancel();
                        layer.msg("新增成功");
                        getList(code);
                    } else if (status == 0) {
                        layer.msg("新增失败");
                    } else if (status == 2) {
                        layer.msg("手机号已存在");
                    } else if (status == 3) {
                        layer.msg("登录人不是超管没资格新增");
                    }
                }
            });
        }

    } else {
        $("#addTip").html("手机号格式不规范，请重新输入");
    }
    if (phone == "") {
        $("#btclick").attr("disabled");
        $("#addTip").html("");
    }
    if ($.trim(name) == "") {
        $("#btclick").attr("disabled");
        $("#addTip").html("请输入高管姓名！");
    }
}

// creator : 侯杏哲 2018-01-26  设置新增会计是否代理
function setAgentType(type, _this) {
    cur_type = type;
    if (type == 1) {
        layer.msg("机构正式启用前，不可录入代理会计！");
        return false;
        _this.parent().next().html("请输入客必盛业务人员信息");
    } else {
        _this.parent().next().html("请输入贵公司会计负责人信息");
    }
    $("#AgentType").val(type);
    $("#agentType").val(type);
    _this.attr("class", "fa fa-dot-circle-o");
    _this.siblings(".fa").attr("class", "fa fa-circle-o");
}

// updator:侯杏哲 2018-01-25  添加会计，点击确定
function addAccountSubmit() {
    var phone = $('#yes_phone').val();
    var name = $('#yes_name').val();
    var type = $("#AgentType").val();
    if (phone == "" || name == "" || type == "") {
        $(".input_notice").html("信息录入不完整");
        return;
    } else {
        $(".input_notice").html("");
    }
    $.ajax({
        url: "../org/addInitialManage.do",
        // manageName 高管code（general总务，finance财务，sale销售 ，accounting 机构会计，代理会计agentAccounting）， phone 手机号， userName 用户名
        data: {"manageName": "accounting", "phone": phone, "userName": name}, // agentType 1-代理  0-非代理
        success: function (res) { // status -0 参数不够，1-成功，2- 手机号已存在
            var status = res["status"];
            if (status == 0) {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("参数不够");
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            } else if (status == 1) {
                $("#kuaiji").remove();
                var user = res["user"];
                if (!user) {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("没有返回用户信息！");
                    $("#mtTip").attr("class", "bonceContainer bounce-red");
                    return false;
                }
                getList("accounting");
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("添加成功");
                $("#mtTip").attr("class", "bonceContainer bounce-green");
            } else if (status == 2) {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("手机号已存在");
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            } else {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html(res);
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            }
            ;
        }
    });
}

//creator: 王静 2017-07-07 11:40:33 添加管理者-确定按钮的亮暗
function addColor() {
    if ($.trim($("#managePhone").val()) == "" || $.trim($("#addManagerName").val()) == "") {
        $("#btclick").addClass("btnco").removeAttr("onclick").removeClass("ty-btn-green");
    } else {
        $("#btclick").attr("onclick", "addManagerCheck()").addClass("ty-btn-green").removeClass("btnco");
    }
}
// creator: 李玉婷，2020-05-24 17:51:26，添加管理者 - 查重提示
function addManagerCheck() {
    var phone   = $("#managePhone").val();
    mobileCheckRepeat(phone, "").then((data) => {
        var status = data["state"];
        if (status === 1) {
            okSubmit();
        } else if (status === 2 || status === 3) {
            var str =
                '<p>'+ data.cont +'</p>' ;
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").hide();$("#tjSure").attr("onclick", "okSubmit()");
            $(".canAllow").show();
        }else if(status === 0) {
            var str = '<p>'+ data.cont +'</p>';
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").show();$(".canAllow").hide();
        }
    }, (err) => {})
}
// creator: 李玉婷，2020-05-24 21:32:04，添加会计，查重提示
function addAccountCheck() {
    var phone = $('#yes_phone').val();
    mobileCheckRepeat(phone, "").then((data) => {
        var status = data["state"];
        if (status === 1) {
            addAccountSubmit();
        } else if (status === 2 || status === 3) {
            var str =
                '<p>' + data.cont + '</p>';
            common_bounce.show($("#checkTip"));
            $("#checkTip_ms").html(str);
            $(".notAllow").hide();
            $("#tjSure").attr("onclick", "addAccountSubmit()");
            $(".canAllow").show();
        }else if(status === 0) {
            var str = '<p>' + data.cont + '</p>';
            common_bounce.show($("#checkTip"));
            $("#checkTip_ms").html(str);
            $(".notAllow").show();
            $(".canAllow").hide();
            return;
        }
    }, (err) => {
    })
}

// ============================== ======================================================== //

/*


$(function () {
    // 获取高管列表
    getList();
});
// creator: 侯杏哲 2018-07-19  获取高管列表
function getList(type) {
    $.ajax({
        "url": "../sys/dynamicManageList.do",
        success: function (res) {
            var list = res["manageList"], superButton = res["superButton"], manageButton = res["manageButton"];
            // manageList[{name 高管列表名,code,user 有值就是对应列表名的高管人员，isDuty （1-机构在职人员 有办理离职按钮，3代理人员 没有离职按钮）},{}]
            // superButton ： 有无操作总经理按钮 0-没有按钮，1有按钮
            // manageButton： 有无操作高管按钮 0-没有按钮，1有按钮
            var navStr = "", str = "", addBtnStr = "";
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var code = list[i]["code"];
                    var name = list[i]["name"];
                    var user = list[i]["user"];
                    // 二级导航栏
                    navStr += "<li class='" + code + "'>" + name + "</li>";
                    // 人员列表
                    if (user) {
                        str += "<tr class='" + code + "'>" +
                            "<td> " + name + " </td>" +
                            "<td> " + user["mobile"] + " </td>" +
                            "<td> " + user["userName"] + " </td>" +
                            "<td> " + ( user["handleTime"] && user["handleTime"].substr(0, 10)) + " </td>" +
                            "<td>";

                        if (code == "smallSuper") { // 总经理
                            if (superButton == 1) {
                                str += "<span class='ty-color-gray'  >编辑</span>";
                                str += "<span class='ty-color-gray'  >离职处理</span>";
                            }
                        } else { // 其他高管
                            if (manageButton == "1") { // 有操作按钮
                                str += "<span class='ty-color-gray'  >编辑</span>";
                                if (code == "accounting") { // 会计
                                    if (user["agentType"] == 1) { // 代理会计
                                        str += "<span class='ty-color-gray' >离职处理</span>";
                                    } else { // 机构会计
                                        str += "<span class='ty-color-gray'  >离职处理</span>";
                                    }
                                    str += "<span class='ty-color-gray'  >修改代理状态</span>";

                                } else {
                                    str += "<span class='ty-color-gray'  >离职处理</span>";
                                }
                            }
                        }
                        str += "</td> </tr>";
                    } else {
                        str += "<tr class='" + code + "'>" +
                            "<td> " + name + " </td>" +
                            "<td></td> <td></td><td></td>" +
                            "<td>" ;
                        if (manageButton == "1"){
                            str += "<span class='ty-color-green' onclick='addManage(\"" + code + "\")' >添加</span>" ;
                        }
                        str +=  "</td> </tr>";
                    }

                }
            }
            // $("#addBtns").html(addBtnStr);
            $(".panel_0 tbody").html(str);

        }
    })
}

var current_mobile, current_name, current_userID, cur_obj; //当前处理对象的手机号、姓名、id, cur_obj--当前处理的对象
//creator:王静 2017-07-06 15:04:34 离职处理弹框
function departure(id, obj) {
    $("#changeTip").html("");
    cur_obj = obj;
    var phone = obj.parent().siblings(":eq(1)").html();
    var name = obj.parent().siblings(":eq(2)").html();
    var type = obj.parent().siblings(":eq(0)").html();
    bounce.show($("#departureDeal"));
    $("#sureBtn").removeClass("ty-btn-red").addClass("btnco");
    $("#descPhone").val("");
    $("#descName").val("");
    $("#oldMg").html(type);
    $("#newMg").html(type);
    $("#neweditUserID").val(id);
    $("#departurePhone").val(phone);
    $("#departureName").val(name);
    current_userID = id;
}

//updater:姚宗涛 2018-02-01 11:20:34 离职处理-确定按钮
function okDeparture() {
    var phone = $("#descPhone").val();
    var name = $("#descName").val();
    if ($.trim(phone) == "" || $.trim(name) == "") {
        $("#changeTip").html("手机号和姓名均不能为空");
        return;
    } else {
        if (testMobile($.trim(phone))) {
            bounce.show($("#sureEditManager"));
            $("#mg").html("离职处理").addClass("ty-color-red");
            $("#sureEditphoe").html($("#departurePhone").val());
        } else {
            $("#changeTip").html("手机号填写不正确！");
        }
    }
}

//updater:姚宗涛 2018-02-01 11:32:34 离职处理-确定按钮的亮暗
function changeColor() {
    if ($("#descName").val() != "" && $("#descPhone").val() == "") {
        $("#sureBtn").attr('disabled');
        $("#sureBtn").addClass('btnco');
    }
    if ($("#descPhone").val() != "") {
        $("#sureBtn").removeClass('btnco').addClass('ty-btn-red').removeAttr('disabled');
    } else {
        $("#sureBtn").attr('disabled');
        $("#sureBtn").removeClass('ty-btn-red').addClass('btnco');
    }
}

//updator: 王静 2017-07-06 14:28:56 切换显示页面
function panelShow(num, obj) {
    manager = num;
    for (var i = 0; i <= 5; i++) {
        $(".panel_" + i).hide();
    }
    $(".panel_" + num).show();
    $("#curN").html(obj.html());
    obj.addClass("ty-active").siblings().removeClass("ty-active");
}

//updater:姚宗涛 2018-02-02 16:02:34  确定无误页面-确定
function sureEdit() {
    localStorage.managerType = manager;
    if ($("#mg").html() == "修改") {
        $('#editManage').submit();
    }
    if ($("#mg").html() == "离职处理") {
        var cur_tr = cur_obj.parent().parent();
        var id = $("#neweditUserID").val();
        var phone = $("#descPhone").val();
        var userName = $("#descName").val();
        $.ajax({
            url: "../sys/outManage.do",
            data: {"id": id, "phone": phone, "userName": userName},
            type: "post",
            dataType: "json",
            beforeSend: function () {
                loading.open();
            },
            success: function (data) {
                console.log(data);
                var user = data["user"];
                var status = data["status"];
                if (status == 1) {
                    cur_tr.children("td").eq(1).html(user.mobile);
                    cur_tr.children("td").eq(2).html(user.userName);
                    cur_tr.children("td").eq(3).html(user["handleTime"]);
                }
                if (status == 2) {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("手机号已存在！");
                    $("#mtTip").attr("class", "bonceContainer bounce-red");
                }
                if (status == 0) {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("传入的参数错误！");
                    $("#mtTip").attr("class", "bonceContainer bounce-red");
                }
            },
            error: function () {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            },
            complete: function () {
                loading.close();
            }
        });
        bounce.cancel();
    }
}

//creator:王静 2017-07-07 10:23:34  编辑修改、离职处理时确定无误-关闭
function sureDelete() {
    if ($("#mg").html() == "修改") {
        bounce.cancel();
    }
    if ($("#mg").html() == "离职处理") {
        bounce.cancel();
        $("#descPhone").val("");
        $("#descName").val("");
        $("#sureBtn").removeClass("ty-btn-red").addClass("btnco");
    }
}

//updator: 王静 2017-07-07 10:59:43 添加管理者弹框
function addManage(code) {
    var strName = "";
    switch (code) {
        case "smallSuper" :
            strName = "总经理";
            break;
        case "general" :
            strName = "总务";
            break;
        case "finance" :
            strName = "财务";
            break;
        case "sale" :
            strName = "销售";
            break;
        case "account" :
            strName = "会计";
            break;
        case "accounting" :
            bounce.show($("#addAccount"));//显示添加会计弹框
            setAgentType(1, $("#yes"));
            break;
        default :
            break;
    }

    if (code != "accounting") {
        if(code == "smallSuper"){
            bounce.show($("#confirm"));
        }else{
            bounce.show($("#addmanager"));
        }
        $("#btclick").removeClass("ty-btn-green").addClass("btnco");
        $("#managePhone").html("");
        $("#userName").html("");
        $("#mgName").html(strName);
        $("#manageName").val(code);
        $("#managePhone").val("");
        $("#addManagerName").val("");
        $("#addTip").html("");
    }

}

// updator: 王静 2017-07-07 11:20:43 确定添加管理者
function okSubmit() {
    var phone = $("#managePhone").val();
    var name = $("#addManagerName").val();
    var code = $("#manageName").val();
    if (phone != "" && testMobile(phone)) {
        if (code == "smallSuper") {
            $.ajax({
                "url": "../sys/addOrgSmallSuper.do",
                "data": {"phone": phone, "userName": name}, // phone小超管手机号，userName小超管名字
                success: function (res) {
                    var status = res["status"]; // status 1-成功 0-失败（参数传错了） 2 -登录人不是超管没资格新增 3-手机号已存在
                    if (status == 1) {
                        bounce.cancel();
                        layer.msg("新增成功");
                        getList("smallSuper");
                    } else if (status == 0) {
                        layer.msg("新增失败");
                    } else if (status == 2) {
                        layer.msg("登录人不是超管没资格新增");
                    } else if (status == 3) {
                        layer.msg("手机号已存在");
                    }
                }
            })
        } else {
            $.ajax({
                "url": "../org/addInitialManage.do",
                "data": {"phone": phone, "userName": name, "manageName": code},
                success: function (res) {
                    var status = res["status"]; // status 1-成功 0-失败（参数传错了） 2 -登录人不是超管没资格新增 3-手机号已存在
                    if (status == 1) {
                        bounce.cancel();
                        layer.msg("新增成功");
                        getList(code);
                    } else if (status == 0) {
                        layer.msg("新增失败");
                    } else if (status == 2) {
                        layer.msg("登录人不是超管没资格新增");
                    } else if (status == 3) {
                        layer.msg("手机号已存在");
                    }
                }
            });
        }

    } else {
        $("#addTip").html("手机号格式不规范，请重新输入");
    }
    if (phone == "") {
        $("#btclick").attr("disabled");
        $("#addTip").html("");
    }
    if ($.trim(name) == "") {
        $("#btclick").attr("disabled");
        $("#addTip").html("请输入高管姓名！");
    }
}

//creator: 王静 2017-07-07 11:40:33 添加管理者-确定按钮的亮暗
function addColor() {
    if ($.trim($("#managePhone").val()) == "" || $.trim($("#addManagerName").val()) == "") {
        $("#btclick").addClass("btnco").removeAttr("onclick").removeClass("ty-btn-green");
    } else {
        $("#btclick").attr("onclick", "okSubmit()").addClass("ty-btn-green").removeClass("btnco");
    }
}

// updator: 王静 2017-07-07 11:45:45 修改管理者
function editManages(userID, obj) {
    var phone = obj.parent().siblings(":eq(1)").html();
    var name = obj.parent().siblings(":eq(2)").html();
    var type = obj.parent().siblings(":eq(0)").html();
    bounce.show($("#editManage"));
    var typeStr = type;
    if (type == "浏览者") {
        typeStr = "超级浏览者";
    }
    $("#editMg").html(typeStr);
    $("#editUserID").val(userID);
    $("#manageName").val(type);
    $("#editPhone").val(phone);
    $("#editTip").html("");
    $("#managerName").val(name);
    current_mobile = phone;
    current_name = name;
}

//creator: 王静 2017-07-07 13:20:33确认修改管理者
var manager = 0;

function okEditSubmit() {
    localStorage.managerType = manager;
    var phone = $("#editPhone").val();
    var name = $("#managerName").val();
    if ($.trim(phone) == "" || $.trim(name) == "") {
        $("#editTip").html("手机号和姓名均不能为空");
    } else if (current_mobile != $("#editPhone").val()) {
        bounce.show($("#sureEditManager"));
        $("#mg").html("修改").addClass("ty-color-red");
        $("#sureEditphoe").html(current_mobile);
    } else if (current_name != $("#managerName").val()) {
        $("#editManage").submit();
        bounce.cancel();
    } else {
        bounce.cancel();
    }

}

// creator : 侯杏哲 2018-01-26  设置新增会计是否代理
function setAgentType(type, _this) {
    cur_type = type;
    if (type == 1) {
        _this.parent().next().html("请输入客必盛业务人员信息");
    } else {
        _this.parent().next().html("请输入贵公司会计负责人信息");
    }
    $("#AgentType").val(type);
    $("#agentType").val(type);
    _this.attr("class", "fa fa-dot-circle-o");
    _this.siblings(".fa").attr("class", "fa fa-circle-o");
}

// updator:侯杏哲 2018-01-25  添加会计，点击确定
function addAccountSubmit() {
    var phone = $('#yes_phone').val();
    var name = $('#yes_name').val();
    var type = $("#AgentType").val();
    if (phone == "" || name == "" || type == "") {
        $(".input_notice").html("信息录入不完整");
        return;
    } else {
        $(".input_notice").html("");
    }
    $.ajax({
        url: "addAccounting.do",
        data: {"phone": phone, "userName": name, "agentType": type}, // agentType 1-代理  0-非代理
        type: "post",
        beforeSend: function () {
            loading.open();
        },
        dataType: "json",
        success: function (res) { // status -0 参数不够，1-成功，2- 手机号已存在
            var status = res["status"];
            if (status == 0) {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("参数不够");
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            } else if (status == 1) {
                $("#kuaiji").remove();
                var user = res["user"];
                if (!user) {
                    bounce.show($("#mtTip"));
                    $("#mt_tip_ms").html("没有返回用户信息！");
                    $("#mtTip").attr("class", "bonceContainer bounce-red");
                    return false;
                }
                getList("accounting");
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("添加成功");
                $("#mtTip").attr("class", "bonceContainer bounce-green");
            } else if (status == 2) {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("手机号已存在");
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            } else {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html(res);
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            }
            ;
        },
        error: function (res) {
            console.log(res);
            bounce.show($("#mtTip"));
            $("#mt_tip_ms").html("连接失败");
            $("#mtTip").attr("class", "bonceContainer bounce-red");
        },
        complete: function () {
            loading.close();
        }
    });
}

//updator: 侯杏哲 2018-01-18 14:16:54  修改代理状态按钮
function agentState(userID, _this, agentType) {
    cur_obj = _this;
    current_userID = userID;
    bounce.show($('#update_agentState'));
    $("#sureInfo").hide();
    $("#showInfo").show();
    $("#info").hide();
    $("#ttl").show();
    if (agentType == 1) {
        $("#agentType_up").html("当前贵公司会计业务由客必盛代理");
        $("#agentType_up_").html("不再由客必盛代理？");
        $("#input_title").html("请输入贵公司会计负责人信息");
        $("#changeAgentType").html("0");
    } else {
        $("#agentType_up").html("当前贵公司会计业务不是客必盛代理");
        $("#agentType_up_").html("修改为由客必盛代理？");
        $("#input_title").html("请输入客必盛业务人员信息");
        $("#changeAgentType").html("1");
    }
}

// creator : 侯杏哲 2017-01-27 修改代理状态 - 录入新的会计负责人
function showInfo() {
    $("#showInfo").hide();
    $("#sureInfo").show();
    $("#info").show();
    $("#ttl").hide();
    $("#info input").val("");
}

// updator，侯杏哲  2017-01-27  修改代理状态的确定按钮
function changeAgentState_sure() {
    var phone = $("#_phone").val();
    var name = $("#_name").val();
    var type = $("#changeAgentType").html();
    if ($.trim(phone) == "" || $.trim(name) == "") {
        $("#agentTip").html("手机号码和姓名不能为空！");
        return false;
    }
    bounce.cancel();
    $.ajax({
        url: "../sys/editAccountingAgentType.do",
        data: {"phone": phone, "userName": name, "agentType": type, "userId": current_userID},
        type: "post",
        beforeSend: function () {
            loading.open();
        },
        dataType: "json",
        success: function (res) { // status -0 失败，1-成功 2-手机号已存在，
            var status = res["status"];
            var user = res["user"];
            if (status == 1) {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("修改成功");
                $("#mtTip").attr("class", "bonceContainer bounce-green");
                cur_obj.parent().siblings(":eq(1)").html(user["mobile"]);
                cur_obj.parent().siblings(":eq(2)").html(user["userName"]); //把修改的值从数据库拿出展示在前端
                cur_obj.parent().siblings(":eq(3)").html(user["createTime"]);
                cur_obj.siblings(":eq(0)").attr("onclick", "editManages(" + user["userID"] + ",$(this))");
                if (type == 1) {
                    cur_obj.attr("onclick", "agentState(" + user["userID"] + ", $(this) ,1 )");
                    cur_obj.prev().attr("class", "ty-color-gray").removeAttr("onclick");
                    $("#AgentType").val(0);
                    $("#agentType").val(0);
                } else {
                    cur_obj.attr("onclick", "agentState(" + user["userID"] + ", $(this) ,0 )");
                    cur_obj.prev().attr("class", "ty-color-red").attr("onclick", "departure(" + user["userID"] + " , $(this) )");
                    $("#AgentType").val(1);
                    $("#agentType").val(1);
                }

            } else if (status == 2) {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("手机几号已存在");
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            } else {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("修改失败");
                $("#mtTip").attr("class", "bonceContainer bounce-red");
            }
        },
        error: function (res) {
            console.log(res);
            bounce.show($("#mtTip"));
            $("#mt_tip_ms").html("连接失败");
            $("#mtTip").attr("class", "bonceContainer bounce-red");
        },
        complete: function () {
            loading.close();
        }

    });
}

*/
