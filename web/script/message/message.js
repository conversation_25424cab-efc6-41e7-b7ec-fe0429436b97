/**
 * Created by 张旭博 on 2017/3/17.
 */

$(function () {
    $(".ty-secondTab li").on("click",function () {
        var index = $(this).index();
        $(this).addClass("ty-active").siblings().removeClass("ty-active");
        setMessageList(1,20,index+1);
    });
    $(".ty-active").click();
});

/* creator：张旭博，2017-12-08 17:36:55，获取“未处理”或“已处理”的数据 */
function setMessageList(curr,totalPage,num) {
    if(num === 1){
        $("#navTxt").html("未处理");
    }else{
        $("#navTxt").html("已处理");
    }
    $.ajax({
        url:"../message/getAllMessage.do",
        data:{   "status": num,  "currentPageNo":curr, "pageSize":totalPage  },
        success:function(data){
            var  userMessages = data["userMessages"] ;
            var  pageInfo = data["pageInfo"] ;
            var str="";
            var mycurr=pageInfo.currentPageNo;
            var ttlPage=pageInfo.totalPage;
            var json = JSON.stringify({
                "num": num
            });
            setPage($("#ye-message"),mycurr,ttlPage,"message",json);
            if(userMessages && userMessages.length > 0 ){
                for(var i = 0 ; i < userMessages.length ; i++){
                    if(userMessages[i]['eventType'] == "请假申请" || userMessages[i]['eventType'] == "请假结果"
                        || userMessages[i]['eventType'] == "加班申请" || userMessages[i]['eventType'] == "加班结果"){
                        // 请假加班消息暂时不予显示
                    }else{
                        str+="<tr>"+
                            "<td>"+ (userMessages[i]["applicant"] || "") +"</td>"+
                            "<td>"+ userMessages[i]['eventType'] +"</td>"+
                            "<td><a style='cursor:pointer;' onclick='news_details("+ JSON.stringify( userMessages[i] ) +" , $(this) )'>" + userMessages[i]["illustrate"] +"</a></td>" +
                            "<td>" + (new Date(userMessages[i].createDate).format('yyyy-MM-dd hh:mm:ss'))  + "</td>" +
                            "</tr>" ;
                    }

                }
            }
            $("#Msg").html(str);
        }
    })
}

//updater:侯杏哲，2017-5-19 ，点击后的消息详情 根据1.7.1同一页面样式的需求，变更查看消息详情，直接跳回对应的页面
var editTr = null ;
function news_details( jsonObj , _this ){  // jsonObj:过程信息 , type:消息类型（1：未处理； 2：已处理） ，
    editTr = _this.parent().parent() ;
    var processID = jsonObj["messageId"] ; // 审批过程的id
    //var messageType = jsonObj["messageId"] ; //讨论区的消息
    var applyID = jsonObj["personnelReimburId"] ; // 在此为某一条报销的id(若加班、请假为多级审批时也可用)
    var userMessageId = jsonObj["id"] ; // 消息ID
    var eventType = jsonObj["eventType"];  // 事件类型
    var approvalStatus = jsonObj["approvalStatus"];  // 区分财务的几种状态 1 - 待审批 2-批准，3-驳回 4-待两讫 5-已报销
    var state = jsonObj["state"];  // 1-待处理 2-已处理
    var isNull = jsonObj["isNull"];  // 为空时需要审批，跳转“我的请求”页面； 1不需要审批，跳转“请求处理页”
    var zhiXi = jsonObj["zhiXi"];  //  1 — 作为直系领导收到的消息 ，否则另作判断
    var mtStockId = jsonObj["mtStockId"];  //  1 — 作为直系领导收到的消息 ，否则另作判断
    var coreCode = jsonObj["title"];  //  1 — 核心人物 2-立案者 2-处理者

    switch (eventType) {
        case "请假申请" :
            var url = "" ;
            if($.trim(isNull)==""){  // 跳转 请求处理 - 请假请求
                url = "../user/leave.do?processID="+ processID ;
                $.cookie('pid', "ma", { path: '/' });
                $.cookie('sid', "mc", { path: '/' });
            }else{ // 跳转我的请求-请假请求
                url = "../user/myLeave.do?applyID="+ applyID ;
                $.cookie('pid', "ma", { path: '/' });
                $.cookie('sid', "md", { path: '/' });
            }
            location.href = url ;
            break ;
        case "请假结果" :
            if( zhiXi == 1 ) { // 直系审批人查看在本页面显示
                getLeaveDetail( applyID ) ;
            }else{
                var url = "" ;
                if($.trim(isNull)==""){  // 跳转 请求处理 - 请假请求
                    url = "../user/leave.do?processID="+ processID ;
                    $.cookie('pid', "ma", { path: '/' });
                    $.cookie('sid', "mc", { path: '/' });
                }else{ // 跳转我的请求-请假请求
                    url = "../user/myLeave.do?applyID="+ applyID ;
                    $.cookie('pid', "ma", { path: '/' });
                    $.cookie('sid', "md", { path: '/' });
                }
                location.href = url ;
            }
            break ;
        case "加班申请" :
            var url = "" ;
            if($.trim(isNull)==""){   // 跳转 请求处理 - 加班请求
                url = "../user/overtime.do?processID="+ processID  ;
                $.cookie('pid', "ma", { path: '/' });
                $.cookie('sid', "mc", { path: '/' });
            }else{  // 跳转 我的请求-加班请求
                url = "../user/myOvertime.do?applyID="+ applyID ;
                $.cookie('pid', "ma", { path: '/' });
                $.cookie('sid', "md", { path: '/' });
            }
            location.href = url ;
            break ;
        case "加班结果" :
            if( zhiXi == 1 ) { // 直系审批人查看在本页面显示
                getOverTimeDetail( applyID ) ;
            }else{
                var url = "" ;
                if($.trim(isNull)==""){   // 跳转 请求处理 - 加班请求
                    url = "../user/overtime.do?processID="+ processID  ;
                    $.cookie('pid', "ma", { path: '/' });
                    $.cookie('sid', "mc", { path: '/' });
                }else{  // 跳转 我的请求-加班请求
                    url = "../user/myOvertime.do?applyID="+ applyID ;
                    $.cookie('pid', "ma", { path: '/' });
                    $.cookie('sid', "md", { path: '/' });
                }
                location.href = url ;
            }
            break ;
        case "报销申请" :
            var url = "" ;
            if($.trim(isNull)==""){   // 跳转 请求处理 - 报销请求
                if( approvalStatus == 1 ){ // 待审批
                    url = "../expense/chargeSubmitSale.do?processID="+ processID +"&approvalStatus=" + approvalStatus ;
                    $.cookie('pid', "ma", { path: '/' });
                    $.cookie('sid', "mc", { path: '/' });
                }else if( approvalStatus == 2 || approvalStatus == 4 || approvalStatus == 6 || approvalStatus == 5){
                    url = "../financeJump/expenseReceive.do?processID="+ processID +"&approvalStatus=" + approvalStatus ;
                    $.cookie('pid', "la", { path: '/' });
                    $.cookie('sid', "lp", { path: '/' });
                }else{
                    alert(approvalStatus) ;
                }
            }else{  // 跳转 我的请求 - 报销申请
                url = "../user/myApplication.do?applyID="+ applyID +"&approvalStatus=" + approvalStatus ;
                $.cookie('pid', "ma", { path: '/' });
                $.cookie('sid', "md", { path: '/' });
            }
            location.href = url ;
            break ;
        case "报销结果" :
        case "两讫结果" :
            if( zhiXi == 1 ) { // 直系审批人查看在本页面显示
                getFinanceDetail( applyID ) ;
            }else{
                var url = "" ;
                if($.trim(isNull)==""){   // 跳转 请求处理 - 报销请求
                    if( approvalStatus == 1 ){ // 待审批
                        url = "../expense/chargeSubmitSale.do?processID="+ processID +"&approvalStatus=" + approvalStatus ;
                        $.cookie('pid', "ma", { path: '/' });
                        $.cookie('sid', "mc", { path: '/' });
                    }else if( approvalStatus == 2 || approvalStatus == 4 || approvalStatus == 6 || approvalStatus == 5){
                        url = "../financeJump/expenseReceive.do?processID="+ processID +"&approvalStatus=" + approvalStatus ;
                        $.cookie('pid', "la", { path: '/' });
                        $.cookie('sid', "lp", { path: '/' });
                    }else{
                        alert(approvalStatus) ;
                    }
                }else{  // 跳转 我的请求 - 报销申请
                    url = "../user/myApplication.do?applyID="+ applyID +"&approvalStatus=" + approvalStatus ;
                    $.cookie('pid', "ma", { path: '/' });
                    $.cookie('sid', "md", { path: '/' });
                }
                location.href = url ;
            }

            break;
        case "修改申请" :
        case "审批结果" :
            var url = "";
            url = "../update/toUpdatePage.do?processID=" + processID + "&approvalStatus=" + approvalStatus;
            $.cookie('pid', "lq", {path: '/'});
            $.cookie('sid', "la", {path: '/'});
            location.href = url;
            break;
        case "仓库修正":
            getStorageAcceptDetail(userMessageId, state);
            break;
        case "成品出库":
            outStorageOkBtn(1, 20, mtStockId, userMessageId);
            break;
        case "客户签收":
            signCheckBtn(mtStockId, userMessageId);
            break;
        case "投诉立案":
            var url = "";
            var pid = "";
            switch (coreCode) {
                case "1":
                    pid = "fb";
                    url = "../coreSetting/toIntegrateManage.do?complaintID=" + processID + "&approvalStatus=" + approvalStatus;
                    break;
                case "2":
                    pid = "fc";
                    url = "../coreSetting/toComplaintFiling.do?complaintID=" + processID + "&approvalStatus=" + approvalStatus;
                    break;
            }
            $.cookie('pid', pid, {path: '/'});
            $.cookie('sid', "fa", {path: '/'});
            location.href = url;
            break;
        case "投诉结案":
            var url = "";
            switch (coreCode) {
                case "1":
                    pid = "fb";
                    url = "../coreSetting/toIntegrateManage.do?complaintID=" + processID + "&approvalStatus=" + approvalStatus;
                    break;
                case "2":
                    pid = "fc";
                    url = "../coreSetting/toComplaintFiling.do?complaintID=" + processID + "&approvalStatus=" + approvalStatus;
                    break;
                case "3":
                    pid = "fd";
                    url = "../coreSetting/toComplaintHandling.do?complaintID=" + processID + "&approvalStatus=" + approvalStatus;
                    break;
            }
            $.cookie('pid', pid, {path: '/'});
            $.cookie('sid', "fa", {path: '/'});
            location.href = url;
            break;
        case "投诉处理":
            var url = "";
            url = "../coreSetting/toComplaintHandling.do?complaintID=" + processID + "&approvalStatus=" + approvalStatus;
            $.cookie('pid', "fd", {path: '/'});
            $.cookie('sid', "fa", {path: '/'});
            location.href = url;
            break;
        case "讨论区发布":
            location.href = "../forum/discussionRecord.do?processID=" + processID + "&id=" + userMessageId;
            break;
        case "科目设置":
            if (state == 1) { // 已读的消息不跳转
                $.cookie('pid', "sa", {path: '/'});
                $.cookie('sid', "sb", {path: '/'});
                location.href = "../accountant/subjectSet.do?id=" + userMessageId;
            }
            break;
        case "会计报税":
            if (state == 1) { // 已读的消息不跳转
                setRead(userMessageId);
            }
            break;
        case "项目管理":
            if (state == 1) { // 1-待处理 2-已处理 ， 已读的消息不跳转
                var personnelReimburId = jsonObj["personnelReimburId"];
                var proType = jsonObj["type"];
                var url = "";
                if (isNull == 1) { // 申请人收到的， 跳转项目管理-立案页面
                    url = "../project/toProjectBaseIndex.do?proId=" + personnelReimburId + "&type=" + proType + "&approvalStatus=" + approvalStatus;
                    $.cookie('pid', "ta", {path: '/'});
                    $.cookie('sid', "tc", {path: '/'});
                } else { // 审批人收到的， 跳转项目管理-综合管理
                    url = "../project/toProjectBaseManage.do?proId=" + personnelReimburId + "&type=" + proType + "&approvalStatus=" + approvalStatus;
                    $.cookie('pid', "ta", {path: '/'});
                    $.cookie('sid', "tb", {path: '/'});
                }
                location.href = url;
            }
            break;
        case "持续改进":
            var url = "";
            if (approvalStatus === 1 || approvalStatus === 4) {
                url = "../improvement/integrateManage.do?id=" + processID + "&status=" + approvalStatus;
                $.cookie('pid', "hb", {path: '/'});
                $.cookie('sid', "ha", {path: '/'});
            } else {
                url = "../improvement/improvementFiling.do?id=" + processID + "&status=" + approvalStatus;
                $.cookie('pid', "hc", {path: '/'});
                $.cookie('sid', "ha", {path: '/'});
            }

            location.href = url;
            break;
        default:
            layer.msg("不属于任何一种情况");
            break;
    }


}
// creator :hxz 2018-05-15 蒋会计报税消息设置为已读 
function setRead(applyID) {
    $.ajax({
        "url" : "../accountant/updateMessage.do" ,
        "data" : { "mid" : applyID } ,
        success:function (data) {
            var res = data["res"] ;
            layer.msg("消息设置为已读") ;
            editTr.remove() ;
        }
    })
}
// 获得 请假详情
function getLeaveDetail( id ){
    if(id){
        $.ajax({
            url:"../lo/myLeaveInfoById.do" ,
            data:{ "id" : id } ,
            type:"post" ,
            dataType:"json" ,
            beforeSend:function(){ loading.open() ;  } ,
            success:function(data){
                var status = data["status"] ;
                if(status == 0 ){
                    $("#mt_tip_ms").html("获得该条记录详情失败") ;
                    bounce.show($("#mtTip")) ;
                }else{
                    var processList = data["processList"] ;
                    var info = data["personnelLeave"] ;
                    bounce.show($("#leaveDetail")) ;
                    var str = "" ;
                    if(processList && processList.length>0){
                        for(var i = 0 ; i < processList.length ; i++){
                            if( processList[i]["approveStatus"] == "1" ){ // 待审批
                                str += "<div class='ty-process-item ty-process-wait'>" +
                                    "<p><span class='dot-wait'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }else if( processList[i]["approveStatus"] == "3"){ // 已驳回
                                str += "<div class='ty-process-item ty-process-no '>" +
                                    "<p><span class='dot-no'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }else{  // 已批准
                                str += "<div class='ty-process-item'>" +
                                    "<p><span class='dot'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }
                        }
                    }
                    $("#leaveProcess").html( str ) ;
                    if(info){
                        $("#createName1").html(info["createName"]) ;
                        $("#beginTime1").html(info["beginTime"]) ;
                        $("#endTime1").html(info["endTime"]) ;
                        $("#type1").html( chargeType(info["type"]) ) ;
                        $("#during1").html(info["duration"]) ;
                        $("#memo1").html(info["reason"]) ;
                    }else{
                        $("#createName1").html("获取失败") ;
                        $("#beginTime1").html("获取失败") ;
                        $("#endTime1").html("获取失败") ;
                        $("#type1").html("获取失败") ;
                        $("#during1").html("获取失败") ;
                        $("#memo1").html("获取失败") ;
                    }
                }
            } ,
            error:function(){
                $("#mt_tip_ms").html("获得该条记录详情失败,链接错误") ;
                bounce.show($("#mtTip")) ;
            } ,
            complete:function(){ loading.close() ;  }
        });
    }else{
        $("#mt_tip_ms").html("获得该条ID失败") ;
        bounce.show($("#mtTip")) ;
    }
}
// 获得 加班详情
function getOverTimeDetail( id ){
    if(id){
        $.ajax({
            url:"../lo/myOutTimeInfoById.do" ,
            data:{ "id" : id } ,
            type:"post" ,
            dataType:"json" ,
            beforeSend:function(){ loading.open() ;  } ,
            success:function(data){
                var status = data["status"] ;
                if(status == 0 ){
                    $("#mt_tip_ms").html("获得该条记录详情失败") ;
                    bounce.show($("#mtTip")) ;
                }else{
                    var processList = data["processList"] ;
                    var info = data["personnelOvertime"] ;
                    bounce.show($("#overtimeDetail")) ;
                    var str = "" ;
                    if(processList && processList.length>0){
                        for(var i = 0 ; i < processList.length ; i++){
                            if( processList[i]["approveStatus"] == "1" ){ // 待审批
                                str += "<div class='ty-process-item ty-process-wait'>" +
                                    "<p><span class='dot-wait'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }else if( processList[i]["approveStatus"] == "3"){ // 已驳回
                                str += "<div class='ty-process-item ty-process-no '>" +
                                    "<p><span class='dot-no'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }else{  // 已批准
                                str += "<div class='ty-process-item'>" +
                                    "<p><span class='dot'></span>处理人："+ processList[i]["toUserName"] +"/"+ processList[i]["userName"] +" </p>" +
                                    "<p>"+processList[i]["handleTime"]+"</p>" +
                                    "</div>" ;
                            }
                        }
                    }
                    $("#process").html( str ) ;
                    if(info){
                        $("#createName").html(info["createName"]) ;
                        $("#beginTime").html(info["beginTime"]) ;
                        $("#endTime").html(info["endTime"]) ;
                        $("#type").html( chargeTypeOverTime(info["type"]) ) ;
                        $("#during").html(info["duration"]) ;
                        $("#memo").html(info["reason"]) ;
                    }else{
                        $("#createName").html("获取失败") ;
                        $("#beginTime").html("获取失败") ;
                        $("#endTime").html("获取失败") ;
                        $("#type").html("获取失败") ;
                        $("#during").html("获取失败") ;
                        $("#memo").html("获取失败") ;
                    }

                }
            } ,
            error:function(){
                $("#mt_tip_ms").html("获得该条记录详情失败,链接错误") ;
                bounce.show($("#mtTip")) ;
            } ,
            complete:function(){ loading.close() ;  }
        });
    }else{
        $("#mt_tip_ms").html("获得该条ID失败") ;
        bounce.show($("#mtTip")) ;
    }
}
// 获取 报销详情
function getFinanceDetail( applyID ){
    bounce.show( $("#financeDetail") ) ;  // 显示详情处理页面
    $.ajax({
        url:"../expense/toOnePersonnelReimburseById.do",
        data:{  "id": applyID  },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function( data ){
            // 显示详情
            var applyInfo = data["personnelReimburse"] ;
            $("#createName2").html( applyInfo["createName"] ) ;
            $("#beginTime2").html( applyInfo["createDate"] ) ;
            $("#fee").html( applyInfo["feeCatName"] ) ;
            $("#ticket").html( applyInfo["billCatName"] ) ;
            $("#ticketMonth").html( chargeBillDate( applyInfo["billDate"] ) ) ;
            $("#summary").html( applyInfo["summary"] ) ;
            $("#purpose").html( applyInfo["purpose"] ) ;
            $("#billQuantity").html( applyInfo["billQuantity"] ) ;
            $("#amount").html( applyInfo["amount"] ) ;
            $("#memo2").html( applyInfo["memo"] ) ;
            // 展示审批流程
            var approvalProcess = data["approvalProcess"] ;
            var str = "" ;
            if( approvalProcess && approvalProcess.length > 0 ){
                for(var i = 0 ; i < approvalProcess.length ; i++ ){
                    var status = approvalProcess[i]["approveStatus"] ; //  approveStatus;//1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销
                    switch( Number(status) ){
                        case 2 :  // 已批准
                        case 5 :  // 已报销
                            str += "<div class='ty-process-item'>" +
                                "<p><span class='dot'></span>处理人："+ approvalProcess[i]["toUserName"] +"/"+approvalProcess[i]["userName"] +" </p>" +
                                "<p>"+ approvalProcess[i]["handleTime"] +"</p>" +
                                "</div>" ;
                            break ;
                        case 3 :  // 已驳回
                            str += "<div class='processTr noProcess '>"+
                                "<span class='no-icon'></span>" +
                                "<span class='touserInfo'>处理人 ：" + approvalProcess[i]["toUserName"] + "/ " + approvalProcess[i]["userName"] + "</span>" +
                                "<span class='timeInfo'>处理时间 ：" +approvalProcess[i]["handleTime"] +  "</span>" +
                                "<p class='memoInfo'>回复内容 ： "+ approvalProcess[i]["approveMemo"]  +"</p>" +
                                "</div>" ;
                            str += "<div class='ty-process-item ty-process-no '>" +
                                "<p><span class='dot-no'></span>处理人："+ approvalProcess[i]["toUserName"] +"/"+ approvalProcess[i]["userName"] +" </p>" +
                                "<p>"+ approvalProcess[i]["handleTime"] +"</p>" +
                                "<p style='color:#666;'>"+ approvalProcess[i]["approveMemo"] +"</p>" +
                                "</div>" ;
                            break ;
                        case 1 :  // 待处理
                        case 4 :  // 待两讫
                            str += "<div class='ty-process-item ty-process-wait'>" +
                                "<p><span class='dot-wait'></span>处理人："+ approvalProcess[i]["toUserName"] +"/"+ approvalProcess[i]["userName"] +" </p>" +
                                "<p>"+ approvalProcess[i]["handleTime"] +"</p>" +
                                "</div>" ;
                            break ;
                        default:
                            break;
                    }

                    $("#financeProcess").html(str);

                }

            }
            // 展示图片
            var imgs = data["personnelReimbursetAttachment"] ;
            if( imgs && imgs.length > 0 ){
                var str = "" ;
                for(var j = 0 ; j < imgs.length ; j++ ){
                    var path = imgs[j]["path"] ;
                    str += "<img class='litPic' onmouseenter='imgEnter($(this))' onmouseout='imgOut($(this))' src='../" + path+ " '/>" ;
                }
                str += "<div class='clr'></div>" ;
                $("#pic").html( str );
            }
        },
        error:function(){  } ,
        complete:function(){ loading.close() ;  }
    });
}
// 获取 入库申请详情（仓库修正)
function getStorageAcceptDetail( applyID , state ){
    if(state === 1){
        var footerStr = '<span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>' +
                        '<span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" onclick="sureStorageChange();">确认</span>'
    }else{
        var footerStr = '<span class="ty-btn ty-btn-big ty-circle-5" onclick="bounce.cancel()">取消</span>' ;
    }
    $("#storageAcceptDetail .bonceFoot").html(footerStr);
    bounce.show( $("#storageAcceptDetail") ) ;  // 显示详情处理页面
    $.ajax({
        url:"../message/getMessageDetail.do",
        data:{  "userMessageId": applyID  },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function( data ){
            loading.close() ;
            var status = data["status"];
            if(status === 0){
                $("#tip #tipMs").html("本消息已被发出者修改，请查看最新消息!") ;
                bounce.show($("#tip")) ;
            }else{
                var mtStock = data["mtStock"];
                var detailStr = "";
                detailStr +=    '<div class="storageAcceptDetail" id="'+mtStock.id+'" mid="'+data["userMessageId"]+'">'+
                                    '<p class="procon"><span>申请时间：</span><span>'+data["createTime"]+'</p>'+
                                    '<p class="procon"><span>商品代号：</span><span>'+mtStock.outerSn+'</p>'+
                                    '<p class="procon"><span>商品名称：</span><span>'+mtStock.outerName+'</p>'+
                                    '<p class="procon"><span>产品图号：</span><span>'+mtStock.innerSn+'</p>'+
                                    '<p class="procon"><span>产品名称：</span><span>'+mtStock.innerSnName+'</p>'+
                                    '<p class="procon"><span>单位：</span><span>'+mtStock.unit+'</p>'+
                                    '<p class="procon"><span>数量：</span><span>'+mtStock.inPlan+'</span><span class="ty-color-red"> → <span class="InFact">'+data["num"]+'</span></span></p>'+
                                    '<p class="procon"><span>生产日期：</span><span>'+ (new Date(mtStock.manufactureDate).format('yyyy-MM-dd')) +'</p>'+
                                    '<p class="procon"><span>产品到期日：</span><span>'+ (new Date(mtStock.invalidDate).format('yyyy-MM-dd')) +'</p>'+
                                    '<p class="procon"><span>修改记录：</span><span>'+data["record"]+'</p>' +
                                '</div>';
                $("#storageAcceptDetail .bonceCon").html(detailStr);
            }
        },
        error:function(){  } ,
        complete:function(){ loading.close() ;  }
    });
}

/* creator：张旭博，2017-08-29 10:28:28，确定修正 */
function sureStorageChange() {
    var ID              = $("#storageAcceptDetail .storageAcceptDetail").attr("id");
    var userMessageId   = $("#storageAcceptDetail .storageAcceptDetail").attr("mid");
    var InFact          = $("#storageAcceptDetail .storageAcceptDetail .InFact").text();
    $.ajax({
        url:"../mtStock/messageEnter.do",
        data:{
            "ID":ID,
            "InFact":InFact,
            "userMessageId": userMessageId
        },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ;  } ,
        success:function( data ){
            loading.close() ;
            var status = data["status"];
            if(status === 1){
                $("#tip #tipMs").html("成功入库!") ;
                bounce.show($("#tip")) ;
                $(".ty-secondTab li").eq(1).click();
            }else{
                $("#tip #tipMs").html("入库失败!") ;
                bounce.show($("#tip")) ;
            }
        },
        error:function(){  } ,
        complete:function(){ loading.close() ;  }
    });
}

/* creator：张旭博，2017-12-01 15:49:56，物流管理-》发货管理-》已出库-》查看-按钮  */
function outStorageOkBtn(pageNum,per,outId,messageId){
    var data = {
        "pageNum":pageNum,
        "per":per,
        "state":8,
        "outId":outId,
        "messageId":messageId
    }
    $.ajax({
        url:"../skl/deliveryList.do" ,
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function( data ){
            //设置分页
            // var mycurr      = data.currPage;
            // var ttlPage     = data.totalPage;

            var deliveryList    = data["data"];
            if(deliveryList !== undefined && deliveryList !== null) {
                bounce.show($("#outStorageOk"));
                var base = deliveryList[0];
                $("#outStorageOkBase .customerName").html(base.customer_name);
                $("#outStorageOkBase .customerCode").html(base.code);
                $("#outStorageOkBase .sn").html(base.sn);
                $("#outStorageOkBase .approveTime").html(formatTime( base.last_review_time , false )); //出库日期
                $("#outStorageOk .countAll").html('本次计划出库共 <span class="ty-color-blue">'+base.item_account+'</span> 种商品，共 <span class="ty-color-blue">'+base.pack_amount+'</span> 件');
                $("#outStorageOk .applyAll").html(
                    ' &nbsp;申请人： <span class="ty-color-blue">'+base.tpa_create_name+'</span> '+formatTime(base.tpa_create_date,false) +
                    ' &nbsp;仓库： <span class="ty-color-blue">'+base.approver_name+'</span> '+formatTime( base.last_review_time , false ) +
                    ' &nbsp;复核人： <span class="ty-color-blue">'+base.reviewer_name+'</span> '+formatTime(base.last_approve_time,false)

                );
                var deliveryItemData = base.dataList;
                var deliveryStr = '';
                for(var i=0;i<deliveryItemData.length;i++){
                    deliveryStr +=  '<tr>'+
                                    '<td>' + -(-i-1) + '</td>'+
                                    '<td>' + deliveryItemData[i].outer_sn + '</td>' +                           //商品代号
                                    '<td>' + deliveryItemData[i].outer_name + '</td>' +                         //商品名称
                                    '<td>' + deliveryItemData[i].inner_sn + '</td>' +                           //产品图号
                                    '<td>' + deliveryItemData[i].NAME + '</td>' +                               //产品名称
                                    '<td>' + deliveryItemData[i].unit + '</td>' +                               //单位
                                    '<td>' + deliveryItemData[i].out_plan + '</td>' +                  //出库数量
                                    '<td>' + deliveryItemData[i].pack_num + '</td>' +                           //货物件数
                                    '<td>' + formatTime( deliveryItemData[i].approve_time,false) + '</td>' +                         //仓库审批时间
                                    '<td>' + formatTime( deliveryItemData[i].review_time,false) + '</td>' +                         //复核时间
                                    '</tr>'
                }
                $("#outStorageOk .tblList tbody").html(deliveryStr);
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;

}

/* creator：张旭博，2017-12-04 09:40:03，物流管理-》发货管理-》已签收-》签收查看-按钮  */
function signCheckBtn(outid , userMessageId) {
    bounce.show($("#signCheck"))
    $.ajax({
        url:"../inOutStock/aTaoLook.do" ,
        data:{
            "outid":outid,
            "state":10,
            "userMessageId":userMessageId
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var base = data["data"];
            var deliveryItemData = base.list;
            $("#signCheckBase .customerName").html(base.customer_name);
            $("#signCheckBase .customerCode").html(base.customer_code);
            $("#signCheckBase .address").html(base.address);
            $("#signCheckBase .contact").html(base.telephone);
            $("#signCheckBase .mobile").html(base.consignee);
            $("#signCheckBase .sn").html(base.sn);
            $("#signCheckBase .create_date").html(formatTime( base.order_c , false ));
            $("#signCheckBase .update_date").html(formatTime( base.order_u , false ));
            $("#signCheckBase .deliveryDate").html(formatTime( base.delivery_date , false ));
            $("#signCheckBase .carrier").html(base.carrier);
            $("#signCheckBase .arriveDate").html(formatTime( base.arrive_date , false ));
            $("#signCheckBase .deliveryWay").html(base.delivery_way);

            $("#signInfoRecord .signDetail").html(base.sign_record);
            $("#signInfoRecord .signer").html(base.sginer);
            $("#signInfoRecord .signTime").html(formatTime(base.sign_time,false));
            $("#signInfoRecord .recorder").html(base.sign_recorder_name);
            $("#signInfoRecord .recordTime").html(formatTime(base.sign_recorder_time,false));
            $("#signCheckBase").attr("oid",base.outid);
            $("#signCheck .countAll").html('本次计划出库共 <span class="ty-color-blue">'+base.gsum+'</span> 种商品，共 <span class="ty-color-blue">'+base.pack+'</span> 件');
            $("#signCheck .applyAll").html(
                ' &nbsp;申请人： <span class="ty-color-blue">'+base.applicant_name+'</span> '+formatTime(base.apply_date,true) +
                ' &nbsp;仓库： <span class="ty-color-blue">'+base.approver_name+'</span> '+formatTime( base.approve_time , true ) +
                ' &nbsp;复核人： <span class="ty-color-blue">'+base.reviewer_name+'</span> '+formatTime(base.review_time,true)

            );

            var deliveryStr = '';
            for(var i=0;i<deliveryItemData.length;i++){
                deliveryStr +=  '<tr id="'+deliveryItemData[i].id+'" pack="'+deliveryItemData[i].pack+'">'+
                    '<td>' + deliveryItemData[i].outer_sn + '</td>' +                           //商品代号
                    '<td>' + deliveryItemData[i].outer_name + '</td>' +                         //商品名称
                    '<td>' + deliveryItemData[i].inner_sn + '</td>' +                           //产品图号
                    '<td>' + deliveryItemData[i].name + '</td>' +                               //产品名称
                    '<td>' + deliveryItemData[i].unit + '</td>' +                               //单位
                    '<td>' + formatTime( deliveryItemData[i].delivery_date , false ) + '</td>' +//要求到货日期
                    '<td>' + deliveryItemData[i].out_plan + '</td>' +                           //出库数量
                    '<td>' + deliveryItemData[i].pack + '</td>' +                           //货物件数
                    '<td>' + formatTime( deliveryItemData[i].approve_time,false) + '</td>' +    //仓库审批时间
                    '<td>' + formatTime( deliveryItemData[i].review_time,false) + '</td>' +    //复核时间
                    '<td class="change_amount">' + deliveryItemData[i].sign_amount + '</td>' +    //实际签收数量
                    '<td class="change_memo" style="display:none;">' + deliveryItemData[i].sign_record + '</td>' ;    //实际签收数量
                var sign_time = deliveryItemData[i].review_time;
                if(sign_time === null || sign_time === undefined || sign_time === ''){
                    deliveryStr +=  '<td></td><td></td></tr>'
                }else{
                    deliveryStr +=  '<td>' + formatTime( deliveryItemData[i].sgin_time,false) + '</td>' +    //录入时间
                        '<td>' +
                        '<span class="ty-color-blue" onclick="signDetailCheckBtn($(this))">查看</span>'+
                        '</td>'+
                        '</tr>';
                }
            }
            $("#signCheck .tblList tbody").html(deliveryStr);
        },
        error:function(){
            $("#tip #tipMs").html("系统错误，请重试!") ;
            bounce.show($("#tip")) ;
        } ,
        complete:function(){ loading.close() ;chargeClose(1) ;  }
    }) ;
}

/* creator：张旭博，2017-11-30 08:30:58，物流管理-》发货管理-》已签收-》已签收列表-》签收记录-》查看按钮 */
function signDetailCheckBtn(selector) {
    bounce_Fixed.show($("#signDetailCheck"));
    var sign_record = selector.parent().siblings(".change_memo").text();
    var sign_amount = selector.parent().siblings(".change_amount").text();
    $("#signDetailCheck .actualDeliveryNum").val(sign_amount);
    $("#signDetailCheck .memo").val(sign_record);
}

// 判别请假类型
function chargeType( type ){
    if( type == "1" ){  return "事假";  }
    if( type == "2" ){  return "病假";  }
    if( type == "3" ){  return "年假";  }
    if( type == "4" ){  return "调休";  }
    if( type == "5" ){  return "婚假";  }
    if( type == "6" ){  return "产假";  }
    if( type == "7" ){  return "陪产假";  }
    if( type == "8" ){  return "路途假";  }
    if( type == "9" ){  return "其他";  }
    return "未识别" ;
}
// 判别请假类型
function chargeTypeOverTime( type ){
    if( type == "1" ){  return "工作日加班";  }
    if( type == "2" ){  return "周末假日加班";  }
    if( type == "3" ){  return "法定节假日加班";  }

    return "未识别" ;
}
// 直系审批人查看 - 关闭
function closeBounce(){
    editTr.remove(); bounce.cancel() ;
}
// 判别票据月份
function chargeBillDate( val ){
    if( Number(val) == 1 ){ return "本月票据" ;  }
    if( Number(val) == 2 ){ return "非本月票据" ;  }
    return "" ;
}
// 鼠标滑进。图片放大
function imgEnter( obj ){
    var path = obj.attr("src") ;
    $("#bigPic").attr( "src" , path ).show() ;
}
// 鼠标划出 ， 图片消失
function imgOut( obj ){
    $("#bigPic").hide() ;
}
/* creator：张旭博，2017-06-27 20:42:45，比较源数据和修改后的数据，相同显示旧数据，不同显示红箭头和新数据 */
function compareNO(o,n){
    if($.trim(o)==$.trim(n)){
        return '';
    }else{
        return '<span class="ty-color-red colorChange"> → '+n+'</span>';
    }
}