/* creator:hxz 2018-06-04 项目管理-综合管理页js */
var fromMessage = { "isFromMessage":false , "proTyp":"" , "proId" :"" } ; // 是否来来自消息页面
$(function () {
    // creator:hxz 2018-06-04 切换展示主页面各状态列表
    $(".ty-secondTab li").click(function () {
        var _index = $(this).index() ;
        searchPro["state"] = _index+1 ;
        goPage(0); // 跳回主页面
        if(_index < 5){
            $(this).addClass("ty-active").siblings().removeClass("ty-active") ;
            $(".ty-secondTab").show(); $(".minControl").show();
            getList();
        }else{ // 显示结案成功
            $(".ty-secondTab").hide(); $(".minControl").hide();
            getList2( 1 )
        }
    }) ;
    // creator:hxz 2018-06-04 分工设置新增的人员列表点击效果
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right") ;
            $(this).find("i").eq(0).addClass("fa-angle-down")
            $(this).next().show();
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down") ;
            $(this).find("i").eq(0).addClass("fa-angle-right") ;
            $(this).next().hide();
        }

    });
    // creator : hxz 2018-06-06 结案完成列表的各种状态查询
    $("#changeState>span").click(function(event){
        $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue") ;
        var _index = $(this).index() ;
        switch (_index){
            case 0:
            case 1:
            case 2:
                var type = _index+1 ;
                searchPro = { "type" :type , "beginDate":"" , "endDate":""} ;
                getList2( 1 ) ;
                break ;
            case 3:
                stop(event) ;
                $(".searchCon").toggle();
                break ;
            default :
                layer.msg("没有匹配项！"); return false ;
        }
    });
    // 组织时间选择框冒泡
    $("body").on("click", "#laydate_box", function (event) {
        event.stopPropagation();
        $('.searchCon').show();
    });
    // 初始化
    var proTyp = getUrlParam("type") ; // type  1-立案待审批 ， 2-结案待审批
    var proId = getUrlParam("proId") ;
    if(proTyp&&proId){
        fromMessage = { "isFromMessage":true , "proTyp":proTyp , "proId" :proId} ;
        if(proTyp == 1){
            searchPro["state"] = 1 ;
        }else{
            searchPro["state"] = 4 ;
        }
        scanData(proId) ;
    }else{
        $(".ty-secondTab").children("li:eq(0)").click();
    }

});
// creator : hxz 阻止事件冒泡
function stop(e) {
    e.stopPropagation() ;
}
// creator:hxz 2018-06-05  自定义结案查询
function searchDIY() {
    var searchStart = $("#searchStart").val();
    var searchEnd = $("#searchEnd").val();
    searchPro = { "type" :4 , "beginDate": (searchStart) , "endDate": (searchEnd)} ;
    getList2( 1 ) ;$(".searchCon").hide();
}
// creator:hxz 2018-06-05  获取主列表工具方法
function getList() {
    var state = searchPro["state"] ;
    $.ajax({
        "url" : "../project/projectBaseList.do" ,
        "data" : { "state" : state } ,
        success:function (res) {
            var list = res ,  str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    if(state<=3){ // 立案部分
                        var beginTime = (new Date(list[i]["beginTime"]).format('yyyy'))  == "1970" ? "" : (new Date(list[i]["beginTime"]).format('yyyy-MM-dd')) ;
                        var endTime = (new Date(list[i]["endTime"]).format('yyyy'))  == "1970" ? "" : (new Date(list[i]["endTime"]).format('yyyy-MM-dd')) ;
                        str += "<tr>" +
                            "<td>"+ list[i]["projectName"] +"</td>" +
                            "<td>"+ list[i]["code"] +"</td>" +
                            "<td>"+ list[i]["category"] +"</td>" +
                            "<td>"+ list[i]["tag"] +"</td>" +
                            "<td>"+ list[i]["principalName"] +"</td>" +
                            "<td>" + beginTime + "</td>" +
                            "<td>" + endTime + "</td>" +
                            "<td>" + list[i]["createName"] + "</td>" +
                            "<td>" +
                            "<span class=\"ty-color-blue\" onclick='scan($(this))'>查看</span>" +
                            "<span class=\"ty-color-blue\" onclick=\"change($(this))\">更换立案者</span>" +
                            "<span class=\"hd\">"+ JSON.stringify(list[i]) +"</span>" +
                            "</td></tr>" ;
                    }else{ // 结案部分
                        str += "<tr>" +
                            "<td>"+ list[i]["projectName"] +"</td>" +
                            "<td>"+ list[i]["code"] +"</td>" +
                            "<td>"+ list[i]["category"] +"</td>" +
                            "<td>"+ list[i]["tag"] +"</td>" +
                            "<td>"+ list[i]["principalName"] +"</td>" +
                            "<td>" + (new Date(list[i]["settleTimeFact"]).format('yyyy-MM-dd')) + "</td>" +
                            "<td>"+ list[i]["settleName"] +"</td>" +
                            "<td>" +
                            "<span class=\"ty-color-blue\" onclick='scan($(this))'>查看</span>" +
                            "<span class=\"ty-color-blue\" onclick=\"change($(this))\">更换立案者</span>" +
                            "<span class=\"hd\">"+ JSON.stringify(list[i]) +"</span>" +
                            "</td>" +
                            "</tr>" ;
                    }
                }
            }
            if(state<=3){ // 立案部分
                $("#end").hide() ; $("#build").show(); $("#buildTbl").html(str) ;
            }else{ // 结案部分
                $("#end").show() ; $("#build").hide(); $("#endPro").html(str) ;
            }
        }
    }) ;
}
// creator:hxz 2018-06-05  获取主列表工具方法 - 获取结案的
var searchPro = { "type" :1 , "beginDate":"" , "endDate":""} ; // 标记结案的查询参数
function getList2( cur ) {
    // type 1- 本年 2-去年 3-前年 4-自定义，beginDate，endDate ，pageSize每页条数，currentPageNo 当前页数
    var type = searchPro["type"] ;
    var beginDate = searchPro["beginDate"] ;
    var endDate = searchPro["endDate"] ;
    $.ajax({
        "url" : "../project/projectSettleList.do" ,
        "data" : { "type" : type , "pageSize":20 , "currentPageNo": cur , "beginDate": beginDate , "endDate" : endDate } ,
        success:function (res) {
            var pageInfo = res["pageInfo"];
            $("#num").html(pageInfo["totalResult"]) ;
            var totalPage = pageInfo["totalPage"];
            setPage($("#ye") , cur , totalPage , "proList2" , "{}" ) ;
            var list = res["projectBaseList"] ,  str = "" , duringStr = "" ;
            var dt = new Date() ;
            var y = dt.getFullYear(),m = dt.getMonth()+1 , d = dt.getDate() ;
            if(m<10){ m = "0" + m ;  }
            if(d<10){ d = "0" + d ;  }
            if(type == 1){  duringStr = "自" + y + "-01-31" + "至" + y + "-" + m + "-" + d ;  }
            else if(type == 2){ duringStr = "自" + (y-1) +"-01-31 至" + (y-1) + "-12-31 " ; }
            else if(type == 3){ duringStr = "自" + (y-2) +"-01-31 至" + (y-2) + "-12-31 " ; }
            else if(type == 4){
                // var beginDateStr = forMat(beginDate) , endDate = forMat(endDate) ;
                duringStr = "自 "+ beginDate +" 至 "+ endDate +" ，" ;
                if (beginDate == endDate) {
                    duringStr = beginDate + "日当天";
                }
            }
            $("#during").html(duringStr) ;
            $("#end").show() ; $("#build").hide(); $(".endSuccess").show();
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    str += "<tr>" +
                                "<td>"+ list[i]["projectName"] +"</td>" +
                                "<td>"+ list[i]["code"] +"</td>" +
                                "<td>"+ list[i]["category"] +"</td>" +
                                "<td>"+ list[i]["tag"] +"</td>" +
                                "<td>"+ list[i]["principalName"] +"</td>" +
                        "<td>" + (new Date(list[i]["settleTimeFact"]).format('yyyy-MM-dd') ) + "</td>" +
                                "<td>"+ list[i]["settleName"] +"</td>" +
                                "<td>" +
                                "<span class=\"ty-color-blue\" onclick='scan($(this))'>查看</span>" +
                                "<span class=\"hd\">"+ JSON.stringify(list[i]) +"</span>" +
                                "</td>" +
                            "</tr>" ;
                }
            }
            $("#endPro").html(str) ;
        }
    }) ;
}
// creator:hxz 2018-06-04  查看立案、结案详情
function scanData(id){
    $.ajax({
        "url" : "../project/getProjectBaseInfo.do" ,
        "data" : { "id" : id } ,
        success:function(res){
            var status = res["status"];
            // var state = searchPro["state"] ;
            if(status == 1){
                var project = res["project"];
                var state = Number(project["state"]); // 状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
                goPage(2) ;
                $("#flowList").hide().next().hide();
                // 立案待审批，审批按钮
                if(state == 1){   $(".endProLiCon").show() ;  }else{  $(".endProLiCon").hide() ;  }
                // 立案通过，显示结案按钮
                if (state == 3 || state == 5) {
                    $(".endProCon").show();
                } else {
                    $(".endProCon").hide();
                }
                var settleTimeFact = project["settleTimeFact"]; // 实际结案时间
                var settleOpinion = project["settleOpinion"]; // 结案意见
                if (settleTimeFact && settleOpinion) {
                    $(".endProInfo").show();
                    $("#scan_time").html( new Date(settleTimeFact).format('yyyy-MM-dd') );
                    $("#scan_Opinion").html(settleOpinion);
                } else {
                    $(".endProInfo").hide();
                }
                // 结案待审批，显示结案审批
                if (state == 4) {
                    $(".endProApplyCon").show();
                } else {
                    $(".endProApplyCon").hide();
                }

                $("#scan_proName").html(project["projectName"]);      // 项目名称
                $("#scan_proKind").html(project["category"]);          // 类别
                $("#scan_proTip").html(project["tag"]);                // 标签
                $("#scan_proNo").html(project["code"]);                // 项目编号
                $("#scan_proBy").html(project["foundation"]);            // 立项依据
                $("#scan_proFu").html(project["principalName"]);          // 项目负责人
                $("#scan_users").html(project["member"]);                 // 小组成员
                $("#scan_curShow").html(project["currentSituation"]);     // 现状描述
                $("#scan_purpose").html(project["proposal"]);            // 立项目的
                $("#scan_abstract").html(project["description"]);          // 项目简介
                $("#scan_proBeginDate").html( (new Date(project["beginTime"]).format('yyyy')) == "1970" ? "" :  (new Date(project["beginTime"]).format('yyyy-MM-dd'))  );
                $("#scan_proEndDate").html( (new Date(project["endTime"]).format('yyyy')) == "1970" ? "" :  (new Date(project["endTime"]).format('yyyy-MM-dd'))  );
                $("#scan_outLook").html(project["outlook"]);               // 相关展望
                $("#scan_outResource").html(project["archive"]);          // 输出资料
                $("#scan_memo").html(project["memo"]);                      // 备注
                // 处理附件
                var pathList = project["projectAttachmentHashSet"] ;  // 图片路径（应该是一堆）
                var imgStr = "" ;
                if(pathList && pathList.length >0){
                    for(var i=0 ; i <pathList.length ; i++){
                        var fileType = pathList[i]["path"].split(".");
                        fileType = fileType[fileType.length - 1];
                        var typeStr = "" ;
                        switch (fileType){
                            case "doc" :
                            case "docx" : typeStr = "ty-file_docx"  ;
                                break ;
                            case "xls" :
                            case "xlsx" : typeStr = "ty-file_xls"  ;
                                break ;
                            case "zip" :
                            case "rar" : typeStr = "ty-file_rar"  ;
                                break ;
                            case "png":
                            case "jpg": typeStr = "ty-file_png"  ;
                                break ;
                            case "ppt" : typeStr = "ty-file_ppt"  ;
                                break ;
                            default :
                        }
                        imgStr += "<div onmouseout='hideClose($(this))' onmouseover='showControl($(this))' title='" + pathList[i]["title"] + "' class=\" ty-left ty-fileType " + typeStr + "\">" +
                            "<a></a>" +
                            "<a class='control ' path='" + pathList[i]["path"] + "' onclick='seeOnline($(this)) '>预览</a> " +
                            "<a class='control' download='' path='" + pathList[i]["path"] + "'  onclick='downLoad($(this))'>下载</a> " +
                            "<span  class='fName'>" + pathList[i]["title"] + "</span> " +
                            "<div class='hd'>" + pathList[i]["path"] + "</div>" +
                            "</div>";
                    }
                }
                $("#scan_accessory").html(imgStr);
                // 处理审批流程
                var processList = res["approvalProcessList"];
                var strFlow = "";
                if(processList && processList.length >0){
                    for (var i = 0; i < processList.length; i++) {
                        var approveStatus = processList[i]["approveStatus"]; // approveStatus;1 - 提交 2-批准，3-驳回
                        if (approveStatus == 2 || approveStatus == 3) {
                            var type_ = processList[i]["type"];// type =1 立案 type=2 是结案
                            var flowTypeStr = type_ == 1 ? "立案" : "结案";
                            var applyer = processList[i]["askName"];
                            var applyTime = (new Date(processList[i]["createDate"]).format('yyyy-MM-dd hh:mm')) ;
                            var chargeTime = (new Date(processList[i]["handleTime"]).format('yyyy-MM-dd hh:mm')) ;
                            var charger = processList[i]["userName"];
                            var statusKlass = approveStatus == 2 ? "dot" : "dot-no";
                            var statusStr = approveStatus == 2 ? "批准" : "驳回";
                            var resonStr = "";
                            if (approveStatus == 3) {
                                resonStr = "<p><span>驳回理由</span> <i>" + processList[i]["reason"] + "</i></p>";
                        }
                            strFlow += "<div class=\"ty-process-item\">" +
                                "    <p><span class=\"" + statusKlass + "\"></span>" + flowTypeStr + "已" + statusStr + "</p>" +
                                "    <article>" + resonStr +
                                "        <p><span>" + flowTypeStr + statusStr + "者</span> <span>" + charger + "</span> <span>" + chargeTime + "</span></p>" +
                                "        <p><span>" + flowTypeStr + "申请者</span> <span>" + applyer + "</span> <span>" + applyTime + "</span></p>" +
                                "    </article>" +
                                "</div>";
                        }
                    }
                }
                $("#flowList").html(strFlow);
                if (strFlow == "") { // 没有流程，不显示
                    $(".chargeCon").hide();
                }
                $(".chargeCon").children(":eq(0)").show().siblings().hide();
            }else{
                layer.msg("获取详情失败") ;
            }
        }
    })
}
function scan(_thisObj){
    editTr = _thisObj.parents("tr") ;
    var info = _thisObj.siblings(".hd").html(); info = JSON.parse(info) ;
    var id = info["id"] ;
    scanData(id) ;
}
var timerSetendProOK = null; // 结案非空监听定时器
// creator:hxz 2018-06-04  结案 - 判断结案确认按钮能不能点
function setendProOK() {
    var dat = $("#endProDate").val();
    var prop = $("#endProComment").val();
    if ($.trim(dat) && $.trim(prop)) {
        $("#endProOK").removeClass("ty-btn-gray").addClass("ty-btn-red").attr("onclick", "endProOK()");
    } else {
        $("#endProOK").addClass("ty-btn-gray").removeClass("ty-btn-red").removeAttr("onclick");
    }
    chargeInput($("#endProContent"));
}
// creator:hxz 2018-06-04  结案
function endPro(type) { // type :  0 - 结案申请驳回, 1 - 结案申请批准 ，2 - 核心人物结案
    switch (type){
        case 0 :
            bounce.show($("#endProApply"));
            $("#endProApplyReason").val("");
            timerSetendProOK = setInterval(function () {
                chargeInput($("#endProApply"));
            }, 300);
            break ;
        case 1 :
            var id = "" ;
            if(editTr){
                var info = editTr.children(":last").children(".hd").html(); info = JSON.parse(info) ;
                id = info["id"] ;
            }else{
                id = fromMessage["proId"]
            }
            approvalProjectSettle(id , 1 , "") ;
            break ;
        case 2 :
            $("#endProOK").addClass("ty-btn-gray").removeClass("ty-btn-red").removeAttr("onclick");
            $("#endProDate").val("");
            $("#endProComment").val("");
            timerSetendProOK = setInterval(function () {
                setendProOK();
            }, 300);
            bounce.show($("#endProContent")); $("#endProDate").val("");$("#endProComment").val("");
            break ;
        default :
    }
}
// creator:hxz 2018-06-04  审批结案申请 - 确定驳回
function endProOk() {
    var id = "" ;
    if(editTr){
        var info = editTr.children(":last").children(".hd").html(); info = JSON.parse(info) ;
        id = info["id"] ;
    }else{
        id = fromMessage["proId"];
    }
    var endProApplyReason = $("#endProApplyReason").val();
    if (testStrNum(endProApplyReason, 200)) {
        approvalProjectSettle(id , 0 , endProApplyReason) ;
    }
}
// creator : hxz 2018-06-12 工具方法用于判断字数有没有超限
function testStrNum( str ,limit ){
    var testOk = false ;
    if(str.length <= limit){
        testOk = true ;
    }
    return testOk ;
}

// creator:hxz 2018-06-04  工具方法 --  审批结案申请
function approvalProjectSettle(id , approvalStatus , settleRejectReason) {
    // ,approvalStatus 1-批准 0-驳回， settleRejectReason 结案驳回原因
    $.ajax({
        "url" : "../project/approvalProjectSettle.do" ,
        "data" : {  "id" : id , "approvalStatus": approvalStatus , "settleRejectReason": settleRejectReason  } ,
        success:function (res) {
            var status = res["status"] ;
            if(status == 1){
                layer.msg("审批成功！") ; bounce.cancel();
                if (fromMessage["isFromMessage"]) { // 从消息跳转过来的
                    $(".ty-secondTab li:eq(3)").click();
                } else {
                    $(".ty-secondTab li.ty-active").click();
                }
            }else{
                layer.msg("审批失败！");
            }
        }
    })
}
// creator:hxz 2018-06-04  核心人物- 确定结案
function endProOK() {
    var id = "" ;
    if(editTr){
        var info = editTr.children(":last").children(".hd").html(); info = JSON.parse(info) ;
        id = info["id"] ;
    }else{
        id = fromMessage["proId"]
    }
    var settleOpinion = $("#endProComment").val() ;
    var settleTimeFact = $("#endProDate").val()  ;
    if($.trim(settleTimeFact) == "" || $.trim(settleOpinion) == "" ){
        layer.msg("请把结案时间和结案意见补充完成");  return false;
    }
    if(!(testStrNum(settleOpinion , 150))){
        layer.msg("结案意见 不能超过150字！");
        return false ;
    }
    clearInterval(timerSetendProOK);
    $.ajax({
        "url" : "/project/projectSettle.do" ,
        "data" : { "id": id , "settleOpinion":settleOpinion , "settleTimeFact": new Date(settleTimeFact) } ,
        success:function (res) {
            var status = res["status"] ;
            if(status == 1){
                layer.msg("结案成功") ; bounce.cancel() ;
                editTr&&editTr.remove();
                $(".ty-secondTab").children("li:eq(2)").click();
            }else{
                layer.msg("结案失败") ;
            }
        }
    })
}
// creator:hxz 2018-06-04  审批立案申请
function liPro(type) { // 0 - 驳回 ； 1 - 批准
    var id = "" ;
    if(editTr){
        var info = editTr.children(":last").children(".hd").html(); info = JSON.parse(info) ;
        id = info["id"] ;
    }else{
        id = fromMessage["proId"]
    }
    if(type == 0){
       bounce.show( $("#registerPro") );
       $("#registerRejectReason").val("");
    }else{
        charLiPro(id , 1 , "") ;
    }
}
// creator:hxz 2018-06-04  审批立案申请 - 驳回-确定
function liProOk() {
    var id = "" ;
    if(editTr){
        var info = editTr.children(":last").children(".hd").html(); info = JSON.parse(info) ;
        id = info["id"] ;
    }else{
        id = fromMessage["proId"]
    }
    var registerRejectReason = $("#registerRejectReason").val();
    charLiPro(id , 0 , registerRejectReason)
}

// creator:hxz 2018-06-04  工具方法 -- 审批立案申请
function charLiPro(id , type , registerRejectReason){
    $.ajax({
        "url" : "../project/approvalProjectCase.do" ,
        "data" : {  "id" : id , "approvalStatus" : type , "registerRejectReason": registerRejectReason  } ,
        success:function (res) {
            var status = res["status"] ;
            bounce.cancel() ;
            if(status == 1){
                layer.msg("审批成功") ;
                $(".ty-secondTab").children("li:eq(0)").click();

            }else{
                layer.msg("审批失败") ;
            }
        }
    })
}
// creator:hxz 2018-06-04  更换立案者
var editTr = null ;
function change(_thisObj){
    editTr = _thisObj.parents("tr") ;
    bounce.show($("#change")) ;
    $("#peoName").html(editTr.children(":eq(0)").html()) ;
    getlabourList(1) ;
}
// creator:hxz 2018-06-04  更换立案者 - 确定
function changeOk() {
    var info = $("#peoLi").val();
    if (info == 0) {
        layer.msg("请选择立案者");
        return false;
    }
    info = JSON.parse(info);
    var userId = info["userID"];
    var userName = info["userName"];
    bounce.cancel() ;
    var pro = editTr.children(":last").children(".hd").html();  pro = JSON.parse(pro) ;
    var id = pro["id"] ;
    $.ajax({
       "url" : "../project/updateInitialReceipter.do" ,
       "data" : { "userId":userId , "id":id } ,
        success:function (res) {
            var status = res["status"];
            if( status == 1 ){
                layer.msg("变更成功") ;
            }else{
                layer.msg("变更失败") ;
            }
        }
    });
}
// creator:hxz 2018-06-04  跳转分工设置页面
function goPage(type , state) { // type: 0 - 主页面 ； 1 - 分工设置 ；  2 - 连、结案详情页 ； 3 - 新增项目 ;
    $("#add").show();
    switch (type){
        case 0 : // state : 1 - 从结案成功跳回主页 , 2 - 从详情页跳转主页面
            $(".proMain").show(); $(".labourSet").hide(); $(".proScan").hide(); $(".addPro").hide() ;
            if(state == 1){
                $(".ty-secondTab li.ty-active").click(); $(".endSuccess").hide() ; $(".minControl").show() ;
            }else if(state == 2){
                var sta = searchPro["state"] ;
                if (fromMessage["isFromMessage"]) {
                    $(".ty-secondTab li:eq(" + (sta - 1) + ")").click();
                } else {
                    switch (sta) {
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                            $(".endSuccess").hide();
                            $(".minControl").show();
                            break;
                        case 6:
                            $(".minControl").hide();
                            $(".ty-secondTab").hide();
                            $("#add").hide();
                            $(".endSuccess").show();

                            break;
                    }
                }

            }else{
                $(".endSuccess").hide() ; $(".minControl").show() ;
            }
            if (timer_addPro) {
                clearInterval(timer_addPro);
            }
            break ;
        case 1:
            $(".proMain").hide(); $(".labourSet").show(); $(".proScan").hide(); $(".addPro").hide() ;$(".endSuccess").hide() ;
            if (timer_addPro) {
                clearInterval(timer_addPro);
            }
            getlabourList() ;
            break ;
        case 2:
            $(".proMain").hide(); $(".labourSet").hide(); $(".proScan").show(); $(".addPro").hide() ;$(".endSuccess").hide() ;
            if (timer_addPro) {
                clearInterval(timer_addPro);
            }
            break ;
        case 3:
            $(".proMain").hide(); $(".labourSet").hide(); $(".proScan").hide(); $(".addPro").show() ;$(".endSuccess").hide() ;
            addPro();
            break ;
        default: break ;
    }
}
// creator:hxz 2018-06-04  工具方法 - 获取已设置的分工人员列表
function getlabourList(type) { // type: 1 - 用于变更项目立案者 ， 其他 - 分工设置页面
    $.ajax({
        "url" : "../project/getFilingUsers.do" ,
        "data" : { "type" :6 } ,
        success:function (res) {
            var status = res["status"] ;
            if(status != 1){
                layer.msg("获取项目管理核心人物失败！");
                return false ;
            }
            var liNum = 0 ;
            var list = res["codeUsers"] , str = "";
            if (type == 1) {
                str = '<option value="0">请选择</option>';
            }
            if(list && list.length >0){
                liNum = list.length ;
                for(var i = 0 ; i < list.length ; i++){
                    if(type == 1){
                        str += '<option value=\'{ "userID" : "'+  list[i]["userID"] +'" , "userName": "'+ list[i]["userName"] +'" }\'>'+ list[i]["userName"] +'</option>' ;
                    }else{
                        str += "<tr><td>"+ list[i]["userName"] +"</td>" +
                            "<td>"+ chargeGender( list[i]["gender"] ) +"</td>" +
                            "<td>"+ list[i]["mobile"] +"</td>" +
                            "<td>"+ list[i]["departName"] +"</td>" +
                            "<td>"+ list[i]["postName"] +"</td>" +
                            "<td>" +
                                "<span class=\"ty-color-red\" onclick='deleteLi($(this))'>删除</span>" +
                                "<span class=\"hd\">"+ JSON.stringify(list[i]) +"</span>" +
                            "</td></tr>" ;
                    }

                }
            }
            if(type == 1){
                $("#peoLi").html(str) ;
            }else{
                $("#labourSetTbl").html(str) ;
                $("#liNum").html(liNum) ;
            }
        }
    }) ;
}
// creator:hxz 2018-06-04  分工设置 - 删除
function deleteLi(obj) {
    editTr = obj.parents("tr") ;
    var name = editTr.children(":eq(0)").html();
    var info = obj.siblings(".hd").html();  
    bounce.show($("#tip")) ; $("#tipMs").html("确定删除立案者："+ name ) ; 
    $("#tipID").html(info);
}
// creator:hxz 2018-06-04  分工设置 - 确定删除
function deleteLiOK() {
    var info = $("#tipID").html(); info = JSON.parse(info) ;
    var id = info["userID"];
    $.ajax({
        "url":"../coreSetting/deleteSpecialUser.do" ,
        "data" : { "coreCode":"projectFiling" , "userId": id } ,
        success:function (res) {
            bounce.cancel() ;
            var status = res["status"] ;
            if(status == 1){
                layer.msg("删除立案者成功") ;
                editTr.remove() ;
            }else{
                layer.msg("删除立案者失败") ;
            }
        }
    })
}
// creator:hxz 2018-06-04  分工设置 - 新增
function addLabourSetBtn() {
    bounce.show($("#addLabourSet")) ;
    // 获取全部人员列表
    $.ajax({
        "url" : "../coreSetting/getOrgListUserList.do" ,
        "data" : { "coreCode" :"projectFiling" } ,
        success:function (data) {
            var list = data["data"];
            var departmentStr = '';
            departmentStr += '<ul class="level1" level="1">' + getNextLevelList(list)+ '</ul>';
            $(".ty-colFileTree").html(departmentStr);
        }
    })
}
// creator:hxz 2018-06-06 分工设置选人- 工具方法 - 拼接字符串
function getNextLevelList(list) {
    var departmentStr = '';
    for(var i=0;i<list.length;i++){
        var subList = list[i].subList;
        var userList = list[i].userList;
        if(subList.length === 0 && userList.length === 0){
            departmentStr +=    '<li>' +
                '<div class="ty-treeItem" id='+ list[i]["id"] +'>' +
                '<i class="ty-fa"></i>' +
                '<i class="fa fa-folder"></i>' +
                '<span>'+list[i]["name"]+'</span>' +
                '</div>';
        }else if(subList.length > 0 ||userList.length > 0){
            departmentStr +=    '<li>' +
                '<div class="ty-treeItem" id='+ list[i]["id"] +'>' +
                '<i class="fa fa-angle-right"></i>' +
                '<i class="fa fa-folder"></i>' +
                '<span>'+list[i]["name"]+'</span>' +
                '</div>' +
                '<ul>';

        }
        if(subList.length > 0){
            departmentStr +=        getNextLevelList(subList);
        }
        if(userList.length > 0){
            for(var j in userList){
                departmentStr +=    '<li>' +
                    '<div class="ty-treeItem" id='+ userList[j]["userID"] +'>' +
                    '<i class="ty-fa"></i>' +
                    '<i class="fa fa-file"></i>' +
                    '<span>'+userList[j]["userName"]+'</span>' +
                    '</div>' +
                    '</li>';
            }
        }
        if(subList.length > 0 ||userList.length > 0){
            departmentStr += '</ul></li>';
        }else {
            departmentStr += '</li>';
        }

    }
    return departmentStr;
}
// creator:hxz 2018-06-04  分工设置选人- 新增确定
function labourSetOk(){
    // 新增分工设置成功
    var userId = $("#addLabourSet .ty-treeItemActive").attr("id");
    var newCoreName = $("#addLabourSet .ty-treeItemActive>span").html();
    if (userId == undefined) {
        layer.msg("请选择立案人员！");
        return false;
    }
    $.ajax({
       "url" : "../coreSetting/addManyUsers.do" ,
       "data" : { "coreCode": "projectFiling" ,"userId": Number(userId) } ,
       success:function (res) {
           var status = res["status"] ;
           if(status ==1){
               layer.msg("新增项目立案人成功") ;
               getlabourList() ;
               bounce.cancel() ;
           }else{
               layer.msg("新增项目立案人失败") ;
           }
       }
    });
}
// creator:hxz 2018-06-04  新增项目 - 判断新增按钮可以点击
function setaddProOk() {
    var add_proName = $("#add_proName").val();
    if ($.trim(add_proName)) {
        $(".addProOk").addClass("ty-btn-green").removeClass("ty-btn-gray").attr("onclick", "addProOk()");
    } else {
        $(".addProOk").removeClass("ty-btn-green").addClass("ty-btn-gray").removeAttr("onclick");
    }
}
var timer_addPro = null; // 新增项目字数监听器
// creator:hxz 2018-06-04  新增项目
function addPro(){
    $("#addproCon").find("input").val("");
    $("#addproCon").find("textarea").val("");
    $(".addProOk").removeClass("ty-btn-green").addClass("ty-btn-gray").removeAttr("onclick");
    $("#add_fu").html("");
    $("#fileUpload").html("");
    //初始化文件上传插件
    $('#fileUpload').Huploadify({
        auto:true ,
        fileTypeExts:'*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.png;*.jpg;*.wps;*.et;*.md;',
        multi:true,
        formData:{},
        fileSizeLimit:40960,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        uploader:"../project/uploadFiles.do",
        onUploadStart:function(){},
        onInit:function(){},
        onSelect:function (file) {
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,pathArr){
            var num = $("#add_fu").find(".ty-fileType").length;
            if (num >= 9) {
                layer.msg("附件最多只能上传9个！") ;
                $("#add_fu").children(".ty-fileType:gt(8)").remove();
                return false ;
            }
            var fileType = file.name.split(".");
            fileType = fileType[fileType.length-1];
            var typeStr = " ty-file_other " ;
            var strYuLan = "<a class='control ' path='" + path + "' onclick='seeOnline($(this))($(this))'>预览</a> ";
            switch (fileType){
                case "doc" :
                case "docx" : typeStr = "ty-file_docx"  ;
                    break ;
                case "xls" :
                case "xlsx" : typeStr = "ty-file_xls"  ;
                    break ;
                case "zip" :
                case "rar" : typeStr = "ty-file_rar"  ;
                    break ;
                case "png":
                case "jpg": typeStr = "ty-file_png"  ;
                    break ;
                case "ppt" : typeStr = "ty-file_ppt"  ;
                    break ;
                default :
                    strYuLan = "<i></i>";
            }
            pathArr = JSON.parse(pathArr);
            var pathList = pathArr["pathList"][0];
            var path = pathList["path"];
            var fileName = pathList["fileName"];
            var str = "<div onmouseout='hideClose($(this))' onmouseover='showControl($(this))' title='" + file.name + "' class=\"inline ty-fileType " + typeStr + "\">" +
                "<i onclick='deleFileOk($(this))' class='control fa fa-times-circle'></i> " +
                strYuLan +
                "<a download='' class='control' path='" + path + "'  onclick='downLoad($(this))'>下载</a> " +
                "<span  class='fName'>" + fileName + "</span> " +
                "<div class='hd'>" + path + "</div>" +
                    "</div>" ;
            $("#add_fu").append(str) ;
        } ,
        onCancel:function(file){

        }
    });
    timer_addPro = setInterval(function () {
        chargeInput($("#addproCon"));
    }, 50);
}
// creator: hxz 2018-06-15 判断当前输入框是否超字数
function chargeInput(containerObj) {
    var inputObj = containerObj.find("input");
    var textareaObj = containerObj.find("textarea");
    charge(inputObj);
    charge(textareaObj);
}
function charge(objArr) {
    objArr.each(function () {
        var text = $(this).val();
        var ttl = $(this).prev("span.ttl").text();
        var palceholder = $(this).attr("placeholder");
        if (palceholder) {
            var numer = palceholder.split("字")[0];
            if (text.length > Number(numer)) {
                text = text.substr(0, numer);
                $(this).val(text);
                layer.msg(ttl + "不能超过" + numer + "个字！");
            }
        }
    });
}
// creator:hxz 2018-06-07 新增项目 - 显示 操作附件按钮
function showControl(obj) {
    obj.find(".control").show();
}
// creator:hxz 2018-06-15 新增项目 - 附件下载
function downLoad(_this) {
    var path = _this.attr("path");
    var pathArr = path.split(".");
    var url = $.webRoot + '/' + path;
    var filename = _this.attr('download');
    var type = pathArr[pathArr.length - 1];
    if ($.inArray(type, ['png', 'jpg', 'jpeg', 'gif'])) {
        var broweer = myBrowser();
        if (broweer == 'IE') {
            downloadIEFile(filename, url);
            return false;
        }
    }
    _this.attr('target', '_blank').attr('href', url);
    return true;
}
// creator:hxz 2018-06-07 新增项目 - 删除附件
function deleFileOk(obj) {
    obj.parent(".ty-fileType").remove() ;
}
// creator:hxz 2018-06-07 新增项目 - 隐藏删除附件按钮
function hideClose(obj) {
    obj.find(".control").hide();
}
// creator:hxz 2018-06-05  新增项目 - 确定
function addProOk() {
    var projectName = $("#add_proName").val();      // 项目名称
    var category = $("#add_proKind").val();          // 类别
    var tag      = $("#add_proTip").val();                // 标签
    var code     = $("#add_proNo").val();                // 项目编号
    var foundation = $("#add_proBy").val();            // 立项依据
    var principalName = $("#add_proFu").val();          // 项目负责人 =======
    var member = $("#add_users").val();                 // 小组成员
    var currentSituation = $("#add_curShow").val();     // 现状描述
    var proposal = $("#add_purpose").val();            // 立项目的
    var description = $("#add_abstract").val();          // 项目简介
    var beginTime = $("#add_proBeginDate").val();       // （传date类型）预计开始时间
    var endTime = $("#add_proEndDate").val();            // endTime（date类型）预计结束时间
    var outlook = $("#add_outLook").val();               // 相关展望
    var archive = $("#add_outResource").val();          // 输出资料
    var memo = $("#add_memo").val();                      // 备注
    var pathList = [];  // 图片路径（应该是一堆）
    $("#add_fu").children(".ty-fileType").each(function () {
        var path = $(this).children(".hd").html();
        var fileName = $(this).children(".fName").html();
        pathList.push({"path": path, "fileName": fileName});
    });
    if(beginTime == ""){ beginTime = null; }
    if(endTime == ""){ endTime = null; }
    // 非空检验
    if( $.trim(projectName) == ""){
        layer.msg("项目名称不能为空！") ; return false ;
    }
    if(!(testStrNum(projectName , 20))){ layer.msg("项目名称最多不能超过20字！"); return false ; }
    if(!(testStrNum(category , 15))){ layer.msg(" 类别最多不能超过15字！"); return false ; }
    if(!(testStrNum(tag , 15))){ layer.msg("标签 最多不能超过15字！"); return false ; }
    if(!(testStrNum(code , 18))){ layer.msg("项目编号 最多不能超过18字！"); return false ; }
    if(!(testStrNum(foundation , 30))){ layer.msg("立项依据 最多不能超过30字！"); return false ; }
    if(!(testStrNum(principalName , 8))){ layer.msg("项目负责人 最多不能超过8字！"); return false ; }
    if(!(testStrNum(member , 60))){ layer.msg("小组成员 最多不能超过60字！"); return false ; }
    if(!(testStrNum(currentSituation , 100))){ layer.msg("现状描述 最多不能超过100字！"); return false ; }
    if(!(testStrNum(proposal , 200))){ layer.msg("立项目的 最多不能超过200字！"); return false ; }
    if(!(testStrNum(description , 300))){ layer.msg("项目简介 最多不能超过300字！"); return false ; }
    if(!(testStrNum(outlook , 300))){ layer.msg("相关展望 最多不能超过300字！"); return false ; }
    if(!(testStrNum(archive , 100))){ layer.msg("输出资料 最多不能超过100字！"); return false ; }
    if(!(testStrNum(memo , 200))){ layer.msg("备注 最多不能超过200字！"); return false ; }
    var data = {};
    data["projectName"] =  projectName ;
    data["category"] =  category ;
    data["tag"] =  tag ;
    data["code"] =  code ;
    data["foundation"] =  foundation ;
    data["principalName"] = principalName;
    data["member"] =  member ;
    data["currentSituation"] =  currentSituation ;
    data["proposal"] =  proposal ;
    data["description"] =  description ;
    data["beginTime"] =  new Date(beginTime) ;
    data["endTime"] = new Date(endTime) ;
    data["outlook"] =  outlook ;
    data["archive"] =  archive ;
    data["memo"] =  memo ;
    data["pathList"] = JSON.stringify(pathList);
    $.ajax({
        "url" : "../project/addProjectBase.do" ,
        "data" : data ,
        success:function (res) {
            var status = res["status"] ;
            if(status == 1){
                // 在立案通过里面查看
                $(".ty-secondTab").children("li:eq(2)").click();
                layer.msg("新增成功");
                if (timer_addPro) {
                    clearInterval(timer_addPro);
                }

            }else{
                layer.msg("新增失败");
            }
        }
    })
}
// creator:hxz 2018-06-04  显示项目审批流程
function showFL(obj) {
    obj.hide().siblings().show() ;
}
// creator:hxz 2018-06-04  显示项目审批流程
function hideFL(obj) {
    obj.siblings(":eq(0)").show().siblings().hide();
}
// creator:hxz 2018-06-04  区分现实性别男女
function chargeGender(type){
    if(type == 0){
        return "女" ;
    }else if(type == 1){
        return "男" ;
    }else{
        return "" ;
    }
}
// creator :hxz 2018-06-06 上传附件
function uplooad(obj) {
    var num = $("#add_fu").find(".ty-fileType").length;
    if(num >9){
        layer.msg("附件最多只能上传9个！") ;
        return false ;
    }
    $("#file_upload_1-button").click();
}


laydate.render({elem: '#add_proBeginDate'});
laydate.render({elem: '#add_proEndDate'});
laydate.render({elem: '#endProDate'});
laydate.render({elem: '#searchStart'});
laydate.render({elem: '#searchEnd'});








