var user = null;
// creator: hxz  2018-07-18  初始化 三级弹框 - fixed2
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
var bounce_Fixed4 = new Bounce(".bounce_Fixed4");
var bounce_Fixed5 = new Bounce(".bounce_Fixed5");
var editContractObj = null
var contractDel = null
$(function(){

    getContractRemind();

    $("#tipcontractGoods").on("change", '.ty-checkbox input', function () {
        var bounceCon = $(this).parents(".bonceCon")
        var length = bounceCon.find('input:checked').length
        bounceCon.find(".count").html(length)
    })
	// 获得基本信息列表
	var userName = $("#userName").html();
	var userID = $("#userID").html();
    user = {"userName": userName, "userID": userID};

    if (sphdSocket.user.oid === 0) {
        $("#outOfOrgBtn").show()
        $("#suspendedCustomerBtn").hide()
    }
    $("body").on("click",".funBtn,.funbtn,.linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
	$(".chooseCusCon").click(function () {
	    let target = $(this).data("target");
	    let str = "其他";
        var type = "" , source = "" ;
	    if(target == "#ReceiveName"){
            str = "收货人";
            type = $("#newReceiveAddressInfo").data('type');
            source = $("#newReceiveAddressInfo").data('source');
        } else  if(target == "#areaName"){
            str = "收货人";
            type = $("#newReceiveAreaInfo").data('type');
            source = $("#newReceiveAreaInfo").data('source');
        } else if(target == "#mailName"){
            str = "发票接收人";
            type = $("#newMailInfo").data('type');
            source = $("#newMailInfo").data('source');
        }
	    $("#target").val(target)
        bounce_Fixed3.show( $("#chooseCusContact") );

        if(source == 'addCustomer') {
            var contactInfo = addCusContact;
            setCusListStr(contactInfo,'addCustomer')
        }else{
            var cusId = $("#updateCustomerPanel").data("id");
            getCusContactList(cusId);
        }
    });
    $(".main").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        var id = $(this).siblings(".hd").find(".cusProId").text()
        switch (name) {
            case 'orgManage':
                var cusInfo = JSON.parse($(this).next(".hd").find(".cusInfo").html())
                $(".orgManage").show().siblings().hide()
                $(".orgManage").data("cusInfo", cusInfo)
                $(".orgManage .orgName").html(cusInfo.name || cusInfo.fullName)
                getOrganizationList(cusInfo.id)
                break;
            case 'outOfOrg':
                $(".page_outOfOrg").show().siblings().hide()
                getOutOfOrgList(1, 20)
                break
        }
    })
    $(".add_cusName").on("blur", function () {
        var val = $(this).val()
        $(this).parents("form").find("[name='name']").val(val.substring(0,6))
    })
    // 机构管理和暂停服务的机构 功能按钮
    $(".orgManage, .page_outOfOrg").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'goBack':
                $(".main").show().siblings().hide()
                break;
            // 新增机构 - 按钮
            case 'newOrg':
                $("#newOrg input, #newOrg select").val("").prop("disabled", false)
                var cusInfo = $(".orgManage").data("cusInfo")
                $("#newOrg input[name='supervisorName']").val(cusInfo.supervisorName)
                $("#newOrg input[name='supervisorMobile']").val(cusInfo.supervisorMobile)
                setEveryTime(bounce, 'newOrg')
                getProductOption().then(res => {
                    $("#newOrg .productSelect").html(res)
                    bounce.show($("#newOrg"))
                })
                break;
            // 机构信息 - 按钮
            case 'orgInfo':
                if ($(".page_outOfOrg").is(":visible")) {
                    $("#changeOrgInfoBtn").hide()
                } else {
                    $("#changeOrgInfoBtn").show()
                }
                var orgId = $(this).parents("tr").data("id")
                getOrgInfo(orgId).then(data => {
                    for (var key in data) {
                        $("#orgInfo .orgInfo_" + key).html(data[key] || '--')
                    }
                    bounce.show($("#orgInfo"))
                })
                $("#orgInfo").data("orgId", orgId)
                break;
            // 产品信息 - 按钮
            case 'productInfo':
                var orgId = $(this).parents("tr").data("id")
                getProductInfoByOid($("#productInfo"), orgId)
                getProductInfoByOid(orgId).then(data => {
                    var mpOrganization = data.mpOrganization // 机构信息

                    for (let key in mpOrganization) {
                        if (mpOrganization.uploadStorageType === null) {mpOrganization.uploadStorageType = 'NFS'}
                        $("#productInfo").find(".orgInfo_"+key).html(mpOrganization[key])
                        if(key === 'address'){
                            $("#productInfo").find(".orgInfo_"+key).html(mpOrganization[key] || '--');
                        }
                    }
                    $("#productInfo .orgInfo_mpPackageName").attr("value", mpOrganization.mpPackageId)

                    if ($(".page_outOfOrg").is(":visible")) {
                        $("#changeProductBtn").hide()
                    } else {
                        $("#changeProductBtn").show()
                    }
                    bounce.show($("#productInfo"))
                    $("#productInfo").data("orgId", orgId)
                })

                break;
            // 增值服务 - 按钮
            case 'addService':
                if ($(this).hasClass("link-gray")) {
                    return false
                }
                var orgId = $(this).parents("tr").data("id")
                getIncreaseModules(orgId)
                bounce.show($("#addService"))
                $("#addService input:radio").prop('checked', false)
                $(".bounce").data("data", {oid: orgId})
                break;
            // 登录记录
            case 'userLoginRecord':
                var orgId = $(this).parents("tr").data("id");
                $(".dateType").html("登录日期");
                $(".loginRecord").data("orgId", orgId);
                $(".loginRecord").show().siblings().hide();
                getCusLoginRecordList(orgId, 1, '', '') ;
                $(".loginRecord").data("prevPage", $(this).parents(".page").attr("name"))
                break;
            // 空间与流量
            case 'resouceManage':
                getResouceInfo($(this))
                break
            // 暂停服务 - 按钮
            case 'stopService':
                bounce.show($("#bounce_tip"))
                $("#bounce_tip .tipMsg").html("机构如被暂停服务，其成员将无法登录。<br>确定继续操作吗?")
                var orgId = $(this).parents("tr").data("id")
                $("#bounce_tip .sureBtn").unbind().on("click", function () {
                    // stopService()
                    $.ajax({
                        url: '../special/addOrgOutOfServiceApply.do',
                        data: {
                            organizationId: orgId
                        },
                        success: function (res){
                            var data = res.data
                            if (data === 1) {
                                layer.msg("操作成功，等待审批")
                                bounce.cancel()
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break;
            // 恢复服务 - 按钮
            case 'recoveryService':
                bounce.show($("#bounce_tip"))
                $("#bounce_tip .tipMsg").html("确定恢复向该客户提供服务吗？")
                var orgId = $(this).parents("tr").data("id")
                var that = $(this)
                $("#bounce_tip .sureBtn").unbind().on("click", function () {
                    // recoveryService()
                    $.ajax({
                        url: '../special/addOrgRestoreServiceApply.do',
                        data: {
                            organizationId: orgId
                        },
                        success: function (res){
                            var data = res.data
                            if (data === 1) {
                                layer.msg("操作成功")
                                bounce.cancel()
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break;
        }
    })
    $(".loginRecord").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'goBack':
                $("." + $(".loginRecord").data("prevPage")).show().siblings().hide()
                break;
        }
    })
    //本年、本月切换状态
    $(".flagTab .ty-btn-groupCom .ty-btn").on("click",function(){
        //样式切换
        $(this).addClass("ty-btn-active-blue").siblings().removeClass("ty-btn-active-blue");
        $("#loginQueryBtn").removeClass("ty-btn-blue");

        //清空自定义选择的时间
        $("#queryBeginTime").val("");
        $("#queryEndTime").val("");

        //获取对应数据
        var thisFlag = Number($(this).index()) + 1;
        var orgId = $(".loginRecord").data("orgId");
        if (thisFlag == 1) {
            $(".dateType").html("登录日期");
        }else if (thisFlag == 2) {
            $(".dateType").html("登录月份");
        }
        getCusLoginRecordList(orgId, thisFlag, '', '') ;
    });
    $(".bounce_Fixed3").on('click','.ty-btn', function(){
        var name = $(this).data('name');
        switch (name) {
            case 'cEndOk': // 暂停履约/合同终止 或者 回复履约的确定
                cEndOk(1)
                break;
            case 'cEndOk2':
                let radio = $("#cEndTip2 [name='expire']:checked").val()
                if (radio === '1') {
                    // 不再执行，转入“已到期的合同
                    cEndOk(2)
                } else if (radio === '2') {
                    // 续约
                    bounce_Fixed3.cancel()
                    contractInfoEdit('cRenewalStop', 'updateCustomer');
                } else {
                    layer.msg("请勾选！")
                }
                break;
            case 'addContact':
                var level = $("#newContectInfo").data('level');
                var type = $("#newContectInfo").data('type');
                var id = $("#newContectInfo").data('id');
                var source = $("#newContectInfo").data('source');
                if(source == 'addCustomer'){
                    var contactInfo = $("#addAccount").data('contactInfo');
                    contactInfo = JSON.parse(contactInfo);
                    let contactData = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $(" #contactNumber").val(),
                        'visitCard': '',
                        'socialList': '[]'
                    }
                    if($("#contactsCard .bussnessCard").length > 0){
                        contactData.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if($(".otherContact li").length > 0){
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json ={
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        contactData.socialList = arr;
                    }
                    var groupUuid= $("#contactsCard").attr("groupUuid")
                    if (type == 'new'){
                        contactData.id = Math.random();
                        contactData.groupUuid = groupUuid;
                        contactInfo.push(contactData);
                        addCusContact.push(contactData);

                    }else if(type == 'update') {
                        var id = $("#newContectInfo").data('id');
                        var index = contactInfo.findIndex(value=>value.id === id);
                        // data.number = id;
                        contactData.id = id;
                        contactData.groupUuid = groupUuid;
                        contactInfo[index] = contactData;
                        addCusContact.forEach(function (item, index) {
                            if(id == item.id){
                                addCusContact[index] = contactData ;
                                $(".mailList tbody tr").each(function(){
                                    let thisContactID = $(this).find(".hd").html()
                                    if(thisContactID == contactData.id){
                                        $(this).children(":eq(2)").html(contactData.name);
                                        $(this).children(":eq(4)").html(contactData.mobile);
                                    }
                                })
                                let place = $(".goodsAddress").siblings(".hd").html();
                                if (place !== "") {
                                    place = JSON.parse(place);
                                    if (place.deliveryType === 2) {
                                        let c_name = contactData.name;
                                        let c_mobile = contactData.mobile;
                                        place.shAddressList.forEach(function (item, index){
                                            if (parseInt(item.customerContact) === parseInt(id)) {
                                                place.shAddressList[index].contact = c_name
                                                place.shAddressList[index].mobile = c_mobile
                                            }
                                        })
                                        place.areaList.forEach(function (item, index){
                                            if (parseInt(item.customerContact) === parseInt(id)) {
                                                place.areaList[index].contact = c_name
                                                place.areaList[index].mobile = c_mobile
                                            }
                                        })
                                        $(".goodsAddress").siblings(".hd").html(JSON.stringify(place));
                                    }
                                }
                            }
                        })
                    }
                    var html = setContactList(contactInfo);
                    $(".contectList").html(html);
                    // 给当前的赋值
                    bounce_Fixed3.cancel();
                    if(level == 2){
                        var tags = $("#contactFlag").html();
                        if(tags == "收货人"){
                            contactData.contact = contactData.name
                            if ($("#newReceiveAddressInfo").is(":visible")){
                                $("#ReceiveName")
                                    .val(contactData.name).data('orgData',contactData.name)
                                    .siblings(".hd").html(JSON.stringify(contactData)) ;
                            } else  if ($("#newReceiveAreaInfo").is(":visible")){
                                $("#areaName")
                                    .val(contactData.name).data('orgData',contactData.name)
                                    .siblings(".hd").html(JSON.stringify(contactData)) ;
                            }
                        }else if(tags== "发票接收人"){
                            bounce_Fixed.show($("#newMailInfo"));
                            $("#mailName")
                                .val(contactData.name).data('orgData',contactData.name)
                                .siblings(".hd").html(JSON.stringify(contactData)) ;
                        }
                    }
                }else{
                    var data = {
                        'tags': $("#contactFlag").html(),
                        'name': $("#contactName").val(),
                        'post': $("#position").val(),
                        'mobile': $("#contactNumber").val(),
                        'visitCard': '',
                        'socialList': '[]'
                    };
                    if($("#contactsCard .bussnessCard").length > 0){
                        data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
                    }
                    if($(".otherContact li").length > 0){
                        var arr = []
                        $(".otherContact li").each(function () {
                            if ($(this).find('input').val() != '') {
                                var json = {
                                    'code': $(this).find("input").val(),
                                    'type': $(this).find("input").data('type'),
                                    'name': $(this).find("input").data('name')
                                };
                                arr.push(json);
                            }
                        })
                        arr = JSON.stringify(arr);
                        data.socialList = arr;
                    }
                    if (type == 'new') {
                        data.customer = id;
                        $.ajax({
                            url: '../sales/addCustomerContact.do',
                            data: data,
                            success: function (res) {
                                data.id = res.id;
                                var status = res.status;
                                if (status === '1' || status === 1) {
                                    layer.msg('新增成功')
                                    var groupUuidArr = []
                                    $("#newContectInfo [groupUuid]").each(function () {
                                        groupUuidArr.push({
                                            type: 'groupUuid',
                                            groupUuid: $(this).attr("groupUuid")
                                        })
                                    })
                                    cancelFileDel(groupUuidArr)
                                    // 给当前的赋值
                                    var tags = $("#contactFlag").html();
                                    if(tags == "收货人"){
                                        data.contact = data.name
                                        if ($("#newReceiveAddressInfo").is(":visible")) {
                                            bounce_Fixed2.show($("#newReceiveAddressInfo"));
                                            setTimer('updateReceive');
                                            $("#ReceiveName")
                                                .val(data.name).data('orgData', data.name)
                                                .siblings(".hd").html(JSON.stringify(data));
                                        } else  if ($("#newReceiveAreaInfo").is(":visible")){
                                            $("#areaName")
                                                .val(data.name).data('orgData',data.name)
                                                .siblings(".hd").html(JSON.stringify(data)) ;
                                        }
                                    }else if(tags== "发票接收人"){
                                        bounce_Fixed.show($("#newMailInfo"));
                                        setTimer('updateMail');
                                        $("#mailName")
                                            .val(data.name).data('orgData',data.name)
                                            .siblings(".hd").html(JSON.stringify(data)) ;
                                    }
                                    bounce_Fixed3.cancel();
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }else if(type == 'update'){
                        data.contactId = id;
                        $.ajax({
                            url: '../sales/updateContactsSocialAndCard.do',
                            data: data,
                            success: function (res) {
                                var status = res.status;
                                if (status === '1' || status === 1) {
                                    var groupUuidArr = []
                                    $("#newContectInfo [groupUuid]").each(function () {
                                        groupUuidArr.push({
                                            type: 'groupUuid',
                                            groupUuid: $(this).attr("groupUuid")
                                        })
                                    })
                                    cancelFileDel(groupUuidArr)
                                    indexUpdateList(cus_seeTrObj);
                                    // 给当前的赋值
                                    var tags = $("#contactFlag").html();
                                    if(tags == "收货人"){
                                        data.contact = data.name
                                        if ($("#newReceiveAddressInfo").is(":visible")) {
                                            bounce_Fixed2.show($("#newReceiveAddressInfo"));
                                            setTimer('updateReceive');
                                            $("#ReceiveName")
                                                .val(data.name).data('orgData', data.name)
                                                .siblings(".hd").html(JSON.stringify(data));
                                        } else  if ($("#newReceiveAreaInfo").is(":visible")){
                                            $("#areaName")
                                                .val(data.name).data('orgData',data.name)
                                                .siblings(".hd").html(JSON.stringify(data)) ;
                                        }
                                    }else if(tags== "发票接收人"){
                                        bounce_Fixed.show($("#newMailInfo"));
                                        setTimer('updateMail');
                                        $("#mailName")
                                            .val(data.name).data('orgData',data.name)
                                            .siblings(".hd").html(JSON.stringify(data)) ;
                                    }
                                    bounce_Fixed3.cancel();
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }
                    bounce_Fixed3.cancel();
                }
                break;
        }
    });
    $(".bounce_Fixed2, .bounce_Fixed3").on('click','.ty-btn,.fileImScan,.node', function(){
        var name = $(this).data('fun');
        let contractInfo = $("#cScan").data("contractInfo")
        let contractBase = {}
        let listZS = []
        let listTY = []
        if (contractInfo) {
            contractBase = contractInfo.contractBase
            listZS = contractInfo.listZS || []
            listTY = contractInfo.listTY || []
        }
        switch (name) {
            case 'imgScan': // 合同的picture
                imgViewer($(this));
                break;
            case 'cWord': // 合同的可编辑版
                // let path = $("#cScan .cWord").attr('path');
                seeOnline($(this))
                break;
            case 'zsNum': //本合同下的商品
                // $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
                // $(".addOrCancel").hide(); $(".cScanc").show();
                // let productList = $("#cScan .gNum").data('list');
                setContactGS(listZS , false);
                $("#tipcontractGoods .bonceHead span").html('本合同下的专属商品');
                $("#tipcontractGoods .tip").html(`本合同下的专属商品共有以下${ listZS.length }种`);
                break;
            case 'tyNum': //本合同下的商品
                // $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
                // $(".addOrCancel").hide(); $(".cScanc").show();
                // productList = $("#cScan .gNum").data('list');
                setContactGS(listTY , false);
                $("#tipcontractGoods .bonceHead span").html('本合同下的通用型商品');
                $("#tipcontractGoods .tip").html(`本合同下的通用型商品共有以下${ listTY.length }种`);
                break;
            case 'cEditLog': // 本版本合同的修改记录
                getContractChangeLog(contractBase.id)
                break;
            case 'cRenewalLog': // 本合同的续约记录
                let primaryId = contractBase.primaryCont
                getContractRenewLog(primaryId)
                break;
            //机构信息变动记录详情弹窗
            case 'ogre':
                //代入数据
                console.log("尝试");
                var orgBegId = $(this).data('id'); //获得id  有问题！！值不对！！id不变成1或2
                var data ={};
                var b =$(this).data('index');//获得索引值
                getOrgBegin(orgBegId, b);
                $(".bounce_Fixed3 #bodcerMore .bonceHead .bounce_title").html("机构信息查看");
                let title2 = $(".bounce_Fixed3 #bodcerMore .bonceHead .bounce_title").html();
                if( title2 === '机构信息查看'){
                    bounce_Fixed3.show($("#bodcerMore"));
                    $(".bounce_Fixed3 #bodcerMore .bonceHis .ordge").show();//自己要显示的表格
                    $(".bounce_Fixed3 #bodcerMore .seeProductBtn").show();//最下面的关闭按钮
                    $(".bounce_Fixed3 #bodcerMore [name]").prop("disabled",true);
                    if(b == "0"){
                        $(".bounce_Fixed3 #bodcerMore .bonLock .Loup").show();
                        $(".bounce_Fixed3 #bodcerMore .bonLock .Lope").hide();
                    }else{
                        $(".bounce_Fixed3 #bodcerMore .bonLock .Lope").show();
                        $(".bounce_Fixed3 #bodcerMore .bonLock .Loup").hide();
                    }
                }
                // var orgBegId = $("#selectModule").data("orgId");
                // var data = {};
                // getOrgBegin(orgBegId);
                // $(".bounce_Fixed3 #bodcerMore .bonceHead .bounce_title").html("机构信息查看");
                // let title2 = $(".bounce_Fixed3 #bodcerMore .bonceHead .bounce_title").html();
                // if( title2 === '机构信息查看'){
                //     bounce_Fixed3.show($("#bodcerMore"));
                //     // $(".bounce_Fixed3 #bodcerMore .bonLock .ordge").show();//要显示的表格
                //     // $(".bounce_Fixed2 #bodcerLine .bonceHis .orgHList").hide();//将之前的表格隐藏
                //     // $(".bounce_Fixed3 #bodcerMore .seeProductBtn").show();//最下面的关闭按钮
                //     // $(".bounce_Fixed3 #bodcerMore .bonLock .Loup").show();//上面的提示文字
                //     // $("#selectModule").data("orgBegId",orgBegId);
                // }
                break;
            case 'addReceive':
                var type = $("#newReceiveAddressInfo").data('type');
                var source = $("#newReceiveAddressInfo").data('source');
                if(source == 'addCustomer' || source == 'updateReceiveInfo'){
                    let receiveData = []
                    let contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
                    var data = {
                        'type': 1,//1-收货地址,3-收货区域"
                        'address': $("#ReceiveAddress").val(),
                        'contact':  contactInfo.contact ,
                        'mobile': contactInfo.mobile,
                        'customerContact': contactInfo.id,
                    }
                    if (type == 'new'){
                        receiveData.push(data);
                        setShList(receiveData);
                    }else if(type == 'update'){
                        let curObj = $("#newReceiveAddressInfo").data('trObj');
                        let html =
                            `<td>${data.address}</td>
                             <td>${data.contact}</td>
                             <td>${data.mobile}</td>
                             <td>
                                <span class="link-blue funBtn" data-type="update" data-fun="addAddress" data-source="addCustomer">修改</span>
                                <span class="link-red funBtn" data-type="delete" data-fun="deleteAddressData">删除</span>
                                <span class="hd">${JSON.stringify(data)}</span>
                             </td>`
                        curObj.html(html)
                    }
                }else if (source == 'updateCustomer'){
                    var id = $("#newReceiveAddressInfo").data('id');
                    let contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
                    if (type == 'new') {
                        var json = {
                            'type': 1,
                            'address': $("#ReceiveAddress").val(),
                            'contact': contactInfo.name ,
                            'customerContact': contactInfo.id ,
                            'mobile':contactInfo.mobile
                        }
                        json = JSON.stringify(json);
                        var params = {
                            'customerId': id,
                            'shAddress': json
                        }
                        $.ajax({
                            url: '../sales/addCustomerAddress.do',
                            data: params,
                            success: function (data) {
                                var status = data.status;
                                if (status === '1' || status === 1) {
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }else if(type == 'update'){
                        var params = {
                            'id': id,
                            'type': 1,
                            'address': $("#ReceiveAddress").val(),
                            'contact':  contactInfo.name ,
                            'mobile': contactInfo.mobile
                        }
                        $.ajax({
                            url: '../sales/updateCustomerAddress.do',
                            data: params,
                            success: function (data) {
                                var status = data.status;
                                if (status === '1' || status === 1) {
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }
                }
                bounce_Fixed2.cancel();
                break;
            case 'addAreaReceive':
                let regionCon = $("#regionCon").val();
                let contactName = $("#areaName").val();
                if (regionCon !== "" && contactName !== "") {
                    var type = $("#newReceiveAreaInfo").data('type');
                    var source = $("#newReceiveAreaInfo").data('source');
                    let contactInfo = JSON.parse($("#areaName").siblings(".hd").html());
                    let receiveData = [];
                    let path = $("#regionCon").siblings(".hd").html();
                    let code = path.split(",");
                    var data = {
                        'type': 3,//1-收货地址,3-收货区域"
                        'address': regionCon,
                        'contact':  contactInfo.contact ,
                        'mobile': contactInfo.mobile,
                        'customerContact': contactInfo.id,
                        'path': path,
                        'regionCode':code[code.length - 1],
                        'requirements': $("#requirements").val()
                    }
                    if(source == 'addCustomer' || source == 'updateReceiveInfo'){
                        if (type == 'new'){
                            receiveData.push(data);
                            setShList(receiveData);
                        }else if(type == 'update'){
                            let curObj = $("#newReceiveAreaInfo").data('trObj');
                            let html =
                                `<td>${data.address}</td>
                             <td>${data.contact}</td>
                             <td>${data.mobile}</td>
                             <td>
                                <span class="link-blue funBtn" data-type="update" data-fun="addArea" data-source="addCustomer">修改</span>
                                <span class="link-red funBtn" data-type="delete" data-fun="deleteAddressData">删除</span>
                                <span class="hd">${JSON.stringify(data)}</span>
                             </td>`
                            curObj.html(html)
                        }
                    }else if (source == 'updateCustomer'){
                        var id = $("#newReceiveAreaInfo").data('id');
                        if (type == 'new') {
                            data.customerContact = contactInfo.id
                            var json = JSON.stringify(data);
                            var params = {
                                'customerId': id,
                                'shAddress': json
                            }
                            $.ajax({
                                url: '../sales/addCustomerAddress.do',
                                data: params,
                                success: function (data) {
                                    var status = data.status;
                                    if (status === '1' || status === 1) {
                                        indexUpdateList(cus_seeTrObj);
                                    } else {
                                        layer.msg("查看失败！");
                                    }
                                }
                            })
                        }else if(type == 'update'){
                            data.id = id;
                            $.ajax({
                                url: '../sales/updateCustomerAddress.do',
                                data: data,
                                success: function (res) {
                                    var status = res.status;
                                    if (status === '1' || status === 1) {
                                        indexUpdateList(cus_seeTrObj);
                                    } else {
                                        layer.msg("查看失败！");
                                    }
                                }
                            })
                        }
                    }
                    bounce_Fixed2.cancel();
                } else {
                    layer.msg('还有必填项尚未填写!');
                }
                break;
        }
    });
    $(".bounce_Fixed").on("click",".ty-btn,.edit,[type='btn']", function(){
        var name = $(this).data('name');
        let info = ``;
        switch (name) {
            case 'cScan': // 已到期的查看
                info = JSON.parse($(this).siblings(".hd").html())
                var cid  =  info.id;
                cScan(cid)
                break;
            case 'cRestart': // 恢复履约/重启合作
                info = JSON.parse($(this).siblings(".hd").html())
                $("#cEndTip").data("cid", info.id).data("enabled", 1)
                // 启用合同时判断合同的状态
                $.ajax({
                    url: $.webRoot + '/sales/checkReStartContractState.do',
                    data: { id: info.id }
                }).then(res => {
                    let state = res.data.state // state 0-不要重复启用 1-没有过期直接恢复 2-已经过期了
                    let vaildEnd = res.data.vaildEnd // 到期日期
                    if (state === 0) {
                        layer.msg("请勿重复操作！")
                    } else if (state === 2) {
                        bounce_Fixed3.show($("#cEndTip2"));
                        $("#cEndTip2 input:radio").prop("checked", false)
                        $("#cEndTip2 .vaildEnd").html(moment(vaildEnd).format("YYYY-MM-DD"))
                    } else if (state === 1) {
                        bounce_Fixed3.show($("#cEndTip"));
                        $("#tipsc").html("<p>点击“确定”后，本合同将回到有效合同列表。</p> " +
                            " <p>确定进行本操作吗？</p>");
                    } else {
                        layer.msg("操作失败！")
                    }
                })
                break;
            case 'cRenewal': // 续约
                info = JSON.parse($(this).siblings(".hd").html())
                if (info.type !== 1) {
                    layer.msg("<p>在此不可对服务合同进行编辑！</p><p>如需要，请到“服务合同”模块中操作。</p>")
                    return false;
                }
                contractInfoEdit('cRenewalHistory', 'updateCustomer', $(this));
                break
            case 'editContractOk': // 编辑合同确定
                editContractOk(1)
                break;
            case 'addMail':
                var source = $("#newMailInfo").data('source');
                var type = $("#newMailInfo").data('type');
                if(source == 'addCustomer'){
                    var mailData = $("#addAccount").data('mailInfo');
                    mailData = JSON.parse(mailData);
                    if (type == 'new'){
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var data = {
                            'number': Math.random(),//
                            'address': $("#mailAddress").val(),
                            'contact':  contactInfo.name,
                            'postcode': $("#mailNumber").val(),
                            'mobile': contactInfo.mobile,
                            'contactInfoNumber': contactInfo.number
                        }
                        mailData.push(data);
                    }else if(type == 'update'){
                        var id = $("#newMailInfo").data('id');
                        var index = mailData.findIndex(value=> Number(value.number) === Number(id));
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var data = {
                            'number': id,
                            'address': $("#newMailInfo #mailAddress").val(),
                            'contact': contactInfo.name,
                            'postcode': $("#newMailInfo #mailNumber").val(),
                            'mobile': contactInfo.mobile,
                            'contactInfoNumber': contactInfo.number
                        }
                        mailData[index] = data;
                    }
                    setMailList(mailData);
                }else{
                    var id = $("#newMailInfo").data('id');
                    if (type == 'new') {
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var json = {
                            'address': $("#mailAddress").val(),
                            'contact': contactInfo.name,
                            'postcode': $("#mailNumber").val(),
                            'mobile': contactInfo.mobile
                        }
                        json = JSON.stringify(json);
                        var params = {
                            'customerId': id,
                            'fpAddress': json
                        }
                        $.ajax({
                            url: '../sales/addCustomerAddress.do',
                            data: params,
                            success: function (data) {
                                var status = data.status;
                                if (status === '1' || status === 1) {
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("新增失败！");
                                }
                            }
                        })
                    }else if(type == 'update'){
                        let contactInfo = JSON.parse($("#mailName").siblings(".hd").html());
                        var params = {
                            'id': id ,
                            'address': $("#mailAddress").val(),
                            'contact': contactInfo.name,
                            'postcode': $("#mailNumber").val(),
                            'mobile': contactInfo.mobile
                        }
                        $.ajax({
                            url: '../sales/updateCustomerAddress.do',
                            data: params,
                            success: function (data) {
                                var status = data.status;
                                if (status === '1' || status === 1) {
                                    indexUpdateList(cus_seeTrObj);
                                } else {
                                    layer.msg("查看失败！");
                                }
                            }
                        })
                    }
                }
                bounce_Fixed.cancel();
                break;
            case 'enableTurn':
                var addressId = $(this).data('id');
                var val = $(this).data('val');
                $.ajax({
                    url: '../sales/startOrStopAddress.do',
                    data: {
                        'addressId': addressId,
                        'enabled': val
                    },
                    success: function (data) {
                        var status = data.status;
                        if (status === '1' || status === 1) {
                            indexUpdateList(cus_seeTrObj);
                            bounce_Fixed.cancel();
                        } else {
                            layer.msg("停止失败！");
                        }
                    }
                })
                break;
            case 'deleteContact':
                var contactId = $(this).data('id');
                $.ajax({
                    url: '../sales/deleteCustomerContact.do',
                    data: {
                        'contactId': contactId
                    },
                    success: function (data) {
                        var status = data.status;
                        if (status === '1' || status === 1) {
                            indexUpdateList(cus_seeTrObj);
                            bounce_Fixed.cancel();
                        } else {
                            layer.msg("停止失败！");
                        }
                    }
                })
                break
            // 机构信息修改 - 提交 按钮
            case 'sureOrgInfoChange':
                var data = {
                    id: $("#orgInfo").data("orgId")
                }
                $("#orgInfoChange [name]").each(function () {
                    var key = $(this).attr("name")
                    data[key] = $(this).val()
                })
                $.ajax({
                    url: '../special/updateCustomerOrganization.do',
                    data: data,
                    success: function (res) {
                        var data = res.data
                        if (data && data === 1) {
                            layer.msg("修改成功")
                            bounce_Fixed.cancel()
                            bounce.cancel()
                        } else {
                            layer.msg("修改失败")
                        }
                    }
                })
                break;
            // 产品信息修改 - 确定
            case 'sureProductInfoChange':
                var data = {
                    packageId: $("#productInfoChange [name='packageId']").val(),
                    organizationId: $("#productInfo").data("orgId")
                }
                $.ajax({
                    url: '../special/updateOrgPopedoms.do',
                    data: data,
                    success: function (res) {
                        var data = res.data
                        if (data && data === 1) {
                            layer.msg("修改申请已提交")
                            var key = $(".main #se0").val();
                            getCustomerMes(1 , 20, key)
                            bounce_Fixed.cancel()
                            bounce.cancel()
                        } else {
                            layer.msg("修改失败")
                        }
                    }
                })
                break
            case 'sureNewCusOrg':
                // 新增机构的确定
                if (!testMobile($.trim($("#newCusOrg input[name='supervisorMobile']").val()))) {
                    layer.msg("请输入正确的手机号")
                    return false
                }
                newOrganization()
                break
            // 暂不勾选模块 - confirm 弹窗
            case 'notSelectModule':
                sale_addsure()
                break;
            // 查看所选产品
            case 'seeProduct':
                var productId = $(this).parents(".bonceCon").find("[name='packageId']").val()
                if (!productId) {
                    layer.msg("请选择产品");
                    return false
                }
                getProductDetail(productId)
                bounce_Fixed2.show($("#seeProduct"));
                break
            case 'seeContractChangeHisDetail': // 客户查看 - 合同 本合同下的商品
                let contractId = $(this).parents("tr").data("id") ;
                seeContractHisDetail(contractId, 'change');
                break;
            case 'seeContractRenewHisDetail': // 客户查看 - 合同 本合同下的商品
                let contractHisId = $(this).parents("tr").data("id") ;
                seeContractHisDetail(contractHisId, 'renew');
                break;

        }
    });
    // 机构产品部分按钮交互
    $(".bounce").on("click",".ty-btn,.edit,[type='btn']", function(){
        var name = $(this).data('name');
        switch (name) {
            case 'sureNewOrg':
                // 新增机构的确定
                if (!testMobile($.trim($("#newOrg input[name='supervisorMobile']").val()))) {
                    layer.msg("请输入正确的手机号")
                    return false
                }
                newOrganization()
                break
            // 机构信息查看弹窗 - 修改 按钮
            case 'orgInfo':
                var orgId = $("#orgInfo").data("orgId")
                getOrgInfo(orgId).then(data => {
                    for (var key in data) {
                        $("#orgInfoChange [name='"+key+"']").val(data[key])
                    }
                    bounce_Fixed.show($("#orgInfoChange"))
                    setEveryTime(bounce_Fixed, 'orgInfoChange')
                })

                break
            // 产品信息弹窗 - 修改 按钮
            case 'changeProduct':
                var orgId = $("#productInfo").data("orgId");
                getProductInfoByOid(orgId).then(data => {
                    var mpOrganization = data.mpOrganization // 机构信息
                    var mpModuleOptionList = data.mpModuleOptionList // 可增值的菜单选项

                    getProductOption().then(str => {
                        $("#productInfoChange .productSelect").html(str)
                        // ---渲染机构信息---
                        for (let key in mpOrganization) {
                            if (mpOrganization.uploadStorageType === null) {mpOrganization.uploadStorageType = 'NFS'}
                            $("#productInfoChange").find("[name='"+key+"']").val(mpOrganization[key])
                        }
                        bounce_Fixed.show($("#productInfoChange"))
                        setEveryTime(bounce_Fixed, 'productInfoChange')
                    })
                })

                break
            // 机构信息变动记录弹窗
            case 'orgChangeHistory':
                //代入数据
                var orgHisId = $("#orgInfo").data("orgId");
                var data = {};
                getOrgChHis(orgHisId);
                $(".bounce_Fixed2 #bodcerLine .bonceHead .bounce_title").html("机构信息变动记录");
                let title1 = $(".bounce_Fixed2 #bodcerLine .bonceHead .bounce_title").html();
                if( title1 === '机构信息变动记录'){
                    bounce_Fixed2.show($("#bodcerLine"));
                    $(".bounce_Fixed2 #bodcerLine .bonceHis .orgHList").show();//自己要显示的表格
                    $(".bounce_Fixed2 #bodcerLine .seeProductBtn").show();//最下面的关闭按钮
                    $("#orgInfo").data("orgHisId", orgHisId);
                }
                break;
            // 暂停服务/恢复服务的操作记录
            case 'stopRecoveryRecord':
                var orgId = $("#orgInfo").data("orgId")
                bounce_Fixed2.show($("#stopRecoveryRecord"))
                // getStopRecoveryRecord()
                $.ajax({
                    url: '../special/orgOutOfRestoreHistories.do',
                    data: {
                        id: orgId
                    },
                    success: function (res) {
                        var list = res.data
                        var str = ''
                        for (var i in list) {
                            str +=  '<tr>' +
                                '   <td>' + list[i].operationNature  +'</td>'+
                                '   <td>' + list[i].updateName + ' ' + moment(list[i].updateTime).format("YYYY-MM-DD HH:mm:ss")  +'</td>'+
                                '</tr>'
                        }
                        $("#stopRecoveryRecord tbody").html(str)
                    }
                })
                break
            // 查看所选产品
            case 'seeProduct':
                var productId = $(this).parents(".bonceCon").find("[name='packageId']").val() || $(this).parents(".bonceCon").find(".orgInfo_mpPackageName").attr("value")
                if (!productId) {
                    layer.msg("请选择产品");
                    return false
                }
                getProductDetail(productId)
                bounce_Fixed2.show($("#seeProduct"));
                break
            // 增值服务修改 - 确定
            case 'sureAddService':
                sureAddService()
                break
        }
    })
    $("#newContractInfo").on('click','[type="btn"]', function () {
        let fun = $(this).data("fun");
        let cusInfo  = $("#updateCustomerPanel").data("cusInfo")
        let tips = '', title = '', list = [], checkedLength = 0;
        let productListZS = $("#newContractInfo").data('productListZS') || []
        let productListTY = $("#newContractInfo").data('productListTY') || []
        let editProductZS = productListZS.filter(item => item.isChecked)
        let editProductTY = productListTY.filter(item => item.isChecked)

        switch (fun){
            case 'scanZsGs' :
                title = '本合同下的专属商品'
                list = editProductZS
                tips = `本合同下的专属商品共有以下${ list.length }种`
                break;
            case 'scanTyGs' :
                title = '本合同下的通用型商品'
                list = editProductTY
                tips = `本合同下的通用型商品共有以下${ list.length }种`
                break;
            case 'removeZsGs' :
                title = '从本合同移出专属商品'
                list = editProductZS
                tips = `可供选择的专属商品共有以下${ list.length }种`
                break;
            case 'addZsGs' :
                title = '向本合同添加专属商品'
                list = productListZS
                tips = `${cusInfo.fullName||''}的专属商品共有以下${ list.length }种`
                break;
            case 'removeTyGs' :
                title = '从本合同移出通用型商品'
                list = editProductTY
                tips = `可供选择的通用型商品共有以下${ list.length }种`
                break
            case 'addTyGs' :
                title = '向本合同添加通用型商品'
                list = productListTY
                tips = `系统内共有以下${ list.length }种通用型商品，均可选择`
                break;
        }
        $("#tipcontractGoods .bonceHead span").html(title);
        $("#tipcontractGoods .tip").html(tips);
        let count = 0
        if (fun === 'addZsGs' || fun === 'addTyGs') {
            count = list.filter(item => item.isChecked).length
        }
        $("#tipcontractGoods .count").html(count);
        $("#tipcontractGoods").data('origin', fun)
        if (fun === 'scanZsGs' || fun === 'scanTyGs') {
            $(".addOrCancel").hide(); $(".cScanc").show();$(".countStr").hide()
            setContactGS(list , false);
        } else if (fun === 'addZsGs' || fun === 'addTyGs'){
            $(".addOrCancel").show(); $(".cScanc").hide();$(".countStr").show()
            setContactGS(list, true, true);
        } else {
            $(".addOrCancel").show(); $(".cScanc").hide();$(".countStr").show()
            setContactGS(list, true);
        }
    })
    $("#tipcontractGoods").on('click','.fa', function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    });
    // 客户修改弹窗按钮交互
    $(".bounce").on('click','.node', function(){
        var name = $(this).data('name');
        var type = $(this).data('type');
        var source = $(this).data('source');
        let info = ``
        switch (name) {
            case 'contractDel': // 客户查看 - 合同 删除合同
                contractDel = $(this) ;
                bounce_Fixed3.show( $("#contractDelTip") )

                break;
            case 'scanGs': // 客户查看 - 合同 本合同下的商品
                var cid = $(this).parents(".contractItem").data("id") ;
                cScanScanGs(cid);
                break;
            case 'cScan': // 已到期的查看
                var contractId = $(this).parents(".contractItem").data("id") ;
                cScan(contractId);
                break;
            case 'contractStopData': // 已暂停/终止的合同
                var id = $("#updateCustomerPanel").data("id")
                contractStopData(id)
                break;
            case 'contractEndData': // 已到期的合同
                var id = $("#updateCustomerPanel").data("id")
                contractEndData(id)
                break;
            case 'cEnd': // 暂停履约/合同终止
                let cInfo = JSON.parse($(this).siblings(".hd").html())
                if (Number(cInfo.type) === 2) {
                    layer.msg("<p>在此不可对服务合同进行编辑！</p><p>如需要，请到“服务合同”模块中操作。</p>")
                    return false;
                }
                var cid = $(this).parents(".contractItem").data("id") ;
                $("#cEndTip").data("cid",cid).data("enabled", 0)
                $("#tipsc").html("<p>点击“确定”后，本合同将进入“已暂停/终止的合同”。</p> " +
                                 "<p>确定进行本操作吗？</p>")
                bounce_Fixed3.show($("#cEndTip"));
                break;
            case 'contractInfo': // 包含了新增和修改客户的
                if(source === 'updateCustomer' && (type === 'update' || type === 'cRenewal')) {
                    info = JSON.parse($(this).siblings(".hd").html())
                    if (Number(info.type) === 2) {
                        layer.msg("<p>在此不可对服务合同进行编辑！</p><p>如需要，请到“服务合同”模块中操作。</p>")
                        return false;
                    }
                }
                contractInfoEdit(type, source, $(this));
			    break;
            case 'contractChangeLog': // 包含了新增和修改客户的
                info = JSON.parse($(this).siblings(".hd").html())
                getContractChangeLog(info.id)
                break;
            case 'contractRenewLog':
                info = JSON.parse($(this).siblings(".hd").html())
                getContractRenewLog(info.primaryCont)
                break
            case 'mailInfo':
                var id = $(this).siblings(".hd").html();
                $("#newMailInfo").data('type', type);
                $("#newMailInfo").data('id', id);
                $("#newMailInfo").data('source', source);
                if(type == 'delete') {
                    var mailData = $("#addAccount").data('mailInfo');
                    mailData = JSON.parse(mailData);
                    var index = mailData.findIndex(value=>value.number === id);
                    mailData.splice(index, 1);
                    setMailList(mailData);
                }else{
                    if(type == 'new'){
                        $("#newMailInfo input").val("");
                    }else if(type == 'update'){
                        $("#mailAddress").val($(this).parents("tr").find("td").eq(1).html());
                        $("#mailName").val($(this).parents("tr").find("td").eq(2).html());
                        $("#mailNumber").val($(this).parents("tr").find("td").eq(3).html());
                        $("#mailContact").val($(this).parents("tr").find("td").eq(4).html());
                    }
                    bounce_Fixed.show($("#newMailInfo"));
                    setTimer('updateMail');
                }
                break;
            case 'contactInfo':
                var id = $(this).parents("tr").data('id');
                $("#newContectInfo").data('type', type);
                $("#newContectInfo").data('id', id);
                $("#newContectInfo").data('source', source);
                $("#newContectInfo").data('level',1);
                if(type == 'delete') {
                    var contactData = $("#addAccount").data('contactInfo');
                    contactData = JSON.parse(contactData);
                    var index = contactData.findIndex(value=>value.number === id || value.id === id);
                    contactData.splice(index, 1);
                    var html = setContactList(contactData);
                    $(".contectList").html(html);
                }else{
                    $("#uploadCard").html("")
                    initCardUpload($("#uploadCard"));
                    document.getElementById('newContectData').reset();
                    $(".otherContact").html("");
                    $("#contactFlag").html("其他");
                    $("#newContectInfo .bonceHead span").html('联系人')
                    $("#contactsCard .bussnessCard").remove();
                    if(type == 'new'){
                        $('#uploadCard').show();
                        $("#addMoreContact").hide();
                    }else if(type == 'update'){
                        var getData = $("#addAccount").data('contactInfo');
                        getData = JSON.parse(getData);
                        var flag = getData.find(value=>value.id === id || value.number === id);
                        if(flag){
                            flag.socialList = JSON.parse(flag.socialList);
                            setContact(flag);
                        }
                    }
                    $("#addMoreContact").hide();
                    bounce_Fixed3.show($("#newContectInfo"));
                    setTimer('updateContact');
                }
                break;
            // 新增客户弹窗 - 暂不勾选模块 - 按钮
            case 'notSelectModule':
                if (!testMobile($.trim($("#addAccount input[name='supervisorMobile']").val()))) {
                    layer.msg("请输入正确的手机号")
                    return false
                }
                $("#mtTip .tip").html("确定暂不勾选模块吗？")
                bounce_Fixed.show($("#mtTip"))
                $("#mtTip .bonceFoot span").attr("data-name", 'notSelectModule')
                break;
            // 新增客户弹窗 - 录入完毕，勾选模块 - 按钮 （此操作会新增客户然后打开勾选模块弹窗）
            case 'selectModule':
                if (!testMobile($.trim($("#addAccount input[name='supervisorMobile']").val()))) {
                    layer.msg("请输入正确的手机号")
                    return false
                }
                var shList =[];
                var imgsQ = [], imgsP = [] ;
                var data = {
                    qImages: '',
                    pImages: '',
                    shAddressList: '',
                    fpAddressList: '',
                    contactsList: ''
                };
                $('#addpayDetails input[type="text"]').each(function(){
                    var name = $(this).attr('name');
                    data[name] = $(this).val();
                })
                var buyCase = $("#addpayDetails input[name='buyCase']:checked").val() == 1;
                if(buyCase){
                    var initialType = $("#addpayDetails input[name='firstTime']:checked").val();
                    if(initialType == undefined){
                        layer.msg('请选择首次购买时间');
                        return false;
                    }else{
                        data['initialType'] = initialType;
                        var date = '', month = '';
                        var year = new Date().getFullYear();
                        if(initialType == 1){
                            month = $("#addpayDetails #buyMonth").val();
                            if(month == "") {
                                layer.msg('请选择月份');
                                return false;
                            }else{
                                var arr = month.split('月');
                                if(arr[0] < 10){
                                    date = year + '0' + arr[0];
                                }else{
                                    date = year + arr[0];
                                }
                            }
                        }else if(initialType == 2){
                            year = Number(year) - 1;
                            date = year + '01';
                        }else if(initialType == 3){
                            year = Number(year) - 2;
                            date = year + '01';
                        }
                        data['initialPeriod'] = date;
                    }
                }
                $("#qImages .imgsthumb").each(function () {
                    var path = {
                        'normal': $(this).find(".filePic").data('path')
                    };
                    imgsQ.push(path);
                })
                $("#pImages .imgsthumb").each(function () {
                    var path = {
                        'normal': $(this).find(".filePic").data('path')
                    };
                    imgsP.push(path);
                })
                imgsQ = JSON.stringify(imgsQ);
                imgsP = JSON.stringify(imgsP);
                data.qImages = imgsQ;
                data.pImages = imgsP;
                if($("#firstContactTime").val() != ''){
                    data.firstContactTime = $("#firstContactTime").val();
                }
                if($("#addpayDetails .receiveList tbody tr").length>0){
                    $("#addpayDetails .receiveList tbody tr").each(function () {
                        var json = {
                            'address': $(this).children().eq(1).html(),
                            'contact': $(this).children().eq(2).html(),
                            'mobile': $(this).children().eq(3).html()
                        }
                        shList.push(json);
                    })
                    shList = JSON.stringify(shList);
                    data.shAddressList= shList;
                }
                data.fpAddressList= $("#addAccount").data('mailInfo');
                data.contactsList= $("#addAccount").data('contactInfo');
                $.ajax({
                    url: '../special/getSaleCtrlsByMobile.do',
                    data: {mobile: data.supervisorMobile},
                    success: function (res) {
                        var state = res.data
                        if (state === 2) {
                            $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                            bounce_Fixed.show($("#tip"))
                            loading.close()
                        } else if (state === 1) {
                            $.ajax({
                                url: "../sales/addPdCustomer.do",
                                data: data,
                                success: function (res) {
                                    var status = res.status
                                    if (status === 1) {
                                        layer.msg("新增成功");
                                        var key = $(".main #se0").val();
                                        getCustomerMes(1 , 20, key);

                                        $("#newReceiveAddressInfo").data('id', '');
                                        $("#newReceiveAddressInfo").data('type', '');
                                        $("#newReceiveAddressInfo").data('source', '');
                                        $("#newMailInfo").data('type', '');
                                        $("#newMailInfo").data('id', '');
                                        $("#newMailInfo").data('source', '');
                                        $("#newContectInfo").data('type', '');
                                        $("#newContectInfo").data('id', '');
                                        $("#newContectInfo").data('source', '');

                                        $(".orgManage").data("cusInfo", res)
                                        $("#newCusOrg select, #newCusOrg input").val("").prop("disabled", false)
                                        $("#newCusOrg input[name='supervisorName']").val(res.supervisorName)
                                        $("#newCusOrg input[name='supervisorMobile']").val(res.supervisorMobile)
                                        $("#newCusOrg input[name='fullName']").val(res.fullName)
                                        $("#newCusOrg input[name='name']").val(res.name)

                                        setEveryTime(bounce_Fixed, 'newCusOrg')
                                        getProductOption().then(str => {
                                            $("#newCusOrg .productSelect").html(str)
                                            bounce_Fixed.show($("#newCusOrg"))
                                        })
                                    } else {
                                        $("#tip .tipMs").html("操作失败！");
                                        bounce_Fixed.show($("#tip"));
                                    }
                                }
                            })
                        } else {
                            layer.msg("系统错误！")
                        }
                    },
                    complete: function () {

                    }
                })
                break;
		}
    })
    $("#invoiceSet .setItem").click(function(){
        $("#invoiceSet .fa").attr("class","fa fa-circle-o");
        $(this).children(".fa").attr("class","fa fa-dot-circle-o");
    })
    $(".module_avatar").on("click", "input:checkbox", function () {
        var isChecked = $(this).prop("checked")
        $(this).closest(".module_row").children(".module_list").find("input:checkbox").prop("checked", isChecked)
        $(this).parents(".module_row").children(".module_item").find("input:checkbox").prop("checked", isChecked)
        if (!$(this).prop("checked") && ($(this).closest(".module_list").children().children(".module_item").find("input:checkbox:checked").length > 0)) {
            $(this).closest(".module_row").parents(".module_row").children(".module_item").find("input:checkbox").prop("checked", true)
        }
    })
    $("input[name='checkAll']").click(function () {
        $(".module_avatar input:checkbox").prop("checked", $(this).prop("checked"))
    })
    $("#updateCustomerPanel").on('click','.edit', function(){
        var name = $(this).data('name');
        var type = $(this).data('type');
        var source = $(this).data('source');
        switch (name) {
            case 'receiveStopData':
            case 'mailStopData':
                let sendData = {
                    "customerId": $("#updateCustomerPanel").data("id"),
                    "type": type
                }
                $("#stopDelContact tbody").children(":gt(0)").remove();
                let ttl = '' ;
                let ttltype = "" ;
                if (type =='1') {
                    ttl = "已被停用的收货信息"
                    ttltype = "shAddressRecords"
                } else if (type =='3') {
                    ttl = "已被停用的发票邮寄信息"
                    ttltype = "areaRecords"
                } else {
                    ttl = "已被停用的到货区域"
                    ttltype = "fpAddressRecords"
                }
                $("#stopDelContact .bonceHead span").html(ttl);
                $.ajax({
                    "url":"../sales/getSuspendAddress.do",
                    "data": sendData,
                    success:function (res) {
                        let list = res['addressList']|| [], status = res['status'],str='';
                        bounce_Fixed.show($("#stopDelContact"));
                        list.nullToStr();
                        for(let i in list){
                            let item = list[i];
                            str += `<tr data-id="${item.id}" data-isstop="ok">
                                    <td>${type =='2' ? item.contact: item.address} <span class="ty-right">${type =='2' ?item.mobile:''}</span></td>
                                    <td>
                                        <span class="edit2 link-red" data-id="${item.id}" type="btn" data-name="enableTurn" data-val="1">启用</span>
                                        <span class="link-blue" data-type="${ttltype}" onclick="getRecordList($(this))">修改记录</span>
                                    </td>
                                </tr>`;
                        }
                        $("#stopDelContact tbody").append(str);
                    }
                });
                break;
            case 'contactDelData':
                $("#stopDelContact tbody").children(":gt(0)").remove();
                $("#stopDelContact .bonceHead span").html("已被删除的联系人");
                $.ajax({
                    "url":"../sales/getDeleteContactsList.do",
                    "data": { "customerId": $("#updateCustomerPanel").data("id") },
                    success:function (res) {
                        let list = res['data']|| [], status = res['status'],str='';
                        bounce_Fixed.show($("#stopDelContact"));
                        list.nullToStr();
                        for(let i in list){
                            let item = list[i];
                            str += `<tr data-id="${item.id}" data-isstop="ok">
                                    <td>${item.name} <span class="ty-right">${item.post && item.post.substr(0,8) }</span></td>
                                    <td>
                                        <span class="edit2 link-blue" onclick="getRecordList($(this))" data-obj="update" data-type="contactRecords">修改记录</span>
                                    </td>
                                </tr>`;
                        }
                        $("#stopDelContact tbody").append(str);
                    }
                });
                break;
            case 'receiveInfo':
                var customerId = $("#updateCustomerPanel").data("id");
                $("#newReceiveAddressInfo").data('type', type);
                $("#newReceiveAddressInfo").data('source', source);
                if(type == 'new'){
                    $("#newReceiveAddressInfo .bonceHead span").html('新增收货地址');
                    if($(".shAddressItem").length >= 10){
                        layer.msg('最多可录入10条收货地址。');
                        break;
                    }
                    $("#newReceiveAddressInfo").data('id', customerId);
                    $("#newReceiveAddressInfo input").val("");
                    bounce_Fixed2.show($("#newReceiveAddressInfo"));
                    setTimer('updateReceive');
                }else if(type == 'update'){
                    $("#newReceiveAddressInfo .bonceHead span").html('修改收货地址');
                    var id = $(this).parents("tr").data('id');
                    $("#newReceiveAddressInfo").data('id', id);
                    $("#updateAddressTip").data('type', 1);
                    bounce_Fixed2.show($("#updateAddressTip"));
                }
                break;
            case 'updateAreaInfo':
                var customerId = $("#updateCustomerPanel").data("id");
                $("#newReceiveAreaInfo").data('type', type);
                $("#newReceiveAreaInfo").data('source', source);
                $("#newReceiveAreaInfo input").val("");
                $("#newReceiveAreaInfo .hd").html("");
                if(type == 'new'){
                    $("#newReceiveAreaInfo .bonceHead span").html('新增到货区域');
                    if($(".shAddressItem").length >= 10){
                        layer.msg('最多可录入10条收货地址。');
                        break;
                    }
                    $("#newReceiveAreaInfo").data('id', customerId);
                    bounce_Fixed2.show($("#newReceiveAreaInfo"));
                  //  setTimer('updateReceive');
                }else if(type == 'update'){
                    $("#newReceiveAreaInfo .bonceHead span").html('修改到货区域');
                    var id = $(this).parents("tr").data('id');
                    $("#newReceiveAreaInfo").data('id', id);
                    $("#updateAddressTip").data('type', 3);
                    bounce_Fixed2.show($("#updateAddressTip"));
                }
                break;
            case 'mailInfo':
                var customerId = $("#updateCustomerPanel").data("id");
                $("#newMailInfo").data('type', type);
                $("#newMailInfo").data('source', source);
                if(type == 'new'){
                    $("#newMailInfo .bonceHead span").html('新增发票邮寄信息');
                    if($(".mailAddressItem").length >= 10){
                        layer.msg('最多可录入10条发票邮寄信息。');
                        break;
                    }
                    $("#newMailInfo").data('id', customerId);
                    $("#newMailInfo input").val("");
                }else if(type == 'update'){
                    $("#newMailInfo .bonceHead span").html('修改发票邮寄信息');
                    var id = $(this).parents("tr").data('id');
                    $("#newMailInfo").data('id', id);
                    var data = {
                        addressId: id,
                        type: '2'
                    }
                    setAddress(2, data);
                }
                bounce_Fixed.show($("#newMailInfo"));
                setTimer('updateMail');
                break;
            case 'contactInfo':
                var customerId = $("#updateCustomerPanel").data("id");
                $("#newContectInfo").data('type', type);
                $("#newContectInfo").data('source', source);
                document.getElementById('newContectData').reset();
                $(".otherContact").html("");
                $('#uploadCard').show();
                $("#contactsCard .bussnessCard").remove();
                $("#uploadCard").html("")
                initCardUpload($("#uploadCard"));
                if(type == 'new'){
                    $("#newContectInfo .bonceHead span").html('新增客户联系人');
                    $("#contactFlag").html('其他');
                    if($(".contactItem").length >= 50){
                        layer.msg('最多可录入50条联系人。');
                        break;
                    }
                    $("#newContectInfo").data("id", customerId);
                    $("#addMoreContact").hide();
                }else if(type == 'update'){
                    $("#newContectInfo .bonceHead span").html('修改客户联系人');
                    var contactId = $(this).parents("tr").data('id');
                    $("#newContectInfo").data('id', contactId);
                    $.ajax({
                        url: '../sales/getContactsSocial.do',
                        data: {
                            'contactId': contactId
                        },
                        success: function (data) {
                            var status = data.status;
                            if (status === '1' || status === 1) {
                                var getData = data.data;
                                setContact(getData);
                            } else {
                                layer.msg("停止失败！");
                            }
                        }
                    })
                }
                bounce_Fixed3.show($("#newContectInfo"));
                setTimer('updateContact');
                break;
        }
    })
    $("#chooseCusContact").on("click", '.fa', function () {
        $("#chooseCusContact").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o")
    })
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
    $("input[name='fullName']").on('blur', function () {
        var name = $(this).val()
        name = name.slice(0,6)
        $(this).parents("table").find("input[name='name']").val(name)
    })
    // 上传的合同删除
    $("#newContractInfo").on("click", '.fa-times', function () {
        let info = JSON.parse($(this).siblings(".hd").html())
        let fileUid = info.fileUid
        cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
        if ($(this).parents(".fileCon1").length > 0 && info.id) {
            let fileItem = $(this).parent(".fileIm").html()
            $("#newContractInfo .deleteFile").append( `<span class="fileItem">${fileItem}</span>`)
        }
        $(this).parent(".fileIm").remove();
    })

    $('#uploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.*;',
        multi:false,
        formData:{
            module: '销售管理',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:10240,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader: "../uploads/uploadfyByFile.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item matListUpload" style="display: none">' +
        '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
        '</div>',
        onUploadStart:function(){
        },
        onInit:function(){},
        onSelect:function (file) {
            $('.matListUpload:not(:last)').remove();
            $('#custormerLeading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            bounce_Fixed2.show($("#importantTip"));
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data);
            var filePath = data.filename;
            var fileUid =data.fileUid;
            let json = {
                "fileUid": fileUid,
                "path": filePath
            }
            importCustomer(json);
        } ,
        onCancel:function(file){
        }
    });
    $("#receiveInfo").on('click','.checkItem', function(){
        if($(this).find(".fa-circle-o").length > 0){
            $(this).find("i").attr("class","fa fa-circle").parent().siblings().children("i").attr("class","fa fa-circle-o")
            let type = $(this).find("i").data("type")
            if (type === 1) {
                $("#receiveInfo .receiveMethod").hide()
            } else {
                $("#receiveInfo .receiveMethod").show()
            }
        } else {
            $(this).find("i").attr("class","fa fa-circle-o")
            let type = $(this).find("i").data("type")
            if (type === 2) {
                $("#receiveInfo .receiveMethod").hide()
            }
        }
    });
}) ;

// create:hxz 2021-06-01 合同 删除合同 确定
function contractDelTipOk() {
    let cInfo = JSON.parse( contractDel.siblings(".hd").html() );
    if(cInfo.fileCon1.length > 0){
        let groupUuid =cInfo.fileCon1[0].groupUuid;
        cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
    }
    if(cInfo.fileCon2.length > 0){
        let groupUuid =cInfo.fileCon2[0].groupUuid;
        cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
    }
    contractDel.parent().parent().remove();
    bounce_Fixed3.cancel()
}
// creator : 2021-5-24 hxz  合同的商品列表
function cScanScanGs(cid) {
    $.ajax({
        'url' : "../sales/getContractBase.do",
        "data":{ "id": cid },
        success:function (res) {
            var newcInfo = res.data
            var status = res.status
            if(status != 1){
                layer.msg("获取合同信息失败！");
                return false;
            }
            $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
            $(".addOrCancel").hide(); $(".cScanc").show();
            let productList = newcInfo.productList || []
            setContactGS(productList , false);
            $("#cGTip").html(`本合同下的商品共有以下${ productList.length }种`)

        }
    })
}

// creator : 2021-5-24 hxz  合同详情
function cScan(contractId) {
    $.ajax({
        url: $.webRoot + '/sales/contractBaseMes.do',
        data:{ id: contractId }
    }).then(res => {
        let data = res.data
        let contractBase = data.contractBase // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
        let listImage = data.listImage || [] // 合同的扫描件或照片
        let listZS = data.listZS || [] // 专属商品
        let listTY = data.listTY || [] // 通用型商品
        let listHis = data.listHis || [] // 暂停恢复记录
        bounce_Fixed2.show($("#cScan"))
        let imageStr = ''
        for (let i in listImage) {
            imageStr += `<span class="link-blue fileImScan" data-fun="imgScan" path="${listImage[i].uplaodPath}">${Number(i) + 1 } <i class="hd">${JSON.stringify(listImage[i])}</i></span>`
        }
        let fileStr = ''
        if (contractBase.filePath) {
            fileStr = ` <a class="link-blue cWord node" data-fun="cWord" path="${contractBase.filePath}">查看</a>`
        }
        let showData = {
            sn: contractBase.sn,
            signTime: contractBase.signTime?moment(contractBase.signTime).format("YYYY-MM-DD"):'',
            validTime: moment(contractBase.validStart).format("YYYY-MM-DD") + ' 至 ' + moment(contractBase.validEnd).format("YYYY-MM-DD"),
            memo: contractBase.memo,
            image: imageStr,
            file: fileStr,
            zsNum: listZS.length,
            tyNum: listTY.length,
            create: contractBase.createName + ' ' + moment(contractBase.createDate).format("YYYY-MM-DD HH:mm:ss")
        }
        for (let key in showData) {
            $("#cScan .contract_see_" + key).html(showData[key])
        }
        $("#cScan").data("contractInfo", data)
        let suspendStr = ''
        listHis.forEach(function (enIm) {
            suspendStr += `
                <p>
                    <span>${ enIm.suspend ? "暂停履约/终止合作" : "恢复履约/重启合作" }</span>
                    <span class="enName">${ enIm.createName }</span>
                    <span>${ moment(enIm.suspendTime).format("YYYY-MM-DD HH:mm:ss") }</span>
                </p>`;
        })
        $("#cScan .enabledList").html(suspendStr)
    })
}

// creator: 张旭博，2024-07-09 04:08:40， 查看修改记录或者续约记录的合同详情 
function seeContractHisDetail(contractHisId, type) {
    let url = '', data = {}
    if (type === 'change') {
        url = $.webRoot + '/sales/contractBaseHisMes.do'
        data = { contractHisId: contractHisId }
    } else {
        url = $.webRoot + '/sales/contractBaseMes.do'
        data = { id: contractHisId }
    }
    $.ajax({
        url: url,
        data: data
    }).then(res => {
        let data = res.data
        let contractBase = {} // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
        let listImage = [] // 合同的扫描件或照片
        let listZS = [] // 专属商品
        let listTY = [] // 通用型商品
        if (type === 'change') {
            data.contractBase = data.contracthistory
            data.listImage = data.listHisImage || []
            data.listZS = data.listHisZS || []
            data.listTY = data.listHisTY || []
        }
        contractBase = data.contractBase
        listImage = data.listImage || []
        listZS = data.listZS || []
        listTY = data.listTY || []
        let imageStr = ''
        for (let i in listImage) {
            imageStr += `<span class="link-blue fileImScan" data-fun="imgScan" path="${listImage[i].uplaodPath}">${Number(i) + 1 } <i class="hd">${JSON.stringify(listImage[i])}</i></span>`
        }
        let fileStr = ''
        if (contractBase.filePath) {
            fileStr = ` <a class="link-blue cWord node" data-fun="cWord" path="${contractBase.filePath}">查看</a>`
        }
        let showData = {
            sn: contractBase.sn,
            signTime: contractBase.signTime?moment(contractBase.signTime).format("YYYY-MM-DD"):'',
            validTime: moment(contractBase.validStart).format("YYYY-MM-DD") + ' 至 ' + moment(contractBase.validEnd).format("YYYY-MM-DD"),
            memo: contractBase.memo,
            image: imageStr,
            file: fileStr,
            zsNum: listZS.length,
            tyNum: listTY.length
        }
        if (type === 'change') {
            bounce_Fixed3.show($("#cScanHis"))

            for (let key in showData) {
                $("#cScanHis .contract_see_" + key).html(showData[key])
            }
        } else {
            bounce_Fixed2.show($("#cScan"))

            for (let key in showData) {
                $("#cScan .contract_see_" + key).html(showData[key])
            }
            $("#cScan")
        }

        $("#cScan").data("contractInfo", data)
    })
}
// creator : 2021-5-24 hxz 编辑合同（新增、修改、续约）
function contractInfoEdit(type, source, thisObj) {
    // type =new , source=addCustomer
    $("#newContractInfo").data('type', type).data('source', source);
    bounce_Fixed.show($("#newContractInfo"));
    let cusInfo  = $("#updateCustomerPanel").data("cusInfo")

    $("#newContractInfo input").val("")
    $("#newContractInfo .fileCon>div").html("")
    $("#newContractInfo .deleteFile").html('')
    initCUpload2($("#cUpload1"),'img');
    initCUpload2($("#cUpload2"),'doc');

    $(".cRenewalTip").hide();
    $("#newContractInfo .scanGs").data('gsArr',[]).data('gsArrNo',[]).html("0") ;

    if(source === 'updateCustomer'){
        // 修改
        $(".cGSZ").show()
        $(".cusItem").show()
        $("#newContractInfo [name='customerName']").val(cusInfo.fullName)
        // // 获取所有商品数据（包括专属商品和通用型商品）
        // $.ajax({
        //     url: $.webRoot + '/sales/insertCommodityByTypeForContract.do',
        //     data: {
        //         customer: cusInfo.id
        //     }
        // }).then(res => {
        //     let data = res.data
        //     $("#newContractInfo").data('productListZS',data.productListZS) ;
        //     $("#newContractInfo").data('productListTY',data.productListTY) ;
        // })
    } else {
        $(".cGSZ").hide()
        $(".cusItem").hide()
    }
    if(type === 'new'){
        $("#newContractInfo .bonceHead span").html("新增合同")
        if (source === 'addCustomer') {
            getContractByCustomer()
                .then(data => {
                    $("#newContractInfo").data('productListTY',data.productListTY) ;
                    renderEditProductTy()
                })
        } else {
            getContractByCustomer(cusInfo.id)
                .then(data => {
                    $("#newContractInfo").data('productListZS',data.productListZS) ;
                    $("#newContractInfo").data('productListTY',data.productListTY) ;
                    renderEditProductZS()
                    renderEditProductTy()
                })
        }
    } else if(type === 'update'){
        $("#newContractInfo .bonceHead span").html("修改合同")
    }else if(type === 'cRenewal' ||type === 'cRenewalHistory'||type === 'cRenewalStop'){
        $("#newContractInfo .bonceHead span").html("合同续约")
        $(".cRenewalTip").show();
    }

    if(type === 'update' || type === 'cRenewal' || type === 'cRenewalHistory'|| type === 'cRenewalStop'){
        editContractObj = thisObj
        if(source === 'addCustomer'){
            var newcInfo = JSON.parse(thisObj.siblings(".hd").html());
            let tyGoods = newcInfo.tyGoods
            getContractByCustomer()
                .then(data => {
                    let productListTY = data.productListTY
                    productListTY.map(item => {
                        item.isChecked = false
                        tyGoods.map(val => {
                            if (item.id === val.commodity) {
                                item.isChecked = true
                            }
                        })
                    })
                    $("#newContractInfo").data('productListTY', productListTY) ;
                    renderEditProductTy()
                })
            $("#newContractInfo .cNo").val(newcInfo.cNo)
            $("#newContractInfo .cSignDate").val(newcInfo.cSignDate)
            $("#newContractInfo .cStartDate").val(newcInfo.cStartDate)
            $("#newContractInfo .cEndDate").val(newcInfo.cEndDate)
            $("#newContractInfo .cMemo").val(newcInfo.cMemo)
            let imgStr1 = ``
            newcInfo.fileCon1.forEach(function(data){
                imgStr1 += `<span class="fileIm" >
                                <span>${data.orders}</span>
                                <span class="fa fa-times"></span>
                                <span class="hd">${JSON.stringify(data) }</span>
                            </span>`
            })
            $("#newContractInfo .fileCon1").append(imgStr1)
            let imgStr2 = ``
            newcInfo.fileCon2.forEach(function(data){
                imgStr2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data)}</span>
                         </span>`
            })
            $("#newContractInfo .fileCon2").html(imgStr2)


        }
        else {
            // 接口获取合同详情
            let cid = 0
            if (!thisObj) {
                cid = $("#cEndTip").data("cid")
            } else {
                cid = thisObj.parents(".contractItem").data("id") ;
            }
            $("#newContractInfo").data("cid",cid)
            $.ajax({
                url : $.webRoot + "/sales/contractBaseMes.do",
                data: { id: cid },
                success:function (res) {
                    let data = res.data
                    let contractBase = data.contractBase
                    let listImage = data.listImage
                    let listTY = data.listTY || []
                    let listZS = data.listZS || []
                    var success = res.success
                    if(success !== 1){
                        layer.msg("获取原来的合同信息失败！");
                        return false;
                    }
                    $("#newContractInfo .cNo").val(contractBase.sn)
                    $("#newContractInfo .cSignDate").val(contractBase.signTime?(new Date(contractBase.signTime).format("yyyy-MM-dd")):'' )
                    let start = new Date(contractBase.validStart).format("yyyy-MM-dd")
                    let end = new Date(contractBase.validEnd).format("yyyy-MM-dd")
                    $("#newContractInfo .cStartDate").val( start ).data("old", start);
                    $("#newContractInfo .cEndDate").val( end ).data("old", end);
                    $("#newContractInfo .cMemo").val(contractBase.memo);
                    let imgStr1 = ``
                    for(let i in listImage) {
                        imgStr1 += `<span class="fileIm"  >
                                       <span>${Number(i) + 1}</span>
                                       <span class="fa fa-times"></span>
                                       <span class="hd">${JSON.stringify(listImage[i]) }</span>
                                  </span>`
                    }
                    $("#newContractInfo .fileCon1").append(imgStr1)
                    let imgStr2 = ``
                    if( contractBase.filePath &&  contractBase.filePath.length > 0){ // 下面的（id: 1）是用来区分是否修改用的
                        imgStr2 = `<span class="fileIm"  >
                                       <span class="fa fa-file-word-o"></span>
                                       <span class="fa fa-times"></span>
                                       <span class="hd">${ JSON.stringify({ id: 1, filename : contractBase.filePath , originalFilename: contractBase.fileName })  }</span>
                                  </span>`
                    }
                    $("#newContractInfo .fileCon2").html(imgStr2)
                    getContractByCustomer(cusInfo.id)
                        .then(data => {
                            let productListZS = data.productListZS || []
                            let productListTY = data.productListTY || []
                            productListZS.map(item => {
                                item.isChecked = false
                                listZS.map(val => {
                                    if (item.id === val.id) {
                                        item.isChecked = true
                                    }
                                })
                            })
                            productListTY.map(item => {
                                item.isChecked = false
                                listTY.map(val => {
                                    if (item.id === val.id) {
                                        item.isChecked = true
                                    }
                                })
                            })
                            $("#newContractInfo").data('productListZS',data.productListZS) ;
                            $("#newContractInfo").data('productListTY',data.productListTY) ;
                            renderEditProductZS()
                            renderEditProductTy()
                        })
                }
            })
        }
    }
}

function getContractByCustomer(id) {
    let data = {}
    if (id) {
        data.customer = id
    }
    return new Promise((resolve, reject) => {
        $.ajax({
            url: $.webRoot + '/sales/insertCommodityByTypeForContract.do',
            data: data
        }).then(res => {
            let data = res.data
            resolve(data)
        })
    })
}

// creator: 张旭博，2024-07-03 03:16:34， 合同修改记录
function getContractChangeLog(id) {
    $.ajax({
        url: $.webRoot + '/sales/contractBaseHistory.do',
        data: {
            id: id
        }
    }).then(res => {
        let list = res.data.list || []
        let str = ''
        for (let i in list) {
            let item = list[i]
            let create = ''
            if (i === '0') {
                create = item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")
            } else {
                create = item.updateName + ' ' + moment(item.updateDate).format("YYYY-MM-DD HH:mm:ss")
            }
            str += `<tr data-id="${item.id}">
                        <td>${i==='0'?'本版本合同的原始信息':'第' + i + '次修改后'}</td>
                        <td>
                            <span class="link-blue" data-name="seeContractChangeHisDetail" type="btn">查看</span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                        <td>${create}</td>
                    </tr>`
        }
        let lastItem = list[list.length-1]
        $("#contractChangeLog tbody").html(str)
        let tips = ''
        if (list.length === 1) {
            tips = '当前数据尚未修改'
        } else {
            tips = ` 当前数据为本版本合同第${list.length-1}次修改后的结果。
                    <div class="ty-right">
                        修改时间：${lastItem.updateName + ' ' + moment(lastItem.updateDate).format("YYYY-MM-DD HH:mm:ss")}
                    </div>`
        }
        $("#contractChangeLog .tips").html(tips)
        bounce_Fixed.show($("#contractChangeLog"))
        bounce_Fixed2.cancel()
    })
}

// creator: 张旭博，2024-07-03 03:16:34， 合同续约记录
function getContractRenewLog(id) {
    $.ajax({
        url: $.webRoot + '/sales/contractBaseSignRecord.do',
        data: {
            primaryId: id
        }
    }).then(res => {
        let list = res.data.list || []
        let str = ''
        for (let i in list) {
            let item = list[i]
            let create = item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")
            str += `<tr data-id="${item.id}">
                        <td>${i==='0'?'第1版（原始版本）':'第' + (Number(i) + 1) + '版（第' + i + '次续约后）'}</td>
                        <td>
                          <span class="link-blue" data-name="seeContractRenewHisDetail" type="btn">查看</span>
                          <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                        <td>${create}</td>
                    </tr>`
        }
        let lastItem = list[list.length-1]
        $("#contractRenewLog tbody").html(str)
        bounce_Fixed.show($("#contractRenewLog"))
        bounce_Fixed2.cancel()
    })
}

 // creator : 2021-5-24 hxz 已暂停/终止的合同
 function contractStopData(customer) {
     $.ajax({
         url: $.webRoot + "/sales/listContractBase.do",
         data: { customer: customer, type: 2 },
         success:function (res) {
             let data = res.data || []
             let list = data.contractBaseList
             let str = ``;
             list.forEach(function (item) {
                 str += `
                  <tr class="contractItem" data-id="${item.id}">
                    <td>${ item.sn }</td>
                    <td>${ new Date(item.suspendTime).format("yyyy-MM-dd hh:mm:ss")  }</td>
                    <td>
                        <span class="link-blue edit2" type="btn" data-name="cScan">查看</span>
                        <span class="link-blue edit2" type="btn" data-name="cRestart"> 恢复履约/重启合作</span>
                        <span class="hd">${ JSON.stringify(item)}</span>
                    </td>
                </tr>
                 `;
             })
             $("#contractStopData tbody").html(str)
             bounce_Fixed.show($("#contractStopData"));
         }
     })
 }
 // creator : 2021-5-24 hxz 已到期的合同
 function contractEndData() {
    let customer = $("#updateCustomerPanel").data("id")
     $.ajax({
         url: $.webRoot + "/sales/listContractBase.do",
         data: { customer: customer, type: 3 },
     }).then(res => {
         let data = res.data || []
         let list = data.contractBaseList
         let str = ``;
         list.forEach(function (item) {
             str += `
                  <tr class="contractItem" data-id="${item.id}">
                    <td>${ item.sn }</td>
                    <td>${ moment(item.validEnd).format("YYYY-MM-DD")  }</td>
                    <td>
                        <span class="link-blue edit2" type='btn' data-name="cScan">查看</span>
                        <span class="link-blue edit2" type='btn' data-name="cRenewal">续约</span>  
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                 `;
         })
         $("#contractEndData tbody").html(str)
         bounce_Fixed.show($("#contractEndData"))
     })
 }
 // creator : 2021-5-24 hxz 暂停履约/合同终止
 function cEndOk(type) {
     let id = $("#cEndTip").data("cid")
     let enabled = $("#cEndTip").data("enabled")
     if (enabled === 1) {
         $.ajax({
             url: $.webRoot + '/sales/reStartContract.do',
             data: { id: id, type: type  }
         }).then(res => {
             let data = res.data
             let state = data.state
             if(state === 1){
                 layer.msg("操作成功！")
                 var cusid = $("#updateCustomerPanel").data("id")
                 if(enabled == 1){
                     bounce_Fixed.cancel()
                     // contractStopData(cusid)
                 }
                 indexUpdateList(cus_seeTrObj);

             } else if (state === 0) {
                 layer.msg('请勿重复操作！')
             } else {
                 layer.msg('操作失败！')
             }
             bounce_Fixed3.cancel();
         })
     } else {
         $.ajax({
             url: $.webRoot + '/sales/terminateContract.do',
             data: { id: id  }
         }).then(res => {
             let data = res.data
             let state = data.state
             if(state === 1){
                 layer.msg("操作成功！")
                 var cusid = $("#updateCustomerPanel").data("id")
                 if(enabled == 1){
                     bounce_Fixed.cancel()
                     // contractStopData(cusid)
                 }
                 indexUpdateList(cus_seeTrObj);

             } else if (state === 0) {
                 layer.msg('请勿重复操作！')
             } else {
                 layer.msg('操作失败！')
             }
             bounce_Fixed3.cancel();
         })
     }

 }

 // creator: 张旭博，2024-07-09 10:09:59， 恢复合同
function cRestartOk() {

    $.ajax({
        url: $.webRoot + '/sales/reStartContract.do',
        data: { id: id }
    }).then(res => {
        let state1 = res.data.state // state 0-不要重复启用 1-没有过期直接恢复 2-已经过期了
        if (state1 === 0) {
            layer.msg("请勿重复操作！")
        } else if (state1 === 2) {
            layer.msg("合同已过期！")
        } else if (state1 === 1) {
            $
        }
    })
}
// creator : 2021-5-24 hxz 合同商品 操作 确定
 function addOrCancelOk() {
     let origin = $("#tipcontractGoods").data("origin");
     let productListZS = $("#newContractInfo").data('productListZS') || []
     let productListTY = $("#newContractInfo").data('productListTY') || []
     if (origin === 'addZsGs') {
         productListZS.map(item => item.isChecked = false)
     }
     if (origin === 'addTyGs') {
         productListTY.map(item => item.isChecked = false)
     }
     let editProductZS = []
     let editProductTY = []
     let listEdit = []
     $("#tipcontractGoods input:checkbox:checked").each(function(){
         let id = $(this).parents("tr").data("id")
         if (origin === 'addZsGs') {
             productListZS.map(item => {
                 if (item.id === id) {
                     item.isChecked = true
                 }
             })
         }
         if (origin === 'addTyGs') {
             productListTY.map(item => {
                 if (item.id === id) {
                     item.isChecked = true
                 }
             })
         }
         if (origin === 'removeZsGs') {
             productListZS.map(item => {
                 if (item.id === id) {
                     item.isChecked = false
                 }
             })
         }
         if (origin === 'removeTyGs') {
             productListTY.map(item => {
                 if (item.id === id) {
                     item.isChecked = false
                 }
             })
         }
     })
     if (origin === 'addZsGs' || origin === 'removeZsGs') {
         renderEditProductZS()
     }
     if (origin === 'addTyGs' || origin === 'removeTyGs') {
         renderEditProductTy()
     }
     bounce_Fixed3.cancel()
     // // 更新数据存储
     // let listYes =  $("#newContractInfo .scanGs").data('gsArr');
     // let listNo =  $("#newContractInfo .scanGs").data('gsArrNo') || [];
     // if(fun === "addGs"){
     //     listYes.push(...listEdit)// 将新增的添加到 选中的
     //     let listNew = []
     //     listNo.forEach(function(noLm, index){ // 从未选中中 去掉 新增的
     //         let has = false
     //         listEdit.forEach(function(noLm2){
     //            if(noLm.id == noLm2.id){
     //                has = true
     //            }
     //         })
     //         if(!has){
     //             listNew.push(noLm)
     //         }
     //     })
     //     listNo = listNew
     // } else if(fun === "removeGs"){
     //     listNo.push(...listEdit)
     //     let listNew = []
     //     listYes.forEach(function(noLm, index){ // 从未选中中 去掉 新增的
     //         let has = false
     //         listEdit.forEach(function(noLm2){
     //             if(noLm.id == noLm2.id){
     //                 has = true
     //             }
     //         })
     //         if(!has){
     //             listNew.push(noLm)
     //         }
     //     })
     //     listYes = listNew
     // }
     // $("#newContractInfo .scanGs").data('gsArr', listYes).data('gsArrNo', listNo).html(listYes.length);
 }

 function renderEditProductZS() {
     let productListZS = $("#newContractInfo").data('productListZS') || []
     let editProductZS = productListZS.filter(item => item.isChecked)
     let nameArr = editProductZS.map(item => item.outerName)
     $("#newContractInfo .zsGoodNumber").html(nameArr.length)
     $("#newContractInfo .zsGoodList").html(nameArr.join("、"))
 }
 function renderEditProductTy() {
     let productListTY = $("#newContractInfo").data('productListTY') || []
     let editProductTY = productListTY.filter(item => item.isChecked)
     let nameArr = editProductTY.map(item => item.outerName)
     $("#newContractInfo .tyGoodNumber").html(nameArr.length)
     $("#newContractInfo .tyGoodList").html(nameArr.join("、"))
 }
 // creator : 2021-5-24 hxz 渲染合同里商品的列表
 function setContactGS(list, boolSet, isHasCheck) {
     let str = '';
     let cstr = '';
     if(boolSet){
         $("#tipcontractGoods .selectTd").show()
         $("#tipcontractGoods .addOrCancel").show()
         $("#tipcontractGoods .cScanc").hide()
         $("#tipcontractGoods .countStr").show()
     } else {
         $("#tipcontractGoods .selectTd").hide()
         $("#tipcontractGoods .addOrCancel").hide()
         $("#tipcontractGoods .cScanc").show()
         $("#tipcontractGoods .countStr").hide()
     }
     list = list || [];
     list.forEach(function (im) {
         let isCheckedStr = im.isChecked && isHasCheck?'checked':''
         let handleStr = boolSet?`<td><div class="ty-checkbox"><input type="checkbox" name="goods" id="good_${im.id}" ${isCheckedStr}/><label for="good_${im.id}"></label></div></td>`:''
         str += `<tr data-id="${im.id}">
                    ${handleStr}
                    <td>${im.outerSn}</td>
                    <td>${im.outerName}</td>
                    <td>${im.model}</td>
                    <td>${im.specifications}</td>
                    <td>${im.unit}</td>
                    <td>
                        <span class="link-blue" onclick="includeGoodContract($(this))">${im.contractNum || 0}个</span>
                        <span class="hd">${JSON.stringify(im)}</span>
                    </td>
                </tr>`
     })
     $("#tipcontractGoods table tbody").html(str);
     bounce_Fixed3.show($("#tipcontractGoods"));
 }

 // creator: 张旭博，2024-06-28 01:28:46， 包含某商品的合同
function includeGoodContract(selector) {
    let goodInfo = JSON.parse(selector.siblings(".hd").html())
    // let cusInfo = $("#updateCustomerPanel").data("cusInfo")
    let goodArr = [goodInfo.outerSn, goodInfo.outerName, goodInfo.model, goodInfo.specifications, goodInfo.unit]
    // if (cusInfo) {
    //     let cusArr = [cusInfo.code, cusInfo.fullName]
    //     $("#includeGoodContract .cusInfo").html([...cusArr].filter(item => item || item === 0).join(" / "));
    //     $("#includeGoodContract .cusInfo").show()
    // } else {
    //     $("#includeGoodContract .cusInfo").html('');
    //     $("#includeGoodContract .cusInfo").hide()
    // }
    $("#includeGoodContract .goodInfo").html([...goodArr].filter(item => item || item === 0).join(" / "));

    bounce_Fixed4.show($("#includeGoodContract"));
    $.ajax({
        url: $.webRoot + '/sales/listContractByCommodity.do',
        data: {
            id: goodInfo.id
        }
    }).then(res => {
        let data = res.data
        let list = data.list
        let str = ''
        for (let item of list) {
            str += `<tr>
                    <td>${item.sn}</td>
                    <td>${item.commodityCount}</td>
                    <td>${moment(item.validStart).format("YYYY-MM-DD")}至${moment(item.validEnd).format("YYYY-MM-DD")}</td>
                    <td>${item.signTime?(moment(item.signTime).format("YYYY-MM-DD")):''}</td>
                    <td>${item.createName} ${moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                </tr>`
        }
        $("#includeGoodContract tbody").html(str)
    })
}
// creator: hxz 2021-05-21 操作上传的合同
function fileControl(obj){
    let funName = obj.data("fun");
    let fInfo = obj.siblings(".fInfo").html();
    fInfo = JSON.parse(fInfo)

}
// creator: hxz 2021-05-21 编辑合同 上传合同文件
function initCUpload2(obj,type){
    let fileTypeExtsStr = ''
    let multi = true
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type == "doc"){
        multi = false
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``
    obj.html("")
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText:"上传",
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data)
            if(type == "img"){
                let len =  $(`.fileCon1`).find(".fileIm").length;
                if(len < 9){
                    data.orders = len + 1
                    data.filePath = data.filename
                    data.title = data.originalFilename
                    let imgStr1 = `<span class="fileIm"  >
                                 <span>${ 1 + len }</span>
                                 <span class="fa fa-times"></span>
                                 <span class="hd">${ JSON.stringify(data) }</span>
                            </span>`
                    $(`.fileCon1`).append(imgStr1);
                }else{
                    layer.msg('最多只能上传9个文件')
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                }
            }else if(type == "doc"){
                let len =  $(`.fileCon2`).find(".fileIm").length;
                if(len === 0){
                }else{
                    let delO = $(`.fileCon2`).find(".fileIm")
                    let info = JSON.parse(delO.find(".hd").html())
                    let fileUid = info.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                    delO.remove();
                }
                let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                $(`.fileCon2`).html(str2);
            }
        }
    })
}

// creator: hxz 2021-05-21 编辑合同确定
function editContractOk(num) {
    let type = $("#newContractInfo").data('type'); // new新增 update修改 cRenewal 续约
    let source = $("#newContractInfo").data('source');
    if(num === 0){ // 取消, 删除上传的文件
        bounce_Fixed.cancel()
        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
        if(fileImArr.length > 0){
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }
        let file2= $("#newContractInfo .fileCon2 .fileIm")
        if(file2.length > 0){
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }

    }else{ // 确定
        let info = {}
        info.cNo = $("#newContractInfo .cNo").val()

        info.cSignDate = $("#newContractInfo .cSignDate").val()
        info.cStartDate = $("#newContractInfo .cStartDate").val()
        info.cEndDate = $("#newContractInfo .cEndDate").val()

        if(info.cNo.length === 0){
            layer.msg('请录入合同编号！');
            return false
        }
        if(!info.cStartDate || !info.cEndDate) {
            layer.msg('请选择合同的有效期！');
            return false
        }
        info.cMemo = $("#newContractInfo .cMemo").val()
        if(type === "cRenewal" || type === "cRenewalHistory" || type === 'cRenewalStop'){
            let cStartDateOld = $("#newContractInfo .cStartDate").data("old");
            let ccEndDateOld = $("#newContractInfo .cEndDate").data("old");
            let endDateOld = ccEndDateOld || cStartDateOld
            let endOld = Date.parse(new Date(ccEndDateOld));
            if(endDateOld){
                if(info.cStartDate){
                    let start = Date.parse(new Date( info.cStartDate));
                    if(endOld >= start){
                        layer.msg(`合同有效期开始时间应晚于${ccEndDateOld}`);
                        return false
                    }
                }else{
                    layer.msg(`请录入晚于${ccEndDateOld}的合同有效期开始时间`)
                    return false
                }
            }
        }
        if(info.cStartDate && info.cEndDate){
            let start = Date.parse(new Date( info.cStartDate));
            let end = Date.parse(new Date( info.cEndDate));
            if(start > end){
                layer.msg('合同有效期开始时间应早于结束时间');
                return false
            }
        }

        info.fileCon1 = [];
        $("#newContractInfo .fileCon1 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon1.push(JSON.parse(itemf))
        })
        info.fileCon2 = [];
        $("#newContractInfo .fileCon2 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon2.push(JSON.parse(itemf))
        })
        info.goodList = [];
        $("#newContractInfo .goodList .gsIm").each(function () {
            let itemg = $(this).find(".hd").html();
            info.goodList.push(JSON.parse(itemg))
        })
        let productListTY = $("#newContractInfo").data('productListTY') || []
        let editProductTY = productListTY.filter(item => item.isChecked)
        let tyGoods = editProductTY.map(item => {return {commodity: item.id}})
        info.tyGoods = tyGoods
        if(source == "addCustomer"){
            let str = `
                <td>${info.cNo}</td>
                <td>${info.cSignDate}</td>
                <td>${info.cStartDate} 至 ${info.cEndDate}</td>
                <td>
                    <span class="link-blue node" data-name="contractInfo" data-type="update" data-source="addCustomer">修改</span>
                    <span class="link-red node" data-name="contractDel">删除</span>
                    <span class="hd" >${ JSON.stringify(info) }</span>
                </td>
                `
            if(type == "new"){
                $("#clist").append(`<tr>${str}</tr>`);
                $(".contractList").show();
                bounce_Fixed.cancel()

            } else if(type == "update"){
                var trObj = editContractObj.parent().parent();
                trObj.html(str)
                bounce_Fixed.cancel()
            }
        }else  if (source == "updateCustomer"){


            let url = '', data = { 'customerId': $("#updateCustomerPanel").data("id") }
            let filePath = '',fileName = '';
            if( info.fileCon2.length > 0  ){
                filePath = info.fileCon2[0].filename
                fileName = info.fileCon2[0].originalFilename
            }
            let imgs = []
            if(info.fileCon1 && info.fileCon1.length > 0){
                info.fileCon1.forEach(function(im, index){
                    let imgItem = {
                        uplaodPath: im.filePath || im.uplaodPath,
                        type: 1, // 类型:1-图片,2-视频,3-文档
                        title: im.title
                    }
                    if (type === 'update') {
                        if (im.id) {
                            imgItem.id = im.id
                            imgItem.operation = 4 // 标签 1 用于表示新增 4 表示没动 2表示删除  ， 此处只有修改的时候需要传变化（即operation不是1的时候都需要传id）
                        } else {
                            imgItem.operation = 1
                        }
                    } else {
                        imgItem.operation = 1
                    }
                    imgs.push(imgItem)
                })
            }
            if (type === 'update') {
                $("#newContractInfo .deleteFile .fileItem").each(function () {
                    let info = JSON.parse($(this).find(".hd").html());
                    imgs.push({
                        uplaodPath: info.filePath || info.uplaodPath,
                        type: 1, // 类型:1-图片,2-视频,3-文档
                        title: info.title,
                        operation: 2, // 标签 4表示删除的文件
                        id: info.id
                    })
                })
            }

            let cusInfo  = $("#updateCustomerPanel").data("cusInfo")
            let productListTY = $("#newContractInfo").data('productListTY') || []
            let editProductTY = productListTY.filter(item => item.isChecked)
            let productListZS = $("#newContractInfo").data('productListZS') || []
            let editProductZS = productListZS.filter(item => item.isChecked)
            let zsGoods = editProductZS.map(item => {return {commodity: item.id}})
            let tyGoods = editProductTY.map(item => {return {commodity: item.id}})
            console.log('zsGoods', zsGoods)
            console.log('tyGoods', tyGoods)
            data = {
                customer: cusInfo.id,
                sn: info.cNo,
                contractSignTime: info.cSignDate,
                contractStartTime: info.cStartDate,
                contractEndTime: info.cEndDate,
                type: 1, // 1-商品合同 1-商品合同
                memo: info.cMemo,
                contractBaseImages: JSON.stringify(imgs),
                productZSList: JSON.stringify(zsGoods),
                productTYList: JSON.stringify(tyGoods)
                // productList: goods,
            }
            if( info.fileCon2.length > 0  ){
                data.filePath = filePath
                data.fileName = fileName
            }
            switch(type) {
                case 'cRenewal':
                    data.type = 1
                    break
                case 'cRenewalHistory':
                    data.type = 3
                    break
                case 'cRenewalStop':
                    data.type = 2
                    break
            }
            if(type == "new"){
                url = '../sales/insertContractBase.do' // 修改过
            } else if(type == "update"){
                url = '../sales/upContractBase.do' // 修改过
                data.id = $("#newContractInfo").data("cid")
            } else if(type == "cRenewal" || type == "cRenewalHistory"|| type === 'cRenewalStop'){
                url = '../sales/renewalContractForCommodity.do'
                data.id = $("#newContractInfo").data("cid")
            }
            $.ajax({
                url: url,
                data: data ,
                success:function (res) {
                    let state = res.data.state
                    if(state === 1){
                        bounce_Fixed.cancel()
                        layer.msg("操作成功！")
                        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
                        if(fileImArr.length > 0){
                            let info = JSON.parse(fileImArr.find(".hd").html())  ;
                            let groupUuid = info.groupUuid;
                            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} )
                        }
                        let file2= $("#newContractInfo .fileCon2 .fileIm")
                        if(file2.length > 0){
                            let info = JSON.parse(file2.find(".hd").html());
                            let groupUuid = info.groupUuid;
                            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid})
                        }
                        indexUpdateList(cus_seeTrObj, type);
                    } else if (state === 2) {
                        layer.msg("已续约不可修改!")
                    } else if (state === 3) {
                        layer.msg("修改的日期不能在上一个合同结束日之前!")
                    } else {
                        layer.msg("操作失败")
                    }
                }
            })
        }
    }
}

// creator: hxz 2021-05-06 客户对发票方面的要求
function goset(thisObj) {
    let type = thisObj.data("type")
    $("#invoiceSet").data("type" , type).find(".fa-dot-circle-o").attr("class", "fa fa-circle-o")
    bounce_Fixed2.show($("#invoiceSet"))
}
function invoiceSetOk() {
    var selectP = $("#invoiceSet .fa-dot-circle-o").parent()
    let type = selectP.data("type");
    let txt = '尚未设置'
    if(Number(type) > 0  && Number(type) !== 4){
        txt = selectP.children("span").html();
    }
    $(".goset_" + $("#invoiceSet").data("type")).data("invoice", type).prev().html(txt);
    bounce_Fixed2.cancel();
}
// creator: hxz 2020-12-10 收货人那里的新增收货人
function chooseCusContactOk() {
    let selectObj = $("#chooseCusContact").find(".fa-dot-circle-o");
    if(selectObj.length > 0){
        let strInfo = selectObj.siblings("span.hd").html();
        let info = JSON.parse(strInfo);
        $($("#target").val())
            .val(selectObj.next().html()).data('orgData',selectObj.next().html())
            .siblings(".hd").html(strInfo) ;
        bounce_Fixed3.cancel();
        var source = $("#newReceiveAddressInfo").data('source');
        if(source == 'addCustomer') {
            let numberSelect = info.id ;
            for(let q = 0 ; q < addCusContact.length ; q++){
                let contactItem = addCusContact[q];
                if(numberSelect == contactItem.id){
                    contactItem["select"] = true;
                }
            }
        }
    }else layer.msg('请先选择人员')
}
// creator: hxz 2020-12-10 新增联系人
function addContactInfo(num) {
    var type = "", source = "";
    if(num === 2){
        $("#newContectInfo .bonceHead span").html('新增客户联系人');
        $("#contactFlag").html('收货人');
        type = $("#newReceiveAddressInfo").data('type');
        source = $("#newReceiveAddressInfo").data('source');
    }else if(num === 3){
        $("#newContectInfo .bonceHead span").html('新增客户联系人');
        $("#contactFlag").html('发票接收人');
        type = $("#newMailInfo").data('type');
        source = $("#newMailInfo").data('source');
    }else if(num === 4){
        $("#newContectInfo .bonceHead span").html('新增客户联系人');
        $("#contactFlag").html('收货人');
        type = $("#newReceiveAreaInfo").data('type');
        source = $("#newReceiveAreaInfo").data('source');
    }
    var customerId = $("#updateCustomerPanel").data("id");

    // add receiveInfo/updateCustomer
    $("#newContectInfo").data('level',2);
    $("#newContectInfo").data('type','new');
    $("#newContectInfo").data('source', source);
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $('#uploadCard').show();
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    $("#newContectInfo").data("id", customerId);
    $("#addMoreContact").hide();
    $("#uploadCard").html("")
    initCardUpload($("#uploadCard"));
    bounce_Fixed3.show($("#newContectInfo"));
    setTimer('updateContact');
}
// creator: hxz 2020-12-10 获取联系人列表
function getCusContactList(cusID) {
    $.ajax({
        "url":"../sales/getContactsList.do",
        "data":{ 'customerId': cusID },
        success:function (res) {
            if(res.status !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data'] || [] ;
            setCusListStr(list, 'updateCustomer')
        }
    });
}
function setCusListStr(list, source) {
    let str="";
    if(list.length === 0){
        $("#chooseCusContact .p0").show()
        $("#chooseCusContact .p1").hide()
        return false
    }
    $("#chooseCusContact .p0").hide()
    $("#chooseCusContact .p1").show()
    for(let i in list){
        let item = list[i];
        item.contact = item.name
        str += `<li>
                    <i class="fa fa-circle-o"></i>
                    <span>${item.name}</span>
                    <span>${item.post && item.post.substr(0,8)}</span>
                    <span>${item.mobile}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                    <span class="linkBtn ty-right" data-source="${source}" data-type="contactSocial" data-id="${item.id}" onclick="cus_recordDetail($(this))">查看</span>
                </li>`;
    }
    $("#chooseCusContact .cusList").html(str);
}
// creator: 张旭博，2019-04-03 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 新增客户
            case 'newTenant':
                var state = 0
                $(".newCustomer input[require]").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                if( state > 0){
                    $("#notSelectModuleBtn").prop("disabled",true)
                    $("#selectModuleBtn").prop("disabled",true)
                }else {
                    $("#notSelectModuleBtn").prop("disabled",false)
                    $("#selectModuleBtn").prop("disabled",false)
                }
                break;
            // 新增机构
            case 'newOrg':
                var state = 0
                $("#newOrg [require]:visible").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                if( state > 0){
                    $("#sureNewOrgBtn").prop("disabled",true)
                }else {
                    $("#sureNewOrgBtn").prop("disabled",false)
                }
                break;
            case 'newCusOrg':
                var state = 0
                $("#newCusOrg [require]:visible").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                if( state > 0){
                    $("#sureNewCusOrgBtn").prop("disabled",true)
                }else {
                    $("#sureNewCusOrgBtn").prop("disabled",false)
                }
                break;
            case 'orgInfoChange':
                var state = 0
                $("#orgInfoChange [require]:visible").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                if( state > 0){
                    $("#sureOrgInfoChangeBtn").prop("disabled",true)
                }else {
                    $("#sureOrgInfoChangeBtn").prop("disabled",false)
                }
            case 'productInfoChange':
                var state = 0
                $("#productInfoChange [require]:visible").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                if( state > 0){
                    $("#sureProductInfoChangeBtn").prop("disabled",true)
                }else {
                    $("#sureProductInfoChangeBtn").prop("disabled",false)
                }
                break;
        }

    });
}

// creator: 李玉婷，2019-09-16 16:26:52，定时器--新增验证
function setTimer (timer) {
    switch (timer) {
        case 'updateReceive':
            bounce_Fixed2.everyTime('0.5s','updateReceive',function () {
                var  filledNum = 0;
                $("#newReceiveAddressInfo input").each(function(){
                    if ($(this).val() == '') filledNum++;

                });
                if(filledNum === 0 ){
                    $("#addReceive").prop("disabled",false);
                }else{
                    $("#addReceive").prop("disabled",true);
                }
            });
            break;
        case 'updateMail':
            bounce_Fixed.everyTime('0.5s','updateMail',function () {
                var state = 0, filledNum = 0;
                $("#newMailInfo input").each(function(){
                    if ($(this).val() == '') filledNum++;

                });
                if(filledNum === 0  ){
                    if (!is_postcode($("#newMailInfo #mailNumber").val())){
                        $("#mailNumError").show();
                        $("#addMail").prop("disabled",true);
                    }else{
                        $("#mailNumError").hide();
                        $("#addMail").prop("disabled",false);
                    }
                }else{
                    $("#addMail").prop("disabled",true);
                }
            });
            break;
        case 'updateContact':
            bounce_Fixed3.everyTime('0.5s','updateContact',function () {
                var state = 0, filledNum = 0,otherContactLen = 0;
                var contactsCard = $("#contactsCard").data('org');
                var imgCard = $("#contactsCard .filePic").data('path');
                var len = $(".otherContact").data('length');
                $("#addMoreContact option").prop('disabled', false);
                if ($(".otherContact li").length > 0) {
                    $(".otherContact li").each(function () {
                        var val = $(this).find("input").val();
                        var type = $(this).find("input").data('type');
                        if (type == '9' || type == '9') type = 6
                        if (val == '') {
                            $("#addMoreContact option").eq(type).prop('disabled', true);
                        }else {
                            otherContactLen++;
                        }
                    })
                }
                if (len !=  otherContactLen) state ++;
                $("#newContectData [require]:visible").each(function(){
                    if ($(this).val() != '') filledNum++;
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(contactsCard != imgCard) state ++;
                if(filledNum > 0 && state > 0){
                    $("#addContact").prop("disabled",false);
                }else{
                    $("#addContact").prop("disabled",true);
                }
            });
            break;
        case 'useDefinedLabel':
            bounce_Fixed4.everyTime('0.5s', 'useDefinedLabel',function () {
                var name = $.trim($("#defLable").val());
                if (name == '' || !name) {
                    $("#addNewLableSure").prop('disabled', true);
                } else {
                    $("#addNewLableSure").prop('disabled', false);
                }
            })
            break;
        case 'newInterview':
            bounce_Fixed.everyTime('0.5s', 'newInterview',function () {
                var state = 0;
                $("#newInterview").find("[require]:visible:not([disabled])").each(function(){
                    if($.trim($(this).val()) != ""){
                        state ++;
                    }
                })
                if (state != 0) {
                    $("#newInterviewSure").prop('disabled', false);
                } else {
                    $("#newInterviewSure").prop('disabled', true);
                }
            })
            break;
        case 'updateInterview':
            bounce_Fixed.everyTime('0.5s','updateInterview',function () {
                var state = 0, needNum = 0;
                $("#newInterview [require]:visible").each(function(){
                    if ($(this).val() != ''){
                        needNum++;
                    }
                    if($(this).val() != $(this).data('org')){
                        state ++;
                    }
                });
                if(needNum > 0 && state > 0){
                    $("#newInterviewSure").prop("disabled",false);
                }else{
                    $("#newInterviewSure").prop("disabled",true);
                }
            });
            break;
    }
}
// creator: 李玉婷，2021-08-16 08:20:02，客户查询
function searchCustomer() {
    var key = $(".main #se0").val();
    getCustomerMes(1 , 20, key);
}
// 获取商品基本信息列表
function getCustomerMes(currPage  , pageSize ,keyWord ){
	// currPage   当前页  pageSize    每页条数
	loading.open();
	$.ajax({
		url:"../sales/getPdCustomer.do",
		data:{
			"keyword":keyWord,
			"currPage":currPage,
			"pageSize":pageSize
		},
		type:"post",
		dataType:"json",
		success:function(data){
			loading.close();
			var totalPage = data["totalPage"];
			var cur = data["currPage"];
			var pdCustomers = data["pdCustomers"];
			$("#ye_customerManage").html("");
			var jsonStr = JSON.stringify({"keyWord": keyWord}) ;
			setPage( $("#ye_customerManage") , cur ,  totalPage , "customerManage" ,jsonStr );
			$("#cusManage_body").html("");
			if(pdCustomers !=undefined && pdCustomers.length > 0 ){
				var number = 0;
				for(var i=0 ; i<pdCustomers.length ; i++ ){
					number = 1 +i;
					var str = "<tr>" +
						"<td>"+ number + "</td>" +
						"<td>"+ handleNull(pdCustomers[i]["code"]) + "</td>" +
						"<td>"+ (pdCustomers[i]["fullName"] || pdCustomers[i]["name"] || '--') + "</td>" +
						"<td>"+ handleNull(pdCustomers[i]["createName"]) + '&nbsp;'+ new Date(pdCustomers[i]["createDate"]).format('yyyy-MM-dd hh:mm:ss') + "</td>" +
						"<td>"+ handleNull(pdCustomers[i]["principalName"]) + "</td>" +
						"<td>" +
						"<span class='link-blue' onclick='sale_seebtn($(this))'>查看</span>" ;
					if( !chargeRole("超管") ){
					    if(sphdSocket.user.oid === 0) {
                            str +=
                                "<span class='link-blue' onclick='indexUpdateList($(this))'>管理</span>" +
                                "<span class='link-red' onclick='sale_delbtn($(this))'>删除</span>" +
                                "<span class='link-blue' onclick='getInterviewList($(this), 1, 20)'>访谈记录</span>" +
                                "<span class='link-blue' type='btn' data-name='orgManage'>机构管理</span>" ;
                        } else {
                            str +=
                                "<span class='link-blue' onclick='indexUpdateList($(this))'>管理</span>" +
                                "<span class='link-red' onclick='sale_delbtn($(this))'>删除</span>" +
                                "<span class='link-orange' onclick='suspendCooperationBtn($(this))'>暂停合作</span>" ;
                        }
					}
					str +="<div class='hd'>" +
						"<span class='productId'>"+ pdCustomers[i]["product_"]  +"</span>" +
						"<span class='cusProId'>"+ pdCustomers[i]["id"]  +"</span>" +
						"<span class='cusInfo'>"+ JSON.stringify(pdCustomers[i])  +"</span>" +
						"</div>" +
						"</td>"+
						"</tr>";
					$(".sale_Tbody").append(str);
				}
			}
		},
		error:function (meg) {
			loading.close();
			alert("连接错误，请稍后重试！");
		}
	})
}
// creator: 张旭博，2024-06-28 01:55:44， 新增客户之前的提示
function sale_addbtn_confirm() {
    if (remindState === 1){
        bounce.show($("#confirm_tip"));
    } else if (remindState === 2){
        sale_addbtn()
    }
}
function noMoreTip(obj){
    obj.toggleClass("fa-circle");
}
function confirm_tipOk(obj){
    if ($("#confirm_tip .fa-circle").is(":visible")) {
        setNoRemind();
    }
    sale_addbtn()
}
let remindState = 0;
function getContractRemind(){
    $.ajax({
        url:"../supplier/getContractRemind.do",
        data:{
            type: 2 //type 1-采购 2-销售
        },
        success:function(data){
            remindState = data.data.state    //1-需要提醒 2-不需提醒
        }
    })
}
function setNoRemind(){
    $.ajax({
        url:"../supplier/updateContractRemind.do",
        data:{
            type: 2 //type 1-采购 2-销售
        },
        success:function(data){
            remindState = 2
        }
    })

}

// 新增按钮
var addCusContact = [];
function sale_addbtn(){
    bounce.cancel()
    if (chargeRole("超管")) { // 0 表示超管
        $("#mtTip .tip").html("您没有此权限？")
        bounce_Fixed.show($("#mtTip"))
		return false ;
	}
    addCusContact = [];
    $("#addAccount").data('mailInfo', '[]');
    $("#addAccount").data('contactInfo', '[]');
    $("#addAccount").data('contractInfo', '[]');
    $("#addAccount .textMax").html('0/255');
    $("#addAccount .goodsAddress").html('');
    $("#addAccount .hd").html('');
    $(".mailList").html('');
    $(".goset_add").data("invoice", 0).prev().html('尚未设置');
    $(".contectList").html('');
    $("#clist").html('');
    $("#qImages .imgsthumb").remove();
    $("#pImages .imgsthumb").remove();
    $(".purchased").hide();
    $("#addAccount .hasContract").hide();
    $("#addAccount .contractList").hide();
    $("#buyMonth").hide();
    document.getElementById('addpayDetails').reset();
    $("#panoramaBtn").html("")
    $("#productPicsBtn").html("")
    $('#addpayDetails .require-box').html("").siblings(".hd").html("")
    initUpload($("#panoramaBtn"));
    initUpload($("#productPicsBtn"));
    bounce.show($("#addAccount"));
    bounce.everyTime('0.5s','addAccount',function () {
        var state = 0
        $("#addAccount [require]:visible").each(function () {
            if ($(this).val() === '') {
                state ++
            }
        })
		//var placeLength = $("#addAccount .receiveList tbody tr").length;
		var mailLength = $("#addAccount .mailList tbody tr").length;
		var contactLength = $("#addAccount .contectList tbody tr").length;
		var goodsAddress = $("#addAccount .goodsAddress").siblings(".hd").html();
        /*if(placeLength >= 11){
        	$("#addAccount #newAddress").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
		}else{
            $("#addAccount #newAddress").data("name","receiveInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }*/
        if(mailLength >= 10){
            $("#addAccount #newMail").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
        }else{
            $("#addAccount #newMail").data("name","mailInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
        if(contactLength >= 50){
            $("#addAccount #newContact").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
        }else{
            $("#addAccount #newContact").data("name","contactInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
        if(goodsAddress === ""){ state ++; }
        if(state === 0){
            $("#addAccountBtn").prop("disabled",false);
            $("#notSelectModuleBtn").prop("disabled",false);
            $("#selectModuleBtn").prop("disabled",false);
        }else{
            $("#addAccountBtn").prop("disabled",true);
            $("#notSelectModuleBtn").prop("disabled",true);
            $("#selectModuleBtn").prop("disabled",true);
        }
	});
}
//updater:孟闯闯，2017-3-02  15:00:00，点击导入，弹出导入页面
function leading(){
	bounce.show($("#leading"));
	$("#appendix").val("");
}
//updater:孟闯闯，2017-3-02  15:00:00，增加导入确定功能
function leadingIn(num){
	var filepath="D:\\Test\\文本1.txt";
	var fso = new ActiveXObject("Scripting.FileSystemObject");
	var file = fso.GetFile(filepath);
	var file=$("#appendix").val();
	$.ajax({
		url:"../export/importCustomer.do",
		type:"post",
		data:{
			file:file,
		},
		dataType:"json",
		multipart:true,
		success:function(data){
			console.log(data)
		},
		error:function(){
		}
	})
}

// creator 侯杏哲 2017-03-07 处理导入客户后的反馈
var importTimer = 0 ;
function reloadPage( ){
	loading.open();
	bounce.cancel() ;
	var t = 1 ;
	importTimer = setInterval(function(){
		t += 1 ;
		var data = $("#importCon").contents().find("body").html() ;
		if(data == 0 || data == 1 ){
			loading.close() ;
			if(data == 1 ){
				location.href="../../sales/customerManage.do" ;
			}else{
                $("#mtTip .tip").html("文件导入失败，请重新导入！");
                bounce_Fixed.show($("#mtTip"));
				clearInterval( importTimer ) ;
				$("#importCon").attr( "src" , "../../script/sales/cusImport.jsp") ;
			}
		}else{
			loading.close() ;
			if( t >= 20 ){
                $("#mtTip .tip").html("文件导入失败，请重新导入！");
                bounce_Fixed.show($("#mtTip"));
				clearInterval( importTimer ) ;
				$("#importCon").attr( "src" , "../../script/sales/cusImport.jsp") ;
			}
		}
	} , 500 ) ;
}

// 新增中的保存
function sale_addsure(){
    let able = true;
    $('#addpayDetails [require]:visible').each(function(){
        if ($(this).val() === "")able = false;
    })
    $('#addpayDetails .require-box').each(function(){
        if ($(this).siblings(".hd").html() === "")able = false;
    })
    if ( !able) {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
        return false;
    }
    var shList =[];
    var imgsQ = [], imgsP = [] ;
    var data = {
        qImages: '',
        pImages: '',
        contractBaseList: '',
        shAddressList: '',
        fpAddressList: '',
        contactsList: ''
	};
	$('#addpayDetails input[type="text"]:visible').each(function(){
        var name = $(this).attr('name');
        data[name] = $(this).val();
    })
    if (sphdSocket.user.oid === 0) {
        if (!testMobile($.trim($("#addpayDetails input[name='supervisorMobile']").val()))) {
            layer.msg("请输入正确的手机号")
            return false
        }
    }
    var hasBuyCase = $("#addpayDetails input[name='buyCase']:checked").val();
    if (hasBuyCase === '1') {
        // 购买过产品
        var initialType = $("#addpayDetails input[name='firstTime']:checked").val();
        if(typeof initialType === 'undefined'){
            layer.msg('请选择首次购买时间');
            return false;
        }else{

            data.initialType = initialType;
            var date = '', month = '';
            var year = new Date().getFullYear();
            if(initialType === '1'){
                month = $("#addpayDetails #buyMonth").val();
                if(month === "") {
                    layer.msg('请选择月份');
                    return false;
                }else{
                    var arr = month.split('月');
                    if(arr[0] < 10){
                        date = year + '0' + arr[0];
                    }else{
                        date = year + arr[0];
                    }
                }
            }else if(initialType === '2'){
                year = Number(year) - 1;
                date = year + '01';
            }else if(initialType === '3'){
                year = Number(year) - 2;
                date = year + '01';
            }
            data.initialPeriod = date;
        }
    }
    $("#qImages .imgsthumb").each(function () {
		var path = {
            'normal': $(this).find(".filePic").data('path')
        };
		imgsQ.push(path);
    })
    $("#pImages .imgsthumb").each(function () {
        var path = {
            'normal': $(this).find(".filePic").data('path')
        };
        imgsP.push(path);
    })
	imgsQ = JSON.stringify(imgsQ);
	imgsP = JSON.stringify(imgsP);
	data.qImages = imgsQ;
	data.pImages = imgsP;
	if($("#firstContactTime").val() != ''){
        data.firstContactTime = $("#firstContactTime").val();
    }
    let goodsAddress = JSON.parse($("#addAccount .goodsAddress").siblings(".hd").html())
    let contractList = [];
	$("#clist tr").each(function(){
	    let infoc = JSON.parse($(this).find(".hd").html())
        let filePath = ''
        let fileName = ''
        if( infoc.fileCon2.length > 0  ){
            filePath = infoc.fileCon2[0].filename
            fileName = infoc.fileCon2[0].originalFilename
        }
        let imgs = []
        if(infoc.fileCon1 && infoc.fileCon1.length > 0){
            infoc.fileCon1.forEach(function(im, index){
                imgs.push({
                    uplaodPath: im.filename,
                    order: index ,
                    type: 1,
                    title: im.originalFilename,
                    operation: 1
                })
            })
        }
        contractList.push({
            sn: infoc.cNo,
            contractSignTime: infoc.cSignDate,
            contractStartTime:infoc.cStartDate,
            contractEndTime:infoc.cEndDate,
            fileName: fileName,
            filePath: filePath,
            memo: infoc.cMemo,
            contractBaseImages: imgs,
            productTYList: JSON.stringify(infoc.tyGoods)
        })
    })
    if (goodsAddress.deliveryType === 2){
        let addressList = [...goodsAddress.shAddressList,...goodsAddress.areaList];
        data.selfState = goodsAddress.selfState;
        data.shAddressList = JSON.stringify(addressList);
    }
    data.deliveryType = goodsAddress.deliveryType;
    data.contractBaseList= JSON.stringify(contractList);
    data.fpAddressList= $("#addAccount").data('mailInfo');
    data.contactsList= $("#addAccount").data('contactInfo');
    data.invoiceRequire = $(".goset_add").data("invoice") || 0
    if (sphdSocket.user.oid === 0) {
        $.ajax({
            url: '../special/getSaleCtrlsByMobile.do',
            data: {mobile: data.supervisorMobile},
            success: function (res) {
                var state = res.data
                if (state === 2) {
                    $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                    bounce_Fixed.show($("#tip"))
                    loading.close()
                } else if (state === 1) {
                    $.ajax({
                        url: "../sales/addPdCustomer.do",
                        data: data,
                        success: function (res) {
                            var status = res.status;
                            if (status === 1) {
                                layer.msg("新增成功");
                                var key = $(".main #se0").val();
                                getCustomerMes(1 , 20, key);
                                bounce.cancel();
                                bounce_Fixed.cancel();
                                var groupUuidArr = []
                                $("#addAccount [groupUuid]").each(function () {
                                    groupUuidArr.push({
                                        type: 'groupUuid',
                                        groupUuid: $(this).attr("groupUuid")
                                    })
                                })
                                cancelFileDel(groupUuidArr)

                            } else if (status === 2) {
                                $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                                bounce_Fixed.show($("#tip"))
                            } else {
                                $("#tip .tipMs").html("操作失败！");
                                bounce_Fixed.show($("#tip"));
                            };

                            $("#newReceiveAddressInfo").data('id', '');
                            $("#newReceiveAddressInfo").data('type', '');
                            $("#newReceiveAddressInfo").data('source', '');
                            $("#newMailInfo").data('type', '');
                            $("#newMailInfo").data('id', '');
                            $("#newMailInfo").data('source', '');
                            $("#newContectInfo").data('type', '');
                            $("#newContectInfo").data('id', '');
                            $("#newContectInfo").data('source', '');
                        }
                    })
                } else {
                    layer.msg("系统错误！")
                }
            },
            complete: function () {

            }
        })
    }
    else {
        $.ajax({
            url: "../sales/addPdCustomer.do",
            data: data,
            success: function (res) {
                var status = res.status;
                if (status === 1) {
                    var key = $(".main #se0").val();
                    layer.msg("新增成功");
                    getCustomerMes(1 , 20, key);
                    bounce.cancel();
                    bounce_Fixed.cancel();
                } else if (status === 2) {
                    $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                    bounce_Fixed.show($("#tip"))
                } else {
                    $("#tip .tipMs").html("操作失败！");
                    bounce_Fixed.show($("#tip"));
                }
            }
        })
    }


}
function formatInviceRequire(type) {
    let str = ''
    switch (Number(type)){
        case 0 :    str = '尚未设置'; break
        case 1 :    str = '需要主要为增值税专用发票'; break
        case 2 :    str = '需要主要为增值税专用发票以外的发票'; break
        case 3 :    str = '基本不需要开发票'; break
        case 4 :    str = '尚未设置'; break
        default: str = '尚未设置'
    }
    return str
}
// 查看按钮
var cus_seeTrObj = null ;
function sale_seebtn(obj){
    //$("#newReceiveAddressInfo").data('id', '');
    //$("#newReceiveAddressInfo").data('type', '');
    //$("#newReceiveAddressInfo").data('source', '');
	bounce.show($("#seeAccount"));
	var id = obj.siblings(".hd").children(".cusProId").html();
	if ( id == undefined || id == ""){
        $("#mtTip .tip").html("系统错误，刷新重试！");
        bounce_Fixed.show($("#mtTip"));
		return false;
	}
    $("#seeAccount").data("id", id);
    $.ajax({
		url : "../sales/getPdCustomerOne.do" ,
		data : { "id":id },
		success:function(data){
			var tr_obj = obj.parent().parent();
			cus_seeTrObj = tr_obj;
			var pdCustomer = data["data"];
            var cuscoding = pdCustomer["code"];
            var invoiceRequire = pdCustomer["invoiceRequire"];
            var cusname = pdCustomer["name"];
            var cusfullName = pdCustomer["fullName"];
            var firstBuyTime = pdCustomer["firstBuyTime"];
            var address = pdCustomer["address"];
            var invoiceName = pdCustomer["invoiceName"];
            var invoiceAddress = pdCustomer["invoiceAddress"];
            var phone = pdCustomer["telephone"];
            var bank = pdCustomer["bankName"];
            var accountnum = pdCustomer["bank_no"];
            var taxpayerID = pdCustomer["taxpayerID"];
            var firstContactAddress = pdCustomer["firstContactAddress"];
            var infoSource = pdCustomer["infoSource"];
            var memo = pdCustomer["memo"];
            var createName = pdCustomer["createName"];
            var supervisorMobile = pdCustomer["supervisorMobile"];
            var supervisorName = pdCustomer["supervisorName"];
            var firstContactTime = new Date(pdCustomer["firstContactTime"]).format('yyyy-MM-dd');
            var createDate = new Date(pdCustomer["createDate"]).format('yyyy-MM-dd hh:mm:ss');
            var qImages = pdCustomer["qImages"];
            var pImages = pdCustomer["pImages"];
            var contactList = pdCustomer["contactsList"];
            var contactBaseList = pdCustomer["contactBaseList"] || [] ;
            var shAddressList = pdCustomer["shAddressList"];
            var fpAddressList = pdCustomer["fpAddressList"];
            var initialPeriod = pdCustomer["initialPeriod"];
            var date = '';
            $("#see_cuscoding").html(cuscoding);
            $("#see_cusname").html( cusfullName);
            $("#see_cusfullName").html(cusname);
            $("#firstBuyTime").html(firstBuyTime);
            $("#see_address").html(address);
            $("#see_phone").html(phone);
            $("#see_bank").html(bank);
            $("#see_invoiceRequire").html( formatInviceRequire(invoiceRequire));
            $("#see_bankNo").html(accountnum);
            $("#taxpayerID").html(taxpayerID);
            $("#see_firstContactTime").html(firstContactTime);
            $("#firstContactAddress").html(firstContactAddress);
            $("#see_createName").html(createName);
            $("#see_createDate").html(createDate);
            $("#see_supervisorName").html(supervisorName);
            $("#see_supervisorMobile").html(supervisorMobile);
            $("#infoSource").html(infoSource);
            $("#see_memo").html(memo);
            $("#see_invoiceName").html(invoiceName);
            $("#see_invoiceAddress").html(invoiceAddress);
            $("#telephone").html(invoiceAddress);
            if(qImages.length > 0){
                var imgStr = '';
                for(var a=0;a<qImages.length;a++){
                    var path = qImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">' +
                        '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                        '</div>';
                }
                $("#overallImgUpload").html(imgStr);
            }else{
                $("#overallImgUpload").html("");
            }
            if(pImages.length > 0){
                var imgStr = '';
                for(var a=0;a<pImages.length;a++){
                    var path = pImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">' +
                        '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                        '</div>';
                }
                $("#productImgUpload").html(imgStr);
            }else{
                $("#productImgUpload").html('');
            }
            // 处理是否购买过本公司的产品
            var hasBuyStr = ''
            var initialType = pdCustomer.initialType;
            if (initialType === null) {
                hasBuyStr = '<div class="ty-alert ty-alert-error">'+createDate + '之前该客户尚未购买过本公司的商品</div>'
            } else {
                if(initialType === '1'){
                    var month = initialPeriod.substr(4,2);
                    if(month.slice(0,1) == '0'){
                        month = month.substr(1,1);
                    }
                    date = initialPeriod.substr(0,4) + '年'  + month  + '月';
                    hasBuyStr = '<div class="ty-alert ty-alert-success">'+date+'该客户首次购买过本公司的商品</div>'
                }else if(initialType === '2'){
                    hasBuyStr = '<div class="ty-alert ty-alert-success">去年该客户首次购买过本公司的商品</div>'
                }else{
                    hasBuyStr = '<div class="ty-alert ty-alert-success">去年之前该客户首次购买过本公司的商品</div>'
                }

            }
            $("#seeAccount .firstBuyTime").html(hasBuyStr)

            $(".see_contactList").html('');
            $(".recivePlaceList tbody").html('');
            $(".mailPlaceList tbody").html('');
            $(".contactNum").html(contactList.length);
            // var htmlContract =  ''
            // contactBaseList.forEach(function (cIm) {
            //     htmlContract += `
            //     <tr data-id="${ cIm.id }" class="contractItem">
            //         <td>${ cIm.sn }</td>
            //         <td>${ new Date(cIm.signTime).format("yyyy-MM-dd")}</td>
            //         <td>${new Date(cIm.validStart).format("yyyy-MM-dd")}  至  ${new Date(cIm.validEnd).format("yyyy-MM-dd")} </td>
            //         <td> <span class="linkBtn node" data-name="scanGs">${ cIm.num }</span>种</td>
            //         <td><span class="link-blue node" data-name="cScan">查看</span></td>
            //     </tr>
            //     `
            // })
            // $(".contractPlaceList tbody").html(htmlContract);

            if(contactList.length > 0){
                var rhtml = '';
                var html = '<div class="leftList"><table class="kj-table">' +
                    '<thead>' +
                    '<tr>' +
                    '    <td>姓名</td>' +
                    '    <td>职位</td>' +
                    '    <td>操作</td>' +
                    '</tr>' +
                    '</thead><tbody>';
                if(contactList.length >= 2){rhtml = html;}
                for(var b in contactList){
                    var slice = b % 2;
                    if(slice > 0){
                        rhtml +=
                            '<tr class="'+(contactList[b].enabled == 1?'':'disable')+'" data-id="' + contactList[b].id + '">' +
                            '    <td>' + contactList[b].name + '</td>' +
                            '    <td>' + contactList[b].post + '</td>' +
                            '    <td>' +
                            '<span class="link-blue" data-id="' + contactList[b].id + '" data-type="contactSocial" onclick="cus_recordDetail($(this))">联系方式</span>' +
                            '<span class="link-blue" data-id="' + contactList[b].id + '" data-type="visitCard" onclick="cus_recordDetail($(this))">查看名片</span>' +
                            '<span class="link-blue" data-type="contactRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                            '</tr>';
                    }else{
                        html +=
                            '<tr class="'+(contactList[b].enabled == 1?'':'disable')+'" data-id="' + contactList[b].id + '">' +
                            '    <td>' + contactList[b].name + '</td>' +
                            '    <td>' + contactList[b].post + '</td>' +
                            '    <td>' +
                            '<span class="link-blue" data-id="' + contactList[b].id + '" data-type="contactSocial" onclick="cus_recordDetail($(this))">联系方式</span>' +
                            '<span class="link-blue" data-id="' + contactList[b].id + '" data-type="visitCard" onclick="cus_recordDetail($(this))">查看名片</span>' +
                            '<span class="link-blue" data-type="contactRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                            '</tr>';
                    }
                }
                html += '</tbody></table></div>';
                if(contactList.length >= 2){rhtml += '</tbody></table></div>';}
                var result = html + rhtml;
                    $(".see_contactList").html(result);
            }
            $(".recivePlaceScanBtn").hide();
            if(pdCustomer.deliveryType === 2){
                let areaList = [];
                let address = [];
                for(let i=0;i<shAddressList.length;i++){
                    if (shAddressList[i].type === '1') {
                        address.push(shAddressList[i]);
                    } else  if (shAddressList[i].type === '3') {
                        areaList.push(shAddressList[i]);
                    }
                }
                let shData = {
                    "selfState": pdCustomer.selfState,
                    "shAddressList": address,
                    "areaList": areaList
                }
                $(".recivePlaceScanBtn").show();
                $(".reciveDescript").html("向该客户提供实体货物").siblings(".hd").html(JSON.stringify(shData));
            }
            else {
                $(".reciveDescript").html("向该客户只提供服务，不提供实体货物，不需编辑收货地址").siblings(".hd").html("");
            }
            if(fpAddressList.length > 0){
                var html = '';
                for(var b in fpAddressList){
                    var num = 1 + Number(b);
                    html +=
                        '<tr class="'+(fpAddressList[b].enabled == 1?'':'disable')+'" data-id="' + fpAddressList[b].id + '">' +
                        '    <td>' + num + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].address) + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].contact) + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].postcode) + '</td>' +
                        '    <td><span class="link-blue" data-type="fpAddressRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                        '</tr>';
                }
                $(".mailPlaceList tbody").html(html);
            }
		}
	})
    $.ajax({
        url: $.webRoot + "/sales/listContractBase.do",
        data: { customer: id, type: 1 },
        success:function (res) {
            let data = res.data || []
            let list = data.contractBaseList
            let str = ``;
            list.forEach(function (item) {
                str += `
                    <tr class="contractItem" data-id="${item.id}">
                                <td>${item.sn}</td>
                                <td>${formatTime(item.signTime, 'date')}</td>
                                <td>${formatTime(item.validStart, 'date')} 至 ${formatTime(item.validEnd, 'date')}</td>
                                <td>${ item.generalCount }种</td>
                                <td>${ item.exclusiveCount }种</td>
                                <td>
                                    <span class="link-blue node" data-name="cScan" >查看</span>
                                </td>
                            </tr>
                 `;
            })
            $(".contractPlaceList tbody").html(str);
        }
    })
}
// 基本信息、开票信息修改按钮
function sale_updatabtn(obj){
    if (chargeRole("超管")) { // 0 表示超管
        $("#mtTip .tip").html("您没有此权限！");
        bounce_Fixed.show($("#mtTip"));
		return false ;
	}
    var objName = obj.data('name');
    var id =     $("#updateCustomerPanel").data("id");
	if ( id == undefined || id == ""){
        $("#mtTip .tip").html("系统错误，刷新重试！");
        bounce_Fixed.show($("#mtTip"));
		return false;
	}
    document.getElementById('sale_updataBase').reset();
	$("#initialPeriod").hide();
	$("#sale_updataBase .hasContract").hide();
    $("#updataccount").data("changed", false);
    $.ajax({
		url : "getPdCustomerOne.do" ,
		data : { "id":id},
		success:function(data){
            var getData = data.data;
		    if (objName == 'updateBase'){
                var pImages = getData["pImages"];
                var qImages = getData["qImages"];
                var firstContactTime = new Date(getData['firstContactTime']).format('yyyy/MM/dd');
                $("#sale_updataBase input[type='text']").each(function(){
                    var name = $(this).attr('name');
                    $(this).val(getData[name]);
                    $(this).data('orgData',getData[name]);
                });
                $("#edit_firstTime").val(firstContactTime).data('orgData',firstContactTime);
                $("#edit_qImages .imgsthumb").remove();
                $("#edit_pImages .imgsthumb").remove();
                $("#edit_qImages").data('orgData',qImages);
                $("#edit_pImages").data('orgData',pImages);
                if(pImages.length > 0){
                    var html = setImgHtml(2, pImages);
                    $("#edit_pImages .imgWall").html(html);
                }
                if(qImages.length > 0){
                    var html = setImgHtml(2, qImages);
                    $("#edit_qImages .imgWall").html(html);
                }
                //是否购买过
                $("#sale_updataBase [name='buyCase']").data('orgData','');
                $(".purchased [type='radio']").data('orgData','');
                $("#initialPeriod").data('orgData','');
                if (getData.initialType == null) {
                    $("#sale_updataBase [name='buyCase'][value='0']").data('orgData','0').click();
                }else {
                    $("#sale_updataBase [name='buyCase'][value='1']").data('orgData','1').click();
                    $(".purchased [type='radio'][value='"+ getData.initialType +"']").data('orgData',getData.initialType).click();
                    if (getData.initialType == '1'){
                        if (getData.initialPeriod != null) {
                            var month = getData.initialPeriod;
                            month = month.substr(4,2);
                            if(month.substring(0,1) == '0') {
                                month = month.substr(1,1) + '月';
                            } else{
                                month = month + '月';
                            }
                            $("#initialPeriod").data('orgData',month).val(month);
                        }
                    }
                    if (getData.hasContract === '1') {
                        $("#sale_updataBase [name='hasContract'][value='1']").data('orgData','1').click();
                        var contractSn = getData.contractSn || ''
                        var expiresTime = getData.expiresTime?new Date(getData.expiresTime).format('yyyy/MM/dd') : ''
                        var contractTime = getData.contractTime?new Date(getData.contractTime).format('yyyy/MM/dd') : ''
                        $("#sale_updataBase [name='contractSn']").data('orgData',contractSn).val(contractSn);
                        $("#sale_updataBase [name='expiresTime']").data('orgData',expiresTime).val(expiresTime);
                        $("#sale_updataBase [name='contractTime']").data('orgData',contractTime).val(contractTime);
                    } else {
                        $("#sale_updataBase [name='hasContract'][value='0']").data('orgData','0').click();
                    }
                }
                $("#edit_panoramaBtn").html('')
                $("#edit_productPicsBtn").html('')
                initUpload($("#edit_panoramaBtn"));
                initUpload($("#edit_productPicsBtn"));

                bounce_Fixed.show($("#updataccount"));
                bounce_Fixed.everyTime('0.5s','updateAccount',function () {
                    var state = 0;
                    var status = 0
                     var buyCase = $("#sale_updataBase [name='buyCase']:checked");
                    var firstTime = $("#sale_updataBase [name='firstTime']:checked");
                    var qjPicArr = $("#edit_qImages").data('orgData');
                    var cpPicArr = $("#edit_pImages").data('orgData');
                    var qjPicLen = $("#edit_qImages .filePic").length;
                    var cpPicLen = $("#edit_pImages .filePic").length;
                    $("#sale_updataBase [require]:visible").each(function(){
                        if ($(this).val() === '') {
                            status++
                        }
                    });
                    if(buyCase.val() != buyCase.data('orgData')) state ++;
                    if(firstTime.val() != firstTime.data('orgData')) state ++;
                    if(qjPicLen == qjPicArr.length && qjPicLen != 0){
                        $("#edit_qImages .filePic").each(function(){
                            var elem = qjPicArr.find(value => value.normal === $(this).data('path'));
                            if(elem == undefined) state ++;
                        })
                    }else{
                        if(qjPicLen > 0 || qjPicArr.length > 0) state ++;
                    }
                    if(cpPicLen == cpPicArr.length && cpPicLen != 0){
                        $("#edit_pImages .filePic").each(function(){
                            var elem = cpPicArr.find(value=>value.normal ===$(this).data('path'));
                            if(elem == undefined) state ++;
                        })
                    }else{
                        if(cpPicLen > 0 || cpPicArr.length > 0) state ++;
                    }
                    if(status === 0){
                        $("#updateAccountBtn").prop("disabled",false);
                    }else{
                        $("#updateAccountBtn").prop("disabled",true);
                    }
                });
            }else{
                document.getElementById('sale_updataInvoice').reset();
                $("#sale_updataInvoice input").each(function(){
                    var name = $(this).data('name');
                    $(this).val(getData[name]);
                    $(this).data('orgData', handleNull(getData[name]));
                })
                $(".goset_update").data("invoice", getData.invoiceRequire ).data("orgData", getData.invoiceRequire )
                    .prev().html(formatInviceRequire(getData.invoiceRequire));
                bounce_Fixed.show($("#updateInvoice"));
                bounce_Fixed.everyTime('0.5s','updataInvoice',function () {
                    var state = 0,filledNum = 0;
                    $("#sale_updataInvoice [require]:visible").each(function(){
                        if ($(this).val() != '') filledNum++;
                        if($(this).val() != $(this).data('orgData')){
                            state ++;
                        }
                    });
                    if($(".goset_update").data("invoice") != $(".goset_update").data("orgData") ){
                        state ++;
                    }
                    if(filledNum>0 && state > 0){
                        $("#updataInvoiceSure").prop("disabled",false);
                    }else{
                        $("#updataInvoiceSure").prop("disabled",true);
                    }
                });
            }
		}
	})
}
// 修改中基本信息修改确定
function updata_sure(){
    var id = $("#updateCustomerPanel").data("id");
	if ( id == undefined || id == ""){
        $("#mtTip .tip").html("系统错误，刷新重试！");
        bounce_Fixed.show($("#mtTip"));
		return false;
	}
    if (sphdSocket.user.oid === 0) {
        if (!testMobile($.trim($("#updataccount input[name='supervisorMobile']").val()))) {
            layer.msg("请输入正确的手机号")
            return false
        }
    }

	var data = {
	    'id': id,
	    'qImages': '[]',
        'pImages': '[]'
    };
	$("#sale_updataBase input[type='text']:visible").each(function(){
	    var name = $(this).attr('name');
        data[name] = $(this).val();
    })
    if($("#edit_firstTime").val() != ""){
	    data.firstContactTime = $("#edit_firstTime").val();
    }

    var hasBuyCase = $("#sale_updataBase input[name='buyCase']:checked").val();
    if (hasBuyCase === '1') {
        // 购买过产品
        var initialType = $("#sale_updataBase input[name='firstTime']:checked").val();
        if(typeof initialType === 'undefined'){
            layer.msg('请选择首次购买时间');
            return false;
        }else{
            data.initialType = initialType;
            var date = '', month = '';
            var year = new Date().getFullYear();
            if(initialType === '1'){
                month = $("#sale_updataBase [name='initialPeriod']").val();
                if(month === "") {
                    layer.msg('请选择月份');
                    return false;
                }else{
                    var arr = month.split('月');
                    if(arr[0] < 10){
                        date = year + '0' + arr[0];
                    }else{
                        date = year + arr[0];
                    }
                }
            }else if(initialType === '2'){
                year = Number(year) - 1;
                date = year + '01';
            }else if(initialType === '3'){
                year = Number(year) - 2;
                date = year + '01';
            }
            data.initialPeriod = date;
        }
    } else {
        // 选择否的时候什么字段都不需要传
    }

    if($("#edit_qImages .imgsthumb").length > 0){
        var imgArr = [];
        $("#edit_qImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal': path
            };
            imgArr.push(json);
        })
        imgArr = JSON.stringify(imgArr);
        data.qImages = imgArr;
    }
    if($("#edit_pImages .imgsthumb").length > 0){
        var imgArr = [];
        $("#edit_pImages .imgsthumb").each(function(){
            var path = $(this).find(".filePic").data('path');
            var json = {
                'normal': path
            };
            imgArr.push(json);
        })
        imgArr = JSON.stringify(imgArr);
        data.pImages = imgArr;
    }
    data.invoiceRequire = $(".goset_update").data("invoice") || 0
    $.ajax({
        url: '../special/getSaleCtrlsByMobile.do',
        data: {mobile: data.supervisorMobile,id: data.id},
        success: function (res) {
            var state = res.data
            if (state === 2) {
                $("#tip .tipMs").html("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                bounce_Fixed.show($("#tip"))
                loading.close()
            } else if (state === 1) {
                $.ajax({
                    url : "../sales/updatePdCustomerBase.do" ,
                    data : data,
                    success:function(data){
                        var status = data["status"];
                        if( status == 0 || status == "0"){
                            $("#mtTip .tip").html("操作失败！");
                            bounce_Fixed.show($("#mtTip"));
                            return false;
                        }
                        // 取消已提交文件的删除
                        var groupUuidArr = []
                        var key = $(".main #se0").val();
                        $("#addAccount [groupUuid]").each(function () {
                            groupUuidArr.push({
                                type: 'groupUuid',
                                groupUuid: $(this).attr("groupUuid")
                            })
                        })
                        cancelFileDel(groupUuidArr)

                        indexUpdateList(cus_seeTrObj);
                        getCustomerMes(1 , 20, key);
                        bounce_Fixed.cancel();
                    }
                })
            } else {
                layer.msg("系统错误！")
            }
        }
    })

}
// creator: 李玉婷，2019-09-07 11:36:29，修改开票信息确定
function updataInvoice_sure(){
    var id =     $("#updateCustomerPanel").data("id");
    var data = {
        'id': id
    };
    $("#sale_updataInvoice input").each(function(){
        var name = $(this).data('name');
        data[name] = $(this).val();
    })
    data['invoiceRequire'] =  $(".goset_update").data("invoice")
    if(data.taxpayerID.length > 20){
        layer.msg('税号不合法，不能超过20位！')
        return false
    }

    $.ajax({
        url: "../sales/updatePdCustomerInvoice.do",
        data: data,
        type: "post",
        dataType: "json",
        success: function (data) {
            var status = data.status;
            if(status == '1'){
                bounce_Fixed.cancel();
                indexUpdateList(cus_seeTrObj);
            }
        }
    })
}
// 删除按钮
function sale_delbtn(obj){
    if (chargeRole("超管")) { // 0 表示超管
        layer.msg("您没有此权限！")
	}
	cus_seeTrObj = obj.parent().parent();
	var id = obj.siblings(".hd").children(".cusProId").html();
	if ( id == undefined || id == ""){  // 校验销售id 是否合法
        layer.msg("系统错误，刷新重试！")
		return false; // 结束
	}
	bounce.show($("#deleteAccount"));
	var del_cuscode = cus_seeTrObj.children(":eq(1)").html();  // 获取客户代号，客户名称
	var del_cusName = cus_seeTrObj.children(":eq(2)").html();
	var str = "确定删除客户代号为：" + del_cuscode + " , 客户名称为：" + del_cusName + " 的客户吗？";
	$("#customerDelTip").html(str); // 给提示信息位置赋值
	$("#del_saleID").html(id); //  给销售ID赋值
}
// 删除中的确定按钮
function sale_delsure(){
	var id = $("#del_saleID").html();
	if ( id == undefined || id == ""){  // 校验销售id 是否合法
        $("#mtTip .tip").html("系统错误，刷新重试！"); //  不合法的提示信息赋值
        bounce_Fixed.show($("#mtTip"));
		return false; // 结束
	}
	$.ajax({
		url:"deletePdCustomerOne.do",
		data:{
			"id":id
		},
		success:function (data){
            bounce.cancel();
            var status = data["status"];
			if(status == 1 ){
				layer.msg("删除成功！")
				cus_seeTrObj.remove();
			}else if( status == 0 ){
				$("#mtTip .tip").html("该客户下已有数据，不可删除！");
                bounce_Fixed.show($("#mtTip"));
			}else{
                $("#mtTip .tip").html("系统错误！");
                bounce_Fixed.show($("#mtTip"));
			}
		}
	})
}
//creator:lyt Date:2018/11/19 修改-删除按钮
function deleteContact(obj){
    var id = obj.parents("tr").data('id');
    $("#turnTipMs").html('删除后，本联系人依旧显示在查看中，但仅可查看。');
    $("#oprationSure").data('name','deleteContact');
    $("#oprationSure").data('id', id);
    bounce_Fixed.show($("#turnStateTip"));
}
//creator:lyt Date:2018/11/19 修改-停用按钮
function turnBtn(obj){
    var id = obj.parents("tr").data('id');
    $("#turnTipMs").html('确定'+ obj.html() +'吗？');
    $("#oprationSure").data('val',obj.data("able"));
    $("#oprationSure").data('name','enableTurn');
    $("#oprationSure").data('id', id);
    bounce_Fixed.show($("#turnStateTip"));
}

// creator: 张旭博，2020-10-27 08:25:22，暂停合作 - 按钮（打开确认弹窗）
function suspendCooperationBtn(selector) {

    var customerId = selector.siblings(".hd").find('.cusProId').html()
    bounce.show($("#confirmSuspendCooperation"))
    $("#confirmSuspendCooperation").data("customerId", customerId)
    if ($(".suspendedCustomer").is(":visible")) {
        $("#confirmSuspendCooperation .start").show().siblings().hide()
    } else {
        $("#confirmSuspendCooperation .stop").show().siblings().hide()
    }

}

// creator: 张旭博，2020-10-27 08:41:28，暂停合作 - 确认
function sureSuspendCooperation() {
    var customerId = $("#confirmSuspendCooperation").data("customerId")
    var state = $(".suspendedCustomer").is(":visible")?0:1
    $.ajax({
        url: '../sales/suspendCooperation.do',
        data: {
            customerId: customerId,
            state:state
        },
        success: function (res) {
            var status = res.status
            if (status === 1) {
                layer.msg("操作成功")
                if ($(".suspendedCustomer").is(":visible")) {
                    getSuspendCustomer(1, 20)
                } else {
                    var key = $(".main #se0").val();
                    getCustomerMes(1 , 20, key)
                }
                bounce.cancel()
            } else {
                layer.msg("操作失败")
            }
        }
    })
}

// creator: 张旭博，2020-10-27 08:51:47，已暂停合作的客户 - 按钮
function suspendedCustomer() {
    $(".suspendedCustomer").show().siblings().hide()
    getSuspendCustomer(1, 20)
}

// creator: 张旭博，2020-10-27 09:47:30，获取暂停/恢复合作历史记录列表
function getSuspendRecordList() {
    bounce_Fixed.show($("#suspendRecord"))
    var customerId = $("#seeAccount").data("id")
    $.ajax({
        url: '../sales/getSuspendOperationList.do',
        data: {
            customerId: customerId
        },
        success: function (res) {
            var data = res.data
            var str = ''
            for(var i in data) {
                str +=  '<tr>' +
                            '<td>' + (data[i].isSuspend === '1'?'暂停合作':'恢复合作') +'</td>' +
                            '<td>' + data[i].updateName + ' ' + formatTime(data[i].updateDate, true) + '</td>' +
                        '</tr>'
            }
            $("#suspendRecord tbody").html(str)
        }
    })
}

// creator: 张旭博，2022/2/11 10:53，获取已暂停服务的列表
function getOutOfOrgList(currentPageNo, pageSize) {
    $.ajax({
        url: "../special/getOutOfOrgList.do",
        data: {
            pageSize: pageSize,// 每页条数
            currentPageNo: currentPageNo // 第几页
        },
        success: function (res) {
            var data = res.data
            var outOfOrgData = data.outOfMpOrganizationList
            // 设置分页
            var pageInfo = data.pageInfo
            setPage( $("#ye_outOfOrg") , pageInfo.currentPageNo, pageInfo.totalPage, "outOfOrg");
            if (outOfOrgData) {
                var str = ''
                for (var i in outOfOrgData) {
                    str +=  '<tr data-id="'+outOfOrgData[i].id+'">' +
                        '    <td>' + (Number(i) + 1) + '</td>' +
                        '    <td>' + outOfOrgData[i].supervisorMobile + '</td>' +
                        '    <td>' + outOfOrgData[i].supervisorName + '</td>' +
                        '    <td>' + outOfOrgData[i].fullName + '</td>' +
                        '    <td>' + outOfOrgData[i].updateName + '&nbsp;&nbsp;&nbsp;' + formatTime(outOfOrgData[i].updateTime, true) + '</td>' +
                        '    <td>' + outOfOrgData[i].mpTenantName + '</td>' +
                        '    <td class="ty-btn-control">' +
                        '        <span type="btn" data-name="orgInfo" class="link-blue">机构信息</span>' +
                        '        <span type="btn" data-name="productInfo" class="link-blue">产品信息</span>' +
                        '        <span type="btn" data-name="addService" class="'+(outOfOrgData[i].mpPackageId !== null?'link-blue':'link-gray')+'">增值服务</span>' +
                        '        <span type="btn" data-name="userLoginRecord" class="link-blue">登录记录</span>' +
                        '        <span type="btn" data-name="resouceManage" class="link-blue">空间与流量</span>' +
                        '        <span type="btn" data-name="recoveryService" class="link-blue">恢复服务</span>' +
                        '    </td>' +
                        '</tr>'
                }
                $(".page_outOfOrg table.orgList tbody").html(str)
            }

        }
    })
}

function goBack() {
    var key = $(".main #se0").val();
    $(".main").show().siblings().hide()
    getCustomerMes(1, 20 ,key)
}

// creator: 张旭博，2020-10-27 08:50:54，获取已暂停合作的客户
function getSuspendCustomer(currPage, pageSize) {
    $.ajax({
        url: '../sales/getSuspendCustomer.do',
        data: {
            currPage:currPage,
            pageSize:pageSize
        },
        success: function (res) {
            var totalPage = res.totalPage
            var currPage = res.currPage
            var pdCustomers = res.pdCustomers
            setPage( $("#ye_suspendCustomer") , currPage ,  totalPage , "suspendCustomer");

            var str = ''
            pdCustomers.nullToStr();
            for (var i = 0; i < pdCustomers.length; i++) {
                str += '<tr>' +
                    '<td>'+ (Number(i) + 1)+'</td>'+
                    '<td>'+pdCustomers[i].code+'</td>'+
                    '<td>'+pdCustomers[i].fullName+'</td>'+
                    '<td>'+pdCustomers[i].createName + ' ' + formatTime(pdCustomers[i].createDate, true)+'</td>'+
                    '<td>'+pdCustomers[i].updateName + ' ' + formatTime(pdCustomers[i].updateDate, true)+'</td>'+
                    '<td>'+
                    '   <span class="link-blue" onclick="sale_seebtn($(this))">查看</span>'+
                    '   <span class="link-green" onclick="suspendCooperationBtn($(this))">恢复合作</span>'+
                    '   <div class="hd"><span class="cusProId">'+pdCustomers[i].id+'</span></div>'+
                    '</td>'+
                    '</tr>'
            }
            $(".suspendedCustomer tbody").html(str)
        }
    })
}
// creator: 李玉婷，2019-09-04 10:20:07，初始化上传图片
function initUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"上传",
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            var data = JSON.parse(data)
            let len =  obj.siblings("div.imgWall").find(".imgsthumb").length;
            if(len < 9){
                $(".uploadify-queue").html("");
                //file 文件上传返回的参数  data 接口返回的参数
                var path = data.filename //路径（包含文件类型）
                //name = file.name,           //文件名称
                obj.parent().attr("groupUuid", data.groupUuid )
                var imgStr =
                    '<div class="imgsthumb">' +
                    '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                    '    <span fileUid="' + data.fileUid + '" onclick="cancleThis($(this))">删除</span> ' +
                    '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                    '</div>';
                obj.siblings("div.imgWall").append(imgStr);
            }else{
                layer.msg('最多只能上传9张')
                let fileUid = data.fileUid
                cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
            }
        }
    });
}
// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initCardUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        itemTemplate: '',
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var path = data.filename //路径（包含文件类型）
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="bussnessCard">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span fileUid="' + data.fileUid + '" class="link-blue" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            $('#uploadCard').hide();
            $('#uploadCard').before(imgStr);
        }
    });
}
// creator: 李玉婷，2019-09-04 19:31:34，删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
	obj.parent().remove();
    $('#uploadCard').show();
}
// creator: 李玉婷，2019-08-21 14:46:56，图片删除
function cancleThis(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent(".imgsthumb").remove();
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.attr('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");
}
// creator: 李玉婷，2019-09-17 16:44:02，是否购买商品点击事件
function purchasedAny (obj){
    if(obj.val() == '1' || obj.val() == 1){
        obj.parents('form').find(".purchased").show();
    }else{
        obj.parents('form').find(".purchased").hide();
        obj.parents('form').find(".purchased").find("input[name=initialPeriod]").val('').hide();
        obj.parents('form').find(".purchased").find("input[type=radio]").prop('checked',false);
        obj.parents('form').find(".hasContract").hide();
        obj.parents('form').find(".hasContract input").val('')
    }
}
// creator: 张旭博，2020-10-13 09:52:57，是否签订合同
function isContract (obj){
    if(obj.val() == '1' || obj.val() == 1){
        obj.parents('form').find(".hasContract").show();
    }else{
        obj.parents('form').find(".hasContract").hide();
    }
}
// creator: 李玉婷，2019-09-04 14:17:55，选择今年
function checkMonth(obj) {
	if(obj.val() == '1' || obj.val() == 1){
        obj.parent().siblings("input").show();
	}else{
        obj.parent().siblings("input").hide().val("");
    }
}
// creator: 李玉婷，2019-09-04 18:55:15，添加
function addMore (obj){
	obj.next("select").show();
}
function setShList(data){
    let able1 = false, areaAble = false;
    let flag = ``, temp = ``, html = ``, str = ``;
    for (var i=0; i < data.length; i++) {
        if (data[i].type === 1) {
            able1 = true;
            flag = `addAddress`
        }else if (data[i].type === 3) {
            flag = `addArea`
            areaAble = true;
        }
        temp =
            '<tr>' +
            '    <td>' + data[i].address + '</td>'+
            '    <td>' + data[i].contact + '</td>' +
            '    <td>' + data[i].mobile + '</td>' +
            '    <td>' +
            '       <span class="link-blue funBtn" data-type="update" data-fun="'+ flag +'" data-source="addCustomer">修改</span>' +
            '       <span class="link-red funBtn" data-type="delete" data-fun="deleteAddressData">删除</span>' +
            '       <span class="hd">'+ JSON.stringify(data[i]) +'</span>' +
            '   </td>' +
            '</tr>';
        if (data[i].type === 1) {
            html += temp;
        }else if (data[i].type === 3) {
            str += temp;
        }
    }
    if (able1 && areaAble) {
        $(".receiveList").show().find("tbody").append(html);
        $(".areaList").show().find("tbody").append(str);
    } else {
        if (able1) {
            $(".receiveList").show().find("tbody").append(html);
        } else if (areaAble) {
            $(".areaList").show().find("tbody").append(str);
        }
    }

}
// creator: 李玉婷，2019-09-05 10:43:09，发票邮寄列表
function setMailList(list) {
    var html =
		'<table class="kj-table">' +
        '<thead> ' +
        '<tr>' +
        '    <td>序号</td>' +
        '    <td>邮寄地址</td>' +
        '    <td>发票接收人</td>' +
        '    <td>邮政编码</td>' +
        '    <td>联系电话</td>' +
        '    <td>操作</td>' +
        '</tr>' +
		'</thead><tbody>';
    for (var i in list) {
    	var num = 1 + Number(i);
        html +=
            '<tr data-id="' + list[i].number + '">' +
            '    <td>' + num + '</td>' +
            '    <td>' + list[i].address + '</td>' +
            '    <td>' + list[i].contact + '</td>' +
            '    <td>' + list[i].postcode + '</td>' +
            '    <td>' + list[i].mobile + '</td>' +
            '    <td>' +
            '       <span class="link-blue node" data-type="update" data-source="addCustomer" data-name="mailInfo">修改</span>' +
            '       <span class="link-red node" data-type="delete" data-name="mailInfo">删除</span>' +
            '       <span class="hd">'+ list[i].number +'</span>' +
            '   </td>' +
            '</tr>';
    }
    html += '</tbody></table>';
        $(".mailList").html(html);
    var mailInfo = list;
    mailInfo = JSON.stringify(mailInfo)
    $("#addAccount").data('mailInfo', mailInfo);
}
// creator: 李玉婷，2019-09-07 10:16:22，输出联系人列表
function setContactList(list) {
    var html = '', rhtml = '',slice = 0;
    if(list && list.length > 0){
        html =
            '<div class="leftList"> <table class="kj-table">' +
            '<thead> ' +
            '<tr>' +
            '    <td>姓名</td>' +
            '    <td>职位</td>' +
            '    <td>操作</td>' +
            '</tr>' +
            '</thead><tbody>';
        if(list.length >= 2) rhtml = html;
        for (var i=0;i < list.length; i++) {
            list[i]['number'] = list[i].number || list[i].id
            slice = i %2 ;
            if(slice > 0){
                rhtml +=
                    '<tr data-id="'+ list[i].number +'" groupUuid="'+list[i].groupUuid+'">' +
                    '    <td>' + list[i].name + '</td>' +
                    '    <td>' + list[i].post + '</td>' +
                    '    <td><span class="link-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer">修改</span><span class="link-red node" data-type="delete" data-name="contactInfo">删除</span></td>' +
                    '</tr>';
            }else{
                html +=
                    '<tr data-id="'+ list[i].number +'" groupUuid="'+list[i].groupUuid+'">' +
                    '    <td>' + list[i].name + '</td>' +
                    '    <td>' + list[i].post + '</td>' +
                    '    <td><span class="link-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer">修改</span><span class="link-red node" data-type="delete" data-name="contactInfo">删除</span></td>' +
                    '</tr>';
            }
        }
        html += '</tbody></table></div>';
        if(list.length >= 2) rhtml += '</tbody></table></div>';
    }
    var str = html + rhtml;
    var tt = JSON.stringify(list);
    $("#addAccount").data('contactInfo', tt);
    return str;
}

// creator: 李玉婷，2019-09-06 11:12:09，主页对客户列表的修改
function indexUpdateList(obj, cRenewalType){
    if (chargeRole("超管")) { // 0 表示超管
        $("#mtTip .tip").html("您没有此权限！");
        bounce_Fixed.show($("#mtTip"));
        return false ;
    }
    cus_seeTrObj = obj;
    var nodeId = obj.siblings(".hd").find(".cusProId").html();
    var cusInfo = obj.siblings(".hd").find(".cusInfo").html();
    $("#updateCustomerPanel").data("id", nodeId);
    $("#updateCustomerPanel").data("cusInfo", JSON.parse(cusInfo));
    var custonerName = $.trim(obj.parents("tr").children().eq(2).html());
    $("#customer_main").html(custonerName);
    $(".contractItem").remove();
    $(".shAddressItem").remove();
    $(".mailAddressItem").remove();
    $(".contactItem").remove();
    $.ajax({
        url: '../sales/getUpdatePageData.do',
        data: {
            'customerId': nodeId
        },
        success: function (data) {
            var status = data.status;
            if (status === '1' || status === 1) {
                var contactsList = data.contactsList;
                var fpAddressList = data.fpAddressList;
                var shAddressList = data.shAddressList;
                var contractBaseList = data.contractBaseList;
                if(data.deliveryType === 2){
                    let addHtml = '', areaHtml = ``;
                    let hasAddress =
                        `<td>收货地址（需送货上门情况下）</td>
                         <td>
                             <span class="link-blue edit" data-name="receiveInfo" data-type="new" data-source="updateCustomer">新增</span>
                             <span class="link-blue edit" data-name="receiveStopData" data-type="1" data-source="updateCustomer">已被停用的数据</span>
                         </td>`;
                    let hasArea =
                        `<tr class="shAddressItem"><td>到货区域（需配送至某城市情况下）</td>
                         <td>
                             <span class="link-blue edit" data-name="updateAreaInfo" data-type="new" data-source="updateCustomer">新增</span>
                             <span class="link-blue edit" data-name="receiveStopData" data-type="3" data-source="updateCustomer">已被停用的数据</span>
                         </td></tr>`;
                    for(var d in shAddressList){
                        if (shAddressList[d].enabled){
                           if (shAddressList[d].type === '1') {
                                addHtml +=
                                    '<tr class="shAddressItem" data-id="'+ shAddressList[d].id +'">' +
                                    '    <td><div class="sign">'+ shAddressList[d].address +'</div></td>' +
                                    '    <td>' +
                                    '        <span class="link-blue edit" data-name="receiveInfo" data-type="update" data-source="updateCustomer">修改</span>' +
                                    '        <span class="link-blue" data-type="shAddressRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                    '        <span class="link-red" data-able="0" onclick="turnBtn($(this))">停用</span>' +
                                    '    </td>' +
                                    '</tr>';
                           } else if (shAddressList[d].type === '3') {
                               areaHtml +=
                                   '<tr class="shAddressItem" data-id="'+ shAddressList[d].id +'">' +
                                   '    <td><div class="sign">'+ shAddressList[d].address +'</div></td>' +
                                   '    <td>' +
                                   '        <span class="link-blue edit" data-name="updateAreaInfo" data-type="update" data-source="updateCustomer">修改</span>' +
                                   '        <span class="link-blue" data-type="areaRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                   '        <span class="link-red" data-able="0" onclick="turnBtn($(this))">停用</span>' +
                                   '    </td>' +
                                   '</tr>';
                           }
                        }
                    }
                    let result = addHtml + hasArea + areaHtml;
                    $("#receivePanel").html(hasAddress);
                    $("#receivePanel").after(result);
                } else {
                    html +=
                        '    <td>不向该客户提供实体货物，故未编辑收货信息</td>' +
                        '    <td>' +
                        '        <span class="link-blue funBtn" data-fun="receiveInfo" data-source="updateReceiveInfo">编辑</span>' +
                        '    </td>';
                    $("#receivePanel").html(html);
                }
                if(contractBaseList.length > 0){
                    var html = '';
                    contractBaseList.forEach(function(cIm){
                        html += `
                            <tr class="contractItem" data-id="${cIm.id}">
                                <td><div class="sign">${cIm.sn}</div></td>
                                <td>
                                    <span class="link-blue node" data-name="cScan" >查看</span>
                                    <span class="link-blue node" data-name="contractInfo" data-type="update" data-source="updateCustomer">修改</span>
                                    <span class="link-blue node" data-name="contractInfo" data-type="cRenewal" data-source="updateCustomer">续约</span>
                                    <span class="link-red node" data-name="cEnd">暂停履约/合同终止</span>
                                    <span class="hd">${JSON.stringify(cIm)}</span>
                                </td>
                            </tr>
                        `
                    });
                    $("#contractPanel").after(html);
                }
                if(fpAddressList.length > 0){
                    var html = '';
                    for(var d in fpAddressList){
                        if (fpAddressList[d].enabled){
                            html +=
                                '<tr class="mailAddressItem" data-id="'+ fpAddressList[d].id +'">' +
                                '    <td><div class="sign">'+ fpAddressList[d].address +'</div></td>' +
                                '    <td>' +
                                '        <span class="link-blue edit" data-name="mailInfo" data-type="update" data-source="updateCustomer">修改</span>' +
                                '        <span class="link-blue" data-type="fpAddressRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '        <span class="link-red" data-able="0" onclick="turnBtn($(this))">停用</span>' +
                                '    </td>' +
                                '</tr>';
                        }else{
                            html +=
                                '<tr class="mailAddressItem" data-id="'+ fpAddressList[d].id +'">' +
                                '    <td><div class="signed">'+ fpAddressList[d].address +'</div></td>' +
                                '    <td>' +
                                '        <span class="link-red" data-able="1" onclick="turnBtn($(this))">启用</span>' +
                                '        <span class="link-blue" data-type="fpAddressRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $("#mailPanel").after(html);
                }
                if(contactsList.length > 0){
                    var html = '';
                    for(var d in contactsList){
                        if (contactsList[d].enabled){
                            html +=
                                '<tr class="contactItem" data-id="'+ contactsList[d].id +'">' +
                                '    <td><div class="sign">' +
                                '           <span>'+ contactsList[d].name +'</span>' +
                                '           <span>'+ contactsList[d].post +'</span>' +
                                '           <span>'+ contactsList[d].mobile +'</span>'+ '</div></td>' +
                                '    <td>' +
                                '        <span class="link-blue edit" data-name="contactInfo" data-type="update" data-source="updateCustomer">修改</span>' +
                                '        <span class="link-blue" data-type="contactRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '        <span class="link-red" onclick="deleteContact($(this))">删除</span>' +
                                '    </td>' +
                                '</tr>';
                        }else{
                            html +=
                                '<tr class="contactItem" data-id="'+ contactsList[d].id +'">' +
                                '    <td><div class="signed">' + contactsList[d].name +'</div></td>' +
                                '    <td>' +
                                '        <span class="link-blue" data-type="contactRecords" data-obj="update" onclick="getRecordList($(this))">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $("#contactPanel").after(html);
                }
                bounce.show($("#updateCustomerPanel"));
                if(cRenewalType == "cRenewalHistory"){
                    var id = $("#updateCustomerPanel").data("id")
                    contractEndData(id )
                }
            } else {
                layer.msg("查看失败！");
            }
        }
    })
    $.ajax({
        url: $.webRoot + "/sales/listContractBase.do",
        data: { customer: nodeId, type: 1 },
        success:function (res) {
            let data = res.data || []
            let list = data.contractBaseList
            let str = ``;
            list.forEach(function (item) {
                str += `
                    <tr class="contractItem" data-id="${item.id}">
                                <td><div class="sign">${item.sn}</div></td>
                                <td>
                                    <span class="link-blue node" data-name="cScan" >查看</span>
                                    <span class="link-blue node" data-name="contractInfo" data-type="update" data-source="updateCustomer">修改合同信息</span>
                                    <span class="link-blue node" data-name="contractInfo" data-type="cRenewal" data-source="updateCustomer">续约</span>
                                    <span class="link-red node" data-name="cEnd">暂停履约/合同终止</span>
                                    <span class="hd">${JSON.stringify(item)}</span>
                                </td>
                            </tr>
                 `;
            })
            $("#contractPanel").after(str);
        }
    })
}
// creator: 李玉婷，2019-09-06 14:26:59，图片字符串输出
function setImgHtml(type,imgs) { // type: 1=查看 2= 修改
    var html = '';
    for(var e in imgs){
        var path = imgs[e].normal;
        html +=
            '<div class="imgsthumb">' +
            '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>';
        if(type == '2'){
            html += '    <span onclick="cancleThis($(this))">删除</span> ';
        }
        html +=
        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a></div>';
    }
    return html;
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContact li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li>' +
                '<span class="sale_ttl1">手机：</span>' +
                '<span class="cellCon"><input type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="link-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '2':
            html +=
                '<li>' +
                '<span class="sale_ttl1">QQ：</span>' +
                '<span class="cellCon"><input type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="link-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '3':
            html +=
                '<li>' +
                '<span class="sale_ttl1">Email：</span>' +
                '<span class="cellCon"><input type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="link-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '4':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微信：</span>' +
                '<span class="cellCon"><input type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="link-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '5':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微博：</span>' +
                '<span class="cellCon"><input type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="link-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed4.show($("#useDefinedLabel"));
            setTimer('useDefinedLabel');
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-07 08:50:59，删除添加的联系方式
function removeAdd(obj) {
    obj.parent("li").remove();
    if ($(".otherContact li").length > 0) {
        $(".otherContact li").each(function () {
            var val = $(this).find("input").val();
            var type = $(this).find("input").data('type');
            if (type == '9' || type == '9') type = 6
            if (val == '') {
                $("#addMoreContact option").eq(type).prop('disabled', true);
            }else {
                $("#addMoreContact option").eq(type).prop('disabled', false);
            }
        })
    }else{
        $("#addMoreContact option").prop('disabled', false);
    }
}
// creator: 李玉婷，2019-09-07 16:09:24，获取客户收货，发票地址
function setAddress(type, json){
    $.ajax({
        url: '../sales/getAddressData.do',
        data: json,
        success: function (data) {
            var status = data.status;
            if (status === '1' || status === 1) {
                var getData = data.data;
                var res = {
                    "address" : getData.address,
                    "type" : getData.type,
                    "name" : getData.contact,
                    "contact" : getData.contact,
                    "post" : getData.postcode,
                    "mobile" : getData.mobile,
                    "linkId" : getData.id,
                    "customerContact" : getData.customerContact,
                    "id" : getData.customerContact,
                }
                if(type == '1'){
                    $("#ReceiveName")
                        .val(res.name)
                        .siblings(".hd").html(JSON.stringify(res)) ;
                    $("#newReceiveAddressInfo").data('id', res.linkId);
                    $("#ReceiveAddress").val(res.address) ;
                }else if(type == '2'){
                    $("#newMailInfo").data('id',  res.linkId);
                    $("#mailName")
                        .val(res.name)
                        .siblings(".hd").html(JSON.stringify(res)) ;
                    $("#mailAddress").val(res.address) ;
                }else if(type == '3'){
                    $("#newReceiveAreaInfo").data('id',  res.linkId);
                    $("#regionCon")
                        .val(res.address)
                        .siblings(".hd").html(getData.regionCode) ;
                    $("#areaName")
                        .val(res.name)
                        .siblings(".hd").html(JSON.stringify(res)) ;
                    $("#requirements").val(getData.requirements) ;
                }
            } else {
                layer.msg("查看失败！");
            }
        }
    })
}
// creator: 李玉婷，2019-09-09 17:19:22，修改联系人-相同输出部分字符串拼接
function setContact(data) {
    var socialList = data.socialList;
    $("#contactFlag").html(data.tags ||'其他');
    $("#contactName").val(data.name).data('org',data.name);
    $("#position").val(data.post).data('org',data.post);
    $("#contactNumber").val(data.mobile).data('org',data.mobile);
    $("#contactsCard").data('org',data.visitCard);
    $("#addMoreContact").hide();
    if (data.visitCard && data.visitCard != '' && data.visitCard != 'undefined' && data.visitCard != 'null'){
        var path = data.visitCard;
        var imgStr =
            '<div class="bussnessCard">' +
            '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
            '   <span class="link-blue" onclick="cancleCard($(this))">删除</span> ' +
            '</div>';
        $("#uploadCard").hide();
        $("#uploadCard").before(imgStr);
    }else{
        $("#uploadCard").show();
    }
    if(socialList.length > 0){
        var html = '';
        for(var r in socialList){
            html +=
                '<li>' +
                '<span class="sale_ttl1">'+ socialList[r].name +'：</span>' +
                '<span class="cellCon"><input type="text" value="'+ socialList[r].code +'" placeholder="请录入" data-type="'+ socialList[r].type +'" data-name="'+ socialList[r].name +'" data-org="'+ socialList[r].code +'" require/></span>'+
                '<span class="link-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
        }
        $(".otherContact").html(html).data('length', socialList.length);
    }
}
function bounceCancel() {
    let isStop = $("#from").val();
    if(isStop == 'ok'){
        bounce.show($("#stopDelContact"))
    }
    bounce_Fixed2.cancel()
}
// creator: 李玉婷，2019-09-09 19:52:16，修改记录列表获取公用方法
function getRecordList(obj) {
    var type = obj.data('type');
    var getObj = obj.data('obj');
    var customerId = '';
    if(getObj == 'see'){
        customerId = $("#seeAccount").data("id");
    }else if(getObj == 'update'){
        customerId = $("#updateCustomerPanel").data("id");
    }else if(getObj == 'interview'){
        customerId = obj.parents("tr").data("id");
    }
    switch (type) {
        case 'baseRecords':
            $(".recordTtl").html('基本信息修改记录');
            $.ajax({
                url: '../sales/getRecordBaseList.do',
                data: {
                    'customerId': customerId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'invoiceRecords':
            $(".recordTtl").html('开票信息修改记录');
            $.ajax({
                url: '../sales/getRecordInvoiceList.do',
                data: {
                    'customerId': customerId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'contactRecords':
            $(".recordTtl").html('联系人修改记录');
            $.ajax({
                url: '../sales/getRecordContactList.do',
                data: {
                    'contactId': obj.parents('tr').data('id')
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'areaRecords':
        case 'shAddressRecords':
        case 'fpAddressRecords':
            var addressId = obj.parents('tr').data('id');
            var isStop = obj.parents('tr').data('isstop');
            $("#from").val(isStop)
            $.ajax({
                url: '../sales/getRecordAddressList.do',
                data: {
                    'addressId': addressId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        if(type == 'areaRecords'){
                            $(".recordTtl").html('到货区域修改记录');
                        }else if(type == 'shAddressRecords'){
                            $(".recordTtl").html('收货地址修改记录');
                        }else{
                            $(".recordTtl").html('发票邮寄信息修改记录');
                        }
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
    }
}
/*修改记录前、后查看*/
function cus_recordDetail(obj){
    $(".initValue").html("");
    var json ={
        'id': obj.data('id'),
        'frontId': obj.data('frontid')
    };
    var seeType = obj.data('type');
    switch(seeType){
        case 'baseRecords':
            $.ajax({
                url : "../sales/getRecordBaseDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    var name = nowData.fullName;
                    var fullName = nowData.name;
                    var code = nowData.code;
                    var address = nowData.address;
                    var supervisorName = nowData.supervisorName;
                    var supervisorMobile = nowData.supervisorMobile;
                    var memo = nowData.memo;
                    var infoSource = nowData.infoSource;
                    var initialPeriod = nowData.initialPeriod;
                    var firstContactTime = new Date(nowData.firstContactTime).format('yyyy-MM-dd');
                    var createDate = new Date(nowData.createDate).format('yyyy-MM-dd hh:mm:ss')
                    var firstContactAddress = nowData.firstContactAddress;
                    var qImages = nowData.qImages;
                    var pImages = nowData.pImages;
                    var date = '';
                    if (qImages.length > 0){
                        var html = setImgHtml(1,qImages);
                        $("#base_qImgUpload").html(html);
                    }
                    if (pImages.length > 0){
                        var html = setImgHtml(1,pImages);
                        $("#base_pImgUpload").html(html);
                    }


                    // 处理是否购买过本公司的产品
                    var hasBuyStr = ''
                    var initialType = nowData.initialType;
                    if (initialType === null) {
                        hasBuyStr = '<div class="ty-alert ty-alert-error">'+createDate + '之前该客户尚未购买过本公司的商品</div>'
                    } else {
                        if(initialType === '1'){
                            var month = initialPeriod.substr(4,2);
                            if(month.slice(0,1) == '0'){
                                month = month.substr(1,1);
                            }
                            date = initialPeriod.substr(0,4) + '年'  + month  + '月';
                            hasBuyStr = '<div class="ty-alert ty-alert-success">'+date+'该客户首次购买过本公司的商品</div>'
                        }else if(initialType === '2'){
                            hasBuyStr = '<div class="ty-alert ty-alert-success">去年该客户首次购买过本公司的商品</div>'
                        }else{
                            hasBuyStr = '<div class="ty-alert ty-alert-success">去年之前该客户首次购买过本公司的商品</div>'
                        }
                        // 处理是否签合同
                        var hasContract = nowData.hasContract
                        if (hasContract === '1') {
                            var sn = []
                            var contractSn = nowData.contractSn
                            var expiresTime = nowData.expiresTime
                            var contractTime = nowData.contractTime

                            if (contractTime) sn.push('签署日期为'+new Date(contractTime).format("yyyy年MM月dd日"))
                            if (expiresTime) sn.push('有效期至'+new Date(expiresTime).format("yyyy年MM月dd日"))
                            if (contractSn) sn.push('合同编号为'+contractSn)
                            if (sn.length === 0) {
                                hasBuyStr += '<div class="ty-alert ty-alert-success">与该客户有处于有效期内的合同</div>'
                            } else {
                                hasBuyStr += '<div class="ty-alert ty-alert-success">与该客户有'+sn.join("、")+'的合同</div>'
                            }
                        } else {
                            hasBuyStr += '<div class="ty-alert ty-alert-error">目前与该客户无处于有效期内的合同</div>'
                        }
                    }
                    $("#BaseRecordsDetail .firstBuyTime").html(hasBuyStr)

                    if(frontData == null){
                        $("#BaseRecordsDetail .detaileTtl").html('原始信息');
                        $("#base_cusname").html(name);
                        $("#base_cusfullName").html(fullName);
                        $("#base_cuscoding").html(code);
                        $("#base_address").html(address);
                        $("#base_supervisorName").html(supervisorName);
                        $("#base_supervisorMobile").html(supervisorMobile);
                        $("#base_firstContactTime").html(firstContactTime);
                        $("#base_firstAddress").html(firstContactAddress);
                        $("#base_infoSource").html(infoSource);
                        $("#base_memo").html(memo);
                    }else{
                        $("#BaseRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#base_cusname").html(compareD(frontData.name,nowData.name));
                        $("#base_cusfullName").html(compareD(frontData.fullName,nowData.fullName));
                        $("#base_cuscoding").html(compareD(frontData.code,nowData.code));
                        $("#base_address").html(compareD(frontData.address,nowData.address));
                        $("#base_supervisorName").html(compareD(frontData.supervisorName, nowData.supervisorName));
                        $("#base_supervisorMobile").html(compareD(frontData.supervisorMobile, nowData.supervisorMobile));
                        $("#base_firstContactTime").html(compareD(new Date(frontData.firstContactTime).format('yyyy-MM-dd'),new Date(nowData.firstContactTime).format('yyyy-MM-dd')));
                        $("#base_firstAddress").html(compareD(frontData.firstContactAddress,nowData.firstContactAddress));
                        $("#base_infoSource").html(compareD(frontData.infoSource,nowData.infoSource));
                        $("#base_memo").html(compareD(frontData.memo,nowData.memo));
                    }
                    bounce_Fixed3.show($("#BaseRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'invoiceRecords':
            $.ajax({
                url : "../sales/getRecordInvoiceDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(frontData == null){
                        $("#invoiceRecordsDetail .detaileTtl").html('原始信息');
                        $("#inv_invoiceName").html(nowData.invoiceName);
                        $("#inv_phone").html(nowData.telephone);
                        $("#inv_invoiceAddress").html(nowData.invoiceAddress);
                        $("#inv_bank").html(nowData.bankName);
                        $("#inv_bankNo").html(nowData.bankNo);
                        $("#inv_taxpayerID").html(nowData.taxpayerID);
                    }else{
                        $("#invoiceRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#inv_invoiceName").html(compareD(frontData.invoiceName,nowData.invoiceName));
                        $("#inv_phone").html(compareD(frontData.telephone,nowData.telephone));
                        $("#inv_invoiceAddress").html(compareD(frontData.invoiceAddress,nowData.invoiceAddress));
                        $("#inv_bank").html(compareD(frontData.bankName,nowData.bankName));
                        $("#inv_bankNo").html(compareD(frontData.bankNo,nowData.bankNo));
                        $("#inv_taxpayerID").html(compareD(frontData.taxpayerID,nowData.taxpayerID));
                    }
                    bounce_Fixed3.show($("#invoiceRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'areaRecords':
        case 'shAddressRecords':
            let ttl = seeType === 'shAddressRecords'? '收货地址：':'到货区域：'
            $(".shAddressTtl").html(ttl);
            $.ajax({
                url : "../sales/getRecordShAddressDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(nowData.operation == '2' || frontData == null){
                        if(nowData.operation == '2'){
                            var tip = nowData.enabled == '1'? '本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+ nowData.updateName +'启用。':'本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+nowData.updateName+'停用。'
                            $('.shDisUse').show().html(tip);
                        }else{
                            $('.shDisUse').hide();
                        }
                        $("#shRecordsDetail .detaileTtl").html('原始信息');
                        $("#shAddress").html(handleNull(nowData.address));
                        $("#shName").html(handleNull(nowData.contact));
                        $("#shNumber").html(handleNull(nowData.mobile));
                    }else{
                        $('.shDisUse').hide().html('');
                        $("#shRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#shAddress").html(compareD(frontData.address,nowData.address));
                        $("#shName").html(compareD(frontData.contact,nowData.contact));
                        $("#shNumber").html(compareD(frontData.mobile,nowData.mobile));
                    }
                    bounce_Fixed3.show($("#shRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'fpAddressRecords':
            $.ajax({
                url : "../sales/getRecordFpAddressDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(nowData.operation == '2' || frontData == null){
                        if(nowData.operation == '2'){
                            var tip = nowData.enabled == '1'? '本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+nowData.updateName+'启用。':'本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+nowData.updateName+'停用。'
                            $('.fpDisUse').show().html(tip);
                        }else{
                            $('.fpDisUse').hide();
                        }
                        $("#fpRecordsDetail .detaileTtl").html('原始信息');
                        $("#fpAddress").html(handleNull(nowData.address));
                        $("#fpName").html(handleNull(nowData.contact));
                        $("#fpNumber").html(handleNull(nowData.postcode));
                        $("#fpMobile").html(handleNull(nowData.mobile));
                    }else{
                        $('.fpDisUse').html('').hide();
                        $("#fpRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#fpAddress").html(compareD(frontData.address,nowData.address));
                        $("#fpName").html(compareD(frontData.contact,nowData.contact));
                        $("#fpNumber").html(compareD(frontData.postcode,nowData.postcode));
                        $("#fpMobile").html(compareD(frontData.mobile,nowData.mobile));
                    }
                    bounce_Fixed3.show($("#fpRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'contactRecords':
            $.ajax({
                url : "../sales/getRecordContactDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front']||{};
                    var beforeSocialList = frontData ? frontData.socialList :[];
                    var socialList = nowData.socialList;
                    var frontSocialList = [];
                    if(frontData == null){
                        $("#contactRecordsDetail .detaileTtl").html('原始信息');
                        $("#record_contactName").html(nowData.name);
                        $("#record_position").html(nowData.post);
                    }else{
                        $("#contactRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#record_contactName").html(compareD(frontData.name,nowData.name));
                        $("#record_position").html(compareD(frontData.post,nowData.post));
                    }
                    $("#record_contactsCard").html("");
                    if (nowData.visitCard != '' && nowData.visitCard != undefined && nowData.visitCard != null){
                        var path = nowData.visitCard;
                        var imgStr =
                            '<div class="bussnessCard">' +
                            '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                            '</div>';
                        $("#record_contactsCard").html(imgStr);
                    }

                    for(let q in socialList){
                        let qItem = socialList[q], getSame = false;
                        for(let w in beforeSocialList){
                            let wItem = beforeSocialList[w];
                            if(qItem['contactSocial'] == wItem['contactSocial']){
                                getSame = true
                                if(qItem['name'] != wItem['name']){
                                    qItem['nameChange'] = true;
                                }
                                if(qItem['code'] != wItem['code']){
                                    qItem['codeChange'] = true;
                                }
                            }
                        }
                        if(!getSame){
                            qItem['nameChange'] = true;
                            qItem['codeChange'] = true;
                        }
                    }
                    socialList.push({ name: "手机", code:nowData.mobile, codeChange:(nowData.mobile != frontData.mobile),type: "1"})
                    //
                    let sortList = [];
                    for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
                    for(var r in socialList){
                        let item = socialList[r];
                        let _index = Number(item.type);
                        sortList[_index].push(item);
                    }
                    let sortAfter = [];
                    for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
                    let allStr = '';
                    for(var t in sortAfter){
                        let item = sortAfter[t];
                        if(t%2===0){
                            allStr += `<tr><td class="${item.nameChange?'link-red':''}">${item.name}</td><td class="${item.codeChange?'link-red':''}">${item.code}</td>`
                        }else{
                            allStr += `<td class="${item.nameChange?'link-red':''}">${item.name}</td><td class="${item.codeChange?'link-red':''}">${item.code}</td></tr>`
                        }
                    }
                    if(sortAfter.length % 2 !== 0){
                        allStr += `<td> </td><td> </td></tr>`
                    }
                    $(".record_otherContact").html(allStr);
                    bounce_Fixed3.show($("#contactRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'contactSocial':
            var source = obj.data('source');
            if(source == 'addCustomer') {
                let info = obj.siblings(".hd").html()
                info = JSON.parse(info)
                info['socialList'] = JSON.parse(info.socialList)
                see_otherContactStr(info)
            }else{
                $.ajax({
                    url : "../sales/getContactsSocial.do" ,
                    data : {
                        'contactId': obj.data('id')
                    },
                    success:function(data){
                        var get = data['data'];
                        see_otherContactStr(get)
                    },
                    error: function (msg) {
                        layer.msg("连接错误，请稍后重试！");
                        return false;
                    }
                });
            }

            break;
        case 'visitCard':
            $.ajax({
                url : "../sales/getContactCard.do" ,
                data : {
                    'contactId': obj.data('id')
                },
                success:function(data){
                    var status = data.status;
                    if (status == '1') {
                        if(data.visitCard && data.visitCard != "" && data.visitCard != null){
                            $('#see_contactsCard').html('<img src="' + $.fileUrl + data.visitCard +'" />');
                        }else{
                            $('#see_contactsCard').html('');
                        }
                    }
                    bounce_Fixed.show($("#visitCardDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'interview':
            $.ajax({
                url : "../sales/getRecordInterviewDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    $("#inputInfo").html('录入者：' + ' &nbsp; ' + nowData.createName + ' &nbsp; ' + new Date(nowData.createDate).format('yyyy-MM-dd hh:mm:ss'));
                    if(frontData == null){
                        $("#seeInterviewInfo .detaileTtl").html('原始信息');
                        $("#see_interviewee").html(nowData.interviewer + '&nbsp;&nbsp;' + nowData.post);
                        $("#see_interviewContent").html(nowData.content);
                        $("#see_interviewDate").html(new Date(nowData.interviewDate).format('yyyy年MM月dd日'));
                    }else{
                        $("#seeInterviewInfo .detaileTtl").html(obj.parent().prev().html());
                        $("#see_interviewee").html(compareD(frontData.interviewer,nowData.interviewer) + '&nbsp;&nbsp;' + compareD(frontData.post,nowData.post));
                        $("#see_interviewContent").html(compareD(frontData.content,nowData.content));
                        $("#see_interviewDate").html(compareD(new Date(frontData.interviewDate).format('yyyy年MM月dd日'),new Date(nowData.interviewDate).format('yyyy年MM月dd日')));
                    }
                    bounce_Fixed2.show($("#seeInterviewInfo"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
    }
}
function see_otherContactStr(get){
    var html = '',socialList = get['socialList'];
    $("#see_contactName").html(get.name);
    $("#see_position").html(get.post);
    $("#see_contactTag").html(get.tags);
    $(".see_otherContact").html("");
    $("#contactSeeDetail .see_createName").html(get.createName);
    $("#contactSeeDetail .see_createDate").html(new Date(get.createDate).format('yyyy-MM-dd hh:mm:ss'));
    if(socialList.length > 0){
        let sortList = [];
        for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
        for(var r in socialList){
            let item = socialList[r];
            let _index = Number(item.type);
            sortList[_index].push(item);
        }
        let sortAfter = [];
        for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
        let allStr = '';
        for(var t in sortAfter){
            let item = sortAfter[t];
            if(t%2===0){
                allStr += `<tr><td>${item.name}</td><td>${item.code}</td>`
            }else{
                allStr += `<td>${item.name}</td><td>${item.code}</td></tr>`
            }
        }
        if(sortAfter.length % 2 !== 0){
            allStr += `<td> </td><td> </td></tr>`
        }
        $(".see_otherContact").html(allStr);
    }
    bounce_Fixed4.show($("#contactSeeDetail"));
}
// creator: 李玉婷，2019-09-10 16:43:23，基本信息、发票信息修改记录列表数据获取
function getRecordsList(data,type){
    var getList = data.list;
    if(getList.length > 0) {
        var str = '';
        var eidtNumber = getList.length - 1;
        $("#updateRecords .recordTip").html('当前数据为第' + eidtNumber + '次修改后的结果。');
        $("#updateRecords .recordEditer").html('修改时间：' + data.updateName + ' ' + new Date(data.updateDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").show();
        type === 'invoiceRecords' ? $(".recordFinance").show():$(".recordFinance").hide();
        for (let r in getList) {
            var lastTd = '';
            if (r == '0') {
                if (type === 'invoiceRecords') {
                    lastTd = ` <td>——</td>`;
                }
                str +=
                    '<tr>' +
                    '   <td>原始信息</td>' +
                    '   <td><span class="link-blue" data-id="' + getList[r].id + '" data-frontid="0" data-type="'+ type +'" onclick="cus_recordDetail($(this))">查看</span></td>' +
                    '   <td>' + getList[r].createName + ' &nbsp; ' + new Date(getList[r].createDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                    lastTd +
                    '</tr>';
            }
            else {
                var front = Number(r) - 1;
                if (type === 'invoiceRecords') {
                    lastTd = '<td>' + (getList[r].financerName || '') + ' &nbsp; ' + new Date(getList[r].financeTime).format('yyyy-MM-dd hh:mm:ss') + '</td>';
                }
                str +=
                    '<tr>' +
                    '   <td>第' + r + '次修改后</td>' +
                    '   <td><span class="link-blue" data-id="' + getList[r].id + '" data-frontid="' + getList[front].id + '" data-type="'+ type +'" onclick="cus_recordDetail($(this))">查看</span></td>' +
                    '   <td>' + getList[r].updateName + ' &nbsp; ' + new Date(getList[r].updateDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' + lastTd +
                    '</tr>';
            }
        }
        $(".changeRecord tbody").html(str);
    }else {
        $("#updateRecords .recordTip").html('当前资料未经修改');
        $("#updateRecords .recordEditer").html('创建人：'+ data.createName + '&nbsp;&nbsp;' + new Date(data.createDate).format('yyyy-MM-dd hh:mm:ss'));
        $(".changeRecord").hide();
    }
    bounce_Fixed2.show($("#updateRecords"));
}
//creator:lyt date:2018/11/28 比较修改记录修改前后不同
function compareD(front,now){
    if(front == now){
        return '<span>' + handleNull(now) +' </span>'
    }else{
        return '<span class="redFlag">' + handleNull(now) + '</span>'
    }
}

// creator: 李玉婷，2019-09-19 09:37:24，清除自定义标签
function clearLableText(obj) {
    obj.siblings().val("");
}
// creator: 李玉婷，2019-09-19 10:14:23，自定义标签确定
function addNewLable(){
    var val = $("#defLable").val();
    var html =
        '<li>' +
        '<span class="sale_ttl1">' + val + '：</span>' +
        '<span class="cellCon"><input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/></span>'+
        '<span class="link-red" onclick="removeAdd($(this))">删除</span>'+
        '</li>';
    $(".otherContact").append(html);
    bounce_Fixed4.cancel();
}

// creator: 张旭博，2020-04-02 14:24:21,获取机构列表
function getOrganizationList(customerId) {
    $.ajax({
        url: '../special/getCustomerOrganizationsByCustomerId.do',
        data: { customerId: customerId },
        success: function (res) {
            var data = res.data;
            if (data) {
                var str = ''
                for (var i in data) {
                    str += '<tr data-id="' + data[i].id + '">' +
                        '    <td>' + data[i].name + '</td>' +
                        '    <td>' + data[i].fullName + '</td>' +
                        '    <td>' + data[i].supervisorName + '</td>' +
                        '    <td>' + data[i].supervisorMobile + '</td>' +
                        '    <td>' + (data[i].address || '——') + '</td>' +
                        // '    <td>' + (data[i].registeredAddress || '——') + '</td>' +
                        '    <td>' + data[i].createName + ' ' + formatTime(data[i].createDate, 'time') + '</td>' +
                        '    <td>' +
                        '        <span type="btn" data-name="orgInfo" class="link-blue">机构信息</span>' +
                        '        <span type="btn" data-name="productInfo" class="link-blue">产品信息</span>' +
                        '        <span type="btn" data-name="addService" class="' + (data[i].mpPackageId !== null ? 'link-blue' : 'link-gray') + '">增值服务</span>' +
                        '        <span type="btn" data-name="userLoginRecord" class="link-blue">登录记录</span>' +
                        '        <span type="btn" data-name="resouceManage" class="link-blue">空间与流量</span>' +
                        '        <span type="btn" data-name="stopService" class="link-red">暂停服务</span>' +
                        '    </td>' +
                        '</tr>'
                }
                $(".orgList tbody").html(str)
            } else {
                $(".orgList tbody").html("")
            }
        }
   })
}

// creator: hxz 2021-04-08 获取空间与资源使用情况
function getResouceInfo(thisObj) {
    let id = thisObj.parent().parent().data("id")
    $.ajax({
        "url": "../st/getSpaceTrafficInfo.do",
        "data": { "id": id },
        success:function (data) {
            let res = data.data
            if(res){
                bounce.show($("#resourceManage"))
                $(`#resourceManage .orgfullname`).html(res.fullName || ' --')
                $(`#resourceManage .orgname`).html(res.name || '--')
                $(`#resourceManage .zoneTop`).html(res.ratedSpace ?  res.ratedSpace :  ' --')
                $(`#resourceManage .usedZone`).html( res.usedSpace ?  res.usedSpace :  ' --')
                $(`#resourceManage .zoneduraing`).html(`${res.beginDate ? new Date(res.beginDate).format("yyyy年MM月dd日") : ' -- ' }- ${ res.endDate ? new Date(res.endDate).format("yyyy年MM月dd日") : ' --' }`)
                $(`#resourceManage .flowAll`).html( res.ratedTraffic ?  res.ratedTraffic :  ' --')
                $(`#resourceManage .flowUsed`).html( res.usedTraffic ?  res.usedTraffic :  ' --')
            }else{
                layer.msg("未获取有效数据");
            }
        }
    })

}

// creator: 张旭博，2020-04-03 11:55:23,新增机构
function newOrganization() {
    //debugger;
    var data = {}
    var selector = $("#newOrg").is(":visible")?$("#newOrg"):$("#newCusOrg")

    selector.find("[name]").each(function () {
        var key = $(this).attr("name")
        data[key] = $(this).val()
    })
    var cusInfo = $(".orgManage").data("cusInfo")
    data.customerId = cusInfo.id
    console.log(data);
    //return false;
    var orgArr = []
    $.ajax({
        url: '../special/addOrgApply.do',
        data: data,
        success: function (res) {
            var data = res.data
            bounce.cancel()
            if (data === 1) {
                bounce_Fixed.cancel()
                layer.msg("已提交申请！")
            } else {
                $("#mtTip .tip").html("操作失败！")
                bounce_Fixed.show($("#mtTip"))
            }
        }
    })
}

// creator: 张旭博，2020-04-07 11:49:02,获取机构详情
function getOrgInfo(orgId) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '../special/getCustomerOrganizationInfo.do',
            data: {  id: orgId },
            success: function (res) {
                var data = res.data
                if (data) {
                    resolve(data)
                }
            }
        })
    })

}
// creator: sy,2022-3-2 企业信息修改记录列表接口
function getOrgChHis(orgHisId) {
    $.ajax({
        url: '../site/getOrganizationHistories.do',
        data: {"id": orgHisId, "pageSize": 20, "currentPageNo": 1},
        success: function (res) {
            let data = res.data;
            console.log(data);
            let den = data.organizationHistoryList;
            console.log(den);//除去首行之外的其余所有数据
            let str2 = '';
            //下面的i是索引值，是自己设定的属性名，属性值为每行数据的索引值；但因为数据中并没有index这个属性，所以不能用存储的方法在里面寻找index；
            //而id是在数据中包含的数据，所以需要在数据中寻找
            if (den) {
                for (let i in den) {
                    if (i == 0) { //索引值为i
                        str2 +=
                            "<tr >" +
                            "<td style='text-align: center'>" + den[i].dataState + "</td>" +
                            "<td style='text-align: center'>" +
                            "<span data-id='" + den[i].id + "' data-index='" + i + "' type='btn' data-fun='ogre' class='link-blue ty-btn' style='color:#0070c0;background:transparent;font-weight:bold'>" + "查看" + "</span>" +
                            "</td>" +
                            "<td style='text-align: center'>" + den[i].operationName + " " +
                            formatTime(den[i].updateTime, true) + "</td>" +
                            "</tr>"
                    } else {
                        let front = Number(i) - 1;
                        str2 +=
                            "<tr>" +
                            "<td style='text-align: center'>" + den[i].dataState + "</td>" +
                            "<td style='text-align: center'>" +
                            "<span data-id='" + den[i].id + "' data-index='" + i + "' type='btn' data-fun='ogre' class='link-blue ty-btn' style='color:#0070c0;background:transparent;font-weight:bold'>" + "查看" + "</span>" +
                            "</td>" +
                            "<td style='text-align: center'>" + den[i].operationName + " " +
                            formatTime(den[i].updateTime, true) + "</td>" +
                            "</tr>"
                    }
                }
                console.log(str2);
                $(".bounce_Fixed2 #bodcerLine .bonceHis .orgHList tr:gt(0)").remove();//将表格标题之外的数据移除
                $(".bounce_Fixed2 #bodcerLine .bonceHis .orgHList").append(str2);
                $("#bodcerLine").data("str2");
            }
        }
    })
}

// creator: sy,2022-3-3 获取查看单一历史详情
function getOrgBegin(orgBegId, index) {
    $.ajax({
        url: '../site/getOrganizationHistoryInfo.do',
        data: {"id": orgBegId},
        success: function (res) {
            let data = res.data;
            console.log(data);
            if (data) {
                var str = '';
                // 思路：用上一个弹窗中的数据中的索引值进行比较，索引值=0时显示Loup那个div,否则显示Lope那个div
                console.log(index);
                if (index == "0") {
                    str +=
                        "<div class='Loup' style='display: block;border-bottom: 1px solid #bfbfbf;margin-bottom: 17px;margin-top: 19px'>" +
                        "<div>" + "以下为机构的原始信息" + "</div>" +
                        "<div style='margin:14px 0;margin-bottom:23px;'>" +
                        "创建人" + " " + formatTime(data.createDate, true) + "</div>" +
                        "</div>"
                } else {
                    str +=
                        "<div class='Lope hd' style='display: block;border-bottom: 1px solid #bfbfbf;margin-bottom: 17px;margin-top: 19px;'>" +
                        "<div>" + "以下为第" + index + "次修改后的机构信息" + "</div>" +
                        "<div style='margin:14px 0;margin-bottom:23px;'>" +
                        "修改人" + " " + formatTime(data.updateDate, true) + "</div>" +
                        "</div>"
                }
                //将数据插入输入框中
                for (let key in data) {
                    $("#bodcerMore .bonLock .ordge [name='" + key + "']").val(data[key]);
                    // $("#orgInfo .org_" + key).html(data[key] || '--');
                    // $("#orgInfo .orgInfo_" + key).html(data[key] || '--')
                }
                $(".bounce_Fixed3 #bodcerMore .bonLock .boxe").append(str);
                $(".bounce_Fixed3 #bodcerMore .bonLock .boxe .Loup:gt(0)").remove();
                $(".bounce_Fixed3 #bodcerMore .bonLock .boxe .Lope:gt(0)").remove();

            }
        }
    })
}

// creator: 张旭博，2020-04-07 11:49:02,获取机构详情 + 产品信息
function getProductInfoByOid(orgId, haveModule=false) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '../special/getIncrementOrgPopedom.do',
            data: {  id: orgId, haveModule: haveModule },
            success: function (res) {
                var data = res.data
                if (data) {
                    resolve(data)
                } else {
                    reject("获取数据失败")
                }
            }
        })
    })
}

// creator: 张旭博，2023-08-03 03:33:37， 获取增值信息
function getIncreaseModules(orgId) {
    // let orgId = orgId
    $.ajax({
        url: '../thali/getIncreaseModules.do',
        data: {
            id: orgId
        }
    }).then(res => {
        let data = res.data
        let orgInfo = data.orgInfo // 机构信息
        let mpPackageInfo = data.mpPackageInfo // 产品信息
        let increaseModuleList = data.increaseModuleList // 可增值模块列表
        let mpSetList = data.mpSetList // 可增值套餐列表
        let orgnizationModule = data.orgnizationModule // 机构已增值的模块，  列表中有  [ {type 类型: 按模块增值 ,2-按套餐增值     setId 套餐id，   moduleId 模块id},{}]
        let moduleStr = ''
        $("#addService").find(".ty-spanText[name='fullName']").html(orgInfo.fullName)
        $("#addService").find(".ty-spanText[name='name']").html(orgInfo.name)
        $("#addService").find(".ty-spanText[name='mpPackageName']").html(mpPackageInfo.name)
        $("#addService").find("[name='packageId']").val(mpPackageInfo.id)

        let checkAngle = '', mpSetId = '', modules = []
        if (orgnizationModule.length > 0) {
            let type = orgnizationModule[0].type
            checkAngle = type
            if (type === 1) {
                for (let item of orgnizationModule) {
                    modules.push(item.moduleId)
                }
            } else {
                mpSetId = orgnizationModule[0].setId
            }
        }

        for (let item of increaseModuleList) {
            moduleStr += `<tr data-id="${item.id}">
                        <td>
                            <div class="ty-checkbox">
                                <input type="checkbox" id="increaseModule_${item.id}" value="${item.id}" disabled>
                                <label for="increaseModule_${item.id}"></label>
                            </div>
                        </td>
                        <td class="name">${item.name}</td>
                        <td>${item.topMenu || '--'}个</td>
                        <td>
                            <span class="link-blue" onclick="seeModule($(this))">查看</span>
                        </td>
                    </tr>`
        }
        $('#addService .tbl_asModule tbody').html(increaseModuleList.length > 0?moduleStr:getNullPage())
        let mpSetStr = 'getNullPage()'

        for (let it of mpSetList) {
            mpSetStr += `<tr data-id="${it.id}">
                            <td>
                                <div class="ty-radio">
                                    <input type="radio" id="mpSet_${it.id}" value="${it.id}" name="mpSet" disabled>
                                    <label for="mpSet_${it.id}"></label>
                                </div>
                            </td>
                            <td class="name">${it.name}</td>
                            <td>
                                <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                                <span class="hd">${JSON.stringify(it)}</span>
                            </td>
                        </tr>`
        }
        $('#addService .tbl_asMpSet tbody').html(mpSetList.length > 0? mpSetStr: getNullPage())
        if (checkAngle) {
            $("#addService [name='checkAngle'][value='"+checkAngle+"']").click()
            if (checkAngle === 1) {
                modules.forEach(item => {
                    $("#addService .tbl_asModule input:checkbox[value='"+item+"']").prop("checked", true)
                })
            } else {
                $("#addService .tbl_asMpSet input:radio[value='"+mpSetId+"']").prop("checked", true)
            }
        }
    })
}

// creator: 张旭博，2023-08-04 01:10:31， 模块角度和套餐角度切换
function chooseCheckAngle() {
    let check = $("#addService [name='checkAngle']:checked").val()
    if (check === '2') {
        $(".tbl_asModule input:checkbox").prop("disabled", true).prop("checked", false)
        $(".tbl_asMpSet input:radio").prop("disabled", false)
    } else {
        $(".tbl_asModule input:checkbox").prop("disabled", false)
        $(".tbl_asMpSet input:radio").prop("disabled", true).prop("checked", false)
    }

}

// creator: 张旭博，2021-08-23 15:16:02，增值服务 - 确定
function sureAddService() {
    var oid = $(".bounce").data("data").oid
    var packageId = $("#addService [name='packageId']").val()
    let incrementModules = []
    $(".tbl_asModule input:checkbox:checked").each(function (){
        let val = $(this).val()
        incrementModules.push(val)
    })
    let setId = $(".tbl_asMpSet input:radio:checked").val()
    var data = {
        organizationId: oid, //机构id ，
        packagesId: packageId, // 产品id
        incrementModules: incrementModules.join(','),
        setId: setId || null
    }
    if (incrementModules.length === 0 && !setId) {
        layer.msg('请至少选择一项')
        return false
    }
    console.log(data)
    $.ajax({
        url: '../thali/addIncrementApply.do',
        data: data,
        success: function (res) {
            var data = res.data
            if (data === 1) {
                layer.msg("操作成功")
                bounce.cancel()
            } else {
                layer.msg("操作失败")
            }
        }
    })
}

// creator: 张旭博，2021-07-28 11:59:37，获取模板列表
function getProductOption() {
    var P_productData = new Promise((resolve, reject) => {
        $.ajax({
            url:"../productSetting/getSelectMpPackages.do",
            success:function (res) {
                let arr = res['data'] || [],str="<option value=''>请选择</option>";
                if(arr && arr.length>0){
                    for(let item of arr){
                        str += '<option value="'+item.id+'">'+item.name+'</option>'
                    }
                }
                resolve(str)
            }
        })
    })
    return P_productData
}

// created : hxz 2020-10-12 获取模板详情
function getProductDetail( productId) {
    $.ajax({
        url: "../thali/getMpPackagesInfo.do",
        data: { packagesId: productId },
        success:function(res){
            let data = res.data
            let mpPackages = data.mpPackages // 产品详情
            let mpTmpl = data.mpTmpl // 模板详情
            let mainModuleList = data.mainModuleList || [] // 主模块列表
            let increaseModuleList = data.increaseModuleList || [] // 增值模块列表
            let reNameList = data.reNameList || [] // 重命名的菜单列表
            $("#seeProduct .pdName").html(mpPackages.name);
            $("#seeProduct .pdTime").html(mpPackages.createName + ' ' + moment(mpPackages.createDate).format('YYYY-MM-DD HH:mm:ss'));
            $("#seeProduct .pdModel").html(mpTmpl.name);
            $("#seeProduct .renameNumber").html(reNameList.length)
            renderRename('see_rename', reNameList)
            renderModuleList('see_mainModule', mainModuleList)
            renderModuleList('see_valueAddedModule', increaseModuleList)
            let increaseIdArr = increaseModuleList.map(item => item.id)
            $("#seeUsableSetMenu").data('add', increaseIdArr)
            bounce_Fixed2.show($("#seeProduct"));
        }
    })
}

// creator: 张旭博，2023-08-01 11:06:47， 渲染模块表格
function renderModuleList(name, data) {
    let str = ''

    for (let item of data) {
        str += `<tr data-id="${item.id}">
                    <td class="name">${item.name}</td>
                    <td>${item.topMenu || '--'}个</td>
                    <td>
                        <span class="link-blue" onclick="seeModule($(this))">查看</span>
                    </td>
                </tr>`
    }
    $('.tbl_' + name + ' tbody').html(str)
}

// creator: 张旭博，2023-07-20 11:43:32， 套餐清单查看 - 按钮
function seeUsableSetMenuBtn() {
    bounce_Fixed3.show($("#seeUsableSetMenu"))
    let ids = $("#seeUsableSetMenu").data('add')

    $.ajax({
        url: '../thali/getMpSets.do',
        data: { ids: ids.length>0?ids.join(','):0 }
    }).then(res => {
        let data = res.data
        let str = ''
        if (data) {
            for (let item of data) {
                str += `<tr data-id="${item.id}">
                            <td class="name">${item.name}</td>
                            <td>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>
                                <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                            </td>
                        </tr>`
            }
            $("#seeUsableSetMenu tbody").html(str)
        } else {
            layer.msg('数据错误！')
        }
    })
}

// creator: 张旭博，2023-08-01 02:36:14， 查看套餐
function seeMenu(selector) {
    let data = JSON.parse(selector.parents("tr").find(".hd").html())
    let setMenuName = data.name
    let create = data.createName + ' ' + moment(data.createDate).format("YYYY-MM-DD HH:mm:ss")
    bounce_Fixed4.show($("#seeSetMenu"))
    $("#seeSetMenu .setMenuName").html(setMenuName)
    $("#seeSetMenu .create").html(create)
    $.ajax({
        url: '../thali/getModulesByMpSetId.do',
        data: { id: data.id }
    }).then(res => {
        let data = res.data
        let str = ''
        if (data) {
            for (let item of data) {
                str += `<tr data-id="${item.id}">
                            <td class="name">${item.name}</td>
                            <td>${item.topMenu || '--'}个</td>
                            <td>
                                <span class="link-blue" onclick="seeModule($(this))">查看</span>
                            </td>
                        </tr>`
            }
            $("#seeSetMenu tbody").html(str)
        } else {
            layer.msg('数据错误！')
        }
    })
}

// creator: 张旭博，2023-08-01 10:19:14， 查看模块详情
function seeModule(selector) {
    let id = selector.parents("tr").data("id")
    let moduleName = selector.parents("tr").find(".name").html()
    $("#seeModule .moduleName").html(moduleName)
    $.ajax({
        url: '../thali/getModuleInfo.do',
        data: { id: id }
    }).then(res => {
        let data = res.data
        if (data && data.length > 0) {
            // 一层模块数据 -> 树型数据，将对应的子级放在subPopdoms中
            let newData = changeDataToTree(data)
            // 将处理后的数据渲染表格
            let tbodyStr = renderModuleToTable(newData, 1, 2)
            $("#seeModule tbody").html(tbodyStr)
            bounce_Fixed5.show($("#seeModule"))
        } else {
            layer.msg('数据错误！')
        }
    })
}

function getNullPage() {
    return `<td class="ty-null" colspan="100"><img src="../../assets/images/common/nodata.svg" alt=""><div class="text">暂无数据</div></td>`
}

// created : hxz 2020-10-12 过滤数据
function filterMenuData(editTree, optionVal){
    let curTree = editTree
    for (let i in curTree) {
        let subModule = curTree[i]['subPopdoms'] || [];
        if(subModule.length > 0){
            let subAfter = filterMenuData(subModule, optionVal);
            curTree[i]['subPopdoms'] = subAfter;
            if(subAfter.length === 0){
                if(curTree[i]['selectOption'] != optionVal){
                    curTree[i]['selectOption'] = 0
                }
            }
        }else{
            if(curTree[i]['selectOption'] != optionVal){
                curTree[i]['selectOption'] = 0
            }
        }
    }
    return curTree.filter((item)=>{ return item['selectOption'] > 0 });
}
// created : hxz 2020-10-12 过滤树,去掉不是末级的不符合的条件
function filterTree(tree) {
    for (let i in tree) {
        let sonLevel = tree[i]['sonLevel'], level = tree[i]['level'], allLevel = tree[i]['allLevel'];
        let subModule = tree[i]['subPopdoms'] || [];
        if(subModule.length > 0){
            tree[i]['subPopdoms']=filterTree(subModule);
        }
    }
    return tree.filter((item)=>{ return (item['sonLevel'] >1 && item['subPopdoms'].length !== 0) || (item['sonLevel'] === 1 && item['subPopdoms'].length == 0)  });
}
// created : hxz 2020-10-12 修改菜单树
function setNewkeyVal(curTree,info,key,isBreak) {
    if(isBreak){
        return true;
    }
    for (let i in curTree) {
        let subModule = curTree[i]['subPopdoms'] || [];
        // let jsonStr = JSON.stringify({"mid": curTree[i]['mid'], "pid": curTree[i]['pid'], "level": 1, "name": curTree[i]['name']});
        if(info['mid'] == curTree[i]['mid']){
            // curTree[i]['selectOption'] = info['selectOption'];
            curTree[i][key] = info[key];
            return true;
        }else if(subModule.length > 0){
            isBreak = arguments.callee(curTree[i]['subPopdoms'],info,key);
            if(isBreak)
                return isBreak;
        }
    }
    return isBreak;
}
// created : hxz 2020-10-12 渲染查看的页面
function rendeMenuScan(list, haveCheck, isOnlyShow) {
    let str = '<div class="module_list">';
    for (let i in list) {
        let subModule = list[i]['subPopdoms'] || []
        let jsonStr = JSON.stringify({ "mid": list[i]['mid'], "pid":list[i]['pid'], "level":1, "name":list[i]['name'] }) ;

        console.log(list[i].selectState)
        var checkStr =  '<input type="checkbox" name="isLeave" id="id'+list[i].mid+'" value="'+list[i].mid+'"'+(list[i].selectState?' checked':'')+(isOnlyShow?' disabled':'')+'>' +
            '<label for="id'+list[i].mid+'"></label>'

        str +=  '<div class="module_row" level="1">'+
                '   <div class="module_item">' +
                '       <div class="ty-checkbox">' +
                (haveCheck ? checkStr : '') + list[i]['name'] +
                '       </div>' +
                '       <span class="hd">'+ jsonStr +'</span>'+
                '   </div>'
        if(subModule.length > 0){
            str += rendeMenuScan(subModule, haveCheck, isOnlyShow);
        }
        str += '</div>';
    }
    str += '</div>'
    return str;
}
// created : hxz 2020-10-12 获取模板初始列表

// creator: 张旭博，2021-09-16 10:02:38，后台返回的模板一级数据转换为层级关系
function computedTree(treeData, id, pid) {
    let arr = []
    treeData.forEach((item, index) => {
        let isParent = false
        treeData.forEach(item2 => {
            if (item[pid] === item2[id]) {
                isParent = true
                !Array.isArray(item2.subPopdoms) && (item2.subPopdoms = [])
                item2.subPopdoms.push(item)
            }
        })
        !isParent && arr.push(index)
    })
    return treeData.filter((item, index) => arr.indexOf(index) > -1)
}
// created : hxz 2020-10-12 给全部菜单添加 Level
function addallLevelInList(list,curlevel) {
    let bigReturn = curlevel ;
    for (let i in list) {
        list[i]['level'] = curlevel ; // 设置当前的级别
        let subModule = list[i]['subPopdoms'] || [];
        if(subModule.length > 0){
            let levelAfter = curlevel + 1 ;
            let returnlevel = addallLevelInList(subModule, levelAfter);
            if(bigReturn < returnlevel){
                bigReturn = returnlevel ;
            }
        }
    }
    for (let j in list) {
        list[j]['allLevel'] = bigReturn; // 设置总级别
    }
    return bigReturn
}
// created : hxz 2020-10-12 给全部菜单添加 sonLevel
function addSonLevel(list) {
    let sonBig = 1;
    for (let i in list) {
        let sonLevel = 1 ;
        let subModule = list[i]['subPopdoms'] || [];
        if(subModule.length > 0){
            let returnL = addSonLevel(subModule)
            sonLevel += returnL ;
            if(sonBig < sonLevel) sonBig = sonLevel
        }
        list[i]['sonLevel'] = sonLevel ;  // 设置下面有几级（包含自己）
    }
    return sonBig
}


/*--------------- 登录记录 -----------------*/
// creator: 李玉婷，2020-08-20 14:43:26，按月、年查询
function getCusLoginRecordList(id, type, beginTime,endTime) {
    $("#userLoginRecord tbody").html('');
    var defType = 0;
    if (type == 3) {
        var beginYear = beginTime.split("-")[0];
        var beginMonth = beginTime.split("-")[1];
        var endYear = endTime.split("-")[0];
        var endMonth = endTime.split("-")[1];
        if(beginYear !== endYear){//跨年
            defType = 3;
            $(".dateType").html("登录年份");
        }else if(beginMonth !== endMonth){//垮月
            defType = 2;
            $(".dateType").html("登录月份");
        }else{
            defType = 1;
            $(".dateType").html("登录日期");
        }
    }
    $.ajax({
        url: '../special/getOrganizationLogins.do',
        data: {
            id: id,
            type: type,
            beginDate: beginTime,
            endDate: endTime
        },
        success: function (res) {
            var data = res.data;
            var userLogList = data.statisticsList;
            var listStr = '';
            if (data) {
                var dateFormat = '';
                var createInfo = data.createName + '&nbsp;' +new Date(Number(data['createDate'])).format('yyyy-MM-dd hh:mm:ss');
                $("#sysOrgName").html(data.name);
                $("#orgCreateName").html(createInfo);
                for(var i=0;i<userLogList.length;i++){
                    if (type == 1 || (type == 3 && defType == 1)) {
                        dateFormat = new Date(userLogList[i].loginDate).format('yyyy-MM-dd');
                    } else if (type == 2 || (type == 3 && defType == 2)) {
                        dateFormat = new Date(userLogList[i].loginDate).format('yyyy-MM');
                    } else {
                        dateFormat = new Date(userLogList[i].loginDate).format('yyyy');
                    }
                    listStr +=  '<tr>' +
                        '<td>'+ dateFormat +'</td>'+
                        '<td>'+handleNull(userLogList[i].sumUser)+'</td>'+
                        '<td>'+handleNull(userLogList[i].sumDuration)+'</td>'+
                        '<td>'+handleNull(userLogList[i].sumLogin)+'</td>'+
                        '<td>'+handleNull(userLogList[i].sumPc)+'</td>'+
                        '<td>'+handleNull(userLogList[i].sumApp)+'</td>'+
                        '</tr>';
                }
                $("#userLoginRecord tbody").html(listStr);
            }
        }
    })
}
// creator: 李玉婷，2020-08-17 15:56:58，登录记录-自定义查询
function sureLoginQuery(){
    //自定义查询设置选中，其他本月等按钮设置不选中
    $("#loginQueryBtn").addClass("ty-btn-blue");
    $(".flagTab .ty-btn-groupCom .ty-btn").removeClass("ty-btn-active-blue");
    var orgId = $(".loginRecord").data("orgId");
    var beginTime = $("#queryBeginTime").val() ;
    var endTime = $("#queryEndTime").val();
    getCusLoginRecordList(orgId, 3, beginTime,endTime);
}
// creator: 李玉婷，2021-07-27 15:49:23，批量导入
function leadingShow() {
    $('.matListUpload').remove();
    $('#custormerLeading .fileFullName').html("尚未选择文件");
    bounce.show($('#custormerLeading'));
}
// creator: 李玉婷，2020-09-03 17:12:49，导入验证
function matImportOk(type) {
    if(type === 'cancel'){
        bounce.cancel()
    }else{
        if ($(".matListUpload").length <= 0) {
            $("#knowTip .knowWord").html('您需选择一个文件后才能“导入”！');
            bounce_Fixed2.show($("#knowTip"))
        } else {
            loading.open() ;
            $(".matListUpload a").click();
        }
    }
}
// creator: 李玉婷，2021-08-04 17:31:30，
function importCustomer(file) {
    $.ajax({
        "url": "../export/importCustomer.do",
        "data": {'file': file.path},
        success: function (data) {
            var status = data;
            if (status == '1' ||status == 1) {
                bounce.cancel()
                layer.msg('操作成功！');
                var key = $(".main #se0").val();
                getCustomerMes(1 , 20, key);
            } else {
                let fileUid = file.fileUid;
                let op = {"type":'fileUid', 'fileUid':fileUid}
                fileDelAjax(op);
                loading.close() ;
                $('#select_btn_1').val("");
                $('.matListUpload').remove();
                $('#custormerLeading .fileFullName').html("尚未选择文件");
                bounce_Fixed2.show($("#importantTip"));
            }
        }
    });
}
/*creator:lyt 2022/11/14 0014 下午 5:25 */
function receiveInfo(obj){
    $("#receiveInfo .checkItem i").attr("class", "fa fa-circle-o");
    $("#receiveInfo .receiveMethod").hide();
    $("#receiveInfo").data('source', obj.data("source"));
    bounce_Fixed.show($("#receiveInfo"))
    $(".areaList tbody tr:gt(0)").remove();
    $(".receiveList tbody tr:gt(0)").remove();
    if (obj.data("source") === "updateReceiveInfo"){
        $("#receiveInfo .checkItem:eq(0)").click();
    } else if (obj.data("source") === "addCustomer") {
        let info = $("#addAccount .goodsAddress").siblings(".hd").html()
        if (info !== ""){
            info = JSON.parse(info);
            let icon = Number(info.deliveryType) ;
            $("#receiveInfo .checkItem").eq(icon - 1).click();
            if (icon === 2) {
                let state = Number(info.selfState) ;
                $("#receiveInfo .selfState").data("icon", state);
                if (state === 1) {//自提状态 1是  0非
                    $("#currentState").html("开启");
                } else {
                    $("#currentState").html("未开启");
                }
                let shAddressList = info.shAddressList;
                let areaList = info.areaList;
                setShList(shAddressList);
                setShList(areaList);
            }
        } else {
            $(".areaList").hide();
            $(".receiveList").hide();
            $("#currentState").html("未开启");
            $("#receiveInfo .selfState").data("icon", 0);
        }
    }
}
/*creator:lyt 2022/11/14 0014 下午 4:46 收货信息*/
function addAddress(obj){
    let type = obj.data('type')
    let source = $("#receiveInfo").data('source')
    $("#newReceiveAddressInfo").data('type', type);
    $("#newReceiveAddressInfo").data('source', source);
    bounce_Fixed2.show($("#newReceiveAddressInfo"))
    $("#newReceiveAddressInfo input").val("");
    $("#ReceiveName")
        .val("").data('orgData',"")
        .siblings(".hd").html("") ;
    $("#newReceiveAddressInfo .bonceHead span").html('新增收货地址');
    if (type === "update"){
        let trObj = obj.parents("tr")
        let info = obj.siblings(".hd").html()
        $("#newReceiveAddressInfo .bonceHead span").html('修改收货地址');
        $("#newReceiveAddressInfo").data('trObj', trObj);
        $("#ReceiveAddress").val(trObj.find("td").eq(0).html());
        $("#ReceiveName")
            .val(trObj.find("td").eq(1).html()).data('orgData',"")
            .siblings(".hd").html(info) ;
    }
    setTimer('updateReceive');
}
// creator: 李玉婷，2022-12-08 11:12:09，编辑收货信息提交
function reciveInfoSure (){
//1-只提供服务,不提供实体货物;2-提供实体货物
    let selectFa = $("#receiveInfo").find(".fa-circle");
    if(selectFa.length > 0){
        let detail = ``;
        let data = {"deliveryType": selectFa.data("type")}
        if (selectFa.data("type") === 2) {
            detail = `向该客户提供实体货物`;
            if ($("#receiveInfo .receiveList tbody tr").length > 1 || $("#receiveInfo .areaList tbody tr").length > 1 || $("#receiveInfo .selfState").data("icon") === 1){
                let shAddressList = [], areaList = []
                $("#receiveInfo .receiveList tbody tr:gt(0)").each(function (){
                    let info = JSON.parse($(this).find(".hd").html())
                    shAddressList.push(info)
                })
                $("#receiveInfo .areaList tbody tr:gt(0)").each(function (){
                    let info = JSON.parse($(this).find(".hd").html())
                    areaList.push(info)
                })
                data.areaList = areaList;
                data.shAddressList = shAddressList;
                data.selfState = $("#receiveInfo .selfState").data("icon");
            } else {
                layer.msg("收货信息编辑后才能保存！")
                return false;
            }
        } else {
            detail = `向该客户只提供服务，不提供实体货物，不需编辑收货地址`;
        }
        let source = $("#receiveInfo").data('source');
        if (source === "addCustomer") {
            $("#addAccount .goodsAddress").html(detail).siblings(".hd").html(JSON.stringify(data))
        } else if (source === "updateReceiveInfo") { //来自客户修改页面，不向该客户提供实体货物，故未编辑收货信息的编辑
            let  id = $("#updateCustomerPanel").data("id");
            data.id = id
            let list  = [...data.shAddressList,...data.areaList];
            data.shAddressList = JSON.stringify(list);
            $.ajax({
                url: "../sales/editCustomerAddress.do",
                data: data,
                success: function (data) {
                    var status = data.success;
                    if(status == '1'){
                        indexUpdateList(cus_seeTrObj);
                    }
                }
            })
        }
        bounce_Fixed.cancel()
    } else {
        layer.msg("收货信息编辑后才能保存！")
        return false;
    }
}
//1-只提供服务,不提供实体货物;2-提供实体货物
function turnSelfState(obj){
    let icon = obj.data("icon");
    let state = 1^icon;
    obj.data("icon", state)
    if (state === 1) {
        $("#currentState").html("开启");
        layer.msg("状态已改为“开启”！")
    } else {
        $("#currentState").html("未开启");
        layer.msg("状态已改为“未开启”！")
    }
}
/*creator:lyt 2022/11/15 0015 下午 12:32 删除收货地址*/
function deleteAddressData(obj){
    obj.parents("tr").remove();
}
/*creator:lyt 2022/11/14 0014 下午 4:46 新增到货区域*/
function addArea(obj){
    let type = obj.data('type')
    let source = $("#receiveInfo").data('source')
    $("#newReceiveAreaInfo").data('type', type);
    $("#newReceiveAreaInfo").data('source', source);
    bounce_Fixed2.show($("#newReceiveAreaInfo"))
    $("#newReceiveAreaInfo input").val("");
    $("#newReceiveAreaInfo .hd").html("");
    $("#areaName").data('orgData',"");
    $("#newReceiveAreaInfo .bonceHead span").html('新增到货区域');
    if (type === "update"){
        let trObj = obj.parents("tr")
        let info = JSON.parse(obj.siblings(".hd").html());
        $("#newReceiveAreaInfo .bonceHead span").html('修改到货区域');
        $("#newReceiveAreaInfo").data('trObj', trObj);
        $("#regionCon").val(info.address);
        $("#regionCon").siblings(".hd").html(info.path);
        $("#requirements").val(info.requirements);
        $("#areaName")
            .val(trObj.find("td").eq(1).html()).data('orgData',"")
            .siblings(".hd").html(JSON.stringify(info)) ;
    }
}
/*creator:lyt 2022/11/14 0014 下午 4:54 新增到货区域确定*/
function updateAddressKeep(){
    let id = ``;
    let type = $("#updateAddressTip").data("type");
    var data = {
        //addressId: id,
        type: type
    }
    if (type === 1) {
        id = $("#newReceiveAddressInfo").data("id");
        data.addressId = id;
        setAddress(1, data);
        bounce_Fixed2.show($("#newReceiveAddressInfo"));
        setTimer('updateReceive');
    } else {
        id = $("#newReceiveAreaInfo").data("id");
        data.addressId = id;
        setAddress(3, data);
        bounce_Fixed2.show($("#newReceiveAreaInfo"));
    }
}
//creator:lyt 2022/12/26 0026 下午 6:02 客户查看-货物的交付地点与方式查看
function recivePlaceScan(){
    bounce_Fixed.show($("#receiveInfoScan"));
    let data = JSON.parse($(".reciveDescript").siblings(".hd").html());
    var html = '',shAddressList = data.shAddressList;
    let areaStr = ``;
    let area = data.areaList;
    $(".recivePlaceList").hide();
    $(".areaListSee").hide();
    for(var b in shAddressList){
        html +=
            '<tr>' +
            '    <td>' + shAddressList[b].address + '</td>' +
            '    <td>' + shAddressList[b].contact + '</td>' +
            '    <td>' + shAddressList[b].mobile + '</td>' +
            '</tr>';
    }
    for(var b in area){
        areaStr +=
            '<tr>' +
            '    <td>' + area[b].address + '</td>' +
            '    <td>' + area[b].contact + '</td>' +
            '    <td>' + area[b].mobile + '</td>' +
            '</tr>';
    }
    if (data.selfState === 1){//自提状态 1是  0非
        $(".ownPick").html("可上门自提");
    } else {
        $(".ownPick").html("不可上门自提");
    }
    if (shAddressList.length > 0){
        $(".recivePlaceList").show().children("tbody").html(html);
    }
    if (shAddressList.length > 0) {
        $(".areaListSee").show().children("tbody").html(areaStr);
    }
}

//--------------- 辅助方法 -----------------
function changeState(val){
    if( val == 0 || val == "0"){ return "停用" };
    if( val == 1 || val == "1"){ return "启用" };
    return "";
}
//处理null
function handleNull(str) {
    var result = str == null || str == undefined || str == 'null' || '' ? '--':str;
    return result;
}
//邮编正则
function is_postcode(postcode) {
    if ( postcode == "") {
        return true;
    } else {
        if (! /^[0-9][0-9]{5}$/.test(postcode)) {
            return false;
        }
    }
    return true;
}
// 现在是所有电话，座机和手机
function isTelNum(str){
    if(str == ""){
        return true;
    }else{
        var pattern = /(^((\(\d{2,3}\))|(\d{3}\-))?(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$)|(^(0|86|17951)?(13[0-9]|15[012356789]|17[678]|18[0-9]|14[57])[0-9]{8}$)/;
        if (pattern.exec(str) !== null) {
            return true;
        }
    }
    return false;
}
// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.parent().siblings(".textMax").text( curLength +'/' + max );
    obj.siblings(".textMax").text( curLength +'/' + max );
}
// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = []
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
}
function formatTime(time, type = 'time') {
    if (time) {
        if (type === 'time') {
            return moment(time).format("YYYY-MM-DD HH:mm:ss")
        } else if (type === 'date') {
            return moment(time).format("YYYY-MM-DD")
        }
    } else {
        return '--'
    }
}
laydate.render({elem: '#edit_firstTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#firstContactTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#interviewDate', format: 'yyyy年MM月dd日'});
laydate.render({elem: '#initialPeriod', type: 'month', format: 'M月',min: new Date().format('yyyy-01-01'),max: new Date().format('yyyy-12-31')});
laydate.render({elem: '#buyMonth', type: 'month', format: 'M月',min: new Date().format('yyyy-01-01'),max: new Date().format('yyyy-12-31')});
laydate.render({elem: '#queryBeginTime'});
laydate.render({elem: '#queryEndTime'});
laydate.render({elem: '#expiresTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#contractTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#update_expiresTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '#update_contractTime', format: 'yyyy/MM/dd'});
laydate.render({elem: '.cSignDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cStartDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cEndDate', format: 'yyyy-MM-dd'});