$(function () {
    var type = Number(getUrlParam("type"))
    $(".tips").html("")
    if (type === 11 || type === 12) {
        // 试用详情
        getTrialInfo()
        $("#mainProductInfo").show().siblings().hide()
    } else if (type === 2) {

    } else if (type === 31 || type === 32) {
        // 增值服务详情
        getAddServiceInfo()
        $("#addServiceInfo").show().siblings().hide()
    } else if (type === 41 || type === 42) {
        // 增值服务修改详情
        getAddServiceChangeInfo()
        $("#addServiceChangeInfo").show().siblings().hide()
    } else if (type === 51 || type === 52) {
        $(".tips").html("此机构被提出了暂停服务的申请")
        // 暂停服务修改详情
        getOrgOutOfServiceInfo()
        $("#addServiceInfo").show().siblings().hide()
    } else if (type === 61 || type === 62) {
        $(".tips").html("此机构被提出了恢复服务的申请")
        // 恢复服务修改详情
        getOrgRestoreServiceInfo()
        $("#addServiceInfo").show().siblings().hide()
    }

    $(".cusProId").html('')
    $(".main").on ("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'agree':
                if (type === 12) {
                    bounce.show($("#agreeTip"))
                    getDomainList()
                    $("#agreeTip").find("input").val("")
                } else if (type === 32) {
                    bounce.show($("#bounce_tip"))
                    $("#bounce_tip .text-center").html("确定批准为该机构所选的增值服务吗？")
                    $("#bounce_tip .sureBtn").one("click", function () {
                        approveAddService(1)
                    })
                } else if (type === 42) {
                    bounce.show($("#bounce_tip"))
                    $("#bounce_tip .text-center").html("确定批准该机构增值服务的修改申请吗？")
                    $("#bounce_tip .sureBtn").one("click", function () {
                        approveAddServiceChange(1)
                    })
                } else if (type === 52) {
                    bounce.show($("#bounce_tip"))
                    $("#bounce_tip .text-center").html("确定批准该机构的暂停服务申请吗？")
                    $("#bounce_tip .sureBtn").one("click", function () {
                        approvalOrgOutOfServiceApply(1)
                    })
                } else if (type === 62) {
                    bounce.show($("#bounce_tip"))
                    $("#bounce_tip .text-center").html("确定批准该机构的恢复服务申请吗？")
                    $("#bounce_tip .sureBtn").one("click", function () {
                        approvalOrgRestoreServiceApply(1)
                    })
                }

                break;
            case 'reject':
                bounce.show($("#rejectTip"))
                $("#rejectTip").find(".textMax").html(`0/50`)
                $("#rejectTip").find("textarea").val('')
                $("#rejectTip .sureBtn").one("click", function () {
                    if (type === 12) {
                        approveTrial(2)
                    } else if (type === 32) {
                        approveAddService(0)
                    } else if (type === 42) {
                        approveAddServiceChange(2)
                    } else if (type === 52) {
                        approvalOrgOutOfServiceApply(2)
                    } else if (type === 62) {
                        approvalOrgRestoreServiceApply(2)
                    }
                })
                break;
        }
    })

})

// creator: 张旭博，2020-04-17 09:34:05, 获取试用机构详情
function getTrialInfo() {
    var id = getUrlParam("id")
    $.ajax({
        url: '/special/getCustomerOrganizationHistoryInfo.do',
        data: {
            id: id
        },
        success: function (res) {
            var data = res.data
            var organizationInfo = data.organizationInfo
            var processList = data.approvalProcessList

            $(".cusProId").html(organizationInfo.gTenantId)
            // 使消息消失
            var mid = GetUrlQuery("mid")
            if (mid) {
                sphdSocket.send('updateUserSuspendMsg', {
                    userId: sphdSocket.user.userID,
                    messageId: mid
                })
            }

            // 处理详情
            if (organizationInfo) {
                $("#trialInfo .trial_fullName").html(organizationInfo.fullName)
                $("#trialInfo .trial_name").html(organizationInfo.name)
                $("#trialInfo .trial_supervisorMobile").html(organizationInfo.supervisorMobile)
                $("#trialInfo .trial_supervisorName").html(organizationInfo.supervisorName)
                $("#trialInfo .trial_uploadStorageType").html(organizationInfo.uploadStorageType)
                $("#trialInfo .trial_packageName").html(JSON.parse(organizationInfo.packageObject).mpPackages.name)
            }

            // 处理审批流程
            var str = '<div>申请人：' + organizationInfo.createName + ' ' + formatTime(organizationInfo.createDate, true) + '</div>'
            for (var i in processList) {
                if (processList[i].approveStatus === '2') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'
                }
                if (processList[i].approveStatus === '3') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'+
                            '<div>驳回理由：' + processList[i].reason + '</div>'
                } else {
                    if(getUrlParam("type") !== '12' && processList[0].approveStatus === '1') {
                        str +=  '<div>等待' + processList[i].askName + '审批</div>'
                    }
                }
            }
            $(".processList").html(str)
            if (processList[0].approveStatus === '1' && sphdSocket.user.oid === 0 && getUrlParam("type") === '12') {
                $(".approveBtn").show()
            } else {
                $(".approveBtn").hide()
            }

            // 处理产品
            let productData = JSON.parse(organizationInfo.packageObject)
            let mpPackages = productData.mpPackages // 产品详情
            let mpTmpl = productData.mpTmpl // 模板详情
            let mainModuleList = productData.mainModuleList || [] // 主模块列表
            let increaseModuleList = productData.increaseModuleList || [] // 增值模块列表
            let reNameList = productData.reNameList || [] // 重命名的菜单列表
            $("#mainProductInfo .pdName").html(mpPackages.name);
            $("#mainProductInfo .pdTime").html(mpPackages.createName + ' ' + moment(mpPackages.createDate).format('YYYY-MM-DD HH:mm:ss'));
            $("#mainProductInfo .pdModel").html(mpTmpl.name);
            $("#mainProductInfo .renameNumber").html(reNameList.length)
            renderRename('see_rename', reNameList)
            renderModuleList('see_mainModule', mainModuleList)
            renderModuleList('see_valueAddedModule', increaseModuleList)
            let increaseIdArr = increaseModuleList.map(item => item.id)
            $("#seeUsableSetMenu").data('add', increaseIdArr)
        }
    })
}

function getAddServiceInfo() {
    $.ajax({
        url: '../thali/getIncrementInfo.do',
        data: {
            id: getUrlParam("id")
        },
        success: function (res) {
            var data = res.data
            var organizationInfo = data.organizationInfo // 包含机构的所有信息
            var processList = data.approvalProcessList
            $(".cusProId").html(organizationInfo.gTenantId)

            var packageInfo = JSON.parse(organizationInfo.packageObject).mpPackages

            var mid = GetUrlQuery("mid")
            if (mid) {
                sphdSocket.send('updateUserSuspendMsg', {
                    userId: sphdSocket.user.userID,
                    messageId: mid
                })
            }

            // 处理详情
            if (organizationInfo) {
                $("#trialInfo .trial_fullName").html(organizationInfo.fullName)
                $("#trialInfo .trial_name").html(organizationInfo.name)
                $("#trialInfo .trial_supervisorMobile").html(organizationInfo.supervisorMobile)
                $("#trialInfo .trial_supervisorName").html(organizationInfo.supervisorName)
                $("#trialInfo .trial_uploadStorageType").html(organizationInfo.uploadStorageType)
                var productStr = `${packageInfo.name}<span class="link-blue" onclick="seeProductInfo(${packageInfo.id})">详情</span>`
                $("#trialInfo .trial_packageName").html(productStr)
            }

            // 处理审批流程
            var str = '<div>申请人：' + organizationInfo.createName + ' ' + formatTime(organizationInfo.createDate, true) + '</div>'
            for (var i in processList) {
                if (processList[i].approveStatus === '2') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'
                }
                if (processList[i].approveStatus === '3') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'+
                        '<div>驳回理由：' + processList[i].reason + '</div>'
                } else {
                    if(getUrlParam("type") !== '32' && processList[0].approveStatus === '1') {
                        str +=  '<div>等待' + processList[i].askName + '审批</div>'
                    }
                }
            }
            $(".processList").html(str)

            // 判断是否显示审批按钮
            if (processList[0].approveStatus === '1' && sphdSocket.user.oid === 0 && getUrlParam("type") === '32') {
                $(".approveBtn").show()
            } else {
                $(".approveBtn").hide()
            }

            // 处理增值服务
            // setServiceInfo($("#addServiceInfo .module_body"), organizationInfo)
            let mpSetInfo = data.mpSetInfo
            let incrementModules = data.incrementModules
            let type = data.type
            if (type === 1) {
                $("#addServiceInfo .tbl_asModule").show().siblings().hide()
                let moduleStr = ''
                for (let item of incrementModules) {
                    moduleStr += `<tr data-id="${item.id}">
                                        <td class="name">${item.name}</td>
                                        <td>${item.topMenu || '--'}个</td>
                                        <td>
                                            <span class="link-blue" onclick="seeModule($(this))">查看</span>
                                        </td>
                                    </tr>`
                                }
                $('#addServiceInfo .tbl_asModule tbody').html(incrementModules.length > 0?moduleStr:getNullPage())
            } else {
                let mpSetStr = `<tr data-id="${mpSetInfo.id}">
                                    <td class="name">${mpSetInfo.name}</td>
                                    <td>
                                        <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                                        <span class="hd">${JSON.stringify(mpSetInfo)}</span>
                                    </td>
                                </tr>`
                $('#addServiceInfo .tbl_asMpSet tbody').html(mpSetStr)
                $("#addServiceInfo .tbl_asMpSet").show().siblings().hide()
            }
        }
    })
}

// creator: 张旭博，2021-09-17 10:01:09，查看产品详情 - 按钮
function seeProductInfo(productId) {
    getProductDetail(productId)
    bounce_Fixed2.show($("#seeProduct"));
}

// creator: 张旭博，2021-09-09 14:46:51，获取增值服务的修改详情
function getAddServiceChangeInfo() {
    $.ajax({
        url: '../thali/getIncrementEditInfo.do',
        data: {
            id: getUrlParam("id")
        },
        success: function (res) {
            var data = res.data
            var organizationInfo = data.oldOrganizationInfo // 包含机构的所有信息
            var oldOrganizationInfo = data.oldOrganizationInfo // 包含机构的所有信息
            var newOrganizationInfo = data.newOrganizationInfo // 包含机构的所有信息
            var processList = data.approvalProcessList

            $(".cusProId").html(organizationInfo.gTenantId)

            var mid = GetUrlQuery("mid")
            if (mid) {
                sphdSocket.send('updateUserSuspendMsg', {
                    userId: sphdSocket.user.userID,
                    messageId: mid
                })
            }

            // 处理详情
            if (organizationInfo) {
                $("#trialInfo .trial_fullName").html(organizationInfo.fullName)
                $("#trialInfo .trial_name").html(organizationInfo.name)
                $("#trialInfo .trial_supervisorMobile").html(organizationInfo.supervisorMobile)
                $("#trialInfo .trial_supervisorName").html(organizationInfo.supervisorName)
                $("#trialInfo .trial_uploadStorageType").html(organizationInfo.uploadStorageType)
                $("#trialInfo .trial_packageName").html(JSON.parse(organizationInfo.packageObject).mpPackages.name)
            }

            // 处理审批流程
            var str = '<div>申请人：' + organizationInfo.createName + ' ' + formatTime(organizationInfo.createDate, true) + '</div>'
            for (var i in processList) {
                if (processList[i].approveStatus === '2') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'
                }
                if (processList[i].approveStatus === '3') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'+
                        '<div>驳回理由：' + processList[i].reason + '</div>'
                } else {
                    if(getUrlParam("type") !== '42'  && processList[0].approveStatus === '1') {
                        str +=  '<div>等待' + processList[i].askName + '审批</div>'
                    }
                }
            }
            $(".processList").html(str)

            // 判断是否显示审批按钮
            if (processList[0].approveStatus === '1' && sphdSocket.user.oid === 0 && getUrlParam("type") === '42') {
                $(".approveBtn").show()
            } else {
                $(".approveBtn").hide()
            }

            // 处理增值服务
            let newIncrementModules = data.newIncrementModules
            let newMpSetInfo = data.newMpSetInfo
            let oldIncrementModules = data.oldIncrementModules
            let oldMpSetInfo = data.oldMpSetInfo
            let newType = data.newType
            let oldType = data.oldType
            if (oldType === 1) {
                let oldModuleStr = ''
                for (let item of oldIncrementModules) {
                    oldModuleStr += `<tr data-id="${item.id}">
                                        <td class="name">${item.name}</td>
                                        <td>${item.topMenu || '--'}个</td>
                                        <td>
                                            <span class="link-blue" onclick="seeModule($(this))">查看</span>
                                        </td>
                                    </tr>`
                }
                $('#addServiceChangeInfo .compare_old .tbl_asModule tbody').html(oldIncrementModules.length > 0?oldModuleStr:getNullPage())
                $("#addServiceChangeInfo .compare_old .tbl_asModule").show().siblings().hide()
            } else {
                let oldMpSetStr = `<tr data-id="${oldMpSetInfo.id}">
                                    <td class="name">${oldMpSetInfo.name}</td>
                                    <td>
                                        <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                                        <span class="hd">${JSON.stringify(oldMpSetInfo)}</span>
                                    </td>
                                </tr>`
                $('#addServiceChangeInfo .compare_old .tbl_asMpSet tbody').html(oldMpSetStr)
                $("#addServiceChangeInfo .compare_old .tbl_asMpSet").show().siblings().hide()
            }
            if (newType === 1) {
                let newModuleStr = ''
                for (let item of newIncrementModules) {
                    newModuleStr += `<tr data-id="${item.id}">
                                        <td class="name">${item.name}</td>
                                        <td>${item.topMenu || '--'}个</td>
                                        <td>
                                            <span class="link-blue" onclick="seeModule($(this))">查看</span>
                                        </td>
                                    </tr>`
                }
                $('#addServiceChangeInfo .compare_new .tbl_asModule tbody').html(newIncrementModules.length > 0?newModuleStr:getNullPage())
                $("#addServiceChangeInfo .compare_new .tbl_asModule").show().siblings().hide()
            } else {
                let newMpSetStr = `<tr data-id="${newMpSetInfo.id}">
                                    <td class="name">${newMpSetInfo.name}</td>
                                    <td>
                                        <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                                        <span class="hd">${JSON.stringify(newMpSetInfo)}</span>
                                    </td>
                                </tr>`
                $('#addServiceChangeInfo .compare_new .tbl_asMpSet tbody').html(newMpSetStr)
                $("#addServiceChangeInfo .compare_new .tbl_asMpSet").show().siblings().hide()
            }
        }
    })
}

// creator: 张旭博，2022/3/4 16:00，获取暂停服务的申请详情
function getOrgOutOfServiceInfo() {
    $.ajax({
        url: '../thali/getOutOfServiceInfo.do',
        data: {
            id: getUrlParam("id")
        },
        success: function (res) {
            var data = res.data
            var organizationInfo = data.organizationInfo // 包含机构的所有信息
            var processList = data.approvalProcessList
            $(".cusProId").html(organizationInfo.gTenantId)

            var packageObject = organizationInfo.packageObject
            var packageInfo = {}
            if (packageObject) { packageInfo = JSON.parse(organizationInfo.packageObject).mpPackages }



            var mid = GetUrlQuery("mid")
            if (mid) {
                sphdSocket.send('updateUserSuspendMsg', {
                    userId: sphdSocket.user.userID,
                    messageId: mid
                })
            }

            // 处理详情
            if (organizationInfo) {
                $("#trialInfo .trial_fullName").html(organizationInfo.fullName)
                $("#trialInfo .trial_name").html(organizationInfo.name)
                $("#trialInfo .trial_supervisorMobile").html(organizationInfo.supervisorMobile)
                $("#trialInfo .trial_supervisorName").html(organizationInfo.supervisorName)
                $("#trialInfo .trial_uploadStorageType").html(organizationInfo.uploadStorageType)
                $("#trialInfo .trial_packageName").html(packageInfo.name || '--')
                if (organizationInfo.packageId) {
                    var productStr = `${packageInfo.name}<span class="link-blue" onclick="seeProductInfo(${packageInfo.id})">详情</span>`
                    $("#trialInfo .trial_packageName").html(productStr)
                }
            }

            // 处理审批流程
            var str = '<div>申请人：' + organizationInfo.createName + ' ' + formatTime(organizationInfo.createDate, true) + '</div>'
            for (var i in processList) {
                if (processList[i].approveStatus === '2') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'
                }
                if (processList[i].approveStatus === '3') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'+
                        '<div>驳回理由：' + processList[i].reason + '</div>'
                } else {
                    if(getUrlParam("type") !== '52'  && processList[0].approveStatus === '1') {
                        str +=  '<div>等待' + processList[i].askName + '审批</div>'
                    }
                }
            }
            $(".processList").html(str)

            // 判断是否显示审批按钮
            if (processList[0].approveStatus === '1' && sphdSocket.user.oid === 0 && getUrlParam("type") === '52') {
                $(".approveBtn").show()
            } else {
                $(".approveBtn").hide()
            }

            // 处理增值服务
            if (organizationInfo.appObject) {
                let mpSetInfo = data.mpSetInfo
                let incrementModules = data.incrementModules
                let type = data.type
                if (type === 1) {
                    $("#addServiceInfo .tbl_asModule").show().siblings().hide()
                    let moduleStr = ''
                    for (let item of incrementModules) {
                        moduleStr += `<tr data-id="${item.id}">
                                        <td class="name">${item.name}</td>
                                        <td>${item.topMenu || '--'}个</td>
                                        <td>
                                            <span class="link-blue" onclick="seeModule($(this))">查看</span>
                                        </td>
                                    </tr>`
                    }
                    $('#addServiceInfo .tbl_asModule tbody').html(incrementModules.length > 0?moduleStr:getNullPage())
                } else {
                    let mpSetStr = `<tr data-id="${mpSetInfo.id}">
                                    <td class="name">${mpSetInfo.name}</td>
                                    <td>
                                        <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                                        <span class="hd">${JSON.stringify(mpSetInfo)}</span>
                                    </td>
                                </tr>`
                    $('#addServiceInfo .tbl_asMpSet tbody').html(mpSetStr)
                    $("#addServiceInfo .tbl_asMpSet").show().siblings().hide()
                }
            } else {
                $("#addServiceInfo .module_body").html("")
                $("#addServiceInfo").hide()
            }
        }
    })
}

// creator: 张旭博，2022/3/7 10:42，获取恢复服务的申请详情
function getOrgRestoreServiceInfo() {
    $.ajax({
        url: '../thali/getRestoreServiceInfo.do',
        data: {
            id: getUrlParam("id")
        },
        success: function (res) {
            var data = res.data
            var organizationInfo = data.organizationInfo // 包含机构的所有信息
            var processList = data.approvalProcessList
            $(".cusProId").html(organizationInfo.gTenantId)

            var packageObject = organizationInfo.packageObject
            var packageInfo = {}
            if (packageObject) { packageInfo = JSON.parse(organizationInfo.packageObject).mpPackages }

            var mid = GetUrlQuery("mid")
            if (mid) {
                sphdSocket.send('updateUserSuspendMsg', {
                    userId: sphdSocket.user.userID,
                    messageId: mid
                })
            }

            // 处理详情
            if (organizationInfo) {
                $("#trialInfo .trial_fullName").html(organizationInfo.fullName)
                $("#trialInfo .trial_name").html(organizationInfo.name)
                $("#trialInfo .trial_supervisorMobile").html(organizationInfo.supervisorMobile)
                $("#trialInfo .trial_supervisorName").html(organizationInfo.supervisorName)
                $("#trialInfo .trial_uploadStorageType").html(organizationInfo.uploadStorageType)
                $("#trialInfo .trial_packageName").html(packageInfo.name || '--')
                if (organizationInfo.packageId) {
                    var productStr = `${packageInfo.name}<span class="link-blue" onclick="seeProductInfo(${packageInfo.id})">详情</span>`
                    $("#trialInfo .trial_packageName").html(productStr)
                } else {
                    $("#trialInfo .trial_packageName").html('--')
                }
            }

            // 处理审批流程
            var str = '<div>申请人：' + organizationInfo.createName + ' ' + formatTime(organizationInfo.createDate, true) + '</div>'
            for (var i in processList) {
                if (processList[i].approveStatus === '2') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'
                }
                if (processList[i].approveStatus === '3') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'+
                        '<div>驳回理由：' + processList[i].reason + '</div>'
                } else {
                    if(getUrlParam("type") !== '62'  && processList[0].approveStatus === '1') {
                        str +=  '<div>等待' + processList[i].askName + '审批</div>'
                    }
                }
            }
            $(".processList").html(str)

            // 判断是否显示审批按钮
            if (processList[0].approveStatus === '1' && sphdSocket.user.oid === 0 && getUrlParam("type") === '62') {
                $(".approveBtn").show()
            } else {
                $(".approveBtn").hide()
            }

            // 处理增值服务
            if (organizationInfo.appObject) {
                let mpSetInfo = data.mpSetInfo
                let incrementModules = data.incrementModules
                let type = data.type
                if (type === 1) {
                    $("#addServiceInfo .tbl_asModule").show().siblings().hide()
                    let moduleStr = ''
                    for (let item of incrementModules) {
                        moduleStr += `<tr data-id="${item.id}">
                                        <td class="name">${item.name}</td>
                                        <td>${item.topMenu || '--'}个</td>
                                        <td>
                                            <span class="link-blue" onclick="seeModule($(this))">查看</span>
                                        </td>
                                    </tr>`
                    }
                    $('#addServiceInfo .tbl_asModule tbody').html(incrementModules.length > 0?moduleStr:getNullPage())
                } else {
                    let mpSetStr = `<tr data-id="${mpSetInfo.id}">
                                    <td class="name">${mpSetInfo.name}</td>
                                    <td>
                                        <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                                        <span class="hd">${JSON.stringify(mpSetInfo)}</span>
                                    </td>
                                </tr>`
                    $('#addServiceInfo .tbl_asMpSet tbody').html(mpSetStr)
                    $("#addServiceInfo .tbl_asMpSet").show().siblings().hide()
                }
            } else {
                $("#addServiceInfo .module_body").html("")
                $("#addServiceInfo").hide()
            }
        }
    })
}

function setServiceInfo(obj, organizationInfo) {
    var appObject = organizationInfo.appObject.split(",") // 增值服务勾选过的模块
    var packageObject = JSON.parse(organizationInfo.packageObject) // 增值服务所属产品信息
    var mpModuleOptionList = packageObject.mpModuleOptionList
    mpModuleOptionList.forEach(item => {
        item.selectState = appObject.findIndex((value)=>value==item.mid) > 0
    })

    // 获取所有模块
    let curTree = computedTree(mpModuleOptionList, 'mid', 'pid')
    // 处理为修改后只有增值模块的树
    for(let item of mpModuleOptionList){
        setNewkeyVal(curTree,item,'selectOption');
        setNewkeyVal(curTree,item,'selectState');
    }

    // 赋值3种类型的模块列表
    let editTree = JSON.parse(JSON.stringify(curTree)) ;
    let tree = filterMenuData(editTree,1);
    let teeStr = '<div class="nullStr">暂无数据</div>';
    if (tree.length > 0) {
        teeStr = rendeMenuScan(tree, true, true);
    }
    obj.html(teeStr);
}

// creator: 张旭博，2020-04-17 11:06:19,审批试用
function approveTrial(approvalStatus) {
    var id = getUrlParam("id")
    var data = {
        id: id,
        userId: sphdSocket.user.userID,
        approvalStatus: approvalStatus
    }
    var tyDomain = $("#agreeTip select[name='tyDomain']").val()
    if (approvalStatus === 1 && (!tyDomain || tyDomain === '')) {
        layer.msg("请选择电脑端登陆网址")
        return false
    } else {
        data.tyDomain = tyDomain
    }
    if (approvalStatus === 2) {
        data.reason = $(".reason").val()
    }
    $.ajax({
        url: '/special/approvalTryApply.do',//id要处理数据id，userId 登录人id,approvalStatus 审批 1-批准，否则认为驳回, reason 驳回理由（驳回时用）
        data: data,
        success: function (res) {
            var data = res.data
            if (data === 1) {
                layer.msg("审批成功！")
                location.reload()
            } else {
                $("#tip .tipCon").html("操作失败！")
            }
        }
    })
}

// creator: 张旭博，2021-09-18 10:14:43，审批增值服务申请
function approveAddService(approveStatus) {
    var id = getUrlParam("id")
    var data = {
        id: id,
        approveStatus: approveStatus
    }
    if (approveStatus === 0) {
        data.reason = $(".reason").val()
    }
    $.ajax({
        url: '../thali/approvalIncrementApply.do',
        data: data,
        success: function (res) {
            var data = res.data
            if (data === 1) {
                layer.msg("审批成功！")
                location.reload()
            } else if (data === 2) {
                layer.msg("由于机构产品已经更换 本次增值服务申请只能驳回处理！")
                bounce.cancel()
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}

// creator: 张旭博，2021-09-18 10:14:43，审批增值服务的修改申请
function approveAddServiceChange(approveStatus) {
    var id = getUrlParam("id")
    var data = {
        id: id,
        approveStatus: approveStatus
    }
    if (approveStatus === 2) {
        data.reason = $(".reason").val()
    }
    $.ajax({
        url: '../thali/approvalIncrementEditApply.do',
        data: data,
        success: function (res) {
            var data = res.data
            if (data === 1) {
                layer.msg("审批成功！")
                location.reload()
            } else if (data === 2) {
                layer.msg("由于机构产品已经更换 本次增值服务修改申请只能驳回处理！")
                bounce.cancel()
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}

// creator: 张旭博，2022/3/4 16:48，审批暂停服务的申请
function approvalOrgOutOfServiceApply(approvalStatus) {
    var id = getUrlParam("id")
    var data = {
        id: id,
        approvalStatus: approvalStatus
    }
    if (approvalStatus === 2) {
        data.reason = $(".reason").val()
    }
    $.ajax({
        url: '../special/approvalOrgOutOfServiceApply.do',
        data: data,
        success: function (res) {
            var data = res.data
            if (data === 1) {
                layer.msg("审批成功！")
                location.reload()
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}

// creator: 张旭博，2022/3/4 16:48，审批恢复服务的申请
function approvalOrgRestoreServiceApply(approvalStatus) {
    var id = getUrlParam("id")
    var data = {
        id: id,
        approvalStatus: approvalStatus
    }
    if (approvalStatus === 2) {
        data.reason = $(".reason").val()
    }
    $.ajax({
        url: '../special/approvalOrgRestoreServiceApply.do',
        data: data,
        success: function (res) {
            var data = res.data
            if (data === 1) {
                layer.msg("审批成功！")
                location.reload()
            } else {
                layer.msg("操作失败！")
            }
        }
    })
}

// creator: 张旭博，2021-05-21 17:33:44，返回 域名列表
function getDomainList() {
    $.ajax({
        url: '/special/getDomainList.do',
        success: function (res) {
            var data = res.data
            var str = '<option value="">请选择</option>'
            for (var i in data) {
                str +=  '<option value="'+data[i]+'">' + data[i] + '</option>';
            }
            $("#tyDomain").html(str)
        }

    })
}
// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.siblings(".textMax").text(curLength +'/' + max );
}

function sale_seebtn(obj){
    bounce.show($("#seeAccount"));
    var id = obj.siblings(".hd").children(".cusProId").html();
    if ( id == undefined || id == ""){
        $("#mtTip .tip").html("系统错误，刷新重试！");
        bounce_Fixed.show($("#mtTip"));
        return false;
    }
    $("#seeAccount").data("id", id);
    $.ajax({
        url : "../sales/getPdCustomerByHandle.do" ,
        data : { "id":id },
        success:function(data){
            var tr_obj = obj.parent().parent();
            cus_seeTrObj = tr_obj;
            var pdCustomer = data["data"];
            var cuscoding = pdCustomer["code"];
            var invoiceRequire = pdCustomer["invoiceRequire"];
            var cusname = pdCustomer["name"];
            var cusfullName = pdCustomer["fullName"];
            var firstBuyTime = pdCustomer["firstBuyTime"];
            var address = pdCustomer["address"];
            var invoiceName = pdCustomer["invoiceName"];
            var invoiceAddress = pdCustomer["invoiceAddress"];
            var phone = pdCustomer["telephone"];
            var bank = pdCustomer["bankName"];
            var accountnum = pdCustomer["bank_no"];
            var taxpayerID = pdCustomer["taxpayerID"];
            var firstContactAddress = pdCustomer["firstContactAddress"];
            var infoSource = pdCustomer["infoSource"];
            var memo = pdCustomer["memo"];
            var createName = pdCustomer["createName"];
            var supervisorMobile = pdCustomer["supervisorMobile"];
            var supervisorName = pdCustomer["supervisorName"];
            var firstContactTime = new Date(pdCustomer["firstContactTime"]).format('yyyy-MM-dd');
            var createDate = new Date(pdCustomer["createDate"]).format('yyyy-MM-dd hh:mm:ss');
            var qImages = pdCustomer["qImages"];
            var pImages = pdCustomer["pImages"];
            var contactList = pdCustomer["contactsList"];
            var contactBaseList = pdCustomer["contactBaseList"] || [] ;
            var shAddressList = pdCustomer["shAddressList"];
            var fpAddressList = pdCustomer["fpAddressList"];
            var initialPeriod = pdCustomer["initialPeriod"];
            var date = '';
            $("#see_cuscoding").html(cuscoding);
            $("#see_cusname").html( cusfullName);
            $("#see_cusfullName").html(cusname);
            $("#firstBuyTime").html(firstBuyTime);
            $("#see_address").html(address);
            $("#see_phone").html(phone);
            $("#see_bank").html(bank);
            $("#see_invoiceRequire").html( formatInviceRequire(invoiceRequire));
            $("#see_bankNo").html(accountnum);
            $("#taxpayerID").html(taxpayerID);
            $("#see_firstContactTime").html(firstContactTime);
            $("#firstContactAddress").html(firstContactAddress);
            $("#see_createName").html(createName);
            $("#see_createDate").html(createDate);
            $("#see_supervisorName").html(supervisorName);
            $("#see_supervisorMobile").html(supervisorMobile);
            $("#infoSource").html(infoSource);
            $("#see_memo").html(memo);
            $("#see_invoiceName").html(invoiceName);
            $("#see_invoiceAddress").html(invoiceAddress);
            $("#telephone").html(invoiceAddress);
            if(qImages.length > 0){
                var imgStr = '';
                for(var a=0;a<qImages.length;a++){
                    var path = qImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">' +
                        '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                        '</div>';
                }
                $("#overallImgUpload").html(imgStr);
            }else{
                $("#overallImgUpload").html("");
            }
            if(pImages.length > 0){
                var imgStr = '';
                for(var a=0;a<pImages.length;a++){
                    var path = pImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">' +
                        '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
                        '</div>';
                }
                $("#productImgUpload").html(imgStr);
            }else{
                $("#productImgUpload").html('');
            }
            // 处理是否购买过本公司的产品
            var hasBuyStr = ''
            var initialType = pdCustomer.initialType;
            if (initialType === null) {
                hasBuyStr = '<div class="ty-alert ty-alert-info">'+createDate + '之前该客户尚未购买过本公司的商品</div>'
            } else {
                if(initialType === '1'){
                    var month = initialPeriod.substr(4,2);
                    if(month.slice(0,1) == '0'){
                        month = month.substr(1,1);
                    }
                    date = initialPeriod.substr(0,4) + '年'  + month  + '月';
                    hasBuyStr = '<div class="ty-alert ty-alert-info">'+date+'该客户首次购买过本公司的商品</div>'
                }else if(initialType === '2'){
                    hasBuyStr = '<div class="ty-alert ty-alert-info">去年该客户首次购买过本公司的商品</div>'
                }else{
                    hasBuyStr = '<div class="ty-alert ty-alert-info">去年之前该客户首次购买过本公司的商品</div>'
                }

            }
            $("#seeAccount .firstBuyTime").html(hasBuyStr)

            $(".see_contactList").html('');
            $(".recivePlaceList tbody").html('');
            $(".mailPlaceList tbody").html('');
            $(".contactNum").html(contactList.length);
            var htmlContract =  ''
            contactBaseList.forEach(function (cIm) {
                htmlContract += `
                <tr data-id="${ cIm.id }" class="contractItem">
                    <td>${ cIm.sn }</td>
                    <td>${ new Date(cIm.signTime).format("yyyy-MM-dd")}</td>
                    <td>${new Date(cIm.validStart).format("yyyy-MM-dd")}  至  ${new Date(cIm.validEnd).format("yyyy-MM-dd")} </td>
                    <td> <span class="linkBtn node" data-name="scanGs">${ cIm.num }</span>种</td>
                </tr>
                `
            })
            $(".contractPlaceList tbody").html(htmlContract);

            if(contactList.length > 0){
                var rhtml = '';
                var html = '<div class="leftList"><table class="ty-table ty-table-control">' +
                    '<thead>' +
                    '<tr>' +
                    '    <td>姓名</td>' +
                    '    <td>职位</td>' +
                    '</tr>' +
                    '</thead><tbody>';
                if(contactList.length >= 2){rhtml = html;}
                for(var b in contactList){
                    var slice = b % 2;
                    if(slice > 0){
                        rhtml +=
                            '<tr class="'+(contactList[b].enabled == 1?'':'disable')+'" data-id="' + contactList[b].id + '">' +
                            '    <td>' + contactList[b].name + '</td>' +
                            '    <td>' + contactList[b].post + '</td>' +
                            '</tr>';
                    }else{
                        html +=
                            '<tr class="'+(contactList[b].enabled == 1?'':'disable')+'" data-id="' + contactList[b].id + '">' +
                            '    <td>' + contactList[b].name + '</td>' +
                            '    <td>' + contactList[b].post + '</td>' +
                            '</tr>';
                    }
                }
                html += '</tbody></table></div>';
                if(contactList.length >= 2){rhtml += '</tbody></table></div>';}
                var result = html + rhtml;
                $(".see_contactList").html(result);
            }
            if(shAddressList.length > 0){
                var html = '';
                for(var b in shAddressList){
                    var num = 1 + Number(b);
                    html +=
                        '<tr class="'+(shAddressList[b].enabled == 1?'':'disable')+'" data-id="' + shAddressList[b].id + '">' +
                        '    <td>' + num + '</td>' +
                        '    <td>' + shAddressList[b].address + '</td>' +
                        '    <td>' + shAddressList[b].contact + '</td>' +
                        '    <td>' + shAddressList[b].mobile + '</td>' +
                        '</tr>';
                }
                $(".recivePlaceList tbody").html(html);
            }
            if(fpAddressList.length > 0){
                var html = '';
                for(var b in fpAddressList){
                    var num = 1 + Number(b);
                    html +=
                        '<tr class="'+(fpAddressList[b].enabled == 1?'':'disable')+'" data-id="' + fpAddressList[b].id + '">' +
                        '    <td>' + num + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].address) + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].contact) + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].postcode) + '</td>' +
                        '</tr>';
                }
                $(".mailPlaceList tbody").html(html);
            }
        }
    })
}