var user = null;
// creator: hxz  2018-07-18  初始化 三级弹框 - fixed2
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#contactRecordsDetail"));
bounce_Fixed2.cancel();
$(function(){
    if (sphdSocket.user.oid === 0) {
        $(".specialOrgPart").show()
    } else {
        $(".specialOrgPart").hide()
    }
    $(".byDepart").on("click",".levelAcon",function(){
        $(this).next().toggle();
    });
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
}) ;

// creator: 李玉婷，2018-11-19 11:03:07， 获取分工基本信息列表
function getDivisionList(currPage  , pageSize  ){
    $.ajax({
        url:"../sales/getPdFenGongCustomer.do",
        data:{
            "currPage":currPage,
            "pageSize":pageSize
        },
        success:function(data){
            var totalPage = data["totalPage"];
            var cur = data["currPage"];
            var salesList = data["data"];
            $("#ye_customerManage").html("");
            var jsonStr = JSON.stringify({ }) ;
            setPage( $("#ye_division") , cur ,  totalPage , "salesDivision" ,jsonStr );
            $("#cusManage_body").html("");
            if(salesList !=undefined && salesList.length > 0 ){
                var number = 1;
                var str = '';
                for(var i=0 ; i<salesList.length ; i++ ){
                    number = i+1;
                    str += "<tr>" +
                        "<td>"+ number + "</td>" +
                        "<td>"+ handleNull(salesList[i]["code"]) + "</td>" +
                        "<td>"+ handleNull(salesList[i]["fullName"]) + "</td>" +
                        "<td>"+ handleNull(salesList[i]["createName"]) + '&nbsp;&nbsp;' + new Date(salesList[i]["createDate"]).format('yyyy-MM-dd hh:mm:ss') + "</td>" +
                        "<td>"+ handleNull(salesList[i]["principalName"]) + "</td>" +
                        "<td>" +
                        "   <span class='ty-color-blue' onclick='division_seebtn($(this))'>查看</span>" +
                        "   <span class='ty-color-blue' onclick='division_updatabtn($(this))'>更换销售负责人</span>";
                        if(salesList[i].size <= 1){
                            str +=
                                "   <span class='ty-color-gray'>销售负责人更换记录</span>";
                        }else{
                            str +=
                                "   <span class='ty-color-blue' onclick='sale_updateRecord($(this))'>销售负责人更换记录</span>";
                        }
                    str +=
                        "   <div class='hd'>" +
                        "       <span class='principalId'>"+ salesList[i]["creator"]  +"</span>" +
                        "       <span class='cusProId'>"+ salesList[i]["id"]  +"</span>" +
                        "       <span class='principalName'>"+ salesList[i]["principal"]  +"</span>" +
                        "   </div>" +
                        "</td>"+
                        "</tr>";
                }
                $(".divisionList tbody").html(str);
            }
        },
        error:function (meg) {
            loading.close();
            alert("连接错误，请稍后重试！");
        }
    })

}

// creator: 张旭博，2020-10-27 08:51:47，已暂停合作的客户 - 按钮
function suspendedCustomer() {
    $(".suspendedCustomer").show().siblings().hide()
    getSuspendCustomer(1, 20)
    $(".btnGroup").hide()
}

// creator: 张旭博，2020-10-27 09:47:30，获取暂停/恢复合作历史记录列表
function getSuspendRecordList() {
    bounce_Fixed.show($("#suspendRecord"))
    var customerId = $("#seeCustomer").data("id")
    $.ajax({
        url: '../sales/getSuspendOperationList.do',
        data: {
            customerId: customerId
        },
        success: function (res) {
            var data = res.data
            var str = ''
            for(var i in data) {
                str +=  '<tr>' +
                    '<td>' + (data[i].isSuspend === '1'?'暂停合作':'恢复合作') +'</td>' +
                    '<td>' + data[i].updateName + ' ' + formatTime(data[i].updateDate, true) + '</td>' +
                    '</tr>'
            }
            $("#suspendRecord tbody").html(str)
        }
    })
}

function goBack() {
    $(".main").show().siblings().hide()
    $(".btnGroup").show()
}

// creator: 张旭博，2020-10-27 08:50:54，获取已暂停合作的客户
function getSuspendCustomer(currPage, pageSize) {
    $.ajax({
        url: '../sales/getAllSuspendCustomer.do',
        data: {
            currPage:currPage,
            pageSize:pageSize
        },
        success: function (res) {
            var totalPage = res.totalPage
            var currPage = res.currPage
            var pdCustomers = res.data
            setPage( $("#ye_suspendCustomer") , currPage ,  totalPage , "suspendCustomer");
            var str = ''
            if (pdCustomers) {
                for (var i = 0; i < pdCustomers.length; i++) {
                    str += '<tr>' +
                        '<td>'+Number(i) + 1+'</td>'+
                        '<td>'+pdCustomers[i].code+'</td>'+
                        '<td>'+pdCustomers[i].name+'</td>'+
                        '<td>'+pdCustomers[i].createName + ' ' + formatTime(pdCustomers[i].createDate, true)+'</td>'+
                        '<td>'+pdCustomers[i].updateName + ' ' + formatTime(pdCustomers[i].updateDate, true)+'</td>'+
                        '<td>'+
                        '   <span class="ty-color-blue" onclick="division_seebtn($(this))">查看</span>'+
                        '   <span class="ty-color-green" onclick="sale_updateRecord($(this))">销售负责人更换记录</span>'+
                        '   <div class="hd"><span class="cusProId">'+pdCustomers[i].id+'</span></div>'+
                        '</td>'+
                        '</tr>'
                }
            }
            $(".suspendedCustomer tbody").html(str)
        }
    })
}

// creator: 李玉婷，2018-11-19 11:16:07， 查看按钮
var cus_seeTrObj = null ;
function division_seebtn(obj){
    bounce.show($("#seeCustomer"));
    var id = obj.siblings(".hd").children(".cusProId").html();
    if ( id == undefined || id == ""){
        $("#mtTip").show().siblings().hide().parent().show();
        $("#mt_tip_ms").html("系统错误，刷新重试！");
        return false;
    }
    $("#seeCustomer").data("id", id);
    $.ajax({
        url : "../sales/getPdCustomerOne.do" ,
        data : {
            "id":id
        },
        success:function(data){
            var tr_obj = obj.parent().parent();
            cus_seeTrObj = tr_obj;
            var pdCustomer = data["data"];
            var cuscoding = pdCustomer["code"];
            var cusname = pdCustomer["name"];
            var cusfullName = pdCustomer["fullName"];
            var firstBuyTime = pdCustomer["firstBuyTime"];
            var address = pdCustomer["address"];
            var invoiceName = pdCustomer["invoiceName"];
            var invoiceAddress = pdCustomer["invoiceAddress"];
            var phone = pdCustomer["telephone"];
            var bank = pdCustomer["bankName"];
            var accountnum = pdCustomer["bank_no"];
            var taxpayerID = pdCustomer["taxpayerID"];
            var firstContactAddress = pdCustomer["firstContactAddress"];
            var infoSource = pdCustomer["infoSource"];
            var memo = pdCustomer["memo"];
            var createName = pdCustomer["createName"];
            var supervisorMobile = pdCustomer["supervisorMobile"];
            var supervisorName = pdCustomer["supervisorName"];
            var firstContactTime = new Date(pdCustomer["firstContactTime"]).format('yyyy-MM-dd');
            var createDate = new Date(pdCustomer["createDate"]).format('yyyy-MM-dd hh:mm:ss');
            var qImages = pdCustomer["qImages"];
            var pImages = pdCustomer["pImages"];
            var contactList = pdCustomer["contactsList"];
            var shAddressList = pdCustomer["shAddressList"];
            var fpAddressList = pdCustomer["fpAddressList"];
            var initialPeriod = pdCustomer["initialPeriod"];
            var date = '';
            $("#see_cuscoding").html(cuscoding);
            $("#see_cusname").html(cusname);
            $("#see_cusfullName").html(cusfullName);
            $("#firstBuyTime").html(firstBuyTime);
            $("#see_address").html(address);
            $("#see_phone").html(phone);
            $("#see_bank").html(bank);
            $("#see_bankNo").html(accountnum);
            $("#taxpayerID").html(taxpayerID);
            $("#see_firstContactTime").html(firstContactTime);
            $("#firstContactAddress").html(firstContactAddress);
            $("#see_createName").html(createName);
            $("#see_createDate").html(createDate);
            $("#see_supervisorName").html(supervisorName);
            $("#see_supervisorMobile").html(supervisorMobile);
            $("#infoSource").html(infoSource);
            $("#see_memo").html(memo);
            $("#see_invoiceName").html(invoiceName);
            $("#see_invoiceAddress").html(invoiceAddress);
            $("#telephone").html(invoiceAddress);
            if(qImages.length > 0){
                var imgStr = '';
                for(var a=0;a<qImages.length;a++){
                    var path = '../' + qImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">' +
                        '    <div class="filePic" data-path="' + qImages[a].normal + '" style="background-image: url(' + path + ')"></div>' +
                        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                        '</div>';
                }
                $("#overallImgUpload").html(imgStr);
            }else{
                $("#overallImgUpload").html("");
            }
            if(pImages.length > 0){
                var imgStr = '';
                for(var a=0;a<pImages.length;a++){
                    var path = '../' + pImages[a].normal;
                    imgStr +=
                        '<div class="imgsthumb">' +
                        '    <div class="filePic" data-path="' + pImages[a].normal + '" style="background-image: url(' + path + ')"></div>' +
                        '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                        '</div>';
                }
                $("#productImgUpload").html(imgStr);
            }else{
                $("#productImgUpload").html('');
            }
            // 处理是否购买过本公司的产品
            var hasBuyStr = ''
            var initialType = pdCustomer.initialType;
            if (initialType === null) {
                hasBuyStr = '<div class="ty-alert ty-alert-info">'+createDate + '之前该客户尚未购买过本公司的商品</div>'
            } else {
                if(initialType === '1'){
                    var month = initialPeriod.substr(4,2);
                    if(month.slice(0,1) == '0'){
                        month = month.substr(1,1);
                    }
                    date = initialPeriod.substr(0,4) + '年'  + month  + '月';
                    hasBuyStr = '<div class="ty-alert ty-alert-info">'+date+'该客户首次购买过本公司的商品</div>'
                }else if(initialType === '2'){
                    hasBuyStr = '<div class="ty-alert ty-alert-info">去年该客户首次购买过本公司的商品</div>'
                }else{
                    hasBuyStr = '<div class="ty-alert ty-alert-info">去年之前该客户首次购买过本公司的商品</div>'
                }
                // 处理是否签合同
                var hasContract = pdCustomer.hasContract
                if (hasContract === '1') {
                    var sn = []
                    var contractSn = pdCustomer.contractSn
                    var expiresTime = pdCustomer.expiresTime
                    var contractTime = pdCustomer.contractTime

                    if (contractTime) sn.push('签署日期为'+new Date(contractTime).format("yyyy年MM月dd日"))
                    if (expiresTime) sn.push('有效期至'+new Date(expiresTime).format("yyyy年MM月dd日"))
                    if (contractSn) sn.push('合同编号为'+contractSn)
                    if (contractSn === ''&&expiresTime === ''&&contractTime === '') {
                        hasBuyStr += '<div class="ty-alert ty-alert-info">与该客户有处于有效期内的合同</div>'
                    } else {
                        hasBuyStr += '<div class="ty-alert ty-alert-info">与该客户有'+sn.join("、")+'的合同</div>'
                    }
                } else {
                    hasBuyStr += '<div class="ty-alert ty-alert-info">目前与该客户无处于有效期内的合同</div>'
                }
            }
            $("#seeCustomer .firstBuyTime").html(hasBuyStr)

            $(".see_contactList").html('');
            $(".recivePlaceList tbody").html('');
            $(".mailPlaceList tbody").html('');
            $(".contactNum").html(contactList.length);
            if(contactList.length > 0){
                var rhtml = '';
                var html = '<div class="leftList"><table class="ty-table ty-table-control">' +
                    '<thead>' +
                    '<tr>' +
                    '    <td>姓名</td>' +
                    '    <td>职位</td>' +
                    '    <td>操作</td>' +
                    '</tr>' +
                    '</thead><tbody>';
                if(contactList.length >= 2){rhtml = html;}
                for(var b in contactList){
                    var slice = b % 2;
                    if(slice > 0){
                        rhtml +=
                            '<tr class="'+(contactList[b].enabled == 1?'':'disable')+'" data-id="' + contactList[b].id + '">' +
                            '    <td>' + contactList[b].name + '</td>' +
                            '    <td>' + contactList[b].post + '</td>' +
                            '    <td>' +
                            '<span class="ty-color-blue" data-id="' + contactList[b].id + '" data-type="contactSocial" onclick="cus_recordDetail($(this))">联系方式</span>' +
                            '<span class="ty-color-blue" data-id="' + contactList[b].id + '" data-type="visitCard" onclick="cus_recordDetail($(this))">查看名片</span>' +
                            '<span class="ty-color-blue" data-type="contactRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                            '</tr>';
                    }else{
                        html +=
                            '<tr class="'+(contactList[b].enabled == 1?'':'disable')+'" data-id="' + contactList[b].id + '">' +
                            '    <td>' + contactList[b].name + '</td>' +
                            '    <td>' + contactList[b].post + '</td>' +
                            '    <td>' +
                            '<span class="ty-color-blue" data-id="' + contactList[b].id + '" data-type="contactSocial" onclick="cus_recordDetail($(this))">联系方式</span>' +
                            '<span class="ty-color-blue" data-id="' + contactList[b].id + '" data-type="visitCard" onclick="cus_recordDetail($(this))">查看名片</span>' +
                            '<span class="ty-color-blue" data-type="contactRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                            '</tr>';
                    }
                }
                html += '</tbody></table></div>';
                if(contactList.length >= 2){rhtml += '</tbody></table></div>';}
                var result = html + rhtml;
                $(".see_contactList").html(result);
            }
            if(shAddressList.length > 0){
                var html = '';
                for(var b in shAddressList){
                    var num = 1 + Number(b);
                    html +=
                        '<tr class="'+(shAddressList[b].enabled == 1?'':'disable')+'" data-id="' + shAddressList[b].id + '">' +
                        '    <td>' + num + '</td>' +
                        '    <td>' + shAddressList[b].address + '</td>' +
                        '    <td>' + shAddressList[b].contact + '</td>' +
                        '    <td>' + shAddressList[b].mobile + '</td>' +
                        '    <td><span class="ty-color-blue" data-type="shAddressRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                        '</tr>';
                }
                $(".recivePlaceList tbody").html(html);
            }
            if(fpAddressList.length > 0){
                var html = '';
                for(var b in fpAddressList){
                    var num = 1 + Number(b);
                    html +=
                        '<tr class="'+(fpAddressList[b].enabled == 1?'':'disable')+'" data-id="' + fpAddressList[b].id + '">' +
                        '    <td>' + num + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].address) + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].contact) + '</td>' +
                        '    <td>' + handleNull(fpAddressList[b].postcode) + '</td>' +
                        '    <td><span class="ty-color-blue" data-type="fpAddressRecords" data-obj="see" onclick="getRecordList($(this))">修改记录</span></td>' +
                        '</tr>';
                }
                $(".mailPlaceList tbody").html(html);
            }
        }
    })
}
/*修改记录前、后查看*/
function cus_recordDetail(obj){
    $(".initValue").html("");
    var json ={
        'id': obj.data('id'),
        'frontId': obj.data('frontid')
    };
    var seeType = obj.data('type');
    switch(seeType){
        case 'baseRecords':
            $.ajax({
                url : "../sales/getRecordBaseDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    var name = nowData.name;
                    var code = nowData.code;
                    var address = nowData.address;
                    var infoSource = nowData.infoSource;
                    var initialType = nowData.initialType;
                    var initialPeriod = nowData.initialPeriod;
                    var firstContactTime = new Date(nowData.firstContactTime).format('yyyy/MM/dd');
                    var firstContactAddress = nowData.firstContactAddress;
                    var qImages = nowData.qImages;
                    var pImages = nowData.pImages;
                    var date = '';
                    if (qImages.length > 0){
                        var html = setImgHtml(1,qImages);
                        $("#base_qImgUpload").html(html);
                    }
                    if (pImages.length > 0){
                        var html = setImgHtml(1,pImages);
                        $("#base_pImgUpload").html(html);
                    }
                    if(initialType == '1'){
                        var month = initialPeriod.substr(4,2);
                        if(month.slice(0,1) == '0'){
                            month = month.substr(1,1);
                        }
                        date = initialPeriod.substr(0,4) + '年'  + month  + '月';
                        $("#base_firstBuyTime").html(date + '该客户首次购买过本公司的商品');
                    }else if(initialType == null) {
                        date = new Date(nowData.createDate).format('yyyy/MM/dd hh:mm:ss');
                        $("#base_firstBuyTime").html(date + '之前该客户尚未购买过本公司的商品');
                    }else if(initialType == '2'){
                        $("#base_firstBuyTime").html('去年该客户首次购买过本公司的商品');
                    }else{
                        $("#base_firstBuyTime").html('去年之前该客户首次购买过本公司的商品');
                    }
                    if(frontData == null){
                        $("#BaseRecordsDetail .detaileTtl").html('原始信息');
                        $("#base_cusname").html(name);
                        $("#base_cuscoding").html(code);
                        $("#base_address").html(address);
                        $("#base_firstContactTime").html(firstContactTime);
                        $("#base_firstAddress").html(firstContactAddress);
                        $("#base_infoSource").html(infoSource);
                        $("#base_firstBuyTime").css("color","#34495e");
                    }else{
                        $("#BaseRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#base_cusname").html(compareD(frontData.name,nowData.name));
                        $("#base_cuscoding").html(compareD(frontData.code,nowData.code));
                        $("#base_address").html(compareD(frontData.address,nowData.address));
                        $("#base_firstContactTime").html(compareD(new Date(frontData.firstContactTime).format('yyyy/MM/dd'),new Date(nowData.firstContactTime).format('yyyy/MM/dd')));
                        $("#base_firstAddress").html(compareD(frontData.firstContactAddress,nowData.firstContactAddress));
                        $("#base_infoSource").html(compareD(frontData.infoSource,nowData.infoSource));
                        if(frontData.initialType != nowData.initialType){
                            $("#base_firstBuyTime").css("color","red");
                        }else{
                            $("#base_firstBuyTime").css("color","#34495e");
                        }
                    }
                    bounce_Fixed2.show($("#BaseRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'invoiceRecords':
            $.ajax({
                url : "../sales/getRecordInvoiceDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(frontData == null){
                        $("#invoiceRecordsDetail .detaileTtl").html('原始信息');
                        $("#inv_invoiceName").html(nowData.invoiceName);
                        $("#inv_phone").html(nowData.telephone);
                        $("#inv_invoiceAddress").html(nowData.invoiceAddress);
                        $("#inv_bank").html(nowData.bankName);
                        $("#inv_bankNo").html(nowData.bankNo);
                        $("#inv_taxpayerID").html(nowData.taxpayerID);
                    }else{
                        $("#invoiceRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#inv_invoiceName").html(compareD(frontData.invoiceName,nowData.invoiceName));
                        $("#inv_phone").html(compareD(frontData.telephone,nowData.telephone));
                        $("#inv_invoiceAddress").html(compareD(frontData.invoiceAddress,nowData.invoiceAddress));
                        $("#inv_bank").html(compareD(frontData.bankName,nowData.bankName));
                        $("#inv_bankNo").html(compareD(frontData.bankNo,nowData.bankNo));
                        $("#inv_taxpayerID").html(compareD(frontData.taxpayerID,nowData.taxpayerID));
                    }
                    bounce_Fixed2.show($("#invoiceRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'shAddressRecords':
            $.ajax({
                url : "../sales/getRecordShAddressDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(nowData.operation == '2' || frontData == null){
                        if(nowData.operation == '2'){
                            var tip = nowData.enabled == '1'? '本地址于'+ new Date(nowData.enabledTime).format('yyyy/MM/dd hh:mm:ss') +'被'+ nowData.updateName +'启用。':'本地址于'+ new Date(nowData.enabledTime).format('yyyy/MM/dd hh:mm:ss') +'被'+nowData.updateName+'停用。'
                            $('.shDisUse').show().html(tip);
                        }else{
                            $('.shDisUse').hide();
                        }
                        $("#shRecordsDetail .detaileTtl").html('原始信息');
                        $("#shAddress").html(handleNull(nowData.address));
                        $("#shName").html(handleNull(nowData.contact));
                        $("#shNumber").html(handleNull(nowData.mobile));
                    }else{
                        $('.shDisUse').hide().html('');
                        $("#shRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#shAddress").html(compareD(frontData.address,nowData.address));
                        $("#shName").html(compareD(frontData.contact,nowData.contact));
                        $("#shNumber").html(compareD(frontData.mobile,nowData.mobile));
                    }
                    bounce_Fixed2.show($("#shRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'fpAddressRecords':
            $.ajax({
                url : "../sales/getRecordFpAddressDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    if(nowData.operation == '2' || frontData == null){
                        if(nowData.operation == '2'){
                            var tip = nowData.enabled == '1'? '本地址于'+ new Date(nowData.enabledTime).format('yyyy/MM/dd hh:mm:ss') +'被'+nowData.updateName+'启用。':'本地址于'+ new Date(nowData.enabledTime).format('yyyy/MM/dd hh:mm:ss') +'被'+nowData.updateName+'停用。'
                            $('.fpDisUse').show().html(tip);
                        }else{
                            $('.fpDisUse').hide();
                        }
                        $("#fpRecordsDetail .detaileTtl").html('原始信息');
                        $("#fpAddress").html(handleNull(nowData.address));
                        $("#fpName").html(handleNull(nowData.contact));
                        $("#fpNumber").html(handleNull(nowData.postcode));
                        $("#fpMobile").html(handleNull(nowData.mobile));
                    }else{
                        $('.fpDisUse').html('').hide();
                        $("#fpRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#fpAddress").html(compareD(frontData.address,nowData.address));
                        $("#fpName").html(compareD(frontData.contact,nowData.contact));
                        $("#fpNumber").html(compareD(frontData.postcode,nowData.postcode));
                        $("#fpMobile").html(compareD(frontData.mobile,nowData.mobile));
                    }
                    bounce_Fixed2.show($("#fpRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'contactRecords':
            $.ajax({
                url : "../sales/getRecordContactDetails.do" ,
                data : json,
                success:function(data){
                    var nowData = data['data']['now'];
                    var frontData = data['data']['front'];
                    var socialList = nowData.socialList;
                    var frontSocialList = [];
                    if(frontData == null){
                        $("#contactRecordsDetail .detaileTtl").html('原始信息');
                        $("#record_contactName").html(nowData.name);
                        $("#record_position").html(nowData.post);
                    }else{
                        $("#contactRecordsDetail .detaileTtl").html(obj.parent().prev().html());
                        $("#record_contactName").html(compareD(frontData.name,nowData.name));
                        $("#record_position").html(compareD(frontData.post,nowData.post));
                    }
                    $("#record_contactsCard").html("");
                    if (nowData.visitCard != '' && nowData.visitCard != undefined && nowData.visitCard != null){
                        var path = '../' + nowData.visitCard;
                        var imgStr =
                            '<div class="bussnessCard">' +
                            '	<div class="filePic" data-path="' + nowData.visitCard + '" style="background-image: url(' + path + ')"></div>' +
                            '</div>';
                        $("#record_contactsCard").html(imgStr);
                    }
                    var html = '',str = '';
                    var mobile = '',qq = '',email = '',weixin = '',weibo = '', defined = '';
                    if(frontData == null){
                        str = nowData.mobile != "" ? '<div class="sale-con">'+ nowData.mobile +'</div>' : '';
                        for(var r in socialList) {
                            switch (socialList[r].type) {
                                case '1':
                                    mobile += '<div class="sale-con">' + socialList[r].code + '</div>';
                                    break;
                                case '2':
                                    qq += '<div class="sale-con">' + socialList[r].code + '</div>';
                                    break;
                                case '3':
                                    email += '<div class="sale-con">' + socialList[r].code + '</div>';
                                    break;
                                case '4':
                                    weixin += '<div class="sale-con">' + socialList[r].code + '</div>';
                                    break;
                                case '5':
                                    weibo += '<div class="sale-con">' + socialList[r].code + '</div>';
                                    break;
                                case '9':
                                    defined += '<div class="sale-con">' + socialList[r].code + '</div>';
                                    break;
                            }
                        }
                    }else{
                        frontSocialList = frontData.socialList;
                        str = nowData.mobile != "" ? '<div class="sale-con">'+ compareD(frontData.mobile,nowData.mobile) +'</div>' : '';
                        for(var r in socialList) {
                            var flag = frontSocialList.find(i=>i.code === socialList[r].code);
                            var redFlag = '';
                            if(flag == undefined){
                                redFlag = 'redFlag'
                            }
                            switch (socialList[r].type) {
                                case '1':
                                    mobile += '<div class="sale-con '+ redFlag +'">' + socialList[r].code + '</div>';
                                    break;
                                case '2':
                                    qq += '<div class="sale-con '+ redFlag +'">' + socialList[r].code + '</div>';
                                    break;
                                case '3':
                                    email += '<div class="sale-con '+ redFlag +'">' + socialList[r].code + '</div>';
                                    break;
                                case '4':
                                    weixin += '<div class="sale-con '+ redFlag +'">' + socialList[r].code + '</div>';
                                    break;
                                case '5':
                                    weibo += '<div class="sale-con '+ redFlag +'">' + socialList[r].code + '</div>';
                                    break;
                                case '9':
                                    defined += '<div class="sale-con '+ redFlag +'">' + socialList[r].code + '</div>';
                                    break;
                            }
                        }
                    }
                    if(mobile != "" || nowData.mobile != ""){
                        html +=
                            '<li>' +
                            '<div class="sale_ttl1">手机：</div>' +
                            '<div>'+ str + mobile +'</div>'+
                            '</li>';
                    }
                    if(qq != ''){
                        html +=
                            '<li>' +
                            '<div class="sale_ttl1">QQ：</div>' +
                            '<div>'+ qq +'</div>'+
                            '</li>';
                    }
                    if(email != ''){
                        html +=
                            '<li>' +
                            '<div class="sale_ttl1">邮箱：</div>' +
                            '<div>'+ email +'</div>'+
                            '</li>';
                    }
                    if(weixin != ''){
                        html +=
                            '<li>' +
                            '<div class="sale_ttl1">微信：</div>' +
                            '<div>'+ weixin +'</div>'+
                            '</li>';
                    }
                    if(weibo != ''){
                        html +=
                            '<li>' +
                            '<div class="sale_ttl1">微博：</div>' +
                            '<div>'+ weibo +'</div>'+
                            '</li>';
                    }
                    if(defined != ''){
                        html +=
                            '<li>' +
                            '<div class="sale_ttl1">自定义：</div>' +
                            '<div>'+ defined +'</div>'+
                            '</li>';
                    }
                    $(".record_otherContact").html(html);
                    bounce_Fixed2.show($("#contactRecordsDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'contactSocial':
            $.ajax({
                url : "../sales/getContactsSocial.do" ,
                data : {
                    'contactId': obj.data('id')
                },
                success:function(data){
                    var get = data['data'];
                    var html = '',socialList = get['socialList'];
                    $("#see_contactName").html(get.name);
                    $("#see_position").html(get.post);
                    $(".see_otherContact").html("");
                    $("#contactSeeDetail .see_createName").html(get.createName);
                    $("#contactSeeDetail .see_createDate").html(new Date(get.createDate).format('yyyy/MM/dd hh:mm:ss'));
                    if(socialList.length > 0){
                        var mobile = '',qq = '',email = '',weixin = '',weibo = '', defined = '';
                        for(var r in socialList){
                            switch (socialList[r].type) {
                                case '1':
                                    mobile += '<div>'+ socialList[r].code +'</div>';
                                    break;
                                case '2':
                                    qq += '<div>'+ socialList[r].code +'</div>';
                                    break;
                                case '3':
                                    email += '<div>'+ socialList[r].code +'</div>';
                                    break;
                                case '4':
                                    weixin += '<div>'+ socialList[r].code +'</div>';
                                    break;
                                case '5':
                                    weibo += '<div>'+ socialList[r].code +'</div>';
                                    break;
                                case '9':
                                    defined += '<div>'+ socialList[r].code +'</div>';
                                    break;
                            }
                        }
                        if(mobile != "" || get.mobile != ""){
                            var str = get.mobile != "" ? '<div>'+ get.mobile +'</div>' : '';
                            html +=
                                '<li>' +
                                '<div class="type-icon icon-mobile"></div>' +
                                '<div>'+ str + mobile +'</div>'+
                                '</li>';
                        }
                        if(qq != ''){
                            html +=
                                '<li>' +
                                '<div class="type-icon icon-qq"></div>' +
                                '<div>'+ qq +'</div>'+
                                '</li>';
                        }
                        if(email != ''){
                            html +=
                                '<li>' +
                                '<div class="type-icon icon-email"></div>' +
                                '<div>'+ email +'</div>'+
                                '</li>';
                        }
                        if(weixin != ''){
                            html +=
                                '<li>' +
                                '<div class="type-icon icon-weixin"></div>' +
                                '<div>'+ weixin +'</div>'+
                                '</li>';
                        }
                        if(weibo != ''){
                            html +=
                                '<li>' +
                                '<div class="type-icon icon-weibo"></div>' +
                                '<div>'+ weibo +'</div>'+
                                '</li>';
                        }
                        if(defined != ''){
                            html +=
                                '<li>' +
                                '<div class="icon-defined">自定义</div>' +
                                '<div>'+ defined +'</div>'+
                                '</li>';
                        }
                        $(".see_otherContact").html(html);
                    }
                    bounce_Fixed.show($("#contactSeeDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
        case 'visitCard':
            $.ajax({
                url : "../sales/getContactCard.do" ,
                data : {
                    'contactId': obj.data('id')
                },
                success:function(data){
                    var status = data.status;
                    if (status == '1') {
                        if(data.visitCard && data.visitCard != "" && data.visitCard != null){
                            $('#see_contactsCard').html('<img src="../' + data.visitCard +'" />');
                        }else{
                            $('#see_contactsCard').html('');
                        }
                    }
                    bounce_Fixed.show($("#visitCardDetail"));
                },
                error: function (msg) {
                    layer.msg("连接错误，请稍后重试！");
                    return false;
                }
            });
            break;
    }
}
// creator: 李玉婷，2019-09-09 19:52:16，修改记录列表获取公用方法
function getRecordList(obj) {
    var type = obj.data('type');
    var getObj = obj.data('obj');
    var customerId = '';
    if(getObj == 'see'){
        customerId = $("#seeCustomer").data("id");
    }else if(getObj == 'update'){
        customerId = $("#updateCustormPanel").data("id");
    }else if(getObj == 'interview'){
        customerId = obj.parents("tr").data("id");
    }
    switch (type) {
        case 'baseRecords':
            $(".recordTtl").html('基本信息修改记录');
            $.ajax({
                url: '/sales/getRecordBaseList.do',
                data: {
                    'customerId': customerId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'invoiceRecords':
            $(".recordTtl").html('开票信息修改记录');
            $.ajax({
                url: '/sales/getRecordInvoiceList.do',
                data: {
                    'customerId': customerId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'contactRecords':
            $(".recordTtl").html('联系人修改记录');
            $.ajax({
                url: '/sales/getRecordContactList.do',
                data: {
                    'contactId': obj.parents('tr').data('id')
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
        case 'shAddressRecords':
        case 'fpAddressRecords':
            var addressId = obj.parents('tr').data('id');
            $.ajax({
                url: '/sales/getRecordAddressList.do',
                data: {
                    'addressId': addressId
                },
                success: function (data) {
                    var status = data.status;
                    if (status === '1' || status === 1) {
                        if(type == 'shAddressRecords'){
                            $(".recordTtl").html('收货信息修改记录');
                        }else{
                            $(".recordTtl").html('发票邮寄信息修改记录');
                        }
                        getRecordsList(data, type);
                    } else {
                        layer.msg("查看失败！");
                    }
                }
            })
            break;
    }
}
// creator: 李玉婷，2019-09-10 16:43:23，基本信息、发票信息修改记录列表数据获取
function getRecordsList(data,type){
    var getList = data.list;
    if(getList.length > 0) {
        var str = '';
        var eidtNumber = getList.length - 1;
        $(".createRecord .recordTip").html('当前数据为第' + eidtNumber + '次修改后的结果。');
        $(".createRecord .recordEditer").html('修改时间：' + new Date(data.updateDate).format('yyyy/MM/dd hh:mm:ss'));
        $(".changeRecord").show();
        for (r in getList) {
            if (r == '0') {
                str +=
                    '<tr>' +
                    '   <td>原始信息</td>' +
                    '   <td><span class="ty-color-blue" data-id="' + getList[r].id + '" data-frontid="0" data-type="'+ type +'" onclick="cus_recordDetail($(this))">查看</span></td>' +
                    '   <td>' + getList[r].createName + ' &nbsp; ' + new Date(getList[r].createDate).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
                    '</tr>';
            } else {
                var front = Number(r) - 1;
                str +=
                    '<tr>' +
                    '   <td>第' + r + '次修改后</td>' +
                    '   <td><span class="ty-color-blue" data-id="' + getList[r].id + '" data-frontid="' + getList[front].id + '" data-type="'+ type +'" onclick="cus_recordDetail($(this))">查看</span></td>' +
                    '   <td>' + getList[r].updateName + ' &nbsp; ' + new Date(getList[r].updateDate).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
                    '</tr>';
            }
        }
        $(".changeRecord tbody").html(str);
    }else {
        $(".createRecord .recordTip").html('当前资料尚未经修改');
        $(".createRecord .recordEditer").html('创建人：'+ data.createName + '&nbsp;&nbsp;' + new Date(data.createDate).format('yyyy/MM/dd hh:mm:ss'));
        $(".changeRecord").hide();
    }
    bounce_Fixed.show($("#updateRecords"));
}
// creator: 李玉婷，2019-11-19 11:16:48， creator:lyt 修改销售负责人按钮
function division_updatabtn(obj){
    cus_seeTrObj = obj.parent().parent();
    var id = obj.siblings(".hd").children(".cusProId").html();
    var orgPrincipalNmae = obj.parents("tr").children().eq(4).html();
    $("#changeSaler").data("cusProId",id);
    $("#orgSale").html(orgPrincipalNmae);
    $.ajax({
        url: "../sales/getSalesmanList.do",
        data: {},
        success: function (data) {
            var sales = data["salesmanList"];
            var salesHtml = '<option value="0">请选择</option>';
            if(sales.length>0){
                for(var t=0;t<sales.length;t++){
                    salesHtml +=
                        '<option value="'+ sales[t].userID +'">'+ sales[t].userName +'</option>';
                }
                $(".salesList").html(salesHtml);
                bounce.show($("#changeSaler"));
            }else{
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("无可换的销售负责人！");
            }
        },
        error: function (msg) {  // 失败以后的操作
            bounce.show($("#mtTip"));
            $("#mt_tip_ms").html("连接错误，请稍后重试！");
            return false;
        }
    });
    bounce.everyTime('0.5s','updateSalar',function(){
        if($(".salesList").val() != "0"){
            $("#changeSalerSure").prop("disabled",false);
        }else{
            $("#changeSalerSure").prop("disabled",true);
        }
    })
}
//creator:lyt date:2018/11/21 16:00:00 更换销售负责人确定
function sale_updateSure(){
    var cusOrg = $("#changeSaler").data("cusProId");
    var principal = $(".salesList").val();
    var principalName = $(".salesList option:selected").html();
    if(principal == '' || principal == '0' || principal == 0){
        bounce_Fixed.show($("#tip"));
        $("#tip .tipMs").html("请选择销售负责人！"); ///  不合法的提示信息赋值
        return false; // 结束
    }
    var pram = {
        "id" : cusOrg,
        "principal" : principal,
        "principalName" : principalName
    };
    $.ajax({
        url: "../sales/updatePrincipal.do",
        data: pram,
        success: function (data) {
            if(data["status"] == 1){
                bounce.cancel();
                cus_seeTrObj.children().eq(4).html(principalName);
                cus_seeTrObj.children().eq(5).find("span").eq(2).removeClass("ty-color-gray").addClass("ty-color-blue").attr("onclick","sale_updateRecord($(this))");
            }
        }
    });
}

//creator:lyt date:2018/11/22 13:20:00 销售负责人修改记录
function sale_updateRecord(obj){
    var id = obj.siblings(".hd").children(".cusProId").html();
    if ( id == undefined || id == ""){  // 校验销售id 是否合法
        bounce.show($("#mtTip"));
        $("#mt_tip_ms").html("系统错误，刷新重试！"); ///  不合法的提示信息赋值
        return false; // 结束
    }
    $.ajax({
        url: "../sales/replacePrincipalrecord.do",
        data: {
            "customer": id
        },
        success: function (data) {
            var list = data["list"];
            var html =
                '<tr>' +
                '    <td>修改时间</td>' +
                '    <td>修改前</td>' +
                '    <td>修改后</td>' +
                '    <td>修改人</td>' +
                '</tr>';
            for(var k=0;k<list.length;k++){
                html +=
                    '<tr>' +
                    '    <td>' + new Date(list[k].updateDate).format('yyyy/MM/dd hh:mm:ss') + '</td>' +
                    '    <td>' + list[k].beforePrincipalName + '</td>' +
                    '    <td>' + list[k].afterPrincipalName + '</td>' +
                    '    <td>' + list[k].updateName + '</td>' +
                    '</tr>';
            }
            $("#changeSalerRecord table tbody").html(html);
            bounce.show($("#changeSalerRecord"));
        }
    })
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.attr('path');
    $("#picShow img").attr('src', src);
    $("#picShow").fadeIn("fast");
}
// creator: 李玉婷，2019-09-06 14:26:59，图片字符串输出
function setImgHtml(type,imgs) { // type: 1=查看 2= 修改
    var html = '';
    for(var e in imgs){
        var path = '../' + imgs[e].normal;
        html +=
            '<div class="imgsthumb">' +
            '    <div class="filePic" data-path="' + imgs[e].normal + '" style="background-image: url(' + path + ')"></div>';
        if(type == '2'){
            html += '    <span onclick="cancleThis($(this))">删除</span> ';
        }
        html +=
            '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a></div>';
    }
    return html;
}


//--------------- 销售回款 -----------------
// creator: 李玉婷，2018-04-26 11:18:32，返回主页
function backIndex(obj){
    obj.hide().siblings().hide();
    $("#mainPane").children().eq(0).show().siblings().hide();
}
//creator:lyt date:2019/4/28 获得分工基本信息/回款录入者列表
function turnTab(num){
    $("#rebackBtn").show();
    if(num == 1){
        // 获得分工基本信息列表
        getDivisionList(1,20);
        $(".opinionCon").show().siblings().hide();
        $("#addInputer").hide();
    }else if (num == 2){
        // 获得回款录入者列表
        getPayBackList('gb');
        $(".moneyBack").show().siblings().hide();
        $("#addInputer").show().data('name', 'payCash');
        $(".payBackList tbody").html("");
    }else if (num == 3){
        // 获得潜在客户录入者列表
        getPayBackList('gc');
        $(".potentialCustomers").show().siblings().hide();
        $("#addInputer").show().data('name', 'potential');
        $(".potentialCustomersList tbody").html("");
    }else if (num == 4){
        // 访谈记录管理
        getPayBackList('gd');
        $(".interview").show().siblings().hide();
        $("#addInputer").show().data('name', 'interview');
        $(".interview tbody").html("");
    }
}
// creator: 李玉婷，2019-05-20 11:20:40，获取有回款权限员工的列表
function getPayBackList(code){
    $.ajax({
        "url": "../saleRtUser/getReturnInputUserList.do",
        "data": {
            'code': code
        },
        success: function (data) {
            var getData = data.data;
            var html = '';
            if(getData && getData.length > 0){
                var num = 0;
                for(var a=0;a<getData.length;a++){
                    num = 1 + a;
                    html +=
                        '<tr>' +
                        '    <td>'+ num +'</td>' +
                        '    <td>'+ getData[a].userName +'</td>' +
                        '    <td>'+ getData[a].mobile +'</td>' +
                        '    <td>'+ handleNull(getData[a].departName) +'</td>' +
                        '    <td>'+ handleNull(getData[a].postName) +'</td>';
                    if(code == 'gb'){
                        if(getData[a].roleCode == 'super' || getData[a].roleCode == 'sale' || getData[a].roleCode == 'smallSuper'){
                            html += '    <td>全部客户</td>';
                        } else{
                            html += '    <td>'+ getData[a].hkNum +'个客户</td>';
                        }
                    }
                    if(code == 'gd'){
                        if(getData[a].roleCode == 'super' || getData[a].roleCode == 'sale' || getData[a].roleCode == 'smallSuper'){
                            html += '    <td>全部客户</td>';
                        } else{
                            html += '    <td>'+ getData[a].hkNum +'个客户</td>';
                        }
                    }
                    if(getData[a].roleCode == 'super' || getData[a].roleCode == 'smallSuper' || getData[a].roleCode == 'finance' || getData[a].roleCode == 'sale') {
                        html +=
                            '    <td></td>';
                    }else {
                        if(code == 'gb'||code == 'gd'){
                            html +=
                                '<td>' +
                                '   <span class="ty-color-blue" data-code="'+ code +'" data-id="'+ getData[a].userID +'" onclick="objectManagement($(this))">对象管理</span>' +
                                '   <span class="ty-color-blue" sId="'+ getData[a].userID +'" data-code="'+ code +'" onclick="limitChange($(this))">取消本权限</span>' +
                                '</td>';
                        }else {
                            html +=
                                '<td>' +
                                '   <span class="ty-color-blue" sId="'+ getData[a].userID +'" data-code="'+ code +'" onclick="limitChange($(this))">取消本权限</span>' +
                                '</td>';
                        }
                    }
                    html +=
                        '</tr>';
                }
                if(code == 'gb'){
                    $(".payBackList tbody").html(html);
                }else if(code == 'gc'){
                    $(".potentialCustomersList tbody").html(html);
                }else if(code == 'gd'){
                    $(".interviewList tbody").html(html);
                }
            }
        }
    })
}
// creator: 李玉婷，2019-05-20 11:23:58，打开新增弹窗
function addInputer(obj) {
    $(".byAll").html("").removeClass('isSelect').hide();
    $(".byDepart").html("").removeClass('isSelect').hide();
    $('.selectBtn').attr('status', '0');
    var name = obj.data('name');
    bounce.show($("#addPayInputer"));
    switch (name) {
        case 'payCash':
            $(".addRightTtl").html('增加有回款录入权限的职工');
            $("#addPayInputer").data('code','gb');
            $(".addInputerSure").data('name','payCash');
            break;
        case 'potential':
            $(".addRightTtl").html('增加有潜在客户录入权限的职工');
            $("#addPayInputer").data('code','gc');
            $(".addInputerSure").data('name','potential');
            break;
        case 'interview':
            $(".addRightTtl").html('增加有访问记录录入权限的职工');
            $("#addPayInputer").data('code','gd');
            $(".addInputerSure").data('name','interview');
            $("#addCollectionEntryObject").data('code','gd');

            break;
    }
}
// creator: 李玉婷，2019-05-20 11:24:14，全部员工职工列表
function byAllType(obj){
    var code = $("#addPayInputer").data('code');
    obj.next().toggle().addClass('isSelect');
    obj.attr('status','1');
    if($(".departSelectBtn").attr("status") == '1'){
        $(".byDepart ul").html("");
        $(".departSelectBtn").attr("status",'0');
        $(".departSelectBtn").next().toggle().removeClass('isSelect');
    }
    var html = '';
    $.ajax({
        "url": "../saleRtUser/getNoReturnUserList.do",
        "data": {
            'code': code
        },
        success: function (data) {
            var stuffDate = data.data;
            if(stuffDate && stuffDate.length > 0){
                for(var s=0;s<stuffDate.length;s++){
                    html +=
                        '<div val="0" onclick="checkTab($(this))">' +
                        '<span>'+ stuffDate[s].userName +'</span>' +
                        '<span>'+ stuffDate[s].mobile +'</span><i class="fa fa-square-o" sId="'+ stuffDate[s].userID +'"></i></div>';
                }
                $(".byAll").html(html);
            }
        }
    })
}
// creator: 李玉婷，2019-05-20 11:24:52，获取按部门排序的职工列表
function byDepartType(obj){
    var code = $("#addPayInputer").data('code');
    obj.next().toggle().addClass('isSelect');
    obj.attr('status','1');
    if($(".allSelectBtn").attr("status") == '1'){
        $(".byAll").html("");
        $(".allSelectBtn").attr("status",'0');
        $(".allSelectBtn").next().toggle().removeClass('isSelect');
    }
    var html = '';
    $.ajax({
        "url": "../saleRtUser/getNoReturnDepartUserList.do",
        "data": {
            'code': code
        },
        success: function (data) {
            var stuffDate = data.data;
            if(stuffDate && stuffDate.length > 0){
                html = '<ul>';
                for(var i in stuffDate){
                    var userList = stuffDate[i]["userList"]; // 人员列表
                    var kidList = stuffDate[i]["subList"]; // 部门列表
                    html += '<li class="levelA"><div class="levelAcon">'+ stuffDate[i].name +'</div>';
                    if(kidList && kidList.length > 0){ // 遍历子级部门
                        html += getUserList( kidList );
                    }
                    if(userList && userList.length > 0){ // 遍历员工
                        html += '<div style="display: none;">';
                        for(var j in userList){
                            html +=
                                '<div class="stuff" val="0" onclick="checkTab($(this))">' +
                                '<span>'+ userList[j].userName +'</span>' +
                                '<span>'+ userList[j].mobile +'</span><i class="fa fa-square-o" sId="'+ userList[j].userID +'"></i></div>';
                        }
                        html += '</div>';
                    }
                    html += '</li>';
                }
                html += '</ul>';
                $(".byDepart").html(html);
            }
        }
    })
}
// creator: 李玉婷，2019-05-20 11:25:30，
function getUserList(arr){
    if(arr && arr.length > 0){
        var partStr = '<ul style="display: none;">';
        for(var i in arr){
            var userList = arr[i]["userList"]; // 人员列表
            var kidList = arr[i]["subList"]; // 部门列表
            partStr += '<li class="levelA"><div class="levelAcon">'+ arr[i].name +'</div>';
            if(kidList && kidList.length > 0){ // 遍历子级部门
                partStr += getUserList( kidList );
            }
            if(userList && userList.length > 0){ // 遍历员工
                partStr += '<div style="display: none;">';
                for(var j in userList){
                    partStr +=
                        '<div class="stuff" val="0" onclick="checkTab($(this))">' +
                        '<span>'+ userList[j].userName +'</span>' +
                        '<span>'+ userList[j].mobile +'</span><i class="fa fa-square-o" sId="'+ userList[j].userID +'"></i></div>';
                }
                partStr += '</div>';
            }
            partStr += '</li>';
        }
        partStr += '</ul>';
        return partStr;
    }
}
// creator: 李玉婷，2019-05-20 11:28:08，新添确定
function addInputerSure() {
    var checkLen = $(".isSelect .fa-check-square-o").length;
    if(checkLen <= 0){
        $(".tipMs").html('您至少需选择一位职工后才可点击确定！');
        bounce_Fixed.show($("#tip"));
        return false;
    }
    var code = $("#addPayInputer").data('code');
    // var code = $("#addCollectionEntryObject").data('code');
    var stuffId = '';
    $(".isSelect .fa-check-square-o").each(function(){
        stuffId += $(this).attr('sId') + ',';
    });
    var len = stuffId.length - 1;
    stuffId = stuffId.substr(0, len);
    $.ajax({
        "url": "../../saleRtUser/saveReturnUsers.do",
        "data": {
            'code': code,
            'userIds': stuffId
        },
        success: function (data) {
            var status = data.success;
            if(status == '1'){
                getPayBackList(code);
                bounce.cancel();
            }
        }
    })
}
// creator: 李玉婷，2019-05-20 11:28:34，取消本权限
var limitObj = null;
function limitChange(obj) {
    var code = obj.data('code');
    limitObj = obj;
    bounce.show($("#changeLimit"));
    switch (code) {
        case 'gb':
            $("#changeLimit .limitTextTip").html('您确定取消其回款录入的权限吗!');
            $("#limitChangeSure").data('code',code);
            break;
        case 'gc':
            $("#changeLimit .limitTextTip").html('您确定取消其潜在客户录入的权限吗!');
            $("#limitChangeSure").data('code',code);
        case 'gd':
            $("#changeLimit .limitTextTip").html('您确定取消其访谈记录录入的权限吗!');
            $("#limitChangeSure").data('code',code);
            break;
    }
}
// creator: 李玉婷，2019-12-04 15:14:29，对象管理
function objectManagement(obj) {
    var userId = obj.data('id');
    var code = obj.data('code');
    var userName = obj.parents('tr').children().eq(1).html();
    $(".enteredName").html(userName);
    $(".objectManagementList tbody").html('');
    $("#addCollectionEntryObject").data('userId', userId);
    $("#addCollectionEntryObject").data('code', code);
    objectManagementListGet();
}
// creator: 李玉婷，2019-12-09 15:48:15，
function objectManagementListGet() {
    var code = $("#addCollectionEntryObject").data('code');
    var userId = $("#addCollectionEntryObject").data('userId');
    var url = "";
    if(code === "gb"){
        $("#objectManagementIndex .bonceHead span").html("回款录入的对象");
        $(".entereType").html("回款");
        url = "../saleRtUser/getWarrantCustomerList.do";
    }else if(code === "gd"){
        $("#objectManagementIndex .bonceHead span").html("访谈记录的对象");
        $(".entereType").html("访谈记录");
        url = "../saleInterview/getWarrantCustomerList.do";
    }
    $.ajax({
        "url": url,
        "data": {
            'userId': userId
        },
        success: function (data) {
            var status = "";
            if(code === "gb"){
                status = data.success == '1' ? true : false ;
            }else if(code === "gd"){
                status = data.code == 200 ? true : false;
            }
            if(status) {
                var list = data.data;
                $(".enteredNum").html(0);
                if (list.length>0) {
                    var html =
                        '<tr>' +
                        '    <td width="25%">客户名称</td>' +
                        '    <td width="25%">客户代号</td>' +
                        '    <td width="25%">授权时间</td>' +
                        '    <td width="25%">操作</td>' +
                        '</tr>';
                    for(var h=0; h<list.length;h++){
                        html +=
                            '<tr>' +
                            '    <td>'+ list[h].name +'</td>' +
                            '    <td>'+ handleNull(list[h].code) +'</td>' +
                            '    <td>'+ new Date(list[h].enabledTime).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                            '    <td><span class="ty-color-blue" data-id="'+ list[h].id +'" onclick="recallPower($(this))">停权</span></td>' +
                            '</tr>';
                    }
                    $(".enteredNum").html(list.length);
                    $(".objectManagementList tbody").html(html);
                }
                bounce.show($("#objectManagementIndex"));
            }else{
                layer.msg("获取数据失败！");
            }
        }
    });
}
// creator: 李玉婷，2019-12-09 11:11:49，对象管理新增
function addObjectManagement() {
    $(".objectSelect tbody").html('');
    var userId = $("#addCollectionEntryObject").data('userId');
    var code = $("#addCollectionEntryObject").data('code');
    var url = "";
    if(code === "gb"){
        url = "../saleRtUser/getNoChoiceCustomerList.do";
        $("#addCollectionEntryObject .bonceHead span").html("增加回款录入对象");
    }else if(code === "gd"){
        url =  "../saleInterview/getNoChoiceCustomerList.do";
        $("#addCollectionEntryObject .bonceHead span").html("增加访谈记录对象");
    }
    $.ajax({
        "url": url,
        "data": {
            userId: userId
        },
        success: function (data) {
            var status = "";
            if(code === "gb"){
                status = data.success == '1' ? true : false ;
            }else if(code === "gd"){
                status = data.code == 200 ? true : false;
            }
            if(status) {
                var list = data.data;
                if (list.length > 0){
                    var str =
                        '<tr>' +
                        '    <td>客户名称</td>' +
                        '    <td>客户代号</td>' +
                        '    <td>选择</td>' +
                        '</tr>';
                    for (var a =0;a<list.length;a++){
                        str +=
                            '<tr data-id="'+ list[a].customerId +'">' +
                            '    <td>'+ list[a].name +'</td>' +
                            '    <td>'+ handleNull(list[a].code) +'</td>' +
                            '    <td><i class="fa fa-dot-circle-o" onclick="selectObject($(this))"></i></td>' +
                            '</tr>'
                    }
                    $(".objectSelect tbody").html(str);
                }
                bounce_Fixed.show($('#addCollectionEntryObject'));
                bounce_Fixed.everyTime('0.5s', 'addObject', function () {
                    var len = $(".objectSelect tbody tr .fa-circle").length;
                    var trLen = $(".objectSelect tbody tr").length -1;
                    if (len == trLen) {
                        $("#quickBtn").html('全不选').attr('onclick','selectNone()');
                    } else {
                        $("#quickBtn").html('全选').attr('onclick','selectAll()');
                    }
                    if (len > 0) {
                        $("#objectSelectSure").prop('disabled', false);
                    } else {
                        $("#objectSelectSure").prop('disabled', true);
                    }
                })
            }else{
                layer.msg("链接错误");
            }
        }
    })
}
// creator: 李玉婷，2019-12-10 09:44:38，全不选
function selectNone() {
    $(".objectSelect tbody tr").each(function(){
        $(this).find('.fa').removeClass('fa-circle').addClass('fa-dot-circle-o');
    });
}
// creator: 李玉婷，2019-12-10 09:53:30，全选
function selectAll() {
    $(".objectSelect tbody tr").each(function(){
        $(this).find('.fa').removeClass('fa-dot-circle-o').addClass('fa-circle');
    });
}
// creator: 李玉婷，2019-12-09 11:38:20，对象管理新增-勾选
function selectObject(obj) {
    if (obj.hasClass('fa-dot-circle-o')){
        obj.removeClass('fa-dot-circle-o').addClass('fa-circle');
    }else{
        obj.removeClass('fa-circle').addClass('fa-dot-circle-o');
    }
}
// creator: 李玉婷，2019-12-09 11:51:58，对象管理新增-确定
function objectSelectSure() {
    var userId = $("#addCollectionEntryObject").data('userId');
    var code = $("#addCollectionEntryObject").data('code');
    var url = "";
    if(code === "gb"){
        url = "../saleRtUser/addWarrantCustomer.do";
    }else if(code === "gd"){
        url = "../saleInterview/addWarrantCustomer.do";
    }
    var authorId = '';
    $(".objectSelect tbody tr").each(function () {
        var len = $(this).find('.fa-circle').length
        if (len > 0) {
            authorId += $(this).data('id') + ',';
        }
    })
    if (authorId != ''){
        var num = authorId.length - 1;
        authorId = authorId.substr(0,num);
    }
    $.ajax({
        "url": url ,
        "data": {
            'customers': authorId,
            'userId': userId
        },
        success: function (data) {
            var status = "";
            if(code === "gb"){
                status = data.success == '1' ? true : false ;
            }else if(code === "gd"){
                status = data.code == 200 ? true : false;
            }
            if(status) {
                bounce_Fixed.cancel();
                if(code === "gb"){
                    turnTab(2);
                    objectManagementListGet();
                }else if(code === "gd"){
                    turnTab(4);
                    objectManagementListGet();
                }

            }
        }
    })
}
// creator: 李玉婷，2019-12-09 14:08:41，停权
function recallPower(obj) {
    var id = obj.data('id');
    $("#recallPower").data('id', id);
    $("#recallPower").data('obj', obj);
    bounce_Fixed.show($("#recallPower"));
    var code = $("#addCollectionEntryObject").data('code');
    var tipStr = ""
    if(code === "gb"){
        tipStr = "确定不再允许该职工对该客户进行回款录入吗？";
    }else if(code === "gd"){
        tipStr = "确定不再允许该职工对该客户进行访谈记录吗？";
    }
    $("#recallPower .handleCenter").html(tipStr);
}
// creator: 李玉婷，2019-12-09 14:37:47，
function recallPowerSure() {
    var powerId = $("#recallPower").data('id');
    $.ajax({
        "url": "../saleRtUser/cancelWarrantCustomer.do",
        "data": { 'id': powerId },
        success: function (data) {
            var status = data.success;
            if(status == '1') {
                $("#recallPower").data('obj').parents('tr').remove();
                var lenth = $(".objectManagementList tbody tr").length -1;
                $(".enteredNum").html(lenth);
                var code = $("#addCollectionEntryObject").data('code');
                var num = 2;
                if(code == "gd"){ num = 4; }
                turnTab(num);
                bounce_Fixed.cancel();
            }
        }
    })
}
// creator: 李玉婷，2019-05-20 11:28:50，取消本权限确定
function limitChangeSure() {
    var sId = limitObj.attr("sId");
    var code = $("#limitChangeSure").data('code');
    $.ajax({
        "url": "../saleRtUser/deleteReturnUser.do",
        "data": {
            'code': code,
            'userId': sId
        },
        success: function (data) {
            var status = data.success;
            if(status == '1') {
                bounce.cancel();
                getPayBackList(code);
            }
        }
    })
}
// creator: 李玉婷，2019-05-20 11:29:13，切换员工的回款权限
function checkTab(obj){
    if(obj.attr('val') == '0'){
        obj.attr('val','1');
        obj.find("i").attr("class","fa fa-check-square-o");
    }else{
        obj.attr('val','0');
        obj.find("i").attr("class","fa fa-square-o");
    }
}

//--------------- 辅助方法 -----------------

//处理null
function handleNull(str) {
    var result = str == null || str == undefined || str == 'null' ? '':str;
    return result;
}
//creator:lyt date:2018/11/28 比较修改记录修改前后不同
function compareD(front,now){
    if(front == now){
        return '<span>' + handleNull(now) +' </span>'
    }else{
        return '<span class="redFlag">' + handleNull(now) + '</span>'
    }
}