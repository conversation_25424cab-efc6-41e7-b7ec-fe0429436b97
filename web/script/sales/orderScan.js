 $(function(){
	 showMainCon(1);
	 $("body").on("click",function () {
         $(".outerSn").next().slideUp('fast')
     });
     /*$(".outerSn + .ty-optionCon").on("click",".ty-option",function () {
         $(this).parent().prev().val($(this).text());
         $(this).parent().prev().attr("id",$(this).attr("id"));
     }).on("mouseover",".ty-option",function () {
         var self 	= $(this),
             outerSn = self.text(),
             goodInfo= self.data("goodInfo"),
             cusId  = $("#detail .cusName").attr("id");

         // 如果商品详情有缓存，从缓存获取，否则调接口获取
         if( goodInfo ){
             setGoodDetail(goodInfo);
         }else{
             $.ajax({
                 url: "getOuterSnLike.do",
                 data: {"cusId":cusId,"outerSn":outerSn},
                 type: "post",
                 dataType: "json",
                 success: function (data){
                     var goodInfo = data;
                     self.data("goodInfo",goodInfo);
                     setGoodDetail(goodInfo);
                 },
                 error: function () {
                     alert("系统错误，请重试！")
                 }
             })
         }
     }) ;*/
	 // 商品代号匹配
	 $(".outerSn").on("click keyup",function (e) {
		 $(this).next().show();
		 e.stopPropagation();
		 // 设置下拉列表（通过缓存数据设置）
		 setOuterSnOption();
	 });
	 //新增订单 - 下拉框条目点击事件（填入商品代号 关闭下拉框并显示与此相关其他信息）
	 $(".outerSn + .ty-optionCon").on("click",".ty-option",function () {
		 $(this).parent().prev().val($(this).find("span").text());
		 $(this).parent().prev().attr("id",$(this).attr("id"));
	 }).on("mouseover mouseout click",".ty-option",function () {
		 var goodInfo = $(this).find(".hd").text();
		 if(goodInfo){
			 goodInfo = JSON.parse(goodInfo);
			 setGoodDetail(goodInfo);
		 }
	 });
     getOrdList( 1 , 10) ;
	// Creator： 侯杏哲  2017-06-05  处理 - 新增商品按钮
	 $("#addGoods").on('click',function(){
		 $("#inputTip").html("") ;
		 bounce_Fixed2.show( $("#newGood") ) ;
		 editObj = { "trObj": null , "info":null ,  "orlInfo":null , "editType":"add" , "isNew":0 } ;
		 $("#goodsCon").find("input,select").val("") ;
         $("#newGood .outerSn").prop("disabled",false)
         $("#newGood .receiveAddress").prop("disabled",false)
		 orlOutSn = "@" ;
         bounce.everyTime('0.5s','pack',function(){
             var pack = $("#newGood .goodNum_stock").attr("pack");
             var goodNum = $("#newGood .goodNum").val();
             if(pack !== undefined && goodNum !== ""){
                 $("#newGood .goodNum_stock").val(Math.ceil(goodNum/parseInt(pack)));
             }else{
                 $("#newGood .goodNum_stock").val("");
             }
         });
	 });
	 //二级标签栏切换事件
	 $(".ty-secondTab li").on("click",function () {
		 $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
		 let inx = $(this).index();
		 $(".setList" + inx).show().siblings("table").hide();
	 });
	 //creator:lyt 2023/8/31 0002 下午 3:52 接受/不接受评审结果
	 $(".saleHandle").on('click','.fa',function(){
		 let icon = $(this).data("icon");
		 if ($(this).hasClass("fa-circle-o")) {
			 $(this).addClass("fa-circle").removeClass("fa-circle-o");
			 $(this).parents("div").siblings().find(".fa-circle").removeClass("fa-circle").addClass("fa-circle-o");
			 if (icon === 3) {
				 $(".adjustGoods").attr("onclick", "adjustGoods(1)").removeClass("ty-color-gray").addClass("blueLinkBtn");
			 } else {
				 $(".adjustGoods").removeAttr("onclick").removeClass("blueLinkBtn").addClass("ty-color-gray");
			 }
		 } else {
			 $(this).addClass("fa-circle-o").removeClass("fa-circle");
			 $(".adjustGoods").removeAttr("onclick").removeClass("blueLinkBtn").addClass("ty-color-gray");
		 }
	 }) ;
	 $(".checkWrap").on('click', ".fa",function() {
		 if ($(this).hasClass("fa-circle-o")) {
			 $(this).addClass("fa-circle").removeClass("fa-circle-o");
			 $(this).parent().siblings(".changeDot").find("span").addClass("fa-circle-o").removeClass("fa-circle");
		 }else {
			 $(this).addClass("fa-circle-o").removeClass("fa-circle");
		 }
	 });
	 getRate();
 }) ;
function getRate(){
	$.ajax({
		"url": "../invoiceSetting/getFinanceInvoiceSetting.do",
		async: false,
		success: function (res) {
			var str = '<option value="">--- 请选择 ---</option>';
			var list = res["financeInvoiceSettings"];
			if (list && list.length > 0) {
				for (var i = 0; i < list.length; i++) {
					if (list[i]["category"] == '1'){
						var rate = list[i]["enableTaxTate"];
						var rates = rate.split(",");
						if (rates && rates.length >0){
							for (var r = 0; r < rates.length; r++) {
								var selectedStr = "";
								str += "<option  value='" + rates[r] + "'>" + rates[r] + "%</option>";
							}
						}
					}
				}
			}
			$(".rate").html(str);
		}
	});
}
/* creator : 侯杏哲 2017-06-01 获取要货评审的订单列表 */
function getOrdList( cur , per ){
	$.ajax({
		//url:"../sr/toPmIndex.do" ,
		url:"/orders/review/orderList" ,
		//data:{ "curPage": cur , "per": per  } ,
		type:"post" ,
		dataType:"json" ,
		timeout: 10000,
		success:function( data ){
			//var code = data["code"] ;
			//var curPage = data["curPage"] ;
			//var totalPage = data["totalPage"] ;
			var list = data || [] ;
			var str = "" ,html1 = `` ,html2 = ``;
			/*if(list && list.length > 0 ){
				for(var i = 0 ; i < list.length ; i++ ){
					// is_scheduled '评审状态,0和null-未评审,1-评审中,2-评审完(要货计划新增)' , 即此参数为 0.null、2 “评审”按钮高亮 ； 1 “处理”按钮高亮
					str += "<tr>" +
						"<td>"+ formatTime( list[i]["sign_date"] ) +"</td>" +
						"<td>"+ (list[i]["sn"] || "") +"</td>" +
						"<td>"+ list[i]["customer_name"] +"</td>" +
						"<td>"+ formatTime( list[i]["create_date"] ) +"</td>" ;
					var is_scheduled = list[i]["is_scheduled"] ;
					if( is_scheduled == null ||is_scheduled == 9 || is_scheduled == 2 || is_scheduled == 0 ){
						str += "<td><span class='ty-color-gray'>处理</span></td> </tr>" ;
					}else{
						str += "<td><span onclick='chargeOrd("+ list[i]["id"] +" , $(this) )' class='ty-color-green'>处理</span></td> </tr>" ;
					}
				}
			}
			$("#setList").html( str ) ;*/
			//setPage( $("#ye_setList") , curPage ,  totalPage , "orderProductionSet"  );
			for(var i = 0 ; i < list.length ; i++ ){
				// phase 阶段0-未开始,1-生产评审,2-销售评审,3-完成
				var oper =
					"<span class='ty-color-blue' onclick='chargeOrdInit("+ list[i]["id"] +" , $(this) )'>订单修改</span>" +
					"<span class='ty-color-gray' onclick='chargeHistory("+ list[i]["id"] +" , $(this) )'>评审记录</span>" +
					"<span class='ty-color-gray' onclick='stopOrd("+ list[i]["id"] +" , $(this) )'>终止评审</span>" +
					"<span class='hd'>"+ JSON.stringify(list[i]) +"</span></td></tr>";
				str = "<tr>" +
					"<td>"+ formatTime( list[i]["sign_date"] ) +"</td>" +
					"<td>"+ list[i]["customer_name"] +"</td>" +
					"<td>"+ (list[i]["sn"] || "") +"</td>" +
					"<td>"+ (list[i]["reviewer_name"] || "") +"</td>" +
					"<td>"+ formatTime( list[i]["create_date"] ) +"</td>"+
					"<td>";
				if (list[i].phase === 1) {
					let btn = "<span class='ty-color-blue' onclick='confirmOrd("+ list[i]["id"] +" , $(this) )'>去评审</span>";
					html1 += str + btn + oper;
				} else if (list[i].phase === 0 || list[i].phase === 2) {
					html2 += str + oper;
				}
			}
			$(".setList0 tbody").html( html1 ) ;
			$(".setList1 tbody").html( html2 ) ;
		} ,
		error:function(){
			loading.close() ;
			bounce.show( $("#tip") ) ; $("#tipMs").html("链接错误") ;
		}
	});
}
function confirmOrd( ordID , _this ){
	if(hasAuthority(0) === true){
		$("#tipMs").html("您没有此权限！");
		bounce.show($("#tip"));
		return false ;
	}
	if( !ordID ){
		bounce.show( $("#tip") ) ;
		$("#tipMs").html("获取订单ID失败") ;
		return false ;
	}
	let verNo = 0;
	let ordDetail = JSON.parse(_this.siblings(".hd").html());	// 订单明细
	ordObj["spanObj"] = _this ;
	ordObj["orderId"] = ordID ;
	ordObj["cusId"] = ordDetail.customer_id ;
	ordObj["invoiceRequire"] = ordDetail.invoice_require ;
	bounce.show( $("#saleOrderReview") );
	getAddressCon();
	//设置弹窗头部信息
	$("#saleOrderReview .hd").html("");
	$("#saleOrderReview .adjustGoods").removeAttr("onclick").removeClass("blueLinkBtn").addClass("ty-color-gray");
	$("#saleOrderReview .fa").addClass("fa-circle-o").removeClass("fa-circle");
	$("#saleOrderReview .cusName").html( ordDetail["customer_name"]);
	$("#saleOrderReview .ordSn").html(ordDetail["sn"]) ;
	$("#saleOrderReview .ordSignDate").html(formatTime(formatTime(ordDetail["sign_date"]))) ;
	$("#saleOrderReview .ordCreator").html(ordDetail["create_name"]) ;
	$("#saleOrderReview .ordCreatDate").html( new Date(ordDetail["create_date"]).format("yyyy-MM-dd hh:mm:ss"));
	$.ajax({
		type: "get",
		url: "/orders/review/reviewDetails?orderId="+ ordID ,
		success: function(res){
			var items = res["items"]["data"] ;
			var order = res["orderBase"][0] ;
			let onTime = 0,unTime = 0;
			ordObj["addressId"] = order.address_id;
			ordObj["reviewer_name"] = order["principal_name"] || "";
			$("#saleOrderReview #ordSums").html(items.length) ;
			if( !items ){
				layer.msg("访问失败") ;  return false ;
			}
			$("#saleOrderReview .ordprincipal").html(order["principal_name"]) ;
			$("#saleOrderReview .cusCode").html(order["code"]) ;
			$("#adjustGoodsScan .saleOrd").html( new Date(order.create_date).format("yyyy-MM-dd hh:mm:ss") ) ;
			$("#adjustGoodsScan .pudLastReview").html(  res.reviewName+ " " + new Date(res.produceReviewDate).format("yyyy-MM-dd hh:mm:ss")  ) ;
			$(".ordGoodsList").html(JSON.stringify(items));
			if( items && items.length > 0 ){
				for(var i = 0 ; i < items.length ; i++ ){  // 循环每个商品组
					let isScheduled = 1;
					let size = items[i].reviews.length;
					if (size > 0) {
						for (let b=0;b<size;b++){
							if(Number(items[i].reviews[b].version_no) > verNo) {
								verNo = Number(items[i].reviews[b].version_no)
							}
						}
					}
					if (size > 0) {
						isScheduled = items[i].reviews[size-1].isScheduled;
					}
					if (isScheduled){
						onTime++;
					} else {
						unTime++;
					}
				}
			}
			ordObj["reviewsVersion"] = verNo;
			$("#saleOrderReview #abledSums").html(onTime) ;
			$("#saleOrderReview #unabledSums").html(unTime) ;
			$("#saleOrderReview #byReviewed").html(res.reviewName + "于" + new Date(res.produceReviewDate).format("yyyy-MM-dd hh:mm:ss")) ;

		}
	});
	bounce.everyTime('0.5s','saleOrderReview',function () {
		if($(".saleHandle .fa-circle").length > 0){
			$("#saleOrderReviewOk").prop("disabled",false);
		}else{
			$("#saleOrderReviewOk").prop("disabled",true);
		}
	});
}
//creator:lyt 2023/9/18 0018 下午 12:04 订单评审-查看
 function goodsScanList(type){
	let html = ``;
	 let items = JSON.parse($(".ordGoodsList").html());
	 for(var i = 0 ; i < items.length ; i++ ) {  // 循环每个商品组
		 let isScheduled = 1;
		 if (items[i].reviews && items[i].reviews.length > 0) {
			 isScheduled = items[i].reviews[items[i].reviews.length-1].isScheduled;
		 }
		 if ((type === 1 && isScheduled) || (type === 0 && !isScheduled) || type === 2) {
			 html +=
				 `<tr>
                            <td>${items[i].outer_sn}</td>
                            <td>${items[i].outer_name}</td>
                            <td>${items[i].unit}</td>
                            <td>${items[i].amount}</td>
                            <td>${new Date(items[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td>${items[i].delivery_address}</td>
                            <td>${items[i].reviews.length > 0 ? items[i].reviews[items[i].reviews.length-1].scheduledAmount : ''}</td>
                            <td>${items[i].reviews.length > 0 ? formatTime(items[i].reviews[items[i].reviews.length-1].surplusDate) : ''}</td>
                            </tr>
                        `;
		 }
	 }
	 $(".goodsScanList tbody").html( html ) ;
	 bounce_Fixed.show( $("#goodsListScan") );
}

function saleOrderReviewOk(){
	let len = $("#saleOrderReview .fa-circle").length;
	if (len > 0){
		let state = $("#saleOrderReview .fa-circle").data("icon")
		let data = {"orderId": ordObj["orderId"], "state": state} //state=2接受，3不接受
		if (state === 3) {
			let ship = $(".adjustGoodsData").html()
			if (ship !== "") {
				ship = JSON.parse(ship)
				adjustGoodsSure(ship, 1);
			} else {
				layer.msg("请调整要货需求");
				return false;
			}
		} else {
			data.reviewVersion = ordObj["reviewsVersion"];
			saleHandle(data);
		}
	}
}
function saleHandle(data){
	$.ajax({
		type: "POST",
		url: "../orders/review/saleHandle",
		data: data  ,//将对象序列化成JSON字符串
		success: function(res){
			if(res.code === 500) {
				layer.msg(res.msg);
			} else {
				bounce.cancel();
				if(data.state === 2) getOrdList( 1 , 10);
			}
		}
	});
}
/* creator ：侯杏哲 2017-06-01 点击“处理”订单  */
var ordObj = {} ;
function chargeOrd( ordID , _this ){
	if(hasAuthority(0) === true){
		$("#tipMs").html("您没有此权限！");
		bounce.show($("#tip"));
		return false ;
	}
	if( !ordID ){
	 	bounce.show( $("#tip") ) ;
	 	$("#tipMs").html("获取订单ID失败") ;
	 	return false ;
	}

    bounce.show( $("#detail") );
	ordObj["spanObj"] = _this ;
	var order = JSON.parse(_this.siblings(".hd").html());	// 订单明细
	$.ajax({
		 url:"../sr/toProductionManage.do" ,
		data:{"orderId": ordID  } ,
		 type:"post" ,
		 dataType:"json" ,
		 success:function( res ){
			 //var order = res["order"];	// 订单明细
			 var list =  res["items"]["data"]; // 商品明细，第一层map是评审记录，第二层data是有修改历史的记录
			 if( !list ){
				 bounce.show( $("#tip") ) ; $("#tipMs").html("获取数据不全，请刷新重试！") ; return false ; 
			 }

			 //ordObj["order"] = order ;
			 ordObj["goodsList"] = list ;

			 //设置弹窗头部信息
             $("#detail .cusName").html( order["customerName"]).attr("id" , order["customer_"]);
			 $("#detail .cusCode").html(order["cusCode"]) ;
			 $("#detail .ordSn").html(order["sn"]) ;
			 $("#detail .ordprincipal").html(order["sn"]) ;
			 $("#detail .ordSignDate").html(formatTime(order["signDate"])) ;
			 $("#detail .ordCreator").html(order["createName"]) ;
			 $("#detail .ordCreatDate").html( new Date(order["createDate"]).format("yyyy-MM-dd hh:mm:ss"));

			 //设置商品编辑弹窗地址
             setAddressCon();

			 //设置商品列表
			 $("#goodsInfo").html("");
			 if( list && list.length > 0 ){
				 for(var i = 0 ; i < list.length ; i++ ){
					 setChargeStr({ "itemInfo":list[i] , "tbl": $("#goodsInfo") , "i": i  }) ;
				 }
			 }

			 //表单验证
			 bounce.everyTime('0.2s','charge',function () {
                // 判断确定能不能点
                var isChargeSure = true ;  // 默认 确定按钮可以点
                $("#goodsInfo .ty-color-blue").each(function(){

                    var txt = $(this).parent().siblings(":eq(4)").html(); //能否按时到货

					 //有一个不是 '是' ,就不能点确定按钮
                    if( $.trim(txt) !== '能' ){
                        isChargeSure = false ;
                    }
                }) ;
                if( isChargeSure ){
                    $("#sure").prop("disabled",false)
                    $("#charge").prop("disabled",true)
                }else{
                    $("#sure").prop("disabled",true)
                    $("#charge").prop("disabled",false)
                }
             })

			 setRoleInit("roleCon_update") ;

		 } ,
		 error:function(){
			  bounce.show( $("#tip") ) ; $("#tipMs").html("链接失败，请刷新重试！") ;
		 }
	 });
 }
function setChargeStr( config ){
	var itemInfo = config["itemInfo"] ;	//商品信息
	var tbl = config["tbl"] ;
	var i = config["i"] ;
	var no = i+1 ;
	var map = itemInfo["map"] ;  // 存的
	var data_1 = map && map["data"] ;   // 所有评审的数据
	var data_history = map["itemHistories"] && map["itemHistories"]["data"] ;   // 所有订单修改
	var list = [] ;
	if(data_history){
		var lastUpdateTime = data_history[data_history.length -1]["update_date"] ; // 最后修改的时间
		if(data_1 && data_1.length > 0){
			for(var j = 0 ; j < data_1.length ; j++ ){
				if( lastUpdateTime < data_1[j]["create_date"] ){
					list.push( data_1[j] ) ;
				}
			}
		}
	}else{
		list = data_1 ;
	}

    //用来显示商品编辑弹窗
    var goodDetailItem = {
        "slOuterSn" : itemInfo.slOuterSn,
        "slInnerSn" : itemInfo.slInnerSn,
        "slInnerSnName" : itemInfo.slInnerSnName,
        "slOutterSnName" : itemInfo.slOutterSnName,
        "taxRate" : itemInfo.taxRate,
        "slUnit" : itemInfo.slUnit,
        "slUnitPrice" : itemInfo.slUnitPrice,
        "slUnitPriceNoTax" : itemInfo.slUnitPriceNoTax,
        "hasInvoice" : itemInfo.hasInvoice,
        "invoiceCategory" : itemInfo.invoiceCategory,
        "currentStock" : itemInfo.currentStock,
        "availableStock" : itemInfo.availableStock,
        "minimumiStock" : itemInfo.minimumiStock,
        "deliveryAddress" : itemInfo.deliveryAddress,
        "deliveryAddressId" : itemInfo.deliveryAddressId,
        "pack" : itemInfo.packages
    };

	 // 找到最后两条要显示的评审，修改记录不用管

	 if(list.length > 0 ){
         var len = list.length ;
		 for(var w = (len > 1 ? len - 2 : 0) ; w < len ; w++ ){
			 var delivery_date 		= formatTime( list[w]["delivery_date"] ),	// 要求到货日期
				 surplus_date 		= formatTime( list[w]["surplus_date"] ),	// 剩余数量到货日期
				 item_delivery_date = formatTime( list[w]["item_delivery_date"] ),
				 slOuterSn 			= list[w]["outer_sn"]  ,
				 id 				= list[w]["id"] ,
				 itemAmount 		= list[w]["itemAmount"] ,
				 scheduled_amount	= list[w]["scheduled_amount"] ,
				 memo				= list[w]["memo"] ;

			 goodDetailItem["amount"] = itemAmount;
			 goodDetailItem["deliveryDate"] = delivery_date;
			 goodDetailItem["id"] = id;

			 var str = 	"<tr info ='"+ JSON.stringify( itemInfo ) +"'>" +
							"<td>" +
								"<div>"+ no + "</div><div class='hd'>"+ JSON.stringify( goodDetailItem ) +"</div>" +
							"</td>" ;
			 if( w === len-2 || w === 0  ){
				 str += 	"<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>" +
								"<div>"+ slOuterSn+"</div>" +
							"</td>" ;
			 }else{
				 str += 	"<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" ;
			 }
			 str += 		"<td>" + itemAmount + "</td>" +
							"<td>" + item_delivery_date + "</td>" ;
			 if( (surplus_date && scheduled_amount) || ( surplus_date && scheduled_amount == 0) ){
				 str += 	"<td>否</td>" +
							"<td>"+ scheduled_amount +"</td>" +
							"<td>"+ surplus_date +"</td>" ;
			 }else{
				 str += 	"<td>能</td>" +
							"<td>— —</td>" +
							"<td>— —</td>" ;
			 }
			 var handleData 		= list[w]["data"] && list[w]["data"][0];
			 if( handleData ){
				var create_date 	= formatTime(handleData.create_date),
					reviewer_name 	= handleData.reviewer_name;
				 str += 	"<td>"+ create_date +"</td>"  +
							"<td>"+ reviewer_name +"</td>" ;
			 }else{
				 str += 	"<td>— —</td>"  +
							"<td>— —</td>" ;
			 }
			 str += 		"<td onmouseover='showReviewMess($(this))' onmouseout='hideshowReviewMess($(this))'>"
								+ memo +
								"<span class='hd'>"+ id +"</span>" +
							"</td>" ;
			 if( w == len-1  ){
				 str += 	"<td>" +
								"<span class='ty-color-blue' onclick='changeGoods($(this))'>修改</span></td>" +
						"</tr>" ;
			 }else{
				 str += "<td><span class='ty-color-gray' >修改</span></td>" +
					 "</tr>" ;
			 }
			 tbl.append( str ) ;
		 }
	 }else{
		 var d1Len = data_1.length ;
		 var useData = data_1[d1Len-1] ;
		 var str = "<tr info ='"+ JSON.stringify( itemInfo ) +"'>" +
			 "<td><div>"+ no + "</div><div class='hd'>"+ JSON.stringify( useData ) +"</div></td>" +
			 "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'><div>"+ itemInfo["slOuterSn"] +"</div></td>" ;
		 str += "<td>"+ useData["itemAmount"] +"</td>" +
			 "<td>"+ useData["delivery_date"] +"</td>" ;
		 str += "<td>— —</td><td>— —</td><td>— —</td>" ;
		 str += "<td></td>"  +
			 "<td></td>" +
			 "<td></td>" ;
		 str += "<td><span class='ty-color-blue' onclick='changeGoods($(this))'>修改</span></td>" +
			 "</tr>" ;
		 tbl.append( str ) ;
	 }

 }
/* Creator : 侯杏哲 2017-07-28 处理页面 鼠标移动到备注  */
function showReviewMess( _this ){
	var info = _this.parent().attr("info") ;
	var reviewID = _this.children(".hd").html() ;
	info = JSON.parse(info);
	var offsetObj = _this.offset();
	var leftObj =offsetObj.left;
	var topObj = offsetObj.top ;
	$("#tipCon").animate({ left: leftObj+ "px" , top:topObj+"px"  }, 10).show();
	var data_1 = info["map"] && info["map"]["data"] ;
	var str = "<span class='tipitemCon'>暂无处理信息</span>" ;
	if( data_1 && data_1.length > 0 ){
	 for(var i = 0 ; i < data_1.length ; i++ ){
		 var id = data_1[i]["id"] ;
		 if(reviewID == id){
			 str = "<span class='tipitemCon'> 评审人："+ ( data_1[i]["reviewer_name"]||"" ) +"</span><span class='tipitemCon'> 评审时间："+ formatTime(data_1[i]["review_date"]) +"</span>" ;
			 break ;
		 }
	 }
	}
	$("#tipitem").html(str);
}
function hideshowReviewMess(){
	 $("#tipCon").hide().offset({ left:0 , top:0 });
}
// Creator : 侯杏哲 2017-06-05 处理 - 修改（商品）
var editObj = { "trObj": null , "info":null ,  "orlInfo":null , "editType":"" , "isNew":0 } ;
// trObj - 当前操作的tr行 ，info - 商品详情 ，orlInfo - 如果修改的已经改过的，存最初版本的信息 ， editType - 标记是新增("add")还是修改("update") ， isNew - 标记修改的是原订单的还是修改过的（0：原来的，1：修改过的）
function changeGoods( _this , isNew ){
	//清空提示
	$("#inputTip").html("") ;
	$("#newGood .outerSn").prop("disabled",true)
	$("#newGood .receiveAddress").prop("disabled",true)

	//获取要改变的商品数据
    var info = _this.parent().siblings(":eq(0)").children("div.hd").html() ;
    var trObj = _this.parent().parent() ;
    info = JSON.parse( info ) ;
    var price = 0 ;
    if(info["hasInvoice"] == 1){
        if(info["invoiceCategory"] == 1){ // 增专
            price = info["slUnitPriceNoTax"] * (1 + info.taxRate/100);
        }else{
            price = info["slUnitPrice"] ; // 增普和其他
        }
    }else{
        price = info["slUnitPriceNoTax"] ;
    }
    //对应赋值
    $("#newGood .outerSn").val(info.slOuterSn);
    $("#newGood .outerName").val(info.slOutterSnName);
    $("#newGood .cInnerSn").val(info.slInnerSn);
    $("#newGood .cInnerSnName").val(info.slInnerSnName);
    $("#newGood .unitPrice").val(price);
    $("#newGood .unit").val(info.slUnit);
    $("#newGood .current_stock").val(info.currentStock);
    $("#newGood .canchoose_stock").val(info.availableStock);
    $("#newGood .minimumi_stock").val(info.minimumiStock);
    $("#newGood .goodNum").val(info.amount);
    $("#newGood #dateGoods").val(info.deliveryDate);
    $("#newGood .goodNum_stock").attr("pack",info.pack);
    $("#newGood .receiveAddress").val(info.deliveryAddressId);

    bounce_Fixed2.show( $("#newGood") ) ;
    bounce.everyTime('0.5s','pack',function(){
        var pack = $("#newGood .goodNum_stock").attr("pack");
        var goodNum = $("#newGood .goodNum").val();
        if(pack !== undefined && goodNum !== ""){
            $("#newGood .goodNum_stock").val(Math.ceil(goodNum/parseInt(pack)));
        }else{
            $("#newGood .goodNum_stock").val("");
        }
    });
	editObj["trObj"] =  trObj ;
	editObj["info"] =  info ;
	editObj["editType"] =  "update" ;
	if(isNew === 1 || isNew === 2 ){
		editObj["isNew"] = isNew ;
		var orlInfo = trObj.prev().children(":eq(0)").children("div.hd").html() ;
		orlInfo = JSON.parse(orlInfo) ;
		editObj["orlInfo"] = orlInfo ;
	}else{
		editObj["isNew"] = 0 ;
	}
}
// Creator: 侯杏哲 2017-06-05 确定新增或修改商品
function submitGoods1(){
	var type 		= editObj["editType"] ;		// 确定或修改
	var amount 		= $(".goodNum").val() ;		// 商品数量
	var dateGoods 	= $("#dateGoods").val() ;	// 要求到货日期
    var str 		= "" ;

    //如果是更新商品
	if(type === "update"){
	 	var trObj   = editObj["trObj"] ;
	 	var info    = editObj["info"] ;
	 	var orlInfo = editObj["orlInfo"] ;


	 	info["amount"] = amount ;
	 	info["delivery_date"] = dateGoods ;
	 	if(editObj["isNew"] == 1){
			 trObj.children(":eq(2)").html(amount) ;
			 trObj.children(":eq(3)").html(dateGoods) ;
			 trObj.children(":eq(0)").children("div.hd").html( JSON.stringify( info) );
	 	}else{
			 var no = trObj.children(":eq(0)").children("div:eq(0)").html();
			 str = "<tr>" +
				 "<td><div>"+ no + "</div><div class='hd'>"+ JSON.stringify(info) +"</div></td>" +
				 "<td> </td>" +
				 "<td>"+ amount +"</td>" +
				 "<td>"+ dateGoods +"</td>"+
				 "<td></td><td></td><td></td><td></td><td></td><td></td>" +
				 "<td><span class='ty-color-blue' eType='1' onclick='changeGoods($(this) , 1)'>修改</span></td>" +
				 "</tr>" ;
			 trObj.after(str);
			 trObj.children(":last").html("<span class='ty-color-gray'>修改</span>");
	 	}
	}else if(type === "add"){
	 	if( $(".goodNum").val() === ''){ $("#inputTip").html("数量不可以为空！") ;  return false ;     }
	 	if( $(".receiveAddress").val() === ''){ $("#inputTip").html("收货地点不可以为空！") ;  return false ;     }
	 	var no =  $("#goodsInfo").children(":last").children(":eq(0)").children(":eq(0)").html();
	 	no = parseInt( no ) + 1 ;


	 	var id 				= $("#newGood .outerSn").attr("id");
	 	var outerSn 		= $("#newGood .outerSn").val();
	 	var outerName 		= $("#newGood .outerName").val();
	 	var cInnerSn 		= $("#newGood .cInnerSn").val();
	 	var cInnerSnName	= $("#newGood .cInnerSnName").val();
	 	var unitPrice 		= $("#newGood .unitPrice").val();
	 	var unitPriceData 		= $("#newGood .unitPrice").data('price');
	 	var unit 			= $("#newGood .unit").val();
	 	var currentStock 	= $("#newGood .current_stock").val();
	 	var availableStock 	= $("#newGood .canchoose_stock").val();
	 	var minimumiStock 	= $("#newGood .minimumi_stock").val();
	 	var goodNum 		= $("#newGood .goodNum").val();
	 	var goodNum_stock 	= $("#newGood .goodNum_stock").val();
	 	var requireDate 	= $("#newGood #dateGoods").val();
	 	var receiveAddress 	= $("#newGood .receiveAddress").find("option:selected").html();
	 	var receiveAddressId= $("#newGood .receiveAddress").val();

	 	var goodsInfo = {
     	    "isNew" : editObj["isNew"],
     	    "amount" : goodNum,
     	    "id" : id,	//评审id
     	    "slOuterSn" : outerSn,
     	    "slInnerSn" : cInnerSn,
     	    "deliveryDate" : requireDate,
     	    "slInnerSnName" : cInnerSnName,
     	    "slOutterSnName" : outerName,
            "taxRate" : unitPriceData.tax_rate,
            "slUnitPriceNoTax" : unitPriceData.unit_price_notax,
            "hasInvoice" : unitPriceData.has_invoice,
            "invoiceCategory" : unitPriceData.invoice_category,
     	    "slUnit" : unit,
     	    "slUnitPrice" : unitPrice,
     	    "num" : no,
     	    "currentStock" : currentStock,
     	    "availableStock" : availableStock,
     	    "minimumiStock" : minimumiStock,
     	    "deliveryAddress" : receiveAddress,
     	    "deliveryAddressId" : receiveAddressId
	 	} ;
	 	str = 	"<tr>" +
					"<td><div>"+ no + "</div><div class='hd'>"+ JSON.stringify(goodsInfo) +"</div></td>" +
					"<td><div>"+ outerSn +"</div></td>" +
					"<td>"+ amount +"</td>" +
					"<td>"+ requireDate +"</td>"+
					"<td>— —</td>" +
					"<td>— —</td>" +
					"<td>— —</td>" +
					"<td>— —</td>" +
					"<td>— —</td>" +
					"<td></td>" +
					"<td><span class='ty-color-blue' eType='1' onclick='changeGoods($(this) , 1)'>修改</span></td>" +
				"</tr>" ;
	 	$("#goodsInfo").append(str) ;
	}
    bounce_Fixed.cancel() ;
}
 // creator : hxz 2018-07-18  新增订单 - 相关订购信息
 // creator: hxz  2018-07-18  初始化 三级弹框 - fixed2
 var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
 bounce_Fixed2.show($("#newGood"));
 bounce_Fixed2.cancel();
 var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
 bounce_Fixed3.show($("#linkOrd"));
 bounce_Fixed3.cancel();
 function linkOrds(obj) {
     bounce_Fixed3.show($("#linkOrd"));
     $("#linkOrdTb").find("tr:gt(0)").remove();
     var cInnerSn = $(".cInnerSn").val() ;
     var pConObj = obj.parents(".bonceContainer") ;
     var cInnerSnName = pConObj.find(".cInnerSnName").val() ;
     var currentStock = pConObj.find(".current_stock").val() ;
     var unit = pConObj.find(".unit").val() ;
     var slOrdersId = ordObj["order"]["id"] ; 		// 订单ID
     $.ajax({
         "url": "../sale/getOccOrder.do",
         "data": {"innerSn": cInnerSn , "orderId": slOrdersId },
         success: function (res) {
             var list = res["data"], str = "";
             if (list && list.length > 0) {
                 for (var i = 0; i < list.length; i++) {
                     str += "<tr>" +
                         "     <td>"+ (i+1) +"</td>" +
                         "     <td>"+ (list[i]["sn"] ||"") +"</td>" +
                         "     <td>"+ (list[i]["create_name"] ||"") +"</td>" +
                         "     <td>"+ (list[i]["amount"] ||"") +"</td>" +
                         "     <td>"+ formatTime(list[i]["earliest_delivery_date"]) +"</td>" +
                         "     <td>"+ (list[i]["customer_name"] ||"") +"</td>" +
                         " </tr>";
                 }
                 $("#linkOrdTb").append(str);
                 $(".ysGs").show(); $(".noGs").hide();
                 $("#linkOrdName").html(cInnerSnName); $("#linkOrdStock").html(currentStock) ;
                 $("#linkOrdUnit").html(unit);
             }else{
                 $(".ysGs").hide(); $(".noGs").show();
             }
         }
     }) ;
 }
// Creator： 侯杏哲  处理 - 新增商品 - 商品代号输入框点击事件（显隐下拉框、为空时设置下拉框内容）
var orlOutSn = "" ; //  标识上次搜索的商品代号，重复时不用再次搜索
// creator: 侯杏哲，2017-06-07 11:33:56，处理记录修改提交 - 确定
function charge(){
	 var goodsList  = [] ,  // 新增商品的数组
		 reviewList = [] ;  // 修改的商品的数组
	 var cusId 		= ordObj["order"]["customer_"] ;// 客户id
	 var slOrdersId = ordObj["order"]["id"] ; 		// 订单ID
    var orderAmount = 0;
    $("#goodsInfo").find(".ty-color-blue").each(function(){
		 var eType = $(this).attr("eType") ;
		 var info = $(this).parent().siblings(":eq(0)").children(".hd").html(); // 商品所有详情，包括新增和修改的
		 info = JSON.parse( info ) ;
        if(info["hasInvoice"] == 1){
            if(info["invoiceCategory"] == 1){ // 增专
                orderAmount += info["slUnitPriceNoTax"] * (1 + info.taxRate/100) * info["amount"];
            }else{
                orderAmount += info["slUnitPrice"] * info["amount"] ; // 增普和其他
            }
        }else{
            orderAmount += info["slUnitPriceNoTax"] * info["amount"] ;
        }
		 if( info["isNew"] == 0 ){
             // 新增的商品
			 var pdProductId = info["id"],  // 销售对照ID
                 deliveryDate = info["deliveryDate"] , // 要求到货日期
                 amount = info["amount"] , // 数量
                 deliveryAddress = info["deliveryAddress"] , // 地址
                 deliveryAddressId = info["deliveryAddressId"] ; // 地址id

			 goodsList.push({
				 "pdProductId":pdProductId ,
				 "deliveryDate":deliveryDate ,
				 "amount":Number(amount) ,
				 "slOrdersId":slOrdersId ,
				 "cusId":Number(cusId),
				 "deliveryAddress":deliveryAddress,
				 "deliveryAddressId":Number(deliveryAddressId)
			 }) ;
		 }else{ // 修改的商品
			 if(eType == 1){
				 var memo = info["memo"] ,
				 	 id = info["id"] ,
                 	 delivery_date = info["delivery_date"] ,
					 amount = info["amount"] ;

				 reviewList.push({
					 "memo": memo,
					 "itemReviewId":Number(id) ,
					 "date":delivery_date ,
					 "amount": (amount)
				 }) ;
			 }
		 }
	 });
	 var data = {
         "reviewList":JSON.stringify(reviewList) ,
		 "goodsList":JSON.stringify(goodsList) ,
		 "slOrdersId": slOrdersId,
		 "ordersAmount": orderAmount
	 };

	 $.ajax({
		url:"../sr/manageEdit.do",
		data : data ,
	 	type:"post" ,
		 // contentType : 'application/json;charset=utf-8',
		success:function( res ){
			var code = res["code"] ;
			var data = res["data"] ;
			if( code == 200 && data =="成功！" ){
				bounce.cancel();
				layer.msg("处理成功！")
				ordObj["spanObj"].attr("class" , "ty-color-gray").removeAttr("onclick") ;
			}else{
				bounce.show( $("#tip") ) ;
				$("#tipMs").html( "处理失败！" ) ;
			}
		} ,
		error:function(){
            bounce.show( $("#tip") ) ;
            $("#tipMs").html( "链接错误，请刷新重试！" ) ;
		 }
	 })

 }
// creator: 侯杏哲，2017-06-07 11:33:56，处理记录确认无误 - 确定
function sure(){
	var slOrdersId =  ordObj["order"]["id"] ; // 订单ID
	var list =  ordObj["goodsList"] ; // 商品
    var orderAmount = 0;
    for (var l=0; l<list.length;l++) {
        if(list[l]["hasInvoice"] == 1){
            if(list[l]["invoiceCategory"] == 1){ // 增专
                orderAmount += list[l]["slUnitPriceNoTax"] * (1 + list[l].taxRate/100) * list[l]["amount"];
            }else{
                orderAmount += list[l]["slUnitPrice"] * list[l]["amount"] ; // 增普和其他
            }
        }else{
            orderAmount += list[l]["slUnitPriceNoTax"] * list[l]["amount"] ;
        }
	}
    orderAmount = orderAmount.toFixed(2);
	 $.ajax({
		 url:"../sr/makeSure.do" ,
		 data:{ "orderId" : slOrdersId, 'ordersAmount': orderAmount } ,
		 type:"post" ,
		 dataType:"json" ,
		 success:function( res ){
			 var code = res["code"] ; 
			 if( code == 200 ){
				 $("#tjTipMs").html("操作成功！") ;  bounce.show( $("#tjTip") ) ;
				 ordObj["spanObj"].parent().parent().remove() ; 
			 }else{
				 $("#tipMs").html("操作失败，请刷新重试 ！") ;  bounce.show( $("#tip") ) ;
			 }
		 } ,
		 error:function(){
			 $("#tipMs").html("操作失败，请刷新重试 ！") ;  bounce.show( $("#tip") ) ;
		 }
	 })
 }
// creator: 张旭博，2018-05-30 10:10:55，设置收货地址
function setAddressCon() {
     var customerID =  $("#detail .cusName").attr("id");
     $.ajax({
         url:"../sales/getAddressListByCondition.do",
         data:{"cusId":customerID},
         type:"post",
         dataType:"json",
         success:function (data) {
             var addrList = data["data"];
             var addrListStr = '<option value="">------- 请选择收货地点 -------</option>';
             for(var i=0;i<addrList.length;i++){
                 addrListStr += '<option value="'+addrList[i].id+'">'+addrList[i].address+'</option>';
             }
             $(".receiveAddress").html(addrListStr);
         }

     })
 }
 // creator: 张旭博，2018-05-30 10:09:04，设置商品代号输入框下拉列表
 function setOuterCon(cusId, invoiceRequire) {
	 if(invoiceRequire == '3'){ invoiceRequire = 4 }
	 $.ajax({
		 url: "../sales/getPdCustomerProductOuterSn.do",
		 data: {"cusId":cusId, 'invoiceRequire': invoiceRequire } ,
		 success: function (data){
			 var pCPOS = data["pdCustomerProductOuterSn"]; // 专属商品
			 var productMap = data["productMap"]; // 通用型商品
			 if((!pCPOS  && !productMap) || ((pCPOS &&pCPOS.length<1) &&(productMap && productMap.length<1)) ){
				 layer.msg("此客户下没有商品，无法新增订单！")
				 $(".newGood").data("goodInfo",'');
				 $(".newGoodCommon").data("goodInfo",'');
			 }else{
				 if(pCPOS && pCPOS.length>0){
					 $(".newGood").data("goodInfo",pCPOS);
				 }else{
					 $(".newGood").data("goodInfo",'');
				 }
				 if( productMap && productMap.length>0){
					 $(".newGoodCommon").data("goodInfo",productMap);
				 }else{
					 $(".newGoodCommon").data("goodInfo",'');
				 }
			 }
		 }
	 })
	 if(Number(invoiceRequire) === 1){ // 增专的 需要系统税率

	 }
 }
 // creator: 张旭博，2018-06-11 15:26:54，根据缓存设置商品代号下拉列表
 function setOuterSnOption() {
	 let type = $("#newGood").data("goodsType")
	 let pCPOS
	 if(type === 1){ // 通用
		 pCPOS = $(".newGoodCommon").data("goodInfo");
	 }else if(type === 2){ // 专属
		 pCPOS = $(".newGood").data("goodInfo");
	 }
	 var inputVal = $("#newGood .outerSn").val();
	 var outerLiStr = "";
	 if(inputVal == ""){
		 //未输入值时显示获得的所有数据
		 for(var i in pCPOS){
			 var invoiceCategory = Number(pCPOS[i]['invoiceCategory']); // 1 增专 2 增普 3 其他票 4 不开票
			 // if(invoiceCategory === '2'){
			 //     pCPOS[i]['unitPrice'] = pCPOS[i]['unitPriceInvoice']
			 // }else if(invoiceCategory === '4'){
			 //     pCPOS[i]['unitPrice'] = pCPOS[i]['unitPriceNoinvoice']
			 // }else if(invoiceCategory == ''){
			 //     pCPOS[i]['unitPrice'] = pCPOS[i]['unitPriceReference']
			 // }
			 outerLiStr += '<li class="ty-option" id="'+pCPOS[i].id+'"><span>'+pCPOS[i].outerSn+'</span><div class="hd">'+JSON.stringify(pCPOS[i])+'</div></li>';
		 }
	 } else {
		 //输入值时将此值与所有数据比对，将含有此值的商品代号显示出来
		 var isSameObj = null
		 for(var j in pCPOS){
			 var outerSn = pCPOS[j].outerSn;
			 var choose = outerSn.indexOf(inputVal);
			 if(outerSn === inputVal){
				 isSameObj = pCPOS[j]
			 }
			 if(choose != "-1"){
				 outerLiStr += '<li class="ty-option" id="'+pCPOS[j].id+'"><span>'+pCPOS[j].outerSn+'</span><div class="hd">'+JSON.stringify(pCPOS[j])+'</div></li>';
			 }
		 }
		 if(isSameObj){
			 setGoodDetail(isSameObj);
		 } else {
			 $("#newGood .invoiceTip").html("");
			 $("#newGood input:not(.outerSn)").val("");
		 }
	 }
	 $(".ty-optionCon").html(outerLiStr);
 }
// creator: 张旭博，2018-05-30 10:09:04，根据商品代号获得其他信息
 function setGoodDetail(data, fuType) {
	 let priceMemo = ''
	 let type = $("#newGood").data("goodsType"); // 1  通用   2专属，
	 let setPrice = false ;
	 let price = data.unitPrice || "";
	 if (Number(type) === 2){
		 priceMemo = data.priceDesc
		 setPrice = true ;
		 $("#newGood .modifyPrice").data('modify', 0);
		 $(".modify").hide();
		 $("#newGood .rate").prop("disabled",true)
		 $("#newGood .noPrice").prop("disabled",true)
		 $("#newGood .price").data("able", 'true').prop("disabled",true)
	 }else if(Number(type) === 1){
		 // 通用型按照 订单上选的类型展示
		 let invoiceCategory = Number(data.invoiceCategory); // 1 增专 2 增普 3 其他票 4 不开票
		 let invoiceRequire = Number(ordObj["invoiceRequire"]);// 1 增专 2 增普 3 不开票
		 // let same = (invoiceCategory === 1 && invoiceRequire === 1) || (invoiceCategory === 2 && invoiceRequire === 2) || (invoiceCategory === 4 && invoiceRequire === 3) ;
		 let same = true ;
		 if(same){ // 订单设置的开票种类 和 通用商品本身设置的开票种类 相同
			 priceMemo =''
			 setPrice = true ;
			 if(invoiceRequire === 2){
				 price = data.unitPriceInvoice || "" ;
			 }else if(invoiceRequire === 3){
				 price = data.unitPriceNoinvoice || "" ;
			 }
		 }else{
			 setPrice = false ;
		 }
		 if(invoiceRequire === 1){
			 if(data.unitPriceInvoice){ priceMemo += `非专票开票价${data.unitPriceInvoice}元`; }
			 if(data.unitPriceNoinvoice){ priceMemo += `不开票价${data.unitPriceNoinvoice}元`; }
			 if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
		 }else if(invoiceRequire === 2){
			 if(data.unitPriceNotax){ priceMemo += `开具${data.taxRate}%专票的不含税价为${data.unitPriceNotax}元，含税价为${data.unitPrice}元`;  }
			 if(data.unitPriceNoinvoice){ priceMemo += `不开票价${data.unitPriceNoinvoice}元`; }
			 if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
		 }else if(invoiceRequire === 3){
			 if(data.unitPriceNotax){ priceMemo += `开具${data.taxRate}%专票的不含税价为${data.unitPriceNotax}元，含税价为${data.unitPrice}元`;  }
			 if(data.unitPriceInvoice){ priceMemo += `非专票开票价${data.unitPriceInvoice}元`; }
			 if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
		 }
	 }
	 let addressList = data.addressList || [];
	 let orderAddress = Number(ordObj["deliveryType"]);////1-收货 2-自提 3-多个收货地址
	 if (fuType === "update") {
		 var addrListStr =
			 '<option value=""></option>'+
			 '<option value="' + data.receiveAddressId + '" selected>' + data.receiveAddress + '</option>';

		 $(".receiveAddress2").html(addrListStr);
		 priceMemo = data.priceInfo.priceDesc
	 } else {
		 setAddress(addressList);
	 }
	 if (orderAddress === 2) {
		 $(".receiveAddress2").val("");
	 } else {
		 $(".receiveAddress2").val(data.receiveAddressId);
	 }
	 if(setPrice){
		 let unitPriceNotax = parseFloat(data.unitPriceNotax && data.unitPriceNotax.toFixed(10)) || 0
		 $("#newGood .rate").val(`${data.taxRate}`).data("val",`${data.taxRate}`)
		 $(".noPrice").val(unitPriceNotax).data("val",`${unitPriceNotax}`).removeAttr("placeholder");
		 $(".price").val(price).data("val",`${price}`).data("able", 'false').removeAttr("placeholder");
	 }else{
		 $("#newGood .rate").val(``).data("val",``)
		 $(".noPrice").val(``).data("val",``).removeAttr("placeholder");
		 $(".price").val(``).data("val",``).data("able", 'false').removeAttr("placeholder");
	 }
	 $(".outerName").val(data.outerName);
	 $(".outerSn").val(data.outerSn);
	 $(".goodNum").val("");
	 $(".unit").val(data["unit"]);
	 $(".currentStock").val(data.currentStock);	//当前库存
	 $(".minimumStock").val(data.minimumStock);	//最低库存
	 $(".goodNum_stock").val(0) 	//货物件数
	 $(".priceMemo").val(priceMemo).attr("title", priceMemo) 	//价格说明
	 data.priceMemo = priceMemo;
	 $(".invoiceTip").html(JSON.stringify(data));
	 $("#newGood .modifyPrice ").show()
 }
 // creator: 李玉婷，2022-12-14 8:12:24，专属商品自选收货地点
 function receiveAddress2(obj){
	 let type = $("#newGood").data("goodsType"); // 返回1 通用 2 专属
	 if (type === '2' || type === 2) {
		 let orderAddress = ordObj["addressId"];
		 let val = obj.val();
		 if (val !== "" && orderAddress !== val) {
			 bounce_Fixed3.show($("#changeAddressTip"));
		 } else {
			 let able = $("#newGood").find(".price").data("able");
			 let val3 = $("#newGood").find(".price").data("val");
			 $("#newGood").find(".price").val(val3);
			 $("#newGood .modifyPrice").show();
			 if ($(".invoice1").is(":visible")){
				 let val1 = $("#newGood").find(".rate").data("val");
				 let val2 = $("#newGood").find(".noPrice").data("val");
				 $("#newGood").find(".rate").val(val1);
				 $("#newGood").find(".noPrice").val(val2);
				 $("#newGood").find(".rate,.noPrice,.price").prop("disabled", able).removeAttr("placeholder");
			 } else {
				 $("#newGood").find(".price").prop("disabled", able).removeAttr("placeholder");
			 }
		 }
	 }
 }
 // creator: 李玉婷，李玉婷，2022-12-14 16:26:52，该商品在该交货地点的价格需重新录入！
 function changeCancel(){
	 $(".receiveAddress2").val("");
	 bounce_Fixed3.cancel()
 }
 // creator: 李玉婷，李玉婷，2022-12-14 16:26:52，该商品在该交货地点的价格需重新录入！
 function changeAddressNext(){
	 let outerName = $("#newGood .outerName").val();
	 if(outerName.length === 0){
		 layer.msg("请先选择商品")
		 return false
	 }
	 let data = JSON.parse($(".invoiceTip").html());
	 bounce_Fixed3.cancel()
	 $("#newGood .modifyPrice").hide();
	 if ($(".invoice1").is(":visible")){
		 $("#newGood").find(".rate,.noPrice,.price").val("").removeAttr("disabled");
		 $("#newGood").find(".noPrice,.price").attr("placeholder", `请录入价格，参考单价${handleNull(data.unitPriceReference)}元`);
	 } else {
		 $("#newGood").find(".price").val("").removeAttr("disabled").attr("placeholder", `请录入价格，参考单价${handleNull(data.unitPriceReference)}元`);
	 }
 }
 // ========================  筛选功能 ==================================
 var role = { "radio":0 , "check":[]   } ;
 /* Creator : 侯杏哲 2017-07-25 初始化筛选框 */
 function setRoleInit( objStr ){
	 $("#"+ objStr +">div:eq(1)").children().each(function(){ // 单选框
		 if( $(this).index() == 0){
			 $(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1") ;
		 }else{
			 $(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
		 }
	 }) ;
	 $("#"+ objStr +">div:eq(2)").children().each(function(){ // 复选框
		 $(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1");
	 }) ;
	 role = { "radio":1 , "check":["5" , "6" , "7" , "8" , "9" , "10" , "11" ] } ;
 }
 /* Creator : 侯杏哲 2017-07-25 初始化筛选框  */
 function setRoleBtn( dropdownBtnObj ){
	 dropdownToggle(dropdownBtnObj) ;
	 // 根据当前权限设置默认值
	 var radio = role["radio"] ;
	 var check = role["check"] ;
	 dropdownBtnObj.siblings(".ty-dropdownCon").children(":eq(1)").children().each(function(){
		 var code = $(this).children("i").attr("code") ;
		 if(code == radio){
			 $(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1") ;
		 }else{
			 $(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
		 }
	 }) ;
	 dropdownBtnObj.siblings(".ty-dropdownCon").children(":eq(2)").children().each(function(){
		 var code = $(this).children("i").attr("code") ;
		 var isIn = $.inArray(code, check);
		 if(isIn == -1 ){ // 不在数组中 
			 $(this).children("i").attr("class" , "fa fa-square-o").attr("isSet" , "0") ;
		 }else{
			 $(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1") ;
		 }
	 }) ;

 }
 /* Creator : 侯杏哲 2017-07-25 筛选按钮  - 设置要显示的数据  */
 function setRole( tbodyObj , dropdownBtnObj , roleCon ){
	 var radio = 0 ; // 单选值
	 $("#"+ roleCon +">div:eq(1)").children().each(function(){
		 var isset = $(this).children("i").attr("isSet") ;
		 if(isset == 1){ radio = $(this).children("i").attr("code") ; 		}
	 });
	 var check = [] ; // 复选值
	 var llk = $("#roleCon div:eq(2)").children() ;
	 $("#"+ roleCon +">div:eq(2)").children().each(function(){
		 var isset = $(this).children("i").attr("isSet") ;
		 if(isset == 1){  var code = $(this).children("i").attr("code") ;  check.push( code ) ; 		}
	 });
	 role = { "radio":radio , "check":check   } ;
	 var codeName = chargeCode( radio ) ;
	 tbodyObj.siblings("thead").children("tr").children(":eq(1)").html( codeName );
	 tbodyObj.children().each(function(){
		 var orcalO =  $(this).children(":eq(1)") ;
		 var oralVal = $(this).children(":eq(1)").html() ;
		 if($.trim(oralVal) !=""){
			 var info = $(this).attr("info");
			 info = info && JSON.parse(info) ;
			 $(this).children(":eq(1)").html( chargeCodeVal(radio , info)  );
		 }
	 });
	 dropdownToggle( dropdownBtnObj ) ;
 }
 /* Creator : 侯杏哲 2017-07-25 筛选 - 取消设置 */
 function cancelSert( dropdownBtnObj ){
	 dropdownToggle( dropdownBtnObj ) ;
 }

 // creator: hxz  2021-05-25  历史价格
 function getHistoryPrice(obj) {
	 var goodsInfo = $("#newGood .invoiceTip").html();
	 if(goodsInfo.length > 0){
		 goodsInfo = JSON.parse(goodsInfo)
		 $.ajax({
			 'url' : "../sales/productPriceHistory",
			 "data":{ "id": goodsInfo.id },
			 success:function (res) {
				 var list = res.data || []

				 bounce_Fixed3.show($("#cHistoryPrice"))
				 let str = ""
				 list.forEach(function (item) {
					 let invoiceCat = Number(item.invoice_require) ;
					 let priceStr = ``;
					 if(invoiceCat === 1){
						 priceStr = `税率${item.tax_rate}%、含税单价${item.unit_price}元，不含税价${item.unit_price_notax}元`;
					 } else if(invoiceCat === 2){
						 priceStr = `开票单价${item.unit_price_invoice}元`;
					 } else {
						 priceStr = `不开票价${item.unit_price_noinvoice}元`;
					 }
					 str += `<tr><td>${priceStr}</td><td>${ new Date(item.create_date).format("yyyy-MM-dd hh:mm:ss") }</td></tr>`;
				 })
				 $("#cHistoryPrice table tbody tr:gt(0)").remove() ;
				 $("#cHistoryPrice table tbody").append(str);
				 $("#cHistoryPrice .cNum").html(list.length);
			 }
		 })
	 }else{
		 layer.msg("请先选择商品")
	 }
 }
 // creator: hxz  2021-05-25  所属的合同
 function getContractDetail(obj) {
	 var goodsInfo = $("#newGood .invoiceTip").html();
	 if(goodsInfo.length > 0){
		 goodsInfo = JSON.parse(goodsInfo)
		 $.ajax({
			 'url' : "../sales/belongContract",
			 "data":{ "id": goodsInfo.id },
			 success:function (res) {
				 var newcInfo = res
				 if(!newcInfo){
					 layer.msg("获取合同信息失败！");
					 return false;
				 }
				 let versionNo = newcInfo.versionNo
				 if(versionNo === -1){
					 $(".nothas").show();$(".has").hide();
				 }else{
					 $(".nothas").hide();$(".has").show();
					 $("#cScan .cNos").html(newcInfo.sn)
					 $("#cScan .create").html(`${newcInfo.createName} ${ new Date(newcInfo.createDate).format("yyyy-MM-dd hh:mm:ss")}`)
					 $("#cScan .cSignDates").html(new Date(newcInfo.signTime).format("yyyy-MM-dd"))
					 $("#cScan .cvalidDates").html(new Date(newcInfo.validStart).format("yyyy-MM-dd") + '至' + (new Date(newcInfo.validEnd).format("yyyy-MM-dd")))
					 $("#cScan .cMemos").html(newcInfo.memo)
					 let imgStr1 = ``
					 let contractBaseImages = newcInfo.contractBaseImages || []
					 contractBaseImages.forEach(function(bsIm){
						 imgStr1 += `<span class="fileImScan" data-fun="imgScan" path="${bsIm.filePath}">
                                  <span>${bsIm.orders + 1 }</span>
                                  <span class="hd">${JSON.stringify(bsIm) }</span>
                             </span>`
					 })
					 $("#cScan .cImgs").html(imgStr1)
					 $("#cScan .cWord").data('path', newcInfo.filePath).attr('path', newcInfo.filePath)
					 let productList = newcInfo.productList || []
					 $("#cScan .gNum").data('list', productList).html(productList.length)
					 let enabledList = newcInfo.enabledList || [] , enableStr= ``;
					 enabledList.forEach(function (enIm) {
						 enableStr += `
                <p>
                    <span>${ enIm.enabled === 1 ? "恢复履约/重启合作" : "暂停履约/终止合作" }</span>
                    <span class="enName">${ enIm.updateName }</span>
                    <span>${ new Date(enIm.enabledTime).format("yyyy-MM-dd hh:mm:ss") }</span>
                </p>`;
					 })
					 $("#cScan .enabledList").html(enableStr)
				 }
				 bounce_Fixed3.show($("#cScan"))
				 // $("#cScan").data("cid",cid)

			 }
		 })
	 }else{
		 layer.msg("请先选择商品")
	 }
 }
 // creator: 张旭博，2018-05-30 10:12:19，点击新增商品按钮
 function newGoodBtn(thisObjType , type) { // type : 1 通用  2 专属  thisObjType: "add"商品新增 "update"商品修改
	 $("#newGood").find("input,textarea,select").val("");
	 $(".outerSn+.ty-optionCon").html("") ;
	 $(".outerSn+.ty-optionCon").hide() ;
	 if(hasAuthority(0) === true){
		 layer.msg("您没有此权限！");
	 }else{
		 $("#newGood .invoiceTip").html("");
		 $("#newGood .outerSn").prop("disabled",false)
		 $("#newGood .goodNum").prop("disabled",false)
		 $("#newGood .requireDate").prop("disabled",false)
		 $("#newGood .receiveAddress2").prop("disabled",false)
		 $("#newGood .receiveAddress2").html(setAddress());
		 bounce_Fixed2.show($("#newGood")) ;
		 $("#newGood").data("goodsType", type); // 返回1 通用 2 专属
		 $("#newGood").data("editType", thisObjType)
		 $("#newGood").data("source", 1);
		 $(".redRequie").hide();
		 $("#newGood").find(".noPrice,.price").attr("placeholder","");
		 let invoiceRequire = ordObj["invoiceRequire"], invoiceTip  = '';
		 switch (Number(invoiceRequire)){
			 case 1:
				 $(".invoice1").show();
				 $(".changeTtl").html('含税单价');
				 invoiceTip = '本订单需开具增值税专用发票。选择商品时，您无法选到系统中没有该开票价格的商品。'
				 if(type === 1) {
					 $(".redRequie").show();
					 invoiceTip = '本订单内商品需开具增值税专用发票。'
				 }
				 break;
			 case 2:
				 $(".invoice1").hide();
				 $(".changeTtl").html('开普通发票的开票单价');
				 invoiceTip = '本订单需开具普通发票。选择商品时，您无法选到系统中没有该开票价格的商品。'
				 if(type === 1) {
					 invoiceTip = '本订单内商品需开具增值税专用发票以外的发票。'
				 }
				 break;
			 case 3:
				 $(".invoice1").hide();
				 $(".changeTtl").html('不开发票的单价');
				 invoiceTip = '本订单不需开具发票。您选择商品时，无法选到有开票价格的商品。'
				 if(type === 1) {
					 invoiceTip = '本订单内商品不需开具发票。'
				 }
				 break;
			 default:
		 }
		 $(".invoiceCatTip").html(invoiceTip);
		 let ttl1 = '增加'
		 if(type === 2){
			 $(".isZ").show(); $(".isT").hide()
			 $("#newGood .modifyPrice").data('modify',0)
			 $("#newGood .rate").prop("disabled",true)
			 $("#newGood .noPrice").prop("disabled",true)
			 $("#newGood .price").prop("disabled",true)
			 $("#newGood .bonceHead span").html(`${ttl1}专属商品`)
		 }else if(type === 1){
			 $(".isT").show()
			 $(".isZ").hide()
			 $("#newGood .rate").prop("disabled",false)
			 $("#newGood .noPrice").prop("disabled",false)
			 $("#newGood .price").prop("disabled",false)
			 $("#newGood .bonceHead span").html(`${ttl1}通用型商品`)
		 }
		 bounce_Fixed2.everyTime('0.5s','pack',function(){
			 //计算包装
			 var pack = $("#newGood .goodNum_stock").attr("pack");        //包装信息（格式：1,2,4）
			 var packNum = 1;                        //包装数量（1x2x4=8）
			 var pack_amount = 0;                    //货物件数

			 var outPlan = $("#newGood .goodNum").val();

			 //计算包装数量
			 if(pack !== "null" && pack !== undefined && pack !== "" && pack !== "0"){
				 var packObj = pack.split(",");
				 for(var i=0;i<packObj.length;i++){
					 packNum  *= Number(packObj[i]);
				 }
			 }else{
				 packNum = 1;
			 }
			 //计算货物件数
			 if(outPlan !== "" ){
				 outPlan = parseInt(outPlan);
				 pack_amount = Math.ceil(outPlan/packNum);
			 }else{
				 pack_amount = 0;
			 }
			 $("#newGood .goodNum_stock").val(pack_amount);
		 });
	 }
 }
 // creator: hxz 2021-05-11 生产方的评审负责人
 function gettProductionPrincipal(val) {
	 $.ajax({
		 'url':"../sale/getProductionPrincipal",
		 "data":{ "oid":sphdSocket.user.oid },
		 success:function (res) {
			 var list = res || [] , str = "<option value=''>请选择</option>"
			 list.forEach(function(item){
				 if (val === item.userID) {
					 str += `<option value='${item.userID}' selected="selected">${item.userName}</option>` ;
				 } else {
					 str += `<option value='${item.userID}'>${item.userName}</option>` ;
				 }
			 })
			 $("#updateOrderInfo .principal").html(str).prop("disabled", false);
			 if (val && val !== "") {
				 $("#updateOrderInfo .principal").prop("disabled", true);
			 }
		 }
	 })
 }

 //creator:lyt 2023/9/2 0002 下午 4:41 调整要货需求
 let batchObj = null;
 function adjustGoods(source){
	 let verNo = 0;
	 let curObj = ordObj["spanObj"];
	 let order = JSON.parse(curObj.siblings(".hd").html());
	 $("#adjustSource").val(source);
	 setOuterCon(ordObj["cusId"], ordObj["invoiceRequire"]);
	 if (source === 1) {
		 $("#adjustGoods .bonceHead span").html("调整要货需求");
	 } else if (source === 2) {
		 $("#adjustGoods .bonceHead span").html("修改商品的要货信息");
	 }
	 $(".goReviewList tbody").html("");
	 $.ajax({
		 type: "get",
		 url: "/orders/review/reviewDetails?orderId="+ order.id ,
		 success: function(res){
			 bounce_Fixed.show( $("#adjustGoods") );
			 var items = res["items"]["data"] ;
			 var order = res["orderBase"][0] ;
			 let html = ``;
			 ordObj["addressId"] = order.address_id;
			 ordObj["deliveryType"] = order.delivery_type;
			 ordObj["reviewer_name"] = res["orderBase"][0]["principal_name"] || "";
			 $(".goReviewList .saleOrd").html(new Date(order.create_date).format("yyyy-MM-dd hh:mm:ss") ) ;
			 $(".goReviewList .pudLastReview").html(  res.reviewName + " " + new Date(res.produceReviewDate).format("yyyy-MM-dd hh:mm:ss")  ) ;
			 if( !items ){
				 layer.msg("访问失败") ;  return false ;
			 }
			 if( items && items.length > 0 ){
				 let btn = ``;
				 if (source === 1) {
					 btn = ` <button class='ty-color-blue' onclick='batchDelivery( $(this) )'>分批发货</button>`;
				 }
				 for(var i = 0 ; i < items.length ; i++ ){  // 循环每个商品组
					 let size = items[i].reviews.length;
					 if (size > 0) {
						 for (let b=0;b<size;b++){
							 if(Number(items[i].reviews[b].version_no) > verNo) {
								 verNo = Number(items[i].reviews[b].version_no)
							 }
						 }
					 }
					 items[i].batch = false;
					 html =
						 `<tr class="reviewTr" data-icon="${ i + 1}">
                            <td>${items[i].outer_sn}</td>
                            <td>${items[i].outer_name}</td>
                            <td>${items[i].unit}</td>
                            <td>${items[i].amount}</td>
                            <td>${new Date(items[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td>${items[i].delivery_address}</td>
                            <td>${size <= 0? "" : handleNull(items[i].reviews[size-1].scheduledAmount)}</td>
                            <td>${size <= 0? "" : new Date(items[i].reviews[size-1].surplusDate).format("yyyy-MM-dd")}</td>
                            <td><input type='text' class='cell1' placeholder="请录入"/></td>
                            <td><input type='text' class='cell2' readonly id='goods${i}' placeholder="请选择"></td>
                            <td>
                            	<select class="addressInfo" onchange="giveTitle($(this))">${setAddress()}</select>
                            </td>
                            <td>
                                ${btn}
                                <span class='ty-color-gray'>删除</span>
                            <span class='hd'>${JSON.stringify(items[i])}</span>
                            </td>
                            </tr>
                        `;
					 $(".goReviewList tbody").append( html ) ;
					 laydate.render({elem: "#goods" + i, min: 0});
				 }
			 }
			 ordObj["reviewsVersion"] =  verNo;
			 if (source === 1) {
				 bounce_Fixed.everyTime('0.5s','adjustGoods',function(){
					 $(".goReviewList tbody .reviewTr").each(function (){
						 let con1 = $(this).find("input").eq(0).val()
						 let con2 = $(this).find("input").eq(1).val()
						 if (con1 !== "" || con2 !== ""){
							 $(this).find("button").attr("disabled", "disabled");
						 } else {
							 $(this).find("button").removeAttr("disabled");
						 }
					 });
				 });
			 }
		 }
	 });
 }
 function giveTitle(obj){
	 //获取下拉框的文本值
	 var checkText= obj.find("option:selected").text();
	 //修改title值
	 obj.attr("title",checkText);
 }
 function adjustGoodsOk(){
	 let source = $("#adjustSource").val();
	 let goodsInfoList = [], goodsInfoList1 = [];
	 let sum = 0;
	 let able = false, addAble = false;
	 let msg = `至少有一项修改才能提交`;
	 if (!able && $(".goReviewList tbody tr.branchRow").length > 0) able = true;
	 if (!able) {
		 $(".goReviewList tbody input").each(function (){
			 let val = $(this).val();
			 if(val !== "") {
				 able = true;
				 return false;
			 }
		 });
		 $(".goReviewList tbody select").each(function (){
			 let val = $(this).val();
			 if(val !== "") {
				 able = true;
				 return false;
			 }
		 });
	 }
	 $(".goReviewList tbody tr.newGoodsItem").each(function (){
		 let inx = $(".goReviewList tbody tr").index($(this)) + 1;
		 let info = JSON.parse($(this).find(".hd").html());
		 let receiveAddressId = $(this).find(".addressInfo").val();
		 if(info.batch) {
			 if(receiveAddressId === "") {able = false;}
		 } else {
			 let goodNum = $(this).find("input").eq(0).val();
			 let date =  $(this).find("input").eq(1).val();
			 if(receiveAddressId === "" || goodNum === "" || date === "") {able = false;}
		 }
		 if (!able) {msg = "请补充完整第"+ inx +"条商品的'订购数量'、'要求到货日期'和'收货地点'";return false;}
	 })
	 if (able) {
		 $(".goReviewList tbody tr.reviewTr").each(function (){
			 let isNew = $(this).data("isnew"); //新增的商品
			 let receiveAddress = $(this).find(".addressInfo option:selected").text();
			 let receiveAddressId = $(this).find(".addressInfo").val();
			 let info = JSON.parse($(this).find(".hd").html());
			 let goodNum = $(this).find("input").eq(0).val();
			 let date =  $(this).find("input").eq(1).val();
			 let temp = {};
			 let price = 0 ;
			 let invoiceRequire = 0;
			 if (isNew === 1) {
				 invoiceRequire = info.invoiceRequire;
				 temp = {
					 "id"				: info["id"] , // 商品对照id
					 "sid"				: info["sid"] ,// 明细id
					 "SlInnerSn"		: info["slInnerSn"]  ,
					 "SlInnerSnName"	: info["slInnerSnName"] ,
					 "SlOuterSn"		: info["slOuterSn"] ,
					 "SlOutterSnName"	: info["slOutterSnName"] ,
					 "SlUnit"			: info["slUnit"] ,
					 "SlUnitPrice"		: info["slUnitPrice"] ,
					 "deliveryDate"     : date === "" ? new Date(info.delivery_date).format("yyyy-MM-dd") : date,
					 "amount"			: goodNum === "" ? info.amount : goodNum,
					 "address"			: receiveAddressId === "" ?info.delivery_address: receiveAddress,
					 "addressId"		: receiveAddressId === "" ?info.address_id: receiveAddressId,
					 ...info.priceInfo
				 };
				 if(invoiceRequire === '1'){ // 增专
					 price = info.priceInfo.unitPrice
				 }else if(invoiceRequire === '2'){
					 price = info.priceInfo.unitPriceInvoice
				 }else if(invoiceRequire === '3'){ //不开票
					 price = info.priceInfo.unitPriceNotax
				 }
			 } else {
				 invoiceRequire = info.invoice_require
				 temp = {
					 "id"				 : info["sr_id"] , // 商品对照id
					 "sid"				 : info["sid"] ,// 明细id
					 "SlInnerSn"		 : info["inner_sn"]  ,
					 "SlInnerSnName"	 : info["name"] ,
					 "SlOuterSn"		 : info["outer_sn"] ,
					 "SlOutterSnName"	 : info["outer_name"] ,
					 "SlUnit"			 : info["unit"] ,
					 "SlUnitPrice"		 : info["unit_price"] ,
					 "unitPriceNoinvoice": info["unit_price_noinvoice"] ,
					 "deliveryDate"      : date === "" ? new Date(info.delivery_date).format("yyyy-MM-dd") : date,
					 "amount"            : goodNum === "" ? info.amount : goodNum,
					 "address"           : receiveAddressId === "" ?info.delivery_address : receiveAddress,
					 "addressId"    	 : receiveAddressId === "" ?info.address_id : receiveAddressId
				 };
				 if(invoiceRequire === '1'){ // 增专
					 price = info.unit_price
				 }else if(invoiceRequire === '2'){
					 price = info.unit_price_invoice
				 }else if(invoiceRequire === '3'){ //不开票
					 price = info.unit_price_noinvoice
				 }
			 }
			 if (info.batch) {
				 let arr = info.partialShipment;
				 for(let i=0;i<arr.length;i++) {
					 isNew === 1 ? "" : arr[i].itemId = info.id;
					 arr[i].addressId = receiveAddressId === "" ?info.address_id : receiveAddressId;
					 sum += arr[i].amount * price;
				 }
				 temp.partialShipment = JSON.stringify(arr);
			 } else {
				 if (temp.amount == info.amount && temp.deliveryDate == new Date(info.delivery_date).format("yyyy-MM-dd") && temp.addressId == info.address_id) {temp.progress = 1;}
				 info.amount           = goodNum !== "" ? goodNum : info.amount;
				 info.delivery_date    = date !== "" ? date : info.delivery_date;
				 sum += info.amount * price;
			 }
			 info.delivery_address = receiveAddressId === "" ? info.delivery_address : receiveAddress;
			 goodsInfoList.push(temp);
			 goodsInfoList1.push(info)
		 });
		 let item = {
			 "operation": 6, //4-修改订单号或订单收到的日期,5-更换生产方的评审负责人,6-更改订单明细要货信息,7-更改订单明细价格元素
			 "save_slOrders": JSON.stringify({
				 "id": ordObj["orderId"],
				 "contractAmount": sum
			 }),
			 "reviewVersion": ordObj["reviewsVersion"],
			 "save_slOrdersItemList": JSON.stringify(goodsInfoList)
		 }
		 if (Number(source) === 1) {//调整要货需求
			 $(".adjustGoodsData").html(JSON.stringify(item));
			 $(".adjustGoodsDataScan").html(JSON.stringify(goodsInfoList1));
		 } else {
			 bounce.cancel() ;
			 adjustGoodsSure(item, source);
		 }
		 bounce_Fixed.cancel() ;
	 } else {
		 layer.msg(msg);
	 }

 }
 function adjustGoodsSure(item, source){
	 $.ajax({
		 url:"../sales/updateSlOrders.do",
		 data: item,
		 type:"post",
		 dataType:"json",
		 async:false,
		 success:function (data) {
			 if (data.code === 500) {
				 layer.msg(data.msg);
				 let curObj = ordObj["spanObj"];
				 curObj.click();
				 return false;
			 }
			 getOrdList(1,10);
			 if(source === "2" || source === 2) {
				 layer.msg(`修改成功！${ordObj["reviewer_name"]}将收到系统消息，并需重新评审本订单。知道了`)
			 } else {
				 let json = {"orderId": ordObj["orderId"], "state": 3, "reviewVersion": ordObj["reviewsVersion"]}
				 saleHandle(json);
				 getOrdList()
			 }
		 }
	 })
 }
 //creator:lyt 2023/9/18 调整要货需求查看
 function adjustGoodsScan(){
	 let data = $(".adjustGoodsDataScan").html();
	 let list = [];
	 let html = ``;
	 if (data !== "") {
		 list = JSON.parse(data);
		 for(var i = 0 ; i < list.length ; i++ ){  // 循环每个商品组
			 let size = (list[i].reviews && list[i].reviews.length) || 0;
			 if (list[i].batch) { //分批了
				 let ship = list[i].partialShipment;
				 html +=
					 `<tr>
                            <td>${list[i].outer_sn}</td>
                            <td>${list[i].outer_name}</td>
                            <td>${list[i].unit}</td>
                            <td>${list[i].amount}</td>
                            <td>${new Date(list[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td>${list[i].delivery_address}</td>
                            <td>${size > 0 ? list[i].reviews[size-1].scheduledAmount : ""}</td>
                            <td>${size > 0 ? formatTime(list[i].reviews[size-1].surplusDate) : ""}</td>
                            <td></td>
                            <td></td>
                            <td>${list[i].delivery_address}</td>
                            </tr>
                        `;
				 for(var a=0;a < ship.length;a++){
					 html +=
						 `<tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>${ship[a].amount}</td>
                            <td>${ship[a].deliveryDate}</td>
                            <td></td>
                            </tr>
                        `;
				 }
			 } else {
				 html +=
					 `<tr>
                            <td>${list[i].outer_sn}</td>
                            <td>${list[i].outer_name}</td>
                            <td>${list[i].unit}</td>
                            <td>${list[i].amount}</td>
                            <td>${new Date(list[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td>${list[i].delivery_address}</td>
                            <td>${size > 0 ? list[i].reviews[size-1].scheduledAmount : ""}</td>
                            <td>${size > 0 ? formatTime(list[i].reviews[size-1].surplusDate) : ""}</td>
                            <td>${list[i].amount}</td>
                            <td>${new Date(list[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td>${list[i].delivery_address}</td>
                            </tr>
                        `;
			 }
		 }
	 } else {
		 list = JSON.parse($(".ordGoodsList").html());
		 for(var i = 0 ; i < list.length ; i++ ){  // 循环每个商品组
			 let size = (list[i].reviews && list[i].reviews.length) || 0;
			 html +=
				 `<tr>
                            <td>${list[i].outer_sn}</td>
                            <td>${list[i].outer_name}</td>
                            <td>${list[i].unit}</td>
                            <td>${list[i].amount}</td>
                            <td>${new Date(list[i].delivery_date).format("yyyy-MM-dd")}</td>
                            <td>${list[i].delivery_address}</td>
                            <td>${size > 0 ? list[i].reviews[size-1].scheduledAmount : ""}</td>
                            <td>${size > 0 ? formatTime(list[i].reviews[size-1].surplusDate) : ""}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            </tr>
                        `;
		 }
	 }
	 $("#adjustGoodsScan tbody").html( html ) ;
	 bounce_Fixed.show( $("#adjustGoodsScan") );
 }
 /*creator:lyt 2023/9/2 0002 下午 6:33 */
 function batchDelivery(obj){
	 let info = JSON.parse(obj.siblings(".hd").html());
	 batchObj = obj;
	 $("#batchDelivery input").val("");
	 $("#recentAmount").val(info.amount);
	 $(".checkBtn").removeAttr("onclick").addClass("grayBg");
	 if (info.batch) {
		 let ship = info.partialShipment;
		 let sum = 0;
		 for(let i=0;i<ship.length;i++){
			 sum += Number(ship[i].amount);
			 if(i > 2) { addMore(); }
			 $("#batchDelivery .batchItem").eq(i).find("input").eq(0).val(ship[i].deliveryDate);
			 $("#batchDelivery .batchItem").eq(i).find("input").eq(1).val(ship[i].amount);
		 }
		 $(".checkBtn").attr("onclick", "checkBtn()").removeClass("grayBg");
		 $("#newAmount").val(sum);
	 }
	 bounce_Fixed2.show( $("#batchDelivery") ) ;
	 bounce_Fixed2.everyTime('0.5s','batch',function(){
		 let empty = 0;
		 $(".batchWrapper input").each(function (){
			 if ($(this).val() === ""){
				 empty++;
				 return false;
			 }
		 });
		 if(empty > 0){
			 $(".addMore span").addClass("ty-color-gray").removeClass("ty-color-blue").removeAttr("onclick");
			 $("#batchDeliveryOk").prop("disabled", true);
		 }else{
			 $("#batchDeliveryOk").prop("disabled", false);
			 $(".addMore span").addClass("ty-color-blue").removeClass("ty-color-gray").attr("onclick", "addMore()");
		 }
	 });
 }
 function batchDeliveryOk(){
	 let able = true;
	 let newAmount = $("#newAmount").val();
	 let count = 0;
	 let msg = ``;
	 let trObj = batchObj.parents("tr");
	 let detail = JSON.parse(batchObj.siblings(".hd").html());
	 let partialShipment = []
	 let index = trObj.data("icon");
	 if (!newAmount || newAmount === "") {
		 newAmount = detail.amount;
	 }
	 if($(".checkBtn .fa-circle").length > 0){
		 detail.batch = false;
		 detail.partialShipment = partialShipment;
		 trObj.find("input").prop("disabled",false);
		 batchObj.siblings(".hd").html(JSON.stringify(detail));
		 bounce_Fixed2.cancel();
		 $(".goReviewList tbody tr.reviewTr" + index).remove();
	 } else {
		 $("#batchDelivery .batchItem").each(function () {
			 let date = $(this).find("input").eq(0).val();
			 let amount = $(this).find("input").eq(1).val();
			 if (date !== "" && amount !== "") {
				 count += Number(amount);
			 }
		 });
		 if (count !== Number(newAmount)) {
			 able = false;
			 msg = `分批发货数量加起来与应到货的总数量不一致。</br>请检查并修正！`;
		 }
		 if (able) {
			 let html = ``;
			 $("#batchDelivery .batchItem").each(function (){
				 let date = $(this).find("input").eq(0).val();
				 let amount = $(this).find("input").eq(1).val();
				 if (date !== "" && amount !== "") {
					 let item = {"deliveryDate": date, "amount": amount}
					 html += `<tr class="reviewTr${index} batchRow">
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td>${amount}</td>
                  <td>${date}</td>
                  <td><input class="cell2" type='text' disabled="disabled"></td>
                  <td>
                  <span class='hd'>${JSON.stringify(item)}</span>
                  </td>
                  </tr>`;
					 partialShipment.push(item);
				 }
			 });
			 partialShipment.length > 0? detail.batch = true:detail.batch = false;
			 detail.partialShipment = partialShipment;
			 $(".goReviewList tbody tr.reviewTr" + index).remove();
			 trObj.after(html);
			 trObj.find("input").val("").attr("disabled","disabled");
			 batchObj.siblings(".hd").html(JSON.stringify(detail));
			 bounce_Fixed2.cancel();
		 } else {
			 layer.msg(msg);
		 }
	 }
 }
 function delGoods(obj){
	 let icon = obj.parents("tr").data("icon");
	 $(".reviewTr" + icon).remove()
	 obj.parents("tr").remove();
 }
 function chargeOrdInit (orderId, _this){
	 var order = JSON.parse(_this.siblings(".hd").html());	// 订单明细
	 $(".checkWrap .fa-circle").attr("class", "fa fa-circle-o");
	 ordObj["spanObj"] = _this ;
	 ordObj["orderId"] = orderId ;
	 ordObj["cusId"] = order.customer_id ;
	 ordObj["invoiceRequire"] = order.invoice_require ;
	 getAddressCon();
	 bounce.show($("#chargeOrdInit"));
 }
 /*creator:lyt 2023/9/7 0007 下午 6:21 */
 function chargeOrdInitOk(){
	 let len = $(".checkWrap .fa-circle").length;
	 let info = ``;
	 if (len === 1) {
		 let type = $(".checkWrap .fa-circle").data("type");
		 switch(type) {
			 case 1 :
				 adjustGoods(2);
				 break;
			 case 2 :
			 case 3 :
				 info = JSON.parse(ordObj["spanObj"].siblings(".hd").html());
				 $("#updateOrderInfo").data("type", type);
				 $("#updateOrderInfo input").each(function (){
					 let name = $(this).attr("name");
					 if (name === 'sign_date') {
						 $(this).val(new Date(info[name]).format("yyyy-MM-dd"));
					 } else if (name === 'invoice_require') {
						 if(info.invoice_require === '1'){ // 增专
							 $(this).val("需开具增值税专用发票");
						 }else if(info.invoice_require === '2'){
							 $(this).val("需开具其他发票");
						 }else if(info.invoice_require === '3'){ //不开票
							 $(this).val("不开发票");
						 } else {
							 $(this).val("不开发票");
						 }
					 } else {
						 $(this).val(info[name]);
					 }
				 });
				 if (type === 2) {
					 gettProductionPrincipal(info["production_principal"]);
					 $("#updateOrderInfo .bonceHead span").html("修改订单号或订单收到的日期");
					 $("#updateOrderInfo .orderNumber").removeAttr("disabled");
					 $("#updateOrderInfo #OrderReceivedDate").removeAttr("disabled");
					 $("#updateOrderInfo .principal").attr("disabled", "disabled");
					 $(".upSn").show();$(".upPl").hide();
				 } else {
					 gettProductionPrincipal();
					 $("#updateOrderInfo .bonceHead span").html("更换生产方的评审负责人");
					 $("#updateOrderInfo .principal").removeAttr("disabled");
					 $("#updateOrderInfo .orderNumber").attr("disabled", "disabled");
					 $("#updateOrderInfo #OrderReceivedDate").attr("disabled", "disabled");
					 $(".upSn").hide();$(".upPl").show();
				 }
				 bounce_Fixed.show($("#updateOrderInfo"));
				 break;
			 case 4 :
				 $.ajax({
					 type: "get",
					 url: "/orders/review/reviewDetails?orderId="+ ordObj["orderId"] ,
					 success: function(res){
						 bounce_Fixed.show( $("#editGoodPriceList") );
						 var items = res["items"]["data"] ;
						 let html = ``;
						 ordObj["reviewer_name"] = res["orderBase"][0]["principal_name"] || "";
						 $("#editGoodList tr:gt(0)").remove(  ) ;
						 if( items && items.length > 0 ){
							 for(var i = 0 ; i < items.length ; i++ ){  // 循环每个商品组
								 html +=
									 `<tr>
										<td>${items[i].outer_sn}</td>
										<td>${items[i].outer_name}</td>
										<td>${items[i].unit}</td>
										<td>${items[i].unit}</td>
										<td>${items[i].amount}</td>
										<td>${new Date(items[i].delivery_date).format("yyyy-MM-dd")}</td>
										<td>${items[i].delivery_address}</td>
										<td>
											<span class='ty-color-blue' onclick='editGoodsPrice( $(this) )'>修改</span>
											<span class='hd'>${JSON.stringify(items[i])}</span>
										</td>
									</tr>`;
							 }
						 }
						 $("#editGoodList").append( html ) ;
					 }
				 });
				 break;
		 }
	 }
 }
 function updateOrderInfoSure(){
	 let type = $("#updateOrderInfo").data("type");
	 var item ={"id": ordObj["orderId"]}
	 if (type === 2) {
		 item.operation = 4;//4-修改订单号或订单收到的日期,5-更换生产方的评审负责人,6-更改订单明细要货信息,7-更改订单明细价格元素
		 item.sn = $("#updateOrderInfo .orderNumber").val();
		 item.sDate = $("#OrderReceivedDate").val();
	 } else {
		 item.operation = 5;
		 item.productionPrincipal = $("#updateOrderInfo .principal").val();
	 }
	 $.ajax({
		 url: "../sales/updateSlOrdersBase.do",
		 data: item ,
		 success: function (status){
			 if(status == 1){
				 bounce_Fixed.cancel();
				 bounce.cancel()
				 getOrdList( 1 , 10) ;
				 let principal = $("#updateOrderInfo .principal option:selected").text();
				 if(type === 2) {
					 layer.msg(`修改成功！${principal}将收到系统消息，但本修改无需重新评审！`);
				 } else if(type === 3) {
					 layer.msg("修改成功！"+ principal +"将收到系统消息，并需重新评审本订单。");
				 }
			 }else{
				layer.msg("修改失败！");
			 }
		 }
	 })
 }

 // creator: 张旭博，2018-05-30 10:09:04，修改价格
 function editGoodsPrice(obj) {
	 let info = JSON.parse(obj.siblings(".hd").html());
	 let invoiceRequire = info.invoice_require
	 $("#newGood").data("obj", obj);
	 $("#newGood").data("source", 2);
	 $("#newGood .invoice1").hide()
	 $("#newGood .modifyPrice").hide();
	 $("#newGood input").attr("disabled","disabled");
	 $("#newGood select").attr("disabled","disabled");
	 $("#newGood .priceMod input").removeAttr("disabled");
	 $("#newGood .priceMod select").removeAttr("disabled");
	 if(invoiceRequire === '1'){ // 增专
		 $("#newGood .rate").val(info.tax_rate)
		 $("#newGood .noPrice").val(info.unit_price_notax)
		 $("#newGood .price").val(info.unit_price)
		 $("#newGood .invoice1").show()
	 }else if(invoiceRequire === '2'){
		 $("#newGood .price").val(info.unit_price_invoice)
	 }else if(invoiceRequire === '3'){ //不开票
		 $("#newGood .price").val(info.unit_price_noinvoice)
	 } else {
		 $("#newGood .price").val(info.unit_price_notax)
	 }
	 $("#newGood .outerName")		.val(info.outer_name);
	 $("#newGood .outerSn")			.val(info.outer_sn);
	 $("#newGood .goodNum")			.val(info.amount);
	 $("#newGood .unit")			.val(info.unit);
	 $("#newGood .currentStock")	.val(info.current_stock);	//当前库存
	 $("#newGood .minimumStock")	.val(info.minimumi_stock);	//最低库存
	 $("#newGood .priceMemo")		.val(info.priceDesc);
	 $("#newGood .requireDate")		.val(new Date(info.delivery_date).format('yyyy-MM-dd'));
	 $("#newGood .receiveAddress")	.html(`<option value="${info.address_id}">${info.delivery_address}</option>`);
	 $("#newGood .goodNum_stock")	.val(0)	//货物件数
	 $(".invoiceTip").html(JSON.stringify(info));
	 bounce_Fixed2.show( $("#newGood") ); //
 }
 //creator:lyt 2023/9/18 0018 上午 8:23 调整要货需求-新增商品确定||修改商品价格确定
 function submitGoods(){
	 let isok = false, sum = 0;
	 let source = $("#newGood").data("source"); // 1=新增， 2=订单修改-修改价格
	 var goodsInfo = $("#newGood .invoiceTip").html();
	 goodsInfo = JSON.parse(goodsInfo);
	 let requireDate 	= $("#DateOfArrival").val();
	 let goodNum = $("#newGood .goodNum").val();
	 let receiveAddressId 	= $("#newGood .receiveAddress2").val();
	 if (source === 1) {
		 if(goodsInfo.length ===0){
			 isok = true
			 layer.msg('<i class="fa fa-exclamation-triangle"></i>请输入或选择存在商品代号！');
		 }else if(requireDate == ""  ){
			 isok = true
			 layer.msg('<i class="fa fa-exclamation-triangle"></i>请选择要求到货时间 ！');
		 }else if(goodNum == "" || goodNum == undefined){
			 isok = true
			 layer.msg('<i class="fa fa-exclamation-triangle"></i>请输入商品数量！');
		 }else if(receiveAddressId === "" || receiveAddressId == undefined){
			 isok = true
			 layer.msg('<i class="fa fa-exclamation-triangle"></i>请选择收货地点！');
		 }
		 if(isok ){
			 return false
		 }
	 }
	 let receiveAddress 	= $("#newGood .receiveAddress2").find("option:selected").html();
	 let invoiceRequire = ordObj["invoiceRequire"];
	 let rate = $("#newGood .rate").val();
	 let price = $("#newGood .price").val();
	 let noPrice = $("#newGood .noPrice").val();
	 let priceMemo = $("#newGood .priceMemo").val();
	 if(invoiceRequire === '1'){ // 增专
		 if(!price || !noPrice){
			 layer.msg('<i class="fa fa-exclamation-triangle"></i>请输入含税单价或者不含税单价！');
			 return false
		 }
		 sum += noPrice * (1 + rate/100) * goodNum;
		 goodsInfo.priceInfo = { "unitPrice":price, "taxRate":rate, "unitPriceNotax":noPrice }
	 }else if(invoiceRequire === '2'){
		 if(!price){
			 layer.msg('<i class="fa fa-exclamation-triangle"></i>开普通发票的开票单价！');
			 return false
		 }
		 sum += price * goodNum ; // 增普和其他
		 goodsInfo.priceInfo = { "unitPriceInvoice":price }
	 }else if(invoiceRequire === '3'){ //不开票
		 if(!price){
			 layer.msg('<i class="fa fa-exclamation-triangle"></i>请输入不开票单价！');
			 return false
		 }
		 sum += price * goodNum ; // 增普和其他
		 goodsInfo.priceInfo = { "unitPriceNoinvoice":price }
	 }
	 goodsInfo.invoiceRequire = invoiceRequire
	 goodsInfo.priceInfo.priceDesc = priceMemo

	 goodsInfo.amount = goodNum
	 goodsInfo.delivery_date = requireDate
	 goodsInfo.delivery_address = receiveAddress
	 goodsInfo.address_id = receiveAddressId
	 let ttl = $("#newGood").data("goodsType"); // 返回1 通用 2 专属
	 goodsInfo.goodsType = ttl

	 if(source === 1) {//1=新增， 2=订单修改-修改价格
		 let len = $(".goReviewList tbody tr").length;
		 let num = $(".goReviewList tbody .reviewTr").length + 1;
		 let adjustSource = $("#adjustSource").val();
		 goodsInfo.outer_sn = goodsInfo.outerSn;
		 goodsInfo.outer_name = goodsInfo.outerName;
		 let html =
			 `<tr class="reviewTr newGoodsItem" data-icon="${num}" data-isnew="1">
                            <td>${goodsInfo.outerSn}</td>
                            <td>${goodsInfo.outerName}</td>
                            <td>${goodsInfo.unit}</td>
                            <td>${goodsInfo.amount}</td>
                            <td>${new Date(goodsInfo.delivery_date).format("yyyy-MM-dd")}</td>
                            <td>${goodsInfo.delivery_address}</td>
                            <td></td>
                            <td></td>
                            <td><input type='text' class='cell1' placeholder="请录入"/></td>
                            <td><input type='text' class='cell2' readonly id='goods${len}'  placeholder="请选择"></td>
                            <td>
                            	<select class="addressInfo" onchange="giveTitle($(this))">${setAddress()}</select>
                            </td>
                            <td>
                                ${adjustSource === '1'? "<button class='ty-color-blue' onclick='batchDelivery( $(this) )'>分批发货</button>": ""}
                            	<span class='ty-color-red' onclick='delGoods( $(this) )'>删除</span>
                            <span class='hd'>${JSON.stringify(goodsInfo)}</span>
                            </td>
                            </tr>
                        `;
		 $(".goReviewList tbody").append( html ) ;
		 laydate.render({elem: "#goods" + len, min: 0});
	 } else if(source === 2) {//1=新增商品， 2=订单修改-修改价格
		 var temp = {
			 "id"				: goodsInfo["sr_id"] , // 商品对照id
			 "sid"				: goodsInfo["sid"] ,// 明细id
			 "SlInnerSn"		: goodsInfo["inner_sn"]  ,
			 "SlInnerSnName"	: goodsInfo["name"] ,
			 "SlOuterSn"		: goodsInfo["outer_sn"] ,
			 "SlOutterSnName"	: goodsInfo["outer_name"] ,
			 "SlUnit"			: goodsInfo["unit"] ,
			 "SlUnitPrice"		: goodsInfo["unit_price"] ,
			 "deliveryDate": goodsInfo.delivery_date,
			 "amount":goodsInfo.amount,
			 "address":goodsInfo.delivery_address,
			 "addressId":goodsInfo.address_id,
			 ...goodsInfo.priceInfo
		 };
		 temp.unitPriceNotax = (temp.unitPriceNotax && temp.unitPriceNotax.toString()) || (temp.unitPriceInvoice && temp.unitPriceInvoice.toString())
		 let goodsInfoList = [temp];
		 let item = {
			 "operation": 7, //4-修改订单号或订单收到的日期,5-更换生产方的评审负责人,6-更改订单明细要货信息,7-更改订单明细价格元素
			 "save_slOrders": JSON.stringify({
				 "id": ordObj["orderId"],
				 "contractAmount": sum
			 }),
			 "save_slOrdersItemList": JSON.stringify(goodsInfoList)
		 }
		 $.ajax({
			 url:"../sales/updateSlOrders.do",
			 data: item,
			 type:"post",
			 dataType:"json",
			 success:function (data) {
				 layer.msg("修改成功！本修改与生产无关，生产不会收到系统消息，且无需重新评审！");
				 if($("#editGoodPriceList").is(":visible")) chargeOrdInitOk();
			 }
		 })
	 }
	 bounce_Fixed2.cancel();
 }
 // create: hxz，2021-05-27 10:16:13 计算价格
 function setprice(num) { // 1- 不含税，2-含税
	 let invoiceRequire = Number(ordObj["invoiceRequire"] || 0);
	 if(invoiceRequire === 1){
		 let noprice = Number($("#newGood .noPrice").val());
		 let price = Number($("#newGood .price").val());
		 let rate = Number($("#newGood .rate").val());
		 if(num === 1){
			 if(rate > 0 && noprice > 0){
				 price = noprice * (rate/100 + 1) ;
				 $("#newGood .price").val(parseFloat(price.toFixed(10)));
			 }
			 if(noprice === 0){
				 $("#newGood .price").prop("disabled", false)
			 }else{
				 $("#newGood .price").prop("disabled", true)
			 }
		 }else if(num === 2){
			 if(rate > 0 && price > 0){
				 noprice = price / (rate/100 + 1) ;
				 $("#newGood .noPrice").val(parseFloat(noprice.toFixed(10)));
			 }
			 if(price === 0){
				 $("#newGood .noPrice").prop("disabled", false)
			 }else{
				 $("#newGood .noPrice").prop("disabled", true)
			 }
		 }else{
			 let priceDis =  $("#newGood .price").prop("disabled");
			 let nopriceDis =  $("#newGood .noPrice").prop("disabled");
			 if(priceDis){ // 按照不含税算
				 if(rate > 0 && noprice > 0){
					 price = noprice * (rate/100 + 1) ;
					 $("#newGood .price").val(parseFloat(price.toFixed(10)));
				 }
			 }else{ // 按照含税算
				 if(rate > 0 && price > 0){
					 noprice = price / (rate/100 + 1) ;
					 $("#newGood .noPrice").val(parseFloat(noprice.toFixed(10)));
				 }
			 }
		 }
	 }
 }
 let addressList = [];
 function getAddressCon() {
	 var customerID = ordObj["cusId"];
	 $.ajax({
		 async:false,
		 url:"../sales/getAddressListByCondition.do",
		 data:{"cusId":customerID},
		 success:function (data) {
			 var addrList = data["data"] || [];
			 addressList = addrList;
		 }
	 })
 }
 function setAddress(list) {
	 let arr = addressList;
	 if(list && list.length > 0) {
		 arr = list;
	 }
	 var addrListStr = '<option value="">请选择</option>';
	 for (var i = 0; i < arr.length; i++) {
		 addrListStr += '<option value="' + addressList[i].id + '">' + addressList[i].address + '</option>';
	 }
	 return addrListStr;
 }
 function addMore(){
	 let len = $(".batchItem").length + 1;
	 let html = `
	   <div class="clear batchItem">
           <div class="itemCn">
               <div class="orderItemTitle">到货日期 <i class="xing">*</i></div>
               <input type="text" class="form-control batchDeliveryDate${len}"  value="" placeholder="请录入"
                      name="DateOfArrival" autocomplete="off">
           </div>
           <div class="itemCn itemGap">
               <div class="orderItemTitle">该日期需到货的数量<i class="xing">*</i>
               <span class="redLinkBtn ty-right" onclick="delBatchItem($(this))">删除本行</span>
               </div>
               <input type="text" class="form-control batchAmount" name="amount" placeholder="请录入" />
           </div>
       </div>`;
	 $("#batchDelivery .batchWrapper").append(html);
	 laydate.render({elem: ".batchDeliveryDate" + len, min: 1});
 }
 function delBatchItem(obj){
	 obj.parents(".batchItem").remove();
 }
 //creator:lyt 2023/9/12  临时调价
 function modifyPrice(){
	 let outerName = $("#newGood .outerName").val();
	 if(outerName.length === 0){
		 layer.msg("请先选择商品")
		 return false
	 }
	 let modify = $("#newGood .modifyPrice").data('modify')
	 let afterModify = 0
	 if(modify === 0){
		 afterModify = 1 // 允许调价
		 $(".modify").show();
		 $("#newGood .rate").prop("disabled",false)
		 $("#newGood .noPrice").prop("disabled",false)
		 $("#newGood .price").prop("disabled",false)
	 } else {
		 afterModify = 0 // 不允许调价
		 $(".modify").hide();
		 $("#newGood .rate").prop("disabled",true).val( $("#newGood .rate").data('val'))
		 $("#newGood .noPrice").prop("disabled",true).val( $("#newGood .noPrice").data('val'))
		 $("#newGood .price").prop("disabled",true).val( $("#newGood .price").data('val'))
	 }
	 $("#newGood .modifyPrice").data('modify', afterModify);
 }
 /*creator:lyt 2023/9/18 0018 上午 9:42 */
 function checkBtn(){
	 if ($(".checkBtn .fa-circle-o").length > 0) {
		 $(".checkBtn .fa").addClass("fa-circle").removeClass("fa-circle-o");
	 } else {
		 $(".checkBtn .fa").addClass("fa-circle-o").removeClass("fa-circle");
	 }
 }
 /*creator:lyt 2023/9/18 0018 上午 7:56 评审记录不在此项目开发*/
 function chargeHistory(){
 }
 function stopOrd(){
 }
 /* Creator : 侯杏哲 2017-07-25 将代号翻译成明文*/
 function chargeCode( code){
	 switch(code){
		 case "1" : return "商品代号";  break ;
		 case "2" : return "外部名称";  break ;
		 case "3" : return "产品图号";  break ;
		 case "4" : return "内部名称";  break ;
		 case "5" : return "商品代号";  break ;
		 case "6" : return "外部名称";  break ;
		 case "7" : return "产品图号";  break ;
		 case "8" : return "内部名称";  break ;
		 case "9" : return "单位";  break ;
		 case "10" : return "含税单价";  break ;
		 case "11" : return "备注";  break ;
		 default: return "" ;
	 }
 }
 /* Creator : 侯杏哲 2017-07-25 根据code 返回对应的键值  */
 function chargeCodeVal( code , info){
	 switch(code){
		 case "1" : return info["slOuterSn"]|| "" ; break ;
		 case "2" : return info["slOutterSnName"] || "" ; break ;
		 case "3" : return info["slInnerSn"] || ""; break ;
		 case "4" : return info["slInnerSnName"] || ""; break ;
		 case "5" : return info["slOuterSn"] || "" ; break ;
		 case "6" : return info["slOutterSnName"] || "" ; break ;
		 case "7" : return info["slInnerSn"] || "" ; break ;
		 case "8" : return info["slInnerSnName"] || "" ; break ;
		 case "9" : return info["slUnit"] || "" ; break ;
		 case "10" : return info["slUnitPrice"] || "" ; break ;
		 case "11" : return info["memo"] || "" ; break ;
		 default : return "" ; break ;
	 }
 }
 // Creator : 侯杏哲 2017-07-25 筛选 - 单选按钮、复选按钮 的切换
 $("div.orderItemTiny").on( "click", function(){
	 var klass = $(this).children("i").attr("class") ;
	 switch( klass ){
		 case "fa fa-circle-o":
			 $(this).siblings().each(function(){
				 $(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
			 }) ;
			 $(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1");
			 break ;
		 case "fa fa-dot-circle-o":
			 break ;
		 case "fa fa-check-square-o":
			 $(this).children("i").attr("class" , "fa fa-square-o").attr("isSet" , "0");
			 break ;
		 case "fa fa-square-o":
			 $(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1");
			 break ;
		 default:
			 break ;
	 }
 });
 // 筛选数据的显示
 function showMess( _this ){
	 var info = _this.parent().attr("info") ;
	 info = JSON.parse(info);
	 var offsetObj = _this.offset();
	 var leftObj =offsetObj.left;
	 var topObj = offsetObj.top ;
	 $("#tipCon").animate({ left: leftObj+ "px" , top:topObj+"px"  }, 10).show();
	 var check = role["check"];
	 var str = "" ;
	 for(var k = 5 ; k <=11 ; k++ ){
		 var code = String(k) ;
		 var isIn = $.inArray(code, check);
		 if(isIn != -1){
			 var codeName = chargeCode( code ) ;
			 var codeVal = chargeCodeVal( code , info ) ;
			 str += "<span class='tipitemCon'>"+ codeName +"："+ codeVal +"</span>" ;
		 }
	 }
	 $("#tipitem").html(str);
 }
 function hideMess( _this ){
	 $("#tipCon").hide().offset({ left:0 , top:0 });
 }
 // ====================== 筛选功能 END ======================================
 // create : hxz 2021-1-34 显示主页面
 function showMainCon(num){
	 //$("#showMainConNum").val(num);
	 $(".mainCon").hide();$(".mainCon" + num).show();
 }
 //  create: hxz 2021-05-11 保留三位小数
 function tofixed3(obj) {
	 let v = obj.value
	 v = v.replace(/[^\d.]/g, "");
	 v = v.replace(/^\./g, "");
	 v = v.replace(/\.{2,}/g, ".");
	 v = v.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".")
	 let vArr = v.split('.')
	 if(vArr[0].length > 9){ // 最多9位
		 vArr[0] = String(vArr[0]).substr(0,9);
	 }
	 v = vArr[0]
	 if(vArr.length > 1){
		 if(vArr[1].length > 4){
			 vArr[1] = vArr[1].substr(0,4);
		 }
		 v += '.' + vArr[1];
	 }
	 obj.value = v
 }
 // 时间控件初始化
 laydate.render({elem: "#dateGoods"});
 laydate.render({elem: "#DateOfArrival"});
 laydate.render({elem: "#OrderReceivedDate"});
 laydate.render({elem: ".batchDeliveryDate1", min: 1});
 laydate.render({elem: ".batchDeliveryDate2", min: 1});
























