/**
 * Created by 张旭博 on 2017/5/17.
 */
$(function () {
    // 获取订单信息
    goPage(0);
    getOrderList(1, 10);
    // 订阅开票回调
    sphdSocket.subscribe('invoiceApplication', invoiceApplicationAllTick, null, 'user');
    sphdSocket.subscribe('invoiceNotice', invoiceApplicationAllTick, null, 'user');
    $("#invoiceSet2 .setItem").click(function(){
        $("#invoiceSet2 .fa").attr("class","fa fa-circle-o");
        $(this).children(".fa").attr("class","fa fa-dot-circle-o");
    })
    // 获取客户的联系人
    $(".chooseCusCon222").click(function () {
        let target = $(this).data("target");
        $("#target").val(target);
        getCusContactList($(".customerName").data("cusId"));
    });
    // 客户名称匹配
    $("#cusSearchInput").on("click keyup",function (e) {
        $(this).next().show();
        e.stopPropagation();
        // 设置下拉列表（通过缓存数据设置）
        setCusOption();
    });
    $("body").click(function(){
        console.log("body 点击")
        let cusName = $("#cusSearchInput").val();
        let cusId = $("#cusSearchInput").data("cusId");
        let vis = $(".cusSearchItems").is(":visible")
        if(vis){
            if(cusId == ""){
                layer.msg("请先选择客户！！");
            }
            $(".cusSearchItems").hide();
        }

    })
    $(".cusSearchItems").on("click", "option", function(){
        matchCus( $(this) );
        $(".cusSearchItems").hide();
        console.log('这里是 点击事件')
    })
    $("#invoiceExecutorSelect").on('click', ".changeDot",function() {
        $(this).children("i").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
        $(this).siblings(".changeDot").find("i").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
    });
    $(".goodsSelect").on('click', ".changeDot",function() {
        if($(this).children("i").hasClass("fa-dot-circle-o")) {
            $(this).children("i").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
        } else {
            $(this).children("i").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
        }
    });
    $("body").on('click', '.funBtn', function () {
        var that = $(this)
        var name = that.data("fun");
        switch (name) { //新增计量单位
            case 'toggleTwo':
                toggleTwo(that)
                break;
            default:
                window[name] && window[name]($(this), name);
        }
    });
});
function toggleTwo(thisObj) {
    let type = thisObj.data("type")
    thisObj.parents(".typeCC").find(".fa").attr("class","fa fa-circle-o")
    thisObj.find('.fa').attr("class","fa fa-dot-circle-o")
    let inputObj = thisObj.parents(".selectItems").find("input")
    if(type === 'zi'){
        inputObj.removeAttr('disabled')
    }else{
        inputObj.attr('disabled','disabled')
        inputObj.val("")
    }
}

//  creator: hxz  2021-07-07  点击选择客户
function matchCus( optionObj){
    //清空商品列表
    $(".goodList tbody").html("");
    if(optionObj.val() == ""){
        $("#gsBd").html("") ;
        $(".orderTotal").val(0);
        $(".newGood").prop("disabled",true);
        layer.msg("请选择有效客户！");
        $(".receiveAddress").val('');
        $(".customerId").val('');
        $(".invoiceRequire").data("invoice", '').val('')
        return false ;
    }
    $(".customerId").val(optionObj.attr("code"));
    let cusId = optionObj.val();
    $(".customerName").val(optionObj.html()).data("cusId" , cusId);
    let invoiceRequire = optionObj.attr("invoice")
    $(".invoiceRequire").data("invoice", invoiceRequire).val(formatInviceRequire(invoiceRequire) )

    //存储该用户下的所有商品信息（调用接口）
    if(Number(invoiceRequire) > 0 && Number(invoiceRequire) < 4){
        setOuterCon(cusId, invoiceRequire);

    }
    //设置该客户下的所有地址（调用接口）
    setAddressCon(cusId, 'new');

}
//  creator: hxz  2021-07-07  设置客户下拉列表
function setCusOption() {
    let cusName = $("#cusSearchInput").val();
    $("#cusSearchInput").data("cusId", "");
    if(cusName === ""){
        $(".cusSearchItems option").show()
    }else{
        let count = 0
        let Obj = {}
        $(".cusSearchItems option").each(function(){
            let itemName = $(this).html();
            if(itemName.indexOf(cusName) > -1){
                $(this).show();
                count++ ;
                if(itemName == cusName){ // 完全相等
                    matchCus($(this));
                }
            }else{
                $(this).hide();
            }
        })
        if(count === 0){
            layer.msg('请通过录入有效的客户名进行选择！！')
            // $("#cusSearchInput").val("");
            $(".cusSearchItems option").show();
        }
    }
}


//  creator: hxz  2018-07-18 终止订单 - 自定义查询
function searchBtn(event) {
    stop(event);
    $(".searchCon").toggle();
}

//  creator: hxz  2018-09-27  按照用户名称和代号查询订单
function searchOedsByCus(type) {
    pageInfo["search"] = true ;
    pageInfo["cur"] = 1 ;
    pageInfo["type"] = type ;
    pageInfo["cusCodeOrName"] = $("#se"+type).val() ;
    if(type == 0){ // 未完成订单
        getOrderList(1,10) ;
    }else if(type == 1){ // 已终止订单
        getOrdEndList(1,10) ;
    }
}
//  creator: hxz  2018-07-18 终止订单 - 自定义查询确定
function searchDIY() {
    $("#searchBtn").addClass("ty-btn-active-blue").siblings("span").removeClass("ty-btn-active-blue");
    pageInfo["cur"] = 1 ;
    pageInfo["search"] = false ;
    pageInfo["status"] = "DIY" ;
    getOrdEndList(1, 10 );
    $(".searchCon").hide();
}
//  creator: hxz  2018-07-18 切换主页面与终止订单页面
function goPage(type) {
    if (type === 0) { // 0 - 主页面 ，
        $(".mainArea").show().siblings().hide();
        pageInfo["type"] = 0 ;
    } else if (type === 1) { //  1 - 终止订单页面
        $(".endArea").show().siblings().hide();
        pageInfo = { "cur":1 , "status":"", "search":false , "type":1 } ;
        // type - 未完结或终止的订单 ； status - 时间查询的状态（"":默认查本月 ； "year":查本年 ； "DIY":自定义时间 ）； "search": 是否按照客户查询
        getOrdEndList(1, 10 );
        $("#yearBtn").removeClass("ty-btn-active-blue") ;
        $("#searchBtn").removeClass("ty-btn-active-blue") ;
    } else if(type === 2) { // 2 - 开票申请页面
        $(".ticketOpen").show().siblings().hide();
        $("#goodsSelectBtn").data('invoicebefore', 0)
        $("#applicationByFinance").data('invoicebefore', 0)
    } else if(type === 3){ // 3- 开票之前的
        $(".ticketBeforeOpen").show().siblings().hide();
    }
}
// creator: hxz  2018-07-18  跳转 终止订单列表页
function endBtn(){
    goPage(1) ;
}
// creator: hxz  2018-07-18  终止订单列表 - 查本年
function getList_year(obj) {
    obj.addClass("ty-btn-active-blue").siblings("span").removeClass("ty-btn-active-blue");
    pageInfo["cur"] = 1 ;
    pageInfo["status"] = "year" ;
    pageInfo["startDate"] = "" ;
    pageInfo["endDate"] = "" ;
    pageInfo["search"] = false ;
    getOrdEndList(1, 10 );
}
function getOrdEndList(currPage, pageSize ) {
    var data = {"currPage": currPage, "pageSize": pageSize, "state": 6 } ;
    if(pageInfo["status"] == "year"){
        data["year"] = 1 ;
    }else if(pageInfo["status"] == "DIY"){
        var startDate_se = $("#searchStart").val();
        var endDate_se = $("#searchEnd").val();
        data["start"] = startDate_se ;
        data["end"] = endDate_se ;
    }
    if(pageInfo["search"]){ // 按照关键字（客户名称代号）查询
        data["keyword"] = pageInfo["cusCodeOrName"] ;
        $("#isSearch").html("符合搜索条件") ;
    }else{
        $("#se"+ pageInfo["type"]).val("") ;
        $("#isSearch").html("") ;
    }
    $.ajax({
        url: "../sales/getSlOrders.do",
        data:  data ,
        success: function (data) {
            var yearEnd = formatTime(data["yearEnd"]).substr(0,10) ;
            var yearStart = formatTime(data["yearStart"]).substr(0,10);
            var monthEnd = formatTime(data["monthEnd"]).substr(0,10);
            var monthFirstDay = formatTime(data["monthFirstDay"]).substr(0,10);
            var startDate = "" , endDate = "" ;
            if(pageInfo["status"] == "year"){ // 按年查询
                startDate = yearStart.replace('-', '年'); startDate = startDate.replace('-', '月');
                endDate = yearEnd.replace('-', '年'); endDate = yearEnd.replace('-', '月');
            }else if(pageInfo["status"] == ""){ // 默认查本月
                startDate = monthFirstDay.replace('-', '年'); startDate = startDate.replace('-', '月');
                endDate = monthEnd.replace('-', '年'); endDate = endDate.replace('-', '月');
            }else if(pageInfo["status"] == "DIY"){ // 自定义时间查询
                startDate = startDate_se.replace('-', '年'); startDate = startDate.replace('-', '月') ;
                endDate = endDate_se.replace('-', '年'); endDate = endDate.replace('-', '月') ;
            }

            $("#endOrdDuring").html(startDate + "日 至 "+ endDate +"日间") ;
            var totalPage = data["totalPage"];
            var slOrdersList = data["data"];
            var totalRows = data["totalRows"] || 0;
            $("#ye_orderManage2").html("");
            $(".orderTableEnd tbody").html("");
            var jsonStr = JSON.stringify({});
            $("#ordNum2").html(totalRows) ;
            setPage($("#ye_orderManage2"), currPage, totalPage, "orderManage2", jsonStr) ;
            if (slOrdersList != undefined && slOrdersList.length > 0) {
                var str2 = "" ;
                for (var i = 0; i < slOrdersList.length; i++) {
                    var No = pageSize * (currPage - 1) + i + 1 ;
                    str2 += '<tr id="' + slOrdersList[i].id + '">' +
                        '<td>' + No + '</td>' +
                        '<td>' + formatTime(slOrdersList[i]["sign_date"]) + '</td>' +
                        '<td>' + (slOrdersList[i].sn || "") + '</td>' +
                        '<td>' + slOrdersList[i]["customer_name"] + '</td>' +
                        '<td>' + formatTime(slOrdersList[i]["create_date"])  + '</td>' +
                        '<td>' + formatTime(slOrdersList[i]["terminate_time"])  + '</td>' +
                        '<td>'+ (slOrdersList[i]["terminate_name"] || "") +'</td>';
                    if (slOrdersList[i]["keywords"] == 0) {
                        str2 += '<td class="ty-td-control"><span class="ty-color-gray" >查看</span></td> <td></td>';
                    } else {
                        str2 += '<td class="ty-td-control"><span class="ty-color-gray">查看</span></td>' +
                            '<td class="ty-td-control"><span class="ty-color-blue" onclick="seeOrderBtn($(this))">查看订单详情</span></td>';
                    }
                    var may = Number((slOrdersList[i]["may"] && slOrdersList[i]["may"] ) || "0")*100;
                    var yet = Number( (slOrdersList[i]["yet"] && slOrdersList[i]["yet"] ) || "0" )*100;
                    var sign = Number( (slOrdersList[i]["sign"] && slOrdersList[i]["sign"] ) || "0" )*100;
                    var invoiced = Number( (slOrdersList[i]["invoiced"] && slOrdersList[i]["invoiced"] ) || "0" )*100;
                    var collect = Number( (slOrdersList[i]["collected"] && slOrdersList[i]["collected"] ) || "0" )*100;
                    if(may > 100 ){ may = 100 ; }
                    if(yet > 100 ){ yet = 100 ; }
                    if(sign > 100 ){ sign = 100 ; }
                    if(invoiced > 100 ){ invoiced = 100 ; }
                    str2 += '<td class="ty-td-control"><span class="ty-color-blue" onclick="lineChart($(this) , 1)">' + may.toFixed(1) + '%</span></td>' + // 可发货
                        '<td class="ty-td-control"><span class="ty-color-blue btn2" onclick="lineChart($(this) , 2)" >' + yet.toFixed(1) + '%</span></td>' + // 已发货
                        '<td class="ty-td-control"><span class="ty-color-blue btn3" onclick="lineChart($(this) , 3)">' + sign.toFixed(1) + '%</span></td>' + // 已签收
                        '<td class="ty-td-control"><span class="ty-color-blue btn3" onclick="lineChart($(this) , 4)">' + invoiced.toFixed(1) + '%</span></td>' + // 已开票
                        '<td class="ty-td-control"><span class="ty-color-blue btn3" onclick="lineChart($(this) , 5)">' + collect.toFixed(1) + '%</span></td></tr>'; // 已回款
                }
                $(".orderTableEnd tbody").html(str2);
            }
        }
    })
}
var pageInfo = {} ; // 查询的分页信息， 未完成已完成都用
var curTime = ''
// updator: hxz，2018-07-18 ，获取 未完结订单列表
function getOrderList(currPage, pageSize) { // currPage   当前页  pageSize    每页条数
    pageInfo["type"] == 0 ;
	$(".orderTable tbody").html("");
	$.ajax({
		url:"../sales/getSlOrders.do",
		data:{ "currPage":currPage , "pageSize":pageSize , "keyword": pageInfo["cusCodeOrName"] } ,
		success:function( data ){
            pageInfo["cur"] = currPage ;
			var al_order = data["al_orders"];  // 大于0标识有已完成订单
			var ter_order = data["ter_orders"]; // 大于0 标识有已终止订单
            if(ter_order > 0){
                $("#endBtn").attr("class" , "ty-btn ty-btn-big ty-btn-blue ty-circle-5").attr("onclick" , "getList_year($(#endBtn))") ;
            }
            if(al_order > 0){ $("#finishedBtn").attr("class" , "ty-btn ty-btn-big ty-btn-blue ty-circle-5") ; }else{ $("#finishedBtn").attr("class" , "ty-btn ty-btn-big ty-btn-gray ty-circle-5") ; }
            if(ter_order > 0){ $("#endBtn").attr("class" , "ty-btn ty-btn-big ty-btn-blue ty-circle-5").attr("onclick","endBtn()") ; }else{ $("#endBtn").attr("class" , "ty-btn ty-btn-big ty-btn-gray ty-circle-5").removeAttr("onclick") ; }
			var totalPage = data["totalPage"];
            var totalRows = data["totalRows"]||0;
            $("#ordNum").html(totalRows);
            var cur = currPage;
            var slOrdersList = data["data"];
			$("#ye_orderManage").html("");
			var jsonStr = JSON.stringify({ }) ;
			setPage( $("#ye_orderManage") , cur ,  totalPage , "orderManage" ,jsonStr );
			$("#orderManage_body").html("");
			if(slOrdersList !=undefined && slOrdersList.length > 0 ){
                var orderTableStr = "", str2 = "";
                for (var i = 0; i < slOrdersList.length; i++) {
                    var No = pageSize * (cur - 1) + i + 1;
                    str2 += '<tr id="' + slOrdersList[i].id + '">' +
                        '<td>' + No + '</td>' +
                        '<td>' + formatTime(slOrdersList[i].sign_date) + '</td>' +
                        '<td>' + (slOrdersList[i].sn || "") + '</td>' +
                        '<td>' + slOrdersList[i].customer_name + '</td>' +
                        '<td>' + formatTime(slOrdersList[i].create_date) + '</td>' +
                        '<td class="ty-td-control">';
                    if (slOrdersList[i]["keywords"] == 0) {
                        str2 += '<span class="ty-color-gray"  >评审记录查看</span>';
                    } else {
                        //str2 += '<span class="ty-color-green" onclick="chargeHistory(' + slOrdersList[i]["id"] + ' , $(this) )">评审记录查看</span>';
                        str2 += '<span class="ty-color-gray"  >评审记录查看</span>';
                    }
                    var may = Number((slOrdersList[i]["may"] && slOrdersList[i]["may"] ) || "0") ;
                    var yet = Number((slOrdersList[i]["yet"] && slOrdersList[i]["yet"] ) || "0");
                    var sign = Number((slOrdersList[i]["sign"] && slOrdersList[i]["sign"] ) || "0");
                    var invoiced = Number( (slOrdersList[i]["invoiced"] && slOrdersList[i]["invoiced"] ) || "0" )*100;
                    var collect = Number( (slOrdersList[i]["collected"] && slOrdersList[i]["collected"] ) || "0" )*100;
                    may *= 100 ;yet *= 100 ;sign *= 100 ;
                    var mayStr =" onclick=\"lineChart($(this) , 1)\"" ;
                    var yetStr =" onclick=\"lineChart($(this) , 2)\"" ;
                    var signStr =" onclick=\"lineChart($(this) , 3)\"" ;
                    var invoicedStr =" onclick=\"lineChart($(this) , 4)\"" ;
                    var collectStr =" onclick=\"lineChart($(this) , 5)\"" ;
                    if(may > 100 ){ may = 100 ; }  if(may >0 && may <0.01){ may = 0.01 ;     } if(may == 0  ){ mayStr = ""   ;   }
                    if(yet > 100 ){ yet = 100 ; }  if(yet >0 && yet <0.01){ yet = 0.01 ;     } if(yet == 0  ){ yetStr = ""   ;   }
                    if(sign > 100 ){ sign = 100 ; }if(sign>0 && sign <0.01 ){ sign = 0.01  ; } if(sign== 0  ){ signStr = ""  ;   }
                    if(invoiced > 100 ){ invoiced = 100 ; }if(invoiced>0 && invoiced <0.01 ){ invoiced = 0.01  ; } if(invoiced== 0  ){ invoicedStr = ""  ;   }
                    if(collect > 100 ){ collect = 100 ; }if(collect>0 && collect <0.01 ){ collect = 0.01  ; } if(collect== 0  ){ collectStr = ""  ;   }
                    str2 += '<span class="ty-color-green" onclick="seeOrderBtn($(this))">订单详情查看</span>' +
                        '<span class="ty-color-blue" onclick="changeOrderBtn($(this))">订单修改</span>' +
                        '<span class="ty-color-red" onclick="endOrderBtn($(this))">订单终止</span>' +
                        '<span class="ty-color-blue" onclick="invoiceExecutorSelect($(this))">开票申请</span>' +
                        '<span class="hd">'+ JSON.stringify(slOrdersList[i]) +'</span>' +
                        '</td>' +
                        '<td class="ty-td-control"><span class="ty-color-blue" '+ mayStr +' >' + may.toFixed(1) + '%</span></td>' + // 可发货
                        '<td class="ty-td-control"><span class="ty-color-blue btn2" '+ yetStr +' >' + yet.toFixed(1) + '%</span></td>' + // 已发货
                        '<td class="ty-td-control"><span class="ty-color-blue btn3" '+ signStr +'>' + sign.toFixed(1) + '%</span></td>' + // 已签收
                        '<td class="ty-td-control"><span class="ty-color-blue btn3" '+ invoicedStr +'>' + invoiced.toFixed(1) + '%</span></td>' + // 已开票
                        '<td class="ty-td-control"><span class="ty-color-blue btn3" '+ collectStr +'>' + collect.toFixed(1) + '%</span></td>'; // 已回款
                }
                $(".orderTable:eq(0) tbody").html(str2);
                $(".orderTable:eq(1) tbody").html(orderTableStr);
			}
        },
        // complete: function( xhr ) {
        //     let time = new Date(xhr.getResponseHeader('Date'));
        //     curTime = time
        //     laydate.render({elem: '#DateOfArrival', min : time.format('yyyy-MM-dd hh:mm:ss'), });
        //     loading.close();
        // }
	})
}

// creator: hxz，2018-12-26  开票提示确定
function ticketTipOk() {
    var iObj = $("#ticketTip").find("i");
    var isYes = iObj.hasClass("fa-check-square-o");
    var enable = 1;
    if (isYes) {
        enable = 0;
    }
    $.ajax({
        "url": "../invoice/updateEnbale.do",
        "data": {"enable": enable},
        success: function (res) {
            var state = res["state"];
            if (state) {
                if(enable == 0){
                    layer.msg("设置成功！");
                }
                bounce.cancel();
                goPage(2);
                getTicketInfo();
            } else {
                layer.msg("设置失败，请重试！");
            }
        },
        complete:function() {
        }
    });


}

var financeRule = []; // 财务设置的发票规则， 后面开票校验用
// creator: hxz，2019-02-20  开票申请
function ticketBtn(_thisObj) {
    if(chargeRole("超管")){
        layer.msg("对不起，您没有权限进行开票申请！");
        return false ;
    }
    bounce.cancel();
    applyTicketList = []; // 初始化开票的数组
    goosList = [];
    endOrderTr = _thisObj.parents("tr");
    let divisionWay = $("#operatorSelect").val();
    $.ajax({
        "url": "../invoice/getRemind.do",
        success: function (res) {
            var state = res["state"];
            var list = res["financeInvoiceSettings"];
            financeRule = list ;
            if (state == 0) {
                layer.msg("财务尚未进行发票设置！");
                loading.close();
            } else {
                if (divisionWay === "1") {
                    if (state === 1) {
                        bounce.show($("#ticketTip"));
                        var str = "";
                        if (list && list.length > 0) {
                            for (var i = 0; i < list.length; i++) {
                                var amountLimited = list[i]["amountLimited"] + "元";
                                var cate = list[i]["category"];
                                if (cate == 1) {
                                    amountLimited = "不含税" + amountLimited;
                                } else if (cate == 2) {
                                    amountLimited = "含税" + amountLimited;
                                }
                                str += "<tr>" +
                                    "    <td>" + category(cate) + "</td>" +
                                    "    <td>" + (list[i]["linesLimited"] || "——") + "</td>" +
                                    "    <td>" + amountLimited + "</td>" +
                                    "</tr>";
                            }
                        }
                        $("#ticketTip tbody").children(":gt(0)").remove();
                        $("#ticketTip tbody").append(str);
                        loading.close();
                    } else if (state === 2) {
                        goPage(2);
                        getTicketInfo();
                    }
                } else if (divisionWay === "2") {
                    $(".goodsSelect i").attr("class","fa fa-circle-o");
                    goPage(2);
                    getTicketInfo();
                }
            }
        },
        complete:function() {

        }
    })
}
//  creator: hxz，2019-02-20 获取某订单开发票的详情
function getTicketInfo() {
    $("#ticket1").hide();
    $("#goodsSelectBtn").data('invoicebefore',0);
    $("#applicationByFinance").data('invoicebefore',0);
    let divisionWay = $("#operatorSelect").val();
    var orderId = endOrderTr.attr("id");
    $.ajax({
        "url": "../sale/orderDetail.do",
        "data": {"orderId": orderId},
        success: function (res) {
            $("#goodsList").data('res', res) ;
            var order = res["order"][0];
            var items = res["items"];
            if (items && order) {
                $("#ticketCustomerName").html(order["customer_name"]).data("id", order["id"]);
                $("#ticketCode").html(order["code"]);
                $("#ticketSn").html(order["sn"]);

                var str = "";
                var invoice_require = Number(order.invoice_require)
                $("#ticketSn").data("invoicerequire", invoice_require)
                let invoiceCat =  '不开票'
                if(invoice_require === 1){
                    invoiceCat = '需开增值税专用发票'
                }else if(invoice_require === 2){
                    invoiceCat = '需开其他发票'
                }
                $("#ticketType").html(invoiceCat);
                for (var i = 0; i < items.length; i++) {
                    items[i].invoice_require = Number(order.invoice_require);
                    var deliveryAndSignList = items[i]["deliveryAndSignList"];
                    var sendNum = 0, signNum = 0;
                    if (deliveryAndSignList && deliveryAndSignList.length > 0) {
                        for (var j = 0; j < deliveryAndSignList.length; j++) {
                            sendNum += Number(deliveryAndSignList[j]["out_fact"] || 0) *10000;
                            signNum += Number(deliveryAndSignList[j]["sign_amount"] || 0) *10000;
                        }
                        sendNum = sendNum / 10000;
                        signNum /= 10000;
                    }
                    var unit_price_notax = "";
                    var rate = "— —";
                    var cat = category(items[i]["invoice_category"]);
                    if (invoice_require == 1) {
                        rate = Number((items[i]["tax_rate"] && items[i]["tax_rate"] ) || "0") + "%";
                        unit_price_notax = "含税" + items[i]["unit_price"].toFixed(2) + "元";
                    } else if (invoice_require == 2) {
                        unit_price_notax = "含税" + items[i]["unit_price_invoice"].toFixed(2) + "元";
                    } else {
                        unit_price_notax = items[i]["unit_price_noinvoice"].toFixed(2) + "元";
                    }
                    // 已开票的数量
                    var invoicedList = items[i]["invoicedList"];
                    var storage = items[i]["storage"], storageNum = 0 ; // 暂存的发票
                    if(storage && storage.length >0){
                        for(var w = 0 ; w < storage.length ; w++){
                            storageNum += Number(storage[w]["item_quantity"]);
                        }
                    }
                    var max_amount = items[i]["amount"]*10000 - Number(items[i]["invoice_amount_yet"]*10000) - storageNum*10000; // 最大可申请数量
                    max_amount /= 10000


                    if(invoice_require !== 3){
                        // if(invoicedList && invoicedList.length >0){
                        //     for(var d = 0 ; d < invoicedList.length ; d++){
                        //         max_amount -= invoicedList[d]["item_quantity"]*10000 ;
                        //     }
                        // }
                        let arrP = String(max_amount).split('.');
                        if(arrP.length >1){
                            if(arrP[1].length > 4){
                                max_amount = arrP[0] + '.' + arrP[1].substr(0,4)
                            }
                        }
                    }else {
                        max_amount = "— —"
                    }
                    if(max_amount !== "— —"){
                        max_amount = parseFloat(max_amount)
                    }
                    str += "<tr itemid='" + items[i]["id"] + "' srid='"+ items[i]["sr_id"]  +"'>" +
                        "     <td onclick='toogleI($(this))'>" +
                        "       <i class='fa fa-square-o'></i>" +
                        "       <span class='hd'>"+ JSON.stringify(items[i]) +"</span>" +
                        "    </td>" +
                        "    <td>" + items[i]["outer_sn"] + "</td>" +
                        "    <td>" + items[i]["outer_name"] + "</td>" +
                        //"    <td>" + items[i]["inner_sn"] + "</td>" +
                        //"    <td>" + items[i]["name"] + "</td>" +
                        "    <td>" + (items[i]["unit"] || '') + "</td>" +
                        "    <td>" + items[i]["amount"] + "</td>" +
                        "    <td>" + unit_price_notax + "</td>" +
                        //"    <td>" + invoice1  + "</td>" +
                        //"    <td>" + invoiceCat + "</td>" +
                        //"    <td>" + rate + "</td>" +
                        //"    <td>" + (new Date(items[i]["delivery_date"]).format("yyyy-MM-dd")) + "</td>" +
                        "    <td>" + sendNum + "</td>" +
                        "    <td>" + signNum + "</td>" +
                        "    <td>" + max_amount + "</td>" +
                        "    <td class='ty-td-gray'></td>" +
                        "</tr>";
                }
                $("#ticketTab tbody").html(str);
                if (divisionWay === "1") {
                    $(".divisionBySale").show().siblings().hide();
                    setTimer1 = setInterval(function () {
                        ticketFun();
                    }, 500);
                } else if (divisionWay === "2") {
                    $(".divisionBySale").hide().siblings().show();
                }
            } else {
                layer.msg("获取数据失败！");
            }
        }
    })
}
// creator : hxz 2019-0424 工具方法 - 送达方式
function chargeDeliveryWay(way) {
    if(way == "A"){
        return "快递";
    }else if(way == "B"){
        return "随身携带" ;
    } else if(way == "C"){
        return "货运" ;
    } else{
        return ""
    }
}
// creator : hxz 2019-02-21 工具方法 - 校验 开票的按钮是否可点
function ticketFun() {
    var selectNum = 0;
    $("#ticketTab tbody").children("tr").each(function () {
        var isSelect = $(this).children(":eq(0)").children("i").hasClass("fa-check-square-o");
        if (isSelect) {
            var num = $(this).children(":last").children("input").val();
            var maxNum = $(this).children(":eq(8)").html();
            if (Number(num) > 0) {
                if (Number(maxNum) < Number(num)) {
                    var name = $(this).children(":eq(2)").html();
                    layer.msg("商品 " + name + " <br>本次申请数量大于可申请的最大数量！");
                    $(this).children(":last").children("input").val(maxNum);
                }
                selectNum++;
            }
        }
    });
    if (selectNum > 0) {
        $("#goodsSelectBtn").removeClass("ty-btn-gray").addClass("ty-btn-blue");
    } else {
        $("#goodsSelectBtn").addClass("ty-btn-gray").removeClass("ty-btn-blue");
    }

}
// creator : hxz 2019-02-15  切换开票勾选状态
function toogleI(obj) {
    let info = JSON.parse(obj.find(".hd").html());
    let invoice_require = info.invoice_require;
    if (invoice_require === 3) {
        layer.msg("本商品不开票");
        return false;
    }
    let item = ``, itemCat = ``;
    obj.parents("tbody").children("tr").each(function () {
        var isSelect = $(this).children(":eq(0)").children("i").hasClass("fa-check-square-o");
        if (isSelect) {
            item = JSON.parse($(this).children(":eq(0)").find(".hd").html());
            itemCat = item.invoice_require;
            return false;
        }
    });
    if (itemCat != "" && itemCat != invoice_require) {
        var selectCat = "";
        if(invoice_require === 1){
            selectCat = '增值税专用发票'
        }else if(invoice_require === 2){
            selectCat = '普通发票'
        }
        layer.msg("本次请选择开 " + selectCat + " 的商品进行开票，其他发票请下次再选择");
        return false;
    }
    var max = obj.siblings(":eq(9)").html();
    if (max == 0) {
        layer.msg("本商品可申请数量上限数目为0，不能开票！");
        return false;
    }
    var iObj = obj.children("i");
    var isOK = iObj.hasClass("fa-square-o");
    if (isOK) {
        iObj.attr("class", "fa fa-check-square-o");
        obj.siblings(":last").html("<input type='text' onkeyup='tofixed3(this)'>").removeClass("ty-td-gray");
    } else {
        iObj.attr("class", "fa fa-square-o");
        obj.siblings(":last").html("").addClass("ty-td-gray");
    }

}
// creator : hxz 2019-02-15  切换是否再次提示的状态
function toggleIcon(obj) {
    var iObj = obj.children("i");
    var isYes = iObj.hasClass("fa-square-o");
    if (isYes) {
        iObj.attr("class", "fa fa-check-square-o");
    } else {
        iObj.attr("class", "fa fa-square-o");
    }

}

// creator: hxz 2019-02-21 切换开发票提示框 下一步的操作项
function togggle2(obj, type) {
    if(type > 3){
        if( obj.children("i").hasClass("fa fa-check-square-o")){
            obj.children("i").attr("class", "fa fa-square-o");

        }else{
            obj.children("i").attr("class", "fa fa-check-square-o");
        }
    }else{
        obj.siblings("div").children("i").attr("class", "fa fa-circle-o");
        obj.children("i").attr("class", "fa fa-dot-circle-o");
        $("#tik1").val(type);
    }
}

// create:hxz 2023-09-22 预开票
function beforeInvoice() {
    let type = $("#operatorSelect").val(); //1-销售 2 -财务
    var selectNum = 0;
    let ids = []
    let gsList = []
    $("#ticketTab tbody").find(".fa-check-square-o").each(function () {
        let info = $(this).siblings('.hd').html()
        info = JSON.parse(info)
        info.kkNum = Number($(this).parents('tr').find('input').val())
        let indx = ids.indexOf(info.id)
        if(indx > -1){
            gsList[indx]['kkNum'] += info.kkNum;
        }else{
            gsList.push(info)
            ids.push(info.id)
        }
    });

    let idsStr = ids.toString()
    $.ajax({
        'url':'../sales/pd/preInvoice',
        'data':{ 'ids': idsStr },
        success:function(res){
            let invoiceTwoList = res.incMerchandises.data || []
            let invoiceHavNewDateList = res.noticeMerchandises.data || []
            let priceHavNewDateList = res.priceChangeMerchandises.data || []
            if(invoiceTwoList.length === 0 && invoiceHavNewDateList.length === 0 && priceHavNewDateList.length === 0){
                let type = $("#operatorSelect").val(); //1-销售 2 -财务
                if(type == 1){
                    $("#goodsSelectBtn").data('invoicebefore', 1)
                    $(".ticketOpen").show().siblings().hide();
                    goodsSelectBtn()
                    return false
                }else{
                    $("#applicationByFinance").data('invoicebefore', 1);
                    $(".ticketOpen").show().siblings().hide();
                    applicationByFinance()
                    return false
                }
            }
            goPage(3)
            if(invoiceTwoList.length === 0 ){
                $(".invoiceTwo").hide()
            }else{
                $(".invoiceTwo").show()
                let invoiceTwoStr = ''
                invoiceTwoList.forEach(item1 => {
                    invoiceTwoStr += `
                <tr>
                    <td>${item1.outer_sn}/${item1.outer_name}/${item1.specifications}/${item1.model}</td>
                    <td class="ty-td-control">
                        <span class="txt"></span>
                        <span class="hd">${ JSON.stringify(item1) }</span>
                        <span class="ty-color-blue" onclick="twoEdit($(this))" data-type="huo" data-sta="info">编辑</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="txt">${ item1.spemodel || '' }</span>
                        <span class="hd">${ JSON.stringify(item1) }</span>
                        <span class="ty-color-blue" data-id="${ item1.id }" onclick="twoEdit($(this))" data-type="model" data-sta="info">编辑</span>
                    </td>
                </tr>
                `
                })
                $(".invoiceTwoTB tr:gt(1)").remove()
                $(".invoiceTwoTB").append(invoiceTwoStr)
            }
            if(invoiceHavNewDateList.length === 0){
                $(".invoiceHavNewDate").hide()
            }else{
                $(".invoiceHavNewDate").show()
                let invoiceHavNewDateListStr = ''
                invoiceHavNewDateList.forEach(item1 => {
                    invoiceHavNewDateListStr += `
                 <tr>
                   <td>${item1.outerSn}/${item1.outerName}/${item1.specifications}/${item1.model}</td>
                    <td>${item1.oldInvoice} </td>
                    <td>${item1.newInvoice} </td>
                    <td data-id="${ item1.id }">
                        <span class="faTick" onclick="faTickTgl($(this))"><i class="fa fa-circle-o" data-type="1"></i>采用原数据</span>
                        <span class="faTick" onclick="faTickTgl($(this))"><i class="fa fa-circle-o" data-type="2"></i>采用新数据</span>
                    </td>
                 </tr>
                `
                })
                $(".invoiceHavNewDateTB tr:gt(1)").remove()
                $(".invoiceHavNewDateTB").append(invoiceHavNewDateListStr)
            }
            if(priceHavNewDateList.length === 0){
                $(".priceHavNewDate").hide()
            }else{
                $(".priceHavNewDate").show()
                let priceHavNewDateListStr = ''
                priceHavNewDateList.forEach(item1 => {
                    let optionStr = ''
                    let priceList = item1.prices || []
                    priceList.forEach( priceItem => {
                        optionStr += `
                        <option value="${ priceItem.price }">${ priceItem.priceMemo }</option>
                    `
                    })
                    let selectNum = 0
                    console.log('gsList=', gsList)
                    console.log('item1.id =', item1.id)
                    gsList.forEach( iiTm => {
                        if(iiTm.sr_id === item1.id){
                            selectNum = iiTm.kkNum
                        }
                    })
                    priceHavNewDateListStr += `
                 <tr>
                    <td>${item1.outerSn}/${item1.outerName}/${item1.specifications}/${item1.model}</td>
                    <td>${item1.unit || ''}</td>
                    <td>${ selectNum }</td>
                    <td><select class="price" data-id="${ item1.id }"><option value="">请选择</option>${ optionStr }</select></td>
                    <td class="ty-td-control"><span data-id="${ item1.id }" class="ty-color-blue" onclick="openTick($(this))">查看</span></td>
                 </tr>
                `
                })
                $(".priceHavNewDateListTB tr:gt(0)").remove()
                $(".priceHavNewDateListTB").append(priceHavNewDateListStr)
            }
        }
    })
}
function faTickTgl(thisObj) {
    thisObj.parent().find('.fa').attr('class', 'fa fa-circle-o')
    thisObj.children('.fa').attr('class', 'fa fa-dot-circle-o')
}
function chargeOkBtn() {
    let dataItems = [] // [{"itemId":358,"operation":1}, {"itemId":359,"operation":2},...]
    let priceItems = [] // [{"itemId":358,"price":4.52}, {"itemId":359,"price":6},...]
    let errorNum = 0
    if($(".invoiceTwoTB").is(":visible") ){
        $(".invoiceTwoTB tr:gt(1)").each(function(){
            let txt = $(this).find('.txt').html()
            if(txt.length == 0){
                errorNum++;
            }
        })
    }

    if($(".invoiceHavNewDateTB").is(":visible") ) {
        $(".invoiceHavNewDateTB tr:gt(1)").each(function(){
            let faObj = $(this).find('.fa-dot-circle-o')
            if(faObj.length == 0){
                errorNum++;
            }else{
                let type = faObj.data("type")
                let id = faObj.parents('td').data("id")
                dataItems.push({ "itemId":id,"operation":type })
            }

        })
    }
    console.log('$(".priceHavNewDateListTB").is(":visible") =', $(".priceHavNewDateListTB").is(":visible") )
    if($(".priceHavNewDateListTB").is(":visible") ) {
        $(".priceHavNewDateListTB .price").each(function () {
            let price = $(this).val();
            if(price == ''){
                errorNum++;
            }else{
                let id = $(this).data('id');
                priceItems.push({ "itemId":id,"price":price })
            }
        })
    }


    if(errorNum > 0){
        layer.msg('还有必填项没有填写！')
        return false;
    }
    $.ajax({
        'url':'../sales/pd/preInvoiceSure',
        'data':{ 'dataItems': JSON.stringify(dataItems),  'priceItems': JSON.stringify(priceItems) },
        success:function(res){
            if(res.code === 200){
                let type = $("#operatorSelect").val(); //1-销售 2 -财务
                if(type == 1){
                    $("#goodsSelectBtn").data('invoicebefore', 1)
                    goodsSelectBtn(priceItems)
                }else{
                    $("#applicationByFinance").data('invoicebefore', 1);
                    applicationByFinance()
                }
            }else{
                layer.msg('操作失败，请重试！')
            }

        }
    })
}
// create: hxz 2023-10-02 获取开票记录
function openTick(thisObj){
    let id = thisObj.data('id');
    var orderId = endOrderTr.attr("id");
    let data = {
        orderId: orderId,
        id: id,
    }
    $.ajax({
        'url':'../sale/orderInvoiced.do',
        "data": data,
        success:function(res){
            let list = res.data || []
            if(list.length === 0){
                layer.msg('暂无开票记录')
                return false
            }
            let str = ''
            list.forEach( item => {
                str += `
                <tr>
                    <td>${ new Date(item.invoice_date).format('yyyy-MM-dd') }</td>
                    <td>${ item.invoice_no || ''}</td>
                    <td>${ item.item_unit || '' }</td>
                    <td>${ item.item_quantity || ''}</td>
                    <td>${ (item.unit_price && item.unit_price.toFixed(2) )|| ''}</td>
                </tr>
                `
            })
            $(".openTickLogTB tr:gt(0)").remove()
            $(".openTickLogTB").append(str)
            bounce.show($("#openTickLog"))

        }
    })

}
// create:hxz 2023-06-10 商品的两项开票资料 编辑
var twoEditObj = null
function twoEdit(thisObj) {
    twoEditObj = thisObj
    bounce_Fixed.cancel()
    const typeA = thisObj.data('type'); // huo- 货物或应税劳务、服务名称 ; model - 规格型号
    const sta = thisObj.data('sta'); // editInfo- 修改基本信息 ; info - 查看详情的
    let type = typeA === 'huo' ? 1 : 2
    let info = JSON.parse(thisObj.siblings('.hd').html())
    info.typeA = typeA
    info.sta = sta
    let merchandise = info.id
    $("#twoEdit").data("info", info)
    $("#foo1").html(info.outer_sn)
    $("#foo2").html(info.outer_name)
    $("#foo3").html(info.specifications)
    $("#foo4").html(info.model)
    let txt = thisObj.siblings('.txt').html()
    $.ajax({
        'url':'../product/getPdMerchandiseInvoice.do',
        'data':{ merchandise: merchandise, type: type },
        success:function(res){
            const data = res.data
            if(data && data.id){
                $(".yesEdit").show()
                $(".noEdit").hide()
                $(".huoVal").html(txt)
                $(".modelVal").html(txt)
            }else{
                $(".yesEdit").hide()
                $(".noEdit").show()
            }
            bounce.show($("#twoEdit"))
            $(`#twoEdit .fa`).attr("class", "fa fa-circle-o");
            $(`#twoEdit input`).val("");
            $(`#twoEdit .${ typeA }`).show().siblings().hide()
        }
    })
}
function twoEditOk() {
    const info = $("#twoEdit").data("info")
    const selectFa = $(`#twoEdit .${ info.typeA }`).find(".fa-dot-circle-o")
    if(selectFa.length === 0){
        layer.msg('需将此数据修改为哪一项？请选择')
        return false
    }
    let userDefined = ''
    const funBtn = selectFa.parent(".funBtn")
    if(funBtn.data('type') === 'zi'){
        userDefined = funBtn.siblings('input').val()
    }
    let data = {
        merchandise: info.id,
        type: info.typeA === 'huo' ? 1 : 2,
        pattern: selectFa.data('val'),
        userDefined: userDefined ,
        memo:''
    }
    $.ajax({
        'url':'../product/editPdMerchandiseInvoice.do',
        "data": data,
        success:function(res){
            if(res.success === 1){
                layer.msg('操作成功')
                let txt = formatPatten(data,info)
                twoEditObj.siblings("span.txt").html(txt || '')
                bounce.cancel()
            }else{
                layer.msg('操作失败')
            }
        }
    })

}
function formatPatten(data, info) {
    let str = ''
    if (!data){
        return false
    }
    if(data.type === 1){ // huowu
        switch (Number(data.pattern)){
            case 1: // 采用商品代号的当前数据
                str = info.outer_sn
                break;
            case 2: // 采用商品名称的当前数据
                str = info.outer_name
                break;
            case 3: // 上述代号与名称都使用，代号在前
                str = info.outer_sn + ' ' + info.outer_name
                break;
            case 4: // 上述代号与名称都使用，名称在前
                str = info.outer_name + ' ' + info.outer_sn
                break;
            case 5: // 自定义
                str = data.userDefined
                break;
        }
    }else{
        switch (Number(data.pattern)){
            case 1: // 发票上的“规格型号”为空，无需填写
                str = ''
                break;
            case 2: // 采用规格的当前数据
                str = info.specifications
                break;
            case 3: // 采用型号的当前数据
                str = info.model
                break;
            case 4: // 上述规格与型号都使用，规格在前
                str = info.specifications + ' ' + info.model
                break;
            case 5: // 上述规格与型号都使用，规格在前
                str = info.model + ' ' + info.specifications
                break;
            case 6: // 自定义
                str = data.userDefined
                break;
        }
    }
    return str
}
// creator: hxz，2018-12-26  开票申请 - 选择完商品
var applyTicketList = []; // 记录本次开票的数组
var goosList = []; // 记录本张开票的商品信息
var ticketInfoOkTimer = 0;
function goodsSelectBtn(priceItems) {
    if ($("#goodsSelectBtn").hasClass("ty-btn-blue")) {
        let invoicebefore = $("#goodsSelectBtn").data('invoicebefore');
        if(invoicebefore === 0){ // 需要预开票
            beforeInvoice()
            return false
        }
        $(".contractTip").hide();
        $("#cheContract").attr("class", "fa fa-check-square-o");
        var count = 0, str = "", amountAll = 0 , rateAmountAll = 0 ;
        goosList = []; // 初始化本张发票数据内容
        $("#tab1 tbody").children("tr:gt(0)").remove();
        var isAllNum = true;
        let invoice_require = $("#ticketSn").data("invoicerequire")
        $("#ticketTab tbody").children("tr").each(function () {
            var trIndex = $(this).index()
            var isSelect = $(this).children(":eq(0)").children("i").hasClass("fa-check-square-o");
            if (isSelect) {
                var mtinfo = JSON.parse($(this).find(".hd").html())
                if (invoice_require === 1 || invoice_require === 2) {
                    count++;
                    var name = $(this).children(":eq(2)").html();
                    var num = $(this).children(":eq(9)").children("input").val();
                    if (Number(num) > 0) {
                    } else {
                        layer.msg("请录入商品 " + name + " 本次申请开票的数量！");
                        isAllNum = false;
                        return false;
                    }
                    var unit = mtinfo.unit;
                    var itemid = $(this).attr("itemid");
                    var srid = $(this).attr("srid");

                    var info = {
                        "ordersItem": itemid,
                        "noticeOption": 0,
                    };

                    var price = '';
                    if(invoice_require === 1){ //增专
                        if(mtinfo.unit_price_notax){

                        }else if(mtinfo.unit_price){
                            let r = mtinfo.tax_rate
                            let up = mtinfo.unit_price_notax
                            mtinfo.unit_price_notax = mtinfo.unit_price / (100+r)*100
                        }
                        price = mtinfo.unit_price_notax
                    }else if(invoice_require === 2){ // 增普
                        price = mtinfo.unit_price_invoice
                    } else{ //
                        price = mtinfo.unit_price_noinvoice
                    }
                    if(priceItems){
                        priceItems.forEach(pI => {
                            console.log('priceItems pI=', pI)
                            if(pI.itemId == srid){
                                if(price == pI.price){
                                    // 原来的价格
                                }else{
                                    price = pI.price
                                    info.noticeOption = 1;
                                }
                            }
                        })
                    }
                    var rateStr = "";
                    var rate = "";
                    var amount = price * num;
                    amountAll += Number(amount.toFixed(2));
                    var rateAmount = "";
                    if (invoice_require === 1 || invoice_require === 2) {
                        rate = Number((mtinfo["tax_rate"] && mtinfo["tax_rate"] ) || "0");
                        rateStr = rate + "%";
                        rateAmount = rate * amount / 100;
                        rateAmountAll += Number(rateAmount.toFixed(2));
                    }
                    info.amount = num;
                    info.money = amount.toFixed(2);
                    info.price = price;
                    info.rate = rate;
                    info.invoiceAmount = rateAmount.toFixed(2);

                    goosList.push(info);

                    str += `
                    <tr class="ty-ticket-no-border gsListItem">
                        <td class="ty-ticket-txt-black">${ trIndex + 1 }</td>
                        <td class="ty-ticket-txt-black">${ mtinfo.outer_sn }/${ mtinfo.outer_name }/${ mtinfo.specifications } /${ mtinfo.model } </td>
                        <td class="ty-ticket-txt-blue">${ mtinfo.outer_name }</td>
                        <td class="ty-ticket-txt-blue">${ mtinfo.specifications }</td>
                        <td class="ty-ticket-txt-blue">${ unit }</td>
                        <td class="ty-ticket-txt-blue">${ num }</td>
                        <td class="ty-ticket-txt-blue">${ price }</td>
                        <td class="ty-ticket-txt-blue">${ amount.toFixed(2) }</td>
                        <td class="ty-ticket-txt-blue">${ rateStr }</td>
                        <td class="ty-ticket-txt-blue">${ (rateAmount&&rateAmount.toFixed(2))  }</td>
                    </tr>
                    `
                }
            }
        });
        if(!isAllNum){
            return false;
        }
        if (invoice_require === 3) {
            $("#msTips2 .msTip").html("您申请开票的商品已被设定为不开票，如果现在确实需要开票，您需先到商品档案中修改相应的商品信息。");
            bounce.show( $("#msTips2")); return false;
        }
        var catNum = 0 , catStr = "";
        if (invoice_require == 3) {
            catNum = 3 ;
            catStr = "<span>增值税普通票/其他普通票</span>" ;
        }else if(invoice_require === 1){
            catNum = 1 ;
            catStr = "增值税专用发票" ;
        }else if(invoice_require === 2){
            catNum = 2 ;
            catStr = "<span>增值税普通票/其他普通票</span>" ;
        }
        var ruleLine = 0 ,  hasAttachment = false ,  ruleSum = 0 ; // 发票的金额和行数限制  canLine- 有无附页， 有的话 就不校验行数
        for(var i = 0 ; i < financeRule.length ; i++){
            if(catNum == financeRule[i]["category"]){
                hasAttachment = financeRule[i]["hasAttachment"] ;
                if(!hasAttachment){
                    ruleLine = financeRule[i]["linesLimited"] || 0 ;
                }
                ruleSum = financeRule[i]["amountLimited"] ; // 增专 不含税 ，增普含税 ， 普通总的金额
            }
        }
        if(!hasAttachment){
            if(ruleLine<count){
                $("#msTips2 .msTip").html("您所选择的商品类别总数已超单张发票行数上限 ，请重新选择。");
                bounce.show( $("#msTips2")); return false;
            }
        }
        if(ruleSum < amountAll){
            $("#msTips2 .msTip").html("您本次选择的商品总额已超单张发票金额上限 ，请重新选择。");
            bounce.show( $("#msTips2")); return false ;
        }
        $("#tabss .gsListItem").remove()
        $("#tabss .gsList").after(str)
        // $("#tab1").append(str);
        $("#count").html(count);
        $("#cat").html(catStr);
        let sumee = Number(amountAll) + Number(rateAmountAll)
        $("#amount").html( sumee.toFixed(2) );
        $("#amountUp").html( convertCurrency(sumee.toFixed(2)) )
        $("#tik1").val("");
        $("#ticketInfoOk").attr("class", " ty-right ty-btn bounce-cancel ty-btn-big ty-circle-5").removeAttr("onclick");
        ticketInfoOkTimer = setInterval(function(){
            if($("#tik1").val() == ""){
                $("#ticketInfoOk").attr("class", "ty-btn ty-right ty-btn-gray ty-btn-big ty-circle-5").removeAttr("onclick");
            }else{
                $("#ticketInfoOk").attr("class", "ty-btn ty-right bounce-ok ty-btn-big ty-circle-5").attr("onclick","ticketInfoOk()");
            }
        },200);
        $("#ticketInfo").find(".fa").attr("class" , "fa fa-circle-o");
        if(catNum == 1){
            $("#tab1").find("tr").each(function() {
                $(this).children("td:eq(6)").show() ;
                $(this).children("td:eq(7)").show() ;
            })
        }else{
            $("#tab1").find("tr").each(function() {
                $(this).children("td:eq(6)").hide() ;
                $(this).children("td:eq(7)").hide() ;
            })
        }
        const orderDetails = $("#goodsList").data('res') ;
        const customer = orderDetails.customer
        $("#tabss .supName").html(customer.invoiceName || '')
        $("#tabss .supNo").html(customer.taxpayerID || '')
        $("#tabss .addATel").html(`${ customer.invoiceAddress } / ${ customer.telephone }`)
        $("#tabss .bankAcc").html(`${ customer.bankName } / ${ customer.bankNo }`)
        bounce.show($("#ticketInfo"));
        goPage(2)
    }
}
// creator : hxz 2021-05-25  查看开票资料
function getInvoiceMessage() {
    let customerID = $("#ticketCustomerName").data("id")
    $.ajax({
        "url":"../sales/getCustomerInvoice",
        "data":{ "id": customerID},
        success:function (res) {
            if(res['data']){
                let info = res['data'] && res['data'][0]
                bounce_Fixed.show($("#InvoiceMessage"));
                $("#InvoiceMessage .invoiceName").html(info.full_name);
                $("#InvoiceMessage .invoiceAddress").html(info.address);
                $("#InvoiceMessage .telephone").html(info.telephone);
                $("#InvoiceMessage .bankName").html(info.bank_name);
                $("#InvoiceMessage .bank_no").html(info.bank_no);
                $("#InvoiceMessage .taxpayerID").html(info.taxpayerID);

            }else{
                layer.msg("未获取开票信息")
            }
        }
    })
}
// creator : hxz 2021-05-26 开票信息修改按钮
function sale_updatabtn(obj){
    if (chargeRole("超管")) { // 0 表示超管
        layer.msg("您没有此权限！");
        return false ;
    }
    let id = $("#ticketCustomerName").data("id")
    if ( id == undefined || id == ""){
        layer.msg("系统错误，刷新重试！");
        return false;
    }
    $("#updateInvoice input").val("");
    $.ajax({
        "url":"../sales/getCustomerInvoice",
        "data":{ "id": id},
        success:function (res) {
            if(res['data']){
                let info = res['data'] && res['data'][0]
                $("#updateInvoice .invoiceName").val(info.full_name);
                $("#updateInvoice .invoiceAddress").val(info.address);
                $("#updateInvoice .telephone").val(info.telephone);
                $("#updateInvoice .bankName").val(info.bank_name);
                $("#updateInvoice .bank_no").val(info.bank_no);
                $("#updateInvoice .taxpayerID").val(info.taxpayerID);
                $(".goset_update").data("invoice", info.invoice_require ).data("orgData", info.invoice_require )
                    .prev().html(formatInviceRequire2(info.invoice_require));
                bounce_Fixed.show($("#updateInvoice"));
                bounce_Fixed.everyTime('0.5s','updataInvoice',function () {
                    var state = 0,filledNum = 0;
                    $("#updateInvoice [require]:visible").each(function(){
                        if ($(this).val() != '') filledNum++;
                        if($(this).val() != $(this).data('orgData')){
                            state ++;
                        }
                    });
                    if($(".goset_update").data("invoice") != $(".goset_update").data("orgData") ){
                        state ++;
                    }
                    if(filledNum>0 && state > 0){
                        $("#updataInvoiceSure").prop("disabled",false);
                    }else{
                        $("#updataInvoiceSure").prop("disabled",true);
                    }
                });

            }else{
                layer.msg("未获取开票信息")
            }
        }
    })
}
function formatInviceRequire2(type) {
    let str = ''
    switch (Number(type)){
        case 0 :    str = '尚未设置'; break
        case 1 :    str = '需要主要为增值税专用发票'; break
        case 2 :    str = '需要主要为增值税专用发票以外的发票'; break
        case 3 :    str = '基本不需要开发票'; break
        case 4 :    str = '尚未设置'; break
        default: str = '尚未设置'
    }
    return str
}
// creator: hxz 2021-05-06 客户对发票方面的要求
function goset(thisObj) {
    let type = thisObj.data("type")
    $("#invoiceSet").data("type" , type).find(".fa-dot-circle-o").attr("class", "fa fa-circle-o")
    bounce_Fixed2.show($("#invoiceSet2"))
}
function invoiceSetOk2() {
    var selectP = $("#invoiceSet2 .fa-dot-circle-o").parent()
    let type = selectP.data("type");
    let txt = '尚未设置'
    if(Number(type) > 0  && Number(type) !== 4){
        txt = selectP.children("span").html();
    }
    $(".goset_update").data("invoice", type).prev().html(txt);
    bounce_Fixed2.cancel();
}
// creator : hxz 2021-05-26 修改开票信息确定
function updataInvoice_sure(){
    var id = $("#ticketCustomerName").data("id")
    var data = { 'id': id };
    $("#updateInvoice input").each(function(){
        var name = $(this).data('name');
        data[name] = $(this).val();
    })
    data['invoiceRequire'] =  $(".goset_update").data("invoice")
    $.ajax({
        url: "/sales/updatePdCustomerInvoice.do",
        data: data,
        type: "post",
        dataType: "json",
        success: function (data) {
            var status = data.status;
            if(status == '1'){
                bounce_Fixed.cancel();

            }
        }
    })
}
// creator : hxz 2019-02-21 开票申请 - 确定
function ticketInfoOk() {
    var type = $("#tik1").val();
    if (type == 1) { // 暂存
        applyTicket(0);
        clearInterval(ticketInfoOk);
    } else if (type == 2) { // 全部提交
        // 这里默认全部提交
        if(applyTicketList.length === 0){
            applyTicket(1);
            clearInterval(ticketInfoOk);
        }else{ // 大于一张的情况
            applyTicket(0,'nextSubmit');
        }
    } else if (type == 3) { // 放弃本发票
        console.log('333')
        bounce.cancel();
        clearInterval(ticketInfoOk);

    } else {
        layer.msg("请选择下一步操作！");
    }
}
// creator : hxz 2019-02-22 开票申请，提交审批、暂存
var invoiceApplicationData = null ;
function applyTicket(type, nextSubmit) {
    let operator = $("#operatorSelect").val();//1-销售 2 -财务
    var orderId = endOrderTr.attr("id");
    var applicationItemId = "",
        allInvoiceCount = 1, // 总张数
        allAmount = 0 , // 总额
        invoiceAmountAll = 0 , // 每条明细的税额
        allMoney = 0 ; // 本发票总额
    if(applyTicketList.length > 0){
        allInvoiceCount += applyTicketList.length ;
        for(var q = 0 ; q < applyTicketList.length ; q++){
            applicationItemId += applyTicketList[q]["applicationItemId"] + ",";
            var gL = applyTicketList[q]["goosList"] ;
            for(var k = 0 ; k < gL.length ; k++){
                allAmount += Number(gL[k]["money"]) ;
                allAmount += Number(gL[k]["invoiceAmount"]) ;
            }
        }
    }
    applicationItemId = applicationItemId.substr(0, applicationItemId.length);
    for (var j = 0; j < goosList.length; j++) {
        goosList[j]["price"] = String(goosList[j]["price"])
      var money = goosList[j]["money"]; // 金额
        var invoiceAmount = goosList[j]["invoiceAmount"]; // 税额
        allAmount += Number(money) + Number(invoiceAmount);
        allMoney += Number(money) + Number(invoiceAmount);
        invoiceAmountAll =invoiceAmountAll + Number(invoiceAmount);
    }
    var invoiceCategory = 1 ;
    let invoice_require = $("#ticketSn").data("invoicerequire")
    if (invoice_require == 2) {
        invoiceCategory = 2;
    }
    var data = {
        "orders": orderId ,
        "itemList": JSON.stringify(goosList),
        "lines": goosList.length,
        "allAmount": allAmount.toFixed(2) ,
        "allMoney": allMoney.toFixed(2) ,
        "invoiceAmount": invoiceAmountAll.toFixed(2) ,
        "invoiceCategory": invoiceCategory,
        "state": type ,
        "applicationItemId": applicationItemId ,
        "status": (type+1) ,
        "divisionWay": operator//1-销售 2 -财务
    };
    if (type == 0) { // 暂存
        data["applicationItemId"] = null ;
    }
    if (operator == 1) {
        data.allInvoiceCount = allInvoiceCount ;
    } else {
        data.allInvoiceCount = 0;
    }
    console.log(data)
    var oid = sphdSocket.user.oid
    var userId = sphdSocket.user.userID
    var seesionid = sphdSocket.sessionid
    invoiceApplicationAllTickType = 0 ;
    data["oid"] = oid ;
    data["userId"] = userId ;
    data["seesionid"] = seesionid ;
    invoiceApplicationData = data ;
    invoiceApplicationData.nextSubmit = nextSubmit
    loading.open();
    sphdSocket.send('invoiceApplication', data);
}

// creator: hxz，2018-12-26 查看开票申请单
function scanTickInfo(sourceType) {
    var orderId = endOrderTr.attr("id");
    $.ajax({
        "url": "../sale/applicationDetails.do",
        "data": { "orderId": orderId },
        success: function (res) {
            $("#tab2 tbody").children(":gt(0)").remove();
            var list = res["data"], str = "", s = 0;
            if(sourceType == "submit"){
                // 把当前的一个也展示出来

            }
            if (list && list.length > 0) {
                $("#tick1").html(list.length);
                for (var i = 0; i < list.length; i++) {
                    var no = i + 1;
                    s += list[i]["amount"];
                    str += "<tr>" +
                        "    <td>" + no + "</td>" +
                        "    <td>" + (new Date(list[i]["create_date"]).format('yyyy-MM-dd hh:mm:ss')) + "</td>" +
                        "    <td>" + category(list[i]["invoice_category"]) + "</td>" +
                        "    <td>" + list[i]["amount"].toFixed(2) + "</td>" +
                        "    <td>" + list[i]["line"] + "</td>" +
                        "    <td>" +
                        "        <span class=\"ty-color-blue\" onclick=\"scan($(this) , " + list[i]["id"] + ")\">查看</span>" +
                        "        <span class=\"ty-color-red\" onclick=\"dele($(this) , " + list[i]["id"] + ")\">删除</span>" +
                        "        <span class='hd'>"+ list[i]["id"] +"</span>" +
                        "        <span class='hd1'>"+ list[i]["item_id"] +"</span>" +
                        "    </td>" +
                        "</tr>";
                }
                bounce.show($("#scanTickInfo"));
                $("#tab2").append(str);
                $("#tickSum").html(s.toFixed(2));
            } else {
                // 没有发票（出现这种情况是因为删除发票删没了）
                bounce.cancel();
                goPage(2);
                getTicketInfo();
            }
        }
    });
    var user = JSON.parse($("#loginUser").html());
    var userName = user["userName"];
    $("#applier").html(userName);
    $("#cusTick").html($("#ticketCustomerName").html());

}

// creator: hxz，2018-12-26 查看已选的货物
function scanGoodsInfo() {
    var orderId = endOrderTr.attr("id");
    $.ajax({
        "url": "../sale/applicationGoods.do",
        "data": {"orderId": orderId},
        success: function (res) {
            var list = res["data"] ;
            $("#tab4").children("tbody").children("tr:gt(0)").remove();
            var str = "" , inSum = 0; // 开票总金额
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var deliveryAndSignList = list[i]["deliveryAndSignList"]; // 发货和签收列表
                    var deliveryNum = 0 , signNum = 0 ;
                    if(deliveryAndSignList && deliveryAndSignList.length>0){
                        for(var j = 0 ; j < deliveryAndSignList.length ; j++){
                            deliveryNum += (deliveryAndSignList[j]["out_fact"] || 0) ;
                            signNum += (deliveryAndSignList[j]["sign_amount"] || 0) ;
                        }
                    }
                    var invoicedNum = 0 , invoicedList = list[i]["invoicedList"] ; // 申请中的数量
                    if(invoicedList && invoicedList.length > 0){
                        for(var k = 0 ; k < invoicedList.length ; k++){
                            invoicedNum += invoicedList[k]["item_quantity"]
                        }
                    }
                    var invoice_amount_yet = list[i]["invoice_amount_yet"]|| 0 ; // 已申请数量
                    var soi_amount = list[i]["soi_amount"]|| 0 ; // 订购总数量
                    var appliLimit = soi_amount - invoice_amount_yet - invoicedNum ; // 可申请数量上限
                    var quantityList= list[i]["item_quantity"], quantity = 0 ; // 本次申请的数量
                    if(quantityList && quantityList.length > 0){
                        for(var w = 0 ; w < quantityList.length ; w++){
                            if(quantityList[w]["id"] == list[i]["id"]){
                                quantity += Number(quantityList[w]["item_quantity"]) ;
                            }
                        }
                    }

                    var cat = list[i]["invoice_category"];
                    var price = "";
                    var priceStr = "" ; // 单价
                    if(cat == 1){
                        price = list[i]["unit_price_notax"].toFixed(2);
                        priceStr = "不含税" + price + "元";
                        inSum += price * quantity + list[i]["invoice_amount"]; // 总金额
                    }else if(cat == 2 || cat == 3){
                        price = list[i]["unit_price"]
                        priceStr = "含税" + price + "元";
                        inSum += price * quantity ;
                    }else{
                        price = list[i]["unit_price_notax"].toFixed(2);
                        priceStr = price + "元";
                        inSum += price * quantity ;
                    }

                    str += "<tr>" +
                        "    <td>"+ list[i]["outer_name"] +"</td>" +
                        "    <td>"+ list[i]["unit"] +"</td>" +
                        "    <td>"+ priceStr +"</td>" +
                        "    <td>"+ (list[i]["has_invoice"] == 1 ? "开票" : "不开票")+"</td>" +
                        "    <td>"+ category(list[i]["invoice_category"]) +"</td>" +
                        "    <td>"+ soi_amount +"</td>" +
                        "    <td>"+ deliveryNum +"</td>" +
                        "    <td>"+ signNum +"</td>" +
                        "    <td>"+ invoice_amount_yet +"</td>" +
                        "    <td>"+ invoicedNum +"</td>" +
                        "    <td>"+ appliLimit +"</td>" +
                        "    <td>"+ quantity +"</td>" +
                        "</tr>" ;
                }
            }
            $("#tab4").append(str);
            bounce.show($("#scanGoodsInfo"));
            $("#inNum").html($("#applicationNum").html());
            $("#inSum").html(inSum.toFixed(2));
            $("#inCus").html($("#ticketCustomerName").html());
            var user = JSON.parse($("#loginUser").html());
            var userName = user["userName"];
            $("#inApply").html(userName);
        }
    });
}
//
function returnDele() {
    $.ajax({
        "url":"../sale/deleteApplicationList.do",
        success:function(res) {
            if(res == 1){ //    1：成功清空  不等于1 是失败
                goPage(0);
            }else{
                layer.msg("暂存内容清除失败， 请重试！")
            }

        }
    });
}
// creator: hxz，2018-12-26 查看开票申请单 - 查看
function scan(obj, applicationId) {
    $.ajax({
        "url": "../sale/applicationGoodsDetails.do",
        "data": {"applicationId": applicationId},
        success: function (res) {
            var tr = obj.parent().parent() ;
            var cat = tr.children(":eq(2)").html() ;
            var list = res["data"] || [] , str = "" , sum = 0 ;
            $("#tab3 tbody").children(":gt(0)").remove();
            var rate = "" , rateSum = 0;
            let invoice_require = $("#ticketSn").data("invoicerequire")
            if(list && list.length>0){
                $("#tikNum").html(list.length);
                for(var i = 0 ; i < list.length ; i++){
                    var no = i + 1 , price = list[i]["unit_price_invoice"] || 0 ;
                    if(cat == "增值税专用发票"){

                        rate = list[i]["invoice_rate"] + "%" ;
                        rateSum = list[i]["invoice_amount"] || 0;
                        sum += rateSum ;
                        if(list[i]["unit_price_notax"] ){

                        }else if(list[i]["unit_price"] ){
                            let r = list[i]['invoice_rate']
                            list[i]['unit_price_notax'] = list[i]['unit_price'] / (100+r)*100
                        }

                        price = list[i]["unit_price_notax"] ;
                    }
                    var amount = list[i]["item_amount"] || 0;
                    sum += amount ;
                    str += "<tr>" +
                           "    <td>"+ no +"</td>" +
                           "    <td>"+ list[i]["name"] +"</td>" +
                           "    <td>"+ list[i]["unit"] +"</td>" +
                           "    <td>"+ list[i]["item_quantity"] +"</td>" +
                           "    <td>"+ (price) +"</td>" +
                           "    <td>"+ (amount && amount.toFixed(2)) +"</td>" +
                           "    <td>"+ rate +"</td>" +
                           "    <td>"+ (rateSum && rateSum.toFixed(2)) +"</td>" +
                           "</tr>" ;
                }
            }
            $("#tab3").append(str);
            $("#tilCat").html(cat);
            $("#tikSum").html(sum.toFixed(2));
            $("#tikCus").html($("#cusTick").html());
            $("#tickApply").html($("#applier").html());
            $("#tickTime").html(tr.children(":eq(1)").html() );
            $("#tab3").find("tr").each(function() {
                if(cat != "增值税专用发票"){
                    $(this).children(":gt(5)").hide();
                }else{
                    $(this).children(":gt(5)").show();
                }
            })

        }
    });
    bounce_Fixed.show($("#ticketInfoScan"));
}

// creator: hxz，2018-12-26 查看开票申请单 - 删除
var delTickInfo = null ;
function dele(obj, applicationId) {
    delTickInfo = { "obj":obj , "applicationId":applicationId } ;
    bounce_Fixed.show($("#ticketInfoDeleTip"));
}
// creator： 侯杏哲 2019-03-01 查看开票申请单 - 确定删除
function okDel() {
    $.ajax({
        "url":"../sale/deleteApplication.do" ,
        "data":{ "applicationId" : delTickInfo["applicationId"] } ,
        success:function(res) {
            if(res == 1){
                bounce_Fixed.cancel() ;
                scanTickInfo();
                var _index = 0;
                for(var i = 0 ; i < applyTicketList.length; i++){
                    if(applyTicketList[i]["deleApplyID"] == delTickInfo["applicationId"]){
                        console.log("删除一次")
                        _index = i ;
                        var addGoosList = applyTicketList[i]["goosList"] ;
                        for(var j = 0 ; j < addGoosList.length ; j++){
                            var itemid = addGoosList[j]['ordersItem'] ;
                            var num = addGoosList[j]['amount'] ;
                            $("#ticketTab tbody").children("tr").each(function(){
                                var itemid_TB = $(this).attr("itemid");
                                if(itemid_TB == itemid){
                                    var curNum = $(this).children("td:eq(8)").html();
                                    var newNum = Number(curNum) + Number(num) ;
                                    $(this).children("td:eq(8)").html(newNum);
                                }
                            })
                        }
                    }
                }
                applyTicketList.splice(_index, 1) ;
                $("#applicationNum").html(applyTicketList.length)
            }
        }
    })
}
// creator: hxz，2018-12-26  一并提交开票申请
function showTp() {
    var num = $("#tab2 tbody").children("tr").length -1 ;

    if(num > 0){
        $("#allNum").html(num);
        bounce_Fixed.show($("#allTicketApplyTip"));
    }else{
        layer.msg("没有需要提交的开票申请单！");
    }

}
function allTick() {
    var orderId = endOrderTr.attr("id");
    var applicationId = "" ;
    $("#tab2 tbody").children("tr:gt(0)").each(function() {
        var id = $(this).find(".hd1").html();
        applicationId += id + "," ; 
    });
    applicationId.substr(0, applicationId.length-1);
    var allAmount = 0, allInvoiceCount = 0 ; // 总额, 总张数
    if(applyTicketList.length > 0){
        allInvoiceCount += applyTicketList.length ;
        for(var q = 0 ; q < applyTicketList.length ; q++){
            // applicationItemId += applyTicketList[q]["applicationItemId"] + ",";
            var gL = applyTicketList[q]["goosList"] ;
            for(var k = 0 ; k < gL.length ; k++){
                allAmount += Number(gL[k]["money"]) ;
                allAmount += Number(gL[k]["invoiceAmount"]) ;
            }
        }
    }
    var oid = sphdSocket.user.oid
    var userId = sphdSocket.user.userID
    var seesionid = sphdSocket.seesionid
    invoiceApplicationAllTickType = 1 ;
    loading.open();
    invoiceApplicationData = {
        "applicationItemId": applicationId,
        "orders": orderId,
        "allAmount": allAmount.toFixed(2),
        "allInvoiceCount": allInvoiceCount,
        "oid": oid,
        "userId": userId,
        "seesionid": seesionid,
        "state": 2
    };
    sphdSocket.send('invoiceApplication', {
        "applicationItemId": applicationId,
        "orders": orderId,
        "allAmount": allAmount.toFixed(2),
        "allInvoiceCount": allInvoiceCount,
        "oid": oid,
        "userId": userId,
        "seesionid": seesionid,
    });
}

// creator: hxz 2019-04-15 暂存或提交开票申请的回调
var invoiceApplicationAllTickType = 0 ;
function invoiceApplicationAllTick(response) {
    loading.close();
    var res = JSON.parse(response)
    console.log('暂存返回值', res);
    if(invoiceApplicationAllTickType == 1){ // 一并提交
        var code = res["code"] ;
        if(code == 200){
            bounce_Fixed.cancel();
            bounce.cancel();
            layer.msg("提交成功！");
            goPage(0);
        }else{
            bounce_Fixed.hide();
            layer.msg("提交失败，请重试！");
        }
    }else{ // 暂存或者本次提交的
        if (res["code"] == 200) {
            var type = invoiceApplicationData["state"] ;
            bounce.cancel();
            if (type == 0) { // 暂存
                applyTicketList.push({ "goosList":goosList , "applicationItemId": res["applicationItemId"], "deleApplyID":res["result"]['id'] });
                $("#applicationNum").html(applyTicketList.length);
                $("#ticket1").show();
                $("#ticketTab tbody").children("tr").each(function () {
                    var isSelect = $(this).children(":eq(0)").children("i").hasClass("fa-check-square-o");
                    if (isSelect) {
                        var num = $(this).children(":eq(9)").children("input").val();
                        var max = $(this).children(":eq(8)").html();
                        max = Number(max) - Number(num);
                        $(this).children(":eq(8)").html(parseFloat(max.toFixed(4)));
                        $(this).children(":eq(9)").html("").attr("class", "ty-td-gray");
                        $(this).children(":eq(0)").children("i").attr("class", "fa fa-square-o");
                    }
                });
                let nextSubmit = invoiceApplicationData.nextSubmit
                if(nextSubmit == "nextSubmit"){
                    scanTickInfo('submit') // 查看开票申请单
                }else{
                    layer.msg("发票已暂存");
                }
            } else if (type == 1) { // 提交
                layer.msg("提交成功！");
                goPage(0);
            } else {
                layer.msg("无法识别分类");
            }
        } else {
            layer.msg("提交失败！");
        }
    }
}
// creator: hxz 2019-03-29 发票异常记录
function ticketErrorLogBtn() {
    var orderID = $("#ordID").val();
    $.ajax({
        "url":"../sale/exceptionInvoice.do" ,
        "data":{ "orderId": orderID },
        success:function(res) {
            var list = res["data"], str = "" ;
            $("#tab5").children("tbody").children("tr:gt(0)").remove();
            if(list && list.length > 0){
                for(var i = 0 ; i < list.length ; i++){

                    str += "<tr>" +
                           "    <td>"+ (new Date(list[i]['operate_date']).format("yyyy-MM-dd"))  +"</td>" +
                           "    <td>"+ list[i]['invoice_no'] +"</td>" +
                           "    <td>"+ list[i]['amount'] +"</td>" +
                           "    <td>"+ chargeErrror(list[i]['sign_error'], list[i]['sign_handle'], list[i]['losted']) +"</td>" +
                           "</tr>" ;
                }
            }
            $("#tab5").append(str);
            bounce_Fixed.show($("#ticketErrorLog")) ;
        }
    })
}
// creator : hxz 2019-03-29 工具方法 ， 返回异常原因
function chargeErrror(type , handle, losted ) {
    // losted 丢失状态:0-未丢失,1-发票被销售弄丢,2-发票被客户弄丢,计划重新开票,3-发票被客户弄丢,计划不予补开,4-发票由于其他原因丢失,计划重新开票,5-发票由于其他原因丢失,计划不予补开,6-发票未丢失但未签收,计划不予补开',
    //type= sign_error  异常原因:1-单价不对,2-数量不对,3-发票破损,4-发票里多商品,5-发票里少商品,6-发票抬头不对,7-发票被客户弄丢,8-发票由于其他原因丢失,9-其他原因'
    // sign_handle  签收结果:1-需重新开具发票,2-不予需重新开具发票',
    var str = "" ;
    switch (losted){
        case "1": str = '发票被销售弄丢';  break ;
        default:
            switch (type){
                case "1": ;
                case "2": ;
                case "3": ;
                case "4": ;
                case "5": ;
                case "6": ;
                case '9':
                    if(handle == 1){
                        str = "发票未丢失但未签收，计划重新开票" ;
                    }else if(handle == 2){
                        str = "发票未丢失但未签收，计划不予补开" ;
                    }
                    break;
                case '7':
                    if(handle == 1){
                        str = "发票被客户弄丢，计划重新开票" ;
                    }else if(handle == 2){
                        str = "发票被客户弄丢，计划不予补开" ;
                    }
                    break;
                case '8':
                    if(handle == 1){
                        str = "发票由于其他原因丢失，计划重新开票" ;
                    }else if(handle == 2){
                        str = "发票由于其他原因丢失，计划不予补开" ;
                    }
                    break;
            }
    }
    return str ;
}
// creator : hxz 2018-07-20  工具方法 ， 返回发票种类
function category(type) {
    if (type == 1) {
        return "增值税专用发票";
    } else if (type == 2) {
        return "普通发票";
    } else if (type == 3) {
        return "普通发票";
    } else {
        return "— —";
    }
}
// creator: hxz，2018-07-17  点击进度获取折线图
function lineChart(_thisObj, type) { // type - 1：可发货 ，2：已发货 ，3：已签收 , 4:已开票 , 5:已回款
    var ordID = _thisObj.parents("tr").attr("id"), json = [] ;
    var ordResiveDate = _thisObj.parents("tr").children("td:eq(4)").html();
    json.push([ordResiveDate , 0]);
    var   url = "../sale/canbeSent.do" ;
    let info = JSON.parse(_thisObj.parents("tr").find(".hd").html())
    if (info.in_progress === 1) {
        $(".inProgressChart").show()
    } else {
        $(".inProgressChart").hide()
    }
    $.ajax({
        "url": url ,
        "data": {"orderId": ordID },
        success: function (res) {
            var lingData = res["percents"] ;
            $(".warming").hide();
            if(lingData && lingData.length >0){
                let flowStr = `` ;
                for(var i = 0 ; i < lingData.length ; i++){
                    var x = formatTime(lingData[i]["createDate"]) , y = 0  ;
                    var xStr = x.replace("-","年").replace("-","月") + "日" ;
                    var typeStr = "" , ttlStr = "" ;
                    switch (type) {
                        case 1:
                            $(".warming").show(); $("#warmingTbl tbody").children(":gt(0)").remove() ;
                            y = Number(lingData[i]["availablePercent"])*100 ; typeStr = "可发出" ; ttlStr = "可发货" ;
                            if(y>100){ y = 100 ; }  if(y >0 && y <0.01){ y = 0.01 ;     }
                            y = y.toFixed(1) ; json.push([x , y]) ;
                            var sendList = res["sendList"] , sendStr = "" ;
                            if(sendList&&sendList.length > 0){
                                for(var j = 0 ; j < sendList.length ; j++){
                                    sendStr += "<tr>" +
                                               "    <td>"+ (j+1) +"</td>" +
                                               "    <td>"+ (sendList[j]["sn"] || "") +"</td>" +
                                               "    <td>"+ sendList[j]["create_name"] +"</td>" +
                                               "    <td>"+ sendList[j]["customer_name"] +"</td>" +
                                               "</tr>" ;
                                }
                            }
                            $("#warmingTbl tbody").append(sendStr) ;
                            if(i == 0){ flowStr = "<p>"+ xStr +"，"+ typeStr +"该订单"+ y +"%的货物</p>" ; }
                            else if(i == (lingData.length-1) ){ flowStr += "<p>"+ xStr +"有新入库的货物后，"+ typeStr +"该订单"+ y +"%的货物。</p>" ; }
                            break;
                        case 2:
                            y = Number(lingData[i]["deliveredPercent"])*100 ; typeStr = "已发出" ;  ttlStr = "已发货" ;
                            if(y>100){ y = 100 ; }  if(y >0 && y <0.01){ y = 0.01 ;     }
                            y = y.toFixed(1) ;
                            json.push([x , y]) ;
                            flowStr += `截至${xStr}，本订单已发出货物的金额为订单金额的${y}%`
                            //if(i == 0){ flowStr = "<p>"+ xStr +"，"+ typeStr +"该订单"+ y +"%的货物</p>" ; }
                            //else if(i == (lingData.length-1) ){ flowStr += "<p>"+ xStr +"有新出库的货物后，"+ typeStr +"该订单"+ y +"%的货物。</p>" ; }
                            break;
                        case 3:
                            y = Number(lingData[i]["signedPercent"])*100 ; typeStr = "已签收" ; ttlStr = "已签收" ;
                            if(y>100){ y = 100 ; }  if(y >0 && y <0.01){ y = 0.01 ;     }
                            y = y.toFixed(1) ;
                            json.push([x , y]) ;
                            flowStr += `截至${xStr}，本订单已签收货物的金额为订单金额的${y}%`
                            //if(i == 0){ flowStr = "<p>"+ xStr +"，"+ typeStr +"该订单"+ y +"%的货物</p>" ; }
                            //else if(i == (lingData.length-1) ){ flowStr += "<p>"+ xStr +"有新入库的货物后，"+ typeStr +"该订单"+ y +"%的货物。</p>" ; }
                            break;
                        case 4:
                            if(lingData[i]["invoicedPercent"]){
                                y = Number(lingData[i]["invoicedPercent"])*100 ; typeStr = "已开票" ; ttlStr = "已开票" ;
                                if(y>100){ y = 100 ; }  if(y >0 && y <0.01){ y = 0.01 ;     }
                                y = y.toFixed(1) ;
                                json.push([x , y]) ;
                                flowStr += `截至${xStr}，本订单已开票金额为订单金额的${y}%`
                                //if(i == 0){ flowStr = "<p>"+ xStr +"，"+ typeStr +"该订单"+ y +"%的货物</p>" ; }
                                //else if(i == (lingData.length-1) ){ flowStr += "<p>"+ xStr +"有新的开票，"+ typeStr +"该订单"+ y +"%的货物。</p>" ; }
                            }
                            break;
                        case 5:
                            if(lingData[i]["collectedPercent"]){
                                y = Number(lingData[i]["collectedPercent"])*100 ; typeStr = "已回款" ; ttlStr = "已回款" ;
                                if(y>100){ y = 100 ; }  if(y >0 && y <0.01){ y = 0.01 ;     }
                                y = y.toFixed(1) ;
                                json.push([x , y]) ;
                                flowStr += `截至${xStr}，本订单已回款金额为订单金额的${y}%`
                                //if(i == 0){ flowStr = "<p>"+ xStr +"，"+ typeStr +"该订单"+ y +"%的货物</p>" ; }
                                //else if(i == (lingData.length-1) ){ flowStr += "<p>"+ xStr +"有新的开票，"+ typeStr +"该订单"+ y +"%的货物。</p>" ; }
                            }
                            break;
                        default:
                    }
                }
                console.log( JSON.stringify(json) ) ;
                creatLineChart("linechartContainer", json);
                $("#flow").html(flowStr);
                $("#ttl").html(ttlStr +"比例——时间变化记录");
                bounce.show($("#chart"));

            } else{
                // layer.msg("无数据！") ;
            }
        }
    });
}
// creator: hxz，2018-07-17  工具方法 - 生成取折线图
function creatLineChart(containerID, json) {
    var myChart = echarts.init(document.getElementById(containerID));
    myChart.showLoading();
    // json = [["2000-06-05", 0], ["2000-06-05", 10], ["2000-06-05", 20],["2000-06-19", 50], ["2000-06-22", 60], ["2000-06-25", 80], ["2000-07-30", 90] ];
    // json = [["2000-06-05", 0] ];
    console.log(json) ;
    myChart.setOption(option = {
        title: {},
        tooltip: {
            trigger: 'axis',
            formatter: function (list) {
                // console.log(list) ;
                var name = list[0]["name"]; // 2018-05-15
                var data = list[0]["data"]; // 90
                var type = list[0]["seriesName"]; // 完成度
                var str = name + "<br>" + type + ": " + data + "% ";
                return str;
            }
        },
        xAxis: {
            type : 'category',
            data: json.map(function (item) {
                return item[0];//x轴数据，取数组的第一个元素
            })
        },
        yAxis: {
            splitLine: {
                show: false
            },
            axisLabel: {
                formatter: '{value} %'
                // color: '#5d9cec'
            },
            max : 100
        },
        toolbox: {
            top: "20px",
            right: "50px",
            feature: {
                dataZoom: {
                    yAxisIndex: 'none'
                },
                restore: {},
                saveAsImage: {}
            }
        },
        dataZoom: [{
            startValue: '2014-06-01'
        }, {
            type: 'inside'
        }],
        visualMap: { // 图例
            top: 10,
            right: 10,
            pieces: [
                {gt: 0, lte: 50, color: '#096'},
                {gt: 50, lte: 150, color: '#ffde33'},
                {gt: 100, lte: 250, color: '#ff9933'},
                {gt: 150, lte: 350, color: '#cc0033'},
                {gt: 200, lte: 450, color: '#660099'},
                {gt: 300, color: '#7e0023'}
            ],
            outOfRange: {
                color: '#999'
            }
        },
        series: {
            name: '完成度',
            type: 'line',
            lineStyle: {color: '#5d9cec'},
            itemStyle: {
                normal: {
                    lineStyle: {color: '#5d9cec'}
                }
            },
            data: json.map(function (item) {
                return item[1];//x轴数据，取数组的第二个元素
            }),
            markLine: {
                silent: true,
                label: {
                    formatter: '{c} %'
                },
                data: [
                    {yAxis: 20},
                    {yAxis: 40},
                    {yAxis: 60},
                    {yAxis: 80}
                ]
            },
            color: ['#5d9cec', '#2f4554', '#61a0a8', '#d48265', '#91c7ae', '#749f83', '#ca8622', '#bda29a', '#6e7074', '#546570', '#c4ccd3']
        }
    });
    myChart.hideLoading();
}
// creator: hxz，2018-07-18 ，终止订单
var endOrderTr = null;
function endOrderBtn(selector) {
    let detail = JSON.parse(selector.siblings(".hd").html())
    if (detail.out_state && detail.out_state === '9' ){
        layer.msg("<p>在此不可对来源于服务合同的订单进行操作！</p><p>如需要，请到“服务合同”模块中操作。</p>");
        return false;
    }
    $("#end_reason").val("") ;
    $("#endTip").html('');
    endOrderTr = selector.parents("tr");
    var orderId = endOrderTr.attr("id") ;
    let trInfo = selector.siblings('.hd').html();
    let info = JSON.parse(trInfo);
    if (info.in_progress === 1) {
        $("input[name='inProgressEnd']").prop('checked', false);
        bounce.show($("#inProgressEndOrd"));
        return false
    }
    $.ajax({
        "url":"../sale/ordersEndDetail.do" ,
        "data":{ "orderId": orderId  } ,
        success:function (res) {
            var invoiced = res["invoiced"]; //invoiced是已开票的金额
            var send = res["send"]; //是已发货的货物金额
            var collect = res["collect"]; //是已回款的金额
            if (invoiced === send && invoiced === collect) {
                $("#endTip").html("该订单的货物尚未完全交付");
            } else if ((collect >= invoiced) && (collect > send) || (collect > invoiced) && (collect >= send)) {
                var str =
                    '<p>该订单回款总额为' + collect + '元，所发出的货物总价值为' + send + '元，已开票总额为' + invoiced + '元。</p>' +
                    '<p>终止该订单后，您可能需处理未尽事宜，如<span class="ty-color-red">客户要求退款，或要求补齐所欠货物及发票等</span>。</p>';
                $("#endTip").html(str);
            } else if ((send >= invoiced) && (send > collect)) {
                var str =
                    '<p>该订单已发出货物总价值已为' + send + '元，客户回款仅为' + collect + '元，已开票总额则为' + invoiced + '元。</p>' +
                    '<p>终止该订单后，您可能需处理未尽事宜，尤其是向其<span class="ty-color-red">索要欠款</span>。</p>';
                $("#endTip").html(str);
            } else if ((invoiced > send) && (invoiced > collect)) {
                var str =
                    '<p>目前已向该客户开票' + invoiced + '元，已发货物总价值则仅为' + send + '元，客户回款则为' + collect + '元。</p>' +
                    '<p>终止该订单后，您可能需处理未尽事宜，尤其是<span class="ty-color-red">财会人员需处理已开票但尚未回款的问题，客户还可能继续要求发货</span>。</p>';
                $("#endTip").html(str);
            }
            bounce.show($("#endOrd"));
        }
    }) ;
} 
function endOrdOk() {
    let info = JSON.parse(endOrderTr.find(".hd").html())
    var orderId = endOrderTr.attr("id") ;
    var reason = '' ;
    let param = {
        "orderId": orderId ,
        "reason": ''
    }
    if (info.in_progress !== 1) {
        reason = $("#end_reason").val() ;
        param.reason = reason
    } else {
        param.inProgress = 1
    }
    $.ajax({
        "url":"../ztr/deleteOrder.do" ,
        "data": param ,
        success:function (res) {
            var status = res["status"] ;
            if(status == 1){
                layer.msg("操作成功！") ;
                getOrderList(pageInfo["cur"], 10) ;
                bounce.cancel() ;
            }else{
                layer.msg("操作失败！")
            }

        }
    }) ;
}


// creator: 张旭博，2018-05-30 10:16:01，新增订单 - 按钮
function newOrderBtn(type) {
    if (chargeRole("超管")) {
		$("#msTips .msTip").html("您没有此权限！");
		bounce.show($("#msTips"));
	}else{
        let bnTtl = '新增订单'
        var collect = {
            'type': '1'
        };
        if (type === 1) bnTtl = '进行中的订单'
        $("#newOrder").data("type",type); // buAndPro - 补发的订单， undefined - 新增订单 , 1-进行中的订单
        $("#newOrder").data("orderEditType",'add')
        $("#newOrder").data('collect', collect);
        editOrd = {} ;
        $("#newOrder .uphide").show();
        $("#newOrder .flexcon .red").show();

        $("#newOrder").find("input,select").val("");
        $("#newOrder .bonceHead span").html(bnTtl);
        $(".customerName").prop('disabled', false);
        $("#newOrder .canOrder").hide();
        bounce.show($("#newOrder"));
        $(".cusSearchItems").hide();
		getCustomName();
        getReviewSet();
		// countOrderTotal();
		$("#OrderReceivedDate").val("") ;
		$("#ordNo").val("") ;
		$("#gsBd").html("") ;
		$(".receiveAddress").html("") ;
        $("#newOrder .form-control").prop('disabled', false);
        $("#newOrder .customerId").prop("disabled", true);
        $("#newOrder .orderTotal").prop("disabled", true);
		$(".orderTotal").val(0);
        $(".newGood").prop("disabled",true).html("选择专属商品");
        $(".newGoodCommon").prop("disabled",true).html("选择通用型商品");
        bounce.everyTime('0.5s','newGoodActive',function(){
            //  必填项全部填上才能高亮按钮
            let count = 0 ;
            $("#newOrder .orderDetail [require]").each(function () {
                let val = $(this).val()
                if($.trim(val).length === 0){
                    count++
                }
            })
            if (!$("#newOrder .principal").prop("disabled") && ($.trim($("#newOrder .principal").val()).length === 0)) {count++}
            if(count === 0){
                let goodInfo1 = $(".newGood").data("goodInfo")
                $(".newGood").prop("disabled",false);
                $(".newGoodCommon").prop("disabled",false);
                $("#sureNewOrder").prop("disabled",false);
                // 计算订单总额
                let sum = 0
                $("#gsBd tr").each(function(){
                    let item = $(this).find(".hd").html()
                    item = item ? JSON.parse(item) : null
                    if(item){
                        let price = 0 ;
                        let invoiceRequire = item.invoiceRequire
                        let goodNum = item.goodNum
                        if(invoiceRequire === 1){ // 增专
                            price = item.priceInfo.unitPrice
                        }else if(invoiceRequire === 2){
                            price = item.priceInfo.unitPriceInvoice
                        }else if(invoiceRequire === 3){ //不开票
                            price = item.priceInfo.unitPriceNoinvoice
                        }
                        sum += goodNum * price
                    }
                })
                $("#newOrder .orderTotal").val(sum.toFixed(2))
                $("#newOrder .canOrder").show();
            }else{
                $("#sureNewOrder").prop("disabled",true);
                $(".newGood").prop("disabled",true);
                $(".newGoodCommon").prop("disabled",true);
                $("#newOrder .canOrder").hide();
            }

        });
	}
}
// updator: 侯杏哲 2018-07-18  10:16:33，查看订单详情
function seeOrderBtn(selector) {
    var orderID = selector.parent().parent().attr("id");
    if (!orderID) {
        layer.msg("获取订单信息失败，请刷新重试！");
        return false;
    }
    $("#ordID").val(orderID);
    if (pageInfo["type"] == 1) { // 已完结的订单
        $("#scanEndOrdLog").show();
    } else {
        $("#scanEndOrdLog").hide();
    }
    $.ajax({
        url: "../sale/orderDetail.do",
        data: {"orderId": orderID },
        success: function (res) {
            bounce.show($("#seeOrder"));
            var items = res["items"] ;
            var order = res["order"][0] ;
            if( !items || !order ){
                layer.msg("获取数据失败！");  return false ;
            }
            var invoices =( res["invoices"] && res["invoices"]["data"])|| [], invoicesStr = ""; // 回款
            var invoicedMap =  (res["invoicedMap"] && res["invoicedMap"]["data"]) || []; // 开票信息汇总
            $("#ordTicket").find("tr:gt(1)").remove();
            var invoiceList = []
            invoicedMap.forEach(function(inv,index){
                // invoice_date  invoice_no  invoice_amount  method  delivery_date         re_name
                let returnInvo = invoices[index] || {}
                invoiceList.push({
                    'invoice_date':inv.invoice_date,
                    'invoice_no':inv.invoice_no,
                    'invoice_amount':inv.invoice_amount,
                    'method':inv.method,
                    'delivery_date':inv.delivery_date,
                    're_name':inv.re_name,
                    'create_date':returnInvo.create_date || "",
                    'item_amount':returnInvo.item_amount || "",
                    'method2':returnInvo.method || "",
                    'return_no':returnInvo.return_no || ""
                })
            });
            if(invoicedMap.length < invoices.length){
                let startIndex = invoicedMap.length ;
                for(let w = startIndex ;  w < invoices.length; w++){
                    invoiceList.push({
                        'invoice_date':"",
                        'invoice_no':"",
                        'invoice_amount':"",
                        'method':"",
                        'delivery_date':'',
                        're_name':"",
                        'create_date':invoices[w].create_date || "",
                        'item_amount':invoices[w].item_amount || "",
                        'method2':invoices[w].method || "",
                        'return_no':invoices[w].return_no || ""
                    })
                }
            }
            invoiceList.forEach(function (inv) {
                invoicesStr += `<tr>
                             <td class="bg_ea">${ (new Date( inv["invoice_date"]).format("yyyy-MM-dd")) }  </td>
                             <td class="bg_ea">${  handleNull(inv["invoice_no"]) } </td>
                             <td class="bg_ea">${  (inv["invoice_amount"] && Number(inv.invoice_amount).toFixed(2)) || "" }</td>
                             <td class="bg_d"> ${  chargeDeliveryWay(inv["method"]) }  </td>
                             <td class="bg_d"> ${(new Date( inv["delivery_date"]).format("yyyy-MM-dd")) }    </td>
                             <td class="bg_d"> ${  handleNull(inv["re_name"]) }   </td>
                             <td class="bg_c"> ${  new Date(inv["create_date"]).format('yyyy-MM-dd')  }  </td>
                             <td class="bg_c"> ${ (inv["item_amount"]) || "" } </td>
                             <td class="bg_c"> ${ incomeTypeStr(inv["method2"]) }</td>
                             <td class="bg_c"> ${ inv["return_no"] || "" } </td>
                         </tr>`;
            })
            $("#ordTicket").append(invoicesStr) ;
            $("#see_cusName2").html(order["customer_name"]) ;
            $("#see_cusId2").html(order["code"]) ;
            $("#see_sn2").html(order["sn"]) ;
            $("#see_signDate2").html(formatTime(order["sign_date"])) ;
            $("#see_createName2").html(order["create_name"]) ;
            $("#see_createDate2").html( formatTime( order["create_date"]) ) ;
            $("#address2").html(order["delivery_address"]) ; // 收货地址
            $("#addrPerson").html(order["contact"]) ; // 收货人
            $("#addrPhone").html(order["mobile"]) ; // 收货电话
            var str = "" ;
            $("#goodsList tr:gt(1)").remove() ;
            if( items && items.length > 0 ){
                for(var i = 0 ; i < items.length ; i++ ){  // 循环每个商品组
                    var deliveryAndSignList = items[i]["deliveryAndSignList"] ; // 发货签收记录
                    var deliveryTimeStr = "" , deliveryNumStr = "" , signTimeStr = "" , signNumStr = "" ;
                    if(deliveryAndSignList && deliveryAndSignList.length > 0){
                        for(var j=0 ; j <deliveryAndSignList.length ; j++){
                            deliveryTimeStr += "<div>"+ formatTime(deliveryAndSignList[j]["review_time"])  +"</div>" ;
                            deliveryNumStr += "<div>"+ (deliveryAndSignList[j]["out_fact"])  +"</div>" ;
                            signTimeStr += "<div>"+ (formatTime(deliveryAndSignList[j]["sgin_time"])||"&nbsp;") +"</div>" ;
                            signNumStr += "<div>"+ (deliveryAndSignList[j]["sign_amount"]||"&nbsp;")  +"</div>" ;
                        }
                    }
                    var invoicedList = items[i]["invoicedList"] ; // 开票记录
                    var invoiceDateStr = "" , invoiceNoStr = "", invoiceNumStr = "", invoiceAmount = "" ;
                    if(invoicedList && invoicedList.length > 0){
                        for(var k=0 ; k <invoicedList.length ; k++){
                            invoiceDateStr += "<div>"+ ((invoicedList[k]["operate_date"] && formatTime(invoicedList[k]["operate_date"]))|| "&nbsp;")  +"</div>" ;
                            invoiceNoStr += "<div>"+ (invoicedList[k]["invoice_no"] || "&nbsp;")  +"</div>" ;
                            invoiceNumStr += "<div>"+ (invoicedList[k]["item_quantity"] || "&nbsp;")  +"</div>" ;
                            invoiceAmount += "<div>"+ ( (invoicedList[k]["item_total_amount"]).toFixed(2) || "&nbsp;")  +"</div>" ;
                        }
                    }
                    var no = i+1 ;
                    str += "<tr>" +
                        "      <td>"+ no +"</td>" +
                        "      <td>"+ items[i]["outer_sn"] +"</td>" +
                        "      <td>"+ items[i]["outer_name"] +"</td>" +
                        "      <td>"+ handleNull(items[i]["inner_sn"]) +"</td>" +
                        "      <td>"+ handleNull(items[i]["name"]) +"</td>" +
                        "      <td>"+ (items[i]["unit"] || '') +"</td>" +
                        "      <td>"+ items[i]["amount"] +"</td>" +
                        "      <td>"+ formatTime( items[i]["delivery_date"] ) +"</td>" +
                        "      <td>"+ deliveryTimeStr +"</td>" +
                        "      <td>"+ deliveryNumStr +"</td>" +
                        "      <td>"+ signTimeStr +"</td>" +
                        "      <td>"+ signNumStr +"</td>" +
                        "      <td>"+ invoiceDateStr +"</td>" +
                        "      <td>"+ invoiceNoStr +"</td>" +
                        "      <td>"+ invoiceNumStr +"</td>" +
                        "      <td>"+ invoiceAmount +"</td>" +
                        "  </tr>" ;
                }
            }
            $("#goodsList").append(str) ;

        }
    })
}
// creator: 侯杏哲 2018-07-18 10:16:33  订单终止审批记录
function endOrdLog() {
    var orderID = $("#ordID").val();
    if (!orderID) {
        layer.msg("获取订单信息失败，请刷新重试！");
        return false;
    }
    bounce_Fixed.show($("#endOrdLog"));
}

// creator: 侯杏哲 2018-07-18 10:16:33，查看原始订单
function seeOrderOral() {
    var orderID = $("#ordID").val();
    if (!orderID) {
        layer.msg("获取订单信息失败，请刷新重试！");
        return false;
    }
    $.ajax({
        url: "../ztr/oldOrder.do",
        data: {"orderId": orderID},
        success: function (data) {
            var slOrders = data["slOrders"];
            var slOrdersHistory = data["slOrdersHistory"];
            var slOrdersItems = data["slOrdersItems"];
            if (slOrdersHistory.id == 0) {
                $("#see_amount").html( (slOrders["amount"] && slOrders["amount"].toFixed(2)) || 0 ) ;
            }else {
                $("#see_amount").html( (slOrdersHistory["contractAmount"] && slOrdersHistory["contractAmount"].toFixed(2)) || 0 ) ;
            }
            if (slOrders && slOrdersItems) {
                bounce_Fixed.show($("#seeOrderOral"));
                $("#see_cusName").html(slOrders["customerName"]);
                $("#see_signDate").html(new Date(slOrders["signDate"]).format("yyyy-MM-dd"));
                $("#see_hasInvoice").html(chargeHasInvoice(slOrders["hasInvoice"]));
                $("#see_cusId").html(slOrders["cusCode"]);
                $("#see_sn").html(slOrders["sn"]);
                $("#see_createName").html(slOrders["createName"]);
                $("#see_createDate").html(new Date(slOrders["createDate"]).format("yyyy-MM-dd"));
                var str = "", sum = 0;
                if (slOrdersItems && slOrdersItems.length > 0) {
                    for (var i = 0; i < slOrdersItems.length; i++) {
                        var no = i + 1;
                        var price = 0;
                        if(slOrdersItems[i]["hasInvoice"] == '0' || slOrdersItems[i]["hasInvoice"] == ''){
                            sum += parseInt(slOrdersItems[i]["amount"]) *  slOrdersItems[i]["slUnitPriceNoTax"] ;
                            price = handleNull(slOrdersItems[i]["SLUnitPriceNoTax"])
                        }else{
                            price = (slOrdersItems[i]["slUnitPrice"]).toFixed(2);
                            if(slOrdersItems[i]["invoiceCategory"] == 1){ // 增专
                                sum += parseInt(slOrdersItems[i]["amount"]) * slOrdersItems[i]["SLUnitPriceNoTax"] * (1 + slOrdersItems[i].taxRate/100);
                            }else{
                                sum += parseInt(slOrdersItems[i]["amount"]) * slOrdersItems[i]["slUnitPrice"] ; // 增普和其他
                            }
                        }
                        str += "<tr>" +
                            "<td>" + no + "</td>" +
                            "<td>" + slOrdersItems[i]["slOuterSn"] + "</td>" +
                            "<td>" + slOrdersItems[i]["slOutterSnName"] + "</td>" +
                            "<td>" + slOrdersItems[i]["slInnerSn"] + "</td>" +
                            "<td>" + slOrdersItems[i]["slInnerSnName"] + "</td>" +
                            "<td>" + price + "</td>" +
                            "<td>" + slOrdersItems[i]["slUnit"] + "</td>" +
                            "<td>" + slOrdersItems[i]["amount"] + "</td>" +
                            "<td>" + ( new Date(slOrdersItems[i]["deliveryDate"]).format("yyyy-MM-dd") ) + "</td>" +
                            "</tr>";
                    }
                }
                //$("#see_amount").html( sum.toFixed(2) ) ;
                $("#see_tbl").html(str);

            } else {
                $("#msTips .msTip").html("获取订单信息失败，请刷新重试！");
                bounce.show($("#msTips"));
            }

        },
        error: function () {
            $("#msTips .msTip").html("获取订单信息失败，请刷新重试！");
            bounce.show($("#msTips"));
        }
    })

}
// creator: 张旭博，2018-05-30 10:16:52，删除订单 - 按钮
function deleteOrderBtn(selector) {
	if(hasAuthority(0) === true){
		$("#msTips .msTip").html("您没有此权限！");
		bounce.show($("#msTips"));
	}else{
		var orderID  = selector.parent().parent().attr("id");
		if(orderID == "" || orderID == undefined){
			$("#msTips .msTip").html("获取订单信息失败，请刷新重试！");
			bounce.show($("#msTips")); return false ;
		}else{
			$("#deleteOrder .msTip").attr("id",orderID);
			var no =  selector.parent().siblings(":eq(1)").html();
			var cus =  selector.parent().siblings(":eq(2)").html();
			$("#deleteOrder .msTip").html("确定删除订单号为：" + no + ", 客户名称为：" + cus + " 的订单？");
			bounce.show($("#deleteOrder"));
		}

	}
}
// creator: 张旭博，2018-05-30 10:17:15，删除订单
function deleteOrder() {
	var orderID = $("#deleteOrder .msTip").attr("id");
	$.ajax({
		url:"../ztr/deleteOrder.do" ,
		data:{ "orderId":orderID },
		success:function(data){
			var status = data["status"];  // 1 - success . 2 - 失败，因为被评审了 ， 3 - 因为被修改了
			bounce.cancel();
			if(status == 1 ){
				layer.msg("订单删除成功！")
                getOrderList(1, 15);
			}else if(status == 2 ){
				$("#msTips .msTip").html("订单已经被评审，不可删除 ！");
				bounce.show($("#msTips"));
			}else if(status == 3 ){
				$("#msTips .msTip").html("订单已经被修改，不可删除 ！");
				bounce.show($("#msTips"));
			}
		},
		error:function(){  
			$("#msTips .msTip").html("系统错误，请刷新重试！");
			bounce.show($("#msTips"));
        }
	});
}
// updater: 侯杏哲，2021-07-09 10:14:55，点击修改订单按钮
var editOrd = {} ;
function changeOrderBtn(selector) {
	if(hasAuthority(0) === true){
		$("#msTips .msTip").html("您没有此权限！") ;
		bounce.show($("#msTips")) ;
	}else{
        let detail = JSON.parse(selector.siblings(".hd").html())
        if (detail.out_state && detail.out_state === '9' ){
            layer.msg("<p>在此不可对来源于服务合同的订单进行操作！</p><p>如需要，请到“服务合同”模块中操作。</p>");
            return false;
        }
		var orderID  = selector.parent().parent().attr("id");
		if(orderID == "" || orderID == undefined){
            $("#msTips .msTip").html("获取订单信息失败，请刷新重试！");
            bounce.show($("#msTips"));
            return false;
        }
		$.ajax({
			url:"../sr/toProductionManage.do" ,
			data: { "orderId" : orderID , "type" : 3 } ,
			success:function(data){
				var items = data["items"] ;
				var order = data["order"] ;
				if( items && order ){
                    $("#newOrder .bonceHead span").html('修改订单');
                    $(".customerName").prop('disabled', true);
                    bounce.show($("#newOrder"));
                    $("#newOrder").data("orderEditType",'update').data("orderid",order.id) ;
                    $(".newGood").prop("disabled",false).html("增加专属商品");
                    $(".newGoodCommon").prop("disabled",false).html("增加通用型商品");
                    $("#newOrder .form-control").prop('disabled', true);
                    $("#newOrder .uphide").hide();
                    $("#newOrder .flexcon .red").hide();
                    $(".customerName").val(order.customerName).data("cusId" , order.customer_);
                    // $(".customerName").html( `<option invoice="${order.invoiceRequire}" code="${order.cusCode}" value="${order.customer_}">${order.customerName} </option>`);
                    $("#newOrder .principal").html(`<option>${handleNull(order.principalName)}</option>`); // 生产方的评审负责人
                    $(".orderNumber").val(order.sn)
                    $(".customerId").val(order.cusCode)
                    $(".invoiceRequire").val(formatInviceRequire(order.invoiceRequire) ).data("invoice", order.invoiceRequire)
                    $("#OrderReceivedDate").val( new Date(order.signDate).format("yyyy-MM-dd"))
                    setOuterCon(order["customer_"], order.invoiceRequire);
                    setAddressCon(order["customer_"], 'update');
                    let option = `<option value="本订单有多个收货地点">本订单有多个收货地点</option>`
                    if (order.deliveryType === 1) {//1-收货 2-自提 3-多个收货地址
                        option = `<option value="${order.addressId}">${order.address}</option>`
                    } else if (order.deliveryType === 2){
                        option = `<option value="到本公司自提">到本公司自提</option>`
                    }
                    $("#newOrder .receiveAddress1").html(option)
                    let invoiceCategory = Number(order.invoiceRequire);
                    let str = ""; //
                    if(items.length > 0){
                        items.forEach(function (item) {
                            item.updateType = 1
                            item.invoiceRequire = order.invoiceRequire
                            item.outerSn = item.outerSn
                            item.outerName = item.outerName
                            item.unit = item.slUnit
                            item.sid = item.id // 明细id
                            item.id = item.salesRelationship_ // 明细id

                            item.goodNum = item.amount
                            item.minimumStock = item.minimumiStock
                            item.receiveAddressId = item.deliveryAddressId
                            item.receiveAddress = item.deliveryAddress
                            item.requireDate = new Date(item.deliveryDate).format("yyyy-MM-dd")
                            if(invoiceCategory === 1){ // 增专
                                item.price = item.unitPrice
                                item.priceInfo = { "unitPrice":item.unitPrice, "taxRate":item.taxRate, "unitPriceNotax": item.unitPriceNotax }
                            }else if(invoiceCategory === 2){
                                item.price = item.unitPriceInvoice
                                item.priceInfo = { "unitPriceInvoice": item.price  }
                            }else if(invoiceCategory === 3){ //不开票
                                item.price = item.unitPriceNoinvoice
                                item.priceInfo = { "unitPriceNoinvoice": item.price  }
                            }
                            item.priceInfo.priceDesc = item.priceDesc

                            str += `
                            <tr>
                                <td>${ item.outerSn}</td>
                                <td>${ item.outerName}</td>
                                <td>${ item.price}</td>
                                <td>${ item.unit}</td>
                                <td>${ item.goodNum}</td>
                                <td>${ item.requireDate}</td>
                                <td>${ item.receiveAddress}</td>
                                <td>
                                    <span class="ty-color-blue updateGoodBtn">修改</span>
                                    <span class="ty-color-red deleteGoodBtn">删除</span>
                                    <span class="hd">${JSON.stringify(item)}</span>
                                </td>
                            </tr>  
                        `;
                        })
                    }
                    $("#gsBd").html(str);
                    $("#newOrder .canOrder").show();
                    bounce.everyTime('0.5s','newGoodActive',function(){
                        // 计算订单总额
                        let sum = 0
                        $("#gsBd tr").each(function(){
                            let item = $(this).find(".hd").html()
                            item = item ? JSON.parse(item) : null
                            if(item){
                                let price = 0 ;
                                let invoiceRequire = item.invoiceRequire
                                let goodNum = item.goodNum
                                if(invoiceRequire === 1){ // 增专
                                    price = item.priceInfo.unitPrice
                                }else if(invoiceRequire === 2){
                                    price = item.priceInfo.unitPriceInvoice
                                }else if(invoiceRequire === 3){ //不开票
                                    price = item.priceInfo.unitPriceNoinvoice
                                }
                                sum += goodNum * price
                            }
                        })
                        $("#newOrder .orderTotal").val(sum.toFixed(2))
                    });

				}else{
					$("#msTips .msTip").html("获取订单信息失败，请刷新重试！");
					bounce.show($("#msTips"));
				}
				// 初始化筛选框
				// setRoleInit("roleCon_update") ;
			} ,
			error:function(){
				$("#msTips .msTip").html("获取订单信息失败，请刷新重试！");
				bounce.show($("#msTips"));
            }
		});

	}
}


var editGsObj = {} ; 
// creator: 侯杏哲，2017-07-12 10:12:19，评审记录 - 修改 - 修改商品按钮  */
function editGs( _this ){
	$(".inputTip").html("");
	var info = _this.siblings(".hd").html() ;
	if( info ){
		info = JSON.parse(info) ;
        // has_invoice          boolean comment '是否开票',
        // invoice_category     char(1) comment '发票类型:1-增值税专用发票,2-增值税普通发票,3-其他票',
		editGsObj["editSpan"] = _this ;
		editGsObj["info"] = info ; 
		bounce_Fixed.show( $("#changeGood") ) ;
		var price = info["slUnitPrice"] ;
		$("#changeGood .inputTip").html(); 
		$("#changeGood .outerSn").val(info["slOuterSn"]) ;
		$("#changeGood .outerName").val( info["slOutterSnName"] ) ;
		$("#changeGood .cInnerSn").val( info["slInnerSn"] ) ;
		$("#changeGood .cInnerSnName").val( info["slInnerSnName"] ) ;
		$("#changeGood .unitPrice").val( price ) ;
		$("#changeGood .unit").val( info["slUnit"] ) ;
		$("#changeGood .goodNum").val( info["amount"] ) ;
		$("#changeGood .requireDate").val( formatTime( info["deliveryDate"] ) ) ;
        $("#changeGood .currentStock").val( info["currentStock"]  ) ;
		$("#changeGood .minimumStock").val( info["minimumStock"]  ) ;
		$("#changeGood .receiveAddress").val(info["deliveryAddressId"]) ;
        $("#changeGood .goodNum_stock").attr("pack", info["packages"]); //货物件数
        bounce.everyTime('0.5s','pack',function(){
            var pack = $("#changeGood .goodNum_stock").attr("pack");
            var goodNum = $("#changeGood .goodNum").val();
            if(pack !== undefined && goodNum !== ""){
                $("#changeGood .goodNum_stock").val(Math.ceil(goodNum/parseInt(pack)));
            }else{
                $("#changeGood .goodNum_stock").val("");
            }
        });
	}else{
		bounce_Fixed.show( $("#fixMsTips")) ;
		$("#fixMsTips .msTip").html("获取商品信息失败，请刷新重试！") ;
	}

}
// creator: 张旭博，2018-05-30 10:12:19，修改订单 - 确定
function sureChangeOrder() {
    var orderTotal = $("#updateAmount").html();
    var orderListArr = {
        "id": editOrd["order"]["id"]  ,
        "cusid" : editOrd["order"]["customer_"]  ,
        "cusName" : editOrd["order"]["customerName"] ,
        "signDate" : editOrd["order"]["signDate"] ,
        "hasInvoice" : editOrd["order"]["hasInvoice"] ,
        "sn" : editOrd["order"]["sn"]  ,
        "ordersAmount" : orderTotal
	};
    var goodListArr = [] ;
    var delGoodsId = [];
    $("#itemsCon .ty-color-blue").each(function () {
		var info = $(this).siblings(".hd").html() ;
		if(info){
			info = JSON.parse( info ) ;
			var goodInfoJson = {
				"id"				: info["salesRelationship_"] , // 商品对照id
				"sid"				: info["id"] ,// 明细id
				"SlInnerSn"		: info["slInnerSn"]  ,
				"SlInnerSnName"	: info["slInnerSnName"] ,
				"SlOuterSn"		: info["slOuterSn"] ,
				"SlOutterSnName"	: info["slOutterSnName"] ,
				"SlUnit"			: info["slUnit"] ,
				"SlUnitPrice"		: info["slUnitPrice"] ,
				"deliveryDate"	    : info["deliveryDate"] ,
				"amount"			: info["amount"],
                "address"			: info["deliveryAddress"],
                "addressId"			: info["deliveryAddressId"]
			} ;
			goodListArr.push(goodInfoJson) ;
		}
    });
	$("#changeOrder .delData").children("li").each(function () {
		delGoodsId.push($(this).text());
	});
	$("#changeOrder .delData").children() ;
	var data = {
		"save_slOrders": JSON.stringify(orderListArr),
		"save_slOrdersItemList": JSON.stringify(goodListArr),
        "save_slOrdersItemListDel": JSON.stringify(delGoodsId)
	};
	$.ajax({
		url: "updateSlOrders.do",
		data: data ,
		success: function (data){
			var status = data["status"] ;
            bounce.cancel() ;
            if(status == 1){
				layer.msg("修改成功！")
                getOrderList(1, 15);
			}else{
				$("#fixMsTips .msTip").html(data["message"]);
				bounce_Fixed.show($("#fixMsTips"));
			}
		},
		error: function () {
			$("#fixMsTips .msTip").html("链接失败，请刷新重试！");
			bounce_Fixed.show($("#fixMsTips"));
        }
	})
}
// creator: 侯杏哲，2017-06-08 10:12:19，评审记录
function chargeHistory( ordID ){
	$.ajax({
		url:"../sr/toProductionManage.do" ,
		data:{ "orderId" : ordID , "type":4   } ,
		success:function( res ){
			var list = res["items"] ;  // 商品列表
			var order = res["order"] ;  // 订单详情
			bounce.show( $("#approvePRHistory") ) ;
			$("#cusName").html( order["customerName"] ) ;
			$("#cusCode").html( order["cusCode"] ) ;
			$("#ordSn").html( order["sn"] ) ;
			$("#ordReciveDate").html( formatTime( order["earliestDeliveryDate"] ) ) ;
			$("#creator").html( order["createName"] ) ;
			$("#createDate").html( formatTime( order["createDate"] ) ) ;
			var str = "" ;
			if( list && list.length ){
				$("#historyList").html("");
				for(var i=0 ; i < list.length ; i++ ){
					setHistoryStr( { "itemInfo":list[i] , "tbl": $("#historyList") , "i": i } ) ;
				}
			}
		} ,
		error:function(){
			$("#fixMsTips .msTip").html("链接失败，请刷新重试！");
			bounce_Fixed.show($("#fixMsTips"));
        }
	});

}
// creator: 张旭博，2018-05-30 10:12:19，评审记录 的列表显示
function setHistoryStr( config ){
	var itemInfo = config["itemInfo"] ;
	var tbl = config["tbl"] ;
	var i = config["i"] ;

	var no = i + 1 ;
	var map = itemInfo["map"] ;
	var data_1 = itemInfo["map"]["data"] ;
	var data_history = map["itemHistories"] && map["itemHistories"]["data"] ;

	var hisNum = 0 ; // 标记历史写入的条数
	for( var k = 0 ; k < data_1.length ; k++ ){
		var str = "" ;
		var data_2 = data_1[k]["data"] && data_1[k]["data"][0] ;
		var dd = data_1[k] ;
		// 首先判断修改的数据
		if(data_history){
			var reviewDate = data_1[k]["review_date"] ; // 本行的创建时间
			// 加入历史表数据， 由此判断这条能不能被修改 ；
			var his = hisNum ;
			for( ; his < data_history.length ; his++ ){
				var str_his = "" ;
				var createDate = data_history[hisNum]["update_date"] ; // 订单修改的时间
				if( reviewDate > createDate){  // 本有效行的时间晚  ， 订单修改的放在前面
					if( hisNum == 0 && k == 0){
						str_his = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
							"<td><div>"+ no + "</div><div class='hd'>"+ JSON.stringify(itemInfo) +"</div></td>" +
							"<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" +
							"<td>"+ data_history[his]["amount"] +"</td>" +
							"<td>"+ formatTime(data_history[his]["delivery_date"]) +"</td>" +
							"<td>— —</td><td>— —</td><td>— —</td><td>——</td><td>— —</td><td>— —</td><td>——</td>" +
							"</tr>" ;
					}else{
						str_his = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
							"<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
							"<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
							"<td>"+ data_history[his]["amount"] +"</td>" +
							"<td>"+ formatTime(data_history[his]["delivery_date"]) +"</td>" +
							"<td>— —</td><td>— —</td><td>— —</td><td>——</td><td>— —</td><td>— —</td><td>——</td>" +
							"</tr>" ;
					}
					hisNum = his + 1  ;
					tbl.append( str_his );
				}
			}
		}
		// 处理这一行数据
		if( !data_2 && (k == (data_1.length - 1)) ){ // 最后一次处理 ，还没评审
			str += "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
				"<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" ;
			if( k == 0){ str += "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" ;  }else{ str += "<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" ; }
		}else if( k == 0 ) {
			str += "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
				"<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" +
				"<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'>"+ itemInfo["slOuterSn"] +"</td>" ;
		}else{
			str += "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
				"<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div> <i class='hd'>"+ JSON.stringify( dd ) +"</i> </td>" +
				"<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'> </td>" ;
		}

        str += "<td>"+ (data_1[k]["itemAmount"] || data_1[k]["amount"] ) +"</td>" +
            "<td>"+ formatTime( data_1[k]["delivery_date"] || data_1[k]["item_delivery_date"] ) +"</td>" ;

        if(data_1[k]["surplus_date"]){  // 评审 否
            str += "<td>否</td><td>"+ data_1[k]["scheduled_amount"] +"</td><td>"+ formatTime( data_1[k]["surplus_date"] ) +"</td>"
        }else{  //  评审 是
            if(data_1[k]["review_date"]){
                str += "<td>是</td><td>— —</td><td>— —</td>" ;
            }else{
                str += "<td>— —</td><td>— —</td><td>— —</td>" ;
            }
        }
		str +=  "<td>"+ formatTime(data_1[k]["create_date"]) +"</td>" +
			"<td>"+ (data_1[k]["create_name"] || "") +"</td>" ;
		if( data_2 ){ // 这一条处理过，显示处理信息
			str+= "<td>"+ formatTime( data_2["review_date"] ) +"</td>" +
				"<td>"+ data_2["reviewer_name"] +"</td>" +
				"</tr>" ;
		}else{
			str+= "<td>— —</td>" +
				"<td>— —</td>" +
				"</tr>" ;
		}
		tbl.append( str );
	}
	if( data_history && hisNum != data_history.length  ){
		for( var g = hisNum ; g < data_history.length ; g++ ){
			str_his = "<tr class='throw' info='"+ JSON.stringify(itemInfo) +"'>" +
				"<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
				"<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
				"<td>"+ data_history[g]["amount"] +"</td>" +
				"<td>"+ formatTime(data_history[g]["delivery_date"]) +"</td>" +
				"<td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
				"</tr>" ;
			tbl.append( str_his );
		}
	}

	// 判断是不是刚修改完
	var hisTime = 0 , reviewTime = 0 ; //
	var data_1_len = data_1.length ;
	if(data_history && data_history.length > 0){ hisTime = data_history[data_history.length -1]["update_date"] ;  }
	if(data_1 && data_1_len > 0){ reviewTime = data_1[data_1_len -1]["create_date"] ;  }
	if(hisTime > reviewTime) { // 刚修改完
		var strNew = "<tr info='"+ JSON.stringify(itemInfo) +"'>" +
			"<td><div>"+ no + "</div><div class='hd'>"+ itemInfo["id"] +"</div></td>" +
			"<td onmouseover='showMess($(this))' onmouseout='hideMess($(this))'></td>" +
			"<td>"+ data_1_len > 0 ? data_1[0]["amount"] : '' +"</td>" +
			"<td>"+ data_1_len > 0 ? formatTime(data_1[0]["delivery_date"]) : '' +"</td>" +
			"<td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>— —</td><td>——</td>" +
			"</tr>" ;
		tbl.append( strNew );
	}

	// 该划线的都划线
	var newTr = [] ;
	var kids = 0 ; // 标记最后一个修改记录的索引号
	tbl.children("tr").each(function(){
		var thisNo = $(this).children("td:eq(0)").children("div:eq(0)").html() ;
		if( thisNo == no ){
			newTr.push( $(this) ) ;
			var klass = $(this).attr("class") ;
			if( klass == "throw"){ kids = newTr.length - 1 ; $(this).attr("class" , "hd");   }
		}
	}) ;
	for(var e = 0 ; e < newTr.length ; e++ ){
		if( kids > e ){
			newTr[e].attr("class" ,"throw") ;
		}
	}

}

// creator：张旭博，2017-05-26 09:40:28， 修改订单 - 修改商品 - 保存
function saveChangeGood() {
	var _thisTr = editGsObj["editSpan"].parent().parent() ;
	var no = editGsObj["info"]["num"] ;
	var goodId = $("#changeOrder .bounceChoose").children().eq(1).attr("id");
	var sid = $("#changeOrder .bounceChoose").children().eq(1).attr("sid");
	var amount = $("#changeGood").find(".goodNum").val();
	var addressId = $("#changeGood").find(".receiveAddress").val();
	var address = $("#changeGood").find(".receiveAddress option:selected").html();
	var requireDate = $("#changeGood").find(".requireDate").val();
	var unitPrice = $("#changeGood").find(".unitPrice").val();
	var unit = $("#changeGood").find(".unit").val();
	if(amount == editGsObj["info"]["amount"] && requireDate == editGsObj["info"]["deliveryDate"] && addressId == editGsObj["info"]["deliveryAddressId"]){
		$("#changeGood .inputTip").html("未对当前数据进行修改，无需保存") ;  return false ;
	}
    if(amount == "" || amount == undefined){
        $("#changeGood .inputTip").html('<i class="fa fa-exclamation-triangle"></i>请输入商品数量！');return false ;
    }else if(addressId === "" || addressId == undefined){
        $("#changeGood .inputTip").html('<i class="fa fa-exclamation-triangle"></i>请选择收货地点！');return false ;
    }
	editGsObj["info"]["amount"] = amount ;
	editGsObj["info"]["deliveryDate"] = requireDate ;
	editGsObj["info"]["deliveryAddress"] = address ;
	editGsObj["info"]["deliveryAddressId"] = addressId ;
	if( _thisTr.attr("class") == "newGoodItem"){
		editGsObj["editSpan"].siblings(".hd").html(JSON.stringify(editGsObj["info"])) ;
		_thisTr.children(":eq(2)").html(amount) ;
		_thisTr.children(":eq(3)").html(requireDate) ;
	}else{
		var goodListStr = "<tr class='newGoodItem'>" +
			"<td>"+ no +"</td>" +
			"<td class='radioChooseTitle'></td>" +
			"<td>"+ amount +"</td>" +
			"<td>"+ requireDate +"</td>" +
			"<td></td>" +
			"<td></td>" +
			"<td></td>" +
			"<td></td>" +
			"<td><span class='ty-color-blue' onclick='editGs($(this))'>修改</span><span class='hd'>"+ JSON.stringify(editGsObj["info"]) +"</span></td>" +
			"</tr>" ;
		_thisTr.after(goodListStr) ;
		editGsObj["editSpan"].attr("class","ty-color-gray").removeAttr("onclick") ;
	}
	bounce_Fixed.cancel() ;
	refreashAmount() ;
}
// creator: 侯杏哲，2018-05-30 10:11:52，根据修改的商品，刷新订单金额
function refreashAmount(){
	var sum = 0 ;
	$("#itemsCon .ty-color-blue").each(function(){
		var info = $(this).siblings(".hd").html() ;
		if( info ){
			info = JSON.parse( info ) ;
            if(info["hasInvoice"] == '1'){
                if(info["invoiceCategory"] == 1){ // 增专
                    sum += info["slUnitPriceNoTax"] * (1 + info.taxRate/100) * info.amount;
                }else{
                    sum += info["slUnitPrice"] * info.amount ; // 增普和其他
                }
            }else{
                sum += info["slUnitPriceNoTax"] * info.amount ;
            }
		}
	}) ;

	$("#changeOrder .contractAmount").text( sum.toFixed(2) ) ;
}
// creator: 张旭博，2018-05-30 10:07:37，删除商品 - 按钮
function deleteGoodBtn(selector) {
	if(hasAuthority(0) === true){
		$("#msTips .msTip").html("您没有此权限！");
		bounce_Fixed.show($("#msTips"));
	}else{
		selector.parent().parent().addClass("bounceChoose");
		selector.parent().parent().siblings("tr").removeClass();
		bounce_Fixed.show($("#deleteGood"));
	}
}
// creator: 张旭博，2018-05-30 10:07:53，删除商品 - 确定
function sureDeleteGood(){
	$("#changeOrder .bounceChoose").remove();
	bounce_Fixed.cancel();
}
// creator: 张旭博，2018-05-30 10:08:17，获取客户名称
function getCustomName() {
	$.ajax({
		url: "getPdCustomerName.do",
		success: function (data){
			if(data == ""||data == undefined){
				layer.msg("未获取到数据！")
			}else{
				var PdCustomerName = data["PdCustomerName"];
				var PdCustomerNameStr = "<option value=''>----- 请选择客户名称 -----</option>";
                for(var i in PdCustomerName){
                    PdCustomerNameStr +=
                        `<option invoice="${PdCustomerName[i].invoiceRequire}" code="${PdCustomerName[i].code}" value="${PdCustomerName[i].id}">${PdCustomerName[i].fullName}</option>`;
                }
				$(".cusSearchItems").html(PdCustomerNameStr);
			}
        }
	})
}
// creator: 张旭博，2018-05-30 10:10:39，查看订单 数字转换字符
function chargeHasInvoice(num) {
	if(num == 1){
		return "是"
	}else{
		return "否"
	}
}
// creator: 张旭博，2018-05-30 10:10:55，转换数字为正数
function chargeNum(num) {
	var num = Number(num);
	return num < 0?0:num;
}

// 阻止冒泡
function stop(e) {
    e.stopPropagation();
}
 // ========================  筛选功能 ==================================
var role = { "radio":0 , "check":[]   } ;
/* Creator : 侯杏哲 2017-07-25 初始化筛选框 */
function setRoleInit( objStr ){
	$("#"+ objStr +">div:eq(1)").children().each(function(){ // 单选框
		if( $(this).index() == 0){
			$(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1") ;
		}else{
			$(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
		}
	}) ;
	$("#"+ objStr +">div:eq(2)").children().each(function(){ // 复选框
		$(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1");
	}) ;
	role = { "radio":1 , "check":["5" , "6" , "7" , "8" , "9" , "10" , "11" ] } ;
}
/* Creator : 侯杏哲 2017-07-25 初始化筛选框  */
function setRoleBtn( dropdownBtnObj ){
	dropdownToggle(dropdownBtnObj) ;
	// 根据当前权限设置默认值
	var radio = role["radio"] ;
	var check = role["check"] ;
	dropdownBtnObj.siblings(".ty-dropdownCon").children(":eq(1)").children().each(function(){
		var code = $(this).children("i").attr("code") ;
		if(code == radio){
			$(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1") ;
		}else{
			$(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
		}
	}) ;
	dropdownBtnObj.siblings(".ty-dropdownCon").children(":eq(2)").children().each(function(){
		var code = $(this).children("i").attr("code") ;
		var isIn = $.inArray(code, check);
		if(isIn == -1 ){ // 不在数组中
			$(this).children("i").attr("class" , "fa fa-square-o").attr("isSet" , "0") ;
		}else{
			$(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1") ;
		}
	}) ;

}
/* Creator : 侯杏哲 2017-07-25 筛选按钮  - 设置要显示的数据  */
function setRole( tbodyObj , dropdownBtnObj , roleCon ){
	var radio = 0 ; // 单选值
	$("#"+ roleCon +">div:eq(1)").children().each(function(){
		var isset = $(this).children("i").attr("isSet") ;
		if(isset == 1){ radio = $(this).children("i").attr("code") ; 		}
	});
	var check = [] ; // 复选值
	var llk = $("#roleCon div:eq(2)").children() ; 
	$("#"+ roleCon +">div:eq(2)").children().each(function(){
		var isset = $(this).children("i").attr("isSet") ;
		if(isset == 1){  var code = $(this).children("i").attr("code") ;  check.push( code ) ; 		}
	});
	role = { "radio":radio , "check":check   } ;
	var codeName = chargeCode( radio ) ;
	tbodyObj.siblings("thead").children("tr").children(":eq(1)").html( codeName );
	tbodyObj.children().each(function(){
		var orcalO =  $(this).children(":eq(1)") ; 
		var oralVal = $(this).children(":eq(1)").html() ;
		if($.trim(oralVal) !=""){
			var info = $(this).attr("info");
			info = info && JSON.parse(info) ;
			$(this).children(":eq(1)").html( chargeCodeVal(radio , info)  );
		}
	});
	dropdownToggle( dropdownBtnObj ) ; 
}
/* Creator : 侯杏哲 2017-07-25 筛选 - 取消设置 */
function cancelSert( dropdownBtnObj ){
	dropdownToggle( dropdownBtnObj ) ;
}
// creator: 李玉婷，2022-05-04 08:58:07，发票内容选项
function invoiceExecutorSelect(obj) {
    let detail = JSON.parse(obj.siblings(".hd").html())
    if (detail.out_state && detail.out_state === '9' ){
        layer.msg("<p>在此不可对来源于服务合同的订单进行操作！</p><p>如需要，请到“服务合同”模块中操作。</p>");
        return false;
    }
    $("#invoiceExecutorSelect").data("obj", obj);
    bounce.show($("#invoiceExecutorSelect"));
    $("#invoiceExecutorSelect i").attr("class","fa fa-circle-o");
}
// creator: 李玉婷，2022-05-04 08:58:07，发票内容选项-确定
function invoiceTypeSelectSure() {
    if($("#invoiceExecutorSelect .fa-dot-circle-o").length > 0){
        let obj = $("#invoiceExecutorSelect").data("obj");
        let type = $("#invoiceExecutorSelect .fa-dot-circle-o").data("type");
        $("#operatorSelect").val(type);//1-销售 2 -财务
        ticketBtn(obj);
    }
}
/* Creator : 侯杏哲 2017-07-25 将代号翻译成明文*/
function chargeCode( code){
	switch(code){
		case "1" : return "商品代号";  break ;
		case "2" : return "外部名称";  break ;
		case "3" : return "产品图号";  break ;
		case "4" : return "内部名称";  break ;
		case "5" : return "商品代号";  break ;
		case "6" : return "外部名称";  break ;
		case "7" : return "产品图号";  break ;
		case "8" : return "内部名称";  break ;
		case "9" : return "单位";  break ;
		case "10" : return "含税单价";  break ;
		case "11" : return "备注";  break ; 
		default: return "" ; 
	}
}
/* Creator : 侯杏哲 2017-07-25 根据code 返回对应的键值  */
function chargeCodeVal( code , info){
	switch(code){
		case "1" : return info["slOuterSn"]|| "" ; break ;
		case "2" : return info["slOutterSnName"] || "" ; break ;
		case "3" : return info["slInnerSn"] || ""; break ;
		case "4" : return info["slInnerSnName"] || ""; break ;
		case "5" : return info["slOuterSn"] || "" ; break ;
		case "6" : return info["slOutterSnName"] || "" ; break ;
		case "7" : return info["slInnerSn"] || "" ; break ;
		case "8" : return info["slInnerSnName"] || "" ; break ;
		case "9" : return info["slUnit"] || "" ; break ;
		case "10" : return info["slUnitPrice"] || "" ; break ;
		case "11" : return info["memo"] || "" ; break ;
		default : return "" ; break ;
	}
}
// Creator : 侯杏哲 2017-07-25 筛选 - 单选按钮、复选按钮 的切换
$("div.orderItemTiny").on( "click", function(){
	var klass = $(this).children("i").attr("class") ;
	switch( klass ){
		case "fa fa-circle-o":
			$(this).siblings().each(function(){
				$(this).children("i").attr("class" , "fa fa-circle-o").attr("isSet" , "0") ;
			}) ;
			$(this).children("i").attr("class" , "fa fa-dot-circle-o").attr("isSet" , "1");
			break ;
		case "fa fa-dot-circle-o":
			break ;
		case "fa fa-check-square-o":
			$(this).children("i").attr("class" , "fa fa-square-o").attr("isSet" , "0");
			break ;
		case "fa fa-square-o":
			$(this).children("i").attr("class" , "fa fa-check-square-o").attr("isSet" , "1");
			break ;
		default:
			break ;
	}
});
// 筛选数据的显示
function showMess( _this ){
	var info = _this.parent().attr("info") ;
	info = JSON.parse(info); 
	var offsetObj = _this.offset();
	var leftObj =offsetObj.left;
	var topObj = offsetObj.top ;
	$("#tipCon").animate({ left: leftObj+ "px" , top:topObj+"px"  }, 10).show();
	var check = role["check"];
	var str = "" ; 
	for(var k = 5 ; k <=11 ; k++ ){
		var code = String(k) ; 
		var isIn = $.inArray(code, check);
		if(isIn != -1){
			var codeName = chargeCode( code ) ;  
			var codeVal = chargeCodeVal( code , info ) ;  
			str += "<span class='tipitemCon'>"+ codeName +"："+ codeVal +"</span>" ;
		}
	}
	$("#tipitem").html(str);
}
function hideMess( _this ){
	$("#tipCon").hide().offset({ left:0 , top:0 }); 
}
// ====================== 筛选功能 END ======================================
//输出相应的字符串
function compareInvStr(existItem, newItem){
    var tip = '';
    if(existItem.hasInvoice !== newItem.hasInvoice) {
        if (existItem.hasInvoice == '0'){
            tip = '不开票的商品';
        }else{
            switch (existItem.invoiceCategory) {
                case '1':
                    tip = '开增值税专用发票的商品';
                    break;
                case '2': //1-专用票,2-普通票,3-其他发票
                    tip = '开增值税普通发票的商品';
                    break;
                case '3':
                    tip = '其他发票的商品';
                    break;
                default:
                    tip = '';
                    break;
            }
        }
    }else{
        if(newItem.hasInvoice === '1') {
            if (existItem.invoiceCategory !== newItem.invoiceCategory) {
                switch (existItem.invoiceCategory) {
                    case '1':
                        tip = '开增值税专用发票的商品';
                        break;
                    case '2': //1-专用票,2-普通票,3-其他发票
                        tip = '开增值税普通发票的商品';
                        break;
                    case '3':
                        tip = '其他发票的商品';
                        break;
                    default:
                        tip = '';
                        break;
                }
            }
        }
    }
    return tip;
}

//=======================================财务开票优化==============================================
//  creator: lyt，2022-05-6 获取财务某订单开发票的详情
function applicationByFinance() {
    var selectNum = 0;
    $("#ticketTab tbody").children("tr").each(function () {
        var isSelect = $(this).children(":eq(0)").children("i").hasClass("fa-check-square-o");
        if (isSelect) {
            var num = $(this).children(":last").children("input").val();
            var maxNum = $(this).children(":eq(8)").html();
            if (Number(num) > 0) {
                if (Number(maxNum) < Number(num)) {
                    var name = $(this).children(":eq(2)").html();
                    layer.msg("商品 " + name + " <br>本次申请数量大于可申请的最大数量！");
                    $(this).children(":last").children("input").val(maxNum);
                }
                selectNum++;
            }
        }
    });
    if (selectNum > 0) {
        if( $(".goodsSelect").find(".fa-dot-circle-o").length < 1){
            layer.msg("下方选项您还未全部勾选！")
        } else if( $(".goodsSelect").find(".fa-dot-circle-o").length === 1){
            let invoicebefore = $("#applicationByFinance").data('invoicebefore');
            if(invoicebefore === 0){ // 需要预开票
                beforeInvoice()
                return false
            }

            var isAllNum = true;
            let invoice_require = $("#ticketSn").data("invoicerequire")
            $("#ticketTab tbody").children("tr").each(function () {
                var isSelect = $(this).children(":eq(0)").children("i").hasClass("fa-check-square-o");
                if (isSelect) {
                    var mtinfo = JSON.parse($(this).find(".hd").html())
                    if (invoice_require === 1 || invoice_require === 2) {
                        // count++;
                        var name = $(this).children(":eq(2)").html();
                        var num = $(this).children(":eq(9)").children("input").val();
                        if (Number(num) > 0) {
                        } else {
                            layer.msg("请录入商品 " + name + " 本次申请开票的数量！");
                            isAllNum = false;
                            return false;
                        }
                        var rate = 0;
                        var price = 0;
                        var rateAmount = 0;
                        if(invoice_require === 1){ // 增专
                            price = mtinfo.unit_price_notax
                            rate = Number((mtinfo["tax_rate"] && mtinfo["tax_rate"] ) || "0");
                            rateAmount = rate * mtinfo.unit_price_notax * num / 100;
                        }else if(invoice_require === 2){ // 增普
                            price = mtinfo.unit_price_invoice
                        } else{ //
                            price = mtinfo.unit_price_noinvoice
                        }
                        var amount = mtinfo.unit_price_notax * num; //金额
                        var itemid = $(this).attr("itemid");
                        var info = {
                            "ordersItem": itemid,
                            "amount": num,
                            "money": amount.toFixed(2),
                            "price": price,
                            "rate": rate,
                            "invoiceAmount": rateAmount.toFixed(2)//税额
                        };
                        goosList.push(info);
                    }
                }
            });
            if(!isAllNum){
                return false;
            }
            goPage(2)

            applyTicket(1);
        }else{
            layer.msg("您还未操作完成！")
        }
    }
}
//=======================================财务开票==============================================
// 时间控件初始化
laydate.render({elem: '#CDateOfArrival'});
laydate.render({elem: '#searchStart'});
laydate.render({elem: '#searchEnd'});

