$(function () {
    getModuleChangeInfo()
    $(".main").on ("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'agree':
                bounce.show($("#agreeTip"))
                break;
            case 'reject':
                bounce.show($("#rejectTip"))
                break;
            case 'seeProduct':
                var productId = $(this).attr("pid")
                if (productId === 'null') {
                    layer.msg("无产品信息")
                    return false
                }
                getProductDetail(productId)
                bounce_Fixed.show($("#seeProduct"));
                break
        }
    })
})

// creator: 张旭博，2020-04-17 09:34:05, 获取试用机构详情
function getModuleChangeInfo() {
    var id = getUrlParam("id")
    $.ajax({
        url: '../special/getEditOrgPopedomInfo.do',
        data: {
            id: id
        },
        success: function (res) {
            var data = res.data
            var newOrganizationInfo = data.newOrganizationInfo
            var oldOrganizationInfo = data.oldOrganizationInfo || {packageObject: null}
            var newPackage = JSON.parse(newOrganizationInfo.packageObject).mpPackages
            if (oldOrganizationInfo.packageObject !== null) {
                var oldPackage = JSON.parse(oldOrganizationInfo.packageObject).mpPackages
            } else {
                var oldPackage = { id: null, name: '无' }
            }

            var processList = data.approvalProcessList

            var mid = GetUrlQuery("mid")
            if (mid) {
                sphdSocket.send('updateUserSuspendMsg', {
                    userId: sphdSocket.user.userID,
                    messageId: mid
                })
            }

            var changeStr =   '<td>修改后产品</td>' +
                        '<td>' + newPackage.name + ' <a class="link-blue" type="btn" pid="'+newPackage.id+'" data-name="seeProduct">详情</a></td>' +
                        '<td>修改前产品</td>' +
                        '<td>' + oldPackage.name + ' <a class="link-blue" type="btn" pid="'+oldPackage.id+'" data-name="seeProduct">详情</a></td>'
            $("table.changeInfo tr").html(changeStr)

            // 处理审批流程
            var str = '<div>申请人：' + newOrganizationInfo.createName + ' ' + formatTime(newOrganizationInfo.createDate, true) + '</div>'
            for (var i in processList) {
                if (processList[i].approveStatus === '2') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'
                } else if (processList[i].approveStatus === '3') {
                    str +=  '<div>审批人：' + processList[i].userName + ' ' + formatTime(processList[i].handleTime, true) + '</div>'+
                        '<div>驳回理由：' + processList[i].reason + '</div>'
                } else {
                    if(getUrlParam("type") !== '1') {
                        str +=  '<div>等待' + processList[i].askName + '审批</div>'
                    }
                }
            }
            $(".processList").html(str)
            if (processList[0].approveStatus === '1' && sphdSocket.user.oid === 0 && getUrlParam("type") === '1') {
                $(".approveBtn").show()
            } else {
                $(".approveBtn").hide()
            }
            if (processList[0].approveStatus === '1') {
                $(".discribe").html('现申请修改 <span class="ty-color-blue">'+newOrganizationInfo.fullName+'</span> 的所用产品。')
            } else if (processList[0].approveStatus === '2') {
                $(".discribe").html('对 <span class="ty-color-blue">'+newOrganizationInfo.fullName+'</span> 的产品修改申请已被审批通过了！')
            } else {
                $(".discribe").html('对 <span class="ty-color-blue">'+newOrganizationInfo.fullName+'</span> 的产品修改申请被驳回！')
            }
        }
    })
}

// creator: 张旭博，2020-04-02 11:21:33,获取模块
function getModule() {
    $.ajax({
        url:"../special/getSelectPopedoms.do" ,
        success:function ( data ) {
            var success = data.success
            var list = data.data
            if (success !== 1) {
                // layer.msg("获取权限列表失败，请刷新重试！")
            } else {
                $(".oldOrg .module_body").html(getModuleStr(list, 'old'))
                $(".newOrg .module_body").html(getModuleStr(list, 'new'))

            }
        }
    })
}

function getModuleStr(list, string) {
    var str = '<div class="module_list">'
    for (var i in list) {
        var subModule = list[i].subPopdoms
        str +=  '<div class="module_row" level="1">'+
            '   <div class="module_item">' +
            '       <div class="ty-checkbox">' +
            '           <input type="checkbox" id="'+ string + list[i].pid + '-' + list[i].mid +'" mid="'+ list[i].mid +'" disabled>' +
            '           <label for="'+ string +list[i].pid + '-' + list[i].mid +'"></label> ' + list[i].name +
            '       </div>' +
            '   </div>'

        str += '<div class="module_list">'
        for (var j in subModule) {
            var thirdModule = subModule[j].subPopdoms
            str +=  '<div class="module_row" level="2">'+
                '   <div class="module_item">' +
                '       <div class="ty-checkbox">' +
                '           <input type="checkbox" id="'+ string +subModule[j].pid + '-' + subModule[j].mid +'" mid="'+ subModule[j].mid +'" disabled>' +
                '           <label for="'+ string +subModule[j].pid + '-' + subModule[j].mid +'"></label> ' + subModule[j].name +
                '       </div>' +
                '   </div>'
            str += '<div class="module_list">'
            for (var k in thirdModule) {
                str +=  '<div class="module_row" level="3">'+
                    '   <div class="module_item">' +
                    '       <div class="ty-checkbox">' +
                    '           <input type="checkbox" id="'+ string +thirdModule[k].pid + '-' + thirdModule[k].mid +'" mid="'+ thirdModule[k].mid +'" disabled>' +
                    '           <label for="'+ string +thirdModule[k].pid + '-' + thirdModule[k].mid +'"></label> ' + thirdModule[k].name +
                    '       </div>' +
                    '   </div>'+
                    '</div>'
            }
            str += '</div>'
            str += '</div>'
        }
        str += '</div>'
        str += '</div>'
    }
    return str
}

// creator: 张旭博，2020-04-17 11:06:19,审批试用
function approveTrial(approvalStatus) {
    var id = getUrlParam("id")
    var data = {
        id: id,
        userId: sphdSocket.user.userID,
        approvalStatus: approvalStatus
    }
    if (approvalStatus === 2) {
        data.reason = $(".reason").val()
    }
    $.ajax({
        url: '/special/approvalEditOrgPopedom.do',//id要处理数据id，userId 登录人id,approvalStatus 审批 1-批准，否则认为驳回, reason 驳回理由（驳回时用）
        data: data,
        success: function (res) {
            var data = res.data
            if (data === 1) {
                layer.msg("操作成功！")
                location.reload()
            } else {
                $("#tip .tipCon").html("操作失败！")
                bounce.show($("#tip"))
            }
        }
    })
}