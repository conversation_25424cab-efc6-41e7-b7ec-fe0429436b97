var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#operatInstructions"));
bounce_Fixed3.show($("#scanGoods"));
bounce_Fixed2.cancel()
bounce_Fixed3.cancel()
$(function() {
    showMainCon(1);
    getList(1,"");
    invoiceSettings()
    $("body").on("click",".funBtn, .ty-btn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    $.ajax({
        url:'../service/getProjectList.do',
        data: { serviceId: '' }
    }).then(res => {
        let data = res.data
        let list = data.list || [];
        $("body").data('projects', list)
    })
})

// creator: 张旭博，2023-12-07 11:44:25， 渲染税率选择框
function invoiceSettings(){
    $.ajax('../invoiceSetting/getFinanceInvoiceSetting.do')
        .then(res => {
            let invoiceList = res.financeInvoiceSettings
            let str = '<option value="">请选择</option>';
            if (invoiceList) {
                if (invoiceList.length > 0) {
                    for (let item of invoiceList) {
                        if (item.category == 1){
                            let rateStr = item.enableTaxTate;
                            let rateArr = rateStr.split(",");
                            if (rateArr && rateArr.length >0){
                                for (let it of rateArr) {
                                    str += `<option value="${it}">${it}%</option>`;
                                }
                            }
                        }
                    }
                }
            }
            $("[name='taxRate']").html(str);
        })
}

// creator: hxz，2022-08-22 08:57:55， 新增套餐 - 第一步（选择是否按周期收费，是否包含产品）
function addService() {
    bounce.show($("#serviceInitChoose"));
    $("#serviceInitChoose input:radio").prop("checked", false)
}

// creator: hxz，2022-08-22 08:57:55， 新增套餐 - 第一步确定（进入主页面）
function serviceInitChoose() {
    let isPeriodical = $("#serviceInitChoose [name='isPeriodical']:checked").val()
    let isMixture = $("#serviceInitChoose [name='isMixture']:checked").val()

    if (!isPeriodical || !isPeriodical) {
        layer.msg("请将选项补充完整")
        return false
    }

    // 初始化数据
    $("#addService").data("main", {
        project: {
            projects: [],
            sumAll: '00.00'
        },
        good2: {
            goods2: [],
            sumAll: '00.00'
        },
        good1: {
            goods1: [],
            sumAll: '00.00'
        }
    })
    $("#addService").data("trail", {
        type: 0,
        state: 0
    })
    let initData = {
        isPeriodical: Number(isPeriodical), // 是否为周期
        isMixture:  Number(isMixture) // 是否有商品
    }
    $("#addService").data("init", initData)
    $("#addService").data("isLimit", true) // 默认是限制选择

    // 初始化头部提示
    let headTipStr = `请录入${ initData.isPeriodical === 0 ? '不':'' }按周期收费、${ initData.isMixture === 0 ? '不':'' }包含商品的套餐！`
    $("#addService .headTip").html(headTipStr);

    // 初始化部分模块显隐
    $("#addService .errorTip").hide()
    $("#addService .projectOrGoods").hide();
    $("#addService .service_content table tbody").html("")
    $("[name='unitPrice'], [name='unitPriceNotax']").prop("disabled", true)
    $("#addService .addGoodsBtn").css('display', initData.isMixture === 1?'inline': 'none');
    setAddServiceTrailPart()

    // 初始化表单
    $("#addService select").val("")
    $("#addService input:text").val("")


    if(initData.isMixture === 1){
        $("#addService .addGoodsBtn").show();
    }else{
        $("#addService .addGoodsBtn").hide();
    }
    if(initData.isPeriodical == 1){
        $("#addService .periodDurationInfo").show();
        $("#addService [name='periodDuration']").prop("disabled", true).val("");
        $("#addService [name='periodUnit']").prop("disabled", false).val("");
    }else {
        $("#addService .periodDurationInfo").hide();
    }
    // 获取操作说明（不同的选择展示不同内容）
    getOperateStr(initData)

    // 初始化体验/试用
    $("#addService").removeData("trailed")
    setAddServiceTrailPart()
    bounce.show($("#addService"))
}

// creator: 张旭博，2024-01-29 02:41:06， 向套餐内添加项目
function addProject() {
    let selectType = judgeProjects() // 根据已添加的项目 判断当前选择类型（全周期、全非周期、混合）

    $("#addProject .noLimit").prop("checked", !($("#addService").data("isLimit")))

    // 获取可选择的项目并渲染
    getAllProjects(selectType)
}

// creator: 张旭博，2023-12-12 02:36:35， 判断当前选择的项目是周期性、非周期性或者混合的
function judgeProjects() {
    let isChange = $("#editService").is(":visible")
    let dialog = isChange?$("#editService"):$("#addService")
    let selectType = 'noChoose'
    let arr = [] // 【0，0，1，1，0】 承载每个项目周期性参数的数组
    let projectsObj = $(".bonceContainer:visible .serviceTab tbody tr") // 新增套餐中已选择的项目
    let nowProjectsObj = dialog.find(".checkbox_selectProject:checked") // 新增项目弹窗内选择的项目
    projectsObj.each(function () {
        let period = $(this).data("period")
        arr.push(period)
    })
    if (dialog.is(":visible")) {
        // 新增项目弹窗 显示时才合并计算
        nowProjectsObj.each(function () {
            let period = $(this).parents("tr").data("period")
            arr.push(period)
        })
    }

    if (arr.length > 0) {
        let periodLength = arr.filter(item => item === 1).length
        let noPeriodLength = arr.filter(item => item === 0).length

        selectType = periodLength === arr.length ? 'period': ( noPeriodLength === arr.length ? 'noPeriod': 'mix' )
    }
    return selectType
}

// creator: 张旭博，2023-12-12 03:58:51， 获取除去已选择的项目剩下的项目数据
function getAllProjects(selectType) {
    let initData = $("#addService").data("init")
    let projectsObj = $(".bonceContainer:visible .serviceTab tbody tr")
    let projects = []
    if (projectsObj.length > 0) {
        projectsObj.each(function () {
            let data = $(this).find(".hd").html()
            data = JSON.parse(data)
            projects.push(data)
        })
    }
    let list = $("body").data("projects")
    let isLimit = $("#addService").data("isLimit") // 默认是限制选择
    let newData = list.filter(e1 => !projects.some(e2 => e2.id === e1.id));
    let str = ''
    newData.forEach((item)=>{
        let isPeriodical = item.isPeriodical
        let inputStr = '<input class="inputNum" type="text" disabled onkeyup="clearNum(this)">'
        let isPeriodicalStr = isPeriodical === 1 && initData.isPeriodical === 1? inputStr: ''
        let selectDisabled = (selectType === 'period' && !isPeriodical && isLimit) || (selectType === 'noPeriod' && isPeriodical && isLimit) ? 'disabled': ''
        str+=  `<tr data-period="${isPeriodical}">
                        <td>
                            <input type="checkbox" class="checkbox_selectProject" onchange="selectProject($(this))" ${selectDisabled}/>
                            <span class="hd">${ JSON.stringify(item) }</span>
                        </td>
                        <td>${ item.code } / ${ item.name }</td>
                        <td>${ item.unit || "" }</td>
                        <td>${ item.unitPriceReference || "" }</td>
                        <td class="changeTd periodQuantity">${isPeriodicalStr}</td>
                        <td class="changeTd itemQuantity">${inputStr}</td>
                    </tr>`
    })
    $("#addProject tbody").html(str);
    bounce_Fixed.show($("#addProject"));
}

// creator: 张旭博，2023-12-08 10:03:26， 选择项目勾选逻辑
function selectProject(selector) {
    let checked = selector.prop("checked")
    selector.parents("tr").find("input.inputNum").prop("disabled", !checked)
    if (!checked) {
        selector.parents("tr").find("input.inputNum").val("")
    }
    let noLimit = $(".noLimit").prop("checked")
    if (!noLimit) {
        const selectProjects =  selector.parents("tbody").find(".checkbox_selectProject:checked")
        if (selectProjects.length === 0) {
            selector.parents("tbody").find(".checkbox_selectProject").prop("disabled", false)
        } else {
            let firstSelectPeriod = selectProjects.eq(0).parents("tr").data("period")
            selector.parents("tbody").find("tr[data-period='"+(1 - firstSelectPeriod)+"']").find(".checkbox_selectProject").prop("disabled", true)
        }
    }
}

// creator: hxz，2022-08-22 08:57:55， 向套餐内添加项目 确定
function addProjectSure() {
    let isChange = $("#editService").is(":visible")
    let ele = isChange?$("#editService"):$("#addService")
    let checkedProjects = $("#addProject table .checkbox_selectProject:checked");
    let init = $("#addService").data("init");
    let mainData =  $("#addService").data("main")
    let nullNum = 0
    let addProjects = []
    if(checkedProjects.length > 0){
        checkedProjects.each(function () {
            let info = $(this).siblings(".hd").html();
            info = JSON.parse(info)
            let periods = $(this).parents("tr").find(".periodQuantity input").val()
            let projects = $(this).parents("tr").find(".itemQuantity input").val()
            if( periods === '' || projects === '' ){
                nullNum++;
            } else {
                info.periodQuantity = periods || 1
                info.itemQuantity = projects
                info.productOrProject = "project"
                info.orders = $(".bonceContainer:visible .serviceTab tbody tr.productOrProject").length
                addProjects.push(info)
            }
        })

        if ( nullNum>0 ){
            layer.msg("请把内容补充完整！")
            return false
        }
        addProjects.forEach(function (value, index){
            mainData.project.projects.push(value)
        })
        mainData.project.sumAll = getSumAll(mainData.project.projects, 'project')
        if (mainData.project.projects.length > 0) {
            ele.find(".projectContent").show();
            ele.find(".projectContent tbody").html(getStrByName(mainData.project.projects, 'project'));
            ele.find(".projectContent .sumAll").html(mainData.project.sumAll)
        } else {
            ele.find(".projectContent").hide().find("tbody").html('')
        }

        calcMaxPeriod()
        bounce_Fixed.cancel()
    }else {
        layer.msg("请先选择好项目！")
    }
}

// creator: 张旭博，2024-07-29 03:08:11， 计算最大周期
function calcMaxPeriod(needClear = true) {
    let selectType = judgeProjects() // 根据已添加的项目 判断当前选择类型（全周期、全非周期、混合）

    // 全为周期项目时 需计算出最大周期
    let maxPeriod = 0

    if (selectType === 'period') {
        let projectsObj = $(".bonceContainer:visible .serviceTab tbody tr")
        let periods = []
        projectsObj.each(function (){
            let thisInfo = JSON.parse($(this).find(".hd").html())
            let periodUnit = thisInfo.periodUnit // 7:年 4:月
            let periodDuration = thisInfo.periodDuration
            let periodQuantity = thisInfo.periodQuantity
            let period = (periodUnit === 7? (12 * periodDuration ):periodDuration) * periodQuantity // 月数 * 周期数 (单位为年的还需要 * 12)
            periods.push(period)
        })
        maxPeriod = Math.max(...periods)
        $(".bonceContainer:visible .periodDuration").append(`<option value="${maxPeriod}">${maxPeriod}</option>`).val(maxPeriod)
        $(".bonceContainer:visible .periodDuration").prop("disabled", true)
        $(".bonceContainer:visible .periodUnit").prop("disabled", true).val('4')
    } else {
        $(".bonceContainer:visible .periodDuration").prop("disabled", false).html('')
        $(".bonceContainer:visible .periodUnit").prop("disabled", false).val('')
        if (needClear) {
            $(".bonceContainer:visible .periodDuration").html('')
            $(".bonceContainer:visible .periodUnit").val('')
        }

    }
}


// creator: 张旭博，2024-04-09 09:07:28， 获取项目或商品计算的总值
function getSumAll(data, name) {
    let projectOrGoodData = data
    let sumAll = 0

    if (name === 'project') {
        for (let item of projectOrGoodData) {
            // 计算总值和合计
            let unitPrice = Number(item.unitPriceReference || 0)
            let amount = item.itemQuantity * item.periodQuantity * unitPrice
            item.amount = amount && amount.toFixed(2);
            sumAll += Number(item.amount);
        }
    } else {
        for (let item of projectOrGoodData) {
            // 计算总值和合计
            let unitPrice = Number(item.unitPriceReference || 0)
            let amount = item.deliveryQuantity * unitPrice
            item.amount = amount && amount.toFixed(2);
            sumAll += Number(item.amount);
        }
    }
    return sumAll
}

// creator: 张旭博，2024-03-04 10:11:56， 获取项目或者商品的字符串
function getStrByName(data, name) {
    let str = ``
    if (name === 'project') {
        for (let item of data) {
            str += `<tr class="projectTr" data-period="${item.isPeriodical}">
                          <td>${item.code} / ${item.name}</td>
                          <td>${Number(item.unitPriceReference || 0)}</td>
                          <td>${item.periodQuantity} * ${item.itemQuantity}</td>
                          <td>${item.amount}</td>
                          <td>
                             <span data-fun="editNumPro" class="link-blue funBtn">修改数量</span>
                             <span data-fun="delPro" class="link-red funBtn">删除</span>
                             <span class="hd">${JSON.stringify(item)}</span>
                          </td>
                     </tr>`
        }
    } else {
        for (let item of data) {
            str += `<tr class="projectTr">
              <td>${item.code} / ${item.name}</td>
              <td>${Number(item.unitPriceReference || 0)}</td>
              <td><span data-fun="scanGoods" class="link-blue funBtn">查看</span></td>
              <td>${item.deliveryQuantity}</td>
              <td>${item.amount}</td>
              <td>
                     <span data-fun="editNumGoods" class="link-blue funBtn">修改</span>
                     <span data-fun="delGoods" class="link-red funBtn">删除</span>
                     <span class="hd">${JSON.stringify(item)}</span>
              </td>
         </tr>`
        }
    }
    return str
}


// creator: hxz，2022-08-22 08:57:55， 向套餐内添加商品 - 选择是否
function addGoodsInit(obj) {
    let where = obj.data("where")
    $("#addGoodsInit input:radio").prop("checked", false)
    $("#addGoodsInit .deliverySequence2").hide();
    $("#addGoodsInit .deliverySequence2 input").val('');
    let init = $("#addService").data("init")
    if (init.isPeriodical == 0) {
        $("#addGoodsInit .part_isPeriod").hide()
    } else {
        $("#addGoodsInit .part_isPeriod").show()
    }
    bounce_Fixed.show($("#addGoodsInit"));
    $("#addGoodsInit").data("where", where);
    $("#addGoodsInit [name='deliverySequence']").unbind().on("change", function () {
        $(this).parents(".dotItem").next(".deliverySequence2").find("input").val("")
        $(this).parents(".dotItem").next(".deliverySequence2").css("display", $("#addGoodsInit [name='deliverySequence']:checked").val() === '2'?'block':'none')
    })
}

// creator: hxz，2022-08-22 08:57:55， 向套餐内添加商品 - 第一步确定
function addGoodsInitSure() {
    let select = $("#addGoodsInit input:radio:checked");
    let init = {}
    if(select.length === 3){
        select.each(function () {
            let name = $(this).attr("name");
            let val = $(this).val()
            init[name] = val;
        })
        if(init.deliverySequence === '2'){
            init.deliveryDuration = $("#deliverySequenceNum").val();
            if(init.deliveryDuration.length === 0){
                layer.msg("请输入天数！");
                return false
            }
        }
        $("#addGoods").data("init", init);
        bounce_Fixed.show($("#addGoods"))
        let goods = [] // 已经录入过的商品
        let goods2Obj = $(".bonceContainer:visible .goods2Tab tbody tr")
        let goods1Obj = $(".bonceContainer:visible .goods1Tab tbody tr")
        if (goods2Obj.length > 0) {
            goods2Obj.each(function () {
                let data = $(this).find(".hd").html()
                data = JSON.parse(data)
                goods.push(data)
            })
        }
        if (goods1Obj.length > 0) {
            goods1Obj.each(function () {
                let data = $(this).find(".hd").html()
                data = JSON.parse(data)
                goods.push(data)
            })
        }
        $.ajax({
            url: '../service/getProductList.do',
            data: { serviceId: '' },
            success:function(data){
                let res = data.data
                let list = res.list || [];

                // 排除已经录入过的商品
                let newData = list.filter(e1 => !goods.some(e2 => e2.id === e1.id));
                let str = ""
                // 是否为先付套餐后提供商品
                let deliveryStr = ''
                if (init.deliverySequence === '1') {
                    if (init.deliveryPlace === '1') {
                        deliveryStr = '--'
                    } else {
                        deliveryStr = `<input class="inputText" type="text" readonly placeholder="请选择" disabled onclick="deliveryTimeSet($(this))">`
                    }
                } else {
                    deliveryStr = `客户付款后，商品提供给客户需要${init.deliveryDuration}天`
                }
                newData.forEach((item)=>{
                    item.code = item.outerSn
                    item.name = item.outerName
                    str +=  `<tr>
                                <td>
                                    <input type="checkbox" class="checkbox_selectGood" onchange="selectGood($(this))"/>
                                    <span class="hd">${ JSON.stringify(item) }</span>
                                </td>
                                <td>${ item.code } / ${ item.name }</td>
                                <td>${ item.unit }</td>
                                <td>${ item.unitPriceReference || ""}</td>
                                <td class="changeTd deliverySet">${deliveryStr}</td>
                                <td class="changeTd deliveryQuantity"><input class="inputNum" type="text" disabled onkeyup="clearNum(this)"></td>
                            </tr>`

                })
                $(".tbl_addGood tbody").html(str);
            }
        })
    } else {
        layer.msg("请设置好套餐内商品的情况")
    }
}

// creator: hxz，2022-08-22 08:57:55， 向套餐内添加商品 - 第二步确定
function addGoodsSure() {
    let isChange = $("#editService").is(":visible")
    let ele = isChange?$("#editService"):$("#addService")
    let checkedProjects = $("#addGoods table .checkbox_selectGood:checked");
    let init = $("#addGoods").data("init"); // 商品初始化内容
    let nullNum = 0
    let mainData =  $("#addService").data("main")
    let addGoods = []
    checkedProjects.each(function () {
        let info = $(this).siblings(".hd").html()
        info = JSON.parse(info);
        info.init = init
        info.deliveryQuantity = $(this).parents("tr").find(".deliveryQuantity input").val();
        let isOk = true
        if(init.deliveryPlace === '2' && init.deliverySequence === '1'){
            let deliveryObj = $(this).parents("tr").find(".deliverySet input")
            info.deliveryStr  = deliveryObj.val() + '发货'
            info.deliveryStage = deliveryObj.attr("deliveryStage")
            info.deliveryLimit = deliveryObj.attr("deliveryLimit")
            if(!info.deliveryStage || !info.deliveryLimit || !info.deliveryQuantity){
                isOk = false
            }
        } else if (init.deliverySequence === '2') {
            info.deliveryStr  = $(this).parents("tr").find(".deliverySet").html()
        } else if (!info.deliveryQuantity) {
            isOk = false;
        }
        addGoods.push(info)

        if(!isOk){
            nullNum++;
        }
    })
    if (nullNum > 0) {
        layer.msg("请将内容补充完整！")
        return false;
    }
    addGoods.forEach(function (value, index){
        if(init.deliveryTerm === '2'){
            mainData.good2.goods2.push(value)
        } else {
            mainData.good1.goods1.push(value)
        }
    })

    if(init.deliveryTerm == 2){
        mainData.good2.sumAll = getSumAll(mainData.good2.goods2, 'good2')
        if (mainData.good2.goods2.length > 0) {
            ele.find(".good2Content").show();
            ele.find(".good2Content tbody").html(getStrByName(mainData.good2.goods2, 'good2'));
            ele.find(".good2Content .sumAll").html(mainData.good2.sumAll)
        } else {
            ele.find(".good2Content").hide().find("tbody").html('')
        }
    } else {
        mainData.good1.sumAll = getSumAll(mainData.good1.goods1, 'good1')
        if (mainData.good1.goods1.length > 0) {
            ele.find(".good1Content").show();
            ele.find(".good1Content tbody").html(getStrByName(mainData.good1.goods1, 'good1'));
            ele.find(".good1Content .sumAll").html(mainData.good1.sumAll)
        } else {
            ele.find(".good1Content").hide().find("tbody").html('')
        }
    }

    bounce_Fixed.cancel()

}

// creator: hxz，2022-08-22 08:57:55，向套餐内添加商品 - 查看商品
function scanGoods(obj) {
    let info = obj.parents("tr").find(".hd").html();
    info = JSON.parse(info)
    if (!info.init) {
        info.init = {
            deliveryTerm: info.deliveryTerm,
            deliveryPlace: info.deliveryPlace,
            deliverySequence: info.deliverySequence,
            deliveryDuration: info.deliveryDuration
        }
    }
    let str = `本商品${ info.init.deliveryTerm === '2' ? '仅第一个收费周期提供' : '每个收费周期都需提供' } ，
        ${ info.init.deliveryPlace == 2 ? '需交给客户' : '需存放于本机构' }，
        向客户提供时${ info.init.deliverySequence === '1' ? '不' : '' }需要先付套餐的款
        <br/>
        ${info.deliveryStr || ''}
    `
    $("#scanGoodsTip").html(str)
    bounce_Fixed3.show($("#scanGoods"))
}

// creator: hxz，2022-08-31 08:57:58 修改项目数量
function editNumPro(obj) {
    let info = JSON.parse(obj.siblings(".hd").html());
    $("#editNumPro .periodQuantity").val(info.periodQuantity);
    $("#editNumPro .itemQuantity").val(info.itemQuantity);
    bounce_Fixed.show($("#editNumPro"))

    let mainData = $("#addService").data("main");
    let initData = $("#addService").data("init");
    let isPeriodical = initData.isPeriodical
    let selectType = judgeProjects() // 判断是否为全部都是周期性的项目（只有选择的全部是有周期的项目才会处修改全部修改）

    let thisPeriod = obj.parents("tr").data("period")

    $("#editNumPro .periodTip").hide()
    $("#editNumPro .periodPart").show();


    if (selectType === 'period') {
        $("#editNumPro .periodTip").show()
    }

    if (thisPeriod === 0) {
        $("#editNumPro .periodPart").hide();
    }
    $("#editNumPro .sureBtn").unbind().on("click", function () {
        let periodQuantity = $("#editNumPro .periodQuantity:visible").val()
        let itemQuantity = $("#editNumPro .itemQuantity").val()
        let isChange = $("#editService").is(":visible")
        let ele = isChange?$("#editService"):$("#addService")

        if( periodQuantity === '' || itemQuantity === ''){
            layer.msg("请将内容补充完整");
            return false;
        }

        let mainData = $("#addService").data("main")
        for (let item of mainData.project.projects) {
            if (item.id === info.id) {
                item.periodQuantity = periodQuantity || 1
                item.itemQuantity = itemQuantity
            } else {
                if (selectType === 'period') {
                    // 周期性的需要特殊处理（周期 一处动随着动）
                    item.periodQuantity = periodQuantity
                }
            }

        }
        $("#addService").data("main", mainData)
        mainData.project.sumAll = getSumAll(mainData.project.projects, 'project')
        ele.find(".projectContent tbody").html(getStrByName(mainData.project.projects, 'project'));
        ele.find(".projectContent .sumAll").html(mainData.project.sumAll)
        calcMaxPeriod()
        bounce_Fixed.cancel()
    })
}

// creator: hxz，2022-08-31 08:57:55，  删除项目
function delPro(obj) {
    let info = JSON.parse(obj.siblings(".hd").html());
    bounce_Fixed.show($("#delProTip"))
    $("#delProTip .sureBtn").unbind().on("click", function () {
        let mainData = $("#addService").data("main");
        let projects = mainData.project.projects
        projects.splice(projects.findIndex(item => item.id===info.id), 1)
        mainData.project.sumAll = getSumAll(mainData.project.projects, 'project')
        let isChange = $("#editService").is(":visible")
        let ele = isChange?$("#editService"):$("#addService")
        if (mainData.project.projects.length > 0) {
            ele.find(".projectContent").show();
            ele.find(".projectContent tbody").html(getStrByName(mainData.project.projects, 'project'));
            ele.find(".projectContent .sumAll").html(mainData.project.sumAll)
        } else {
            ele.find(".projectContent").hide().find("tbody").html('')
        }
        bounce_Fixed.cancel()
    })

}

// creator: hxz，2022-08-22 08:57:55，向套餐内添加商品 - 修改商品
function editNumGoods(obj) {
    let isChange = $("#editService").is(":visible")
    let ele = isChange?$("#editService"):$("#addService")
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info)
    $("#editNumGoods .oldDeliveryQuantity").val(info.deliveryQuantity)
    $("#editNumGoods .deliveryQuantity").val("")

    let init = info.init;
    if(init.deliveryPlace === '2' && init.deliverySequence === '1'){
        $("#editNumGoods .oldDeliveryTimeSet").val(info.deliveryStr)
        $("#editNumGoods .deliveryTimeSet").val("")
        $("#editNumGoods .deliverySetPart").show()
    }else{
        $("#editNumGoods .deliverySetPart").hide()
    }
    bounce_Fixed.show($("#editNumGoods"))
    $("#editNumGoods .sureBtn").unbind().on("click", function (){
        let deliveryQuantity = $("#editNumGoods .deliveryQuantity").val()
        let deliveryTimeSet = $("#editNumGoods .deliveryTimeSet").val()
        if(init.deliveryPlace === '2' && init.deliverySequence === '1'){
            if( deliveryQuantity === '' || deliveryTimeSet === ''){
                layer.msg("请将内容补充完整");
                return false;
            }
        }else{
            if( deliveryQuantity === ''){
                layer.msg("请将内容补充完整");
                return false;
            }
        }


        let mainData = $("#addService").data("main")
        if (info.init.deliveryTerm === '2') {
            for (let item of mainData.good2.goods2) {
                if (item.id === info.id) {
                    item.deliveryQuantity = deliveryQuantity
                    if (info.init.deliveryPlace === '2' && info.init.deliverySequence === '1') {
                        let deliveryObj = $("#editNumGoods").find(".deliverySet input")
                        item.deliveryStr = deliveryObj.val()
                        item.deliveryStage = deliveryObj.attr("deliveryStage")
                        item.deliveryLimit = deliveryObj.attr("deliveryLimit")
                    }
                }
            }
            mainData.good2.sumAll = getSumAll(mainData.good2.goods2, 'good2')
            ele.find(".good2Content tbody").html(getStrByName(mainData.good2.goods2, 'good2'));
            ele.find(".good2Content .sumAll").html(mainData.good2.sumAll)
        }
        if (info.init.deliveryTerm === '1') {
            for (let item of mainData.good1.goods1) {
                if (item.id === info.id) {
                    item.deliveryQuantity = deliveryQuantity
                    if (info.init.deliveryPlace === '2' && info.init.deliverySequence === '1') {
                        let deliveryObj = $("#editNumGoods").find(".deliverySet input")
                        item.deliveryStr = deliveryObj.val()
                        item.deliveryStage = deliveryObj.attr("deliveryStage")
                        item.deliveryLimit = deliveryObj.attr("deliveryLimit")
                    }
                }
            }
            mainData.good1.sumAll = getSumAll(mainData.good1.goods1, 'good1')
            ele.find(".good1Content tbody").html(getStrByName(mainData.good1.goods1, 'good1'));
            ele.find(".good1Content .sumAll").html(mainData.good1.sumAll)
        }
        bounce_Fixed.cancel()
    })
}

// creator: hxz，2022-08-22 08:57:55，向套餐内添加商品 - 删除商品
function delGoods(obj) {
    let info = JSON.parse(obj.siblings(".hd").html());
    let isChange = $("#editService").is(":visible")
    let ele = isChange?$("#editService"):$("#addService")
    bounce_Fixed.show($("#delProTip"))
    $("#delProTip .sureBtn").unbind().on("click", function () {
        let mainData = $("#addService").data("main");
        if (info.init.deliveryTerm == 2) {
            let goods2s = mainData.good2.goods2
            goods2s.splice(goods2s.findIndex(item => item.id===info.id), 1)
            mainData.good2.sumAll = getSumAll(mainData.good2.goods2, 'good2')
            if (mainData.good2.goods2.length > 0) {
                ele.find(".good2Content").show();
                ele.find(".good2Content tbody").html(getStrByName(mainData.good2.goods2, 'good2'));
                ele.find(".good2Content .sumAll").html(mainData.good2.sumAll)
            } else {
                ele.find(".good2Content").hide().find("tbody").html('')
            }
        } else if(info.init.deliveryTerm == 1) {
            let goods1s = mainData.good1.goods1
            goods1s.splice(goods1s.findIndex(item => item.id===info.id), 1)
            mainData.good1.sumAll = getSumAll(mainData.good1.goods1, 'good1')
            if (mainData.good1.goods1.length > 0) {
                ele.find(".good1Content").show();
                ele.find(".good1Content tbody").html(getStrByName(mainData.good1.goods1, 'good1'));
                ele.find(".good1Content .sumAll").html(mainData.good1.sumAll)
            } else {
                ele.find(".good1Content").hide().find("tbody").html('')
            }
        }
        bounce_Fixed.cancel()
    })
}

// creator: hxz，2022-08-22 08:57:55，向套餐内添加商品 - 选择发货时间
function deliveryTimeSet(obj) {
    if (!obj.is(":disabled")) {
        bounce_Fixed2.show($("#deliveryTimeSet"));
        $("#deliveryTimeSet").find("select, input").val('')
        $("#deliveryTimeSet .sureBtn").unbind().on("click", function (){
            let timeBetweenValue =  $("#deliveryTimeSet").find(".timeBetween").val()
            let timeBetween =  $("#deliveryTimeSet").find(".timeBetween option:selected").html()
            let days =     $("#deliveryTimeSet").find(".days").val()
            if(timeBetween.length > 0 && days.length > 0){
                obj.val(`${timeBetween}${days}日内`).attr("deliveryStage", timeBetweenValue).attr("deliveryLimit", days);
                bounce_Fixed2.cancel()
            }else{
                layer.msg("请把内容补充完整！")
            }
        })
    }
}


// creator: 张旭博，2023-12-08 10:03:39， 选择商品勾选逻辑
function selectGood(selector) {
    selector.parents("tr").find("input:text").prop("disabled", !selector.prop("checked"))
    selector.parents("tr").find("input:text").val("")
}

function showNext(selector) {
    selector.parents(".dotItem").next(".deliverySequence2").css("display", selector.prop("checked")?'block':'none')
}

// creator: 张旭博，2023-12-08 10:02:28， 无限制选择 - 勾选逻辑
function noLimit() {
    let noLimit = $(".noLimit").prop("checked")
    if (noLimit) {
        $("#addService").data("isLimit", false)
        $("#addProject").find(".checkbox_selectProject").prop("disabled", false)
    } else {
        let selectType = judgeProjects() // 根据已添加的项目 判断当前选择类型（全周期、全非周期、混合）

        if (selectType === 'mix') {
            layer.msg('当前选择的项目既包含按周期收费的项目，又包含不按周期收费的项目，无法修改此状态')
            $(".noLimit").prop("checked", true)
        } else {
            $("#addService").data("isLimit", true)
            $("#addProject").find(`tr[data-period='${selectType === 'period'?0:1}']`).find('.checkbox_selectProject').prop("disabled", true).val('')
            $("#addProject").find(`tr[data-period='${selectType === 'period'?0:1}']`).find(".inputNum").prop("disabled", true).val('')
        }
    }
}

// creator: 张旭博，2024-01-16 09:56:44， 更改 体验环节/试用流程
function changeService() {
    let trailData = $("#addService").data("trail")
    if (trailData.state === 0) {
        layer.msg("目前没有可改的数据！")
        return false
    } else {
        bounce_Fixed.show($("#setExperienceOrTrail"))
        initDialog('setExperienceOrTrail')
    }
}

// creator: 张旭博，2023-12-08 09:51:39， 体验环节或试用流程 改变状态
function changeState() {
    let trailedData = $("#addService").data("trailed")

    if (trailedData) {
        bounce_Fixed.show($("#changeStateTip"))
        $("#changeStateTip .sureBtn").unbind().on("click", function () {
            bounce_Fixed.cancel()
            $("#addService").data("trail", {
                type: 0,
                state: 0
            })
            $("#addService").removeData("trailed")
            setAddServiceTrailPart()
        })
    } else {
        let mainData = $("#addService").data("main")
        let initData = $("#addService").data("init")
        $("#addService").data("trail", trailedData)
        // 判断是否必填项设置完（上面的所有星号项，以及必须一个项目）
        let requireKey = "name,code,chargeStage,chargeLimit,";
        let priceKey = "unitPrice,unitPriceNotax,unitPriceInvoice,unitPriceNoinvoice,unitPriceReference";
        if (initData.isPeriodical === 1) {
            requireKey += "periodDuration,periodUnit,"
        }
        let nullNum = 0
        let priceNum = 0
        $("#addService [name]").each(function () {
            let name = $(this).attr("name")
            let val = $(this).val()
            if ( requireKey.indexOf(name) > -1 && val === '') {
                nullNum++
            }
            if ( priceKey.indexOf(name) > -1 && val.length > 0) {
                priceNum++
            }
        })
        if(priceNum === 0){
            nullNum++
        }
        if (nullNum > 0 || mainData.project.projects.length === 0) {
            layer.msg("操作失败！\n上面所有必填项设置完后，才可进行此项操作！")
            return false
        }
        initDialog('setExperienceOrTrail')
        bounce_Fixed.show($("#setExperienceOrTrail"))
    }
}

// creator: 张旭博，2023-12-08 09:51:39， 体验环节或试用流程 改变状态记录
function changeStateLog() {
    bounce_Fixed.show($("#changeStateLog"))
    $.ajax({
        url: $.webRoot + '',
    }).then(res => {
        let data = res.data
        let str = ''
        for (let item in data) {
            let handleStr = '<td><span class="link-blue funBtn" data-fun="see">查看</span></td>'
            str += `<tr>
                        <td>${item.aa?'未设置':'已设置'}</td>
                        <td>${item.createName} ${moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                        <td>${item.aa?handleStr:'--'}</td>
                    </tr>`
        }
        $("#changeStateLog").html(str)
    })
}

function initDialog(name) {
    if (name === 'setExperienceOrTrail') {
        let trailData = $("#addService").data("trail")
        let initData = $("#addService").data("init")

        $("#setExperienceOrTrail section").hide()
        $("#setExperienceOrTrail input:radio").prop("checked", false)
        $("#setExperienceOrTrail .part_fee").hide()
        $("#setExperienceOrTrail [name]:not(:radio)").val("")
        $("#setExperienceOrTrail select").val("")
        if (trailData.type > 1) {
            // 如果是修改
            trailData = $("#addService").data("trailed")
            $("#setExperienceOrTrail [name='setExperienceOrTrail'][value='"+trailData.type+"']").click()
            $("#setExperienceOrTrail [name='isChargeable'][value='"+trailData.isChargeable+"']").click()
            $("#setExperienceOrTrail [name]:not(:radio)").each(function () {
                let name = $(this).attr("name")
                $(this).val(trailData[name])
            })
            $("#setExperienceOrTrail select").each(function () {
                let name = $(this).attr("name")
                $(this).val(trailData[name])
            })
        }
        if (initData.isPeriodical === 1) {
            $("#setExperienceOrTrail .experience .trialUpper").css('display', 'none')
            $("#setExperienceOrTrail .experience .trialDays").css('display', 'flex')
        } else {
            $("#setExperienceOrTrail .experience .trialUpper").css('display', 'flex')
            $("#setExperienceOrTrail .experience .trialDays").css('display', 'none')
        }
        $("#setExperienceOrTrail [name='setExperienceOrTrail']").unbind().on("change", function () {
            let val = $("#setExperienceOrTrail [name='setExperienceOrTrail']:checked").val()
            $("#setExperienceOrTrail .typeName").html(val === '2'?'体验':'试用')
            $("#setExperienceOrTrail section").show()
            // 获取项目和商品数据
            if (trailData.type === 0) {
                let mainData = $("#addService").data("main")
                trailData = JSON.parse(JSON.stringify(mainData))

                trailData.project.projects.map(item => {
                    item.main_itemQuantity = item.itemQuantity
                    item.main_periodQuantity= item.periodQuantity
                    item.itemQuantity = 0
                })
                trailData.good2.goods2.map(item => {
                    item.main_deliveryQuantity= item.deliveryQuantity
                    item.deliveryQuantity = 0
                })
                trailData.good1.goods1.map(item => {
                    item.main_deliveryQuantity = item.deliveryQuantity
                    item.deliveryQuantity = 0
                })
                $("#addService").data("trail", trailData)
                trailData.project.sumAll = getSumAll(trailData.project.projects, 'project')
                trailData.good2.sumAll = getSumAll(trailData.good2.goods2, 'good2')
                trailData.good1.sumAll = getSumAll(trailData.good1.goods1, 'good1')
            }
            // 渲染数据到试用弹窗页面
            setExperienceOrTrailTable()
        })
        $("#setExperienceOrTrail [name='isChargeable']").unbind().on("change", function () {
            let initData = $("#addService").data("init")

            let val = $("#setExperienceOrTrail [name='isChargeable']:checked").val()
            $("#setExperienceOrTrail .part_fee").css('display', val === '1'?'block':'none')
        })
    }
}

// creator: 张旭博，2024-02-19 05:24:59， 设置体验/试用 - 确定
function sureSetExperienceOrTrail() {
    let initData = $("#addService").data("init")
    let main = $("#addService").data("main")
    let trailData = $("#addService").data("trail")
    console.log('main', main)
    console.log('trail', trailData)
    let setExperienceOrTrail = $("#setExperienceOrTrail [name='setExperienceOrTrail']:checked").val()
    // 判断是否勾选了试用或者体验
    if (setExperienceOrTrail) {
        // 判断是否勾选了收费
        let isChargeable = $("#setExperienceOrTrail [name='isChargeable']:checked").val()
        let otherInfo = {
            type: setExperienceOrTrail,
            trialDays: $("#setExperienceOrTrail [name='trialDays']").val(),
            trialUpper: $("#setExperienceOrTrail [name='trialUpper']").val(),
            isChargeable: isChargeable
        }

        if (otherInfo.trialUpper === '' && otherInfo.trialDays === '') {
            layer.msg("请录入必填项！")
            return false
        }
        let days = otherInfo.trialDays || otherInfo.trialUpper
        let mainDays = getMainPeriodDurationToDays()
        if (days > mainDays) {
            layer.msg("操作失败！试用流程中设置的数量不合理！")
            return false
        }
        if (!isChargeable) {
            layer.msg("请选择是否收费！")
            return false
        } else {
            if (isChargeable === '1') {
                // 收费
                let priceInputNum = 0
                $("#setExperienceOrTrail .part_fee .priceForm [name]").each(function () {
                    let name = $(this).attr("name")
                    otherInfo[name] = $(this).val()
                    if ($(this).val() !== '') {
                        priceInputNum ++
                    }
                })
                if (priceInputNum === 0) {
                    layer.msg("至少录入一种单价！")
                    return false
                }
                let chargeStage = $("#setExperienceOrTrail .part_fee [name='chargeStage']").val()
                let chargeLimit = $("#setExperienceOrTrail .part_fee [name='chargeLimit']").val()
                if (chargeLimit === '' || chargeStage === '') {
                    layer.msg('请录入必填项！')
                    return false
                }
                otherInfo.chargeStage = chargeStage
                otherInfo.chargeLimit = chargeLimit
            }

        }
        trailData = {...trailData, ...otherInfo}
        // 判断是否必填项设置完（上面的所有星号项，以及必须一个项目）
        $("#addService").data("trail", trailData)
        $("#addService").data("trailed", JSON.parse(JSON.stringify(trailData)))
        bounce_Fixed.cancel()
        setAddServiceTrailPart()
    } else {
        layer.msg("操作失败！请勾选")
    }

}

function getMainPeriodDurationToDays() {
    let periodDuration = $("#addService [name='periodDuration']").val()
    let periodUnit = $("#addService [name='periodUnit']").val()
    let days = 0
    if (periodUnit && periodDuration) {
        if (periodUnit === '7') {
            days = periodDuration * 12 * 30
        } else if (periodUnit === '4') {
            days = periodDuration * 30
        }
    }
    return days
}

// creator: 张旭博，2024-02-28 10:12:00， 设置新增套餐试用部分表格
function setAddServiceTrailPart() {
    let trailData = $("#addService").data("trailed")
    let initData = $("#addService").data("init")
    let str = ''
    if (trailData) {
        let typeName = trailData.type === '2'?'体验':'试用'
        if (initData.isPeriodical === 1) {
            // 是周期
            str = `<table class="kj-table">
                   <tbody>
                   <tr>
                       <td style="width: 120px">状态</td>
                       <td>已设置${typeName}流程<span class="link-blue ty-right" onclick="changeState()">改变状态</span></td>
                   </tr>
                   <tr>
                       <td>可${typeName}的天数</td>
                       <td><span class="">${trailData.trialDays}</span></td>
                   </tr>
                   <tr>
                       <td>${typeName}价格</td>
                       <td><span class="">${trailData.unitPriceReference || 0}</span></td>
                   </tr>
                   <tr>
                       <td>收费时间</td>
                       <td><span class="">${formatChargeStage(trailData.chargeStage, trailData.chargeLimit)}</span></td>
                   </tr>
                   </tbody>
               </table>`
        } else {
            // 非周期
            str = `<table class="kj-table">
                       <tbody>
                       <tr>
                           <td style="width: 180px">状态</td>
                           <td>已设置${typeName}流程<span class="link-blue ty-right" onclick="changeState()">改变状态</span></td>
                       </tr>
                       <tr>
                           <td>某客户可${typeName}的数量上限</td>
                           <td><span class="">${trailData.trialUpper}</span></td>
                       </tr>
                       <tr>
                           <td>${typeName}价格</td>
                           <td><span class="">${trailData.unitPriceReference || 0}</span></td>
                       </tr>
                       <tr>
                           <td>收费时间</td>
                           <td><span class="">${formatChargeStage(trailData.chargeStage, trailData.chargeLimit)}</span></td>
                       </tr>
                       </tbody>
                   </table>`
        }
    } else {
        str =   `<table class="kj-table">
                     <tbody>
                     <tr>
                         <td style="width: 120px">状态</td>
                         <td>未设置<span class="link-blue ty-right" onclick="changeState(0)">改变状态</span></td>
                     </tr>
                     </tbody>
                 </table>`
    }
    $("#addService .trailContent").html(str)
}

// creator: 张旭博，2024-01-26 04:47:13， 设置试用页面表格
function setExperienceOrTrailTable () {
    let trailData = $("#addService").data("trail")
    let projectContentStr = ''
    for (let item of trailData.project.projects) {
        projectContentStr +=   `<tr class="projectTr">
                                      <td>${ item.code } / ${ item.name }</td>
                                      <td>${ Number(item.unitPriceReference || 0) }</td>
                                      <td>${ item.itemQuantity }</td>
                                      <td>${ item.amount }</td>
                                 </tr>`
    }
    $("#setExperienceOrTrail .menu1 tbody").html(projectContentStr)
    $("#setExperienceOrTrail .menu1 .sumAll").html(trailData.project.sumAll)
    $("#setExperienceOrTrail .menu1").css("display", trailData.project.projects.length>0?'block': 'none')
    let good2ContentStr = ''
    for (let item of trailData.good2.goods2) {
        good2ContentStr += `<tr class="goodsTr">
                                  <td>${ item.code } / ${ item.name }</td>
                                  <td>${ item.unitPriceReference || 0 }</td>
                                  <td>${ item.deliveryStr || '' }</td>
                                  <td>${ item.deliveryQuantity }</td>
                                  <td>${ item.amount }</td>
                             </tr>`
    }
    $("#setExperienceOrTrail .menu2 tbody").html(good2ContentStr)
    $("#setExperienceOrTrail .menu2 .sumAll").html(trailData.good2.sumAll)
    $("#setExperienceOrTrail .menu2").css("display", trailData.good2.goods2.length>0?'block': 'none')
    let good1ContentStr = ''
    for (let item of trailData.good1.goods1) {
        good1ContentStr += `<tr class="goodsTr">
                                  <td>${ item.code } / ${ item.name }</td>
                                  <td>${ item.unitPriceReference || 0 }</td>
                                  <td>${ item.deliveryStr || '' }</td>
                                  <td>${ item.deliveryQuantity }</td>
                                  <td>${ item.amount }</td>
                             </tr>`
    }
    $("#setExperienceOrTrail .menu3 tbody").html(good1ContentStr)
    $("#setExperienceOrTrail .menu3 .sumAll").html(trailData.good1.sumAll)
    $("#setExperienceOrTrail .menu3").css("display", trailData.good1.goods1.length>0?'block': 'none')
}

// creator: 张旭博，2024-01-26 10:13:07， 设置体验环节中的项目数量
function setTrailNum(type) {
    let trailData = $("#addService").data('trail')
    if (type === 1) {
        let projectStr = ''
        for (let item of trailData.project.projects) {
            projectStr +=   `<tr class="projectTr">
                                          <td>${ item.code } / ${ item.name }</td>
                                          <td>${ item.unit || '' }</td>
                                          <td>${ Number(item.unitPriceReference || 0) }</td>
                                          <td class="main_periodNum" num="${ item.main_periodQuantity }">${ item.main_periodQuantity } </td>
                                          <td><input type="text" class="trail_periodNum inputNum" value="0" onkeyup="clearNum(this)"></td>
                                          <td class="main_projectNum" num="${ item.main_itemQuantity }">${ item.main_itemQuantity }</td>
                                          <td><input type="text" class="trail_projectNum inputNum" value="0" onkeyup="clearNum(this)"></td>
                                          <td class="hd">${JSON.stringify(item)}</td>
                                     </tr>`
        }
        $("#setTrailItemQuantity tbody").html(projectStr)
        bounce_Fixed2.show($("#setTrailItemQuantity"))
        $("#setTrailItemQuantity .sureBtn").unbind().on("click", function () {
            let projectArr = []
            let state = 0
            let require = 0
            $("#setTrailItemQuantity tbody tr").each(function () {
                let item = JSON.parse($(this).find(".hd").html())
                let mainPeriodNum = $(this).find(".main_periodNum").attr("num")
                let periodNum = $(this).find(".trail_periodNum").val()
                let mainProjectNum = $(this).find(".main_projectNum").attr("num")
                let projectNum = $(this).find(".trail_projectNum").val()
                if (periodNum > mainPeriodNum) {
                    state++
                }
                if (projectNum > mainProjectNum) {
                    state++
                }
                if (periodNum === '') {
                    require++
                }
                if (projectNum === '') {
                    require++
                }
                item.periodQuantity = periodNum
                item.itemQuantity = projectNum
                projectArr.push(item)
            })
            if (require > 0) {
                layer.msg("您还有数据未录入！")
                return false
            }
            if (state > 0) {
                layer.msg("您所设置的数量不合理！")
                return false
            }
            trailData.project.projects = projectArr
            trailData.project.sumAll = getSumAll(trailData.project.projects, 'project')
            setExperienceOrTrailTable()
            bounce_Fixed2.cancel()
        })
    } else {
        bounce_Fixed2.show($("#setTrailDeliveryQuantity"))
        if (type === 2) {
            let goods2tr = ''
            for (let item of trailData.good2.goods2) {
                goods2tr +=   `<tr class="goodsTr">
                                              <td>${ item.code } / ${ item.name }</td>
                                              <td>${ item.unit }</td>
                                              <td>${ item.unitPriceReference || 0 }</td>
                                              <td>${ item.deliveryStr || '' }</td>
                                              <td class="main_deliveryNum" num="${ item.main_deliveryQuantity }">${ item.main_deliveryQuantity }</td>
                                              <td><input type="text" class="trail_deliveryQuantity inputNum" value="0" onkeyup="clearNum(this)"></td>
                                              <td class="hd">${JSON.stringify(item)}</td>
                                         </tr>`
            }
            $("#setTrailDeliveryQuantity tbody").html(goods2tr)
            bounce_Fixed2.show($("#setTrailDeliveryQuantity"))
            $("#setTrailDeliveryQuantity .sureBtn").unbind().on("click", function () {
                let goodArr = []
                let state = 0
                let require = 0
                $("#setTrailDeliveryQuantity tbody tr").each(function () {
                    let item = JSON.parse($(this).find(".hd").html())
                    let main_deliveryNum = $(this).find(".main_deliveryNum").attr("num")
                    let deliveryQuantity = $(this).find(".trail_deliveryQuantity").val()
                    if (deliveryQuantity === '') { require++ }
                    if (deliveryQuantity > main_deliveryNum) { state++ }
                    item.deliveryQuantity = deliveryQuantity
                    goodArr.push(item)
                })
                if (require > 0) {
                    layer.msg("您还有数据未录入！")
                    return false
                }
                if (state > 0) {
                    layer.msg("您所设置的数量不合理！")
                    return false
                }
                trailData.good2.goods2 = goodArr
                trailData.good2.sumAll = getSumAll(trailData.good2.goods2, 'good2')
                setExperienceOrTrailTable()
                bounce_Fixed2.cancel()
            })
        } else {
            let goods1tr = ''
            for (let item of trailData.good1.goods1) {
                goods1tr += `<tr class="goodsTr">
                                  <td>${ item.code } / ${ item.name }</td>
                                  <td>${ item.unit }</td>
                                  <td>${ item.unitPriceReference || 0 }</td>
                                  <td>${ item.deliveryStr || '' }</td>
                                  <td class="main_deliveryNum" num="${ item.main_deliveryQuantity }">${ item.main_deliveryQuantity }</td>
                                  <td><input type="text" class="trail_deliveryQuantity inputNum" value="0" onkeyup="clearNum(this)"></td>
                                  <td class="hd">${JSON.stringify(item)}</td>
                             </tr>`
            }
            $("#setTrailDeliveryQuantity tbody").html(goods1tr)
            bounce_Fixed2.show($("#setTrailDeliveryQuantity"))
            $("#setTrailDeliveryQuantity .sureBtn").unbind().on("click", function () {
                let goodArr = []
                let state = 0
                let require = 0
                $("#setTrailDeliveryQuantity tbody tr").each(function () {
                    let item = JSON.parse($(this).find(".hd").html())
                    let deliveryQuantity = $(this).find(".trail_deliveryQuantity").val()
                    let main_deliveryNum = $(this).find(".main_deliveryNum").attr("num")
                    if (deliveryQuantity === '') { require++ }
                    if (deliveryQuantity > main_deliveryNum) { state++ }
                    item.deliveryQuantity = deliveryQuantity
                    goodArr.push(item)
                })
                if (require > 0) {
                    layer.msg("您还有数据未录入！")
                    return false
                }
                if (state > 0) {
                    layer.msg("您所设置的数量不合理！")
                    return false
                }
                trailData.good1.goods1 = goodArr
                trailData.good1.sumAll = getSumAll(trailData.good1.goods1, 'good1')
                setExperienceOrTrailTable()
                bounce_Fixed2.cancel()
            })
        }
    }
}

function delProSure() {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    let tableO = editObj.parents("table")
    editObj.parents("tr").remove();
    resetSumAll(tableO)
    bounce_Fixed.cancel()
}
function resetSumAll(tableO){
    let trList = tableO.find("tbody tr")
    if(trList.length == 0){
        tableO.parents(".projectOrGoods").hide()
    }else{
        let sumAll = 0
        trList.each(function () {
            let i_info = $(this).find(".hd").html()
            i_info = JSON.parse(i_info)
            sumAll += Number(i_info.sum);
        })
        tableO.find("tfoot .sumAll").html(sumAll.toFixed(2))
    }
}
// creator: hxz，2022-08-22 08:57:55， 向套餐内添加项目 - 操作说明
function operatInstructions() {
    bounce_Fixed2.show($("#operatInstructions"))
}
// creator: hxz，2022-08-22 08:57:55， 获取 系统内在用的常规服务套餐
function getList(cur,key = ''){
    $.ajax({
        url: $.webRoot + '/service/getServicePackageList.do',
        data: { currentPageNo: cur, pageSize: 20, keyword: key },
        success:function(data){
            let res = data.data
            let list = res["list"] || []
            let pageInfo = res["pageInfo"] || {}
            let str = ``
            list.forEach((item)=>{
                str += `
                <tr>
                    <td>${ item.code }/${ item.name }</td>
                    <td>${ item.unitPriceReference || '' }</td>
                    <td>${ formatDur( item.isPeriodical,  item.periodDuration,  item.periodUnit) }</td>
                    <td>${item.createName} ${ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss") }</td>
                    <td>
                        <span data-fun="service_scan" class="link-blue funBtn">查看</span>
                        <span data-fun="service_stop" class="link-blue funBtn">停用</span>
                        <span data-fun="service_del" class="link-red funBtn">删除</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                `
            })
            $("#main1Tab tbody").html(str)
            $("#mainsNum").html(list.length)
            setPage($("#ye_service"),pageInfo.currentPageNo, pageInfo.totalPage,"serviceSetMealList", JSON.stringify({ "key":key,  "status": 1 }))
        }
    })
}

function formatDur(isPeriodical, periodDuration, periodUnit) {
    let str = ``;
    if(isPeriodical == 1){
        str = `每${ periodDuration }${ formatPeriodUnit(periodUnit) }收费一次`;
    }else {
        str = `一次收取完毕`;
    }
    return str;
}
function formatPeriodUnit(periodUnit){
    let str = ``
    switch (Number(periodUnit)){
        case 1:
            str = `日`
            break;
        case 2:
            str = `周`
            break;
        case 3:
            str = `旬`
            break;
        case 4:
            str = `月`
            break;
        case 5:
            str = `季`
            break;
        case 6:
            str = `半年`
            break;
        case 7:
            str = `年`
            break;
        default:
    }
    return str;
}
// creator: hxz，2022-08-22 08:57:55， 查找 在用的常规服务套餐
function searchList() {
    let key = $("#search1").val()
    getList(1,key)

}
// creator: hxz，2022-08-22 08:57:55， 查找 停用的常规服务套餐
function searchListStop() {
    let key = $("#search2").val()
    getStopList(1,key)
}
// creator: hxz，2022-08-22 08:57:55，  查看服务套餐
var editObjTr = null
function service_scan(obj) {
    editObjTr = obj
    bounce.show($("#serviceScan"));
    let trInfo = obj.siblings(".hd").html()
    trInfo = JSON.parse(trInfo);
    let serviceId = trInfo.id
    $.ajax({
        url: '../service/getServiceDetail.do',
        data: { serviceId: serviceId },
        success:function (res) {
            bounce.show($("#serviceScan"));
            let data = res.data
            $("#serviceScan").data("data", data);
            $("#cre1").html(`创建：${ data.createName } ${ new Date(data.createDate).format("yyyy-MM-dd hh:mm:ss") }`);
            $("#re1").html(data.isPeriodical == 1 ? '本套餐按周期收费。' : '本套餐不按周期收费。')

            // 设置基本信息、价格信息、收费模式及说明、服务套餐的两项开票资料 信息
            let getData = JSON.parse(JSON.stringify(data))
            let productList = data.productList || []
            let syProductList = data.syProductList || []
            let good1s = productList.filter(item => item.deliveryTerm === 1)
            let good2s = productList.filter(item => item.deliveryTerm === 2)
            let syGood1s = syProductList.filter(item => item.deliveryTerm === 1)
            let syGood2s= syProductList.filter(item => item.deliveryTerm === 2)
            let computedData = {
                VATInvoice: chargeVATInvoice(data),
                overview: data.isPeriodical == 1 ? '本套餐按周期收费。' : '本套餐不按周期收费。',
                feeCycle: formatDur( data.isPeriodical,  data.periodDuration,  data.periodUnit),
                feeTime: formatChargeStage(data.chargeStage, data.chargeLimit),
                projectNum: data.projectList?data.projectList.length : 0,
                projectNum_trial: data.syProjectList?data.syProjectList.length : 0,
                firstGoodNum: good2s.length,
                firstGoodNum_trial: syGood2s.length,
                everyGoodNum: good1s.length,
                everyGoodNum_trial: syGood1s.length,
                nameInvoice: chargeNameInvoice(data),
                ggInvoice: chargeGgInvoice(data),
                unitPriceInvoice: data.unitPriceInvoice?(data.unitPriceInvoice + '元'):'--',
                unitPriceNoinvoice: data.unitPriceNoinvoice?(data.unitPriceNoinvoice + '元'):'--',
                unitPriceReference: data.unitPriceReference?(data.unitPriceReference + '元'):'--'
            }
            let showData = {...getData, ...computedData}
            for (let key in showData) {
                $("#serviceScan .service_" + key).html(showData[key])
            }

            // 设置试用/体验部分
            let state = data.state
            let syPackage = data.syPackage // 试用/体验信息
            let syStr = ''
            if (state > 0) {
                // 设置了体验/试用
                let typeName = syPackage.type === 2?'体验':'试用'
                if (data.isPeriodical === 1) {
                    // 是周期
                    syStr = `<table class="kj-table">
                               <tbody>
                               <tr>
                                   <td style="width: 220px">状态</td>
                                   <td>已设置${typeName}流程</td> 
<!--                                   <span class="link-blue ty-right" onclick="changeState()">改变状态</span>-->
                               </tr>
                               <tr>
                                   <td>可${typeName}的天数</td>
                                   <td><span class="">${syPackage.trialDays?(syPackage.trialDays + '天'):''}</span></td>
                               </tr>
                               <tr>
                                   <td>${typeName}价格</td>
                                   <td><span class="">${syPackage.unitPriceReference?(syPackage.unitPriceReference + '元'): ''}</span></td>
                               </tr>
                               <tr>
                                   <td>收费时间</td>
                                   <td><span class="">${formatChargeStage(syPackage.chargeStage, syPackage.chargeLimit)}</span></td>
                               </tr>
                               </tbody>
                           </table>`
                } else {
                    // 非周期
                    syStr = `<table class="kj-table">
                               <tbody>
                               <tr>
                                   <td style="width: 220px">状态</td>
                                   <td>已设置${typeName}流程</td>
                               </tr>
                               <tr>
                                   <td>某客户可${typeName}的数量上限</td>
                                   <td><span class="">${syPackage.trialUpper}</span></td>
                               </tr>
                               <tr>
                                   <td>${typeName}是否需交付/验收？</td>
                                   <td><span class=""></span></td>
                               </tr>
                               <tr>
                                   <td>${typeName}价格</td>
                                   <td><span class="">${syPackage.unitPriceReference?(syPackage.unitPriceReference + '元'):''}</span></td>
                               </tr>
                               <tr>
                                   <td>收费时间</td>
                                   <td><span class="">${formatChargeStage(syPackage.chargeStage, syPackage.chargeLimit)}</span></td>
                               </tr>
                               </tbody>
                           </table>`
                }
            } else {
                syStr =   `<table class="kj-table">
                             <tbody>
                             <tr>
                                 <td style="width: 220px">状态</td>
                                 <td>未设置</td>
                             </tr>
                             </tbody>
                         </table>`
            }
            $("#serviceScan .part_sy tbody").html(syStr)
        }
    })

}

// creator: 张旭博，2024-04-09 08:48:42， 查看套餐 - 查看商品或者项目（包括试用体验）
function seeGoodOrProject(obj) {
    let type = obj.attr("type")
    let goodType = obj.attr("good") // undefined: 项目 2:
    // 判断是项目还是商品
    if (goodType) {
        bounce_Fixed2.show($("#seeGood"))
    } else {
        bounce_Fixed2.show($("#seeProject"))
    }
    let data = $("#serviceScan").data("data")
    let productList = data.productList || []
    let projectList = data.projectList || []

    let syProductList = data.syProductList || []
    let syProjectList = data.syProjectList || []
    let projects = [], goods2 = [], goods1= []
    if (type === 'main') {
        projects = projectList
        goods2 = productList.filter(item => {
            return item.deliveryTerm === 1
        })
        goods1 = productList.filter(item => {
            return item.deliveryTerm === 2
        })
    } else {
        projects = syProjectList
        goods2 = syProductList.filter(item => {
            return item.deliveryTerm === 1
        })
        goods1 = syProductList.filter(item => {
            return item.deliveryTerm === 2
        })
    }

    let mainData = {
        project: {
            projects: projects,
            sumAll: 0
        },
        good2: {
            goods2: goods2,
            sumAll: 0
        },
        good1: {
            goods1: goods1,
            sumAll: 0
        }
    }
    let sumAll1 = 0, sumAll2 = 0, sumAll3 = 0
    for (let item of projects) {
        // 计算总值和合计
        let unitPrice = Number(item.unitPriceReference || 0)
        let amount = item.itemQuantity * item.periodQuantity * unitPrice
        item.amount = amount && amount.toFixed(2);
        sumAll1 += Number(item.amount);
    }
    mainData.project.sumAll = sumAll1
    for (let item of goods2) {
        // 计算总值和合计
        let unitPrice = Number(item.unitPriceReference || 0)
        let amount = item.deliveryQuantity * unitPrice
        item.amount = amount && amount.toFixed(2);
        sumAll2 += Number(item.amount);
    }
    mainData.good2.sumAll = sumAll2
    for (let item of goods1) {
        // 计算总值和合计
        let unitPrice = Number(item.unitPriceReference || 0)
        let amount = item.deliveryQuantity * unitPrice
        item.amount = amount && amount.toFixed(2);
        sumAll3 += Number(item.amount);
    }
    mainData.good1.sumAll = sumAll3

    if (goodType) {
        if (goodType === '2') {
            let good2ContentStr = ''
            for (let item of mainData.good2.goods2) {
                good2ContentStr += `<tr class="goodsTr">
                                          <td>${ item.code } / ${ item.name }</td>
                                          <td>${ item.unitPriceReference || 0 }</td>
                                          <td><span data-fun="scanGoods" class="link-blue funBtn">查看</span></td>
                                          <td>${ item.deliveryQuantity }</td>z
                                          <td>${ item.amount }</td>
                                          <td class="hd">${ JSON.stringify(item) }</td> 
                                          
                                     </tr>`
            }
            $("#seeGood tbody").html(good2ContentStr)
            $("#seeGood .sumAll").html(mainData.good2.sumAll)
        } else {
            let good1ContentStr = ''
            for (let item of mainData.good1.goods1) {
                good1ContentStr += `<tr class="goodsTr">
                                   <td>${ item.code } / ${ item.name }</td>
                                  <td>${ item.unitPriceReference || 0 }</td>
                                  <td><span data-fun="scanGoods" class="link-blue funBtn">查看</span></td>
                                  <td>${ item.deliveryQuantity }</td>
                                  <td>${ item.amount }</td>
                                  <td class="hd">${ JSON.stringify(item) }</td> 
                                  
                             </tr>`
            }
            $("#seeGood tbody").html(good1ContentStr)
            $("#seeGood .sumAll").html(mainData.good1.sumAll)
        }
        bounce_Fixed2.show($("#seeGood"))
    } else {
        bounce_Fixed2.show($("#seeProject"))
        let projectContentStr = ''
        for (let item of mainData.project.projects) {
            projectContentStr +=   `<tr class="projectTr">
                                      <td>${ item.code } / ${ item.name }</td>
                                      <td>${ Number(item.unitPriceReference || 0) }</td>
                                      <td>${ item.itemQuantity }</td>
                                      <td>${ item.amount }</td>
                                 </tr>`
        }
        $("#seeProject tbody").html(projectContentStr)
        $("#seeProject .sumAll").html(mainData.project.sumAll)
    }

}

// creator: hxz，2022-08-22 08:57:55，  修 改 套餐
function formatChargeStage(chargeStage, deliveryLimit) {
    // 发货时间要求类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后
    // ,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,
    // 11-本期通过验收后,12-最终通过验收后 13服务结束后
    let str = ``
    if(chargeStage){
        switch (Number(chargeStage)){
            case 1:  str = `合同签订后`; break;
            case 2:  str = `服务开始前`; break;
            case 3:  str = `服务开始后`; break;
            case 4:  str = `交付前`; break;
            case 5:  str = `交付后`; break;
            case 6:  str = `本期交付前`; break;
            case 7:  str = `本期交付后`; break;
            case 8:  str = `最终交付前`; break;
            case 9:  str = `最终交付后`; break;
            case 10:  str = `通过验收后`; break;
            case 11:  str = `本期通过验收后`; break;
            case 12:  str = `最终通过验收后`; break;
            case 13:  str = `服务结束后`; break;
            default:
        }
        str += `${ deliveryLimit }天`
    }
    return str
}
// creator: 张旭博，2024-07-30 08:32:11， 修改套餐
function editBtn() {
    let data = $("#serviceScan").data("data");
    $("#addService").data("isLimit", data.isRestrictive === 1)
    let mainData = {
        code: data.code,
        name: data.name,
        memo: data.memo,
    }
    for(let key in mainData) {
        $("#editService [name='"+key+"']").val(mainData[key])
    }

    let initData = {
        isPeriodical: data.isPeriodical, // 是否为周期
        isMixture: data.isMixture // 是否有商品
    }
    $("#addService").data("init", initData)

    // 初始化部分模块显隐
    $("#editService .errorTip").hide() // 隐藏所有提示
    $("#editService .projectOrGoods").hide(); // 隐藏项目和商品表格
    $("#editService .service_content table tbody").html("") // 清空表格内容
    $("#editService").find("[name='unitPrice'], [name='unitPriceNotax']").prop("disabled", !data.taxRate) // 默认禁用此两个字段
    $("#editService .addGoodsBtn").css('display', initData.isMixture === 1?'inline': 'none'); // 根据设置显隐增加商品按钮

    // 渲染项目和商品内容
    let projectList = data.projectList || []
    let productList = data.productList || []
    let good1s = productList.filter(item => item.deliveryTerm === 1)
    let good2s = productList.filter(item => item.deliveryTerm === 2)
    let computedData = {
        taxRate: data.taxRate,
        unitPrice: data.unitPrice,
        unitPriceNotax: data.unitPriceNotax,
        unitPriceInvoice: data.unitPriceInvoice,
        unitPriceNoinvoice: data.unitPriceNoinvoice,
        unitPriceReference: data.unitPriceReference,
        priceDesc: data.priceDesc,
        periodDuration: data.periodDuration,
        periodUnit: data.periodUnit,
        chargeStage: data.chargeStage,
        chargeLimit: data.chargeLimit
    }
    for (let key in computedData) {
        $("#editService [name='"+key+"']").val(computedData[key])
    }
    let projectsOrigin = $("body").data('projects')
    let projects = projectList.map(item => {
        let newItem = projectsOrigin.find(it => it.id === item.serviceItem)
        return {...item, ...newItem}
    })
    good2s.forEach(item => {
        item.init = {
            deliveryTerm: item.deliveryTerm,
            deliveryPlace: item.deliveryPlace,
            deliverySequence: item.deliverySequence,
            deliveryDuration: item.deliveryDuration
        }
    })
    good1s.forEach(item => {
        item.init = {
            deliveryTerm: item.deliveryTerm,
            deliveryPlace: item.deliveryPlace,
            deliverySequence: item.deliverySequence,
            deliveryDuration: item.deliveryDuration
        }
    })
    let main = {
        project: {
            projects: projects,
            sumAll: getSumAll(projects, 'project')
        },
        good2: {
            goods2: good2s,
            sumAll: getSumAll(good2s, 'good2')
        },
        good1: {
            goods1: good1s,
            sumAll: getSumAll(good1s, 'good1')
        }
    }
    $("#addService").data("main", main)
    if (main.project.projects.length > 0) {
        $("#editService .projectContent").show();
        $("#editService .projectContent tbody").html(getStrByName(main.project.projects, 'project'));
        $("#editService .projectContent .sumAll").html(main.project.sumAll)
    } else {
        $("#editService .projectContent").hide().find("tbody").html('')
    }
    if (main.good2.goods2.length > 0) {
        $("#editService .good2Content").show();
        $("#editService .good2Content tbody").html(getStrByName(main.good2.goods2, 'good2'));
        $("#editService .good2Content .sumAll").html(main.good2.sumAll)
    } else {
        $("#editService .good2Content").hide().find("tbody").html('')
    }
    if (main.good1.goods1.length > 0) {
        $("#editService .good1Content").show();
        $("#editService .good1Content tbody").html(getStrByName(main.good1.goods1, 'good1'));
        $("#editService .good1Content .sumAll").html(main.good1.sumAll)
    } else {
        $("#editService .good1Content").hide().find("tbody").html('')
    }
    $("#editService").data("main", main)


    if(initData.isMixture === 1){
        $("#editService .addGoodsBtn").show();
    }else{
        $("#editService .addGoodsBtn").hide();
    }
    if(initData.isPeriodical == 1){
        $("#editService .periodDurationInfo").show();
    }else {
        $("#editService .periodDurationInfo").hide();
    }


    bounce.show($("#editService"));
    // 收费周期部分看是否需要计算最大周期
    let selectType = judgeProjects() // 根据已添加的项目 判断当前选择类型（全周期、全非周期、混合）

    if (selectType === 'period') {
        $(".bonceContainer:visible .periodDuration").append(`<option value="${data.periodDuration}">${data.periodDuration}</option>`).val(data.periodDuration)
        $(".bonceContainer:visible .periodDuration").prop("disabled", true)
        $(".bonceContainer:visible .periodUnit").prop("disabled", true).val('4')
        return false
    }
    $("#editService [name='periodUnit']").val(data.periodUnit)
    let strOption = '<option value="">--请选择--</option>'
    if (data.periodUnit === 7) {
        strOption += `<option value="1">1</option>`
        $("#editService [name='periodDuration']").prop("disabled", false).html(strOption).val('1')
    } else if (data.periodUnit === 4) {
        for (let i = 1; i< 12; i++) {
            strOption += `<option value="${i}">${i}</option>`
        }
        $("#editService [name='periodDuration']").prop("disabled", false).html(strOption).val(data.periodDuration)
    } else {
        $("#editService [name='periodDuration']").prop("disabled", true).val('')
    }


    // let projectList = data.projectList || []
    // let str_projectList = ``// 项目str
    // let eTab3Sum = 0
    // let eTab2Sum = 0
    // let eTab1Sum = 0
    // projectList.forEach((item, index)=>{
    //     item.init = init
    //     item.itemQuantity = item.itemQuantity
    //     item.periodQuantity = item.periodQuantity
    //     item.id = item.serviceItem
    //     item.orders = index + 1
    //     let price = item.unitPriceReference || 0
    //     let num = item.itemQuantity || 0
    //     item.sum = item.amount
    //     eTab1Sum += Number(item.amount )
    //     str_projectList += `
    //                 <tr class="projectTr">
    //                     <td>${ item.code }/${ item.name }</td>
    //                     <td>${ item.unitPriceReference || ""  }</td>
    //                     <td>${ item.itemQuantity } * ${ item.periodQuantity }</td>
    //                     <td>${ item.amount }</td>
    //                     <td>
    //                         <span data-fun="editNumPro" class="link-blue funBtn">修改数量</span>
    //                         <span data-fun="delPro" class="link-red funBtn">删除</span>
    //                         <span class="hd">${ JSON.stringify(item) }</span>
    //                     </td>
    //                 </tr>
    //             `
    // })
    // $("#e_tab1 tbody tr:gt(0):not('.guDing')").remove();
    // $("#e_tab1 tbody tr.guDing").before(str_projectList);
    //
    // let productList = data.productList || [];
    // let str_productList2 = ``// 商品str 2
    // let str_productList3 = ``// 商品str 3
    // productList.forEach((item2)=>{
    //     item2.init = init
    //     item2.id = item2.serviceItem
    //     item2.init.deliveryTerm = item2.deliveryTerm;
    //     item2.init.deliveryPlace = item2.deliveryPlace;
    //     item2.init.deliverySequence = item2.deliverySequence;
    //     item2.sendDianStr = formatChargeStage(item2.deliveryStage, item2.deliveryLimit)
    //     let deliveryTerm = item2.deliveryTerm;
    //     item2.code = item2.code
    //     item2.name = item2.name
    //     item2.sendDian = item2.deliveryStage
    //     item2.sebdDDay = item2.deliveryLimit
    //     item2.name = item2.codeName
    //     item2.deliveryQuantity = item2.deliveryQuantity
    //     item2.deliveryDuration = item2.deliveryDuration
    //     item2.sum = item2.amount
    //     if(deliveryTerm == 1){ // 每个收费周期都需提供
    //         eTab3Sum += Number(item2.amount )
    //         str_productList3 += `
    //                 <tr class="goodsTr">
    //                     <td>${ item2.code }/${ item2.name }</td>
    //                     <td>${ item2.unitPriceReference || ""  }</td>
    //                     <td>${ formatChargeStage(item2.deliveryStage, item2.deliveryLimit) }</td>
    //                     <td>${ item2.deliveryQuantity }</td>
    //                     <td>${ item2.amount }</td>
    //                     <td>
    //                      <span data-fun="scanGoods" class="link-blue funBtn">查看</span>
    //                      <span data-fun="editNumGoods" class="link-blue funBtn">修改</span>
    //                      <span data-fun="delGoods" class="link-red funBtn">删除</span>
    //                      <span class="hd">${ JSON.stringify(item2) }</span>
    //                   </td>
    //                 </tr>
    //                 `
    //     }else if(deliveryTerm == 2){ // 仅第一个收费周期提供
    //         eTab2Sum += Number(item2.amount )
    //         str_productList2 += `
    //                 <tr class="goodsTr">
    //                     <td>${ item2.code }/${ item2.name }</td>
    //                     <td>${ item2.unitPriceReference || ""  }</td>
    //                     <td>${ formatChargeStage(item2.deliveryStage, item2.deliveryLimit) }</td>
    //                     <td>${ item2.deliveryQuantity }</td>
    //                     <td>${ item2.amount }</td>
    //                     <td>
    //                         <span data-fun="scanGoods" class="link-blue funBtn">查看</span>
    //                         <span data-fun="editNumGoods" class="link-blue funBtn">修改</span>
    //                         <span data-fun="delGoods" class="link-red funBtn">删除</span>
    //                         <span class="hd">${ JSON.stringify(item2) }</span>
    //                   </td>
    //                 </tr>
    //                 `
    //     }
    // })
    // $("#e_tab2 tbody tr:gt(0):not('.guDing')").remove();
    // $("#e_tab2 tbody tr.guDing").before(str_productList2);
    // $("#e_tab3 tbody tr:gt(0):not('.guDing')").remove();
    // $("#e_tab3 tbody tr.guDing").before(str_productList3);
    //
    // $("#eTab1Sum").html(eTab1Sum)
    // $("#eTab2Sum").html(eTab2Sum)
    // $("#eTab3Sum").html(eTab3Sum)
    //
    // if(str_projectList.length > 0){
    //     $("#editService .s_tbc_1").show()
    // }else {
    //     $("#editService .s_tbc_1").hide()
    // }
    // if(str_productList2.length > 0){
    //     $("#editService .s_tbc_2").show()
    // }else {
    //     $("#editService .s_tbc_2").hide()
    // }
    // if(str_productList3.length > 0){
    //     $("#editService .s_tbc_3").show()
    // }else {
    //     $("#editService .s_tbc_3").hide()
    // }

}
// creator: hxz，2022-08-22 08:57:55，  修 改 套餐 - 确定
function editServiceOk() {
    let oldData = $("#serviceScan").data("data");

    let init = $("#addService").data("init");
    let mains = $("#editService").data("main");
    // 判断是否必填项设置完（上面的所有星号项，以及必须一个项目）
    let requireKey = "name,code,chargeStage,chargeLimit,";
    let priceKey = "unitPrice,unitPriceNotax,unitPriceInvoice,unitPriceNoinvoice,unitPriceReference";
    if (init.isPeriodical === '1') {
        requireKey += "periodDuration,periodUnit,"
    }
    let nullNum = 0
    let priceNum = 0
    $("#editService [name]").each(function () {
        let name = $(this).attr("name")
        let val = $(this).val()
        if ( requireKey.indexOf(name) > -1 && val === '') {
            nullNum++
        }
        if ( priceKey.indexOf(name) > -1 && val.length > 0) {
            priceNum++
        }
    })
    if(priceNum === 0){
        nullNum++
    }
    if (nullNum > 0 || mains.project.projects.length === 0) {
        layer.msg("操作失败！\n上面所有必填项设置完后，才可进行此项操作！")
        return false
    }

    let goodsMain = [...mains.good2.goods2, ...mains.good1.goods1]
    let mainData = {
        id: oldData.id,
        type: 1, // 	是 	int 	类型:1-正式,2-体验,3-试用 	1.262新增参数
        state: 0,
        isPeriodical: init.isPeriodical, // 	是 	Integer 	是否周期性 1是 0否
        isMixture: init.isMixture,   // 	是 	Integer 	是否混合:项目+商品 1是 0否
        chargeNum: '',
        enteringType: '', // 	否 	Integer 	录入类型:1-金额,2-比例
        upperType: '',   // 	否 	Integer 	上限类型:1-专票不含税价,2-专票含税价,3-普票价,4-不开票价,5-参考单价
        productListJson: JSON.stringify(chargeParam(goodsMain)),   // 	否 	String 	商品 字符串集合
        projectListJson: JSON.stringify(chargeParam(mains.project.projects))   // 	否 	String 	项目 字符串集合
    }
    $("#editService [name]").each(function () {
        let name = $(this).attr("name")
        let value = $(this).val()
        mainData[name] = value
    })

    console.log('lastMainData', mainData)
    $.ajax({
        url: '../service/updateServicePackage.do',
        data: mainData,
        success:function(res){
            let success = res.success
            if(success == 1){
                bounce.show($('#serviceScan'));
                service_scan(editObjTr)
            }else{
                layer.msg('操作失败！')
            }
        }
    })

}
// creator: hxz，2022-08-22 08:57:55，  修改记录
function editLog(obj) {
    bounce_Fixed.show($("#editLog"))
    let trInfo = editObjTr.siblings(".hd").html()
    trInfo = JSON.parse(trInfo);
    let serviceId = trInfo.id
    $("#logTab tbody").html('');
    $.ajax({
        url: $.webRoot + '/service/getRecordBaseList.do',
        data: { serviceId: serviceId },
    }).then(res => {
        let list = res.data.list || [];
        if(list.length > 0){
            let str = ``;
            let lastItem = {}
            list.forEach((logItem, index)=>{
                logItem.index = index
                lastItem = logItem
                str += `<tr>
                        <td>${ index == 0 ? '原始信息' : `第${ index }次修改后` }</td>
                        <td class="ty-td-control"><span data-fun="logDetails" class="funBtn ty-color-blue">查看</span><span class="hd">${ JSON.stringify(logItem) }</span></td>
                        <td>${ logItem.updateName || logItem.createName } ${ new Date(logItem.updateDate || logItem.createDate).format("yyyy-MM-dd hh:mm:ss")  } </td>
                    </tr>`;
            })
            $("#logTab").show();
            $("#logTab tbody").html(str);
            $("#logResult").html(`当前资料为第${ lastItem.index }次修改后的结果，修改人：${ lastItem.updateName || lastItem.createName } ${ new Date(lastItem.updateDate || lastItem.createDate ).format("yyyy-MM-dd hh:mm:ss")  } `)

        }else{
            $("#logTab").hide();
            $("#logResult").html(`当前资料为原始信息，创建人：${ res.data.createName } ${ new Date(res.data.createDate).format("yyyy-MM-dd hh:mm:ss")  } `)
        }
    })

}

// creator: 张旭博，2024-03-13 04:10:41， 服务套餐的两项开票资料 - 编辑
function editInvoiceInfo(obj) {
    let serviceData = $("#serviceScan").data("data")
    let type = obj.attr("type")
    bounce_Fixed.show($("#editInvoiceInfo" + type))
    $("#editInvoiceInfo" + type + " input:radio").prop("checked", false).unbind().on('change', function () {
        $("#editInvoiceInfo" + type + " [name='userDefined']").prop("disabled", $("#editInvoiceInfo" + type + " input:radio:checked").attr("class") !== 'custom')
    })
    $("#editInvoiceInfo" + type + " [name='userDefined']").val("").prop("disabled", true)
    if (type === '1') {
        $("#editInvoiceInfo1 .name").html(serviceData.name)
        $("#editInvoiceInfo1 .code").html(serviceData.code)
        if (serviceData.nameInvoice) {
            $("#editInvoiceInfo1 .tipDone .content").html(chargeNameInvoice(serviceData))
            $("#editInvoiceInfo1 .tipDone").show()
            $("#editInvoiceInfo1 .tipEdit").hide()
        } else {
            $("#editInvoiceInfo1 .tipDone").hide()
            $("#editInvoiceInfo1 .tipEdit").show()
        }
    } else {
        if (serviceData.ggInvoice) {
            $("#editInvoiceInfo2 .tipDone .content").html(chargeGgInvoice(serviceData))
            $("#editInvoiceInfo2 .tipDone").show()
            $("#editInvoiceInfo2 .tipEdit").hide()
        } else {
            $("#editInvoiceInfo2 .tipDone").hide()
            $("#editInvoiceInfo2 .tipEdit").show()
        }
    }

}

function chargeNameInvoice(data) {
    let invoice = data.nameInvoice
    let str = ''
    if (invoice) {
        let pattern = invoice.pattern
        switch (pattern) {
            case 1:
                str = data.code
                break;
            case 2:
                str = data.name
                break;
            case 3:
                str = data.code + data.name
                break;
            case 4:
                str = data.name + data.code
                break;
            case 5:
                str = invoice.userDefined
                break;
            default:
                str = '--'
        }
    } else {
        str =  '--'
    }
    return str
}

function chargeGgInvoice(data) {
    let invoice = data.ggInvoice
    let str = ''
    if (invoice) {
        let pattern = invoice.pattern
        switch (pattern) {
            case 1:
                str = '--'
                break;
            case 5:
                str = invoice.userDefined
                break;
            default:
                str = '--'
        }
    } else {
        str =  '--'
    }
    return str
}

// creator: hxz，2022-09-01 08:57:55， 修改记录 - 查看详情
function logDetails(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info)
    let index = info.index
    $.ajax({
        "url":"../service/getRecordBaseDetail.do",
        "data":{ "id": info.id },
        success:function (res) {
            let dataO = res.data
            let front = dataO.front
            let now = dataO.now
            if(now){
                let data = now
                $("#logDetails_durInfo").html(now.isPeriodical == 1 ? `本套餐服务费按周期收取。`: `本套餐不按周期收费。`)
                $("#logDetails_create").html(`${ index === 0 ? '创建': '修改' }：${ now.updateName || now.createName } ${ new Date(now.updateDate || now.createDate).format("yyyy-MM-dd hh:mm:ss")  }`)
                $("#log_name").html(now.name)
                $("#log_code").html(now.code)
                $("#log_desc").html(now.memo)
                $("#logDetails_tax").html(now.taxRate)
                $("#logDetails_priceNoTax").html(now.unitPriceNotax || '')
                $("#logDetails_priceContain").html(now.unitPrice || '')
                $("#logDetails_pricePu").html(now.unitPriceInvoice || '')
                $("#logDetails_priceNoPiao").html(now.unitPriceNoinvoice || '')
                $("#logDetails_peiceCanKao").html(now.unitPriceReference || '')
                $("#logDetails_desc").html(now.priceDesc || '')
                $("#log_feeTime").html(`${ formatChargeStage(data.chargeStage, data.chargeLimit) }`)

                if(data.isPeriodical == 1){
                    $("#log_feeZhou").html(`${ formatDur( data.isPeriodical,  data.periodDuration,  data.periodUnit) }`)
                    $("#log_feeZhouC").show()
                }else{
                    $("#log_feeZhouC").hide()
                }

                let projectList = data.projectList || []
                let str_projectList = ``// 项目str
                let sum1 = 0
                projectList.forEach((item)=>{
                    sum1 += Number( item.amount )
                    str_projectList += `
                    <tr>
                        <td>${ item.code }/${ item.name }</td>
                        <td>${ item.unitPriceReference || ""  }</td>
                        <td>${ item.itemQuantity } * ${ item.periodQuantity }</td>
                        <td>${ item.amount }</td>
                    </tr>
                `
                })
                $("#log_tab1 tbody tr:gt(0):not('.guDing')").remove();
                $("#log_tab1 tbody tr.guDing").before(str_projectList);
                $("#logTab1Sum").html(sum1)
                let productList = data.productList || [];
                let str_productList2 = ``// 商品str 2
                let str_productList3 = ``// 商品str 3
                let sum2 = 0
                let sum3 = 0
                productList.forEach((item2)=>{
                    let deliveryTerm = item2.deliveryTerm;
                    if(deliveryTerm == 1){ // 每个收费周期都需提供
                        sum3 += Number( item2.amount )
                        str_productList3 += `
                    <tr>
                        <td>${ item2.code }/${ item2.name }</td>
                        <td>${ item2.unitPriceReference || ""  }</td>
                        <td>${ formatChargeStage(item2.deliveryStage, item2.deliveryLimit) }</td>
                        <td>${ item2.deliveryQuantity }</td>
                        <td>${ item2.amount }</td>
                    </tr>
                    `
                    }else if(deliveryTerm == 2){ // 仅第一个收费周期提供
                        sum2 += Number( item2.amount )
                        str_productList2 += `
                    <tr>
                        <td>${ item2.code }/${ item2.name }</td>
                        <td>${ item2.unitPriceReference || ""  }</td>
                        <td>${ formatChargeStage(item2.deliveryStage,  item2.deliveryLimit) }</td>
                        <td>${ item2.deliveryQuantity }</td>
                        <td>${ item2.amount }</td>
                    </tr>
                    `
                    }
                })
                $("#log_tab2 tbody tr:gt(0):not('.guDing')").remove();
                $("#log_tab2 tbody tr.guDing").before(str_productList2);
                $("#logTab2Sum").html(sum2)

                $("#log_tab3 tbody tr:gt(0):not('.guDing')").remove();
                $("#log_tab3 tbody tr.guDing").before(str_productList3);
                $("#logTab3Sum").html(sum3)

                if(str_projectList.length == 0){
                    $("#logDetails .s_tbc_1").hide()
                }else{
                    $("#logDetails .s_tbc_1").show()
                }
                if(str_productList2.length == 0){
                    $("#logDetails .s_tbc_2").hide()
                }else{
                    $("#logDetails .s_tbc_2").show()
                }
                if(str_productList3.length == 0){
                    $("#logDetails .s_tbc_3").hide()
                }else{
                    $("#logDetails .s_tbc_3").show()
                }

                bounce_Fixed.show( $("#logDetails") );
            }


        }
    })

}
// creator: hxz，2022-08-22 08:57:55，   起用服务套餐
function service_start(obj) {
    editObjTr = obj
    let trInfo = obj.siblings(".hd").html()
    trInfo = JSON.parse(trInfo);
    let serviceId = trInfo.id
    $("#stopOrStartService").data("sta", 1).data("id", serviceId)
    $("#stopTip").html(`<p>确定后，新增合同中将又可选到该套餐。</p><p>确定复用该套餐吗？</p>`);
    bounce.show($("#stopOrStartService"));
}
// creator: hxz，2022-08-22 08:57:55，  停用服务套餐
function service_stop(obj) {
    editObjTr = obj
    let trInfo = obj.siblings(".hd").html()
    trInfo = JSON.parse(trInfo);
    let serviceId = trInfo.id
    $("#stopOrStartService").data("sta", 0).data("id", serviceId)
    $("#stopTip").html(`<p>确定后，套餐或合同中将无法选到该项目</p><p>确定停用该套餐吗？</p>`);
    bounce.show($("#stopOrStartService"));
}
// creator: hxz，2022-09-16 08:57:55，  停用 启用 删除 确定
function stopOrStartService() {
    let sta = $("#stopOrStartService").data("sta")
    let serviceId = $("#stopOrStartService").data("id")
    let url = '../service/stopServiceDetail.do'
    if(sta == 1){
        url = '../service/startServiceDetail.do'
    }else if(sta == 2){
        url = '../service/deleteServicePackage.do'
    }
    $.ajax({
        "url":url,
        "data":{ "serviceId": serviceId },
        success:function(res){
            let success = res.success
            bounce.cancel();
            if(success == 1){
                layer.msg("操作成功！")
                let cur = $(".yecur:visible").html()
                if($(".mainCon1").is(":visible")){ // 启用
                    let key = $("#search1").val()
                    getList(cur,key)
                } else { // 停用
                    let key = $("#search1").val()
                    getStopList(cur,key)
                }
            }else{
                layer.msg("操作失败！")
            }
        }
    })
}
// creator: hxz，2022-08-22 08:57:55，  删除服务套餐
function service_del(obj) {
    editObjTr = obj
    bounce.show($("#serviceScan"));
    let trInfo = obj.siblings(".hd").html()
    trInfo = JSON.parse(trInfo);
    let serviceId = trInfo.id
    $("#stopOrStartService").data("sta", 2).data("id", serviceId)
    $("#stopTip").html(`<p>确定删除该套餐吗？</p>`);
    bounce.show($("#stopOrStartService"));
}
// creator: hxz，2022-08-22 08:57:55，  获取暂停套餐列表
function stoppedService() {
    showMainCon(2);
    getStopList(1,"")
}
// creator: hxz，2022-08-22 08:57:55，  获取暂停套餐列表
function getStopList(cur,key) {
    $.ajax({
        "url":"../service/getSuspendServicePackageList.do",
        "data":{ "currentPageNo": cur, "pageSize": 20, "keyword": key },
        success:function(res){
            let list = res["data"]["list"] || []
            let pageInfo = res["data"]["pageInfo"] || {}
            let str = ``
            list.forEach((item)=>{
                str += `
                <tr>
                    <td>${ item.code }/${ item.name }</td>
                    <td>${ item.unitPriceReference || '' }</td>
                    <td>${ formatDur( item.isPeriodical,  item.periodDuration,  item.periodUnit) }</td>
                    <td>${item.createName} ${ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss") }</td>
                    <td>
                        <span data-fun="service_scan" class="link-blue funBtn">查看</span>
                        <span data-fun="service_start" class="link-blue funBtn">复用</span>
                        <span data-fun="service_del" class="link-red  funBtn">删除</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                `
            })
            $("#main2Tab tbody").html(str)
            $("#snum").html(list.length);
            setPage($("#ye2"),pageInfo.currentPageNo, pageInfo.totalPage,"serviceSetMealListStop", JSON.stringify({ "key":key,  "status": 0 }))

        }
    })
}
// creator: hxz，2022-08-22 08:57:55，  返回
function reback() {
    showMainCon(1);
    let cur = $("#ye_service .yecur").html()
    getList(cur,"");
}
// creator: 李玉婷，2022-04-12 08:57:55，显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    $(".mainCon" + num).show().siblings().hide();
}
// creator: 李玉婷，2020-04-03 10:28:28，描述、说明字数限制
function limitWord(obj, num) {
    var val = obj.val();
    var length = val.length;
    if (length <= num){
        obj.siblings(".lenTip").html(length + '/' + num);
    }else{
        var str = val.slice(0,num);
        obj.val(str);
        obj.siblings(".lenTip").html(str.length + '/' + num);
    }
}

// creator: hxz，2022-08-22 08:57:55， 新增套餐 确定
function addServiceSure() {
    let init = $("#addService").data("init");
    let mains = $("#addService").data("main");
    let isRestrictive = $("#addService").data("isLimit")?1:0
    // 判断是否必填项设置完（上面的所有星号项，以及必须一个项目）
    let requireKey = "name,code,chargeStage,chargeLimit,";
    let priceKey = "unitPrice,unitPriceNotax,unitPriceInvoice,unitPriceNoinvoice,unitPriceReference";
    if (init.isPeriodical === '1') {
        requireKey += "periodDuration,periodUnit,"
    }
    let nullNum = 0
    let priceNum = 0
    $("#addService [name]").each(function () {
        let name = $(this).attr("name")
        let val = $(this).val()
        if ( requireKey.indexOf(name) > -1 && val === '') {
            nullNum++
        }
        if ( priceKey.indexOf(name) > -1 && val.length > 0) {
            priceNum++
        }
    })
    if(priceNum === 0){
        nullNum++
    }
    if (nullNum > 0 || mains.project.projects.length === 0) {
        layer.msg("操作失败！\n上面所有必填项设置完后，才可进行此项操作！")
        return false
    }

    if (init.isMixture === 0 && mains.project.projects.length < 2) {
        // 无商品
        layer.msg("操作失败！此种套餐要求项目不得少于两个！")
        return false
    }

    let trailData = $("#addService").data("trailed");
    let goodsMain = [...mains.good2.goods2, ...mains.good1.goods1]
    let mainData = {
        type: 1, // 	是 	int 	类型:1-正式,2-体验,3-试用 	1.262新增参数
        state: trailData?1:0,
        isPeriodical: init.isPeriodical, // 	是 	Integer 	是否周期性 1是 0否
        isMixture: init.isMixture,   // 	是 	Integer 	是否混合:项目+商品 1是 0否
        isRestrictive: isRestrictive, // 是否限制选择 1是 0否
        chargeNum: '',
        enteringType: '', // 	否 	Integer 	录入类型:1-金额,2-比例
        upperType: '',   // 	否 	Integer 	上限类型:1-专票不含税价,2-专票含税价,3-普票价,4-不开票价,5-参考单价
        productListJson: JSON.stringify(chargeParam(goodsMain)),   // 	否 	String 	商品 字符串集合
        projectListJson: JSON.stringify(chargeParam(mains.project.projects))   // 	否 	String 	项目 字符串集合
    }
    $("#addService [name]").each(function () {
        let name = $(this).attr("name")
        let value = $(this).val()
        mainData[name] = value
    })

    console.log('lastMainData', mainData)
    console.log('lastTrailData', trailData)
    $.ajax({
        url: '../service/addServicePackage.do',
        data: mainData,
        beforeSend: function () {
            loading.open()
        },
        success:function(res){
            let success = res.success
            if(success == 1){

                let data = res.data
                let mainPackageId = data.id
                if (trailData) {
                    let goodsTrail = [...trailData.good2.goods2, ...trailData.good1.goods1]
                    trailData.productListJson = JSON.stringify(chargeParam(goodsTrail))   // 	否 	String 	商品 字符串集合
                    trailData.projectListJson = JSON.stringify(chargeParam(trailData.project.projects))   // 	否 	String 	项目 字符串集合
                    delete trailData.good2
                    delete trailData.good1
                    delete trailData.project
                    trailData.mainPackage = mainPackageId
                    $.ajax({
                        url: '../service/addServicePackage.do',
                        data: trailData,
                        beforeSend: function () {},
                        success:function(res){
                            let success = res.success
                            if(success == 1){
                                loading.close()
                                layer.msg("新增成功！")
                                getList(1);
                            }else{
                                layer.msg("新增失败！")
                            }
                            bounce.cancel();
                        },
                        complete: function () {}
                    })
                } else {
                    getList(1);
                    layer.msg("新增成功！")
                    loading.close()
                }
            }else{
                layer.msg("新增失败！")
            }
            bounce.cancel();
        },
        complete: function () {}
    })
    // $.ajax({
    //     url: '../service/addServicePackage.do',
    //     data: mainData,
    //     beforeSend: function () {
    //         loading.open()
    //     },
    //     success:function(res){
    //         let success = res.success
    //         if(success == 1){
    //             layer.msg("新增成功！")
    //             getList(1);
    //             if (trailData) {
    //                 $.ajax({
    //                     url: '../service/addServicePackage.do',
    //                     data: trailData,
    //                     beforeSend: function () {},
    //                     success:function(res){
    //                         let success = res.success
    //                         if(success == 1){
    //                             loading.close()
    //                             layer.msg("新增成功！")
    //                             let cur = $("#ye1 .yecur").html()
    //                             let json = $("#ye1 .json").html()
    //                             json = JSON.parse(json);
    //                             let key = json.key || ''
    //                             getList(cur, key);
    //                         }else{
    //                             layer.msg("新增失败！")
    //                         }
    //                         bounce.cancel();
    //                     },
    //                     complete: function () {}
    //                 })
    //             } else {
    //                 loading.close()
    //             }
    //
    //         }else{
    //             layer.msg("新增失败！")
    //         }
    //         bounce.cancel();
    //     },
    //     complete: function () {}
    // })
    //
    // $(".service_main input:visible").each(function () {
    //     let name = $(this).attr("name")
    //     let value = $(this).val()
    //     mainData[name] = value
    // })
    // $(".service_other input:visible").each(function () {
    //     let name = $(this).attr("name")
    //     let value = $(this).val()
    //     otherData[name] = value
    // })
    // let sendData = {
    //     type: 1, // 类型:1-正式,2-体验,3-试用 (1.262新增)
    //     name: $("#name_sv").val(), // 简称
    //     code: $("#code_sv").val(), // 代码
    //     memo:$("#desc_sv").val(), // 说明
    //     isPeriodical: init.isPeriod, // 是否周期性 1是 0否|
    //     isMixture: init.hasGoods, // 是否混合:项目+商品 1是 0否
    //     periodDuration: $("#periodDuration").val(), // 周期数
    //     periodUnit: $("#periodUnit").val(), // 周期单位:1-日,2-周,3-旬,4-月,5-季,6-半年,7-年
    //     chargeNum: "", // 收费次数
    //     chargeStage: $("#chargeStage").val(), // 收费时间类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,11-本期通过验收后,12-最终通过验收后
    //     chargeLimit: $("#chargeLimit").val(), // 收费时限(n天内)
    //     taxRate: $("#taxRate").val(), // 税率
    //     unitPrice: $("#unitPrice").val(), // 含税单价(增专)
    //     unitPriceNotax: $("#unitPriceNotax").val(), // 不含税单价(增专
    //     unitPriceInvoice: $("#unitPriceInvoice").val(), // 开票单价(增普)
    //     unitPriceNoinvoice: $("#unitPriceNoinvoice").val(), // 不开票单价
    //     unitPriceReference: $("#unitPriceReference").val(), // 参考价格
    //     priceDesc: $("#priceDesc").val() , // 价格描述
    //     enteringType: "", // 录入类型:1-金额,2-比例
    //     upperType: "", // 上限类型:1-专票不含税价,2-专票含税价,3-普票价,4-不开票价,5-参考单价
    //     productListJson: [], // 商品 字符串集合
    //     projectListJson: [], // 项目 字符串集合
    // }
    // if (type === 2) {
    //     sendData.concat({
    //         state: state, // 状态:0-未设置体验/试用,1-已设置
    //         trialDays: state, // 试用天数
    //         trialUpper: state, // 试用数量上限
    //         isChargeable: state // 是否收费
    //     })
    // }
    // let reqireKey = "name,code,chargeStage,chargeLimit,";
    // let priceKey = "unitPrice,unitPriceNotax,unitPriceInvoice,unitPriceNoinvoice,unitPriceReference";
    // if(init.isPeriod == 1){
    //     reqireKey += "periodDuration,periodUnit,"
    // }
    // let nullNum = 0
    // let priceNum = 0
    // for(let key in sendData){
    //     let val = sendData[key]
    //     if(reqireKey.indexOf(key) > -1 && val.length == 0){
    //         nullNum++
    //     }else if(priceKey.indexOf(key) > -1 && val.length > 0){
    //         priceNum++
    //     }
    // }
    // if(priceNum == 0){
    //     nullNum++
    // }
    // if(nullNum > 0){
    //     layer.msg("请将内容补充完整！");
    //     return false;
    // }
    // $("#serviceTab tbody tr.projectTr").each(function () {
    //     let info = $(this).find(".hd").html();
    //     info = JSON.parse(info);
    //     let index = $(this).index()
    //     sendData.projectListJson.push({
    //         orders: index,
    //         codeName: info.code,//代码
    //         serviceItem: info.id,//服务项目ID
    //         itemQuantity: info.itemQuantity,//项目数量
    //         periodQuantity: info.periodQuantity,//周期数量
    //         amount: info.unitPriceReference || 0 ,//金额
    //         memo: info.memo, // 备注
    //     });
    //
    // })
    // $("#goodsTab tbody tr.goodsTr").each(function () {
    //     let info = $(this).find(".hd").html();
    //     info = JSON.parse(info);
    //     let index = $(this).index()
    //     sendData.productListJson.push({
    //         orders: index,
    //         codeName: info.code,//代码
    //         serviceItem: info.id,//服务项目ID
    //         // "itemQuantity": info.itemQuantity,//项目数量
    //         // "periodQuantity": info.periodQuantity,//周期数量
    //         deliveryTerm: info.init.deliveryTerm ,//供货方式:1-每个收费周期都需提供,2-仅第一个收费周期提供
    //         deliveryPlace: info.init.deliveryPlace ,//供货地点1-存放于本机构2-需交到客户手中
    //         deliverySequence: info.init.deliverySequence ,//供货顺序1-不需要2-需要先付套餐后提供商品
    //         deliveryDuration: info.init.deliveryDuration,//客户付款后,商品提供给客户需要时长(天)
    //         deliveryQuantity: info.deliveryQuantity,//货物数量
    //         amount: info.sum,  //金额
    //         // "chargeNum": info.orders,  //收费次数(第n次)
    //         deliveryStage: info.sendDian, //发货时间要求类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,11-本期通过验收后,12-最终通过验收后
    //         // "deliveryNum": "", // 交付次数(第n次)
    //         deliveryLimit: info.sebdDDay, // 发货时间要求时限(n天内)
    //         // "memo": "", // 备注
    //     });
    //
    // })
    // $("#goods2Tab tbody tr.goodsTr").each(function () {
    //     let info = $(this).find(".hd").html();
    //     info = JSON.parse(info);
    //     let index = $(this).index()
    //     sendData.productListJson.push({
    //         orders: index ,
    //         codeName: info.code,//代码
    //         serviceItem: info.id,//服务项目ID
    //         // "itemQuantity": info.itemQuantity,//项目数量
    //         // "periodQuantity": info.periodQuantity,//周期数量
    //         deliveryTerm: info.init.deliveryTerm ,//供货方式:1-每个收费周期都需提供,2-仅第一个收费周期提供
    //         deliveryPlace: info.init.deliveryPlace ,//供货地点1-存放于本机构2-需交到客户手中
    //         deliverySequence: info.init.deliverySequence ,//供货顺序1-不需要2-需要先付套餐后提供商品
    //         deliveryDuration: info.init.deliveryDuration,//客户付款后,商品提供给客户需要时长(天)
    //         deliveryQuantity: info.deliveryQuantity,//货物数量
    //         amount: info.sum,  //金额
    //         // "chargeNum": info.orders,  //收费次数(第n次)
    //         deliveryStage: info.sendDian, //发货时间要求类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,11-本期通过验收后,12-最终通过验收后
    //         // "deliveryNum": "", // 交付次数(第n次)
    //         deliveryLimit: info.sebdDDay, // 发货时间要求时限(n天内)
    //         // "memo": "", // 备注
    //     });
    //
    // })
    // sendData.productListJson = JSON.stringify(sendData.productListJson)
    // sendData.projectListJson = JSON.stringify(sendData.projectListJson)
    // $.ajax({
    //     url: '../service/addServicePackage.do',
    //     data: mainData,
    //     beforeSend: function () {
    //         loading.open()
    //     },
    //     success:function(res){
    //         let success = res.success
    //         if(success == 1){
    //             layer.msg("新增成功！")
    //             let cur = $("#ye1 .yecur").html()
    //             let json = $("#ye1 .json").html()
    //             json = JSON.parse(json);
    //             let key = json.key || ''
    //             getList(cur, key);
    //             if (trailData) {
    //                 $.ajax({
    //                     url: '../service/addServicePackage.do',
    //                     data: trailData,
    //                     beforeSend: function () {},
    //                     success:function(res){
    //                         let success = res.success
    //                         if(success == 1){
    //                             loading.close()
    //                             layer.msg("新增成功！")
    //                             let cur = $("#ye1 .yecur").html()
    //                             let json = $("#ye1 .json").html()
    //                             json = JSON.parse(json);
    //                             let key = json.key || ''
    //                             getList(cur, key);
    //                         }else{
    //                             layer.msg("新增失败！")
    //                         }
    //                         bounce.cancel();
    //                     },
    //                     complete: function () {}
    //                 })
    //             } else {
    //                 loading.close()
    //             }
    //
    //         }else{
    //             layer.msg("新增失败！")
    //         }
    //         bounce.cancel();
    //     },
    //     complete: function () {}
    // })

}

function chargeParam(data) {
    let newData = []
    for (let i in data) {
        let item = data[i]
        let newItem = {
            orders: Number(i) + 1,
            codeName: item.code,
            serviceItem: item.serviceItem?item.serviceItem:item.id,
            itemQuantity: item.itemQuantity,
            periodQuantity: item.periodQuantity,
            deliveryDuration: item.deliveryDuration,
            deliveryQuantity: item.deliveryQuantity,
            amount: item.amount,
            chargeNum: item.chargeNum || '',
            deliveryStage: item.deliveryStage,
            deliveryNum: item.deliveryNum,
            deliveryLimit: item.deliveryLimit,
            memo: item.memo
        }
        if (item.init) {
            newItem.deliveryTerm = item.init.deliveryTerm
            newItem.deliveryPlace = item.init.deliveryPlace
            newItem.deliverySequence = item.init.deliverySequence
        }
        newData.push(newItem)
    }
    return newData
}

// creator: 张旭博，2023-12-07 11:45:25， 获取操作说明（不同的选择展示不同内容）
function getOperateStr(data) {
    let operatInstructionsStr = ``
    if(data.isPeriodical == 1){
        if(data.isMixture == 1){
            operatInstructionsStr = `
                     <p>本类别的服务套餐：</p>
                    <p>1 按周期收费，且项目与商品均需至少有一种。</p>
                    <p>2 不可既包含按周期收费的项目，又包含不按周期收费的项目。</p>
                    <p>3 所选如为不按周期收费的项目，套餐的收费周期需另行设置。</p>
                    <p>4 所选为按周期收费项目时，项目的数量仅可为1不可编辑，套餐的收费周期也由系统确定为该项目的收费周期，也不可编辑。</p>
                    <p>5 选择一个按周期收费项目后，继续选择时，仅可选择相同收费周期的项目。</p>
                    <p>6 系统不支持设置与交付/验收有关的套餐，故所选入套餐的项目，即便收费与交付/验收有关，相关事项也无法进入套餐。   </p>
                `
        }else{
            operatInstructionsStr = `
                    <p>本类别的服务套餐：</p>
                    <p>1 按周期收费、不含商品，需包含项目不得少于两个。</p>
                    <p>2 不可既包含按周期收费的项目，又包含不按周期收费的项目。</p>
                    <p>3 所选如为不按周期收费的项目，套餐的收费周期需另行设置。</p>
                    <p>4 所选为按周期收费项目时，套餐的收费周期系由系统确定，不可自行设定。</p>
                    <p>5 选择一个按周期收费项目后，继续选择时，仅可选择相同收费周期的项目。</p>
                    <p>6 系统不支持设置与交付/验收有关的套餐，故所选入套餐的项目，即便收费与交付/验收有关，相关事项也无法进入套餐。</p>
                `
        }
    }else {
        if(data.isMixture == 1){
            operatInstructionsStr = `
                    <p>本类别的服务套餐：</p>
                    <p>1 不按周期收费，且项目与商品均需至少有一种。</p>
                    <p>2 选择项目时，仅可在不按周期收费的项目中选择。</p>
                    <p>3 系统不支持设置与交付/验收有关的套餐，故所选入套餐的项目，即便收费与交付/验收有关，相关事项也无法进入套餐。</p>
                `
        }else{
            operatInstructionsStr = `
                    <p>本类别的服务套餐：</p>
                    <p>1 不按周期收费、不含商品，需包含项目不得少于两个。</p>
                    <p>2 选择项目时，仅可在不按周期收费的项目中选择。</p>
                    <p>3 系统不支持设置与交付/验收有关的套餐，故所选入套餐的项目，即便收费与交付/验收有关，相关事项也无法进入套餐。</p>
                `
        }
    }
    $("#operatInstructionsStr").html(operatInstructionsStr)
}

// creator: 张旭博，2024-03-14 11:54:22， 编辑服务套餐的开票资料
function sureEditInvoiceInfo(type) {
    let serviceData = $("#serviceScan").data("data")
    let pattern = $("#editInvoiceInfo" + type + " [name='useDataType" + type + "']:checked").val()
    let data = {
        servicePackage: serviceData.id,
        type: type,
        pattern: pattern
    }
    if (pattern === '5') {
        data.userDefined = $("#editInvoiceInfo" + type + " [name='userDefined']").val()
    }
    $.ajax({
        url: $.webRoot + '/service/editInvoice.do',
        data: data
    }).then(res => {
        let data = res.success
        if (data === 1) {
            layer.msg("操作成功")
            bounce_Fixed.cancel()
            service_scan(editObjTr)
        }
    })
}

function editInvoiceInfoLog(obj) {
    let type = obj.attr("type")
    let serviceData = $("#serviceScan").data("data")
    let data = {
        servicePackage: serviceData.id,
        type: type
    }
    $.ajax({
        url: $.webRoot + '/service/getInvoiceRecordList.do',
        data: data
    }).then(res => {
        bounce_Fixed2.show($("#invoiceInfoLog" + type))
        let data = res.data
        let list = data.list || []
        let str = ''
        let serviceData = $("#serviceScan").data("data");
        for (let i in list) {
            let item = list[i]
            i = Number(i)
            let invoiceData = {
                name: serviceData.name,
                code: serviceData.code
            }
            let invoiceStr = ''
            if (type === '1') {
                invoiceData.nameInvoice = item
                invoiceStr = chargeNameInvoice(invoiceData)
            } else {
                invoiceData.ggInvoice = item
                invoiceStr = chargeGgInvoice(invoiceData)
            }
            str += `<tr>
                    <td>${i === 0?'首次编辑后':'第'+(i+1)+'次编辑后'}</td>
                    <td>${invoiceStr}</td>
                    <td>${item.createName + '  ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                </tr>`
        }
        let tipStr = list.length === 0?'当前资料尚未编辑':`当前资料为第${list.length}次编辑后的结果，操作者：${data.createName + ' ' + moment(data.createDate).format("YYYY-MM-DD HH:mm:ss")}`
        $("#invoiceInfoLog" + type + " .tips").html(tipStr)
        $("#invoiceInfoLog" + type + " tbody").html(str)
    })
}

// creator: hxz，2022-011-23 税率控制
function chargeRate(obj) {
    let rate = obj.val();
    obj.parents(".priceForm").find("[name='unitPrice']").val('').prop("disabled", rate.length === 0)
    obj.parents(".priceForm").find("[name='unitPriceNotax']").val('').prop("disabled", rate.length === 0)
}
function setPrice(obj, type) {
    let priceForm = obj.parents('.priceForm')
    let rate = priceForm.find("[name='taxRate']").val();
    let unitPrice = priceForm.find("[name='unitPrice']").val();
    let unitPriceNotax = priceForm.find("[name='unitPriceNotax']").val();
    if(type == 1){
        if(unitPrice.length > 0){
            unitPriceNotax = unitPrice/100*(100+Number(rate))
            unitPriceNotax = mathFixed(unitPriceNotax, 8)
            priceForm.find("[name='unitPriceNotax']").val(unitPriceNotax);
        }
    }else if(type == 2){
        if(unitPriceNotax.length > 0){
            unitPrice = unitPriceNotax/(100+Number(rate))*100
            unitPrice = mathFixed(unitPrice, 8)
            priceForm.find("[name='unitPrice']").val(unitPrice);
        }
    }
}
function mathFixed(value, length) {
    let returnVal = value
    let arr = String(value).split('.')
    if(arr.length == 2){
        let str = arr[1]
        if(str.length > length){
            returnVal = value.toFixed(length)
        }
    }
    return returnVal;
}
// creator: hxz，2022-011-23  ， 收费周期
function chargeUnit(obj) {
    let periodDurationInfo = obj.parents(".periodDurationInfo")
    let periodDuration = periodDurationInfo.find("[name='periodDuration']")
    let periodUnit = periodDurationInfo.find("[name='periodUnit']").val()
    let strOption = '<option value="">--请选择--</option>'
    if (periodUnit === '7') {
        strOption += `<option value="1">1</option>`
        periodDuration.prop("disabled", false).html(strOption)
    } else if (periodUnit === '4') {
        for (let i = 1; i< 12; i++) {
            strOption += `<option value="${i}">${i}</option>`
        }
        periodDuration.prop("disabled", false).html(strOption)
    } else {
        periodDuration.prop("disabled", true).val('')
    }
}

// creator: 张旭博，2023-11-22 09:52:12， 检查代号是否重复
function checkCode(selector) {
    let code = selector.val()

    if ($("#editService").is(":visible")) {
        let data = $("#serviceScan").data("data");
        let oldCode = data.code
        if (code === oldCode) {
            $(".errorTip").hide()
            return false
        }
    }
    if ($.trim(code) !== '')
    $.ajax({
        url: $.webRoot + '/service/findServicePackageCode.do',
        data: {
            code: code,
            id: 0
        },
        beforeSend: function () {},
        success: function (res) {
            let data = res.data
            if (data.obj !== null) {
                $(".errorTip").show()
            } else {
                $(".errorTip").hide()
            }
        },
        complete: function () {}
    })
    console.log('检测了！')
}

// creator: 张旭博，2024-04-11 09:13:50， 收费内容格式化
function chargeVATInvoice(data) {
    let taxRate = data.taxRate || ''
    let unitPrice = data.unitPrice || ''
    let unitPriceNotax = data.unitPriceNotax || ''
    let arr = []
    if (taxRate) arr.push('税率'+data.taxRate+'%')
    if (unitPrice) arr.push('含税单价'+data.unitPrice+'元')
    if (unitPriceNotax) arr.push('不含税价'+data.unitPriceNotax+'元')
    if (arr.length === 0) {
        return '--'
    } else {
        return arr.join('，')
    }
}

// creator: 张旭博，2024-04-11 09:05:32， 复制内容到剪贴版
async function copyTextToClipboard(obj) {
    let text = obj.siblings('span').html()
    try {
        await navigator.clipboard.writeText(text);
        console.log('Text copied to clipboard');
        layer.msg("已复制到剪贴板")
    } catch (err) {
        console.error('复制失败: ', err);
    }
}

