var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#seeServiceByCycle"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#scanGoods"));
bounce_Fixed3.cancel();
var bounce_Fixed4 = new Bounce(".bounce_Fixed4");
bounce_Fixed4.show($("#tip1"));
bounce_Fixed4.cancel();
$(function () {
    getContractList(1)
    // 客户名称匹配
    $("#cusSearchInput").on("click keyup",function (e) {
        $(this).next().show();
        e.stopPropagation();
        // 设置下拉列表（通过缓存数据设置）
        setCusOption();
    });
    $("body").on("click",".funBtn,.funbtn,.linkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    $("body").click(function(){
        console.log("body 点击")
        let cusId = $("#cusSearchInput").data("cusId");
        let vis = $(".cusSearchItems").is(":visible")
        if(vis){
            if(cusId == ""){
                layer.msg("请先选择客户！！");
            }
            $(".cusSearchItems").hide();
        }
    });
    $(".cusSearchItems").on("click", "option", function(){
        matchCus( $(this) );
        $(".cusSearchItems").hide();
        console.log('这里是 点击事件')
    })
    $("#invoiceSet .setItem").click(function(){
        $("#invoiceSet .fa").attr("class","fa fa-circle-o");
        $(this).children(".fa").attr("class","fa fa-dot-circle-o");
    })
    $("#modeSetting .dotItem").on("click",".changeDot", function () {
        let idx = $(this).index();
        let pInx = $("#modeSetting .dotItem").index($(this).parents(".dotItem"));
        if ($(this).find("i").hasClass("fa-circle-o")){
            $(this).find("i").attr("class", "fa fa-dot-circle-o");
            $(this).siblings().find("i").attr("class", "fa fa-circle-o");
            if (pInx === 1) {
                if (idx === 1) {
                    $("#modeSetting .dotItem:eq(2)").hide();
                    $("#modeSetting .dotItem:eq(3)").hide();
                } else if (idx === 2) {
                    $("#modeSetting .dotItem:eq(2)").show();
                }
            } else if (pInx === 2) {
                if (idx === 1) {
                    $("#modeSetting .dotItem:eq(3)").show();
                } else if (idx === 2) {
                    $("#modeSetting .dotItem:eq(3)").hide();
                }
            }
        }
    });
    $("#serviceInitChoose").on("click", '.changeDot', function () {
        if ($(this).find("i").hasClass("fa-circle-o")){
            $(this).find("i").attr("class", "fa fa-dot-circle-o");
            $(this).siblings().find("i").attr("class", "fa fa-circle-o");
        }
    });
    invoiceSettings($("#addService select[name='taxRate']"), "");
    $("#mealInitChoose").on("click",".changeDot",function(){
        $(this).parent(".dotItem").find(".fa").attr("class", "fa fa-circle-o");
        let faO = $(this).find(".fa");
        faO.attr("class", "fa fa-dot-circle-o");
        let key = faO.data("type")
        let val = faO.data("val")
        if(key == "fuBefore"){
            if(val == 2){
                $("#addGoods1 .fuBefore2").show();
                $("#addGoods1 .fuBefore2 input").val("");
            }else{
                $("#addGoods1 .fuBefore2").hide();
            }
        }
    })
    $("#addGoods1").on("click",".changeDot",function(){
        $(this).parent(".dotItem").find(".fa").attr("class", "fa fa-circle-o");
        let faO = $(this).find(".fa");
        faO.attr("class", "fa fa-dot-circle-o");
        let key = faO.data("type")
        let val = faO.data("val")
        if(key == "fuBefore"){
            if(val == 2){
                $("#addGoods1 .fuBefore2").show();
                $("#addGoods1 .fuBefore2 input").val("");
            }else{
                $("#addGoods1 .fuBefore2").hide();
            }
        }
    })
    // 添加 项目 toggle
    $("#addpTab1").on("click",".fa", function () {
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class", "fa fa-check-square-o")
            let changetds = $(this).parents("tr").find(".changeTd")
            let where = $("#addpTab1").data('where');
            let init = $("#addMealService").data("init");
            if(where == "edit"){
                init = $("#editService").data("init");
            }
            let isdur = init.isdur
            changetds.each(function () {
                let index = $(this).index()
                if(index == 4){
                    if(isdur == 1){
                        $(this).removeClass("bgGray");
                        $(this).html(`<input type="text" onkeyup="clearNum(this)" class="form-control" />`)
                    }
                }else if(index == 5){
                    $(this).removeClass("bgGray");
                    $(this).html(`<input type="text" onkeyup="clearNum(this)" class="form-control" />`)
                }
            })
        }else{
            $(this).attr("class", "fa fa-square-o")
            let changetds = $(this).parents("tr").find(".changeTd")
            changetds.addClass("bgGray")
            changetds.html(``)
        }
    })
    // 添加 商品 toggle
    $("#addgTab1").on("click",".fa", function () {
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class", "fa fa-check-square-o")
            let init = $("#addGoods2").data("init");
            let changetds = $(this).parents("tr").find(".changeTd")
            changetds.each(function(){
                let index = $(this).index()
                if(index == 4){
                    if(init.whereSet == 1 && init.fuBefore == 1 ){
                        $(this).removeClass("bgGray");
                        $(this).html(`<input type="text" class="time funbtn" readonly data-fun="inputTime" placeholder="请选择">`)
                    }else{
                        $(this).removeClass("bgGray");
                        $(this).html(`<input type="text" onkeyup="clearNum(this)" class="form-control gn" />`)
                    }
                }else if(index == 5){
                    $(this).removeClass("bgGray");
                    $(this).html(`<input type="text" onkeyup="clearNum(this)" class="form-control gn" />`)
                }
            })

        }
        else{
            $(this).attr("class", "fa fa-square-o")
            // let changetds = $(this).parents("tr").find(".changeTd")
            // changetds.addClass("bgGray")
            // changetds.html(``)
            let init = $("#addGoods2").data("init");
            let changetds = $(this).parents("tr").find(".changeTd")
            changetds.each(function(){
                let index = $(this).index()
                if(index == 4){
                    if(init.whereSet == 1 && init.fuBefore == 1 ){
                        $(this).addClass("bgGray");
                        $(this).html(`<input type="text" readonly placeholder="请选择">`)
                    }else{
                        $(this).addClass("bgGray");
                        $(this).html(``)
                    }
                }else if(index == 5){
                    $(this).addClass("bgGray");
                    $(this).html(``)
                }
            })
        }
    })
});
function getContractList(currPage){
    let searchKey = $("#search1").val();
    $.ajax({
        "url": '../saleContract/list',
        "data": {
            "customerName": searchKey,
            "pageSize": 20,
            "currentPageNo": currPage
        },
        success:function(data){
            let list =  data.data.data || [];
            let str = ``
            list.forEach((item)=>{
                str += `
                <tr>
                    <td>${ item.sn }</td>
                    <td>${ item.customer_name }</td>
                    <td>${ new Date(item.sign_time).format("yyyy-MM-dd") }</td>
                    <td>${new Date(item.valid_start).format("yyyy-MM-dd")}至${ new Date(item.valid_start).format("yyyy-MM-dd") }</td>
                    <td>${item.create_name} ${new Date(item.create_date).format("yyyy-MM-dd hh:mm:ss")}</td>
                    <td class="ty-td-control">
                        <span data-fun="unDo" class="ty-color-blue funbtn">合同管理</span>
                        <span data-fun="unDo" class="ty-color-blue funbtn">回款录入</span>
                        <span data-fun="unDo" class="ty-color-blue funbtn">开票申请</span>
                    </td>
                    <td>0.00%</td>
                    <td>0.00%</td>
                    <td>0.00%</td>
                    <td>0.00%</td>
                </tr>
                `
            })
            $("#mainsNum").html(list.length);
            $("#main1Tab").find("tbody").html(str);
            setPage($("#ye1"), data.data.currPage, data.data.totalPage, "serviceContract")
        }
    })
}
// creator: 张旭博，2018-05-30 10:08:17，新增服务合同 - 按钮
function addServiceContract() {
    bounce.show($("#newServiceContract"));
    $(".cusSearchItems").hide();
    $("#cusSearchInput").data("cusId", "");
    $("#newServiceContract input").val("");
    $("#newServiceContract .gray-box").html("");
    getCustomName();
    $("#newServiceContract .hd").html("")
    $("#newServiceContract .s_contact").hide();
    invoiceSettings($("#newServiceContract select[name='taxRate']"), "");
    bounce.everyTime('0.5s', 'newContract', function () {
        let cusId = $("#cusSearchInput").data("cusId");
        let date = $("#contractReceiveTime").val();
        let type = $(".invoiceRequire").val();
        if (cusId == "" || cusId == undefined || date === "" || type === "") {
            $(".serviceInfo").html("")
            $(".noSelectMod").show();
            $(".selectedMod").hide();
            $(".s_contact input").val("");
            $(".s_contact select").val("");
            $(".s_contact .gray-box").html("");
            $(".s_contact .hd").html("")
            $("#newServiceContract .s_contact").hide();
            $(".selectBtns span:eq(0)").html("在项目模板中选择")
            $(".selectBtns span:eq(1)").html("在套餐模板中选择")
            $(".selectBtns span").removeClass("linkBtn").removeAttr("onclick");
        } else {
            let amount = "";
            if($(".serviceInfo").html() !== ""){
                $(".noSelectMod").hide();
                $(".selectedMod").show();
                if (type === 1) {
                    amount = $("#newServiceContract .priceDot:visible input").val();
                } else {
                    amount = $("#newServiceContract .priceSet:visible input").val();
                }
                $(".contractAmount").val(amount)
                $(".selectBtns span:eq(0)").addClass("linkBtn").attr("onclick", "updateTemplate(1)").html("改选其他项目模板");
                $(".selectBtns span:eq(1)").addClass("linkBtn").attr("onclick", "updateTemplate(2)").html("改选其他套餐模板");
            } else {
                $(".noSelectMod").show();
                $(".selectedMod").hide();
                $(".selectBtns span:eq(0)").addClass("linkBtn").attr("onclick", "getTemplate(1, 1)").html("在项目模板中选择");
                $(".selectBtns span:eq(1)").addClass("linkBtn").attr("onclick", "getTemplate(1, 2)").html("在套餐模板中选择");
            }
        }
    });
}
// creator: 张旭博，2018-05-30 10:08:17，获取客户名称
function getCustomName() {
    $.ajax({
        url: "/sales/getPdCustomerName.do",
        success: function (data){
            if(data == ""||data == undefined){
                layer.msg("未获取到数据！")
            }else{
                var PdCustomerName = data["PdCustomerName"];
                var PdCustomerNameStr = "<option value=''>----- 请选择客户名称 -----</option>";
                for(var i in PdCustomerName){
                    PdCustomerNameStr +=
                        `<option invoice="${PdCustomerName[i].invoiceRequire}" code="${PdCustomerName[i].code}" value="${PdCustomerName[i].id}">${PdCustomerName[i].fullName}</option>`;
                }
                $(".cusSearchItems").html(PdCustomerNameStr);
            }
        }
    })
}
//  creator: hxz  2021-07-07  点击选择客户
function matchCus( optionObj){
    //清空商品列表
    if(optionObj.val() == ""){
        layer.msg("请选择有效客户！");
        return false ;
    }
    $(".customerId").val(optionObj.attr("code"));
    let cusId = optionObj.val();
    getAddressList(cusId)
    $(".customerName").val(optionObj.html()).data("cusId" , cusId);
    let invoiceRequire = optionObj.attr("invoice")
    $(".invoiceRequire").data("invoice", invoiceRequire).val(formatInviceRequire(invoiceRequire) )
}
//  creator: hxz  2021-07-07  设置客户下拉列表
function setCusOption() {
    let cusName = $("#cusSearchInput").val();
    if(cusName === ""){
        $(".cusSearchItems option").show()
    }else{
        let count = 0
        let Obj = {}
        $(".cusSearchItems option").each(function(){
            let itemName = $(this).html();
            if(itemName.indexOf(cusName) > -1){
                $(this).show();
                count++ ;
                if(itemName == cusName){ // 完全相等
                    matchCus($(this));
                } else {
                    $("#cusSearchInput").data("cusId", "");
                    $(".customerId").val("");
                    $(".invoiceRequire").val("");
                    $("#newServiceContract .hd").html("")
                    $("#newServiceContract .s_contact").hide();
                }
            }else{
                $(this).hide();
            }
        })
        if(count === 0){
            layer.msg('请通过录入有效的客户名进行选择！！')
            // $("#cusSearchInput").val("");
            $(".cusSearchItems option").show();
        }
    }
}
// creater :hxz 2021-05-12 开票要求 编辑
function invoiceRequireEdit(){
    let cusId = $(".customerName").data("cusId")
    if(!cusId || cusId=== ""){
        layer.msg("请先选择客户名称！");
        return false;
    }
    if($(".serviceInfo").html() !== ""){
        layer.msg('<p>模板选定后，此项已不可编辑！</p><p>如需要，请重新“新增服务合同”！</p>');
        return false;
    }
    $("#invoiceSet").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o")
    bounce_Fixed.show($("#invoiceSet"));
}
function invoiceSetOk() {
    var selectP = $("#invoiceSet .fa-dot-circle-o").parent();
    let type = selectP.data("type");
    let txt = ''
    if(Number(type) > 0  ){
        txt = selectP.children("span").html();
        let cusID = $(".customerName").data("cusId")
        $.ajax({
            "url":"../sales/updateCustomerInvoiceSet",
            "data":{ 'cusId': cusID, 'invoiceRequire': type},
            success:function (res) {
                $(".invoiceRequire").data("invoice", type).val(txt);
            }
        })
        bounce_Fixed.cancel();
    }else{
        layer.msg("请先选择开票要求")
    }

}
/*creator:lyt 2022/10/11 下午 8:15 */
function getTemplate(cur, source) {
    let curObj = $("#proTemplate");
    let url = `../saleService/list`;
    let data = { "currentPageNo": cur, "pageSize": 20 }
    if (source === 1) {
        data.name = "";
        data.enabled = 1;
    }else if (source === 2) {
        curObj = $("#mealTemplate");
        data.keyword = ""
        url = `../service/getServicePackageList.do`;
    }
    curObj.find("tbody tr:gt(0)").remove();
    $.ajax({
        "url": url,
        "data": data,
        success:function(data){
            let list = [];
            let pageInfo = {};
            let str = ``
            if (source === 1) {
                list =  data.data || [];
                pageInfo = data.page || {};
            } else if (source === 2) {
                list =  data.data.list || [];
                pageInfo = data.data["pageInfo"] || {};
            }
            list.forEach((item)=>{
                str += `
                <tr>
                    <td><i class="fa fa-circle-o" onclick="checkState($(this))"></i></td>
                    <td>${ item.code }/${ item.name }</td>
                    <td>${ formatDur( item, 'price') }</td>
                    <td>${ formatDur( item, 'Duration') }</td>
                    <td>${item.createName} ${ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss") }</td>
                    <td>
                        <span data-fun="${source === 1 ?'seeService':'service_scan'}" class="ty-color-blue funbtn">查看</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                `
            })
            curObj.find("tbody").append(str);
            bounce_Fixed.show(curObj);
            setPage($("#template" + source), pageInfo.currentPageNo, pageInfo.totalPage, "serviceTemplateSet", JSON.stringify({
               "source": source
            }))
        }
    })
}
// creater :lyt 2022-12-05 改选其他项目模板
function updateTemplate(source){
    $("#reTemplateTip").data("source", source);
    bounce_Fixed.show($("#reTemplateTip"));
}
// creater :lyt 2022-12-05 改选其他项目模板确定
function reTemplateSure(){
    $(".serviceInfo").html("")
    $(".s_contact input").val("");
    $(".s_contact select").val("");
    $(".s_contact .gray-box").html("");
    $(".s_contact .hd").html("")
    $("#newServiceContract .s_contact").hide();
    getTemplate(1, $("#reTemplateTip").data("source"));
}
// creator: 李玉婷，2022-04-12 09:13:11，新增服务项目
function addServiceProject(){
    $("#serviceInitChoose .changeDot i").attr("class", "fa fa-circle-o");
    bounce_Fixed2.show($("#serviceInitChoose"));
}
function serviceInitChoose(){
    if ($("#serviceInitChoose .fa-dot-circle-o").length > 0) {
        let tip = ``;
        let val = $("#serviceInitChoose .fa-dot-circle-o").parent().data("type");
        $("#addService").data("cycle", val);
        $("#addService input").val("").prop("disabled", false);
        $("#addService select").val("");
        $("#addService").removeData("modeSetting");
        $("#addService select[name='periodDuration']").prop("disabled", true);
        if (val === 1) {
            tip = `请录入按周期（如按年、按月）收费的项目！`;
            $("#addService .byCycle").show();
            $("#addService .noByCycle").hide();
        } else {
            tip = `请录入不按周期收费的项目！`;
            $("#addService .byCycle").hide();
            $("#addService .noByCycle").show();
            $("#addService .modeNoSetted").show().siblings().hide();
            getUnitList($("#add_unitSelect"), 2);
        }
        $("#addService .headTip").html(tip);
        bounce_Fixed2.show($("#addService"));
    }
}
// creator: 李玉婷，2022-04-12 09:48:09，收费周期-年、个月切换
function switchPeriod(obj){
    let val = obj.val();
    let options = ``;
    if (val !== "") {
        obj.prev().val("").prop("disabled", false);
        if (val === '7') {
            options = `<option value="1">1</option>`;
        } else if (val === '4') {
            options =  `<option value="1">1</option>
                      <option value="2">2</option>
                      <option value="3">3</option>
                      <option value="4">4</option>
                      <option value="5">5</option>
                      <option value="6">6</option>
                      <option value="7">7</option>
                      <option value="8">8</option>
                      <option value="9">9</option>
                      <option value="10">10</option>
                      <option value="11">11</option>`;
        }
        obj.prev().html(options);
    } else {
        obj.prev().val("").prop("disabled", true);
    }
}
// creator: 李玉婷，2022-04-12 10:21:53，模式设置确定
function modeSettingSure(){
    let empty = 0;
    $("#modeSetting .dotItem:visible").each(function (){
        if ($(this).find(".fa-dot-circle-o").length === 0) empty++;
    });
    if (empty <= 0) {
        let headTip = ``;
        let setting = {};
        $("#modeSetting .dotItem:visible").each(function (){
            let idx = $(this).index();
            let val = $(this).find(".fa-dot-circle-o").data("type");
            if (idx === 0) {
                setting.relate = val;
            } else if (idx === 1) {
                setting.few = val;
            } else if (idx === 2) {
                setting.type = val;
            } else if (idx === 3) {
                setting.limit = val;
            }
        });
        let obj = $("#addService");
        obj.data("modeSetting", setting);
        let options = ``;
        if (setting.few === 1) {
            if (setting.relate === 1) {
                options = `
                     <option value="">请选择</option>
                     <option value="1">合同签订后</option>
                     <option value="2">服务开始前</option>
                     <option value="3">服务开始后</option>
                     <option value="4">交付前</option>
                     <option value="5">交付后</option>
                     <option value="10">通过验收后</option>
                     <option value="13">服务结束后</option>
                    `;
            } else {
                options = `
                     <option value="">请选择</option>
                     <option value="1">合同签订后</option>
                     <option value="2">服务开始前</option>
                     <option value="3">服务开始后</option>
                     <option value="13">服务结束后</option>
                    `;
            }
            obj.find(".oneTime").show().siblings().hide();
            obj.find(".oneTime select").html(options);
            headTip = `请录入服务费一次性收取，且收取与交付/验收${setting.relate === 1? '有': '无'}关的项目！`;
        } else {
            headTip = `请录入需分多次收费，且收费与交付/验收${setting.relate === 1? '有': '无'}关的项目！`;
            if (setting.relate === 1) {
                obj.find(".many_relate").show().siblings().hide();
                obj.find(".modeSetted").find(".lenTip:eq(0)").html("0/20");
                obj.find(".modeSetted").find(".lenTip:eq(1)").html("0/40");
            } else {
                obj.find(".many_noRelate").show().siblings().hide();
            }
            obj.find(".manyTime").show().siblings().hide();
            obj.find(".addOne").data("carry", setting.type);
            obj.find(".amountType").html(setting.type === 1?'金额<input name="amount" type="text" oninput="clearNoNumN(this, 2)"/>元':'比例<input name="amount" type="text" oninput="limitNumber(this)"/>%');
        }
        obj.find(".headTip").html(headTip);
        obj.find(".modeSetted").find("input").val("");
        obj.find(".modeSetted").find("select").val("");
        obj.find(".modeSetted").find(".parts").find(".price-box:gt(0)").remove();
        bounce_Fixed3.cancel();
        obj.find(".modeSetted").show().siblings().hide();
    }
}
function addServiceSure(){
    let empty = true, filed = 0;
    $("#addService [require]:visible").each(function(){
        let val = $(this).val();
        if (val === "") empty = false;
    });
    $("#addService .priceForm [need]").each(function(){
        let val = $(this).val();
        let name = $(this).attr("name");
        if (name === 'taxRate') {
            if ($(this).val() === "") {
                if ($("#addService input[name = 'unitPriceNotax']").val() !== "" || $("#addService input[name = 'unitPrice']").val() !== "")
                    empty = false;
            } else {
                if ($("#addService input[name = 'unitPriceNotax']").val() !== "" && $("#addService input[name = 'unitPrice']").val() !== "") {
                    filed++;
                } else {
                    empty = false;
                }
            }
        } else {
            if (val !== "") filed++;
        }
    });
    if ($("#addService .manyTime").is(":visible")) {
        $("#addService .price-box:visible").each(function (){
            let _this = $(this)
            let num = $(this).find("input:visible").length + $(this).find("select:visible").length;
            let filled = 0;
            _this.find("input:visible").each(function (){
                if ($(this).val() !== "") filled ++;
            })
            _this.find("select:visible").each(function (){
                if ($(this).val() !== "") filled ++;
            })
            if (filled > 0 && num !== filled) empty = false;
        })
    }
    if (empty && filed > 0) {
        let serviceChargeList = [];
        let cycle = $("#addService").data("cycle");//是否周期性 0-否 1-是
        let param = `isPeriodical=${cycle}`;
        $("#addService table input:visible").each(function(){
            let val = $(this).val();
            let name = $(this).attr("name");
            if (name === 'code' || name === 'name' || name === 'priceDesc') {
                param += `&${name}=${escape(encodeURIComponent(val))}`
            } else {
                param += `&${name}=${val}`
            }
        });
        $("#addService table select:visible").each(function(){
            let val = $(this).val();
            let name = $(this).attr("name");
            param += `&${name}=${val}`;
        });
        param += `&enabled=1&previousId=0&operation=1`;//1-增,2-删,3-改(基本信息),4-修改模式,8-启/复用,9-停用
        if (cycle === 1) {
            param += `&periodDuration=${$("#addService .byCycle select[name='periodDuration']").val()}`
            param += `&periodUnit=${$("#addService .byCycle select[name='periodUnit']").val()}`
            let item = {
                "chargeLimit": $("#addService .byCycle input[name='chargeLimit']").val(),
                "chargeStage": $("#addService .byCycle select[name='chargeStage']").val()
            }
            serviceChargeList.push(item);
        } else {
            let setting = $("#addService").data("modeSetting");
            let chargeNum = 0;
            if (setting) {
                param += `&deliveryAcceptable=${setting.relate}`;
                if (setting.few === 1){
                    chargeNum = 1;
                    let item = {
                        "chargeLimit": $("#addService .oneTime input[name='chargeLimit']").val(),
                        "chargeStage": $("#addService .oneTime select[name='chargeStage']").val()
                    }
                    serviceChargeList.push(item);
                } else {
                    let allow = 0;
                    chargeNum = 2;
                    param += `&enteringType=${setting.type}`;
                    if (setting.type === 1) param += `&upperType=${setting.limit}`;
                    $("#addService .manyTime .parts:visible").each(function (){
                        let index = $(this).index();
                        if (index === 0) {
                            let val = $(this).find("input[name='amount']").val();
                            if ( val !== "") {
                                let temp = {}
                                temp.chargeNum = 1;
                                temp.chargeType = 2;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                temp.chargeStage = $(this).find("select").val();
                                temp.chargeLimit = $(this).find("input[name='chargeLimit']").val();
                                allow += Number(val);
                                if (setting.type === 1) {
                                    temp.amount = val;
                                } else {
                                    temp.percentage = val;
                                }
                                serviceChargeList.push(temp);
                            }
                        } else if (index === 1) {
                            $(this).find(".price-box").each(function (){
                                let _this = $(this)
                                let val = _this.find("input[name='amount']").val();
                                if ( val !== "") {
                                    let temp = {}
                                    temp.chargeNum = 1;
                                    temp.chargeType = 4;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                    temp.chargeLimit = _this.find("input[name='chargeLimit']").val();
                                    allow += Number(val);
                                    if (setting.type === 1) {
                                        temp.amount = val;
                                    } else {
                                        temp.percentage = val;
                                    }
                                    if (setting.relate === 1) {
                                        temp.chargeStage = _this.find("select").val();
                                    } else {
                                        temp.chargeStage = 13;
                                    }
                                    serviceChargeList.push(temp);
                                }
                            })
                        } else if (index === 2) {
                            $(this).find(".price-box").each(function (){
                                let _this = $(this)
                                let val = _this.find("input[name='amount']").val();
                                if ( val !== "") {
                                    let temp = {}
                                    temp.chargeNum = 1;
                                    temp.chargeType = 3;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                    allow += Number(val);
                                    if (setting.relate === 1) {
                                        temp.chargeStage = _this.find("select").val();
                                        temp.chargeLimit = _this.find("input[name='chargeLimit']").val();
                                        temp.stageName = _this.find("input[name='stageName']").val();
                                        temp.stageDesc = _this.find("input[name='stageDesc']").val();
                                    }
                                    if (setting.type === 1) {
                                        temp.amount = val;
                                    } else {
                                        temp.percentage = val;
                                    }
                                    serviceChargeList.push(temp);
                                }
                            })
                        }
                    });
                    let amount = 100;
                    if (setting.type === 1) {
                        let price = ["","unitPriceNotax","unitPrice","unitPriceInvoice","unitPriceNoinvoice","unitPriceReference"]
                        amount = $("#addService input[name="+price[setting.limit]+"]").val();
                    }
                    if (allow !== Number(amount)){
                        layer.msg("<p>操作失败！</p><p>各期款项的比例或金额有误或没录全！</p>");
                        return false;
                    }
                    if (!empty){
                        layer.msg("<p>操作失败！</p><p>因为还有必填项尚未填写！</p>");
                        return false;
                    }
                }
            }
            param += `&chargeNum=${chargeNum}`;
        }
        var settings = {
            "url": "../saleService/add?" + param,
            "method": "POST",
            "timeout": 1000,
            "headers": {
                "Content-Type": "application/json"
            },
            "data": JSON.stringify(serviceChargeList),
            beforeSend:function(){ loading.open() ; }
        };
        $.ajax(settings).done(function(response){
            if (response === 2) {
                loading.close() ;
                layer.msg("新增失败，名称或者代码重复！");
            } else{
                bounce_Fixed2.cancel();
                getTemplate(1, 1);
            }
        })
    } else {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
    }
}
function invoiceSettings(curObj,selectedVal){
    var str = '<option value=""></option>';
    $.ajax({
        "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
        success: function (res) {
            var list = res["financeInvoiceSettings"];
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    if (list[i]["category"] == '1'){
                        var rate = list[i]["enableTaxTate"];
                        var rates = rate.split(",");
                        if (rates && rates.length >0){
                            for (var r = 0; r < rates.length; r++) {
                                var selectedStr = "";
                                if (selectedVal == rates[r]) {
                                    selectedStr = " selected='true' ";
                                }
                                str += "<option " + selectedStr + " value='" + rates[r] + "'>" + rates[r] + "%</option>";
                            }
                        }
                    }
                }
            }
            curObj.html(str);
        }
    });
}
// creator: 李玉婷，2020-08-10 11:08:33，服务项目-查看
function seeService(obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    $.ajax({
        url:"../saleService/detail",
        data: {
            id: info.id
        },
        success:function(data){
            let info = data.data;
            let serviceCharges = null;
            let headTip = ``;
            if (info.isPeriodical === 1) {
                serviceCharges = info.serviceCharges[0];
                $("#seeServiceByCycle [need]").each(function(){
                    let key = $(this).data("name");
                    if( key == 'taxRate' ) {
                        $(this).html(handleNull(info[key]) !== "" ? info[key]+'%':'');
                    } else {
                        $(this).html(info[key]);
                    }
                });
                headTip = `本项目按周期收费。`;
                $("#seeServiceByCycle .headTip").html(headTip);
                $("#seeServiceByCycle .seeCreater").html(info.createName +' ' +new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss'));
                $("#seeServiceByCycle .feeCycle").html(info.periodDuration + changeStr(info.periodUnit, 'periodUnit'));
                $("#seeServiceByCycle .timeCycle").html(changeStr(serviceCharges.chargeStage, 'chargeStage') + serviceCharges.chargeLimit);
                bounce_Fixed2.show($("#seeServiceByCycle"));
            } else {
                $("#seeServiceNoCycle [need]").each(function(){
                    let key = $(this).data("name");
                    if( key == 'taxRate') {
                        $(this).html(handleNull(info[key]) !== "" ? info[key]+'%':'');
                    } else {
                        $(this).html(info[key]);
                    }
                });
                $("#seeServiceNoCycle .seeCreater").html(info.createName + ' ' +new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss'));
                if (info.chargeNum === 1) {
                    serviceCharges = data.data.serviceCharges[0];
                    $("#seeServiceNoCycle .modeSettings").show().siblings().hide();
                    $("#seeServiceNoCycle .oneTime").show().siblings().hide();
                    $("#seeServiceNoCycle .timeCycle").html(changeStr(serviceCharges.chargeStage, 'chargeStage') + serviceCharges.chargeLimit);
                    headTip = `本项目服务费一次性收取，且收取与交付/验收${info.deliveryAcceptable === 1? '有':'无' }关！`;
                } else  if (info.chargeNum > 1){
                    let html = ``,middle = ``,final = ``, amountStr= 0;
                    let enteringTypeStr = ``, enteringTypeUnit=``;
                    serviceCharges = data.data.serviceCharges;
                    enteringTypeStr = info.enteringType === 1? '应收取金额':'应收取比例';
                    enteringTypeUnit = info.enteringType === 1? '元':'%';
                    amountStr = info.enteringType === 1? 'amount':'percentage';
                    headTip = `本项目需分多次收费，且收取与交付/验收${info.deliveryAcceptable === 1? '有':'无' }关！`;
                    $("#seeServiceNoCycle .modeSettings").show().siblings().hide();
                    $("#seeServiceNoCycle .oneTime").hide().siblings().show();
                    for (var i=0;i<serviceCharges.length;i++){
                        if (serviceCharges[i].chargeType === 2){
                            html +=
                                `<div class="clear">
                                        <div class="ty-left">
                                            <span class="sm-ttl">预付款</span>
                                        </div>
                                        <div class="ty-right">
                                            <span>${enteringTypeStr}：<span class="sm-con">${serviceCharges[i][amountStr]}${enteringTypeUnit}</span></span>
                                            <span class="rws-con">应收取时间：<span>${changeStr(serviceCharges[i].chargeStage, 'chargeStage')}  ${serviceCharges[i].chargeLimit}天内</span></span>
                                        </div>
                                    </div>`;
                        } else if (serviceCharges[i].chargeType === 3){
                            if (info.deliveryAcceptable === 1) {//有关
                                middle +=
                                    `<div class="clear">
                                        <div class="ty-left">
                                            <span class="sm-ttl">中间款项</span>
                                            ${serviceCharges[i].stageName}
                                        </div>
                                        <div class="ty-right">
                                            <span>${enteringTypeStr}：<span class="sm-con">${serviceCharges[i][amountStr]}${enteringTypeUnit}</span></span>
                                            <span class="rws-con">应收取时间：<span>${changeStr(serviceCharges[i].chargeStage, 'chargeStage')}  ${serviceCharges[i].chargeLimit}天内</span></span>
                                        </div>
                                    </div>
                                    <div class="des-box">${serviceCharges[i].stageDesc}</div>`;
                            } else {
                                middle +=
                                    `<div class="clear">
                                        <div class="ty-left">
                                            <span class="sm-ttl">中间款项</span>
                                        </div>
                                        <div class="ty-right">
                                            <span>${enteringTypeStr}：<span class="sm-con">${serviceCharges[i][amountStr]}${enteringTypeUnit}</span></span>
                                            <span class="rws-con"></span>
                                        </div>
                                    </div>`;
                            }
                        } else if (serviceCharges[i].chargeType === 4){
                            final +=
                                `<div class="clear">
                                        <div class="ty-left">
                                            <span class="sm-ttl">尾款</span>
                                        </div>
                                        <div class="ty-right">
                                            <span>${enteringTypeStr}：<span class="sm-con">${serviceCharges[i][amountStr]}${enteringTypeUnit}</span></span>
                                            <span class="rws-con">应收取时间：<span>
                                                  ${info.deliveryAcceptable === 0? '服务结束后': changeStr(serviceCharges[i].chargeStage, 'chargeStage')}
                                            ${serviceCharges[i].chargeLimit}天内</span></span>
                                        </div>
                                    </div>`;
                        }
                    }
                    $("#seeServiceNoCycle .manyTime").html(html+ middle +final);
                } else {
                    headTip = `本项目不按周期收费。`;
                    $("#seeServiceNoCycle .modeSettings").hide();
                }
                $("#seeServiceNoCycle .headTip").html(headTip);
                bounce_Fixed2.show($("#seeServiceNoCycle"));
            }
        }
    })
}
/*creator:lyt 2022/10/25 */
function addOne(obj){
    if (obj.parents(".parts").find(".price-box").length < 3) {
        let carry = obj.data("carry");
        let wStr = ` <div class="price-box">
                                    <div class="delSite">
                                            <span class="gapRt0">
                                                 ${carry === 1? "金额":"比例"}<input need name="amount" type="text" oninput="${carry === 1? 'clearNoNumN(this, 2)':'limitNumber(this)'}"/>${carry === 1? "元":"%"}
                                            </span>
                                            收费时间
                                            <select class="entry">
                                                <option value=""></option>
                                                 <option value="8">最终交付前</option>
                                                <option value="9">最终交付后</option>
                                                <option value="12">最终验收通过后</option>
                                                <option value="13">服务结束后</option>
                                            </select>
                                            <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            <span class="funBtn del-btn" data-fun="delPrev">删除</span>
                                     </div>
                                        </div>`;
        obj.parents(".parts").append(wStr);
    } else {
        layer.msg("尾款最多分三期");
    }
}
function no_addOne(obj){
    if (obj.parents(".parts").find(".price-box").length < 3) {
        let carry = obj.data("carry");
        let oStr = ` <div class="price-box">
                                <div class="delSite">
                                <span class="gapRt0">
                                     ${carry === 1 ? "金额" : "比例"}<input need name="amount" type="text" oninput="${carry === 1 ? 'clearNoNumN(this, 2)' : 'limitNumber(this)'}" />${carry === 1 ? "元" : "%"}
                                </span>
                                收费时间为服务结束后
                                     <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                     <span class="funBtn del-btn" data-fun="delPrev">删除</span>
                                     </div>
                                </div>`;
        obj.parents(".parts").append(oStr);
    } else {
        layer.msg("尾款最多分三期");
    }
}
function addMore(obj){
    let carry = obj.data("carry");
    let zStr = `<div class="price-box">
                                            <div>本期中间款项的收取信息</div>
                                            <div class="delSite">
                                            <span class="gapRt0">
                                                  ${carry === 1? "金额":"比例"}<input need name="amount" type="text" oninput="${carry === 1? 'clearNoNumN(this, 2)':'limitNumber(this)'}"/>${carry === 1? "元":"%"}
                                            </span>
                                            收费时间
                                            <select class="entry">
                                                <option value=""></option>
                                                 <option value="6">本期交付前</option>
                                                <option value="7">本期交付后</option>
                                                <option value="11">本期验收通过后</option>
                                            </select>
                                            <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            <span class="funBtn del-btn" data-fun="delPrev">删除</span>
                                            </div>
                                            <div class="midPay">
                                                <div class="clear">
                                                    <div><span class="gapRt0">交付/验收环节的名称</span>
                                                    <input class="middleSize" name="stageName" type="text" maxlength="20" onkeyup="limitWord($(this), 20)"/></div>
                                                    <div class="lenTip ty-right" style="clear: both">0/20</div>
                                                </div>
                                                <div class="clear">
                                                    <div><span class="gapRt0">交付/验收的内容描述</span>
                                                    <input class="middleSize" name="stageDesc" type="text" maxlength="40" onkeyup="limitWord($(this), 40)"/></div>
                                                    <div class="lenTip ty-right" style="clear: both">0/40</div>
                                                </div>
                                            </div>
                                        </div>`;
    obj.parents(".parts").append(zStr);
}
function no_addMore(obj){
    let carry = obj.data("carry");
    let mStr = `<div class="price-box">
                  <div class="delSite">
                    <div>本期中间款项的收取信息</div>
                    <span class="gapRt0">
                        ${carry === 1? "金额":"比例"}<input need name="amount" type="text" oninput="${carry === 1? 'clearNoNumN(this, 2)':'limitNumber(this)'}"/>${carry === 1? "元":"%"}
                    </span>
                  </div>
                </div>`;
    obj.parents(".parts").append(mStr);
}
function delPrev(obj){obj.parents(".price-box").remove();}
function modeSetting(){
    let empty = true, filed = 0;
    $("#addService [require]:visible").each(function(){
        let val = $(this).val();
        if (val === "") empty = false;
    });
    $(".priceForm [need]").each(function(){
        let val = $(this).val();
        if (val !== "") filed++;
    });
    if (empty && filed > 0) {
        $("#modeSetting .changeDot i").attr("class", "fa fa-circle-o");
        $("#modeSetting .dotItem:gt(1)").hide();
        bounce_Fixed3.show($("#modeSetting"));
    } else {
        layer.msg("<p>操作失败！</p><p>本项目基础信息录完后，才能开启设置！</p>");
    }
}
function reModeSetting(){
    bounce_Fixed4.show($("#reModTip"));
}
/*creator:lyt 2022/8/25 下午 5:57 重新设置模式确定*/
function reModSure(){
    $("#addService").removeData("modeSetting").find(".modeNoSetted").show().siblings().hide();
    bounce_Fixed4.cancel();
}
// creator: hxz，2022-08-22 08:57:55， 新增套餐
function addServiceMeal() {
    bounce_Fixed2.show($("#mealInitChoose"));
    $("#mealInitChoose .fa").attr("class", "fa fa-circle-o");
}
// creator: hxz，2022-08-22 08:57:55， 新增套餐 - 第一步确定
function mealInitChoose() {
    let selectFa = $("#mealInitChoose").find(".fa-dot-circle-o");
    let data = {}
    if(selectFa.length == 2){
        selectFa.each(function(){
            let type = $(this).data("type");
            let val = $(this).data("val");
            data[type] = val;
        })
        $("#addMealService").data("init", data);
        $("#addMealService input").val("").prop("disabled", false);
        $("#addMealService select").val("");
        invoiceSettings($("#taxRate"), "");
        bounce_Fixed2.show($("#addMealService"))
        $("#addMealService .prijectOrGoods").hide();
        $("#goodsTab tbody tr:gt(0):not(.guDing)").remove();
        $("#goodsTab2 tbody tr:gt(0):not(.guDing)").remove();
        $("#serviceTab tbody tr:gt(0):not(.guDing)").remove();

        if(data.isgoods == 1){
            $("#addGoodsBtn").show();
        }else{
            $("#addGoodsBtn").hide();
        }
        if(data.isdur == 1){
            $("#periodDurationInfo").show();
            $("#periodDuration").attr("disabled","true").val("");
            $("#periodDuration").val("")
        }else {
            $("#periodDurationInfo").hide();
        }
    }else{
        layer.msg("请将选项补充完整")
    }
}
// creator: hxz，2022-08-22 08:57:55， 向套餐内添加商品
function addGoods(obj) {
    let where = obj.data("where")
    $("#addGoods1 .fa-dot-circle-o").attr("class", "fa fa-circle-o");
    $("#addGoods1 .fuBefore2").hide();
    bounce_Fixed3.show($("#addGoods1"));
    $("#addGoods1").data("where", where);
}
// creator: hxz，2022-08-22 08:57:55， 向套餐内添加商品 - 第一步确定
function addGoodsSure() {
    let select = $("#addGoods1 .fa-dot-circle-o");
    let init = {}
    if(select.length == 3){
        select.each(function () {
            let type = $(this).data("type");
            let val = $(this).data("val");
            init[type] = val;
        })
        if(init.fuBefore == 2){
            init.dayNum = $("#fuBeforeNum").val();
            if(init.dayNum.length == 0){
                layer.msg("请输入天数！");
                return false
            }
        }
        $("#addgTab1 tbody tr:gt(0)").remove();
        $("#addGoods2").data("init", init);
        bounce_Fixed3.show($("#addGoods2"))
        if(init.whereSet == 1 && init.fuBefore == 1){
            $("#sendTtl").show()
        }else{
            $("#sendTtl").hide()
        }
        $.ajax({
            "url":"../service/getProductList.do",
            "data":{ "serviceId": "" },
            success:function(data){
                let res = data.data
                let list = res.list || [];

                let str = ""
                list.forEach((item)=>{
                    str=  `
                    <tr>
                        <td><i class="fa fa-square-o"></i><span class="hd">${ JSON.stringify(item) }</span></td>
                        <td>${ item.outerSn } / ${ item.outerName }</td>
                        <td>${ item.unit }</td>
                        <td>${ item.unitPriceReference || ""}</td>
                        ${ init.whereSet == 1 && init.fuBefore == 1 ? `<td class="bgGray changeTd"><input type="text" readonly placeholder="请选择"></td>` : '' } 
                        <td class="bgGray changeTd"></td>
                    </tr>
                    `
                    $("#addgTab1 tbody").append(str);
                })
            }
        })
    } else {
        layer.msg("请设置好套餐内商品的情况")
    }
}
// creator: hxz，2022-08-22 08:57:55， 向套餐内添加商品 - 第二步确定
function addProductSure() {
    let selectFa = $("#addgTab1 .fa-check-square-o");
    let init = $("#addGoods2").data("init");
    let nullNum = 0
    let sumAll = 0
    let str = ``
    selectFa.each(function () {
        let info = $(this).siblings(".hd").html()
        info = JSON.parse(info);
        let trO = $(this).parents("tr")
        info.init = init
        info.goodsNum = trO.find("input.gn").val();
        let isOk = true
        if(init.whereSet == 1 && init.fuBefore == 1){
            let timeO = trO.find(".time")
            info.sendDianStr  = timeO.val()
            info.sendDian = timeO.data("sendDian")
            info.sebdDDay = timeO.data("sebdDDay")
            if(!info.sendDian || !info.sebdDDay || !info.goodsNum){
                isOk = false
            }
        }else if(!info.goodsNum){
            isOk = false;
        }
        if(!isOk){
            nullNum++;
        }
        else {
            let unitprice = info.unitPriceReference || 0;

            info.sum = Number(info.goodsNum) * Number(unitprice)
            sumAll += info.sum
            str += `
                 <tr class="goodsTr">
                      <td>${ info.outerSn } / ${ info.outerName }</td>
                      <td>${ unitprice }</td>
                      <td>${ info.sendDianStr || '' }</td>
                      <td>${ info.goodsNum }</td>
                      <td>${ info.sum }</td>
                      <td>
                         <span data-fun="scanGoods" class="ty-color-blue funbtn">查看</span>
                         <span data-fun="editNumGoods" class="ty-color-blue funbtn">修改</span>
                         <span data-fun="delGoods" class="ty-color-blue funbtn">删除</span>
                         <span class="hd">${ JSON.stringify(info) }</span>
                      </td>
                 </tr>
                `
        }
    })
    if(nullNum > 0){
        layer.msg("请将内容补充完整！")
        return false;
    }else{
        let where = $("#addGoods1").data("where");
        let tableO = null
        if(where == "edit"){ // 修改的
            if(init.durSet == 2){
                $("#e_tab2 tbody tr.guDing").before(str);
                $("#eTab2Sum").html(sumAll)
                $("#editService .s_tbc_2").show();
                tableO = $("#e_tab2")
            }else if(init.durSet == 1){
                $("#e_tab3 tbody tr.guDing").before(str);
                $("#eTab3Sum").html(sumAll)
                $("#editService .s_tbc_3").show();
                tableO = $("#e_tab3")
            }
        }else{ // 新增
            if(init.durSet == 2){
                $("#goodsTab tbody tr.guDing").before(str);
                $("#sumAll2").html(sumAll)
                $("#addMealService .prijectOrGoods2").show();
                tableO = $("#goodsTab")
            }else if(init.durSet == 1){
                $("#goodsTab2 tbody tr.guDing").before(str);
                $("#sumAll3").html(sumAll)
                $("#addMealService .prijectOrGoods3").show();
                tableO = $("#goodsTab2")
            }
        }

        resetSumAll(tableO)
        bounce_Fixed3.cancel()
    }
}
// creator: hxz，2022-08-22 08:57:55， 新增套餐 确定
function addMealServiceSure(obj) {
    let init = $("#addMealService").data("init");
    let sendData = {
        name: $("#name_sv").val(), // 简称
        code: $("#code_sv").val(), // 代码
        memo:$("#desc_sv").val(), // 说明
        isPeriodical: init.isdur, // 是否周期性 1是 0否|
        isMixture: init.isgoods, // 是否混合:项目+商品 1是 0否
        periodDuration: $("#periodDuration").val(), // 周期数
        periodUnit: $("#periodUnit").val(), // 周期单位:1-日,2-周,3-旬,4-月,5-季,6-半年,7-年
        chargeNum: "", // 收费次数
        chargeStage: $("#chargeStage").val(), // 收费时间类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,11-本期通过验收后,12-最终通过验收后
        chargeLimit: $("#chargeLimit").val(), // 收费时限(n天内)
        taxRate: $("#taxRate").val(), // 税率
        unitPrice: $("#unitPrice").val(), // 含税单价(增专)
        unitPriceNotax: $("#unitPriceNotax").val(), // 不含税单价(增专
        unitPriceInvoice: $("#unitPriceInvoice").val(), // 开票单价(增普)
        unitPriceNoinvoice: $("#unitPriceNoinvoice").val(), // 不开票单价
        unitPriceReference: $("#unitPriceReference").val(), // 参考价格
        priceDesc: $("#priceDesc").val() , // 价格描述
        enteringType: "", // 录入类型:1-金额,2-比例
        upperType: "", // 上限类型:1-专票不含税价,2-专票含税价,3-普票价,4-不开票价,5-参考单价
        productListJson: [], // 商品 字符串集合
        projectListJson: [], // 项目 字符串集合
    }
    let nullNum = 0
    let priceNum = 0
    for(let key in sendData){
        let val = sendData[key]
        let reqireKey = "name,codeName,chargeStage,chargeLimit,";
        let priceKey = "unitPriceNotax,unitPriceInvoice,unitPriceReference,unitPrice,contactsList,";
        if(init.isdur == 1){
            reqireKey += "periodDuration,periodUnit,"
        }
        if(reqireKey.indexOf(key) > -1 && val.length == 0){
            nullNum++
        }else if(priceKey.indexOf(key) > -1 && val.length > 0){
            priceNum++
        }
    }
    if(priceNum == 0){
        nullNum++
    }
    if(nullNum > 0){
        layer.msg("请将内容补充完整！");
        return false;
    }
    $("#serviceTab tbody tr.projectTr").each(function () {
        let info = $(this).find(".hd").html();
        info = JSON.parse(info);
        let index = $(this).index()
        // let sendPro = {
        //     "orders": info.orders,
        //     "codeName": info.code,//代码
        //     "serviceItem": info.id,//服务项目ID
        //     "itemQuantity": info.proNum,//项目数量
        //     "periodQuantity": info.zhouNum,//周期数量
        //     "deliveryTerm": info.orders,//供货方式:1-每个收费周期都需提供,2-仅第一个收费周期提供
        //     "deliveryPlace": info.orders,//供货地点1-存放于本机构2-需交到客户手中
        //     "deliverySequence": info.orders,//供货顺序1-不需要2-需要先付套餐后提供商品
        //     "deliveryDuration": info.orders,//客户付款后,商品提供给客户需要时长(天)
        //     "deliveryQuantity": info.orders,//货物数量
        //     "amount": info.orders,//金额
        //     "chargeNum": info.orders,//收费次数(第n次)
        //     "deliveryStage": info.orders,//发货时间要求类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,11-本期通过验收后,12-最终通过验收后
        //     "deliveryNum": "", // 交付次数(第n次)
        //     "deliveryLimit": "", // 发货时间要求时限(n天内)
        //     "memo": "", // 备注
        // }
        sendData.projectListJson.push({
            "orders": index,
            "codeName": info.code,//代码
            "serviceItem": info.id,//服务项目ID
            "itemQuantity": info.proNum,//项目数量
            "periodQuantity": info.zhouNum,//周期数量
            "amount": info.unitPriceReference || 0 ,//金额
            "memo": info.memo, // 备注
        });

    })
    $("#goodsTab tbody tr.goodsTr").each(function () {
        let info = $(this).find(".hd").html();
        info = JSON.parse(info);
        let index = $(this).index()
        sendData.productListJson.push({
            "orders": index,
            "codeName": info.outerSn,//代码
            "serviceItem": info.id,//服务项目ID
            // "itemQuantity": info.proNum,//项目数量
            // "periodQuantity": info.zhouNum,//周期数量
            "deliveryTerm": info.init.durSet ,//供货方式:1-每个收费周期都需提供,2-仅第一个收费周期提供
            "deliveryPlace": info.init.whereSet ,//供货地点1-存放于本机构2-需交到客户手中
            "deliverySequence": info.init.fuBefore ,//供货顺序1-不需要2-需要先付套餐后提供商品
            "deliveryDuration": info.init.dayNum,//客户付款后,商品提供给客户需要时长(天)
            "deliveryQuantity": info.goodsNum,//货物数量
            "amount": info.sum,  //金额
            // "chargeNum": info.orders,  //收费次数(第n次)
            "deliveryStage": info.sendDian, //发货时间要求类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,11-本期通过验收后,12-最终通过验收后
            // "deliveryNum": "", // 交付次数(第n次)
            "deliveryLimit": info.sebdDDay, // 发货时间要求时限(n天内)
            // "memo": "", // 备注
        });

    })
    $("#goodsTab2 tbody tr.goodsTr").each(function () {
        let info = $(this).find(".hd").html();
        info = JSON.parse(info);
        let index = $(this).index()
        sendData.productListJson.push({
            "orders": index ,
            "codeName": info.outerSn,//代码
            "serviceItem": info.id,//服务项目ID
            // "itemQuantity": info.proNum,//项目数量
            // "periodQuantity": info.zhouNum,//周期数量
            "deliveryTerm": info.init.durSet ,//供货方式:1-每个收费周期都需提供,2-仅第一个收费周期提供
            "deliveryPlace": info.init.whereSet ,//供货地点1-存放于本机构2-需交到客户手中
            "deliverySequence": info.init.fuBefore ,//供货顺序1-不需要2-需要先付套餐后提供商品
            "deliveryDuration": info.init.dayNum,//客户付款后,商品提供给客户需要时长(天)
            "deliveryQuantity": info.goodsNum,//货物数量
            "amount": info.sum,  //金额
            // "chargeNum": info.orders,  //收费次数(第n次)
            "deliveryStage": info.sendDian, //发货时间要求类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,11-本期通过验收后,12-最终通过验收后
            // "deliveryNum": "", // 交付次数(第n次)
            "deliveryLimit": info.sebdDDay, // 发货时间要求时限(n天内)
            // "memo": "", // 备注
        });

    })
    sendData.productListJson = JSON.stringify(sendData.productListJson)
    sendData.projectListJson = JSON.stringify(sendData.projectListJson)
    $.ajax({
        "url":"../service/addServicePackage.do",
        "data":sendData,
        success:function(res){
            let success = res.success
            if(success == 1){
                layer.msg("新增成功！")
                //let cur = $("#ye1 .yecur").html()
                //let json = $("#ye1 .json").html()
                //json = JSON.parse(json);
                //let key = json.key || ''
                bounce_Fixed2.cancel();
                getTemplate(1, 2);
            }else{
                layer.msg("新增失败！")
            }

        }
    })

}
// creator: hxz，2022-08-22 08:57:55，  查看服务套餐
var editObjTr = null
function service_scan(obj) {
    editObjTr = obj
    bounce_Fixed2.show($("#serviceScan"));
    let trInfo = obj.siblings(".hd").html()
    trInfo = JSON.parse(trInfo);
    let serviceId = trInfo.id
    $.ajax({
        "url":"../service/getServiceDetail.do",
        "data":{ "serviceId": serviceId },
        success:function (res) {
            let data = res.data
            //$("#serviceScan").data("data", data);
            $("#cre1").html(`创建：${ data.createName } ${ new Date(data.createDate).format("yyyy-MM-dd hh:mm:ss") }`);
            $("#re1").html(data.isPeriodical == 1 ? '本套餐按周期收费。' : '本套餐不按周期收费。')
            $("#s_code").html(data.code)
            $("#s_name").html(data.name)
            $("#s_desc").html(data.memo)
            $("#s_rate").html(handleNull(data.taxRate) !== "" ? data.taxRate+'%':'')
            $("#s_price1").html(data.unitPriceNotax || '')
            $("#s_price2").html(data.unitPrice || '')
            $("#s_price3").html(data.unitPriceInvoice || '')
            $("#s_price4").html(data.unitPriceNoinvoice|| '')
            $("#s_price5").html(data.unitPriceReference || '')
            $("#s_priceDesc").html(data.priceDesc || '')
            if(data.isPeriodical == 1){
                $("#s_feeZhou").html(`${ formatDur( data, "Duration") }`)
                $("#s_feeZhouC").show()
            }else{
                $("#s_feeZhouC").hide()
            }
            $("#s_feeTime").html(`${ changeStr(data.chargeStage, 'chargeStage') }` + data.chargeLimit +'天内')

            let projectList = data.projectList || []
            let str_projectList = ``// 项目str
            let sum1 = 0
            projectList.forEach((item)=>{
                sum1 += Number( item.amount )
                str_projectList += `
                    <tr>
                        <td>${ item.code }/${ item.name }</td>
                        <td>${ item.unitPriceReference || ""  }</td>
                        <td>${ item.itemQuantity }</td>
                        <td>${ item.amount }</td>
                    </tr>
                `
            })
            $("#s_tab1 tbody tr:gt(0):not(.guDing)").remove();
            $("#s_tab1 tbody tr.guDing").before(str_projectList);
            $("#sTab1Sum").html(sum1)
            let productList = data.productList || [];
            let str_productList2 = ``// 商品str 2
            let str_productList3 = ``// 商品str 3
            let sum2 = 0
            let sum3 = 0
            productList.forEach((item2)=>{
                let deliveryTerm = item2.deliveryTerm;
                if(deliveryTerm == 1){ // 每个收费周期都需提供
                    sum3 += Number( item2.amount )
                    str_productList3 += `
                    <tr>
                        <td>${ item2.code }/${ item2.name }</td>
                        <td>${ item2.unitPriceReference || ""  }</td>
                        <td>${ formatChargeStage(item2.deliveryStage , item2.deliveryLimit)}</td>
                        <td>${ item2.deliveryQuantity }</td>
                        <td>${ item2.amount }</td>
                    </tr>
                    `
                }else if(deliveryTerm == 2){ // 仅第一个收费周期提供
                    sum2 += Number( item2.amount )
                    str_productList2 += `
                    <tr>
                        <td>${ item2.code }/${ item2.name }</td>
                        <td>${ item2.unitPriceReference || ""  }</td>
                        <td>${ formatChargeStage(item2.deliveryStage,item2.deliveryLimit)}</td>
                        <td>${ item2.deliveryQuantity }</td>
                        <td>${ item2.amount }</td>
                    </tr>
                    `
                }
            })
            $("#s_tab2 tbody tr:gt(0):not(.guDing)").remove();
            $("#s_tab2 tbody tr.guDing").before(str_productList2);
            $("#sTab2Sum").html(sum2)

            $("#s_tab3 tbody tr:gt(0):not(.guDing)").remove();
            $("#s_tab3 tbody tr.guDing").before(str_productList3);
            $("#sTab3Sum").html(sum3)

            if(str_projectList.length == 0){
                $("#serviceScan .s_tbc_1").hide()
            }else{
                $("#serviceScan .s_tbc_1").show()
            }
            if(str_productList2.length == 0){
                $("#serviceScan .s_tbc_2").hide()
            }else{
                $("#serviceScan .s_tbc_2").show()
            }
            if(str_productList3.length == 0){
                $("#serviceScan .s_tbc_3").hide()
            }else{
                $("#serviceScan .s_tbc_3").show()
            }
        }
    })
}
// creator: hxz，2022-08-22 08:57:55，向套餐内添加商品 - 查看商品
function scanGoods(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info)//供货地点1-存放于本机构2-需交到客户手中
    let str = `本商品${ info.init.durSet == 2 ? '仅第一个收费周期提供' : '每个收费周期都需提供' } ，
        ${ info.init.whereSet == 2 ? '需交给客户' : '需存放于本机构' }，
        向客户提供时${ info.init.fuBefore == 1 ? '不' : '' }需要先付套餐的款
        ${ info.init.fuBefore == 1 ? ' 。' : `, 客户付款后，商品提供给客户需要${ info.init.dayNum }天` }
    `
    $("#scanGoodsTip").html(str)
    bounce_Fixed3.show($("#scanGoods"))
}
// creator: hxz，2022-08-22 08:57:55，向套餐内添加商品 - 修改商品
function editNumGoods(obj) {
    editObj = obj
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info)
    $("#editNumGoods .oldNum").html(info.goodsNum)
    $("#editNumGoods .newNum").val("")

    let init = info.init;
    if(init.whereSet == 1 && init.fuBefore == 1){
        $("#editNumGoods .oldSendD").html(info.sendDianStr)
        $("#editNumGoods .newSendD").val("")
        $("#editNumGoods .sendD").show()
    }else{
        $("#editNumGoods .sendD").hide()
    }
    bounce_Fixed3.show($("#editNumGoods"))

}
function editNumGoodsSure() {
    let info = editObj.siblings(".hd").html();
    info = JSON.parse(info)
    info.goodsNum = $("#editNumGoods .newNum").val()
    let init = info.init;
    if(init.whereSet == 1 && init.fuBefore == 1){
        info.sendDianStr = $("#editNumGoods .newSendD").val()
        info.sendDian = $("#editNumGoods .newSendD").data("sendDian")
        info.sebdDDay = $("#editNumGoods .newSendD").data("sebdDDay")
    }
    let unitprice = info.unitPriceReference || 0;
    info.sum = Number(info.goodsNum) * Number(unitprice)
    let str = `
              <td>${ info.outerSn } / ${ info.outerName }</td>
              <td>${ unitprice }</td>
              <td>${ info.sendDianStr || '' }</td>
              <td>${ info.goodsNum }</td>
              <td>${ info.sum }</td>
              <td>
                 <span data-fun="scanGoods" class="ty-color-blue funbtn">查看</span>
                 <span data-fun="editNumGoods" class="ty-color-blue funbtn">修改</span>
                 <span data-fun="delGoods" class="ty-color-blue funbtn">删除</span>
                 <span class="hd">${ JSON.stringify(info) }</span>
              </td>
            `
    let tabO = editObj.parents("table")
    editObj.parents("tr").html(str)
    bounce_Fixed3.cancel()
    resetSumAll(tabO);
}
// creator: hxz，2022-08-22 08:57:55，向套餐内添加商品 - 删除商品
function delGoods(obj) {
    delPro(obj)
}
// creator: hxz，2022-08-22 08:57:55，向套餐内添加商品 - 选择发货时间
var editTimeObj = null
function inputTime(obj) {
    editTimeObj = obj;
    bounce_Fixed4.show($("#inputTime"));
    $("#sendDian").val("")
    $("#sebdDDay").val("")
}
// creator: hxz，2022-08-22 08:57:55， 向套餐内添加商品 - 选择发货时间 确定
function inputTimeOk() {
    let sendDian = $("#sendDian").val();
    let sendDianStr = $("#sendDian option:selected").html();
    let sebdDDay = $("#sebdDDay").val();
    if(sendDian.length > 0 && sebdDDay.length > 0){
        editTimeObj.val(`${sendDianStr}${sebdDDay}日内`).data("sendDian", sendDian).data("sebdDDay", sebdDDay);
        bounce_Fixed4.cancel()
    }else{
        layer.msg("请把内容补充完整！")
    }
}
// creator: hxz，2022-08-22 08:57:55， 向套餐内添加项目
function addProject(obj) {
    let where = obj.data("where")
    $("#addpTab1").data('where', where);

    $("#addpTab1 tbody tr:gt(0)").remove();
    $.ajax({
        "url":"../service/getProjectList.do",
        "data": { "serviceId": "" },
        success:function (data) {
            let res = data.data
            let list = res.list || [] ;
            let str = ""
            list.forEach((item)=>{
                str+=  `
                    <tr>
                        <td><i class="fa fa-square-o"></i><span class="hd">${ JSON.stringify(item) }</span></td>
                        <td>${ item.code } / ${ item.name }</td>
                        <td>${ item.unit || "" }</td>
                        <td>${ item.unitPriceReference || "" }</td>
                        <td class="bgGray changeTd zhouNum"></td>
                        <td class="bgGray changeTd proNum"></td>
                    </tr>
                `
            })
            $("#addpTab1 tbody").append(str);
            bounce_Fixed3.show($("#addProject"));

        }

    })
}
// creator: hxz，2022-08-22 08:57:55， 向套餐内添加项目 确定
function addProjectSure() {
    let selectFa = $("#addProject .fa-check-square-o");
    let init = $("#addMealService").data("init");
    let isdur = init.isdur
    let str = ``
    let sumAll = 0
    let nullNum = 0
    if(selectFa.length > 0){
        selectFa.each(function () {
            let info = $(this).siblings(".hd").html();
            info = JSON.parse(info)
            if(isdur == 1){
                info.zhouNum = $(this).parent("td").siblings(".zhouNum").children("input").val()
            }else{
                info.zhouNum = "1"
            }
            info.proNum = $(this).parent("td").siblings(".proNum").children("input").val()
            if( info.zhouNum.length == 0 || info.proNum.length == 0 ){
                nullNum++;
            } else {
                let unitprice =  Number(info.unitPriceReference || 0)
                let sum = info.proNum * info.zhouNum * unitprice
                info.sum = sum && sum.toFixed(2);
                sumAll += Number(info.sum);
                info.productOrProject = "project"
                info.orders = $("#serviceTab tbody tr.productOrProject").length
                str += `
                 <tr class="projectTr">
                      <td>${ info.code } / ${ info.name }</td>
                      <td>${ unitprice }</td>
                      <td>${ info.proNum } * ${ info.zhouNum }</td>
                      <td>${ info.sum }</td>
                      <td>
                         <span data-fun="editNumPro" class="ty-color-blue funbtn">修改数量</span>
                         <span data-fun="delPro" class="ty-color-blue funbtn">删除</span>
                         <span class="hd">${ JSON.stringify(info) }</span>
                      </td>
                 </tr>
                `
            }
        })
        if(nullNum>0){
            layer.msg("请把内容补充完整！")
        }
        else{
            $("#serviceTab tbody tr.guDing").before(str);
            resetSumAll($("#serviceTab"));
            $("#addMealService .prijectOrGoods1").show();
            bounce_Fixed3.cancel()
        }
    }else {
        layer.msg("请先选择好项目！")
    }
}
// creator: hxz，2022-08-31 08:57:58 修改项目数量
var editObj = null
function editNumPro(obj) {
    editObj = obj;
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    bounce_Fixed3.show($("#editNumPro"))
    $("#durNum").val(info.zhouNum);
    $("#proNum").val(info.proNum);
    let init = $("#addMealService").data("init");
    let isdur = init.isdur
    if(isdur == 1){
        $("#editNumPro .zhouP").show();
    }else{
        $("#editNumPro .zhouP").hide();
    }
}
function editNumProSure() {
    let info = editObj.siblings(".hd").html();
    info = JSON.parse(info);
    let init = $("#addMealService").data("init");
    let isdur = init.isdur
    if(isdur == 1){
        info.zhouNum = $("#durNum").val();
    }else{
        info.zhouNum = 1;
    }
    info.proNum = $("#proNum").val();
    if( info.zhouNum.length == 0 || info.proNum.length == 0 ){
        layer.msg("请将内容补充完整");
        return false;
    }
    let unitprice =  Number(info.unitPriceReference || 0)
    let sum = info.proNum * info.zhouNum * unitprice
    info.sum = sum && sum.toFixed(2);
    let str = `
              <td>${ info.code } / ${ info.name }</td>
              <td>${ unitprice }</td>
              <td>${ info.proNum } * ${ info.zhouNum }</td>
              <td>${ info.sum }</td>
              <td>
                 <span data-fun="editNumPro" class="ty-color-blue funbtn">修改数量</span>
                 <span data-fun="delPro" class="ty-color-blue funbtn">删除</span>
                 <span class="hd">${ JSON.stringify(info) }</span>
              </td>
            `
    editObj.parents("tr").html(str);
    bounce_Fixed3.cancel();
}
// creator: hxz，2022-08-31 08:57:55，  删除项目
function delPro(obj) {
    editObj = obj;
    bounce_Fixed3.show($("#delProTip"));
}
function delProSure() {
    let info = editObj.siblings(".hd").html();
    info = JSON.parse(info);
    let tableO = editObj.parents("table")
    editObj.parents("tr").remove();
    resetSumAll(tableO)
    bounce_Fixed3.cancel();
}
function resetSumAll(tableO){
    let trList = tableO.find("tbody tr:gt(0):not('.guDing')")
    if(trList.length == 0){
        tableO.parents(".prijectOrGoods").hide()
    }else{
        let sumAll = 0
        trList.each(function () {
            let i_info = $(this).find(".hd").html()
            i_info = JSON.parse(i_info)
            sumAll += Number(i_info.sum);
        })
        tableO.find(".guDing .sumAll").html(sumAll.toFixed(2))
    }
}
// creator: hxz，2022-08-22 08:57:55， 向套餐内添加项目 - 操作说明
function operatInstructions() {
    bounce_Fixed4.show($("#operatInstructions"))
}
// creator: 李玉婷，2020-04-14 19:14:45，模板中选择完毕-确定
function templateSetOk(source){
    let icon = 1;
    let curObj = $("#proTemplate");
    let invoiceType = $(".invoiceRequire").data("invoice")
    let partCur =  $("#newServiceContract .s_contact1");
    $(".editPrice").hide();
    $(".priceSet i.xing").hide();
    if (source === 2) {curObj = $("#mealTemplate");}
    if (curObj.find(".fa-dot-circle-o").length > 0) {
        bounce_Fixed.cancel()
        $("#newServiceContract .gray-box").html("").siblings(".hd").html("");
        curObj.find(".fa-dot-circle-o").each(function() {
            let info = JSON.parse($(this).parents("tr").find(".hd").html());
            $(".serviceInfo").html($(this).parents("tr").find(".hd").html());
            let html = ``
            let able = false, delivery = false, split = true;
            if (source === 1) {
                let serviceCharges = info.serviceCharges || []
                if (info.isPeriodical === 1){
                    html = `每${info.periodDuration}${changeStr(info.periodUnit, 'periodUnit')}收一次`;
                    partCur.find(".chargeStage").html(changeStr(serviceCharges[0].chargeStage, 'chargeStage'));
                    partCur.find(".chargeStageTtl").html(changeStr(serviceCharges[0].chargeStage, 'chargeStageTtl'));
                } else {
                    if (info.chargeNum === 1 && !serviceCharges[0].chargeNum) {
                        html = `一次收取完毕，与交付/验收${info.deliveryAcceptable === 0? '无':'有'}关`;
                        partCur.find(".chargeStage").html(changeStr(serviceCharges[0].chargeStage, 'chargeStage'));
                        partCur.find(".chargeStageTtl").html(changeStr(serviceCharges[0].chargeStage, 'chargeStageTtl'));
                    } else{
                        split = false;
                        icon = 2;
                        partCur =  $("#newServiceContract .s_contact2");
                        if (info.chargeNum > 1) {
                            html = `与交付/验收${info.deliveryAcceptable === 0? '无':'有'}关，按比例分${info.chargeSize}次收取`;
                            partCur.find(".add_modeNoSetted").hide();
                            partCur.find(".add_modeSetted").show();
                        } else {
                            html = '未设置'
                            partCur.find(".add_modeNoSetted").show();
                            partCur.find(".add_modeSetted").hide();
                        }
                    }
                }
            } else {
                html = formatDur(info, 'Duration');
                let productList = info.productList || [], productStr = ``;
                icon = 3;
                partCur =  $("#newServiceContract .s_contact3");
                productList.forEach((item2)=>{
                    let deliveryTerm = item2.deliveryPlace; //供货地点1-存放于本机构2-需交到客户手中
                    if(deliveryTerm === 2){ // 每个收费周期都需提供
                        delivery = true;
                        split = false;
                    }
                    productStr += `${ item2.code }/${ item2.name }`
                })
                if (delivery) {
                    icon = 4;
                    partCur =  $("#newServiceContract .s_contact4");
                }
                partCur.find(".productCon").attr("title", productStr).html(`共${productList.length}种` + (productList.length>0?'，':"") + productStr).siblings().html(productList);
                partCur.find(".chargeStage").html(changeStr(info.chargeStage, 'chargeStage'));
                partCur.find(".chargeStageTtl").html(changeStr(info.chargeStage, 'chargeStageTtl'));
            }
            partCur.show().siblings().hide();
            partCur.find(".priceSet").hide();
            partCur.find(".priceSet").each(function(){
                $(this).find("input, select").removeData("fact");
                $(this).find("input, select").prop("disabled", false);
            })
            let priceDesc = ``
            switch (invoiceType){
                case 1:
                case '1':
                    partCur.find(".priceSet:lt(3)").show()
                    if (handleNull(info.unitPriceNotax) !== "" && handleNull(info.unitPrice) !== ""){
                        able = true
                        if (icon === 1) {
                            partCur.find(".priceSet").eq(0).find("input").data("fact",info["unitPriceNotax"]).prop("disabled", true);
                            partCur.find(".priceSet").eq(1).find("select").data("fact",info["taxRate"]).prop("disabled", true);
                        } else {
                            partCur.find(".priceSet").eq(1).find("input").data("fact",info["unitPriceNotax"]).prop("disabled", true);
                            partCur.find(".priceSet").eq(0).find("select").data("fact",info["taxRate"]).prop("disabled", true);
                        }
                        partCur.find(".priceSet").eq(2).find("input").data("fact",info["unitPrice"]).prop("disabled", true);
                    } else {
                        priceDesc =
                            (handleNull(info.unitPriceInvoice) !== "" ? `非专票开票价`+ info.unitPriceInvoice+`元`:'') +
                            (handleNull(info.unitPriceNoinvoice) !== "" ? `，不开票价`+ info.unitPriceNoinvoice+`元`:'')+
                            (handleNull(info.unitPriceReference) !== "" ? `，参考价格`+ info.unitPriceReference+`元`:'')
                    }
                    break;
                case 2:
                case '2':
                    partCur.find(".priceSet").eq(3).show()
                    if (handleNull(info.unitPriceInvoice) !== ""){
                        able = true
                        partCur.find(".priceSet").eq(3).find("input").data("fact",info["unitPriceInvoice"]).prop("disabled", true);
                    } else {
                        priceDesc = (handleNull(info.unitPriceNotax) !== "" ? `开具`+ info["taxRate"] +`%专票的不含税价为`+ info["unitPriceNotax"]+`元`:'') +
                            (handleNull(info.unitPriceNoinvoice) !== "" ? `，不开票价`+ info.unitPriceNoinvoice+`元`:'')+
                            (handleNull(info.unitPriceReference) !== "" ? `，参考价格`+ info.unitPriceReference+`元`:'')
                    }
                    break;
                case 3:
                case '3':
                    partCur.find(".priceSet").eq(4).show()
                    if (handleNull(info.unitPriceNoinvoice) !== ""){
                        able = true
                        partCur.find(".priceSet").eq(4).find("input").data("fact",info["unitPriceInvoice"]).prop("disabled", true);
                    } else {
                        priceDesc = (handleNull(info.unitPriceNotax) !== "" ? `开具`+ info["taxRate"] +`%专票的不含税价为`+ info["unitPriceNotax"]+`元`:'') +
                            (handleNull(info.unitPriceInvoice) !== "" ? `，非专票开票价`+ info.unitPriceInvoice+`元`:'') +
                                (handleNull(info.unitPriceReference) !== "" ? `，参考价格`+ info.unitPriceReference+`元`:'')
                    }
                    break;
            }
            if (able) {
                $(".editPrice").show().data("able", true);
            } else {
                $(".priceSet i.xing").show();
                partCur.find("input[name='priceDesc']").val(priceDesc);
            }
            partCur.find(".chargingMethod").html(html);
            partCur.find("input:visible").each(function (){
                let name = $(this).attr("name");
                if (name === 'name' && !split) {
                    $(this).val(info[name]+ '/' + info['code']);
                } else if (name === 'priceDesc' && !able){
                } else {
                    $(this).val(info[name]);
                }
            })
            partCur.find("select:visible").each(function (){
                let name = $(this).attr("name");
                $(this).val(info[name]);
            })
        })
    }
}
// update: lyt 2022-10-13 编辑合同文本方面的信息
function contractInfoEdit(obj) {
    let cNO = $(".onContractText:visible").html();
    if (cNO !== "") {
        let info = JSON.parse($(".onContractText:visible").siblings(".hd").html());
        $("#newContractInfo .cNO").val(info.cNO)
        $("#newContractInfo .cSignDate").val(info.cSignDate)
        $("#newContractInfo .cStartDate").val(info.cStartDate)
        $("#newContractInfo .cEndDate").val(info.cEndDate)
        $("#newContractInfo .cMemo").val(info.cMemo)
        let imgStr1 = ``
        info.fileCon1.forEach(function(data){
            imgStr1 = `<span class="fileIm" >
                                <span>${data.orders}</span>
                                <span class="fa fa-times"></span>
                                <span class="hd">${JSON.stringify(data) }</span>
                            </span>`
        })
        $("#newContractInfo .fileCon1").append(imgStr1)
        let imgStr2 = ``
        info.fileCon2.forEach(function(data){
            imgStr2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data)}</span>
                         </span>`
        })
        $("#newContractInfo .fileCon2").html(imgStr2)
    } else {
        $("#newContractInfo input").val("")
        $("#newContractInfo .fileCon>div").html("")
        initCUpload2($("#cUpload1"),'img');
        initCUpload2($("#cUpload2"),'doc');
    }
    bounce_Fixed.show($("#newContractInfo"));
}
// creator: hxz 2021-05-21 编辑合同确定
function editContractOk(num) {
    if(num === 0){ // 取消, 删除上传的文件
        let fileImArr = $("#newContractInfo .fileCon1 .fileIm")
        if(fileImArr.length > 0){
            let info = JSON.parse(fileImArr.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }
        let file2= $("#newContractInfo .fileCon2 .fileIm")
        if(file2.length > 0){
            let info = JSON.parse(file2.find(".hd").html());
            let groupUuid = info.groupUuid;
            cancelFileDel({type: 'groupUuid',groupUuid: groupUuid} , true)
        }
    }else{ // 确定
        let info = {}
        info.cNo = $("#newContractInfo .cNo").val()
        if(info.cNo.length === 0){
            layer.msg('请录入合同编号');
            return false
        }
        info.cSignDate = $("#newContractInfo .cSignDate").val()
        if(info.cSignDate === ""){
            layer.msg('请选择签署日期');
            return false
        }
        info.cStartDate = $("#newContractInfo .cStartDate").val()
        info.cEndDate = $("#newContractInfo .cEndDate").val()
        info.cMemo = $("#newContractInfo .cMemo").val()
        if(info.cStartDate && info.cEndDate){
            let start = Date.parse(new Date( info.cStartDate));
            let end = Date.parse(new Date( info.cEndDate));
            if(start > end){
                layer.msg('合同有效期开始时间应早于结束时间');
                return false
            }
        }
        info.fileCon1 = [];
        $("#newContractInfo .fileCon1 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon1.push(JSON.parse(itemf))
        })
        info.fileCon2 = [];
        $("#newContractInfo .fileCon2 .fileIm").each(function () {
            let itemf = $(this).find(".hd").html();
            info.fileCon2.push(JSON.parse(itemf))
        })
        $(".onContractText:visible").html(info.cNo).siblings(".hd").html(JSON.stringify(info));
    }
    bounce_Fixed.cancel()
}
/*creator:lyt 2022/10/17 14:25 设置收款时间节点*/
function collectionTimeSet(){
    let allAmount = $("#newServiceContract .contractAmount:visible").val();
    if (Number(allAmount) > 0){
        $("#editCollectionTime .conBody .surplusSect").prevAll().remove();
        bounce_Fixed.show($("#editCollectionTime"));
        let invoiceType = $(".invoiceRequire").data("invoice")
        let serviceInfo = JSON.parse($(".serviceInfo").html());
        let serviceCharges = serviceInfo.serviceCharges || [];
        let html = ``, finalStr = ``,interCode = false;
        let enteringTypeUnit = serviceInfo.enteringType === 1? '元':'%';
        let amountStr = serviceInfo.enteringType === 1? 'amount':'percentage';
        $("#timeSum").html(serviceInfo.chargeSize);
        let editBtn = ``;
        if (serviceInfo.enteringType === 1) {
            editBtn = `<span class="linkBtn ty-right" data-fun="editRentPay" data-state=1>修改</span>`;
        }
        if (serviceCharges.length > 0){
            $(".surplusTtl").html("剩余款项的收款日期");
            for (var i=0;i<serviceCharges.length;i++) {
                let chargeType = ``, careStr = changeStr(serviceCharges[i].chargeStage, 'chargeStage') + serviceCharges[i].chargeLimit + '天内需收'+serviceCharges[i][amountStr] + enteringTypeUnit
                if (serviceCharges[i].chargeType === 2) {
                    chargeType = '(预付款)'
                } else if (serviceCharges[i].chargeType === 3) {
                    interCode = true;
                    if (serviceInfo.deliveryAcceptable === 1){//1=有关
                        chargeType = `(${serviceCharges[i].stageName})`
                    } else {
                        careStr = '需收取中间款项' + +serviceCharges[i][amountStr] + enteringTypeUnit
                    }
                } else if (serviceCharges[i].chargeType === 4) {
                    if (serviceInfo.deliveryAcceptable === 2){//1=有关
                        careStr = '服务结束后' + serviceCharges[i].chargeLimit + '天内需收'+serviceCharges[i][amountStr] + enteringTypeUnit
                    }
                    chargeType = '(尾款)'
                }
                let prop = 0, amount = 0;
                if (serviceInfo.enteringType === 1) {
                    amount = serviceCharges[i].amount
                    prop = (serviceCharges[i].amount /allAmount*100).toFixed(2);
                } else {
                    amount = (serviceCharges[i].percentage * allAmount / 100).toFixed(2);
                    prop = serviceCharges[i].percentage;
                }
                if (serviceCharges[i].chargeType === 4) {
                    finalStr += `<div class="clear">
                        <p class="orgCare"> 
                            ${careStr}${chargeType}
                        </p>
                        <div class="modItem">
                            <p>${serviceInfo.deliveryAcceptable === 1?changeStr(serviceCharges[i].chargeStage, 'chargeStageTtl')+'的': "应收款"}日期<i class="xing"></i></p>
                            <input class="form-control collectionChargeDate" value="" placeholder="请录入" name="chargeTime" require/>
                        </div>
                        <div class="modItem">
                            <p>应收金额<i class="xing"></i>${editBtn}</p>
                            <div class="gray-box">
                                <span class="priceItem">${amount}</span>
                                <input class="priceInput" value="${amount}" placeholder="请录入" name="amount" oninput="editRentPayCount($(this))" require/>
                                <span>(占合同总金额的<span class="percentage" data-fact="${prop}">${prop}</span>%)</span>
                            </div>
                        </div>
                        <div class="hd">${JSON.stringify(serviceCharges[i])}</div>
                    </div>`;
                } else {
                    html += `<div class="clear">
                        <p class="orgCare"> 
                            ${careStr}${chargeType}
                            ${serviceCharges[i].chargeType === 3? '<span class="rt-arrow">></span>':''}
                        </p>
                        <div class="modItem">
                            <p>${serviceInfo.deliveryAcceptable === 1?changeStr(serviceCharges[i].chargeStage, 'chargeStageTtl')+'的': "应收款"}日期<i class="xing"></i></p>
                            <input class="form-control collectionChargeDate" value="" placeholder="请录入" name="chargeTime" require/>
                        </div>
                        <div class="modItem">
                            <p>应收金额<i class="xing"></i>${editBtn}</p>
                            <div class="gray-box">
                                <span class="priceItem">${amount}</span>
                                <input class="priceInput" value="${amount}" placeholder="请录入" name="amount" oninput="editRentPayCount($(this))" require/>
                                <span>(占合同总金额的<span class="percentage" data-fact="${prop}">${prop}</span>%)</span>
                            </div>
                        </div>
                        <div class="hd">${JSON.stringify(serviceCharges[i])}</div>
                       </div>`;
                }
            }
        } else {
            interCode = true;
            $(".surplusTtl").html("收款日期");
        }
        let tip = ``;
        $("#editCollectionTime .surplusSect").show();
        $("#editCollectionTime .conBody").prepend(html + finalStr);
        $("#editCollectionTime .collectionChargeDate").each(function(){
            let _this = $(this)
            laydate.render({elem: _this[0], format: 'yyyy-MM-dd'});
        })
        if(serviceInfo.deliveryAcceptable === 1) {
            tip = `<div class="orgCare">应收金额的修改，产生了剩余的应收款项。您可继续修改各条应收金额，也可对剩余款项如何收取进行设置</div>`;
        } else {
            tip = '<div class="orgCare">上述应收金额合计与合同金额不相同，差额即为剩余款项。请确定剩余款项的收款日期。</div>' +
                '<div class="careful">注：剩余款项可能产生于您对上述应收金额的修改，也可能本项目设置时，对应收款项没有设置全。</div>';
        }
        if (!interCode) {
            tip = `<div class="orgCare">所选项目或套餐模式设置时，未对全部款项进行设置，请确认剩余款项的收款日期</div>`;
        }
        bounce_Fixed.everyTime('0.5s', 'surplusSect', function () {
            let all = 0;
            $("#editCollectionTime .conBody > div:not(.surplusSect)").each(function (){
                let amount = $(this).find(".priceItem").html()
                if ($(this).find(".priceInput").is(":visible")){
                    amount = $(this).find(".priceInput").val()
                }
                all += Number(amount)
            });
            if (all !== Number(allAmount)) {
                $("#editCollectionTime .surplusSect .priceItem").html(Number(allAmount - all).toFixed(2));
                $("#editCollectionTime .surplusSect .percentage").html(Number((allAmount - all) / allAmount)*100);
                $("#editCollectionTime .surplusSect").show();
            } else {
                $("#editCollectionTime .surplusSect").hide();
            }
        });
        $("#editCollectionTime .surplusTip").html(tip);
    } else {
        layer.msg("请先录入合同金额！")
    }
}
/*creator:lyt 2022/10/18 0018 下午 设置收款时间节点确定*/
function editCollectionTimeSure(){
    let abled = true;
    $("#editCollectionTime .conBody input[require]:visible").each(function(){
        if ($(this).val() === "") {
            abled = false
            return false
        }
    })
    if (abled) {
        let collectionTimeList = [];
        let serviceInfo = JSON.parse($(".serviceInfo").html());
        $("#editCollectionTime .conBody > div:not(.surplusSect)").each(function(){
            let time = $(this).find(".collectionChargeDate").val()
            let amount = $(this).find(".priceItem").html()
            if ($(this).find(".priceInput").is(":visible")){
                amount = $(this).find(".collectionChargeDate").val()
            }
            let info = JSON.parse($(this).find(".hd").html());
            let temp = {
                "chargeTime": time,
                "chargeStage": info.chargeStage,
                "chargeType": info.chargeType,
                "amount": amount
            }
            if (serviceInfo.deliveryAcceptable === 1) {
                temp.stageName = info.stageName
                temp.stageDesc = info.stageDesc
            }
            collectionTimeList.push(temp)
        });
        if ($(".surplusSect").is(":visible")){
            if (Number($(".surplusSect .priceItem").html()) >= 0) {
                let temp = {
                    "chargeTime": $(".surplusSect #surplusDate").val(),
                    "chargeType": 5,//charge_type是5 1-全款,2-预付款,3-中间款,4-尾款', 5-剩余款项
                    "amount": $(".surplusSect .priceItem").html()
                }
                collectionTimeList.push(temp)
                bounce_Fixed.cancel()
                $("#newServiceContract .collectionTimeData").html(JSON.stringify(collectionTimeList))
            } else {
                layer.msg("<p>操作失败</p><p>当前剩余款项为负数，这不符合逻辑！</p>");
            }
        } else {
            bounce_Fixed.cancel()
            $("#newServiceContract .collectionTimeData").html(JSON.stringify(collectionTimeList))
        }
        let detail = `需设置${collectionTimeList.length}个，已设置${collectionTimeList.length}个`
        $("#newServiceContract .collectionTime").html(detail)
    } else {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
    }
}
/*creator:lyt 2022/10/20 新增服务合同确定 */
function newServiceContractSure(){
    let able = true, c_able = true;
    let invoiceRequire = $(".invoiceRequire").data("invoice")
    let order = $("#newServiceContract .s_contact").index($("#newServiceContract .s_contact:visible"));
    $("#newServiceContract input[require]:visible").each(function (){
        $(this).val() === ""?able = false: ""
    })
    //合同
    let contract = $("#newServiceContract .s_contact:visible .onContractText").siblings(".hd").html()
    let times = ``,deliveryTime= ``,productPlace= ``
    if (order === 1) {
        if ($(".add_modeSetted").is(":visible")){
            times = $("#newServiceContract .collectionTimeData").html()
            if (times === "") able = false
        }
    } else if (order === 3) {
        deliveryTime = $("#newServiceContract .deliveryTime").siblings(".hd").html()
        productPlace = $(".s_contact4 .productPlace").siblings(".hd").html()
        if (deliveryTime === "" || productPlace === "") able = false
    }
    if (contract === "") able = false
    if($(".serviceInfo").html() ==="" || $(".s_contact:visible").length <= 0) c_able = false
    if (able && c_able) {
        let serviceInfo = JSON.parse($(".serviceInfo").html());
        let data = {"contract":
                {
                    "merchandise": 0,
                    "customerId": $(".customerName").data("cusId")
                }
        }
        $("#newServiceContract .priceSet:visible").each(function (){
            let name = ``, val = ``;
            if ($(this).find("input").length > 0) {
                val =  $(this).find("input").val()
                name = $(this).find("input").attr("name")
            } else {
                val =  $(this).find("select").val()
                name = $(this).find("select").attr("name")
            }
            data.contract[name] = val
        })
        let icon = order * 1 + 1
        let contractChargeList = [], charge = {}
        if (order === 0 || order === 1) {
            data.contract.serviceItem = serviceInfo.id
            if (order === 0){
                charge.chargeTime = $("#newServiceContract .s_contact" + icon).find(".chargeStageDate").val()
                charge.chargeStage = serviceInfo.serviceCharges[0].chargeStage
                contractChargeList.push(charge)
            } else {
                if ($(".add_modeNoSetted").is(":visible")) {
                    charge.chargeTime = $("#newServiceContract .s_contact" + icon).find(".chargeStageDate").val()
                    contractChargeList.push(charge)
                } else {
                    contractChargeList = JSON.parse(times)
                }
            }
        } else if (order === 2 || order === 3){
            data.contract.servicePackage = serviceInfo.id
            if (order === 2){
                charge.chargeTime = $("#newServiceContract .s_contact" + icon).find(".chargeStageDate").val()
                charge.chargeStage = serviceInfo.chargeStage
                contractChargeList.push(charge)
            } else if (order === 3){
                productPlace = JSON.parse(productPlace)
                deliveryTime = JSON.parse(deliveryTime);
                data.contractDuration = deliveryTime
                data.contractDelivery = productPlace
            }
        }
        contract = JSON.parse(contract)
        let imgs = [] , filePath = '',fileName = '';
        if( contract.fileCon2.length > 0  ){
            filePath = contract.fileCon2[0].filename
            fileName = contract.fileCon2[0].originalFilename
        }
        if(contract.fileCon1 && contract.fileCon1.length > 0){
            contract.fileCon1.forEach(function(im, index){
                imgs.push({
                    "filePath": im.filePath,
                    "order":index ,
                    "type":"1",
                    "title": im.title
                })
            })
        }
        data.contractBase = {
            "sn": contract.cNo,
            "signTime": order === 3? deliveryTime.signTime:contract.cSignDate,
            "validStart": contract.cStartDate,
            "validEnd": contract.cEndDate,
            "filePath": filePath,
            "fileName": fileName,
            "memo": contract.cMemo,
            "hasInvoice": invoiceRequire,
            "contractType": 2,
            "contractBaseImages": imgs,
            "receiveTime": $("#contractReceiveTime").val()
        }
        data.contractChargeList = contractChargeList
        var settings = {
            "url": "../saleContract/add?",
            "method": "POST",
            "timeout": 10000,
            "headers": {
                "Content-Type": "application/json"
            },
            "data": JSON.stringify(data),

        };
        $.ajax(settings).done(function(response){
            if (response === 2) {
                layer.msg("新增失败，名称或者代号重复！");
            } else if (response.status === 1){
                getContractList(1)
                bounce.cancel();
                layer.msg("<p>操作成功！</p><p>Wonderss将按设置给予您系统提示！</p>");
            }
        })
    } else {
        if (!able) {
            layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
        } else {
            layer.msg("<p>请根据合同实际情况选择一个合适的项目或套餐模板。</p>");
        }
    }
}
// creator: lyt，2022-08-22 08:57:55， 查找 在用的常规服务合同
function searchList() {
    getContractList(1)
}
// creator: hxz 2021-05-21 编辑合同 上传合同文件
function initCUpload2(obj,type){
    let fileTypeExtsStr = ''
    let multi = true
    let groupUuid = sphdSocket.uuid();
    if(type == "img"){
        fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    }else if(type == "doc"){
        multi = false
        fileTypeExtsStr = '*.doc,*.docx;*.xls;*.xlsx;*.ppt;*.txt;'
    }
    let itemTemplate = ``
    obj.html("")
    obj.Huploadify({
        auto:true,
        fileTypeExts: fileTypeExtsStr,
        itemTemplate: itemTemplate,
        multi: multi,
        buttonText:"上传",
        formData:{
            module: '销售管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            data = JSON.parse(data)
            if(type == "img"){
                let len =  $(`.fileCon1`).find(".fileIm").length;
                if(len < 9){
                    data.orders = len + 1
                    data.filePath = data.filename
                    data.title = data.originalFilename
                    let imgStr1 = `<span class="fileIm"  >
                                 <span>${ 1 + len }</span>
                                 <span class="fa fa-times"></span>
                                 <span class="hd">${ JSON.stringify(data) }</span>
                            </span>`
                    $(`.fileCon1`).append(imgStr1);
                }else{
                    layer.msg('最多只能上传9个文件')
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                }
            }else if(type == "doc"){
                let len =  $(`.fileCon2`).find(".fileIm").length;
                if(len === 0){
                }else{
                    let delO = $(`.fileCon2`).find(".fileIm")
                    let info = JSON.parse(delO.find(".hd").html())
                    let fileUid = data.fileUid
                    cancelFileDel([{'type':'fileId', 'fileId':fileUid } ], true);
                    delO.remove();
                }
                let str2 = `<span class="fileIm"  >
                             <span class="fa fa-file-word-o"></span>
                             <span class="fa fa-times"></span>
                             <span class="hd">${ JSON.stringify(data) }</span>
                        </span>`
                $(`.fileCon2`).html(str2);
            }
        }
    })
}
// creator: 李玉婷，2020-04-14 19:14:45，修改价格
function editPrice(obj){
    let able = obj.data("able");
    let parObj = $("#newServiceContract .priceSet:visible");
    obj.data("able", !able);
    parObj.each(function (){
        if (able){
            $(".priceSet i.xing").show();
            $(this).find("input, select").val("").prop("disabled", false);
        } else {
            $(".priceSet i.xing").hide();
            let fact = $(this).find("input, select").data("fact");
            $(this).find("input, select").val(fact).prop("disabled", true);
        }
    })
}
/*creator:lyt 2022/10/17 应收金额修改 */
function editRentPay(obj){
    let state = obj.data("state")
    if (state === 1) {
        obj.data("state", 2)
        obj.parent().siblings().find(".priceItem").hide().siblings(".priceInput").show();
    } else {
        obj.data("state", 1)
        obj.parent().siblings().find(".priceInput").hide().siblings(".priceItem").show();
        obj.parent().siblings().find(".percentage").html(obj.parent().siblings().find(".percentage").data("fact"));
    }
}
/*creator:lyt 2022/10/26 计算百分比 */
function editRentPayCount(obj){
    let allAmount = $("#newServiceContract .contractAmount:visible").val();
    let prop = obj.val() / allAmount * 100
    obj.siblings().find(".percentage").html(prop);
}
/*creator:lyt 2022/10/19 收款与发货的时间节点设置 */
function deliveryTimeSet(obj){
    $("#deliveryTimeSet input").val("");
    let times = obj.parent().siblings(".hd").html()
    if (times !== "") {
        times = JSON.parse(times)
        for(let item in times) {
            $("#deliveryTimeSet .contract_" + item).val(times[item])
        }
    } else {
        let contract = $("#newServiceContract .s_contact:visible .onContractText").siblings(".hd").html()
        if (contract !== "") {
            contract = JSON.parse(contract)
            $("#deliveryTimeSet .contract_signTime").val(contract.cSignDate)
        }
    }
    bounce_Fixed.show($("#deliveryTimeSet"));
}
/*creator:lyt 2022/10/19  收款与发货的时间节点设置确定*/
function deliveryTimeSure(){
    let able = true, str = ``;
    $("#deliveryTimeSet input").each(function(){
        if ($(this).val() === "") {
            able = false;
        }
    })
    if (able) {
        let data ={
            "signTime": $("#deliveryTimeSet .contract_signTime").val(),
            "beginTime": $("#deliveryTimeSet .contract_beginTime").val(),
            "endTime": $("#deliveryTimeSet .contract_endTime").val()
        }
        str = `合同签订的日期${data.signTime}，服务开始的日期${data.beginTime}，服务结束的日期${data.endTime}`
        bounce_Fixed.cancel();
        $(".deliveryTime").attr("title", str).html(str).siblings(".hd").html(JSON.stringify(data));
        let contract = $("#newServiceContract .s_contact:visible .onContractText").siblings(".hd").html();
        if (contract !== "") {
            contract = JSON.parse(contract)
            contract.cSignDate = $("#deliveryTimeSet .contract_signTime").val()//把合同文本的签订日期值也改成最新的
            $("#newServiceContract .s_contact:visible .onContractText").siblings(".hd").html(JSON.stringify(contract))
        }
    } else {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
    }
}
/*creator:lyt 2022/10/19 本合同所涉及的商品 */
function productConScan(){
    let serviceInfo = JSON.parse($(".serviceInfo").html());
    let productList = serviceInfo.productList || []
    let str = ``
    let init = {
        "isdur":serviceInfo.isPeriodical,
        "isgoods":serviceInfo.isMixture
    }
    $("#productConScan .ty-table tr:gt(0)").remove()
    productList.forEach((item2)=>{
        item2.init = init
        item2.init.durSet = item2.deliveryTerm;
        item2.init.whereSet = item2.deliveryPlace;
        item2.init.fuBefore = item2.deliverySequence;
        if (item2.deliverySequence == 2) {item2.init.dayNum = item2.deliveryDuration }
        str += `
                    <tr>
                        <td>${ item2.code }/${ item2.name }</td>
                        <td>${ item2.deliveryQuantity || ""  }</td>
                        <td>${ formatChargeStage(item2.deliveryStage,item2.deliveryLimit)}</td>
                        <td>${ item2.deliveryTerm === 1?'每个收费周期都需提供':'仅第一个收费周期提供' }</td>
                        <td><span class="ty-color-blue funBtn" data-fun="scanGoods">查看</span><span class="hd">${ JSON.stringify(item2) }</span></td>
                    </tr>
                    `
    })
    $("#productConScan .ty-table tbody").append(str)
    bounce_Fixed.show($("#productConScan"))
}
/*creator:lyt 2022/10/19 本合同所涉及的商品 */
function productConEdit(){
    let serviceInfo = JSON.parse($(".serviceInfo").html());
    let productList = serviceInfo.productList
    let str = ``
    let init = {
        "isdur":serviceInfo.isPeriodical,
        "isgoods":serviceInfo.isMixture
    }
    let productPlace = $(".s_contact4 .productPlace").siblings(".hd").html()
    $("#productConEdit .ty-table tr:gt(0)").remove()
    let addrList = $(".customerAddressCon").html()
    addrList !== "" ? addrList = JSON.parse(addrList): []
    productPlace !== "" ? productPlace = JSON.parse(productPlace): []
    productList.forEach((item2)=>{
        let place = `存放于本机构`
        item2.init = init
        item2.init.durSet = item2.deliveryTerm;
        item2.init.whereSet = item2.deliveryPlace;
        item2.init.fuBefore = item2.deliverySequence;
        if (item2.deliverySequence == 2) {item2.init.dayNum = item2.deliveryDuration }
        let addrListStr = `<option value="">请选择</option>`;
        for (var i = 0; i < addrList.length; i++) {
            if (productPlace.length > 0) {
                let icon = productPlace.findIndex((item) => item.serviceDetail === item2.id)
                addrListStr += `<option value="${addrList[i].id}" ${Number(productPlace[icon].deliveryAddress) === Number(addrList[i].id) ? "selected": ""}>${addrList[i].address}</option>`;
            } else {
                addrListStr += `<option value="${addrList[i].id}">${addrList[i].address}</option>`;
            }
        }
        if (item2.deliveryPlace === 2){
            place = `<select>${addrListStr}</select>`
        }
        str += `
                    <tr data-delivery="${ item2.deliveryPlace }">
                        <td>${ item2.code }/${ item2.name }</td>
                        <td>${ item2.deliveryQuantity || ""  }</td>
                        <td>${ formatChargeStage(item2.deliveryStage, 'chargeStage',item2.deliveryLimit)}</td>
                        <td>${ item2.deliveryTerm === 1?'每个收费周期都需提供':'仅第一个收费周期提供' }</td>
                        <td>${place}
                        </td>
                        <td><span class="ty-color-blue funBtn" data-fun="scanGoods">查看</span><span class="hd">${ JSON.stringify(item2) }</span></td>
                    </tr>
                    `
    })
    $("#productNum").html(productList.length)
    $("#productConEdit .ty-table tbody").append(str)
    bounce_Fixed.show($("#productConEdit"))
}
/*creator:lyt 2022/11/10 0010 上午 9:45 */
function addProductAddress(){
    let abled = true;
    $("#productConEdit .ty-table tbody tr:gt(0)").each(function (){
        if ($(this).data("delivery") !== 2 || $(this).find("select").val() === ""){
            abled = false;
        }
    })
    if (abled) {
        let serviceInfo = JSON.parse($(".serviceInfo").html());
        let arr = []
        $("#productConEdit .ty-table tbody tr:gt(0)").each(function (){
            let info = JSON.parse($(this).find(".hd").html())
            let item =  {
                    "serviceDetail": info.id,
                    "servicePackage": serviceInfo.id,
                    "deliveryAddress": $(this).find("select").val(),
                    "merchandise": info.serviceItem,
                    "addressDesc": $(this).data("delivery") === 1?$(this).find("select").find("option:selected").text():""
                }
            arr.push(item)
        })
        let detail = `需设置${arr.length}个，已设置${arr.length}个`
        $(".s_contact4 .productPlace").html(detail)
        $(".s_contact4 .productPlace").siblings(".hd").html(JSON.stringify(arr))
        bounce_Fixed.cancel()
    } else {
        layer.msg("还有内容未填完整！")
    }
}
/*creator:lyt 2022/10/19 设置收货地址 */
function getAddressList(cusId){
    $.ajax({
        url: "../sales/getAddressListByCondition.do",
        data: {"cusId": cusId},
        success: function (data) {
            var addrList = data["data"] || [];
            /*var addrListStr = '<option value="">请选择</option>';
            for (var i = 0; i < addrList.length; i++) {
                addrListStr += '<option value="' + addrList[i].id + '">' + addrList[i].address + '</option>';
            }*/
            $(".customerAddressCon").html(JSON.stringify(addrList))
        }
    })
}
// creator: 李玉婷，2020-04-14 19:14:45，勾选模板
function checkState(obj) {
    obj.parents("tbody").find(".fa-dot-circle-o").removeClass("fa-dot-circle-o").addClass("fa-circle-o");
    obj.addClass("fa-dot-circle-o").removeClass("fa-circle-o");
}
// create: lyt，2021-05-27 10:16:13 计算价格
function setprice(num, obj) { // 1- 不含税，2-含税
    let PObj = obj.parents(".priceSetGroup");
    let noprice = Number(PObj.find(".noPriceDot").find("input").val());
    let price = Number(PObj.find(".priceDot").find("input").val());
    let rate = Number(PObj.find(".rateDot").find("select").val());
    if(num === 1){
        if(rate > 0 && noprice > 0){
            price = noprice * (rate/100 + 1) ;
            PObj.find(".priceDot").find("input").val(price.toFixed(2));
        }
        if(noprice === 0){
            PObj.find(".priceDot").find("input").val("").prop("disabled", false)
        }else{
            PObj.find(".priceDot").find("input").prop("disabled", true)
        }
    }else if(num === 2){
        if(rate > 0 && price > 0){
            noprice = price / (rate/100 + 1) ;
            PObj.find(".noPriceDot").find("input").val(noprice.toFixed(2));
        }
        if(price === 0){
            PObj.find(".noPriceDot").find("input").val("").prop("disabled", false)
        }else{
            PObj.find(".noPriceDot").find("input").prop("disabled", true)
        }
    }else{
        let priceDis =  PObj.find(".priceDot").find("input").attr("disabled");
        let nopriceDis =  PObj.find(".noPriceDot").find("input").attr("disabled");
        if(priceDis){ // 按照不含税算
            if(rate > 0 && noprice > 0){
                price = noprice * (rate/100 + 1) ;
                PObj.find(".priceDot").find("input").val(price.toFixed(2));
            }
        }else if (nopriceDis){ // 按照含税算
            if(rate > 0 && price > 0){
                noprice = price / (rate/100 + 1) ;
                PObj.find(".noPriceDot").find("input").val(noprice.toFixed(2));
            }
        }
    }
}
function formatDur(item, unit) {
    let detail = ``;
    switch (unit){
        case 'price'://含税单价>普票单价>不开票单价>参考单价
            if (handleNull(item.unitPrice) !== "") {
                detail = item.unitPrice;
            } else if (handleNull(item.unitPriceNotax) !== "") {
                detail = item.unitPriceNotax;
            } else if (handleNull(item.unitPriceInvoice) !== "") {
                detail = item.unitPriceInvoice;
            } else if (handleNull(item.unitPriceNoinvoice) !== "") {
                detail = item.unitPriceNoinvoice;
            } else if (handleNull(item.unitPriceReference) !== "") {
                detail = item.unitPriceReference;
            }
            break;
        case 'Duration':
            if (item.isPeriodical === 1) {
                detail = `每${item.periodDuration}${changeStr(item.periodUnit, 'periodUnit')}收一次`;
            } else {
                if (item.chargeNum === 1) {
                    detail = `与交付/验收${item.deliveryAcceptable === 0? '无':'有'}关，一次收取完毕`;
                } else if (item.chargeNum > 1) {
                    detail = `与交付/验收${item.deliveryAcceptable === 0? '无':'有'}关，需按${item.enteringType === 1? '金额':'比例'}分${item.serviceCharges.length}次收取`;
                } else if (item.chargeNum === 0) {
                    detail = `本项目不按周期收费。`;
                } else {
                    detail = `一次收取完毕`;
                }
            }
            break;
    }
    return detail;
}
function changeStr(val, cate){
    let str = ``;
    if (val) {
        switch (cate) {
            case "periodUnit":
                let unit = ['日','周','旬','月','季','半年','年'];
                str = unit[val-1];
                break;
            case "chargeStage":
                let stage = ['合同签订后','服务开始前','服务开始后','交付前','交付后','本期交付前','本期交付后','最终交付前','最终交付后','通过验收后','本期通过验收后','最终通过验收后','服务结束后'];
                str = stage[val-1];
                break;
            case "chargeStageTtl":
                let stage1 = ['合同签订','应开始服务','应开始服务','应交付','应交付','本期应交付','本期应交付','最终应交付','最终应交付','应通过验收','应通过本期验收','应通过最终验收','服务应结束'];
                str = stage1[val-1];
                break;
        }
    }
    return str;
}
// creator: hxz，2022-08-22 08:57:55，  修 改 套餐
function formatChargeStage(chargeStage, deliveryLimit) {
    // 发货时间要求类型:1合同签订后,2-服务开始前,3-服务开始后,4-交付前,5-交付后
    // ,6-本期交付前.7-本期交付后,8-最终交付前,9-最终交付后,10-通过验收后,
    // 11-本期通过验收后,12-最终通过验收后 13服务结束后
    let str = ``
    if(chargeStage){
        switch (Number(chargeStage)){
            case 1:  str = `合同签订后`; break;
            case 2:  str = `服务开始前`; break;
            case 3:  str = `服务开始后`; break;
            case 4:  str = `交付前`; break;
            case 5:  str = `交付后`; break;
            case 6:  str = `本期交付前`; break;
            case 7:  str = `本期交付后`; break;
            case 8:  str = `最终交付前`; break;
            case 9:  str = `最终交付后`; break;
            case 10:  str = `通过验收后`; break;
            case 11:  str = `本期通过验收后`; break;
            case 12:  str = `最终通过验收后`; break;
            case 13:  str = `服务结束后`; break;
            default:
        }
        str += `${ deliveryLimit }天`
    }
    return str
}
// create :hxz 2021-05-11 格式化显示 开票要求
function formatInviceRequire(type) {
    let str = ''
    switch (Number(type)){
        case 1 :    str = '需开具增值税专用发票'; break
        case 2 :    str = '需开具其他发票'; break
        case 3 :    str = '不开发票'; break
        case 0 :
        case 4 :    str = ''; break
        default: str = ''
    }
    return str
}
// creator: 李玉婷，2020-04-03 10:28:28，描述、说明字数限制
function limitWord(obj, num) {
    var val = obj.val();
    var length = val.length;
    obj.parent().siblings(".lenTip").html(length + '/' + num);
}
// creator: 李玉婷，2022-08-30 10:12:08，只允许录入自然数，且需在1-99之间
function limitNumber(obj){
    clearNum(obj)
    if (!/^[1-9]+$/.test(obj.value)) obj.value = obj.value.replace(/\D/g,'');
    if (obj.value > 99) obj.value = 99;
}

function addUnit(idStr) {
    bounce_Fixed3.show($("#addUnit"));
    $("#addUnit input").val("");
    $("#updateType").val(idStr);
}
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed3.cancel();
                var idStr = $("#updateType").val();
                getUnitList($("#" + idStr), module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed4.show($("#tip1"));
            }
        }
    })
}
// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectedID) {
    // 　1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['name'] +'</option>';
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41   计量单位设置
function unitAssign(obj) {
    var unitName = obj.children("option:selected").html();
    obj.siblings("input").val(unitName)
}
function unDo(){
    layer.msg("未开发")
}
laydate.render({elem: '#contractReceiveTime'});
laydate.render({elem: '.cSignDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cStartDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.cEndDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.s_contact1 .chargeStageDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.s_contact2 .chargeStageDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.s_contact3 .chargeStageDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.s_contact4 .chargeStageDate', format: 'yyyy-MM-dd'});
laydate.render({elem: '.contract_signTime', format: 'yyyy-MM-dd'});
laydate.render({elem: '.contract_beginTime', format: 'yyyy-MM-dd'});
laydate.render({elem: '.contract_endTime', format: 'yyyy-MM-dd'});
laydate.render({elem: '#surplusDate', format: 'yyyy-MM-dd'});






















