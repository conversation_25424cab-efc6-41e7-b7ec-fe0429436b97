let Province = [];
let City = [];
let District = [];
$(function() {
    $(".region-content").on("click", "ul li a",function (){
        let lev = $(this).parents("ul").data("level");
        let name = $(this).html();
        let code = $(this).data("code");
        $(this).parent().addClass("region-content-current").siblings().removeClass("region-content-current")
        $(".region-tab li").eq(lev - 1).html(name).data("code", code);
        if (lev === 1) {
            initCity(code);
        } else if (lev === 2) {
            initDistrict(code);
        } else if (lev === 3) {
            let address = ``,path = [];
            $(".regionBox").hide();
            $(".regionBox .region-content-current").each(function (){
                path.push($(this).children("a").data("code"));
                address += $(this).children("a").html();
            })
            $("#regionCode").val(path[2]);
            $("#regionCon").val(address)
                .siblings(".hd").html(path.toString());
        }
    });
    $("body").click(function(){
        var _con = $('.regionBox'); // 设置目标区域
        var _tarCon = $('.regionText');
        if (!_con.is(event.target) && _con.has(event.target).length === 0 && !_tarCon.is(event.target) && _tarCon.has(event.target).length === 0) { // Mark 1
            $('.regionBox').hide();  //滑动消失
        }
    })
    //二级标签栏切换事件
    $(".region-tab").on("click","li", function () {
        let index = $(this).index();
        $(this).addClass("region-current").siblings("li").removeClass("region-current");
        $(".region-content ul").eq(index).show().siblings().hide();
    });
    getRegionData(1, 2, true);
    getRegionData(3, 3, true);
});
function getRegionData(start, end, async){
    $.ajax({
        url: "../region/getTYRegion.do",
        data: {
            "startLevel": start,
            "endLevel": end
        },
        async: async,
        success: function (data) {
            if (start === 1) {
                if (end === 1) {
                    Province = data.data.list;
                } else if (end === 2) {
                    Province = data.data.list;
                }
            } else if (start === 3) {
                District = data.data.list;
            } else {
                //不推荐这样获取数据
            }
        }
    })
}
function initProvince (val) {
    if (!Province || Province.length === 0) {
        getRegionData(1, 2, false);
    }
    let tab = `<li>请选择</li>`;
    let lev1 = ``;
    for( let i = 0; i < Province.length; i++) {
        if (parseInt(val) === parseInt(Province[i].code)) {
            tab = `<li>${Province[i].name}</li>`;
            lev1 += `<li class="region-content-current"><a data-code="${Province[i].code}">${Province[i].name}</a></li>`;
        } else {
            lev1 += `<li><a data-code="${Province[i].code}">${Province[i].name}</a></li>`;
        }
    }
    $(".region-tab").html(tab);
    $(".region-content ul:gt(0)").remove();
    $(".region-content ul:eq(0)").html(lev1);
}
function initCity (parentCode, val) {
    let str = ``, tab = `<li>请选择</li>`;
    let json = Province.find(item=>Number(item.code) === Number(parentCode));
    if (json.subRegion) {
        let sonlist = json.subRegion;
        for (let i = 0; i < sonlist.length; i++) {
            if (parseInt(val) === parseInt(sonlist[i].code)) {
                tab = `<li>${sonlist[i].name}</li>`;
                str += `<li class="region-content-current" data-parent="${sonlist[i].parentCode}">
                        <a data-code="${sonlist[i].code}">${sonlist[i].name}</a></li>`;
            }else {
                str += `<li data-parent="${sonlist[i].parentCode}">
                        <a data-code="${sonlist[i].code}">${sonlist[i].name}</a></li>`;
            }
        }
        $(".region-tab li:gt(0)").remove();
        $(".region-content ul:gt(0)").remove();
        $(".region-tab").append(tab);
        if ($(".region-content ul").length < 2) {
            $(".region-content").append('<ul data-level="2">' + str + '</ul>');
        } else {
            $(".region-content ul").eq(1).html(str);
        }
        $(".region-tab li").eq(1).click();
    }
}
function initDistrict (parentCode, val) {
    let str = ``, tab = `<li>请选择</li>`;
    $.each(District, function (index, item) {
        if (parseInt(item.parentCode) === parseInt(parentCode)) {
            if (parseInt(item.code) === parseInt(val)) {
                tab = `<li>${item.name}</li>`;
                str += `<li class="region-content-current" data-parent="${item.parentCode}"><a data-code="${item.code}">${item.name}</a></li>`;
            } else {
                str += `<li data-parent="${item.parentCode}"><a data-code="${item.code}">${item.name}</a></li>`;
            }
        }
    })
    $(".region-tab li:gt(1)").remove();
    $(".region-content ul:gt(1)").remove();
    $(".region-tab").append(tab);
    $(".region-content").append('<ul data-level="3">' + str + '</ul>');
    $(".region-tab li").eq(2).click();
}
function regionCheck(){
    let regionCon = $("#regionCon").val();
    if (regionCon === "") {
        $("#regionCon").val("").siblings(".hd").html('');
        $(".region-content ul:eq(0)").show();
        $(".region-content ul:gt(0)").remove();
        initProvince();
    } else {
        let code = $("#regionCon").siblings(".hd").html();
        let arr = code.split(',');
        let path = []
        if (arr.length <= 1){
            path = initPath(code);
        } else {
            path = arr;
        }
        if (path.length === 3){
            initProvince(path[0]);
            initCity(path[0], path[1]);
            initDistrict(path[1], path[2]);
        } else {
            initProvince();
        }
    }
    $(".regionBox").show();
}
function initPath(val){
    let path = [], data3 = {};
    for(let i=0;i<District.length;i++){
        if (District[i].code == val) {
            data3 = District[i];
            console.log(District[i].code + '\n');
        }
    }
    Province.forEach(function (item,index ) {
        let son = item.subRegion;
        let temp = son.find(data => data.code == data3.parentCode);
        if(temp) {
            let arr = temp.path.split(',');
            path = [...arr];
            return false;
        }
    });
    path.push(val);
    $("#regionCon").siblings(".hd").html(path.toString());
    return path;
}





















