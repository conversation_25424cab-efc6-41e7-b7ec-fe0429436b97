var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#tip1"));
bounce_Fixed2.cancel();
$(function() {
    showMainCon(1);
    getServiceList("", 1,1, 20);
    $("body").on("click", '.funBtn, span[type="btn"]', function () {
        var name = $(this).data("fun");
        let info = ``;
        switch (name) {
            case 'addService':
                $("#serviceInitChoose .changeDot i").attr("class", "fa fa-circle-o");
                bounce.show($("#serviceInitChoose"));
                break;
            case 'stoppedService':
                showMainCon(2);
                getServiceList("",0, 1,20)
                break;
            case 'seeService':
                info = JSON.parse($(this).siblings(".hd").html())
                seeService(info.id);
                break;
            case 'stopService':
                let able = $(this).data("able");
                info = JSON.parse($(this).siblings(".hd").html())
                if (able === 0) {
                    stopService(info.id, able, 1);
                } else {
                    $("#stopService").data("id", info.id);
                    $("#stopService").data("enabled", 1);
                    $("#tipMsg").html("<p>确定后，套餐或合同中将又可选到该项目。</p> <p>确定复用该项目吗？</p>");
                    bounce_Fixed.show($("#stopService"));
                }
                break;
            case 'delService':
                info = JSON.parse($(this).siblings(".hd").html())
                if (info.enabled === 1) {
                    delService(info.id, 1);
                } else {
                    $("#delService").data("id", info.id);
                    bounce_Fixed.show($("#delService"));
                }
                break;
        }
    })
    $("body").on("click", '.fun-btn, .nodeBtn ', function () {
        var name = $(this).data("type");
        let detail = ``, carry = ``;
        switch (name) {
            case 'addOne':
                if ($(this).parents(".parts").find(".price-box").length < 3) {
                    carry = $(this).data("carry");
                    let wStr = ` <div class="price-box">
                                    <div class="delSite">
                                        <span class="gapRt0">
                                          ${carry === 1? "金额":"比例"}<input need name="amount" type="text" oninput="${carry === 1? 'clearNoNumN(this, 2)':'limitNumber(this)'}"/>${carry === 1? "元":"%"}
                                        </span>
                                        收费时间
                                        <select class="entry">
                                            <option value=""></option>
                                             <option value="8">最终交付前</option>
                                            <option value="9">最终交付后</option>
                                            <option value="12">最终验收通过后</option>
                                            <option value="13">服务结束后</option>
                                        </select>
                                        <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                        <span class="fun-btn del-btn" data-type="delPrev">删除</span>
                                     </div>
                                 </div>`;
                    $(this).parents(".parts").append(wStr);
                } else {
                    layer.msg("尾款最多分三期");
                }
                break;
            case 'no_addOne':
                if ($(this).parents(".parts").find(".price-box").length < 3) {
                    carry = $(this).data("carry");
                    let oStr =
                        ` <div class="price-box">
                                <div class="delSite">
                                     <span class="gapRt0">
                                     ${carry === 1 ? "金额" : "比例"}<input need name="amount" type="text" oninput="${carry === 1 ? 'clearNoNumN(this, 2)' : 'limitNumber(this)'}" />${carry === 1 ? "元" : "%"}
                                </span>
                                收费时间为服务结束后
                                     <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                     <span class="fun-btn del-btn" data-type="delPrev">删除</span>
                                     </div>
                                </div>`;
                    $(this).parents(".parts").append(oStr);
                } else {
                    layer.msg("尾款最多分三期");
                }
                break;
            case 'addMore':
                carry = $(this).data("carry");
                let zStr = `<div class="price-box">
                                            <div>本期中间款项的收取信息</div>
                                            <div class="delSite">
                                            <span class="gapRt0">
                                                  ${carry === 1? "金额":"比例"}<input need name="amount" type="text" oninput="${carry === 1? 'clearNoNumN(this, 2)':'limitNumber(this)'}"/>${carry === 1? "元":"%"}
                                            </span>
                                            收费时间
                                            <select class="entry">
                                                <option value=""></option>
                                                 <option value="6">本期交付前</option>
                                                <option value="7">本期交付后</option>
                                                <option value="11">本期验收通过后</option>
                                            </select>
                                            <input need name="chargeLimit" type="text" oninput="clearNum(this)"/>天内
                                            <span class="fun-btn del-btn" data-type="delPrev">删除</span>
                                            </div>
                                            <div class="midPay">
                                                <div class="clear">
                                                    <div><span class="gapRt0">交付/验收环节的名称</span>
                                                    <input class="middleSize" name="stageName" type="text" maxlength="20" onkeyup="limitWord($(this), 20)"/></div>
                                                    <div class="lenTip ty-right" style="clear: both">0/20</div>
                                                </div>
                                                <div class="clear">
                                                    <div><span class="gapRt0">交付/验收的内容描述</span>
                                                    <input class="middleSize" name="stageDesc" type="text" maxlength="40" onkeyup="limitWord($(this), 40)"/></div>
                                                    <div class="lenTip ty-right" style="clear: both">0/40</div>
                                                </div>
                                            </div>
                                        </div>`;
                $(this).parents(".parts").append(zStr);
                break;
            case 'no_addMore':
                carry = $(this).data("carry");
                let mStr = `<div class="price-box">
                                            <div>本期中间款项的收取信息</div>
                                            <div class="delSite">
                                                <span class="gapRt0">
                                                    ${carry === 1? "金额":"比例"}<input need name="amount" type="text" oninput="${carry === 1? 'clearNoNumN(this, 2)':'limitNumber(this)'}"/>${carry === 1? "元":"%"}
                                                </span>
                                                <span class="fun-btn del-btn" data-type="delPrev">删除</span>
                                            </div>
                                        </div>`;
                $(this).parents(".parts").append(mStr);
                break;
            case 'delPrev':
                $(this).parents(".price-box").remove();
                break;
            case 'modeSetting':
                let empty = true, filed = 0;
                $("#addService [require]:visible").each(function(){
                    let val = $(this).val();
                    if (val === "") empty = false;
                });
                $(".priceForm [need]").each(function(){
                    let val = $(this).val();
                    if (val !== "") filed++;
                });
                if (empty && filed > 0) {
                    $("#modeSetting .changeDot i").attr("class", "fa fa-circle-o");
                    $("#modeSetting .dotItem:gt(1)").hide();
                    $("#modeSetting").data("source",'add')
                    bounce_Fixed.show($("#modeSetting"));
                } else {
                    layer.msg("<p>操作失败！</p><p>本项目基础信息录完后，才能开启设置！</p>");
                }
                break;
            case 'edit_modeSetting':
                $("#modeSetting .changeDot i").attr("class", "fa fa-circle-o");
                $("#modeSetting .dotItem:gt(1)").hide();
                $("#modeSetting").data("source",'edit')
                $("#editServiceNoCycle .noByCycleSet").show().siblings(".byCycle").hide();
                bounce_Fixed.show($("#modeSetting"));
                break;
            case 'updateService':
                detail = JSON.parse($("#serviceProjectScan .serviceData").html());
                let cycle = detail.isPeriodical;
                $("#editService").data("cycle", cycle);
                bounce_Fixed.show($("#editService"));
                if (cycle === 1) {
                    $("#editService .byCycle").show().siblings(".noByCycle").hide();
                } else {
                    $("#editService .noByCycle").show().siblings(".byCycle").hide();
                }
                invoiceSettings($("#editService select[name='taxRate']"), handleNull(detail.taxRate));
                $("#editService input").prop("disabled", false)
                $("#editService input:visible").each(function(){
                    let key = $(this).attr("name");
                    if (key !== 'chargeLimit') $(this).val(detail[key]);
                });
                $("#editService select:visible").each(function(){
                    let key = $(this).attr("name");
                    if (key !== 'chargeStage') $(this).val(detail[key]);
                });
                if (cycle === 0) {
                    getUnitList($("#edit_unitSelect"), 2, detail.unitId);
                }
                break;
            case 'editModeService':
                let obj = null;
                detail = JSON.parse($("#serviceProjectScan .serviceData").html());
                let modeList = detail.serviceCharges;
                $("#editServiceNoCycle .noByCycleSet").show().siblings().hide();
                if (detail.chargeNum > 1){
                    if (detail.deliveryAcceptable === 1){
                        obj = $(".many_relate");
                    } else {
                        obj = $(".many_noRelate");
                    }
                    let enteringTypeStr = ``, enteringTypeUnit=``, amountStr= ``;
                    enteringTypeStr = detail.enteringType === 1? '金额':'比例';
                    enteringTypeUnit = detail.enteringType === 1? '元':'%';
                    amountStr = detail.enteringType === 1? 'amount':'percentage';
                    $("#editServiceNoCycle input").val("");
                    $("#editServiceNoCycle select").val("");
                    $("#editServiceNoCycle .amountType").html(enteringTypeStr + `<input name="amount" type="text" oninput="${detail.enteringType === 1? 'clearNoNumN(this, 2)':'limitNumber(this)'}"/>`+ enteringTypeUnit);
                    $("#editServiceNoCycle .addOne").data("carry", detail.enteringType);
                    $("#editServiceNoCycle .manyTime").show().siblings().hide();
                    obj.find(".parts").find(".price-box:gt(0)").remove();
                    obj.show().siblings().hide();
                    let num =0, finalNum = 0 ;
                    for (let i=0;i<modeList.length;i++){
                        if (modeList[i].chargeType === 2){//收费类型1-全款,2-预付款,3-中间款,4-尾款
                            obj.find(".parts").eq(0).find("input[name='id']").val(modeList[i].id);
                            obj.find(".parts").eq(0).find("input[name='amount']").val(modeList[i][amountStr]);
                            obj.find(".parts").eq(0).find("select").val(modeList[i].chargeStage);
                            obj.find(".parts").eq(0).find("input[name='chargeLimit']").val(modeList[i].chargeLimit);
                        } else if (modeList[i].chargeType === 3){
                            num++;
                            if (num > 1) {
                                obj.find(".parts:eq(2) .fun-btn").click();
                            }
                            obj.find(".parts:eq(2) .price-box").eq(num-1).find("input[name='id']").val(modeList[i].id);
                            obj.find(".parts:eq(2) .price-box").eq(num-1).find("input[name='amount']").val(modeList[i][amountStr]);
                            if (detail.deliveryAcceptable === 1) {//有关
                                obj.find(".parts:eq(2) .price-box").eq(num - 1).find("select").val(modeList[i].chargeStage);
                                obj.find(".parts:eq(2) .price-box").eq(num - 1).find("input[name='chargeLimit']").val(modeList[i].chargeLimit);
                                obj.find(".parts:eq(2) .price-box").eq(num - 1).find("input[name='stageName']").val(modeList[i].stageName);
                                obj.find(".parts:eq(2) .price-box").eq(num - 1).find("input[name='stageDesc']").val(modeList[i].stageDesc);
                                obj.find(".parts:eq(2) .price-box").eq(num - 1).find(".lenTip").eq(0).html(modeList[i].stageName.length + '/20');
                                obj.find(".parts:eq(2) .price-box").eq(num - 1).find(".lenTip").eq(1).html(modeList[i].stageDesc.length + '/40');
                            }
                        } else if (modeList[i].chargeType === 4){
                            finalNum++;
                            if (finalNum > 1) {
                                obj.find(".parts:eq(1) .fun-btn").click();
                            }
                            obj.find(".parts:eq(1) .price-box").eq(finalNum-1).find("input[name='id']").val(modeList[i].id);
                            obj.find(".parts:eq(1) .price-box").eq(finalNum-1).find("input[name='amount']").val(modeList[i][amountStr]);
                            obj.find(".parts:eq(1) .price-box").eq(finalNum - 1).find("input[name='chargeLimit']").val(modeList[i].chargeLimit);
                            if (detail.deliveryAcceptable === 1) {//有关
                                obj.find(".parts:eq(1) .price-box").eq(finalNum - 1).find("select").val(modeList[i].chargeStage);
                            }
                        }
                    }
                } else {
                    $("#editServiceNoCycle .oneTime [name=\"chargeStage\"]").val(modeList[0].chargeStage);
                    $("#editServiceNoCycle .oneTime [name=\"chargeLimit\"]").val(modeList[0].chargeLimit);
                    $("#editServiceNoCycle .oneTime").show().siblings().hide();
                }
                $("#editServiceNoCycle").data("mode", 'old');
                bounce_Fixed.show($("#editServiceNoCycle"));
                $("#editServiceNoCycle [need]:visible").each(function(){
                    let key = $(this).data("name");
                    $(this).html(detail[key]);
                });
                break;
            case 'updateServiceRecords':
                detail = JSON.parse($("#serviceProjectScan .serviceData").html());
                updateServiceRecords(detail.id, 1);
                break;
            case 'editModeLog':
                detail = JSON.parse($("#serviceProjectScan .serviceData").html());
                updateServiceRecords(detail.id, 2);
                break;
            case 'serviceEditLogScan':
                serviceEditLogScan($(this));
                break;
            case 'chargeEditLogScan':
                chargeEditLogScan($(this));
                break;
            case 'updateTrailLogScan':
                updateTrailLogScan($(this));
                break;
            case 'reModeSet':
                $("#reModTip").data("source", "edit");
                bounce_Fixed2.show($("#reModTip"));
                break;
            case 'reModeSetting':
                $("#reModTip").data("source", "add");
                bounce_Fixed2.show($("#reModTip"));
                break;
            case 'chargeUpdate':
                detail = JSON.parse($("#serviceProjectScan .serviceData").html());
                bounce_Fixed.show($("#editServiceNoCycle"));
                $("#editServiceNoCycle [need]:visible").each(function(){
                    let key = $(this).data("name");
                    $(this).html(detail[key]);
                });
                $("#editServiceNoCycle .byCycle").show().siblings().hide();
                $("#editServiceNoCycle .byCycle [name=\"periodUnit\"]").val(detail.periodUnit)
                switchPeriod($("#editServiceNoCycle .byCycle [name=\"periodUnit\"]"));
                $("#editServiceNoCycle .byCycle [name=\"periodDuration\"]").val(detail.periodDuration)
                $("#editServiceNoCycle .byCycle [name=\"chargeStage\"]").val(detail.serviceCharges[0].chargeStage);
                $("#editServiceNoCycle .byCycle [name=\"chargeLimit\"]").val(detail.serviceCharges[0].chargeLimit);
                break;
        }
    });
    $("#modeSetting .dotItem").on("click",".changeDot", function () {
        let idx = $(this).index();
        let pInx = $("#modeSetting .dotItem").index($(this).parents(".dotItem"));
        if ($(this).find("i").hasClass("fa-circle-o")){
            $(this).find("i").attr("class", "fa fa-dot-circle-o");
            $(this).siblings().find("i").attr("class", "fa fa-circle-o");
            if (pInx === 1) {
                if (idx === 1) {
                    $("#modeSetting .dotItem:eq(2)").hide();
                    $("#modeSetting .dotItem:eq(3)").hide();
                } else if (idx === 2) {
                    $("#modeSetting .dotItem:eq(2)").show();
                    $("#modeSetting .dotItem:eq(2)").find("i").attr("class", "fa fa-circle-o");;
                }
            } else if (pInx === 2) {
                if (idx === 1) {
                    $("#modeSetting .dotItem:eq(3)").show();
                } else if (idx === 2) {
                    $("#modeSetting .dotItem:eq(3)").hide();
                }
            }
        }
    });
    $("#serviceInitChoose").on("click", '.changeDot', function () {
        if ($(this).find("i").hasClass("fa-circle-o")){
            $(this).find("i").attr("class", "fa fa-dot-circle-o");
            $(this).siblings().find("i").attr("class", "fa fa-circle-o");
        }
    });
    $(".cateChoose").on("click", '.fa', function () {
        let type = $(this).data("type");
        let val = $(this).data("val");
        if ($(this).hasClass("fa-circle-o")) {
            $(this).attr("class", "fa fa-circle");
            $(this).parent().siblings("div").find("span").attr("class", "fa fa-circle-o");
            if (type === 1 && val === 5 || type === 2 && val === 2) {
                $(this).parent().next("input").prop("disabled", false);
            }else {
                $(this).parent().siblings("input").prop("disabled", true).val("");
            }
        } else {
            $(this).attr("class", "fa fa-circle-o");
            if (type === 1 && val === 5 || type === 2 && val === 2) $(this).parent().next("input").prop("disabled", true).val("");
        }
    });
    $("#trialSetting").on("click", '.typeDot', function () {
        let iCur = $(this).find("i");
        let type = iCur.data("type");
        if (iCur.hasClass("fa-circle-o")){
            iCur.attr("class", "fa fa-circle");
            $(this).siblings().find("i").attr("class", "fa fa-circle-o");
            $("#trialSetting .mar:eq(0)").show();
            let ttl = ``;
            if (type === 2) {
                ttl = `体验`;
            } else {
                ttl = `试用`;
            }
            $(".tagCon").html(ttl);
            let options = `<option value="">请选择</option>
                            <option value="1">合同签订后</option>
                            <option value="2">${ttl}开始前</option>
                            <option value="3">${ttl}开始后</option>
                            <option value="4">${ttl}结束后</option>`;
            $("#trialSetting select[name='chargeStage']").html(options);
        } else {
            iCur.attr("class", "fa fa-circle-o");
            $("#trialSetting .mar").hide();
            $("#trialSetting .trialSettingBtn").hide();
        }
    });
    $("#trialSetting").on("click", '.chargeDot', function () {
        let iCur = $(this).find("i");
        let type = iCur.data("type");
        if (iCur.hasClass("fa-circle-o")){
            let ttl = $(".tagCon:eq(0)").html();
            let cycle = 1;
            let icon = $("#trialSetting").data("type");
            iCur.attr("class", "fa fa-circle");
            $(this).siblings().find("i").attr("class", "fa fa-circle-o");
            if (icon === 'add') {
                cycle = $("#addService").data("cycle");
            } else {
                let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
                cycle = detail.isPeriodical;
            }
            if (cycle === 1) {
                $("#trialSetting .mar:eq(1)").hide();
                if (type === 1) {
                    $("#trialSetting .mar:eq(2)").show();
                } else {
                    $("#trialSetting .mar:eq(2)").hide();
                }
                $("#trialSetting .trialSettingBtn").show();
            } else if (cycle === 0) {
                if (type === 1) {
                    $("#trialSetting .mar:gt(0)").show();
                    if ($("#trialSetting .mar:eq(1) .fa-circle").length > 0) {
                        $("#trialSetting .trialSettingBtn").show();
                    } else {
                        $("#trialSetting .trialSettingBtn").hide();
                    }
                } else {
                    $("#trialSetting .mar:gt(0)").hide();
                    $("#trialSetting .trialSettingBtn").show();
                }
            }
        } else {
            iCur.attr("class", "fa fa-circle-o");
            $("#trialSetting .mar:gt(0)").hide();
            $("#trialSetting .trialSettingBtn").hide();
        }
    });
    $("#trialSetting").on("click", '.acceptDot', function () {
        let iCur = $(this).find("i");
        let type = iCur.data("type");
        if (iCur.hasClass("fa-circle-o")){
            iCur.attr("class", "fa fa-circle");
            $(this).siblings().find("i").attr("class", "fa fa-circle-o");
            let ttl = $(".tagCon:eq(0)").html();
            let options = ``;
            if (type === 1) {
                options = `<option value="">请选择</option>
                            <option value="1">合同签订后</option>
                            <option value="2">${ttl}开始前</option>
                            <option value="3">${ttl}开始后</option>
                            <option value="5">交付前</option>
                            <option value="6">交付后</option>
                            <option value="7">通过验收后</option>
                            <option value="4">${ttl}结束后</option>`;
            } else {
                options = `<option value="">请选择</option>
                            <option value="1">合同签订后</option>
                            <option value="2">${ttl}开始前</option>
                            <option value="3">${ttl}开始后</option>
                            <option value="4">${ttl}结束后</option>`;
            }
            $("#trialSetting select[name='chargeStage']").html(options);
            $("#trialSetting .trialSettingBtn").show();
        } else {
            iCur.attr("class", "fa fa-circle-o");
            $("#trialSetting .trialSettingBtn").hide();
        }
    });
})
// creator: 李玉婷，2022-08-10 08:07:46，服务项目列表
function getServiceList(name,enabled, currentPageNo,pageSize){
    let param = {
        "name": name,
        "enabled": enabled,
        "pageSize": pageSize,
        "currentPageNo": currentPageNo
    }
    $.ajax({
        url:"../saleService/list",
        data: param,
        success:function(data){
            let list = data.data;
            let html = ``;
            let pageInfo = data.page;
            var cur = pageInfo["currentPageNo"];
            var totalPage = pageInfo["totalPage"];
            let paramJson = {"name": name,"enabled": enabled}
            paramJson = JSON.stringify(paramJson);
            for(let i=0;i<list.length;i++){
                let detail = ``;
                if (list[i].isPeriodical === 1) {
                    detail = `每${list[i].periodDuration}${changeStr(list[i].periodUnit, 'periodUnit')}收一次`;
                } else {
                    detail = `本项目不按周期收费。`
                    if (list[i].chargeNum === 1) {
                        detail = `与交付/验收${list[i].deliveryAcceptable === 0? '无':'有'}关，一次收取完毕`;
                    } else if (list[i].chargeNum > 1) {
                        detail = `与交付/验收${list[i].deliveryAcceptable === 0? '无':'有'}关，需按${list[i].enteringType === 1? '金额':'比例'}分${list[i].chargeSize}次收取`;
                    }
                }
                let price = 0;//含税单价>普票单价>不开票单价>参考单价
                if (handleNull(list[i].unitPrice) !== "") {
                    price = list[i].unitPrice;
                } else if (handleNull(list[i].unitPriceNotax) !== "") {
                    price = list[i].unitPriceNotax;
                } else if (handleNull(list[i].unitPriceInvoice) !== "") {
                    price = list[i].unitPriceInvoice;
                } else if (handleNull(list[i].unitPriceNoinvoice) !== "") {
                    price = list[i].unitPriceNoinvoice;
                } else if (handleNull(list[i].unitPriceReference) !== "") {
                    price = list[i].unitPriceReference;
                }
                let stopStr = enabled === 0? `<td>${list[i].updateName} ${new Date(list[i].enabledTime).format('yyyy-MM-dd hh:mm:ss')}</td>`: ""
                html += `<tr><td>${list[i].code}/${list[i].name}</td>
                         <td>${price}</td>
                         <td>${detail}</td>
                         ${stopStr}
                         <td>${list[i].createName} ${new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss')}</td>
                        <td>
                            <span class="ty-color-blue" type="btn" data-fun="seeService">查看</span>
                            <span class="ty-color-blue" type="btn" data-fun="stopService" data-able="${1^enabled}">${enabled === 1 ?'停用':'复用'}</span>
                            <span class="ty-color-blue" type="btn" data-fun="delService">删除</span>
                            <span class="hd">${JSON.stringify(list[i])}</span>
                       </td></tr>`;
            }
            let mainObj = ``,icon = 1;
            if ($(".mainCon1").is(":visible")) {
                mainObj = $(".mainCon1");
            } else if ($(".mainCon2").is(":visible")) {
                icon = 2;
                mainObj = $(".mainCon2");
            } else if ($(".mainCon3").is(":visible")) {
                icon = 3;
                mainObj = $(".mainCon3");
            }
            setPage( $("#ye" + icon) , cur ,  totalPage , "serviceProject" , paramJson);
            mainObj.find(".num").html(pageInfo.totalResult);
            mainObj.find("table tbody").html(html);
        }
    })
}
// creator: 李玉婷，2022-04-12 08:57:55，显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    num === 1 || num === 2? $(".search input").val(""): "";
    $(".mainCon" + num).show().siblings().hide();
}
// creator: 李玉婷，2022-04-12 09:13:11，新增服务项目
function serviceInitChoose(){
    if ($("#serviceInitChoose .fa-dot-circle-o").length > 0) {
        let tip = ``;
        let val = $("#serviceInitChoose .fa-dot-circle-o").parent().data("type");
        $("#addService").data("cycle", val).removeData("modeSetting");
        $("#addService input").val("");
        $("#addService select").val("");
        $("#addService select[name='periodDuration']").prop("disabled", true);
        invoiceSettings($("#addService select[name='taxRate']"), "");
        if (val === 1) {
            tip = `请录入按周期（如按年、按月）收费的项目！`;
            $("#addService .byCycle").show();
            $("#addService .noByCycle").hide();
        } else {
            tip = `请录入不按周期收费的项目！`;
            $("#addService .byCycle").hide();
            $("#addService .noByCycle").show();
            $("#addService .modeNoSetted").show().siblings().hide();
            getUnitList($("#add_unitSelect"), 2);
        }
        $(".trialingShow tr:gt(0)").remove();
        $(".trialState").data("state", 0).html("未设置");
        $("#addService input").prop("disabled", false);
        $("#addService .headTip").html(tip);
        bounce.show($("#addService"));
    }
}
// creator: 李玉婷，2022-04-12 09:48:09，收费周期-年、个月切换
function switchPeriod(obj){
    let val = obj.val();
    let options = ``;
    if (val !== "") {
        obj.prev().val("").prop("disabled", false);
        if (val === '7' || val === 7) {
            options = `<option value="1">1</option>`;
        } else if (val === '4' || val === 4) {
            options =  `<option value="1">1</option>
                      <option value="2">2</option>
                      <option value="3">3</option>
                      <option value="4">4</option>
                      <option value="5">5</option>
                      <option value="6">6</option>
                      <option value="7">7</option>
                      <option value="8">8</option>
                      <option value="9">9</option>
                      <option value="10">10</option>
                      <option value="11">11</option>`;
        }
        obj.prev().html(options);
    } else {
        obj.prev().val("").prop("disabled", true);
    }
}
// creator: 李玉婷，2022-04-12 10:21:53，模式设置确定
function modeSettingSure(){
    let empty = 0;
    $("#modeSetting .dotItem:visible").each(function (){
        if ($(this).find(".fa-dot-circle-o").length === 0) empty++;
    });
    if (empty <= 0) {
        let headTip = ``;
        let setting = {};
        let source = $("#modeSetting").data("source")
        $("#modeSetting .dotItem:visible").each(function (){
            let idx = $(this).index();
            let val = $(this).find(".fa-dot-circle-o").data("type");
            if (idx === 0) {
                setting.relate = val;
            } else if (idx === 1) {
                setting.few = val;
            } else if (idx === 2) {
                setting.type = val;
            } else if (idx === 3) {
                setting.limit = val;
            }
        });
        let obj = source === 'add' ?  $("#addService") :  $("#editServiceNoCycle");
        obj.data("modeSetting", setting);
        let options = ``;
        if (setting.few === 1) {
            if (setting.relate === 1) {
                options = `
                     <option value="">请选择</option>
                     <option value="1">合同签订后</option>
                     <option value="2">服务开始前</option>
                     <option value="3">服务开始后</option>
                     <option value="4">交付前</option>
                     <option value="5">交付后</option>
                     <option value="10">通过验收后</option>
                     <option value="13">服务结束后</option>
                    `;
            } else {
                options = `
                     <option value="">请选择</option>
                     <option value="1">合同签订后</option>
                     <option value="2">服务开始前</option>
                     <option value="3">服务开始后</option>
                     <option value="13">服务结束后</option>
                    `;
            }
            obj.find(".oneTime").show().siblings().hide();
            obj.find(".oneTime select").html(options);
            headTip = `请录入服务费一次性收取，且收取与交付/验收${setting.relate === 1? '有': '无'}关的项目！`;
        } else {
            headTip = `请录入需分多次收费，且收费与交付/验收${setting.relate === 1? '有': '无'}关的项目！`;
            if (setting.relate === 1) {
                obj.find(".many_relate").show().siblings().hide();
                obj.find(".modeSetted").find(".lenTip:eq(0)").html("0/20");
                obj.find(".modeSetted").find(".lenTip:eq(1)").html("0/40");
            } else {
                obj.find(".many_noRelate").show().siblings().hide();
            }
            obj.find(".manyTime").show().siblings().hide();
            obj.find(".addOne").data("carry", setting.type);
            obj.find(".amountType").html(setting.type === 1?'金额<input name="amount" type="text" oninput="clearNoNumN(this, 2)"/>元':'比例<input name="amount" type="text" oninput="limitNumber(this)"/>%');
        }
        obj.find(".headTip").html(headTip);
        obj.find(".modeSetted input").val("");
        obj.find(".modeSetted select").val("");
        obj.find(".modeSetted").find(".parts").find(".price-box:gt(0)").remove();
        bounce_Fixed.cancel();
        if (source === 'edit'){
            $("#editServiceNoCycle").data("mode", 'new');
            bounce_Fixed.show($("#editServiceNoCycle"));
            let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
            $("#editServiceNoCycle [need]:visible").each(function(){
                let key = $(this).data("name");
                $(this).html(detail[key]);
            });
        } else{
            obj.find(".modeSetted").show().siblings().hide();
        }
    }
}
function addServiceSure(){
    let empty = true, filed = 0;
    $("#addService [require]:visible").each(function(){
        let val = $(this).val();
        if (val === "") empty = false;
    });
    $("#addService .priceForm [need]").each(function(){
        let val = $(this).val();
        let name = $(this).attr("name");
        if (name === 'taxRate') {
            if ($(this).val() === "") {
                if ($("#addService input[name = 'unitPriceNotax']").val() !== "" || $("#addService input[name = 'unitPrice']").val() !== "")
                    empty = false;
            } else {
                if ($("#addService input[name = 'unitPriceNotax']").val() !== "" && $("#addService input[name = 'unitPrice']").val() !== "") {
                    filed++;
                } else {
                    empty = false;
                }
            }
        } else {
            if (val !== "") filed++;
        }
    });
    if ($("#addService .manyTime").is(":visible")) {
        $("#addService .price-box:visible").each(function (){
            let _this = $(this)
            let num = $(this).find("input:visible").length + $(this).find("select:visible").length;
            let filled = 0;
            _this.find("input:visible").each(function (){
                if ($(this).val() !== "") filled ++;
            })
            _this.find("select:visible").each(function (){
                if ($(this).val() !== "") filled ++;
            })
            if (filled > 0 && num !== filled) empty = false;
        })
    }
    if (empty && filed > 0) {
        let serviceChargeList = [], info = ``;
        let cycle = $("#addService").data("cycle");//是否周期性 0-否 1-是
        let param = `isPeriodical=${cycle}`;
        let trialState = $(".trialState").data("state");//类型:1-正式,2-体验,3-试用, 0-未设置
        $("#addService table input:visible").each(function(){
            let val = $(this).val();
            let name = $(this).attr("name");
            if (name === 'code' || name === 'name' || name === 'priceDesc') {
                param += `&${name}=${escape(encodeURIComponent(val))}`
            } else {
                param += `&${name}=${val}`
            }
        });
        $("#addService table select:visible").each(function(){
            let val = $(this).val();
            let name = $(this).attr("name");
            param += `&${name}=${val}`;
        });
        param += `&enabled=1&previousId=0&operation=1`;//1-增,2-删,3-改(基本信息),4-修改模式,8-启/复用,9-停用
        if (trialState === 1) {
            info = JSON.parse($("#trialingData").html());
        }
        if (cycle === 1) {
            let pD = $("#addService .byCycle select[name='periodDuration']").val();
            let pT = $("#addService .byCycle select[name='periodUnit']").val();
            param += `&periodDuration=`+ pD;
            param += `&periodUnit=` + pT;
            let item = {
                "chargeLimit": $("#addService .byCycle input[name='chargeLimit']").val(),
                "chargeStage": $("#addService .byCycle select[name='chargeStage']").val()
            }
            serviceChargeList.push(item);
            if (pT === '4') {
                if (info.trailDays > 30*pD){
                    layer.msg("客户可试用的天数设置的不合理！");
                    return false
                }
            } else if (pT === '7') {
                if (info.trailDays > 365){
                    layer.msg("客户可试用的天数设置的不合理！");
                    return false
                }
            }
        } else {
            let setting = $("#addService").data("modeSetting");
            let chargeNum = 0;
            if (setting) {
                param += `&deliveryAcceptable=${setting.relate}`;
                if (setting.few === 1){
                    chargeNum = 1;
                    let item = {
                        "chargeLimit": $("#addService .oneTime input[name='chargeLimit']").val(),
                        "chargeStage": $("#addService .oneTime select[name='chargeStage']").val()
                    }
                    serviceChargeList.push(item);
                } else {
                    let allow = 0;
                    chargeNum = 2;
                    param += `&enteringType=${setting.type}`;
                    if (setting.type === 1) param += `&upperType=${setting.limit}`;
                    $("#addService .manyTime .parts:visible").each(function (){
                        let index = $(this).index();
                        let temp = {}
                        if (index === 0) {
                            let val = $(this).find("input[name='amount']").val();
                            if ( val !== ""){
                                temp.chargeNum = 1;
                                temp.chargeType = 2;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                temp.chargeStage = $(this).find("select").val();
                                temp.chargeLimit = $(this).find("input[name='chargeLimit']").val();
                                allow += Number(val);
                                if (setting.type === 1) {
                                    temp.amount = val;
                                }else {
                                    temp.percentage = val;
                                }
                                serviceChargeList.push(temp);
                            }
                        } else if (index === 1) {
                            $(this).find(".price-box").each(function (){
                                let _this = $(this)
                                let val = _this.find("input[name='amount']").val();
                                if ( val !== "") {
                                    let temp = {}
                                    temp.chargeNum = 1;
                                    temp.chargeType = 4;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                    temp.chargeLimit = _this.find("input[name='chargeLimit']").val();
                                    allow += Number(val);
                                    if (setting.type === 1) {
                                        temp.amount = val;
                                    } else {
                                        temp.percentage = val;
                                    }
                                    if (setting.relate === 1) {
                                        temp.chargeStage = _this.find("select").val();
                                    } else {
                                        temp.chargeStage = 13;
                                    }
                                    serviceChargeList.push(temp);
                                }
                            })
                        } else if (index === 2) {
                            $(this).find(".price-box").each(function (){
                                let _this = $(this)
                                let val = _this.find("input[name='amount']").val();
                                if ( val !== "") {
                                    let temp = {}
                                    temp.chargeNum = 1;
                                    temp.chargeType = 3;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                    allow += Number(val);
                                    if (setting.relate === 1) {
                                        temp.chargeStage = _this.find("select").val();
                                        temp.chargeLimit = _this.find("input[name='chargeLimit']").val();
                                        temp.stageName = _this.find("input[name='stageName']").val();
                                        temp.stageDesc = _this.find("input[name='stageDesc']").val();
                                    }
                                    if (setting.type === 1) {
                                        temp.amount = val;
                                    } else {
                                        temp.percentage = val;
                                    }
                                    serviceChargeList.push(temp);
                                }
                            })
                        }
                    });
                    let amount = 100;
                    if (setting.type === 1) {
                        let price = ["","unitPriceNotax","unitPrice","unitPriceInvoice","unitPriceNoinvoice","unitPriceReference"]
                        amount = $("#addService input[name="+price[setting.limit]+"]").val();
                    }
                    if (allow !== Number(amount)){
                        layer.msg("<p>操作失败！</p><p>各期款项的比例或金额有误或没录全！</p>");
                        return false;
                    }
                    if (!empty){
                        layer.msg("<p>操作失败！</p><p>因为还有必填项尚未填写！</p>");
                        return false;
                    }
                }
            }
            param += `&chargeNum=${chargeNum}`;
        }
        if(trialState === 1) {
            param += `&state=1`;
        } else {
            param += `&state=0`;
        }
        var settings = {
            "url": "../saleService/add?" + param,
            "method": "POST",
            "timeout": 1000000,
            "headers": {
                "Content-Type": "application/json"
            },
            "data": JSON.stringify(serviceChargeList),
            beforeSend:function(){ loading.open() ; }
        };
        $.ajax(settings).done(function(response){
            if (response === 2) {
                loading.close() ;
                layer.msg("新增失败，名称或者代码重复！");
            } else{
                bounce.cancel();
                if (trialState === 1) {
                    info = JSON.parse($("#trialingData").html());
                    let sendInfo = `mainItem=${response.data.id}`;
                    for(var key in info) {
                        if (key !== 'serviceChargeList') sendInfo += `&${key}=${info[key]}`;
                    }
                    var settings1 = {
                        "url": "../saleService/add?" + sendInfo,
                        "method": "POST",
                        "timeout": 300000000,
                        "headers": {
                            "Content-Type": "application/json"
                        },
                        "data": info.serviceChargeList,
                        beforeSend:function(){ loading.open() ; }
                    };
                    $.ajax(settings1).done(function(response){})
                }
                getServiceList("",1, 1,20);
            }
        })
    } else {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
    }
}
function updateServiceSure(){
    let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
    let serviceChargeList = [], cycle= detail.isPeriodical;
    let empty = true, filed = 0;
    let param = `id=${detail.id}&isPeriodical=${cycle}`;
    if ($("#editService").is(':visible')){
        $("#editService [require]:visible").each(function(){
            let val = $(this).val();
            if (val === "") empty = false;
        });
        $("#editService .priceForm [need]").each(function(){
            let val = $(this).val();
            let name = $(this).attr("name");
            if (name === 'taxRate') {
                if ($(this).val() === "") {
                    if ($("#editService input[name = 'unitPriceNotax']").val() !== "" || $("#editService input[name = 'unitPrice']").val() !== "")
                        empty = false;
                } else {
                    if ($("#editService input[name = 'unitPriceNotax']").val() !== "" && $("#editService input[name = 'unitPrice']").val() !== "") {
                        filed++;
                    } else {
                        empty = false;
                    }
                }
            } else {
                if (val !== "") filed++;
            }
        });
        if (empty && filed > 0) {
            $("#editService table input:visible").each(function(){
                let val = $(this).val();
                let name = $(this).attr("name");
                if (name === 'code' || name === 'name' || name === 'priceDesc') {
                    param += `&${name}=${escape(encodeURIComponent(val))}`
                } else if (name === 'unitPriceNotax' || name === 'unitPrice' || name === 'unitPriceInvoice' || name === 'unitPriceNoinvoice' || name === 'unitPriceReference') {
                    if (val ==="" && handleNull(detail[name]) !== "") {
                        param += `&${name}=${-1}`
                    } else {
                        param += `&${name}=${val}`
                    }
                } else {
                    param += `&${name}=${val}`
                }
            });
            $("#editService table select:visible").each(function(){
                let val = $(this).val();
                let name = $(this).attr("name");
                param += `&${name}=${val}`;
            });
            param += `&enabled=1&previousId=0&operation=3`;//1-增,2-删,3-改(基本信息),4-修改状态,5-改收费信息，6-其他修改,8-启/复用,9-停用
            var settings = {
                "url": "../saleService/update?" + param,
                "method": "POST",
                "timeout": 100000,
                "headers": {
                    "Content-Type": "application/json"
                },
                "data": JSON.stringify(serviceChargeList),
                beforeSend:function(){ loading.open() ; }
            };
            $.ajax(settings).done(function(response){
                if (response === 2) {
                    loading.close() ;
                    layer.msg("新增失败，名称或者代码重复！");
                } else if (response.success === 1){
                    bounce_Fixed.cancel();
                    let num = $(".mainCon").index($(".mainCon:visible")) + 1;
                    let json = JSON.parse($(".mainCon" + num).find(".yeCon").siblings(".json").html());
                    seeService(detail.id);
                    getServiceList(json.name,json.enabled, 1,20);
                }
            })
        } else {
            layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
        }
    } else {
        $("#editServiceNoCycle [require]:visible").each(function(){
            let val = $(this).val();
            if (val === "") empty = false;
        });
        if (cycle === 0) {
            if ($("#editServiceNoCycle .manyTime").is(":visible")) {
                $("#editServiceNoCycle .price-box:visible").each(function (){
                    let _this = $(this)
                    let num = $(this).find("input:visible").length + $(this).find("select:visible").length;
                    let filled = 0;
                    _this.find("input:visible").each(function (){
                        if ($(this).val() !== "") filled ++;
                    })
                    _this.find("select:visible").each(function (){
                        if ($(this).val() !== "") filled ++;
                    })
                    if (filled > 0 && num !== filled) empty = false;
                })
            }
        }
        if (empty) {
            param += `&operation=5`;
            if (cycle === 1) {
                param += `&periodDuration=${$("#editServiceNoCycle .byCycle select[name='periodDuration']").val()}`
                param += `&periodUnit=${$("#editServiceNoCycle .byCycle select[name='periodUnit']").val()}`
                let item = {
                    "id": detail.serviceCharges[0].id,
                    "chargeLimit": $("#editServiceNoCycle .byCycle input[name='chargeLimit']").val(),
                    "chargeStage": $("#editServiceNoCycle .byCycle select[name='chargeStage']").val()
                }
                serviceChargeList.push(item);
            } else {
                let mode = $("#editServiceNoCycle").data("mode");
                let setting = {}, dell = [], chargeNum = 0;
                if (mode === 'new') {
                    setting = $("#editServiceNoCycle").data("modeSetting");
                    detail.serviceCharges.forEach((item) => {//删除原模式数据用的
                        dell.push(item.id)
                    })
                    param += `&deleteCharges=${dell.join(",")}`;
                } else {
                    setting = {
                        relate: detail.deliveryAcceptable, // 相关性
                        few: detail.chargeNum,// 一次性、多次性
                        type: detail.enteringType,// 录入金额、比例
                        limit: detail.upperType// 金额上限
                    }
                }
                chargeNum = setting.few
                param += `&deliveryAcceptable=${setting.relate}`;
                if (setting.few === 1){
                    let item = {
                        "chargeLimit": $("#editServiceNoCycle .oneTime input[name='chargeLimit']").val(),
                        "chargeStage": $("#editServiceNoCycle .oneTime select[name='chargeStage']").val()
                    }
                    if (mode === 'old') item.id = detail.serviceCharges[0].id
                    serviceChargeList.push(item);
                } else {
                    param += `&enteringType=${setting.type}`;
                    let allow = 0;
                    if (setting.type === 1) param += `&upperType=${setting.limit}`;
                    $("#editServiceNoCycle .manyTime .parts:visible").each(function (){
                        let index = $(this).index();
                        let temp = {}
                        if (index === 0) {
                            let val = $(this).find("input[name='amount']").val();
                            if (val !== ""){
                                if (setting.type === 1) {
                                    temp.amount = val;
                                }else {
                                    temp.percentage = val;
                                }
                                temp.chargeNum = 1;
                                temp.chargeType = 2;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                temp.chargeStage = $(this).find("select").val();
                                temp.chargeLimit = $(this).find("input[name='chargeLimit']").val();
                                allow += Number(val);
                                if (mode === 'old') temp.id = $(this).find("input[name='id']").val()
                                serviceChargeList.push(temp);
                            }
                        } else if (index === 1) {
                            $(this).find(".price-box").each(function (){
                                let temp = {}
                                let _this = $(this)
                                let val = _this.find("input[name='amount']").val();
                                if (val !== "") {
                                    temp.chargeNum = 1;
                                    temp.chargeType = 4;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                    temp.id = mode === 'old' ? _this.find("input[name='id']").val() : "";
                                    temp.chargeLimit = _this.find("input[name='chargeLimit']").val();
                                    allow += Number(val);
                                    if (setting.relate === 1) {
                                        temp.chargeStage = _this.find("select").val();
                                    } else {
                                        temp.chargeStage = 13;
                                    }
                                    if (setting.type === 1) {
                                        temp.amount = val;
                                    } else {
                                        temp.percentage = val;
                                    }
                                    serviceChargeList.push(temp);
                                }
                            })
                        } else if (index === 2) {
                            $(this).find(".price-box").each(function (){
                                let _this = $(this)
                                let val = _this.find("input[name='amount']").val();
                                if (val !== "") {
                                    let temp = {}
                                    temp.id = mode === 'old' ? _this.find("input[name='id']").val() : "";
                                    temp.chargeNum = 1;
                                    temp.chargeType = 3;//收费类型1-全款,2-预付款,3-中间款,4-尾款
                                    allow += Number(val);
                                    if (setting.relate === 1) {
                                        temp.chargeStage = _this.find("select").val();
                                        temp.chargeLimit = _this.find("input[name='chargeLimit']").val();
                                        temp.stageName = _this.find("input[name='stageName']").val();
                                        temp.stageDesc = _this.find("input[name='stageDesc']").val();
                                    }
                                    if (setting.type === 1) {
                                        temp.amount = val;
                                    } else {
                                        temp.percentage = val;
                                    }
                                    serviceChargeList.push(temp);
                                }
                            })
                        }
                    });
                    let amount = 100;
                    if (setting.type === 1) {
                        let price = ["","unitPriceNotax","unitPrice","unitPriceInvoice","unitPriceNoinvoice","unitPriceReference"]
                        amount = detail[price[setting.limit]];
                    }
                    if (allow !== Number(amount)){
                        layer.msg("<p>操作失败！</p><p>各期款项的比例或金额有误或没录全！</p>");
                        return false;
                    }
                }
                param += `&chargeNum=${chargeNum}`;
            }
            var settings = {
                "url": "../saleService/update?" + param,
                "method": "POST",
                "timeout": 100000,
                "headers": {
                    "Content-Type": "application/json"
                },
                "data": JSON.stringify(serviceChargeList),
                beforeSend:function(){ loading.open() ; }
            };
            $.ajax(settings).done(function(response){
                if (response === 2) {
                    loading.close()
                    layer.msg("新增失败，名称或者代码重复！");
                } else if (response.success === 1){
                    bounce_Fixed.cancel();
                    seeService(detail.id);
                    getServiceList("",1, 1,20);
                }
            })
        } else {
            layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
        }
    }
}
function invoiceSettings(curObj,selectedVal){
    var str = '<option value=""></option>';
    $.ajax({
        "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
        "async": false,
        "data": {},
        success: function (res) {
            var list = res["financeInvoiceSettings"];
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    if (list[i]["category"] == '1'){
                        var rate = list[i]["enableTaxTate"];
                        var rates = rate.split(",");
                        if (rates && rates.length >0){
                            for (var r = 0; r < rates.length; r++) {
                                var selectedStr = "";
                                if (selectedVal == rates[r]) {
                                    selectedStr = " selected='true' ";
                                }
                                str += "<option " + selectedStr + " value='" + rates[r] + "'>" + rates[r] + "%</option>";
                            }
                        }
                    }
                }
            }
            curObj.html(str);
        }
    });
}
// creator: 李玉婷，2020-08-10 11:08:33，服务项目-查看
function seeService(id){
    let modeHtml = ``,modeCharge = ``, chargeEditBtn = ``, trlStr = ``, trlTip = `未设置`;
    $("#serviceProjectScan #scan_trail tbody tr:gt(0)").remove();
    $(".trailUpdateBtns").hide();
    $.ajax({
        url: "../saleService/detail",
        data: {"id": id},
        success:function(data){
            let info = data.data;
            let serviceCharges = null;
            let headTip = ``;
            let trailItem = info.trailItem;
            var operList = info.startAndStopList, operStr = ``;
            $("#serviceProjectScan [need]").each(function(){
                let key = $(this).data("name");
                if( key == 'taxRate' ) {
                    $(this).html(handleNull(info[key]) !== "" ? info[key]+'%':'');
                } else {
                    $(this).html(info[key]);
                }
            });
            if (info.isPeriodical === 1) {
                serviceCharges = data.data.serviceCharges[0];
                headTip = `本项目按周期收费。`;
                modeCharge = `
                        <tr>
                            <td>收费周期</td>
                            <td><span>每<span>${info.periodDuration + changeStr(info.periodUnit, 'periodUnit')}</span>收取一次</span></td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td><span>${changeStr(serviceCharges.chargeStage, 'chargeStage') + serviceCharges.chargeLimit}</span>天内</td>
                        </tr>`;
                if (info.enabled === 1) {
                    chargeEditBtn = `<span class="def-btn fun-btn" data-type="chargeUpdate">修改</span>
                            <span class="def-btn fun-btn" data-type="editModeLog">修改记录</span>`;
                }
            } else {
                chargeEditBtn = `${info.enabled === 1?'<span class="def-btn fun-btn" data-type="editModeService">修改</span>':''}
                            <span class="def-btn fun-btn" data-type="editModeLog">修改记录</span>
                           ${info.enabled === 1?'<span class="def-btn fun-btn" data-type="reModeSet">重新设置</span>':''}`;
                if (info.chargeNum === 1) {
                    serviceCharges = data.data.serviceCharges[0];
                    modeCharge = `<tr>
                            <td width="30%">计量单位</td>
                            <td width="70%">${info.unit}</td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td><span><span>${changeStr(serviceCharges.chargeStage, 'chargeStage') + serviceCharges.chargeLimit}</span>天内</span></td>
                        </tr>`;
                    headTip = `本项目服务费一次性收取，且收取与交付/验收${info.deliveryAcceptable === 1? '有':'无' }关！`;
                } else  if (info.chargeNum > 1){
                    let amountStr= 0;
                    let enteringTypeStr = ``, enteringTypeUnit=``;
                    serviceCharges = data.data.serviceCharges;
                    enteringTypeStr = info.enteringType === 1? '应收取金额':'应收取比例';
                    enteringTypeUnit = info.enteringType === 1? '元':'%';
                    amountStr = info.enteringType === 1? 'amount':'percentage';
                    headTip = `本项目需分多次收费，且收取与交付/验收${info.deliveryAcceptable === 1? '有':'无' }关！`;
                    for (var i=0;i<serviceCharges.length;i++){
                        if (serviceCharges[i].chargeType === 2){
                            modeCharge +=
                                `<tr>
                                     <td>预付款 </td>
                                     <td>
                                         <span>${enteringTypeStr}：<span class="sm-con">${handleNull(serviceCharges[i][amountStr])}${enteringTypeUnit}</span></span>
                                         <span class="ty-right">应收取时间：<span>${changeStr(serviceCharges[i].chargeStage, 'chargeStage')}  ${handleNull(serviceCharges[i].chargeLimit)}天内</span></span>
                                     </td>
                                 </tr>`;
                        } else if (serviceCharges[i].chargeType === 3){
                            if (info.deliveryAcceptable === 1) {//有关
                                modeCharge +=
                                    `<tr>
                                        <td>
                                            ${serviceCharges[i].stageName}
                                        </td>
                                        <td>
                                            <span>${enteringTypeStr}：<span class="sm-con">${handleNull(serviceCharges[i][amountStr])}${enteringTypeUnit}</span></span>
                                            <span class="ty-right">应收取时间：<span>${changeStr(serviceCharges[i].chargeStage, 'chargeStage')}  ${handleNull(serviceCharges[i].chargeLimit)}天内</span></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            内容描述
                                        </td>
                                        <td>
                                            ${serviceCharges[i].stageDesc}
                                        </td>
                                    </tr>`;
                            } else {
                                modeCharge +=
                                    `<tr>
                                        <td>
                                            <span class="sm-ttl">中间款项</span>
                                        </td>
                                        <td>
                                            <span>${enteringTypeStr}：<span class="sm-con">${handleNull(serviceCharges[i][amountStr])}${enteringTypeUnit}</span></span>
                                        </td>
                                    </tr>`;
                            }
                        } else if (serviceCharges[i].chargeType === 4){
                            modeCharge +=
                                `<tr>
                                        <td>
                                            尾款
                                        </td>
                                        <td>
                                            <span>${enteringTypeStr}：<span class="sm-con">${handleNull(serviceCharges[i][amountStr])}${enteringTypeUnit}</span></span>
                                            <span class="ty-right">应收取时间：<span>
                                                  ${info.deliveryAcceptable === 0? '服务结束后': changeStr(serviceCharges[i].chargeStage, 'chargeStage')}
                                            ${serviceCharges[i].chargeLimit}天内</span></span>
                                        </td>
                                    </tr>`;
                        }
                    }
                    modeCharge += `<tr>
                            <td width="30%">计量单位</td>
                            <td width="70%">${info.unit}</td>
                        </tr>`;
                } else {
                    headTip = `本项目不按周期收费。`;
                    modeCharge = `<tr>
                            <td>计量单位</td>
                            <td>${info.unit}</td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td>尚未设置</td>
                        </tr>`;
                    info.enabled === 1 ?chargeEditBtn = `<span class="def-btn fun-btn" data-type="edit_modeSetting">模式设置</span>`: '';
                }
            }
            modeHtml = `<tr>
                            <td width="30%">概述</td>
                            <td width="70%">${headTip}</td>
                        </tr>
                        ${modeCharge}
                        <tr>
                            <td>项目说明</td>
                            <td>${info.memo}</td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td>${info.priceDesc}</td>
                        </tr>`;
            if (trailItem) {
                let ttl = trailItem.type === 2? '体验':'试用';
                let priceStr = `--`, accept = ``, trailCharges = `--`, len = trailItem.serviceCharges.length;
                if (trailItem.isChargeable === 1) {
                    //含税单价>普票单价>不开票单价>参考单价
                    if (handleNull(trailItem.unitPrice) !== "") {
                        priceStr = '税率'+handleNull(trailItem.taxRate)+'%，含税单价'+ trailItem.unitPrice+'元，不含税价'+ trailItem.unitPriceNotax+'元';
                    } else if (handleNull(trailItem.unitPriceInvoice) !== "") {
                        priceStr = '开普票时的开票单价'+trailItem.unitPriceInvoice+'元';
                    } else if (handleNull(trailItem.unitPriceNoinvoice) !== "") {
                        priceStr = '不开发票时的单价'+trailItem.unitPriceNoinvoice+'元';
                    } else if (handleNull(trailItem.unitPriceReference) !== "") {
                        priceStr = '参考单价'+trailItem.unitPriceReference+'元';
                    }
                    trailCharges = `${changeStr(trailItem.serviceCharges[len-1].chargeStage, 'trialStage', ttl) + trailItem.serviceCharges[len-1].chargeLimit}天内`;
                    if (info.isPeriodical === 0) {
                        accept = `<tr>
                            <td>${ttl}是否需交付/验收？</td>
                            <td>${trailItem.deliveryAcceptable === 1? '是':'否'}</td>
                        </tr>`;
                        if(trailItem.deliveryAcceptable === 1){
                            trailCharges = `${changeStr(trailItem.serviceCharges[len-1].chargeStage, 'exStage', ttl) + trailItem.serviceCharges[len-1].chargeLimit}天内`;
                        }
                    }
                }
                trlStr = `
                        <tr>
                            <td>${info.isPeriodical === 1 ? '可'+ ttl +'的天数':'某客户可'+ ttl +'的数量上限'}</td>
                            <td>${info.isPeriodical === 1 ? trailItem.trailDays+'天':trailItem.trailUpper+ info.unit}</td>
                        </tr>${accept}
                        <tr>
                            <td>${ttl}价格</td>
                            <td>${priceStr}</td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td>${trailCharges}</td>
                    </tr>`;
                trlTip = `已设置${trailItem.type === 2? '体验环节':'试用流程'}`;
                $(".trailUpdateBtns").show();
            }
            $("#scan_invoice_name").html("");
            $("#scan_invoice_formate").html("");
            if (info.itemInvoices && info.itemInvoices.length > 0) {
                for(var i=0;i<info.itemInvoices.length;i++){
                    if (info.itemInvoices[i].type === 1) {
                        $("#scan_invoice_name").html(handleNull(info.itemInvoices[i].nameResult));
                    } else if (info.itemInvoices[i].type === 2){
                        $("#scan_invoice_formate").html(handleNull(info.itemInvoices[i].nameResult));
                    }
                }
            }
            $("#serviceProjectScan .headTip").html(headTip);
            $("#serviceProjectScan #scan_mode").html(modeHtml);
            $("#serviceProjectScan #scan_trail_state").html(trlTip);
            $("#serviceProjectScan #scan_trail tbody").append(trlStr);
            $("#serviceProjectScan .chargeUpdateBtns").html(chargeEditBtn);
            $("#serviceProjectScan .serviceData").html(JSON.stringify(info));
            $("#serviceProjectScan #seeCreater").html(`<span class="oprationName">${info.createName}</span>` +new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss'));
            /* if (operList && operList.length > 0) {
                            for(var r=0;r<operList.length;r++) {
                                operStr +=
                                    `<div><span class="oper">${operList[r].enabled == 0? '暂停销售':'恢复销售'}</span> <span class="oprationName">${operList[r].updateName}</span>${new Date(operList[r].updateDate).format('yyyy-MM-dd hh:mm:ss')}</div>`;
                            }
                        }
                        $("#serviceProjectScan .reLog").html(operStr);*/
            bounce.show($("#serviceProjectScan"));
            if (info.enabled === 1) {
                $(".ableBtn").show();
            } else {
                $(".ableBtn").hide();
                operStr =
                    `<div><span class="oper">暂停销售</span> <span class="oprationName">${info.updateName}</span>${new Date(info.enabledTime).format('yyyy-MM-dd hh:mm:ss')}</div>`;
            }
            $("#serviceProjectScan .reLog").html(operStr);
        }
    })
}
// creator: 李玉婷，2020-08-10 11:33:12，服务项目-删除
function delService(id, operation){
    let param = {id: id}
    operation ? param.operation = operation: "";
    $.ajax({
        url:"../saleService/delete",
        data: param,
        beforeSend:function(){ loading.open() ; },
        success:function(data){
            if (operation) {
                loading.close()
                if (data) {
                    $("#delService").data("id", id);
                    bounce_Fixed.show($("#delService"));
                } else {
                    layer.msg("<p>操作失败！</p><p>因为已被套餐或合同用过的项目不能删除！</p>");
                }
            } else {
                bounce_Fixed.cancel();
                let num = $(".mainCon").index($(".mainCon:visible")) + 1;
                let json = JSON.parse($(".mainCon" + num).find(".yeCon").siblings(".json").html());
                getServiceList(json.name,json.enabled, 1,20);
            }
        }
    })
}
// creator: 李玉婷，2020-08-10 11:33:12，服务项目-停用
function stopService(id, enabled, operation){
    let param = {
        id: id,
        enabled: enabled
    }
    operation ? param.operation = operation: "";
    $.ajax({
        url:"../saleService/enable",
        data: param,
        beforeSend:function(){ loading.open() ; },
        success:function(data){
            if (operation) {
                loading.close()
                if (data) {
                    $("#stopService").data("id", id);
                    $("#stopService").data("enabled", enabled);
                    $("#tipMsg").html("<p>确定后，套餐或合同中将无法选到该项目。</p> <p>确定停用该项目吗？</p>");
                    bounce_Fixed.show($("#stopService"));
                } else {
                    layer.msg("<p>操作失败！</p><p>因为正被套餐使用着的项目不能停用！</p>");
                }
            } else {
                bounce_Fixed.cancel();
                let num = $(".mainCon").index($(".mainCon:visible")) + 1;
                let json = JSON.parse($(".mainCon" + num).find(".yeCon").siblings(".json").html());
                getServiceList(json.name,json.enabled, 1,20);
            }
        }
    })
}
function delServiceSure(){
    let id = $("#delService").data("id");
    delService(id);
}
function stopServiceSure(){
    let id = $("#stopService").data("id");
    let enabled = $("#stopService").data("enabled");
    stopService(id, enabled);
}
// creator: 李玉婷，2020-08-12 13:08:10，服务项目-查找
function serviceSearch(obj, type){
    let key = obj.prev("input").val();
    showMainCon(3);
    getServiceList(key, type, 1, 20);
}
function addUnit(idStr) {
    bounce_Fixed2.show($("#addUnit"));
    $("#addUnit input").val("");
    $("#updateType").val(idStr);
}
function addUnitOk(module) {
    var name = $("#unitName").val();
    $.ajax({
        "url":"../unit/addUnit.do",
        "data":{ 'name': name, "module": module },
        success:function(res) {
            var status = res['status'] ;
            if(status ==1){
                layer.msg("新增成功！");
                bounce_Fixed2.cancel();
                var idStr = $("#updateType").val();
                getUnitList($("#" + idStr), module);
            }else {
                var tipStr = '' ;
                if(status == 0){
                    tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                }else if(status == 2){
                    tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                }
                $("#tip1 .bonceCon").html(tipStr);
                bounce_Fixed2.show($("#tip1"));
            }
        }
    })
}
// creator: hxz，2020-09-02 14:51:41 获取计量单位列表、
function getUnitList(obj, module, selectedID) {
    // 　1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料
    $.ajax({
        "url":"../unit/selectUnitListForModule.do",
        "data":{ 'module': module },
        success:function(res) {
            var list = res['list'] ;
            var str = '<option value="">- - 请选择 - - </option>';
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++){
                    var item = list[i], seleStr = " ";
                    if(selectedID == item["id"]) { seleStr = " selected" }
                    str += '<option'+ seleStr +' value="'+ item['id'] +'">'+ item['name'] +'</option>';
                }
            }
            obj.html(str);
        }
    })
}
// creator: hxz，2020-09-02 14:51:41   计量单位设置
function unitAssign(obj) {
    var unitName = obj.children("option:selected").html();
    obj.siblings("input").val(unitName)
}
/*creator:lyt 2022/8/24 0024 下午 4:55 */
function updateServiceRecords(id, type){
    let url = ``;
    if (type === 1) {
        url = '../saleService/history?id='+ id + '&operation=3';
    } else if (type === 2) {
        url = '../saleService/chargeHistoryDetails?id='+ id + '&operation=5';
    }
    $.ajax({
        "url": url,
        "method": "GET",
        success:function(res) {
            let list = res.data;
            let curStaStr = ``;
            //var cur = data.pageInfo["currentPageNo"];
            //var totalPage = data.pageInfo["totalPage"];
            bounce_Fixed.show($("#updateServiceRecords"));
            if(list.length >0){
                let str = ``;
                //$("#ye_log").show();
                //setPage( $("#ye_log") , cur ,  totalPage , "businessNameEditLog" );
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    let n = list.length - 1;
                    if (type === 1) {
                        str += `<tr>
                             <td>${i === 0?"原始信息":"第"+i+"次修改后"}</td>
                             <td class="ty-td-control">
                                <span class="nodeBtn ty-color-blue" data-type="serviceEditLogScan">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                             <td>${handleNull(i === 0?item.createName:item.updateName)} ${new Date(i === 0?item.createDate: item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                        curStaStr = `<p>当前资料为第${n}次修改后的结果。</p><p>修改人：${handleNull(list[n].updateName) === ""? "系统": handleNull(list[n].updateName)} ${new Date(list[n].updateDate).format("yyyy-MM-dd hh:mm:ss")}</p> `
                    } else if (type === 2) {
                        str += `<tr>                   
                             <td>${i === 0?"原始信息":"第"+i+"次修改后"}</td>
                             <td class="ty-td-control">
                                <span class="nodeBtn ty-color-blue" data-type="chargeEditLogScan">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                             <td>${handleNull(item.createName)} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                        curStaStr = `<p>当前资料为第${n}次修改后的结果。</p><p>修改人：${handleNull(list[n].createName) === ""? "系统": handleNull(list[n].createName)} ${new Date(list[n].createDate).format("yyyy-MM-dd hh:mm:ss")}</p> `
                    }
                }
                $("#updateServiceRecords tbody").children(":gt(0)").remove();
                $("#updateServiceRecords tbody").append(str);
                $("#updateServiceRecords table").show();
            }else{
                //$("#ye_log").hide();
                $("#updateServiceRecords table").hide();
                curStaStr = `<p>当前资料尚未经修改。</p> `
            }
            $("#updateServiceRecords .curSta").html(curStaStr);
        }
    });
}
/*creator:lyt 2022/8/25 0025 下午 4:15 查看*/
function serviceEditLogScan(obj){
    var detail = JSON.parse(obj.siblings(".hd").html());
    $("#serviceLogScan #logscan_trail tbody tr:gt(0)").remove();
    $.ajax({
        "url":"../saleService/historyDetails?id="+ detail.id,
        success:function(data){
            let info = data.data;
            $("#serviceLogScan [need]").each(function(){
                let key = $(this).data("name");
                if( key == 'taxRate' ) {
                    $(this).html(handleNull(info[key]) !== "" ? info[key]+'%':'');
                } else {
                    $(this).html(info[key]);
                }
            });
            bounce_Fixed2.show($("#serviceLogScan"));
        }
    });
}
//creator:lyt 2023/5/31 0031 下午 2:04 收费修改历史查看
function chargeEditLogScan(obj){
    var detail = JSON.parse(obj.siblings(".hd").html());
    let modeHtml = ``,modeCharge = ``;
    $("#serviceLogScan #logscan_trail tbody tr:gt(0)").remove();
    $.ajax({
        "url":"../saleService/historyDetails?id="+ detail.itemHistory,
        success:function(data){
            let info = data.data;
            let serviceCharges = null;
            let headTip = ``;
            if (info.isPeriodical === 1) {
                serviceCharges = data.data.serviceCharges[0];
                headTip = `本项目按周期收费。`;
                modeCharge = `
                        <tr>
                            <td>收费周期</td>
                            <td><span>每<span>${info.periodDuration + changeStr(info.periodUnit, 'periodUnit')}</span>收取一次</span></td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td><span>${changeStr(serviceCharges.chargeStage, 'chargeStage') + serviceCharges.chargeLimit}</span>天内</td>
                        </tr>`;
            } else {
                if (info.chargeNum === 1) {
                    serviceCharges = data.data.serviceCharges[0];
                    modeCharge = `<tr>
                            <td width="30%">计量单位</td>
                            <td width="70%">${handleNull(info.unit)}</td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td><span><span>${changeStr(serviceCharges.chargeStage, 'chargeStage') + serviceCharges.chargeLimit}</span>天内</span></td>
                        </tr>`;
                    headTip = `本项目服务费一次性收取，且收取与交付/验收${info.deliveryAcceptable === 1? '有':'无' }关！`;
                } else  if (info.chargeNum > 1){
                    let amountStr= 0;
                    let enteringTypeStr = ``, enteringTypeUnit=``;
                    serviceCharges = data.data.serviceCharges;
                    enteringTypeStr = info.enteringType === 1? '应收取金额':'应收取比例';
                    enteringTypeUnit = info.enteringType === 1? '元':'%';
                    amountStr = info.enteringType === 1? 'amount':'percentage';
                    headTip = `本项目需分多次收费，且收取与交付/验收${info.deliveryAcceptable === 1? '有':'无' }关！`;
                    for (var i=0;i<serviceCharges.length;i++){
                        if (serviceCharges[i].chargeType === 2){
                            modeCharge +=
                                `<tr>
                                     <td>预付款 </td>
                                     <td>
                                         <span>${enteringTypeStr}：<span class="sm-con">${handleNull(serviceCharges[i][amountStr])}${enteringTypeUnit}</span></span>
                                         <span class="ty-right">应收取时间：<span>${changeStr(serviceCharges[i].chargeStage, 'chargeStage')}  ${handleNull(serviceCharges[i].chargeLimit)}天内</span></span>
                                     </td>
                                 </tr>`;
                        } else if (serviceCharges[i].chargeType === 3){
                            if (info.deliveryAcceptable === 1) {//有关
                                modeCharge +=
                                    `<tr>
                                        <td>
                                            ${serviceCharges[i].stageName}
                                        </td>
                                        <td>
                                            <span>${enteringTypeStr}：<span class="sm-con">${handleNull(serviceCharges[i][amountStr])}${enteringTypeUnit}</span></span>
                                            <span class="ty-right">应收取时间：<span>${changeStr(serviceCharges[i].chargeStage, 'chargeStage')}  ${handleNull(serviceCharges[i].chargeLimit)}天内</span></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            内容描述
                                        </td>
                                        <td>
                                            ${serviceCharges[i].stageDesc}
                                        </td>
                                    </tr>`;
                            } else {
                                modeCharge +=
                                    `<tr>
                                        <td>
                                            <span class="sm-ttl">中间款项</span>
                                        </td>
                                        <td>
                                            <span>${enteringTypeStr}：<span class="sm-con">${handleNull(serviceCharges[i][amountStr])}${enteringTypeUnit}</span></span>
                                        </td>
                                    </tr>`;
                            }
                        } else if (serviceCharges[i].chargeType === 4){
                            modeCharge +=
                                `<tr>
                                        <td>
                                            尾款
                                        </td>
                                        <td>
                                            <span>${enteringTypeStr}：<span class="sm-con">${handleNull(serviceCharges[i][amountStr])}${enteringTypeUnit}</span></span>
                                            <span class="ty-right">应收取时间：<span>
                                                  ${info.deliveryAcceptable === 0? '服务结束后': changeStr(serviceCharges[i].chargeStage, 'chargeStage')}
                                            ${serviceCharges[i].chargeLimit}天内</span></span>
                                        </td>
                                    </tr>`;
                        }
                    }
                    modeCharge += `<tr>
                            <td width="30%">计量单位</td>
                            <td width="70%">${handleNull(info.unit)}</td>
                        </tr>`;
                } else {
                    headTip = `本项目不按周期收费。`;
                    modeCharge = `<tr>
                            <td>计量单位</td>
                            <td>${handleNull(info.unit)}</td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td>尚未设置</td>
                        </tr>`;
                }
            }
            modeHtml = `<tr>
                            <td width="30%">概述</td>
                            <td width="70%">${headTip}</td>
                        </tr>
                        ${modeCharge}
                        <tr>
                            <td>项目说明</td>
                            <td>${handleNull(info.memo)}</td>
                        </tr>
                        <tr>
                            <td>价格说明</td>
                            <td>${handleNull(info.priceDesc)}</td>
                        </tr>`;
            $("#chargeLogScan #chargeLogScanCon").html(modeHtml);
            bounce_Fixed2.show($("#chargeLogScan"));
        }
    });
}
/*creator:lyt 2022/8/25 下午 5:57 重新设置模式确定*/
function reModSure(){
    let source = $("#reModTip").data("source");
    let obj = source === 'add' ?  $("#addService") :  $("#serviceProjectScan");
    obj.find(".modeNoSetted").show().siblings().hide();
    bounce_Fixed2.cancel();
    if (source === 'add') {
        obj.removeData("modeSetting");
    } else {
        let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
        let param = `id=${detail.id}&chargeNum=0&operation=5`;
        var settings = {
            "url": "../saleService/update?" + param,
            "method": "POST",
            "timeout": 100000,
            "headers": {
                "Content-Type": "application/json"
            },
            beforeSend:function(){ loading.open() ; }
        };
        $.ajax(settings).done(function(response){
            if (response === 2) {
                loading.close()
                layer.msg("新增失败，名称或者代码重复！");
            } else if (response.success === 1){
                bounce_Fixed.cancel();
                seeService(detail.id);
            }
        })
    }
}
// create: lyt，2021-05-27 10:16:13 计算价格
function setprice(num, obj) { // 1- 不含税，2-含税
    let trObj = obj.parents("tr");
    let noprice = Number(trObj.find(".noPrice").val());
    let price = Number(trObj.find(".price").val());
    let rate = Number(trObj.find(".rate").val());
    if(num === 1){
        if(rate > 0 && noprice > 0){
            price = noprice * (rate/100 + 1) ;
            trObj.find(".price").val(price.toFixed(2));
        }
        if(noprice === 0){
            trObj.find(".price").val("").prop("disabled", false)
        }else{
            trObj.find(".price").prop("disabled", true)
        }
    }else if(num === 2){
        if(rate > 0 && price > 0){
            noprice = price / (rate/100 + 1) ;
            trObj.find(".noPrice").val(noprice.toFixed(2));
        }
        if(price === 0){
            trObj.find(".noPrice").val("").prop("disabled", false)
        }else{
            trObj.find(".noPrice").prop("disabled", true)
        }
    }else{
        let priceDis =  trObj.find(".price").attr("disabled");
        let nopriceDis =  trObj.find(".noPrice").attr("disabled");
        if(priceDis){ // 按照不含税算
            if(rate > 0 && noprice > 0){
                price = noprice * (rate/100 + 1) ;
                trObj.find(".price").val(price.toFixed(2));
            }
        }else if (nopriceDis){ // 按照含税算
            if(rate > 0 && price > 0){
                noprice = price / (rate/100 + 1) ;
                trObj.find(".noPrice").val(noprice.toFixed(2));
            }
        }
    }
}
/*creator:lyt 2022/8/29 上午 08:12 返回*/
function reback(num){
    showMainCon(num);
    if (num === 1) {
        let json = JSON.parse($(".mainCon1").find(".yeCon").siblings(".json").html());
        getServiceList(json.name,1, 1,20);
    }
}
//creator:lyt 2023/5/8  体验环节或试用流程-改变状态
function changeTrialState(type){
    let state = 0;
    let cycle = 1;
    if (type === 'add'){
        cycle = $("#addService").data("cycle")
        state = $(".trialState").data("state");
    } else {
        let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
        state = detail.state;
        cycle = detail.isPeriodical;
    }
    if (state === 0) {
        $("#trialSetting input").val("").prop("disabled", false);
        $("#trialSetting select").val("");
        $("#trialSetting i.fa").attr("class","fa fa-circle-o")
        $("#trialSetting .mar").hide();
        $("#trialSetting .trialSettingBtn").hide();
        $("#trialSetting .bonceHead span").html("设置体验环节/试用流程");
        invoiceSettings($("#trialSetting select[name='taxRate']"), "");
        if (cycle === 1) {
            $(".marCircle").show().siblings().hide();
        } else {
            $(".marNoCircle").show().siblings().hide();
            let unit = $("#add_unitSelect").val() === "" ? "": $("#add_unitSelect option:selected").text();
            $(".marUnit").html(unit);
        }
        $("#trialSetting").data("type", type);
        bounce_Fixed.show($("#trialSetting"))
    } else {
        $(".updateTrailTip").hide();
        if (type === 'update'){
            $(".updateTrailTip").show();
        }
        $("#changeState").data("type", type);
        bounce_Fixed.show($("#changeState"))
    }
}
//creator:lyt 2023/5/8  体验环节或试用流程-改变状态
function changeStateSure(){
    let type = $("#changeState").data("type");
    bounce_Fixed.cancel();
    if (type === 'add') {
        $(".trialState").data("state", 0).html("未设置");
        $("#trialingData").html("");
        $(".trialingShow tr:gt(0)").remove();
    } else {
        let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
        let sendInfo = `id=${detail.trailItem.id}&state=0&operation=4`;
        var settings1 = {
            "url": "../saleService/update?" + sendInfo,
            "method": "POST",
            "timeout": 300000000,
            "headers": {
                "Content-Type": "application/json"
            },
            beforeSend:function(){ loading.open() ; }
        };
        $.ajax(settings1).done(function(response){
            seeService(detail.id)
        })
    }
}
//creator:lyt 2023/5/8  体验环节或试用流程
function updateTrialSetting(type){
    let state = 0, info = ``, cycle = 1, unit = ``;
    $("#trialSetting").data("type", type);
    $("#trialSetting input").val("");
    $("#trialSetting select").val("");
    if (type === 'add'){
        cycle = $("#addService").data("cycle");
        state = $(".trialState").data("state");
        if (state !== 0) info = JSON.parse($("#trialingData").html());
        if (cycle === 0) unit = $("#add_unitSelect").val() === "" ? "": $("#add_unitSelect option:selected").text();
    } else {
        let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
        state = detail.state;
        if (state === 1 && detail.trailItem) {
            info = detail.trailItem;
        }
        cycle = detail.isPeriodical;
        if (cycle === 0) unit = detail.unit;
    }
    if (state === 0) {
        layer.msg("目前没有可改的数据！");
    } else {
        $("#trialSetting i.fa").attr("class","fa fa-circle-o");
        $("#trialSetting .mar").hide();
        $("#trialSetting .bonceHead span").html("修改体验环节/试用流程");
        $("#trialSetting .trialSettingBtn").hide();
        if (info.type === 2) {
            $(".trialType .typeDot:eq(0)").click();
        } else {
            $(".trialType .typeDot:eq(1)").click();
        }
        if (info.isChargeable === 1) {
            $(".trialCharge .chargeDot:eq(0)").click();
        } else {
            $(".trialCharge .chargeDot:eq(1)").click();
        }
        if (cycle === 1) {
            $(".marCircle").show().siblings().hide();
        } else {
            $(".marNoCircle").show().siblings().hide();
            $(".marUnit").html(unit);
            if (info.deliveryAcceptable === 1) { //0-无关 1-有关
                $(".trialDeliveryAcceptable .fa:eq(0)").click();
            } else if (info.deliveryAcceptable === 0) {
                $(".trialDeliveryAcceptable .fa:eq(1)").click();
            }
        }
        invoiceSettings($("#trialSetting select[name='taxRate']"), "");
        if (info.isChargeable === 1) {
            let serviceChargeList = ``;
            if (type === 'add') {
                serviceChargeList = JSON.parse(info.serviceChargeList);
            } else {
                serviceChargeList = info.serviceCharges;
            }
            $("#trialSetting [name='chargeStage']").val(serviceChargeList[serviceChargeList.length - 1].chargeStage);
            $("#trialSetting [name='chargeLimit']").val(serviceChargeList[serviceChargeList.length - 1].chargeLimit);
        }
        bounce_Fixed.show($("#trialSetting"));
        $("#trialSetting [need]:visible").each(function(){
            let name = $(this).attr("name");
            $(this).val(info[name]);
        });
    }
}
//creator:lyt 2023/5/8  体验环节或试用流程确定
function trialSettingSure(){
    let abled = true, filed =1;
    $("#trialSetting [require]:visible").each(function(){
        let val = $(this).val();
        if (val === "") abled = false;
    });
    if ($(".trialCharge .fa-circle").data("type") === 1) {
        filed =0;
        $("#trialSetting .priceForm [need]:visible").each(function(){
            let val = $(this).val();
            let name = $(this).attr("name");
            if (name === 'taxRate') {
                if ($(this).val() === "") {
                    if ($("#trialSetting input[name = 'unitPriceNotax']").val() !== "" || $("#trialSetting input[name = 'unitPrice']").val() !== "")
                        abled = false;
                } else {
                    if ($("#trialSetting input[name = 'unitPriceNotax']").val() !== "" && $("#trialSetting input[name = 'unitPrice']").val() !== "") {
                        filed++;
                    } else {
                        abled = false;
                    }
                }
            } else {
                if (val !== "") filed++;
            }
        });
    }
    if (abled && filed > 0){
        let html = ``, str = ``, serviceChargeList = [];
        let type = $("#trialSetting").data("type");
        let data = {
            "state": 1,
            "type": $(".trialType .fa-circle").data("type"),
            "isChargeable": $(".trialCharge .fa-circle").data("type")
        }
        let cycle = 1, detail = ``;
        let priceStr = `--`, chargeTime = '--', ttl = `体验`;
        $("#trialSetting [need]:visible").each(function(){
            let val = $(this).val();
            let name = $(this).attr("name");
            data[name] = val;
        });
        if ($("#trialSetting .mar:eq(2)").is(":visible")) {
            let item = {
                "chargeLimit": $("#trialSetting input[name='chargeLimit']").val(),
                "chargeStage": $("#trialSetting select[name='chargeStage']").val()
            }
            serviceChargeList.push(item);
        }
        data.serviceChargeList = JSON.stringify(serviceChargeList);
        $(".trialingShow tbody tr:gt(0)").remove();
        bounce_Fixed.cancel();
        if (data.type === 3) ttl = `试用`;
        if (type === 'add') {
            cycle = $("#addService").data("cycle");
        } else {
            detail = JSON.parse($("#serviceProjectScan .serviceData").html());
            cycle = detail.isPeriodical;
        }
        if (cycle === 0 && data.isChargeable === 1) {
            data.deliveryAcceptable = $(".trialDeliveryAcceptable .fa-circle").data("type");
            str = `<tr>
                    <td>${ttl}是否需交付/验收？</td>
                    <td>${data.deliveryAcceptable === 1? '是':'否'}</td>
                </tr>`;
        }
        if (type === 'add') {
            //含税单价>普票单价>不开票单价>参考单价
            if (handleNull(data.unitPrice) !== "") {
                priceStr = '税率'+handleNull(data.taxRate)+'%，含税单价'+ data.unitPrice+'元，不含税价'+ data.unitPriceNotax+'元';
            } else if (handleNull(data.unitPriceInvoice) !== "") {
                priceStr = '开普票时的开票单价'+data.unitPriceInvoice+'元';
            } else if (handleNull(data.unitPriceNoinvoice) !== "") {
                priceStr = '不开发票时的单价'+data.unitPriceNoinvoice+'元';
            } else if (handleNull(data.unitPriceReference) !== "") {
                priceStr = '参考单价'+data.unitPriceReference+'元';
            }
            data.isChargeable === 1 ? chargeTime = `${changeStr(serviceChargeList[0].chargeStage, 'trialStage', ttl) + serviceChargeList[0].chargeLimit}天内`: '';
            if(cycle === 0 && data.deliveryAcceptable === 1) {
                data.isChargeable === 1 ? chargeTime = `${changeStr(serviceChargeList[0].chargeStage, 'exStage', ttl) + serviceChargeList[0].chargeLimit}天内`: '';
            }
            html = `<tr>
                    <td>${cycle === 1 ? '可'+ ttl +'的天数':'某客户可'+ ttl +'的数量上限'}</td>
                    <td>${cycle === 1 ? data.trailDays +'天':data.trailUpper + $(".marUnit").html()}</td>
                </tr>${str}
                <tr>
                    <td>${ttl}价格</td>
                    <td>${priceStr}</td>
                </tr>
                <tr>
                    <td>收费时间</td>
                    <td>${chargeTime}</td>
                </tr>`;
            $(".trialState").data("state", 1).html(`已设置${data.type === 2? '体验环节':'试用流程'}`);
            $(".trialingShow tbody").append(html);
            $("#trialingData").html(JSON.stringify(data));
        } else {
            let url = ``;
            let sendInfo = ``;
            if (detail.state === 0) {
                sendInfo = `mainItem=${detail.id}`
                url = "../saleService/add?";
            } else {
                sendInfo = `id=${detail.trailItem.id}&operation=6`
                url = "../saleService/update?";
            }
            for(var key in data) {
                if (key !== 'serviceChargeList') sendInfo += `&${key}=${data[key]}`;
            }
            var settings1 = {
                "url": url + sendInfo,
                "method": "POST",
                "timeout": 300000000,
                "headers": {
                    "Content-Type": "application/json"
                },
                "data": data.serviceChargeList,
                beforeSend:function(){ loading.open() ; }
            };
            $.ajax(settings1).done(function(response){
                seeService(detail.id)
            })
        }
    } else {
        layer.msg("<p>操作失败</p><p>因为还有必填项尚未填写。</p>");
    }
}
//creator:lyt 2023/5/18 项目“代号”的查重功能
function codeCheck(obj){
    let val = obj.val();
    if (val !== "") {
        $.ajax({
            "url": '../saleService/duplicate',
            "data": {
                "code": val,
                "name": ""
            },
            success: function (res) {
                if (res === 2){
                    layer.msg("这个代号已被使用，请换一个！");
                    obj.val("");
                }
            }
        })
    }
}
//creator:lyt 2023/5/26 0026 下午 1:19 服务项目的两项开票资料编辑
function changeInvoiceCon(type){
    let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
    let itemInvoices = detail.itemInvoices;
    $(".emptyTip"+ type).show().siblings().hide();
    if (type === 1) {
        bounce_Fixed.show($("#invoiceInfoSet"));
        $("#invoiceInfoSet .fa").attr("class", "fa fa-circle-o");
        $("#invoiceInfoSet input").val("").prop("disabled", true);
        $("#serviceCode").html(detail.code);
        $("#serviceName").html(detail.name);
    } else {
        bounce_Fixed.show($("#invoiceFormateSet"));
        $("#invoiceFormateSet input").val("").prop("disabled", true);
        $("#invoiceFormateSet .fa").attr("class", "fa fa-circle-o");
    }
    if (itemInvoices && itemInvoices.length > 0) {
        let index = itemInvoices.findIndex(invoice => invoice.type === type);
        if (index > -1 && handleNull(itemInvoices[index].nameResult) !== "") {
            $(".inv_ttl" + type).html(itemInvoices[index].nameResult);
            $(".repeatTip"+ type).show().siblings().hide();
        }
    }
}
//creator:lyt 2023/5/26 0026 下午 1:59 服务项目的两项开票资料编辑确定
function invoiceInfoSet(type){
    if ($(".cateChoose:visible").find(".fa-circle").length > 0){
        let url = ``;
        let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
        let pt = $(".cateChoose:visible").find(".fa-circle").data("val")
        let data = "serviceItem=" + detail.id + "&type=" + type + "&pattern="+ pt;
        let itemInvoices = detail.itemInvoices;
        if (type === 1 && pt === 5) {
            data +="&userDefined="+ $("#invoiceInfoSet .cateChoose input").val();
        } else if (type === 2 && pt === 2) {
            data +="&userDefined="+ $("#invoiceFormateSet .cateChoose input").val();
        }
        if (itemInvoices && itemInvoices.length > 0) {
            let index = itemInvoices.findIndex(invoice => invoice.type === type);
            if (index > -1) {
                data +="&id=" + detail.itemInvoices[index].id;
            }
        }
        $.ajax({
            "url": '../serviceItem/invoice/edit?' + data,
            success: function (res) {
                seeService(detail.id, 1);
                bounce_Fixed.cancel();
            }
        })
    }
}
function copyServiceInfo(obj){
    var cont = obj.prev().text();
    copyText(cont);
    layer.msg("复制成功");
}
function copyText(text) {
    var textarea = document.createElement("input");//创建input对象
    var currentFocus = document.activeElement;//当前获得焦点的元素
    document.body.appendChild(textarea);//添加元素
    textarea.value = text;
    textarea.focus();
    if(textarea.setSelectionRange)
        textarea.setSelectionRange(0, textarea.value.length);//获取光标起始位置到结束位置
    else
        textarea.select();
    try {
        document.execCommand("copy");//执行复制
    } catch(eo) {
        layer.msg("复制失败");
    }
    document.body.removeChild(textarea);//删除元素
    currentFocus.focus();
}
/*creator:lyt 2023/5/27 0027 上午 10:04 */
function invoiceUpdateRecords(type){
    if (type === 1) {
        $("#invoiceUpdateRecords .bonceHead span").html("开票资料“货物或应税劳务、服务名称”的编辑记录");
    } else {
        $("#invoiceUpdateRecords .bonceHead span").html("开票资料“规格型号”的编辑记录");
    }
    let curStaStr = ``;
    let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
    let itemInvoices = detail.itemInvoices
    $("#invoiceUpdateRecords tbody").children(":gt(0)").remove();
    if (itemInvoices && itemInvoices.length > 0) {
        var item = itemInvoices.find(value => value.type === type);
        if (item) {
            $.ajax({
                "url": '../serviceItem/invoice/history/list',
                "data": {
                    "serviceItemInvoice": item.id,
                    "type": type
                },
                success:function(res) {
                    let str = ``;
                    if (res){
                        let list = res;
                        if(list.length >0){
                            for(let i = 0 ; i < list.length;i++){
                                let item = list[i];
                                let index = i + 1;
                                str += `<tr>
                             <td>${i === 0?"首次编辑后":"第"+index+"次编辑后"}</td>
                             <td class="pos-left">
                                ${item.nameResult}
                             </td>
                             <td>${handleNull(item.createName)} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                            }
                            let n = list.length - 1;
                            curStaStr = `当前资料为第${n}次编辑后的结果，操作者：${handleNull(list[n].createName) === ""? "系统": handleNull(list[n].createName)} ${new Date(list[n].createDate).format("yyyy-MM-dd hh:mm:ss")}`
                            $("#invoiceUpdateRecords table").show();
                        }
                    }
                    $("#invoiceUpdateRecords tbody").append(str);
                }
            });
        }
    }else {
        $("#invoiceUpdateRecords table").hide();
        curStaStr = `<p>当前资料尚未经修改。</p> `
    }
    bounce_Fixed.show($("#invoiceUpdateRecords"));
    $("#invoiceUpdateRecords .curSta").html(curStaStr);
}
//creator:lyt 2023/5/27 0027 下午 12:02 体验环节/试用流程修改记录
function updateTrailRecords(icon){
    let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
    let param = ``, curStaStr = ``;
    if (icon === 1) {
        if (detail.trailItem){
            param = 'id='+ detail.trailItem.id +'&operation=6';
        } else {
            $("#updateServiceRecords table").hide();
            curStaStr = `<p>当前资料尚未经修改。</p> `
            $("#updateServiceRecords .curSta").html(curStaStr);
            bounce_Fixed.show($("#updateServiceRecords"));
            return false;
        }
    } else {
        param = 'id='+ detail.id +'&operation=4';
        $("#updateTrailRecords tbody").children(":gt(0)").remove();
    }
    $.ajax({
        "url": '../saleService/history?'+ param,
        success:function(res) {
            let list = res.data;
            let str = ``;
            if(list[0] && list.length >0){
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    if (icon === 1) {
                        str += `<tr>
                             <td>${i === 0?"原始信息":"第"+ i +"次修改后"}</td>
                             <td>${handleNull(i === 0?item.createName:item.updateName)} ${new Date(i === 0?item.createDate: item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                             <td class="ty-td-control">
                                ${item.state === 0?'<span class="ty-tip-blue">--</span>':'<span class="ty-color-blue nodeBtn" data-type="updateTrailLogScan">查看</span><span class="hd">'+JSON.stringify(item)+'</span>'}
                             </td>
                            </tr>`
                    } else {
                        str += `<tr>
                             <td>${item.state === 0?"未设置":"已设置"}</td>
                             <td>${handleNull(i === 0?item.createName:item.updateName)} ${new Date(i === 0?item.createDate: item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                             <td class="ty-td-control">
                             ${item.state === 0?'<span class="ty-tip-blue">--</span>':'<span class="ty-color-blue nodeBtn" data-type="updateTrailLogScan">查看</span><span class="hd">'+JSON.stringify(item)+'</span>'}
                             </td>
                            </tr>`;
                    }
                }
            }
            if (icon === 1) {
                if (list[0] && list.length >0)  {
                    $("#updateServiceRecords tbody").children(":gt(0)").remove();
                    $("#updateServiceRecords tbody").append(str);
                    let n = list.length - 1;
                    curStaStr = `<p>当前资料为第${n}次修改后的结果。</p><p>修改人：${handleNull(list[n].updateName) === ""? "系统": handleNull(list[n].updateName)} ${new Date(list[n].updateDate).format("yyyy-MM-dd hh:mm:ss")}</p> `
                    $("#updateServiceRecords table").show();
                }else{
                    $("#updateServiceRecords table").hide();
                    curStaStr = `<p>当前资料尚未经修改。</p> `
                }
                $("#updateServiceRecords .curSta").html(curStaStr);
                bounce_Fixed.show($("#updateServiceRecords"));
            } else if (icon === 2) {
                $("#updateTrailRecords tbody").append(str);
                bounce_Fixed.show($("#updateTrailRecords"));
            }
        }
    });
}
function updateTrailLogScan(obj){
    let detail = JSON.parse($("#serviceProjectScan .serviceData").html());
    let info = JSON.parse(obj.siblings(".hd").html());
    let trlStr = ``;
    $.ajax({
        "url": '../saleService/historyDetails?id='+ info.id,
        "method": "GET",
        success:function(data){
            let trailItem = data.data;
            if (trailItem) {
                let ttl = trailItem.type === 2? '体验':'试用';
                let priceStr = `--`, accept = ``, trailCharges = `--`;
                if (trailItem.isChargeable === 1) {
                    //含税单价>普票单价>不开票单价>参考单价
                    if (handleNull(trailItem.unitPrice) !== "") {
                        priceStr = '税率'+handleNull(trailItem.taxRate)+'%，含税单价'+ trailItem.unitPrice+'元，不含税价'+ trailItem.unitPriceNotax+'元';
                    } else if (handleNull(trailItem.unitPriceInvoice) !== "") {
                        priceStr = '开普票时的开票单价'+trailItem.unitPriceInvoice+'元';
                    } else if (handleNull(trailItem.unitPriceNoinvoice) !== "") {
                        priceStr = '不开发票时的单价'+trailItem.unitPriceNoinvoice+'元';
                    } else if (handleNull(trailItem.unitPriceReference) !== "") {
                        priceStr = '参考单价'+trailItem.unitPriceReference+'元';
                    }
                    trailCharges = `${changeStr(trailItem.serviceCharges[0].chargeStage, 'trialStage', ttl) + trailItem.serviceCharges[0].chargeLimit}天内`;
                    if (detail.isPeriodical === 0) {
                        accept = `<tr>
                            <td>${ttl}是否需交付/验收？</td>
                            <td>${trailItem.deliveryAcceptable === 1? '是':'否'}</td>
                        </tr>`;
                        if(trailItem.deliveryAcceptable === 1) {
                            trailCharges = `${changeStr(trailItem.serviceCharges[0].chargeStage, 'exStage', ttl) + trailItem.serviceCharges[0].chargeLimit}天内`;
                        }
                    }
                }
                trlStr = `
                        <tr>
                            <td>操作者</td>
                            <td>${trailItem.updateName} ${new Date(trailItem.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                        </tr>
                        <tr>
                            <td>设置结果</td>
                            <td>${trailItem.type === 2? '体验环节':'试用流程'}</td>
                        </tr>
                        <tr>
                            <td>${detail.isPeriodical === 1 ? '可'+ ttl +'的天数':'某客户可'+ ttl +'的数量上限'}</td>
                            <td>${detail.isPeriodical === 1 ? trailItem.trailDays+'天':trailItem.trailUpper+ detail.unit}</td>
                        </tr>${accept}
                        <tr>
                            <td>${ttl}价格</td>
                            <td>${priceStr}</td>
                        </tr>
                        <tr>
                            <td>收费时间</td>
                            <td>${trailCharges}</td>
                    </tr>`;
            }
            $("#trailLogScan #trailLogScanCon tbody").html(trlStr);
            bounce_Fixed2.show($("#trailLogScan"));
        }
    });
}
function changeStr(val, cate, ttl){
    let str = ``;
    if(cate) {
        switch (cate) {
            case "periodUnit":
                let unit = ['日', '周', '旬', '月', '季', '半年', '年'];
                str = unit[val - 1];
                break;
            case "chargeStage":
                let stage = ['合同签订后', '服务开始前', '服务开始后', '交付前', '交付后', '本期交付前', '本期交付后', '最终交付前', '最终交付后', '通过验收后', '本期通过验收后', '最终通过验收后', '服务结束后'];
                str = stage[val - 1];
                break;
            case "trialStage":
                let arr = ['合同签订后', ttl + '开始前', ttl + '开始后', ttl + '结束后'];
                str = arr[val - 1];
                break;
            case "exStage":
                let arrE = ['合同签订后', ttl + '开始前', ttl + '开始后', ttl + '结束后', '交付前', '交付后', '通过验收后'];
                str = arrE[val - 1];
                break;
        }
    }
    return str;
}
// creator: 李玉婷，2020-04-03 10:28:28，描述、说明字数限制
function limitWord(obj, num) {
    var val = obj.val();
    var length = val.length;
    obj.parent().siblings(".lenTip").html(length + '/' + num);
}
// creator: 李玉婷，2022-08-30 10:12:08，只允许录入自然数，且需在1-99之间
function limitNumber(obj){
    clearNum(obj)
    if (!/^[1-9]+$/.test(obj.value)) obj.value = obj.value.replace(/\D/g,'');
    if (obj.value > 99) obj.value = 99;
}
















