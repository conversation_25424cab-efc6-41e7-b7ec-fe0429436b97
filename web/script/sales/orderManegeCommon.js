var deleteGoodObj = null;
$(function () {
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
    $("body").on("click",function () {
        $(".outerSn").next().hide();
    });

    // 商品代号匹配
    $(".outerSn").on("click keyup",function (e) {
        $(this).next().show();
        e.stopPropagation();
        // 设置下拉列表（通过缓存数据设置）
        setOuterSnOption();
    });

    //新增订单 - 下拉框条目点击事件（填入商品代号 关闭下拉框并显示与此相关其他信息）
    $(".outerSn + .ty-optionCon").on("click",".ty-option",function () {
        $(this).parent().prev().val($(this).find("span").text());
        let id = $(this).attr("id")
        let fuSpanObj = $(this).parent().prev()
        fuSpanObj.attr("id",id);
    }).on("mouseover mouseout click",".ty-option",function () {
        var goodInfo = $(this).find(".hd").text();
        if(goodInfo){
            goodInfo = JSON.parse(goodInfo);
            setGoodDetail(goodInfo);
        }
    });
    $(".linkBtn").click(function () {
        let fun = $(this).data("fun");
        switch (fun){
            case "addressExplain": addressExplain(); break; // 收货地址说明
           // case "newReceiveInfo":
           // case "newReceiveInfo2":
           //     newReceiveInfo(fun); break; // 新增收货地址
            case "invoiceRequireEdit": invoiceRequireEdit(); break; // 开票要求
            case "modifyPrice": // 临时调价
                let outerName = $("#newGood .outerName").val();
                if(outerName.length === 0){
                    layer.msg("请先选择商品")
                    return false
                }
                let modify = $("#newGood .modifyPrice").data('modify')
                let afterModify = 0
                if(modify === 0){
                    afterModify = 1 // 允许调价
                    $(".modify").show();
                    $("#newGood .rate").prop("disabled",false)
                    $("#newGood .noPrice").prop("disabled",false)
                    $("#newGood .price").prop("disabled",false)
                } else {
                    afterModify = 0 // 不允许调价
                    $(".modify").hide();
                    $("#newGood .rate").prop("disabled",true).val( $("#newGood .rate").data('val'))
                    $("#newGood .noPrice").prop("disabled",true).val( $("#newGood .noPrice").data('val'))
                    $("#newGood .price").prop("disabled",true).val( $("#newGood .price").data('val'))
                }
                $("#newGood .modifyPrice").data('modify', afterModify);
                break;
            case "receiveFirstPage":
                let source = $(this).data("source");
                receiveFirstPage(source); break; // 新增收货地址
        }
    })
    $("#invoiceSet .setItem").click(function(){
        $("#invoiceSet .fa").attr("class","fa fa-circle-o");
        $(this).children(".fa").attr("class","fa fa-dot-circle-o");
    })
    $("#newGoodsTip .ngTipR").click(function(){
        $("#newGoodsTip .fa").attr("class","fa fa-circle-o");
        $(this).children(".fa").attr("class","fa fa-dot-circle-o");
    })
    // 新增/修改订单 - 删除商品
    $(".goodList").on("click",".deleteGoodBtn",function () {
       let info = JSON.parse($(this).siblings(".hd").html());
       if(info.updateType === 1){
           layer.msg(`<p>不是本次新增的商品不可删除！</p><p>如客户确不再需要该商品，您可将要其订购数量改为零。</p>`)
       }else{
           deleteGoodObj = $(this)
           bounce_Fixed.show($("#delGoodTip"));
       }
    });
    // 新增/修改订单 - 修改商品
    $("#gsBd").on("click",".updateGoodBtn",function () {
        let data = JSON.parse($(this).siblings(".hd").html());
        editGsObj = $(this);
        $.ajax({
            "url":"../sales/getPdType", //传商品id
            "data":{ "id":data.id  },
            success:function (res) { // 返回1 通用 2 专属
                $("#newGood").data("ttl", res )
                newGoodBtn('update' , res)
                setGoodDetail(data, 'update')
                bounce_Fixed.show($("#newGood"));
                let orderEditType = $("#newOrder").data("orderEditType")
                if(orderEditType === "update"){ // 修改
                    if(data.updateType === 1){ // 原来的订单的商品
                        // $("#newGood .goodNum").attr("onkeyup", "clearNum0(this); manLen(9,$(this))");
                    }
                }
                $("#newGood .modifyPrice ").show()
                $("#newGood .outerSn").prop("disabled", true)
                $("#newGood .goodNum").val(data.goodNum)
                $("#newGood .requireDate").val(data.requireDate)
                //$("#newGood .receiveAddress").val(data.receiveAddressId)
                let invoiceRequire = data.invoiceRequire
                if(invoiceRequire === 1){ // 增专
                    $("#newGood .rate").val(data.priceInfo.taxRate)
                    $("#newGood .noPrice").val(data.priceInfo.unitPriceNotax)
                    $("#newGood .price").val(data.priceInfo.unitPrice)
                }else if(invoiceRequire === 2){
                    $("#newGood .price").val(data.priceInfo.unitPriceInvoice)
                }else if(invoiceRequire === 3){ //不开票
                    $("#newGood .price").val(data.priceInfo.unitPriceNoinvoice)
                }
            }
        })

    });

    // 获取客户的联系人
    $(".chooseCusCon").click(function () {
        getCusContactList( $(".customerId").val());
    });
    // 选择联系人
    $("#chooseCusContact").on("click", '.fa', function () {
        $("#chooseCusContact").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).attr("class", "fa fa-dot-circle-o")
    })

    $(".bounce_Fixed2").on('click','.fileImScan,.node', function(){
        var name = $(this).data('fun');
        switch (name) {
            case 'imgScan': // 合同的picture
                imgViewer($(this));
                break;
            case 'cWord': // 合同的可编辑版
                // let path = $("#cScan .cWord").attr('path');
                seeOnline($("#cScan .cWord"))
                break;
            case 'gNum': //本合同下的商品
                $("#tipcontractGoods .bonceHead span").html('本合同下的商品');
                $(".addOrCancel").hide(); $(".cScanc").show();
                let productList = $("#cScan .gNum").data('list');
                setContactGS(productList , false);
                break;
            case 'cEditLog': // 本版本合同的修改记录
                break;
            case 'cRenewalLog': // 本合同的续约记录
                break;
        }
    });
    $(".tfType").click(function(){
        $(".tfType i").attr("class","fa fa-circle-o");
        $(this).children("i").attr("class","fa fa-dot-circle-o");
    });

    $("body").on("click",".funBtn,.lineBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
    $(".placeList").on('click','.fa', function(){
        if($(this).hasClass("fa-square-o")){
            $(this).attr("class","fa fa-check-square-o")
        }else{
            $(this).attr("class","fa fa-square-o")
        }
    });
});
// creator: hxz，2021-5-28 删除商品确定
function delGoodTipOk() {
    deleteGoodObj.parent().parent().remove();
    bounce_Fixed.cancel()
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.attr('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");
}
// creater : 2021-5-24 hxz 渲染合同里商品的列表
function setContactGS(list, boolSet) {
    let str = '';
    let cstr = '';
    if(boolSet){
        cstr = '<span class="fa fa-square-o"></span>';
    }
    list = list || [];
    list.forEach(function (im) {
        str += `
          <tr>
              <td class="controlTd">${cstr}${im.sn}<span class="hd">${JSON.stringify(im)}</span></td>
              <td>${im.name}</td>
              <td>${im.model}</td>
              <td>${im.specifications}</td>
              <td>${im.unit}</td>
          </tr>`
    })
    $("#tipcontractGoods table tr:gt(0)").remove();
    $("#tipcontractGoods table").append(str);
    bounce_Fixed3.show($("#tipcontractGoods"));
}
// creator: hxz  2018-07-18  初始化 三级弹框 - fixed2
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#invoiceSet2"));
bounce_Fixed2.cancel();

var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#newContectInfo"));
bounce_Fixed3.cancel();
var bounce_Fixed4 = new Bounce(".bounce_Fixed4");
bounce_Fixed4.show($("#chooseCusContact"));
bounce_Fixed4.cancel();
var bounce_Fixed5 = new Bounce(".bounce_Fixed5");
bounce_Fixed5.show($("#contactSeeDetail"));
bounce_Fixed5.cancel();


// creator: hxz  2021-05-25  历史价格
function getHistoryPrice(obj) {
    var goodsInfo = $("#newGood .invoiceTip").html();
    if(goodsInfo.length > 0){
        goodsInfo = JSON.parse(goodsInfo)
        $.ajax({
            'url' : "../sales/productPriceHistory",
            "data":{ "id": goodsInfo.id },
            success:function (res) {
                var list = res.data || []

                bounce_Fixed2.show($("#cHistoryPrice"))
                let str = ""
                list.forEach(function (item) {
                    let invoiceCat = Number(item.invoice_require) ;
                    let priceStr = ``;
                    if(invoiceCat === 1){
                        priceStr = `税率${item.tax_rate}%、含税单价${item.unit_price}元，不含税价${item.unit_price_notax}元`;
                    } else if(invoiceCat === 2){
                        priceStr = `开票单价${item.unit_price_invoice}元`;
                    } else {
                        priceStr = `不开票价${item.unit_price_noinvoice}元`;
                    }
                    str += `<tr><td>${priceStr}</td><td>${ new Date(item.create_date).format("yyyy-MM-dd hh:mm:ss") }</td></tr>`;
                })
                $("#cHistoryPrice table tbody tr:gt(0)").remove() ;
                $("#cHistoryPrice table tbody").append(str);
                $("#cHistoryPrice .cNum").html(list.length);
            }
        })
    }else{
        layer.msg("请先选择商品")
    }
}
// creator: hxz  2021-05-25  所属的合同
function getContractDetail(obj) {
    var goodsInfo = $("#newGood .invoiceTip").html();
    if(goodsInfo.length > 0){
        goodsInfo = JSON.parse(goodsInfo)
        $.ajax({
            'url' : "../sales/belongContract",
            "data":{ "id": goodsInfo.id },
            success:function (res) {
                var newcInfo = res
                if(!newcInfo){
                    layer.msg("获取合同信息失败！");
                    return false;
                }
                let versionNo = newcInfo.versionNo
                if(versionNo === -1){
                    $(".nothas").show();$(".has").hide();
                }else{
                    $(".nothas").hide();$(".has").show();
                    $("#cScan .cNos").html(newcInfo.sn)
                    $("#cScan .create").html(`${newcInfo.createName} ${ new Date(newcInfo.createDate).format("yyyy-MM-dd hh:mm:ss")}`)
                    $("#cScan .cSignDates").html(new Date(newcInfo.signTime).format("yyyy-MM-dd"))
                    $("#cScan .cvalidDates").html(new Date(newcInfo.validStart).format("yyyy-MM-dd") + '至' + (new Date(newcInfo.validEnd).format("yyyy-MM-dd")))
                    $("#cScan .cMemos").html(newcInfo.memo)
                    let imgStr1 = ``
                    let contractBaseImages = newcInfo.contractBaseImages || []
                    contractBaseImages.forEach(function(bsIm){
                        imgStr1 += `<span class="fileImScan" data-fun="imgScan" path="${bsIm.filePath}">
                                  <span>${bsIm.orders + 1 }</span>
                                  <span class="hd">${JSON.stringify(bsIm) }</span>
                             </span>`
                    })
                    $("#cScan .cImgs").html(imgStr1)
                    $("#cScan .cWord").data('path', newcInfo.filePath).attr('path', newcInfo.filePath)
                    let productList = newcInfo.productList || []
                    $("#cScan .gNum").data('list', productList).html(productList.length)
                    let enabledList = newcInfo.enabledList || [] , enableStr= ``;
                    enabledList.forEach(function (enIm) {
                        enableStr += `
                <p>
                    <span>${ enIm.enabled === 1 ? "恢复履约/重启合作" : "暂停履约/终止合作" }</span>
                    <span class="enName">${ enIm.updateName }</span>
                    <span>${ new Date(enIm.enabledTime).format("yyyy-MM-dd hh:mm:ss") }</span>
                </p>`;
                    })
                    $("#cScan .enabledList").html(enableStr)
                }
                bounce_Fixed2.show($("#cScan"))
                // $("#cScan").data("cid",cid)

            }
        })
    }else{
        layer.msg("请先选择商品")
    }
}

//  creator: hxz  2021-05-06 功能说明
function addressExplain(){
    bounce_Fixed.show($("#reviceAddress"));
}
function addReceive(obj){
    let cusID = $(".customerName").data("cusId");
    if(!cusID){
        layer.msg("请先选择客户名称！");
        return false;
    }
    let type = obj.data('type')
    $("#newReceiveInfo").data('id', cusID);
    $("#newReceiveInfo").data('type', type);
    bounce_Fixed3.show($("#newReceiveInfo"))
    $("#newReceiveInfo input").val("");
    $("#ReceiveName")
        .val("").data('orgData',"")
        .siblings(".hd").html("") ;
    if (type === "update"){
        let trObj = obj.parents("tr")
        let info = JSON.parse(obj.siblings(".hd").html())
        $("#newReceiveInfo").data('id', info.id);
        $("#newReceiveInfo").data('trObj', trObj);
        $("#ReceiveAddress").val(trObj.find("td").eq(0).html());
        $("#ReceiveName")
            .val(trObj.find("td").eq(1).html()).data('orgData',"")
            .siblings(".hd").html(JSON.stringify(info)) ;
    }
    setTimer('updateReceive');
}
// creater :hxz 2021-05-12 开票要求 编辑
function invoiceRequireEdit(){
    let cusID = $(".customerName").val()
    if(!cusID){
        layer.msg("请先选择客户名称！");
        return false;
    }
    $("#invoiceSet").find(".fa-dot-circle-o").attr("class", "fa fa-circle-o")
    bounce_Fixed.show($("#invoiceSet"));

}
function invoiceSetOk() {
    var selectP = $("#invoiceSet .fa-dot-circle-o").parent();
    let type = selectP.data("type");
    let txt = ''
    if(Number(type) > 0  ){
        txt = selectP.children("span").html();
        let cusID = $(".customerName").data("cusId")
        $.ajax({
            "url":"../sales/updateCustomerInvoiceSet",
            "data":{ 'cusId': cusID, 'invoiceRequire': type},
            success:function (res) {
                $(".invoiceRequire").data("invoice", type).val(txt);
                setOuterCon(cusID, type);
            }
        })
        bounce_Fixed.cancel();
    }else{
        layer.msg("请先选择开票要求")
    }

}
// creator:hxz 2021-05-11 新增联系人
function addContactInfo(num) {
    $("#contactFlag").html('收货人');
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $('#uploadCard').show().html("");
    $("#contactsCard .bussnessCard").remove();
    if($(".contactItem").length >= 50){
        layer.msg('最多可录入50条联系人。');
    }
    $("#addMoreContact").hide();
    $("#newContectInfo").data("ssType", num);
    initCardUpload($("#uploadCard"));
    bounce_Fixed3.show($("#newContectInfo"));
    bounce_Fixed3.everyTime('0.5s','updateContact',function () {
        var state = 0, filledNum = 0,otherContactLen = 0;
        var contactsCard = $("#contactsCard").data('org');
        var imgCard = $("#contactsCard .filePic").data('path');
        var len = $(".otherContact").data('length');
        $("#addMoreContact option").prop('disabled', false);
        if ($(".otherContact li").length > 0) {
            $(".otherContact li").each(function () {
                var val = $(this).find("input").val();
                var type = $(this).find("input").data('type');
                if (type == '9' || type == '9') type = 6
                if (val == '') {
                    $("#addMoreContact option").eq(type).prop('disabled', true);
                }else {
                    otherContactLen++;
                }
            })
        }
        if (len !=  otherContactLen) state ++;
        $("#newContectData [require]:visible").each(function(){
            if ($(this).val() != '') filledNum++;
            if($(this).val() != $(this).data('org')){
                state ++;
            }
        });
        if(contactsCard != imgCard) state ++;
        if(filledNum > 0 && state > 0){
            $("#addContact").prop("disabled",false);
        }else{
            $("#addContact").prop("disabled",true);
        }
    });
}
function addContact() {
    var data = {
        'tags': $("#contactFlag").html(),
        'name': $("#contactName").val(),
        'post': $("#position").val(),
        'mobile': $("#contactNumber").val(),
        'visitCard': '',
        'socialList': '[]'
    };
    let num = $("#newContectInfo").data("ssType");
    if($("#contactsCard .bussnessCard").length > 0){
        data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
    }
    if($(".otherContact li").length > 0){
        var arr = []
        $(".otherContact li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
        arr = JSON.stringify(arr);
        data.socialList = arr;
    }
    data.customer = $("#newOrder .customerName").data("cusId")
    $.ajax({
        url: '../sales/addCustomerContact.do',
        data: data,
        success: function (res) {
            data.id = res.id;
            var status = res.status;
            if (status === '1' || status === 1) {
                layer.msg('新增成功')
                let info = {"name":data.name ,"contact":data.name , "mobile":data.mobile , "id":data.id, "post":data.post, "enabled":1};
                if (num === 2) {
                    $("#ReceiveName")
                        .val(info.name)
                        .siblings(".hd").html(JSON.stringify(info)) ;
                    bounce_Fixed3.show($("#newReceiveInfo"));
                } else {
                    $("#areaName")
                        .val(info.name)
                        .siblings(".hd").html(JSON.stringify(info)) ;
                    bounce_Fixed3.show($("#newReceiveAreaInfo"));
                }
                setTimer('updateReceive');
            } else {
                layer.msg("新增失败！");
            }
        }
    })
}
// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initCardUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        formData:{
            module: '客户管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var path = data.filename //路径（包含文件类型）
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="bussnessCard" style="width:70%; margin: 0 auto;">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span fileUid="' + data.fileUid + '" class="ty-color-blue" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            $('#uploadCard').hide();
            $('#uploadCard').before(imgStr);
        }
    });
}
// creator: 李玉婷，2019-09-04 19:31:34，删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    $('#uploadCard').show();
}
// creator: 李玉婷，2019-08-21 14:46:56，图片删除
function cancleThis(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent(".imgsthumb").remove();
}

// update: lyt，2023-10-23 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    let num = $("#newContectInfo").data("ssType");
    var dialog = selector.parents(".bonceContainer")
    var groupUuidArr = [];
    dialog.find("[groupUuid]").each(function () {
        groupUuidArr.push({
            type: 'groupUuid',
            groupUuid: $(this).attr("groupUuid")
        })
    })
    cancelFileDel(groupUuidArr, true)
    if (num === 2) {
        bounce_Fixed3.show($("#newReceiveInfo"));
    } else {
        bounce_Fixed3.show($("#newReceiveAreaInfo"));
    }
    setTimer('updateReceive');
}
// creator: 李玉婷，2019-09-04 18:55:15，添加
function addMore (obj){
    obj.next("select").show();
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContact li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li>' +
                '<span class="sale_ttl1">手机：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '2':
            html +=
                '<li>' +
                '<span class="sale_ttl1">QQ：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '3':
            html +=
                '<li>' +
                '<span class="sale_ttl1">Email：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '4':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微信：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '5':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微博：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed4.show($("#useDefinedLabel"));
            bounce_Fixed4.everyTime('0.5s', 'useDefinedLabel',function () {
                var name = $.trim($("#defLable").val());
                if (name == '' || !name) {
                    $("#addNewLableSure").prop('disabled', true);
                } else {
                    $("#addNewLableSure").prop('disabled', false);
                }
            })
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-07 08:50:59，删除添加的联系方式
function removeAdd(obj) {
    obj.parent("li").remove();
    if ($(".otherContact li").length > 0) {
        $(".otherContact li").each(function () {
            var val = $(this).find("input").val();
            var type = $(this).find("input").data('type');
            if (type == '9' || type == '9') type = 6
            if (val == '') {
                $("#addMoreContact option").eq(type).prop('disabled', true);
            }else {
                $("#addMoreContact option").eq(type).prop('disabled', false);
            }
        })
    }else{
        $("#addMoreContact option").prop('disabled', false);
    }
}
// creator: hxz 2020-12-10 获取联系人列表
function getCusContactList(cusID) {
    $.ajax({
        "url":"../sales/getContactsList.do",
        "data":{ 'customerId': cusID },
        success:function (res) {
            if(res.status !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data'] || [] ;
            setCusListStr(list)
        }
    });
}
function setCusListStr(list, source) {
    let str="";
    if(list.length === 0){
        $("#chooseCusContact .p0").show()
        $("#chooseCusContact .p1").hide()
        return false
    }
    $("#chooseCusContact .p0").hide()
    $("#chooseCusContact .p1").show()
    for(let i in list){
        let item = list[i];
        item.contact = item.name
        str += `<li>
                    <i class="fa fa-circle-o"></i>
                    <span>${item.name}</span>
                    <span>${item.post && item.post.substr(0,8)}</span>
                    <span>${item.mobile}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                    <span class="linkBtn ty-right" data-id="${item.id}" onclick="cus_recordDetail($(this))">查看</span>
                </li>`;
    }
    $("#chooseCusContact .cusList").html(str);
    bounce_Fixed4.show($("#chooseCusContact"))
}
/*修改记录前、后查看*/
function cus_recordDetail(obj){
    $(".initValue").html("");
    var source = obj.data('source');
    $.ajax({
        url : "../sales/getContactsSocial.do" ,
        data : { 'contactId': obj.data('id') },
        success:function(data){
            var get = data['data'];
            see_otherContactStr(get)
        }
    });
}
function see_otherContactStr(get){
    var html = '',socialList = get['socialList'];
    $("#see_contactName").html(get.name);
    $("#see_position").html(get.post);
    $("#see_contactTag").html(get.tags);
    $(".see_otherContact").html("");
    $("#contactSeeDetail .see_createName").html(get.createName);
    $("#contactSeeDetail .see_createDate").html(new Date(get.createDate).format('yyyy-MM-dd hh:mm:ss'));
    if(socialList.length > 0){
        let sortList = [];
        for(let i = 0 ; i < 10; i++){ sortList.push([]);   }
        for(var r in socialList){
            let item = socialList[r];
            let _index = Number(item.type);
            sortList[_index].push(item);
        }
        let sortAfter = [];
        for(let j = 0 ; j < 10; j++){ sortAfter.push(...sortList[j]); }
        let allStr = '';
        for(var t in sortAfter){
            let item = sortAfter[t];
            if(t%2===0){
                allStr += `<tr><td>${item.name}</td><td>${item.code}</td>`
            }else{
                allStr += `<td>${item.name}</td><td>${item.code}</td></tr>`
            }
        }
        if(sortAfter.length % 2 !== 0){
            allStr += `<td> </td><td> </td></tr>`
        }
        $(".see_otherContact").html(allStr);
    }
    bounce_Fixed5.show($("#contactSeeDetail"));
}
// creator: hxz 2020-12-10 收货人那里的新增收货人
function chooseCusContactOk() {
    let selectObj = $("#chooseCusContact").find(".fa-dot-circle-o");
    if(selectObj.length > 0){
        let strInfo = selectObj.siblings("span.hd").html();
        let info = JSON.parse(strInfo);
        $($("#target").val())
            .val(info.name).data('orgData',selectObj.next().html())
            .siblings(".hd").html(strInfo) ;
        bounce_Fixed4.cancel();

    }else layer.msg('请先选择人员')
}
//  create: hxz 2021-05-11 保留三位小数
function tofixed3(obj) {
    let v = obj.value
    v = v.replace(/[^\d.]/g, "");
    v = v.replace(/^\./g, "");
    v = v.replace(/\.{2,}/g, ".");
    v = v.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".")
    let vArr = v.split('.')
    if(vArr[0].length > 9){ // 最多9位
        vArr[0] = String(vArr[0]).substr(0,9);
    }
    v = vArr[0]
    if(vArr.length > 1){
        if(vArr[1].length > 4){
            vArr[1] = vArr[1].substr(0,4);
        }
        v += '.' + vArr[1];
    }
    obj.value = v
}
//  create: hxz 2021-05-11 确定新增收货地址
function addReceiveOk (obj){
    let shType = obj.data("type");
    var id   = '', url = ``;
    var type = '';
    let contactInfo = ``;
    var json = {
        'type': obj.data("type")
    }
    if (shType === 1) {
        json.address = $("#ReceiveAddress").val();
        id   = $("#newReceiveInfo").data('id');
        type = $("#newReceiveInfo").data('type');
        contactInfo = JSON.parse($("#ReceiveName").siblings(".hd").html());
    } else if (shType === 3) {
        let regionCon = $("#regionCon").val();
        let contactName = $("#areaName").val();
        if (regionCon === "" || contactName === "") {
            layer.msg('还有必填项尚未填写!');
            return false;
        }
        let code = $("#regionCon").siblings(".hd").html();
        let path = code.split(',');
        json.address = regionCon;
        json.regionCode = path[path.length - 1];
        json.requirements = $("#requirements").val();
        id   = $("#newReceiveAreaInfo").data('id');
        type = $("#newReceiveAreaInfo").data('type');
        contactInfo = JSON.parse($("#areaName").siblings(".hd").html());
    }
    json.contact = contactInfo.contact;
    json.mobile = contactInfo.mobile;
    var params = {}
    if (type == 'new') {
        url = `../sales/addCustomerAddress.do`;
        json.customerContact = contactInfo.id
        json = JSON.stringify(json);
        params = {
            'customerId': id,
            'shAddress': json
        }
    }else if(type == 'update'){
        json.id = id
        params  = json;
        url = `../sales/updateCustomerAddress.do`;
    }
    $.ajax({
        url: url,
        data: params,
        success: function (data) {
            var status = data.status;
            if (status === '1' || status === 1) {
                let msg = type == 'update'? "修改成功":"新增成功";
                let cusId = $(".customerName").data("cusId");
                let source = $("#receiveFirstPage").data('source');
                let orderEditType = $("#newOrder").data("orderEditType")
                orderEditType === 'add' ? orderEditType='new':"";
                if (source === "newOrder") {
                    setAddressCon(cusId, orderEditType, $(".receiveAddress1"));
                } else if (source === 'newGood') {
                    setAddressCon(cusId, orderEditType);
                    manageDeliveryPlace();
                }
                receiveFirstPage(source);
                layer.msg(msg);
            } else {
                let msg = ``;
                if (type == 'new') {
                    if (shType === 1) {
                        msg = `新增收货地址失败`;
                    } else if  (shType === 1) {
                        msg = `新增到货区域失败`;
                    }
                } else if (type == 'update') {
                    if (shType === 1) {
                        msg = `修改收货地址失败`;
                    } else if  (shType === 1) {
                        msg = `修改到货区域失败`;
                    }
                }
                layer.msg(msg);
            }
        }
    })
    bounce_Fixed3.cancel();
}
// creator: 张旭博，2018-05-30 10:10:55，设置收货地址
function setAddressCon(cusId, type, setObj) {
    var customerID = cusId;
    $.ajax({
        async:false,
        url:"../sales/getAddressListByCondition.do",
        data:{"cusId":customerID},
        success:function (data) {
            var addrList = data["data"] || [];
            var addrListStr = '<option value="">------- 请选择收货地点 -------</option>';
            var tailStr = '<option value="到本公司自提">到本公司自提</option><option value="本订单有多个收货地点">本订单有多个收货地点</option>';
            if(data === null) {
                if(type == "new"){
                    $(".receiveAddress1").html(addrListStr + tailStr);
                }
                $(".receiveAddressCon").html("");
            } else {
                if (addrList && addrList.length > 0) {
                    for (var i = 0; i < addrList.length; i++) {
                        addrListStr += '<option value="' + addrList[i].id + '">' + addrList[i].address + '</option>';
                    }
                }
                let oldVal = ''
                if(setObj){
                    oldVal = setObj.val()
                }
                if(type == "new"){
                    $(".receiveAddress1").html(addrListStr + tailStr);
                }
                $(".receiveAddressCon").html(JSON.stringify(data));
                setObj && setObj.val(oldVal)
            }
        }
    })
}

// creator: 张旭博，2018-05-30 10:09:04，设置商品代号输入框下拉列表
function setOuterCon(cusId, invoiceRequire) {
    if(invoiceRequire == '3'){ invoiceRequire = 4 }
    $.ajax({
        url: "../sales/getPdCustomerProductOuterSn.do",
        data: {"cusId":cusId, 'invoiceRequire': invoiceRequire } ,
        success: function (data){
            var pCPOS = data["pdCustomerProductOuterSn"]; // 专属商品
            var productMap = data["productMap"]; // 通用型商品
            if((!pCPOS  && !productMap) || ((pCPOS &&pCPOS.length<1) &&(productMap && productMap.length<1)) ){
                layer.msg("此客户下没有商品，无法新增订单！")
                $(".newGood").data("goodInfo",'');
                $(".newGoodCommon").data("goodInfo",'');
            }else{
                if(pCPOS && pCPOS.length>0){
                    $(".newGood").data("goodInfo",pCPOS);
                }else{
                    $(".newGood").data("goodInfo",'');
                }
                if( productMap && productMap.length>0){
                    $(".newGoodCommon").data("goodInfo",productMap);
                }else{
                    $(".newGoodCommon").data("goodInfo",'');
                }
            }
        }
    })
    if(Number(invoiceRequire) === 1){ // 增专的 需要系统税率
        $.ajax({
            "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
            async: false,
            success: function (res) {
                var str = '<option value="">--- 请选择 ---</option>';
                var list = res["financeInvoiceSettings"];
                if (list && list.length > 0) {
                    for (var i = 0; i < list.length; i++) {
                        if (list[i]["category"] == '1'){
                            var rate = list[i]["enableTaxTate"];
                            var rates = rate.split(",");
                            if (rates && rates.length >0){
                                for (var r = 0; r < rates.length; r++) {
                                    var selectedStr = "";
                                    str += "<option  value='" + rates[r] + "'>" + rates[r] + "%</option>";
                                }
                            }
                        }
                    }
                }
                $(".rate").html(str);
            }
        });
    }
}

// update: hxz，2023-11-10  根据商品代号获得其他信息
function setGoodDetail(data, fuType) {
    if (fuType === "update") { // 修改订单的情况

    }else{
        let newGoodsItem = data
        let oldData = null
        $("#gsBd .hd").each(function () {
            let info = $(this).html()
            info = JSON.parse(info)
            if(info.salesRelationship_ === newGoodsItem.id){ // 有同一个商品
                let nameOk = newGoodsItem.outerName == info.outerName
                let snOk = newGoodsItem.outerSn == info.outerSn
                let modelOk = newGoodsItem.model == info.model
                let speOk = newGoodsItem.specifications == info.specifications
                if(nameOk && snOk && modelOk && speOk){
                }else{
                    oldData = info
                }
            }
        })
        if(oldData && oldData.salesRelationship_){
            $("#newGoodsTip").data('infoo', oldData) // 原数据
            $("#newGoodsTip").data('infon', data) // 新数据
            $("#newGoodsTip .oralData").html(`${ oldData.outerSn }/${ oldData.outerName }/${ oldData.model }/${ oldData.specifications } `)
            $("#newGoodsTip .newData").html(`${ data.outerSn }/${ data.outerName }/${ data.model }/${ data.specifications } `)
            bounce_Fixed2.show($("#newGoodsTip"))
            return false
        }

    }
    let priceMemo = ''
    let type = $("#newGood").data("ttl"); // 1  通用   2专属，
    let setPrice = false ;
    let price = data.unitPrice || "";
    if (Number(type) === 2){
        priceMemo = data.priceDesc
        setPrice = true ;
        $("#newGood .modifyPrice").data('modify', 0);
        $(".modify").hide();
        $("#newGood .rate").prop("disabled",true)
        $("#newGood .noPrice").prop("disabled",true)
        $("#newGood .price").data("able", 'true').prop("disabled",true)
    }else if(Number(type) === 1){
        // 通用型按照 订单上选的类型展示
        let invoiceCategory = Number(data.invoiceCategory); // 1 增专 2 增普 3 其他票 4 不开票
        let invoiceRequire = Number($(".invoiceRequire").data("invoice"));// 1 增专 2 增普 3 不开票
        // let same = (invoiceCategory === 1 && invoiceRequire === 1) || (invoiceCategory === 2 && invoiceRequire === 2) || (invoiceCategory === 4 && invoiceRequire === 3) ;
        let same = true ;
        if(same){ // 订单设置的开票种类 和 通用商品本身设置的开票种类 相同
            priceMemo =''
            setPrice = true ;
            if(invoiceRequire === 2){
                price = data.unitPriceInvoice || "" ;
            }else if(invoiceRequire === 3){
                price = data.unitPriceNoinvoice || "" ;
            }
        }else{
            setPrice = false ;
        }
        if(invoiceRequire === 1){
            if(data.unitPriceInvoice){ priceMemo += `非专票开票价${data.unitPriceInvoice}元`; }
            if(data.unitPriceNoinvoice){ priceMemo += `不开票价${data.unitPriceNoinvoice}元`; }
            if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
        }else if(invoiceRequire === 2){
            if(data.unitPriceNotax){ priceMemo += `开具${data.taxRate}%专票的不含税价为${data.unitPriceNotax}元，含税价为${data.unitPrice}元`;  }
            if(data.unitPriceNoinvoice){ priceMemo += `不开票价${data.unitPriceNoinvoice}元`; }
            if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
        }else if(invoiceRequire === 3){
            if(data.unitPriceNotax){ priceMemo += `开具${data.taxRate}%专票的不含税价为${data.unitPriceNotax}元，含税价为${data.unitPrice}元`;  }
            if(data.unitPriceInvoice){ priceMemo += `非专票开票价${data.unitPriceInvoice}元`; }
            if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
        }
    }
    let addressList = data.addressList || [];
    let orderAddress = $("#newOrder .receiveAddress").val()
    if (fuType === "update") {
        var addrListStr =
            '<option value=""></option>'+
            '<option value="' + data.receiveAddressId + '" selected>' + data.receiveAddress + '</option>';

        $(".receiveAddress2").html(addrListStr);
        priceMemo = data.priceInfo.priceDesc
    } else {
        setAddress2Con(addressList);
    }
    if (orderAddress === "到本公司自提") {
        $(".receiveAddress2").val("");
    } else {
        $(".receiveAddress2").val($("#newOrder .receiveAddress").val());
    }
    if(setPrice){
        let unitPriceNotax = parseFloat(data.unitPriceNotax && data.unitPriceNotax.toFixed(10)) || 0
        $("#newGood .rate").val(`${data.taxRate}`).data("val",`${data.taxRate}`)
        $(".noPrice").val(unitPriceNotax).data("val",`${unitPriceNotax}`).removeAttr("placeholder");
        $(".price").val(price).data("val",`${price}`).data("able", 'false').removeAttr("placeholder");
    }else{
        $("#newGood .rate").val(``).data("val",``)
        $(".noPrice").val(``).data("val",``).removeAttr("placeholder");
        $(".price").val(``).data("val",``).data("able", 'false').removeAttr("placeholder");
    }
    $(".outerName").val(data.outerName);
    $(".outerSn").val(data.outerSn);
    $(".goodNum").val("");
    $(".unit").val(data["unit"]);
    $(".currentStock").val(data.currentStock);	//当前库存
    $(".minimumStock").val(data.minimumStock);	//最低库存
    $(".goodNum_stock").val(0) 	//货物件数
    $(".priceMemo").val(priceMemo).attr("title", priceMemo) 	//价格说明
    data.priceMemo = priceMemo;
    $(".invoiceTip").html(JSON.stringify(data));
    $("#newGood .modifyPrice ").show()
}

// creator: hxz，2023-11-10 09:37:24  新增商品确定
function newGoodsTipOk() {
    let selectFa = $("#newGoodsTip .fa-dot-circle-o")
    if(selectFa.length === 0){
        layer.msg('请先选择')
    }else{
        let type = selectFa.parent('.ngTipR').data('type') // 1- 原数据，2-新数据
        let oldData = $("#newGoodsTip").data('infoo') // 原数据
        let newData = $("#newGoodsTip").data('infon') // 新数据
        if(type === 1){
            setGoodDetail2(oldData)
        }else{
            setGoodDetail2(newData)
        }
        bounce_Fixed2.cancel()
    }

}

function cancelSelect() {
    let type = $("#newGood").data("ttl")
    bounce_Fixed2.cancel()
    newGoodBtn('add' , type)
}
// creator: hxz，2023-11-10  新增的情况 赋值
function setGoodDetail2(data) {
    let priceMemo = ''
    let type = $("#newGood").data("ttl"); // 1  通用   2专属，
    let setPrice = false ;
    let price = data.unitPrice || "";
    if (Number(type) === 2){
        priceMemo = data.priceDesc
        setPrice = true ;
        $("#newGood .modifyPrice").data('modify', 0);
        $(".modify").hide();
        $("#newGood .rate").prop("disabled",true)
        $("#newGood .noPrice").prop("disabled",true)
        $("#newGood .price").data("able", 'true').prop("disabled",true)
    }else if(Number(type) === 1){
        // 通用型按照 订单上选的类型展示
        let invoiceCategory = Number(data.invoiceCategory); // 1 增专 2 增普 3 其他票 4 不开票
        let invoiceRequire = Number($(".invoiceRequire").data("invoice"));// 1 增专 2 增普 3 不开票
        // let same = (invoiceCategory === 1 && invoiceRequire === 1) || (invoiceCategory === 2 && invoiceRequire === 2) || (invoiceCategory === 4 && invoiceRequire === 3) ;
        let same = true ;
        if(same){ // 订单设置的开票种类 和 通用商品本身设置的开票种类 相同
            priceMemo =''
            setPrice = true ;
            if(invoiceRequire === 2){
                price = data.unitPriceInvoice || "" ;
            }else if(invoiceRequire === 3){
                price = data.unitPriceNoinvoice || "" ;
            }
        }else{
            setPrice = false ;
        }
        if(invoiceRequire === 1){
            if(data.unitPriceInvoice){ priceMemo += `非专票开票价${data.unitPriceInvoice}元`; }
            if(data.unitPriceNoinvoice){ priceMemo += `不开票价${data.unitPriceNoinvoice}元`; }
            if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
        }else if(invoiceRequire === 2){
            if(data.unitPriceNotax){ priceMemo += `开具${data.taxRate}%专票的不含税价为${data.unitPriceNotax}元，含税价为${data.unitPrice}元`;  }
            if(data.unitPriceNoinvoice){ priceMemo += `不开票价${data.unitPriceNoinvoice}元`; }
            if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
        }else if(invoiceRequire === 3){
            if(data.unitPriceNotax){ priceMemo += `开具${data.taxRate}%专票的不含税价为${data.unitPriceNotax}元，含税价为${data.unitPrice}元`;  }
            if(data.unitPriceInvoice){ priceMemo += `非专票开票价${data.unitPriceInvoice}元`; }
            if(data.unitPriceReference){ priceMemo += `参考价格${data.unitPriceReference}元` }
        }
    }
    let addressList = data.addressList || [];
    let orderAddress = $("#newOrder .receiveAddress").val()
    setAddress2Con(addressList);

    if (orderAddress === "到本公司自提") {
        $(".receiveAddress2").val("");
    } else {
        $(".receiveAddress2").val($("#newOrder .receiveAddress").val());
    }
    if(setPrice){
        let unitPriceNotax = parseFloat(data.unitPriceNotax && data.unitPriceNotax.toFixed(10)) || 0
        $("#newGood .rate").val(`${data.taxRate}`).data("val",`${data.taxRate}`)
        $(".noPrice").val(unitPriceNotax).data("val",`${unitPriceNotax}`).removeAttr("placeholder");
        $(".price").val(price).data("val",`${price}`).data("able", 'false').removeAttr("placeholder");
    }else{
        $("#newGood .rate").val(``).data("val",``)
        $(".noPrice").val(``).data("val",``).removeAttr("placeholder");
        $(".price").val(``).data("val",``).data("able", 'false').removeAttr("placeholder");
    }
    $(".outerName").val(data.outerName);
    $(".outerSn").val(data.outerSn);
    $(".goodNum").val("");
    $(".unit").val(data["unit"]);
    $(".currentStock").val(data.currentStock);	//当前库存
    $(".minimumStock").val(data.minimumStock);	//最低库存
    $(".goodNum_stock").val(0) 	//货物件数
    $(".priceMemo").val(priceMemo).attr("title", priceMemo) 	//价格说明
    data.priceMemo = priceMemo;
    $(".invoiceTip").html(JSON.stringify(data));
    $("#newGood .modifyPrice ").show()
}
// creator: hxz 2021-05-11 生产方的评审负责人
function gettProductionPrincipal() {
    $.ajax({
        'url':"../sale/getProductionPrincipal",
        "data":{ "oid":sphdSocket.user.oid },
        success:function (res) {
            var list = res || [] , str = "<option value=''>----- 请选择 -----</option>"
            list.forEach(function(item){
                str += `<option value='${item.userID}'>${item.userName}</option>`
            })
            $(".principal").html(str).prop("disabled", false);
        }
    })
}
// creator: 李玉婷，2021-10-12 14:28:07，设置生产方的评审负责人
function getReviewSet() {
    $.ajax({
        'url':"../sales/ordersSet",
        "data":{},
        success:function (res) {
            let state = res.reviewSet; //1 需要评审  0：无需评审
            if (state === 0 || state === '0') {
                $(".principal").prop("disabled", true).html('<option value="">--</option>');
            } else {
                gettProductionPrincipal();
            }
        }
    })
}
// create :hxz 2021-05-11 格式化显示 开票要求
function formatInviceRequire(type) {
    let str = ''
    switch (Number(type)){
        case 1 :    str = '需开具增值税专用发票'; break
        case 2 :    str = '需开具其他发票'; break
        case 3 :    str = '不开发票'; break
        case 0 :
        case 4 :    str = ''; break
        default: str = ''
    }
    return str
}
// creator: 张旭博，2018-05-30 10:12:19，点击新增商品按钮
function newGoodBtn(thisObjType , type) { // type : 1 通用  2 专属  thisObjType: "add"商品新增 "update"商品修改
    $("#newGood").find("input,textarea,select").val("");
    $(".outerSn+.ty-optionCon").html("") ;
    $(".outerSn+.ty-optionCon").hide() ;
    if(hasAuthority(0) === true){
        $("#msTips .msTip").html("您没有此权限！");
        bounce_Fixed.show($("#msTips"));
    }else{
        $("#newGood .invoiceTip").html("");
        $("#newGood .outerSn").prop("disabled",false)
        $("#newGood .requireDate").prop("disabled",false)
        $("#newGood .receiveAddress2").prop("disabled",false)
        bounce_Fixed.show($("#newGood")) ;
        $("#newGood").data("ttl", type)
        $("#newGood").data("editType", thisObjType)
        $(".redRequie").hide();
        $("#newGood").find(".noPrice,.price").attr("placeholder","")
        let invoiceRequire = $(".invoiceRequire").data("invoice"), invoiceTip  = '';
        switch (Number(invoiceRequire)){
            case 1:
                $(".invoice1").show();
                $(".changeTtl").html('含税单价');
                invoiceTip = '本订单需开具增值税专用发票。选择商品时，您无法选到系统中没有该开票价格的商品。'
                if(type === 1) {
                    $(".redRequie").show();
                    invoiceTip = '本订单内商品需开具增值税专用发票。'
                }
                break;
            case 2:
                $(".invoice1").hide();
                $(".changeTtl").html('开普通发票的开票单价');
                invoiceTip = '本订单需开具普通发票。选择商品时，您无法选到系统中没有该开票价格的商品。'
                if(type === 1) {
                    invoiceTip = '本订单内商品需开具增值税专用发票以外的发票。'
                }
                break;
            case 3:
                $(".invoice1").hide();
                $(".changeTtl").html('不开发票的单价');
                invoiceTip = '本订单不需开具发票。您选择商品时，无法选到有开票价格的商品。'
                if(type === 1) {
                    invoiceTip = '本订单内商品不需开具发票。'
                }
                break;
            default:
        }
        $(".invoiceCatTip").html(invoiceTip);
        let ttl1 = '选择'
        // $("#newGood .goodNum").attr("onkeyup", "clearNum(this); manLen(9,$(this))");
        let orderEditType = $("#newOrder").data("orderEditType")
        if(orderEditType === "update"){ // 修改订单
            ttl1 = '增加'
        }
        if(thisObjType === "update"){ // 修改订单的修改商品
            $("#newGood .bonceHead span").html("修改要货信息")
        }
        if(type === 2){
            $(".isZ").show(); $(".isT").hide()
            $("#newGood .modifyPrice").data('modify',0)
            $("#newGood .rate").prop("disabled",true)
            $("#newGood .noPrice").prop("disabled",true)
            $("#newGood .price").prop("disabled",true)
            if(thisObjType !== "update"){
                $("#newGood .bonceHead span").html(`${ttl1}专属商品`)
            }
        }else if(type === 1){
            $(".isT").show()
            $(".isZ").hide()
            $("#newGood .rate").prop("disabled",false)
            $("#newGood .noPrice").prop("disabled",false)
            $("#newGood .price").prop("disabled",false)
            if(thisObjType !== "update"){
                $("#newGood .bonceHead span").html(`${ttl1}通用型商品`)
            } }

        bounce.everyTime('0.5s','pack',function(){
            //计算包装
            var pack = $("#newGood .goodNum_stock").attr("pack");        //包装信息（格式：1,2,4）
            var packNum = 1;                        //包装数量（1x2x4=8）
            var pack_amount = 0;                    //货物件数

            var outPlan = $("#newGood .goodNum").val();

            //计算包装数量
            if(pack !== "null" && pack !== undefined && pack !== "" && pack !== "0"){
                var packObj = pack.split(",");
                for(var i=0;i<packObj.length;i++){
                    packNum  *= Number(packObj[i]);
                }
            }else{
                packNum = 1;
            }
            //计算货物件数
            if(outPlan !== "" ){
                outPlan = parseInt(outPlan);
                pack_amount = Math.ceil(outPlan/packNum);
            }else{
                pack_amount = 0;
            }
            $("#newGood .goodNum_stock").val(pack_amount);
        });
    }
}


// creator: 张旭博，2018-05-30 10:12:19，新增订单/修改订单 - 新增商品 - 保存商品信息
function sureNewGood() {
    let editType = $("#newGood").data("editType")
    var goodsInfo = $("#newGood .invoiceTip").html();
    if(goodsInfo.length > 10){
        goodsInfo = JSON.parse(goodsInfo);
        let goodNum 		= $("#newGood .goodNum").val();
        let requireDate 	= $("#DateOfArrival").val();
        let receiveAddress 	= $("#newGood .receiveAddress2").find("option:selected").html();
        let receiveAddressId 	= $("#newGood .receiveAddress2").val();

        let isok = false
        if(goodsInfo.length ===0){
            isok = true
            layer.msg('<i class="fa fa-exclamation-triangle"></i>请输入或选择存在商品代号！');
        }else if(requireDate == ""  ){
            isok = true
            layer.msg('<i class="fa fa-exclamation-triangle"></i>请选择要求到货时间 ！');
        }else if(goodNum == "" || goodNum == undefined){
            isok = true
            layer.msg('<i class="fa fa-exclamation-triangle"></i>请输入商品数量！');
        }else if(receiveAddressId === "" || receiveAddressId == undefined){
            isok = true
            layer.msg('<i class="fa fa-exclamation-triangle"></i>请选择收货地点！');
        }
        let rate = $("#newGood .rate").val();
        let price = $("#newGood .price").val();
        let noPrice = $("#newGood .noPrice").val();
        let priceMemo = $("#newGood .priceMemo").val();
        let invoiceRequire = Number($(".invoiceRequire").data("invoice"));// 1 增专 2 增普 3 不开票
        //   unitPrice; 单价(含税) taxRate 税率(增专) unitPriceNotax; 不含税单价(增专) unitPriceInvoice; 开票单价(增通) unitPriceNoinvoice;不开票单价
        //  unitPriceReference;//参考价格  priceDesc; 价格描述
        if(invoiceRequire === 1){ // 增专
            if(!price || !noPrice){
                layer.msg('<i class="fa fa-exclamation-triangle"></i>请输入含税单价或者不含税单价！');
                return false
            }
            goodsInfo.priceInfo = { "unitPrice":price, "taxRate":rate, "unitPriceNotax":noPrice }
        }else if(invoiceRequire === 2){
            if(!price){
                layer.msg('<i class="fa fa-exclamation-triangle"></i>开普通发票的开票单价！');
                return false
            }
            goodsInfo.priceInfo = { "unitPriceInvoice":price }
        }else if(invoiceRequire === 3){ //不开票
            if(!price){
                layer.msg('<i class="fa fa-exclamation-triangle"></i>请输入不开票单价！');
                return false
            }
            goodsInfo.priceInfo = { "unitPriceNoinvoice":price }
        }
        goodsInfo.invoiceRequire = invoiceRequire
        goodsInfo.priceInfo.priceDesc = priceMemo

        goodsInfo.goodNum = goodNum
        goodsInfo.requireDate = requireDate
        goodsInfo.receiveAddress = receiveAddress
        goodsInfo.receiveAddressId = receiveAddressId
        let ttl = $("#newGood").data("ttl"); // 1-通用 2-专属；
        goodsInfo.goodsType = ttl

        if(isok ){
            return false
        }

        let goodStr = `
            <tr>
                <td>${goodsInfo.outerSn}</td>
                <td>${goodsInfo.outerName}</td>
                <td>${price}</td>
                <td>${goodsInfo.unit}</td>
                <td>${goodsInfo.goodNum}</td>
                <td>${goodsInfo.requireDate}</td>
                <td>${goodsInfo.receiveAddress}</td>
                <td>
                    <span class="ty-color-blue updateGoodBtn">修改</span>
                    <span class="ty-color-red deleteGoodBtn">删除</span>
                    <span class="hd">${JSON.stringify(goodsInfo)}</span>
                </td>
            </tr>  
        `;
        if( editType == "add"){
            $("#gsBd").append(goodStr);
        }else if( editType == "update"){
            editGsObj.parents("tr").after(goodStr);
            editGsObj.parents("tr").remove()
        }
        bounce_Fixed.cancel();
    }
}
// create: hxz，2021-05-27 10:16:13 计算价格
function setprice(num) { // 1- 不含税，2-含税
    let invoiceRequire = Number( $("#newOrder .invoiceRequire").data("invoice") );
    if(invoiceRequire === 1){
        let noprice = Number($("#newGood .noPrice").val());
        let price = Number($("#newGood .price").val());
        let rate = Number($("#newGood .rate").val());
        if(num === 1){
            if(rate > 0 && noprice > 0){
                price = noprice * (rate/100 + 1) ;
                $("#newGood .price").val(parseFloat(price.toFixed(10)));
            }
            if(noprice === 0){
                $("#newGood .price").prop("disabled", false)
            }else{
                $("#newGood .price").prop("disabled", true)
            }
        }else if(num === 2){
            if(rate > 0 && price > 0){
                noprice = price / (rate/100 + 1) ;
                $("#newGood .noPrice").val(parseFloat(noprice.toFixed(10)));
            }
            if(price === 0){
                $("#newGood .noPrice").prop("disabled", false)
            }else{
                $("#newGood .noPrice").prop("disabled", true)
            }
        }else{
           let priceDis =  $("#newGood .price").attr("disabled");
           let nopriceDis =  $("#newGood .noPrice").attr("disabled");
           if(priceDis){ // 按照不含税算
               if(rate > 0 && noprice > 0){
                   price = noprice * (rate/100 + 1) ;
                   $("#newGood .price").val(parseFloat(price.toFixed(10)));
               }
           }else{ // 按照含税算
               if(rate > 0 && price > 0){
                   noprice = price / (rate/100 + 1) ;
                   $("#newGood .noPrice").val(parseFloat(noprice.toFixed(10)));
               }
           }
        }
    }
}
// updator: hxz，2021-05-14 10:16:13，新增订单 - 确认
function sureNewOrder() {
    let type = $("#newOrder").data("type"); // // buAndPro - 补发的订单， undefined - 新增订单
    let orderEditType = $("#newOrder").data("orderEditType")
    var collect = $("#newOrder").data('collect');
    var orderTotal = $("#newOrder .orderTotal").val();

    var customerName = $("#newOrder .customerName").val();
    var OrderReceivedDate = $("#OrderReceivedDate").val();
    var invoiceRequire = $("#newOrder .invoiceRequire").data("invoice");
    var principal = $("#newOrder .principal").val();
    var customerId = $("#newOrder .customerName").data("cusId");
    var orderNumber = $("#newOrder .orderNumber").val();
    var receiveAddress1 = $("#newOrder .receiveAddress1").val();
    var orderListArr = {}, goodListArr = [] ;
    let url = ''
    var data ={
        'inProgress': (type === 1 ? 1:0)
    }
    if(orderEditType === "update"){ // 修改订单
        url = "../sales/updateSlOrders.do"
        orderListArr = {
            "id": $("#newOrder").data("orderid") ,
            "contractAmount": orderTotal ,
        } ;
        $("#gsBd tr").each(function () {
            let goodInfo = JSON.parse($(this).find(".hd").html())
            var goodInfoJson = {
                "id"				: goodInfo["id"] , // 商品对照id
                "sid"				: goodInfo["sid"] ,// 明细id
                "outerName"		: goodInfo["outerName"]  ,
                "outerSn"		: goodInfo["outerSn"]  ,
                "model"		: goodInfo["model"]  ,
                "specifications"		: goodInfo["specifications"]  ,
                "SlInnerSn"		: goodInfo["slInnerSn"]  ,
                "SlInnerSnName"	: goodInfo["slInnerSnName"] ,
                "SlOuterSn"		: goodInfo["slOuterSn"] ,
                "SlOutterSnName"	: goodInfo["slOutterSnName"] ,
                "SlUnit"			: goodInfo["slUnit"] ,
                "SlUnitPrice"		: goodInfo["slUnitPrice"] ,
                "deliveryDate": goodInfo.requireDate,
                "amount":goodInfo.goodNum,
                "address":goodInfo.receiveAddress,
                "addressId":goodInfo.receiveAddressId,
                ...goodInfo.priceInfo
            };
            goodInfoJson.unitPriceNotax = (goodInfoJson.unitPriceNotax && goodInfoJson.unitPriceNotax.toString()) || (goodInfoJson.unitPriceInvoice && goodInfoJson.unitPriceInvoice.toString())
            if(goodInfo.updateType === 1){// 原来的商品

            }else{ // 新增的商品
                goodInfoJson.id = goodInfo.id
                goodInfoJson.sid = ""
            }
            goodListArr.push(goodInfoJson);
        });
    }
    else{ // 新增订单，补单
        if(collect.type === 2){
            if(orderTotal < Number(collect.collectAmount)){
                layer.msg( '本次需补发的订单总额不可低于已回款的' + collect.collectAmount + '元！');
                return false;
            }
        }
        //var review = $("#newOrder .principal").data("review");
        url = "../sales/addSlOrders.do"
        orderListArr = {
            "cusid" : customerId ,
            "cusName" : customerName,
            "signDate" : OrderReceivedDate ,
            "invoiceRequire" : invoiceRequire ,
            "principal" : principal ,
            //"address" : receiveAddress1 ,
            "deliveryType" : "" ,
            "sn" : orderNumber ,
            "contractAmount" : orderTotal,
            "ordersAmount" : orderTotal
            //"is_review" : review
        };
        if (receiveAddress1 === "本订单有多个收货地点") {
            orderListArr.deliveryType = 3;//1-收货 2-自提 3-多个收货地址
        } else if (receiveAddress1 === "到本公司自提"){
            orderListArr.deliveryType = 2;
        } else {
            orderListArr.address = receiveAddress1;
            orderListArr.deliveryType = 1;
        }
        $("#gsBd tr").each(function () {
            let goodInfo = JSON.parse($(this).find(".hd").html())
            var goodInfoJson = {
                "id": goodInfo.id ,
                "deliveryDate": goodInfo.requireDate,
                "amount":goodInfo.goodNum,
                "address":goodInfo.receiveAddress,
                "customerAddress":goodInfo.receiveAddressId,
                "addressId":goodInfo.receiveAddressId,
                ...goodInfo.priceInfo
            };
            goodListArr.push(goodInfoJson);
        });
        if (collect.type === 2){
            data.type = 3;
            data.applicationId = collect.collectId;
            data.collectMoney = collect.collectAmount;
        }
    }
    data.save_slOrders = JSON.stringify(orderListArr)
    data.save_slOrdersItemList = JSON.stringify(goodListArr)
    $.ajax({
        url: url,
        data: data ,
        success: function (data){
            var status = data["status"];
            bounce.cancel();
            if(status == 1){
                if(orderEditType === "update") { // 修改订单
                    if(status == 1){
                        layer.msg("修改成功！")
                        getOrderList(1, 15);
                    }else{
                        $("#fixMsTips .msTip").html(data["message"]);
                        bounce_Fixed.show($("#fixMsTips"));
                    }
                }else {
                    if (collect.type == '1') {
                        layer.msg("新增成功");
                        getOrderList(1, 10);
                    } else if (collect.type == '2') {
                        supplyOrderBtn();
                        getOrderList(1, 10);
                    } else if (collect.type == '3') {
                        var detailId = GetUrlQuery('id');
                        getAllocated(detailId);
                    }
                }
            }else{
                $("#fixMsTips .msTip").html("新增失败！没有商品");
                bounce_Fixed.show($("#fixMsTips"));
            }
        }
    })
}

// creator: 张旭博，2018-06-11 15:26:54，根据缓存设置商品代号下拉列表
function setOuterSnOption() {
    let type = $("#newGood").data("ttl")
    let pCPOS
    if(type === 1){ // 通用
        pCPOS = $(".newGoodCommon").data("goodInfo");
    }else if(type === 2){ // 专属
        pCPOS = $(".newGood").data("goodInfo");
    }
    var inputVal = $("#newGood .outerSn").val();
    var outerLiStr = "";
    if(inputVal == ""){
        //未输入值时显示获得的所有数据
        for(var i in pCPOS){
            var invoiceCategory = Number(pCPOS[i]['invoiceCategory']); // 1 增专 2 增普 3 其他票 4 不开票
            // if(invoiceCategory === '2'){
            //     pCPOS[i]['unitPrice'] = pCPOS[i]['unitPriceInvoice']
            // }else if(invoiceCategory === '4'){
            //     pCPOS[i]['unitPrice'] = pCPOS[i]['unitPriceNoinvoice']
            // }else if(invoiceCategory == ''){
            //     pCPOS[i]['unitPrice'] = pCPOS[i]['unitPriceReference']
            // }
            outerLiStr += '<li class="ty-option" id="'+pCPOS[i].id+'"><span>'+pCPOS[i].outerSn+'</span><div class="hd">'+JSON.stringify(pCPOS[i])+'</div></li>';
        }
    } else {
        //输入值时将此值与所有数据比对，将含有此值的商品代号显示出来
        var isSameObj = null
        for(var j in pCPOS){
            var outerSn = pCPOS[j].outerSn;
            var choose = outerSn.indexOf(inputVal);
            if(outerSn === inputVal){
                isSameObj = pCPOS[j]
            }
            if(choose != "-1"){
                outerLiStr += '<li class="ty-option" id="'+pCPOS[j].id+'"><span>'+pCPOS[j].outerSn+'</span><div class="hd">'+JSON.stringify(pCPOS[j])+'</div></li>';
            }
        }
        if(isSameObj){
            setGoodDetail(isSameObj);
        } else {
            $("#newGood .invoiceTip").html("");
            $("#newGood input:not(.outerSn)").val("");
        }
    }
    $(".ty-optionCon").html(outerLiStr);
}
// creator: 李玉婷，2019-09-19 09:37:24，清除自定义标签
function clearLableText(obj) {
    obj.siblings().val("");
}
// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.parent().siblings(".textMax").text( curLength +'/' + max );
    obj.siblings(".textMax").text( curLength +'/' + max );
}
// creator: 李玉婷，2019-09-19 10:14:23，自定义标签确定
function addNewLable(){
    var val = $("#defLable").val();
    var html =
        '<li>' +
        '<span class="sale_ttl1">' + val + '：</span>' +
        '<span class="gap"><input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/></span>'+
        '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
        '</li>';
    $(".otherContact").append(html);
    bounce_Fixed4.cancel();
}
function manLen(len, obj){
    let val = obj.val();
    obj.val(val.length > len ? val.substr(0,8) : val);
}
// creator : hxz 2018-07-18  新增/修改订单 中 编辑商品 - 相关订购信息
function linkOrds(obj) {
    bounce_Fixed2.show($("#linkOrd"));
    $("#linkOrdTb").find("tr:gt(0)").remove();
    var pConObj = obj.parents(".bonceContainer") ;
    var outerSn = pConObj.find(".outerSn").val() ;
    var outerName = pConObj.find(".outerName").val() ;
    var currentStock = pConObj.find(".currentStock").val() ;
    var unit = pConObj.find(".unit").val() ;
    if(outerSn == ''){
        layer.msg('请先选择商品！')
        return false
    }
    var data =  {"outerSn": outerSn  } ;
    var orderEditType = $("#newOrder").data("orderEditType")
    var orderid = $("#newOrder").data("orderid")
    if( orderEditType === "update" ){
        data["orderId"] = orderid ;
    }
    $.ajax({
        "url": "../sale/getOccOrder.do",
        "data": data ,
        success: function (res) {
            var list = res["data"], str = "";
            if (list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    str += "<tr>" +
                        "     <td>"+ (i+1) +"</td>" +
                        "     <td>"+ (list[i]["sn"] ||"") +"</td>" +
                        "     <td>"+ (list[i]["create_name"] ||"") +"</td>" +
                        "     <td>"+ (list[i]["t_amount"] ||"") +"</td>" +
                        "     <td>"+ formatTime(list[i]["earliest_delivery_date"]) +"</td>" +
                        "     <td>"+ (list[i]["customer_name"] ||"") +"</td>" +
                        " </tr>";
                }
                $("#linkOrdTb").append(str);
                $(".ysGs").show(); $(".noGs").hide();
                $("#linkOrdName").html(outerName); $("#linkOrdStock").html(currentStock) ; $("#linkOrdUnit").html(unit);
            }else{
                $(".ysGs").hide(); $(".noGs").show();
            }
        }
    })
}

// creator: lyt，2022-12-11 10:10:55，编辑收货信息
function receiveFirstPage(source){
    let cusID = $(".customerName").val()
    if(!cusID){
        layer.msg("请先选择客户名称！");
        return false;
    }
    let detail = $(".receiveAddressCon").html();
    if (detail !== "") {
        detail = JSON.parse(detail);
        if(detail === null || detail.deliveryType === null || detail.deliveryType === '1' || detail.deliveryType === 1){
            layer.msg("不可选择该客户");
            return false;
        } else{
            let addrList = detail.data || [];
            $(".areaList").hide();
            $(".receiveList").hide();
            $(".areaList table tbody tr:gt(0)").remove();
            $(".receiveList table tbody tr:gt(0)").remove();
            setShList(addrList);
            if (detail.selfState === 1) {
                $("#receiveFirstPage .selfStateTxt").html("关闭");
            } else if (detail.selfState === 1){
                $("#receiveFirstPage .selfStateTxt").html("开启");
            }
            $("#receiveFirstPage").data('source', source);
            bounce_Fixed2.show($("#receiveFirstPage"))
        }
    }
}
// creator: lyt，2022-12-11 11:20:14，编辑收货信息
function receiveFirstPageSure(fun){
    let source = $("#receiveFirstPage").data('source');
    if (fun === 'cancel') {
        bounce_Fixed2.cancel();
        if (source === 'newGood') {
            bounce_Fixed2.show($("#chooseDelivery"))
        }
    } else {
        let cusId = $("#newOrder .customerName").data("cusId")
        let data = {
            "id": cusId,
            "selfState": $("#receiveFirstPage .selfStateTxt").html() === "开启"? 0:1
        }
        $.ajax({
            url: "../sales/editCustomerAddress.do",
            data: data,
            success: function (data) {
                var status = data.success;
                if(status == '1'){
                    bounce_Fixed2.cancel();
                    if (source === 'newGood') {
                        bounce_Fixed2.show($("#chooseDelivery"))
                    }
                }
            }
        })
    }
}
/*creator:lyt 2022/11/15 0015 下午 12:32 删除收货地址*/
function deleteData(obj){
    obj.parents("tr").remove();
}
//creator:lyt 2022/11/14 上门自提 -改变状态
function turnSelfState(obj){
    let str = $(".selfStateTxt").html();
    if (str === "开启") {
        $(".selfStateTxt").html("关闭");
    } else {
        $(".selfStateTxt").html("开启");
    }
}
function setShList(data){
    let able1 = false, areaAble = false;
    let flag = ``, temp = ``, html = ``, str = ``;
    for (var i=0; i < data.length; i++) {
        if (data[i].type === '1') {
            able1 = true;
            flag = `addReceive`
        }else if (data[i].type === '3') {
            flag = `addArea`
            areaAble = true;
        }
        temp =
            '<tr>' +
            '    <td>' + data[i].address + '</td>'+
            '    <td>' + data[i].contact + '</td>' +
            '    <td>' + data[i].mobile + '</td>' +
            '    <td>' +
            '       <span class="ty-color-blue funBtn" data-type="update" data-fun="'+ flag +'">修改</span>' +
            '       <span class="ty-color-red funBtn" data-type="stop" data-fun="stopAddress">停用</span>' +
            '       <span class="hd">'+ JSON.stringify(data[i]) +'</span>' +
            '   </td>' +
            '</tr>';
        if (data[i].type === '1') {
            html += temp;
        }else if (data[i].type === '3') {
            str += temp;
        }
    }
    if (able1 && areaAble) {
        $(".receiveList").show().find("tbody").append(html);
        $(".areaList").show().find("tbody").append(str);
    } else {
        if (able1) {
            $(".receiveList").show().find("tbody").append(html);
        } else if (areaAble) {
            $(".areaList").show().find("tbody").append(str);
        }
    }

}
//creator:lyt 2023/7/25 0025 下午 7:37 新增到货区域
function addArea(obj){
    let  id =``;
    let type = obj.data('type')
    $("#newReceiveAreaInfo").data('type', type);
    bounce_Fixed3.show($("#newReceiveAreaInfo"))
    $("#newReceiveAreaInfo input").val("");
    $("#newReceiveAreaInfo .hd").html("");
    $("#areaName").data('orgData',"");
    $("#newReceiveAreaInfo .bonceHead span").html('新增到货区域');
    if (type === "new"){
        id = $(".customerName").data("cusId");
    } else if (type === "update"){
        let trObj = obj.parents("tr")
        let info = JSON.parse(obj.siblings(".hd").html());
        id = info.id;
        $("#newReceiveAreaInfo .bonceHead span").html('修改到货区域');
        $("#newReceiveAreaInfo").data('trObj', trObj);
        $("#regionCon").val(info.address);
        $("#regionCon").siblings(".hd").html(info.regionCode);
        $("#requirements").val(info.requirements);
        $("#areaName")
            .val(trObj.find("td").eq(1).html()).data('orgData',"")
            .siblings(".hd").html(JSON.stringify(info)) ;
    }
    $("#newReceiveAreaInfo").data('id', id);
}
/*creator:lyt 2022/11/15 0015 下午 12:32 停用收货地址*/
function stopAddress(obj){
    let info = JSON.parse(obj.siblings(".hd").html())
    bounce_Fixed3.show($("#turnStateTip"));
    $("#turnStateTip").data('id', info.id);
}
//creator:lyt Date:2018/11/19 修改-停用按钮
function turnStatenSure(){
    var addressId = $("#turnStateTip").data('id');
    $.ajax({
        url: '../sales/startOrStopAddress.do',
        data: {
            'addressId': addressId,
            'enabled': 0
        },
        success: function (data) {
            var status = data.status;
            if (status === '1' || status === 1) {
                let source = $("#receiveFirstPage").data('source');
                let orderEditType = $("#newOrder").data("orderEditType")
                let cusId = $("#newOrder .customerName").data("cusId")
                orderEditType === 'add' ? orderEditType='new':"";
                if (source === "newOrder") {
                    setAddressCon(cusId, orderEditType, $(".receiveAddress1"));
                } else if (source === 'newGood') {
                    setAddressCon(cusId, orderEditType);
                    manageDeliveryPlace();
                }
                receiveFirstPage(source);
                bounce_Fixed3.cancel();
            } else {
                layer.msg("停止失败！");
            }
        }
    })
}
// creator: 李玉婷，2022-12-11 8:12:24，管理交货地点确定
function setAddress2Con(data, val){
    var addrListStr = '<option value=""></option>';
    if (data && data.length > 0) {
        for (var i = 0; i < data.length; i++) {
            addrListStr += '<option value="' + data[i].id + '">' + data[i].address + '</option>';
        }
    }
    $(".receiveAddress2").html(addrListStr);
    $(".receiveAddress2").val(val);
}
// creator: 李玉婷，2022-12-11 8:12:24，管理交货地点
function manageDeliveryPlace(){
    let detail = $(".receiveAddressCon").html();
    if (detail !== "") {
        detail = JSON.parse(detail);
        let addrList = detail.data;
        let addrListStr = ``;
        if(detail === null){
            $(".placeList").html(addrListStr);
        }else{
            if (addrList && addrList.length > 0) {
                for (var i = 0; i < addrList.length; i++) {
                    addrListStr +=
                        ` <li>
                                   <i class="fa fa-square-o"></i>
                                   <span>${addrList[i].address}</span>
                                   <span class="hd">${JSON.stringify(addrList[i])}</span>
                               </li>`;
                }
            }
            $(".placeList").html(addrListStr);
        }
    }
    bounce_Fixed2.show($("#chooseDelivery"))
}

// creator: 李玉婷，2022-12-11 8:12:24，管理交货地点确定
function chooseDeliveryOk(){
    bounce_Fixed2.cancel()
    let address = `<option value=""></option>`;
    $(".placeList .fa-check-square-o").each(function (){
        let info = JSON.parse($(this).siblings(".hd").html())
        address += `<option value="${info.id}">${info.address}</option>`;
    })
    $(".receiveAddress2").html(address)
}
// creator: 李玉婷，2022-12-14 8:12:24，专属商品自选收货地点
function receiveAddress2(obj){
    let type = $("#newGood").data("ttl")
    if (type === '2' || type === 2) {
        let orderAddress = $(".receiveAddress1").val();
        let val = obj.val();
        if (val !== "" && orderAddress !== val) {
            bounce_Fixed2.show($("#changeAddressTip"));
        } else {
            // let able = $("#newGood").find(".price").data("able");
            // let val3 = $("#newGood").find(".price").data("val");
            // $("#newGood").find(".price").val(val3);
            $("#newGood .modifyPrice").show();
            if ($(".invoice1").is(":visible")){
                // let val1 = $("#newGood").find(".rate").data("val");
                // let val2 = $("#newGood").find(".noPrice").data("val");
                // $("#newGood").find(".rate").val(val1);
                // $("#newGood").find(".noPrice").val(val2);
                $("#newGood").find(".rate,.noPrice,.price").removeAttr("placeholder");
            } else {
                $("#newGood").find(".price").removeAttr("placeholder");
            }
        }
    }
}
// creator: 李玉婷，李玉婷，2022-12-14 16:26:52，该商品在该交货地点的价格需重新录入！
function changeCancel(){
    $(".receiveAddress2").val("");
    bounce_Fixed2.cancel()
}
// creator: 李玉婷，李玉婷，2022-12-14 16:26:52，该商品在该交货地点的价格需重新录入！
function changeAddressNext(){
    let outerName = $("#newGood .outerName").val();
    if(outerName.length === 0){
        layer.msg("请先选择商品")
        return false
    }
    let data = JSON.parse($(".invoiceTip").html());
    bounce_Fixed2.cancel()
    $("#newGood .modifyPrice").hide();
    if ($(".invoice1").is(":visible")){
        $("#newGood").find(".rate,.noPrice,.price").removeAttr("disabled");
        $("#newGood").find(".noPrice,.price").attr("placeholder", `请录入价格，参考单价${handleNull(data.unitPriceReference)}元`);
    } else {
        $("#newGood").find(".price").removeAttr("disabled").attr("placeholder", `请录入价格，参考单价${handleNull(data.unitPriceReference)}元`);
    }
}
// creator: 李玉婷，2019-09-16 16:26:52，定时器--新增验证
function setTimer (timer) {
    switch (timer) {
        case 'updateReceive':
            bounce_Fixed3.everyTime('0.5s','updateReceive',function () {
                var  filledNum = 0;
                $("#newReceiveInfo input").each(function(){
                    if ($(this).val() == '') filledNum++;

                });
                if(filledNum === 0 ){
                    $("#addReceiveOk").prop("disabled",false);
                }else{
                    $("#addReceiveOk").prop("disabled",true);
                }
            });
            break;
    }
}

laydate.render({elem: '#OrderReceivedDate'});
getHostTime (setDateOfArrival)
function setDateOfArrival(time){
    laydate.render({
        elem: '#DateOfArrival',
        // elem: '#OrderReceivedDate',
        min : time.format('yyyy-MM-dd hh:mm:ss'),
        ready: function() {
            var elem = $(".layui-laydate-content");//获取table对象
            elem.find('td').each(function () {
                //遍历td
                var tdTemp = $(this);
                if (tdTemp.hasClass('laydate-disabled')) {
                    tdTemp.click(function () {
                        let clickDate = tdTemp.attr('lay-ymd')
                        let curTime = new Date(time).format('yyyy-M-dd')
                        let clickDateTamp = new Date(clickDate).getTime()
                        let curTimeTamp = new Date(curTime).getTime()
                        if(clickDateTamp < curTimeTamp){
                            console.log('这是第一处给的提示！！！')
                            layer.msg('此处不可选择以前的日期。确有需要，请去“补发订单”中操作！', {
                                offset:['10px','40%'],
                                // time:99999
                            })
                        }

                    })
                }
            })
        }
    });
}

$("body").on("click",'.laydate-disabled', function () {
    console.log('这是第 2222222 处给的提示！！！')

    layer.msg('此处不可选择以前的日期。确有需要，请去“补发订单”中操作！')
});