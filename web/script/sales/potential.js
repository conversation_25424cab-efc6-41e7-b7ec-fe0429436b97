var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#newContectInfo"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#useDefinedLabel"));
bounce_Fixed3.cancel();
$(function() {
    initData();
    initCardUpload($(".cardUploadBtn"));
    if (sphdSocket.user.oid === 0) {
        $(".isSpecial").show()
    } else {
        $(".isSpecial").hide()
    }
    $("#picShow").on("click", '', function () {
        $("#picShow").fadeOut("fast");
    })
    $("#plCustomerInput").on('click', '.node', function () {
        var name = $(this).data('name');
        var type = $(this).data('type');
        switch (name) {
            case 'contactInfo':
                var id = $(this).parents("tr").data('id');
                $("#newContectInfo").data('type', type);
                $("#newContectInfo").data('id', id);
                if (type == 'delete') {
                    var contactData = $("#plCustomerInput").data('contactInfo');
                    contactData = JSON.parse(contactData);
                    var index = contactData.findIndex(value => Number(value.id) === id);
                    contactData.splice(index, 1);
                    var html = setContactList(contactData);
                    $(".contectList").html(html);
                } else {
                    $("#uploadCard").html("")
                    initCardUpload($("#uploadCard"))
                    document.getElementById('newContectData').reset();
                    $(".otherContact").html("");
                    $("#contactSource").hide();
                    $("#contactsCard .bussnessCard").remove();
                    if (type == 'new') {
                        $("#newContectInfo").data('source', '');
                        $('#uploadCard').show();
                    } else if (type == 'update') {
                        var getData = $("#plCustomerInput").data('contactInfo');
                        getData = JSON.parse(getData);
                        var flag = getData.find(value => Number(value.id) === Number(id));
                        setContact(flag);
                    }
                    $("#addMoreContact").hide();
                    bounce_Fixed2.show($("#newContectInfo"));
                    setTimer('newContact');
                }
                break;
            case 'interviewInfo':
                if (type == 'delete') {
                    $(this).parents("tr").remove();
                } else {
                    clearInterviewCon();
                    $("#editType").val(type);
                    if (type == 'new') {
                        $(".interviewTtl").html('新增访谈记录');
                    } else if (type == 'update') {
                        let data = JSON.parse($(this).siblings(".hd").html());
                        $("#newInterviewInfo").data('obj', $(this));
                        $(".interviewTtl").html('修改访谈记录');
                        $("#editType").val("update");
                        $("#addInterview [data-name='id']").val(data['id']);
                        let interviewer = data.interviewer;// 访谈对象
                        let tsIds = data.tsIds;// 同行者同事
                        let ftxIds = data.ftxIds;// 同行者非同事
                        let name = [], fellow=[], unFellow=[], html = ``, talkStr = ``;
                        let cgContent = handleNull(data.cgContent) !== ""?data.cgContent.split("$a$") : [];
                        let talks =  handleNull(data.thydJson) !== ""?JSON.parse(data.thydJson) : [];// 谈话要点
                        if (interviewer !== "") {
                            interviewer = JSON.parse(interviewer);
                            interviewer.forEach(function(item) {
                                name.push(item.name || '--');
                            })
                            $("#addInterview #interviewer").html("共" + interviewer.length + "人：" + name.join('、')).siblings(".hd").html(data.interviewer);
                        }
                        if (tsIds !== "") {
                            tsIds = JSON.parse(tsIds);
                            tsIds.forEach(function(item) {
                                fellow.push(item.userName)
                            })
                            $("#addInterview #fellowTravelers").html("共" + fellow.length + "人：" + fellow.join('、')).siblings(".hd").html(data.tsIds);
                        }
                        if (ftxIds !== "") {
                            ftxIds = JSON.parse(ftxIds);
                            ftxIds.forEach(function(item) {
                                unFellow.push(item.name)
                            })
                            $("#addInterview #fellowUnColleague").html("共" + ftxIds.length + "人：" + unFellow.join('、')).siblings(".hd").html(data.ftxIds);
                        }
                        for(let i =0; i < cgContent.length; i++) {
                            html += setGainStr(cgContent[i]);
                        }
                        for(let i =0; i < talks.length; i++) {
                            talkStr += setTalksStr(talks[i]);
                        }
                        handleNull(data.mbContent) !== ""? $("#addInterview .purposeCon").show(): ""
                        $("#addInterview .purposeCon").html(data.mbContent);
                        $("#addInterview .gains").after(html);
                        $(".interviewerContent").after(talkStr);
                        $("#newInterviewInfo #interviewDate").val(data['interviewDate']);
                    }
                    bounce.show($("#newInterviewInfo"));
                    setTimer('newInterview');
                }
                break;
        }
    });
    $(".bounce").on('click', '.ty-btn', function () {
        var name = $(this).data('name');
        switch (name) {
            case 'addInterview':
                let data = {
                    "createName": sphdSocket.user.userName,
                    "customerContact": Math.random() + '',
                    "interviewDate": $("#interviewDate").val()
                };
                // 谈话要点
                let talkingPoints = [], cgContent = ``;
                let ftxIds = $("#fellowUnColleague").siblings(".hd").html();// 同行者非同事
                $(".interviewerContentItem:visible").each(function () {
                    let info = JSON.parse($(this).find(".hd").html());
                    talkingPoints.push(info);
                })
                // 成果/结论
                $(".gainsItem:visible").each(function () {
                    let val = $(this).find(".gainsCon").html();
                    if (val !== "") {
                        cgContent += val + '$a$';
                    }
                })
                if (cgContent !== "") {
                    cgContent = cgContent.slice(0, cgContent.length-3);
                }
                data['cgContent']= cgContent;
                data['thydJson']= JSON.stringify(talkingPoints);
                data['interviewer']= $("#interviewer").siblings(".hd").html();// 访谈对象
                data['tsIds']= $("#fellowTravelers").siblings(".hd").html();// 同行者同事
                data['mbContent']= $("#addInterview .purposeCon").html();// 目标内容
                data['ftxIds']= ftxIds;// 录入de同事外的人
                data['ftsListJson']= ftxIds;// 录入de同事外的人
                let interviewData = [data];
                setViewList(interviewData);
                bounce.cancel();
                break;
        }
    })
    $(".bounce_Fixed2").on('click', '.ty-btn', function () {
        var name = $(this).data('name');
        switch (name) {
            case 'addContact':
                var type = $("#newContectInfo").data('type');
                var contactInfo = $("#plCustomerInput").data('contactInfo');
                contactInfo = JSON.parse(contactInfo);
                var data = {
                    'name': $("#newContectInfo #contactName").val(),
                    'post': $("#newContectInfo #position").val(),
                    'mobile': $("#newContectInfo #contactNumber").val(),
                    'visitCard': '',
                    'socialList': []
                }
                if ($("#contactsCard .bussnessCard").length > 0) {
                    data.visitCard = $("#contactsCard .bussnessCard .filePic").data('path');
                }
                if ($(".otherContact li").length > 0) {
                    var arr = []
                    $(".otherContact li").each(function () {
                        if ($(this).find("input").val() !== '') {
                            var json = {
                                'code': $(this).find("input").val(),
                                'type': $(this).find("input").data('type'),
                                'name': $(this).find("input").data('name')
                            };
                            arr.push(json);
                        }
                    })
                    data.socialList = arr;
                }
                if (type == 'new') {
                    var groupUuid = $("#newContectInfo").data("groupUuid")
                    var source = $("#newContectInfo").data('source');
                    data.id = Math.random() + '';
                    data.tags = '其他';
                    data.groupUuid = groupUuid
                    contactInfo.push(data);
                    if (source == 'addInterviewer') {
                        data.tags = '访谈记录';
                        var data1 = {
                            'id': data.id,
                            'tags': '访谈记录',
                            'name': data.name,
                            'post': data.post,
                            'mobile': data.mobile,
                            'interviewer': data.name,
                            'interviewerID': data.id,
                            'customerContact': Math.random() + '',
                            'visitCard': data.visitCard,
                            'socialList': data.socialList
                        }
                        data1.groupUuid = groupUuid
                        let html = `<li>
                                     <i class="fa fa-square-o"></i>
                                     <span>${data1.interviewer}</span>
                                     <span>${data1.mobile}</span>
                                     <span class="hd info">${JSON.stringify(data1)}</span>
                                     <span class="linkBtn ty-right" data-type="contactSocial" onclick="interviewerSee($(this))">查看</span>
                                    </li>`;
                        $("#choosePotContact .cusList").append(html);
                        layer.msg('新增成功')
                    }
                } else if (type == 'update') {
                    var id = $("#newContectInfo").data('id');
                    var index = contactInfo.findIndex(value => Number(value.id) === Number(id));
                    data.id = id;
                    contactInfo[index] = data;
                    // 修改对应视图
                    /*$(".interviewList tbody tr").each(function () {
                        let interviewerIDObj = $(this).find(".interviewerID") ;
                        let interviewerID = interviewerIDObj.data('id');
                        if (interviewerID == id){
                            interviewerIDObj.html(data.name).next().html(data.post)
                        }
                    })*/
                    // 修改数据
                    var interviewList = JSON.parse($("#plCustomerInput").data('interviewInfo'));
                    var index = interviewList.findIndex(value => Number(value.interviewerID) === Number(id));
                    if(index > -1){
                        var updateInterviewItem = interviewList[index]
                        updateInterviewItem['post'] = data.post ;
                        updateInterviewItem['mobile'] = data.mobile ;
                        updateInterviewItem['interviewer'] = data.name ;
                        updateInterviewItem['visitCard'] = data.visitCard ;
                        updateInterviewItem['socialList'] = data.socialList ;
                        //$("#plCustomerInput").data('interviewInfo', JSON.stringify(interviewList));
                    }
                }
                var html = setContactList(contactInfo);
                $(".contectList").html(html);
                bounce_Fixed2.cancel();
                break;
        }
    })
    $(".viewsList").on("click", '.fa', function () {
        if($(this).hasClass("fa-check-square-o")){
            $(this).attr("class","fa fa-square-o");
        }else{
            $(this).attr("class","fa fa-check-square-o")
        }
    });
    $("body").on("click",".linkBtn,.redLinkBtn", function(){
        let funCode = $(this).data("fun")
        switch (funCode) {
            default:
                if(funCode){
                    window[funCode]($(this), funCode)
                }
        }
    })
});
// creator: 李玉婷，2019-09-06 15:10:07，初始化upload
function initData(){
    initIndexData();
    initUpload($("#panoramaBtn"));
    initUpload($("#productPicsBtn"));
}
// creator: 李玉婷，2019-09-28 08:46:04，页面初始化
function initIndexData(){
    $("#plCustomerInput").data('contactInfo', '[]');
    $("#plCustomerInput").data('interviewInfo', '[]');
    $(".interviewList").html('');
    $(".contectList").html('');
    $("#qImages .imgsthumb").remove();
    $("#pImages .imgsthumb").remove();
    document.getElementById('plCustomerInput').reset();
    $('body').everyTime('0.5s','addAccount',function () {
        var customerName = $.trim($("#plCustomerInput #add_cusname").val());
        var contactLength = $("#plCustomerInput .contectList tbody tr").length;
        var interviewLength = $("#plCustomerInput .interviewList tbody tr").length;
        if(contactLength >= 50){
            $("#plCustomerInput #newContact").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
        }else{
            $("#plCustomerInput #newContact").data("name","contactInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
        if(interviewLength >= 100){
            $("#plCustomerInput #newInterview").data("name","").removeClass("ty-btn-green").addClass("ty-btn-gray");
        }else{
            $("#plCustomerInput #newInterview").data("name","interviewInfo").removeClass("ty-btn-gray").addClass("ty-btn-green");
        }
        if(customerName !== ""){
            $("#addAccountBtn").prop("disabled",false);
        }else{
            $("#addAccountBtn").prop("disabled",true);
        }
    });
}
// creator: 李玉婷，2019-09-16 16:26:52，定时器--新增验证
function setTimer (timer) {
    switch (timer) {
        case 'newContact':
            bounce_Fixed.everyTime('0.5s', 'newContact',function () {
                var name = $("#contactName").val();
                var post = $("#position").val();
                var mobil = $("#contactNumber").val();
                if (name == '' && post == '' && mobil == '') {
                    $("#addContact").prop('disabled', true);
                } else {
                    $("#addContact").prop('disabled', false);
                }
                $("#addMoreContact option").prop('disabled', false);
                if ($(".otherContact li").length > 0) {
                    $(".otherContact li").each(function () {
                        var val = $(this).find("input").val();
                        var type = $(this).find("input").data('type');
                        if (val == '') {
                            $("#addMoreContact option").eq(type).prop('disabled', true);
                        }
                    })
                }
            })
            break;
        case 'newInterview':
            bounce.everyTime('0.5s', 'newInterview',function () {
                var state = 0;
                if ($("#interviewer").html() != "" && $("#interviewer").html() != "尚未选择") {
                    state ++;
                }

                if (state != 0) {
                    $("#newInterviewSure").prop('disabled', false);
                } else {
                    $("#newInterviewSure").prop('disabled', true);
                }
            })
            break;
    }
}
// creator: 李玉婷，2019-09-04 10:20:07，初始化上传图片
function initUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"上传",
        formData:{
            module: '潜在客户',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            //name = file.name,           //文件名称
            obj.parent().attr("groupUuid", data.groupUuid )
            var imgStr =
                '<div class="imgsthumb">' +
                '    <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '    <span fileUid="' + data.fileUid + '" onclick="cancleThis($(this))">删除</span> ' +
                '    <a path="'+ path +'" onclick="imgViewer($(this))">预览</a>' +
                '</div>';
            obj.before(imgStr);
        }
    });
}
// creator: 李玉婷，2019-09-17 15:09:14，名片上传
function initCardUpload(obj){
    var groupUuid = sphdSocket.uuid();
    obj.Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;',
        multi:true,
        buttonText:"点击此处上传名片",
        formData:{
            module: '潜在客户',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:99999999,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            //name = file.name,           //文件名称
            var imgStr =
                '<div class="bussnessCard">' +
                '	<div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path + ')"></div>' +
                '   <span class="ty-color-blue" fileUid="' + data.fileUid + '" onclick="cancleCard($(this))">删除</span> ' +
                '</div>';
            if ($("#newContectInfo").is(":visible")) {
                $("#newContectInfo").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
                $("#newContectInfo").data("groupUuid", data.groupUuid)
                $('#uploadCard').hide();
                $('#uploadCard').before(imgStr);
            } else if ($("#addOtherTogether").is(":visible")) {
                $('#addOtherTogether .businessCard').html(imgStr);
            }
        }
    });
}
// creator: 李玉婷，2019-09-07 10:16:22，输出联系人列表
function setContactList(list) {
    var html = '', rhtml = '',slice = 0;
    if(list && list.length > 0){
        html =
            '<div class="leftList"> <table class="ty-table ty-table-control">' +
            '<thead> ' +
            '<tr>' +
            '    <td>姓名</td>' +
            '    <td>职位</td>' +
            '    <td>操作</td>' +
            '</tr>' +
            '</thead><tbody>';
        if(list.length >= 2) rhtml = html;
        for (var i=0;i < list.length;i++) {
            slice = i %2 ;
            if(slice > 0) {
                rhtml +=
                    '<tr data-id="'+ list[i].id +'" data-uuid="'+list[i].groupUuid+'">' +
                    '    <td>' + list[i].name + '</td>' +
                    '    <td>' + list[i].post + '</td>' +
                    '    <td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer">修改</span><span class="ty-color-red node" data-type="delete" data-name="contactInfo">删除</span></td>' +
                    '</tr>';
            }else{
                html +=
                    '<tr data-id="'+ list[i].id +'" data-uuid="'+list[i].groupUuid+'">' +
                    '    <td>' + list[i].name + '</td>' +
                    '    <td>' + list[i].post + '</td>' +
                    '    <td><span class="ty-color-blue node" data-type="update" data-name="contactInfo" data-source="addCustomer">修改</span><span class="ty-color-red node" data-type="delete" data-name="contactInfo">删除</span></td>' +
                    '</tr>';
            }
        }
        html += '</tbody></table></div>';
        if(list.length >= 2) rhtml += '</tbody></table></div>';
    }
    var str = html + rhtml;
    var tt = JSON.stringify(list);
    $("#plCustomerInput").data('contactInfo', tt);
    return str;
}
// creator: 李玉婷，2019-09-09 17:19:22，修改联系人-相同输出部分字符串拼接
function setContact(data) {
    var socialList = data.socialList;
    $("#contactName").val(data.name).data('org',data.name);
    $("#position").val(data.post).data('org',data.post);
    $("#contactNumber").val(data.mobile).data('org',data.mobile);
    $("#contactsCard").data('org',data.visitCard);
    if (data.visitCard != '' && data.visitCard != 'undefined'){
        var path = data.visitCard;
        var imgStr =
            '<div class="bussnessCard">' +
            '	<div class="filePic" data-path="' + data.visitCard + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
            '   <span class="ty-color-blue" onclick="cancleCard($(this))">删除</span> ' +
            '</div>';
        $("#uploadCard").hide();
        $("#uploadCard").before(imgStr);
    }else{
        $("#uploadCard").show();
    }
    if(socialList.length > 0){
        var html = '';
        for(var r =0; r < socialList.length; r++){
            html +=
                '<li>' +
                '<span class="sale_ttl1">'+ socialList[r].name +'：</span>' +
                '<span class="gap"><input type="text" value="'+ socialList[r].code +'" placeholder="请录入" data-type="'+ socialList[r].type +'" data-name="'+ socialList[r].name +'" data-org="'+ socialList[r].code +'" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
        }
        $(".otherContact:visible").html(html);
    }
}
// creator: 李玉婷，2019-09-04 18:55:15，添加
function addMore (obj){
    obj.next("select").show();
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContact li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li>' +
                '<span class="sale_ttl1">手机：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="1" data-name="手机" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '2':
            html +=
                '<li>' +
                '<span class="sale_ttl1">QQ：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="2" data-name="QQ" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '3':
            html +=
                '<li>' +
                '<span class="sale_ttl1">Email：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="3" data-name="Email" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '4':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微信：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="4" data-name="微信" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '5':
            html +=
                '<li>' +
                '<span class="sale_ttl1">微博：</span>' +
                '<span class="gap"><input type="text" placeholder="请录入" data-type="5" data-name="微博" data-org="" require/></span>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $(".otherContact").append(html);
            break;
        case '9':
            $("#useDefinedLabel").data("source", 1);
            $("#useDefinedLabel input").val("");
            bounce_Fixed2.show($("#useDefinedLabel"));
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-28 09:36:04，输出访谈记录列表
function setViewList(list) {
    let editType = $("#editType").val();
    var html = '';
    let headStr = ``, tail = ``;
    if (list && list.length > 0) {
        for (var i = 0; i < list.length; i++) {
            html +=
                `<tr data-id="${list[i].customerContact}">
                     <td title="${$("#interviewer").html()}">${$("#interviewer").html() === "尚未选择"? '共0人':$("#interviewer").html()}</td>
                     <td>${list[i].interviewDate}</td>
                     <td>${list[i].createName} &nbsp;&nbsp;-- </td>
                     <td><span class="ty-color-blue node" data-type="update" data-name="interviewInfo">修改</span><span class="ty-color-red node" data-type="delete" data-name="interviewInfo">删除</span>
                     <span class="hd">${JSON.stringify(list[i])}</span>
                     </td>
                 </tr>`;
        }
    }
    if (editType === 'update') {
        let obj = $("#newInterviewInfo").data('obj');
        obj.parents("tr").replaceWith(html);
    } else {
        if ($(".interviewList table").length > 0) {
            $("#plCustomerInput .interviewList tbody").append(html);
        } else {
            headStr =
                '<table class="ty-table ty-table-control">' +
                '<thead> ' +
                '<tr>' +
                '    <td width="30%">访谈对象</td>' +
                '    <td width="20%">访谈日期</td>' +
                '    <td width="30%">录入者</td>' +
                '    <td width="20%">操作</td>' +
                '</tr>' +
                '</thead><tbody>';
            tail = '</tbody></table>';
            $("#plCustomerInput .interviewList").html(headStr + html + tail);
        }
    }
}
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.siblings(".textMax").text( curLength +'/' + max );
}
// creator: 李玉婷，2019-09-19 09:37:24，清除自定义标签
function clearLableText(obj) {
    obj.siblings().val("");
}
// creator: 李玉婷，2019-09-19 10:14:23，自定义标签确定
function addNewLable(){
    let source = $("#useDefinedLabel").data("source");
    var val = $("#defLable").val();
    var html = ``;
    if (source === 1) {//新增联系人、新增访谈对象
        html =
            '<li>' +
            '<span class="sale_ttl1">' + val + '：</span>' +
            '<span class="gap"><input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/></span>'+
            '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
            '</li>';
    } else {//录入同事外的人
        html =
            '<li>' +
            '<p>' + val + '</p>' +
            '<input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" data-org="" require/>'+
            '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
            '</li>';
    }
    $(".otherContact").append(html);
    bounce_Fixed3.cancel();
}
// creator: 李玉婷，2019-09-07 08:50:59，删除添加的联系方式
function removeAdd(obj) {
    obj.parent("li").remove();
}
// creator: 李玉婷，2019-09-03 15:43:51，图片预览
function imgViewer(obj) {
    var src = obj.attr('path');
    $("#picShow img").attr('src', $.fileUrl + src);
    $("#picShow").fadeIn("fast");
}
// creator: 李玉婷，2019-09-04 19:31:34，删除名片
function cancleCard(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent().remove();
    $('#uploadCard').show();
}
// creator: 李玉婷，2019-08-21 14:46:56，图片删除
function cancleThis(obj) {
    if (obj.attr('fileUid')) {
        var option = {type: 'fileUid', fileUid: obj.attr('fileUid')}
        fileDelAjax(option)
    }
    obj.parent(".imgsthumb").remove();
}
// creator: 李玉婷，2019-10-13 11:05:05，提交潜在客户
function cunstomerInputSure(){
    var imgsQ = [], imgsP = [] ;
    var data = {
        qImages: '',
        pImages: '',
        contactsList: '',
        interviewList: '',
    };
    $('#plCustomerInput input[type="text"]:visible').each(function(){
        var name = $(this).data('name');
        data[name] = $(this).val();
    })
    $("#qImages .imgsthumb").each(function () {
        var path = {
            'normal': $(this).find(".filePic").data('path')
        };
        imgsQ.push(path);
    })
    $("#pImages .imgsthumb").each(function () {
        var path = {
            'normal': $(this).find(".filePic").data('path')
        };
        imgsP.push(path);
    })
    imgsQ = JSON.stringify(imgsQ);
    imgsP = JSON.stringify(imgsP);
    data.qImages = imgsQ;
    data.pImages = imgsP;
    if($("#firstContactTime").val() != ''){
        data.firstContactTime = $("#firstContactTime").val();
    }
    data.contactsList = $("#plCustomerInput").data('contactInfo');
    var list = [{type: 'groupUuid', groupUuid: $("#pImages").attr("groupUuid")}, {type: 'groupUuid', groupUuid: $("#qImages").attr("groupUuid")}]
    for (var i in data.contactsList) {
        list.push({type: 'groupUuid', groupUuid: data.contactsList[i].groupUuid})
    }
    let interviewList = [];
    $("#plCustomerInput .interviewList tbody tr").each(function () {
        let info = JSON.parse($(this).find(".hd").html());
        let arr1=[],arr2=[],arr3=[],arr4=[];
        let interviewer = info.interviewer;// 访谈对象
        let tsIds = info.tsIds;// 同行者同事
        let ftxIds = info.ftxIds;// 同行者非同事
        let talks = info.thydJson;
        delete info.thydJson;
        if (interviewer !== "") {
            interviewer = JSON.parse(interviewer);
            interviewer.forEach(function(item) {
                arr1.push(item.id)
            })
        }
        if (tsIds !== "") {
            tsIds = JSON.parse(tsIds);
            tsIds.forEach(function(item) {
                arr2.push(item.userID)
            })
        }
        if (ftxIds !== "") {
            ftxIds = JSON.parse(ftxIds);
            ftxIds.forEach(function(item) {
                arr3.push(item.id)
            })
        }
        if (talks !== "") {
            talks = JSON.parse(talks);
            talks.forEach(function(item, index) {
                let talk = {
                    "type": item.type,//"参与者类型:1-访谈对象，2-同行者(同事)，3-同行者（非同事）"
                    "content": item.content,
                    "orders": index+1
                }
                if (item.type === 2) {
                    talk.participator = item.userID;//参与者id
                    talk.participatorName = item.userName;//参与者名称
                } else {
                    talk.participator = item.id;//参与者id
                    talk.participatorName = item.name;
                }
                arr4.push(talk);
            })
        }
        info.interviewer = arr1.toString()
        info.tsIds = arr2.toString()
        info.ftxIds = arr3.toString()
        info.thydJson = JSON.stringify(arr4)
        var date = info.interviewDate.replace('年', "-");
        date = date.replace('月', "-");
        date = date.replace('日', "");
        info.interviewDate = date;
        interviewList.push(info);
    })
    data.interviewList = JSON.stringify(interviewList);
    $.ajax({
        url: "../sale/addPotentialCustomer.do",
        data: data,
        success: function (data) {
            var status = data["status"];
            if (status == 0 || status == "0") {
                bounce.show($("#mtTip"));
                $("#mt_tip_ms").html("该客户名称已存在!");
                return false;
            }else if (status == 1) {
                cancelFileDel(list)
                layer.msg("新增成功");
                cancelFileDel("all")
                initIndexData();
                bounce.cancel();
            }
        }
    })
}
// creator: 李玉婷，2020-12-21 15:30:15，新增访谈记录- 新增访谈对象
function addInterviewer() {
    $("#uploadCard").html("")
    initCardUpload($("#uploadCard"))
    $("#newContectInfo").data('type', 'new');
    $("#newContectInfo").data('source', 'addInterviewer');
    document.getElementById('newContectData').reset();
    $(".otherContact").html("");
    $("#contactsCard .bussnessCard").remove();
    $('#uploadCard').show();
    $("#contactSource span").html("");
    $("#contactSource").show();
    $("#addMoreContact").hide();
    bounce_Fixed2.show($("#newContectInfo"));
    setTimer('newContact');
}
// creator: 李玉婷，2020-12-22 10:10:26，访谈对象选择人列表赋值
function choosePotContact() {
    let list = $("#plCustomerInput").data('contactInfo'), str="";
    list = JSON.parse(list);
    let selected = $("#interviewer").siblings(".hd").html();
    let selectedList = [];
    if (selected !== "") {selectedList = JSON.parse(selected)}
    bounce_Fixed.show( $("#choosePotContact"));
    for(let i=0; i < list.length; i++){
        let item = list[i];
        let faClass = `fa-square-o`;
        let icon = selectedList.findIndex(value => Number(value.id) === Number(item.id));
        icon > -1 ? faClass = `fa-check-square-o`: "";
        str += `<li>
                    <i class="fa ${faClass}"></i>
                    <span>${item.name}</span>
                    <span>${item.mobile}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                    <span class="linkBtn ty-right" data-type="contactSocial" data-id="${item.id}" onclick="interviewerSee($(this))">查看</span>
              </li>`;
    }
    $("#choosePotContact .cusList").html(str);
}
// creator: 李玉婷，2020-12-22 10:59:01，访谈对象查看
function interviewerSee(obj) {
    var get =  obj.siblings('.hd').html();
    get = JSON.parse(get);
    var html = '',socialList = get['socialList'];
    $("#see_contactName").html(get.name);
    $("#see_position").html(get.post);
    //$("#see_contactTag").html(get.tags);
    $(".see_otherContact").html("");
    $("#contactSeeDetail .see_createName").html(get.createName);
    $("#contactSeeDetail .see_createDate").html(new Date(get.createDate).format('yyyy-MM-dd hh:mm:ss'));
    let strArr = ['', '', '', '', '', '', '', '', '', ''], allStr = '';
    strArr[1] = `<li><span>手机</span><span>${get.mobile}</span></li>`;
    if(socialList.length > 0){
        var mobile = '',qq = '',email = '',weixin = '',weibo = '', defined = '';
        let tag = ['', '手机', 'QQ', 'Email', '微信', '微博', '', '', '', '自定义'];
        for(var r in socialList){
            let item = socialList[r];
            let _index = Number(item.type);
            strArr[_index] += `<li><span>${tag[_index]}</span><span>${item.code}</span></li>`;
        }
    }
    allStr = strArr[1] + strArr[2] + strArr[3] + strArr[4] + strArr[5] + strArr[9] ;
    $(".see_otherContact").html(allStr);
    bounce_Fixed3.show($("#contactSeeDetail"));
}
// creator: 李玉婷，2020-12-22 15:08:07，确定选择访谈对象
function choosePotContactOk() {
    let selectObj = $("#choosePotContact").find(".fa-check-square-o");
    if(selectObj.length > 0){
        let arr = [], name = [];
        selectObj.each(function () {
            let info = $(this).siblings(".hd").html();
            let infoJson = JSON.parse(info);
            infoJson.type = 1;
            arr.push(infoJson);
            name.push(infoJson.name || '--');
        })
        name = name.join('、');
        $("#interviewer").html("共" + selectObj.length + "人：" + name)
            .siblings(".hd").html(JSON.stringify(arr));
        delTalkPoints();
        bounce_Fixed.cancel();
    }else layer.msg('请先选择人员')
}
/*creator:lyt 2023/4/7 0007 下午 2:51 用户删除访谈对象，删除者如有“谈话要点”一并删除*/
function delTalkPoints() {
    let list = [];
    let arr1 = $("#interviewer").siblings(".hd").html();
    let arr2 = $("#fellowTravelers").siblings(".hd").html();
    if (arr1 !== "") {arr1 = JSON.parse(arr1);}
    if (arr2 !== "") {arr2 = JSON.parse(arr2);}
    list = [...arr1,...arr2];
    $(".interviewerContentItem").each(function () {
        let uid = $(this).data("uid");
        let type = $(this).data("type");
        let index = ``;
        if (type === 1) {
            index = list.findIndex(value => Number(value.id) === uid)
        } else {
            index = list.findIndex(value => Number(value.userID) === uid)
        }
        if (index === -1) {
            $(this).remove();
        }
    })
}
// create : lyt 2023-1-22 清空访谈记录
function clearInterviewCon() {
    $(".textMax").html("0/200");
    $("#addInterview .gainsItem").remove();
    $("#addInterview .purposeCon").html("").hide();
    $("#addInterview .interviewerContentItem").remove();
    $("#addInterview #interviewDate").val("");
    $("#addInterview #interviewer").html("尚未选择").siblings(".hd").html("");
    $("#addInterview #fellowTravelers").html("尚未选择").siblings(".hd").html("");
    $("#addInterview #fellowUnColleague").html("尚未选择").siblings(".hd").html("");
}
//creator:lyt 2022/11/25 0025 下午 7:27 同行者（同事）选择
function fellowTravelers(obj){
    let target = obj.data("target");
    $("#chooseFellow .target").val(target)
    bounce_Fixed.show($("#chooseFellow"));
    let selected = $("#fellowTravelers").siblings(".hd").html();
    let selectedList = [];
    if (selected !== "") {selectedList = JSON.parse(selected)}
    $.ajax({
        "url":"../org/getPresentUsers.do",
        "data":{},
        success:function (res) {
            if(res.success !== 1){
                layer.msg('未获取数据');
                return false
            }
            let list = res['data'] || [], str="";
            for(let i in list){
                let item = list[i];
                item.type = 2
                let faClass = `fa-square-o`;
                let icon = selectedList.findIndex(value => Number(value.userID) === Number(item.userID));
                icon > -1 ? faClass = `fa-check-square-o`: "";
                str += `<li>
                    <i class="fa ${faClass}"></i>
                    <span>${item.userName}</span>
                    <span>${item.mobile}</span>
                    <span class="hd info">${JSON.stringify(item)}</span>
                </li>`;
            }
            $("#chooseFellow .fellowList").html(str);
        }
    });
}
function chooseFellowOk(){
    let selectObj = $("#chooseFellow").find(".fa-check-square-o");
    if(selectObj.length > 0){
        let arr = [], name = [];
        selectObj.each(function () {
            let info = $(this).siblings("span.hd").html();
            let infoJson = JSON.parse(info);
            arr.push(infoJson);
            name.push(infoJson.userName);
        })
        name = name.join('、');
        $($("#chooseFellow .target").val()).html("共" + selectObj.length + "人：" + name)
            .siblings(".hd").html(JSON.stringify(arr));
        delTalkPoints();
        bounce_Fixed.cancel();
    }else layer.msg('请先选择人员')
}
//creator:lyt 2022/11/26 0026 下午 9:55 同行者（非同事）选择
function fellowUnColleague(obj){
    let html = ``;
    let target = obj.data("target");
    $("#chooseFellowUn .target").val(target);
    let unFellow = $("#fellowUnColleague").siblings(".hd").html();
    if (unFellow !== "") {
        unFellow = JSON.parse(unFellow);
        unFellow.forEach((item)=> {
            html +=
                `<li>
                 <span>${item.name}</span>
                 <span>${item.mobile}</span>
                 <div class="ty-right">
                    <span class="linkBtn" data-fun="updateOtherTogether">修改</span>
                    <span class="redLinkBtn" data-fun="delOtherTip" data-source="delOtherTip">删除</span>
                    <span class="hd">${JSON.stringify(item)}</span>
                </div>      
             </li>`;
        })
    }
    $("#chooseFellowUn .fellowList").html(html);
    bounce_Fixed.show($("#chooseFellowUn"));
}
function chooseFellowUnOk() {
    let selectObj = $("#chooseFellowUn .fellowList li");
    if(selectObj.length > 0){
        let arr = [], name = [];
        selectObj.each(function () {
            let info = $(this).find("span.hd").html();
            let infoJson = JSON.parse(info);
            arr.push(infoJson);
            name.push(infoJson.name);
        })
        name = name.join('、');
        $("#fellowUnColleague").html("共" + selectObj.length + "人：" + name)
            .siblings(".hd").html(JSON.stringify(arr));
        bounce_Fixed.cancel();
    }else layer.msg('请先选择人员');
}
//creator:lyt 2022/11/26 0026 下午 10:05 录入同事外的人
function addOtherTogether(){
    $("#addOtherTogether").data("source", "add").find("input").val("");
    $("#addOtherTogether .addMoreContact").hide();
    $("#addOtherTogether .otherContact").html("");
    $("#addOtherTogether .businessCard").html("");
    bounce_Fixed2.show($("#addOtherTogether"));
}
function updateOtherTogether(obj){
    let data = JSON.parse(obj.siblings(".hd").html());
    $("#addOtherTogether").data("obj", obj)
    $("#addOtherTogether").data("source", "update").find("input").val("");
    $("#addOtherTogether .addMoreContact").hide();
    $("#addOtherTogether .otherContact").html("");
    $("#addOtherTogether .businessCard").html("");
    $("#addOtherTogether input").each(function () {
        let name = $(this).attr("name");
        $(this).val(data[name]);
    })
    var socialList = JSON.parse(data.socialList);
    if (data.cardPath && data.cardPath != '' && data.cardPath != 'undefined' && data.cardPath != 'null'){
        var path = data.cardPath;
        var imgStr =
            '	<div class="bussnessCard"><div class="filePic" data-path="' + path + '" style="background-image: url(' + $.fileUrl + path + ')"></div>' +
            '   <span class="ty-color-blue" onclick="cancleCard($(this))">删除</span> </div>';
        $("#addOtherTogether .businessCard").html(imgStr);
    }
    var html = '';
    if(socialList.length > 0){
        for(var r in socialList){
            html +=
                '<li>' +
                '<p>'+ socialList[r].name +'</p>' +
                '<input type="text" value="'+ socialList[r].code +'" placeholder="请录入" data-type="'+ socialList[r].type +'" data-name="'+ socialList[r].name +'" data-org="'+ socialList[r].code +'" require/>'+
                '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
        }
    }
    $("#addOtherTogether .otherContact").html(html).data('length', socialList.length);
    bounce_Fixed2.show($("#addOtherTogether"));
}
function addOtherTogetherSure(){
    var name = $("#otherTogetherName").val();
    if(name === ""){
        layer.msg("还有必填项尚未填写！");
        return false;
    }
    let source = $("#addOtherTogether").data("source");
    var data = {
        'id': Math.random() + '',
        'type': 3,//1-访谈对象，2-同行者(同事)，3-同行者（非同事）"
        'cardPath': '',
        'socialList': '[]'
    };
    $("#addOtherTogether input[require]").each(function () {
        let name = $(this).attr("name");
        data[name] = $(this).val();
    });
    if($("#addOtherTogether .businessCard").length > 0){
        let path = $("#addOtherTogether .businessCard .filePic").data('path');
        data.cardPath = path ;
        var groupUuid = $("#newContectInfo").data("groupUuid")
        data.groupUuid = groupUuid
    }
    if($("#addOtherTogether .otherContact li").length > 0){
        var arr = []
        $("#addOtherTogether .otherContact li").each(function () {
            if ($(this).find('input').val() != '') {
                var json = {
                    'code': $(this).find("input").val(),
                    'type': $(this).find("input").data('type'),
                    'name': $(this).find("input").data('name')
                };
                arr.push(json);
            }
        })
        arr = JSON.stringify(arr);
        data.socialList = arr;
    }
    let html = `<li>
                 <span>${data.name}</span>
                 <span>${data.mobile}</span>
                 <div class="ty-right">
                    <span class="linkBtn gapR" data-fun="updateOtherTogether">修改</span>
                    <span class="redLinkBtn" data-fun="delOtherTip" data-source="delOtherTip">删除</span>
                    <span class="hd">${JSON.stringify(data)}</span>
                </div>      
             </li>`;
    if (source === "add"){
        $("#chooseFellowUn .fellowList").append(html);
    } else {
        let obj = $("#addOtherTogether").data("obj");
        obj.parents("li").replaceWith(html);
    }
    bounce_Fixed2.cancel();
}
function delOtherTip(obj) {
    $("#delTip").data("obj",obj);
    $("#delTip").data("source", obj.data("source"));
    bounce_Fixed3.show($("#delTip"));
}
function delOtherOk(){
    let obj = $("#delTip").data("obj");
    let source = $("#delTip").data("source");
    if (source === 'delOtherTip'){
        obj.parents("li").remove();
    }
    bounce_Fixed3.cancel();
}
// creator: 李玉婷，2019-09-07 08:32:05, 添加更多联系人
function addMoreContact(obj){
    var val = obj.val();
    var html = '', type = ``;
    obj.val('0').hide();
    $(".otherContact:visible li").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    if (val === '9'){
        $("#useDefinedLabel").data("source", 2);
        $("#useDefinedLabel input").val("");
        bounce_Fixed3.show($("#useDefinedLabel"));
        bounce_Fixed3.everyTime('0.5s', 'useDefinedLabel',function () {
            var name = $.trim($("#defLable").val());
            if (name == '' || !name) {
                $("#addNewLableSure").prop('disabled', true);
            } else {
                $("#addNewLableSure").prop('disabled', false);
            }
        })
    } else {
        switch (val) {
            case '1':
                type = '手机';
                break;
            case '2':
                type = 'QQ';
                break;
            case '3':
                type = 'Email';
                break;
            case '4':
                type = '微信';
                break;
            case '5':
                type = '微博';
                break;
            default:break;
        }
        html +=
            '<li>' +
            '<p>'+ type+'</p>' +
            '<input type="text" placeholder="请录入" data-type="'+ val +'" data-name="'+ type+'" require/>'+
            '<span class="ty-color-red" onclick="removeAdd($(this))">删除</span>' +
            '</li>';
        $(".otherContact:visible").append(html);
    }
}
//creator:lyt 2023/1/12 14:14 访谈目标编辑
function contentEntry(obj) {
    let icon = 0;
    let target = obj.data("target");
    let con = ``;
    if (target === 'purpose'){
        con = $("#addInterview .purposeCon").html();
    } else if (target === 'gains') {
        icon = 1;
        let source = obj.data("type");
        $("#storageData").data("obj", obj);
        $("#storageData").data("type", source);
        if (source === 'update') {
            con = obj.parent().siblings(".gainsCon").html();
        }
    }
    $("#contentEntry .target").val(target);
    $("#contentEntry textarea").val(con);
    $("#contentEntry .textMax").html(con.length + "/200");
    $(".con" + icon).show().siblings().hide();
    bounce_Fixed.show($("#contentEntry"));
}
function contentEntryOk() {
    let target = $("#contentEntry .target").val();
    let con = $("#contentEntry textarea").val();
    if (target === 'purpose'){
        $("#addInterview .purposeCon").show().html(con);
    } else if (target === 'gains') {//成果/结论编辑
        let obj = $("#storageData").data("obj");
        let source = $("#storageData").data("type");
        let out = ``;
        if (source === 'update') {
            out = $(".addInterview tr").index(obj.parents("tr"))
        }
        let html = setGainStr(con);
        if (source === 'add') {
            if ($("#addInterview .gainsItem").length > 0){
                $(".gainsItem:last").after(html);
            } else {
                $(".gains").after(html);
            }
        } else if(source === 'update') {
            $(".addInterview tr").eq(out).replaceWith(html);
        }
    }
    bounce_Fixed.cancel();
}
function setGainStr(con) {
    let html =
        `<tr class="gainsItem">
                    <td></td>
                    <td colspan="2">
                        <span class="gainsCon">${con}</span>
                        <div class="ty-right">
                        <span class="redLinkBtn gapR" data-fun="gainDel">删除</span>
                        <span class="linkBtn" data-fun="contentEntry" data-target="gains" data-type="update">编辑</span>
                        </div>
                    </td>
                </tr>`;
    return html;
}
function setTalksStr(infoJson) {
    let html  =
        `<tr class="interviewerContentItem" data-type="${infoJson.type}" data-uid="${infoJson.type === 2? infoJson.userID:infoJson.id}">
                    <td><span class="interviewerName">${infoJson.type === 2? infoJson.userName || '--':infoJson.name || '--'}</span>：</td>
                    <td colspan="2">
                        <span class="hd">${JSON.stringify(infoJson)}</span>
                        <div class="interviewerCon">${infoJson.content}</div>
                        <div class="ty-right">
                        <span class="redLinkBtn gapR" data-fun="gainDel">删除</span>
                        <span class="linkBtn" data-fun="addTalkingPoints" data-type="update">编辑</span>
                        </div>
                    </td>
                </tr>`;
    return html;
}
//creator:lyt 2023/1/12 14:24 访谈目标删除
function purposeDel() {
    $(".entryCon:visible").find("textarea").val("");
    $(".entryCon:visible").find(".textMax").html("0/200");
}
//creator:lyt 2023/1/12 14:24 成果/结论删除
function gainDel(obj) {
    obj.parents("tr").remove();
}
//creator:lyt 2023/1/28 10:04 谈话要点编辑
function addTalkingPoints(obj){
    let type = obj.data("type");
    let list = [], str="";
    let list1 = $("#interviewer").siblings(".hd").html();
    let list2 = $("#fellowTravelers").siblings(".hd").html();
    let list3 = $("#fellowUnColleague").siblings(".hd").html();
    if (list1 !== "") {
        list1 = JSON.parse(list1);
        list = [...list1];
    }
    if (list2 !== "") {
        list2 = JSON.parse(list2);
        list = [...list,...list2];
    }
    if (list3 !== "") {
        list3 = JSON.parse(list3);
        list = [...list,...list3];
    }
    for(let i in list){
        let item = list[i];
        str += `<option value='${JSON.stringify(item)}'>${item.type === 2? item.userName || '--':item.name || '--'}</option>`;
    }
    $("#addTalkingPoints").data("obj", obj);
    $("#addTalkingPoints #participant").html(str);
    bounce_Fixed.show($("#addTalkingPoints"));
    purposeDel();
    if (type === 'update') {
        let info = JSON.parse(obj.parent().siblings(".hd").html());
        let name = info.type === 2? info.userName:info.name
        let con = obj.parent().siblings(".interviewerCon").html();
        $("#participant option:contains("+ name +")").attr("selected",true);
        $(".entryCon:visible").find("textarea").val(con);
        $(".entryCon:visible").find(".textMax").html(con.length + "/200");
    }
}
function addTalkingPointsOk(){
    let info = $("#addTalkingPoints #participant").val();
    let isOk = true;
    let infoJson = JSON.parse(info);
    let val = $(".entryCon:visible").find("textarea").val();
    let obj = $("#addTalkingPoints").data("obj");
    let type = obj.data("type")
    if( info !== ""){
        if (val === "") {
            layer.msg("请录入该发言者的谈话要点");
            return false;
        }
        let curObj = $(".interviewerContentItem");
        let out = ``;
        if (type === 'update') {
            out = $(".addInterview tr").index(obj.parents("tr"))
        }
        curObj.each(function(){
            let idx = $(this).index();
            if (idx !== out) {
                var person = $(this).data("uid");
                if (infoJson.type === 2) {
                    if(infoJson.userID == person){ isOk = false }
                } else {
                    if(infoJson.id == person){ isOk = false }
                }
            }
        })
        if(!isOk){ layer.msg("已有此人，无需再选！"); return false; }
        if (val !== "") {
            infoJson.content = val;
            let html = setTalksStr(infoJson);
            if (type === 'add') {
                if ($("#addInterview .interviewerContentItem").length > 0){
                    $(".interviewerContentItem:last").after(html);
                } else {
                    $(".interviewerContent").after(html);
                }
            } else if (type === 'update'){
                $(".addInterview tr").eq(out).replaceWith(html);
            }
            bounce_Fixed.cancel();
        } else  layer.msg('请录入该发言者的谈话要点');
    }else layer.msg('请先选择人员');
}
// creator: 张旭博，2021-03-17 16:04:30, 重写上传弹窗删除方法
function chargeXhr(selector) {
    var parent = selector.parents(".bounce")
    var parent1 = selector.parents(".bounce_Fixed")
    var parent2 = selector.parents(".bounce_Fixed2")
    var parent3 = selector.parents(".bounce_Fixed3")
    if (parent.length > 0) {
        bounce.cancel()
    } else if (parent1.length > 0) {
        bounce_Fixed.cancel()
    } else if (parent2.length > 0) {
        bounce_Fixed2.cancel()
    } else if (parent3.length > 0) {
        bounce_Fixed3.cancel()
    } else {
        console.log("未定义的状态")
    }
    var option = selector.parents(".bonceContainer").data('groupUuidObj')
    cancelFileDel(option, true)
}
laydate.render({elem: '#interviewDate', format: 'yyyy年MM月dd日'});
laydate.render({elem: '#firstContactTime', format: 'yyyy/MM/dd'});