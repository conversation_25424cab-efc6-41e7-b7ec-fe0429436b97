let detailID = getUrlParam("id");
$(function () {
    getTicketInfo();
    $(".goodsSelect").on('click', ".changeDot",function() {
        $(this).children("i").addClass("fa-dot-circle-o").removeClass("fa-circle-o");
        $(this).siblings(".changeDot").find("i").addClass("fa-circle-o").removeClass("fa-dot-circle-o");
    });
// 填完发票号的回调
    sphdSocket.subscribe('invoiceHandle', handleCallback, null, 'user')
});
// creator: 李玉婷，2022-05-09 08:59:52，开票申请单
function getTicketInfo() {
    $.ajax({
        "url": "../sale/applicationDetail",
        "data": {"applicationId": detailID},
        success: function (res) {
            var items = res["details"];
            var order = res["base"][0];
            if (items && order) {
                var str = "";
                var invoice_require = Number(order.invoice_require);
                var application_state = Number(order.application_state);
                $("#ticketSn").data("invoicerequire", invoice_require)
                $("#ticketSn").data("applicationstate", application_state)
                let invoiceCat =  '不开票'
                if(invoice_require === 1){
                    invoiceCat = '需开增值税专用发票'
                }else if(invoice_require === 2){
                    invoiceCat = '需开其他发票'
                }
                $("#ticketType").html(invoiceCat);

                // 检查开票状态，如果application_state等于5，则禁用确定按钮
                if(application_state === 5) {
                    $(".goodsSelect .ty-btn-blue").addClass("ty-btn-disabled").prop("disabled", true);
                }
                for (var i = 0; i < items.length; i++) {
                    str += "<tr itemid='" + items[i]["id"] + "'>" +
                        "     <td onclick='toogleI($(this))'>" +
                        "       <i class='fa fa-square-o'></i>" +
                        "       <span class='hd'>"+ JSON.stringify(items[i]) +"</span>" +
                        "    </td>" +
                        "    <td>" + handleNull(items[i]["code"]) + "</td>" +
                        "    <td>" + items[i]["name"] + "</td>" +
                        "    <td>" + items[i]["unit"] + "</td>" +
                        "    <td>" + items[i]["amount"] + "</td>" +
                        "    <td>" + items[i]["unit_price_c"] + "</td>" +
                        "    <td>" + items[i]["item_quantity"] + "</td>" +
                        "    <td>" + items[i]["invoice_yet_amount"] + "</td>" +
                        "    <td class='ty-td-gray'></td>" +
                        "</tr>";
                }
                $("#ticketTab tbody").html(str);
                $("#applyDetail").html(JSON.stringify(order));
                $("#ticketCustomerName").html(order["name"]).data("id", order["order_id"]);
                $("#ticketCode").html(order["code"]);
                $("#ticketSn").html(order["sn"]);
                $("#ticketTotal").html(order["invoice_amount"]);
                $("#ticketApplyer").html(order.create_name + ' &nbsp; ' + new Date(order.create_date).format('yyyy-MM-dd hh:mm:ss'));
                $("#ticketApprover").html(order.update_name + ' &nbsp; ' + new Date(order.update_date).format('yyyy-MM-dd hh:mm:ss'));
                $("#enteredInfo").html(`已录入${order.fid_count}张发票，总金额${parseFloat((order.fid_all_amount|| 0).toFixed(2))}元`);
                setTimer1 = setInterval(function () {
                    ticketFun();
                }, 500);
            } else {
                layer.msg("获取数据失败！");
            }
        }
    })
}
// creator: 李玉婷，2022-06-02 16:45:03，获取已录入的发票
function getTempTickets() {
    let orderId = $("#ticketCustomerName").data("id");
    bounce.show($("#ticketEnteredLog"));
    $(".cusName").html($("#ticketCustomerName").html());
    $(".cusNo").html($("#ticketCode").html());
    $(".ticketCat").html($("#ticketType").html());
    $("#ticketEnteredLog table tr:gt(0)").remove();
    $.ajax({
        "url":"../sale/applicationDetails.do",
        "data":{
            "state": 3,
            "orderId": orderId,
            "applicationId": detailID},
        success:function (res) {
            let html = ``;
            let list = res.data || [];
            for(let i =0;i<list.length;i++){
                html +=
                    `
                 <tr>
                     <td>${list[i].invoice_no}</td>
                     <td>${new Date(list[i].operate_date).format('yyyy-MM-dd')}</td>
                     <td>${list[i].invoice_amount}</td>
                     <td>${list[i].fid_line}</td>
                     <td>
                         <span class="ty-color-blue" onclick="getEditInvoiceMessage($(this));">编辑发票信息</span>
                         <span class="ty-color-blue" onclick="delInvoiceMessage($(this), ${list[i].fid});">删除本条</span>
                         <span class="hd">${JSON.stringify(list[i])}</span>
                     </td>
                 </tr>
             `;
            }
            $("#ticketEnteredLog table tbody").append(html);
            $("#ticketEnteredLog .entryedNum").html(list.length);
        }
    })
}
// creator: 李玉婷，2022-05-18 10:07:24，进入下一步：录入该张发票的号码、开票日期与金额。
let goodList = [];
function ticketApplyByFinanceSure(){
    // 检查开票状态，如果application_state等于5，则提示并返回
    var application_state = $("#ticketSn").data("applicationstate");
    if(application_state === 5) {
        layer.msg("开票已完成，无需再次操作");
        return;
    }

    goodList = [];
    if ($(".checkSect .fa-dot-circle-o").length > 0) {
        let type = $(".checkSect .fa-dot-circle-o").data("type");
        if (type === 1) {
            if ($("#ticketTab .fa-check-square-o").length > 0) {
                let data = {}, checkAmount = 0;
                let invoice_require = $("#ticketSn").data("invoicerequire")
                $("#ticketTab .fa-check-square-o").each(function () {
                    data = JSON.parse($(this).siblings(".hd").html());
                    if ($(this).parents("tr").find("input").val() === "") {
                        checkAmount++;
                    } else {
                        let num = $(this).parents("tr").find("input").val()
                        var rate = "";
                        var rateAmount = "";
                        var amount = data.unit_price_c * num;
                        if (invoice_require === 1 || invoice_require === 2) {
                            rate = Number((data["invoice_rate"] && data["invoice_rate"] ) || "0");
                            rateAmount = rate * data.unit_price_c * num / 100;
                        }
                        data.invoice_quantity = num;
                        data.fid_invoice_amount = rateAmount.toFixed(2);
                        data.fid_amount = amount.toFixed(2);
                        goodList.push(data);
                    }
                });
                if (checkAmount > 0) {
                    layer.msg("操作失败，请根据发票选择商品并录入数量！");
                } else {
                    $("#tickets").val("");
                    $("#entryInvoiceMessage input").val("");
                    let option = `<option value="">请选择</option>`
                    $("#tickets").html(option);
                    getInvoices(invoice_require, $("#tickets"));
                    setEditInvoiceMessage(1);
                }
            }
        } else {
            let isAllOk = true;
            $("#ticketTab tbody tr").each(function(){
                Number($(this).children().eq(6).html()) !== Number($(this).children().eq(7).html()) ? isAllOk = false:"";
            });
            if (isAllOk) {
                invoiceInforCompleted();
            } else {
                layer.msg("操作失败，请检查所录入的数据！");
            }
        }
    }
}
// creator: 李玉婷，2022-06-10 09:33:12，本次开票申请中的货物均已开具发票
function invoiceInforCompleted() {
    let detail = JSON.parse($("#applyDetail").html());
    let param = {
        userId: sphdSocket.user.userID,
        saleId: detail.creator,//
        state: 2,
        sure: 1,
        applicationState: 5,
        divisionWay: 2
    };
    let ids = ``, item = ``;
    $("#ticketTab tbody tr").each(function () {
        item = JSON.parse($(this).children().eq(0).find(".hd").html());
        ids += item.item_id + ',';
    });
    ids = ids.substring(0, ids.length-1);
    param.applicationItemIds = ids;
    loading.open();
    console.log(
        '全部提交:'+ JSON.stringify(param)
    )
    sphdSocket.send('invoiceApplicationHandle', param)
}
// creator : hxz 2021-05-25  查看开票资料
function getInvoiceMessage() {
    let customer = JSON.parse($("#applyDetail").html());
    let customerID = customer.id;
    $.ajax({
        "url":"../sales/getCustomerInvoice",
        "data":{ "id": customerID},
        success:function (res) {
            if(res['data']){
                let info = res['data'] && res['data'][0]
                bounce.show($("#InvoiceMessage"));
                $("#InvoiceMessage .invoiceName").html(info.full_name);
                $("#InvoiceMessage .invoiceAddress").html(info.address);
                $("#InvoiceMessage .telephone").html(info.telephone);
                $("#InvoiceMessage .bankName").html(info.bank_name);
                $("#InvoiceMessage .bank_no").html(info.bank_no);
                $("#InvoiceMessage .taxpayerID").html(info.taxpayerID);

            }else{
                layer.msg("未获取开票信息")
            }
        }
    })
}
// creator: 李玉婷，2022-05-19 15:48:26，获取发票列表 ticket
function getInvoices(invoiceCategory, obj) {
    invoiceCategory = Number(invoiceCategory);
    if(invoiceCategory == 2 || invoiceCategory == 3){
        invoiceCategory = 4
    }
    $.ajax({
        "url":"../invoice/getInvoices.do",
        "data":{'type': invoiceCategory},
        success:function (res) {
            let list = res.invoiceDetails;
            let html = ``;
            for(let i =0;i<list.length;i++){
                html +=
                    `<option value='${list[i].id}'>${list[i].invoiceNo}</option>`;
            }
            obj.append(html);
        }
    })
}

// creator: 李玉婷，2022-05-19 16:16:11，删除本条
function delInvoiceMessage(obj, id){
    $("#delInvoiceMessage").data("id",id);
    $("#delInvoiceMessage").data("obj",obj);
    bounce_Fixed.show($("#delInvoiceMessage"));
}
function delInvoiceMessageSure(){
    let id = $("#delInvoiceMessage").data("id");
    let obj = $("#delInvoiceMessage").data("obj");
    $.ajax({
        "url":"../sale/delInvoiceDetail",
        "data":{ "id": id},
        success:function (res) {
            obj.parents("tr").remove();
            bounce_Fixed.cancel();
            getTempTickets();
            getTicketInfo();
        }
    })
}
// creator: 李玉婷，2022-05-19 09:33:46，编辑发票信息
function getEditInvoiceMessage(obj) {
    goodList = [];
    let info = JSON.parse(obj.siblings(".hd").html())
    let option = `<option value="${info.fid}">${info.invoice_no}</option>`
    $("#e_tickets").html(option).data("old", info.fid);
    $("#e_invoiceDate").val(new Date(info.operate_date).format('yyyy-MM-dd'));
    $("#editInvoiceMessage").data("id", info.item_id);
    getInvoices(info.invoice_category, $("#e_tickets"));
    $.ajax({
        "url":"../sale/applicationGoodsDetails.do",
        "data":{
            "divisionWay": 2,
            "applicationId": info.fid
        },
        success:function (res) {
            let list = res.data || [];
            goodList = list;
            setEditInvoiceMessage(3);
        }
    })
}
function setEditInvoiceMessage(num) {
    let html = ``, total = 0;
    for (let i =0;i<goodList.length;i++){
        total += Number(goodList[i].fid_amount) + Number(goodList[i].fid_invoice_amount) ;
        html +=
            `
             <tr>
                 <td>${-(-i-1)}
                 <span class="hd">${JSON.stringify(goodList[i])}</span>
                 </td>
                 <td>${goodList[i].name}</td>
                 <td>${goodList[i].unit}</td>
                 <td>${goodList[i].invoice_quantity || 0}</td>
                 <td>${goodList[i].unit_price_c || 0}</td>
                 <td>${goodList[i].fid_amount || 0}</td>
                 <td>${goodList[i].invoice_rate}%</td>
                 <td>${Number(goodList[i].fid_invoice_amount  || 0).toFixed(2)}</td>
             </tr>
            `
    }
    if (num === 1) {
        $("#goodsDetails tr:gt(0)").remove();
        $("#goodsDetails").append(html);
        $("#entryInvoiceMessage .detailCon span:eq(0)").html(goodList.length);
        $("#entryInvoiceMessage .detailCon span:eq(1)").html(total.toFixed(2));
        bounce_Fixed.show($("#entryInvoiceMessage"));
    } else {
        $("#e_goodsDetails tr:gt(0)").remove();
        $("#e_goodsDetails").append(html);
        bounce_Fixed.show($("#editInvoiceMessage"));
    }
}
// creator: 李玉婷，2022-05-19 17:23:58，已开发票的信息确定
function invoiceMessageSure() {
    let invoiceDate = $("#invoiceDate").val();
    let invoice = $("#tickets").val();
    if (invoiceDate !== "" && invoice !== "") {
        let detail = JSON.parse($("#applyDetail").html());
        let item = ``, item_amount=``,amounts = 0;
        $("#goodsDetails tr:gt(0)").each(function () {
            item = JSON.parse($(this).children().eq(0).find(".hd").html());
            item_amount += item.item_id + '-'+ item.invoice_quantity + ',';
            amounts += Number((item.unit_price_c * item.invoice_quantity).toFixed(2))+Number((item.unit_price_c * item.invoice_quantity*item.invoice_rate/100).toFixed(2));
        });
        item_amount = item_amount.substring(0, item_amount.length-1);
        let param = {
            itemIdAndAmount: item_amount,
            userId: sphdSocket.user.userID,
            saleId: detail.creator,//
            operation: 0,
            state: 2,
            financeInvoiceId: invoice,
            date: invoiceDate,
            invoiceCategory: detail.invoice_require,
            amount: amounts,
            includeAmount: $("#priceTaxTotal").val(),
            line: $("#goodsDetails tr:gt(0)").length,
            applicationState: detail.application_state
        };
        loading.open();
        sphdSocket.send('invoiceApplicationHandle', param)
    }
}
// creator: 李玉婷，2022-06-07 13:00:19，编辑发票信息确定
function e_invoiceMessageSure() {
    let val = $("#e_tickets").val();
    let date = $("#e_invoiceDate").val();
    let item_id = $("#editInvoiceMessage").data("id");
    if (val !== '' && date !== "") {
        let oldId = $("#e_tickets").data("old");
        $.ajax({
            "url":"../sale/editInvoiceDetail",
            "data":{
                "itemId": item_id,
                "oldFid": oldId,
                "newFid": val,
                "date": date
            },
            success:function () {
                bounce_Fixed.cancel();
                getTempTickets();
            }
        })
    }
}
function ticketApplyBack(){
    let orderId = $("#ticketCustomerName").data("id");
    bounce.cancel();
    $.ajax({
        "url":"../sale/applicationDetails.do",
        "data":{
            "state": 3,
            "orderId": orderId,
            "applicationId": detailID},
        success:function (res) {
            let tempGoodList = res.data || [];
            if (tempGoodList.length > 0){
                let ids = ``;
                for(let i=0;i<tempGoodList.length;i++){
                    ids += tempGoodList[i].fid + `,`;
                }
                ids = ids.substring(0, ids.length-1);
                $.ajax({
                    "url":"../sale/delInvoiceDetail",
                    "data":{ "id": ids},
                    success:function (res) {
                        window.parent.hideDetailsPage();
                    }
                })
            } else {
                window.parent.hideDetailsPage();
            }
        }
    })
}
// creator: 李玉婷，2022-05-23 18:55:45，回调
function handleCallback(response) {
    loading.close();
    bounce_Fixed.cancel();
    let res = JSON.parse(response)
    let userId = sphdSocket.user.userID
    if (res['state'] === '5') { // 前面确定开票
        // 状态：0-暂存,1-录入,2-提交,3审批通过,4-暂缓开票,5-财务开票,6-待发出(核对),7-发出登记,8-货运,9-签收
        //layer.msg('其他财务人员已对此发票申请开票');
        layer.msg('操作成功');
        window.parent.hideDetailsPage();
    } else { // 后面选发票号的

        $(".goodsSelect .changeDot i").attr("class", "fa fa-circle-o");
        getTicketInfo();
        if (res['auditor'] === userId) { // 本人看到
            layer.msg('操作成功！');
        } else { // 看到其他财务操作了
            layer.msg('其他财务人员已填写该发票的发票号');

        }
    }
}
// creator : hxz 2019-02-15  切换开票勾选状态
function toogleI(obj) {
    let info = JSON.parse(obj.find(".hd").html());
    let invoice_require = info.invoice_require;
    if (invoice_require === 3) {
        layer.msg("本商品不开票");
        return false;
    }
    let item = ``, itemCat = ``;
    obj.parents("tbody").children("tr").each(function () {
        var isSelect = $(this).children(":eq(0)").children("i").hasClass("fa-check-square-o");
        if (isSelect) {
            item = $(this).children(":eq(0)").find(".hd").html();
            itemCat = item.invoice_require;
            return false;
        }
    });
    if (itemCat != "" && itemCat != invoice_require) {
        var selectCat = "";
        if(invoice_require === 1){
            selectCat = '增值税专用发票'
        }else if(invoice_require === 2){
            selectCat = '普通发票'
        }
        layer.msg("本次请选择开 " + selectCat + " 的商品进行开票，其他发票请下次再选择");
        return false;
    }
    var able = obj.siblings(":eq(5)").html() === obj.siblings(":eq(6)").html();
    if (able) {
        layer.msg("本商品可申请数量上限数目为0，不能开票！");
        return false;
    }
    var iObj = obj.children("i");
    var isOK = iObj.hasClass("fa-square-o");
    if (isOK) {
        iObj.attr("class", "fa fa-check-square-o");
        obj.siblings(":last").html("<input type='text' onkeyup='tofixed3(this)'>").removeClass("ty-td-gray");
    } else {
        iObj.attr("class", "fa fa-square-o");
        obj.siblings(":last").html("").addClass("ty-td-gray");
    }

}
// creator : hxz 2019-02-21 工具方法 - 校验 开票的按钮是否可点
function ticketFun() {
    $("#ticketTab tbody").children("tr").each(function () {
        var isSelect = $(this).children(":eq(0)").children("i").hasClass("fa-check-square-o");
        if (isSelect) {
            var num = $(this).children(":last").children("input").val();
            var maxNum = (Number($(this).children(":eq(6)").html()) - Number($(this).children(":eq(7)").html() || 0)).toFixed(4);
            if (Number(num) > 0) {
                if (maxNum < Number(num)) {
                    layer.msg("操作失败！请录入符合逻辑的数量！");
                    $(this).children(":last").children("input").val(maxNum);
                }
            }
        }
    });
}
// creator : hxz 2018-07-20  工具方法 ， 返回发票种类
function category(type) {
    if (type == 1) {
        return "增值税专用发票";
    } else if (type == 2) {
        return "普通发票";
    } else if (type == 3) {
        return "普通发票";
    } else {
        return "— —";
    }
}

//  create: hxz 2021-05-11 保留三位小数
function tofixed3(obj) {
    let v = obj.value
    v = v.replace(/[^\d.]/g, "");
    v = v.replace(/^\./g, "");
    v = v.replace(/\.{2,}/g, ".");
    v = v.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".")
    let vArr = v.split('.')
    if(vArr[0].length > 9){ // 最多9位
        vArr[0] = String(vArr[0]).substr(0,9);
    }
    v = vArr[0]
    if(vArr.length > 1){
        if(vArr[1].length > 4){
            vArr[1] = vArr[1].substr(0,4);
        }
        v += '.' + vArr[1];
    }
    obj.value = v
}

laydate.render({elem: '#invoiceDate'});
laydate.render({elem: '#e_invoiceDate'});
