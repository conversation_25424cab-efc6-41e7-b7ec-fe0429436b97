var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var subArr = [];
$(function(){
    // 初始化
    $(".ty-secondTab li").on("click",function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        $(".ty-mainData table").addClass("hd");
//获取点击按钮的下标
        var index = $(this).index();
        if(index === 0){
            subArr.push(sphdSocket.subscribe('invoiceHandle', handleCallback, null, 'user'));
            $(".ty-mainData table").eq(0).removeClass("hd");
            getListData(1);
        }else if(index === 1){
            cancelSub();
            $(".ty-mainData table").eq(1).removeClass("hd");
            getSignList(1);
        }else if(index === 2){
            cancelSub();
            $(".ty-mainData table").eq(2).removeClass("hd");
            getRecordList(1);
        }
    });
    $(".ty-secondTab li:eq(0)").click();
    $("#togetherWith").change(function(){
        $(".togetherInfo").removeClass("hd");
        var data = JSON.parse($(this).find("option:selected").attr("data"));
        $("#with_company").html(data.deliveryCompany);
        $("#with_expressSn").html(data.deliverySn);
        $("#with_mailDate").html(new Date(data.deliveryTime).format('yyyy/MM/dd'));
    });
    $("#edit-together").change(function(){
        $(".editTogetherInfo").removeClass("hd");
        var data = $(this).find("option:selected").attr("dataD");
        data = JSON.parse(data);
        $("#with_companyEdit").html(data.deliveryCompany);
        $("#with_expressSnEdit").html(data.deliverySn);
        $("#with_mailDateEdit").html(new Date(data.deliveryTime).format('yyyy/MM/dd'));
    });
});
function handleCallback(data){
    data = JSON.parse(data);
    // var data = JSON.parse(data);
    if (data.state == '5') {
        getListData(1);
    }
}
// creator: 李玉婷，2019-05-27 11:36:52，获取数据
function getListData(cur){
    $.ajax({
        "url" : "../invoiceDelivery/getInvoiceList.do" ,
        "data" : { "currPage" : cur , "pageSize": 20  } ,
        success:function(res) {
            var str = "" , list = res["list"]["data"] ;
            //设置分页信息
            var currentPageNo   = res["list"]["currPage"],
                totalPage       = res["list"]["totalPage"];

            setPage( $("#ye_issued"), currentPageNo, totalPage, "noIssued") ;
            if(list && list.length>0){
                for(var i = 0 ; i < list.length ; i++){
                    var checkBut = list[i].checkBut ? '' : 'disabled';
                    var sendBut = list[i].allowBut ? '' : 'disabled';
                    str += "<tr>" +
                        "    <td>"+ (new Date(list[i]["createDate"]).format("yyyy/MM/dd hh:mm:ss")) +"</td>" +
                        "    <td>"+ clearNull(list[i]["applicantName"]) +"</td>" +
                        "    <td>"+ (new Date(list[i]["applyDate"]).format("yyyy/MM/dd hh:mm:ss")) +"</td>" +
                        "    <td>"+ clearNull(list[i]["createName"]) +"</td>" +
                        "    <td>"+ clearNull(list[i]["customerName"]) +"</td>" +
                        "    <td>"+ clearNull(list[i]["customerCode"]) +"</td>" +
                        "    <td>"+ clearNull(list[i]["sn"]) +"</td>" +
                        "    <td>"+ (clearNull(list[i]["amount"])).toFixed(2) +"</td>" +
                        "    <td>"+ clearNull(list[i]["invoiceCount"]) +"</td>" +
                        "    <td>" +
                        "        <button class=\"ty-btn ty-btn-blue ty-circle-3\""+ checkBut +" onclick=\"invoiceCheck($(this))\">发票核对</button>" +
                        "        <button class=\"ty-btn ty-btn-blue ty-circle-3\""+ sendBut +" onclick=\"invoiceSend($(this))\">发票发出登记</button>" +
                        "        <span class='hd'>"+ JSON.stringify(list[i]) +"</span>" +
                        "    </td>" +
                        "</tr>" ;
                }
            }
            $("#serviceList tbody").html(str);
        }
    })
}
// creator: 李玉婷，2019-05-27 11:37:47，清除订阅
function cancelSub(){
    if(subArr.length>0){
        for(var w=0;w<subArr.length;w++){
            sphdSocket.unsubscribe(subArr[w]);
        }
    }
}
//creator:hxz date:2019-03-01 发票核对列表
var pointObj = null;
function invoiceCheck(obj){
    pointObj = obj;
    var info = obj.siblings(".hd").html(); info = JSON.parse(info) ;
    var id = info["id"];
    getInvoiceList(id);
}
//creator:hxz date:2019-03-16 发票核对列表数据获取
function getInvoiceList(id){
    $.ajax({
        "url" : "../invoiceDelivery/checkInvoiceList.do" ,
        "data" : { "id": id } ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var html = '';
                var checkCount = 0;
                var reasonCount = 0;
                var normalCount = 0;
                var reasonAble = 0;
                var operResult = '';
                var list = res["list"] ;
                for(var t=0;t<list.length;t++){
                    if(list[t].result == '-1'){ //未核对
                        checkCount++ ;
                    }else if(list[t].result == '1'){ // 通过
                        reasonCount++ ;
                        if(list[t].checkBut){
                            reasonAble++ ;
                        }
                    }else if(list[t].result == '0'){ // 未通过
                        normalCount++ ;
                    }
                }
                if(reasonCount || normalCount){
                    operResult = '<td></td>';
                    $(".checkResult").removeClass('hd');
                    if(normalCount){
                        $(".checkDiff").removeClass("hd").siblings().addClass('hd');
                        $(".resultInit ul").removeClass("hd");
                    }else{
                        $(".checkSame").removeClass("hd").siblings().addClass('hd');
                        $(".resultInit ul").addClass("hd");
                    }
                }else{
                    $(".checkDiff").addClass("hd");
                    $(".checkSame").addClass("hd");
                    $(".checkResult").addClass('hd');
                    $(".resultInit ul").addClass("hd");
                    $(".checkInit").removeClass('hd').siblings().addClass('hd');
                }
                if(reasonAble <= 0){
                    pointObj.siblings("button").prop("disabled",true);
                }else{
                    pointObj.siblings("button").prop("disabled",false);
                }
                for(var a=0; a<list.length;a++){
                    var checkBut = list[a].checkBut ? '':'disabled';
                    if(list[a].invoiceCategory == '1'){
                        html +=
                            '<tr><td>增值税专用发票</td>';
                    }else if(list[a].invoiceCategory == '2'){
                        html +=
                            '<tr><td>增值税普通发票</td>';
                    }else if(list[a].invoiceCategory == '3'){
                        html +=
                            '<tr><td>其他普通发票</td>';
                    }
                    html +=
                        '    <td>' + list[a].invoiceNo + '</td>' +
                        '    <td>' + Number(list[a].amount).toFixed(2) + '</td>';
                    //result -1未审核    0审核未通过    1通过
                    if(list[a].result == '-1'){
                        html += '<td><button class="ty-btn ty-btn-blue ty-circle-3" onclick="checkDetail($(this))" '+ checkBut+'>核对</button><span class="hd">'+ JSON.stringify(list[a]) +'</span></td>' + operResult;
                    }else if(list[a].result == '0'){
                        html +=
                            '  <td><button class="ty-btn ty-btn-blue ty-circle-3" onclick="checkDetail($(this))" '+ checkBut +'>重新核对</button><span class="hd">'+ JSON.stringify(list[a]) +'</span></td>'+
                            '  <td class="flag">'+ list[a].resultReasion +'</td>';
                    }else{
                        html +=
                            '  <td><button class="ty-btn ty-btn-blue ty-circle-3" onclick="checkDetail($(this))" '+ checkBut +'>重新核对</button><span class="hd">'+ JSON.stringify(list[a]) +'</span></td>'+
                            '  <td>'+ list[a].resultReasion +'</td>';
                    }
                    html += '</tr>';
                }
                $(".initList tbody").html(html);
                bounce.show($("#invoiceCheck"));
            }
        }
    });
}
//creator:lyt date:2018/12/21 发票核对详情
function checkDetail(obj){
    $(".partThrList").remove();
    //applicationItemId  //申请单明细 id
    var info = JSON.parse(obj.siblings('.hd').html());
    var itemId = info.applicationItemId;
    let param = { "applicationItemId": itemId }
    if (info.division_way === '2') { param.fid = info.invoice; }
    $("#invoiceDetailCheck button").removeClass("choosed");
    $.ajax({
        "url" : "../invoiceDelivery/invoiceDetails.do" ,
        "data" : param ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var invoice = res["data"];
                var list = invoice["intem"] ;
                var lost = invoice["losted"];
                var partOne = ' 一  手中为发票号码为<span style="color:red;">' + info.invoiceNo + '</span>的<span style="font-weight:600;">' + invoiceType(invoice.type) +'</span>';
                var partTwo= '';
                $(".partOneCon").html(partOne);
                $(".partFourAmount").html(info.amount && info.amount.toFixed(2) );
                $(".partTwoCustorm").html(invoice.customerName);
                $("#invoiceDetailCheck").data("detail",JSON.stringify(info));
                for(var r=0; r<list.length; r++){
                    partTwo +=
                        '<tr class="partThrList">' +
                        '    <td>'+ list[r].itemName +'</td>' +
                        '    <td>'+ list[r].itemName +'</td>' +
                        '    <td>'+ list[r].itemQuantity +'</td>' +
                        '    <td>'+ list[r].itemPrice +'元</td>' +
                        '    <td>' +
                        '        <button class="ty-btn check-sign" choose="1" onclick="checkResult($(this))">一致</button>' +
                        '        <button class="ty-btn check-sign" choose="0" onclick="checkResult($(this))">不一致</button>' +
                        '    </td>' +
                        '</tr>';
                }
                $(".partThrTable").after(partTwo);
                if(lost == '1'){ //lost=1:发票丢失
                    $(".miss").addClass("hd");
                    $(".finded").removeClass("hd");
                    $(".checkFinish").prop("disabled",true);
                }
                bounce_Fixed.show($("#invoiceDetailCheck"));
            }
        }
    }) ;
}
//creator:lyt date:2018/12/21 一致、不一致
function checkResult(obj, num){
    if(num == '0'){
        $(".partTwo button").prop("disabled",true);
        $(".partThrList button").prop("disabled",true);
        $(".partFour button").prop("disabled",true);
    }else if(num == '1'){
        $(".partTwo button").prop("disabled",false);
        $(".partThrList button").prop("disabled",false);
        $(".partFour button").prop("disabled",false);
    }
    obj.addClass("choosed").siblings().removeClass("choosed");
}
//creator:lyt date:2018/12/24 发票已丢失
function invoiceMissed(){
    var invoiceInfo = $("#invoiceDetailCheck").data("detail");
    invoiceInfo = JSON.parse(invoiceInfo);
    var itemId  = invoiceInfo.applicationItemId
    var param = {
        "applicationItemId": itemId
    }
    if (invoiceInfo.division_way === '2') { param.fid = invoiceInfo.invoice; }
    $.ajax({
        "url" : "../invoiceDelivery/invoiceLoss.do" ,
        "data" : param ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                $("#tipContent").html("你糟糕了！请立即将丢失情况告知财务，以便处理。");
                $(".tipBtn").attr("onclick","bounce_Fixed2.cancel()");
                bounce_Fixed.cancel();
                bounce_Fixed2.show($('#missedTip'));
                var itemData = pointObj.siblings(".hd").html();
                itemData     = JSON.parse(itemData);
                var itemId   = itemData.id;
                getInvoiceList(itemId);
            }
        }
    }) ;
}
//creator:lyt date:2018/3/16 发票已找回
function invoiceFinded(){
    var invoiceInfo = $("#invoiceDetailCheck").data("detail");
    invoiceInfo = JSON.parse(invoiceInfo);
    var itemId  = invoiceInfo.applicationItemId;
    var param = { "applicationItemId": itemId }
    if (invoiceInfo.division_way === '2') { param.fid = invoiceInfo.invoice; }
    $.ajax({
        "url" : "../invoiceDelivery/invoiceBack.do" ,
        "data" : param ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                $("#tipContent").html("请速与财务确认是否已重新开发票。如已重开发票，则请其作废，以避免税费损失。");
                $(".tipBtn").attr("onclick","bounce_Fixed2.cancel()");
                $(".miss").removeClass("hd");
                $(".finded").addClass("hd");
                $(".checkFinish").prop("disabled",false);
                bounce_Fixed2.show($('#missedTip'));
            }
        }
    }) ;
}
//creator:lyt date:2018/12/24 本张发票核对完毕
function checkFinished() {
    if($(".partOne .choosed").length <= 0){
        layer.msg("请勾选第一部分检查结果！"); return false ;
    }else{
        if($('.partOne .choosed').attr("org") == '1'){
            if($(".partTwo .choosed").length <=0 ){
                layer.msg("请勾选客户名称检查结果！"); return false ;
            }
            var checkCount = 0;
            $(".partThrList").each(function(){
                if($(this).find(".choosed").length <= 0){
                    checkCount ++ ;
                    return false ;
                }
            })
            if(checkCount > 0){
                layer.msg("请勾选商品信息检查结果！");return false ;
            }else{
                if($(".partFour .choosed").length <=0 ){
                    layer.msg("请勾选发票金额检查结果！"); return false ;
                }
            }
        }
    }
    var result = 1;
    var combin = 0;
    // String applicationNo  //申请单号
    var checkOne = $('.partOne .choosed').attr("org");
    if(checkOne == '1'){ //1=无问题，2=发票不对
        var flag2 = 0, flag3 = 0, flag4 = 0;
        if($(".partTwo .choosed").attr("choose") == '0') { flag2 = 1; }
        $(".partThrList .choosed").each(function(){
            if($(this).attr("choose") == '0') { flag3 = 2; }
        });
        if($(".partFour .choosed").attr("choose") == '0') { flag4 = 4; }
        combin = flag2 + flag3 + flag4;
        if(combin == 0){
            result = 1;
        }else{
            result = 0;
        }
    }else{
        result = 0;
    }
    var invoiceInfo = $("#invoiceDetailCheck").data("detail");
    invoiceInfo = JSON.parse(invoiceInfo);
    var invoiceId  = invoiceInfo.invoice;
    var param = {
        'checkResult': result,
        'invoice': invoiceId
    }
    if(!result && combin){
        param.resultType = combin
    }
    $.ajax({
        "url" : "../invoiceDelivery/checkInvoice.do" ,
        "data" : param ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                bounce_Fixed.cancel();
                var itemData = pointObj.siblings(".hd").html();
                itemData     = JSON.parse(itemData);
                var itemId   = itemData.id;
                getInvoiceList(itemId);
            }
        }
    }) ;
}
//creator:hxz date:2019-03-01 发票发出登记列表
function invoiceSend(obj,id){
    var info = obj.siblings(".hd").html(); info = JSON.parse(info) ;
    var id = info["id"];
    $(".send-deliveType span").attr("check","0");
    $(".send-deliveType .fa").attr("class","fa fa-square-o");
    $("#deliverCon").children().addClass("hd");
    $(".type-express").addClass("hd");
    $.ajax({
        "url" : "../invoiceDelivery/registrationList.do" ,
        "data" : { "id": id } ,
        success:function(res){
            var status = res["state"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var html = '';
                var option = '<option value="0">请选择</option>';
                var snOption = '<option value="0">请选择</option>';
                var receiver = res["customerInvoice"];
                var deliveryList = res["deliveryList"];
                var invoiceInfo = res["list"];
                $('#issueRegist').data("registId",id);
                $(".reg-customer").html(res.customerName);
                for(var x=0; x<invoiceInfo.length; x++){
                    html +=
                        '<tr><td class="hd">'+ JSON.stringify(invoiceInfo[x]) +'</td>';
                    if(invoiceInfo[x].invoiceCategory == '1'){
                        html +=
                            '<td>增值税专用发票</td>';
                    }else if(invoiceInfo[x].invoiceCategory == '2'){
                        html +=
                            '<td>增值税普通发票</td>';
                    }else if(invoiceInfo[x].invoiceCategory == '3'){
                        html +=
                            '<td>其他普通发票</td>';
                    }
                    html +=
                        '    <td>'+ invoiceInfo[x].invoiceNo +'</td>' +
                        '    <td>'+ (invoiceInfo[x].amount).toFixed(2) +'</td>' +
                        '    <td>' +
                        '        <span><i class="fa fa-square-o" onclick="checkedSet($(this))"></i></span>' +
                        '    </td>' +
                        '</tr>';
                }
                for(var a=0; a<receiver.length; a++){
                    option +=
                        '<option value="'+ receiver[a].contact +'">'+ receiver[a].contact +'</option>';
                }
                for(var b=0; b<deliveryList.length; b++){
                    snOption +=
                        '<option data=\''+ JSON.stringify(deliveryList[b]) +'\' value="'+ deliveryList[b].deliverySn +'">'+ deliveryList[b].deliverySn +'</option>';
                }
                $("#receiver").html(option);
                $("#togetherWith").html(snOption);
                $("#invoicesCheck tbody").html(html);
                $("#deliverCon input").val('');
                $("#deliverCon select").children().eq(0).attr("selected","selected");
                bounce.show($('#issueRegist'));
                bounce.everyTime('0.5s','invoicesCheck',function () {
                    var field = 0;
                    var len = $("#invoicesCheck tbody tr").length;
                    var long = $("#invoicesCheck tbody .fa-check-square-o").length;
                    if(len == long){
                        $("#checkAll i").attr("class","fa fa-check-square-o");
                    }else{
                        $("#checkAll i").attr("class","fa fa-square-o");
                    }
                    if(long <= 0){field++;}
                    if($("#receiver").val() == 0){field++;}
                    if($("#invoiceDeliveType .fa-check-square-o").length == 0){
                        field++;
                    }else{
                        if($("#invoiceDeliveType .fa-check-square-o").attr("type_express") == 'A'){
                            if($(".type-express .fa-check-square-o").length == 0){
                                field++;
                            }else{
                                if($(".type-express .fa-check-square-o").attr("type") == '1'){
                                    if($("#expressCompany").val() == '' || $("#expressSn").val() == '' || $("#mailDate").val() == ''){ field++; }
                                }else{
                                    if($("#togetherWith").val() == '0'){ field++; }
                                }
                            }
                        }else{
                            if($("#carryer").val() == '' || $("#carryerCall").val() == '' || $("#carryDate").val() == ''){ field++; }
                        }
                    }
                    field > 0 ? $("#sendRegistSure").prop("disabled", true) : $("#sendRegistSure").prop("disabled", false)
                });
            }
        }
    });
}
//creator:lyt date:2018/12/24 全选按钮
function checkAllBtn(obj){
    if(obj.attr("class") == 'fa fa-square-o'){
        obj.attr("class","fa fa-check-square-o");
        $("#invoicesCheck").find(".fa").attr("class","fa fa-check-square-o");
    }else{
        obj.attr("class","fa fa-square-o");
        $("#invoicesCheck").find(".fa").prop("class","fa fa-square-o");
    }
}
//creator:lyt date:2018/12/25 发票勾选
function checkedSet(obj){
    if(obj.attr("class") == 'fa fa-square-o'){
        obj.attr("class","fa fa-check-square-o");
    }else{
        obj.attr("class","fa fa-square-o");
    }
}
//creator:lyt date:2018/12/25 发出方式 - 快递与随身携带切换
function setDeliveType(obj,num){
    if(obj.attr("check") == "0"){
        obj.attr("check","1").children().attr("class","fa fa-check-square-o");
        obj.siblings().attr("check","0").children().attr("class","fa fa-square-o");
        $("#deliverCon>div").addClass("hd");
        if(num == "1"){
            $(".type-express").removeClass("hd");
            $(".type-express span").attr("check","0").children("i").attr("class","fa fa-square-o");
        }else if(num =="2"){
            $(".type-express").addClass("hd");
            $("#deliverCon>div:eq(1)").removeClass("hd");
            $("#deliverCon>div:eq(2)").removeClass("hd");
        }else if(num =="3"){
            $("#deliverCon>div:eq(0)").removeClass("hd");
            $("#deliverCon>div:eq(2)").removeClass("hd");
        }else if(num =='4'){
            $("#deliverCon>div:eq(3)").removeClass("hd");
        }
    }
}
//creator:lyt date:2019/3/18 发票发出登记确定
function sendRegistSure(){
    var invoices = [];
    var resId = $('#issueRegist').data("registId");
    var receiver = $("#receiver").val();
    var sendWay = $("#invoiceDeliveType .fa-check-square-o").attr("type_express");
    if(receiver == '0'){
        layer.msg("请选择发票接收人！"); return false ;
    }
    if(!sendWay){
        layer.msg("请选择发票发出方式！"); return false ;
    }
    $("#invoicesCheck tbody tr").each(function(){
        var info = JSON.parse($(this).children().eq(0).html());
        console.log($(this).find(".fa-check-square-o").length);
        if($(this).find(".fa-check-square-o").length > 0){
            info.selected = true;
        }else{
            info.selected = false;
        }
        invoices.push(info);
    })
    invoices = JSON.stringify(invoices);
    var param = {
        'id': resId,
        'invoices': invoices,
        'receiver': receiver,
        'deliveryWay': sendWay
    }
    if(sendWay == 'A'){ // A-快递,B-随身携带,C-货运
        var expressWay = $(".type-express .fa-check-square-o").attr("type");
        if(expressWay == '1'){ // 1-单独寄出,2-与其他物品共同寄出
            var expressCompany = $("#expressCompany").val();
            var expressSn      = $("#expressSn").val();
            var mailDate       = $("#mailDate").val();
            var memo = $("#sign_mome").val();

            param.memo            = memo;
            param.deliverySn      = expressSn;
            param.deliveryTime    = mailDate;
            param.deliverySubWay  = expressWay;
            param.deliveryCompany = expressCompany;
        }else if(expressWay == '2'){
            param.deliverySubWay  = expressWay;
            param.deliverySn      = $("#with_expressSn").html();
            param.deliveryTime    = $("#with_mailDate").html();
            param.deliveryCompany = $("#with_company").html();
        }
    }else if(sendWay == 'B'){
        var carryer = $("#carryer").val();
        var call = $("#carryerCall").val();
        var carryDate = $("#carryDate").val();
        var memo = $("#sign_mome").val();

        param.memo          = memo;
        param.operator      = carryer;
        param.operatorPhone = call;
        param.deliveryTime  = carryDate;
    }
    $.ajax({
        "url" : "../invoiceDelivery/registration.do" ,
        "data" : param ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                bounce.cancel();
                $(".ty-secondTab li:eq(0)").click();
            }
        }
    });
}

// Creator： lyt 待签收列表获取
function getSignList(cur) {
    $.ajax({
        "url" : "../invoiceDelivery/signForList.do" ,
        "data" : { "currPage" : cur , "pageSize": 20  } ,
        success:function(res) {
            var str = "" , list = res["data"] ;

            //设置分页信息
            var currentPageNo   = res["currPage"],
                totalPage       = res["totalPage"];

            setPage( $("#ye_issued"), currentPageNo, totalPage, "noSigned") ;
            if(list && list.length>0){
                for(var i = 0 ; i < list.length ; i++){
                    var delivery = list[i]["delivery"];
                    var len = delivery.length;
                    for(var t=0;t<delivery.length;t++){
                        var deliveryWay = '';
                        var recordBtn = delivery[t]["recordBut"] ? '':'disabled';
                        deliveryWay = delivery[t]["deliveryWay"] == 'A' ? '快递':'随身携带';
                        if(t == 0){
                            str += "<tr>" +
                                "    <td rowspan="+ len +">"+ (new Date(list[i]["createDate"]).format("yyyy/MM/dd hh:mm:ss")) +"</td>" +
                                "    <td rowspan="+ len +">"+ clearNull(list[i]["applicantName"]) +"</td>" +
                                "    <td rowspan="+ len +">"+ clearNull(list[i]["customerName"]) +"</td>" +
                                "    <td rowspan="+ len +">"+ clearNull(list[i]["customerCode"]) +"</td>" +
                                "    <td rowspan="+ len +">"+ (clearNull(list[i]["amount"]) !== ''?clearNull(list[i]["amount"]).toFixed(2): '') +"</td>" +
                                "    <td rowspan="+ len +">"+ clearNull(list[i]["invoiceCount"]) +"</td>" +
                                " <td>"+ clearNull(deliveryWay) +"</td>" +
                                " <td>" + delivery[t].invoiceCount + "</td>" +
                                " <td>" + (new Date(delivery[t].createDate).format("yyyy/MM/dd")) + "</td>" +
                                " <td>" + delivery[t].receiver + "</td>" +
                                " <td>" +
                                "      <button class=\"ty-btn ty-btn-blue ty-circle-3\" onclick=\"invoiceUpdate($(this), " + delivery[t]["deliveryId"] + ");\">修改</button>" +
                                "      <button class=\"ty-btn ty-btn-blue ty-circle-3\" onclick=\"updateRecord(" + delivery[t]["deliveryId"] + ")\" "+ recordBtn +">修改记录</button>" +
                                "      <span class=\"hd\">"+ list[i]["id"] +"</span>" +
                                " </td>" +
                                " <td><button class=\"ty-btn ty-btn-blue ty-circle-3\" onclick=\"signForRecive(" + delivery[t]["deliveryId"] + ")\">签收登记</button></td>" +
                                "</tr>";
                        }else{
                            str +=
                                "<tr>"+
                                " <td>"+ deliveryWay +"</td>" +
                                " <td>" + delivery[t].invoiceCount + "</td>" +
                                " <td>" + (new Date(delivery[t].createDate).format("yyyy/MM/dd")) + "</td>" +
                                " <td>" + delivery[t].receiver + "</td>" +
                                " <td>" +
                                "      <button class=\"ty-btn ty-btn-blue ty-circle-3\" onclick=\"invoiceUpdate($(this), " + delivery[t]["deliveryId"] + ");\">修改</button>" +
                                "      <button class=\"ty-btn ty-btn-blue ty-circle-3\" onclick=\"updateRecord(" + delivery[t]["deliveryId"] + ")\" "+ recordBtn +">修改记录</button>" +
                                "      <span class=\"hd\">"+ list[i]["id"] +"</span>" +
                                " </td>" +
                                " <td><button class=\"ty-btn ty-btn-blue ty-circle-3\" onclick=\"signForRecive(" + delivery[t]["deliveryId"] + ")\">签收登记</button></td>"+
                                "</tr>";
                        }
                    }
                }
            }
            $("#noSigned tbody").html(str)
        }
    })
}
//creator:lyt date:2019/3/19 待签收-修改-发出方式勾选切换
function setDeliveTypeUpdate(obj,num){
    if(obj.attr("check") == "0"){
        obj.attr("check","1").children().attr("class","fa fa-check-square-o");
        obj.siblings().attr("check","0").children().attr("class","fa fa-square-o");
        $("#deliverConUpdate>div").addClass("hd");
        if(num == "1"){
            $(".type-expressUpdate").removeClass("hd");
            $(".type-expressUpdate span").attr("check","0").children("i").attr("class","fa fa-square-o");
        }else if(num =="2"){
            $(".type-expressUpdate").addClass("hd");
            $("#deliverConUpdate>div:eq(1)").removeClass("hd");
            $("#deliverConUpdate>div:eq(2)").removeClass("hd");
        }else if(num =="3"){
            $("#deliverConUpdate>div:eq(0)").removeClass("hd");
            $("#deliverConUpdate>div:eq(2)").removeClass("hd");
        }else if(num =='4'){
            $("#deliverConUpdate>div:eq(3)").removeClass("hd");
        }
    }
}
//creator:lyt date:2019/3/19 待签收-修改-查看
function invoiceUpdate(obj,id){
    pointObj = obj;
//Integer deliveryId      //发运单id
    $.ajax({
        "url" : "../invoiceDelivery/registrationLook.do" ,
        "data" : {"deliveryId": id} ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var html = '';
                var getData = res["data"];
                var mailList = res["list"];
                var deliveryList = res["deliveryList"];
                var receivers = res["customerInvoice"];
                var customer = '客户名称：' + getData.customerName;
                var together = '<option value="0">请选择</option>';
                var option = '<option value="'+ getData.receiver +'">'+ getData.receiver +'</option>';
                $("#customeName").html(customer);
                $(".record-date").html(new Date(getData.operationDate).format('yyyy/MM/dd hh:mm:ss'));
                $(".record-operate").html(getData.operationName);
                $("#deliverConUpdate input").val("");
                $('#issueRegistUpdate').data("deliveryId",id);

                for(var z=0; z<mailList.length; z++){
                    var choose = mailList[z].selected ? 'fa-check-square-o':'fa-square-o"';
                    html +=
                        '<tr>' +
                        '    <td class="hd">'+ JSON.stringify(mailList[z]) +'</td>'+
                        '    <td>'+ invoiceType(mailList[z].invoiceCategory) +'</td>' +
                        '    <td>'+ mailList[z].invoiceNo +'</td>' +
                        '    <td>'+ (mailList[z].amount).toFixed(2) +'</td>' +
                        '    <td>'+
                        '        <span><i class="fa '+ choose +'" onclick="checkedSet($(this))" pai="'+ mailList[z].applicationItemId +'"></i></span>'
                        '    </td>' +
                        '</tr>';
                }
                $("#invoicesCheckUpdate tbody").html(html);
                for(var m=0; m<receivers.length; m++){
                    if(getData.receiver != receivers[m].contact){
                        option +=
                            '<option value="'+ receivers[m].contact +'">'+ receivers[m].contact +'</option>'
                    }
                }
                for(var a=0; a<deliveryList.length; a++){
                    together +=
                        '<option dataD=\''+ JSON.stringify(deliveryList[a]) +'\' value="'+ deliveryList[a].deliverySn +'">'+ deliveryList[a].deliverySn +'</option>';
                }
                $("#reciverUpdate").html(option);
                $("#edit-together").html(together);
                $("#up-memo").val(getData.memo);
                if(getData.deliveryWay == 'A'){
                    $("#updateDeliveType span").eq(0).click();
                    if(getData.deliverySubWay == '1'){
                        $(".type-expressUpdate span").eq(0).click();
                        $("#deliverConUpdate #up_company").val(getData.deliveryCompany);
                        $("#deliverConUpdate #up_sn").val(getData.deliverySn);
                        $("#deliverConUpdate #mailDateUpdate").val(new Date(getData.deliveryTime).format('yyyy/MM/dd'));
                    }else{
                        $(".type-expressUpdate span").eq(1).click();
                        $("#edit-together").val(getData.deliverySn);
                    }
                }else{
                    $("#updateDeliveType span").eq(1).click();
                    $("#deliverConUpdate #up-carrier").val(getData.operator);
                    $("#deliverConUpdate #up-call").val(getData.operatorPhone);
                    $("#deliverConUpdate #carryDateUpdate").val(new Date(getData.deliveryTime).format('yyyy/MM/dd'));
                }
                bounce.show($('#issueRegistUpdate'));
                bounce.everyTime('0.5s','invoicesCheckEdit',function () {
                    var field = 0;
                    var long = $("#invoicesCheckUpdate tbody .fa-check-square-o").length;
                    if(long <= 0){field++;}
                    if($("#reciverUpdate").val() == 0){field++;}
                    if($("#updateDeliveType .fa-check-square-o").length == 0){
                        field++;
                    }else{
                        if($("#updateDeliveType .fa-check-square-o").attr("up_express") == 'A'){
                            if($(".type-expressUpdate .fa-check-square-o").length == 0){
                                field++;
                            }else{
                                if($(".type-expressUpdate .fa-check-square-o").attr("val") == '1'){
                                    if($("#up_company").val() == '' || $("#up_sn").val() == '' || $("#mailDateUpdate").val() == ''){ field++; }
                                }else{
                                    if($("#edit-together").val() == '0'){ field++; }
                                }
                            }
                        }else{
                            if($("#up-carrier").val() == '' || $("#up-call").val() == '' || $("#carryDateUpdate").val() == ''){ field++; }
                        }
                    }
                    field > 0 ? $("#invoiceIssueUpdate").prop("disabled", true) : $("#invoiceIssueUpdate").prop("disabled", false)
                });
            }
        }
    });
}
//creator:lyt date:2019/3/19 待签收-修改-确定
function updateSure(){
    var invoices = [];
    var resId = pointObj.siblings(".hd").html();
    var deliveryId = $('#issueRegistUpdate').data("deliveryId");
    var receiver = $("#reciverUpdate").val();
    var sendWay = $("#updateDeliveType .fa-check-square-o").attr("up_express");
    $("#invoicesCheckUpdate tbody tr").each(function(){
        var temp = JSON.parse($(this).children().eq(0).html());
        if($(this).find(".fa-check-square-o").length > 0){
            temp.selected = true;
        }else{
            temp.selected = false;
        }
        invoices.push(temp);
    })
    invoices = JSON.stringify(invoices);
    var param = {
        'id': resId,
        'deliveryId': deliveryId,
        'invoices': invoices,
        'receiver': receiver,
        'deliveryWay': sendWay
    }
    if(sendWay == 'A'){ // A-快递,B-随身携带,C-货运
        var expressWay = $(".type-expressUpdate .fa-check-square-o").attr("val");
        if(expressWay == '1'){ // 1-单独寄出,2-与其他物品共同寄出
            var expressCompany = $("#up_company").val();
            var expressSn      = $("#up_sn").val();
            var mailDate       = $("#mailDateUpdate").val();
            var memo = $("#up-memo").val();

            param.memo            = memo;
            param.deliverySn      = expressSn;
            param.deliveryTime    = mailDate;
            param.deliverySubWay  = expressWay;
            param.deliveryCompany = expressCompany;
        }else if(expressWay == '2'){
            param.deliverySubWay  = expressWay;
            param.deliverySn      = $("#edit-together").val();
            param.deliveryTime    = $("#with_mailDateEdit").html();
            param.deliveryCompany = $("#with_companyEdit").html();
        }
    }else if(sendWay == 'B'){
        var carryer = $("#up-carrier").val();
        var call = $("#up-call").val();
        var carryDate = $("#carryDateUpdate").val();
        var memo = $("#sign_mome").val();

        param.memo          = memo;
        param.operator      = carryer;
        param.operatorPhone = call;
        param.deliveryTime  = carryDate;
    }
    $.ajax({
        "url" : "../invoiceDelivery/registrationUpdate.do" ,
        "data" : param ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                $(".ty-secondTab li:eq(1)").click();
                bounce.cancel();
            }
        }
    });
}
// creator: 李玉婷，2019-03-19 11:41:22，待签收--修改记录详列表
function updateRecord(id) {
    $.ajax({
        "url" : "../invoiceDelivery/registrationRecordList.do" ,
        "data" : {"deliveryId":id} ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var list = res["list"];
                var html = '';
                for(var s=0; s<list.length; s++){
                    html +=
                        '<tr>' +
                        '    <td>'+ (new Date(list[s].updateDate).format("yyyy-MM-dd hh:mm:ss")) +'</td>' +
                        '    <td>'+ list[s].updateName +'</td>' +
                        '    <td class="ty-td-control"><span class="ty-color-blue" onclick="updateDetail('+ list[s].recordId +', '+ list[s].isNew +', 1)">查看</span></td>' +
                        '    <td><span class="ty-color-blue" onclick="updateDetail('+ list[s].recordId +', '+ list[s].isNew +', 2)">查看</span></td>' +
                        '</tr>'
                }
                $("#issueUpdateRecordList table tbody").html(html);
                bounce.show($('#issueUpdateRecordList'))
            }
        }
    });
}
// creator: 李玉婷，2019-03-19 11:42:05，待签收--修改记录详情查看
function updateDetail(recordId, isNew, num) {
    $("#noSignUpdateRecord span").removeClass("flag");
    $.ajax({
        "url" : "../invoiceDelivery/registrationRecord.do" ,
        "data" : {
            "recordId": recordId,
            "isNew": isNew
        } ,
        success:function(res){
            var status = res["status"] ;
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var before = res["before"];
                var after = res["after"];
                var html = '';
                var record = {}, tempData = {};
                if(num == 1){
                    record = before;
                    tempData = after;
                    $("#noSignRecord").html('发票发出登记— —修改前');
                }else{
                    record = after;
                    tempData = before;
                    $("#noSignRecord").html('发票发出登记— —修改后');
                }
                $("#his-customer").html(record.customerName);
                $("#his-reciver").html(record.receiver);
                $(".his-sendBus").html(record.deliveryCompany);
                $(".his-sendSn").html(record.deliverySn);
                $(".his-carryer").html(record.operator);
                $(".his-carryerCall").html(record.operatorPhone);
                $(".his-notice").html(record.memo);
                $(".his-sendDate").html(new Date(record.deliveryTime).format('yyyy/MM/dd'));
                $(".his-carryDate").html(new Date(record.deliveryTime).format('yyyy/MM/dd'));

                conCompare(before.receiver, after.receiver) ? $("#his-reciver").removeClass("flag") : $("#his-reciver").addClass("flag");
                conCompare(before.deliveryWay, after.deliveryWay) ? $("#his-sendType").removeClass("flag") : $("#his-sendType").addClass("flag");
                conCompare(before.deliverySubWay, after.deliverySubWay) ? $("#his-sendType").removeClass("flag") : $("#his-sendType").addClass("flag");
                conCompare(before.deliverySn, after.deliverySn) ? $(".his-sendSn").removeClass("flag") : $(".his-sendSn").addClass("flag");
                conCompare(before.deliveryTime, after.deliveryTime) ? $(".his-carryDate").removeClass("flag") : $(".his-carryDate").addClass("flag");
                conCompare(before.deliveryTime, after.deliveryTime) ? $(".his-sendDate").removeClass("flag") : $(".his-sendDate").addClass("flag");
                conCompare(before.deliveryCompany, after.deliveryCompany) ? $(".his-sendBus").removeClass("flag") : $(".his-sendBus").addClass("flag");
                conCompare(before.operator, after.operator) ? $(".his-carryer").removeClass("flag") : $(".his-carryer").addClass("flag");
                conCompare(before.operatorPhone, after.operatorPhone) ? $(".his-carryerCall").removeClass("flag") : $(".his-carryerCall").addClass("flag");
                conCompare(before.memo, after.memo) ? $(".his-notice").removeClass("flag") : $(".his-notice").addClass("flag");

                if(record.deliveryWay == 'A'){
                    if(record.deliverySubWay == '1'){
                        $("#his-sendType").html('快递 - 单独寄出');
                    }else{
                        $("#his-sendType").html('快递 - 与其他物品共同寄出');
                    }
                    $(".expressWay").removeClass("hd").siblings().addClass("hd");
                }else{
                    $(".carryWay").removeClass("hd").siblings().addClass("hd");
                    $("#his-sendType").html('随身携带');
                }
                var flagClass = 'flagBg';
                var recordItems = record.items;
                var tempDataItems = tempData.items;
                for(var t=0; t<recordItems.length; t++){
                    flagClass = 'flagBg';
                    for(var i=0; i<tempDataItems.length; i++){
                        if(recordItems[t].invoice === tempDataItems[i].invoice){
                            flagClass = '';
                        }
                    }
                    html +=
                        '<tr class="'+ flagClass +'">' +
                        '    <td>'+ invoiceType(recordItems[t].type) +'</td>' +
                        '    <td>'+ recordItems[t].invoiceNo +'</td>' +
                        '    <td>'+ (recordItems[t].amount).toFixed(2) +'</td>' +
                        '</tr>';
                }
                $(".lookDetailTb tbody").html(html);
                bounce_Fixed.show($("#noSignUpdateRecord"));
            }
        }
    });
}
// creator: 李玉婷，2019-03-21 11:42:51，待签收 -- 签收登记
function signForRecive(id){
    $('#signRegister').data('deliveryId',id);
    $.ajax({
        "url" : "../invoiceDelivery/signForLook.do" ,
        "data" : {"deliveryId":id} ,
        success:function(res){
            var status = res["status"] ;
            var html = '';
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var sendInfo = res["data"];
                var list = sendInfo["list"];
                $("#sign-customer").html(sendInfo.customerName);
                $(".sign-receiver").html(sendInfo.receiver);
                $(".sign-bus").html(sendInfo.deliveryCompany);
                $(".sign-sn").html(sendInfo.deliverySn);
                $(".sign-carryer").html(sendInfo.operator);
                $(".sign-carryerCall").html(sendInfo.operatorPhone);
                $(".sign-notice").html(sendInfo.memo);
                $(".sign-sendDate").html(new Date(sendInfo.deliveryTime).format('yyyy/MM/dd'));
                $(".sign-carryDate").html(new Date(sendInfo.deliveryTime).format('yyyy/MM/dd'));
                if(sendInfo.deliveryWay == 'A'){
                    if(sendInfo.deliverySubWay == '1'){
                        $(".sign-sendType").html('快递 - 单独寄出');
                    }else{
                        $(".sign-sendType").html('快递 - 与其他物品共同寄出');
                    }
                    $(".sign-express").removeClass("hd").siblings().addClass("hd");
                }else{
                    $(".sign-carryWay").removeClass("hd").siblings().addClass("hd");
                    $(".sign-sendType").html('随身携带');
                }

                for(var f=0; f<list.length; f++){
                    html +=
                        ' <tr id="'+ list[f].itemId +'" signState="0">' +
                        '    <td>'+ invoiceType(list[f].type) +'</td>' +
                        '    <td>'+ list[f].invoiceNo +'</td>' +
                        '    <td>'+ (list[f].amount).toFixed(2) +'</td>' +
                        '    <td><i class="fa fa-circle-o" onclick="signInvoice($(this),1)"></i></td>' +
                        '    <td><i class="fa fa-circle-o" onclick="signInvoice($(this),2)"></i></td>' +
                        '    <td>' +
                        '        <select disabled="disabled">' +
                        '            <option value="0"></option>' +
                        '            <option value="1">发票中商品单价不对</option>' +
                        '            <option value="2">发票中商品数量不对</option>' +
                        '            <option value="3">发票破损</option>' +
                        '            <option value="4">发票里多商品</option>' +
                        '            <option value="5">发票里少商品</option>' +
                        '            <option value="6">发票抬头不对</option>' +
                        '            <option value="7">发票被客户弄丢</option>' +
                        '            <option value="8">发票由于其他原因丢失</option>' +
                        '            <option value="9">发票中有其他原因</option>' +
                        '        </select>' +
                        '    </td>' +
                        '    <td>' +
                        '        <select disabled="disabled">' +
                        '            <option value="0"></option>' +
                        '            <option value="1">需重新开发票</option>' +
                        '            <option value="2">不予重新开票</option>' +
                        '        </select>' +
                        '    </td>' +
                        '</tr>'
                }
                $(".signSet tbody").html(html);
                $(".signInforEntry input").val("");
                $(".signInforEntry textarea").val("");

                bounce.show($('#signRegister'));
                bounce.everyTime('0.5s','signSet',function () {
                    var kk = 0;
                    var field = 0;
                    var counts = $(".signSet tbody tr").length;
                    var long = $(".signSet tbody .fa-dot-circle-o").length;
                    $(".signSet tbody tr").each(function (){
                        if($(this).children().eq(3).find("i").hasClass("fa-dot-circle-o")){
                            kk++;
                        }
                    });
                   if(counts == kk){
                       $("#signOperate").children().attr("check","1").children("i").attr("class","fa fa-dot-circle-o");
                   }else{
                       $("#signOperate").children().attr("check","0").children("i").attr("class","fa fa-circle-o");
                   }
                   if(long <= 0){
                       field++;
                   }else{
                       $(".signSet tbody tr").each(function (){
                           if($(this).children().eq(4).find("i").hasClass("fa-dot-circle-o")){
                               if($(this).children().eq(5).find("select").val() == '0' || $(this).children().eq(6).find("select").val() == '0'){
                                   field++;
                                   return false;
                               }
                           }
                       });
                   }
                   if(field == 0){
                       if($("#signatory").val() == '' || $("#signDate").val() == ''){
                           field++;
                       }
                   }
                    field > 0 ? $("#signForReceive").prop("disabled", true) : $("#signForReceive").prop("disabled", false);
                });
            }
        }
    });
}
// creator: 李玉婷，2019-03-21 13:21:31，全部正常签收按钮
function sign(obj){
    if(obj.attr("check") == "0"){
        obj.attr("check","1").children("i").attr("class","fa fa-dot-circle-o");
        $(".signSet tr").each(function(){
            $(this).attr("signState","1");
            $(this).find("td").eq(3).children("i").attr("class","fa fa-dot-circle-o");
            $(this).find("td").eq(4).children("i").attr("class","fa fa-circle-o");
            $(this).find("td").eq(5).children("select").prop("disabled",true).children().eq(0).prop("selected",true);
            $(this).find("td").eq(6).children("select").prop("disabled",true).children().eq(0).prop("selected",true);
        })
    }else{
        obj.attr("check","0").children("i").attr("class","fa fa-circle-o");
        $(".signSet tbody tr").each(function(){
            $(this).attr("signState","2");
            $(this).find("td").eq(3).children("i").attr("class","fa fa-circle-o");
        })
    }
}
// creator: 李玉婷，2019-03-21 13:26:28，待签收--签收登记切换
function signInvoice(obj,num){
    if(num == 1){
        obj.parents("tr").attr("signState","1");
        obj.parents("tr").find("td").eq(5).children("select").prop("disabled",true).children().eq(0).prop("selected",true);
        obj.parents("tr").find("td").eq(6).children("select").prop("disabled",true).children().eq(0).prop("selected",true);
    }else{
        obj.parents("tr").attr("signState","2");
        obj.parents("tr").find("td").eq(5).children("select").prop("disabled",false);
        obj.parents("tr").find("td").eq(6).children("select").prop("disabled",false);
    }
    obj.parents("tr").find("i").attr("class","fa fa-circle-o");
    obj.attr("class","fa fa-dot-circle-o");
}
// creator: 李玉婷，2019-03-21 13:27:21，待签收--签收登记提交
function signForSure(){
    var len = $(".signSet tbody tr").length;
    if($("#signatory").val == ""){
        layer.msg("请录入签收人！"); return false ;
    }
    if($("#signDate").val == ""){
        layer.msg("请录入签收日期！"); return false ;
    }
    var deliveryId = $('#signRegister').data('deliveryId');
    var signer = $("#signatory").val();
    var signDate = $("#signDate").val();
    var otherInfo = $("#otherInfo").val();
    var arr = [];
    var group = len;
    var result = 1;
    $(".signSet tbody tr").each(function () {
        var json = {};
        if($(this).attr("signState") == 1){
            var item = $(this).attr("id");
            json = {
                "itemId": item,
                "signResult": $(this).attr("signState")
            }
        }else if($(this).attr("signState") == 2){
            var error1 = $(this).find("td").eq(5).children("select").val();
            var error2 = $(this).find("td").eq(6).children("select").val();
            if(error1 == '' || error1 == 0){
                layer.msg("请选择未签收原因！"); return false ;
            }
            if(error2 == '' || error2 == 0){
                layer.msg("请选择处理办法！"); return false ;
            }
            group--;
            var item = $(this).attr("id");
            json = {
                "itemId": item,
                "signResult": $(this).attr("signState"),
                "signError": error1,
                "signHandle": error2
            }
        }else{
            layer.msg("请选择签收记录！"); return false ;
        }
        arr.push(json);
    });
    arr = JSON.stringify(arr);
    if(group == len){
        result = 1; //全部签收
    }else if(group < len && group > 0){
        result = 2; //未全部签收
    }else if(group == 0){
        result = 3; //全部未签收
    }
    var param = {
        "deliveryId": deliveryId,
        "result": result,
        "sginer": signer,
        "signTime": signDate,
        "signRecord": otherInfo,
        "data": arr
    };
    $.ajax({
        "url": "../invoiceDelivery/signFor.do",
        "data": param,
        success: function (res) {
            var status = res["status"];
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                bounce.cancel();
                if(result === 1){
                    $(".ty-secondTab li:eq(2)").click();
                }else{
                    bounce.show($("#notAllSignTip"));
                }
            }
        }
    });
}
// creator: 李玉婷，2019-04-08 13:27:51，未全部正常签收 确定后提示
function notAllSignTip(){
    bounce.cancel();
    $(".ty-secondTab li:eq(2)").click();
}
// creator: 李玉婷，2019-04-08 13:29:32，修改理由
function updateReason(obj){
    obj.children("i").attr("class","fa fa-dot-circle-o");
    obj.siblings().children("i").attr("class","fa fa-circle-o");
}
// creator: 李玉婷，2019-03-22 13:34:00，发票签收记录列表
function getRecordList(cur){
    $.ajax({
        "url" : "../invoiceDelivery/signForRecordList.do" ,
        "data" : { "currPage" : cur , "pageSize": 20  } ,
        success:function(res) {
            var state = res["state"];
            if(state == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var str = "" , list = res["data"] ;
                var currentPageNo   = res["currPage"],
                    totalPage       = res["totalPage"];

                setPage( $("#ye_issued"), currentPageNo, totalPage, "signedRecord") ;
                if(list && list.length>0){
                    for(var i = 0 ; i < list.length ; i++){
                        var delivery = list[i].delivery;
                        var len = delivery.length;
                        for(var t=0; t<len; t++){
                            var byWay = delivery[t].deliveryWay === 'A' ? '快递': '随身携带';
                            var btnContrl = delivery[t].recordBut ? '': 'disabled';
                            if(t == 0) {
                                str +=
                                    '<tr>' +
                                    '    <td rowspan="'+ len +'">'+ (new Date(list[i].applyDate).format('yyyy/MM/dd hh:mm:ss')) +'</td>' +
                                    '    <td rowspan="'+ len +'">'+ clearNull(list[i].applicantName) +'</td>' +
                                    '    <td rowspan="'+ len +'">'+ clearNull(list[i].customerName) +'</td>' +
                                    '    <td rowspan="'+ len +'">'+ clearNull(list[i].customerCode) +'</td>' +
                                    '    <td rowspan="'+ len +'">'+ (clearNull(list[i].amount)).toFixed(2)	+'</td>' +
                                    '    <td rowspan="'+ len +'">'+ clearNull(list[i].invoiceCount) +'</td>' +
                                    '    <td>'+ byWay +'</td>' +
                                    '    <td>'+ delivery[t].invoiceCount +'</td>' +
                                    '    <td>'+ (new Date(delivery[t].createDate).format('yyyy/MM/dd')) +'</td>' +
                                    '    <td>'+ delivery[t].receiver +'</td>' +
                                    '    <td>'+ signState(delivery[t].signResult) +'</td>' +
                                    '    <td>' +
                                    '        <button class="ty-btn ty-btn-blue ty-circle-3" onclick="signedSee('+ delivery[t].deliveryId +')">查看</button>' +
                                    '        <button class="ty-btn ty-btn-blue ty-circle-3" onclick="signedUpdateReason($(this))">修改</button>' +
                                    '        <button class="ty-btn ty-btn-blue ty-circle-3" onclick="signedRecordList('+ delivery[t].deliveryId +')" '+ btnContrl +'>修改记录</button>' +
                                    '        <span class="hd">'+ delivery[t].deliveryId +'</span>' +
                                    '    </td>' +
                                    '</tr>' ;
                            }else{
                                str +=
                                    ' <tr>' +
                                    '    <td>'+ byWay +'</td>' +
                                    '    <td>'+ delivery[t].invoiceCount +'</td>' +
                                    '    <td>'+ (new Date(delivery[t].createDate).format('yyyy/MM/dd')) +'</td>' +
                                    '    <td>'+ delivery[t].receiver +'</td>' +
                                    '    <td>'+ signState(delivery[t].signResult) +'</td>' +
                                    '    <td>' +
                                    '        <button class="ty-btn ty-btn-blue ty-circle-3" onclick="signedSee('+ delivery[t].deliveryId +')">查看</button>' +
                                    '        <button class="ty-btn ty-btn-blue ty-circle-3" onclick="signedUpdateReason($(this))">修改</button>' +
                                    '        <button class="ty-btn ty-btn-blue ty-circle-3" onclick="signedRecordList('+ delivery[t].deliveryId +')" '+ btnContrl +'>修改记录</button>' +
                                    '        <span class="hd">'+ delivery[t].deliveryId +'</span>' +
                                    '    </td>' +
                                    '</tr>';
                            }
                        }
                    }
                    $("#signRecord tbody").html(str);
                }
            }
        }
    })
}
// creator: 李玉婷，2019-03-18 13:45:01，发票签收记录查看
function signedSee(id){
    $.ajax({
        "url" : "../invoiceDelivery/signForLook.do" ,
        "data" : {"deliveryId":id} ,
        success:function(res){
            var status = res["status"] ;
            var html = '';
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var sendInfo = res["data"];
                var list = sendInfo["list"];
                $(".see-customer").html(sendInfo.customerName);
                $(".see-receiver").html(sendInfo.receiver);
                $(".see-sendBus").html(sendInfo.deliveryCompany);
                $(".see-sendSn").html(sendInfo.deliverySn);
                $(".see-carryer").html(sendInfo.operator);
                $(".see-carryerCall").html(sendInfo.operatorPhone);
                $(".see-signer").html(sendInfo.sginer);
                $(".see-notice").html(sendInfo.memo);
                $(".see-memo").html(sendInfo.signRecord);
                $(".see-sendDate").html(new Date(sendInfo.deliveryTime).format('yyyy/MM/dd'));
                $(".see-carryDate").html(new Date(sendInfo.deliveryTime).format('yyyy/MM/dd'));
                $(".see-signDate").html(new Date(sendInfo.signTime).format('yyyy/MM/dd'));
                if(sendInfo.deliveryWay == 'A'){
                    if(sendInfo.deliverySubWay == '1'){
                        $(".see-sendWay").html('快递 - 单独寄出');
                    }else{
                        $(".see-sendWay").html('快递 - 与其他物品共同寄出');
                    }
                    $(".see-express").removeClass("hd").siblings().addClass("hd");
                }else{
                    $(".see-carryWay").removeClass("hd").siblings().addClass("hd");
                    $(".see-sendWay").html('随身携带');
                }

                for(var f=0; f<list.length; f++){
                    var signed = '',noSgined = '';
                    if(list[f].signResult == '1'){ //正常签收
                        signed   = 'fa-dot-circle-o';
                        noSgined = 'fa-circle-o';
                    }else if(list[f].signResult == '2'){
                        signed   = 'fa-circle-o';
                        noSgined = 'fa-dot-circle-o';
                    }
                    html +=
                        '<tr id="'+ list[f].itemId +'" signState="0">' +
                        '    <td>'+ invoiceType(list[f].type) +'</td>' +
                        '    <td>'+ list[f].invoiceNo +'</td>' +
                        '    <td>'+ (list[f].amount).toFixed(2) +'</td>' +
                        '    <td><i class="fa '+ signed +'"></i></td>' +
                        '    <td><i class="fa '+ noSgined +'"></i></td>' +
                        '    <td>' + signError(list[f].signError) + '</td>' +
                        '    <td>' + signHandle(list[f].signHandle) + '</td>' +
                        '</tr>';
                }
                $(".signRecordSee tbody").html(html);
                bounce.show($('#signedSee'));
            }
        }
    });
}
// creator: 李玉婷，2019-03-22 13:46:12，修改原因
function signedUpdateReason(obj){
    pointObj = obj;
    $("#updateReason i").attr("class","fa fa-circle-o");
    bounce.show($('#signedUpdateReason'));
    bounce.everyTime('0.5s','signedUpdateReason',function () {
        $("#updateReason .fa-dot-circle-o").length == 0 ? $("#signedUpdateBtn").prop("disabled", true) : $("#signedUpdateBtn").prop("disabled", false);
    })
}
// creator: 李玉婷，2019-03-22 13:50:47，发票签收记录 - 修改
function signedUpdate() {
    var org = $("#updateReason .fa-dot-circle-o").attr("value");
    var id = pointObj.siblings(".hd").html();
    if(org == '1'){
        $.ajax({
            "url": "../invoiceDelivery/updateAllNotSignFor.do",
            "data": {"deliveryId": id},
            success: function () {
                bounce.cancel();
                $(".ty-secondTab li:eq(2)").click();
            }
        });
    }else if(org == '2'){
        $.ajax({
            "url" : "../invoiceDelivery/signForLook.do" ,
            "data" : {"deliveryId":id} ,
            success:function(res){
                var status = res["status"] ;
                var html = '';
                if(status == 0){
                    layer.msg("获取数据失败！"); return false ;
                }else{
                    var sendInfo = res["data"];
                    var list = sendInfo["list"];
                    $(".signUp-customer").html(sendInfo.customerName);
                    $(".signUp-receiver").html(sendInfo.receiver);
                    $(".signUp-sendBus").html(sendInfo.deliveryCompany);
                    $(".signUp-sendSn").html(sendInfo.deliverySn);
                    $(".signUp-carryer").html(sendInfo.operator);
                    $(".signUp-carryerCall").html(sendInfo.operatorPhone);
                    $("#signUp-signer").val(sendInfo.sginer);
                    $(".signUp-memo").html(sendInfo.memo);
                    $("#signUp-other").val(sendInfo.signRecord);
                    $(".signUp-sendDate").html(new Date(sendInfo.deliveryTime).format('yyyy/MM/dd'));
                    $(".signUp-carryDate").html(new Date(sendInfo.deliveryTime).format('yyyy/MM/dd'));
                    $("#signDateUpdate").val(new Date(sendInfo.signTime).format('yyyy/MM/dd'));
                    if(sendInfo.deliveryWay == 'A'){
                        if(sendInfo.deliverySubWay == '1'){
                            $(".signUp-sendWay").html('快递 - 单独寄出');
                        }else{
                            $(".signUp-sendWay").html('快递 - 与其他物品共同寄出');
                        }
                        $(".signUp-express").removeClass("hd").siblings().addClass("hd");
                    }else{
                        $(".signUp-carryWay").removeClass("hd").siblings().addClass("hd");
                        $(".signUp-sendWay").html('随身携带');
                    }

                    for(var f=0; f<list.length; f++){
                        var signed = '',noSgined = '';
                        if(list[f].signResult == '1'){ //正常签收
                            signed   = 'fa-dot-circle-o';
                            noSgined = 'fa-circle-o';
                        }else if(list[f].signResult == '2'){
                            signed   = 'fa-circle-o';
                            noSgined = 'fa-dot-circle-o';
                        }
                        html +=
                            ' <tr id="'+ list[f].itemId +'" signState="'+ list[f].signResult +'">' +
                            '    <td>'+ invoiceType(list[f].type) +'</td>' +
                            '    <td>'+ list[f].invoiceNo +'</td>' +
                            '    <td>'+ (list[f].amount).toFixed(2) +'</td>' +
                            '    <td><i class="fa '+ signed +'" onclick="signInvoice($(this),1)"></i></td>' +
                            '    <td><i class="fa '+ noSgined +'" onclick="signInvoice($(this),2)"></i></td>' +
                            '    <td>' + signErrorOption(list[f].signResult,list[f].signError) +
                            '    </td>' +
                            '    <td>' + signHandleOption(list[f].signResult,list[f].signHandle) +
                            '    </td>' +
                            '</tr>';
                    }
                    $("#signedUpdate tbody").html(html);
                    $(".signInforEntryEidt input").html(html);
                    $(".signInforEntryEidt textarea").html(html);
                    bounce.show($('#signRegisterUpdate'));
                    bounce.everyTime('0.5s','signRegisterUpdate',function () {
                        var kk = 0;
                        var field = 0;
                        var long = $("#signedUpdate tbody .fa-dot-circle-o").length;
                        $("#signedUpdate tbody tr").each(function (){
                            if($(this).children().eq(3).find("i").hasClass("fa-dot-circle-o")){
                                kk++;
                            }
                        });
                        if(long <= 0){
                            field++;
                        }else{
                            $("#signedUpdate tbody tr").each(function (){
                                var yy = $(this).children().eq(4).find(".fa-dot-circle-o").length;
                                if(yy > 0){
                                    if($(this).children().eq(5).find("select").val() == '0' || $(this).children().eq(6).find("select").val() == '0'){
                                        field++;
                                        return false;
                                    }
                                }
                            });
                        }
                        if(field == 0){
                            if($("#signUp-signer").val() == '' || $("#signDateUpdate").val() == ''){
                                field++;
                            }
                        }
                        field > 0 ? $("#signedUpdateSure").prop("disabled", true) : $("#signedUpdateSure").prop("disabled", false)
                    })
                }
            }
        });
    }
}
// creator: 李玉婷，2019-03-22 13:51:27，发票签收记录 - 修改确定
function signedUpdateSure(){
    if($("#signUp-signer").val == ""){
        layer.msg("请录入签收人！"); return false ;
    }
    if($("#signDateUpdate").val == ""){
        layer.msg("请录入签收日期！"); return false ;
    }
    var deliveryId = pointObj.siblings(".hd").html();
    var signer = $("#signUp-signer").val();
    var signDate = $("#signDateUpdate").val();
    var otherInfo = $("#signUp-other").val();
    var arr = [];
    var len = $("#signedUpdate tbody tr").length;
    var group = len;
    var result = 1;
    $("#signedUpdate tbody tr").each(function () {
        var json = {};
        if($(this).attr("signState") == 1){
            var item = $(this).attr("id");
            json = {
                "itemId": item,
                "signResult": $(this).attr("signState")
            };
        }else if($(this).attr("signState") == 2){
            var error1 = $(this).find("td").eq(5).children("select").val();
            var error2 = $(this).find("td").eq(6).children("select").val();
            if(error1 == '' || error1 == 0){
                layer.msg("请选择未签收原因！"); return false ;
            }
            if(error2 == '' || error2 == 0){
                layer.msg("请选择处理办法！"); return false ;
            }

            var item = $(this).attr("id");
            json = {
                "itemId": item,
                "signResult": $(this).attr("signState"),
                "signError": error1,
                "signHandle": error2
            };
            group--;
        }else{
            layer.msg("请选择签收记录！"); return false ;
        }
        arr.push(json);
    })
    arr = JSON.stringify(arr);
    if(group == len){
        result = 1;
    }else if(group < len && group > 0){
        result = 2;
    }else if(group == 0){
        result = 3;
    }
    var param = {
        "deliveryId": deliveryId,
        "result": result,
        "sginer": signer,
        "signTime": signDate,
        "signRecord": otherInfo,
        "data": arr
    };
    $.ajax({
        "url": "../invoiceDelivery/signForUpdate.do",
        "data": param,
        success: function (res) {
            var status = res["status"];
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                bounce.cancel();
                $(".ty-secondTab li:eq(2)").click();
            }
        }
    });
}
// creator: 李玉婷，2019-03-22 13:52:11，发票签收记录 - 修改记录列表
function signedRecordList(id){
    $.ajax({
        "url": "../invoiceDelivery/signForUpdateRecordList.do",
        "data": {"deliveryId": id},
        success: function (res) {
            var status = res["status"];
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var str = ''
                var list = res["list"];
                for(var t=0; t<list.length; t++){
                    str +='<tr>' +
                        '    <td>'+ (new Date(list[t].updateDate).format('yyyy/MM/dd hh:mm:ss')) +'</td>' +
                        '    <td>'+ list[t].updateName +'</td>' +
                        '    <td class="ty-td-control"><span class="ty-color-blue" onclick="signedUpdateRecord('+ list[t].recordId +', '+ list[t].isNew +', 1)">查看</span></td>' +
                        '    <td><span class="ty-color-blue" onclick="signedUpdateRecord('+ list[t].recordId +', '+ list[t].isNew +', 2)">查看</span></td>' +
                        '</tr>';
                }
                $("#signedRecordList tbody").html(str);
                bounce.show($("#signedUpdateRecord"));
            }
        }
    });
}
// creator: 李玉婷，2018-12-27 13:52:22，发票签收记录 - 修改记录查看
function signedUpdateRecord(id, isNew, num){
    $.ajax({
        "url": "../invoiceDelivery/signForUpdateRecordLook.do",
        "data": {
            "recordId": id,
            "isNew": isNew
        },
        success: function (res) {
            var status = res["status"];
            if(status == 0){
                layer.msg("获取数据失败！"); return false ;
            }else{
                var before = res["before"];
                var after = res["after"];
                var html = '';
                var record = {}, tempData = {};
                if(num == 1){
                    record = before;
                    tempData = after;
                    $("#editLook").html('发票签收记录修改前');
                }else{
                    record = after;
                    tempData = before;
                    $("#editLook").html('发票签收记录修改后');
                }
                $(".record-customer").html(record.customerName);
                $(".record-receiver").html(record.receiver);
                $(".record-sendBus").html(record.deliveryCompany);
                $(".record-sendSn").html(record.deliverySn);
                $(".record-carryer").html(record.operator);
                $(".record-carryerCall").html(record.operatorPhone);
                $(".record-memo").html(record.memo);
                $(".record-signer").html(record.sginer);
                $(".record-other").html(record.signRecord);
                $(".record-sendDate").html(new Date(record.deliveryTime).format('yyyy/MM/dd'));
                $(".record-carryDate").html(new Date(record.deliveryTime).format('yyyy/MM/dd'));
                $(".record-signDate").html(new Date(record.signTime).format('yyyy/MM/dd'));
                if(record.deliveryWay == 'A'){
                    if(record.deliverySubWay == '1'){
                        $(".record-sendWay").html('快递 - 单独寄出');
                    }else{
                        $(".record-sendWay").html('快递 - 与其他物品共同寄出');
                    }
                    $(".record-expressWay").removeClass("hd").siblings().addClass("hd");
                }else{
                    $(".record-carryWay").removeClass("hd").siblings().addClass("hd");
                    $(".record-sendWay").html('随身携带');
                }

                var flagClass = 'flagBg';
                var recordItems = record.items;
                var tempDataItems = tempData.items;
                for(var t=0; t<recordItems.length; t++){
                    flagClass = 'flagBg';
                    for(var i=0; i<tempDataItems.length; i++){
                        if(recordItems[t].invoice === tempDataItems[i].invoice){
                            flagClass = '';
                        }
                    }
                    if(flagClass == ''){
                        conCompare(recordItems[t].signResult,tempDataItems[t].signResult) ? flagClass = '':flagClass = 'flagBg';
                    }
                    if(flagClass == ''){
                        conCompare(recordItems[t].signError,tempDataItems[t].signError) ? flagClass = '':flagClass = 'flagBg';
                    }
                    if(flagClass == ''){
                        conCompare(recordItems[t].signHandle,tempDataItems[t].signHandle) ? flagClass = '':flagClass = 'flagBg';
                    }
                    var signed = '',noSgined = '';
                    if(recordItems[t].signResult == '1'){ //正常签收
                        signed   = 'fa-dot-circle-o';
                        noSgined = 'fa-circle-o';
                    }else if(recordItems[t].signResult == '2'){
                        signed   = 'fa-circle-o';
                        noSgined = 'fa-dot-circle-o';
                    }
                    html +=
                        '<tr class="'+ flagClass +'">' +
                        '    <td>'+ invoiceType(recordItems[t].type) +'</td>' +
                        '    <td>'+ recordItems[t].invoiceNo +'</td>' +
                        '    <td>'+ (recordItems[t].amount).toFixed(2) +'</td>' +
                        '    <td><i class="fa '+ signed +'"></i></td>' +
                        '    <td><i class="fa '+ noSgined +'"></i></td>' +
                        '    <td>' + signError(recordItems[t].signError) + '</td>' +
                        '    <td>' + signHandle(recordItems[t].signHandle) + '</td>' +
                        '</tr>';
                }
                conCompare(before.sginer, after.sginer) ? $(".record-signer").removeClass("flag") : $(".record-signer").addClass("flag");
                conCompare(before.signTime, after.signTime) ? $(".record-signDate").removeClass("flag") : $(".record-signDate").addClass("flag");
                conCompare(before.signRecord, after.signRecord) ? $(".record-other").removeClass("flag") : $(".record-other").addClass("flag");
                $(".signedRecordSee tbody").html(html);
                bounce_Fixed.show($('#signedUpdateRecordSee'));
            }
        }
    });
}
//create:lyt date:2019/3/15 发票类型
function invoiceType(num){
    var str = '';
    if(num == '1'){
        str = '增值税专用发票';
    }else if(num == '2'){
        str = '增值税普通发票';
    }else if(num == '3'){
        str = '其他普通发票';
    }
    return str;
}
// creator: 李玉婷，2019-03-22 13:54:28，未签收原因
function signError(num) {
    var str = '';
    switch (num) {
        case 1: ;
        case '1': str = '发票中有商品单价不对';break;
        case 2: ;
        case '2': str = '发票中有商品数量不对';break;
        case 3: ;
        case '3': str = '发票破损';break;
        case 4: ;
        case '4': str = '发票里多商品';break;
        case 5: ;
        case '5': str = '发票里少商品';break;
        case 6: ;
        case '6': str = '发票抬头不对';break;
        case 7: ;
        case '7': str = '发票被客户弄丢';break;
        case 8: ;
        case '8': str = '发票由于其他原因丢失';break;
        case 9: ;
        case '9': str = '发票中有其他原因';break;
    }
    return str;
}
// creator: 李玉婷，2019-03-22 13:54:40，输出相应字符串
function signHandle(num) {
    var str = '';
    switch (num) {
        case 1: ;
        case '1': str = '需重新开发票';break;
        case 2: ;
        case '2': str = '不予重新开发票';break;
    }
    return str;
}
//create:lyt date:2019/3/20 修改记录比较
function conCompare(strA, strB){
    var result = false;
    if(strA == strB){
        result = true;
    }
    return result;
}
//create:lyt date:2019/4/4 输出签收状态
function signState(type){
    var str = '';
    switch(type){
        case '1': str = '已全部正常签收';break;
        case '2': str = '未全部正常签收';break;
        case '3': str = '未能正常签收';break;
    }
    return str;
}
// creator: 李玉婷，2019-04-4 13:56:26，初始化未签收原因
function signErrorOption (state,num){
    var noUse = state == 1 ? 'disabled': '';
    var html =
        '<select '+ noUse +'>' +
        '    <option value="0"></option>';
    for(var a=1;a<=9;a++){
        if(a == num){
            html += '<option value="'+ a +'" selected>'+ signError(a) +'</option>';
        }else{
            html += '<option value="'+ a +'">'+ signError(a) +'</option>';
        }
    }
    html += '</select>';
    return html;
}
// creator: 李玉婷，2019-04-4 13:57:57，初始化
function signHandleOption (state,num){
    var noUse = state == 1 ? 'disabled': '';
    var html =
        '<select '+ noUse +'>' +
        '    <option value="0"></option>';
    for(var a=1;a<=2;a++){
        if(a == num){
            html += '<option value="'+ a +'" selected>'+ signHandle(a) +'</option>';
        }else{
            html += '<option value="'+ a +'">'+ signHandle(a) +'</option>';
        }
    }
    html += '</select>';
    return html;
}
//处理null
function clearNull(str) {
    var result = str === null || str === 'null' || str === undefined ? '--':str;
    return result;
}




laydate.render({elem: '#mailDate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#carryDate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#mailDateUpdate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#carryDateUpdate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#signDate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#signDateUpdate',format: 'yyyy/MM/dd'});







