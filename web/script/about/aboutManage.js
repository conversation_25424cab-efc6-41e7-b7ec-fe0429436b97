$(function () {
    // updater : 侯杏哲 2017-11-18 每一级目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容）
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        let treeName = $(this).parents(".ty-colFileTree").data("name")
        let hasChildren = Number($(this).attr('children')) > 0
        let thisLevel = Number($(this).attr("level"))

        //添加文件夹选中样式
        $(this).parents(".ty-colFileTree").find(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");

        var categoryId = $(this).data("id") ;
        var code = $(this).attr("code") ;
        //点击文件夹箭头方向的切换
        if (hasChildren) {
            $(this).find("i").eq(0).toggleClass("fa-angle-down")
            //获取本级与子级文件夹内容

            let pms_childFolder = new Promise(resolve => {
                let url = thisLevel === 0 ? '../about/getAboutMpApplicationDirectory.do' : '../about/getAboutDirectoryAndChildDirection.do'
                let data = thisLevel === 0 ? { }: (!categoryId?{code: code}: { category:categoryId })
                $.ajax({
                    url: url,
                    data: data,
                    success: function (res) {
                        if (res) {
                            resolve(res.data)
                        }
                    }
                })
            })
            // 右侧内容处理
            let treeItemThis = $(this)
            pms_childFolder.then(res => {
                let thisFolder = {}, childFolder = [];
                if (thisLevel === 0 ) {
                    childFolder = res.listFirstFolder
                } else {
                    childFolder = res.childFolder
                    if (thisLevel === 1 && (treeName === 'chooseSaveFolder' || treeName === 'moveTo')) {
                        delete childFolder[2]
                        delete childFolder[3]
                    }
                }


                // 左侧文件夹填充子级内容
                let level = Number(treeItemThis.attr("level")) + 1
                let childFolderStr = setFolderStr(childFolder, level)
                treeItemThis.next().remove()
                if (treeItemThis.children(".fa-angle-down").length > 0) {
                    treeItemThis.after(childFolderStr)
                }
            })
            if (treeName === 'main') {
                // 非末级文件夹不显示文件列表
                $(".section_right").hide()
            }
        } else {

            if (treeName === 'main') {
                // 末级文件夹显示文件列表
                $(".section_right").show()
                getTxtList( 1,20, categoryId);
            }
        }
    });

    $(".page_main header").on("click", "[type='btn']", function () {
        let name= $(this).data("name")
        switch (name) {
            // 上传内容
            case 'txtUpload':
                // 表单初始化
                $("#needOther").prop("checked", true)
                $(".txtApply input.ty-inputText").val("").prop("disabled", false)
                $(".txtApply textarea").val("")
                $(".txtApply .clearInput").show()
                $(".txtApply .formInput").show("").find(".savePlace").hide().html("")
                $(".txtApply .formShow").hide("")
                $(".txtApply .savePlace").html("")
                // 显示
                $(".page_contentApply").show().siblings(".page").hide()
                getApprover()

                break;

        }
    })

    $(".ty-container").on("click", ".ty-fileItem [type='btn']", function () {
        if ($(this).attr("disable") === 'true') {
            return false
        }
        let name= $(this).data("name")
        let id = $(this).parents(".ty-fileItem").data("id")
        let thisFileName = $(this).parents(".ty-fileItem").find(".ty-fileName").html();
        let thisFileNo = $(this).parents(".ty-fileItem").find(".ty-fileNo").html();

        // 设置选中
        $(".ty-fileItem").removeClass("ty-fileItemActive");
        $(this).parents(".ty-fileItem").addClass("ty-fileItemActive");

        switch (name) {
            case 'see':
                // 查看
                getTxtDetail(id).then(data => {
                    let res = data.aboutFile || data.aboutFileHistory
                    let approvalProcess = data.listAp
                    $(".txtContent pre").html(res.content)
                    $(".txtShow").show().siblings(".main").hide()
                })

                break
            // 换版内容
            case 'txtChangeVersion':
            case 'txtChangeVersionApply':
                // 表单初始化
                $(".txtApply input[require]").val("").prop("disabled", true)
                $(".txtApply textarea").val("")
                $(".txtApply .clearInput").hide()
                $(".txtApply .formInput").hide("")
                $(".txtApply .formShow").show("")
                $("#needOther").prop("checked", true)
                getApprover()
                // 赋值数据
                getTxtDetail(id).then(data => {
                    let res = data.aboutFile
                    let approvalProcess = data.listAp
                    $(".txtApply input[name='name']").val(res.name)
                    $(".txtApply input[name='fileSn']").val(res.fileSn)

                    let str = '<i class="fa fa-folder"></i> '
                    $(".txtApply .savePlace").html(str + res.allCategoryName)
                })
                $(".page_contentApply").data("fileId", id)
                // 显示隐藏
                $(".page_contentApply").show().siblings(".page").hide()
                break;
            case 'basicMsg':
                getTxtDetail(id).then(data => {
                    let res = data.aboutFile
                    let approvalProcess = data.listAp
                    if ($(".page_fileHistory").is(":visible")) {
                        res = data.aboutFileHistory
                    }
                    let reason      = res.reason,
                        view_num    = res.viewNum,         // 浏览次数
                        move_num    = res.moveNum,         // 移动次数
                        upName      = res.upNameNum,   // 文件名称修改次数
                        upFileSn    = res.upFileSnNum // 文件编号修改次数

                    var history = $(".page_fileHistory").is(":visible") // 是否是换版记录中的文件

                    typeof (reason) === "undefined" ? reason = "--":reason;

                    $("#info_title").html(res.name);

                    // left
                    $("#info_sn").html(res.fileSn);
                    $("#info_category").html(res.allCategoryName);

                    // right
                    let updateTime = res.updateTime || res.createTime
                    $("#info_gn").html("G" + res.versionNo);
                    $("#info_createName").html(res.createName);
                    $("#info_createDate").html(moment(updateTime).format("YYYY-MM-DD HH:mm:ss"));

                    if( res.versionNo > 0){ // 换过版
                        $("#docInfoScan .info_content .ttl").html("换版原因：")
                        $("#info_description").html(reason);
                    }else{
                        $("#docInfoScan .info_content .ttl").html("说明：")
                        $("#info_description").html(res.description);
                    }

                    if (approvalProcess.length > 0) {
                        var approveStr = '<div class="trItem"><span class="ttl">申请人：</span><span class="con" style="max-width: 280px"><span class="info_name">' + approvalProcess[0].userName + '</span><span>' + formatTime(approvalProcess[0].createDate, true) +'</span></div>'
                        // 审批流程
                        for (var i = 0; i < approvalProcess.length; i++) {
                            var name = ''
                            if ( i === approvalProcess.length - 1) {
                                name = '文 管'
                            } else {
                                name = '审批人'
                            }
                            approveStr += '<div class="trItem"><span class="ttl">' + name + '：</span><span class="con" style="max-width: 280px"><span class="info_name">' + approvalProcess[i].toUserName + '</span><span>' + formatTime(approvalProcess[i].handleTime, true) +'</span></div>'
                        }
                        $("#docInfoScan .processList").html(approveStr)
                    } else {
                        $("#docInfoScan .processList").html("")
                    }


                    view_num !== 0  ? $("#viewNumBtn").prop("disabled",false)       :$("#viewNumBtn").prop("disabled",true);
                    move_num === 0  ? $("#moveNumBtn").prop("disabled",true)        :$("#moveNumBtn").prop("disabled",false);
                    upName === 0    ? $("#changeNameNumBtn").prop("disabled",true)  :$("#changeNameNumBtn").prop("disabled",false);
                    upFileSn === 0  ? $("#changeNoNumBtn").prop("disabled",true)    :$("#changeNoNumBtn").prop("disabled",false);
                    $("#docInfoScan .censusInfo .view_num").html(view_num);
                    $("#docInfoScan .censusInfo .move_num").html(move_num);
                    $("#docInfoScan .censusInfo .name_num").html(upName);
                    $("#docInfoScan .censusInfo .no_num").html(upFileSn);

                    if (history) {
                        $("#docInfoScan .censusInfo").hide()
                    } else {
                        $("#docInfoScan .censusInfo").show()
                    }
                    bounce.show($("#docInfoScan")) ;
                })
                break
            case 'changeVersionRecord': // 换版记录
                $(".page_fileHistory").show().siblings(".page").hide()
                $(".page_fileHistory").data("fileId", id)
                getAboutFileRecord(1, 20)
                break
            case 'move':
                $("#moveFile .bounce_title").html("移动到")
                bounce_Fixed.show($("#moveFile"));
                $(".ty-colFileTree[data-name='moveTo']").html('<ul>' +
                        '    <li>' +
                        '        <div class="ty-treeItem ty-treeItemActive" title="全部" children="4" level="0" parent="null" directoryAble="false">' +
                        '            <i class="fa fa-angle-right"></i>' +
                        '            <i class="fa fa-folder"></i>' +
                        '            <span>全部</span>' +
                        '        </div>' +
                        '    </li>' +
                        '</ul>')
                $(".ty-colFileTree[data-name='moveTo'] .ty-treeItem").eq(0).click().hide()
                $("#moveFile").data("id", id)
                break
            case 'changeFileName':
                $("#changeFileName .changeFileName").val("");
                bounce.show($("#changeFileName"));
                $("#changeFileName").find(".currentFileName").html(thisFileName);
                $("#changeFileName").data("currentFileId",id);
                bounce.everyTime('0.2s','changeFileName',function(){
                    var changeFileName = $("#changeFileName .changeFileName").val();
                    if(changeFileName === ""){
                        $("#changeFileNameBtn").prop("disabled",true)
                    }else{
                        $("#changeFileNameBtn").prop("disabled",false)
                    }
                });
                break
            case 'changeFileSn':
                $("#changeFileNo .changeFileNo").val("");
                bounce.show($("#changeFileNo"));
                $("#changeFileNo").find(".currentFileNo").html(thisFileNo.substring(4,thisFileNo.length));
                $("#changeFileNo").data("currentFileId",id);
                bounce.everyTime('0.2s','changeFileNo',function(){
                    var changeFileName = $("#changeFileNo .changeFileNo").val();
                    if(changeFileName === ""){
                        $("#changeFileNoBtn").prop("disabled",true)
                    }else{
                        $("#changeFileNoBtn").prop("disabled",false)
                    }
                });
                break
        }
    })

    /* creator：张旭博，2017-05-06 15:17:01，每一个文件绑定事件（添加样式、获取文件详细信息、） */
    $(".ty-container").on("click",".ty-fileItem",function (){
        $(".ty-fileItem").removeClass("ty-fileItemActive");
        $(this).addClass("ty-fileItemActive");
    });

    $(".page_contentApply, .page_fileHistory, .page_search").on("click", "[type='btn']", function () {
        var name= $(this).data("name")
        switch (name) {
            case 'back':
                $(".page_main").show().siblings(".page").hide()

                break
            case 'sureTxtUpload':
                //获取表单内容和接口需要的参数

                let isChangeVersion = $(".txtApply [name='name']").prop("disabled")
                let data = {}, url=''
                if (isChangeVersion) {
                    data = {
                        file: $(".page_contentApply").data("fileId"),
                        content : $(".txtApply [name='content']").val(),
                        reason : $(".txtApply [name='reason']").val(),
                        type: 1, // 新增类型 固定传1（直接新增）
                        approveStatus: 2, // 审批状态 固定传2（批准状态）
                        isNeedOther: false
                    }
                    data.isNeedOther = $(".txtApply input:radio:checked").val()
                    if (data.isNeedOther === '1') {
                        // 选择了审批人
                        data.type = 2
                        data.approveStatus = 1
                        data.auditName = $(".chooseApprover").find("option:selected").html()
                        data.auditor = $(".chooseApprover").val()
                    } else {
                        // 由文管直接发布
                        if (isGeneral) {
                            data.type = 1
                            data.approveStatus = 2
                        } else {
                            data.type = 2
                            data.approveStatus = 1
                        }
                    }
                    url = '/about/aboutChangeFileVersion.do'
                    if ($.trim($(".txtApply [name='content']").val()) === '') {
                        layer.msg("请输入内容！")
                        return false
                    }
                    if (!data.isNeedOther) {
                        layer.msg("请勾选是否需要审批！")
                        return false
                    }
                    if (data.isNeedOther === '1' &&  $(".chooseApprover").val() === '') {
                        layer.msg("请选择审批人！")
                        return false
                    }
                } else {
                    data = {
                        category: $(".txtApply .savePlace").attr("categoryId"),
                        name : $(".txtApply [name='name']").val(),
                        fileSn : $(".txtApply [name='fileSn']").val(),
                        content : $(".txtApply [name='content']").val(),
                        description : $(".txtApply [name='description']").val(),
                        type: 1, // 新增类型 固定传1（直接新增）
                        approveStatus: 2, // 审批状态 固定传2（批准状态）
                        isNeedOther: false
                    }
                    data.isNeedOther = $(".txtApply input:radio:checked").val()
                    if (data.isNeedOther === '1') {
                        // 选择了审批人
                        data.type = 2
                        data.approveStatus = 1
                        data.auditName = $(".chooseApprover").find("option:selected").html()
                        data.auditor = $(".chooseApprover").val()
                    } else {
                        // 由文管直接发布
                        if (isGeneral) {
                            data.type = 1
                            data.approveStatus = 2
                        } else {
                            data.type = 2
                            data.approveStatus = 1
                        }
                    }
                    url = '/about/aboutAffirdFile.do'
                    if ($.trim(data.name) === '') {
                        layer.msg("请输入文件名称！")
                        return false
                    }
                    if ($.trim(data.fileSn) === '') {
                        layer.msg("请输入文件编号！")
                        return false
                    }
                    if ($.trim(data.content) === '') {
                        layer.msg("请输入内容！")
                        return false
                    }
                    if ($.trim($(".txtApply .savePlace").html()) === '') {
                        layer.msg("请选择保存位置！")
                        return false
                    }
                    if (!data.isNeedOther) {
                        layer.msg("请勾选是否需要审批！")
                        return false
                    }
                    if (data.isNeedOther === '1' &&  $(".chooseApprover").val() === '') {
                        layer.msg("请选择审批人！")
                        return false
                    }
                }

                console.log(data)
                $.ajax({
                    url: url,
                    data: data,
                    success: function (res) {
                        let data = res.data
                        let status = Number(data.status)
                        if (status === 1) {
                            layer.msg("操作成功")
                            $(".page_main").show()
                            $(".page_contentApply").hide()
                            if (isGeneral && data.isNeedOther !== '1') {
                                $(".ty-colFileTree[data-name='main'] .ty-treeItemActive").click()
                                $(".ty-colFileTree[data-name='main'] .ty-treeItemActive").click()
                            }
                            if (isChangeVersion) {
                                $(".ty-fileItemActive:visible").find("[data-name='txtChangeVersionApply']").attr("disable", 'true')
                            }
                        } else if (status === 2) {
                            if (isChangeVersion) {
                                layer.msg("已有文件正在换版现在不能换版!")
                            } else {
                                layer.msg("已上传文件中有编号重复的，请检查确认!")
                            }

                        } else if (status === 3) {
                            layer.msg("文件夹不存在!")
                        } else {
                            $("#mtTip #mt_tip_ms").html("操作失败!") ;
                            bounce.show($("#mtTip"));
                        }
                    }
                })
                break
            case 'chooseSaveFolder':
                $("#chooseSaveFolder .bounce_title").html("选择保存位置")
                bounce_Fixed.show($("#chooseSaveFolder"));
                let initStr =   '<ul>' +
                    '    <li>' +
                    '        <div class="ty-treeItem" title="全部" children="2" level="0" parent="null" directoryAble="false">' +
                    '            <i class="fa fa-angle-right"></i>' +
                    '            <i class="fa fa-folder"></i>' +
                    '            <span>全部</span>' +
                    '        </div>' +
                    '    </li>' +
                    '</ul>'
                $(".ty-colFileTree[data-name='chooseSaveFolder']").html(initStr)
                $(".ty-colFileTree[data-name='chooseSaveFolder'] .ty-treeItem").eq(0).click().hide()
                // setEveryTime(bounce_Fixed, 'chooseSaveFolder');
                break;
        }
    })
    $(".txtShow").on("click", "[type='btn']", function (){
        var name= $(this).data("name")
        switch (name) {
            case 'back':
                $(".main").show().siblings(".txtShow").hide()
                break
        }
    })

    $(".bounce_Fixed").on("click", "[type='btn']", function () {
        let name= $(this).data("name")
        let thisNode = $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive")
        let thisCategoryId = thisNode.data("id")
        let chooseNode, categoryId
        switch (name) {
            case 'sureChooseFolder':
                chooseNode = $(".ty-colFileTree[data-name='chooseSaveFolder']").find(".ty-treeItemActive");
                categoryId = chooseNode.data("id");
                // 先判断文件夹是否设置了权限，没有就不能确定
                if (!categoryId) {
                    layer.msg("请选择目标文件夹")
                    return false
                }

                if (chooseNode.attr("children") > 0) {
                    layer.msg("该类别下存在子类别，请选择该子类别或其他类别")
                    return false
                }

                var folderNameObj = chooseNode.parents("li").children(".ty-treeItem").children("span")
                var str = '<'
                folderNameObj.each(function (index) {
                    var name = $(this).text()
                    if(index === 0){
                        str = '<span class="ty-color-red">'+name+'</span>'
                    } else {
                        str = name + ' / ' + str
                    }
                })
                str = '<i class="fa fa-folder"></i>' + str
                $(".txtApply .savePlace").show().html(str)
                $(".txtApply .savePlace").attr("categoryId", categoryId)
                bounce_Fixed.cancel()
                break;
            case 'sureMoveFile':
                chooseNode = $(".ty-colFileTree[data-name='moveTo']").find(".ty-treeItemActive");
                categoryId = chooseNode.data("id");
                // 先判断文件夹是否设置了权限，没有就不能确定
                if (!categoryId) {
                    layer.msg("请选择目标文件夹")
                    return false
                }

                if (thisCategoryId === categoryId) {
                    layer.msg("不能移动到当前文件夹")
                    return false
                }

                if (chooseNode.attr("children") > 0) {
                    layer.msg("该类别下存在子类别，请选择该子类别或其他类别")
                    return false
                }
                var fileId      =  $("#moveFile").data("id");
                $.ajax({
                    url:"../about/aboutMoveFile.do",
                    data:{ id:fileId, category : categoryId},
                    success: function(res){
                        var data = res.data
                        var state = data.state
                        if(state === 1 ){
                            bounce_Fixed.cancel();
                            layer.msg("操作成功");
                            $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive").click()
                        } else if (state === 2) {
                            layer.msg("目标目录和原目录在同一级");
                        } else if (state === 3) {
                            layer.msg("目标目录不存在");
                        } else {
                            layer.msg("操作失败");
                        }
                    }
                });
                break

        }
    })

    // 文件-更多按钮点击事件
    $(".ty-container").on("click","[data-name='more']",function (e) {
        $(".hel").hide()
        $(this).next(".hel").show();
        e.stopPropagation()
    });
    // 点击body隐藏更多
    $("body").on("click",function () {
        $(".hel").hide()
    });
    // × 点击事件
    $(".clearInput").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            $(this).prev().focus();
        }
    });

    $("#advancedSearchBtn").on("click",function () {
        $("#searchSort").hide()
        $("#fileNameOrSn").val("");
        var type = $("#advancedSearch").data("type");
        type === 0 ? advancedSearch_file(1,20): advancedSearch_folder(1,20);
    })

    $("#advancedSearch .ty-secondTab li").on("click",function () {
        var index = $(this).index();
        $(this).addClass("ty-active").siblings('li').removeClass("ty-active");
        $("#advancedSearch .searchCon").eq(index).show().siblings(".searchCon").hide();
        $("#advancedSearch").data("type",index);
    })
    /* creator：张旭博，2017-05-06 15:17:01，为高级搜索 按文件类别搜索 的文件夹绑定事件（添加样式、获取自己文件夹信息、） */
    $(".page_search").on("click",".ty-folderItem",function (){
        $(this).addClass("ty-folderItemActive").siblings().removeClass("y-folderItemActive");
        var folderId        = $(this).attr("id"),
            folderParent    = $(this).find(".ty-folderParent").html(),
            folderName      = $(this).find(".ty-folderName").html(),
            nextFolderParent= '';
        folderParent === '' ? nextFolderParent = folderName :nextFolderParent = folderParent + '/' + folderName;
        //获取子级文件或者文件夹
        getChildFolder(folderId,nextFolderParent);
    });

    $("#aboutTree .ty-treeItem").eq(0).click()
})

// creator: 张旭博，2018-04-24 10:17:16，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        if ($(".page_main").is(":visible")) {
            $("#ye_con").show() ;
            $("#fileSort").show() ;
        }
        for(var i = 0; i < fileInfo.length; i++){
            var id          = fileInfo[i].id,
                file    = fileInfo[i].file,
                category    = fileInfo[i].category,
                name        = fileInfo[i].name,
                fileSn      = fileInfo[i].fileSn,
                versionNo   = fileInfo[i].versionNo,
                operation   = fileInfo[i].operation,
                updateName  = fileInfo[i].updateName,
                createName  = fileInfo[i].createName,
                updateDate  = fileInfo[i].updateTime,
                createDate  = fileInfo[i].createTime,
                updator  = fileInfo[i].updator,
                creator  = fileInfo[i].creator,
                applyDisabledClass = '',    //定义换版申请禁用class
                recordDisabledClass = '',   //定义换版记录禁用class
                handleStr = ''             //定义操作按钮部分字符串


            updateName  = updateName ||createName;   //更新人为空时 赋值 创建人，最后统一取更新时间 ， 下同
            updateDate  = moment(updateDate || createDate).format("YYYY-MM-DD HH:mm:ss");
            if (!updator) {updator = creator}

            operation   === '3' ? applyDisabledClass  = 'ty-disabled': applyDisabledClass  = ''; //换版申请后的禁用（换版申请后  换版和移动按钮被禁用）
            versionNo   === 0   ? recordDisabledClass = 'ty-disabled': recordDisabledClass = ''; //换版记录的禁用 （换版为0 换版记录按钮禁用）

            let funcName = {see: '查看', basicMsg: '基本信息', more: '更多', 'txtChangeVersion': '换版', 'txtChangeVersionApply': '换版申请', changeVersionRecord: '换版记录', move: '移动', changeFileName: '修改文件名称', changeFileSn: '修改文件编号', disable: '禁用'}

            let func = {}
            let moreFunc = {}

            // if (file) {
            //     // 换版记录中的文件 - 功能
            //
            //     func = {
            //         see: {},
            //         basicMsg: { history: '' }
            //     }
            // } else {
            //     if (isGeneral) {
            //         // 总务 - 功能
            //         func = {
            //             see: {},
            //             txtChangeVersion: {disable: operation === '3'},
            //             more: {}
            //         }
            //         moreFunc = {
            //             basicMsg: {},
            //             changeVersionRecord: { disable: versionNo   === 0 },
            //             move: { disable: operation === '3' },
            //             changeFileName: { disable: operation === '3' },
            //             changeFileNo: { disable: operation === '3' }
            //         }
            //     } else {
            //         // 普通员工 和 超管 - 功能
            //         if (isSuper) {
            //             // 超管 - 暂时不用的文件 - 功能
            //             func = {
            //                 see: {},
            //                 more: {}
            //             }
            //             moreFunc = {
            //                 basic: {},
            //                 changeVersionRecord: { disable: versionNo   === 0 }
            //             }
            //         } else {
            //             // 普通职工 - 功能 （普通职工无暂时不用的文件）
            //             func = {
            //                 see: {},
            //                 changeVersionApply: {disable: operation === '3'},
            //                 more: {}
            //             }
            //             moreFunc = {
            //                 basicMsg: {},
            //                 changeVersionRecord: { disable: versionNo   === 0 },
            //                 changeFileName: { disable: operation === '3' && loginUserId === updator },
            //                 changeFileNo: { disable: operation === '3' && loginUserId === updator }
            //             }
            //         }
            //     }
            // }

            if (file) {
                // 换版记录中的文件 - 功能

                func = {
                    see: {},
                    basicMsg: { history: '' }
                }
            } else {
                if (isGeneral) {
                    func = {
                        see: {},
                        txtChangeVersion: {disable: operation === '3'},
                        more: {}
                    }
                    if (category === 3 || category === 4) {
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: versionNo   === 0 }
                        }
                    } else {
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: versionNo   === 0 },
                            move: { disable: operation === '3' },
                            changeFileName: { disable: operation === '3' },
                            changeFileSn: { disable: operation === '3' }
                        }
                    }
                } else {
                    func = {
                        see: {},
                        txtChangeVersionApply: {disable: operation === '3'},
                        more: {}
                    }
                    if (category === 3 || category === 4) {
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: versionNo   === 0 }
                        }
                    } else {
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: versionNo   === 0 },
                            changeFileName: { disable: operation === '3' },
                            changeFileSn: { disable: operation === '3' }
                        }
                    }
                }

            }

            for (let key in func) {
                handleStr += '<a class="aBtn" type="btn" data-name="'+key+'"'
                let attr = func[key]
                for (var item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</a>'
            }
            handleStr += '<ul class="hel" style="display: none">'
            for (let key in moreFunc) {
                handleStr += '<li class="liBtn" type="btn" data-name="'+key+'"'
                let attr = moreFunc[key]
                for (let item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</li>'
            }
            handleStr += '</ul>'
            console.log(handleStr)


            fileListStr +=  '<div class="ty-fileItem" data-id="'+id+'" pid="'+category+'" data-id="'+id+'">'+
                '<div class="ty-fileType ty-file_txt"></div>'+
                '<div class="ty-fileInfo">'+
                '   <div class="ty-fileRow">'+
                '       <div class="ty-fileName" title="'+name+ '-G'+versionNo+'">'+ name +'</div>'+
                '       <div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                '       <div class="ty-fileVersion" versionNo="'+ versionNo +'">'+'G'+versionNo+'</div>'+
                '   </div>'+
                '   <div class="ty-fileRow">'+
                '       <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + updateDate + '</div>'+
                '       <div class="ty-fileHandle">'+ handleStr + '</div>'+
                '   </div>'+
                '</div>'+
                '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2021-09-26 11:31:11，获取文件夹树字符串
function setFolderStr(list, level) {
    // 构建文件夹树
    var str = '<ul>'
    for(var i in list){
        str +=
            '<li>' +
            '   <div class="ty-treeItem '+(list[i].isTrash?'trash':'')+'" title="'+ list[i].name +'" data-id="'+ (list[i].id || '') +'" children="'+list[i].children+'" level="'+level+'" parent="'+list[i].parent+'" directoryAble="'+list[i].directoryAble+'" code="'+list[i].code+'">' +
            (list[i].children > 0?'<i class="fa fa-angle-right"></i>':'<i class="ty-fa"></i>') +
            (list[i].isTrash?'<i class="fa fa-trash"></i>':'<i class="fa fa-folder"></i>') +
            '       <span>' + list[i]["name"] + '</span>' +
            '   </div>' +
            '</li>';

    }
    str += '</ul>'
    return str
}

// creator: 张旭博，2021-10-15 08:24:23，获取文件列表
function getTxtList (currentPageNo, pageSize, categoryId) {


    let pms_childFile = new Promise(resolve => {
        let url = '../about/getAboutDirectoryFile.do'
        let data = { category:categoryId ,currentPageNo : currentPageNo, pageSize : pageSize}
        $.ajax({
            url: url,
            data: data,
            success: function (res) {
                if (res) {
                    resolve(res.data)
                }
            }
        })
    })
    pms_childFile.then(res => {


        var pageInfo = res.pageInfo,
            fileInfo = res.list;

        //设置分页信息
        var currentPageNo   = pageInfo["currentPageNo"],
            totalPage       = pageInfo["totalPage"],
            jsonStr = JSON.stringify( { categoryId : categoryId} ) ;

        //设置文件信息
        var fileListStr = getFileListStr(fileInfo);
        if($(".page_search").is(':visible')){
            setPage( $("#ye-search-file"), currentPageNo, totalPage, "txtList", jsonStr) ;
            $(".page_search .fileContent").find('.searchFile').html(fileListStr).attr("type","folder");
        }else{
            setPage( $("#ye_con"), currentPageNo, totalPage, "txtList", jsonStr) ;
            $(".ty-fileList").html(fileListStr);
            $(".ty-fileList").attr("type","folder");
        }
    })
}

// creator: 张旭博，2021-10-15 08:24:38，获取文件详情
function getTxtDetail(id) {
    return new Promise(resolve => {
        let url = '../about/aboutFileSingle.do'
        let data = { fileId: id}
        if ($(".page_fileHistory").is(":visible")) {
            url = '../about/fileHistorySingleByAbout.do'
            data = { id: id}
        }
        $.ajax({
            url: url,
            data: data,
            success: function (res) {
                if (res) {
                    let data = res.data
                    resolve(data)
                }
            }
        })
    })
}

// creator: 张旭博，2018-05-16 15:27:29，更改文件名称 - 确定
function sureChangeFileName() {
    var currentFileId = $("#changeFileName").data("currentFileId"),
        changeFileName = $("#changeFileName .changeFileName").val();
    $.ajax({
        url:"../about/aboutUpdateFileName.do",
        data:{
            "id"  : currentFileId,
            "name": changeFileName
        },
        success: function(data){
            bounce.cancel();
            if(data["success"] === 1){
                layer.msg("成功修改文件名称");
                updateFile(currentFileId);
            }else{
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }
        }
    });
}

// creator: 张旭博，2018-05-16 15:27:29，更改文件编号 - 确定
function sureChangeFileNo() {
    var currentFileId = $("#changeFileNo").data("currentFileId"),
        changeFileNo = $("#changeFileNo .changeFileNo").val();
    $.ajax({
        url:"../about/aboutUpdateFileSn.do",
        data:{
            "id"    : currentFileId,
            "fileSn": changeFileNo
        },
        success: function(res){
            let data = res.data
            var state = data.state
            if (state == 1) {
                bounce.cancel();
                layer.msg("成功修改文件编号");
                updateFile(currentFileId);
            } else if (state == 2) {
                layer.msg("文件编号重复！")
            } else {
                bounce.show( $("#tip") ) ; $("#tipMess").html("操作失败！");
            }
        }
    });
}

// creator: 张旭博，2021-07-21 09:14:10，更新文件数据
function updateFile(fileId){
    $.ajax({
        url: "../about/aboutFileSingle.do",
        data: {"fileId" :fileId},
        success: function (res) {
            var state = res["success"] ;
            if(state !== 1){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list    = res["data"].aboutFile;
                var listArr = [];
                listArr.push(list);
                var listStr = getFileListStr(listArr);
                $(".ty-fileItemActive").replaceWith(listStr);
            }
        }
    })
}


// creator: 张旭博，2021-10-15 09:17:53，获取换版后的历史文件列表
function getAboutFileRecord(currentPageNo, totalPage){
    //得到地址栏传过来的ID
    var fileId = $(".page_fileHistory").data("fileId");
    var data = {
        id: fileId,
        currentPageNo: currentPageNo,
        pageSize: totalPage
    }
    $.ajax({
        url: "../about/aboutFileHistory.do",
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"]

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo);
            setPage( $("#ye_record"), currentPageNo, totalPage, "aboutChangeRecord") ;
            $(".page_fileHistory .historyFileList").html(fileListStr)
        }
    })
}

// creator: 张旭博，2018-05-16 16:29:28，查看各种操作记录
function seeHandelRecordBtn(type){
    //打开弹窗
    bounce_Fixed.show($("#handleRecord"));

    //更改弹窗标题
    var recordTitleName = '';
    switch (type){
        case 1:
            recordTitleName = '浏览记录';
            break;
        case 2:
            recordTitleName = '下载记录';
            break;
        case 5:
            recordTitleName = '移动记录';
            break;
        case 6:
            recordTitleName = '文件名称修改记录';
            break;
        case 7:
            recordTitleName = '文件编号修改记录';
            break;
    }
    $("#handleRecord").find(".recordTitleName").html(recordTitleName);

    //渲染操作记录表格
    setHandelRecord(type);
}

// creator: 张旭博，2018-05-16 16:46:20，获取各种操作记录
function setHandelRecord(type) {
    var currentFileId = $(".ty-fileItemActive").data("id"),
        tableStr = '';
    switch (type){
        case 5:
            tableStr =  '<thead>' +
                '<tr>' +
                '<th>序号</th>'+
                '<th>移动前</th>'+
                '<th>移动后</th>'+
                '<th>移动人</th>'+
                '<th>移动时间</th>'+
                '</tr>'+
                '</thead>';
            break;
        case 6:
        case 7:
            tableStr =  '<thead>' +
                '<tr>' +
                '<th>序号</th>'+
                '<th>修改前</th>'+
                '<th>修改后</th>'+
                '<th>修改人</th>'+
                '<th>修改时间</th>'+
                '</tr>'+
                '</thead>';
            break;
    }
    $.ajax({
        url:"../about/aboutUpFileRecord.do ",
        data:{
            id  : currentFileId,
            operation: type
        },
        success: function(res){
            if(res["success"] === 1){
                var list = res.data.list;
                tableStr += '<tbody>';
                for(var i = 0; i < list.length; i++){
                    tableStr += '<tr>' +
                        '<td>' + (i + 1) +'</td>'+
                        '<td>' + list[i].nameBefore + '</td>'+
                        '<td>' + list[i].nameAfter + '</td>'+
                        '<td>' + list[i].createName + '</td>'+
                        '<td>' + moment(list[i].createTime).format("YYYY-MM-DD HH:mm:ss") + '</td>'+
                        '</tr>'
                }
                tableStr += '</tbody>';
                $("#handleRecord .recordTable").html(tableStr)
            }else{
                $("#mt_tip_ms").html('系统错误！');
                bounce.show($("#mtTip"));
            }
        }
    });
}

// creator: 张旭博，2021-10-20 09:30:21，获取审批人
function getApprover() {
    $.ajax({
        url: "../res/getAllOidUserByResource.do" ,
        data: { userID: sphdSocket.user.userID },
        success: function (data) {
            var approverList = data["data"]["list"];
            var optStr = '<option value="">请选择本文件的审批人</option>';
            if (approverList && approverList.length >0){
                for(var i in approverList) {
                    optStr += '<option value="'+ approverList[i].userID +'">'+ approverList[i].userName +'</option>';
                }
            }
            $(".chooseApprover").html(optStr);
        }
    })
}

//------------------------- 文件查询 ---------------------------//

// creator: 张旭博，2018-04-20 19:47:19，普通搜索 - 搜索
function searchBtn() {
    $("#searchSort").show()
    $("#search_applier").html("")
    $("#searchSort li").eq(1).find(".sort").hide()
    $("#searchSort li").eq(1).find("i").removeClass("fa-long-arrow-up")
    getGeneralFile(1, 20);
}

// creator: 张旭博，2018-04-20 19:47:39，高级搜索 - 按钮
function advanceSearchBtn() {
    bounce.show($("#advancedSearch"))
    $("#advancedSearch").find("input,textarea,select").val("");
}

// creator: 张旭博，2018-04-20 14:49:37，根据名字和编号搜索文件(普通检索)
var sphdSocketArr = [];
function getGeneralFile(cur, pageSize){
    var name = $("#fileNameOrSn").val();
    var condition = {
        currentPageNo: cur,
        pageSize: pageSize,
        name: name
    }
    $.ajax({
        url:"../about/aboutGeneralFindFile.do",
        data: condition,
        success: function(data){
            data = data["data"];
            var fileInfo = data.list,
                pageInfo = data.pageInfo;
            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"]

            setPage($("#ye-search-file"), currentPageNo, totalPage, "generalFile") ;
            $("#btn-group").hide();
            $(".page_search").show().siblings().hide();
            $(".page_search .fileContent").show().siblings().hide();

            var fileListStr = '';
            if(fileInfo.length === 0){
                fileListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件</div>';
            }else{
                // for(var t in categoryInfo){
                //     sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                //         console.log('普通搜索： ' + data)
                //         var result = JSON.parse(data);
                //         var fileResult = [];
                //         fileResult.push(result.res);
                //         var resultStr = getFileListStr(fileResult);
                //         $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                //         $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                //     }, null, 'custom', categoryInfo[t]));
                // }
                fileListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 '+pageInfo.totalResult+' 个文件符合查询结果</div>'
                    +   getFileListStr(fileInfo);
            }

            $(".page_search .fileContent .searchFile").html(fileListStr);
        }
    });
}

// creator: 张旭博，2018-04-20 19:53:29，高级检索 -- 文件检索
function advancedSearch_file(currentPageNo,pageSize) {
    var data = {
        "currentPageNo"     : currentPageNo,
        "pageSize"          : pageSize
    };
    $("#advancedSearch").find("input,select").each(function () {
        if($(this).val() !== ""){
            var key = $(this).attr("class");
            data[key] = $(this).val();
        }
    });
    $.ajax({
        url : '../about/aboutAdvancedFindFile.do',
        data : data,
        success: function(data) {
            bounce.cancel();
            data = data["data"];
            var fileInfo = data.list,
                pageInfo = data.pageInfo;
            var catorageInfo = data.listCategory;
            // 设置分页信息
            var currentPageNo = pageInfo["currentPageNo"],
                totalPage = pageInfo["totalPage"];

            setPage($("#ye-search-file"), currentPageNo, totalPage, "advancedFindFile");
            $("#btn-group").hide();
            $(".page_search").show().siblings().hide();
            $(".page_search .fileContent").show().siblings().hide();

            // 展示查询结果（提示+文件列表）
            var fileListStr = '';
            if (fileInfo.length === 0) {
                fileListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件</div>';
            } else {
                fileListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 ' + pageInfo.totalResult + ' 个文件符合查询结果</div>'
                    + getFileListStr(fileInfo);
                // for(var t in catorageInfo){
                //     sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                //         var result = JSON.parse(data);
                //         var fileResult = [];
                //         fileResult.push(result.res);
                //         var resultStr = getFileListStr(fileResult);
                //         $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                //         $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                //     }, null, 'custom', catorageInfo[t]));
                // }
            }
            $(".page_search .fileContent .searchFile").html(fileListStr);
        }
    });
}

// creator: 张旭博，2018-04-20 19:53:29，高级检索 -- 文件类别检索
function advancedSearch_folder(currentPageNo,pageSize) {
    var folderName = $("#advancedSearch .folderName").val();
    $.ajax({
        url:"../about/aboutFindDirectory.do",
        type:"post",
        data:{
            "currentPageNo"     : currentPageNo,
            "pageSize"          : pageSize,
            "name"              : folderName
        },
        success: function(data){
            bounce.cancel();
            data = data["data"];
            var folderInfo = data.list,
                pageInfo = data.pageInfo;

            // 设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"];

            setPage( $("#ye-search-folder"), currentPageNo, totalPage, "findFolder") ;

            // 展示查询结果（提示+文件列表）
            var folderListStr = '';
            if(folderInfo.length === 0){
                folderListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件类别</div>';
                $("#ye-search-folder").hide();
            }else{
                folderListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 '+pageInfo.totalResult+' 个文件类别符合查询结果</div>'
                    + getFolderListStr(folderInfo);
                $("#ye-search-folder").show();
            }

            $("#btn-group").hide();
            $(".page_search").show().siblings().hide();
            $(".page_search .folderContent").show().siblings().hide();
            $(".page_search .searchFolderContent").html(folderListStr).attr("name",name);
        }
    });
}


// creator: 张旭博，2018-04-24 10:17:55，根据文件夹数据返回文件类别字符串
function getFolderListStr(folderInfo,parentFolder) {
    var folderListStr = '';
    if(folderInfo.length !== 0){
        for(var i = 0; i < folderInfo.length; i++) {
            var id = folderInfo[i].id,
                name = folderInfo[i].name,
                allParentCategory = folderInfo[i].allParentCategory,
                firstLevelClass = '';

            if (parentFolder) {
                allParentCategory = parentFolder;
            } else {
                if (allParentCategory === null) {
                    allParentCategory = '';
                    firstLevelClass = 'firstLevel'
                } else {
                    firstLevelClass = ''
                }
            }

            folderListStr += '<div class="ty-folderItem" id="' + id + '">' +
                '<div class="ty-left ty-folderType"><i class="fa fa-folder"></i></div>' +
                '<div class="ty-folderInfo ty-left">' +
                '<div class="ty-folderName ' + firstLevelClass + '">' + name + '</div>' +
                '<div class="ty-folderParent">' + allParentCategory + '</div>' +
                '</div>' +
                '</div>';
        }
    }
    return folderListStr;
}

// creator: 张旭博，2018-04-23 11:32:46，高级搜索 -- 按文件类别查询 -- 获取子级文件类别
function getChildFolder(folderId,parentFolder){
    var url = '../about/getAboutDirectoryAndChildDirection.do'
    $.ajax({
        url:url,
        data:{
            "category": folderId
        },
        success: function(data){
            data = data["data"];
            var childFolder  = data.childFolder;

            // 展示查询结果（提示+文件列表）
            if(!childFolder || childFolder.length === 0){
                getTxtList(1,20,folderId);
                $(".page_search .fileContent").show().siblings().hide();
                sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                    var result = JSON.parse(data);
                    var fileResult = [];
                    fileResult.push(result.res);
                    var resultStr = getFileListStr(fileResult);
                    $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                    $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                }, null, 'custom', folderId));
            }else{
                var folderListStr = getFolderListStr(childFolder,parentFolder);
                $(".page_search .folderContent").show().siblings().hide();
                $(".page_search .childFolder").children(":last").hide();
                $(".page_search .childFolder").append('<div class="childFolderContent">'+folderListStr+'</div>').attr("id",folderId).show().siblings().hide();
            }
        }
    });
}

// creator: 张旭博，2018-05-24 11:24:08，查询返回
function goBack(type) {
    switch (type) {
        case 'query':
            var fileContent = $(".page_search .fileContent"),
                folderContent = $(".page_search .folderContent");

            if(fileContent.find('.searchFile').html() !== ""){
                fileContent.find('.searchFile').html('');
                if(folderContent.find('.searchFolderContent').html() === ""){
                    $(".page_main").show().siblings().hide();
                    $("#fileNameOrSn").val("");
                }else{
                    fileContent.hide().siblings().show();
                }
            }else{
                if(folderContent.find('.childFolder').children().length > 1){
                    fileContent.find('.searchFile').html('');
                    folderContent.find('.childFolder').children(":last").remove();
                    folderContent.find('.childFolder').children(":last").show();
                }else if(folderContent.find('.childFolder').children().length === 1){
                    folderContent.find('.childFolder').html("");
                    folderContent.find('.searchFolder').show();

                }else{
                    folderContent.find('.searchFolderContent').html("");
                    $(".page_main").show().siblings().hide();
                    $("#advancedSearch").find("input,textarea,select").val("");
                    $("#fileNameOrSn").val("");
                }
            }
            break;
        case 'record':
            $("#main").show().siblings().hide();
            break;
    }
    if($(".ty-fileContent").is(":visible")) {
        $("#btn-group").show()
    } else {
        $("#btn-group").hide()
    }

}
