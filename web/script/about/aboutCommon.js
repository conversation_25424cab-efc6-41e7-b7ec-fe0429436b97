/* creator：张旭博，2017-04-27 08:28:29，页面初始化以及事件绑定 */
$(function () {
    let urlId = Number(getUrlParam("id"))
    if (urlId !== 2) {
        $(".search").hide()
    }
    $("#fileNameOrSn").val("")
    $.ajax({
        url: '../about/getAboutInitialDirectory.do',
        success: function (res) {
            let listFirstFolder = res.data.listFirstFolder
            let urlId = Number(getUrlParam("id"))
            let thisItem = listFirstFolder.find(item => { return item.id === urlId })
            $(".pageTotalName").html(thisItem.name)
            if (urlId === 1 || urlId === 2) {
                // 版本说明和使用帮助
                let str =   '<ul>' +
                            '    <li>' +
                            '        <div class="ty-treeItem ty-treeItemActive" title="'+thisItem.name+'" children="'+thisItem.children+'" level="1" parent="null" data-id="'+thisItem.id+'">' +
                            (urlId === 1?'':'            <i class="fa fa-angle-right"></i>') +
                            '            <i class="fa fa-folder"></i>' +
                            '            <span>'+thisItem.name+'</span>' +
                            '        </div>' +
                            '    </li>' +
                            '</ul>'
                $("#aboutTree").html(str)
                $("#aboutTree .ty-treeItem").eq(0).click()
            } else {
                $(".txtShow .btn-group").hide()
                let pms_childFolder = new Promise(resolve => {
                    let url = '../about/getAboutDirectoryAndChildDirection.do'
                    let data = { category: urlId }
                    $.ajax({
                        url: url,
                        data: data,
                        success: function (res) {
                            if (res) {
                                resolve(res.data)
                            }
                        }
                    })
                })
                pms_childFolder.then(res => {
                    let childFolder = res.list
                    seeTxtDetail(childFolder[0].id)
                })
            }
        }
    })

    // updater : 侯杏哲 2017-11-18 每一级目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容）
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        let treeName = $(this).parents(".ty-colFileTree").data("name")
        let hasChildren = Number($(this).attr('children')) > 0
        let thisLevel = Number($(this).attr("level"))

        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");

        var categoryId = $(this).data("id") ;
        //点击文件夹箭头方向的切换
        if (hasChildren) {
            $(this).find("i").eq(0).toggleClass("fa-angle-down")
            //获取本级与子级文件夹内容

            let pms_childFolder = new Promise(resolve => {
                let url = thisLevel === 0 ? '../about/getAboutInitialDirectory.do' : '../about/getAboutDirectoryAndChildDirection.do'
                let data = thisLevel === 0 ? { }:{ category:categoryId }
                $.ajax({
                    url: url,
                    data: data,
                    success: function (res) {
                        if (res) {
                            resolve(res.data)
                        }
                    }
                })
            })
            // 右侧内容处理
            let treeItemThis = $(this)
            pms_childFolder.then(res => {
                let thisFolder = {}, childFolder = [];
                if (thisLevel === 0 ) {
                    childFolder = res.listFirstFolder
                } else {
                    childFolder = res.childFolder
                }


                // 左侧文件夹填充子级内容
                let level = Number(treeItemThis.attr("level")) + 1
                let childFolderStr = setFolderStr(childFolder, level)
                treeItemThis.next().remove()
                if (treeItemThis.children(".fa-angle-down").length > 0) {
                    treeItemThis.after(childFolderStr)
                }
            })
            // 非末级文件夹不显示文件列表
            $(".section_right").hide()
        } else {
            // 非末级文件夹不显示文件列表
            $(".section_right").show()
            getTxtList( 1,20, categoryId);
        }
    });

    $(".page_contentDetail").on("click", "[type='btn']", function () {
        var name= $(this).data("name")
        switch (name) {
            case 'back':
                $(".page_contentApply").hide()
                $(".page_main").show()
                break
        }
    })


})

// creator: 张旭博，2018-04-24 10:17:16，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无内容</p></div>';
    }else{
        $("#ye_con").show() ;
        for(var i = 0; i < fileInfo.length; i++){
            var id          = fileInfo[i].id,
                category    = fileInfo[i].category,
                name        = fileInfo[i].name,
                allCategoryName= fileInfo[i].allCategoryName,
                versionNo= fileInfo[i].versionNo

            let urlId = Number(getUrlParam("id"))
            if(urlId === 1) {
                name = '版本号：' + name
            }
            if(urlId === 2) {
                name = '《' + allCategoryName + '-' + name + '》'
            }
            fileListStr +=  '<div class="ty-fileItem" data-id="'+id+'" pid="'+category+'" onclick="seeTxtDetail('+id+')">'+
                            '   <div class="ty-fileType ty-file_txt"></div>'+
                            '   <div class="ty-fileInfo">'+
                            '      <div class="ty-fileRow">'+ name +'</div>'+
                            '   </div>'+
                            '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2021-09-26 11:31:11，获取文件夹树字符串
function setFolderStr(list, level) {
    // 构建文件夹树
    var str = '<ul>'
    for(var i in list){
        str +=
            '<li>' +
            '   <div class="ty-treeItem '+(list[i].isTrash?'trash':'')+'" title="'+ list[i].name +'" data-id="'+ list[i].id +'" children="'+list[i].children+'" level="'+level+'" parent="'+list[i].parent+'" directoryAble="'+list[i].directoryAble+'">' +
            (list[i].children > 0?'<i class="fa fa-angle-right"></i>':'<i class="ty-fa"></i>') +
            '<i class="fa fa-folder"></i>'+
            '       <span>' + list[i]["name"] + '</span>' +
            '   </div>' +
            '</li>';

    }
    str += '</ul>'
    return str
}

// creator: 张旭博，2021-10-12 10:33:15，设置文件列表
function getTxtList (currentPageNo, pageSize, categoryId) {
    let pms_childFile = new Promise(resolve => {
        let url = '../about/getAboutDirectoryFile.do'
        let data = { category:categoryId ,currentPageNo : currentPageNo, pageSize : pageSize}
        $.ajax({
            url: url,
            data: data,
            success: function (res) {
                if (res) {
                    resolve(res.data)
                }
            }
        })
    })
    pms_childFile.then(res => {


        var pageInfo = res.pageInfo,
            fileInfo = res.list;

        //设置分页信息
        var currentPageNo   = pageInfo["currentPageNo"],
            totalPage       = pageInfo["totalPage"],
            jsonStr = JSON.stringify( { categoryId: categoryId} ) ;

        //设置文件信息
        var fileListStr = getFileListStr(fileInfo);
        setPage( $("#ye_con"), currentPageNo, totalPage, "txtList", jsonStr) ;
        $(".ty-fileList").html(fileListStr);
        $(".ty-fileList").attr("type","folder");

    })
}

// creator: 张旭博，2021-10-12 10:32:54，查看文件详情
function seeTxtDetail(id) {
    let txt = new Promise(resolve => {
        let url = '../about/aboutFileSingle.do'
        let data = { fileId: id}
        $.ajax({
            url: url,
            data: data,
            success: function (res) {
                if (res) {
                    resolve(res.data.aboutFile)
                }
            }
        })
    })
    txt.then(res => {
        $(".txtContent pre").html(res.content)
        $(".txtShow").show().siblings(".main").hide()
    })
}

// creator: 张旭博，2021-10-12 10:32:45，回到主页
function backToMain() {
    $(".main").show().siblings(".txtShow").hide()
}
function backToMainPage() {
    $(".page_main").show().siblings(".page").hide()
    $("#fileNameOrSn").val("")
}

//------------------------- 文件查询 ---------------------------//

// creator: 张旭博，2018-04-20 19:47:19，普通搜索 - 搜索
function searchBtn() {
    $("#searchSort").show()
    $("#search_applier").html("")
    $("#searchSort li").eq(1).find(".sort").hide()
    $("#searchSort li").eq(1).find("i").removeClass("fa-long-arrow-up")
    getGeneralFile(1, 20);
}

// creator: 张旭博，2018-04-20 14:49:37，根据名字搜索文件(普通检索)
function getGeneralFile(cur, pageSize){
    var name = $("#fileNameOrSn").val();
    var condition = {
        currentPageNo: cur,
        pageSize: pageSize,
        name: name,
        type: 1
    }
    $.ajax({
        url:"../about/aboutGeneralFindFile.do",
        data: condition,
        success: function(data){
            data = data["data"];
            var fileInfo = data.list,
                pageInfo = data.pageInfo;
            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"]

            setPage($("#ye-search-file"), currentPageNo, totalPage, "generalFile") ;
            $("#btn-group").hide();
            $(".page_search").show().siblings().hide();
            $(".page_search .fileContent").show().siblings().hide();

            var fileListStr = '';
            if(fileInfo.length === 0){
                fileListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件</div>';
            }else{
                // for(var t in categoryInfo){
                //     sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                //         console.log('普通搜索： ' + data)
                //         var result = JSON.parse(data);
                //         var fileResult = [];
                //         fileResult.push(result.res);
                //         var resultStr = getFileListStr(fileResult);
                //         $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                //         $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                //     }, null, 'custom', categoryInfo[t]));
                // }
                fileListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 '+pageInfo.totalResult+' 个文件符合查询结果</div>'
                    +   getFileListStr(fileInfo);
            }

            $(".page_search .fileContent .searchFile").html(fileListStr);
        }
    });
}