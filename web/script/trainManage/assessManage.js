// creator: lyt  2021-05-05  初始化 三级弹框 - fixed2
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#alertTip"));
bounce_Fixed2.cancel();
var questionPageSize = 20;
var editBankSelectQ = null;
var peroidYear = "",peroidMonth = "", page=[];
$(function () {
    let jsonObj = { "type": 3 , "status":1 , "conNum": 1 };
    getList(1, jsonObj);
    $("#showMainConNum").val(1);

    // 选择题库toggle
    $("#selectBankList").on('click',".toggle", function () {
        let faObj = $(this).children(".fa");
        if(faObj.hasClass("fa-square-o")){
            faObj.attr("class","fa fa-check-square-o");
        }else{
            faObj.attr("class","fa fa-square-o");
        }
    });
    $(".testers").on("click",".fa-times",function(){
        $(this).parent().remove();
    });
    $("#testUserList").change(function(){
        // let curU = $("#testUserList").val() // userID
        let curU = ($("#testUserList option:selected").data("info"));
        // curU = JSON.parse($("#testUserList option:selected").data("info"));
        let list = getTestUsers();
        let index = list.findIndex((item)=>{ return item.user === curU.userID});
        if(index > -1){
            layer.msg("该员工已经选择过了");
            return false;
        }
        let str = `<span class="tester">${curU.userName} <i class="fa fa-times"></i><span class="hd">${JSON.stringify(curU)}</span></span>`;
        $(".testers").append(str);
    });
    // linkBtn 按钮
    $(".linkBtn").click(function(){
        let fun = $(this).data("fun");
        switch (fun){
            case 'selectQuestionBank': // 选择题库
                bounce_Fixed.show($("#selectQustionBank"));
                $("#selectQustionBank .bonceHead span").html("单选题库");
                $("#selectQustionBank .step0").show();
                $("#selectQustionBank .step1").hide();
                getBankList();
                break;
            case 'makeupScan': // 查看补考
                makeupScan();
                break;
            case 'monthScan': // 查看月统计
                showMainCon(9) ;
                byMonthQueryScan();
                break;
            default:
        }
    });
    // 按钮
    $("body").on("click", ".ty-btn,.btnCat", function () {
        let fun = $(this).data("fun");
        switch (fun){
            case 'goStop': // 被终止的考核
                showMainCon(3) ;
                let jsonObj = { "type": 3 , "status":0 , "conNum": 3 };
                $(".mainCon3 .fa").addClass("fa-long-arrow-down").removeClass("fa-long-arrow-up");
                getList(1, jsonObj);
                break;
            case 'goFinish': // 已结束的考核
                showMainCon(4) ;
                $(".mainCon4 .fa").addClass("fa-long-arrow-down").removeClass("fa-long-arrow-up");
                getFinishList(1, { "type": 1 , "conNum": 4, "source": 1  });
                break;
            case 'startTest': // 发起考核
                bounce.show($("#editTest"));
                $("#editTest table").hide();
                $(".testers").html("");
                $("#editTest input").val("");
                $("#editTest .bonceHead span").html("发起考核");
                $("#bankList tbody").children(":gt(0)").remove();
                getTestUserList();
                break;
            case 'selectQustionBankOk': // 选择题库 -确定
                selectQustionBankOk();
                break;
            case 'bankSelectQOk': // 发起考核 - 选题 -确定
                bankSelectQOk();
                break;
            case 'selectQuesOk': // 题已选完，下一步
                selectQuesOk();
                break;
            case 'editTestOk': // 各道试题的分数 - 下一步
                if($("#editTest2 .editTestOk").hasClass("bounce-cancel")){
                    return false
                }
                // bounce.cancel();
                bounce.show($("#testScan"));
                $("#testScan input").val("");
                $("#testScan .makeup").hide();
                $("#testScan .scan").hide();
                $("#testScan .edit").show();
                $("#testScan #passingScore").val("60");
                $("#testScan .publicity").data("top", '3').html("成绩前三名将给予公示，但无奖励。");
                $("#testScan .bonceHead span").html("考核的其他事项");
                var us = getTestUsers() ;
                var bs = getBanks() ;
                $("#testScan .usersLen").html(us.length).data("info", us);
                $("#testScan .banksLen").html(bs.length).data("info", bs);
                $("#testScan .target").html($("#editTest .target").val());
                var cAllnum = Number($("#cAllnum").html());
                var tAllnum = Number($("#tAllnum").html());
                let count = (cAllnum + tAllnum) * 10;
                let minuteTime = Math.ceil(count / 60)
                $("#testScan .testInfo").html(`共${cAllnum + tAllnum}道，其中单选题${cAllnum}道，判断题${tAllnum}道`);
                let fenList = [];
                $("#fenList tr.c").each(function(){
                    let type = $(this).data("type");
                    let q = $(this).data("q");
                    let s = $(this).find(".s").val();
                    let num = $(this).find(".num").val();
                    fenList.push({"questionType": q, "scoreType":type, "score":s , "questionNum":num });
                })
                $("#editTest2 .editTestOk").data("info",fenList);
                $("#answerDuration").val(minuteTime);
                break;
            case 'cancelTest': // 取消本次考核
                bounce.cancel();
                 break;
            case 'sureTest': // 完成，发出考核通知
                sureTest();
                break;
            case 'makeupTest': // 确定发起补考
                makeupTest();
                break;
            case 'confirmTipOk': // confirmTipOk
                confirmTipOk();
                break;
            case 'backMain': // 返回 按钮
                backMain();
                break;
            case 'backCon4': // 关闭 按钮
                showMainCon(4) ;
                break;
            case 'backPre': // 关闭 按钮
                backPre() ;
                break;

            default:
        }
    });
    // table 里面的按钮
    $("table").on("click", ".funbtn", function () {
        let fun = $(this).data("fun"), info = '';
        switch (fun){
            case 'bankDel': // 发起考核 - 删除 题库
                $(this).parents("tr").remove();
                break;
            case 'bankSelectQ': // 发起考核 - 选题
                bounce_Fixed.show($("#selectQustionBank"));
                $("#selectQustionBank .bonceHead span").html("选择试题");
                $("#selectQustionBank .step1").show();
                $("#selectQustionBank .step0").hide();
                editBankSelectQ = $(this);
                getSelectOptionalQuestionCount()
                break;
            case 'questionBankScan': // 查看相关题库
                questionBankScan();
                break;
            case 'testUserScan': // 查看考核对象
                testUserScan();
                break;
            case 'rankScan': // 查看考试排名
                rankScan($(this));
                break;
            case 'makeupTest': // 发起补考
                info = JSON.parse($(this).siblings(".hd").html());
                if (info.failNum == 0) {
                    bounce_Fixed2.show($("#alertTip"));
                    let str = "无需发起补考！<br>因为所有参考人员均已通过了考核。"
                    $("#alertTip .tip").html(str);
                } else {
                    testScan($(this), "makeup");
                }
                break;
            case 'scanTestPaper': // 查看试卷
                scanTestPaper($(this));
                break;
            case 'testScan': // 查看考核
                testScan($(this));
                break;
            case 'staffTestScan': // 职工补考记录
                staffTestScan($(this));
                break;
            case 'testStop': // 终止本次考核
                testStopJudge($(this));
                break;
            case 'assessmentRecordScan': // 职工考核记录
                assessmentRecordScan($(this));
                break;
            case 'editPubSetting': // 修改考核公示的设置
                $("#publicitySetting .topFew").val("3");
                $("#publicitySetting .pubSet i").attr("class","fa fa-circle-o");
                bounce_Fixed.show($("#publicitySetting"));
                break;
            default:
                if(fun){
                    window[fun]($(this), fun)
                }
                break;
        }

    });
    // 排列 升序降序
    $(".arrowBtn").click(function() {
        let type = $(this).data("type");
        let o = $(this).children("i");
        let down = o.hasClass("fa-long-arrow-down");
        if(down){
            o.attr("class","fa fa-long-arrow-up");
        }else {
            o.attr("class","fa fa-long-arrow-down");
        }
        let typeNum = 1 ;
        switch (type){
            case "endTime": // 考试截止时间
                if(down){
                    typeNum = 2;
                }else {
                    typeNum = 1 ;
                }
                break;
            case "createTime": // 创建
                if(down){
                    typeNum = 4 ;
                }else {
                    typeNum = 3 ;
                }
                break;
            case "bankNum": // 相关题库数量
                if(down){
                    typeNum = 6 ;
                }else {
                    typeNum = 5 ;
                }
                break;
            case "stopTime": // 终止时间
                if(down){
                    typeNum = 8 ;
                }else {
                    typeNum = 7 ;
                }
                break;
            default:
        }
        let mainConNum = $("#showMainConNum").val()
        let jsonObj = JSON.parse($("#page" + mainConNum).find(".json").html());
        jsonObj["type"] = typeNum;
        if(mainConNum == '4'){
            getFinishList(1, jsonObj )
        }else {
            getList(1, jsonObj);
        }
    });
    // creator: 李玉婷，2022-01-20 14:31:49，修改考核公示的设置页面按钮切换
    $(".settingMain").on("click", ".fa", function () {
        var klass = $(this).hasClass('fa-dot-circle-o');
        if(klass){
            $(this).attr("class" , "fa fa-circle-o");
            $(this).parent(".pubSet").siblings().find("i").attr("class" , "fa fa-circle-o");
        }else{
            $(this).attr("class" , "fa fa-dot-circle-o");
            $(this).parent(".pubSet").siblings().find("i").attr("class" , "fa fa-circle-o");
        }
    });
    $(".clearInputVal").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            $(this).prev().get(0).focus();
        }
    });
});
//  create:hxz 2021-4-9 返回 按钮
function backMain() {
    showMainCon(1) ;
}
//  create:hxz 2021-3-8  确定发起补考
function makeupTest() {
    let date1 = $("#date1").val();
    let time = $("#dateTime").val();
    if (!date1 || date1 == "" || !time || time == "") {
        layer.msg("请录入考核截止时间");
        return false;
    }
    bounce.cancel();
    let id = $("#testScan").data("id");
    let finishDate = date1 + ' ' + time + ':00';

    $.ajax({
        "url":"../examManage/addTrainingExamAgain.do",
        "data": {
            "id":  id ,
            "endTime":  finishDate
        },
        success:function(res) {
            let status = res.status; // 1成功，0失败
            if(status == 1){
                layer.msg("考核通知已发出！")
                let jsonObj = { "type": 3 , "status":1 , "conNum": 1 };
                getList(1, jsonObj);
            }else {
                layer.msg("操作失败")
            }
        }
    })
}
// create :hxz 2021-3-5 查看考试排名
function rankScan(thisObj){
    $(".mianCon").hide();$(".mainCon5").show();
    let temp = JSON.parse(thisObj.siblings(".hd").html());
    $.ajax({
        "url":"../examManage/selectExamDetail.do",
        "data": { "id":temp.id } ,
        success:function(res) {
            $(".mainCon5 .makeupScan").data("id", temp.id) ;
            let info = res.trainingExam || [];
            let banks = res.respExamBankList || [];
            let users = res.respExamUserList || [];
            let questions = res.respExamQuestionList || [];
            let rankList = res.respExamUserList || [];
            let c = 0 , t = 0 ;
            banks.forEach(function(item){
                c += Number(item.trainingExamBank.choiceNum);
                t += Number(item.trainingExamBank.tfNum);
            })
            $(".mainCon5 .endTime").html(new Date(info.endTime).format("yyyy-MM-dd hh:mm"))
            $(".mainCon5 .usersLen").html(users.length)
            $(".mainCon5 .target").html(info.goal) ;
            $(".mainCon5 .failNum").html(info.failNum) ;
            $(".mainCon5 .testInfo").html(`共${c+t}道，其中单选题${c}道，判断题${t}道`) ;
            $(".mainCon5 .passingScore").html(info.passingScore + "分") ;
            let str1 = `` ,str2 = `` ;
            rankList.forEach(function(item){
                let result = item.trainingExamUser;
                let user = item.user;
                if(result.passingState == 1){
                    str1 += `<tr>
                            <td>${result.ranking}</td>
                            <td>${user.userName} &nbsp;&nbsp; ${user.mobile}</td>
                            <td>${handleNull(user.departName)}<br>${handleNull(user.postName)}</td>
                            <td>${ parseFloat(((result.stopTime - result.startTime)/1000/60).toFixed(1)) }分钟</td>
                            <td>${result.score}</td>
                        </tr>`;
                }else{
                    str2 += `<tr class="gray">
                            <td>${result.ranking}</td>
                            <td>${user.userName}&nbsp;&nbsp; ${user.mobile}</td>
                            <td>${handleNull(user.departName)}<br>${handleNull(user.postName)}</td>
                            <td>${ parseFloat(((result.stopTime - result.startTime)/1000/60).toFixed(1)) }分钟</td>
                            <td>${result.score}</td>
                        </tr>`;
                }
            })
            $("#tb5 tbody>tr:gt(0)").remove();
            $("#tb5 tbody").append(str1 + str2);
        }
    });
}

// create :hxz 2021-3-5 查看试卷
function getTestPaper(thisObj){
    let temp = JSON.parse(thisObj.siblings(".hd").html());
    $.ajax({
        "url":"../examManage/selectExamDetail.do",
        "data": { "id":temp.id } ,
        success:function(res) {
            testPaperView(res);
        }
    });
}
function scanTestPaper(obj){
    bounce_Fixed.show($("#testPaperScan"));
    let res = JSON.parse(obj.siblings(".hd").html());
    testPaperView(res);
}
function testPaperView(res){
    bounce_Fixed.show($("#testPaperScan"));
    let info = res.trainingExam || [];
    let banks = res.respExamBankList || [];
    let users = res.respExamUserList || [];
    let questions = res.respExamQuestionList || [];
    let rankList = res.respExamUserList || [];
    let c = 0 , t = 0 ;
    banks.forEach(function(item){
        c += Number(item.trainingExamBank.choiceNum);
        t += Number(item.trainingExamBank.tfNum);
    })
    $("#testPaperScan .endTime").html(new Date(info.endTime).format("yyyy-MM-dd hh:mm"))
    $("#testPaperScan .usersLen").html(users.length)
    $("#testPaperScan .target").html(info.goal) ;
    $("#testPaperScan .testInfo").html(`共${c+t}道，其中单选题${c}道，判断题${t}道`) ;
    let str1 = ``,str2 = ``, ra = [ "A","B","C","D","E","F" ],cNum = 0, tfNum = 0, cSum = 0, tfSum = 0  ;
    questions.forEach(function(item, index){
        let fen = item.trainingExamQuestion ;
        let qus = item.trainingQuestion ;
        let type = qus.type ;
        let options = item.trainingExamKeyList , optionStr =``;
        if (options) {
            options.forEach(function(ops,index){
                optionStr +=` <p><span>${ra[index]}</span> &nbsp;&nbsp; ${ops.content}</p>`;
            });
        }else {
            optionStr += '<p>' +
                '    <span><i class="fa fa-circle-o"></i><i class="fa fa-check"></i></span>' +
                '    <span><i class="fa fa-circle-o"></i><i class="fa fa-times"></i></span>' +
                '</p>';
        }
        if(type == 1){
            cNum++;
            cSum += Number(fen.score);
            str1 += `<div class="questionItem">
                    <span>${index + 1}、（本题${fen.score}分)${qus.content}</span>
                    ${optionStr}
                </div>` ;
        }else{
            tfNum++;
            tfSum += Number(fen.score);
            str2 += `<div class="questionItem">
                    <span>${index + 1}、（本题${fen.score}分)${qus.content}</span>
                    ${optionStr}
                </div>` ;
        }
    });
    $(".cInfo").html(`一、单选题（共${cNum}道，共${cSum}分)`);
    $(".tfInfo").html(`二、判断题（共${tfNum}道，共${tfSum}分)`);
    $("#testPaperScan .type1").html(str1);
    $("#testPaperScan .type2").html(str2);
}
// create :hxz 2021-3-5 查看补考
function makeupScan(){
    let id = $(".mainCon5 .makeupScan").data("id") ;
    showMainCon(6) ;
    $(".mainCon6 .endTime").html($(".mainCon5 .endTime").html());
    $(".mainCon6 .usersLen").html($(".mainCon5 .usersLen").html());
    $(".mainCon6 .target").html($(".mainCon5 .target").html());
    $.ajax({
        "url":"../examManage/selectExamAgain.do",
        "data":{ "id": id },
        success:function(res) {
            let status = res.status ;
            let str1 = `` ,str2 = `` ;
            if (status != 0) {
                let rankList = res.respExamUserList ;
                if (rankList.length > 0) {
                    rankList.forEach(function(item){
                        let result = item.trainingExamUser ;
                        let user = item.user ;
                        if(result.passingState == 1){
                            str1 += `<tr>
                            <td>${user.userName} ${user.mobile}</td>
                            <td>${handleNull(user.departName)}<br>${handleNull(user.postName)}</td>
                            <td>${result.answerTimes}</td>
                            <td> ${result.passingState == 0 ? "还需补考" : "补考已过"  }</td>
                            <td>
                                <span class="ty-color-blue funbtn" data-fun="staffTestScan">查看</span>
                                <span class="hd">${ JSON.stringify({"user": user.userID, "id": result.exam}) }</span>
                             </td>
                        </tr>`;
                        }else{
                            str2 += `<tr class="gray">
                            <td>${user.userName} ${user.mobile}</td>
                            <td>${handleNull(user.departName)}<br>${handleNull(user.postName)}</td>
                            <td>${result.answerTimes}</td>
                            <td> ${result.passingState == 0 ? "还需补考" : "补考已过"  }</td>
                            <td>
                                <span class="ty-color-blue funbtn" data-fun="staffTestScan">查看</span>
                                <span class="hd">${ JSON.stringify({"user": user.userID, "id": result.exam}) }</span>
                             </td>
                        </tr>`;
                        }
                    });
                }
            }
            $("#tb6 tbody>tr:gt(0)").remove();
            $("#tb6 tbody").append(str1 + str2);
        }
    })

}
// create :hxz 2021-3-6 职工补考记录
function staffTestScan(thisObj){
    let sta = thisObj.parent().prev().html();
    let data = JSON.parse(thisObj.siblings(".hd").html());
    $.ajax({
        "url":"../examManage/selectExamAgainDetail.do",
        "data": data ,
        success:function(res) {
            let firstTest = res.respExamformal  ;
            let user = firstTest.user  ;
            let testList = res.respExamForUserList || [];
            bounce.show($("#staffTestScan"));
            $("#staffTestScan .userName").html(`${user.userName} ${user.mobile}`);
            $("#staffTestScan .departName").html(user.departName);
            $("#staffTestScan .postName").html(user.postName);
            $("#staffTestScan .passingScore").html(firstTest.trainingExam.passingScore);
            $("#staffTestScan .score").html(firstTest.trainingExamUser.score);
            $("#staffTestScan .rank").html(firstTest.trainingExamUser.ranking);
            $("#staffTestScan .goal").html(firstTest.trainingExam.goal);
            $("#staffTestScan .endTime").html( new Date(firstTest.trainingExam.endTime).format("yyyy-MM-dd hh:mm:ss"));
            $("#staffTestScan .userName").html(user.userName);
            $("#staffTestScan .stateCur").html(sta);
            $("#staffTestScan .passingState").html(firstTest.trainingExamUser.passingState == 0 ? "还需补考" : "补考已过");
            let str = ``;
            testList.forEach(function(item){
                let test = item.trainingExam  ;
                let result = item.trainingExamUser ;
                str += `<tr>
                            <td>${ new Date(test.endTime).format("yyyy-MM-dd hh:mm") }</td>
                            <td>${ new Date(result.stopTime).format("yyyy-MM-dd hh:mm:ss") }</td>
                            <td>${ parseFloat(((result.stopTime - result.startTime)/1000/60).toFixed(1)) }分钟</td>
                            <td>${Number(result.score)}</td>
                        </tr>`;
            });
            $("#tbTestList tbody>tr:gt(0)").remove();
            $("#tbTestList").append(str);
        }
    });

}
// create :hxz 2021-3-5 查看考核
function testScan(thisObj, type){
    let info = JSON.parse(thisObj.siblings(".hd").html());
    $("#testScan").data("id", info.id);
    let url = `../examManage/selectExamDetail.do`;
    if(type == "makeup") {
        url = `../examManage/selectExamFailDetail.do`;
    }
    $.ajax({
        "url": url,
        "data": { "id":info.id } ,
        success:function(res) {
            if(type == "makeup") {
                if (res.status == -2) {
                    var str = "正在补考,无需再发起补考！"
                    $("#alertTip .tip").html(str);
                    bounce_Fixed2.show($("#alertTip"));
                    return false;
                }
            }
            let info = res.trainingExam || [];
            let banks = res.respExamBankList || [];
            let users = res.respExamUserList || [];
            let questions = res.respExamQuestionList || [];
            $("#testScan .publicity").data("top", info.publicityRanking).html(info.publicity);
            $("#testScan .scanTestPaper").siblings(".hd").html(JSON.stringify(res));
            bounce.show($("#testScan"));
            if(type == "makeup"){
                $("#testScan .scan").hide();
                $("#testScan .edit").hide();
                $("#testScan .makeup").show();
                $("#testScan #date1").val("");
                $("#testScan .bonceHead span").html("发起补考");
            }else{
                $("#testScan .edit").hide();
                $("#testScan .makeup").hide();
                $("#testScan .scan").show();
                $("#testScan .bonceHead span").html("考核查看");
            }
            let c = 0 , t = 0 ;
            banks.forEach(function(item){
                c += Number(item.trainingExamBank.choiceNum);
                t += Number(item.trainingExamBank.tfNum);
            })
            if(info.enabled == 0){
                $("#testScan .stop").html(`本次考核已于 ${ new Date(info.enabledTime).format("yyyy-MM-dd hh:mm:ss")}被${info.createName}终止。`).show();
            }else{
                $("#testScan .stop").hide();
            }
            $("#testScan .usersLen").html(users.length).data("info",users);
            $("#testScan .banksLen").html(banks.length).data("info",banks);
            $("#testScan .target").html(info.goal) ;
            $("#testScan .testInfo").html(`共${c+t}道，其中单选题${c}道，判断题${t}道`) ;
            $("#testScan .answerDuration").html(info.answerDuration + "分钟") ;
            $("#testScan .passingScore").html(info.passingScore + "分") ;
            $("#testScan .endTime").html(new Date(info.endTime).format("yyyy-MM-dd hh:mm")) ;

        }
    });
}
// create :hxz 2021-3-5 各种confirm 判断
function confirmTipOk(){
    let type = $("#confirmTip").data("type");
    let json = $("#confirmTip").data("json");
    switch (type){
        case "testStop": // 终止本次考核
            $.ajax({
                "url":"../examManage/stopExam.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        layer.msg("操作成功");
                        var cur = $("#page1").find(".yecur").html();
                        let jsonObj = JSON.parse($("#page1").find(".json").html());
                        getList(cur, jsonObj);
                    }else{
                        layer.msg("操作失败");
                    }
                    bounce_Fixed2.cancel();
                }
            });
            break;
        default:
    }
}
// create :hxz 2021-3-5 终止本次考核
function testStopJudge(thisObj) {
    let info = JSON.parse(thisObj.siblings(".hd").html());
    $.ajax({
        "url":"../examManage/stopExamJudge.do",
        "data": { "id": info.id } ,
        success:function(res) {
            let status = res.status
            if(status == 1){
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type","testStop");
                let json = { "id":info.id };
                let str = "确定终止本次考核吗？";
                $("#confirmTip").data("json",json);
                $("#confirmTip .tip").html(str);
            }else{
                bounce_Fixed2.show($("#alertTip"));
                let str = "操作失败!<br>因为已有职工开始答题了。"
                $("#alertTip .tip").html(str);
            }
        }
    });
}
function testStop() {


}
// create :hxz 2021-2-24 查看相关题库
function questionBankScan() {
    let list = $("#testScan .banksLen").data("info");
    bounce_Fixed.show($("#usesBanksScan"));
    $("#usesBanksScan .banksCon").show();
    $("#usesBanksScan .usersCon").hide();
    let str = ``;
    if(list[0].trainingQuestionBank){
        list.forEach(function(item){
            str += `<tr>
                    <td>${ item.trainingQuestionBank.code }</td>
                    <td>${ item.trainingQuestionBank.name }</td>
                    <td>${ item.trainingExamBank.choiceNum + item.trainingExamBank.tfNum }</td>
                </tr>`
        })
    }else{
        list = $("#bankList tr:gt(0)");
        list.each(function() {
            str += `<tr>
                    <td>${$(this).children(":eq(0)").html() }</td>
                    <td>${$(this).children(":eq(1)").html() }</td>
                    <td>${$(this).children(":eq(2)").html() }</td>
                </tr>`
        });
    }
    $(".listLen").html(list.length);
    $("#usesBanksScan .bonceHead span").html("相关题库");
    $("#usesBanksScan .banksCon tbody").children(":gt(0)").remove();
    $("#usesBanksScan .banksCon tbody").append(str);
}
// create :hxz 2021-2-24 查看考核对象
function testUserScan() {
    let list = $("#testScan .usersLen").data("info")  ;
    let str = ``;
    if(list.length > 0){
        if(list[0].user.userID){
            list.forEach(function(item) {
                str += ` <tr>
                    <td>${item.user.userName} ${item.user.mobile}</td>
                    <td>部门：${handleNull(item.user.departName)} <br> 岗位：${handleNull(item.user.postName)}</td>
                </tr>`
            });
            $(".listLen").html(list.length);
        }else {
            $(".testers .tester").each(function() {
                let item =  JSON.parse($(this).find(".hd").html());
                str += ` <tr>
                    <td>${item.userName} ${item.mobile}</td>
                    <td>部门：${handleNull(item.departName)} <br> 岗位：${handleNull(item.postName)}</td>
                </tr>`
            });
            $(".listLen").html( $(".testers .tester").length);
        }
    }
    if ($("#testScan .bonceHead span").html() == "发起补考") {
        $("#commScan").hide().siblings().show();
    } else {
        $("#commScan").show().siblings().hide();
    }
    bounce_Fixed.show($("#usesBanksScan"));
    $("#usesBanksScan .banksCon").hide();
    $("#usesBanksScan .usersCon").show();
    $("#usesBanksScan .bonceHead span").html("考核对象");
    $("#usesBanksScan .usersCon tbody").children(":gt(0)").remove();
    $("#usesBanksScan .usersCon tbody").append(str);
}
// create :hxz 2021-2-24 完成，发出考核通知
function sureTest() {
    let endTime = $("#date1").val();
    let time = $("#dateTime").val();
    let dur = $("#answerDuration").val();
    let score = $("#passingScore").val();
    if (endTime =='' || time =='') {
        layer.msg("请录入考核截止时间！")
        return false;
    } else if (dur == '') {
        layer.msg("请录入答卷时间！")
        return false;
    } else if ( score == '') {
        layer.msg("请录入及格分数！")
        return false;
    }
    let finishDate = endTime + ' ' + time+ ':00';
    $.ajax({
        "url":"../examManage/addTrainingExam.do",
        "data": {
            "userList":  JSON.stringify($("#testScan .usersLen").data("info")),
            "examBank":  JSON.stringify($("#testScan .banksLen").data("info")),
            "questionScore":  JSON.stringify($("#editTest2 .editTestOk").data("info")),
            "goal":  $("#testScan .target").html(),
            "publicity":  $("#testScan .publicity").html(),
            "publicityRanking":  $("#testScan .publicity").data("top"),
            "endTime":  finishDate,
            "answerDuration":  dur,
            "passingScore":  score,
        },
        success:function(res) {
            let status = res.status; // 1成功，0失败 -1可选题数不足，请重新生成题库
            bounce.cancel();
            if(status == 1){
                layer.msg("考核通知已发出！")
                let jsonObj = { "type": 3 , "status":1 , "conNum": 1 };
                getList(1, jsonObj);
            }else if(status == -1){
                layer.msg("可选题数不足，请重新生成题库！")
            }else{
                layer.msg("操作失败")
            }

        }
    })
}
// create :hxz 2021-2-23 考核列表
function getList(cur, jsonObj ) {
    // type 排序规则 type 排序规则 1考试截至时间降序排序，2考试截至时间升序排序，
    // 3创建时间降序排序，4创建时间升序排序，5题库数量降序排序，6题库数量升序排序，
    // 7终止时间降序排序，8终止时间升序排序
    // status 1启用状态的，0停用状态的
    let type = jsonObj.type;
    let status = jsonObj.status;
    let conNum = jsonObj.conNum;
    let url = "../examManage/selectExamList.do";
    let data = { "type":type,"status":status,"pageSize":questionPageSize,"currentPageNo":cur };
    $.ajax({
        "url":url,
        "data": data,
        success:function(res) {
            let list = res.trainingExamList|| [], str = ``, pageInfo = res.pageInfo;
            for(let i = 0 ; i < list.length ; i++){
                let item = list[i];
                str += `<tr>
                        <td>${new Date(item.endTime).format("yyyy-MM-dd hh:mm")}</td>
                        <td>${item.createName} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")} </td>
                        ${ status == 0 ? ` <td>${item.createName} ${new Date(item.enabledTime).format("yyyy-MM-dd hh:mm:ss")} </td>` : "" }
                        <td>${item.goal }</td>
                        ${status == 1 ? `<td>${item.bankNum}</td>`: "" } 
                        <td>${item.choiceNum + item.tfNum}</td>
                        <td>
                            <span class="funbtn ty-color-blue" data-fun="testScan">查看</span>
                            ${ status == 1 ? `<span class="funbtn ty-color-red" data-fun="testStop">终止本次考核</span>` : "" }
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                  </tr>`;
            }
            let tbody =  $(".mainCon" + conNum + " table tbody")
            tbody.children("tr:gt(0)").remove();
            tbody.append(str);
            let sumPage = pageInfo.totalPage
            setPage($("#page" + conNum),cur,sumPage,"examList",JSON.stringify(jsonObj));
        }
    })
}
// create :hxz 2021-3-5 已结束的考核
function getFinishList(cur, jsonObj ) {
    // type 排序规则  1考试截至时间降序排序，2考试截至时间升序排序
    let type = jsonObj.type;
    let conNum = jsonObj.conNum;
    let url = "../examManage/selectFinishExamList.do";
    let data = { "type":type,"pageSize":questionPageSize,"currentPageNo":cur };
    if (jsonObj.source == 2) {
        data.peroid = peroidMonth;
        $(".queryByTime").hide();
        $(".byMonth1").hide();
        $(".byMonth2").show();
    } else if (jsonObj.source == 1) {
        $(".byMonth1").show();
        $(".byMonth2").hide();
        $(".queryByTime").show();
        getHostTime(function (hosttime) {
            peroidMonth = new Date(hosttime).format("yyyyMM");
        });
    }
    $.ajax({
        "url":url,
        "data": data,
        success:function(res) {
            let list = res.trainingExamList|| [], str = ``, pageInfo = res.pageInfo;
            var startTime = new Date(Number(res.startTime)).format('yyyy-MM-dd');
            var endTime = new Date(res.endTime).format('yyyy-MM-dd');
            for(let i = 0 ; i < list.length ; i++){
                let item = list[i];
                str += `<tr>
                        <td>${new Date(item.endTime).format("yyyy-MM-dd hh:mm")}</td>
                        <td>${item.goal}</td>
                        <td>${item.userNum}</td>
                        <td>${item.onePassNum}</td>
                        <td>${item.failNum}</td>
                        <td>${item.passingScore}</td>
                        <td>${Number(item.meanScore).toFixed(2)}</td>
                        <td>${item.highestScore}</td>
                        <td>${item.lowestScore}</td> 
                        <td>
                            <span class="funbtn ty-color-blue" data-fun="rankScan">查看考试排名</span>
                            <span class="funbtn ty-color-blue" data-fun="getTestPaper">查看试卷</span>
                            <span class="funbtn ty-color-blue" data-fun="makeupTest">发起补考</span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                    </tr> `;
            }
            let tbody =  $(".mainCon" + conNum + " table tbody")
            tbody.children("tr:gt(0)").remove();
            tbody.append(str);
            $(".mainCon" + conNum + " .infottl").html("以下为"+ startTime + "至"+ endTime +"间已结束的考核");
            let sumPage = pageInfo.totalPage
            setPage($("#page" + conNum),cur,sumPage,"finishedExamList",JSON.stringify(jsonObj));
        }
    })
}
// create : hxz 2021-1-34 显示主页面
function showMainCon(num){
    $("#showMainConNum").val(num);
    $(".mianCon").hide();$(".mainCon" + num).show();
    if (num == 1 || page == "") {
        page = [num];
    } else {
        page.push(num);
    }
    if (num == 4) {
        $("#monthQueryTime").html("");
        $("#yearQueryTime").html("");
    }
}
//  create :hxz 2021-2-23 考核对象可选择列表
function getTestUserList() {
    $.ajax({
        "url":"../examManage/selectExamOptionalPersonnelList.do", 
        success:function(res) {
            let list = res.userList || [] , str = `<option value="">—— 请选择 ——</option>`;
            list.forEach(function(item) {
                str += `<option data-info='${JSON.stringify(item)}' value="${item.userID}">${item.userName}</option>`;
            })
            $("#testUserList").html(str);
        }
    });
}
//  create :hxz 2021-2-23 获取已经选择的考核对象列表
function getTestUsers() {
    let list = [];
    if( $(".testers .tester").length ==0){ return list; }
    $(".testers .tester").each(function() {
        let uItem = JSON.parse($(this).find(".hd").html());
        // let uItem = JSON.parse($(this).find(".hd").html());
        list.push({ "user":uItem.userID });
    })
    return list;
}
//  create :hxz 2021-2-23 可选题库列表
function getBankList() {
    $.ajax({
        "url":"../examManage/selectOptionalQuestionBankList.do",
        success:function(res) {
            let list = res.trainingQuestionBankList || [] , str = ``;
            list.forEach(function(item) {
                str += `<tr>
                            <td class="toggle">
                                <i class="fa fa-square-o"></i>
                                <span class="hd">${JSON.stringify(item)}</span>
                            </td>
                            <td>${ item.code}</td>
                            <td>${ item.name}</td>
                        </tr>`;
            });
            $("#selectBankList tbody").children(":gt(0)").remove();
            $("#selectBankList tbody").append(str);
        }
    });
}
//  create :hxz 2021-2-23 选择题库 -确定
function selectQustionBankOk() {
    let str = `` ;
    let banks = getBanks();
    let repeatBank = ``;
    $("#selectBankList .fa-check-square-o").each(function() {
        let bankItem = JSON.parse($(this).siblings(".hd").html());
        let index = banks.findIndex((item)=>{ return item.bank === bankItem.id});
        if(index > -1){
            repeatBank += `${bankItem.name}(${bankItem.code})<br/>`
        }else{
            bankItem.cNum = 0;
            bankItem.tNum = 0;
            str += `<tr>
                    <td>${bankItem.code}</td>
                    <td>${bankItem.name}</td>
                    <td>0道</td>
                    <td>
                        <span class="funbtn ty-color-blue" data-fun="bankSelectQ">选题</span>
                        <span class="funbtn ty-color-red" data-fun="bankDel">删除</span>
                        <span class="hd">${JSON.stringify(bankItem)}</span>
                    </td>
                </tr>`
        }
    })
    let repeatStr = `您选择的题库都已经选择过，无需再次选择:`;
    if(str.length > 0){
        $("#bankList").append(str).show();
    }else{
        repeatStr = `您选择的题库中如下几个题库已经选择过，<br>无需再次选择:<br>${repeatBank}`
    }
    if(repeatBank.length > 0){
        layer.msg(repeatStr);
    }
    bounce_Fixed.cancel();
}
//  create :hxz 2021-2-23 获取已经选择的题库
function getBanks() {
    let list = [];
    $("#bankList tr:gt(0)").each(function() {
        let bankItem = JSON.parse($(this).find(".hd").html());
        list.push({ "bank": bankItem.id, "choiceNum": bankItem.cNum, "tfNum": bankItem.tNum });
    });
    return list;
}
//  create :hxz 2021-2-23 选择试题 - 选题
function getSelectOptionalQuestionCount() {
    var bankItem = JSON.parse(editBankSelectQ.siblings(".hd").html());
    $("#cNum").val(bankItem.cNum);
    $("#tNum").val(bankItem.tNum);
    $("#selectQustionBank .step1 .code").html(bankItem.code);
    $("#selectQustionBank .step1 .name").html(bankItem.name);
    $("#selectQustionBank .step1 .create").html(`${bankItem.createName}&nbsp;&nbsp;&nbsp;${new Date(bankItem.createDate).format("yyyy-MM-dd hh:mm:ss")}`);

    $.ajax({
        "url":"../examManage/selectOptionalQuestionCount.do",
        "data":{"id": bankItem.id },
        success:function (res) {
            let info = res;
            $("#selectQustionBank .step1 .cNum").html(info.choiceNum);
            $("#selectQustionBank .step1 .tNum").html(info.tFNum);
            $("#selectQustionBank .step1 .sNum").html(info.sum);

        }
    })

}
//  create :hxz 2021-2-23 选择试题 - 确定
function bankSelectQOk() {
    let cNum = Number($("#cNum").val());
    let tNum = Number($("#tNum").val());
    let sNum = Number($("#selectQustionBank .step1 .sNum").html());
    let s = cNum + tNum ;
    if(s > sNum){
        layer.msg(`合计则不超过${sNum}道`);
        return false;
    }
    bounce_Fixed.cancel();
    let bankItem = JSON.parse(editBankSelectQ.siblings(".hd").html());
    bankItem.cNum = Number($("#cNum").val());
    bankItem.tNum = Number($("#tNum").val());
    editBankSelectQ.siblings(".hd").html(JSON.stringify(bankItem));
    editBankSelectQ.parent().prev().html(`${bankItem.cNum + bankItem.tNum}道`);
    $("#fenList").show()
}
// create : hxz 2021-3-4 题已选完，下一步
function selectQuesOk(){
    var banka = getBanks(),cAllnum = 0,tAllnum = 0 ;
    let userLen = $(".testers .tester").length
    let goal = $("#editTest .target").val();
    if(userLen === 0 ){
        layer.msg('请先选择考核对象！')
    }else if(banka.length === 0 ){
        layer.msg('请先单选题库！')
    }else {
        banka.forEach(function(item) {
            cAllnum += Number(item.choiceNum) ;
            tAllnum += Number(item.tfNum) ;
        });
        if(cAllnum === 0 && tAllnum === 0){
            layer.msg('请先选题！')
        }else{
            $("#editTest2 .editTestOk").addClass("bounce-cancel").removeClass("bounce-ok");
            bounce.show($("#editTest2"))
            $("#editTest2 .bonceHead span").html("各道试题的分数");
            bounce.everyTime('1s', 'countFen',countFen);
            $("#cAllnum").html(cAllnum);
            $("#tAllnum").html(tAllnum);
            $("#fenList .num").val("0");
            $("#fenList input[name='c_default']").val(cAllnum);
            $("#fenList input[name='t_default']").val(tAllnum);
            $("#fenList .s").val("0");
        }
    }
}
// create : hxz 2021-1-34 计算总分
function countFen() {
    var sum = 0 ,ch = 0,tf = 0,i = 0, isok = true;
    $("#editTest2 .c").each(function(){
        let numObj = $(this).children("td:eq(1)").children("input");
        let unitObj = $(this).children("td:eq(3)").children("input");
        let num = Number(numObj.val())
        let unit = Number(unitObj.val())
        num = num ? num : 0;
        unit = unit ? unit : 0;
        if(!num){ numObj.val(0);  }
        if(!unit){ unitObj.val(0);  }
        sum += num * unit ;
        if(i++ >1){
            tf += num ;
        }else{
            ch += num;
        }

    })
    $("#editTest2 .d").val(sum);
    let cAllnum = Number($("#cAllnum").html());
    let tAllnum = Number($("#tAllnum").html());
    if(cAllnum !== ch || tAllnum !== tf || sum !== 100){
        isok = false;
    }
    if(isok){
        $("#editTest2 .editTestOk").addClass("bounce-ok").removeClass("bounce-cancel");
    }else{
        $("#editTest2 .editTestOk").removeClass("bounce-ok").addClass("bounce-cancel");

    }
}
function formatminutes(){
    //自定义分钟
    var showtime = $($(".laydate-time-list li ol")[1]).find("li");
    for (var i = 0; i < showtime.length; i++) {
        var t00 = showtime[i].innerText;
        if (t00 != "00" && t00 != "30") {
           //如果不等于0 和 30 分钟删除其余元素
            $(showtime[i]).hide();
        }
    }
    $(".laydate-time-list>li").eq(2).remove();  //清空秒
}
// create：hxz 2021-1-21 字数超出限制
function setWordsNum(thisObj , maxLen){
    let curtxt = thisObj.val()
    if(curtxt.length > maxLen){
        thisObj.val(curtxt.substr(0,maxLen));
        layer.msg(`最多录入${maxLen}个字符`);
    }
    thisObj.parent().find(".lenTip").html( thisObj.val().length + " / " + maxLen);
}
// creator: 李玉婷，2021-07-8 08:49:00，s
function controlNum(obj) {
    clearNum(obj)
    let type = $(obj).data("type");
    let objNum =  Number($(obj).val());
    let cNumM = Number($("#selectQustionBank .cNum").html());
    let tNumM = Number($("#selectQustionBank .tNum").html());
    let maxNum = objNum;
    let limitNum = Number($("#selectQustionBank .sNum").html());
    let count = objNum;
    if (type == 1) {
        maxNum = cNumM;
        count += Number($("#tNum").val());
    } else if (type == 2) {
        maxNum = tNumM;
        count += Number($("#cNum").val());
    }
    if (objNum > maxNum || count > limitNum){
        layer.msg('本试题库下的素材数量不足');
        $(obj).val("");
    }
}
// creator: 李玉婷，2021-10-11 18:31:56，获取当前时间
function nowTime(){
    return new Date().format("hh:mm:ss")
}
// creator: 李玉婷，2021-11-23 16:08:55，切换为按职工查看
function byEmployeeScan() {
    showMainCon(7);
    $.ajax({
        "url":"../examManage/selectExamUserInfoList.do",
        "data":{"peroid": peroidMonth },
        success:function (res) {
            let info = res;
            let str = ``,list = res.examUserInfoList || [];
            var startTime = new Date(res.startTime).format('yyyy-MM-dd');
            var endTime = new Date(res.endTime).format('yyyy-MM-dd');
            $(".mainCon7 .infottl").html(startTime + "至"+ endTime +"间已结束的考核统计表");
            for(let i = 0 ; i < list.length ; i++){
                let item = list[i];
                item.startTime = startTime;
                item.endTime = endTime;
                str += `<tr>
                        <td>${item.userName} ${item.mobile}</td>
                        <td>${handleNull(item.departName)} ${handleNull(item.postName)}</td>
                        <td>${item.examTotal}</td>
                        <td>${item.examPassing}</td>
                        <td>${item.answerTimes}</td>
                        <td>${Number(item.meanScore).toFixed(2)}</td>
                        <td>${item.highestScore}</td>
                        <td>${item.lowestScore}</td>
                        <td>${item.highestRanking}</td> 
                        <td>${item.lowestRanking}</td> 
                        <td>
                            <span class="funbtn ty-color-blue" data-fun="assessmentRecordScan">查看详情</span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                    </tr> `;
            }
            $(".mainCon7 table tr:gt(0)").remove();
            $(".mainCon7 table").append(str);
        }
    })
}
// creator: 李玉婷，2022-01-21 08:03:31，修改考核公示的设置
function publicitySettingSure() {
    var len = $("#publicitySetting .pubSet .fa-dot-circle-o").length;
    if (len > 0) {
        var publicity = "",publicityRanking = 0;
        var flag = $("#publicitySetting .pubSet .fa-dot-circle-o").data("type");
        if (flag == 1) {
            publicity = "本次考核的结果不公示。";
        } else {
            publicityRanking = $("#publicitySetting .topFew").val();
            publicity = "成绩前"+ numberLowercase(publicityRanking) +"名将给予公示，但无奖励。";
        }
       if ($("#testScan .bonceHead span").html() == "考核查看") {
            var param = {
                "id": $("#testScan").data("id"),
                "publicity": publicity,
                "publicityRanking": publicityRanking
            }
            $.ajax({
                "url":"../examManage/updatePublicity.do",
                "data":param,
                success:function (res) {
                }
            })
        }
        $("#testScan .publicity").data("top", publicityRanking).html(publicity);
        bounce_Fixed.cancel();
    }
}
// creator: 李玉婷，2022-01-25 17:49:56，职工考核记录
function assessmentRecordScan(obj) {
    var info = JSON.parse(obj.siblings(".hd").html());
    var param = {
        "userId": info.userId,
        "peroid": peroidMonth
    }
    $("#record_staff").html(info.userName + " " +info.mobile);
    $("#record_depart").html(handleNull(info.departName));
    $("#record_post").html(handleNull(info.postName));
    $("#record_peroid").html(handleNull(info.startTime) + '—'+handleNull(info.endTime));
    bounce.show($("#assessmentRecordScan"));
    $("#assessmentRecordScan table:eq(1) tr:gt(0)").remove();
    $.ajax({
        "url":"../examManage/selectExamUserInfo.do",
        "data":param,
        success:function (res) {
            let list = res.trainingExamUserList,listStr = ``;
            let topTb =
                `<tr>
                    <td>参加考核总次数</td>
                    <td>一次通过的次数</td>
                    <td>补考合计</td>
                </tr>
                <tr>
                    <td>${res.examTotal}</td>
                    <td>${res.examPassing}</td>
                    <td>${res.answerTimes}</td>
                </tr>
                <tr>
                    <td>最高分数</td>
                    <td>最低分数</td>
                    <td>平均分数</td>
                </tr>
                <tr>
                    <td>${res.highestScore}</td>
                    <td>${res.lowestScore}</td>
                    <td>${Number(res.meanScore).toFixed(2)}</td>
                </tr>
                <tr>
                    <td>最高名次</td>
                    <td>最低名次</td>
                    <td>总排名</td>
                </tr>
                <tr>
                    <td>${res.highestRanking}</td>
                    <td>${res.lowestRanking}</td>
                    <td></td>
                </tr>`;
            $("#assessmentRecordScan table:eq(0)").html(topTb);
            if (list && list.length > 0) {
                var redClass = '';
                for(var i=0;i<list.length;i++){
                    if (list[i].passingState == '0') {
                        redClass = 'red';
                    } else {
                        redClass = '';
                    }
                    listStr +=
                        `<tr class="${redClass}">
                            <td>${new Date(list[i].examStopTime).format('yyyy-MM-dd hh:mm')}</td>
                            <td>${new Date(list[i].stopTime).format('yyyy-MM-dd hh:mm')}</td>
                            <td>${parseFloat(((list[i].stopTime - list[i].startTime)/1000/60).toFixed(1))}分钟</td>
                            <td>${handleNull(list[i].ranking)}</td>
                            <td>${list[i].score}</td>
                        </tr>`;
                }
            }
            $("#assessmentRecordScan table:eq(1)").append(listStr);
        }
    })
}
// creator: 李玉婷，2022-01-25 09:57:02，按年统计已完成考核
function getFinishYearList(cur, jsonObj) {
// type 排序规则  1考试截至时间降序排序，2考试截至时间升序排序
    let type = jsonObj.type;
    let conNum = jsonObj.conNum;
    let url = "../examManage/selectFinishExamListByYear.do";
    let data = { "type":type,"pageSize":questionPageSize,"currentPageNo":cur,"year":peroidYear };
    $.ajax({
        "url":url,
        "data": data,
        success:function(res) {
            let list = res.trainingExamList|| [], str = ``, pageInfo = res.pageInfo;
            var startTime = new Date(res.startTime).format('yyyy-MM-dd');
            var endTime = new Date(res.endTime).format('yyyy-MM-dd');
            for(let i = 0 ; i < list.length ; i++){
                let item = list[i];
                str += `<tr>
                        <td>${new Date(item.endTime).format("yyyy-MM-dd hh:mm")}</td>
                        <td>${item.goal}</td>
                        <td>${item.userNum}</td>
                        <td>${item.onePassNum}</td>
                        <td>${item.passingScore}</td>
                        <td>${Number(item.meanScore).toFixed(2)}</td>
                        <td>${item.highestScore}</td>
                        <td>${item.lowestScore}</td> 
                        <td>
                            <span class="funbtn ty-color-blue" data-fun="rankScan">查看考试排名</span>
                            <span class="funbtn ty-color-blue" data-fun="makeupTest">发起补考</span>
                            <span class="hd">${JSON.stringify(item)}</span>
                        </td>
                    </tr> `;
            }
            let tbody =  $(".mainCon" + conNum + " table tbody")
            tbody.children("tr:gt(0)").remove();
            tbody.append(str);
            $(".mainCon" + conNum+ " .infottl").html("以下为"+ startTime + "至"+ endTime +"间已结束的考核");
            let sumPage = pageInfo.totalPage
            setPage($("#page" + conNum),cur,sumPage,"finishedExamList",JSON.stringify(jsonObj));
        }
    })
}
// creator: 李玉婷，2022-01-25 13:12:06，查看月统计
function byMonthQueryScan() {
    let tbody =  $(".mainCon9 table")
    tbody.find("tr:gt(0)").remove();
    $.ajax({
        "url":"../examManage/selectExamListByMonth.do",
        "data":{"year": peroidYear},
        success:function (res) {
            let list = res.peroidExamList|| [], str = ``;
            for(let i = 0 ; i < list.length ; i++){
                let item = list[i];
                str += `<tr>
                        <td>${item.month}</td>
                        <td>${item.sum}次</td>
                        <td>${item.sum}次</td>
                    </tr> `;
            }
            tbody.append(str);
        }
    })
}
// creator: 李玉婷，2022-01-26 18:15:13，返回上一页
function backPre(){
    if (page.length >= 2) {
        var num = page[page.length-2];
        page.splice(page.length-2,2);
        showMainCon(num);
    }
}

// creator: 李玉婷，2022-01-21 09:39:26，汉字小写转换
function numberLowercase(n) {
    //汉字的数字
    var cnNums = new Array('','','','三', '四', '五');
    return cnNums[parseInt(n)];
}
// creator: 李玉婷，2022-01-28 09:26:27，最小值设置
function setYearMinTime(type) {
    var loginUser = auth.getUser();
    var min = '';
    if (type == 1) {
        min = new Date(loginUser.createTime).format('yyyy') ;
        min = min + '-01-01';
    } else {
        min = new Date(loginUser.createTime).format('yyyy-MM') ;
        min = min + '-01';
    }
    return min;
}


laydate.render({
    elem: '#date1',
    min: nowTime(),
    format: 'yyyy-MM-dd',
    showBottom: false ,
    done: function (value, date, endDate) {
        lay('#dateTime').val("00:00");
    }
});
laydate.render({
    elem: '#yearQueryTime' //指定元素
    ,eventElem: '#yearQuery' //绑定执行事件的元素
    ,type: 'year'
    ,min: setYearMinTime(1)
    ,max: 'date'
    ,btns: [ 'confirm']
    ,done: function (value, date, endDate) {
        peroidYear = value;//+ '01'
        showMainCon(8) ;
        getFinishYearList(1, { "type": 1 , "conNum": 8  });
    }
});
laydate.render({
    elem: '#monthQueryTime' //指定元素
    ,eventElem: '#monthQuery' //绑定执行事件的元素
    ,type: 'month'
    ,format: 'yyyyMM'
    ,min: setYearMinTime(2)
    ,max: 'date'
    ,btns: ['confirm']
    ,done: function (value, date, endDate) {
        peroidMonth = value;
        showMainCon(4) ;
        $(".mainCon4 .fa").addClass("fa-long-arrow-down").removeClass("fa-long-arrow-up");
        getFinishList(1, { "type": 1 , "conNum": 4, "source": 2  });
    }
});
laydate.render({
    elem: '#dateTime',
    type: 'time',
    format: 'HH:mm',
    ready: function(value, date, endDate){
        formatminutes();//此方法里写方案三中的分钟转换成半小时
        //var timeDom = $('.layui-laydate').find("span[lay-type='datetime']")[0];
        //$(timeDom).click(function(){
        //    formatminutes();//此方法里写方案三中的分钟转换成半小时
        //});
    },
    change: function(value){
        lay('#dateTime').val(value);
        $('.laydate-time-list li ol').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
        })
    }
});
