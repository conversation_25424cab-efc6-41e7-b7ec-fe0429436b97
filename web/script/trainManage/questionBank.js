/*
*  试题库
*
* */
var questionPageSize = 20;
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#alertTip"));
bounce_Fixed2.cancel();
$(function () {
    let jsonObj = { "type": 3 , "status":1 , "conNum": 1 };
    getList(1, questionPageSize, jsonObj);
    $("#showMainConNum").val(1);
    initUpload($("#uploadFile"));
    // 排列 升序降序
    $(".arrowBtn").click(function() {
        let type = $(this).data("type");
        let o = $(this).children("i");
         let down = o.hasClass("fa-long-arrow-down");
        if(down){
            o.attr("class","fa fa-long-arrow-up");
        }else {
            o.attr("class","fa fa-long-arrow-down");
        }
        let typeNum = 1 ;
        switch (type){
            case "code":
                if(down){
                    typeNum = 2 ;
                }else {
                    typeNum = 1 ;
                }
                break;
            case "create":
                if(down){
                    typeNum = 4 ;
                }else {
                    typeNum = 3 ;
                }
                break;
            case "sourceNum":
                if(down){
                    typeNum = 6 ;
                }else {
                    typeNum = 5 ;
                }
                break;
            default:
        }
        let mainConNum = $("#showMainConNum").val()
        let jsonObj = JSON.parse($("#page" + mainConNum).find(".json").html());
        jsonObj["type"] = typeNum;
        getList(1, questionPageSize, jsonObj);
    });
    $(".arrowSource").click(function() {
        let type = $(this).data("type");
        let o = $(this).children("i");
         let down = o.hasClass("fa-long-arrow-down");
        if(down){
            o.attr("class","fa fa-long-arrow-up");
        }else {
            o.attr("class","fa fa-long-arrow-down");
        }
        let typeNum = 1 ;
        switch (type){
            case "code":
                if(down){
                    typeNum = 2 ;
                }else {
                    typeNum = 1 ;
                }
                break;
            case "create":
                if(down){
                    typeNum = 4 ;
                }else {
                    typeNum = 3 ;
                }
                break;
            case "questionNum":
                if(down){
                    typeNum = 6 ;
                }else {
                    typeNum = 5 ;
                }
                break;
            default:
        }
        let mainConNum = $("#showMainConNum").val()
        let jsonObj = JSON.parse($("#page" + mainConNum).find(".json").html());
        jsonObj["type"] = typeNum;
        getSourceList(1, questionPageSize, jsonObj);
    });
    // 新增判断题切换
    $(".tfType").click(function(){
        if ($(this).attr("disabled") !="disabled") {
            $(".tfType i").attr("class","fa fa-circle-o");
            $(this).children("i").attr("class","fa fa-dot-circle-o");
            let val = $(this).data("val");
            $("#newTF").show();
            $("#newTF").val($("#editSource .souceContent").html());
            if(val ==  1){ // 正确
                $("#newTF").attr("disabled",true);
            }else {
                $("#newTF").removeAttr("disabled");
            }
        }
    });
    // 按钮
    $("body").on("click", ".ty-btn,.btnCat,.bonceHead>a", function () {
        var fun = $(this).data("fun");
        var jsonObj = { "type": 3 , "status":1 , "conNum": 1 };
        switch (fun){
            case 'bankEdit': // 修改题库
                let fileHtml = ``, files = [];
                $(".scanFileList .attachItem").each(function(){
                    let res = JSON.parse($(this).find(".hd").html())
                    let filetype = res.path.split('.').pop();
                    let fileTypeStr = chargeFileType(filetype)
                    fileHtml +=`<div class="attachItem">
                            <div class="fa ${fileTypeStr}"></div>
                            <div>
                                <p title="${res.title}" class="fileName">${res.title}</p>
                                <div class="fileSize">${formatSize(res.size)}</div>
                            </div>
                            <div>
                                <span class="redLink">删除</span>
                                <span class="hd">${JSON.stringify(res)}</span>
                            </div>
                        </div>`;
                    files.push(res.id);
                });
                $(".attachList").data("org", files);
                $('.attachList').html(fileHtml).data("del","");
                bounce.show($("#editQuestionBank"));
                $("#bankName").val( $("#bankInfo .bankNm").html() ).data("org",$("#bankInfo .bankNm").html());
                $("#editQuestionBank .bankCode").html( $("#bankInfo .bankCode").html() );
                $("#editQuestionBank .lenTip").html( $("#bankInfo .bankNm").html().length+' / 15' );
                $("#editQuestionBank .bonceHead span").html("修改题库基本信息");
                break;
            case 'backSourceInfo': // 返回 
                bounce_Fixed2.show($("#cancelTip"));
                break;
            case 'backSourceInfoSure': // 返回
                bounce_Fixed.cancel();
                bounce_Fixed2.cancel();
                bounce.show($("#editSource"));
                break;
            case 'base': // 返回题库基本信息修改记录
                bounce.cancel();
                break;
            case 'backBankEditLogList': // 返回题库基本信息修改记录
                bounce.show($("#bankEditLog"));
                break;
            case 'backBankScan': // 返回题库基本信息查看
                bankInfo();
                break;
            case 'backOn': // 关闭本页 - 返回未停用的列表
                $(".offOption").hide(); $(".onOption").show();
                $("#questionScan .option").hide();
                $("#questionScan .bonceHead span").html("单选题查看");
                var optionJson = $("#questionScan").data('json');
                getOptionList({ "id": optionJson.id, "status": 1 });
                break;
            case 'back1': // 返回首页
                showMainCon(1);
                var cur = $("#page1").find(".yecur").html();
                jsonObj = JSON.parse($("#page1").find(".json").html());
                getList(cur, questionPageSize, jsonObj);
                break;
            case 'back3': // 返回停用首页
                showMainCon(3);
                var cur = $("#page3").find(".yecur").html();
                jsonObj = JSON.parse($("#page3").find(".json").html());
                getList(cur, questionPageSize, jsonObj);
                break;
            case 'back5': // 题库详情的返回
                let num = $(this).data('num');
                //let sta = 1;
                showMainCon(num);
                var cur = $("#page"+ num).find(".yecur").html();
                /*if (num == 3 || num == 4) {
                    sta = 0;
                }*/
                jsonObj = JSON.parse($("#page" + num).find(".json").html());
                if (num == 2) {
                    jsonObj.keyword = $(".mainCon1 .keywordSearch input").val();
                } else if (num == 4){
                    jsonObj.keyword = $(".mainCon3 .keywordSearch input").val();
                }
                getList(cur, questionPageSize,jsonObj);
                break;
            case 'goStop': // 已停用的题库
                showMainCon(3);
                jsonObj = { "type": 3 , "status":0 , "conNum": 3 };
                getList(1, questionPageSize, jsonObj);
                break;
            case 'searchBtn1': // 首页 搜索
                showMainCon(2);
                var keyword = $(this).siblings().find("input").val()
                $("#searchKey").val(keyword) ; // 所有的搜索都存这里
                jsonObj = { "type": 3 , "status":1 , "conNum": 2, "keyword": keyword};
                getList(1, questionPageSize, jsonObj);
                break;
            case 'searchBtn2': // 停用页 搜索
                showMainCon(4);
                var stopKeyword = $(this).siblings().find("input").val()
                $("#searchKey").val(stopKeyword) ; // 所有的搜索都存这里
                jsonObj = { "type": 3 , "status":0 , "conNum": 4, "keyword": stopKeyword};
                getList(1, questionPageSize, jsonObj);
                break;
            case 'back4': // 返回素材停用首页
                showMainCon(6);
                var cur = $("#page6").find(".yecur").html();
                var id = $(".mainCon5").data("id");
                jsonObj = JSON.parse($("#page6").find(".json").html());
                jsonObj.id = id;
                getSourceList(cur, questionPageSize, jsonObj);
                break;
            case 'add': // 新增题库
                bounce.show($("#editQuestionBank"));
                $("#editQuestionBank input").val("")
                $("#editQuestionBank .bonceHead span").html("新增题库");
                getBankCode()
                $(".attachList").html("").data("del","")
                break;
            case 'save': // 保存题库
                questionBankSave();
                break;
            case 'addSource': // 新增素材
                bounce.show($("#editSource"));
                $("#editSource .bonceHead span").html("新增素材");
                $("#editSource .step0").show().siblings().hide();
                $("#sourceTxt").val("");
                break;
            case 'saveSource': // 保存素材
                saveSource();
                break;
            case 'nextStep': // 开始制作单选或判断
                nextStep();
                break;
            case 'saveQuestion1': // 保存单选题
                saveQuestion1();
                break;
            case 'saveQuestion2': // 保存判断题
                saveQuestion2Btn();
                break;
            case 'confirmTipOk': // confirmTipOk
                confirmTipOk();
                break;
            case 'stopSourceList': //  已停用的素材
                showMainCon(6);
                var id = $(".mainCon5").data("id");
                var souceJson = { 'id': id, 'type': 3 , 'status': 0, "conNum":6 }
                getSourceList(1,questionPageSize,souceJson);
                break;
            case 'back6':
                showMainCon(5);
                var souceJson = JSON.parse($("#page5").find(".json").html());
                var cur = $("#page5").find(".yecur").html();
                getSourceList(cur,questionPageSize,souceJson);
                break;
            case 'allUsingSource': //  全部在用素材
                showMainCon(7);
                var id = $(".mainCon5").data("id");
                var souceJson = { 'id': id, 'type': 3 , 'status': 1, "conNum":7 }
                getSourceList(1,questionPageSize,souceJson);
                break;
            case 'searchSource': //  mainCon5 素材查找
                showMainCon(8);
                var key = $(this).siblings().find("input").val();
                var id = $(".mainCon5").data("id");
                var souceJson = { 'id': id, 'type': 3 , 'status': 1, "conNum":8 , "keyword":key }
                getSourceList(1,questionPageSize,souceJson);
                break;
            case 'searchSource2': //   mainCon6 停用素材查找
                showMainCon(9);
                var key = $(this).siblings().find("input").val();
                var id = $(".mainCon5").data("id");
                var souceJson = { 'id': id, 'type': 3 , 'status': 0, "conNum":9 , "keyword":key }
                getSourceList(1,questionPageSize,souceJson);
                break;
            case 'goSourceInfo': //  返回素材查看
                bounce.show($("#editSource"));
                var json = $("#questionList1").data("json");
                json.status = 1 ;
                getQuestionList(json);
                $("#questionList1").data("json", json);
                break;
            case "saveOption": // 新增干扰项
                saveOption();
                break;
            case "bankInfo": // 题库基本信息
                bankInfo();
                break;
            default:
        }

    });
    // linkBtn 按钮
    $(".linkBtn").click(function () {
        let fun = $(this).data("fun");
        switch (fun){
            case 'newAttachBtn': // 增加附件
                $("#addAttach .fa").removeClass("fa-dot-circle-o").addClass("fa-circle-o")
                bounce_Fixed.show( $("#addAttach") );
                break;
            case 'addOption': // 新增干扰项
                let length = $("#qtTb1 tbody tr").length;
                if (length < 5) {
                    bounce_Fixed.show( $("#questionScan") );
                    $("#questionScan .bonceHead span").html("新增干扰项");
                    $("#questionScan .option").show().siblings().hide();
                    $("#questionScan .option textarea").val("");
                } else {
                    bounce_Fixed2.show($("#alertTip"));
                    let str = "系统支持一道单选题最多可有五个干扰项。<br>已经有五个了。";
                    $("#alertTip .tip").html(str);
                }
                break;
            case 'stopOption': // 查看已停用的干扰项
                bounce_Fixed.show( $("#questionScan") );
                var optionJson = $("#questionScan").data('json');
                optionJson.status = 0;
                $("#questionScan .bonceHead span").html("已停用的干扰项");
                $("#questionScan .onOption").hide();
                $("#questionScan .offOption").show();
                $("#questionScan .option").hide();
                $("#questionScan").data('json',optionJson);
                getOptionList(optionJson);
                break;
            case 'showStopQuetion': // 查看已停用的试题
                let jsonObj =  $("#questionList1").data("json");
                jsonObj['status'] = 0
                $("#questionList1").data("json", jsonObj);
                getQuestionList(jsonObj);
                bounce.show($("#stopQuestion"));
                break;
            case 'bankEditLog': // 题库修改记录接口
                bankEditLog()
                break;
            default:
                break;
        }
    })
    // 附件删除
    $(".attachList").on("click",".redLink", function () {
        let hdData = JSON.parse($(this).siblings(".hd").html());
        if (hdData && hdData.id) {
            let delList = [];
            if ($(".attachList").data("del") != "") {
                delList = $(".attachList").data("del");
            }
            delList.push({id: hdData.id});
            $(".attachList").data("del", delList);
        } else {
            if ($(this).attr('fileUid')) {
                var option = {type: 'fileUid', fileUid: $(this).attr('fileUid')}
                fileDelAjax(option)
            }
        }

        $(this).parents(".attachItem").remove();
    });
    // table里的操作按钮
    $("table").on("click", ".funbtn", function(){
        let fun = $(this).data("fun");
        switch (fun){
            case "bankInfoLogScan": // 题库修改记录查看
                bankInfoLogScan($(this));
                break;
            case "bankScan": // 题库查看
                $(".mainCon5 .keywordSearch input").val("")
                $(".mainCon5 .back5").data("num", $("#showMainConNum").val())
                showMainCon(5);
                bankScan($(this));
                break;
            case "bankStop":
            case "bankStart":
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type",fun);
                let info = JSON.parse($(this).siblings(".hd").html());
                let json = { "id":info.id , "enabled":0 };
                let str = "确定后，生成试卷时将不再选择该题库中的试题。<br/>确定停用该题库吗？";
                if(fun == "bankStart"){
                    str = "确定后，生成试卷时本题库内的试题将重新可能被选择。<br/>确定启用本题库吗？";
                    json.enabled = 1;
                }
                $("#confirmTip").data("json",json);
                $("#confirmTip .tip").html(str);
                break;
            case "bankDel":
                bankDelJudge($(this));
                break;
            case "sourceScan": // 查看素材
                showQuestionCon();
                let info4 = JSON.parse($(this).siblings(".hd").html());
                let json4 = { "id":info4.id , "status":1};
                $("#questionList1").data("json", json4);
                getQuestionList(json4);
                break;
            case "sourceStop": // 停用素材
            case "sourceStart": // 启用素材
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type",fun);
                let info2 = JSON.parse($(this).siblings(".hd").html());
                let json2 = { "id":info2.id , "enabled":0 };
                let str2 = "确定后，生成试卷时将不再选择该素材下的试题。<br>确定停用该素材吗？";
                if(fun == "sourceStart"){
                    str2 = "确定后，生成试卷时本素材下的试题将重新可能被选择。<br>确定启用本素材吗？";
                    json2.enabled = 1;
                }
                $("#confirmTip").data("json",json2);
                $("#confirmTip .tip").html(str2);
                break;
            case "sourceDel": // 删除素材
                sourceDelJudge($(this));
                break;
            case "questionScan": // 题目查看
                questionScan($(this));
                break;
            case "questionStop":  // 题目停启用
            case "questionStart":
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type",fun);
                let info3 = JSON.parse($(this).siblings(".hd").html());
                let json3 = { "id":info3.id , "enabled":0 };
                let str3 = "确定后，生成试卷时将不再会选择该试题。<br>确定停用该试题吗？";
                if(fun == "questionStart"){
                    str3 = "确定后，该题将可能成为新试题。<br>确定启用该试题吗？";
                    json3.enabled = 1;
                }
                $("#confirmTip").data("json",json3);
                $("#confirmTip .tip").html(str3);
                break;
            case "questionDel": // 题目删除
                questionDelJudge($(this));
                break;
            case "optionStop": // 干扰项停用
            case "optionStart": // 干扰项停用
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type",fun);
                let infooption = JSON.parse($(this).siblings(".hd").html());
                let jsonoption = { "id":infooption.id , "enabled":0 };
                let stroption = "确定后，该干扰项将不再成为新试题的备选项。<br>确定停用该干扰项吗？";
                if(fun == "optionStart"){
                    stroption = "确定后，该干扰项将可能成为新试题的备选项。<br>确定启用该干扰项吗？";
                    jsonoption.enabled = 1;
                }
                $("#confirmTip").data("json",jsonoption);
                $("#confirmTip .tip").html(stroption);
                break;
            case "optionDel": // 干扰项删除
                optionDelJudge($(this));
                break;
        }
    });
});
//  create :hxz 2021-1-21 新增素材
function saveSource() {
    let content = $("#sourceTxt").val();
    let bankId = $(".mainCon5").data("id");
    $.ajax({
        "url":"../trainManage/addTrainingMaterial.do",
        "data":{ "content":content, "bank":bankId },
        success:function(res){
            let status = res.status
            if(status == 1){
                layer.msg("操作成功");
                // 刷新列表
                refreshSource();
                $("#editSource .step1").show().siblings().hide();
                $("#questionTypeCon").hide();
                $(".firstTime").show(); $(".moreTime").hide();
                let code = res.code
                let id = res.id
                $("#editSource .souceCode").html(code);
                $("#editSource .souceContent").html(content);
                $("#editSource .fa-dot-circle-o").attr("class","fa fa-circle-o");
                $("#questionList1").data("json",{ "id":id, "status":1})
            }else{
                layer.msg("操作失败");
            }
        }
    })
}
//  create :hxz 2021-1-21 保存单选题
function saveQuestion1() {
    let material = $("#editQuestion").data("material");
    let contentOral = $("#editQuestion .editS").html();
    let content = $("#editQuestion .editS").html();
    let options = [];
    $(".qt1 .interfereOptionList .interfereOption").each(function(){
        let txt =  $(this).find("textarea").val();
        if(txt.length > 0){
            options.push({ "content": txt, "isKey":0});
        }
    });
    if(options.length < 3){
        bounce_Fixed2.show($("#alertTip"));
        let str = "正确项与干扰项均需制作，且干扰项至少需制作三个。<br/>否则不可保存！";
        $("#alertTip .tip").html(str);
        return false;
    }
    let curRightTxt = $(".qt1 .curRight").html();
    content = content.replace('<span class="ty-color-red">'+ curRightTxt + '</span>','（&nbsp;&nbsp;）');
    if(curRightTxt.length >0){
        options.push({ "content": curRightTxt, "isKey":1 })
    }else{
        bounce_Fixed2.show($("#alertTip"));
        let str = "正确项与干扰项均需制作，且干扰项至少需制作三个。<br/>否则不可保存！";
        $("#alertTip .tip").html(str);
        return false;
    }
    $.ajax({
        "url":"../trainManage/addChoiceQuestion.do",
        "data":{ "material":material, "content":content, "optionList":JSON.stringify(options) },
        success:function(res){
            let status = res.status
            let code = res.code
            if(status == 1){
                bounce_Fixed2.show($("#saveTip"));
                // 刷新列表
                refreshQuestion();
                showQuestionCon();
                refreshSource();
            } else if(status == -2){
                layer.msg("选项有重复无法新增");
            } else{
                layer.msg("操作失败");
            }
        }
    })
}
//  create :hxz 2021-2-19 保存判断题1
function saveQuestion2Btn() {
    let material = $("#editQuestion").data("material");
    let isFirst = $("#editQuestion").data("isFirst"); // 1-第一次；0-不是第一次
    let contentList = [];
    let str = "您没有制作标准答案为“错误”的备选题。<br>题库中将仅有一道标准答案为“正确”的试题！";
    let rightStr = $(".qt2 .curRight").html();
    if(isFirst === 1){
        $(".qt2 .interfereOptionList .interfereOption").each(function(){
            let textarea =  $(this).find("textarea");
            let txt = textarea.val();
            if(txt.length > 0 && $.trim(txt) !== rightStr){
                contentList.push({ "content":$.trim(txt), "isKey":0});
            }
        });
        if(contentList.length > 0){
            str = `除标准答案为“正确”的试题外，<br>系统还将生成标准答案为“错误”的试题${contentList.length}道！`;
        }
        // else{
        //     layer.msg("您还没有录入新的答案，无需保存！");
        //     return false;
        // }
        contentList.push({ "content": rightStr, "isKey":1 });

    }else{ // 不是第一次
        let souceContent = $(".qt2 .souceContent").html();
        let val = $(".tfType i.fa-dot-circle-o").parent(".tfType").data("val");
        if(val== 1 || val==0 && $("#newTF").val() !== souceContent){
            str = `系统将生成一道标准答案为“${val ==1 ? "正确":"错误"}”的试题！`;
            contentList.push({ "content": $("#newTF").val(), "isKey":val });
        } else {
            bounce_Fixed2.show($("#alertTip"));
            let str = "您还没有编辑新内容！";
            $("#alertTip .tip").html(str);
            return false;
        }
    }

    bounce_Fixed2.show($("#confirmTip"));
    $("#confirmTip").data("type","saveQuestion2");
    let json ={ "material":material, "contentList":JSON.stringify(contentList) };
    $("#confirmTip").data("json",json);
    $("#confirmTip .tip").html(str);
}
//  create :hxz 2021-2-19 显示题目列表页
function showQuestionCon() {
    bounce_Fixed.cancel();
    bounce.show($("#editSource"));
    $("#editSource .step1").show().siblings().hide();
    $("#questionTypeCon").hide();$(".firstTime").hide(); $(".moreTime").show();
    $("#editSource .step1 .fa").attr("class", "fa fa-circle-o");
    $("#editSource .bonceHead span").html("素材查看");
}
//  create :hxz 2021-1-21 设置正确项
function setNormal(thisObj){
    let redObj = thisObj.find('.ty-color-red');
    redObj.after(redObj.html());
    redObj.remove();
}
function getHightLight(thisObj){
    var sel = window.getSelection ? window.getSelection() : document.selection.createRange(); // FF : IE
    if(sel.getRangeAt){ // thats for FF
        var range = sel.getRangeAt(0);
        var newNode = document.createElement("span");
        newNode.setAttribute('class', 'ty-color-red');
        range.surroundContents(newNode);
    } else {
        sel.pasteHTML('<span class="ty-color-red">'+sel.htmlText+'</span>');
    }
    let ri = thisObj.find(".ty-color-red").html();
    $(".curRight").html(ri);
}
// create :hxz 2021-1-21  开始制作单选或判断
function nextStep() {
    // $("#questionTypeCon .fa-dot-circle-o").show().siblings().hide();
    if($("#questionTypeCon .fa-dot-circle-o").length > 0){
        let val = $("#questionTypeCon .fa-dot-circle-o").parent("span").data("val");
        let info4 = $("#questionList1").data("json");
       let material = info4.id;
        $.ajax({
            "url":"../trainManage/addQuestionJudge.do",
            "data":{ "material": material, "type":val },
            success:function(res) {
                let status = res.status
                if(status ==1 || status ==2){
                    let ttl = "", str = '';
                    if(val == '1'){ // 单选
                        $("#editQuestion .editS").html($("#editSource .souceContent").html());
                        $("#editQuestion .curRight").html("");

                        ttl = "制作单选题";
                        for(var i = 1 ; i <6; i++){
                            str += `
                                <div class="form-group interfereOption">
                                     <label>干扰项${i}</label>
                                     <textarea class="form-control" placeholder="请录入干扰项内容，字数上限为80个。" onkeyup="setWordsNum($(this),80)"></textarea>
                                     <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                                </div>`
                        }
                    }else {  // 判断
                        ttl = "制作判断题";
                        if(status ==2){ // 单个增加判断题
                            let yesNum = res.yesNum; // 1为有正确的，0为没有正确的
                            $("#editQuestion").data("isFirst",0);
                            $("#editQuestion .souceCode").html($("#editSource .souceCode").html())
                            $("#editQuestion .souceContent").html($("#editSource .souceContent").html())
                            $(".tfType i").attr("class","fa fa-circle-o");
                            $("#newTF").hide();
                            $(".notFirst").show(); $(".isFirst").hide();
                            if (yesNum == 1) {
                                $(".tfType").eq(0).attr("disabled","disabled");
                            }else{
                                $(".tfType").eq(0).removeAttr("disabled");
                            }
                        }else{
                            $(".isFirst").show(); $(".notFirst").hide();
                            $("#editQuestion").data("isFirst",1);
                            let sourcetxt = $("#editSource .souceContent").html();
                            $("#editQuestion .qt2 .curRight").html(sourcetxt);
                            for(var i = 1 ; i <4; i++){
                                str += `
                                <div class="form-group interfereOption">
                                     <label>标准答案为“错误”的备选题${i}</label>
                                     <textarea class="form-control" onkeyup="setWordsNum($(this),80)">${sourcetxt}</textarea>
                                     <i class="fa fa-times clearInputVal" onmousedown="clearPrevVal($(this))"></i>
                                 </div>`;
                            }
                        }

                    }
                    $(".qt"+ val +" .interfereOptionList").html(str)
                    $("#editQuestion .bonceHead span").html(ttl);
                    $("#editQuestion .qt" + val).show().siblings().hide();
                    bounce_Fixed.show($("#editQuestion"));
                    $("#editQuestion").data("material", material);
                    bounce.cancel();
                }else{ // 已满4题，无法新增
                    bounce_Fixed2.show($("#alertTip"));
                    let str = "操作失败！系统对于同一素材下试题的数量有限制。<br>目前，该素材下试题数量已达上限。";
                    $("#alertTip .tip").html(str);
                }
            }
        });
    }else{
        layer.msg('请先题型！');
    }
}
// create :hxz 2021-1-21 获取题库编号
function getBankCode() {
    $.ajax({
        "url":"../trainManage/selectQuestionBankCode.do",
        success:function(res){
            $("#editQuestionBank .bankCode").html(res.code);
        }
    })
}
// create :hxz 2021-2-17 保存题库
function questionBankSave() {
    let type = $("#editQuestionBank .bonceHead span").html()
    let bankName = $("#bankName").val()
    if(bankName.length ==0){
        layer.msg("题库名称不能为空！");
        return false;
    }//trainingQuestionBankAttachment:附件
    let url = "../trainManage/addQuestionBank.do";
    let data = {
        "name": bankName,
        "module": '培训管理'
    //"addAttachmentList": JSON.stringify([])
    };
    let files = [];
    $(".attachList .attachItem").each(function () {
        let info = JSON.parse($(this).find(".hd").html());
        if (!info.id) {
            let item = {
                path: info.path,
                title: info.title,
                size: info.size
            };
            files.push(item);
        }
    })
    if(type === "修改题库基本信息"){
        let diff = 0;
        let nameOrg = $("#bankName").data("org");
        let delList = $(".attachList").data("del");
        if (nameOrg != bankName || files.length > 0 || delList != "" && delList.length > 0) {
            diff++;
        }
        if (diff <= 0){
            $("#alertTip .tip").html("您没做任何修改！");
            bounce_Fixed2.show($("#alertTip"));
            return false;
        }
        let bank = JSON.parse($("#page5").find(".json").html());
        url = "../trainManage/updateTrainingQuestionBank.do";
        data['id'] = bank.id
        if (delList != "" && delList.length > 0) {
            data['deleteAttachmentList'] = JSON.stringify(delList);
        }
        if (files.length > 0) {
            data.addAttachmentList = JSON.stringify(files);
        }
    } else {
        if (files.length > 0) {
            data.trainingQuestionBankAttachment = JSON.stringify(files);
        }
    }
    $.ajax({
        "url":url,
        "data":data,
        success:function(res){
            let status = res.status
            if(status == 1){
                layer.msg("操作成功");
                if(type === "修改题库基本信息"){
                    $(".mainCon5 .bankName").html(bankName);
                    bankInfo();
                    refreshSource();
                }else{
                    // 刷新列表
                    refresh();
                }
            }else{
                layer.msg("操作失败");
            }
            bounce.cancel();
        }
    })
}
// create :hxz 2021-1-11  题库列表数据
function getList(cur, per, jsonObj ) {
    // type 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
    // status 1启用状态的，0停用状态的
    let type = jsonObj.type;
    let status = jsonObj.status;
    let conNum = jsonObj.conNum;
    let keyword = jsonObj.keyword;
    let url = "../trainManage/selectQuestionBankList.do";
    let data = { "type":type,"status":status,"pageSize":questionPageSize,"currentPageNo":cur };
    if(keyword && keyword.length >0){
        url = "../trainManage/selectQuestionBankListByKeyword.do";
        data['keyword'] = keyword
    }
    $.ajax({
        "url":url,
        "data": data,
        success:function(res) {
            let list = res.trainingQuestionBankList|| [], str = ``, pageInfo = res.pageInfo;
            for(let i = 0 ; i < list.length ; i++){
                let item = list[i];
                str += `<tr>
                      <td>${item.code}</td>
                      <td>${item.name}</td>
                      <td>${item.createName} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")} </td>
                      <td>${item.materialNum}</td>
                      <td>单选题${item.choiceNum}道，判断题${item.tfNum}道</td>
                      <td>
                          <span class="funbtn ty-color-blue" data-fun="bankScan">查看</span>
                          ${ status ==1 ? '<span class="funbtn ty-color-blue" data-fun="bankStop">停用</span>' : '<span class="funbtn ty-color-blue" data-fun="bankStart">启用</span>' }
                          <span class="funbtn ty-color-red" data-fun="bankDel">删除</span>
                          <span class="hd">${JSON.stringify(item)}</span>
                      </td>
                  </tr>`;
            }
            let tbody =  $(".mainCon" + conNum + " table tbody")
            tbody.children("tr:gt(0)").remove();
            tbody.append(str);
            let sumPage = pageInfo.totalPage
            setPage($("#page" + conNum),cur,sumPage,"questionBankList",JSON.stringify(jsonObj));
        }
    })
}
// create :hxz 2021-2/18 查看题库
function bankScan(thisObj){
    let info = JSON.parse(thisObj.siblings(".hd").html());
    let souceJson = { 'id': info.id, 'type': 3 , 'status': 1, "conNum":5 }
    getSourceList(1,questionPageSize,souceJson);
    $(".mainCon5").data("id", info.id);
    $(".mainCon5 .bankName").html(info.name);
    $(".mainCon5 .bankCreate").html(info.createName + '&nbsp;&nbsp;' + (new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss")) );
}
// create :hxz 2021-2-19 素材列表数据
function getSourceList(cur,per,jsonObj){
    let keyword = jsonObj.keyword;
    let url = "../trainManage/selectQuestionBankDetail.do";
    let data = { "id": jsonObj.id, "type":jsonObj.type, "status":jsonObj.status, "pageSize":questionPageSize, "currentPageNo":cur  };
    if(keyword && keyword.length >0){
        url = "../trainManage/selectQuestionBankDetailByKeyword.do";
        data['keyword'] = keyword
    }
    if (jsonObj.status == 0) {
        $(".showAble").hide();
    } else {
        $(".showAble").show();
    }
    $.ajax({
        "url":url,
        "data": data ,
        success:function(res) {
            let list = res.trainingMaterialList|| [], str = ``, pageInfo = res.pageInfo;
            for(let i = 0 ; i < list.length ; i++){
                let item = list[i];
                if(jsonObj.conNum === 7){
                    str += `<tr>
                              <td>${item.code}</td> 
                              <td>${item.createName} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")} </td>
                              <td>${item.content}</td> 
                          </tr>`;
                }else{
                    let contros = ``;
                    if (res.status == 0) {
                        contros = `
                          <span class="funbtn ty-color-blue" data-fun="sourceScan">查看</span>`;
                    } else {
                        contros = `
                          <span class="funbtn ty-color-blue" data-fun="sourceScan">查看</span>
                          ${ jsonObj.status ==1 ? '<span class="funbtn ty-color-blue" data-fun="sourceStop">停用</span>' : '<span class="funbtn ty-color-blue" data-fun="sourceStart">启用</span>' }
                          <span class="funbtn ty-color-red" data-fun="sourceDel">删除</span>`;
                    }
                    str += `<tr>
                      <td>${item.code}</td> 
                      <td>${item.createName} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")} </td>
                      <td>单选题${item.choiceNum}道，判断题${item.tfNum}道</td>
                      <td>${item.disabledNum}道</td> 
                    <td>
                          ${contros}
                          <span class="hd">${JSON.stringify(item)}</span>
                      </td>
                  </tr>`;
                }
            }
            let tbody =  $(".mainCon" + jsonObj.conNum + " table tbody")
            tbody.children("tr:gt(0)").remove();
            tbody.append(str);
            let sumPage = pageInfo.totalPage
            setPage($("#page" + jsonObj.conNum),cur,sumPage,"questionBankSourceList",JSON.stringify(jsonObj));
        }
    });
}
// create :hxz 2021-2-19 题目列表数据
function getQuestionList(jsonObj){
    let url = "../trainManage/selectTrainMaterialDetail.do";
    let data = { "id": jsonObj.id, "status":jsonObj.status };
    $.ajax({
        "url":url,
        "data": data ,
        success:function(res) {
            let enabled = res.status;
            let list = res.trainingQuestionList|| [], str = ``, info = res.trainingMaterial ;
            for(let i = 0 ; i < list.length ; i++){
                let item = list[i];
                let scanBtn =
                    `${ jsonObj.status ==1 ? '<span class="funbtn ty-color-blue" data-fun="questionStop">停用</span>' : '<span class="funbtn ty-color-blue" data-fun="questionStart">启用</span>' }
                    <span class="funbtn ty-color-red" data-fun="questionDel">删除</span>`;
                if (jsonObj.status != 0) {
                    scanBtn = `<span class="funbtn ty-color-blue" data-fun="questionScan">查看</span>${ scanBtn }`;
                }
                str += `
                <tr>
                    <td>${item.type == 1? "单选题": "判断题"}</td>
                    <td>${item.createName} ${new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")} </td>
                    <td>
                        ${scanBtn}
                        <span class="hd">${JSON.stringify(item)}</span>
                    </td>
                </tr>`;
            }
            let tbody =  $("#questionList"+ jsonObj.status +" tbody");
            tbody.children("tr:gt(0)").remove();
            tbody.append(str);
            let idStr = "#stopQuestion";
            if(jsonObj.status == 1){
                idStr = "#editSource";
                $(idStr +" .chosNum").html(info.choiceNum);
                $(idStr +" .chargNum").html(info.tfNum);
                if (enabled == 0) {
                    $(".step1 .enabledType").hide();
                } else {
                    $(".step1 .enabledType").show();
                }
            }
            $(idStr +" .souceCode").html(info.code);
            $(idStr +" .souceContent").html(info.content);
        }
    });
}
// create :hxz 2021-1-11 各种confirm 判断
function confirmTipOk(){
    let type = $("#confirmTip").data("type");
    let json = $("#confirmTip").data("json");
    switch (type){
        case "bankStop": // 停用题库
        case "bankStart":
            $.ajax({
                "url":"../trainManage/updateQuestionBank.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        layer.msg("操作成功");
                        refresh();
                    }else{
                        layer.msg("操作失败");
                    }
                    bounce_Fixed2.cancel();
                }
            });
            break;
        case "bankDel":
            $.ajax({
                "url":"../trainManage/deleteQuestionBank.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        layer.msg("操作成功");
                        refresh();
                    }else{
                        layer.msg("操作失败");
                    }
                    bounce_Fixed2.cancel();
                }
            });
            break;
        case "sourceStop": // 停用素材
        case "sourceStart":
            $.ajax({
                "url":"../trainManage/updateTrainingMaterial.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        layer.msg("操作成功");
                        refreshSource();
                    }else{
                        layer.msg("操作失败");
                    }
                    bounce_Fixed2.cancel();
                }
            });
            break;
        case "sourceDel":
            $.ajax({
                "url":"../trainManage/deleteTrainingMaterial.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        layer.msg("操作成功");
                        refreshSource();
                    }else{
                        layer.msg("操作失败");
                    }
                    bounce_Fixed2.cancel();
                }
            });
            break;
        case "saveQuestion2": // 保存判断题
            $.ajax({
                "url":"../trainManage/addTfQuestion.do",
                "data": json,
                success:function(res){
                    let status = res.status
                    let code = res.code
                    if(status == 1){
                        layer.msg("操作成功");
                        // 刷新列表
                        refreshQuestion();
                        showQuestionCon();
                        refreshSource();
                        bounce_Fixed2.cancel();
                    }else  if(status == -3){
                        bounce_Fixed2.show($("#alertTip"));
                        let str = "新增失败。<br/>系统中已经有该素材标准答案为“正确”的判断题！";
                        $("#alertTip .tip").html(str);

                    }else {
                        layer.msg("操作失败");
                        bounce_Fixed2.cancel();

                    }
                }
            });
            break;
        case "questionStop": // 停启用题型
        case "questionStart":
            $.ajax({
                "url":"../trainManage/updateTrainingQuestion.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        layer.msg("操作成功");
                        refreshSource();
                        refreshQuestion();
                        bounce_Fixed2.cancel();
                    }else if(status == 3){
                        bounce_Fixed2.show($("#alertTip"));
                        let str = "停用失败。<br>该判断题为正确项。";
                        $("#alertTip .tip").html(str);
                    }else if(status == -2){
                        bounce_Fixed2.show($("#alertTip"));
                        let str = "<p>启用失败，因为系统支持一个素材最多可有四个判断题。</p><p>而现在已经有了已经有四个了。</p>";
                        $("#alertTip .tip").html(str);
                    }else if(status == -1){
                        bounce_Fixed2.show($("#alertTip"));
                        let str = "<p>启用失败，因为系统支持一个素材最多可有四个单选题。</p><p>而现在已经有了已经有四个了。</p>";
                        $("#alertTip .tip").html(str);
                    }else if(status == -3){
                        bounce_Fixed2.show($("#alertTip"));
                        let str = "<p>启用失败</p><p>因为系统中已经有该素材标准答案为“正确”的判断题！</p>";
                        $("#alertTip .tip").html(str);
                    }else{
                        layer.msg("操作失败");
                        bounce_Fixed2.cancel();
                    }
                }
            });
            break;
        case "questionDel": // 删除试题
            $.ajax({
                "url":"../trainManage/deleteTrainingQuestion.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        layer.msg("操作成功");
                        refreshSource();
                        refreshQuestion();
                    }else if(status == 3){
                        bounce_Fixed2.show($("#alertTip"));
                        let str = "删除失败。<br>该判断题为正确项。";
                        $("#alertTip .tip").html(str);
                    }else{
                        layer.msg("操作失败");
                    }
                    bounce_Fixed2.cancel();
                }
            });
            break;
        case "optionStop": // 停用干扰项
        case "optionStart":
            $.ajax({
                "url":"../trainManage/updateTrainingQuestionKey.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        layer.msg("操作成功");
                        var optionJson = $("#questionScan").data('json');
                        let json = { "id": optionJson.id, "status": 1 }
                        if(type == "optionStart"){
                            json.status = 0
                        }
                        getOptionList(json);
                        bounce_Fixed2.cancel();
                    }else if(status == -1){
                        bounce_Fixed2.show($("#alertTip"));
                        var str = "启用失败，因为系统支持一道单选题最多可有五个干扰项。<br>而现在这道题已经有五个了。";
                        $("#alertTip .tip").html(str);
                    }else if(status == -2){
                        bounce_Fixed2.show($("#alertTip"));
                        var str = "系统要求一道单选题至少要有三个干扰项。<br>您再停用，干扰项就不够了。";
                        $("#alertTip .tip").html(str);
                    }
                }
            });
            break;
        case "optionDel": // 删除干扰项
            $.ajax({
                "url":"../trainManage/deleteDisableKey.do",
                "data": json ,
                success:function(res) {
                    let status = res.status
                    if(status == 1){
                        let delObj = $("#confirmTip").data("obj");
                        delObj.parents("tr").remove();
                        layer.msg("操作成功");
                        refreshQuestion();
                        bounce_Fixed2.cancel();
                    }else if(status == 0){
                        bounce_Fixed2.show($("#alertTip"));
                        let str = "删除失败！<br> 因为已使用过的干扰项不可被删除。";
                        $("#alertTip .tip").html(str);
                    }else if(status == -2){
                        bounce_Fixed2.show($("#alertTip"));
                        let str = "系统要求一道单选题至少要有三个干扰项。<br>您再删除，干扰项就不够了。";
                        $("#alertTip .tip").html(str);
                    }
                }
            });
            break;

    }
}
// create :hxz 2021-2-18  刷新题库列表
function refresh(){
    let mainConNum = $("#showMainConNum").val()
    var cur = $("#page"+ mainConNum).find(".yecur").html();
    let jsonObj = JSON.parse($("#page" + mainConNum).find(".json").html());
    getList(cur, questionPageSize,jsonObj);
}
// create :hxz 2021-2-18  刷新资源列表
function refreshSource(){
    let mainConNum = $("#showMainConNum").val()
    var cur = $("#page"+ mainConNum).find(".yecur").html();
    let jsonObj = JSON.parse($("#page" + mainConNum).find(".json").html());
    getSourceList(cur, questionPageSize,jsonObj);
}
// create :hxz 2021-2-18  刷新题目列表
function refreshQuestion(){
    let jsonObj =  $("#questionList1").data("json");
    getQuestionList(jsonObj);
}
// create :hxz 2021-2-19  删除题库判断
function bankDelJudge(thisObj){
    let info = JSON.parse(thisObj.siblings(".hd").html());
    $.ajax({
        "url":"../trainManage/deleteQuestionBankJudge.do",
        "data": { "id": info.id } ,
        success:function(res) {
            let status = res.status
            if(status == 1){
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type","bankDel");
                let json = { "id":info.id };
                let str = "确定删除该题库吗？";
                $("#confirmTip").data("json",json);
                $("#confirmTip .tip").html(str);
            }else{
                bounce_Fixed2.show($("#alertTip"));
                let str = "删除失败！<br>因为该题库中含有已使用过的试题。"
                $("#alertTip .tip").html(str);
            }
        }
    });
}
// create :hxz 2021-2-19  删除题库判断
function sourceDelJudge(thisObj){
    let info = JSON.parse(thisObj.siblings(".hd").html());
    $.ajax({
        "url":"../trainManage/deleteTrainingMaterialJudge.do",
        "data": { "id": info.id } ,
        success:function(res) {
            let status = res.status
            if(status == 1){
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type","sourceDel");
                let json = { "id":info.id };
                let str = "确定删除该素材吗？";
                $("#confirmTip").data("json",json);
                $("#confirmTip .tip").html(str);
            }else{
                bounce_Fixed2.show($("#alertTip"));
                let str = "删除失败！<br>因为该素材中含有已使用过的试题。"
                $("#alertTip .tip").html(str);
            }
        }
    });
}
// create :hxz 2021-2-19  删除试题判断
function questionDelJudge(thisObj){
    let info = JSON.parse(thisObj.siblings(".hd").html());
    $.ajax({
        "url":"../trainManage/deleteTrainingQuestionJudge.do",
        "data": { "id": info.id } ,
        success:function(res) {
            let status = res.status
            if(status == 1){
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type","questionDel");
                let json = { "id":info.id };
                let str = "确定删除该道试题吗？";
                $("#confirmTip").data("json",json);
                $("#confirmTip .tip").html(str);
            }else if(status == 3){
                bounce_Fixed2.show($("#alertTip"));
                let str = "删除失败。<br>该判断题为正确项。";
                $("#alertTip .tip").html(str);
            }else{
                bounce_Fixed2.show($("#alertTip"));
                let str = "删除失败！<br>因为已使用过的试题不可被删除。";
                $("#alertTip .tip").html(str);
            }
        }
    });
}
// create :hxz 2021-2-19  删除干扰项判断
function optionDelJudge(thisObj){
    let info = JSON.parse(thisObj.siblings(".hd").html());
    $.ajax({
        "url":"../trainManage/deleteDisableKeyJudge.do",
        "data": { "id": info.id } ,
        success:function(res) {
            let status = res.status
            if(status == 1){
                bounce_Fixed2.show($("#confirmTip"));
                $("#confirmTip").data("type","optionDel");
                $("#confirmTip").data("obj", thisObj);
                let json = { "id":info.id };
                let str = "确定后，该干扰项将从系统中彻底消失。<br>确定删除该干扰项吗？";
                $("#confirmTip").data("json",json);
                $("#confirmTip .tip").html(str);
            }else if(status == 0){
                bounce_Fixed2.show($("#alertTip"));
                let str = "删除失败！<br> 因为已使用过的干扰项不可被删除。";
                $("#alertTip .tip").html(str);
            }else if(status == -2){
                bounce_Fixed2.show($("#alertTip"));
                let str = "系统要求一道单选题至少要有三个干扰项。<br>您再删除，干扰项就不够了。";
                $("#alertTip .tip").html(str);
            }
        }
    });
}
// create :hxz 2021-2-19  题目查看
function questionScan(thisObj){
    let info = JSON.parse(thisObj.siblings(".hd").html());
    $("#questionScan").data("id", info.id);
    let json = { "id": info.id, "status": 1 }
    getOptionList(json);
    $("#questionScan").data('json', json);
    $("#questionScan .onOption").show();
    $("#questionScan .offOption").hide();
    $("#questionScan .option").hide();
}
function getOptionList(json){
    $.ajax({
        "url":"../trainManage/selectTrainQuestionDetail.do",
        "data": { "id": json.id, "status": json.status } ,
        success:function(res) {
            let item = res.trainingQuestion;
            let ops = res.trainingQuestionKeyList;
            bounce_Fixed.show($("#questionScan"));
            $("#questionScan .codeQ").html(item.code);
            $("#questionScan .contentQ").html(item.content);
            $("#questionScan .qt" + item.type).show().siblings().hide();
            if(item.type == 1){ // 单选
                $("#questionScan .qt1Right").show();
                let str = ``;
                for(let i = 0 ; i < ops.length ; i++){
                    let option = ops[i];
                    if(option.isKey  == 0){
                        if (res.status == 0) {
                            str +=`<tr>
                            <td class="txtLeft" width="70%;">
                                干扰项<br>
                                ${ option.content}
                            </td>
                        </tr>`;
                        } else {
                            str +=`<tr>
                            <td class="txtLeft" width="70%;">
                                干扰项<br>
                                ${ option.content}
                            </td>
                            <td width="30%;">                        
                                ${ json.status ==1 ? '<span class="funbtn ty-color-blue" data-fun="optionStop">停用</span>' : '<span class="funbtn ty-color-blue" data-fun="optionStart">启用</span>' }
                                <span data-fun="optionDel" class="funbtn ty-color-red">删除</span>
                                <span class="hd">${ JSON.stringify(option) }</span>
                            </td>
                        </tr>`;
                        }
                    }else {
                        $("#questionScan .curRight").html(option.content);
                    }
                }
                $("#qtTb" + item.type ).html(str);
                if (res.status == 0) {
                    $("#questionScan .addOption").hide();
                } else {
                    $("#questionScan .addOption").show();
                }
            }else{ // 判断
                $("#questionScan .qt1Right").hide();
                $("#questionScan .bonceHead span").html("判断题查看");
                $("#questionScan .qt" + item.type).html(`<p>标准答案为“${ ops[0]['isKey']==1?"正确" :"错误"}”的。</p>`)
            }
        }
    });
}
// create :hxz 2021-2-19 新增干扰项
function saveOption(){
    let txt = $("#questionScan .option textarea").val();
    let question = $("#questionScan").data("id");
    $.ajax({
        "url":"../trainManage/addDisableKey.do",
        "data": { "question": question, "disableKey": txt } ,
        success:function(res) {
            let status = res.status;
            if(status == 1){
                layer.msg("操作成功！");
                $(".offOption").hide(); $(".onOption").show();
                $("#questionScan .option").hide();
                var optionJson = $("#questionScan").data('json');
                getOptionList({ "id": optionJson.id, "status": 1 });
            }else{
                bounce_Fixed2.show($("#alertTip"));
                let str = "系统支持一道单选题最多可有五个干扰项。<br>已经有五个了。";
                $("#alertTip .tip").html(str);
            }
        }
    });
}
// create :hxz 2021-2-19 题库基本信息
function bankInfo(){
    let info1 = JSON.parse($("#page5").find(".json").html());
    let id = info1.id;
    if (info1.status != 0) {
        $("#bankInfo .scanAbel").show();
    } else {
        $("#bankInfo .scanAbel").hide();
    }
    $("#bankInfo .base").show();
    $("#bankInfo .hisBase").hide();
    $("#bankInfo .bonceHead a").data("fun", 'cancel');
    $.ajax({
        "url":"../trainManage/selectQuestionBankInfo.do",
        "data": { "id": id  } ,
        success:function(res) {
            bounce.show($("#bankInfo"));
            let info = res.trainingQuestionBank ;
            let fileList = res.trainingQuestionBankAttachmentList, fileHtml = `` ;
            $("#bankInfo .bankNm").html(info.name);
            $("#bankInfo .bankCode").html(info.code);
            $("#bankInfo .bankCreat").html(info.createName + '&nbsp;' +(new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss")));
            if (fileList && fileList.length > 0) {
                for (var i=0;i<fileList.length;i++) {
                    let filetype = fileList[i].path.split('.').pop();
                    let fileTypeStr = chargeFileType(filetype)
                    fileHtml +=
                        `<div class="attachItem">
                            <div class="fa ${fileTypeStr}"></div>
                            <div>
                                <div title="${fileList[i].title}" class="fileName">${fileList[i].title}</div>
                                <div class="fileSize">${formatSize(fileList[i].size)}</div>
                            </div>
                            <div>
                                <a class="ty-left" path="${fileList[i].path}" onclick="seeOnline($(this))">在线预览</a>
                                <a class="ty-left" path="${fileList[i].path}" onclick="getDownLoad($(this))" download="${fileList[i].title}">下载</a>
                                <span class="hd">${JSON.stringify(fileList[i])}</span>
                            </div>
                        </div>`;
                }
            }
            $(".scanFileList").html(fileHtml);
        }
    });
}
function bankInfoLogScan(thisObj){
    let info1 = JSON.parse(thisObj.siblings(".hd").html());
    $("#bankInfo .base").hide();
    $("#bankInfo .scanAbel").hide();
    $("#bankInfo .hisBase").show();
    $("#bankInfo .bonceHead a").data("fun", 'backBankEditLogList');
    $.ajax({
        "url":"../trainManage/selectTrainBankHistoryInfo.do",
        "data": { "id": info1.id  } ,
        success:function(res) {
            bounce.show($("#bankInfo"));
            let info = res.trainingQuestionBankHistory;
            let fileList = res.trainingQuestionBankAttachmentHistoryList, fileHtml = `` ;
            $("#bankInfo .bankNm").html(info.name);
            $("#bankInfo .bankCode").html(info.code);
            $("#bankInfo .bankCreat").html(info.createName + '&nbsp;' + (new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss")));
            if (fileList && fileList.length > 0) {
                for (var i=0;i<fileList.length;i++) {
                    let filetype = fileList[i].path.split('.').pop();
                    let fileTypeStr = chargeFileType(filetype)
                    fileHtml +=
                        `<div class="attachItem">
                            <div class="fa ${fileTypeStr}"></div>
                            <div>
                                <div title="${fileList[i].title}" class="fileName">${fileList[i].title}</div>
                                <div class="fileSize">${formatSize(fileList[i].size)}</div>
                            </div>
                            <div>
                                <a class="ty-left" path="${fileList[i].path}" onclick="seeOnline($(this))">在线预览</a>
                                <a class="ty-left" path="${fileList[i].path}" onclick="getDownLoad($(this))" download="${fileList[i].title}">下载</a>
                                <span class="hd">${JSON.stringify(fileList[i])}</span>
                            </div>
                        </div>`;
                }
            }
            $(".scanFileList").html(fileHtml);
        }
    });
}
// create :hxz 2021-2-19 题库修改记录接口
function bankEditLog(){
    let info = JSON.parse($("#page5").find(".json").html());
    let id = info.id;
    $.ajax({
        "url":"../trainManage/selectTrainBankHistoryList.do",
        "data": { "id": id  } ,
        success:function(res) {
            let list = res.trainingQuestionBankHistoryList || [];
            let info = res.trainingQuestionBank ;
            bounce.show($("#bankEditLog"));
            let curStaStr = ``;
            if(list.length >0){
                let str = ``;
                for(let i = 0 ; i < list.length;i++){
                    let item = list[i];
                    str += `<tr>
                             <td>${i===0 ? "原始信息":"第"+ i +"次修改后"}</td>
                             <td class=" ty-table-control">
                                <span class="funbtn ty-color-blue" data-fun="bankInfoLogScan">查看</span>
                                <span class="hd">${JSON.stringify(item)}</span>
                             </td>
                                <td>${item.updateName} ${new Date(item.updateDate).format("yyyy-MM-dd hh:mm:ss")}</td>
                            </tr>`
                }
                $("#bankEditLog tbody").children(":gt(0)").remove();
                $("#bankEditLog tbody").append(str);
                $("#bankEditLog table").show();
                let n = list.length-1
                let last = list[n-1]
                curStaStr = `<span style="float: right;">修改人：${last.updateName} ${new Date(last.updateDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料为第${n}次修改后的结果。`
            }else{
                $("#bankEditLog table").hide();
                curStaStr = `<span style="float: right;">创建人：${info.createName} ${new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss")}</span> 当前资料尚未经修改。`
            }
            $("#bankEditLog .curSta").html(curStaStr);
        }
    });
}
// create :hxz 2021-1-11  显示页面
function showMainCon(num){
    $("#showMainConNum").val(num)
    $(".mainCon" + num).show().siblings().hide();
}
// create :hxz 2021-1-11  题库附件选择方式
function selectSouce(thisObj){
    $("#addAttach .fa").attr("class", "fa fa-circle-o");
    thisObj.find(".fa").attr("class", "fa fa-dot-circle-o");
}
// create :hxz 2021-1-11  是否在素材上出题
function selectFromSouce(thisObj){
    $("#editSource .step1 .fa").attr("class", "fa fa-circle-o");
    thisObj.find(".fa").attr("class", "fa fa-dot-circle-o");
    $("#questionTypeCon").show();
}
// create :hxz 2021-1-11  是否在素材上出题
function selectQuestionType(thisObj){
    $("#questionTypeCon .fa").attr("class", "fa fa-circle-o");
    thisObj.find(".fa").attr("class", "fa fa-dot-circle-o");
}
// create :hxz 2021-1-11  题库附件选择方式
function openFileSouce(){
    if($("#addAttach .fa-dot-circle-o").length > 0){
        let val = $("#addAttach .fa-dot-circle-o").parent("p").data("val");
        if(val == '2'){ // 本地文件
            $("#uploadFile .uploadify-button").click();
        }
    }else{
        layer.msg('请先选择附件来源！')
    }
}
// creator: 李玉婷，2021-08-23 08:57:58，新增、修改题库取消
function closeQuestionBank() {
    let type = $("#editQuestionBank .bonceHead span").html()
    if (type == '修改题库基本信息') {
        bankInfo();
    } else {
        bounce.cancel();
    }
}
// create :hxz 2021-1-11  初始化文件上传控件
function initUpload(thisObj){
    var groupUuid = sphdSocket.uuid();
    thisObj.Huploadify({
        auto:true,
        multi:true,
        buttonText:"上传附件",
        formData:{
            module: '培训管理',
            userId: sphdSocket.user.userID,
            groupUuid: groupUuid
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:false,
        showUploadedSize:false,
        removeTimeout:********,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onSelect: function (file){ //选择文件后向队列中添加每个上传任务时都会触发
            loading.open();
        },
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            let res = JSON.parse(data)
            res.path = res.filename;
            res.title = res.originalFilename;
            let filetype = file.name.split('.').pop();
            let fileTypeStr = chargeFileType(filetype)
            let imgStr =`<div class="attachItem">
                            <div class="fa ${fileTypeStr}"></div>
                            <div>
                                <p title="" class="fileName">${file.name}</p>
                                <div class="fileSize">${formatSize(file.size)}</div>
                            </div>
                            <div>
                                <span class="redLink" fileUid="${res.fileUid}">删除</span>
                                <span class="hd">${JSON.stringify(res)}</span>
                            </div>
                        </div>`;
            $('.attachList').append(imgStr);
            bounce_Fixed.cancel();
            loading.close();
        }
    });
}
// create：hxz 2021-1-21 字数超出限制
function setWordsNum(thisObj , maxLen){
    let curtxt = thisObj.val()
    if(curtxt.length > maxLen){
        thisObj.val(curtxt.substr(0,maxLen));
        layer.msg(`最多录入${maxLen}个字符`);
    }
    thisObj.parent().find(".lenTip").html( thisObj.val().length + " / " + maxLen);
}
// create：hxz 2021-1-21 工具方法： 返回文件类型的 类名
function chargeFileType(fileType) {
    var faStr = "fa-file";
    switch (fileType) {
        case "doc":
        case "docx":
            faStr = "fa-file-word-o";
            break;
        case "xls":
        case "xlsx":
        case "et":
            faStr = "fa-file-excel-o";
            break;
        case "ppt":
            faStr = "fa-file-powerpoint-o";
            break;
        case "rar":
        case "zip":
            faStr = "fa-file-archive-o";
            break;
        case "pdf":
            faStr = "fa-file-pdf-o";
            break;
        case "png":
        case "jpg":
        case "gif":
            faStr = "fa-file-image-o";
            break;
        case "txt":
            faStr = "fa-file-text-o";
            break;
        default:
    }
    return faStr ;
}
// create：hxz 2021-1-21 格式化文件大小
function formatSize(size) {
    if(size < 1024){ return size.toFixed(2) + 'B' }
    size = size / 1024 ; // KB
    if(size < 1024){ return size.toFixed(2) + 'KB' }
    size = size / 1024 ; // MB
    if(size < 1024){ return size.toFixed(2) + 'MB' }
    size = size / 1024 ; // GB
    if(size < 1024){ return size.toFixed(2) + 'GB' }
    size = size / 1024 ; // TB
    return size.toFixed(2) + 'TB'
}
// creator: 李玉婷，2021-06-25 11:19:14，一键清除
function clearPrevVal(obj){
    obj.prev().val("");
    obj.prev().get(0).focus();
    obj.parents(".clearArea").find(".lenTip").html('0 / 80');
    obj.parents(".clearInput").find(".lenTip").html('0 / 15');
}
