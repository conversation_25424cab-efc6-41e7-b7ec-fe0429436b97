/*
* creator:sy
* 2022-05-11
* 工序查看
* */
var user = null;
var contractDel = null;
var contractInfo = null;
var editContractObj = null;
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#newMailInfo"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#lookpoot"));
bounce_Fixed3.cancel();

$(function(){
    //获得工序查看首页列表
    getprceview(1,20);
})

// creator: sy 2023-05-11   工序查看首页礼包
function getprceview(currentPageNo,pageSize,keyword){
    $.ajax({
        url:"../processView/getPdBaseProcessList.do",
        data:"",
        success:function(res){
            loading.close();
            var list = res.data || [];
            if(list == []){
                $("#proviewlist").html("");
                return false;
            }
            // var pagec = list["pageInfo"];
            // var totalPage = list["totalResult"];
            // var totalResult = list["totalResult"];
            // var cur = list["currentPageNo"];
            // $("#ye_adcityindex").html("");
            // var jsonStr = JSON.stringify({"keyword":keyword});
            // setPage($("#ye_adcityindex"),cur,totalPage,"processview",jsonStr);
            $("#proviewlist").html("");
            var str = ``;
            list.forEach(item => {
                str = `
                <tr>
                    <td>${handleNull(item.innerSn)}/${handleNull(item.name)}/${handleNull(item.specifications)}/${handleNull(item.model)}
                    /${handleNull(item.unit)}
                    </td>
                    <td>${handleNull(item.netWeight)}</td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue funBtn" onclick="">${handleNull(null)}个</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue">已设置</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue">已设置</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue funBtn" onclick="">${handleNull(null)}个</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue funBtn" onclick="">${handleNull(null)}个</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue funBtn" onclick="">${handleNull(null)}种</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue funBtn" onclick="">${handleNull(null)}种</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue funBtn" onclick="">${handleNull(null)}种</span>
                    </td>
                    <td class="ty-td-control">
                        <span class="ty-color-blue funBtn" onclick="">${handleNull(null)}位</span>
                    </td>
                    <td>
                        <span class="ty-color-blue funBtn">查看</span>
                        <span class="hd">${JSON.stringify(item)}</span>
                    </td>
                </tr>`;
                $("#proviewlist").append(str);
            })
        }
    })
}

// creator: sy 2023-05-31   处理表格中没有值的情况
function handleNull(str){
    var result = str == null || str == undefined || str == 'null' || ''?'——':str;
    return result;
}