//检测是否全为数字
function isDigit(num)
{
	var reg = /^\d+(?=\.{0,1}\d+$|$)/
	if(!reg.test(num.value)) {alert("请输入数字(大于0的整数或者小数)");}
}

// 检测是否正浮点型  
function isFloat(obj)
{
	var str=obj.value;
	var patrn=/^\\d+(\\.\\d+)?$/;
	var patrn2=/^[0-9]{1,20}.[0-9]{1,20}$/;
	var patrn3=/^[0-9]{1,20}$/;
	if (patrn.exec(str)==null && patrn2.exec(str)==null&& patrn3.exec(str)==null){
		// obj.nextSibling.value="请输入正确的数字";
		obj.value="";
		alert("请输入正确的数值");
	}
}

//  输入小数或整数(4位）
function clearNoNum(obj) {
    obj.value = obj.value.replace(/[^\d.]/g, "");  //清除除了“数字”和“.”以外的字符
    obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3');//只能输入3个小数
    if (obj.value.indexOf(".") < 0 && obj.value != "") {
        //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        obj.value = parseFloat(obj.value);
    }
}
// creator: 李玉婷，2021-12-27 13:28:27，输入小数或整数(n位）
function clearNoNumN(obj, num) {
    obj.value = obj.value.replace(/[^\d.]/g, "");  //清除除了“数字”和“.”以外的字符
    obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj.value = obj.value.replace(eval('/^(\\-)*(\\d+)\\.(\\d{'+num+'}).*$/'), '$1$2.$3');//只能输入3个小数
    if (obj.value.indexOf(".") < 0 && obj.value != "") {
        //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        obj.value = parseFloat(obj.value);
    }
}
// 不能输入中文
function clearChinese(obj){
    obj.value = obj.value.replace(/[\u4e00-\u9fa5]/g, "");
}
function testNum(obj){
    obj.value = obj.value.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/^\./g,"");  //验证第一个字符是数字而不是.
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
    if(obj.value.indexOf(".")< 0 && obj.value !=""){//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        obj.value= parseFloat(obj.value);
    }
}
//  输入整数
function clearNum(obj){
	obj.value = obj.value.replace(/[^\d]/g,"");  //清除“数字”和“.”以外的字符

 	obj.value = obj.value.replace(/^0/g,"");  //验证第一个字符不是零
	obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的.
	obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
}

//  输入整数
function clearNum0(obj){
    obj.value = obj.value.replace(/[^\d]/g,"");  //清除“数字”和“.”以外的字符
	obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的.
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
}
 

//验证邮箱
function isMail(obj){
	var pattern = /\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/;
	var str=obj.value;
	if (pattern.exec(str)==null){
		obj.value="";
		alert("请输入正确的邮箱");
		}
}
//验证中文
function isChinese(obj){
	var str=obj.value;
	var pattern = /[\u4e00-\u9fa5]/;
	if (pattern.exec(str)==null) {
		obj.value="";
		alert("请输入正确的中文");
	}

}
//电话
   // 现在是所有电话，座机和手机 
function isTel(obj){
	var str=obj.value;
/*  var pattern = /^((\(\d{2,3}\))|(\d{3}\-))?(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$/;*/
	var pattern = /(^((\(\d{2,3}\))|(\d{3}\-))?(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$)|(^(0|86|17951)?(13[0-9]|15[012356789]|16[012356789]|17[0135678]|18[0-9]|14[57])[0-9]{8}$)/;
	if (pattern.exec(str)==null) {
		obj.value="";
	}

}
//cellPhone
function isMobile(obj){
	var str=obj.value;
	var pattern =/^(0|86|17951)?(13[0-9]|15[012356789]|16[012356789]|17[0-9]|18[0-9]|14[57])[0-9]{8}$/;
	//regexp="^((\(\d{3}\))|(\d{3}\-))?13[0-9]\d{8}|15[89]\d{8}"
	if (pattern.exec(str)==null) {
		// obj.nextSibling.value="请输入正确的手机号";
		obj.value="";
	}

}
// val 为要验证的手机号 ， 返回bool值， true表示手机号正确
function testMobile(val){
	var pattern =/^(0|86|17951)?(13[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|14[0-9]|19[0-9])[0-9]{8}$/;
	if (pattern.exec(val)==null) { return false ; 	}
	return true ;
}
//QQ
function isQQ(obj){
	var str=obj.value;
	var pattern =/^[0-9]{1,20}$/;
	if (pattern.exec(str)==null) {
		// obj.nextSibling.value="请输入正确的手机号";
		obj.value="";
		alert("请输入正确的QQ号");
	}

}
// 获取当前时间
function getLocaltime(){
	   var mydate = new Date();
	   var str = "" + mydate.getFullYear() + "-";
	   str += (mydate.getMonth()+1) + "-";
	   str += mydate.getDate();
	   return str;
}
 
// 格式化金额，保留两位小数 
function formatCurrency(num) {
	num = num.toString().replace(/\$|\,/g,'');
	if(isNaN(num))
		num = "0";
	sign = (num == (num = Math.abs(num)));
	num = Math.floor(num*100+0.50000000001);
	cents = num%100;
	num = Math.floor(num/100).toString();
	if(cents<10)
		cents = "0" + cents;
	for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++)
		num = num.substring(0,num.length-(4*i+3))+','+
			num.substring(num.length-(4*i+3));
	return (((sign)?'':'-') + num + '.' + cents);
}
// 设置金额为默认格式（比如199,231,000.00)
function formatMoney(number, places, symbol, thousand, decimal) {
    number = number || 0;
    places = !isNaN(places = Math.abs(places)) ? places : 2;
    symbol = symbol !== undefined ? symbol : "";
    thousand = thousand || ",";
    decimal = decimal || ".";
    var negative = number < 0 ? "-" : "",
        i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
        j = (j = i.length) > 3 ? j % 3 : 0;
    return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
}

// creator: 张旭博，2022-12-29 08:29:14，bytes自适应转换到KB,MB,GB
function formatFileSize(fileSize) {
	if (fileSize < 1024) {
		return fileSize + 'B';
	} else if (fileSize < (1024*1024)) {
		var temp = fileSize / 1024;
		temp = temp.toFixed(2);
		return temp + 'KB';
	} else if (fileSize < (1024*1024*1024)) {
		var temp = fileSize / (1024*1024);
		temp = temp.toFixed(2);
		return temp + 'MB';
	} else {
		var temp = fileSize / (1024*1024*1024);
		temp = temp.toFixed(2);
		return temp + 'GB';
	}
}

//  creator : 张旭博  2017-05-15  我的请求工具方法 - 阿拉伯数字转中文数字
var chnNumChar = ["零","一","二","三","四","五","六","七","八","九"];  // 单个数字转换用数组实现
var chnUnitSection = ["","万","亿","万亿","亿亿"];                    // 节权位同样用数组实现
var chnUnitChar = ["","十","百","千"];                               // 节内权位同样用数组实现

// creator : 张旭博  2017-05-15  我的请求工具方法 - 节内转换算法：
function SectionToChinese(section){
	var strIns = '', chnStr = '';
	var unitPos = 0;
	var zero = true;
	while(section > 0){
		var v = section % 10;
		if(v === 0){
			if(!zero){
				zero = true;
				chnStr = chnNumChar[v] + chnStr;
			}
		}else{
			zero = false;
			strIns = chnNumChar[v];
			strIns += chnUnitChar[unitPos];
			chnStr = strIns + chnStr;
		}
		unitPos++;
		section = Math.floor(section / 10);
	}
	return chnStr;
}

//   creator : 张旭博  2017-05-15  我的请求工具方法 -   转换算法主函数
function NumberToChinese(num){
	var unitPos = 0;
	var strIns = '', chnStr = '';
	var needZero = false;
	if(num === 0){
		return chnNumChar[0];
	}
	while(num > 0){
		var section = num % 10000;
		if(needZero){
			chnStr = chnNumChar[0] + chnStr;
		}
		strIns = SectionToChinese(section);
		strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
		chnStr = strIns + chnStr;
		needZero = (section < 1000) && (section > 0);
		num = Math.floor(num / 10000);
		unitPos++;
	}

	return chnStr;
}