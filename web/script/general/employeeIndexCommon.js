// creator: 张旭博，2023-07-18 10:16:55， 转换性别
function chargeSex(sex) {
    switch(sex){
        case "1":
            return "男";
            break;
        case "0":
            return "女";
            break;
        default:
            return '';
    }
}
// creator: 李玉婷，2019-12-02 10:07:28，转换婚姻状况
function chargeMarry(marry) {
    switch(marry){
        case "1":
            return "未婚";
            break;
        case "0":
            return "已婚";
            break;
        default:
            return '';
    }
}
// creator: 张旭博，2018-05-11 10:19:31，学历转换
function chargeDegree(degreeCode) {
    var degree = '';
    switch (degreeCode){
        case 1:
            degree =  "研究生";
            break;
        case 2:
            degree =  "本科";
            break;
        case 3:
            degree =  "大专";
            break;
        case 4:
            degree =  "中专或高中";
            break;
        case 5:
            degree =  "其它";
            break;
    }
    return degree
}