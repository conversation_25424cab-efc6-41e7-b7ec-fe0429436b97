 
var myApp =  angular.module("myApp",["ui.router"]) ;
myApp.config(function( $stateProvider , $urlRouterProvider){
    $urlRouterProvider.otherwise('index');
    $stateProvider
        .state( 'index' ,{
            url:'/index' ,
            templateUrl:'/script/general/zpgl/view/baseInfor.html' ,
            controller:"baseInforform"
        })
        .state( 'contectInfor' ,{
            url:'/contectInfor' ,
            templateUrl:'/script/general/zpgl/view/contectInfor.html',
            controller:"contectInfoForm"
        })
        .state('workExperience',{
            url:'/workExperience',
            templateUrl:'/script/general/zpgl/view/workExperience.html',
            controller:"workExperienceForm"
        })
        .state('eduExperience',{
            url:'/eduExperience',
            templateUrl:'/script/general/zpgl/view/eduExperience.html',
            controller:"eduExperience"
        })
        .state('familyMembers',{
            url:'/familyMembers',
            templateUrl:'/script/general/zpgl/view/familyMembers.html',
            controller:"familyMember"
        })
});
