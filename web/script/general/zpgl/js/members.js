/**
 * Created by Administrator on 2017/10/16.
 */

//个人基本信息yanzheng
myApp.controller("baseInforform",["$scope","$rootScope","$state",function ($scope,$rootScope,$state) {
    $scope.inforData={"personal":""};
    $scope.baseInfor={"myname":"","sex":"","mybirthday":"","highEdu":"","origin":"","nation":"","visage":"","maritalStatus":"","idNumber":"","address":""
        ,"salary":"","job":"","specialty":"","hobby":""};
    $scope.nameRequired=false;
    $scope.slRequired=false;
    $scope.baseInforform=function(deret){
        if(deret.myname === undefined || deret.myname === ""){
            $scope.nameRequired=true;
        }else if(deret.salary === undefined || deret.salary === ""){
            $scope.slRequired=true;
        }else{
            $scope.nameRequired=false;
            $scope.slRequired=false;
            $state.go("contectInfor");
            $scope.inforData.personal=$scope.baseInfor;
            localStorage.inforData=JSON.stringify($scope.inforData);
        }
    };
    $scope.name_change=function(){
        if($scope.baseInfor.myname === ""){
            $scope.nameRequired=true;
        }else{
            $scope.nameRequired=false;
        }
    };
    $scope.salary_change=function(){
        if($scope.baseInfor.salary === ""){
            $scope.slRequired=true;
        }else{
            $scope.slRequired=false;
        }
    };

}]);
//联系方式
myApp.controller("contectInfoForm",["$scope","$state",function ($scope,$state){
    $scope.inforData=JSON.parse(localStorage.getItem("inforData"));
    $scope.contacts={"call":"","qq":"","mail":""};
    $scope.callRequired=false;
    $scope.emailRequired=false;
    var exp = /^1[3|5][0-9]\d{4,8}$/;
    var emailExp = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
    $scope.nextWork=function(way){
        if(!exp.test(way.call)){
            $scope.callRequired=true;
            return false;
        }else{
            $state.go("workExperience");
            $scope.inforData.contact=$scope.contacts;
            localStorage.inforData=JSON.stringify($scope.inforData);
        }
    };
    $scope.call_change=function(){
        if($scope.contacts.call === ""){
            $scope.callRequired=true;
        }else{
            $scope.callRequired=false;
        }
    };
    $scope.email_change=function(){
        if(!emailExp.test($scope.contacts.mail)){
            $scope.emailRequired=true;
        }else{
            $scope.emailRequired=false;
        }
    };
}]);

//工作经历
myApp.controller("workExperienceForm",["$scope","$state",function ($scope,$state){
    $scope.inforData=JSON.parse(localStorage.getItem("inforData"));
    $scope.finish=false;
    $scope.Required=false;
    $scope.inforData.workExp=[];
    $scope.wDetail={"bus_name":"","bus_time":"","bus_salary":"","bus_position":"","bus_reason":"","bus_duty":""};
    $scope.workEpBtn=function(buss){
        if(buss.bus_salary === undefined || buss.bus_salary === ""){
            $scope.Required=true;
        }else{
            $scope.Required=false;
            $scope.finish = true;
            $scope.inforData.workExp.push($scope.wDetail);
            $scope.wDetail={"bus_name":"","bus_time":"","bus_salary":"","bus_position":"","bus_reason":"","bus_duty":""};
        }
    };
    $scope.nextEdu=function(ss){
        if(ss.bus_salary === undefined || ss.bus_salary === ""){
            $scope.Required=true;
        }else{
            $scope.Required=false;
            $state.go("eduExperience");
            $scope.inforData.workExp.push($scope.wDetail);
            localStorage.inforData=JSON.stringify($scope.inforData);
        }
    };
    $scope.bus_Salary_change=function(){
        if($scope.wDetail.bus_salary === ""){
            $scope.Required=true;
        }else{
            $scope.Required=false;
        }
    };
}]);
//教育经历
myApp.controller("eduExperience",["$scope","$state",function ($scope,$state){
    $scope.inforData=JSON.parse(localStorage.getItem("inforData"));
    $scope.finish=false;
    $scope.inforData.eduExp=[];
    $scope.eExp={"name":"","time":"","major":"","education":"","explain":""};
    $scope.eduEpBtn=function(){
        $scope.finish = true;
        $scope.inforData.eduExp.push($scope.eExp);
        $scope.eExp={"name":"","time":"","major":"","education":"","explain":""};
    };
    $scope.nextfamily=function(){
        $state.go("familyMembers");
        $scope.inforData.eduExp.push($scope.eExp);
        localStorage.inforData=JSON.stringify($scope.inforData);
    }
}]);
//家庭成员
myApp.controller("familyMember",["$scope","$rootScope",function($scope,$rootScope){
    $scope.inforData=JSON.parse(localStorage.getItem("inforData"));
    $scope.approval=false;
    $scope.Required=false;
    $scope.inforData.members=[];
    $scope.family={"name":"","sex":"","age":"","relation":""};
    $scope.addMemberbtn=function(){
        $scope.finish=true;
        $scope.inforData.members.push($scope.family);
        $scope.family={"name":"","sex":"","age":"","relation":""};
    };
    $scope.submitInfo=function(){
        if(!$scope.approval){
            $scope.Required=true;
        }else{
            $scope.Required=false;
            $scope.inforData.members.push($scope.family);
            localStorage.inforData=JSON.stringify($scope.inforData);
        }
    }
}]);