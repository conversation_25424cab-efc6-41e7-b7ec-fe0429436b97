<div class="contactInfo" ng-controller="workExperienceForm">
    <h4>工作经历</h4>
    <div class="allInfor" ng-repeat="x in inforData.workExp" ng-show="finish">
        <p>公司名称:{{x.bus_name}}</p>
        <p>工作时间:{{x.bus_time}}</p>
        <p>薪资水平:{{x.bus_salary}}</p>
        <p>在职职位:{{x.bus_position}}</p>
        <p>为继续工作的原因:{{x.bus_reason}}</p>
        <p>工作职责:{{x.bus_duty}}</p>
    </div>
    <div>
        <div class="sect">
            <span>公司名称</span>
            <input class="form-control" type="text" ng-model="wDetail.bus_name"/>
        </div>
        <div class="sect">
            <span>工作时间</span>
            <input class="form-control" type="text" ng-model="wDetail.bus_time"/>
        </div>
        <div class="sect">
            <span>薪资水平<sub>*</sub></span>
            <input class="form-control" type="text" ng-model="wDetail.bus_salary" ng-change="bus_Salary_change()" />
            <p style="color:red;" ng-show="Required" ng-model="Required">薪资水平是必填的</p>
        </div>
        <div class="sect">
            <span>在职职位</span>
            <input class="form-control" type="text" ng-model="wDetail.bus_position" />
        </div>
        <div class="sect">
            <span>为继续工作的原因</span>
            <input class="form-control" type="text" ng-model="wDetail.bus_reason" />
        </div>
        <div class="sect">
            <span>工作职责</span>
            <textarea class="form-control" rows="3" ng-model="wDetail.bus_duty"></textarea>
        </div>
    </div>
    <div class="sect overflow">
        <p class="addAgain" ng-click="workEpBtn(wDetail)">+</p>
    </div>
    <div class="sect next">
        <button class="btn btn-primary btn-lg active" ng-click="nextEdu(wDetail)">下一步</button>
    </div>
</div>