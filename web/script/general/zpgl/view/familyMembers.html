<div id="familyMembers" ng-controller="familyMember">
    <h4>家庭成员</h4>
    <form class="" action="" method="post">
        <div class="allInfor" ng-repeat="x in inforData.members" ng-show="finish">
            <p>姓名:{{x.name}}</p>
            <p>性别:{{x.sex}}</p>
            <p>年龄:{{x.age}}</p>
            <p>与本人关系:{{x.relation}}</p>
        </div>
        <div id="membersMainCon">
            <div class="sect"><span>姓名</span><input class="form-control" type="text" ng-model="family.name" /></div>
            <div class="sect">
                <span>性别</span>
                <input id="familymale" class="hd" name="sex" type="radio" value="男" ng-model="family.sex" checked/>
                <label class="sexchk" for="familymale"></label><span>男</span>
                <input id="familyfemale" class="hd" name="sex" type="radio" value="女" ng-model="family.sex"/>
                <label class="sexchk" for="familyfemale"></label><span>女</span>
            </div>
            <div class="sect"> <span>年龄</span> <input class="form-control" type="text"  ng-model="family.age" /> </div>
            <div class="sect"> <span>与本人关系</span> <input class="form-control" type="text"  ng-model="family.relation" /> </div>
        </div>
        <div class="sect overflow">
            <p class="addAgain" ng-click="addMemberbtn(family.name)">+</p>
        </div>
        <div class="sect">
            <input id="chk1" class="hd" type="checkbox" ng-model="approval" />
            <label for="chk1"><span class="chkbox"></span>以上全部填写内容经本人确认，均属实有效</label>
            <p style="color:red;" ng-show="Required" ng-model="Required">请同意本协议</p>
        </div>
        <div class="sect next overflow">
            <button class="btn btn-primary btn-lg active" ng-click="submitInfo()">下一步</button>
        </div>
    </form>
</div>