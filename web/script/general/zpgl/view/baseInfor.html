<div class="basicInfor" ng-controller="baseInforform">
    <h4>基本信息</h4>
    <div class="sect baseInfor">
        <span>照片</span>
        <input class="hd headPortrait" type="file" />
        <div class="tx-default"></div>
        <p>点击上传图片</p>
    </div>
    <div class="sect">
        <span>姓名<sub>*</sub></span>
        <input class="form-control" type="text" name="username" ng-model="baseInfor.myname" ng-change="name_change()" />
        <span style="color:red;" ng-show="nameRequired" ng-model="nameRequired">姓名是必须的。</span>
    </div>
    <div class="sect">
        <span>性别</span>
        <input id="Candidatemale" class="hd" type="radio" name="sex" value="男" ng-model="baseInfor.sex" checked/>
        <label class="sexchk" for="Candidatemale"></label><span>男</span>
        <input id="Candidatefemale" class="hd" type="radio" name="sex" value="女" ng-model="baseInfor.sex"/>
        <label class="sexchk" for="Candidatefemale"></label><span>女</span>
    </div>
    <div class="sect">
        <span>出生年月</span>
        <input class="form-control" type="date" ng-model="baseInfor.mybirthday" />
    </div>
    <div class="sect">
        <span>最高学历</span>
        <input class="form-control" type="text" ng-model="baseInfor.highEdu" />
    </div>
    <div class="sect">
        <span>籍贯</span>
        <input class="form-control" type="text" ng-model="baseInfor.origin"/>
    </div>
    <div class="sect">
        <span>民族</span>
        <input class="form-control" type="text" ng-model="baseInfor.nation" />
    </div>
    <div class="sect">
        <span>政治面貌</span>
        <input class="form-control" type="text" ng-model="baseInfor.visage" />
    </div>
    <div class="sect">
        <span>婚姻状况</span>
        <input class="form-control" type="text" ng-model="baseInfor.maritalStatus" />
    </div>
    <div class="sect">
        <span>身份证号</span>
        <input class="form-control" type="text" ng-model="baseInfor.idNumber" />
    </div>
    <div class="sect">
        <span>家庭住址</span>
        <input class="form-control" type="text" ng-model="baseInfor.address" />
    </div>
    <div class="sect">
        <span>期望资金<sub>*</sub></span>
        <input class="form-control" type="text" ng-model="baseInfor.salary" ng-change="salary_change()" />
        <span style="color:red;" ng-show="slRequired" ng-model="slRequired">期望资金是必须的。</span>
    </div>
    <div class="sect">
        <span>应聘职位</span>
        <input class="form-control" type="text" ng-model="baseInfor.job" />
    </div>
    <div class="sect">
        <span>特长</span>
        <input class="form-control" type="text" ng-model="baseInfor.specialty" />
    </div>
    <div class="sect">
        <span>爱好</span>
        <input class="form-control" type="text" ng-model="baseInfor.hobby" />
    </div>
    <div class="sect next">
        <button class="btn btn-primary btn-lg active" ng-click="baseInforform(baseInfor)">下一步</button>
    </div>
</div>