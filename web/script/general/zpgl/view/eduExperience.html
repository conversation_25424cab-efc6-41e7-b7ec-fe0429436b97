<div class="contactInfo" ng-controller="eduExperience">
    <h4>教育经历</h4>
    <div class="allInfor" ng-repeat="x in inforData.eduExp" ng-show="finish">
        <p>学校名称:{{x.name}}</p>
        <p>学习时间:{{x.time}}</p>
        <p>专业:{{x.major}}</p>
        <p>学历:{{x.education}}</p>
        <p>说明:{{x.explain}}</p>
    </div>
    <div class="sect">
        <span>学校名称</span>
        <input class="form-control" type="text" ng-model="eExp.name" />
    </div>
    <div class="sect">
        <span>学习时间</span>
        <input class="form-control" ng-model="eExp.time" type="text" />
    </div>
    <div class="sect">
        <span>专业</span>
        <input class="form-control" ng-model="eExp.major" type="text" />
    </div>
    <div class="sect">
        <span>学历</span>
        <input class="form-control" ng-model="eExp.education" type="text" />
    </div>
    <div class="sect">
        <span>说明</span>
        <textarea class="form-control" rows="2" ng-model="eExp.explain"></textarea>
    </div>
    <div class="sect overflow">
        <p class="addAgain" ng-click="eduEpBtn()">+</p>
    </div>
    <div class="sect next">
        <button class="btn btn-primary btn-lg active" ng-click="nextfamily()">下一步</button>
    </div>
</div>