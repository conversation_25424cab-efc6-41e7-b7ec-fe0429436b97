html,body,*{margin:0;padding:0;font-size:14px;}
h1,h2,h3,h4,h5,h6{font-weight:normal;}
.pageWrap{padding:10px;}
.entryInfor{width:100%;min-width:300px;}
.entryInfor_ttl{font-size:16px;text-align:center;height:34px;line-height:34px;}
.hd{display:none;}
input.hd{display:none;}
.tx-default{width:120px;height:70px;background:url("../images/default.png") left top;background-size:cover;margin:auto;}
.baseInfor p{text-align:center;}
.sect{margin-bottom:14px;}
.sect span sub{font-size:20px;color:red;font-style: inherit;vertical-align: middle;}
.next{text-align:center;}
.next button{font-size:12px;}
.lbcook{font-size:14px;}
.overflow{overflow:hidden;}
.addAgain{float:left;font-size:20px;text-align:center;color:#555;font-weight:bold;width:60px;background:#ccc;}

/*familyMembers*/
.allInfor{margin-bottom:16px;}
.allInfor p{margin-bottom:0;}

/*firststep*/
.sexchk{position:relative;background-color: #e2e4e3; border: 1px solid #aaa;padding: 5px;margin:0 8px -3px 10px;
    display: inline-block;height: 14px;box-shadow: 0 0 6px #ccc inset,0 0 1px #fff;}
.sexchk:active {box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);  }
#Candidatemale:checked + label::after,#Candidatefemale:checked + label::after,#familymale:checked + label::after,#familyfemale:checked + label::after  {
    content: '\2714';  position: absolute;  font-size: 12px;  top: -5px;  left: 0px;  color: #758794;  width: 100%;  text-align: center;  font-size: 1em;
    padding: 1px 0 0 0;  vertical-align: text-top;  }


/*家庭成员*/
#chk1 + label{font-size:14px;font-weight:normal;}
#chk1 + label .chkbox{position:relative;background-color: #e2e4e3; border: 1px solid #aaa;padding: 5px;  margin-right: 8px;display: inline-block;
    height: 14px;box-shadow: 0 0 6px #ccc inset,0 0 1px #fff;}
#chk1 + label .chkbox:active {box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);  }
#chk1:checked + label .chkbox::after {  content: '\2714';  position: absolute;  font-size: 12px;  top: -5px;  left: 0px;  color: #758794;  width: 100%;
    text-align: center;  font-size: 1em;  padding: 1px 0 0 0;  vertical-align: text-top;  }