/* =========  考勤管理主页 ===========*/
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
let openStatus = false;
$(function(){
    selectFormInit("00:00",0,$(".halfTime"));
    $(".attendanceQuery").find('input,select').val("");

    // 未设置考勤 以及 设置后还未到开始时间都不能操作按钮（除了考勤设置）
    if (setStatus === '1' && (Date.parse(new Date(startUp)) <= hostTime)) {
        $(".main button").prop("disabled", false)
        $(".main").find("input,select").prop("disabled", false)
    }

    // 查询按钮的点击事件
    $(".attendanceQuery").on("click","button",function () {
        var name = $(this).data("name");
        var page = Number($("#pageNum").val()); // 1=首页、2=统计页、3=详细页、4=考勤修改页明细、5=考勤修改页统计
        $("#byUserKey").show(); // 按职工查找只在首页不显示
        switch (name) {
            case 'countScreen':
                // 考勤统计
                if (page === 4 || page === 5) {
                    setTimeTip(5);
                } else {
                    setTimeTip(2);
                }
                // 最顶部按钮
                $(".collectionBtn .back").show().siblings().hide();
                $(".main .ty-tblContainer.count").show().siblings().hide();
                getAttendanceUsers();
                getAttendanceCount(1, 20);
                break;
            case 'detailScreen':
                //
                if (page === 4 || page === 5) {
                    setTimeTip(4);
                } else {
                    setTimeTip(3);
                }
                $(".collectionBtn .back").show().siblings().hide();
                $(".main .ty-tblContainer.detail").show().siblings().hide();
                getAttendanceUsers();
                getAttendanceDetail(1, 20);
                break;
            case 'userScreen':
                $("#departSelect").val("");
                if (page === 2 || page === 5) {
                    getAttendanceCount(1, 20);
                } else if (page === 3 || page === 4) {
                    getAttendanceDetail(1, 20);
                }
                break;
            case 'departScreen':
                $("#userKey").val("");
                if (page === 1) {
                    $("#byUserKey").hide();
                    var partId = $("#departSelect").val();
                    seeAttendantDetails(partId);
                } else if (page === 2 || page === 5) {
                    getAttendanceCount(1, 20);
                } else if (page === 3 || page === 4) {
                    getAttendanceDetail(1, 20);
                }
                break;
        }
    });

    $("#leaveTypeList").on("click", "[type='btn']", function () {
        var name= $(this).attr("name")
        switch (name) {
            case 'stopOrRecoverLeaveType':
                bounce_Fixed.show($("#tipSure"))
                var enabled = $(this).parents("tr").data("enabled")
                var enabledStr = ''
                if (enabled === 1) {
                    enabledStr = "确定后，职工请假时将无法再选择该类型。<br>确定暂停使用该请假类型吗？"
                    enabled = 0
                } else {
                    enabledStr = '确定恢复使用该请假类型吗？'
                    enabled = 1
                }
                $("#tipSure").find(".tipMsg").html(enabledStr)

                var leaveTypeId = $(this).parents("tr").data("id")
                $("#tipSure").find(".sureBtn").unbind().on("click", function () {
                    $.ajax({
                        url: '../leaveType/pauseResumeUse.do',
                        data: {
                            leaveTypeId: leaveTypeId,
                            enabled: enabled,
                            userId: sphdSocket.user.userID,
                        },
                        success: function (res) {
                            var status = res.data.status
                            if (status === 1) {
                                layer.msg("操作成功")
                                bounce_Fixed.cancel()
                                getLeaveTypeList()
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break
            case 'deleteLeaveType':
                bounce_Fixed.show($("#tipSure"))
                $("#tipSure").find(".tipMsg").html("确定删除该请假类型吗？")
                var leaveTypeId = $(this).parents("tr").data("id")
                $("#tipSure").find(".sureBtn").unbind().on("click", function () {
                    $.ajax({
                        url: '../leaveType/deleteLeaveType.do',
                        data: {
                            leaveTypeId: leaveTypeId,
                        },
                        success: function (res) {
                            var status = res.data.status
                            if (status === 1) {
                                layer.msg("操作成功")
                                bounce_Fixed.cancel()
                                getLeaveTypeList()
                            } else if (status === 2) {
                                layer.msg("删除失败！因为该请假类型已被使用过。")
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break
        }
    })
    $("#updateAttend .otherInput .ty-checkbox").on("click", function () {
        var index = $(this).index()
        $(this).addClass("checked").siblings().removeClass("checked")
        if ($(this).find("input").prop("checked")) {
            $("#updateAttend .kj-panel").eq(index).show().siblings().hide()
            if ($("#updateAttend .kj-panel").eq(index).find(".inputInfo_avatar").html() === '') {
                $("#updateAttend .kj-panel").eq(index).find(".inputInfo_avatar").html($("#updateAttend .kj-panel").eq(index).find(".input_model").html())
                $("#updateAttend .kj-panel").eq(index).find(".inputInfo_avatar .inputInfo_item_del").remove()
            }
        } else {
            $("#updateAttend .kj-panel").hide()
            $("#updateAttend .kj-panel").eq(index).find(".inputInfo_avatar").html("")
        }
    })

    setAllDeparts($("#departSelect"));
    $.ajax({//1.323 是否考勤宝模式
        url: $.webRoot + "/workAttendance/getAttendancePattern.do",
        success: function (data) {
            openStatus = data?.success===1
            console.log('getAttendancePattern.do', data, 'openStatus=', openStatus)
        }
    })
    seeAttendantDetails("");
    $(".modeSelect > span").click(function () {
        let editType = $("#attendanceNormalSetting").data("editType");
        if(editType === "add"){
            if ($(this).find('.fa-circle-o').length > 0) {
                let code = $(this).data("icon");
                let faObj = $(this).find('.fa')
                $(this).parent().find('.fa').attr('class' , 'fa fa-circle-o')
                faObj.attr('class' , 'fa fa-circle');
                $(".panelCon:gt(0)").show();
                $(".mode" + code).show();
                code === 1? $(".mode2").hide(): $(".mode1").hide();
            } else {
                $(".panelCon:gt(0)").hide();
                $(this).find('.fa').attr('class' , 'fa fa-circle-o')
            }
        }
    });
    $(".leaveCase .fa").click(function () {
        if ($(this).hasClass('fa-circle-o')) {
            $(this).parents(".leaveCase").find('.fa').attr('class' , 'fa fa-circle-o')
            $(this).attr('class' , 'fa fa-circle');
        } else {
            $(this).attr('class' , 'fa fa-circle-o')
        }
    });
    $(".weekdayCheckRadio .fa").click(function () {
        if ($(this).hasClass('fa-circle-o')) {
            $(this).parents(".weekdayCheckRadio").find('.fa').attr('class' , 'fa fa-circle-o')
            $(this).attr('class' , 'fa fa-circle');
        } else {
            $(this).attr('class' , 'fa fa-circle-o')
        }
    });
    $(".arrowBtn").on("click", function () {
        if ($(this).children("i").hasClass('fa-angle-down')) {
            $(this).children("span").html("收起");
            $(this).parents(".sdLine").find(".viewContent").show("linear");
            $(this).children("i").attr('class' , 'fa fa-angle-up');
        } else {
            $(this).children("span").html("展开");
            $(this).parents(".sdLine").find(".viewContent").hide("linear");
            $(this).children("i").attr('class' , 'fa fa-angle-down');
        }
    });
});
/* =========  首页三按钮点击事件 ===========*/

// creator: 张旭博，2020-11-08 19:11:43，考勤录入 - 按钮
function attendanceInputBtn() {
    if( $(".workOrRest").attr("type") === "work" ) {
        $(".attendanceInput").show().siblings().hide();
        getAttendentList(1,20)
    } else {
        $("#mt_tip_ms").html("今天是非考勤日，不能进行考勤录入");
        bounce.show($("#mtTip"));
    }
}

// creator: 张旭博，2020-11-08 19:15:25，考勤设置 - 按钮
function attendanceSetBtn() {
    $(".page.attendanceSet").show().siblings(".page").hide()
    // 显示设置的考勤规则
    getAccountSet(0) ;
}

// creator: 张旭博，2020-11-08 19:16:25，考勤修改 - 按钮
function attendanceUpdateBtn() {
    // 考勤修改内容展示
    $(".collectionBtn .back").show().siblings().hide();
    $(".attendanceQuery #byLastMonth").show().siblings(".queryItem").hide()
    $("#byUserKey").show()
    $(".main .ty-tblContainer.detail").show().siblings().hide();
    $("#pageNum").val(4);
    // 特殊提示展示
    var mBroad = new Date(new Date().getTime() + diff).format('yyyy年MM月');
    $("#updateAttendanceTip").show();
    $("#attendanceTip").html("以下为" + mBroad + "的考勤明细").data("date", "");
    // 获取考勤详情
    getAttendanceUsers();
    getAttendanceDetail(1, 20);
}

// updater: 张旭博，2020-11-10 18:12:11，查询考勤月查询统计
function seeAttendantDetails(id){
    setTimeTip(1);
    $.ajax({
        url: "../workAttendance/getAttendance.do",
        data: {
            "deptId": id,
            "oid": sphdSocket.user.oid
        },
        success: function (data) {
            var attendance = data.data.userList1;
            var threeTitle = "";
            if(attendance && attendance.length > 0){
                if(attendance[0].workOrRest == "1"){
                    $(".workOrRest").html("今日无需考勤").attr("type","rest");
                }else{
                    $(".workOrRest").attr("type","work");
                }
                var str ="";
                var leftTdArr = ['今日', '昨日', '前日']
                for(var i=0 ;i<attendance.length;i++ ){
                    var dateStr = attendance[i].date;
                    var allStuff = JSON.stringify(attendance[i].userList); //应出勤人员
                    var realStuff = JSON.stringify(attendance[i].realUser); //实际出勤人员
                    var notInStuff = JSON.stringify(attendance[i].notInAttendanceUser); //不参与考勤人员
                    var lateStuff = JSON.stringify(attendance[i].lateUser); //迟到人员
                    var lateUser = JSON.stringify(attendance[i].leaveEarlyUser); //早退人员
                    var outsideUser = JSON.stringify(attendance[i].outsideUser); //外出人员
                    var travelUser = JSON.stringify(attendance[i].leaveUser); //请假人员
                    var leaveUser = JSON.stringify(attendance[i].travelUser); //出差人员
                    var absenteeismUser = JSON.stringify(attendance[i].absenteeismUser); //旷工人员
                    var overTimeUser = JSON.stringify(attendance[i].overTimeUser); //加班人员
                    str +=  '<tr date="'+ attendance[i].date +'"><th>'+leftTdArr[i]+'</th>';
                    str += attendance[i].userTotalNum   ? '<td class="hover" onclick="seeDetails(0,$(this))">'+ attendance[i].userTotalNum    + '<span class="hd">'+allStuff      +'</span></td>': '<td>--</td>'
                    str += attendance[i].notInAttendance? '<td class="hover" onclick="seeDetails(9,$(this))">'+ attendance[i].notInAttendance + '<span class="hd">'+notInStuff    +'</span></td>': '<td>--</td>'
                    str += attendance[i].realNum        ? '<td class="hover" onclick="seeDetails(1,$(this))">'+ attendance[i].realNum         + '<span class="hd">'+realStuff     +'</span></td>': '<td>--</td>'
                    str += attendance[i].leaveNum       ? '<td class="hover" onclick="seeDetails(5,$(this))">'+ attendance[i].leaveNum        + '<span class="hd">'+travelUser    +'</span></td>': '<td>--</td>'
                    str += attendance[i].travelNum      ? '<td class="hover" onclick="seeDetails(6,$(this))">'+ attendance[i].travelNum       + '<span class="hd">'+leaveUser     +'</span></td>': '<td>--</td>'
                    str += attendance[i].outsideNum     ? '<td class="hover" onclick="seeDetails(4,$(this))">'+ attendance[i].outsideNum      + '<span class="hd">'+outsideUser   +'</span></td>': '<td>--</td>'
                    str += attendance[i].lateNum        ? '<td class="hover" onclick="seeDetails(2,$(this))">'+ attendance[i].lateNum         + '<span class="hd">'+lateStuff     +'</span></td>': '<td>--</td>'
                    str += attendance[i].leaveEarlyNum  ? '<td class="hover" onclick="seeDetails(3,$(this))">'+ attendance[i].leaveEarlyNum   + '<span class="hd">'+lateUser      +'</span></td>': '<td>--</td>'
                    str += attendance[i].absenteeismNum ? '<td class="hover" onclick="seeDetails(7,$(this))">'+ attendance[i].absenteeismNum  + '<span class="hd">'+absenteeismUser+'</span></td>': '<td>--</td>'
                    str += attendance[i].overTimeNum    ? '<td class="hover" onclick="seeDetails(8,$(this))">'+ attendance[i].overTimeNum     + '<span class="hd">'+overTimeUser  +'</span></td>': '<td>--</td>'
                    str +=  '</tr>';
                }
                $(".collectInfo table tbody").html(str);
            }
        }
    })
}

// updater: 张旭博，2020-11-13 16:15:47，查询考勤月查询统计 - 查看
function seeDetails(num, obj){
    // 应出勤人数 查看
    var info = JSON.parse(obj.find('.hd').html())
    var tHeadStr = ''
    var tBodyStr = ''
    if (info) {
        switch (num) {
            // 应出勤人数 - 查看
            case 0:
            // 实际出勤人数 - 查看
            case 1:
            // 外出人员 - 查看
            case 4:
            // 不参与考勤人员 - 查看
            case 9:
                tHeadStr =  '<tr>' +
                            '   <th>序号</th>' +
                            '   <th>部门</th>' +
                            '   <th>姓名</th>' +
                            '</tr>';
                for (var i = 0; i<info.length; i++) {
                    tBodyStr += '<tr>' +
                                '    <td>'+ ( + ( + i + 1)) +'</td>' +
                                '    <td>'+ handleNull(info[i].departName) +'</td>' +
                                '    <td>'+ info[i].userName +'</td>' +
                                '</tr>';
                }
                break;
            // 迟到人员 - 查看
            case 2:
            // 早退人员 - 查看
            case 3:
                tHeadStr =  '<tr>' +
                            '   <th>序号</th>' +
                            '   <th>部门</th>' +
                            '   <th>姓名</th>' +
                            '   <th>考勤人</th>' +
                            '   <th>考勤时间</th>' +
                            '</tr>';
                let icon = 0;
                for (var i = 0; i<info.length; i++) {
                    let subList = info[i].personnelAttendanceUserDetailList || [];
                    for(let t=0;t<subList.length; t++) {
                        if (Number(subList[t].type) === num) {//（2-迟到,3-早退）
                            icon++;
                            tBodyStr += '<tr>' +
                                '    <td>' + icon + '</td>' +
                                '    <td>' + handleNull(info[i].departName) + '</td>' +
                                '    <td>' + info[i].userName + '</td>' +
                                '    <td>' + subList[t].createName + '</td>' +
                                '    <td>' + new Date(subList[t].createDate).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                                '</tr>';
                        }
                    }
                }
                break;
            // 请假人员 - 查看
            case 5:
                tHeadStr =  '<tr>' +
                            '   <th>序号</th>' +
                            '   <th>部门</th>' +
                            '   <th>姓名</th>' +
                            '   <th>请假类型</th>' +
                            '   <th>详情</th>' +
                            '</tr>';
                let order = 0;
                for(var j in info){
                    var leaveList = info[j].personnelAttendanceUserDetailList || []
                    for(var i in leaveList){
                        if(leaveList[i].type === "5"){
                            order++;
                            var source = {
                                source: leaveList[i].source,
                                business: leaveList[i].business,
                                id: leaveList[i].id
                            }
                            tBodyStr += '<tr>' +
                                        '   <td>'+ order +'</td>' +
                                        '   <td>'+ handleNull(info[j].departName) +'</td>' +
                                        '   <td>'+info[j].userName+'</td>'+
                                        '   <td>'+ leaveList[i].leaveTypeName +'</td>' +
                                        '   <td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="seeLeaveRecord(\'leave\', $(this))">查看</button></td>' +
                                        '   <td class="hd">'+JSON.stringify(source)+'</td>' +
                                        '</tr>';
                        }
                    }
                }
                break;
            // 出差人员 - 查看
            case 6:
                tHeadStr =  '<tr>' +
                            '   <th>序号</th>' +
                            '   <th>部门</th>' +
                            '   <th>姓名</th>' +
                            '   <th>出差事由</th>' +
                            '</tr>';
                for (var i = 0; i<info.length; i++) {
                    tBodyStr += '<tr>' +
                                '    <td>'+ ( + i + 1) +'</td>' +
                                '    <td>'+ handleNull(info[i].departName) +'</td>' +
                                '    <td>'+ info[i].userName +'</td>' +
                                '    <td></td>' +
                                '</tr>';
                }
                break
            // 旷工人员 - 查看
            case 7:
                tHeadStr =  '<tr>' +
                            '   <th>序号</th>' +
                            '   <th>部门</th>' +
                            '   <th>姓名</th>' +
                            '   <th>开始时间</th>' +
                            '   <th>结束时间</th>' +
                            '   <th>考勤人</th>' +
                            '   <th width="180">考勤时间</th>' +
                            '</tr>';
                for (var i = 0; i<info.length; i++) {
                    let lvList = info[i].personnelAttendanceUserDetailList || [];
                    for(let t=0;t<lvList.length; t++) {
                        if (Number(lvList[t].type) === 7) {
                            tBodyStr += '<tr>' +
                                '    <td>'+ ( + i + 1) +'</td>' +
                                '    <td>'+ handleNull(info[i].departName) +'</td>' +
                                '    <td>'+ info[i].userName +'</td>' +
                                '    <td>'+ new Date(lvList[t].beginTime).format('hh:mm')+'</td>' +
                                '    <td>'+ new Date(lvList[t].endTime).format('hh:mm')+'</td>' +
                                '    <td>'+lvList[t].createName+'</td>' +
                                '    <td>'+ new Date(lvList[t].createDate).format('yyyy-MM-dd hh:mm:ss')	+'</td>' +
                                '</tr>';
                        }
                    }
                }
                break
            // 加班人员 - 查看
            case 8:
                var number = 0
                tHeadStr =  '<tr>' +
                    '   <th>序号</th>' +
                    '   <th>部门</th>' +
                    '   <th>姓名</th>' +
                    '   <th>批准时长</th>' +
                    '   <th>详情</th>' +
                    '</tr>';
                for(var i=0; i<info.length; i++){
                    var overList = info[i]["personnelAttendanceUserDetailList"] || [];
                    for(var t=0;t<overList.length;t++){
                        if(overList[t].type == "8"){
                            number = number+1;
                            var source = {
                                source: overList[t].source,
                                business: overList[t].business,
                                id: overList[t].id
                            }
                            tBodyStr += '<tr>' +
                                        '   <td>'+ number +'</td>' +
                                        '   <td>'+handleNull(info[i].departName)+'</td>' +
                                        '   <td>'+info[i].userName+'</td>' +
                                        '   <td>'+overList[t].duration+'h</td>' +
                                        '   <td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="seeLeaveRecord(\'overTime\', $(this))">查看</button></td>' +
                                        '   <td class="hd">'+JSON.stringify(source)+'</td>' +
                                        '</tr>';
                        }
                    }
                }
                break
        }
    }

    var tipArr = ['应出勤人员', '实际出勤人员', '迟到人员', '早退人员', '外出人员', '请假人员', '出差人员', '旷工人员', '加班人员', '不参与考勤人员']
    // 设置标题
    $("#seeDetails .bounce_title").html(new Date(obj.parent().attr("date")).format('YYYY年MM月DD日') + tipArr[num]) ;

    // 设置表单主体
    $("#seeDetails table thead").html(tHeadStr) ;
    $("#seeDetails table tbody").html(tBodyStr) ;
    bounce.show($("#seeDetails")) ;
}


// =========  考勤录入 ===========

//creator:lyt date:2018-5-24 考勤录入列表显示
function getAttendentList(pageCur, pageTotal){
    $.ajax({
        url:"../workAttendance/attendanceList.do",
        data: {
            "currentPageNo": pageCur,
            "pageSize": pageTotal
        },
        success:function(result) {
            // 分页设置
            var cur = result["page"]["currentPageNo"];
            var total=result["page"]["totalPage"];

            pagecur = cur;
            setPage($("#log_page") , cur , total , "attendantInput");

            // 填充数据
            var data=result['data'];
            var isFristUsed = data["startUsingSystemTime"];
            var minusFrist = isFristUsed + " 00:00:00";
            minusFrist = new Date(minusFrist).getTime();
            var deadLine = data["inputTime"];
            var nowDay = new Date().getTime() + diff;  //将时间调整为服务器时间
            nowDay = new Date(nowDay).format('yyyy-MM-dd');
            var attendanceDeadLine = nowDay + ' ' + deadLine;
            deadLine = new Date(attendanceDeadLine);
            var userList = data.userList;
            var weekDay = ["日", "一", "二", "三", "四", "五", "六"];
            var date = {}, day = "", todayStr, yesterdayStr;
            var todaySub = "";
            if (data.today) {
                day = new Date(data.today);
                //today = day;
                todaySub = day.format('yyyy-MM-dd');
                todayStr = day.format('yyyy年MM月dd日') + ' 星期' + weekDay[day.getDay()];
                date.today = day.format('yyyy-MM-dd hh:mm:ss');
                date.todayStr = todayStr;
            }
            if (data.yesterday) {
                var yesterday = new Date(data.yesterday);
                yesterdayStr = yesterday.format('yyyy年MM月dd日') + ' 星期' + weekDay[yesterday.getDay()];
                var minusYesterday = yesterday.format('yyyy-MM-dd 24:00:00');
                minusYesterday = new Date(minusYesterday).getTime()-1;
                date.yesterday = yesterday.format('yyyy-MM-dd hh:mm:ss');
                date.yestodayStr = yesterdayStr;
            }
            date = JSON.stringify(date);
            var fristStr = ""; //第一次使用系统录入考勤的表格
            var notFrist = "";//不是第一次使用系统录入考勤的表格
            var notFristThead = '<tr>' +
                '<th rowspan="3" class="bold"> 部门 </th>' +
                '<th rowspan="3" class="bold"> 姓名 </th>' +
                '<th colspan="5" class="bold">' + yesterdayStr + '</th>' +
                '<th colspan="3" class="bold">' + todayStr + '</th>' +
                '</tr><tr>' +
                '<th colspan="2" class="bold"> 上班 </th>' +
                '<th colspan="2" class="bold"> 下班 </th>' +
                '<th rowspan="2" class="bold"> 操作 </th>' +
                '<th colspan="2" class="bold"> 上班 </th>' +
                '<th rowspan="2" class="bold"> 操作 </th>' +
                '</tr><tr>' +
                '<th class="bold">状态</th>' +
                '<th class="bold">考勤时间</th>' +
                '<th class="bold">状态</th>' +
                '<th class="bold">考勤时间</th>' +
                '<th class="bold">状态</th>' +
                '<th class="bold">考勤时间</th>' +
                '</tr>';
            var fristStrHead = '<tr>' +
                '<th rowspan="3" class="bold"> 部门 </th>' +
                '<th rowspan="3" class="bold"> 姓名 </th>' +
                '<th colspan="3" class="bold">' + todayStr + '</th>' +
                '</tr><tr>' +
                '<th colspan="2" class="bold"> 上班 </th>' +
                '<th rowspan="2" class="bold"> 操作 </th>' +
                '</tr><tr>' +
                '<th class="bold">状态</th>' +
                '<th class="bold">考勤时间</th>' +
                '</tr>';
            if (userList && userList.length > 0) {
                //第一次使用系统考勤
                if (isFristUsed == todaySub || minusFrist>minusYesterday ) {
                    for (var i = 0; i < userList.length; i++) {
                        var info = JSON.stringify(userList[i]);
                        fristStr +=
                            '<tr>' +
                            '<td inf=\'' + info + '\' date=\'' + date + '\' class="hd"></td>' +
                            '<td>' + userList[i].departName + '</td>' +
                            '<td>' + userList[i].userName + '</td>';
                        if (userList[i].beginStateToday == "0") {
                            fristStr += '<td><span class="ty-color-red">未考勤</span></td>' +
                                '<td></td>' +
                                '<td>' +
                                '<button class="ty-btn ty-btn-blue ty-circle-2" workTime="' + userList[i].workBeginTime + '" onclick="inputAttendance($(this))">考勤</button>' +
                                '</td>' +
                                '</tr>';
                        } else {
                            if (userList[i].beginStateToday == "5") {
                                fristStr +=
                                    '<td><span class="ty-color-green">已请假</span></td>';
                            } else {
                                fristStr +=
                                    '<td><span class="ty-color-green">已考勤</span></td>';
                            }
                            fristStr +=
                                '<td>' + new Date(userList[i].amAttendanceToday).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                                '<td>' +
                                '<button class="ty-btn ty-btn-blue ty-circle-2" disabled>考勤</button>' +
                                '</td>' +
                                '</tr>';
                        }
                    }
                } else {//非第一次使用系统考勤
                    for (var i = 0; i < userList.length; i++) {
                        var info = JSON.stringify(userList[i]);
                        notFrist += '<tr>' +
                            '<td inf=\'' + info + '\' date=\'' + date + '\' class="hd"></td>' +
                            '<td>' + userList[i].departName + '</td>' +
                            '<td>' + userList[i].userName + '</td>';
                        //昨日已考勤
                        if (userList[i].idYesterday != "0" && userList[i].idYesterday != "" && userList[i].idYesterday != null) {
                            if (userList[i].beginStateYesterday == "5") {
                                notFrist +=
                                    '<td><span class="ty-color-green">已请假</span></td>';
                            } else if(userList[i].beginStateYesterday == "0"){
                                notFrist += '<td></td>';
                            }else{
                                notFrist +=
                                    '<td><span class="ty-color-green">已考勤</span></td>';
                            }
                            notFrist +=
                                '<td>' + new Date(userList[i].amAttendanceYesterday).format('yyyy-MM-dd hh:mm:ss') + '</td>';

                            if (userList[i].endStateYesterday == "0") {
                                //昨日下班未考勤
                                notFrist +=
                                    '<td><span class="ty-color-red">未考勤</span></td><td></td>' +
                                    '<td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="checkAttendance($(this))">考勤</button></td>' ;
                                if(userList[i].idToday != "0" && userList[i].idToday != "" && userList[i].idToday != null){
                                    notFrist +=
                                        '<td><span class="ty-color-red">未考勤</span></td><td></td>' +
                                        '<td>' +
                                        '<button class="ty-btn ty-btn-blue ty-circle-2" disabled>考勤</button>' +
                                        '</td>' +
                                        '</tr>';
                                }else{
                                    notFrist +=
                                        '<td></td><td></td><td></td>' +
                                        '</tr>';
                                }

                            } else {
                                //昨日下班已考勤
                                if (userList[i].endStateYesterday == "5") {
                                    notFrist +=
                                        '<td><span class="ty-color-green">已请假</span></td>';
                                }else {
                                    notFrist +=
                                        '<td><span class="ty-color-green">已考勤</span></td>';
                                }
                                notFrist +=
                                    '<td>' + new Date(userList[i].pmAttendanceYesterday).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                                    '<td class="bg-gray"><button class="ty-btn ty-btn-blue ty-circle-2" disabled>考勤</button></td>';
                                //今日考勤
                                if(userList[i].idToday != "0" && userList[i].idToday != "" && userList[i].idToday != null){
                                    if (userList[i].beginStateToday == "0") {
                                        notFrist +=
                                            '<td><span class="ty-color-red">未考勤</span></td><td></td>' +
                                            '<td>' +
                                            '<button class="ty-btn ty-btn-blue ty-circle-2" workTime="' + userList[i].workBeginTime + '" onclick="inputAttendance($(this))">考勤</button>' +
                                            '</td>' +
                                            '</tr>';
                                    } else {
                                        if (userList[i].beginStateToday == "5") {
                                            notFrist +=
                                                '<td class="ty-color-green">已请假</td><td>' + new Date(userList[i].amAttendanceToday).format('yyyy-MM-dd hh:mm:ss') + '</td>';
                                        } else {
                                            notFrist +=
                                                '<td class="ty-color-green">已考勤</td><td>' + new Date(userList[i].amAttendanceToday).format('yyyy-MM-dd hh:mm:ss') + '</td>';
                                        }
                                        notFrist +=
                                            '<td>' +
                                            '<button class="ty-btn ty-btn-blue ty-circle-2" disabled>考勤</button>' +
                                            '</td>' +
                                            '</tr>';
                                    }
                                }else{
                                    notFrist +=
                                        '<td></td><td></td>' +
                                        '<td></td>' +
                                        '</tr>';
                                }
                            }
                        } else {
                            notFrist +=
                                '<td></td>' +
                                '<td></td>' +
                                '<td></td>' +
                                '<td></td>' +
                                '<td class="bg-gray"></td>';
                            if (userList[i].beginStateToday == "0") {
                                notFrist +=
                                    '<td><span class="ty-color-red">未考勤</span></td><td></td>' +
                                    '<td>' +
                                    '<button class="ty-btn ty-btn-blue ty-circle-2" workTime="' + new Date(userList[i].workBeginTime).format('yyyy-MM-dd hh:mm:ss') + '" onclick="inputAttendance($(this))">考勤</button>' +
                                    '</td>' +
                                    '</tr>';
                            } else if (userList[i].beginStateToday == "5") {
                                notFrist += '<td>已请假</td><td>' + new Date(userList[i].amAttendanceToday).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                                    '<td>' +
                                    '<button class="ty-btn ty-btn-blue ty-circle-2" disabled>考勤</button>' +
                                    '</td>' +
                                    '</tr>';
                            } else {
                                notFrist += '<td class="ty-color-green">已考勤</td><td>' + new Date(userList[i].amAttendanceToday).format('yyyy-MM-dd hh:mm:ss') + '</td>' +
                                            '<td>' +
                                            '<button class="ty-btn ty-btn-blue ty-circle-2" disabled>考勤</button>' +
                                            '</td>' +
                                            '</tr>';
                            }
                        }
                    }
                }
            }
            if (isFristUsed == todaySub || minusFrist>minusYesterday) {
                fristStr = fristStrHead + fristStr;
                $("#attendInput tbody").html(fristStr);
            } else {
                notFrist = notFristThead + notFrist;
                $("#attendInput tbody").html(notFrist);
            }
        }
    })
}

//creator:lyt date:2018-5-21 第一次考勤录入，只能录入本日上午的考勤
var clockType = 1; //1：上班考勤录入  2：下班考勤录入
function inputAttendance(obj){
    clockType = 1;
    //初始化
    var userInfo = obj.parent().parent().children().attr("inf");
    $(".worker").html(userInfo);
    userInfo = JSON.parse(userInfo);
    var date = obj.parent().parent().children().attr("date");
    $(".worker").attr("dateInfo",date);
    date = JSON.parse(date);
    //outOfworkStrTime
    $('#attendanceState .fa').attr("class","fa fa-circle-o");
    $('#outofworkBtn').attr({"class":"fa fa-circle-o","outradio":"0"});
    $(".outOfwork dl").addClass("hd");
    $(".outWork").hide();
    $("#outOfworkStrTime").html("");
    $("#outOfworkEndTime").html("<option value='0'>请选择</option>");
    for(var ao=0;ao<$("#isoutWork span").length;ao++){
        var bum = 1 + ao;
        $("#isoutWork span").eq(ao).parent().css("color","#333");
        $("#isoutWork span").eq(ao).attr({"onclick":"yesOrNot(" + bum +",$(this))"});
    }
    $("#workAttend").val("7");
    $("#first_attendance .bonceHead .name").html(userInfo.userName);
    $("#first_attendance .bonceHead .name").next().html(date.todayStr);
    bounce.show($("#first_attendance"));
    selectFormInit("00:00",0,$("#outOfworkStrTime"));
    selectFormInit("00:00",0,$("#outOfworkEndTime"));
    $("#outOfworkStrTime").val("08:00");
    /*creator：李玉婷 2018/4/2  上班必填项验证*/
    var atWorkTime = obj.attr("workTime");
    bounce.everyTime('0.5s','inputAttendance',function () {
        var beginTime = $("#outOfworkStrTime").val();
        var state = 0,status =0;
        var spot = $("#outofworkBtn").attr("outradio");

        if(spot == 1){
            if(beginTime == atWorkTime){
                $("#workAttend").val("7");
                $(".outWork").show();
                $("#isoutWork span").each(function(){
                    $(this).parent().css("color","#ccc");
                    $(this).attr({"onclick":"","class":"fa fa-circle-o"});
                });
                if($("#outOfworkStrTime").val()==0){
                    $(".mornAttendSure").prop("disabled",true);
                }else{
                    $(".mornAttendSure").prop("disabled",false);
                }
            }else{
                $(".outWork").hide();
                $("#isoutWork span").eq(0).attr("onclick",'yesOrNot(1,$(this))').parent().css("color","#333");
                $("#isoutWork span").eq(1).attr("onclick",'yesOrNot(2,$(this))').parent().css("color","#333");
                if($("#attendanceState").find(".fa-dot-circle-o").length >0){
                    $(".mornAttendSure").prop("disabled",false);
                }else{
                    $(".mornAttendSure").prop("disabled",true);
                }
            }
        }else{
            if($("#attendanceState").find(".fa-dot-circle-o").length >0){
                $(".mornAttendSure").prop("disabled",false);
            }else{
                $(".mornAttendSure").prop("disabled",true);
            }
        }
    });
}

// updater: 张旭博，2020-11-24 09:55:48，返回功能
function back(obj){
    if (obj.parents(".page").hasClass('main')) {
        $(".main .back").hide().siblings().show()
        $(".main .attendanceQuery .queryItem").show()
        $(".main .attendanceQuery #byUserKey").hide()
        $(".main .attendanceQuery #byLastMonth").hide()
        $(".main .attendanceQuery #byNowMonth").hide()
        $(".main .collectInfo").show().siblings().hide()
        $(".attendanceQuery").find("select,input").val("")
    } else {
        $(".page.main").show().siblings('.page').hide()
        seeAttendantDetails("");
    }
}

// creator 姚宗涛 2018/2/25 10:21:07 考勤录入 上班考勤弹框 确定按钮
function startAttendSure() {
    //传递参数：dtype(1-今天上班考勤 2-昨天下班考勤)、date(考勤日期)、time旷工时间{begin，end}（json数据）、id(考勤id)、user(考勤人员id)、
    // type(考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班)、beginTime（上班时间）、endTime（下班时间）、breakBegin（午休开始时间(上午下班时间)）、
    // breakEnd（午休结束时间(下午上班时间)）、beginState（上班状态:0-未考勤,1-已考勤）、endState（下班状态:0-,未考勤,1-已考勤）
    var workState = $("#workAttend").val(); //1:正常 2：迟到
    var workerInfo = JSON.parse($(".worker").html());
    var dateInfo = JSON.parse($(".worker").attr("dateInfo"));
    var today = dateInfo.today.substr(0,10);
    var timeStr = [];
    if($("#outofworkBtn").attr("outRadio")==="1"){
        var outWorkEnd ="";
        var outWorkBegin = today + " " + $("#outOfworkStrTime").val() + ':00';
        if($("#outOfworkEndTime").val()!="0"){
            outWorkEnd = today + " " + $("#outOfworkEndTime").val() + ':00';
        }
        timeStr =[{"begin":outWorkBegin, "end":outWorkEnd/*,"attendanceDetailId":""*/}];
        timeStr = JSON.stringify(timeStr);
    }else if($("#outofworkBtn").attr("outRadio")==="0"){
        timeStr = JSON.stringify([{
            "begin":"",
            "end":""//,
            // "attendanceDetailId":""
        }]);
    }
    var mornClock= {
        "dtype":clockType,
        "date":today,
        "time":timeStr,
        "id":workerInfo["idToday"],
        "user":workerInfo["userID"],
        /*"beginTime":"",
        "endTime":"",
        "breakBegin":"",
        "breakEnd":"",*/
        "beginState":workState,//上班考勤
        "endState":"0" //下班考勤
    };
    $.ajax({
        url:"../workAttendance/getAttendanceRecord.do",
        data:mornClock,
        beforeSend:function(){ loading.open(); },
        success:function (data) {
            if(data.status ===1){
                var cur = $("#log_page .yecur").html();
                getAttendentList(cur,20);
            }
            bounce.cancel();
        },
        complete:function () { }
    });
}

// creator 姚宗涛 2018/2/8 10:02:09  考勤录入 下班考勤按钮
var attendObj = null ;
function checkAttendance(obj){ 
    //typeYesterday昨天类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 0-其他
    
    //初始化
    $(".isAbsent .fa").show().attr({"spot":"0","outRadio":"0"});
    $(".outOfworkAdd").hide();
    $("#offDuty_absent dl").remove();
    $("#offWorkSet .fa").attr("class","fa fa-circle-o");
    $("#endWorkAttend").val("0");
    clockType =2;
    attendObj = obj ;
    var info = obj.parent().parent().children().attr("inf");
    info = JSON.parse(info);
    var dateJson = obj.parent().parent().children().attr("date");
    date = JSON.parse(dateJson);
    info.date = dateJson;
    $(".updaterInfo p span.fa").attr("class","fa fa-circle-o");
    bounce.everyTime("0.5s","offWork",function(){
        var temp = 1,valide = 1,count =0;
        var outState = $(".isAbsent").children("span").attr("outradio");
        if(outState > 0){
            count = $("#offDuty_absent dl").length;
            $(".absentEndtime").each(function(){
                if($(this).val() == 0){
                    temp = 0;
                }
            });
        }
        if($("#endWorkAttend").val()=="0"){
            valide = 0;
        }
        if(temp == 1 && count < 10){
            $(".outOfworkAdd").prop("disabled",false);
        }else{
            $(".outOfworkAdd").prop("disabled",true);
        }
        if(temp == 1 && valide == 1){
            $(".offSureBtn").prop("disabled",false);
        }else{
            $(".offSureBtn").prop("disabled",true);
        }
    });
    var bonceTitle = obj.parent().siblings(":eq(2)").html() + date.yestodayStr;
    $(".name").html(bonceTitle) ;
    $.ajax({
        url:"../workAttendance/getAttendanceDetail.do",
        data:{"attendanceId":info.idYesterday},
        success:function (data) {
            var detail = data["personnelAttendanceUserDetails"];
            var userDetail = data["personnelAttendanceUser"];
            info.otherDetail = detail;
            info = JSON.stringify(info);
            $(".attDetails").html(info);
            $(".yestDayMorn").html(getType(userDetail.beginState)).attr("state",userDetail.beginState);
            if(detail && detail.length > 0){
                $(".isAbsent .fa").hide().attr("outRadio","1");
                if(detail[0].endTime !==""){
                    var absendRecord =  '<dl>' +
                                        '    <dd>开始时间</dd>' +
                                        '    <dd class="absentStar_ban">' + detail[0].beginTime.substr(11,5) + '</dd>' +
                                        '    <dd>结束时间</dd>' +
                                        '    <dd class="absentEnd_ban">' + detail[0].endTime.substr(11,5) + '</dd>' +
                                        '</dl>';
                    $("#offDuty_absent").html(absendRecord);
                }else{
                    var absendRecord =  '<dl>' +
                                        '    <dd>开始时间</dd>' +
                                        '    <dd class="absentStar_ban">' + detail[0].beginTime.substr(11,5) + '</dd>' +
                                        '    <dd><span class="xing">结束时间</span></dd>' +
                                        '    <dd class="absentEnd_ban">' +
                                        '     <select class="absentEndtime" id="absentEnd" name=""></select>' +
                                        '    </dd>' +
                                        '</dl>';
                    $("#offDuty_absent").html(absendRecord);
                    selectFormInit(detail[0].beginTime.substr(11,5),1,$("#absentEnd"));
                }
                $(".outOfworkAdd").show();
            }else{
                $(".isAbsent .fa").attr("outRadio","0");
            }
            bounce.show($("#attendance")) ;
        }
    });
}

// creator 姚宗涛 2018/2/25 10:21:07 考勤录入 下班考勤弹框 确定按钮
function endAttendSure() {
    //beginState和endState，值为0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他，在考勤录入中，不为0的都是已考勤，为5的是已请假
    var info = JSON.parse($(".attDetails").html());
    var date = JSON.parse(info["date"]);
    var yesterday = date.yesterday;
    var yesterdaySub = yesterday.substr(0,10);
    var workState = $("#endWorkAttend").val();
    var beginState = $(".yestDayMorn").attr("state");
    var outOfWorkDetail = info.otherDetail;
    var timeStr = [];
    if(outOfWorkDetail && outOfWorkDetail.length>0){
        if(outOfWorkDetail[0].endTime != ""){
            timeStr.push({
                "begin":info.otherDetail[0].beginTime,
                "end":info.otherDetail[0].endTime,
                "attendanceDetailId":info.otherDetail[0].id
            })
        }else{
            timeStr.push({
                "begin":info.otherDetail[0].beginTime,
                "end":yesterdaySub+" "+$("#offDuty_absent dl").eq(0).find("select").eq(0).val()+":00",
                "attendanceDetailId":info.otherDetail[0].id
            });
        }
        $("#offDuty_absent dl").each(function(){
            if($(this).index()>0){
                timeStr.push({
                    "begin":yesterdaySub+" "+$(this).find("select").eq(0).val()+":00",
                    "end":yesterdaySub+" "+$(this).find("select").eq(1).val()+":00",
                    "attendanceDetailId":""
                });
            }
        });
        timeStr = JSON.stringify(timeStr);
    }else{
        if($(".isAbsent span").attr("outRadio")=="1"){
            $("#offDuty_absent dl").each(function(){
                timeStr.push({
                    "begin":yesterdaySub+" "+$(this).find("select").eq(0).val()+":00",
                    "end":yesterdaySub+" "+$(this).find("select").eq(1).val()+":00"/*,
                    "attendanceDetailId":""*/});
            });
            timeStr = JSON.stringify(timeStr);
        }else if($(".isAbsent span").attr("outRadio")=="0"){
            timeStr = JSON.stringify([{
                "begin":"",
                "end":""//,
                // "attendanceDetailId":""
            }]);
        }
    }
    var yesterdayClock= {
        "dtype":clockType,
        "date":yesterday,
        "time":timeStr,
        "id":info["idYesterday"],
        "user":info["userID"],
        /*"beginTime":"",
        "endTime":"",
        "breakBegin":"",
        "breakEnd":"",*/
        "beginState":beginState,//上班考勤
        "endState":workState //下班考勤
    };
    $.ajax({
        url:"../workAttendance/getAttendanceRecord.do",
        data:yesterdayClock,
        beforeSend:function(){ loading.open(); },
        success:function (data) {
            if(data.status ===1){
                var cur = $("#log_page .yecur").html();
                getAttendentList(cur,20);
            }
            bounce.cancel();
        },
        complete:function () { }
    });
}

// creator 姚宗涛 2018/2/25 09:25:07 考勤录入 考勤弹框 旷工/新增按钮
var Num = 1 , outEndId = "" ,outBeginId = "";
function outOfworkAdd(obj){
    var outCount = obj.parent().next().find("dl").length;
    outBeginId = "outOfworkBeginTime" + Num ;
    outEndId = "outOfworkEndTime" + Num ;
    Num++ ;
    var str =
        '<dl class="">' +
        '    <dd><span class="xing">开始时间</span></dd>' +
        '    <dd><select class="absentEndtime" id="'+ outBeginId +'"><option value="0">请选择</option></select></dd>' +
        '    <dd><span class="xing">结束时间</span></dd>' +
        '    <dd>' +
        '        <select class="absentEndtime" id="'+outEndId+'" name="">' +
        '            <option value="0">请选择</option>' +
        '        </select>' +
        '    </dd>' ;
    if(outCount >0){
        str += '    <i class="ty-btn ty-circle-3 ty-btn-red ty-right  outOfworkDel" onclick="outOfworkDel($(this))">删除</i>'
    }
    str += '</dl>' ;
    obj.parent().next().append(str) ;
    selectFormInit("00:00",0,$("#"+outBeginId));
    selectFormInit("00:00",0,$("#"+outEndId));
}

// creator 姚宗涛 2018/2/25 09:22:07 考勤录入 考勤弹框 旷工/删除按钮
function outOfworkDel(obj) {
    obj.parent().remove() ;
    if($("#offDuty_absent dl").length=9) {
        $(".outOfworkAdd").prop("disabled",false);
    }
}

// =========  考勤设置 ===========

// creator 姚宗涛 2018/2/7  09:14:23   考勤设置  编辑  中午是否考勤按钮 /考勤录入  考勤弹框 上班考勤   （点击单选 是 否 有问题  请查看这里）
function yesOrNot(num,_this){
    _this.attr({"class" : "fa fa-dot-circle-o","att":"1"}) ;
    _this.siblings(".fa").attr("class" , "fa fa-circle-o") ;
    _this.parent().siblings().children().attr({"class" : "fa fa-circle-o","att":"0"}) ;

    _this.parent().siblings("input").val(num);
}

// creator 姚宗涛 2018/2/7  09:46:23   考勤设置  编辑  午休时间  /  考勤录入 考勤弹框  旷工
function isSure(_this){
    if(_this.attr("class") == "fa fa-circle-o"){
        _this.attr("outRadio","1");
        _this.attr("class" , "fa fa-dot-circle-o") ;
        //旷工
        if(_this.parent().next().children("dl").length>0){
            _this.parent().next().children("dl").removeClass("hd") ;
        }else{
            _this.siblings().click();
        }

        if(_this.attr("spot") == 1 && $("#workAttend").val()==7){
            _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(3)").removeClass("hd") ;
        }else if(_this.attr("spot") == 0){
            $(".outOfworkAdd").show();
        }
        _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(0)").css("color","#ccc") ;
        _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(0)").children().attr("onclick","") ;
        _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(1)").css("color","#ccc") ;
        _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(1)").children().attr("onclick","") ;
    } else {
        _this.attr("class" , "fa fa-circle-o") ;
        _this.attr("outRadio","0");
        $(".outWork").hide();
        //旷工
        if(_this.parent().next().children("dl").length>0){
            _this.parent().next().children("dl").addClass("hd") ;
        }else{
            _this.parent().next().children().remove();
        }
        if(_this.attr("spot") == 1){
            _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(3)").addClass("hd") ;
        }else if(_this.attr("spot") == 0){
            $(".outOfworkAdd").hide();
        }
        _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(0)").css("color","#333") ;
        _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(0)").children().attr("onclick","yesOrNot(1,$(this))") ;
        _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(1)").css("color","#333") ;
        _this.parent().parent().siblings(":eq(0)").children(":eq(1)").children().children(":eq(1)").children().attr("onclick","yesOrNot(0,$(this))") ;
    }
}

// creator: 张旭博，2021-06-04 09:30:22，请假类型设置
function leaveTypeSetting() {
    $(".page.leaveTypeSet").show().siblings(".page").hide()
    // 显示设置的考勤规则
    getLeaveTypeList() ;
}

// creator: 张旭博，2021-06-04 09:39:22，获取请假类型列表
function getLeaveTypeList() {
    $.ajax({
        url: '../leaveType/getAllLeaveType.do',
        data: {
            oid: sphdSocket.user.oid
        },
        success: function (res) {
            var leaveTypes = res.data.leaveTypes
            var str = ''
            for (var i in leaveTypes) {
                if (leaveTypes[i].id < 0) {
                    str +=  '<tr>' +
                            '   <td>' + leaveTypes[i].name + '</td>' +
                            '   <td>--</td>' +
                            '   <td>--</td>' +
                            '   <td></td>' +
                            '</tr>'
                } else {
                    var enabled = 1
                    var enabledStr = '--'
                    var handleStr = ''
                    if (leaveTypes[i].enabled) {
                        enabled = 1
                        handleStr = '<button class="ty-btn ty-btn-blue" type="btn" name="stopOrRecoverLeaveType">暂停使用</button>&nbsp;'+
                                    '<button class="ty-btn ty-btn-red" type="btn" name="deleteLeaveType">删除</button>';
                    } else {
                        enabled = 0
                        handleStr = '<button class="ty-btn ty-btn-blue" type="btn" name="stopOrRecoverLeaveType">恢复使用</button>';
                    }
                    if (leaveTypes[i].enabledTime) {
                        enabledStr = leaveTypes[i].updateName + ' ' + formatTime(leaveTypes[i].enabledTime, true)
                    }

                    str += '<tr data-id="'+leaveTypes[i].id+'" data-enabled="'+enabled+'">' +
                            '<td>' + leaveTypes[i].name + '</td>' +
                            '<td>' + leaveTypes[i].createName + ' ' + formatTime(leaveTypes[i].createDate, true) + '</td>' +
                            '<td>' + enabledStr +'</td>' +
                            '<td>' + handleStr + '</td>' +
                            '</tr>'
                }
            }
            $("#leaveTypeList tbody").html(str)
        }
    })
}

// creator: 张旭博，2021-06-04 10:58:33，新增请假类型
function newLeaveType() {
    bounce.show($("#newLeaveType"))
    $("#newLeaveType").find("input").val("")
}

// creator: 张旭博，2021-06-04 11:04:09，新增请假类型 - 确定按钮
function sureAddLeaveType() {
    var name = $("#newLeaveType input").val()
    if ($.trim(name) === '') {
        layer.msg("请录入要增加请假类型的名称")
        return false
    }
    var data = {
        name: name,
        userId: sphdSocket.user.userID
    }
    $.ajax({
        url: '/leaveType/addLeaveType.do',
        data: data,
        success: function (res) {
            var status = res.data.status
            var success = res.success
            if (status === 1) {
                layer.msg("操作成功")
                bounce.cancel()
                getLeaveTypeList()
            } else if (status === 2) {
                layer.msg("操作失败！因为系统中已有该请假类型。")
            } else {
                layer.msg("系统错误")
            }
        }
    })

}

// creator: 张旭博，2021-07-05 16:00:26，请假类型 - 暂停使用/恢复 - 确定
function stopOrRecoverLeaveType(enabled) {
    $.ajax({
        url: '../leaveType/stopOrRecoverLeaveType',
        data: {
            leaveTypeId: type,
            enabled: enabled,
            userId: sphdSocket.user.userID,
        },
        success: function (res) {

        }
    })
}

// =========  修改考勤 ===========

// creator : hxz 2018-04-27 获取全部部门的接口
var allDepartArr = []  ; // 存放所有部门， allDepartsTree 为树形
function setAllDeparts(obj) {
    allDepartArr = [];
    obj.html("");
    $.ajax({
        url: "../workAttendance/getCheckDepartment.do" ,
        data: {} ,
        success: function(data) {
            var list = data["data"]["organizations"] ;
            setAllDepr( list ) ;
            var str =  '<option readonly="" value="">请选择部门</option>' ;
            if(allDepartArr && allDepartArr.length >0){
                for(var i=0 ; i < allDepartArr.length; i++){
                    str += '<option value="'+ allDepartArr[i]["id"] +'">'+ allDepartArr[i]["name"] +'</option>'
                }
            }
            obj.html(str);
        }
    })
}

// creator: 侯杏哲 2018-05-02 工具方法 - 获取所有部门
function setAllDepr( arr ){
    for(var i = 0 ; i < arr.length ; i++ ){
        allDepartArr.push( {"name":arr[i]["name"],"id":arr[i]["id"] }) ;
        var sub = arr[i]["subList"] ;
        if(sub && sub.length > 0){
            setAllDepr( sub ) ;
        }
    }
}

// creator: 李玉婷，2020-07-16 19:48:59，获取职工列表的接口
function getAttendanceUsers() {
    $("#userKey").html("");
    var time = $("#attendanceTip").data("date");
    time = time.replace("-", "");
    $.ajax({
        url : "../workAttendance/getAttendanceUsers.do" ,
        data :{
            "yearMonth": time,
            "oid": sphdSocket.user.oid
        } ,
        "success":function(data){
            var list = data["data"]["listMap"] ;
            var str = '<option value="">请选择职工</option>';
            for(var i=0 ; i < list.length; i++){
                str += '<option readonly="" value="'+ list[i]["userId"] +'">'+ list[i]["userName"] +'</option>';
            }
            $("#userKey").html(str);
        },
        "complete":function() {}
    })
}

// updater: 张旭博，2020-11-20 08:44:29，考勤统计 - 查看某种考勤类型统计详情
function logs(type, obj){
    var date = $("#attendCountList").data('date');
    var userId = obj.parents("tr").data('id');
    var param = {
        ttType: 2,
        type: type,
        beginTime: date,
        userId: userId
    };
    $.ajax({
        url: "../workAttendance/getPersonnelLeave.do",
        data: param,
        success: function (data) {
            var attendanceData = data["listMap"];
            var personName = data["userName"];
            if(attendanceData && attendanceData.length>0){
                $(".personName").html(personName) ;
                var  sequence = 0;
                //  请假记录
                if( type == 5 ) {
                    $("#log .type").html("请假") ;
                    var str =   '<thead>' +
                                '    <tr>' +
                                '        <th>序号</th>' +
                                '        <th>日期</th>' +
                                '        <th>请假类型</th>' +
                                '        <th>详情</th>' +
                                '    </tr>'+
                                '</thead>'+
                                '<tbody>';
                    for(var t= 0;t<attendanceData.length;t++){
                        var seeDetail = attendanceData[t];
                        sequence = 1+t;
                        var source = {
                            source: seeDetail.source,
                            business: seeDetail.business,
                            id: seeDetail.id
                        }
                        str +=  '   <tr>' +
                                '       <td>'+ sequence +'</td>' +
                                '       <td>'+ new Date(seeDetail.attendanceDate).format('yyyy-MM-dd')+'</td>' +
                                '       <td>'+ seeDetail.leaveTypeName +'</td>'+
                                '       <td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="seeLeaveRecord(\'leave\', $(this))">查看</button></td>' +
                                '       <td class="hd">'+JSON.stringify(source)+'</td>' +
                                '   </tr>';
                    }
                    str +=      '</tbody>' ;
                }
                // 迟到记录
                if( type == 2 ){
                    $("#log .type").html("迟到") ;
                    var str =   '<thead>'+
                                '   <tr>' +
                                '       <th>序号</th>' +
                                '       <th>日期</th>' +
                                '       <th>考勤人</th>' +
                                '       <th>考勤时间</th>' +
                                '   </tr>' +
                                '</thead>'+
                                '<tbody>';
                    for(var w= 0;w<attendanceData.length;w++){
                        var seeDetail = attendanceData[w];
                        sequence = 1+w;
                        str +=  '   <tr>' +
                                '       <td>'+ sequence +'</td>' +
                                '       <td>'+ new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') +'</td>' +
                                '       <td>'+ seeDetail.createName +'</td>' +
                                '       <td>'+ new Date(seeDetail.createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                                '   </tr>';
                    }
                    str +=      '</tbody>' ;
                }
                // 早退记录
                if( type == 3 ) {
                    $("#log .type").html("早退") ;
                    var str =   '<thead>' +
                                '   <tr>' +
                                '       <th>序号</th>' +
                                '       <th>日期</th>' +
                                '       <th>考勤人</th>' +
                                '       <th>考勤时间</th>' +
                                '   </tr>'+
                                '</thead>'+
                                '<tbody>';
                    for(var q= 0;q<attendanceData.length;q++){
                        var seeDetail = attendanceData[q];
                        sequence = 1+q;
                        str +=  '   <tr>' +
                                '       <td>'+ sequence +'</td>' +
                                '       <td>'+ new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') +'</td>' +
                                '       <td>'+ seeDetail.createName +'</td>' +
                                '       <td>'+ new Date(seeDetail.createDate).format('yyyy-MM-dd hh:mm:ss')  +'</td>' +
                                '   </tr>';
                    }
                    str +=      '</tbody>';
                }
                // 旷工记录
                if( type == 7 ) {
                    $("#log .type").html("旷工") ;
                    var str =   '<thead>'+
                                '   <tr>' +
                                '       <th>序号</th>' +
                                '       <th>日期</th>' +
                                '       <th>开始时间</th>' +
                                '       <th>结束时间</th>' +
                                '       <th>考勤人</th>' +
                                '       <th>考勤时间</th>' +
                                '   </tr>'+
                                '   </thead>'+
                                '<tbdoy>';
                    for(var w= 0;w<attendanceData.length;w++){
                        var seeDetail = attendanceData[w];
                        sequence = 1+w;
                        str +=  '   <tr>' +
                                '       <td>'+ sequence +'</td>' +
                                '       <td>'+ new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') +'</td>' +
                                '       <td>'+ new Date(seeDetail.beginTime).format('hh:mm') +'</td>' +
                                '       <td>'+ new Date(seeDetail.endTime).format('hh:mm') +'</td>' +
                                '       <td>'+ seeDetail.createName +'</td>' +
                                '       <td>'+ new Date(seeDetail.createDate).format('yyyy-MM-dd hh:mm:ss') +'</td>' +
                                '   </tr>';
                    }
                    str +=      '</tbody>' ;
                }
                // 加班记录
                if( type == 8 ) {
                    $("#log .type").html("加班") ;
                    var str =   '<thead>' +
                                '   <tr>' +
                                '       <th>序号</th>' +
                                '       <th>日期</th>' +
                                '       <th>批准时长</th>' +
                                '       <th>详情</th>' +
                                '   </tr>'+
                                '</thead>'+
                                '<tbody>';
                    for(var r= 0;r<attendanceData.length;r++){
                        var seeDetail = attendanceData[r];
                        sequence = 1+r;
                        var source = {
                            source: seeDetail.source,
                            business: seeDetail.business,
                            id: seeDetail.id
                        }
                        str +=  '   <tr>' +
                                '       <td>'+sequence+'</td>' +
                                '       <td>'+ new Date(seeDetail.attendanceDate).format('yyyy-MM-dd') +'</td>' +
                                '       <td>'+ seeDetail.duration +'h</td>' +
                                '       <td><button class="ty-btn ty-btn-blue ty-circle-2" onclick="seeLeaveRecord(\'overTime\', $(this))">查看</button></td>' +
                                '       <td class="hd">'+JSON.stringify(source)+'</td>' +
                                '   </tr>';
                    }
                    str +=      '</tbody>' ;
                }
                // 出差记录
                if( type == 6 ) {
                    $("#log .type").html("出差") ;
                    for(var t= 0;t<attendanceData.length;t++){
                        sequence = 1+t;
                    }
                    var str = '' ;
                }
                // 外出记录
                if( type == 4 ) {
                    $("#log .type").html("外出") ;
                    for(var t= 0;t<attendanceData.length;t++){
                        sequence = 1+t;
                    }
                    var str = '' ;
                }
                $("#log .kj-table").html(str) ;
                bounce.show($("#log")) ;
            }
        }
    });
}

// creator: lyt date:2018/6/8 右侧表格详情查看
var clickId = "",overTimeCount = "";//记录点击的考勤id
function takeDetail(type, obj, showType){
    // showType : 考勤类型 0-全部类型 / 其他类型,只在显示时用到
    // 此接口在考勤统计详情(type: 1..) / 月考勤详情中用到(type: all)
    var thisDay = obj.attr("someInfo");
    var thisDayInfo = {};
    clickId = obj.attr("id");
    if(thisDay){
        thisDay = JSON.parse(thisDay);
        thisDayInfo = thisDay;
    }
    var param = {
        ttType: 1, // 查询某月/某天的考勤情况（1-某天的考勤情况  2-某月的考勤情况）
        type: type, // 考勤类型（1-正常,2-迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，0-其他）
        beginTime: thisDay.date, // 开始时间 (当查询某天的考勤情况时，此时间为具体的某一天；当查询为某月的考勤情况时，此时间为此月的某一天传递，后台处理)
        userId: thisDay.userID //查询考勤人员id
    };
    $.ajax({
        url: "../workAttendance/getPersonnelLeave.do",
        data: param,
        success: function (res, status, xhr) {
            var userInfo    = res.personnelAttendanceUser1; // 列表信息
            var baseInfo    = res.personnelAttendanceUserDetail;
            var inputTimeEnd= res.inputTime;
            var beginTime   = res.beginTime;
            var endTime     = res.endTime;

            var pageNum = $("#pageNum").val();
            var todaySys = new Date(xhr.getResponseHeader('Date')).format('yyyy-MM-dd');
            thisDayInfo.beginTime = beginTime;
            thisDayInfo.endTime = endTime;
            if(userInfo == null){
                //无需考勤
                if(beginTime == null){
                    layer.msg("该部门未设置考勤，不可修改");
                }else{
                    thisDayInfo.type = 'noNeedAttendance'// 用来区分是无需考勤（修改提交时调不同接口和传值）
                    $("#updateAttend").data("info", thisDayInfo)
                    attendUpdate()
                }
            }else{
                if(userInfo){
                    // showType： 区分是考勤点进来的还是直接加班时间点进来的，展示不同内容
                    if (showType) {
                        // 直接加班时间点进来的，只显示加班内容
                        $("#attendanceChangeDetail .kj-table").show().siblings().hide()
                        $("#attendanceChangeDetail .bounce_title").html(thisDayInfo.name + ' '+thisDayInfo.date + '加班情况')
                        if(baseInfo){
                            var str = ''
                            for(var t=0;t<baseInfo.length;t++){
                                var typeId = Number(baseInfo[t].type);
                                if(typeId === 8){
                                    // 赋值加班时长
                                    var source = {
                                        source: baseInfo[t].source,
                                        business: baseInfo[t].business,
                                        id: baseInfo[t].id
                                    }
                                    str +=  '<tr>' +
                                        '    <th>加班</th>' +
                                        '    <td>加班时长：'+baseInfo[t].duration+'h <button class="ty-btn ty-btn-blue ty-circle-2 ty-right" onclick="seeLeaveRecord(\'overTime\', $(this))">查看</button></td>' +
                                        '    <td class="hd">'+JSON.stringify(source)+'</td>' +
                                        '</tr>'
                                }
                            }
                        }
                        $("#attendanceChangeDetail .kj-table").html(str)
                        bounce.show($("#attendanceChangeDetail"));
                        thisDayInfo.id = userInfo.id;
                        $("#updateAttend").data("info", thisDayInfo)
                    } else {
                        // 考勤点进来的，显示全部内容
                        $("#attendanceChangeDetail .kj-table").show().siblings().show()
                        $("#attendanceChangeDetail .bounce_title").html(thisDayInfo.name + ' '+thisDayInfo.date + '考勤情况')
                        $("#attendanceChangeDetail .shouldTime").html(' ' + beginTime + ' ~ ' + endTime)
                        var str = ''
                        str +=  '<tr>' +
                                '    <th>上班</th>' +
                                '    <td>'+getType(userInfo.beginState)+'</td>' +
                                '</tr>';
                        // 未到录入时间时不显示下班
                        if (Number(userInfo.endState)) {
                            str +=  '<tr>' +
                                    '    <th>下班</th>' +
                                    '    <td>'+getType(userInfo.endState)+'</td>' +
                                    '</tr>'
                        }

                        // 展示加班请假旷工等信息
                        if(baseInfo){
                            for(var t=0;t<baseInfo.length;t++){
                                var typeId = Number(baseInfo[t].type);
                                if(typeId === 7){
                                    // 赋值旷工时间段
                                    str +=  '<tr>' +
                                            '    <th>旷工</th>' +
                                            '    <td>'+ new Date(baseInfo[t].beginTime).format('hh:mm') + ' ~ ' + new Date(baseInfo[t].endTime).format('hh:mm')+'</td>' +
                                            '</tr>'
                                }else if(typeId === 8){
                                    // 赋值加班时长
                                    var source = {
                                        source: baseInfo[t].source,
                                        business: baseInfo[t].business,
                                        id: baseInfo[t].id
                                    }
                                    str +=  '<tr>' +
                                            '    <th>加班</th>' +
                                            '    <td>加班时长：'+baseInfo[t].duration+'h <button class="ty-btn ty-btn-blue ty-circle-2 ty-right" onclick="seeLeaveRecord(\'overTime\', $(this))">查看</button></td>' +
                                            '    <td class="hd">'+JSON.stringify(source)+'</td>' +
                                            '</tr>'
                                }else if(typeId === 5){
                                    // 赋值请假时长
                                    var source = {
                                        source: baseInfo[t].source,
                                        business: baseInfo[t].business,
                                        id: baseInfo[t].id
                                    }
                                    str +=  '<tr>' +
                                            '    <th>请假 - '+baseInfo[t].leaveTypeName+'</th>' +
                                            '    <td>'+new Date(baseInfo[t].beginTime).format('hh:mm') + ' ~ ' + new Date(baseInfo[t].endTime).format('hh:mm')+' <button class="ty-btn ty-btn-blue ty-circle-2 ty-right" onclick="seeLeaveRecord(\'leave\', $(this))">查看</button></td>' +
                                            '    <td class="hd">'+JSON.stringify(source)+'</td>' +
                                            '</tr>'
                                }
                            }
                        }
                        thisDayInfo.id = userInfo.id;

                        if (thisDay.date !== todaySys && pageNum !== '3' && userInfo.endState !== "0") {
                            $("#attendanceChangeDetail .changeBtn").show()
                            // $(".editSure").attr("onclick","updateHistoryAttend()");
                        } else {
                            $("#attendanceChangeDetail .changeBtn").hide()
                        }
                        $("#attendanceChangeDetail .creator").html("创建人：" + userInfo.updateName + ' ' + new Date(userInfo.updateDate).format('yyyy-MM-dd hh:mm:ss'))
                        $("#attendanceChangeDetail .kj-table").html(str)
                        bounce.show($("#attendanceChangeDetail"));
                        $("#updateAttend").data("info", thisDayInfo)
                    }
                }
            }
        }
    });
}

// creator: 张旭博，2020-11-12 08:54:09，查看请假/加班记录
function seeLeaveRecord(name, selector) {

    bounce_Fixed.show($("#leaveRecord"))

    // 从上个接口在页面绑定的值中取值
    var sourceData = JSON.parse(selector.parents("td").siblings('.hd').html())
    var source = Number(sourceData.source)
    var id = source === 1 ? sourceData.business:sourceData.id


    switch (name) {
        case 'leave':
            $.ajax({
                url: '../workAttendance/getLeaveDetail.do',
                data: {
                    leaveId: id, // 请假详情id
                    source: source // 来源 1-审批 2-录入 source=1时，说明此请假来源为审批进入考勤系统的，那么leaveId为business字段值。 source=2时，说明此请假来源为考勤系统的录入，那么leaveId取详情id的字段值
                },
                success: function (res) {
                    var data = res.data
                    if (source === 1) {
                        var detail      = data.personnelLeave // 计划请假详情
                        var processList = data.processList1 // 计划请假的审批流程
                        var personnelLeaveItemList = data.personnelLeaveItems // 提前结束请假详情
                        
                        var str =   '<div class="bounceItem">' +
                                    '    <div class="bounceItem_title">职工姓名</div>' +
                                    '    <div class="bounceItem_content">' +
                                    '        <span class="name">'+detail.userName+'</span>' +
                                    '    </div>' +
                                    '</div>' +
                                    '<div class="ty-hr"></div>';
                        var strLeave = ''
                        if (personnelLeaveItemList) {
                            for (var i in personnelLeaveItemList) {
                                var process = personnelLeaveItemList[i].processList

                                if (personnelLeaveItemList[i].approveStatus === '2') {
                                    str +=  '<div class="kj-panel">' +
                                            '    <div class="bounceItem">' +
                                            '        <div class="bounceItem_title">提前结束请假</div>' +
                                            '        <div class="bounceItem_content"></div>' +
                                            '    </div>' +
                                            '    <div class="bounceItem">' +
                                            '        <div class="bounceItem_title">计划上班时间</div>' +
                                            '        <div class="bounceItem_content">'+moment(personnelLeaveItemList[i].actualEndTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                                            '    </div>' +
                                            '    <div class="bounceItem">' +
                                            '        <div class="bounceItem_title">说明</div>' +
                                            '        <div class="bounceItem_content">'+chargeNull(personnelLeaveItemList[i].actualReason)+'</div>' +
                                            '    </div>' +
                                            '</div>' ;
                                    str +=  '<div class="process">' +
                                            '    <div>申请人 <span class="processName">' + personnelLeaveItemList[i].createName + '</span> ' + formatTime(personnelLeaveItemList[i].createDate, true) + '</div>' ;
                                    for (var k in process) {
                                        if (process[k].approveStatus === '2' || process[k].approveStatus === '3') {
                                            str += ' <div>审批人 <span class="processName">' + process[k].userName + '</span> ' + formatTime(process[k].handleTime, true) + '</div>'
                                        }
                                    }
                                    str +=  '</div><div class="ty-hr"></div>'
                                } else {
                                    strLeave +=     '<div class="ty-hr"></div>' +
                                                    '<div class="kj-panel">' +
                                                    '    <div class="bounceItem">' +
                                                    '        <div class="bounceItem_title">提前结束请假</div>' +
                                                    '        <div class="bounceItem_content"></div>' +
                                                    '    </div>' +
                                                    '    <div class="bounceItem">' +
                                                    '        <div class="bounceItem_title">计划上班时间</div>' +
                                                    '        <div class="bounceItem_content">'+moment(personnelLeaveItemList[i].actualEndTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                                                    '    </div>' +
                                                    '    <div class="bounceItem">' +
                                                    '        <div class="bounceItem_title">说明</div>' +
                                                    '        <div class="bounceItem_content">'+chargeNull(personnelLeaveItemList[i].actualReason)+'</div>' +
                                                    '    </div>';

                                    for (var j in process) {
                                        if (process[j].approveStatus === '3') {
                                            strLeave +=     '    <div class="bounceItem ty-color-red">提前结束请假的申请被'+process[j].userName+'驳回！</div>' +
                                                            '    <div class="bounceItem ty-color-red">' +
                                                            '        <div class="bounceItem_title">驳回理由</div>' +
                                                            '        <div class="bounceItem_content">'+chargeNull(process[j].reason)+'</div>' +
                                                            '    </div>' ;
                                        }
                                    }
                                    strLeave +=     '</div>' +
                                                    '<div class="process">' +
                                                    '   <div>申请人 <span class="processName">' + personnelLeaveItemList[i].createName + '</span> ' + formatTime(personnelLeaveItemList[i].createDate, true) + '</div>' ;
                                    for (var k in process) {
                                        if (process[k].approveStatus === '2' || process[k].approveStatus === '3') {
                                            strLeave += '<div>审批人 <span class="processName">' + process[k].userName + '</span> ' + formatTime(process[k].handleTime, true) + '</div>'
                                        }

                                    }
                                    strLeave +=     '</div>' ;
                                }
                            }
                        }
                        str +=      '<div class="kj-panel">' +
                                    '    <div class="bounceItem">' +
                                    '        <div class="bounceItem_title">开始时间</div>' +
                                    '        <div class="bounceItem_content">'+moment(detail.beginTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                                    '    </div>' +
                                    '    <div class="bounceItem">' +
                                    '        <div class="bounceItem_title">结束时间</div>' +
                                    '        <div class="bounceItem_content">'+moment(detail.endTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                                    '    </div>' +
                                    '    <div class="bounceItem">' +
                                    '        <div class="bounceItem_title">请假类型</div>' +
                                    '        <div class="bounceItem_content">'+detail.leaveTypeName+'</div>' +
                                    '    </div>' +
                                    '    <div class="bounceItem">' +
                                    '        <div class="bounceItem_title">请假事由</div>' +
                                    '        <div class="bounceItem_content">'+detail.reason+'</div>' +
                                    '    </div>' +
                                    '</div>' +
                                    '<div class="process">' +
                                    '    <div>申请人 <span class="processName">' + detail.createName + '</span> ' +formatTime(detail.createDate, true) + '</div>';

                        for (var m in processList) {
                            if (processList[m].approveStatus === '2' || processList[m].approveStatus === '3') {
                                str += ' <div>审批人 <span class="processName">' + processList[m].userName + '</span> ' + formatTime(processList[m].handleTime, true) + '</div>'
                            }

                        }
                        str +=      '</div>' + strLeave;
                    } else {
                        var detail      = data.personnelAttendanceUserDetail // 请假详情

                        var str =   '<div class="bounceItem">' +
                                    '        <div class="bounceItem_title">职工姓名</div>' +
                                    '        <div class="bounceItem_content">' +
                                    '            <span class="name">'+detail.userName+'</span>' +
                                    '        </div>' +
                                    '    </div>' +
                                    '    <div class="ty-hr"></div>' +
                                    '    <div class="kj-panel">' +
                                    '        <div class="bounceItem">' +
                                    '            <div class="bounceItem_title">开始时间</div>' +
                                    '            <div class="bounceItem_content">'+moment(detail.beginTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                                    '        </div>' +
                                    '        <div class="bounceItem">' +
                                    '            <div class="bounceItem_title">结束时间</div>' +
                                    '            <div class="bounceItem_content">'+moment(detail.endTime).format('YYYY-MM-DD HH:mm')+'</div>' +
                                    '        </div>' +
                                    '        <div class="bounceItem">' +
                                    '            <div class="bounceItem_title">请假类型</div>' +
                                    '            <div class="bounceItem_content">'+detail.leaveTypeName+'</div>' +
                                    '        </div>' +
                                    '        <div class="bounceItem">' +
                                    '            <div class="bounceItem_title">请假事由</div>' +
                                    '            <div class="bounceItem_content">'+detail.reason+'</div>' +
                                    '        </div>' +
                                    '    </div>';
                    }
                    // 赋值计划请假详情
                    $("#leaveRecord .bounce_title").html('请假记录')
                    $("#leaveRecord .detail").html(str)
                }
            })
            break
        case 'overTime':
            $.ajax({
                url: '../workAttendance/getOverTimeDetail.do',
                data: {
                    overTimeId: id, // 加班详情id
                    source: source // 来源 1-审批 2-录入 source=1时，说明此请假来源为审批进入考勤系统的，那么leaveId为business字段值。 source=2时，说明此请假来源为考勤系统的录入，那么leaveId取详情id的字段值
                },
                success: function (res) {
                    var data = res.data
                    if (source === 1) {
                        var detail      = data.personnelOvertime // 加班详情
                        var processList1 = data.approvalProcess1 // 计划加班审批流程
                        var processList2 = data.approvalProcess2 // 申报加班和批准加班的审批流程
                        
                        str =   '<div class="bounceItem">' +
                                '    <div class="bounceItem_title">职工姓名</div>' +
                                '    <div class="bounceItem_content">' +
                                '        <span class="name">'+detail.userName+'</span>' +
                                '    </div>' +
                                '</div>' +
                                '<div class="bounceItem">' +
                                '    <div class="bounceItem_title">加班所属日期</div>' +
                                '    <div class="bounceItem_content">' +
                                '        <span class="overtime_beginTime">'+moment(detail.beginTime).format('YYYY-MM-DD dddd')+'</span>' +
                                '    </div>' +
                                '</div>' +
                                '<div class="ty-hr"></div>' +
                                '<div class="kj-panel approveDetail">' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">批准时长</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_approveDuration">'+detail.approveDuration+'</span>h' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">说明</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_approveExplain">'+chargeNull(detail.approveExplain)+'</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="processList">' +
                                '        <div class="process">';
                        for (var i in processList2) {
                            if (processList2[i].approveStatus === '2') {
                                str +=  '   <div>审批人 <span class="processName">'+processList2[i].userName+ '</span> ' +formatTime(processList2[i].handleTime, true)+'</div>';
                            }
                        }
                        str +=  '   </div>' +
                                '</div>' +
                                '<div class="ty-hr"></div>' +
                                '<div class="kj-panel applyDetail">' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">申报时长</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_actualDuration">'+detail.actualDuration+'h</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">申报起止时间</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_actualBeginTime">'+moment(detail.actualBeginTime).format('HH:mm')+'</span> ~ <span class="overtime_actualBeginTime">'+moment(detail.actualEndTime).format('HH:mm')+'</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">加班事由</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_actualReason">'+detail.actualReason+'</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="processList">' +
                                '        <div class="process">' +
                                '            <div>申请人 <span class="processName">' + detail.createName + '</span> ' + formatTime(detail.actualApplyTime, true)+'</div>' +
                                '        </div>' +
                                '    </div>' +
                                '</div>' +
                                '<div class="ty-hr"></div>' +
                                '<div class="kj-panel planDetail">' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">计划时长</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_duration">'+detail.duration+'h</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">计划起止时间</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_beginTime">'+moment(detail.beginTime).format('HH:mm')+'</span> ~ <span class="overtime_endTime">'+moment(detail.endTime).format('HH:mm')+'</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">加班事由</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_reason">'+handleNull(detail.reason)+'</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="processList">' +
                                '        <div class="process">' +
                                '            <div>申请人 <span class="processName">' + detail.createName + '</span> ' + formatTime(detail.createDate, true) + '</div>' ;
                        for (var j in processList2) {
                            if (processList1.length > 0 && processList1[j].approveStatus === '2') {
                                str +=  '        <div>审批人 <span class="processName">'+processList1[j].userName+ '</span> ' +formatTime(processList1[j].handleTime, true)+'</div>'
                            }
                        }
                        str +=  '            </div>' +
                                '        </div>' +
                                '    </div>';

                    } else {
                        var detail      = data.personnelAttendanceUserDetail // 加班详情
                        // 赋值加班详情
                        var str =   '<div class="bounceItem">' +
                                    '    <div class="bounceItem_title">职工姓名</div>' +
                                    '    <div class="bounceItem_content">' +
                                    '        <span class="overtime_createName">'+detail.userName+'</span>' +
                                    '    </div>' +
                                    '</div>'+
                                    '<div class="bounceItem">' +
                                    '    <div class="bounceItem_title">加班所属日期</div>' +
                                    '    <div class="bounceItem_content">' +
                                    '        <span class="overtime_beginTime">'+moment(detail.attendanceDate).format('YYYY-MM-DD dddd')+'</span>' +
                                    '    </div>' +
                                    '</div>';
                        str +=  '<div class="ty-hr"></div>' +
                                '<div class="approveDetail">' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">加班时长</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_duration">'+detail.duration+'h</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">加班事由</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_reason">'+handleNull(detail.reason)+'</span>' +
                                '        </div>' +
                                '    </div>' +
                                '    <div class="bounceItem">' +
                                '        <div class="bounceItem_title">备注</div>' +
                                '        <div class="bounceItem_content">' +
                                '            <span class="overtime_duration">'+ handleNull(detail.memo) +'</span>' +
                                '        </div>' +
                                '    </div>' +
                                '</div>'
                    }
                    $("#leaveRecord .detail").html(str)
                    $("#leaveRecord .bounce_title").html('加班记录')
                }
            })
            break
    }
}

// creator: 张旭博，2020-11-12 16:30:14，新增加班
function addNewInput(selector) {
    var html = selector.parents(".kj-panel").find(".input_model").html()
    selector.parents(".kj-panel").find(".inputInfo_avatar").append(html)
}

// creator: 张旭博，2020-11-12 16:30:14，删除加班
function delInput(selector) {
    selector.parents(".inputInfo_item").remove()
}

//  creator lyt 2018/6/13  修改考勤  复杂修改考勤
function attendUpdate() {
    var info = $("#updateAttend").data("info") // 考勤信息
    var attendanceId = info.id
    $.ajax({
        url: '/workAttendance/judgeUpdateAttendanceUser.do',
        data: {
            id: attendanceId,
            attendanceUserId: info.userID,
            attendanceDate: info.date
        },
        success: function (res) {
            var status = res.status
            if (status === 0) {
                bounce_Fixed.show($("#tipRepeat")) ;
            } else {
                bounce_Fixed.show($("#updateAttend")) ;
                var info = $("#updateAttend").data("info")
                $("#updateAttend .bounce_title").html('修改'+ info.name + ' ' + info.date + '考勤')
                $("#updateAttend").find("input:checkbox").prop("checked", false)
                $("#updateAttend").find("input:radio").prop("checked", false)
                $("#updateAttend").find("input:not(:radio), select").val("")
                $("#updateAttend").find(":radio").prop("checked", false)
                $("#updateAttend").find(":radio").prop("checked", false)
                $("#updateAttend").find(":checkbox").prop("checked", false)
                $("#updateAttend").find(".kj-panel").hide()
                $("#updateAttend").find(".kj-panel .inputInfo_avatar").html("")
                $("#updateAttend").find(".mainChange .ty-checkbox").removeClass("checked")
                $("#updateAttend .textMax").each(function () {
                    var max = $(this).attr('max')
                    $(this).html($(this).prev().val().length + '/' + max)
                })
                var data = {
                    oid: sphdSocket.user.oid
                }
                $.ajax({
                    url: '../leaveType/getLeaveTypeUsable.do',
                    data: data,
                    success: function (res) {
                        var leaveTypes = res.data.leaveTypes
                        var str = ''
                        for (var i in leaveTypes) {
                            str += '<option value="'+leaveTypes[i].id+'">'+leaveTypes[i].name+'</option>';
                        }
                        $("#updateAttend [name='leaveType']").html(str)
                    }
                })
            }
        }
    })
}

// creator: 张旭博，2020-11-13 09:08:25，修改考勤 - 确认
function sureChangeAttendance() {
    var info = $("#updateAttend").data("info") // 考勤信息
    var attendanceId = info.id
    var day = info.date
    let noNeedType = 0;

    var $upIsNormal = $("#updateAttend [name='upIsNormal']:checked")
    var $downIsNormal = $("#updateAttend [name='downIsNormal']:checked")
    var updateDesc  = $("#updateAttend [name='updateDesc']").val()

    if (updateDesc === '') {
        layer.msg("请输入修改理由！")
        return false
    }
    if ($upIsNormal.length === 0) {
        layer.msg("请选择上班考勤！")
        return false
    }
    if ($downIsNormal.length === 0) {
        layer.msg("请选择下班考勤！")
        return false
    }
    var attendance = {
        upIsNormal    : $upIsNormal.val(),     // 上班考勤 1-正常，0-迟到
        downIsNormal  : $downIsNormal.val(),   // 下班考勤 1-正常，0-早退
        isAbsenteeism : $("#updateAttend [name='isAbsenteeism']").prop("checked")?1: 0  ,// 1-旷工， 0-不旷工
        isLeave       : $("#updateAttend [name='isLeave']").prop("checked")?1: 0  ,      // 1- 请假 0-没请假
        isOverTime    : $("#updateAttend [name='isOverTime']").prop("checked")?1: 0,     // 1-加班 0-没加班
        updateDesc    : $("#updateAttend [name='updateDesc']").val()      // 修改理由
    }
    var url = '../workAttendance/applyUpdateAttendanceUser.do'
    attendance.attendanceDate = day  // 考勤时间
    attendance.attendanceUserId = info.userID  //员工id
    if (info.type === 'noNeedAttendance') {
        attendance.attendanceId = 0
        noNeedType = 1;
    } else {
        attendance.attendanceId = attendanceId   // 考勤id
    }
    var absenteeism = [], leaveList = [], overList = []
    var state = 0

    if (attendance.isLeave === 1) {
        $("#updateAttend .leavePart .inputInfo_avatar .inputInfo_item").each(function () {
            var out = {}
            $(this).find("[name]").each(function () {
                var name = $(this).attr("name")
                out[name] = $(this).val()
                if ($(this).hasClass("halfTime")) {
                    out[name] = day + ' ' + out[name] +':00'
                }
                if ($(this).val() === "" && $(this).attr("require") === "") {
                    state++
                }
            })
            leaveList.push(out)
        })
    }
    if (state > 0) {
        layer.msg("请录入完整的请假数据！");
        $("#updateAttend .otherInput .ty-checkbox").eq(0).click();
        return false
    }

    if (attendance.isOverTime === 1) {
        $("#updateAttend .overTimePart .inputInfo_avatar .inputInfo_item").each(function () {
            var out = {}
            $(this).find("[name]").each(function () {
                var name = $(this).attr("name")
                out[name] = $(this).val()
                if ($(this).hasClass("halfTime")) {
                    out[name] = day + ' ' + out[name] +':00'
                }
                if ($(this).val() === "" && $(this).attr("require") === "") {
                    state++
                }
            })
            overList.push(out)
        })
    }
    if (state > 0) {
        layer.msg("请录入完整的加班数据！");
        $("#updateAttend .otherInput .ty-checkbox").eq(1).click();
        return false
    }
    if (attendance.isAbsenteeism === 1) {
        $("#updateAttend .outPart .inputInfo_avatar .inputInfo_item").each(function () {
            var out = {}
            $(this).find("[name]").each(function () {
                var name = $(this).attr("name")
                out[name] = $(this).val()
                if ($(this).hasClass("halfTime")) {
                    out[name] = day + ' ' + out[name] +':00'
                }
                if ($(this).val() === "" && $(this).attr("require") === "") {
                    state++
                }
            })
            absenteeism.push(out)
        })
    }
    if (state > 0) {
        layer.msg("请录入完整的旷工数据！");
        $("#updateAttend .otherInput .ty-checkbox").eq(2).click();
        return false
    }

    $.ajax({
        url: url,
        data: {
            noNeedType    : noNeedType,
            ...attendance,
            //attendance: JSON.stringify([attendance]), // 考勤信息
            absenteeism: JSON.stringify(absenteeism), // 旷工信息
            leaveList: JSON.stringify(leaveList), // 请假信息
            overList: JSON.stringify(overList) // 加班信息
        },
        success: function (res) {
            var status = res.data.status
            if (status === 1) {
                bounce.cancel()
                bounce_Fixed.cancel()
                layer.msg("提交成功")
            } else if (status === 2){
                layer.msg(res.data.content);
            }
        }
    })
}

// creator lyt 2018/5/30 考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 0-其他
function getType(num) {
    switch(num){
        case "1":return "正常";break;
        case "2":return "迟到";break;
        case "3":return "早退";break;
        case "4":return "外出";break;
        case "5":return "请假";break;
        case "6":return "出差";break;
        case "7":return "旷工";break;
        case "8":return "加班";break;
        default:return "其他";break;
    }
}

// creator: 张旭博，2020-12-11 08:48:22，半小时时间段初始化
function selectFormInit(start,delay, obj ) {
    var data = []; //保存时间点
    var a = start.split(":")[0];
    var b = start.split(":")[1];
    if(a.substr(0,1) == "0"){
        a = a.substr(1,1);
    }
    for(var i=a;i<24;i++){
        if (i < 10) {
            data.push({"text": '0' + i + ':00'});
            data.push({"text": '0' + i + ':30'});
        } else {
            data.push({"text": i + ':00'});
            data.push({"text": i + ':30'});
        }
    }
    if(b === "30"){
        data.shift();
    }
    if(delay>0){
        for(var j=0;j<delay;j++){
            data.shift();
        }
    }
    var optionStr = '<option value="">---请选择---</option>'
    for (var i in data) {
        optionStr+= '<option value="'+data[i].text+'">'+data[i].text+'</option>'
    }
    obj.html(optionStr)
}

// creator: 张旭博，2020-11-16 09:55:01，修改上月考勤/ 回到本月考勤
function changeLastMonthAttendance(type) {
    // 0 本月 / 1 上月
    if (type === 0) {
        var nowDate = new Date().getTime() + diff;
        var nowMonth = new Date(nowDate).format('yyyy-MM')
        $("#attendanceTip").html("以下为" + new Date(nowDate).format('yyyy年MM月') + "的考勤明细").data("date", nowMonth);
        getAttendanceDetail(1, 20)
        $(".attendanceQuery #byLastMonth").show().siblings("").hide()
    } else if (type === 1) {
        $.ajax({
            url: '../workAttendance/checkAttendanceUpdate.do',
            data: {
                oid: sphdSocket.user.oid
            },
            success: function (res) {
                var data = res.data
                var status = data.status
                if (status === 1) {
                    var nowDate = new Date().getTime() + diff;
                    var nowYear = new Date(nowDate).getFullYear()
                    var nowMonth = new Date(nowDate).getMonth()
                    var lastMonth = nowMonth === 0?nowYear - 1 + '-12' : nowYear + '-' + (nowMonth > 9 ? nowMonth : '0' + nowMonth)
                    $("#attendanceTip").html("以下为" + new Date(lastMonth).format('yyyy年MM月') + "的考勤明细").data("date", lastMonth)
                    getAttendanceDetail(1, 20)
                    $(".attendanceQuery #byNowMonth").show().siblings("").hide()
                } else {
                    bounce.show($("#tip"))
                }
            }
        })
    }
}

// =========  考勤统计 ===========*/

// creator: 李玉婷，2020-07-17 06:56:55，获取考勤统计列表
function getAttendanceCount(currPage,pageSize){
    var userId = $("#userKey").val();
    var deptId = $("#departSelect").val();
    var yearMonth = $("#attendanceTip").data("date");
    var timeKey = yearMonth.replace("-","");
    $("#attendCountList tbody").html("");
    $.ajax({
        url:"../workAttendance/getAttendanceMonthlyStatistics.do" ,
        data:{
            "deptId": deptId,
            "userId": userId,
            "oid": sphdSocket.user.oid,
            "yearMonth": timeKey,
            "currentPageNo": currPage,
            "pageSize": pageSize
        },
        success:function(res){
            var list = res.data;
            var pageInfo = res.page

            // 设置分页
            var totalPage = pageInfo.totalPage; //总页数
            var curr = pageInfo.currentPageNo; //当前页
            setPage( $("#count_page") , curr ,  totalPage , "attendanceCount" );

            // 设置内容
            if(list && list.length > 0) {
                var html = '';
                if (yearMonth == "") {
                    yearMonth = new Date().getTime() + diff;
                    yearMonth = new Date(yearMonth).format('yyyy-MM');
                }
                var timeNote = yearMonth + '-01 00:00:01';
                for (var i = 0; i < list.length; i++) {
                    list[i].yearMonth = yearMonth
                    html += '<tr data-id="'+list[i].user+'">';
                    if (openStatus){
                        html += '<td onclick="showClockRecord($(this))"><span class="blueFont">' + list[i].userName + '</span><span class="hd">'+ JSON.stringify(list[i]) +'</span>';
                    } else {
                        html +=  '<td>' + list[i].userName +'</span>';
                    }
                    html += '</td>' +
                            '    <td>' + (list[i].departmentName?list[i].departmentName:"") + '</td>';

                    html += list[i].leaveNum         ? '<td class="hover" onclick="logs(5,$(this))">'+ list[i].leaveNum       + '</td>': '<td>0</td>'; // 请假
                    html += list[i].travelNum       ? '<td class="hover" onclick="logs(6,$(this))">'+ list[i].travelNum     + '</td>': '<td>0</td>'; // 出差
                    html += list[i].outsideNum      ? '<td class="hover" onclick="logs(4,$(this))">'+ list[i].outsideNum    + '</td>': '<td>0</td>'; // 外出
                    html += list[i].lateNum         ? '<td class="hover" onclick="logs(2,$(this))">'+ list[i].lateNum       + '</td>': '<td>0</td>'; // 迟到
                    html += list[i].leaveEarlyNum   ? '<td class="hover" onclick="logs(3,$(this))">'+ list[i].leaveEarlyNum + '</td>': '<td>0</td>'; // 早退
                    html += list[i].absenteeismNum  ? '<td class="hover" onclick="logs(7,$(this))">'+ list[i].absenteeismNum+ '</td>': '<td>0</td>'; // 旷工
                    html += list[i].overtimeNum    ? '<td class="hover" onclick="logs(8,$(this))">'+ list[i].overtimeNum  + '</td>': '<td>0</td>'; // 加班
                    html += '</tr>' ;
                }
                $("#attendCountList tbody").html(html);
                $("#attendCountList").data('date', timeNote);
            }
        }
    });
}
let clockRecordPage = {}
function showClockRecord(obj){
    let info = JSON.parse(obj.find(".hd").html())
    let yearMonth =	info.yearmonth.toString().substring(0, 4) + '-' + info.yearmonth.toString().substring(4)
    $("#recordTime").html(yearMonth);
    $("#recordName").html(info.userName	 + ' ' + yearMonth);
    $("#clockRecordList tbody tr:gt(0)").remove()
    clockRecordPage = {
        "start" : yearMonth,
        "period" : 3 ,
        "currentPageNo": 1,
        "pageSize":20,
        "userID": info.user
    }
    getClockRecord(1)
}
function getClockRecord(pageNum){
    $("#clockRecordList tbody tr:gt(0)").remove()
    clockRecordPage.currentPageNo = pageNum
    //查询考勤记录，支持：分页/不分页；按时间段/年月日时分秒；用户id/姓名；部门id/名称；打卡手机唯一码/品牌/型号；打卡器唯一码/编号；
    // 打卡记录的卡号/打卡类型等查询，字符串查询属性均支持模糊查询。见1.323接口文档
    $.ajax({
        url: $.webRoot + "/workAttendance/getPersonnelAttendancePunches.do",
        data: clockRecordPage,
        success: function (data) {
            let list = data.data || [];
            let str = ``
            let dateGroup = list.map(item => item.punchDateStr)
            let dateList = [...new Set(dateGroup)]
            let jsonStr = JSON.stringify({}) ;
            setPage( $("#clockRecordPage") , data.page.currentPageNo ,  data.page["totalPage"] , "clockRecord" ,jsonStr );
            dateList.forEach(function (item, index){
                let result = list.filter(value => value.punchDateStr == item)
                if (result.length > 0) {
                    for (let i=0;i <result.length;i++) {
                        str +=
                            `<tr>
                            ${(i===0? '<td rowspan="'+ result.length+'">'+ result[i].punchDateStr +'</td>':'' )}
                                <td>${result[i].punchTimeStr}</td>
                                <td>${result[i].punchTypeStr || ''}</td>
                                <td>${result[i].brand || ''}</td>
                                <td>${result[i].model || ''}</td>
                                <td>${result[i].terminalUuid || ''}</td>
                                <td>${result[i].sn || ''}</td>
                            </tr>`
                    }
                }
            })
            $("#clockRecordList").append(str)
        }
    });
    bounce.show($("#clockRecord"));
}
// =========  考勤明细 ===========*/

// creator: 李玉婷，2020-07-17 06:56:55，获取考勤明细列表
function getAttendanceDetail(currPage,pageSize){
    var pageNum = $("#pageNum").val();
    var userId = $("#userKey").val();
    var deptId = $("#departSelect").val();
    var yearMonth = $("#attendanceTip").data("date");
    var timeKey = yearMonth.replace("-","");
    $("#attendDetList thead").html("");
    $("#attendDetList tbody").html("");
    $.ajax({
        url: "../workAttendance/getAttendanceMonthly.do",
        data: {
            "yearMonth" : timeKey,
            "deptId" : deptId,
            "userId" : userId,
            "currentPageNo":currPage,
            "pageSize":pageSize,
            "oid": sphdSocket.user.oid
        },
        success: function (data) {
            var list = data["data"];
            // 设置分页
            var cur = data["page"]["currentPageNo"];
            var total=data["page"]["totalPage"];
            setPage( $("#eidt_page") , cur , total , "updateAttendant");
            if(yearMonth === '')yearMonth = new Date(new Date().getTime() + diff).format('yyyy-MM');
            var timeNote = yearMonth+'-01 00:00:01';
            var orgDate = new Date(timeNote);
            var orgYear = orgDate.getFullYear();
            var orgMonth = orgDate.getMonth();
            //获取该月有多少天
            var totleDays = new Date(orgYear, orgMonth + 1, 0);
            totleDays = totleDays.getDate();
            //获取1号是周几
            var firstDay = new Date(orgYear, orgMonth, 1);  //获得month的1号
            var firstDayWeekday = firstDay.getDay();
            if (firstDayWeekday == 0) {
                firstDayWeekday = 7;
            }
            var weekDay = ["日", "一", "二", "三", "四", "五", "六"];
            var rhtHtml = '';
            var tHead = '<tr>' +
                        '   <th class="fixed" rowspan="2"> 姓名 </th>' +
                        '   <th class="fixed2" rowspan="2"> 部门 </th>' +
                        '   <th class="fixed3"> 星期 </th>';
            for(var l= firstDayWeekday;l<totleDays+firstDayWeekday;l++){
                var num = l%7 ;
                tHead += '  <th>'+ weekDay[num] +'</th>';
            }
            tHead +=    '</tr>';
            tHead +=    '<tr>' +
                        '   <th class="fixed3">日期</th>';
            for(var g = 1; g <= totleDays; g++){
                tHead += '  <th><i>'+ g +'</i></th>';
            }
            tHead += '</tr>';
            $("#attendDetList thead").html(tHead);

            // 填充tbody数据
            if(list) {
                var overTimeStr = '';
                for (var h = 0; h < list.length; h++) {
                    var everyDayAttend = list[h];
                    overTimeStr = '<tr><th class="fixed3">加班</th>';
                    var variable = '';
                    var overTimeVar = '';
                    var dayStr = '<tr>' +
                        '<th class="fixed" rowspan="2" scope="row">'+ everyDayAttend.userName +'</th>' +
                        '<th class="fixed2" rowspan="2" scope="row">'+ handleNull(everyDayAttend.departmentName) +'</th>' +
                        '<th class="fixed3">考勤</th>';
                    for (var s = 1; s <= totleDays; s++) {
                        var infoDate = '';
                        if(s<10){
                            variable = everyDayAttend["dayStatus0" +s];
                            overTimeVar =everyDayAttend["overtimeDuration0" +s];
                            infoDate = yearMonth+'-0' + s;
                        }else{
                            variable = everyDayAttend["dayStatus" +s];
                            overTimeVar =everyDayAttend["overtimeDuration" +s];
                            infoDate = yearMonth +'-'+ s;
                        }
                        var info={
                            "date":infoDate,
                            "userID":everyDayAttend.user,
                            "name":everyDayAttend.userName
                        };
                        info = JSON.stringify(info);
                        switch (variable) {//1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 0-其他
                            case "complex":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(9,$(this))"><i class="fa fa-star-o"></i></td>';
                                break;
                            case "noNeed":
                                if (pageNum ==3) {
                                    dayStr += '<td><i class="fa fa-minus"></i></td>';
                                }else if (pageNum == 4) {
                                    dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(10,$(this))"><i class="fa fa-minus"></i></td>';
                                }
                                break;
                            case "normal":
                            case "overtime":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(1,$(this))"><i class="fa fa-check"></i></td>';
                                break;
                            case "late":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(2,$(this))"><i class="fa fa-level-down"></i></td>';
                                break;
                            case "leaveEarly":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(3,$(this))"><i class="fa fa-level-up"></i></td>';
                                break;
                            case "outside":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(4,$(this))"><i class="fa fa-bicycle"></i></td>';
                                break;
                            case "leave":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(5,$(this))"><i class="fa fa-clock-o"></i></td>';
                                break;
                            case "travel":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(6,$(this))"><i class="fa ty-plane"></i></td>';
                                break;
                            case "absenteeism":
                                dayStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(7,$(this))"><i class="fa fa-times"></i></td>';
                                break;
                            default:
                                dayStr += '<td><i></i></td>';
                                break;
                        }
                        if(overTimeVar) {
                            overTimeStr += '<td class="hover" someInfo=\'' + info + '\' onclick="takeDetail(1,$(this),1)"><span>' + overTimeVar + '</span></td>';
                        } else {
                            overTimeStr += '<td></td>';
                        }
                    }
                    dayStr += '</tr>';
                    overTimeStr += '</tr>';
                    rhtHtml += dayStr + overTimeStr;
                }
                $("#attendDetList tbody").html(rhtHtml);
            }
        },
    });
}

// creator: 李玉婷，2020-07-17 06:46:42，获取时间提示
function setTimeTip(page) {
    var currentTime = new Date().getTime() + diff;
    $(".main .nowDay .nowDay_date").html(new Date(currentTime).format('yyyy年MM月dd日')).data("today",new Date(currentTime).format('yyyy-MM-dd') );
    $(".main .nowDay .workOrRest").html(' 星期' + "日一二三四五六".charAt(new Date(currentTime).getDay()));

    $("#pageNum").val(page); // 1=首页、2=统计页、3=详细页、4=考勤修改页明细、5=考勤修改页统计

    var countMonth = $("#countKey").val() ? moment($("#countKey").val()).format('YYYY年MM月') : moment(currentTime).format('YYYY年MM月')
    var detailMonth = $("#detailedKey").val() ? moment($("#detailedKey").val()).format('YYYY年MM月') : moment(currentTime).format('YYYY年MM月')

    switch (page) {
        case 1:
            // 首页
            $("#attendanceTip").html("以下为近三日的考勤情况").data("date", "");
            break;
        case 2:
        case 5:
            // 统计页和考勤修改统计页
            $("#attendanceTip").html("以下为" + countMonth + "的考勤统计").data("date", $("#countKey").val());
            break;
        case 3:
        case 4:
            // 详细页和考勤修改页明细
            $("#attendanceTip").html("以下为" + detailMonth + "的考勤明细").data("date", $("#detailedKey").val());
    }

    // 考勤修改页多一条提示
    if (page === 4) {
        $("#updateAttendanceTip").show();
    } else {
        $("#updateAttendanceTip").hide();
    }
    $(".attendanceQuery").find("input, select").val("");
}
/*1.268考勤打卡-lyt*/
//creator:lyt 2023/09/21上午 08:02 倒班设置
function shiftsTip(){
    layer.msg("开发中，敬请期待……");
}
//creator:lyt 2023/09/21 上午 08:05 正常班设置
function normalClass(){
    $(".panelCon:gt(0)").hide();
    $(".modeRecord").hide();
    $("#roleConList").hide();
    $("#workRestSetting").data("state", "0");
    $(".modeSelect > span").removeClass("disabled");
    $(".modeSelect").find('.fa').attr('class' , 'fa fa-circle-o');
    $("#roleConList tbody tr:gt(0)").remove();
    $("#attendanceNormalSetting .hd").html("");
    $("#attendanceNormalSetting input").val("");
    $("#attendanceNormalSetting select").val("");
    $("#attendanceNormalSetting").data("editType","add");
    $("#attendanceNormalSetting .bounce_title").html("正常班的考勤设置");
    $("#attendanceNormalSetting .recState").html("未");
    bounce.show($("#attendanceNormalSetting"));
}

//creator:lyt 2023/09/21 上午 10:05 作息日期编辑
function setWorkRestDate(type){ //type: 0=本月状态,1=下月状态
    $("#workRestSetting").data("editType", "add");
    initWorkRest(type);
}
function initWorkRest(type){
    $("#workRestSetting").find('.fa').attr('class' , 'fa fa-circle-o');
    getHostTime(function (hosttime) { //与当前时间作比较
        let thisDate = new Date(hosttime);
        let curMonth = thisDate.getMonth();
        let initMonth = thisDate;
        if (type === 2) { //上月
            initMonth =  thisDate.setMonth(curMonth-1, 1);
        }else if (type === 1) {
            initMonth =  thisDate.setMonth(curMonth+1, 1);
        }
        initMonth = new Date(initMonth).format('yyyyMM')
        var yearMonthArr = [initMonth];
        var monthNav = "";
        if (yearMonthArr && yearMonthArr.length > 0) {
            for (var i = 0; i < yearMonthArr.length; i++) {
                if (i == 0) {
                    monthNav += '<li class="ty-active">' + yearMonthArr[i].substr(0, 4) + "年" + yearMonthArr[i].substr(4, 2) + "月" + '</li>';
                } else {
                    monthNav += '<li>' + yearMonthArr[i].substr(0, 4) + "年" + yearMonthArr[i].substr(4, 2) + "月" + '</li>';
                }
            }
        }
        $("#monthsNav").html(monthNav).data("time", type);
        $(".weekdayEdit").hide().siblings().show();
        $(".yearTab").html(yearMonthArr[0].substr(0, 4) + "年" + yearMonthArr[0].substr(4, 2) + "月");
        if (type === 0) {
            $.ajax({
                "url" : "../workAttendance/getTimeTable.do" ,
                "data" :{  "timeTable": new Date(thisDate).format('yyyy-MM-dd') } ,
                success:function (res) {
                    var monthInfo = [];
                    var thisInfoList = res["data"]["personnelAttendanceExceptions"] ;
                    if (thisInfoList && thisInfoList.length > 0) {
                        for (var i = 0; i < thisInfoList.length; i++) {
                            var date = new Date(thisInfoList[i]["exceptionDate"]).format('yyyy-MM-dd');
                            var type = thisInfoList[i]["type"];    // 1 - 假 ， 2 - 班
                            monthInfo.push({"date": date, "type": type});
                        }
                    }
                    datepicker.init($("#clendarsTab"), yearMonthArr, monthInfo,false); // 设置日历
                }
            })
        } else {
            datepicker.init($("#clendarsTab"), yearMonthArr, "",false); // 设置日历
        }
        bounce_Fixed.show($("#workRestSetting"));
        bounce_Fixed.everyTime('0.5s',workRestSetting,function () {
            if($(".weekdayEdit").is(":visible") || ($(".weekdayCheck").is(":visible") && $(".weekdayCheck").find(".fa-circle").length > 0) ) {
                $("#workRestSetting .bonceFoot").show();
            } else {
                $("#workRestSetting .bonceFoot").hide();
            }
        });
    });
}
/*creator:lyt 2023/11/7 0007 下午 6:05 */
function getTimeDetail(flag){
    let state = 0;
    if (flag === 0) {
        state = $("#thisMonthState").data("state");
    } else if (flag === 1) {
        state = $("#nextMonthState").data("state");
    } else if (flag === 2) {
        state = $("#lastMonthState").data("state");
    }
    $("#workRestSetting").data("editSource", flag);
    $("#workRestSetting").data("editType", "update");
    $("#workRestSetting .fa").attr("class", "fa fa-circle-o");
    if (state === 0){
        $("#workRestSetting").data("state", "0");
        initWorkRest(flag);
    } else {
        $("#workRestSetting").data("state", "1");
        $(".weekdayEdit").show().siblings().hide();
        $("#workRestSetting .bonceFoot").show();
        getHostTime(function (hosttime) { //与当前时间作比较
            let thisDate = new Date(hosttime);
            let thisMonth = thisDate.getMonth();
            let monthNav = '';
            let initDate = ``;
            if (flag === 1) {//下月
                initDate = thisDate.setMonth(thisMonth + 1, 1);
            } else if (flag === 2) { //上月
                initDate = thisDate.setMonth(thisMonth - 1, 1);
            }
            initDate = new Date(thisDate).format('yyyy-MM-dd')
            monthNav= '<li class="ty-active">' + initDate.substr(0,4) + "年" + initDate.substr(5,2) + "月" + '</li>';
            $("#monthsNav").html(monthNav).data("time", flag);
            $.ajax({
                "url" : "../workAttendance/getTimeTable.do" ,
                "data" :{  "timeTable": initDate } ,
                success:function (res) {
                    var monthInfo = [];
                    var thisInfoList = res["data"]["personnelAttendanceExceptions"] ;
                    if (thisInfoList && thisInfoList.length > 0) {
                        for (var i = 0; i < thisInfoList.length; i++) {
                            var date = new Date(thisInfoList[i]["exceptionDate"]).format('yyyy-MM-dd');
                            var type = thisInfoList[i]["type"];    // 1 - 假 ， 2 - 班
                            monthInfo.push({"date": date, "type": type});
                        }
                    }
                    datepicker.init($("#clendarsTab"), [initDate.substr(0,4)+ initDate.substr(5,2)], monthInfo,false); // 设置日历
                    bounce_Fixed.show($("#workRestSetting"));
                }
            })
        });
    }
}
function dateSetSure(){
    if($(".weekdayCheckRadio .fa-circle").length > 0){
        let icon = $(".weekdayCheckRadio .fa-circle").data("icon");
        if(icon === 1 || icon === 3) {
            var time =  [], mList = [];
            let nav = $("#monthsNav").data("time");
            let editType = $("#workRestSetting").data("editType");
            let source = $("#workRestSetting").data("editSource");
            $("#clendarsTab").find("td.active").each(function () {
                var dateInfo = $(this).attr("date");
                dateInfo = JSON.parse(dateInfo);
                var day = dateInfo["date"];
                var flag = icon === 1 ? $(this).data("default"): $(this).attr("flag"); // flag=1:休息日  flag=2:节假日 flag=0:工作日
                var type = 2; // 1-假，2-班
                if (flag != 0) {
                    type = 1;
                }
                if(editType === "update" && (Number(source) === 2 || Number(source) === 0)) {
                    if (Number($(this).data("default")) !== Number($(this).attr("flag"))) mList.push({"exceptionDate": day, "type": type});
                } else {
                    time.push({"date": day, "typeSet": type});
                }
            });
            bounce_Fixed.cancel();
            if(editType === "add") {
                if(nav === 0) {
                    $(".recState:eq(0)").html("已");
                    $("#recSettings").html(JSON.stringify(time));
                } else {
                    $(".recState:eq(1)").html("已");
                    $("#nextSettings").html(JSON.stringify(time));
                }
            } else {
                if (Number(source) === 2 || Number(source) === 0){
                    updatePrevMonthDate(JSON.stringify(mList), source);
                } else {
                    setDate(JSON.stringify([time]));
                }
            }
        } else {
            $(".weekdayEdit").show().siblings().hide();
        }
    }
}
//creator:lyt 2023/10/18 8:13 更换模式
function changeMode(){
    let mode = $(".cldType").html(); //1-考勤宝,2-手工录入(默认)
    $(".modeRecord").show();
    $("#roleConList").hide();
    $(".panelCon:gt(0)").show();
    $(".recState").html("未");
    $("#workRestSetting").data("state", "1");
    $("#roleConList tbody tr:gt(0)").remove();
    $("#attendanceNormalSetting .hd").html("");
    $("#attendanceNormalSetting input").val("");
    $("#attendanceNormalSetting select").val("");
    $("#attendanceNormalSetting .noSet").show();
    $("#attendanceNormalSetting .bounce_title").html("更换模式");
    $("#attendanceNormalSetting").data("editType","update");
    $("#attendanceNormalSetting .modeSelect .fa").attr("class", "fa fa-circle-o");
    if(mode === "1") {
        $(".mode1").hide();$(".mode2").show();
        $(".modeSelect > span").eq(0).addClass("disabled");
        $("#attendanceNormalSetting .modeSelect .fa").eq(1).attr("class", "fa fa-circle");
    } else {
        $(".mode1").show();$(".mode2").hide();
        $(".modeSelect > span").eq(1).addClass("disabled");
        $("#attendanceNormalSetting .modeSelect .fa").eq(0).attr("class", "fa fa-circle");
    }
    bounce.show($("#attendanceNormalSetting"));
}


// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.siblings(".textMax").text(curLength +'/' + max );
}

function chargeNull(data) {
    return data===null?'' :data
}
//creator:lyt 2023/11/9 返回
function backPage(){
    attendSet();
}
// 时间控件初始化
laydate.render({
    elem: '#countKey',
    type: 'month',
    min: startMonth,
    max: ssEnd,
    showBottom: false,
    change: function (value, date, endDate) { //监听日期被切换
        lay('#countKey').val(value);
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
        })
    }
});
laydate.render({elem: '#detailedKey',type: 'month',min: startMonth , max: ssEnd, showBottom: false,
    change: function (value, date, endDate) { //监听日期被切换
        lay('#detailedKey').val(value);
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
        })
    }});