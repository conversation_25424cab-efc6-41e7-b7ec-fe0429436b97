var datepicker = {};
//creator:lyt date:2018/4/24 获得节假日 date:201804
datepicker.getHoliday = function (date) {
    var holidayJson = {}; //保存节假日
    // 使用了同步获取数据，暂未找到好的方法
    /* $.ajax({
         url: "http://www.easybots.cn/api/holiday.php?m=" + date,
         dataType: "json",
         async: false,
         success:function (res) {
             holidayJson = res;
         },
         error:function(res){
             layer.msg("获取" + date + "节假日信息失败！");
             holidayJson = [];
         }
     });*/
    return holidayJson;
};
//creator:lyt date:2018/4/24 日历的主体部分
datepicker.getMonthData = function (yearMonth) {
    var ret = [];
    var year = yearMonth.substr(0, 4);
    var month = yearMonth.substr(4, 2);
    var firstDay = new Date(year, month - 1, 1);  //获得month的1号
    var firstDayWeekDay = firstDay.getDay();    //获得1号是星期几
    if (firstDayWeekDay === 0) firstDayWeekDay = 7;
    var lastDayOfLastMonth = new Date(year, month - 1, 0); //上一月的最后一天
    var lastDateOfLastMonth = lastDayOfLastMonth.getDate(); //上一月的最后一天的日期

    var preMonthDayCount = firstDayWeekDay - 1; //记录1号之前需要显示上月的天数

    var lastDay = new Date(year, month, 0); //获得本月最后一天
    var lastDate = lastDay.getDate();//获得本月最后一天的日期

    for (var i = 0; i < 42; i++) {
        var date = i + 1 - preMonthDayCount;
        var showDate = date;
        var thisMonth = month;
        var monthType = 1;
        if (date <= 0) {
            //上月
            thisMonth = month - 1;
            showDate = lastDateOfLastMonth + date;
            monthType = -1;
        } else if (date > lastDate) {
            //下一月
            thisMonth = month + 1;
            showDate = date - lastDate;
            monthType = -2;
        }
        if (thisMonth === 0) thisMonth = 12;
        if (thisMonth === 13) thisMonth = 1;
        var thisMonth_n = thisMonth, date_n = date;
        if (date < 10) {
            date_n = "0" + date_n
        }
        var dateInfo = year + "-" + thisMonth_n + "-" + date_n;
        var week = new Date(dateInfo).getDay();
        ret.push({
            "date": dateInfo,
            "year": year,
            "month": thisMonth,
            "day": date,
            "showDate": showDate,
            "holiday": 0,  // 0-班 ， 1-休 ， 2-节
            "type": monthType, //表示上月或本月或下月的数据
            "week": week // 0-周日，1-周一，2-周二，···6-周六
        });
    }
    return ret;
};
//creator:lyt date:2018/4/24 日历可点击与不可点击设置
datepicker.buildUi = function (yearMonthArr, initDateObj, masked) {
    // yearMonthArr - 要显示的月份数组 ( 一般只有两个 )
    // initDateObj - 设置默认对象
    var calendarStr = ""; // calendarStr - 最后生成的日历字符串
    if (yearMonthArr && yearMonthArr.length > 0) {
        for (var g = 0; g < yearMonthArr.length; g++) {
            var yearMonth = yearMonthArr[g];
            var monthData = datepicker.getMonthData(yearMonth); // 获取要展示的所有月份数据
            var dot = 0; //
            var html =
                '<table class="ty-table" id="' + yearMonth + '">' +
                '    <thead>' +
                '    <td>一</td>' +
                '    <td>二</td>' +
                '    <td>三</td>' +
                '    <td>四</td>' +
                '    <td>五</td>' +
                '    <td>六</td>' +
                '    <td>日</td>' +
                '    </thead>' +
                '    <tbody>';
            for (var i = 0; i < monthData.length; i++) {
                var dataStr = JSON.stringify(monthData[i]);
                dot = numStyle(monthData[i]["showDate"]);
                if (i % 7 === 0) {
                    html += '<tr>';
                }
                if (monthData[i]["type"] == -1 || monthData[i]["type"] == -2) { // type:1 - 本月 ， -2 - 下月 ， -1 - 上月
                    //上月或下月的数据
                    html += '<td disabled>' + monthData[i]["showDate"] + '</td>';
                } else if (monthData[i]["type"] == 1) { // 处理本月数据
                    var date2 = monthData[i]["date"];
                    if (initDateObj && initDateObj.length > 0) {
                        for (var q = 0; q < initDateObj.length; q++) { // 遍历初始化的数据
                            var type = parseInt(initDateObj[q]["type"]); // type : 1 - 休息日 ,  2 - 工作日
                            var date1 = initDateObj[q]["date"];
                            if (date2 == date1) {
                                monthData[i]["holiday"] = type;
                            }
                        }
                    }
                    switch (monthData[i]["holiday"]) {
                        case 1:
                            html += '<td class="rest active" flag="1" data-default="1" date=\'' + dataStr + '\' ><div class="day-rel"><span class="holiday_sign">休</span>' + monthData[i]["showDate"] + '</div></td>';
                            break;
                        case 2:
                            html += '<td class="work active" flag="0" data-default="0" date=\'' + dataStr + '\'  ><div class="day-rel"><span class="holiday_sign">班</span>' + monthData[i]["showDate"] + '</div></td>';
                            break;
                        default:
                            var isWeekend = monthData[i]["week"] % 6 == 0;
                            if (masked) { //containerObj.attr('id') == 'clendarsScan'
                                var content = '<i class="fa fa-minus"></i>';
                                if (isWeekend) {
                                    html += '<td class="work active" flag="1" data-default="1" date=\'' + dataStr + '\' ><div class="day-rel"><span class="holiday_sign" content = "休">' + content + '</span>' + monthData[i]["showDate"] + '</div></td>';
                                } else {
                                    html += '<td class="work active" flag="0" data-default="0" date=\'' + dataStr + '\'  ><div class="day-rel"><span class="holiday_sign" content = "班">' + content + '</span>' + monthData[i]["showDate"] + '</div></td>';
                                }
                            } else { //containerObj.attr('id')=='clendars'
                                if (isWeekend) {
                                    html += '<td class="rest active" flag="1" data-default="1" date=\'' + dataStr + '\' ><div class="day-rel"><span class="holiday_sign">休</span>' + monthData[i]["showDate"] + '</div></td>';
                                } else {
                                    html += '<td class="work active" flag="0" data-default="0" date=\'' + dataStr + '\'  ><div class="day-rel"><span class="holiday_sign">班</span>' + monthData[i]["showDate"] + '</div></td>';
                                }
                            }
                    }
                }
                if (i % 7 === 6) {
                    html += '</tr>';
                }
            }
            html += '</tbody></table>';
            calendarStr += html;
        }
    }

    return calendarStr;
};

//creator:lyt date:2018/4/24 日历整体的html代码
// update : hxz 2018-05-03 修改日历初始化传值，加入年月的规则
// obj - 日历展示的地方 ，  yearMonthArr - 要获取的月份数组,[yyyyMM]
// wyu added - masked : true/false,是否遮罩，遮罩（浏览状态）显示休/班/未设置三种图标，无遮罩（编辑状态）只显示休和班
datepicker.init = function (containerObj, yearMonthArr, initDateObj, masked) {
    var tbody = datepicker.buildUi(yearMonthArr, initDateObj, masked);
    containerObj.html(tbody);
};

//creator:lyt  小于10的数字，在数字前边填个0
function numStyle(num) {
    if (num <= 9) {
        return "0" + num;
    } else {
        return num;
    }
}





