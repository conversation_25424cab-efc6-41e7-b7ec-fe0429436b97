/*
* creator: 侯杏哲 2018-05-31  考勤管理 - 考勤设置部分
* */

$(function(){
    // 获取全部部门
    getAllDeparts() ;
    // updator: 侯杏哲  date:2018/3/29  考勤设置 - 设置 - 删除部门
    $("#initeSetting").on("click" , ".departItem" ,function(){
        $(this).remove() ;
    });
    //creator:lyt 2018/4/13 修改记录中返回按钮
    $(".recordBack").click(function(){
        $(".editRecord").hide().siblings().show();
        $(".fristSet").hide();
        $(".ty-firstTab").show().siblings(".clear").show();
    });
    // creator : hxz 2018-04-27 作息时间弹框的 班/休 切换
    $(".clendars").on("click", "td.active", function () {
        var state = $("#workRestSetting").data("state");
        //$(this).parents(".clendars").attr("id") ;
        var today = $(".nowDay_date").data("today");
        var time1 = (new Date(today)).valueOf() ;
        var date = $(this).attr("date") ;
        date = JSON.parse(date);
        date = date["date"] ;
        var time2 = (new Date(date)).valueOf() ;
        if(state !== "0" && time1>=time2 && Number($(this).data("default")) === 1){
            //layer.msg("不能修改今日及之前日期的作息规则！") ; return false ;
            layer.msg("修改今日及之前日期的作息规则，仅支持将“工作日”改作“休息日”") ; return false ;
        }
        if($(this).hasClass("work")){
            $(this).removeClass("work").addClass("rest") ;
            $(this).find(".holiday_sign").html("休") ;
            $(this).attr("flag", "1"); // flag=1:休息日  flag=2:节假日 flag=0:工作日
        }else{
            $(this).removeClass("rest").addClass("work") ;
            $(this).find(".holiday_sign").html("班") ;
            $(this).attr("flag", "0");
        }
    }) ;
    // creator : hxz 2018-04-31   查看设置的部门toggle
    $(".departTree").on("click" , "li>div" , function () {
        $(this).next().toggle();
    });
    // creator : hxz 2018-05-03 作息时间设置弹窗的 月份切换 - 适用于考勤作息设置与查看
    $(".monthsNav").on("click" , "li" , function () {
        var _index = $(this).index() ;
        $(this).attr("class","ty-active").siblings().removeClass("ty-active") ;
        $(this).parent("ul").siblings(".clendars").children("table:eq(" + _index + ")").show().siblings().hide();
    }) ;
    // creator:hxz 2018-08-20
    $("#roles").on("change" , "select" , function(){
        var ulObj = $(this).parents("ul") ;
        var workBeginTime = ulObj.find(".workBeginTime").val() ;
        var workEndTime = ulObj.find(".workEndTime").val() ;
        var isSetNoon = ulObj.find(".isSetNoon").attr("lang") ;
        var noonBeginTime = ulObj.find(".noonBeginTime").val() ;
        var noonEndTime = ulObj.find(".noonEndTime").val() ;
        if(workBeginTime == "" ){ layer.msg("请设置上班时间") ; return false ; }
        if( workEndTime == ""){ layer.msg("请设置下班时间") ;  return false ; }
        if(isSetNoon == 1){
            if(noonBeginTime == "" ){ layer.msg("请设置午休开始时间") ;  return false ; }
            if(noonEndTime == ""){ layer.msg("请设置午休结束时间") ; return false ;  }
        }
        // 判断填写的时间是否合法
        var workBegin = (new Date("2000-10-10 "+ workBeginTime)).valueOf();
        var workEnd = (new Date("2000-10-10 "+ workEndTime)).valueOf();
        var noonBegin = (new Date("2000-10-10 "+ noonBeginTime)).valueOf();
        var noonEnd = (new Date("2000-10-10 "+ noonEndTime)).valueOf();
        if(isSetNoon == 1){
            if(workBegin>=noonBegin){ layer.msg("上班时间 应小于 午休开始时间") ; return false ; }
            if(noonBegin>=noonEnd){ layer.msg("午休开始时间 应小于 午休结束时间") ;  return false ; }
            if(noonEnd>=workEnd){ layer.msg("午休结束时间 应小于 下班时间") ;  return false ; }
        }else{
            if(workBegin>=workEnd){ layer.msg("上班时间 应小于 下班时间") ;  return false ; }
        }
    })
}) ;

/* === 考勤设置 ===== */

// creator : hxz 2018-04-27 获取全部部门的接口
var allDeparts = [] , allDepartsTree = [] ,allDepartsList = [] // 存放所有部门， allDepartsTree 为树形
function getAllDeparts() {
    $.ajax({
        "url" : "../workAttendance/getCheckDepartment.do" ,
        "data" :{enabled: 1} ,
        "beforeSend":function(){ loading.open() ;  },
        "success":function(data){
            var list = data["data"]["organizations"] ;
            getAllDepr( list ) ;
            allDepartsTree = list ;
            var str = "" ;
            if(allDepartsList && allDepartsList.length >0){
                str =  '<option class="ty-opItem" value="0" onclick="tySelect($(this))">'+ "全部部门" +'</option>' ;
                for(var i=0 ; i < allDepartsList.length; i++){
                    str += '<option class="ty-opItem" value=\'{"id":'+ allDepartsList[i]["id"] +'}\' onclick="tySelect($(this))">'+ allDepartsList[i]["name"] +'</option>'
                }
            }
            $("#allDepar").html(str);
        },
        "complete":function() {}
    })
}
//  creater ：侯杏哲 2018/4/31   工具方法 - 初始化时间选择的下拉框
//update :2018/05/29 lyt 去掉了dateAll的id的多余的空格
function select2Init( obj ){
    var dateAll = []; //保存时间点
    var noontime = []; //保存午休时间点
    for(var i = 0;i<24;i++){
        if(i<10){
            dateAll.push({"id": '0' + i + ':00',"text": '0' + i + ' : 00'});
            dateAll.push({"id": '0' + i + ':30',"text": '0' + i + ' : 30'});
        }else{
            dateAll.push({"id": i + ':00',"text": i + ' : 00'});
            dateAll.push({"id": i + ':30',"text": i + ' : 30'});
        }
    }
    for(var i = 0;i<6;i++){
        var mid = 10 +i;
        noontime.push({"id": mid + ':00',"text": mid + ' : 00'});
    }
    //   select2  的初始化
    $.fn.select2.defaults.set("theme", "default");
    // 考勤设置
    // obj.find(".workBeginTime")     .select2({placeholder: "请选择（必填）", data:dateAll }).val("08:00").trigger("change");
    // obj.find(".workEndTime")     .select2({placeholder: "请选择（必填）",data:dateAll }).val("15:00").trigger("change");
    // obj.find(".noonBeginTime")     .select2({data:noontime }).val("12:00").trigger("change");
    // obj.find(".noonEndTime")     .select2({data:noontime }).val("13:00").trigger("change");
    // 考勤录入
    obj.find("#outOfworkStrTime")   .select2({placeholder: "请选择",data:dateAll }).val("08:00").trigger("change");
    obj.find("#outOfworkEndTime")   .select2({placeholder: "请选择",data:dateAll }).trigger("change");
    /*$("#absentEnd")     .select2({placeholder: "请选择（必填）",data:dateAll });
    $("#outOfworkStrTime")     .select2({placeholder: "请选择",data:dateAll });
    $("#outOfworkEndTime")     .select2({placeholder: "请选择",data:dateAll });*/
}
// updator：hxz 2018-06-13  考勤设置 - 作息时间查看
function timeTableSet(){
    $.ajax({
        "url":"../workAttendance/getTimeTable.do" ,
        "data" : {} ,
        success:function (res) {
            $("#today").val(res["today"]) ;
            var thisMonth = res["thisMonth"];
            var nextMonth = res["nextMonth"];
            var thisYear = thisMonth.substr(0,4);
            var nextYear = nextMonth.substr(0,4);
            var this_m = thisMonth.substr(5);
            var next_m = nextMonth.substr(5);
            var singleMon = thisYear==nextYear?next_m:nextMonth;
            var strNav = '<li class="ty-active">' + thisMonth + '</li><li>'+ nextMonth +'</li>' ;
            $("#monthsTip").html(thisMonth + '份与'+ singleMon +'份的作息计划如下，您可根据实际情况勾选修改。') ;
            $("#monthsScan").html(strNav) ;
            var nextInfoList = res["nextPersonnelAttendanceExceptions"] ;
            var thisInfoList = res["personnelAttendanceExceptions"] ;
            var monthInfo = [];
            if (thisInfoList && thisInfoList.length > 0) {
                for (var i = 0; i < thisInfoList.length; i++) {
                    var date = thisInfoList[i]["exceptionDate"].substr(0, 10);
                    var type = thisInfoList[i]["type"];    // 1 - 假 ， 2 - 班
                    // var flag = 0; // flag=1:休息日  flag=2:节假日 flag=0:工作日
                    // if (type == 1) {
                    //     flag = 1;
                    // } else {
                    //     flag = 0;
                    // }
                    monthInfo.push({"date": date, "type": type});
                }
            }
            if (nextInfoList && nextInfoList.length > 0) {
                for (var i = 0; i < nextInfoList.length; i++) {
                    var date = nextInfoList[i]["exceptionDate"].substr(0, 10);
                    var type = nextInfoList[i]["type"];    // 1 - 假 ， 2 - 班
                    // var flag = 0; // flag=1:休息日  flag=2:节假日 flag=0:工作日
                    // if (type == 1) {
                    //     flag = 1;
                    // } else {
                    //     flag = 0;
                    // }
                    monthInfo.push({"date": date, "type": type});
                }
            }
            //wyu：挪到上面赋值 20191106
            // var thisYear = thisMonth.substr(0,4) ;
            // var nextYear = nextMonth.substr(0,4) ;
            // var this_m = thisMonth.substr(5) ;
            // var next_m = nextMonth.substr(5) ;
            this_m = this_m.substr(0, this_m.length-1); if(Number(this_m)<10){ this_m = 0 + this_m; }
            next_m = next_m.substr(0, next_m.length-1); if(Number(next_m)<10){ next_m = 0 + next_m; }
            // var yearMonthArr = [] ;
            // if(thisYear == nextYear){ yearMonthArr = [ thisYear+this_m, thisYear+next_m ];   }else{  yearMonthArr = [thisYear+this_m, nextYear+next_m ]; }
            var yearMonthArr = [thisYear+this_m, nextYear+next_m ];
            datepicker.init($("#clendarsScan"), yearMonthArr, monthInfo, true);
            bounce.show($('#timetableSetting')) ;
            scanAttc(0);
        }
    }) ;
}
// creator：hxz 2018-06-14 考勤设置 - 作息时间查看 - 切换浏览模式、编辑模式
function scanAttc(type) {
    // 0 - 浏览模式 ； 1 - 编辑模式
    if (type == 1) {
        $("#timetableSetting .edit").show().siblings().hide();
        $("#timetableSetting .mask").hide();
    } else {
        $("#timetableSetting .scan").show().siblings().hide();
        $("#timetableSetting .mask").show();
    }
}
function beforeScanAttcs() {
    $('.holiday_sign').each(function(){
        if(typeof $(this).attr('content') != 'undefined') {
            var content=$(this).attr('content');
            $(this).attr('content', $(this).html());
            $(this).html(content);
            if(content=='休') {
                $(this).closest('.active').removeClass('work').addClass('rest');
            } else {
                $(this).closest('.active').removeClass('rest').addClass('work');
            }
        }
    });
}
// creator：hxz 2018-06-14 考勤设置 - 作息时间编辑 - 确定编辑
function editAttendanceOK() {
    var time = [];
    $("#clendarsScan").find("td.active").each(function () {
        var dateInfo = $(this).attr("date");
        var dateInfo = JSON.parse(dateInfo);
        var day = dateInfo["date"];
        var flag = $(this).attr("flag"); // flag=1:休息日  flag=2:节假日 flag=0:工作日
        var type = 2; // 1-假，2-班
        if (flag != 0) {
            type = 1;
        }
        time.push({"date": day, "typeSet": type});
    });
    setDate(JSON.stringify(time), "update");
}
// updator : hxz 2018-05-31 获取考勤规则列表
var attendanceType = 1 ; // 考勤设置类型： 1-正常班 2-倒班
function attendSet() {
    $(".page.attendanceSet").show().siblings(".page").hide()
    // 显示设置的考勤规则
    getAccountSet() ;
}
// creator：李玉婷 2018/2/31  考勤设置 -  考勤记录按钮
function editRecord(){
    bounce.show($("#attendanceSetRecord"))
    $.ajax({
        "url" : "../workAttendance/getUpdateAttendanceSetting.do" ,
        success:function (res) {
            var list = res["data"]["personnelAttendanceRecords"];
            var str = "" ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++ ){
                    var num = Number(i) + 1;
                    str += "<tr data-id='" + list[i]["id"] +"'>" +
                        "<td>"+ num +"</td>" +
                        "<td>"+ new Date(list[i]["updateDate"]).format('yyyy-MM-dd hh:mm:ss') +"</td>" +
                        "<td>"+ list[i]["updateName"] +"</td>" +
                        "<td>" + new Date(list[i]["effectDate"]).format('yyyy-MM-dd') + "</td>" +
                        "<td><span class=\"ty-color-blue\" onclick=\" logDetails($(this)); \">查看</span></td>" +
                        "</tr>" ;
                }
            }else{
                str += "<tr>" +
                    "<td colspan='5'>暂无数据</td>" +
                    "</tr>" ;
            }
            $("#attendanceSetRecord table tbody").html(str) ;
        }
    })
}
// creator: 侯杏哲 2018-05-21 考勤设置 - 修改记录 - 查看详情
function logDetails(thisObj) {
    $("#restRecordDetails tbody tr:gt(0)").remove();
    var id = thisObj.parents("tr").data('id') ;
    $.ajax({
        "url" : "../workAttendance/getUpdateAttendanceSettingDetail.do" ,
        "data": {"attendanceRecordId": id},
        success:function (res) {
            var status = res["status"];
            if (status == 0) {
                layer.msg("获取详情失败！");
                return false;
            }
            $(".recordDetailsCon").children("div:gt(0)").remove();
            var roles = [res["data"]["personnelAttendanceConfig"]]; // 全部考勤规则
            var info = res["data"]["personnelAttendanceRecord"]; // 修改的基本信息
            let str = ``;
            if (roles && info) {
                $("#infoEditDate").html(new Date(info["updateDate"]).format('yyyy-MM-dd hh:mm:ss')); // 修改时间
                $("#infoEditor").html(info["updateName"]); // 修改人
                $("#takeEffectTime").html("本修改自" + new Date(info["effectDate"]).format('yyyy年MM月dd日') + "起生效"); // 生效时间
                for(var j = 0 ; j < roles.length ; j++){
                    var depars = roles[j]["personnelAttendanceDepartmentConfigList"] ;
                    var departArr = [] , departsID = [] ;
                    if(depars && depars.length > 0){
                        for(var i = 0 ; i < depars.length ; i++){
                            departArr.push('<span info=\''+ JSON.stringify(depars[i]) +'\'>'+ depars[i]["deptName"] +'</span>') ;
                            departsID.push(depars[i]["dept"]) ;
                        }
                    }
                    var dayRole = roles[j] ;
                    dayRole["depars"] = depars ;
                    dayRole["isBreak"] = dayRole.break ;
                    str += `<tr>
                                <td>${departArr.join(",")}</td>
                                <td>${dayRole["beginTimeString"]}-${dayRole["endTimeString"]}</td>
                                <td>${dayRole["breakBeginString"]}-${dayRole["breakEndString"]}</td>
                                <td>${dayRole.break ? "是" : "否"}</td>
                                <td>${new Date(info["effectDate"]).format('yyyy-MM-dd')}</td>
                            </tr>`;
                }
                $("#restRecordDetails tbody").append(str);
                bounce_Fixed.show($('#editRecordDetails'));
            } else {
                layer.msg("未获取考勤规则！");
                return false;
            }
        }
    })
}
var thisDepar = [] ; // 当前规则使用的部门
var selectedDeparts = [] ; // 其他规则使用的部门
var departSelectedDiv = null ;
// creator: 侯杏哲 2018-04-27 考勤设置 - 设置 - 选择考勤部门 ( 设置默认的部门 )
function addDepart( obj ){
    departSelectedDiv = obj.siblings(".departSelected") ;
    selectedDeparts = []; // 其他规则选中的部门
    thisDepar = []; // 本规则选中的部门
    let trArr = null;
    let source = $("#initeSetting").data("source");
    $(".plusAll").attr("class" , "fa fa-plus-square plusAll") ;
    /*if (setAttendanceSureType == 2) { // 去设置
        $("#roleCon tbody tr:gt(0)").each(function () {
            $(this).find(".depars").children("span").each(function () {
                var info = $(this).attr("info");
                info = JSON.parse(info);
                var dept = info["departmentIds"];
                selectedDeparts.push(Number(dept))
            })
        })
    }*/
    if (source === 0) {
        trArr = $("#roleConList tbody tr:gt(0)");
    } else if (source === 2) {
        trArr = $("#roleCon tbody tr:gt(0)");
    } else {
        trArr = updateBtnObj.parents("tr").siblings();
    }
    trArr.each(function(){
        if ($(this).find(".hd").length > 0) {
            var info = $(this).find(".hd").html() ; info = JSON.parse(info) ;
            let ids = info.departmentIds.split(",");
            selectedDeparts.push(...ids) ;
        }
    }) ;
    obj.siblings(".departSelected").children().each(function () {
        var info = $(this).attr("info") ; info = JSON.parse(info) ;
        thisDepar.push(Number(info["id"])) ;
    });
    bounce_Fixed2.show( $("#scanSet") ) ;
    $("#allRight").html("");
    $("#nowRight").html("");
    var depars = allDepartsTree ;
    var left = depars , right = [] ;
    var str1 = setStr(left , "plus" ) ;
    $("#allRight").html(str1) ;
    // 已设置的部门 特殊显示,且不可选
    $("#allRight").find(".fa-plus-square").each(function () {
        var departID = $(this).parent().attr("info") ;
        departID !== "all" ? departID = departID * 1:"";
        var isH_1 = (selectedDeparts.findIndex( value => value == departID ) == -1) ;
        var isH_2 = (thisDepar.indexOf( departID ) == -1) ;
        if(!(isH_1&&isH_2)){
            $(this).click() ;
        }
    }) ;
    var grayNum = 0 ;
    $("#nowRight").find(".fa-minus-square").each(function(){
        var departID = $(this).parent().attr("info") ;
        departID !== "all" ? departID = departID * 1:"";
        var isH_1 = (selectedDeparts.findIndex( value => value == departID ) != -1) ;
        var isN_2 = (thisDepar.indexOf( departID ) == -1) ;
        if(isH_1){
            $(this).addClass("deparSelected").addClass("ty-gray") ;
            grayNum ++ ;
        }
        if(isN_2){
            $(this).click();
        }
    });
    if(grayNum > 0){
        $(".plusAll").addClass("ty-gray") ;
    }
    if(selectedDeparts.length == 0 && (depars.length == thisDepar.length)){
        $(".minusAll").removeClass("ty-gray");
    }
}
// creator : 侯杏哲 2018-04-31 考勤设置 - 设置 - 选择考勤部门 - 确定
function selectDepartOk() {
    // 先获取其他部门设置的
    var selectDeparts = [] ;
    departSelectedDiv.parents(".busyTimeSetting").siblings("ul").each(function () {
        $(this).find(".departSelected").children(".departItem").each(function () {
            var info = $(this).attr("info") ; info = JSON.parse(info) ;
            selectDeparts.push(Number(info["id"]))
        }) ;
    }) ;
    var strDeparts = "" ;
    $("#nowRight").find(".fa-minus-square").each(function () {
        var isGray = $(this).hasClass("ty-gray") ;
        if(!isGray ){ // 选定的部门
            var departID = $(this).parent().attr("info") ;
            if(selectDeparts.indexOf(Number(departID)) == -1 && departID != "all"){ // 没有被其他规则选中
                var departName = $(this).siblings("span").html() ;
                var info = JSON.stringify({ "name" : departName , "id":departID }) ;
                strDeparts +=  ' <span class="departItem" info=\''+ info +'\'>'+ departName +'<i class="fa fa-close" ></i></span>';
            }
        }
    }) ;
    departSelectedDiv.html(strDeparts) ;
    bounce_Fixed2.cancel() ;
}
var setInfo = { "isDateSet": false , "isAllSet": false  } ; // 考勤设置信息：isAllSet - 是否全部部门设置了考勤 ，isDateSet - 是否设置作息日期
var setAttendanceSureType = 1; // 分情况：1. 修改考勤 ； 2. 去设置
var deleteAttendanceSetIds = [] ; // 删除的考勤规则
// creator：李玉婷 2018/2/26  考勤设置 - 设置 - 确定
function setAttendanceSure(){
    let html = ``;
    var isAllSet= setInfo["isAllSet"] ; // 标记是否所有部门都已经设置
    var isDateSet = setInfo["isDateSet"] ; // 标记是否设置作息日期
    var okDeparts = [] ; // 本次要设置的部门
    // 非空判断
    var isNull = chargeAttendance() ;
    if(isNull){   return false ; }
    let source = Number($("#initeSetting").data("source"));//source : 0 - 第一次设置 ； 1 - 考勤模式设置页去修改 ； 2 - 考勤详情页仍有机构未设置- 去设置 3-考勤详情页-去修改
    let curObj = ``;
    clearInterval(rolesTimer) ;
    $("#roles").find(".departSelected").each(function () {
        $(this).children().each(function () {
            var info =  $(this).attr("info") ;
            info = JSON.parse(info) ;
            okDeparts.push(Number(info["id"])) ;
        });
    });
    var loseDepars = [] ;
    var setedIDs = [] ;
    if (source === 0 || source === 2) {
        curObj = source === 0 ? $("#roleConList"): $("#roleCon");
        curObj.find("tbody tr:gt(0)").each(function (){
            let info = JSON.parse($(this).find(".hd").html());
            var departs = info["departmentIds"].split(',') ;
            for(var j = 0 ; j < departs.length ; j++){
                setedIDs.push( Number(departs[j]) ) ;
            }
        })
        loseDepars = loseDepaerts(setedIDs , okDeparts ) ;
        isAllSet = loseDepars.length >0 ? false : true ;
        if(!curObj.is(":visible")) {curObj.show();}
        if(isAllSet){
            if(source === 0) {
                setInfo["isAllSet"] = true;
                html = setDepartInfoHtml('str');
                curObj.find("tbody").append(html);
            } else {
                let data = setDepartInfoHtml('data');
                saveAnotherAttendance(data);
            }
        } else {
            // 提示是否继续设置其他部门
            $("#setAnotherTip").data("source", source);
            bounce_Fixed.show($("#setAnotherTip"));
        }
    } else {
        if (source === 3) {
            let data = setDepartInfoHtml('data');
            saveAttendance(data);
        } else {
            html = setDepartInfoHtml('str');
            updateBtnObj.parents("tr").replaceWith(html);
        }
    }
}
//creator:lyt 2024/1/16 暂不设置
function notSaveSetting(){
    let html = ``;
    let source = $("#setAnotherTip").data("source");
    bounce_Fixed.cancel();
    if(source === 0) {
        html = setDepartInfoHtml('str');
        $("#roleConList tbody").append(html);
    }else {
        let data = setDepartInfoHtml('data');
        saveAnotherAttendance(data);
    }
}
function setDepartInfoHtml(type){
    var roles = [];
    let html = ``;
    $("#roles").children(".busyTimeSetting").each(function () {
        var isSetNoon = $(this).find(".isSetNoon .fa-circle").attr("lang") === '1'? true: false;
        var beginTime = $(this).find(".workBeginTime").val();
        var endTime = $(this).find(".workEndTime").val();
        var breakBegin = $(this).find(".noonBeginTime").val();
        var breakEnd = $(this).find(".noonEndTime").val();
        var departmentIds = "", departArr = [];
        $(this).find(".departSelected").children().each(function () {
            var info = $(this).attr("info");
            info = JSON.parse(info);
            var id = info["id"];
            departmentIds += id + ",";
            departArr.push(info.name);
        });
        departmentIds = departmentIds.substr(0, (departmentIds.length - 1));
        var attendanceID = $(this).attr("atID");
        var role = {beginTime, endTime, breakBegin, breakEnd,departmentIds, "type": attendanceType, "isBreak": isSetNoon, "id": Number(attendanceID)};
        roles.push(role);
        html += `<tr>
                     <td>${departArr.join("，")}</td>
                     <td>${beginTime}-${endTime}</td>
                     <td>${breakBegin}-${breakEnd}</td>
                     <td>${isSetNoon ? '是': '否'}</td>
                     <td class="ty-td-control">
                     <span class="ty-color-blue" onclick="setInitOperate(1, $(this))">修改</span>
                     <span class="ty-color-red" onclick="deleteTimeRule($(this))">删除</span>
                     <span class="hd">${JSON.stringify(role)}</span>
                     </td>
                 </tr>`;
    });
    bounce_Fixed.cancel();
    if(type === 'str') {
        return html;
    } else {
        return roles;
    }
}
//  creator : 侯杏哲 2018-06-12  工具方法 - 保存考勤规则
function saveAttendance(roles) {
    var effectDate = ($("#editOprate").val());
    let icon = $(".cldType").html();
    if (Number(icon) === 2) {
        var inputTime = $("#inputTime").html();
        for (let i=0;i<roles.length;i++) {
            roles[i].inputTime = inputTime;
        }
    }
    var deleDepar = "";
    for (var q = 0; q < deleteAttendanceSetIds.length; q++) {
        deleDepar += deleteAttendanceSetIds[q] + ",";
    }
    deleDepar = deleDepar.substr(0, (deleDepar.length - 1));
    bounce.cancel();
    $.ajax({
        "url": "../workAttendance/updateAttendanceSetting.do",
        "data": {
            "personnelAttendanceConfigs": JSON.stringify(roles),
            "deleteAttendanceSetIds": deleDepar,
            "attendancePattern": icon, //考勤模式:1-考勤宝,2-手工录入(默认)
            "effectDate": effectDate,
            "patternType": false
        },
        success: function (res) {
            var status = res.data["status"];
            if (status == 1) {
                layer.msg("各部门考勤设置成功！");
                //  重载考勤规则
                attendSet();
            } else if (status == 2) { //
                layer.msg("修改考勤的启用时间不能早于明天，请重新设置");
            } else {
                layer.msg("各部门考勤设置失败！");
            }
        }

    })
}
// creator : 侯杏哲 2018-05-04 工具方法 - 考勤时间非空判断
function chargeAttendance() {
    var chargeNull = false ;
    $("#roles").children("ul").each(function(){
        var workBeginTime = $(this).find(".workBeginTime").val() ;
        var workEndTime = $(this).find(".workEndTime").val() ;
        var isSetNoon = $(this).find(".isSetNoon").attr("lang") ;
        var noonBeginTime = $(this).find(".noonBeginTime").val() ;
        var noonEndTime = $(this).find(".noonEndTime").val() ;
        var len = $(this).find(".departSelected").children().length ;
        if(workBeginTime == "" ){ layer.msg("请设置上班时间") ; chargeNull = true ; return false ; }
        if( workEndTime == ""){ layer.msg("请设置下班时间") ; chargeNull = true ; return false ; }
        if(isSetNoon == 1){
            if(noonBeginTime == "" ){ layer.msg("请设置午休开始时间") ; chargeNull = true ; return false ; }
            if(noonEndTime == ""){ layer.msg("请设置午休结束时间") ; chargeNull = true ; return false ;  }
        }
        if(len == 0){ layer.msg("请选择设置考勤的部门") ; chargeNull = true ; return false ; }

        // 判断填写的时间是否合法
        var workBegin = (new Date("2000-10-10 "+ workBeginTime)).valueOf();
        var workEnd = (new Date("2000-10-10 "+ workEndTime)).valueOf();
        var noonBegin = (new Date("2000-10-10 "+ noonBeginTime)).valueOf();
        var noonEnd = (new Date("2000-10-10 "+ noonEndTime)).valueOf();
        console.log(workBegin) ;
        console.log(workEnd) ;
        console.log(noonBegin) ;
        console.log(noonEnd) ;
        if(isSetNoon == 1){
            if(workBegin>=noonBegin){ layer.msg("上班时间 应小于 午休开始时间") ; chargeNull = true ; return false ; }
            if(noonBegin>=noonEnd){ layer.msg("午休开始时间 应小于 午休结束时间") ; chargeNull = true ; return false ; }
            if(noonEnd>=workEnd){ layer.msg("午休结束时间 应小于 下班时间") ; chargeNull = true ; return false ; }
        }else{
            if(workBegin>=workEnd){ layer.msg("上班时间 应小于 下班时间") ; chargeNull = true ; return false ; }
        }
    }) ;

    return chargeNull ;
}
// creator : 侯杏哲 2018-04-31 考勤设置 - 设置 - 去设置其他部门
function setAnother() {
    let source = $("#setAnotherTip").data("source");
    notSaveSetting();
    setInitOperate(source);
}
// creator : lyt 2023-11-11 考勤设置 - 作息时间设置 - 确定
function attendanceModeOk() {
    // var isDateSet = setInfo["isDateSet"] ; // 标记是否设置作息日期
    // 非空验证
    var url = ``;
    var pattern = $(".modeSelect .fa-circle").parent("span").data("icon") ;
    var roleStartDate = $("#roleStartDate").val() ;
    var attendanceTime = $("#attendanceTime").val() ;
    let editType = $("#attendanceNormalSetting").data("editType");
    if(!pattern){ layer.msg("请选择考勤数据进入系统的模式！"); return false ; }
    if(roleStartDate == ""){ layer.msg("请设置开始使用本系统记录考勤的日期！"); return false ; }
    if(pattern === 2 && attendanceTime == ""){ layer.msg("请设置每日考勤录入时间"); return false ; }
    if($("#roleConList .ty-color-blue").length <= 0){ layer.msg("请设置正常班的上下班时间"); return false ; }
    if($("#recSettings").html() == ""){ layer.msg("请设置本月的作息日期"); return false ; }
    if (pattern === 1) {
        if($("#lateLimit").val() == ""){ layer.msg("请录入上班时间到后几分钟的到岗算作迟到？"); return false ; }
        if($("#earlyLimit").val() == ""){ layer.msg("请录入下班时间前几分钟内的离岗算作早退？"); return false ; }
        if($("#attendanceNormalSetting .leaveCase .fa-circle").length <= 0){ layer.msg("请选择请假情况下的到岗离岗是否使用考勤宝考核？"); return false ; }
    }
    bounce.cancel();
    // 第一次设置
    // var roleStartDate = $("#roleStartDate").val(), attendanceTime = $("#attendanceTime").val();
    var roles = [];
    var openDate = $("#roleStartDate").val();
    var inputTime = $("#attendanceTime").val();
    $("#roleConList tr:gt(0)").each(function () {
        var info = $(this).find(".hd").html();
        info = JSON.parse(info);
        pattern === 2 ? info.inputTime = inputTime : "";
        roles.push(info);
    });
    let data = {
        "personnelAttendanceConfigs": JSON.stringify(roles),
        "attendancePattern": pattern, //考勤模式:1-考勤宝,2-手工录入(默认)
        "effectDate": openDate
    }
    if (pattern === 1) {
        data.lateLimit = $("#lateLimit").val();//迟到时限
        data.earlyLimit = $("#earlyLimit").val();//早退时限(分钟)
        data.leaveWork = $("#attendanceNormalSetting .leaveCase .fa-circle").data("icon");//请假到岗是否使用考勤宝:0-不使用(默认,false),1-使用(true)
    }
    if(editType === 'add') {
        url = "../workAttendance/attendanceTimeSetting.do";
    } else {
        data.patternType = true;
        url = "../workAttendance/updateAttendanceSetting.do";
    }
    $.ajax({
        "url": url,
        "data": data,
        success: function (res) {
            var status = res["data"]["status"];
            if (status == 1) {
                // var attendanceId = res["attendanceIds"] ;
                //layer.msg("各部门考勤设置成功！");
                let arr = [JSON.parse($("#recSettings").html())];
                let nextData = $("#nextSettings").html();
                if(nextData !== "") {
                    nextData = JSON.parse(nextData);
                    arr.push(nextData);
                }
                setDate(JSON.stringify(arr));
            } else if (status == 2) {
                layer.msg("系统启用时间不能早于明天！");
                loading.close();
            } else {
                layer.msg("各部门考勤设置失败！");
                loading.close();
            }
        },
        complete: function () {
        }
    })
}
/* creator : hxz 2018-05-21  工具方法 - 生成选择的部门的字符串 */
function deparStr(deparArr) {
    var str = "" ;
    if(deparArr && deparArr.length >0){
        for(var i = 0 ; i < deparArr.length ; i++){
            var departName = deparArr[i]["deptName"] ;
            var info = JSON.stringify({"name": departName ,"id":deparArr[i]["dept"]}) ;
            str +=  '<span class="departItem" info=\''+ info +'\'">'+ departName +'<i class="fa fa-close" ></i></span>';
        }
    }
    return str ;
}
// creator : 侯杏哲 2018-05-03 考勤设置 - 设置 - 保存作息时间
function setDate(dateStr, type) {
    // attendanceId（考勤设置id）、time 设置的时间{【考勤时间，1-假，2-班】假与班的时间均存}（二维数组）
    $.ajax({
        "url" : "../workAttendance/setTimeTable.do" ,
        "data" :{  "time": dateStr } ,
        success:function (res) {
            layer.msg("保存作息时间成功") ;
            bounce.cancel() ; bounce_Fixed.cancel() ;
            attendSet($("#attendSet") , 1);
        },
        complete:function () {
            
        }
    })
}
//creator:lyt 2024/2/21 修改作息时间
function updatePrevMonthDate(dateStr, source) {
    // attendanceId（考勤设置id）、time 设置的时间{【考勤时间，1-假，2-班】假与班的时间均存}（二维数组）
    $.ajax({
        "url" : "../workAttendance/updateTimeTable.do" ,
        "data" :{
            "type": source === 2 ? 1: "",
            "time": dateStr
        } ,
        success:function (res) {
            let status = res.data.status;
            if (status === 1) {
                layer.msg("保存作息时间成功") ;
                bounce.cancel() ; bounce_Fixed.cancel() ;
                attendSet($("#attendSet") , 1);
            } else {
                layer.msg(res.data.content) ;
            }

        }
    })
}
//  creator：李玉婷 2018/2/26  考勤设置 -  暂不设置 - 情况1. 显示作息日期设置弹框(第一次设置) ； 情况2. 直接保存
function setAttenceDate(){
    var isDateSet = setInfo["isDateSet"]; // 标记是否启用
    if (isDateSet) { // 已经启用 ，只是修改考勤规则
        bounce.cancel();
        bounce_Fixed.cancel();
        saveAttendance();
    } else { // 首次启用
        // 判断是否需要设置
        //$(".otherSetting").html("");
        var yearMonthArr = acecssDate["yearMonthArr"];
        var monthNav = "";
        if (yearMonthArr && yearMonthArr.length > 0) {
            for (var i = 0; i < yearMonthArr.length; i++) {
                if (i == 0) {
                    monthNav += '<li class="ty-active">' + yearMonthArr[i].substr(0, 4) + "年" + yearMonthArr[i].substr(4, 2) + "月" + '</li>';
                } else {
                    monthNav += '<li>' + yearMonthArr[i].substr(0, 4) + "年" + yearMonthArr[i].substr(4, 2) + "月" + '</li>';
                }

            }

        }
        $("#months").html(monthNav);
        datepicker.init($("#clendars"), yearMonthArr,"", false); // 设置日历
        bounce_Fixed.show($("#attenceOtherSetting"));

    }

}
// creator : 侯杏哲 2018-05-16 考勤设置 -
function updateSetTimeBtn(){
    var timeInput = $("#endInputTime").val() ;
    var attendanceIds = "" ;
    $("#roleCon tr:gt(0)").each(function () {
        var roleID = $(this).attr("info") ;
        attendanceIds += roleID +"," ;
    }) ;
    attendanceIds = attendanceIds.substr(0,attendanceIds.length-1) ;
    $.ajax({
        "url" :"../workAttendance/updateInputTime.do" ,
        "data" : { "attendanceIds" : attendanceIds , "inputTime": timeInput } ,
        success:function (res) {
            var status = res["status"] ;
            bounce.cancel() ;
            if(status == 1){
                layer.msg("设置成功") ; $("#inputTime").html(timeInput) ;$("#inputTime2").html(timeInput) ;
            }else{
                layer.msg("设置失败") ;
            }
        }
    })

}
/* creator : hxz 2018-04-27  选择部门的工具方法 */
function setStr( data , method ) { // data - 部门列表(树形) ， method - 加减
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var kidList = data[i]["subList"]; // 子部门列表
            if(kidList && kidList.length > 0){ // 遍历子级部门
                strAll += '<li>' +
                    '<div lass="departid" haveChild="1" info="'+ data[i]["id"]  +'">' +
                    '<i class="fa fa-angle-right"></i>' +
                    '<span>'+ data[i]["name"] +'</span> ' +
                    '<i class="fa fa-'+ method +'-square" onclick="'+ method +'($(this), event)"></i></div>' ;
                strAll += "<ul>" ;
                strAll += setStr( kidList , method  ) ;
                strAll += "</ul>" ;
            }else{
                strAll += '<li>' +
                    '<div lass="departid" haveChild="0" info="'+ data[i]["id"] +'">' +
                    '<i class="fa fa-angle-down"></i>' +
                    '<span>'+ data[i]["name"] +'</span> ' +
                    '<i class="fa fa-'+ method +'-square" onclick="'+ method +'($(this), event)"></i></div>' ;
            }
            strAll += "</li>" ;
        }
    }
    return strAll ;
}
/* creator：侯杏哲，2018-04-31  选择部门 - 减号 */
function minus( obj , event) {
    event.stopPropagation();
    $("#selectDepartOk").attr("class" ,"ty-btn ty-btn-blue ty-btn-big ty-circle-5").attr("onclick" , "selectDepartOk()") ;
    var kls = obj.hasClass("ty-gray") ;
    if( kls ){
        console.log("不生效!");
    }else{
        var pDivObj = obj.parent() ;
        var lass = pDivObj.attr("lass") , id = pDivObj.attr("info") ;
        var isE_DivObj = isExist( lass , id , $("#all") ) ;
        // 处理左边
        if(isE_DivObj){
            isE_DivObj.find("i.ty-gray").removeClass("ty-gray") ;
            var ulObj = isE_DivObj.next("ul");
            if(ulObj){ // 子级全部点亮
                ulObj.find("i.ty-gray").each(function () {
                    $(this).removeClass("ty-gray") ;
                })
            }
        }else {
            console.log("左面没找到!");
        }
        // 处理右边
        var pLiObj = pDivObj.parent() ;
        var siblingNum = 0 ;
        pLiObj.siblings().each(function(){
            var minusObj = $(this).find(".fa-minus-square") ;
            var isSh = minusObj.hasClass("ty-gray") ;
            if(!isSh){ siblingNum ++ ; }
        }) ;
        var ulID = pLiObj.parent().attr("id") ;
        if(ulID == "nowRight"){ // 是第一级的
            pLiObj.remove() ;
        }else{ //  不是第一级的
            obj.addClass("ty-gray") ;
            var sUlObj = pDivObj.next("ul") ;

        }
        /* 补充显示 */
        chargeBoth( $("#allRight") , $("#nowRight") );
    }

}
/* creator：侯杏哲，2018-04-31  选择部门 - 加号 */
function plus( obj , event) {
    event.stopPropagation() ;
    $("#selectDepartOk").attr("class" ,"ty-btn ty-btn-blue ty-btn-big ty-circle-5").attr("onclick" , "selectDepartOk()") ;
    var kls = obj.hasClass("ty-gray") ;
    if( kls ){
        console.log("不生效!");
    }else{
        var lass = obj.parent().attr("lass") , id = obj.parent().attr("info") ;
        var isE = isExist(lass , id , $("#nowRight")) ;
        if(isE){ // 右边已存在
            isE.children("i.fa-minus-square").removeClass("ty-gray") ; // 右边点亮
            var nUlObj = isE.next("ul") ;
            if(nUlObj){ nUlObj.find(".fa-minus-square").each(function(){ $(this).removeClass("ty-gray") ;  } ) }
            obj.addClass("ty-gray") ; // 左边熄灭
            var lUlObj = obj.parent().next("ul") ;
            if(lUlObj){ lUlObj.find(".fa-plus-square").each(function(){ $(this).addClass("ty-gray") ;  } ) }
        }else{
            // 以下是右边需要新增的情况
            var len = obj.parents("li").length ;
            if( len > 1){ // 不是一级文件夹
                var pDivObj = obj.parent("div") ;
                var pLiObj = pDivObj.parent(); // 点击的哪一级全部li
                var p2UlObj = pLiObj.parent("ul") ; // 点击的包含这一级的ul
                var kDivStr = pLiObj.html() ;
                pLiObj.html("REPLACE") ;
                var firstLiObj = p2UlObj.parents("li:last") ;
                var str = "<li>" + firstLiObj.html() + "</li>";
                var minusStr1 = str.replace( /fa-plus-square/g , "fa-minus-square ty-gray") ; // 上级的减号不能点
                minusStr1 = minusStr1.replace( /plus/g , "minus") ; // 将上级的方法替换
                var kidStr = kDivStr.replace( /plus/g , "minus") ; // 子级的加号替换为减号
                var minusStr = minusStr1.replace("REPLACE" , kidStr ) ; // 组成用来替换的字符串
                $("#nowRight").append( minusStr ) ;
                pLiObj.html(kDivStr) ;
                pLiObj.children().find("i.fa-plus-square").addClass("ty-gray") ;
            }else{ // 一级文件夹
                var liObj = obj.parents("li:last") ;
                var str = liObj.html() ;
                str = "<li>" + str + "</li>" ;
                var minusStr = str.replace( /plus/g , "minus") ;
                $("#nowRight").append( minusStr ) ;
                liObj.find("i.fa-plus-square").addClass("ty-gray") ;
            }
        }
        /* 补充显示 */
        chargeBoth( $("#allRight") , $("#nowRight") );
    }
    $(".plusAll").attr("class" , "fa fa-plus-square plusAll ty-gray") ;

}
// creator : hxz 2018-08-30
function editTimeBtn() {
    var inputTime = $.trim( $("#inputTime").html() ) ;
    $("#endInputTime").children("option").each(function () {
        var val = $(this).val();
        if(val == inputTime){
            $(this).attr("selected" , "selected") ;
        }
    }) ;
    bounce.show($('#editTime')) ;
}
/* creator：侯杏哲 2017-12-07 判断ulObj是否有选中的id , 有的话返回该div 对象 */
function isExist( lass , id , ulObj ) {
    var isE = false ;
    ulObj.children("li").each(function(){
        var _thisDiv = $(this).children("div") ;
        var _lass = _thisDiv.attr("lass") ;
        var _id = _thisDiv.attr("info") ;
        if(_lass == lass && _id == id){ // 右边有这个部门
            isE = _thisDiv ; return _thisDiv ;
        }
        if(!isE){
            var kUlObj = $(this).children("ul") ;
            if(kUlObj){ isE = isExist( lass , id , kUlObj ) }
        }
    }) ;
    return isE ;
}
/* creator: 侯杏哲 2017-12-06 工具方法 对比左右的字符串 */
function chargeBoth(leftObj , rightObj) {
    var infoArr =   rightObj && rightObj.find(".fa-info") ;
    if(rightObj && infoArr.length >0){
        infoArr.each(function(){
            var _thisInfo = $(this) ;
            var userID = _thisInfo.parent().attr("info") ;
            var kls = _thisInfo.siblings(".fa-minus-square").hasClass("ty-gray") ; // 当前的这个是不是灰色的
            if(!kls){ // 当前是高亮的
                var isAllAct = true ; // 默认所有都是高亮
                var sib = _thisInfo.parent().parent().siblings("li") ;
                if(sib.length > 0){
                    sib.each(function(){
                        var isAct = $(this).children("div").children(".fa-minus-square").hasClass("ty-gray") ;
                        if( isAct ){ // 不是高亮的
                            isAllAct = false ;
                        }
                    })
                }
                if(isAllAct){ // 符合要求，父级设置为高亮
                    var pDiv = _thisInfo.parent("div").parent("li").parent("ul").siblings("div") ;
                    var lass = pDiv.attr("lass") ;
                    var deparID = pDiv.attr("info") ;
                    pDiv.children(".fa-minus-square").removeClass("ty-gray") ;
                    var isE_DivObj = isExist( lass , deparID , $("#all") ) ;
                    if(isE_DivObj){ isE_DivObj.children(".fa-plus-square").addClass("ty-gray") ;  }
                }
            }
        }) ;
        var bigAct = true ;
        rightObj.children("li:first").children("ul").children("li").each(function(){
            var isA = $(this).children("div").children(".fa-minus-square").hasClass("ty-gray") ;
            if(isA){ bigAct = false ;  }
        });
        if(bigAct){
            rightObj.children("li:first").children("div").children(".fa-minus-square")
        }
    }

}
/* updator：侯杏哲 2018/2/26  考勤设置 - 设置 */
var acecssDate = { } ; // 标识可以设置的时间
var rolesTimer = "" , updateBtnObj = ""; // 监听考勤规则确认按钮是否可点击的定时器
function setInitOperate(type, obj) { // type : 0 - 第一次设置 ； 1 - 考勤模式设置页去修改 ； 2 - 考勤详情页仍有机构未设置- 去设置 3-考勤详情页-去修改
    // select2Init($("#initeSetting .bonceCon ul:eq(0)")) ; // 初始化时间选择控件'
    updateBtnObj = obj;
    bounce_Fixed.show($("#initeSetting"));
    // 获取可以设置的考勤时间
    /*$.ajax({
        "url" : "../workAttendance/getTomorrowAndMonth.do" ,
        success:function (res) {
            var nextLastDay = new Date(res["nextLastDay"]).format('yyyy-MM-dd') ;
            var tomorrow = new Date(res["tomorrow"]).format('yyyy-MM-dd') ;
            var month_1 = tomorrow.split("-")[1] ;
            var day_1 = tomorrow.split("-")[2] ;
            acecssDate["startDay"] = day_1 ;
            acecssDate["startMonth"] = month_1 ;
            acecssDate["tomorrow"] = tomorrow ;
            acecssDate["nextMonth"] = nextLastDay ;
            var yaerMonth_1 = tomorrow.replace("-","").substr(0 , 6) ;
            var yaerMonth_2 = nextLastDay.replace("-","").substr(0 , 6) ;
            acecssDate["yearMonthArr"] = [yaerMonth_1 , yaerMonth_2] ;
        }
    });*/
    var setOpera = "" ,strDeparts = "";
    let workBeginTime = "08:00";
    let workEndTime = "17:00";
    let noonBeginTime = "12:00";
    let noonEndTime = "13:00";
    let atID = ``;
    $("#roles .isSetNoon .fa").attr("class","fa fa-circle-o") ;
    if (type == 1 || type == 3) { // 去修改 2= 去设置
        var roleInfo = obj.siblings(".hd").html() ;
        roleInfo = JSON.parse(roleInfo) ;
        let icon = roleInfo.isBreak ? 0: 1;
        workBeginTime = roleInfo.beginTime;
        workEndTime = roleInfo.endTime;
        noonBeginTime = roleInfo.breakBegin;
        noonEndTime = roleInfo.breakEnd;
        if (type == 3) {
            //deleteAttendanceSetIds = [] ; // 初始化删除的考勤规则列表
            $("#updateAT").show();
            atID = roleInfo.id;
        }
        let depars = roleInfo.departmentIds.split(",");
        let tree = {
            "id": '',
            "subList": allDepartsTree
        }
        for(let i=0;i<depars.length;i++){
            let pItem = ""
            pItem = preOrder(tree, depars[i])
            var pInfo = JSON.stringify({ "name" : pItem.name , "id": pItem.id }) ;
            strDeparts +=  ' <span class="departItem" info=\''+ pInfo +'\'>'+ pItem.name +'<i class="fa fa-close" ></i></span>';
        }
        $("#roles .isSetNoon .fa").eq(icon).click() ;
    }else{
        $("#roles .isSetNoon span").eq(1).attr("class","fa fa-circle") ;
        if (type == 2) {
            $("#updateAT").show();
        } else {
            $("#updateAT").hide();
        }
    }
    $("#roles .busyTimeSetting").attr("atID", atID);
    $("#roles .workBeginTime").html(timeOptionStr(workBeginTime , 0 , 48));
    $("#roles .workEndTime").html(timeOptionStr(workEndTime , 0 , 48));
    $("#roles .noonBeginTime").html(timeOptionStr(noonBeginTime , 0 , 48));
    $("#roles .noonEndTime").html(timeOptionStr(noonEndTime , 0 , 48));
    $("#roles .departSelected").html(strDeparts);
    $("#initeSetting").data("source", type);
    rolesTimer = setInterval(function(){ setOkAtive() } , 300)
}
function preOrder (node, id) {
    if (node.id !=="" && Number(node.id) === Number(id)) {
        return node
    } else if (node.subList.length <= 0) {
        return ''
    } else {
        for(let i=0;i<node.subList.length;i++) {
            let result = preOrder(node.subList[i], id);
            if (result.name) {
                return result;
            }
        }
    }
}
// creator: 侯杏哲 2018-04-27  工具方法 - 勤规则确认按钮是否可点击
function setOkAtive() {
    var isOk = chargeOkAtive() ;
    if(isOk){
        $("#attendanceSure").attr("class" , "ty-btn ty-btn-blue ty-btn-big ty-circle-5").attr("onclick" , "setAttendanceSure()") ;
    }else{
        $("#attendanceSure").attr("class" , "ty-btn ty-btn-gray ty-btn-big ty-circle-5").removeAttr("onclick") ;
    }
}
function chargeOkAtive() {
    var isok = true ;
    $("#roles").children(".busyTimeSetting").each(function () {
        var workBeginTime = $(this).find(".workBeginTime").val() ;
        var workEndTime = $(this).find(".workEndTime").val() ;
        var isSetNoon = $(this).find(".isSetNoon .fa-circle").length ;
        var departs = $(this).find(".departSelected").children(".departItem").length ;
        let hasBreak = isSetNoon > 0 && $(this).find(".isSetNoon .fa-circle").attr("lang") === 1 && ($(this).find(".noonBeginTime").val() !== "" || $(this).find(".noonEndTime").val() !== "");
        let effect = $("#editOprate").val();
        if(workBeginTime === "" || workEndTime === "" || isSetNoon <= 0 || departs==0 || hasBreak || $("#editOprate").is(":visible") && effect === ""){
            isok = false ;
        }
    }) ;
    return isok ;
}

// creator: 侯杏哲 2018-04-27  工具方法 - 获取考勤时间规则俩表
var setedDeparts = [] ; // 统计已经设置了考勤的部门
Update_openDate = ""; // 记录系统启用时间
function getAccountSet(type) { // type只有第一次考勤设置完成的时候会有
    $.ajax({
        url: "../workAttendance/getAttendanceTime.do",
        data: { "type": attendanceType },
        success: function (res) {
            let data = res.data;
            setedDeparts = [] ;
            var exceptionType = data["exceptionType"] ; // 1-已设置了作息时间 0-未设置作息时间
            var status = data["status"] ; // 1-有未设置考勤的部门  0-没有未设置考勤的部门
            if(status == 1){  $(".otherDepar").show();   }else{  $(".otherDepar").hide();  }
            setInfo = { "isDateSet": Boolean(exceptionType)  , "isAllSet": Boolean(status)  } ;
            var dayRoles = data["personnelAttendanceConfigs"] ;
            if (!exceptionType || exceptionType == 0) {
                $(".attendanceSet .noSet").show().siblings().hide() ;
            }else{
                let str = `` ;
                var startUsingSystemTime = data["startUsingSystemTime"]; // 开始启用系统时间
                $("#roleCon tbody tr:gt(0)").remove();
                if( dayRoles && dayRoles.length > 0){
                    for(var j = 0 ; j < dayRoles.length ; j++){
                        if(j == 0){
                            setInfo.cofId = dayRoles[j].id;
                            Update_openDate = dayRoles[j]["openDate"];
                            var openDate = new Date(dayRoles[j]["openDate"]).format('yyyy年MM月dd日');
                            var inputTime = dayRoles[j]["inputTimeString"] ;
                            $("#openDate").html(openDate) ;
                            $("#inputTime").html(inputTime) ;
                            $(".cldType").html(dayRoles[j].attendancePattern); //1-考勤宝,2-手工录入(默认)
                            $("#modeStr").html(dayRoles[j].attendancePattern === 1?"考勤宝":"手动录入");
                            $(".modeCreate").html(dayRoles[j].createName + ' &nbsp; ' + new Date(dayRoles[j].createDate).format('yyyy-MM-dd hh:mm:ss'));
                            if(dayRoles[j].attendancePattern === 1){
                                $(".onEquip").show();$(".onManual").hide();
                                $("#leaveLimit").html(JSON.stringify(dayRoles[j]));
                                $(".lateLimitInfo").html(dayRoles[j].lateLimit);
                                $(".earlyLimitInfo").html(dayRoles[j].earlyLimit);
                                dayRoles[j].leaveWork ? $(".isLeaveWork").show().siblings().hide():$(".isLeaveWork").hide().siblings().show();
                            } else {
                                $(".onEquip").hide();$(".onManual").show();
                            }
                        }
                        var depars = dayRoles[j]["personnelAttendanceDepartmentConfigHashSet"] ;
                        var departArr = [] , departsID = [] ;
                        if(depars && depars.length > 0){
                            for(var i = 0 ; i < depars.length ; i++){
                                departArr.push('<span info=\''+ JSON.stringify(depars[i]) +'\'>'+ depars[i]["deptName"] +'</span>') ;
                                departsID.push(depars[i]["dept"]) ;
                            }
                        }
                        var dayRole = dayRoles[j] ;
                        dayRole["depars"] = depars ;
                        dayRole["beginTime"] = dayRole.beginTimeString ;
                        dayRole["endTime"] = dayRole.endTimeString ;
                        dayRole["breakBegin"] = dayRole.breakBeginString ;
                        dayRole["breakEnd"] = dayRole.breakEndString ;
                        dayRole["isBreak"] = dayRole.break ;
                        str += `<tr info="${dayRole["id"]}">
                                <td>${departArr.join(",")}</td>
                                <td>${dayRole["beginTimeString"]}-${dayRole["endTimeString"]}</td>
                                <td>${dayRole["breakBeginString"]}-${dayRole["breakEndString"]}</td>
                                <td>${dayRole.middleBreak ? "是" : "否"}</td>
                                <td>${new Date(dayRole.openDate).format('yyyy-MM-dd')}</td>
                                <td class="ty-td-control">
                                <span class="ty-color-blue" onclick="setInitOperate(3, $(this))">修改</span>
                                <span class="ty-color-gray">删除</span>
                                <span class="hd">${JSON.stringify(dayRole)}</span>
                                </td>
                            </tr>`;
                        setedDeparts.push({"atID":dayRole["id"] , "departs": departsID })
                    }
                }
                $("#roleCon tbody").append(str);
                $(".tabCard").show().siblings().hide() ;
                getTimeState();
                $(".attendanceSet .noSet").hide().siblings().show() ;
                $(".viewContent").hide();
                $(".arrowBtn").children("span").html("展开").siblings(".fa").addClass("fa-angle-down").removeClass("fa-angle-up");
            }
        }
    })
}
//creator:lyt 2023/11/6 获取作息日期状态
function getTimeState(){
    $.ajax({
        "url" :"../workAttendance/getTimeState.do" ,
        "data" : {} ,
        success:function (res) { //1-已设置了作息时间 0-未设置
            let data = res.data;
            $("#lastMonthState").data("state", data.lastMonthState).html(data.lastMonthState === 1 ? "已":"未");
            $("#thisMonthState").data("state", data.thisMonthState).html(data.thisMonthState === 1 ? "已":"未");
            $("#nextMonthState").data("state", data.nextMonthState).html(data.nextMonthState === 1 ? "已":"未");
        }
    })
}
// creator: 侯杏哲 2018-04-27  删除考勤规则
function deleteRole(obj) {
    var info = JSON.parse(obj.siblings(".hd").html()) ;
    var attendanceId = info.id;
    var attendanceIds =  [ attendanceId ] ;
    $.ajax({
        "url" :"../workAttendance/deleteSetAttendance.do" ,
        "data" : { "attendanceIds":attendanceId  } ,
        success:function (res) {
            obj.parents("tr").remove() ;
            layer.msg("删除成功") ;
        }
    })

}
function deleteTimeRule(obj) {
    obj.parents("tr").remove() ;
}
// creator: 侯杏哲 2018-04-27  设置是否显示午休时间的下拉框
function setNoonToggle( obj ) {
    var lang = obj.attr("lang") ;
    if (obj.hasClass("fa-circle-o")){
        obj.attr("class" , "fa fa-circle").siblings().attr("class" , "fa fa-circle-o") ;
        if(lang == 1){
            $(".isNeed").show();
        }else{
            $(".isNeed").hide();
        }
    } else {
        $(".isNeed").hide();
        obj.attr("class" , "fa fa-circle-o") ;
    }
}

// creator: 侯杏哲 2018-05-02  获取没有设置考勤的部门
function loseDepaerts( departs1 , departs2 ) { // departs1 已经设置了考勤的 departs2 将要设置考勤的
    var loseArr = [] ;
    for(var i = 0 ; i < allDeparts.length ; i++){
        var id = Number( allDeparts[i] ) ;
        var id_1 = ( departs1.indexOf(id) == -1 ) ;
        var id_2 = ( departs2.indexOf(id) == -1 ) ;
        if(id_1 && id_2){ // 该部门没在选中的部门里面
            loseArr.push(id) ;
        }
    }
    return loseArr ;
}
// creator: 侯杏哲 2018-05-02 工具方法 - 获取所有部门
function getAllDepr( arr ){
    for(var i = 0 ; i < arr.length ; i++ ){
        allDeparts.push( arr[i]["id"] ) ;
        allDepartsList.push( {"name":arr[i]["name"],"id":arr[i]["id"] }) ;
        var sub = arr[i]["subList"] ;
        if(sub && sub.length > 0){
            getAllDepr( sub ) ;
        }
    }
}
// creator: 侯杏哲 2018-05-02 工具方法 - 获取所有部门
function setOperaStr(roleArr, type) { // roleArr：考勤规则列表 ， type ：1 - 去修改（允许修改）， 2 - 去设置（不允许修改）
    var str = "" ;
    if(roleArr && roleArr.length >0){
        for(var i = 0 ; i < roleArr.length ; i++){
            var beginTime = roleArr[i]["beginTime"].substr(11,5) ;
            var endTime = roleArr[i]["endTime"].substr(11,5) ;
            var breakBegin = roleArr[i]["breakBegin"].substr(11,5) ;
            var breakEnd = roleArr[i]["breakEnd"].substr(11,5) ;
            var depars = roleArr[i]["depars"] ;
            var atID = roleArr[i]["id"] ;
            var disbleStr = "";
            if (type == 2) { // 去设置（不允许修改）
                disbleStr = "<div class='mask'></div>";
            }
            if (i === 0) {
                var delRoleStr = ""
            } else {
                var delRoleStr = "<span class=' deleRole' onclick='deleRole($(this))'><i class='fa fa-trash'></i></span>"
            }
            str += `
            <div class="clear clearfix busyTimeSetting" atID=''>
                    <div class="dutyItem">
                        <div class="dutyItemTitle">应上班时间 <i class="xing"></i></div>
                        <select class="workBeginTime">${timeOptionStr(beginTime , 0 , 48)}</select>
                    </div>
                    <div class="dutyItem">
                        <div class="dutyItemTitle">应下班时间 <i class="xing"></i></div>
                        <select class="workEndTime">${timeOptionStr(endTime , 0 , 48)}</select>
                    </div>
                    <div class="dutyItem dutyItemLong">
                        <div class="dutyItemTitle ty-left">午休前后是否考勤 <i class="xing"></i></div>
                        <div class="dutyItemTitle ty-right isSetNoon">
                            <span class="fa fa-circle-o" lang="1" onclick="setNoonToggle($(this))"></span>是
                            <span class="fa fa-circle" lang="0" onclick="setNoonToggle($(this))"></span>否
                        </div>
                    </div>
                    <div class="dutyItem dutyItemLong noonTime">
                        <div class="dutyItemTitle">午休时间<i class="xing isNeed"></i></div>
                        <select class="noonBeginTime">${timeOptionStr(breakBegin , 20 , 31)}</select>
                        <select class="noonEndTime ty-right">${timeOptionStr(breakEnd , 20 , 31)}</select>
                    </div>
                    <div class="dutyItem dutyItemLong">
                        <div class="dutyItemTitle">适用部门 <i class="xing"></i></div>
                        <div class="department overflow">
                            <div class="departSelected" id="departSelected"> ${deparStr( depars )}</div>
                            <div class="addDepart" onclick="addDepart($(this))"><i class="fa fa-bars"></i> </div>
                        </div>
                    </div>
                    <div class="dutyItem dutyItemLong" id="updateAT">
                        <div class="dutyItemTitle">本次设置的生效日期 <i class="xing"></i></div>
                        <input type="text" id="editOprate"/>
                    </div>
                </div>`;
        }
        if (type == 2) { // 去设置， 多一条空白规则
            str += setOperaStr([]);
        }
    }else{
        str += `
            <div class="clear clearfix busyTimeSetting" atID=''>
                    <div class="dutyItem">
                        <div class="dutyItemTitle">应上班时间 <i class="xing"></i></div>
                        <select class="workBeginTime">${timeOptionStr("08:00" , 0 , 48)}</select>
                    </div>
                    <div class="dutyItem">
                        <div class="dutyItemTitle">应下班时间 <i class="xing"></i></div>
                        <select class="workEndTime">${timeOptionStr("17:00" , 0 , 48)}</select>
                    </div>
                    <div class="dutyItem dutyItemLong">
                        <div class="dutyItemTitle ty-left">午休前后是否考勤 <i class="xing"></i></div>
                        <div class="dutyItemTitle ty-right isSetNoon">
                            <span class="fa fa-circle-o" lang="1" onclick="setNoonToggle($(this))"></span>是
                            <span class="fa fa-circle" lang="0" onclick="setNoonToggle($(this))"></span>否
                        </div>
                    </div>
                    <div class="dutyItem dutyItemLong noonTime">
                        <div class="dutyItemTitle">午休时间<i class="xing isNeed"></i></div>
                        <select class="noonBeginTime">${timeOptionStr("12:00" , 20 , 31)}</select>
                        <select class="noonEndTime ty-right">${timeOptionStr("13:00" , 20 , 31)}</select>
                    </div>
                    <div class="dutyItem dutyItemLong">
                        <div class="dutyItemTitle">适用部门 <i class="xing"></i></div>
                        <div class="department overflow">
                            <div class="departSelected" id="departSelected"> ${deparStr( depars )}</div>
                            <div class="addDepart" onclick="addDepart($(this))"><i class="fa fa-bars"></i> </div>
                        </div>
                    </div>
                    <div class="dutyItem dutyItemLong" id="updateAT">
                        <div class="dutyItemTitle">本次设置的生效日期 <i class="xing"></i></div>
                        <input type="text" id="editOprate"/>
                    </div>
                </div>`;
    }
    return str ;
}
// creator: 侯杏哲 2018-05-17 工具方法 - 获取 select中的 option 值
function timeOptionStr(selectedVal , startNum , endNum ) { // selectedVal - 默认值 ： 08:30 这种
    var timeArr = [] ;
    for(var i = startNum ; i < endNum ; i++){
        var n = parseInt(i/2) , q = i%2 ;
        var h = n ; if(h <10){ h = "0" + n ; }
        var m = "30" ; if(q == 0){ m = "00" ; }
        var tim = h + ":" + m ;
        timeArr.push(tim) ;
    }
    var str = "" ;
    for(var j = 0 ; j < timeArr.length ; j++){
        var t =  timeArr[j] ;
        var timStr = "<option  value='"+ timeArr[j] +"'>"+ timeArr[j]  +"</option>";
        if(selectedVal == timeArr[j] ){
            timStr = "<option selected='selected' value='"+ timeArr[j] +"'>"+ timeArr[j]  +"</option>";
        }
        str += timStr ;
    }
    return str ;
}
// creator : hxz 2018-06-12 删除考勤部门
function deleRole(obj) {
    var deparID = obj.parent("ul").attr("atid");
    if(deparID){
        deleteAttendanceSetIds.push(deparID) ;
    }
    obj.parent("ul").remove() ;
}



// creator : hxz 2018-06-13 去设置-保存
function saveAnotherAttendance(roles) {
    var effectDate = $.trim($("#editOprate").val());
    if (!effectDate) {
        layer.msg("请输入生效时间");
        return false;
    }
    let icon = $(".cldType").html();
    if (Number(icon) === 2) {
        var inputTime = $("#inputTime").html();
        for (let i=0;i<roles.length;i++) {
            roles[i].inputTime = inputTime;
        }
    }
    bounce.cancel();
    $.ajax({
        "url": "../workAttendance/attendanceTimeSetting.do",
        "data": {
            "personnelAttendanceConfigs": JSON.stringify(roles),
            "effectDate": effectDate,
            "attendancePattern": icon //考勤模式:1-考勤宝,2-手工录入(默认)
        },
        success: function (res) {
            var status = res["data"]["status"];
            if (status == 1) {
                attendSet();
                layer.msg("各部门考勤设置成功！");

            } else {
                layer.msg("各部门考勤设置失败！");
            }
        }
    })
}
//creator:lyt 2023/11/9 模式更换记录
function modeChangeRecord(){
    $("#modeRecordList tbody tr:gt(0)").remove() ;
    bounce_Fixed.show($("#modeChangeRecord"))
    $.ajax({
        "url" : "../workAttendance/modelRecord.do" ,
        success:function (res) {
            var list = res["data"]["modelRecord"];
            let str = `` ;
            if(list && list.length >0){
                for(var i = 0 ; i < list.length ; i++ ){
                    str += `<tr info="${list[i]["id"]}">
                                <td>${list[i].patternName}</td>
                                <td>${new Date(list[i].updateDate).format('yyyy-MM-dd hh:mm:ss')}</td>
                                <td>${list[i].updateName}</td>
                                <td>${new Date(list[i].effectDate).format('yyyy-MM-dd')}</td>
                                <td class="ty-td-control">
                                <span class="ty-color-blue" onclick="modelRecordDetail(${list[i].id},$(this))">查看</span>
                                <span class="hd">${JSON.stringify(list[i])}</span>
                                </td>
                            </tr>`;
                }
            }else{
                str += "<tr>" +
                    "<td colspan='5'>暂无数据</td>" +
                    "</tr>" ;
            }
            $("#modeRecordList tbody").append(str) ;
        }
    })
}
function modelRecordDetail(id, obj){
    let html = ``, str = `` ;
    let info = JSON.parse(obj.siblings(".hd").html());
    if (info.patternName === '考勤宝') {
        $(".byDevicesInfo").show();
    } else if (info.patternName === '手动录入'){
        $(".byDevicesInfo").hide();
    }
    $("#restSetList tbody tr:gt(0)").remove();
    $("#devicesList tbody tr:gt(0)").remove();
    bounce_Fixed2.show($("#modeChangeRecordDetails"));
    $.ajax({
        "url" :"../workAttendance/modelRecordDetail.do" ,
        "data" : { "historyId": id  } ,
        success:function (res) {
            let {modelRecordDetail, iotTerminals} = res.data;
            if (modelRecordDetail && modelRecordDetail.length > 0) {
                for (let i=0; i<modelRecordDetail.length;i++) {
                    if (i===0 && info.patternName === '考勤宝') {
                        $(".rd_earlyLimit").html(modelRecordDetail[i].earlyLimit);
                        $(".rd_lateLimit").html(modelRecordDetail[i].lateLimit);
                    }
                    html += `<tr>
                             <td>${modelRecordDetail[i].deptNames}</td>
                             <td>${modelRecordDetail[i]["beginTime"]}-${modelRecordDetail[i]["endTime"]}</td>
                             <td>${modelRecordDetail[i]["breakBegin"]}-${modelRecordDetail[i]["breakEnd"]}</td>
                             <td>${modelRecordDetail[i].break ? "是" : "否"}</td>
                         </tr>`;
                }
            }else {
                html += "<tr>" +
                    "<td colspan='4'>暂无数据</td>" +
                    "</tr>" ;
            }
            if (iotTerminals && iotTerminals.length > 0) {
                for (let i=0; i<iotTerminals.length;i++) {
                    str += `<tr>
                             <td>${iotTerminals[i].sn}</td>
                             <td>${iotTerminals[i]["brand"]}/${iotTerminals[i]["model"]}</td>
                             <td>${iotTerminals[i]["deviceUuid"]}</td>
                             <td>${iotTerminals[i].enabled ? "是" : "否"}</td>
                         </tr>`;
                }
            }else {
                str += "<tr>" +
                    "<td colspan='4'>暂无数据</td>" +
                    "</tr>" ;
            }
            $("#restSetList tbody").append(html);
            $("#devicesList tbody").append(str);
        }
})
}
function equipScan(enabled){ //1-有效,0-无效
    let html = ``;
    $(".mainCon2").show().siblings().hide();
    $("#equipList" + enabled).find("tbody tr:gt(0)").remove();
    $.ajax({
        "url" :"../iot/attendanceDevices.do" ,
        "data" : { "enabled":enabled  } ,
        success:function (res) {
            let list = res.data.iotTerminals;
            let holdNum = 1^ enabled;
            for(var i = 0 ; i < list.length ; i++){
                html += `<tr info="${list[i]["id"]}">
                                <td>${list[i].sn}</td>
                                <td>${list[i].brand}/${list[i].model}</td>
                                <td>${list[i].deviceUuid}</td>
                                <td>${list[i].createName} ${new Date(list[i].createTime).format('yyyy-MM-dd hh:mm:ss')}</td>
                                <td class="ty-td-control">
                                <span class="ty-color-blue" onclick="holdIotTerminals(${holdNum}, $(this))">${enabled === 1?'停用':'启用'}</span>
                                <span class="ty-color-gray">停用/启用记录</span>
                                <span class="hd">${JSON.stringify(list[i])}</span>
                                </td>
                            </tr>`;
            }
            $("#equipList" + enabled).children("tbody").append(html);
        }
    })
    $(".equipCon" + enabled).show().siblings("div").hide();
}
function holdIotTerminals(enabled, obj){
    let info = JSON.parse(obj.siblings(".hd").html());
    let deviceUuid = info.deviceUuid;
    let data = {enabled, deviceUuid};
    let str = enabled === 1?'启用':'停用';
    $("#operMsg").html(str);
    $(".epId").html(JSON.stringify(data));
    bounce.show($("#iotTip"));
}
function iotTerminalsSetSure(){ //停用、启用设备，是否有效,1-有效,0-无效
    let data = JSON.parse($(".epId").html());
    $.ajax({
         "url" :"../iot/updateTerminalState.do" ,
         "data" : data ,
         success:function (res) {
             let key = data.enabled^1;
             equipScan(key);
             bounce.cancel();
         }
     })
}
function updateLimit(){
    $("#updateLimit input").val("");
    $("#updateLimit .leaveCase .fa").attr("class", "fa fa-circle-o");
    let data = $("#leaveLimit").html();
    if (data !== "") {
        data = JSON.parse(data);
        let num = data.leaveWork ? 0: 1;
        $("#lateLimit_up").val(data.lateLimit);
        $("#earlyLimit_up").val(data.earlyLimit);
        $("#updateLimit .leaveCase .fa").eq(num).click();
    }
    bounce.show($("#updateLimit"));
}
// creator : lyt 2023-11-16 修改 - 作息时间设置 - 确定
function updateLimitOk() {
    // 非空验证
    if($("#lateLimit_up").val() == ""){ layer.msg("请录入上班时间到后几分钟的到岗算作迟到？"); return false ; }
    if($("#earlyLimit_up").val() == ""){ layer.msg("请录入下班时间前几分钟内的离岗算作早退？"); return false ; }
    if($("#updateLimit .leaveCase .fa-circle").length <= 0){ layer.msg("请选择请假情况下的到岗离岗是否使用考勤宝考核？"); return false ; }
    bounce.cancel();
    let data = {
        "lateLimit": $("#lateLimit_up").val(),
        "earlyLimit": $("#earlyLimit_up").val(), //考勤模式:1-考勤宝,2-手工录入(默认)
        "leaveWork": $("#updateLimit .leaveCase .fa-circle").data("icon"),
        "configId": setInfo.cofId
    }
    $.ajax({
        "url": '../workAttendance/updateLimit.do',
        "data": data,
        success: function (res) {
        }
    })
}
laydate.render({elem: '#roleStartDate'});
// laydate.render({elem: '#attendanceTime'});
laydate.render({elem: '#editOprate'});










