$(function(){
    //  creater 姚宗涛  2018/2/6  09:46:03  选项卡效果
    $(".ty-secondTab li").click(function () {
        var index = $(this).index();
        $(this).addClass("ty-active").siblings().removeClass("ty-active");
        $(".ty-mainData .ty-table").eq(index).show().siblings(".ty-table").hide();
        if(index == 1){
            getRecordList();
        }
    });
});
function getRecordList(){
    //untreatedList
    $.ajax({
        "url": "../workAttendance/getUpdateAttendanceLog.do",
        "data": "",
        success: function (data) {
            var html = '';
            var historyList = data["personnelAttendanceUserHistorys"];
            $("#editRecordList table").eq(1).show().siblings().hide();
            /*if(log == 0){
                html += '<tr>' +
                    '        <td>'+ historyList.createDate +'</td>' +
                    '        <td>'+ historyList.userName +'</td>' +
                    '        <td>'+ historyList.attendanceDate +'</td>' +
                    '        <td>' +
                    '            <span class="ty-color-blue" onclick="look($(this))">查看</span>' +
                    '        </td>' +
                    '    </tr>';
                $("#untreatedList tbody").html(html);
            }else if(log == 1){

                html += '<tr>' +
                    '        <td>'+ historyList.createDate +'</td>' +
                    '        <td>'+ historyList.userName +'</td>' +
                    '        <td>'+ historyList.attendanceDate +'</td>' +
                    '        <td>' +
                    '            <span class="ty-color-blue" onclick="approvedLook($(this))">查看</span>' +
                    '        </td>' +
                    '    </tr>';
                $("#approvedList tbody").html(html);
            }else if(log == 2){
                html += '<tr>' +
                    '        <td>'+ historyList.createDate +'</td>' +
                    '        <td>'+ historyList.userName +'</td>' +
                    '        <td>'+ historyList.attendanceDate +'</td>' +
                    '        <td>' +
                    '            <span class="ty-color-blue" onclick="look($(this))">查看</span>' +
                    '        </td>' +
                    '    </tr>';
                $("#rejectList tbody").html(html);
            }*/
            for(var t=0;t<historyList.length;t++){
                html +=
                    '<tr>' +
                    '    <td>'+ historyList[t].createDate +' </td>' +
                    '    <td>'+ historyList[t].createName+' </td>' +
                    '    <td>'+ new Date(historyList[t].attendanceDate).format('yyyy年MM月dd日')+'</td>' +
                    '    <td>--</td>' +
                    '    <td>--</td>' +
                    '    <td>' +
                    '        <span class="ty-color-blue" id="'+ historyList[t].id +'" onclick="approvedLook($(this))">查看</span>' +
                    '    </td>' +
                    '</tr>';
            }
            $("#approvedList tbody").html(html);
        }
    });
}
//  creater  姚宗涛  2018/2/12  13:51:06   待处理查看
var pendingThis = null ;
function look(_this){
    pendingThis = _this ;
    var usertype = chargeRole("超管");
    if (usertype) {
        bounce.show($("#look")) ;
    } else {
        $("#look .bonceFoot").hide() ;
        bounce.show($("#look")) ;
    }
}
//  creater  姚宗涛  2018/2/12  13:51:06   待处理查看弹框  的批准
function yes(){
    bounce.cancel() ;
    pendingThis.parent().parent().remove() ;
    var str = '  <tr>' +
        '                            <td>2017年10月11日12:00</td>' +
        '                            <td>陆展博</td>' +
        '                            <td>2017年10月10日</td>' +
        '                            <td>刘涛</td>' +
        '                            <td>2017年10月11日14:00</td>' +
        '                            <td>' +
        '                                <span class="ty-color-blue" onclick="approvedLook()">查看</span>' +
        '                            </td>' +
        '                        </tr>' ;
    $(".approved tbody").append(str) ;
}
//  creater  姚宗涛  2018/2/12  14:15:09  待处理查看弹框  的驳回
function no ()  {
    bounce.cancel() ;
    pendingThis.parent().parent().remove() ;
    var str = '  <tr>' +
        '                            <td>2017年10月11日12:00</td>' +
        '                            <td>陆展博</td>' +
        '                            <td>2017年10月10日</td>' +
        '                            <td>刘涛</td>' +
        '                            <td>2017年10月11日14:00</td>' +
        '                            <td>' +
        '                                <span class="ty-color-blue" onclick="rejectedLook()">查看</span>' +
        '                            </td>' +
        '                        </tr>' ;
    $(".reject tbody").append(str) ;
}
// creater  姚宗涛 2018/2/24  13:53:00  已经批准 查看
function approvedLook(obj) {
    $("#lookDetail").find(".hd").removeClass("hd") ;
    $("#lookDetail .bonceFoot").remove() ;
    var id = obj.attr("id");
    $.ajax({
        "url": "../workAttendance/updateAttendanceLogDetail.do",
        "data": {"attendanceLogId":id},
        beforeSend:function(){ loading.open(); },
        success: function (data) {
            if(data.status == "1"){
                var editBefore = "";
                var editAfter = "";
                if(data["personnelAttendanceUserHistory"]){
                    var oldInfo =data["personnelAttendanceUserHistory"]; //修改前的职工考勤
                    var oldHistory = data["personnelAttendanceDepartmentConfigHistorys"]; //修改前的职工考勤明细
                    var oldHtml = createDetailsTab(oldHistory);
                    editBefore +=
                        '                <div class="titalInfo dush clearFlt">' +
                        '                    <dl>' +
                        '                        <dd>'+ oldInfo.userName + new Date(oldInfo.attendanceDate).format('yyyy年MM月dd日') +'考勤情况 （修改前）</dd>' +
                        '                    </dl>' +
                        '                </div>' +
                        '                <div class="updaterInfo">' +
                        '                    <p> ' +
                        '                       <span>上班考勤</span> ' +
                        '                       <span class="ty-right" style="border:0;color:#333;"><span>考勤时间</span><span style="margin:0 15px;">'+ oldInfo.updateDate+'</span></span>' +
                        '                    </p>' +
                        '                    <div class="clearFlt">' +
                        '                        <dl>' +
                        '                            <dd>'+ getType(oldInfo.beginState) +'</dd>' +
                        '                        </dl>' +
                        '                    </div>' +
                        '                </div>'  + oldHtml.outStr + oldHtml.leaveStr;
                    if(oldInfo.endState != "0" && oldInfo.endState != "") {
                        editBefore +=
                            '                <div class="updaterInfo">' +
                            '                    <p> <span>下班考勤</span> </p>' +
                            '                    <div class="clearFlt">' +
                            '                        <dl>' +
                            '                            <dd>' + getType(oldInfo.endState) + '</dd>' +
                            '                        </dl>' +
                            '                    </div>' +
                            '                </div>';
                    }
                    editBefore += oldHtml.overStr ;
                }
                if(data["personnelAttendanceUser1"]){
                    var newInfo = data["personnelAttendanceUser1"];//修改后的职工考勤
                    var newDetail =data["personnelAttendanceUserDetails"];//修改后的职工考勤明细
                    var newHtml = createDetailsTab(newDetail);
                    editAfter +=
                        '<div class="titalInfo clearFlt">' +
                        '                    <dl>' +
                        '                        <dd>'+ newInfo.userName + new Date(newInfo.attendanceDate).format('yyyy年MM月dd日')+'考勤情况 （修改后）</dd>' +
                        '                        <dd></dd>' +
                        '                    </dl>' +
                        '                    <dl>' +
                        '                        <dd>审批人</dd>' +
                        '                        <dd>审批时间</dd>' +
                        '                    </dl>' +
                        '                    <dl>' +
                        '                        <dd>'+ newInfo.updateName +'</dd>' +
                        '                        <dd>'+ newInfo.updateDate +'</dd>' +
                        '                    </dl>' +
                        '                </div>' +
                        '                <div class="titalInfo clearFlt">' +
                        '                    <dl>' +
                        '                        <dd>修改理由 <span class="updateReason"> '+ data.updateDesc +' </span></dd>' +
                        '                    </dl>' +
                        '                </div>' +
                        '                <div class="updaterInfo">' +
                        '                    <p><span>上班考勤</span></p>' +
                        '                    <div class="clearFlt">' +
                        '                        <dl>' +
                        '                            <dd>'+ getType(newInfo.beginState) +'</dd>' +
                        '                        </dl>' +
                        '                    </div>' +
                        '                </div>' + newHtml.outStr + newHtml.leaveStr +
                        '                <div class="updaterInfo">' +
                        '                    <p> <span>下班考勤</span> </p>' +
                        '                    <div class="clearFlt">' +
                        '                        <dl>' +
                        '                            <dd>' + getType(newInfo.endState) + '</dd>' +
                        '                        </dl>' +
                        '                    </div>' +
                        '                </div>' + newHtml.overStr ;
                }
                var str = editAfter + editBefore;
                $("#editDetails").html(str);
                bounce.show($("#lookDetail"));
            }
        },
        complete:function () {  loading.close();  }
    });
}
// creater 姚宗涛 2018/2/24  14:27:36  已驳回  查看
function rejectedLook() {
    $("#look").find(".hd").removeClass("hd") ;
    $("#look .bonceFoot").remove() ;
    $(".rejectedReason").show() ;
    $(".rejectedReason").parent().prev().children(":eq(1)").show() ;
    bounce.show($("#look")) ;
}
//通用方法 --查看详情拼接字符串
function createDetailsTab(userInfo){
    if(userInfo){
        var html ={};
        var num1 =0,num2= 0,num3=0;
        var outOfWorkStr = '<div class="updaterInfo"><p><span>旷工</span></p>';//旷工 type =7
        var overTimeStr = '<div class="updaterInfo"><p><span>加班</span></p>';//加班type =8
        var leaveStr = '<div class="updaterInfo"><p><span>请假</span></p>';//请假type =5
        for(var f=0;f<userInfo.length;f++){
            var typeId = userInfo[f].type;
            if(typeId == "7"){
                num1 ++;
                outOfWorkStr +=
                    '<div class="clearFlt">' +
                    '    <dl>' +
                    '        <dd>开始时间</dd>' +
                    '    </dl>' +
                    '    <dl>' +
                    '        <dd>'+ userInfo[f].beginTime +'</dd>' +
                    '    </dl>' +
                    '    <dl>' +
                    '        <dd>结束时间</dd>' +
                    '    </dl>' +
                    '    <dl>' +
                    '        <dd>'+ userInfo[f].endTime +'</dd>' +
                    '    </dl>' +
                    '</div>';
            }else if(typeId == "8"){
                num2 ++;
                overTimeStr += 
                    '<div class="clearFlt">' +
                    '    <dl>' +
                    '        <dd>加班时长</dd>' +
                    '        <dd>加班事由</dd>' +
                    '    </dl>' +
                    '    <dl>' +
                    '        <dd>'+ userInfo[f].duration +'</dd>' +
                    '        <dd>'+ userInfo[f].reason +'</dd>' +
                    '    </dl>' +
                    '</div>';
            }else if(typeId == "5"){
                num3 ++;
                leaveStr +=
                    '<div class="clearFlt">' +
                    '    <dl>' +
                    '        <dd>开始时间</dd>' +
                    '        <dd>请假类型</dd>' +
                    '        <dd>请假事由</dd>' +
                    '    </dl>' +
                    '    <dl>' +
                    '        <dd>'+ userInfo[f].beginTime +'</dd>' +
                    '        <dd>'+ userInfo[f].leaveTypeName +'</dd>' +
                    '        <dd>'+ userInfo[f].reason +'</dd>' +
                    '    </dl>' +
                    '    <dl>' +
                    '        <dd>结束时间</dd>' +
                    '    </dl>' +
                    '    <dl>' +
                    '        <dd>'+ userInfo[f].endTime +'</dd>' +
                    '    </dl>' +
                    '</div>';
            }
        }
        outOfWorkStr+='</div>';
        overTimeStr+='</div>';
        leaveStr+='</div>';
        if(!num1){
            outOfWorkStr = "";
        }
        if(!num2){
            overTimeStr = "";
        }
        if(!num3){
            leaveStr = "";
        }
        html.outStr=outOfWorkStr;
        html.leaveStr=leaveStr;
        html.overStr=overTimeStr;
        return html;
    }
}

// creator lyt 2018/5/30 考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 0-其他
function getType(num) {
    switch(num){
        case "1":return "正常";break;
        case "2":return "迟到";break;
        case "3":return "早退";break;
        case "4":return "外出";break;
        case "5":return "请假";break;
        case "6":return "出差";break;
        case "7":return "旷工";break;
        case "8":return "加班";break;
        default:return "其他";break;
    }
}