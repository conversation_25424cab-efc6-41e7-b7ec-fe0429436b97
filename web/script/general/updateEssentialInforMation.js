
//creator:lyt date:2018/1/10 获取筛选条件
var condition = getUrlParam("condition");
$(function(){
    var state = getUrlParam("state");
    $(".conditionCondition").val(condition);
    $.fn.select2.defaults.set("theme", "default");
    $("#gender").select2({minimumResultsForSearch: -1});
    $("#user_edu").select2({minimumResultsForSearch: -1});
    $("#marry").select2({minimumResultsForSearch: -1});
    $(".ordinaryEmployees").select2({minimumResultsForSearch: -1});
    $(".manage1").select2({minimumResultsForSearch: -1});
    $("#leader").select2({minimumResultsForSearch: -1});
    if(state == 1){    //state: 1=基本信息可编辑状态 0-基本信息为可查看状态
        $(".addStaffInfo").show();$(".seeStaffInfoStatus").hide();
    }else{
        $(".seeStaffInfoStatus").show();$(".addStaffInfo").hide();
    }

    //必填项为空时保存按钮不能点击
    $('body').everyTime('0.5s','saveEmployeeEdit',function(){
        if($("#userName").val() !== ""&& isMobileMethod($(".mobileInfo input[name='mobile']").val())){
            if($.trim($("#idCard").val())=== ""|| isCardNo($("#idCard").val())){
                $("#saveEmployeeInfo").prop("disabled",false)
            }else{
                $("#saveEmployeeInfo").prop("disabled",true)
            }
        }else{
            $("#saveEmployeeInfo").prop("disabled",true)
        }
    });

    // 手机号码验证
    jQuery.validator.addMethod("isMobile", function(value, element) {
        var length = value.length;
        var mobile = /^(13[0-9]{9})|(18[0-9]{9})|(14[0-9]{9})|(17[0-9]{9})|(15[0-9]{9})$/;
        return this.optional(element) || (length == 11 && mobile.test(value));
    }, "请正确填写手机号码");

    $("#updateUser").validate({
        rules: {
            userName : {
                required:true
            },
            email : {
                email:true
            },
            mobile : {
                required:true,
                isMobile:true
            }
        }
    });
    //creator:lyt date:2017/1/11 限制教育背景、工作经历、评论情况行数最多为8行
    var eduNum = $("#us_edu tbody").children("tr").length;
    var workSpaceNum = $("#us_work tbody").children("tr").length;
    var assessNum = $("#us_evalute tbody").children("tr").length;
    if(eduNum>=8){
        $(".eduAvailable").hide();
        $(".eduVailable").show();
    }else{
        $(".eduAvailable").show();
        $(".eduVailable").hide();
    }
    if(workSpaceNum>=8){
        $(".occupAvailable").hide();
        $(".occupVailable").show();
    }else{
        $(".occupAvailable").show();
        $(".occupVailable").hide();
    }
    if(assessNum>=8){
        $(".assessAvailable").hide();
        $(".assessVailable").show();
    }else{
        $(".assessAvailable").show();
        $(".assessVailable").hide();
    }
});

function isMobileMethod(mobile){
    var regPhone = /^(13[0-9]|15[012356789]|18[0123456789]|147|145|17[0-9])\d{8}$/;
    if(regPhone.test(mobile)){
        return true;
    }else{
        return false;
    }
}
function isCardNo(card) {
    // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if(reg.test(card)) {
        return true;
    }else{
        return false;
    }
}
//-------------------------<<<<职位>>>>------------------------//
function shanchu( type , obj ){
    if (chargeRole("超管")) {
        $('#altMS').html('您没有此操作权限！');
        bounce.show($('#alertMS'));
        return false;
    }
    ;
    if(confirm("确定要删除？")){
        obj.parent().next().submit() ;
    }
}
// 选择职位
function getPosition(obj){
    var oid = obj.attr("id");
    var oname = obj.text();
    $(".choosePosition").find("span").removeClass("ty-btn-blue");
    obj.addClass("ty-btn-blue");
    $("#zhiName").val(oname);
    $("#zhiId").val(oid);
}

//-------------------------<<<<部门>>>>------------------------//
function getLevelName(level_num){
    var level = "";
    switch(level_num)
    {
        case 1 : level = "一"; break;
        case 2 : level = "二"; break;
        case 3 : level = "三"; break;
        case 4 : level = "四"; break;
        case 5 : level = "五"; break;
        case 6 : level = "六"; break;
        case 7 : level = "七"; break;
        case 8 : level = "八"; break;
        default: level = "子"; break;
    }
    level += "级部门";
    return level ;
}
//	单击部门，召唤出子级部门
function getKidsDepart(obj){
    obj.attr("style","background:#36C6D3; border-color:#36C6D3; color:#fff;  ");
    obj.siblings("span").attr("style","background:#fff; border-color:#666; color:#333; ");
    var oid = obj.attr("id");
    var activeIdObj = obj.parents(".levelItem").find(".activeId") ;
        activeIdObj.html(oid) ;
        $("#oId").val(oid);
    var level_num = parseInt(obj.parent().attr("level"))+1;
    var nextLevel = getLevelName(level_num);
    if( oid == "" || oid == null){
        alert("输入不合法！");
    }else{
        $.ajax({
            url:"checkSonDepartment.do",
            data:{ "id":oid   },
            type:"post",
            dataType:"json",
            success:function(data){
                obj.parents(".levelItem").nextAll().remove();
                var str = "";
                if(data.length> 0){
                    str = "<div class='levelItem' level='"+level_num+"'>" +
                        "<div style='display:none; '>" +
                        "<span class='activeId' ></span>" +
                        "</div><a class='depatlevel'>"+ nextLevel +"</a>";
                    for( var i =0 ; i< data.length; i++ ){
                        str += " <span class='ty-btn departItem' id="+data[i].id+" pid="+data[i].pid+" onclick='getKidsDepart($(this))'>"+ data[i]["name"] +"</span> "
                    }
                    // obj.attr("isLast","0");
                    $(".chooseDepart").append(str);
                }
                /*else{
                    obj.attr("isLast","1")
                }*/
                setDepatShow();
            },
            error:function(){alert("连接错误！");}
        });
    }
}

// 显示要编辑的内容
function conShow(str){
    $("#"+str).show().siblings().hide();
}
// 下拉框toggle
function toggleSelect(obj){
    var num = obj.attr("val");
    if (num == 0) {
        obj.parent().siblings("div.hd").show();
        obj.attr("val","1");
    } else{
        obj.parent().siblings("div.hd").hide();
        obj.attr("val","0");
    };
    return false;
}
function toggleSe(obj){
    var toggleObj = obj.prev();
    toggleSelect(toggleObj);
}
// 设置选择部门的路径
//update:李玉婷 date:2017/12/11
function setDepatShow(){
    var DepartLink = "";
    $(".activeId").each(function(){
        var activeIdObj = $(this);
        var activeId = activeIdObj.html();
        activeIdObj.parents(".levelItem").find(".departItem").each(function(){
            var id = $(this).attr("id");
            if (activeId == id) {
                DepartLink +=  $(this).html() + " - " ;
            }
        })
    });
    $("#oName").val(DepartLink.substring(0 , DepartLink.length-2));
}
function setDepatShow22(){
    // 职位中权限的显示、
    var activeZhiId = $("#activeZhiId").html();
    if(activeZhiId == "" || activeZhiId == null || activeZhiId == "  "){
        $("#zhiControl").hide();
    }else{
        $("#zhiControl").show();
    }
}
//update: 李玉婷 date:2018/1/8 保存信息按钮
function saveEmployeeEdit() {
    if($("#manage").val() === ''||$("#manage").val() === undefined){
        $("#manage").prev().val("");
        $("#manage").prev().prev().val("");
    }else{
        var manager = $("#manage").val().split("-")[0];
        var managerCode = $("#manage").val().split("-")[1];
        $("#manage").prev().val(managerCode);
        $("#manage").prev().prev().val(manager);
    }
    $("body").stopTime();
    $("#saveEmployeeInfo").prop("disabled",true);
    $("#saveEmployeeInfo").html("正在保存");
    $('#updateUser').submit();
}

//creator:李玉婷 date:2017/12/3 编辑按钮
function editStaffInfo(id){
    location.href='toUpdateEssentialInformation.do?id='+id+'&state=1&condition='+condition;
}
//上传头像
//update:李玉婷 date:2017/1/4
function setImagePreview() {
    var docObj = document.getElementById("imgFile");
    var imgObjPreview = document.getElementById("preview");
    if (docObj.files && docObj.files[0]) {
        /*火狐下，直接设img属性*/
        //imgObjPreview.style.display = 'inline-block';
        //imgObjPreview.style.width = '150px';
        //imgObjPreview.style.height = '150px';
        /*imgObjPreview.src = docObj.files[0].getAsDataURL();*/
        /*火狐7以上版本不能用上面的getAsDataURL()方式获取，需要一下方式*/
        //creator:lyt date:2018/1/11 num:0=未修改头像图片 1：修改了头像图片
        $(".editFile").val("1");
        imgObjPreview.src = window.URL.createObjectURL(docObj.files[0]);
    } else {
        $(".editFile").val("0");
        /*IE下，使用滤镜*/
        docObj.select();
        var imgSrc = document.selection.createRange().text;
        var localImagId = document.getElementById("localImag");
        /*必须设置初始大小*/
        //localImagId.style.width = "150px";
        //localImagId.style.height = "150px";
        /*图片异常的捕捉，防止用户修改后缀来伪造图片*/
        try {
            localImagId.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)";
            localImagId.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src           = imgSrc;
        } catch (e) {
            alert("您上传的图片格式不正确，请重新选择!");
            return false;
        }
        imgObjPreview.style.display = ' ';
        document.selection.empty();
    }
    return true;
}

//
function openWindowSure(str,obj){
    if(chargeRole('超管')){
        $('#altMS').html('您没有此操作权限！');
        bounce.show($('#alertMS'));return false;
    }else{
        var userId = obj.attr("userId");
        var address = obj.attr("address");
        if(str == "id"){
            openWindow(address+'?id=' +userId,'800','700');
        }else if(str == "userId"){
            openWindow(address+'?userId=' +userId,'800','700');
        }else if(str == "folkId"){
            var fId = obj.attr("folkId");
            openWindow(address+'?folkId='+fId+'&userId=' +userId,'800','700');
        }
    }
}


//creator:lyt date:2017/12/8 10:40 删除头像
function deleteUerImg(){
    $("#preview").attr("src","${pageContext.request.contextPath}/assets/oralResource/user.png");
    $("#imgFile").val("");
    $(".editFile").val("1");
}
function goIndex(){
    location.href='employeeIndex.do?condition='+condition;
}
// 时间控件初始化
laydate.render({elem: '#birthday'});
laydate.render({elem: '#date1'});
