var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#seeHistoryDetail"));
bounce_Fixed2.cancel();
$(function () {
    console.log(sphdSocket.user)
    getQRCodeUrl();
    jumpPage("main", function () {
        // 主页为静态资源
        $(".ty-page-header").hide()
        getRecruitNoticeList()
    })
    //放大二维码
    $("#qrCode_small").on("click",function () {
        bounce.show($("#QR_code"))
    })
    // 启事页面按钮事件
    $(".page[page='main']").on("click", "[type='btn']", function () {
        var name = $(this).attr("name")
        var id = $(this).parents("tr").data("id")
        $("#recruitManage").data("id", id)
        switch(name) {
            // 管理按钮
            case 'manage':
                bounce.show($("#recruitManage"))
                $("#recruitManage .toggleItem").hide()
                $("#recruitManage input:radio").prop("checked", false)
                var intervals = $(this).parents("tr").data("intervals")
                $("#recruitManage #changeResumeIntervals").val(intervals)
                $("#recruitManage .normalRecruit").show().siblings().hide()
                break
            // 已收简历、通知面试、已面试、已入职 各人数点击
            case 'share':
                let url = $.webRoot + '/vue/recruit/dist/index.html#/wxshare/' + id + '/' + sphdSocket.user.userID + '/' + sphdSocket.user.oid
                window.open(url, '_blank')
                break
            case 'recruitCount':
                jumpPage("recruitCount")
                var by = $(this).attr("by")
                getRecruitCount(by)
                break
        }
    })
    // 已失效的招聘启事页面按钮事件
    $(".page[page='invalidRecruitNotice']").on("click", "[type='btn']", function () {
        var name = $(this).attr("name")
        var id = $(this).parents("tr").data("id")
        $("#recruitManage").data("id", id)
        switch(name) {
            case 'manage':
                bounce.show($("#recruitManage"))
                $("#recruitManage .voidRecruit").show().siblings().hide()
                var deadline = $(this).parents("tr").find("td").eq(1).text()
                $("#recruitManage input:radio").prop("checked", false)
                $("#recruitManage .deadline").html(deadline)
                break
            case 'recruitCount':
                jumpPage("recruitCount")
                var by = $(this).attr("by")
                getRecruitCount(by)
                break
        }
    })
    $(".page[page='editRecruitCommon']").on("click", "[type='btn']", function () {
        var name = $(this).attr("name")
        switch(name) {
            case 'edit':
                var code = $(this).parents("tr").data("code")
                var dialogName = "#editNotice_" + code

                $(".halfTime").html('<option value="">------请选择------</option>'+getTimeOption())
                if (code === 'custom') {
                    var noticeData = $(".tbl_RecruitNotice").data("notice")
                    var itemName = $.trim($(this).parents("tr").find("td").eq(0).html())
                    var noticeItem = noticeData[noticeData.findIndex(item => item.itemName === itemName)]
                    var content = noticeItem.content

                    var id = $(this).parents("tr").data("id")
                    if (id) {
                        // 非第一次自定义的项目，只能修改内容
                        bounce.show($(dialogName))
                        $("#editNotice_custom .itemName").html(itemName)
                        if (content) {
                            $(dialogName).find("textarea").val(content)
                        } else {
                            $(dialogName).find("textarea").val("")
                        }
                    } else {
                        // 第一次自定义的项目, 打开新增一行的弹窗，可以修改项目名字和内容
                        bounce.show($("#addOneNoticeProject"))
                        $("#addOneNoticeProject").data('type', 'change')
                        $("#addOneNoticeProject").data('oldItemName', itemName)
                        $("#addOneNoticeProject .bounce_title").html("编辑项目内容")
                        $("#addOneNoticeProject [name='itemName']").val(itemName)
                        $("#addOneNoticeProject [name='content']").val(content)
                        $("#addOneNoticeProject input:radio[value='"+noticeItem.unitive+"']").prop("checked", true)
                    }
                } else {
                    bounce.show($(dialogName))
                    initNoticeDialog(code)
                }
                $('#editNotice_' + code + ' .textMax').each(function (){
                    var curLength = $(this).parents(".item").find(".autoFill").val().length
                    var max = $(this).parents(".item").find(".autoFill").attr("max")
                    $(this).text( curLength +'/' + max );
                })
                break
            case 'changeState':
                var code = $(this).parents("tr").data("code")
                var need = $(this).parents("tr").data("need")
                var noticeData = $(".tbl_RecruitNotice").data("notice")
                if (need || code === 'image') {
                    layer.msg("操作失败！此项必须显示！")
                    return false
                }
                if (code === 'custom') {
                    var itemName = $.trim($(this).parents("tr").find("td").eq(0).text())
                    var thisItem = noticeData[noticeData.findIndex(item => item.itemName === itemName)]
                } else {
                    var thisItem = noticeData[noticeData.findIndex(item => item.itemCode === code)]
                }
                thisItem.show = !thisItem.show
                renderRecruitNotice('item')
                break
        }
    })
    $(".page[page='postNeedList']").on("click", "[type='btn']", function () {
        var name = $(this).attr("name")
        switch(name) {
            case 'edit':
                jumpPage("editPostNeed")
                var postData = $(this).parents("tr").find(".hd").html()
                postData = JSON.parse(postData)
                $(".tbl_PostNeed").data("post", postData.data)
                $(".tbl_PostNeed").data("id", postData.id)
                $(".tbl_PostNeed").data("type", "change")
                renderPostNeed()

                break
            case 'delete':
                var id = $(this).parents("tr").data("id")
                $("#errorTip .tipWord").html("确定本次招聘不再包含此条岗位吗？");
                $("#errorTip .sureBtn").unbind().on("click", function (){
                    var postNeed =  $(".tbl_postNeedList").data("postneed")
                    postNeed.splice(postNeed.findIndex(item => item.id === id), 1)
                    renderPostNeedList()
                    bounce.cancel()
                })
                bounce.show($("#errorTip"))
                break
        }
    })
    $(".page[page='editPostNeed']").on("click", "[type='btn']", function () {
        var name = $(this).attr("name")
        switch(name) {
            case 'edit':
                var code = $(this).parents("tr").data("code")
                var dialogName = '#editPost_' + code

                $(".halfTime").html('<option value="">------请选择------</option>'+getTimeOption())
                if (code === 'custom') {
                    var postData = $(".tbl_PostNeed").data("post");

                    var itemName = $.trim($(this).parents("tr").find("td").eq(0).html())
                    var postItem = postData[postData.findIndex(item => item.itemName === itemName)]
                    var content = postItem.content

                    var id = $(this).parents("tr").data("id")
                    if (id) {
                        // 非第一次自定义的项目，只能修改内容
                        bounce.show(dialogName)
                        $("#editPost_custom .itemName").html(itemName)
                        if (content) {
                            $(dialogName).find("textarea").val(content)
                        } else {
                            $(dialogName).find("textarea").val("")
                        }
                    } else {
                        // 第一次自定义的项目, 打开新增一行的弹窗，可以修改项目名字和内容
                        bounce.show($("#addOnePostProject"))
                        $("#addOnePostProject").data('type', 'change')
                        $("#addOnePostProject").data('oldItemName', itemName)
                        $("#addOnePostProject .bounce_title").html("编辑项目内容")
                        $("#addOnePostProject [name='itemName']").val(itemName)
                        $("#addOnePostProject [name='content']").val(content)
                        $("#addOnePostProject input:radio[value='"+postItem.unitive+"']").prop("checked", true)
                    }
                } else {
                    bounce.show($(dialogName))
                    initPostDialog(code)
                }
                $(dialogName + ' .textMax').each(function (){
                    var curLength = $(this).parents(".item").find(".autoFill").val().length
                    var max = $(this).parents(".item").find(".autoFill").attr("max")
                    $(this).text( curLength +'/' + max );
                })
                break
            case 'changeState':
                var code = $(this).parents("tr").data("code")
                var need = $(this).parents("tr").data("need")
                var postData = $(".tbl_PostNeed").data("post")
                if (need) {
                    layer.msg("操作失败！此项必须显示！")
                    return false
                }
                if (code === 'custom') {
                    var itemName = $.trim($(this).parents("tr").find("td").eq(0).text())
                    var thisItem = postData[postData.findIndex(item => item.itemName === itemName)]
                } else {
                    var thisItem = postData[postData.findIndex(item => item.itemCode === code)]
                }
                thisItem.show = !thisItem.show
                renderPostNeed()
                break
        }
    })
    $(".radioToggle").on("click change", function (){
        $("#changeResumeIntervals").val("")
        var forTo = $(this).attr("for")
        $("#"+forTo).find(".kj-input").val("")
        $("#"+forTo).find("input:radio").prop("checked", false)
        $(this).parents(".bonceContainer").find(".toggleItem").hide()
        if ($(this).prop("checked")) {
            $("#"+forTo).show()
        } else {
            $("#"+forTo).hide()
        }
    })
    $("#changeResumeIntervals").on("change", function (){
        $(".radioToggle").prop("checked", false)
        $(".toggleItem").hide()
    })
    $("#postName").on("change", function (){
        var val = $(this).val()
        if (val) {
            $.ajax({
                url: $.webRoot + '/post/getPostInfo.do',
                data: {
                    id: val
                }
            }).then(res => {
                $(".postInfo").show()
                var data = res.data
                var duty = JSON.parse(data.duty || "[]")
                var requirement = JSON.parse(data.requirement || "[]")
                var synthesis = JSON.parse(data.synthesis || "[]")
                var dutyStr = ''
                for (var i = 0; i < duty.length; i++) {
                    dutyStr += '<div>'+(i+1) + '、' + duty[i]+'</div>'
                }
                var requirementStr = ''
                for (var j = 0; j < requirement.length; j++) {
                    requirementStr += '<div>'+(j+1) + '、' + requirement[j]+'</div>'
                }
                $(".postSee_name").html(data.name)
                $(".postSee_duty").html(dutyStr)
                $(".postSee_requirement").html(requirementStr)
                $(".postSee_synthesis").html(synthesis.join('、'))
                $(".postInfo").data("postinfo", {
                    duty: duty,
                    requirement: requirement,
                    synthesis: synthesis
                })
            })
        } else {
            $(".postInfo").hide()
        }
    })
    var sw = false
    $("body").on("input focus keyup", '.autoFill', function () {
        if (sw === false) {
            var max = $(this).attr("max")
            var curLength = $(this).val().length;
            if (curLength > max) {
                curLength = max
                var cutValue = $(this).val().substring(0,max);
                $(this).val(cutValue);
            }
            var textMaxSelector = $(this).siblings(".textMax")
            if (textMaxSelector.length === 0) {
                textMaxSelector = $(this).parents(".item").find(".textMax")
            }
            textMaxSelector.text( curLength +'/' + max );
        }
    })
    $("body").on("compositionstart", '.autoFill', function () {
        sw = true
    })
    $("body").on("compositionend", '.autoFill', function () {
        sw = false
        var max = $(this).attr("max")
        var curLength = $(this).val().length;
        if (curLength > max) {
            curLength = max
            var cutValue = $(this).val().substring(0,max);
            $(this).val(cutValue);
        }
        var textMaxSelector = $(this).siblings(".textMax")
        if (textMaxSelector.length === 0) {
            textMaxSelector = $(this).parents(".item").find(".textMax")
        }
        textMaxSelector.text( curLength +'/' + max );
    })
    $(document).click(function () {
        $(".input_choose_list:not(.dir)").slideUp('fast');
    });
    $(".custom_select .input_choose").on("click", function () {
        $(this).find('.search').focus()
        $(this).parents(".custom_select").find(".input_choose_list").slideDown('fast')
        return false
    })
    $(".custom_select .input_choose").on('input', '.search', function () {
        var customSelector =  $(this).parents(".custom_select")
        var list = customSelector.find(".input_choose").data('list')
        var val = $(this).val()
        var newList = list.filter(item => {
            return item.itemName.indexOf(val) !== -1
        })
        var listStr = updateSynthesizeList(newList)
        customSelector.find(".input_choose_list").html(listStr)
    })
    $(".custom_select .input_choose").on('click','.delRole', function () {
        var customSelector =  $(this).parents(".custom_select")
        var userID = $(this).parents('.selected_item').data('id')
        var list = customSelector.find(".input_choose").data('list')

        list.map(item => {
            if (item.id === userID) {
                item.select = false
            }
        })

        var listStr = updateSynthesizeList(list)
        customSelector.find(".input_choose_list").html(listStr)
        $(this).parents('.selected_item').remove()
    })
    $(".input_choose_list").on('click', '.input_choose_item', function () {
        var customSelector =  $(this).parents(".custom_select")
        if(!$(this).hasClass("selected")){
            $(this).addClass('selected')
            var role_name = $(this).find('.input_choose_item_name').html()
            var role_id = $(this).data('id')
            var list = customSelector.find(".input_choose").data('list')

            list.map(item => {
                if (item.id === role_id) {
                    item.select = true
                }
            })

            var listStr = updateSynthesizeList(list)
            customSelector.find(".input_choose_list ").html(listStr)
            var str =   '<div class="selected_item" data-id="'+role_id+'">' +
                '    <span class="selected_name">'+role_name+'</span>' +
                '    <span class="delRole">+</span>' +
                '</div>'
            customSelector.find(".search").before(str).val("")
        }
        return false
    })
});
// ---------------------- 主页 ------------------------

// creator: 张旭博，2023-05-08 10:58:39， 分享到微信得效果
function shareWechatEffect() {
    bounce_Fixed.show($("#shareWechatEffect"))
}

// creator: 张旭博，2023-06-01 02:34:39， 获取招聘启事
function getRecruitNoticeList() {
    $.ajax($.webRoot + '/resume/getRdNoticeList.do')
        .then(res => {
            var data = res.data
            var str = ''
            for (var i in data) {
                var postData = data[i].rdNoticePostList || []
                var postStrArr = []
                for (var j=0; j<postData.length; j++) {
                    var postName = postData[j].postName
                    var planCount = postData[j].planCount
                    var postAtr = postName + planCount + '名'
                    postStrArr.push(postAtr)
                }
                str +=  '<tr data-id="'+data[i].id+'" data-intervals="'+data[i].intervals+'">' +
                    '    <td>'+data[i].createName+ ' ' + moment(data[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>' +
                    '    <td>'+moment(data[i].endTime).format("YYYY-MM-DD")+'</td>' +
                    '    <td>'+postStrArr.join("，")+'</td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="resume">'+data[i].recievedCount+'份</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="interview">'+data[i].noticedCount+'人</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="interviewed">'+data[i].interviewedCount+'人</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="employed">'+data[i].enteredCount+'人</span></td>' +
                    '    <td>' +
                    '        <span class="link-blue" type="btn" name="manage">管理</span>' +
                    '        <span class="link-blue" type="btn" name="share">分享</span>' +
                    '    </td>'
                    '</tr>'

            }
            $(".table_recruitNoticeManage tbody").html(str)
        })
}

// creator: 张旭博，2023-05-31 04:00:48， 二维码
function qrCodeBtn() {
    jumpPage('qrCode')
}
// ---------------------- 启事 ------------------------

// creator: 张旭博，2023-05-08 11:02:33， 已失效的招聘启事
function invalidRecruitNoticeBtn() {
    jumpPage('invalidRecruitNotice', function (){
        $("#countMonth").val("")
        $.ajax('/resume/getDeaNoticeList.do').then(res => {
            var data = res.data
            var str = ''
            for (var i in data) {
                var postData = data[i].rdNoticePostList || []
                var postStrArr = []
                for (var j=0; j<postData.length; j++) {
                    var postName = postData[j].postName
                    var planCount = postData[j].planCount
                    var postAtr = postName + planCount + '名'
                    postStrArr.push(postAtr)
                }
                str +=  '<tr data-id="'+data[i].id+'">' +
                    '    <td>'+data[i].createName+ ' ' + moment(data[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>' +
                    '    <td>'+moment(data[i].endTime).format("YYYY-MM-DD")+'</td>' +
                    '    <td>'+moment(data[i].enabledTime).format("YYYY-MM-DD")+'</td>' +
                    '    <td>'+postStrArr.join("，")+'</td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="resume">'+data[i].recievedCount+'份</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="interview">'+data[i].noticedCount+'人</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="interviewed">'+data[i].interviewedCount+'人</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="employed">'+data[i].enteredCount+'人</span></td>' +
                    '    <td>' +
                    '        <span class="link-blue" type="btn" name="manage">管理</span>' +
                    '    </td>'
                '</tr>'
            }
            $(".page[page='invalidRecruitNotice'] tbody").html(str)
            $(".page[page='invalidRecruitNotice'] .beginDay").html(moment().format("YYYY-MM-") + '01')
            $(".page[page='invalidRecruitNotice'] .endDay").html(moment().format("YYYY-MM-DD"))
        })
    })
}

// creator: 张旭博，2023-05-25 02:41:03, 查询已失效的招聘启事
function screen () {
    var month = $("#countMonth").val()
    $.ajax({
        url: $.webRoot + '/resume/getDeaNoticesByYearMonth.do',
        data: {
            yearMonth: month
        },
        success: function (res) {
            var data = res.data
            var str = ''
            for (var i in data) {
                var postData = data[i].rdNoticePostList || []
                var postStrArr = []
                for (var j=0; j<postData.length; j++) {
                    var postName = postData[j].postName
                    var planCount = postData[j].planCount
                    var postAtr = postName + planCount + '名'
                    postStrArr.push(postAtr)
                }
                str +=  '<tr data-id="'+data[i].id+'">' +
                    '    <td>'+data[i].createName+ ' ' + moment(data[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>' +
                    '    <td>'+moment(data[i].endTime).format("YYYY-MM-DD")+'</td>' +
                    '    <td>'+postStrArr.join("，")+'</td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="resume">'+data[i].recievedCount+'份</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="interview">'+data[i].noticedCount+'人</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="interviewed">'+data[i].interviewedCount+'人</span></td>' +
                    '    <td><span class="link-blue" type="btn" name="recruitCount" by="employed">'+data[i].enteredCount+'人</span></td>' +
                    '    <td>' +
                    '        <span class="link-blue" type="btn" name="manage">管理</span>' +
                    '    </td>'
                '</tr>'
            }
            $(".page[page='invalidRecruitNotice'] tbody").html(str)
            $(".page[page='invalidRecruitNotice'] .beginDay").html(month + '-01')
            $(".page[page='invalidRecruitNotice'] .endDay").html(month + '-' + new Date(month.slice(0, 4), month.slice(5, 7), 0).getDate())
        }
    })
}

// creator: 张旭博，2023-06-12 11:28:54， 招聘启事 - 管理 - 确定
function sureRecruitManage() {
    var isVoid = $("#recruitManage .voidRecruit").is(":visible")
    var id = $("#recruitManage").data("id")
    if (isVoid) {
        // 已失效的招聘启事 - 管理
        var checked = $("input:radio[name='voidnew']").prop("checked")
        if (!checked) {
            layer.msg("请勾选对应内容")
            return false
        } else {
            // 勾选了以此为基础创建一个新的招聘启事
            createANewRecruit(id)
        }
    } else {
        var recruitSetting = $("input:radio[name='recruitSetting']:checked").val()

        if (recruitSetting === '1') {
            var endTime = $("#changeResumeStopTime").val()
            $.ajax({
                url: $.webRoot + '/resume/updateRdNoticeEndTime.do',
                data: {
                    id: id,
                    endTime: endTime
                }
            }).then(res => {
                var data = res.data
                if (data === 1) {
                    layer.msg("操作成功！")
                    bounce.cancel()
                    getRecruitNoticeList()
                }
            })
        } else if (recruitSetting === '2') {
            var endnew = $("input:radio[name='endnew']:checked").val()
            if (endnew === '1') {
                $.ajax({
                    url: $.webRoot + '/resume/rdNoticeCancel.do',
                    data: {
                        id: id
                    }
                }).then(res => {
                    var data = res.data
                    if (data === 1) {
                        layer.msg("操作成功！")
                        bounce.cancel()
                        getRecruitNoticeList()
                    }
                })
            } else {
                createANewRecruit(id)
            }
        } else {
            var intervals = $("#changeResumeIntervals").val()
            $.ajax({
                url: $.webRoot + '/resume/updateRdNoticeIntervals.do',
                data: {
                    id: id,
                    intervals: intervals
                }
            }).then(res => {
                var data = res.data
                if (data === 1) {
                    layer.msg("操作成功！")
                    bounce.cancel()
                }
            })
        }
    }
}

// creator: 张旭博，2023-06-27 02:59:19， 以此为基础创建一个新的招聘启事
function createANewRecruit(id) {
    $.ajax({
        url: $.webRoot + '/resume/getNoticeCopyInfo.do',
        data: {
            id: id
        }
    }).then(res => {
        var data = res.data
        var noticePostList = data.noticePostList
        var noticeItemList = data.noticeItemList
        var temArr = []
        noticeItemList.forEach(item => {
            if (item.content) {
                try {
                    item.content = JSON.parse(item.content)
                } catch (e) {
                    // console.log(e)
                }
            }
        })
        noticePostList.forEach((item, index) => {
            if (item.content) {
                try {
                    item.content = JSON.parse(item.content)
                } catch (e) {
                    // console.log(e)
                }
            }
            if (temArr.findIndex(it => it.id === item.noticePostId) == -1) {
                temArr.push({
                    id: item.noticePostId,
                    data: [item]
                })
            } else {
                temArr[temArr.findIndex(it => it.id === item.noticePostId)].data.push(item)
            }

        })
        console.log(temArr)
        jumpPage('createRecruitNotice', function (){
            $.ajax({
                url: $.webRoot + '/resume/getNoticeItemList.do',
                success: function (res) {
                    var data = res.data
                    // 区分出来上传和显示的值
                    data.map(item => {
                        item.content = ''
                    })
                    data[data.findIndex(item => item.itemCode === 'resumeChanges')].content = '10'
                    $(".tbl_RecruitNotice").data("origin", JSON.parse(JSON.stringify(data)))
                    $(".tbl_RecruitNotice").data("notice", noticeItemList)
                    $(".tbl_postNeedList").data("postneed", temArr)
                    renderRecruitNotice('item')
                    renderRecruitNotice('count')
                    bounce.cancel()
                }
            })
        })
    })
}

// creator: 张旭博，2023-06-14 08:58:40， 获取招聘详情
function getRecruitInfo(id) {
    $.ajax({
        url: $.webRoot + '/resume/getRdnoticeInfo.do?id=' + id,
        type: 'GET'
    }).then(res => {
        var data = res.data
        var rdNotice = data.rdNotice
        var rdNoticeItemList = data.rdNoticeItemList
        var rdNoticeAdditionalList = data.rdNoticeAdditionalList
        var rdNoticePostList = data.rdNoticePostList
        var resumeChanges = getContent(rdNoticeItemList, 'resumeChanges')
        var deadline = getContent(rdNoticeItemList, 'deadline')
        $("#changeResumeIntervals").val(resumeChanges)
        $("#recruitManage .deadline").html(deadline)
    })
}

// ---------------------- 创建招聘启事 ------------------------

// creator: 张旭博，2023-05-08 11:02:33， 创建招聘启事
function createRecruitNotice() {
    jumpPage('createRecruitNotice', function (){
        $.ajax({
            url: $.webRoot + '/resume/getNoticeItemList.do',
            success: function (res) {
                var data = res.data
                // 区分出来上传和显示的值
                data.map(item => {
                    item.content = ''
                })
                $(".tbl_postNeedList").data("postneed", [])
                data[data.findIndex(item => item.itemCode === 'resumeChanges')].content = '10'
                $(".tbl_RecruitNotice").data("origin", JSON.parse(JSON.stringify(data)))
                $(".tbl_RecruitNotice").data("notice", JSON.parse(JSON.stringify(data)))
                $(".tbl_RecruitNotice").data("noticeset", false)
                $(".tbl_RecruitNotice").data('setting', {
                    workRest: {
                        unitive : null,
                        beginTime : '',
                        endTime : ''
                    },
                    address: {
                        unitive : null,
                        address : ''
                    }
                })
                renderRecruitNotice('count')
            }
        })

    })
}

function editRecruitCommon() {
    jumpPage('editRecruitCommon', function (){
        renderRecruitNotice('item')
    })
}

// creator: 张旭博，2023-05-08 01:35:57， 清空数据 - 按钮
function clearRecruitDataBtn() {
    let checked = $("#clearContent").prop("checked")
    if (!checked) {
        layer.msg("请先勾选")
    } else {
        $("#errorTip .tipWord").html("确定清空页面上的全部数据吗？");
        $("#errorTip .sureBtn").unbind().on("click", function (){
            layer.msg("操作成功")
            bounce.cancel()
            clearRecruitData()
            renderRecruitNotice('item')
            renderRecruitNotice('count')
            $("#clearContent").prop("checked", false)

        })
        bounce.show($("#errorTip"))
    }
}

// creator: 张旭博，2023-06-01 02:29:02， 确定 - 清空数据
function clearRecruitData() {
    $(".tbl_postNeedList").data("postneed", [])
    var origin = $(".tbl_RecruitNotice").data("origin")
    $(".tbl_RecruitNotice").data("notice", JSON.parse(JSON.stringify(origin)))
}

// creator: 张旭博，2023-05-08 01:35:57， 效果预览
function effectPreviewBtn() {
    var rdNoticePostList = $(".tbl_postNeedList").data("postneed") || []
    var rdNoticeItemList = $(".tbl_RecruitNotice").data("notice")
    var deadline = getContent(rdNoticeItemList, 'deadline')
    if (rdNoticePostList.length === 0 || !deadline) {
        layer.msg("操作失败！<br>招聘启事中尚有重要要素无数据！")
        return false
    } else {
        jumpPage("effectPreview", function (){
            $(".otherData .item-row").hide()

            var rdNoticeItemList =    $(".tbl_RecruitNotice").data("notice")
            var postStr = ''
            var postCountArr = []
            for (var i in rdNoticePostList) {
                var postItem = rdNoticePostList[i].data
                var postName = getContent(postItem, 'postName', 'postName')
                var recruitingNumbers = getContent(postItem, 'recruitingNumbers')
                postStr +=  '<tr>' +
                    '    <td>'+ postName +'</td>' +
                    '    <td>'+ recruitingNumbers +'人</td>' +
                    '    <td>' +
                    '        <span class="link-blue seeDetail" onclick="seePostDetail($(this))">查看详情</span>' +
                    '        <span class="link-blue" onclick="acceptFake()">应聘</span>' +
                    '    </td>' +
                    '    <td class="hd">'+JSON.stringify(postItem)+'</td>'
                '</tr>'
                postCountArr.push(postName + recruitingNumbers + '名')
            }
            var image = getContent(rdNoticeItemList, 'image')
            if (image) {
                image = $.uploadUrl + image
            } else {
                image = $.webRoot + '/assets/images/recruit_default.png'
            }
            $(".effect_notice_image").attr('src', image)
            console.log(sphdSocket.user)
            $(".app_info .orgName").html(sphdSocket.user.userName)

            $(".share_post").html(postCountArr.join("，"))

            var deadline = getContent(rdNoticeItemList, 'deadline')
            $(".share_deadline").html(deadline)
            $(".effect_notice_deadline").show()
            $(".effect_notice_deadline .item-content").html(deadline)


            var beginTime = getContent(rdNoticeItemList, 'workRest', 'beginTime')
            var endTime = getContent(rdNoticeItemList, 'workRest', 'endTime')
            var beginTimeShow = getShow(rdNoticeItemList, 'workRest')
            if (beginTime && beginTimeShow) {
                $(".effect_notice_workRest").show()
                $(".effect_notice_workRest .item-content").html(beginTime + ' - ' + endTime)
            }
            $(".effect_tbl_post tbody").html(postStr)
            $(".effect_tbl_post .seeDetail").eq(0).click()
            $(".effect_introduce").html(getContent(rdNoticeItemList, 'introduce'))

            var addressShow = getShow(rdNoticeItemList, 'address')
            if (addressShow) {
                var address = getContent(rdNoticeItemList, 'address', 'address')
                if (address) {
                    $(".effect_notice_address").show()
                    $(".effect_notice_address .item-content").html(address)
                }
            }
            var descriptionShow = getShow(rdNoticeItemList, 'description')
            if (descriptionShow) {
                var description = getContent(rdNoticeItemList, 'description')
                if (description) {
                    $(".effect_notice_description").show()
                    $(".effect_notice_description .item-content").html(description)
                }
            }
            var contactShow = getShow(rdNoticeItemList, 'contact')
            if (contactShow) {
                var contacter = getContent(rdNoticeItemList, 'contact', 'contacter')
                var telephone = getContent(rdNoticeItemList, 'contact', 'telephone')
                var email = getContent(rdNoticeItemList, 'contact', 'email')
                if (contacter) {
                    $(".effect_notice_contact").show()
                    $(".effect_notice_contact .item-content").html(contacter)
                }
                if (telephone) {
                    $(".effect_notice_telephone").show()
                    $(".effect_notice_telephone .item-content").html(telephone)
                }
                if (email) {
                    $(".effect_notice_email").show()
                    $(".effect_notice_email .item-content").html(email)
                }
            }
            var custom = getContent(rdNoticeItemList, 'custom')
            if (custom) {
                $(".effect_notice_custom").show()
                $(".effect_notice_custom").html(custom)
            }
        })
    }
}

// creator: 张旭博，2023-06-29 02:27:44， 应聘 - 按钮（假）
function acceptFake() {
    layer.msg("这只是个示意图哦！微信分享的链接才能“应聘”！")
}

// creator: 张旭博，2025-07-23 01:59:17， 编辑完成
function recruitSetDone() {
    let radio = $("#recruitSetDone").prop("checked")
    if (!radio) {
        layer.msg('请先勾选！')
    } else {
        var noticeData = $(".tbl_RecruitNotice").data("notice")
        var postNeedList = $(".tbl_postNeedList").data("postneed") || []
        var postNeedListLast = postNeedList.map(item => item.data)

        var deadline = noticeData[noticeData.findIndex(item => item.itemCode === 'deadline')].content
        if (postNeedList.length === 0 || !deadline) {
            layer.msg("操作失败！<br>招聘启事中尚有重要要素无数据！")
            return false
        }
        var json = {
            noticeItemList: noticeData,
            noticePostList: postNeedListLast
        }
        $.ajax({
            url: $.webRoot + '/resume/addRdNotice.do',
            data: {
                json: JSON.stringify(json)
            }
        }).then(res => {
            var data = res.data
            if (data === 1) {
                layer.msg("已成功保存！<br>分享至微信，需在系统手机端操作！")
                clearRecruitData()
                back(function (){
                    getRecruitNoticeList()
                })
            } else {
                layer.msg("操作失败！")
            }
        })
    }
}

// creator: 张旭博，2023-06-08 08:49:04， 获取某个数据
function getContent (data, code, nextCode) {
    if (code === 'custom') {
        var str = ''
        data.forEach(item => {
            if (item.itemCode === 'custom' && item.show && item.content) {
                str +=  '<div class="item-row effect_notice_email">' +
                        '    <div class="item-title">'+item.itemName+'：</div>' +
                        '    <div class="item-content">'+item.content+'</div>' +
                        '</div>'
            }
        })
        return str
    } else {
        let index = data.findIndex(it => it.itemCode === code)
        if (index !== -1) {
            if (nextCode) {
                if (code === 'postName' && nextCode !== 'postName') {
                    let otherData = data[index].otherData
                    if (otherData) {
                        var itemArr = otherData[nextCode]
                        var newArr =  itemArr.map((item, index) => { return (index+1) + '、' + item })
                        return newArr.join('<br>')
                    } else {
                        return ''
                    }
                } else {
                    var content = data[index].content
                    if (content) {
                        return content[nextCode]
                    } else {
                        return ''
                    }
                }
            } else {
                if (code === 'otherAsk') {
                    let content = data[index].content
                    let itemArr = []
                    if (content.age) {
                        let ageThresholdStr = content.ageThreshold ? (content.ageThreshold === '1' ? '或以上' : '或以下') : ''
                        itemArr.push('要求年龄在' + content.age + '周岁' + ageThresholdStr)
                    }
                    if (content.gender) {
                        itemArr.push('要求性别为' + chargeNoticeGender(content.gender))
                    }
                    if (content.education) {
                        const eduArr = ['硕士', '本科', '大专', '高中或中专']
                        let educationThresholdStr = content.educationThreshold ? (content.educationThreshold === '1' ? '或以上' : '或以下') : ''
                        itemArr.push('要求学历在' + eduArr[content.education - 1] + educationThresholdStr)
                    }
                    if (content.residence) {
                        itemArr.push('要求户籍在' + (content.residence === '1' ? '本地户籍' : '非本地户籍'))
                    }
                    if (content.experiences) {
                        itemArr.push('要求工作经验为' + content.experiences)
                    }
                    if (content.characters) {
                        itemArr.push('要求性格为' + content.characters)
                    }
                    var newArr =  itemArr.map((item, index) => { return (index+1) + '、' + item })
                    return newArr.join('<br>')
                } else {
                    return data[index].content
                }
            }
        } else {
            return ''
        }
    }
}

// creator: 张旭博，2023-06-29 02:28:27， 获取是否显示
function getShow (data, code) {
    let index = data.findIndex(it => it.itemCode === code)
    if (index !== -1) {
        return data[index].show
    } else {
        return false
    }
}

// creator: 张旭博，2023-06-08 10:06:56， 预览 - 获取岗位详情
function seePostDetail(selector) {
    var hd = selector.parents("tr").find(".hd").html()
    var rdNoticePostItem = JSON.parse(hd)

    $(".effect_post_postName .item-content").html(getContent(rdNoticePostItem, 'postName', 'postName'))
    $(".effect_post_recruitingNumbers .item-content").html(getContent(rdNoticePostItem, 'recruitingNumbers'))


    $(".otherPostData .item-row").hide()
    $(".otherPostData .item-column").hide()

    var otherAskShow = getShow(rdNoticePostItem, 'otherAsk')
    if (otherAskShow) {
        var otherAsk = getContent(rdNoticePostItem, 'otherAsk')
        if (otherAsk) {
            $(".effect_post_otherAsk").show()
            $(".effect_post_otherAsk .item-content").html(otherAsk)
        }
    }

    var salaryShow = getShow(rdNoticePostItem, 'salary')
    if (salaryShow) {
        var salary = getContent(rdNoticePostItem, 'salary')
        if (salary) {
            $(".effect_post_salary").show()
            $(".effect_post_salary .item-content").html(salary)
        }
    }
    var workRestShow = getShow(rdNoticePostItem, 'workRest')
    if (workRestShow) {
        var beginTime = getContent(rdNoticePostItem, 'workRest', 'beginTime')
        var endTime = getContent(rdNoticePostItem, 'workRest', 'endTime')
        if (beginTime) {
            $(".effect_post_workRest").show()
            $(".effect_post_workRest .item-content").html(beginTime + ' - ' + endTime)
        }
    }
    var addressShow = getShow(rdNoticePostItem, 'address')
    if (addressShow) {
        var address = getContent(rdNoticePostItem, 'address', 'address')
        if (address) {
            $(".effect_post_address").show()
            $(".effect_post_address .item-content").html(address)
        }
    }
    var duty = getContent(rdNoticePostItem, 'postName', 'duty')
    var requirement = getContent(rdNoticePostItem, 'postName', 'requirement')
    var synthesis = getContent(rdNoticePostItem, 'postName', 'synthesis')
    if (duty) {
        $(".effect_post_duty").show()
        $(".effect_post_duty .item-content").html(duty)
    }
    if (requirement) {
        $(".effect_post_requirement").show()
        $(".effect_post_requirement .item-content").html(requirement)
    }
    if (synthesis) {
        $(".effect_post_synthesis").show()
        $(".effect_post_synthesis .item-content").html(synthesis)
    }
    var custom = getContent(rdNoticePostItem, 'custom')
    if (custom) {
        $(".effect_post_custom").show()
        $(".effect_post_custom").html(custom)
    }
}

// creator: 张旭博，2023-05-08 01:35:57， 操作完毕，保存
function endAndSave() {
    var noticeData = $(".tbl_RecruitNotice").data("notice")
    var postNeedList = $(".tbl_postNeedList").data("postneed") || []
    var postNeedListLast = postNeedList.map(item => item.data)

    var deadline = noticeData[noticeData.findIndex(item => item.itemCode === 'deadline')].content
    if (postNeedList.length === 0 || !deadline) {
        layer.msg("操作失败！<br>招聘启事中尚有重要要素无数据！")
        return false
    }
    var json = {
        noticeItemList: noticeData,
        noticePostList: postNeedListLast
    }
    $.ajax({
        url: $.webRoot + '/resume/addRdNotice.do',
        data: {
            json: JSON.stringify(json)
        }
    }).then(res => {
        var data = res.data
        if (data === 1) {
            layer.msg("已成功保存！<br>分享至微信，需在系统手机端操作！")
            clearRecruitData()
            back(function (){
                getRecruitNoticeList()
            })
        } else {
            layer.msg("操作失败！")
        }
    })
}

// creator: 张旭博，2023-02-15 11:50:08， 查看岗位需求列表
function seeInputPostNeedDetail() {
    jumpPage("postNeedList", function (){
        renderPostNeedList()
    })
}

// creator: 张旭博，2023-05-08 01:38:19， 启事 - 新增一行
function addOneNoticeProject() {
    bounce.show($("#addOneNoticeProject"))
    $("#addOneNoticeProject").data('type', 'new')
    $("#addOneNoticeProject .bounce_title").html("增加一行")
    $("#addOneNoticeProject .ty-inputText").val("")
    $("#radio_addNoticeNeed2").prop("checked", true)
}

// creator: 张旭博，2023-05-30 08:57:31， 启事 - 新增一行确定
function sureAddOneNoticeProject() {
    var type = $("#addOneNoticeProject").data('type')

    var itemName = $.trim($("#addOneNoticeProject [name='itemName']").val())
    var unitive = $("#addOneNoticeProject [name='unitive']:checked").val()

    if ($.trim(itemName) === '' || !unitive) {
        layer.msg("请输入或选择对应内容！")
        return false
    }
    var content = $("#addOneNoticeProject [name='content']").val()
    var noticeData = $(".tbl_RecruitNotice").data("notice")
    if (type === 'new') {
        noticeData.push({
            itemCode: 'custom', //(自定义都传这个值)
            unitive: unitive, // 自定义时多传这个字段 true是永久生效，false是单次生效
            itemName: itemName, // 自定义的名
            content: content,// content= 编辑的内容
            show: true,
            required: false
        })
    } else {
        var oldItemName = $("#addOneNoticeProject").data('oldItemName')
        var noticeData = $(".tbl_RecruitNotice").data("notice")
        var noticeItem = noticeData[noticeData.findIndex(item => item.itemName === oldItemName)]
        noticeItem.itemName = itemName
        noticeItem.content = content
        noticeItem.unitive = unitive
    }

    $(".tbl_RecruitNotice").data("notice", noticeData)
    console.log(noticeData)
    renderRecruitNotice('item')
    bounce.cancel()
}

// creator: 张旭博，2023-05-30 04:30:27， 渲染岗位列表
function renderPostNeedList() {
    var postNeedList = $(".tbl_postNeedList").data("postneed")
    var str = ''
    var postAllCount = postNeedList.length
    var planAllCount = 0
    for (var i in postNeedList) {
        var id = postNeedList[i].id
        var postItem = postNeedList[i].data
        var postName = postItem[postItem.findIndex(item => item.itemCode === 'postName')].content.postName
        var recruitingNumbers = postItem[postItem.findIndex(item => item.itemCode === 'recruitingNumbers')].content
        planAllCount += Number(recruitingNumbers)
        str +=  '<tr data-id="'+id+'">' +
                '   <td>'+postName+'</td>' +
                '   <td>'+recruitingNumbers+'人</td>' +
                '   <td>' +
                '       <span class="link-blue" type="btn" name="edit">编辑</span>' +
                '       <span class="link-red" type="btn" name="delete">删除</span>' +
                '   </td>' +
                '   <td class="hd">'+JSON.stringify(postNeedList[i])+'</td>'+
                '</tr>'
    }
    $(".page[page='postNeedList'] .tips").html('本招聘启示中计划招聘 <b class="ty-color-blue"> '+planAllCount+' </b> 人，涉及 <b class="ty-color-blue"> '+postAllCount+' </b> 个岗位，具体如下：')
    $(".tbl_postNeedList tbody").html(str)
}

// creator: 张旭博，2023-05-30 11:20:18， 编辑启事需求
function sureEditNotice() {
    var code = $(".bonceContainer:visible").attr("id").slice(11)
    var noticeData = $(".tbl_RecruitNotice").data("notice")
    var selector = $("#editNotice_" + code)
    var content = ''
    var thisItem = {}

    var validate = true
    if (code === 'custom') {
        var itemName = $.trim(selector.find(".itemName").html())
        thisItem = noticeData[noticeData.findIndex(item => item.itemName === itemName)]
        content = selector.find("[name='content']").val()
        if (content) {
            thisItem.content = content
        } else {
            validate = false
        }
    } else {
        thisItem = noticeData[noticeData.findIndex(item => item.itemCode === code)]
        switch (code) {
            case 'image':
                thisItem.content = selector.find("img").attr("path")
                break
            case 'introduce':
                content = selector.find("[name='introduce']").val()
                if (content) {
                    thisItem.content = content
                } else {
                    validate = false
                }
                break
            case 'workRest':
                var unitive = selector.find("[name='workRestSet']:checked").val()
                var beginTime = selector.find("[name='beginTime']").val()
                var endTime = selector.find("[name='endTime']").val()

                if (unitive && beginTime && endTime) {
                    thisItem.content = {
                        unitive : unitive,
                        beginTime : beginTime,
                        endTime : endTime
                    }
                    var setting = $(".tbl_RecruitNotice").data('setting')
                    if (setting) {
                        setting.workRest = {
                            unitive : setting.workRest.unitive?true: unitive,
                            beginTime : beginTime,
                            endTime : endTime
                        }
                    }
                } else {
                    validate = false
                }

                break
            case 'address':
                var unitive = selector.find("[name='addressSet']:checked").val()
                var address = selector.find("[name='address']").val()

                if (unitive && address) {
                    thisItem.content = {
                        unitive : unitive,
                        address : address
                    }
                    var setting = $(".tbl_RecruitNotice").data('setting')
                    if (setting) {
                        setting.address = {
                            unitive : setting.address.unitive?true: unitive,
                            address : address
                        }
                    }
                } else {
                    validate = false
                }
                break
            case 'description':
                content = selector.find("[name='description']").val()
                if (content) {
                    thisItem.content = content
                } else {
                    validate = false
                }
                break
            case 'resumeChanges':
                content = selector.find("[name='resumeChanges']").val()
                if (content) {
                    thisItem.content = content
                } else {
                    validate = false
                }
                break
            case 'deadline':
                content = selector.find("[name='deadline']").val()
                if (content) {
                    thisItem.content = content
                } else {
                    validate = false
                }
                break
            case 'contact':
                var contacter = selector.find("[name='contacter']").val()
                var telephone = selector.find("[name='telephone']").val()
                var email = selector.find("[name='email']").val()

                if (contacter || telephone || email) {
                    thisItem.content = {
                        contacter: contacter,
                        telephone: telephone,
                        email: email
                    }
                } else {
                    validate = false
                }
                break
        }
    }
    if (validate) {
        renderRecruitNotice('item');
        bounce.cancel();
        console.log('noticeData', $(".tbl_RecruitNotice").data())
    } else {
        layer.msg("请输入或选择对应内容！")
    }
}

// creator: 张旭博，2023-05-29 02:38:18， 渲染启事需求(页面包含岗位统计)
function renderRecruitNotice(type) {
    // 渲染招聘启事各项
    if (type === 'item') {
        var noticeData = $(".tbl_RecruitNotice").data("notice")
        console.log("noticeData", noticeData)
        var str = ''
        for (var i in noticeData) {
            var contentStr = '尚无内容'
            var content = noticeData[i].content
            if (content) {
                switch (noticeData[i].itemCode) {
                    case 'image':
                        contentStr = '已有图片'
                        break
                    case 'introduce':
                        contentStr = '已编辑'
                        break
                    case 'workRest':
                        contentStr = '上班时间'+content.beginTime+'，下班时间' + content.endTime
                        break
                    case 'address':
                        contentStr = content.address
                        break
                    case 'description':
                        contentStr = '已编辑'
                        break
                    case 'resumeChanges':
                        contentStr = '简历提交后，'+content+'分钟内可修改'
                        break
                    case 'deadline':
                        contentStr = content
                        break
                    case 'contact':
                        contentStr = '已编辑'
                        break
                    case 'custom':
                        contentStr = '已编辑'
                        break
                }
            } else {
                switch (noticeData[i].itemCode) {
                    case 'image':
                        contentStr = '已有图片'
                        break
                }
            }

            str +=  ' <tr data-id="'+(noticeData[i].id || '')+'" data-code="'+noticeData[i].itemCode+'" data-need="'+noticeData[i].required+'">' +
                '    <td>'+
                (noticeData[i].required?'<span class="ty-color-red">*</span>':'') +noticeData[i].itemName+'' +
                '    </td>' +
                '    <td>'+contentStr+'</td>' +
                '    <td>'+(noticeData[i].show?'显示':'不显示')+'</td>' +
                '    <td>' +
                '        <span class="link-blue" type="btn" name="edit">编辑项目内容</span>' +
                '        <span class="link-blue" type="btn" name="changeState">改变状态</span>' +
                '    </td>' +
                '</tr>'
        }
        $(".tbl_RecruitNotice tbody").html(str)
        $("#recruitCommonSetDone").prop("checked", false)
        $("#clearContent").prop("checked", false)
    } else if (type === 'count') {
        // 渲染头部统计
        var postNeedList = $(".tbl_postNeedList").data("postneed") || []
        var postAllCount = postNeedList.length
        var planAllCount = 0
        for (var i in postNeedList) {
            var postItem = postNeedList[i].data
            var recruitingNumbers = postItem[postItem.findIndex(item => item.itemCode === 'recruitingNumbers')].content
            planAllCount += Number(recruitingNumbers)
        }
        let tips = `目前，本招聘启示中计划招聘 <b class="ty-color-blue">${planAllCount}</b> 人，涉及 <b class="ty-color-blue">${postAllCount}</b> 个岗位。`
        let set = $(".tbl_RecruitNotice").data("noticeset")
        $(".page[page='createRecruitNotice'] .commonInfoState").html(set?'已设置':'未设置')
        $(".page[page='createRecruitNotice'] .tips").html(tips)
        $("#recruitSetDone").prop("checked", false)
    }
}

// creator: 张旭博，2023-05-31 10:43:26， 更换图片
function changePic() {
    $("#editNotice_image .uploadify-button").click()
}

// creator: 张旭博，2025-07-28 08:12:31， 启示公共部分设置完毕
function recruitCommonSetDone() {

    let radio = $("#recruitCommonSetDone").prop("checked")
    if (!radio) {
        layer.msg('请先勾选！')
    } else {
        let noticeData = $(".tbl_RecruitNotice").data("notice")

        let deadline = noticeData[noticeData.findIndex(item => item.itemCode === 'deadline')].content
        if (!deadline) {
            layer.msg("操作失败！还有必填项尚未设置！")
        } else {
            back(function (){
                $(".tbl_RecruitNotice").data("noticeset", true)
                renderRecruitNotice('count')
            })
        }
    }
}

// ---------------------- 新增岗位需求/查看岗位详情列表-》编辑、删除岗位需求 ------------------------

// creator: 张旭博，2023-02-15 09:48:24， 新增岗位需求
function newPostNeed() {
    jumpPage("editPostNeed", function (){
        $(".tbl_PostNeed").data("type", 'new')
        // 获取项目列表
        $.ajax({
            url: $.webRoot + '/resume/getNoticePostItemList.do',
            success: function (res) {
                var data = res.data
                // 区分出来上传和显示的值
                data.map(item => {
                    item.content = ''
                })
                $(".tbl_PostNeed").data("origin", JSON.parse(JSON.stringify(data)))
                $(".tbl_PostNeed").data("post", JSON.parse(JSON.stringify(data)))
                renderPostNeed()
            }
        })
    })
}

// creator: 张旭博，2023-05-29 02:38:18， 渲染岗位需求
function renderPostNeed() {
    var data = $(".tbl_PostNeed").data("post")
    var str = ''
    for (var i in data) {
        var contentStr = '尚无内容'
        var content = data[i].content
        if (content) {
            switch (data[i].itemCode) {
                case 'postName':
                    contentStr = content.postName
                    break
                case 'recruitingNumbers':
                    contentStr = content + '人'
                    break
                case 'otherAsk':
                    contentStr = '已编辑'
                    break
                case 'workRest':
                    contentStr = '上班时间'+content.beginTime+'，下班时间' + content.endTime
                    break
                case 'address':
                    contentStr = content.address
                    break
                case 'salary':
                    contentStr = content
                    break
                case 'custom':
                    contentStr = '已编辑'
                    break
            }
        }
        str +=  ' <tr data-code="'+data[i].itemCode+'" data-sys="'+data[i].system+'" data-need="'+data[i].required+'" data-unitive="'+data[i].unitive+'">' +
                '    <td>'+
                    (data[i].required?'<span class="ty-color-red">*</span>':'') +data[i].itemName+'' +
                '    </td>' +
                '    <td>'+contentStr+'</td>' +
                '    <td>'+(data[i].show?'显示':'不显示')+'</td>' +
                '    <td>' +
                '        <span class="link-blue" type="btn" name="edit">编辑项目内容</span>' +
                '        <span class="link-blue" type="btn" name="changeState">改变状态</span>' +
                '    </td>' +
                '</tr>'
    }
    $(".tbl_PostNeed tbody").html(str)
}

// creator: 张旭博，2023-05-08 01:38:19， 新增岗位 - 新增一行
function addOnePostProject() {
    bounce.show($("#addOnePostProject"))
    $("#addOnePostProject").data('type', 'new')
    $("#addOnePostProject .bounce_title").html("增加一行")
    $("#addOnePostProject .ty-inputText").val("")
    $("#radio_addPostNeed2").prop("checked", true)
}

// creator: 张旭博，2023-05-30 08:57:31， 新增岗位 - 新增一行确定
function sureAddOnePostProject() {
    var type = $("#addOnePostProject").data('type')

    var itemName = $.trim($("#addOnePostProject [name='itemName']").val())
    var unitive = $("#addOnePostProject [name='unitive']:checked").val()

    if ($.trim(itemName) === '' || !unitive) {
        layer.msg("请输入或选择对应内容！")
        return false
    }
    var content = $("#addOnePostProject [name='content']").val()
    var postData = $(".tbl_PostNeed").data("post")
    if (type === 'new') {
        postData.push({
            itemCode: 'custom', //(自定义都传这个值)
            unitive: unitive, // 自定义时多传这个字段 true是永久生效，false是单次生效
            itemName: itemName, // 自定义的名
            content: content,// content= 编辑的内容
            show: true,
            required: false
        })
    } else {
        var oldItemName = $("#addOnePostProject").data('oldItemName')
        var postItem = postData[postData.findIndex(item => item.itemName === oldItemName)]
        postItem.itemName = itemName
        postItem.content = content
        postItem.unitive = unitive
    }

    $(".tbl_PostNeed").data("post", postData)
    console.log(postData)
    renderPostNeed()
    bounce.cancel()
}

// creator: 张旭博，2023-05-26 03:10:39， 编辑项目内容 - 确定
function sureEditPost() {
    var code = $(".bonceContainer:visible").attr("id").slice(9)
    var postData = $(".tbl_PostNeed").data("post")
    var selector = $("#editPost_" + code)
    var thisItem = {}
    var content = ''
    var validate = true

    if (code === 'custom') {
        var itemName = $.trim(selector.find(".itemName").html())
        thisItem = postData[postData.findIndex(item => item.itemName === itemName)]
        content = selector.find("[name='content']").val()
        if (content) {
            thisItem.content = content
        } else {
            validate = false
        }
    } else {
        thisItem = postData[postData.findIndex(item => item.itemCode === code)]
        switch (code) {
            case 'postName':
                var postId = selector.find("[name='postName']").val()
                var type = $(".tbl_PostNeed").data("type")
                if (type === 'new') {
                    var postNeedList = $(".tbl_postNeedList").data("postneed")
                    var postNeedArr = postNeedList.map(item => item.data)
                    var hasRepeatName = postNeedArr.findIndex(item => {
                        var thisItem = item.filter(it => it.itemCode === 'postName')
                        if (thisItem[0].content) {
                            return thisItem[0].content.postId === postId
                        }
                    })
                    if (hasRepeatName !== -1) {
                        layer.msg("岗位重复，请选择其他岗位！")
                        return false
                    }
                }
                if (postId) {
                    var postName = selector.find("[name='postName'] option:selected").html()
                    thisItem.content = {
                        postId : postId,
                        postName : postName
                    }
                    var postInfo = $(".postInfo").data("postinfo")
                    thisItem.otherData = postInfo
                } else {
                    validate = false
                }
                break
            case 'recruitingNumbers':
                content = selector.find("[name='recruitingNumbers']").val()
                if (content) {
                    thisItem.content = content
                } else {
                    validate = false
                }
                break
            case 'otherAsk':
                var age = selector.find("[name='age']").val()
                var gender = selector.find("[name='gender']").val()
                var education = selector.find("[name='education']").val()
                var residence = selector.find("[name='residence']").val()
                var experiences = selector.find("[name='experiences']").val()
                var characters = selector.find("[name='characters']").val()
                if (age || gender || education || residence || experiences || characters) {
                    thisItem.content = {
                        age : age,
                        ageThreshold : selector.find("[name='ageThreshold']").val(),
                        gender : gender,
                        education : education,
                        educationThreshold : selector.find("[name='educationThreshold']").val(),
                        residence : residence,
                        experiences : experiences,
                        characters : characters
                    }
                } else {
                    validate = false
                }
                break
            case 'workRest':
                var unitive = selector.find("[name='workRestSet']:checked").val()
                var beginTime = selector.find("[name='beginTime']").val()
                var endTime = selector.find("[name='endTime']").val()

                if (unitive && beginTime && endTime) {
                    thisItem.content = {
                        unitive : unitive,
                        beginTime : beginTime,
                        endTime : endTime
                    }
                    // 岗位和启事相通设置
                    var setting = $(".tbl_RecruitNotice").data('setting')
                    console.log('setting', setting)
                    if (setting) {
                        setting.workRest = {
                            unitive : setting.workRest.unitive?true: unitive,
                            beginTime : beginTime,
                            endTime : endTime
                        }
                    }
                } else {
                    validate = false
                }
                break
            case 'address':
                var unitive = selector.find("[name='addressSet']:checked").val()
                var address = selector.find("[name='address']").val()

                if (unitive && address) {
                    thisItem.content = {
                        unitive : unitive,
                        address : address
                    }
                    // 岗位和启事相通设置
                    var setting = $(".tbl_RecruitNotice").data('setting')
                    console.log('setting', setting)
                    if (setting) {
                        setting.address = {
                            unitive : setting.address.unitive?true: unitive,
                            address : address
                        }
                    }
                } else {
                    validate = false
                }
                break
            case 'salary':
                content = selector.find("[name='salary']").val()
                if (content) {
                    thisItem.content = content
                } else {
                    validate = false
                }
                break
        }
    }
    if (validate) {
        renderPostNeed();
        bounce.cancel();
        console.log('postData', postData)
    } else {
        layer.msg("请输入或选择对应内容！")
    }
}

// creator: 张旭博，2023-05-30 02:02:26， 保存岗位信息
function savePostData() {
    var postData = $(".tbl_PostNeed").data("post");
    var postName = postData[postData.findIndex(item => item.itemCode === 'postName')].content
    var recruitingNumbers = postData[postData.findIndex(item => item.itemCode === 'recruitingNumbers')].content
    if (!postName || !recruitingNumbers) {
        layer.msg("请录入必填项！")
        return false
    }
    var postNeedList = $(".tbl_postNeedList").data("postneed") || []
    var type = $(".tbl_PostNeed").data("type")
    if (type === 'change') {
        var id = $(".tbl_PostNeed").data("id");
        postNeedList[postNeedList.findIndex(item => item.id === id)].data = postData
    } else {
        postNeedList.push({
            id: Math.floor(Math.random() * 1000000),
            data: postData
        })
    }
    $(".tbl_postNeedList").data("postneed", postNeedList)
    back(function (form, to){
        var type = $(".tbl_PostNeed").data("type");
        if (type === 'change') {
            renderPostNeedList()
        } else {
            renderRecruitNotice('count')
        }
    })
}

// creator：张旭博，2017-12-05 16:09:27，新增岗位
function addNewPosition(){
    bounce_Fixed.show($("#addPost"));
    var dutyList = ['']
    var requirementList = ['']
    $("#addPost input").val("")
    $("#addPost .input_show .search").prevAll().remove()
    $('#addPost .textMax').each(function (){
        var curLength = $(this).parents(".item").find(".autoFill").val().length
        var max = $(this).parents(".item").find(".autoFill").attr("max")
        $(this).text( curLength +'/' + max );
    })

    $("#addPost").data("duty", dutyList)
    $("#addPost").data("requirement", requirementList)
    renderInput($("#addPost"),'duty', dutyList)
    renderInput($("#addPost"), 'requirement', requirementList)
    $.ajax($.webRoot + '/post/getSynthesizes.do').then(res => {
        var list = res.data
        $("#addPost .input_choose").data('list', list)
        var str = updateSynthesizeList(list)
        $("#addPost .input_choose_list").html(str)
    })
}

// creator: 张旭博，2023-05-10 11:45:44， 综合 - 增加
function addSynthesizeBtn(selector) {
    var bounce = selector.parents(".bonceContainer")
    bounce_Fixed2.show($("#addSynthesize"))
    $("#addSynthesize input").val("")
    $("#addSynthesize .sureBtn").unbind().on("click", function (){
        var name = $("#addSynthesize input").val()
        if ($.trim(name) === '') {
            layer.msg("请输入内容！")
            return false
        }
        $.ajax({
            url: $.webRoot + '/post/addSynthesize.do',
            data: {
                name: name
            }
        }).then(res => {
            var data = res.data
            if (data === 1) {
                layer.msg("新增成功！")
                bounce_Fixed2.cancel()
                $.ajax($.webRoot + '/post/getSynthesizes.do').then(res => {
                    var list = res.data
                    var str = updateSynthesizeList(list)
                    bounce.find(".input_choose_list").html(str)
                    bounce.find(".input_choose").data('list', list)
                })
            }
        })
    })
}

// creator: 张旭博，2019-07-02 12:07:26，更新综合列表
function updateSynthesizeList(list) {
    var str = ''
    for (var i in list) {
        str += '<div class="input_choose_item ' + (list[i].select ? 'selected' : '') + '" data-id="' + list[i].id + '">' +
            '    <div class="input_choose_item_info">' +
            '        <div class="input_choose_item_name text-center">' + list[i].itemName + '</div>' +
            '    </div>' +
            '</div>'
    }
    return str
}

// creator: 张旭博，2023-05-24 09:11:57, 增加一行
function addRow(selector, name) {
    var list = []
    selector.parents(".bonceContainer").find("." +name + "Panel .item-row").each(function (){
        var content = $(this).find("input").val()
        list.push(content)
    })
    list.push('')
    renderInput(selector.parents(".bonceContainer"), name, list)
    selector.parents(".bonceContainer").data(name, list)
}

function delRow(selector) {
    var name = selector.next("input").attr("name")
    var list = []
    var bounce = selector.parents(".bonceContainer")
    selector.parents(".item-row").remove()
    bounce.find("." +name + "Panel .item-row").each(function (){
        var content = $(this).find("input").val()
        list.push(content)
    })
    renderInput(bounce, name, list)
    bounce.data(name, list)
}

// creator: 张旭博，2019-07-02 12:07:26，渲染新增岗位的输入框
function renderInput(selector, name, inputList) {
    var str = ''
    if (inputList.length > 1) {
        for (var i=0; i<inputList.length; i++) {
            str +=  '<div class="item-row">' +
                '    <label class="input_num">'+(i+1)+'</label>' +
                (i>0?'<i class="fa fa-minus-circle input_del" onclick="delRow($(this))"></i>':'') +
                '    <input class="kj-input autoFill" max="30" name="'+name+'" value="'+inputList[i]+'"/>' +
                '    <div class="textMax text-right">'+inputList[i].length+'/30</div>' +
                '</div>'
        }
    } else {
        for (var i=0; i<inputList.length; i++) {
            str +=  '<div class="item-row">' +
                '    <input class="kj-input autoFill" max="30" name="'+name+'" value="'+inputList[i]+'"/>' +
                '    <div class="textMax text-right">'+inputList[i].length+'/30</div>' +
                '</div>'
        }
    }
    selector.find("." +name + "Panel").html(str)
}

// updater：姚宗涛，2018-2-11 10:37:58，新增岗位-确认
function sureAddPost(){
    var name = $("#addPost [name='name']").val();
    if ($.trim(name) === '') {
        layer.msg("请填写必填项！")
        return false
    }
    var dutyArr = [], requirementArr = [], synthesisArr = []
    $("#addPost .dutyPanel .item-row").each(function (){
        var content = $(this).find("input").val()
        dutyArr.push(content)
    })
    $("#addPost .requirementPanel .item-row").each(function (){
        var content = $(this).find("input").val()
        requirementArr.push(content)
    })
    $("#addPost .synthesisPanel .input_show .selected_item").each(function(){
        var content= $(this).find('.selected_name').html();
        synthesisArr.push(content);
    });
    $.ajax({
        url: $.webRoot + '/post/addPost.do',
        data:{
            name: name,
            dutys: JSON.stringify(dutyArr),
            requirements: JSON.stringify(requirementArr),
            synthesis: JSON.stringify(synthesisArr)
        }
    }).then(res => {
        var data = res.data
        if (data === 1) {
            layer.msg("操作成功")
            bounce_Fixed.cancel()
            $.ajax($.webRoot + '/post/getPostList.do')
                .then(res => {
                    var data = res.data
                    $("#editPost_postName").data("post", data)
                    renderPostOption(data)
                    $(".postInfo").hide()
                })
        } else {
            layer.msg("操作失败")
        }
    })
}


// 生成时间选项
function getTimeOption(timeArr) {
    // timeArr 示例 ['00:00-09:00', '10:00-11:00'] 需要显示的区间（剩下的禁用）

    if (!timeArr) {
        timeArr = ['00:00-24:00']
    }
    var data = []
    var n = 23
    for (var i = 0; i <= n; i++) {
        var textMM = i < 10 ? '0' + i : i;
        data.push({value: textMM + ':' + '00', text: textMM + ':' + '00', disabled: true});
        if (i !== 24) {
            data.push({value: textMM + ':' + '30', text: textMM + ':' + '30', disabled: true});
        }
    }
    timeArr.forEach((elem, index) => {
        console.log(elem, index);
        var start = elem.split("-")[0]
        var end = elem.split("-")[1]
        if (end === '24:00') {
            end = '23:30'
        }

        var startNum = chargeToNum(start)
        var endNum = chargeToNum(end)
        for (var i = startNum; i <= endNum; i++) {
            data[i].disabled = false
        }
    });

    var str = ''

    for (var k in data) {
        var disabledStr = data[k].disabled ? 'disabled' : ''
        str += '<option value="' + data[k].value + '" '+disabledStr+'>' + data[k].text + '</option>'
    }
    return str;

}

function chargeToNum(time) {
    var timeMM = Number(time.split(":")[0]);
    var timeSS = time.split(":")[1];
    timeSS = timeSS === '30'?  0.5: 0
    return Number((- ( -timeMM - timeSS)) * 2 )
}

// ---------------------- 其他方法 ------------------------

// creator: 张旭博，2023-02-13 03:41:26， 招聘启事各项 - 初始化
function initNoticeDialog(code) {
    var noticeData = $(".tbl_RecruitNotice").data("notice")
    var dialogName = '#editNotice_' + code
    var noticeItem = noticeData[noticeData.findIndex(item => item.itemCode === code)]
    var content = noticeItem.content
    switch (code) {
        case 'image':
            if (!content) {content = $.webRoot + '/assets/images/recruit_default.png'} else {content = $.uploadUrl + content}
            $("#editNotice_image .imgShow").html('<img src="'+content+'" alt="默认图片" path=""/>')
            $('.imgUpload').html("")
            $('.imgUpload').Huploadify({
                auto: true,
                fileTypeExts: '*.png;*.jpg;*.gif;*.jpeg;',
                fileSizeLimit: 4096,  // 300M = ( 300 * 1024 * 1024 ) B
                showUploadedSize: true,
                removeTimeout: 99999999,
                buttonText: "上传图片",
                formData:{
                    module: '招聘启事',
                    userId: sphdSocket.user.userID
                },
                uploader:$.webRoot+"/uploads/uploadfyByFile.do",
                onSelect: function () {
                    $(".imgUpload .uploadify-button").hide();
                    $("#editNotice_image .sureBtn").prop("disabled", true)
                },
                onUploadError: function () {
                    layer.msg("上传失败！")
                },
                onUploadSuccess: function (file, json) {
                    var data = JSON.parse(json),
                        name = data.filename,
                        fileName = file.name
                    var str = '<img src="'+$.uploadUrl + name+'" alt="'+fileName+'" path="'+name+'">'
                    $("#editNotice_image .imgShow").html(str);
                    $("#editNotice_image .sureBtn").prop("disabled", false)
                },
                onQueueComplete:function() {
                    $(".imgUpload .uploadify-queue").html('')
                }
            })
            break
        case 'introduce':
        case 'description':
            $(dialogName).find("textarea").val(content)
            break
        case 'workRest':

            if (content) {
                var setting = $(".tbl_RecruitNotice").data('setting') // 最后一次设置的有最终显示权
                var unitive = setting.workRest.unitive
                $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                $(dialogName).find("[name='beginTime']").val(content.beginTime)
                $(dialogName).find("[name='endTime']").val(content.endTime)
            } else {
                var setting = $(".tbl_RecruitNotice").data('setting') // 最后一次设置的有最终显示权
                var unitive = setting.workRest.unitive
                var beginTime = setting.workRest.beginTime
                var endTime = setting.workRest.endTime
                if (unitive === null) {
                    $(dialogName).find("input:radio").prop("checked", false)
                    $(dialogName).find("select").val('')
                } else if (unitive === 'true') {
                    $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                    $(dialogName).find("[name='beginTime']").val(beginTime)
                    $(dialogName).find("[name='endTime']").val(endTime)
                } else {
                    $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                    $(dialogName).find("select").val('')
                }
            }
            break
        case 'address':
            if (content) {
                var setting = $(".tbl_RecruitNotice").data('setting') // 最后一次设置的有最终显示权
                var unitive = setting.address.unitive
                $(dialogName).find("input:radio[value='"+ unitive +"']").prop("checked", true)
                $(dialogName).find("[name='address']").val(content.address)
            } else {
                var setting = $(".tbl_RecruitNotice").data('setting') // 最后一次设置的有最终显示权
                var unitive = setting.address.unitive
                var address = setting.address.address
                if (unitive === null) {
                    $(dialogName).find("input:radio").prop("checked", false)
                    $(dialogName).find("[name='address']").val('')
                } else if (unitive === 'true') {
                    $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                    $(dialogName).find("[name='address']").val(address)
                } else {
                    $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                    $(dialogName).find("[name='address']").val('')
                }
            }
            break
        case 'resumeChanges':
            $(dialogName).find("select").val(content || "10")
            break
        case 'deadline':
            $(dialogName).find("input").val(content)
            break
        case 'contact':
            if (content) {
                $(dialogName).find("[name='contacter']").val(content.contacter)
                $(dialogName).find("[name='telephone']").val(content.telephone)
                $(dialogName).find("[name='email']").val(content.email)
            } else {
                $(dialogName).find("input").val("")
            }
            break
    }
}

// creator: 张旭博，2023-06-02 02:34:09， 岗位新增各项 - 初始化
function initPostDialog(code) {
    var postData = $(".tbl_PostNeed").data("post");
    var dialogName = '#editPost_' + code
    var postItem = postData[postData.findIndex(item => item.itemCode === code)]
    var content = postItem.content

    switch (code) {
        case 'postName':
            var postData = $("#editPost_postName").data("post")
            if (postData) {
                renderPostOption(postData)
            } else {
                $.ajax($.webRoot + '/post/getPostList.do')
                    .then(res => {
                        var data = res.data
                        $("#editPost_postName").data("post", data)
                        renderPostOption(data)
                        $(".postInfo").hide()
                    })
            }
            if (content) {
                $(dialogName).find("[name='postName']").val(content.postId).change()
                $(dialogName).find(".postInfo").show()
            } else {
                $(dialogName).find("input:radio").prop("checked", false)
                $(dialogName).find(".postInfo").hide()
            }
            break
        case 'recruitingNumbers':
        case 'salary':
            $(dialogName).find("input").val(content)
            break
        case 'workRest':
            if (content) {
                var setting = $(".tbl_RecruitNotice").data('setting') // 最后一次设置的有最终显示权
                var unitive = setting.workRest.unitive
                $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                $(dialogName).find("[name='beginTime']").val(content.beginTime)
                $(dialogName).find("[name='endTime']").val(content.endTime)
            } else {
                var setting = $(".tbl_RecruitNotice").data('setting') // 最后一次设置的有最终显示权
                var unitive = setting.workRest.unitive
                var beginTime = setting.workRest.beginTime
                var endTime = setting.workRest.endTime
                if (unitive === null) {
                    $(dialogName).find("input:radio").prop("checked", false)
                    $(dialogName).find("select").val('')
                } else if (unitive === 'true' || unitive === true) {
                    $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                    $(dialogName).find("[name='beginTime']").val(beginTime)
                    $(dialogName).find("[name='endTime']").val(endTime)
                } else {
                    $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                    $(dialogName).find("select").val('')
                }
            }
            break
        case 'address':
            if (content) {
                var setting = $(".tbl_RecruitNotice").data('setting') // 最后一次设置的有最终显示权
                var unitive = setting.address.unitive
                $(dialogName).find("input:radio[value='"+ unitive +"']").prop("checked", true)
                $(dialogName).find("[name='address']").val(content.address)
            } else {
                var setting = $(".tbl_RecruitNotice").data('setting') // 最后一次设置的有最终显示权
                var unitive = setting.address.unitive
                var address = setting.address.address
                if (unitive === null) {
                    $(dialogName).find("input:radio").prop("checked", false)
                    $(dialogName).find("[name='address']").val('')
                } else if (unitive === 'true' || unitive === true) {
                    $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                    $(dialogName).find("[name='address']").val(address)
                } else {
                    $(dialogName).find("input:radio[value='"+unitive +"']").prop("checked", true)
                    $(dialogName).find("[name='address']").val('')
                }
            }
            break
        case 'otherAsk':
            if (content) {
                $(dialogName).find("[name='age']").val(content.age)
                $(dialogName).find("[name='ageThreshold']").val(content.ageThreshold)
                $(dialogName).find("[name='gender']").val(content.gender)
                $(dialogName).find("[name='education']").val(content.education)
                $(dialogName).find("[name='educationThreshold']").val(content.educationThreshold)
                $(dialogName).find("[name='residence']").val(content.residence)
                $(dialogName).find("[name='experiences']").val(content.experiences)
                $(dialogName).find("[name='characters']").val(content.characters)
            } else {
                $(dialogName).find("[name='age']").val('')
                $(dialogName).find("[name='ageThreshold']").val('')
                $(dialogName).find("[name='gender']").val('')
                $(dialogName).find("[name='education']").val('')
                $(dialogName).find("[name='educationThreshold']").val('2')
                $(dialogName).find("[name='residence']").val('')
                $(dialogName).find("[name='experiences']").val('')
                $(dialogName).find("[name='characters']").val('')
            }
            break
    }
}

function getRecruitCount (by) {
    var id = $("#recruitManage").data("id")
    var url = '', tips = ''
    switch (by) {
        case 'resume':
            url = '/resume/getRdResumeList.do'
            break
        case 'interview':
            url = '/resume/getNotificationList.do'
            break
        case 'interviewed':
            url = '/resume/getInterviewList.do'
            break
        case 'employed':
            url = '/resume/getEntryList.do'
            break
    }
    $.ajax({
        url: $.webRoot + url,
        data: {
            id: id
        },
        success: function (res) {
            var data = res.data
            var str = ''
            for (var i in data) {
                str +=  '<tr>' +
                    '    <td>'+data[i].userName+'</td>' +
                    '    <td>'+chargeGender(data[i].gender)+'</td>' +
                    '    <td>'+(data[i].age||'--')+'</td>' +
                    '    <td>'+data[i].postName+'</td>' +
                    '    <td>'+moment(data[i].createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>' +
                    '    <td>'+data[i].mobile+'</td>' +
                    '    <td>已通知'+(data[i].notificationNum || 0)+'次</td>' +
                    '    <td>面试官'+(data[i].interviewerNum || 0)+'名<br>同意入职'+(data[i].passNum || 0)+'名</td>' +
                    '    <td>'+chargeResult(data[i].result)+'</td>' +
                    '</tr>'
            }
            switch (by) {
                case 'resume':
                    tips = '本次招聘已收到简历共'+data.length+'份，具体如下'
                    break
                case 'interview':
                    tips = '本次招聘已通知面试共'+data.length+'人，具体如下'
                    break
                case 'interviewed':
                    tips = '本次招聘已面试共'+data.length+'人，具体如下'
                    break
                case 'employed':
                    tips = '本次招聘已入职共'+data.length+'人，具体如下'
                    break
            }
            $(".page[page='recruitCount'] .tip").html(tips)
            $(".page[page='recruitCount'] tbody").html(str)
        }
    })
}

function chargeResult(result) {
    if (result === 1) {
        return '已入职'
    } else if (result === 0) {
        return '已终止面试'
    } else {
        return '尚未最终决定'
    }
}

function chargeGender(gender) {
    if (gender === '1') {
        return '男'
    } else if (gender === '0') {
        return '女'
    } else {
        return '--'
    }
}

function chargeNoticeGender(gender) {
    if (gender === '1') {
        return '女'
    } else if (gender === '0') {
        return '不限'
    } else if (gender === '2') {
        return '男'
    }
}

// creator: 张旭博，2023-05-08 10:54:26， 截止日期履历
function deadlineResume(selector){
    bounce_Fixed.show($("#deadlineResume"))
    $("#deadlineResume tbody").html("")
    var id = $("#recruitManage").data("id")
    var deadline = selector.siblings(".deadline").html()
    $("#deadlineResume .deadline").html(deadline)
    $.ajax({
        url: $.webRoot + '/resume/getDeaNoticeHistories.do',
        data: {
            id: id
        }
    }).then(res => {
        var data = res.data
        var str = ''
        for (var i in data) {
            str +=  '<tr>' +
                    '   <td>'+moment(data[i].endTime).format("YYYY-MM-DD")+'</td>' +
                    '   <td>'+data[i].createName + ' ' + moment(data[i].createDate).format("YYYY-MM-DD HH:mm:ss")+'</td>' +
                    '</tr>'
        }
        $("#deadlineResume tbody").html(str)
    })
}

function renderPostOption(postData) {
    var str = '<option value="">------请选择------</option>';
    for(var j=0;j<postData.length;j++){
        str +=  '<option value="'+postData[j].id+'">'+postData[j].name+'</option>'
    }
    $("#editPost_postName select").html(str);
}

// creator: 张旭博，2023-02-24 15:39:49，返回上一步
function back(callback) {
    var pathArr = $("#home").data("pathArr")
    var from = pathArr[pathArr.length - 1]
    var to = pathArr[pathArr.length - 2]
    if (to === 'main') {
        $(".backBtn").hide()
    }
    if (callback) { callback(from, to)} else {
        if (to === 'createRecruitNotice') {
            renderRecruitNotice('count')
        }
        if (to === 'recruitNoticeManage' && from === 'createRecruitNotice') {
            bounce.show($("#errorTip"))
            $("#errorTip .tipWord").html("操作后数据将会被清空，确定返回吗？")
            $("#errorTip .sureBtn").unbind().on("click", function (){
                bounce.cancel()
                pathArr.pop()
                $("#home").data("pathArr", pathArr)
                isShowNav()
                $(".page[page='"+to+"']").show().siblings(".page").hide()
                if (callback) { callback(from, to)}
            })
            return false
        }
    }
    pathArr.pop()
    $("#home").data("pathArr", pathArr)
    isShowNav()
    $(".page[page='"+to+"']").show().siblings(".page").hide()

}

// creator: 张旭博，2023-02-24 15:40:51，回到主页
function backToMain() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr = [pathArr[0]]
    $("#home").data("pathArr", pathArr)
    isShowNav()
    $(".page[page='"+pathArr[0]+"']").show().siblings(".page").hide()
}

// creator: 张旭博，2023-02-09 04:56:46， 跳转及退回
function jumpPage(page, callback) {
    var arr = $("#home").data("pathArr") || []
    arr.push(page)
    $("#home").data("pathArr", arr)
    isShowNav()
    $(".page[page='"+page+"']").show().siblings(".page").hide()
    if (page !== 'main') {
        $(".backBtn").show()
    }
    console.log('path', JSON.stringify(arr))
    if (callback) { callback()}
}

// creator: 张旭博，2023-02-24 15:40:37，是否禁用导航
function isShowNav() {
    var pathArr = $("#home").data("pathArr")
    if (pathArr.length > 1) {
        $(".ty-page-header").show()
    } else {
        $(".ty-page-header").hide()
    }
}

// creator: 张旭博，2018-04-27 08:44:53，获取二维码地址
function getQRCodeUrl() {
    $.ajax({
        url : "../recruit/getQRLink.do" ,
        beforeSend:function(){ },
        success:function( data ){
            var url = data["data"];
            $("#qrCode_small").qrcode(
                {
                    render : "canvas",    //设置渲染方式，有table和canvas，使用canvas方式渲染性能相对来说比较好
                    text : url,    //扫描二维码后显示的内容,可以直接填一个网址，扫描二维码后自动跳向该链接
                    width : "150",            // //二维码的宽度
                    height : "150",              //二维码的高度
                    background : "#ffffff",       //二维码的后景色
                    foreground : "#000000"        //二维码的前景色
                }
            );
            $("#qrCode").qrcode(
                {
                    render : "canvas",    //设置渲染方式，有table和canvas，使用canvas方式渲染性能相对来说比较好
                    text : url,    //扫描二维码后显示的内容,可以直接填一个网址，扫描二维码后自动跳向该链接
                    width : "400",            // //二维码的宽度
                    height : "400",              //二维码的高度
                    background : "#ffffff",       //二维码的后景色
                    foreground : "#000000"        //二维码的前景色
                }
            );
        },
        complete:function () {}
    })
}

// creator: 张旭博，2018-04-19 16:28:49，获取招聘人员列表
function getOfferList(currentPageNo,pageSize,offerStatus,timeType,selector){
    var data = {
        "offerStatus" : offerStatus,
        "currentPageNo" : currentPageNo,
        "pageSize" : pageSize,
        "type" :  timeType
    };
    // 本年和自定义有特殊的展示方式
    if(timeType > 1){
        //本年等隐藏
        $(".timer").hide()
        //返回按钮
        $(".back").show()
        $(".queryHistory").children(":last").hide();
    }
    if(timeType === 3){
        var beginDate = $("#queryBeginTime").val();
        var endDate = $("#queryEndTime").val();
        data["beginDate"] = beginDate;
        data["endDate"] = endDate;
    }else if(timeType === 4){
        data["beginDate"] = selector.siblings(".beginDate").html();
        data["endDate"] = selector.siblings(".endDate").html();
    }
    $.ajax({
        url : "../recruit/offerList.do" ,
        data : data,
        success:function( data ){
            var status  = data["status"],   //状态 1-成功
                pageInfo= data["pageInfo"], //分页信息
                offerList = data["offerList"],  //应聘列表
                listButton = data["listButton"];    //判断该用户是否拥有职工档案模块
            if(status === 1){
                //设置分页
                var cur     = pageInfo["currentPageNo"],
                    total   = pageInfo["totalPage"],
                    jsonStr = JSON.stringify({
                        "offerStatus" : offerStatus,
                        "timeType"    : timeType
                    }) ;
                setPage( $("#ye-recruit"), cur, total, "recruit",jsonStr) ;

                //设置列表
                var offerListStr = '';
                if(timeType === 1) {
                    if (typeof (offerList) !== "undefined" && offerList.length > 0) {
                        for (var i = 0; i < offerList.length; i++) {
                            var id = offerList[i].userID,
                                userName = offerList[i].userName,
                                gender = offerList[i].gender,
                                degree = offerList[i].degree,
                                offerPost = offerList[i].offerPost,
                                offerTime = offerList[i].offerTime,
                                mobile = offerList[i].mobile,
                                onDutyDate = offerList[i].onDutyDate,
                                handleTime = offerList[i].handleTime,
                                createName = offerList[i].createName;
                            gender === "1" ? gender = '男' : gender = '女';
                            degree = chargeDegree(degree);

                            offerListStr += '<tr id="' + id + '">' +
                                '<td>' + userName + '</td>' +    //应聘姓名
                                '<td>' + gender + '</td>' +      //性别
                                '<td>' + degree + '</td>' +      //文化程度
                                '<td>' + offerPost + '</td>' +   //应聘职位
                                '<td>' + offerTime + '</td>' +   //应聘时间
                                '<td>' + mobile + '</td>';      //手机号
                            if (offerStatus === 1) {
                                offerListStr += '<td>' +    //拥有职工档案才拥有查看个人简历按钮
                                                (listButton === 0?'':'<span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),0)">查看</span>') +
                                                '</td>' +
                                                '<td>' +
                                                '    <span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),1)">面试意见</span>' +
                                                ((listButton === 0 || chargeRole("超管")) ?'':    //只有拥有职工档案并且不是超管才有入职和面试未通过按钮
                                                '    <span class="ty-color-blue" onclick="entryBtn($(this))">入职</span>' +
                                                '    <span class="ty-color-blue" onclick="offerNotPassedBtn($(this))">面试未通过</span>') +
                                                '</td>';
                            } else if (offerStatus === 2) {
                                offerListStr += '<td>' + onDutyDate + '</td>' +    //入职时间
                                                '<td>' + createName + '</td>' +    //操作者
                                                '<td>' + handleTime + '</td>' +   //处理时间
                                                '<td>' +
                                                (listButton === 0?'':'<span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),0)">查看</span>') +
                                                '</td>' +
                                                '<td>' +
                                                '    <span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),1)">查看</span>' +
                                                '</td>';
                            } else if (offerStatus === 3) {
                                offerListStr += '<td>' + createName + '</td>' +    //操作者
                                                '<td>' + handleTime + '</td>'+    //处理时间
                                                '<td>' +
                                                (listButton === 0?'':'<span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),0)">查看</span>') +
                                                '</td>' +
                                                '<td>' +
                                                '    <span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),1)">查看</span>' +
                                                '</td>';
                            }
                        }
                    }
                    $(".main .tblContainer").eq(offerStatus - 1).find("tbody").html(offerListStr);
                }else{
                    var monthList = data["monthList"];
                    var yearList  = data["yearList"];
                    var offerList = data["offerList"];
                    var workNumName = ''
                    offerStatus === 2? workNumName = '入职人数':workNumName = '已拒绝人数'
                    if(monthList){
                        offerListStr =  '<div class="historyItem">' +
                                            '<table class="ty-table ty-table-control">' +
                                            '    <thead>' +
                                            '    <tr>' +
                                            '        <td class="timeName">月份</td>' +
                                            '        <td>'+workNumName+'</td>' +
                                            '        <td>操作</td>' +
                                            '    </tr>' +
                                            '    </thead>' +
                                            '    <tbody>';
                        for(var i = 0;i<monthList.length;i++){
                            if(monthList[i].number !== 0){
                                offerListStr +=         '<tr>'+
                                                            '<td>' + (monthList[i].month + 1) + '</td>'+
                                                            '<td>' + monthList[i].number + '</td>'+
                                                            '<td>' +
                                                            '    <span class="ty-color-blue" onclick="getOfferList(1,20,'+offerStatus+',4,$(this))">查看</span>' +
                                                            '    <div class="hd beginDate">'+monthList[i].beginDate+'</div>' +
                                                            '    <div class="hd endDate">'+monthList[i].endDate+'</div>' +
                                                            '</td>'+
                                                        '</tr>';
                            }
                        }
                        offerListStr +=         '</tbody>' +
                                            '</table>'+
                                        '</div>';
                    }else if(yearList){
                        offerListStr =  '<div class="historyItem">' +
                                            '<table class="ty-table ty-table-control">' +
                                            '    <thead>' +
                                            '    <tr>' +
                                            '        <td class="timeName">年份</td>' +
                                            '        <td>'+workNumName+'</td>' +
                                            '        <td>操作</td>' +
                                            '    </tr>' +
                                            '    </thead>' +
                                            '    <tbody>';
                        for(var i = 0;i<yearList.length;i++){
                            if(yearList[i].number !== 0){
                                offerListStr +=         '<tr>'+
                                                            '<td>' + yearList[i].year + '</td>'+
                                                            '<td>' + yearList[i].number + '</td>'+
                                                            '<td>' +
                                                            '    <span class="ty-color-blue" onclick="getOfferList(1,20,'+offerStatus+',4,$(this))">操作</span>' +
                                                            '    <div class="hd beginDate">'+yearList[i].beginDate+'</div>' +
                                                            '    <div class="hd endDate">'+yearList[i].endDate+'</div>' +
                                                            '</td>'+
                                                            '</tr>';
                            }
                        }
                        offerListStr +=         '</tbody>' +
                                            '</table>'+
                                        '</div>';
                    }else if(offerList){
                        if(offerStatus === 2){
                            offerListStr =  '<div class="historyItem">' +
                                '<table class="ty-table ty-table-control">' +
                                '<thead>' +
                                '<td width="6%">姓名</td>' +
                                '<td width="4%">性别</td>' +
                                '<td width="8%">文化程度</td>' +
                                '<td width="12%">应聘职位</td>' +
                                '<td width="14%">应聘时间</td>' +
                                '<td width="10%">手机号</td>' +
                                '<td width="14%">入职时间</td>' +
                                '<td width="6%">操作者</td>' +
                                '<td width="14%">操作时间</td>' +
                                '<td width="6%">个人简历</td>' +
                                '<td width="6%">面试意见</td>' +
                                '</thead>' +
                                '<tbody>';
                        }else if(offerStatus === 3){
                            offerListStr =  '<div class="historyItem">' +
                                '<table class="ty-table ty-table-control">' +
                                '<thead>' +
                                '<td width="8%">姓名</td>' +
                                '<td width="6%">性别</td>' +
                                '<td width="12%">文化程度</td>' +
                                '<td width="12%">应聘职位</td>' +
                                '<td width="14%">应聘时间</td>' +
                                '<td width="10%">联系电话</td>' +
                                '<td width="8">操作者</td>' +
                                '<td width="14%">操作时间</td>' +
                                '<td width="8%">个人简历</td>' +
                                '<td width="8%">面试意见</td>'+
                                '</thead>' +
                                '<tbody>';
                        }


                        for(var i = 0;i<offerList.length;i++){
                            var id = offerList[i].userID,
                                userName = offerList[i].userName,
                                gender = offerList[i].gender,
                                degree = offerList[i].degree,
                                offerPost = offerList[i].offerPost,
                                offerTime = offerList[i].offerTime,
                                mobile = offerList[i].mobile,
                                onDutyDate = offerList[i].onDutyDate,
                                handleTime = offerList[i].handleTime,
                                createName = offerList[i].createName;
                            gender === "1" ? gender = '男' : gender = '女';
                            degree = chargeDegree(degree);

                            offerListStr += '<tr id="' + id + '">' +
                                '<td>' + userName + '</td>' +    //应聘姓名
                                '<td>' + gender + '</td>' +      //性别
                                '<td>' + degree + '</td>' +      //文化程度
                                '<td>' + offerPost + '</td>' +   //应聘职位
                                '<td>' + offerTime + '</td>' +   //应聘时间
                                '<td>' + mobile + '</td>';      //手机号
                            if (offerStatus === 2) {
                                offerListStr += '<td>' + onDutyDate + '</td>' +    //入职时间
                                    '<td>' + createName + '</td>' +    //操作者
                                    '<td>' + handleTime + '</td>' +    //处理时间
                                    '<td>' +
                                    '    <span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),0)">查看</span>' +
                                    '</td>' +
                                    '<td>' +
                                    '    <span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),1)">查看</span>' +
                                    '</td>';

                            } else if (offerStatus === 3) {
                                offerListStr += '<td>' + createName + '</td>' +    //操作者
                                    '<td>' + handleTime + '</td>' +    //处理时间
                                    '<td>' +
                                    '    <span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),0)">查看</span>' +
                                    '</td>' +
                                    '<td>' +
                                    '    <span class="ty-color-blue" onclick="seeOfferInfoBtn($(this),1)">查看</span>' +
                                    '</td>';
                            }
                            offerListStr += '</tr>'
                        }
                        offerListStr +=         '</tbody>' +
                            '</table>'+
                            '</div>';
                    }
                    $(".queryHistory").append(offerListStr);
                }

            }
        }
    })
}

// creator: 张旭博，2018-04-27 08:45:14，查看个人简历/面试意见 - 按钮
function seeOfferInfoBtn(selector,type) {
    var id = selector.parents("tr").attr("id"),
        state  = $(".ty-secondTab li.ty-active").data("state");
    $(".offerInfo").show().siblings().hide();
    $(".offerInfo").data("id",id);
    // 获取简历详情
    getOfferInfoStr(id,type);
    // type-0 查看个人简历 面试意见隐藏 type-1面试意见 显示面试意见
    if(type === 0){
        $(".offerFeedBack").hide();
    }else{
        $(".offerFeedBack").show();
    }
    //查看、非待处理、超管均不能看到面试意见按钮
    if(type === 0 || state !== 1){
        $(".timer").hide()
        $("#offerFeedBackBtn").hide();
    }else{
        $("#offerFeedBackBtn").show();
    }
    if($(".queryHistory").html() !== ""){
        $(".back").hide();
    }
}

// creator: 张旭博，2018-04-27 08:52:02，入职 - 按钮
function entryBtn(selector) {
    $("#entry").find("input,select").not(".ordinaryEmployees").val("");
    var offerId = selector.parents("tr").attr("id");
    bounce.show($("#entry"));
    $("#entry").data("offerId",offerId);
    bounce.everyTime('0.2s','entry',function(){
        var state = 1;
        $("#entry").find(".required").each(function () {
            if($.trim($(this).val()) === ''){
                state = 0;
            }
        });
        var mobile = $("#entry .phone").val();

        if(testMobile(mobile) && mobile !== '' && state === 1){
            $("#entryConfirmBtn").prop("disabled",false);
        }else{
            $("#entryConfirmBtn").prop("disabled",true);
        }
    });
    getSelect();
}

// creator: 张旭博，2018-04-27 16:28:15，入职确认 - 显示确认弹窗
function entryConfirm() {
    bounce_Fixed.show($("#entryConfirm"));
}

// creator: 张旭博，2018-05-10 11:56:34，入职 - 确认
function sureEntryConfirm() {
    var offerId = $("#entry").data("offerId"),
        ordinaryEmployees = $("#entry .ordinaryEmployees").val(),
        manager = $("#entry .manager").val(),
        leaderId = $("#entry .leaderId").val(),
        department = $("#entry .department").val(),
        postId = $("#entry .postId").val(),
        onDutyDate = $("#entry .onDutyDate").val(),
        phone = $("#entry .phone").val();
    var data = {
        "offerId":offerId,  //招聘id
        "ordinaryEmployees":ordinaryEmployees,  //1-普通员工，0-非普通 manager 高管id
        "manager":manager,    //高管id
        "leaderId":leaderId,    //直接上级id
        "department":department,    //部门id
        "postId":postId,    //职位id
        "onDutyDate":onDutyDate,    //入职日期
        "phone":phone   //手机号
    }
    $.ajax({
        url : "../recruit/offerToUser.do" ,
        data : data,
        success:function( data ){
            var status = data["status"];
            bounce_Fixed.cancel();
            bounce.cancel();
            $("#errorTip .sureBtn").unbind().on("click", function (){
                bounce.cancel()
            })
            if(status === 1){
                layer.msg("入职成功");
                $(".ty-secondTab li").eq(1).click();
            }else if(status === 2){
                $("#errorTip .tipWord").html("手机号重复，请重新输入！");
                bounce.show($("#errorTip"));
            }else{
                $("#errorTip .tipWord").html("入职失败！");
                bounce.show($("#errorTip"));
            }
        }
    })
}

// creator: 张旭博，2018-04-27 08:48:48，面试未通过 - 按钮
function offerNotPassedBtn(selector) {
    bounce.show($("#refuseEntry"))
    var offerId = selector.parents("tr").attr("id");
    $("#refuseEntry").data("offerId",offerId)
}

// creator: 张旭博，2018-04-27 08:48:48，面试未通过 - 确定
function sureOfferNotPassed() {
    var offerId = $("#refuseEntry").data("offerId");
    $.ajax({
        url : "../recruit/offerFalse.do" ,
        data : { "offerId":offerId },
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                bounce.cancel();
                layer.msg("该面试已拒绝");
                $(".ty-secondTab li").eq(2).click();
            }
        }
    })
}

// creator: 张旭博，2018-04-27 08:49:45，新增面试意见 - 按钮
function newOfferFeedBackBtn() {
    bounce.show($("#newOfferFeedBack"));
    $("#newOfferFeedBack").find("input,select,textarea").val("");
}

// creator: 张旭博，2018-04-27 08:51:37，新增面试意见 - 确认
function sureNewOfferFeedBack() {
    var dutyTime = $("#newOfferFeedBack .dutyTime").val(),
        interviewTime = $("#newOfferFeedBack .interviewTime").val(),
        professionalKnowledge = $("#newOfferFeedBack .professionalKnowledge").val(),
        appearanceAttitude = $("#newOfferFeedBack .appearanceAttitude").val(),
        languageCompetence = $("#newOfferFeedBack .languageCompetence").val(),
        reactionCapacity = $("#newOfferFeedBack .reactionCapacity").val(),
        senseTrust = $("#newOfferFeedBack .senseTrust").val(),
        behaviorMotivation = $("#newOfferFeedBack .behaviorMotivation").val(),
        overallAssessment = $("#newOfferFeedBack .overallAssessment").val(),
        probationSalary = $("#newOfferFeedBack .probationSalary").val(),
        salary = $("#newOfferFeedBack .salary").val(),
        probationPeriod = $("#newOfferFeedBack .probationPeriod").val(),
        suggestion = $("#newOfferFeedBack .suggestion").val(),
        memo = $("#newOfferFeedBack .memo").val(),
        offerId = $(".offerInfo").data("id");
    probationSalary === ""?probationSalary=0:probationSalary;
    salary === ""?salary=0:probationSalary;
    var data = {
        "offerId":offerId,  //招聘id
        "interviewTime1":interviewTime,    //面试时间
        "dutyTime1":dutyTime,    //可以到岗时间
        "professionalKnowledge":professionalKnowledge,  //专业知识 4-优、3-良、2-中、1-差
        "appearanceAttitude":appearanceAttitude,    //态度仪表: 4-优、3-良、2-中、1-差
        "languageCompetence":languageCompetence,    //语言能力: 4-优、3-良、2-中、1-差
        "reactionCapacity":reactionCapacity,    //4-优、3-良、2-中、1-差
        "senseTrust":senseTrust,    //信赖感: 4-优、3-良、2-中、1-差
        "behaviorMotivation":behaviorMotivation,    //4-优、3-良、2-中、1-差
        "overallAssessment":overallAssessment,  //综合评价
        "probationSalary":probationSalary,  //试用工资
        "salary":salary,    //转正后工资
        "probationPeriod":probationPeriod,  //试用期(月)
        "suggestion":suggestion,    //个人意见:1-建议试用、,2-暂缓决定,3-不建议试用
        "memo":memo,    //备注
    };
    $.ajax({
        url : "../recruit/addPersonnelInterview.do" ,
        data : data,
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                bounce.cancel();
                layer.msg("新增面试意见成功");
                getOfferInfoStr(offerId,1)
            }else{
                $("#errorTip .sureBtn").unbind().on("click", function (){
                    bounce.cancel()
                })
                $("#errorTip .tipWord").html("新增面试意见失败！");
                bounce.show($("#errorTip"));
            }
        }
    })
}

// creator: 张旭博，2018-04-27 08:51:47，获取招聘详情字符串
function getOfferInfoStr(id,type) {
    $.ajax({
        url : "../recruit/getPersonnelInterviewList.do" ,
        data : { "id": id },
        success:function( data ){
            var status = data["status"];
            if(status === 1){
                var offerInfo = data["offer"],
                    feedBacks = offerInfo.personnelInterviews,
                    personalEducations = offerInfo.personalEducations,
                    personnelFolkses = offerInfo.personnelFolkses,
                    personnelOccupations = offerInfo.personnelOccupations,
                    personalEducationsStr = '',
                    personnelFolksesStr	= '',
                    personnelOccupationsStr	= '',
                    offerFeedBackStr = '',
                    state = $(".ty-secondTab li.ty-active").data("state");
                if(state === 1){
                    $(".offerInfo .postInfo").hide()
                }else{
                    $(".offerInfo .postInfo").show()
                }
                if(typeof (offerInfo) !== "undefined"){
                        offerInfo.gender === "1"?offerInfo.gender = '男':offerInfo.gender = '女';
                        $(".offerInfo .scan").find(".userName").html(offerInfo.userName);
                        $(".offerInfo .scan").find(".gender").html(offerInfo.gender);
                        $(".offerInfo .scan").find(".birthday").html(offerInfo.birthday);
                        $(".offerInfo .scan").find(".degree").html(chargeDegree(offerInfo.degree));
                        $(".offerInfo .scan").find(".residence").html(offerInfo.residence);
                        $(".offerInfo .scan").find(".nation").html(offerInfo.nation);
                        $(".offerInfo .scan").find(".politicalStatus").html(offerInfo.politicalStatus);
                        $(".offerInfo .scan").find(".marry").html(chargeMarry(offerInfo.marry));
                        $(".offerInfo .scan").find(".offerSalary").html(offerInfo.offerSalaty);
                        $(".offerInfo .scan").find(".offerPost").html(offerInfo.offerPost);
                        $(".offerInfo .scan").find(".interesting").html(offerInfo.interesting);
                        $(".offerInfo .scan").find(".speciality").html(offerInfo.speciality);
                        $(".offerInfo .scan").find(".avatar").attr('src',offerInfo.imgPath);
                        if(type === 0){
                            //查看
                            $(".offerInfo .scan").find(".idCard").html(offerInfo.idCard);
                            $(".offerInfo .scan").find(".homeAddress").html(offerInfo.homeAddress);
                            $(".offerInfo .scan").find(".mobile").html(offerInfo.mobile);
                            $(".offerInfo .scan").find(".qq").html(offerInfo.qq);
                            $(".offerInfo .scan").find(".email").html(offerInfo.email);
                            $(".important").show()

                        }else if(type === 1){
                            $(".important").hide();
                            $(".offerInfo .scan").find(".idCard").html("");
                            $(".offerInfo .scan").find(".homeAddress").html("");
                            $(".offerInfo .scan").find(".mobile").html("");
                            $(".offerInfo .scan").find(".qq").html("");
                            $(".offerInfo .scan").find(".email").html("");

                        }
                        $(".offerInfo .postInfo").find(".onDutyDate").html(offerInfo.onDutyDate.substring(0,10));
                        $(".offerInfo .postInfo").find(".departName").html(offerInfo.departName);
                        $(".offerInfo .postInfo").find(".postName").html(offerInfo.postName);
                        $(".offerInfo .postInfo").find(".ordinaryEmployees").html(chargeOrdinaryEmployees(offerInfo.ordinaryEmployees));
                        $(".offerInfo .postInfo").find(".manageName").html(chargeManageCode(offerInfo.managerCode));
                        $(".offerInfo .postInfo").find(".leaderName").html(offerInfo.leaderName);

                }
                if(typeof (feedBacks) !== "undefined"){
                    for(var i=0;i<feedBacks.length;i++){
                        var gapStr = '';
                        if(i!==0){gapStr = '<div class="col-xs-12"><span class="gap"></span></div>'}else{gapStr = ''};
                        offerFeedBackStr += gapStr +
                                            '<div class="feedBackItem">' +
                                                '<ul class="row">'+
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">面试官：</span> <span class="createName">'+feedBacks[i].interviewerName+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">录入时间：</span> <span class="createTime">'+feedBacks[i].createDate+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">个人意见：</span> <span class="suggestion">'+chargeSuggestion(feedBacks[i].suggestion)+'</span></li>' +
                                                '</ul>'+
                                                '<ul class="row">'+
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">面试时间：</span> <span class="dutyTime">'+feedBacks[i].interviewTime.substring(0,10)+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">可到岗日期：</span> <span class="dutyTime">'+feedBacks[i].dutyTime.substring(0,10)+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">专业知识：</span> <span class="professionalKnowledge">'+chargeLevel(feedBacks[i].professionalKnowledge)+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">态度仪表：</span> <span class="appearanceAttitude">'+chargeLevel(feedBacks[i].appearanceAttitude)+'</span></li>' +
                                                '</ul>'+
                                                '<ul class="row">'+
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">语言能力：</span> <span class="languageCompetence">'+chargeLevel(feedBacks[i].languageCompetence)+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">反应能力：</span> <span class="reactionCapacity">'+chargeLevel(feedBacks[i].reactionCapacity)+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">信赖感：</span> <span class="senseTrust">'+chargeLevel(feedBacks[i].senseTrust)+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">应征动机：</span> <span class="behaviorMotivation">'+chargeLevel(feedBacks[i].behaviorMotivation)+'</span></li>' +
                                                '</ul>'+
                                                '<ul class="row">'+
                                                '    <li class="col-xs-6 col-sm-12"><span class="us_field">综合评价：</span> <span class="overallAssessment">'+feedBacks[i].overallAssessment+'</span></li>' +
                                                '</ul>'+
                                                '<ul class="row">'+
                                                '    <li class="col-xs-12 col-sm-12"><b class="ty-color-gray">待遇建议</b></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">试用期月薪：</span> <span class="probationSalary">'+(feedBacks[i].probationSalary === 0?'':feedBacks[i].probationSalary)+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">转正后月薪：</span> <span class="salary">'+(feedBacks[i].salary === 0?'':feedBacks[i].salary)+'</span></li>' +
                                                '    <li class="col-xs-6 col-sm-3"><span class="us_field">试用期：</span> <span class="probationPeriod">'+feedBacks[i].probationPeriod+'个月</span></li>' +
                                                '    <li class="col-xs-6 col-sm-12"><span class="us_field">备注：</span> <span class="memo">'+feedBacks[i].memo+'</span></li>' +
                                                '</ul>'+
                                            '</div>';
                    }
                }
                //教育信息
                if(typeof (personalEducations) !== "undefined"){
                    for(var j=0;j<personalEducations.length;j++){
                        personalEducationsStr += '<tr>' +
                            '<td>'+personalEducations[j].collegeName+'</td>'+
                            '<td>'+personalEducations[j].beginTime.substring(0,10)+'~'+personalEducations[j].endTime.substring(0,10)+'</td>'+
                            '<td>'+personalEducations[j].major+'</td>'+
                            '<td>'+personalEducations[j].degree+'</td>'+
                            '<td>'+personalEducations[j].memo+'</td>'+
                            '</tr>'
                    }
                }
                //家庭成员
                if(typeof (personnelFolkses) !== "undefined"){
                    for(var k=0;k<personnelFolkses.length;k++){
                        personnelFolksesStr += '<tr>' +
                            '<td>'+personnelFolkses[k].name+'</td>'+
                            '<td>'+personnelFolkses[k].gender+'</td>'+
                            '<td>'+(personnelFolkses[k].age === 0?'':personnelFolkses[k].age)+'</td>'+
                            '<td>'+personnelFolkses[k].relation+'</td>'+
                            '</tr>'
                    }
                }
                //工作经历
                if(typeof (personnelOccupations) !== "undefined"){
                    for(var m=0;m<personnelOccupations.length;m++){
                        personnelOccupationsStr += '<tr>' +
                            '<td>'+personnelOccupations[m].corpName+'</td>'+
                            '<td>'+personnelOccupations[m].beginTime.substring(0,10)+'~'+personnelOccupations[m].endTime.substring(0,10)+'</td>'+
                            '<td>'+personnelOccupations[m].salary+'</td>'+
                            '<td>'+personnelOccupations[m].post+'</td>'+
                            '<td>'+personnelOccupations[m].operatingDuty+'</td>'+
                            '<td>'+personnelOccupations[m].memo+'</td>'+
                            '</tr>'
                    }
                }
                $(".offerInfo .offerFeedBack .con_part").html(offerFeedBackStr);
                $(".offerInfo .education tbody").html(personalEducationsStr);
                $(".offerInfo .work tbody").html(personnelOccupationsStr);
                $(".offerInfo .family tbody").html(personnelFolksesStr);
            }
        }
    })
}

// // creator: 张旭博，2018-05-16 10:19:58，返回
// function back() {
//     if($(".queryHistory").html() === "" ){
//         $(".main").show().siblings().hide();
//         var state  = $(".ty-secondTab li.ty-active").data("state");
//         if(state > 1){
//             $(".timer").show()
//         }
//     }else{
//         $(".queryHistory").show().siblings().hide();
//         $(".back").show();
//     }
// }
//
// function goback() {
//     if($(".queryHistory").children().length === 1){
//         $(".timer").show();
//         $(".back").hide();
//         $(".queryHistory").html("");
//         $(".main").show().siblings().hide();
//     }else{
//         $(".queryHistory").children(":last").remove();
//         $(".queryHistory").children(":last").show();
//     }
// }

// creator: 张旭博，2018-05-16 10:20:08，获取入职中的选择框信息（高管、直接上级、职位、部门）
function getSelect() {
    $.ajax({
        url : "../recruit/getSelect.do" ,
        success:function( data ){
            var userList = data["userList"],    //直接上级
                manageList = data["manageList"],    //所属高管
                postList = data["postList"],    //职位
                departmentList = data["departmentList"],    //部门
                userListOption = '<option value="">请选择直接上级</option>',
                manageListOption = '<option value="">请选择所属高管</option>',
                postListOption = '<option value="">请选择职位</option>',
                departmentListOption = '<option value="">请选择部门</option>';
            for(var i=0; i<userList.length; i++){
                userListOption += '<option value="'+userList[i].userID+'">'+userList[i].userName+'</option>'
            }
            for(var j=0; j<manageList.length; j++){
                manageListOption += '<option value="'+manageList[j].userID+'">'+manageList[j].manageName+'</option>'
            }
            for(var k=0; k<postList.length; k++){
                postListOption += '<option value="'+postList[k].id+'">'+postList[k].name+'</option>'
            }
            for(var m=0; m<departmentList.length; m++){
                departmentListOption += '<option value="'+departmentList[m].id+'">'+departmentList[m].name+'</option>'
            }
            $("#entry .manager").html(manageListOption);
            $("#entry .leaderId").html(userListOption);
            $("#entry .postId").html(postListOption);
            $("#entry .department").html(departmentListOption);

        }
    })
}

//-------------------------------------辅助函数-----------------------------------------//

// creator: 张旭博，2018-05-11 10:19:31，学历转换
function chargeDegree(degreeCode) {
    var degree = '';
    switch (degreeCode){
        case 1:
            degree =  "研究生";
            break;
        case 2:
            degree =  "本科";
            break;
        case 3:
            degree =  "大专";
            break;
        case 4:
            degree =  "中专或高中";
            break;
        case 5:
            degree =  "其它";
            break;
    }
    return degree
}

// creator: 张旭博，2018-05-11 10:19:40，评分转换
function chargeLevel(level) {
    var levelStr = '';
    switch (level){
        case "1":
            levelStr =  "差";
            break;
        case "2":
            levelStr =  "中";
            break;
        case "3":
            levelStr =  "良";
            break;
        case "4":
            levelStr =  "优";
            break;
        case "5":
            levelStr =  "其它";
            break;
    }
    return levelStr
}

// creator: 张旭博，2018-05-11 10:19:49，面试结果转换
function chargeSuggestion(suggest) {
    var suggestStr = '';
    switch (suggest){
        case "1":
            suggestStr =  "面试通过";
            break;
        case "2":
            suggestStr =  "暂缓决定";
            break;
        case "3":
            suggestStr =  "终止面试";
            break;
    }
    return suggestStr
}

// creator: 张旭博，2018-05-11 10:19:58，是否为普通员工转换
function chargeOrdinaryEmployees(ordinaryEmployees) {
    var ordinaryEmployeesStr = '';
    switch (ordinaryEmployees){
        case 1:
            ordinaryEmployeesStr =  "是";
            break;
        case 0:
            ordinaryEmployeesStr =  "否";
            break;
    }
    return ordinaryEmployeesStr
}

// creator: 张旭博，2018-05-11 10:19:58，婚姻转换
function chargeMarry(marry) {
    var marryStr = '';
    switch (marry){
        case "1":
            marryStr =  "未婚";
            break;
        case "0":
            marryStr =  "已婚";
            break;
    }
    return marryStr
}

// creator: 张旭博，2018-05-11 10:19:58，所属高管转换
function chargeManageCode(managerCode) {
    var managerStr = '';
    //1  超管	超管角色模板	super
    //2	 总务	总务角色模板	general
    //3	 财务	财务角色模板	finance
    //4	 销售	销售角色模板	sale
    //5	 超级浏览者	浏览者角色模板	browse
    //6	 普通员工	普通员工角色模板	staff
    //7	 会计	会计角色模板	accounting
    //8	 代理会计	代理会计角色模板	agentAccounting
    //10 代理小会计	代理小会计角色模板	agentSmallAccounting
    switch (managerCode){
        case "super":
            managerStr =  "超管";
            break;
        case "general":
            managerStr =  "总务";
            break;
        case "finance":
            managerStr =  "财务";
            break;
        case "sale":
            managerStr =  "销售";
            break;
        case "accounting":
            managerStr =  "会计";
            break;
    }
    return managerStr
}

// creator: 张旭博，2018-05-16 10:21:43，date选择框绑定时间控件
laydate.render({elem: '#queryBeginTime'});
laydate.render({elem: '#queryEndTime'});
laydate.render({elem: '#onDutyDate'});
laydate.render({elem: '#interviewTime'});
laydate.render({elem: '#dutyTime'});
laydate.render({elem: '#changeResumeStopTime', min: 1});
laydate.render({elem: '#countMonth',type: 'month',max: 0 , showBottom: false,
    change: function (value, date, endDate) { //监听日期被切换
        lay('#countMonth').val(value);
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
        })
        screen()
    }});
laydate.render({elem: '#notice_deadline', min: 1});



