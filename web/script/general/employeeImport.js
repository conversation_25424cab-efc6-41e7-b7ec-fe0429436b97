$(function () {
    getPresentUsers()
})
//--------------------------- 重写批量导入部分代码 -------------------------------//

// creator: 张旭博，2020-12-14 10:48:21，批量导入
function leadingStart(){
    $.ajax({
        url:"../userImport/unfinishedImportUser.do",
        success:function (data) {
            var userList = data.userImportList;
            if (userList.length > 0) {
                $('.unfinishedForm .fa').attr("class","fa fa-circle-o");
                $('#importNotCompleted').data("userData",data);
                bounce.show($('#importNotCompleted'));
            } else {
                $('#select_btn_1').val("");
                $('.userListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                bounce.show($('#leading'));
            }
        }
    });
}

// creator: 张旭博，2020-12-14 14:18:39，最后的确定保存
function lastSave(){
    $.ajax({
        url: "../userImport/completeImportUser.do",
        success: function (data) {
            var state = data.status;
            if (state == 1) {
                bounce.cancel();
                window.location.href = '../sys/logout.do'
            } else {
                layer.msg("保存失败！");
            }
        }
    })
}

// creator: 张旭博，2020-12-15 10:15:43，放弃
function clearUser() {
    var type = $("#clearUser").data("type");
    bounce.cancel();
    $(".importOpertion").siblings("button.ty-btn").show()
    if (type == '1') {
        $(".importing").hide();
        $(".importNoSave").hide();
        $(".main").show();
        getPresentUsers();
    } else {
        turnCancel(2)
    }
}

// creator: 李玉婷，2020-10-18 14:06:42，接口放弃
function turnCancel(type){
    $.ajax({
        url: "../userImport/giveUpImportUser.do",
        success: function (data) {
            var state = data.status;
            if (state == '1')
                if (type == '1') {
                    $('#select_btn_1').val("");
                    $('.userListUpload').remove();
                    $('#leading .fileFullName').html("尚未选择文件");
                    bounce.show($('#leading'));
                }else{
                    $(".importing").hide();$(".importNoSave").hide();$(".main").show();
                    getPresentUsers();
                }

        }
    })
}

// creator: 张旭博，2020-12-15 15:16:11，获取当前职工
function getPresentUsers() {
    $.ajax({
        url: '../org/getPresentUsers.do',
        success: function (res) {
            var data = res.data
            var str = ''
            if (data) {
                for (var i = 0; i<data.length; i++) {
                    str += '<tr>' +
                        '<td>'+data[i].userName+'</td>' +
                        '<td>'+data[i].mobile+'</td>' +
                        '<td>'+chargeOrdinaryEmployees(data[i].ordinaryEmployees)+'</td>' +
                        '<td>'+handleNull(data[i].leaderName)+'</td>' +
                        '</tr>'
                }
                $(".main tbody").html(str)
                $(".onDutyNum").html(data.length)
            }
        }
    })
}

function chargeOrdinaryEmployees(data) {
    return data == 1?'否':'是'
}