$(function () {

});







//---------------------------------------------------------//
function shanchu( type , obj ){
    if(confirm("确定要删除？")){
        obj.parent().next().submit() ;
    }
}
// 选择职位
function getZhi(obj){
    var oid = obj.children(".hd").html();
    var oname = obj.children(":eq(0)").html();
    obj.siblings(":eq(0)").children(".activeZhiId").html(oid);
    obj.attr("style","background:#36C6D3; border-color:#36C6D3; color:#fff;  ");
    obj.siblings("span").attr("style","background:#fff; border-color:#666; color:#333; ");
    $("#zhiName").val(oname);
    $("#zhiId").val(oid);
}
//新增部门,num=0 :新增同级，num=1 : 新增子级
var editDepartObj = null ; // current control departmentObj
function addDeparBtn(num,obj){
    var activeId = obj.parent().siblings(":eq(0)").children(".activeId").html();
    obj.parent().siblings("span").each(function(){
        var deparId = $(this).children(".hd").html();
        if (deparId == activeId) {
            editDepartObj = $(this) ;
            var pid ="";
            if (num == 0) {
                pid = obj.parent().parent().prev().children(":eq(0)").children(".activeId").html();
            } else if (num == 1) {
                pid = deparId;
            }
            $("#departpId_add").html(pid);
            $("#addType").html(num);

        }
    })

}
function addDepartment(){
    var pid = $("#departpId_add").html();
    var type = $("#addType").html();
    var name = $("#departName_add").val();
    // 异步新增
    // then
    var status = 1; // 新增成功
    var str = "<span onclick='getKidsDepart($(this))'><span>"+ name +"</span><span class='hd'>4</span></span>";
    if (type == 0) {
        editDepartObj.parent().append(str);
    } else if (type == 1) {
        editDepartObj.parent().next().append(str);
    }
    $(".close").click();
}
//	单击部门，召唤出子级部门
function getKidsDepart(obj){
    var oid = obj.children(".hd").html();
    var oname = obj.children(":eq(0)").html();
    var activeId = obj.siblings(":eq(0)").children(".activeId").html();
    if (activeId != oid){
        // then get Kids departments by oid
        // then
        var str = "<div><div style='display:none; '><span class='activeId' ></span><span class='isKidsShow' ></span></div><a class='depatlevel'>二级部门</a>"+
            "<span onclick='getKidsDepart($(this))'><span>销售部1</span><span class='hd'>4</span></span><span onclick='getKidsDepart($(this))'><span>销售部2</span><span class='hd'>5</span></span>"+
            "<span onclick='getKidsDepart($(this))'><span>销售部3</span><span class='hd'>6</span></span><div class='control'><a href='#editDepar' data-toggle='modal' class='hd' onclick='upBtn($(this))'>编辑</a>"+
            "<a class='hd' onclick='delBtn($(this))'>删除</a><a href='#addDepar' data-toggle='modal' onclick='addDeparBtn(0,$(this))' class='hd'>新增同级部分</a>"+
            "<a href='#addDepar' data-toggle='modal' onclick='addDeparBtn(1,$(this))' class='hd'>新增子级部门</a>" ;
        // update kids after the level clicked
        if (activeId != "") {
            obj.parent().nextAll().remove();
        }
        obj.parent().after(str);
        obj.siblings(":eq(0)").children(".activeId").html(oid);
        obj.attr("style","background:#36C6D3; border-color:#36C6D3; color:#fff;  ");
        obj.siblings("span").attr("style","background:#fff; border-color:#666; color:#333; ");

    }
    obj.siblings(".control").children(".hd").show();


    $("#oId").val(oid);
}
// 删除部门名称
function delBtn(obj){
    // 获得该部门下的子部门数量
    var kidDeparNum = 0;
    if (parseInt(kidDeparNum)>0) {
        alert("该部门下还有子部门，不能删除");
    }else{

        var activeId = obj.parent().siblings(":eq(0)").children(".activeId").html();
        obj.parent().siblings("span").each(function(){
            var deparId = $(this).children(".hd").html();
            if (deparId == activeId) {
                var departName = $(this).children(":eq(0)").html();
                if (confirm("确定删除部门:"+ departName + "？")) {
                    // 执行删除操作
                    $(this).remove();
                    obj.parent().siblings(":eq(0)").children(".activeId").html("");
                }
            }
        })
    }
}
// 修改部门名称
function upBtn(obj){
    var activeId = obj.parent().siblings(":eq(0)").children(".activeId").html();
    var activeName = "";
    obj.parent().siblings("span").each(function(){
        var deparId = $(this).children(".hd").html();
        if (deparId == activeId) {
            editDepartObj = $(this) ;
            activeName = $(this).children(":eq(0)").html();
        }
    });
    $("#departName").val(activeName);
    $("#departId").val(activeId);
}
function updateDepartment(){
    var status = 1 ;
    var id = $("#departId").val();
    var name = $("#departName").val();
    // 异步修改部门名称
    // then
    if (status == 1) {
        if (editDepartObj == null ) {
            alert("获取原部门失败");
        } else{
            editDepartObj.children(":eq(0)").html(name);
        }
    } else{
        alert("修改失败！");
    }
    $(".close").click();
}
// 显示要编辑的内容
function conShow(str){
    $("#"+str).show().siblings().hide();
}
// 下拉框toggle
function toggleSelect(obj){
    var num = obj.attr("title");
    if (num == 0) {
        obj.parent().siblings("div.hd").show();
        obj.attr("title","1");
    } else{
        obj.parent().siblings("div.hd").hide();
        obj.attr("title","0");
    }
    return false;
}
function toggleSe(obj){
    var toggleObj = obj.prev();
    toggleSelect(toggleObj);
}
// 设置选择部门的路径
function setDepatShow(){
    var DepartLink = "";
    $(".activeId").each(function(){
        var activeIdObj = $(this);
        var activeId = activeIdObj.html();
        activeIdObj.parent().siblings("span").each(function(){
            var id = $(this).children(":eq(1)").html();
            if (activeId == id) {
                DepartLink +=  $(this).children(":eq(0)").html() + " - " ;
            }
        })
    });
    $("#oName").val(DepartLink.substring(0 , DepartLink.length-2));
}

function setImagePreview() {
    var docObj = document.getElementById("imgFile");
    var imgObjPreview = document.querySelector(".preview");
    if (docObj.files && docObj.files[0]) {
        /*火狐下，直接设img属性*/
        imgObjPreview.style.display = 'block';
        imgObjPreview.style.width = '150px';
        imgObjPreview.style.height = '150px';
        /*imgObjPreview.src = docObj.files[0].getAsDataURL();*/
        /*火狐7以上版本不能用上面的getAsDataURL()方式获取，需要一下方式*/
        imgObjPreview.src = window.URL.createObjectURL(docObj.files[0]);
    } else {
        /*IE下，使用滤镜*/
        docObj.select();
        var imgSrc = document.selection.createRange().text;
        var localImagId = document.getElementById("localImag");
        /*必须设置初始大小*/
        localImagId.style.width = "150px";
        localImagId.style.height = "150px";
        /*图片异常的捕捉，防止用户修改后缀来伪造图片*/
        try {
            localImagId.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)"; localImagId.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = imgSrc;
        } catch (e) {
            alert("您上传的图片格式不正确，请重新选择!");
            return false;
        }
        imgObjPreview.style.display = 'none';
        document.selection.empty();
    }
    return true;
}

//-----------------------------------------------------------------------------------------//
