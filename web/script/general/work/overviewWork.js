$(function(){
    $(".page").on("click","[type='btn']",function () {
        var name = $(this).data("name");
        switch (name) {
            case 'seeTimeSetting':
                timeTableSet()
                break;
            case 'seeOnebodyWork':
                var month = $("#chooseMonth").data("month")
                var teamUserName = $(this).parents("tr").find("td").eq(0).html()
                var teamUserId = $(this).parents("tr").find("td").eq(0).attr("data-id")
                var leader = $(this).parents("tr").find("td").eq(0).attr("data-leader")
                var isLeader = Number(leader) === sphdSocket.user.userID
                jumpPage("employee", function (){
                    $(".onedayWorkTitle").html(moment(month).format("YYYY年MM月 ") + teamUserName)

                    $(".tbl_month").data("userId", teamUserId)
                    $(".tbl_month").data("isLeader", isLeader)
                    $(".tbl_month").data("month", month)
                    initMonth()
                })
                break
            case 'seeOnebodyReport':
                var month = $("#monthReport").data("month")
                var teamUserName = $(this).parents("tr").find("td").eq(0).html()
                var teamUserId = $(this).parents("tr").find("td").eq(0).attr("data-id")
                $("#monthReport").data('teamUser', {
                    teamUserName: teamUserName,
                    teamUserId: teamUserId
                })
                jumpPage("monthReport", function (){
                    getMonthlyReportStat(month)
                })
                break
        }
    });

    $(".workQuery").find('input,select,textarea').val("");

    // 查询按钮的点击事件
    $(".departSelect").on("change",function () {
        getAttendanceDays(1, 20)
    })
    // 日历某一天 - 点击
    $(".tbl_month").on("click", 'td', function () {
        var thisMonth = $(".tbl_month").data("month")
        var thisDate = thisMonth + '-' + ($(this).attr("day")>9?$(this).attr("day"):'0'+$(this).attr("day"))
        // 初始化数据
        if ($(this).attr("day") !== '') {
            getWorkOfSomeday(thisDate)
        }
    })
    // 工作录入内的所有按钮 - 点击
    $("#workInput, #workChange, #workSee, #workRecordSee, #workFutureSee").on("click", 'button', function () {
        var name = $(this).attr("name")

        switch (name) {
            case 'seeLeave':
                seeLeaveRecord($(this))
                break;
        }

    })
    // 获取工作记录汇总
    jumpPage("main", function () {
        $(".backBtn").hide()
        setAllDeparts($(".departSelect"))
        getAttendanceDays(1, 20)
    })
});

// creator: 张旭博，2022/6/23 11:44，获取工作记录汇总
function getAttendanceDays (currentPageNo, pageSize) {
    var page = $(".page:visible").attr("page")
    var deptId = $(".page:visible .departSelect").val()
    var data = {
        deptId: deptId,
        pageSize: pageSize,
        currentPageNo: currentPageNo,
        sourceType: 2
    } // dateType(dateType:1-月报 2-年报)，Integer onlyUser(职工id，仅仅查询此职工的相关天数
    if (page === 'main') {
        var month = $("#otherMonthWork").val() || moment(hostTime).format("YYYY-MM")
        $(".page_main .nowDay").html(moment(hostTime).format('YYYY年MM月DD日 dddd'));
    } else if (page === 'monthReportPre') {
        var month = $("#monthReport").data("month")
        data.dateType = month.length > 4?1:2
    }
    data.beginDate = month.length > 4?(month+ '-01'):(month+'-01-01')
    $.ajax({
        url: '../workAttendance/getAttendanceDays.do',
        data: data,
        success: function (res) {
            var data = res.data
            var pageInfo = res.page
            var userDaysDtos = data.userDaysDtos
            var workingDays  = data.workingDays

            if (page === 'main') {
                $(".page:visible .workTip").html('<span id="chooseMonth" data-month="'+moment(month).format('YYYY-MM')+'">' + moment(month).format('YYYY年MM月 ') + '</span><span style="margin-left: 16px">本月工作日共'+workingDays+'天</span>');
            } else if (page === 'monthReportPre') {
                var workTipStr = month.length > 4? ('<span id="chooseMonth">' + moment(month).format('YYYY年MM月 ') + '</span><span style="margin-left: 16px">本月工作日共'+workingDays+'天</span>'):('<span id="chooseMonth">' + moment(month).format('YYYY年 ') + '</span><span style="margin-left: 16px">本年工作日共'+workingDays+'天</span>')
                $(".page:visible .ty-title").html(month.length > 4?'工作月报':'工作年报')
                $(".page:visible .workTip").html(workTipStr);
            }

            // 设置分页
            var totalPage = pageInfo.totalPage; //总页数
            var curr = pageInfo.currentPageNo; //当前页
            var ye = page === 'main'?$("#ye_attendanceDays"):$("#ye_attendanceReport")
            setPage( ye, curr ,  totalPage , "attendanceDays");

            var str = ''
            var handleStr = ''
            if (page === 'main') {
                handleStr =  '<span class="link-blue" type="btn" data-name="seeOnebodyWork">查看</span>'
            } else if (page === 'monthReportPre') {
                handleStr =  '<span class="link-blue" type="btn" data-name="seeOnebodyReport">'+(month.length > 4?'查看月报':'查看年报')+'</span>'
            }
            if (userDaysDtos.length > 0) {
                for (var i in userDaysDtos) {
                    str +=  '<tr>' +
                        '   <td data-id="'+userDaysDtos[i].userID+'" data-leader="'+userDaysDtos[i].leader+'">'+userDaysDtos[i].userName+'</td>' +
                        '   <td>'+userDaysDtos[i].mobile+' </td>' +
                        '   <td>'+userDaysDtos[i].outTimeDays+'天</td>' +
                        '   <td>'+userDaysDtos[i].submissionDays+'天</td>' +
                        '   <td>'+userDaysDtos[i].notSubmissionDays+'天</td>' +
                        '   <td>'+handleStr + '</td>'+
                        '</tr>'
                }
            }
            $(".page:visible .workRecordTotal tbody").html(str)
        }
    })
}

// creator: 张旭博，2020-12-18 10:13:22，初始化日表格
function initMonth(noloading) {
    var month = $(".tbl_month").data("month")
    dateppicker.init($(".tbl_month"), month.substr(0,4), month.substr(5,2))
    $(".tbl_month").data("noRed", '1')
    getRedListByMonth(noloading)
}

// creator: 张旭博，2020-12-18 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 工作记录
            case 'workSee':
                $("#workSee tbody").each(function () {
                    if ($(this).find("tr").length === 0) {
                        $(this).parents("table").hide()
                    } else {
                        $(this).parents("table").show()
                    }
                })
                break;
            // 工作记录
            case 'workRecordSee':
                $("#workRecordSee tbody").each(function () {
                    if ($(this).find("tr").length === 0) {
                        $(this).parents("table").hide()
                    } else {
                        $(this).parents("table").show()
                    }
                })
                break;
        }
    });
}

// creator: 张旭博，2020-12-18 10:19:00，获取某天的工作记录详情
function getWorkOfSomeday(date) {
    var isLeader = $(".tbl_month").data("isLeader")
    var teamUserId = $(".tbl_month").data("userId")
    var isToday = isTodayOrFuture(date) // -1: 过去 0: 现在 1: 未来
    if (isLeader && isToday < 1) {
        $(".panel_inputReview").show()
    } else {
        $(".panel_inputReview").hide()
    }

    $(".tbl_month").data("date", date)
    $(".currentDayWeek").html(moment(date).format("YYYY年MM月DD日 dddd"))
    var data = {
        org: sphdSocket.user.oid,
        userId: sphdSocket.user.userID,
        date: date
    }
    if (teamUserId) {
        data.teamUserId = teamUserId
    }
    var pm_workOfSomeDay = new Promise((resolve, reject) => {
        $.ajax({
            url: '../mywork/getWorkOfSomeday.do',
            data: data,
            success: function (res) {
                var success = res.success
                if (success === 1) {
                    resolve(res.data)
                } else {
                    reject()
                }
            }
        })
    })
    pm_workOfSomeDay.then(data => {
        $(".tbl_month").data("workId", data.workId || 0)
        $(".tbl_month").data("state", data.state)
        $(".tbl_month").data("versionNo", data.versionNo)

        var state = Number(data.state) // 2-保存草稿 3-正式提交

        // -----------------初始化弹窗------------------
        var showDialog;

        // 已经正式提交 （显示查看弹窗）
        showDialog = 'workSee'
        bounce.show($("#workSee"))
        setEveryTime(bounce, 'workSee')
        // 1.如果之前正式提交近日的工作计划，那么以后都不会显示此部分内容
        if (data.recentList.length === 0) {
            $("#workInput .recentWork").hide()
        }

        // -----------------处理表头------------------ （申请 查看 修改 公用）
        var lastOperateTime = data.lastOperateTime ? moment(data.lastOperateTime).format("YYYY-MM-DD HH:mm:ss"): '暂无' // 最后的操作时间
        var leaveData = data.leaveData // 请假信息
        var isWorkDay = data.isWorkDay // true 正常上班 false 休息
        var hasExtraWork = data.hasExtraWork // true 有加班 false 无加班
        var timeSpentTotal = data.timeSpentTotal // 当日已录入内容合计耗时
        var commentList = data.commentList // 评论

        // 赋值当日应该有工作记录的原因
        $("#" + showDialog).find(".workReason").html(chargeWorkReason(isWorkDay, hasExtraWork))
        // 处理请假
        handleLeaveDetail(leaveData, showDialog)
        // 处理评论
        handleComment(commentList, showDialog)
        // 赋值当日已录入内容合计耗时
        $("#" + showDialog).find(".timeSpentTotal").html(timeSpentTotal)
        // 赋值最后的操作时间
        $("#" + showDialog).find(".lastOperateTime").html(lastOperateTime)

        if (state < 2 && isToday < 1) {
            $(".workPart").hide()
        } else {
            $(".workPart").show()
        }

        if (state > 1) {
            $(".lastOperateTimeVisible .workRecordBtn").show()
        } else {
            $(".lastOperateTimeVisible .workRecordBtn").hide()
        }

        // -----------------处理内容------------------

        var routineList = data.routineList // 日常工作内容
        var innerList = data.innerList

        // 获取日常工作
        var routineStr = ''
        var routineHeadStr = ''
        for (var m = 0; m < routineList.length; m++) {
            var fulfillment = Number(routineList[m].routineFulfillment)

            var routineItem = {
                id: routineList[m].id,
                routineId: routineList[m].routineId,
                contentPlan: routineList[m].contentPlan,
                routineFulfillment: routineList[m].routineFulfillment || 0,
                timePlan: routineList[m].timePlan?moment(routineList[m].timePlan).format("YYYY-MM-DD HH:mm:ss"):'',
                timePlanDate: routineList[m].timePlan?moment(routineList[m].timePlan).format("YYYY-MM-DD"):'',
                timePlanTime: routineList[m].timePlan?moment(routineList[m].timePlan).format("HH:mm"):'',

            }
            if (fulfillment === 1) {
                routineItem.content = routineList[m].content
                routineItem.timeFact = moment(routineList[m].timeFact).format("YYYY-MM-DD HH:mm:ss")
                routineItem.timeFactDate = moment(routineList[m].timeFact).format("YYYY-MM-DD")
                routineItem.timeFactTime = moment(routineList[m].timeFact).format("HH:mm")
                routineItem.durationFactHour = routineList[m].durationFactHour
                routineItem.durationFactMinute = routineList[m].durationFactMinute
                routineItem.durationFact = -(-routineList[m].durationFactHour - (routineList[m].durationFactMinute/60).toFixed(2))  //实际耗时（小时，保存两位）
            }
            if (isToday === 1) {
                routineHeadStr ='<tr>' +
                    '    <th>工作内容</th>' +
                    '    <th>计划开始时间</th>' +
                    '</tr>'
                routineStr +=   '<tr>' +
                    '   <td>'+(routineItem.content?routineItem.contentPlan + '/<span class="ty-color-orange">'+routineItem.content+'</span>':routineItem.contentPlan)+'</td>'+
                    '   <td>'+routineItem.timePlanTime+'</td>'+
                    '</tr>'
            } else {
                routineHeadStr ='<tr>' +
                    '    <th>工作内容</th>' +
                    '    <th>计划开始时间</th>' +
                    '    <th>结果</th>' +
                    '</tr>'
                routineStr +=   '<tr>' +
                    '   <td>'+(routineItem.content?routineItem.contentPlan + '/<span class="ty-color-orange">'+routineItem.content+'</span>':routineItem.contentPlan)+'</td>'+
                    '   <td>'+(routineItem.timePlanTime || '--')+'</td>'+
                    '   <td>'+chargeRoutineFulfillment(routineItem)+'</td>'+
                    '</tr>'
            }

        }

        $("#" + showDialog).find(".routineWork thead").html(routineHeadStr)
        $("#" + showDialog).find(".routineWork tbody").html(routineStr)
        if (routineList.length > 0) {
            $("#" + showDialog).find(".routineWork").show()
        } else {
            $("#" + showDialog).find(".routineWork").hide()
        }


        // 获取当日所作的计划内工作
        var innerStr = ''
        var innerHeadStr = ''
        for (var l = 0; l < innerList.length; l++) {
            var fulfillment = Number(innerList[l].fulfillment)
            if (fulfillment === 1) {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    content: innerList[l].content,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment,
                    timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                    timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                    timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                    durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                    durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                    durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                }
            } else if (fulfillment === 2) {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    content: innerList[l].content,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment,
                    timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                    timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                    timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                    durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                    durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                    durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                    timeContinue: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD HH:mm:ss"):''),  //剩余部分计划开始时间
                    timeContinueDate: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD"):''),  //剩余部分计划开始日期
                    timeContinueTime: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("HH:mm"):''), //剩余部分计划开始分秒
                    continuePlan: -(-innerList[l].continuePlanHour - (innerList[l].continuePlanMinute/60).toFixed(2)),  //剩余部分计划耗时（小时，保存两位）
                    continuePlanMinute: (innerList[l].continuePlanMinute || 0),                                        //剩余部分计划耗时（分钟）
                    continuePlanHour: (innerList[l].continuePlanHour || 0),                                            //剩余部分计划耗时（小时）
                    reason: innerList[l].reason //关闭原因
                }
            } else if (fulfillment === 3) {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    content: innerList[l].content,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment,
                    timeFact: moment(innerList[l].timeFact).format("YYYY-MM-DD HH:mm:ss"),  //实际开始时间
                    timeFactDate: moment(innerList[l].timeFact).format("YYYY-MM-DD"),  //实际开始时间
                    timeFactTime: moment(innerList[l].timeFact).format("HH:mm"),  //实际开始时间
                    durationFact: -(-innerList[l].durationFactHour - (innerList[l].durationFactMinute/60).toFixed(2)),  //实际耗时（小时，保存两位）
                    durationFactMinute: innerList[l].durationFactMinute,                                                //实际耗时（分钟）
                    durationFactHour: innerList[l].durationFactHour,                                                    //实际耗时（小时）
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                    reason: innerList[l].reason //关闭原因
                }
            } else if (fulfillment === 4) {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment,
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,
                    timeContinue: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD HH:mm:ss"):''),  //剩余部分计划开始时间
                    timeContinueDate: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("YYYY-MM-DD"):''),  //剩余部分计划开始日期
                    timeContinueTime: (innerList[l].timeContinue?moment(innerList[l].timeContinue).format("HH:mm"):''), //剩余部分计划开始分秒//计划耗时（小时）
                    reason: innerList[l].reason //关闭原因
                }
            } else {
                var innerItem = {
                    type: 1, //类型:1-计划内,2-计划外,3-近日计划
                    contentPlan: innerList[l].contentPlan,
                    timePlan: moment(innerList[l].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                    timePlanDate: moment(innerList[l].timePlan).format("YYYY-MM-DD"),
                    timePlanTime: moment(innerList[l].timePlan).format("HH:mm"),
                    fulfillment: innerList[l].fulfillment || 0,
                    durationPlan: -(-innerList[l].durationPlanHour - (innerList[l].durationPlanMinute/60).toFixed(2)),  //计划耗时（小时，保存两位）
                    durationPlanMinute: innerList[l].durationPlanMinute,                                                //计划耗时（分钟）
                    durationPlanHour: innerList[l].durationPlanHour,                                                    //计划耗时（小时）
                    reason: innerList[l].reason //关闭原因
                }
            }
            innerItem.id = innerList[l].id
            innerItem.group = innerList[l].group
            if (isToday === 1) {
                innerHeadStr =  '<tr>' +
                    '   <th>工作内容</th>' +
                    '   <th>计划开始时间</th>' +
                    '   <th>计划耗时</th>' +
                    '</tr>'
                innerStr +=     '<tr>' +
                    '   <td>'+innerItem.contentPlan+'</td>'+
                    '   <td>'+moment(innerItem.timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                    '   <td>'+innerItem.durationPlanHour+'小时</td>'+
                    '</tr>'
            } else {
                innerHeadStr =  '<tr>' +
                    '   <th>工作内容</th>' +
                    '   <th>计划开始时间</th>' +
                    '   <th>计划耗时</th>' +
                    '   <th>结果</th> ' +
                    '</tr>'
                var content = innerItem.content?(innerItem.contentPlan + '/<span class="ty-color-orange">'+innerItem.content+'</span>'):innerItem.contentPlan
                innerStr +=     '<tr>' +
                    '   <td>'+content+'</td>'+
                    '   <td>'+moment(innerItem.timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                    '   <td>'+innerItem.durationPlan+'小时</td>'+
                    '   <td>'+chargeFulfillment(innerItem)+'</td>'+
                    '</tr>'
            }
        }
        $("#" + showDialog).find(".innerWork thead").html(innerHeadStr)
        $("#" + showDialog).find(".innerWork tbody").html(innerStr)
        if (innerList.length > 0) {
            $("#" + showDialog).find(".innerWork").show()
        } else {
            $("#" + showDialog).find(".innerWork").hide()
        }


        // 获取当日所做的计划外工作
        var outterList = data.outterList
        var outterStr = ''
        for (var j = 0; j < outterList.length; j++) {
            var outterItem = {
                type: 2,
                id: outterList[j].id,
                content: outterList[j].content,
                timeFact: moment(outterList[j].timeFact).format("YYYY-MM-DD HH:mm:ss"),
                timeFactDate: moment(outterList[j].timeFact).format("YYYY-MM-DD"),
                timeFactTime: moment(outterList[j].timeFact).format("HH:mm"),
                durationFactHour: outterList[j].durationFactHour,
                durationFactMinute: outterList[j].durationFactMinute,
                durationFact: -(-outterList[j].durationFactHour - (outterList[j].durationFactMinute/60).toFixed(2))
            }
            outterStr +=    '<tr data-id="'+outterItem.id+'">' +
                '   <td>'+outterItem.content+'</td>'+
                '   <td>'+outterItem.timeFactDate+'</td>'+
                '   <td>'+outterItem.timeFactTime+'</td>'+
                '   <td><span class="durationFact">'+outterItem.durationFact+'</span>小时</td>'+
                '</tr>'
        }
        $("#" + showDialog).find(".outterWork tbody").html(outterStr)
        if (outterList.length > 0) {
            $("#" + showDialog).find(".outterWork").show()
        } else {
            $("#" + showDialog).find(".outterWork").hide()
        }

        // 获取近日的工作计划
        var recentList = data.recentList
        var recentStr = ''
        for (var k = 0; k < recentList.length; k++) {
            var recentItem = {
                type: 3,
                id: recentList[k].id,
                contentPlan: recentList[k].contentPlan,
                timePlan: moment(recentList[k].timePlan).format("YYYY-MM-DD HH:mm:ss"),
                timePlanDate: moment(recentList[k].timePlan).format("YYYY-MM-DD"),
                timePlanTime: moment(recentList[k].timePlan).format("HH:mm"),
                durationPlanHour: recentList[k].durationPlanHour,
                durationPlanMinute: recentList[k].durationPlanMinute,
                durationPlan: -(-recentList[k].durationPlanHour - (recentList[k].durationPlanMinute/60).toFixed(2)),
                fulfillment: recentList[k].fulfillment
            }
            recentStr +=    '<tr data-id="'+recentItem.id+'">' +
                '   <td>'+recentItem.contentPlan+'</td>'+
                '   <td>'+moment(recentItem.timePlan).format("YYYY-MM-DD HH:mm")+'</td>'+
                '   <td>'+recentItem.durationPlan+'小时</td>'+
                '   <td class="hd">'+JSON.stringify(recentItem)+'</td>'+
                '</tr>'
        }
        $("#" + showDialog).find(".recentWork tbody").html(recentStr)
        if (recentList.length > 0) {
            $("#" + showDialog).find(".recentWork").show()
        } else {
            $("#" + showDialog).find(".recentWork").hide()
        }
    })
}

// creator: 张旭博，2022/6/27 17:10，某条记录的详情 - 评论处理
function handleComment(commentList, showDialog) {
    var str = ''
    for (var i in commentList) {
        var replyList = commentList[i].replyList
        var replyStr = ''
        if (replyList.length > 0) {
            replyStr =  '<div class="reply-avatar">'
            for (var j in replyList) {
                replyStr += '<div class="review-item">' +
                    '    <div class="review-title">' + replyList[j].createName + ' ' + moment(replyList[j].createDate).format("YYYY-MM-DD HH:mm:ss") + '回复' +'</div>' +
                    '    <div class="review-content">'+replyList[j].content+'</div>' +
                    '</div>'
            }
            replyStr +=     '</div>'
        }

        str +=  '<div class="review-item-avatar">' +
            '    <div class="review-avatar" data-id="'+commentList[i].id+'" data-user="'+commentList[i].creator+'">' +
            '        <div class="review-item">' +
            '            <div class="review-title">' + commentList[i].createName + ' ' + moment(commentList[i].createDate).format("YYYY-MM-DD HH:mm:ss") + '点评' +
            '            </div>' +
            '            <div class="review-content">'+commentList[i].content+'</div>' +
            '        </div>' +
            '    </div>' + replyStr +
            '</div>'
    }
    $("#" + showDialog).find(".reviewPart").html(str)
    if (commentList.length === 0) {
        $("#" + showDialog).find(".panel_review").hide()
    } else {
        $("#" + showDialog).find(".panel_review").show()
    }
}

// creator: 张旭博，2022/6/27 11:21，设置部门列表
function setAllDeparts(obj) {
    obj.html("");
    $.ajax({
        url: "../workAttendance/getCheckDepartment.do" ,
        beforeSend: function (){},
        success: function(data) {
            var list = data.organizations;
            var str =  '<option readonly="" value="">请选择部门</option>' ;
            if(list && list.length >0){
                for(var i=0 ; i < list.length; i++){
                    str += '<option value="'+ list[i]["id"] +'">'+ list[i]["name"] +'</option>'
                }
            }
            obj.html(str);
        },
        complete: function (){}
    })
}


// 时间控件初始化
laydate.render({
    elem: '#otherMonthWork',
    type: 'month',
    max: 0,
    showBottom: false,
    change: function (value, date, endDate) { //监听日期被切换
        lay('#otherMonthWork').val(value);
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
            getAttendanceDays(1, 20)
        })
    }
});
laydate.render({
    elem: '#yearReport',
    type: 'year',
    max: 0,
    showBottom: false,
    change: function (value) { //监听日期被切换
        $('#yearReport').val('');
        $('.laydate-year-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
            jumpPage("monthReportPre", function (){
                $("#monthReport").data("month", value)
                $(".page:visible .departSelect").val("")
                getAttendanceDays(1, 20)
            })
        })
    }
});
laydate.render({
    elem: '#monthReport',
    type: 'month',
    max: 0,
    showBottom: false,
    change: function (value) { //监听日期被切换
        $('#monthReport').val('');
        $('.laydate-month-list').on('click','li',function () {//利用的事件委托
            $('.layui-laydate').remove()
            jumpPage("monthReportPre", function (){
                $("#monthReport").data("month", value)
                $(".page:visible .departSelect").val("")
                getAttendanceDays(1, 20)
            })
        })
    }
});