/**
 * Created by Administrator on 2017/7/6.
 */
/**
 * Created by Administrator on 2017/7/4.
 */
$(function(){
    $(".ty-secondTab li").on("click",function () {
        $(this).siblings("li").removeClass("ty-active");
        $(this).addClass("ty-active");
        var index = $(this).index();
        $(".tplContainer").hide().eq(index).show();
        getCapitalAssertsList(1,15,1 - index,0);
    });

    $(".sort").click(function(){
        var index = 1 - $(".ty-secondTab li").index($(".ty-secondTab li.ty-active"));
        if($(this).hasClass("sort-active")){
            if($(this).children("i").hasClass("fa-long-arrow-down")){
                $(this).children("i").removeClass("fa-long-arrow-down").addClass("fa-long-arrow-up");
                if($(this).hasClass("sortName")){
                    getCapitalAssertsList(1,15,index,2);//固定资产名称顺序
                }else if($(this).hasClass("sortDate")){
                    getCapitalAssertsList(1,15,index,4);//入库日期顺序
                }
            }else{
                $(this).children("i").removeClass("fa-long-arrow-up").addClass("fa-long-arrow-down");
                if($(this).hasClass("sortName")){
                    getCapitalAssertsList(1,15,index,1);//固定资产名称倒序
                }else if($(this).hasClass("sortDate")){
                    getCapitalAssertsList(1,15,index,3);//入库日期倒序
                }
            }
        }else{
            $(".sort").removeClass("sort-active");
            $(this).addClass("sort-active");
            if($(this).hasClass("sortName")){
                getCapitalAssertsList(1,15,index,1);//固定资产名称默认倒序
            }else if($(this).hasClass("sortDate")){
                getCapitalAssertsList(1,15,index,3);//入库日期默认倒序
            }
        }
    });
    init();
});
/* creator：张旭博，2017-04-11 09:56:09，初始化 */
function init() {
    getCapitalAssertsListBySort();
}

/* creator：张旭博，2017-07-05 17:07:24，获取固定资产列表 */
function getCapitalAssertsList(currPage, pageSize, useState, rank) {
    var url = "getAllCapitalAsserts.do"; //获取列表接口
    //Integer currPage当前页, Integer pageSize每页最大条数,String useState使用状态0-报废,1-在用
    var data = {
        "currPage":currPage,
        "pageSize":pageSize,
        "useState":useState,
        "rank"    :rank
    };
    $.ajax({
        url : url ,
        data : data,
        success:function( data ){
            var assertsList = data["financeFixedAssets"];
            var currPage = data["currPage"];
            var totalPage = data["totalPage"];
            var useAssertsStr = "";
            var scrapAssertsStr = "";
            var jsonStr = JSON.stringify({ "useState":useState , "rank":rank }) ;
            setPage($("#ye") , currPage , totalPage , "capitalAsserts" , jsonStr ) ;
            // if(rank == 1 || rank == 2){
            //     var sortByName = function(x,y) {
            //         return x["name"].localeCompare(y["name"]);
            //     };
            //     assertsList.sort(sortByName);
            //     if(rank == 2){
            //         assertsList.reverse();
            //     }
            // }

            for(var i = 0 ;i < assertsList.length ;i++){

                var receiveState    = assertsList[i].receiveState;  //领用状态 0-正常 1-领用
                var lyghRecord      = assertsList[i].lyghRecord;    //领用记录 0-不可点击 1-可点击
                var wxRecord        = assertsList[i].wxRecord;      //维修记录 0-不可点击 1-可点击

                useAssertsStr +=   '<tr id="'+assertsList[i].id+'" farid="'+assertsList[i].farId+'">' +
                    '<td>'+assertsList[i].snPrefix.toString()+assertsList[i].sn.toString()+'</td>'+ //固定资产序号
                    '<td>'+assertsList[i].name+'</td>'+ //固定资产名称
                    '<td>'+assertsList[i].modeNumber+'</td>'+ //型号
                    '<td>'+ ( new Date( assertsList[i].buyDate).format('yyyy-MM-dd')) +'</td>'+ //采购日期
                    '<td>'+assertsList[i].buyerName+'</td>'+ //采购员
                    '<td>'+ (assertsList[i].originalValue || '')  +'</td>'+ //原值
                    '<td>'+ ( new Date( assertsList[i].storageDate).format('yyyy-MM-dd')) +'</td>'+ //入库日期
                    '<td>'+
                        ( receiveState === "1"?assertsList[i].receiverName : '' ) +
                    '</td>'+ //领用者(状态为领用时显示)
                    '<td>'+
                        ( receiveState === "1"? ( new Date( assertsList[i].receiveDate).format('yyyy-MM-dd')) : '' ) +
                    '</td>'+ //领用日期(状态为领用时显示)
                    '<td>'+assertsList[i].memo+'</td>'; //备注


                if(wxRecord == 0){
                    useAssertsStr +=    '<td><span class="colorC">查看</span></td>';
                }else if(wxRecord == 1){
                    useAssertsStr +=    '<td><span class="ty-color-blue" onclick="repairHistoryBtn($(this))">查看</span></td>';
                }
                if(lyghRecord == 0){
                    useAssertsStr +=    '<td><span class="colorC">查看</span></td>';
                }else if(lyghRecord == 1){
                    useAssertsStr +=    '<td><span class="ty-color-blue" onclick="giveBackScrapHistoryBtn($(this))">查看</span></td>';
                }
                if(receiveState == 0){
                    useAssertsStr +=    '<td>'+
                        '   <span class="ty-color-cyan"   onclick="toTakeBtn($(this))">领用</span>'+
                        '   <span class="colorC">归还</span>'+
                        '   <span class="ty-color-red"    onclick="scrapBtn($(this))">报废</span>'+
                        '   <span class="ty-color-orange" onclick="repairBtn($(this))">维修</span>'+
                        '</td>'+
                        '</tr>';
                }else if(receiveState == 1){
                    useAssertsStr +=    '<td>'+
                        '   <span class="colorC">领用</span>'+
                        '   <span class="ty-color-green"  onclick="giveBackBtn($(this))">归还</span>'+
                        '   <span class="colorC">报废</span>'+
                        '   <span class="colorC">维修</span>'+
                        '</td>'+
                        '</tr>';
                }
                scrapAssertsStr +=  '<tr id="'+assertsList[i].id+'" farid="'+assertsList[i].farId+'">' +
                    '<td>'+assertsList[i].snPrefix.toString()+assertsList[i].sn.toString()+'</td>'+ //固定资产序号
                    '<td>'+assertsList[i].name+'</td>'+ //固定资产名称
                    '<td>'+assertsList[i].modeNumber+'</td>'+ //型号
                    '<td>'+ ( new Date( assertsList[i].buyDate).format('yyyy-MM-dd')) +'</td>'+ //采购日期
                    '<td>'+assertsList[i].buyerName+'</td>'+ //采购员
                    '<td>'+assertsList[i].originalValue+'</td>'+ //原值
                    '<td>'+ ( new Date( assertsList[i].storageDate).format('yyyy-MM-dd'))  +'</td>'+ //入库日期
                    '<td>'+ ( new Date( assertsList[i].retirementDate ).format('yyyy-MM-dd'))  +'</td>'+ //报废日期
                    '<td>'+assertsList[i].memo+'</td>'+ //备注
                    '<td class="reasonHover">'+changeReason(assertsList[i].reason)+
                    '<div style="position:relative"><div class="reasonHoverCon">'+assertsList[i].reason+'</div></div>'+
                    '</td>'; //报废原因
                if(wxRecord == 0){
                    scrapAssertsStr +=    '<td><span class="colorC">查看</span></td>';
                }else if(wxRecord == 1){
                    scrapAssertsStr +=    '<td><span class="ty-color-blue" onclick="repairHistoryBtn($(this))">查看</span></td>';
                }
                if(lyghRecord == 0){
                    scrapAssertsStr +=    '<td><span class="colorC">查看</span></td></tr>';
                }else if(lyghRecord == 1){
                    scrapAssertsStr +=    '<td><span class="ty-color-blue" onclick="giveBackScrapHistoryBtn($(this))">查看</span></td></tr>';
                }
            }
            $("#usingList").html(useAssertsStr);
            $("#scrappedList").html(scrapAssertsStr);
        }
    })
}

/* creator：张旭博，2017-07-17 14:51:24，获取上次输入的固定资产信息 */
function getDataSource(mark,farId) {
    var url = "getDataSource.do";
    if(farId == undefined || farId == null){
        data = {
            "mark"      : mark
        }
    }else{
        var data = {
            "mark"      : mark,             //1-是新增固定资产时获取的默认固定资产序号；2-是归还固定资产时获取的默认归还者
            "farId"     : farId             //领用表ID
        };
    }
    $.ajax({
        url : url ,
        data : data,
        success:function( data ){
            if(data == undefined){
                $("#errorTip .tipWord").html("未获取到数据！");
                bounce.show($("#errorTip"));
            }else if(data == ""){
                if(farId == undefined || farId == null){
                    $("#newCapitalAssert .snNamePart1   ").val("");
                    $("#newCapitalAssert .snNamePart2   ").val("");
                }else{
                    $("#giveBack .returnDate").val();
                    $("#giveBack .returnerName").val();
                }

            }else{
                if(farId == undefined || farId == null){
                    $("#newCapitalAssert .snNamePart1   ").val(data["snPrefix"]);
                    $("#newCapitalAssert .snNamePart2   ").val(data["sn"]);
                }else{
                    $("#giveBack .returnDate").val(( new Date( data["createDate"] ).format('yyyy-MM-dd')) );
                    $("#giveBack .returnerName").val(data["receiverName"]);
                }
            }
        }
    });
}

/* creator：张旭博，2017-07-04 10:54:31，新增固定资产 - 按钮 */
function newCapitalAssertBtn(){
    $("#newCapitalAssert input").val('')
    if(hasAuthority(0) !== true){
        bounce.show($("#newCapitalAssert"));
        getDataSource(1);
        $("#newCapitalAssert .buyDate").val(getCurDate());
        $("#newCapitalAssert .storageDate").val(getCurDate());
        $('body').everyTime('0.5s','newCapitalAssert',function(){
            chargenewCapitalAsser() ;
        });
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}
// updater : 侯杏哲 2017-08-31 新增固定资产 - 固定资产名称焦点失去事件
function chargenewCapitalAsser(){
    if(($("#newCapitalAssert .name").val() == "")||($.trim($("#newCapitalAssert .name").val()) == "")){
        $("#sureNewCapitalAssertBtn").attr("disabled","disabled");
    }else {
        $("#sureNewCapitalAssertBtn").removeAttr("disabled");
    }
}

/* creator：张旭博，2017-07-05 16:44:08，新增固定资产- 确定 */
function sureNewCapitalAssert() {
    var url = "addCapitalAsserts.do";
    //String snName固定资产序号String，name固定资产名称【必输字段】，String modeNumber型号，String buyerName采购员姓名，Date buyDate采购日期(YYYY/MM/DD)，Date storageDate入库日期(YYYY/MM/DD)，String originalValue原值，String memo备注
    var snPrefix        = $("#newCapitalAssert .snNamePart1").val();
    var sn              = $("#newCapitalAssert .snNamePart2").val();
    var name            = $("#newCapitalAssert .name").val();
    var modeNumber      = $("#newCapitalAssert .modeNumber").val();
    var buyerName       = $("#newCapitalAssert .buyerName").val();
    var buyDate         = $("#newCapitalAssert .buyDate").val();
    var storageDate     = $("#newCapitalAssert .storageDate").val();
    var originalValue   = $("#newCapitalAssert .originalValue").val();
    var memo            = $("#newCapitalAssert .memo").val();

    var data = {
        "snPrefix"      : snPrefix,             //固定资产序号 前输入框
        "sn"            : sn,                   //固定资产序号 后输入框
        "name"          : name,                 //固定资产名称
        "modeNumber"    : modeNumber,           //型号
        "buyerName"     : buyerName,            //采购员
        "buyDate1"      : buyDate,              //采购日期
        "storageDate1"  : storageDate,          //入库日期
        "originalValue" : originalValue,        //原值
        "memo"          : memo                  //备注
    };
    if(sn == "" && snPrefix != ""){
        $("#F_errorTip .tipWord").html("固定资产序号格式不正确（没有以数字结尾）");
        bounce_Fixed.show($("#F_errorTip"));
    }else if((name == "")&&($.trim(name)=="")){/* 修改：lyt，2017-08-31 ，新增固定资产- 确定 */
        $("#F_errorTip .tipWord").html("请填写固定资产名称！");
        bounce_Fixed.show($("#F_errorTip"));
    }else{
        data.name=$.trim(name);
        $.ajax({
            url : url ,
            data : data,
            success:function( data ){
                var status = data["status"];
                if(status == 1){
                    $("#tip .tipWord").html("新增成功!");
                    bounce.show($("#tip"));
                    getCapitalAssertsListBySort();
                }else if(status == 0){
                    $("#F_errorTip .tipWord").html("新增失败!固定资产名称不能为空 ");
                    bounce_Fixed.show($("#F_errorTip"));
                }else if(status == 2){
                    $("#F_errorTip .tipWord").html("固定资产序号格式不正确（没有以数字结尾）");
                    bounce_Fixed.show($("#F_errorTip"));
                }else{
                    $("#errorTip .tipWord").html("系统错误，请重试！");
                    bounce.show($("#errorTip"));
                }
            }
        });
    }

}

/* creator：张旭博，2017-07-17 09:29:52，领用归还/维修记录 - 查看 */
function getRecordsDetail(flag) {
    var url = "getRecordsDetail.do";
    var id = $(".itemActive").attr("id");  //获取选中一条的固定资产id
    var data = {
        "id" : id,
        "flag" : flag       //标志：1-维修记录查看 2-领用记录查看
    };
    $.ajax({
        url : url ,
        data : data,
        success:function( data ){
            var financeAssetsMaintainList = data["financeAssetsMaintainList"];
            var recordStr1 = "";
            var recordStr2 = "";
            var index = 1;
            for(var i = 0; i<financeAssetsMaintainList.length; i++){
                if(flag == 1){
                    recordStr1 +=   '<tr id="'+financeAssetsMaintainList[i].id+'">' +                           //固定资产维修ID
                        '<td>'+index+'</td>'+                                                   //序号
                        '<td>'+ ( new Date( financeAssetsMaintainList[i].repairDate ).format('yyyy-MM-dd'))  +'</td>'+ //报修日期
                        '<td>'+financeAssetsMaintainList[i].faultReason+'</td>'+                //故障原因
                        '<td>'+financeAssetsMaintainList[i].nature+'</td>'+                     //维修性质
                        '<td>'+financeAssetsMaintainList[i].content+'</td>'+                    //维修内容
                        '<td>'+financeAssetsMaintainList[i].existProblem+'</td>'+               //存在问题
                        '<td>'+financeAssetsMaintainList[i].workerName+'</td>'+                 //维修工姓名
                        '<td>'+financeAssetsMaintainList[i].contactPhone+'</td>'+               //联系方式
                        '<td>'+financeAssetsMaintainList[i].contactCorp+'</td>'+                //维修单位
                        '</tr>';
                }else if(flag == 2){
                    recordStr2 +=   '<tr id="'+financeAssetsMaintainList[i].id+'">' +                           //固定资产领用归还表ID
                        '<td>'+index+'</td>'+                                                       //序号
                        '<td>'+ ( new Date(  financeAssetsMaintainList[i].receiveDate ).format('yyyy-MM-dd')) +'</td>'+//领用日期
                        '<td>'+financeAssetsMaintainList[i].receiveDeptName+'</td>'+            //领用部门
                        '<td>'+financeAssetsMaintainList[i].receiverName+'</td>'+               //领用者
                        '<td>'+ ( new Date( financeAssetsMaintainList[i].returnDate ).format('yyyy-MM-dd'))  +'</td>'+ //归还日期
                        '<td>'+financeAssetsMaintainList[i].returnerName+'</td>'+               //归还者
                        '<td>'+financeAssetsMaintainList[i].memo+'</td>'+                       //归还备注
                        '</tr>';
                }
                index  ++;
            }
            if(flag == 1){
                $("#repairHistoryList").html(recordStr1);
            }else if(flag == 2){
                $("#giveBackScrapHistoryList").html(recordStr2);
            }

        }
    });

}

/* creator：张旭博，2017-07-07 10:38:22，维修记录-查看-按钮 */
function repairHistoryBtn(selector) {
    selector.parent().parent().addClass("itemActive").siblings().removeClass("itemActive");
    getRecordsDetail(1);
    bounce.show($("#repairHistory"));
}

/* creator：张旭博，2017-07-07 10:40:44，领用/归还记录-查看-按钮 */
function giveBackScrapHistoryBtn(selector) {
    selector.parent().parent().addClass("itemActive").siblings().removeClass("itemActive");
    getRecordsDetail(2);
    bounce.show($("#giveBackScrapHistory"));
}

/* creator：张旭博，2017-07-15 15:12:49， 领用/归还/报废-提交*/
function addFinanceAssetsReceive(sign) {    //sign标志：1--领用2--归还3--报废
    var url = "addFinanceAssetsReceive.do";
    var id = $(".itemActive").attr("id");  //获取选中一条的固定资产id
    var farId = $(".itemActive").attr("farid");  //获取选中一条的固定资产领用归还ID
    var data = {
        "id" : id,
        "sign" : sign
    };
    switch(sign){
        //领用
        case 1:
            var receiveDate         = $.trim($("#toTake .receiveDate").val());
            var receiveDeptName     = $.trim($("#toTake .receiveDeptName").val());
            var receiverName        = $.trim($("#toTake .receiverName").val());
            data["receiveDate1"]    = receiveDate;                                      //领用时间
            data["receiveDeptName"] = receiveDeptName;                                  //领用部门名称
            data["receiverName"]    = receiverName;                                     //领用人
            if(receiveDate == ""){
                $("#F_errorTip .tipWord").html("请选择领用时间!");
                bounce_Fixed.show($("#F_errorTip"));
                return;
            }else if(receiverName == ""){
                $("#F_errorTip .tipWord").html("请输入领用人!");
                bounce_Fixed.show($("#F_errorTip"));
                return;
            }
            break;
        //归还
        case 2:
            var returnDate          = $.trim($("#giveBack .returnDate").val());
            var returnerName        = $.trim($("#giveBack .returnerName").val());
            var memo                = $.trim($("#giveBack .memo").val());
            data["farId"]           = farId;                                        //固定资产领用归还ID
            data["returnDate1"]     = returnDate;                                   //归还日期
            data["returnerName"]    = returnerName;                                 //归还人
            data["memo"]            = memo;                                         //归还备注
            if(returnDate == ""){
                $("#F_errorTip .tipWord").html("请选择归还时间!");
                bounce_Fixed.show($("#F_errorTip"));
                return;
            }else if(returnerName == ""){
                $("#F_errorTip .tipWord").html("请输入归还人!");
                bounce_Fixed.show($("#F_errorTip"));
                return;
            }
            break;
        //报废
        case 3:
            data["reason"]          = $("#scrap .reason").val();            //报废原因
            break;
    }
    $.ajax({
        url : url ,
        data : data,
        success:function( data ){
            var status = data["status"];
            switch (status) {
                case 1:
                    $("#tip .tipWord").html("领用成功!");
                    bounce.show($("#tip"));
                    getCapitalAssertsListBySort();
                    break;
                case 2:
                    $("#F_errorTip .tipWord").html("必输项领用人为空!");
                    bounce_Fixed.show($("#F_errorTip"));
                    break;
                case 3:
                    $("#F_errorTip .tipWord").html(" 必输项领用时间为空!");
                    bounce_Fixed.show($("#F_errorTip"));
                    break;
                case 5:
                    $("#F_errorTip .tipWord").html(" 必输项归还日期为空!");
                    bounce_Fixed.show($("#F_errorTip"));
                    break;
                case 6:
                    $("#F_errorTip .tipWord").html(" 必输项归还人为空!");
                    bounce_Fixed.show($("#F_errorTip"));
                    break;
                case 7:
                    $("#tip .tipWord").html("归还成功!");
                    bounce.show($("#tip"));
                    getCapitalAssertsListBySort();
                    break;
                case 9:
                    $("#tip .tipWord").html("报废成功!");
                    bounce.show($("#tip"));
                    $(".ty-secondTab li").eq(1).click();
                    break;
                default:
                    $("#tip .tipWord").html("未知的返回值");
                    bounce.show($("#tip"));
            }

        }
    });


}

/* creator：张旭博，2017-07-05 17:00:53，操作-领取-按钮 */
function toTakeBtn(selector){
    if(hasAuthority(0) !== true){
        $("#toTake input").val("");
        $("#toTake .receiveDate").val(getCurDate());
        bounce.show($("#toTake"));
        selector.parent().parent().addClass("itemActive").siblings().removeClass("itemActive");
        $('body').everyTime('0.5s','taken',function(){
            chargeTaken() ;
        });

    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}
/* creator : 侯杏哲  2017-08-31 判断领用的确定是否可用  */
function chargeTaken(){
    var i = 0;
    if($("#toTake .receiveDate").val() == "" || $.trim($("#toTake .receiverName").val()) == ""){i++}
    if(i == 0){
        $("#sureToTakeBtn").removeAttr("disabled")
    }else{
        $("#sureToTakeBtn").attr("disabled","disabled")
    }
}

/* creator：张旭博，2017-07-05 17:02:29，弹窗-领取-确定 */
function sureToTake() {
    addFinanceAssetsReceive(1);
}

/* creator：张旭博，2017-07-05 17:00:53，操作-归还-按钮 */
function giveBackBtn(selector){
    if(hasAuthority(0) !== true){
        selector.parent().parent().addClass("itemActive").siblings().removeClass("itemActive");
        var farId = selector.parent().parent().attr("farid");
        getDataSource(2,farId);
        $("#giveBack .memo").val("");
        bounce.show($("#giveBack"));
        $('body').everyTime('0.5s','giveback',function(){
            chargeGiveBack() ;
        });
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}
/* creator : 侯杏哲 2017-08-31 判断 操作归还确实按钮 是否可点击  */
function chargeGiveBack(){
    var i = 0;
    if( $.trim($("#giveBack .returnDate").val()) == "" || $.trim($("#giveBack .returnerName").val()) == ""){i++}
    if(i == 0){
        $("#sureGiveBackBtn").removeAttr("disabled")
    }else{
        $("#sureGiveBackBtn").attr("disabled","disabled")
    }
}
/* creator：张旭博，2017-07-05 17:02:29，弹窗-归还-确定 */
function sureGiveBack() {
    addFinanceAssetsReceive(2);
}

/* creator：张旭博，2017-07-05 17:00:53，操作-报废-按钮 */
function scrapBtn(selector){
    if(hasAuthority(0) !== true){
        selector.parent().parent().addClass("itemActive").siblings().removeClass("itemActive");
        $("#scrap textarea").val("");
        bounce.show($("#scrap"));
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}

/* creator：张旭博，2017-07-05 17:02:29，弹窗-报废-确定 */
function sureScrap() {
    addFinanceAssetsReceive(3);
}

/* creator：张旭博，2017-07-05 17:00:53，操作-维修-按钮 */
function repairBtn(selector){
    if(hasAuthority(0) !== true){
        selector.parent().parent().addClass("itemActive").siblings().removeClass("itemActive");
        bounce.show($("#repair"));
        $("#repair input,#repair textarea").val("");
        $("#repair .repairDate").val(getCurDate());
        $('body').everyTime('0.5s','repair',function(){
            chargeRepear()
        });
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}
/* creator : 侯杏哲 2017-08-30 判断 操作维修弹框确定按钮是否可用  */
function chargeRepear(){
    if($.trim($("#repairDate").val()) == "" || $.trim($("#repair .content").val()) == "" || $.trim($("#repair .contactCorp").val()) == "" ){
        $("#sureRepairBtn").attr("disabled","disabled")
    }else{
        $("#sureRepairBtn").removeAttr("disabled")
    }
}
/* creator：张旭博，2017-07-05 17:02:29，弹窗-维修-确定 */
function sureRepair() {
    var url = "addFinanceAssetsMaintain.do";

    var id              = $(".itemActive").attr("id");              //获取选中一条的固定资产id
    var repairDate      = $("#repair .repairDate").val();           //报修日期
    var faultReason     = $("#repair .faultReason").val();          //故障原因
    var nature          = $("#repair .nature").val();               //维修性质
    var content         = $("#repair .content").val();              //维修内容
    var existProblem    = $("#repair .existProblem").val();         //存在问题
    var workerName      = $("#repair .workerName").val();           //维修工姓名
    var contactPhone    = $("#repair .contactPhone").val();         //联系方式
    var contactCorp     = $("#repair .contactCorp").val();          //维修单位名称

    var data = {
        "id"            : id,
        "repairDate1"   : repairDate,       //报修日期
        "faultReason"   : faultReason,      //故障原因
        "nature"        : nature,           //维修性质
        "content"       : content,          //维修内容
        "existProblem"  : existProblem,     //存在问题
        "workerName"    : workerName,       //维修工姓名
        "contactPhone"  : contactPhone,     //联系方式
        "contactCorp"   : contactCorp       //维修单位名称
    };
    if(repairDate == ""){
        $("#F_errorTip .tipWord").html("必输项报修日期为空!");
        bounce_Fixed.show($("#F_errorTip"));
    }else if(content == ""){
        $("#F_errorTip .tipWord").html("必输项维修内容为空!");
        bounce_Fixed.show($("#F_errorTip"));
    }else if(contactCorp == ""){
        $("#F_errorTip .tipWord").html("必输项维修单位名称为空!");
        bounce_Fixed.show($("#F_errorTip"));
    }else{
        $.ajax({
            url : url ,
            data : data,
            success:function( data ){
                loading.close();
                var status = data["status"];
                switch (status) {
                    case 1:
                        $("#tip .tipWord").html("新增维修记录成功!");
                        bounce.show($("#tip"));
                        getCapitalAssertsListBySort();
                        break;
                    case 2:
                        $("#F_errorTip .tipWord").html("必输项维修内容为空!");
                        bounce_Fixed.show($("#F_errorTip"));
                        break;
                    case 3:
                        $("#F_errorTip .tipWord").html("必输项维修单位名称为空!");
                        bounce_Fixed.show($("#F_errorTip"));
                        break;
                    case 4:
                        $("#F_errorTip .tipWord").html("必输项报修日期为空!");
                        bounce_Fixed.show($("#F_errorTip"));
                        break;
                    case 0:
                        $("#F_errorTip .tipWord").html("新增维修记录失败!");
                        bounce_Fixed.show($("#F_errorTip"));
                        break;
                    default:
                        $("#tip .tipWord").html("未知的返回值");
                        bounce.show($("#tip"));
                }

            }
        })
    }

}

function getCapitalAssertsListBySort() {
    var index = 1 - $(".ty-secondTab li").index($(".ty-secondTab li.ty-active"));
    if($(".sort-active").length>0){
        if($(".sort-active").children("i").hasClass("fa-long-arrow-up")){
            if($(".sort-active").hasClass("sortName")){
                getCapitalAssertsList(1,15,index,2);//固定资产名称顺序
            }else if($(".sort-active").hasClass("sortDate")){
                getCapitalAssertsList(1,15,index,4);//入库日期顺序
            }
        }else{
            if($(".sort-active").hasClass("sortName")){
                getCapitalAssertsList(1,15,index,1);//固定资产名称倒序
            }else if($(".sort-active").hasClass("sortDate")){
                getCapitalAssertsList(1,15,index,3);//入库日期倒序
            }
        }
    }else{
        getCapitalAssertsList(1,15,index,0);//入库日期默认倒序
    }
}



//---------------------辅助方法---------------------//
function changeReason(reason) {
    if(reason.length>8){
        reason = reason.substring(0,8)+'...';
    }
    return reason;
}

//elem:目标元素。由于laydate.js封装了一个轻量级的选择器引擎，因此elem还允许你传入class、tag但必须按照这种方式 '#id .class'
// 日期控件
// 新增固定资产日期
laydate.render({elem: '#buyDate'});
laydate.render({elem: '#storageDate'});
laydate.render({elem: '#receiveDate'});
laydate.render({elem: '#returnDate'});
laydate.render({elem: '#repairDate'});