var editZhiObj = null;
var editDepartObj = null ;
$(function(){
    //creator:李玉婷 date: 2017/12/26 设置默认格式，设置下拉框使用select2插件
    $.fn.select2.defaults.set("theme", "default");
    $("#news_sex").select2({minimumResultsForSearch: -1});
    $("#user_edu").select2({minimumResultsForSearch: -1});
    $("#user_marry").select2({minimumResultsForSearch: -1});
    $("#is_ordinary").select2({minimumResultsForSearch: -1});
    $(".manage").select2({minimumResultsForSearch: -1});
    $(".superior").select2({minimumResultsForSearch: -1});
    //必填项为空时保存按钮不能点击
    $('body').everyTime('0.5s','newStuff',function(){
        if($("#userName").val() !== ""&& isMobileMethod($("#mobile").val())){
            if($.trim($("#idCard").val())=== ""|| isCardNo($("#idCard").val())){
                if($.trim($("#email").val())=== ""|| isEmail($("#email").val())){
                    $("#saveStuff").prop("disabled",false)
                }else{
                    $("#saveStuff").prop("disabled",true)
                }
            }else{
                $("#saveStuff").prop("disabled",true)
            }
        }else{
            $("#saveStuff").prop("disabled",true)
        }
    });
    setInterval('setDepatShow()',500);
    $("#saveUser").validate({
        rules: {
            userName : {
                required:true
            },
            email : {
                email:true
            },
            mobile : {
                required:true,
                isMobile:true
            }
        }
    })
    $("#birthday").val(get);
    $("#date1").val(getCurDate());
});
// 手机号码验证
jQuery.validator.addMethod("isMobile", function(value, element) {
    var length = value.length;
    var mobile = /^(13[0-9]{9})|(18[0-9]{9})|(14[0-9]{9})|(17[0-9]{9})|(15[0-9]{9})$/;
    return this.optional(element) || (length == 11 && mobile.test(value));
}, "请正确填写手机号码");

function isMobileMethod(mobile){
    var regPhone = /^(13[0-9]|15[012356789]|18[0123456789]|147|145|17[0-9])\d{8}$/;
    if(regPhone.test(mobile)){
        return true;
    }else{
        return false;
    }
}

// 选择职位
function getZhi(obj){
    var oid = obj.children(".hd").html();
    var oname = obj.children(":eq(0)").html();
    obj.siblings(":eq(0)").children(".activeZhiId").html(oid);
    obj.attr("style","background:#36C6D3; border-color:#36C6D3; color:#fff;  ");
    obj.siblings("span").attr("style","background:#fff; border-color:#666; color:#333; ");
    $("#zhiName").val(oname);
    $("#zhiId").val(oid);
}

function getLevelName(level_num){
    var level = "";
    switch(level_num)
    {
        case 1 : level = "一"; break;
        case 2 : level = "二"; break;
        case 3 : level = "三"; break;
        case 4 : level = "四"; break;
        case 5 : level = "五"; break;
        case 6 : level = "六"; break;
        case 7 : level = "七"; break;
        case 8 : level = "八"; break;
        default: level = "子"; break;
    }
    level += "级部门";
    return level ;
}

//	单击部门，召唤出子级部门
function getKidsDepart(obj){
    obj.attr("style","background:#36C6D3; border-color:#36C6D3; color:#fff;  ");
    obj.siblings("span").attr("style","background:#fff; border-color:#666; color:#333; ");
    var oid = obj.children(".oid").html();
    var oname = obj.children(":eq(0)").html();
    $("#oId").val(oid) ;
    var activeIdObj = obj.parents(".levelItem").find(".activeId") ;
    var level_num = obj.siblings(".depatlevel_num").html();
    level_num= parseInt(level_num) + 1 ;
    var level = "";
    level = getLevelName(level_num);
    if( oid == "" || oid == null){
        alert("输入不合法！");
    }else{
        activeIdObj.html(oid) ;
        obj.parents(".levelItem").nextAll().remove() ;
        $(".departItem").removeClass("active") ; obj.addClass("active") ;
        $.ajax({
            url:"checkSonDepartment.do",
            data:{ "id":oid   },
            type:"post",
            dataType:"json",
            success:function(data){
                var str = "";
                if(data.length> 0){
                    str = "<div class='levelItem'>" +
                        "<div style='display:none; '>" +
                        "<span class='activeId' ></span>" +
                        "<span class='isKidsShow' ></span>" +
                        "</div>" +
                        "<a class='depatlevel'>"+ level +"</a>" +
                        "<a class='depatlevel_num hd'>"+ level_num +"</a>";
                    str += "<div>" ;
                    for( var i =0 ; i< data.length; i++ ){
                        str += "<span  class='departItem'  onclick='getKidsDepart($(this))'>" +
                            "<span>"+ data[i]["name"] +"</span>" +
                            "<span class='hd oid'>"+ data[i]["id"] +"</span>" +
                            "<span class='hd pid'>"+ data[i]["pid"] +"</span>" +
                            "</span>"
                    }
                    str += "<div class='clr'></div></div> " ;

                    obj.parents().$("#levelCon").append(str) ;
                }
            },
            error:function(){alert("连接错误！");}
        });
        setDepatShow() ;
    }
}

// 显示要编辑的内容
function conShow(str){
    $("#"+str).show().siblings().hide();
}
// 下拉框toggle
function toggleSelect(source, type , obj ){
    var num = obj.attr("info");
    if (num == 0) {
        if(type == 1){
            if (source == 'new'){
                $("#deparCon").show();
            }else{

            }
        }else{
            if (source == 'new'){
                $("#zhiCon").show();
            }else{

            }
        }
        obj.attr("info","1");
    } else{
        if(type == 1){
            if (source == 'new'){
                $("#deparCon").hide();
            }else{

            }
        }else{
            if (source == 'new'){
                $("#zhiCon").hide();
            }else{

            }
        }
        obj.attr("info","0");
    }
    return false;
}
function toggleSe(obj){
    var toggleObj = obj.prev();
    toggleSelect(toggleObj);
}

// 设置选择部门的路径
function setDepatShow(){
    var DepartLink = "";
    $(".activeId").each(function(){
        var activeIdObj = $(this);
        var activeId = activeIdObj.html();
        activeIdObj.parents(".levelItem").find(".departItem").each(function(){
            var id = $(this).children(":eq(1)").html();
            if (activeId == id) {
                DepartLink +=  $(this).children(":eq(0)").html() + " - " ;
            }
        })
    });
    $("#oName").val(DepartLink.substring(0 , DepartLink.length-2));


    // 职位中权限的显示、
    var activeZhiId = $("#activeZhiId").html();
    if(activeZhiId == "" || activeZhiId == null || activeZhiId == "  "){
        $("#zhiControl").hide();
    }else{
        $("#zhiControl").show();
    }
}
function isEmail(email) {
    var reg = /^[a-zA-Z0-9_-]+@([a-zA-Z0-9]+\.)+(com|cn|net|org)$/;
    if(reg.test(email)) {
        return true;
    }else{
        return false;
    }
}

//update:李玉婷 date:2018/1/18  点击保存后将按钮改成正在保存、灰色、不可点击状态
function newStuff() {
    var manager = $(".manage").val().split("-")[0];
    var managerCode = $(".manage").val().split("-")[1];
    $(".manage").prev().val(managerCode);
    $(".manage").prev().prev().val(manager);
    $("body").stopTime();
    $("#saveStuff").prop("disabled",true);
    $("#saveStuff").html("正在保存");
    $('#saveUser').submit();
}
function isCardNo(card) {
    // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if(reg.test(card)) {
        return true;
    }else{
        return false;
    }
}
//上传头像
function setImagePreview() {
    var docObj = document.getElementById("imgFile");
    var imgObjPreview = document.getElementById("preview");
    if (docObj.files && docObj.files[0]) {
        /*火狐下，直接设img属性*/
        //imgObjPreview.style.display = 'inline-block';
        //imgObjPreview.style.width = '150px';
        //imgObjPreview.style.height = '150px';
        /*imgObjPreview.src = docObj.files[0].getAsDataURL();*/
        /*火狐7以上版本不能用上面的getAsDataURL()方式获取，需要一下方式*/
        imgObjPreview.src = window.URL.createObjectURL(docObj.files[0]);
        docObj.removeAttribute('disabled');
    } else {
        /*IE下，使用滤镜*/
        docObj.select();
        docObj.setAttribute("disabled","disabled");
        var imgSrc = document.selection.createRange().text;
        var localImagId = document.getElementById("localImag");
        /*必须设置初始大小*/
        //localImagId.style.width = "150px";
        //localImagId.style.height = "150px";
        /*图片异常的捕捉，防止用户修改后缀来伪造图片*/
        try {
            localImagId.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)";
            localImagId.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src           = imgSrc;
        } catch (e) {
            alert("您上传的图片格式不正确，请重新选择!");
            return false;
        }
        imgObjPreview.style.display = ' ';
        document.selection.empty();
    }
    return true;
}
//creator:lyt date:2018/12/8 10:40 删除头像
function uploadPhoto(){
    $("#imgFile").removeAttr("disabled");
    $('#imgFile').click();
}
//creator:lyt date:2017/12/8 10:40 删除头像
function deleteUerImg(){
    $("#preview").attr("src","${pageContext.request.contextPath}/assets/oralResource/user.png");
    $("#imgFile").attr("disabled","disabled");
}
// 时间控件初始化
laydate.render({elem: '#birthday', value: new Date().format('yyyy-MM-dd')});
laydate.render({elem: '#date1', value: new Date().format('yyyy-MM-dd')});