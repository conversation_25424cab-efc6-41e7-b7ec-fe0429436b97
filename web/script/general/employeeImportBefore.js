var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#iknowTip"));
bounce_Fixed3.cancel();
$(function () {
    getList();
    $("tbody").on('click', ".btn",function() {
        var name = $(this).data('name');
        var thisObj = $(this);
        switch (name) {
            case "initUpdate": // 修改
                var info = thisObj.parents("tr").data("info");
                $("#updateUser").data("type", 1);
                $("#updateUser").data("obj", thisObj);
                $(".userForm .userName").val(info.userName);
                $(".userForm .userPhone").val(info.mobile);
                $(".userForm .userName").data("oldName",info.userName);
                $(".userForm .userPhone").data("oldMobile",info.mobile);
                $("#importUpdateUser").prop("disabled",true);
                bounce.show($("#updateUser"));
                setUpdateUserTimer();
                break;
            case "initDel": // 删除
                $("#delUser").data("obj", thisObj);
                $("#delUser").data("type", '1');
                bounce.show($("#delUser"));
                break;
            case "stepNext": // 下一步
                bounce.show($("#nextStep"));
                break;
            case "update": // 修改
                var id = thisObj.parents("tr").data("id");
                var userName = thisObj.parents("tr").find("td").eq(0).html();
                var mobile = thisObj.parents("tr").find("td").eq(1).html();
                $("#updateUser").data("obj", thisObj);
                $("#updateUser").data("id", id);
                $("#updateUser").data("type", 2);
                $(".userForm .userName").val(userName);
                $(".userForm .userPhone").val(mobile);
                $(".userForm .userName").data("oldName",userName);
                $(".userForm .userPhone").data("oldMobile",mobile);
                $("#importUpdateUser").prop("disabled",true);
                bounce.show($("#updateUser"));
                setUpdateUserTimer();
                break;
            case "del": // 删除
                $("#delUser").data("obj", thisObj);
                $("#delUser").data("type", '2');
                bounce.show($("#delUser"));
                break;
        }
    });
    $(".importOpertion").on('click', ".btn",function() {
        var name = $(this).data('type');
        var thisObj = $(this);
        switch (name) {
            case "stepNext": // 下一步
                var list = [];
                $(".importNoSave table tbody tr").each(function(){
                    var item = $(this).data("info");
                    list.push(item);
                });
                var sum = list.length;
                list = JSON.stringify(list);
                $.ajax({
                    url: "../userImport/allImportUserEnter.do",
                    data:{
                        "usersList": list,
                        "importSum": sum
                    },
                    success: function (data) {
                        var noSaveSum = data.falseImportSum;
                        if (noSaveSum > 0) {
                            $("#nextStep #noSaveMbSum").html(noSaveSum);
                            $(".safeCondition").show();
                        } else {
                            $(".safeCondition").hide();
                        }
                        bounce.show($("#nextStep"));
                    }
                });
                break;
            case "clearNoSave": // 放弃
                $("#clearUser").data("type", 1);
                bounce.show($("#clearUser"));
                break;
            case "cancelSave": // 放弃
                $("#clearUser").data("type", 2);
                bounce.show($("#clearUser"));
                break;
        }
    });
    $(".bounce").on('click', ".ty-btn",function() {
        var name = $(this).data('name');
        switch (name) {
            case "nextStep": // 下一步
                var list = [];
                var userList = $(".importNoSave").data("trueUserList");
                $(".importNoSave table tbody tr").each(function(){
                    var item = $(this).data("info");
                    list.push(item);
                });
                for (var i in userList) {
                    list.push(userList[i]);
                }
                var sum = list.length;
                list = JSON.stringify(list);
                var json = {
                    usersList: list,
                    importSum: sum
                }
                saveImportList(json);
                break;
            case "clearUser": // 放弃
                var type = $("#clearUser").data("type");
                bounce.cancel();
                if (type == '1') {
                    getList();
                } else {
                    turnCancel(2);
                }
                break;
            case "lastSaveSure": // 确定保存
                lastSaveBefore();
                break;
            case "judgeImport": // 批量导入尚未完成是否继续
                var flag = $(".unfinishedForm .fa-circle").data("type")
                if (flag == '1') {
                    $(".importing").show();
                    $(".importNoSave").hide();
                    $(".brfore").hide();
                    bounce.cancel();
                    var res = $("#importNotCompleted").data("userData");
                    getImportUserList(res);
                } else if (flag == '0') {
                    bounce.cancel();
                    turnCancel(1);
                } else {
                    layer.msg("请选择上次的批量导入尚未完成是否继续！");
                }
                break;
        }
    });
    $(".unfinishedForm").on('click', ".fa",function() {
        $(this).addClass("fa-circle").removeClass("fa-circle-o");
        $(this).parent().siblings(".changeDot").find("span").addClass("fa-circle-o").removeClass("fa-circle");
    });
    $('#uploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{},
        buttonText:'浏 览',
        fileSizeLimit:10240,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader:"../export/newImportUser.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item userListUpload" style="display: none">' +
        '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
        '</div>',
        onUploadStart:function(){
        },
        onInit:function(){},
        onSelect:function (file) {
            $('.userListUpload:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data);
            var status = data.status;
            if (status == '1') {
                var userList = data.trueUserList;
                var falseList = data.falseUserList;
                $(".importNoSave").data("trueUserList", userList);
                $(".importNoSave table tbody").html("");
                bounce.cancel();
                if (falseList && falseList.length > 0) {
                    loading.close() ;
                    var html = '';
                    for (var a=0; a<falseList.length;a++) {
                        var item = {
                            "userName": falseList[a].userName,
                            "mobile": falseList[a].mobile
                        }
                        html +=
                            '<tr data-info=\''+ JSON.stringify(item) +'\'>' +
                            '    <td>'+ handleNull(falseList[a].userName) +'</td>' +
                            '    <td>'+ handleNull(falseList[a].mobile) +'</td>' +
                            '    <td>' +
                            '          <span class="ty-color-blue btn" data-name="initUpdate">修改</span>' +
                            '          <span class="ty-color-red btn" data-name="initDel">删除</span>' +
                            '    </td>' +
                            '</tr>';
                    }
                    $(".importNoSave table tbody").html(html);
                    $(".importNoSave .initAll").html(data.importSum);
                    $(".importNoSave .initWrong").html(data.falseImportSum);
                    $(".ty-secondTab").hide();
                    $(".importNoSave").show().siblings().hide();
                } else {
                    var importList = [];
                    for (var b=0; b<userList.length;b++) {
                        var item = {
                            "userName": userList[b].userName,
                            "mobile": userList[b].mobile
                        }
                        importList.push(item);
                    }
                    var json = {
                        usersList: JSON.stringify(importList),
                        importSum: data.importSum
                    }
                    saveImportList(json);
                }
            } else {
                loading.close() ;
                $('#select_btn_1').val("");
                $('.userListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                bounce_Fixed.show($("#importantTip"));
            }
        } ,
        onCancel:function(file){
        }
    });
});
// creator:hxz 2018-08-07 不导入，直接使用系统按钮
function getList() {
    var data = {
        "isDuty": 1,            // 是否在职  1表示在职，2表示离职
        "edus": "",                // 学历
        "gender": "",            // 性别
        "departments": "",  // 部门
        "posts": "",              // 职位
        "marry": "",              // 婚姻类型
        "birthday1": "",      // 出生日期
        "birthday2": "",      // 出生日期
        "onDutyDate1": "",  // 入职时间
        "onDutyDate2": "",  // 入职时间
        "quantum": "20",          // 每页条数
        "pageNumber": 1     // 当前页数
    };
    $(".importing").hide();
    $(".importNoSave").hide();
    $(".brfore").show();
    $.ajax({
        url: "../general/screen.do",//isDuty （1表示在职，2表示离职）、quantum （每页多少条）、pageNumber（当前页）
        data: data,
        success: function (data) {
            var users = data["users"];
            var totalPage = data["totalPage"];//总页数
            var cur = data["pageNumber"];//当前页
            var onPostListStr = '';
            for (var i = 0; i < users.length; i++) {
                var handelStr = '<span class="ty-color-gray" >查看</span>' +
                    '<span class="ty-color-gray">编辑</span>' +
                    '<span class="ty-color-gray">指纹</span>';
                var roleType = users[i].roleCode;
                var leaderNameStr = "";
                // if (roleType != "staff") {
                //     leaderNameStr = "总经理"
                // } else {
                    leaderNameStr = users[i].leaderName;
                // }
                onPostListStr += '<tr id="' + users[i].userID + '">' +
                    '   <td>' + users[i].userName + '</td>' +    //姓名
                    '   <td>' + chargeSex(users[i].gender) + '</td>' +      //性别
                    '   <td>' + users[i].mobile + '</td>' +      //手机号
                    '   <td>' + users[i].departName + '</td>' +  //部门
                    '   <td>' + users[i].postName + '</td>' +    //职位
                    '   <td>' + users[i].leaderName + '</td>' +  //直接上级
                    '   <td>' + chargeDegree(users[i].degree) + '</td>' +      //最高学历
                    '   <td>' + handelStr + '</td>' +
                    '</tr>';
            }
            $(".brfore tbody").html(onPostListStr);
        }
    });
}

function clearImport() {
    $.ajax({
        "url": "../org/deleteUserList.do ",
        success: function (res) {
            var status = res["status"];
            if (status == 1) {
                layer.msg("操作成功");
                location.href = "";
            } else {
                layer.msg("操作失败");
            }
        }
    })
}
// creator:hxz 2018-08-07 不导入，直接使用系统按钮
function startBtn() {
    $("#tipType").val("1");
    $("#btn2").html("确定");
    bounce.show($("#tip"));
    $("#tipMs").html("以后再增加新职工，您需逐个录入 \n" + "开始正式使用系统?");
}
// creator:hxz 2018-08-07 导入职工
function importBtn() {
    $('#uploadFile').val("");
    $('.fileFullName').html("尚未选择文件");
    $(".importNoSave").data("trueUserList", "");
    // 检查是否设置了 部门
    $.ajax({
        "url": "../general/getAllManageAndPosts.do",
        "data": {"orgType": 2},
        success: function (res) {
            var organizations = res["organizations"];
            if (organizations && organizations.length > 0) {
                judgeUnfinished();
            } else {
                loading.close() ;
                $("#tipType").val("2");
                $("#btn2").html("继续批量导入");
                bounce.show($("#tip"));
                $("#tipMs").html("您尚未完成部门设置，继续批量导入可能会导致您工作量增加");
            }
        },
        complete:function(){   }
    });

}
// creator: 李玉婷，2020-10-18 10:29:50，判断是否有未完成的导入
function judgeUnfinished(){
    $.ajax({
        url:"../userImport/unfinishedImportUser.do",
        success:function (data) {
            var userList = data.userImportList;
            if (userList.length > 0) {
                $('.unfinishedForm .fa').attr("class","fa fa-circle-o");
                $('#importNotCompleted').data("userData",data);
                bounce.show($('#importNotCompleted'));
            } else {
                $('#select_btn_1').val("");
                $('.userListUpload').remove();
                $('#leading .fileFullName').html("尚未选择文件");
                bounce.show($('#leading'));
            }
        }
    });
}
// creator: 李玉婷，2020-10-18 14:06:42，放弃
function turnCancel(type){
    $.ajax({
        url: "../userImport/giveUpImportUser.do",
        success: function (data) {
            var state = data.status;
            if (state == '1')
                if (type == '1') {
                    $('#uploadFile #select_btn_1').val("");
                    $('.fileFullName').html("尚未选择文件");
                    $(".importNoSave").data("trueUserList", "");
                    bounce.show($('#leading'));
                }else{
                    getList();
                }
        }
    })
}
// creator: 李玉婷，2020-10-21 16:42:51，下一步-数据保存
function saveImportList(userData) {
    $.ajax({
        url: "../userImport/saveImportUser.do",
        data: userData,
        success: function (data) {
            $(".importing").show();
            $(".importNoSave").hide();
            $(".brfore").hide();
            bounce.cancel();
            getImportUserList(data);
        }
    });
}
// creator:hxz 2018-08-07 提示框 确定
function tipOk() {
    var tipType = $("#tipType").val();
    switch (tipType) {
        case "1" : // 直接启用系统
            confirmOrg();
            break;
        case "2" : // 继续导入
            bounce.show($('#leading'));
            break;
        case "3" : // 一键清空
            clearImport();
            break;
        case "4" : // 导入后确定
            confirmOrg();
            break;
        default:
    }
}
// creator:hxz 2018-08-07 导入
function confirmOrg() {
    bounce.cancel();
    $.ajax({
        "url": $.webRoot+"/org/confirmOrg.do",
        success: function (res) {
            var status = res["status"];
            if (status == 1) {
                location.href = "../general/employeeIndex.do";
            } else {
                layer.msg("正式启用系统失败！");
            }
        }
    })
}
// creator:hxz 2018-08-07 导入
function importOk() {
    if ($(".userListUpload").length <= 0) {
        $("#knowTip .knowWord").html('您需选择一个文件后才能“导入”！');
        bounce_Fixed.show($("#knowTip"))
    } else {
        loading.open() ;
        $(".userListUpload a").click();
    }
}
// creator: 李玉婷，2020-12-14 13:56:41，最后保存确定
function lastSaveBefore() {
    $.ajax({
        url: "../userImport/completeImportUser.do",
        success: function (data) {
            var state = data.status;
            if (state == 1) {
                bounce.cancel();
                location.href = "../general/employeeIndex.do";
            } else {
                layer.msg("正式启用系统失败！");
            }
        }
    })
}
// creator: 李玉婷，2020-08-27 09:49:20，修改 - 一键清空
function clearTxt(obj) {
    obj.prev().val("");
}
/* creator：张旭博，2017-11-06 09:23:16，转换男女 */
function chargeSex(sex) {
    switch (sex) {
        case "1":
            return "男";
            break;
        case "0":
            return "女";
            break;
        default:
            return '';
    }
}
// creator: 张旭博，2018-05-11 10:19:31，学历转换
function chargeDegree(degreeCode) {
    var degree = '';
    switch (degreeCode) {
        case 1:
            degree = "研究生";
            break;
        case 2:
            degree = "本科";
            break;
        case 3:
            degree = "大专";
            break;
        case 4:
            degree = "中专或高中";
            break;
        case 5:
            degree = "其它";
            break;
        default:
            degree = '';
            break;
    }
    return degree
}
function isMobileMethod(mobile){
    var regPhone = /^(13[0-9]|15[012356789]|18[0123456789]|147|145|17[0-9])\d{8}$/;
    if(regPhone.test(mobile)){
        return true;
    }else{
        return false;
    }
}



