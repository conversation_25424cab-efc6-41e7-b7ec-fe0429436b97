var screenConditions = ""; //保存筛选条件
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#employeeUpdate"));
bounce_Fixed2.cancel();
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed3.show($("#seeBaseInfor"));
bounce_Fixed3.cancel();
let matchType = false;
let useInfo = null;
$(function () {
    useInfo = auth.getOrg();
// 处理报销修改请求提交后的反馈
    var success =getUrlParam('success');
    var error =getUrlParam("error");
    if($.trim(success) === ""){
        if($.trim(error) !== ""){
            $("#mt_tip_ms").html(error);
            bounce.show( $("#mtTip") ) ;
        }
    }else{
        if($.trim(error) === ""){
            $("#mt_tip_ms").html(success);
            bounce.show( $("#mtTip") ) ;
        }
    }
    $(".importing").hide();$(".importNoSave").hide();
    $(".main").show();
    getOrgSonOrgs();
    //筛选多选框绑定事件
    $(".ty-form-checkbox").on("click",function (e) {
        e.stopPropagation();
        $(this).toggleClass("ty-form-checked");
    });
    //二级标签栏切换事件
    $(".ty-secondTab li").on("click",function () {
        $(this).addClass("ty-active").siblings("li").removeClass("ty-active");
        var index = $(this).index();
        if(index === 0){
            $("#third_n").html("在职列表");
            $("#customQueryBtn").show();
        }else{
            $("#third_n").html("离职列表").parent().nextAll().remove();
            $("#customQueryBtn").hide();
            $(".searchCon .ty-form-checkbox").removeClass("ty-form-checked");
        }
        $(".tblContainer").eq(index).show().siblings().hide();
        defineGetList(1,20,index+1);
    });
    //点击查询按钮
    $("#queryEmployeeBtn").on("click",function () {
        var index = Number($(".ty-secondTab li").index($(".ty-secondTab li.ty-active"))) + 1;
        defineGetList(1,20,index);
    });
    //限制筛选条件出生日期、入职日期不同时为空时，按钮变暗
    $('body').everyTime('0.5s','queryEmployeeBtn',function(){
        var i = 0;
        var birthday1 = $.trim($("#birthday1").val());
        var birthday2 = $.trim($("#birthday2").val());
        var onDutyDate1 = $.trim($("#onDutyDate1").val());
        var onDutyDate2 = $.trim($("#onDutyDate2").val());
        if(birthday1 === ""&&birthday2 !== "") {
            i ++;
        }
        if(birthday1 !== ""&&birthday2 === "") {
            i ++;
        }
        if(onDutyDate1 === ""&&onDutyDate2 !== "") {
            i ++;
        }
        if(onDutyDate1 !== ""&&onDutyDate2 === "") {
            i ++;
        }
        if(i === 0){
            $("#queryEmployeeBtn").prop("disabled",false)
        }else{
            $("#queryEmployeeBtn").prop("disabled",true)
        }
    });
    //获取在职列表
    defineGetList(1,20,1);
    $("#handleForm").validate({
        rules: {
            userName : {
                required:true
            },
            mobile : {
                required:true,
                isMobile:true
            }
        }
    });
    $("#handleUpdateForm").validate({
        rules: {
            userName : {
                required:true
            },
            mobile : {
                required:true,
                isMobile:true
            }
        }
    })
    $.extend($.validator.messages, {
        required: "这是必填字段"
    })
    $(".importOpertion").on('click', ".btn",function() {
        var name = $(this).data('type');
        var thisObj = $(this);
        switch (name) {
            case "stepNext": // 下一步
                var list = [];
                $(".importNoSave table tbody tr").each(function(){
                    var item = $(this).data("info");
                    list.push(item);
                });
                var sum = list.length;
                list = JSON.stringify(list);
                let param = {
                    "usersList": list,
                    "importSum": sum
                }
                // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
                let storage = $(".part_own").data("storage")
                if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
                $.ajax({
                    url: "../userImport/allImportUserEnter.do",
                    data: param,
                    success: function (data) {
                        var noSaveSum = data.falseImportSum;
                        if (noSaveSum > 0) {
                            $("#nextStep #noSaveMbSum").html(noSaveSum);
                            $(".safeCondition").show();
                        } else {
                            $(".safeCondition").hide();
                        }
                        bounce.show($("#nextStep"));
                    }
                });
                break;
            case "clearNoSave": // 放弃
                $("#clearUser").data("type", 1);
                bounce.show($("#clearUser"));
                break;
            case "cancelSave": // 放弃
                $("#clearUser").data("type", 2);
                bounce.show($("#clearUser"));
                break;
        }
    });
    $(".bounce").on('click', ".ty-btn",function() {
        var name = $(this).data('name');
        switch (name) {
            case "nextStep": // 下一步
                var list = [];
                var userList = $(".importNoSave").data("trueUserList");
                $(".importNoSave table tbody tr").each(function(){
                    var item = $(this).data("info");
                    list.push(item);
                });
                for (var b=0; b<userList.length;b++) {
                    var item = {
                        userName: userList[b].userName,
                        mobile: userList[b].mobile
                    }
                    list.push(item);
                }
                var sum = list.length;
                list = JSON.stringify(list);
                var json = {
                    usersList: list,
                    importSum: sum
                }
                saveImportList(json);
                break;
            case "clearUser": // 放弃
                clearUser()
                break;
            case "lastSaveSure": // 确定保存
                lastSave();
                break;
            case "judgeImport": // 批量导入尚未完成是否继续
                var flag = $(".unfinishedForm .fa-circle").data("type")
                if (flag == '1') {
                    $(".importing").show();
                    $(".importNoSave").hide();
                    $(".main").hide();
                    bounce.cancel();
                    $(".importOpertion").siblings("button.ty-btn").hide()
                    var res = $("#importNotCompleted").data("userData");
                    getImportUserList(res);
                } else if (flag == '0') {
                    bounce.cancel();
                    turnCancel(1);
                } else {
                    layer.msg("请选择上次的批量导入尚未完成是否继续！");
                }
                break;
        }
    });
    $("tbody").on('click', ".btn",function() {
        var name = $(this).data('name');
        var thisObj = $(this);
        switch (name) {
            case "initUpdate": // 修改
                var info = thisObj.parents("tr").data("info");
                $("#updateUser").data("type", 1);
                $("#updateUser").data("obj", thisObj);
                $(".userForm .userName").val(info.userName);
                $(".userForm .userPhone").val(info.mobile);
                $(".userForm .userName").data("oldName",info.userName);
                $(".userForm .userPhone").data("oldMobile",info.mobile);
                $("#importUpdateUser").prop("disabled",true);
                bounce.show($("#updateUser"));
                setUpdateUserTimer();
                break;
            case "initDel": // 删除
                $("#delUser").data("obj", thisObj);
                $("#delUser").data("type", '1');
                bounce.show($("#delUser"));
                break;
            case "stepNext": // 下一步
                bounce.show($("#nextStep"));
                break;
            case "update": // 修改
                var id = thisObj.parents("tr").data("id");
                var userName = thisObj.parents("tr").find("td").eq(0).html();
                var mobile = thisObj.parents("tr").find("td").eq(1).html();
                $("#updateUser").data("obj", thisObj);
                $("#updateUser").data("id", id);
                $("#updateUser").data("type", 2);
                $(".userForm .userName").val(userName);
                $(".userForm .userPhone").val(mobile);
                $(".userForm .userName").data("oldName",userName);
                $(".userForm .userPhone").data("oldMobile",mobile);
                $("#importUpdateUser").prop("disabled",true);
                bounce.show($("#updateUser"));
                setUpdateUserTimer();
                break;
            case "del": // 删除
                $("#delUser").data("obj", thisObj);
                $("#delUser").data("type", '2');
                bounce.show($("#delUser"));
                break;
        }
    });
    $(".unfinishedForm").on('click', ".fa",function() {
        $(this).addClass("fa-circle").removeClass("fa-circle-o");
        $(this).parent().siblings(".changeDot").find("span").addClass("fa-circle-o").removeClass("fa-circle");
    });
    $('#sysUseUploadFile').Huploadify({
        auto:false ,
        fileTypeExts:'*.xls;*.xlsx;',
        multi:false,
        formData:{
            module: '职工导入',
            userId: sphdSocket.user.userID
        },
        buttonText:'浏 览',
        fileSizeLimit:10240,  // 40M = ( 40 * 1024 ) KB
        showUploadedPercent:false,
        showUploadedSize:false,
        queueSizeLimit:1,
        removeTimeout:99999999,
        removeCompleted:true,
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        itemTemplate: '<div id="${fileID}" class="uploadify-queue-item userListUpload" style="display: none">' +
        '<div class="uploadify_bottom"><div><a class="uploadbtn" href="javascript:void(0);"></a></div></div>' +
        '</div>',
        onUploadStart:function(){
        },
        onInit:function(){},
        onSelect:function (file) {
            $('.userListUpload:not(:last)').remove();
            $('#leading .fileFullName').html(file.name);
        },
        onUploadComplete:function(file,data){
        },
        onUploadError:function(file){
            layer.msg( file.name + "上传失败!");
        },
        onUploadSuccess:function(file,data,pathArr){
            var data = JSON.parse(data)
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename //路径（包含文件类型）
            var fileUid = data.fileUid
            let param =  {
                filePath: path
            };
            // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
            let storage = $(".part_own").data("storage")
            if (storage && storage.source === 1) {
                param.sonOid = storage.sonOid;
            }
            $.ajax({
                url: '../export/newImportUser.do',
                data: param,
                success: function (data) {
                    var status = data.status
                    $(".importNoSave table tbody").html("");
                    // 下面一句处理中枢管控自带的按钮消失
                    $(".importOpertion").siblings("button.ty-btn").hide()
                    if (status == '1') {
                        // 取消删除程序
                        cancelFileDel({type: 'fileUid', fileUid: fileUid})
                        var userList = data.trueUserList;
                        var falseList = data.falseUserList;
                        $(".importNoSave").data("trueUserList", userList);
                        $(".importNoSave table tbody").html("");
                        bounce.cancel();
                        if (falseList && falseList.length > 0) {
                            loading.close() ;
                            var html = '';
                            for (var a=0; a<falseList.length;a++) {
                                var item = {
                                    "userName": falseList[a].userName,
                                    "mobile": falseList[a].mobile
                                }
                                html +=
                                    '<tr data-info=\''+ JSON.stringify(item) +'\'>' +
                                    '    <td>'+ handleNull(falseList[a].userName) +'</td>' +
                                    '    <td>'+ handleNull(falseList[a].mobile) +'</td>' +
                                    '    <td>' +
                                    '          <span class="ty-color-blue btn" data-name="initUpdate">修改</span>' +
                                    '          <span class="ty-color-red btn" data-name="initDel">删除</span>' +
                                    '    </td>' +
                                    '</tr>';
                            }
                            $(".importNoSave table tbody").html(html);
                            $(".importNoSave .initAll").html(data.importSum);
                            $(".importNoSave .initWrong").html(data.falseImportSum);
                            $(".ty-secondTab").hide();$(".main").hide();
                            $(".importNoSave").show().siblings().hide();
                        } else {
                            var importList = [];
                            for (var b=0; b<userList.length;b++) {
                                var item = {
                                    "userName": userList[b].userName,
                                    "mobile": userList[b].mobile
                                }
                                importList.push(item);
                            }
                            var json = {
                                usersList: JSON.stringify(importList),
                                importSum: data.importSum
                            }
                            saveImportList(json);
                        }
                    } else {
                        loading.close() ;
                        $('#select_btn_1').val("");
                        $('.userListUpload').remove();
                        $('#leading .fileFullName').html("尚未选择文件");
                        bounce_Fixed3.show($("#importantTip"));
                    }
                }
            })

        } ,
        onCancel:function(file){
        }
    });
});
/* creator：张旭博，2017-11-06 09:19:29，办理入职 */
function joinStaff(){
    var userType = chargeRole("超管")
    if (!userType) { // 3 表示总务
        $("#oName").attr('info', '0');
        $("#zhiName").attr('info', '0');
        $("#zhiCon").hide();
        $("#deparCon").hide();
        $("#deparCon .levelItem").not(":first").remove();
        document.getElementById("handleForm").reset();
        $("#handleForm #mobile-error").html('');
        $("#handleForm input[type='hidden']").val('');
        $("#handleForm select[name='pid']").html('');
        //$("#handleForm select[name='managerCode']").html('');
        $("#handleForm select[name='postID']").html('');
        $("#handleForm select[name='department']").html('');
        $("#joinTime").val(new Date().format('yyyy/MM/dd'));
        $.ajax({
            "url": $.webRoot+"/recruit/getSelect.do",
            "data": "",
            success: function (res) {
                var userList = res.userList; // 直接上级
                var manageList = res.manageList; // 所属高管列表
                var postList = res.postList; // 职位列表
                var departmentList = res.departmentList; // 部门列表
                if (userList.length > 0) {
                    var uHtml = '';
                    var smallSuper = 0
                    for (var u=0;u<userList.length;u++){
                        if(userList[u].roleCode === 'smallSuper') {
                            smallSuper = userList[u].userID
                        }
                        uHtml +=
                            '<option value="'+ userList[u].userID +'">'+ userList[u].userName +'</option>';
                    }
                    $("#handleForm select[name='pid']").html(uHtml);
                    if (smallSuper) {
                        $("#handleForm select[name='pid']").val(smallSuper);
                    }
                }
                /*if (manageList.length > 0) {
                    var mHtml = '';
                    for (var m=0;m<manageList.length;m++){
                        mHtml +=
                            '<option value="'+ manageList[m].userID + '-' + manageList[m].managerCode +'">'+ changeWorkFuc(manageList[m].managerCode) +'</option>';
                    }
                    $("#handleForm select[name='managerCode']").html(mHtml);
                }*/
                // if (postList.length > 0) {
                //     var pHtml = '';
                //     for (var m=0;m<postList.length;m++){
                //         pHtml +=
                //             '<span class="zhiItem" onclick="getZhi($(this))">' +
                //             '    <span>'+ postList[m].name +'</span>' +
                //             '    <span class="hd">' + postList[m].id + '</span>' +
                //             '</span>';
                //     }
                //     $("#join_zhi").html(pHtml);
                // }
                var postStr = '<option value="">------请选择------</option>';
                if (postList.length > 0) {
                    for (var m in postList){
                        postStr += '<option value="'+postList[m].id+'">'+postList[m].name+'</option>';
                    }
                }
                $("#handleForm select[name='postID']").html(postStr);
                if (departmentList.length > 0) {
                    var dHtml = '';
                    for (var m=0;m<departmentList.length;m++){
                        if (departmentList[m].level == 1) {
                            dHtml +=
                                '<span class="departItem" onclick="getKidsDepart($(this))">' +
                                '    <span>'+ departmentList[m].name +'</span>' +
                                '    <span class="hd oid">'+ departmentList[m].id +'</span>' +
                                '</span>';
                        }
                    }
                    $("#join_depart").html(dHtml);
                }
                bounce.show($('#handleEntry'));
                bounce.everyTime('0.5s','newStuff',function(){
                    if($("#userName").val() !== ""&& testMobile($("#mobile").val())){
                        $("#saveStuff").prop("disabled",false);
                    }else{
                        $("#saveStuff").prop("disabled",true);
                    }
                });
            } ,
            error:function(){
                $("#mt_tip_ms").html("链接失败，请刷新重试！");
                bounce.show( $("#mtTip") ) ;
            }
        });
    }else{
        bounce.show( $("#mtTip") ) ;
        $("#mt_tip_ms").html("您没有此权限！");
    }
}
// creator: 李玉婷，2019-11-14 08:54:04，验证手机号
jQuery.validator.addMethod("isMobile", function(value, element) {
    var length = value.length;
    return this.optional(element) || (length == 11 && testMobile(value));
}, "请正确填写手机号码");
function isCardNo(card) {
    // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if(reg.test(card)) {
        return true;
    }else{
        return false;
    }
}
function isMobileMethod(mobile){
    var regPhone = /^(13[0-9]|15[012356789]|18[0123456789]|147|145|17[0-9]|191)\d{8}$/;
    if(regPhone.test(mobile)){
        return true;
    }else{
        return false;
    }
}
// creator: 李玉婷，2019-11-11 14:02:38，办理入职
function newStuff() {
    var params = {};
    $("#handleForm [require]").each(function(){
        var name = $(this).attr('name');
        params[name] = $(this).val();
        if (name === 'postID') {
            if (params[name] === '') {
                params.postName = ''
            } else {
                params.postName = $(this).find("option:selected").html()
            }
        }
    });
    /*var mul = $("#handleForm [name='managerCode']").val();
    var manager = mul.split("-")[0];
    var managerCode = mul.split("-")[1];
    params.manager = manager
    params.managerCode = managerCode*/
    $.ajax({
        "url": $.webRoot+"/general/addUser.do",
        "data": params,
        success: function (res) {
            var success = res;
            if (success == '1') {
                bounce.cancel();
                defineGetList(1,20,1);
            }else if(res == 3){
                $("#errorTip .tipWord").html('姓名超出或者手机号已存在！');
                bounce_Fixed.show($("#errorTip"));
            } else {
                layer.msg('提交失败！');
            }
        },
        error:function(){
            $("#mt_tip_ms").html("链接失败，请刷新重试！");
            bounce.show( $("#mtTip") ) ;
        }
    });
}
/* creator：张旭博，2017-11-06 09:19:00，办理离职 */
function forLeaving( selector ){
    var userType = chargeRole("超管");
    var userID = selector.parents("tr").data('id');
    if (userType) { // 3 表示总务
        bounce.show( $("#mtTip") ) ;
        $("#mt_tip_ms").html("您没有此权限！");
    }else{
        openWindow("toForLeaving.do?userId="+ userID,'800','700');
    }
}
/* creator：张旭博，2017-11-02 17:26:51，筛选 */
function defineGetList(pageNumber,quantum,isDuty) {
    var edus = '',
        edusName = '',
        gender = '',
        genderName = '',
        departments = '',
        posts = '',
        marry = '',
        marryName = '',
        conditionNum = 0;
    $("#education .ty-form-checked").each(function (index) {
        if(index === 0){
            edusName = $(this).children("span").html();
            edus = $(this).children("span").data('val');
        }else{
            edusName += ","+$(this).children("span").html();
            edus += ","+$(this).children("span").data('val');
        }
    });
    $("#gender .ty-form-checked").each(function (index) {
        if(index === 0){
            gender = $(this).children("span").attr("value");
            genderName = $(this).children("span").html();
        }else{
            gender += ","+$(this).children("span").attr("value");
            genderName += ","+$(this).children("span").html();
        }
    });
    $("#department .ty-form-checked").each(function (index) {
        if(index === 0){
            departments = $(this).children("span").html();
        }else{
            departments += ","+$(this).children("span").html();
        }
    });
    $("#post .ty-form-checked").each(function (index) {
        if(index === 0){
            posts = $(this).children("span").html();
        }else{
            posts += ","+$(this).children("span").html();
        }
    });
    $("#marry .ty-form-checked").each(function (index) {
        if(index === 0){
            marry = $(this).children("span").attr("value");
            marryName = $(this).children("span").html();
        }else{
            marry += ","+$(this).children("span").attr("value");
            marryName += ","+$(this).children("span").html();
        }
    });
    var birthday1 = $("#birthday1").val();
    var birthday2 = $("#birthday2").val();
    var onDutyDate1 = $("#onDutyDate1").val();
    var onDutyDate2 = $("#onDutyDate2").val();
    var line = [edusName,genderName,departments,posts,marryName,birthday1+'~'+birthday2,onDutyDate1+'/'+onDutyDate2];
    var baseArr = [edusName,genderName,marryName,birthday1+'~'+birthday2];
    let oids = [];
    screenConditions = line;
    for(var i = 0 ;i<line.length;i++) {
        if(line[i] === "" || typeof(line[i]) === "undefined" ||line[i] === "~"||line[i] === "/") {
            line.splice(i,1);
            i= i-1;
        }
    }
    for(var i = 0 ;i<baseArr.length;i++) {
        if(baseArr[i] !== "" && typeof(baseArr[i]) !== "undefined" && baseArr[i] !== "~") {
            conditionNum++;
        }
    }
    line = line.join("、");
    $("#third_n").parent().nextAll().remove();
    if($.trim(line) !== ""){
        $("#third_n").parent().after('<span class="nav_"> / </span><span class="navTxt"><i class=""></i><span>'+line+'</span></span>' );
    }
    $("#belongOrg .ty-form-checked").each(function (){
        let val = $(this).children("span").data("val");
        oids.push(val);
    });
    var data = {};
    if(isDuty === 1){
        data = {
            "isDuty":isDuty,            // 是否在职  1表示在职，2表示离职
            "edus":edus,                // 学历
            "gender":gender,            // 性别
            "departments":departments,  // 部门
            "posts":posts,              // 职位
            "marry":marry,              // 婚姻类型
            "birthday1":birthday1,      // 出生日期
            "birthday2":birthday2,      // 出生日期
            "onDutyDate1":onDutyDate1,  // 入职时间
            "onDutyDate2":onDutyDate2,  // 入职时间
            "quantum":quantum,          // 每页条数
            "pageNumber":pageNumber     // 当前页数
        }
        if(oids.length > 0){
            oids = oids.join(",");
            data.oids = oids;
        }
    }else{
        data = {
            "isDuty":isDuty,            // 是否在职  1表示在职，2表示离职
            "edus":'',                // 学历
            "gender":'',            // 性别
            "departments":'',  // 部门
            "posts":'',              // 职位
            "marry":'',              // 婚姻类型
            "birthday1":'',      // 出生日期
            "birthday2":'',      // 出生日期
            "onDutyDate1":'',  // 入职时间
            "onDutyDate2":'',  // 入职时间
            "quantum":quantum,          // 每页条数
            "pageNumber":pageNumber     // 当前页数
        }
    }
    $.ajax({
        url:"../general/screen.do" ,//isDuty （1表示在职，2表示离职）、quantum （每页多少条）、pageNumber（当前页）
        data:data,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; },
        success:function( data ){
            var users = data["users"];
            var totalPage = data["totalPage"];//总页数
            var cur = data["pageNumber"];//当前页
//                $(".employeeQuery").css("display","inline-block");$(".tools").show();
            //设置分页
            $("#ye_employee").html("");
            var jsonStr = JSON.stringify({"isDuty":1}) ;
            setPage( $("#ye_employee") , cur ,  totalPage , "employee" ,jsonStr );
            var onPostListStr = '', orgNameStr = ``;
            for(var i=0;i<users.length;i++){
                orgNameStr = ``;
                if (conditionNum > 0 && users[i].submitState != '1'){
                    continue;
                }
                if (matchType) {
                    orgNameStr = `<td> ${handleNull(users[i].orgName)} </td>`;
                }
                var handelStr = '';
                if(isDuty === 1){
                    //update:李玉婷，高管没有办理离职，普通员工有办理离职
                    if(users[i].roleCode === "staff"){
                        handelStr = '<span class="ty-color-blue" onclick="employeeInforView('+users[i].userID+')">个人信息</span>'+
                            '<span class="ty-color-blue" onclick="handleUpdate($(this))">档案管理</span>' +
                            '<span class="ty-color-gray">指纹</span>'+
                            '<span class="ty-color-red"  onclick="forLeaving($(this))">办理离职</span>';
                    }else if(users[i].roleCode === "agent"){
                        handelStr = '<span class="ty-color-gray">个人信息</span>'
                            '<span class="ty-color-gray">档案管理</span>' +
                            '<span class="ty-color-gray">指纹</span>';
                    } else {
                        handelStr = '<span class="ty-color-blue" onclick="employeeInforView('+users[i].userID+')">个人信息</span>'+
                            '<span class="ty-color-blue" onclick="handleUpdate($(this))">档案管理</span>' +
                            '<span class="ty-color-gray">指纹</span>';
                    }
                }else{
                    handelStr = '<span class="ty-color-gray">复职</span>';
                }
                var sexStr = '',degreeStr = '', leaderStr = '';
                if (users[i].submitState == '1') {
                    sexStr = chargeSex(users[i].gender);
                    degreeStr = chargeDegree(users[i].degree);
                }
                leaderStr = users[i].roleCode == 'super' ? '':users[i].leaderName;
                onPostListStr   +=  '<tr data-id="'+users[i].userID+'">'+
                    '   <td>'+users[i].userName+'</td>'+    //姓名
                    '   <td>'+sexStr+'</td>'+      //性别
                    '   <td>'+users[i].mobile+'</td>'+ orgNameStr+      //手机号
                    '   <td>'+users[i].departName+'</td>'+  //部门
                    '   <td>'+users[i].postName+'</td>'+    //职位
                    '   <td>'+leaderStr+'</td>'+  //直接上级
                    '   <td>'+degreeStr+'</td>'+      //最高学历
                    '   <td>' +handelStr+ '</td>'+
                    '</tr>';
            }
            $(".tblContainer tbody").eq(isDuty-1).html(onPostListStr);
        },
        error:function(){
            $("#mt_tip_ms").html("系统错误，请重试!");
            bounce.show( $("#mtTip") ) ;
        } ,
        complete:function(){ loading.close() }
    }) ;
}
// creator: 李玉婷，2019-11-09 09:41:10，个人信息预览
function employeeInforView(id) {
    $('#employeeInforView').data('employeeId', id);
    $("#employeeBaseInfo [require]").each(function () {
        $(this).html('');
    });
    $('#employeeBaseInfo .contectList') .html('');
    $('#eduHashMap') .html('');
    $('#workHashMap') .html('');
    $.ajax({
        "url": "/userInfo/userInfoPreview.do",
        "data": {
            'passiveUserId': id
        },
        success: function (res) {
            var success = res['success'];
            if (success == '1') {
                var userInfo = res.data.user;
                var checkState = userInfo.submitState;
                $("#employeeUserName").html(userInfo.userName);
                if(checkState == '0') {
                    $("#employeeEditBtn").prop('disabled', true);
                    $("#skills").hide();
                }else{
                    var userContacts = res.data.userContacts;
                    var eduList = res.data.personalEducations;
                    var workList = res.data.personnelOccupations;
                    var skills = 0;
                    $("#employeeEditBtn").prop('disabled', false);
                    $("#employeeBaseInfo [require]").each(function () {
                        var name = $(this).data('name');
                        switch (name) {
                            case 'gender':
                                var str = "";
                                if (handleNull(userInfo['gender']) != ""){
                                    str = chargeSex(userInfo['gender']) + '<i> / </i>';
                                }
                                $(this).html(str);
                                break;
                            case 'marry':
                                var str = "";
                                if (handleNull(userInfo['marry']) != ""){
                                    str = chargeMarry(userInfo['marry']) + '<i> / </i>';
                                }
                                $(this).html(str);
                                break;
                            case 'birthday':
                                if (handleNull(userInfo[name]) != '') {
                                    $(this).html(new Date(userInfo[name]).format('yyyy年MM月dd日生') + '<i> / </i>');
                                } else{
                                    $(this).html('');
                                }
                                break;
                            case 'degree':
                                if (chargeDegree(userInfo['degree']) != '') {
                                    $(this).html(chargeDegree(userInfo['degree']) + '<i> / </i>');
                                } else{
                                    $(this).html('');
                                }
                                break;
                            default:
                                if (handleNull(userInfo[name]) != "") {
                                    $(this).html(userInfo[name] + '<i> / </i>');
                                } else {
                                    $(this).html('');
                                }
                                break;
                        }
                    });
                    $("#employeeBaseInfo [need]").each(function () {
                        var name = $(this).data('name');
                        if (handleNull(userInfo[name]) != "") {
                            skills++;
                            $(this).html(userInfo[name]);
                        } else {
                            $(this).html('');
                        }
                    });
                    if (skills > 0) {
                        $("#skills").show();
                    } else {
                        $("#skills").hide();
                    }
                    var html = '';
                    var contactList= {
                        'mobile':[{
                            name: 'mobile',
                            code: userInfo.mobile
                        }],
                        'qq':[],
                        'email':[],
                        'weixin':[],
                        'weibo':[],
                        'defined':[]
                    };
                    var typeList = [
                        'mobile','qq','email','weixin','weibo','defined'
                    ]
                    for (var t=0; t<userContacts.length;t++) {
                        if (userContacts[t].open == true){
                            var type = userContacts[t].type;
                            switch (type) {
                                case '1':
                                    contactList['mobile'].push(userContacts[t]);
                                    break;
                                case '2':
                                    contactList['qq'].push(userContacts[t]);
                                    break;
                                case '3':
                                    contactList['email'].push(userContacts[t]);
                                    break;
                                case '4':
                                    contactList['weixin'].push(userContacts[t]);
                                    break;
                                case '5':
                                    contactList['weibo'].push(userContacts[t]);
                                    break;
                                case '9':
                                    contactList['defined'].push(userContacts[t]);
                                    break;
                            }
                        }
                    }
                    for(var y=0;y<typeList.length;y++){
                        var name = typeList[y];
                        if(contactList[name] && contactList[name].length > 0) {
                            if (name == 'defined') {
                                for (var t=0; t<contactList[name].length;t++){
                                    html +=
                                        ' <li>' +
                                        '    <div class="tctImg ">'+ contactList[name][t].name +':</div>' +
                                        '    <div class="ty-left">'+
                                        '    <p title="'+ contactList[name][t].code +'">'+ contactList[name][t].code +'</p>' +
                                        '</div></li>';
                                }
                            }else{
                                html +=
                                    ' <li>' +
                                    '    <div class="tctImg ' + name + '"></div>' +
                                    '    <div class="ty-left">';
                                for (var t = 0; t < contactList[name].length; t++) {
                                    html += '<p title="'+ contactList[name][t].code +'">' + contactList[name][t].code + '</p>';
                                }
                                html += '</div></li>';
                            }
                        }
                    }
                    $('#employeeBaseInfo .contectList') .html(html);
                    if (eduList.length > 0){
                        var ehtml = '';
                        for(var e=0;e<eduList.length;e++){
                            if (eduList[e].deleted == false) {
                                ehtml +=
                                '<div class="areaBood">' +
                                '<div class="charact">' +
                                '    <div class="mmTtl">'+ new Date(eduList[e].beginTime).format('yyyy/MM') +'-'+ new Date(eduList[e].endTime).format('yyyy/MM') +' </div>' +
                                '    <div class="timeSlot">' +
                                '        <span>'+ handleNull(eduList[e].collegeName) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(eduList[e].departmentName) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(eduList[e].major) +'</span>' +
                                '        <i>|</i>' +
                                '        <span>'+ handleNull(eduList[e].degreeDesc) +'</span>' +
                                '    </div>' +
                                '</div>' +
                                '<div class="charact">' +
                                '    <div class="mmTtl">专业描述：</div>' +
                                '    <div class="mmCon">' +
                                '        <p>'+ handleNull(eduList[e].majorDesc) +'</p>' +
                                '    </div>' +
                                '</div>' +
                                '<div class="charact">' +
                                '    <div class="mmTtl">补充说明：</div>' +
                                '    <div class="mmCon">' +
                                '        <p>'+ handleNull(eduList[e].memo) +'</p>' +
                                '    </div>' +
                                '</div></div> ';
                            }
                        }
                        $('#eduHashMap') .html(ehtml);
                    }
                    if (workList.length > 0){
                        var whtml = '';
                        for (var w=0; w<workList.length;w++) {
                            if (workList[w].deleted == false) {
                                whtml +=
                                    '<div class="areaBood">' +
                                    '<div class="charact">' +
                                    '    <div class="mmTtl">'+ new Date(workList[w].beginTime).format('yyyy/MM') +'-'+ new Date(workList[w].endTime).format('yyyy/MM') +' </div>' +
                                    '    <div class="timeSlot">' +
                                    '        <span>'+ handleNull(workList[w].corpName) +'</span>' +
                                    '        <i>|</i>' +
                                    '        <span>'+ handleNull(workList[w].corpNature) +'</span>' +
                                    '        <i>|</i>' +
                                    '        <span>'+ handleNull(workList[w].corpSize) +'</span>' +
                                    '        <i>|</i>' +
                                    '        <span>'+ handleNull(workList[w].corpDepartment) +'</span>' +
                                    '        <i>|</i>' +
                                    '        <span>'+ handleNull(workList[w].post) +'</span>' +
                                    '    </div>' +
                                    '</div>' +
                                    '<div class="charact">' +
                                    '    <div class="mmTtl">工作描述：</div>' +
                                    '    <div class="mmCon">' +
                                    '        <p>'+ handleNull(workList[w].jobDesc) +'</p>' +
                                    '    </div>' +
                                    '</div>' +
                                    '<div class="charact">' +
                                    '    <div class="mmTtl">补充说明：</div>' +
                                    '    <div class="mmCon">' +
                                    '        <p>'+ handleNull(workList[w].memo) +'</p>' +
                                    '    </div>' +
                                    '</div></div>';
                            }
                        }
                        $('#workHashMap') .html(whtml);
                    }
                }
                bounce.show($('#employeeInforView'));
            }else {
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $("#mt_tip_ms").html("链接失败，请刷新重试！");
            bounce.show( $("#mtTip") ) ;
        }
    });
}
// creator: 李玉婷，2019-11-09 10:51:33，档案管理
function handleUpdate(obj) {
    var id = obj.parents('tr').data("id");
    $("#handleUpdate").data('id', id);
    if(chargeRole('超管')){
        $('#altMS').html('您没有此操作权限！');
        bounce.show($('#alertMS'));
        return false;
    }
    $("#update_oName").attr('info', '0');
    $("#update_zhiName").attr('info', '0');
    $("#update_zhiCon").hide();
    $("#update_deparCon").hide();
    $("#update_deparCon .levelItem").not(":first").remove();
    document.getElementById("handleUpdateForm").reset();
    $("#handleUpdateForm select[name='department']").html('<option selected="selected" style="display: none" value=""></option>');
    $("#handleUpdateForm select[name='postID']").html('');
    $("#handleUpdateForm select[name='managerCode']").html('');
    $("#handleUpdateForm select[name='leader']").html('');
    $.ajax({
        "url": "/recruit/getSelect.do",
        "data":  {
            'userId': id
        },
        success: function (res) {
            var userList = res.userList; // 直接上级
            var manageList = res.manageList; // 所属高管列表
            var postList = res.postList; // 职位列表
            var departmentList = res.departmentList; // 部门列表
            if (departmentList.length > 0) {
                var dHtml = '';
                for (var m = 0; m < departmentList.length; m++) {
                    if (departmentList[m].level == 1) {
                        dHtml +=
                            '<span class="departItem" onclick="getKidsDepart($(this))">' +
                            '    <span>' + departmentList[m].name + '</span>' +
                            '    <span class="hd oid">' + departmentList[m].id + '</span>' +
                            "    <span class='hd pid'>"+ departmentList[m]["pid"] +"</span>" +
                            '</span>';
                    }
                }
                $("#update_depart").html(dHtml);
            }
            if (userList.length > 0) {
                var uHtml = '<option value="">---请选择---</option>';
                for (var u = 0; u < userList.length; u++) {
                    uHtml +=
                        '<option value="' + userList[u].userID + '">' + userList[u].userName + '</option>';
                }
                $("#handleUpdateForm select[name='leader']").html(uHtml);
            }
            if (manageList.length > 0) {
                var mHtml = '';
                for (var m = 0; m < manageList.length; m++) {
                    mHtml +=
                        '<option value="' + manageList[m].managerCode + '">' + changeWorkFuc(manageList[m].managerCode) + '</option>';
                }
                $("#handleUpdateForm select[name='managerCode']").html(mHtml);
            }
            // if (postList.length > 0) {
            //     var pHtml = '';
            //     for (var m = 0; m < postList.length; m++) {
            //             pHtml +=
            //                 '<span class="zhiItem" onclick="getZhi($(this))">' +
            //                 '    <span>'+ postList[m].name +'</span>' +
            //                 '    <span class="hd">' + postList[m].id + '</span>' +
            //                 '</span>';
            //     }
            //     $("#update_join_zhi").html(pHtml);
            // }
            var postStr = '<option value="">------请选择------</option>';
            if (postList.length > 0) {
                for (var m in postList){
                    postStr += '<option value="'+postList[m].id+'">'+postList[m].name+'</option>';
                }
            }
            $("#handleUpdateForm select[name='postID']").html(postStr);
            $.ajax({
                "url": "/userInfo/getUserArchives.do",
                "data": {
                    'passiveUserId': id
                },
                beforeSend:function(){ loading.open() ; },
                success: function (res) {
                    var success = res.success;
                    if (success == '1') {
                        var user = res['data'];
                        // 是否显示帮他激活按钮 - 张
                        var activationButton = user.activationButton
                        if (activationButton) {
                            $("#helpActivateBtn").show()
                        } else {
                            $("#helpActivateBtn").hide()
                        }
                        $("#helpActivateTip").data('data', {
                            userID: user.userID,
                            userName: user.userName,
                            mobile: user.mobile
                        })

                        if (user.submitState == '1') {
                            $("#emergencyName").html(handleNull(user.emergencyName));
                            $("#emergencyContact").html(handleNull(user.emergencyContact));
                        }else{
                            $("#emergencyName").html("");
                            $("#emergencyContact").html("");
                        }
                        $("#handleUpdateForm [name='departName']").val(handleNull(user['departName']));
                        $("#handleUpdateForm [name='postID']").val(handleNull(user.postID));
                        $("#handleUpdateForm [name='oldMobile']").val(handleNull(user['mobile']));
                        $("#handleUpdateForm [require]").each(function () {
                            var name = $(this).attr('name');
                            var type = $(this).data('type');
                            switch (type) {
                                case 1:
                                case '1':
                                    $(this).val(handleNull(user[name])).data('org', handleNull(user[name]));
                                    if (name == 'ordinaryEmployees' || name == 'mobile'){
                                        if (user.roleCode == 'staff'){
                                            $(this).prop('disabled', false);
                                        }else{
                                            $(this).prop('disabled', true);
                                        }
                                        if (name == 'ordinaryEmployees') {
                                            if(handleNull(user[name]) == "") $(this).val("0").data('org', "0");
                                            $(this).data('haveSubordinate', user["haveSubordinate"]);
                                        }
                                    }
                                    break;
                                case 2:
                                case '2':
                                    if (name == 'managerCode') {
                                        if (user.roleCode == 'staff') {
                                            $(this).data('org', handleNull(user[name])).prop('disabled', false);
                                            $(this).find("option[value='"+ handleNull(user[name]) +"']").attr("selected", true);
                                        }else{
                                            var option = '<option value="'+ user.managerCode +'">--</option>'
                                            $(this).html(option).prop('disabled', true).data('org', user.managerCode);
                                        }
                                    }else if (name == 'leader'){
                                        $(this).data('org', handleNull(user[name]));
                                        var choosed = $(this).find("option[value='"+ handleNull(user[name]) +"']")
                                        if (choosed.length > 0) {
                                            choosed.attr("selected", true);
                                        } else {
                                            $(this).val("")
                                        }
                                        if (user.roleCode == 'staff') {
                                            $(this).prop('disabled', false);
                                        }else{
                                            if (user.roleCode == 'super') {
                                                var option = '<option value="'+ handleNull(user[name]) +'">--</option>';
                                                $(this).html(option).prop('disabled', true).data('org', handleNull(user[name]));
                                            }
                                            $(this).prop('disabled', true);
                                        }
                                    } else {
                                        $(this).data('org', handleNull(user[name]))
                                        $(this).find("option[value='"+ handleNull(user[name]) +"']").attr("selected", true);
                                    }
                                    break;
                                case 3:
                                case '3':
                                    if (name == 'date1' && user["roleCode"] == "super") {
                                        $(this).val('').data('org', "").prop("disabled", true);
                                    } else {
                                        if (handleNull(user['onDutyDate']) == '') {
                                            $(this).val(new Date().format('yyyy/MM/dd')).data('org', '');
                                        } else {
                                            $(this).val(new Date(user['onDutyDate']).format('yyyy/MM/dd')).data('org', new Date(user['onDutyDate']).format('yyyy/MM/dd'));
                                        }
                                    }
                                    break;
                            }
                        });
                        bounce.show($("#handleUpdate"));
                        bounce.everyTime('0.5s','updateStuff',function(){
                            var diff = 0;
                            $("#handleUpdateForm [require]").each(function () {
                                if ($(this).val() != $(this).data('org')){
                                    diff++;
                                }
                            });
                            if(diff >0 && $("#updateUserName").val() !== "" && testMobile($("#updateMobile").val())){
                                $("#handleUpdateSure").prop("disabled",false);
                            }else{
                                $("#handleUpdateSure").prop("disabled",true);
                            }
                        });
                    } else {
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#mt_tip_ms").html("链接失败，请刷新重试！");
                    bounce.show( $("#mtTip") ) ;
                }
            });
        },
        complete:function(){}
    });
}
// creator: 李玉婷，2019-11-12 14:04:49，档案管理提交
function handleUpdateSure() {
    var params = {
        'userID': $("#handleUpdate").data("id")
    };
    $("#handleUpdate [require]").each(function () {
        var name = $(this).attr('name');
        params[name] = $(this).val();
        if (name === 'postID') {
            if (params[name] === '') {
                params.postName = ''
            } else {
                params.postName = $(this).find("option:selected").html()
            }
        }
    });
    $.ajax({
        "url": "/general/updateEssentialInformation.do",
        "data": params,
        success: function (res) {
            if (res == '1') {
                defineGetList(1,20,1);
                bounce.cancel();
            } else if (res == 3){
                $("#errorTip .tipWord").html('姓名超出或者手机号已存在！');
                bounce_Fixed.show($("#errorTip"));
            } else {
                layer.msg('提交失败！');
            }
        },
        error:function(){
            $("#mt_tip_ms").html("链接失败，请刷新重试！");
            bounce.show( $("#mtTip") ) ;
        }
    });
}
// creator: 李玉婷，2020-06-02 16:54:18，切换是否为普通员工
function changeOrdinary(obj){
    var isCan = obj.data("haveSubordinate");
    if (isCan == 1) {
        var val = obj.val();
        if (val == 1) {
            obj.val("0");
        }else{
            obj.val("1");
        }
        bounce_Fixed.show($("#changeOrdinary"));
    }
}
// creator: 李玉婷，2019-11-09 11:02:58，员工修改列表
function getUpdateInfor() {
    if(chargeRole('超管')){
        $('.tipWord').html('您没有此操作权限！');
        bounce_Fixed.show($('#errorTip'));
        return false;
    }
    var id = $('#employeeInforView').data('employeeId');
    $(".experience").remove();
    $.ajax({
        "url": "/userInfo/userInfoPreview.do",
        "data": {
            'passiveUserId': id
        },
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                var user = res['data']['user'];
                var workList = res['data']['personnelOccupations'];
                var eduList = res['data']['personalEducations'];
                $("#userNameEdit").html(user.userName);
                if (eduList.length > 0) {
                    var educations = '';
                    for( var t = 0; t < eduList.length; t++) {
                        if (eduList[t].deleted == true) {
                            educations +=
                                '<tr class="experience">' +
                                '    <td><p style="text-indent: 2em;">'+ eduList[t].collegeName +'<span class="ty-color-red">(已删除)</span></p></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-name="edu" data-id="'+ eduList[t].id +'" onclick="employeeBaseRecord(\'edu\', '+ eduList[t].id +', 1, 10)">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }else {
                            educations +=
                                '<tr class="experience">' +
                                '    <td><p style="text-indent: 2em;">'+ eduList[t].collegeName +'</p></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-type="update" data-id="'+ eduList[t].id +'" onclick="employeeEduUpdate($(this))">修改</span>' +
                                '        <span class="ty-color-blue" data-name="edu" data-id="'+ eduList[t].id +'" onclick="employeeBaseRecord(\'edu\', '+ eduList[t].id +', 1, 10)">修改记录</span>' +
                                '        <span class="ty-color-blue" data-name="edu" data-id="'+ eduList[t].id +'" onclick="deleteExperience($(this))">删除</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $(".eduList").after(educations);
                }
                if (workList.length > 0) {
                    var  occupations = '';
                    for( var t = 0; t < workList.length; t++) {
                        if (workList[t].deleted == true) {
                            occupations +=
                                '<tr class="experience">' +
                                '    <td><p style="text-indent: 2em;">'+ workList[t].corpName +'<span class="ty-color-red">(已删除)</span></p></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-name="work" data-id="'+ workList[t].id +'" onclick="employeeBaseRecord(\'work\', '+ workList[t].id +', 1, 10)">修改记录</span>' +
                                '    </td>' +
                                '</tr>';
                        }else {
                            occupations +=
                                '<tr class="experience">' +
                                '    <td><p style="text-indent: 2em;">'+ workList[t].corpName +'</p></td>' +
                                '    <td>' +
                                '        <span class="ty-color-blue" data-type="update" data-id="'+ workList[t].id +'" onclick="employeeWorkUpdate($(this))">修改</span>' +
                                '        <span class="ty-color-blue" onclick="employeeBaseRecord(\'work\', '+ workList[t].id +', 1, 10)">修改记录</span>' +
                                '        <span class="ty-color-blue" data-name="work" data-id="'+ workList[t].id +'" onclick="deleteExperience($(this))">删除</span>' +
                                '    </td>' +
                                '</tr>';
                        }
                    }
                    $(".workList").after(occupations);
                }
                bounce_Fixed.show($('#employeeUpdateIndex'));
            } else {
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $("#errorTip .tipWord").html("链接失败，请刷新重试！");
            bounce.show( $("#errorTip") ) ;
        }
    });
}
// creator: 李玉婷，2019-11-09 11:41:11，基本信息编辑
function updateEmployeeBase() {
    document.getElementById("updateBaseDetails").reset();
    $(".addMoreSelect").hide();
    $(".otherContactType").remove();
    var id = $('#employeeInforView').data('employeeId');
    $.ajax({
        "url": "/userInfo/userInfoPreview.do",
        "data":{
            'passiveUserId': id
        },
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                var userContactLen = 0;
                var base = res['data']['user'];
                var userContacts = res['data']['userContacts'];
                var degree = handleNull(base.degree) == '' ? 0:base.degree;
                $("#updateBaseDetails input[name='sex']").prop('checked', false);
                $("#updateBaseName").html(base.userName);
                $("#personPhone").html(base.mobile);
                $("#personBirthday").val(base.birthday);
                $("#updateBaseDetails input[name='sex'][value='" + handleNull(base.gender) + "']").prop('checked',true).data('org', handleNull(base.gender));
                $("#updateBaseDetails select[name='degree']").val(degree);
                $("#updateBaseDetails select[name='degree']").data('org', degree);
                $("#updateBaseDetails [require]").each(function () {
                    var name = $(this).attr('name');
                    if (name == 'birthday') {
                        var day = new Date(base[name]).format('yyyy/MM/dd');
                        if (day != ''){
                            $(this).val(day).data('org',day);
                        } else {
                            $(this).val(new Date().format('yyyy/MM/dd')).data('org',day);
                        }
                    } else if (name == 'marry') {
                        if (handleNull(base[name]) != ''){
                            $(this).val(handleNull(base[name])).data('org',handleNull(base[name]));
                        } else {
                            $(this).val('1').data('org',handleNull(base[name]));
                        }
                    } else{
                        $(this).val(base[name]).data('org',handleNull(base[name]));
                    }
                });
                if (userContacts.length > 0) {
                    var html = '',classStr= '', name = ['','其他手机','Q Q','Email','微信','微博'], nameStr = '', definedName = '';
                    for( var t = 0; t < userContacts.length; t++) {
                        if (userContacts[t].open == true){
                            classStr= '';
                            userContactLen++;
                        } else{
                            classStr= ' hd';
                        }
                        nameStr = userContacts[t].type == '9' ? userContacts[t].name:name[Number(userContacts[t].type)];
                        definedName = userContacts[t].type == '9' ? ' data-name="' + userContacts[t].name + '" ':'';
                        html +=
                            '<li class="otherContactType '+ classStr +'" data-can="'+ userContacts[t].open +'">' +
                            '   <span class="one-ttl">'+ nameStr +'</span>' +
                            '   <input type="text" value="'+ userContacts[t].code +'" placeholder="请录入" ' + definedName + ' data-type="'+ userContacts[t].type +'" data-org="'+ userContacts[t].code +'" />' +
                            '   <a class="clearKey" onclick="clearKey($(this))">x</a>' +
                            '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>' +
                            '</li>';
                    }
                    $("#updateBaseDetails ul").append(html);
                }
                $("#updateBaseDetails").data('userContacts',userContactLen);
                bounce_Fixed2.show($("#employeeUpdate"));
                bounce_Fixed2.everyTime('0.5s','baseInfo',function(){
                    var state = 0;
                    $("#updateBaseDetails [require]").each(function () {
                        if($(this).data('org') != $(this).val()) state ++;
                    });
                    if($("#updateBaseDetails input[name='sex']:checked").length>0 && $("#updateBaseDetails input[name='sex']:checked").data('org') != $("#updateBaseDetails input[name='sex']:checked").val()){
                        state ++;
                    }
                    if($("#updateBaseDetails select[name='degree']").data('org') != $("#updateBaseDetails select[name='degree']").val()){
                        state ++;
                    }
                    if ($("#updateBaseDetails").data('userContacts') <= $("#updateBaseDetails .otherContactType:visible").length){
                        $("#updateBaseDetails .otherContactType").each(function(){
                            if($(this).find("input").val() != $(this).find("input").data('org')){
                                state ++;
                            }
                        })
                    }else {
                        state ++;
                    }
                    if(state != 0){
                        $("#updateEmployeeBase").prop('disabled', false);
                    }else{
                        $("#updateEmployeeBase").prop('disabled', true);
                    }

                    if ($("#updateBaseDetails .otherContactType").length > 0) {
                        $("#updateBaseDetails .otherContactType").each(function () {
                            var val = $(this).find("input").val();
                            var type = $(this).find("input").data('type');
                            if (type == '9' || type == '9') type = 6
                            if (val == '') {
                                $(".addMoreSelect option").eq(type).prop('disabled', true);
                            }else {
                                $(".addMoreSelect option").eq(type).prop('disabled', false);
                            }
                        })
                    } else {
                        $(".addMoreSelect option").prop('disabled', false);
                    }
                })
            } else{
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $("#errorMSTip").html("链接失败，请刷新重试！");
            bounce_Fixed3.show( $("#errorMS") ) ;
        }
    });
}
// creator: 李玉婷，2019-11-05 17:12:04，添加更多联系方式
function addMoreContact (obj) {
    obj.next("select").show();
}
// creator: 李玉婷，2019-11-05 17:15:13，添加更多联系人
function addMoreChange(obj){
    var val = obj.val();
    var html = '';
    obj.val('0').hide();
    $(".otherContactType").each(function(){
        var val1 = $(this).find('input').val();
        if(val1 == ''){
            $(this).remove();
        }
    })
    switch (val) {
        case '1':
            html +=
                '<li class="otherContactType" data-can="true">' +
                '   <span class="one-ttl">其他手机</span>' +
                '   <input type="text" placeholder="请录入" data-type="1" data-org="" onkeyup="clearKeyShow($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $("#updateBaseDetails ul").append(html);
            break;
        case '2':
            html +=
                '<li class="otherContactType" data-can="true">' +
                '   <span class="one-ttl">Q Q</span>' +
                '   <input type="text" placeholder="请录入" data-type="2" data-org="" onkeyup="clearKeyShow($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $("#updateBaseDetails ul").append(html);
            break;
        case '3':
            html +=
                '<li class="otherContactType" data-can="true">' +
                '   <span class="one-ttl">Email</span>' +
                '   <input type="text" placeholder="请录入" data-type="3" data-org="" onkeyup="clearKeyShow($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $("#updateBaseDetails ul").append(html);
            break;
        case '4':
            html +=
                '<li class="otherContactType" data-can="true"">' +
                '   <span class="one-ttl">微信</span>' +
                '   <input type="text" placeholder="请录入" data-type="4" data-org="" onkeyup="clearKeyShow($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $("#updateBaseDetails ul").append(html);
            break;
        case '5':
            html +=
                '<li class="otherContactType" data-can="true">' +
                '   <span class="one-ttl">微博</span>' +
                '   <input type="text" placeholder="请录入" data-type="5" data-org="" onkeyup="clearKeyShow($(this))" />' +
                '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
                '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
                '</li>';
            $("#updateBaseDetails ul").append(html);
            break;
        case '9':
            $("#useDefinedLabel input").val("");
            bounce_Fixed3.show($("#useDefinedLabel"));
            bounce_Fixed3.everyTime('0.5s', 'useDefinedLabel', function () {
                if ($("#defLable").val() == '') {
                    $("#addNewLableSure").prop('disabled', true);
                } else{
                    $("#addNewLableSure").prop('disabled', false);
                }
            })
            break;
        default:break;
    }
}
// creator: 李玉婷，2019-09-19 10:14:23，自定义标签确定
function addNewLable(){
    var val = $("#defLable").val();
    var html =
        '<li class="otherContactType" data-can="true">' +
        '<span class="one-ttl">' + val + '：</span>' +
        '<input type="text" placeholder="请录入" data-type="9" data-name="' + val + '" onkeyup="clearKeyShow($(this))" data-org=""/>'+
        '   <a class="clearKey" style="display: none" onclick="clearKey($(this))">x</a>' +
        '   <span class="ty-color-red" onclick="removeAdd($(this))">删除</span>'+
        '</li>';
    $("#updateBaseDetails ul").append(html);
    bounce_Fixed3.cancel();
}
// creator: 李玉婷，2019-09-19 09:37:24，清除自定义标签
function clearLableText(obj) {
    obj.siblings().val("");
}
// creator: 李玉婷，2019-11-15 10:04:14，一键清除
function clearKey(obj){
    obj.prev('input').val('')
}
// creator: 李玉婷，2019-11-15 10:06:49，一键清除出现
function clearKeyShow(obj){
    obj.next('a').show();
}
// creator: 李玉婷，2019-11-05 19:29:31，添加更多联系方式 - 删除
function removeAdd (obj) {
    obj.parent("li").remove();
}
// creator: 李玉婷，2019-11-09 15:44:32，基本信息编辑提交
function updateEmployeeBaseSure () {
    var employeeId = $('#employeeInforView').data('employeeId');
    var userInfo = {
        'passiveUserId': employeeId,
        'gender': '',
        'degree': $("#updateBaseDetails select[name='degree']").val()
    }
    var contacts = [];
    if ($("#updateBaseDetails input[name='sex']:checked").length > 0) {
        userInfo.gender = $("#updateBaseDetails input[name='sex']:checked").val();
    }
    $("#updateBaseDetails [require]").each(function () {
        var name = $(this).attr('name');
        userInfo[name] = $(this).val();
    });
    $("#updateBaseDetails .otherContactType").each(function(){
        if ($(this).find('input').val() != ''){
            var type = $(this).find('input').data('type');
            var name = $(this).find('input').data('name');
            var isOpen = $(this).data('can');
            contacts.push({
                'type': type,
                'name': name,
                'code': $(this).find('input').val(),
                'isOpen': isOpen
            })
        }
    });
    userInfo = JSON.stringify(userInfo);
    contacts = JSON.stringify(contacts);
    var params = {
        'userInfo': userInfo,
        'contacts': contacts
    };
    $.ajax({
        "url": "/userInfo/updateUserBasicInfo.do",
        "data": params,
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                bounce_Fixed2.cancel();
                employeeInforView(employeeId);
                defineGetList(1,20,1);
            } else{
                layer.msg('提交失败！');
            }
        },
        error:function(){
            $("#errorMSTip").html("链接失败，请刷新重试！");
            bounce_Fixed3.show( $("#errorMS") ) ;
        }
    });
}
// creator: 李玉婷，2019-11-09 14:25:27，教育经历修改
function employeeEduUpdate(obj) {
    document.getElementById("eduInfoForm").reset();
    var type = obj.data('type');
    var employeeId = $("#employeeInforView").data('employeeId');
    $("#eduExperienceTj").data('type', type);
    if(type == 'new'){
        $("#eduInfoForm [require]").each(function () {
            $(this).data('org','');
        });
    }
    else if(type == 'update') {
        var eduId = obj.data('id');
        $('#addEduInfo').data('eduId', eduId);
        $.ajax({
            "url": "/userInfo/userInfoPreview.do",
            "data": {
                'passiveUserId': employeeId
            },
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    var eduList = res['data']['personalEducations'];
                    var findItem = {};
                    findItem = eduList.find(item => item.id == eduId);
                    if(findItem != undefined) {
                        $("#eduInfoForm [require]").each(function () {
                            var name = $(this).attr('name');
                            if (name == 'beginTime' || name == 'endTime') {
                                $(this).val(new Date(findItem[name]).format('yyyy/MM')).data('org',new Date(findItem[name]).format('yyyy/MM'));
                            }else{
                                $(this).val(findItem[name]).data('org',findItem[name]);
                            }
                        })
                    }
                } else {
                    layer.msg('获取失败！');
                }
            },
            error:function(){
                $("#errorMSTip").html("链接失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        })
    }
    bounce_Fixed2.show($('#addEduInfo'));
    setEvery('addEduInfo');
}
// creator: 李玉婷，2019-11-06 10:33:18，新增教育经历提交
function employeeEduUpdateTj () {
    var params = {}
    var tjType = $("#eduExperienceTj").data('type');
    $("#eduInfoForm [require]").each(function () {
        var name = $(this).attr('name');
        params[name] = $(this).val();
    })
    var employeeId = $("#employeeInforView").data('employeeId');
    params.passiveUserId = employeeId;
    if (tjType == 'new') {
        $.ajax({
            "url": "/userInfo/addPersonalEducation.do",
            "data": params,
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    bounce_Fixed2.cancel();
                    getUpdateInfor();
                    employeeInforView(employeeId);
                } else{
                    layer.msg('提交失败！');
                }
            },
            error:function(){
                $("#errorMSTip").html("链接失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        });
    } else {
        params.id = $('#addEduInfo').data('eduId');
        $.ajax({
            "url": "/userInfo/updatePersonalEducation.do",
            "data": params,
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    bounce_Fixed2.cancel();
                    getUpdateInfor();
                    employeeInforView(employeeId);
                } else{
                    layer.msg('提交失败！');
                }
            },
            error:function(){
                $("#errorMSTip").html("链接失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        });
    }
}
// creator: 李玉婷，2019-11-06 10:00:45，新增/修改工作经历
function employeeWorkUpdate(obj) {
    document.getElementById("WorkInfoForm").reset();
    var type = obj.data('type');
    var employeeId = $("#employeeInforView").data('employeeId');
    $("#workExperienceTj").data('type', type);
    if(type == 'new'){
        $("#WorkInfoForm [require]").each(function () {
            $(this).data('org','');
        });
    } else if (type == 'update') {
        var eduId = obj.data('id');
        $('#addWorkInfo').data('workId', eduId);
        $.ajax({
            "url": "/userInfo/userInfoPreview.do",
            "data": {
                'passiveUserId': employeeId
            },
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    var workList = res['data']['personnelOccupations'];
                    var findItem = {};
                    findItem = workList.find(item => item.id == eduId);
                    if(findItem != undefined) {
                        $("#WorkInfoForm [require]").each(function () {
                            var name = $(this).attr('name');
                            if (name == 'beginTime' || name == 'endTime') {
                                $(this).val(new Date(findItem[name]).format('yyyy/MM')).data('org',new Date(findItem[name]).format('yyyy/MM'));
                            }else{
                                $(this).val(findItem[name]).data('org',findItem[name]);
                            }
                        })
                    }
                } else{
                    layer.msg('获取失败！');
                }
            },
            error:function(){
                $("#errorMSTip").html("链接失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        })
    }
    bounce_Fixed2.show($('#addWorkInfo'));
    setEvery('addWorkInfo');
}
// creator: 李玉婷，2019-11-06 13:02:49，新增工作经历提交
function employeeWorkUpdateTj () {
    var params = {}
    var tjType = $("#eduExperienceTj").data('type');
    var employeeId = $("#employeeInforView").data('employeeId');
    params.passiveUserId = employeeId;
    $("#WorkInfoForm [require]").each(function () {
        var name = $(this).attr('name');
        params[name] = $(this).val();
    })
    if (tjType == 'new') {
        $.ajax({
            "url": "/userInfo/addPersonnelOccupation.do",
            "data": params,
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    bounce_Fixed2.cancel();
                    getUpdateInfor();
                    employeeInforView(employeeId);
                } else{
                    layer.msg('获取失败！');
                }
            },
            error:function(){
                $("#errorMSTip").html("链接失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        });
    } else {
        params.id = $('#addWorkInfo').data('workId');
        $.ajax({
            "url": "/userInfo/updatePersonnelOccupation.do",
            "data": params,
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    bounce_Fixed2.cancel();
                    getUpdateInfor();
                    employeeInforView(employeeId);
                } else{
                    layer.msg('获取失败！');
                }
            },
            error:function(){
                $("#errorMSTip").html("链接失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        });
    }
}
// creator: 李玉婷，2019-11-13 14:43:19，设置定时器
function setEvery(name) {
    switch (name) {
        case 'addEduInfo':
            bounce_Fixed2.everyTime('0.5s', 'educations', function(){
                var edu = 0, filledNum =0;
                $("#eduInfoForm [require]").each(function() {
                    if ($(this).val() != '') filledNum++;
                    if ($(this).data('org') != $(this).val()){
                        edu ++;
                    }
                })
                if (edu > 0 && filledNum>0) {
                    $("#eduExperienceTj").prop('disabled', false);
                } else{
                    $("#eduExperienceTj").prop('disabled', true);
                }
            });
        case 'addWorkInfo':
            bounce_Fixed2.everyTime('0.5s', 'occupations', function(){
                var work = 0, filledNum =0;
                $("#WorkInfoForm [require]").each(function() {
                    if ($(this).val() != '') filledNum++;
                    if ($(this).data('org') != $(this).val()){
                        work ++;
                    }
                })
                if (work > 0 && filledNum>0) {
                    $("#workExperienceTj").prop('disabled', false);
                } else{
                    $("#workExperienceTj").prop('disabled', true);
                }
            });
    }
}
// creator: 李玉婷，2019-11-07 08:39:30，教育经历/工作经历删除
function deleteExperience (obj) {
    var state = $("#personalInfor").data('state');
    var name = obj.data('name');
    var delId = obj.data('id');
    var json ={
        'id': delId,
        'name': name
    }
    json = JSON.stringify(json);
    $("#deleteDetail").data('deleteParam', json);
    if (name == 'edu') {
        $(".deleteTtl").html('教育经历');
    } else {
        $(".deleteTtl").html('工作经历');
    }
    if(state == '0'){
        $(".tipMessage .deleteClear").show().siblings().hide();
    }else{
        $(".tipMessage .deleteClear").hide().siblings().show();
    }
    bounce_Fixed2.show($('#deleteDetail'));
}
// creator: 李玉婷，2019-11-07 10:10:46，教育经历/工作经历删除确定
function deleteExperienceSure() {
    var deleteParam = JSON.parse($("#deleteDetail").data('deleteParam'));
    var employeeId = $('#employeeInforView').data('employeeId');
    if (deleteParam.name == 'edu') {
        $.ajax({
            "url": "/userInfo/markDeleteEducation.do",
            "data": {
                id: deleteParam.id
            },
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    bounce_Fixed2.cancel();
                    getUpdateInfor();
                    employeeInforView(employeeId);
                } else{
                    layer.msg('删除失败！');
                }
            },
            error:function(){
                $("#errorMSTip").html("链接失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        });
    } else {
        $.ajax({
            "url": "/userInfo/markDeleteOccupation.do",
            "data": {
                id: deleteParam.id
            },
            success: function (res) {
                var success = res['success'];
                if (success == '1' || success == 1) {
                    bounce_Fixed2.cancel();
                    getUpdateInfor();
                    employeeInforView(employeeId);
                } else{
                    layer.msg('删除失败！');
                }
            },
            error:function(){
                $("#errorMSTip").html("链接失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        });
    }
}
// creator: 李玉婷，2019-11-09 12:18:27，
function employeeBaseRecord(name, id, currPage, pageSize) {
    var employeeId = $('#employeeInforView').data('employeeId');
    var jsonStr = JSON.stringify({
        'name': name,
        'id': id
    }) ;
    switch (name) {
        case 'base':
            $("#baseRecordName").html('基本信息修改记录');
            $.ajax({
                "url": "/userInfo/getUserBasicInfoHistory.do",
                "data": {
                    passiveUserId: employeeId,
                    pageSize: pageSize,
                    currentPageNo: currPage
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var record = res['data']['userRoleHistoryList'];
                        $("#basePageInfo").html('');
                        if (Number(res['data'].number) > 0) {
                            var pageInfo = res['data']['pageInfo'];
                            var totalPage = pageInfo["totalPage"];
                            var cur = pageInfo["currentPageNo"];
                            setPage( $("#basePageInfo") , cur ,  totalPage , "employeeRecord" ,jsonStr );
                            $('.baseUpdateNum').html(res['data'].number);
                            $('.baseUpdateInfo').html(handleNull(res['data'].updateName) + ' &nbsp;&nbsp; ' + new Date(res['data'].updateTime).format('yyyy/MM/dd hh:mm:ss'));
                            var str = recordList(record, 'base');
                            $(".updateBaseMessage").html(str);
                            $(".notEditBase").hide();
                            $(".recordMain").show();
                        } else {
                            $(".notEditBase").html('基本信息尚未修改过。').show();
                            $(".recordMain").hide();
                        }
                        bounce_Fixed2.show($('#baseInfoRecord'));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            break;
        case 'edu':
            $("#recordName").html('教育经历修改记录');
            $("#otherPageInfo").html('');
            $.ajax({
                "url": "/userInfo/personalEducationHistories.do",
                "data": {
                    id: id,
                    pageSize: pageSize,
                    currentPageNo: currPage
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var record = res['data']['personnelEducationHistoryList'];
                        var info = res['data']['personalEducation'];
                        if (Number(res['data'].number) > 0) {
                            var pageInfo = res['data']['pageInfo'];
                            var totalPage = pageInfo["totalPage"];
                            var cur = pageInfo["currentPageNo"];
                            setPage( $("#otherPageInfo") , cur ,  totalPage , "employeeRecord" ,jsonStr );
                            if (info.deleted == true){
                                $('.othereUpdateNum').html('<span class="colorOrange">该条数据已被删除。</span>');
                                $('.deltedAfter').show();
                                $('.deltedTtl').html('毕业院校');
                                $('.deltedCon').html(info.collegeName);
                            }else{
                                $('.deltedAfter').hide();
                                $('.othereUpdateNum').html('当前数据为第' + res['data'].number + '次修改后的结果。');
                            }
                            $('.otherUpdateInfo').html(info.updateName + '&nbsp;&nbsp;' +new Date(info.updateDate).format('yyyy/MM/dd hh:mm:ss'));
                            var str = recordList(record, 'edu');
                            $(".updateRecord").html(str);
                            $(".notEditOther").hide();
                            $(".recordMain").show();
                        } else {
                            $(".notEditOther").html('教育经历尚未修改过。').show();
                            $(".recordMain").hide();
                        }
                        bounce_Fixed2.show($('#eduInfoRecord'));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            break;
        case 'work':
            $("#recordName").html('工作经历修改记录');
            $("#otherPageInfo").html('');
            $.ajax({
                "url": "/userInfo/personnelOccupationHistories.do",
                "data": {
                    id: id,
                    pageSize: pageSize,
                    currentPageNo: currPage
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var record = res['data']['personnelOccupationHistoryList'];
                        var info = res['data']['personnelOccupation'];
                        if (Number(res['data'].number) > 0) {
                            var pageInfo = res['data']['pageInfo'];
                            var totalPage = pageInfo["totalPage"];
                            var cur = pageInfo["currentPageNo"];
                            setPage( $("#otherPageInfo") , cur ,  totalPage , "employeeRecord" ,jsonStr );
                            if (info.deleted == true){
                                $('.othereUpdateNum').html('<span class="colorOrange">该条数据已被删除。</span>');
                                $('.deltedAfter').show();
                                $('.deltedTtl').html('公司名称');
                                $('.deltedCon').html(info.collegeName);
                            }else{
                                $('.deltedAfter').hide();
                                $('.othereUpdateNum').html('当前数据为第' + res['data'].number + '次修改后的结果。');
                            }
                            $('.otherUpdateInfo').html(info.updateName + '&nbsp;&nbsp;' + new Date(info.updateDate).format('yyyy/MM/dd hh:mm:ss'));
                            var str = recordList(record, 'work');
                            $(".updateRecord").html(str);
                            $(".notEditOther").hide();
                            $(".recordMain").show();
                        } else {
                            $(".notEditOther").html('工作经历尚未修改过。').show();
                            $(".recordMain").hide();
                        }
                        bounce_Fixed2.show($('#eduInfoRecord'));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            break;
        case 'card':
            $("#baseRecordName").html('身份证修改记录');
            $("#basePageInfo").html('');
            $.ajax({
                "url": "/userInfo/getUserIdCardHistories.do",
                "data": {
                    passiveUserId: employeeId
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var record = res['data']['userIdcardHistoryList'];
                        if (Number(res['data'].number) > 0) {
                            $('.baseUpdateNum').html(res['data'].number);
                            $('.baseUpdateInfo').html(handleNull(res['data'].updateName) + ' &nbsp;&nbsp; ' + new Date(res['data'].updateDate).format('yyyy/MM/dd hh:mm:ss'));
                            var str = recordList(record, 'card');
                            $(".updateBaseMessage").html(str);
                            $(".notEditBase").hide();
                            $(".recordMain").show();
                        } else {
                            $(".notEditBase").html('身份证尚未修改过。').show();
                            $(".recordMain").hide();
                        }
                        bounce_Fixed2.show($('#baseInfoRecord'));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            break;
    }
}
// creator: 李玉婷，2019-11-07 11:47:43，修改记录查看
function recordDetails (obj) {
    $(".seeMoreContact").remove();
    var name = obj.data('name');
    var id = obj.data('id');
    switch (name) {
        case 'base':
            $.ajax({
                "url": "/userInfo/getUserHistory.do",
                "data": {
                    'id': id
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var baseDetails = res['data']['user'];
                        var userContacts = res['data']['userContacts'];
                        $("#baseDetails [require]").each(function () {
                            var name = $(this).data('name');
                            switch (name) {
                                case 'gender':
                                    var value = chargeSex(handleNull(baseDetails[name]));
                                    $(this).html(value);
                                    break;
                                case 'marry':
                                    var value = chargeMarry(handleNull(baseDetails['marry']));
                                    $(this).html(value);
                                    break;
                                case 'degree':
                                    $(this).html(chargeDegree(baseDetails[name]));
                                    break;
                                case 'birthday':
                                    $(this).html(new Date(baseDetails[name]).format('yyyy/MM/dd'));
                                    break;
                                default:
                                    $(this).html(handleNull(baseDetails[name]));
                                    break;
                            }
                        });
                        if (userContacts.length > 0) {
                            var html = '';
                            var typeArr = ['', '其它手机', 'QQ', 'Email', '微信', '微博'];
                            for (var t = 0; t < userContacts.length; t++) {
                                if (userContacts[t].open == true){
                                    if (userContacts[t].type == '9') {
                                        html +=
                                            '<li class="seeMoreContact">' +
                                            '    <div class="ltTtl">' + userContacts[t].name + '</div>' +
                                            '    <span class="rtCon" title="' + userContacts[t].code + '">' + userContacts[t].code + '</span>' +
                                            '</li>';
                                    } else {
                                        html +=
                                            '<li class="seeMoreContact">' +
                                            '    <div class="ltTtl">' + typeArr[Number(userContacts[t].type)] + '</div>' +
                                            '    <span class="rtCon" title="' + userContacts[t].code + '">' + userContacts[t].code + '</span>' +
                                            '</li>';
                                    }
                                }
                            }
                            $('#baseDetails').append(html);
                        }
                        bounce_Fixed3.show($("#seeBaseInfor"));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            break;
        case 'edu':
            $.ajax({
                "url": "/userInfo/getPersonalEducationHistory.do",
                "data": {
                    'id': id
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var details = res['data'];
                        $("#eduRecordSee [require]").each(function () {
                            var name = $(this).data('name');
                            switch (name) {
                                case 'beginTime':
                                case 'endTime':
                                    $(this).html(new Date(details[name]).format('yyyy/MM'));
                                    break;
                                default:
                                    $(this).html(handleNull(details[name]));
                                    break;
                            }
                        });
                        bounce_Fixed3.show($("#seeEduInfor"));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            break;
        case 'work':
            $.ajax({
                "url": "/userInfo/getPersonnelOccupationHistory.do",
                "data": {
                    'id': id
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var details = res['data'];
                        $("#workRecordSee [require]").each(function () {
                            var name = $(this).data('name');
                            switch (name) {
                                case 'beginTime':
                                case 'endTime':
                                    $(this).html(new Date(details[name]).format('yyyy/MM'));
                                    break;
                                default:
                                    $(this).html(handleNull(details[name]));
                                    break;
                            }
                        });
                        bounce_Fixed3.show($("#seeWorkInfor"));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            break;
        case 'card':
            $.ajax({
                "url": "/userInfo/getUserIdCard.do",
                "data": {
                    'id': id
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var data = res.data;
                        if (data.versionNo == '0'){
                            $("#cardNumberSee").html(handleNull(data.cardNewValue));
                            $("#cardAddressSee").html(handleNull(data.addressNewValue));
                        }else{
                            $("#cardNumberSee").html(compareD(data.cardOldValue, data.cardNewValue));
                            $("#cardAddressSee").html(compareD(data.addressOldValue, data.addressNewValue));
                        }
                        bounce_Fixed3.show($("#identityCardSee"));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            break;
        case 'handle':
            $.ajax({
                "url": "/userInfo/getUserHistory.do",
                "data": {
                    'id': id
                },
                success: function (res) {
                    var success = res['success'];
                    if (success == '1' || success == 1) {
                        var baseDetails = res['data']['user'];
                        var userContacts = res['data']['userContacts'];
                        $("#fileManage [require]").each(function () {
                            var name = $(this).data('name');
                            switch (name) {
                                case 'ordinaryEmployees':
                                    var value = handleNull(baseDetails[name]) == 1?'无':'有' ;
                                    $(this).html(value);
                                    break;
                                case 'onDutyDate':
                                    $(this).html(new Date(baseDetails[name]).format('yyyy/MM/dd'));
                                    break;
                                case 'manageCode':
                                    $(this).html(changeWorkFuc(baseDetails.managerCode));
                                    break;
                                default:
                                    $(this).html(handleNull(baseDetails[name]));
                                    break;
                            }
                        });
                        bounce_Fixed3.show($("#seeHandleInfor"));
                    } else{
                        layer.msg('获取失败！');
                    }
                },
                error:function(){
                    $("#errorMSTip").html("链接失败，请刷新重试！");
                    bounce_Fixed3.show( $("#errorMS") ) ;
                }
            });
            bounce_Fixed3.show($("#seeHandleInfor"));
            break;
    }
}
// creator: 李玉婷，2019-11-07 13:39:28，修改记录列表设置
function recordList(record, name) {
    var html =
            ' <tr>' +
            '     <td>记录</td>' +
            '     <td>操作</td>' +
            '     <td>创建者/修改者</td>' +
            ' </tr>' ;
    switch (name) {
        case 'base':
        case 'card':
        case 'handle':
            for (var a =0;a<record.length;a++) {
                html +=
                    ' <tr>' +
                    '     <td>'+ record[a].dataState +'</td>' +
                    '     <td><span class="see-btn ty-color-blue" data-name="'+ name +'" data-id="'+ record[a].id +'" onclick="recordDetails($(this))">查看</span></td>' +
                    '     <td>'+ handleNull(record[a].updateName) +' &nbsp;&nbsp; ' + new Date(record[a].updateTime).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    ' </tr>';
            }
            return html;
            break;
        default:
            for (var a =0;a<record.length;a++) {
                if (record[a].dataState == '删除') {
                    html +=
                        ' <tr>' +
                        '     <td><span class="colorOrange">'+ record[a].dataState +'</span></td>' +
                        '     <td>--</td>' +
                        '     <td>'+ handleNull(record[a].updateName) +' &nbsp;&nbsp; ' + new Date(record[a].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        ' </tr>';
                }else{
                    html +=
                        ' <tr>' +
                        '     <td>'+ record[a].dataState +'</td>' +
                        '     <td><span class="see-btn ty-color-blue" data-name="'+ name +'" data-id="'+ record[a].id +'" onclick="recordDetails($(this))">查看</span></td>' +
                        '     <td>'+ handleNull(record[a].updateName) +' &nbsp;&nbsp; ' + new Date(record[a].updateDate).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                        ' </tr>';
                }
            }
            return html;
            break;
    }
}
// creator: 李玉婷，2019-11-09 16:55:28，身份证编辑
function identityCardEdit() {
    document.getElementById("cardEditForm").reset();
    var employeeId = $('#employeeInforView').data('employeeId');
    $.ajax({
        "url": "/userInfo/userInfoPreview.do",
        "data": {
            'passiveUserId': employeeId
        },
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                var user = res['data']['user'];
                $("#cardNumber").val(handleNull(user.idCard)).data('org',handleNull(user.idCard));
                $("#cardAddress").val(handleNull(user.address)).data('org',handleNull(user.address));
                bounce_Fixed2.show($("#identityCardEdit"));
                bounce_Fixed2.everyTime('0.5s', 'identityCardEdit', function () {
                    var number = $("#cardNumber").val();
                    var address = $("#cardAddress").val();
                    if(number != '' && !isCardNo(number)){
                        $("#cardNumberTip").show();
                    }else{
                        $("#cardNumberTip").hide();
                    }
                    if (number != '' && address != '' && (number != $("#cardNumber").data('org') || address != $("#cardAddress").data('org')) && isCardNo(number)){
                        $("#identityCardEditSure").prop('disabled', false);
                    }else{
                        $("#identityCardEditSure").prop('disabled', true);
                    }
                })
            } else{
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $("#errorMSTip").html("链接失败，请刷新重试！");
            bounce_Fixed3.show( $("#errorMS") ) ;
        }
    });
}

// creator: 李玉婷，2019-11-09 16:56:16，
function identityCardEditSure() {
    var employeeId = $('#employeeInforView').data('employeeId');
    var params = {
        'passiveUserId': employeeId,
        'idCard': $("#cardNumber").val(),
        'address': $("#cardAddress").val()
    };
    $.ajax({
        "url": "/userInfo/updateUserIdCard.do",
        "data": params,
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                bounce_Fixed2.cancel();
            } else{
                layer.msg('提交失败！');
            }
        },
        error:function(){
            $("#errorMSTip").html("链接失败，请刷新重试！");
            bounce_Fixed3.show( $("#errorMS") ) ;
        }
    });
}

//	单击部门，召唤出子级部门
function getKidsDepart(obj){
    obj.attr("style","background:#36C6D3; border-color:#36C6D3; color:#fff;  ");
    obj.siblings("span").attr("style","background:#fff; border-color:#666; color:#333; ");
    var oid = obj.children(".oid").html();
    var oname = obj.children(":eq(0)").html();
    if($("#handleEntry").is(":visible")){
        $("#oId").val(oid) ;
    }else if($("#handleUpdate").is(":visible")){
        $("#update_oId").val(oid) ;
    }
    var activeIdObj = obj.parents(".levelItem").find(".activeId") ;
    var level_num = obj.siblings(".depatlevel_num").html();
    level_num= parseInt(level_num) + 1 ;
    var level = "";
    level = getLevelName(level_num);
    if( oid == "" || oid == null){
        alert("输入不合法！");
    }else{
        activeIdObj.html(oid) ;
        obj.parents(".levelItem").nextAll().remove() ;
        $(".departItem").removeClass("active") ; obj.addClass("active") ;
        $.ajax({
            url:"checkSonDepartment.do",
            data:{ "id":oid   },
            type:"post",
            dataType:"json",
            success:function(data){
                var str = "";
                if(data.length> 0){
                    str = "<div class='levelItem'>" +
                        "<div style='display:none; '>" +
                        "<span class='activeId' ></span>" +
                        "<span class='isKidsShow' ></span>" +
                        "</div>" +
                        "<a class='depatlevel'>"+ level +"</a>" +
                        "<a class='depatlevel_num hd'>"+ level_num +"</a>";
                    str += "<div>" ;
                    for( var i =0 ; i< data.length; i++ ){
                        str += "<span  class='departItem'  onclick='getKidsDepart($(this))'>" +
                            "<span>"+ data[i]["name"] +"</span>" +
                            "<span class='hd oid'>"+ data[i]["id"] +"</span>" +
                            "<span class='hd pid'>"+ data[i]["pid"] +"</span>" +
                            "</span>"
                    }
                    str += "<div class='clr'></div></div> " ;

                    obj.parents(".levelCon").append(str) ;
                }
            },
            error:function(){alert("连接错误！");}
        });
        setDepatShow() ;
    }
}
// 下拉框toggle
function toggleSelect(source, type , obj ){
    var num = obj.attr("info");
    if (num == 0) {
        if(type == 1){
            if (source == 'new'){
                $("#deparCon").show();
            }else{
                $("#update_deparCon").show();
            }
        }else{
            if (source == 'new'){
                $("#zhiCon").show();
            }else{
                $("#update_zhiCon").show();
            }
        }
        obj.attr("info","1");
    } else{
        if(type == 1){
            if (source == 'new'){
                $("#deparCon").hide();
            }else{
                $("#update_deparCon").hide();

            }
        }else{
            if (source == 'new'){
                $("#zhiCon").hide();
            }else{
                $("#update_zhiCon").hide();
            }
        }
        obj.attr("info","0");
    }
    return false;
}
function toggleSe(obj){
    var toggleObj = obj.prev();
    toggleSelect(toggleObj);
}
// 设置选择部门的路径
function setDepatShow(){
    var DepartLink = "";
    if($("#handleEntry").is(":visible")){
        $("#handleEntry .activeId").each(function(){
            var activeIdObj = $(this);
            var activeId = activeIdObj.html();
            activeIdObj.parents(".levelItem").find(".departItem").each(function(){
                var id = $(this).children(":eq(1)").html();
                if (activeId == id) {
                    DepartLink +=  $(this).children(":eq(0)").html() + " - " ;
                }
            })
        });
        $("#oName").val(DepartLink.substring(0 , DepartLink.length-2));
    }else if($("#handleUpdate").is(":visible")){
        $("#handleUpdate .activeId").each(function(){
            var activeIdObj = $(this);
            var activeId = activeIdObj.html();
            activeIdObj.parents(".levelItem").find(".departItem").each(function(){
                var id = $(this).children(":eq(1)").html();
                if (activeId == id) {
                    DepartLink +=  $(this).children(":eq(0)").html() + " - " ;
                }
            })
        });
        $("#update_oName").val(DepartLink.substring(0 , DepartLink.length-2));
    }
}
// 选择职位
function getZhi(obj){
    var oid = obj.children(".hd").html();
    var oname = obj.children(":eq(0)").html();
    obj.siblings(":eq(0)").children(".activeZhiId").html(oid);
    obj.attr("style","background:#36C6D3; border-color:#36C6D3; color:#fff;  ");
    obj.siblings("span").attr("style","background:#fff; border-color:#666; color:#333; ");
    if($("#handleEntry").is(":visible")) {
        $("#zhiName").val(oname);
        $("#zhiId").val(oid);
    }else if($("#handleUpdate").is(":visible")) {
        $("#update_zhiName").val(oname);
        $("#update_zhiId").val(oid);
    }
}
// creator: 李玉婷，2020-05-28 16:21:44，修改手机号
function updateMobileNo(obj) {
    var valid = $("#handleUpdateForm").validate();
    var mobBool = valid.element(obj);
    var editId = $("#handleUpdate").data("id");
    var phone = obj.val();
    var oldNo = obj.siblings("input").val();
    if (mobBool && phone != oldNo) {
        $("#checkTip").data("name", "updateStuff");
        $("#confirmEditMobile").data("obj", obj);
        mobileCheckCallback(phone, editId);
    }
}
// creator: 李玉婷，2020-05-28 17:47:55，更新手机号确定
function updateMobileNoSure(){
    var mobile = $("#updateMobile").val();
    var uId = $("#handleUpdate").data("id");
    $.ajax({
        "url": "../userInfo/updateUserMobile.do",
        "data": {
            "userId": uId,
            "phone": mobile       // 高管手机号
        },
        success: function (res) {
            if(res == true){
                defineGetList(1,20,1);
                bounce_Fixed.cancel();
            }else{
                $("#errorMSTip").html("修改失败，请刷新重试！");
                bounce_Fixed3.show( $("#errorMS") ) ;
            }
        }
    })
}
// creator: 李玉婷，2020-05-28 11:59:40，查重后操作
function mobileCheckCallback(phone, id) {
    var name = $("#checkTip").data("name");
    mobileCheckRepeat(phone, id).then((data) => {
        var status = data["state"]; // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同
        if (status === 1) {
            if (name == 'newStuff') {
                tjSure(name);
            } else {
                bounce_Fixed.show($("#confirmEditMobile"));
            }
        } else if (status === 2 || status === 3) {
            var str =
                '<p>'+ data.cont +'</p>' ;
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").hide();$("#tjSure").attr("onclick", "tjSure('"+ name +"')");
            $(".canAllow").show();
        }else if(status === 0) {
            var str = '<p>'+ data.cont +'</p>';
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").show();$(".canAllow").hide();
        }
    }, (err) => {
    })
}
// creator: 李玉婷，2020-06-16 21:10:47，办理入职的手机号查重
function newStuffCheck() {
    var stuffPh = $("#handleEntry #mobile").val();
    $("#checkTip").data("name", "newStuff");
    mobileCheckCallback(stuffPh, '');
}
// creator: 李玉婷，2020-06-16 21:23:28，办理入职提交、修改手机号提交
function tjSure(name) {
    common_bounce.cancel();
    switch (name) {
        case 'newStuff':
            newStuff();
            break;
        case 'updateStuff':
            updateMobileNoSure();
            break;
    }
}
// creator: 李玉婷，2020-06-22 22:20:16，修改提示的取消操作
function cancelTip(){
    bounce_Fixed.cancel()
    var objCur = $("#confirmEditMobile").data("obj");
    var oldNo = objCur.siblings("input").val();
    objCur.val(oldNo);
}

// creator: 李玉婷，2020-05-28 19:52:07，职工档案 - 修改记录
function handleRecord(currPage, pageSize) {
    var employeeId = $("#handleUpdate").data('id');
    $("#basePageInfo").html('');
    $("#baseRecordName").html('档案管理修改记录');
    $.ajax({
        "url": "../userInfo/getUserBasicInfoHistory.do",
        "data": {
            passiveUserId: employeeId,
            pageSize: pageSize,
            currentPageNo: currPage
        },
        success: function (res) {
            var success = res['success'];
            if (success == '1' || success == 1) {
                var record = res['data']['userRoleHistoryList'];
                if (Number(res['data'].number) > 0) {
                    var pageInfo = res['data']['pageInfo'];
                    var totalPage = pageInfo["totalPage"];
                    var cur = pageInfo["currentPageNo"];
                    setPage( $("#basePageInfo") , cur ,  totalPage , "handleRecord"  );
                    $('.baseUpdateNum').html(res['data'].number);
                    $('.baseUpdateInfo').html(handleNull(res['data'].updateName) + ' &nbsp;&nbsp; ' + new Date(res['data'].updateTime).format('yyyy/MM/dd hh:mm:ss'));
                    var str = recordList(record, 'handle');
                    $(".updateBaseMessage").html(str);
                    $(".notEditBase").hide();
                    $(".recordMain").show();
                } else {
                    $(".notEditBase").html('当前数据尚未修改过。').show();
                    $(".recordMain").hide();
                }
                bounce_Fixed2.show($('#baseInfoRecord'));
            } else{
                layer.msg('获取失败！');
            }
        },
        error:function(){
            $("#errorMSTip").html("链接失败，请刷新重试！");
            bounce_Fixed3.show( $("#errorMS") ) ;
        }
    });
}

// creator: 张旭博，2021-06-17 10:15:06，职工档案 - 帮他激活 - 按钮
function helpActivateBtn() {
    bounce_Fixed.show($("#helpActivateTip"))
    $("#helpActivateTip input").prop("checked", false)
    var userInfo = $("#helpActivateTip").data('data')
    $("#helpActivateTip .helpActivateTip_userName").html(userInfo.userName)
    $("#helpActivateTip .helpActivateTip_mobile").html(userInfo.mobile)
}

// creator: 张旭博，2021-06-17 10:44:41，职工档案 - 帮他激活 - 确定（跳转到再次确认弹窗）
function helpActivateConfirmBtn() {
    var checked = $("#helpActivateTip input").is(":checked")
    if (checked) {
        var userInfo = $("#helpActivateTip").data('data')
        $("#helpActivateConfirmTip .helpActivateConfirmTip_mobile").html(userInfo.mobile)
        bounce_Fixed2.show($("#helpActivateConfirmTip"))
    } else {
        layer.msg("请勾选确认项！")
    }
}

// creator: 张旭博，2021-06-17 10:44:41，职工档案 - 帮他激活 - 最终确定
function sureHelpActivate() {
    var userInfo = $("#helpActivateTip").data('data')
    $.ajax({
        url: '../general/activationUser.do',
        data: {
            passiveUserId: userInfo.userID // 要被激活的用户id
        },
        success: function (res) {
            var data = res.data
            if (data === 1) {
                layer.msg("操作成功")
                bounce_Fixed.cancel()
                bounce_Fixed2.cancel()
                $("#helpActivateBtn").hide()
            } else {
                layer.msg("操作失败")
            }
        }
    })
}

// creator: 张旭博，2021-06-17 10:52:20，

//----------------------批量导入----------

/* creator：张旭博，2017-11-06 09:16:49，点击批量导入-按钮 */
// update: 李玉婷，2020-10-18 10:29:50，判断是否有未完成的导入
function leadingStart(){
    var userType = chargeRole("超管")
    if(!userType){ // || hasAuthority(10) === true
        $.ajax({
            url:"../userImport/unfinishedImportUser.do",
            success:function (data) {
                var userList = data.userImportList;
                if (userList.length > 0) {
                    $('.unfinishedForm .fa').attr("class","fa fa-circle-o");
                    $('#importNotCompleted').data("userData",data);
                    bounce.show($('#importNotCompleted'));
                } else {
                    $('#select_btn_1').val("");
                    $('.userListUpload').remove();
                    $('#leading .fileFullName').html("尚未选择文件");
                    bounce.show($('#leading'));
                }
            }
        });
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce_Fixed.show($("#errorTip"));
    }
}
// creator: 李玉婷，2020-08-28 08:55:00，导入确定
function sysUseImportOk() {
    if ($(".userListUpload").length <= 0) {
        $("#knowTip .knowWord").html('您需选择一个文件后才能“导入”！');
        bounce_Fixed3.show($("#knowTip"))
    } else {
        loading.open();
        $(".userListUpload a").click();
    }
}
// creator: 李玉婷，2020-12-14 13:58:17，最后保存确定
function lastSave(){
    $.ajax({
        url: "../userImport/completeImportUser.do",
        success: function (data) {
            var state = data.status;
            if (state == 1) {
                bounce.cancel();
                location.href = "../general/employeeIndex.do";
            } else {
                layer.msg("保存失败！");
            }
        }
    })
}
// creator: 李玉婷，2020-08-27 09:49:20，修改 - 一键清空
function clearTxt(obj) {
    obj.prev().val("");
}
// creator: 李玉婷，2020-10-21 16:42:51，下一步-数据保存
function saveImportList(userData) {
    // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {userData.sonOid = storage.sonOid;}
    $.ajax({
        url: "../userImport/saveImportUser.do",
        data: userData,
        success: function (data) {
            $(".importing").show();
            $(".importNoSave").hide();
            $(".main").hide();
            bounce.cancel();
            getImportUserList(data);
        }
    });
}

function clearUser() {
    var type = $("#clearUser").data("type");
    bounce.cancel();
    if (type == '1') {
        $(".importing").hide();
        $(".importNoSave").hide();
        $(".main").show();
        defineGetList(1,20,1);
    } else {
        turnCancel(2)
    }
}
// creator: 李玉婷，2020-10-18 14:06:42，放弃
function turnCancel(type){
    $.ajax({
        url: "../userImport/giveUpImportUser.do",
        success: function (data) {
            var state = data.status;
            if (state == '1')
                if (type == '1') {
                    $('#select_btn_1').val("");
                    $('.userListUpload').remove();
                    $('#leading .fileFullName').html("尚未选择文件");
                    bounce.show($('#leading'));
                }else{
                    $(".importing").hide();$(".importNoSave").hide();$(".main").show();
                    defineGetList(1,20,1);
                }

        }
    })
}
//---------------------- 辅助函数 -----------------------//

//creator：李玉婷，2017-11-24 返回按钮
function goback(){
    $(".main").show();
    $(".employeeView").hide();$(".employeeEdit").hide();$(".goBack").css("display","none");
    $("#editEmployeeInfo").css("display","none").siblings().css("display","inline-block");
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
    }
    obj.parent().siblings(".textMax").text( curLength +'/' + max );
    obj.siblings(".textMax").text( curLength +'/' + max );
}
//creator:lyt date:2018/11/28 比较修改记录修改前后不同
function compareD(front,now){
    if(front == now){
        return '<i>' + handleNull(now) +' </i>'
    }else{
        return '<i class="redFlag">' + handleNull(now) + '</i>'
    }
}
function getLevelName(level_num){
    var level = "";
    switch(level_num)
    {
        case 1 : level = "一"; break;
        case 2 : level = "二"; break;
        case 3 : level = "三"; break;
        case 4 : level = "四"; break;
        case 5 : level = "五"; break;
        case 6 : level = "六"; break;
        case 7 : level = "七"; break;
        case 8 : level = "八"; break;
        default: level = "子"; break;
    }
    level += "级部门";
    return level ;
}

//---------------------- 以后有可能用的公共函数 -----------------------//

// creator:hxz 2018-08-07 获取导入的员工列表
function getImportUserList(data) {
    var staffUserList = data["userImportList"];
    var manageList = data["manageList"];
    var buttonState = data["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰
    if (buttonState == 1) {
        $("#ok").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "confirmOrgTip()");
    } else {
        $("#ok").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
    }
    $(".importing .importSum").html(data.importSum);
    $(".importing .saveSum").html(data.tureImportSum);
    var str = "";
    if (staffUserList && staffUserList.length > 0) {
        for (var i = 0; i < staffUserList.length; i++) {
            var isP = staffUserList[i]["ordinaryEmployees"]; // 是否普通员工
            var leader = handleNull(staffUserList[i]["leader"]); // 直接上级id
            var leaderName = handleNull(staffUserList[i]["leaderName"]); // 直接上级name
            var userID = staffUserList[i]["id"]; // 用户id
            var managerCode = staffUserList[i]["managerCode"];  // 所属高管
            var strP = setP(isP, userID);
            var strGroup = setGroup(managerCode, manageList, userID);
            str += "<tr data-id='"+ staffUserList[i]["id"] +"'>" +
                "    <td>" + staffUserList[i]["userName"] + "</td>" +
                "    <td>" + staffUserList[i]["mobile"] + "</td>" +
                "    <td>" + strP +
                "    </td>" +
                "    <td>" +
                "        <div class=\"z\" onclick='getOption($(this) ,1 , event)'> " +
                "           <input type='text' class='name' value='" + leaderName + "'>" +
                "           <input type='hidden' class='id' value='" + leader + "'>" +
                "           <input type='hidden' class='type' value='1'>" +
                "           <input type='hidden' class='userID' value='" + userID + "'>" +
                "           <div class='options'></div>" +
                "        </div>"+
                "    </td>" +
                "    <td>" + strGroup +
                "    </td>" +
                "    <td>" +
                "       <span class='ty-color-blue btn'  data-name='update'>修改</span>"+
                "       <span class='ty-color-red btn'  data-name='del'>删除</span>"+
                "    </td>" +
                "</tr>"
        }
    }
    $(".importing tbody").html(str);
}
// creator:hxz 2018-08-13  生成是不是普通员工的下拉框
function setP(type, userID) {
    var na = "有", val = "0";
    if (type == 1) {
        na = "无";
        val = "1";
    }
    var str = "<div class=\"z\" onclick='getOption($(this) ,0 , event)'>" +
        "           <input type='text' class='name' value='" + na + "' readonly>" +
        "           <input type='hidden' class='id' value='" + val + "'>" +
        "           <input type='hidden' class='type' value='0'>" +
        "           <input type='hidden' class='userID' value='" + userID + "'>" +
        "           <div class='options'>" +
        "               <option onclick='selectThis($(this) , event)' value=\"1\" >无</option>" +
        "               <option onclick='selectThis($(this) , event)' value=\"0\">有</option>" +
        "           </div>" +
        "        </div>";
    return str;
}
// creator:hxz 2018-08-13  生成所属高管的下拉框
function setGroup(manageCode, manageList, userID) {
    var str = "<select class=\"s\" userID='" + userID + "' onchange='selectThis($(this))'><option value='0' >请选择</option>";
    if (manageList && manageList.length > 0) {
        for (var i = 0; i < manageList.length; i++) {
            var m = manageList[i]["userID"] + '-' + manageList[i]["roleCode"];
            var charact = changeWorkFuc(manageList[i].roleCode);
            if (manageCode == manageList[i]["roleCode"]) {
                str += "<option value='" + m + "' selected>" + charact + "</option>";
            } else {
                str += "<option value='" + m + "'>" + charact + "</option>";
            }
        }
    }
    str += "</select>";
    return str;
}
// creator:hxz 2018-08-13  获取直系上级的下拉列表
var timer = 0; // 直系上级 匹配的定时器
function getOption(obj, type, event) {
    event.stopPropagation();
    $(".options").hide();
    obj.find(".options").show();
    if (type == 0) { // 是否直接普通员工

    }
    else { // 选择直接上级的下拉框
        var userID = obj.children("input.userID").val();
        var name = obj.children(".name").val();
        let param = {"id": userID};
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            //"url": "../approval/getOptionalUser.do",
            "url": "../userImport/selectOptionalLeader.do",
            "data": param,
            success: function (res) {
                var userList = res;
                var str = "<option value='' style='display: none'></option>";
                if (userList && userList.length > 0) {
                    for (var i = 0; i < userList.length; i++) {
                        var _na = userList[i]["userName"] + " - - " + userList[i]["mobile"];
                        var index = _na.indexOf(name);
                        if (index != -1) {
                            obj.siblings(".id").val(userList[i]["id"]);
                            str += "<option onclick='selectThis($(this) , event)' value='" + userList[i]["id"] + "' data-flag='"+ userList[i]["status"] +"'>" + _na + "</option>";
                        } else {
                            str += "<option onclick='selectThis($(this) , event)' value='" + userList[i]["id"] + "' data-flag='"+ userList[i]["status"] +"' style='display: none'>" + _na + "</option>";
                        }
                    }
                }
                obj.find(".options").html(str);
            }
        });
        if (timer) {
            clearInterval(timer);
        }
        timer = setInterval(function () {
            match(obj.children(".name"))
        }, 300)
    }

}

// creator:hxz 2018-08-13  直系上级输入匹配
function match(obj) {
    var name = obj.val();
    if ($.trim(name) == "") {
        obj.siblings(".options").children().show();
    } else {
        obj.siblings(".options").children().each(function () {
            var _na = $(this).html();
            var index = _na.indexOf(name);
            if (index != -1) {
                $(this).show();
                if (_na == name) {
                    obj.siblings(".id").val($(this).val());
                }
            } else {
                $(this).hide();
            }
        })
    }
}
// creator:hxz 2018-08-13  页面中全部选择操作
function selectThis(obj, event) {
    var data = {};
    var userID = "";
    if (event) { // 设置普通员工和 直接上级
        event.stopPropagation();
        var val = obj.val();
        var name = obj.html();
        var ops = obj.parent(".options");
        var type = ops.siblings(".type").val(); // 0 - 是否普通员工 ， 1 - 直系上级
        userID = ops.siblings(".userID").val();
        data["leaderSorce"] = obj.data("flag");
        if (type == 1) {
            data["leaderId"] = val;
        } else {
            data["ordinaryEmployees"] = val;
        }
    } else { // 设置高管
        var val = obj.val();
        var arr = val.split('-');
        userID = obj.attr("userID");
        if (arr[0] != "null" && arr[0] != "") data["manageId"] = arr[0];
        data["manageCode"] = arr[1];
    }
    data["userId"] = userID;
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {data.sonOid = storage.sonOid;}
    $.ajax({
        "url": "../userImport/updateImportUser.do",
        "data": data,
        success: function (res) {
            var status = res["status"];
            var buttonState = res["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰
            if (buttonState == 1) {
                $("#ok").attr("class", "ty-btn ty-btn-blue ty-btn-big ty-circle-3").attr("onclick", "confirmOrgTip()");
            } else {
                $("#ok").attr("class", "ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("onclick")
            }
            if (event) {
                if (status == 1) { // 设置没问题
                    var type = ops.siblings(".type").val();
                    ops.siblings(".id").val(val);
                    ops.siblings(".name").val(name);
                    if (type == 0) {
                        resetImportUser();
                    } else {
                        loading.close();
                    }
                } else if (status == 2) {
                    loading.close();
                    layer.msg("下级有员工，不能变成普通员工,设置失败！");
                } else {
                    loading.close();
                    layer.msg("设置失败！");
                }
                clearInterval(timer);
                ops.hide();
            } else {
                loading.close();
                if (status == 1) {
                } else {
                    layer.msg("设置所属高管失败！");
                }
            }
        },
        complete:function(){  }
    })
}
// creator: 李玉婷，2020-12-11 11:12:02，删除、是否有下属更改对直接上级影响
function resetImportUser() {
    // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
    let param = {};
    let storage = $(".part_own").data("storage")
    if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
    $.ajax({
        url:"../userImport/unfinishedImportUser.do",
        data: param,
        success:function (data) {
            getImportUserList(data);
        }
    });
}
function confirmOrgTip() {
    var sum = $(".importing table tbody tr").length;
    var importNum = $(".importing .importSum").html();
    $("#lastSave #saveSum").html(importNum);
    $("#lastSave #saveAble").html(sum);
    bounce.show($("#lastSave"));
}
// creator: 李玉婷，2020-08-30 07:17:44，删除职工
function delUserSure() {
    var obj = $("#delUser").data("obj");
    var type = $("#delUser").data("type");
    var trObj = obj.parents("tr");
    bounce.cancel();
    if (type == '1'){
        trObj.remove();
        var len = $(".importNoSave tbody tr").length;
        $(".importNoSave .initWrong").html(len);
    } else if(type == '2') {
        let param = {"id": trObj.data("id")};
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            "url": "../userImport/deleteImportUser.do",
            "data": param,
            success: function (res) {
                var status = res["status"];
                if (status == 1) {
                    resetImportUser();
                } else {
                    loading.close();
                    layer.msg("删除失败！");
                }
            },
            complete:function(){  }
        })
    }
}
// creator: 李玉婷，2020-11-12 09:27:09，修改材料定时器
function setUpdateUserTimer(){
    bounce.everyTime('0.5s','importUpdate',function(){
        var call = $("#updateUser .userPhone").val();
        var callOld = $("#updateUser .userPhone").data("oldMobile");
        var name = $("#updateUser .userName").val();
        var nameOld = $("#updateUser .userName").data("oldName");
        if(call != "" && name != "" && (call !== callOld || name !== nameOld)){
            $("#importUpdateUser").prop("disabled",false);
        }else{
            $("#importUpdateUser").prop("disabled",true);
        }
    });
}
// creator: 李玉婷，2020-08-27 09:58:54，修改职工信息
function updateUserInfo() {
    var call = $("#updateUser .userPhone").val();
    if (!testMobile(call)) {
        var str = '<p>您录入的手机号有误。</p><p>请确认！</p>';
        $("#iknowTip .iknowWord").html(str);
        bounce_Fixed3.show($("#iknowTip"))
    } else {
        var old = $("#updateUser .userPhone").data("oldMobile");
        var same = 0;
        var type = $("#updateUser").data("type");
        var name = '';
        if (type == 1) {
            $(".importNoSave table tbody tr").each(function () {
                var info = $(this).data("info");
                if (call == info.mobile && old != info.mobile){
                    same++;
                    name = info.userName;
                }
            });
        } else if (type == 2) {
            $(".importing tbody tr").each(function () {
                var otherMb = $(this).find("td").eq(1).html();
                if (call == otherMb && old != otherMb){
                    same++;
                    name = $(this).find("td").eq(0).html();
                }
            });
        }
        if(same >0) {
            var str = '<p>您录入的手机号与本次导入的'+ name +'手机号相同。</p><p>请确认！</p>';
            $("#iknowTip .iknowWord").html(str);
            bounce_Fixed3.show($("#iknowTip"));
        }else {
            $("#checkTip").data("name", "updateImportStuff");
            mobileCheckImport(call, name);
        }
    }
}
// creator: 李玉婷，2020-10-21 11:21:59，修改职工信息确定
function updateImportUserSure(){
    var obj = $("#updateUser").data("obj");
    var type = $("#updateUser").data("type");
    var trObj = obj.parents("tr");
    var call = $("#updateUser .userPhone").val();
    var name = $("#updateUser .userName").val();
    if (type == 1) {
        var trData = obj.parents("tr").data("info");
        trObj.find("td").eq(0).html(name);
        trObj.find("td").eq(1).html(call);
        trData.userName = name;
        trData.mobile = call;
        trObj.data("info", trData);
        bounce.cancel();
        common_bounce.cancel();
    }
    else if (type == 2) {
        let param = {
            "id": trObj.data("id"),
            "mobile": call,
            "userName": name
        };
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            "url": "../userImport/updateImportUserMobile.do",
            "data": param,
            success: function (res) {
                var status = res["status"];
                if (status == 1) {
                    bounce.cancel();
                    common_bounce.cancel();
                    trObj.find("td").eq(0).html(name);
                    trObj.find("td").eq(1).html(call);
                } else if (status == -1) {
                    layer.msg("和本次导入的其他手机号重复！");
                } else {
                    layer.msg("修改失败！");
                }
            }
        })
    }
}
// creator: 李玉婷，2020-05-28 11:59:40，查重后操作
function mobileCheckImport(phone, name) {
    mobileCheckRepeatImport(phone, name).then((data) => {
        var status = data["status"]; // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同 4-与冻结一致
        if (status === 1) {
            updateImportUserSure();
        } else if (status === 2) {
            var str =
                '<p>您录入的手机号与公司已离职者'+ data.name +'手机号相同。</p>' ;
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").hide();$("#tjSure").attr("onclick", "updateImportUserSure()");
            $(".canAllow").show();
        } else if (status === 3) {
            var str =
                '<p>您录入的手机号与公司'+ data.name +'曾用过的手机号相同。</p>' ;
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").hide();$("#tjSure").attr("onclick", "updateImportUserSure()");
            $(".canAllow").show();
        }else if(status === 0 || status === 4) {
            var str =
                '<p>您录入的手机号与公司'+ data.name +'手机号相同。</p>'+
                '<p>请确认！</p>';
            common_bounce.show($("#checkTip")) ; $("#checkTip_ms").html(str);
            $(".notAllow").show();$(".canAllow").hide();
        }
    }, (err) => {
    })
}
// creator: 李玉婷，2020-11-22 10:25:36，输入手机号 查重接口
function mobileCheckRepeatImport(phone, name){
    return new Promise( (resolve, reject) => {
        var param = {
            "mobile": phone,
            "userName": name
        };
        // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
        let storage = $(".part_own").data("storage")
        if (storage && storage.source === 1) {param.sonOid = storage.sonOid;}
        $.ajax({
            "url": "../userImport/updateFalseUserEnter.do",
            "data": param,
            "async": false,
            success: function (res) {
                resolve(res);
            },
            error: function () {
                reject('')
            }
        })
    });
}
// -------------------多地点-分支机构-------------------
//creator:liyuting date:2022/08/12 可选择的机构列表
function getOrgSonOrgs(){
    $.ajax({
        async: false,
        url:'../sonOrg/getOrgSonOrgs.do',
        success:function(data){
            let list = data.data;
            let options = ``;
            if (list && list.length > 0) {
                if (list.length > 1 || list.length === 1 && list[0].id !== useInfo.id) {
                    for(var i=0;i<list.length;i++) {
                        options +=
                            `<div class='ty-form-checkbox' skin="green">
                             <span data-val="${list[i].id}">${list[i].name}</span>
                             <i class="fa fa-check"></i>
                         </div>`;
                    }
                }

            }
            if (useInfo.orgType === 1 && list.length > 1){//orgType 是4 就是子机构， 是1 就是总机构
                matchType = true;
                $(".belongScreenBody").show();
                $(".belongOrg").show();
            } else {
                matchType = false;
                $(".belongScreenBody").hide();
                $(".belongOrg").hide();
            }
            $("#belongOrg").html(options);
        }
    })
}
/*creator:lyt 2022/8/19 下午 6:18 */
function checkAllOrg(e){
    e.stopPropagation();
    $("#belongOrg .ty-form-checkbox").addClass("ty-form-checked");
}

// creator: 李玉婷，2020-11-02 10:15:50，工作特点转换
function changeWorkFuc(str) {
    var charact = '';
    switch (str) {
        case "general":
            charact = "不含销售工作与财会工作";
            break;
        case "finance":
            charact = "包含财务工作";
            break;
        case "sale":
            charact = "含有销售工作";
            break;
        case "accounting":
            charact = "包含会计工作";
            break;
    }
    return charact;
}

/* creator：张旭博，2017-11-06 09:23:39，时间插件初始化 */
laydate.render({elem: '#base_birthday',format: 'yyyy/MM/dd'});
laydate.render({elem: '#joinTime',format: 'yyyy/MM/dd'});
laydate.render({elem: '#onDutyDateEdit',format: 'yyyy/MM/dd'});
laydate.render({elem: '#workBegin', type: 'month', format: 'yyyy/MM'});
laydate.render({elem: '#workEnd', type: 'month', format: 'yyyy/MM'});
laydate.render({elem: '#studyBegin', type: 'month', format: 'yyyy/MM'});
laydate.render({elem: '#studyEnd', type: 'month', format: 'yyyy/MM'});
//    creator 侯杏哲 2017-03-07 处理导入用户后的反馈
//    var importTimer = 0 ;
//    function reloadPage(){
//        loading.open() ;
//        bounce.cancel() ;
//        var t = 1 ;
//        importTimer = setInterval(function(){
//            t += 1 ;
//            var data = $("#importCon").contents().find("body").html() ;
//            if(data == 0 || data == 1 ){
//                loading.close() ;
//                if(data == 1 ){
//                    location.href="../../general/employeeIndex.do" ;
//                }else{
//                    $("#mt_tip_ms").html("文件导入失败，请重新导入") ;
//                    bounce.show($("#mtTip")) ;
//                    clearInterval( importTimer ) ;
//                    $("#importCon").attr( "src" , "../../script/general/employeeImport.jsp") ;
//                }
//            }else{
//                if( t >= 20 ){
//                    loading.close() ;
//                    $("#mt_tip_ms").html("文件导入失败，请重新导入") ;
//                    bounce.show($("#mtTip")) ;
//                    clearInterval( importTimer ) ;
//                    $("#importCon").attr( "src" , "../../script/general/employeeImport.jsp") ;
//                }
//            }
//        } , 500 ) ;
//
//    }
//    function submitForm(obj) {
//        $(obj).parents("form").submit();
//    }