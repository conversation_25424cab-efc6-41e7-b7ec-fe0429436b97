
$(function () {
    // 初始化
    $(".importIntro").hide();
    getCoreUserRole();
    $(".ty-colFileTree").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-angle-right")){
            $(this).find("i").eq(0).removeClass("fa-angle-right") ;
            $(this).find("i").eq(0).addClass("fa-angle-down")
            $(this).next().show();
        }else if($(this).find("i").eq(0).hasClass("fa-angle-down")){
            $(this).find("i").eq(0).removeClass("fa-angle-down") ;
            $(this).find("i").eq(0).addClass("fa-angle-right") ;
            $(this).next().hide();
        }

    });
});
var editModule = {} ,  //当前模块信息
    allModule = [
    {
        "e_name":'core',
        "c_name":'投诉管理',
        "type":1
    },{
        "e_name":'projectCore',
        "c_name":'项目管理',
        "type":5
    },{
        "e_name":'improvementCore',
        "c_name":'持续改进管理',
        "type":7
    },{
        "e_name":'vehicleCore',
        "c_name":'车务管理',
        "type":9
    },{
        "e_name":'pickingCore',
        "c_name":'领料分工',
        "type":11
    },{
        "e_name":'buySalesCore',
        "c_name":'购销统筹',
        "type":13
        }
];

// creator: 张旭博，2018-06-07 10:56:19，分配或修改负责人（多个模块公用）
function changePeopleInChargeBtn(num) {
    editModule = allModule[num] ;
    bounce.show($("#newDivision"));
    getOrgListUserList(num);
    $('body').everyTime('0.5s','newDivision',function(){
        if($(".ty-treeItemActive>.fa-file").length>0){
            $("#sureNewDivisionBtn").prop("disabled",false);
        }else{
            $("#sureNewDivisionBtn").prop("disabled",true);
        }
    });
}

// creator: hxz 2018-06-05 获取所有项目当前核心人物

// updater: 张旭博，2018-06-07 14:27:18，获取所有项目当前核心人物
function getCoreUserRole() {
    $.ajax({
        url:"../coreSetting/getCoreUserRole.do" ,
        data:{},
        success:function( data ){
            var status = data["status"];
            for(var i in data){
                var project = data[i].project,
                    coreUser = data[i].coreUser;
                $("." + project).show();
                if(coreUser === null){
                    $("." + project + " .coreUser").html("暂无").data("value","");
                }else{
                    var coreUserName = coreUser.userName;
                    $("." + project + " .coreUser").html(coreUserName).data("value",coreUserName);
                }
            }
        }
    }) ;
}

// creator: 张旭博，2018-04-25 11:04:06，保存核心人物接口 - 打开确认弹窗
function confirmSaveCoreUser() {
    var userId = $("#newDivision .ty-treeItemActive").attr("id");
    var newCoreName = $("#newDivision .ty-treeItemActive > span").html();
    var nowCoreName = $("." + editModule.e_name + " .coreUser").html();
    if(nowCoreName === "暂无"){
        $("#normalTip .tipWord").html( editModule.c_name + '将由<span class="ty-color-blue"> '+ newCoreName +' </span>全权负责')
    }else{
        $("#normalTip .tipWord").html( editModule.c_name + '负责人将由<span class="ty-color-blue"> '+ nowCoreName +' </span>变更为 <span class="ty-color-blue"> '+newCoreName)
    }
    bounce_Fixed.show($("#normalTip"));
    $("#normalTip").data("userId",userId);
}

/* creator：张旭博，2018-01-23 10:01:44，保存修改核心人物接口 */
function sureSaveCoreUser() {
    var userId  = $("#normalTip").data("userId");
    $.ajax({
        url: "../coreSetting/editCoreUserRole.do" ,
        data: {
            "userId":userId,
            "type":editModule.type,
        } ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open()},
        success:function( data ){
            bounce_Fixed.cancel();
            bounce.cancel();
            var status = data["status"];
            if(status === 1){
                layer.msg("保存成功");
                getCoreUserRole();
            }else{
                $("#errorTip .tipWord").html("保存失败!") ;
                bounce.show($("#errorTip")) ;
            }

        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}

/* creator：张旭博，2018-01-22 10:31:36，过去部门及部门下人员列表接口 */
function getOrgListUserList(num) {
    var url = "../coreSetting/getOrgListUserList.do" ;
    var data = {"coreCode": editModule.e_name }
    if(num === 0){
        url = "../complaint/getCoreSettingUserListByComplaint.do" ;
        data = {"oid": sphdSocket.org.id }
    }
    $.ajax({
        url: url,
        data:data,
        success:function( data ){
            var list = data["data"];
            var departmentStr = '';
            departmentStr += '<ul class="level1" level="1">' + getNextLevelList(list, num)+ '</ul>';
            $(".ty-colFileTree").html(departmentStr);

        },
        error:function(){
            $("#errorTip .tipWord").html("系统错误，请重试!") ;
            bounce.show($("#errorTip")) ;
        } ,
        complete:function(){ loading.close();}
    }) ;
}
function getNextLevelList(list, num) {
    var departmentStr = '';
    for(var i=0;i<list.length;i++){
        var subList = list[i].subList;
        var userList = list[i].userList;
        if(num === 0){ // 投诉处理的
            for(var q in list){
                departmentStr +=    '<li>' +
                    '<div class="ty-treeItem" id='+ list[q]["userID"] +'>' +
                    '<i class="ty-fa"></i>' +
                    '<i class="fa fa-file"></i>' +
                    '<span>'+list[q]["userName"]+'</span>' +
                    '</div>' +
                    '</li>';
            }
            return departmentStr;
        }

        if(!subList && subList.length === 0 && !userList && userList.length === 0){
            departmentStr +=    '<li>' +
                '<div class="ty-treeItem" id='+ list[i]["id"] +'>' +
                '<i class="ty-fa"></i>' +
                '<i class="fa fa-folder"></i>' +
                '<span>'+list[i]["name"]+'</span>' +
                '</div>';
        }else if(subList.length > 0 ||userList.length > 0){
            departmentStr +=    '<li>' +
                '<div class="ty-treeItem" id='+ list[i]["id"] +'>' +
                '<i class="fa fa-angle-right"></i>' +
                '<i class="fa fa-folder"></i>' +
                '<span>'+list[i]["name"]+'</span>' +
                '</div>' +
                '<ul>';

        }
        if(subList && subList.length > 0){
            departmentStr +=        getNextLevelList(subList);
        }
        if(userList && userList.length > 0){
            for(var j in userList){
                departmentStr +=    '<li>' +
                    '<div class="ty-treeItem" id='+ userList[j]["userID"] +'>' +
                    '<i class="ty-fa"></i>' +
                    '<i class="fa fa-file"></i>' +
                    '<span>'+userList[j]["userName"]+'</span>' +
                    '</div>' +
                    '</li>';
            }
        }
        if(subList && subList.length > 0 || userList && userList.length > 0){
            departmentStr += '</ul></li>';
        }else {
            departmentStr += '</li>';
        }

    }
    return departmentStr;
}

function changeHistory(type) {
    editModule = allModule.find(value=>value.type === type)
    $("#coreChangeHistory .module").html(editModule.c_name)
    bounce.show($("#coreChangeHistory"))
    $.ajax({
        url: '../coreSetting/getUserRoleHistoryList.do',
        data: {
            type: type // 1-投诉核心人物 5-项目管理核心人物 7-持续改进核心人物 9-汽车核心人物 11-领料核心人物 13-统筹管理人物
        },
        success: function (data) {
            var content = data.data
            var list = content.userRoleHistoryList
            $("#coreChangeHistory .changeHistory").hide()
            if(list.length === 0){
                $("#coreChangeHistory .userRole").html('')
                $("#coreChangeHistory .isChange").html('')
                $("#coreChangeHistory .createInfo").html('')
                $("#coreChangeHistory .changeHistory tbody").html('')
            }else if (list.length === 1) {
                $("#coreChangeHistory .userRole").html('')
                $("#coreChangeHistory .isChange").html('当前资料未经修改')
                $("#coreChangeHistory .createInfo").html('创建人：' + content.updateName + ' ' + formatTime(content.updateTime, true))
                $("#coreChangeHistory .changeHistory tbody").html('')
            } else {
                $("#coreChangeHistory .userRole").html(content.userName)
                $("#coreChangeHistory .isChange").html('当前资料为第' + content.number + '次修改后的结果。')
                $("#coreChangeHistory .createInfo").html('修改人：' + list[list.length - 1].updateName + ' ' + formatTime(list[list.length - 1].updateTime, true))
                $("#coreChangeHistory .changeHistory").show()
                var str = ''
                for (var i = 0; i<list.length; i++) {
                        str +=  '<tr>' +
                                '<td>' + list[i].dataState + '</td>'+
                                '<td>' + list[i].userName + '</td>'+
                                '<td>' + list[i].updateName + ' ' + formatTime(list[i].updateTime, true) + '</td>'+
                                '</tr>'
                }
                $("#coreChangeHistory .changeHistory tbody").html(str)
            }
        }
    })
}
