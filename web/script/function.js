var yicunzai="yicunzai";

function Add(leftsel,rightsel,stroe) {
   //alert('add');
	allRoles = document.getElementById(leftsel);
	selectids = document.getElementById(stroe);
	selectRoles = document.getElementById(rightsel);
	for(var i =0;i<allRoles.options.length;i++){
		if(allRoles.options[i].selected==true){
			var ifExit = false;
			for(var n =0;n<selectRoles.options.length;n++){
				if(selectRoles.options[n].value == allRoles.options[i].value){
					ifExit = true;
				}
			}
			if(ifExit == false){
				selectRoles.add( new Option(allRoles.options[i].text,allRoles.options[i].value));
			}else{
				alert(yicunzai);
			}
			allRoles.remove(i);
			i--;
		}
	}
	id="";
	for(var m =0;m<selectRoles.options.length;m++){
		id+=selectRoles.options[m].value +",";
	}
	selectids.value=id;
}
function Remove(leftsel,rightsel,stroe) {
	//alert('remove');
	selectids = document.getElementById(stroe);
	allRoles = document.getElementById(leftsel);
	selectRoles = document.getElementById(rightsel);
	for(var i =0;i<selectRoles.options.length;i++){
		if(selectRoles.options[i].selected==true){
			allRoles.add( new Option(selectRoles.options[i].text,selectRoles.options[i].value));
			selectRoles.remove(i);
			i--;
		}
	}
	id="";
	for(var m =0;m<selectRoles.options.length;m++){
		id+=selectRoles.options[m].value +",";
	}
	selectids.value=id;
}

//open sized window
function openWindow(sHref,strWidth,strHeight) {
	var strLeft=(screen.availWidth-strWidth)/2;
	var strTop=(screen.availHeight-strHeight)/2;
	var strRef="";
	strRef=strRef+"width="+strWidth+"px,height="+strHeight+"px,";
	strRef=strRef+"left="+strLeft+"px,top="+strTop+"px,";
	strRef=strRef+"resizable=yes,scrollbars=yes,status=yes,toolbar=no,systemmenu=no,location=no,borderSize=thin";//channelmode,fullscreen
	var openerobj= window.open(sHref,'newwin',strRef,false);
	openerobj.focus();
}

function  openWindowWithName(sHref,strWidth,strHeight,sName){

  var strLeft=(screen.availWidth-strWidth)/2;
  var strTop=(screen.availHeight-strHeight)/2;
  var strRef="";
  strRef=strRef+"width="+strWidth+"px,height="+strHeight+"px,";
  strRef=strRef+"left="+strLeft+"px,top="+strTop+"px,";
  strRef=strRef+"resizable=no,scrollbars=yes,status=yes,toolbar=no,systemmenu=no,location=no,borderSize=thin";//channelmode,fullscreen
  window.open(sHref,sName,strRef,false);
}

function refreshOpener(){
    opener.location.reload();
    window.close();
	return false ;
}
function LTrim(str) 
{ 
	var i;
	for(i=0;i<str.length;i++)
	{
	if(str.charAt(i)!=" "&&str.charAt(i)!=" ")break;
	}
	str=str.substring(i,str.length);
	return str;
}
function RTrim(str) 
{ 
	var i;
	for(i=str.length-1;i>=0;i--)
	{
	if(str.charAt(i)!=" "&&str.charAt(i)!="?")break;
	}
	str=str.substring(0,i+1);
	return str;
} 

function Trim(str) 
{ 
	return LTrim(RTrim(str));
} 









































