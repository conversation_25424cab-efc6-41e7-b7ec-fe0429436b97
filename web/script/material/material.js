var superList = []; // 供应商列表
$(function () {
    // 获取物料的基本列表
    getMaterialByKindid( "" , 1 , 10 , 1);
    // 获取供应商列表
    getSupList( $("#supplier"))
});
function getSupList(obj){
    superList = [];
    $.ajax({
        url:"retrievalSupplierByName.do",
        data:{ "name" : "" },
        success:function (data) {
            var list = data["supplierList"];
            if(list && list.length > 0){
                for(var i=0 ; i < list.length ; i++){
                    var supID = list[i]["id"] ;
                    var infoList = list[i]["mtSupplierMaterialHashSet"] || [] ;
                    if( infoList.length > 0 ){
                        var info = "" , goOn = true ; // goOn - 标识是否继续循环
                        for(var j = 0 ; j < infoList.length  ; j++){
                            var _supId = infoList[j]["supplier_"] ;
                            if(_supId == supID){
                                info = infoList[j] ;
                                info["id"] = supID ;
                                info["sbId"] = "" ;
                                info["name"] = list[i]["name"] ;
                                info["fullName"] = list[i]["fullName"] ;
                                goOn = false ;
                                superList.push(info);
                            }
                        }
                    }
                }
            }
            setSupplier(superList, obj);
        }
    }) ;
}
var isall = true ; // 用于处理页面显示的分类
// 根据pid找到子级分类的列表
function getMtCategoryListBypid( pid ){
    $.ajax({
        url:"getMtCategoryBypid.do",
        data:{ pid:pid },
        success:function (data) {
            var status = data["status"];
            var categoryList = data["mtCategories"];
            // var categoryList = data["categoryList"];
            if (status == 1 && categoryList != undefined ){
                setMtKind(categoryList); // 将获取到的列表插入页面
            }
            return 0 ; 

        }
    })
}
// 判断当前列表数据是来源于商品还是物料
function chargeMtisFromGoods (){
     var htmlkind = $("#curID").children(":eq(1)").children(":eq(0)").html();
    if(htmlkind == "商品" || htmlkind == "半成品" || htmlkind == "外购成品" ){  return true ;     }
    return false ;
}
function chargeMtgetHtmlkind  () {
    var htmlkind = $("#curID").children(":eq(1)").children(":eq(0)").html();
    return htmlkind ;
}
//creator ：侯杏哲 2018-02-25 盘点
function inventoryMtBtn( obj ){
    bounce.show( $("#inventory") ) ;
}
// updator : 侯杏哲 2018-03-06  新增物料
function addMtBtn(){
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
    $("#mtInfo").hide(); $("#addBtn").hide();
    $(".ty-header>p").append('<span class="nav_"> / </span>'+
        '<span class="navTxt"><i class=""></i>新增物料</span>') ;
    $("#mtAdd").show();
    $("#mtTTL").html("新增物料");
    $("#mtAdd input").val(""); //  set addMt container input null
    $("#mt_type").val("add_mt") ;
    $(".addsupTip").html("");
    $(".mt_hasSelect").html("");
    $("#suppierList").html("") ;
    //  为一级分类赋值
    $("#mtKindContainer").html("");
    getMtCategoryListBypid("");
    $("#mt_isControl").val("0");
    // $("#suppierList").html(""); // 供应商清空
    $("#editNormalMaterialMess").show();
    $("#editGoodsMaterialMess").hide();
    $(".notCaiGou").show() ; // 显示供应商新增
    $("#editNormalMaterialMess input").removeAttr("readonly");
    $("#qualityCon input").removeAttr("readonly");
    $("#mt_minStorage").attr("readonly", "readonly") ;
    $("#mt_oralStorage").attr("readonly", "readonly") ;
}
// updator : 侯杏哲 2018-02-25 编辑物料
var mtUpdateObj_Tr = null ;
function editMtBtn(obj){
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
    $("#addBtn").hide() ;
    $("#strNav").append('<span class="nav_"> / </span>'+
        '<span class="navTxt"><i class=""></i>修改物料</span>') ;
    mtUpdateObj_Tr = obj.parent().parent() ;
    var isFromGoods = false ;
    var htmlkind = $("#curID").children(":eq(1)").children(":eq(0)").html();
    if(htmlkind == "商品" || htmlkind == "半成品" || htmlkind == "外购成品" ){  isFromGoods = true ;     }
    var editType = isFromGoods ? "update_gs" : "update_mt" ;
    if (htmlkind == "商品" || htmlkind == "半成品") {
        // 只有商品/半成品 没有供应商 ， 其他都显示供应商，且可以编辑 初始库存
        $(".notCaiGou").hide() ;
        $("#goods_oralStorage").removeAttr("readonly");
    }else{
        $(".notCaiGou").show() ;
        $("#goods_oralStorage").attr("readonly","readonly");
    }
    var id = obj.siblings(".hd").html(); // 物料id 
    if(id == "" || id == undefined){
        $("#mt_tip_ms").html("未获取要编辑的信息，请稍后重试！");
        $("#mtTip").show().parent().show();
    }else{
        var editMtData = { "baseId" : id  } ;
        var editUrl = "toUpdateBaseStockInfoQuality.do" ;
        if( isFromGoods ){ editUrl = "getPdBaseSupplerByPdBaseId.do" ; editMtData = { "pdBaseId": id , "categoryName":chargeMtgetHtmlkind() } }
        supplerDeleID = [] ; // 删除供应商的数组清空
        $.ajax({
            url: editUrl ,
            data: editMtData ,
            success:function (data) {
                // 返回的数据包括 物料的基本信息 base ，
                // 返回的数据包括 商品的基本信息 pdBase ，
                // 物料所属分类的父级到子级的正序列表 mtKind_list，
                // 涉及到的分类列表(level 从1到* 的各级列表) continuityCategory  ，
                // 供应商列表 mtSupplierMaterialList，
                // 库房信息 mtStockInfo，
                // 质量信息 mtQuality  
                var mt_info = data["base"]; // 来源于物料的
                var pdBase = data["pdBase"]; // 来源于商品的
                var kind_list = data["continuityCategory"];
                var supply_list = data["mtSupplierMaterialList"];
                var ku_info = data["mtStockInfo"];
                var quality_info = data["mtQuality"];
                if ((mt_info == undefined && pdBase == undefined  ) ||
                    kind_list == undefined ||
                    // ku_info == undefined ||
                    quality_info == undefined) {
                    $("#mt_tip_ms").html("没有接收到所有的返回值");  bounce.show( $("#mtTip")) ;
                    return false ;
                }
                // 下面是赋默认值
                $("#mtInfo").hide();   $("#mtAdd").show();    $("#mtTTL").html("修改物料");
                $("#mtAdd input").val(""); //  set addMt container input null
                $(".addsupTip").html("");
                $(".mt_hasSelect").html(""); 
                /*mt_type*/
                $("#mt_type").val(editType);
                /* 供应商列表 */
                var str = "" , initStorage = 0 , minStorage = 0;
                if( supply_list.length > 0 ){
                    for( var s = 0 ; s < supply_list.length ; s++ ){
                        var supItem = supply_list[s] ;
                        if(supItem.perchaseCycle == 0){ supItem.perchaseCycle = "" ; }
                        var suptemp = {
                            "id": supItem.supplier_ , // 供应商id ====
                            // "sbId":  supItem.id , // 供应关系id====
                            "sbId": id , // 供应关系id====
                            "name":  supItem.name , // 供应名称
                            "fullName": supItem.fullName , // 供应全称
                            "codeName":  supItem.codeName , // 供应名称
                            "priceStable":  supItem.priceStable , // 价格是否稳定:1-相对稳定,2-变动频繁'
                            "draftAcceptable":  supItem.draftAcceptable ,
                            "hasContact":  supItem.hasContact ,
                            "contractSn":  supItem.contractSn ,
                            "validDate":  supItem.validDate && supItem.signDate.substr(0,10) ,
                            "signDate":  supItem.signDate && supItem.signDate.substr(0,10),
                            "invoicable":  supItem.invoicable ,
                            "invoiceCategory":  supItem.invoiceCategory ,
                            "atPar":  supItem.atPar ,
                            "unitPrice":  supItem.unitPrice ,
                            "taxRate":  supItem.taxRate ,
                            "perchaseCycle":  supItem.perchaseCycle ,
                            "minimumPurchase":  supItem.minimumPurchase ,
                            "minimumStock":  supItem.minimumStock ,
                            // "initialStock":  supItem.initialStock ,
                            "chargeAcceptable":  supItem.chargeAcceptable ,
                            "chargeBegin":  supItem.chargeBegin ,
                            "chargePeriod":  supItem.chargePeriod ,
                            "isInclude":  supItem.isInclude ,
                            "materialInvoicable":  supItem.materialInvoicable ,
                            "materialInvoiceCategory":  supItem.materialInvoiceCategory ,
                            "materialTaxRate":  supItem.materialTaxRate ,
                            "isImprest":  supItem.isImprest ,
                            "isParValue":  supItem.isParValue ,
                            "isTax":  supItem.isTax ,
                            "packageMethod":  supItem.packageMethod ,
                            "inclusiveFreight":  supItem.inclusiveFreight
                        };
                        minStorage += Number( supply_list[s]["minimumStock"]) ;
                        initStorage += Number(supply_list[s]["initialStock"]) ;
                        str += "<tr>" +
                            "<td>" + supply_list[s]["name"] + "</td>" +  // 供应商名
                            "<td>" + supply_list[s]["contractSn"] + "</td>" +  // 采购合同
                            "<td>"+ catoryFormat(supply_list[s]["invoiceCategory"]) + "</td>" ;   //  发票种类
                        if(supply_list[s]["priceStable"] == 1){
                            str += "<td>"+ supply_list[s]["unitPrice"] + "</td>" +  //  已约定单价
                                "<td>- -</td>" ;   //  参考单价
                        }else{
                            str += "<td>- -</td>" +  //  已约定单价
                                "<td>"+ supply_list[s]["unitPrice"] +"</td>" ;   //  参考单价
                        }
                        str += "<td>"+ giveInvoiceFormat(supply_list[s]["materialInvoicable"]) + "</td>"+   //  是否开票
                            "<td>"+ supply_list[s]["perchaseCycle"] + "</td>" +   /// 采购周期
                            "<td>"+ supply_list[s]["minimumStock"] + "</td>"+   //  最低库存
                            "<td>"+
                            '<span class="ty-color-blue" onclick="gScan($(this))">查看</span>' +
                            '<span class="ty-color-blue" onclick="editPurchaseBtn($(this))">编辑</span>' +
                            '<span class="ty-color-blue" onclick="delSupBtn($(this))">删除</span>' +
                            '<span class="hd">'+ JSON.stringify(suptemp) +'</span>' +
                            '</td>' +
                            "</tr>" ;
                    }
                }
                $("#suppierList").html(str);

                /* mt_info */
                // 需要先判断物料来源是否为商品录入 
                /*  normal material  */
                if( isFromGoods ){ // 来源于商品
                    $("#goods_minStorage").val(minStorage) ; $("#goods_oralStorage").val(initStorage) ;
                    if (htmlkind == "商品" || htmlkind == "半成品") {
                        // 只有商品/半成品 给最低库存、初始库存赋值
                        $("#goods_minStorage").val(pdBase["minimumiStock"]) ;
                    }
                    $("#editNormalMaterialMess").hide();
                    $("#editGoodsMaterialMess").show();
                    $("#goods_name").val(pdBase["name"]);
                    $("#goods_id").val(pdBase["id"]);
                    // $("#goods_type").val(pdBase["name"]);
                    // $("#goods_isControl").val(pdBase["name"]);
                    $("#goods_code").val(pdBase["innerSn"]);
                    $("#goods_unit").val(pdBase["unit"]);
                    $("#goods_net_weight").val(pdBase["netWeight"]);
                    $("#goods_specifications").val(pdBase["specifications"]);
                    $("#goods_model").val(pdBase["model"]);
                    $("#goods_memo").val(pdBase["memo"]);

                    // 外购&商品&半成品 设置质量信息不可修改
                    $("#qualityCon").find("input").each(function () {
                        $(this).attr("readonly", "true") ;
                    }) ;

                }else{ // 来源于物料
                    $("#editNormalMaterialMess").show();
                    $("#editGoodsMaterialMess").hide();
                    $("#mt_name").val(mt_info["name"]);
                    $("#mt_id").val(mt_info["id"]);
                    $("#mt_code").val(mt_info["code"]);
                    $("#mt_unit").val(mt_info["unit"]);
                    $("#mt_net_weight").val(mt_info["netWeight"]);
                    $("#mt_specifications").val(mt_info["specifications"]);
                    $("#mt_model").val(mt_info["model"]);
                    $("#mt_memo").val(mt_info["memo"]);
                    $("#mt_minStorage").val(minStorage) ; $("#mt_oralStorage").val(initStorage) ;
                    // 构成商品的原辅材料、商品的包装物 基本信息不能修改
                    var kind = $("#curID").children("span:eq(1)").children("span").html() ;
                    if (kind == "商品的包装物" || kind == "构成商品的原辅材料") {
                        $("#mt_name").attr("readonly", "true") ; $("#mt_code").attr("readonly", "true") ;
                        $("#mt_unit").attr("readonly", "true") ; $("#mt_net_weight").attr("readonly", "true") ;
                        $("#mt_specifications").attr("readonly", "true") ; $("#mt_model").attr("readonly", "true") ;
                    }else{
                        $("#mt_name").removeAttr("readonly") ; $("#mt_code").removeAttr("readonly")  ;
                        $("#mt_unit").removeAttr("readonly") ; $("#mt_net_weight").removeAttr("readonly")  ;
                        $("#mt_specifications").removeAttr("readonly")  ; $("#mt_model").removeAttr("readonly")  ;
                    }

                }
                /* mtKind_list */
                $("#mtKindContainer").html("");
                if( kind_list.length > 0 ){
                    for(var k = 0 ; k < kind_list.length; k++ ){
                        // setMtKind(kind_list[k] , mtKind_list ); /* 按照原来的方法编辑物料 */
                        setMtKind(kind_list[k] , "upateMtType");  
                    }
                }
                /*ku_info*/
                if (ku_info && ku_info.length > 0) {
                    $("#mt_stock_position").val(ku_info[0]["stockPosition"]);
                    $("#mt_stock_requirements").val(ku_info[0]["stockRequirements"]);
                    if (htmlkind == "商品" || htmlkind == "半成品") {
                        // 只有商品/半成品 给最低库存、初始库存赋值
                        $("#goods_oralStorage").val(ku_info[0]["stock"]) ;
                    }
                } else {
                    $("#mt_stock_position").val("");
                    $("#mt_stock_requirements").val("");
                }
                /* quality_info */
                if( quality_info.length > 0 ){
                    $("#mt_experimental_method").val(quality_info[0]["experimentalMethod"]);
                    $("#mt_expiration_date").val(quality_info[0]["expirationDate"]);
                    $("#mt_inspection_standard").val(quality_info[0]["inspectionStandard"]);
                    $("#mt_inspection_operation").val(quality_info[0]["inspectionOperation"]);
                    $("#mt_inspection_instructions").val(quality_info[0]["inspectionInstructions"]);
                    $("#mt_protocol_sn").val(quality_info[0]["protocolSn"]);
                    $("#mt_outer_record").val(quality_info[0]["outerRecord"]);
                    $("#mt_sealed_samples_sn").val(quality_info[0]["sealedSamplesSn"]);
                    $("#mt_qulity_memo").val(quality_info[0]["memo"]);
                }

                $("#mt_isControl").val("0"); 
            }
        })
    }
}
// updator :侯杏哲 2018-02-28 保存物料（新增或修改）
function saveMtEdit(){
    /* mt_info */
    var mt_id = $("#mt_id").val();
    var mt_name = $("#mt_name").val();
    var mt_code = $("#mt_code").val();
    var mt_unit = $("#mt_unit").val();
    var mt_net_weight = $("#mt_net_weight").val();
    var mt_specifications = $("#mt_specifications").val();
    var mt_model = $("#mt_model").val();
    var mt_memo = $("#mt_memo").val();
   /* gs_info*/
    var pd_id = $("#goods_id").val();
    /* mtKind_list */
    var lastSelectID = $("#mtKindContainer").children(":last").val();
    var mt_kindID = $(".mt_hasSelect").children(":last").children(".hd").html();
    var mt_kindID_pid = $(".mt_hasSelect").children(":first").children(".hd").html(); // 种类的父级科目
    var mt_kindname = $(".mt_hasSelect").children(":last").children(":eq(1)").html();
    
    if(lastSelectID != mt_kindID ){
        $("#mt_tip_ms").html("您所选分类下仍有子分类，请继续选择子分类！");
        bounce.show( $("#mtTip")) ;return false;
    }
    /* supply_list */
    var supplierList = [];
    var supCount = 0 ; 
    $("#suppierList").children("tr").each(function(){
        var supItem = $(this).children(":last").children(".hd").html();
        supItem = JSON.parse( supItem ) ;
        supplierList.push(supItem) ;
    }) ;

    /*ku_info*/
    var mt_stock_position = $("#mt_stock_position").val();
    // var mt_minimum_stock = $("#mt_minimum_stock").val();
    // var mt_stock = $("#mt_stock").val();
    var mt_stock_requirements = $("#mt_stock_requirements").val();
    /* quality_info */
    var mt_experimental_method = $("#mt_experimental_method").val();
    var mt_expiration_date = $("#mt_expiration_date").val();
    var mt_inspection_standard = $("#mt_inspection_standard").val();
    var mt_inspection_operation = $("#mt_inspection_operation").val();
    var mt_inspection_instructions = $("#mt_inspection_instructions").val();
    var mt_protocol_sn = $("#mt_protocol_sn").val();
    var mt_outer_record = $("#mt_outer_record").val();
    var mt_sealed_samples_sn = $("#mt_sealed_samples_sn").val();
    var mt_qulity_memo_2 = $("#mt_qulity_memo").val();

    /* 需要保存的数据 */
    var save_mtKind = mt_kindID ;
    var save_mt_info = {
        "id" : mt_id ,
        "name" : mt_name ,
        "code" : mt_code ,
        "unit" : mt_unit,
        "netWeight" : mt_net_weight ,
        "specifications" : mt_specifications ,
        "model" : mt_model ,
        "memo" : mt_memo
    } ;
    var save_ku_info = {
        "stockPosition" : mt_stock_position ,
        "stockRequirements" : mt_stock_requirements
    };
    var save_quality_info = {
        "experimentalMethod" : mt_experimental_method ,
        "expirationDate" : mt_expiration_date ,
        "inspectionStandard" : mt_inspection_standard ,
        "inspectionOperation" : mt_inspection_operation ,
        "inspectionInstructions" : mt_inspection_instructions ,
        "protocolSn" : mt_protocol_sn ,
        "outerRecord" : mt_outer_record ,
        "sealedSamplesSn" : mt_sealed_samples_sn ,
        "memo" : mt_qulity_memo_2
    };
    var data = {
        "pid" : mt_kindID_pid ,
        "mt_info" : JSON.stringify(save_mt_info)  ,
        "save_mtKind" :  save_mtKind ,
        "ku_info" : JSON.stringify(save_ku_info)  ,
        "quality_info" : JSON.stringify(save_quality_info)  ,
        "supplierList" : JSON.stringify(supplierList),
        "supplerDeleID":  JSON.stringify(supplerDeleID) ,
    };
    /*mt_type*/
    var mt_type = $("#mt_type").val(); // update_mt  or  add_mt
    var url = "addBase.do" ;
    if( mt_type == "update_gs" ){
        url = "updatePdBaseAndSuppler.do" ;
        var stock = $("#goods_oralStorage").val() ;
        data = {
            "pd_id":pd_id ,
            "stock":stock ,
            "firstCategory": mt_kindID_pid ,
            "save_mtKind" :  save_mtKind ,
            "ku_info" : JSON.stringify(save_ku_info)  ,
            "quality_info" : JSON.stringify(save_quality_info)  ,
            "supplierList" : JSON.stringify(supplier),
            "supplerDeleID":  JSON.stringify(supplerDeleID)
        }
    }else{
        if( mt_name == "" || mt_name == undefined ){
            layer.msg("物料名称为必填项，请补充完整！"); return false;
        }else if( mt_code == "" || mt_code == undefined ){
            layer.msg("物料代号为必填项，请补充完整！");  return false;
        }else if( mt_kindID == "" || mt_kindID == undefined ){
            layer.msg("请重新选择物料类型");  return false;
        }else if(mt_type == "update_mt" &&  mt_id == "" || mt_id == undefined ){
            $("#mt_tip_ms").html("修改参数错误，请稍后重试");
            bounce.show( $("#mtTip")) ;return false;
        }
        if(mt_type == "update_mt"){
            url = "updateBase.do" ;
        }
    }

    var control = $("#mt_isControl").val();  // 判断保存是不是被二次点击
    if(control != "0"){  return false ;  }else{  $("#mt_isControl").val("1");  }  
    $.ajax({
        url: url ,
        data:  data ,
        success:function (data) {
            var status = data["status"];
            var mtEidtInfo = data["mtBase"];
            window.scrollTo(0,0);
            $("#mtInfo").show();
            $("#mtAdd").hide();
            $("#addBtn").show();
            $("#strNav").html(" <span class=\"navTxt\"><i class=\"fa fa-home\"></i> 物料管理</span>\n" +
                "<span class=\"nav_\"> / </span>\n" +
                "<span class=\"navTxt\" onclick=\"goback()\"><a href=\"javascript:;\"><i class=\"\"></i>物料信息</a></span>") ;

            if(status == 1 ){ 
                var _id = $("#curID>span:last").children(".hd").html();
                var yecur = $("#ye").children(".yeCon").children(".yecur").html();
                getMaterialByKindid( _id , yecur , 10 ) ;

            }else if(status==2){
                $("#mt_tip_ms").html("代号已存在，请重试！");
                bounce.show( $("#mtTip")) ;
            }else if(status==3){
                $("#mt_tip_ms").html("商品已入库，初始库存不能修改，请重新设置其他项目");
                bounce.show( $("#mtTip")) ;
            }else if(status==0){
                var msg = data["message"];
                $("#mt_tip_ms").html(msg);
                bounce.show( $("#mtTip")) ;
            }else{
                $("#mt_tip_ms").html("商品已入库 或 供应商有重复，保存失败 ！");
                bounce.show( $("#mtTip")) ;
            }
        }
    })

}
//取消新增、编辑物料
function cancelMtEdit(){
    $("#mtInfo").show();
    $("#addBtn").show();
    $("#strNav").html(" <span class=\"navTxt\"><i class=\"fa fa-home\"></i> 物料管理</span>\n" +
        "<span class=\"nav_\"> / </span>\n" +
        "<span class=\"navTxt\" onclick=\"goback()\"><a href=\"javascript:;\"><i class=\"\"></i>物料信息</a></span>") ;
    $("#mtAdd").hide();
}
// 为物料种类赋值
function setMtKind( kindList , obj ){ // kindList : 某等级的分类列表   obj : 需要选中的分类列表
    if(kindList!= undefined && kindList.length > 0){
        var disable = 0 ; 
        var str_1 = "<select class='mt_kindList' onchange='selectMtKind($(this))'>"  ;
        var str_2 = ""; 
        if(obj != "upateMtType"){
            str_2 = "<option value='0' ></option>";
        }
        for( var j = 0; j < kindList.length ; j++ ){
            var id = kindList[j]["id"];
            var name = kindList[j]["name"];
            if(obj == "upateMtType" ){
                if(j == 0){
                    var selectList = "<span> <span class='hd'>"+ id +"</span> <span> > "+ name +" </span> </span>";
                    if( name == "外购成品" || name == "半成品" || name == "商品" || name == "构成商品的原辅材料" || name == "商品的包装物"  ){
                        disable = 1 ;
                        str_2 += " <option value='"+ id +"' >"+ name +"</option>";
                    }
                    $(".mt_hasSelect").append(selectList); // 初始化已选中 
                }
                if(name != "外购成品" && name != "半成品" &&name != "商品" && name != "构成商品的原辅材料" && name != "商品的包装物"  ){
                    str_2 += " <option value='"+ id +"' >"+ name +"</option>";
                }
                
            }else{
                if(name != "外购成品" && name != "半成品" && name != "商品" && name != "构成商品的原辅材料" && name != "商品的包装物"  ){
                    str_2 += " <option value='"+ id +"' >"+ name +"</option>";
                }
            }
        }
        if(disable == 1){
            str_1 = "<select class='mt_kindList' disabled='disabled' onchange='selectMtKind($(this))'>"  ;
        }
        var str = str_1 + str_2 + "</select>" ;
        $("#mtKindContainer").append(str);
    }
}
// 删除物料
function delMtBtn(obj){
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
    var id = obj.siblings(".hd").html();
    var name = obj.parent().siblings(":eq(0)").html(); 
    mtUpdateObj_Tr = obj.parent().parent();
    $("#mtConfirm").show().siblings().hide().parent().show();
    $("#mt_confirm_ms").html("您确定删除名为 "+ name +" 物料？");
    $("#mt_confirm_type").html("2");
    $("#mt_confirm_id").html(id);
}

// creator : 侯杏哲 2018-03-30 匹配供应商
function matchSupper(thisObj){
    var supplier = $("#supplier").val() ;
    if(supplier == ""){
        $(".supInfo").hide();
        $(".stable").hide();
        $("#e_gName1").val("")
        $("#e_gCode1").val("");
        return false;
    }
    supplier = JSON.parse(supplier) ;
    $("#e_gName1").val(supplier["name"])
    $("#e_gCode1").val(supplier["codeName"]);
    $(".supInfo").show();
    $(".stable").hide();
    $(".supInfo input").val("");
    $(".stable input").val("");
    $("#addPurchase .fa").each(function(){
        $(this).attr("class", "fa fa-circle-o");
        $(this).removeAttr("disabled");
        $(this).parent().removeAttr("disabled");
    })
    setSupInfo(supplier, $("#sup1"));
    chargeShowInvoice();
}
// creator : 侯杏哲 2018-03-30 供应商简称
function setGname2(thisObj){
    var fullname = thisObj.val() ;
    $("#e_gName2").val(fullname.substr(0,6));
}
// creator  : 侯杏哲 2018-03-06  新增采购信息
function addBtn_purchase(){
    bounce.show($("#addPurchase"));
    $("#addPurchase bounceHead span").html("新增采购信息");

    $(".stable").hide();$(".supInfo").hide();
    $("#e_pMtName").val($("#mt_name").val());
    $("#e_pMtCode").val( $("#mt_code").val());
    $("#e_pMtUnit").val($("#mt_unit").val());
    $(".purUnit").html($("#mt_unit").val());
    $("#editType").val("");
    $("#e_gName1").val("");
    $("#e_gCode1").val("");
    getSupList( $("#supplier"))
    $("#supplier").removeAttr("disabled");
}
// creator  : 侯杏哲 2018-03-06  确定新增采购信息
function purchaseOK() {
    var editType = $("#editType").val() ;
    var sup = $("#supplier").val() ;
    if(!sup){
        layer.msg("请先选择供应商！");
        return false ;
    }
    sup = JSON.parse(sup) ;
    var supplier = sup ;
    if(editType === "1") { // 编辑
        supplier = curSupplier
    }else {
        // 供应商唯一判断
        var fullName = supplier["fullName"], same = false;
        $("#suppierList").children("tr").each(function () {
            var itemSup = $(this).children(":last").children(".hd").html();
            itemSup = JSON.parse(itemSup) ;
            if(itemSup["fullName"] == fullName){
                layer.msg("已存在该供应商！");
                same = true ;
                return false
            }
        })
        if(same){
            return false;
        }
    }
    supplier["isInclude"] = $("#containThis").val(); // 合同是否包含本物料:1-包含,0—不包含
    if(supplier["hasContact"] === "1"){
        if(supplier["isInclude"] !== "1" && supplier["isInclude"] !== "0"){
            layer.msg("请选择采购合同是否包含本材料");
            return false;
        }
    }
    supplier["priceStable"] = $("#isStable").val();  //价格是否稳定:1-相对稳定,2-变动频繁
    if(supplier["priceStable"] !== "1" && supplier["priceStable"] !== "2"){
        layer.msg("请选择该供应商供应的本材料价格是否稳定");
        return false;
    }
    supplier["isParValue"] = $("#isParValue").val();  //是否为开票价格 1-是,0-否
    supplier["inclusiveFreight"] = $("#containYunFee").val();  //是否包含运费 是否包含运费:1-为送货上门价格,含所有运费/2-含长途运输费用,到本市后需我司自提/3-为离厂价格,不包含任何运费
    if(supplier["inclusiveFreight"] !== "1" && supplier["inclusiveFreight"] !== "2" && supplier["inclusiveFreight"] !== "3"){
        layer.msg("请选择该价格是否含运费");
        return false;
    }
    supplier["atPar"] = $("#price").val();  //已约定单价(参考价格)
    supplier["materialInvoicable"] = $("#canInvoice").val(); //本物料能否开发票  1可以 0不可以 2不确定
    supplier["materialInvoiceCategory"] = $("#incoiceType").val();  //本物料发票类型:1-增值税专票,2-普通发票 4-不给开票
    supplier["materialTaxRate"] =  "";  //本物料税率  普通票不传，开增值传前面设置的税率
    supplier["isTax"] =  $("#referPrice").val();  // 是否含税

    if(supplier["invoicable"] === "1"){
        if(supplier["materialInvoicable"] !== "1" && supplier["materialInvoicable"] !== "2" && supplier["materialInvoicable"] !== "0"){
            layer.msg("请选择购买本材料是否能开发票");
            return false;
        }
        if(supplier["materialInvoicable"] === "1"){
            if(supplier["materialInvoiceCategory"] !== "1" && supplier["materialInvoiceCategory"] !== "2" && supplier["materialInvoiceCategory"] !== "4"){
                layer.msg("请选择购买本材料给开何种发票");
                return false;
            }
            if(supplier["materialInvoiceCategory"] === "1"){
                supplier["materialTaxRate"] = supplier["taxRate"];
                if(supplier["isTax"] !== "1" && supplier["isTax"] !== "0" ){
                    layer.msg("请选择是否含税");
                    return false;
                }
            }
        }
    }
    supplier["unitPrice"] =  $("#price").val();
    if(supplier["unitPrice"] === ""){
        layer.msg("请输入单价");
        return false;
    }
    supplier["packageMethod"] =  $("#packgeType").val(); //包装方式:1-基本固定,2-型式不定
    if(supplier["packageMethod"] !== "1" && supplier["packageMethod"] !== "2" ){
        layer.msg("请选择所供应材料的包装方式");
        return false;
    }
    supplier["perchaseCycle"] =  $("#purTurn").val();  //采购周期
    if(supplier["perchaseCycle"] === ""){
        layer.msg("请输入采购周期");
        return false;
    }
    supplier["minimumPurchase"] =  $("#minPur").val();  //最低采购量
    if(supplier["perchaseCycle"] === ""){
        layer.msg("请输入最低采购量");
        return false;
    }
    supplier["minimumStock"] =  $("#minStorage").val(); //最低库存
    if(supplier["perchaseCycle"] === ""){
        layer.msg("请输入最低库存");
        return false;
    }
    if(editType === "1"){ // 编辑
        editPurchaseTr.children(":eq(0)").html(supplier["fullName"]);
        editPurchaseTr.children(":eq(1)").html(supplier["contractSn"]);
        editPurchaseTr.children(":eq(2)").html(catoryFormat(supplier["invoiceCategory"]));
        if(supplier["priceStable"] == 1){
            editPurchaseTr.children(":eq(3)").html(supplier["unitPrice"]);
            editPurchaseTr.children(":eq(4)").html("- -");
        }else{
            editPurchaseTr.children(":eq(4)").html(supplier["unitPrice"]);
            editPurchaseTr.children(":eq(3)").html("- -");
        }
        editPurchaseTr.children(":eq(5)").html(giveInvoiceFormat(supplier["materialInvoicable"]));
        editPurchaseTr.children(":eq(6)").html(supplier["perchaseCycle"]);
        editPurchaseTr.children(":eq(7)").html(supplier["minimumStock"]);
        var oldSupplier = JSON.parse(editPurchaseTr.children(":eq(8)").children(".hd").html());
        // supplier["sbId"] = oldSupplier["sbId"];
        supplier["id"] = oldSupplier["id"];
        editPurchaseTr.children(":eq(8)").children(".hd").html(JSON.stringify(supplier))

    }else{
        supplier["sbId"] = "";
        var str = "<tr>" +
            "<td>" + supplier["fullName"] + "</td>" +  // 供应商名
            "<td>" + supplier["contractSn"] + "</td>" +  // 采购合同
            "<td>"+ catoryFormat(supplier["invoiceCategory"]) + "</td>" ;   //  发票种类
        if(supplier["priceStable"] == 1){
            str += "<td>"+ supplier["unitPrice"] + "</td>" +  //  已约定单价
                "<td>- -</td>" ;   //  参考单价
        }else{
            str += "<td>- -</td>" +  //  已约定单价
                "<td>"+ supplier["unitPrice"] +"</td>" ;   //  参考单价
        }
        str += "<td>"+ giveInvoiceFormat(supplier["materialInvoicable"]) + "</td>"+   //  是否开票
            "<td>"+ supplier["perchaseCycle"] + "</td>" +   /// 采购周期
            "<td>"+ supplier["minimumStock"] + "</td>"+   //  最低库存
            "<td>"+
            '<span class="ty-color-blue" onclick="gScan($(this))">查看</span>' +
            '<span class="ty-color-blue" onclick="editPurchaseBtn($(this))">编辑</span>' +
            '<span class="ty-color-blue" onclick="delSupBtn($(this))">删除</span>' +
            '<span class="hd">'+ JSON.stringify(supplier) +'</span>' +
            '</td>' +
            "</tr>" ;
        $("#suppierList").append(str);
    }

    bounce.cancel();
}
// creator:hxz 2019-12-26 编辑采购信息
var editPurchaseTr = null;
var curSupplier = null ;
function editPurchaseBtn(thisObj) {
    editPurchaseTr = thisObj.parent().parent();
    var supplier = thisObj.siblings(".hd").html();
    supplier = JSON.parse(supplier);
    curSupplier = supplier
    bounce.show($("#addPurchase"));
    $("#addPurchase bounceHead span").html("修改采购信息");
    $(".stable").hide();$(".supInfo").hide();
    $("#e_pMtName").val($("#mt_name").val());
    $("#e_pMtCode").val( $("#mt_code").val());
    $("#e_pMtUnit").val($("#mt_unit").val());
    $(".purUnit").html($("#mt_unit").val());
    $(".supInfo input").val("");
    $(".stable input").val("");
    $("#addPurchase .fa").each(function(){
        $(this).attr("class", "fa fa-circle-o");
        $(this).removeAttr("disabled");
        $(this).parent().removeAttr("disabled");
    });

    $("#editType").val("1");
    setSupplier(superList, $("#supplier"), supplier);
    $("#supplier").attr("disabled", "disabled");
    $("#e_gName1").val(supplier["name"]);
    $("#e_gCode1").val(supplier["codeName"]);
    setSupInfo(supplier, $("#sup1"));


    if(String(supplier["materialInvoicable"]) === "1" || String(supplier["materialInvoicable"]) === "0"){
        canInvoice(supplier["materialInvoicable"]);
        if(String(supplier["materialInvoicable"]) === "1" ){
            incoiceType(supplier["materialInvoiceCategory"]);
        }
    }
    isStable(supplier["priceStable"]);
    containYunFee(supplier["inclusiveFreight"]);
    referPrice(supplier["isTax"]);
    packgeType(supplier["packageMethod"]);
    $("#price").val(supplier["unitPrice"]);
    $("#purTurn").val(supplier["perchaseCycle"]);
    $("#minPur").val(supplier["minimumPurchase"]);
    $("#minStorage").val(supplier["minimumStock"]);
    chargeShowInvoice();
    $(".supInfo").show();
    if(supplier["hasContact"] === "1"){
        if(supplier["isInclude"] === "1" || supplier["isInclude"] === "0"){
            containThis(supplier["isInclude"]);
        }
    }else{
        $(".containThis").hide();
    }

}
// creator:hxz 2019-12-26 总结采购信息
function setSupMtInfo(supplier, obj) {
    var str = "" ;
    if(supplier["isInclude"] === "1"){
        str = "本材料已包含于采购合同中。<br>"
    }else if(supplier["isInclude"] === "0"){
        str = "本材料不包含于采购合同中。<br>"
    }
    if(supplier["packageMethod"] === "1"){
        str += "<span class='supOrange'>该供应商供应的本材料包装方式基本固定，</span>"
    }else if(supplier["packageMethod"] === "2"){
        str += "<span class='supOrange'>该供应商供应的本材料包装方式型式不定，</span>"
    }else {
        str += "<span class='supOrange'>该供应商供应的本材料包装方式暂不清楚，</span>"
    }
    str += "<span class='supGreen'>最低采购量为"+ supplier["minimumPurchase"] +"，</span>";
    str += "<span class='supOrange'>采购周期为"+ supplier["perchaseCycle"] +"天</span><br/>";

    if(supplier["priceStable"] === "1"){
        str += "<span class='supOrange'>价格相对稳定，</span>";

    }else if(supplier["priceStable"] === "2"){
        str += "<span class='supOrange'>价格变动较频繁，</span>";
    }
    if(String(supplier["materialInvoicable"]) === "1"){
        if(supplier["materialInvoiceCategory"] === "1"){
            str += "<span class='supGreen'>给开税率为"+ supplier["materialTaxRate"] +"%的增值税专用发票，</span>";
        }else if(supplier["materialInvoiceCategory"] === "2"){
            str += "<span class='supGreen'>给开其他发票，</span>";
        }
    }else if(String(supplier["materialInvoicable"]) === "0"){
        str += "<span class='supGreen'>不给开发票，</span>";
    }
    if(supplier["isImprest"] === "1"){ // 0-不确定,1-需要,2-不需要
        str += "<span class='supOrange'>需要预付款</span>";
    }else if(supplier["isImprest"] === "2"){
        str += "<span class='supOrange'>无需预付款</span>";
    }else if(supplier["isImprest"] === "0"){
        str += "<span class='supOrange'>不确定是否需预付款</span>";
    }
    return str ;
}
// creator:hxz 2019-12-26 总结供应商信息
function setSupInfo(supplier, obj) {
    // 供应商信息赋值
    var str1 = "", str2 = "";
    if(supplier["hasContact"] == 1){ // 签订采购合同
        str1 = "采购合同已签订";
        if(supplier["contractSn"]){ str1 += "，合同编号"+ supplier["contractSn"].substr(0,10); }
        if(supplier["validDate"]){ str1 += "，有效期至"+ supplier["validDate"].substr(0,10); }
        if(supplier["signDate"]){ str1 += "，签署日期为"+ supplier["signDate"].substr(0,10) ; }
    }else{
        str1 = "与该供应商暂无采购合同 ";
        $(".containThis").hide(); $("#containThis").val("");
    }
    if(supplier["invoicable"] == 1){
        $(".incoiceType").show();
        if(supplier["invoiceCategory"] == 1){
            if(supplier["taxRate"]){
                str2 = "<span class='supGreen'>该供应商能开税率为"+ supplier["taxRate"] +"%的增值税专用发票， </span>"
            }else{
                str2 = "<span class='supGreen'>该供应商能开增值税专用发票， </span>"
            }
        }else{
            str2 = "<span class='supGreen'>该供应商仅能开普通发票， </span>"
        }
    }else{
        $(".incoiceType").hide();
        str2 = "<span class='supGreen'>该供应商不能开发票， </span>"
    }
    if(supplier["chargeAcceptable"] == 1){
        str2 += "<span class='supOrange'>可接受挂账，";
        if(supplier["chargePeriod"]){
            str2 += "账期"+ supplier["chargePeriod"] +"天，</span>";
        }
        if(supplier["chargeBegin"] == "1"){
            str2 += "自货物入库之日起计算，</span>";
        }else if(supplier["chargeBegin"] == "2"){
            str2 += "自发票入账之日起计算，</span>";
        }
    }else{
        str2 += "<span class='supOrange'>不接受挂账，</span>";
    }
    // 4.1  可接收承兑汇票
    // 4.2  不确定能接受承兑汇票
    if(supplier["draftAcceptable"]){
        str2 += "<span class='supBlue'>可接收承兑汇票</span>";
    }else {
        str2 += "<span class='supBlue'>不确定能接受承兑汇票</span>";
    }
    obj.html(str1 + "<br>" + str2);

}
//  updator : 侯杏哲 2018-03-06  新增供应商
function addBtn_supply(){
    // 初始化表单控件
    $("#supTip").html("");
    $("#addSupply input[type='text']").val("");
    $("#addSupply input[type='hidden']").val("");
    $("#addSupply .fa").each(function () {
        $(this).attr("class" , "fa fa-circle-o").removeAttr("disabled") ;
    });
    $("#e_gMtName").val($("#mt_name").val());
    $("#e_gMtCode").val( $("#mt_code").val());
    $("#e_gMtUnit").val($("#mt_unit").val());
    var hideHang = [4,6,"_6","_7","7_1",8];
    for(var e=0; e<hideHang.length;e++){
        $(".hang" + hideHang[e]).hide() ;
    }
    $("#e_gPerchaseCycle").val("");
    $("#e_gMinimumPurchase").val("0");
    $("#e_gMinimumStock").val("0");
    $("#e_gInitialStock").val("0");
    $(".isKai").show();
    // 赋值物料信息
    var mt_type = $("#mt_type").val();
    if(mt_type == "update_mt" || mt_type == "add_mt"){
        $("#e_gMtName").val($("#mt_name").val()).attr("disabled" , 'disabled') ;
        $("#e_gMtCode").val($("#mt_code").val()).attr("disabled" , 'disabled') ;
        $("#e_gMtUnit").val($("#mt_unit").val()).attr("disabled" , 'disabled') ;
    }else{
        $("#e_gMtName").val($("#goods_name").val()).attr("disabled" , 'disabled') ;
        $("#e_gMtCode").val($("#goods_code").val()).attr("disabled" , 'disabled') ;
        $("#e_gMtUnit").val($("#goods_unit").val()).attr("disabled" , 'disabled') ;
    }

    $("#sup_type").val("0");
    $("#supConTtl").html("新增供应商");
    bounce.show($("#addSupply"));

}

// creator ：侯杏哲 2018-02-25 供应商 - 查看
function gScan( _thisObj ){
    bounce.show( $("#gScanInfo") ) ;
    var supInfo = _thisObj.siblings(".hd").html();
    supInfo = JSON.parse(supInfo) ;

    $("#s_gMtName").val($("#mt_name").val());
    $("#s_gMtCode").val( $("#mt_code").val());
    $("#s_gMtUnit").val($("#mt_unit").val());
    $("#s_gName").val(supInfo["fullName"]);
    $("#s_gName2").val(supInfo["name"]);
    $("#s_gCode").val(supInfo["codeName"]);
    setSupInfo(supInfo, $("#sup2"));
    var priceInfo = chargePriceShow(supInfo);
    $("#priceTtl").html(priceInfo.ttl);
    $("#priceCon").html(priceInfo.info);
    $("#mtTip_s").html(setSupMtInfo(supInfo));
    $("#minStorageInfo").html("规定该供应商所供应该材料的最低库存为" + supInfo["minimumStock"] + " " + $("#mt_unit").val());
}
// crearor:hxz 2019-12-25 保存编辑的供应商
function saveNewSupply(){
    var name = $("#e_gName").val();   // string    供应商名称
    var name2 = $("#e_gName2").val();   // string    供应商名称
    var gCode = $("#e_gCode").val() ; // 供应商代号
    gCode = $.trim(gCode) ;   name2 = $.trim(name2) ;     name = $.trim(name) ;
    var hasContact = $("#haveContract").val() ;  //  boolean  '是否已与其签订采购合同;true-有,false-无',
    var contractSn = $("#e_gCompactNo").val() ;  // varchar(100)    '采购合同编号',
    var validDate = $("#e_gCompactExpire").val() ; //  date '有效期至',(yyyy-MM-dd)
    var signDate = $("#e_gCompactSignDay").val(); // date '签署日期',(yyyy-MM-dd)
    var invoicable = $("#haveInvoice").val()  ; // boolean  '该供应商是否能开发票::true-可开具,false-不能开',
    var invoiceCategory = $("#vatInvoice").val()  ; // boolean  '是否能开增值税专用发票1:: 普通发票2
    var taxRate = $("#e_gRate0").val() ; // 该合同开发票的税率
    taxRate = $.trim(taxRate) ;
    var chargeAcceptable = $("#setHangAccount").val() ; // 是否接受挂账
    var chargePeriod = $("#hangDays").val() ; // 账期
    var chargeBegin = $("#setStartDate").val() ; // 1 - 自入库之日起 0 - 自发票提交之日起
    var isImprest = $("#setAdvanceFee").val() ; //  是否需要预付款 :0-不确定,1-需要,2-不需要
    var draftAcceptable = $("#hui").val() ; // 汇票 1接受 0不接受 2不确定
    if(name === ""){ layer.msg("供应商名称不能为空！"); return false ;  }
    if(name2 === ""){ layer.msg("供应商简称不能为空！"); return false ;  }
    if(gCode === ""){ layer.msg("供应商代号不能为空！"); return false ;  }
    if(hasContact === ""){ layer.msg("请选择“是否已与其签订采购合同”项目！"); return false ;  }
    if(hasContact === "1"){
        if(validDate === ""){  layer.msg("合同有效期至不能为空！"); return false ;     }
        if(signDate === ""){  layer.msg("合同签署日期不能为空！"); return false ;     }
        if(contractSn === ""){  layer.msg("合同编号不能为空！"); return false ;     }
    }
    if(invoicable === ""){ layer.msg("请选择“该供应商是否能开发票”项目！"); return false ;  }
    if(invoicable === "1"){
        if(invoiceCategory === ""){ layer.msg("请选择“是否能开增值税专用发票”项目！"); return false ;  }
        if(invoiceCategory === "1"){
            if(taxRate === ""){ layer.msg("税率不能为空！"); return false ;  }
            if(draftAcceptable === ""){ layer.msg("请选择“是否可接受汇票”项目！"); return false ;  }
        }
    }
    if(chargeAcceptable === ""){ layer.msg("请选择“是否接受挂账”项目！"); return false ;  }
    if(chargeAcceptable === "1") {
        if (chargePeriod === "") {
            layer.msg("请录入已约定的账期！");
            return false;
        }
        if (chargeBegin === "") {
            layer.msg("请选择“请选择从何时开始计算账期”项目！");
            return false;
        }
    }
    if(isImprest === ""){ layer.msg("请选择“是否需要预付款”项目！"); return false ;  }

    var supItem = {
      "id": "" , // 供应名称
      "fullName": name , // 供应全称
      "name": name2 , // 供应名称
      "codeName": gCode , // 代号
      "hasContact": hasContact ,
      "contractSn": contractSn ,
      "validDate": validDate ,
      "signDate": signDate ,
      "invoicable": invoicable ,
      "invoiceCategory": invoiceCategory ,
      "draftAcceptable": draftAcceptable ,
      "taxRate": taxRate ,
      "chargeAcceptable": chargeAcceptable ,
      "chargePeriod": chargePeriod ,
      "chargeBegin": chargeBegin ,
      "isImprest": isImprest
    };
    superList.push(supItem);
    bounce.show($("#addPurchase"))
    $("#supplier").append("<option value='"+ JSON.stringify(supItem) +"' class='supItem'>"+ supItem["fullName"] +"</option>") ;
    // setSupplier(superList, $("#supplier"))
}
// creator : hxz 2019-12-23 供应商刷新
function setSupplier(list , obj, selectedSuplier){
    var name = "" ;
    if(selectedSuplier){
        name = selectedSuplier["fullName"]
    }
    var str = "<option value='' class='supItem'> </option>" ;
    if(list && list.length > 0){
        for(var i=0 ; i < list.length ; i++){
            var info = list[i];
            if(name === info["fullName"]){
                str += "<option selected value='"+ JSON.stringify(info) +"' class='supItem'>"+ info["fullName"] +"</option>" ;
            }else{
                str += "<option value='"+ JSON.stringify(info) +"' class='supItem'>"+ info["fullName"] +"</option>" ;
            }
        }
    }
    obj.html(str);
}
// creator : hxz 2018-08-27 格式化是否开票
function giveInvoiceFormat(giveInvoice) {
    if(giveInvoice == 1){
        return "是" ;
    }else {
        return "否" ;
    }
}
// 删除供应商
var deleteObj = null;
var supplerDeleID = [];
function delSupBtn(obj){
    $("#mt_confirm_ms").html("确定删除名称为 "+ obj.parent().siblings(":eq(0)").html() + " 的供应商？");
    $("#mt_confirm_type").html("1");
    deleteObj = obj ;
    bounce.show( $("#mtConfirm") ) ;
}
// 提示框的确定按钮
function okConfirm(){
    var id =  $("#mt_confirm_id").html();
    var type =  $("#mt_confirm_type").html();
    switch(type){
        case 1:
        case "1": // delete supplier
            bounce.cancel();
            var supperItem = deleteObj.siblings(".hd").html() ;
            supperItem = JSON.parse(supperItem) ;
            var supID = supperItem["id"] ;
            if(supID != undefined || supID != ""){
                supplerDeleID.push(supID);
            }
            deleteObj.parent().parent().remove();
            break;
        case 2:  
        case "2": // delete materail
            bounce.cancel();
            $.ajax({
                url: "deleteBaseById.do" ,
                data:{ id: id },
                success:function(data){ 
                    var status = data["status"];
                    if( status == 1 || status == "1" ){
                        mtUpdateObj_Tr.remove();
                        $("#mt_tip_ms").html("物料删除成功");
                        $("#mtTip").show().siblings().hide().parent().show();
                    }else{
                        $("#mt_tip_ms").html("物料删除失败，请查找原因，稍后重试！");
                        $("#mtTip").show().parent().show();
                    }
                }
            });
            break;
        default:
            break;
    }

}
// 编辑物料的选择分类
function selectMtKind( obj ){
    // 获取下一级分类
    var thisID = obj.val();
    obj.nextAll().remove();
    if(thisID != undefined){
        getMtCategoryListBypid(thisID);
    }

    var selectList = "";
    $(".mt_kindList").each(function () {
        var id = $(this).val();
        var selectObj = $(this);
        if( id != 0 ){
            selectObj.children().each(function(){
                var item_id = $(this).val();
                var item_name = $(this).html();
                if(item_id == id){
                    selectList += "<span> <span class='hd'>"+ id +"</span> <span> > "+ item_name +" </span> </span>";
                }
            })
        }
    })
    $(".mt_hasSelect").html(selectList);


}

// 点击选择分类
function showkindBtn(obj){
    isall = false ;
    var id = obj.children("div.hd").children(".kindId").html();
    if(id != undefined || id != null){
        getMaterialByKindid( id , 1 , 10 );
    }else{
        $("#mt_tip_ms").html("系统错误，请刷新请刷新重试！");
        $("#mtTip").show().parent().show();
    }
}
// 点击头部导航
function showkindNav( obj  , isfirst ){
    var id = obj.children(".hd").html() ;
    if(id != undefined  ){

        getMaterialByKindid( id , 1 , 10 , isfirst);
    }else{
        $("#mt_tip_ms").html("系统错误，请刷新请刷新重试！");
        $("#mtTip").show().parent().show();
    }
}
// 编辑分类
var editKindObj = null ;
function upBtnAccount(obj){
    var id = obj.parent().next().children(".kindId").html();
    var name = obj.parent().parent().children(":eq(0)").html();
    if(id == null || id == ""){
        $("#mt_tip_ms").html("获取基本信息失败，请稍后重试！");
        $("#mtTip").show().parent().show();
    }else{
        $("#kindEditID").val(id);
        $("#kindEditName").val(name);
        editKindObj = obj.parent().parent().parent();
        $("#updateAccount").show().parent().show();
    }
}
function saveKindEdit(){
    var id = $("#kindEditID").val();
    var name = $("#kindEditName").val();

    bounce.cancel();
    $.ajax({
        url:"updateCategory.do",
        data:{ "id": id , "name":name },
        success:function (data) {
            $("#updateAccount").hide().parent().hide();
            var status = data["status"];
            if( status == 1 ){
                var kindInfo = data["kindInfo"];
                editKindObj.children().children(":eq(0)").html(name);
            }else{
                $("#mt_tip_ms").html("编辑失败，请稍后重试！");
                $("#mtTip").show().parent().show();
            }
        }
    })
}
// 删除分类
function delBtnAccount(obj){

    var id = obj.parent().next().children(".kindId").html();
    var name = obj.parent().parent().children(":eq(0)").html();
    if(id == null || id == ""){
        $("#mt_tip_ms").html("获取基本信息失败，请稍后重试！");
        $("#mtTip").show().parent().show();
    }else{
        $("#deleteAccount").show().siblings().hide().parent().show();
        $("#delKindname").html(name);
        editKindObj = obj.parent().parent().parent();
    }
}
// 确定删除分类
function okDeleteKind(){
    bounce.cancel();
    var id = editKindObj.children().children("div.hd").children(".kindId").html();
    $.ajax({
        url:"deleteCategory.do",
        data:{ "id": id  },
        success:function (data) {
            var status = data["status"];
            if( status == 1 ){
                $("#mt_tip_ms").html("删除成功！");
                $("#mtTip").show().parent().show();
                editKindObj.remove();
            }else if( status == 2 ){
                $("#mt_tip_ms").html("当前分类下有子分类，不可以删除！");
                $("#mtTip").show().parent().show();
            }else if( status == 3 ){
                $("#mt_tip_ms").html("当前分类的子分类下有物料，不可以删除！");
                $("#mtTip").show().parent().show();
            }else if( status == 4 ){
                $("#mt_tip_ms").html("当前分类下有物料，不可以删除！");
                $("#mtTip").show().parent().show();
            }
        }
    })

}
// 新增分类
function addBtnAct(num){ // num : 0 同级 ； 1 子级
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
   if( curPid == "" ){
       num = 0; 
   }else{
       num = 1 ; 
   }
    var id = $("#curID>span:last").children(".hd").html();
    var kindname = $("#curID>span:last").children(":eq(0)").html();
    if( kindname == "待分类" ){ 
        $("#mt_tip_ms").html(" ' 待分类 ' 下不能新增分类");
        $("#mtTip").show().siblings().hide().parent().show();
        return false; 
    } 
    $("#addAccount").show().parent().show();
    $("#addkind_pid").html(id);
    $("#addkind_type").html(num);
    var ttl = "新增一级分类";
    if( num == 1 ){ ttl = "新增分类"; }
    $("#addkindTtl").html(ttl);
    $("#addkind_name").val("");
}
function addkind(){
    var pid =  $("#addkind_pid").html();
    var type =  $("#addkind_type").html(); // 0 新增同级 ； 1 新增子级 ； 2 修改分类
    var name = $("#addkind_name").val();
    // 判断名字是否重复
    var isRepeat = false;
    $("#kindsTree").children("li.faceul1").each(function () {
        var na = $(this).children("a").children("span:eq(0)").html();
        if (name == na) {
            isRepeat = true;
        }
    });
    if (isRepeat) {
        layer.msg("已存在重复的同级类别名称！");
        return false;
    }
    if(name.length > 50){
        layer.msg("类别名称太长了！");
        return false;
    }
    $.ajax({
        url:"addMtCategoryByPid.do",
        data:{ pid:pid , type:type , name:name  },
        success:function (data) {
            if(data.status== 0){
                $("#mt_tip_ms").html("新增失败！");  bounce.show( $("#mtTip")) ;
            } else if (data.status == 2) {
                $("#mt_tip_ms").html("已存在重复的同级类别名称！");
                bounce.show($("#mtTip"));
            }else{
                var str = "<li class='faceul1'><a>"+
                    "<span onclick='showkindBtn($(this).parent())'>"+ data["name"]  +"</span>"+
                    "<span>"+  data["amount"]   +"</span>"+
                    "<span>"+
                    "<span class='fa fa-edit edit mar' onclick='upBtnAccount($(this))'></span>"+
                    "<span class='fa fa-trash delet' onclick='delBtnAccount($(this))'></span>"+
                    "</span>"+
                    "<div class='hd'>"+
                    "<span class='kindId'>"+ data["id"]  +"</span>"+
                    "</div>"+
                    "</a> </li>";
                if(type == 1){
                    if(!isall){ $("#kindsTree").append(str);   }
                }else{ // 新增一级分类
                    if (isall){ // 当前显示一级分类
                        $("#kindsTree").append(str);
                    }
                }
                var conHeight = $(".bigContainer").height();
                conHeight++;
                $(".between").height(conHeight);
            }
        }
    })
    $("#addAccount").hide().parent().hide();
}
// updator :侯杏哲 2018-04-14  获取物料类型，信息 列表
var curPid = "" ;
function getMaterialByKindid( pid , curpage , per , isFirst){
    curPid = pid ; 
    var url = "getMtCategoryAndMtBaseOne.do";
    if(isFirst == undefined || isFirst == ""){
        url = "partMaterial.do" ;
        isall = false ;
    }
    if(pid == ""){  isall = true ;     }

    $.ajax({ 
        url: url ,
        data:{ pid:pid , pageNumber:curpage , quantum:per  },
        success:function (data) {
            var mtCategories = data["mtCategories"];
            var mtBases = data["mtBases"];
            var pdBases = data["pdBases"];
            var daozheMtCategory = data["daozheMtCategory"];
            var cur = data["cur"];
            var countall = data["countall"];
            
            $("#kindsTree").html("");
            $("#curID").html("");
            $("#materialList").html("");
            $("#goodsmaterialList").html("");
            $("#ye").html("");
            $(".between").height(0);
            if(pid == ""){ 
                if(daozheMtCategory.length ){
                    pid =daozheMtCategory[daozheMtCategory.length - 1]["id"] ;
                }
            }
            var jsonStr = JSON.stringify({ 'pid': pid  }) ;
            setPage( $("#ye") , cur , countall , "material" , jsonStr );
            // 展示分类列表
            if(mtCategories.length > 0){
                for(var i=0; i<mtCategories.length; i++){
                    var amount = mtCategories[i]["amount"] ;
                    var createDate = mtCategories[i]["createDate"] ;
                    var createName = mtCategories[i]["createName"] ;
                    var creator = mtCategories[i]["creator"] ;
                    var descriptiom = mtCategories[i]["descriptiom"] ;
                    var id = mtCategories[i]["id"] ;
                    var keywords = mtCategories[i]["keywords"] ;
                    var level = mtCategories[i]["level"] ;
                    var locked = mtCategories[i]["locked"] ;
                    var name = mtCategories[i]["name"] ;
                    var orders = mtCategories[i]["orders"] ;
                    var org_ = mtCategories[i]["org_"] ;
                    var parent_ = mtCategories[i]["parent_"] ;
                    var updateDate = mtCategories[i]["updateDate"] ;
                    var updateName = mtCategories[i]["updateName"] ;
                    var updator = mtCategories[i]["updator"] ;
                    var isInitKind = false ;
                    if(name == "构成商品的原辅材料" || name == "商品的包装物" || name == "办公用品" || name == "其他原辅材料"
                        || name == "外购成品" || name == "半成品" || name == "商品" || name == "待分类" ){
                        isInitKind = true ;
                    }
                    var str = "<li class='faceul1'><a>"+
                        "<span title='"+ name +"' onclick='showkindBtn($(this).parent())'>"+ name +"</span>"+
                        "<span>"+ amount +"</span>";
                    if( !isInitKind ){
                        str +=  "<span>"+
                            "<span class='fa fa-edit edit mar' onclick='upBtnAccount($(this))'></span>"+
                            "<span class='fa fa-trash delet' onclick='delBtnAccount($(this))'></span>"+
                            "</span>";
                    }
                    str +=   "<div class='hd'>" +
                        "<span class='kindId'>" + id +"</span>" +
                        "</div>" +
                        "</a> </li>" ;
                    $("#kindsTree").append(str) ; 
                }
            }
            if(daozheMtCategory && daozheMtCategory.length >= 0 ){
                var strson_1 = "<span onclick='showkindNav($(this) , 1)'>"+
                    "<span>全部</span> > "+
                    "<span class='hd'>"+ "" +"</span>"+
                    "</span>";
                $("#curID").append(strson_1);
                for(var i=0; i<daozheMtCategory.length; i++){
                    var id = daozheMtCategory[i]["id"] ;
                    var name = daozheMtCategory[i]["name"] ;
                    var strson = "<span onclick='showkindNav($(this))'>"+
                                        "<span>"+ name +"</span> > "+
                                        "<span class='hd'>"+ id +"</span>"+
                                    "</span>";
                    $("#curID").append(strson);
                }
            }

            // 判断是物料来源的还是商品来源的
            var secondTereName = daozheMtCategory[0]["name"];
            if(secondTereName == "半成品" || secondTereName == "商品" || secondTereName == "外购成品"){
                $("#goodsmaterialList").parent().show().siblings("table").hide();
                if(pdBases && pdBases.length >0){
                    for(var i=0; i<pdBases.length; i++){
                            // 来自商品
                            var str_base = "<tr>"+
                                    "<td class='tz1'>"+ pdBases[i]["name"] +"</td>"+  // 内部名称
                                    "<td>"+ pdBases[i]["innerSn"] +"</td>"+                // 内部图号
                                    "<td>"+ pdBases[i]["model"] +"</td>"+
                                    "<td>"+ pdBases[i]["specifications"] +"</td>"+
                                    "<td>"+ pdBases[i]["categoryName"]  +"</td>"+
                                    "<td>"+ "商品录入"  +"</td>"+
                                    "<td>"+ pdBases[i]["unit"] +"</td>"+
                                    "<td>"+ pdBases[i]["currentStock"] +"</td>"+
                                    "<td>"+ pdBases[i]["minimumiStock"]  +"</td>"+
                                    "<td>"+ pdBases[i]["stockPosition"]  +"</td>"+
                                    "<td class='bj'>"+
                                    "<span class='ty-color-blue' onclick='editMtBtn($(this))'>编辑</span>"+
                                    "<span class='ty-color-blue' onclick='inventoryMtBtn($(this))'>盘点</span>"+
                                    "<span class='ty-color-gray' >盘点记录</span>"+
                                    "<div class='hd'>"+ pdBases[i]["id"]+"</div>"+
                                    "</td>"+ 
                                "</tr>";
                            $("#goodsmaterialList").append(str_base);
                    }
                }
                if(secondTereName == "半成品" || secondTereName == "商品"  ){
                    $(".notCaiGou").hide();
                }else{
                    $(".notCaiGou").show();
                }
            }else{
                $("#materialList").parent().show().siblings("table").hide();
                if(mtBases && mtBases.length >0){
                    for(var i=0; i<mtBases.length; i++){
                        var category_name = mtBases[i]["categoryName"] ;
                        var code = mtBases[i]["code"] ;
                        var id = mtBases[i]["id"] ;
                        var ignore = mtBases[i]["ignore"] ;
                        var model = mtBases[i]["model"] ;
                        var name = mtBases[i]["name"] ;
                        var sourceInit = mtBases[i]["source"];
                        var source = chargeMtSource( sourceInit ) ;
                        var specifications = mtBases[i]["specifications"] ;
                        var unit = mtBases[i]["unit"] ;
                        var stockPosition = mtBases[i]["stockPosition"] ;
                        var currentStock = mtBases[i]["currentStock"] ;
                        var minimumStock = mtBases[i]["minimumStock"] ;

                        // 来自物料
                        var str_base = "<tr>" +
                            "<td class='tz1'>"+ name +"</td>" +
                            "<td>"+ code +"</td>" +
                            "<td>"+ model +"</td>" +
                            "<td>"+ specifications+"</td>" +
                            "<td>"+ category_name +"</td>" +
                            "<td>"+ source +"</td>" +
                            "<td>"+ unit +"</td>" +
                            "<td>"+ currentStock +"</td>" +
                            "<td>"+ minimumStock +"</td>" +
                            "<td>"+ stockPosition +"</td>" +
                            "<td class='bj'>" +
                            "<span class='ty-color-blue' onclick='editMtBtn($(this))'>编辑</span>";
                        if (secondTereName == "构成商品的原辅材料" || secondTereName == "商品的包装物") {
                            // 没有删除
                        } else {
                            str_base += "<span class='ty-color-red' onclick='delMtBtn($(this))'>删除</span>";
                        }
                        str_base += "<span class='ty-color-blue' onclick='inventoryMtBtn($(this))'>盘点</span>" +
                            "<span class='ty-color-gray' >盘点记录</span>" +
                            "<div class='hd'>" + id + "</div>" +
                            "</td>" +
                            "</tr>";
                        $("#materialList").append(str_base);
                       
                    }
                }
            }
           
            var conHeight = $(".bigContainer").height() ;
            conHeight++;
            $(".between").height(conHeight);
        }
    }) ;
    
}
//  返回上一级
function gobackLstLevel(){
    var  n = $("#curID").children().length ;
    var kindId = "";
    var m = n - 2 ; 
    if( m > 0 ){
        kindId = $("#curID").children(":eq("+ m +")").children(".hd").html();
        getMaterialByKindid( kindId , 1 , 10 );
    } else{
        getMaterialByKindid( "" , 1 , 10 , 1);
    }
}
function goback() {
   $("#mtInfo").show();
    $("#mtAdd").hide();
    $(".ty-header span:gt(2)").remove();
    $("#addBtn").show();
}
// 判断非空，用零补齐
function setValue( val ){
    if(val == undefined || val == "" || val == null || val == "  "){
        return 0 ;
    }
    return val ;
}
// 判断物料来源 source
function chargeMtSource(str){
    if(str == undefined || str == "null"){
        return "";
    }else{
        if(str == "1"){
            return "商品录入";
        }else if(str == "2"){
            return "物料录入";
        }else{
            return str; 
        }
    }
}
// creator ：侯杏哲 2018-02-25 编辑物料 - 查看最低库存、原始库存
function showStorageHis(typeNum) { // typeNum : 1- 最低库存 ； 2 - 原始库存
    var mtid = $("#mt_id").val() ;
    var isMt = $("#mt_type").val() ;
    if(isMt != "update_mt"){
        mtid = $("#goods_id").val() ;
    }
    $.ajax({
        "url": "../material/modifyTheRecord.do" ,
        "data": { "mtId" : mtid  } ,
        success: function (res) {
            var struts = res["struts"] ;
            if(struts == 1){
                var str = "" ;
                var initialList = res["initialList"] ;
                var minlist = res["minlist"] ;
                var list = null;
                var strHead = "" ;
                var StorageTtl = "初始库存 - 修改记录" ;
                list = initialList ;
                var htmlkind = $("#curID").children(":eq(1)").children(":eq(0)").html();
                if (htmlkind == "半成品") { // 属于商品半成品的 没有供应商，用tb2
                    strHead = "<td width=\"20%\">修改人</td>" +
                        "<td width=\"30%\">修改时间</td>" +
                        "<td width=\"25%\">修改前的初始库存</td>" +
                        "<td width=\"25%\">修改后的初始库存</td>" ;
                    if(list && list.length > 0){
                        for(var i = 0 ; i < list.length ; i++){
                            str += "<tr>" +
                                "<td width=\"20%\">"+ list[i]["createName"] +"</td>" +
                                "<td width=\"30%\">"+ list[i]["createDate"] +"</td>" +
                                "<td width=\"25%\">"+ (list[i]["initialStock"]||0 )+"</td>" +
                                "<td width=\"25%\">"+ list[i]["afterInitialStock"] +"</td></tr>" ;
                        }
                    }
                }else{
                    strHead = "<td width=\"20%\">修改人</td>" +
                        "<td width=\"20%\">修改时间</td>" +
                        "<td width=\"20%\">供应商名称</td>" +
                        "<td width=\"20%\">修改前的初始库存</td>" +
                        "<td width=\"20%\">修改后的初始库存</td>" ;
                    if(typeNum == 1){
                        list = minlist ;
                        StorageTtl = "最低库存 - 修改记录" ;
                        strHead = "<td width=\"20%\">修改人</td>" +
                            "<td width=\"20%\">修改时间</td>" +
                            "<td width=\"20%\">供应商名称</td>" +
                            "<td width=\"20%\">修改前的最低库存</td>" +
                            "<td width=\"20%\">修改后的最低库存</td>" ;
                    }
                    if(list && list.length > 0){
                        for(var i = 0 ; i < list.length ; i++){
                            str += "<tr>" +
                                "<td width='20%'>"+ list[i]["createName"] +"</td>" +
                                "<td width='20%'>"+ list[i]["createDate"] +"</td>" +
                                "<td width='20%'>"+ (list[i]["name"]||"") +"</td>" ;
                            if(typeNum == 1){
                                str += "<td width=\"20%\">"+ (list[i]["minimumStock"]||"") +"</td><td width=\"20%\">"+ list[i]["afterMinimumStock"] +"</td></tr>" ;
                            }else{
                                str += "<td width=\"20%\">"+ (list[i]["initialStock"] || "") +"</td><td width=\"20%\">"+ list[i]["afterInitialStock"] +"</td></tr>" ;
                            }
                        }
                    }
                    $("#tb2").hide(); $("#tb1").show() ;
                }
                $("#headStorage").html(strHead);
                $("#oralStorageHis").html( str ) ;
                $("#StorageHistory").html(StorageTtl);
                bounce.show( $("#storageHis") ) ;

            }else{
                bounce.show($("#mtTip")) ; $("#mt_tip_ms").html("获取数据失败") ;
            }



        }
    })
}
// creator ：侯杏哲 2018-02-12  第 3 行   是否有合同
function haveContract(type , thisObj){
    setRadioSelect("haveContract", [1,0], type, thisObj);

    if(type == 1){  // 有合同
       $(".hang4").show(500);
    }else if(type == 0){
        $(".hang4").hide(200);
        // 清空数据
        $("#e_gCompactNo").val("") ;
        $("#e_gCompactExpire").val("") ;
        $("#e_gCompactSignDay").val("") ;
    }
}
// creator ：侯杏哲 2018-02-12  第5行 能否开发票
function haveInvoice(type , thisObj){
    setRadioSelect("haveInvoice", [1,0], type, thisObj);
    if(type === 1){
        $(".hang6").show()
    }else{
        $(".hang6").hide()
    }
    chargeshow8();
    chargeHangType();
}
// creator ：侯杏哲 2018-02-12  第6行 能否开增值税专用发票
function vatInvoice(type , thisObj){
    setRadioSelect("vatInvoice", [1,2], type, thisObj);
    if(type == 1){
        $(".hang_6").show();
    }else{
        $(".hang8").hide();
        $(".hang_6").hide();
        $("#e_gRate0").val("");
    }
    chargeshow8();
}
function chargePriceShow(supInfo) {
    var supplier = supInfo ;

    // var ttl = "", info = supplier["isTax"] === "1"?"含税":"不含税";
    var ttl = "", info = "";
    var isStable = supplier["priceStable"];
    var canInvoice = String(supplier["materialInvoicable"]);
    var incoiceTypeVal = supplier["materialInvoiceCategory"];

    if(isStable === "1"){ //稳定
        if(canInvoice === "1"){ // 能开票
            ttl = "已约定的单价";
            info += "开票价";
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "已约定的单价";
            info += "不开票价";
        }
    }else if(isStable === "2"){ // 变动频繁
        if(canInvoice === "1"){ // 能开票
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                ttl = "参考单价";
                info += "开票价";
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                ttl = "参考单价";
                info += "开票价";
            }
        }else if(canInvoice === "0"){ // 不能开票
            ttl = "参考单价";
            info += "不开票价";

        }else if(canInvoice === "2"){ // 不确定
            ttl = "参考单价";
            info += "不开票价";
        }
    }
    var price = supplier["unitPrice"]
    if(supplier["isTax"] === "1"){ // 含税

    }else{ // 不含税，换算成含税
        if(incoiceTypeVal === "1") { // 能开增值税专用发票
            var rate = Number(supplier.taxRate) / 100
            price = (Number(supplier["unitPrice"])*(1+rate)).toFixed(2);
        }

    }
    info += price + "元";
    var infoall = ""
    switch (String(supplier["inclusiveFreight"])){
        case "1":
            infoall = "含运费的" + info ;
            break;
        case "2":
            infoall = "含运费的" + info + "，但材料到本市后需我司自提";
            break;
        case "3":
            infoall = "不含运费的" + info ;
            break;
        default:
            infoall = info ;
    }
    return { "ttl":ttl , "info":infoall }

}
function chargeShowInvoice() {
    var isStable = $("#isStable").val();
    var supplier = $("#supplier").val() ;
    if(!supplier){ // 直接编辑采购信息的情况
        supplier = curSupplier ;
    }else{
        supplier = JSON.parse(supplier) ;
    }
    var canInvoice = $("#canInvoice").val() ;
    var incoiceTypeVal = $("#incoiceType").val() ;
    if(supplier["invoicable"] === "1"){
        $(".canInvoice").show();
        if(supplier["invoiceCategory"] === "1"){ // 能开增值税专用发票

        }else{ // 只能开普通票
            incoiceTypeVal = "2";  $("#incoiceType").val(incoiceTypeVal);
            $("#incoiceType1").attr("disabled", "disabled");
            $("#incoiceType1Con").attr("disabled", "disabled");
            $("#incoiceType0").attr("class", "fa fa-dot-circle-o");

        }
    } else{
        canInvoice = "0"; $("#canInvoice").val(canInvoice)
        $(".canInvoice").hide(); $(".incoiceType").hide();
    }
    if(isStable === "1"){ //稳定
        $(".priceInfo").show();
        $(".incoiceType4").hide();
        $(".canInvoice2").hide();
        if(canInvoice === "1"){ // 能开票
            $(".incoiceType").show();
            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                $(".priceInfo .type3").html("已约定的单价"); $("#isParValue").val(1);
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                $("#isParValue").val(1);
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("已约定的<span class='supOrange'>开票</span>单价");
            }
        }else if(canInvoice === "0"){ // 不能开票
            $(".incoiceType").hide();
            $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
            $(".priceInfo .type1").html("已约定的<span class='supOrange'>不开票</span>单价");
            $("#isParValue").val(0);
        }else{ // 还没选能否开票
            $(".priceInfo").hide();
            $(".incoiceType").hide();$("#isParValue").val("");
        }
    }else if(isStable === "2"){ // 变动频繁
        $(".priceInfo").show();
        $(".canInvoice2").show();
        if(canInvoice === "1"){ // 能开票
            if(supplier["invoicable"] === "1"){
                $(".incoiceType").show();
                $(".incoiceType4").hide();
            }

            if(incoiceTypeVal === "1"){ // 能开增值税专用发票
                $(".priceInfo .type1").hide(); $(".priceInfo .type2").show();
                $(".type3").html("参考单价");
                $("#isParValue").val(0);
            }else if(incoiceTypeVal === "2"){ // 开普通发票
                $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
                $(".priceInfo .type1").html("参考单价(<span class='supOrange'>开票价</span>)");
                $("#isParValue").val(0);
            }
        }else if(canInvoice === "0"){ // 不能开票
            $(".incoiceType").hide();
            $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
            $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票</span>)");
            $("#isParValue").val(0);
        }else if(canInvoice === "2"){ // 不确定
            if(supplier["invoiceCategory"] === "2"){ // 供应商只能开普通票
                $(".priceInfo .type1").html("参考单价");
                $("#isParValue").val("");
            }else{
                $("#isParValue").val(0);
                $(".priceInfo .type1").html("参考单价(<span class='supOrange'>不开票价</span>)");
                $("#isParValue").val(0);
            }
            $(".incoiceType").hide();
            $(".priceInfo .type1").show(); $(".priceInfo .type2").hide();
        }else{ // 还没选能否开票
            $(".priceInfo").hide();
        }
    }else {// 初始化
        $(".stable").hide();
    }

}
function chargeshow8() {
    var haveInvoice = $("#haveInvoice").val();
    var invoiceCategory = $("#vatInvoice").val();
    if(haveInvoice === "1" && invoiceCategory === "1"){
        $(".hang8").show();
    }else{
        $(".hang8").hide();
    }
}
// creator: hxz 2019-12-25 判断挂账的项目
function chargeHangType(){
    // 考虑公司不能开发票，则从入库之日起，且不可编辑
    var haveInvoice = $("#haveInvoice").val() ;
    var setHangAccount = $("#setHangAccount").val() ;
    if(setHangAccount === "0" || setHangAccount === ""){
        $("#setStartDate").val("") ;
        return false
    }
    if(haveInvoice === "1"){ // 能开票
        $("#setStartDate1").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate2Con").removeAttr("disabled") ;
        $("#setStartDate").val("") ;
    }else if(haveInvoice === "0"){
        $("#setStartDate1").attr("class" ,"fa fa-dot-circle-o ccc").attr("disabled" , "disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ccc").attr("disabled" , "disabled") ;
        $("#setStartDate2Con").attr("disabled" , "disabled") ;
        $("#setStartDate").val(1) ;
    }else{
        $("#setStartDate1").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate2").attr("class" ,"fa fa-circle-o ").removeAttr("disabled") ;
        $("#setStartDate").val("") ;
    }
}
//  creator ：侯杏哲 2018-02-12  第13行 以上价格是否包含运费
function hui(type , thisObj) {
    setRadioSelect("hui", [1,2], type, thisObj);

}
//  creator ：侯杏哲 2018-02-12  第16行 是否需要预付款
function setAdvanceFee(type , thisObj) {
    setRadioSelect("setAdvanceFee", [1,0,2], type, thisObj);

}
//  creator ：侯杏哲 2018-02-12  第7行 是否接受挂账
function setHangAccount(type , thisObj) {
    setRadioSelect("setHangAccount", [1,0], type, thisObj);
    $("#hangDays").val("") ;
    // $("#setStartDate").val("") ;
    // $("#setStartDate1").attr("class" , "fa fa-circle-o") ;
    // $("#setStartDate0").attr("class" , "fa fa-circle-o").removeAttr("disabled") ;
    if(type == 1){
        $(".hang7_1").show();  $(".hang_7").show();
        // setStartDate(1, $("#setStartDate1Con"));

    }else{
        $(".hang7_1").hide(); $(".hang_7").hide();
    }
    // 设置挂账的选项
    chargeHangType();
}
//  creator ：侯杏哲 2018-02-12  第7行 自入库之日起 / 自发票提交之日起
function setStartDate(type , thisObj) {
    setRadioSelect("setStartDate", [1,2], type, thisObj);
}
//  creator ：侯杏哲 2019/12/4 采购合同中包含本材料吗
function containThis(type , thisObj) {
    setRadioSelect("containThis", [1,0], type, thisObj);

}

//  creator ：侯杏哲 2019/12/4 该供应商供应的本材料价格是否稳定
function isStable(type , thisObj) {
    setRadioSelect("isStable", [1,2], type, thisObj);
    chargeShowInvoice();
}

//  creator ：侯杏哲 2019/12/4 购买本材料给开何种发票
function incoiceType(type , thisObj) {
    setRadioSelect("incoiceType", [1,2,4], type, thisObj);
    chargeShowInvoice();
}
//  creator ：侯杏哲 2019/12/4 参考单价
function referPrice(type , thisObj) {
    setRadioSelect("referPrice", [1,0], type, thisObj);

}
//  creator ：侯杏哲 2019/12/4 所供应材料的包装方式
function packgeType(type , thisObj) {
    setRadioSelect("packgeType", [1,2], type, thisObj);

}

//  creator ：侯杏哲 2019/12/4 购买本材料是否能开发票
function canInvoice(type , thisObj) {
    setRadioSelect("canInvoice", [1,2,0], type, thisObj);
    chargeShowInvoice();
}
//  creator ：侯杏哲 2019/12/4 该价格是否含运费
function containYunFee(type , thisObj) {
    setRadioSelect("containYunFee", [1,2,3], type, thisObj);
}
// creator：侯杏哲 2019/12/24 操作单选按钮
function setRadioSelect(str, arr, selectVal, thisObj){
    if( thisObj && thisObj.find("i.fa").attr("disabled") =="disabled" ){
        return false ;
    }
    $("#"+str).val(selectVal) ;
    for(var i=0; i<arr.length; i++){
        $("#"+ str + arr[i]).attr("class" , "fa fa-circle-o")
    }
    $("#"+ str + selectVal).attr("class" , "fa fa-dot-circle-o")

}


// creator: hxz 2018-09-03 格式化发票种类
function catoryFormat( type ){
    if(type == 1){
        return "增值税专用发票" ;
    }else if(type == 2){
        return "其他发票" ;
    }else{
        return "" ;
    }
}
// creator : 侯杏哲 2018-02-28
laydate.render({elem: '#e_gCompactExpire',});
laydate.render({elem: '#e_gCompactSignDay',});



