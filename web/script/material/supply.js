/**
 * Created by Administrator on 2016/9/25.
 */

$(function(){
    getSupplyList();
});


// 调去供应商名录的接口
function getSupplyList(){
    $.ajax({
        url:"getAllMtSupplier.do",
        data:{ },
        type:"post",
        dataType:"json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function (data) {
            loading.close();
             var supplierList = data["mtSuppliers"] ;
            $("#supplyList").html("");
             if ( supplierList.length > 0 ){
                 for(var i = 0; i < supplierList.length; i++ ){
                     var id = supplierList[i]["id"];
                     var name = supplierList[i]["name"];
                     var contact = supplierList[i]["contact"];
                     var mobile = supplierList[i]["mobile"];
                     var telephone = supplierList[i]["telephone"];
                     var fax = supplierList[i]["fax"];
                     var email = supplierList[i]["email"];
                     var address = supplierList[i]["address"];
                     var payment_method = supplierList[i]["paymentMethod"];
                     if( payment_method == 1 ){ payment_method = "现金";  }
                     if( payment_method == 2 ){ payment_method = "转账";  }
                     if( payment_method == 3 ){ payment_method = "汇款";  }
                     var str = " <tr>" +
                         "<td>"+ name +"</td>" +
                         "<td>"+ contact +" </td>" +
                         "<td>"+ mobile +"</td>" +
                         "<td>"+ telephone +"</td>" +
                         "<td>"+ email +"</td>" +
                         "<td>"+ fax +"</td>" +
                         "<td>"+ address +"</td>" +
                         "<td>"+ payment_method +"</td>" +
                         "<td>" +
                            "<span class='ty-color-blue' onclick='scanBtnSup($(this))'>查看</span> " +
                            "<span class='ty-color-red' onclick='delBtnSup($(this))'>删除</span>" +
                            "<span class='hd'>"+ id +"</span>" + 
                         "</td>" +
                     "</tr> ";
                     $("#supplyList").append(str);

                 }
             }
        },
        error:function (msg) {
            loading.close();
            console.log(msg);
            alert("连接错误，请刷新重试");
        } ,
        complete:function(){  chargeClose(1) ;  }
    })
}

// 查看供应商
var scanSupTr = null ;
function scanBtnSup(obj){
    scanSupTr = obj.parent().parent();
    var id = obj.siblings(".hd").html();
    if( id == undefined || id == ""){
        $("#mt_tip_ms").html("链接错误，请刷新重试！");
        $("#mtTip").show().parent().show() ;
        bounce.show($("#mtTip"));
    }else {
        $.ajax({
            url:"checkMtSupplierContact.do",
            data:{ "supplierId" : id  },
            type:"post",
            dataType:"json",
            beforeSend:function(){ loading.open() ;  } ,
            success:function (data) {
                loading.close();
                var supInfo = data["mtSuppliers"];
                var contactList = data["mtSupplierContacts"];
                if( supInfo == undefined ){
                    $("#mt_tip_ms").html("未获取有效数据！");
                    $("#mtTip").show().parent().show() ;
                    bounce.show($("#mtTip"));
                }else{
                    $(".ty-header p").append('<span class="nav_"> / </span>'+
                        '<span class="navTxt">'+supInfo["name"]+'信息详情</span>');
                    $("#scanInfo").show();
                    $("#supplierList").hide();
                    $(".editSup_1").hide();
                    $(".editSup_0").show();
                    var supplyMsg = supInfo ;
                    
                    $("#supId_input").val(id);
                    $("#supCompant_input").val( supplyMsg["name"] );
                    $("#supCompant_span").html( supplyMsg["name"] );
                    $("#supContact_input").val( supplyMsg["contact"] );
                    $("#supContact_span").html( supplyMsg["contact"] );
                    $("#supMobile_span").html( supplyMsg["mobile"] );
                    $("#supMobile_input").val( supplyMsg["mobile"] );
                    $("#supTel_span").html( supplyMsg["telephone"] );
                    $("#supTel_input").val( supplyMsg["telephone"] );
                    $("#supMail_span").html( supplyMsg["email"] );
                    $("#supMail_input").val( supplyMsg["email"] );
                    $("#supFax_span").html( supplyMsg["fax"] );
                    $("#supFax_input").val( supplyMsg["fax"] );
                    $("#supAddr_span").html( supplyMsg["address"] );
                    $("#supAddr_input").val( supplyMsg["address"] );
                    var payment_type = supplyMsg["paymentType"] ;
                    $("#supPayType_input").val(payment_type);
                    if( payment_type == 1 ){ payment_type = "预付";  }
                    if( payment_type == 2 ){ payment_type = "货到付款";  }
                    $("#supPayType_span").html(payment_type);
                    var payment_method = supplyMsg["paymentMethod"] ;
                    $("#supPayMethod_input").val(payment_method);
                    if( payment_method == 1 ){ payment_method = "现金";  }
                    if( payment_method == 2 ){ payment_method = "转账";  }
                    if( payment_method == 3 ){ payment_method = "汇款";  }
                    $("#supPayMethod_span").html(payment_method );
                    $("#supBank_span").html( supplyMsg["bankName"] );
                    $("#supBank_input").val( supplyMsg["bankName"] );
                    $("#supBankNo_span").html( supplyMsg["bankNo"] );
                    $("#supBankNo_input").val( supplyMsg["bankNo"] );

                    $("#supContact").html("");
                    if(contactList.length > 0){
                        for(var i=0; i<contactList.length; i++ ){
                            var str = " <tr>" +
                                "<td>" + contactList[i]["name"] + "</td>" +
                                "<td>" + contactList[i]["departName"] + "</td>" +
                                "<td>" + contactList[i]["postName"] + "</td>" +
                                "<td> " + contactList[i]["mobile"] + "</td>" +
                                "<td>" + contactList[i]["email"] + "</td>" +
                                "<td>" + contactList[i]["fax"] + "</td>" +
                                "<td>" + contactList[i]["memo"] + "</td>" +
                                "<td>" +
                                    "<span class='ty-color-blue' onclick='upBtnContact($(this))'>编辑</span>" +
                                    "<span class='ty-color-red' onclick='delBtnContact($(this))'>删除</span>" +
                                    "<span class='hd'>" + contactList[i]["id"]+ "</span>" +
                                "</td>" +
                            "</tr>";
                            $("#supContact").append(str);
                        }
                    }else{
                        var str = "<tr><td colspan='8'>暂无联系人</td></tr>";
                        $("#supContact").append(str);

                    }
                }
            },
            error:function (msg) {
                loading.close();
                console.log(msg);
                alert("连接错误，请刷新重试");
            } ,
            complete:function(){ loading.close() ;  }
        })
    }
    
}

// 新增联系人
function addBtnContact(){
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
    $("#addContact").show().parent().show();
    $("#addContact input").val("");
    $("#contactTtl").html("新增联系人");
    $("#editcontact_type").val("add");
    $("#msTip").html("");
}

// 修改联系人
var editContactTr = null ;
function upBtnContact(obj ){
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
    $("#msTip").html("");
    editContactTr = obj.parent().parent();
    $("#addContact").show().parent().show();
    bounce.show($("#addContact"));
    $("#addContact input").val("");
    $("#contactTtl").html("修改联系人");
    $("#editcontact_type").val("update");
    $("#editcontact_id").val(obj.siblings(".hd").html());
    $("#editcontact_name").val(editContactTr.children(":eq(0)").html());
    $("#editcontact_mobile").val(editContactTr.children(":eq(3)").html());
    $("#editcontact_depart").val(editContactTr.children(":eq(1)").html());
    $("#editcontact_zhi").val(editContactTr.children(":eq(2)").html());
    $("#editcontact_email").val(editContactTr.children(":eq(4)").html());
    $("#editcontact_fax").val(editContactTr.children(":eq(5)").html());
    $("#editcontact_memo").val(editContactTr.children(":eq(6)").html());
}

// save contact
function saveContact() {
    var sup_id = $("#supId_input").val();
    console.log("sup_id = " + sup_id);
    var editcontact_type = $("#editcontact_type").val();
    var editcontact_name = $("#editcontact_name").val();//联系人
    var editcontact_mobile = $("#editcontact_mobile").val();//电话
    var editcontact_depart = $("#editcontact_depart").val();//部门
    var editcontact_zhi = $("#editcontact_zhi").val();//职位
    var editcontact_email = $("#editcontact_email").val();//邮箱
    var editcontact_fax = $("#editcontact_fax").val();//传真
    var editcontact_memo = $("#editcontact_memo").val();//备注

    if( $.trim(editcontact_mobile) == ""){  $("#msTip").html("手机号码不能为空！");    return false;    }
    if( $.trim(editcontact_name) == ""){   $("#msTip").html("联系人不能为空！");   return false;  }

    var url = "";
    var data = null ;
    var isPost = 0 ;
    if(editcontact_type == "add"){
        if(sup_id != undefined && sup_id != ""){ isPost = 1 ; }
        console.log( sup_id != undefined && sup_id != "" );
        url = "addSupplierContact.do"
        data = {
            "msid" : sup_id ,
            "name" : editcontact_name ,
            "mobile" : editcontact_mobile ,
            "email" : editcontact_email ,
            "fax" : editcontact_fax ,
            "memo" : editcontact_memo ,
            "departName" : editcontact_depart ,
            "postName" : editcontact_zhi ,
        };
        if(isPost == 1 ){
            $("#addContact").hide().parent().hide();

            $.ajax({
                url:url ,
                data: data ,
                type:"post",
                dataType:"json",
                beforeSend:function(){ loading.open() ;  } ,
                success:function (data) {
                    var status = data["status"];
                    if(status == 1){
                        var mtSupplierContact = data["mtSupplierContact"];
                        if( mtSupplierContact != undefined ){
                                var str = " <tr>" +
                                    "<td>" + mtSupplierContact["name"] + "</td>" +
                                    "<td>" + mtSupplierContact["departName"] + "</td>" +
                                    "<td>" + mtSupplierContact["postName"] + "</td>" +
                                    "<td>" + mtSupplierContact["mobile"] + "</td>" +
                                    "<td>" + mtSupplierContact["email"] + "</td>" +
                                    "<td>" + mtSupplierContact["fax"] + "</td>" +
                                    "<td>" + mtSupplierContact["memo"] + "</td>" +
                                    "<td>" +
                                    "<span class='ty-color-blue' onclick='upBtnContact($(this))'>编辑</span>" +
                                    "<span class='ty-color-red' onclick='delBtnContact($(this))'>删除</span>" +
                                    "<span class='hd'>" + mtSupplierContact["id"] + "</span>" +
                                    "</td>" +
                                    "</tr>" ;
                                $("#supContact").append(str);

                            var str= null;
                           if($("#supContact").children(":eq(0)").children("td").attr('colspan')==8){
                               $("#supContact").children(":eq(0)").children("td").remove();
                           } ;

                        }
                        $("#mt_tip_ms").html("恭喜，编辑成功");
                        $("#mtTip").show().parent().show();
                        bounce.show($("#mtTip"));
                    }else{
                        $("#mt_tip_ms").html("链接错误，请刷新重试！");
                        $("#mtTip").show().parent().show() ;
                        bounce.show($("#mtTip"));
                    }
                    loading.close();

                },
                error:function (msg) {
                    loading.close();
                    console.log(msg);
                    alert("连接错误，请刷新重试");
                } ,
                complete:function(){ loading.close() ;  }
            })
        }
 

    }else if(editcontact_type == "update"){
        var contact_id = $("#editcontact_id").val();
        if((sup_id != undefined || sup_id != "") && ( contact_id != undefined || contact_id != "" )){ isPost = 1 ; }
        url = "updateSupplierContact.do" ;
        data = {
            "msid" : sup_id ,
            "id" : contact_id ,
            "name" : editcontact_name ,
            "mobile" : editcontact_mobile ,
            "email" : editcontact_email ,
            "fax" : editcontact_fax ,
            "memo" : editcontact_memo ,
            "departName" : editcontact_depart ,
            "postName" : editcontact_zhi
        };

        if(isPost == 1 ){
            $("#addContact").hide().parent().hide();
            $.ajax({
                url:url ,
                data: data ,
                type:"post",
                dataType:"json",
                beforeSend:function(){ loading.open() ;  } ,
                success:function (data) {
                    var status = data["status"];
                    if(status == 1){
                        var mtSupplierContact = data["mtSupplierContact"];
                        if( mtSupplierContact != undefined ){
                              
                                editContactTr.children(":eq(0)").html(mtSupplierContact["name"]);
                                editContactTr.children(":eq(3)").html(mtSupplierContact["mobile"]);
                                editContactTr.children(":eq(1)").html(mtSupplierContact["departName"]);
                                editContactTr.children(":eq(2)").html(mtSupplierContact["postName"]);
                                editContactTr.children(":eq(4)").html(mtSupplierContact["email"]);
                                editContactTr.children(":eq(5)").html(mtSupplierContact["fax"]);
                                editContactTr.children(":eq(6)").html(mtSupplierContact["memo"]);

                        }
                    }else{
                        $("#mt_tip_ms").html("链接错误，请刷新重试！");
                        $("#mtTip").show().parent().show() ;
                    }
                },
                error:function (msg) {
                    console.log(msg);
                    alert("连接错误，请刷新重试");
                } ,
                complete:function(){ chargeClose(1) ;  }
            })
        }
    }else{
        $("#mt_tip_ms").html("刷新重试！");
        bounce.show( $("#mtTip") );  

    }
}

// cancel contact edit 
function delBtnContact( obj ){
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
    editContactTr = obj.parent().parent();
    var id = obj.siblings(".hd").html();
    var name = editContactTr.children(":eq(0)").html();
    $("#mtConfirm").show().parent().show();
    bounce.show($("#mtConfirm"));
    $("#mt_confirm_ms").html("确定删除联系人：" + name);
    $("#mt_confirm_type").html("delete_contact");
    $("#mt_confirm_id").html(id);
}

function okConfirm(){
    var type =  $("#mt_confirm_type").html();
    var id =  $("#mt_confirm_id").html();
    var supid =  $("#supId_input").val();
    switch (type){  
        case "delete_contact" :
            $("#mtConfirm").hide().parent().hide();
            if(id != undefined && id != "" && supid != undefined && supid != "" ){
                $.ajax({
                    url:"deleteSupplierContact.do",
                    data:{ "mtSupplierId": supid , "id":id  },
                    type:"post",
                    dataType:"json",
                    beforeSend:function(){ loading.open() ;  } ,
                    success:function (data) {
                        var status = data["status"];
                        if(status == 1){
                            editContactTr.remove();
                        }else{
                            $("#mt_tip_ms").html("删除失败，请稍后重试！");
                            $("#mtTip").show().parent().show() ;
                        }
                    },
                    error:function (msg) {
                        console.log(msg);
                        alert("连接错误，请刷新重试");
                    } ,
                    complete:function(){ loading.close() ;  }
                });
            }
            break;
        case "delete_supplier" :
            $("#mtConfirm").hide().parent().hide();
            $.ajax({
                url: "deleteSupplier.do",
                data: {"id": id},
                type: "post",
                dataType: "json",
                beforeSend:function(){ loading.open() ;  } ,
                success: function (data) {
                    // status 状态 1-删除成功，2-该供应商还有供货关系 不可删除， 0-删除失败（此原因为传入id为空）
                    var status = data["status"];
                    if( status == 2 ){
                        $("#mt_tip_ms").html("该供应商还有供货关系 不可删除！");
                        $("#mtTip").show().parent().show() ;
                    }else if( status == 0 ){
                        $("#mt_tip_ms").html("删除失败，稍后重试");
                        $("#mtTip").show().parent().show() ;
                    }else if( status == 1 ){
                        $("#mt_tip_ms").html("删除成功");
                        $("#mtTip").show().parent().show() ;
                        deleSupTrObj.remove();
                    }

                },
                error: function (msg) {
                    console.log(msg);
                    alert("连接错误，请刷新重试");
                } ,
                complete:function(){ loading.close() ;  }
            })
            break;
        default:
    }
}
// 删除供应商
var deleSupTrObj = null ;
function delBtnSup( obj ){
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
    var id = obj.siblings(".hd").html();
    if( id == undefined || id == ""){
        $("#mt_tip_ms").html("获取基本信息失败，请刷新重试！");
        $("#mtTip").show().parent().show() ;
        bounce.show( $("#mtTip") )
    }else{
        deleSupTrObj = obj.parent().parent();
        var name = obj.parent().siblings(":eq(0)").html();
        $("#mtConfirm").show().siblings().hide().parent().show();
        bounce.show($("#mtConfirm"));
        $("#mt_confirm_ms").html("确定删除名为 " + name + " 的供应商？");
        $("#mt_confirm_type").html("delete_supplier");
        $("#mt_confirm_id").html(id);
    }


}
// 弹框的取消
$(".bounce_close").click(function(){
    $(".bounce").hide().children().hide();
})
function bounce_cancel(){
    $(".bounce").hide().children().hide();
}

// 返回供应商页
function gobacksupList(){
    $(".ty-header p span:gt(2)").remove();
    $("#scanInfo").hide();
    $("#supplierList").show();
}

//  edit supply massage
function editBtn_supInfo(){
    if (chargeRole("超管")) { // 0 表示超管
        bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
    }
    $(".editSup_1").show();
    $(".editSup_0").hide();
}
// save supplierInfo
function saveBtn_supInfo(){
    var id = $("#supId_input").val();
    var contact = $("#supContact_input").val();
    var mobile = $("#supMobile_input").val();
    var tel = $("#supTel_input").val();
    var mail = $("#supMail_input").val();
    var fax = $("#supFax_input").val();
    var addr = $("#supAddr_input").val();
    var payMethod = $("#supPayMethod_input").val();
    var bankName = $("#supBank_input").val();
    var bankNo = $("#supBankNo_input").val();
    var payType = $("#supPayType_input").val();
    if(id == undefined || id == ""){
        alert(" 获取修改项错误，请刷新重试！ ");
    }else{
        $.ajax({
            url:"updateSupplierDirectory.do",
            data:{
                id: id ,
                contact:contact ,
                mobile: mobile ,
                telephone: tel ,
                email: mail ,
                fax : fax ,
                address : addr ,
                paymentMethod: payMethod ,
                bankName: bankName ,
                bankNo: bankNo ,
                paymentType: payType ,
            },
            type:"post",
            dataType:"json",
            beforeSend:function(){ loading.open() ;  } ,
            success:function (data) {
                var status = data["status"];
                var supplyMsg = data["mtSupplier"];
                if( status == 0){
                    alert("修改失败，请重试！");
                }else if( status == 1){
                    $("#supCompant_input").val( supplyMsg["name"] );
                    $("#supCompant_span").html( supplyMsg["name"] );
                    $("#supContact_input").val( supplyMsg["contact"] );
                    $("#supContact_span").html( supplyMsg["contact"] );
                    $("#supMobile_span").html( supplyMsg["mobile"] );
                    $("#supMobile_input").val( supplyMsg["mobile"] );



                    $("#supTel_span").html( supplyMsg["telephone"] );
                    $("#supTel_input").val( supplyMsg["telephone"] );
                    $("#supMail_span").html( supplyMsg["email"] );
                    $("#supMail_input").val( supplyMsg["email"] );
                    $("#supFax_span").html( supplyMsg["fax"] );
                    $("#supFax_input").val( supplyMsg["fax"] );
                    $("#supAddr_span").html( supplyMsg["address"] );
                    $("#supAddr_input").val( supplyMsg["address"] );
                    var payment_type = supplyMsg["paymentType"] ;
                    $("#supPayType_input").val(payment_type);
                    if( payment_type == 1 ){ payment_type = "预付";  }
                    if( payment_type == 2 ){ payment_type = "货到付款";  }
                    $("#supPayType_span").html(payment_type);
                    var payment_method = supplyMsg["paymentMethod"] ;
                    $("#supPayMethod_input").val(payment_method);
                    if( payment_method == 1 ){ payment_method = "现金";  }
                    if( payment_method == 2 ){ payment_method = "转账";  }
                    if( payment_method == 3 ){ payment_method = "汇款";  }
                    $("#supPayMethod_span").html(payment_method );
                    $("#supBank_span").html( supplyMsg["bankName"] );
                    $("#supBank_input").val( supplyMsg["bankName"] );
                    $("#supBankNo_span").html( supplyMsg["bankNo"] );
                    $("#supBankNo_input").val( supplyMsg["bankNo"] );

                    $(".editSup_1").hide();
                    $(".editSup_0").show();

                }else{
                    alert("返回数据不合法");
                }

            },
            error:function (msg) {
                console.log(msg);
                alert("连接错误，请刷新重试");
            } ,
            complete:function(){ loading.close() ;  }
        })
    }

}

// cacel update suppliers
function cancelBtn_supInfo(){
    $("#supCompant_input").val($("#supCompant_span").html());
    $("#supContact_input").val($("#supContact_span").html());
    $("#supMobile_input").val($("#supMobile_span").html());
    $("#supTel_input").val($("#supTel_span").html());
    $("#supMail_input").val($("#supMail_span").html());
    $("#supFax_input").val($("#supFax_span").html());
    $("#supAddr_input").val($("#supAddr_span").html());
    var payment_type = $("#supPayType_span").html() ;
    if( payment_type == "预付" ){ payment_type =1 ;  }
    if( payment_type == "货到付款" ){ payment_type = 2 ;  }
    $("#supPayType_input").val(payment_type);
    var payment_method = $("#supPayMethod_span").html();
    if( payment_method == "现金" ){ payment_method = 1;  }
    if( payment_method == "转账" ){ payment_method = 2;  }
    if( payment_method == "汇款" ){ payment_method = 3;  }
    $("#supPayMethod_input").val(payment_method);
    $("#supBank_input").val($("#supBank_span").html());
    $("#supBankNo_input").val($("#supBankNo_span").html());
    $(".editSup_1").hide();
    $(".editSup_0").show();
}

// loading 标识的开关
var loading = {
    open : function(){
        $(".zhe_AjaxPic").show();
    },
    close : function(){
        $(".zhe_AjaxPic").hide();
    }
}

