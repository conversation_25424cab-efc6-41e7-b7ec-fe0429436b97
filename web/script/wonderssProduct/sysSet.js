var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#seeSetMenu"));
bounce_Fixed3.show($("#seeModule"));
bounce_Fixed2.cancel();
bounce_Fixed3.cancel();
$(function () {

    jumpPage("main", function () {
        // 主页为静态资源
        getProductList();
        // getOralMenu();
    })

    $(".page").on('click', '[type="btn"]', function () {
        let name = $(this).attr("name");
        let info = JSON.parse($(this).parents("tr").find(".hd").html())
        switch (name){
            case 'scan':
                bounce.show($("#seeProduct"));
                getProductInfo(info.id);
                break;
            case 'update':
                bounce.show($("#editProduct"));
                $("#editProduct .bounce_title").html("修改产品");
                $("#editProduct [name='name']").val(info.name);
                $("#editProduct .updateShow").show();
                $("#editProduct").data('type', 'update');
                $("#editProduct").data('id', info.id);
                $.ajax('../productSetting/getMpModules.do')
                    .then(res => {
                        let data = res.data
                        let optionStr = ''
                        if (data && data.length > 0) {
                            for(let item of data){
                                optionStr += '<option value="' + item.id + '">' + item.name + '</option>'
                            }
                        }
                        $("#editProduct .chooseModel").html(optionStr)
                        $("#editProduct .chooseModel").val(info.mpTmplId).change();
                        $("#editProduct").data('oldTmplId', info.mpTmplId);
                    })
                break;
            case "stop":
                bounce.show($("#bounce_tip"));
                $("#bounce_tip .tipMsg").html('产品如被停用，则在新增机构时无法再被选择。<br>确定停用本产品吗?')
                $("#bounce_tip .sureBtn").unbind().on("click", function (){
                    $.ajax({
                        url: '../productSetting/mpPackageStop.do',
                        data:{ id: info.id }
                    }).then(res => {
                        let data = res.data
                        if (data == 1) {
                            bounce.cancel()
                            layer.msg("操作成功")
                            getProductList()
                        } else {
                            layer.msg("操作失败");
                        }
                    })
                })
                break;
            case "start":
                let name = $.trim($(this).parents("tr").find("td").eq(0).text())
                bounce.show($("#bounce_tip"));
                $("#bounce_tip .tipMsg").html('恢复使用后，新增机构时该产品将重新成为选项。<br>确定提交本申请吗?')
                $("#bounce_tip .sureBtn").unbind().on("click", function (){
                    $.ajax({
                        url: '../thali/mpPackageRecoveryApply.do',
                        data:{ id: info.id, name: name }
                    }).then(res => {
                        let data = res.data
                        if (data == 1) {
                            bounce.cancel()
                            layer.msg("操作成功")
                        } else {
                            layer.msg("操作失败");
                        }
                    })
                })
                break;
            case "oldOrgNum":
                $(".page[page='product_usedOrg'] .pdName").html(info.name)
                $(".page[page='product_usedOrg'] .pdModel").html(info.modelName)
                $(".page[page='product_usedOrg'] .pdTime").html(info.createName + ' ' + moment(info.createDate).format('YYYY-MM-DD HH:mm:ss'))
                jumpPage('product_usedOrg', function (){
                    getUsingOrg(info.id, 'oldOrgNum');
                })
                break;
            case "orgNum":
                $(".page[page='product_useOrg'] .pdName").html(info.name)
                $(".page[page='product_useOrg'] .pdModel").html(info.modelName)
                $(".page[page='product_useOrg'] .pdTime").html(info.createName + ' ' + moment(info.createDate).format('YYYY-MM-DD HH:mm:ss'))
                jumpPage('product_useOrg', function (){
                    getUsingOrg(info.id, 'orgNum');
                })
                break;
            case "log":
                // 等待开发
                break;
            default:
                break
        }
    })
});

// creator: 张旭博，2023-07-21 08:24:30， 新增产品 - 按钮
function newProduct() {
    bounce.show($("#editProduct"));

    $("#editProduct .bounce_title").html("新增产品");
    $("#editProduct [name='name']").val('')
    $("#editProduct .updateShow").hide()
    $("#editProduct .modelInfo").hide()
    $("#editProduct").data('type', 'new')
    $.ajax('../productSetting/getMpModules.do')
        .then(res => {
            let data = res.data
            let optionStr = '<option value="">----请选择----</option>'
            if (data && data.length > 0) {
                for(let item of data){
                    optionStr += '<option value="' + item.id + '">' + item.name + '</option>'
                }
            }
            $(".chooseModel").html(optionStr)
        })
}

// create :hxz 2020-10-27 查看停用列表
function seeStopProduct() {
    $(".tbl_product_stop tbody").html('')
    jumpPage('product_stop', function (){
        $.ajax('../productSetting/getStopMpPackages.do')
            .then(res => {
                var data = res.data
                var tbodyStr = ''
                if (data && data.length > 0) {
                    for (let item of data) {
                        tbodyStr += '<tr>' +
                            '   <td>' + item.name + '</td>' +
                            '   <td>' + item.modelName + '</td>' +
                            '   <td>' + item.createName + ' ' + moment(item.createDate).format('YYYY-MM-DD HH:mm:ss') + '</td>' +
                            '   <td><span class="link-blue" type="btn" name="oldOrgNum">' + item.orgCount + '个</span></td>' +
                            '   <td>' +
                            '       <span class="link-blue" type="btn" name="scan">查看</span>' +
                            '       <span class="link-blue" type="btn" name="start">恢复使用</span>' +
                            '       <span class="link-gray">修改记录</span>' +
                            '   </td>' +
                            '   <td class="hd">' + JSON.stringify(item) + '</td>' +
                            '</tr>'
                    }
                }
                $(".tbl_product_stop tbody").html(tbodyStr)
            })
    })
}

// create :hxz 2020-10-27 获取正在使用某产品的机构
function getUsingOrg(pdID, type) {
    if (!pdID) {
        pdID = $(".seeOldOrg").attr("data-id") // 暂时没有开发好
    }
    $.ajax({
        url: '../special/getMpPackagesOrgsById.do',
        data: {
            id: pdID
        }
    }).then(res => {
        let data = res.data
        let tbodyStr = ''
        for(let item of data){
            tbodyStr += '<tr>' +
                '    <td>'+item.fullName+'</td>' +
                '    <td>'+(item.createName || item.updateName)+'</td>' +
                ' </tr>'
        }
        if (type === 'oldOrgNum') {
            $(".page[page='product_usedOrg'] .orgNum").html(data.length);
            $(".tbl_usedOrg tbody").html(tbodyStr);
        } else {
            $(".page[page='product_useOrg'] .orgNum").html(data.length);
            $(".tbl_useOrg tbody").html(tbodyStr);
        }
    })
}

// creator: 张旭博，2023-07-21 09:13:18， 正在使用某产品的机构 - 查看曾使用的机构
function seeOldOrg() {
    // jumpPage('product_usedOrg', function () {
    //     getUsingOrg('', 'oldOrgNum')
    // })
}

// creator: 张旭博，2023-08-03 11:06:13， 编辑产品 - 确定
function sureEditProduct() {
    let name = $("#editProduct [name='name']").val();
    let moduleId = $("#editProduct .chooseModel").val();
    let type = $("#editProduct").data('type')
    if(name.length === 0 ){
        layer.msg("产品名称不能为空！")
        return false
    }
    if(moduleId === ''){
        layer.msg("请选择模板！")
        return false
    }
    let str = '确定提交新产品的创建申请吗?'
    if(type === 'update'){
        str = '产品的修改可能对正在使用的机构产生重大影响。<br>确定提交本申请吗?'
    }
    $("#bounceFixed_tip .tipMsg").html(str)
    bounce_Fixed.show($("#bounceFixed_tip"))
    $("#bounceFixed_tip .sureBtn").unbind().on("click", function (){
        let url = ''
        let data = {}
        if(type === 'update') {
            let id = $("#editProduct").data('id')
            let oldTmplId = $("#editProduct").data('oldTmplId')
            url = '../thali/mpPackageEditApply.do'
            data = { id: id, oldTmplId: oldTmplId, tmplId: moduleId , name: name }
        } else {
            url = '../thali/addMpPackageApply.do'
            data = { tmplId: moduleId , name: name }
        }
        $.ajax({
            url: url ,
            data: data
        }).then(res => {
            let data = res.data;
            if(data == 1){
                bounce.cancel();
                bounce_Fixed.cancel();
                layer.msg("操作成功");
            }else{
                layer.msg("操作失败");
            }
        })
    })
}

// create :hxz 2020-10-26 获取产品详情
function getProductInfo(pdID) {
    $.ajax({
        url: '../thali/getMpPackagesInfo.do',
        data: { packagesId: pdID }
    }).then(res => {
        let data = res.data
        let mpPackages = data.mpPackages // 产品详情
        let mpTmpl = data.mpTmpl // 模板详情
        let mainModuleList = data.mainModuleList || [] // 主模块列表
        let increaseModuleList = data.increaseModuleList || [] // 增值模块列表
        let reNameList = data.reNameList || [] // 重命名的菜单列表
        $("#seeProduct .pdName").html(mpPackages.name);
        $("#seeProduct .pdTime").html(mpPackages.createName + ' ' + moment(mpPackages.createDate).format('YYYY-MM-DD HH:mm:ss'));
        $("#seeProduct .pdModel").html(mpTmpl.name);
        $("#seeProduct .renameNumber").html(reNameList.length)
        renderRename('see_rename', reNameList)
        renderModuleList('see_mainModule', mainModuleList)
        renderModuleList('see_valueAddedModule', increaseModuleList)
        let increaseIdArr = increaseModuleList.map(item => item.id)
        $("#seeUsableSetMenu").data('add', increaseIdArr)
    })

}

// create :hxz 2020-10-26 获取列表
function getProductList() {
    $.ajax('../productSetting/getMpPackages.do')
        .then(res => {
            var data = res.data
            var tbodyStr = ''
            if (data && data.length > 0) {
                for(let item of data){
                    tbodyStr += '<tr data-id="'+item.id+'">' +
                        '   <td>'+ item.name+'</td>' +
                        '   <td>'+ item.modelName+'</td>' +
                        '   <td>'+ item.createName + ' ' + moment(item.createDate).format('YYYY-MM-DD HH:mm:ss') + '</td>' +
                        '   <td><span class="link-blue" type="btn" name="orgNum">'+ item.orgCount+'</span></td>' +
                        '   <td>' +
                        '       <span class="link-blue" type="btn" name="scan">查看</span> ' +
                        (item.orgCount > 0 ? '<span class="link-gray">修改</span>' : '<span class="link-blue" type="btn" name="update">修改</span>') +
                        '       <span class="link-red" type="btn" name="stop">停用</span>' +
                        '       <span class="link-gray">修改记录</span>' +
                        '   </td>' +
                        '   <td class="hd">' + JSON.stringify(item) + '</td>' +
                        '</tr>' ;
                }
            }
            $(".tbl_main tbody").html(tbodyStr);
        })
}

// create :hxz 2020-10-27 根据选择的模板展示详情
function changeModelInfo(selector) {
    $(".modelInfo").show()
    let id = selector.val()
    $.ajax({
        url: '../thali/getMpTmplInfo.do',
        data: { id: id }
    }).then(res => {
        let data = res.data
        let mpTmpl = data.mpTmpl // 模板详情
        let mainModuleList = data.mainModuleList // 主模块列表
        let increaseModuleList = data.increaseModuleList // 主模块列表
        let reNameList = data.reNameList // 主模块列表
        $("#editProduct .renameNumber").html(reNameList.length)
        renderRename('see_rename', reNameList)
        renderModuleList('see_mainModule', mainModuleList)
        renderModuleList('see_valueAddedModule', increaseModuleList)
        let increaseIdArr = increaseModuleList.map(item => item.id)
        $("#seeUsableSetMenu").data('add', increaseIdArr)
    })
}

// creator: 张旭博，2023-08-01 11:06:47， 渲染模块表格
function renderModuleList(name, data) {
    let str = ''

    for (let item of data) {
        str += `<tr data-id="${item.id}">
                    <td class="name">${item.name}</td>
                    <td>${item.topMenu || '--'}个</td>
                    <td>
                        <span class="link-blue" onclick="seeModule($(this))">查看</span>
                    </td>
                </tr>`
    }
    $('.tbl_' + name + ' tbody').html(str)
}

// creator: 张旭博，2023-07-20 11:43:32， 套餐清单查看 - 按钮
function seeUsableSetMenuBtn() {
    bounce_Fixed.show($("#seeUsableSetMenu"))
    let ids = $("#seeUsableSetMenu").data('add')

    $.ajax({
        url: '../thali/getMpSets.do',
        data: { ids: ids.length>0?ids.join(','):0 }
    }).then(res => {
        let data = res.data
        let str = ''
        if (data) {
            for (let item of data) {
                str += `<tr data-id="${item.id}">
                            <td class="name">${item.name}</td>
                            <td>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>
                                <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                            </td>
                        </tr>`
            }
            $("#seeUsableSetMenu tbody").html(str)
        } else {
            layer.msg('数据错误！')
        }
    })
}

// creator: 张旭博，2023-08-01 02:36:14， 查看套餐
function seeMenu(selector) {
    let id = selector.parents("tr").data("id")
    let setMenuName = selector.parents("tr").find("td").eq(0).html()
    let create = selector.parents("tr").find("td").eq(1).html()
    bounce_Fixed2.show($("#seeSetMenu"))
    $("#seeSetMenu .setMenuName").html(setMenuName)
    $("#seeSetMenu .create").html(create)
    $.ajax({
        url: '../thali/getModulesByMpSetId.do',
        data: { id: id }
    }).then(res => {
        let data = res.data
        let str = ''
        if (data) {
            for (let item of data) {
                str += `<tr data-id="${item.id}">
                            <td class="name">${item.name}</td>
                            <td>${item.topMenu || '--'}个</td>
                            <td>
                                <span class="link-blue" onclick="seeModule($(this))">查看</span>
                            </td>
                        </tr>`
            }
            $("#seeSetMenu tbody").html(str)
        } else {
            layer.msg('数据错误！')
        }
    })
}

// creator: 张旭博，2023-08-01 10:19:14， 查看模块详情
function seeModule(selector) {
    let id = selector.parents("tr").data("id")
    let moduleName = selector.parents("tr").find(".name").html()
    $("#seeModule .moduleName").html(moduleName)
    $.ajax({
        url: '../thali/getModuleInfo.do',
        data: { id: id }
    }).then(res => {
        let data = res.data
        if (data && data.length > 0) {
            // 一层模块数据 -> 树型数据，将对应的子级放在subPopdoms中
            let newData = changeDataToTree(data)
            // 将处理后的数据渲染表格
            let tbodyStr = renderModuleToTable(newData, 1, 2)
            $("#seeModule tbody").html(tbodyStr)
            bounce_Fixed3.show($("#seeModule"))
        } else {
            layer.msg('数据错误！')
        }
    })
}