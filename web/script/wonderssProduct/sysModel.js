var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#chooseModule"));
bounce_Fixed3.show($("#seeModule"));
bounce_Fixed2.cancel();
bounce_Fixed3.cancel();
$(function () {
    getModelList();
    $(".page[page='main']").on('click', '[type="btn"]', function () {
        var name = $(this).attr("name")
        switch (name) {
            case 'seeModel':
                bounce.show($("#seeModel"))
                break
        }
    })
    $(".bounce").on('click', '[type="btn"]', function () {
        var name = $(this).attr("name")
        switch (name) {
            case 'seeModule':
                bounce_Fixed3.show($("#seeModule"))
                break
        }
    })
    $(".bounce_Fixed").on('click', '[type="btn"]', function () {
        var name = $(this).attr("name")
        switch (name) {
            case 'seeModule':
                bounce_Fixed3.show($("#seeModule"))
                break
            case 'seeSetMenu':
                bounce_Fixed2.show($("#seeSetMenu"))
                break
        }
    })
    $(".bounce_Fixed2").on('click', '[type="btn"]', function () {
        var name = $(this).attr("name")
        switch (name) {
            case 'seeModule':
                let id = $(this).parents("tr").data("id")
                let moduleName = $(this).parents("tr").find(".name").html()
                $("#seeModule .moduleName").html(moduleName)
                $.ajax({
                    url: '../thali/getModuleInfo.do',
                    data: { id: id }
                }).then(res => {
                    let data = res.data
                    if (data && data.length > 0) {
                        // 一层模块数据 -> 树型数据，将对应的子级放在subPopdoms中
                        let newData = changeDataToTree(data)
                        // 将处理后的数据渲染表格
                        let tbodyStr = renderModuleToTable(newData, 1, 2)
                        $("#seeModule tbody").html(tbodyStr)
                        bounce_Fixed3.show($("#seeModule"))
                    } else {
                        layer.msg('数据错误！')
                    }
                })
                break
        }
    })
});

// creator: 张旭博，2023-08-02 05:28:50， 获取模板列表
function getModelList() {
    $.ajax('../productSetting/getMpModules.do')
        .then(res => {
            var data = res.data
            var tbodyStr = ''
            if (data && data.length > 0) {
                for(let item of data) {
                    tbodyStr += `<tr data-id="${item.id}">
                                   <td>${item.name}</td>
                                   <td>${item.name}</td>
                                   <td>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                                   <td>
                                       <span class="link-blue" onclick="seeModelBtn($(this))">查看</span>
                                       <span class="link-gray">修改</span>
                                       <span class="link-gray">修改记录</span>
                                        <span class="link-blue" onclick="reNameLogBtn($(this))">菜单重命名记录</span>
                                        <span class="link-red" onclick="delModelBtn($(this))">删除</span>
                                   </td>
                                </tr>`
                }
                $(".tbl_main tbody").html(tbodyStr)
            }
        })
}

// creator: hxz 2025-05-20  菜单重命名记录
function reNameLogBtn(selector) {
    let id = selector.parents("tr").data("id")
    $.ajax({
        url: '../thali/getTmplRenameHistories.do',
        data: { tmplId: id },
        success:function(res){
            let list = res.data || []
            let str = ''
            list.forEach(item=>{
                str += `
                <tr>
                    <td>${ item.name || '' }</td>
                    <td>${ item.newName || '' }</td>
                    <td>${ item.createName || '' } ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }</td>
                    <td>
                        <span class="ty-color-blue" onclick="reNameLogDetail($(this))">查看</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                `
            })
            $("#logList tr:gt(0)").remove()
            $("#logList").append(str)
            bounce.show($("#reNameLog"))
        },
        error:function(errMsg){
            console.log('errMsg=', errMsg)
        },
    })
}

function reNameLogDetail(selecter) {
    let info = selecter.siblings('.hd').html()
    let item = JSON.parse(info)
    console.log('')
    $("#logList2 tr:gt(0)").remove()
    let str = `
                <tr>
                    <td>${ item.createName || '' } ${ new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss') }</td>
                    <td>${ item.newName || '' }</td>
                    <td>${ item.memo || '' }</td>
                </tr>
                `
    $("#logList2").append(str)
    $(".chuTip").html(`初始名称为${ item.name }，当前名称为${ item.newName }菜单的重命名记录`)
    bounce_Fixed.show($("#reNameLog2"))

}

// creator: hxz 2025-05-20  删除模板
function delModelBtn(selector) {
    let id = selector.parents("tr").data("id")
    $("#delModel").data('delid', id)
    bounce.show($("#delModel"))
}
function delModelOkBtn() {
    let id = $("#delModel").data('delid')
    $.ajax({
        url: '../thali/deleteMpTmpl.do',
        data: { id: id },
        success:function(res){
            let success = res.success
            bounce.cancel()
            if(success === 1){
                layer.msg(res.data)
            }else{
                layer.msg(res.error.code)
            }

        },
        error:function(errMsg){
            console.log('errMsg=', errMsg)
        },
    })
}


// creator: 张旭博，2023-08-02 05:29:02， 查看模板
function seeModelBtn(selector) {
    let id = selector.parents("tr").data("id")
    $.ajax({
        url: '../thali/getMpTmplInfo.do',
        data: { id: id }
    }).then(res => {
        let data = res.data
        let mpTmpl = data.mpTmpl // 模板详情
        let mainModuleList = data.mainModuleList // 主模块列表
        let increaseModuleList = data.increaseModuleList // 主模块列表
        let reNameList = data.reNameList // 主模块列表
        $("#seeModel .modelName").html(mpTmpl.name)
        $("#seeModel .renameNumber").html(reNameList.length)
        renderRename('see_rename', reNameList)
        renderModuleList('see_mainModule', mainModuleList)
        renderModuleList('see_valueAddedModule', increaseModuleList)
        bounce.show($("#seeModel"))
        let increaseIdArr = increaseModuleList.map(item => item.id)
        $("#seeModel").data('add', increaseIdArr)
    })
}


// creator: 张旭博，2023-07-20 10:51:37， 创建模板 - 按钮
function createModel() {
    bounce.show($("#editModel"));
    $("#editModel input").val("")
    $("#editModel").find("[part='step1']").show().siblings(".part").hide()
    let moduleData = $("#editModel").data('data')
    if (!moduleData) {
        $.ajax({
            url: '../thali/getSelectModules.do',
            data: {
                ids: 0
            }
        }).then(res => {
            let data = res.data
            $("#editModel").data('data', {
                originModule: JSON.stringify(data),
                mainModule: JSON.stringify([]),
                valueAddedModule: JSON.stringify([])
            })
        })
    } else {
        let data = $("#editModel").data('data')
        data.mainModule = JSON.stringify([])
        data.valueAddedModule = JSON.stringify([])
    }
    renderModuleList('mainModule', [])
    renderModuleList('valueAddedModule', [])
}

// creator: 张旭博，2023-07-20 10:54:18， 创建模板 - 下一步按钮
function createNextBtn() {
    let data = $("#editModel").data('data')
    let mainModule = JSON.parse(data.mainModule)
    if (mainModule.length < 1) {
        layer.msg('主套餐至少选择一个模块')
        return false
    }
    $("#editModel").find("[part='step2']").show().siblings(".part").hide()
    renderModuleList('mainModuleSee', mainModule)
}

// creator: 张旭博，2023-07-19 03:16:46， 关于模板 - 按钮
function aboutModelBtn() {
    bounce_Fixed.show($("#aboutModel"))
}

// creator: 张旭博，2023-07-19 03:16:46， 去选择模块 - 按钮
function chooseModuleBtn(type) {
    bounce_Fixed2.show($("#chooseModule"))
    $("#chooseModule").data('type', type)
    renderChooseModuleTable()
}

// creator: 张旭博，2023-08-02 03:39:48， 渲染可选表格
function renderChooseModuleTable() {
    let type = $("#chooseModule").data('type')
    let data = {}
    if (type === 'menu') {
        data = $("#editSetMenu").data('data')

        let originModule = JSON.parse(data.originModule)
        let menuModule = JSON.parse(data.menuModule)

        let chooseModule = getNoRepeatModule(originModule, menuModule)

        renderModuleList('chooseModule', chooseModule)
    } else {
        data = $("#editModel").data('data')

        let totalModule = []
        let mainModule = JSON.parse(data.mainModule)
        let originModule = JSON.parse(data.originModule)

        if (type === 'valueAdded') {
            let valueAddedModule = JSON.parse(data.valueAddedModule)
            totalModule = [...mainModule, ...valueAddedModule]
        } else if (type === 'main'){
            totalModule = mainModule
        }
        let chooseModule = getNoRepeatModule(originModule, totalModule)

        renderModuleList('chooseModule', chooseModule)

    }
}

// creator: 张旭博，2023-08-02 11:00:22， 筛选出排重后的模块
function getNoRepeatModule(originModule, totalModule) {
    let otherModule = originModule.filter(item => {
        return totalModule.findIndex(it => it.id === item.id) === -1
    })

    let mids = []
    if (totalModule.length > 0) {
        let memoArr = totalModule.map(item => item.menuSet)
        let memoStr = memoArr.join(',')
        mids = memoStr.split(',')
    }


    // 筛选出跟已选模块没有菜单冲突的模块
    let canChooseModule = otherModule.filter(item => {
        return compare(item.menuSet.split(','), mids)
    })
    $("#chooseModule").data('canChooseModule', canChooseModule)
    return canChooseModule
}

// creator: 张旭博，2023-08-02 10:47:02， 查看两个数组是否有交集
function compare(a, b) {
    // 两数组去重之后与原数组元素个数对比
    return [...new Set([...a, ...b])].length === a.length + b.length
}

// creator: 张旭博，2023-08-01 10:13:16， 去选择模块 - 确定按钮
function sureChooseModule() {
    let type = $("#chooseModule").data('type') // 是从哪点击的去选择模块 main:主套餐、valueAdded:增值服务、menu:新套餐
    let editModelData = $("#editModel").data('data')
    let editSetMenuData = $("#editSetMenu").data('data')

    let originModule = [], menuModule = [], mainModule = [], valueAddedModule = []
    if (type === 'menu') {
        originModule = JSON.parse(editSetMenuData.originModule)
        menuModule = JSON.parse(editSetMenuData.menuModule) // 新套餐数据
    } else {
        originModule = JSON.parse(editModelData.originModule)
        mainModule = JSON.parse(editModelData.mainModule) // 主套餐数据
        valueAddedModule = JSON.parse(editModelData.valueAddedModule) // 增值数据
    }

    // 存储新选择的数据，存储与原数据相加后的数据
    let selectModule = [], newModule = []
    $("#chooseModule tbody input:checkbox:checked").each(function () {
        let id = $(this).parents("tr").data("id")
        let item = originModule[originModule.findIndex(it => it.id === id)]
        selectModule.push(item)
    })
    if (selectModule.length < 1) {
        layer.msg('请至少选择一个模块！')
        return false
    }
    if (type === 'main') {
        newModule = [...mainModule, ...selectModule]
        editModelData.mainModule = JSON.stringify(newModule)
        renderModuleList('mainModule', newModule)
    }
    if (type === 'valueAdded') {
        newModule = [...valueAddedModule, ...selectModule]
        editModelData.valueAddedModule = JSON.stringify(newModule)
        renderModuleList('valueAddedModule', newModule)
    }

    if (type === 'menu') {
        newModule = [...menuModule, ...selectModule]
        editSetMenuData.menuModule = JSON.stringify(newModule)
        renderModuleList('menuModule', newModule)
    }
    bounce_Fixed2.cancel()
}

// creator: 张旭博，2023-08-01 10:19:14， 查看模块详情
function seeModule(selector) {
    let id = selector.parents("tr").data("id")
    let moduleName = selector.parents("tr").find(".name").html()
    $("#seeModule .moduleName").html(moduleName)
    $.ajax({
        url: '../thali/getModuleInfo.do',
        data: { id: id }
    }).then(res => {
        let data = res.data
        if (data && data.length > 0) {
            // 一层模块数据 -> 树型数据，将对应的子级放在subPopdoms中
            let newData = changeDataToTree(data)
            // 将处理后的数据渲染表格
            let tbodyStr = renderModuleToTable(newData, 1, 2)
            $("#seeModule tbody").html(tbodyStr)
            bounce_Fixed3.show($("#seeModule"))
        } else {
            layer.msg('数据错误！')
        }
    })
}

// creator: 张旭博，2023-08-01 11:02:36， 移除模块（不调用接口）
function removeModule(selector, type) {
    let id = selector.parents('tr').data('id')

    if (type === 'menu') {
        let data = $("#editSetMenu").data('data')
        let menuModule = JSON.parse(data.menuModule)
        let newMenuModule = menuModule.filter(item => item.id !== id)
        data.menuModule = JSON.stringify(newMenuModule)
        renderModuleList('menuModule', newMenuModule)
    }
    let data = $("#editModel").data('data')
    let mainModule = JSON.parse(data.mainModule)
    let valueAddedModule = JSON.parse(data.valueAddedModule)

    if (type === 'main') {
        let newMainModule = mainModule.filter(item => item.id !== id)
        data.mainModule = JSON.stringify(newMainModule)
        renderModuleList('mainModule', newMainModule)
    }
    if (type === 'valueAdded') {
        let newMainModule = valueAddedModule.filter(item => item.id !== id)
        data.valueAddedModule = JSON.stringify(newMainModule)
        renderModuleList('valueAddedModule', newMainModule)
    }
}

// creator: 张旭博，2023-08-01 11:06:47， 渲染模块表格
function renderModuleList(name, data) {
    let str = ''

    for (let item of data) {
        let removeStr = ''
        let chooseStr = ''
        switch (name) {
            case 'mainModule':
                removeStr = `<span class="link-red" onclick="removeModule($(this), 'main')">移除</span>`
                break
            case 'valueAddedModule':
                removeStr = `<span class="link-red" onclick="removeModule($(this), 'valueAdded')">移除</span>`
                break
            case 'menuModule':
                removeStr = `<span class="link-red" onclick="removeModule($(this), 'menu')">移除</span>`
                break
            case 'chooseModule':
                chooseStr = `<td>
                                <div class="ty-checkbox" onchange="screenCanChooseModule()">
                                    <input type="checkbox" id="module_${item.id}" ${item.disabled?"disabled":""}>
                                    <label for="module_${item.id}"></label>
                                </div>
                            </td>`
                break
        }
        str += `<tr data-id="${item.id}">
                    ${chooseStr}
                    <td class="name">${item.name}</td>
                    <td>${item.topMenu || '--'}个</td>
                    <td>
                        <span class="link-blue" onclick="seeModule($(this))">查看</span>
                        ${removeStr}
                    </td>
                </tr>`
    }
    $('.tbl_' + name + ' tbody').html(str)
}

// creator: 张旭博，2023-08-02 01:29:06， 筛选
function screenCanChooseModule() {
    // 未勾选时现在可选的总模块
    let canChooseModule = $("#chooseModule").data('canChooseModule')
    // 已勾选的模块
    let checkedModule = []
    $("#chooseModule input:checkbox:checked").each(function (){
        let id = $(this).parents("tr").data("id")
        let thisItem = canChooseModule[canChooseModule.findIndex(item => item.id === id)]
        checkedModule.push(thisItem)
    })
    // 剩余模块
    let otherModule = canChooseModule.filter(item => {
        return checkedModule.findIndex(it => it.id === item.id) === -1
    })
    // 勾选模块的各个菜单
    let mids = []
    if (checkedModule.length > 0) {
        let memoArr = checkedModule.map(item => item.menuSet)
        let memoStr = memoArr.join(',')
        mids = memoStr.split(',')
    }

    // 将跟勾选模块菜单冲突的模块禁用或者解除禁用
    otherModule.forEach(item => {
        $("#chooseModule input:checkbox[id='module_"+item.id+"']").prop("disabled", !compare(item.menuSet.split(','), mids))
    })
}

// creator: 张旭博，2023-07-20 11:41:54， 创建新套餐 - 按钮
function editSetMenuBtn() {
    bounce_Fixed.show($("#editSetMenu"))
    $("#editSetMenu .kj-input").val('')
    let data = $("#editModel").data('data')
    let valueAddedModule = JSON.parse(data.valueAddedModule)
    $("#editSetMenu").data('data', {
        originModule: JSON.stringify(valueAddedModule),
        menuModule: JSON.stringify([])
    })
    renderModuleList('menuModule', [])
}

// creator: 张旭博，2023-08-02 04:51:25， 创建新套餐 - 确定按钮
function sureEditSetMenu() {
    let name = $("#editSetMenu [name='name']").val()
    let editSetMenuData = $("#editSetMenu").data('data')
    let menuModule = JSON.parse(editSetMenuData.menuModule)
    let ids = menuModule.map(item => item.id)
    if (name === '') {
        layer.msg('请录入套餐名称！')
        return false
    }
    if (ids.length < 2) {
        layer.msg('操作失败！<br>套餐中所含模块不能少于两个！！')
        return false
    }
    $.ajax({
        url: '../thali/addMpSet.do',
        data: {
            name: name,
            ids: ids.join(',')
        }
    }).then(res => {
        if (res.error) {
            layer.msg(res.error.message)
        } else {
            let data = res.data
            if (data === 1) {
                layer.msg("操作成功！")
                bounce_Fixed.cancel()
            } else {
                layer.msg('数据错误！')
            }
        }
    })
}

// creator: 张旭博，2023-07-20 11:43:32， 套餐清单查看 - 按钮
function seeUsableSetMenuBtn(type) {
    bounce_Fixed.show($("#seeUsableSetMenu"))
    let data = $("#editModel").data('data')
    let ids = []
    if (type === 'seeModel') {
        ids = $("#seeModel").data('add')
    } else {
        let valueAddedModule = JSON.parse(data.valueAddedModule)
        ids = valueAddedModule.map(item => item.id)
    }
    $.ajax({
        url: '../thali/getMpSets.do',
        data: { ids: ids.length>0?ids.join(','):0 }
    }).then(res => {
        let data = res.data
        let str = ''
        if (data) {
            for (let item of data) {
                str += `<tr data-id="${item.id}">
                            <td class="name">${item.name}</td>
                            <td>${item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss")}</td>
                            <td>
                                <span class="link-blue" onclick="seeMenu($(this))">查看</span>
                            </td>
                        </tr>`
            }
            $("#seeUsableSetMenu tbody").html(str)
        } else {
            layer.msg('数据错误！')
        }
    })
}

// creator: 张旭博，2023-08-01 02:36:14， 查看套餐
function seeMenu(selector) {
    let id = selector.parents("tr").data("id")
    let setMenuName = selector.parents("tr").find("td").eq(0).html()
    let create = selector.parents("tr").find("td").eq(1).html()
    bounce_Fixed2.show($("#seeSetMenu"))
    $("#seeSetMenu .setMenuName").html(setMenuName)
    $("#seeSetMenu .create").html(create)
    $.ajax({
        url: '../thali/getModulesByMpSetId.do',
        data: { id: id }
    }).then(res => {
        let data = res.data
        let str = ''
        if (data) {
            for (let item of data) {
                str += `<tr data-id="${item.id}">
                            <td class="name">${item.name}</td>
                            <td>${item.topMenu || '--'}个</td>
                            <td>
                                <span class="link-blue" onclick="seeModule($(this))">查看</span>
                            </td>
                        </tr>`
            }
            $("#seeSetMenu tbody").html(str)
        } else {
            layer.msg('数据错误！')
        }
    })
}

// creator: 张旭博，2023-07-20 01:39:13， 创建完成
function createComplete() {
    bounce_Fixed.show($("#addModelTip"))
    $("#addModelTip input:radio").prop('checked', false)
}

// creator: 张旭博，2023-08-04 01:19:17， 重命名
function addModelTipOkBtn() {
    let type = $("#addModelTip [name='isNeedRename']:checked").val()
    if(type == undefined){
        layer.msg("请选择是否需要重命名的模块！");
    }else if(type == '2'){
        $("#renameManage .tbl_renameManage tbody").html('')
        $("#renameNumber").html(0);

        $.ajax('../special/getSelectPopedoms.do').then(res => {
            let data = res.data
            if (data && data.length > 0) {
                let moduleStr = renderModuleToTable(data, 1, 2)
                $("#renameManage .tbl_rename tbody").html('')
                $("#renameManage .renameNumber").html(0)
                $("#renameManage .moduleTable tbody").html(moduleStr)
                bounce_Fixed.show($("#renameManage"))
                $("#renameManage").data('rename', '[]')
                $("#renameManage .moduleTable tbody .thisLevel").unbind().on('click', function (){
                    let name = $.trim($(this).text())
                    let mid = $(this).attr('mid')
                    let pid = $(this).attr('pid')
                    bounce_Fixed2.show($("#renameEdit"));
                    $("#renameEdit input").val('')
                    $("#renameEdit .oldName").val(name)
                    $("#renameEdit").data('data', {
                        name: name,
                        mid: mid,
                        pid: pid
                    })
                })
            } else {
                layer.msg('数据错误！')
            }
        })
    }else if(type == 1){
        $("#renameManage").data('rename', '[]')
        $("#editModelTip .bonceCon").html(" <p>确定提交新模板的创建申请吗?</p>");
        bounce_Fixed.show($("#editModelTip"))
    }
}

// created : hxz 2020-12-15 模块重命名 确定
function renameEditOk() {
    let newName = $("#renameEdit .newName").val()
    if(newName.length == 0){
        layer.msg('请先输入新名字');
        return false
    }
    let data = $("#renameEdit").data('data')
    if (data.name === newName) {
        layer.msg('与初始名字重复！');
        return false
    }
    data.newName = newName
    let renameList = JSON.parse($("#renameManage").data('rename'))
    let index = renameList.findIndex(item => item.mid === data.mid)
    if (index > -1) {
        renameList[index].newName = newName
    } else {
        renameList.push(data)
    }
    $("#renameManage").data('rename', JSON.stringify(renameList))
    $("#renameManage .renameNumber").html(renameList.length)
    renderRename('renameManage', renameList)
    bounce_Fixed2.cancel();
}

// creator: 张旭博，2023-08-04 03:36:34， 再次修改模板名称
function reRename(selector) {
    let name =selector.parents('tr').find('.name').text()
    let newName =selector.parents('tr').find('.newName').text()
    let mid = selector.parents('tr').attr('mid')
    let pid = selector.parents('tr').attr('pid')
    bounce_Fixed2.show($("#renameEdit"));
    $("#renameEdit .oldName").val(name)
    $("#renameEdit .newName").val(newName)
    $("#renameEdit").data('data', {
        name: name,
        mid: mid,
        pid: pid
    })
}

// creator: 张旭博，2023-08-04 03:29:41， 重命名管理 - 提交按钮
function renameOkBtn() {
    let renameList = $("#renameManage").data('rename')
    if (renameList.length > 0) {
        $("#editModelTip .bonceCon").html(" <p>确定提交新模板的创建申请吗?</p>");
        bounce_Fixed.show($("#editModelTip"))
    } else {
        layer.msg('请先设置重命名的菜单！')
    }
}

// creator: 张旭博，2023-08-04 03:37:04， 确定提交模板申请
function editModelOk() {
    let name = $("#editModel [name='name']").val()
    let editModelData = $("#editModel").data('data')
    let mainModule = JSON.parse(editModelData.mainModule)
    let valueAddedModule = JSON.parse(editModelData.valueAddedModule)
    let mainIds = mainModule.map(item => item.id)
    let increaseIds = valueAddedModule.map(item => item.id)
    let midRenames = $("#renameManage").data('rename')
    if (name === '') {
        layer.msg("请录入模板名称！")
        return false
    }
    let data = {
        name: name, // 模板名称
        mainIds: mainIds.join(','), // 主模快
        increaseIds: increaseIds.join(','), // 增值模块
        midRenames: midRenames || ''
    }
    $.ajax({
        url: '../thali/addMpTmplApply.do',
        data: data
    }).then(res => {
        let data = res.data
        if (data === 1) {
            layer.msg("操作成功！")
            bounce.cancel()
            bounce_Fixed.cancel()
            getModelList()
        } else {
            layer.msg("操作失败！")
        }
    })
}