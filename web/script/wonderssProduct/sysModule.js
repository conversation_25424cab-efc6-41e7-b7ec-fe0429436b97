$(function () {
    getModuleList();
    $(".page[page='main']").on('click', '[type="btn"]', function () {
        let name = $(this).attr("name")
        let id = $(this).parents("tr").data("id")
        switch (name) {
            case 'see':
                let moduleName = $(this).parents("tr").find("td").eq(0).html()
                $("#seeModule .moduleName").html(moduleName)
                $.ajax({
                    url: '../thali/getModuleInfo.do',
                    data: { id: id }
                }).then(res => {
                    let data = res.data
                    if (data && data.length > 0) {
                        // 一层模块数据 -> 树型数据，将对应的子级放在subPopdoms中
                        let newData = changeDataToTree(data)
                        // 将处理后的数据渲染表格
                        let tbodyStr = renderModuleToTable(newData, 1, 2)
                        $("#seeModule tbody").html(tbodyStr)
                        bounce.show($("#seeModule"))

                    } else {
                        layer.msg('数据错误！')
                    }
                })
                break;
            case 'del2': // 模块删除
                $("#delModule").data('delid', id)
                bounce.show($("#delModule"))
                break;
        }
    })
})

// creator: hxz，2025-05-20 08:46:09 删除模块
function delModelOkBtn(){
    let id = $("#delModule").data('delid')
    $.ajax({
        url: '../thali/deleteMpModule.do',
        data: { id: id },
        success:function(res){
            let success = res.success
            bounce.cancel()
            if(success === 1){
                layer.msg(res.data)
            }else{
                layer.msg(res.error.code)
            }



        },
        error:function(errMsg){
            console.log('errMsg=', errMsg)
        },

    })
}
// creator: 张旭博，2023-07-20 08:46:09， 创建模块 - 按钮
function addNewModuleBtn() {
    // 初始化数据
    $("#editModule .kj-input").val('')
    $("#editModule input:checkbox").prop('checked', false)

    // 渲染表身，获取模块数据缓存，如无则调接口
    let moduleData = $("#editModule").data('module')
    if (moduleData) {
        // 渲染弹窗中的模块表格 type: 1:包含勾选按钮 2：不包含勾选, 公共方法，定义在sysCommon.js中
        let tbodyStr = renderModuleToTable(moduleData, 1, 1)
        $("#editModule tbody").html(tbodyStr)
        bounce.show($("#editModule"))
    } else {
        $.ajax('../special/getSelectPopedoms.do').then(res => {
            let data = res.data
            if (data && data.length > 0) {
                let tbodyStr = renderModuleToTable(data, 1, 1)
                $("#editModule tbody").html(tbodyStr)
                bounce.show($("#editModule"))
                $("#editModule").data('module', data)
            } else {
                layer.msg('数据错误！')
            }
        })
        // 添加一些勾选逻辑
        $(".moduleTable").on('change', 'input:checkbox', function () {
            let checked = $(this).prop("checked")
            if (checked) {
                // 子勾父勾，子消父不一定消
                $(this).parents("tr").children('.thisLevel').find('input:checkbox').prop("checked", true)
            }
            // 父勾子勾，父消子消
            $(this).closest("tr").children('.nextLevel').find('input:checkbox').prop("checked", checked)
            // 全部勾选逻辑
            let name = $(this).attr("name")
            if (name === 'all') {
                $(".moduleTable").find('input:checkbox').prop("checked", checked)
            }
        })
    }
}

// creator: 张旭博，2023-07-20 11:16:29， 获取模块列表
function getModuleList() {
    $.ajax('../thali/getModuleList.do')
        .then(res => {
            let data = res.data
            let tbodyStr = ''
            if (data && data.length > 0) {
                for (let item of data) {
                    tbodyStr += '<tr data-id="' + item.id + '">' +
                        '   <td>' + item.name + '</td>' +
                        '   <td>' + item.createName + ' ' + moment(item.createDate).format("YYYY-MM-DD HH:mm:ss") + '</td>' +
                        '   <td>' +
                        '       <span class="link-blue" type="btn" name="see">查看</span>' +
                        '       <span class="link-gray">修改</span>' +
                        '       <span class="link-gray">修改记录</span>' +
                        '       <span class="link-red" type="btn" name="del2">删除</span>' +
                        '   </td>' +
                        '</tr>'
                }
                $(".tbl_main tbody").html(tbodyStr)
            }
        })
}

// creator: 张旭博，2023-07-31 04:35:32， 创建-模板-确定
function editModelOkBtn() {
    let name = $("#editModule input[name='name']").val()
    let firstNumber = $("#editModule tbody [level='1'] input:checkbox:checked").length
    if (name === '') {
        layer.msg('请录入模块名称！')
        return false
    }
    if (firstNumber === 0) {
        layer.msg('请至少选择一种模块！')
        return false
    }

    let midArr = []
    $("#editModule tbody input:checkbox:checked").each(function (){
        let moduleCode = $(this).attr("name")
        midArr.push(moduleCode)
    })
    $.ajax({
        url: '../thali/addModule.do',
        data: {
            name: name,
            mids: midArr.join(','),
            firstNumber: firstNumber
        }
    }).then(res => {
        let data = res.data
        if (data === 1) {
            layer.msg('操作成功')
            bounce.cancel()
            getModuleList()
        } else {
            layer.msg('操作失败')
        }
    })
}