// creator: 张旭博，2023-02-24 15:39:49，返回上一步
function back() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr.pop()
    $("#home").data("pathArr", pathArr)
    isShowNav()
    var lastEle = pathArr[pathArr.length - 1]
    $(".page[page='"+lastEle+"']").show().siblings(".page").hide()
    if (lastEle === 'main') {
        $(".backBtn").hide()
    }
}

// creator: 张旭博，2023-02-24 15:40:51，回到主页
function backToMain() {
    if ($(".ty-page-header").attr("disabled")) {
        return false
    }
    var pathArr = $("#home").data("pathArr")
    pathArr = [pathArr[0]]
    $("#home").data("pathArr", pathArr)
    isShowNav()
    $(".page[page='"+pathArr[0]+"']").show().siblings(".page").hide()
}

// creator: 张旭博，2023-02-09 04:56:46， 跳转及退回
function jumpPage(page, callback) {
    var arr = $("#home").data("pathArr") || []
    arr.push(page)
    $("#home").data("pathArr", arr)
    isShowNav()
    $(".page[page='"+page+"']").show().siblings(".page").hide()
    if (page !== 'main') {
        $(".backBtn").show()
    }
    if (callback) { callback()}
}

// creator: 张旭博，2023-02-24 15:40:37，是否禁用导航
function isShowNav() {
    var pathArr = $("#home").data("pathArr")
    if (pathArr.length > 1) {
        $(".ty-page-header").show()
    } else {
        $(".ty-page-header").hide()
    }
}

// creator: 张旭博，2023-07-31 02:05:02， 将模块数据 平铺结构改为树型结构
function changeDataToTree(originData) {
    let newData = []
    for (let item of originData) {
        for (let it of originData) {
            if (it.pid === item.mid) {
                item.subPopdoms.push(it)
            }
        }
        if (item.pid === '0') {
            newData.push(item)
        }
    }
    return newData
}

// creator: 张旭博，2023-07-31 02:08:14， 渲染模块数据成表格（递归）
function renderModuleToTable(originData, level, type) {
    // type : 1 勾选 2 查看
    let listStr = ''
    for (let item of originData) {
        let nextData = item.subPopdoms
        let NextStr = level < 3 ? renderModuleToTable(nextData, level+1, type): ''
        listStr +=  '<tr>' +
            '   <td class="thisLevel" level="'+level+'" mid="'+item.mid+'" pid="'+item.pid+'">' +
    (type === 1?'   <div class="ty-checkbox">' +
                '           <input type="checkbox" name="'+item.mid+'" id="module_' + item.mid + '">' +
                '           <label for="module_' + item.mid + '"></label>' + item.name +
                '       </div>' +
                '   </td>' : item.name) +
    (level < 3? '   <td class="nextLevel">' +
                '       <table class="kj-table">'+
                '           <tbody>'+
                NextStr +
                '           </tbody>'+
                '       </table>'+
                '   </td>':'') +
            '</tr>'
    }
    if (originData.length > 0) {
        return listStr
    } else {
        // 占位
        return '<tr><td><span style="visibility: hidden">--</span></td></tr>'
    }
}

// creator: 张旭博，2023-08-04 03:36:09， 渲染重命名表格
function renderRename(name, data) {
    let str = ''
    let changeStr = name === 'see_rename'?'':'<td><span class="link-blue" onclick="reRename($(this))">再次重命名</span></td>'
    for (let item of data) {
        str += `<tr mid="${item.mid}" pid="${item.pid}">
                    <td class="name">${item.name}</td>
                    <td class="newName">${item.newName}</td>
                    ${changeStr}
                </tr>`
    }
    $(".tbl_" + name +" tbody").html(str)
}

// creator: 张旭博，2025-04-27 11:49:32， 格式化时间
function formatTime(time, bool) {
    if (bool) {
        return moment(time).format("YYYY-MM-DD HH:mm:ss")
    } else {
        return moment(time).format("YYYY-MM-DD")
    }
}