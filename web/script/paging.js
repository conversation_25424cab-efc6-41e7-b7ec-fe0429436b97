			$("#paging").show();
				var curpage = 1;
				var pageno = 20;
				$("#len").html("共" + window.len + "条");
				var pages = window.len % pageno == 0 ? window.len / pageno : Math.ceil(window.len / pageno);
				$("#cur").html(curpage + " / " + pages);
				$("#con tr:gt(0)").hide();
				var temp = (curpage-1) * pageno;
				$("#con tr:lt(" + (pageno+1) + ")").show();
				$("#nextpage").click(function(){
					curpage++;
					if(curpage <= pages)
						$("#cur").html(curpage + " / " + pages);
					else
					{
						curpage--;
						return;
					}
						
					$("#con tr:gt(0)").hide();
					
					temp = (curpage-1) * pageno;
					$("#con tr:lt(" + (temp + pageno + 1) + ")").show();
					$("#con tr:lt(" + (temp + 1) + ")").hide();
					$("#con tr:eq(0)").show();
				});
				$("#prepage").click(function(){
					curpage--;
					if(curpage >= 1)
						$("#cur").html(curpage + " / " + pages);
					else
					{
						curpage++;
						return;
					}
						
					$("#con tr:gt(0)").hide();
					
					temp = (curpage-1) * pageno;
					$("#con tr:lt(" + (temp + pageno + 1) + ")").show();
					$("#con tr:lt(" + (temp + 1) + ")").hide();
					$("#con tr:eq(0)").show();
				});
				
				$("#firstpage").click(function(){
					curpage = 1;
					$("#con tr:gt(0)").hide();
					$("#con tr:lt(" + (pageno + 1) + ")").show();
					$("#cur").html(1 + " / " + pages);
				});
				
				$("#endpage").click(function(){
					curpage = pages;
					$("#con tr:gt(0)").hide();
					temp = (curpage-1) * pageno;
					$("#con tr:lt(" + (temp + pageno + 1) + ")").show();
					$("#con tr:lt(" + (temp + 1) + ")").hide();
					$("#cur").html(curpage + " / " + pages);
					$("#con tr:eq(0)").show();
				});