// creator: 李玉婷，2019-05-5 10:56:06, amountA = 待分配的金额, amountB = 已有订单录入的金额的和, amountC = 剩余带分派的金额
var amountA = 0, amountB = 0, amountC = 0;
var customerInfo = {};
var editOrd = {};
$(function(){
    var detailId = GetUrlQuery('id');
    getAllocated(detailId);
})
// creator: 李玉婷，2019-05-05 10:55:07，获取待分配回款详情
function getAllocated(id){
    $.ajax({
        url: '../salesBack/collectDisposalDetail.do',
        data: {
            "id": id
        },
        success: function (data) {
            var detail = data.slCollectApplication;
            var orders = data.orders;
            var val = Number(detail.method);
            //method：1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            amountA = amountC = detail.amount;
            $("#mngCuster").html(data.customerName);
            $("#mngAmount").html(formatMoney(detail.amount));
            $("#collected").html('0');
            $("#noneCollect").html(formatMoney(detail.amount));
            customerInfo.customerName = data.customerName;
            customerInfo.code = data.customerCode;
            customerInfo.customer = detail.customer;
            customerInfo.payId = detail.id;
            switch(val){
                case 1 : // 现金
                    $("#mngIncome").html('现金');
                    $("#mngCashGetDate").html(new Date(detail.receiveDate).format('yyyy/MM/dd'));
                    $(".cashType").show();
                    $(".bankType").hide();
                    $(".billType").hide();
                    $(".checkType").hide();
                    break ;
                case 5 : // 银行转账
                    $("#mngIncome").html('银行转账');
                    $("#financeReceiveDate").html(new Date(detail.expireDate).format('yyyy/MM/dd'));
                    $("#financeReceiveBank").html(detail.bankName);
                    $(".cashType").hide();
                    $(".bankType").show();
                    $(".billType").hide();
                    $(".checkType").hide();
                    break ;
                case 4 : // 承兑汇票
                    $("#mngIncome").html('承兑汇票');
                    $("#mngBlSn").html(detail.returnNo);
                    $("#mngBlReciveDate").html(new Date(detail.receiveDate).format('yyyy/MM/dd'));
                    $("#mngBlDueDate").html(new Date(detail.expireDate).format('yyyy/MM/dd'));
                    $("#mngBlUnit").html(detail.originalCorp);
                    $("#mngBlBank").html(detail.bankName);
                    $(".cashType").hide();
                    $(".bankType").hide();
                    $(".billType").show();
                    $(".checkType").hide();
                    break ;
                case 3 : // 转账支票
                    $("#mngIncome").html('转账支票');
                    $("#mngCkReciveDate").html(new Date(detail.receiveDate).format('yyyy/MM/dd'));
                    $("#mngCkSn").html(detail.returnNo);
                    $("#mngCkDueDate").html(new Date(detail.expireDate).format('yyyy/MM/dd'));
                    $("#mngCkUnit").html(detail.originalCorp);
                    $("#mngCkBank").html(detail.bankName);
                    $(".cashType").hide();
                    $(".bankType").hide();
                    $(".billType").hide();
                    $(".checkType").show();
                    break ;
                default:
                    break;
            }
            if(orders && orders.length>0){
                var html = '';
                $(".manageTip").html('请根据实际情况，在某订单/合同后的“本笔回款中应属于该订单/合同的金额”栏内录入金额。');
                for(var t=0;t<orders.length;t++){
                    var percent = orders[t].collectedPercent === 0 ? 0:orders[t].collectedPercent*100;
                    var collectedMoney = orders[t].amount === null ? 0:orders[t].amount;
                    var maxInput = Number(orders[t].contractAmount) - Number(collectedMoney);
                    html +=
                        '<tr oderId="'+ orders[t].id +'" customer="'+ orders[t].customer +'">' +
                        '    <td>'+ setNull(orders[t].sn) +'</td>' +
                        '    <td>'+ formatMoney(orders[t].contractAmount) +'</td>' +
                        '    <td val="'+ orders[t].collectedPercent +'">'+ percent.toFixed(2) +'%/' + formatMoney(collectedMoney) + '</td>' +
                        '    <td><input type="text" placeholder="请输入金额" style="border:none;" onkeyup="changePay($(this))" data-max="'+ maxInput +'" disPay="'+ detail.amount +'" /></td>' +
                        '</tr>';
                }
                $(".listPay tbody").html(html);
                $(".smallPanel").show();
            }else{
                $(".manageTip").html('回款需从属于订单，目前系统中没有可供选择的订单。');
                $(".listPay").hide();
                $(".smallPanel").hide();
                $(".listPay tbody").html('');
            }
            bounce.show($("#payHandle"))
            $('.collectDealCon').everyTime('0.5s','amountMng',function(){
                var flag = $(".makeAgain .fa-square").length;
                if(!flag && amountC > 0 || amountB > amountA){
                    $("#paySure").prop('disabled',true);
                } else {
                    $("#paySure").prop('disabled',false);
                }
                if(amountC == 0){
                    $(".makeAgain").hide();
                }else{
                    $(".makeAgain").show();
                }
            })
        }
    })
}
// creator: 李玉婷，2019-05-5 10:56:45，分配回款金额
function changePay(obj){
    var allPaert = 0;
    var disPay = Number(obj.attr('disPay'));
    var amountMax = Number(obj.data('max'));
    obj.val(obj.val().replace(/[^\d.]/g,""));  //清除“数字”和“.”以外的字符
    obj.val(obj.val().replace(/^\./g,""));  //验证第一个字符是数字而不是.
    obj.val(obj.val().replace(/\.{2,}/g,".")); //只保留第一个. 清除多余的
    obj.val(obj.val().replace(".","$#$").replace(/\./g,"").replace("$#$","."));
    obj.val(obj.val().replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'));//只能输入两个小数
    if(obj.val().indexOf(".")< 0 && obj.val() !=""){//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        obj.val(parseFloat(obj.val()));
    }
    $(".listPay tbody input").each(function(){
        if($(this).val() !== ''){
            allPaert = (allPaert*100 + Number($(this).val())*100)/100;
        }
    });
    amountB = allPaert;
    amountC = amountA - allPaert;
    if(allPaert > disPay || Number(obj.val()) > amountMax){
        obj.val('');
        changePay(obj);
        bounce_Fixed.show($("#clearInput"));
    }else if(allPaert == disPay){
        $(".smallPanel").hide();
    } else {
        $("#collected").html(formatMoney(amountB));
        $("#noneCollect").html(formatMoney(amountC));
        $(".smallPanel").show();
    }
}
// creator: 李玉婷，2019-05-05 10:57:06，确定-跳往不同的页面
function payOrderType(){
    if(amountC > 0) {
        var val = $('.makeAgain .fa-square').attr('val');
        if(val == '1'){
            $("#newOrder .bonceHead span").html('补发订单');
            $('.customerName').val(customerInfo.customerName);
            $.ajax({
                url: "../sales/getPdCustomerName.do",
                success: function (data){
                    if(data == ""||data == undefined){
                        layer.msg("未获取到数据！")
                    }else{
                        bounce.show($("#newOrder"));
                        var PdCustomerName = data["PdCustomerName"];
                        let cusID = '', cusCode = '', invoiceRequire = '';
                        for(var i in PdCustomerName){
                            if(PdCustomerName[i].fullName == customerInfo.customerName) {
                                cusID = PdCustomerName[i].id;
                                cusCode = PdCustomerName[i].code;
                                invoiceRequire = PdCustomerName[i].invoiceRequire;
                            }
                        }
                        $(".customerName").val(customerInfo.customerName).data("cusId" , cusID);
                        $(".customerName").prop('disabled',true);
                        $(".customerId").val(cusCode);
                        $("#newOrder").data('collect', {area:'somePlace' , type: 3 , collectAmount:$("#noneCollect").html() });//type别=2，=2是订单管理-待补发订单-新增订单
                        $(".invoiceRequire").val(formatInviceRequire(invoiceRequire)).data("invoice", invoiceRequire);
                        //存储该用户下的所有商品信息（调用接口）
                        if(Number(invoiceRequire) > 0 && Number(invoiceRequire) < 4){
                            setOuterCon(cusID, invoiceRequire);
                        }
                        //设置该客户下的所有地址（调用接口）
                        setAddressCon(cusID, 'new');
                        gettProductionPrincipal()
                        $("#OrderReceivedDate").val("") ;
                        $("#ordNo").val("") ;
                        $("#gsBd").html("") ;
                        $(".orderTotal").val(0);
                        $(".newGood").prop("disabled",true).html("选择专属商品");
                        $(".newGoodCommon").prop("disabled",true).html("选择通用型商品");
                        bounce.everyTime('0.5s','newGoodActive',function(){
                            //  必填项全部填上才能高亮按钮
                            let count = 0 ;
                            $("#newOrder .orderDetail [require]").each(function () {
                                let val = $(this).val()
                                if($.trim(val).length === 0){
                                    count++
                                }
                            })
                            if(count === 0){
                                let goodInfo1 = $(".newGood").data("goodInfo")
                                $(".newGood").prop("disabled",false);
                                $(".newGoodCommon").prop("disabled",false);
                                $("#sureNewOrder").prop("disabled",false);
                                // 计算订单总额
                                let sum = 0
                                $("#gsBd tr").each(function(){
                                    let item = $(this).find(".hd").html()
                                    item = item ? JSON.parse(item) : null
                                    if(item){
                                        let price = 0 ;
                                        let invoiceRequire = item.invoiceRequire
                                        let goodNum = item.goodNum
                                        if(invoiceRequire === 1){ // 增专
                                            price = item.priceInfo.unitPrice
                                        }else if(invoiceRequire === 2){
                                            price = item.priceInfo.unitPriceInvoice
                                        }else if(invoiceRequire === 3){ //不开票
                                            price = item.priceInfo.unitPriceNoinvoice
                                        }
                                        sum += goodNum * price
                                    }
                                })
                                $("#newOrder .orderTotal").val(sum.toFixed(2))
                                $("#newOrder .canOrder").show();
                            }else{
                                $("#sureNewOrder").prop("disabled",true);
                                $(".newGood").prop("disabled",true);
                                $(".newGoodCommon").prop("disabled",true);
                                $("#newOrder .canOrder").hide();
                            }

                        });
                    }
                }
            });
        }else if(val == '2' || val == '5'){
            payOrderSure()
        }
    }else if(amountC == 0){
        payOrderSure()
    }
}
// creator: 李玉婷，2019-05-20 10:57:22，设置地址
function setPastAddress(){
    $.ajax({
        url:"../sales/getAddressListByCondition.do",
        data:{"cusId": customerInfo.customer},
        success:function (data) {
            var addrList = data["data"];
            var addrListStr = '<option value="">------- 请选择收货地点 -------</option>';
            if(data === null){
                $(".receiveAddress").html(addrListStr);
            }else{
                if (addrList && addrList.length > 0) {
                    for (var i = 0; i < addrList.length; i++) {
                        addrListStr += '<option value="' + addrList[i].id + '">' + addrList[i].address + '</option>';
                    }
                }
                $(".receiveAddress").html(addrListStr);
            }
        }
    })
}
// creator: 李玉婷，2019-05-29 10:58:01，保存新订单
function temporaryGoods(){
    $("#amountAllocated").removeData("save");
    var contractAmount = $('.orderTotal').val();
    if(contractAmount >= amountC){
        var cusid = customerInfo.customer;
        var cusName = customerInfo.customerName;
        var signDate = $("#OrderReceivedDate").val();
        var hasInvoice = $('.hasInvoice').val();
        var sn = $('.orderNumber').val();
        var collectAmount = amountC; //回款金额
        var save_slOrders = {
            'cusid': cusid,
            'cusName': cusName,
            'signDate': signDate,
            'hasInvoice': hasInvoice,
            'sn': sn,
            'contractAmount': contractAmount,
            'collectAmount': collectAmount,
            "ordersAmount" : contractAmount
        }
        var goodListArr = [] ;
        var str = '';
        $("#newOrder .goodList tbody tr").each(function () {
            var goodInfoJson = {
                "id"		  : $(this).children("td").eq(1).attr("id"), // 商品对照id
                "deliveryDate": $(this).children("td").eq(11).text(),
                "amount"	  : $(this).children("td").eq(10).text(),
                "address"     : $(this).children("td").eq(12).text(),
                "addressId"   : $(this).children("td").eq(12).attr("aid")
            };
            goodListArr.push(goodInfoJson);
        });
        var save = {
            "save_slOrders": JSON.stringify(save_slOrders),
            "save_slOrdersItemList": JSON.stringify(goodListArr)
        };
        $("#amountAllocated").data('save', save);
        if(amountB == 0){
            str +=
                '<tr oderId="" customer="'+ cusid +'">' +
                '    <td>'+ setNull(sn) +'</td>' +
                '    <td>'+ contractAmount +'</td>' +
                '    <td val="0">0%</td>' +
                '    <td>0.00</td>' +
                '    <td>'+ amountA +'</td>' +
                '</tr>';
            $(".manageTip").html('本笔回款属于以下订单：');
            $(".listPay").show();
            $(".listPay tbody").html(str);
        }else{
            str +=
                '<tr oderId="" customer="'+ cusid +'">' +
                '    <td>--</td>' +
                '    <td>'+ contractAmount +'</td>' +
                '    <td val="0">0%</td>' +
                '    <td>0.00</td>' +
                '    <td>'+ amountC +'</td>' +
                '</tr>';
            $(".manageTip").html('您需根据实际情况，在订单后面“本笔回款中应属于该订单的金额”栏内录入数字。');
            $(".listPay tbody").append(str);
            $(".listPay").show();
        }
        amountC = 0;
        $(".smallPanel").hide();
        bounce.cancel();
    }else{
        bounce_Fixed.show($("#errorTip"));
        return false;
    }
}
// creator: 李玉婷，2019-05-31 11:00:07，回款处置提交
function payOrderSure(){
    var param = {
        'id': customerInfo.payId
    };
    var applyArr = [];
    var pay = 0;
    $('.listPay tbody input').each(function(){
        if($(this).val() !== ''){
            var trObj = $(this).parents("tr");
            var json = {
                'id': trObj.attr('oderId'),
                'sn': trObj.children().eq(0).html(),
                'contractAmount': trObj.children().eq(1).html(),
                'collectedPercent': trObj.children().eq(2).attr('val'),
                'amount': trObj.children().eq(3).html(),
                'customer': trObj.attr('customer'),
                'collectAmount': $(this).val()
            }
            applyArr.push(json);
            pay += Number($(this).val());
        }
    });
    if(pay == amountA){
        param.ordersState   = '1';
        applyArr = JSON.stringify(applyArr);
        param.orderJson = applyArr;
    }else {
        var disType = $('.makeAgain .fa-square').attr('val');
        if(disType == '1'){
            var collect = $("#amountAllocated").data('save');
            applyArr = JSON.stringify(applyArr);
            param.orderJson = applyArr;
            param.ordersState   = '2';
            param.save_slOrders = collect.save_slOrders;
            param.save_slOrdersItemList = collect.save_slOrdersItemList;
        }else if(disType == '2'){
            param.ordersState = '3';
            if(applyArr.length>0){
                applyArr = JSON.stringify(applyArr);
                param.orderJson = applyArr;
            }
        }else if(disType == '5'){
            param.overpayPurpose = 1;
            param.overpayment = amountC;
        }
    }
    $.ajax({
        url: '../salesBack/collectDisposalAccept.do',
        data: param,
        success: function (data) {
            var state = data.status;
            if(state == '1'){
                cancelDo();
            }else{
                layer.msg(data.msg);
            }
        }
    });
}
// creator: 李玉婷，2019-07-25 16:04:05，取消回款处置
function cancelDo (){
    bounce.cancel();
    window.parent.hideDetailsPage();
}
// creator: 李玉婷，2019-05-15 11:01:43，立即补发、稍后补发按钮切换
function changeWay (obj) {
    obj.siblings().attr("sym","0");
    if(obj.attr("sym") == '0'){
        $(".makeAgain").attr("sym","0");
        $(".makeAgain i").attr("class","fa fa-square-o");
        obj.attr("sym","1");
        obj.children().attr("class","fa fa-square")
    }
}
// creator: 李玉婷，2019-04-29 10:52:27，处理null
function setNull(str) {
    var result = str == null || str == undefined ? '--':str;
    return result;
}