var userStr = sphdSocket.user.managerCode;
$(function(){
    $.fn.select2.defaults.set("theme", "default");
    initFace();
    $('#in-amount').on('input', function () {
        var num = $(this).val();
        if(num.indexOf(".") !== 0){
            num = num.replace(/[^\d.]/g, "");  //清除“数字”和“.”以外的字符
            num = num.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
            num = num.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            num = num.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
            if (num.indexOf(".") < 0 && num != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
                num = parseFloat(num);
            }
        }else{
            num = "";
        }
        $(this).val(num);
    });
    $(".financeBankType").hide();
    // creator: 李玉婷，2019-04-27 10:43:10，必填项验证
    $('.form_payBack').everyTime('0.5s','updateSalar',function(){
        var company = $("#in-company").val();
        var amount  = $.trim($("#in-amount").val());
        var incomeType = $("#incomeType").val();
        if(company == '0' || company == '' || amount == '' || incomeType == '0') {
            $("#inputSure").prop('disabled',true);
        }else {
            var enable = 0;
            switch(incomeType){ //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
                case '1':
                    if($("#receiveDate").val() == '') enable++;
                    break;
                case '4':
                    $(".billType input").each(function(){
                        if($.trim($(this).val()) == '') enable ++;
                    });
                    break;
                case '3':
                    $(".checkType input").each(function(){
                        if($.trim($(this).val()) == '') enable++;
                    });
                    break;
                case '5':
                    if(userStr == 'finance') {
                        if($("#fcReceiveDate").val() == '' || $.trim($("#fcReceiveBank").val()) == '') { enable ++; }
                    }else {
                        if($("#payBegin").val() == '' || $("#payEnd").val() == '') { enable ++; }
                    }
                    break;
            }
            if(enable) {
                $("#inputSure").prop('disabled',true);
            }else {
                $("#inputSure").prop('disabled',false);
            }
        }

    })
});
// creator: 李玉婷，2021-07-02 14:13:38，客户名称列表获取
function getCompany(){
    $.ajax({
        url: '../invoice/getWarrantCustomerByUser.do',
        data: '',
        success: function (data) {
            var resData = data.data;
            var options = [];
            for (var i = 0, len = resData.length; i < len; i++) {
                var option = {
                    "id": resData[i]["customerId"],
                    "text": resData[i]["fullName"]
                };
                options.push(option);
            }
            $("#in-company")  .select2({
                placeholder: "请选择",
                data:options
            })
        }
    })
}
// creator: 李玉婷，2019-05-27 10:45:33，待补充汇款订单
function seePayBackDetail (obj) {
    $('.orderAgain tbody').html('');
    var param = {
        'id': obj.parents("tr").attr('val')
    }
    $.ajax({
        url: '../sale/getCollectDetailsById.do',
        data: param,
        success: function (data) {
            var info = data.data[0];
            var orderList = data.orders;
            var incomeType = info.method;
            $(".collectNameSl").html(info.name);
            $(".collectAmontSl").html(formatMoney(info.amount));
            $(".recordInputer").html(info.create_name);
            $(".recordInputerTime").html(new Date(info.create_date).format('yyyy/MM/dd hh:mm:ss'));
            $(".recordFinance").html(info.financer_name);
            $(".recordFinanceTime").html(new Date(info.approval_date).format('yyyy/MM/dd hh:mm:ss'));
            $(".recordSale").html(info.so_create_name);
            $(".recordSaleTime").html(new Date(info.update_date).format('yyyy/MM/dd hh:mm:ss'));

            switch (incomeType) { //'回款方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐'
                case '1':
                    $(".collectTypeSl").html('现金');
                    $(".cashReciveDateSl").html(new Date(info.receive_date).format('yyyy/MM/dd'));
                    $(".cashTypeSl").show();
                    $(".chequeTypeSl").hide();
                    $(".billTypeSl").hide();
                    $(".bankTypeSl").hide();
                    break;
                case '3':
                    $(".collectTypeSl").html('转账支票');
                    $(".chequeSnSl").html(info.return_no);
                    $(".chequeBankSl").html(info.bank_name);
                    $(".chequeUnitSl").html(info.original_corp);
                    $(".chequeDueDateSl").html(new Date(info.expire_date).format('yyyy/MM/dd'));
                    $(".chequeReciveDateSl").html(new Date(info.receive_date).format('yyyy/MM/dd'));
                    $(".cashTypeSl").hide();
                    $(".chequeTypeSl").show();
                    $(".billTypeSl").hide();
                    $(".bankTypeSl").hide();
                    break;
                case '4':
                    $(".collectTypeSl").html('承兑汇票');
                    $(".billSnSl").html(info.return_no);
                    $(".billDueBankSl").html(info.bank_name);
                    $(".billDueUnitSl").html(info.original_corp);
                    $(".billDueDateSl").html(new Date(info.expire_date).format('yyyy/MM/dd'));
                    $(".billReceiveDateSl").html(new Date(info.receive_date).format('yyyy/MM/dd'));
                    $(".cashTypeSl").hide();
                    $(".chequeTypeSl").hide();
                    $(".billTypeSl").show();
                    $(".bankTypeSl").hide();
                    break;
                case '5':
                    $(".collectTypeSl").html('银行转账');
                    $(".cashTypeSl").hide();
                    $(".chequeTypeSl").hide();
                    $(".billTypeSl").hide();
                    $(".bankReceiveDate").html(new Date(info.expire_date).format('yyyy/MM/dd'));
                    $(".bankReceiveBank").html(info.bank_name);
                    $(".bankTypeSl").show();
                    break;
                default:
                    break;
            }
            if(orderList && orderList.length>0){
                var orderStr = '', disedAmount = 0;
                for(var x=0;x<orderList.length;x++){
                    disedAmount += orderList[x].item_amount;
                    var percent = orderList[x].collected_percent * 100
                    orderStr +=
                        '<tr>' +
                        '    <td>'+ handleNull(orderList[x].sn) +'</td>' +
                        '    <td>'+ formatMoney(orderList[x].contract_amount) +'</td>' +
                        '    <td>'+ percent.toFixed(2) +'%</td>' +
                        '    <td>'+ formatMoney(orderList[x].soc_item_amount) +'</td>' +
                        '    <td>'+ formatMoney(orderList[x].item_amount) +'</td>' +
                        '</tr>';
                }
                var lastAmount = info.amount - disedAmount;
                $("#disedAmount").html(formatMoney(disedAmount));
                $("#lastAmount").html(formatMoney(lastAmount));
                $(".orderAgain tbody").html(orderStr);
                $(".haveOrgOrders").show();
            }else{
                $(".haveOrgOrders").hide();
                $(".orderAgain tbody").html('');
            }
            bounce.show($("#noSupplyingOrder"));
        }
    });
}
// creator: 李玉婷，2019-04-26 10:54:22，回款录入- 切换收入方式
function changeIncomeType( obj ){
    var val = Number(obj.val());
    switch(val){
        case 1 : // 现金
            $(".cashType").show();
            $(".bankType").hide();
            $(".billType").hide();
            $(".checkType").hide();
            $(".financeBankType").hide();
            break ;
        case 5 : // 银行转账
            if(userStr == 'finance') {
                $.ajax({
                    url: '../reimburseWindow/getAccounts.do',
                    data: {
                        'oid': sphdSocket.user.oid
                    },
                    success: function (data) {
                        var accounts = data.data;
                        var options = '<option value="">请选择</option>';
                        if (accounts && accounts.length > 0) {
                            for (var t=0; t<accounts.length; t++){
                                let bankNameStr = accounts[t]["name"] + ' ' + formatAccount(accounts[t]["account"])+ ' ' + accounts[t]["bankName"] ;
                                if(accounts[t].isPublic == 1){
                                    bankNameStr = formatAccount(accounts[t]["account"])+ ' ' + accounts[t]["bankName"] ;
                                }
                                options +=
                                    '<option value="'+ accounts[t].id +'">'+ bankNameStr +'</option>';
                            }
                        }
                        $('#fcReceiveBank').html(options);
                    }
                });
                $(".cashType").hide();
                $(".bankType").hide();
                $(".billType").hide();
                $(".checkType").hide();
                $(".financeBankType").show();
            }else {
                $(".cashType").hide();
                $(".bankType").show();
                $(".billType").hide();
                $(".checkType").hide();
                $(".financeBankType").hide();
            }
            break ;
        case 4 : // 承兑汇票
            $(".cashType").hide();
            $(".bankType").hide();
            $(".billType").show();
            $(".checkType").hide();
            $(".financeBankType").hide();
            break ;
        case 3 : // 转账支票
            $(".cashType").hide();
            $(".bankType").hide();
            $(".billType").hide();
            $(".checkType").show();
            $(".financeBankType").hide();
            break ;
        default:
            $(".cashType").hide();
            $(".bankType").hide();
            $(".billType").hide();
            $(".checkType").hide();
            $(".financeBankType").hide();
            break;
    }
}
// create:hxz 2022-03-07 格式化 账号
function formatAccount(account){
    let accountStr = `****`
    if(account.length >3){
        accountStr += account.substr(account.length-4,4)
    }else{
        let n = 4-account.length ;
        for(let i = 0 ; i < n; n++){
            accountStr += '*'
        }
        accountStr += account
    }
    return accountStr
}
// creator: 李玉婷，2019-04-29 10:46:35，回款录入确定
function payInputSure(){
    var customer   = $("#in-company").val();    //客户id
    var url = '';
    var incomeType = $("#incomeType").val();
    var amount     = $.trim($("#in-amount").val());            //回款金额
    //String method           //回款方式   1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐',
    var params = {
        'amount': amount,
        'customer': customer,
        'method': incomeType
    };
    switch (incomeType) {
        case '1':
            params.receiveDate = $("#receiveDate").val();
            break;
        case '3':
            params.bankName  = $.trim($("#checkBank").val());
            params.returnNo  = $.trim($("#checkNo").val());
            params.expireDate  = $("#checkDueDate").val();
            params.receiveDate  = $("#checkReceiveDate").val();
            params.originalCorp  = $.trim($("#checkSendUnit").val());
            break;
        case '4':
            params.bankName = $.trim($("#sendBank").val());
            params.returnNo = $.trim($("#billNo").val());
            params.expireDate = $("#billDueDate").val();
            params.receiveDate = $("#receiveBillDate").val();
            params.originalCorp = $.trim($("#sendUnit").val());
            break;
        case '5':
            if(userStr == 'finance') {
                let bankId = $("#fcReceiveBank").val()
                let bankName = '';
                $("#fcReceiveBank option").each(function(){
                    if (bankId === $(this).attr("value")){
                        bankName = $(this).html();
                    }
                })
                params.bankName = bankName;
                params.financeAccountId = bankId;
                params.expireDate =$("#fcReceiveDate").val();
            }else {
                params.expectBeginDate =$("#payBegin").val();
                params.expectEndDate = $("#payEnd").val();
            }
            break;
    }
    if(userStr == 'finance') {
        url = '../salesBack/financialInput.do'; // 财务
    }else {
        url = '../salesBack/notFinancialInput.do'; // 非财务
    }
    $.ajax({
        url: url,
        data: params,
        success: function (data) {
            var state = data.status;
            if(state == '1') {
                layer.msg("录入成功！");
                initFace();
            }else {
                layer.msg("录入失败，请重试！");
            }
        }
    })
}
// creator: 李玉婷，2019-05-31 10:49:24， 待补发订单列表
function supplyOrderBtn(){
    $('.supplyAgain').show().siblings().hide();
    $.ajax({
        url: '../sale/reOrders.do',
        data: {
            'oid': sphdSocket.user.oid
        },
        success: function (data) {
            var list = data.data;
            if(list && list.length>0){
                var html = '';
                for(var t=0;t<list.length;t++){
                    html +=
                        '<tr val="'+ list[t].id +'">' +
                        '    <td>'+ list[t].fullName +'</td>' +
                        '    <td>'+ formatMoney(list[t].amount-list[t].item_amount) +'</td>' +
                        '    <td>'+ handleNull(list[t].create_name) + new Date(list[t].create_date).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    '    <td>'+ handleNull(list[t].financer_name) + new Date(list[t].finance_time).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    '    <td>'+ handleNull(list[t].saler_name) + new Date(list[t].saler_time).format('yyyy/MM/dd hh:mm:ss') +'</td>' +
                    '    <td class="ty-td-control">' +
                    '        <span class="ty-color-blue" onclick="collectOrderBtn($(this))">补发订单</span>' +
                    '        <span class="ty-color-blue" onclick="seePayBackDetail($(this))">查看</span>' +
                    '        <span class="hd">'+ JSON.stringify(list[t]) +'</span>' +
                    '    </td>' +
                    '</tr>';
                }
                $('.supplyAgain tbody').html(html);
            }else{
                $('.supplyAgain tbody').html('');
            }
        }
    })
}
// creator: 李玉婷，2019-06-6 10:51:28，待补发订单
function collectOrderBtn(obj){
    if (chargeRole("超管")) {
        $("#msTips .msTip").html("您没有此权限！");
        bounce.show($("#msTips"));
    }else{
        var info = obj.siblings('.hd').html();
        info = JSON.parse(info);
        var item_amount = Number( (info.item_amount && info.item_amount ) || "0" );
        var dealing = info.amount - item_amount;
        var collect = {
            'type': 2,
            'collectId': info.id,
            'collectAmount': dealing
        }
        $("#newOrder .bonceHead span").html('补发订单');
        $("#newOrder").data('collect', collect);
        $.ajax({
            url: "../sales/getPdCustomerName.do",
            success: function (data){
                if(data == ""||data == undefined){
                    alert("未获取到数据！")
                }else{
                    bounce.show($("#newOrder"));
                    var PdCustomerName = data["PdCustomerName"];
                    let cusID = '', cusCode = '', invoiceRequire = '';
                    for(var i in PdCustomerName){
                        if(PdCustomerName[i].name == info.name) {
                            cusID = PdCustomerName[i].id;
                            cusCode = PdCustomerName[i].code;
                            invoiceRequire = PdCustomerName[i].invoiceRequire;
                        }
                    }
                    // $(".customerName").html( `<option code="${cusCode}" value="${cusID}" selected>${info.fullName}</option>`).val(cusID);
                    $(".customerName").val(info.fullName).data("cusId" , cusID);
                    $(".customerName").prop('disabled',true);
                    $(".customerId").val(cusCode);
                    //存储该用户下的所有商品信息（调用接口）
                    if(Number(invoiceRequire) > 0 && Number(invoiceRequire) < 4){
                        setOuterCon(cusID, invoiceRequire);
                    }
                    //设置该客户下的所有地址（调用接口）
                    setAddressCon(cusID, 'new');
                    gettProductionPrincipal()
                    $("#OrderReceivedDate").val("") ;
                    $("#ordNo").val("") ;
                    $("#gsBd").html("") ;
                    $(".orderTotal").val(0);
                    $(".newGood").prop("disabled",true).html("选择专属商品");
                    $(".newGoodCommon").prop("disabled",true).html("选择通用型商品");
                    bounce.everyTime('0.5s','newGoodActive',function(){
                        //  必填项全部填上才能高亮按钮
                        let count = 0 ;
                        $("#newOrder .orderDetail [require]").each(function () {
                            let val = $(this).val()
                            if($.trim(val).length === 0){
                                count++
                            }
                        })
                        if(count === 0){
                            let goodInfo1 = $(".newGood").data("goodInfo")
                            if(goodInfo1 && goodInfo1.length > 0){
                                $(".newGood").prop("disabled",false);
                            }else {
                                $(".newGood").prop("disabled",true);
                            }
                            let goodInfo2 = $(".newGoodCommon").data("goodInfo")
                            if(goodInfo2 && goodInfo2.length > 0){
                                $(".newGoodCommon").prop("disabled",false);
                            }else {
                                $(".newGoodCommon").prop("disabled",true);
                            }
                            $("#sureNewOrder").prop("disabled",false);
                            // 计算订单总额
                            let sum = 0;
                            $("#gsBd tr").each(function(){
                                let item = $(this).find(".hd").html()
                                item = item ? JSON.parse(item) : null
                                if(item){
                                    let price = 0 ;
                                    let invoiceRequire = item.invoiceRequire
                                    let goodNum = item.goodNum
                                    if(invoiceRequire === 1){ // 增专
                                        price = item.priceInfo.unitPrice
                                    }else if(invoiceRequire === 2){
                                        price = item.priceInfo.unitPriceInvoice
                                    }else if(invoiceRequire === 3){ //不开票
                                        price = item.priceInfo.unitPriceNoinvoice
                                    }
                                    sum += goodNum * price
                                }
                            })
                            $("#newOrder .orderTotal").val(sum.toFixed(2))
                            $("#newOrder .canOrder").show();
                        }else{
                            $("#sureNewOrder").prop("disabled",true);
                            $(".newGood").prop("disabled",true);
                            $(".newGoodCommon").prop("disabled",true);
                        }

                    });
                }
            }
        });
    }
}
// creator: 李玉婷，2019-04-29 10:52:27，处理null
function handleNull(str) {
    var result = str == null || str == null || str == undefined ? '--':str;
    return result;
}
// creator: 李玉婷，2019-04-29 10:51:58，处理收入方式
function incomeTypeStr(type) {
    var result = '';
    switch (type) {
        case '1':
            result = '现金';
            break;
        case '2':
            result = '现金支票';
            break;
        case '3':
            result = '转帐支票';
            break;
        case '4':
            result = '承兑汇票';
            break;
        case '5':
            result = '银行转帐';
            break;
    }
    return result;
}
// creator: 李玉婷，2019-05-5 10:53:03，初始化
function initFace(){
    $('#in-company').html(`<option value="0">请选择</option>`);
    getCompany();
    $(".form_payBack select").val("0");
    $(".form_payBack input").val('');
    $(".cashType").hide();
    $(".bankType").hide();
    $(".billType").hide();
    $(".checkType").hide();
    $(".financeBankType").hide();
}

// creator: 李玉婷，2019-04-26 10:53:23，时间插件设置
laydate.render({elem: '#receiveDate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#payBegin',format: 'yyyy/MM/dd'});
laydate.render({elem: '#payEnd',format: 'yyyy/MM/dd'});
laydate.render({elem: '#receiveBillDate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#billDueDate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#checkReceiveDate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#checkDueDate',format: 'yyyy/MM/dd'});
laydate.render({elem: '#fcReceiveDate',format: 'yyyy/MM/dd'});
