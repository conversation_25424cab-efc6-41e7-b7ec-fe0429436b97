/*
* created by hxz for openManage
* */
$(function () {
    getStartManages();
    // table里面的按钮
    $("table").on("click" , ".ctrlBtn" , function () {
        var type = $(this).data("type");
        var info =  $(this).siblings('.hd').html();
        switch (type){
            case "sendSysMeg":
                sendMeg(1 , info);
                break;
            case "sendMobileMeg":
                sendMeg(2 , info);
                break;
            default:

        }
    })
})
// creater : hxz 2020-07-21 获取列表
function getStartManages() {
    getHostTime(function (hosttime) { //与当前时间作比较
        console.log(hosttime)
        var dayStr = new Date(hosttime).format("yyyy年MM月dd日");
        var week =new Date(hosttime).getDay(), weekStr = "";
        if (week == 0) {
            weekStr = "星期日";
        } else if (week == 1) {
            weekStr = "星期一";
        } else if (week == 2) {
            weekStr = "星期二";
        } else if (week == 3) {
            weekStr = "星期三";
        } else if (week == 4) {
            weekStr = "星期四";
        } else if (week == 5) {
            weekStr = "星期五";
        } else if (week == 6) {
            weekStr = "星期六";
        }
        $("#TimeState").html("今天是"+ dayStr + " " + weekStr);
    });
    $.ajax({
        url:"../startManage/getStartManages.do",
        success:function(res){
            var list = res['data'], str = "";
            if(list && list.length > 0){
                for (var i = 0 ; i < list.length ; i++){
                    var item = list[i];
                    str += "<tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ charge(item['state'] , 'state') +"</td>" +
                        "    <td>"+ (item['userName'] || "暂无") +"</td>" +
                        "    <td>" +
                        "       <span class='ty-color-blue ctrlBtn' data-type='sendSysMeg' >发系统消息</span>" +
                        "       <span class='ty-color-blue ctrlBtn' data-type='sendMobileMeg'>发手机短信</span>" +
                        "       <span class='hd'>"+ JSON.stringify(item) +"</span>"+
                        "     </td>" +
                        "</tr>"
                }
            }
            $("#list tbody").html(str);
        },
        error:function(){
            // alert("错误")
        } ,
        complete:function(){ loading.close() ;  }
    });
}
// creater : hxz 2020-07-21  发送消息
function charge(state , type) {
    var str = "";
    switch (type){
        case 'state':
            if(state == 1){
                str = "已设置";
            }else if(state == 0){
                str = "未设置";
            }
            break;
    }
    return str ;
}
// creater : hxz 2020-07-21  发送消息
function sendMeg(messageType , info) {
    // userId 登录人id，itemId 项目id，messageType 1- 系统消息 2-手机短信
    var userId = sphdSocket.user.userID ;
    var itemId = JSON.parse(info).id ;
    var messageType = messageType ;
    $.ajax({
        url:"../startManage/sendMessageToCoreUser.do",
        data:{ "userId":userId , "itemId":itemId , "messageType":messageType ,  },
        success:function(res){
            var data = res['data'];
            var state = data['state'];
            var cont = data['cont'];
            $("#tip .bonceCon").html("<p>"+ cont +"</p>")
            bounce.show($("#tip"));
        },
        error:function(){
            // alert("错误")
        } ,
        complete:function(){ loading.close() ;  }
    });
}


