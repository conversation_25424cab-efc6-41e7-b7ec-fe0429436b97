/**
 * Created by Administrator on 2017/5/10.
 */
/* creator：张旭博，2017-04-27 08:28:29，页面初始化以及事件绑定 */
jQuery.fn.treeItemSlide = function (type) {
    return $(this).each(function(){
        var iconNode = $(this).children('i').eq(0)
        if (!iconNode.hasClass("ty-fa")) {
            if (type === 'up') {
                return iconNode.removeClass('fa-angle-down').addClass('fa-angle-right')
            } else if (type === 'down') {
                return iconNode.removeClass('fa-angle-right').addClass('fa-angle-down')
            } else if (type === 'toggle') {
                if (iconNode.hasClass("fa-angle-right")) {
                    return iconNode.eq(0).removeClass('fa-angle-right').addClass('fa-angle-down')
                } else {
                    return iconNode.eq(0).removeClass('fa-angle-down').addClass('fa-angle-right')
                }
            }
        }
    });
}
$(function () {
    //第一级目录加载
    firstLevelLoad();
    $(".ty-colFileTree[name='chooseFolder']").on("click", ".ty-treeItem", function () {
        if ($(this).hasClass("trash") || $(this).hasClass("cannotChoose")) {
            $(this).parents(".ty-colFileTree").find(".ty-treeItem").removeClass("ty-treeItemActive")   // 取消之前选中
            $(this).addClass("ty-treeItemActive"); // 设置选中
            return false
        }
        var treeItemThis = $(this)
        var categoryId = $(this).data("id")
        // var url = 'getFolderAndChildFolderManager.do'
        var url = '../mpRes/getMiniProgramDircetionAndChild.do'
        // 处理逻辑
        treeItemThis.parents(".ty-colFileTree").find(".ty-treeItem").removeClass("ty-treeItemActive")   // 取消之前选中
        treeItemThis.addClass("ty-treeItemActive"); // 设置选中
        treeItemThis.treeItemSlide('toggle')        // icon切换（顶部jQuery方法拓展）

        $.ajax({
            url: url,
            data: {"category": categoryId},
            success: function (res) {
                var data = res.data
                if (!data) {
                    $("#mt_tip_ms").html("连接失败，请刷新重试！");
                    bounce.show($("#mtTip"));
                } else {
                    // 渲染返回的信息
                    if (treeItemThis.children().eq(0).hasClass('fa-angle-right')) {
                        treeItemThis.next().remove()
                    } else {
                        showTreeData(data, treeItemThis);
                    }
                }
            }
        })




    })
    // updater : 侯杏哲 2017-11-18 每一级目录绑定事件（添加样式、获取下一级目录、本级详细信息、子级文件夹内容）
    $(".ty-colFileTree[name='main']").on("click",".ty-treeItem",function (){
        //添加文件夹选中样式
        $(".ty-treeItem").removeClass("ty-treeItemActive");
        $(this).addClass("ty-treeItemActive");
        //点击文件夹箭头方向的切换
        if($(this).find("i").eq(0).hasClass("fa-caret-right")){
            $(this).find("i").eq(0).removeClass("fa-caret-right") ;
            $(this).find("i").eq(0).addClass("fa-caret-down");
        }else if($(this).find("i").eq(0).hasClass("fa-caret-down")){
            $(this).find("i").eq(0).removeClass("fa-caret-down") ;
            $(this).find("i").eq(0).addClass("fa-caret-right") ;
        }
        if ($(this).attr('flag') == '0'){
            firstLevelLoad();
        }else{
            //设置选中文件夹详细内容的头部名称
            $(".headPanel").children("h3").html($(this).text());
            $(".normalName").html($(this).text());
            var btnGroup = ''
            if ($(this).hasClass("trash")) {
                btnGroup =  '' ;
            } else {
                var isLevel1 = $(this).parent().parent().hasClass("level1")
                var isFix = $(this).parent().data("category") === 1
                btnGroup =
                    // '<span class="fa-stack icon-de spaceGap" onclick="scanSetBtn()" title="使用权限设置">' +
                    // '    <i class="fa fa-square-o fa-stack-2x"></i>' +
                    // '    <i class="fa fa-cog fa-stack-1x"></i>' +
                    // '</span>' +
                    '<i class="fa fa-plus spaceGap" onclick="newClass()" title="新建子文件夹"></i>' +
                    (isFix?'':'<i class="fa fa-edit spaceGap" onclick="changeClass()" title="修改文件夹名称"></i>') +
                    (isFix?'':'<i class="fa fa-file-text-o spaceGap" onclick="folderEditRecord()" title="文件夹名称修改记录"></i>') +
                    // (isLevel1 ||isFix?'':'<i class="fa fa-arrows spaceGap" onclick="moveClass()" title="移动"></i>') +
                    (isFix?'':'<i class="fa fa-trash-o spaceGap deleteFoleder" onclick="deleteClass()" title="删除"></i>') ;
            }
            $(".operableBtn").html(btnGroup);
            //获取子级文件夹内容
            var categoryId = $(this).attr("id") ;
            let pid = $(this).parents(".level").eq(0).prev().attr("id");
            console.log("pid", pid)
            $(".CategoryMessage").data("pid", pid);
            var treeItemThis = $(this) ;
            $.ajax({
                url: "../mpRes/getMiniProgramDircetionAndChild.do",
                data: {"category":categoryId },
                type: "post",
                dataType: "json",
                beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
                success: function (res) {
                    if(res == "" || res == undefined){
                        bounce.show( $("#tip") ) ; $("#tipMess").html( "系统错误，请重试！" ) ;
                    }else {
                        var data = res["data"], //所有数据
                            listNotice = data["parentFolder"], // 点击的那层文件夹的信息
                            listNoticeChild = data["childFolder"],// 点击打那层文件夹的子文件夹的信息

                            level = parseInt(treeItemThis.parent().parent().attr("level")),
                            nextLevel = level + 1,
                            levelStr = '',
                            listNoticeStr = '',   //类别信息字符串
                            createDate = '',
                            updateDate = '';
                        var thisChildFolder = '';
                        //拼接子类别侧边菜单字符串
                        levelStr += '<div class="level level' + nextLevel + '" level="' + nextLevel + '">';
                        for (var i in listNoticeChild) {
                            levelStr += '<li data-category="'+listNoticeChild[i].category+'">' +
                                        '<div class="ty-treeItem '+(treeItemThis.hasClass("trash")?'trash':'')+'" title="' + listNoticeChild[i].name + '" id=' + listNoticeChild[i]["id"] + '>' +
                                        (listNoticeChild[i]["children"] > 0 ?'<i class="fa fa-caret-right"></i>':'<i class="ty-fa"></i>') +
                                        '<i class="fa fa-folder"></i>' +
                                        '<span>' + listNoticeChild[i].name + '</span>' +
                                        '</div>' +
                                        '</li>';

                            thisChildFolder = childFolerList(listNoticeChild);

                        }
                        levelStr += '</div>';
                        $(".childlFolderInfo").html(thisChildFolder);
                        if (treeItemThis.children("i:first").hasClass("fa-caret-down")) {
                            treeItemThis.next().remove();
                            treeItemThis.after(levelStr);
                        } else {
                            treeItemThis.next().remove();
                        }

                        //拼接此类别信息列表字符串
                        var creator = listNotice.category == '1' ? '系统' : listNotice.createName
                        $(".generalFolder").attr("path", listNotice.path)
                        $(".generalFolder").find('td').eq(2).html(creator);
                        $(".generalFolder").find('td').eq(3).html(new Date(listNotice["createDate"]).format('yyyy/MM/dd hh:mm:ss'));
                        if (treeItemThis.find("span").html() === "暂时不用的文件") {
                            $(".operableBtn").html('');
                            $(".cCategoryMessage").hide();
                            $(".generalFolder").find('td').eq(0).html(`${ listNoticeChild.length }个文件夹，有效文件共${listNotice.leafs}个`);
                            return false;
                        } else {
                            $(".nomalTip span").eq(1).html(listNotice.children);
                            $(".generalTip").hide();
                            $(".nomalTip").show();
                            $(".CategoryMessage").attr("id", categoryId);
                            $(".cCategoryMessage").show();
                            $(".generalFolder").find('td').eq(0).html( listNotice.children + '个文件，有效文件共' + listNotice.leafs + '个');
                            if (Number(listNotice["childStatus"]) >0 || Number(listNotice["leafs"]) >0 ) {
                                $('.deleteFoleder').attr("onclick", "deleteClass(1)"); // 不可以删
                            } else {
                                $('.deleteFoleder').attr("onclick", "deleteClass(0)"); // 可以删
                            }
                        }
                        treeItemThis.attr("fileNum", listNotice.leafs)
                    }
                },
                error:function (err) {
                    bounce.show( $("#tip") ) ; $("#tipMess").html( "系统错误，请重试！" ) ;
                } ,
                complete:function(){ chargeClose( 2 ) ;  }
            })
        }
    });
    // creator:侯杏哲 2017-11-18  查看设置的文件夹toggle
    $("#allRight").on("click" , "li>div.departName" , function () {
        changeVal($(this), "left");
    });
    $("#nowRight").on("click" , "li>div.departName" , function () {
        changeVal($(this), "right");
    });
    $(".btnLink").click(function(){
        var type = $(this).data("type");
        if(type == "allClear"){ // 全部清空
            setVal(sysDeparAndUserList, { "havRight":0 } , true)
            $(".selectedNum").html(countSet(sysDeparAndUserList, {'key':"havRight", 'val':1}));
            chargeState();
        }else if(type == "allSelect"){ // 全选
            setVal(sysDeparAndUserList, { "havRight":1}, true)
            $(".selectedNum").html(countSet(sysDeparAndUserList, {'key':"havRight", 'val':1}));
            chargeState();
        }
    })
    $(".mar").on("click", "table [type='btn']", function () {
        var name= $(this).attr("name")
        switch (name) {
            case 'fileSize':
                var categoryPath = $(this).parents("tr").attr("path")
                var type = $(this).attr("fileType")
                var data = {
                    type: type // String 类型 1是获取有效文件 2是获取历史文件
                }
                if (categoryPath) {
                    data.categoryPath = categoryPath
                }
                $.ajax({
                    url: '../res/effectiveFileSize.do',
                    data: data,
                    success: function (res) {
                        var size = res.data.size
                        bounce.show($("#tip"))
                        var str = (type === '1'?'有效文件': '历史文件') + '大小： ' + size
                        $("#tipMess").html(str)
                    }
                })

                break
        }
    })
}) ;
/* updator：侯杏哲 ，2017-12-15 13:46:44，第一级类别加载 */
function firstLevelLoad() {
    $.ajax({
        url: "../mpRes/getMiniProgramInitialDirectory.do" ,
        data: { type : 1  },
        success: function (res) {
            var data = res["data"] ;
            let authFolder = data['authFolder'] ; //  0是没有权限 1是有权限
            if(authFolder === 1){

            }
            // 存储第一级目录数组
            var listFirstCategory = data["listFirstFolder"]; // 一级文件夹列表
            var createDate = '';  // 系统文件夹创建时间
            var childInfoStr = ''; //子级文件夹的信息拼接
            $(".bubu").html(listFirstCategory.length)
            // if(listFirstCategory.length<4){
            //     bounce.show( $("#tip") ) ; $("#tipMess").html( "暂无查看权限！" ) ;
            //     return false ;
            // }
            // 构建文件夹树
            var firstLevelStr = '<ul class="level1" level="0"><li>' +
                                '   <div class="ty-treeItem ty-treeItemActive" title="全部" flag="0">' +
                                '   <i class="ty-fa"></i>' +
                                '   <i class="fa fa-folder"></i>' +
                                '   <span>全部</span></div>'+
                                '   <ul class="level1" level="1">' ;
            for(var i in listFirstCategory){
                //判断每个文件夹是否有子级文件夹  是否来显示左侧箭头

                if (i == 0){
                    createDate = new Date(listFirstCategory[i]["createDate"]).format('yyyy/MM/dd hh:mm:ss');
                }
                firstLevelStr +=    '<li data-category="'+listFirstCategory[i].category+'">' +
                                    '<div class="ty-treeItem '+(listFirstCategory[i].trash?'trash':'')+'" title="'+ listFirstCategory[i]["name"] +'" id='+ listFirstCategory[i]["id"] +' >' +
                                    (listFirstCategory[i].children > 0 ?'<i class="fa fa-caret-right"></i>':'<i class="ty-fa"></i>') +
                                    (listFirstCategory[i].trash?'<i class="fa fa-lock"></i>':'<i class="fa fa-folder"></i>') +
                                    '<span>' + listFirstCategory[i]["name"] + '</span>' +
                                    '</div>' +
                                    '</li>'

                childInfoStr = childFolerList(listFirstCategory);

            }
            firstLevelStr += '</ul></li></ul>';
            $(".ty-colFileTree").html(firstLevelStr) ;

            // 显示一级文件夹详情
            if (authFolder === 2) {
                var btnGroup =
                    '<i class="fa fa-plus spaceGap" style="margin-left:30px;" onclick="newSameClass()" title="新建子文件夹"></i>' ;
                    // '<i class="fa fa-history spaceGap" style="margin-left:30px;" onclick="setRightLog(1)" title="使用权限操作记录"></i>'
            } else {
                var btnGroup =
                    // '<span class="fa-stack icon-de spaceGap" onclick="scanSetBtn()" title="使用权限设置">' +
                    // '    <i class="fa fa-square-o fa-stack-2x"></i>' +
                    // '    <i class="fa fa-cog fa-stack-1x"></i>' +
                    // '</span>' +
                    '<i class="fa fa-plus spaceGap" style="margin-left:30px;" onclick="newSameClass()" title="新建子文件夹"></i>' ;
                    // '<i class="fa fa-history spaceGap" style="margin-left:30px;" onclick="setRightLog(1)" title="使用权限操作记录"></i>'
            }
            $(".headPanel").children("h3").html('全部');
            $(".operableBtn").html(btnGroup);
            $(".generalFolder").attr("path", '')
            // $(".generalFolder").find('td').eq(0).html(data.folderNum +'个文件夹，有效文件共' + data.fileNum  + '个');
            $(".generalFolder").find('td').eq(0).html(`${listFirstCategory.length}个文件夹，有效文件共${ data.fileNum  }个`);
            $(".generalFolder").find('td').eq(2).html('系统');
            $(".generalFolder").find('td').eq(3).html(createDate);
            $(".generalTip span").html(data.folderNum);
            $(".generalTip").show(); $(".nomalTip").hide();
            // 处理子级文件夹详情
            $('.childlFolderInfo').html(childInfoStr);
            $(".cCategoryMessage").show();
        }
    })
}
// creator: 李玉婷，2019-07-17 10:05:48，子级文件夹信息列表
function childFolerList(arr){
    var childInfoStr = '';
    for (var i=0;i<arr.length;i++){
        var creator = arr[i].category == '1'?'系统':arr[i]["createName"];
        var createDate = arr[i]["createDate"]? new Date(arr[i]["createDate"]).format('yyyy/MM/dd hh:mm:ss'):'--';
        childInfoStr +=
            '<tr path="'+arr[i].path+'">' +
            '    <td>' + arr[i]["name"] +'</td>' +
            '    <td>' + arr[i]["children"] +'个文件' + '</td>' +
            '    <td>' +
            '- -' +
            // '       <span class="ty-btn ty-btn-blue" type="btn" fileType="1" name="fileSize">有效文件</span>' +
            // '       <span class="ty-btn ty-btn-blue" type="btn" fileType="2" name="fileSize">历史文件</span>'+
            '    </td>' +
            '    <td>' + handleNull(creator) +'</td>' +
            '    <td>' + createDate +'</td>' +
            '</tr>';
    }
    return childInfoStr;
}
/* creator：张旭博，2017-04-27 08:29:05，点击类别名称 修改 按钮 */
function changeClass() {
    //清空输入框
    var title = $(".ty-treeItemActive>span").text();
    $(".folderNameChange").text(title);
    $(".bounce-changeClass .categoryName").val('');
    $(".bounce-changeClass .categoryName").attr('placeholder', title);
    //显示弹窗
    bounce.show($('.bounce-changeClass'));
}

// creator: 张旭博，2021-07-21 11:07:31，点击-移动-按钮
function moveClass() {
    bounce.show($("#chooseSaveFolder"));
    getFirstDoc()
    // setEveryTime(bounce_Fixed, 'chooseSaveFolder')
}

// creator: 张旭博，2021-07-21 11:57:18，移动 - 确认
function sureMoverFolder() {
    var fromChooseNode = $(".ty-colFileTree[name='main'] .ty-treeItemActive")
    var toChooseNode = $(".ty-colFileTree[name='chooseFolder'] .ty-treeItemActive")
    var oldCategoryId = fromChooseNode.attr("id");
    var newCategoryId = toChooseNode.data("id");
    var node = $(".ty-colFileTree[name='chooseFolder'] .ty-treeItem[data-id='"+oldCategoryId+"']") // 大页面选择的文件夹在移动弹窗中的位置

    // 父级和所有子级做上标记，不能移动到有标记文件夹
    node.parent().parent().prev(".ty-treeItem").removeClass("disabled").addClass("disabled")
    node.parent().find(".ty-treeItem").removeClass("disabled").addClass("disabled")

    if (Number(fromChooseNode.attr("fileNum")) > 500) {
        layer.msg("操作失败！因为系统不支持一次移动500个文件")
        return false
    }
    if(!newCategoryId){
        layer.msg("请选择要移动的文件夹")
        return false
    }
    if ($(".ty-colFileTree[name='chooseFolder'] .ty-treeItemActive").hasClass("disabled")) {
        layer.msg("不能移动到此文件夹")
        return false
    }

    if(toChooseNode.hasClass("trash")) {
        // 某文件夹“移动”至“暂时不用的文件”时，系统给予“其他职工将无法再见到…”的提示。
        $("#bounce_tip .tipMsg").html( "移动后，其他职工将无法再见到该文件夹及里面的文件。<br>确定移动吗？" ) ;
        $("#bounce_tip .sureBtn").one("click", function () {
            judgeMove(oldCategoryId,newCategoryId)
        })
        bounce.show($("#bounce_tip")) ;
    } else {
        judgeMove(oldCategoryId,newCategoryId)
    }
}

function judgeMove(oldCategoryId, newCategoryId) {
    $.ajax({
        url: '../res/clickButtenJudgementMove.do',
        data: {
            oldCategoryId: oldCategoryId,
            newCategoryId: newCategoryId,
            type: 2
        },
        success: function (res) {
            loading.close()
            var data = res.data
            if (data === 1) {
                bounce_Fixed.cancel()
                sureMove(oldCategoryId, newCategoryId)
            } else if (data === 2) {
                layer.msg("无法移动到当前文件夹")
            } else {
                $("#bounce_tip .tipMsg").html("有职工正在借阅该文件夹下的文件。<br>移动后，借阅者的借阅将被终止。<br>确定移动吗？") ;
                $("#bounce_tip .sureBtn").one("click", function () {
                    sureMove(oldCategoryId, newCategoryId)
                })
            }
        },
        complete: function () {

        }
    })
}

function sureMove(oldCategoryId, newCategoryId) {
    $.ajax({
        url: "../res/changeCategoryPlace.do",
        data: {
            oldCategoryId: oldCategoryId,
            newCategoryId: newCategoryId
        },
        success: function (res) {
            var data = res.data
            if  (data === '移动成功') {
                bounce.cancel()
                layer.msg("操作成功")
                window.location.reload()
            }
        }
    })
}
/* creator：张旭博，2017-05-04 13:16:41，点击修改类别 确认 按钮 */
function sureChangeClass() {
    var categoryId = $(".CategoryMessage").attr("id");
    var parent = $(".CategoryMessage").data("pid");
    var categoryName = $.trim($(".bounce-changeClass .categoryName").val());
    if(categoryName == ""){
        bounce.show( $("#tip") ) ; $("#tipMess").html( "类别名称不能为空" ) ;
    }else{

        // 1 id Integer 目录的id
        // 2 parent Integer 父目录的id
        // 3 name String 新的名字

        $.ajax({
            // url: "../res/UpdateFolderName.do",
            url: "../mpRes/upMiniProgramDirectoryName.do",
            data: {"category": categoryId, "name": categoryName , "parent" : parent },
            success: function (res) {
                if (res.data && res.data["state"] == 1 ) {
                    bounce.cancel(); layer.msg("更改成功") ;
                    $(".bounce-changeClass .categoryName").val("");
                    $(".ty-treeItemActive span").html(categoryName);
                    $(".ty-treeItemActive").attr("title",categoryName);
                    $(".ty-treeItemActive").click();
                    $(".ty-treeItemActive").click();
                } else if (res.data && res.data["state"] == 2) {
                    bounce.show( $("#tip") ) ; $("#tipMess").html("类别名称重复，请更换类别名称！");
                }else{
                    bounce.show( $("#tip") ) ; $("#tipMess").html("操作失败");
                }
            }
        })
    }
}
/* creator：张旭博，2017-04-27 08:29:36，点击类别名称 删除 按钮 */
function deleteClass(num) {
    if(num == '0'){ // 可以删除
        bounce.show($('.bounce-deleteClass'));
    }else{
        var html =
            '<p>删除失败！</p>' +
            '<p>您试图删除有内容的文件夹。</p>' +
            '<p>为防止误操作，本系统仅支持删除空文件夹。</p>';
        $("#tipMess").html(html);
        bounce.show($("#tip"));
    }
}
/* creator：张旭博，2017-05-04 13:17:56，点击删除类别 确认 按钮 */
function sureDeleteClass() {
    var categoryId = $(".CategoryMessage").attr("id");
    $.ajax({
        // url: "../res/delCentreFolder.do",
        url: "../mpRes/delMiniProgramDirectory.do",
        data: {"category":categoryId  },
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (data) {
            if(data["success"] == 0){
                bounce.show( $("#tip") ) ; $("#tipMess").html("不允许删除此类别！");
            }else if(data["success"] == 1){
                let sate = data['data']['state']; //  1是删除成功 2是目录已经删除 3是目录下有东西不能删除
                if(sate == 1){
                    bounce.cancel(); layer.msg("删除成功") ;
                    if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                        location.reload();
                    }
                    if($(".ty-treeItemActive").parent().parent().children().length<2){
                        $(".ty-treeItemActive").parent().parent().prev().children().eq(0).attr("class","ty-fa");
                    }
                    $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                    $(".ty-treeItemActive").click();
                }else if(sate == 2){
                    bounce.cancel(); layer.msg("目录已经删除!") ;
                    if($(".ty-treeItemActive").parent().parent().attr("level") == 1){
                        location.reload();
                    }
                    if($(".ty-treeItemActive").parent().parent().children().length<2){
                        $(".ty-treeItemActive").parent().parent().prev().children().eq(0).attr("class","ty-fa");
                    }
                    $(".ty-treeItemActive").parent().parent().parent().children().eq(0).click();
                    $(".ty-treeItemActive").click();
                }else if(sate == 3){
                    bounce.cancel(); layer.msg("目录下还有文件不能删除 !") ;
                }

            }else{
                bounce.show( $("#tip") ) ; $("#tipMess").html("删除失败！");
            }
        },
        error:function (err) {
            bounce.show( $("#tip") ) ; $("#tipMess").html("系统错误，请重试！");
        } ,
        complete:function(){ loading.close() ;  }
    })
}
/* creator：张旭博，2017-04-27 08:29:49，点击新增子级类别按钮 */
function newClass() {
    var folderName = $(".ty-treeItemActive span").text();
    $(".recentParentFolder").html(folderName);
    //清空输入框
    $("#havRightUserCon").html("");
    $(".bounce-newClass .categoryName").val("");
    //判断该类别下是否有文件
    var categoryId = $(".CategoryMessage").attr("id");
    //显示弹窗
    bounce.show($('.bounce-newClass'));

    // $.ajax({
    //     url: "../res/clickButtenJudgementFileByCentre.do",
    //     data: { "categoryId": categoryId },
    //     success: function (res) {
    //         let data = res['data'];
    //         let status = data["state"]; // 文件夹的状态 有0-3的4种情况 0-文件夹不存在 1-文件夹下什么都没有 2-文件夹下有东西，不能删除 3-文件夹没有权限。
    //         if(status == 0 || status == 3){
    //             let str = '<p>文件夹不存在！</p>' ;
    //             if(status == 3){
    //                 str = '<p>您需要先设置完这个文件夹的使用权限！</p>' ;
    //             }
    //             bounce.show( $("#tip") ) ; $("#tipMess").html(str);
    //         } else{
    //             //显示弹窗
    //             bounce.show($('.bounce-newClass'));
    //             let authCount = data['authCount'];
    //             $(".hasSettedNum").html(authCount);
    //         }
    //     }
    // })
}
/* update:hxz 2020-10-23 点击新增子级类别 确认 按钮 */
function sureNewClass() {
    // let hasSettedNum = $(".bounce-newClass .hasSettedNum").html();
    // if(Number(hasSettedNum) <= 0){
    //     bounce_Fixed.show( $("#tip2") ) ; $("#tipMess2").html("您需要先设置完这个文件夹的使用权限！") ;
    //     return false;
    // }
    var categoryId = $(".CategoryMessage").attr("id");
    var categoryName = $.trim($(".bounce-newClass .categoryName").val());
    var maxChildCategorys = $(".ty-treeItemActive").next().children("li").length;
    if(categoryName == ""){
        bounce.show( $("#tip") ) ; $("#tipMess").html("类别名称不能为空！") ;
    }else{
        // let havRightUser = $("#havRightUserCon").html() ;
        // let users = []
        // if(havRightUser != ""){
        //     havRightUser = JSON.parse(havRightUser);
        //     havRightUser.forEach(function(user){
        //         users.push(user.userID)
        //     })
        // }
        // 1 parent Integer 父级目录id，当新增1级文件夹时此值不用传，新增子级的时候才传
        // 2 categoryName String 新增目录的名字

        $.ajax({
            // url: "../res/insertSameFolder.do",
            url: "../mpRes/addMiniProgramDirectory.do",
            data: {
                parent: categoryId,
                categoryName: categoryName
            },
            success: function (data) {
                if(data == null || data == undefined){
                    bounce.show( $("#tip") ) ; $("#tipMess").html("返回值不存在！") ;
                }else{
                    var status = data['data']["status"]; // 1是新增成功，2是文件夹下有文件不能新增了，3是同级文件夹中有同名
                    if(status == 3){
                        bounce.show( $("#tip") ) ; $("#tipMess").html("类别名称重复，请更换类别名称！") ;
                        $(".bounce-newClass .categoryName").val("");
                    }else if(status == 2){
                        bounce.show( $("#tip") ) ; $("#tipMess").html("文件夹下有文件不能新增了！") ;
                        $(".bounce-newClass .categoryName").val("");
                    }else if(status == 1){
                        bounce.cancel(); layer.msg("新增成功") ;
                        if($(".ty-treeItemActive").children().eq(0).hasClass("ty-fa")){
                            $(".ty-treeItemActive").children().eq(0).attr("class","fa fa-caret-right");
                            $(".ty-treeItemActive").click();
                        }else{
                            $(".ty-treeItemActive").click();
                            $(".ty-treeItemActive").click();
                        }

                    }else{
                        bounce.show( $("#tip") ) ; $("#tipMess").html("未知的返回值错误") ;
                        $(".bounce-newClass .categoryName").val("");
                    }
                }
            }
        })
    }
}
/* creator：hxz，2020-10-17 点击权限历史记录 - 查看 */
function logScanBtn(thisObj,typeNum) {
    let info = JSON.parse(thisObj.siblings(".hd").html()) ;
    // typeNum 1-设置，2-新增，3-取消
    // type String 1是查看新增的2是查看删除的
    let cat = info['category']
    if(Number(cat) > 0){
        $("#logCat").html("文件夹");
    }else{
        $("#logCat").html("文件");
    }
    $.ajax({
        "url":"../res/resAclLogMes.do",
        "data":{"aclId": info.id, "type":typeNum },
        success:function (res) {
            let resAclLog = res['data']["resAclLog"] ;
            let delUser = res['data']["delUser"] ;
            let addUser = res['data']["addUser"] ;
            let usersStr = "", list = [], resultStr = "";
            if(typeNum == 1 ) {
                list = addUser;
                resultStr = "如下" + list.length + "位职工获得了使用权限"
            }else if(typeNum == 2){
                list = delUser;
                resultStr = "如下" + list.length + "位职工取消了使用权限"
            }
            for(let i = 0 ; i < list.length ; i++){
                usersStr += "<p>"+ list[i]['userName'] +"("+ list[i]['mobile'] +")</p>";
            }
            $("#scanSetLogScan .bonceCon .users").html(usersStr);
            $("#scanSetLogScan .result").html(resultStr);
            $("#scanSetLogScan .name").html(resAclLog['name']);
            $("#scanSetLogScan .path").html(resAclLog['path']);
            if(resAclLog['operateType']==1){
                resAclLog['operateTypeStr'] = "设置使用权限";
            }else if(resAclLog['operateType']==2){
                resAclLog['operateTypeStr'] = "修改使用权限";
            }else if(resAclLog['operateType']==3){
                resAclLog['operateTypeStr'] = "删除使用权限";
            }
            $("#scanSetLogScan .operateType").html(resAclLog['operateTypeStr']);
            $("#scanSetLogScan .createName").html(resAclLog['createName'] + (new Date(resAclLog['createDate']).format("yyyy-MM-dd hh:mm:ss")));
            bounce_Fixed.show($("#scanSetLogScan"));
        }
    })
}
/* creator：hxz，2020-10-17 点击权限历史记录 */
function setRightLog(cur) {
    let oid = sphdSocket.org.id;
    $.ajax({
        "url":"../res/getResAclLog.do",
        "data":{"oid": oid, "pageSize":20, "currentPageNo":cur },
        success:function (res) {
            let list = res['data']["listAclLog"]||[],str="";
            let pageInfo = res['data']["pageInfo"]||[] ;
            let totalPage = pageInfo["totalPage"] ;
            let currentPageNo = pageInfo["currentPageNo"] ;
            setPage($("#logYe"), currentPageNo, totalPage, 'setRightLog');
            if(list.length>0){
                for(let i = 0 ; i < list.length; i++){
                    let item = list[i]; // 1-设置权限,2-修改权限,3-删除权限
                    let operateTypeStr = "", result = "";
                    if(item['operateType']==1){
                        operateTypeStr = "设置使用权限";
                        result = item['increasedNum'] + "人获得了使用权限<span class='ty-color-blue' onclick='logScanBtn($(this),1)'>查看</span>";
                    }else if(item['operateType']==2){
                        operateTypeStr = "修改使用权限";
                        if(Number(item['increasedNum']) > 0){
                            result =
                                item['increasedNum'] + "人新获得了使用权限<span class='ty-color-blue' onclick='logScanBtn($(this),1)'>查看</span><br>" ;
                        }
                        if(Number(item['decreasedNum']) > 0){
                            result += item['decreasedNum'] + "人被取消了使用权限<span class='ty-color-blue' onclick='logScanBtn($(this),2)'>查看</span>";
                        }
                    }else if(item['operateType']==3){
                        operateTypeStr = "删除使用权限";
                        if(Number(item['decreasedNum']) > 0){
                            result = item['decreasedNum'] + "人被取消了使用权限<span class='ty-color-blue' onclick='logScanBtn($(this),2)'>查看</span>";
                        }
                    }
                    str += " <tr>" +
                        "    <td>"+ item['name'] +"</td>" +
                        "    <td>"+ operateTypeStr +"</td>" +
                        "    <td class='ty-td-control'>"+ result +"<span class='hd'>"+ JSON.stringify(item) +"</span></td>" +
                        "    <td>"+ item['createName'] + (new Date(item['createDate'] ).format("yyyy-MM-dd hh:mm:ss")) +"</td>" +
                        "    <td>"+ item['path'] +"</td>" +
                        "</tr>";
                }
            }
            $("#rightLog").html(str);
            bounce.show($("#scanSetLog"));
        }
    })

}
/* creator：张旭博，2017-05-09 10:13:27，点击新增同级类别按钮 */
function newSameClass() {
    //清空输入框
    $("#havRightUserCon_same").html("");
    $(".bounce-newSameClass .categoryName").val("");
    //显示弹窗
    $(".bounce-newSameClass .hasSettedNum").html("0");
    bounce.show($(".bounce-newSameClass"));
}
/* creator：张旭博，2017-05-09 10:14:11，点击新增同级类别 确认 按钮 */
function sureNewSameClass() {
    // let hasSettedNum = $(".bounce-newSameClass .hasSettedNum").html();
    // if(Number(hasSettedNum) <= 0){
    //     bounce_Fixed.show( $("#tip2") ) ; $("#tipMess2").html("您需要先设置完这个文件夹的使用权限！") ;
    //     return false;
    // }
    // let havRightUser = getRightUser(sysDeparAndUserList) ;
    // havRightUser.forEach(function(user){
    //     dataStr += "&userID=" + user.userID ;
    // })
    var parentId = $(".ty-treeItemActive .ty-treeItem").eq(0).attr("id");
    if(parentId == undefined){
        parentId = null;
    }
    var categoryName =$.trim($(".bounce-newSameClass .categoryName").val());
    if(categoryName == ""){
        bounce.show( $("#tip") ) ; $("#tipMess").html("类别名称不能为空！") ;
    }else{
        // let users = []
        // let havRightUser = $("#havRightUserCon_same").html() ;
        // if(havRightUser != ""){
        //     havRightUser = JSON.parse(havRightUser);
        //     havRightUser.forEach(function(user){
        //         users.push(user.userID)
        //     })
        // }
        $.ajax({
            // url: "../res/insertSameFolder.do",
            url: "../mpRes/addMiniProgramDirectory.do",
            data: {
                categoryName: categoryName,
                // users:  JSON.stringify(users)
            },
            success: function (res) {
                bounce.cancel();
                var status = res['data']["status"]; // 1新增陈功，2同级文件夹名称重复，3是没有设置文件夹权限
                if(status == 2){
                    bounce.show( $("#tip") ) ; $("#tipMess").html("同级文件夹名称重复!") ;
                    $(".bounce-newSameClass .categoryName").val("");
                }else if(status == 3){
                    bounce.show( $("#tip") ) ; $("#tipMess").html("没有设置文件夹权限!") ;
                    $(".bounce-newSameClass .categoryName").val("");
                }else if(status == 1){
                    if($(".ty-colFileTree").html() === ""){
                        window.location.reload();
                    }else{
                        if($(".ty-treeItemActive:visible").parent().parent().attr("level") == 1){
                            firstLevelLoad("refresh");
                        }else{
                            $(".ty-treeItemActive:visible").parent().parent().parent().children().eq(0).click();
                            $(".ty-treeItemActive:visible").click();
                        }
                    }
                    bounce.cancel(); layer.msg("新增成功") ;
                }else{
                    bounce.show( $("#tip") ) ; $("#tipMess").html( "未知的返回值错误！" ) ;
                    $(".bounce-newSameClass .categoryName").val("");
                }
            }
        })
    }

}
// creator: 李玉婷，2019-07-01 13:55:07，文件夹名称修改记录
function folderEditRecord() {
    var categoryId = $(".CategoryMessage").attr("id");
    var title = $(".ty-treeItemActive>span").text();
    $(".folderNameSee").html(title);
    $.ajax({
        // url : "../res/getCategoryHistory.do" ,
        url : "../mpRes/miniProgramDirectoryHistory.do" ,
        data : { "category" : categoryId } ,
        success:function ( res ) {
            var success = res["success"] ;
            if(success != 1){
                bounce.show( $("#tip") ) ; $("#tipMess").html( "获取数据失败，请刷新重试！" ) ; return false ;
            }
            var html = '';
            var data = res["data"] ;
            var record = data["list"] ;
            if(record.length > 0){
                var lastIndex = record.length -1;
                $(".recordTip").html('该名称为第'+ NumberToChinese(lastIndex) +'次修改后的结果');
                $(".recordEditer").html('修改人： ' + record[lastIndex].createName + ' . ' + (new Date(record[lastIndex].createDate).format("yyyy-MM-dd hh:mm:ss")) );
                for(var a=0;a<record.length;a++){
                    if(a==0){
                        html +=
                            '<tr>' +
                            '    <td>原始信息</td>' +
                            '    <td>'+ record[a].name+'</td>' +
                            '    <td>' + record[a].createName + ' . ' + (new Date(record[a].createDate).format("yyyy-MM-dd hh:mm:ss"))  +'</td>' +
                            '</tr>';
                    }else{
                        html +=
                            '<tr>' +
                            '    <td>第'+ NumberToChinese(record[a].versionNo) +'次修改后</td>' +
                            '    <td>'+ record[a].name+'</td>' +
                            '    <td>' + record[a].createName + ' . ' + (new Date(record[a].createDate).format("yyyy-MM-dd hh:mm:ss")) +'</td>' +
                            '</tr>';
                    }
                }
                $(".historyCon tbody").html(html);
                $(".historyCon").show();
            }else{
                var creater = $('.generalFolder td').eq(2).html();
                var createDate = $('.generalFolder td').eq(3).html();
                $(".recordTip").html('该名称未经修改');
                $(".recordEditer").html('创建人： ' + creater + ' . ' + (new Date(createDate).format("yyyy-MM-dd hh:mm:ss")) );
                $(".historyCon").hide();
            }
            bounce.show($("#nameEditRecord"));
        }
    });
}

// creator: 侯杏哲 2017-12-04  获取一级文件夹列表
function getFirstDoc(){
    $.ajax({
        url: "getInitialFolderByManage.do" ,
        data: { type : 1  },
        success: function (res) {
            var data = res["data"] ;

            // 存储第一级目录数组
            var listFirstCategory = data["listFirstFolder"]; // 一级文件夹列表

            // 构建文件夹树
            var firstLevelStr = '<ul class="level1" level="0"><li>' +
                                '   <div class="ty-treeItem cannotChoose disabled" title="全部" flag="0">' +
                                '   <i class="ty-fa"></i>' +
                                '   <i class="fa fa-folder"></i>' +
                                '   <span>全部</span></div>'+
                                '   <ul class="level1" level="1">' ;
            for(var i in listFirstCategory){
                //判断每个文件夹是否有子级文件夹  是否来显示左侧箭头
                firstLevelStr +=    '<li>' +
                    '<div class="ty-treeItem '+(listFirstCategory[i].isTrash?'trash':'')+'" title="'+ listFirstCategory[i]["name"] +'" data-id='+ listFirstCategory[i]["id"] +' >' +
                    (listFirstCategory[i].childStatus == 1 && !listFirstCategory[i].isTrash?'<i class="fa fa-angle-right"></i>':'<i class="ty-fa"></i>') +
                    (listFirstCategory[i].isTrash?'<i class="fa fa-trash"></i>':'<i class="fa fa-folder"></i>') +
                    '<span>' + listFirstCategory[i]["name"] + '</span>' +
                    '</div>' +
                    '</li>'

            }
            firstLevelStr += '</ul></li></ul>';
            $("#chooseSaveFolder .ty-colFileTree").html(firstLevelStr) ;
        }
    })
}

/* creator: 侯杏哲 2017-11-18 查看设置按钮 */
var userArr = [] ; // 存储查看设置之前的人员
var sysDeparAndUserList = null; // 系统操作一套数据
function scanSetBtn(type){
    // type - "son":新增子级文件夹，"same":新增同级文件夹，不传表示设置已有文件加的权限
    // 超管不能设置查看设置的
    // let userType = chargeRole("超管");
    // if (userType) {
    //     return false;
    // }
    let isNew = type || "";
    $("#isNew").val(isNew);
    let categoryId = $(".ty-treeItemActive").attr("id") ;
    if (!categoryId) {categoryId = 0}
    let packName = $(".ty-treeItemActive").attr("title") ;
    let authtype = 2; // type 1新增文件夹 2正常修改权限
    if(isNew != ""){ // 新增文件夹的
        authtype = 1
        if(isNew === "same"){
            categoryId = 0 ;
            packName = $.trim($(".bounce-newSameClass .categoryName").val());
        }else{
            packName = $.trim($(".bounce-newClass .categoryName").val());
        }
    }
    $(".packName").html(packName);
    $("#getNum").html("")
    $("#cancelNum").html("")
    $("#sureChangeRight").show()
    $("#categoryId").val(categoryId) ;
    $.ajax({
        url : "../res/getForderAuthDepsAndUsers.do" ,
        data : { "categoryId" : categoryId , "type": authtype } ,
        success:function ( res ) {
            var success = res["success"] ;
            if(success != 1){
                bounce.show( $("#tip") ) ; $("#tipMess").html( "获取数据失败，请刷新重试！" ) ; return false ;
            }
            var data = res["data"] ;
            var catAuthTree = data["catAuthTree"]  ;
            sysDeparAndUserList = catAuthTree ;
            let isUpdate = initData(sysDeparAndUserList); // 大于零表示修改
            $("#scanSet").data("isupdate", isUpdate);
            $("#allRight").data("type","depart");
            $("#nowRight").data("type","user");
            let info = {'key':"havRight", 'val':1};
            var count = countSet(sysDeparAndUserList, info);
            $(".selectedNum").html(count);
            chargeState();
            $("#changeTypeLeft").html("直接展示全部职工");
            $("#changeTypeRight").html("切换为按部门展示");
            bounce.show( $("#scanSet") );
        }
    });
}

function chargeState(){
    var leftType = $("#allRight").data("type");
    var rightType = $("#nowRight").data("type");
    if( leftType==="depart"){
        var str1 = renderDataByType(1, sysDeparAndUserList, 1);
        $("#allRight").html(str1) ;
    }else if( leftType==="user"){
        var str1 = renderDataByType(2, sysDeparAndUserList, 1);
        $("#allRight").html(str1) ;
    }
    if( rightType==="depart"){
        var str2 = renderDataByType(1, sysDeparAndUserList, 2);
        $("#allRight").html(str1) ;
        $("#nowRight").html(str2) ;
    }else if( rightType==="user"){
        var str2 = renderDataByType(2, sysDeparAndUserList, 2);
        $("#nowRight").html(str2) ;
    }
    let isupdate = $("#scanSet").data("isupdate");
    if(isupdate > 0){ // 修改的可以展示第二行
        var cancelStr = cancelOrGetStr(sysDeparAndUserList, 1);
        var getStr = cancelOrGetStr(sysDeparAndUserList, 2);
        if(cancelStr.length < 10 && getStr.length < 10){
            $(".getCon").hide();
            $(".cancelCon").hide();
            $(".changeCon").hide();
            $("#cancelNum").html(0)
            $("#getNum").html(0)
        }else {
            $(".changeCon").show();
        }
        if(cancelStr.length > 10){
            let info = {'key':"changeSta", 'val':1};
            let cancelNum = countSet(sysDeparAndUserList, info)
            $("#cancelNum").html(cancelNum);
            $(".cancelCon").show();
            $("#cancelRight").html(cancelStr) ;
        }else{
            $(".cancelCon").hide();
            $("#cancelNum").html(0);
        }
        if(getStr.length > 10){
            let info = {'key':"changeSta", 'val':2};
            let getNum = countSet(sysDeparAndUserList, info)
            $("#getNum").html(getNum);
            $(".getCon").show();
            $("#getRight").html(getStr);
        }else {
            $(".getCon").hide();
            $("#getNum").html(0);

        }
    }else{
        $(".getCon").hide();
        $(".cancelCon").hide();
        $(".changeCon").hide();
        $("#cancelNum").html(0)
        $("#getNum").html(0)
    }
}
function initData(treeData, pName){
    let allCount = 0
    pName = pName || ""
    for(let i = 0 ; i < treeData.length; i++){
        let count = 0
        let deparID = treeData[i]['id']
        let pDepartname = treeData[i]['name']
        let userList = treeData[i]["userList"]; // 人员列表
        let subDeparList = treeData[i]["subList"]; // 部门列表
        let sonPName = pName + pDepartname + ">>"

        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j]['roleCode'] == 1 ;
                userList[j]['deparID'] = deparID ;
                userList[j]['departsName'] = sonPName ;
                userList[j]['havRight'] = isok ? 1 : 0 ;
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += initData(subDeparList, sonPName);
        }
        treeData[i]['count'] = count;
        allCount += count ;
        treeData[i]['leftOpen'] = false;
        treeData[i]['rightOpen'] = false;
    }
    return allCount;
}
// create : hxz 2020-10-16 获取树里某键值对应的 人数
function countSet(treeData, info){
    let key = info['key']
    let val = info['val']
    let allCount = 0 ;
    for(let i = 0 ; i < treeData.length; i++){
        var count = 0
        var userList = treeData[i]["userList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j][key] == val ;
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += countSet(subDeparList, info);
        }
        if(key == 'havRight'){ // 统计是否有权限的人数赋值给部门count键，方便页面读取
            treeData[i]['count'] = count;
        }
        allCount += count;
    }
    return allCount;
}

/* creator : 侯杏哲 2017-12-04 设置权限 - 取消按钮  */
function cancelChangeRight(type){
    let isNew = $("#isNew").val();
    if(isNew != ""){
        if(isNew === "same"){
            bounce.show($(".bounce-newSameClass"));
        }else{
            bounce.show($(".bounce-newClass"));
        }
    }else{
        bounce.cancel()
    }
}
/* creator : 侯杏哲 2020-10-23 设置权限 - 确定按钮  */
function sureChangeRight(type){
    // type 1-表示确定权限设置
    // 判断是否为新增文件夹的
    let isNew = $("#isNew").val();
    if(isNew != ""){
        let havRightUser = getRightUser(sysDeparAndUserList) ;
        $(".hasSettedNum").html(havRightUser.length)
        if(isNew === "same"){
            $("#havRightUserCon_same").html(JSON.stringify(havRightUser));
            bounce.show($(".bounce-newSameClass"));
        }else{
            $("#havRightUserCon").html(JSON.stringify(havRightUser));
            bounce.show($(".bounce-newClass"));
        }
        return false;
    }
    // 判断选中的是否跟原来一样
    let RchangeightNum = diffCharge(sysDeparAndUserList) ;
    if(RchangeightNum == 0){
        let havRightUser = getRightUser(sysDeparAndUserList) ;
        let str = "当前选择的人员与原来完全相同！"
        if(havRightUser.length === 0){
            str = "您还没有选择任何职工呢！"
        }
        bounce.show( $("#tip") ) ; $("#tipMess").html( str ) ;
        return false ;
    }
    // 处理权限设置
    let isUpdate = $("#scanSet").data("isupdate");
    if(Number(isUpdate) > 0 && !type){
        bounce_Fixed.show($("#scanSetTip"));
        let getNum = $("#getNum").html();
        let cancelNum = $("#cancelNum").html();
        let packName = $(".ty-treeItemActive").attr("title") ;
        let tipStr = "您本次";
        if(Number(getNum) > 0){
            tipStr += "新增加的"+ getNum +"位职工有"+ packName +"文件夹的全部使用权限<br>" ;
        }
        if(Number(cancelNum) > 0){
            tipStr += cancelNum +"位职工刚被取消了"+ packName +"文件夹的全部使用权限<br>";
        }
        tipStr += "<p>您可在各下级文件夹中进行限制性设置！</p>";
        $("#scanSetTip .bonceCon").html(tipStr);
        return false;
    }
    // let categoryId = $(".ty-treeItemActive").attr("id") ;
    let dataStr = ''
    var categoryId = $("#categoryId").val()
    if (categoryId && categoryId !== '0') {
        dataStr += 'categoryId=' + categoryId + '&'
    }

    let havRightUser = getRightUser(sysDeparAndUserList) ;
    var userArr = []
    havRightUser.forEach(function(user){
        userArr.push("userID=" + user.userID)
    })
    dataStr += userArr.join('&')
    $.ajax({
        url: "../res/updateFolderAuth.do",
        data: dataStr ,
        success: function (data) {
            var data = data["data"] ;
            if( data === '1' ){
                bounce.cancel();  bounce_Fixed.cancel(); layer.msg("设置成功") ;
                if ($(".ty-treeItemActive:visible").attr("flag") == 0) {
                    window.location.reload()
                }
            } else if (data === '2') {
                bounce.cancel();
                layer.msg("操作失败！已有文件夹被设置了使用权限，已不可再统一设置！")
            } else{
                bounce.show( $("#tip") ) ; $("#tipMess").html( "设置失败！" ) ;
            }
        }
    })
}
// create :hxz 2020-10-13 切换展示员工列表和部门
function changeType(thisObj , location) {
    let type = thisObj.html() == "直接展示全部职工" ? "user" : "depart" ;
    let html = thisObj.html() == "直接展示全部职工" ? "切换为部门展示" : "直接展示全部职工" ;
    thisObj.html(html);
    if(location == "left"){
        $("#allRight").data("type", type);
    }else{
        $("#nowRight").data("type", type);
    }
    chargeState();
}

/*  create :hxz 2020-10-13 渲染一四象限的数据 */
function renderDataByType(type, data, state) {
    // type : 1-按部门展示2-展示全部职工； data：数据； state:1-左边的2-右边的
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var hasKids = false ;
            var leftOpen = data[i]["leftOpen"];
            var rightOpen = data[i]["rightOpen"];
            var userList = data[i]["userList"]; // 人员列表
            var kidList = data[i]["subList"]; // 部门列表
            var itemInfo = { "type":"depart", "id":data[i]["id"] }
            if( (userList && kidList.length > 0) || (userList && userList.length > 0) ){ // 有子级
                hasKids = true ;
                if(type === 1){ // 按部门展示
                    if(state === 1){ // -左边的
                        var cartStr = " fa-caret-right";
                        var showStr = "";
                        if(leftOpen){
                            cartStr = " fa-caret-down";
                            showStr = " style='display:block;'";
                        }
                        strAll += '<li><div class="departName" title="点击该部门后，展示为直属于该部门的子部门或职工">' +
                            '   <i class="fa'+ cartStr +'"></i>' +
                            '   <span>'+ data[i]["name"] +'</span> ' +
                            "   <span class='ctrl' data-type='depart' data-id='"+ data[i]["id"] +"' onclick='plus($(this), event)'>全选</span>" +
                            '</div><ul'+ showStr +'>';
                    }else{ // 右边的
                        var cartStr = " fa-caret-right";
                        var showStr = "";
                        if(rightOpen){
                            cartStr = " fa-caret-down";
                            showStr = " style='display:block;'";
                        }
                        strAll += '<li><div class="departName" title="点击该部门后，展示为直属于该部门的子部门或职工">' +
                            '   <i class="fa'+ cartStr +'"></i>' +
                            '   <span>'+ data[i]["name"] +'</span> ' +
                            "   <span data-id='"+ data[i]["id"] +"' class='ctrl'>已选职工："+ data[i]["count"] +"位</span>" +
                            '</div><ul'+ showStr +'>';
                    }
                }
            }else{ // 无子级
                if(type === 1) { // 按部门展示
                    if(state === 1) { // -左边的
                        var cartStr = " fa-caret-right";
                        if(leftOpen){
                            cartStr = " fa-caret-down";
                        }
                        strAll += '<li><div class="departName" title="点击该部门后，展示为直属于该部门的子部门或职工">' +
                            '   <i class="fa'+ cartStr +'"></i>' +
                            '   <span>'+ data[i]["name"] +'</span> ' +
                            '   <span class="hd">'+ JSON.stringify(data[i]) +'</span> ' +
                            "   <span class='ctrl' data-type='depart' data-id='"+ data[i]["id"] +"' onclick='plus($(this), event)'>全选</span>" +
                            '</div>';
                    }else{ // 右边的
                        var cartStr = " fa-caret-right";
                        if(rightOpen){
                            cartStr = " fa-caret-down";
                        }
                        strAll += '<li><div class="departName" title="点击该部门后，展示为直属于该部门的子部门或职工">' +
                            '   <i class="fa'+ cartStr +'"></i>' +
                            '   <span>'+ data[i]["name"] +'</span> ' +
                            '   <span class="hd">'+ JSON.stringify(data[i]) +'</span> ' +
                            "   <span class='ctrl' data-id='"+ data[i]["id"] +"'>已选职工："+ data[i]["count"] +"位</span>" +
                            '</div>';
                    }
                }
            }
            if(kidList && kidList.length > 0){ // 遍历子级部门
                strAll += renderDataByType(type, kidList, state) ;
            }
            if(userList && userList.length > 0){ // 遍历员工
                for(var j in userList){
                    var itemInfo2 = { "type":"user", "id":userList[j]["userID"] }; // 提示作用
                    let isO = (userList[j]['havRight']===0)
                    if(state === 1) { // -左边的
                        if(isO){ // 没有权限的
                            strAll += '<li data-type="user" title="'+ userList[j]['departsName'] +'" data-departid="'+ userList[j]["deparID"] +'" data-id="'+ userList[j]["userID"]  +'" onclick="plus($(this), event)">' +
                                '<div>' +
                                '   <i class="fa fa-black-tie"></i>' +
                                '   <span>'+ userList[j]["userName"] +'</span> ' +
                                '   <span class="hd">'+ JSON.stringify(userList[j]) +'</span> ' +
                                '</div></li>' ;
                        }
                    } else{ // 右边的
                        if(!isO){ // 有权限的
                            strAll += '<li title="'+ userList[j]['departsName'] +'">' +
                                '<div>' +
                                '   <i class="fa fa-black-tie"></i>' +
                                '   <span>'+ userList[j]["userName"] +'</span> ' +
                                '   <span class="hd">'+ JSON.stringify(userList[j]) +'</span> ' +
                                "   <span data-type='user' data-id='"+ userList[j]["userID"] +"' class='ctrl fa fa-times' onclick='minus($(this), event)'></span>"+
                                '</div></li>' ;
                        }
                    }
                }
            }
            if(hasKids){
                strAll += "</ul></li>" ;
            }else{
                strAll += "</li>" ;
            }
        }

    }
    return strAll ;
}
/*  create :hxz 2020-10-13 渲染二三象限的数据 */
function cancelOrGetStr(data, state) {
    // data：数据； state:1-第二行左边 2-第二行右边
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var userList = data[i]["userList"]; // 人员列表
            var kidList = data[i]["subList"]; // 部门列表
            if(kidList && kidList.length > 0){
                strAll += cancelOrGetStr(kidList, state);
            }
            let method = "minus";
            if(state == 1){ method = "plus"; }
            if(userList && userList.length > 0){
                for(var j in userList){
                    var itemInfo2 = { "type":"user", "id":userList[j]["userID"] };
                    if(state == userList[j]["changeSta"]){
                        strAll += '<li title="'+  userList[j]['departsName'] +'">' +
                            '<div>' +
                            '   <i class="fa fa-black-tie"></i>' +
                            '   <span>'+ userList[j]["userName"] +'</span> ' +
                            "   <span data-type='user' data-id='"+ userList[j]["userID"] +"' class='ctrl fa fa-times' onclick='"+ method +"($(this), event)'></span>"+
                            '</div></li>' ;
                    }
                }
            }
        }
    }
    return strAll ;
}
// create:hxz 2020-10-13 减号
function minus(thisObj,event){
    event.stopPropagation();
    var type = thisObj.data('type');
    var id = thisObj.data('id');
    let info = { "type": type , "id":id, "havRight":0 } ;
    setVal(sysDeparAndUserList, info);
    let info2 = {'key':"havRight", 'val':1};
    var count = countSet(sysDeparAndUserList, info2);
    $(".selectedNum").html(count);
    chargeState();
}
// create:hxz 2020-10-13 加号
function plus(thisObj,event){
    event.stopPropagation();
    var type = thisObj.data('type');
    var departid = thisObj.data('departid');
    var id = thisObj.data('id');
    let info = { "type": type , "id":id, "havRight":1 } ;
    setVal(sysDeparAndUserList, info);
    let info2 = {'key':"havRight", 'val':1};
    var count = countSet(sysDeparAndUserList, info2);
    $(".selectedNum").html(count);
    chargeState();
}
// create:hxz 2020-10-13 设置值
function changeVal(thisObj, location){
    thisObj.next().toggle();
    var departId = thisObj.children(".ctrl").data("id");
    setOpenVal(location,departId, sysDeparAndUserList);
    var iObj = thisObj.children("i") ;
    var hasRi = iObj.hasClass("fa-caret-right");
    if(hasRi){
        iObj.attr("class","fa fa-caret-down")
    }else{
        iObj.attr("class","fa fa-caret-right")
    }
}
function setOpenVal(location ,departId,treeData){
    for(let i = 0 ; i < treeData.length; i++){
        var item_departID =  treeData[i]["id"];
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(Number(item_departID) === Number(departId)){
            var isOpen = treeData[i][location + 'Open']
            treeData[i][location + 'Open'] = !isOpen;
        }
        if(subDeparList && subDeparList.length>0){
            setOpenVal(location,departId,subDeparList)
        }
    }
}
function setVal(treeData, info, allselect){
    // info:选中的数据（部门/用户 ， id ，要设置的权值）
    // allselect 是否全选，如果是部门全选，则子部门和下面的人都全选
    var type = info['type'];
    var id = info['id'];
    for(let i = 0 ; i < treeData.length; i++){
        var departID =  treeData[i]["id"]
        var userList = treeData[i]["userList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(var u = 0; u < userList.length; u++){
                let userItem = userList[u];
                var uid = userItem["userID"];
                if((uid == id && type == "user") || allselect || (type =="depart" && departID == id)){
                    userItem['havRight'] = info['havRight'] ; // 用户有权限（本次设置的）1-有 ， 0-没有
                    if(userItem["roleCode"] == 1){ // 已有权限
                        if( userItem['havRight'] === 1){
                            userItem['changeSta'] = 0; // 原来有，现在有
                        }else{
                            userItem['changeSta'] = 1; // 原来有，现在没有 （第二行左边的）
                        }
                    }else{
                        if( userItem['havRight'] === 1){
                            userItem['changeSta'] = 2; // 原来没有，现在有（第二行右边的）
                        }else{
                            userItem['changeSta'] = 0; // 原来没有，现在没有
                        }
                    }
                    userList[u] = userItem ;
                }
            }
        }
        treeData[i]["userList"] = userList
        if(subDeparList && subDeparList.length>0){
            if(allselect || (departID == id && type == "depart")){
                setVal(treeData[i]["subList"], info, true);
            }else{
                setVal(treeData[i]["subList"], info);
            }
        }
    }
}

/* creator: 侯杏哲 2020-10-16 获取有权限的职工  */
function getRightUser(treeData) {
    let havRightUser = [] ;
    for(let i = 0 ; i < treeData.length; i++){
        let curUser = [];
        var userList = treeData[i]["userList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j]['havRight'] == 1 ;
                if(isok){
                    curUser.push(userList[j]);
                }
            }
        }
        let sonHav = []
        if(subDeparList && subDeparList.length>0){
            sonHav = getRightUser(subDeparList)
        }
        if(curUser.length > 0){
            havRightUser.push(...curUser);
        }
        if(sonHav.length > 0){
            havRightUser.push( ...sonHav);
        }

    }
    return havRightUser;
}
// creator: 侯杏哲 2020-10-16  计算有权限变化的人员数目
function diffCharge(treeData) {
    let allCount = 0 ;
    for(let i = 0 ; i < treeData.length; i++){
        var count = 0
        var userList = treeData[i]["userList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j]['changeSta'] == 1 || userList[j]['changeSta'] == 2 ;
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += diffCharge(subDeparList);
        }
        treeData[i]['count'] = count;
        allCount += count;
    }
    return allCount;
}

// creator: 侯杏哲,2017-12-04 渲染获取的文件夹数据和页面 ( 适用于主页面文件夹树和移动文件弹框的文件夹树 )
function showTreeData(data , treeItemThis) {
    // data - 获取的数据 ， treeItemThis - 点击的那个文件夹对象 ， treeObj - 点击的树对象

    // 处理子文件夹数据
    var listNotice      = data["parentFolder"] || [];
    var listNoticeChild = data["childFolder"];
    var level = parseInt(treeItemThis.parent().parent().attr("level")) ;
    var nextLevel = level + 1 ;
    var levelStr = "";
    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">' ;
    for(var i in listNoticeChild){
        if(listNoticeChild[i]["childStatus"] === '1'){ // 1时代表此文件夹下有子文件夹
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="fa fa-angle-right"></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"]+'</span>' +
                '</div></li>'
        }else{
            levelStr += '<li><div class="ty-treeItem" title="'+ listNoticeChild[i]["name"] +'" data-id='+listNoticeChild[i]["id"]+'><i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+listNoticeChild[i]["name"] + '</span>' +
                '</div></li>'
        }
    }
    levelStr += '</div>';
    localStorage.setItem("noticemax",listNotice["childStatus"] ); // childStatus : 1 是有文件夹; 2 是有文件; null标识什么都没有

    treeItemThis.attr("child", listNoticeChild.length > 0)
    if (listNoticeChild.length > 0) {
        if (treeItemThis.next().length === 0) {
            treeItemThis.after(levelStr);
        }
    }

}

// creator: 李玉婷，2019-07-15 19:43:10，工具方法-数字转化成汉语数字
var chnNumChar = ["零","一","二","三","四","五","六","七","八","九"];
var chnUnitSection = ["","万","亿","万亿","亿亿"];
var chnUnitChar = ["","十","百","千"];

function SectionToChinese(section){
    var strIns = '', chnStr = '';
    var unitPos = 0;
    var zero = true;
    while(section > 0){
        var v = section % 10;
        if(v === 0){
            if(!zero){
                zero = true;
                chnStr = chnNumChar[v] + chnStr;
            }
        }else{
            zero = false;
            strIns = chnNumChar[v];
            strIns += chnUnitChar[unitPos];
            chnStr = strIns + chnStr;
        }
        unitPos++;
        section = Math.floor(section / 10);
    }
    return chnStr;
}

function NumberToChinese(num){
    var unitPos = 0;
    var strIns = '', chnStr = '';
    var needZero = false;

    if(num === 0){
        return chnNumChar[0];
    }

    while(num > 0){
        var section = num % 10000;
        if(needZero){
            chnStr = chnNumChar[0] + chnStr;
        }
        strIns = SectionToChinese(section);
        strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
        chnStr = strIns + chnStr;
        needZero = (section < 1000) && (section > 0);
        num = Math.floor(num / 10000);
        unitPos++;
    }

    return chnStr;
}
// creator: 李玉婷，2019-07-17 09:18:35，文件夹大小换算
function byteConvert(bytes) {
    if (isNaN(bytes)) {
        return '';
    }
    var symbols = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    var exp = Math.floor(Math.log(bytes)/Math.log(2));
    if (exp < 1) {
        exp = 0;
    }
    var i = Math.floor(exp / 10);
    bytes = bytes / Math.pow(2, 10 * i);

    if (bytes.toString().length > bytes.toFixed(2).toString().length) {
        bytes = bytes.toFixed(2);
    }
    return bytes + ' ' + symbols[i];
}





