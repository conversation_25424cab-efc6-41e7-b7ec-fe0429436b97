/* 发布管理 */
$(function () {
    getList(1);
});
// creator : 侯杏哲 2017-11-30 获取发布列表
var searchInfo = {"status":1  } ; // 1 - 待审批 ，2 - 已批准, 3 - 已驳回
function getList( cur ){
    if( searchInfo["status"] == 2 ){  $("#type").html("换版批准时间") ;  }else{ $("#type").html("换版驳回时间") ; }
    $.ajax({
        url: "getChangePublishFile.do" ,
        data : {
            "pageSize":20 ,
            "currentPageNo" : cur ,
            "approveStatus":searchInfo["status"]
        },
        success:function (res) {
            var success = res["success"];
            if(success !== 1){
                bounce.show($("#tip")) ;
                $("#TipCon").html("链接错误！");
                return false;
            }
            var data    = res["data"] ;
            var pageInfo= data["pageInfo"] ;
            var cur     = pageInfo["currentPageNo"];
            var count   = pageInfo["totalPage"];
            //设置分页
            setPage( $("#ye_con"), cur, count, "changeVersion","{}");
            var list    = data["list"] ;
            var str     = "" ;
            if( list && list.length > 0){
                for( var i in list ){
                    var size = list[i]["size"] , sizeStr = "";
                    if(size<102400){
                        sizeStr = parseFloat(size/1024).toFixed(2) + 'KB';
                    }else{
                        sizeStr = parseFloat(size/1048576).toFixed(2) + 'MB';
                    }
                    if(searchInfo["status"] == 1){
                        str += '<tr>' +
                            ' <td>'+( list[i]["updateDate"] || "" )+'</td>' +
                            ' <td>'+ list[i]["updateName"] +'</td>' +
                            ' <td>'+ list[i]["fileSn"] +'</td>' +
                            ' <td>'+ list[i]["name"] +'</td>' +
                            ' <td>'+ list[i]["version"] +'</td>' +
                            ' <td>'+ "G" + list[i]["changeNum"] +'</td>' +
                            ' <td>'+ sizeStr +'</td>' +
                            ' <td><span class="ty-color-blue" onclick="getInfo($(this) , '+ list[i]["id"] +')">详情</span></td>' +
                            ' </tr>' ;
                    }else{
                        str += '<tr>' +
                            ' <td>'+( list[i]["updateDate"] || "" )+'</td>' +
                            ' <td>'+ list[i]["updateName"] +'</td>' +
                            ' <td>'+ list[i]["fileSn"] +'</td>' +
                            ' <td>'+ list[i]["name"] +'</td>' +
                            ' <td>'+ list[i]["version"] +'</td>' +
                            ' <td>'+ "G" + list[i]["changeNum"] +'</td>' +
                            ' <td>'+ sizeStr +'</td>' +
                            ' <td>'+ list[i]["auditDate"] +'</td>' +
                            ' <td><span class="ty-color-blue" onclick="getInfo($(this) , '+ list[i]["id"] +')">详情</span></td>' +
                            ' </tr>' ;
                    }
                }
            }
            if(searchInfo["status"] == 1){ // 1 - 待审批
                $("#tab1Con").html(str);
                $("#tab1").show(); $("#tab2").hide() ;
            }else{ //  2 - 已批准, 3 - 已驳回
                $("#tab2Con").html(str);
                $("#tab2").show(); $("#tab1").hide() ;
            }
            if(searchInfo["status"] == 2){
                $("#tab2 thead td").eq(7).html("换版批准时间")
            }else{
                $("#tab2 thead td").eq(7).html("换版驳回时间")
            }
        }
    })
}
// creator : 侯杏哲 2017-11-30 切换二级导航
function showPagetab( obj , status) {
    searchInfo["status"] = status ;
    getList(1) ;
    obj.addClass("ty-active").siblings().removeClass("ty-active") ;
}
// creator : 侯杏哲 2017-11-30 获取详情
function getInfo( obj , id ) {
    searchInfo["id"] = id ;
    searchInfo["editObj"] = obj ;
    $("#scanInfo").show();
    $("#reason").val("");
    $.ajax({
        "url" : "getUpdatePublishMessage.do" ,
        "data" : { "hisId" : id  } ,
        "type" : "post" ,
        "dataType" : "json" ,
        "beforeSend" : function () { loading.open(); } ,
        success:function (res) {
            var success = res["success"] ;
            if( success == 1 ){
                var info = res["data"]["fileHistory"] ;
                bounce.show( $("#scanInfo") ) ;
                if(searchInfo["status"] == 1){
                    if( generalType ){ // 总务，可审批
                        $(".info_charge").show();  $(".info_scan").hide();
                    }else{ // 非总务 ， 不可审批
                        $(".info_charge").hide();  $(".info_scan").show();
                    }
                }else {
                    $(".info_charge").hide();  $(".info_scan").show();
                }
                var size = info["size"];
                var sizeStr = "";
                if(size<102400){
                    sizeStr = parseFloat(size/1024).toFixed(2) + 'KB';
                }else{
                    sizeStr = parseFloat(size/1048576).toFixed(2) + 'MB';
                }
                var handleStr = '<a class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" path="'+ info["path"] +'" onclick="seeOnline($(this))">在线预览</a> ' +
                    '<a class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" path="'+ info["path"] +'" onclick="return getDownLoad($(this));" download="'+ info["name"] +'.'+info["version"]+'">下载</a>';

                if(searchInfo["status"] == 1){ // 待审批
                    if(generalType != 1 && generalType != 2){ // 登录人不是总务
                        $(".notZW_char").show(); $(".isZW_char").hide();
                    }else{
                        $(".notZW_char").hide(); $(".isZW_char").show();
                    }
                    $(".alreadyOk").hide(); $(".alreadyNo").hide(); // 隐藏批准的信息
                    $("#info_ttl").html("待审批详情");
                }else if(searchInfo["status"] == 2) { // 已批准
                    $(".notZW_char").show(); $(".isZW_char").hide();
                    $("#info_ttl").html("已批准详情");
                    $(".alreadyOk").show(); $(".alreadyNo").hide();
                }else{ //  已驳回
                    $(".notZW_char").show(); $(".isZW_char").hide();
                    $("#info_ttl").html("已驳回详情");
                    $(".alreadyOk").hide(); $(".alreadyNo").show();
                }
                var arr = res["data"]["listUser"] ;
                var creator = info["creator"] ; // 创建人
                var issue =  chargeZW(creator , arr) ;
                if( issue ) { // 上传人是总务
                    $("#creatDoc .isZW").show();$("#creatDoc .notZW").hide();
                    $("#info_creat").html(info["createName"]) ; $("#info_creatTime").html( formatDateStr(info["createDate"]) ) ;
                }else {
                    $("#creatDoc .isZW").hide();$("#creatDoc .notZW").show();
                    $("#info_notCreater").html(info["createName"]) ; $("#info_notCreaterTime").html( formatDateStr(info["createDate"]) ) ;
                }
                var changeNum = "G" + info["changeNum"] ;
                $("#info_category").html(res["data"]["categoryName"]); $("#info_name").html(info["name"]);
                $("#info_sn").html(info["fileSn"]); $("#info_vertion").html(info["version"]);
                $("#info_size").html(sizeStr); $("#info_changeNum").html(changeNum);
                $("#info_content").html(info["content"]); $("#info_changeReason").html(info["reason"]);
                $("#info_noTime").html(formatDateStr(info["auditDate"])); $("#info_noReason").html(info["approveMemo"]);
                $("#info_okTime").html(formatDateStr(info["auditDate"]));
                $("#info_notChange").html(info["updateName"]); $("#info_notChangeTime").html(formatDateStr(info["updateDate"]));
                var firstAuditDate = res["data"]["firstAuditDate"];  // 发布批准时间
                $("#info_chargedDate").html(formatDateStr(firstAuditDate));
                $("#info_audo").html(info["opertatorName"]);  $("#info_audoTime").html(formatDateStr(info["operateDate"]));
                $("#info_charger").html(info["verifierName"]);  $("#info_chargeTime").html(formatDateStr(info["verifyDate"]));
                $("#info_char").html(info["approverName"]);  $("#info_charTime").html(formatDateStr(info["approveDate"]));
                $("#scanInfo .handle").html(handleStr)

            }else{
                bounce.show( $("#tip") ) ; $("#TipCon").html("获取数据失败！");
            }

        } ,
        error:function () {
            bounce.show( $("#tip") ) ; $("#TipCon").html("链接错误！");
        } ,
        complete:function () {
            loading.close() ;
        }
    }) ;
}
// creator : 侯杏哲 2017-12-01  审批批准、驳回
function charge( type ) { // type : 2 - 批准 ， 3- 驳回
    var approveMemo = "" ;
    if(type == 3){  approveMemo = $("#reason").val() ;   }
    $.ajax({
        "url" : "approveChangeVersionFile.do" ,
        "data" : { "id":searchInfo["id"] , "approveStatus" : type , "approveMemo": approveMemo } ,
        "type" : "post" ,
        "dataType" : "json" ,
        "beforeSend" : function () { loading.open(); } ,
        success:function (res) {
            var success = res["success"] ;
            if( success == 1 ){
                searchInfo["editObj"].parent().parent().remove(); bounce.show( $("#tip") ) ; $("#TipCon").html("审批成功！") ;
            }else{
                bounce.show( $("#tip") ) ; $("#TipCon").html("审批失败！") ;
            }


        } ,
        error:function () {
            bounce.show( $("#tip") ) ; $("#TipCon").html("链接错误！");
        } ,
        complete:function () {
            loading.close() ;
        }
    })

}
/* creator: 侯杏哲 2017-12-06 工具方法 - 格式化默认时间 */
function formatDateStr( _date ) {
    if(_date =="1900-01-01 00:00:00" || _date == "1900-01-01"){ _date = "" ; }
    return _date ;
}
/* creator : 侯杏哲 2017-12-16 判断当前是否为总务 */
function chargeZW(testID , arr) {
    var isZW = false ;
    if(arr && arr.length > 0){
        for(var i = 0 ; i < arr.length ; i++){
            var id = arr[i]["userID"] ;
            if( id == testID){ isZW = true ;  }
        }
    }
    return isZW ;
}


