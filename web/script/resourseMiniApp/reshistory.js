/**
 * Created by Administrator on 2017/5/10.
 */
$(function () {
    $(".ty-fileList").on("click",".ty-fileItem",function () {
        $(".ty-fileItem").removeClass("ty-fileItemActive") ;
        $(this).addClass("ty-fileItemActive") ;
    });
    getRecord( 1,20);
}) ;
//updator:王静 2017-08-03 15:38:23 获取换版后的历史文件列表
function getRecord(cur, pageSize){
    //得到地址栏传过来的ID
    var fileId = getUrlParam("id");
    $.ajax({
        url: "getUpdateFile.do",
        data: {"fileId":fileId,"currentPageNo":cur,"pageSize":pageSize},
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success: function (res) {
            var data = res["data"] ;
            var pageInfo = data["pageInfo"] ;
            var count = pageInfo["totalPage"] ;
            var cur = pageInfo["currentPageNo"] ;
            var jsonStr=JSON.stringify({"fileId":fileId});
            setPage( $("#ye_con") , cur , count , "changeRecord" , jsonStr);
            if(data === ""||typeof (data) === "undefined") {
                $("#mt_tip_ms").html("系统错误，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var fileInfo = data["list"];
                var itemStr = "";
                if(fileInfo && fileInfo.length > 0){
                    for(var i = 0; i < fileInfo.length; i++){
                        var id          = fileInfo[i].id,
                            name        = fileInfo[i].name,
                            size        = fileInfo[i].size,
                            path        = fileInfo[i].path,
                            fileSn      = fileInfo[i].fileSn,
                            changeNum   = fileInfo[i].changeNum,
                            fileType    = fileInfo[i].version,
                            updateName  = fileInfo[i].updateName,
                            createName  = fileInfo[i].createName,
                            updateDate  = fileInfo[i].updateDate,
                            createDate  = fileInfo[i].createDate,
                            updator     = fileInfo[i].updator,
                            creator     = fileInfo[i].creator;

                        //格式化数据
                        size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';

                        updateName  === null? updateName = createName:updateName;
                        updateDate  === null? updateDate = createDate:updateDate;
                        updator     === null? updator    = creator:updateDate;

                        itemStr +=  '<div class="ty-fileItem" id="'+id+'">'+
                                        '<div class="ty-left ty-fileType ty-file_'+fileType+'"></div>'+
                                        '<div class="ty-fileInfo ty-left">'+
                                            '<div class="ty-fileName" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                                            '<div class="ty-fileVersion">'+'G'+changeNum+'</div>'+
                                            '<div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                                            '<div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate +'</div>'+
                                        '</div>'+
                                        '<div class="ty-fileHandle ty-right">'+
                                            '<a class="ty-left" path="'+ path +'" onclick="fileSeeOnline($(this))">在线预览</a>' +
                                            '<a class="ty-left" path="'+ path +'" onclick="fileDownload($(this));" download="'+ name + '.' + fileType+'">下载</a>'+
                                            '<a class="ty-left" onclick="basicMessage($(this))">基本信息</a>'+
                                        '</div>'+
                                    '</div>';
                    }
                    $(".ty-fileList").html(itemStr) ;
                }
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ chargeClose(1) ;  }
    })
}
//updator:王静 2017-08-03 16:01:23 点击基本信息
function basicMessage(obj){
    var hFileId=obj.parent().parent().attr("id");
    $.ajax({
        url: "getUpFileMes.do",
        data: { "id": hFileId },
        type: "post",
        dataType: "json",
        beforeSend:function(){ loading.open() ;  } ,
        success: function (res) {
            var success = res["success"] ;
            if (success != 1) {
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            } else {
                var list = res["data"]["resource"];
                var operateDate = list.operateDate;
                if(operateDate == "1900-01-01 00:00:00"){
                    operateDate = "--";
                }
                var verifyDate = list.verifyDate;
                if(verifyDate == "1900-01-01 00:00:00"){
                    verifyDate = "--";
                }
                var approveDate = list.approveDate;
                if(approveDate == "1900-01-01 00:00:00"){
                    approveDate = "--";
                }
                var reason = list.reason;
                if(reason == undefined){
                    reason = "--";
                }
                var size = list.size;
                var sizeStr = "";
                if(size<102400){
                    sizeStr = parseFloat(size/1024).toFixed(2) + 'KB';
                }else{
                    sizeStr = parseFloat(size/1048576).toFixed(2) + 'MB';
                }
                var detailStr =
                    '<div class="ty-json">'+
                    '<div class="ty-jsonHead">基本信息</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">文件编号</div>'+
                    '<div class="ty-val fileSn">'+list.fileSn+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">文件名称</div>'+
                    '<div class="ty-val fileName">'+list.name+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">原始上传人</div>'+
                    '<div class="ty-val fileName">'+list.createName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">原始上传时间</div>'+
                    '<div class="ty-val fileName">'+ (list.createDate && list.createDate.substring(0,19)) +'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">版本号</div>'+
                    '<div class="ty-val">G'+list.changeNum+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">当前版本上传人</div>';
                if(list.updateName==null || list.updateName==""){
                    detailStr += '<div class="ty-val fileName">'+list.createName+'</div>';
                }else{
                    detailStr += '<div class="ty-val fileName">'+list.updateName+'</div>';
                }
                detailStr += '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">当前版本上传时间</div>';
                if(list.updateDate==null || list.updateDate==""){
                    detailStr += '<div class="ty-val fileName">'+((list.createDate || "") && list.createDate.substring(0,19))+'</div>';
                }else{
                    detailStr += '<div class="ty-val fileName">'+list.updateDate.substring(0,19)+'</div>';
                }
                detailStr +=
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">大小</div>'+
                    '<div class="ty-val">'+sizeStr+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">类型</div>'+
                    '<div class="ty-val">'+list["version"] +'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">换版原因</div>'+
                    '<div class="ty-val">'+reason+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">说明</div>'+
                    '<div class="ty-val">'+ (list["content"] ||"") +'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-jsonHead">制作与审批信息</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">制作人</div>'+
                    '<div class="ty-val">'+list.opertatorName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">制作时间</div>'+
                    '<div class="ty-val">'+operateDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">审批人</div>'+
                    '<div class="ty-val">'+list.verifierName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">审批时间</div>'+
                    '<div class="ty-val">'+verifyDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">批准人</div>'+
                    '<div class="ty-val">'+list.approverName+'</div>'+
                    '</div>'+
                    '<div class="ty-json">'+
                    '<div class="ty-key">批准时间</div>'+
                    '<div class="ty-val">'+approveDate.substring(0,19)+'</div>'+
                    '</div>'+
                    '</div>';
                $(".ty-filePreview .ty-fileDetail").html("");
                $(".seeOnline").remove();
                $(".ty-filePreview .ty-fileDetail").append(detailStr);
                $(".ty-filePreview .seeOnline").attr("path",list.path);
            }

        },
        error: function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){ loading.close() ;  }
    }) ;
}
// creator: 张旭博，2018-05-24 11:04:53，文件预览 - 按钮
function fileSeeOnline(selector) {
    seeOnline(selector)
}

// creator: 张旭博，2018-05-24 11:05:09，文件下载 - 按钮
function fileDownload(selector) {
    getDownLoad(selector)
}