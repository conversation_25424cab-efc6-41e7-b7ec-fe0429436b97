/* 发布管理 */
$(function () {
    getList(1);
});
// creator : 侯杏哲 2017-11-30 获取发布列表
var searchInfo = {"status":1  } ; // 1 - 待审批 ，2 - 已批准, 3 - 已驳回
function getList( cur ){
    if( searchInfo["status"] == 2 ){
        $("#type").html("发布批准时间") ;
    }else {
        $("#type").html("发布驳回时间") ;
    }
    $.ajax({
        "url": "getPublishFile.do" ,
        "data" : { "pageSize":20 , "currentPageNo" : cur , "approveStatus":searchInfo["status"] } ,
        "type":"post" ,
        "dataType":"json" ,
        beforeSend:function(){ loading.open();  } ,
        success:function (res) {
            var success = res["success"];
            var data = res["data"] ;
            var pageInfo = data["pageInfo"] ;
            var count = pageInfo["totalPage"] ;
            var cur = pageInfo["currentPageNo"] ;
            setPage( $("#ye_con"), cur, count, "issue","{}");
            var list = data["list"] ;
            var str = "" ;
            if( list && list.length >0){
                for( var i in list ){
                    //格式化数据
                    var size = list[i]["size"] ;
                    size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';
                    if(searchInfo["status"] == 1){
                        str += '<tr>' +
                            ' <td>'+( list[i]["createDate"] || "" )+'</td>' +
                            ' <td>'+ list[i]["createName"] +'</td>' +
                            ' <td>'+ list[i]["fileSn"] +'</td>' +
                            ' <td>'+ list[i]["name"] +'</td>' +
                            ' <td>'+ list[i]["version"] +'</td>' +
                            ' <td>'+ size +'</td>' +
                            ' <td><span class="ty-color-blue" onclick="getInfo($(this) , '+ list[i]["id"] +')">详情</span></td>' +
                            ' </tr>' ;
                    }else{
                        str += '<tr>' +
                            ' <td>'+( list[i]["createDate"] || "" )+'</td>' +
                            ' <td>'+ list[i]["createName"] +'</td>' +
                            ' <td>'+ list[i]["fileSn"] +'</td>' +
                            ' <td>'+ list[i]["name"] +'</td>' +
                            ' <td>'+ list[i]["version"] +'</td>' +
                            ' <td>'+ size +'</td>' +
                            ' <td>'+ list[i]["auditDate"] +'</td>' +
                            ' <td><span class="ty-color-blue" onclick="getInfo($(this) , '+ list[i]["id"] +')">详情</span></td>' +
                            ' </tr>' ;
                    }
                }
            }
            if(searchInfo["status"] == 1){
                $("#tab1Con").html(str);
                $("#tab1").show();  $("#tab2").hide() ;
            }else{
                $("#tab2Con").html(str);
                $("#tab2").show();   $("#tab1").hide();
            }
        },
        error:function () {
            bounce.show() ;
        },
        complete:function () {
            loading.close() ;
        }
    })
}
// creator : 侯杏哲 2017-11-30 切换二级导航
function showSecTab( obj , status) {
    searchInfo["status"] = status ;
    getList(1) ;
    obj.addClass("ty-active").siblings().removeClass("ty-active") ;
}
// creator : 侯杏哲 2017-11-30 获取详情
function getInfo( obj , id ) {
    searchInfo["id"] = id ;
    searchInfo["tr"] = obj.parent().parent() ;
    $("#scanInfo").show();
    $.ajax({
        "url" : "getOnePublishFileMessage.do" ,
        "data" : { "id" : id  } ,
        "type" : "post" ,
        "dataType" : "json" ,
        "beforeSend" : function () { loading.open(); } ,
        success:function (res) {
            var success = res["success"] ;
            if( success == 1 ){
                var info = res["data"]["resource"] ;
                var categoryName = res["data"]["categoryName"] ;
                bounce.show( $("#scanInfo") ) ;
                if(searchInfo["status"] == 1){ // 待审批
                    $("#infoTTL").html("待审批详情") ;
                    $(".alreadyNo").hide(); $(".alreadyOk").hide();
                    if( generalType==1 || generalType ==2 ){ // 总务，可审批
                        $(".info_charge").show();  $(".info_scan").hide();
                    }else{ // 非总务 ， 不可审批
                        $(".info_charge").hide();  $(".info_scan").show();
                    }
                } else {
                    $(".info_charge").hide();  $(".info_scan").show();
                    if(searchInfo["status"] == 2){ // 已批准
                        $(".alreadyNo").hide(); $(".alreadyOk").show(); $("#infoTTL").html("已批准详情") ;
                    }else{ // 已驳回
                        $(".alreadyNo").show(); $(".alreadyOk").hide();  $("#infoTTL").html("已驳回详情") ;
                    }
                }
                if(info){
                    var size = info["size"];
                    var sizeStr = "";
                    if(size<102400){
                        sizeStr = parseFloat(size/1024).toFixed(2) + 'KB';
                    }else{
                        sizeStr = parseFloat(size/1048576).toFixed(2) + 'MB';
                    }
                    var handleStr = '<a class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" path="'+ info["path"] +'" onclick="seeOnline($(this))">在线预览</a> ' +
                                    '<a class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" path="'+ info["path"] +'" onclick="return getDownLoad($(this));" download="'+ info["name"] +'.'+info["version"]+'">下载</a>';
                    $("#info_category").html(categoryName); $("#info_name").html(info["name"]);
                    $("#info_sn").html(info["fileSn"]); $("#info_vertion").html(info["version"]);
                    $("#info_size").html(sizeStr); $("#info_content").html(info["content"]);
                    $("#info_noTime").html( formatDateStr( info["auditDate"]) ); $("#info_noReason").html(info["approveMemo"]);
                    $("#info_okTime").html( formatDateStr(info["auditDate"]) ); $("#info_creator").html(info["createName"]);
                    $("#info_creatTime").html(formatDateStr(info["createDate"])); $("#info_audo").html(info["opertatorName"]);
                    $("#info_audoTime").html(formatDateStr(info["operateDate"])); $("#info_charger").html(info["verifierName"]);
                    $("#info_chargeTime").html(formatDateStr(info["verifyDate"])); $("#info_char").html(info["approverName"]);
                    $("#info_charTime").html(formatDateStr(info["approveDate"]));
                    $("#scanInfo .handle").html(handleStr)
                }else{
                    bounce.show( $("#tip") ) ; $("#TipCon").html("获取数据失败！");
                }
            }else{
                bounce.show( $("#tip") ) ; $("#TipCon").html("获取数据失败！");
            }
        } ,
        error:function () {
            bounce.show( $("#tip") ) ; $("#TipCon").html("链接错误！");
        } ,
        complete:function () {
            loading.close() ;
        }
    }) ;
}

// creator : 侯杏哲 2017-12-01  审批批准、驳回
function charge( type ) { // type : 2 - 批准 ， 3- 驳回
    var memo = $("#reason").val();
    var dataJs = { "id":searchInfo["id"] , "approveStauts" : type } ;
    if(type == 3){ dataJs["approveMemo"] = memo;  }
    $.ajax({
        "url" : "approveOrRebutPublishFile.do" ,
        "data" : dataJs ,
        "type" : "post" ,
        "dataType" : "json" ,
        "beforeSend" : function () { loading.open(); } ,
        success:function (res) {
            var success = res["success"] ;
            if( success == 1 ){
                bounce.cancel();
                layer.msg("审批成功")
                searchInfo["tr"].remove() ;
            }else{
                bounce.show( $("#tip") ) ; $("#TipCon").html("审批失败");
            }
        } ,
        error:function () {
            bounce.show( $("#tip") ) ; $("#TipCon").html("链接错误！");
        } ,
        complete:function () {
            loading.close() ;
        }
    })

}

/* creator: 侯杏哲 2017-12-06 工具方法 - 格式化默认时间 */
function formatDateStr( _date ) {
    if(_date =="1900-01-01 00:00:00" || _date == "1900-01-01"){ _date = "" ; }
    return _date ;
}


