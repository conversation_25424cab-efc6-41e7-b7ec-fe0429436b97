jQuery.fn.treeItemSlide = function (type) {
    return $(this).each(function(){
        var iconNode = $(this).children('i').eq(0)
        if (!iconNode.hasClass("ty-fa")) {
            if (type === 'up') {
                return iconNode.removeClass('fa-angle-down').addClass('fa-angle-right')
            } else if (type === 'down') {
                return iconNode.removeClass('fa-angle-right').addClass('fa-angle-down')
            } else if (type === 'toggle') {
                if (iconNode.hasClass("fa-angle-right")) {
                    return iconNode.eq(0).removeClass('fa-angle-right').addClass('fa-angle-down')
                } else {
                    return iconNode.eq(0).removeClass('fa-angle-down').addClass('fa-angle-right')
                }
            }
        }
    });
}
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
var destroySubscribe = [];
var filelist = new Array();
//重构关闭方法，取消选项和定时器。
bounce.oldCancle = bounce.cancel;
bounce.cancel = function () {
    delayLogout.clearDelayLogoutTime();
    filelist.length=0;
    if ($("#upload-folder-01").data("kendoUpload")) {
        $("#upload-folder-01").data("kendoUpload").removeAllFiles();

    }
    if ($("#upload-file-01").data("kendoUpload")) {
        $("#upload-file-01").data("kendoUpload").removeAllFiles();
    }
    this.oldCancle();
}
$(function () {
    $(".ty-switch__core").on("click",function (){
        $(this).parents(".ty-switch").toggleClass("is-checked")
        $(this).siblings("input").prop("checked", $(this).parents(".ty-switch").hasClass("is-checked"))
        var categoryId = $("#listDoc .ty-treeItemActive").data("id");
        getFile(1, 20, categoryId)
    })
    initPage();

    sphdSocket.subscribe('resCentreAddFile', function (data) {
        // bounce.cancel();
        console.log('resCentreAddFile', data)
        var data = JSON.parse(data);
        var res = data.res
        var category = data.category
        var mainTreeActive = $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive")
        if (res) {
            // 返回的是文件信息
            var operationTag = res.operationTag // 1代表实时新增文件，2代表要实时删除文件
            if (mainTreeActive.length > 0 && res.category === mainTreeActive.data("id")) {
                if(operationTag == 2){ // 删除文件
                    $(".ty-fileList [data-id='" + res.id + "']").remove();
                    // mainTreeActive.click();
                }else{
                    if(typeof($(".ty-fileList [data-id='" + res.id + "']").attr('pid')) === 'undefined'){
                        var listStr = getFileListStr([res]);
                        $(".ty-fileList").prepend(listStr);
                    }else {
                        var listStr = getFileListStr([res]);
                        $(".mainFileList  [data-id='" + res.id + "']").replaceWith(listStr);
                    }
                    if ($('.ty-fileNull').length > 0) {
                        if($('.mainFileList .ty-fileItem').length >0){
                            $('.ty-fileNull').hide()
                        }else{
                            $('.ty-fileNull').show()
                        }
                    }
                }

            }
        }
        if (category) {
            $("#fileUploadBtn").attr("class","ty-btn ty-btn-big ty-btn-green ty-circle-3")
            $("#folderUploadBtn").attr("class","ty-btn ty-btn-big ty-btn-green ty-circle-3")
            var parent = category.parent
            var operationTag = category.operationTag // 1代表实时新增文件，2代表要实时删除文件
            if(operationTag == 2){ // 删除文件夹
                /* var folerID = category.id
                 var folder = $(".ty-colFileTree[data-name='main'] .ty-treeItem[data-id='"+ folerID +"']");
                 if(folder.length > 0 ){
                     if(folder.hasClass("ty-treeItemActive")){
                         layer.msg("因权限调整，您已无权查看此文件夹");
                         $(".mainFileList").html("")
                         $("#ye_con").html("")
                     }
                     folder.remove();
                 }*/
            }else{
                var parentFolder = $(".ty-colFileTree[data-name='main'] .ty-treeItem[data-id='"+parent+"']");
                if (parentFolder.length > 0) {
                    var angleObj = parentFolder.children().eq(0);
                    var insertOk = false ;
                    if ( angleObj.hasClass("fa-angle-down") ) {
                        insertOk = true
                    }else if(angleObj.hasClass("ty-fa")){
                        if(parentFolder.hasClass("ty-treeItemActive")){
                            angleObj.attr("class", "fa fa-angle-down");
                            insertOk = true
                        }else{
                            angleObj.attr("class", "fa fa-angle-right");
                        }
                    }
                    if(insertOk){
                        var level = parseInt(parentFolder.parent().parent().attr("level")) ;
                        var nextLevel = level + 1 ;
                        var folderStr = "";
                        if(category.children !== 0){ // 1时代表此文件夹下有子文件夹
                            folderStr += '<li><div class="ty-treeItem" title="'+ category.name +'" data-id='+category.id+' data-child="true"> <i class="fa fa-angle-right"></i> <i class="fa fa-folder"></i> <span>'+category.name+'</span>' +
                                '</div></li>'
                        }else{
                            folderStr += '<li><div class="ty-treeItem" title="'+ category.name +'" data-id='+category.id+' data-child="false"> <i class="ty-fa"></i> <i class="fa fa-folder"></i> <span>'+category.name + '</span>' +
                                '</div></li>'
                        }
                        if(parentFolder.siblings().length > 0){
                            parentFolder.siblings().append(folderStr)
                        }else{
                            let levelStr =  '<div class="level'+ nextLevel +'" level="'+ nextLevel +'">' + folderStr +  '</div>' ;
                            parentFolder.after(levelStr)
                        }
                    }

                }else{
                    if(Number(parent) > 0){
                        return false
                    }
                    // 新增一级文件夹
                    var levelStr = '<li><div class="ty-treeItem" title="'+ category.name +'" data-id='+category.id+'>' +
                        '<i class="ty-fa"></i></i><i class="fa fa-folder"></i><span>'+category.name + '</span>' +
                        '</div></li>'
                    if(isGeneral){
                        $("#listDoc .level1").children("li:last").prev().after(levelStr);

                    } else {
                        if($("#listDoc .level1").children("li:last").length > 0){
                            $("#listDoc .level1").children("li:last").after(levelStr);
                        }else {
                            $("#listDoc .level1").prepend(levelStr);
                        }
                    }

                }
            }

        }
        // }, null, 'custom', id)
    }, null, 'user');
    $("#btn-group").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            // 文件上传/文件发布申请/文件换版/文件换版申请 - 选择保存位置 - 按钮
            case 'fileUpload':
                if($("#fileUploadBtn").hasClass("ty-btn-gray")){
                    return false
                }
                if(isGeneral){
                    $("#fileUpload .bounce_title").html("上传文件")
                } else {
                    $("#fileUpload .bounce_title").html("文件发布申请")
                }
                // 初始化表单
                $("#fileUpload").find("input.ty-inputText").val("")
                $("#needOther").prop("checked", true)
                $("#fileUpload .savePlace").hide().html("")
                // 展示为上传文件的内容（和换版共用弹窗）
                $("#fileUpload .bounce_title").html($(this).html())
                $("#fileUpload .inputPart").show().siblings(".seePart").hide()
                $("#sureUploadNewFileBtn").data("name", "sureUploadNewFile")
                bounce.show($("#fileUpload"))
                // 初始化缓存数据
                $("#fileUpload").data("fileInfo", '')
                $("#fileUpload").data("fileArr", [])
                getApprover()
                // 初始化上传插件
                initKendoFileUpload()
                // 显示名称编号输入框（单个文件显示，多个隐藏）
                setFileInput([])
                setEveryTime(bounce, 'fileUpload')
                break
            case 'folderUpload':
                if($("#folderUploadBtn").hasClass("ty-btn-gray")){
                    return false
                }
                $("#chooseSaveFolderBtn").attr("class","ty-btn ty-btn-gray ty-circle-3")

                filelist.length=0
                bounce.show($("#folderUpload"))
                $('#folderStatus').html('&nbsp;');
                $("#folderUpload").find("input.ty-inputText").val("")
                $("#folderUpload .savePlace").hide().html("")
                setEveryTime(bounce, 'folderUpload')
                break
        }
    })
    $(".bounce").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            // 文件上传/文件发布申请/文件换版/文件换版申请 - 选择保存位置 - 按钮
            case 'chooseSaveFolder':
                if ($("#folderUpload").is(":visible")) {
                    var gray = $("#chooseSaveFolderBtn").hasClass('ty-btn-gray')
                    if(gray){
                        layer.msg('请先选择文件夹！')
                        return false
                    }
                }

                $("#chooseSaveFolder .bounce_title").html("选择保存位置")
                bounce_Fixed.show($("#chooseSaveFolder"));
                getFirstDoc('chooseSaveFolder')
                setEveryTime(bounce_Fixed, 'chooseSaveFolder');
                break
            case 'sureUploadNewFile':
                var fileLength = $("#fileUpload").data("fileArr")
                if (fileLength && fileLength.length > 1) {
                    bounce_Fixed.show($("#confirmSignIn"))
                    $("#confirmSignIn .sureBtn").unbind().on("click", function (){
                        sureUploadNewFile()
                    })
                } else {
                    sureUploadNewFile()
                }
                break;
            case 'sureChangeVersion':
                sureChangeVersion()
                break;
            case 'sureUploadNewFolder':
                sureUploadNewFolder()
                break;
            case 'chooseInWaitRelate':
                // 在待关联的表格中选择
                bounce_Fixed.show($("#chooseInFolder"))
                $("#fileNameOrSn2").val("")
                $("#chooseInFolder").data("type", 1)
                $("#chooseInFolder .bounce_title").html("在待关联的表格中选择")
                getSheetList(1 , 20, 1)
                $("#chooseInFolder .sureBtn").unbind().on("click", function () {
                    var id = $("#chooseInFolder .fileItemRadioChoose.active").data("id")
                    if(!id) {
                        layer.msg("请选择")
                        return false
                    }
                    var resource = $("#page_sheetRelate").data("fileId")
                    $.ajax({
                        url: '../res/addResFormCorrelation.do',
                        data: {
                            id: id,
                            resource: resource
                        },
                        success: function(res) {
                            var data = res.data
                            var state = data.state
                            if (state === 1) {
                                layer.msg("操作成功")
                                bounce_Fixed.cancel()
                                bounce.cancel()
                                getSheetRelateList(1, 20)
                            } else if (state === 2) {
                                layer.msg("表格已存在关联，无需新增")
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break;
            case 'chooseInRelated':
                // 在已关联的表格中选择
                bounce_Fixed.show($("#chooseInFolder"))
                $("#fileNameOrSn2").val("")
                $("#chooseInFolder").data("type", 2)
                $("#chooseInFolder .bounce_title").html("在已关联的表格中选择")
                getSheetList(1 , 20, 2)
                $("#chooseInFolder .sureBtn").unbind().on("click", function () {
                    var id = $("#chooseInFolder .fileItemRadioChoose.active").data("id")
                    if(!id) {
                        layer.msg("请选择")
                        return false
                    }
                    var resource = $("#page_sheetRelate").data("fileId")
                    $.ajax({
                        url: '../res/addResFormCorrelation.do',
                        data: {
                            id: id,
                            resource: resource
                        },
                        success: function(res) {
                            var data = res.data
                            var state = data.state
                            if (state === 1) {
                                layer.msg("操作成功")
                                bounce_Fixed.cancel()
                                bounce.cancel()
                                getSheetRelateList(1, 20)
                            } else if (state === 2) {
                                layer.msg("表格已存在关联，无需新增")
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break;
            case 'chooseInStoped':
                // 在被废止的表格中选择
                bounce_Fixed.show($("#chooseInFolder"))
                $("#fileNameOrSn2").val("")
                $("#chooseInFolder").data("type", 3)
                $("#chooseInFolder .bounce_title").html("在被废止的表格中选择")
                getSheetList(1 , 20, 3)

                $("#chooseInFolder .sureBtn").unbind().on("click", function () {
                    var id = $("#chooseInFolder .fileItemRadioChoose.active").data("id")
                    if(!id) {
                        layer.msg("请选择")
                        return false
                    }
                    var resource = $("#page_sheetRelate").data("fileId")
                    $.ajax({
                        url: '../res/addResFormCorrelation.do',
                        data: {
                            id: id,
                            resource: resource
                        },
                        success: function(res) {
                            var data = res.data
                            var state = data.state
                            if (state === 1) {
                                layer.msg("操作成功")
                                bounce_Fixed.cancel()
                                bounce.cancel()
                                getSheetRelateList(1, 20)
                            } else if (state === 2) {
                                layer.msg("表格已存在关联，无需新增")
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })
                break;
            case 'seeHandleRecordDetail':
                bounce_Fixed.show($("#handleRecordDetail"))
                var id = $(this).parents("tr").data("id")
                $("#handleRecordDetail").data("id", id)
                getOneSheetDetail()
                break;
        }
    })
    $(".bounce_Fixed").on("click", "[type='btn']", function () {
        var name = $(this).data("name")

        switch (name) {
            // 文件上传/文件发布申请/文件换版/文件换版申请 - 选择保存位置 - 按钮
            case 'sureChooseFolder':
                var chooseNode = $(".ty-colFileTree[data-name='chooseSaveFolder']").find(".ty-treeItemActive");
                var categoryId = chooseNode.data("id");
                // 先判断文件夹是否设置了权限，没有就不能确定

                $.ajax({
                    url : 'getForderAuthDepsAndUsers.do' ,
                    data : {
                        categoryId: categoryId ,
                        type: 1
                    } ,
                    success: function (res) {
                        var success = res.success ;
                        if(success != 1){
                            layer.msg( "校验失败，不能获取权限" ) ; return false ;
                        }
                        var data = res.data ;
                        var catAuthTree = data.catAuthTree  ;
                        sysDeparAndUserList = catAuthTree ;
                        let isUpdate = initData(sysDeparAndUserList); // 大于零表示有权限
                        if(isUpdate > 0){
                            var folderNameObj = chooseNode.parents("li").children(".ty-treeItem").children("span")
                            var str = '<'
                            folderNameObj.each(function (index) {
                                var name = $(this).text()
                                if(index === 0){
                                    str = '<span class="ty-color-red">'+name+'</span>'
                                } else {
                                    str = name + ' / ' + str
                                }
                            })
                            str = '<i class="fa fa-folder"></i>' + str
                            if ($("#fileUpload").is(":visible")) {
                                $("#fileUpload .savePlace").show().html(str)
                                $("#fileUpload .savePlace").data("categoryId", categoryId)
                            } else if ($("#folderUpload").is(":visible")) {
                                $("#folderUpload .savePlace").show().html(str)
                                $("#folderUpload .savePlace").data("categoryId", categoryId)
                            }
                        }else{
                            layer.msg( "选择失败，因为这个文件夹的使用权限尚未设置！" ) ;
                        }
                    }
                });
                bounce_Fixed.cancel()
                break
            case 'sureMoveFile':
                // 下一步
                var thisNode = $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive");
                var chooseNode = $(".ty-colFileTree[data-name='moveTo']").find(".ty-treeItemActive");
                var thisCategoryId = thisNode.data("id");
                var categoryId = chooseNode.data("id");
                let fileId = $(".ty-fileItemActive:visible").data("id")
                // 先判断文件夹是否设置了权限，没有就不能确定
                if (!categoryId) {
                    layer.msg("请选择目标文件夹")
                    return false
                }

                if (thisCategoryId === categoryId) {
                    layer.msg("不能移动到当前文件夹")
                    return false
                }

                if (chooseNode.attr("data-child") === 'true') {
                    layer.msg("该类别下存在子类别，请选择该子类别或其他类别")
                    return false
                }

                if (chooseNode.hasClass("trash")) {
                    // 如果是文件移动到暂时不用的文件 - 不需要权限判断，有特殊提示
                    // 后台验证是否可以移动
                    judgeMove().then(res => {
                        var state = res.state
                        if (state === 2 || state === 4) {
                            layer.msg("无法移动到当前文件夹")
                        } else {
                            if (state === 1) {
                                $("#bounceFixed2_tip .tipMsg").html("移动后，其他职工将无法再见到该文件。<br>确定移动吗？");
                            } else if (state === 3) {
                                $("#bounceFixed2_tip .tipMsg").html('有职工正在借阅该文件或其历史版本。  <a class="ty-color-blue" onclick="seeBorrowPerson('+fileId+')">查看</a><br>移动后，借阅者的借阅将被终止。<br>确定移动吗？');
                            }
                            $("#bounceFixed2_tip .sureBtn").unbind().on("click", function () {
                                sureMove()
                            })
                            bounce_Fixed2.show($("#bounceFixed2_tip")) ;
                        }
                    })
                } else {
                    var toChooseNode = $(".ty-colFileTree[data-name='moveTo'] .ty-treeItemActive")
                    var categoryName = toChooseNode.attr("title");
                    // 后台验证是否可以移动
                    judgeMove().then(res => {
                        var state = res.state
                        if (state === 1) {
                            console.log('datalist', res)
                            var listCatAclOld = res.listCatAclOld // 文件夹这个暂时没用
                            var listCatAclNew = res.listCatAclNew
                            var resLockUuid = res.resLockUuid
                            $("#movedScanSet").data("resLockUuid", resLockUuid)
                            $("#movedScanSet").data("oldAcl", listCatAclOld)
                            $("#movedScanSet").data("firstList", listCatAclNew)
                            $("#movedScanSet").data("secondList", [])
                            $("#movedScanSet").find(".moveToFolderName").html(categoryName)
                            renderList(listCatAclNew, [])
                            $("#movedScanSet input:checkbox").prop("checked", false)
                            bounce_Fixed2.show($("#movedScanSet"))
                        } else if (state === 2 || state === 4) {
                            layer.msg("无法移动到当前文件夹")
                        } else if (state === 3) {
                            // 移动到暂时不用才会出现此传值
                            layer.msg("错误的返回值！")
                        }
                    })
                }
                break
        }
    })

    // 增加需与本文件关联的表格 - 文件选中逻辑
    $("body").on("click", '.fileItemRadioChoose', function () {
        $(this).find("input").prop("checked", true)
        $(this).addClass("active").siblings(".fileItemRadioChoose").removeClass("active")
        // return false
    })


    $(".btnLink").click(function(){
        var type = $(this).data("type");
        if(type == "allClear"){ // 全部清空
            setVal(sysDeparAndUserList, { "havRight":0 } , true)
            $(".selectedNum").html(countSet(sysDeparAndUserList, {'key':"havRight", 'val':1}));
            chargeState();
        }else if(type == "allSelect"){ // 全选
            setVal(sysDeparAndUserList, { "havRight":1}, true)
            $(".selectedNum").html(countSet(sysDeparAndUserList, {'key':"havRight", 'val':1}));
            chargeState();
        }
    })
    // 文件-更多按钮点击事件
    $(".ty-container").on("click","[name='more']",function (e) {
        $(".hel").hide()
        $(this).next(".hel").show();
        e.stopPropagation()
    });
    // 点击body隐藏更多
    $("body").on("click",function () {
        $(".hel").hide()
    });
    // × 点击事件
    $(".clearInput").on({
        "mousedown": function () {
            $(this).prev().val("");
            if ($(this).parents(".ty-search").length > 0) {
                var type =    $("#chooseInFolder").data("type")
                var name = $("#fileNameOrSn2").val()
                getSheetList(1 , 20, type, name)
            }
        },
        "mouseup": function () {
            $(this).prev().focus();
        }
    });

    $("#fileSort li").on("click",function () {
        if($(this).hasClass('ty-active')){
            // 如果点击的是已经点击过的，改为升序，获取升序列表
            $(this).find("i").toggleClass("fa-long-arrow-up");
        } else {
            // 如果点击的是未被点击的，获取降序列表
            $(this).find('.sort').show()
            $(this).siblings().find('.sort').hide()
            $(this).find("i").removeClass("fa-long-arrow-up");
        }
        $(this).addClass("ty-active").siblings().removeClass("ty-active");
        var categoryId = $("#listDoc .ty-treeItemActive").data("id");
        var type = $("#fileSort li.ty-active").attr("type");
        // "1"代表获取的是按时间降序的列表，“2”代表获取的是时间升序的列表，“3”代表获取的是文件编号升序的列表，“4”代表的是获取文件编号降序的列表
        if (type!=='1') {
            if(!$(this).find("i").hasClass('fa-long-arrow-up')){
                $("#fileSort").data('type', 1)
            }else{
                $("#fileSort").data('type', 2)
            }
        } else {
            if($(this).find("i").hasClass('fa-long-arrow-up')){
                $("#fileSort").data('type', 3)
            }else{
                $("#fileSort").data('type', 4)
            }
        }
        getFile( 1,20, categoryId);
    });

    $("#searchSort li").eq(1).on("click",function () {
        if ($(this).find(".sort").is(":visible")) {
            $(this).find("i").toggleClass("fa-long-arrow-up");
        } else {
            $(this).find(".sort").show()
        }
        getGeneralFile(1, 20);
    });
    $("#search_applier").on("change", function () {
        getGeneralFile(1, 20);
    })

    $(".ty-colFileTree").on("click", ".ty-treeItem", function () {

        var treeItemThis = $(this)
        var categoryId = $(this).data("id")
        var avatarName = $(this).parents(".ty-colFileTree").data("name")
        var url = isGeneral || isSuper ? 'getFolderAndChildFolderManager.do' : 'getFolderAndChildFolder.do'

        // 处理逻辑
        treeItemThis.parents(".ty-colFileTree").find(".ty-treeItem").removeClass("ty-treeItemActive")   // 取消之前选中
        treeItemThis.addClass("ty-treeItemActive"); // 设置选中
        treeItemThis.treeItemSlide('toggle')        // icon切换（顶部jQuery方法拓展）

        // // 设置移动弹窗中的暂时不用的文件无法展开
        // if ($(this).parents(".ty-colFileTree").data("name") === 'moveTo' && $(this).hasClass("trash")) {
        //     return false
        // }
        if (avatarName === 'main') {
            $("#fileSort").show()
        }

        if (avatarName === 'main' || (avatarName !== 'main' && $(this).attr("data-child") === 'true')) {
            // 获取子级信息
            $.ajax({
                url: url,
                data: {"categoryId": categoryId, "type": 1},
                success: function (res) {
                    var data = res.data
                    if (!data) {
                        $("#mt_tip_ms").html("连接失败，请刷新重试！");
                        bounce.show($("#mtTip"));
                    } else {
                        // 渲染返回的信息
                        if (treeItemThis.children().eq(0).hasClass('fa-angle-right')) {
                            treeItemThis.next().remove()
                        } else {
                            showTreeData(data, treeItemThis);
                        }
                        if (avatarName === 'main') {
                            $("#fileSort li").eq(0).addClass("ty-active").siblings().removeClass("ty-active");
                            $("#fileSort li").eq(0).find('.sort').show()
                            $("#fileSort li").eq(0).siblings().find('.sort').hide()
                            $("#fileSort li").eq(0).find("i").removeClass("fa-long-arrow-up");
                            $(".ty-switch").removeClass("is-checked")
                            $(".ty-switch input:checkbox").prop("checked", false)

                            var pageInfo = data.pageInfo,
                                fileInfo = data.list;

                            //设置分页信息
                            var currentPageNo   = pageInfo["currentPageNo"],
                                totalPage       = pageInfo["totalPage"],
                                jsonStr = JSON.stringify( { "categoryId" : categoryId} ) ;

                            //设置文件信息
                            var fileListStr = getFileListStr(fileInfo);
                            setPage( $("#ye_con"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                            $(".ty-fileList").html(fileListStr);
                            $(".ty-fileList").attr("type","folder");

                        }
                    }
                }
            })
        }
    })

    $(".ty-colFileTree").on("dblclick", ".ty-treeItem", function () {
        if ($(this).parents(".ty-colFileTree").data("name") !== 'main' && $(this).attr("data-child") === 'false') {
            var sureBtn = $(this).parents(".bonceContainer").find(".sureBtn")
            if (!$(this).prop("disabled")) {
                $(this).parents(".bonceContainer").find(".sureBtn").click()
            }
        }
    })

    /* creator：张旭博，2017-05-06 15:17:01，每一个文件绑定事件（添加样式、获取文件详细信息、） */
    $(".ty-container").on("click",".ty-fileItem",function (){
        $(".ty-fileItem").removeClass("ty-fileItemActive");
        $(this).addClass("ty-fileItemActive");
    });

    $(".ty-container, #chooseInFolder").on("click",".ty-fileItem [type='btn']",function (){
        var name = $(this).attr("name")
        if ($(this).attr("disable") === 'true') {
            return false
        }
        switch (name) {
            case 'seeOnline': // 在线预览
                fileSeeOnline($(this))
                break
            case 'download': // 下载
                fileDownload($(this))
                break
            case 'recovery': // 还原
                recoveryNoUseFile($(this))
                break
            case 'delete': // 删除
                deleteNoUseFile($(this))
                break
            case 'disable': // 禁用
                disableFile($(this))
                break
            case 'basicMsg': // 基本信息
                basicMessage($(this))
                break
            case 'scanSet': // 权限设置
                scanSetBtn('file', $(this))
                break
            case 'move': // 移动
                moveFile($(this))
                break;
            case 'changeVersion': // 换版
            case 'changeVersionApply': // 换版申请
                changeVersion($(this))
                break
            case 'changeVersionRecord': // 换版记录
                chargeRecord($(this))
                break
            case 'changeFileName': // 修改文件名称
                changeFileName($(this))
                break
            case 'changeFileNo': // 修改文件编号
                changeFileNo($(this))
                break
            case 'fileAbolish': // 文件废止
                fileAbolish($(this))
                break
            case 'fileAbolishApply': // 文件废止申请
                fileAbolish($(this))
                break
            case 'fileReuse': // 文件复用
                fileReuse($(this))
                break
            case 'changeSheetVersion': // 换版
                changeSheetVersion($(this))
                break;
            case 'sheetRelate': // 表格关联
                sheetRelate($(this))
                break
            case 'relateSheet': // 相关表格
                relateSheet($(this))
                break
            case 'sheetRelateRemove': //解除关联
                sheetRelateRemove($(this))
                break
            case 'sheetHandleRecord': // 表格关联的操作记录
                sheetHandleRecord($(this), 'file')
                break
            case 'sheetRelateHandleRecord': // 表格关联的操作记录
                sheetRelateHandleRecord($(this))
                break
            case 'changeSheetMsg': // 修改表格信息
                changeSheetMsg($(this))
                break;
            case 'relateRecord': // 关联记录
                relateRecord($(this))
                break;
            case 'relate': // 所关联的文件
                relate($(this))
                break;
            case 'related': // 曾关联的文件
                related($(this))
                break;
        }
    });
    /* creator：张旭博，2017-05-06 15:17:01，为高级搜索 按文件类别搜索 的文件夹绑定事件（添加样式、获取自己文件夹信息、） */
    $(".ty-searchContent").on("click",".ty-folderItem",function (){
        $(this).addClass("ty-folderItemActive").siblings().removeClass("y-folderItemActive");
        var folderId        = $(this).attr("id"),
            folderParent    = $(this).find(".ty-folderParent").html(),
            folderName      = $(this).find(".ty-folderName").html(),
            nextFolderParent= '';
        folderParent === '' ? nextFolderParent = folderName :nextFolderParent = folderParent + '/' + folderName;
        //获取子级文件或者文件夹
        getChildFolder(folderId,nextFolderParent);
    });
    // creator:侯杏哲 2017-11-18  查看设置的文件夹toggle
    $("#allRight").on("click" , "li>div.departName" , function () {
        changeVal($(this), "left");
    });
    $("#nowRight").on("click" , "li>div.departName" , function () {
        changeVal($(this), "right");
    });

    $("#advancedSearch .ty-secondTab li").on("click",function () {
        var index = $(this).index();
        $(this).addClass("ty-active").siblings('li').removeClass("ty-active");
        $("#advancedSearch .searchCon").eq(index).show().siblings(".searchCon").hide();
        $("#advancedSearch").data("type",index);
    })

    $("#advancedSearchBtn").on("click",function () {
        $("#searchSort").hide()
        $("#fileNameOrSn").val("");
        var type = $("#advancedSearch").data("type");
        type === 0 ? advancedSearch_file(1,20): advancedSearch_folder(1,20);
    })
    $("#fileNameOrSn").on("keydown",function (e) {
        var keyNum;
        keyNum = window.event ? e.keyCode : e.which;
        if(keyNum === 13){
            searchBtn();
        }
    })
    $("input:radio[name='isNeedOther']").on('change', function () {
        if ($(this).val() === '0') {
            $(this).parents(".bonceContainer").find(".chooseApprover").val("")
        }
    })

    $(".chooseApprover").on("change", function () {
        if ($(this).val() !== '') {
            $(this).parents(".bonceContainer").find("input:radio[name='isNeedOther']").prop("checked", true)
        }
    })
    $("body").on('click', '.delFile', function () {
        $(this).parents('.file_item').remove()
        var uid = $(this).attr("fileUid")
        if (uid) {
            var option = {type: 'fileUid', fileUid: uid}
            fileDelAjax(option)
        }
    })

    $("#movedScanSet").on("click", "[type='btn']", function () {
        var name= $(this).data("name")
        switch (name) {
            case 'delUser':
                var firstList = $("#movedScanSet").data("firstList")
                var secondList = $("#movedScanSet").data("secondList")
                var userId = $(this).data("id")

                var newFirstList = firstList.filter(item => item.user !== userId)
                var newSecondList = secondList.concat(firstList.filter(item => item.user === userId))

                $("#movedScanSet").data("firstList", newFirstList)
                $("#movedScanSet").data("secondList", newSecondList)
                renderList(newFirstList, newSecondList)
                break
            case 'addUser':
                var firstList = $("#movedScanSet").data("firstList")
                var secondList = $("#movedScanSet").data("secondList")
                var userId = $(this).data("id")
                var newFirstList = firstList.concat(secondList.filter(item => item.user === userId))
                var newSecondList = secondList.filter(item => item.user !== userId)

                $("#movedScanSet").data("firstList", newFirstList)
                $("#movedScanSet").data("secondList", newSecondList)
                renderList(newFirstList, newSecondList)

                break
            case 'sureMove':
                var isSelect = $("#movedScanSet input:checkbox").prop("checked")
                if (isSelect) {
                    sureMove()
                } else {
                    layer.msg("请确认权限是否已调整完成，并需在下方勾选框内勾选")
                }
                break
        }
    })

    //wyu：重现初始化界面。

    //wyu：重新初始化界面。
    $('#upload-folder-01').parents('.upload_avatar').html('<input name="file" type="file" id="upload-folder-01"></div>');
    let groupUuid=null;
    $('#upload-folder-01').kendoUpload({
        localization: {
            select: "选择文件夹",
            headerStatusUploading: "正在上传",
            headerStatusUploaded: "等待下一个…",//多文件上传，不是最后一个文件不能提示："上传结束"
            headerStatusPaused: "暂停上传",
            invalidFileExtension: "系统暂不支持该类型文件的上传",
            invalidMaxFileSize: "文件超过1.5G"
        },
        async: {
            chunkSize: 1024 * 1024 * 2 - 500,// bytes
            saveUrl: $.webRoot+"/uploads/uploadfyByKendo.do",//断点续传后端。
            removeUrl: $.webRoot+"/uploads/removeByKendo.do",//删除已上传的后台文件。
            autoRetryAfter: 1000,
            maxAutoRetries: Number.MAX_VALUE,
            concurrent: true,
            autoUpload: true
        },
        validation: {
            maxFileSize: 1024 * 1024 * 512 * 3 //1.5G
        },
        directory: true,
        directoryDrop:true,
        upload: function(e) {
            e.data = {module : '文件与资料', userId : sphdSocket.user.userID, groupUuid : groupUuid};
            //wyu：添加token
            e.XMLHttpRequest.addEventListener('readystatechange',function(e) {if (e.currentTarget.readyState == 1 /*OPENED*/) {e.currentTarget.setRequestHeader('token', auth.getToken())}})
        },
        select: function(e) {
            //wyu：清空数据
            $("#chooseSaveFolderBtn").attr("class","ty-btn ty-btn-gray ty-circle-3");
            filelist.length=0;//清空确认文件夹上传按钮提交的对象数组。
            this.removeAllFiles();//清空之前上传的文件列表。
            noOperated = true;//清空文件列表ul的鼠标操作标志，上传过程文件列表随进度事件的文件位置变化滚动条位置。
            //wyu：1.122文件与资料优化5 3.2文件夹名称，获取本地文件夹的根路径，保存到"文件夹名称"输入框。
            if(e.files.length>0) {
                let index = e.files[0].rawFile.webkitRelativePath.indexOf('/');
                if(index>=0) {
                    $('#folder_name').val(e.files[0].rawFile.webkitRelativePath.substr(0,index));
                }
            }
            groupUuid=sphdSocket.uuid();
            //延时30秒检查是否有上传
            setTimeout(function(){
                if($('.k-file-success').length==0 && $('.k-file-progress').length==0) {
                    if($('.k-file-invalid').length==0) {
                        //修改提示内容为没有文件上传。
                        $('.k-upload-status-total').html("没有文件被上传，请检查所选目录中有文件！");
                    } else {
                        //修改提示内容为没有符合条件文件上传。
                        $('.k-upload-status-total').html("没有文件被上传，请确认目录中的文件符合要求！");
                    }
                }
            },30000);//延时30秒检查是否有上传，设置时间太短有可能服务器端未响应导致提示错误。
            updateStatusTotal();
        },
        progress: function (e) {
            // console.log("progressE",e)
            // console.log(e.files[0].name, $('li[data-uid=' + e.files[0].uid + ']').position().top - $('.k-upload-files').position().top <=0,$('.k-upload-files').position().top + $('.k-upload-files').outerHeight() < $('li[data-uid=' + e.files[0].uid + ']').position().top + $('li[data-uid=' + e.files[0].uid + ']').outerHeight())
            if(noOperated && (
                    $('li[data-uid=' + e.files[0].uid + ']').position().top - $('.k-upload-files').position().top <=0
                    || $('.k-upload-files').position().top + $('.k-upload-files').outerHeight() < $('li[data-uid=' + e.files[0].uid + ']').position().top + $('li[data-uid=' + e.files[0].uid + ']').outerHeight()
                )) {
                //wyu：将滚动条置于底部
                // $('.k-upload-files').scrollTop(1024*1024*1024);
                //wyu：将滚动条随进度事件滚动，可以通过鼠标进入滚动区域或键盘输入ESC或者ENTER停止。
                $('.k-upload-files').scrollTop($('li[data-uid=' + e.files[0].uid + ']').offset().top - $('.k-upload-files').position().top);
            }
            if(e.percentComplete>0&&delayLogout.stopILTime==null) {
                // console.log("e.percentComplete>0&&stopILTime==null",e.percentComplete>0&&delayLogout.stopILTime==null,delayLogout.stopILTime==null,delayLogout.stopILTime)
                delayLogout.setDelayLogoutTime(new Date().getTime() + 72 * 3600 * 1000);//三天
            }
            //wyu：单个文件的上传进度，测试用
            // console.log("upload percentage:"+e.percentComplete+"%");
            // console.log('e', e);
        },
        success: function (e) {
            // console.log(e)
            // console.log(e.operation)
            if(typeof e.response.token === 'string' && e.response.token.length > 0) {
                auth.saveToken(e.response.token)
            }
            switch(e.operation) {
                case 'upload':
                    //response对象添加到确认上传文件夹的提交的数组。
                    let response = e.response;
                    filelist.push(response);
                    //第一个文件上传成功后执行
                    updateStatusTotal();
                    delayLogout.setDelayLogoutTime(new Date(response.createDate).getTime() + 72 * 3600 * 1000);//三天
                    break;
                case 'remove':
                    let files = e.files;
                    filelist = filelist.filter(function (item) {
                        for(let index in files) {
                            let name = files[index].name.split("\0")[0];
                            if(item.originalRelativePath == name) {
                                return false;
                            }
                        }
                        return true;
                    });
                    updateStatusTotal();
                    break;
            }
            // // console.log(response);
            // //wyu：原始文件名，可用于默认文件名和编号
            // console.log("originalFilename:",response.originalFilename);
            // //wyu：上传后文件路径
            // console.log("filename:",response.filename);
            // //wyu：文件扩展名
            // console.log("lastName:",response.lastName);
            // //wyu：相对路径
            // console.log("relativePath:",response.relativePath);
            // //wyu：文件下载地址，不是预览地址，预览地址可以参考原预览代码
            // console.log($.uploadUrl + response.filename);
            // if (e.operation == "upload") {
            //     console.log("Successfully uploaded " + files.length + " files");
            // }
            // // console.log('e', e);
        },
        remove: function(e) {
            e.async = false;
            e.files.forEach(function(file){
                //wyu：文件名后加"\0"和文件uid，服务器端用uid处理，避免同名文件重复上传导致的BUG
                file.name = file.name + "\0" + file.uid
                //wyu：处理上传完成后点击限制上传的文件后面的×，提示信息。
                if(typeof(file.validationErrors) != 'undefined') {
                    updateStatusTotal();
                }
            })
            e.data = {userId : sphdSocket.user.userID};
        }
    });
    let intevalUpdateStatusTotal=null;
    function updateStatusTotal() {
        //wyu：只启动一个定时器。
        intevalUpdateStatusTotal = intevalUpdateStatusTotal || setInterval(function () {
            let progressNum=$('.k-file-progress').length;
            let successNum=$('.k-file-success').length;
            let invalidNum=$('.k-file-invalid').length;
            let count=progressNum+successNum+invalidNum;
            if(count>=0){
                let folderStatus='共'+count+'个文件';
                if(successNum>0) {
                    folderStatus+='，已上传'+successNum+'个';
                } else if(progressNum>0) {
                    folderStatus+='，正在上传'+progressNum+'个';
                }
                if(invalidNum>0) {
                    folderStatus += '，'+invalidNum + '个无法上传';
                }
                folderStatus+='。';
                $('#folderStatus').html(folderStatus);
                if(count == 0){
                    $("#chooseSaveFolderBtn").attr("class","ty-btn ty-btn-gray ty-circle-3");

                }
            }
            // console.log('updateStatusTotal',$('.k-file-progress').length,$('.k-file-success').length, filelist.length)
            if ($('.k-file-progress').length == 0) {
                if ($('.k-file-success').length > 0) {
                    //修改默认单个文件上传完成后的提示内容为全部文件上传完成。
                    $('.k-upload-status-total').html("上传结束！");
                    $("#chooseSaveFolderBtn").attr("class","ty-btn ty-btn-blue ty-circle-3")
                    // if ($('.k-file-invalid').length == 0) {
                    //     $('.k-upload-status-total').html("上传结束，共上传" + filelist.length + "个文件！");
                    // } else {
                    //     $('.k-upload-status-total').html("上传结束，共上传" + filelist.length + "个文件，另有" + $('.k-file-invalid').length + "个文件不符合上传要求！");
                    // }
                } else {//validation导致没有文件上传时，不会触发任何事件,此处else代码不会被执行。//code not used
                    $('.k-upload-status-total').html("没有可以上传的文件！");
                }
                clearInterval(intevalUpdateStatusTotal);
                intevalUpdateStatusTotal = null;
            }
        }, 300);
    }
    //wyu：后台清除已上传文件。
    $(window).bind('beforeunload',function (){//直接刷新页面，删除已上传的后台文件列表。
        let uploaderFolder = $('#upload-folder-01').data('kendoUpload');
        if (uploaderFolder) {
            if(uploaderFolder.getFiles().length>0) {
                removeFiles(uploaderFolder.getFiles())
                filelist.length=0;
            }
        }

        let uploaderFile = $('#upload-file-01').data('kendoUpload');
        if (uploaderFile) {
            if(uploaderFile.getFiles().length>0) {
                removeFiles(uploaderFile.getFiles())
            }
        }
        kendo.destroy(document.body);
    });
    function removeFiles(files) {
        let fileNamesList = Array();
        files.forEach(function(file){
            fileNamesList.push(file.name + "\0" + file.uid);
        });
        $.ajax({url: $.webRoot+'/uploads/removeFilesByKendo.do',data:{fileNamesList:JSON.stringify(fileNamesList),userId: sphdSocket.user.userID}, beforeSend: function() {}, error: function() {}, complete: function() {}});
    }
    //wyu：如果开始下载后，用户鼠标在文件列表ul范围操作或键盘输入ESC或者ENTER，就不再自动滚动。
    $(document).mousewheel(function (e) {
        operated(e);
    });
    $(document).mousedown(function (e) {
        operated(e);
    });
    $(document).mousemove(function (e) {
        operated(e);
    });
    $(document).mouseup(function (e) {
        operated(e);
    });
    $(document).keydown(function (e) {
        operated(e);
    });
    $(document).keyup(function (e) {
        operated(e);
    });
    var noOperated;
    function operated(e) {
        //wyu：终止上传列表自动滚动的条件：鼠标进入控件滚动的范围或者键盘输入ESC或者回车键。
        if($('.k-upload-files').offset()!=undefined
            && (
                //mouse range
                e.pageY!=undefined
                && e.pageY>=$('.k-upload-files').offset().top
                && e.pageY<=$('.k-upload-files').offset().top+$('.k-upload-files').outerHeight()
                && e.pageX>=$('.k-upload-files').offset().left
                && e.pageX<=$('.k-upload-files').offset().left+$('.k-upload-files').outerWidth()
                ||
                //key press
                e.keyCode!=undefined
                && (
                    e.keyCode==13//Enter
                    ||e.keyCode==27//Esc
                )
            ) && $('#reLogin').is(':hidden') //wyu：未锁屏
        ) {
            noOperated=false;
        }
    }
});

function setFileInput(fileArr) {
    if (fileArr.length < 2) {
        $("#upload-file-01").next("span").html('选择文件')
        $(".show_fileOrFiles").show()
        var response = fileArr[0]
        if (fileArr.length === 0) {
            $("#fileUpload input[name='name']").val('')
            $("#fileUpload input[name='fileNo']").val('')
        } else {
            $("#upload-file-01").next("span").html('选择下一个文件')
            if (response.originalFilename) {
                if(response.lastName.length>0) {
                    $("#fileUpload input[name='name']").val(response.originalFilename.substring(0, response.originalFilename.length-response.lastName.length-1))
                    $("#fileUpload input[name='fileNo']").val(response.originalFilename.substring(0, response.originalFilename.length-response.lastName.length-1))
                } else {//wyu：扩展名为空的情况，目前not used
                    $("#fileUpload input[name='name']").val(response.originalFilename)
                    $("#fileUpload input[name='fileNo']").val(response.originalFilename)
                }
            }
        }
    } else {
        $("#upload-file-01").next("span").html('选择下一个文件')
        $(".show_fileOrFiles").hide()
        $("#fileUpload input[name='name']").val('')
        $("#fileUpload input[name='fileNo']").val('')
    }
}
// creator: 张旭博, 2020-04-28 15:25:30, 初始化页面
function initPage() {
    if(isGeneral){
        $("#newFoler").show()
    } else {
        $("#newFoler").hide()
    }
    // 第一级目录加载
    getFirstDoc('main') ;
}

// creator: 张旭博，2019-04-03 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 新增客户
            case 'fileUpload':
                var state = 0
                var length = 1

                var isNeedOther = $("#fileUpload input:radio:checked").val()
                if (isNeedOther === '1' && $("#fileUpload .chooseApprover").val() === ''){
                    state++
                }
                $("#fileUpload input[require]:visible").each(function () {
                    if ($(this).val() === "") {
                        state ++
                    }
                })
                if ($("#fileUpload").data("fileArr")) {
                    length = $("#fileUpload").data("fileArr").length
                }
                if (length > 1) {
                    $("#sureUploadNewFileBtn").html("下一步")
                } else {
                    $("#sureUploadNewFileBtn").html("确定")
                }
                if ( state > 0 || $("#fileUpload .savePlace").html() === '' || $("#fileUpload .k-upload-status").text() !== '上传成功') {
                    $("#sureUploadNewFileBtn").prop("disabled",true)
                }else {
                    $("#sureUploadNewFileBtn").prop("disabled",false)
                }
                break;
            case 'changeVersion':
                var state = 0
                var isNeedOther = $("#fileUpload input:radio:checked").val()
                if (isNeedOther === '1' && $("#fileUpload .chooseApprover").val() === ''){
                    state++
                }
                if ($("#fileUpload").data("fileInfo") === '') {
                    state ++
                }

                if ( state > 0 ) {
                    $("#sureUploadNewFileBtn").prop("disabled",true)
                }else {
                    $("#sureUploadNewFileBtn").prop("disabled",false)
                }
                break
            case 'fileAbolish':
                var state = 0
                var isNeedOther = $("#fileAbolish input:radio:checked").val()
                if (isNeedOther === '1' && $("#fileAbolish .chooseApprover").val() === ''){
                    state++
                }
                if ( state > 0 ) {
                    $("#sureFileAbolishBtn").prop("disabled",true)
                }else {
                    $("#sureFileAbolishBtn").prop("disabled",false)
                }
                break
            case 'folderUpload':
                //wyu：1.122文件与资料优化5 3.2文件夹名称，增加"文件夹名称"输入框不为空判断。
                if($("#folderUpload .savePlace").html() === '' || filelist.length === 0 || $('.k-file-progress').length !== 0 || $('#folder_name').val().trim().length === 0){
                    $("#sureUploadNewFolderBtn").prop("disabled",true)
                }else {
                    $("#sureUploadNewFolderBtn").prop("disabled",false)
                }
                break
            case 'chooseSaveFolder':
                let isDis = true
                if ($("#fileUpload").is(":visible")) {
                    if($("#chooseSaveFolder .ty-treeItemActive").length > 0 && $("#chooseSaveFolder .ty-treeItemActive").attr("data-child") === 'false'){
                        isDis = false
                    }

                } else if ($("#folderUpload").is(":visible")) {
                    if($("#chooseSaveFolder .ty-treeItemActive").length > 0 ){
                        isDis = false
                    }
                } else {
                    if($("#chooseSaveFolder .ty-treeItemActive").length > 0 && $("#chooseSaveFolder .ty-treeItemActive").attr("data-child") === 'false'){
                        isDis = false
                    }
                }
                $("#sureChooseFolderBtn").prop("disabled",isDis );

                break;
            case 'newSheetRelateFile':
                var state = 0
                $("#newSheetRelateFile [require]:visible").each(function () {
                    var val = $(this).val()
                    if (val === '') {
                        state ++
                    }
                })
                if ($("#newSheetRelateFile .fileShowList .file_item").length === 0) {
                    state ++
                }
                $("#newSheetRelateFileBtn").prop("disabled", state > 0 );

                break;
        }

    });
}

// creator: 侯杏哲,2017-12-04 渲染获取的文件夹数据和页面 ( 适用于主页面文件夹树和移动文件弹框的文件夹树 )
function showTreeData(data , treeItemThis) {
    // data - 获取的数据 ， treeItemThis - 点击的那个文件夹对象 ， treeObj - 点击的树对象

    // 处理子文件夹数据
    var listNotice      = data["parentFolder"] || [];
    var listNoticeChild = data["childFolder"];
    var level = parseInt(treeItemThis.parent().parent().attr("level")) ;
    var nextLevel = level + 1 ;
    var levelStr = "";
    levelStr += '<div class="level'+nextLevel+'" level="'+nextLevel+'">' ;
    for(var i in listNoticeChild){
        // childStatus null：无子文件夹 '1':有子文件夹
        levelStr += '<li>' +
                    '   <div class="ty-treeItem" title="'+ listNoticeChild[i].name +'" data-id="'+listNoticeChild[i].id+'" data-child="'+(listNoticeChild[i].childStatus === '1')+'">' +
                    (listNoticeChild[i].childStatus === '1'?'<i class="fa fa-angle-right"></i>':'<i class="ty-fa"></i>') +
                    '       <i class="fa fa-folder"></i>' +
                    '       <span>'+listNoticeChild[i]["name"]+'</span>' +
                    '   </div>' +
                    '</li>'
    }
    levelStr += '</div>';
    localStorage.setItem("noticemax",listNotice["childStatus"] ); // childStatus : 1 是有文件夹; 2 是有文件; null标识什么都没有

    if (listNoticeChild.length > 0) {
        if (treeItemThis.next().length === 0) {
            treeItemThis.after(levelStr);
        }
    }

}

//------------------------- 文件上传 ---------------------------//

// creator: 张旭博，2021-07-21 10:05:33，kende 文件上传初始化
function initKendoFileUpload(type) {

    $('#upload-file-01').parents('.upload_avatar').html('<input name="file" type="file" id="upload-file-01"></div>');
    if (type === 'changeVersion') {
        // 换版
        $('#upload-file-01').kendoUpload({
            multiple: false,//只允许单文件上传，由于kendoUpload提交到async.removeUrl的参数只有文件名，当多文件上传时删除无法区别同名的文件。
            localization: {
                select: "选择文件",
                headerStatusUploading: "正在上传",
                headerStatusUploaded: "上传结束",
                headerStatusPaused: "暂停上传",
                invalidFileExtension: "系统暂不支持该类型文件的上传",
                invalidMaxFileSize: "文件超过1.5G"
            },
            async: {
                chunkSize: 1024 * 1024 * 2 - 500,// bytes
                saveUrl: $.webRoot+"/uploads/uploadfyByKendo.do",//断点续传后端。
                removeUrl: $.webRoot+"/uploads/removeByKendo.do",//删除已上传的后台文件。
                autoRetryAfter: 1000,
                maxAutoRetries: Number.MAX_VALUE,
                autoUpload: true
            },
            validation: {
                maxFileSize: 1024 * 1024 * 512 * 3 //1.5G
            },
            upload: function(e) {
                e.data = {module : '文件与资料', userId : sphdSocket.user.userID};
                //wyu：添加token
                e.XMLHttpRequest.addEventListener('readystatechange',function(e) {if (e.currentTarget.readyState == 1 /*OPENED*/) {e.currentTarget.setRequestHeader('token', auth.getToken())}})
            },
            select: function() {
                $("#fileUpload").data("fileInfo", '')
                //wyu：清空数据————由于kendoUI的BUG，如果文件很小，上传很快会导致headerStatusUploaded被removeAllFiles方法清空。
                // this.removeAllFiles();//清空之前上传的文件列表。
            },
            progress: function (e) {
                //wyu：上传进度，测试用
                // console.log("upload percentage:"+e.percentComplete+"%");
                if(e.percentComplete>0&&delayLogout.stopILTime==null) {
                    // console.log("e.percentComplete>0&&stopILTime==null",e.percentComplete>0&&delayLogout.stopILTime==null,delayLogout.stopILTime==null,delayLogout.stopILTime)
                    delayLogout.setDelayLogoutTime(new Date().getTime() + 72 * 3600 * 1000);//三天
                }
                // var files = e.files;
                // cuts = []
                console.log('progressE', e);
                // console.log('progress is touched');
            },
            success: function (e) {
                // console.log("successE",e)
                if(typeof e.response.token === 'string' && e.response.token.length > 0) {
                    auth.saveToken(e.response.token)
                }
                switch(e.operation) {
                    case 'upload':
                        var response = e.response;
                        //wyu：文件下载地址，不是预览地址，预览地址可以参考原预览代码
                        console.log($.uploadUrl + response.filename);
                        console.log("response", response)
                        //wyu：原始文件名
                        console.log("originalFilename:", response.originalFilename);
                        //wyu：上传后文件路径
                        console.log("filename:", response.filename);
                        //wyu：文件扩展名
                        console.log("lastName:", response.lastName);
                        //wyu：相对路径（空）
                        console.log("relativePath:", response.relativePath);
                        delayLogout.setDelayLogoutTime(new Date(response.createDate).getTime() + 72 * 3600 * 1000);//三天
                        var fileInfo = {
                            path: response.filename,
                            size: response.size,
                            version: response.lastName
                        }
                        if (response.originalFilename) {
                            if(response.lastName.length>0) {
                                $("#fileUpload input[name='name']").val(response.originalFilename.substring(0, response.originalFilename.length-response.lastName.length-1))
                                $("#fileUpload input[name='fileNo']").val(response.originalFilename.substring(0, response.originalFilename.length-response.lastName.length-1))
                            } else {//wyu：扩展名为空的情况，目前not used
                                $("#fileUpload input[name='name']").val(response.originalFilename)
                                $("#fileUpload input[name='fileNo']").val(response.originalFilename)
                            }
                            $("#fileUpload").data("fileInfo", fileInfo)
                        } else {
                            $("#fileUpload").data("fileInfo", '')
                        }
                        break;
                    case 'remove':
                        $("#fileUpload input[name='name']").val('');
                        $("#fileUpload").data("fileInfo", '')
                        break;
                }
            },
            remove: function(e) {
                e.async = false;
                e.files.forEach(function(file){
                    //wyu：文件名后加"\0"和文件uid，服务器端用uid处理，避免同名文件重复上传导致的BUG
                    file.name = file.name + "\0" + file.uid
                })
                e.data = {userId : sphdSocket.user.userID};
            }
        });
    } else {
        var groupUuid = sphdSocket.uuid();
        $('#upload-file-01').kendoUpload({
            multiple: true,//允许多文件上传，由于kendoUpload提交到async.removeUrl的参数只有文件名，当多文件上传时删除无法区别同名的文件。
            localization: {
                select: "选择文件", // 上传一个文件成功后改为选择下一个文件
                headerStatusUploading: "正在上传",
                headerStatusUploaded: "上传成功",
                headerStatusPaused: "暂停上传",
                invalidFileExtension: "系统暂不支持该类型文件的上传",
                invalidMaxFileSize: "文件超过1.5G"
            },
            async: {
                chunkSize: 1024 * 1024 * 2 - 500,// bytes
                saveUrl: $.webRoot+"/uploads/uploadfyByKendo.do",//断点续传后端。
                removeUrl: $.webRoot+"/uploads/removeByKendo.do",//删除已上传的后台文件。
                autoRetryAfter: 1000,
                maxAutoRetries: Number.MAX_VALUE,
                autoUpload: true
            },
            validation: {
                maxFileSize: 1024 * 1024 * 512 * 3 //1.5G
            },
            upload: function(e) {
                e.data = {module : '文件与资料', userId : sphdSocket.user.userID, groupUuid: groupUuid};
                //wyu：添加token
                e.XMLHttpRequest.addEventListener('readystatechange',function(e) {if (e.currentTarget.readyState == 1 /*OPENED*/) {e.currentTarget.setRequestHeader('token', auth.getToken())}})
            },
            progress: function (e) {
                //wyu：上传进度，测试用
                // console.log("upload percentage:"+e.percentComplete+"%");
                if(e.percentComplete>0&&delayLogout.stopILTime==null) {
                    // console.log("e.percentComplete>0&&stopILTime==null",e.percentComplete>0&&delayLogout.stopILTime==null,delayLogout.stopILTime==null,delayLogout.stopILTime)
                    delayLogout.setDelayLogoutTime(new Date().getTime() + 72 * 3600 * 1000);//三天
                }
                // var files = e.files;
                // cuts = []
                console.log('progressE', e);
                // console.log('progress is touched');
            },
            success: function (e) {
                // console.log("successE",e)
                if(typeof e.response.token === 'string' && e.response.token.length > 0) {
                    auth.saveToken(e.response.token)
                }
                switch(e.operation) {
                    case 'upload':
                        var response = e.response;
                        //wyu：文件下载地址，不是预览地址，预览地址可以参考原预览代码
                        console.log($.uploadUrl + response.filename);
                        console.log("response", response)
                        //wyu：原始文件名
                        console.log("originalFilename:", response.originalFilename);
                        //wyu：上传后文件路径
                        console.log("filename:", response.filename);
                        //wyu：文件扩展名
                        console.log("lastName:", response.lastName);
                        //wyu：相对路径（空）
                        console.log("relativePath:", response.relativePath);
                        delayLogout.setDelayLogoutTime(new Date(response.createDate).getTime() + 72 * 3600 * 1000);//三天


                        var fileArr = $("#fileUpload").data("fileArr") || []
                        fileArr.push(response)
                        $("#fileUpload").data("fileArr", fileArr)
                        setFileInput(fileArr)
                        $("#fileUpload").data("groupUuidObj", {type: 'groupUuid', groupUuid: response.groupUuid})
                        break;
                    case 'remove':
                        // 从上传的文件中删除移除的文件
                        var fileArr = $("#fileUpload").data("fileArr") || []
                        fileArr.splice(fileArr.findIndex(function (item) {
                            return item.fileUid === e.files[0].uid
                        }), 1)
                        $("#fileUpload").data("fileArr", fileArr)
                        setFileInput(fileArr)
                        break;
                }
            },
            remove: function(e) {
                e.async = false;
                e.files.forEach(function(file){
                    //wyu：文件名后加"\0"和文件uid，服务器端用uid处理，避免同名文件重复上传导致的BUG
                    file.name = file.name + "\0" + file.uid
                })
                e.data = {userId : sphdSocket.user.userID};
            }
        });
    }

}

// creator: 张旭博, 2020-04-27 08:58:11, 文件上传 - 确定
function sureUploadNewFile(){
    //获取表单内容和接口需要的参数
    var fileInfo = $("#fileUpload").data("fileInfo");

    //参数json
    var data = {
        userID: sphdSocket.user.userID,
        category: $("#fileUpload .savePlace").data("categoryId"),
        path: fileInfo.path,
        size: fileInfo.size,
        version: fileInfo.version,
        changeNum: 0,
        module: '文件与资料',
        isNeedOther: false
    }
    var files = []

    // 获取文件列表
    var fileArr = $("#fileUpload").data("fileArr") || []

    fileArr.forEach(function (item) {
        var fileName = ''
        if (item.originalFilename) {
            if(item.lastName.length>0) {
                fileName = item.originalFilename.substring(0, item.originalFilename.length-item.lastName.length-1)
            } else {//wyu：扩展名为空的情况，目前not used
                fileName = item.originalFilename
            }
        }
        files.push({
            name: fileName, // 文件名字
            fileSn: fileName, // 文件编号
            path: item.filename, // 文件存储路径
            size: item.size, // 文件大小
            version: item.lastName // 文件后缀
        })
    })
    if (fileArr.length === 1) {
        files[0].name = $("#fileUpload [name='name']").val()
        files[0].fileSn = $("#fileUpload [name='fileNo']").val()
    } else{
        let radio = $("#confirmSignIn input:radio:checked").val()
        if (!radio) {
            layer.msg("请选择签收模式！")
            return false
        } else {
            data.noticeType = radio
        }
    }
    data.files = JSON.stringify(files)
    data.isNeedOther = $("#fileUpload input:radio:checked").val()
    if (data.isNeedOther === '1') {
        // 选择了审批人
        data.type = 1
        data.auditName = $(".chooseApprover").find("option:selected").html()
        data.auditor = $(".chooseApprover").val()
    } else {
        // 由文管直接发布
        if (isGeneral) {
            data.type = 2
        } else {
            data.type = 1
        }
    }
    var content = $("#fileUpload [name='content']").val()
    data.content = content
    console.log(data)
    $.ajax({
        url: 'resCentrenAffirdFile.do',
        data: data,
        success: function (res) {
            var state = parseInt(res.data)
            if (state === 1) {
                layer.msg("操作成功")
                $("#upload-file-01").data("kendoUpload").clearAllFiles();
                var groupUuidObj = $("#fileUpload").data("groupUuidObj")
                cancelFileDel(groupUuidObj)
                bounce.cancel();
                bounce_Fixed.cancel();
            } else if (state === 2) {
                layer.msg("已上传文件中有编号重复的，请检查确认!")
            } else if (state === 3) {
                layer.msg("文件夹不存在!")
            } else {
                $("#mtTip #mt_tip_ms").html("操作失败!") ;
                bounce.show($("#mtTip"));
            }
        }
    })
}

// creator: 张旭博, 2020-04-30 16:14:15, 文件夹上传 - 确定
function sureUploadNewFolder() {
    if(filelist.length==0) {
        $("#mt_tip_ms").html("没有能上传的文件！");
        bounce.show($("#mtTip"));
    } else {
        var formData = {}
        var categoryId = $("#folderUpload .savePlace").data("categoryId")
        formData.parent = categoryId;   //父文件夹id，测试用父文件夹id=1902请用界面选择的上传位置文件夹id替换此处数值
        formData.module = '文件与资料';
        //wyu：1.122文件与资料优化5 3.2文件夹名称，用"文件夹名称"替换filelist中的根路径。
        let path=$('#folder_name').val().trim()+'/';
        if(path.length>0){
            filelist.forEach(function (f, i, arr) {
                //wyu：查看了后端保存代码，只修改relativePath就可以。
                // arr[i].originalRelativePath=f.originalRelativePath.replace(/[^\/]+\//,path);
                arr[i].relativePath=f.relativePath.replace(/[^\/]+\//,path);
            })
        }
        formData.files = JSON.stringify(filelist);//已上传的文件对象数组
        $.ajax({
            url: $.webRoot+"/res/resCentrenAffirdFolder.do",
            data: formData,
            success: function (r) {
                console.log('r', r)
                if(r.success>0){//确认上传成功
                    $("#mt_tip_ms").html(r.data);//提示信息。
                    $("#upload-folder-01").data("kendoUpload").clearAllFiles();
                    bounce.show($("#mtTip"));
                } else if(r.error!=null) {//提交失败
                    // alert(data.error.message);
                    $(".tipWord").html(r.error.message);//错误提示信息。
                    bounce_Fixed.show($("#F_errorTip"));
                }
            }
        });
    }
}

// creator：张旭博，2017-05-15 16:39:08，中断上传，关闭弹窗
function chargeXhr(){
    if(curXhr){ curXhr.abort(); curXhr = null;   }
    bounce.cancel();
    loading.close();
}

//------------------------- 文件上的所有按钮功能 ---------------------------//

// creator: 张旭博，2018-05-24 11:04:53，文件功能 - 预览
function fileSeeOnline(selector) {
    let isChargeRecord = $("#resHistory").is(":visible")
    let isSheetPage = $("#page_sheetRelate").is(":visible")
    if(!isChargeRecord && !isSheetPage){
        // 获取文件信息(后台要求的，不知道为啥)
        getFileInfo(selector);
        //记录点击次数
        var currentFileId = selector.parents(".ty-fileItem").data("id");
        $.ajax({
            url:"../res/viewAndDownloadRecord.do",
            data:{
                "id"  : currentFileId,
                "type": 1
            },
            success: function(data){
                if(data["success"] !== 1){
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }

    seeOnline(selector)
}

// creator: 张旭博，2018-05-24 11:05:09，文件功能 - 下载
function fileDownload(selector) {
    //记录点击次数
    if (!$("#resHistory").is(":visible") && !$("#page_sheetRelate").is(":visible")) {
        var currentFileId = selector.parents(".ty-fileItem").data("id");
        $.ajax({
            url:"../res/viewAndDownloadRecord.do",
            data:{
                "id"  : currentFileId,
                "type": 2
            },
            success: function(data){
                if(data["success"] !== 1){
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }
    getDownLoad(selector)
}

// creator：张旭博，2017-05-06 15:10:52，文件功能 - 换版
function changeVersion(obj) {
    if(isGeneral){
        $("#fileUpload .bounce_title").html("文件换版")
    } else {
        $("#fileUpload .bounce_title").html("文件换版申请")
    }
    initKendoFileUpload('changeVersion')
    $("#fileUpload").find("input.ty-inputText").val("")
    $("#needOther").prop("checked", true)
    $("#fileUpload .seePart").show().siblings(".inputPart").hide()

    $("#sureUploadNewFileBtn").data("name", "sureChangeVersion")
    var fileId = obj.parents(".ty-fileItem").data("id");
    $.ajax({
        url: "../res/getFileMessage.do",
        data: { id: fileId },
        success: function (data) {
            var success = data["success"] ;
            if(success !== 1){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list = data["data"]["resource"];
                $("#fileUpload .see_fileNo").html(list.fileSn);
                $("#fileUpload .see_fileName").html(list.name);
                var chooseNode = $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive")
                var folderNameObj = chooseNode.parents("li").children(".ty-treeItem").children("span")
                var str = ''
                folderNameObj.each(function (index) {
                    var name = $(this).text()
                    if(index === 0){
                        str = '<span class="ty-color-red">'+name+'</span>'
                    } else {
                        str = name + ' / ' + str
                    }
                })
                str = '<i class="fa fa-folder"></i>' + str
                $("#fileUpload .see_savePlace").html(str)
                $("#fileUpload .see_savePlace").data("categoryId", list.category)
                bounce.show($("#fileUpload"));
                $("#fileUpload").data("fileInfo", '')
            }
        }
    });
    getApprover()
    setEveryTime(bounce, 'changeVersion')
}

// creator: 张旭博，2021-07-21 09:07:57，文件功能 - 换版记录
function chargeRecord(obj){
    $("#resHistory").show().siblings().hide();
    $("#btn-group").hide()
    $("#resHistory").data("fileId", obj.parents(".ty-fileItem").data("id"));
    getRecord(1, 20)
}

// creator: 张旭博，2019-06-19 14:02:04，文件功能 - 基本信息
function basicMessage(obj){
    var pid=obj.parents(".ty-fileItem").data("id");
    var loginId = sphdSocket.user.userID;
    $("#docInfoScan").data("currentFileId",pid);

    var url = '../res/getFileMessage.do'

    var history = $("#resHistory").is(":visible") // 是否是换版记录中的文件
    if (history) {
        url = "../res/getUpFileMes.do"
    }
    $.ajax({
        url: url,
        data: {"id" :pid},
        success: function (res) {
            var state = res["success"] ;
            if(state !== 1){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list    = res["data"].resource, // 文件信息
                    arr     = res["data"].listUser, // 总务的列表
                    approvalProcess  = res["data"].listAp, // 审批流程
                    listUser  = res["data"].listUser, // 审批流程
                    size        = list.size,
                    reason      = list.reason,
                    view_num    = list.viewNum,         // 浏览次数
                    download_num= list.downloadNum,     // 下载次数
                    move_num    = list.moveNum,         // 移动次数
                    change_num  = list.changeNum,       // 换版次数
                    upName      = res["data"].upName,   // 文件名称修改次数
                    upFileSn    = res["data"].upFileSn, // 文件编号修改次数
                    categoryName= res["data"].categoryName;

                typeof (reason) === "undefined" ? reason = "--":reason;
                size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size = parseFloat(size/1048576).toFixed(2) + 'MB';

                $("#info_title").html(list["name"]);

                // left
                $("#info_sn").html(list["fileSn"]);
                $("#info_category").html(categoryName);
                $("#info_size").html(size);
                $("#info_version").html( ( list["version"] || "未知") );
                $("#info_content").html(list["content"]);


                // right
                var creator = '<span class="info_createName info_name">'+list["createName"]+'</span> <span class="info_createDate">'+list["createDate"]+'</span>'
                var updater = change_num === 0?'--':'<span class="info_updateName info_name">'+list["updateName"]+'</span> <span class="info_updateDate">'+list["updateDate"]+'</span>'
                $("#info_gn").html("G" + change_num);
                $("#info_creator").html(creator);
                $("#info_updater").html(updater);
                if (isGeneral) {
                    var resSignedMes  = res["data"].resSignedMes // 签收信息
                    $("#docInfoScan .generalPart").show()
                    $("#docInfoScan .sign_num").html(resSignedMes?(resSignedMes.haveSignedNum + '/' + resSignedMes.signedNum):'--/--');
                    $("#docInfoScan").data("docInfo", {
                        resHisId: resSignedMes.resHisId,
                        fileName: list.name,
                        fileSn: list.fileSn,
                        changeNum: list.changeNum,
                        creator: list.changeNum > 0?('换版人：' + list.updateName+' ' + list.updateDate):('创建人：' + list.createName+' ' + list.createDate)
                    })
                } else {
                    $("#docInfoScan .generalPart").hide()
                }

                var approveStr = '<div class="trItem"><span class="ttl">申请人：</span><span class="con" style="max-width: 280px"><span class="info_name">' + approvalProcess[0].userName + '</span><span>' + formatTime(approvalProcess[0].createDate, true) +'</span></div>'
                // 审批流程
                for (var i = 0; i < approvalProcess.length; i++) {
                    var name = ''
                    if ( i === approvalProcess.length - 1) {
                        name = '文 管'
                    } else {
                        name = '审批人'
                    }
                    approveStr += '<div class="trItem"><span class="ttl">' + name + '：</span><span class="con" style="max-width: 280px"><span class="info_name">' + approvalProcess[i].toUserName + '</span><span>' + formatTime(approvalProcess[i].handleTime, true) +'</span></div>'
                }
                $("#docInfoScan .processList").html(approveStr)
                if( change_num > 0){ // 换过版
                    $("#docInfoScan .info_content .ttl").html("换版原因：")
                }else{
                    $("#docInfoScan .info_content .ttl").html("说明：")
                }

                var userLimit = chargeRole('超管') || chargeRole('小超管') || chargeRole('总务') || chargeZW(loginId , arr)

                view_num !== 0 && userLimit      ? $("#viewNumBtn").prop("disabled",false)    :$("#viewNumBtn").prop("disabled",true);
                download_num !== 0 && userLimit  ? $("#downloadNumBtn").prop("disabled",false):$("#downloadNumBtn").prop("disabled",true);

                move_num === 0      ? $("#moveNumBtn").prop("disabled",true)        :$("#moveNumBtn").prop("disabled",false);
                upName === 0        ? $("#changeNameNumBtn").prop("disabled",true)  :$("#changeNameNumBtn").prop("disabled",false);
                upFileSn === 0      ? $("#changeNoNumBtn").prop("disabled",true)    :$("#changeNoNumBtn").prop("disabled",false);
                $("#docInfoScan .censusInfo .view_num").html(view_num);
                $("#docInfoScan .censusInfo .download_num").html(download_num);
                $("#docInfoScan .censusInfo .move_num").html(move_num);
                $("#docInfoScan .censusInfo .name_num").html(upName);
                $("#docInfoScan .censusInfo .no_num").html(upFileSn);

                if (history) {
                    $("#docInfoScan .censusInfo").hide()
                } else {
                    $("#docInfoScan .censusInfo").show()
                }
                bounce.show($("#docInfoScan")) ;
            }
        }
    })
}

// creator: 张旭博，2021-07-20 15:47:04，文件功能 - 删除弹窗(暂时不用的文件专属)
function deleteNoUseFile(obj) {
    bounce.show($("#bounce_tip"))
    $("#bounce_tip").find(".tipMsg").html("确定“删除”这个文件吗？")
    $("#bounce_tip").find(".sureBtn").unbind().on("click", function () {
        var fileId      = obj.parents(".ty-fileItem").data("id")
        $.ajax({
            url: '../res/delTrashFile.do',
            data: {
                fileId: fileId
            },
            success: function (res) {
                bounce.cancel()
                var state = res.data.state
                if (state === 1) {
                    layer.msg("文件已“删除”！之后3天，在“即将消失的文件”中您还能见到该文件!")
                } else if (state === 2) {
                    $("#tipMess2").html("删除失败！<br>文件进入“暂时不用的文件”需满5天才能删除！")
                    bounce_Fixed.show($("#tip2"))
                } else {
                    $("#tipMess2").html("删除失败！")
                    bounce_Fixed.show($("#tip2"))
                }
            }
        })
    })
}

// creator: 张旭博，2021-07-20 15:47:04，文件功能 - 禁用文件弹窗
function disableFile(obj) {
    var fileHisId      = obj.parents(".ty-fileItem").data("id")
    $.ajax({
        url: '../res/clickButtenDisable.do',
        data: {
            fileHisId: fileHisId
        },
        success: function (res) {
            var data = res.data
            if (data === 2) {
                layer.msg("文件已经被禁用，请勿重复禁用")
            } else {
                if (data === 1) {
                    $("#bounceFixed2_tip .tipMsg").html("禁用后，其他职工将无法打开该文件的这个版本。<br>确定禁用该文件的这个版本吗？");
                } else if (data === 3) {
                    $("#bounceFixed2_tip .tipMsg").html(`现有职工在借阅本文件或其历史版本。  <a class="ty-color-blue" onclick="seeBorrowPerson(${fileHisId}, 1)">查看</a><br>禁用后，借阅者的借阅将被终止。<br>确定禁用吗？`);
                }
                $("#bounceFixed2_tip .sureBtn").unbind().on("click", function () {
                    sureDisableFile()
                })
                bounce_Fixed2.show($("#bounceFixed2_tip")) ;
            }
        }
    })
}

// creator: 张旭博，2021-09-23 10:30:04，禁用文件 - 确定
function sureDisableFile() {
    var fileHisId      = $(".ty-fileItemActive:visible").data("id");
    $.ajax({
        url:"../res/disableFileHis.do",
        data:{ fileHisId: fileHisId},
        success: function(res){
            var state = res.data.state
            if(state === 1 ){
                bounce_Fixed.cancel();
                bounce_Fixed2.cancel();
                layer.msg("操作成功");
                getRecord(1, 20)
            } else if (state === 2) {
                layer.msg("文件已经被禁用，请勿重复禁用");
            } else {
                layer.msg("操作失败")
            }
        }
    });
}

// creator: 张旭博，2021-09-23 10:29:33，

// creator: 张旭博，2021-07-20 15:47:04，文件功能 - 还原弹窗(暂时不用的文件专属)
function recoveryNoUseFile(obj) {
    bounce.show($("#bounce_tip"))
    $("#bounce_tip").find(".tipMsg").html("文件“还原”后，还回到其原来的文件夹。<br>确定“还原”这个文件吗？")
    $("#bounce_tip").find(".sureBtn").unbind().on("click", function () {
        var fileId      = obj.parents(".ty-fileItem").data("id")
        $.ajax({
            url: '../res/trashFileRestore.do',
            data: {
                fileId: fileId
            },
            success: function (res) {
                bounce.cancel()
                var state = res.data.state
                if (state === 1) {
                    layer.msg("操作成功")
                } else if (state === 2 || state === 4) {
                    $("#tipMess2").html("还原失败！<br>因为原文件夹已被移动或删除！")
                    bounce_Fixed.show($("#tip2"))
                } else if (state === 3) {
                    $("#tipMess2").html("还原失败！<br>因为有文件与要还原文件的文件编号重复！！")
                    bounce_Fixed.show($("#tip2"))
                } else {
                    $("#tipMess2").html("还原失败！")
                    bounce_Fixed.show($("#tip2"))
                }
            }
        })
    })
}

// creator: 张旭博，2018-05-16 15:27:29，文件功能 - 更改文件名称
function changeFileName(selector) {
    $(".ty-fileItem").removeClass("ty-fileItemActive");
    selector.parents(".ty-fileItem").addClass("ty-fileItemActive");
    $("#changeFileName .changeFileName").val("");
    bounce.show($("#changeFileName"));
    var currentFileName = selector.parents(".ty-fileItem").find(".ty-fileName").html();
    var currentFileId = selector.parents(".ty-fileItem").data("id");
    $("#changeFileName").find(".currentFileName").html(currentFileName);
    $("#changeFileName").data("currentFileId",currentFileId);
    bounce.everyTime('0.2s','changeFileName',function(){
        var changeFileName = $("#changeFileName .changeFileName").val();
        if(changeFileName === ""){
            $("#changeFileNameBtn").prop("disabled",true)
        }else{
            $("#changeFileNameBtn").prop("disabled",false)
        }
    });
}

// creator: 张旭博，2018-05-16 15:27:29，文件功能 - 更改文件编号
function changeFileNo(selector) {
    $(".ty-fileItem").removeClass("ty-fileItemActive");
    selector.parents(".ty-fileItem").addClass("ty-fileItemActive");
    $("#changeFileNo .changeFileNo").val ("");
    bounce.show($("#changeFileNo"));
    var currentFileNo = selector.parents(".ty-fileItem").find(".ty-fileNo").html();
    var currentFileId = selector.parents(".ty-fileItem").data("id");
    $("#changeFileNo").find(".currentFileNo").html(currentFileNo.substring(4,currentFileNo.length));
    $("#changeFileNo").data("currentFileId",currentFileId);
    bounce.everyTime('0.2s','changeFileNo',function(){
        var changeFileName = $("#changeFileNo .changeFileNo").val();
        if(changeFileName === ""){
            $("#changeFileNoBtn").prop("disabled",true)
        }else{
            $("#changeFileNoBtn").prop("disabled",false)
        }
    });
}

// creator: 张旭博，2022-07-08 04:35:48， 文件功能 - 文件废止/文件废止申请
function fileAbolish(selector) {
    if(isGeneral){
        $("#fileAbolish .tip").html("确定废止本文件吗？")
    } else {
        $("#fileAbolish .tip").html("确定提交本文件的废止申请吗？")
    }
    $("#fileAbolish_needOther").prop("checked", true)

    var fileId = selector.parents(".ty-fileItem").data("id");
    $.ajax({
        url: "../res/getCorrelationForAbolishFile.do",
        data: { fileId: fileId },
        success: function (res) {
            var data = res.data
            var listResAndResAtt = data.listResAndResAtt
            var listResAtt  = data.listResAtt
            var relateOtherStr = ''
            var relateSelfStr = ''
            if (listResAndResAtt.length > 0) {
                $(".part_relateOther").show()
                for (var i in listResAndResAtt) {
                    relateOtherStr += '<tr>' +
                        '   <td>' + listResAndResAtt[i].resAttNameAndSn + '</td>'+
                        '   <td>' + listResAndResAtt[i].resNameAndSn.join("<br>") + '</td>'+
                        '</tr>'
                }
                $("#tbl_relateOther tbody").html(relateOtherStr)
            } else {
                $(".part_relateOther").hide()
            }
            if (listResAtt.length > 0) {
                $(".part_relateSelf").show()
                for (var j in listResAtt) {
                    relateSelfStr += '<tr>' +
                        '   <td>' + listResAtt[j].resAttNameAndSn + '</td>'+
                        '</tr>'
                }

                $("#tbl_relateSelf tbody").html(relateSelfStr)
            } else {
                $(".part_relateSelf").hide()
            }
        }
    });
    bounce.show($("#fileAbolish"));
    getApprover()
    setEveryTime(bounce, 'fileAbolish')
}

// creator: 张旭博，2022-07-14 08:50:31， 文件功能 - 文件复用
function fileReuse(selector) {
    var id = selector.parents(".ty-fileItem").data("id")
    bounce.show($("#bounce_tip"));
    $("#bounce_tip .tipMsg").html("确定复用文件吗？")
    $("#bounce_tip .sureBtn").unbind().on("click", function() {
        $.ajax({
            url: '../res/reuseFile.do',
            data: {
                id: id
            },
            success: function (res) {
                var data = res.data
                var state = data.state
                if (state === 1) {
                    layer.msg("操作成功")
                    bounce.cancel()
                } else if (state === 0) {
                    bounce_Fixed.show($("#bounceFixed_tip"))
                    $("#bounceFixed_tip .tipMsg").html("不能复用，源文件的状态已经改变")
                    $("#bounceFixed_tip .sureBtn").unbind().on("click", function() {
                        bounce_Fixed.cancel()
                        bounce.cancel()
                    })
                } else {
                    layer.msg("操作失败")
                }
            }
        })
    })

}

// creator: 张旭博，2022/3/10 14:19，文件功能 - 表格相关
function sheetRelate(selector) {
    $("#btn-group").hide()
    $("#page_sheetRelate").show().siblings().hide();
    $("#page_sheetRelate .tips").html('系统中，已与本文件关联的表格共<span class="relateNum"></span>个。关联表格的管理，如增加、解除关联及换版，均需您操作。')
    $("#addNeedRelateSheetBtn").show()
    $("#page_sheetRelate").data("fileId", selector.parents(".ty-fileItem").data("id"));
    getSheetRelateList(1, 20)
}

// creator: 张旭博，2022/3/10 14:19，文件功能 - 相关表格
function relateSheet(selector) {
    $("#btn-group").hide()
    $("#page_sheetRelate").show().siblings().hide();
    $("#page_sheetRelate .tips").html('系统中，已与本文件关联的表格共<span class="relateNum"></span>个。')
    $("#addNeedRelateSheetBtn").hide()
    $("#page_sheetRelate").data("fileId", selector.parents(".ty-fileItem").data("id"));
    getSheetRelateList(1, 20)
}

// creator: 张旭博，2022/3/13 9:33，文件功能 - 解除关联
function sheetRelateRemove(selector) {
    var id = selector.parents(".ty-fileItem").data("id");
    var resource = $("#page_sheetRelate").data("fileId")
    var data = {
        id: id,
        resource: resource
    }
    $.ajax({
        url: '../res/getRemoveCorrelationStateAndFile.do',
        data: data,
        success: function (res) {
            var data = res.data
            var state = data.state
            if (state === 3) {
                layer.msg("状态已变更，请刷新页面")
            } else {
                bounce.show($("#bounce_tip"))
                var str = ''

                if (state === 1) {
                    var list = data.list
                    str += '<div class="ty-alert">除本文件外，本表格尚与以下文件关联</div>'
                    str += '<table class="ty-table"><thead><tr><td>文件编号</td><td>文件名称</td></tr></thead><tbody>'
                    for (var i in list) {
                        var item = list[i].resEntity
                        str += '<tr><td>'+item.fileSn+'</td><td>'+item.name+'</td></tr>'
                    }
                    str += '</tbody></table><div class="ty-hr"></div><p>此项关联的解除，与上述关联关系无关。</p><p>确定解除此项关联吗？</p>'
                } else if (state === 2) {
                    str = '本表格仅与本文件有关联关系。<br>解除此项关联后，本表格将进入被废止的表格。<br>确定解除此项关联吗？'
                } else {
                    str = '系统错误'
                }
                $("#bounce_tip .tipMsg").html(str)
                $("#bounce_tip .sureBtn").unbind().on("click", function () {
                    $.ajax({
                        url: '../res/removeCorrelationByResForm.do',
                        data: {
                            id: id,
                            resource: resource
                        },
                        success: function (res) {
                            var data = res.data
                            var state = data.state
                            if (state === 1) {
                                layer.msg("操作成功")
                                bounce.cancel()
                                selector.parents(".ty-fileItem").remove()
                            } else if (state === 2) {
                                layer.msg("已经解除关联了，不用重复操作")
                            } else {
                                layer.msg("操作失败")
                            }
                        }
                    })
                })

            }
        }
    })

}

// creator: 张旭博，2022/3/10 14:24，获取表格关联文件
function getSheetRelateList (currentPageNo, pageSize) {
    var resource = $("#page_sheetRelate").data("fileId")
    var data = {
        type: 3,
        resource: resource,
        currentPageNo: currentPageNo,
        pageSize: pageSize
    }
    $.ajax({
        url: '../res/getResFormAssociatedFile.do',
        data: data,
        success: function (res) {
            var data = res.data
            var list = data.list
            $("#page_sheetRelate .relateNum").html(list.length)
            var str = getFileListStrBySheet(list)
            $("#page_sheetRelate .fileList").html(str)
        }
    })
}

// creator: 张旭博，2022/3/13 15:50，表格关联的操作记录
function sheetRelateHandleRecord(selector) {
    bounce.show($("#sheetRelateHandleRecord"))
    $("#sheetRelateHandleRecord").data("id", selector.parents(".ty-fileItem").data("id"))
    getSheetHandleRecordByFile(1, 20)

}

// creator: 张旭博，2022/3/2 15:50，表格功能 - 修改表格信息
function changeSheetMsg(selector) {
    bounce.show($("#changeSheetMsg"))
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#changeSheetMsg [name='name']").val(name)
    $("#changeSheetMsg [name='sn']").val(sn)
    $("#changeSheetMsg").data("id", id)
    $("#changeSheetMsg .sureBtn").unbind().on("click", function() {
        var name1 = $("#changeSheetMsg [name='name']").val()
        var sn1 = $("#changeSheetMsg [name='sn']").val()
        if (name1 === name && sn1 === sn) {
            layer.msg("请修改表格信息后再提交")
            return false
        }
        //参数json
        var addData = {
            id: id
        }
        if (name1 !== name) {
            addData.name = name1
        }
        if (sn1 !== sn) {
            addData.sn = sn1
        }
        $.ajax({
            url: "../res/updateResRorm.do",
            data: addData,
            success: function(res) {
                var data= res.data
                var state = data.state
                if (state === 1) {
                    layer.msg("修改成功")
                    bounce.cancel()
                    getSheetRelateList(1, 20)
                } else if (state === 2) {
                    bounce_Fixed.show($("#bounceFixed_errorTip"))
                    $("#bounceFixed_errorTip").find(".tipMsg").html("修改失败！<br>表格编号重复")
                } else {
                    layer.msg("修改失败")
                }
            }
        })
    })
    bounce.everyTime('0.5s', 'changeSheetMsg',function() {
        var state = 0
        $("#changeSheetMsg [require]:visible").each(function () {
            if ($.trim($(this).val()) === '') {
                state++
            }
        })
        if (state > 0) {
            $("#sureChangeSheetMsgBtn").prop("disabled", true)
        } else {
            $("#sureChangeSheetMsgBtn").prop("disabled", false)
        }
    })
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 换版
function changeSheetVersion(selector) {
    bounce.show($("#changeSheetVersion"))

    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#changeSheetVersion .seeName").html(name)
    $("#changeSheetVersion .seeSn").html(sn)
    $("#changeSheetVersion").data("id", id)
    $('#changeSheetVersion .fileUpload').html("")
    $('#changeSheetVersion .fileShowList').html("")
    $('#changeSheetVersion .fileUpload').Huploadify({
        auto: true,
        fileTypeExts: '*',
        formData:{
            module: '文件与资料',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: 1024 * 1024 * 2 - 500,  // 1.5G
        showUploadedSize: true,
        removeTimeout: 99999999,
        buttonText: "选择",
        queueID: 'fileQueue',
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onSelect: function () {
            $(".fileUpload .uploadify-button").hide();
        },
        onUploadError: function () {
            $(".fileUpload .uploadify_state").html("上传失败！")
        },
        onUploadSuccess: function (file, json) {
            console.log(json)
            var data = JSON.parse(json),
                name = data.filename,
                type = data.lastName,
                size = file.size,
                fileName = file.name,
                fileInfo = {
                    path: name,
                    size: size,
                    title: fileName,
                    type: type,
                    module: '事务讨论',
                    place: 1
                };
            $("#changeSheetVersion").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
            $("#changeSheetVersion .fileUpload").data("fileInfo", fileInfo);
            var str = '<div class="file_item"><div class="hd">'+JSON.stringify(fileInfo)+'</div><div class="fileType ty-file_'+type+'"></div><div class="file_name" title="'+fileName+'">'+fileName+'</div><div class="delFile" fileUid="'+data.fileUid+'">+</div></div>'
            $("#changeSheetVersion .fileShowList").html(str);
        },
        onQueueComplete:function() {
            $(".fileUpload .uploadify-button").show();
            $(".fileUpload .uploadify-queue").html('')
        }
    })

    $("#changeSheetVersion .sureBtn").unbind().on("click", function() {
        var fileInfo = $("#changeSheetVersion .fileUpload").data("fileInfo");

        //参数json
        var addData = {
            id: id,
            path: fileInfo.path,
            size: fileInfo.size,
            type: fileInfo.type,
            module: '文件与资料'
        }
        $.ajax({
            url: "../res/changeResRormVersion.do",
            data: addData,
            success: function(res) {
                var data= res.data
                var resAtt= data.resAtt
                if (resAtt) {
                    layer.msg("修改成功")
                    bounce.cancel()
                    getSheetRelateList(1, 20)
                } else {
                    layer.msg("修改失败")
                }
            }
        })
    })
    bounce.everyTime('0.5s', 'changeSheetVersion',function() {
        var state = 0
        if ($("#changeSheetVersion .fileShowList").html() === '') {
            state++
        }
        if (state > 0) {
            $("#sureChangeSheetVersionBtn").prop("disabled", true)
        } else {
            $("#sureChangeSheetVersionBtn").prop("disabled", false)
        }
    })
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 操作记录
function sheetHandleRecord(selector, type) {
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    bounce.show($("#sheetHandleRecord"))
    $("#sheetHandleRecord").data("id", id)
    getSheetHandleRecord(1, 20)
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 所关联的文件
function relate(selector) {
    bounce.show($("#relate"))
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#relate .fileSn").html(sn)
    $("#relate .fileName").html(name)
    $("#relate").data("id", id)
    getFileBySheet(1, 20, 1)
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 曾关联的文件
function related(selector) {
    bounce.show($("#related"))
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#related .fileSn").html(sn)
    $("#related .fileName").html(name)
    $("#relate").data("id", id)
    getFileBySheet(1, 20, 2)
}

// creator: 张旭博，2022/3/25 20:04，获取表格的操作记录
function getSheetHandleRecord(currentPageNo, pageSize) {
    var id = $("#sheetHandleRecord").data("id")
    var data = {
        id: id,
        currentPageNo: currentPageNo,
        pageSize: pageSize
    }
    $.ajax({
        url: '../res/getResRormHis.do',
        data: data,
        success: function (res) {
            var data = res.data
            var list = data.list
            var pageInfo = data.pageInfo

            //设置分页信息
            var currentPageNo   = pageInfo.currentPageNo,
                totalPage       = pageInfo.totalPage;
            setPage( $("#ye_handleRecord"), currentPageNo, totalPage, "sheetHandleRecord") ;

            var str = ''
            for (var i = 0; i<list.length; i++) {
                if (i === 0) {
                    str +=  '<tr data-id="'+list[i].id+'">' +
                        '<td>创建</td>'+
                        '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                        '<td><a class="ty-color-blue" type="btn" data-name="seeHandleRecordDetail">查看</a></td>'+
                        '</tr>'
                } else {
                    str +=  '<tr data-id="'+list[i].id+'">' +
                        '<td>'+(list[i].operation === '1'?'换版':'修改')+'</td>'+
                        '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                        '<td><a class="ty-color-blue" type="btn" data-name="seeHandleRecordDetail">查看</a></td>'+
                        '</tr>'
                }
            }
            $("#sheetHandleRecord tbody").html(str)
        }
    })
}

// creator: 张旭博，2022/3/25 20:04，获取某一条表格的操作记录详情
function getOneSheetDetail() {
    var id = $("#handleRecordDetail").data("id")
    $.ajax({
        url: '../res/getResRormHisMes.do',
        data: {
            id: id
        },
        success: function (res) {
            var data = res.data
            var resAttHis = data.resAttHis
            var size  = resAttHis.size
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size = parseFloat(size/1048576).toFixed(2) + 'MB';

            $("#handleRecordDetail .info_title").html(resAttHis.name);

            // left
            $("#handleRecordDetail .info_sn").html(resAttHis.sn);
            $("#handleRecordDetail .info_size").html(size);

            // right
            $("#handleRecordDetail .info_gn").html("G" + resAttHis.version);
            $("#handleRecordDetail .info_createName").html(resAttHis.createName);
            $("#handleRecordDetail .info_createDate").html(moment(resAttHis.createDate).format("YYYY-MM-DD HH:mm:ss"));
            $("#handleRecordDetail .info_version").html( ( resAttHis.type || "未知") );
        }
    })
}

// creator: 张旭博，2022/3/2 17:18，文件功能 - 关联记录
function relateRecord(selector) {
    bounce.show($("#relateRecord"))
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#relateRecord .fileSn").html(sn)
    $("#relateRecord .fileName").html(name)
    $("#relateRecord").data("id", id)
    getFileHandleRecordBySheet(1, 20)
}

// creator: 张旭博，2022/3/25 20:20，通过表格获取关联文件的操作记录和通过文件获取关联表格的操作记录
function getFileHandleRecordBySheet(currentPageNo, pageSize) {
    var id = $("#relateRecord").data("id")
    var data = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        attId: id
    }
    $.ajax({
        url: '../res/getResFormCorrelationRecord.do',
        data: data,
        success: function (res) {
            var data = res.data
            //设置分页信息
            // var pageInfo = data.pageInfo
            // var currentPageNo   = pageInfo.currentPageNo,
            //     totalPage       = pageInfo.totalPage;
            // setPage( $("#ye_relateRecord"), currentPageNo, totalPage, "relateRecord") ;
            //设置列表
            var list  = data.list
            var str = ''
            for (var i in list ) {
                str +=  '<tr>' +
                    '<td>'+(list[i].operation === '4'?'关联':'解除关联')+'</td>'+
                    '<td>'+list[i].resEntity.fileSn+'</td>'+
                    '<td>'+list[i].resEntity.name+'</td>'+
                    '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                    '</tr>'
            }
            $("#relateRecord tbody").html(str)
        }
    })
}

// creator: 张旭博，2022/3/25 20:20，通过表格获取关联文件的操作记录和通过文件获取关联表格的操作记录
function getSheetHandleRecordByFile(currentPageNo, pageSize) {
    var id = $("#sheetRelateHandleRecord").data("id")
    var data = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        resource: id
    }
    $.ajax({
        url: '../res/getResFormCorrelationRecord.do',
        data: data,
        success: function (res) {
            var data = res.data
            //设置分页信息
            // var pageInfo = data.pageInfo
            // var currentPageNo   = pageInfo.currentPageNo,
            //     totalPage       = pageInfo.totalPage;
            // setPage( $("#ye_relateRecord"), currentPageNo, totalPage, "relateRecord") ;
            //设置列表
            var list  = data.list
            var str = ''
            for (var i in list ) {
                str +=  '<tr>' +
                    '<td>'+(list[i].operation === '4'?'关联':'解除关联')+'</td>'+
                    '<td>'+list[i].resAtt.sn+'</td>'+
                    '<td>'+list[i].resAtt.name+'</td>'+
                    '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                    '</tr>'
            }
            $("#sheetRelateHandleRecord tbody").html(str)
        }
    })
}

// creator: 张旭博，2022/3/25 21:09，通过表格获取关联文件和通过文件获取关联表格
function getFileBySheet(currentPageNo, pageSize, type) {
    var id = $("#relate").data("id")
    var data = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        type: type, // 1是获取表格正在关联的文件 2是获取表格曾经关联的文件
        attId: id
    }
    $.ajax({
        url: '../res/getResFormAssociatedFile.do',
        data: data,
        success: function (res) {
            var data = res.data
            // var pageInfo = data.pageInfo

            //设置分页信息
            // var currentPageNo   = pageInfo.currentPageNo,
            //     totalPage       = pageInfo.totalPage,
            //     jsonStr = JSON.stringify( { type: type} ) ;
            // var dom = type === 1?$("#ye_relate"):$("#ye_related")
            // setPage( dom, currentPageNo, totalPage, "relate" ,jsonStr) ;
            var list  = data.list
            var str = ''
            for (var i in list ) {
                if (type === 1) {
                    str +=  '<tr>' +
                        '<td>'+list[i].resEntity.fileSn+'</td>'+
                        '<td>'+list[i].resEntity.name+'</td>'+
                        '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                        '</tr>'
                } else {
                    str +=  '<tr>' +
                        '<td>'+list[i].resEntity.fileSn+'</td>'+
                        '<td>'+list[i].resEntity.name+'</td>'+
                        '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                        '<td>'+list[i].updateName + ' ' + moment(list[i].updateDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                        '</tr>'
                }
            }
            if (type === 1) {
                $("#relate tbody").html(str)
            } else {
                $("#related tbody").html(str)
            }

        }
    })
}

// creator: 张旭博，2022/3/10 16:41，增加需与本文件关联的表格
function newSheetRelateFile() {
    bounce.show($("#newSheetRelateFile"))
    $('#newSheetRelateFile .fileUpload').html("")
    $('#newSheetRelateFile input').val("")
    $('#newSheetRelateFile .fileShowList').html("")
    $('#newSheetRelateFile .fileUpload').Huploadify({
        auto: true,
        fileTypeExts: '*',
        formData:{
            module: '文件与资料',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: 1024 * 1024 * 2 - 500,  // 1.5G
        showUploadedSize: true,
        removeTimeout: 99999999,
        buttonText: "选择",
        queueID: 'fileQueue',
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onSelect: function () {
            $("#newSheetRelateFile .fileUpload .uploadify-button").hide();
        },
        onUploadError: function () {
            $("#newSheetRelateFile .fileUpload .uploadify_state").html("上传失败！")
        },
        onUploadSuccess: function (file, json) {
            console.log(json)
            var data = JSON.parse(json),
                name = data.filename,
                type = data.lastName,
                size = file.size,
                fileName = file.name,
                fileInfo = {
                    path: name,
                    size: size,
                    title: fileName,
                    type: type,
                    module: '事务讨论',
                    place: 1
                };
            $("#newSheetRelateFile").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
            $("#newSheetRelateFile .fileUpload").data("fileInfo", fileInfo);
            $("#newSheetRelateFile [name='name']").val(data.displayName)
            $("#newSheetRelateFile [name='sn']").val(data.displayName)
            var str = '<div class="file_item"><div class="hd">'+JSON.stringify(fileInfo)+'</div><div class="fileType ty-file_'+type+'"></div><div class="file_name" title="'+fileName+'">'+fileName+'</div><div class="delFile" fileUid="'+data.fileUid+'">+</div></div>'
            $("#newSheetRelateFile .fileShowList").html(str);
        },
        onQueueComplete:function() {
            $(".fileUpload .uploadify-button").show();
            $(".fileUpload .uploadify-queue").html('')
        }
    })
    setEveryTime(bounce, 'newSheetRelateFile')

    $("#newSheetRelateFile .sureBtn").unbind().on("click", function() {
        var fileInfo = $("#newSheetRelateFile .fileUpload").data("fileInfo");
        var resource = $("#page_sheetRelate").data("fileId")
        //参数json
        var addData = {
            path: fileInfo.path,
            size: fileInfo.size,
            type: fileInfo.type,
            module: '文件与资料',
            resource: resource
        }
        $("#newSheetRelateFile [require]:visible").each(function () {
            addData[$(this).attr("name")] = $(this).val()
        })
        $.ajax({
            url: "../res/addResRorm.do",
            data: addData,
            success: function(res) {
                var data= res.data
                if (data.state === 1) {
                    layer.msg("新增成功")
                    bounce.cancel()
                    getSheetRelateList(1, 20)
                } else if (data.state === 2) {
                    bounce_Fixed.show($("#bounceFixed_errorTip"))
                    $("#bounceFixed_errorTip").find(".tipMsg").html("操作失败！<br>表格编号重复")
                } else {
                    layer.msg("新增失败")
                }
            }
        })
    })
}

// creator: 张旭博，2022/2/28 15:44，获取表格列表（待关联、已关联、被废止）
function getSheetList(currentPageNo , pageSize, type, name) {
    var pm_list = new Promise((resolve, reject) => {
        $.ajax({
            url: '../res/getResRormByType.do ',
            data: {
                type: type, // 1是获取待关联 2是已关联 3是解除关联
                pageSize: pageSize, //条数
                currentPageNo: currentPageNo, //页码
                name: name
            },
            success: function (res) {
                var data = res.data
                resolve(data)
            }
        })
    })
    pm_list.then(data => {
        var pageInfo = data.pageInfo
        //设置分页信息
        var currentPageNo   = pageInfo["currentPageNo"],
            totalPage       = pageInfo["totalPage"],
            jsonStr = JSON.stringify( { type : type, name: name} ) ;

        //设置文件信息
        setPage( $("#ye_chooseRelate"), currentPageNo, totalPage, "chooseRelate", jsonStr) ;

        var list = data.list
        var listStr = getFileListStrBySheet(list, true);
        $("#chooseInFolder .ty-fileList").html(listStr);
        if(list.length >0){
            $('#chooseInFolder .ty-fileNull').hide()
        }else{
            $('#chooseInFolder .ty-fileNull').show()
        }
    })
}

// creator: 张旭博，2018-05-16 15:27:29，文件功能 - 移动
function moveFile( obj ){
    $(".ty-fileItem").removeClass("ty-fileItemActive");
    obj.parents(".ty-fileItem").addClass("ty-fileItemActive");
    $("#chooseSaveFolder .bounce_title").html("移动到")
    bounce_Fixed.show($("#moveFile"));
    getFirstDoc('moveTo')
}

// creator: 张旭博，2022-11-09 03:30:46， 更新权限增减部分内容
function renderList(addList, delList) {
    var str = ''
    var str2 = ''
    for (var i in addList) {
        str     += '<div class="user_avatar" type="btn" data-name="delUser" data-id="'+addList[i].user+'"><span>'+addList[i].userName+'</span><i class="fa fa-minus-circle delUser"></i></div>'
    }
    $("#movedScanSet .addList").html(str)
    for (var j in delList) {
        str2    += '<div class="user_avatar" type="btn" data-name="addUser" data-id="'+delList[j].user+'"><span>'+delList[j].userName+'</span><i class="fa fa-plus-circle addUser"></i></div>'
    }
    $("#movedScanSet .delList").html(str2)
    $("#movedScanSet").find(".scanUseNum").html(addList.length)
    $("#movedScanSet").find(".decreaseNum").html(delList.length)
}

// creator: 张旭博，2022-11-09 03:31:07， 移动文件夹之前判断是否可以移动
function judgeMove() {
    let toChooseNode = $(".ty-colFileTree[data-name='moveTo'] .ty-treeItemActive")
    let newCategoryId = toChooseNode.data("id");
    let fileId = $(".ty-fileItemActive:visible").data("id")
    return new Promise((resolve, reject ) => {
        $.ajax({
            url: '../res/clickButtenJudgementMove.do',
            data: {
                fileId: fileId,
                newCategoryId: newCategoryId,
                type: 1
            },
            success: function (res) {
                loading.close()
                var data = res.data
                var resLockUuid = data.resLockUuid
                $("#movedScanSet").data("resLockUuid", resLockUuid)
                resolve(data)
            },
            complete: function () {}
        })
    })
}

// creator: 张旭博，2022-11-11 01:57:28， 文件功能 - 移动 - 权限选择 - 查看更多
function seeMoreMoveAcl() {
    bounce_Fixed3.show($("#moreMoveAcl"))
    var firstList = $("#movedScanSet").data("firstList")

    var oldAcl = $("#movedScanSet").data("oldAcl")
    var loseAcl = oldAcl.filter(item => !firstList.some(ele => ele.user === item.user))
    var getAcl = firstList.filter(item => !oldAcl.some(ele => ele.user === item.user))

    var oldAclStr = '',loseAclStr = '',getAclStr = ''
    for (let k in oldAcl) {
        oldAclStr  += '<div class="user_avatar"><span>'+oldAcl[k].userName+'</span></div>'
    }
    for (let i in loseAcl) {
        loseAclStr  += '<div class="user_avatar"><span>'+loseAcl[i].userName+'</span></div>'
    }
    for (let j in getAcl) {
        getAclStr  += '<div class="user_avatar"><span>'+getAcl[j].userName+'</span></div>'
    }
    $("#moreMoveAcl .oldAclNum").html(oldAcl.length)
    $("#moreMoveAcl .loseAclNum").html(loseAcl.length)
    $("#moreMoveAcl .getAclNum").html(getAcl.length)
    $("#moreMoveAcl .oldAcl").html(oldAclStr)
    $("#moreMoveAcl .loseAcl").html(loseAclStr)
    $("#moreMoveAcl .getAcl").html(getAclStr)
}

// creator：张旭博，2017-05-06 15:11:35，换版弹窗 - 确定
function sureChangeVersion(){
    //获取表单内容和接口需要的参数
    var fileInfo = $("#fileUpload").data("fileInfo");
    var fileId = $(".ty-fileItemActive").data("id")

    if (!fileId) {
        layer.msg("文件发生移动，无法换版！")
        return false
    }
    // lockFile()
    var pm_lockFile = lockFile(fileId)
    pm_lockFile.then(function(){
        //参数json
        var data = {
            userID: sphdSocket.user.userID,
            file: fileId,
            category: $("#fileUpload .see_savePlace").data("categoryId"),
            name:$("#fileUpload .see_fileName").text(),
            fileSn:$("#fileUpload .see_fileNo").text(),
            content:$("#fileUpload input[name='content']:visible").val(),
            path: fileInfo.path,
            size: fileInfo.size,
            version: fileInfo.version,
            module: '文件与资料',
            changeNum: $.trim($(".ty-fileItemActive .ty-fileVersion").attr("changeNum")) * 1 + 1,
            isNeedOther: false
        }
        data.isNeedOther = $("#fileUpload input:radio:checked").val()
        if (data.isNeedOther === '1') {
            // 选择了审批人
            data.type = 1
            data.approveName = $(".chooseApprover").find("option:selected").html()
            data.approveId = $(".chooseApprover").val()
        } else {
            // 由文管直接发布
            if (isGeneral) {
                data.type = 2
            } else {
                data.type = 1
            }
        }
        console.log(`data: ${JSON.stringify(data)}`)
        $.ajax({
            url: 'updateFileVersionByCentre.do',
            data: data,
            success: function (res) {
                var state = parseInt(res.data)
                if (state === 1) {
                    layer.msg("操作成功")
                    $("#upload-file-01").data("kendoUpload").clearAllFiles();
                    bounce.cancel();
                } else {
                    $("#mtTip #mt_tip_ms").html("操作失败!") ;
                    bounce.show($("#mtTip"));
                }
            }
        })
    })
}

// creator: 张旭博，2022-07-12 04:27:38， 废止弹窗 - 确定
function sureFileAbolish() {
    //获取表单内容和接口需要的参数
    var fileId = $(".ty-fileItemActive").data("id")
    var categoryId  = $(".ty-colFileTree[data-name='main'] .ty-treeItemActive").data("id");

    if (!fileId) {
        layer.msg("文件发生移动，无法废止！")
        return false
    }
    var pm_lockFile = lockFile(fileId)
    pm_lockFile.then(function(){
        //参数json
        var data = {
            file: fileId,
            category: categoryId,
            module: '文件与资料',
            isNeedOther: false
        }
        data.isNeedOther = $("#fileAbolish input:radio:checked").val()
        if (data.isNeedOther === '1') {
            // 选择了审批人
            data.type = 1
            data.approveName = $("#fileAbolish .chooseApprover").find("option:selected").html()
            data.approveId = $("#fileAbolish .chooseApprover").val()
        } else {
            // 由文管直接发布
            if (isGeneral) {
                data.type = 2
            } else {
                data.type = 1
            }
        }
        console.log(`data: ${JSON.stringify(data)}`)
        $.ajax({
            url: 'updateFileVersionByCentre.do',
            data: data,
            success: function (res) {
                var state = parseInt(res.data)
                if (state === 1) {
                    layer.msg("操作成功")
                    bounce.cancel();
                } else {
                    $("#mtTip #mt_tip_ms").html("操作失败!") ;
                    bounce.show($("#mtTip"));
                }
            }
        })
    })
}

// creator: 张旭博，2022-07-12 04:29:12， 锁定
function lockFile(fileId) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '../res/applyUpdateFileOPeration.do',
            data: {
                id: fileId
            },
            success: function (res) {
                var data = res.data
                var state = data.state
                if (state === 1) {
                    resolve()
                } else {
                    reject()
                }
            }
        })
    })
}

// creator: 张旭博，2018-05-16 15:27:29，更改文件名称 - 确定
function sureChangeFileName() {
    var currentFileId = $("#changeFileName").data("currentFileId"),
        changeFileName = $("#changeFileName .changeFileName").val();
    $.ajax({
        url:"../res/updateFileName.do",
        data:{
            "id"  : currentFileId,
            "name": changeFileName
        },
        success: function(data){
            bounce.cancel();
            if(data["success"] === 1){
                layer.msg("成功修改文件名称");
                updateFile(currentFileId);
            }else{
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }
        }
    });
}

// creator: 张旭博，2018-05-16 15:27:29，更改文件编号 - 确定
function sureChangeFileNo() {
    var currentFileId = $("#changeFileNo").data("currentFileId"),
        changeFileNo = $("#changeFileNo .changeFileNo").val();
    $.ajax({
        url:"../res/updateFileSn.do",
        data:{
            "id"    : currentFileId,
            "fileSn": changeFileNo
        },
        success: function(data){
            bounce.cancel();
            if(data["success"] === 1){
                layer.msg("成功修改文件编号");
                updateFile(currentFileId);
            }else{
                $("#mt_tip_ms").html(data["error"].code);
                bounce.show($("#mtTip"));
            }
        }
    });
}

// creator：王静，2017-08-03 2:00:23 移动弹窗确认提示窗 - 确定
function sureMove(){
    var firstList = $("#movedScanSet").data("firstList") || []
    var resLockUuid = $("#movedScanSet").data("resLockUuid")
    var userID = firstList.map(item=>{
        return item.user
    })
    var fileId      = $(".ty-fileItemActive:visible").data("id");
    var categoryId  = $(".ty-colFileTree[data-name='moveTo'] .ty-treeItemActive").data("id");
    $.ajax({
        url:"../res/changeFilePlaceByResourCentre.do",
        data:{
            fileId: fileId,
            categoryId: categoryId,
            userID: JSON.stringify(userID),
            resLockUuid: resLockUuid
        },
        success: function(res){
            var data = res.data
            var state = data.state
            if(state === 1 ){
                bounce_Fixed.cancel();
                bounce_Fixed2.cancel();
                layer.msg("移动成功");
            } else if (state === 2){
                $("#mt_tip_ms").html('移动失败');
                bounce.show($("#mtTip"));
            } else if (state === 3){
                $("#mt_tip_ms").html('安全锁过期');
                bounce.show($("#mtTip"));
            } else {
                $("#mt_tip_ms").html('系统错误');
                bounce.show($("#mtTip"));
            }
        }
    });
}


//------------------------- 文件查询 ---------------------------//

// creator: 张旭博，2018-04-20 19:47:19，普通搜索 - 搜索
function searchBtn() {
    $("#searchSort").show()
    $("#search_applier").html("")
    $("#searchSort li").eq(1).find(".sort").hide()
    $("#searchSort li").eq(1).find("i").removeClass("fa-long-arrow-up")
    getGeneralFile(1, 20);
}
// creator: 张旭博，2018-04-20 19:47:19，普通搜索 - 搜索
function searchBtn2() {
    var type =    $("#chooseInFolder").data("type")
    var name = $("#fileNameOrSn2").val()
    getSheetList(1 , 20, type, name)
}

// creator: 张旭博，2018-04-20 19:47:39，高级搜索 - 按钮
function advanceSearchBtn() {
    bounce.show($("#advancedSearch"))
    $("#advancedSearch").find("input,textarea,select").val("");
}

// creator: 张旭博，2018-04-20 14:49:37，根据名字和编号搜索文件(普通检索)
var sphdSocketArr = [];
function getGeneralFile(cur, pageSize){
    var name = $("#fileNameOrSn").val();
    var creator = $("#search_applier").val()
    var condition = {
        currentPageNo: cur,
        pageSize: pageSize,
        name: name,
        fileSn: name,
        type: 1,
        creator: creator
    }
    if ($("#searchSort li").eq(1).find(".sort").is(":visible")) {
        if($("#searchSort li").eq(1).find("i").hasClass('fa-long-arrow-up')){
            condition.type = 2
        }else{
            condition.type = 3
        }
    } else {
        condition.type = 2
    }
    $.ajax({
        url:"../res/generalFindFile.do",
        data: condition,
        success: function(data){
            data = data["data"];
            var fileInfo = data.listFile,
                pageInfo = data.pageInfo;
            var categoryInfo = data.listCategory;
            var creatorInfo = data.listCreator;
            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"]

            setPage($("#ye-search-file"), currentPageNo, totalPage, "generalFile") ;
            $("#btn-group").hide();
            $(".ty-searchContent").show().siblings().hide();
            $(".ty-searchContent .fileContent").show().siblings().hide();

            var fileListStr = '';
            if(fileInfo.length === 0){
                fileListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件</div>';
            }else{
                for(var t in categoryInfo){
                    sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                        console.log('普通搜索： ' + data)
                        var result = JSON.parse(data);
                        var fileResult = [];
                        fileResult.push(result.res);
                        var resultStr = getFileListStr(fileResult);
                        $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                        $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                    }, null, 'custom', categoryInfo[t]));
                }
                fileListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 '+pageInfo.totalResult+' 个文件符合查询结果</div>'
                    +   getFileListStr(fileInfo);
            }

            var applierListStr = '<option value="">--请选择--</option>'
            if ($("#search_applier").html() === '') {
                for (var i in creatorInfo) {
                    applierListStr += '<option value="'+creatorInfo[i].userID+'">'+creatorInfo[i].userName+'</option>'
                }
                $("#search_applier").html(applierListStr)
            }

            $(".ty-searchContent .fileContent .searchFile").html(fileListStr);
        }
    });
}

// creator: 张旭博，2018-04-20 19:53:29，高级检索 -- 文件检索
function advancedSearch_file(currentPageNo,pageSize) {
    var data = {
        "currentPageNo"     : currentPageNo,
        "pageSize"          : pageSize
    };
    $("#advancedSearch").find("input,select").each(function () {
        if($(this).val() !== ""){
            var key = $(this).attr("class");
            data[key] = $(this).val();
        }
    });
    $.ajax({
        url : '../res/advancedFindFile.do',
        data : data,
        success: function(data) {
            bounce.cancel();
            data = data["data"];
            var fileInfo = data.listFile,
                pageInfo = data.pageInfo;
            var catorageInfo = data.listCategory;
            // 设置分页信息
            var currentPageNo = pageInfo["currentPageNo"],
                totalPage = pageInfo["totalPage"];

            setPage($("#ye-search-file"), currentPageNo, totalPage, "advancedFindFile");
            $("#btn-group").hide();
            $(".ty-searchContent").show().siblings().hide();
            $(".ty-searchContent .fileContent").show().siblings().hide();

            // 展示查询结果（提示+文件列表）
            var fileListStr = '';
            if (fileInfo.length === 0) {
                fileListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件</div>';
            } else {
                fileListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 ' + pageInfo.totalResult + ' 个文件符合查询结果</div>'
                    + getFileListStr(fileInfo);
                for(var t in catorageInfo){
                    sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                        var result = JSON.parse(data);
                        var fileResult = [];
                        fileResult.push(result.res);
                        var resultStr = getFileListStr(fileResult);
                        $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                        $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                    }, null, 'custom', catorageInfo[t]));
                }
            }
            $(".ty-searchContent .fileContent .searchFile").html(fileListStr);
        }
    });
}

// creator: 张旭博，2018-04-20 19:53:29，高级检索 -- 文件类别检索
function advancedSearch_folder(currentPageNo,pageSize) {
    var folderName = $("#advancedSearch .folderName").val();
    $.ajax({
        url:"../res/findFolder.do",
        type:"post",
        data:{
            "currentPageNo"     : currentPageNo,
            "pageSize"          : pageSize,
            "name"              : folderName
        },
        success: function(data){
            bounce.cancel();
            data = data["data"];
            var folderInfo = data.listCategory,
                pageInfo = data.pageInfo;

            // 设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"];

            setPage( $("#ye-search-folder"), currentPageNo, totalPage, "findFolder") ;

            // 展示查询结果（提示+文件列表）
            var folderListStr = '';
            if(folderInfo.length === 0){
                folderListStr += '<div class="ty-alert ty-alert-error"><i class="fa fa-exclamation-circle"></i> 对不起，没有找到符合条件的文件类别</div>';
                $("#ye-search-folder").hide();
            }else{
                folderListStr += '<div class="ty-alert ty-alert-warning"><i class="fa fa-info-circle"></i> 共有 '+pageInfo.totalResult+' 个文件类别符合查询结果</div>'
                    + getFolderListStr(folderInfo);
                $("#ye-search-folder").show();
            }

            $("#btn-group").hide();
            $(".ty-searchContent").show().siblings().hide();
            $(".ty-searchContent .folderContent").show().siblings().hide();
            $(".ty-searchContent .searchFolderContent").html(folderListStr).attr("name",name);
        }
    });
}

// creator: 张旭博，2018-04-23 11:32:46，高级搜索 -- 按文件类别查询 -- 获取子级文件类别
function getChildFolder(folderId,parentFolder){
    var url = '';
    if(isGeneral || isSuper){
        url = '../res/getFolderAndChildFolderManager.do'
    }else{
        url = '../res/getFolderAndChildFolder.do'
    }
    $.ajax({
        url:url,
        type:"post",
        data:{
            "type"      : 1,
            "categoryId": folderId
        },
        success: function(data){
            data = data["data"];
            var childFolder  = data.childFolder;

            // 展示查询结果（提示+文件列表）
            if(childFolder.length === 0){
                getFile(1,20,folderId);
                $(".ty-searchContent .fileContent").show().siblings().hide();
                sphdSocketArr.push(sphdSocket.subscribe('sendSearchFile', function (data) {
                    var result = JSON.parse(data);
                    var fileResult = [];
                    fileResult.push(result.res);
                    var resultStr = getFileListStr(fileResult);
                    $(".searchInfo [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                    $(".mainFileList [data-id='" + result["res"].id + "']").replaceWith(resultStr);
                }, null, 'custom', folderId));
            }else{
                var folderListStr = getFolderListStr(childFolder,parentFolder);
                $(".ty-searchContent .folderContent").show().siblings().hide();
                $(".ty-searchContent .childFolder").children(":last").hide();
                $(".ty-searchContent .childFolder").append('<div class="childFolderContent">'+folderListStr+'</div>').attr("id",folderId).show().siblings().hide();
            }
        }
    });
}

// creator: 张旭博，2018-05-24 11:24:08，查询返回
function goBack(type) {
    switch (type) {
        case 'query':
            var fileContent = $(".ty-searchContent .fileContent"),
                folderContent = $(".ty-searchContent .folderContent");

            if(fileContent.find('.searchFile').html() !== ""){
                fileContent.find('.searchFile').html('');
                if(folderContent.find('.searchFolderContent').html() === ""){
                    $(".ty-fileContent").show().siblings().hide();
                    $("#fileNameOrSn").val("");
                    //清除搜索用到的订阅
                    for(var a=0; a< sphdSocketArr.length; a++){
                        sphdSocket.unsubscribe(sphdSocketArr[a]);
                    }
                }else{
                    fileContent.hide().siblings().show();
                }
            }else{
                if(folderContent.find('.childFolder').children().length > 1){
                    fileContent.find('.searchFile').html('');
                    folderContent.find('.childFolder').children(":last").remove();
                    folderContent.find('.childFolder').children(":last").show();
                }else if(folderContent.find('.childFolder').children().length === 1){
                    folderContent.find('.childFolder').html("");
                    folderContent.find('.searchFolder').show();

                }else{
                    folderContent.find('.searchFolderContent').html("");
                    $(".ty-fileContent").show().siblings().hide();
                    $("#advancedSearch").find("input,textarea,select").val("");
                    $("#fileNameOrSn").val("");
                    //清除搜索用到的订阅
                    for(var a=0; a< sphdSocketArr.length; a++){
                        sphdSocket.unsubscribe(sphdSocketArr[a]);
                    }
                }
            }
            break;
        case 'record':
            $("#main").show().siblings().hide();
            break;
    }
    if($(".ty-fileContent").is(":visible")) {
        $("#btn-group").show()
    } else {
        $("#btn-group").hide()
    }

}



//------------------------- 其他操作 ---------------------------//

// creator: 张旭博，2018-05-16 16:29:28，查看各种操作记录
function seeHandelRecordBtn(type){
    //打开弹窗
    bounce_Fixed.show($("#fileHandleRecord"));

    //更改弹窗标题
    var recordTitleName = '';
    switch (type){
        case 1:
            recordTitleName = '浏览记录';
            break;
        case 2:
            recordTitleName = '下载记录';
            break;
        case 5:
            recordTitleName = '移动记录';
            break;
        case 6:
            recordTitleName = '文件名称修改记录';
            break;
        case 7:
            recordTitleName = '文件编号修改记录';
            break;
    }
    $("#fileHandleRecord").find(".recordTitleName").html(recordTitleName);

    //渲染操作记录表格
    setFileHandelRecord(type);
}

// creator: 张旭博，2023-04-26 09:34:13，查看签收记录
function seeSignRecordBtn(selector) {
    bounce_Fixed2.show($("#signRecord"))
    if (selector) {
        var docInfo = selector.parents("tr").find("td.hd").html()
        docInfo = JSON.parse(docInfo)
        $("#signRecord .info_fileName").html(docInfo.name)
        $("#signRecord .info_fileSn").html(docInfo.fileSn)
        $("#signRecord .creator").html('操作时的版本号：G' + docInfo.changeNum + '<div class="text-right" style="flex: auto">操作：'+docInfo.updateName + ' ' +docInfo.updateDate+'</div>')
        docInfo.resHisId = docInfo.resSignedMes.resHisId
    } else {
        var docInfo = $("#docInfoScan").data("docInfo")
        $("#signRecord .info_fileName").html(docInfo.fileName)
        $("#signRecord .info_fileSn").html(docInfo.fileSn)
        $("#signRecord .creator").html('版本号：G' + docInfo.changeNum + '<div class="text-right" style="flex: auto">'+docInfo.creator+'</div>')
    }


    $.ajax({
        url: $.webRoot + '/res/getResHisSignedRecord.do',
        data: {
            resHisId: docInfo.resHisId, // 历史文件id
            type: selector?(docInfo.teminateState === '1'?4:5):(docInfo.changeNum>0?3:2) // 2-版本签收记录 4-废止签收记录 5-废止复用签收记录
        }
    }).then(res => {
        var data = res.data
        var listResNotice = data.listResNotice
        var str = ''
        for (var i in listResNotice) {
            var extinctionTime = listResNotice[i].extinctionTime
            if (extinctionTime) {
                extinctionTime = moment(extinctionTime).format("YYYY-MM-DD HH:mm:ss")
            } else {
                extinctionTime = '尚未签收'
            }
            str += '<tr>' +
                '<td>'+listResNotice[i].userName + ' ' + listResNotice[i].mobile +'</td>'+
                '<td>'+ extinctionTime +'</td>'+
                '</tr>'
        }
        $("#signRecord tbody").html(str)
    })
}

// creator: 张旭博，2023-04-26 11:35:02， 文件废止/复用记录
function seeTerminateRecordBtn(type) {
    bounce_Fixed.show($("#terminateRecord"))
    var fileId = $("#docInfoScan").data("currentFileId")
    var docInfo = $("#docInfoScan").data("docInfo")
    $("#terminateRecord .info_fileName").html(docInfo.fileName)
    $("#terminateRecord .info_fileSn").html(docInfo.fileSn)
    if (type === 4) {
        $("#terminateRecord .bounce_title").html("文件废止/复用记录")
    } else if (type === 5) {
        $("#terminateRecord .bounce_title").html("文件停用/复用记录")
    }
    $.ajax({
        url: $.webRoot + '/res/getResTemianteRecord.do',
        data: {
            fileId: fileId
        }
    }).then(res => {
        var data = res.data
        var listResHistory = data.listResHistory
        var str = ''
        for (var i in listResHistory) {
            var resSignedMes = listResHistory[i].resSignedMes
            str +=  '<tr data-id="'+resSignedMes.resHisId+'">' +
                    '<td>'+(listResHistory[i].teminateState === '1'?'废止':'复用') +'</td>'+
                    '<td>'+(listResHistory[i].updateDate) +'</td>'+
                    '<td>'+(listResHistory[i].updateName) +'</td>'+
                    '<td>G'+(listResHistory[i].changeNum) +'</td>'+
                    '<td>'+(resSignedMes.haveSignedNum + '/' + resSignedMes.signedNum) +'</td>'+
                    '<td><span class="link-blue" onclick="seeSignRecordBtn($(this))">查看</span></td>'+
                    '<td class="hd">'+JSON.stringify(listResHistory[i])+'</td>'+
                    '</tr>'
        }
        $("#terminateRecord tbody").html(str)
    })

}

// creator: 张旭博，2021-07-21 09:14:10，更新文件数据
function updateFile(fileId){
    $.ajax({
        url: "../res/getFileMessage.do",
        data: {"id" :fileId},
        success: function (res) {
            var state = res["success"] ;
            if(state !== 1){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }else{
                var list    = res["data"].resource;
                var listArr = [];
                listArr.push(list);
                var listStr = getFileListStr(listArr);
                $(".ty-fileItemActive").replaceWith(listStr);
                $("#mytest").html(listStr);
            }
        }
    })
}

// creator: 张旭博，2021-08-03 09:13:41，获取借阅人
function seeBorrowPerson(fileId, isDisable) {
    var data = {
        fileId: fileId
    }
    if (isDisable) {
        data = {
            fileHisId: fileId
        }
    }
    bounce_Fixed3.show($("#seeBorrowPerson"))
    $.ajax({
        url: '../res/getReadroomUserRecord.do',
        data: data,
        success: function (res) {
            var data = res.data
            var fileInfo = data.res || data.resHistory
            var borrowInfo = data.listUserDto
            fileInfo.borrowNum = borrowInfo.length
            fileInfo.changeNum = 'G' + fileInfo.changeNum
            for (var key in fileInfo) {
                $("#seeBorrowPerson [name='"+key+"']").html(fileInfo[key])
            }
            var str = ''
            for (var i in borrowInfo) {
                str +=  '<tr>' +
                            '<td>'+borrowInfo[i].userName+'</td>' +
                            '<td>'+chargeNull(borrowInfo[i].gender)+'</td>' +
                            '<td>'+borrowInfo[i].mobile+'</td>' +
                            '<td>'+chargeNull(borrowInfo[i].departName)+'</td>' +
                            '<td>'+chargeNull(borrowInfo[i].postName)+'</td>' +
                        '</tr>'
            }
            $(".table_borrowPerson").html(str)
        }
    })

}

//------------------------- 获取数据 ---------------------------//

// creator: 张旭博, 2020-05-05 10:31:35, 获取审批人
function getApprover() {
    $.ajax({
        url: "../res/getAllOidUserByResource.do" ,
        data: { userID: sphdSocket.user.userID },
        success: function (data) {
            var approverList = data["data"]["list"];
            var optStr = '<option value="">请选择本文件的审批人</option>';
            if (approverList && approverList.length >0){
                for(var i in approverList) {
                    optStr += '<option value="'+ approverList[i].userID +'">'+ approverList[i].userName +'</option>';
                }
            }
            $(".chooseApprover").html(optStr);
        }
    })
}

// creator: 张旭博，2018-05-16 16:46:20，获取各种操作记录
function setFileHandelRecord(type) {
    var currentFileId = $("#docInfoScan").data("currentFileId"),
        tableStr = '';
    switch (type){
        case 1:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>浏览人</td>'+
                '<td>浏览时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 2:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>下载人</td>'+
                '<td>下载时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 5:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>移动前</td>'+
                '<td>移动后</td>'+
                '<td>移动人</td>'+
                '<td>移动时间</td>'+
                '</tr>'+
                '</thead>';
            break;
        case 6:
        case 7:
            tableStr =  '<thead>' +
                '<tr>' +
                '<td>序号</td>'+
                '<td>修改前</td>'+
                '<td>修改后</td>'+
                '<td>修改人</td>'+
                '<td>修改时间</td>'+
                '</tr>'+
                '</thead>';
            break;
    }
    if(type === 1||type === 2){
        $.ajax({
            url:"../res/getRecordByShow.do",
            data:{
                "id"  : currentFileId,
                "type": type
            },
            success: function(data){
                if(data["success"] === 1){
                    data = data["data"];
                    tableStr += '<tbody>';
                    for(var i = 0; i < data.length; i++){
                        tableStr += '<tr>' +
                            '<td style="width: 10%">' + (i + 1) +'</td>'+
                            '<td style="width: 45%">' + data[i].createName + '</td>'+
                            '<td style="width: 45%">' + data[i].createDate.substring(0,19) + '</td>'+
                            '</tr>'
                    }
                    tableStr += '</tbody>';
                    $("#fileHandleRecord .recordTable").html(tableStr)
                }else{
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }else{
        $.ajax({
            url:"../res/getRecordByUpAndMove.do",
            data:{
                "fileId"  : currentFileId,
                "operation": type
            },
            success: function(data){
                if(data["success"] === 1){
                    data = data["data"];
                    tableStr += '<tbody>';
                    for(var i = 0; i < data.length; i++){
                        tableStr += '<tr>' +
                            '<td style="width: 7%">' + (i + 1) +'</td>'+
                            '<td style="width: 30%">' + data[i].nameBefore + '</td>'+
                            '<td style="width: 30%">' + data[i].nameAfter + '</td>'+
                            '<td style="width: 15%">' + data[i].createName + '</td>'+
                            '<td style="width: 18%">' + data[i].createDate.substring(0,19) + '</td>'+
                            '</tr>'
                    }
                    tableStr += '</tbody>';
                    $("#fileHandleRecord .recordTable").html(tableStr)
                }else{
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }
}

// creator: 张旭博，2018-07-05 14:28:42，获取文件详情
function getFileInfo(obj){
    var fileId = obj.parents(".ty-fileItem").data("id");
    $.ajax({
        url: "../res/getFileMessage.do",
        data: {"id" : fileId},
        type: "post",
        dataType: "json",
        beforeSend:function(){  } ,
        success: function (data) {
            var status = data["success"] ;
            if(status != 1){
                $("#mt_tip_ms").html("连接失败，请刷新重试！");
                bounce.show($("#mtTip"));
            }
        },
        error:function (err) {
            $("#mt_tip_ms").html("连接失败，请刷新重试！");
            bounce.show($("#mtTip"));
        } ,
        complete:function(){  }
    });
}

//updator:王静 2017-08-03 15:38:23 获取换版后的历史文件列表
function getRecord(currentPageNo, totalPage){
    //得到地址栏传过来的ID
    var fileId = $("#resHistory").data("fileId");
    var data = {
        "fileId": fileId,
        "currentPageNo": currentPageNo,
        "pageSize": totalPage
    }
    $.ajax({
        url: "getUpdateFile.do",
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"],
                jsonStr = JSON.stringify( { "fileId" : fileId} ) ;

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo);
            setPage( $("#ye_record"), currentPageNo, totalPage, "changeRecord", jsonStr) ;
            $("#resHistory .fileList").html(fileListStr)
        }
    })
}

//------------------------- 公共方法 -----------------------//

// updater: 张旭博，2018-04-20 10:30:37，设置文件列表
function getFile(currentPageNo , pageSize , categoryId){
    var url = "getFile.do" ;
    if(isGeneral || isSuper){
        url = "getFileByManager.do" ;
    }
    var type = $("#fileSort").data('type')
    if(!type){
        type = 1;
    }
    // type - "1"代表获取的是按时间降序的列表，“2”代表获取的是时间升序的列表，“3”代表获取的是文件编号升序的列表，“4”代表的是获取文件编号降序的列表
    var data = {
        categoryId : categoryId,
        currentPageNo : currentPageNo,
        pageSize : pageSize,
        type : type
    }
    var isShowAbolishFile = $("#switchShowAbolishFile input").prop("checked")
    if (isShowAbolishFile) {
        data.teminateState = 1
    }
    $.ajax({
        url: url ,
        data: data,
        success: function (data) {
            data = data["data"] ;

            var pageInfo = data["pageInfo"],
                fileInfo = data["list"];

            //设置分页信息
            var currentPageNo   = pageInfo["currentPageNo"],
                totalPage       = pageInfo["totalPage"],
                jsonStr = JSON.stringify( { "categoryId" : categoryId} ) ;

            //设置文件信息
            var fileListStr = getFileListStr(fileInfo);
            if($(".ty-searchContent").is(':visible')){
                setPage( $("#ye-search-file"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                $(".ty-searchContent .fileContent").find('.searchFile').html(fileListStr).attr("type","folder");
            }else{
                setPage( $("#ye_con"), currentPageNo, totalPage, "fileMessage", jsonStr) ;
                $(".ty-fileList").html(fileListStr);
                $(".ty-fileList").attr("type","folder");
            }
        }
    })
}

// creator: 张旭博，2018-04-24 10:17:16，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        // $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="'+$.webRoot+'/css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        if ($(".ty-fileContent").is(":visible")) {
            $("#ye_con").show() ;
            // $("#fileSort").show() ;
        }
        var trashID = $(".trash").data("id")
        for(var i = 0; i < fileInfo.length; i++){
            var fileItem = fileInfo[i]
            if (fileItem.resEntity) {
                fileItem = fileItem.resEntity
            }
            var id          = fileItem.id,
                file        = fileItem.file,
                category    = fileItem.category,
                name        = fileItem.name,
                size        = fileItem.size,
                path        = fileItem.path,
                fileSn      = fileItem.fileSn,
                changeNum   = fileItem.changeNum,
                fileType    = fileItem.version,
                operation   = fileItem.operation,
                updateName  = fileItem.updateName || fileItem.createName,
                updateDate  = fileItem.updateDate || fileItem.createDate,
                updator     = fileItem.updator || fileItem.creator,
                version     = fileItem.version.toLowerCase(),
                isTrash     = fileItem.isTrash,
                enabled     = fileItem.enabled,
                teminateState     = fileItem.teminateState, // 废止状态
                isTrashFolder = trashID === fileItem.category,

                handleStr = ''             //定义操作按钮部分字符串


            //格式化数据
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';

            let funcName = {
                seeOnline:  '在线预览',
                download:   '下载',
                basicMsg:   '基本信息',
                recovery:   '还原',
                delete:     '删除',
                move:       '移动',
                more:       '更多',
                changeVersion:      '换版',
                changeVersionApply: '换版申请',
                changeVersionRecord:'换版记录',
                scanSet:            '使用权限设置',
                changeFileName:     '修改文件名称',
                changeFileNo:       '修改文件编号',
                disable:            '禁用',
                relateSheet:        '相关表格',
                sheetRelate:        '表格关联',
                sheetRelateHandleRecord: '表格关联的操作记录',
                sheetRelateRemove:  '解除关联',
                fileAbolish:        '文件废止',
                fileAbolishApply:   '文件废止申请',
                fileReuse:          '文件复用'
            }

            let func = {}, moreFunc = {}

            if (file) {
                // 普通职工 - 换版记录 - 功能
                func = {
                        seeOnline: { path: path, disable: customVersion.indexOf(version) === -1 || (!enabled && (!isGeneral && !isSuper)) },
                        download: { path: path, download: name + '.' + fileType, disable: !enabled && (!isGeneral && !isSuper)},
                        basicMsg: { history: '' }
                }
                if (isGeneral) {
                    // 总务 - 换版记录 - 功能（加禁用）
                    func.disable = {color: 'red', disable: !enabled || (teminateState === '1' || teminateState === '2')}
                }
            } else {
                if (isGeneral) {
                    // 总务 - 功能
                    if(isTrash) {
                        // 总务 - 暂时不用的文件 - 功能
                        func = {
                            seeOnline: { path: path,  disable: customVersion.indexOf(version) === -1 },
                            download: { path: path, download: name + '.' + fileType },
                            recovery: {},
                            delete: {color: 'red'},
                            more: {}
                        }
                        if (!isTrashFolder) {
                            delete  func.recovery
                        }
                        moreFunc = {
                            basicMsg: { },
                            changeVersionRecord: { disable: changeNum   === 0 }
                        }
                    } else {
                        // 总务 - 其他文件 - 功能
                        func = {
                            seeOnline: { path: path, disable: customVersion.indexOf(version) === -1 },
                            download: { path: path, download: name + '.' + fileType },
                            changeVersion: {disable: operation === '3' || teminateState === '1'},
                            sheetRelate: {disable: operation === '3' || teminateState === '1'},
                            more: {}
                        }
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: changeNum   === 0 },
                            scanSet: {},
                            move: { disable: operation === '3' },
                            changeFileName: { disable: operation === '3' || teminateState === '1'},
                            changeFileNo: { disable: operation === '3' || teminateState === '1' },
                            fileAbolish: { visible: teminateState !== '1', disable: operation === '3' },
                            fileReuse: { visible: teminateState === '1', disable: operation === '3' },
                            sheetRelateHandleRecord: {}
                        }
                    }
                } else {
                    // 普通员工 和 超管 - 功能
                    if (isSuper && isTrash) {
                        // 超管 - 暂时不用的文件 - 功能
                        func = {
                            seeOnline: { path: path },
                            download: { path: path, download: name + '.' + fileType },
                            more: {}
                        }
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: changeNum   === 0 }
                        }
                    } else {
                        // 普通职工 - 功能 （普通职工无暂时不用的文件）
                        func = {
                            seeOnline: { path: path,  disable: customVersion.indexOf(version) === -1 },
                            download: { path: path, download: name + '.' + fileType },
                            changeVersionApply: {disable: operation === '3' || teminateState === '1'},
                            relateSheet: {disable: operation === '3' || teminateState === '1' },
                            more: {}
                        }
                        moreFunc = {
                            basicMsg: {},
                            changeVersionRecord: { disable: changeNum   === 0 },
                            changeFileName: { disable: operation === '3' || sphdSocket.user.userID !== updator || teminateState === '1' },
                            changeFileNo: { disable: operation === '3' || sphdSocket.user.userID !== updator || teminateState === '1' },
                            fileAbolishApply: {visible: teminateState !== '1', disable: operation === '3'},
                            sheetRelateHandleRecord: {}
                        }
                    }
                }
            }
            for (let key in func) {
                handleStr += '<a class="aBtn" type="btn" name="'+key+'"'
                let attr = func[key]
                for (var item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</a>'
            }
            handleStr += '<ul class="hel" style="display: none">'
            for (let key in moreFunc) {
                let visible = moreFunc[key].visible
                if (typeof visible === 'undefined' || visible === true) {
                    handleStr += '<li class="liBtn" type="btn" name="'+key+'"'
                    let attr = moreFunc[key]
                    for (let item in attr) {
                        handleStr += ' ' +item + '="' + attr[item] + '"'
                    }
                    handleStr += '>' + funcName[key] + '</li>'
                }
            }
            handleStr += '</ul>'

            let disabledPngStr = enabled === false?'<img class="disablePng" src="'+$.webRoot +'/css/content/img/disabled.png">':''
            let abolishPngStr = teminateState==='1'?'<img class="abolishPng" src="'+$.webRoot +'/css/content/img/abolish.png">':''
            let reusePngStr = teminateState==='2'?'<img class="reusePng" src="'+$.webRoot +'/css/content/img/reuse.png">':''

            fileListStr +=  '<div class="ty-fileItem" data-id="'+id+'" pid="'+category+'" data-id="'+id+'">'+
                                disabledPngStr + abolishPngStr + reusePngStr +
                                '<div class="ty-fileType ty-file_'+fileType+'"></div>'+
                                '<div class="ty-fileInfo">'+
                                '   <div class="ty-fileRow">'+
                                '       <div class="ty-fileName" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                                '       <div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                                '       <div class="ty-fileVersion" changeNum="'+ changeNum +'">'+'G'+changeNum+'</div>'+
                                '   </div>'+
                                '   <div class="ty-fileRow">'+
                                '       <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate + '</div>'+
                                '       <div class="ty-fileHandle">'+ handleStr + '</div>'+
                                '   </div>'+
                                '</div>'+
                            '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2022/3/12 20:09，根据文件数据返回表格列表字符串（可选择）
function getFileListStrBySheet(fileInfo, canChoose) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        // $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="'+$.webRoot+'/css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        if ($(".ty-fileContent").is(":visible")) {
            $("#ye_con").show() ;
            // $("#fileSort").show() ;
        }
        for(var i = 0; i < fileInfo.length; i++){
            var fileItem = fileInfo[i]
            if (fileItem.resAtt) {
                // 表格信息 当通过文件查表格
                fileItem = fileItem.resAtt
            }
            var id          = fileItem.id,
                name        = fileItem.name,
                size        = fileItem.size,
                path        = fileItem.path,
                fileSn      = fileItem.sn, // 表格文件的文件编号，与正常文件的文件编号的字段不一致
                changeNum   = fileItem.version, // 表格文件的版本号，与正常文件的版本号的字段不一致
                fileType    = fileItem.type,
                operation   = fileItem.operation,
                updateName  = fileItem.updateName || fileItem.createName,
                updateDate  = fileItem.updateDate || fileItem.createDate,
                updator     = fileItem.updator || fileItem.creator,
                version     = fileItem.type.toLowerCase(),// 表格文件的后缀，与正常文件后缀的字段不一致
                enabled     = fileItem.enabled, // true 已关联 false 未关联
                handleStr = ''            //定义操作按钮部分字符串

            //格式化数据
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';

            updateDate = moment(updateDate).format("YYYY-MM-DD HH:mm:ss")

            let funcName = {
                seeOnline: '在线预览',
                download: '下载',
                basicMsg: '基本信息',
                recovery: '还原',
                delete: '删除',
                more: '更多',
                changeVersion: '换版',
                changeVersionApply: '换版申请',
                changeVersionRecord: '换版记录',
                move: '移动',
                scanSet: '使用权限设置',
                changeFileName: '修改文件名称',
                changeFileNo: '修改文件编号',
                disable: '禁用',
                relateSheet: '相关表格',
                sheetRelate: '表格关联',
                sheetRelateHandleRecord: '表格关联的操作记录',
                sheetRelateRemove: '解除关联',
                changeSheetVersion: '换版',
                changeSheetMsg: '修改',
                sheetHandleRecord: '操作记录',
                relateRecord: '关联记录',
                relate: '所关联的文件',
                related: '曾关联的文件'
            }

            let func = {}
            let moreFunc = {}

            if (canChoose) {
                func = {
                    seeOnline: { path: path, disable: customVersion.indexOf(version) === -1 },
                    download: { path: path, download: name + '.' + fileType},
                }
            } else {
                if (isGeneral) {
                    func = {
                        seeOnline: { path: path, disable: customVersion.indexOf(version) === -1 },
                        download: { path: path, download: name + '.' + fileType },
                        changeSheetVersion: {},
                        sheetRelateRemove: {color: 'red'},
                        more: {}
                    }
                    moreFunc = {
                        changeSheetMsg: {},
                        sheetHandleRecord: {},
                        relateRecord: {},
                        relate: {},
                        related: {}
                    }
                } else {
                    func = {
                        seeOnline: { path: path, disable: customVersion.indexOf(version) === -1 },
                        download: { path: path, download: name + '.' + fileType },
                        more: {}
                    }
                    moreFunc = {
                        sheetHandleRecord: {},
                        relateRecord: {},
                        relate: {},
                        related: {}
                    }
                }
            }

            for (let key in func) {
                handleStr += '<a class="aBtn" type="btn" name="'+key+'"'
                let attr = func[key]
                for (var item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</a>'
            }
            handleStr += '<ul class="hel" style="display: none">'
            for (let key in moreFunc) {
                handleStr += '<li class="liBtn" type="btn" name="'+key+'"'
                let attr = moreFunc[key]
                for (let item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</li>'
            }
            handleStr += '</ul>'

            var chooseStr = ''
            if (canChoose) {
                chooseStr = '   <div class="ty-radio">' +
                            '       <input type="radio" id="resource_choose_'+id+'" name="resource_choose">' +
                            '       <label for="resource_choose_'+id+'"></label>' +
                            '   </div>'
            }

            //单条文件代码字符串拼接
            fileListStr +=  '<div class="ty-fileItem fileItemRadioChoose" data-id="'+id+'">'+
                            chooseStr +
                            '   <div class="ty-fileType ty-file_'+fileType+'"></div>'+
                            '   <div class="ty-fileInfo">'+
                            '       <div class="ty-fileRow">'+
                            '           <div class="ty-fileName" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                            '           <div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                            '           <div class="ty-fileVersion" changeNum="'+ changeNum +'">'+'G'+changeNum+'</div>'+
                            '       </div>'+
                            '       <div class="ty-fileRow">'+
                            '           <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate + '</div>'+
                            '           <div class="ty-fileHandle">'+ handleStr + '</div>'+
                            '       </div>'+
                            '   </div>'+
                            '   <div class="hd">'+JSON.stringify(fileItem)+'</div>'+
                            '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2018-04-24 10:17:55，根据文件夹数据返回文件类别字符串
function getFolderListStr(folderInfo,parentFolder) {
    var folderListStr = '';
    if(folderInfo.length !== 0){
        for(var i = 0; i < folderInfo.length; i++) {
            var id = folderInfo[i].id,
                name = folderInfo[i].name,
                allParentCategory = folderInfo[i].allParentCategory,
                firstLevelClass = '';

            if (parentFolder) {
                allParentCategory = parentFolder;
            } else {
                if (allParentCategory === null) {
                    allParentCategory = '';
                    firstLevelClass = 'firstLevel'
                } else {
                    firstLevelClass = ''
                }
            }

            folderListStr += '<div class="ty-folderItem" id="' + id + '">' +
                '<div class="ty-left ty-folderType"><i class="fa fa-folder"></i></div>' +
                '<div class="ty-folderInfo ty-left">' +
                '<div class="ty-folderName ' + firstLevelClass + '">' + name + '</div>' +
                '<div class="ty-folderParent">' + allParentCategory + '</div>' +
                '</div>' +
                '</div>';
        }
    }
    return folderListStr;
}

// creator: 侯杏哲 2017-12-04  获取一级文件夹列表
function getFirstDoc(name){
    var url = "getInitialFolder.do" ;
    if( isGeneral || isSuper){ // 超管或总务
        url = "getInitialFolderByManage.do" ;
    }
    $.ajax({
        url: url ,
        data: { "type": 1  },
        success: function (res) {
            var data = res["data"] ,
                listFirstCategory = data["listFirstFolder"],
                authFolder = data["authFolder"],    //  0是没有权限 1是有权限
                firstLevelStr = "";
            firstLevelStr += '<div class="level1" level="1">';
            if(listFirstCategory.length === 0){
                $("#fileUploadBtn").attr("class","ty-btn ty-btn-big ty-btn-gray ty-circle-3");
            }
            for(var i in listFirstCategory){
                if (listFirstCategory[i].isTrash) {
                    // 如果是暂时不用的文件（3个地方处理不同 main: 全部显示， chooseSaveFolder: 不显示，move: 只显示此文件夹，不显示子级）
                    if (name === 'main') {
                        firstLevelStr +=    '<li><div class="ty-treeItem trash" title="'+ listFirstCategory[i]["name"] +'" data-id="'+listFirstCategory[i]["id"]+'" data-child="true">' +
                                            (listFirstCategory[i].childStatus == 1?'<i class="fa fa-angle-right"></i>':'<i class="ty-fa"></i>') +
                                            '<i class="fa fa-trash"></i>' +
                                            '<span>'+listFirstCategory[i]["name"]+'</span></div></li>'
                    } else if (name === 'moveTo') {
                        if ($(".ty-fileItemActive:visible .abolishPng").length > 0) {
                            // 废止的文件不能移动到暂时不用的文件夹（不显示）
                            firstLevelStr += ''
                        } else {
                            firstLevelStr +=    '<li><div class="ty-treeItem trash" title="'+ listFirstCategory[i]["name"] +'" data-id="'+listFirstCategory[i]["id"]+'" data-child="false">' +
                                                '<i class="ty-fa"></i>' +
                                                '<i class="fa fa-trash"></i>' +
                                                '<span>'+listFirstCategory[i]["name"]+'</span></div></li>'
                        }
                    } else {
                        firstLevelStr += ''
                    }

                } else {
                    firstLevelStr +=    '<li><div class="ty-treeItem '+(listFirstCategory[i].isTrash?'trash':'')+'" title="'+ listFirstCategory[i]["name"] +'" data-id="'+listFirstCategory[i]["id"]+'" data-child="'+(listFirstCategory[i].childStatus === '1')+'">' +
                        (listFirstCategory[i].childStatus == 1?'<i class="fa fa-angle-right"></i>':'<i class="ty-fa"></i>') +
                        '<i class="fa fa-folder"></i>' +
                        '<span>'+listFirstCategory[i]["name"]+'</span></div></li>'
                }



            }
            firstLevelStr += '<p id="tstip"></p>'+
                '</div>';
            $(".ty-colFileTree[data-name='"+name+"']").html(firstLevelStr)
            if(authFolder === 0){
                $("#fileUploadBtn").attr("class","ty-btn ty-btn-big ty-btn-gray ty-circle-3");
                $("#folderUploadBtn").attr("class","ty-btn ty-btn-big ty-btn-gray ty-circle-3");
            }
        }
    });
}
/* creator：hxz 2020-10-18 10:13:27 点击新增文件夹 */
function newClass() {
    let activeObj = $(".ty-treeItemActive:visible"), folderName = "全部文件", categoryId = null;
    if(activeObj.length >0){
        $("#scanSetFolerBtn").attr("onclick", "scanSetBtn('son')")
        $("#isNew").val('son');// 防止设置不点击
        folderName = $(".ty-treeItemActive:visible").attr("title");
        categoryId = $(".ty-treeItemActive:visible").data("id");
        //判断该类别下是否有文件
        $.ajax({
            url: "../res/clickButtenJudgementFileByCentre.do",
            data: { "categoryId": categoryId },
            success: function (res) {
                let data = res['data'];
                let status = data["state"]; // 文件夹的状态 有0-3的4种情况 0-文件夹不存在 1-文件夹下什么都没有 2-文件夹下有东西，不能删除 3-文件夹没有权限。
                // if(!status ){
                if(status == 0 || status == 3){
                    let str = '<p>文件夹不存在！</p>' ;
                    if(status == 3){
                        str = '<p>文件夹没有权限！</p>' ;
                    }
                    bounce.show( $("#tip") ) ; $("#tipMess").html(str);
                }else{
                    //显示弹窗
                    let authCount = data['authCount'];
                    $(".hasSettedNum").html(authCount);
                    bounce.show($('.bounce-newClass'));

                }
            }
        })
    }else {
        $("#scanSetFolerBtn").attr("onclick", "scanSetBtn('same')")
        $(".hasSettedNum").html(0);
        bounce.show($('.bounce-newClass'));
    }
    $(".recentParentFolder").html(folderName);
    $(".bounce-newClass .categoryName").val("");
}

// creator: 侯杏哲 2017-12-04 查看设置按钮
var userArr = [] ; // 存储查看设置之前的人员
var sysDeparAndUserList = null; // 系统操作一套数据
function scanSetBtn(type, fileThisObj){
    // type - "son":新增子级文件夹，"same":新增同级文件夹，不传表示设置已有文件加的权限, "file": 文件的
    // 超管不能设置查看设置的
    let userType = chargeRole("超管");
    if (userType) {
        return false;
    }
    let isNew = type || "";
    $("#isNew").val(isNew);
    let categoryId = $(".ty-treeItemActive:visible").data("id") || 0;
    let packName = $(".ty-treeItemActive:visible").attr("title") ;
    let url = "getForderAuthDepsAndUsers.do" ;
    let data = { "categoryId" : categoryId } ;
    if(isNew == "son"||isNew == "same"){ // 新增文件夹的
        data['type'] = 1; // type 1新增文件夹 2正常修改权限
        if(isNew === "same"){
            categoryId = 0 ;
        }
        packName = $.trim($(".bounce-newClass .categoryName").val());
        $(".packName").html(packName);
        $(".packType").html("文件夹");

    }else if(isNew === "file"){ // 设置文件的
        let fileItemObj = fileThisObj.parents(".ty-fileItem")
        packName = fileItemObj.children(".ty-fileName").html();
        var fileId = fileItemObj.data("id"); // 这里是文件id
        $(".packName").html(packName);
        $(".packType").html("文件");
        url =  "../res/getFileAuthDepsAndUsers.do" ;
        data = {"fileId": fileId };

    }else{ // 设置已有文件加的权限
        $(".packName").html( packName);
        data['type'] = 2;
    }
    bounce.show( $("#scanSet") );
    $("#categoryId").val(categoryId) ;
    $.ajax({
        url :url ,
        data : data ,
        success:function ( res ) {
            let success = res["success"] ;
            if(success != 1){
                bounce.show( $("#tip") ) ; $("#tipMess").html( "获取数据失败，请刷新重试！" ) ; return false ;
            }
            let data = res["data"] ;
            let fileAuthTree = data["fileAuthTree"]  ;
            let catAuthTree = data["catAuthTree"]  ;
            let resLockUuid = data["resLockUuid"]  ;
            if(isNew === "file") {
                sysDeparAndUserList = fileAuthTree ;
            }else {
                sysDeparAndUserList = catAuthTree ;
            }
            let isUpdate = initData(sysDeparAndUserList); // 大于零表示修改
            $("#scanSet").data("isupdate", isUpdate);
            $("#scanSet").data("resLockUuid", resLockUuid);
            $("#allRight").data("type","depart");
            if(isNew === "file"){
                $("#allRight").data("type","user");
                $(".changeTypeLeft").html("切换为按部门展示");
            }
            $("#nowRight").data("type","user");
            let info = {'key':"havRight", 'val':1};
            var count = countSet(sysDeparAndUserList, info);
            $(".selectedNum").html(count);
            chargeState();
            $("#changeTypeLeft").html("直接展示全部职工");
            $("#changeTypeRight").html("切换为按部门展示");
        }
    });
}

function chargeState(){
    var leftType = $("#allRight").data("type");
    var rightType = $("#nowRight").data("type");
    if( leftType==="depart"){
        var str1 = renderDataByType(1, sysDeparAndUserList, 1);
        $("#allRight").html(str1) ;
    }else if( leftType==="user"){
        var str1 = renderDataByType(2, sysDeparAndUserList, 1);
        $("#allRight").html(str1) ;
    }
    if( rightType==="depart"){
        var str2 = renderDataByType(1, sysDeparAndUserList, 2);
        $("#allRight").html(str1) ;
        $("#nowRight").html(str2) ;
    }else if( rightType==="user"){
        var str2 = renderDataByType(2, sysDeparAndUserList, 2);
        $("#nowRight").html(str2) ;
    }
    let isupdate = $("#scanSet").data("isupdate");
    if(isupdate > 0){ // 修改的可以展示第二行
        var cancelStr = cancelOrGetStr(sysDeparAndUserList, 1);
        var getStr = cancelOrGetStr(sysDeparAndUserList, 2);
        if(cancelStr.length < 10 && getStr.length < 10){
            $(".getCon").hide();
            $(".cancelCon").hide();
            $(".changeCon").hide();
            $("#cancelNum").html(0)
            $("#getNum").html(0)
        }else {
            $(".changeCon").show();
        }
        if(cancelStr.length > 10){
            let info = {'key':"changeSta", 'val':1};
            let cancelNum = countSet(sysDeparAndUserList, info)
            $("#cancelNum").html(cancelNum);
            $(".cancelCon").show();
            $("#cancelRight").html(cancelStr) ;
        }else{
            $(".cancelCon").hide();
            $("#cancelNum").html(0);
        }
        if(getStr.length > 10){
            let info = {'key':"changeSta", 'val':2};
            let getNum = countSet(sysDeparAndUserList, info)
            $("#getNum").html(getNum);
            $(".getCon").show();
            $("#getRight").html(getStr);
        }else {
            $(".getCon").hide();
            $("#getNum").html(0);

        }
    }else{
        $(".getCon").hide();
        $(".cancelCon").hide();
        $(".changeCon").hide();
        $("#cancelNum").html(0)
        $("#getNum").html(0)
    }
}
function initData(treeData, pName){
    let allCount = 0
    pName = pName || ""
    for(let i = 0 ; i < treeData.length; i++){
        let count = 0
        let deparID = treeData[i]['id']
        let pDepartname = treeData[i]['name']
        let userList = treeData[i]["userList"]; // 人员列表
        let subDeparList = treeData[i]["subList"]; // 部门列表
        let sonPName = pName + pDepartname + ">>"

        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j]['roleCode'] == 1 ;
                userList[j]['deparID'] = deparID ;
                userList[j]['departsName'] = sonPName ;
                userList[j]['havRight'] = isok ? 1 : 0 ;
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += initData(subDeparList, sonPName);
        }
        treeData[i]['count'] = count;
        allCount += count ;
        treeData[i]['leftOpen'] = false;
        treeData[i]['rightOpen'] = false;
    }
    return allCount;
}
// create : hxz 2020-10-16 获取树里某键值对应的 人数
function countSet(treeData, info){
    let key = info['key']
    let val = info['val']
    let allCount = 0 ;
    for(let i = 0 ; i < treeData.length; i++){
        var count = 0
        var userList = treeData[i]["userList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j][key] == val ;
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += countSet(subDeparList, info);
        }
        if(key == 'havRight'){ // 统计是否有权限的人数赋值给部门count键，方便页面读取
            treeData[i]['count'] = count;
        }
        allCount += count;
    }
    return allCount;
}
/* creator : 侯杏哲 2017-12-04 设置权限 - 取消按钮  */
function cancelChangeRight(type){
    let isNew = $("#isNew").val();
    if(isNew === "same" || isNew === "son"){
        bounce.show($(".bounce-newClass"));
    }else{
        bounce.cancel()
    }
}
/* creator : 侯杏哲 2017-12-04 设置权限 - 确定按钮  */
function sureChangeRight(type){
    // type : 1 表示已经提示过，直接提交， 没传表示需要提示
    // 判断是否为新增文件夹的
    let isNew = $("#isNew").val();
    if(isNew == "same" || isNew == "son"){
        let havRightUser = getRightUser(sysDeparAndUserList) ;
        $("#havRightUserCon").html(JSON.stringify(havRightUser));
        $(".hasSettedNum").html(havRightUser.length);
        bounce.show($(".bounce-newClass"));
        return false;
    }
    // 判断选中的是否跟原来一样
    let RchangeightNum = diffCharge(sysDeparAndUserList) ;
    if(RchangeightNum == 0){
        bounce.show( $("#tip") ) ; $("#tipMess").html( "当前选择的人员与原来完全相同！" ) ;
        return false ;
    }

    // 处理权限设置
    let isUpdate = $("#scanSet").data("isupdate");
    let operateType = 1 ; // 默认设置权限
    if(Number(isUpdate) > 0 ){
        operateType = 2 // 修改权限
    }
    if(Number(isUpdate) > 0 && !type){
        bounce_Fixed.show($("#scanSetTip"));
        let getNum = $("#getNum").html();
        let cancelNum = $("#cancelNum").html();
        let packName = $("#scanSet .packName").html() ;
        let packType = $("#scanSet .packType:eq(0)").html() ;
        let tipStr = "您本次";
        if(Number(getNum) > 0){
            tipStr += "新增加的"+ getNum +"位职工有"+ packName + packType +"的全部使用权限<br>" ;
        }
        if(Number(cancelNum) > 0){
            tipStr += "取消了"+ cancelNum +"位职工在"+ packName + packType +"的全部使用权限<br>";
        }
        if(packType == "文件夹"){
            tipStr += "<p>您可在各下级文件夹中进行限制性设置！</p>";
        }
        $("#scanSetTip .bonceCon").html(tipStr);
        return false;
    }
    let dataStr = "";
    let url = "../res/updateFolderAuth.do";
    if(isNew == "file"){ // 设置文件的
        dataStr = "&fileId=" + $(".ty-fileItemActive:visible").data("id");
        url = "../res/updateFileAuth.do";
    }else{
        dataStr = "&categoryId=" + $("#categoryId").val();
    }
    // let categoryId = $(".ty-treeItemActive").attr("id") ;
    let havRightUser = getRightUser(sysDeparAndUserList) ;
    havRightUser.forEach(function(user){
        dataStr += "&userID=" + user.userID ;
    })
    var resLockUuid = $("#scanSet").data("resLockUuid")
    dataStr += '&resLockUuid=' + resLockUuid
    $.ajax({
        url: url,
        data: dataStr ,
        success: function (data) {
            var success = data["success"] ;
            if( success == 1 ){
                bounce.cancel();bounce_Fixed.cancel(); layer.msg("设置成功") ;
            }else{
                bounce.show( $("#tip") ) ;bounce_Fixed.cancel(); $("#tipMess").html( "设置失败！" ) ;
            }
        }
    })
}
// create :hxz 2020-10-13 切换展示员工列表和部门
function changeType(thisObj , location) {
    let type = thisObj.html() == "直接展示全部职工" ? "user" : "depart" ;
    let html = thisObj.html() == "直接展示全部职工" ? "切换为部门展示" : "直接展示全部职工" ;
    thisObj.html(html);
    if(location == "left"){
        $("#allRight").data("type", type);
    }else{
        $("#nowRight").data("type", type);
    }
    chargeState();
}

/*  create :hxz 2020-10-13 渲染一四象限的数据 */
function renderDataByType(type, data, state) {
    // type : 1-按部门展示2-展示全部职工； data：数据； state:1-左边的2-右边的
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var hasKids = false ;
            var leftOpen = data[i]["leftOpen"];
            var rightOpen = data[i]["rightOpen"];
            var userList = data[i]["userList"]; // 人员列表
            var kidList = data[i]["subList"]; // 部门列表
            var itemInfo = { "type":"depart", "id":data[i]["id"] }
            if( (userList && kidList.length > 0) || (userList && userList.length > 0) ){ // 有子级
                hasKids = true ;
                if(type === 1){ // 按部门展示
                    if(state === 1){ // -左边的
                        var cartStr = " fa-caret-right";
                        var showStr = "";
                        if(leftOpen){
                            cartStr = " fa-caret-down";
                            showStr = " style='display:block;'";
                        }
                        strAll += '<li><div class="departName" title="点击该部门后，展示为直属于该部门的子部门或职工">' +
                            '   <i class="fa'+ cartStr +'"></i>' +
                            '   <span>'+ data[i]["name"] +'</span> ' +
                            "   <span class='ctrl' data-type='depart' data-id='"+ data[i]["id"] +"' onclick='plus($(this), event)'>全选</span>" +
                            '</div><ul'+ showStr +'>';
                    }else{ // 右边的
                        var cartStr = " fa-caret-right";
                        var showStr = "";
                        if(rightOpen){
                            cartStr = " fa-caret-down";
                            showStr = " style='display:block;'";
                        }
                        strAll += '<li><div class="departName" title="点击该部门后，展示为直属于该部门的子部门或职工">' +
                            '   <i class="fa'+ cartStr +'"></i>' +
                            '   <span>'+ data[i]["name"] +'</span> ' +
                            "   <span data-id='"+ data[i]["id"] +"' class='ctrl'>已选职工："+ data[i]["count"] +"位</span>" +
                            '</div><ul'+ showStr +'>';
                    }
                }
            }else{ // 无子级
                if(type === 1) { // 按部门展示
                    if(state === 1) { // -左边的
                        var cartStr = " fa-caret-right";
                        if(leftOpen){
                            cartStr = " fa-caret-down";
                        }
                        strAll += '<li><div class="departName" title="点击该部门后，展示为直属于该部门的子部门或职工">' +
                            '   <i class="fa'+ cartStr +'"></i>' +
                            '   <span>'+ data[i]["name"] +'</span> ' +
                            '   <span class="hd">'+ JSON.stringify(data[i]) +'</span> ' +
                            "   <span class='ctrl' data-type='depart' data-id='"+ data[i]["id"] +"' onclick='plus($(this), event)'>全选</span>" +
                            '</div>';
                    }else{ // 右边的
                        var cartStr = " fa-caret-right";
                        if(rightOpen){
                            cartStr = " fa-caret-down";
                        }
                        strAll += '<li><div class="departName" title="点击该部门后，展示为直属于该部门的子部门或职工">' +
                            '   <i class="fa'+ cartStr +'"></i>' +
                            '   <span>'+ data[i]["name"] +'</span> ' +
                            '   <span class="hd">'+ JSON.stringify(data[i]) +'</span> ' +
                            "   <span class='ctrl' data-id='"+ data[i]["id"] +"'>已选职工："+ data[i]["count"] +"位</span>" +
                            '</div>';
                    }
                }
            }
            if(kidList && kidList.length > 0){ // 遍历子级部门
                strAll += renderDataByType(type, kidList, state) ;
            }
            if(userList && userList.length > 0){ // 遍历员工
                for(var j in userList){
                    var itemInfo2 = { "type":"user", "id":userList[j]["userID"] }; // 提示作用
                    let isO = (userList[j]['havRight']===0)
                    if(state === 1) { // -左边的
                        if(isO){ // 没有权限的
                            strAll += '<li data-type="user" title="'+ userList[j]['departsName'] +'"data-departid="'+ userList[j]["deparID"] +'" data-id="'+ userList[j]["userID"]  +'" onclick="plus($(this), event)">' +
                                '<div>' +
                                '   <i class="fa fa-black-tie"></i>' +
                                '   <span>'+ userList[j]["userName"] +'</span> ' +
                                '   <span class="hd">'+ JSON.stringify(userList[j]) +'</span> ' +
                                '</div></li>' ;
                        }
                    } else{ // 右边的
                        if(!isO){ // 有权限的
                            strAll += '<li title="'+ userList[j]['departsName'] +'">' +
                                '<div>' +
                                '   <i class="fa fa-black-tie"></i>' +
                                '   <span>'+ userList[j]["userName"] +'</span> ' +
                                '   <span class="hd">'+ JSON.stringify(userList[j]) +'</span> ' +
                                "   <span data-type='user' data-id='"+ userList[j]["userID"] +"' class='ctrl fa fa-times' onclick='minus($(this), event)'></span>"+
                                '</div></li>' ;
                        }
                    }
                }
            }
            if(hasKids){
                strAll += "</ul></li>" ;
            }else{
                strAll += "</li>" ;
            }
        }

    }
    return strAll ;
}
/*  create :hxz 2020-10-13 渲染二三象限的数据 */
function cancelOrGetStr(data, state) {
    // data：数据； state:1-第二行左边 2-第二行右边
    var strAll = "" ;
    if(data && data.length > 0){
        for(var i in data){
            var userList = data[i]["userList"]; // 人员列表
            var kidList = data[i]["subList"]; // 部门列表
            if(kidList && kidList.length > 0){
                strAll += cancelOrGetStr(kidList, state);
            }
            let method = "minus";
            if(state == 1){ method = "plus"; }
            if(userList && userList.length > 0){
                for(var j in userList){
                    var itemInfo2 = { "type":"user", "id":userList[j]["userID"] };
                    if(state == userList[j]["changeSta"]){
                        strAll += '<li title="'+ userList[j]['departsName'] +'">' +
                            '<div>' +
                            '   <i class="fa fa-black-tie"></i>' +
                            '   <span>'+ userList[j]["userName"] +'</span> ' +
                            "   <span data-type='user' data-id='"+ userList[j]["userID"] +"' class='ctrl fa fa-times' onclick='"+ method +"($(this), event)'></span>"+
                            '</div></li>' ;
                    }
                }
            }
        }
    }
    return strAll ;
}
// create:hxz 2020-10-13 减号
function minus(thisObj,event){
    event.stopPropagation();
    var type = thisObj.data('type');
    var id = thisObj.data('id');
    let info = { "type": type , "id":id, "havRight":0 } ;
    setVal(sysDeparAndUserList, info);
    let info2 = {'key':"havRight", 'val':1};
    var count = countSet(sysDeparAndUserList, info2);
    $(".selectedNum").html(count);
    chargeState();
}
// create:hxz 2020-10-13 加号
function plus(thisObj,event){
    event.stopPropagation();
    var type = thisObj.data('type');
    var departid = thisObj.data('departid');
    var id = thisObj.data('id');
    let info = { "type": type , "id":id, "havRight":1 } ;
    setVal(sysDeparAndUserList, info);
    let info2 = {'key':"havRight", 'val':1};
    var count = countSet(sysDeparAndUserList, info2);
    $(".selectedNum").html(count);
    chargeState();
}
// create:hxz 2020-10-13 设置值
function changeVal(thisObj, location){
    thisObj.next().toggle();
    var departId = thisObj.children(".ctrl").data("id");
    setOpenVal(location,departId, sysDeparAndUserList);
    var iObj = thisObj.children("i") ;
    var hasRi = iObj.hasClass("fa-caret-right");
    if(hasRi){
        iObj.attr("class","fa fa-caret-down")
    }else{
        iObj.attr("class","fa fa-caret-right")
    }
}
function setOpenVal(location ,departId,treeData){
    for(let i = 0 ; i < treeData.length; i++){
        var item_departID =  treeData[i]["id"];
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(Number(item_departID) === Number(departId)){
            var isOpen = treeData[i][location + 'Open']
            treeData[i][location + 'Open'] = !isOpen;
        }
        if(subDeparList && subDeparList.length>0){
            setOpenVal(location,departId,subDeparList)
        }
    }
}
function setVal(treeData, info, allselect){
    // info:选中的数据（部门/用户 ， id ，要设置的权值）
    // allselect 是否全选，如果是部门全选，则子部门和下面的人都全选
    var type = info['type'];
    var id = info['id'];
    for(let i = 0 ; i < treeData.length; i++){
        var departID =  treeData[i]["id"]
        var userList = treeData[i]["userList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(var u = 0; u < userList.length; u++){
                let userItem = userList[u];
                var uid = userItem["userID"];
                if((uid == id && type == "user") || allselect || (type =="depart" && departID == id)){
                    userItem['havRight'] = info['havRight'] ; // 用户有权限（本次设置的）1-有 ， 0-没有
                    if(userItem["roleCode"] == 1){ // 已有权限
                        if( userItem['havRight'] === 1){
                            userItem['changeSta'] = 0; // 原来有，现在有
                        }else{
                            userItem['changeSta'] = 1; // 原来有，现在没有 （第二行左边的）
                        }
                    }else{
                        if( userItem['havRight'] === 1){
                            userItem['changeSta'] = 2; // 原来没有，现在有（第二行右边的）
                        }else{
                            userItem['changeSta'] = 0; // 原来没有，现在没有
                        }
                    }
                    userList[u] = userItem ;
                }
            }
        }
        treeData[i]["userList"] = userList
        if(subDeparList && subDeparList.length>0){
            if(allselect || (departID == id && type == "depart")){
                setVal(treeData[i]["subList"], info, true);
            }else{
                setVal(treeData[i]["subList"], info);
            }
        }
    }
}

/* creator: 侯杏哲 2020-10-16 获取有权限的职工  */
function getRightUser(treeData) {
    let havRightUser = [] ;
    for(let i = 0 ; i < treeData.length; i++){
        let curUser = [];
        var userList = treeData[i]["userList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j]['havRight'] == 1 ;
                if(isok){
                    curUser.push(userList[j]);
                }
            }
        }
        let sonHav = []
        if(subDeparList && subDeparList.length>0){
            sonHav = getRightUser(subDeparList)
        }
        if(curUser.length > 0){
            havRightUser.push(...curUser);
        }
        if(sonHav.length > 0){
            havRightUser.push( ...sonHav);
        }

    }
    return havRightUser;
}
// creator: 侯杏哲 2020-10-16  计算有权限变化的人员数目
function diffCharge(treeData) {
    let allCount = 0 ;
    for(let i = 0 ; i < treeData.length; i++){
        var count = 0
        var userList = treeData[i]["userList"]; // 人员列表
        var subDeparList = treeData[i]["subList"]; // 部门列表
        if(userList && userList.length>0) {
            for(let j = 0; j < userList.length; j++){
                let isok = userList[j]['changeSta'] == 1 || userList[j]['changeSta'] == 2 ;
                if(isok){
                    count++;
                }
            }
        }
        if(subDeparList && subDeparList.length>0){
            count += diffCharge(subDeparList);
        }
        treeData[i]['count'] = count;
        allCount += count;
    }
    return allCount;
}

/* creator : 侯杏哲 2017-12-16 判断当前是否为总务 */
function chargeZW(testID , arr) {
    var isZW = false ;
    if(arr && arr.length > 0){
        for(var i = 0 ; i < arr.length ; i++){
            var id = arr[i]["userID"] ;
            if( id == testID){ isZW = true ;  }
        }
    }
    return isZW ;
}

/* creator：张旭博，2017-05-04 13:18:40，点击新增子级类别 确认 按钮 */
function sureNewClass() {
    let hasSettedNum = $(".bounce-newClass .hasSettedNum").html();
    if(Number(hasSettedNum) <= 0){
        bounce_Fixed.show( $("#tip2") ) ; $("#tipMess2").html("您需要先设置完这个文件夹的使用权限！") ;
        return false;
    }
    var categoryId = $(".ty-treeItemActive:visible").data("id");
    var categoryName = $.trim($(".bounce-newClass .categoryName").val());
    var maxChildCategorys = $(".ty-treeItemActive:visible").next().children("li").length;
    if(categoryName == ""){
        bounce_Fixed.show( $("#tip2") ) ; $("#tipMess2").html("类别名称不能为空！") ;
    }else{
        let isNew = $("#isNew").val();
        let data = {
            parent: categoryId,
            categoryName: categoryName
        }
        if(isNew == "same"){
            data = {
                categoryName: categoryName
            }
        }
        let users = []
        let havRightUser = $("#havRightUserCon").html() ;
        if(havRightUser != ""){
            havRightUser = JSON.parse(havRightUser);
            havRightUser.forEach(function(user){
                users.push(user.userID)
            })
            data.users = JSON.stringify(users)
        }
        $.ajax({
            url: "../res/insertSameFolder.do",
            data: data,
            success: function (res) {
                bounce.cancel();
                var status = res['data']["status"]; // 1新增陈功，2同级文件夹名称重复，3是没有设置文件夹权限
                if(status == 2){
                    layer.msg("类别名称重复，请更换类别名称！") ;
                }else if(status == 3){
                    layer.msg("您需要先设置完这个文件夹的使用权限！") ;
                }else if(status == 1){
                    if(isNew == "same"){
                        getFirstDoc('main') ;
                    }else{
                        bounce.cancel(); layer.msg("新增成功") ;
                        $(".ty-treeItemActive:visible").click();
                        $(".ty-treeItemActive:visible").click();
                        if($(".ty-treeItemActive:visible").children(".ty-fa").length > 0){
                            $(".ty-treeItemActive:visible").children(".ty-fa").attr("class","fa fa-angle-down");
                        }
                    }

                }else{
                    layer.msg("未知的返回值错误") ;
                }
            }
        })
    }
}

function chargeNull(str){
    return str === "" || str === null?"--":str;
}


// creator：张旭博，2017-05-06 15:13:09，绑定选择时间插件
laydate.render({elem: '#CV-fileMakeDate'});
laydate.render({elem: '#CV-fileReviewDate',});
laydate.render({elem: '#CV-fileApproveDate'});
laydate.render({elem: '#fileMakeDate'});
laydate.render({elem: '#fileReviewDate'});
laydate.render({elem: '#fileApproveDate'});
laydate.render({elem: '#uploadDateStart'});
laydate.render({elem: '#uploadDateEnd'});
laydate.render({elem: '#ChangeDateStart'});
laydate.render({elem: '#ChangeDateEnd'});