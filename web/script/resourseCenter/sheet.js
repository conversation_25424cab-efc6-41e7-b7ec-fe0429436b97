$(function () {
    $(".colTab").on("click", ".colTab_item", function() {
        $(this).addClass("active").siblings().removeClass("active")
        var index = $(this).index()
        getSheetList(1 , 20, Number(index)+1)
    })
    $(".ty-container").on("click",".ty-fileItem [type='btn']",function (){
        var name = $(this).attr("name")
        if ($(this).attr("disable") === 'true') {
            return false
        }
        switch (name) {
            case 'seeOnline': // 在线预览
                fileSeeOnline($(this))
                break
            case 'download': // 下载
                fileDownload($(this))
                break
            case 'changeSheetMsg': // 修改(就是只修改表格名称和编号）
                changeSheetMsg($(this))
                break
            case 'delete': // 删除
                deleteNoUseFile($(this))
                break
            case 'sheetHandleRecord': // 操作记录
                sheetHandleRecord($(this), 'file')
                break
            case 'relateRecord': // 关联记录
                relateRecord($(this))
                break;
            case 'relate': // 所关联的文件
                relate($(this))
                break;
            case 'related': // 曾关联的文件
                related($(this))
                break;
            case 'changeSheetVersion': // 换版
                changeSheetVersion($(this))
                break
        }
    });
    $("#btn-group").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            // 文件上传/文件发布申请/文件换版/文件换版申请 - 选择保存位置 - 按钮
            case 'newSheet':
                // 初始化表单
                // $("#fileUpload").find("input.ty-inputText").val("")
                // $("#needOther").prop("checked", true)
                // $("#fileUpload .savePlace").hide().html("")
                // // 展示为上传文件的内容（和换版共用弹窗）
                // $("#fileUpload .bounce_title").html($(this).html())
                // $("#fileUpload .inputPart").show().siblings(".seePart").hide()
                // $("#sureUploadNewFileBtn").data("name", "sureUploadNewFile")
                bounce.show($("#newSheet"))
                $('#newSheet .fileUpload').html("")
                $('#newSheet input').val("")
                $('#newSheet .fileShowList').html("")
                $('#newSheet .fileUpload').Huploadify({
                    auto: true,
                    fileTypeExts: '*',
                    formData:{
                        module: '文件与资料',
                        userId: sphdSocket.user.userID
                    },
                    fileSizeLimit: 1024 * 1024 * 2 - 500,  // 1.5G
                    showUploadedSize: true,
                    removeTimeout: 99999999,
                    buttonText: "选择",
                    queueID: 'fileQueue',
                    uploader:$.webRoot+"/uploads/uploadfyByFile.do",
                    onSelect: function () {
                        $(".fileUpload .uploadify-button").hide();
                    },
                    onUploadError: function () {
                        $(".fileUpload .uploadify_state").html("上传失败！")
                    },
                    onUploadSuccess: function (file, json) {
                        console.log(json)
                        var data = JSON.parse(json),
                            name = data.filename,
                            type = data.lastName,
                            size = file.size,
                            fileName = file.name,
                            fileInfo = {
                                path: name,
                                size: size,
                                title: fileName,
                                type: type,
                                module: '事务讨论',
                                place: 1
                            };
                        $("#newSheet").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
                        $("#newSheet .fileUpload").data("fileInfo", fileInfo);
                        var str = '<div class="file_item"><div class="hd">'+JSON.stringify(fileInfo)+'</div><div class="fileType ty-file_'+type+'"></div><div class="file_name" title="'+fileName+'">'+fileName+'</div><div class="delFile" fileUid="'+data.fileUid+'">+</div></div>'
                        $("#newSheet .fileShowList").html(str);
                        $("#newSheet [name='name']").val(data.displayName);
                        $("#newSheet [name='sn']").val(data.displayName);
                    },
                    onQueueComplete:function() {
                        $(".fileUpload .uploadify-button").show();
                        $(".fileUpload .uploadify-queue").html('')
                    }
                })
                bounce.everyTime('0.5s', 'newSheet',function() {
                    var state = 0
                    $("#newSheet [require]:visible").each(function () {
                        if ($.trim($(this).val()) === '') {
                            state++
                        }
                    })
                    if ($("#newSheet .fileShowList").html() === '') {
                        state++
                    }
                    if (state > 0) {
                        $("#sureNewSheetBtn").prop("disabled", true)
                    } else {
                        $("#sureNewSheetBtn").prop("disabled", false)
                    }
                })
                break
        }
    })
    $(".bounce").on("click", "[type='btn']", function () {
        var name = $(this).data("name")
        switch (name) {
            case 'sureNewSheet':
                // 确定新增表格
                var fileInfo = $("#newSheet .fileUpload").data("fileInfo");

                //参数json
                var addData = {
                    path: fileInfo.path,
                    size: fileInfo.size,
                    type: fileInfo.type,
                    module: '文件与资料',
                    resource: 0
                }
                $("#newSheet [require]:visible").each(function () {
                    addData[$(this).attr("name")] = $(this).val()
                })
                $.ajax({
                    url: "../res/addResRorm.do",
                    data: addData,
                    success: function(res) {
                        var data= res.data
                        var state= data.state
                        if (state === 1) {
                            layer.msg("操作成功")
                            bounce.cancel()
                            $(".colTab .colTab_item.active").click()
                        } else if (state === 2) {
                            bounce_Fixed.show($("#bounceFixed_errorTip"))
                            $("#bounceFixed_errorTip").find(".tipMsg").html("操作失败！<br>表格编号重复")
                        } else {
                            layer.msg("操作失败")
                        }
                    }
                })
                break
            case 'sureChangeSheetVersion':
                // 确定表格换版
                var fileInfo = $("#newSheet .fileUpload").data("fileInfo");

                //参数json
                var addData = {
                    path: fileInfo.path,
                    size: fileInfo.size,
                    type: fileInfo.type,
                    module: '文件与资料',
                    category: 0
                }
                $.ajax({
                    url: "../res/changeResRormVersion.do",
                    data: addData,
                    success: function(res) {
                        var data= res.data
                        if (res.success === 1) {
                            layer.msg("操作成功")
                            bounce.cancel()
                            $(".colTab .colTab_item.active").click()
                        } else {
                            layer.msg("操作失败")
                        }
                    }
                })
                break
            // case 'sureChangeSheetMsg':
            //     // 确定修改表格信息
            //     var addData = {
            //         id: $("#changeSheetVersion").data("id")
            //     }
            //     $("#newSheet [require]:visible").each(function () {
            //         addData[$(this).attr("name")] = $(this).val()
            //     })
            //     $.ajax({
            //         url: "../res/updateResRorm.do",
            //         data: addData,
            //         success: function(res) {
            //             var data= res.data
            //             if (data.state === 1) {
            //                 layer.msg("操作成功")
            //                 bounce.cancel()
            //                 $(".colTab .colTab_item.active").click()
            //             } else {
            //                 layer.msg("操作失败")
            //             }
            //         }
            //     })
            //     break;
            case 'seeHandleRecordDetail':
                bounce_Fixed.show($("#handleRecordDetail"))
                var type = $(this).attr("data-type")

                var id = $(this).parents("tr").data("id")
                $("#handleRecordDetail").data("id", id)
                getOneSheetDetail(type)
                break;
        }
    })
    $(".bounce_Fixed").on("click", "[type='btn']", function () {
        var name = $(this).data("name")

        switch (name) {
            // 文件上传/文件发布申请/文件换版/文件换版申请 - 选择保存位置 - 按钮
            case 'sureChooseFolder':
                var chooseNode = $(".ty-colFileTree[data-name='chooseSaveFolder']").find(".ty-treeItemActive");
                var categoryId = chooseNode.data("id");
                // 先判断文件夹是否设置了权限，没有就不能确定

                $.ajax({
                    url : 'getForderAuthDepsAndUsers.do' ,
                    data : {
                        categoryId: categoryId ,
                        type: 1
                    } ,
                    success: function (res) {
                        var success = res.success ;
                        if(success != 1){
                            layer.msg( "校验失败，不能获取权限" ) ; return false ;
                        }
                        var data = res.data ;
                        var catAuthTree = data.catAuthTree  ;
                        sysDeparAndUserList = catAuthTree ;
                        let isUpdate = initData(sysDeparAndUserList); // 大于零表示有权限
                        if(isUpdate > 0){
                            var folderNameObj = chooseNode.parents("li").children(".ty-treeItem").children("span")
                            var str = '<'
                            folderNameObj.each(function (index) {
                                var name = $(this).text()
                                if(index === 0){
                                    str = '<span class="ty-color-red">'+name+'</span>'
                                } else {
                                    str = name + ' / ' + str
                                }
                            })
                            str = '<i class="fa fa-folder"></i>' + str
                            if ($("#fileUpload").is(":visible")) {
                                $("#fileUpload .savePlace").show().html(str)
                                $("#fileUpload .savePlace").data("categoryId", categoryId)
                            } else if ($("#folderUpload").is(":visible")) {
                                $("#folderUpload .savePlace").show().html(str)
                                $("#folderUpload .savePlace").data("categoryId", categoryId)
                            }
                        }else{
                            layer.msg( "选择失败，因为这个文件夹的使用权限尚未设置！" ) ;
                        }
                    }
                });
                bounce_Fixed.cancel()
                break
            case 'sureMoveFile':
                var thisNode = $(".ty-colFileTree[data-name='main']").find(".ty-treeItemActive");
                var chooseNode = $(".ty-colFileTree[data-name='moveTo']").find(".ty-treeItemActive");
                var thisCategoryId = thisNode.data("id");
                var categoryId = chooseNode.data("id");
                // 先判断文件夹是否设置了权限，没有就不能确定
                if (!categoryId) {
                    layer.msg("请选择目标文件夹")
                    return false
                }

                if (thisCategoryId === categoryId) {
                    layer.msg("不能移动到当前文件夹")
                    return false
                }

                if (chooseNode.attr("data-child") === 'true') {
                    layer.msg("该类别下存在子类别，请选择该子类别或其他类别")
                    return false
                }

                if (chooseNode.hasClass("trash")) {
                    // 如果是文件移动到暂时不用的文件 - 不需要权限判断，有特殊提示

                    let fileId = $(".ty-fileItemActive:visible").data("id")
                    $.ajax({
                        url: '../res/clickButtenJudgementMove.do',
                        data: {
                            fileId: fileId,
                            newCategoryId: categoryId,
                            type: 1
                        },
                        success: function (res) {
                            var data = res.data
                            if (data === 2) {
                                layer.msg("无法移动到当前文件夹")
                            } else {
                                if (data === 1) {
                                    $("#bounceFixed2_tip .tipMsg").html("移动后，其他职工将无法再见到该文件。<br>确定移动吗？");
                                } else if (data === 3) {
                                    $("#bounceFixed2_tip .tipMsg").html('有职工正在借阅该文件或其历史版本。  <a class="ty-color-blue" onclick="seeBorrowPerson('+fileId+')">查看</a><br>移动后，借阅者的借阅将被终止。<br>确定移动吗？');
                                }
                                $("#bounceFixed2_tip .sureBtn").one("click", function () {
                                    sureMove()
                                })
                                bounce_Fixed2.show($("#bounceFixed2_tip")) ;
                            }
                        }
                    })
                } else {
                    // 如果是 移动到普通文件夹需要权限判断
                    $.ajax({
                        url : 'getForderAuthDepsAndUsers.do' ,
                        data : {
                            categoryId: categoryId ,
                            type: 1
                        } ,
                        success: function (res) {
                            var success = res.success ;
                            if(success != 1){
                                layer.msg( "校验失败，不能获取权限" ) ; return false ;
                            }
                            var data = res.data ;
                            var catAuthTree = data.catAuthTree  ;
                            sysDeparAndUserList = catAuthTree ;
                            let isUpdate = initData(sysDeparAndUserList); // 大于零表示有权限
                            if(isUpdate > 0){
                                $("#bounceFixed2_tip .tipMsg").html("您确定要把本文件移动到 <b class='ty-color-blue'>" + chooseNode.find("span").html() + "</b> 下吗？");
                                $("#bounceFixed2_tip .sureBtn").one("click", function () {
                                    sureMove()
                                })
                                bounce_Fixed2.show($("#bounceFixed2_tip")) ;
                            }else{
                                layer.msg( "选择失败，因为这个文件夹的使用权限尚未设置！" ) ;
                            }
                        }
                    });
                }
                break
        }
    })

    // 文件-更多按钮点击事件
    $(".ty-container").on("click","[name='more']",function (e) {
        $(".hel").hide()
        $(this).next(".hel").show();
        e.stopPropagation()
    });
    // 点击body隐藏更多
    $("body").on("click",function () {
        $(".hel").hide()
    });
    // × 点击事件
    $(".clearInput").on({
        "mousedown": function () {
            $(this).prev().val("");
        },
        "mouseup": function () {
            $(this).prev().focus();
        }
    });


    $("#search_applier").on("change", function () {
        getGeneralFile(1, 20);
    })

    /* creator：张旭博，2017-05-06 15:17:01，每一个文件绑定事件（添加样式、获取文件详细信息、） */
    $(".ty-container").on("click",".ty-fileItem",function (){
        $(".ty-fileItem").removeClass("ty-fileItemActive");
        $(this).addClass("ty-fileItemActive");
    });
    /* creator：张旭博，2017-05-06 15:17:01，为高级搜索 按文件类别搜索 的文件夹绑定事件（添加样式、获取自己文件夹信息、） */
    $(".ty-searchContent").on("click",".ty-folderItem",function (){
        $(this).addClass("ty-folderItemActive").siblings().removeClass("y-folderItemActive");
        var folderId        = $(this).attr("id"),
            folderParent    = $(this).find(".ty-folderParent").html(),
            folderName      = $(this).find(".ty-folderName").html(),
            nextFolderParent= '';
        folderParent === '' ? nextFolderParent = folderName :nextFolderParent = folderParent + '/' + folderName;
        //获取子级文件或者文件夹
        getChildFolder(folderId,nextFolderParent);
    });

    $("#fileNameOrSn").on("keydown",function (e) {
        var keyNum;
        keyNum = window.event ? e.keyCode : e.which;
        if(keyNum === 13){
            searchBtn();
        }
    })
    $(".colTab .colTab_item.active").click()

    $("body").on('click', '.delFile', function () {
        $(this).parents('.file_item').remove()
        var uid = $(this).attr("fileUid")
        if (uid) {
            var option = {type: 'fileUid', fileUid: uid}
            fileDelAjax(option)
        }
    })


});

// creator: 张旭博，2022/2/28 15:44，获取list
function getSheetList(currentPageNo , pageSize, type, name) {
    if (!name) {name = ''}
    var pm_list = new Promise((resolve, reject) => {
        var data = {
            type: type, // 1是获取待关联 2是已关联 3是解除关联
            pageSize: pageSize, //条数
            currentPageNo: currentPageNo //页码
        }
        if (name !== '') {
            data.name = name
        }
        $.ajax({
            url: '../res/getResRormByType.do ',
            data: data,
            success: function (res) {
                var data = res.data
                resolve(data)
            }
        })
    })
    pm_list.then(data => {
        var pageInfo = data.pageInfo

        //设置分页信息
        var currentPageNo   = pageInfo.currentPageNo,
            totalPage       = pageInfo.totalPage,
            jsonStr = JSON.stringify( { type: type, name: name} ) ;
        setPage( $("#ye_sheetList"), currentPageNo, totalPage, "sheetList" ,jsonStr) ;

        if ($(".searchTopBar").is(":visible")) {
            $(".searchTopBar .searchSheetNum").html(pageInfo.totalResult)
        }

        var list = data.list
        var listStr = getFileListStr(list);
        $(".ty-fileList").html(listStr);
        if(list.length >0){
            $('.ty-fileNull').hide()
        }else{
            $('.ty-fileNull').show()
        }
    })
}

// creator: 张旭博，2022/3/2 9:49，根据文件数据返回文件列表字符串
function getFileListStr(fileInfo) {
    var fileListStr = '';
    if(fileInfo.length === 0){
        $("#ye_con").hide() ;
        $("#fileSort").hide() ;
        fileListStr = '<div class="ty-fileNull"><img src="../../css/resourceCenter/images/nodata.svg" alt=""><p>暂无文件</p></div>';
    }else{
        var trashID = $(".trash").data("id")
        for(var i = 0; i < fileInfo.length; i++){
            var id          = fileInfo[i].id,
                file        = fileInfo[i].file,
                category    = fileInfo[i].category,
                name        = fileInfo[i].name,
                size        = fileInfo[i].size,
                path        = fileInfo[i].path,
                fileSn      = fileInfo[i].sn,
                changeNum   = fileInfo[i].version,
                fileType    = fileInfo[i].type,
                operation   = fileInfo[i].operation,
                updateName  = fileInfo[i].updateName,
                createName  = fileInfo[i].createName,
                updateDate  = fileInfo[i].updateDate,
                createDate  = fileInfo[i].createDate,
                updator     = fileInfo[i].updator,
                creator     = fileInfo[i].creator,
                version     = fileInfo[i].type.toLowerCase(),
                state       = Number(fileInfo[i].state), // 0是待关联 1是已关联
                enabled     = fileInfo[i].enabled, // 是否解除关联 true是没有解除存在关联 false是已经解除关联
                loginUser   = $("#loginUser").html(),
                isTrashFolder = trashID === fileInfo[i].category,
                loginUserId = 0,
                applyDisabledClass = '',    //定义换版申请禁用class
                recordDisabledClass = '',   //定义换版记录禁用class
                handleStr = ''             //定义操作按钮部分字符串



            //获取当前userId
            loginUser   = JSON.parse(loginUser);
            loginUserId = loginUser.userID;

            // 通用格式可以使用在线预览
            // handleStr = customVersion.indexOf(version) !== -1?'<a class="ty-left" path="'+ path +'" onclick="fileSeeOnline($(this))">在线预览</a>': '<a class="ty-left ty-disabled">在线预览</a>'

            //格式化数据
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size =  parseFloat(size/1048576).toFixed(2) + 'MB';

            updateName  === null || updateName  == undefined? updateName = createName:updateName;   //更新人为空时 赋值 创建人，最后统一取更新时间 ， 下同
            updateDate  === null || updateDate  == undefined? updateDate = createDate:updateDate;
            updateDate = moment(updateDate).format("YYYY-MM-DD HH:mm:ss")
            if (!updator) {updator = creator}

            operation   === '3' ? applyDisabledClass  = 'ty-disabled': applyDisabledClass  = ''; //换版申请后的禁用（换版申请后  换版和移动按钮被禁用）
            changeNum   === 0   ? recordDisabledClass = 'ty-disabled': recordDisabledClass = ''; //换版记录的禁用 （换版为0 换版记录按钮禁用）

            let funcName = {
                seeOnline: '在线预览',
                download: '下载',
                changeSheetMsg: '修改',
                basicMsg: '基本信息',
                sheetHandleRecord: '操作记录',
                relateRecord: '关联记录',
                delete: '删除',
                more: '更多',
                changeSheetVersion: '换版',
                relate: '所关联的文件',
                related: '曾关联的文件',
                disable: '禁用'
            }

            let func = {}
            let moreFunc = {}
            if (state === 0) {
                // 总务 - 其他文件 - 功能
                func = {
                    seeOnline: { path: path, disable: customVersion.indexOf(version) === -1 },
                    download: { path: path, download: name + '.' + fileType },
                    changeSheetVersion: {},
                    changeSheetMsg: {},
                    delete: { color: 'red' }
                }
            } else {
                if (enabled) {
                    func = {
                        seeOnline: { path: path, disable: customVersion.indexOf(version) === -1 },
                        download: { path: path, download: name + '.' + fileType },
                        changeSheetVersion: {},
                        more: {}
                    }
                    moreFunc = {
                        changeSheetMsg: {},
                        sheetHandleRecord: {},
                        relateRecord: {},
                        relate: {},
                        related: {}
                    }
                } else {
                    func = {
                        seeOnline: { path: path, disable: customVersion.indexOf(version) === -1 },
                        download: { path: path, download: name + '.' + fileType },
                        more: {}
                    }
                    moreFunc = {
                        sheetHandleRecord: {},
                        relateRecord: {},
                        related: {}
                    }
                }
            }
            for (let key in func) {
                handleStr += '<a class="aBtn" type="btn" name="'+key+'"'
                let attr = func[key]
                for (var item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</a>'
            }
            handleStr += '<ul class="hel" style="display: none">'
            for (let key in moreFunc) {
                handleStr += '<li class="liBtn" type="btn" name="'+key+'"'
                let attr = moreFunc[key]
                for (let item in attr) {
                    handleStr += ' ' +item + '="' + attr[item] + '"'
                }
                handleStr += '>' + funcName[key] + '</li>'
            }
            handleStr += '</ul>'
            console.log(handleStr)

            fileListStr +=  '<div class="ty-fileItem" data-id="'+id+'" pid="'+category+'" data-id="'+id+'">'+
                '<div class="ty-fileType ty-file_'+fileType+'"></div>'+
                '<div class="ty-fileInfo">'+
                '   <div class="ty-fileRow">'+
                '       <div class="ty-fileName" title="'+name+ '-G'+changeNum+'">'+ name +'</div>'+
                '       <div class="ty-fileNo"  title="' + fileSn + '">编号 ：'+ fileSn +'</div>'+
                '       <div class="ty-fileVersion" changeNum="'+ changeNum +'">'+'G'+changeNum+'</div>'+
                '   </div>'+
                '   <div class="ty-fileRow">'+
                '       <div class="ty-fileDetail">'+ updateName + ' &nbsp;&nbsp; ' + size + ' &nbsp;&nbsp; ' + updateDate + '</div>'+
                '       <div class="ty-fileHandle">'+ handleStr + '</div>'+
                '   </div>'+
                '</div>'+
                '</div>';
        }
    }

    //返回文件列表字符串
    return fileListStr;
}

// creator: 张旭博，2022/3/2 15:50，表格功能 - 删除弹窗
function deleteNoUseFile(obj) {
    bounce.show($("#bounce_tip"))
    $("#bounce_tip").find(".tipMsg").html("确定删除该表格吗？")
    $("#bounce_tip").find(".sureBtn").unbind().on("click", function () {
        var fileId      = obj.parents(".ty-fileItem").data("id")
        $.ajax({
            url: '../res/deleteResRorm.do',
            data: {
                id: fileId
            },
            success: function (res) {
                bounce.cancel()
                var state = res.data.state
                if (state === 1) {
                    layer.msg("操作成功")
                    $(".colTab .colTab_item.active").click()
                } else if (state === 2) {
                    bounce.show($("#bounce_errorTip"))
                    $("#bounce_errorTip").find(".tipMsg").html("删除失败！<br>表格已经存在关联")
                } else {
                    bounce.show($("#bounce_errorTip"))
                    $("#bounce_errorTip").find(".tipMsg").html("删除失败")
                }
            }
        })
    })
}

// creator: 张旭博，2022/3/2 15:50，表格功能 - 修改表格信息
function changeSheetMsg(selector) {
    bounce.show($("#changeSheetMsg"))
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#changeSheetMsg [name='name']").val(name)
    $("#changeSheetMsg [name='sn']").val(sn)
    $("#changeSheetMsg").data("id", id)
    $("#changeSheetMsg .sureBtn").unbind().on("click", function() {
        var name1 = $("#changeSheetMsg [name='name']").val()
        var sn1 = $("#changeSheetMsg [name='sn']").val()
        if (name1 === name && sn1 === sn) {
            layer.msg("请修改表格信息后再提交")
            return false
        }
        //参数json
        var addData = {
            id: id
        }
        if (name1 !== name) {
            addData.name = name1
        }
        if (sn1 !== sn) {
            addData.sn = sn1
        }
        $.ajax({
            url: "../res/updateResRorm.do",
            data: addData,
            success: function(res) {
                var data= res.data
                var state = data.state
                if (state === 1) {
                    layer.msg("修改成功")
                    bounce.cancel()
                    $(".colTab .colTab_item.active").click()
                } else if (state === 2) {
                    bounce_Fixed.show($("#bounceFixed_errorTip"))
                    $("#bounceFixed_errorTip").find(".tipMsg").html("修改失败！<br>表格编号重复")
                } else {
                    layer.msg("修改失败")
                }
            }
        })
    })
    bounce.everyTime('0.5s', 'changeSheetMsg',function() {
        var state = 0
        $("#changeSheetMsg [require]:visible").each(function () {
            if ($.trim($(this).val()) === '') {
                state++
            }
        })
        if (state > 0) {
            $("#sureChangeSheetMsgBtn").prop("disabled", true)
        } else {
            $("#sureChangeSheetMsgBtn").prop("disabled", false)
        }
    })
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 换版
function changeSheetVersion(selector) {
    bounce.show($("#changeSheetVersion"))

    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#changeSheetVersion .seeName").html(name)
    $("#changeSheetVersion .seeSn").html(sn)
    $("#changeSheetVersion").data("id", id)
    $('#changeSheetVersion .fileUpload').html("")
    $('#changeSheetVersion .fileShowList').html("")
    $('#changeSheetVersion .fileUpload').Huploadify({
        auto: true,
        fileTypeExts: '*',
        formData:{
            module: '文件与资料',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: 1024 * 1024 * 2 - 500,  // 1.5G
        showUploadedSize: true,
        removeTimeout: 99999999,
        buttonText: "选择",
        queueID: 'fileQueue',
        uploader:$.webRoot+"/uploads/uploadfyByFile.do",
        onSelect: function () {
            $(".fileUpload .uploadify-button").hide();
        },
        onUploadError: function () {
            $(".fileUpload .uploadify_state").html("上传失败！")
        },
        onUploadSuccess: function (file, json) {
            console.log(json)
            var data = JSON.parse(json),
                name = data.filename,
                type = data.lastName,
                size = file.size,
                fileName = file.name,
                fileInfo = {
                    path: name,
                    size: size,
                    title: fileName,
                    type: type,
                    module: '事务讨论',
                    place: 1
                };
            $("#changeSheetVersion").data("groupUuidObj", {type: 'groupUuid', groupUuid: data.groupUuid})
            $("#changeSheetVersion .fileUpload").data("fileInfo", fileInfo);
            var str = '<div class="file_item"><div class="hd">'+JSON.stringify(fileInfo)+'</div><div class="fileType ty-file_'+type+'"></div><div class="file_name" title="'+fileName+'">'+fileName+'</div><div class="delFile" fileUid="'+data.fileUid+'">+</div></div>'
            $("#changeSheetVersion .fileShowList").html(str);
        },
        onQueueComplete:function() {
            $(".fileUpload .uploadify-button").show();
            $(".fileUpload .uploadify-queue").html('')
        }
    })

    $("#changeSheetVersion .sureBtn").unbind().on("click", function() {
        var fileInfo = $("#changeSheetVersion .fileUpload").data("fileInfo");

        //参数json
        var addData = {
            id: id,
            path: fileInfo.path,
            size: fileInfo.size,
            type: fileInfo.type,
            module: '文件与资料'
        }
        $.ajax({
            url: "../res/changeResRormVersion.do",
            data: addData,
            success: function(res) {
                var data= res.data
                var resAtt= data.resAtt
                if (resAtt) {
                    layer.msg("操作成功")
                    bounce.cancel()
                    $(".colTab .colTab_item.active").click()
                } else {
                    layer.msg("操作失败")
                }
            }
        })
    })
    bounce.everyTime('0.5s', 'changeSheetVersion',function() {
        var state = 0
        if ($("#changeSheetVersion .fileShowList").html() === '') {
            state++
        }
        if (state > 0) {
            $("#sureChangeSheetVersionBtn").prop("disabled", true)
        } else {
            $("#sureChangeSheetVersionBtn").prop("disabled", false)
        }
    })
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 操作记录
function sheetHandleRecord(selector, type) {
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    bounce.show($("#sheetHandleRecord"))
    $("#sheetHandleRecord").data("id", id)
    getsheetHandleRecord(1, 20)
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 所关联的文件
function relate(selector) {
    bounce.show($("#relate"))
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#relate .fileSn").html(sn)
    $("#relate .fileName").html(name)
    $("#relate").data("id", id)
    getFileBySheet(1, 20, 1)
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 曾关联的文件
function related(selector) {
    bounce.show($("#related"))
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#related .fileSn").html(sn)
    $("#related .fileName").html(name)
    $("#relate").data("id", id)
    getFileBySheet(1, 20, 2)
}

// creator: 张旭博，2022/3/25 20:04，获取表格的操作记录
function getsheetHandleRecord(currentPageNo, pageSize) {
    var id = $("#sheetHandleRecord").data("id")
    var data = {
        id: id,
        currentPageNo: currentPageNo,
        pageSize: pageSize
    }
    $.ajax({
        url: '../res/getResRormHis.do',
        data: data,
        success: function (res) {
            var data = res.data
            var list = data.list
            var pageInfo = data.pageInfo

            //设置分页信息
            var currentPageNo   = pageInfo.currentPageNo,
                totalPage       = pageInfo.totalPage;
            setPage( $("#ye_sheetHandleRecord"), currentPageNo, totalPage, "sheetHandleRecord") ;

            var str = ''
            for (var i = 0; i<list.length; i++) {
                if (i === 0) {
                    str +=  '<tr data-id="'+list[i].id+'">' +
                        '<td>创建</td>'+
                        '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                        '<td><a class="ty-color-blue" type="btn" data-type="create" data-name="seeHandleRecordDetail">查看</a></td>'+
                        '</tr>'
                } else {
                    str +=  '<tr data-id="'+list[i].id+'">' +
                        '<td>'+(list[i].operation === '1'?'换版':'修改')+'</td>'+
                        '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                        '<td><a class="ty-color-blue" type="btn" data-type="'+(list[i].operation === '1'?'changeVersion':'changeInfo')+'" data-name="seeHandleRecordDetail">查看</a></td>'+
                        '</tr>'
                }
            }
            $("#sheetHandleRecord tbody").html(str)
        }
    })
}

// creator: 张旭博，2022/3/25 20:04，获取某一条表格的操作记录详情
function getOneSheetDetail(type) {
    var id = $("#handleRecordDetail").data("id")
    $.ajax({
        url: '../res/getResRormHisMes.do',
        data: {
            id: id
        },
        success: function (res) {
            var data = res.data
            var resAttHis = data.resAttHis
            var size  = resAttHis.size
            size < 102400 ? size = parseFloat(size/1024).toFixed(2) + 'KB':size = parseFloat(size/1048576).toFixed(2) + 'MB';

            $("#handleRecordDetail .info_title").html(resAttHis.name);

            var str = ''
            if (type === 'changeVersion') {
                str =   '<a class="aBtn" type="btn" name="seeOnline" path="'+resAttHis.path+'" onclick="fileSeeOnline($(this))">在线预览</a>' +
                        '<a class="aBtn" type="btn" name="download" path="'+resAttHis.path+'" download="'+resAttHis.name +'.' + resAttHis.type+'" onclick="fileDownload($(this))">下载</a>'

            }
            $("#handleRecordDetail .info_handle").html(str);

            // left
            $("#handleRecordDetail .info_sn").html(resAttHis.sn);
            $("#handleRecordDetail .info_size").html(size);

            // right
            $("#handleRecordDetail .info_gn").html("G" + resAttHis.version);
            $("#handleRecordDetail .info_createName").html(resAttHis.createName);
            $("#handleRecordDetail .info_createDate").html(moment(resAttHis.createDate).format("YYYY-MM-DD HH:mm:ss"));
            $("#handleRecordDetail .info_version").html( ( resAttHis.type || "未知") );
        }
    })
}

// creator: 张旭博，2022/3/2 17:18，表格功能 - 关联记录
function relateRecord(selector) {
    bounce.show($("#relateRecord"))
    var item = selector.parents(".ty-fileItem")
    var id = item.data("id")
    var sn = item.find(".ty-fileNo").attr("title")
    var name = item.find(".ty-fileName").html()
    $("#relateRecord .fileSn").html(sn)
    $("#relateRecord .fileName").html(name)
    $("#relateRecord").data("id", id)
    getFileHandleRecordBySheet(1, 20)
}

// creator: 张旭博，2022/3/25 20:20，通过表格获取关联文件的操作记录和通过文件获取关联表格的操作记录
function getFileHandleRecordBySheet(currentPageNo, pageSize) {
    var id = $("#relateRecord").data("id")
    var data = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        attId: id
    }
    $.ajax({
        url: '../res/getResFormCorrelationRecord.do',
        data: data,
        success: function (res) {
            var data = res.data
            //设置分页信息
            // var pageInfo = data.pageInfo
            // var currentPageNo   = pageInfo.currentPageNo,
            //     totalPage       = pageInfo.totalPage;
            // setPage( $("#ye_relateRecord"), currentPageNo, totalPage, "relateRecord") ;
            //设置列表
            var list  = data.list
            var str = ''
            for (var i in list ) {
                str +=  '<tr>' +
                        '<td>'+(list[i].operation === '4'?'关联':'解除关联')+'</td>'+
                        '<td>'+list[i].resEntity.fileSn+'</td>'+
                        '<td>'+list[i].resEntity.name+'</td>'+
                        '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                        '</tr>'
            }
            $("#relateRecord tbody").html(str)
        }
    })
}



// creator: 张旭博，2022/3/25 21:09，通过表格获取关联文件和通过文件获取关联表格
function getFileBySheet(currentPageNo, pageSize, type) {
    var id = $("#relate").data("id")
    var data = {
        currentPageNo: currentPageNo,
        pageSize: pageSize,
        type: type, // 1是获取表格正在关联的文件 2是获取表格曾经关联的文件
        attId: id
    }
    $.ajax({
        url: '../res/getResFormAssociatedFile.do',
        data: data,
        success: function (res) {
            var data = res.data
            // var pageInfo = data.pageInfo

            //设置分页信息
            // var currentPageNo   = pageInfo.currentPageNo,
            //     totalPage       = pageInfo.totalPage,
            //     jsonStr = JSON.stringify( { type: type} ) ;
            // var dom = type === 1?$("#ye_relate"):$("#ye_related")
            // setPage( dom, currentPageNo, totalPage, "relate" ,jsonStr) ;
            var list  = data.list
            var str = ''
            for (var i in list ) {
                if (type === 1) {
                    str +=  '<tr>' +
                            '<td>'+list[i].resEntity.fileSn+'</td>'+
                            '<td>'+list[i].resEntity.name+'</td>'+
                            '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                            '</tr>'
                } else {
                    str +=  '<tr>' +
                            '<td>'+list[i].resEntity.fileSn+'</td>'+
                            '<td>'+list[i].resEntity.name+'</td>'+
                            '<td>'+list[i].createName + ' ' + moment(list[i].createDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                            '<td>'+list[i].updateName + ' ' + moment(list[i].updateDate).format("YYYY-MM-DD HH:mm:ss") +'</td>'+
                            '</tr>'
                }
            }
            if (type === 1) {
                $("#relate tbody").html(str)
            } else {
                $("#related tbody").html(str)
            }

        }
    })
}

//------------------------- 文件上的所有按钮功能 ---------------------------//

// creator: 张旭博，2018-05-24 11:04:53，表格功能 - 预览
function fileSeeOnline(selector) {
    let isChargeRecord = $("#resHistory").is(":visible")
    if(!isChargeRecord){
        // 获取文件信息(后台要求的，不知道为啥)
        // getFileInfo(selector);
        //记录点击次数
        var currentFileId = selector.parents(".ty-fileItem").data("id");
        $.ajax({
            url:"../res/viewAndDownloadRecord.do",
            data:{
                "id"  : currentFileId,
                "type": 1
            },
            success: function(data){
                if(data["success"] !== 1){
                    $("#mt_tip_ms").html(data["data"]);
                    bounce.show($("#mtTip"));
                }
            }
        });
    }

    seeOnline(selector)
}

// creator: 张旭博，2018-05-24 11:05:09，表格功能 - 下载
function fileDownload(selector) {
    //记录点击次数
    // if (!$("#resHistory").is(":visible")) {
    //     var currentFileId = selector.parents(".ty-fileItem").data("id");
    //     $.ajax({
    //         url:"../res/viewAndDownloadRecord.do",
    //         data:{
    //             "id"  : currentFileId,
    //             "type": 2
    //         },
    //         success: function(data){
    //             if(data["success"] !== 1){
    //                 $("#mt_tip_ms").html(data["data"]);
    //                 bounce.show($("#mtTip"));
    //             }
    //         }
    //     });
    // }
    getDownLoad(selector)
}

//------------------------- 文件查询 ---------------------------//

// creator: 张旭博，2018-04-20 19:47:19，普通搜索 - 搜索
function searchBtn() {
    $(".searchTopBar").show()
    var index = $("#listDoTc .colTab_item").index($("#listDoTc .colTab_item.active"))
    var name = $("#fileNameOrSn").val()
    getSheetList(1 , 20, Number(index)+1, name)
}

function backToMain() {
    var index = $("#listDoTc .colTab_item").index($("#listDoTc .colTab_item.active"))
    getSheetList(1 , 20, Number(index)+1)
    $(".searchTopBar").hide()
    $("#fileNameOrSn").val("")
}