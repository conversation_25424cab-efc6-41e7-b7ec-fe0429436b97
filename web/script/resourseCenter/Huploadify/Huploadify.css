@charset "utf-8";
/* CSS Document */
.uploadify-button {
	display:inline-block;
	margin:12px;
	border:1px solid #808080;
	background-color:#707070;
	line-height:24px;
	border-radius:12px;
	padding:0 18px;
	font-size:12px;
	font-weight:600;
	font-family:'微软雅黑';
	color:#FFF;
	cursor:pointer;
	text-decoration:none;
}
.uploadify-button:hover {
	background-color:#888;
}
.uploadfile {
	width:0;
}
.uploadify-queue .uploadify-queue-item {
	list-style-type:none;
	display:flex;
	flex-direction:column;
}
.uploadbtn,.delfilebtn {
	display:inline-block;
	border:1px solid #999;
	line-height:24px;
	border-radius:4px;
	padding:0 18px;
	font-size:12px;
	color:#666;
	text-decoration:none;
}
.uploadbtn {
	display:none;
	/*默认不显示上传按钮，非自动上传时用js控制显示*/
}
.up_filename,.progressnum,.delfilebtn,.uploadbtn,.up_percent {
	 font-size:12px;
	 color:#666;
 }
.uploadify-progress {
	width:340px;
	height:3px;
	background-color:white;
	margin:3px 0;
}
.uploadify-progress-bar {
	width:0%;
	height:100%;
	border-radius:3px;
	background-color:#0099FF;
}
.uploadify_top {
	display:flex;
	flex-direction:row;
	justify-content:space-between;
}
.uploadify_bottom {
	display:flex;
	flex-direction:row;
	justify-content:space-between;
}
.uploadify_state{
	color: #0099FF;
	font-size: 12px;
}
