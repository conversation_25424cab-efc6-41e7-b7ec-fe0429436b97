/**
 * Created by Administrator on 2017/7/4.
 */
$(function(){
    // 获取销售和财务人员
    getAllFinancers($("#newInvoice .buyerName"));
    getAllCustomer($("#invoiceEntry .receiveCorpItems"));
    //获取发票段列表--35
    getInvoiceList();
    // 开始发票号和结束发票号的绑定事件
    $("#newInvoice .beginNo").on("keyup",function(){
        var beginNo = parseInt($(this).val());
        var endNo = $("#newInvoice .endNo").val();
        if($(this).val().length == 8 && $.trim(beginNo)<99999979){
            $("#newInvoice .endNo").val(PrefixInteger((beginNo + 19),8));
        }else{
            $("#newInvoice .endNo").val("");
        }
    });
    $("#newInvoice .endNo").on("keyup",function(){
        var beginNo = $("#newInvoice .beginNo").val();
        var endNo = $("#newInvoice .endNo").val();
        if(endNo.length>5 && beginNo.length>5){
            var commonNo = beginNo.substring(0,5) ;
            var endLastNo = endNo.substring(5,endNo.length);
            $("#newInvoice .endNo").val(commonNo.toString() + endLastNo.toString());
        }
    });



    //获取客户名称输入框焦点
    $("#receiveCorp").on('focus',function(){
        $(".receiveCorpItems").show();
    })
    $("#receiveCorp").blur(function(){
        setTimeout(function(){
            $(".receiveCorpItems").hide();
        },200)
    })
    $("#receiveCorp").on({
        keyup:function(){
            let value1 = $(this).val();
            let b = false;//赋值
            $(".receiveCorpItems option").each(function(){//遍历开始
                $(this).hide();
                if($(this).text().indexOf($.trim(value1))>=0){
                    $(this).show();
                    //一开始 b是 false
                    if($(this).text()=== value1){//true
                        let obj ={};
                        let invoice=$(this).attr("invoice");
                        let code=$(this).attr("code");
                        let costId=$(this).val();//筛选框中id
                        obj.invoice=invoice;
                        obj.code=code;
                        obj.costId=costId;
                        obj.username=value1;
                        $("#receiveCorp").data("revice",obj);//存储json数据
                        b=true;
                    }
                }
            })
            //判断
            if(b==false){//if中==是条件语句
                $("#receiveCorp").data("revice", {});
            }
        },
    })
    $(".receiveCorpItems").on('click','option',function(){
        $("#receiveCorp").val($(this).text());
        //已知鼠标点击后显示在输入框中的值，而输入框中的值和筛选框中值完全匹配时按钮高亮，
        //将用户点击的筛选框中的数据进行存储，以方便进行id的比较
        let obj ={};
        let invoice=$(this).attr("invoice");
        let code=$(this).attr("code");
        let costId=$(this).val();//筛选框中id
        obj.invoice=invoice;
        obj.code=code;
        obj.costId=costId;
        obj.username=$(this).text();
        $("#receiveCorp").data("revice",obj);//存储json数据


    })












});

// creator：sy 2022-02-11 显示供选择的客户名称列表
function showReceiveCorpItems(){
    $("#receiveCorp").on({
        focus:function(){
            $(".receiveCorpItems").show();
        },
        blur:function(){
            setTimeout(function(){
                $(".receiveCorpItems").hide();
            },100)
        },
        keyup:function(){
            let value1 = $(this).val();
            let value2 = $(".receiveCorpItems option").val();
            $(".receiveCorpItems option").each(function (){
                $(this).hide()
                if($(this).text().indexOf($.trim(value1))>=0){
                    $(this).show();
                    if(value1 == value2){

                    }
                }
            })
        }
    })
    $(".receiveCorpItems option").on({
        click:function(){
            $("#receiveCorp").val($(this).text());
        }
    })
}










// creator：hxz 2019-02-19 初始化bounce_Fixed2 三级弹窗
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
bounce_Fixed2.show($("#invoiceLogInfo"));
bounce_Fixed2.cancel();
/* creator：hxz，2021-0830  刷新 列表 */
function refreashList() {
    getInvoiceList()
}
/* creator：张旭博，2017-07-05 17:07:24，获取发票段 列表 */
function getInvoiceList() {
    $.ajax({
        url: "getAllInvoices.do",
        success:function( res ){
            let data = res.data
            var invoiceSetted = data["invoiceSettingNum"]; // 0 - 财务没有进行发票设置
            if(invoiceSetted == 0 ){
                $("#newInvoiceBtn").attr("class" , "ty-btn ty-btn-gray ty-btn-big ty-circle-5 ty-left").removeAttr("onclick")
            }
            var financeInvoices = data["financeInvoices"];
            var invoiceListStr = "";
            if(financeInvoices == undefined){
                $("#errorTip .tipWord").html("系统错误，请重试！");
                bounce.show($("#errorTip"));
            }else{
                for(var i=0; i<financeInvoices.length; i++){
                    //buyDate(购买日期) buyerName(购买者) beginNo(起号码) endNo(止号码) createName(录入者
                    invoiceListStr  +=  '<tr id="'+financeInvoices[i].id+'">'+
                                            '<td><span class="ty-color-blue" onclick="invoiceListBtn($(this))">'+financeInvoices[i].beginNo+' ~ '+financeInvoices[i].endNo+'</span></td>'+       //起止号码
                                           '<td>'+ financeInvoices[i]["invoiceCode"] +'</td>' +
                                           '<td>'+ category(financeInvoices[i]["type"]) +'</td>' +
                                            '<td>'+ (financeInvoices[i].detailNum || 0) +'</td>'+
                                            '<td>'+financeInvoices[i].buyerName+'</td>'+   //购买者
                                            '<td>'+ (new Date(financeInvoices[i].buyDate).format('yyyy-MM-dd')) +'</td>'+       //购买日期
                                            '<td>'+ financeInvoices[i].createName + ''+ (new Date(financeInvoices[i].createDate).format('yyyy-MM-dd hh:mm:ss')) +'</td>'+       //录入日期
                                        '</tr>';
                }
                $("#invoicePurchaseList").html(invoiceListStr);
            }

        }
    })
}

/* updator: hxz 2019-02-16 获取每一段的发票详情列表 */
function getInvoiceDetailList(){
    var invoiceId = $(".itemActive").attr("id");
    $("#financeInvoiceDetails").html("");
    $.ajax({
        url: "getAllInvoiceDetails.do",
        data : { "invoiceId" : invoiceId },
        success:function( data ){
            var financeInvoiceDetails = data["financeInvoiceDetails"];
            var invoiceListDetailStr = "";
            if(financeInvoiceDetails == undefined){
                $("#errorTip .tipWord").html("系统错误，请重试！");
                bounce.show($("#errorTip"));
            }else{
                for(var i=0; i<financeInvoiceDetails.length; i++){
                    if(financeInvoiceDetails[i].reason.length<=8){
                        var reason =    '<td>'+financeInvoiceDetails[i].reason+'</td>';
                    }else{
                        var reason =    '<td class="reasonHover">'+changeReason(financeInvoiceDetails[i].reason)+//作废理由
                            '<div style="position:relative"><div class="reasonHoverCon">'+financeInvoiceDetails[i].reason+'</div></div>'+
                            '</td>';
                    }
                    var state = financeInvoiceDetails[i]["state"];
                    var amount = financeInvoiceDetails[i]["amount"];
                    invoiceListDetailStr += '<tr id="' + financeInvoiceDetails[i]["id"] + '" state="' + financeInvoiceDetails[i]["state"] + '">' +
                        '<td>' + financeInvoiceDetails[i]["invoiceNo"] + '</td>' +       //发票号
                        '<td>' + new Date(financeInvoiceDetails[i]["operateDate"]).format('yyyy-MM-dd') + '</td>' +       //开票时间
                        '<td>' + (amount ? amount : "") + '</td>' +       //开票金额
                        '<td>' + financeInvoiceDetails[i]["receiveCorp"] + '</td>' +       //客户名称
                        '<td>' + new Date(financeInvoiceDetails[i]["applicationTime"]).format('yyyy-MM-dd') + '</td>' +       //开票申请日期
                        '<td>' + financeInvoiceDetails[i]["applicantName"] + '</td>' +   //申请者
                        '<td>' + chargeState(state) + '</td>' + reason;  //状态
                    invoiceListDetailStr += '<td>';
                    if (state == 1 || state == 4) { // 空白
                        invoiceListDetailStr += '<span class="ty-color-blue" onclick="invoiceEntryBtn($(this))">开票录入</span>' +
                            '<span class="ty-color-red" onclick="voidInvoiceBtn($(this))">作废</span>';
                    } else if (state == 2) { // 已使用
                        invoiceListDetailStr += '<span class="ty-color-blue" onclick="invoiceUpdateBtn($(this))">修改</span>' +
                            '<span class="ty-color-blue" onclick="invoiceUpdateLogBtn($(this))">操作记录</span>';
                    } else if (state == 3) { // 已作废
                        invoiceListDetailStr += '<span class="ty-color-blue" onclick="invoiceOpenBtn($(this))">启用</span>' +
                            '<span class="ty-color-blue" onclick="invoiceUpdateLogBtn($(this))">操作记录</span>';
                    }
                    invoiceListDetailStr += '<span class="hd">' + JSON.stringify(financeInvoiceDetails[i]) + '</span></td></tr>';
                }
                $("#financeInvoiceDetails").html(invoiceListDetailStr);
            }

        }
    })
}

/* creator：sy，2022-2-14 财务管理显示供选择的客户名称列表 */
/*function showReceiveCorpItems(){}*/
//改变确定按钮的颜色
function valietni(){
}












/* creator：hxz，2019-02-15 工具方法 - 根据数值返回发票的状态 */
function chargeState(state) {
    switch (state) {
        case 1:
        case "1":
        case 4:
        case "4":
            return "空白";
            break;
        case 2:
        case "2":
            return "已使用";
            break;
        case 3:
        case "3":
            return "已作废";
            break;
        default:
    }
}
/* creator：张旭博，2017-07-04 10:54:31，新增发票 - 按钮 */
function newInvoiceBtn(){
    if(hasAuthority(0) !== true){
        bounce.show($("#newInvoice"));
        $("#buyDate").val(getCurDate());
        $("#newInvoice .beginNo").val("");
        $("#newInvoice .endNo").val("");
        $("#newInvoice .buyNo").val("");
        // 获取设置的发票种类
        $.ajax({
            "url": "../invoiceSetting/getFinanceInvoiceSetting.do",
            success: function (res) {
                var list = res["financeInvoiceSettings"];
                var str = "<option value=\"\"> 请选择</option>";
                if (list && list.length > 0) {
                    for (var i = 0; i < list.length; i++) {
                        var cate = list[i]["category"] ;
                        str += " <option value='"+ cate +"'>"+ category(cate) +"</option>";
                    }
                }
                $(".buyKind").html(str) ;

            }
        });
        // 启用定时器
        $('body').everyTime('0.5s','newInvoice',function(){
            var i = 0;
            $("#newInvoice input").each(function () {
                if($(this).val() == ""){
                    i++;
                }
            });
            $("#newInvoice select").each(function () {
                if ($(this).val() == "") {
                    i++;
                }
            });
            var beginNo = $("#newInvoice .beginNo").val();
            var endNo   = $("#newInvoice .endNo").val();
            if(beginNo.length != 8 || endNo.length  != 8 || beginNo>endNo || endNo-beginNo >19){i++;}
            if(i == 0){
                $("#sureNewInvoiceBtn").removeAttr("disabled")
            }else{
                $("#sureNewInvoiceBtn").attr("disabled","disabled")
            }
        });
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}

/* creator：张旭博，2017-07-05 16:44:08，新增发票 - 确定 */
function sureNewInvoice() {
    // buyDate(购买日期) buyerName(购买者) beginNo(起号码) endNo(止号码) 
    var buyDate     = $("#newInvoice .buyDate").val();      //购买日期
    var buyerName   = $("#newInvoice .buyerName option:selected").text();    //购买者
    var beginNo     = $("#newInvoice .beginNo").val();      //起号码
    var endNo       = $("#newInvoice .endNo").val();        //止号码
    var buyKind = $("#newInvoice .buyKind").val();    // 发票种类
    var buyNo = $("#newInvoice .buyNo").val();        // 发票代码

    var data = {
        "buyDate": new Date(buyDate),    //购买日期
        "type": buyKind,                 //发票种类
        "invoiceCode": buyNo,              //发票代码
        "buyerName" : buyerName,            //购买者
        "beginNo"   : beginNo,              //起号码
        "endNo"     : endNo                 //止号码
    };
    if(buyDate == ""){
        $("#F_errorTip .tipWord").html("请选择购买日期！");
        bounce_Fixed.show($("#F_errorTip"));
    }else if(buyerName == ""){
        $("#F_errorTip .tipWord").html("请选择购买者！");
        bounce_Fixed.show($("#F_errorTip"));
    }else if(beginNo == ""){
        $("#F_errorTip .tipWord").html("请输入起号码！");
        bounce_Fixed.show($("#F_errorTip"));
    }else{
        $.ajax({
            url: "../invoice/addInvoice.do",
            data : data,
            success:function( data ){
                var state = data["state"];
                if(state == undefined){
                    $("#F_errorTip .tipWord").html("系统错误，请重试！");
                    bounce_Fixed.show($("#F_errorTip"));
                }else if(state == 1){
                    $("#tip .tipWord").html("添加成功！");
                    bounce.show($("#tip"));
                    getInvoiceList();
                }else if(state == 0){
                    $("#F_errorTip .tipWord").html("添加失败！");
                    bounce_Fixed.show($("#F_errorTip"));
                }else if(state == 2){
                    $("#F_errorTip .tipWord").html("请录入等于或少于20张支票！");
                    bounce_Fixed.show($("#F_errorTip"));
                }else{
                    $("#F_errorTip .tipWord").html("未知的返回值！");
                    bounce_Fixed.show($("#F_errorTip"));
                }
            }
        })
    }
}
/* creator：张旭博，2017-07-05 17:00:53，点击发票编号段按钮弹出发票列表 */
function invoiceListBtn(selector){
    bounce.show($("#invoiceList"));
    selector.parent().parent().addClass("itemActive").siblings("tr").removeClass("itemActive");
    getInvoiceDetailList();
}

/* creator：张旭博，2017-07-05 17:01:56，开票录入-按钮 */
function invoiceEntryBtn(selector){
    if(hasAuthority(0) !== true){
        updateInvoiceType = 0;
        selector.parent().parent().addClass("invoiceItemActive").siblings("tr").removeClass("invoiceItemActive");
        bounce_Fixed.show($("#invoiceEntry"));
        $("#invoiceEntry .redTip").html("");
        // $("#applicationTime").val(getCurDate());
        // $("#operateDate").val(getCurDate());
        $("#amount").val("");
        // $("#applicantName").val("");
        // $("#taxInclusive").val("");
        // $("#receiveCorp").val("");

        // $("#receiveCorp").data("revice",{});
        //初始显示，点击录入按钮后再点击输入框时显示全部内容
        $(".receiveCorpItems option").show();

        $('body').everyTime('0.5s','setInvoice',function(){
            var i = 0;
            //尝试
            //let cd = $(".receiveCorpItems option").attr("costId");//读取costId的值
            let cc = $("#receiveCorp").data("revice");//data存储的输入的json数据
            console.log("这是cc的值：")
            console.log(cc);
            let dd = cc.costId;
            console.log(dd);

            if($("#invoiceEntry .operateDate").val() === "" ||
                dd === undefined ||
                //要改!!!
                //识别输入框中的数据所代表的id中是否有cusID,先将costId的值读出来
                $("#invoiceEntry .amount").val() === "" ||
                $("#taxInclusive").val() == null){
                i++;//i是用来判断必填项中的内容是否填写完整的，是否是能在筛选框中匹配到的
            }
            if(i === 0){//输入框中填入的信息正好可以跟筛选框中的内容进行匹配，b=true时
                $("#sureInvoiceEntryBtn").removeAttr("disabled")
                //i=0时可以点击按钮
            }else{//输入框中填入的信息或为1或为a，无法跟筛选框中匹配，b=false
                $("#sureInvoiceEntryBtn").attr("disabled","disabled")
                //i>0时按钮不能点
            }
        });
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}

/* creator：张旭博，2017-07-05 17:01:56，开票录入-修改 */
function invoiceUpdateBtn(selector) {
    if (hasAuthority(0) !== true) {
        selector.parent().parent().addClass("invoiceItemActive").siblings("tr").removeClass("invoiceItemActive");
        bounce_Fixed.show($("#updateInvoice"));
        updateInvoiceType = 0;
        $("#updateInvoice").find("i").attr("class", "fa fa-circle-o");

    } else {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}

/* creator: hxz 2019-02-18 开票录入 - 修改 - 切换选项 */
var updateInvoiceType = 0;

function togleIcon(obj, type) {
    updateInvoiceType = type;
    obj.find("i").attr("class", "fa fa-dot-circle-o")
        .parent().siblings().find("i").attr("class", "fa fa-circle-o");
}

/* creator: hxz 2019-02-18 开票录入 - 修改 - 确定1 */
function sureupdateInvoiceType() {
    if (updateInvoiceType == 2) {
        bounce_Fixed.show($("#invoiceEntry"));
        var info = $(".invoiceItemActive").find(".hd").html();
        info = JSON.parse(info);
        var applicationTime = info["applicationTime"];
        applicationTime = moment(applicationTime).format("YYYY-MM-DD")
        var operateDate = info["operateDate"];
        operateDate = moment(operateDate).format("YYYY-MM-DD");
        $("#invoiceEntry .errorTip").html("");
        $("#applicationTime").val(applicationTime);
        $("#operateDate").val(operateDate);
        $("#amount").val(info["amount"]);// 发票金额
        $("#applicantName").val(info["applicantName"]); // 申请人
        $("#receiveCorp").val(info["receiveCorp"]).data('id', info["receiveCorpId"]); // 客户名称
        $("#taxInclusive").val(info["taxInclusive"]); // 是否含税

        $("#receiveCorp").data("revice",{});
        $(".receiveCorpItems option").show();

        $('body').everyTime('0.5s', 'setInvoice', function () {
            var i = 0;
            let oo = $("#receiveCorp").data("revice");

            let tt = oo.costId;

            if ($("#invoiceEntry .operateDate").val() === "" ||
                tt === undefined || $("#invoiceEntry .amount").val() === "") {
                i++;
            }
            if (i === 0) {
                $("#sureInvoiceEntryBtn").removeAttr("disabled")
            } else {
                $("#sureInvoiceEntryBtn").attr("disabled", "disabled")
            }
        });
    } else {
        sureInvoiceEntry();
    }
}

/* creator：张旭博，2017-07-05 17:02:29，开票录入(修改或者)-确定 */
function sureInvoiceEntry() {
    var id                  = $(".invoiceItemActive").attr("id");
    var applicationTime     = $("#invoiceEntry .applicationTime").val();    //开票申请时间
    var applicantName       = $("#invoiceEntry .applicantName").val();      //申请者
    var operateDate         = $("#invoiceEntry .operateDate").val();        //开票时间
    var receiveCorpId       = $("#invoiceEntry .receiveCorpItems option").val();        //客户id
    var receiveCorp         = $("#invoiceEntry .receiveCorp").val();        //客户名称
    var amount              = $("#invoiceEntry .amount").val();             //发票金额
    var taxInclusive        = $("#invoiceEntry .taxInclusive").val();       //是否含税
    var data = {};
    var url = "";
    if (updateInvoiceType == 0) { // 新增
        url = "addInvoiceDetail.do";
        if (operateDate == "" || receiveCorpId == "" || amount == "") {
            return;
        }
        data = {
            "id": id,                                           //id(支票详情的id)
            "applicationTime": new Date(applicationTime),                    //开票申请时间
            "applicantName": applicantName,                                //申请者
            "operateDate": new Date(operateDate),                        //开票时间
            "receiveCorpId": receiveCorpId,                                //客户名称id
            "receiveCorp": receiveCorp,                                  //客户名称
            "amount": amount,                                       //发票金额
            "taxInclusive": taxInclusive                                  //是否含税
        };
    } else { // 修改
        url = "../invoice/updateInvoice.do";
        if (updateInvoiceType == 2) {
            if (operateDate == "" || receiveCorpId == "" || amount == "") {
                return;
            }
            data = {
                "id": id,                                         // id(支票详情的id)
                "operation": updateInvoiceType,                 //
                "amount": amount,                                       //发票金额
                "operateDate": new Date(operateDate),                        //开票时间
                "receiveCorp": receiveCorp,                                  //客户名称
                "applicationTime": new Date(applicationTime),                    //开票申请时间
                "taxInclusive": taxInclusive,                                 //是否含税
                "applicantName": applicantName,                                //申请者
                "receiveCorpId": receiveCorpId,                                //客户名称id
            };
        } else {
            data = {
                "id": id,                                           // id(支票详情的id)
                "operation": updateInvoiceType
            };
        }
    }
    $.ajax({
        url: url,
        data: data,
        success: function (data) {
            var state = data["state"];
            if (updateInvoiceType == 0) { // 新增
                if(state == undefined){
                    $("#errorTip .tipWord").html("系统错误，请重试！");
                    bounce.show($("#errorTip"));
                }else if(state == 1){
                    bounce_Fixed.cancel();
                    $("#F_tip .tipWord").html("录入成功！");
                    bounce_Fixed.show($("#F_tip"));
                    getInvoiceDetailList();
                } else if (state == 0 || state == 2) {
                    $("#invoiceEntry .errorTip").html("录入失败，录入的金额大于此类发票设置的金额上限！");
                }else{
                    $("#invoiceEntry .errorTip ").html("未知的返回值！");
                }
            } else { // 修改
                if (state == 1) {
                    bounce_Fixed.cancel();
                    layer.msg("修改成功！");
                    getInvoiceDetailList();
                } else {
                    layer.msg("修改失败！");
                }
            }

        }
    })

}

// creator: hxz 2019-02-16 操作记录列表
function invoiceUpdateLogBtn(selector) {
    var info = selector.siblings(".hd").html();
    info = JSON.parse(info);
    var id = info["id"];
    $.ajax({
        "url": "../invoice/getRecords.do",
        "data": {"invoiceDetailId": id},
        success: function (res) {
            bounce_Fixed.show($("#invoiceLog"));
            var list = res["financeInvoiceDetailHistories"];
            if (list && list.length > 0) {
                var str = "";
                for (var i = 0; i < list.length; i++) {
                    var previousStateStr = chargeState(list[i]["previousState"]);
                    if (previousStateStr == "已使用") {
                        previousStateStr = "<span class='ty-color-blue' onclick='showLogInfo($(this)," + list[i]["previousId"] + ")'>查看</span>"
                    }
                    var stateStr = chargeState(list[i]["state"]);
                    if (stateStr == "已使用") {
                        stateStr = "<span class='ty-color-blue' onclick='showLogInfo($(this)," + list[i]["id"] + ")'>查看</span>"
                    }
                    str += "<tr>" +
                        "    <td>" + moment(list[i]["createDate"]).format("YYYY-MM-DD HH:mm:ss") + "</td>" +
                        "    <td>" + chargeOperation(list[i]["operation"]) + "</td>" +
                        "    <td>" + list[i]["createName"] + "</td>" +
                        "    <td class='ty-td-control'>" + previousStateStr + "</td>" +
                        "    <td>" + stateStr + "</td>" +
                        "</tr>";
                }
                $("#invoiceLog tbody").children("tr:gt(0)").remove();
                $("#invoiceLog tbody").append(str);
            }
        }
    })
}

// creator: hxz 2019-02-19 某条操作历史
function showLogInfo(obj, id) {
    $.ajax({
        "url": "../invoice/getInvoiceDetailHistoryDetail.do",
        "data": {"invoiceDetailHistoryId": id},
        success: function (res) {
            bounce_Fixed2.show($("#invoiceLogInfo"));
            var info = res["financeInvoiceDetailHistory"];
            $("#applicationTime_log").html(moment(info["applicationTime"]).format("YYYY-MM-DD"));
            $("#applicantName_log").html(info["applicantName"]);
            $("#operateDate_log").html(moment(info["operateDate"]).format("YYYY-MM-DD"));
            $("#receiveCorp_log").html(info["receiveCorp"]);
            $("#amount_log").html(info["amount"]);
            $("#taxInclusive_log").html(info["taxInclusive"] == 0 ? "否" : "是");
        }
    })
}
// creator : hxz 2019-02-15 工具方法 - 返回操作内容
function chargeOperation(operation) {
    switch (operation) {
        case 1:
        case "1":
            return "开票录入";
            break;
        case 2:
        case "2":
            return "删除";
            break;
        case 3:
        case "3":
            return "修改";
            break;
        case 4:
        case "4":
            return "作废";
            break;
        case 5:
        case "5":
            return "启用";
            break;
        default :
            return "";
    }
}
// creator : hxz 2019-02-15 发票作废
function voidInvoiceBtn(selector) {
    if(hasAuthority(0) !== true){
        selector.parent().parent().addClass("invoiceItemActive").siblings("tr").removeClass("invoiceItemActive");
        $("#voidInvoice .reason").val("");
        bounce_Fixed.show($("#voidInvoice"));
    }else{
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}

// updator : hxz 2019-02-15 发票作废-确定
function sureVoidInvoice() {
    var url = "cancelInvoice.do";
    var invoiceDetailId     = $(".invoiceItemActive").attr("id");
    var reason              = $("#voidInvoice .reason").val();

    var data = {
        "invoiceDetailId":invoiceDetailId,
        "state":3,
        "reason":reason
    };
    $.ajax({
        url: url,
        data: data,
        success: function (data) {
            loading.close();
            bounce_Fixed.cancel();
            var state = data["state"];
            if (state == undefined) {
                $("#errorTip .tipWord").html("系统错误，请重试！");
                bounce.show($("#errorTip"));
            } else if (state == 1) {

                $("#F_tip .tipWord").html("报废成功！");
                bounce_Fixed.show($("#F_tip"));
                getInvoiceDetailList();
            } else if (state == 0) {
                $("#F_errorTip .tipWord").html("报废失败！");
                bounce_Fixed.show($("#F_errorTip"));
            } else {
                $("#F_errorTip .tipWord").html("未知的返回值！");
                bounce_Fixed.show($("#F_errorTip"));
            }
        }
    })
}

// creator : hxz 2019-02-15 发票启用
function invoiceOpenBtn(selector) {
    if (hasAuthority(0) !== true) {
        selector.parent().parent().addClass("invoiceItemActive").siblings("tr").removeClass("invoiceItemActive");
        $("#voidInvoice .reason").val("");
        bounce_Fixed.show($("#openInvoice"));
    } else {
        $("#errorTip .tipWord").html("您没有此权限！");
        bounce.show($("#errorTip"));
    }
}

// updator : hxz 2019-02-15 发票作废-确定
function sureOpenInvoice() {
    var invoiceDetailId = $(".invoiceItemActive").attr("id");
    $.ajax({
        "url": "enable.do",
        "data": {"invoiceDetailId": invoiceDetailId},
        success:function(data ){
            var state = data["state"];
            if (state == 1) {
                bounce_Fixed.cancel();
                layer.msg("启用成功！");
                getInvoiceDetailList();
            } else {
                layer.msg("启用失败,请重试！");
            }
        }
    })
}

function getAllCustomer(selector) {
    var url = "getAllCustomer.do";
    $.ajax({
        url : url ,
        beforeSend: function () {
        },
        success:function( data ){
            //Customers 客户信息{name(客户名称)、id(客户id)}
            var customers   = data["Customers"];
            var customerStr = "";
            if(customers == undefined){
                $("#errorTip .tipWord").html("系统错误，请重试！");
                bounce.show($("#errorTip"));
            }else{
                if (customers.length === 0) {
                    customerStr = `<span>未获取到客户</span> `
                }
                for(var i in customers){
                    customerStr += '<option invoice="'+customers[i].invoiceRequire+'" code="'+customers[i].code+'" ' +
                        'value="'+customers[i].id+'">'+customers[i].fullName+'</option>'
                }
                selector.html(customerStr);
            }
        },
        complete:function(){}
    })
}
function getAllFinancers(selector) {
    var url = "getAllFinanceers.do";
    $.ajax({
        url : url ,
        beforeSend: function () {
        },
        success:function( data ){
            //Customers 客户信息{name(客户名称)、id(客户id)}
            var user   = data["user"];
            var financerStr = "";
            if(user !== null){
                financerStr = '<option value="'+user.userID+'">'+user.userName+'</option>'
                selector.html(financerStr);
            }
        },
        complete:function(){}
    })
}
function validateNo(obj){
    obj.val(obj.val().replace(/[^\d]/g,"")); //清除“数字”和“.”以外的字符
    obj.val(obj.val().replace(/\.{2,}/g,".")); //只保留第一个. 清除多余的.
    obj.val(obj.val().replace(".","$#$").replace(/\./g,"").replace("$#$",".")); //清除“数字”和“.”以外的字符
    if(obj.val().length > 8){
        obj.val(obj.val().substring(0,8));
    }
}
function validateAmount(obj) {
    obj.val(obj.val().replace(/[^\d]\./g,"")); //清除“数字”和“.”以外的字符
    obj.val(obj.val().replace(/^\./g, "")); // //验证第一个字符是数字而不是.
    obj.val(obj.val().replace(/\.{2,}/g,".")); //只保留第一个. 清除多余的.
    obj.val(obj.val().replace(".","$#$").replace(/\./g,"").replace("$#$",".")); //清除“数字”和“.”以外的字符
    var valArr = obj.val().split('.');
    var newVal= obj.val();
    if(valArr.length === 2  ){
        newVal = valArr[0] + '.' + valArr[1].substr(0,2);
    }
    obj.val(newVal);
    var  amountLimited = $("#setInvoice .amountLimited").val();
    if(parseInt(obj.val())>parseInt(amountLimited)){
        obj.val("");
    }
}
function PrefixInteger(num, length) {
    return (Array(length).join('0') + num).slice(-length);
}

function changeReason(reason) {
    if(reason.length>8){
        reason = reason.substring(0,8)+'...';
    }
    return reason;
}


























// creator : hxz 2018-07-20  工具方法 ， 返回发票种类
function category(type) {
    if (type == 1) {
        return "增值税专用票";
    } else if (type == 2) {
        return "增值税普通票";
    } else if (type == 3) {
        return "其他普通票";
    } else {
        return "";
    }
}
// 日期控件
laydate.render({elem: '#buyDate'});
laydate.render({elem: '#applicationTime'});
laydate.render({elem: '#operateDate'});