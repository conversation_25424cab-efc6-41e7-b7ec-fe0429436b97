$(function(){
    getMainData();
    $(".mainCon1").show();
    // 选择离职员工
    $("#unStaff").on('click',"li", function () {
        $("#unStaff .fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).find(".fa").attr("class", "fa fa-dot-circle-o");
    });
    $(".checkStaff").on('click',".toggle", function () {
        let type = $(this).data("type");
        let faObj = $(this).children(".fa");
        if(faObj.hasClass("fa-square-o")){
            faObj.attr("class","fa fa-check-square-o");
            if (type == 'all') {
                $(this).parent().siblings().find(".fa").attr("class","fa fa-check-square-o");
            } else {
                let noCheckLen = $(".checkStaff .fa-square-o").length;
                if (noCheckLen <= 1) {
                    $(this).parent().siblings().eq(0).find(".fa").attr("class","fa fa-check-square-o");
                }
            }
        }else{
            if (type == 'all') {
                $(this).parent().siblings().find(".fa").attr("class","fa fa-square-o");
            } else {
                let first = $(this).parent().siblings().eq(0).find(".fa");
                if (first.hasClass("fa-check-square-o")) {
                    first.attr("class","fa fa-square-o");
                }
            }
            faObj.attr("class","fa fa-square-o");
        }
    });
    $("body").on("click", ".jumpBtn,.ty-btn", function () {
        let fun = $(this).data("fun");
        switch (fun) {
            case 'salaryScan': // 查看 某次的工资记录
                let item = $(this).siblings('.hd').html();
                item = JSON.parse(item);
                salaryScan(item);
                break;
            case 'present': // 当月手动录入
                layer.msg('尚无数据，不可录入！');
                break;
            case 'ssBackMain':
                $(this).hide();
                getMainData();
                break;
            case 'backMain': // 返 回
                showMainCon(1);
                break;
            case 'back31': // 返 回
                showMainCon(31);
                break;

            case 'nextStepEdit': // 修改-下一步
                editSalaryNext();
                break;
            case 'backCheck': // 返回-下一步
                bounce.show($("#cancelTip2"))
                break;
            default:
                fun && window[fun]($(this));
        }
    });
});
// creator: hxz，2022-05-10 按职工查看
function scanByUser(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    $.ajax({
        "url":"../salary/getPayByUserList.do",
        "data":{ "period": info.period , "type":info.type },
        success:function (res) {
            showMainCon(32);
            let data = res.data;
            let monthStr = info.period.substr(0,4) + '年' + info.period.substr(4,2) + '月' ;
            $("#tt32").html(monthStr);
            $(".mainCon32 .number_i").html(data.userNum);
            let pay = 0
            let list = data.mapList || [],str="";
            list.forEach(function (item) {
                pay += Number(item.factPay)
                item.period = info.period
                item.type = info.type
                str += `
                <tr>
                    <td>${ item.userName }</td>
                    <td>${ item.mobile }</td>
                    <td>${ item.departName || '' }  ${ item.postName && item.departName ? '/' : '' } ${ item.postName || '' }</td>
                    <td>${ item.factPay.toFixed(2) }</td>
                    <td>
                        <span class="ty-color-blue jumpBtn" data-fun="scanPersonByMonth">查看</span>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                </tr>
                `
            })
            $(".mainCon32 .amount_i").html(pay.toFixed(2));

            $("#scanSalaryByUser tbody tr:gt(0)").remove();
            $("#scanSalaryByUser tbody").append(str);
            $("#scanSalaryByUser .placeTitle").html('实发');
        }
    })

}
function backScan(){
    let back = $(".mainCon5").data("back")
    if(back == 1){
        showMainCon(31);
    }else {
        showMainCon(3);
    }
}
// creator: hxz，2022-05-10 5页的返回键
function backCheck() {
    bounce.cancel();
    let back = $(".mainCon5").data("back")
    if(back == 1){
        showMainCon(31);
    }else {
        showMainCon(3);
    }
}
// creator: hxz，2022-05-09 13:45:19 修改
function itemUpdate2(obj) {
    let info = JSON.parse($("#itemInfo").html()) ;
    itemUpdateFun(info)
    $(".mainCon5").data("back",2);
}
// creator: hxz，2022-05-09 13:45:19 修改
function itemUpdate(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    itemUpdateFun(info)
    $(".mainCon5").data("back",1);
}
// creator: hxz，2022-05-09 13:45:19 修改
function itemUpdateFun(info) {
    let op1 = ``;
    let op2 = ``;
    let typeStr = ``;
    if(info.type == 1){
        op1 = '发放信息'
        op2 = '职工工资'
        typeStr = '职工工资'
        $(".mainCon4 .placeTitle").html('实发')
    }else{
        op1 = '支出信息'
        op2 = '职工项'
        if(info.type == 2){
            typeStr = '个人所得税'
            $(".mainCon4 .placeTitle").html('个人所得税')

        }else if(info.type == 3){
            typeStr = '社保'
            $(".mainCon4 .placeTitle").html('应缴社保')

        }else if(info.type == 4){
            typeStr = '公积金'
            $(".mainCon4 .placeTitle").html('应缴公积金')
        }
    }
    $("#updateTip .tip0").html(`如${ op1 }与${ typeStr }均修改，请修改两次`);
    $("#updateTip .op1").html(op1);
    $("#updateTip .op2").html(op2);
    $("#updateTip").data('info', info);
    bounce.show($("#updateTip"))
    $("#updateTip select").val("");
}
function updateTipOk(){
    let info = $("#updateTip").data('info');
    let type = info.type
    let editType = $("#updateTip select").val();
    if(!(Number(editType) > 0)){
        layer('请先选择 修改的类型 ！')
        return false
    }
    bounce.cancel();
    let yearMonth = info.period.substr(0,4) + '年' + info.period.substr(4,2) + '月';
    let date_t = '支出日期'
    let method_t = '支出方式'
    let amount_t = ''
    let ttl = ''
    let typeStr = ''
    let tipdStr = ''
    let catStr = changeType(type);
    $(".mainCon5 .placeTitle").html(catStr);
    switch (Number(type)){
        case 1:
            date_t = '发放日期'
            method_t = '发放方式'
            amount_t = '本次实发总额'
            ttl = `工资所属月份：${yearMonth}`
            typeStr = '工资'
            tipdStr = '请选择要修改工资的职工'
            break;
        case 2:
            amount_t = '本月职工应缴个所税总额'
            ttl = `${yearMonth}个人所得税明细表`
            typeStr = '个人所得税'
            tipdStr = '请选择要修改应缴个所税的职工'
            break;
        case 3:
            amount_t = '本月职工应缴社保总额'
            ttl = `${yearMonth}社保缴纳明细表`
            typeStr = '社保'
            tipdStr = '请选择要修改应缴社保的职工'
            break;
        case 4:
            amount_t = '本月职工应缴公积金总额'
            ttl = `${yearMonth}公积金缴纳明细表`
            typeStr = '公积金'
            tipdStr = '请选择要修改应缴公积金的职工'
            break;
        default:
    }
    $(".mainCon4 .tipd").html(tipdStr);
    $(".mainCon4 .tt4").html(ttl);
    $(".mainCon5 .tt4").html(ttl);
    $(".mainCon5 .date_span1").html(date_t);
    $(".mainCon5 .method_span1").html(method_t);
    $(".mainCon5 .amount_span1").html(amount_t);
    $(".mainCon5 .getUnStaff").data("period",info.period);
    $(".mainCon5 .backCheck").data("back", { 'type': type, 'editType':editType })
    if(editType == 1){
        $(".mainCon5 .tt4").html(ttl);
        $(".mainCon5 .edittype1 .tip1").html(`请在页面上修改${ typeStr }的${ method_t }。`)
        $(".mainCon5 .edittype2").hide()
        $(".mainCon5 .edittype1").show()
    }else{
        $(".mainCon5 .edittype2 .tip1").html(`请在页面上修改${ typeStr }的${ method_t }。`)
        $(".mainCon5 .edittype2").show()
        $(`.mainCon5 .edittype2 .type${ type }`).show().siblings().hide();
        $(".mainCon5 .edittype1").hide()
    }
    if(type == 1){// 工资的
        salaryScan(info, editType);
    }else{ // 其他几项
        scanItemInfo(info.period,type,editType)
    }
}
// creator: hxz，2022-05-09 渲染修改的页面
function renderSelectTb(data) {
    let list = data.mapList || [];
    let str2 = ``;
    $(".checkStaff tbody tr:gt(0)").remove()
    $(".checkStaff .fa-check-square-o").attr("class","fa fa-square-o")

    list.forEach((item)=>{
        str2 += `
                <tr>
                    <td class="toggle">
                        <i class="fa fa-square-o"></i>
                        <span class="hd">${ JSON.stringify(item) }</span>
                    </td>
                    <td>${ item.userName }</td>
                    <td>${ item.mobile }</td>
                    <td>${ item.departName || '' } ${ item.postName && item.departName ? '/':'' } ${ item.postName || '' }</td>
                    <td>${ item.factPay.toFixed(2) }</td>
                </tr>
                `
    })
    $(".checkStaff tbody").append(str2)
}
function renderShowTb(data, editType) {
    let info = data.personnelPay ;
    let list = data.mapList || [];
    $(".updateSalary tr:gt(0)").remove()

    if(editType == 1){
        $("#date_input1").val(new Date(info.payTime).format("yyyy-MM-dd"))
        getAccountList($("#method_input1"), info.financeAccount)
        $("#amount_input1").html(info.pay.toFixed(2))
    }else{
        $("#updateTotalAmount").val(info.pay.toFixed(2))
    }
    let str2 = ``;
    list.forEach((item)=>{
        str2 += `
                <tr data-id="${ item.payId }">
                    <td>${ item.userName }</td>
                    <td>${ item.mobile }</td>
                    <td>${ item.departName || '' } ${ item.postName && item.departName ? '/':'' } ${ item.postName || '' }</td>
                    <td>${ editType == 1 ? `${ item.factPay.toFixed(2) }` : `<input type="text" value="${ item.factPay.toFixed(2) } "/>` }</td>
                </tr>
                `
    })
    $(".updateSalary").append(str2)
}
// creator: hxz，2022-05-05 13:45:19 保存修改
function saveUpdateBtn(obj) {
    let userList = []
    let backInfo = $(".mainCon5 .backCheck").data("back")
    let type = backInfo.type
    let editType = backInfo.editType

    $(".updateSalary tr:gt(0):visible").each(function () {
        let payId = $(this).data("id");
        let area = $(this).data("area");
        let factPay = ''
        if(editType == 1){
            factPay = $(this).children("td:last-child").html()
        }else{
            factPay = $(this).find("input").val();
        }
        if(area == 2){
            userList.push({ "userId": payId , "factPay": factPay,  "payId": null ,  })
        }else{
            userList.push({ "payId": payId , "factPay": factPay ,  "userId": null , })
        }
    })
    let operation = 0
    if(type == 1){
        if(editType == 1){
            operation = 5 ;
        }else{
            operation = 6 ;
        }
    }else{
        if(editType == 1){
            operation = 7 ;
        }else{
            operation = 8 ;
        }
    }
    let data = {
        "operation":operation,
        "type":type,
        "userList": JSON.stringify(userList),
    }
    let oralData = $(".updateSalary").data("data")
    let oldInfo = oralData.personnelPay ;
    data.versionNo = oldInfo.versionNo;
    data.period = oldInfo.period;

    if(editType == 1){ // 改发放方式
        data.payTime = $("#date_input1").val();
        let method_input = $("#method_input1").val();
        method_input = method_input.split('-')
        data.payWay = method_input[0];
        data.accountId = method_input[1];
        data.pay = oldInfo.pay

    }else{ // 改职工项
        data.payWay = oldInfo.payWay;
        data.accountId = oldInfo.financeAccount;
        data.payTime = new Date(oldInfo.payTime).format("yyyy-MM-dd");
        data.pay = $("#updateTotalAmount").val();

        // let salary = $(".mainCon3").data('salary');
        let salary = $("#updateTip").data('info');
        let item_total = 0;
        $(".updateSalary input").each(function(){
            if ($(this).val() != ""){
                item_total += Number($(this).val());
            }
        })
        item_total = item_total.toFixed(2)
        if(Number(data.pay) != Number(item_total)) {
            let tip = ``;
            if (salary.type == 1) {
                tip = `<p>每位职工实发金额的和，与本月实发工资总额不相等。</p>`;
            } else if (salary.type == 2) {
                tip = `<p>每位职工个人所得税的和，与本月职工应缴个人所得税总额不相等。</p>`;
            } else if (salary.type == 3) {
                tip = `<p>每位职工应缴社保的和，与本月职工应缴社保总额不相等。</p>`;
            } else if (salary.type == 4){
                tip = `<p>每位职工公积金的和，与本月职工应缴公积金总额不相等。</p>`;
            }
            tip = tip + `<p>请检查后修正！</p>`;
            $("#alertTip .tip").html(tip);
            bounce_Fixed.show($("#alertTip"));
            return false;
        }
    }
    $.ajax({
        "url":"../salary/updateManual.do",
        "data":data,
        success:function (res) {
            let data = res.data;
            let state = data.state;
            if(state == 1){
                layer.msg('操作成功！')
                backCheck();
                let period = $(".mainCon5 .getUnStaff").data("period");
                scanItemInfo(period ,type);
            }
        }
    })

}
// creator: hxz，2022-05-05 13:45:19 已离职的职工
function getUnStaff(obj) {
    let area = obj.data('area')
    let period = obj.data("period");
    period = period.replace("-","");
    $.ajax({
        "url":"../salary/getUsers.do",
        "data": {
            "month": period,
            "isDuty": 2,
        },
        success:function(res) {
            let list = res.data && res.data.users || [];
            let html = ``;
            for(var y=0;y<list.length; y++){
                html +=
                    `<li> 
                        <span class="hd">${ JSON.stringify(list[y]) }</span>
                        <i class="fa fa-circle-o"></i> ${list[y].userName} - ${list[y].mobile}
                    </li>` ;
            }
            if(html== ``){
                html = '暂无数据'
                $("#unStaff .unStaffOK").hide()
            }else {
                $("#unStaff .unStaffOK").show()
            }
            $("#unStaff ul").html(html).data('area', area);
            bounce.show($("#unStaff"));
        }
    })
}
function unStaffOK() {
    let actObj = $("#unStaff .fa-dot-circle-o");
    if(actObj.length > 0){
        let area = $("#unStaff ul").data('area');
        let info = actObj.siblings('.hd').html();
        console.log(actObj.html())
        info = JSON.parse(info)
        let isSame = false
        let tblStr = 'staffList'
        if(area == 2){
            tblStr = 'updateSalary'
        }
        $(`#${tblStr} tbody tr:gt(0)`).each(function () {
            let i_id = $(this).data("id")
            if(i_id == info.userID){
                isSame = true
            }
        })
       if(isSame){
           layer.msg("列表中已存在该员工，无需再次添加")
           return false
       }
        let html =
            `<tr data-id="${info.userID}" data-duty="2" data-area="${area}">
                <td>${info.userName}</td>
                <td>${info.mobile}</td>
                <td>${handleNull(info.departName)} <br/>${handleNull(info.postName)} </td>
                <td><input placeholder="请录入金额" class="form-control" onkeyup="clearNoNumN(this,2)"/></td>
            </tr>`;
        $(`#${tblStr} tbody`).append(html);
        bounce.cancel();
    }else{
        layer.msg('请先选择职工！')
    }
}
// creator: hxz，2022-05-05 13:45:19 查看
function scan(obj){
    let info = obj.parents("tr").find(".hd").html();
    info = JSON.parse(info);
    let type = obj.data("type");
    let catStr = changeType(type);
    let yearMonth = (info.period).replace("-","年") + '月'
    $(".mainCon3 .date_t").html("支出日期 ")
    $(".mainCon3 .number_t").html("职工人数 ")
    $(".mainCon3 .method_t").html("支出方式 ")
    switch (type){
        case 1: // 工资
            $(".mainCon3 .date_t").html("发放日期 ")
            $(".mainCon3 .number_t").html("发放人数 ")
            $(".mainCon3 .method_t").html("发放方式 ")
            $(".mainCon3 .amount_t").html("本次实发总额 ")
            $(".mainCon3 .placeTitle").html("实发");
            $("#tt3").html(`工资所属月份：${yearMonth}`);
            $("#tt31").html(`${yearMonth}工资发放记录`);
            break;
        case 2: // 个人所得税
            $("#tt3").html(`${yearMonth}个人所得税明细表`);
            $(".mainCon3 .amount_t").html("本月职工应缴个人所得税总额 ")
            $(".mainCon3 .placeTitle").html("个人所得税");

            break;
        case 3: // 社保
            $("#tt3").html(`${yearMonth}应缴社保明细表`);
            $(".mainCon3 .amount_t").html("本月职工应缴社保总额 ")
            $(".mainCon3 .placeTitle").html("应缴社保");
            break;
        case 4: // 公积金
            $("#tt3").html(`${yearMonth}应缴公积金明细表`);
            $(".mainCon3 .amount_t").html("本月职工应缴公积金总额 ")
            $(".mainCon3 .placeTitle").html("应缴公积金");
            break;
    }
    scanItemInfo(info.period,type)
}
// creator: hxz，2022-05-06 13:45:19 查看 某次的工资记录
function salaryScan(itemTr, editType) {
    $.ajax({
        'url':"../salary/getPayMonthList.do",
        "data":{ type: 1, period: itemTr.period , versionNo: itemTr.versionNo },
        success:function (res) {
            if(Number(editType) > 0){
                $(".updateSalary").data('data', res.data)

                if(editType == 1){
                    showMainCon(5)
                    $(".mainCon5").data('data', res.data)
                    renderShowTb(res.data, editType)
                }
                else if(editType == 2){
                    showMainCon(4)
                    renderSelectTb(res.data)
                }
            }
            else{
                showMainCon(3);
                $(".mainCon3 .type2").show(); $(".mainCon3 .type1").hide();
                $("#scanSalary1 tr:gt(0)").remove()
                $("#scanSalary tr:gt(0)").remove()
                let data = res.data ;
                if(!data){
                    return false
                }
                let list = data.mapList || [];
                let info = data.personnelPay ;
                $("#itemInfo").html(JSON.stringify(itemTr));
                $(".mainCon3 .flexbox .jumpBtn").hide();
                $(".mainCon3 .number_i").html(list.length + '人');
                $(".mainCon3 .amount_i").html(info.pay.toFixed(2));
                $(".mainCon3 .date_i").html(new Date(info.payTime).format("yyyy-MM-dd") );
                let bankNameStr = ``
                let financeAccount = data.financeAccount
                if(info.payWay == 2){
                    bankNameStr = '转账支付-' + financeAccount["name"] + ' ' + formatAccount(financeAccount["account"])+ ' ' + financeAccount["bankName"] ;
                    if(financeAccount.isPublic == 1){
                        bankNameStr = '转账支付-' + formatAccount(financeAccount["account"])+ ' ' + financeAccount["bankName"] ;
                    }
                }else{
                    bankNameStr = `现金支付`
                }
                $(".mainCon3 .method_i").html(bankNameStr);
                $(".mainCon3 .createD").html(`${info.createName} ${ new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss") }`);
                let str2 = ``;
                list.forEach((item)=>{
                    item.versionNo = itemTr.versionNo
                    item.period =  itemTr.period
                    item.type = 1
                    str2 += `
                            <tr>
                                <td>${ item.userName }</td>
                                <td>${ item.mobile }</td>
                                <td>${ item.departName || '' } ${ item.postName && item.departName ? '/' : ''  } ${ item.postName || '' }</td>
                                <td>${ item.factPay.toFixed(2) }</td>
                               
                            </tr>
                    `
                    // <td>
                    //     <span data-fun="scanPersonByMonth" class="ty-color-blue jumpBtn">查看</span>
                    //     <span class="hd">${ JSON.stringify(item) }</span>
                    // </td>
                })
                $("#scanSalary").append(str2)
                // $("#scanSalary1").append(str2)
                data.type = 1
                data.month =  itemTr.period
                $(".mainCon3").data('salary',data);
            }

        }
    })
}

// creator: hxz，2022-05-06 13:45:19 查看 某人 某月的记录
function scanPersonByMonth(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    let periodStr = info.period.substr(0,4) + '年' + info.period.substr(4,2) + '月'
    $("#scanPerson h4").html(`${ periodStr }职工工资`);
    $("#scanPerson .name").html(info.userName);
    $("#scanPerson .mobile").html(info.mobile);
    $("#scanPerson .depart").html(`${ info.departName || '' } ${ info.postName && info.departName ? '/' : '' } ${ info.postName || '' }`);
    $.ajax({
        "url":"../salary/getPayByUserMonthList.do",
        "data":{
            'userId': info.userId ,
            'type': info.type ,
            'period': info.period ,
        },
        success:function (res) {
            bounce.show($("#scanPerson"));
            let data = res.data;
            if(!data) return false;
            let sunAmount = 0 ;
            let list = data.personnelPayMonth || [];
            let str = ``
            $("#scanPerson table tr:gt(0)").remove()
            list.forEach(function (item) {
                let bankNameStr = ``
                if(item.payWay == 2){
                    bankNameStr = item["name"] + ' ' + formatAccount(item["account"])+ ' ' + item["bankName"] ;
                    if(item.isPublic == 1){
                        bankNameStr = formatAccount(item["account"])+ ' ' + item["bankName"] ;
                    }
                }
                sunAmount += Number(item.factPay);
                str += `
                <tr>
                    <td>${ new Date(item.payTime).format("yyyy-MM-dd") }</td>
                    <td>${ item.payWay == 1 ? '现金' : '转账支付-' } ${ bankNameStr }</td>
                    <td>${ item.factPay.toFixed(2) }</td>
                    <td>${ item.createName } ${ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss") }</td>
                </tr>
                `
            })
            $("#scanPerson .sunAmount").html(sunAmount.toFixed(2));
            $("#scanPerson table").append(str);
        }
    })

}
function back32() {
    showMainCon(31);
}
// creator: hxz，2022-05-11 13:45:19 继续录入本月工资
function entryMore(obj) {
    let info = obj.siblings(".hd").html();
    info = JSON.parse(info);
    let yearMonth = info.period.substr(0,4) + '年' + info.period.substr(4,2) + '月'
    $(".mainCon2 .salaryTitle").html(`工资所属月份：${yearMonth}`);
    $(`#cancelbtn`).html('放弃已编辑数据，退出');
    $(`#date_span`).html('发放日期');
    $(`#method_span`).html('发放方式');
    $(`#amount_span`).html('本次实发总额');
    $("#staffList .tbTitle").html(`实发`);
    $(".mainCon2").data("type",info.type);
    $(".mainCon2").data("period",info.period);
    $(".mainCon2 .getUnStaff").data("period",info.period);
    $(".mainCon2 input").val("");
    $(".mainCon2 .entryTip").hide();
    $(".mainCon2 .tip1").show();
    getEnteryData(info.period);
    getAccountList($("#method_input"))
    showMainCon(2);
}
// creator: hxz，2022-05-05 13:45:19 录入数据
function initEntry(obj){
    let info = obj.parents("tr").find(".hd").html();
    info = JSON.parse(info);
    let idToday = info.period == info.today ? true: false;
    if(idToday){
        layer.msg('尚无数据，不可录入！')
        return false
    }
    let type = Number(obj.data("type"));
    $(".mainCon2").data("type",type);
    $(".mainCon2").data("period",info.period);
    $(".mainCon2 .getUnStaff").data("period",info.period);
    $(".mainCon2 input").val("");
    $(".mainCon2 .entryTip").hide();

    $(`.mainCon2 .ty-left .tip${ type}`).show();
    $(`#cancelbtn`).html('取 消');
    $(`#date_span`).html('支出日期');
    $(`#method_span`).html('支出方式');
    let catStr = changeType(type);
    $(".mainCon2 .placeTitle").html(catStr);
    let yearMonth = info.period
    yearMonth = yearMonth.substr(0,4) + '年' + yearMonth.substr(5,2) + '月' ;
    switch (type){
        case 1: // 工资
            $(`#cancelbtn`).html('放弃已编辑数据，退出');
            $(`#date_span`).html('发放日期');
            $(`#method_span`).html('发放方式');
            $(`#amount_span`).html('本次实发总额');
            $(".mainCon2 .salaryTitle").html(`工资所属月份：${yearMonth}`);
            $("#staffList .tbTitle").html(`实发`);

            break;
        case 2: // 个人所得税
            $(`#amount_span`).html('本月职工个人所得税总额');
            $(".mainCon2 .salaryTitle").html(`${yearMonth}个人所得税明细表`);
            $("#staffList .tbTitle").html(`个人所得税`);
            break;
        case 3: // 社保
            $(`#amount_span`).html('本月职工社保总额');
            $(".mainCon2 .salaryTitle").html(`${yearMonth}社保缴纳明细表`);
            $("#staffList .tbTitle").html(`社保`);

            break;
        case 4: // 公积金
            $(`#amount_span`).html('本月职工公积金总额');
            $(".mainCon2 .salaryTitle").html(`${yearMonth}公积金缴纳明细表`);
            $("#staffList .tbTitle").html(`公积金`);
            break;
    }
    getEnteryData(info.period);
    getAccountList($("#method_input"))
    showMainCon(2);
}
// creator: 李玉婷，2021-07-05 13:45:19，放弃您刚编辑的内容
function backPrev(){
    showMainCon(1)
    bounce.cancel();
}
// create : hxz 2021-1-34 显示主页面
function showMainCon(num){
    $("#showMainConNum").val(num);
    $(".mainCon").hide();$(".mainCon" + num).show();
}

// creator: 李玉婷，2021-06-09 13:41:23，主页获取
function getMainData() {
    $("#indexBack").hide().siblings().show();
    $("#yearSearch").val("");
    getSalaryList("")
}
// update: hxz，2022-05-12 15:57:44，搜索
function searchKeySure() {
    if ($("#yearSearch").val() == "") {
        return false;
    }
    $("#indexBack").show().siblings().hide();
    getSalaryList($("#yearSearch").val())
}
function getSalaryList(year) {
    $.ajax({
        "url":"../salary/getSalaryList.do",
        "data": { yearDate:year },
        success:function(res, status, xhr) {
            let list = res.data && res.data.payPeriodDtos  || []
            let today = new Date(xhr.getResponseHeader('Date')).format('yyyy');
            let todayMonth = new Date(xhr.getResponseHeader('Date')).format('yyyy-MM');
            laydate.render({elem: '#yearSearch', type: 'year', max: today });
            $("#yearDesc").html(year || today);
            let str = ``
            list.forEach(function (item) {
                item.today = todayMonth
                if(item.period){
                    item.period = item.period.substr(0,4) + '-' + item.period.substr(4,2)
                } else {
                    item.period = '- -'
                }
                let idToday = item.period == today ? true: false;

                str += `<tr>
                            <td>${ item.period }<span class="hd">${ JSON.stringify(item) }</span></td>
                            <td>
                                <div class="${ idToday || item.salaryState == 2  ? '': 'ty-color-red' }">${  item.salaryState == 2 ? (new Date(item.dateSalary).format("MM-dd")): (item.salaryState == 1 ? '需继续录入' : '尚无数据') }</div>
                                ${ item.salaryState == 2 || item.salaryState == 1 ? `<div class="jumpBtn" data-fun="scan" data-type="1">查看</div>` : `<div class="jumpBtn" data-fun="initEntry" data-type="1">手动录入</div>` } 
                            </td>
                            <td>
                                <div class="${ idToday || item.incomeTaxState == 2 ? '': 'ty-color-red' }">${ item.incomeTaxState == 2 ? (new Date(item.dateIncomeTax).format("MM-dd")): (item.incomeTaxState == 1 ? '需继续录入' : '尚无数据') }</div>
                               ${ item.incomeTaxState == 2 || item.incomeTaxState == 1 ? `<div class="jumpBtn" data-fun="scan" data-type="2">查看</div>` : `<div class="jumpBtn" data-fun="initEntry" data-type="2">手动录入</div>` } 
                            </td>
                            <td>
                                <div class="${ idToday || item.socialSecurityState == 2 ? '': 'ty-color-red' }">${ item.socialSecurityState == 2 ? (new Date(item.dateSocialSecurity).format("MM-dd")): (item.socialSecurityState == 1 ? '需继续录入' : '尚无数据') }</div>
                                ${ item.socialSecurityState == 2 || item.socialSecurityState == 1 ? `<div class="jumpBtn" data-fun="scan" data-type="3">查看</div>` : `<div class="jumpBtn" data-fun="initEntry" data-type="3">手动录入</div>` } 
                            </td>
                            <td>
                                <div class="${ idToday || item.accumulationFundState == 2 ? '': 'ty-color-red' }">${  item.accumulationFundState == 2 ?(new Date(item.dateAccumulation).format("MM-dd")): (item.accumulationFundState == 1 ? '需继续录入' : '尚无数据') }</div>
                                ${ item.accumulationFundState == 2 || item.accumulationFundState == 1 ? `<div class="jumpBtn" data-fun="scan" data-type="4">查看</div>` : `<div class="jumpBtn" data-fun="initEntry" data-type="4">手动录入</div>` } 
                            </td>
                        </tr>`
            })
            $(".mainList tbody").html(str);
        }
    })
}
// update: hxz，2022-05-5 09:33:46，获取录入页 职工列表
function getEnteryData(month) {
    $.ajax({
        "url":"../salary/getUsers.do",
        // "url":"../salary/salaryByMonth.do",
        "data": {
            "month": month,
            "isDuty": 1,
        },
        success:function(res) {
            let list = res.data && res.data.users || [];
            let html = ``;
            for(var y=0;y<list.length; y++){
                html +=
                    `<tr data-id="${list[y].userID}" data-duty="1">
                            <td>${list[y].userName}</td>
                            <td>${list[y].mobile}</td>
                            <td>${handleNull(list[y].departName)} <br/>${handleNull(list[y].postName)} </td>
                            <td><input placeholder="请录入金额" class="form-control" onkeyup="clearNoNumN(this,2)"/></td>
                        </tr>`;
            }
            $("#staffList tbody tr:not(:eq(0))").remove();
            $("#staffList tbody").append(html);
        }
    })
}
// creator: hxz，2022-05-05 16:31:57 获取录入页 支付方式 的 现金账户和银行账户
function getAccountList(obj, selectID) {
    $.ajax({
        "url":"../account/getAccountKinds.do",
        "data":{ 'accountStatus': 1 },
        success:function (res) {
            let list = res.data && res.data.financeAccounts || [] ;
            let str = `<option value="0">请选择</option>`
            list.forEach(function (item) {
                let isSelect = ''
                if(selectID == item.id ){
                    isSelect = "selected"
                }
                if ( item.accountType == 1 ){
                    str += `<option value="1-${ item.id }" ${ isSelect }>现金支付</option>`
                }else{
                    let bankNameStr = item["name"] + ' ' + formatAccount(item["account"])+ ' ' + item["bankName"] ;
                    if(item.isPublic == 1){
                        bankNameStr = formatAccount(item["account"])+ ' ' + item["bankName"] ;
                    }
                    str += `<option ${ isSelect } value="2-${ item.id }">${ `转账支付 - ${ bankNameStr }` }</option>`
                }
            })

            obj.html(str)
        }
    })
}
// create:hxz 2022-03-07 格式化 账号
function formatAccount(account){
    let accountStr = `****`
    if(account.length >3){
        accountStr += account.substr(account.length-4,4)
    }else{
        let n = 4-account.length ;
        for(let i = 0 ; i < n; n++){
            accountStr += '*'
        }
        accountStr += account
    }
    return accountStr
}
// creator: 李玉婷，2021-06-10 16:31:57，保存
function saveSalary(){
    let emptyNum = 0;
    $("#staffList input").each(function(){
        if($(this).val() == ""){ emptyNum++; }
    });
    if (emptyNum > 0){
        layer.msg('还有金额未录入!');
        return false;
    }
    if( $("#date_input").val().length === 0 ){
        layer.msg(`请输入${ $("#date_span").html() }`);
        return false;
    }
    if( $("#method_input").val() == 0 ){
        layer.msg(`请输入${ $("#method_span").html() }`);
        return false;
    }
    if( $("#amount_input").val().length === 0 ){
        layer.msg(`请输入${ $("#amount_span").html() }`);
        return false;
    }

    let item_total = 0;
    let type = $(".mainCon2").data("type");
    let totalAmount = $("#amount_input").val() == "" ? 0: Number($("#amount_input").val());
    $("#staffList input").each(function(){
        if ($(this).val() != ""){
            item_total += Number($(this).val());
        }
    })
    if(totalAmount != item_total) {
        let tip = ``;
        if (type == 1) {
            tip = `<p>每位职工实发金额的和，与本月实发工资总额不相等。</p>`;
        } else if (type == 2) {
            tip = `<p>每位职工个人所得税的和，与本月职工应缴个人所得税总额不相等。</p>`;
        } else if (type == 3) {
            tip = `<p>每位职工应缴社保的和，与本月职工应缴社保总额不相等。</p>`;
        } else if (type == 4){
            tip = `<p>每位职工公积金的和，与本月职工应缴公积金总额不相等。</p>`;
        }
        tip = tip + `<p>请检查后修正！</p>`;
        $("#alertTip .tip").html(tip);
        bounce_Fixed.show($("#alertTip"));
        return false;
    }
    let staffList = [];
    $("#staffList input").each(function(){
        if ($(this).val() != ""){
            var json = {
                "userId": $(this).parents("tr").data("id"),
                "isduty": $(this).parents("tr").data("duty"),
                "factPay": $(this).val()
            }
            staffList.push(json);
        }
    })
    let method_input = $("#method_input").val();
    let date_input = $("#date_input").val();
    method_input = method_input.split('-')
    let payWay = method_input[0];
    let accountId = method_input[1];
    let period = $(".mainCon2").data("period").replace("-",'')
    $.ajax({
        // "url":"../salary/addMonthSalary.do",
        "url":"../salary/manualEntry.do",
        "data": {
            "type": type,
            "payTime": date_input,
            "payWay": payWay,
            "accountId": accountId,
            "pay": totalAmount,
            "period": period ,
            "userList": JSON.stringify(staffList)
        },
        success:function(res) {
            let status = res.data && res.data.status
            if(status == 2){
                layer.msg("此账户已被冻结，不允许操作")
                return false
            }else if(status == 3){
                layer.msg("该账户余额不足")
                return false
            }else {
                let month = $(".mainCon2").data("period")
                let monthStr = month.replace("-",'年') + '月'
                let tip = ''
                if (type == 1) {
                    tip = monthStr + "工资表 已保存成功！"
                } else if (type == 2) {
                    tip = `${ monthStr }个人所得税明细表已保存成功！`;
                } else if (type == 3) {
                    tip = `${ monthStr }应缴社保明细表已保存成功！`;
                } else if (type == 4){
                    tip = `${ monthStr }应缴公积金明细表已保存成功！`;
                }
                layer.msg(tip);
                showMainCon(1);
                if ($("#indexBack:visible").length > 0) {
                    searchKeySure();
                } else {
                    getMainData();
                }
            }

        }
    })
}
// update: hxz，2022-05-05 09:45:50，查看 某月的工资/个人所得税/社保/公积金
function scanItemInfo(month,type,editType) {
    month = month.replace("-","");
    $.ajax({
        "url":"../salary/getPersonnelPayMonth.do",
        "data": { "type": type, "period": month },
        success:function(res) {
            let data = res.data ;
            if(Number(editType) > 0){ // 修改的
                $(".updateSalary").data('data', data)

                if(editType == 1){
                    showMainCon(5)
                    renderShowTb(data, editType)
                }
                else if(editType == 2){
                    showMainCon(4)
                    renderSelectTb(data)
                }
            }
            else{ // 查看详情的
                showMainCon(31)
                let list = []
                if(type == 1){
                    $("#salar").html(JSON.stringify( { "type": type, "period": month } ))
                    $(".mainCon3 .type1").show(); $(".mainCon3 .type2").hide();
                    $("#scanSalaryList tr:gt(0)").remove()
                    list = data && data.personnelPayMonth || [];
                    let str = ``;
                    list.forEach((item)=>{
                        let bankNameStr = ``
                        item.period = month
                        item.type = 1
                        if(item.payWay == 2){
                            bankNameStr = item["name"] + ' ' + formatAccount(item["account"])+ ' ' + item["bankName"] ;
                            if(item.isPublic == 1){
                                bankNameStr = formatAccount(item["account"])+ ' ' + item["bankName"] ;
                            }
                        }
                        str += `
                            <tr>
                                <td>${ new Date(item.payTime ).format("yyyy-MM-dd")}</td>
                                <td>${ item.payWay == 1 ? '现金' : '转账支付-' } ${ bankNameStr } </td>
                                <td>${ item.pay.toFixed(2) } </td>
                                <td>${ item.userNum } 人</td>
                                <td>${item.createName } ${ new Date(item.createDate ).format("yyyy-MM-dd hh:mm:ss")}</td>
                                <td>
                                    <span data-fun="salaryScan" class="jumpBtn ty-color-blue">查看</span>
                                    <span data-fun="itemUpdate" class="jumpBtn ty-color-blue">修改</span>
                                    <span data-fun="itemUpdateLog" class="jumpBtn ty-color-blue">修改记录</span>
                                    <span class="hd">${ JSON.stringify(item) }</span>
                                </td> 
                            </tr>
                    `
                    });
                    $("#scanSalaryList").append(str);
                }else if(type == 2 || type == 3 || type == 4){
                    $(".mainCon3 .type2").show(); $(".mainCon3 .type1").hide();
                    showMainCon(3)
                    $(".mainCon3 .flexbox .jumpBtn").show();
                    $("#scanSalary tr:gt(0)").remove()
                    list = data.mapList || [];
                    let info = data.personnelPay ;
                    let financeAccount = data.financeAccount
                    let methodStr = ``;
                    if(info.payWay == 1){
                        methodStr = `现金支付`
                    }else{
                        methodStr = `转账支付-`
                        let bankNameStr = financeAccount["name"] + ' ' + formatAccount(financeAccount["account"])+ ' ' + financeAccount["bankName"] ;
                        if(financeAccount.isPublic == 1){
                            bankNameStr = formatAccount(financeAccount["account"])+ ' ' + financeAccount["bankName"] ;
                        }
                        methodStr += bankNameStr
                    }

                    $(".mainCon3 .date_i").html(new Date(info.payTime).format("yyyy-MM-dd"));
                    $(".mainCon3 .number_i").html(list.length + '人');
                    $(".mainCon3 .method_i").html(methodStr);
                    $(".mainCon3 .amount_i").html(data.pay.toFixed(2));
                    $(".mainCon3 .createD").html( info.createName + ' ' + new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss"));
                    let str2 = ``;
                    list.forEach((item)=>{
                        str2 += `
                            <tr>
                                <td>${ item.userName }</td>
                                <td>${ item.mobile }</td>
                                <td>${ item.departName || '' } ${ item.postName && item.departName ? '/':'' } ${ item.postName || '' }</td>
                                <td>${ item.factPay.toFixed(2) }</td>
                            </tr>
                    `
                    })
                    $("#scanSalary").append(str2)
                    data.type = type
                    data.month = month
                    $("#itemInfo").html(JSON.stringify(data));
                    $(".mainCon3").data('salary',data);
                }

            }

        }
    })
}
// creator: 李玉婷，2021-06-11 15:12:41，修改页面
function editSalary() {
    let data = $(".mainCon3").data('salary');
    let list = data.personnelSalaryList;
    let html = ``, type = data.type;
    let catStr = changeType(type);
    $(".mainCon4 .placeTitle").html(catStr);
    if (list && list.length >0) {
        let salary = ``;
        for(var y=0;y<list.length; y++){
            if (type == 1) {
                salary = list[y].factSalary
            } else  if (type == 2) {
                salary = list[y].incomeTax
            } if (type == 3) {
                salary = list[y].insurancePremium
            } if (type == 4) {
                salary = list[y].accumulationFund
            }
            html +=
                `<tr data-info='${JSON.stringify(list[y])}'>
                    <td class="toggle" data-type="single"><i class="fa fa-square-o"></i></td>
                    <td>${list[y].userName}</td>
                    <td>${list[y].phone}</td>
                    <td>${handleNull(list[y].departName)} <br/>${handleNull(list[y].postName)}</td>
                    <td>${salary.toFixed(2)}</td>
                </tr>`;
        }
    }

    $(".checkStaff tbody tr:not(:eq(0))").remove();
    $(".checkStaff tbody").append(html);
    $("#editType").html(changeSubType(type));
    $(".mainCon5").data('salary',{'month':data.month,'type': data.type});
}
// update: 侯杏哲，2022-05-16 17:28:19，下一步
function editSalaryNext() {
    let len = $(".checkStaff .fa-check-square-o").length;
    if (len <= 0) {
        layer.msg(`请先选择需要修改的职工！`);
    } else {
        showMainCon(5);
        $("#updateTotalAmount").val("");
        $(".updateSalary tr:gt(0)").remove()
        let selectSum = 0
        $(".checkStaff tbody tr:gt(0)").each(function(){
            let faObj = $(this).find(".fa")
            let checked = faObj.hasClass("fa-check-square-o")
            let item = JSON.parse( faObj.siblings(".hd").html() );
            let str = `
                <tr data-id="${ item.payId }" style="${ checked ? '' : 'display:none;' }">
                    <td>${ item.userName }</td>
                    <td>${ item.mobile }</td>
                    <td>${ item.departName || '' }  ${ item.postName && item.departName ? '/' : '' } ${ item.postName || '' }</td>
                    <td><input placeholder="请录入金额" type="text" class="form-control" value="${ item.factPay.toFixed(2) }" onkeyup="clearNoNumN(this,2)"/></td>
                </tr>
                `
            $(".updateSalary tbody").append(str)
            selectSum += Number(item.factPay);
        });
        $("#updateTotalAmount").val(selectSum.toFixed(2));
    }
}
// creator: 李玉婷，2021-06-11 19:16:15，修改确定
function saveUpdateSalary(){
    let emptyNum = 0;
    $(".mainCon5 input").each(function(){
        if($(this).val() == ""){ emptyNum++; }
    });
    if (emptyNum > 0){
        layer.msg('还有金额未录入!');
        return false;
    }
    let notWages = $("#updateTotalAmount").siblings(".hd").html();
    let item_total = 0;
    let salary = $(".mainCon5").data('salary');
    let totalAmount = $("#updateTotalAmount").val() == "" ? 0: Number($("#updateTotalAmount").val());
    $(".updateSalary input").each(function(){
        if ($(this).val() != ""){
            item_total += Number($(this).val());
        }
    })
    item_total += Number(notWages);
    if(totalAmount != item_total) {
        let tip = ``;
        if (salary.type == 1) {
            tip = `<p>每位职工实发金额的和，与本月实发工资总额不相等。</p>`;
        } else if (salary.type == 2) {
            tip = `<p>每位职工个人所得税的和，与本月职工应缴个人所得税总额不相等。</p>`;
        } else if (salary.type == 3) {
            tip = `<p>每位职工应缴社保的和，与本月职工应缴社保总额不相等。</p>`;
        } else if (salary.type == 4){
            tip = `<p>每位职工公积金的和，与本月职工应缴公积金总额不相等。</p>`;
        }
        tip = tip + `<p>请检查后修正！</p>`;
        $("#alertTip .tip").html(tip);
        bounce_Fixed.show($("#alertTip"));
        return false;
    }
    let param = {type: salary.type}, list = [];
    $(".updateSalary input").each(function(){
        let json = {
            id: $(this).parents("tr").data("id"),
            salary: $(this).val()
        }
        list.push(json);
    });
    param.list = JSON.stringify(list);
    $.ajax({
        "url":"../salary/updateMonthSalary.do",
        "data": param,
        success:function(res) {
            let msg = $(".mainCon3 .timeSect").html() + $(".mainCon3 .tbType").html()+ $(".mainCon3 .tbType2").html() +'表';
            layer.msg(msg + "已修改成功！");
            showMainCon(3);
            scanItemInfo(salary.month,salary.type);
        },
        complete:function(){ }
    })
}
// creator: 李玉婷，2021-06-11 19:17:02，修改
function itemUpdateLog2(obj){
    let item = JSON.parse($("#itemInfo").html()) ;
    itemUpdateLogFun(item)
}
// creator: 李玉婷，2021-06-11 19:17:02，修改
function itemUpdateLog(obj) {
    let item = obj.siblings(".hd").html();
    item = JSON.parse(item)
    itemUpdateLogFun(item)
}
// creator: 李玉婷，2021-06-11 19:17:02，修改记录
function itemUpdateLogFun(item){
    let info = item.personnelPay
    let versionNo = item.versionNo || info.versionNo // 工资的 ｜｜ 其他的
    $.ajax({
        "url":"../salary/getRecords.do",
        "data": {
            "versionNo":versionNo,
            "period": item.period,
            "type": item.type,
        },
        success:function(res) {
            let data = res.data ;
            if(!data){
                layer.msg("链接错误！")
                return false;
            }
            bounce.show($("#editRecord"));
            let list = data.personnelPayHistories || []
            if(list.length > 1){
                $("#editRecord .noRecord").hide();
                $("#editRecord .hasRecord").show();
                let html = ``
                let last = list[list.length - 1]
                list.forEach(function (logItem,index) {
                    html +=
                        `<tr>
                        <td>${ logItem.subVersionNo == 0 ? `原始信息` : `第${logItem.subVersionNo}次修改后`} </td>
                        <td class="ty-td-control">
                            <span class="ty-color-blue" onclick="recordsScan($(this))">查看</span>
                            <span class="hd">${JSON.stringify(logItem)}</span>
                        </td>
                        <td>${handleNull(logItem.createName)} &nbsp; ${new Date(logItem.createDate).format('yyyy-MM-dd hh:mm:ss')}</td>
                    </tr>`;
                })
                $("#recordList tbody tr:not(:eq(0))").remove();
                $("#recordList tbody").append(html);
                $("#editRecord .hasRecord .createInfo").html(`${ last.createName } ${ new Date(last.createDate).format("yyyy-MM-dd hh:mm:ss") }` );

            }else {
                info = list[0] || item ;
                $("#editRecord .noRecord").show();
                $("#editRecord .hasRecord").hide();
                $("#editRecord .noRecord .createInfo").html(`${ info.createName } ${ new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss") }` );
            }
        }
    })
}
// update: hxz，2022-05-11 18:54:09，历史记录查看
function recordsScan(obj){
    let trItem = obj.siblings(".hd").html();
    trItem = JSON.parse(trItem);
    let type = trItem.type;
    let catStr = changeType(type);
    $("#editRecordScan .placeTitle").html(catStr);
    let ttl = $(".mainCon3 h3").html();
    $("#editRecordScan .kindTtl").html(ttl);
    if(type == 1){
        $("#editRecordScan .datettl").html('发放日期 ')
        $("#editRecordScan .methodttl").html('发放方式 ')
    }else{
        $("#editRecordScan .datettl").html('支出日期 ')
        $("#editRecordScan .methodttl").html('支出方式 ')
    }
    $.ajax({
        "url":"../salary/getRecordDetails.do",
        "data": {
            "period": trItem.period,
            "type": type,
            "subVersionNo": trItem.subVersionNo,
            "versionNo": trItem.versionNo
        },
        success:function(res) {
            let data = res.data ;
            let curMsg = data.personnelPayHistories;
            let oldMsg = data.personnelPayHistoriesOld;
            let list = curMsg.mapList || [] ;
            let oldlist = oldMsg.mapList || [] ;
            let financeAccount = curMsg.financeAccount ;
            let financeAccountOld = oldMsg.financeAccount ;

            let dateStr = new Date(curMsg.payTime).format("yyyy-MM-dd") ;
            let dateStrOld = new Date(oldMsg.payTime).format("yyyy-MM-dd") ;
            if(Number(trItem.subVersionNo) > 0 && dateStr != dateStrOld){
                dateStr = `<span class="ty-color-red">${ dateStr }</span>`
            }
            $("#editRecordScan .dateVal").html(dateStr)
            let methodChanged = false // 没改
            if(curMsg.payWay == oldMsg.payWay ){
                if(curMsg.payWay == 2 && financeAccount.id != financeAccountOld.id){
                    methodChanged = true
                }
            }else{
                methodChanged = true
            }
            let bankNameStr = ``
            if(curMsg.payWay == 2){
                bankNameStr = '转账支付-' +financeAccount["name"] + ' ' + formatAccount(financeAccount["account"])+ ' ' + financeAccount["bankName"] ;
                if(financeAccount.isPublic == 1){
                    bankNameStr = '转账支付-' + formatAccount(financeAccount["account"])+ ' ' + financeAccount["bankName"] ;
                }
            }else{
                bankNameStr = `现金支付`
            }
            if(Number(trItem.subVersionNo) > 0 && methodChanged){
                bankNameStr = `<span class="ty-color-red">${ bankNameStr }</span>`

            }
            $("#editRecordScan .methodVal").html(bankNameStr)
            let numStr = list.length == oldlist.length ? `` :` class='ty-color-red' `
            let amountStr = curMsg.pay == oldMsg.pay ? `` : ` class='ty-color-red' `
            if(Number(trItem.subVersionNo) == 0 ){
                numStr = ''
                amountStr = ''
            }
            if (type == 1) {
                $("#editRecordScan .scanCount").html(`共<span ${ numStr }>${list.length}</span>人开工资，总额<span ${ amountStr }>${curMsg.pay.toFixed(2)}</span>元`);
            } else {
                $("#editRecordScan .scanCount").html(`共<span ${ numStr }>${list.length}</span>人，共<span ${ amountStr }>${curMsg.pay.toFixed(2)}</span>元`);
            }
            let html = `` ;
            list.forEach(function (item) {
                let isChanged = isChangedFun(item, oldlist, trItem.subVersionNo)
                // if(Number(trItem.subVersionNo) == 0 ){
                //     isChanged = false
                // }
                html +=
                    `<tr>
                        <td>${item.userName}</td>
                        <td>${item.mobile}</td>
                        <td>${item.departName || ''} <br/>${ item.postName || '' } </td>
                        <td ${isChanged ? `class='red'` : ``}>${ item.factPay.toFixed(2) }</td>
                    </tr>`;
            })
            $("#recordScanSalary tbody tr:gt(0)").remove();
            $("#recordScanSalary tbody").append(html);
            if (trItem.subVersionNo == 0) {
                $("#editRecordScan .scanCreate").html('创建人：&nbsp;&nbsp;' + curMsg.createName + '&nbsp;&nbsp;' +new Date(curMsg.createDate).format('yyyy-MM-dd hh:mm:ss'));
            } else {
                $("#editRecordScan .scanCreate").html('修改人：&nbsp;&nbsp;' + curMsg.createName + '&nbsp;&nbsp;' +new Date(curMsg.createDate).format('yyyy-MM-dd hh:mm:ss'));
            }
            bounce_Fixed.show($("#editRecordScan"));
        }
    })
}
function isChangedFun(info, list, subVersionNo) {
    if(subVersionNo == 0){
        return false
    }
    list = list || []
    let same = false ; // 默认没修改
    let isHave = false
    list.forEach((item)=>{
        if(item.userId == info.userId){
            isHave = true
            if(info.factPay != item.factPay){
                same = true // 修改了
            }
        }
    })
    if(!isHave){
        same = true // 修改了
    }
    return same
}
// creator: 李玉婷，2021-06-10 14:23:50，转换字符串
function changeType(num) {
    var str = '';
    switch (num) {
        case 1:
        case '1':
            str = '实发';
            break;
        case 2:
        case '2':
            str = '个人所得税';
            break;
        case 3:
        case '3':
            str = '社保';
            break;
        case 4:
        case '4':
            str = '公积金';
            break;
    }
    return str;
}
function changeSubType(num) {
    var str = '';
    switch (num) {
        case 1:
        case '1':
            str = '工资';
            break;
        case 2:
        case '2':
            str = '个人所得税';
            break;
        case 3:
        case '3':
            str = '社保';
            break;
        case 4:
        case '4':
            str = '公积金';
            break;
    }
    return str;
}

laydate.render({elem: '#date_input'});
laydate.render({elem: '#date_input1'});









