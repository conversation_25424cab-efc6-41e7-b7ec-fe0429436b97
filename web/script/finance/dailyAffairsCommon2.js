// creator: 张旭博，2019-07-15 09:32:19，初始化三级和四级弹窗
var bounce_Fixed2 = new Bounce(".bounce_Fixed2");
var bounce_Fixed3 = new Bounce(".bounce_Fixed3");
bounce_Fixed2.show($("#goodEntry"));
bounce_Fixed3.show($("#tip"));
bounce_Fixed2.cancel();
bounce_Fixed3.cancel();
var uploadCode = "" // 标记每次添加的代号
$(function () {

    $(".handleBtn").on("click", '.ty-form-checkbox', function () {
        if ($(this).hasClass("ty-form-checked")){
            $(this).removeClass("ty-form-checked")
            $("#form_billEntry").hide()
            document.getElementById('form_billEntry').reset()
        } else {
            $(this).addClass("ty-form-checked")
            $("#form_billEntry").show()
            setAmount()
        }
    })

    $(".bounce").on("click", '[type="btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            // 票据录入
            case 'invoiceUpload':
                initInvoiceUp()
                break
            case 'goodEntry':
                moveGoodEntry("600px");
                $(".ty-nextTip").hide();
                $(".VATInvoice").hide().siblings().hide();
                $('#form_goodEntry')[0].reset()
                $("#goodEntry .textMax").html("0/30");
                var firstLoadBill = $("#reimburse tbody tr").eq(0)
                if (firstLoadBill.length !== 0) {
                    var billCatName = firstLoadBill.children().eq(0).html() // 已经录入的票据类型
                    if (billCatName === '收据') {
                        $("#form_goodEntry [name='billCat'] option").eq(5).prop('selected', true)
                    } else {
                        $("#form_goodEntry [name='billCat']").val('')
                    }
                }
                $(".secondFeeCat").hide()
                bounce_Fixed2.show($("#goodEntry"))
                $("#goodEntry").data('type', 'new')
                setEveryTime(bounce_Fixed2, 'goodEntry')
                $("#onlyOneRow").val("");
                $("#goodEntry").find("i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
                $("#goodEntry").data('type', 'new')
                initUpload()
                $('#form_billEntry').data("qrInfo", '')
                break;
            // 报销申请-确定
            case 'reimburse':
                sureReimburse($(this))
                break;
            // 报销申请 - 修改
            case 'changeBill':
                changeBill($(this))
                break;
            // 报销申请 - 删除
            case 'delBill':
                var billInfo = $(this).parent().siblings(".billInfo").html();
                var data = JSON.parse(billInfo);
                var bills = data.bills
                bills.forEach(function(bill){
                    let fileArr = bill.fileArr
                    if(fileArr.length > 0){
                        let fileid = fileArr[0]
                        let op = {'type':'fileId', 'fileId':fileid }
                        cancelFileDel(op, true);
                    }
                })
                $(this).parents('tr').remove()
                break;
        }
    })
    $(".bounce_Fixed").on("click", '[type="btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            // 票据信息 - 获取列表 - 修改
            case 'changeGood':
                // 初始化
                document.getElementById('form_goodEntry').reset()
                bounce_Fixed2.show($("#goodEntry"))
                var data = $("#billEntry").data('state')
                $("#goodEntry [name='billCat']").val(data.billCat)
                $("#goodEntry .issueDate").val(data.issueDate)
                $("#goodEntry .billNo").val(data.billNo)
                // 开启验证
                setEveryTime(bounce_Fixed2, 'goodEntry')
                // 获取数据
                var goodInfo = $(this).parents('td').prev('.goodInfo').html()
                goodInfo = JSON.parse(goodInfo)
                // 赋值各个参数
                for(var key in goodInfo){
                    $("#goodEntry [name='"+key+"']").val(goodInfo[key])
                }
                setFeeCat(goodInfo)

                // 赋值代表是修改用的弹窗
                $("#goodEntry").data('type', 'change')
                $("#goodEntry").data('obj', $(this))
                $("#onlyOneRow").val(0);

                break;
            // 票据信息 - 获取列表 - 删除
            case 'delGood':
                if ($(this).parents('tbody').find('tr').length === 1) {
                    var type = $("#billEntry").data('type')
                    var obj = $("#billEntry").data('obj')
                    bounce_Fixed.cancel()
                    if (type === 'change') {
                        obj.parents("tr").remove()
                    }
                }else {
                    $(this).parents("tr").remove()
                }
                break;
            // 票据信息 - 确认
            case 'billEntry':
                sureBillEntry($(this))
                break;
            // 票据信息- 录入下一种货物
            case 'goodEntryNext':
                document.getElementById('form_goodEntry').reset()
                $(".secondFeeCat").hide()
                bounce_Fixed2.show($("#goodEntry"))
                setEveryTime(bounce_Fixed2, 'goodEntry')
                // 初始化录入完毕下面内容
                $(".handleBtn .ty-form-checkbox").removeClass("ty-form-checked")
                $("#form_billEntry").hide()
                document.getElementById('form_billEntry').reset()
                $("#goodEntry").data('type', 'newNext')
                $(".ty-nextTip").hide();
                $("#billCat").attr("disabled","disabled");
                var data = $("#billEntry").data('state')
                $("#goodEntry [name='billCat']").val(data.billCat)
                $("#goodEntry .issueDate").val(data.issueDate)
                $("#goodEntry .billNo").val(data.billNo)
                $("#goodEntry .billNo").val(data.billNo)
                $("#goodEntry .billMemo").val(data.memo)
                break
        }
    })
    $(".bounce_Fixed2").on("click", '[type="btn"]', function (e) {
        var name = $(this).data('name')
        switch (name) {
            /* ------------- 货物录入弹窗 --------------- */
            // 货物录入确认
            case 'sureGoodEntry':
                sureGoodEntry($(this))
                break;
            // 获取录入 -新增行 （定额发票）
            case 'iconOneRow':
                var val_ = $(this).data('val');
                $("#onlyOneRow").val(val_);
                $(this).find("i.fa").attr("class", "fa fa-dot-circle-o");
                $(this).siblings().find("i.fa").attr("class", "fa fa-circle-o");
                if(val_ == '1'){
                    var billCatName = $("#billCat").find("option:selected").html();
                    $(".in4").html(billCatName);
                    if( billCatName === '增值税普通发票'){
                        $(".in2").show();
                        $(".in3").show();
                        $(".in1").hide();
                    }else{
                        $(".in2").hide();
                        $(".in3").hide();
                        $(".in1").show();

                    }
                    $(".oneRowTab").children("tbody").html("") ;
                    inputNextTr(1);
                    $("#summary").html("您已录入"+ billCatName +"共 0 张，发票金额总计 0 元，实际支出（即您将报销）总计 0 元。");
                }
                break;
            // 录入下一张只有一行内容的增值税普通发票
            case 'inputNextBtn':
                var isGray = $(this).hasClass("ty-btn-gray");
                if(!isGray){
                    inputNextTr(1);
                }
                break;
            // 单行增普上传图片
            case 'uploadImg':
                var deleObj = $(this) ;
                uploadCode = 'f'+ Math.random().toString(36).substr(2);
                var itemTemp =
                    '<div id="${fileID}" title="${fileName}" size="${size}" code="${uploadCode}" class="upItem uploadify-queue-item" onmouseout=\'hideIMG($(this))\' onmouseover=\'showIMGInfo($(this))\'>' +
                    '<i class="hd up_delBtn fa fa-close" onclick="deleteFile($(this))"></i>' +
                    '<span class="hd up_fileType">${fileType}</span>' +
                    '<span class="up_name">${fileName}</span>' +
                    '<span class="uploadify-progress"><div class="uploadify-progress-bar"></div></span>' +
                    '</div>';
                $(this).siblings(".img").Huploadify({
                    auto: true,
                    fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;*.pdf;',
                    multi: true,
                    buttonText: "",
                    buttonNumber: 0,
                    formData: {
                        module: '日常事务',
                        userId: sphdSocket.user.userID
                    },
                    fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
                    showUploadedPercent: true,
                    showUploadedSize: true,
                    removeTimeout: 99999999,
                    itemTemplate:itemTemp ,
                    uploader: $.webRoot + "/uploads/uploadfyByFile.do",
                    onUploadStart: function () {
                        deleObj.hide();
                        deleObj.siblings(".img").show();
                    },
                    onUploadComplete: function (file, data) {
                    },
                    onUploadError: function (file) {
                        layer.msg('文件上传出错！');
                        console.log('出错文件：', file)
                    },
                    onUploadSuccess: function (file, data) {
                        data = JSON.parse(data);
                        // data = result.data;
                        var fileUid =data.fileUid;
                        var filePath = $.fileUrl + data.filename;
                        var fileName = file.name;
                        var size = file.size;
                        $(".upItem").each(function(){
                            var txt = $(this).attr("title");
                            var s = $(this).attr("size");
                            var co = $(this).attr("code");
                            if(fileName == txt && size == s && uploadCode == co){ // 名称/大小/批次 一样就默认是一个,即：每次不能穿两个名字、类型、大小完全相同的文件
                                var imgStr = "<img data-fileid='"+ fileUid +"' data-imgpath='"+ data.filename +"' src='"+ filePath +"' data-filename='"+ fileName +"' />" ;
                                $(this).html(imgStr)
                            }
                        });
                    }
                });
                $(this).siblings(".img").find(".uploadify-button").click();
                break;
            // 单行增普删除附件
            case 'delImg':
                window.tipDelObj = { "obj":$(this), "type":"img"  } ;
                $(".deltip").html("确定要删除该附件 ?");
                bounce_Fixed3.show($("#tipDelTr"));
                break;
            // 清空本行
            case 'reset':
                var tr = $(this).parents("tr");
                tr.children(":eq(0)").children(".FeeCats").html("选择类别");
                tr.children(":eq(9)").children(".memoAcronym").html("");
                tr.children(":eq(0)").children(".hd").html("");
                tr.children(":eq(9)").children(".hd").html("");
                tr.find("input").val("");
                break;
            // 删除本行
            case 'clearTr':
                window.tipDelObj = { "obj":$(this), "type":"tr"  } ;
                $(".deltip").html("确定要删除该行 ?");
                bounce_Fixed3.show($("#tipDelTr"));
                break;
            case 'newRow':
                var isGray = $(this).hasClass("ty-color-gray");
                if(!isGray){
                    inputNextTr(2);
                }
                break;
            // 获取录入 -删除行 （定额发票）
            case 'delRow':
                $(this).parents("tr").remove()
        }
    })
    $("#form_billEntry").on("input", "[name='number']",  function () {
        var num = $(this).val()
        var billNo = $("#billEntry").data('state').billNo
        if(num>1 && billNo !== '' ){
            var str = ''
            for(var i = 0;i<num-1;i++){
                str += '<input type="text" class="billNoOther" require>'
            }
            $(".repeatBill").show()
            $(".repeatCon").html(str)
        }else {
            $(".repeatBill").hide()
            $(".repeatCon").html('')
        }
    })

    $(".firstFeeCat select").on('input', function () {
        var feeCatName = $(this).find('option:selected').html()
        var feeCat = $(this).val()
        if(feeCatName === '交通费' || feeCatName === '车务支出') {
            $(".secondFeeCat").show()
            getSecondFeeCat(feeCat, function (err, data) {
                var str = '<option value="">---请选择二级费用类别---</option>'
                for (var i in data) {
                    if (Number(data[i].enabled) === 1) {
                        str += '<option value="' + data[i].id + '">' + data[i].name + '</option>'
                    }
                }
                $(".secondFeeCat select").html(str)
            })
        }else {
            $(".secondFeeCat").hide()
        }
    })

    $("#billCat").on("change", function () {
        $(".kindInvoice [name]").val('')
        $(".kindInvoice .cp_imgShow").html('')
        $(".addRow").remove()
        var firstLoadBill = $("#reimburse tbody tr").eq(0)
        var thisBill = $(this).find("option:selected").html(), isSelect = true;
        $("#onlyOneRow").val("")
        $(".ty-nextTip").find(".fa").attr("class", "fa fa-circle-o");
        if (firstLoadBill.length !== 0) {
            var billCatName = firstLoadBill.children().eq(0).html() // 已经录入的票据类型
            if (billCatName === '收据') {
                if (thisBill !== '收据') {
                    $("#tip .tip").html('您刚才已录入收据，发票需另外提出申请！');
                    bounce_Fixed3.show($("#tip"));
                    $(this).val('');
                    isSelect = false;
                }
            } else {
                if (thisBill === '收据') {
                    $("#tip .tip").html('您刚才已录入发票，收据需另外提出申请！');
                    bounce_Fixed3.show($("#tip"))
                    $(this).val('')
                    isSelect = false;
                }
            }
        }
        if(isSelect){
            if(thisBill === "收据" || thisBill === "其他普通发票"){
                $(".in2").show();$(".in1").hide();$(".in3").show();
            }else if(thisBill === "增值税普通发票"){
                $(".in1").show();$(".in2").hide();$(".in3").hide();
            }

            if (billCatName === '收据') {
                $(".VATInvoice .formItem_itemName .titleName").html('票据内容')
                $(".VATInvoice .formItem_itemQuantity .titleName").html('数量')
                $(".VATInvoice .formItem_price .titleName").html('金额')
            } else if (billCatName === '其他普通发票') {
                $(".VATInvoice .formItem_itemName .titleName").html('票据内容')
                $(".VATInvoice .formItem_itemQuantity .titleName").html('数量')
                $(".VATInvoice .formItem_price .titleName").html('金额')
            } else {
                $(".VATInvoice .formItem_itemName .titleName").html('货物或应税劳务、服务名称')
                $(".VATInvoice .formItem_itemQuantity .titleName").html('数量')
                $(".VATInvoice .formItem_price .titleName").html('金额')
            }
        }

        if (thisBill === '定额普通发票'|| thisBill ==='定额发票') {
            $(".quotaInvoice tbody").html("");
            $(".colNumTotal").html("0");
            $(".colPriceTotal").html("0");
            $(".colAmountTotal").html("0");
            inputNextTr(2);
        }


    });
});

// 验证码
function createCode(yzmInfo){
    $("#inputCode").val("")  ;
    $("#color_hint").html(yzmInfo.color_hint)  ;
    let str = `<img src="data:image/png;base64,${yzmInfo.key1}" />`
    $("#code").html(str)

}
//creator：侯杏哲 2022-02-21 校验验证码, 读取 发票信息
function getUploadQR() {
    let fileInfo = $("#invoiceUp").data("invoice") ;
    let isPdf = false;
    let filePath = fileInfo.filename;
    $.ajax({
        "url":"../reimburseWindow/getUploadQR.do",
        "type":"get",
        "data":{
            'filePath':filePath,
        },
        success:function(response){
            res = response.data
            if(res){
                let status = res.status;
                if(status == 0){
                    layer.msg("系统没能识别这个文件！")
                    loading.close()
                }else if(status == 1){
                    let code = res.result.data;
                    // createCode()
                    // bounce_Fixed.show($("#invoiceUpload1"));
                    $("#invoiceUpload1").data('count',1).data("qr",code).data('res','')
                    getInvoiceByQR(code,1)
                }else if(status == 2){
                    // layer.msg("未识别成功！")
                    layer.msg("系统没能识别这个文件！")
                    loading.close()
                }
            }else {
                loading.close()
                layer.msg("系统没能识别这个文件！")
                // layer.msg(response.error && response.error.message) ;
            }
        },
        complete:function () {
        }

    })
}
function reFreashQR() {
    let count = $("#invoiceUpload1").data('count')
    let code = $("#invoiceUpload1").data("qr")
    getInvoiceByQR(code, count, 'refreash');
}
function validate(){
    var inputCode = $("#inputCode").val();
    if(inputCode.length <= 0) { //若输入的验证码长度为0
        layer.msg("请输入验证码！"); //则弹出请输入验证码
    }else { //输入正确时
        let qr = $("#invoiceUpload1").data("qr")
        let count = $("#invoiceUpload1").data('count')
        getInvoiceByQR(qr,count)
    }
}
function getInvoiceByQR(code, count, eventType) {
    console.log('这是第' + count + '次识别')
    let url = "../reimburseWindow/getInvoiceByQR.do"
    let sendData = { "qrInfo": code }
    if(Number(count) > 1){
        // url = '../reimburseWindow/getSecondInvoiceByQR.do'
        let info = $("#invoiceUpload1").data('count',count).data('res')
        let yzm_info = info.resultMap.data.yzm_info
        sendData = {
            'key2': yzm_info.key2 ,
            'key3': yzm_info.key3 ,
            'key6': yzm_info.key6 ,
            'nowtime': yzm_info.nowtime ,
            'yzm': $("#inputCode").val() ,
            "qrInfo": code
        }
        if(eventType === 'refreash'){
            sendData.yzm = ''
        }
    }
    $.ajax({
        "url": url ,
        "type":"get",
        "data": sendData ,
        success:function (res){
            if(res.data){
                res = res.data
                let status = res.status;
                let content = res.content;
                if(status == 1){
                    bounce_Fixed.cancel();
                    let commonInvoiceCertification = res.commonInvoiceCertification;
                    let resultMap = res.resultMap;
                    let billCats = res.billCats;
                    let qrInfo = commonInvoiceCertification.qrInfo // 扫二维码取的所有信息

                    let invInfo = commonInvoiceCertification.responseData
                    invInfo = JSON.parse(invInfo)
                    let cat = invInfo.yzm_info.fplx // 发票类型 【10：增值税电子普通发票，04：增值税普通发票，01：增值税专用发票】
                    let invoiceCat = formatCat2(cat, billCats)
                    let inv_info = invInfo.inv_info
                    let yzm_info = invInfo.yzm_info
                    let fphm = yzm_info.fphm // 发票号码
                    let date = yzm_info.date // 开票日期

                    let kprq_dzfp = inv_info.kprq_dzfp // # 开票日期
                    let xfmc_dzfp = inv_info.xfmc_dzfp // # 名称
                    let xfsbh_dzfp = inv_info.xfsbh_dzfp // #  销售方 纳税人识别号
                    let xfdzdh_dzfp = inv_info.xfdzdh_dzfp // # 销售方 地址
                    let xfyhzh_dzfp = inv_info.xfyhzh_dzfp // # 销售方  银行帐号
                    let gfmc_dzfp = inv_info.gfmc_dzfp // # 购方名称
                    let gfsbh_dzfp = inv_info.gfsbh_dzfp // # 购买方 纳税人识别号
                    let gfdzdh_dzfp = inv_info.gfdzdh_dzfp // # 购方  地址
                    let gfyhzh_dzfp = inv_info.gfyhzh_dzfp // # 购方  银行帐号
                    let jym_dzfp = inv_info.jym_dzfp // # 校验码
                    let se_dzfp = inv_info.se_dzfp // # 税额
                    let jshjxx_dzfp = inv_info.jshjxx_dzfp // # 价税合计 小写
                    let jshjdx_dzfp = inv_info.jshjxx_dzfp // # 价税合计 金额大写
                    let bz_dzfp = inv_info.bz_dzfp // # 备注
                    let sbbh_dzfp = inv_info.sbbh_dzfp // # 机器编码
                    let je_dzfp = inv_info.je_dzfp // # 金额
                    let summarys = inv_info.summarys // # 发票具体信息详情列表

                    let feeCatList = $("#selectFee").data('feestr');
                    let lastFee = feeCatList[feeCatList.length-1] // 默认处理为"其他类别"
                    feeCatList.forEach(function (fee) {
                        if(fee.name == "其他" || fee.name == "其它"){
                            lastFee = fee
                        }
                    })
                    let goodsList = inv_info.summarys || []

                    var firstLoadBill = $("#reimburse tbody tr").eq(0)
                    if (firstLoadBill.length !== 0) {
                        var billCatName = firstLoadBill.children().eq(0).html() // 已经录入的票据类型
                        if (billCatName === '收据' ) {
                            if(invoiceCat.name != '收据'){
                                layer.msg('您刚才已录入收据，发票需另外提出申请！')
                                return false
                            }
                        }
                    }
                    $("#goodEntry").data('type', 'new');

                    $("#billCat").val(invoiceCat.id);
                    let billCatName22 = invoiceCat.name
                    if(billCatName22 !== '其他普通发票' && billCatName22 !== '收据'){
                        $(".t1").hide(); $(".t2").show();
                        $("#m2").html('货物或应税劳务、服务名称');
                    }else{
                        $(".t2").hide(); $(".t1").show();
                        $("#m2").html('票据内容');
                    }
                    $("#goodEntry").find("i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
                    $("#goodEntry").data('type', 'new')
                    initUpload()

                    // 赋值识别出来的信息
                    let feeCatVal = $("#feeCat option:last-child").val();// 默认最后一项 其他
                    $("#feeCat").val(feeCatVal)
                    // 20220212
                    let issueDateStr = kprq_dzfp.substr(0,4) + '-' + kprq_dzfp.substr(4,2) + '-' + kprq_dzfp.substr(6,2)

                    goodsList.forEach(function (useGood) {
                        let good = {}
                        good.itemName = useGood.name ;
                        good.model = useGood.spec ;
                        good.unit = useGood.unit ;
                        if(!useGood.amount || !useGood.priceUnit){
                            useGood.amount = 1
                            useGood.priceUnit = useGood.priceSum
                        }
                        good.itemQuantity = parseFloat(useGood.amount ) ;
                        good.uniPrice = parseFloat(useGood.priceUnit) ;
                        good.price = parseFloat(useGood.priceSum );
                        good.taxRate = useGood.taxRate ;
                        good.taxAmount = useGood.taxSum ;
                        good.amount = Number(good.taxAmount )+ good.price;
                        good.memo = bz_dzfp ;

                        good.billCat            = invoiceCat.id
                        good.billCatName        = invoiceCat.name
                        good.feeCatName         = lastFee.name
                        good.feeCat             = lastFee.id
                        good.secondFeeCatName   =  ''
                        good.secondFeeCat       =  ''

                        // sureGoodEntry('invoiceUpload')
                        if(useGood.billCatName  == '其他普通发票' || good.billCatName  == '收据'){
                            $("#m2").html('票据内容');
                        }else{
                            $("#m2").html('货物或应税劳务、服务名称');
                        }
                        $("#billEntry").data('type', 'new')
                        // initForm('billEntry')
                        $("#billEntry [name='number']").val(1)

                        $("#billEntry .otherGood tbody").html('')
                        // 不同费用类别表格
                        if(good.billCatName === '其他普通发票') {
                            var str =   '<tr>' +
                                '<td>'+good.feeCatName+(good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                                '<td>'+good.itemName+'</td>'+
                                '<td>'+good.model+'</td>'+
                                '<td>'+good.unit+'</td>'+
                                '<td>'+good.itemQuantity+'</td>'+
                                '<td>'+good.uniPrice+'</td>'+
                                '<td>'+good.price+'</td>'+
                                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                                '<td>'+
                                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                                '</td>'+
                                '</tr>'
                            $("#billEntry .otherGood").show().siblings().hide()
                            $("#billEntry .otherGood tbody").append(str)

                        }
                        else if (good.billCatName === '收据') {
                            var str =   '<tr>' +
                                '<td>'+good.feeCatName+(good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                                '<td>'+good.itemName+'</td>'+
                                '<td>'+good.model+'</td>'+
                                '<td>'+good.unit+'</td>'+
                                '<td>'+good.itemQuantity+'</td>'+
                                '<td>'+good.uniPrice+'</td>'+
                                '<td>'+good.price+'</td>'+
                                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                                '<td>'+
                                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                                '</td>'+
                                '</tr>'
                            $("#billEntry .receipt").show().siblings().hide()
                            $("#billEntry .receipt tbody").append(str)
                        }
                        else {
                            good.taxRate /= 100
                            var str =   '<tr>' +
                                '<td>'+good.feeCatName+ (good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                                '<td>'+good.itemName+'</td>'+
                                '<td>'+good.model+'</td>'+
                                '<td>'+good.unit+'</td>'+
                                '<td>'+good.itemQuantity+'</td>'+
                                '<td>'+good.uniPrice+'</td>'+
                                '<td>'+good.price+'</td>'+
                                '<td>'+(good.taxRate * 100).toFixed(2) +' %</td>'+
                                '<td>'+good.taxAmount+'</td>'+
                                '<td>'+good.amount+'</td>'+
                                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                                '<td>'+
                                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                                '</td>'+
                                '</tr>'
                            $("#billEntry .VATGood").show().siblings().hide()
                            $("#billEntry .VATGood tbody").append(str)
                        }

                        $("#billEntry .ty-alert .issueDate").html(good.issueDate)
                        bounce_Fixed2.cancel()
                        bounce_Fixed.show($("#billEntry"));

                    })

                    // 记录费用类别 和 开票日期 为后面 做判断(而且这些字段的值只有一个，录入多个货物按照最后录入的更新）
                    var commonVal = {
                        billCatName: invoiceCat.name,
                        billCat: invoiceCat.id ,
                        issueDate: issueDateStr ,
                        memo: bz_dzfp,
                        billNo: fphm
                    }
                    $("#billEntry").data('state', commonVal)
                    countAllAmount()

                    setEveryTime(bounce_Fixed, 'billEntry')


                    let fileInfo = $("#invoiceUp").data("invoice") ;
                    var fileUid = fileInfo.fileUid;
                    //file 文件上传返回的参数  data 接口返回的参数
                    var path = fileInfo.filename,  //路径（包含文件类型）
                        fullpath = $.fileUrl + fileInfo.filename,
                        name = fileInfo.originalFilename,           //文件名称
                        imgStr = '';
                    imgStr =    '<div class="cp_img_box">' +
                        '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                        '   <a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                        '   <a class="hd fileUid">'+ fileUid +'</a>' +
                        '   </div>'+
                        '   <div class="cp_img_name">'+ name +'</div>' +
                        '</div>';
                    $('#form_billEntry').find(".cp_imgShow").html(imgStr);
                    $('#form_billEntry').data("qrInfo", qrInfo)

                    $("#billEntry .ty-form-checkbox").addClass("ty-form-checked")
                    $("#form_billEntry").show()
                    setAmount()
                }
                else if(status == 2 || status == 0){
                    let resultMap = res.resultMap
                    let data = resultMap.data
                    let yzm_info = data.yzm_info

                    if(yzm_info){
                        bounce_Fixed.show($("#invoiceUpload1"));
                        createCode(yzm_info);//刷新验证码
                        count++;
                        $("#inputCode").val("");//清空文本框
                        $("#invoiceUpload1").data('count',count).data('res',res)
                    }else{
                        layer.msg('系统没能识别这个文件！')
                        bounce_Fixed.cancel()
                    }
                }
            }else{
                let error = res.error
                layer.msg("系统没能识别这个文件！")
                // layer.msg(error.message)
            }

        }
    })
}
function formatCat2(cat, List){
    // 发票类型 【10：增值税电子普通发票，04：增值税普通发票，01：增值税专用发票】
    let invoiceCat = {}
    List.forEach(function (item) {
        if(item.name == '增值税专用发票'){
            if(cat == '01'){
                invoiceCat = item
            }
        }else if (item.name == '增值税普通发票'){
            if(cat == '04' || cat == '10'){
                invoiceCat = item
            }
        }
    })

    return invoiceCat
}
function initInvoiceUp(){
    $("#billEntry tbody").html('')
    $("#onlyOneRow").val("");
    $("#goodEntry").find("i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
    $("#goodEntry").data('type', 'new')
    $("#billEntry input").val('')
    $(".ty-nextTip").hide();
    $(".VATInvoice").hide().siblings().hide();
    $('#form_goodEntry')[0].reset()
    $("#goodEntry .textMax").html("0/30");
    var firstLoadBill = $("#reimburse tbody tr").eq(0)
    if (firstLoadBill.length !== 0) {
        var billCatName = firstLoadBill.children().eq(0).html() // 已经录入的票据类型
        if (billCatName === '收据') {
            $("#form_goodEntry [name='billCat'] option").eq(5).prop('selected', true)
        } else {
            $("#form_goodEntry [name='billCat']").val('')
        }
    }
    $(".secondFeeCat").hide()

    $('#form_billEntry').data("qrInfo", '')
    $('#invoiceUp').html("").Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;*.pdf;',
        multi:true,
        formData:{
            module: '日常事务',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        fileObjName:'file',
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadSuccess:function(file, data){
            var data = JSON.parse(data)
            $("#invoiceUp").data("invoice", data) ;
            getUploadQR()

        }
    });
    $('#invoiceUp .uploadify-button').click();

}

// creator: hxz，2020/02/06 显示图片信息
function tipDelTrOk() {
    if(tipDelObj.type ==  "tr"){
        var imgObj = tipDelObj.obj.siblings(".img");
        let fileid = imgObj.find("img").data("fileid")
        let op = {'type':'fileId', 'fileId':fileid }
        cancelFileDel(op, true);
        window.tipDelObj.obj.parents("tr").remove();
    }else{
        var imgObj = tipDelObj.obj.parents(".img");
        let fileid = imgObj.find("img").data("fileid")
        let op = {'type':'fileId', 'fileId':fileid }
        cancelFileDel(op, true);
        imgObj.html("").hide().siblings(".uploadImg").show();
    }
    bounce_Fixed3.cancel();

}
// creator: hxz，2020/02/06 显示图片信息
var IMGObj = null;
function showIMGInfo(obj) {
    IMGObj = obj;
    var hav = obj.find(".IMGinfo");
    if(hav.length >0){
        obj.find(".IMGinfo").show();
    }else{
        var filename = obj.find("img").data("filename");
        var url = obj.find("img").attr("src");
        var left = obj.offset().left - 155;
        var top = obj.offset().top + 31;
        var str = "<div class='IMGinfo' style='left:"+ left +"px;top:"+ top +"px'>" +
                        "<p class=\"picName\">文件名："+ filename +"</p>" +
                        "<a href='"+ url +"' target='_blank' class=\"ty-color-blue\" data-name='scanImg'>查看大图</a>" +
                        "<span type='btn' class=\"ty-color-red\" data-name='delImg'>删除附件</span>" +
                    "</div>";
        obj.append(str);
        obj.find(".IMGinfo").show() ;
    }


}
function hideIMG(obj) {
    obj.find(".IMGinfo").hide();
}
// creator: 张旭博，2019-07-15 09:32:56，模块初始化
function init() {
    initUpload()
    $("#beginTime1").select2({placeholder: "起时间", data: getData("0:00", 0)});
    $("#endTime1").select2({placeholder: "止时间", data: getData("17:30", 0)});
    $("#endTime001").select2({placeholder: "", data: getData("0:00", 0)});
    $("#endTime002").select2({placeholder: "", data: getData("0:00", 0)});
    $('#beginTime1').on('select2:select', function (evt) {
        $("#endTime1").html("");
        $("#endTime1").select2({
            placeholder: "止时间",
            data: getData($(this).val(), 1)
        })
    });
}

// creator: 张旭博，2019-07-15 09:34:41，初始化各个表单
function initForm(name) {
    switch (name) {
        case 'overTime':
            $(".overtimeApply input").val("");
            $(".overtimeApply textarea").val("");
            $(".overtimeApply select").html("")
            laydate.render({
                elem: '#overTimeBeginDate',
                min: 0 ,
                done: function(value){
                    var data = {
                        oid: sphdSocket.user.oid,
                        attendanceDate: value, // 请假日期
                        deptId: sphdSocket.user.deptId, // 部门id
                        userId: sphdSocket.user.userID
                    }
                    $.ajax({
                        url: '../workAttendance/getWorkTime.do',
                        data: data,
                        success: function (res) {
                            var data = res.data
                            var beginTime = data.beginTime
                            var endTime = data.endTime
                            var breakBegin = data.breakBegin
                            var breakEnd = data.breakEnd
                            var workOrNo = data.workOrNo
                            if (workOrNo==='2') {
                                $("#overtimeApply").data('timeSet', data)
                                var canChooseTimeArr = ['00:00-' + beginTime, breakBegin + '-' + breakEnd, endTime + '-24:00']
                                $("#overTimeBeginTime").html(getTimeOption(canChooseTimeArr))

                            } else {
                                var canChooseTimeArr = ['00:00-24:00']
                                $("#overTimeBeginTime").html(getTimeOption())
                                $("#overTimeEndTime").html(getTimeOption(['00:30-24:00']))
                            }
                            $("#overTimeBeginTime").unbind().on("change", function () {
                                var thisTime = $(this).val()
                                // 根据选择的时间生成止时间的区间(先查看属于哪个区间，再重新生成当前时间之后到止时间的区间)
                                var endTimePeriod = getEndTimePeriod(thisTime, canChooseTimeArr)
                                // 生成代码
                                $("#overTimeEndTime").html(getTimeOption(endTimePeriod))
                            })
                            $("#overTimeBeginTime").val(endTime).change()
                        },
                        beforeSend: function () {}
                    })
                }
            });
            break
        case 'leave':
            getAllLeaveType()
            $(".leaveApply input").val("")
            $(".leaveApply select").html("")
            $(".leaveApply textarea").val("")
            laydate.render({
                elem: '#leaveBeginDate',
                min: 0,
                ready: function(date){
                    console.log(date); //得到初始的日期时间对象：{year: 2017, month: 8, date: 18, hours: 0, minutes: 0, seconds: 0}
                    var formatMonth = date.month < 10 ? '0' + date.month : date.month
                    var formatDate = date.date < 10 ? '0' + date.date : date.date
                    getTimeMonth(date.year + '-' + formatMonth + '-' + formatDate)
                },
                change: function(value, date, endDate){
                    // 最近的两个月需要根据考勤设置判断是否可以请假（休 代表不能请假）
                    var now = moment(moment(new Date()).format('YYYY-MM-DD'))
                    var date = moment(value)
                    var diff = date.diff(now, 'months')
                    if (diff < 2 && diff >= 0) {
                        getTimeMonth(value)
                    }
                },
                done: function(value, date, endDate){
                    var leaveEndDate = $("#leaveEndDate").val()
                    // getTimeDay(value)
                    // 开始日期设置之后，设置结束日期不能小于开始日期
                    end.config.min = {
                        year : date.year,
                        month : date.month - 1,
                        date : date.date,
                        hours : date.hours,
                        minutes : date.minutes,
                        seconds : date.seconds
                    };
                    // 如果未选择结束日期，设置结束日期与开始日期相同
                    if (moment(leaveEndDate).diff(moment(value)) < 0) {
                        $("#leaveEndDate").val('')
                        $("#leaveEndTime").html('')
                    }
                    var data = {
                        oid: sphdSocket.user.oid,
                        attendanceDate: value, // 请假日期
                        deptId: sphdSocket.user.deptId, // 部门id
                        userId: sphdSocket.user.userID
                    }
                    $.ajax({
                        url: '../workAttendance/getWorkTime.do',
                        data: data,
                        success: function (res) {
                            var data = res.data
                            var beginTime = data.beginTime
                            var endTime = data.endTime
                            var breakBegin = data.breakBegin
                            var breakEnd = data.breakEnd
                            if (beginTime) {
                                // 请假可使用的时间段为上班时间（即上班开始-午休开始，午休结束-上班结束）
                                var canChooseTimeArr = [beginTime + '-' + breakBegin, breakEnd + '-' + endTime]

                                // 设置开始时间的时间段，以及开始时间修改时重新配置结束时间的选项
                                $("#leaveBeginTime").html(getTimeOption(canChooseTimeArr))
                            } else {
                                var canChooseTimeArr = ['00:00-24:00']
                                $("#leaveBeginTime").html(getTimeOption())
                            }
                            $("#leaveBeginTime").on("change", function () {
                                var thisTime = $(this).val()

                                var leaveBeginDate = $("#leaveBeginDate").val()
                                var leaveEndDate = $("#leaveEndDate").val()
                                if (leaveEndDate && leaveBeginDate === leaveEndDate) {
                                    // 根据选择的时间生成止时间的区间(先查看属于哪个区间，再重新生成当前时间之后到止时间的区间)
                                    var endTimePeriod = getEndTimePeriod(thisTime, canChooseTimeArr, true)
                                    // 生成代码
                                    $("#leaveEndTime").html(getTimeOption(endTimePeriod))
                                }
                            })
                        },
                        beforeSend: function () {}
                    })
                }
            });
            var end = laydate.render({
                elem: '#leaveEndDate',
                min: 0 ,
                ready: function(date){
                    var formatMonth = date.month < 10 ? '0' + date.month : date.month
                    var formatDate = date.date < 10 ? '0' + date.date : date.date
                    getTimeMonth(date.year + '-' + formatMonth + '-' + formatDate)
                },
                change: function(value){
                    // 最近的两个月需要根据考勤设置判断是否可以请假（休 代表不能请假）
                    var now = moment(moment(new Date()).format('YYYY-MM-DD'))
                    var date = moment(value)
                    var diff = date.diff(now, 'months')
                    if (diff < 2 && diff >= 0) {
                        getTimeMonth(value)
                    }
                },
                done: function(value){
                    var data = {
                        oid: sphdSocket.user.oid,
                        attendanceDate: value, // 请假日期
                        deptId: sphdSocket.user.deptId, // 部门id
                        userId: sphdSocket.user.userID
                    }
                    $.ajax({
                        url: '../workAttendance/getWorkTime.do',
                        data: data,
                        success: function (res) {
                            var data = res.data

                            // 获取已选择的起始日期
                            var leaveBeginDate = $("#leaveBeginDate").val()
                            var leaveEndDate = $("#leaveEndDate").val()

                            // 判断有无考勤设置,来获取结束时间的范围以及选中的值
                            if (data.beginTime) {
                                // 获取起始时间午休时间
                                var beginTime = data.beginTime
                                var endTime = data.endTime
                                var breakBegin = data.breakBegin
                                var breakEnd = data.breakEnd

                                // 请假可使用的时间段为上班时间（即上班开始-午休开始，午休结束-上班结束）
                                var canChooseTimeArr = [beginTime + '-' + breakBegin, breakEnd + '-' + endTime]
                            } else {
                                var endTime = '24:00'
                                var canChooseTimeArr = ['00:00-24:00']
                            }
                            if (leaveEndDate && leaveBeginDate === leaveEndDate) {
                                // 根据选择的时间生成止时间的区间(先查看属于哪个区间，再重新生成当前时间之后到止时间的区间)
                                var beginTime = $("#leaveBeginTime").val()
                                var endTimePeriod = getEndTimePeriod(beginTime, canChooseTimeArr, true)
                                // 生成代码
                                $("#leaveEndTime").html(getTimeOption(endTimePeriod))
                                // 加班开始时间默认赋值下班时间
                            } else {
                                $("#leaveEndTime").html(getTimeOption(canChooseTimeArr))
                            }
                            $("#leaveEndTime").val(endTime)
                        },
                        beforeSend: function () {}
                    })
                }
            });
            break;
        case 'reimburse':
            document.getElementById('form_reimburse').reset()
            $("#reimburse tbody").html('')
            break;
        case 'billEntry':
            $(".handleBtn .ty-form-checkbox").removeClass("ty-form-checked")
            $("#form_billEntry").hide()
            $("#billEntry tbody").html('')
            document.getElementById('form_billEntry').reset()
            $("#billEntry .cp_imgShow").html('')
            $("#billEntry .repeatCon").html('')
            $(".repeatBill").hide()
            break;
    }
}

// creator: 张旭博，2019-05-24 15:48:17，所有定时器
function setEveryTime(type, name) {
    // 定时器合计
    type.everyTime('0.5s',name,function(){
        switch (name) {
            // 我要报销 - 主页面
            case 'reimburse':
                var amount12              = $("#goodEntry [name='price']").val();
                if (amount12){
                    console.log(amount12)
                }

                var billCount = 0
                var count = 0
                var num = 0
                var nowMonth = formatTime(new Date()).substring(0, 7)
                var monthList = []
                $("#reimburse tbody tr").each(function () {
                    var billAmount = $(this).find('.billAmount').html()
                    var numbers = Number($(this).find('.number').html())
                    var amount = $(this).find('.amount').html()
                    billCount += parseFloat(billAmount)
                    count += parseFloat(amount)
                    num += parseInt(numbers)
                    var month = $(this).data('month')
                    if (month) {
                        monthList.push(month === nowMonth)
                    }
                });
                var isTip = false
                if (monthList.length > 1) {
                    for (var i=0; i<monthList.length; i++){
                        if(i<monthList.length-1){
                           if(monthList[i] !== monthList[i+1]){
                               isTip = true
                           }
                        }
                    }
                }

                if (isTip) {
                    $(".isMonthTip").show()
                } else {
                    $(".isMonthTip").hide()
                }
                $(".billCountAmount").html(billCount.toFixed(2))
                $(".countAmount").html(count.toFixed(2))
                $(".billCountNumber").html(num)
                var state = 0
                $("#form_reimburse").find("[require]:visible").each(function () {
                    if($.trim($(this).val()) === ''){
                        state++
                    }
                });
                $("#form_reimburse .textMax").html($("#form_reimburse [name='purpose']").val().length+'/255')
                if(!$(".transactionType input:radio:checked").val()){state++}
                if (num === 0 || state > 0 || $(".isMonthTip").is(":visible")) {
                    $("#reimburseBtn").prop("disabled",true)
                } else {
                    $("#reimburseBtn").prop("disabled",false)
                }
                break;
            // 我要报销 - 货物录入
            case 'goodEntry':
                var amount12              = $("#goodEntry [name='price']").val();
                if (amount12){
                    console.log(amount12)
                }

                var state = 0
                var isChange = $("#goodEntry").data('type')
                // $("#goodEntry").data('type', 'newNext')
                // 不同票据类型 展示和隐藏
                var kindInvoice = false , // 显示哪种类型录入
                    billNo = false, // 显示发票号码
                    billNoRed = false, // 显示发票前的必填红星
                    itemQuantityRed = true, // 显示数量前的必填红星
                    itemNameRed = true, // 显示票据信息前的必填红星
                    memoItemShow = true, // 显示发票上的备注
                    moveWidth = "600px", //
                    nextTip = false; //  显示想要录入的票据上只有一行内容项

                if(isChange === "change" || isChange === 'newNext'){
                    $("#goodEntry [name='billCat']").prop("disabled",true)
                }else{
                    $("#goodEntry .bonceFoot").children().hide();
                    $("#goodEntry [name='billCat']").prop("disabled",false)
                }
                var billCatVal = $("#billCat").val()
                var onlyOneRow = $("#onlyOneRow").val()
                var billCatName = $("#billCat").find("option:selected").html();
                var hightLight = true, sumNumber = 0, sumAmount1 = 0, sumAmount2 = 0 ; // 默认高亮


                // 判断
                if(billCatVal !== ''){
                    if (billCatName === '增值税普通发票' || billCatName === '其他普通发票' || billCatName === '收据') {
                        if(isChange === "change" || isChange === 'newNext'){
                            nextTip = false ;
                        }else{
                            nextTip = true ;
                        }
                        if(onlyOneRow === '1'){
                            if( billCatName === '收据'){
                                $(".oneRowTab thead td:eq(1)").hide()
                                $(".oneRowTab thead td:eq(9)").hide()
                                $(".oneRowTab tbody tr").each(function(){
                                    $(this).children(":eq(1)").hide();
                                    $(this).children(":eq(9)").children("span").hide();
                                    $(this).children(":eq(9)").children("i").hide();
                                    $(this).children(":eq(9)").find("div.ctrlConBtn").css({ "right":'-120px' , "top":'-15px'});
                                    $(this).children(":eq(9)").css({ "border-color": '#F0F8FF', 'background':'#F0F8FF' });
                                })
                                $("#in4").html("票据金额");
                            }else{
                                $(".oneRowTab thead td:eq(1)").show()
                                $(".oneRowTab thead td:eq(9)").show().html("发票上的备注")
                                $(".oneRowTab tbody tr").each(function(){
                                    $(this).children(":eq(1)").show();
                                    // $(this).children(":eq(9)").show();
                                    $(this).children(":eq(9)").children("span:not(.hd)").show();
                                    $(this).children(":eq(9)").children("i").show();
                                    $(this).children(":eq(9)").children("div").css({ "right":'-10px' , "top":'-8px'});
                                    $(this).children(":eq(9)").css({ "border-color": '#d7d7d7', 'background':'#fff' });

                                })
                                $("#in4").html("发票金额");
                            }

                            kindInvoice = ".oneRowInvoice" ;
                            moveWidth = "1660px";

                            // 录入按钮
                            $("#inputNextBtn").html("录入下一张只有一行内容的" + billCatName)
                            $(".oneRowTab tbody").children("tr").each(function(index){
                                var feeCats = $(this).children(":eq(0)").children(".hd").html() ;
                                var no = $(this).children(":eq(1)").children("input").val();
                                var date = $(this).children(":eq(2)").children("input").val();
                                var sum = $(this).children(":eq(7)").children("input").val();
                                var sum2 = $(this).children(":eq(8)").children("input").val();
                                // if(Number(sum) > 0){ $(this).children(":eq(7)").children("input").val( Number(sum).toFixed(2));  }
                                // if(Number(sum2) > 0){ $(this).children(":eq(8)").children("input").val( Number(sum2).toFixed(2));  }
                                if(feeCats == "" || date == "" || sum == "" || sum2 == "" ){
                                    hightLight = false;
                                }else{
                                     if(billCatName !== '收据'&& billCatName !== '其他普通发票'){
                                        if(no == ""){
                                            hightLight = false;
                                        }else{
                                            sumNumber++;
                                            sumAmount1 += Number(sum) ;
                                            sumAmount2 += Number(sum2) ;
                                        }
                                    }else{
                                         sumNumber++;
                                         sumAmount1 += Number(sum) ;
                                         sumAmount2 += Number(sum2) ;
                                     }
                                }
                                if(index == 0){
                                    $(this).find(".clearTr").hide();
                                }
                            }) ;
                            sumAmount1 = sumAmount1.toFixed(2) ;
                            sumAmount2 = sumAmount2.toFixed(2) ;
                            $("#summary").html("您已录入"+ billCatName +"共 <span class='sumNumber'>"+ sumNumber +"</span> 张，发票金额总计 <span class='sumAmount1'>"+ sumAmount1 +"</span> 元，实际支出（即您将报销）总计 <span class='sumAmount2'>"+ sumAmount2 +"</span> 元。");

                            if(hightLight){
                                $("#goodEntryBtn").prop("disabled",false);
                                $("#inputNextBtn").attr("class", "ty-right ty-btn ty-btn-green ty-btn-big ty-circle-3").attr("disabled","true");
                            }else{
                                $("#goodEntryBtn").prop("disabled",true);
                                $("#inputNextBtn").attr("class", "ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("disabled");
                            }
                        }else if(onlyOneRow === '0'){
                            kindInvoice = ".VATInvoice" ;
                            moveWidth = "600px";
                            $(".VATInvoice>.ty-alert").hide();
                            if (billCatName !== '收据') {
                                billNo = true;
                                billNoRed = true;
                            }
                            if (billCatName == '其他普通发票') {
                                billNoRed = false;
                                itemNameRed = false;
                            }

                            if(billCatName !== '其他普通发票' && billCatName !== '收据'){
                                $(".t1").hide(); $(".t2").show();
                                $("#m1").html('货物或应税劳务、服务名称');
                                $("#m2").html('货物或应税劳务、服务名称');
                            }else{
                                $(".t2").hide(); $(".t1").show();
                                $("#m1").html('票据内容');
                                $("#m2").html('票据内容');
                                if(billCatName == '其他普通发票'){
                                    itemQuantityRed = false;
                                }else if(billCatName == '收据'){
                                    memoItemShow = false;
                                }
                            }
                        }else { // 可能还没选择
                            nextTip = true;
                            moveWidth = "600px";
                        }
                    }else {
                        $("#onlyOneRow").val("");
                        $("#goodEntry").find("i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
                        if(billCatName === '定额发票'|| billCatName === '定额普通发票'){ // 所有定额的处理都在这里
                            kindInvoice = ".quotaInvoice" ;
                            moveWidth = "1660px";
                            var colNumTotal = 0, colPriceTotal = 0, colPriceTotal2 = 0 ;
                            $(".quotaInvoice tbody").children("tr").each(function (index) {
                                var feeCats = $(this).children(":eq(0)").children(".hd").html() ;
                                var rowSinglePrice = $(this).children(':eq(1)').children("input").val();
                                var rowNum = $(this).children(':eq(2)').children("input").val() ;
                                var practiceFee = $(this).children(':eq(4)').children("input").val()  ;
                                if(feeCats == "" || rowSinglePrice == "" || rowNum == "" || practiceFee == "" ){
                                    hightLight = false;
                                }
                                rowSinglePrice = rowSinglePrice || 0 ;
                                rowNum = rowNum || 0 ;
                                practiceFee = practiceFee || 0 ;
                                var rowAmountTotal = rowSinglePrice * rowNum;
                                $(this).children(':eq(3)').children("input").val(rowAmountTotal.toFixed(2));
                                colNumTotal += parseInt(rowNum);
                                colPriceTotal +=  parseFloat(rowAmountTotal)
                                colPriceTotal2 +=  parseFloat(practiceFee) ;

                                if(index == 0){
                                    $(this).find(".clearTr").hide();
                                }

                            })
                            // 备注剩余字符
                            $("#form_goodEntry .textMax").html($("#form_goodEntry [name='memo']").val().length+'/255');
                            if(hightLight){
                                colPriceTotal = colPriceTotal.toFixed(2);
                                colPriceTotal2 = colPriceTotal2.toFixed(2);
                                $("#goodEntryBtn").prop("disabled",false);
                                $("#summary2").html("您已录入"+ billCatName +"共 <span class='colNumTotal'> "+ colNumTotal +"</span> 张，发票金额总计 <span class='colPriceTotal'>"+ colPriceTotal +"</span> 元，实际支出（即您将报销）总计 <span class='colAmountTotal'>"+ colPriceTotal2 +"</span> 元。");
                                $("#inputNextBtn").attr("class", "ty-right ty-btn ty-btn-green ty-btn-big ty-circle-3").attr("disabled","true");
                                $("#newRow").attr("class", "ty-color-blue").attr("disabled","true");
                            }else{
                                $("#goodEntryBtn").prop("disabled",true);
                                $("#inputNextBtn").attr("class", "ty-right ty-btn ty-btn-gray ty-btn-big ty-circle-3").removeAttr("disabled");
                                $("#newRow").attr("class", "ty-color-gray").attr("disabled","true");
                            }

                        }else if(billCatName === '增值税专用发票'){
                            kindInvoice = ".VATInvoice" ;
                            billNo = true;
                            billNoRed = true;
                            moveWidth = "600px";
                            $(".VATInvoice>.ty-alert").show();
                            $(".t1").hide(); $(".t2").show();
                            $("#goodEntry .bonceFoot").children().show();
                        }
                    }
                }

                // 实操
                if(kindInvoice){
                    $(kindInvoice).show().siblings().hide();
                    if(kindInvoice === ".VATInvoice"){ // VATInvoice 显示 用state判断按钮
                        $("#goodEntryBtn").html("确定")
                        if(billNo){
                            $(".VATInvoice .formItem_billNo").show()
                        }else{
                            $(".VATInvoice .formItem_billNo").hide()
                        }
                        if(billNoRed){
                            $(".formItem_billNo .ty-color-red").show();
                            $(".formItem_billNo input").attr('require', '')
                        }else {
                            $(".formItem_billNo .ty-color-red").hide();
                            $(".formItem_billNo input").removeAttr('require')
                        }
                        if(itemQuantityRed){
                            $(".formItem_itemQuantity .ty-color-red").show();
                            $(".formItem_itemQuantity input").attr('require', '')
                        }else {
                            $(".formItem_itemQuantity .ty-color-red").hide();
                            $(".formItem_itemQuantity input").removeAttr('require')
                        }
                        if(itemNameRed){
                            $(".formItem_itemName .ty-color-red").show();
                            $(".formItem_itemName input").attr('require', '')
                        }else {
                            $(".formItem_itemName .ty-color-red").hide();
                            $(".formItem_itemName input").removeAttr('require')
                        }
                        if(memoItemShow){
                            $(".formItem_itemMemo").show();
                            // $(".formItem_itemMemo input").attr('require', '')
                        }else {
                            $(".formItem_itemMemo").hide();
                            // $(".formItem_itemMemo input").removeAttr('require')
                        }

                        $(".tipTaxRate").hide();
                        switch (billCatName){
                            case "增值税专用发票":
                            case "增值税普通发票":
                                // 显示增值税发票表单
                                $(".taxInput").show();
                                break;
                            case "其他普通发票":
                            case "收据":
                                $(".taxInput").hide();
                                break;
                            default:
                        }
                        // 四个票据类型 计算单价或税额
                        var amount              = $("#goodEntry [name='price']").val();
                        if (billCatName === '增值税专用发票' || billCatName === '增值税普通发票') {
                            var goodNum             = $("#goodEntry [name='itemQuantity']").val();
                            var tax                 = $("#goodEntry [name='taxAmount']").val();
                            if (goodNum && amount) {
                                var price = amount/goodNum;
                                $("#goodEntry [name='uniPrice']").val(price.toFixed(2));
                            } else {
                                $("#goodEntry [name='uniPrice']").val('');
                            }
                            if (amount && tax) {
                                var taxRate = tax/amount,
                                    taxTotal = -(-amount-tax);
                                var taxRatePercent = parseFloat( taxRate * 100 )
                                $("#goodEntry [name='taxRate']").val(taxRatePercent.toFixed(2));
                                $("#goodEntry [name='taxRate']").attr("value",taxRate.toFixed(2));
                                $("#goodEntry [name='amount']").val(taxTotal.toFixed(2));
                                if (taxRate > 1) {
                                    $(".tipTaxRate").show()
                                    state ++
                                } else {
                                    $(".tipTaxRate").hide()
                                }
                            } else {
                                $("#goodEntry [name='taxRate']").val('');
                                $("#goodEntry [name='taxRate']").attr("value",'');
                                $("#goodEntry [name='amount']").val('');
                            }
                        } else if ( billCatName === '其他普通发票' || billCatName === '收据'){
                            var goodNum             = $("#goodEntry [name='itemQuantity']").val();
                            if (goodNum && amount) {
                                var price = amount/goodNum;
                                $("#goodEntry [name='uniPrice']").val(price.toFixed(2));
                            } else {
                                $("#goodEntry [name='uniPrice']").val('');
                            }
                        } else {
                            var amount = $("#goodEntry .quotaInvoice [name='amount']").val();
                        }
                        if (amount == 0) {
                            state ++
                        }
                        $("#form_goodEntry").find("[require]:visible").each(function () {
                            if($.trim($(this).val()) === ''){
                                // if($(this).attr("name") === 'specialInterval' && !$("#newEvent [name='special']").prop("checked")){}
                                state++
                            }
                        });
                        if( state > 0){
                            $("#goodEntryBtn").prop("disabled",true)
                        }else {
                            $("#goodEntryBtn").prop("disabled",false)
                        }
                    }else{
                        $("#goodEntryBtn").html("录入完毕，下一步")
                    }
                }else{
                    $(".VATInvoice").hide().siblings().hide();
                }

                if(nextTip){
                    if(onlyOneRow === '1' || onlyOneRow === '0'){
                        $("#goodEntry .bonceFoot").children().show();
                    }else{
                        $("#goodEntry .bonceFoot").children().hide();
                    }

                    $(".ty-nextTip").show();
                }else {
                    if(billCatName === '定额发票'){
                        $("#goodEntry .bonceFoot").children().show();
                    }
                    $(".ty-nextTip").hide();
                }
                moveGoodEntry(moveWidth);
                break;
            // 我要报销 - 票据确认
            case 'billEntry':
                var amount12 = $("#goodEntry [name='price']").val();
                if (amount12){
                    console.log(amount12)
                }

                // 定时器
                var commonVal = $("#billEntry").data('state')
                countAllAmount()

                var number = parseInt($("#form_billEntry [name='number']").val())
                $("#billEntry .issueMonth").html(commonVal.issueDate.substring(0,4) + '年' + commonVal.issueDate.substring(5,7) + '月')
                $("#billEntry .issueDate").html(commonVal.issueDate)
                $("#billEntry .thisBillNo").html(commonVal.billNo)

                $("#billEntry .thisBillNum").html(number - 1)
                var isEntry = $("#billEntry .handleBtn .ty-form-checkbox").hasClass('ty-form-checked')
                var state = 0
                $("#form_billEntry").find("[require]:visible").each(function () {
                    if($.trim($(this).val()) === ''){
                        state++
                    }
                });
                if($(".isBillAmountTip").is(":visible")) {
                    state ++
                }
                if( state > 0 || !isEntry){
                    $("#billEntryBtn").prop("disabled",true)
                }else {
                    $("#billEntryBtn").prop("disabled",false)
                }
                break
        }

    });
}

// creator: 张旭博，2021-05-08 09:11:24，获取某月的作息时间
function getTimeMonth(beginDate) {
    var data = {
        oid: sphdSocket.user.oid,
        beginDate: beginDate
    }
    $.ajax({
        url: '../workAttendance/getTimeMonth.do',
        data: data,
        success: function (res) {
            var data = res.data
            var attendace = data.personnelAttendanceExceptions
            for (var i = 0; i<attendace.length; i++) {
                var exceptionDate = moment(attendace[i].exceptionDate).format('YYYY-M-D')
                var type = Number(attendace[i].type)
                if (type === 1) {
                    $(".layui-laydate:visible [lay-ymd='"+exceptionDate+"']").addClass('laydate-disabled')
                }
            }
        }
    })
}

// creator: 张旭博，2021-05-17 16:21:15，根据选择的时间生成止时间的区间(先查看属于哪个区间，再重新生成当前时间之后到止时间的区间)
function getEndTimePeriod(tim, timeArr, isNeedOtherAfterTimeArr) {
    var time = getNextHalfHourTime(tim)
    var newRange = '', sliceIndex = -1, newTimeArr = []
    var thisTimeNum = chargeToNum(time)
    var endTime = ''
    timeArr.forEach((elem, index) => {
        console.log(elem, index);
        var start = elem.split("-")[0]
        var end = elem.split("-")[1]

        var startNum = chargeToNum(start)
        var endNum = chargeToNum(end)
        if (thisTimeNum >= startNum && thisTimeNum <= endNum) {
            sliceIndex = index
            endTime = end
            newRange = time + '-' + endTime
            newTimeArr.push(newRange)
        }
        if (timeArr[index + 1]) {
            var start1 = timeArr[index + 1].split("-")[0]
            var startNum1 = chargeToNum(start1)
            if (thisTimeNum > endNum && thisTimeNum <= startNum1) {
                sliceIndex = index
            }
        }

    });

    if (isNeedOtherAfterTimeArr) {
        if (sliceIndex < 0) {
            newTimeArr = []
        } else {
            newTimeArr.push(...timeArr.slice(sliceIndex+1))
        }
    }

    return newTimeArr
}

// creator: 张旭博，2021-05-17 17:02:18，获取推迟半小时后的时间
function getNextHalfHourTime(time) {
    var a = time.split(":")
    var z = Number(a[0])
    var x = a[1]
    if (x === '00') {
        x = '30'
    } else {
        z++
        x = '00'
    }
    return ( z > 9 ? z : '0' + z ) + ':' + x
}

// creator: 张旭博，2021-05-08 09:28:43，获取所有请假类型
function getAllLeaveType() {
    var data = {
        oid: sphdSocket.user.oid
    }
    $.ajax({
        url: '../leaveType/getLeaveTypeUsable.do',
        data: data,
        success: function (res) {
            var leaveTypes = res.data.leaveTypes
            var str = ''
            for (var i in leaveTypes) {
                str += '<option value="'+leaveTypes[i].id+'">'+leaveTypes[i].name+'</option>';
            }
            $("#leaveType").html(str)
        }
    })
}

// creator: hxz，2020/02/05 增普弹窗变化
function moveGoodEntry(targetWidth){
    var oldWidth = $("#goodEntry").css("min-width") ;
    if(oldWidth == targetWidth){
    }else{
        $("#goodEntry").css("min-width", targetWidth) ;
        bounce_Fixed2.show($("#goodEntry"));
    }
}
// creator: 张旭博，2019-07-15 09:33:15，初始化上传插件
function initUpload() {
    //初始化图片上传
    $('#form_billEntry').find('.cp_imgUpload').html("").Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;*.pdf;',
        multi:true,
        formData:{
            module: '日常事务',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        fileObjName:'file',
        uploader:$.webRoot + "/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var fileUid = data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                fullpath = $.fileUrl + data.filename,
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =    '<div class="cp_img_box">' +
                        '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                        '   <a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                        '   <a class="hd fileUid">'+ fileUid +'</a>' +
                        '   </div>'+
                        '   <div class="cp_img_name">'+file.name+'</div>' +
                        '</div>';


            $('#form_billEntry').find(".cp_imgShow").html(imgStr);
        }
    });
    $('.quotaInvoice').find('.cp_imgUpload').html("").Huploadify({
        auto:true,
        fileTypeExts:'*.gif,*.png;*.jpg;*.jpeg;*.pdf;',
        multi:true,
        formData:{
            module: '日常事务',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit:40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedPercent:true,
        showUploadedSize:true,
        removeTimeout:99999999,
        fileObjName:'file',
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        /* creator：张旭博，2017-05-06 15:15:22，上传文件成功执行的方法（将返回的文件名称、类型、大小赋值到页面） */
        onUploadSuccess:function(file, data){
            $(".uploadify-queue").html("");
            var data = JSON.parse(data)
            var fileUid =data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                fullpath = $.fileUrl + data.filename,
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =    '<div class="cp_img_box">' +
                        '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                        '   <a path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                        '   <a class="hd fileUid">'+ fileUid +'</a>' +'   </div>'+
                        '   <div class="cp_img_name">'+file.name+'</div>' +
                        '</div>';
            $('.quotaInvoice').find(".cp_imgShow").html(imgStr);
        }
    });

}
function cancelfile() {
    $("#form_billEntry .cp_imgShow .cp_img_box").each(function () {
        var fileUid = $(this).find('.hd.fileUid').html()
        let op = {'type':'fileId', 'fileId':fileUid }
        cancelFileDel(op,true);
    })
    bounce_Fixed.cancel()
}
// creator: 张旭博，2019-07-12 10:29:03，计算票据信息页面 单张票据总金额
function countAllAmount() {
    var allAmount = 0
    var billAmount = $("#form_billEntry [name='billAmount']").val()
    var number = $("#form_billEntry [name='number']").val() || 1
    $("#billEntry table:visible tbody tr").each(function () {
        var goodInfo = $(this).find('.goodInfo').html()
        if (goodInfo) {goodInfo = JSON.parse(goodInfo)}
        var amount = goodInfo.amount || goodInfo.price
        allAmount += parseFloat(amount)
    })
    // 如果没有录入票据金额，那么默认给遍历货物的金额的票据金额合计，录入之后按输入与票据数量相乘得出的合计
    if (billAmount) {
        if (parseFloat(billAmount).toFixed(2) !== allAmount.toFixed(2)) {
            $(".isBillAmountTip").show()
        } else {
            $(".isBillAmountTip").hide()
        }
        allAmount = billAmount * number
    } else {
        $(".isBillAmountTip").hide()
    }
    $("#billEntry").find(".countAllAmount").html(allAmount.toFixed(2))
}
function setAmount() {
    var billAmount = $("#form_billEntry [name='billAmount']").val()
    var number = $("#form_billEntry [name='number']").val() || 1
    var allAmount = $("#billEntry").find(".countAllAmount").html()
    if(billAmount){
        allAmount = (billAmount * number).toFixed(2)
    }
    $("#billEntry").find(".inputAmount").val( allAmount)
}
function setToFixed2(thisObj){
    thisObj.val(Number(thisObj.val()).toFixed(2));
}
// creator: 张旭博，2019-07-18 14:55:25，报销申请 - 修改
function changeBill(obj) {
    // 获取票据数据
    var billInfo = obj.parents('tr').find('.billInfo').html()
    if (billInfo) { billInfo = JSON.parse(billInfo) }
    var itemNum = billInfo.itemNum ;
    var billCatName = billInfo.billCatName
    var billCat = billInfo.billCat

    if(itemNum == 1){ // 单行的数据展示
        bounce_Fixed2.show($("#goodEntry"));
        $("#goodEntry").data('type', 'change')
        $("#goodEntry").data("obj", obj)
        $("#billCat").val(billCat).attr("disabled","disabled");
        $("#billCat option").each(function () {
            if($(this).html() === billCatName){
                $(this).attr("selected" , "selected");
            }else {
                $(this).removeAttr("selected");
            }
        });
        setEveryTime(bounce_Fixed, 'goodEntry');
        $(".ty-nextTip").hide();  $("#onlyOneRow").val(1);
        if (billCatName === '增值税普通发票' || billCatName === '其他普通发票' || billCatName === '收据') {
            $(".oneRowInvoice").show().siblings().hide();
            $(".oneRowInvoice tbody").html("");
            var bills = billInfo.bills ;
            for(var i = 0 ; i < bills.length ; i++){
                var str = "";
                var invoiceDateID = "I" + Math.random().toString(36).substr(2).substr(5,10) ;
                var good = bills[i]["billItems"][0], bill = bills[i];
                var feeJson = { "feeCat_1":good['feeCat'], "feeCat_2":good['secondFeeCat'],
                    "feeCatName_1":good['feeCatName'], "feeCatName_2":good['secondFeeCatName'] } ;
                var fee = good["feeCatName"] ;
                if(good['secondFeeCat']){
                    fee += " - "+ good["secondFeeCatName"] ;
                }
                str = "<tr>" +
                    "    <td class='ty-td-control'><span class='ty-color-blue FeeCats' onclick='oneSelect($(this))'>"+ fee +"</span><span class='hd'>"+ JSON.stringify(feeJson) +"</span></td>" +
                    "    <td><input type=\"text\" value='"+ bill.billNos[0] +"'></td>" +
                    "    <td><input id='"+ invoiceDateID +"' type=\"text\" class='big' value='"+ bill.issueDate  +"'></td>" +
                    "    <td><input onchange='countWords($(this),14)' style=\"width:200px; \" class=\"litFont\" type=\"text\" value='"+ good["itemName"] +"'></td>" +
                    "    <td><input type=\"text\" value='"+ good["model"] +"'></td>" +
                    "    <td><input type=\"text\" value='"+ good["unit"] +"'></td>" +
                    "    <td><input type=\"text\" onkeyup=\"clearNoNum(this)\" value='"+ good["itemQuantity"] +"'></td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["price"] +"'>元</td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["amount"] +"'>元</td>" +
                    "    <td>" +
                    "<span class='memoAcronym'>"+ good["memo"].substr(0,2) + "..." +"</span>" +
                    "<span class='hd'>"+ good["memo"] +"</span>" +
                    "<i class='fa fa-info addMemoBtn' title='添加备注' onclick='addMemo($(this))'></i>" +
                    "<div class='ctrlCon'>" +
                    "<div class='ctrlConBtn'>" +
                    "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                    "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>";
                    if(bill.imgName){
                        let fileid = bill.fileArr
                        let path =  bill.imgPaths[0];
                        let fullpath = $.fileUrl + bill.imgPaths[0];
                    str +=  "<span type='btn' data-name='uploadImg' class='uploadImg' style='display: none' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                        "<span class='img' style='display:initial'>"+
                        "<div class='upItem' onmouseout='hideIMG($(this))' onmouseover='showIMGInfo($(this))'>" +
                        "<img data-fileid='"+ fileid +"' data-imgpath='"+ path +"' src='"+ fullpath +"' data-filename='"+ bill.imgName +"' />"+
                        // "<div class='IMGinfo hd' >" +
                        // "<p class='picName'>文件名："+ bill.imgName +"</p>"+
                        // "<a target='_blank' class='ty-color-blue' data-type='scanImg' href='"+ bill.imgPaths[0] +"'>查看大图</a>"+
                        // "<span type='btn' class='ty-color-red' data-type='delImg'>删除附件</span>"+
                        // "</div>"
                        "</div>";
                    }else{
                        str += "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                            "<span class='img'>";
                    }

                str += "</span></div></div></td> </tr>";
                $(".oneRowTab").append(str) ;
                laydate.render({elem: '#'+invoiceDateID, istoday: true, festival: true});

            }
        }else {
            // 定额
            $(".quotaInvoice").show().siblings().hide();
            $(".quotaInvoice tbody").html("");
            var bills = billInfo.bills ;
            for(var i = 0 ; i < bills.length ; i++){
                var str = "";
                var invoiceDateID = "I" + Math.random().toString(36).substr(2).substr(5,10) ;
                var good = bills[i]["billItems"][0], bill = bills[i];
                var feeJson = { "feeCat_1":good['feeCat'], "feeCat_2":good['secondFeeCat'],
                    "feeCatName_1":good['feeCatName'], "feeCatName_2":good['secondFeeCatName'] } ;
                var fee = good["feeCatName"] ;
                if(good['secondFeeCat']){
                    fee += " - "+ good["secondFeeCatName"] ;
                }
                var str = "<tr>" +
                    "   <td class='ty-td-control'><span class='ty-color-blue FeeCats' onclick='oneSelect($(this))'>"+ fee +"</span><span class='hd'>"+ JSON.stringify(feeJson) +"</span></td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["uniPrice"] +"'>元</td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["itemQuantity"] +"'>张</td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["price"] +"'>元</td>" +
                    "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\" value='"+ good["amount"] +"'>元" +
                    "<div class='ctrlCon'>" +
                    "<div class='ctrlConBtn'>" +
                    "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                    "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>";
                if(bill.imgName){
                    let path =  bill.imgPaths[0];
                    let fullpath = $.fileUrl + bill.imgPaths[0];
                    str +=  "<span type='btn' data-name='uploadImg' style='display:none;' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                    "<span class='img' style='display:initial'>"+
                        "<div class='upItem' onmouseout='hideIMG($(this))' onmouseover='showIMGInfo($(this))'>" +
                        "<img data-fileid='bill.fileArr[0]' data-imgpath='"+ path +"' src='"+ fullpath +"' data-filename='"+ bill.imgName +"' />"+
                        // "<div class='IMGinfo hd' >" +
                        // "<p class='picName'>文件名："+ bill.imgName +"</p>"+
                        // "<a target='_blank' class='ty-color-blue' data-type='scanImg' href='"+ bill.imgPaths[0] +"'>查看大图</a>"+
                        // "<span  class='ty-color-red' data-type='delImg'>删除附件</span>"+
                        // "</div>"+
                    "</div>";
                }else{
                    str += "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                    "<span class='img'>";
                }

                str +=  "</span></div></div></td> </tr>";

                $(".quotaInvoice tbody").append(str) ;
            }

        }

        return false
    }
    // 多行的数据显示
    $("#onlyOneRow").val(0);
    // 录入完毕的表单展示，勾选 ‘本张票录入完毕’按钮
    $("#form_billEntry").show()
    if (!$(".handleBtn .ty-form-checkbox").hasClass("ty-form-checked")) {
        $(".handleBtn .ty-form-checkbox").addClass("ty-form-checked")
    }

    // 获取货物数据
    var goods = billInfo.bills[0].billItems

    // 定额发票单独处理，因为是直接跳转到货物录入页面
    if (billCatName === '定额发票') {
        // 初始化
        document.getElementById('form_goodEntry').reset()
        $(".quotaInvoice tbody .addRow").remove()
        bounce_Fixed2.show($("#goodEntry"))
        // 开启验证
        setEveryTime(bounce_Fixed2, 'goodEntry')
        $(".quotaInvoice").show().siblings().hide()

        // 赋值表格
        var bills = billInfo['bills']
        var str = ''
        for(var j in bills){
            var good = bills[j]["billItems"][0]
            str += "<tr>" +
                "    <td><select onchange='setNextCat($(this))' class='feeCat_1'>"+ setFeeCats(good['feeCat'], feeCatsStr) +"</select></td>" +
                // "    <td><select class='feeCat_2'>"+ "" +"</select></td>" +
                "    <td><input type=\"text\" class='right' value='"+ good['uniPrice'] +"' onkeyup=\"clearNoNum(this)\">元</td>" +
                "    <td><input type=\"text\" class='right' value='"+ good['itemQuantity'] +"' onkeyup=\"clearNoNum(this)\">张</td>" +
                "    <td><input type=\"text\" class='right' value='"+ good['price'] +"' onkeyup=\"clearNoNum(this)\">元</td>" +
                "    <td><input type=\"text\" class='right' value='"+ good['amount'] +"' onkeyup=\"clearNoNum(this)\">元" +
                "<div class='ctrlCon'>" +
                "<div class='ctrlConBtn'>" +
                "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>"+
                "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                "<span class='img'></span>"+
                "</div>" +
                "</div>" +
                "</td>" +
                "</tr>";
            // str +=  '<tr class="addRow">' +
            //     '    <td><input type="text" class="rowSinglePrice" value="'+goods[j].uniPrice+'" require onkeyup="testNum(this)"></td>' +
            //     '    <td><input type="text" class="rowNum" style="width: 80px" value="'+goods[j].itemQuantity+'" require onkeyup="clearNum(this)">张</td>' +
            //     '    <td class="rowAmountTotal">'+goods[j].price+'</td>' +
            //     '    <td><span class="ty-color-red" type="btn" data-name="delRow">删除</span></td>' +
            //     '</tr>'
        }
        $(".quotaInvoice tbody").prepend(str)
        // 赋值一级费用类别和二级费用类别
        setFeeCat(billInfo)
        var imgPath = billInfo.imgPaths
        var imgStr = ''
        if (imgPath.length > 0) {
            imgStr =    '<div class="cp_img_box">' +
                '   <div class="fileType" style="background-image: url('+imgPath[0]+')">' +
                '   <a path="'+ imgPath[0] +'" onclick="seeOnline($(this))">预览</a>' +
                '   </div>'+
                '   <div class="cp_img_name">'+billInfo.imgName+'</div>' +
                '</div>';
        }
        $("#form_billEntry .textMax").html(billInfo.memo.length+'/255')
        $('#goodEntry').find(".cp_imgShow").html(imgStr);
        // 赋值代表是修改用的弹窗
        $("#goodEntry").data('type', 'change')
        $("#goodEntry").data('obj', obj)
        return false
    }
    // 其他发票在修改时的赋值，跳转到票据信息页面
    var str = ''
    // 最上方货物表格赋值
    for (var i in goods) {
        // 不同费用类别表格
        var feeCatName = goods[i].feeCatName
        var secondFeeCatName = goods[i].secondFeeCatName
        var catName = ''
        if (secondFeeCatName) {catName = feeCatName + '-' + secondFeeCatName} else {catName = feeCatName}
        if(billCatName === '其他普通发票') {
            str +=   '<tr>' +
                '<td>'+catName+'</td>'+
                '<td>'+goods[i].itemName+'</td>'+
                '<td>'+goods[i].model+'</td>'+
                '<td>'+goods[i].unit+'</td>'+
                '<td>'+goods[i].itemQuantity+'</td>'+
                '<td>'+goods[i].uniPrice+'</td>'+
                '<td>'+goods[i].price+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(goods[i])+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'

        } else if (billCatName === '收据') {
            str +=   '<tr>' +
                '<td>'+catName+'</td>'+
                '<td>'+goods[i].itemName+'</td>'+
                '<td>'+goods[i].model+'</td>'+
                '<td>'+goods[i].unit+'</td>'+
                '<td>'+goods[i].itemQuantity+'</td>'+
                '<td>'+goods[i].uniPrice+'</td>'+
                '<td>'+goods[i].price+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(goods[i])+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
        } else {
            str +=   '<tr>' +
                '<td>'+catName+'</td>'+
                '<td>'+goods[i].itemName+'</td>'+
                '<td>'+goods[i].model+'</td>'+
                '<td>'+goods[i].unit+'</td>'+
                '<td>'+goods[i].itemQuantity+'</td>'+
                '<td>'+goods[i].uniPrice+'</td>'+
                '<td>'+goods[i].price+'</td>'+
                '<td>'+(goods[i].taxRate * 100).toFixed(2) +' %</td>'+
                '<td>'+goods[i].taxAmount+'</td>'+
                '<td>'+goods[i].amount+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(goods[i])+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'

        }
    }
    if (billCatName === '其他普通发票'){
        $("#billEntry .otherGood").show().siblings().hide()
        $("#billEntry .otherGood tbody").html(str)
    } else if (billCatName === '收据') {
        $("#billEntry .receipt").show().siblings().hide()
        $("#billEntry .receipt tbody").html(str)
    } else {
        $("#billEntry .VATGood").show().siblings().hide()
        $("#billEntry .VATGood tbody").html(str)
    }
    // 下方票据信息确认赋值
    var info = billInfo.bills[0]
    $("#form_billEntry [name='billAmount']").val(info['billAmount'])
    $("#form_billEntry [name='amount']").val(info['amount'])
    $("#form_billEntry [name='memo']").val(info['memo'])
    $("#form_billEntry [name='number']").val(billInfo['number'])

    // 票据数量单独处理，涉及到其他发票号码的显隐
    if(Number(billInfo.number) > 1 || (billInfo.billNos && billInfo.billNos.length > 1)) {
        $("#form_billEntry .repeatBill").show()
        $("#form_billEntry .thisBillNo").html(info.billNos[0])
        var noStr = ''
        for (var k = 1 ;k < info.billNos.length; k++) {
            noStr += '<input type="text" class="billNoOther" value="'+info.billNos[k]+'" require>'
        }
        $("#form_billEntry .repeatCon").html(noStr)
    } else {
        $("#form_billEntry .repeatBill").hide()
    }
    // 文件赋值
    var imgPath = billInfo.bills[0].imgPaths
    var fileUid = billInfo.bills[0].fileArr[0]
    var imgStr = ''
    if (imgPath.length > 0) {
        imgStr =    '<div class="cp_img_box">' +
            '   <div class="fileType" style="background-image: url('+ $.fileUrl + imgPath[0]+')">' +
            '   <a path="'+ imgPath[0] +'" onclick="seeOnline($(this))">预览</a>' +
            '   <a class="hd fileUid">'+ fileUid +'</a>' + '   </div>'+
            '   <div class="cp_img_name">'+billInfo.bills[0].imgName+'</div>' +
            '</div>';
    }
    $('#form_billEntry').find(".cp_imgShow").html(imgStr);

    // 展示票据信息页面
    bounce_Fixed.show($("#billEntry"))

    // 记录费用类别 和 开票日期 为后面 做判断(而且这些字段的值只有一个，录入多个货物按照最后录入的更新）
    var commonVal = {
        billCatName: billInfo.billCatName,
        billCat: billInfo.billCat,
        issueDate: info.issueDate,
        memo: info.memo,
        billNo: info.billNos[0] || ''
    }
    console.log('每张票据的每次录入货物都会更新的子段（开票日期、票据类型、发票号码）：' + JSON.stringify(commonVal))
    $("#billEntry").data('state', commonVal)

    // 记录点击来源
    $("#billEntry").data('type', 'change')
    $("#billEntry").data('obj', obj)
    // 表单验证
    setEveryTime(bounce_Fixed, 'billEntry')
}
function cancelReimburseBtn(){
    bounce.cancel()
    $("#reimburse").find(".billInfo").each(function() {
        let data = JSON.parse($(this).html())
        let bills = data.bills
        bills.forEach(function(bill){
            let fileArr = bill.fileArr
            if(fileArr.length > 0){
                let fileid = fileArr[0]
                let op = {'type':'fileId', 'fileId':fileid }
                cancelFileDel(op, true);
            }
        })
    })
}
function cancelGoodsEntery() {
    bounce_Fixed2.cancel();
    $("#goodEntry").find(".img").each(function() {
        let fileid = $(this).find("img").data("fileid")
        let op = {'type':'fileId', 'fileId':fileid }
        cancelFileDel(op, true);
    })
}
// creator: 张旭博，2019-07-15 11:32:57，货物录入 - 确定
function sureGoodEntry(type) {
    // 搜集最后需要的货物详情参数
    // 定额发票传值和其他区别很大单独分开处理
    if($(".quotaInvoice").is(":visible")){
        var bills = [], commonBillItem = {};
        commonBillItem.billCat        = Number($("#billCat").val()) ;
        commonBillItem.billCatName    = $("#billCat").find("option:selected").html()
        commonBillItem.itemNum    = '1'
        commonBillItem.number = $(".colNumTotal").html()
        var billAmountOnly = 0;
        var allBillAmount = 0 , allAmount = 0;
        $(".quotaInvoice tbody tr").each(function () {
            var bill = {}
            var billItems = []
            var imgObj = $(this).find("img");
            // bill.billNos = []
            // bill.issueDate = ""
            if(imgObj.length == 0){
                bill.imgPaths      = []
                bill.fileArr      = []
            }else{
                bill.imgPaths      = [imgObj.data('imgpath')]
                bill.fileArr      = [imgObj.data('fileid')]
            }
            // bill.imgPaths      = [imgObj.attr('src')]
            bill.imgName =  imgObj.data('filename')
            var feeCats = $(this).children(":eq(0)").children(".hd").html() ;
            feeCats = JSON.parse(feeCats) ;
            var good = {
                itemQuantity       : $(this).children(":eq(2)").children("input").val(),
                uniPrice           : $(this).children(":eq(1)").children("input").val(),
                price              :  $(this).children(":eq(3)").children("input").val(),
                feeCat             :  feeCats["feeCat_1"],
                feeCatName         :  feeCats["feeCatName_1"],
                secondFeeCat       : feeCats["feeCat_2"],
                memo                : "",
                secondFeeCatName   : feeCats["feeCatName_2"],
                amount   : $(this).children(":eq(4)").children("input").val()
            }
            bill.memo           =  good.memo
            bill.billAmount     = Number(good.price).toFixed(2)
            bill.number         =  1
            bill.amount         = Number(good.amount).toFixed(2)
            allBillAmount = (Number(bill.billAmount) + Number(allBillAmount)).toFixed(2)
            allAmount = (Number(bill.amount) + Number(allAmount)).toFixed(2)
            if(billAmountOnly != "--"){
                if(billAmountOnly == 0){
                    billAmountOnly = good.uniPrice
                }else{
                    if(billAmountOnly == good.uniPrice){}else{
                        billAmountOnly = "--"
                    }
                }
            }
            billItems.push(good);
            bill.billItems = billItems
            bill.qrInfo = ''
            bills.push(bill);
        })
        commonBillItem.bills = bills;
        var str = '<tr>' +
            '<td>' + commonBillItem.billCatName + '</td>'+
            '<td>'+ billAmountOnly +'</td>'+
            '<td class="number">' + commonBillItem.number + '</td>'+
            '<td class="billAmount">' + allBillAmount + '</td>'+
            '<td class="amount">' + allAmount+ '</td>'+
            '<td class="billInfo" style="display: none">'+JSON.stringify(commonBillItem)+'</td>'+
            '<td>'+
            '<span type="btn" data-name="changeBill" class="ty-color-blue">修改</span>'+
            '<span type="btn" data-name="delBill" class="ty-color-red">删除</span>'+
            '</td>'+
            '</tr>';

        var type = $("#goodEntry").data("type")
        if(type === 'change') {
            var obj = $("#goodEntry").data("obj")
            obj.parents('tr').replaceWith(str)
        } else {
            $("#reimburse table tbody").append(str)
        }
        bounce_Fixed2.cancel()
        return false
    }
    /*--- 除开定额发票的其他发票处理 ----*/
    var display = $(".VATInvoice").css("display");
    if(display == "none"){ // 单行表格的情况
        var commonBillItem = {};
        commonBillItem.billCat        = $("#billCat").val()
        commonBillItem.billCatName    = $("#billCat").find("option:selected").html()
        commonBillItem.number = 0
        commonBillItem.itemNum    = '1'
        var bills = [] ;
        // bill.imgPaths = [];
        var billAmountOnly = 0;
        var allBillAmount = 0 , allAmount = 0;
        $(".oneRowInvoice tbody tr").each(function () {
            commonBillItem.number++
            var bill = {};
            var billItems = []
            var imgObj = $(this).find("img");
            if(imgObj.length == 0){
                bill.imgPaths      = []
                bill.fileArr      = []
            }else{
                bill.imgPaths      = [imgObj.data('imgpath')]
                bill.fileArr      = [imgObj.data('fileid')]
            }
            bill.imgName =  imgObj.data('filename')
            bill.issueDate = $(this).children(":eq(2)").children("input").val()
            bill.billNos      =  [ $(this).children(":eq(1)").children("input").val() ]; // 发票号
            var feeCats = $(this).children(":eq(0)").children(".hd").html() ;
            feeCats = JSON.parse(feeCats) ;
            var good = {
                unit         : $(this).children(":eq(5)").children("input").val(), // 单位
                model       : $(this).children(":eq(4)").children("input").val(), // 型号
                itemName       : $(this).children(":eq(3)").children("input").val(), // 货物或应税劳务、服务名称  票据内容
                itemQuantity       : $(this).children(":eq(6)").children("input").val(),
                uniPrice           : 0,
                price              :  $(this).children(":eq(7)").children("input").val(),
                taxRate            : '',
                taxAmount          : '',
                amount             : $(this).children(":eq(8)").children("input").val(), // 含税金额
                feeCat             :  feeCats["feeCat_1"],
                feeCatName         :  feeCats["feeCatName_1"],
                secondFeeCat       : feeCats["feeCat_2"],
                secondFeeCatName   : feeCats["feeCatName_2"],
                memo : $(this).children(":eq(9)").children(".hd").html()
            }
            bill.memo         = good.memo
            bill.billAmount     = good.price
            bill.amount         = good.amount

            allBillAmount = (Number(bill.billAmount) + Number(allBillAmount)).toFixed(2)
            allAmount = (Number(bill.amount) + Number(allAmount)).toFixed(2)

            bill.number         =  1
            if(billAmountOnly != "--"){
                if(billAmountOnly == 0){
                    billAmountOnly = good.price
                }else{
                    if(billAmountOnly == good.price){}else{
                        billAmountOnly = "--"
                    }
                }
            }
            billItems.push(good);
            bill.billItems = billItems ;
            bill.qrInfo = ''
            bills.push(bill);
        })

        commonBillItem.bills = bills
        var str =   '<tr>' +
            '<td>'+commonBillItem.billCatName+'</td>'+
            '<td>'+ billAmountOnly +'</td>'+
            '<td class="number">'+commonBillItem.number+'</td>'+
            '<td class="billAmount">'+  allBillAmount +'</td>'+
            '<td class="amount">'+ allAmount +'</td>'+
            '<td class="billInfo" style="display: none">'+JSON.stringify(commonBillItem)+'</td>'+
            '<td>'+
            '<span type="btn" data-name="changeBill" class="ty-color-blue">修改</span>'+
            '<span type="btn" data-name="delBill" class="ty-color-red">删除</span>'+
            '</td>'+
            '</tr>'

        var type = $("#goodEntry").data('type')
        var obj = $("#goodEntry").data('obj')
        if (type === 'change') {
            obj.parents('tr').replaceWith(str)
        } else {
            $("#reimburse table tbody").append(str)
        }
        bounce_Fixed2.cancel()
        initForm('billEntry')


    }else{ // 不是单行的情况
        var good = {}
        $("#goodEntry [name]:visible").each(function () {
            var name = $(this).attr("name")
            var type = $(this).data("type")
            if (type === 'num') {
                good[name] = Number($(this).val())
            }  else {
                good[name] = $(this).val()
            }
        })
        good.billCat            = $("#billCat").val()
        good.billCatName        = $("#billCat").find("option:selected").html()
        good.feeCatName         = $(".firstFeeCat select option:selected").html()
        good.feeCat             = $(".firstFeeCat select").val()
        good.secondFeeCatName   = $('.secondFeeCat').is(":visible")? $(".secondFeeCat select option:selected").html() : ''
        good.secondFeeCat       = $('.secondFeeCat').is(":visible")? $(".secondFeeCat select").val(): ''

        var type = $("#goodEntry").data('type')
        if(good.billCatName  == '其他普通发票' || good.billCatName  == '收据'){
            $("#m2").html('票据内容');
        }else{
            $("#m2").html('货物或应税劳务、服务名称');
        }
        // 在票据信息赋值来源为新增
        if(type === 'new') {
            // 如果货物录入是第一次新增的，那么票据信息页面设置为新
            $("#billEntry").data('type', 'new')
            initForm('billEntry')
            $("#billEntry [name='number']").val(1)
        }else{

            // $(".repeatCon").html("")
            // $(".repeatBill").hide()
            // $("#billEntry [name='number']").val(1)
        }

        // 不同费用类别表格
        if(good.billCatName === '其他普通发票') {
            var str =   '<tr>' +
                '<td>'+good.feeCatName+(good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                '<td>'+good.itemName+'</td>'+
                '<td>'+good.model+'</td>'+
                '<td>'+good.unit+'</td>'+
                '<td>'+good.itemQuantity+'</td>'+
                '<td>'+good.uniPrice+'</td>'+
                '<td>'+good.price+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
            $("#billEntry .otherGood").show().siblings().hide()
            if (type === 'change') {
                var obj = $("#goodEntry").data('obj')
                obj.parents('tr').replaceWith(str)
            } else {
                $("#billEntry .otherGood tbody").append(str)
            }

        }
        else if (good.billCatName === '收据') {
            var str =   '<tr>' +
                '<td>'+good.feeCatName+(good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                '<td>'+good.itemName+'</td>'+
                '<td>'+good.model+'</td>'+
                '<td>'+good.unit+'</td>'+
                '<td>'+good.itemQuantity+'</td>'+
                '<td>'+good.uniPrice+'</td>'+
                '<td>'+good.price+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
            $("#billEntry .receipt").show().siblings().hide()
            if (type === 'change') {
                var obj = $("#goodEntry").data('obj')
                obj.parents('tr').replaceWith(str)
            } else {
                $("#billEntry .receipt tbody").append(str)
            }
        } else {
            good.taxRate /= 100
            var str =   '<tr>' +
                '<td>'+good.feeCatName+ (good.secondFeeCatName?'-'+good.secondFeeCatName : '')+'</td>'+
                '<td>'+good.itemName+'</td>'+
                '<td>'+good.model+'</td>'+
                '<td>'+good.unit+'</td>'+
                '<td>'+good.itemQuantity+'</td>'+
                '<td>'+good.uniPrice+'</td>'+
                '<td>'+good.price+'</td>'+
                '<td>'+(good.taxRate * 100).toFixed(2) +' %</td>'+
                '<td>'+good.taxAmount+'</td>'+
                '<td>'+good.amount+'</td>'+
                '<td class="goodInfo" style="display: none">'+JSON.stringify(good)+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeGood" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delGood" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
            $("#billEntry .VATGood").show().siblings().hide()
            if (type === 'change') {
                var obj = $("#goodEntry").data('obj')
                obj.parents('tr').replaceWith(str)
                // 总结新的数据

            } else {
                $("#billEntry .VATGood tbody").append(str)
            }
        }
        // console.log(JSON.stringify(good))

        $("#billEntry .ty-alert .issueDate").html(good.issueDate)
        bounce_Fixed2.cancel()
        bounce_Fixed.show($("#billEntry"))

        // 记录费用类别 和 开票日期 为后面 做判断(而且这些字段的值只有一个，录入多个货物按照最后录入的更新）
        var commonVal = {
            billCatName: good.billCatName,
            billCat: good.billCat,
            issueDate: good.issueDate,
            memo: good.memo,
            billNo: $(".billNo").val()
        }
        $("#billEntry").data('state', commonVal)
        countAllAmount()
        setEveryTime(bounce_Fixed, 'billEntry')
    }


}

// creator: 张旭博，2019-07-15 11:36:58，票据信息 - 确定
function sureBillEntry(obj) {
    // 获取货物信息
    var bill = {}, commonBillItem = {}
    var goods = []
    $("#billEntry table:visible tbody tr").each(function () {
        var good = $(this).children('.goodInfo').html()
        good = JSON.parse(good)
        bill.memo = good['memo']
        goods.push(good)
    })
    $("#billEntry [name]:visible").each(function () {
        var name = $(this).attr("name")
        var type = $(this).data("type")
        if (type === 'num') {
            if(name == "number"){
                commonBillItem[name] =  Number($(this).val())
            }else{
                bill[name] =  Number($(this).val())
            }
        } else if (type === 'singleCheck') {
            bill[name] = $(this).is(':checked')
        } else {
            bill[name] = $(this).val()
        }
    })
    // 获取票据信息
    bill.billItems = goods

    var state = $("#billEntry").data('state')
    // 这四个字段从state中去（因为是实时更新的）
    commonBillItem.billCat = state.billCat
    commonBillItem.billCatName = state.billCatName
    if (state.billNo) {
        bill.billNos = [state.billNo]
    } else {
        bill.billNos = []
    }
    $(".repeatCon input").each(function () {
        var val = $(this).val()
        bill.billNos.push(val)
    })
    bill.issueDate = state.issueDate
    var countAllAmount = $(".countAllAmount").html()
    bill.imgPaths = []
    bill.fileArr = []
    $("#form_billEntry .cp_imgShow .cp_img_box").each(function () {
        var path = $(this).find('a').attr('path')
        var fileUid = $(this).find('.hd.fileUid').html()
        var name = $(this).find('.cp_img_name').html()
        bill.imgPaths.push(path)
        bill.fileArr.push(fileUid)
        bill.imgName = name
    })
    bill.amount = bill.amount.toFixed(2);
    let qrInfo = $('#form_billEntry').data("qrInfo")
    if(qrInfo){
        bill.qrInfo = qrInfo
    }else{
        bill.qrInfo = ''
    }
    commonBillItem.bills = [bill];
    var str =   '<tr data-month="'+commonBillItem.bills[0].issueDate.substring(0, 7)+'">' +
                '<td>'+commonBillItem.billCatName+'</td>'+
                '<td>'+bill.billAmount+'</td>'+
                '<td class="number">'+commonBillItem.number+'</td>'+
                '<td class="billAmount">'+countAllAmount+'</td>'+
                '<td class="amount">'+bill.amount+'</td>'+
                '<td class="billInfo" style="display: none">'+JSON.stringify(commonBillItem)+'</td>'+
                '<td>'+
                '<span type="btn" data-name="changeBill" class="ty-color-blue">修改</span>'+
                '<span type="btn" data-name="delBill" class="ty-color-red">删除</span>'+
                '</td>'+
                '</tr>'
    // console.log(JSON.stringify(bill))
    var type = $("#billEntry").data('type')
    var obj = $("#billEntry").data('obj')
    if (type === 'change') {
        obj.parents('tr').replaceWith(str)
    } else {
        $("#reimburse table tbody").append(str)
    }
    bounce_Fixed.cancel()
    initForm('billEntry')
}

// creator: 张旭博，2019-07-15 11:36:58，报销申请 - 确定
function sureReimburse() {
    var billCountAmount = $(".billCountAmount").html()
    var countAmount = $(".countAmount").html()
    var billCountNumber = $(".billCountNumber").html()
    var reimburse = {
        billQuantity: billCountNumber,
        billAmount: billCountAmount,
        amount: countAmount,

    }
    var commonBills = []
    $("#reimburse [name]:visible").each(function () {
        var name = $(this).attr("name")
        var type = $(this).data("type")
        if (type === 'num') {
            reimburse[name] = Number($(this).val())
        } else {
            reimburse[name] = $(this).val()
        }
    })
    reimburse.transactionType = $(".transactionType input:radio:checked").val()
    $("#reimburse tbody tr").each(function () {
        var commonBillItem = $(this).children('.billInfo').html()
        commonBillItem = JSON.parse(commonBillItem)
        let bills = commonBillItem.bills
        bills.forEach(function(bill){
            // let path = bill.imgPaths[0]
            // bill.imgPaths[0] = path ? path.split('upload/')[1] : ''
            let fileArr = bill.fileArr
            delete bill.fileArr
            if(!bill.qrInfo){
                delete bill.qrInfo
            }
        })
        if(!commonBillItem.itemNum){
            commonBillItem.itemNum = 0 ;
        }else {
            commonBillItem.itemNum = 1 ;
        }
        for(var i in  commonBillItem.bills.billItems){
            if ( commonBillItem.bills.billItems[i].secondFeeCat) {
                commonBillItem.bills.billItems[i].feeCat = goods[i].secondFeeCat
            }
        }
        commonBills.push(commonBillItem)
    })

    var bill = {
        userId:window.parent.sphdSocket.user.userID,
        reimburse: JSON.stringify(reimburse),
        commonBills: JSON.stringify(commonBills)
    }
    let submitType = $("#reimburse").data("submitType")
    let url = '../data/debitReimburse.do'
    if(submitType == "myself"){
        bill.factUser = 0
    }else if(submitType == "otherTog"){
        bill.factUser = $("#partnerName").val()
    }
    $("#firstname").val("")
    $(".notTransfor").hide()

    $.ajax({
        url: url,
        data: bill,
        success: function (res) {
            bounce.cancel();
            let content = res["data"]["content"];
            let success = res["success"];
            if ( success === 1) {
                $("#reimburse").find(".billInfo").each(function() {
                    let data = JSON.parse($(this).html())
                    let bills = data.bills
                    bills.forEach(function(bill){
                        let fileArr = bill.fileArr
                        if(fileArr.length > 0){
                            let fileid = fileArr[0]
                            let op = {'type':'fileId', 'fileId':fileid }
                            cancelFileDel(op);
                        }
                    })
                })
                layer.msg(content);
            } else {
                layer.msg("链接错误，申请失败！");
            }
        }
    })
}

// creator:hxz 2020/03/16 选择费用类别
var editTD = null; // 记录费用类别的操作td
function oneSelect(thisObj) {
    editTD = thisObj;
    $("#pFeeCat2").hide();
    $(".feeCat_1").html(feeCatsStr);
    bounce_Fixed3.show($("#selectFee"));
}
function selectFeeOk() {
    var feeCat_1 = $(".feeCat_1").val();
    if(feeCat_1 === ""){
        layer.msg("请选择费用类别！");
        return false;
    }
    var feeCatName_1 = $(".feeCat_1 option:selected").html();
    var feeJson = { "feeCat_1":feeCat_1, "feeCat_2":"", "feeCatName_1":feeCatName_1, "feeCatName_2":"" } ;
    var str = feeCatName_1 ;
    var v = $("#pFeeCat2").is(":visible")
    if(v){
        var feeCat_2 = $(".feeCat_2").val();
        if(feeCat_2 === ""){
            layer.msg("请选择二级类别");
            return false
        }
        var feeCatName_2 = $(".feeCat_2 option:selected").html();
        feeJson["feeCat_2"] = feeCat_2
        feeJson["feeCatName_2"] = feeCatName_2
        str += " - " + feeCatName_2 ;
    }
    editTD.html(str);
    editTD.siblings(".hd").html(JSON.stringify(feeJson));
    bounce_Fixed3.cancel();
}
// creator:hxz 2020/03/16 添加备注
function addMemo(thisObj) {
    editTD = thisObj;
    bounce_Fixed3.show($("#addMemo"));
    $("#memo3").val(thisObj.siblings(".hd").html());
}
function addMemoOk() {
    var memo3 = $("#memo3").val();
    editTD.siblings(".memoAcronym").html(memo3.substr(0,2) + "...");
    editTD.siblings(".hd").html(memo3);
    bounce_Fixed3.cancel()
}
// creator:hxz 2020/0205 插入一行
function inputNextTr(type , isReplace){
    var invoiceDateID = "I" + Math.random().toString(36).substr(2).substr(5,10) ;
    switch (type){
        case 1:
            var str = "<tr>" +
                // "    <td><select onchange='setNextCat($(this))' class='feeCat_1'>"+ feeCatsStr +"</select></td>" +
                // "    <td><select class='feeCat_2'></select></td>" +
                "    <td class='ty-td-control'><span class='ty-color-blue FeeCats' onclick='oneSelect($(this))'>选择类别</span><span class='hd'></span></td>" +
                "    <td><input type=\"text\" onchange='countWords($(this),8)'></td>" +
                "    <td><input id='"+ invoiceDateID +"' type=\"text\" class='big'></td>" +
                "    <td><input type=\"text\" onchange='countWords($(this),14)' style='width:200px; ' class='litFont'></td>" +
                "    <td><input type=\"text\" onchange='countWords($(this),10)'></td>" +
                "    <td><input type=\"text\" onchange='countWords($(this),6)'></td>" +
                "    <td><input type=\"text\" onkeyup=\"clearNoNum(this);\" onchange='countWords($(this),10)'></td>" +
                "    <td><input type=\"text\" class='right' onchange='toFix2($(this))' onkeyup=\"clearNoNum(this);countWords($(this),8)\">元</td>" +
                "    <td><input type=\"text\" class='right' onchange='toFix2($(this))' onkeyup=\"clearNoNum(this);countWords($(this),8)\">元</td>" +
                "    <td>" +
                    "<span class='memoAcronym'></span>" +
                    "<span class='hd'></span>" +
                    "<i class='fa fa-info addMemoBtn' title='添加备注' onclick='addMemo($(this))'></i>" +
                "<div class='ctrlCon'>" +
                    "<div class='ctrlConBtn'>" +
                    "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                    "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>"+
                    "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                    "<span class='img'></span>"+
                "</div>" +
                "</div>" +
                "</td>" +
                "</tr>";
            $(".oneRowTab").append(str) ;
            laydate.render({elem: '#'+invoiceDateID, istoday: true, festival: true});
            break;
        case 2:
            var str = "<tr>" +
                // "    <td><select onchange='setNextCat($(this))' class='feeCat_1'>"+ feeCatsStr +"</select></td>" +
                // "    <td><select class='feeCat_2'></select></td>" +
                "   <td class='ty-td-control'><span class='ty-color-blue FeeCats' onclick='oneSelect($(this))'>选择类别</span><span class='hd'></span></td>" +
                "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\">元</td>" +
                "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\">张</td>" +
                "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\">元</td>" +
                "    <td><input type=\"text\" class='right' onkeyup=\"clearNoNum(this)\">元" +
                "<div class='ctrlCon'>" +
                    "<div class='ctrlConBtn'>" +
                    "<span type='btn' data-name='clearTr' class='clearTr' title='删除本行'><i class='fa fa-trash'></i></span>"+
                    "<span type='btn' data-name='reset' class='reset' title='清空本行'><i class='fa fa-undo'></i></span>"+
                    "<span type='btn' data-name='uploadImg' class='uploadImg' title='上传票据照片'><i class='fa fa-paperclip'></i></span>"+
                    "<span class='img'></span>"+
                    "</div>" +
                "</div>" +
                "</td>" +
                "</tr>";

            $(".quotaInvoice tbody").append(str) ;
            break
    }


}
// creator:hxz 2020/05/26 两位数
function toFix2(thisObj) {
    thisObj.val(Number(thisObj.val()).toFixed(2));
}
// creator:hxz 2020/0205 设置而二级科目
function setNextCat(thisObj){
    var feeCatName = thisObj.find('option:selected').html()
    var feeCat = thisObj.val();
    if(feeCatName === '交通费' || feeCatName === '车务支出') {
        $("#pFeeCat2").show();
        getSecondFeeCat(feeCat, function (err, data) {
            var str = '<option value="">---请选择二级费用类别---</option>'
            for (var i in data) {
                if (Number(data[i].enabled) === 1) {
                    str += '<option value="' + data[i].id + '">' + data[i].name + '</option>'
                }
            }
            thisObj.parent().next().children("select").html(str)
        })
    }else {
        $("#pFeeCat2").hide();
        thisObj.parent().next().children("select").html("")
    }

}
// creator:hxz 2018-10-20 确定新增 加班/请假 按钮
function sureOvertimeApply() {

    let overTimeBeginDate   = $("#overTimeBeginDate").val();
    let overTimeBeginTime   = $("#overTimeBeginTime").val();
    let overTimeEndTime     = $("#overTimeEndTime").val();
    let hours   = $("#hours").val();
    let reason  = $("#overTimeReason").val();


    if ($.trim(overTimeBeginDate) === "") {
        layer.msg("请选择加班日期！");
        return;
    } else if ($.trim(overTimeBeginTime) === "") {
        layer.msg("请选择加班开始时间！");
        return;
    } else if ($.trim(overTimeEndTime) === "") {
        layer.msg("请选择加班结束时间！");
        return;
    } else if ($.trim(reason) === "") {
        layer.msg("请输入加班事由！");
        return;
    } else {
        var _b = new Date(overTimeBeginTime);
        var _e = new Date(overTimeEndTime);
        if (_b > _e) {
            layer.msg("加班开始时间不能晚于结束时间 ！");
            return;
        }
    }
    let data = {
            beginTime1: overTimeBeginDate + ' ' + overTimeBeginTime + ':00',
            endTime1: overTimeBeginDate + ' ' + overTimeEndTime + ':00',
            duration: hours,
            userId: sphdSocket.user.userID,
            reason: reason,
            tjtype: 1
        }
    $.ajax({
        url: '../leaveAndOutTime/addPlanOverTime.do',
        data: data,
        success: function (res) {
            bounce.cancel();
            let status = res.data.status;
            if (status === 1) {
                layer.msg("申请成功！");
            } else {
                layer.msg("您申请中的时间有些问题，请重新申请！");
            }
        }
    })

}

// creator: 张旭博，2021-06-03 17:16:59，新增请假 - 确定按钮
function sureLeaveApply() {
    let leaveType = $("#leaveType").val();
    let leaveBeginDate = $("#leaveBeginDate").val();
    let leaveBeginTime = $("#leaveBeginTime").val();
    let leaveEndDate = $("#leaveEndDate").val();
    let leaveEndTime = $("#leaveEndTime").val();
    let leaveReason = $("#leaveReason").val();
    if ($.trim(leaveType) === "") {
        layer.msg("请选择请假类型！");
        return;
    } else if ($.trim(leaveBeginTime) === "") {
        layer.msg("请选择请假开始时间！");
        return;
    } else if ($.trim(leaveEndTime) === "") {
        layer.msg("请选择请假结束时间！");
        return;
    }  else if ($.trim(leaveReason) === "") {
        layer.msg("请输入请假事由！");
        return;
    } else {
        var beginTime1 = leaveBeginDate + " " + leaveBeginTime + ':00';
        var endTime1 = leaveEndDate + " " + leaveEndTime + ':00';
        var _b = new Date(beginTime1);
        var _e = new Date(endTime1);
        if (_b > _e) {
            layer.msg("请假开始时间不能晚于结束时间 ！");
            return;
        }
    }
    $.ajax({
        url: '../leaveAndOutTime/addLeave.do',
        data: {
            beginTime1: beginTime1,
            endTime1: endTime1,
            leaveType: leaveType,
            reason: leaveReason
        },
        success: function (res) {
            bounce.cancel();
            var data = res.data
            let status = data.status;
            let content = data.content;
            if (status) {
                layer.msg(content);
            } else {
                layer.msg("操作失败");
            }
        }
    })
}

// 格式化时间
function chargeTime(time) {
    var a = time.split(":")[0];
    var b = time.split(":")[1];
    if (parseInt(a)< 10) {
        a = "0" + a
    }
    time = a + ":" + b + ":" + "00";
    return time;
}

// 生成时间选项
function getTimeOption(timeArr) {
    // timeArr 示例 ['00:00-09:00', '10:00-11:00'] 需要显示的区间（剩下的禁用）

    if (!timeArr) {
        timeArr = ['00:00-24:00']
    }
    var data = []
    var n = 24
    if ($(".leaveApply").is(":visible")) {
        n = 23
    }
    for (var i = 0; i <= n; i++) {
        var textMM = i < 10 ? '0' + i : i;
        data.push({value: textMM + ':' + '00', text: textMM + ':' + '00', disabled: true});
        if (i !== 24) {
            data.push({value: textMM + ':' + '30', text: textMM + ':' + '30', disabled: true});
        }
    }
    timeArr.forEach((elem, index) => {
        console.log(elem, index);
        var start = elem.split("-")[0]
        var end = elem.split("-")[1]
        if ($(".leaveApply").is(":visible")) {
            if (end === '24:00') {
                end = '23:30'
            }
        }

        var startNum = chargeToNum(start)
        var endNum = chargeToNum(end)
        for (var i = startNum; i <= endNum; i++) {
            data[i].disabled = false
        }
    });

    var str = ''

    for (var k in data) {
        var disabledStr = data[k].disabled ? 'disabled' : ''
            str += '<option value="' + data[k].value + '" '+disabledStr+'>' + data[k].text + '</option>'
    }
    return str;

}

function chargeToNum(time) {
    var timeMM = Number(time.split(":")[0]);
    var timeSS = time.split(":")[1];
    timeSS = timeSS === '30'?  0.5: 0
    return Number((- ( -timeMM - timeSS)) * 2 )
}

// creator: 张旭博，2019-07-15 09:34:24，查询全部费用类别
var feeCatsStr = ""; // 费用类别的字串
function getFeeCats() {
    $.ajax({
        url:"../expense/querySysCode.do",
        data:{},
        success:function(data){
            if(typeof(data.feeCats)==="undefined"){
                return false;
            }else{
                var str='<option value="">------请选择费用类别------</option>';
                for(var i=0;i<data.feeCats.length;i++){
                    str+='<option value='+data.feeCats[i].id+'>'+data.feeCats[i].name+'</option>'
                }
                $("#feeCat").html(str);
                feeCatsStr = str ;
                $("#selectFee").data('feestr', data.feeCats)
            }
        }
    });
}

// creator: hxz，2020-03-08，设置选中的费用类别
function setFeeCats(selectedCatID, feeStr){
    return feeStr.replace("value='"+ selectedCatID +"'", "value='"+ selectedCatID +"' selected ");
}
// creator: 张旭博，2019-08-05 10:37:45，设置费用类别
function setFeeCat(billInfo) {
    $("#goodEntry .feeCat").val(billInfo.feeCat)
    if (billInfo.feeCatName === '车务支出' || billInfo.feeCatName === '交通费') {
        $("#goodEntry .secondFeeCat").show()
        getSecondFeeCat(billInfo.feeCat, function (err, data) {
            var str = '<option value="">---请选择二级费用类别---</option>'
            for (var i in data) {
                if (Number(data[i].enabled) === 1) {
                    str += '<option value="' + data[i].id + '">' + data[i].name + '</option>'
                }
            }
            $(".secondFeeCat select").html(str)
            $(".secondFeeCat select").val(billInfo.secondFeeCat)
        })
    } else {
        $("#goodEntry .secondFeeCat").hide()
    }
}

// creator: 张旭博，2019-07-15 09:33:46，根据一级费用类别获取二级费用类别
function getSecondFeeCat(feeCat, callback) {
    $.ajax({
        url:'../reimburseWindow/getSecondCodeList.do',
        data: {id: feeCat},
        success: function (data) {
            callback(null, data)
        }
    })
}

// creator: 张旭博，2019-07-15 09:34:04，查询全部票据种类
function getBillCats() {
    $.ajax({
        url:"../expense/queryCodeCategory.do",
        data:{},
        success:function(data){
            if(typeof(data.billCats)==="undefined"){
                return false;
            }else{
                var str='<option value="">------请选择票据种类------</option>';
                for(var i=0;i<data.billCats.length;i++){
                    str+='<option value='+data.billCats[i].id+'>'+data.billCats[i].name+'</option>'
                }
                $("#billCat").html("").append(str);
            }
        }
    })
}

// creator:hxz 2018-10-20 计算加班时长工具方法
function betweenHous(start, end) {
    let startTime = new Date(start).getTime();
    let endTime = new Date(end).getTime();
    let btws = endTime - startTime;
    return btws / 1000 / 60 / 60;
}

// creator: 张旭博，2019-03-14 14:30:47，统计字数
function countWords(obj,max){
    var curLength = obj.val().length;
    if (curLength > max) {
        curLength = max
        var cutValue = obj.val().substring(0,max);
        obj.val(cutValue);
        layer.msg("字数不能超过" + max + "个！");
    }
    obj.siblings(".textMax").text(curLength + '/' + max);
}
// 工具方法： 返回文件类型的DOM串
function chargeFileType(fileType) {
    var midtermStr = "";
    switch (fileType) {
        case "doc":
        case "docx":
            midtermStr = '<div class="ty-fileType ty-file_doc"></div>';
            break;
        case "xls":
        case "xlsx":
        case "et":
            midtermStr = '<div class="ty-fileType ty-file_xls"></div>';
            break;
        case "ppt":
            midtermStr = '<div class="ty-fileType ty-file_ppt"></div>';
            break;
        case "rar":
        case "zip":
            midtermStr = '<div class="ty-fileType ty-file_rar"></div>';
            break;
        case "pdf":
            midtermStr = '<div class="ty-fileType ty-file_pdf"></div>';
            break;
        case "png":
        case "jpg":
        case "gif":
            midtermStr = '<div class="ty-fileType ty-file_jpg"></div>';
            break;
        default:
            midtermStr = '<div class="ty-fileType ty-file_other"></div>';
    }
    return midtermStr ;
}
laydate.render({elem: '#beginDate1', festival: true});
laydate.render({elem: '#beginTime00', istoday: true, festival: true});
laydate.render({elem: '#issueDate', istoday: true, festival: true});
laydate.render({elem: '#endTime', istoday: true, festival: true});
laydate.render({elem: '#reimburseBeginDate', istoday: true, festival: true});
laydate.render({elem: '#reimburseEndDate', istoday: true, festival: true});
laydate.render({elem: '#endTime', istoday: true, festival: true});
