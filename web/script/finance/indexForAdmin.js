/*
creator : 侯杏哲 时间:2017-03-20  完成数据查看页的功能 
 */

var searchLevel = 1 ; // 查询的日期：1-本日 ， 2-本月 ， 3-上月 ， 4-本年 ， 5-自定义查询
var searchMethod = 0 ; // 查询的方法：0-按时间查询 ， 1-按账户查询 , 2-按流水查询
let matchType = false;
let useInfo = null;
// 页面加载 
$(function(){
    useInfo = auth.getOrg();
    //输入框获取银行数据
    getOrgSonOrgs();
    getBank( $(".accountBank") );
    searchByLevel(1 , $("#theDay") );
    // creator : 侯杏哲 2017-03-20 给主页面的本日、本月、上月、本年 绑定点击事件
    $("#theDay").on("click", function(){ searchByLevel(1 , $(this) ); });
    $("#theMonth").on("click", function(){ searchByLevel(2 , $(this)  );  });
    $("#theYear").on("click", function(){ searchByLevel(4 , $(this)  );  });
    // creator : 侯杏哲 2017-03-20  给主页面的按时间查询按账户查询 绑定点击事件
    $("#searchByFlow").on("click", function(){ searchByMethod(2 , $(this)  );  });
    $("#searchByTime").on("click", function(){ searchByMethod(0 , $(this)  );  });
    $("#searchByAccount").on("click", function(){ searchByMethod(1 , $(this)  );  });
    // creator : 侯杏哲 2017-03-22  给主页面的自定义查询 绑定点击事件
    $("#searchDIY").on("click", function(){
        if ($("#searchStart").val() !== '' && $("#searchEnd").val() !== '') {
            searchByLevel(5 , $("#searchDIY_btn")  );
        } else {
            layer.msg('请输入时间范围')
        }
        return false
    });
    $("#chooseC").on("click","li",function(){
        $("#chooseC i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).find("i").attr("class", "fa fa-dot-circle-o")
    })
    $(".stakeholderCategory option").click(function () {
        let val = $(this).val();
        if(val == 0){
            return false
        }
        bounce.show($("#oppositeCorpDiv"))
        $("#oppositeCorpDiv").data("type", val)
        let ttl = ""
        let list = ""
        let str = ""
        if(val == '1'){
            ttl = '选择供应商'
            $("#oppositeCorpDiv .bonceHead span").html(ttl)
            $.ajax({
                "url":"../po/suppliers?state=3&type=1",
                "data":{},
                success:function(res){
                    list = res.data || [];
                    str = ``
                    list.forEach(function(item){
                        str += `
                        <li data-val="${item.id}">
                        	<i class="fa fa-circle-o"></i>
                        	<span>${item.full_name} ${ item.enabled ==0 ? '已暂停供货资格':'' }</span>
                        </li>
                        `
                    })
                    $("#chooseC").html(str);
                }
            })
        }else if(val == '2'){
            ttl = '选择公司职工'
            $("#oppositeCorpDiv .bonceHead span").html(ttl)
            $.ajax({
                "url":"../stakeholder/getUsers.do",
                success:function(res){
                    list = res.data.users || [];
                    str = ``
                    list.forEach(function(item){
                        str += `
                        <li data-val="${item.userID}">
                        	<i class="fa fa-circle-o"></i>
                        	<span>${item.userName} ${ item.mobile }  ${ item.isDuty ==2 ? '(已不在职)':'' }</span>
                        </li>
                        `
                    })
                    $("#chooseC").html(str);
                }
            })
        }else if(val == '3'){
            ttl = '选择其他单位或个人'
            $("#oppositeCorpDiv .bonceHead span").html(ttl)
            $.ajax({
                "url":"../stakeholder/getStakeholderList.do",
                success:function(res){
                    list = res.data.stakeholdersAvailable || [];
                    str = ``
                    list.forEach(function(item){
                        str += `
                        <li data-val="${item.id}">
                        	<i class="fa fa-circle-o"></i>
                        	<span>${item.name}</span>
                        </li>
                        `
                    })
                    $("#chooseC").html(str);
                }
            })
        }

    })
    $("#chooseC").on("click","li",function(){
        $("#chooseC i.fa-dot-circle-o").attr("class", "fa fa-circle-o");
        $(this).find("i").attr("class", "fa fa-dot-circle-o")
    })
    // 转账支票和承兑汇票的切换
    $("#update_method_3_").on("change",function () {
       if($(this).val() == 1){
           $("#detailKind_3_ .update_method_3_1").show();
           $("#detailKind_3_ .update_method_3_2").hide();
       }else if($(this).val() == 2){
           $("#detailKind_3_ .update_method_3_1").hide();
           $("#detailKind_3_ .update_method_3_2").show();
           $("#update_chequeNo_3_2").val("");
       }
    });
    // 内部支票和外部支票的切换
    $("#update_method_3_2").on("change",function () {
        if($(this).val() == 1){
            $("#detailKind_3_ .update_method_3_1_1").show();
            $("#detailKind_3_ .update_method_3_1_2").hide();
        }else if($(this).val() == 2){
            $("#detailKind_3_ .update_method_3_1_1").hide();
            $("#detailKind_3_ .update_method_3_1_2").show();
            $("#update_chequeNo_3_1").val("");
        }
    });

    // 银行的切换并对应设置对应的支票号
    $("#update_bankAccount_3_").on("click",function () {
        var accoundId = Number($(this).val()) ;
        $("#Inner").hide();$("#Outer").hide();
        //设置支票号
        setCheckNo(accoundId);
    });
    // 外部支票点击支票号输入框弹出支票号选择弹窗
    $("#update_chequeNo_3_1").on("click",function () {
        setBankVal( $(this) , 1 , 2 )
        bounce.show($("#outerCheck"));
    });
    // 承兑汇票点击汇票号输入框弹出支票号选择弹窗
    $("#update_chequeNo_3_2").on("click",function () {
        setBankVal( $(this) , 1 , 3 )
        bounce.show($("#acceptCheck"));
    });
    //支票号弹窗（汇票号弹窗）点击对应行选中单选按钮
    $("#acceptCheckCon,#outerCheckCon").on("click","tr",function () {
        $(this).siblings().children().children().removeClass("radioActive");
        $(this).children().eq(0).children().addClass("radioActive");
    });
    $("#update_checkNo_3_1").on("click",function () {
        if($(this).html() == ""||$(this).val() == null){
            $("#tip #mt_tip_ms").html("无可用支票，请选择其他银行账户！");
            bounce.show($("#tip"));
        }
    })
    //附件图片删除按钮
    $("#info_images").on("click",".imgClose",function () {
        $(this).parent().remove();
    });

    //处理报销修改请求提交后的反馈
    var state =$("#chargeSubmitStatus").text();
    if($.trim(state) == "" || state == undefined){

    }else if(state == 1){
        $("#tip #mt_tip_ms").html("修改申请已提交，请等待审批！");
        bounce.show($("#tip"));
    }else if(state == 0){
        $("#errorTip .tipWord").html("修改申请提交失败！");
        bounce.show($("#errorTip"))
    }else if(state == 2){
        $("#errorTip .tipWord").html("余额不足！");
        bounce.show($("#errorTip"))
    }else if(state == 3){
        $("#errorTip .tipWord").html("您已提交申请，请等待当前申请审批完成之后再提交申请！");
        bounce.show($("#errorTip"));
    }

    $("#reimburse").on('click', '[type="btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            case 'billInfo':
                bounce_Fixed.show($("#billInfo"))
                var reimburseBillId = $(this).parents("tr").attr("id")
                var trObj = $(this).parents("tr") ;
                $.ajax({
                    url: "../reimburseWindow/getReimburseBill.do",
                    data: {reimburseBillId: reimburseBillId},
                    success: function (res) {
                        var list = res["financeReimburseBillList"]
                        var isOneTr = false ; // 默认查看的票据是多张单行的
                        if(list && list.length == 1){
                            if(list[0]["itemCount"] == 1){
                                isOneTr = true;
                            }
                        }else{
                            isOneTr = true ;
                        }
                        if(isOneTr){    // 处理单行
                            $("#billInfo .oneRow").show().siblings().hide();
                            var tabStr = "";
                            var amountAll = 0, priceAll = 0
                            var number = 0
                            var goodsStr = ''
                            var billCatName = list[0]['billCatName'], indexTb = 0 ;
                            if(billCatName == "定额发票"){
                                indexTb = 1 ;
                            } else {
                                if(billCatName == "增值税普通发票"){
                                    $("#ticketText").html("货物或应税劳务、服务名称");
                                }else if(billCatName == "其他普通发票"){
                                    $("#ticketText").html("票据内容");
                                }
                            }
                            for(var i in list) {
                                var goods = list[i]['financeReimburseBillItemList'][0]
                                var issueDate = list[i].issueDate
                                if(indexTb === 1){
                                    goodsStr += "<tr data-type='2' id='"+ list[i]["id"] +"'>" +
                                        '   <td>'+list[i].feeCatName+(list[i].secondFeeCatName?'-'+list[i].secondFeeCatName : '')+'</td>' +
                                        '   <td>'+ goods.uniPrice +'</td>' +
                                        '   <td>'+goods.itemQuantity+'</td>' +
                                        '   <td>'+list[i].billAmount.toFixed(2)+'</td>' +
                                        '   <td>'+list[i].amount.toFixed(2)+'</td>' +
                                        "    <td><span class='ty-color-blue' type='btn' data-name='billPic'>发票图片</span>" +
                                        '       <span class="hd">'+ JSON.stringify(list[i]['pictures']) +'</span>'+
                                        "</td>" +
                                        "</tr>"
                                }else{
                                    goodsStr += "<tr data-type='2' id='"+ list[i]["id"] +"'>" +
                                        '   <td>'+list[i].feeCatName+(list[i].secondFeeCatName?'-'+list[i].secondFeeCatName : '')+'</td>' +
                                        "    <td>"+ list[i].billNo +"</td>" +
                                        "    <td>"+ issueDate +"</td>" +
                                        '   <td>'+goods.itemName+'</td>' +
                                        '   <td>'+goods.model+'</td>' +
                                        '   <td>'+goods.unit+'</td>' +
                                        '   <td>'+goods.itemQuantity+'</td>' +
                                        '   <td>'+list[i].billAmount.toFixed(2)+'</td>' +
                                        '   <td>'+list[i].amount.toFixed(2)+'</td>' +
                                        "    <td><span class='ty-color-blue' type='btn' data-name='billPic'>发票图片</span>" +
                                        '       <span class="hd">'+ JSON.stringify(list[i]['pictures']) +'</span>'+
                                        "</td>" +
                                        "</tr>"
                                }

                                amountAll = parseFloat(amountAll) + parseFloat(list[i].billAmount)
                                priceAll = parseFloat(priceAll) + parseFloat(list[i].amount)
                                number = parseInt(number) + parseInt(goods.itemQuantity)
                            }
                            $(".oneRow tbody:eq("+ indexTb +")").html(goodsStr).parent().show().siblings("table").hide()
                            var num = list.length
                            if(indexTb === 1){
                                num = Number(trObj.children(":eq(2)").html());
                                priceAll = Number(trObj.children(":eq(3)").html());
                                amountAll = Number(trObj.children(":eq(4)").html());
                            }
                            var summaryStr = list[0].billCatName + "共 "+ num +" 张，发票金额总计 "+ priceAll.toFixed(2) +" 元，实际支出（即您将报销）总计 "+ amountAll.toFixed(2) +" 元。"

                            $("#summary").html(summaryStr);

                            $("#billInfo .memo").html(list[0].memo) ;
                            $("#billInfo .singleAmount").hide()

                        }else{
                            $("#billInfo .oneRow").hide().siblings().show();
                            var quotaBill = list[0] ;
                            var goods= quotaBill.financeReimburseBillItemList
                            var billCatName = quotaBill.billCatName
                            $("#billInfo .billQuantity").hide()
                            $("#billInfo .singleAmount").show()
                            switch (billCatName) {
                                case '增值税专用发票':
                                case '增值税普通发票':
                                    $("#billInfo .VATGood").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice.toFixed(2)+'</td>' +
                                            '   <td>'+goods[i].price.toFixed(2)+'</td>' +
                                            '   <td>'+(goods[i].taxRate * 100).toFixed(2) +' %</td>' +
                                            '   <td>'+goods[i].taxAmount.toFixed(2)+'</td>' +
                                            '   <td>'+goods[i].amount.toFixed(2)+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .VATGood tbody").html(goodsStr)
                                    var issueDate = quotaBill.issueDate
                                    var quantity = quotaBill.relativeBillQuantity
                                    issueDate = issueDate.substring(0,4) +'年' + issueDate.substring(5,7) + '月'
                                    if (Number(quantity) > 1) {
                                        $("#billInfo .billQuantity").show().html('内容与本票据完全相同，且开票月份也为' + issueDate + '的票据总数量为' + quantity + '张')
                                    }
                                    break;
                                case '定额普通发票':
                                    var goodsStr = ''
                                    $("#billInfo .quotaGood").show().siblings().hide()
                                    $("#billInfo .quotaInvoice .billCatName").html('定额普通发票')
                                    $("#billInfo .quotaInvoice .feeCatName").html(quotaBill.feeCatName)
                                    var amount = 0
                                    var number = 0
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '    <td>'+goods[i].uniPrice.toFixed(2)+'</td>' +
                                            '    <td>'+goods[i].itemQuantity+'张</td>' +
                                            '    <td>'+goods[i].price.toFixed(2)+'</td>' +
                                            '</tr>'
                                        amount = parseFloat(amount) + parseFloat(goods[i].price)
                                        number = parseInt(number) + parseInt(goods[i].itemQuantity)
                                    }
                                    goodsStr += '<tr>' +
                                        '    <td>总计</td>' +
                                        '    <td>'+number+'张</td>' +
                                        '    <td>'+amount.toFixed(2)+'</td>' +
                                        '</tr>'
                                    $("#billInfo .quotaGood tbody").html(goodsStr)
                                    $("#billInfo .singleAmount").hide()
                                    break;
                                case '其他普通发票':
                                    $("#billInfo .otherGood").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice.toFixed(2)+'</td>' +
                                            '   <td>'+goods[i].price.toFixed(2)+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .otherGood tbody").html(goodsStr)
                                    var issueDate = quotaBill.issueDate
                                    var quantity = quotaBill.relativeBillQuantity
                                    issueDate = issueDate.substring(0,4) +'年' + issueDate.substring(5,7) + '月'
                                    if (Number(quantity) > 1) {
                                        $("#billInfo .billQuantity").show().html('内容与本票据完全相同，且开票月份也为' + issueDate + '的票据总数量为' + quantity + '张')
                                    }
                                    break;
                                case '收据':
                                    $("#billInfo .receipt").show().siblings().hide()
                                    var goodsStr = ''
                                    for(var i in goods) {
                                        goodsStr += '<tr>' +
                                            '   <td>'+goods[i].feeCatName+(goods[i].secondFeeCatName?'-'+goods[i].secondFeeCatName : '')+'</td>' +
                                            '   <td>'+goods[i].itemName+'</td>' +
                                            '   <td>'+goods[i].model+'</td>' +
                                            '   <td>'+goods[i].unit+'</td>' +
                                            '   <td>'+goods[i].itemQuantity+'</td>' +
                                            '   <td>'+goods[i].uniPrice.toFixed(2)+'</td>' +
                                            '   <td>'+goods[i].price.toFixed(2)+'</td>' +
                                            '</tr>'
                                    }
                                    $("#billInfo .receipt tbody").html(goodsStr)
                                    break;
                                default:
                                    console.log('不存在的值')
                            }
                            $("#billInfo .memo").html(quotaBill.memo);
                            $("#billInfo .singleAmount").show();
                            $("#billInfo .amount").html(quotaBill.itemAmount.toFixed(2)).show();

                        }

                    }
                })
                break;
            case 'billPic':
                var pictures = $(this).siblings(".hd").html()
                var list = JSON.parse(pictures)
                if (list.length > 0) {
                    for(var i = 0 ; i < list.length ; i++){
                        seePicture(list[i].path)
                    }
                } else {
                    layer.msg('未上传图片')
                }

                break;
        }
    })
    $("#billInfo").on('click', '[type="btn"]', function () {
        var name = $(this).data('name')
        switch (name) {
            case 'billPic':
                var pictures = $(this).siblings(".hd").html()
                var list = JSON.parse(pictures)
                if (list.length > 0) {
                    for(var i = 0 ; i < list.length ; i++){
                        seePicture(list[i].path)
                    }
                } else {
                    layer.msg('未上传图片')
                }
                break;
        }
    })

    $("body").on("click", "[type='btn']" ,function () {
        var name = $(this).data('name')
        switch (name) {
            case 'back':
                var origin = $(this).data("origin")
                switch (origin) {
                    case 'main':
                        $(this).parent().hide().siblings().show()
                        $(".searchType .btnnActive").click()
                        break;
                }
                break
        }
    })
    
});

// creator： 侯杏哲 2021-12-02 新增收款单位
function addOopBtn(obj) {
    bounce.show($("#oppositeAddDiv"));
    $("#oppositeAddDiv input").val("");
    $("#oppositeAddDiv textarea").val("");
    $(".showNum").html(`0 / 50`)
    $("#oppositeAddDiv").data('catobj',obj.siblings(".openOopBtn"))
    $("#oppositeAddDiv").data('userobj',obj.parents(".stakeholderCategoryContsiner").next().find("input"))
}
function addOopOK() {
    let name = $("#oopName").val();
    if(name.length === 0){
        layer.msg("请输入收款单位名称")
        return false
    }
    let remark = $("#oopMemo").val();
    bounce.cancel()
    $.ajax({
        "url":"../stakeholder/addStakeholder.do",
        "data":{ 'name':name , 'remark': remark },
        success:function (res) {
            let status = res.data.status;
            if(status == 0){
                layer.msg("操作失败")
            }else if(status == 1){
                layer.msg("操作成功");
                let id= res.data.financeStakeholder.id
                let catobj = $("#oppositeAddDiv").data('catobj')
                let userobj = $("#oppositeAddDiv").data('userobj')
                catobj.html('财务自行录入的收款单位（含个人）').data("type",3);
                userobj.val(name).data("id",id);
                $(".oop2").show();
            }else if(status == 2){
                layer.msg("系统里已有该收款单位！")

            }
        }
    })
}

// creator : 侯杏哲 2017-03-20  按照主页面上的时间按钮查询
function searchByLevel(level , obj  ){
    $(".dataInfo").hide().siblings().show();
    let searchOrg = $("#searchOrg").val();
    if (level === '2' || level === 2) {
        searchMethod = 2;
        $("#searchByTime").html('按日期查看');
        $("#searchByFlow").css('display','inline-block');
        $(".searchType").find('.btnn').eq(0).addClass('btnnActive').siblings().removeClass('btnnActive');
    }else{
        searchMethod = 0;
        $("#searchByTime").html('按时间查看');
        $("#searchByFlow").css('display','none');
        $(".searchType").find('.btnn').eq(1).addClass('btnnActive').siblings().removeClass('btnnActive');
    }
    obj.addClass("btnnActive").siblings().removeClass("btnnActive");
    if( level == 5){
        $("#theDay").removeClass("btnnActive").siblings().removeClass("btnnActive") ;  
        // $("#searchDIY_btn").click();
    }else{ $("#searchDIY").removeClass("btnnActive") ;   }
    searchLevel = level ;
    showMainData( level , searchMethod , searchOrg);
}

// creator : 侯杏哲 2017-03-20  按照主页面上的方式按钮查询
function searchByMethod(method , obj){
    let searchOrg = $("#searchOrg").val();
    obj.addClass("btnnActive").siblings().removeClass("btnnActive");
    searchMethod = method ;
    showMainData( searchLevel , searchMethod , searchOrg);
}

// 根绝主页面点击的时间，方式 查询数据并做相应的处理 
function showMainData( level , method , searchOrg){
    $("#goPrev").hide()
    let oid = null;
    if (matchType){$("#searchOrg").val() !== "" ? oid = $("#searchOrg").val(): "";}
    // 执行数据查询  state 1-本日，2-本月，3-上月，-4-本年，5-自定义 （为5时多传这两个时间） beginDate 开始时间 endDate结束时间
    var data = { "state":level , "oid": oid} ;
    var beginDate = "" ,  endDate = "";
    if(level == 5){
        beginDate = $("#searchStart").val() + " 00:00:00" ;  endDate = $("#searchEnd").val() + " 23:59:59";
        data = { "state":level , "beginDate":beginDate , "endDate": endDate , "oid": oid }
    }
    $("#mainDataNav").show().siblings().hide() ;
    $("#mainDataNav").parent().show().siblings(".dataInfo").hide();
    $("#mainDataNav").parent().siblings(".dataList").show();
    // 主页面统计的显示
    $.ajax({
        url:"../data/getAllAccountData.do" ,
        data: data  ,
        success:function (dataR) {
            var allPreviousBalance = dataR.data["allPreviousBalance"];           // 总上期余额
            var allCredit = dataR.data["allCredit"];                               // 总收入
            var allDebit = dataR.data["allDebit"];                                 // 总支出
            var allBalance = dataR.data["allBalance"];                             // 总当前余额
            beginDate =  ( new Date( dataR.data["beginDate"] ).format('yyyy-MM-dd') ) ;                       // 开始时间// 结束时间
            if(dataR.data["endDate"] != null){
                endDate = ( new Date( dataR.data["endDate"] ).format('yyyy-MM-dd') ) ;
            }else{
                endDate = "";
            }
            if(level == 1){
                $("#betweenDate").html("今天是" + beginDate );
            }else{
                $("#betweenDate").html(beginDate + " ~ " + endDate );
            }
            $("#allBalance").html((parseFloat(allBalance)).toFixed(2));
            $("#allCredit").html((parseFloat(allCredit)).toFixed(2));
            $("#allDebit").html((parseFloat(allDebit)).toFixed(2));
            $("#allPreviousBalance").html((parseFloat(allPreviousBalance)).toFixed(2));
        },
        error:function(){
            bounce.show($("#tip")) ;
            $("#mt_tip_ms").html("链接错误，请刷新重试！");
        }
    });   
    // 获取并显示明细数据
    if(method === 0){ // 按时间查询 ，
        $.ajax({
            url:"../data/getAllAccountDataByTime.do" ,
            data: data ,
            type : "post" ,
            dataType : "json" ,
            success:function (data) {
                // type A-跨年，B-跨月，C-本月，D-本日 ， （本日的时候取 明细列表accountDetailList 其余都取accountPeriodList列表）
                var type = data.data["type"] ;
                var list = null ;
                if(type == "D"){ list = data.data["accountDetailList"];  }else{ list = data.data["accountPeriodList"];  }
                // 显示页面
                switch (level){
                    case 1 :
                        dataByLevel( level , method );
                        addInfoList({ "list" : list , "insertObj": $("#dataInfo_tbl") , "method":method , "level":level }) ;
                        break ;
                    case 2 :
                    case 3 :
                    case 4 :
                        dataByLevel( level , method );
                        addList({ "list" : list , "insertObj": $("#dataByLevel_tbl") , "method":method , "type":type , "level":level ,"method":method ,"beginDate":beginDate , "endDate": endDate }) ;
                        break ;
                    case 5 :
                        dataByLevel5( type , method );
                        if(type == "D"){
                            addInfoList({ "list" : list , "insertObj": $("#dataInfo_tbl") , "method":method   }) ;
                        }else{
                            addList({ "list" : list , "insertObj": $("#dataByLevel_tbl") , "method":method , "type":type , "level":level , "beginDate":beginDate , "endDate": endDate }) ;
                        }
                        break ;
                    default :
                        break ;
                }
            },
            error:function(){
                bounce.show( $("#tip") ) ; $("#mt_tip_ms").html("链接错误！");
            }
        });
    }else if(method === 1){ // 按账户查询
        $.ajax({
            url:"../data/getDataByAccount.do" ,
            data: data ,
            type : "post" ,
            dataType : "json" ,
            success:function (dataR) {
                var str = "" ; var monthOrDay = 0 ; 
                switch ( level ){  // level : 1-本日，2-本月，3-上月，-4-本年，5-自定义    // // monthOrDay 1-年进月，2-月进日，3-日进明细
                    case 1 :  monthOrDay = 3 ;  break ;
                    case 2 :  monthOrDay = 2 ;  break ;
                    case 3 :  monthOrDay = 2 ;  break ;
                    case 4 :  monthOrDay = 1 ;  break ;
                    case 5 :  monthOrDay = "" ; break ;
                    default : break ; 
                }
                var  beginDate = dataR.data["beginDate"];
                var  endDate = dataR.data["endDate"];
                var  accountPeriod1 = dataR.data["accountPeriod1"]; // 现金账户的信息
                // {accountName:账号，bankName：银行名称，isBasic：是否为基本户，debit：支出，credit：收入，previousBalance:上期余额，balance：本期余额，accountId_：账户id}
                var accountPeriod2 = dataR.data["accountPeriod2"]; // 银行汇总账户的信息
                // {debit：支出，credit：收入，previousBalance:上期余额，balance：本期余额}；
                var accountPeriod3 = dataR.data["accountPeriod3"]; // 所有银行的信息
                let orgNameStr = ``,orgNameStr2 = ``,orgNameStr3 = ``;
                // {accountName:账号，bankName：银行名称，isBasic：是否为基本户，debit：支出，credit：收入，previousBalance:上期余额，balance：本期余额，accountId_：账户id}
                if (matchType) {
                    orgNameStr = `<td>总机构</td>`;
                }
                if(accountPeriod1){
                    for(let i =0;i<accountPeriod1.length;i++){
                        if (matchType) {
                            orgNameStr = `<td> ${handleNull(accountPeriod1[i].orgName)} </td>`;
                        }
                        accountPeriod1[i]["beginDate"] = beginDate ;
                        accountPeriod1[i]["endDate"] = endDate ;
                        accountPeriod1[i]["financeAccountId"] = accountPeriod1[i]["accountId"]  ;
                        str += "<tr>" +
                            "<td class='textLeft'>现金/备用金</td>" +orgNameStr+
                            "<td>"+  (parseFloat(accountPeriod1[i]["previousBalance"])).toFixed(2) +"</td>" +
                            "<td>"+  (parseFloat(accountPeriod1[i]["credit"])).toFixed(2) +"</td>" +
                            "<td>"+  (parseFloat(accountPeriod1[i]["debit"])).toFixed(2) +"</td>" +
                            "<td>"+  (parseFloat(accountPeriod1[i]["balance"])).toFixed(2) +"</td>" +
                            "<td>"+
                            "<span class='ty-color-blue' onclick='showDetailList($(this) ,"+ monthOrDay +")'>查看</span><span class='hd'>"+ JSON.stringify(accountPeriod1[i]) +"</span>"+
                            "</td>"+
                            "</tr>" ;
                    }
                }else {
                    str += "<tr>" +
                        "<td class='textLeft'>现金/备用金</td>" +orgNameStr+
                        "<td>"+  0 +"</td>" +
                        "<td>"+  0 +"</td>" +
                        "<td>"+  0 +"</td>" +
                        "<td>"+  0 +"</td>" +
                        "<td>"+
                        "<span class='ty-color-blue' onclick='showDetailList($(this) , "+ monthOrDay +")'>查看</span><span class='hd'>"+ JSON.stringify(accountPeriod1) +"</span>"+
                        "</td>"+
                        "</tr>" ;
                }
                if(accountPeriod2){
                    accountPeriod2["beginDate"] = beginDate ;
                    accountPeriod2["endDate"] = endDate ;
                    accountPeriod2["accountId_"] = 0 ;
                    accountPeriod2["financeAccountId"] = accountPeriod2["accountId"]  ;
                    matchType? orgNameStr2 = `<td> ${handleNull(accountPeriod2.orgName)} </td>`: "";

                    str += "<tr>" +
                        "<td class='textLeft'>银行账户汇总</td>" + orgNameStr2+
                        "<td>"+  (parseFloat(accountPeriod2["previousBalance"])).toFixed(2) +"</td>" +
                        "<td>"+  (parseFloat(accountPeriod2["credit"])).toFixed(2) +"</td>" +
                        "<td>"+  (parseFloat(accountPeriod2["debit"])).toFixed(2) +"</td>" +
                        "<td>"+  (parseFloat(accountPeriod2["balance"])).toFixed(2) +"</td>" +
                        "<td>"+
                        "<span class='ty-color-blue' onclick='showDetailList($(this) , "+ monthOrDay +")'>查看</span><span class='hd'>"+ JSON.stringify(accountPeriod2) +"</span>"+
                        "</td>"+
                        "</tr>" ;
                }else{
                    str += "<tr>" +
                        "<td class='textLeft'>银行账户汇总</td>" + orgNameStr2+
                        "<td>"+  0 +"</td>" + 
                        "<td>"+  0 +"</td>" + 
                        "<td>"+  0 +"</td>" + 
                        "<td>"+  0 +"</td>" + 
                        "<td>"+
                        "<span class='ty-color-blue' onclick='showDetailList($(this) , "+ monthOrDay +")'>查看</span><span class='hd'>"+ JSON.stringify(accountPeriod2) +"</span>"+
                        "</td>"+
                        "</tr>" ;
                }
               if(accountPeriod3){
                    for(var i=0; i<accountPeriod3.length ; i++){
                        matchType? orgNameStr3 = `<td> ${handleNull(accountPeriod3[i].orgName)} </td>`: "";
                        accountPeriod3[i]["beginDate"] = beginDate ;  
                        accountPeriod3[i]["endDate"] = endDate ;
                        accountPeriod3[i]["financeAccountId"] = accountPeriod3[i]["accountId"]  ;

                        let bankNameStr = accountPeriod3[i]["accountRealName"] + ' ' + formatAccount(accountPeriod3[i]["accountName"])+ ' ' + accountPeriod3[i]["bankName"] ;
                        if(accountPeriod3[i].isPublic == 1){
                            bankNameStr = formatAccount(accountPeriod3[i]["accountName"])+ ' ' + accountPeriod3[i]["bankName"] ;
                        }
                        str += "<tr>" +
                            "<td class='ind2'>"+ bankNameStr +"</td>" +orgNameStr3+
                            "<td>"+  (parseFloat(accountPeriod3[i]["previousBalance"])).toFixed(2) +"</td>" +
                            "<td>"+  (parseFloat(accountPeriod3[i]["credit"])).toFixed(2) +"</td>" +
                            "<td>"+  (parseFloat(accountPeriod3[i]["debit"])).toFixed(2) +"</td>" +
                            "<td>"+  (parseFloat(accountPeriod3[i]["balance"])).toFixed(2) +"</td>" +
                            "<td>"+
                            "<span class='ty-color-blue' onclick='showDetailList($(this) , "+ monthOrDay +")'>查看</span><span class='hd'>"+ JSON.stringify(accountPeriod3[i]) +"</span>"+
                            "</td>"+
                            "</tr>" ;
                    }
                }
                $("#dataByAccount_btl").html(str);
                if(level == 5){
                    dataByLevel5(level , method);
                }else{
                    dataByLevel(level , method);
                }
            },
            error:function(){ bounce.show( $("#tip") ) ; $("#mt_tip_ms").html("链接错误！"); }
        });
    }else if(method === 2){ // 按流水查询
        $.ajax({
            url:"../data/getDetailByMonth.do" ,
            data: {"oid": oid} ,
            type : "post" ,
            dataType : "json" ,
            success:function (data) {
                var html = '';
                var flowList = data.data['accountDetails'];
                // 显示页面
                dataByLevel( level , method );
                addInfoList({ "list" : flowList , "insertObj": $("#dataByLevel_tbl") , "method":method , "level":level }) ;
            },
            error:function(){
                bounce.show( $("#tip") ) ; $("#mt_tip_ms").html("链接错误！");
            }
        });
    }
    $("#dataScan").attr( "onclick" , "showMainData( "+ level +" , "+ searchMethod +" ); $(this).parent().nextAll().remove(); " ) ;
}
// create:hxz 2022-03-07 格式化 账号
function formatAccount(account){
    let accountStr = `****`
    if(account.length >3){
        accountStr += account.substr(account.length-4,4)
    }else{
        let n = 4-account.length ;
        for(let i = 0 ; i < n; i++){
            accountStr += '*'
        }
        accountStr += account
    }
    return accountStr
}
// creator : 侯杏哲 2017-03-20 表格中的查看，点击后跳转查询页面
function showDetailList( obj , monthOrDay ){
    $("#goPrev").show()
    $(".dataInfo").hide().siblings().show();
    let oid = null;
    if (matchType) {
        $("#searchOrg").val() !== "" ? oid = $("#searchOrg").val(): "";
    }
    var data = {"oid": oid} , date = "";  // monthOrDay 1-年进月，2-月进日，3-日进明细
    var scanStr = obj.next(".hd").html() ;
    if(!scanStr ||　scanStr=="null"){
        bounce.show( $("#tip") ) ; $("#mt_tip_ms").html("无明细可查看！"); return false ; 
    }
    var scanInfo = JSON.parse( scanStr ) ;
    data["monthOrDay"] = monthOrDay ;
    data["beginDate"] = new Date(scanInfo["beginDate"]).format('yyyy-MM-dd hh:mm:ss') ;
    data["endDate"] = new Date(scanInfo["endDate"]).format('yyyy-MM-dd hh:mm:ss') ;
    // data["fid"] = scanInfo["accountId_"] ;  // 账户id。（获取所有账户时不传，或传fid=null）
    // data["fid"] = scanInfo["accountId"] ;  // 账户id。（获取所有账户时不传，或传fid=null）
    if(searchMethod > 0){
        data["fid"] = scanInfo["financeAccountId"] ;  // 账户id。（获取所有账户时不传，或传fid=null）
    }

    if( data["monthOrDay"] == "2" ){ data["endDate"] = getEndDate( data["beginDate"] , 2 ); }
    else if( data["monthOrDay"] == "3" ){ data["endDate"] = getEndDate( data["beginDate"] , 3 ); }
    if( !data["endDate"] ){ data["endDate"] = data["beginDate"] ;   }
    if(obj.parent().attr("class") == "navTxt"){ // 点击导航栏
        date = obj.html();
        obj.parent().nextAll().remove();
    }else{ // 数据查看
        date = obj.parent().siblings(":eq(0)").html();
        var navStr = "<span class='nav_' > / </span><span class='navTxt'><a onclick='showDetailList($(this) ,"+ monthOrDay +" )'>"+ date +"</a><i class='hd'>"+ scanStr +"</i></span>";
        $("#navControl").append(navStr);
    }
    $.ajax({
        url:"../data/getAccountMonthOrDay.do" ,
        data: data ,
        type : "post" ,
        dataType : "json" ,
        success:function (data) {
            // type A-跨年，B-跨月，C-本月，D-本日 ， （本日的时候取 明细列表accountDetailList 其余都取accountPeriodList列表）
            // allBalance 总余额 allCredit总收入 allDebit总支出 allPreviousBalance总上期余额 beginDate开始 endDate结束
            var type = data.data["type"] ;
            var beginDate = data.data["beginDate"];
            var endDate = data.data["endDate"];
            var financeAccountId = data.data["financeAccountId"];
            var timeStr = "" ;
            switch(monthOrDay){
                case 1 : timeStr= cutLen(beginDate , 10) + " ~ " + cutLen(endDate , 10) ;  break ;
                case 2 : timeStr= cutLen(beginDate , 7);   break ;
                default:  timeStr= cutLen(beginDate , 10) ;  break ;
            }
            $("#nav5").html(timeStr);
            $("#nav6").html((parseFloat(data.data["allPreviousBalance"])).toFixed(2));
            $("#nav7").html((parseFloat(data.data["allCredit"])).toFixed(2));
            $("#nav8").html((parseFloat(data.data["allDebit"])).toFixed(2));
            $("#nav9").html((parseFloat(data.data["allBalance"])).toFixed(2));
            var list = null ;
            if(!type && !monthOrDay){
                list = data.data["accountPeriodList"];
                addList({ "list" : list , "insertObj": $("#dataByLevel_tbl") , "type":type , "beginDate":beginDate , "endDate": endDate ,"financeAccountId": financeAccountId , "monthOrDay":monthOrDay }) ;
            }else{
                if(type == "D"){
                    list = data.data["accountDetailList"];
                    addInfoList({ "list" : list , "insertObj": $("#dataInfo_tbl")  }) ;
                    monthOrDay = 3
                }else{
                    list = data.data["accountPeriodList"];
                    addList({ "list" : list , "insertObj": $("#dataByLevel_tbl") , "type":type , "beginDate":beginDate , "endDate": endDate , "financeAccountId": financeAccountId , "monthOrDay":monthOrDay , "fid": scanInfo["accountId_"]   }) ;
                }
                // 显示页面
                dataAndNavByLevel(  monthOrDay );
            }

        },
        error:function(){
            bounce.show( $("#tip") ) ; $("#mt_tip_ms").html("链接错误！");
        }
    });

}

// creator : 侯杏哲 2017-03-20 向详情的表格中插入数据
function addInfoList( obj ){
    var list = obj["list"] ;
    var insertObj = obj["insertObj"] ;
    var str = "" ;
    let orgNameStr = ``;
    if(list){
        for(var i in list){
            // 加入借款的内容
            var id          = list[i].id          //数据类型
            var type        = list[i].type          //数据类型
            var source      = list[i].source        //区分是借款的字段，为2则是借款数据,3=回款数据  6的是工资管理的
            var reimburseId = list[i].reimburseId   //报销id，也用来判断是否为报销数据
            var business    = list[i].business      //借款id
            var businessType    = list[i].businessType      // 0101-常规借款借入,0102-常规借款还款，0103-常规借款借出，0104-常规借款收款
            var modityStatus= list[i].modityStatus  //判断修改是否被禁用
            var controlStr  = "";
            var credit = 0 , debit = 0 ;
            // 当businessType 为0101和0103时， business对应id；当businessType对应0102和0104时，business对应repaymentID
            debit = list[i]["debit"] ; credit = list[i]["credit"] ;
            if(!debit){ debit = "--"; }
            if(!credit){ credit = "--"; }
            if (type !== '1') {
                if (source && source === '2') {
                    if(businessType == "0101" || businessType == "0103"){
                        var param = { 'id' : business }
                    }else {
                        // 如果是支出的话
                        var param = { 'repaymentID' : business }
                    }
                    param = JSON.stringify(param);
                    controlStr =    "<span class='ty-color-blue' onclick='seeOrdLoanDetail(" + param + " )'>查看</span>" +
                                    "<span class='ty-color-gray'>修改</span>" ;
                } else if (source === '3') {
                    controlStr ="<span class='ty-color-blue' onclick='seeCollectDetail(" + business + " )'>查看</span>";
                    controlStr += "<span class='ty-color-gray'>修改</span>" ;
                } else if (source === '6') {
                    controlStr ="<span class='ty-color-blue' onclick='seeWageDetail($(this))'>查看</span>" +
                        "<span class='ty-color-gray'>修改</span>" ;
                } else if (source === '8') {
                    controlStr ="<span class='ty-color-gray' >查看</span>" +
                        "<span class='ty-color-gray'>修改</span>" ;
                }else{
                    if (reimburseId) {
                        controlStr =    "<span class='ty-color-blue' onclick='getReimburseDetail(" + reimburseId + " )'>查看</span>" +
                                        "<span class='ty-color-gray'>修改</span>" ;
                    } else {
                        if(modityStatus == 2 || modityStatus == 3 || useInfo.orgType === 1 && list[i].orgName !== useInfo.name){
                            controlStr =    "<span class='ty-color-gray' disabled onclick='getDetails($(this) ,"+ id +" , \"scan\" )'>查看</span>" +
                                "<span class='ty-color-gray'>修改</span>" ;
                        }else{
                            controlStr =    "<span class='ty-color-blue' onclick='getDetails($(this) ,"+ id +" , \"scan\" )'>查看</span>";
                            if (Number(list[i].method) === 4 || Number(list[i].method) === 3){
                                //method;//0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
                                controlStr += "<span class='ty-color-blue' onclick='updateFromBank($(this))'>修改</span>" ;
                            } else {
                                controlStr += "<span class='ty-color-orange' onclick='getDetails($(this) ,"+ id +" , \"update\" )'>修改</span>"
                            }
                            controlStr += "<span class='hd'>"+JSON.stringify(list[i])+"</span>" ;
                        }
                    }
                }
            }
            if (matchType) {orgNameStr = `<td> ${handleNull(list[i].orgName)} </td>`;}
            if (!(list[i].credit === 0 && list[i].debit === 0)) {
                str +=  "<tr>" +
                    "<td>"+ new Date(list[i].createDate).format('yyyy-MM-dd hh:mm:ss') +" </td>"+
                    "<td>"+ (list[i].summary || '') +" </td>" + orgNameStr +
                    "<td>"+ (list[i].credit || '--') +"</td>" +
                    "<td>"+ (list[i].debit || '--') +"</td>" +
                    "<td>"+ (list[i].auditorName|| '--') +" </td>" +
                    "<td >" +controlStr +
                    "<span class='hd detailid'>"+ list[i].id +" </span>" +
                    "<span class='hd businessHistory'>"+ list[i].businessHistory +" </span>" +
                    "</td>" +
                    "</tr>"
            }
        }

    }
    insertObj.html( str ) ;
}

// creator : 侯杏哲 2017-03-20 向列表表格中插入数据
function seeWageDetail( obj ){
    $("#goPrev").show()
    var navStr = "<span class='nav_'> / </span><span class='navTxt'>"+ obj.parent().siblings(":eq(1)").html() ;
    var navCredit = obj.parent().siblings(":eq(2)").html() ;
    var navDebit = obj.parent().siblings(":eq(3)").html() ;
    if( isNaN( Number(navCredit) )  ){ navStr+= "(支出)明细";  }else{ navStr += "(收入)明细";  }
    navStr += "</span>" ;
    $("#navControl").append(navStr);

    dataInfo( $("#detailKind_wage")) ;
    let businessHistory = obj.siblings('.businessHistory').html();


    $.ajax({
        'url':"../salary/getPayHistory.do",
        'data':{ "businessHistory": businessHistory },
        success:function (data) {
            let res = data.data
            let list = res.mapList || [];
            let info = res.personnelPayHistory || {};
            let financeAccount = res.financeAccount || {};

            $("#detailKind_wage .number_i").html(list.length + '人');
            $("#detailKind_wage .amount_i").html(info.pay.toFixed(2));
            $("#detailKind_wage .date_i").html(new Date(info.payTime).format("yyyy-MM-dd") );
            let bankNameStr = ``
            if(info.payWay == 2){
                bankNameStr = financeAccount["name"] + ' ' + formatAccount(financeAccount["account"])+ ' ' + financeAccount["bankName"] ;
                if(financeAccount.isPublic == 1){
                    bankNameStr = formatAccount(financeAccount["account"])+ ' ' + financeAccount["bankName"] ;
                }
            }else{
                bankNameStr = `现金支付`
            }
            $("#detailKind_wage .method_i").html(bankNameStr);
            $("#detailKind_wage .createD").html(`${info.createName} ${ new Date(info.createDate).format("yyyy-MM-dd hh:mm:ss") }`);
            let yearMonth = info.period.substr(0,4) + '年' + info.period.substr(4,2) + '月'
            $("#detailKind_wage .date_t").html("支出日期 ")
            $("#detailKind_wage .number_t").html("职工人数 ")
            $("#detailKind_wage .method_t").html("支出方式 ")
            switch (info.type){
                case 1: // 工资
                    $("#detailKind_wage .date_t").html("发放日期 ")
                    $("#detailKind_wage .number_t").html("发放人数 ")
                    $("#detailKind_wage .method_t").html("发放方式 ")
                    $("#detailKind_wage .amount_t").html("本次实发总额 ")
                    $("#tt3").html(`工资所属月份：${yearMonth}`);
                    $("#detailKind_wage .placeTitle").html("实发")

                    break;
                case 2: // 个人所得税
                    $("#tt3").html(`${yearMonth}个人所得税明细表`);
                    $("#detailKind_wage .amount_t").html("本月职工应缴个所税总额 ")
                    $("#detailKind_wage .placeTitle").html("个人所得税")
                    break;
                case 3: // 社保
                    $("#tt3").html(`${yearMonth}应缴社保明细表`);
                    $("#detailKind_wage .amount_t").html("本月职工应缴社保总额 ")
                    $("#detailKind_wage .placeTitle").html("应缴社保")
                    break;
                case 4: // 公积金
                    $("#tt3").html(`${yearMonth}应缴公积金明细表`);
                    $("#detailKind_wage .amount_t").html("本月职工应缴公积金总额 ")
                    $("#detailKind_wage .placeTitle").html("应缴公积金")
                    break;
            }

            let str2 = ``;
            list.forEach((item)=>{
                str2 += `
                            <tr>
                                <td>${ item.userName }</td>
                                <td>${ item.mobile }</td>
                                <td>${ item.departName || '' } ${ item.postName && item.departName ? '/' : ''  } ${ item.postName || '' }</td>
                                <td>${ item.factPay.toFixed(2) }</td>
                            </tr>
                    `
            })
            $("#scanSalary tbody tr:gt(0)").remove()
            $("#scanSalary tbody").append(str2)

        }
    })

}
// creator : 侯杏哲 2017-03-20 向列表表格中插入数据
function addList( obj ){
    var monthOrDay = obj["monthOrDay"] ; // 1-年进月，2-月进日，3-日进明细
    var list = obj["list"] ;
    var insertObj = obj["insertObj"] ;
    var type = obj["type"] ;  // type A-跨年，B-跨月，C-本月，D-本日
    var level = obj["level"] ;  // 1-本日，2-本月，3-上月，-4-本年，5-自定义
    var beginDate = obj["beginDate"] ;
    var endDate = obj["endDate"] ;
    var fid = obj["fid"] ;
    var financeAccountId = obj["financeAccountId"] ;
    var timeLen = 4 , timeNum = 1 ;

    if(monthOrDay){ // 有 monthOrDay
        switch(monthOrDay){
            case 1 : timeLen = 7 ; monthOrDay = 2 ;  break ;
            case 2 : timeLen = 10 ; monthOrDay = 3 ;  break ; 
            default:  timeLen = 4 ; monthOrDay = 3 ; break ;
        }
    }else{  // 首次点查看
        monthOrDay = 0 ;
        switch (level){
            case 1 : timeLen = 19 ;  break ;
            case 2 :
                var method = obj["method"];
                if(method == 2){
                    timeLen = 19 ;
                    monthOrDay = 0 ;
                }else{
                    timeLen = 10 ;
                    monthOrDay = 3 ;
                }
                break ;
            case 3 : timeLen = 10 ; monthOrDay = 3 ; break ;
            case 4 : timeLen =  7 ; monthOrDay = 2 ; break ;
            default :
                if(type == "A"){ timeLen = 10 ; timeNum = 2 ; monthOrDay = 1 ;   }
                else if(type == "B"){ timeLen =  7 ; monthOrDay = 2 ;  }
                else if(type == "C"){ timeLen =  10 ; monthOrDay = 3 ;  }
                else if(type == "D"){ timeLen =  19 ;   }
                break ;
        }
    }

    var str = "" ;
    if(list){
        for(var i=0; i<list.length ; i++){
            list[i]["accountId_"] = fid  ;
            list[i]["financeAccountId"] = financeAccountId  ;
            var timeStr = cutLen(list[i]["beginDate"] , timeLen) ;
            let orgNameStr = ``;
            if(timeNum == 2 ){  timeStr += " ~ " +  cutLen( list[i]["endDate"] , timeLen ) ;  }
            if (matchType) {orgNameStr = `<td> ${handleNull(list[i].orgName)} </td>`;}
            str += "<tr>" +
                    "<td>"+ timeStr +"</td>" +   
                    "<td>"+ list[i]["previousBalance"] +"</td>" + orgNameStr +
                    "<td>"+ list[i]["credit"] +"</td>" +   
                    "<td>"+ list[i]["debit"] +"</td>" +  
                    "<td>"+ list[i]["balance"] +"</td>" +  
                    "<td>" +
                        "<span class='ty-color-blue' onclick='showDetailList( $(this) , "+ monthOrDay +")'>查看</span>" +
                        "<span class='hd'>"+ JSON.stringify(list[i]) +"</span>" +
                    "</td>" +
                "</tr>"
        }
    }
    insertObj.html( str ) ;
}

// creator : 侯杏哲 2017-03-20 展示主页面: 前四种
function dataByLevel( level , method ){ // level 表示的是当前查询的等级
    $("#mainDataNav").show().siblings().hide();
    if( method == 0){ // 按时间查询
        switch (level){ // 1-本日 ， 2-本月 ， 3-上月 ， 4-本年
            case 1 :
                $("#dataInfo").show().siblings().hide();
                $("#prevBalance").html("昨日余额"); $("#curGet").html("今日收入"); $("#curOut").html("今日支出"); $("#curBalance").html("当前余额");
                break ;
            case 2 :
                $("#dataByLevel").show().siblings().hide();
                $("#td0").html("日期");$("#td1").html("昨日余额");$("#td2").html("今日收入");$("#td3").html("今日支出");$("#td4").html("今日余额");
                $("#prevBalance").html("上月余额"); $("#curGet").html("本月收入"); $("#curOut").html("本月支出"); $("#curBalance").html("本月余额");
                break ;
            case 3 :
                $("#dataByLevel").show().siblings().hide();
                $("#td0").html("日期");$("#td1").html("昨日余额");$("#td2").html("今日收入");$("#td3").html("今日支出");$("#td4").html("今日余额");
                $("#prevBalance").html("上期余额"); $("#curGet").html("上月收入"); $("#curOut").html("上月支出"); $("#curBalance").html("上月余额");
                break ;
            case 4 :
                $("#dataByLevel").show().siblings().hide();
                $("#td0").html("日期");$("#td1").html("上月余额");$("#td2").html("本月收入");$("#td3").html("本月支出");$("#td4").html("本月余额");
                $("#prevBalance").html("上期余额"); $("#curGet").html("本年收入"); $("#curOut").html("本年支出"); $("#curBalance").html("本年余额");
                break ;
            default :
                break ;
        }
    }else if(method == 1){ // 按账户查询 
        $("#dataByAccount").show().siblings().hide();
        switch (level){ // 1-本日 ， 2-本月 ， 3-上月 ， 4-本年
            case 1 :
                $("#ac1").html("昨日余额");$("#ac2").html("今日收入");$("#ac3").html("今日支出");$("#ac4").html("当前余额");
                $("#prevBalance").html("昨日余额"); $("#curGet").html("今日收入"); $("#curOut").html("今日支出"); $("#curBalance").html("当前余额");
                break ;
            case 2 :
                $("#ac1").html("上月余额");$("#ac2").html("本月收入");$("#ac3").html("本月支出");$("#ac4").html("本月余额");
                $("#prevBalance").html("上月余额"); $("#curGet").html("本月收入"); $("#curOut").html("本月支出"); $("#curBalance").html("本月余额");
                break ; 
            case 3 :
                $("#ac1").html("上期余额");$("#ac2").html("上月收入");$("#ac3").html("上月支出");$("#ac4").html("上月余额");
                $("#prevBalance").html("上期余额"); $("#curGet").html("上月收入"); $("#curOut").html("上月支出"); $("#curBalance").html("上月余额");
                break ;
            case 4 :
                $("#ac1").html("上期余额");$("#ac2").html("本年收入");$("#ac3").html("本年支出");$("#ac4").html("本年余额");
                $("#prevBalance").html("上期余额"); $("#curGet").html("本年收入"); $("#curOut").html("本年支出"); $("#curBalance").html("本年余额");
                break ;
            default :
                break ;
        }
    }else if(method == 2) { // 按流水查询
        $("#dataByLevel").show().siblings().hide();
        $("#td0").html("时间");$("#td1").html("摘要");$("#td2").html("收入");$("#td3").html("支出");$("#td4").html("经手人");
        $("#prevBalance").html("上月余额"); $("#curGet").html("本月收入"); $("#curOut").html("本月支出"); $("#curBalance").html("本月余额");
    }
}

// creator : 侯杏哲 2017-03-20 展示主页面: 自定义查询
function dataByLevel5( type , method ){  // type : A-跨年，B-跨月，C-本月，D-本日 ， method : 1 按账户查询 ， 0 按时间查询
    if( method == 0 ){
        $("#prevBalance").html("上期余额"); $("#curGet").html("本期收入"); $("#curOut").html("本期支出"); $("#curBalance").html("本期余额");
        switch( type ){
            case "D" :
                $("#dataInfo").show().siblings().hide();
                break ;
            case "C" :
            case "B" :
            case "A" :
                $("#dataByLevel").show().siblings().hide();
                $("#ac1").html("上期余额");$("#ac2").html("本期收入");$("#ac3").html("本期支出");$("#ac4").html("本期余额");
                break ;
            default :
                break ;
        }
    }else if(method == 1){
        $("#dataByAccount").show().siblings().hide();
        $("#ac1").html("上期余额");$("#ac2").html("本期收入");$("#ac3").html("本期支出");$("#ac4").html("本期余额");
    }

}

// creator : 侯杏哲 2017-03-20 返回统计和明细：点击查看的
function dataAndNavByLevel(monthOrDay ){ // 1-年进月，2-月进日，3-日进明细
    $("#duringLevel").show().siblings().hide();
    switch( Number(monthOrDay) ){
        case 3 :
            $("#dataInfo").show().siblings().hide();
            $("#nva1").html("昨日余额");$("#nva2").html("当日收入");$("#nva3").html("当日支出");$("#nva4").html("当前余额");
            break ;
        case 2 :
            $("#dataByLevel").show().siblings().hide();
            $("#td1").html("昨日余额");$("#td2").html("今日收入");$("#td3").html("今日支出");$("#td4").html("今日余额");
            $("#nva1").html("上月余额");$("#nva2").html("本月收入");$("#nva3").html("本月支出");$("#nva4").html("本月余额");
            break ;
        case 1 :
            $("#dataByLevel").show().siblings().hide();
            $("#td1").html("上月余额");$("#td2").html("本月收入");$("#td3").html("本月支出");$("#td4").html("本月余额");
            $("#nva1").html("上期余额");$("#nva2").html("本年收入");$("#nva3").html("本年支出");$("#nva4").html("本年余额");
            break ;
        default: // 没有传monthOrDay ，目前只有按账户自定义查询会出现
            $("#dataByLevel").show().siblings().hide();
            $("#td1").html("上期余额");$("#td2").html("本期收入");$("#td3").html("本期支出");$("#td4").html("本期余额");
            $("#nva1").html("上期余额");$("#nva2").html("本期收入");$("#nva3").html("本期支出");$("#nva4").html("本期余额");
            break ;
    }
}

// creator : 侯杏哲 2017-03-20 返回统计和明细- 按详情，展示某一条数据的详情
function scanDataInfo(){
    $(".dataNav").hide();  $(".dataList").hide();  $(".dataInfo").show(); 
}

// 按要求截取时间字符串
function cutLen( str , len){
    if(str){
        let format = `yyyy-MM-dd hh:mm:ss`;
        format = format.substr(0,len) ;
        return new Date(str).format(format);
    }else{
        return "" ;
    }
}
function formatStakeholderCategory(cat) {
    cat = Number(cat);
    let str = ''
    switch (cat){
        case 1 : str = '供应商'; break;
        case 2 : str = '公司职工'; break;
        case 3 : str = '财务自行录入的收款单位（含个人）'; break;
    }
    return str
}
// 根据  beginDate 判断得到查看数据的 endDate
function getEndDate( beginDate , monthOrDay){ // monthOrDay : 2-月进日 ， 3-日进明细
    var dateArr = new Date(beginDate) ;
    var year = dateArr.getFullYear() ;
    var month = dateArr.getMonth() ;
    var endDate = new Date(year,month+1,0).format('yyyy-MM-dd')  ;
    endDate += " 23:59:59";
    /*if ( monthOrDay == 2 ){
        if(month == "01" || month == "03"  || month == "05"  || month == "07"  || month == "08"  || month == "10"  || month == "12" ){
            endDate += "-31 23:59:59";
        }else if( month == "04"  || month == "06"  || month == "09"|| month == "11"){
            endDate += "-30 23:59:59";
        }else{
            if(year%100 == 0){ // 整百年/400无余 ， 闰年
                if(year%400 == 0){
                    endDate += "-29 23:59:59";
                }else{
                    endDate += "-28 23:59:59";
                }
            }else{
                if(year%4 == 0){
                    endDate += "-29 23:59:59";
                }else{
                    endDate += "-28 23:59:59";
                }
            }
        }
    }else if( monthOrDay == 3 ){
        endDate += "-" + dateArr[2].substr(0,2) + " 23:59:59" ; 
    }*/
    return endDate ;

}
function setNum(obj) {
    let val = obj.val();
    if(val.length > 50){
        val = val.substr(0,50);
        obj.val(val);
    }
    obj.next(".showNum").html(`${ val.length } / 50`)
}
// 查看详情
var editTr = null ;
var rateEntryTimer
function getDetails(obj , detailId , typeStr){
    var disabled = obj.attr("disabled");
    if(disabled){
        return false;
    }
    $("#goPrev").show()
    var navStr = "<span class='nav_'> / </span><span class='navTxt'>"+ obj.parent().siblings(":eq(1)").html() ;
    var navCredit = obj.parent().siblings(":eq(2)").html() ;
    var navDebit = obj.parent().siblings(":eq(3)").html() ;
    if( isNaN( Number(navCredit) )  ){ navStr+= "(支出)明细";  }else{ navStr += "(收入)明细";  }
    navStr += "</span>" ;
    var userType = chargeRole("超管");
    if (userType && typeStr === "update") {

    }else{
        $("#navControl").append(navStr);
    }
    if( $.trim( detailId ) == "" ){
        bounce.show( $("#tip") );
        $("#mt_tip_ms").html("获取基本信息失败，请重试！");
    }
    editTr = obj.parent().parent() ;

    if(typeStr == "scan"){   }else{ getAprovList();  }
    $("#detailKind_3").data("detailid", detailId);

    $.ajax({
        url:"../data/getOneDetail.do" ,
        data:{ "detailId" : detailId  } ,
        success:function (data) {
            var kind = data["kind"];
            var content = data["content"];
            if( Number(kind) > 0 && Number(kind) < 9 && content ){
                if( typeStr == "scan" ){
                    $(".update").hide();  $(".updateControl").hide(); $(".updateRadio").hide();
                    $(".scan").show();  $(".scanControl").show();
                }else{
                    $(".update").show(); $(".updateControl").show(); $(".updateRadio").show();
                    $(".scan").hide();$(".scantd").hide(); $(".scanControl").hide();
                }
                console.log(  Number(kind) );
                let images = data.financeAccountBillImages || [];
                var imagesStr = `` ;
                let imgDelStr = '<span class="ww fa fa-times" onclick="delFile($(this))"></span>'
                if( typeStr == "scan" ){
                    imgDelStr = '';
                }
                let picFilePath= ""
                images.forEach(function (img) {
                    picFilePath = img.uplaodPath;
                    let fullpath = $.fileUrl + img.uplaodPath;
                    let path = img.uplaodPath;
                    imagesStr +=
                        '<div class="cp_img_box">' +
                        '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                        '       <a class="ww" path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                        imgDelStr +
                        '   </div>'+
                        '</div>';;
                })

                switch ( Number(kind) ){ 
                    case 1 : // 来源于报销数据的现金/银行转账信息
                        dataInfo($("#detailKind_1"),typeStr) ; 
                        var createName = content["createName"] ;                      // (申请人)
                        var createDate = content["createDate"] ;                      // (申请时间)
                        var feeCatName = content["feeCatName"];                       // (费用类别)
                        var feeCat = content["feeCat"];                               // (费用类别)
                        var billCatName = content["billCatName"];                     // (票据种类)
                        var billDate = content["billDate"];                            // (发票所属月份,1-本月票据,2-非本月票据)
                        var billQuantity = content["billQuantity"];                   // (票据数量)
                        var summary = content["summary"];                              // (摘要)
                        var purpose = content["purpose"];                              // (用途)
                        var amount = content["amount"];                                // (实际金额)
                        var billAmount = content["billAmount"];                        // (发票金额)
                        var memo = content["memo"];                                    // (备注)
                        var billQuantity = content["billQuantity"];                   // ( 票据数量 )
                        var categoryDesc = content["categoryDesc"];                   // 其他
                        var approvalProcess = content["approvalProcessHashSet"];     // (审批流程)
                        var str = "" ;
                        if(approvalProcess && approvalProcess.length >0){
                            for( var i =0 ; i < approvalProcess.length ; i++ ){
                                var prostatus = approvalProcess[i]["approveStatus"] ;
                                switch( Number(prostatus) ){
                                    case 2 :  // 已批准
                                        str += "<div class='processTr'>"+
                                            "<span class='ok-icon'></span>" +
                                            "<span class='touserInfo'>处理人 ： 已批准 / " + approvalProcess[i]["toUserName"] + "</span>" +
                                            "<span class='timeInfo'>处理时间 ：" +approvalProcess[i]["handleTime"] +  "</span>" +
                                            "</div>" ;
                                        break ;
                                    case 5 :  // 已报销
                                        str += "<div class='processTr'>"+
                                            "<span class='ok-icon'></span>" +
                                            "<span class='touserInfo'>处理人 ： 已报销 / " + approvalProcess[i]["toUserName"] + "</span>" +
                                            "<span class='timeInfo'>处理时间 ：" +approvalProcess[i]["handleTime"] +  "</span>" +
                                            "</div>" ;
                                        break ;
                                    case 3 :  // 已驳回
                                        str += "<div class='processTr noProcess '>"+
                                            "<span class='no-icon'></span>" +
                                            "<span class='touserInfo'>处理人 ： 已驳回 / " + approvalProcess[i]["toUserName"] + "</span>" +
                                            "<span class='timeInfo'>处理时间 ：" + approvalProcess[i]["handleTime"] +  "</span>" +
                                            "<p class='memoInfo'>回复内容 ： "+ approvalProcess[i]["approveMemo"]  +"</p>" +
                                            "</div>" ;
                                        break ;
                                    case 1 :  // 待处理
                                        str += "<div class='processTr'>"+
                                            "<span class='wait-icon'></span>" +
                                            "<span class='touserInfo'>处理人 ： 待处理 / " + approvalProcess[i]["toUserName"] + "</span>" +
                                            "</div>" ;
                                        break ;
                                    case 4 :  // 待两讫
                                        str += "<div class='processTr'>"+
                                            "<span class='wait-icon'></span>" +
                                            "<span class='touserInfo'>处理人 ： 待两讫 / " + approvalProcess[i]["toUserName"] + "</span>" +
                                            "</div>" ;
                                        break ;
                                    default:
                                        break;
                                }
                            }
                        }
                        //new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')
                        //new Date(item.createDate).format('yyyy-MM-dd')
                        $("#approvCon").html( str );
                        $("#applyName").html(createName);  // 申请人
                        $("#applyTime").html(new Date(createDate).format('yyyy-MM-dd hh:mm:ss'));  // 申请时间
                        var imgs = content["personnelReimbursetAttachmentHashSet"];
                        $("#info_images").html( "" ) ;
                        if( imgs && imgs.length > 0 ){
                            var strimg = "" ;
                            for(var j = 0 ; j < imgs.length ; j++ ){
                                var path = imgs[j]["path"] ;
                                strimg += '<div class="imgBox">'+
                                        '<a  href="../'+path+'" target="_blank"><img src="../'+path+'")"></a>'+
                                        // '<div class="imgClose"><i class="fa fa-close"></i></div>'+
                                        '</div>' ;
                            }
                            $("#info_images").html( strimg ) ;
                            $("#info_images").attr( "l",imgs.length ) ;
                        }
                        if( typeStr == "scan"){  // 查看
                            $("#scan_feeCatName_1").html( feeCatName );
                            $("#scan_billCatName_1").html( billCatName );
                            $("#scan_billDate_1").html( chargeBillPeriod(billDate) );
                            $("#scan_summery_1").html( summary );
                            $("#scan_purpose_1").html( purpose );
                            $("#scan_billAccount_1").html(billQuantity);  // 票据数量
                            $("#scan_amount_1").html(amount);
                            $("#scan_billAmount_1").html(billAmount);
                            $("#scan_memo_1").html(memo);
                            $("#changeApply .uploadFiles").hide();

                        }else{  // 修改
                            $(".processId").val(content["id"]);
                            $("#update_feeCatName_1").val( feeCat );
                            $("#update_summery_1").val( summary );
                            $("#update_purpose_1").val( purpose );
                            $("#update_billAccount_1").val( purpose );
                            $("#update_amount_1").val( amount );
                            $("#update_billAmount_1").val( billAmount );
                            $("#update_memo_1").val(memo);
                            $("#update_billAccount_1").val(billQuantity);
                            if( billCatName == "增值票" ){  changeTicketType($("#applyRadio_billCat_1") , 1 ) ;        }
                            else if( billCatName == "普通票" ){ changeTicketType($("#applyRadio_billCat_2") , 1 ) ;   }
                            else if( billCatName == "收据" ){ changeTicketType($("#applyRadio_billCat_3") , 1 ) ;     }

                            if( billDate == "1" ){  changeTicketMonth( $("#applyRadio_billMonth_1") , 1 ) ;        }
                            else if( billDate == "2" ){ changeTicketType($("#applyRadio_billMonth_2") , 1 ) ;   }
                            $("#changeApply .uploadFiles").show();

                        }
                        break ;
                    case 2 : // 项目为收入的现金/银行转账信息
                        dataInfo( $("#detailKind_2"),typeStr ) ;
                        var type = content["type"]; // (项目 1-初始金额冲减,2-转账交易,3-收入,4-支出)
                        var method = content["method"]; // (支出方式 1-现金,2-现金支票,3-转账支票,4-承兑汇票,5-银行转账，6-存现金，7-取现金，8-其他内部转)
                        var genre = content["genre"]; // (类别 1-货款，2-借款，3-投资款，4-废品，5-其他)
                        var categoryDesc = content["categoryDesc"]; // (5-其他 自定义输入的内容)
                        var oppositeCorp = content["oppositeCorp"]; // (付款单位)
                        var receiveAccountDate = ( new Date(content["receiveAccountDate"]).format('yyyy-MM-dd') )   ; // (到账时间)
                        var accountId = content["accountId_"]; // (收款银行)
                        var purpose = content["purpose"]; // (用途)
                        var summary = content["summary"]; // (摘要)
                        var auditorName = content["auditorName"]; // (经手人)
                        var credit = content["credit"]; // (金额)
                        var billAmount = content["billAmount"]; // (金额)
                        var memo = content["memo"]; // (备注)
                        if(method == 5){
                            $("#update_method5_2").show();
                        }else{
                            $("#update_method5_2").hide();
                        }

                        if( typeStr == "scan"){
                            if(genre == 5){
                                $("#scan_categoryDesc_2").show()
                                $("#scan_categoryDesc_2").html(categoryDesc );
                            }else{
                                $("#scan_categoryDesc_2").hide()
                            }
                            $("#scan_method_2").html( chargeMethod(method) );
                            $("#scan_feeCat_2").html( chargeGenre(genre) );
                            $("#scan_summery_2").html( summary );
                            $("#scan_amount_2").html( credit );
                            $("#scan_billAmount_2").html( billAmount );
                            $("#scan_purpose_2").html( purpose );
                            $("#scan_auditorName_2").html( auditorName );
                            $("#scan_oppositeCorp_2").html( oppositeCorp );
                            $("#scan_memo_2").html( memo);
                            $("#scan_billCat_2").html("");
                            $("#scan_billMonth_2").html("");
                            $("#scan_billAccount_2").html("");
                            $("#scan_accountBank_2").html( str );
                            $("#scan_receiveAccountDate_2").html( receiveAccountDate ); //create:lyt date:2018/2/8
                            getScanBank($("#scan_accountBank_2"),accountId);

                        }else{
                            if(genre == 5){
                                $("#update_categoryDesc_2").show()
                            }else{
                                $("#update_categoryDesc_2").hide()
                            }
                            $("#detailKind_2").children(".bonceCon1").attr("id",content["id"]);
                            $("#update_feeCat_2").val( genre );
                            $("#update_categoryDesc_2").val(categoryDesc );
                            $("#update_summery_2").val( summary );
                            $("#update_amount_2").val( credit );
                            $("#update_billAmount_2").val( billAmount );
                            $("#update_purpose_2").val( purpose );
                            $("#update_auditorName_2").val( auditorName );
                            $("#update_oppositeCorp_2").val( oppositeCorp );
                            $("#update_method_2").html(chargeMethod(method)) ;
                            $("#update_receiveAccountDate_2").val( receiveAccountDate );
                            $("#update_accountBank_2").val( accountId );
                            $("#update_memo_2").val(memo);
                            $("#update_billAccount_2").html("");
                        }
                        break ;
                    case 3 : // 项目为支出的现金/银行转账信息
                        dataInfo( $("#detailKind_3"),typeStr ) ;
                        var type = content["type"]; // (项目 1-初始金额冲减,2-转账交易,3-收入,4-支出)
                        var method = content["method"]; // (支出方式 1-现金,2-现金支票,3-转账支票,4-承兑汇票,5-银行转账，6-存现金，7-取现金，8-其他内部转)
                        var genre = content["genre"]; // (类别 1-货款，2-借款，3-投资款，4-废品，5-其他)
                        var oppositeCorp = content["oppositeCorp"]; // (付款单位)
                        var receiveAccountDate = content["receiveAccountDate"]; // (到账时间)
                        var accountId = content["accountId_"]; // (收款银行)
                        var purpose = content["purpose"]; // (用途)
                        var summary = content["summary"]; // (摘要)
                        var auditorName = content["auditorName"]; // (经手人)
                        var debit = content["debit"]; // (金额)
                        var billAmount = content["billAmount"]; // (金额)
                        var billNumber = content["billQuantity"];  // 票据数量
                        var billCat = content["billCat"];  // 票据类型
                        var billPeriod = content["billPeriod"]; // （1-本月票据,2-非本月票据）
                        var memo = content["memo"];
                        if(method == 5){
                            $("#update_method5_3").show();
                        }else{
                            $("#update_method5_3").hide();
                        }
                        $(".stakeholderCategory").hide();

                        if(content.subType == 6){ // 汇划费
                            $("#detailKind_3 .bonceCon2").hide(); $("#detailKind_3 .bonceCon1").show();
                            $(".oral2").show(); $(".oral1").hide();
                            $("#scan_imgs_3").html( imagesStr ); // 凭证照片
                            if( typeStr == "scan" ){
                                $(".addOopBtn").hide()
                                var accountBank = content["accountBank"]
                                $("#scan_exp_3").html( fotmatExpType(content.subType) );
                                $("#scan_paydate_3").html( new Date(content.factDate ).format("yyyy-MM-dd") ); // 支出日期
                                $("#scan_summaryPurpose_3").html( summary ); // 用途/摘要
                                $("#scan_payAmount_3").html( debit ); // 支出金额
                                $("#scan_payBank_3").html( accountBank ); // 开户行
                                $("#scan_payMemo_3").html( memo ); // 备注

                            }
                            else{
                                $(".addOopBtn").show()
                                var accountBank = content["accountBank"]
                                $("#update_exp_3").html( fotmatExpType(content.subType) );
                                $("#update_paydate_3").val( new Date(content.factDate ).format("yyyy-MM-dd") ); // 支出日期
                                $("#update_summaryPurpose_3").val( summary ); // 用途/摘要
                                $("#update_payAmount_3").val( debit ); // 支出金额
                                $("#update_payMemo_3").val( memo ); // 备注
                                uploadInit2($("#uploadBtn2") , 'scan_imgs_3')
                                $("#update_payBank_3").val( accountId );// 开户行
                            }
                        }
                        else if(content.subType == 5){ // 税款
                            let info = content
                            $("#detailKind_3 .bonceCon2").show(); $("#detailKind_3 .bonceCon1").hide();
                            if( typeStr == "scan" ){
                                $("#detailKind_3 .bonceCon2 .scan000").show().siblings().hide();
                                $("#detailKind_3 .bonceCon2 .scan000 .ratename").html(info.taxName)
                                $("#detailKind_3 .bonceCon2 .scan000 .ratenDur").html(`${info.businessPeriodBegin} - ${info.businessPeriodEnd}`)
                                $("#detailKind_3 .bonceCon2 .scan000 .rateFactDate").html(new Date(info.factDate).format("yyyy-MM-dd"))
                                $("#detailKind_3 .bonceCon2 .scan000 .rateAmount").html(info.debit)
                                $("#detailKind_3 .bonceCon2 .scan000 .rateMethod").html(`${info.method == 1 ? '现金支付' : `转账支付-${info.accountBank}`}`)
                                $("#detailKind_3 .bonceCon2 .scan000 .ratePic").html(imagesStr)
                                $("#detailKind_3 .bonceCon2 .scan000 .rateReportLog").html(`需申报的起止日期为${ new Date(info.beginDate ).format("yyyy-MM-dd") }至${ new Date(info.endDate ).format("yyyy-MM-dd") }的申报记录 `)
                                $("#detailKind_3 .bonceCon2 .scan000 .rateMemo").html(info.memo)

                            }else{
                                $("#detailKind_3 .bonceCon2 .edit000").show().siblings().hide();
                                $("#rateEntryFrm input").val("")
                                $("#ratePic").html("")
                                $("#rateEntryFrm select").val("0")
                                $("#rateEntryFrm").data("type","edit")
                                getRateList($("#rateCat"), info.businessKey); // 2022-0828

                                $("#rateStartDate").val(info.businessPeriodBegin)
                                $("#rateEndDate").val(info.businessPeriodEnd)
                                $("#rateRealDate").val(new Date(info.factDate).format("yyyy-MM-dd"))
                                $("#moneyReal").val(info.debit)
                                getPatMethod($("#patMethod"), info.accountId_)
                                let data = { "taxId":info.businessKey , "startDate":info.businessPeriodBegin , "endDate":info.businessPeriodEnd  };
                                let optionSelect = {
                                    "report":info.report,
                                    "id":info.taxPeriodId,
                                    "startDate":info.beginDate,
                                    "endDate":info.endDate
                                }
                                getRateApplyLogAj(data, $("#rateApplyLog"), info.taxPeriodId, optionSelect )
                                $("#rateApplyLog").data("beforeid", info.taxPeriodId)
                                $("#rateApplyLog").data("old", info.report)
                                $("#rateMemo").val(info.memo)
                                uploadInit3()
                                $("#ratePic").html(imagesStr).data("path",picFilePath)
                                rateEntryTimer = setInterval(function () {
                                    getRateApplyLog($("#rateApplyLog"))

                                }, 200)
                            }

                        }
                        else{
                            $("#detailKind_3 .bonceCon2").hide(); $("#detailKind_3 .bonceCon1").show();

                            $("#scan_imgs_32").html( imagesStr ); // 凭证照片
                            $(".oral2").hide(); $(".oral1").show();
                            if(content.subType == 7){ //
                                $("#billAmountTr").hide()
                                $("#actMoney").html('支出金额')
                                $("#actPayDate").html('支出日期')
                            }else{
                                $("#actMoney").html('实际金额合计')
                                $("#actPayDate").html('实际付款日期')
                                $("#billAmountTr").show()
                            }
                            if( typeStr == "scan" ){
                                $(".addOopBtn").hide()
                                var accountBank = content["accountBank"]
                                $("#scan_exp_3").html( fotmatExpType(content.subType) );
                                $("#scan_billDate_3").html( new Date(content.billDate ).format("yyyy-MM-dd") );
                                $("#scan_summery_3").html( summary );
                                $("#scan_amount_3").html(debit);
                                $("#scan_billAmount_3").html(billAmount);
                                $("#scan_purpose_3").html( purpose );
                                $("#scan_auditorName_3").html( auditorName );
                                $("#scan_oppositeCorp_3").html( oppositeCorp );
                                $("#scan_memo_3").html(memo);
                                $("#scan_billAccount_3").html(billNumber);
                                $("#scan_accountBank_3").html(accountBank);
                                $("#scan_method_3").html(chargeMethod(method));
                                $("#scan_stakeholderCategory_3").html( formatStakeholderCategory( content.stakeholderCategory ) );
                                $("#scan_factDate_3").html( new Date(content.factDate).format("yyyy-MM-dd") ) ;

                            }else{
                                $(".addOopBtn").show()
                                $("#update_exp_3").html( fotmatExpType(content.subType) );
                                $("#update_billDate_3").val( new Date(content.billDate ).format("yyyy-MM-dd") );
                                $("#detailKind_3").children(".bonceCon1").attr("id",content["id"]);
                                $("#update_summery_3").val( summary ) ;
                                $("#update_amount_3").val( debit ) ;
                                $("#update_factDate_3").val(new Date(content.factDate).format("yyyy-MM-dd") ) ;
                                $("#update_billAmount_3").val( billAmount ) ;
                                $("#update_purpose_3").val( purpose ) ;
                                $("#update_auditorName_3").val( auditorName ) ;
                                $("#update_oppositeCorp_3").val( oppositeCorp ).data("id",content.stakeholder) ;
                                $("#update_stakeholderCategory_3").html( formatStakeholderCategory( content.stakeholderCategory ) ).data("type", content.stakeholderCategory) ;
                                $("#update_accountBank_3").val( accountId );
                                $("#update_memo_3").val(memo) ;
                                $("#update_billAccount_3").val(billNumber) ;
                                $("#update_method_3").html(chargeMethod(method)) ;
                                uploadInit2($("#uploadBtn22") , 'scan_imgs_32')
                            }

                        }
                        break ;
                    case 4 : // 现金支票/内部转账支票的信息
                        dataInfo( $("#detailKind_3_"),typeStr ) ;
                        var type = content["type"]; // (支出方式 1-转账支票，2-承兑汇票)
                        var accountId = content["accountId_"]; // 银行账号ID
                        var account = content["account"]; // 账号
                        var bankName = content["bankName"]; // 银行名称
                        var amount = content["amount"]; // 金额
                        var billAmount = content["billAmount"]; // 金额
                        var summary = content["summary"]; // 摘要
                        var purpose = content["purpose"]; // 用途
                        var financialHandling = content["financialHandling"]; // 财务经手人
                        var operator = content["operator"]; // 支付经手人
                        var receiver = content["receiver"]; // 接收经手人
                        var receiveDate = content["receiveDate"]; // 接收日期
                        var receiveCorp = content["receiveCorp"]; // 接收公司
                        var expireDate = content["expireDate"]; // 支票到期日
                        var chequeNo = content["chequeNo"]; // 支票号
                        var chequeId = content["id"]; // 支票id 
                        var billCat = content["billCat"]; // 票据种类
                        var billPeriod = content["billPeriod"]; // 票据所属月份
                        var billQuantity = content["billQuantity"]; // 票据数量
                        var memo = content["memo"]; // 备注

                        $(".stakeholderCategory").hide() ;
                        $(".update_method_3_1").show();
                        $(".update_method_3_1_1").show();
                        $(".update_method_3_1_2").hide();
                        $(".update_method_3_2").hide();
                        $("#scan_imgs_3_").html( imagesStr );
                        if( typeStr == "scan" ){
                            $(".addOopBtn").hide()

                            $("#scan_billCat_3_").html(billCat);
                            // if(billPeriod == 1){ billPeriod="本月票据" ; }else{ billPeriod="非本月票据" ;}
                            // $("#scan_billMonth_3_").html(billPeriod);

                            $("#scan_exp_3_").html(fotmatExpType(content.subType));
                            $("#scan_billDate_3_").html( new Date(content.billDate ).format("yyyy-MM-dd") );
                            $("#scan_summery_3_").html(summary);
                            $("#scan_billAccount_3_").html(billQuantity);
                            $("#scan_amount_3_").html(amount);
                            $("#scan_billAmount_3_").html(billAmount);
                            $("#scan_purpose_3_").html(purpose);
                            $("#scan_auditorName_3_").html(financialHandling);
                            if(type == 1){ type = "转账支票"; }else{ type = "现金支票"; }
                            $("#scan_method_3_").html(type);
                            $("#scan_method_3_2").html("内部支票");
                            $("#scan_bankAccount_3_").html(bankName + account).attr("title" , bankName + account );
                            $("#scan_checkNo_3_").html(chequeNo);
                            $("#scan_checkendDate_3_").html(new Date(expireDate).format('yyyy-MM-dd'));
                            $("#scan_receiveCpmy_3_").html(receiveCorp);
                            $("#scan_receiveDate_3_").html(new Date(receiveDate).format('yyyy-MM-dd'));
                            $("#scan_receivePerson_3_").html(receiver);
                            $("#scan_payPerson_3_").html(operator);
                            $("#scan_memo_3_").html(memo);
                            let financeAccountBill = data.financeAccountBill
                            $("#scan_stakeholderCategory_3_").html( formatStakeholderCategory( financeAccountBill.stakeholderCategory ) );
                            $("#scan_oppositeCorp_3_").html( financeAccountBill.oppositeCorp );
                            $("#scan_factDate_3_").html(new Date(financeAccountBill.factDate).format("yyyy-MM-dd") );

                        }else{
                            $(".addOopBtn").show()
                            uploadInit2($("#uploadBtn22_") , 'scan_imgs_3_')
                            $("#detailKind_3_ .update_method_3_1").show();
                            $("#detailKind_3_ .update_method_3_2").hide();
                            $("#detailKind_3_ .update_method_3_1_1").show();
                            $("#detailKind_3_ .update_method_3_1_2").hide();
                            $("#detailKind_3_").children(".bonceCon1").attr("id",content["id"]);
                            $("#update_exp_3_").html(fotmatExpType(content.subType));

                            $("#update_billDate_3_").val( new Date(content.billDate ).format("yyyy-MM-dd") );
                            $("#update_summery_3_").val(summary);
                            $("#update_billAccount_3_").val(billQuantity);
                            $("#update_amount_3_").val(amount);
                            $("#update_billAmount_3_").val(billAmount);
                            $("#update_purpose_3_").val(purpose);
                            $("#update_auditorName_3_").val(financialHandling);
                            // $("#update_bankAccount_3_").val(accountId);
                            getBankList(1 , accountId );
                            $("#update_method_3_").val(type);
                            $("#update_method_3_2").val(1);
                            $(".update_bankAccount_3_").val(accountId);
                            $(".update_checkNo_3_1").val(chequeNo);
                            $(".update_checkId_3_1").val(chequeId);
                            $("#update_checkendDate_3_").val(new Date(expireDate).format('yyyy-MM-dd'));
                            $("#update_receiveCpmy_3_").val(receiveCorp);
                            $("#update_receiveDate_3_").val(new Date(receiveDate).format('yyyy-MM-dd'));
                            $("#update_receivePerson_3_").val(receiver);
                            $("#update_payPerson_3_").val(operator);
                            $("#update_memo_3_").val(memo);
                            let financeAccountBill = data.financeAccountBill
                            $("#update_stakeholderCategory_3_").html( formatStakeholderCategory( financeAccountBill.stakeholderCategory ) ).data('type',financeAccountBill.stakeholderCategory );
                            $("#update_oppositeCorp_3_").val( financeAccountBill.oppositeCorp ).data('id', financeAccountBill.stakeholder);
                            $("#update_factDate_3_").val( new Date(financeAccountBill.factDate).format("yyyy-MM-dd") );

                        }
                        break ;
                    case 5 : // 承兑汇票/外部转账支票的信息
                        dataInfo($("#detailKind_5"),typeStr) ;
                        var billType = content["billType"] ;   // 项目1-收入,2-支出
                        if(billType == 1){  billType = "收入" ;  }else{  billType = "支出" ;  }
                        var type = content["type"] ;            // 1-转账支票，2-承兑汇票
                        var category = content["category"] ;   // 种类:1-货款,2-借款,3-投资款,4-废品,5-其他
                        var categoryOther = content["categoryDesc"] ;   // 其他种类（category == 5)********
                        var amount = content["amount"] ;     // 金额
                        var billAmount = content["billAmount"] ;     // 金额
                        var summary = content["summary"] ;     // 摘要
                        var purpose = content["purpose"] ;     // 用途
                        var operatorName = content["operatorName"] ;      // 经手人
                        var bankName = content["bankName"] ;              // 出具支票（汇票）银行
                        var saveBankName = content["saveBankName"] ;     // 存入银行
                        var accout = content["accout"] ;     // 存入银行  （张 190929新增）
                        var saveBankId = content["saveBankId"] ;     // 存入银行ID
                        var depositDate = new Date(content["depositDate"]).format('yyyy-MM-dd') ;       // 存入时间
                        var depositorName = content["depositorName"] ;   // 存入经手人
                        var recev = new Date(content["receiveAccountDate"]).format('yyyy-MM-dd') ;         // 到账时间
                        var originalCorp = content["originalCorp"];                       // 出具支票（汇票）单位
                        var createDate = new Date(content["createDate"]).format('yyyy-MM-dd');              // 录入支票（汇票）日期
                        var receiveDate = new Date(content["receiveDate"]).format('yyyy-MM-dd');            // 收到支票（汇票）日期
                        var payer = content["payer"];                     // 付款单位
                        var returnNo = content["returnNo"];              //  支票（汇票）号
                        var expireDate = new Date(content["expireDate"]).format('yyyy-MM-dd');          // 支票（汇票）到期日
                        var createName= content["createName"];                        // 财务经手人
                        var memo= content["memo"];                                     // 备注

                        if(type == 1){  // 1-转账支票
                            $("#method_receive").hide(); $("#method_trans").show();   var typestring = "转账支票" ;
                            if(category == 5){

                            }
                        }else if(type == 2){  // 2-承兑汇票
                            $("#method_receive").show(); $("#method_trans").hide();  var typestring = "承兑汇票" ;
                        }
                        if(category == 5){
                            $("#scan_categoryDesc_5").parents(".itemTr").show();
                            $("#update_categoryDesc_5").parents(".itemTr").show();

                        }else{
                            $("#scan_categoryDesc_5").parents(".itemTr").hide();
                            $("#update_categoryDesc_5").parents(".itemTr").hide();
                        }

                        if( typeStr == "scan" ){
                            $(".update").hide();   $(".scan").show();
                            $("#scan_type_5").html(billType);
                            $("#scan_cat_5").html( chargeGenre(category) );
                            $("#scan_categoryDesc_5").html( categoryOther);
                            $("#scan_summery_5").html( summary );
                            $("#scan_billAmount_5").html( billAmount );
                            $("#scan_purpose_5").html( purpose );
                            $("#scan_auditorName_5").html( operatorName );
                            $("#scan_method_5").html( typestring );
                            $("#scan_cunTime_5").html( depositDate );
                            $("#scan_cunPerson_5").html( depositorName );
                            $("#scan_receTime_5").html( recev );
                            $("#scan_cunBank_5").html( saveBankName + ' ' + accout );

                            if(type == 1){  // 1-转账支票
                                $("#scan_amount_5").html( amount );
                                $("#scan_recevDate_5").html(receiveDate) ; // 收到支票日期
                                $("#scan_payCmp_5").html(payer) ; // 付款单位
                                $("#scan_checkno_5").html(returnNo) ; // 支票号
                                $("#scan_endDate_5").html(expireDate) ; // 支票到期日
                                $("#scan_origCmp_5").html(originalCorp) ; // 原始出具支票单位
                                $("#scan_origBank_5").html(bankName) ; // 出具支票银行
                                $("#scan_jingshou_5").html(createName) ; // 财务经手人
                                $("#scan_inputTime_5").html(createDate) ; // 录入时间
                                $("#scan_memos_5").html(memo) ; // 备注

                            }else if(type == 2){  // 2-承兑汇票
                                $("#scan_amount_5_1").html( amount );
                                $("#scan_recevDate_5_1").html(receiveDate) ;   // 收到汇票日期
                                $("#scan_payCmp_5_1").html(payer) ;   // 付款单位
                                $("#scan_checkno_5_1").html(returnNo) ;   // 汇票号
                                $("#scan_endDate_5_1").html(expireDate) ;   // 汇票到期日
                                $("#scan_origCmp_5_1").html(originalCorp) ;   // 原始出具汇票单位
                                $("#scan_origBank_5_1").html(bankName) ;   // 出具汇票银行
                                $("#scan_memos_5_1").html(memo) ;   // 备注
                            }

                        }else{
                            $("#detailKind_5").children(".bonceCon1").attr("id",content["id"]);
                            $(".update").show(); $(".scan").hide();
                            $("#update_type_5").html(billType);
                            $("#update_cat_5").html( chargeGenre(category) );
                            $("#update_categoryDesc_5").html( categoryOther);
                            $("#update_summery_5").html( summary );
                            $("#update_billAmount_5").html( billAmount );
                            $("#update_purpose_5").html( purpose );
                            $("#update_auditorName_5").html( operatorName );
                            $("#update_method_5").html( typestring );
                            $("#update_cunBank_5").val( saveBankName );
                            $("#update_cunTime_5").val( depositDate );
                            $("#update_cunPerson_5").val( depositorName );
                            $("#update_receTime_5").val( recev );
                            setBankVal($("#update_cunBank_5") , saveBankId ,1) ;
                            if(type == 1){  // 1-转账支票
                                $("#trans_update_amount_5").html( amount );
                                $("#update_recevDate_5").html(receiveDate) ; // 收到支票日期
                                $("#update_payCmp_5").html(payer) ; // 付款单位
                                $("#update_checkno_5").html(returnNo) ; // 支票号
                                $("#update_endDate_5").html(expireDate) ; // 支票到期日
                                $("#update_origCmp_5").html(originalCorp) ; // 出具支票单位
                                $("#update_origBank_5").html(bankName) ; // 出具支票银行
                                $("#update_jingshou_5").html(createName) ; // 财务经手人
                                $("#update_inputTime_5").html(createDate) ; // 录入时间
                                $("#update_memos_5").html(memo) ; // 备注

                            }else if(type == 2){  // 2-承兑汇票
                                $("#receive_update_amount_5").html( amount );
                                $("#update_recevDate_5_1").html(receiveDate) ;   // 收到汇票日期
                                $("#update_payCmp_5_1").html(payer) ;   // 付款单位
                                $("#update_checkno_5_1").html(returnNo) ;   // 汇票号
                                $("#update_endDate_5_1").html(expireDate) ;   // 汇票到期日
                                $("#update_origCmp_5_1").html(originalCorp) ;   // 原始出具汇票单位
                                $("#update_origBank_5_1").html(bankName) ;   // 出具汇票银行
                                $("#update_memos_5_1").html(memo) ;      // 备注
                            }

                        }
                        break ;
                    case 6 : // 内部非支出性转账的存现金和内部转账
                    case 7 : // 内部非支出性转账的取现金非基本户
                    case 8 : // 内部非支出性转账的取现金基本户
                        dataInfo( $("#detailKind_4"),typeStr ) ;
                        var method = content["method"];
                        var memo = content["memo"];
                        var bankBase = content["bankBase"];                     // 是否基本户
                        var auditDate = content["auditDate"];
                        var accountBank = content["accountBank"];           // 本账户
                        var accountId = content["accountId_"];              // 本账户id
                        var oppositeAccount = content["oppositeAccount"];   // 对方账户
                        var oppositeId = content["oppositeId"];             // 对方账户的id
                        var payAccount = "" , receiveAccount = "" ;
                        var money = "" ;
                        var moneyType = "";
                        var credit = content["credit"] ;
                        var debit = content["debit"] ;
                        // debit
                        if(debit || debit == 0){
                            money = debit ; payAccount = accountBank ; receiveAccount = oppositeAccount ; moneyType = "d"
                        }else{
                            money = credit ;  payAccount = oppositeAccount ; receiveAccount = accountBank ; moneyType = "c" ;
                        }

                        // 取现金 - 账户为基本户时，
                        if( kind == 6 ||kind == 7 ){
                            $("#checkCon").hide() ;

                        }else if(kind == 8){
                            var checkId = content["chequeId"];
                            var checkNo = content["chequeNo"];
                            $("#checkCon").show() ;
                            if(typeStr == "scan"){
                                $("#scan_checkNo_4").html(checkNo);
                            }else{
                                if( credit == "" || credit == 0 ){
                                    getCheckListByBankId(accountId , checkId , checkNo );
                                }else{
                                    getCheckListByBankId(oppositeId , checkId , checkNo );
                                }
                            }
                        }

                        if( typeStr == "scan" ){
                            $(".update").hide();  $(".updateControl").hide(); $(".updateRadio").hide();
                            $(".scan").show();  $(".scanControl").show();
                            $("#scan_transKind_4").html(chargeMethod(method)) ;
                            $("#scan_auditDate_4").html( new Date(auditDate).format('yyyy-MM-dd') ) ;
                            $("#scan_money_4").html(money) ;
                            $("#scan_payAccount_4").html(payAccount) ;
                            $("#scan_receiveAccount_4").html(receiveAccount) ;
                            $("#scan_memo_4").html(memo);
                        }else{
                            $("#detailKind_4").children(".bonceCon1").attr("id",content["id"]);
                            $(".update").show(); $(".updateControl").show(); $(".updateRadio").show();
                            $(".scan").hide(); $(".scanControl").hide();
                            $("#update_transKind_4").html(chargeMethod(method));
                            $("#update_memo_4").val(memo);
                            $("#update_auditDate_4").val(new Date(auditDate).format('yyyy-MM-dd'));
                            $("#update_money_4").val(money);
                            $("#update_money_4").attr("moneyType",moneyType);
                            $("#update_payAccount_4").html(payAccount);
                            $("#update_receiveAccount_4").html(receiveAccount);

                        }
                        break ;
                    default :
                        bounce.show( $("#tip") );
                        $("#mt_tip_ms").html("获取基本信息失败1，请重试！");
                }
            }else{
                bounce.show( $("#tip") );
                $("#mt_tip_ms").html("获取基本信息失败，请重试！");
            }
        },
        error:function () {
            bounce.show( $("#tip") );
            $("#mt_tip_ms").html("获取基本信息失败，请重试！");
        }
    });
}

function uploadInit2(obj, strIdStr) {
    obj.html("")
    obj.Huploadify({
        auto: true,
        fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;',
        multi: true,
        buttonText: "上传发票图片",
        buttonNumber: 0,
        formData: {
            module: '财务管理',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
        showUploadedPercent: true,
        showUploadedSize: true,
        removeTimeout: 99999999,
        // itemTemplate:itemTemp ,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart: function () {
            // deleObj.hide();
            // deleObj.siblings(".img").show();
        },
        onUploadComplete: function (file, data) {
        },
        onUploadError: function (file) {
            layer.msg('文件上传出错！');
            console.log('出错文件：', file)
        },
        onUploadSuccess: function (file, data) {
            data = JSON.parse(data)
            var fileUid = data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                fullpath = $.fileUrl + data.filename,
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =
                '<div class="cp_img_box" data-new="1">' +
                '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                '       <a class="ww" path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                '       <span class="ww fa fa-times" onclick="delFile($(this))"></span>' +
                '   <a class="hd fileUid">'+ fileUid +'</a>' +
                '   </div>'+
                // '   <div class="cp_img_name">'+file.name+'</div>' +
                '</div>';
            $(`#${strIdStr}`).append(imgStr)
            $(`#${strIdStr} .imgI:last-child`).data('val', data)
        }
    });

}
function upBtn2(obj, strIdStr){
    let num = $(`#${ strIdStr } .cp_img_box`).length
    if(num >= 9){
        return false
    }
    obj.find(".uploadify-button").click();
}

// creator： 侯杏哲 2021-12-02 选择 收款单位
function openOop(obj){
    let sghow = obj.next(".stakeholderCategory").is(":visible")
    let txtObj = obj.parents(".stakeholderCategoryContsiner").next().find("input")
    $("#oppositeCorpDiv").data("objcat", obj)
    $("#oppositeCorpDiv").data("objtxt", txtObj)
    if(sghow){
        obj.next(".stakeholderCategory").hide();
    }else{
        obj.next(".stakeholderCategory").show();
    }
}
function chooseCOK() {
    let selectObj = $("#chooseC i.fa-dot-circle-o");
    if(selectObj.length > 0){
        let type = $("#oppositeCorpDiv").data("type")
        let typeStr = ''
        if(type == 1){
            typeStr = '供应商'
        }else if(type == 2){
            typeStr = '公司职工'
        }else if(type == 3){
            typeStr = '财务自行录入的收款单位（含个人）'
        }
        let objcat = $("#oppositeCorpDiv").data("objcat")
        let objtxt = $("#oppositeCorpDiv").data("objtxt")
        objcat.html(typeStr).data("type",type);
        let id = selectObj.parent().data('val');
        let name = selectObj.next("span").html();
        objtxt.val(name).data("id",id);
        bounce.cancel()
        $(".stakeholderCategory").hide()
    }else{
        layer.msg('请先选择收款对象')
    }
}

// radioButton 选择 , 票据种类
function changeTicketType(obj , num ){ // obj 点击的radioButton , num radio的位置 （ 第几个弹框中的 ）
    obj.addClass("radioActive") ;
    obj.siblings().removeClass("radioActive") ;
}
function fotmatExpType(type){
    type = Number(type)
    let str = ''
    switch (type){
        case 1:
            str = '本人的公务支出（财务费用除外、需入库物品的报销除外）'
            break;
        case 2:
            str = '其他同事的公务支出（财务费用除外，需入库物品的报销除外）'
            break;
        case 3:
            str = '需入库物品的报销'
            break;
        case 4:
            str = '社保/公积金'
            break;
        case 5:
            str = '税款'
            break;
        case 6:
            str = '汇划费'
            break;
        case 7:
            str = '其他财务费用的支出'
            break;
        case 8:
            str = '以上类别以外的支出'
            break;

    }
    return str
}
// radioButton 选择 , 票据所属月份
var BillMonthObj = null ;
function changeTicketMonth(obj , num ) { // obj 点击的radioButton , num radio的位置 （ 第几个弹框中的 ）
    obj.addClass("radioActive") ;
    obj.siblings().removeClass("radioActive") ;
    BillMonthObj = JSON.parse( obj.children(".val").html() ) ;
}

// 获得所有申请报销的项目 
function getAprovList(){
    $.ajax({
        url:"../expense/querySysCode.do",
        type:"post",
        dataType:"json",
        success:function(data){
            if(typeof(data.feeCats)==="undefined"){
                return false;
            }else{
                var str="";
                for(i=0;i<data.feeCats.length;i++){
                    str+='<option value='+data.feeCats[i].id+'>'+data.feeCats[i].name+'</option>'
                }
                $("#update_feeCatName_1").html(str);
            }
        },
        error:function(){
            // alert("错误")
        }
    });
}

// 变类别
function changeCatergray(obj){
    var num = obj.val();
    if( Number(num) == 5){ $(".isGenre").show();  }else { $(".isGenre").hide(); }
}

// 获得查看详情所需的银行账户
function getBankList(type , val ,selector){  //type 1-内部支票   2-外部支票    3-承兑汇票     4-银行转账, 此处必须传 1
    $.ajax({
        url:"../data/chooseCheque.do" ,
        data:{ "type" : type } ,
        type:"post" ,
        dataType:"json" ,
        success:function (data) {
            var content = data["content"] ;
            switch (Number(type)){
                case 1:
                    if( content ) {
                        var str = "";
                        for (var i = 0; i < content.length; i++) {
                            str += "<option value='"+ content[i]["id"]  +"'>"+ content[i]["bankName"] + "  " + content[i]["account"] +"</option>"
                        }
                        $("#update_bankAccount_3_").html(str) ;
                        $("#update_bankAccount_3_").val(val) ;
                        $("#"+selector).val(val) ;
                    };
                    setCheckNo(val);
                    break;
                case 3:
                    var str = "";
                    $("#acceptCheck").html("") ;
                    if( content && content.length > 0 ) {
                        var str = "";
                        for (var i = 0; i < content.length; i++) {
                            str += "<tr onclick='acceptCheckSelect($(this))'>" +
                                "<td>" +
                                "<span class='radioBtn ' > " +
                                "<span class='val'>{ value : '"+ content[i]["id"] +"' }</span>" +
                                "<span class='radioShow'></span>"+
                                "</span>" +
                                "</td>" +
                                "<td>"+ content[i]["createDate"].substr( 0 , 10 ) +"</td>" +
                                "<td>"+ content[i]["payer"] +"</td>" +
                                "<td>"+ content[i]["amount"] +"</td>" +
                                "<td>"+ content[i]["returnNo"] +"</td>" +
                                "<td>"+ content[i]["expireDate"].substr( 0 , 10 ) +"</td>" +
                                "<td>"+ content[i]["originalCorp"] +"</td>" +
                                "<td>"+ content[i]["bankName"] +"</td>"+
                                "</tr>";
                        }
                        $("#acceptCheck").html(str) ;
                    }
                    break ;
            }


        },
        error:function () {
            $("#mt_tip_ms").html("获得可用银行失败，请刷新重试！"); bounce.show($("#tip"));
        }
    })
}

/* creator：张旭博，2017-06-16 11:48:27，获取内部支票的支票号 */
function setCheckNo( accountId ){
    $.ajax({
        url:"../data/getChequeByAccountId.do" ,
        data:{ "accountId" : accountId } ,
        type:"post" ,
        dataType:"json" ,
        success:function (data) {
            var nowAccountId = $(".update_bankAccount_3_").val();
            var nowCheckNo = $(".update_checkNo_3_1").val();
            var nowCheckId = $(".update_checkId_3_1").val();
            var accountId = $("#update_bankAccount_3_").val();

            var financeChequeDetails = data["financeChequeDetails"] ;
            if( financeChequeDetails !== undefined) {
                console.log(accountId)
                console.log(nowAccountId)
                var str = "";
                if(accountId == nowAccountId){
                    str += '<option value="'+nowCheckId+'">'+nowCheckNo+'</option>';
                }
                for (var i = 0; i < financeChequeDetails.length; i++) {
                    str += "<option value='"+ financeChequeDetails[i]["id"]  +"'>" + financeChequeDetails[i]["chequeNo"] +"</option>"
                }
                $("#update_checkNo_3_1").html(str);
                if(accountId == nowAccountId){
                    $("#update_checkNo_3_1").val(nowCheckId);
                }

            }else{
                $("#update_checkNo_3_1").html("");
            }

        },
        error:function () {
            $("#mt_tip_ms").html("获得支票失败，请刷新重试！"); bounce.show($("#mtTip"));
        }
    })
}

// 设置非支出性内部转账的 支票号（ 返回可用的现金支票）
function getCheckListByBankId(accountId , checkId , chequeNo ){
    $.ajax({
        url:"../data/getCashChequeByAccountId.do" ,
        data:{ "financeAccountId" : accountId } ,
        type:"post" ,
        dataType:"json" ,
        success:function (data) {
            var status = data["status"] ;
            if( status == 0 || status == 2 ){ // 当前账户没有可用的支票
                $("#quCheckCon").hide() ;
                $("#mt_tip_ms").html("当前账户没有可用的支票"); bounce.show($("#tip"));
            }else{
                var list = data["chequeDetailList"] ;
                if(list){
                    var str = "<option value='"+ checkId +"'>"+  chequeNo +"</option>" ;
                    for(var i=0 ; i<list.length ; i++ ){
                        str += "<option value='"+ list[i]["id"] +"'>"+ list[i]["chequeNo"] +"</option>" ;
                    }
                    $("#update_checkNo_4").html(str);
                    $("#update_checkNo_4").val(checkId);
                }else{
                    $("#quCheckCon").hide() ;
                    $("#mt_tip_ms").html("当前账户没有可用的支票"); bounce.show($("#tip"));
                }
            }
            // var financeChequeDetails = data["financeChequeDetails"] ;
            // if( financeChequeDetails  ) {
            //     var str = "<option value='"+ checkId +"'>"+  chequeNo +"</option>";
            //     for (var i = 0; i < financeChequeDetails.length; i++) {
            //         str += "<option value='"+ financeChequeDetails[i]["id"]  +"'>"+  financeChequeDetails[i]["chequeNo"] +"</option>" ;
            //     }
            //     $("#update_checkNo_4").html(str);
            //     $("#update_checkNo_4").val(checkId);
            // }

        },
        error:function () {
            $("#mt_tip_ms").html("获得支票失败，请刷新重试！"); bounce.show($("#tip"));
        }
    })
}

// 修改详情（kind = 5 时）获取银行账户 type: 1:外部支票 2：外部支票 3:承兑汇票
function setBankVal( obj , val , type ){
    $.ajax({
        url:"../data/chooseCheque.do" ,
        data:{ "type" : type }  ,
        type:"post" ,
        dataType:"json" ,
        success:function(data){
            var content = data["content"] ;
            switch ( Number(type) ) {  // 1-内部支票   2-外部支票    3-承兑汇票     4-银行转账
                case 1 :
                    if (content) {
                        var str = "";
                        for (var i = 0; i < content.length; i++) {
                            str += "<option value='" + content[i]["id"] + "'>" + content[i]["bankName"] + "  " + content[i]["account"] + "</option>"
                        }
                        obj.html(str);
                        obj.val(val);
                    };
                    break;
                case 2:
                    var str = "";
                    $("#outerCheckCon").html("") ;
                    if( content && content.length > 0 ) {
                        var str = "";
                        for (var i = 0; i < content.length; i++) {
                            str += "<tr onclick='selectOuterCheck($(this))'>" +
                                "<td>" +
                                "<span class='radioBtn ' >" +
                                "<span class='val'>{value:"+ content[i]["id"] +" , name:''}</span>" +
                                "<span class='radioShow'></span>" +
                                "</span>" +
                                "</td>" +
                                "<td id='"+ content[i]["id"] +"'>"+ content[i]["returnNo"] +"</td>" +
                                "<td>"+ content[i]["amount"] +"</td>" +
                                "<td>"+ content[i]["bankName"] +"</td>" +
                                "<td>"+ content[i]["expireDate"].substr(0,10) +"</td> " +
                                "</tr>";
                        }
                        $("#outerCheckCon").html(str) ;
                    }
                    break ;
                case 3 :
                    var str = "";
                    $("#acceptCheckCon").html("") ;
                    if( content && content.length > 0 ) {
                        var str = "";
                        for (var i = 0; i < content.length; i++) {
                            str += "<tr>" +
                                "<td>" +
                                "<span class='radioBtn'> " +
                                "<span class='val'>{ value : '"+ content[i]["id"] +"' }</span>" +
                                "<span class='radioShow'></span>"+
                                "</span>" +
                                "</td>" +
                                "<td>"+ content[i]["createDate"].substr( 0 , 10 ) +"</td>" +
                                "<td>"+ content[i]["payer"] +"</td>" +
                                "<td>"+ content[i]["amount"] +"</td>" +
                                "<td id='"+ content[i]["id"] +"'>"+ content[i]["returnNo"] +"</td>" +
                                "<td>"+ content[i]["expireDate"].substr( 0 , 10 ) +"</td>" +
                                "<td>"+ content[i]["originalCorp"] +"</td>" +
                                "<td>"+ content[i]["bankName"] +"</td>"+
                                "</tr>";
                        }
                        $("#acceptCheckCon").html(str) ;
                    }
                    break;
            }
        },
        error:function(){

        }
    });
}
// 选择外部支票
function selectOuterCheck( obj ){
    obj.siblings().each(function(){
        $(this)	.children(":eq(0)").children(".radioBtn").removeClass("radioActive");
    });
    var radioBtn = obj.children(":eq(0)").children(".radioBtn");
    radioBtn.addClass("radioActive");
    var outerCheck =  eval('('+ radioBtn.children(".val").html()  +')');
    $("#outerCheckid").val(outerCheck["value"]);
    $("#outerCheckNo").val( obj.children(":eq(1)").html() );
}
// 显示详情的某项数据    
function dataInfo( obj ,typeStr){
    var userType = chargeRole("超管");
    if (userType && typeStr === "update") {
        alert("您没有此权限！")
    }else{
        $(".dataInfo").show().siblings().hide();
        obj.show().siblings().hide()  ;
    }

}
var applyData = {} // 税款的日期区间信息
// creator： 侯杏哲 2022-8-1 获取 税种 列表
function getRateList(obj, selectID) {
    $.ajax({
        "url":"../accountant/taxList.do",
        success:function (res) {
            let list = res.data || [] ;
            let str = `<option value="0">请选择</option>`
            list.forEach(function (item) {
                let isSelect = ''
                if(selectID == item.id ){
                    isSelect = "selected"
                }
                str += `<option ${ isSelect } value="${ item.id }">${ item.name }</option>`
            })

            obj.html(str)
        }
    })
}
// creator： 侯杏哲 2022-8-1 获取支付方式
function getPatMethod(obj, selectID) {
    $.ajax({
        "url":"../account/getAccountKinds.do",
        "data":{ 'accountStatus': 1 },
        success:function (res) {
            let list = res.data && res.data.financeAccounts || [] ;
            let str = `<option value="0">请选择</option>`
            list.forEach(function (item) {
                let isSelect = ''
                if(selectID == item.id ){
                    isSelect = "selected"
                }
                if ( item.accountType == 1 ){
                    str += `<option value="1-${ item.id }" ${ isSelect }>现金支付</option>`
                }else{
                    let bankNameStr = item["name"] + ' ' + formatAccount(item["account"])+ ' ' + item["bankName"] ;
                    if(item.isPublic == 1){
                        bankNameStr = formatAccount(item["account"])+ ' ' + item["bankName"] ;
                    }
                    str += `<option ${ isSelect } value="5-${ item.id }">${ `转账支付 - ${ bankNameStr }` }</option>`
                }
            })

            obj.html(str)
        }
    })
}
// creator： 侯杏哲 2022-8-1 获取 申报记录
function getRateApplyLog(obj, selectID) {
    console.log("1")
    let rateCat = $("#rateCat").val()
    let rateStartDate = $("#rateStartDate").val()
    let rateEndDate = $("#rateEndDate").val()
    if(rateCat && rateCat.length > 0 && rateStartDate.length > 0 && rateEndDate.length > 0){
        let stDate = new Date(rateStartDate)
        let edDate = new Date(rateEndDate)
        if(stDate > edDate){
            layer.msg("税款所属时期开始日期应在截止日期之前！")
            $("#rateEndDate").val("")
            return false;
        }
        let data = { "taxId":rateCat, "startDate":rateStartDate, "endDate":rateEndDate };
        if(data.taxId == applyData.taxId && data.startDate == applyData.startDate && data.endDate == applyData.endDate ){
            return false
        }
        getRateApplyLogAj(data , obj, selectID)
    }
}
function getRateApplyLogAj(data, obj, selectReport, optionSelect){
    if(!data.startDate || !data.endDate || Number(data.taxId) == 0 ){
        return false
    }
    applyData = JSON.parse(JSON.stringify(data))

    data.startDate = data.startDate.toString().split('');
    data.startDate.splice(4, 0, "-");
    data.startDate.splice(7, 0, "-");
    data.startDate = data.startDate.join("")

    data.endDate = data.endDate.toString().split('');
    data.endDate.splice(4, 0, "-");
    data.endDate.splice(7, 0, "-");
    data.endDate = data.endDate.join("")

    $.ajax({
        "url":"../accountant/getApplyTaxList.do",
        "data":data,
        beforeSend:function(){},
        success:function (res) {
            let list = res.data || [] ;
            list.push(optionSelect);
            let str = `<option value="0">请选择</option>`
            list.forEach(function (item) {
                if(item){
                    let isSelect = ''
                    if(selectReport == item.id ){
                        isSelect = "selected"
                    }
                    str += `<option ${ isSelect } value="${ item.report }" data-id="${ item.id }" startDate="${ item.startDate }" endDate="${ item.endDate }">需申报的起止日期为${ new Date(item.startDate ).format("yyyy-MM-dd") }至${ new Date(item.endDate ).format("yyyy-MM-dd") }的申报记录 </option>`

                }

            })
            obj.html(str)
        }
    })
}
// create:hxz 2022-08-08 税款修改 提交
function rateAddtrok() {
    let rateCat = $("#rateCat").val()
    let detailId =  $("#detailKind_3").data("detailid");
    let rateCatName = $("#rateCat option:selected").html()
    let rateStartDate = $("#rateStartDate").val()
    let rateEndDate = $("#rateEndDate").val()
    let rateRealDate = $("#rateRealDate").val()
    let moneyReal = $("#moneyReal").val()
    let patMethod = $("#patMethod").val()
    let patMethodStr = $("#patMethod option:selected").html()
    let rateApplyLog = $("#rateApplyLog").val()
    let taxPeriodIdInsert = $("#rateApplyLog option:selected").data("id")
    let taxPeriodIdUpdate = $("#rateApplyLog").data("beforeid")
    let startDate = $("#rateApplyLog option:selected").attr("startDate")
    let endDate = $("#rateApplyLog option:selected").attr("endDate")
    let rateApplyLogTxt = $("#rateApplyLog option:selected").html()
    let rateMemo = $("#rateMemo").val()
    let editType = $("#rateEntryFrm").data("type")

    if(rateCat== 0 || rateApplyLog== 0 || rateStartDate.length == 0 || rateEndDate.length == 0 || rateRealDate.length == 0 || moneyReal.length == 0 || patMethod.length == 0){
        layer.msg("请将必填项补充完整！");
        return false;
    }

    let imgPaths = ""
    let imgPathInfo = {}
    if($("#ratePic .cp_img_box").length > 0){
        $("#ratePic .cp_img_box").each(function () {
            let path = $(this).find(".ww").attr("path");
            let fileUid = $(this).find(".fileUid").html();
            imgPaths = path
            imgPathInfo = { path : path , fileUid: fileUid }
        })
    }

    let picPath_Old = $("#ratePic").data("path")

    let methodArr = patMethod.split("-")
    let method = methodArr[0]
    let financeAccountId = methodArr[1]
    let oldReport = $("#rateApplyLog").data("old");
    let info = {
        "taxId": rateCat ,
        "taxCategoryName": rateCatName ,
        "report": rateApplyLog ,         //    报表id
        "businessPeriodBegin": rateStartDate,  //  税款所属时期开始时间(格式是yyyyMMdd)
        "businessPeriodEnd": rateEndDate ,  //  税款所属时期结束时间(格式是yyyyMMdd)
        "periodBegin": startDate ,         //    申报记录开始时间（yyyy-MM-dd）
        "periodEnd": endDate ,            //   申报记录结束时间（yyyy-MM-dd）
        "accountId": financeAccountId ,     //     银行账户id
        "detailId": detailId,
        "debit": moneyReal ,
        "memo": rateMemo ,
        "factDate": rateRealDate ,
        "imgPaths": ([imgPaths]) ,
        "imgType": imgPaths == picPath_Old ? 0 : 1 ,
        "oldReport": oldReport,
        "taxPeriodIdInsert": taxPeriodIdInsert, // Integer taxPeriodIdInsert：申报记录id(修改后的)
        "taxPeriodIdUpdate": taxPeriodIdUpdate,  // Integer taxPeriodIdUpdate：申报记录id(修改前的)
    }
    $.ajax({
        "url":"../update/updateCash.do",
        "data": { "json": JSON.stringify(info) },
        success:function(res){
            let data = res.data
            let status = data && data.state
            let content = data && data.content
            layer.msg(content)
            if(status == 1){
                goPrev()
                clearInterval(rateEntryTimer);
            }
            // state：0-修改失败 1-操作成功 2-余额不足
            // content：与status对应的描述
        }
    })
}
function uploadInit3() {
    $("#ratePic").html("")
    $("#uploadBtn3").html("")
    $("#uploadBtn3").Huploadify({
        auto: true,
        fileTypeExts: '*.gif,*.png;*.jpg;*.jpeg;',
        multi: true,
        buttonText: "上传发票图片",
        buttonNumber: 0,
        formData: {
            module: '财务管理',
            userId: sphdSocket.user.userID
        },
        fileSizeLimit: (100*1024),  // 100M = ( 100 * 1024 ) KB
        showUploadedPercent: true,
        showUploadedSize: true,
        removeTimeout: 99999999,
        // itemTemplate:itemTemp ,
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        onUploadStart: function () {
            // deleObj.hide();
            // deleObj.siblings(".img").show();
        },
        onUploadComplete: function (file, data) {
        },
        onUploadError: function (file) {
            layer.msg('文件上传出错！');
            console.log('出错文件：', file)
        },
        onUploadSuccess: function (file, data) {
            data = JSON.parse(data)
            var fileUid = data.fileUid;
            //file 文件上传返回的参数  data 接口返回的参数
            var path = data.filename,  //路径（包含文件类型）
                fullpath = $.fileUrl + data.filename,
                name = file.name,           //文件名称
                imgStr = '';
            imgStr =
                '<div class="cp_img_box">' +
                '   <div class="fileType" style="background-image: url('+ fullpath +')">' +
                '       <a class="ww" path="'+ path +'" onclick="seeOnline($(this))">预览</a>' +
                '       <span class="fa fa-times" onclick="delFile($(this))"></span>' +
                '       <a class="hd fileUid">'+ fileUid +'</a>' +
                '   </div>'+
                // '   <div class="cp_img_name">'+file.name+'</div>' +
                '</div>';
            $("#ratePic").append(imgStr)
            $("#ratePic .imgI:last-child").data('val', data)
        }
    });

}
function upBtn3(){
    let num = $("#ratePic .cp_img_box").length
    if(num >= 1){
        return false
    }
    $("#uploadBtn3").find(".uploadify-button").click();
}
// creator： 侯杏哲 2022-8-1 税种 更改判断
function rateCatChange(obj) {
    let rateCat = $("#rateCat").val();
    if(rateCat){
        let rateCatName = $("#rateCat option:selected").html()
        if(rateCatName == "个人所得税"){
            bounce_Fixed.show($("#changeTip"))
        }
    }
}


// creator: 张旭博，2017-06-27 11:29:58，提交修改申请（5种数据来源）
function updateSubmit(selector,type){
    sphdSocket.subscribe('backMessage', function(data){
        loading.close()
        console.log('backMessage:'+data);
        var content = JSON.parse(data)
        var state = content.state
        if (Number(state) !== 2) {
            $("#goPrev").click()
        }
        layer.msg(content.content)
        cancelFileDel( fileDelAjaxList , true);
        fileDelAjaxList = [];
    });
    switch (type) {
        //数据来源为报销的修改申请
        case 1:
        //数据来源为收入的修改申请
        case 2:
            var id          = $("#detailKind_2").children().attr("id");
            var Genre       = $("#update_feeCat_2").val();
            var Summary     = $("#update_summery_2").val();
            var categoryDesc= $("#update_categoryDesc_2").val();
            var credit      = $("#update_amount_2").val();
            var purpose     = $("#update_purpose_2").val();
            var auditorName = $("#update_auditorName_2").val();
            var OppositeCorp= $("#update_oppositeCorp_2").val();
            var receiveAccountDate= $("#update_receiveAccountDate_2").val();
            var accountBank= $("#update_accountBank_2 option:selected").text();
            var accountId_= $("#update_accountBank_2").val();
            var memo        = $("#update_memo_2").val();
            var data = {
                "session": sphdSocket.sessionid,
                "userId": sphdSocket.user.userID,
                "detailId":id,
                "genre":Genre,                  //类别
                "summary":Summary,              //摘要
                "credit":credit,                //摘要
                "purpose":purpose,              //用途
                "auditorName":auditorName,      //经手人
                "oppositeCorp":OppositeCorp,    //付款单位
                "memo":memo                     //备注
            };
            if(Genre == 5){
                data["categoryDesc"] = categoryDesc;
            }
            //银行转账多传的字段
            if($("#update_method_2").text() == "银行转账"){
                data["receiveAccountDate"] = new Date(receiveAccountDate);
                data["accountId"] = accountId_;
            }
            if(Summary == ""){
                $("#tip #mt_tip_ms").html("请填写摘要！");
                bounce.show($("#tip"));
            }else if(credit == ""){
                $("#tip #mt_tip_ms").html("请填写金额！");
                bounce.show($("#tip"));
            }else if(validateNum(credit) == false){
                $("#tip #mt_tip_ms").html("请检查您输入的金额是否合法（不能为负）！");
                bounce.show($("#tip"));
            }else if(purpose == ""){
                $("#tip #mt_tip_ms").html("请填写用途！");
                bounce.show($("#tip"));
            }else if(auditorName == ""){
                $("#tip #mt_tip_ms").html("请填写经手人！");
                bounce.show($("#tip"));
            }else if(OppositeCorp == ""){
                $("#tip #mt_tip_ms").html("请填写付款单位！");
                bounce.show($("#tip"));
            }else if($("#update_method_2").text() == "银行转账" && receiveAccountDate == "") {
                $("#tip #mt_tip_ms").html("请选择到账时间！");
                bounce.show($("#tip"));
            }else if($("#update_method_2").text() == "银行转账" && accountId_ == "") {
                $("#tip #mt_tip_ms").html("请选择收款银行！");
                bounce.show($("#tip"));
            }else{
                console.log(data)
                sphdSocket.send('updateCash',data)
            }
            break;
        //数据来源为支出的修改申请
        case 30: // 汇划费的提交
            id = $("#detailKind_3").data("detailid");
            Summary     = $("#update_summaryPurpose_3").val();
            paydate     = $("#update_paydate_3").val();
            payAmount     = $("#update_payAmount_3").val();
            memo        = $("#update_payMemo_3").val();
            accountBank = $("#update_payBank_3 option:selected").text();
            accountId_  = $("#update_payBank_3").val();
            var imgList = []
            var imgType = 0; // 1-图片有修改  0-图片未修改[用来判断图片是否修改了]
            $("#scan_imgs_3 .cp_img_box").each(function () {
                var newType = $(this).data("new")
                var imginfo = $(this).find(".ww").attr("path")
                imgList.push(imginfo)
                if(newType == 1){
                    imgType = 1;
                }
            })

            data = {
                "detailId":id,
                "summary":Summary,              //摘要
                "debit":payAmount,                  //支出
                "purpose":Summary,                  //摘要
                "memo":memo     ,                //备注
                "accountId": accountId_ ,
                "factDate": paydate ,
                "imgType": imgType ,
                "imgPaths": JSON.stringify( imgList ) ,
            };
            if(factDate == ""){
                $("#tip #mt_tip_ms").html("请填写支出日期！");
                bounce.show($("#tip"));
            }else if(payAmount == ""){
                $("#tip #mt_tip_ms").html("请填写支出金额！");
                bounce.show($("#tip"));
            }else if(accountId_ == ""){
                $("#tip #mt_tip_ms").html("请填写开户行！");
                bounce.show($("#tip"));
            }else{
                console.log(data)
                loading.open()
                sphdSocket.send('updateCash',data)

            }
            break;
        case 3:
                id          = $("#detailKind_3").children().attr("id");
                Summary     = $("#update_summery_3").val();
            var billAccount = $("#update_billAccount_3").val();
            var debit       = $("#update_amount_3").val();
                purpose     = $("#update_purpose_3").val();
                auditorName = $("#update_auditorName_3").val();
            var billDate = $("#update_billDate_3").val();
                OppositeCorp= $("#update_oppositeCorp_3").val();
                memo        = $("#update_memo_3").val();
                accountBank = $("#update_accountBank_3 option:selected").text();
                accountId_  = $("#update_accountBank_3").val();

            var billCat = $("#update_billCat_3 .radioActive").next("span").text();
            var stakeholderCategory = $("#update_stakeholderCategory_3").data("type")
            var stakeholder = $("#update_oppositeCorp_3").data("id")
            var factDate = $("#update_factDate_3").val()

            var imgList = []
            var imgType = 0; // 1-图片有修改  0-图片未修改[用来判断图片是否修改了]

            $("#scan_imgs_32 .cp_img_box").each(function () {
                var newType = $(this).data("new")
                var imginfo = $(this).find(".ww").attr("path")
                imgList.push(imginfo)
                if(newType == 1){
                    imgType = 1;
                }
            })

            data = {
                "credit": "",
                "receiveAccountDate": "",
                "accountId": "",
                "factDate": factDate ,
                "genre": "",
                "categoryDesc": "",
                "billAmount": "",
                "session": sphdSocket.sessionid,
                "userId": sphdSocket.user.userID,
                "detailId":id,
                // "billCat":billCat,              //票据种类
                "billDate":billDate,         //票据所属日期
                "summary":Summary,              //摘要
                "debit":debit,                  //摘要
                "billQuantity":billAccount,     //票据数量
                "purpose":purpose,              //用途
                "auditorName":auditorName,      //经手人
                "oppositeCorp":OppositeCorp,    //收款单位
                "stakeholderCategory":stakeholderCategory,    //收款单位
                "stakeholder":stakeholder,    //收款单位
                "imgType": imgType ,
                "imgPaths": JSON.stringify( imgList ) ,
                "memo":memo                     //备注

            };
            ///银行转账多传的字段
            if($("#update_method_3").text() == "银行转账"){
                // data["accountBank"] = accountBank;
                data["accountId"] = accountId_;
            }
            if(Summary == ""){
                $("#tip #mt_tip_ms").html("请填写摘要！");
                bounce.show($("#tip"));
            }else if(debit == ""){
                $("#tip #mt_tip_ms").html("请填写金额！");
                bounce.show($("#tip"));
            }else if(validateNum(debit) == false){
                $("#tip #mt_tip_ms").html("请检查您输入的金额是否合法（不能为负）！");
                bounce.show($("#tip"));
            }else if(billAccount == ""){
                $("#tip #mt_tip_ms").html("请填写票据数量！");
                bounce.show($("#tip"));
            }else if(validateNum(billAccount) == false){
                $("#tip #mt_tip_ms").html("请检查您输入的票据数量是否合法（不能为负）！");
                bounce.show($("#tip"));
            }else if(purpose == ""){
                $("#tip #mt_tip_ms").html("请填写用途！");
                bounce.show($("#tip"));
            }else if(auditorName == ""){
                $("#tip #mt_tip_ms").html("请填写经手人！");
                bounce.show($("#tip"));
            }else if(OppositeCorp == ""){
                $("#tip #mt_tip_ms").html("请填写收款单位！");
                bounce.show($("#tip"));
            }else if($("#update_method_3").text() == "银行转账" && accountId_ == "") {
                $("#tip #mt_tip_ms").html("请选择转账银行！");
                bounce.show($("#tip"));
            }else{
                console.log(data)
                loading.close();
                sphdSocket.send('updateCash',data)
            }
            break;
        //数据来源为支出中的转账支票(内部的转账支票或者改为外部的汇款票据)的修改申请
        case 4:
                id              = $("#detailKind_3_").children().attr("id");
                Summary         = $("#update_summery_3_").val();
                billAccount     = $("#update_billAccount_3_").val();
            var amount          = $("#update_amount_3_").val();
            var billAmount          = $("#update_billAmount_3_").val();
                purpose         = $("#update_purpose_3_").val();
                auditorName     = $("#update_auditorName_3_").val();
            OppositeCorp        = $("#update_oppositeCorp_3_").val();
            var method_3        = $("#update_method_3_").val(); //支出方式
            var receiveCpmy     = $("#update_receiveCpmy_3_").val(); //收款单位
            var method_3_2      = $("#update_method_3_2").val(); //两种转账方式

            var cunBank         = $("#update_bankAccount_3_").find("option:selected").text();
            var account          = cunBank.split(" ")[2];
            var accountId       = $("#update_bankAccount_3_").val();
            var saveBankName    = cunBank.split(" ")[0];

            var checkId         = $("#update_checkNo_3_1").val(); //支票号
            var checkNo         = $("#update_checkNo_3_1 option:selected").text(); //支票号
            var checkendDate    = $("#update_checkendDate_3_").val(); //支票到期日
            var receiveDate     = $("#update_receiveDate_3_").val(); //接收日期
            var receivePerson   = $("#update_receivePerson_3_").val(); //接收经手人
            var payPerson       = $("#update_payPerson_3_").val(); //支付经手人
            var stakeholderCategory       = $("#update_stakeholderCategory_3_").data("type");
            var stakeholder       = $("#update_oppositeCorp_3_").data("id");
            var billDate       = $("#update_billDate_3_").val();
            var factDate = $("#update_factDate_3_").val()
                memo        = $("#update_memo_3_ ").val();

            var imgList = []
            var imgType = 0; // 1-图片有修改  0-图片未修改[用来判断图片是否修改了]
            $("#scan_imgs_3_ .cp_img_box").each(function () {
                var newType = $(this).data("new")
                var imginfo = $(this).find(".ww").attr("path")
                imgList.push(imginfo)
                if(newType == 1){
                    imgType = 1;
                }
            })
             data = {
                "billAmount": billAmount,
                "factDate": factDate,

                 "session": sphdSocket.sessionid,
                 "userId": sphdSocket.user.userID,
                 "chequeDetailId":id,
                 "stakeholder":stakeholder,
                 "billDate":billDate,
                 "stakeholderCategory":stakeholderCategory,
                 "oppositeCorp":OppositeCorp,
                 "summary":Summary,              //摘要
                 "billQuantity":billAccount,     //票据数量
                 "amount":amount,                //金额
                 "purpose":purpose,              //用途
                 "financialHandling":auditorName,//经手人
                 "receiveCorp":receiveCpmy,      //收款单位
                 "imgType": imgType ,
                 "imgPaths": JSON.stringify( imgList ) ,
                 "memo":memo                     //备注
            };
            //根据支出方式判断要传的参数（支出方式1-转账支票 2-承兑汇票  转账方式 1-内部转账 2-外部转账）
            if(method_3 == 1){
                if(method_3_2 == 1){
                    // "type":type,                     //支出方式
                    // "accountId_":accountId_,         //银行账户
                    // "account":account,               //银行账号
                    // "bankName":bankName,             //银行名称
                    // "chequeNo":chequeNo,             //支票号
                    // "expireDate":expireDate,         //支票到期日
                    // "receiveDate":receiveDate,       //接收日期
                    // "receiver":receiver,             //接收经手人
                    // "operator":operator,             //支付经手人
                    //转账内部支票
                    data["type"] = 1;
                    data["chequeId"] = checkId;
                    data["chequeNo"] = checkNo;
                    data["accountId"] = accountId;
                    data["account"] = account;
                    // data["bankName"] = saveBankName;
                    data["expireDate"] = new Date(checkendDate);
                    data["receiveDate"] = new Date(receiveDate);
                    data["receiver"] = receivePerson;
                    data["operator"] = payPerson;
                }else if(method_3_2 == 2){
                    checkNo = $("#update_chequeNo_3_1").val();
                    checkId = $("#update_chequeNo_3_1").attr("cid");
                    // "chequeNo":chequeNo,              //  支票号
                    data["type"] = 4;
                    data["chequeNo"] = checkNo;
                    data["chequeId"] = checkId;
                }
            }else if(method_3 == 2){
                checkNo = $("#update_chequeNo_3_2").val();
                checkId = $("#update_chequeNo_3_2").attr("cid");
                // "chequeNo":chequeNo,              //  支票号
                data["type"] = 3;
                data["chequeNo"] = checkNo;
                data["chequeId"] = checkId;
            }
            if(Summary == ""){
                $("#tip #mt_tip_ms").html("请填写摘要！");
                bounce.show($("#tip"));
            }else if(billAccount == ""){
                $("#tip #mt_tip_ms").html("请填写票据数量！");
                bounce.show($("#tip"));
            }else if(validateNum(billAccount) == false){
                $("#tip #mt_tip_ms").html("请检查您输入的票据数量是否合法（不能为负）！");
                bounce.show($("#tip"));
            }else if(amount == ""){
                $("#tip #mt_tip_ms").html("请填写金额！");
                bounce.show($("#tip"));
            }else if(validateNum(amount) == false){
                $("#tip #mt_tip_ms").html("请检查您输入的金额是否合法（不能为负）！");
                bounce.show($("#tip"));
            }else if(purpose == ""){
                $("#tip #mt_tip_ms").html("请填写用途！");
                bounce.show($("#tip"));
            }else if(auditorName == ""){
                $("#tip #mt_tip_ms").html("请填写经手人！");
                bounce.show($("#tip"));
            }else if(method_3 == 1 && method_3_2 == 1 && checkendDate == ""){
                $("#tip #mt_tip_ms").html("请选择支票到期日！");
                bounce.show($("#tip"));
            }else if(method_3 == 1 && method_3_2 == 1 && receiveDate == ""){
                $("#tip #mt_tip_ms").html("请选择接收日期！");
                bounce.show($("#tip"));
            }else if(method_3 == 1 && method_3_2 == 1 && receivePerson == ""){
                $("#tip #mt_tip_ms").html("请填写接收经手人！");
                bounce.show($("#tip"));
            }else if(method_3 == 1 && method_3_2 == 1 && payPerson == ""){
                $("#tip #mt_tip_ms").html("请填写支付经手人！");
                bounce.show($("#tip"));
            }else if(method_3 == 1 && checkNo == ""){
                $("#tip #mt_tip_ms").html("请选择支票号！");
                bounce.show($("#tip"));
            }else if(method_3 == 2 && checkNo == ""){
                $("#tip #mt_tip_ms").html("请选择汇票号！");
                bounce.show($("#tip"));
            }else if(receiveCpmy == ""){
                $("#tip #mt_tip_ms").html("请填写收款单位！");
                bounce.show($("#tip"));
            }else{
                console.log(data)
                loading.close();
                sphdSocket.send('updateBankTransfer',data)
            }
            break;
        //数据来源为承兑汇票和转账支票中支票的修改申请
        case 5:
                cunBank     = $("#update_cunBank_5").find("option:selected").text();

                id          = $("#detailKind_5").children().attr("id");
            var accout      = cunBank.split(" ")[2];
                accountId   = $("#update_cunBank_5").val();
                saveBankName= cunBank.split(" ")[0];
            var cunTime     = $("#update_cunTime_5").val();
            var cunPerson   = $("#update_cunPerson_5").val();
            var receTime    = $("#update_receTime_5").val();

                data = {
                    "session": sphdSocket.sessionid,
                    "userId": sphdSocket.user.userID,
                    "returnId":id,
                    "accountId":parseInt(accountId),    //付款单位
                    // "accout":accout,
                    // "saveBankName":saveBankName,
                    "depositDate":new Date(cunTime),
                    "depositorName":cunPerson,
                    "receiveAccountDate":new Date(receTime)
                };
            if(cunTime == ""){
                $("#errorTip .tipWord").html("请选择存入时间！");
                bounce.show($("#errorTip"));
            }else if(cunPerson == ""){
                $("#errorTip .tipWord").html("请选择存入经手人！");
                bounce.show($("#errorTip"))
            }else if(receTime == ""){
                $("#errorTip .tipWord").html("请选择到账时间！");
                bounce.show($("#errorTip"));
            }else{
                console.log(data)
                sphdSocket.send('updateReturn',data)
            }
            break;
        //数据来源为内部非支出性转账的修改申请
        case 6:
                id          = $("#detailKind_4").children().attr("id");
            var transKind       = $("#update_transKind_4").text();               //转账类型
            // var payAccount     = $("#update_payAccount_4").val();               //付款账户
            // var receiveAccount     = $("#update_receiveAccount_4").val();       //收款账户
            // var checkNo     = $("#update_checkNo_4").val();                     //支票号
            var money = $("#update_money_4").val();                             //转账金额
            var moneyType = $("#update_money_4").attr("moneyType");               //转账金额
            var auditDate= $("#update_auditDate_4").val();                      //业务发生时期
                memo        = $("#update_memo_4").val();                        //备注
                data = {
                    "session": sphdSocket.sessionid,
                    "userId": sphdSocket.user.userID,
                    "detailId":id,
                    "auditDate":new Date(auditDate),    //业务发生时期
                    "memo":memo                   //备注
                };
            //取现金多传的参数
            if(transKind == "取现金"){
                var ChequeId       = $("#update_checkNo_4").val();
                var chequeNo       = $("#update_checkNo_4").find("option:selected").text();
                // data["chequeNo"] =  chequeNo;
                data["chequeId"] =  ChequeId;

            }
            //取现金和存现金传的参数不同
            if(moneyType == "c"){
                data["credit"] =  money;
            }else{
                data["debit"] =  money;
            }
            if(money == ""){
                $("#errorTip .tipWord").html("请输入转账金额！");
                bounce.show($("#errorTip"));
            }else if(auditDate == ""){
                $("#errorTip .tipWord").html("请选择业务发生时期！");
                bounce.show($("#errorTip"))
            }else{
                console.log(data)
                sphdSocket.send('updateTransferAccounts',data)
            }
            break;
        default:
            $("#tip #mt_tip_ms").html("错误的数据来源！");
            bounce.show($("#tip"));
    }
}

// creator: 张旭博，2017-06-27 11:29:58，确定选择转账支票
function sureOuterCheck(selector) {
    //获取支票号
    var no = selector.parents("#outerCheck").find(".radioActive").parent().parent().children().eq(1).html();
    //获取支票id
    var chequeid = selector.parents("#outerCheck").find(".radioActive").parent().parent().children().eq(1).attr("id");
    //赋值到输入框中
    $("#update_chequeNo_3_1").val(no);
    $("#update_chequeNo_3_1").attr("cid",chequeid);
    bounce.cancel();
}

// creator: 张旭博，2017-06-27 11:29:58，确定选择承兑汇票
function sureAcceptCheck(selector) {
    //同上
    var no = selector.parents("#acceptCheck").find(".radioActive").parent().parent().children().eq(4).html();
    var chequeid = selector.parents("#acceptCheck").find(".radioActive").parent().parent().children().eq(4).attr("id");
    $("#update_chequeNo_3_2").val(no);
    $("#update_chequeNo_3_2").attr("cid",chequeid);
    bounce.cancel();
}

// creator: 张旭博，2017-06-27 11:29:58，获取银行信息
let bankInfoList = []
function getBank( setObj, selectedID ){
    $.ajax({
        url:"../cheque/getBankAccount.do" ,
        type:"post" ,
        dataType:"json" ,
        beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
        success:function (data) {
            loading.close();
            var financeAccounts = data["financeAccounts"] ;
            if( financeAccounts && financeAccounts.length > 0 ){
                var str = "<option value=''>选择收款银行</option>";
                let seleStr = ``
                bankInfoList = financeAccounts
                for( var i=0 ; i< financeAccounts.length ; i++ ){
                    seleStr = ``;
                    if(selectedID == financeAccounts[i]["id"]) { seleStr = " selected" }
                    str += "<option value="+ financeAccounts[i]["id"] + seleStr + ">"+ financeAccounts[i]["bankName"] + " " + financeAccounts[i]["account"] +"</option>"
                }
                setObj.html(str);
            }
        },
        error:function () {
            $("#mt_tip_ms").html("获得可用银行失败，请刷新重试！"); bounce.show($("#mtTip"));
        },
        complete:function(){ chargeClose(1) ;  }
    })
}

function getScanBank( setObj , accountId ){
    $.ajax({
        url:"../cheque/getBankAccount.do" ,
        success:function (data) {
            var financeAccounts = data["financeAccounts"] ;
            if( financeAccounts && financeAccounts.length > 0 ){
                bankInfo = financeAccounts ;
                var str = "";
                for( var i=0 ; i<bankInfo.length ; i++ ){
                    if(accountId == financeAccounts[i]["id"]){
                        str = financeAccounts[i]["bankName"] +" "+financeAccounts[i]["account"];
                    }
                }
                setObj.html(str);
            }
        }
    })
}

function goPrev() {
    $("#navControl .navTxt").eq(-2).find('a').click()
}

// -------------------报销部分-------------------

// creator: 张旭博，2019-05-24 09:28:12，获取报销详情
function getReimburseDetail(reimburseId) {
    bounce.show($("#reimburse"))
    $("#reimburse .steps:gt(0)").remove()
    $.ajax({
        url:"../reimburseWindow/getReimburseInfo.do" ,
        data: {reimburseId : reimburseId },
        success:function (data) {
            var processLisft	 = data.processList || []
            var personnelReimburse = data.personnelReimburse
            var billList = data.billList

            var billStr = ''
            var count =1;
            var addNew = false;

            var stepItemStr = "<div class=\"stepItem\">" +
                "    <div class=\"litCircle\"><div>"+ count +"</div></div>" +
                "    <div class=\"bigCircle\">" +
                "        <p>提交报销申请</p>" +
                "        <p>申请人:"+ personnelReimburse.createName +"</p>" +
                "        <p><small>" + (new Date(personnelReimburse.createDate).format("yyyy-MM-dd hh:mm:ss")) +"</small></p>" +
                "    </div>" +
                "</div>";
            for (var i in processList) {
                if(addNew){
                    var newStepCon = "<div class=\"processStep\">" +
                        "     <div class=\"line\"><div><div></div></div></div>" +
                        "     <div class=\"steps\">" +
                        "     </div>" +
                        " </div>";
                    $("#reimburse .processStep:last").after(newStepCon);
                    addNew = false;
                }
                var approveStatus = processList[i].approveStatus
                var businessType = processList[i].businessType
                var userName = processList[i].userName
                var handleTime = new Date(processList[i].handleTime).format("yyyy-MM-dd hh:mm:ss")

                var  t1 = "", t2='', t3 = handleTime

                switch (approveStatus) {
                    case '2':
                        if(businessType == 17){
                            t1 = "付款审批通过"; t2='审批人'
                        }else if(businessType == 20){
                            t1 = "付款复核通过"; t2='审批人'
                        }else if(businessType == 21){
                            t1 = "付款审批通过"; t2='审批人'
                        }else if(businessType == 22){
                            t1 = "付款方式修改"; t2='出纳员'
                        }else{
                            t1 = "审批通过"; t2='审批人'
                        }
                        break;
                    case '7':
                        t1 = "票据审批通过"; t2='出纳员';
                        break;
                    case '5':
                        t1 = "付款完成"; t2='出纳员';
                        break;
                }
                count++;
                var moreTr = count%10 ;
                if(moreTr == 1){
                    $("#reimburse .steps:last").html(stepItemStr)
                    stepItemStr = "" ;
                    addNew = true;
                }
                stepItemStr += "<div class=\"stepItem\">" +
                    "    <div class=\"litCircle\"><div>"+ count +"</div></div>" +
                    "    <div class=\"bigCircle\">" +
                    "        <p>"+ t1 +"</p>" +
                    "        <p>"+ t2 +":"+ userName +"</p>" +
                    "        <p><small>" + handleTime +"</small></p>" +
                    "    </div>" +
                    "</div>";

            }
            $("#reimburse .steps:last").html(stepItemStr)

            $("#reimburse .summary").html(personnelReimburse.summary)
            $("#reimburse .purpose").html(personnelReimburse.purpose)

            var totalQuantity = 0
            var totalBillAmount = 0
            var totalAmount = 0
            for (var j in billList) {
                billStr +=  '<tr id="'+billList[j].reimburseBillId+'">' +
                    '    <td>' + billList[j].billCatName + '</td>' +
                    '    <td>' + (billList[j].price ? billList[j].price : '- -') + '</td>' +
                    '    <td>' + billList[j].num + '</td>' +
                    '    <td>' + billList[j].billAmount.toFixed(2) + '</td>' +
                    '    <td>' + billList[j].amount.toFixed(2) + '</td>' +
                    '    <td>' +
                    '        <span class="ty-color-blue" data-name="billInfo" type="btn">票据内容</span>' +
                    '        <span class="ty-color-blue" data-itemCount="'+ billList[j].itemCount +'" data-name="billPic" type="btn">票据图片</span>' +
                    '       <span class="hd">'+ JSON.stringify(billList[j]['pictures']) +'</span>'+
                    '    </td>' +
                    '</tr>'
                // totalQuantity = -(-totalQuantity - billList[j].relativeBillQuantity)
                totalQuantity +=  Number(billList[j].num)
                totalBillAmount = -(-totalBillAmount - billList[j].billAmount)
                totalAmount = -(-totalAmount - billList[j].amount)
            }
            if (billList.length > 1) {
                billStr +=  '<tr>' +
                            '    <td></td>' +
                            '    <td></td>' +
                            '    <td>' + totalQuantity + '</td>' +
                            '    <td>' + totalBillAmount.toFixed(2) + '</td>' +
                            '    <td>' + totalAmount.toFixed(2) + '</td>' +
                            '    <td></td>' +
                            '</tr>'
            }
            $("#reimburse .billDetail tbody").html(billStr)
        }
    })
}

// creator: 张旭博，2019-02-19 10:19:45，查看借款详情
function seeOrdLoanDetail(param) {
    $("#generalScanTtl").html('综合查看')
    $("#generalScanTtl .handleBtn").hide()
    bounce.show($("#generalScan"));
    getOrdLoanDetail(param, true)
}

// creator: lyt，2019-06-10 10:19:45，查看回款详情
function seeCollectDetail(detailId){
    $.ajax({
        url: "../collectWindow/getSlCollectDetailPC.do",
        data: {
            'collectId': detailId
        },
        success: function (data) {
            var detail = data;
            if (detail){
                var collect = detail.slCollectApplication
                var method = collect.method;
                $("#cltAmount").html(formatMoney(collect.amount));
                $("#cltCustomer").html(detail.customerName);
                switch (method) {
                    case '1':
                        $("#cashRecive").html(new Date(collect.receiveDate).format('yyyy/MM/dd'));
                        $('.cltByCrah').show().siblings().hide();break;
                    case '3':
                        $("#cqRecive").html(new Date(collect.receiveDate).format('yyyy/MM/dd'));
                        $("#cqSn").html(collect.returnNo);
                        $("#cqDueDate").html(new Date(collect.expireDate).format('yyyy/MM/dd'));
                        $("#cqUnit").html(collect.originalCorp);
                        $("#cqBank").html(collect.bankName);
                        $("#cqDepositBank").html(collect.receiveBankName);
                        $("#cqDepositDate").html(new Date(collect.depositDate).format('yyyy/MM/dd'));
                        $("#cqDepositMan").html(collect.depositorName);
                        $("#cqArriveTime").html(new Date(collect.receiveAccountDate).format('yyyy/MM/dd'));
                        $('.cltByCheque').show().siblings().hide();break;
                    case '4':
                        $("#billRecive").html(new Date(collect.receiveDate).format('yyyy/MM/dd'));
                        $("#billSn").html(collect.returnNo);
                        $("#billDueDate").html(new Date(collect.expireDate).format('yyyy/MM/dd'));
                        $("#billUnit").html(collect.originalCorp);
                        $("#billBank").html(collect.bankName);
                        $("#billDepositBank").html(collect.receiveBankName);
                        $("#billDepositDate").html(new Date(collect.depositDate).format('yyyy/MM/dd'));
                        $("#billDepositMan").html(collect.depositorName);
                        $("#billArriveTime").html(new Date(collect.receiveAccountDate).format('yyyy/MM/dd'));
                        $('.cltByBill').show().siblings().hide();break;
                    case '5':
                        $("#bankRecive").html(new Date(collect.expireDate).format('yyyy/MM/dd'));
                        $("#bankReciveBank").html(collect.bankName);
                        $('.cltByBank').show().siblings().hide();break;
                    default:
                        $("#cashRecive").hide().siblings().hide();
                }
            }
            bounce.show($("#collectDetail"));
        }
    })
}
// -------------------多点点-分支机构-------------------
function screenOrgData(obj){
    let val = obj.val();
    showMainData( searchLevel , searchMethod , val);
}

//creator:liyuting date:2022/08/12 可选择的机构列表
function getOrgSonOrgs(){
    $.ajax({
        async: false,
        url:'../sonOrg/getOrgSonOrgs.do',
        success:function(data){
            let list = data.data;
            let options = `<option value="">请选择机构</option>`;
            if (list && list.length > 0) {
                for(var i=0;i<list.length;i++) {
                    options += `<option value="${list[i].id}">${list[i].name}</option>`;
                }
            }
            if (useInfo.orgType === 1 && list.length > 1){//orgType 是4 就是子机构， 是1 就是总机构
                matchType = true;
                $(".screenBody").show();
                $(".belongOrg").show();
            } else {
                matchType = false;
                $(".screenBody").hide();
                $(".belongOrg").hide();
            }
            $("#searchOrg").html(options);
        }
    })
}
//creator:liyuting date:2024/07/22 更换承兑汇票/更换转账支票
let turnToBankDetail= {}
function updateFromBank(obj, source){
    let info = JSON.parse(obj.siblings(".hd").html())
    editTr = obj.parent().parent() ;
    //method;//0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
    let method = Number(info.method);
    let ttl = ``;
    let type = 3; //承兑应该是3/转账应该是2
    //turnToBankDetail= info;
    if (method === 3) {
        ttl = `更换转帐支票`
        type = 2;
    } else if (method === 4) {
        ttl = `更换承兑汇票`;
    }
    $(".singleRow .fa").removeClass("fa-circle");
    $("#updateBillInfo").data("kind", type);
    $("#updateBillInfo .changeTtl").html(ttl);
    $('.toggleCon').addClass("hd");
    $.ajax({
        url:"../data/getOneDetail.do" ,
        data:{ "detailId" : info.id  } ,
        success:function (data) {
            var content = data["content"];
            turnToBankDetail = content;
        }})
    billNolist(type);
    getBank( $("#turnToBank_1")) ;
    bounce.show($("#updateBillInfo"))
}
function initUpdateStr(info){
    $("#turnToBank_1").val(info.receiveBank || info.saveBankId)
    $("#bill_depositDate").val(new Date(info.depositDate).format('yyyy-MM-dd'))
    $("#bill_depositorName").val(info.depositorName)
    $("#billReceiveAccountDate").val(new Date(info.receiveAccountDate).format('yyyy-MM-dd'))
}
//creator:liyuting date:2024/07/22 获取承兑汇票/获取转账支票
function billNolist(sort){
    $.ajax({
        url:"../data/chooseReturn.do",
        data:{ "type": sort 	},//承兑应该是3/转账应该是2
        success:function(res){
            let data = res.data;
            let options = `<option value=''>请选择</option>`;
            var financeReturns =data["content"];
            for(var i=0 ; i<financeReturns.length ; i++ ){
                //options += `<option value="${{'id':financeReturns[i].id,'id':financeReturns[i].id}}">${financeReturns[i].returnNo}</option>`
                options += `<option value="${financeReturns[i].id}">${financeReturns[i].returnNo}</option>`
            }
            $("#billNolist").html(options);
        }
    })
}
//creator:liyuting date:2024/07/22 更换承兑汇票/更换转账支票
function changeMethod(obj){
    let val = Number(obj.data("val"));
    obj.toggleClass("fa-circle");
    obj.siblings('.toggleCon').toggleClass('hd');
    if (obj.parent().siblings().find(".fa").hasClass("fa-circle")) {
        obj.parent().siblings().find(".fa").removeClass("fa-circle").siblings('.toggleCon').toggleClass('hd');
    }
    if (val === 1 && obj.hasClass("fa-circle")) {
        initUpdateStr(turnToBankDetail);
    }
}
//creator:liyuting date:2024/07/22 更换承兑汇票/更换转账支票提交
function updateBillInfoOk(){
    let info = turnToBankDetail
    let kind = $("#updateBillInfo").data("kind");// 承兑应该是3/转账应该是2
    let selectVal = $("#updateBillInfo .fa-circle").data('val')
    let json = {
        'updateType': selectVal, //1-修改其他信息(修改存入银行的信息，默认) 2-更换承兑汇票
        'returnIdNew': '',
        'returnId': info.id
    }
    if (Number(selectVal) === 2){
        if ($("#billNolist").val() !== '') {
            json.returnIdNew = $("#billNolist").val();
            //json.returnId = $("#billNolist").val();
            json.bankName = info.saveBankName
            json.account = info.returnNo
            json.accountId = info.saveBankId
            json.depositDate = new Date(info.depositDate).format('yyyy-MM-dd')
            json.depositorName = info.depositorName || info.depositorName
            json.receiveAccountDate = new Date(info.receiveDate).format('yyyy-MM-dd')
        } else {
            let tip = `请选择承兑汇票!`;
            if (Number(kind) === 1) {tip = `请选择转账支票!`;}
            layer.msg(tip);
            return false
        }
    } else {
        //var returnId = turnToBankDetail.returnId;
        var bankId = $("#turnToBank_1").val();
        if(!bankId){  $("#turnToTip_1").html("请选择存入银行");   return false;  }
        let bankInfo = bankInfoList.find(item=> Number(item.id) === Number(bankId));
        var accountId = bankId ;
        var BankName = bankInfo["bankName"] ;
        var account =  bankInfo["account"] ;
        var depositDate = $("#bill_depositDate").val();
        var depositorName = $("#bill_depositorName").val();
        var receiveAccountDate = $("#billReceiveAccountDate").val();
        if(!json.returnId){  $("#turnToTip_1").html("获得支票信息失败请刷新重试");   return false;  }
        if(!accountId){  $("#turnToTip_1").html("获得账户ID失败请刷新重试");   return false;  }
        if(!BankName){  $("#turnToTip_1").html("获得银行名称失败请刷新重试");   return false;  }
        if(!account){  $("#turnToTip_1").html("获得银行账号失败请刷新重试");   return false;  }
        if(!depositDate){  $("#turnToTip_1").html("存入时间不能为空");   return false;  }
        if(!receiveAccountDate){  $("#turnToTip_1").html("到账日期不能为空");   return false;  }
        //json.returnId = returnId
        json.accountId = accountId
        json.bankName = BankName
        json.account = account
        json.depositDate = depositDate
        json.depositorName = depositorName
        json.receiveAccountDate = receiveAccountDate
    }
    $.ajax({
        url:"../update/updateReturn.do",
        data: {"json": JSON.stringify(json)},
        success:function(res){
            var status = res.data["state"];
            if( status === 0 || status === "0"){
                bounce_Fixed.show($("#mtTip"));
                $("#mt_tip_msg").html("操作失败！");
            } else if( status === 3 || status === "3"){
                bounce_Fixed.show($("#mtTip"));
                $("#mt_tip_msg").html(res.data.content);
            } else {
                bounce_Fixed.show($("#mtTip"));
                $("#mt_tip_msg").html("操作成功！");
                showMainData( searchLevel , searchMethod , '');
                bounce.cancel();
            }
        }
    })
}
// 判别转账方式
function chargeMethod( val ){
    switch( Number(val) ){
        case 1 : return "现金" ; break ;
        case 2 : return "现金支票" ; break ;
        case 3 : return "转账支票" ; break ;
        case 4 : return "承兑汇票" ; break ;
        case 5 : return "银行转账" ; break ;
        case 6 : return "存现金" ; break ;
        case 7 : return "取现金" ; break ;
        case 8 : return "其他内部转账" ; break ;
        default : return "";
    }
}




// -------------------过滤器、工具方法-------------------

// 判别 费用类别
function chargeGenre( val ){
    switch( Number(val) ){
        case 1 : return "货款" ; break ;
        case 2 : return "借款" ; break ;
        case 3 : return "投资款" ; break ;
        case 4 : return "废品" ; break ;
        case 5 : return "其他" ; break ;
        default : return "";
    }
}

// 判别票据所属月份
function chargeBillPeriod( val ){
    switch( Number(val) ){
        case 1 : return "本月票据" ; break ;
        case 2 : return "非本月票据" ; break ;
        default : return "";
    }
}

//判断是否为正实数
function validateNum(num) {
    var reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
    if(reg.test(num)) return true;
    return false ;
}

// 鼠标进入图片
function imgEnter(obj) {
    var path = obj.attr("src") ;
    $("#bigImag").attr("src" , path);
    $("#bigImagcon").show();
}

// 鼠标离开图片
function imgOut(obj) {
    $("#bigImagcon").hide();
}
let fileDelAjaxList = []
// creator ：侯杏哲 2022-04-08 删除图片
function delFile(thisObj){
    thisObj.parents('.cp_img_box').remove();
    let fileUid = thisObj.siblings(".hd").html()
    // fileDelAjax({ 'type':'fileUid', 'fileUid': fileUid })
    fileDelAjaxList.push({ 'type':'fileUid', 'fileUid': fileUid })
}

// 时间控件的初始化
;!function(){
    laydate.render({elem: '#searchStart'});
    laydate.render({elem: '#searchEnd'});
    laydate.render({elem: '#update_auditDate_4'});
    laydate.render({elem: '#update_checkendDate_3_'});
    laydate.render({elem: '#update_receTime_5'});
    laydate.render({elem: '#update_receiveAccountDate_2'});
    laydate.render({elem: '#update_receiveDate_3_'});
    laydate.render({elem: '#update_cunTime_5'});
    laydate.render({elem: '#update_billDate_3'});
    laydate.render({elem: '#update_billDate_3_'});
    laydate.render({elem: '#update_factDate_3'});
    laydate.render({elem: '#update_factDate_3_'});
    laydate.render({elem: '#update_paydate_3'});

    laydate.render({elem: '#rateStartDate', format: 'yyyyMMdd' });
    laydate.render({elem: '#rateEndDate', format: 'yyyyMMdd' });
    laydate.render({elem: '#rateRealDate'});
    laydate.render({elem: '#bill_depositDate'});
    laydate.render({elem: '#billReceiveAccountDate'});

}();